package com.ibookso.products

class CategoryLevel3 {
    String browseNodeId
    String nodeName
    String parentId
    String lastChild
    String updated
    Date lastUpdated
    Date lastChecked
    Integer updateInterval
    String categoryType
    Integer syllabusId
    Integer gradeId
    Integer subjectId

    static mapping = {
        datasource 'wsoutsideproducts'
    }
    static constraints = {
        lastChild blank:true, nullable: true
        updated blank:true, nullable: true
        lastUpdated blank:true, nullable: true
        lastChecked blank:true, nullable: true
        updateInterval blank:true, nullable: true
        categoryType blank:true, nullable: true
        syllabusId blank:true, nullable: true
        gradeId blank:true, nullable: true
        subjectId blank:true, nullable: true
    }
}
