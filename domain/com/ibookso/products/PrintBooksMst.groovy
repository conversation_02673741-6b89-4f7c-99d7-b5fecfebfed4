package com.ibookso.products

class PrintBooksMst {
    String title
    Date dateCreated
    String publisher
    String coverImage
    String status
    String isbn
    String asin
    String categoryId
    String baseCategory


    static constraints = {
        publisher blank:true, nullable: true
        coverImage blank:true, nullable: true
        status blank:true, nullable: true
        isbn blank:true, nullable: true
        asin blank:true, nullable: true
        categoryId blank:true, nullable: true
        baseCategory blank:true, nullable: true

    }
    static mapping = {
        datasource 'wsoutsideproducts'
    }

}
