package com.wonderslate.groups

class GroupsMst {

    String name
    String privacyType
    String createdBy
    String visibility
    String image
    String description
    String welcomeText
    String welcomeImage
    String welcomeFile
    String colorCode
    Date dateCreated
    Integer siteId
    String allPost
    String groupType
    Integer batchId
    Integer instituteId

    static constraints = {
        description blank:true, nullable: true
        image blank:true, nullable: true
        visibility blank:true, nullable: true
        welcomeText blank:true, nullable: true
        welcomeImage blank:true, nullable: true
        welcomeFile blank:true, nullable: true
        colorCode blank:true, nullable: true
        siteId blank:true,nullable: true
        allPost blank:true,nullable: true
        groupType blank:true,nullable:true
        batchId blank:true,nullable:true
        instituteId blank:true,nullable:true
    }

    static mapping = {
        datasource 'wsuser'
    }
}
