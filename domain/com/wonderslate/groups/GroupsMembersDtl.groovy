package com.wonderslate.groups

class GroupsMembersDtl {
    String username
    String name
    Long groupId
    Date dateCreated
    String addedBy
    String profilepic
    String role
    Date lastVisitedOn
    Long userId

    static constraints = {
        addedBy blank:true, nullable: true
        role blank:true, nullable: true
        lastVisitedOn blank:true, nullable: true
        profilepic blank:true, nullable: true
        name blank:true, nullable: true
        userId blank:true, nullable: true
    }

    static mapping = {
        datasource 'wsuser'
    }
}
