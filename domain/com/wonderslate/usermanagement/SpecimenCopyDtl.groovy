package com.wonderslate.usermanagement

class SpecimenCopyDtl {

    String name
    String email
    String phoneNumber
    String dateOfBirth
    String institute
    String address
    String pincode
    String country
    String state
    String district
    String publisher
    String level
    String syllabus
    String grade
    String subject
    String photoId
    Integer siteId
    String teacherImage
    Boolean specimentSent
    String dateCreated

    static constraints = {
        name nullable: false, blank: false
        email nullable: false, blank: false, email: true
        phoneNumber nullable: false, blank: false
        dateOfBirth nullable: false
        dateCreated nullable: false
        institute nullable: true
        address nullable: true
        pincode nullable: true
        country nullable: true
        state nullable: true
        district nullable: true
        publisher nullable: true
        level nullable: true
        syllabus nullable: true
        grade nullable: true
        subject nullable: true
        photoId nullable: true
        specimentSent nullable: true
        teacherImage blank:true, nullable:true

    }
}
