package com.wonderslate.usermanagement

class PointsDtl {
    String username
    Date date
    String type
	Integer points
	String activity
	String subActivity

    static constraints = {
        username blank: false, nullable: false
        date blank: false, nullable: false
        type blank: false, nullable: false
        points blank: false, nullable: false	
        activity blank: false, nullable: false	
        subActivity blank: true, nullable: true
    }
    static mapping = {
        datasource 'wslog'
    }
}
