package com.wonderslate.usermanagement

class PollsResults {

    Long pollId
    int option1Count
    int option2Count
    int option3Count
    int option4Count
    int  totalCount

    static constraints = {
        option1Count blank:true, nullable: true
        option2Count blank:true, nullable: true
        option3Count blank:true, nullable: true
        option4Count blank:true, nullable: true
        totalCount blank:true, nullable: true
    }

    static mapping = {
        datasource 'wscomm'
    }
}
