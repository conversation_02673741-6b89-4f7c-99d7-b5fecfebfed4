package com.wonderslate.usermanagement

class User {

	transient springSecurityService

	String username
	String password
	boolean enabled = true
	boolean accountExpired
	boolean accountLocked
	boolean passwordExpired

	String name
	String email
	String qualification
	String profession
	String workplace
	String address1
	String address2
	String city
	String state
	String district
	String country
	String introduction
	String facebook
	String webpage
	String workplacewebpage
	String linkedin
	String twitter
	String interests
	String profilepic
    String summary
	String wonderSlateEmployee
    String authType
	String sex
	String student
	String parent
	String teacher
	String win
	String registeredFrom
    Date dateCreated
	String school
	Date dateOfBirth
	String classStudying
	String syllabus
	String showAnalytics
	String canPublish
	Integer publisherId
	Integer authorId
	String mobile
	Integer siteId
	String institution
	String department
	String otherInterests
	String studentDiscipline
	String userType
	String otpFinished

    String name1
    String email1
	String institution1
	String department1
	String interests1
    String country1

	String oldUsername
	Integer maxLogins
	String commentsBlocked

	Integer lifeTimePoints
	Integer sessionCount

	Integer pincode
	Integer noOfTrees
	String sageLogin
	Date commentsBlockedDate
	Integer commentsBlockedResId
	String affliationCd
	String termsCondition
	String ipAddress
	Date tcAcceptedDate
	Integer signedUpInstituteId
	Integer chatTokensBalance

	static transients = ['springSecurityService']

	static constraints = {
		username blank: false, unique: true
		password blank: false
        qualification blank: true, nullable: true
        profession blank: true, nullable: true
        workplace blank: true, nullable: true
        address1 blank: true, nullable: true
        address2 blank: true, nullable: true
        city blank: true, nullable: true
        state blank: true, nullable: true
		district blank: true, nullable: true
        country blank: true, nullable: true
        introduction blank: true, nullable: true
        facebook blank: true, nullable: true
        webpage blank: true, nullable: true
        workplacewebpage blank: true, nullable: true
        linkedin blank: true, nullable: true
        twitter blank: true, nullable: true
        interests blank: true, nullable: true
        profilepic blank: true, nullable: true
        summary blank: true, nullable: true
		wonderSlateEmployee blank: true, nullable: true
        authType blank: true, nullable: true
		sex blank: true, nullable: true
		student blank: true, nullable: true
		parent blank: true, nullable: true
		teacher blank: true, nullable: true
		win blank: true, nullable: true
		registeredFrom blank: true, nullable: true
		school blank: true, nullable: true
		dateOfBirth blank: true, nullable: true
		classStudying blank: true, nullable: true
		syllabus blank: true, nullable: true
		showAnalytics blank: true, nullable: true
		canPublish blank: true, nullable: true
		authorId blank: true, nullable: true
		publisherId blank: true, nullable: true
		mobile blank: true, nullable: true
		siteId blank: true, nullable: true
		institution blank: true, nullable: true
		department blank: true, nullable: true
		otherInterests blank: true, nullable: true
		studentDiscipline blank: true, nullable: true
		userType blank: true, nullable: true
		otpFinished blank: true, nullable: true
        name1 blank: true, nullable: true
        email1 blank: true, nullable: true
        institution1 blank: true, nullable: true
        department1 blank: true, nullable: true
        interests1 blank: true, nullable: true
        country1 blank: true, nullable: true
		oldUsername blank: true, nullable: true
		email blank: true, nullable: true
		maxLogins blank: true, nullable: true
		commentsBlocked blank: true, nullable: true
		lifeTimePoints blank: true, nullable: true
		sessionCount blank: true, nullable: true
		pincode blank: true, nullable: true
		noOfTrees blank: true, nullable: true
		sageLogin blank: true, nullable: true
		commentsBlockedDate blank: true, nullable: true
		commentsBlockedResId blank: true, nullable: true
		affliationCd blank: true, nullable: true
		termsCondition blank: true, nullable: true
		ipAddress blank: true, nullable: true
		tcAcceptedDate blank: true, nullable: true
		signedUpInstituteId blank: true, nullable: true
		chatTokensBalance blank: true, nullable: true
	}

	static mapping = {
		password column: '`password`'
		datasource 'wsuser'
	}

	static hasMany = [oAuthIDs: OAuthID]

	Set<Role> getAuthorities() {
		UserRole.findAllByUser(this).collect { it.role }
	}

	def beforeInsert() {
		encodePassword()
	}

	def beforeUpdate() {
		if (isDirty('password')) {
			encodePassword()
		}
	}

	protected void encodePassword() {
		password = springSecurityService?.passwordEncoder ? springSecurityService.encodePassword(password) : password
	}
}
