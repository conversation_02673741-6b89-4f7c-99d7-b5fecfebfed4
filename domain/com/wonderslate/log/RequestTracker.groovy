package com.wonderslate.log

class RequestTracker {
    String request_id
    String status = 'STARTED'
    Boolean error = false
    String errorMessage
    Date dateCreated
    Date lastUpdated

    static constraints = {
        request_id blank: true, nullable: true
        status blank: false, inList: ['STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED']
        errorMessage blank: true, nullable: true
        error blank: true, nullable: true
        dateCreated blank: true, nullable: true
        lastUpdated blank: true, nullable: true
    }

    static mapping = {
        datasource 'wslog'
    }
}
