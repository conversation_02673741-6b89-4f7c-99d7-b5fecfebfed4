package com.wonderslate.log

class GptLog {
    String username
    String userPrompt
    String systemPrompt
    String response
    Integer resId
    Integer readingMaterialResId
    String promptType
    String feedbackType
    String feedback
    Date dateCreated
    String response2
    Integer siteId
    Integer materialId
    String imgLink
    Integer quizObjId



    static constraints = {
        promptType blank: true, nullable: true
        resId blank: true, nullable: true
        readingMaterialResId blank: true, nullable: true
        promptType blank: true, nullable: true
        systemPrompt blank: true, nullable: true
        userPrompt blank: true, nullable: true
        feedback blank: true, nullable: true
        feedbackType blank: true, nullable: true
        dateCreated blank: true, nullable: true
        response2 blank: true, nullable: true
        siteId blank: true, nullable: true
        materialId blank: true, nullable: true
        imgLink blank: true, nullable: true
        quizObjId blank: true, nullable: true
    }
    static mapping = {
        datasource 'wslog'
    }
}
