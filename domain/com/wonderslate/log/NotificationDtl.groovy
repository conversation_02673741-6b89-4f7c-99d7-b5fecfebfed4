package com.wonderslate.log

class NotificationDtl {
    String title
    String body
    String createdBy
    Date dateCreated
    Date sendDate
    String sendTo
    String status
    String imageUrl
    Integer siteId
    String link
    String messageType
    Integer resId
    String deletedBy
    Date dateDeleted




    static constraints = {
        sendDate blank: true, nullable: true
        sendTo blank: true, nullable: true
        status blank: true, nullable: true
        imageUrl blank: true, nullable: true
        link blank: true, nullable: true
        messageType blank: true, nullable: true
        resId blank: true, nullable: true
        deletedBy blank: true, nullable: true
        dateDeleted blank: true, nullable: true
    }
    static mapping = {
        datasource 'wscomm'
    }
}
