package com.wonderslate.log

class ResourceView {

    Integer resourceDtlId
    Date dateCreated
    String username
    String action
    String source
    String fromTab
    String viewedFrom
    Long chapterId
    Integer siteId
    String resType
    String resSubType

    static constraints = {
        resourceDtlId blank: true, nullable: true
        username blank: true, nullable: true
        source blank: true, nullable: true
        fromTab blank: true, nullable: true
        viewedFrom blank: true, nullable: true
        chapterId blank: true, nullable: true
        siteId blank: true, nullable: true
        resType blank: true, nullable: true
        resSubType blank: true, nullable: true
    }

    static mapping = {
        datasource 'wslog'
    }
}
