package com.wonderslate.log

class Quizrecorder {
    String username
    Integer testgenid
    Integer quizid
    Date starttime
    Date endtime
    String timetaken
    Integer totalQuestions
    Integer correctAnswers
    Integer wrongAnswers
    Integer skipped
    Date quizTakenTime
    Long bookId
    Double score
    Integer rank
    String otherLanguage
    String userAnswers

    static constraints = {
        testgenid blank: true, nullable: true
        quizid blank: true, nullable: true
        starttime blank: true, nullable: true
        endtime blank: true, nullable: true
        timetaken blank: true, nullable: true
        totalQuestions blank: true, nullable: true
        correctAnswers blank: true, nullable: true
        wrongAnswers blank: true, nullable: true
        skipped blank: true, nullable: true
        quizTakenTime blank: true, nullable: true
        bookId blank: true, nullable: true
        score blank: true, nullable: true
        rank blank: true, nullable: true
        otherLanguage blank: true, nullable: true
        userAnswers blank: true, nullable: true
    }

    static mapping = {
        datasource 'wslog'
    }
}
