package com.wonderslate.log

class TeacherNomineeDtl {

    String userType
    String userName
    String userEmail
    String userCity
    String userState
    String userMobile
    String userPincode
    Integer siteId
    String teacherName
    String teacherEmail
    String teacherSubject
    String teacherClass
    String teacherMobile
    String teacherImage
    String description
    String comments
    String schoolName
    String status
    Date dateCreated

    static constraints = {
        userEmail blank: true, nullable: true
        userCity blank: true, nullable: true
        userState blank: true, nullable: true
        userMobile blank: true, nullable: true
        teacherEmail blank:true, nullable:true
        userPincode blank:true, nullable:true
        teacherSubject blank:true, nullable:true
        teacherClass blank:true, nullable:true
        teacherMobile blank:true, nullable:true
        teacherImage blank:true, nullable:true
        comments blank:true, nullable:true
        description blank:true, nullable:true
        status blank:true, nullable:true
        schoolName blank:true, nullable:true
    }
    static mapping = {
        datasource 'wscomm'
    }
}
