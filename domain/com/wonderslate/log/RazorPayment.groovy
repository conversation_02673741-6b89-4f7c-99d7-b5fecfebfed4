package com.wonderslate.log

class RazorPayment {

    String shoppingCartId
    String username
    String fee
    String description
    String createdAt
    String amountRefunded
    String bank
    String errorReason
    String errorDescription
    String bankTransactionId
    String captured
    String contact
    String invoiceId
    String currency
    String razorPaymentId
    String international
    String email
    String amount
    String refundStatus
    String wallet
    String method
    String vpa
    String errorSource
    String errorStep
    String tax
    String cardId
    String errorCode
    String orderId
    String entity
    String status
    Date dateCreated
    Integer siteId

    static constraints = {
        fee blank: true,  nullable: true
        description blank: true,  nullable: true
        amountRefunded blank: true,  nullable: true
        errorReason blank: true,  nullable: true
        errorDescription blank: true,  nullable: true
        bankTransactionId blank: true,  nullable: true
        captured blank: true,  nullable: true
        contact blank: true,  nullable: true
        invoiceId blank: true,  nullable: true
        currency blank: true,  nullable: true
        international blank: true,  nullable: true
        email blank: true,  nullable: true
        amount blank: true,  nullable: true
        refundStatus blank: true,  nullable: true
        wallet blank: true,  nullable: true
        method blank: true,  nullable: true
        vpa blank: true,  nullable: true
        errorSource blank: true,  nullable: true
        errorStep blank: true,  nullable: true
        tax blank: true,  nullable: true
        cardId blank: true,  nullable: true
        errorCode nullable: true
        orderId blank: true,  nullable: true
        entity blank: true,  nullable: true
        status blank: true,  nullable: true
        siteId blank: true,  nullable: true
        bank blank: true,  nullable: true
    }

    static mapping = {
        datasource 'wslog'
    }


}
