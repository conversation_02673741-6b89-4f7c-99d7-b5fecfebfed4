package com.wonderslate.log

//import org.bson.types.ObjectId

class MdResourceView {
    Integer resourceDtlId
    Date date
    String username
    String action
    String source
    String fromTab
    String viewedFrom
    Integer siteId
    String resName
    String resType
    String resSubType
    Integer duration
    Long publisherId
    Date nextRevisionDate
    Integer revisionNo
    String stopRevision
    String revisionCompleted

    static constraints = {
        resourceDtlId blank: true, nullable: true
        date blank: true, nullable: true
        action blank: true, nullable: true
        source blank: true, nullable: true
        fromTab blank: true, nullable: true
        viewedFrom blank: true, nullable: true
        siteId blank: true, nullable: true
        resName blank: true, nullable: true
        resType blank: true, nullable: true
        resSubType blank: true, nullable: true
        publisherId blank: true, nullable: true
        duration blank: true, nullable: true
        username blank: true, nullable: true
        nextRevisionDate blank: true, nullable: true
        revisionNo blank: true, nullable: true
        stopRevision blank: true, nullable: true
        revisionCompleted blank: true, nullable: true

    }

    static mapping = {
        datasource 'wslog'
    }
}
