package com.wonderslate.content

class ContentExamDtl {

    Integer year
    String month
    String shift
    String questionPaperPath
    String extractedContent
    String createdBy
    Date dateCreated
    Long contentExamMstId
    String language1
    String language2

    static constraints = {
        year nullable: false
        month nullable: true
        shift nullable: true
        questionPaperPath blank: true, nullable: true
        extractedContent blank: true, nullable: true
        createdBy nullable: false
        dateCreated nullable: false
        contentExamMstId nullable: false
        language1 blank: true, nullable: true
        language2 blank: true, nullable: true
    }

    String toString() {
        "${exam?.examName} - ${year} ${month ?: ''} ${shift ?: ''}"
    }
    
}
