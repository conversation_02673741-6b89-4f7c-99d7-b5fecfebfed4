package com.wonderslate.data

class UserGradeDtl {
    Long gradeId
    String username
    Long syllabusId
    String grade
    String currentlySelected
    String syllabus


    static constraints = {
        gradeId blank:true, nullable: true
        syllabusId blank:true, nullable: true
        grade blank:true, nullable: true
        currentlySelected blank:true, nullable: true
        syllabus blank:true, nullable: true
    }
    static mapping = {
        datasource 'wsuser'
    }
}
