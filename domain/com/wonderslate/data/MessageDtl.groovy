package com.wonderslate.data

class MessageDtl {
    Long msgId
    Long fromUserId
    Long toUserId
    Long groupId
    Date dateCreated
    Date dateChecked
    Date dateDelivered
    Date dateFromDeleted
    Date dateToDeleted

    static constraints = {
        groupId blank:true, nullable: true
        dateChecked blank:true, nullable: true
        dateFromDeleted blank:true, nullable: true
        dateDelivered blank:true, nullable: true
        dateToDeleted blank:true, nullable: true
    }
}
