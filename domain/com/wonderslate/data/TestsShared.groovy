package com.wonderslate.data

class TestsShared {
    Long testId
    Long batchId
    String name
    Date startDateTime
    Date endDateTime
    Date resultDateTime
    Integer duration
    String createdBy
    Date dateCreated
    Date dateDeleted
    String deletedBy

    static constraints = {
        startDateTime blank:true, nullable: true
        endDateTime blank:true, nullable: true
        resultDateTime blank:true, nullable: true
        duration blank:true, nullable: true
        dateDeleted blank:true, nullable: true
        deletedBy blank:true, nullable: true
    }
    static mapping = {
        datasource 'wsuser'
    }

}
