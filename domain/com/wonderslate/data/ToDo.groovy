package com.wonderslate.data

class ToDo {

    String taskName
    Date taskDate
    String fromTime
    String toTime
    String priority
    String status
    Long resId
    Date completedOn
    String userId

    static constraints = {
        taskName blank:false, nullable:false
        taskDate blank:false, nullable:false
        userId blank:false, nullable:false
        fromTime blank:true, nullable:true
        toTime blank:true, nullable:true
        priority blank:true, nullable:true
        status blank:true, nullable:true
        resId blank:true, nullable:true
        completedOn blank:true, nullable:true
    }

    static mapping = {
        datasource 'wsuser'
    }
}
