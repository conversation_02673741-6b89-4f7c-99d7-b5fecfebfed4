package com.wonderslate.data

class EmailMst {
	String email
	Integer siteId
    String validated
    Date dateCreated

	static constraints = {
		email blank: true, nullable: true
        siteId blank: true, nullable: true
        validated blank: true, nullable: true
        dateCreated blank: false, nullable: false
	}

	static mapping = {
        dateCreated defaultValue: "now()"
		datasource 'wsuser'
	}

}
