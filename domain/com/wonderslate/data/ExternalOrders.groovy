package com.wonderslate.data

class ExternalOrders {
    String orderId
    String orderFrom
    String itemCode
    Date dateCreated
    String accessCode
    Integer siteId
    String status
    Double poValue
    String createdBy

    static constraints = {
        accessCode blank: true, nullable: true
        status blank: true, nullable: true
        poValue blank: true, nullable: true
    }

    static mapping = {
        datasource 'wsshop'
    }
}
