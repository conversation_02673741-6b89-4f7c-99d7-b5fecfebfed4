package com.wonderslate.data

class GradeMst {

    String grade
    String syllabusType
    String country
    Integer sortBy
    String syllabus
    Integer sortOrder

    static constraints = {
        grade blank:false, nullable:false, unique: false
        syllabusType blank:false, nullable:false, unique: false
        country blank:false, nullable:false, unique: false
        sortBy blank:false, nullable:false, unique: false
        syllabus blank:false, nullable:false, unique: false
        sortOrder blank:false, nullable:false, unique: false
    }

    static mapping = {
        datasource 'wsshop'
    }
}
