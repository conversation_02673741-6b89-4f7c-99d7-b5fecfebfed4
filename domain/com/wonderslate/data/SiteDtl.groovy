package com.wonderslate.data

class SiteDtl {

    Long siteId
    String themeColor
    String logo
    String logoIcon
    String favicon
    String bannerImage
    String facebookLink
    String twitterLink
    String instagramLink
    String linkedinLink
    String youtubeLink
    String addressLine1
    String addressLine2
    String mobileNumber
    String emailAddress
    String websiteLink
    String gstNumber
    String privacyPolicy
    String termsCondition
    String playStore
    String appStore
    String jurisdictionPlace
    String jurisdictionState
    String customStyles
    String companyName
    String customLandingPage
    String noLogin
    String enableContactus
    String enableOTPLogin
    String siteTitle
    String siteDescription
    String keywords
    String showLandingPage
    String whatsappLink
    String telegramLink
    String disableStore
    String disableScratchCode
    String ipAddressAccess
    String canAddBooksFromAllSites
    String digitalLibraryLandingPage
    String customGptLoader
    String gptLoaderDisplayName
    String gptLoaderPath
    String showAnalytics

    static constraints = {
        siteId blank:true, nullable: true
        themeColor blank: true, nullable: true
        logo blank: true, nullable: true
        logoIcon blank: true, nullable: true
        favicon blank: true, nullable: true
        bannerImage blank: true, nullable: true
        facebookLink blank: true, nullable: true
        twitterLink blank: true, nullable: true
        instagramLink blank: true, nullable: true
        linkedinLink blank: true, nullable: true
        youtubeLink blank: true, nullable: true
        addressLine1 blank: true, nullable: true
        addressLine2 blank: true, nullable: true
        mobileNumber blank: true, nullable: true
        emailAddress blank: true, nullable: true
        websiteLink blank: true, nullable: true
        gstNumber blank: true, nullable: true
        privacyPolicy blank: true, nullable: true
        termsCondition blank: true, nullable: true
        playStore blank: true, nullable: true
        appStore blank: true, nullable: true
        jurisdictionPlace blank: true, nullable: true
        jurisdictionState blank: true, nullable: true
        customStyles blank: true, nullable: true
        companyName blank: true, nullable: true
        customLandingPage blank: true, nullable: true
        noLogin blank: true, nullable: true
        enableContactus blank: true, nullable: true
        enableOTPLogin blank: true, nullable: true
        siteTitle blank: true, nullable: true
        siteDescription blank: true, nullable: true
        keywords blank: true, nullable: true
        showLandingPage blank: true, nullable: true
        whatsappLink blank: true, nullable: true
        telegramLink blank: true, nullable: true
        disableStore blank: true, nullable: true
        disableScratchCode blank: true, nullable: true
        ipAddressAccess blank: true, nullable: true
        canAddBooksFromAllSites blank: true, nullable: true
        digitalLibraryLandingPage blank: true, nullable: true
        customGptLoader blank: true, nullable: true
        gptLoaderDisplayName blank: true, nullable: true
        gptLoaderPath blank: true, nullable: true
        showAnalytics blank: true, nullable: true
    }
}
