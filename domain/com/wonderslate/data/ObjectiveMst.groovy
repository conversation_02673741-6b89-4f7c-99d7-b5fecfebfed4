package com.wonderslate.data

import groovy.transform.AutoClone;

@AutoClone
class ObjectiveMst {
    Integer quizId
    String quizType
    String question
    String option1
    String option2
    String option3
    String option4
    String option5
    String description1
    String description2
    String description3
    String description4
    String description5
    String answerDescription1
    String questionDescription1
    String answer1
    String answer2
    String answer3
    String answer4
    String answer5
    String answerDescription
    String hint
    String examYear
    String difficultylevel
    Integer topicId
    String directions
    String section
    Integer directionId
    String answer
    String subject
    String chapter
    Date expiryDate
    Double marks
    Double negativeMarks
    String explainLink
    String startTime
    String endTime
    Integer quizSort
    String questionType
    String gptExplanation
    String gptSimilarQuestions
    String gptHint
    String isValidAnswerKey
    String qType
    String bloomType
    String subTopic

    static constraints = {
        quizId blank:false, nullable:false
        quizType blank:false, nullable:false
        question blank:false, nullable:false
        option1 blank:true, nullable:true
        option2 blank:true, nullable:true
        option3 blank:true, nullable:true
        option4 blank:true, nullable:true
        option5 blank:true, nullable:true
        answerDescription1 blank:true, nullable:true
        questionDescription1 blank:true, nullable:true
        description1 blank:true, nullable:true
        description2 blank:true, nullable:true
        description3 blank:true, nullable:true
        description4 blank:true, nullable:true
        description5 blank:true, nullable:true
        answer1 blank:true, nullable:true
        answer2 blank:true, nullable:true
        answer3 blank:true, nullable:true
        answer4 blank:true, nullable:true
        answer5 blank:true, nullable:true
        answerDescription blank: true, nullable: true
        hint blank: true, nullable: true
        examYear blank: true, nullable: true
        topicId blank: true, nullable: true
        difficultylevel blank: true, nullable: true
        directions blank: true, nullable: true
        section blank: true, nullable: true
        directionId blank: true, nullable: true
        answer (blank: true, nullable:true, maxSize:1073741824)
        subject blank: true, nullable: true
        chapter blank: true, nullable: true
        expiryDate (blank: true, nullable:true)
        marks blank: true, nullable: true
        negativeMarks blank: true, nullable: true
        explainLink blank: true, nullable: true
        startTime blank: true, nullable: true
        endTime blank: true, nullable: true
        quizSort blank: true, nullable: true
        questionType blank: true, nullable: true
        gptExplanation blank: true, nullable: true
        gptSimilarQuestions blank: true, nullable: true
        gptHint blank: true, nullable: true
        isValidAnswerKey blank: true, nullable: true
        qType blank: true, nullable: true
        bloomType blank: true, nullable: true
        subTopic blank: true, nullable: true

    }
}
