package com.wonderslate.data

import groovy.transform.AutoClone;

@AutoClone
class BooksMst {
    Long id
    String title
    Date dateCreated
    Long publisherId
    String coverImage
    String status
    String subjectyear
    String isbn
    String createdBy
    Integer siteId
    Double rating
    Double price
    String currency
    String description
    Double listprice
    Date datePublished
	String headerImage
	String language
    String bookType
    String vendor
    String reviewLink1
    String reviewLink2
    String buylink1
    String buylink2
    Long mirrorBookId
    String sellChapterWise
    String testTypeBook
    String discipline
    String authors
    String tags
    Integer noOfViews
    Long parentBookId
    Date testStartDate
    Date testEndDate
    String rankingDone
    Integer validityDays
    Integer noOfTestTakers
    String asin
    Integer totalReviews
    String packageBookIds
    String showInLibrary
    String showDiscount
    String hasQuiz
    String source
    String bookDeepLink
    String medium
    String downloadChapters
    Integer chapterDownloadCount
    String singleEpub
    Date lastSold
    String allowSubscription
    String bookCode
    String subscriptionPackage
    Double testsListprice
    Double testsPrice
    Double upgradePrice
    String subscriptionId
    Integer bookWeight
    Integer currentStock
    Date bookExpiry
    String genericReader
    String newStorage
    String externalLink
    String basePrompt
    String showNotification
    String showMcq
    String showQa
    String showSnapshot
    String showPdf
    String enableToken
    String enableCompiler
    String lockEdit

    static constraints = {
        publisherId blank:true, nullable: true
        coverImage blank:true, nullable: true
        status blank:true, nullable: true
        subjectyear blank:true, nullable: true
        isbn blank:true, nullable: true
        createdBy blank:true, nullable: true
        siteId blank:true, nullable: true
        rating blank:true, nullable: true
        price blank:true, nullable: true
        currency blank:true, nullable: true
        description blank:true, nullable: true
        listprice blank:true, nullable: true
        datePublished blank:true, nullable: true
		headerImage blank:true, nullable: true
		language blank:true, nullable: true
        bookType blank:true, nullable: true
        vendor blank:true, nullable: true
        reviewLink1 blank:true, nullable: true
        reviewLink2 blank:true, nullable: true
        buylink1 blank:true, nullable: true
        buylink2 blank:true, nullable: true
        mirrorBookId blank:true, nullable: true
        sellChapterWise blank:true, nullable: true
        testTypeBook blank:true, nullable: true
        discipline blank:true, nullable: true
        authors blank:true, nullable: true
        tags blank:true, nullable: true
        noOfViews blank:true, nullable: true
        parentBookId blank:true, nullable: true
        testStartDate blank:true, nullable: true
        testEndDate blank:true, nullable: true
        rankingDone blank:true, nullable: true
        validityDays blank:true, nullable: true
        noOfTestTakers blank:true, nullable: true
        asin blank:true, nullable: true
        totalReviews blank:true, nullable: true
        packageBookIds blank:true, nullable: true
        showInLibrary blank:true, nullable: true
        showDiscount blank:true, nullable: true
        hasQuiz blank:true, nullable: true
        source blank:true, nullable: true
        bookDeepLink blank:true, nullable: true
        medium blank:true, nullable: true
        downloadChapters blank:true, nullable: true
        chapterDownloadCount blank:true, nullable: true
        singleEpub blank:true, nullable: true
        lastSold blank:true, nullable: true
        allowSubscription blank:true, nullable: true
        bookCode blank:true, nullable: true
        subscriptionPackage blank:true, nullable: true
        testsListprice blank:true, nullable: true
        testsPrice blank:true, nullable: true
        upgradePrice blank:true, nullable: true
        subscriptionId blank:true, nullable: true
        bookWeight blank:true, nullable: true
        currentStock blank:true, nullable: true
        bookExpiry blank:true, nullable: true
        genericReader blank:true, nullable: true
        newStorage blank:true, nullable: true
        externalLink blank:true, nullable: true
        basePrompt blank:true, nullable: true
        showNotification blank:true, nullable: true
        showMcq blank:true, nullable: true
        showQa blank:true, nullable: true
        showSnapshot blank:true, nullable: true
        showPdf blank:true, nullable: true
        enableToken blank:true, nullable: true
        enableCompiler blank: true, nullable: true
        lockEdit blank: true, nullable: true
    }
    static mapping = {
        id generator: 'assigned'
        datasources([ 'DEFAULT','wsuser','wsshop'])
    }
}
