package com.wonderslate.data

import groovy.transform.AutoClone;

@AutoClone
class ResourceDtl {
    Integer topicId
    String resType
    String createdBy
    Date dateCreated
    String resLink
    String filename
    String resourceName
    String sharing
    Integer noOfViews = 0
    Integer noOfFavourites = 0
    Integer noOfLikes = 0
    String quizMode
    Long chapterId
    String chapterDesc
    Integer noOfPages = 0
    Integer fileSize
    String modifiedFile
    String description
    String canReorderQuestions
    String examSyllabus
    String status
    Integer sortOrder
    String grade
    String language1
    String language2
    String examSubject
    Integer examId
    Date testStartDate
    Date testEndDate
    Integer noOfTestTakers
    Long parentId
    String videoPlayer
    String allowComments
    String displayComments
    String syllabus
    String subject
    String downloadlink1
    String downloadlink2
    String downloadlink3
    Date testResultDate
    Integer siteId
    String resSubType
    String privacyLevel
    String ebupChapterLink
    String flashCardFastTime
    String fastTimeUsername
    String indContentType
    String allowReAttempt
    String streamKey
    String channelId
    String resDeepLink
    String currentAffairsType
    String zoomLevel
    String deletedBy
    String gptResourceType
    String vectorStored
    String extractPath
    String mcqTypes
    String qaTypes
    String mcqTotalTime


    static constraints = {
        topicId blank:true, nullable:true
        resourceName blank:false, nullable:false
        resType blank:false, nullable:false
        createdBy blank:false, nullable:false
        resLink blank:false, nullable:false
        filename blank:true, nullable:true
        sharing blank:true, nullable:true
        noOfViews blank:true, nullable: true
        noOfFavourites blank:true, nullable: true
        noOfLikes blank:true, nullable: true
        quizMode blank:true, nullable: true
        chapterId blank:true, nullable: true
        chapterDesc blank:true, nullable: true
        noOfPages blank:true, nullable: true
        fileSize blank:true, nullable: true
		modifiedFile blank:true, nullable:true
        description blank:true, nullable:true
        canReorderQuestions blank:true, nullable:true
        examSyllabus blank:true, nullable:true
        status blank:true, nullable:true
        sortOrder blank:true, nullable:true
        grade blank:true, nullable:true
        language1 blank:true, nullable:true
        language2 blank:true, nullable:true
        examSubject blank:true, nullable:true
        examId blank:true, nullable:true
        testStartDate blank:true, nullable:true
        testEndDate blank:true, nullable:true
        noOfTestTakers blank:true, nullable:true
        parentId blank:true, nullable:true
        videoPlayer blank:true, nullable:true
        allowComments blank:true, nullable:true
        displayComments blank:true, nullable:true
        syllabus blank:true, nullable:true
        subject blank:true, nullable:true
        downloadlink1 blank:true, nullable:true
        downloadlink2 blank:true, nullable:true
        downloadlink3 blank:true, nullable:true
        testResultDate blank:true, nullable:true
        siteId blank:true, nullable:true
        resSubType blank:true, nullable:true
        privacyLevel blank:true, nullable:true
        ebupChapterLink blank:true, nullable: true
        flashCardFastTime blank:true, nullable: true
        fastTimeUsername  blank:true, nullable: true
        indContentType blank:true, nullable: true
        allowReAttempt blank: true,nullable: true
        streamKey blank:true, nullable:true
        channelId blank:true, nullable:true
        resDeepLink blank:true, nullable:true
        currentAffairsType blank:true, nullable:true
        zoomLevel blank:true, nullable:true
        deletedBy blank:true, nullable:true
        gptResourceType blank:true, nullable:true
        vectorStored blank:true, nullable:true
        extractPath blank:true, nullable:true
        //add mcqTypes and qaTypes and size 1000
        mcqTypes blank:true, nullable:true, maxSize: 1000
        qaTypes blank:true, nullable:true, maxSize: 1000
        mcqTotalTime blank: true, nullable: true
    }
}
