package com.wonderslate.data

class MessageTimeUserMap {

    //instead of using from and to.. we are using little hack of less and greater.. to make 1 less db query when we finding the list of latest msg
    Long lesserUserId
    Long greaterUserId
    Long groupId
    String messagetype
    Date lastUpdated

    static constraints = {
        lesserUserId blank:true, nullable: true
        greaterUserId blank:true, nullable: true
        groupId blank:true, nullable: true
        messagetype blank:true, nullable: true
    }
}
