package com.wonderslate.data

class SiteMst {

    String siteName
    String clientName
    String displayInMainSite
    String categories
    Long publisherId
    String displineCoverImage  //use for mobile only or both email and mobile flag
    String teachingNotes //user for whitelabel logo
    String teachingSlides
    String sampleChapters
    String mediaLinks
    String exercises  // for book access code enabled
    Boolean otpReg
    String googleClientId
    String fbMessageDatabaseUrl
    String fbMessageKeyFile
    String fbFirebaseWebAPI
    String sendNotification
    String googleApiKey
    String domainUriPrefix
    String iosBundleId
    String androidPackageName
    String razorPayKeyId
    String razorPaySecretKey
    String channelId
    String siteBaseUrl
    String smsUsername
    String smsPassword
    String smsSenderId
    String smsSenderName
    String appOnly
    String smsUrl
    String smsUrl1
    String securityKey
    String smsResendUrl
    String smsResendUrl1
    String googleUniversalAnalyticsKey
    String jwChannelId
    String jwAuthId
    String sageOnly
    String appInApp
    String awsAccessKeyId
    String awsSecretAccessKey
    String currentAffairsType
    String prepjoySite
    String playStoreInstallUrl
    String allBooksLibrary
    String downloadBookChapters
    String instituteLibrary
    String siteDomainName
    String fromEmail
    String mainResourcesPage
    String fixPayments
    String commonWhiteLabel
    String sellPrintOnMainSite
    String sellPrintOnWhiteLabel
    String productYoutubeLink
    String youtubeLinkTitle
    String associatedSites

    static constraints = {
        publisherId blank:true, nullable:true
        displineCoverImage blank:true, nullable:true
        teachingNotes blank:true, nullable:true
        teachingSlides blank:true, nullable:true
        sampleChapters blank:true, nullable:true
        mediaLinks blank:true, nullable:true
        exercises blank:true, nullable:true
        otpReg blank:true, nullable:true, defaultValue: false
        fbFirebaseWebAPI blank:true, nullable:true
        googleClientId blank:true, nullable:true
        fbMessageDatabaseUrl blank:true, nullable:true
        fbMessageKeyFile blank:true, nullable:true
        sendNotification blank:true, nullable:true
        googleApiKey blank:true, nullable:true
        domainUriPrefix blank:true, nullable:true
        iosBundleId blank:true, nullable:true
        androidPackageName blank:true, nullable:true
        razorPayKeyId blank:true, nullable:true
        razorPaySecretKey blank:true, nullable:true
        channelId blank:true, nullable:true
        smsUsername blank:true, nullable:true
        smsPassword blank:true, nullable:true
        smsSenderId blank:true, nullable:true
        smsSenderName blank:true, nullable:true
        appOnly blank:true, nullable:true
        smsUrl blank:true, nullable:true
        smsUrl1 blank:true, nullable:true
        securityKey blank:true, nullable:true
        smsResendUrl blank:true, nullable:true
        smsResendUrl1 blank:true, nullable:true
        googleUniversalAnalyticsKey blank:true, nullable:true
        jwChannelId blank:true, nullable:true
        jwAuthId blank:true, nullable:true
        sageOnly blank:true, nullable:true
        appInApp blank:true, nullable:true
        awsAccessKeyId blank:true, nullable:true
        awsSecretAccessKey blank:true, nullable:true
        currentAffairsType blank:true, nullable:true
        prepjoySite blank:true, nullable:true
        playStoreInstallUrl blank:true, nullable:true
        allBooksLibrary blank:true, nullable:true
        downloadBookChapters blank:true, nullable:true
        instituteLibrary blank:true, nullable:true
        siteDomainName blank:true, nullable:true
        fromEmail blank:true, nullable:true
        mainResourcesPage blank:true, nullable:true
        fixPayments blank:true, nullable:true
        commonWhiteLabel blank:true, nullable:true
        sellPrintOnMainSite blank:true, nullable:true
        sellPrintOnWhiteLabel blank:true, nullable:true
        productYoutubeLink blank:true, nullable:true
        youtubeLinkTitle blank:true, nullable:true
        associatedSites blank:true, nullable:true
    }
    static mapping = {
        id generator: 'assigned'
    }
}
