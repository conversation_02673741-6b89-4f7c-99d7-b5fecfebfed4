package com.wonderslate.data

class PurchaseOrder {
    Date dateCreated
	Integer itemCode
    Double amount
    String currency	
    String status
	String username	
    Integer siteId
	String paymentId
	String poFor
    Integer poReference
    String poMethod
	Integer sequencePo
	Integer gstPercentage
	Double bookPrice
	Integer discountId
	Double discountAmount
	String affiliationCd
	Long cartMstId
	Integer instituteId
	String directSales
	String subscriptionId
	String bookType
	String paymentFrom

    static constraints = {
		dateCreated blank: false, nullable: false	
		itemCode blank: false, nullable: false		
		amount blank: false, nullable: false	
		currency blank: false, nullable: false	
		status blank: false, nullable: false		
		username blank: false, nullable: false	
		siteId blank: false, nullable: false	
		paymentId blank: false, nullable: false
		poFor blank: false, nullable: false
        poReference blank: true, nullable: true
        poMethod blank: true, nullable: true
		sequencePo blank: true, nullable: true
		gstPercentage blank: true, nullable: true
		bookPrice blank: true, nullable: true
		discountId blank: true, nullable: true
		discountAmount blank: true, nullable: true
		affiliationCd blank: true, nullable: true
		cartMstId blank: true, nullable: true
		instituteId blank: true, nullable: true
		directSales blank: true, nullable: true
		subscriptionId blank: true, nullable: true
		bookType blank: true, nullable: true
		paymentFrom blank: true, nullable: true
	}

	static mapping = {
		datasource 'wsshop'
	}
}
