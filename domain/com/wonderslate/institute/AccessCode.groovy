package com.wonderslate.institute

class AccessCode {
    String code
    String username
    Long instituteId
    Date dateCreated
    Date dateRedeemed
    String status
    Integer siteId


    static constraints = {
        dateRedeemed blank:true, nullable: true
        code blank:true, nullable: true
        username blank:true, nullable: true
        instituteId blank:true, nullable: true
        status blank:true, nullable: true
        siteId blank:true, nullable: true
    }

    static mapping = {
        datasource 'wsuser'
    }
}
