package com.wonderslate.institute

class CourseBatchesDtl {
    Long courseId
    Long conductedBy
    Long conductedFor
    Date startDate
    Date endDate
    String status
    String name
    Integer groupId
    String grade
    String syllabus


    static constraints = {
        startDate blank:true, nullable: true
        endDate blank:true, nullable: true
        groupId blank:true, nullable: true
        grade blank:true, nullable: true
        syllabus blank:true, nullable: true
        courseId blank:true, nullable: true
    }
    static mapping = {
        datasource 'wsuser'
    }
}
