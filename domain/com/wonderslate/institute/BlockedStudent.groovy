package com.wonderslate.institute

class BlockedStudent {
    Long testId
    String username
    String blockedBy        // The instructor’s username
    String reason           // Optional reason for blocking
    Date blockedAt          // Timestamp when blocking occurred

    static constraints = {
        reason nullable: true, blank: true
    }

    static mapping = {
        datasource 'wsuser'
           }
}
