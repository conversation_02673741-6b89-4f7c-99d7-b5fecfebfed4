package com.wonderslate.institute

class InstituteCourseMst {
    Long id
    String name
    String gradeType // 'Year', 'Semester', 'Custom'
    Integer gradeStart // Starting number of grades/years/semesters
    Integer gradeEnd // Ending number of grades/years/semesters
    String customGrades // Comma-separated values for custom grades

    static constraints = {
        name blank: false, nullable: false
        gradeType inList: ['Year', 'Semester', 'Custom'], nullable: false
        gradeStart nullable: true
        gradeEnd nullable: true
        customGrades blank: true, nullable: true
    }

    static mapping = {
        datasource 'wsuser'
      }
}
