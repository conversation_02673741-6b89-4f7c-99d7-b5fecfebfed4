package com.wonderslate.institute

class InstituteMst {
    String name
    String type
    String addressLine1
    String addressLine2
    String town
    String state
    String country
    String zipcode
    // Institution Contact name is the librarian of the institute
    String contactName
    String contactNumber
    String contactEmail
    String ipRestricted
    Integer noOfUsers
    Integer usersLoginlimit
    Long siteId
    Long publisherId
    String tagline
    String aboutUs
    String contactDetails
    String logo
    String urlname
    String website
    String facebook
    String instagram
    String linkedin
    String youtube
    String twitter
    String fax
    String checkOutDays
    String fullLibraryView
    String paidFreeTab
    String eduWonder
    String level
    String syllabus
    String section3
    String section4
    String seoDescription
    String shopEnabled
    String leaderBoardEnabled
    String defaultBooksTemplateInstitute
    String copyDefaultBooks
    String goneLive
    Date goneLiveDate
    String preUniversity
    String secretKey
    String forceUserValidity
    Date dateCreated
    String associatedLibraries
    String driveForInstructor
    String driveForStudent
    String raForInstructor
    String raForStudent
    String showReferenceSection
    String enableTest
    String enableAnalytics
    String enableQuestionPaper


    static constraints = {
        addressLine1 blank:true, nullable: true
        addressLine2 blank:true, nullable: true
        town blank:true, nullable: true
        state blank:true, nullable: true
        country blank:true, nullable: true
        zipcode blank:true, nullable: true
        contactName blank:true, nullable: true
        contactNumber blank:true, nullable: true
        contactEmail blank: true, nullable: true
        ipRestricted blank:true, nullable: true
        siteId blank:true, nullable: true
        noOfUsers blank:true, nullable: true
        usersLoginlimit blank:true, nullable: true
        publisherId blank:true, nullable: true
        tagline blank:true, nullable: true
        aboutUs blank:true, nullable: true
        contactDetails blank:true, nullable: true
        logo blank:true, nullable: true
        urlname blank:true, nullable: true
        website blank:true, nullable: true
        linkedin blank:true, nullable: true
        youtube blank:true, nullable: true
        facebook blank:true, nullable: true
        instagram blank:true, nullable: true
        twitter blank:true, nullable: true
        fax blank: true, nullable: true
        checkOutDays blank: true, nullable: true
        fullLibraryView blank: true, nullable: true
        paidFreeTab blank: true, nullable: true
        eduWonder blank: true, nullable: true
        level blank: true, nullable: true
        syllabus blank: true, nullable: true
        section3 blank: true, nullable: true
        section4 blank: true, nullable: true
        seoDescription blank: true, nullable: true
        shopEnabled blank: true, nullable: true
        leaderBoardEnabled blank: true, nullable: true
        defaultBooksTemplateInstitute blank: true, nullable: true
        copyDefaultBooks blank: true, nullable: true
        goneLive blank: true, nullable: true
        goneLiveDate blank: true, nullable: true
        preUniversity blank: true, nullable: true
        secretKey blank: true, nullable: true
        forceUserValidity blank: true, nullable: true
        dateCreated blank: true, nullable: true
        associatedLibraries blank: true, nullable: true
        driveForInstructor blank: true, nullable: true
        driveForStudent blank: true, nullable: true
        raForInstructor blank: true, nullable: true
        raForStudent blank: true, nullable: true
        showReferenceSection blank: true, nullable: true
        enableTest blank: true, nullable: true
        enableAnalytics blank: true, nullable: true
        enableQuestionPaper blank: true, nullable: true
    }
    static mapping = {
        datasource 'wsuser'
    }
}
