    package com.wonderslate.institute

class BatchUserDtl {
    Long batchId
    String username
    Date dateCreated
    String createdBy
    String instructor
    String classTeacher
    String admissionNo
    Date validityDate
    String modifiedUsername
    Date earlierDate
    String userType
    static constraints = {
        instructor blank:true, nullable: true
        classTeacher blank:true, nullable: true
        admissionNo blank:true, nullable: true
        validityDate blank:true, nullable: true
        modifiedUsername blank:true, nullable: true
        earlierDate blank:true, nullable: true
        userType blank:true, nullable: true
    }
    static mapping = {
        datasource 'wsuser'
    }

}
