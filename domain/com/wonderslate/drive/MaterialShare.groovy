package com.wonderslate.drive

class MaterialShare {

    Long materialId         // Reference to StudyMaterial.id
    Long batchId            // Reference to course_batches_dtl.id
    String sharedByUsername // User's username who shared the material
    Date sharedAt

    static constraints = {
        materialId nullable: false
        batchId nullable: false
        sharedByUsername blank: false, maxSize: 255
        sharedAt nullable: false
    }

    static mapping = {
        datasource 'wsuser'
    }
}
