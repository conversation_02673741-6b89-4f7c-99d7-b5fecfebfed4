package com.wonderslate.drive

class WebInteraction {
    String username
    String webpageUrl
    String userQuery
    String aiResponsePart1
    String aiResponsePart2
    Date timestamp

    static constraints = {
        username nullable: false, blank: false
        webpageUrl nullable: false, blank: false
        userQuery nullable: false, blank: false
        aiResponsePart1 nullable: false, blank: false
        aiResponsePart2 nullable: true, blank: true
        timestamp nullable: false
    }
    static mapping = {
        datasource 'wsuser'
    }
}
