package com.wonderslate.qp

class QuestionPaperPattern {
    String createdBy // Username of the user who created this pattern
    Long id
    String name // Name of the pattern
    String description // Optional description of the pattern
    Date dateCreated // Automatically managed by Grails
    Date lastUpdated // Automatically managed by Grails
    Long instituteId
    Long bookId // ID of the BooksMst table

    static hasMany = [sections: Section]

    static constraints = {
        name(nullable: false, blank: false, maxSize: 255)
        description(nullable: true, maxSize: 500)
        instituteId(nullable: true)
        bookId(nullable: true)
    }
}

class Section {
    String createdBy // Username of the user who created this section
    Long id
    String name // Name of the section (e.g., Section A, B, C)
    String instructions // Section-specific instructions
    Integer totalMarks // Total marks for the section
    QuestionPaperPattern pattern // Parent question paper pattern

    static hasMany = [questionTypes: QuestionType]

    static constraints = {
        name(nullable: false, blank: false, maxSize: 100)
        instructions(nullable: true, maxSize: 500)
        totalMarks(nullable: false, min: 0)
    }
}

class QuestionType {
    String createdBy // Username of the user who created this question type
    Long id
    String type // Type of question (e.g., MCQ, Short Answer, Long Answer)
    Integer numberOfQuestions // Number of questions of this type in the section
    Integer marksPerQuestion // Marks assigned to each question of this type
    Section section // Parent section

    static constraints = {
        type(nullable: false, blank: false, maxSize: 50)
        numberOfQuestions(nullable: false, min: 0)
        marksPerQuestion(nullable: true, min: 0)
    }
}
