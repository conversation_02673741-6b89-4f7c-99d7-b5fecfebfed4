package com.wonderslate.qp

class QuestionPaperSet {
    String createdBy // Username of the user who created this set
    Long id
    String groupId // Auto-generated identifier for grouping sets
    Boolean isSelected // Flag to indicate which set is selected for the exam
    QuestionPaperPattern pattern // Associated pattern
    String header // Editable header populated from pattern description
    Long instituteId
    Date dateCreated
    String name // Name of the set

    static hasMany = [sections: QuestionPaperSection]

    static constraints = {
        groupId nullable: false
        isSelected nullable: false
        header nullable: true, maxSize: 1000
        instituteId(nullable: true)
        name nullable: true, maxSize: 255
    }
}

class QuestionPaperSection {
    String createdBy // Username of the user who created this section
    Long id
    QuestionPaperSet questionPaperSet // Parent question paper set
    String name // Section name (e.g., Section A, Section B)
    String description // Editable description populated from pattern section description

    static hasMany = [questions: QuestionPaperQuestion]

    static constraints = {
        name nullable: false, maxSize: 255
        description nullable: true, maxSize: 1000
    }
}

class QuestionPaperQuestion {
    String createdBy // Username of the user who created this question
    Long id
    QuestionPaperSection section // Parent section
    Long objId // ID of the question from ObjectiveMst
    Integer marks // Marks associated with the question
    String chapterName // Name of the chapter from ChaptersMst

    static constraints = {
        objId nullable: false
        marks nullable: true
        chapterName nullable: true, maxSize: 255
    }
}

