package com.wonderslate.publish

class Publishers {
    String name
    String contactPerson
    String landline
    String mobile
    String email
    String address1
    String address2
    String city
    String state
    String country
    String pincode
    Integer royalty
    String website
    String description
    String logo
    String coverImage
    Integer siteId
    Date dateCreated
    String createdBy
    String urlname
    String facebook
    String instagram
    String youtube
    String tweeter
    String quote
    String telegram
    String publisherType
    String backgroundColor
    String tagline
    String showVideoOnlyInApp
    String sellOnlyInCountry
    String publisherNameForTitle
    String siteTitle
    String siteDescription
    String keywords
    String sendEbookEmail
    Integer printBookShare
    Integer ebookShareOnWS
    Integer ebookShareOnWhitelabel
    String customPrompt

    static constraints = {
        contactPerson blank:true, nullable: true
        landline blank:true, nullable: true
        mobile blank:true, nullable: true
        email blank:true, nullable: true
        address1 blank:true, nullable: true
        address2 blank:true, nullable: true
        city blank:true, nullable: true
        state blank:true, nullable: true
        country blank:true, nullable: true
        pincode blank:true, nullable: true
        royalty blank:true, nullable: true
        website blank:true, nullable: true
        description blank:true, nullable: true
        logo blank:true, nullable: true
        coverImage blank:true, nullable: true
        siteId blank:true, nullable: true
        dateCreated blank:true, nullable: true
        createdBy blank:true, nullable: true
        urlname blank:true, nullable: true
        facebook blank:true, nullable: true
        instagram blank:true, nullable: true
        youtube blank:true, nullable: true
        tweeter blank:true, nullable: true
        quote blank:true, nullable: true
        telegram blank:true, nullable: true
        publisherType blank:true, nullable: true
        backgroundColor blank:true, nullable: true
        tagline blank:true, nullable: true
        showVideoOnlyInApp blank:true, nullable: true
        sellOnlyInCountry blank:true, nullable: true
        publisherNameForTitle blank:true, nullable: true
        siteTitle blank: true, nullable: true
        siteDescription blank: true, nullable: true
        keywords blank: true, nullable: true
        sendEbookEmail blank: true, nullable: true
        printBookShare blank: true, nullable: true
        ebookShareOnWS blank: true, nullable: true
        ebookShareOnWhitelabel blank: true, nullable: true
        customPrompt blank: true, nullable: true

    }

    static mapping = {
        datasource 'wsshop'
    }
}
