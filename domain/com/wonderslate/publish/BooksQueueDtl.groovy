package com.wonderslate.publish

class BooksQueueDtl {
    Long bookId
    String username
    Long batchId
    String action
    Date dateCreated
    String addedBy
    Date expiryDate
    Date addedDate
    Date estimatedDate



    static constraints = {
        batchId blank: true, nullable: true
        action blank: true, nullable: true
        addedBy blank: true, nullable: true
        expiryDate blank: true, nullable: true
        addedDate blank: true, nullable: true
        estimatedDate blank: true, nullable: true
    }

    static mapping = {
        datasource 'wsuser'
    }
}
