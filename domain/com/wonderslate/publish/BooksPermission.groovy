package com.wonderslate.publish

class BooksPermission {
    Long bookId
    String username
	Integer poNo
	String poType
	String bookCode
	Date dateCreated
	String addedBy
    Date expiryDate
	Long packageBookId
	String clientPo
	Double price
	Long batchId
	Integer testsPoNo
	String testsPurchased
	Integer subscriptionId
	Integer bookCodeLogId
	Integer chatTokensBalance
	String bookType
    
	  static constraints = {
		poNo blank: true, nullable: true	
		poType blank: true, nullable: true
		bookCode blank: true, nullable: true
		dateCreated blank: true, nullable: true
		addedBy blank: true, nullable: true
        expiryDate blank: true, nullable: true
		packageBookId blank: true, nullable: true
		clientPo blank: true, nullable: true
		price blank: true, nullable: true
		  batchId blank: true, nullable: true
		  testsPoNo blank: true, nullable: true
		  testsPurchased blank: true, nullable: true
		  subscriptionId blank: true, nullable: true
		  bookCodeLogId blank: true, nullable: true
		  chatTokensBalance blank: true, nullable: true
		  bookType blank: true, nullable: true
	  }
	static mapping = {
		datasource 'wsuser'
	}
}
