package com.wonderslate.publish

class BooksPermissionCopy {
    Long bookId
    String username
    Integer poNo
    String poType
    String bookCode
    Date dateCreated
    String addedBy
    Date expiryDate
    Long packageBookId
    String clientPo
    Double price
    Long batchId
    String status
    Date returnDate
    Long bpId

    static constraints = {
        poNo blank: true, nullable: true
        poType blank: true, nullable: true
        bookCode blank: true, nullable: true
        dateCreated blank: true, nullable: true
        addedBy blank: true, nullable: true
        expiryDate blank: true, nullable: true
        packageBookId blank: true, nullable: true
        clientPo blank: true, nullable: true
        price blank: true, nullable: true
        batchId blank: true, nullable: true
        status blank: true, nullable: true
        returnDate blank: true, nullable: true
        bpId blank: true, nullable: true

    }
    static mapping = {
        datasource 'wsuser'
    }
}
