package com.wonderslate.discussions

class Questions {
    String question
    long chapterId
    long topicId
    long resId
    String username
    Date dateCreated
    String status
    String name
    String adminUser
    Integer chatNo
    String toUser
    Integer bookId
    Integer pollId
    Integer pollDuration

    static constraints = {
        chapterId blank:true, nullable: true
        topicId blank:true, nullable: true
        resId blank:true, nullable: true
        status blank:true, nullable: true
        name blank:true, nullable: true
        adminUser blank:true, nullable: true
        chatNo blank:true, nullable: true
        toUser blank:true, nullable: true
        bookId blank:true, nullable: true
        pollId blank:true, nullable: true
        pollDuration blank:true, nullable: true
    }
    static mapping = {
        datasource 'wscomm'
    }
}
