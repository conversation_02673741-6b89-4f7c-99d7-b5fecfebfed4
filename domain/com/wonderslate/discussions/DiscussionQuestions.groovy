

package com.wonderslate.discussions


class DiscussionQuestions {
    String question
    Boolean free
    // add book_id, chapter id
    String tags
    Boolean showQuestion
    Integer siteId
    Integer ansCount
    Integer upvoteCount
    String imgName
    Date dateCreated
    String createdBy
    Long dldId
//    Integer highAnsVote
//    Long highAnswerId
//    String highAnswer
//    String highUserName
//    String highUserId

    static mapping = {
        datasource 'wsuser'
    }
    static constraints = {
        dldId blank: true, nullable: true
        imgName blank: true, nullable: true
        ansCount blank: true, nullable: true
        upvoteCount blank: true, nullable: true
    }
}

