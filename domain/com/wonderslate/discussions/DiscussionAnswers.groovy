package com.wonderslate.discussions

class DiscussionAnswers {

        String answer
        Long discussionQuestionId
        Integer upVoteCount
        String imgName
        Boolean showAnswer
        Date dateCreated
        String createdBy

        static constraints = {
            imgName blank: true, nullable: true
            upVoteCount blank: true, nullable: true
        }

        static mapping = {
                datasource 'wsuser'
        }

}
