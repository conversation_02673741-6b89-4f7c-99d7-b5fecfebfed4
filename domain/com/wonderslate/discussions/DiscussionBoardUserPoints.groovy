package com.wonderslate.discussions

class DiscussionBoardUserPoints {
    /*
      DiscussionPointsMst values

      id : pointsFor  :   pointsValue
      1   :  Question added : 5
      2   :  Answer added :   10
      3   :   Question upvoted : 5
      4   :  Answer upvoted : 5
      5   :  Question Shared : 5
      6   :  Question fallowing : 0

      */
    String userId
    Long discussionPointsId
    Long questionId
    Long answerId

    static constraints = {
        questionId blank: true, nullable: true
        answerId blank: true, nullable: true
    }

    static mapping = {
        datasource 'wsuser'
    }
}
