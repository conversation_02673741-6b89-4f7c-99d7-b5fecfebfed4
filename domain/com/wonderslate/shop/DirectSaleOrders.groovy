package com.wonderslate.shop

class DirectSaleOrders {
    String username
    Integer siteId
    String createdBy
    Integer instituteId
    Date dateCreated
    String status
    String paymentMode
    String paymentId
    String dateUpdated
    Integer bookId

    static constraints = {
        instituteId blank:true, nullable: true
        status blank:true, nullable: true
        paymentMode blank:true, nullable: true
        paymentId blank:true, nullable: true
        dateUpdated blank:true, nullable: true
        bookId blank:true, nullable: true

    }
    static mapping = {
        datasource 'wsshop'
    }
}
