package com.wonderslate.shop

class VendorDeliveryDetails {

    Integer vendorId
    String calculationType
    Double threshold
    Double flatFee
    String currencyCd
    String createdBy
    Date dateUpdated


    static mapping = {
        datasource 'wsshop'
    }

    static constraints = {
        threshold blank: true, nullable: true
        currencyCd blank: true, nullable: true
        createdBy blank: true, nullable: true
        dateUpdated blank: true, nullable: true
        flatFee blank: true, nullable: true

    }
}
