package com.wonderslate.shop

class ShoppingCartOrdersDtl {
    String username
    long bookId
    long cartMstId
    Date dateCreated
    Integer siteId
    Double discountAmount
    Double bookPrice
    Double payablePrice
    Integer discountId
    String bookType
    String subscriptionId
    String subsStartingBookId
    String subsDuration

    static constraints = {
        discountAmount blank: true, nullable: true
        payablePrice blank: true, nullable: true
        bookPrice blank: true, nullable: true
        cartMstId blank: true, nullable: true
        discountId blank: true, nullable: true
        bookType blank: true, nullable: true
        subscriptionId blank: true, nullable: true
        subsStartingBookId blank: true, nullable: true
        subsDuration blank: true, nullable: true
    }

    static mapping = {
        datasource 'wsshop'
    }
}
