package com.wonderslate.shop

class ShoppingCartActiveDtl {

    String username
    long bookId
    Date dateCreated
    Integer siteId
    String bookType
    String subscriptionId
    String subsStartingBookId
    String subsDuration
    Long publisherId
    Double bookWeight

    static constraints = {
        bookType blank: true, nullable: true
        subscriptionId blank: true, nullable: true
        subsStartingBookId blank: true, nullable: true
        subsDuration blank: true, nullable: true
        publisherId blank: true, nullable: true
        bookWeight blank: true, nullable: true
    }

    static mapping = {
        datasource 'wsshop'
    }
}
