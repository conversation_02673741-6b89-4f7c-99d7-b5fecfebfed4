package com.wonderslate.shop

class BookPriceDtl {
     Integer bookId
     String currencyCd
     String bookType
     Double listPrice
     Double sellPrice
     Double gst
    Integer freeChatTokens

    static constraints = {
        listPrice blank:true, nullable: true
        gst blank:true, nullable: true
        freeChatTokens blank:true, nullable: true
    }
    static mapping = {
        datasource 'wsshop'
    }
}
