package com.wonderslate.shop

class OutsidePurchase {

    Integer siteId
    String name
    String mobile
    String email
    Integer bookId
    Double price
    String paymentReference
    Date dateCreated
    String accessCode
    String isbns
    String status
    Date dateUpdated
    String missedIsbns
    String saleSource
    String bookCode
    String paymentStatus
    Boolean isBookAdded


    static constraints = {
        mobile blank: true, nullable: true
        email blank: true, nullable: true
        paymentReference blank: true, nullable: true
        accessCode blank: true, nullable: true
        isbns blank: true, nullable: true
        status blank: true, nullable: true
        dateUpdated blank: true, nullable: true
        missedIsbns blank: true, nullable: true
        saleSource blank: true, nullable: true
        bookCode blank: true,nullable: true
        paymentStatus blank: true,nullable: true
        isBookAdded blank:true,nullable:true
    }
    static mapping = {
        datasource 'wsshop'
    }
}
