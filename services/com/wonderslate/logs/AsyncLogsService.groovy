package com.wonderslate.logs

import com.wonderslate.admin.AdminService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.cache.SearchService
import com.wonderslate.data.AutogptService
import com.wonderslate.data.BooksMst
import grails.async.DelegateAsync
import grails.async.Promise
import grails.async.Promises
import grails.transaction.Transactional

@Transactional
class AsyncLogsService {
    @DelegateAsync LogsService logsService
    @DelegateAsync AdminService adminService
    @DelegateAsync DataProviderService dataProviderService
    @DelegateAsync SearchService searchService
    @DelegateAsync AutogptService autogptService
    Promise addResView(Long resId,String username,String action,String source,String fromTab,String viewedFrom,Integer siteId, Integer duration){
        Promises.task {
            logsService.addResView(resId,username,action,source,fromTab,viewedFrom,siteId,duration)
        }
    }

    Promise addResViewChapter(Long chapterId,String username,String action,String source,String fromTab,String viewedFrom){
        Promises.task {
            logsService.addResViewChapter(chapterId,username,action,source,fromTab,viewedFrom)
        }
    }

    Promise updateBookView(bookId,viewSource,viewType,siteId,username,instituteId) {
        Promises.task {
            logsService.updateBookView(bookId,viewSource,viewType,siteId,username,instituteId)
        }
    }

    Promise updateUsageList(Long resId,String username) {
        Promises.task {
            logsService.updateUsageList(resId,username)
        }
    }

    Promise addQuizRecorderJSON(username,jsonObject,bookId) {
        Promises.task {
            logsService.addQuizRecorderJSON(username,jsonObject,bookId)
        }
    }

    Promise addQuizRecorder(username,params,bookId) {
        Promises.task {
            logsService.addQuizRecorder(username,params,bookId)
        }
    }

    Promise updateChapterAccess(String username,Long resId,Long chapterId) {
        Promises.task {
            logsService.updateChapterAccess(username,resId,chapterId)
        }
    }
    Promise addPackageBooksToUser(BooksMst booksMst, String oldBooksId){
        Promises.task {
            logsService.addPackageBooksToUser(booksMst,oldBooksId)
        }
    }

    Promise updateLiveVideoCount(Long resId,Long count,String status) {
        Promises.task {
            logsService.updateLiveVideoCount(resId,count,status)
        }
    }



    Promise updateQuizRecDtl(quizRecId,chapterId,userAnswers,username,siteId) {
        Promises.task {
            logsService.updateQuizRecDtl(quizRecId,chapterId,userAnswers,username,siteId,subject)
        }
    }


    Promise sendMcqUpdateNotification(chapterId,bookId,resId) {
        Promises.task {
            logsService.sendMcqUpdateNotification(chapterId, bookId, resId)
        }
    }

    Promise updateBookPrice(booksMst,valueHolder) {
        Promises.task {
            adminService.updateBookPrice(booksMst,valueHolder)
        }
    }
    Promise updateSearchMap(Integer siteId,String siteIdList) {
        Promises.task {
            dataProviderService.updateSearchMap(siteId,siteIdList)
        }
    }
    Promise updatePrintSearchMap() {
        Promises.task {
            searchService.updatePrintSearchMap()
        }
    }
    Promise updateSalesAffiliation(scd,bookId,username) {
        Promises.task {
            logsService.updateSalesAffiliation(scd,bookId,username)
        }
    }

    Promise runAutoGPT(chapterId,serverIPAddress) {
        Promises.task {
            autogptService.runAutoGPT(chapterId,serverIPAddress)
        }
    }

    Promise autoGPTPdfProcessorRunner(params) {
        Promises.task {
            autogptService.autoGPTPdfProcessorRunner(params)
        }
    }

}
