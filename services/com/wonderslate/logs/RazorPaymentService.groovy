package com.wonderslate.logs

import com.wonderslate.log.RazorPayment
import grails.transaction.Transactional

@Transactional
class RazorPaymentService {

    @Transactional
    def saveOrUpdatePayment(payment,siteId) {
        def json = payment
        def razorPayment = RazorPayment.findByRazorPaymentId(payment.id)
        if (razorPayment) {
            // If payment exists, update its properties
            razorPayment.siteId = siteId
            razorPayment.razorPaymentId = ""+json.id
            razorPayment.shoppingCartId = ""+json.notes.shoppingCartId
            razorPayment.username = ""+json.notes.username
            razorPayment.fee = ""+json.fee
            razorPayment.description = ""+json.description
            razorPayment.createdAt = ""+json.created_at
            razorPayment.amountRefunded = ""+json.amount_refunded
            razorPayment.bank = ""+json.bank
            razorPayment.errorReason = ""+json.error_reason
            razorPayment.errorDescription = ""+json.error_description
           razorPayment.captured = ""+json.captured
            razorPayment.contact = ""+json.contact
            razorPayment.invoiceId = ""+json.invoice_id
            razorPayment.currency = ""+json.currency
            razorPayment.international = ""+json.international
            razorPayment.email = ""+json.email
            razorPayment.amount = ""+json.amount
            razorPayment.refundStatus = ""+json.refund_status
            razorPayment.wallet = ""+json.wallet
            razorPayment.method = ""+json.method
            razorPayment.vpa = ""+json.vpa
            razorPayment.errorSource = ""+json.error_source
            razorPayment.errorStep = ""+json.error_step
            razorPayment.tax = ""+json.tax
            razorPayment.cardId = ""+json.card_id
            razorPayment.errorCode = ""+json.error_code
            razorPayment.orderId = ""+json.order_id
            razorPayment.entity = ""+json.entity
            razorPayment.status = ""+json.status
        } else {
            // If payment doesn't exist, create a new instance
            razorPayment = new RazorPayment(
                    siteId:siteId,
                    razorPaymentId : ""+json.id,
                    shoppingCartId: ""+json.notes.shoppingCartId,
                    username: ""+json.notes.username,
                    fee: ""+json.fee,
                    description: ""+json.description,
                    createdAt: ""+json.created_at,
                    amountRefunded: ""+json.amount_refunded,
                    bank: ""+json.bank,
                    errorReason: ""+json.error_reason,
                    errorDescription: ""+json.error_description,
                    captured: ""+json.captured,
                    contact: ""+json.contact,
                    invoiceId: ""+json.invoice_id,
                    currency: ""+json.currency,
                    international: ""+json.international,
                    email: ""+json.email,
                    amount: ""+json.amount,
                    refundStatus: ""+json.refund_status,
                    wallet: ""+json.wallet,
                    method: ""+json.method,
                    vpa: ""+json.vpa,
                    errorSource: ""+json.error_source,
                    errorStep: ""+json.error_step,
                    tax: ""+json.tax,
                    cardId: ""+json.card_id,
                    errorCode: ""+json.error_code,
                    orderId: ""+json.order_id,
                    entity: ""+json.entity,
                    status: ""+json.status
            )
        }

        if (razorPayment.save(failOnError: true, flush: true)) {
            return "Payment saved/updated successfully."
        } else {
            return "Failed to save/update payment."
        }
    }
}
