package com.wonderslate.logs

import com.wonderslate.log.AutogptErrorLogger
import grails.transaction.Transactional
import org.springframework.transaction.annotation.Propagation


class AutogptErrorLoggerService {

    @Transactional (propagation = Propagation.REQUIRES_NEW)
    def createLog(Long chapterId, Long resId, String promptType, String errorString, String inputToApi){
        AutogptErrorLogger autogptErrorLogger = new AutogptErrorLogger(chapterId: chapterId, resId: resId, promptType: promptType,
                errorString: errorString.substring(0, Math.min(errorString.length(), 1000)), inputToApi: inputToApi.substring(0, Math.min(inputToApi.length(), 1000)))
        autogptErrorLogger.save(failOnError: true, flush: true)
    }
}
