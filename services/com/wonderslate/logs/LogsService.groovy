package com.wonderslate.logs

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.DataNotificationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.UtilService
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.log.BooksViewDtl
import com.wonderslate.log.LiveVideoCount
import com.wonderslate.log.MdResourceView
import com.wonderslate.log.Quizrecorder
import com.wonderslate.log.Quizrecorderdtl
import com.wonderslate.log.ResourceView
import com.wonderslate.log.SalesAffiliationLog
import com.wonderslate.log.UserLoginLog
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.prepjoy.QuizQuestionUserSummary
import com.wonderslate.prepjoy.QuizRecDtl
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.prepjoy.QuizStatistics
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.ChapterAccess
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.ProgressService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import org.grails.web.json.JSONArray

import java.text.DateFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat


@Transactional
class LogsService {


    def redisService
    DataProviderService dataProviderService
    UtilService utilService
    DataNotificationService dataNotificationService
    def springSecurityService
    def grailsApplication
    def limit = 10
    def skip = 10
    UserManagementService userManagementService
    ProgressService progressService



    def addResView(Long resId,String username,String action,String source,String fromTab,String viewedFrom, Integer siteId,Integer duration){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(resId)
        String resType = "", resSubType = "", resourceName = ""
        MdResourceView mdResourceView = new MdResourceView()
        mdResourceView.resourceDtlId = resId
        if(username == null || username.isEmpty()) username = "ne"
        mdResourceView.username = username
        mdResourceView.action = action
        mdResourceView.source = source
        mdResourceView.fromTab = fromTab
        mdResourceView.viewedFrom = viewedFrom
        if(resourceDtl.resType == null || resourceDtl.resType.isEmpty()) resType = "ne"
        else resType = resourceDtl.resType
        mdResourceView.resType = resType
        if(resourceDtl.resSubType == null || resourceDtl.resSubType.isEmpty()) resSubType = "ne"
        else resSubType = resourceDtl.resSubType
        mdResourceView.resSubType = resSubType
        mdResourceView.siteId = siteId
        if(duration!=null) mdResourceView.duration=duration
        else mdResourceView.duration=0
        if(resourceDtl.resourceName == null || resourceDtl.resourceName.isEmpty()) resourceName = "ne"
        else resourceName = resourceDtl.resourceName
        mdResourceView.resName = resourceName
        DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date today = df1.parse(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
        mdResourceView.date = today
        if(resourceDtl.chapterId!=null) {
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
//           mdResourceView.bookId = booksMst.id
//           mdResourceView.bookName = booksMst.title
//           mdResourceView.chapterId = resourceDtl.chapterId
            if(booksMst.publisherId!=null) mdResourceView.publisherId = booksMst.publisherId
        }
        //for PrepRevision

        if(siteId.intValue()==45&&username!=null&&!"".equals(username)){
            List lastOneList = MdResourceView.findAllByUsernameAndResourceDtlId(username,resId,[max: 1, sort: "id", order: "desc"])
            MdResourceView lastOne = null
            if(lastOneList.size()>0) lastOne = lastOneList[0]
            int currentPosition = 0
            if(lastOne!=null&&!"true".equals(lastOne.stopRevision)&&lastOne.revisionNo!=null){
                currentPosition = lastOne.revisionNo.intValue()+1
            }
            if(lastOne!=null){
                lastOne.revisionCompleted = "true"
                lastOne.save(failOnError: true, flush:true)
            }
           mdResourceView.revisionNo = new Integer(currentPosition)
           mdResourceView.nextRevisionDate = getNextRevisionDate(currentPosition)
        }
        mdResourceView.save(failOnError: true, flush:true)
        updateResViewByUser(username,0)
        if(siteId.intValue()==45&&username!=null&&!"".equals(username)){

            updateRevisionListForUser(username)

        }
        return mdResourceView
    }

    def addDurationByResId(long id, int duration){
        MdResourceView mdResourceView = MdResourceView.findById(id)
        mdResourceView.duration = duration
        mdResourceView.save(failOnError: true, flush:true)
        return mdResourceView
    }

    def updateResViewByUser(String userName,int batchIndex) {
        try{
            int offset = batchIndex * skip
            List<MdResourceView> mdResourceViews = MdResourceView.findAllByUsername(userName,[max: limit, sort: "date", order: "desc", offset: offset])
            List resourcesViewLog = new ArrayList()
            mdResourceViews.collect { r ->
                String bookName,chapterName,startDate,reslink,bookId,chapterId
                String sql = "select bm.title,cm.name,rd.test_start_date testStartDate,rd.res_link resLink,bm.id bookId," +
                        " cm.id chapterId from resource_dtl rd,chapters_mst cm, books_mst bm  where  rd.chapter_id=cm.id " +
                        " and cm.book_id=bm.id and rd.id="+r.resourceDtlId
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new SafeSql(dataSource)
                def results = sql1.rows(sql);
                if(results!=null && results.size>=1){
                    bookName=results[0][0]
                    chapterName=results[0][1]
                    startDate=results[0][2]
                    reslink=results[0][3]
                    bookId=results[0][4]+""
                    chapterId=results[0][5]+""
                }
                resourcesViewLog.add('resourceDtlId':r.resourceDtlId,'dateCreated':r.date,'username':r.username,'action':r.action,'source':r.source,'fromTab':r.fromTab,'viewedFrom':r.viewedFrom
                        ,'siteId':r.siteId,'resName':r.resName,'resType':r.resType,'resSubType':r.resSubType,'duration':r.duration,'publisherId':r.publisherId,bookName:bookName,chapterName:chapterName,testStartDate:startDate!=null?startDate:"",quizId:r.resType=="Multiple Choice Questions"?reslink:"",bookId:bookId,chapterId:chapterId)
            }
            Gson gson = new Gson();
            String element = gson.toJson(resourcesViewLog,new TypeToken<List>() {}.getType())
            if(batchIndex == 0) {
                redisService.("resViewLogByUser_"+userName) = element

            }
            else if(batchIndex > 0) return resourcesViewLog
        }catch(Exception ex){
            println(ex)
        }
    }

    def updateRevisionListForUser(String userName) {
        try {

            List resourcesViewLog = new ArrayList()
            String sql = "select * from md_resource_view where username='"+userName+"' and revision_no is not null "+
                    " and stop_revision is null and revision_completed is null and next_revision_date > CURDATE() order by next_revision_date asc"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql);
            results.collect { r ->
                resourcesViewLog.add('resourceDtlId': r.resource_dtl_id, 'dateCreated': r.date, 'username': r.username, 'action': r.action, 'source': r.source, 'fromTab': r.from_tab, 'viewedFrom': r.viewed_from
                        , 'siteId': r.site_id, 'resName': r.res_name, 'resType': r.res_type, 'resSubType': r.res_sub_type, 'duration': r.duration, 'publisherId': r.publisher_id,
                          'logId':r.id,'nextRevisionDate':r.next_revision_date,'revisionNo':r.revision_no)
            }
            Gson gson = new Gson();
            String element = gson.toJson(resourcesViewLog, new TypeToken<List>() {}.getType())

            redisService.("revisionListByUser_" + userName) = element

        }
        catch(Exception ex){
            println(ex)
        }
    }

    def getResViewByUser(def userName, int batchIndex){
        if(redisService.("resViewLogByUser_"+userName) == null && batchIndex == 0){
            updateResViewByUser(userName,batchIndex)
        }
        List resourcesViewLog = null
        if(redisService.("resViewLogByUser_"+userName) != null && batchIndex == 0) resourcesViewLog = new JsonSlurper().parseText(redisService.("resViewLogByUser_"+userName))
        else if(batchIndex > 0) {
            resourcesViewLog = updateResViewByUser(userName,batchIndex)

        }
        if(resourcesViewLog == null) resourcesViewLog = new ArrayList()
        return resourcesViewLog
    }

    def getRevisionListByUser(def userName){
        if(redisService.("revisionListByUser_"+userName) == null){
            updateRevisionListForUser(userName)
        }
        List resourcesViewLog = null
        if(redisService.("revisionListByUser_"+userName) != null ) resourcesViewLog = new JsonSlurper().parseText(redisService.("revisionListByUser_"+userName))

        if(resourcesViewLog == null) resourcesViewLog = new ArrayList()
        return resourcesViewLog
    }

    def addResViewChapter(Long chapterId,String username,String action,String source,String fromTab,String viewedFrom) {
        ResourceView resourceView = new ResourceView(chapterId: chapterId,
                username: username, action: action, source: source,fromTab:fromTab,viewedFrom:viewedFrom);
        resourceView.save(failOnError: true, flush: true)

    }


    def updateBookView(bookId,viewSource,viewType,siteId,username,instituteId){
          BooksViewDtl  booksViewDtl = new BooksViewDtl(bookId:new Long(bookId), viewSource: viewSource, viewType:viewType,username:username,instituteId:instituteId?instituteId:null,
                    siteId:siteId)
        booksViewDtl.save(flush:true, failOnError: true)

        //for WS and Libwonder
        if(instituteId!=null){
               CourseBatchesDtl defaultCBD =dataProviderService.getDefaultCourseBatchesDtl(new Integer(""+instituteId))
            if(defaultCBD!=null) {
                dataProviderService.getLastReadBooksForInstitute(username, "" + defaultCBD.id)
            }
        }

        userManagementService.getLatestReadBooks(username)
    }

    def updateUsageList(Long resId,String username) {
        /**
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(resId)
        if (resourceDtl != null && resourceDtl.chapterId != null) {
            if (redisService.("seenResources_" + username + "_" + resourceDtl.chapterId) == null) {
                String seenResIds=",";
                //sending only the attempted quiz info
                if(redisService.("defaultResourceIDs_"+resourceDtl.chapterId)==null) dataProviderService.getChapterDefaultResourcesAsString(resourceDtl.chapterId)
                if(redisService.("defaultResourceIDs_"+resourceDtl.chapterId)!=null&&!"".equals(redisService.("defaultResourceIDs_"+resourceDtl.chapterId))) {
                    String sql = "SELECT distinct(quizid) FROM wslog.quizrecorder qr " +
                            " where qr.username='" + username + "' " +
                            "and  qr.quizid in (" + dataProviderService.getChapterDefaultResourcesAsString(resourceDtl.chapterId) + ")"
                    def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                    def sql1 = new SafeSql(dataSource)
                    def results = sql1.rows(sql);
                    results.each { resource ->
                        seenResIds += resource.quizid + ","
                    }
                }
                redisService.("seenResources_"+username+"_"+resourceDtl.chapterId) = seenResIds
            } else {
                if (("" + redisService.("seenResources_" + username + "_" + resourceDtl.chapterId)).indexOf("," + resId + ",") == -1)
                    redisService.("seenResources_" + username + "_" + resourceDtl.chapterId) = redisService.("seenResources_" + username + "_" + resourceDtl.chapterId) + resId + ","
            }
        }*/
    }

  def addQuizRecorderJSON(username,jsonObject,bookId){
     DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
      Date quizDate = df.parse(jsonObject.takenAt);
      String userAnswers = jsonObject.queData;
       DecimalFormat df1 = new DecimalFormat("0.00");
      double score = (jsonObject.score!=null?Double.parseDouble(""+jsonObject.score):0)
      Quizrecorder quizrecorder = new Quizrecorder(username: username, testgenid: (jsonObject.testgenid!=null?new Integer(jsonObject.testgenid):null),
              quizid: (jsonObject.quizId!=null?new Integer(jsonObject.quizId):null), timetaken: jsonObject.timeTaken, totalQuestions: new Integer(jsonObject.noOfQuestions),
              correctAnswers: jsonObject.correctAnswers, wrongAnswers: jsonObject.wrongAnswers, skipped: jsonObject.skipped, endtime: new Date(), quizTakenTime: quizDate,bookId: bookId,
              score: jsonObject.score!=null?new Double(df1.format(score)):null)
      quizrecorder.save(failOnError: true, flush: true)
      JSONArray arrayObject = jsonObject.queData;
      org.grails.web.json.JSONObject questionDetails
      String correctAnswer
      for (int i = 0; i < (arrayObject.length()); i++) {
          questionDetails = arrayObject.get(i);
          correctAnswer=questionDetails.correctAnswer
          if("true".equals(questionDetails.skipped)) correctAnswer="skipped"
          Quizrecorderdtl quizrecorderdtl = new Quizrecorderdtl(quizrecorderid: quizrecorder.id, objectivemstid: new Integer(questionDetails.id),
                  option1: (questionDetails.ans1!=null?""+questionDetails.ans1:null),
                  option2: (questionDetails.ans2!=null?""+questionDetails.ans2:null),
                  option3: (questionDetails.ans3!=null?""+questionDetails.ans3:null),
                  option4: (questionDetails.ans4!=null?""+questionDetails.ans4:null),
                  option5: (questionDetails.ans5!=null?""+questionDetails.ans5:null),
                  correctanswer: correctAnswer)
          quizrecorderdtl.save(failOnError: true, flush: true)
      }


  }

    def addQuizRecorder(username,params,bookId){
        DecimalFormat df = new DecimalFormat("0.00");
        double score = (params.score!=null?Double.parseDouble(params.score):0)
        Quizrecorder quizrecorder = new Quizrecorder(username: username, testgenid: (params.testgenid!=null)?new Integer(params.testgenid):null,
                quizid:(params.quizid!=null)? new Integer(params.quizid):null,
                timetaken: params.timetaken, totalQuestions: new Integer(params.noOfQuestions), correctAnswers: params.correctAnswers,
                wrongAnswers: params.wrongAnswers, skipped: params.skipped, endtime: new Date(), quizTakenTime: new Date(),
                bookId: bookId,score: params.score!=null?new Double(df.format(score)):null)
        quizrecorder.save(failOnError: true, flush: true)
        for (int i = 0; i < (Integer.parseInt(params.noOfQuestions)); i++) {
            Quizrecorderdtl quizrecorderdtl = new Quizrecorderdtl(quizrecorderid: quizrecorder.id, objectivemstid: new Integer(params["id" + i]), option1: params["option1" + i], option2: params["option2" + i], option3: params["option3" + i], option4: params["option4" + i], option5: params["option5" + i], correctanswer: params["correctAnswer" + i])
            quizrecorderdtl.save(failOnError: true, flush: true)
        }

    }

    def updateChapterAccess(String username,Long resId,Long chapterId){
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
        //update the last accessed topic
        ChapterAccess chapterAccess = ChapterAccess.findByBookIdAndUsername(chaptersMst.bookId, username)
        if (chapterAccess == null) {
            chapterAccess = new ChapterAccess(username: username, bookId: chaptersMst.bookId, chapterId: chaptersMst.id, resourceId: resId)
            chapterAccess.save(failOnError: true, flush: true);
        } else {
             String sql1 = "update ChapterAccess set resourceId=" + resId + " where bookId=" + chaptersMst.bookId + " and username='" + username + "'";
            ChapterAccess.executeUpdate(sql1);
        }
    }
    def addPackageBooksToUser(BooksMst booksMst, String oldBooksId){
       //changed the logic of showing the package books. Resetting the cache of each user books list is expensive and time consuming operation. So refershing the whole cache
        redisService.flushDB()

    }

    def updateLiveVideoCount(Long resId, Long count, String status) {
        String statusAction=status
        Integer liveCount = count
        switch (statusAction) {
            case "update":
                try {
                    LiveVideoCount.executeUpdate("update LiveVideoCount set  userCount=" + liveCount + " where resId =" + resId)
                } catch (Exception ex) {
                    println(ex)
                }
                redisService.("videoUserCount_"+resId)=liveCount+""
                break;
            case "add":
                LiveVideoCount liveVideoCount = LiveVideoCount.findByResId(new Long(resId))
                if (liveVideoCount == null) {
                    liveVideoCount = new LiveVideoCount(resId: resId, userCount: liveCount)
                    liveVideoCount.save(failOnError: true, flush: true);
                } else {
                    liveCount = liveVideoCount.userCount + liveCount
                    liveVideoCount.userCount = liveCount
                    liveVideoCount.save(failOnError: true, flush: true);
                }
                redisService.("videoUserCount_"+resId)=liveVideoCount.userCount+""
                break;
            case "removeupdate":
                try {
                    LiveVideoCount.executeUpdate("update LiveVideoCount set  userCount=" + liveCount + " where resId =" + resId)
                } catch (Exception ex) {
                    println(ex)
                }
                redisService.("videoUserCount_"+resId)=liveCount+""
                break;
            case "removeadd":
                LiveVideoCount liveVideoCount = LiveVideoCount.findByResId(new Long(resId))
                if (liveVideoCount == null) {
                    liveVideoCount = new LiveVideoCount(resId: resId, userCount: liveCount)
                    liveVideoCount.save(failOnError: true, flush: true);
                } else {
                    liveCount = liveVideoCount.userCount - liveCount
                    liveVideoCount.userCount = liveCount
                    liveVideoCount.save(failOnError: true, flush: true);
                }
                redisService.("videoUserCount_"+resId)=liveVideoCount.userCount+""
                break;

        }
    }

    def getDefaultRevisionFrequency(){
        if(redisService.("defaultRevisionFrequency")==null){
            KeyValueMst keyValueMst = KeyValueMst.findByKeyName("defaultRevisionFrequency")
            redisService.("defaultRevisionFrequency") = keyValueMst.keyValue
        }

        return redisService.("defaultRevisionFrequency")
    }

    def getNextRevisionDate(int currentPosition){
        String defaultRevisionFrequency = getDefaultRevisionFrequency()
        String[]  frequency = defaultRevisionFrequency.split(",")

        if(currentPosition>frequency.size()) currentPosition=0;
        Calendar c = Calendar.getInstance()
        c.add(Calendar.DATE, Integer.parseInt(frequency[currentPosition]))

        return c.getTime()

    }

    def stopRevision(Integer logId){
        MdResourceView mdResourceView = MdResourceView.findById(logId)
        if(mdResourceView!=null){
            mdResourceView.stopRevision="true"
            mdResourceView.revisionCompleted="true"
            mdResourceView.save(failOnError: true, flush: true)
            updateRevisionListForUser(mdResourceView.username)
        }

    }

    def updateQuizQuestionUserSummary(username,objId,siteId,userOption,correctOption,chapterId){
        QuizQuestionUserSummary quizQuestionUserSummary
        Integer bookId = null
        String lastAnswer = "skipped"


        if(chapterId!=null) quizQuestionUserSummary = QuizQuestionUserSummary.findByUsernameAndObjIdAndChapterId(username,objId,chapterId)
        else quizQuestionUserSummary = QuizQuestionUserSummary.findByUsernameAndObjId(username,objId)

        if(quizQuestionUserSummary==null){
            if(chapterId!=null){
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
                bookId = chaptersMst.bookId
            }
            Integer correct = new Integer(0), wrong=new Integer(0), skipped= new Integer(0)
            if("-1".equals(userOption)) skipped = new Integer(1)
            else if((""+correctOption).equals(userOption)) {
                correct = new Integer(1)
                lastAnswer = "correct"
            }
            else {
                wrong = new Integer(1)
                lastAnswer = "wrong"
            }
            quizQuestionUserSummary = new QuizQuestionUserSummary(username:username,objId:objId,siteId:siteId,
                    totalCount: new Integer(1), correct:correct,wrong:wrong,skipped:skipped,chapterId:chapterId, bookId: bookId,lastAnswer:lastAnswer)
        }else{
            quizQuestionUserSummary.totalCount = new Integer(quizQuestionUserSummary.totalCount.intValue()+1)
            if("-1".equals(userOption)) {
                quizQuestionUserSummary.skipped = new Integer(quizQuestionUserSummary.skipped.intValue()+1)
                quizQuestionUserSummary.lastAnswer = "skipped"
            }
            else if((""+correctOption).equals(userOption)) {

                quizQuestionUserSummary.correct = new Integer(quizQuestionUserSummary.correct.intValue() + 1)
                quizQuestionUserSummary.lastAnswer = "correct"
            }
            else {
                quizQuestionUserSummary.wrong = new Integer(quizQuestionUserSummary.wrong.intValue()+1)
                quizQuestionUserSummary.lastAnswer = "wrong"
            }
        }
        quizQuestionUserSummary.save(failOnError: true, flush: true)
    }

    def updateQuizRecDtl(quizRecId,chapterId,userAnswers,username,siteId,subject){
        int noOfCorrectAnswers = 0;
        int noOfInCorrectAnswers = 0;
        int noOfSkipped = 0;
        boolean subjectProvided = false
        boolean chapterIdProvided = false
        if(subject!=null) subjectProvided = true
        if(chapterId!=null) chapterIdProvided = true
        for(int i=0;i<userAnswers.size();i++){
            if(!subjectProvided||!chapterIdProvided){
                // in case of test generator or current affairs
                ObjectiveMst objectiveMst = getObjectiveMst(userAnswers[i].id)
                if(objectiveMst!=null){
                    ResourceDtl resourceDtl = getResourceDtl(objectiveMst.quizId)
                    if(resourceDtl!=null){
                        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                        if(chaptersMst!=null){
                            if(!chapterIdProvided) {
                                chapterId=chaptersMst.id
                            }
                            if(!subjectProvided) {
                                subject=null
                                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
                                if (booksTagDtl != null) subject = booksTagDtl.subject
                            }
                        }
                    }
                }
            }

            QuizRecDtl.executeUpdate("update QuizRecDtl set latest=null where latest is not null and objId=" + userAnswers[i].id+" and username='"+username+"'")

            QuizRecDtl quizRecDtl = new QuizRecDtl(quizRecId: quizRecId,objId: userAnswers[i].id,userOption: userAnswers[i].userOption,challengerOption: userAnswers[i].botOption,
                    userTime: new Double(userAnswers[i].userTime), challengerTime: new Double(userAnswers[i].botTime),correctOption: userAnswers[i].correctOption,
                    chapterId: chapterId,username: username,markedForReview:userAnswers[i].reviewedQ,subject:subject,latest: 'true')
            quizRecDtl.save(failOnError: true, flush: true)
            if (quizRecDtl.correctOption.equals(quizRecDtl.userOption)) noOfCorrectAnswers++;
            if (!(quizRecDtl.correctOption .equals(quizRecDtl.userOption)) && !(quizRecDtl.userOption .equals('-1'))) noOfInCorrectAnswers++;
            if (quizRecDtl.userOption.equals('-1')) noOfSkipped++;
            saveQuizStatistics(quizRecDtl)

        }
        QuizRecMst quizRecMst = QuizRecMst.findById(quizRecId)
        if(quizRecMst!=null){
            quizRecMst.correctAnswers = new Integer(noOfCorrectAnswers)
            quizRecMst.incorrectAnswers = new Integer(noOfInCorrectAnswers)
            quizRecMst.skipped = new Integer(noOfSkipped)
            quizRecMst.save(failOnError: true, flush: true)
            if("dailyTests".equals(quizRecMst.quizType))
                redisService.("userQuizHistoryFor_dailyTests_"+username+"_"+quizRecMst.realDailyTestDtlId) = null
            else redisService.("userQuizHistoryFor_regular_"+username+"_"+quizRecMst.resId) = null
        }
        userManagementService.getUserQuizDefaultTimeLog(username)
        progressService.getSubjectwiseInfoTotal(username,"7")
        progressService.getTestAttemptInfo(username,"7")

    }

   def saveQuizStatistics(QuizRecDtl quizRecDtl){
      //save quiz statistics
      QuizStatistics quizStatistics = QuizStatistics.findByObjId(quizRecDtl.objId)
      if(quizStatistics==null){
          println("calling the save quiz thingy 1.1"+quizRecDtl.objId)
          quizStatistics = new QuizStatistics(objId: quizRecDtl.objId,correctTime: new Double(0),incorrectTime: new Double(0),skippedTime: new Double(0),
                  correctAnswers: new Integer(0),incorrectAnswers: new Integer(0),skippedAnswers: new Integer(0),
                  averageTimeTaken: new Double(0),accuracy: new Double(0))
          println("calling the save quiz thingy1.2 "+quizRecDtl.objId)
          quizStatistics.save(failOnError: true, flush: true)
      }
      double averageTime
      if (quizRecDtl.correctOption.equals(quizRecDtl.userOption)) {
          quizStatistics.correctTime = new Double(((quizStatistics.correctAnswers.intValue()*quizStatistics.correctTime.doubleValue())+quizRecDtl.userTime)/(quizStatistics.correctAnswers.intValue()+1))
          quizStatistics.correctAnswers = new Integer(quizStatistics.correctAnswers.intValue()+1)
          if(quizStatistics.fastestTime==null||(quizStatistics.fastestTime.doubleValue()>quizRecDtl.userTime.doubleValue())) quizStatistics.fastestTime=quizRecDtl.userTime
      }
      if (!(quizRecDtl.correctOption .equals(quizRecDtl.userOption)) && !(quizRecDtl.userOption .equals('-1'))) {
          quizStatistics.incorrectTime = new Double(((quizStatistics.incorrectAnswers.intValue()*quizStatistics.incorrectTime.doubleValue())+quizRecDtl.userTime)/(quizStatistics.incorrectAnswers.intValue()+1))
          quizStatistics.incorrectAnswers = new Integer(quizStatistics.incorrectAnswers.intValue()+1)
      }
      if (quizRecDtl.userOption.equals('-1')) {
          quizStatistics.skippedTime = new Double(((quizStatistics.skippedAnswers.intValue()*quizStatistics.skippedTime.doubleValue())+quizRecDtl.userTime)/(quizStatistics.skippedAnswers.intValue()+1))
          quizStatistics.skippedAnswers = new Integer(quizStatistics.skippedAnswers.intValue()+1)
      }

      //calcuate average time
      double totalCorrectTime = quizStatistics.correctAnswers.intValue()*quizStatistics.correctTime.doubleValue()
      double totalIncorrectTime  = quizStatistics.incorrectAnswers.intValue()*quizStatistics.incorrectTime.doubleValue()
       if(quizStatistics.correctAnswers.intValue()>0||quizStatistics.incorrectAnswers.intValue()>0) {
           quizStatistics.averageTimeTaken = new Double((totalCorrectTime + totalIncorrectTime) / (quizStatistics.correctAnswers.intValue() + quizStatistics.incorrectAnswers.intValue()))
           quizStatistics.accuracy = new Double((quizStatistics.correctAnswers.intValue() / (quizStatistics.correctAnswers.intValue() + quizStatistics.incorrectAnswers.intValue())) * 100)
       }
       quizStatistics.save(failOnError: true, flush: true)
   }

    def storeSageTermsDtl(session,params,request){
        String status=""
        String termsCondition=params.termsCondition
        if(termsCondition!=null && !"".equals(termsCondition)){
            User user = User.findByUsername(springSecurityService.currentUser.username)
            user.termsCondition='true'
            user.tcAcceptedDate=new Date()
            String ipAddress = utilService.getIPAddressOfClient(request)
            user.ipAddress=ipAddress
            user.save(failOnError: true, flush: true)
            session['userdetails'] = user
            status="OK"
        }
         def  json=[status:status]
        return json
    }

    def addUserLoginLog(Integer siteId,String source,String username){
        UserLoginLog userLoginLog = UserLoginLog.findBySiteIdAndUsernameAndDateCreatedGreaterThanEquals(siteId,username, new Date().clearTime())

        if(userLoginLog==null){
             userLoginLog = new UserLoginLog(username:username,source: source,siteId:siteId)
            userLoginLog.save(failOnError: true, flush: true)
        }

        return

    }


    def sendMcqUpdateNotification(chapterId, bookId, resId){
        dataNotificationService.quizUpdated(chapterId, bookId, resId)
    }

    def getObjectiveMst(objId){
        ObjectiveMst objectiveMst = redisService.memoizeDomainObject(ObjectiveMst, "objectiveMst_"+objId) {
            return ObjectiveMst.findById(new Integer(objId))
        }

        return objectiveMst
    }

    def getResourceDtl(quizId){
        ResourceDtl resourceDtl = redisService.memoizeDomainObject(ResourceDtl, "resourceQuizMst_"+quizId) {
            return ResourceDtl.findByResLink(""+quizId)
        }
        if(resourceDtl==null){
            resourceDtl = ResourceDtl.findByResLink(""+quizId)
        }
        return resourceDtl
    }

    def detectDevice(request) {
        String userAgent = request.getHeader('User-Agent')

        if (isMobile(userAgent)) {
           return "Mobile"
        } else if (isTablet(userAgent)) {
            return "Tablet"
        } else {
           return "Laptop"
        }
    }

    private boolean isMobile(String userAgent) {
        return userAgent?.contains('Mobi') || userAgent?.contains('Android') || userAgent?.contains('iPhone')
    }

    private boolean isTablet(String userAgent) {
        return userAgent?.contains('iPad') || userAgent?.contains('Tablet') || (userAgent?.contains('Android') && !userAgent?.contains('Mobile'))
    }

    def updateSalesAffiliation(String scd,bookId,username){
       SalesAffiliationLog salesAffiliationLog =  new SalesAffiliationLog(scd:scd,bookId:bookId!=null?new Integer(bookId):null,username:username)
            salesAffiliationLog.save(failOnError: true, flush: true)
    }

}
