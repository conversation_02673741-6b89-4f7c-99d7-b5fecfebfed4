package com.wonderslate.institute

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.TestsDtl
import com.wonderslate.data.TestsMst
import com.wonderslate.data.TestsShared
import com.wonderslate.data.UtilService
import com.wonderslate.prepjoy.QuizRecDtl
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.sqlutil.SafeSql
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.prepjoy.QuizRecMst

import java.text.SimpleDateFormat


class OnlineTestService {

    def springSecurityService
    def grailsApplication
    def redisService
    UtilService utilService
    DataProviderService dataProviderService
    /**
     * 1. List tests for a given instituteId with filtering and sorting logic.
     *    Returns a map with [tests: List<TestSummary>, totalCount: int].
     */
    @Secured(['ROLE_USER']) @Transactional
    Map listTests(Integer max, Integer offset, String filter, siteId) {

        String username = springSecurityService.currentUser.username
        if(redisService.("institutesList_"+username)==null){
            getInstitutesForUser(siteId)
        }
        List institutesList = new JsonSlurper().parseText(redisService.("institutesList_"+username))
        //create List of all batchIds from institutesList and no need for matching instituteId and each batchId as Long
        List batchIds = institutesList*.batchId.collect { it as Long }




        // 2. Query TestsShared table for tests whose batchId is in batchIds
        def criteria = TestsShared.createCriteria()
        def results = criteria.list(max: max, offset: offset) {
            'in'('batchId', batchIds)
             'ge'('id', new Long(0)) // do no show negative ids .. negative ids are deleted tests

            // Filter: upcoming, ongoing, awaitingResults, completed
            // We'll interpret them by the test’s startDateTime, endDateTime, resultDateTime
            if (filter) {
                or {
                    if (filter == 'upcoming') {
                        // startDateTime > now
                        gt('startDateTime', new Date())
                    }
                    if (filter == 'ongoing') {
                        // now between startDateTime and endDateTime
                        and {
                            le('startDateTime', new Date())
                            ge('endDateTime', new Date())
                        }
                    }
                    if (filter == 'awaitingResults') {
                        // test ended, but result date not reached
                        and {
                            lt('endDateTime', new Date())
                            isNotNull('resultDateTime')
                            gt('resultDateTime', new Date())
                        }
                    }
                    if (filter == 'completed') {
                        // test ended, and either no resultDateTime or we've reached resultDateTime
                        or {
                            and {
                                lt('endDateTime', new Date())
                                isNull('resultDateTime')
                            }
                            and {
                                lt('endDateTime', new Date())
                                le('resultDateTime', new Date())
                            }
                        }
                    }
                }
            }

            order('dateCreated', 'desc') // Default sorting by creation date (desc)
        }

        // Prepare the data for return
        def testList = results.collect { TestsShared test ->
            //get the batch name from institutesList
            def batch = institutesList.find { it.batchId == test.batchId }

            def startDateTime = test.startDateTime?utilService.convertDate(test.startDateTime,"UTC","IST"):null
            def endDateTime = test.endDateTime?utilService.convertDate(test.endDateTime,"UTC","IST"):null
            def resultDateTime = test.resultDateTime?utilService.convertDate(test.resultDateTime,"UTC","IST"):null
            //now if the user is instructor and the test is not yet started then he can delete the test. So let us set that flag
            def canDelete = test.startDateTime && test.startDateTime > new Date() && test.createdBy == username
            def canSeeQP = test.createdBy == username
            //get the user type from institutesList
            def userType = institutesList.find { it.batchId == test.batchId }?.userType


            //if the userType is Instructor or manager set the flag showResults to true if date is greater than endDateTime else if the userType is student set the flag showResults to true if date is greater than resultDateTime
            def showResults = (userType == "Instructor" || userType == "Manager") ? new Date() > test.endDateTime : ((test.resultDateTime!=null&&!"".equals(test.resultDateTime))?new Date() > test.resultDateTime:false)
             //handle the case where the student is the userType and resultDateTime is null
            if((userType == "Student"||userType==null) && (test.resultDateTime==null||"".equals(test.resultDateTime))){
                showResults = new Date() > test.endDateTime
            }
            // now if the user is student and test is started but not ended then he can take the test. Let us set that flag
            def canTakeTest = (userType == "Student"||userType==null) && new Date() > test.startDateTime && new Date() < test.endDateTime
            def testAlreadyTaken = false
            def quizRecId = null
            def quizRec = QuizRecMst.findByTestGenIdAndUsername(test.testId, username)
            if(quizRec){
                quizRecId = quizRec.id
            }
            if(canTakeTest){
                //check the user has already taken the test
                if(quizRec){
                    canTakeTest = false
                    testAlreadyTaken = true
                }
            }
            def ongoingTest = false
            if(new Date() > test.startDateTime && new Date() < test.endDateTime){
                ongoingTest = true
            }
            [
                    testId         : test.testId,
                    batchId        : test.batchId,
                    name           : test.name,
                    startDateTime  : startDateTime,
                    endDateTime    : endDateTime,
                    resultDateTime : resultDateTime,
                    duration       : test.duration,
                    createdBy      : test.createdBy,
                    batchName      : batch?.name ?: "Unknown",
                    canDelete      : canDelete,
                    showResults    : showResults,
                    canTakeTest    : canTakeTest,
                    testAlreadyTaken : testAlreadyTaken,
                    userType: userType,
                    quizRecId: quizRecId,
                    ongoingTest: ongoingTest,
                    canSeeQP: canSeeQP
            ]
        }

        return [tests: testList, totalCount: results.totalCount]
    }

    /**
     * 2. Delete a test (only if it's in an upcoming state).
     *    We'll check if startDateTime > now to confirm it's upcoming.
     */
    boolean deleteTest(Long testId) {
        TestsShared test = TestsShared.findByTestId(testId)
        if (!test) return false

        // Check if upcoming
        if (test.startDateTime && test.startDateTime > new Date()) {
            test.dateDeleted = new Date()
            test.deletedBy = springSecurityService.currentUser.username
            test.batchId = -test.batchId
            test.save(flush: true, failOnError: true)
            return true
        }
        return false
    }

    /**
     * 3. Blocking/Unblocking users
     */
    boolean bulkBlockUsers(Long testId, List<String> usernames, String blockedBy, String reason) {
        Date now = new Date()
        for (String uname : usernames) {
            // Check if already blocked
            def existing = BlockedStudent.findByTestIdAndUsername(testId, uname)
            if (!existing) {
                new BlockedStudent(
                        testId   : testId,
                        username  : uname,
                        blockedBy : blockedBy,
                        reason    : reason,
                        blockedAt : now
                ).save(flush: true)
            }
        }
        return true
    }

    boolean bulkUnblockUsers(Long testId, List<String> usernames) {
        for (String uname : usernames) {
            def record = BlockedStudent.findByTestIdAndUsername(testId, uname)
            if (record) {
                record.delete(flush: true)
            }
        }
        return true
    }

    /**
     * 4. Results Page for instructor/manager
     *    Returns a list of [ username, name, correctAnswers, wrongAnswers, skipped, score, timeTaken, status ]
     */
    List getResultsForTest(Long testId, Long batchId) {
        // 1. Find all students in the batch where userType not in ('Instructor', 'Manager')

        def batchUsers = BatchUserDtl.findAllByBatchId(batchId).findAll { it.userType == null || it.userType == 'Student' }.collect { it.username }
        // 2. For each username, check if blocked in BlockedStudent
        // 3. For each username, check if there's a QuizRecMst with testGenId = testId
        //    that indicates a taken test.
        //    We'll assume there's only 1 record per user for now.

        List results = []
        batchUsers.each { String uname ->
            def blocked = (BlockedStudent.findByTestIdAndUsername(testId, uname) != null)
            def quizRec = QuizRecMst.findByTestGenIdAndUsername(testId, uname)
            if (blocked) {
                results << buildResultRow(uname, "Blocked", null)
            } else if (!quizRec) {
                // Not taken
                results << buildResultRow(uname, "Not taken", null)
            } else {
                // Test taken
                results << buildResultRow(uname, "Test taken", quizRec)
            }
        }
        // Sort the results by status (Test taken, Not taken, Blocked), then name
        // We'll manually define an order for the statuses
        def statusOrder = ["Test taken": 1, "Not taken": 2, "Blocked": 3]
        results.sort { a, b ->
            statusOrder[a.status] <=> statusOrder[b.status] ?: a.name <=> b.name
        }

        return results
    }

    private Map buildResultRow(String username, String status, QuizRecMst quizRec) {
        // Possibly fetch user’s name from the User table
        def userObj = dataProviderService.getUserMst(username)
        String fullName = userObj?.name ?: "No Name"

        Map row = [
                username : username,
                name     : fullName,
                status   : status,
                score    : null,
                correctAnswers : null,
                incorrectAnswers : null,
                skipped  : null,
                timeTaken: null,
                quizRecId: null
        ]

        if (quizRec && status == "Test taken") {
            row.score            = quizRec.userScore
            row.correctAnswers   = quizRec.correctAnswers ?: 0
            row.incorrectAnswers = quizRec.incorrectAnswers ?: 0
            row.skipped          = quizRec.skipped ?: 0
            // userTime is stored in seconds, also we want to show it in some readable format or raw?
            row.timeTaken        = quizRec.userTime ?: 0.0
            row.quizRecId        = quizRec.id
        }

        return row
    }

    /**
     * 5. Excel Export
     *    This method returns a List of Maps or some structure that
     *    can be used in a controller to generate an Excel file.
     */
    List<Map> getResultsForExcel(Long testId, Long batchId) {
        List table = getResultsForTest(testId, batchId)
        // table is the same structure as getResultsForTest
        // Possibly you want additional columns or transformations for the Excel
        return table
    }

    def getInstitutesForUser(siteId){
        String username=springSecurityService.currentUser.username

        List instituteDetails
        BatchUserDtl bud = BatchUserDtl.findByUsernameAndUserType(username,'Manager')
        String sql
        if(bud==null) {
            sql =
                    " SELECT bud.batch_id, im.id institute_id, im.name,bud.user_type,cbd.name batchName " +
                            " FROM wsuser.batch_user_dtl bud, wsuser.course_batches_dtl cbd, wsuser.institute_mst im " +
                            " where im.site_id=" + siteId + " and bud.username='" + username + "' " +
                            " and cbd.id=bud.batch_id and im.id=cbd.conducted_by " +
                            " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) "
        }else{
            //get all batches for the manager
            CourseBatchesDtl cbd = CourseBatchesDtl.findById(bud.batchId)
            sql =
                    " SELECT cbd.id batch_id, im.id institute_id, im.name,'Manager' user_type,cbd.name batchName " +
                            " FROM  wsuser.course_batches_dtl cbd, wsuser.institute_mst im " +
                            " where im.site_id=" + siteId + " and cbd.conducted_by=" + cbd.conductedBy +
                            "  and im.id=cbd.conducted_by " +
                            " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) "
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String instituteIds=""
        instituteDetails = results.collect{
            instituteIds = instituteIds+it.institute_id+","
            return [batchId: it.batch_id, id: it.institute_id, name:"Default".equals(it.batchName)? it.name:it.batchName,userType:it.user_type]
        }

        Gson gson = new Gson();
        String element = gson.toJson(instituteDetails,new TypeToken<List>() {}.getType())
        redisService.("institutesList_"+username) = element

        return instituteDetails;

    }

    /**
     * Return aggregated question-level analytics for a given testId (TestsShared.testId).
     * Sort by the specified column (e.g., 'correctCount', 'incorrectCount', 'skippedCount', 'avgTime', 'questionText').
     */
    List<Map> getQuestionAnalyticsForTest(Integer testId, String sortColumn, String sortDir) {
        // 1. Gather all QuizRecMst IDs for the given testId
        //    quizRecMst.testGenId = testId
        def quizRecIds = QuizRecMst.createCriteria().list {
            eq('testGenId', testId)
            projections { property('id')  }
        } as List<Integer>

        if (!quizRecIds) {
            // No submissions found, either test is not taken or there's no data
            return []
        }

        //convert Long to Integer of quizRecIds
        quizRecIds = quizRecIds.collect { it as Integer }

        // 2. Join QuizRecDtl -> Group by objId -> compute correct, wrong, skip, avgTime
        // We'll do this in memory for simplicity. For large data, consider a more optimized SQL or HQL approach.

        def details = QuizRecDtl.createCriteria().list {
            'in'('quizRecId', quizRecIds)
            // We'll fetch all needed fields for aggregation
            // We won't do sum or avg here in the criteria; we do it in memory for clarity
        }

        // Group by objId
        Map<Long, List<QuizRecDtl>> grouped = details.groupBy { it.objId }

        // 3. Build an analytics row for each objId
        List<Map> results = []
        grouped.each { Long objId, List<QuizRecDtl> recDtls ->

            // Aggregation fields
            int correctCount = 0
            int incorrectCount = 0
            int skippedCount = 0
            double totalTime = 0.0
            int totalAttempts = recDtls.size()

            recDtls.each { QuizRecDtl dtl ->
                if (dtl.userOption == '-1') {
                    skippedCount++
                } else {
                    // Compare dtl.userOption with dtl.correctOption for correctness
                    // Single-select scenario
                    if (dtl.userOption == dtl.correctOption) {
                        correctCount++
                    } else {
                        incorrectCount++
                    }
                }
                if (dtl.userTime != null) {
                    totalTime += dtl.userTime
                }
            }

            double avgTime = (totalAttempts > 0) ? (totalTime / totalAttempts) : 0.0

            // 4. Retrieve question text from ObjectiveMst (or a short summary)
            ObjectiveMst questionObj = ObjectiveMst.findById(objId)
            String questionText = questionObj?.question ?: "Question #${objId}"

            // We'll store in a Map
            results << [
                    objId         : objId,
                    questionText  : questionText,
                    correctCount  : correctCount,
                    incorrectCount: incorrectCount,
                    skippedCount  : skippedCount,
                    avgTime       : avgTime
            ]
        }

        // 5. Sort by requested column
        // Available columns: 'correctCount', 'incorrectCount', 'skippedCount', 'avgTime', 'questionText'
        // Default to questionText if unknown
        String col = sortColumn in ['correctCount','incorrectCount','skippedCount','avgTime','questionText']
                ? sortColumn : 'questionText'
        // sortDir is either 'asc' or 'desc'
        boolean asc = (sortDir?.toLowerCase() == 'asc')

        results.sort { a, b ->
            def valA = a[col]
            def valB = b[col]
            if (asc) {
                // ascending
                return valA <=> valB
            } else {
                // descending
                return valB <=> valA
            }
        }

        return results
    }

    /**
     * Aggregates data needed for the analytics page:
     * - Score distribution for the test
     * - Question-level stats (correct, incorrect, skipped, average time)
     * Returns a map:
     * [
     *   scoreDistribution: [ [range:'0-10', count:5], [range:'11-20', count:10], ... ],
     *   questionStats    : [ [questionText:'Q1...', correct:XX, incorrect:XX, skipped:XX, avgTime:XX], ... ]
     * ]
     */
    Map getAnalyticsDataForTest(Integer testId,analytics) {


        // 2) Question-level stats
        List<Map> qStats = computeQuestionStats(testId,analytics)
        int totalQuestions = qStats.size()
        // 1) Score distribution data
        List<Map> scoreDist = computeScoreDistribution(testId, totalQuestions)
        return [
                scoreDistribution: scoreDist,
                questionStats    : qStats
        ]
    }

    // -----------------------
    //    HELPER METHODS
    // -----------------------

    /**
     * Compute score distribution (e.g. bucket scores by 10-point intervals or any logic you prefer).
     * For demonstration, we'll do a simple "bucket of 10 points" approach.
     */
    private List<Map> computeScoreDistribution(Integer testId, int totalQuestions) {
        if (totalQuestions <= 0) {
            return []
        }
        def quizRecs = QuizRecMst.findAllByTestGenId(testId)
        if (!quizRecs) {
            // No attempts, but we want 4 buckets of zero
            return makeEmptyBuckets(totalQuestions)
        }

        List<Double> scores = quizRecs.collect { it.userScore ?: 0.0 }
        int bucketSize = (int) Math.ceil(totalQuestions / 4.0)
        List<Map> buckets = []
        int start = 0
        for(int i=1; i<=4; i++) {
            int end = (i<4) ? (start+bucketSize-1) : totalQuestions
            if (end > totalQuestions) end = totalQuestions
            buckets << [range:"${start}-${end}", startVal:start, endVal:end, count:0]
            start = end + 1
        }
        // Assign scores to buckets
        scores.each { sc ->
            int s = (int) Math.round(sc)
            def b = buckets.find { s >= it.startVal && s <= it.endVal }
            if (b) {
                b.count++
            }
        }

        // Return final array
        return buckets.collect { b ->
            [ range: b.range, count: b.count ]
        }
    }

    private List<Map> makeEmptyBuckets(int totalQuestions) {
        if (totalQuestions < 1) return []
        int bucketSize = (int)Math.ceil(totalQuestions / 4.0)
        List<Map> bks = []
        int st = 0
        for(int i=1; i<=4; i++) {
            int ed = (i<4) ? (st+bucketSize-1) : totalQuestions
            if (ed>totalQuestions) ed = totalQuestions
            bks << [range:"${st}-${ed}", count:0]
            st = ed+1
        }
        return bks
    }

    /**
     * Compute question-level stats from QuizRecDtl
     * - correct, incorrect, skipped counts
     * - average time (in seconds)
     * We'll join them in memory.
     */
    private List<Map> computeQuestionStats(Integer testId,List analytics) {
        // 1. Find all QuizRecMst for the test
        def quizRecIds = QuizRecMst.createCriteria().list {
            eq('testGenId', testId)
            projections {
                property('id') // the domain 'id'
            }
        } as List<Integer>
        if (!quizRecIds) {
            return []
        }
        //convert Long to Integer of quizRecIds
        quizRecIds = quizRecIds.collect { it as Integer }
        // 2. Get QuizRecDtl for these quizRecIds
        List<QuizRecDtl> dtlList = QuizRecDtl.createCriteria().list {
            'in'('quizRecId', quizRecIds)
        }
        if (!dtlList) {
            return []
        }

        // 3. Group by objId
        Map<Integer, List<QuizRecDtl>> grouped = dtlList.groupBy { it.objId }

        // 4. For each objId, compute stats
        List<Map> results = []
        grouped.each { Integer objId, List<QuizRecDtl> recs ->
            int correct = 0
            int incorrect = 0
            int skipped = 0
            double totalTime = 0
            int count = recs.size()

            recs.each { QuizRecDtl dtl ->
                if (dtl.userOption == '-1') {
                    skipped++
                } else {
                    // Compare dtl.userOption vs dtl.correctOption
                    if (dtl.userOption == dtl.correctOption) {
                        correct++
                    } else {
                        incorrect++
                    }
                }
                if (dtl.userTime != null) {
                    totalTime += dtl.userTime
                }
            }

            double avgTime = (count > 0) ? (totalTime / count) : 0.0

            // 5. Get question text from ObjectiveMst
            def questionObj = ObjectiveMst.get(objId)
            String questionText = questionObj?.question ?: "Question #${objId}"

            results << [
                    questionText : questionText.substring(0,5),
                    correct      : correct,
                    incorrect    : incorrect,
                    skipped      : skipped,
                    avgTime      : avgTime,
                    objId: objId

            ]
        }

        //analytics is the sorted list of objects. We need to sort the results based on the objId in the order that is present in the analytics
        int questionNo = 0

        List<Map> sortedResults = []
        analytics.each { a ->
            results.each { r ->
                if(a.objId == r.objId){
                    questionNo = questionNo + 1
                    r.questionNo = questionNo
                    sortedResults << r
                }
            }
        }

        return sortedResults
    }

    /**
     * Retrieve all questions for a specific test
     * @param testId The ID of the test from TestsShared
     * @return List of maps containing question details
     */
    @Secured(['ROLE_USER']) @Transactional
    List<Map> getQuestionsForTest(Long testId) {
        // This is a fallback implementation that would need to be customized based on how
        // questions are associated with tests in your system
        // For example, you might have a direct mapping table or some other mechanism

        // For now, we'll return an empty list as a placeholder

        TestsMst testsMst = TestsMst.get(testId)
        // get the questions from TestsDtl using testId
        List testsDtl = TestsDtl.findAllByTestId(testId)
        List<Map> questions = []
        testsDtl.each { testDtl ->
            ObjectiveMst question = ObjectiveMst.get(testDtl.objId)
            if (question) {
                questions << [
                        id: question.id,
                        question: question.question,
                        option1: question.option1,
                        option2: question.option2,
                        option3: question.option3,
                        option4: question.option4,
                        option5: question.option5,
                        answer: question.answer,
                        subject: question.subject,
                        chapter: question.chapter,
                        difficultylevel: question.difficultylevel,
                        answerDescription: question.answerDescription
                ]
            }
        }
        return questions
    }


}

