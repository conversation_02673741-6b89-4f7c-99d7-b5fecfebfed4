package com.wonderslate.institute

import com.wonderslate.data.SiteMst
import com.wonderslate.groups.GroupsMembersDtl
import com.wonderslate.librarybooks.LibraryBooksService
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.UserRole
import com.wonderslate.usermanagement.WinGenerator
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import com.wonderslate.usermanagement.User
import com.wonderslate.data.BooksMst

import java.text.DateFormat
import java.text.SimpleDateFormat
import groovy.sql.Sql

@Transactional
class InstManagerService {
    def springSecurityService
    def grailsApplication
    LibraryBooksService libraryBooksService
    /**
     * Course Management Methods
     */

    // Create a new course
    InstituteCourseMst createCourse(String name, String gradeType, Integer gradeStart, Integer gradeEnd, String customGrades) {
        InstituteCourseMst course = new InstituteCourseMst(
                name: name,
                gradeType: gradeType,
                gradeStart: gradeStart,
                gradeEnd: gradeEnd,
                customGrades: customGrades
        )
        course.save(flush: true)
        return course
    }

    // Update an existing course
    InstituteCourseMst updateCourse(Long id, Map params) {
        InstituteCourseMst course = InstituteCourseMst.get(id)
        if (course) {
            course.properties = params
            course.save(flush: true)
        }
        return course
    }

    // Delete a course
    boolean deleteCourse(Long id) {
        InstituteCourseMst course = InstituteCourseMst.get(id)
        if (course) {
            course.delete(flush: true)
            return true
        }
        return false
    }

    // Get a course by ID
    InstituteCourseMst getCourse(Long id) {
        return InstituteCourseMst.get(id)
    }

    // List all courses
    List<InstituteCourseMst> listCourses() {
        return InstituteCourseMst.list([sort: 'name', order: 'asc'])
    }

    /**
     * Batch (Section) Management Methods
     */

    // Create a new batch
    CourseBatchesDtl createBatch(Long courseId, Long conductedBy, String name, String grade, Date startDate, Date endDate, String status, Integer groupId, String syllabus) {
        CourseBatchesDtl batch = new CourseBatchesDtl(
                courseId: courseId,
                conductedBy: conductedBy,
                conductedFor: conductedBy,
                name: name,
                grade: grade,
                startDate: startDate,
                endDate: endDate,
                status: "active",
                groupId: groupId,
                syllabus: syllabus
        )
        batch.save(failOnError: true, flush: true)

        return batch
    }

    // Update an existing batch
    CourseBatchesDtl updateBatch(Long id, Map params) {
        CourseBatchesDtl batch = CourseBatchesDtl.get(id)
        if (batch) {
            batch.properties = params
            batch.save(flush: true)
        }
        return batch
    }

    // Delete a batch
    boolean deleteBatch(Long id) {
        CourseBatchesDtl batch = CourseBatchesDtl.get(id)
        if (batch) {
            batch.delete(flush: true)
            return true
        }
        return false
    }

    // Get a batch by ID
    CourseBatchesDtl getBatch(Long id) {
        return CourseBatchesDtl.get(id)
    }

    // List all batches for an institute
    List<CourseBatchesDtl> listBatches(Long instituteId) {
        return CourseBatchesDtl.findAllByConductedBy(instituteId, [sort: 'name', order: 'asc'])
    }

    /**
     * User Assignment Methods
     */

    // Assign a user to a batch
    BatchUserDtl assignUserToBatch(Long batchId, String username, String userType, String classTeacher, String admissionNo, Date validityDate) {
        BatchUserDtl batchUser = BatchUserDtl.findByBatchIdAndUsername(batchId, username)
        if(batchUser==null) {
            batchUser = new BatchUserDtl(
                    batchId: batchId,
                    username: username,
                    dateCreated: new Date(),
                    userType: userType,
                    classTeacher: classTeacher,
                    admissionNo: admissionNo,
                    validityDate: validityDate,
                    createdBy: springSecurityService.currentUser.username
            )
            batchUser.save(failOnError: true, flush: true)

        }else{
            println("user already present in batch")
        }
        return batchUser
    }

    // Remove a user from a batch
    boolean removeUserFromBatch(Long id) {
        BatchUserDtl batchUser = BatchUserDtl.get(id)
        if (batchUser) {
            String username = batchUser.username
            batchUser.delete(flush: true)
            // check if the batch is the default batch. If so, remove all other batches of that institute.
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchUser.batchId)
            if (courseBatchesDtl != null) {
                if (courseBatchesDtl.name == "Default") {
                    List<CourseBatchesDtl> courseBatchesDtls = CourseBatchesDtl.findAllByConductedBy(courseBatchesDtl.conductedBy)
                    for (CourseBatchesDtl courseBatchesDtl1 : courseBatchesDtls) {
                        batchUser = BatchUserDtl.findByUsernameAndBatchId(username, courseBatchesDtl1.id)
                        if (batchUser != null) {
                            batchUser.delete(flush: true)
                        }
                    }
                }
            }

            return true
        }
        return false
    }

    // List users in a batch
    List<BatchUserDtl> listUsersInBatch(Long batchId) {
        return BatchUserDtl.findAllByBatchId(batchId)
    }

    /**
     * Book Assignment Methods
     */

    // Assign a book to a batch
    BooksBatchDtl assignBookToBatch(Long batchId, Long bookId, String instructorControlled, Integer numberOfLicenses, Integer validity, Date bookExpiryDate, String addedBy, Long materialId) {
        BooksBatchDtl booksBatch = new BooksBatchDtl(
                batchId: batchId,
                bookId: bookId,
                instructorControlled: instructorControlled,
                numberOfLicenses: numberOfLicenses,
                validity: validity,
                bookExpiryDate: bookExpiryDate,
                addedBy: addedBy,
                materialId: materialId,
                dateCreated: new Date()
        )
        booksBatch.save(flush: true)
        return booksBatch
    }



    // List books assigned to a batch
    List<BooksBatchDtl> listBooksInBatch(Long batchId) {
        return BooksBatchDtl.findAllByBatchId(batchId)
    }

    Map getDashboardData(Long instituteId) {
        Map dashboardData = [:]

        // Total number of courses offered by the institute
        List<CourseBatchesDtl> allBatches = CourseBatchesDtl.findAllByConductedBy(instituteId)
        //get Default Batch ID
        Long defaultBatchId = getDefaultBatchId(instituteId)
        //remove the default course
       List  batches = allBatches.findAll { it.name != 'Default' }

        Set<Long> courseIds = allBatches*.courseId.findAll { it != null } as Set
        List<InstituteCourseMst> courses = InstituteCourseMst.findAllByIdInList(courseIds.toList())
        dashboardData.totalCourses = courses.size()

        // Total number of batches (sections)
        dashboardData.totalBatches = batches.size()

        // Total number of students and instructors
        List<Long> batchIds = allBatches*.id
        List<BatchUserDtl> batchUsers = BatchUserDtl.findAllByBatchId(defaultBatchId)
        dashboardData.totalStudents = batchUsers.size()


        // Total number of books assigned
        List<BooksBatchDtl> booksBatchList = BooksBatchDtl.findAllByBatchIdInList(batchIds)
        Set<Long> uniqueBookIds = booksBatchList*.bookId.toSet()
        dashboardData.totalBooksAssigned = uniqueBookIds.size()

        // Recent activities
        dashboardData.recentBatches = batches.sort { -it.id }.take(5)
        dashboardData.recentUsers = batchUsers.sort { -it.id }.take(5)
        dashboardData.recentBooksAssigned = booksBatchList.sort { -it.id }.take(5)

        return dashboardData
    }

    /**
     * Get Default Batch ID for an Institute
     */
    Long getDefaultBatchId(Long instituteId) {
        CourseBatchesDtl defaultBatch = CourseBatchesDtl.findByConductedByAndName(instituteId, 'Default')
        return defaultBatch?.id
    }

    /**
     * Get Users List with Pagination, Search, and Filtering for a Batch
     */
    def getUsersList(Long batchId, Integer max, Integer offset, String search, String userType, Integer siteId = null) {
        // Fetch BatchUserDtl entries for the batch
        def criteria = BatchUserDtl.createCriteria()
        def batchUsersList = criteria.list(max: max, offset: offset) {
            eq('batchId', batchId)
            if (userType) {
                eq('userType', userType)
            }

            // If search term is provided, we'll handle it differently
            // We'll collect all matching users first, then filter by batch
            if (!search) {
                order('dateCreated', 'desc')
            }
        }

        // If search is provided and siteId is available, we need to handle search differently
        List<Map> users = []
        int totalCount = 0

        if (search && siteId) {
            // Get all usernames in the batch
            def usernamesInBatch = batchUsersList.collect { it.username }

            // Search for users by name, email, or mobile that match the search term and are in the batch
            def userCriteria = User.createCriteria()
            def matchingUsers = userCriteria.list(max: max, offset: offset) {
                eq('siteId', siteId)
                inList('username', usernamesInBatch)
                or {
                    ilike('name', "%${search}%")
                    ilike('email', "%${search}%")
                    ilike('mobile', "%${search}%")
                    ilike('username', "%${search}%")
                }
                order('name', 'asc')
            }

            totalCount = matchingUsers.totalCount

            // Map users to the required format
            matchingUsers.each { user ->
                BatchUserDtl batchUser = BatchUserDtl.findByUsernameAndBatchId(user.username, batchId)
                if (batchUser) {
                    String userTypeLabel = batchUser.userType == null ? 'Student' : batchUser.userType
                    users << [
                            id          : user.id,
                            username    : user.username,
                            name        : user.name,
                            email       : user.email,
                            mobileNumber: user.mobile,
                            userType    : userTypeLabel
                    ]
                }
            }
        } else {
            // Use the original approach for non-search or when siteId is not available
            totalCount = batchUsersList.totalCount

            batchUsersList.each { batchUser ->
                // If siteId is provided, use it to filter users
                User user = siteId ?
                    User.findByUsernameAndSiteId(batchUser.username, siteId) :
                    User.findByUsername(batchUser.username)

                if (user) {
                    String userTypeLabel = batchUser.userType == null ? 'Student' : batchUser.userType
                    users << [
                            id          : user.id,
                            username    : user.username,
                            name        : user.name,
                            email       : user.email,
                            mobileNumber: user.mobile,
                            userType    : userTypeLabel
                    ]
                }
            }
        }

        return [users: users, totalCount: totalCount]
    }

    /**
     * Get User Suggestions for Autocomplete within a Batch
     * Optimized version using direct SQL queries
     */
    def getUserSuggestions(Long batchId, String term, Integer siteId, Boolean onlyBatchUsers) {
        // Get a reference to the wsuser datasource
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql = new SafeSql(dataSource)

        // Determine if the batchId corresponds to the default batch
        String batchCheckSql = "SELECT name FROM wsuser.course_batches_dtl WHERE id = "+batchId
        def batchResult = sql.rows(batchCheckSql)[0]
        boolean isDefaultBatch = batchResult && "Default".equals(batchResult.name)

        List<Map> users = []

        try {
            if (isDefaultBatch && !onlyBatchUsers) {
                // For default batch and not only batch users, search all users by site ID
                // Use CONVERT to handle character encoding issues
                String userSql = "SELECT id, username, name, " +
                        "email,mobile " +
                        "FROM wsuser.user " +
                        " WHERE site_id = " + siteId  +
                        " AND name LIKE '%"+term+"%' OR " +
                        " email LIKE '%" +term+"%' OR " +
                         " mobile LIKE '%"+term+"%'  " +
                        " ORDER BY name ASC " +
                        " LIMIT 10"


                users = sql.rows(userSql)
            } else {
                // For non-default batch or only batch users, search users in the batch
                // Use CONVERT to handle character encoding issues
                String userSql = 'SELECT u.id, u.username, name, ' +
                    'email,mobile, ' +
                    'bud.user_type ' +
                    'FROM wsuser.user u ' +
                    'JOIN wsuser.batch_user_dtl bud ON u.username = bud.username ' +
                    'WHERE bud.batch_id =  ' +batchId
                    'AND u.site_id =  ' +siteId
                        " AND u.name LIKE '%"+term+"%' OR " +
                        " u.email LIKE '%" +term+"%' OR " +
                        " u.mobile LIKE '%"+term+"%'  " +
                    ' ORDER BY u.name ASC ' +
                    'LIMIT 10'


                users = sql.rows(userSql)
            }

            // Prepare suggestions from users
            def suggestions = users.collect { user ->
                if (onlyBatchUsers) {
                    // For onlyBatchUsers=true, return detailed user info
                    String userType = "Student"

                    // If we don't have user_type from the join query, fetch it
                    if (!user.containsKey('user_type') || user.user_type == null) {
                        def batchUserResult = sql.firstRow(
                            "SELECT user_type FROM wsuser.batch_user_dtl WHERE username = ? AND batch_id = ?",
                            [user.username, batchId]
                        )
                        userType = batchUserResult?.user_type ?: "Student"
                    } else {
                        userType = user.user_type ?: "Student"
                    }

                    [
                        id          : user.id,
                        username    : user.username.split("_")[1],
                        name        : user.name,
                        email       : user.email,
                        mobileNumber: user.mobile,
                        userType    : userType
                    ]
                } else {
                    // For onlyBatchUsers=false, return label/value format
                    def returnValue = user.name
                    if (user.email) {
                        returnValue += " (Email:${user.email})"
                    }
                    if (user.mobile) {
                        returnValue += " (Mobile:${user.mobile})"
                    }
                    [
                        label: returnValue,
                        value: user.username
                    ]
                }
            }

            return suggestions
        } catch (Exception e) {
            println("Error fetching user suggestions: ${e.message}")
            return []
        }
    }


    /**
     * Set User as Instructor within a Batch
     */
    def setUserAsInstructor(String username, Long batchId) {
        BatchUserDtl batchUser = BatchUserDtl.findByUsernameAndBatchId(username, batchId)
        if (batchUser) {
            batchUser.instructor = 'true'
            batchUser.save(flush: true)
            return !batchUser.hasErrors()
        } else {
            // User is not in the batch
            return false
        }
    }



    def createUser(params,session){
        Integer siteId = session["siteId"]

        //first check the user exists
        String username = "" + siteId + "_" + params.mobile
        if(params.mobile){
            username = "" + siteId + "_" + params.mobile
        }else if(params.email){
            username = "" + siteId + "_" + params.email
        }
        User user = User.findByUsername(username)
        if(user==null)
        if (user == null) {
            //create the user
            String name = params.name
            String password = params.password
            String mobile = params.mobile
            String email = params.email
            String pincode = params.pincode
            String city = params.city
            WinGenerator winGenerator = new WinGenerator()
            winGenerator.save(failOnError: true)
            user = new User(username: username, name: name, password: password, mobile: mobile, email: email, win: winGenerator.id, siteId: siteId, pincode: pincode, city: city)
            user.save(failOnError: true, flush: true)
            //add appropriate roles
            Role role = Role.findByAuthority("ROLE_USER")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_ADD")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_UPLOAD")
            UserRole.create(user, role, true)
        }
         return user
    }

    def addUserToBatch(String username,String userType,String admissionNumber,Integer batchId){
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchId)
             if (courseBatchesDtl != null) {
                BatchUserDtl batchUserDtl
                //check if the user is already added
                batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(username,batchId)
                if (batchUserDtl == null) {
                    batchUserDtl = new BatchUserDtl(
                            batchId: batchId,
                            username: username,
                            createdBy: springSecurityService.currentUser.username,
                            userType: userType,
                            admissionNo: admissionNumber)
                    batchUserDtl.save(failOnError: true, flush: true)

                    return "OK"
                } else {
                    return "PRESENT"
                }
            }else{
                return "NO_BATCH"
            }

    }

    /**
     * Get User Data for Editing
     */
    def getUserData(Long userId, Long siteId) {
        User user = User.findByIdAndSiteId(userId, siteId)
        if (!user) {
            return null
        }

        // Prepare user data to pass to GSP
        Map userData = [
                id          : user.id,
                username    : user.username.split("_")[1],
                name        : user.name,
                email       : user.email,
                mobileNumber: user.mobile
        ]

        return userData
    }

    /**
     * Update User Details
     */
    boolean updateUser(Map updateParams, Long siteId) {
        Long userId = updateParams.id as Long
        User user = User.findByIdAndSiteId(userId, siteId)
        if (!user) {
            return false
        }

        user.name = updateParams.name
        user.email = updateParams.email
        user.mobile = updateParams.mobileNumber

        user.save(failOnError:true,flush: true)
        return !user.hasErrors()
    }
/**
 * Get Batch Data
 */
    Map getBatchData(Long batchId) {
        CourseBatchesDtl batch = CourseBatchesDtl.get(batchId)
        if (!batch) {
            return null
        }

        String courseName = InstituteCourseMst.get(batch.courseId)?.name ?: 'Unknown Course'

        Map batchData = [
                id         : batch.id,
                name       : batch.name,
                grade      : batch.grade,
                courseName : courseName,
                conductedBy: batch.conductedBy
        ]

        return batchData
    }

    /**
     * Get Available Books for Batch with Pagination and Search
     */
    Map getAvailableBooksForBatch(Long batchId, Long instituteId, Integer max, Integer offset, String search) {
        // Get default batch ID
        Long defaultBatchId = getDefaultBatchId(instituteId)
        if (!defaultBatchId) {
            return [books: [], totalCount: 0]
        }

        // Get bookIds assigned to default batch
        List<Long> defaultBatchBookIds = getAssignedBookIds(defaultBatchId)

        // Get bookIds already assigned to current batch
        List<Long> currentBatchBookIds = getAssignedBookIds(batchId)

        // Books to display: books in defaultBatchBookIds but not in currentBatchBookIds
        List<Long> availableBookIds = defaultBatchBookIds - currentBatchBookIds

        if (availableBookIds.isEmpty()) {
            return [books: [], totalCount: 0]
        }

        // Fetch BooksMst entries for availableBookIds, apply search and pagination
        def booksList = BooksMst.createCriteria().list(max: max, offset: offset) {
            'in'('id', availableBookIds)
            if (search) {
                ilike('title', "%${search}%")
            }
            order('title', 'asc')
        }

        // Use totalCount from PagedResultList
        def totalCount = booksList.totalCount

        // Prepare list of books
        List<Map> books = booksList.collect { book ->
            [
                    id    : book.id,
                    title : book.title,
                    author: book.authors
            ]
        }

        return [books: books, totalCount: totalCount]
    }


    /**
     * Get Assigned Book IDs for a Batch
     */
    List<Long> getAssignedBookIds(Long batchId) {
        String batchIdStr = libraryBooksService.getAllBatchIds(  ""+batchId)
        List<BooksBatchDtl> assignedBooks = BooksBatchDtl.findAllByBatchIdInList(batchIdStr.split(",") as List<Long>)
        List<Long> assignedBookIds = assignedBooks*.bookId
        return assignedBookIds
    }

    /**
     * Assign Books to Batch
     */
    boolean assignBooksToBatch(Long batchId, List<Long> bookIds) {
        try {
            // Assign selected books to the batch
            bookIds.each { bookId ->
                // Check if the assignment already exists
                BooksBatchDtl existingAssignment = BooksBatchDtl.findByBatchIdAndBookId(batchId, bookId)
                if (!existingAssignment) {
                    BooksBatchDtl booksBatchDtl = new BooksBatchDtl(batchId: batchId, bookId: bookId, dateCreated: new Date())
                    booksBatchDtl.save(flush: true)
                }
            }
            return true
        } catch (Exception e) {
            // Log the exception (not shown here)
            return false
        }
    }

    /**
     * Get Institutes List with Pagination, Search, and Filtering
     */
    def getInstitutesList(Long max, Long offset, String search, String status,Integer siteId) {

        def institutesList = InstituteMst.findAllByNameLikeAndSiteId("%${search}%",siteId,[max: max, offset: offset, sort: 'name', order: 'asc'])
        def totalCount = InstituteMst.countBySiteId(siteId)

        // Prepare the data for the GSP
        List<Map> institutes = institutesList.collect { institute ->
            CourseBatchesDtl defaultBatch = CourseBatchesDtl.findByConductedByAndName(institute.id, 'Default')
            [
                    id          : institute.id,
                    name        : institute.name,
                    contactName : institute.contactName,
                    contactEmail: institute.contactEmail,
                    startDate   : defaultBatch?.startDate?.format('dd-MM-yyyy'),
                    endDate     : defaultBatch?.endDate?.format('dd-MM-yyyy') ?: '',
                    status      : defaultBatch?.status
            ]
        }

        return [institutes: institutes, totalCount: totalCount]
    }

    /**
     * Get Books in Batch with Pagination and Search
     */
    def getBooksInBatch(Long batchId, Long max, Long offset, String search) {
        // Get bookIds associated with the batch
        List<Long> bookIdsInBatch = BooksBatchDtl.createCriteria().list {
            eq('batchId', batchId)
            projections {
                property('bookId')
            }
        } as List<Long>

        if (bookIdsInBatch.isEmpty()) {
            return [books: [], totalCount: 0]
        }

        def criteria = BooksMst.createCriteria()
        def booksList = criteria.list(max: max, offset: offset) {
            'in'('id', bookIdsInBatch)
            if (search) {
                or {
                    ilike('title', "%${search}%")
                    ilike('isbn', "%${search}%")
                    if (search.isLong()) {
                        eq('id', search.toLong())
                    }
                }
            }
            order('title', 'asc')
        }

        def totalCount = booksList.totalCount

        // Prepare the data for the GSP
        List<Map> books = booksList.collect { book ->
            [
                    id    : book.id,
                    title : book.title,
                    isbn  : book.isbn
            ]
        }

        return [books: books, totalCount: totalCount]
    }

    def deleteUser(Long userId, Long batchId) {
        User user = User.get(userId)
        if (user) {
            // Remove user from the batch
            BatchUserDtl batchUser = BatchUserDtl.findByUsernameAndBatchId(user.username, batchId)
            if (batchUser) {
                batchUser.delete(flush: true)
            }
            //check if the batch is the default batch. If so, remove all other batches of that institute.
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchId)
            if (courseBatchesDtl != null) {
                if (courseBatchesDtl.name == "Default") {
                    List<CourseBatchesDtl> courseBatchesDtls = CourseBatchesDtl.findAllByConductedBy(courseBatchesDtl.conductedBy)
                    for (CourseBatchesDtl courseBatchesDtl1 : courseBatchesDtls) {
                        batchUser = BatchUserDtl.findByUsernameAndBatchId(user.username, courseBatchesDtl1.id)
                        if (batchUser != null) {
                            batchUser.delete(flush: true)
                        }
                    }
                }
            }

            return "success"
        }
        return "fail"
    }


}
