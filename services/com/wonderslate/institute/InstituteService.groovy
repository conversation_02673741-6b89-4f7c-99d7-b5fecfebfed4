package com.wonderslate.institute

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SiteMst
import com.wonderslate.groups.GroupsMembersDtl
import com.wonderslate.publish.BooksPermission
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserRole
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional
import groovy.sql.Sql

import java.text.DateFormat
import java.text.SimpleDateFormat

@Transactional
class InstituteService {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    WsLibraryCacheService wsLibraryCacheService
    def redisService
    def grailsApplication

    def checkAdmissionNumber(batchId, admissionNo){
        BatchUserDtl dtl =  BatchUserDtl.findByBatchIdAndAdmissionNo(batchId, admissionNo)
        if(dtl==null){
            return true
        }else{
            return dtl.username.split('_')[1]
        }
    }

    def updateAdmissionNumber(batchId, username, admissionNo){
        BatchUserDtl dtl = BatchUserDtl.findByBatchIdAndUsername(new Long(batchId), username)
        dtl.admissionNo = admissionNo
        dtl.save()
        return true
    }

    def addUserToBatch(username, batchId, classTeacher, instructor, admissionNumber, siteId){
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchId)
        User user = User.findByUsernameAndSiteId(username, siteId)
        if (courseBatchesDtl != null) {
            BatchUserDtl batchUserDtl
            //check if the user is already added
            batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(username,batchId)
            if (batchUserDtl == null) {
                batchUserDtl = new BatchUserDtl(
                        batchId: batchId,
                        username: username,
                        createdBy: springSecurityService.currentUser.username,
                        classTeacher: classTeacher,
                        instructor: instructor,
                        admissionNo: admissionNumber)
                batchUserDtl.save(failOnError: true, flush: true)
                //for instructor type user add publisher access
                //add publishing admin access to the user.
                InstituteMst instituteMst = InstituteMst.findById(courseBatchesDtl.conductedBy)
                Role role
                if(instituteMst.publisherId!=null&&instructor.toString().equals("true")&&siteId==1&&"Default".equals(courseBatchesDtl.name)){
                    user.publisherId = instituteMst.publisherId
                    user.save(failOnError: true, flush: true)
                    role = Role.findByAuthority("ROLE_BOOK_CREATOR")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_PUBLISHER")
                    UserRole.create(user, role, true)
                }

                if(courseBatchesDtl.groupId!=null&&!"Default".equals(courseBatchesDtl.name)){
                    GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                    if(groupsMembersDtl==null){
                        groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: instructor.toString().equals("true")?"admin":"user",
                                profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                        groupsMembersDtl.save(failOnError: true, flush: true)
                    }
                }

                //if instructor then add to the institute teachers group
                if(courseBatchesDtl.groupId!=null&&"Default".equals(courseBatchesDtl.name)&&instructor.toString().equals("true")){
                    GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                    if(groupsMembersDtl==null){
                        groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "admin",
                                profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                        groupsMembersDtl.save(failOnError: true, flush: true)
                    }
                }
                return "OK"
            } else {
                return "PRESENT"
            }
        }else{
            return "NO_BATCH"
        }
    }

    def addBooksToBatch(Integer batchId, Integer defaultBatchId,String bookIds){

        //seperate out the book ids
        String[] books = bookIds.split(",")
        //insert each book
        for(int i=0;i<books.length;i++) {
            if (books[i] == null || (books[i].trim().length() == 0)) {
                continue
            };
            //check whether this book belongs to this institute
            BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBookIdAndBatchId(new Integer(books[i]),defaultBatchId)
            if(booksBatchDtl!=null){

                //check if the book already exists
                booksBatchDtl = BooksBatchDtl.findByBookIdAndBatchId(new Integer(books[i]),batchId)
                if(booksBatchDtl==null) {
                    booksBatchDtl = new BooksBatchDtl(batchId: batchId, bookId: new Integer(books[i]))
                    booksBatchDtl.save(failOnError: true, flush: true)
                }
            }
        }

        //update the cache
       wsLibraryCacheService.instituteBooksforUser(""+batchId)
        wsLibraryCacheService.getInstituteBooksPagination(""+batchId)

        return
    }

    def removeUserFromBatch(Integer userId, Integer batchId){


           User user = User.findById(userId)
            BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user.username,batchId)
            if(batchUserDtl!=null) {
                CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchId)
                if(courseBatchesDtl.groupId!=null){
                    GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,user.username)
                    if(groupsMembersDtl!=null)groupsMembersDtl.delete(flush: true)
                }
                batchUserDtl.delete(flush: true)
            }
            //delete the library books from deleted user.
            List books = BooksPermission.findAllByUsernameAndPoType(user.username,"ADDEDFROMINSTITUTE")
            books.each {book->
                book.delete(flush:true)
            }

            dataProviderService.getBooksListForUser(user.username)
            dataProviderService.getUserBatchesAsStudent(user.username);
            dataProviderService.getUserBatchesIds(user.username)

           return

    }

    def editBatch(Integer batchId,String batchName,String endDateStr,String syllabus,String grade){
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchId)
        if(courseBatchesDtl!=null){
            courseBatchesDtl.name = batchName
            if(endDateStr!=null&&!"".equals(endDateStr)){
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
                courseBatchesDtl.endDate = df.parse(endDateStr);
            }else{
                courseBatchesDtl.endDate = null
            }
            courseBatchesDtl.syllabus = syllabus
            courseBatchesDtl.grade = grade
            courseBatchesDtl.save(failOnError: true, flush: true)
        }

       return
    }

     CourseBatchesDtl getBatchDetails(Integer batchId){
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchId)

        return courseBatchesDtl
    }

    def validateInstituteUrl(params){
        InstituteMst instituteMst
        String status="OK"
        String instituteUrl=params.instituteUrl
        Integer instituteId=Integer.parseInt(params.instituteId)
         instituteMst = InstituteMst.findByUrlname(instituteUrl)
        if(instituteMst!=null){
            status="exist"
        }else{
            instituteMst=InstituteMst.findById(new Long(params.instituteId))
            instituteMst.urlname=params.instituteUrl
            instituteMst.save(failOnError: true, flush: true)
            redisService.("institutionsUrl_"+instituteMst.urlname) = ""+instituteMst.id

        }
        def json =  ["status":status]
        return json
    }

    def addDefaultBooksToInstitute(Long instituteId){
        InstituteMst instituteMst = dataProviderService.getInstituteMst(instituteId)
        InstituteMst defaultInstituteMst = InstituteMst.findByDefaultBooksTemplateInstituteAndLevelAndSyllabus("true",instituteMst.level,instituteMst.syllabus)
        if(defaultInstituteMst!=null){
          Integer defaultBatchId = (CourseBatchesDtl.findByConductedByAndName(defaultInstituteMst.id,"Default")).id
          Integer targetDefaultBatchId = (CourseBatchesDtl.findByConductedByAndName(instituteId,"Default")).id
            copyBooks(defaultBatchId,targetDefaultBatchId)
        }
    }

    def copyBooks(Long sourceBatchId,Long targetBatchId){
        List sourceBooks = BooksBatchDtl.findAllByBatchId(sourceBatchId)
        sourceBooks.each { sourceBook ->
            BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBatchIdAndBookId(targetBatchId,sourceBook.bookId)
            if (booksBatchDtl == null) {
                booksBatchDtl = new BooksBatchDtl(batchId:  targetBatchId, bookId:  sourceBook.bookId)
                booksBatchDtl.save(failOnError: true, flush: true)
            }
        }
        redisService.("userMyLibraryInstituteBooks_" + targetBatchId) = null
        wsLibraryCacheService.getInstituteBooksPagination(targetBatchId)
        wsLibraryCacheService.getInstituteBooksPaginationNew(targetBatchId)
    }

    def addDefaultBooksToBatch(CourseBatchesDtl courseBatchesDtl, String level,String syllabus){
        InstituteMst defaultInstituteMst = InstituteMst.findByDefaultBooksTemplateInstituteAndLevelAndSyllabus("true",level,syllabus)
        if(defaultInstituteMst!=null){
            CourseBatchesDtl defaultCourseBatchesDtl = CourseBatchesDtl.findByConductedByAndGrade(defaultInstituteMst.id,courseBatchesDtl.grade)
            if(defaultCourseBatchesDtl!=null)
             copyBooks(defaultCourseBatchesDtl.id,courseBatchesDtl.id)

        }
    }

    def getLatestReleasedInstitutes(totalRequired){
        String sql = "select name,id,urlname,town from institute_mst where edu_wonder='true' and gone_live='true' and urlname is not null order by gone_live_date desc "
        if("min".equals(totalRequired)) sql +=" limit 8"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("latestReleasedInstitutes_"+totalRequired) = element

    }

}
