package com.wonderslate.usermanagement

import com.wonderslate.cache.DataProviderService
import grails.transaction.Transactional
import groovy.json.JsonSlurper

import javax.crypto.SecretKeyFactory
import javax.crypto.spec.PBEKeySpec
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.Cipher
import javax.crypto.SecretKey
import java.security.Key
import java.security.spec.KeySpec
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.codec.binary.Base64;

@Transactional
class SecurityService {
    DataProviderService dataProviderService
    def redisService

    String encrypt(String text, int siteId) {
        try {
            if(redisService.("securityKeys_"+siteId)==null) dataProviderService.getSecurityKeys(siteId)
            String securityKey = redisService.("securityKeys_"+siteId)

            String keyValue = redisService.("securityKeys_"+siteId)
            SecretKeyFactory factory =   SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1");
            KeySpec spec = new PBEKeySpec(keyValue.toCharArray(), hex("dc0da04af8fee58593442bf834b30739"), 1000, 128);

            Key key = new SecretKeySpec(factory.generateSecret(spec).getEncoded(), "AES");
            Cipher c = Cipher.getInstance("AES/CBC/PKCS5Padding");
            c.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(hex("dc0da04af8fee58593442bf834b30739")));

            byte[] encVal = c.doFinal(text.getBytes());
            String base64EncodedEncryptedData = new String(Base64.encodeBase64(encVal));

            return base64EncodedEncryptedData;
        }  catch (Exception e) {
            println("Exception="+e.toString())
        }

        return "";
    }

    String decrypt(String text, int siteId) {
        try {
            if(redisService.("securityKeys_"+siteId)==null) dataProviderService.getSecurityKeys(siteId)
            String securityKey = redisService.("securityKeys_"+siteId)
            byte[] decodedBytes = Base64.getDecoder().decode(text);
            Cipher ecipher = Cipher.getInstance("AES/CBC/PKCS5PADDING", "SunJCE");
            IvParameterSpec iv =  new IvParameterSpec((securityKey).getBytes());
            SecretKey secretKey = new SecretKeySpec(securityKey.getBytes(), "AES");
            ecipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

            return new String(ecipher.doFinal(decodedBytes));
        }  catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }
    String testEncrypt(){
        String keyValue = "Abcdefghijklmnop";
        SecretKeyFactory factory =   SecretKeyFactory.getInstance("PBKDF2WithHmacSHA1");
        KeySpec spec = new PBEKeySpec(keyValue.toCharArray(), hex("dc0da04af8fee58593442bf834b30739"), 1000, 128);

        Key key = new SecretKeySpec(factory.generateSecret(spec).getEncoded(), "AES");
        Cipher c = Cipher.getInstance("AES/CBC/PKCS5Padding");
        c.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(hex("dc0da04af8fee58593442bf834b30739")));

        byte[] encVal = c.doFinal("Hello love".getBytes());
        String base64EncodedEncryptedData = new String(Base64.encodeBase64(encVal));
        return base64EncodedEncryptedData;
    }



      String hex(byte[] bytes) {
        return Hex.encodeHexString(bytes);
    }

    byte[] hex(String str) {

            return Hex.decodeHex(str.toCharArray());

    }

   String base64(byte[] bytes) {
        return Base64.encodeBase64String(bytes);
    }

    byte[] base64(String str) {
        return Base64.decodeBase64(str);
    }

}
