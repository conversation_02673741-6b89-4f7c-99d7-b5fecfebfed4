package com.wonderslate.usermanagement

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.sql.Date

@Transactional
class UserPointsCacheService {

    def grailsApplication
    def redisService

    def serviceMethod() {
    }

    def updateLifeTimeUserPoints(int scoreTypeId ){
        HashMap<String, Integer> PointsHashMap = new HashMap<String,Integer>()
        String userName = "", date = ""
        String sql = " SELECT u.username, SUM(u.points) AS points" +
                " FROM user_points u" +
                " WHERE u.score_type_id = " + scoreTypeId +
                " GROUP BY u.username" +
                " ORDER BY u.username"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def result = sql1.rows(sql)
        result.collect { user7DaysPoint ->
            Integer pointsInt = user7DaysPoint['points']
            PointsHashMap.put(user7DaysPoint['username'],pointsInt)
        }
        Gson gson = new Gson();
        String element = gson.toJson(PointsHashMap)
        redisService.("userLifeTimePoints_"+scoreTypeId) = element
    }

    def getLifeTimeUserPoints(int scoreTypeId){
        if(redisService.("userLifeTimePoints_"+scoreTypeId) == null) updateLifeTimeUserPoints(scoreTypeId)
        updateLifeTimeUserPoints(scoreTypeId)
        HashMap pointsHashMap = null
        pointsHashMap = new JsonSlurper().parseText(redisService.("userLifeTimePoints_"+scoreTypeId))
        return pointsHashMap
    }

    def addLifeTimeUserPoints(String data,int scoreTypeId){
        redisService.("userLifeTimePoints_"+scoreTypeId) = data
    }

    def update7DaysUserPoints(int scoreTypeId ){
        HashMap<String,HashMap<String, Integer>> PointsHashMap = new HashMap<String,HashMap<String,Integer>>()
        String userName = "", date = ""
        String sql = " SELECT u.username, SUM(u.points) AS points, DATE(u.date_created) AS date_created" +
                " FROM user_points u" +
                " WHERE u.score_type_id = " + scoreTypeId +
                " AND u.date_created > DATE(DATE_ADD(SYSDATE(),interval -7 day))" +
                " GROUP BY u.username , DATE(u.date_created)" +
                " ORDER BY u.username"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def result = sql1.rows(sql)
        List user7DaysPoints = result.collect { user7DaysPoint ->
            HashMap<String,Integer> dateAndPoint = null
            if(PointsHashMap.get(user7DaysPoint['username']) == null){
                dateAndPoint = new HashMap<>()

            }else{
                dateAndPoint = PointsHashMap.get(user7DaysPoint['username'])
            }
            String dateCreated = user7DaysPoint['date_created'].toString().split(" ")[0]
            Integer pointsInt = user7DaysPoint['points']
            dateAndPoint.put(dateCreated,pointsInt)
            PointsHashMap.put(user7DaysPoint['username'],dateAndPoint)
        }
        Gson gson = new Gson();
        String element = gson.toJson(PointsHashMap,new TypeToken<HashMap<String,HashMap<String, Integer>>>() {}.getType())
        redisService.("user7daysPoints_"+scoreTypeId) = element
    }

    def get7DaysUserPoints(int scoreTypeId){
        if(redisService.("user7daysPoints_"+scoreTypeId) == null)update7DaysUserPoints(scoreTypeId)
        HashMap pointsHashMap = null
        pointsHashMap = new JsonSlurper().parseText(redisService.("user7daysPoints_"+scoreTypeId))
        return pointsHashMap
    }

    def add7DaysUserPoints(String data,int scoreTypeId){
        redisService.("user7daysPoints_"+scoreTypeId) = data
    }

    def getTopRankersCache(int scoreType,String rankType){
        if(redisService.(scoreType+"_"+rankType) == null)
            updateTopRankersCache(scoreType,rankType)
        Map topersRankMap = new HashMap()
        if(redisService.(scoreType+"_"+rankType) != null) topersRankMap = new JsonSlurper().parseText(redisService.(scoreType+"_"+rankType))
        return topersRankMap

    }

    def updateTopRankersCache(int scoreType, String rankType){
        String sql = " SELECT u.name, tr.score, u.id, u.profilepic, tr.user_name" +
                     " FROM top_rankers tr, user u" +
                     " WHERE tr.user_name = u.username" +
                     " AND tr.rank_type = '" +rankType + "'" +
                     " AND tr.score_type = " + scoreType +
                     " ORDER BY tr.score DESC"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def result = sql1.rows(sql)
        if(result != null){
            Map topersRankMap = new HashMap()
            int rank = 1;
            result.collect { toperRank ->
                topersRankMap.put(toperRank['user_name'],['name':toperRank['name'],'id':toperRank['id'],'profilepic':toperRank['profilepic'],'score':toperRank['score'],'rank':rank])
                rank ++
            }
            Gson gson = new Gson();
            String element = gson.toJson(topersRankMap,new TypeToken<HashMap>() {}.getType())
            redisService.(scoreType+"_"+rankType) = element
        }
    }

    def updateUserPointsModuleWise(String username){
        String sql = " SELECT st.score_type AS scoreType, SUM(up.points) AS score, st.id" +
                " FROM user_points up, score_type st" +
                " WHERE up.score_type_id = st.score_id" +
                " AND up.username = '" + username + "'" +
                " GROUP BY st.score_type, st.id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def result = sql1.rows(sql)
        if(result != null){
            List<HashMap> moduleWiseScoreList = new ArrayList()
            HashMap moduleWiseScore = new HashMap()
            result.collect { r->
                HashMap map = new HashMap()
                map.put("scoreType",r['scoreType'])
                map.put("score",r['score'])
                map.put("scoreTypeId",r['id'])
                moduleWiseScoreList.add(map)
            }
            moduleWiseScore.put("moduleWiseScore",moduleWiseScoreList)
            Gson gson = new Gson()
            String data = gson.toJson(moduleWiseScore,new TypeToken<HashMap>() {}.getType())
            redisService.(""+username+"score") = data
        }
    }

    def getUserPointsModuleWise(String userName){
        if(redisService.(""+userName+"score") == null) updateUserPointsModuleWise(userName)
        Map moduleWiseScore = new HashMap()
        if(redisService.(""+userName+"score") != null) moduleWiseScore = new JsonSlurper().parseText(redisService.(""+userName+"score"))
        return moduleWiseScore
    }

    def addUserPointsModuleWise(String data,String username){
        redisService.(""+username+"score") = data
    }
}
