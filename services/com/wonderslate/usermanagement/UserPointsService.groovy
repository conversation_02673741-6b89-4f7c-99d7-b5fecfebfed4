package com.wonderslate.usermanagement

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import grails.transaction.Transactional

import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit;

@Transactional
class UserPointsService {

    def springSecurityService
    def DataProviderService dataProviderService
    def UserPointsCacheService userPointsCacheService

    def serviceMethod() {

    }

    def addPoints(String reason, int points, int scoreTypeId,String userNameStr){
//        println("==============reason================")
//        println(reason)
//        println("=============points=================")
//        println(points)
//        println("===============scoreTypeId===============")
//        println(scoreTypeId)
//        println("=================userNameStr=============")
//        println(userNameStr)
        int user7daysPoints = 0

        // ADD points to UserPoints Table
        DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd")
        Date currentDate = df1.parse(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
        def user = null
        if(userNameStr == null || userNameStr.isEmpty()) user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        else if(!userNameStr.isEmpty()) user = dataProviderService.getUserMst(userNameStr)
        UserPoints userPoints = new UserPoints()
        userPoints.username = user.username
        userPoints.reason = reason
        userPoints.points = points
        userPoints.scoreTypeId = scoreTypeId
        userPoints.dateCreated = currentDate

        //Date in string format : "25/11/2020"
        String currentDateStr = df1.format(currentDate).split(" ")[0]

        //update user lifetime points in user table
        if(user.lifeTimePoints == null) user.lifeTimePoints = points
        else user.lifeTimePoints = user.lifeTimePoints + points
        user.save(failOnError: true,flush: true)
//        println(user)
        //Update 7daysUserPoints cache data
        //Key is the userName
        //Value is hashmap of {date1:points,date2:points,}
        //sort the map with keys (dates)
        //first key will be the oldest date
        //calculate difference oldest date and current date
        //check if date already exists then update the points
        //if difference is greater then or equal to 7 days then remove the oldest date and add current date
        //Calculate users 7days score

        HashMap<String,HashMap<String, Integer>> pointsHashMap = userPointsCacheService.get7DaysUserPoints(scoreTypeId)
        HashMap<String,Integer> lifeTimeUserPoints = userPointsCacheService.getLifeTimeUserPoints(scoreTypeId)
        HashMap moduleWiseScoreMap = userPointsCacheService.getUserPointsModuleWise(user.username)
        userPoints.save(failOnError: true,flush: true)
        if(moduleWiseScoreMap != null && moduleWiseScoreMap.get("moduleWiseScore") != null) updateUserModuelWieScore(user.username,points,scoreTypeId)
        else userPointsCacheService.updateUserPointsModuleWise(user.username)
        int lifeTimeUserPointsForTheScoreType = 0
        if(pointsHashMap.get(user.username) == null) {
            userPointsCacheService.update7DaysUserPoints(scoreTypeId)
            pointsHashMap = userPointsCacheService.get7DaysUserPoints(scoreTypeId)
//            println("==1111111==pointsHashMap=====")
//            println(pointsHashMap)
            user7daysPoints = points
        }else {
            Map<String, Integer> sortedByDateMap = new TreeMap<>(pointsHashMap.get(user.username))
            Date firstKsyDate = df1.parse(sortedByDateMap.firstKey())
            int dateDifference = TimeUnit.DAYS.convert(Math.abs(currentDate.getTime() - firstKsyDate.getTime()), TimeUnit.MILLISECONDS)
            if(sortedByDateMap.containsKey(currentDateStr)){
                sortedByDateMap.put(currentDateStr,sortedByDateMap.get(currentDateStr) + points)
            }else if(dateDifference >= 7){
                sortedByDateMap.remove(sortedByDateMap.firstKey())
                sortedByDateMap.put(currentDateStr,points)
            }else sortedByDateMap.put(currentDateStr,points)
            pointsHashMap.put(user.username,sortedByDateMap)
            for(Map.Entry<String,Integer> map:sortedByDateMap){
                user7daysPoints = user7daysPoints + map.value
            }
            Gson gson = new Gson();
            String data = gson.toJson(pointsHashMap,new TypeToken<HashMap<String,HashMap<String, Integer>>>() {}.getType())
            userPointsCacheService.add7DaysUserPoints(data,scoreTypeId)
        }
        if(lifeTimeUserPoints.get(user.username) == null) {
            userPointsCacheService.updateLifeTimeUserPoints(scoreTypeId)
            lifeTimeUserPoints = userPointsCacheService.getLifeTimeUserPoints(scoreTypeId)
            lifeTimeUserPointsForTheScoreType = lifeTimeUserPoints.get(user.username)
        }else{
            lifeTimeUserPointsForTheScoreType = lifeTimeUserPoints.get(user.username)
            lifeTimeUserPointsForTheScoreType = lifeTimeUserPointsForTheScoreType + points
        }
        lifeTimeUserPoints.put(user.username,lifeTimeUserPointsForTheScoreType)
        ArrayList<Date> datesFromMap = new ArrayList<>()
//        println("====pointsHashMap=====")
//        println(pointsHashMap)

        Gson gson = new Gson();
        String data = gson.toJson(lifeTimeUserPoints)
        userPointsCacheService.addLifeTimeUserPoints(data,scoreTypeId)
        HashMap topRankersCache = userPointsCacheService.getTopRankersCache(scoreTypeId,"7days")
        updateTopRankers(topRankersCache, user, user7daysPoints,scoreTypeId,"7days")
        topRankersCache = userPointsCacheService.getTopRankersCache(scoreTypeId,"lifeTime")
        updateTopRankers(topRankersCache, user, lifeTimeUserPointsForTheScoreType,scoreTypeId,"lifeTime")

        return userPoints
    }

    private void updateTopRankers(HashMap topRankersCache, User user, int points,int scoreType,String rankType) {
//        println("====topRankersCache=====")
//        println(topRankersCache)
        DateFormat df1 = new SimpleDateFormat("yyyy-MM-dd")
        Date currentDate = df1.parse(new SimpleDateFormat("yyyy-MM-dd").format(new Date()))
        if (topRankersCache.get(user.username) != null) {
            TopRankers topRankers = TopRankers.findByRankTypeAndUserNameAndScoreType(rankType, user.username,scoreType)
            if(topRankers != null){
                topRankers.score = points
                topRankers.save(failOnError: true, flush: true)
            }else {
                TopRankers topRanker = new TopRankers()
                topRanker.rankType = rankType
                topRanker.score = points
                topRanker.userName = user.username
                topRanker.createdDate = currentDate
                topRanker.scoreType = scoreType
                topRanker.save(failOnError: true, flush: true)
            }
        } else {
            List<TopRankers> topRankers = TopRankers.findAllByRankTypeAndScoreType(rankType,scoreType, [sort: 'score'])
            if (topRankers != null && topRankers.size() >= 10 && points >= topRankers.get(0).score) {
                topRankers.get(0).score = points
                topRankers.get(0).userName = user.username
                topRankers.get(0).save(failOnError: true, flush: true)
            }else if(topRankers == null || topRankers.size() < 10){
                TopRankers topRanker = new TopRankers()
                topRanker.rankType = rankType
                topRanker.score = points
                topRanker.userName = user.username
                topRanker.createdDate = currentDate
                topRanker.scoreType = scoreType
                topRanker.save(failOnError: true, flush: true)
            }
        }
        userPointsCacheService.updateTopRankersCache(scoreType,rankType)
    }

    private void updateUserModuelWieScore(String userName,int points,int scoreTypeId){
        ScoreType scoretype = ScoreType.findByScoreId(scoreTypeId)
        HashMap moduleWiseScoreMap = userPointsCacheService.getUserPointsModuleWise(userName)
        List<HashMap> userPointsModuleWiseList = moduleWiseScoreMap.get("moduleWiseScore")
        println("moduleWiseScoreMap====1111====")
        println(moduleWiseScoreMap)
        int i = 0;
        for(HashMap m:userPointsModuleWiseList){
            String key1 = m.get("scoreType"),type1 = scoretype.getScoreType()
            if(key1.equals(type1)){
                String scoreStr = m.get("score")
                Integer score = scoreStr == null ? 0:Integer.parseInt(scoreStr)
                score = score + points
                m.put("score",score)
                userPointsModuleWiseList.set(i,m)
//                println("map====inside if====")
//                println(m)
            }
            i++;
        }
        moduleWiseScoreMap.put("moduleWiseScore",userPointsModuleWiseList)
//        println("moduleWiseScoreMap=== after if=====")
//        println(moduleWiseScoreMap)
        Gson gson = new Gson()
        String data = gson.toJson(moduleWiseScoreMap,new TypeToken<HashMap>() {}.getType())
        userPointsCacheService.addUserPointsModuleWise(data,userName)
    }

    def getUserPointsModuleWise(String uerName){
        HashMap moduleWiseScoreMap = userPointsCacheService.getUserPointsModuleWise(uerName)
        return moduleWiseScoreMap
    }

    def getTopRankers(int scoreType, String rankType){
        return userPointsCacheService.getTopRankersCache(scoreType,rankType)
    }
}
