package com.wonderslate.usermanagement

import com.google.api.client.json.Json
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.EmailMst
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.PrepjoyService
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteIpAddress
import com.wonderslate.institute.InstituteMst
import com.wonderslate.log.NomineeVotingDtl
import com.wonderslate.log.TeacherNomineeDtl
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.logs.LogsService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksPermissionCopy
import com.wonderslate.publish.BooksQueueDtl
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.ChapterAccess
import com.wonderslate.data.ResourceDtl
import com.wonderslate.log.ResourceView
import com.wonderslate.publish.Publishers
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookPriceService
import com.wonderslate.shop.PagesService
import com.wonderslate.shop.PurchaseService
import com.wonderslate.shop.WsshopService
import grails.converters.JSON
import grails.plugins.mail.MailService
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.apache.commons.validator.routines.EmailValidator
import org.json.JSONObject
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest

import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.imageio.ImageIO
import javax.servlet.http.Cookie
import java.awt.image.BufferedImage
import java.text.DateFormat
import java.text.SimpleDateFormat
import com.razorpay.Payment
import com.wonderslate.sqlutil.SafeSql

@Transactional
class UserManagementService {
    SpringSecurityService springSecurityService
    MailService mailService
    def redisService
    def grailsApplication
    DataProviderService dataProviderService
    AsyncLogsService asyncLogsService
    UserManagementService userManagementService
    UtilService utilService
    WsshopService wsshopService
    PrepjoyService prepjoyService
    ProgressService progressService
    LogsService logsService
    WsLibraryCacheService wsLibraryCacheService
    BookPriceService bookPriceService
    PagesService pagesService
    def rememberMeServices

    static final int FRIEND_REQUESTED = 0
    static final int FRIEND_ACCEPTED = 1
    static final int FRIEND_IGNORED = 2
    static final int FRIEND_UNFRIENDED = 3

    static final int GROUP_ACTIVE = 0
    static final int GROUP_DELETED = 1

    static final int GROUP_CREATOR = 0
    static final int GROUP_ADMIN = 1
    static final int GROUP_MEMBER = 2

    def setEntrySession(siteName,session,servletContext) {
        session["userSessionSet"]="true"
        String entryController;
        if(session['entryController']==null){
            entryController=siteName
            if(entryController!=null && !"".equals(entryController) && !"null".equals(entryController)) {
                if("thewinnersinstitute".equals(entryController)||"jbclasses".equals(entryController)){
                    session['entryController'] ="welcome"
                }
                else if("currentaffairs".equals(entryController)||"karnataka".equals(entryController)||"neet".equals(entryController)||"enggentrances".equals(entryController)||"cacscma".equals(entryController)||"ctet".equals(entryController)){
                    session['siteName'] = entryController
                    session['entryController'] ="prepjoy"
                    session.setAttribute("prepjoySite", "true")
                }else {
                    session['entryController'] = entryController;

                }
            }
        } else {
            entryController=session['entryController']
        }

        //once you get the siteName add the siteId
        if(entryController!= null&&!"".equals(entryController)){
            if("books".equals(entryController)) session['siteId'] = new Integer(1);
            else if("arihant".equals(entryController)) session['siteId'] = new Integer(3);
            else if("sage".equals(entryController)) session['siteId'] = new Integer(9);
            else if("vidyaprakashan".equals(entryController)) session['siteId'] = new Integer(8);
            else if("arivupro".equals(entryController)) session['siteId'] = new Integer(10);
            else if("edugorilla".equals(entryController)) session['siteId'] = new Integer(11);
            else if("evidya".equals(entryController)) session['siteId'] = new Integer(12);
            else if("ebooksaccess".equals(entryController)) session['siteId'] = new Integer(26);
            else if("ebouquet".equals(entryController)) session['siteId'] = new Integer(24);
            else if("etexts".equals(entryController)) session['siteId'] = new Integer(23);
            else if("libwonder".equals(entryController)) session['siteId'] = new Integer(25);
            else if("currentaffairs".equals(session['siteName'])) session['siteId'] = new Integer(27);
            else if("ctet".equals(session['siteName'])) session['siteId'] = new Integer(33);
            else if("neet".equals(session['siteName'])) session['siteId'] = new Integer(32);
            else if("enggentrances".equals(session['siteName'])) session['siteId'] = new Integer(31);
            else if("karnataka".equals(session['siteName'])) session['siteId'] = new Integer(30);
            else if("cacscma".equals(session['siteName'])) session['siteId'] = new Integer(35);
            else if("radianbooks".equals(entryController)) session['siteId'] = new Integer(37);
            else if("oswaal".equals(entryController)) session['siteId'] = new Integer(38);
            else if("oswalpublisher".equals(entryController)) session['siteId'] = new Integer(39);
            else if("mtg".equals(entryController)) session['siteId'] = new Integer(46);

        }

        if(servletContext.getAttribute("googleLogEnabled")==null){
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }

        if(springSecurityService.currentUser!=null){
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }
        }

        if(session["activeCategories_"+session['siteId']] == null) {
            if(redisService.("activeCategories_"+session['siteId'])==null) wsshopService.activeCategories(new Integer(session['siteId']))
            session["activeCategories_"+session['siteId']] = redisService.("activeCategories_"+session['siteId'])
        }
        if(session["activeCategoriesSyllabus_"+session['siteId']] == null) {
            if(redisService.("activeCategoriesSyllabus_"+session['siteId'])==null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(session['siteId']))
            session["activeCategoriesSyllabus_"+session['siteId']] = redisService.("activeCategoriesSyllabus_"+session['siteId'])
        }

        if(session["googleUAId_"+session['siteId']]==null){
            if(redisService.("googleUAId_"+session['siteId'])==null) dataProviderService.getGoogleUniversalAnalytics(session['siteId'])
            session["googleUAId_"+session['siteId']] = redisService.("googleUAId_"+session['siteId'])
        }

        return
    }

    def setUserSession(siteName,session,servletContext,response) {
        session["userSessionSet"]="true"
        SiteMst siteMst  = dataProviderService.getSiteMstBySiteName(siteName)
        SiteDtl siteDtl = dataProviderService.getSiteDtl(siteMst.id)
        Cookie cookie = new Cookie("siteName", "privatelabel")
        cookie.path = "/"
        response.addCookie(cookie)
        cookie = new Cookie("plSiteName", siteName)
        cookie.path = "/"
        response.addCookie(cookie)
        session.setAttribute("commonWhiteLabel", "true")
        session.setAttribute("entryController", "privatelabel")
        session.setAttribute("siteName", siteMst.siteName)
        session.setAttribute("clientName", siteMst.clientName)
        session.setAttribute("siteId", siteMst.id)
        session.setAttribute("themeColor", siteDtl.themeColor)
        session.setAttribute("favicon", siteDtl.favicon)
        session.setAttribute("logo", siteDtl.logo)
        session.setAttribute("logoIcon", siteDtl.logoIcon)
        session.setAttribute("bannerImage", siteDtl.bannerImage)
        session.setAttribute("addressLine1", siteDtl.addressLine1)
        session.setAttribute("addressLine2", siteDtl.addressLine2)
        session.setAttribute("gstNumber", siteDtl.gstNumber)
        session.setAttribute("websiteLink", siteDtl.websiteLink)
        session.setAttribute("mobileNumber", siteDtl.mobileNumber)
        session.setAttribute("emailAddress", siteDtl.emailAddress)
        session.setAttribute("facebookLink", siteDtl.facebookLink)
        session.setAttribute("twitterLink", siteDtl.twitterLink)
        session.setAttribute("instagramLink", siteDtl.instagramLink)
        session.setAttribute("linkedinLink", siteDtl.linkedinLink)
        session.setAttribute("youtubeLink", siteDtl.youtubeLink)
        session.setAttribute("privacyPolicy", siteDtl.privacyPolicy)
        session.setAttribute("termsCondition", siteDtl.termsCondition)
        session.setAttribute("playStore", siteDtl.playStore)
        session.setAttribute("appStore", siteDtl.appStore)
        session.setAttribute("customStyles", siteDtl.customStyles)
        session.setAttribute("companyName", siteDtl.companyName)
        session.setAttribute("googleAnalyticsId",siteMst.googleUniversalAnalyticsKey)
        session.setAttribute("enableContactus",siteDtl.enableContactus)
        session.setAttribute("enableOTPLogin",siteDtl.enableOTPLogin)
        session.setAttribute("siteTitle",siteDtl.siteTitle)
        session.setAttribute("siteDescription",siteDtl.siteDescription)
        session.setAttribute("keywords",siteDtl.keywords)
        session.setAttribute("showLandingPage",siteDtl.showLandingPage)
        session.setAttribute("whatsappLink",siteDtl.whatsappLink)
        session.setAttribute("telegramLink",siteDtl.telegramLink)
        session.setAttribute("disableStore",siteDtl.disableStore)
        session.setAttribute("disableScratchCode",siteDtl.disableScratchCode)
        session.setAttribute("ipAddressAccess",siteDtl.ipAddressAccess)
        session.setAttribute("digitalLibraryLandingPage",siteDtl.digitalLibraryLandingPage)
        session.setAttribute("showAnalytics",siteDtl.showAnalytics)
        if("true".equals(siteMst.exercises)) session.setAttribute("showAccessCode","true")
        if(siteDtl.customLandingPage!=null) session.setAttribute("customLandingPage", siteDtl.customLandingPage)

        // Google Analytics
        if(servletContext.getAttribute("googleLogEnabled") == null) {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled", "true")
            else servletContext.setAttribute("googleLogEnabled", "false")
        }

        // Get user basic analytics with Cart count
        if(springSecurityService.currentUser != null) {
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
            }
            String username = springSecurityService.currentUser.username
            if(session.getAttribute("userdetails").authorities.any {
                it.authority == "ROLE_INSTITUTE_MANAGER"
            }) {
                def instituteManagerInstituteId = userManagementService.getInstituteForInstituteManager(username, session["siteId"])
                if(instituteManagerInstituteId!=-1) session["instituteManagerInstituteId"] = ""+instituteManagerInstituteId else session["instituteManagerInstituteId"] = null
            }
            if(redisService.("usersCartBooksDetails_" + username) == null) {
                dataProviderService.usersCartBooksDetailsByUsername(username, session["siteId"])
            }
            session["userCartCount"] = Integer.parseInt(redisService.("usersCartBooksDetailsCount_" + username))
        }else{
            String username = session["siteId"]+session.getId()+"_temp"
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }

        // Active Categories
        if(session["activeCategories_" + session["siteId"]] == null) {
            if(redisService.("activeCategories_" + session["siteId"]) == null) wsshopService.activeCategories(new Integer(""+session["siteId"]))
            session["activeCategories_" + session["siteId"]] = redisService.("activeCategories_" + session["siteId"])
        }

        // Active Categories & Syllabus
        if(session["activeCategoriesSyllabus_" + session["siteId"]] == null) {
            if(redisService.("activeCategoriesSyllabus_" + session["siteId"]) == null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(""+session["siteId"]))
            session["activeCategoriesSyllabus_" + session["siteId"]] = redisService.("activeCategoriesSyllabus_" + session["siteId"])
        }

        if(session["activeGrades_" + session["siteId"]] == null) {
            if(redisService.("activeGrades_" + session["siteId"])==null) wsshopService.getActiveGrades(new Integer(""+session["siteId"]))
            session["activeGrades_" + session["siteId"]] = redisService.("activeGrades_" + session["siteId"])
        }
        if(session["activeSubjects_" + session["siteId"]] == null) {
            if(redisService.("activeSubjects_" + session["siteId"])==null) wsshopService.getActiveSubjects(new Integer(""+session["siteId"]))
            session["activeSubjects_" + session["siteId"]] = redisService.("activeSubjects_" + session["siteId"])
        }
        // Google Universal Analytics
        if(session["googleUAId_" + session["siteId"]] == null) {
            if(redisService.("googleUAId_" + session["siteId"]) == null) dataProviderService.getGoogleUniversalAnalytics(session["siteId"])
            session["googleUAId_" + session["siteId"]] = redisService.("googleUAId_" + session["siteId"])
        }

        pagesService.getCustomPageMenus(new Long(""+session["siteId"]))
        session["customPageMenus_" + session["siteId"]] = redisService.("customPageMenus_" + session["siteId"])

        if(siteMst.id.intValue()==56){
            session["levelLabel"] = "Select University"
            session["syllabusLabel"] = "Select Course"
            session["gradeLabel"] = "Select Semester"
            session["subjectLabel"] = "Select Subject"
            session["doNotChangeLabel"] = "true"
        }
        else if(siteMst.id.intValue()==82){
            session["levelLabel"] = "Select University"
            session["syllabusLabel"] = "Select"
            session["gradeLabel"] = "Select"
            session["subjectLabel"] = "Select"
            session["doNotChangeLabel"] = "true"
        }
        else if (siteMst.id.intValue() == 66){
            session['wileySite'] = true
            if(redisService.("wileyCollectionBook")==null) wsshopService.getWileyCollectionMainBook()
            session['wileyCollectionBookId'] = redisService.("wileyCollectionBook")
        }
        if(siteMst.currentAffairsType!=null&&!"".equals(siteMst.currentAffairsType)) session['showCurrentAffairs']="true"
        if(siteMst.playStoreInstallUrl!=null&&!"".equals(siteMst.playStoreInstallUrl)) session['showMockTests']="true"

        return

    }

    def sendWelcomeEmail(String toEmail,String fullname, Integer siteId, String siteName, String clientName){
        if("<EMAIL>"!=toEmail&&(siteId.intValue()==1||siteId.intValue()==9||siteId.intValue()==12||siteId.intValue()==23||siteId.intValue()==24)) {
            if (userManagementService.validateEmail(toEmail, siteId)) {
                String view = "/creation/userCreationEmail"
                String fromEmail = "Wonderslate <<EMAIL>>"

                if (siteId.intValue() == 9) {
                    view = "/creation/userCreationEmailSage"
                    fromEmail = "SAGE Texts <<EMAIL>>"
                } else if (siteId.intValue() == 12) {
                    view = "/creation/userCreationEmailEvidya"
                    fromEmail = "SAGE eVidya <<EMAIL>>"
                } else if (siteId.intValue() == 23) {
                    view = "/creation/userCreationEmailEtexts"
                    fromEmail = "SAGE eTexts <<EMAIL>>"
                } else if (siteId.intValue() == 24) {
                    view = "/creation/userCreationEmailEbouquet"
                    fromEmail = "SAGE eBouquet <<EMAIL>>"
                }


                try {
                    mailService.sendMail {
                        async true
                        to toEmail
                        from fromEmail

                        subject "Welcome to " + clientName
                        body(view: view,
                                model: [name: fullname, account: toEmail, siteName: siteName, clientName: clientName])
                    }
                } catch (Exception e) {
                    println("Exception in sending welcome email to " + toEmail + " and exception is " + e.toString())
                }
            }
        }
    }

    def sendPwResetEmail(String toEmail, String fullname, Integer siteId, String url, String siteName, String clientName) {
        if ("<EMAIL>" != toEmail) {

            String view = "/creation/resetPasswordEmail"
            String fromEmail = "Wonderslate <<EMAIL>>"

            if (siteId.intValue() == 9) {
                view = "/creation/resetPasswordEmailSage"
                fromEmail = "SAGE Texts <<EMAIL>>"
            } else if (siteId.intValue() == 12) {
                view = "/creation/resetPasswordEmailEvidya"
                fromEmail = "SAGE eVidya <<EMAIL>>"
            } else if (siteId.intValue() == 20) {
                view = "/creation/resetPasswordEmail"
                fromEmail = "Wonderslate <<EMAIL>>"
            } else if (siteId.intValue() == 11) {
                view = "/creation/resetPasswordEmail"
                fromEmail = "EduGorilla <<EMAIL>>"
            } else if (siteId.intValue() == 23) {
                view = "/creation/resetPasswordEmailEtexts"
                fromEmail = "SAGE eTexts <<EMAIL>>"
            }else if (siteId.intValue() == 24) {
                view = "/creation/resetPasswordEmailEtexts"
                fromEmail =  "SAGE eBouquet <<EMAIL>>"
            }
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject "Password reset from " + clientName
                    body(view: view,
                            model: [name: fullname, account: toEmail, url: url, siteName: siteName, clientName: clientName])
                }
            } catch (Exception e) {
                println("Exception in sending forgot password to " + toEmail + " and exception is " + e.toString())
            }
        }
    }

    def sendRecomentBookEtexts(String coverImage,String siteName, String toEmail, String title, String id, String author, String userEmail){
       if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                    from "SAGE eTexts <<EMAIL>>"
                cc "<EMAIL>"
                subject "Book Recommendation"
                body(view: "/creation/etextsSuggestbookemail", model:[ siteName:siteName, title:title, id:id, author:author,  coverImage:coverImage, useremail: (userEmail==""||userEmail==null)?"":userEmail])
            }
        }
    }

    def sendRecomentBookEbouquet(String coverImage,String siteName, String toEmail, String title, String id, String author, String userEmail){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from  "SAGE eBouquet <<EMAIL>>"
                cc "<EMAIL>"
                subject "Book Recommendation"
                body(view: "/creation/ebouquetSuggestbookemail", model:[ siteName:siteName, title:title, id:id, author:author,  coverImage:coverImage, useremail: (userEmail==""||userEmail==null)?"":userEmail])
            }
        }
    }

    def sendEmailToUserEbouquet(String toEmail,String password, String siteName){
        password=encrypt(password)
        password=URLEncoder.encode(password,"UTF-8")
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from  (siteName=='ebouquet'?"SAGE eBouquet <<EMAIL>>":(siteName=='evidya'?"SAGE eVidya <<EMAIL>>":(siteName=='etexts'?"SAGE eTexts <<EMAIL>>":'')))
                subject (siteName=='ebouquet'?"Welcome to e-bouquet":(siteName=='evidya'?"Welcome to evidya":(siteName=='etexts'?"Welcome to eTexts":'')))
                body(view: "/creation/ebouquetUser", model:[name:toEmail, password:password, siteName: siteName])
            }
        }
    }

    def sendEmailToUserBouquetRetriever(String toEmail, String password, String siteName){
        password=encrypt(password)
        password=URLEncoder.encode(password,"UTF-8")
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from  "SAGE eBouquet <<EMAIL>>"
                subject "Welcome to e-bouquet"
                body(view: "/creation/ebouquetRetriever", model:[name:toEmail, password:password,siteName:siteName])
            }
        }
    }

    def sendEmailToUserWS(String toEmail,String password,siteName){
        try {
            if("<EMAIL>"!=toEmail) {
                String site=""
                if(siteName=="books"){
                    site="Welcome to Wonderslate"
                }else{
                    site="Welcome to LibWonder"
                }
                mailService.sendMail {
                    async true
                    to toEmail
                    from  "Wonderslate <<EMAIL>>"
                    subject site
                    body(view: "/creation/wsuser", model:[name:toEmail, password:password,siteName:siteName])
                }
            }
        }catch(Exception e){
            println("Exception in sending backend welcome email to "+toEmail+" and exception is "+e.toString())
        }
    }


    def  sendEmailToInstituteUser(String instituteName,String instituteLogo, Long instituteId, String toEmail,String password,siteName,instituteUrl){

        try {
            if("<EMAIL>"!=toEmail) {
                String subjectName=instituteName+" Important announcement"
                mailService.sendMail {
                    async true
                    to toEmail
                    from  "Wonderslate <<EMAIL>>"
                    subject subjectName
                    body(view: "/creation/instituteWelcomeEmail", model:[name:toEmail, password:password,instituteName:instituteName,instituteLogo:instituteLogo,instituteId:instituteId,instituteUrl:instituteUrl])
                }
            }
        }catch(Exception e){
            println("Exception in sending backend welcome email to "+toEmail+" and exception is "+e.toString())
        }

    }

    def sendContactUsEtexts(String name,String toEmail, String mobile, String comment,String copy,String siteName,Integer siteId){
        if (validateEmail(toEmail, siteId)) {
            if(copy.equals("on")) {
              mailService.sendMail {
                async true
                to "SAGE eTexts <<EMAIL>>"
                from "SAGE eTexts <<EMAIL>>"
                subject "Contact Information"
                body(view: "/creation/etextsSendContactUs", model: [name: name, email: toEmail, mobile: mobile, comment: comment,copy:copy,siteName:siteName])
            }
            mailService.sendMail {
                async true
                to toEmail
                from "SAGE eTexts <<EMAIL>>"
                subject "Contact Information"
                body(view: "/creation/etextsSendContactUs", model: [name: name, email: toEmail, mobile: mobile, comment: comment,siteName:siteName])
            }
        }else{

            mailService.sendMail {
                async true
                to "SAGE eTexts <<EMAIL>>"
                from "SAGE eTexts <<EMAIL>>"
                subject "Contact Information"
                body(view: "/creation/etextsSendContactUs", model: [name: name, email: toEmail, mobile: mobile, comment: comment,siteName:siteName])
            }

            }
        }

    }

    def sendContactUsEbouquet(String name,String toEmail, String mobile, String comment,String copy,String siteName,Integer siteId){
        if (validateEmail(toEmail, siteId)) {
             if(copy.equals("on")) {
               mailService.sendMail {
                async true
                to "SAGE eBouquet <<EMAIL>>"
                from "SAGE eBouquet <<EMAIL>>"
                subject "Contact Information"
                body(view: "/creation/etextsSendContactUs", model: [name: name, email: toEmail, mobile: mobile, comment: comment,copy:copy,siteName:siteName])
            }
            mailService.sendMail {
                async true
                to toEmail
                from "SAGE eBouquet <<EMAIL>>"
                subject "Contact Information"
                body(view: "/creation/etextsSendContactUs", model: [name: name, email: toEmail, mobile: mobile, comment: comment,siteName:siteName])
            }
        }else{

            mailService.sendMail {
                async true
                to "SAGE eBouquet <<EMAIL>>"
                from "SAGE eBouquet <<EMAIL>>"
                subject "Contact Information"
                body(view: "/creation/etextsSendContactUs", model: [name: name, email: toEmail, mobile: mobile, comment: comment,siteName:siteName])
            }

            }
        }

    }


    def sendRequestDemo(String name,String toEmail, String mobile, String comment,String companyName,String jobTitle){
        mailService.sendMail {
            async true
            to "SAGE eBouquet <<EMAIL>>"
            from "SAGE eBouquet <<EMAIL>>"
            subject "SAGE eBouquet: Request a demo"
            body(view: "/creation/ebouquetRequestDemo", model: [name: name, email: toEmail, mobile: mobile, comment: comment,jobTitle:jobTitle,companyName:companyName])
        }

    }


    def sendFeedbackEtexts(String search, String readingexperience, String mylibrary,
                           String notes, String products,String dashboard,String experience,String review
                           ,String expectation,String requirement,String improvement,String name,String email,String institutionName){

            mailService.sendMail {
                async true
                to "<EMAIL>"
                from "SAGE eTexts <<EMAIL>>"
                cc "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"
                subject "Feedback Details"
                body(view: "/creation/etextsSendFeedback", model:[ search:search, readingexperience:readingexperience,
                                                                       mylibrary:mylibrary, notes:notes,  products:products
                                                                       ,dashboard:dashboard,experience:experience,review:review,expectation:expectation,
                                                                       requirement:requirement,improvement:improvement,name:name,email:email,institutionName:institutionName])
            }
        }




    def sendRecommendBookEmail(String coverImage,String siteName, String toEmail, String title, String id, String author, String userEmail){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from "SAGE eVidya <<EMAIL>>"
                cc "<EMAIL>"
                subject "Book Recommendation"
                body(view: "/creation/evidyaSuggestbookemail", model:[ siteName:siteName, title:title, id:id, author:author,  coverImage:coverImage, useremail: (userEmail==""||userEmail==null)?"":userEmail])
            }
        }
    }

    def sendRecomentBookEmailWk(String coverImage,String siteName, String toEmail, String title, String id, String author){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from "Wonderslate <<EMAIL>>"
                cc "<EMAIL>"
                subject "Book Recommendation"
                body(view: "/creation/wolterskluwerSuggestbookemail", model:[ siteName:siteName, title:title, id:id, author:author,  coverImage:coverImage])
            }
        }
    }

    def sendOtpEmail(String name, String toEmail, Integer siteId, String otp, String siteName, String clientName, String entryController, String logo){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                SiteMst siteMst = SiteMst.findById(siteId)
                from siteMst.fromEmail?siteMst.fromEmail:"Wonderslate <<EMAIL>>"
                String   view = "/creation/otpRequestEmail"
                async true
                to toEmail

                if(siteId==9) {
                    from "SAGE Texts <<EMAIL>>"
                    view = "/creation/otpRequestEmailSage"
                } else if(siteId==12) {
                    from "SAGE eVidya <<EMAIL>>"
                }else if("ebouquet".equals(""+siteName)) {
                    from "SAGE eBouquet <<EMAIL>>"
                    view = "/creation/otpRequestEmailEbouquet"
                }else if("libwonder".equals(""+siteName)) {
                    from "Wonderslate <<EMAIL>>"
                    view = "/creation/otpRequestEmailLibwonder"
                }
                else if("arihant".equals(""+siteName)) {
                    from "Arihant <<EMAIL>>"
                    view = "/creation/otpRequestEmailArihant"
                }

                if (siteId==66){
                    subject "Your OTP to register with Wonderslate"
                }else{
                    subject "Your OTP to register with "+clientName
                }
                body(view: view, model:[name:name, siteId:siteId, otp:otp, siteName:siteName, clientName:clientName, entryController:entryController, logo: logo])
            }
        }
    }

    def sendUserFormEmail(String name, String email, String query, String subject1){
        if("<EMAIL>"!=email) {
            mailService.sendMail {
                async true

                if(subject1.endsWith("Ask the author")) {
                    to "SAGE Texts <<EMAIL>>"
                } else {
                    to "<EMAIL>"
                }

                from "SAGE Texts <<EMAIL>>"
                subject subject1
                text "Name: "+name+"\n\nEmail: "+email+"\n\nQuery: "+query
            }
        }
    }


    def invoiceEmail(SiteMst siteMst, SiteDtl siteDtl, User user, PurchaseOrder poTemp,String str1,request,entryController,deliveryCosts,Double totalAmount,List purchaseOrdersList,Integer poNumber){
        PurchaseOrder purchaseOrder = dataProviderService.getPurchaseOrder(poNumber)
        DateFormat df = new SimpleDateFormat("dd.MM.yyyy / hh.mm aa")
        String siteName = siteMst.siteName;
        Integer siteId = siteMst.id;
        String mobile = user.mobile;
        String toEmail = user.email;
        String fullname = user.name;
        String bccEmail = grailsApplication.config.grails.po.invoice.bcc;
        String poNo = purchaseOrder.sequencePo != null ? "UT" + purchaseOrder.sequencePo + "" : purchaseOrder.id + "";
        String paymentId = poTemp.paymentId;
        String amount = poTemp.amount + "";
        Long id = null;
        String title = "";
        String coverImage = "";
        String method = poTemp.poMethod;
        String amtStr = str1;
        String createdAt = df.format(utilService.convertDate(poTemp.dateCreated, "UTC", "IST"));
        String state = user.state;
        String clientName = siteMst.clientName;
        String ipAddress = utilService.getIPAddressOfClient(request);
        Integer gstPercentage = poTemp.gstPercentage;
        String discountAmount = poTemp.discountAmount + "";
        List shoppingCartOrdersDtl = purchaseOrdersList;
        String logo = siteDtl!=null?siteDtl.logo:null;

        String addressLine1 = "No 401 Fourth Floor, Sapthagiri Apartments,";
        String addressLine2 = "No. 30, 10th Cross, 15th Main Rd, Raj Mahal Vilas Extension,Sadashivanagar, Bengaluru, 560080";
        String gstNumber = "29AABCW7019D1ZC";
        String emailAddress = "<EMAIL>";
        String websiteLink = siteDtl!=null?siteDtl.websiteLink:"www.wonderslate.com";
        String jurisdictionPlace = "Bengaluru";
        String jurisdictionState = "Karnataka";
        String companyName = siteDtl!=null?siteDtl.companyName:null;

        boolean copyPublisher = false
        String publisherEmailId = ""
           BooksMst booksMst = dataProviderService.getBooksMst(purchaseOrder.itemCode)
            if(booksMst!=null){
                if(booksMst.publisherId!=null){
                    Publishers publisher = Publishers.findById(booksMst.publisherId)
                    if(publisher!=null&&publisher.email!=null&&publisher.email!=""){
                        publisherEmailId = publisher.email
                        copyPublisher = true
                    }
                }
            }


        if("<EMAIL>"!=toEmail) {
            String   view = "/creation/wsinvoice"
            String    fromEmail = "Wonderslate <<EMAIL>>"
            String privacyLink =""
            String termsAndCondition=""

           if(siteId.intValue()==37){
                view = "/creation/radianBooksInvoice"
                fromEmail = "Radian Books <<EMAIL>>"
            }else if(siteMst.prepjoySite=="true"){
                 view = "/creation/prepjoyInvoice"
                 fromEmail = "Wonderslate <<EMAIL>>"
             }else if(siteId.intValue()==1){
                 view = "/creation/wsinvoice"
                 fromEmail = "Wonderslate <<EMAIL>>"
             }else if("privatelabel".equals(""+entryController)){
                 privacyLink = siteDtl!=null?siteDtl.privacyPolicy:null
                 termsAndCondition = siteDtl!=null?siteDtl.termsCondition:null
                 view = "/creation/privateLabelInvoice"
                 fromEmail = siteMst.fromEmail
             }else{
                 println("****** site name is "+siteMst.siteName)
                 view = "/creation/"+siteMst.siteName+"Invoice"
                 fromEmail = siteMst.fromEmail
             }
           String bookExpiry=null
            if(siteId.intValue()==66){
                if(redisService.("wileyCollectionBook")==null) wsshopService.getWileyCollectionMainBook()
                def wileyCollectionBookId = redisService.("wileyCollectionBook")
                 booksMst = dataProviderService.getBooksMst(new Integer(wileyCollectionBookId))
                if (booksMst.bookExpiry != null && !"".equals(booksMst.bookExpiry)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                    bookExpiry = sdf.format(booksMst.bookExpiry);
                }
            }
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject "Invoice " + poNo
                    body(view: view,
                            model: [name      : fullname, mobile: mobile, account: toEmail, poNo: poNo, paymentId: paymentId, amount: amount, id: id, title: title,
                                    coverImage: coverImage, method: method, amtStr: amtStr, createdAt: createdAt, state: state,
                                    siteName  : siteName, clientName: clientName, ipAddress: ipAddress,gstPercentage:gstPercentage,discountAmount:discountAmount,shoppingCartOrdersDtl:shoppingCartOrdersDtl,totalAmount:totalAmount,
                                    siteId: siteId, entryController: entryController, logo: logo, addressLine1: addressLine1, addressLine2: addressLine2, gstNumber: gstNumber, emailAddress: emailAddress, websiteLink: websiteLink,
                                    jurisdictionPlace: jurisdictionPlace, jurisdictionState: jurisdictionState, companyName: companyName,deliveryCosts:deliveryCosts,privacyLink:privacyLink,termsCondition:termsAndCondition,bookExpiry:bookExpiry])
                    if(copyPublisher) {
                        bcc publisherEmailId
                    }
                }
                 }catch(Exception e){
                println("Exception in sending invoiceEmail to "+toEmail+" and exception is "+e.toString())
            }
        }
    }


    def invoiceEmailOld(String siteName,Integer siteId, String mobile, String toEmail, String fullname, String bccEmail, String poNo,
                     String paymentId, String amount, Long id, String title, String coverImage, String publisher,
                     String method, String amtStr, String createdAt, String state, String clientName, String ipAddress,Integer gstPercentage,String discountAmount,String price){
        if("<EMAIL>"!=toEmail) {
            SiteMst siteMst = SiteMst.findById(siteId)
            String   view = "/creation/wsinvoice"
            String    fromEmail = "Wonderslate <<EMAIL>>"

            if(siteId.intValue()==21){
                view = "/creation/wsinvoice"
                fromEmail = "Wonderslate <<EMAIL>>"
            }
            else if(siteId.intValue()==21){
                view = "/creation/winnersInvoice"
                fromEmail = "The Winners Institute <<EMAIL>>"
            }else if(siteId.intValue()==34){
                view = "/creation/jbclassInvoice"
                fromEmail = "JB Classes <<EMAIL>>"
            }else if(siteId.intValue()==37){
                 view = "/creation/radianBooksInvoice"
                fromEmail = "Radian Books <<EMAIL>>"
            }else if((siteId.intValue()==3)){
                view = "/creation/arihantInvoice"
                fromEmail = "Arihant <<EMAIL>>"
            }else if(siteMst.prepjoySite=="true"){
                view = "/creation/prepjoyInvoice"
                fromEmail = "Wonderslate <<EMAIL>>"
            }else{
                view = "/creation/"+siteMst.siteName+"Invoice"
                fromEmail = siteMst.fromEmail
            }
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject "Invoice " + poNo
                    body(view: view,
                            model: [name      : fullname, mobile: mobile, account: toEmail, poNo: poNo, paymentId: paymentId, amount: amount, id: id, title: title,
                                    coverImage: coverImage, publisher: publisher, method: method, amtStr: amtStr, createdAt: createdAt, state: state,
                                    siteName  : siteName, clientName: clientName, ipAddress: ipAddress,gstPercentage:gstPercentage,discountAmount:discountAmount,price:price])
                }
            }catch(Exception e){
                println("Exception in sending invoiceEmailold to "+toEmail+" and exception is "+e.toString())
            }
        }
    }

    def userBookPurchase(String amount, String toEmail, String fullname,String paymentId,String loginId,String cartBooks){
        if("<EMAIL>"!=toEmail&&"<EMAIL>"!=toEmail) {
            String   view = "/creation/userBookPurchaseEmail"
            String    fromEmail = "Wonderslate <<EMAIL>>"
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject "Successful purchase of your Books"
                    body(view: view,
                            model: [name      : fullname,  account: toEmail, paymentId: paymentId, amount: amount ,loginId:loginId,cartBooks:cartBooks])
                }
            }catch(Exception e){
                println("Exception in sending userBookPurchase email to "+toEmail+" and exception is "+e.toString())
            }
        }

    }

    boolean hasRole(String givenRole) {
        boolean hasrole = false
        def roles = springSecurityService.getPrincipal().getAuthorities()

        for(def role in roles){
            if(role.getAuthority() == givenRole){
                hasrole = true
                break
			}
        }

        return  hasrole
    }

    boolean canEdit(String loggedInUser,String itemAuthor,String resType) {
        //this is very basic check.. things will evolve from here.
        if("Multiple Choice Questions"==resType || "Notes"==resType) {
            return itemAuthor==loggedInUser || "wonder"==loggedInUser
        }
        else return false
    }

    boolean canShare(String loggedInUser,String itemAuthor,String sharing) {
        //this is very basic check.. things will evolve from here.
        if("public"==sharing) return true
        else{
            return itemAuthor==loggedInUser
        }
    }

    boolean canPublish(String loggedInUser,String itemAuthor,String sharing) {
        //this is very basic check.. things will evolve from here.
        if("public"==sharing) return false
        else{
            return itemAuthor==loggedInUser
        }
    }

    boolean canDelete(String loggedInUser,String itemAuthor) {
        //this is very basic check.. things will evolve from here.
        return itemAuthor.equals(loggedInUser) || "wonder".equals(loggedInUser)
    }

    boolean canSeeGroup(String id){
        return GroupsDtl.findByGroupIdAndUsernameAndRoleInList(new Integer(id), springSecurityService.currentUser.username, [new Integer(UserManagementService.GROUP_MEMBER), new Integer(UserManagementService.GROUP_ADMIN), new Integer(UserManagementService.GROUP_CREATOR)]) != null
    }

    boolean isGroupAdmin(String id){
        return GroupsDtl.findByGroupIdAndUsernameAndRoleInList(new Integer(id), springSecurityService.currentUser.username, [new Integer(UserManagementService.GROUP_ADMIN), new Integer(UserManagementService.GROUP_CREATOR)]) != null
    }

    boolean hasAlreadyActed(String loggedInUser,String id,String action){
        if(ResourceView.findByUsernameAndResourceDtlIdAndAction(loggedInUser,new Integer(id),action)) return true
        else return false
    }

    int numberOfFriends(username){
        String sql = "select um.name from User um,Friends f where "+
                "((um.username = f.friend1 and f.friend2='"+username+"')\n" +
                " or (um.username = f.friend2 and f.friend1='"+username+"'))\n" +
                " and f.status="+FRIEND_ACCEPTED
        List results = User.executeQuery(sql)
        return results.size()
    }

    int noOfGroups(username){
        def sql = "select g.name,g.profilepic, g.id from Groups g, GroupsDtl gd where g.id=gd.groupId and gd.username='"+username+"' "+
                " and gd.role in ("+GROUP_MEMBER +","+GROUP_CREATOR+","+GROUP_ADMIN+")"

        List results = Groups.executeQuery(sql)
        return results.size()
    }

    boolean canSeeResource(String username, String createdBy,Long resourceDtlId,String sharing){
        if(sharing.equals("deleted")) return false
        else if(username.equals(createdBy)) return true
        else{
          def sql = "select gd.username,rgd.resourceId from ResourceGroupDtl rgd, " +
                  "GroupsDtl gd where gd.groupId=rgd.groupId and" +
                  "   gd.username ='"+username+"' and rgd.resourceId="+resourceDtlId
            List items = ResourceDtl.executeQuery(sql)
            if(items.size()>0) return true
        }

        return false
    }

    @Transactional
	void addPoints(String username, String type, String activity, String subActivity) {
		def pm = PointsMst.findByActivityAndType(activity,type)

		if(pm!=null) {
			PointsDtl pd = new PointsDtl()
			pd.username=username
			pd.date = new Date()
			pd.type = type
			pd.activity = activity
			pd.points = pm.points
			pd.subActivity = subActivity
			pd.save(failOnError: true)
		}
	}

    @Transactional
	void addOncePoints(String username, String type, String activity, String subActivity) {
		def pm = PointsMst.findByActivityAndType(activity,type)
		def pdt = PointsDtl.findByTypeAndActivityAndSubActivityAndUsername(type,activity,subActivity,username)
		if(pm!=null && pdt==null) {
			PointsDtl pd = new PointsDtl()
			pd.username=username
			pd.date = new Date()
			pd.type = type
			pd.activity = activity
			pd.points = pm.points
			pd.subActivity = subActivity
			pd.save(failOnError: true)
		}
	}

    @Transactional
	void updatePoints(String username, String type, String activity) {
		def pm = PointsMst.findByActivityAndType(activity,type)

		if(pm!=null) {
			if(activity=="INVITATION") {
				def pd = PointsDtl.findByTypeAndActivityAndSubActivityAndPoints(type,activity,username,0)

				if(pd!=null) {
					pd.points = pm.points
					pd.save(failOnError: true,flush: true)
				}
			}
		}
	}

    @Transactional
	void addUpdatePoints(String username, String type, String activity, String subActivity, Integer points) {
		if(activity=="PLAY" && type=="LP") {
			def pd = PointsDtl.findByTypeAndActivityAndSubActivityAndUsername(type,activity,subActivity,username)

			if(pd!=null && points>pd.points) {
				pd.points = points
				pd.save(failOnError: true,flush: true)
			} else {
				PointsDtl pd1 = new PointsDtl()
				pd1.username=username
				pd1.date = new Date()
				pd1.type = type
				pd1.activity = activity
				pd1.points = points
				pd1.subActivity = subActivity
				pd1.save(failOnError: true)
			}
		} else if(activity=="ADDQUIZ" && type=="CP") {
			def pd = PointsDtl.findByTypeAndActivityAndSubActivityAndUsername(type,activity,subActivity,username)

			if(pd!=null) {
				pd.points = points
				pd.save(failOnError: true,flush: true)
			} else {
				PointsDtl pd1 = new PointsDtl()
				pd1.username=username
				pd1.date = new Date()
				pd1.type = type
				pd1.activity = activity
				pd1.points = points
				pd1.subActivity = subActivity
				pd1.save(failOnError: true)
			}
		}
	}

    boolean  canSeeChapter(Long chapterId){

        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
        if("true"==""+chaptersMst.previewChapter) return true
        else{
            BooksMst booksMst =  dataProviderService.getBooksMst(chaptersMst.bookId)
            if(booksMst.price != null && booksMst.price.doubleValue() == 0&&"published".equals(booksMst.status)) return true
            else if(springSecurityService.currentUser!=null){
                String username = springSecurityService.currentUser.username
                if (redisService.("userShelfBooks_" + username) == null  || redisService.("userShelfBooks_" + username) == "null") wsLibraryCacheService.userShelfBooks(username)

                boolean hasBookAccess = false
                def userSelfCache=redisService.("userShelfBooks_"+springSecurityService.currentUser.username+"_"+"bookIds")
                List booksIds = userSelfCache!=null?Arrays.asList(redisService.("userShelfBooks_"+springSecurityService.currentUser.username+"_"+"bookIds").split("\\s*,\\s*")):null
                if(booksIds.indexOf(""+chaptersMst.bookId)>-1){
                    hasBookAccess=true;
                }
                return hasBookAccess
            } else return false
        }
    }

    String userBatches(){
        List userBatches  = BatchUserDtl.findAllByUsername(springSecurityService.currentUser.username)
        String[] userBatchesArray = new String[userBatches.size()]
        for(int i=0;i<userBatches.size();i++){
            userBatchesArray[i] = userBatches.get(i).batchId
        }
        if(userBatches.size()>0) return String.join(",", userBatchesArray)
        else return ""
    }

    @Transactional
    void updateChapterAccess(Long bookId,Long chapterId){
        asyncLogsService.updateChapterAccess(springSecurityService.currentUser.username,new Long(0),chapterId)
    }

    @Transactional
    void updateChapterAccess(Long bookId,Long chapterId,Long resourceId){
        asyncLogsService.updateChapterAccess(springSecurityService.currentUser.username,resourceId,chapterId)
    }

    @Transactional
    List getLastReadBooks(){
        List latestReadBooks = []
        List chapterAccess = ChapterAccess.findAllByUsername(springSecurityService.currentUser.username, [sort: "lastUpdated",order: "desc", max:5])

        if(chapterAccess!=null && chapterAccess.size()>0) {
            chapterAccess.each{ chapter->
                BooksMst booksMst = dataProviderService.getBooksMst(chapter.bookId)
                if("test"!=booksMst.bookType) {
                    BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chapter.bookId)
                    latestReadBooks << [bookId: booksMst.id, title: booksMst.title, coverImage: booksMst.coverImage,
                            chapterId: chapter.chapterId, resourceId: chapter.resourceId, siteId: booksMst.siteId,
                            level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                            grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",
                            lastUpdated:chapter.lastUpdated]
                }
            }
        }

        return latestReadBooks
    }

    boolean isInstructor(){
        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndInstructor(springSecurityService.currentUser.username,"true")
        return batchUserDtl != null
    }

    boolean isInstructorForInstitute(Integer instituteId){
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndNameAndStatus(instituteId,"Default","active")
        BatchUserDtl batchUserDtl
        if(courseBatchesDtl!=null)   batchUserDtl = BatchUserDtl.findByUsernameAndInstructorAndBatchId(springSecurityService.currentUser.username,"true",courseBatchesDtl.id)
        return batchUserDtl != null
    }

    def sendMmail(String toEmail, String fromEmail, String fullname, String subjectStr, String message){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true

                to toEmail

                if(fromEmail!=null && fromEmail!="")
                    from fromEmail

                subject subjectStr

                if(fullname!=null && fullname!="")
                    text "Dear "+fullname+", "+message
                else
                    text message
            }
        }
    }


    def addPackageBooksToUser(String username,BooksMst booksMst){
        //changed this logic as way of showing package books is changed
        redisService.(username+"_"+"booksList")
    }



    @Transactional
     boolean validateEmail(String email, Integer siteId) {
        //additional checks to avoid null or empty value checks
        if(email==null || email.trim()=="") return false

        //added below to avoid unnecessary email check for invalid formats
        if(!EmailValidator.getInstance().isValid(email)) return false

        EmailMst em = EmailMst.findByEmail(email)

        if(em!=null) {
            //special to check to see if an email is used for more than one site and if yes, then insert site specific entry
            EmailMst em1 = EmailMst.findByEmailAndSiteId(email,siteId)
            if(em1==null) {
                em1 = new EmailMst()
                em1.email = email
                em1.validated = em.validated;
                em1.siteId = siteId
                em1.dateCreated = new Date()
                em1.save(failOnError: true,flush: true)
            }

            if(em.validated=="true") return true
        }

        if(!System.properties['os.name'].toLowerCase().contains('windows') &&
            !System.properties['os.name'].toLowerCase().contains('mac') && em==null) {
            try {
                //def url = "https://api.millionverifier.com/api/v3/credits?api=Hopxo283ooFRI1wIUXFcNgaw8"
                def url = "https://api.millionverifier.com/api/v3/?api=Hopxo283ooFRI1wIUXFcNgaw8&email="+email+"&timeout=10"

                def scriptCom = ['curl', '-o', '-',"${url}"]
                def proc = scriptCom.execute()

                //kill after 10 seconds if still running
                proc.waitForOrKill(10000)

                def data = proc.in.text.replace(System.getProperty("line.separator"),"")

                if(proc.exitValue() != 0) {
                    println "[[return code: ${proc.exitValue()}]]"
                    println "[[stderr: ${proc.err.text}]]"
                }

                def respJson = new JsonSlurper().parseText(data)

                em = new EmailMst()
                em.email = email
                em.validated = respJson.result=="ok"?"true":"false";
                em.siteId = siteId
                em.dateCreated = new Date()
                em.save(failOnError: true,flush: true)

                return respJson.result=="ok"
            } catch(Exception e){
                return false
            }
        } else
            return false
    }


    @Transactional
    boolean validateEmail(String email, Integer siteId, boolean validate) {

        //additional checks to avoid null or empty value checks
        if(email==null || email.trim()=="") return false

        //added below to avoid unnecessary email check for invalid formats
        if(!EmailValidator.getInstance().isValid(email)) return false

        EmailMst em = EmailMst.findByEmail(email)

        if (em!=null) {
            //special to check to see if an email is used for more than one site and if yes, then insert site specific entry
            EmailMst em1 = EmailMst.findByEmailAndSiteId(email,siteId)
            if(em1==null) {
                em1 = new EmailMst()
                em1.email = email
                em1.validated = em.validated;
                em1.siteId = siteId
                em1.dateCreated = new Date()
                em1.save(failOnError: true,flush: true)
            }

            return em.validated=="true"
        } else if (!validate) {
            println("oko tri")
            em = new EmailMst()
            em.email = email
            em.validated = "true";
            em.siteId = siteId
            em.dateCreated = new Date()
            em.save(failOnError: true,flush: true)
            return true;

        } else {
            return validateEmail(email, siteId)
        }
    }

    def sendInquiryEmail(String toEmail, String name, String emailId){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from "Wonderslate <<EMAIL>>"
                subject "Inquiry from EduWonder"
                text "Name: "+name+"\n\nEmail: "+emailId
            }
        }
    }

    @Transactional
    def sendAffiliationEmail(String name,String toEmail, String mobile, String state){
        mailService.sendMail {
            async true
            to "Wonderslate <<EMAIL>>"
            from  "Wonderslate <<EMAIL>>"
            subject "Request for Affiliation Program"
            body(view: "/creation/affiliationEmail", model: [name: name, email: toEmail, mobile: mobile, state: state])
        }

    }

    def registerUserLogin(username,sessionId){
        User user = dataProviderService.getUserMst(username)
        int simulAllowed = 1;
        List userSessions
        if(user.maxLogins!=null) simulAllowed = user.maxLogins.intValue();
        if(redisService.("session_"+username)==null){
            userSessions = [sessionId]

        }else{
            userSessions = new JsonSlurper().parseText(redisService.("session_"+username))
            //if session id is already present for this user, then do nothing.
            if(!userSessions.contains(sessionId)) {
                if (userSessions != null && userSessions.size() >= simulAllowed&&!username.startsWith("80_")) {
                    userSessions.remove(0)
                }
                userSessions.push(sessionId)
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(userSessions,new TypeToken<List>() {}.getType())
        redisService.("session_"+username) = element
        return
    }
    def isValidSession(username,sessionId){
        def sessionValid=true
        if(username.indexOf("1_cookie_")==-1&&redisService.("session_"+username)!=null){
            List userSessions = new JsonSlurper().parseText(redisService.("session_"+username))
            if(!userSessions.contains(sessionId)) sessionValid = false
        }
        return sessionValid

    }
    def sendContentModerationEmail(String toEmail,String fullname, String message, String eSubject, String siteName, String clientName, String resLink, String resType){
        if("<EMAIL>"!=toEmail && EmailValidator.getInstance().isValid(toEmail)) {
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from "Wonderslate <<EMAIL>>"
                    subject eSubject
                    body(view: "/creation/contentModerationEmailWS",
                            model: [name: fullname, account: toEmail, siteName: siteName, clientName: clientName,message: message, resLink:resLink, resType: resType])
                }
            }catch (Exception e) {
                println("Exception in sending content moderation email to " + toEmail + " and exception is " + e.toString())
            }
        }
    }
        def sendRecommendBookEmailEvidyaUser(String toEmail, String name, String title){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from "SAGE eVidya <<EMAIL>>"
                cc "<EMAIL>"
                subject "Book Recommendation"
                body(view: "/creation/evidyaSuggestbookemailuser", model:[name: name, title: title])
            }
        }
    }
        def sendRecomentBookEtextsUser(String toEmail, String name,String title){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from "SAGE eTexts <<EMAIL>>"
                cc "<EMAIL>"
                subject "Book Recommendation"
                body(view: "/creation/etextsSuggestbookemailuser", model:[name: name, title: title])
            }
        }
    }
        def sendRecommendBookEbouquetUser(String toEmail, String name, String title){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from  "SAGE eBouquet <<EMAIL>>"
                cc "<EMAIL>"
                subject "Book Recommendation"
                body(view: "/creation/ebouquetSuggestbookemailuser", model:[name: name, title: title])
            }
        }
    }
    def sendEnquiryFormEmail(String toEmail, String name){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from  "Wonderslate <<EMAIL>>"
                subject "Greetings from Wonderslate!"
                body(view: "/creation/enquiryFormEmail", model:[name: name])
            }
        }
    }

    def sendInstituteUserEmail(String toEmail, String name, String message, bookId,siteName){
        if("<EMAIL>"!=toEmail) {
            String site=""
            String title = "-"
            String urlTag = "-"
            if(bookId!=null && bookId!="" && bookId!='false'){
                title=BooksMst.findById(new Long(bookId)).title.replace(' ','-')
                urlTag=BooksTagDtl.findByBookId(new Long(bookId)).syllabus.replace(' ', '-')
            }
            if(siteName=="books"){
                site="Greetings from Wonderslate!"
            }else{
                site="Greetings from LibWonder!"
            }
            mailService.sendMail {
                async true
                to toEmail
                from  "Wonderslate <<EMAIL>>"
                subject site
                body(view: "/creation/instituteUserEmail", model:[user: name, message: message, bookId: bookId,siteName:siteName, title: title, urlTag: urlTag])
            }
        }
    }

    def isInstitutePublisher(){
        boolean isInsitutePublisher = false
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if(user.publisherId!=null){
            Publishers publishers = dataProviderService.getPublisher(user.publisherId)
            if("systemCreated".equals(publishers.publisherType)) isInsitutePublisher = true
        }
        return  isInsitutePublisher
    }

    def getInstituteId(){
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        def instituteId = -1
        if(user.publisherId!=null){
            InstituteMst instituteMst = InstituteMst.findByPublisherId(user.publisherId)
            if(instituteMst!=null){
                instituteId = instituteMst.id.intValue()

            }
        }
        return instituteId
    }



    // the checks are in the order of making less db calls. Basic test // bookId is Long
    def hasAccessToBook(bookId, session,request,addToLibrary,response){
        Integer siteId = utilService.getSiteId(request,session)
        boolean hasAccess = false
        BooksMst booksMst = dataProviderService.getBooksMst(bookId)
        if(booksMst!=null){
            //if published free book, return true
            BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(""+booksMst.id),"eBook")
            if(bookPriceDtl!=null&&bookPriceDtl.sellPrice != null && bookPriceDtl.sellPrice.doubleValue() == 0&&"published".equals(booksMst.status)) {
                hasAccess = true
                if(addToLibrary&&springSecurityService.currentUser!=null) {
                    BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(booksMst.id, springSecurityService.currentUser.username)
                    if (booksPermission == null) {
                        booksPermission = new BooksPermission()
                        booksPermission.bookId = booksMst.id
                        booksPermission.username = springSecurityService.currentUser.username
                        booksPermission.poType = 'ADDEDFORFREE'
                        booksPermission.save(failOnError: true, flush: true)
                        dataProviderService.getBooksListForUser()
                        if (siteId == 1 || siteId == 3)
                            redisService.("userShelfBooks_" + springSecurityService.currentUser.username) = null
                    }
                }
            }
            if(!hasAccess){
                //check for remote library access via secret key
                if(doesRemoteAddressHasAccessForBook(booksMst.id,request,siteId,response,session)){
                    hasAccess = true
                }
                //check for ip library access
               else if(doesIPAddressHasAccessForBook(booksMst.id,request,siteId)){
                    hasAccess = true
                }
                else if(springSecurityService.currentUser!=null){
                    BooksPermission booksPermission
                   String ipAddress = utilService.getIPAddressOfClient(request)
                    List batchIdList = getInstitutesForUser(siteId,ipAddress)
                    if(batchIdList != null && batchIdList.size() <= 1) {
                        long bId = batchIdList.size() == 1? batchIdList.get(0).batchId:0
                        booksPermission= BooksPermission.findByBookIdAndUsernameAndBatchId(new Long(bookId),springSecurityService.currentUser.username,new Long(bId))
                    }else if(batchIdList != null || batchIdList.size() > 1){
                        for(int i=0;i<batchIdList.size();i++){
                            booksPermission= BooksPermission.findByBookIdAndUsernameAndBatchId(new Long(bookId),springSecurityService.currentUser.username,new Long(batchIdList[i].batchId))
                            if(booksPermission!=null)break;
                        }
                    }
                    if(booksPermission!=null){
                        hasAccess = true
                    }
                    //for institute publishers, only their books should be shown by default

                    else {
                        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                        //see if the book is in user's library
                        if (redisService.("userShelfBooks_" + springSecurityService.currentUser.username) == null || redisService.("userShelfBooks_" + springSecurityService.currentUser.username)=="null") {
                            wsLibraryCacheService.userShelfBooks(springSecurityService.currentUser.username)
                        }
                        def userSelfCache = redisService.("userShelfBooks_" + springSecurityService.currentUser.username + "_" + "bookIds")
                        List booksIds = userSelfCache != null ? Arrays.asList(redisService.("userShelfBooks_" + springSecurityService.currentUser.username + "_" + "bookIds").split("\\s*,\\s*")) : null
                        if (booksIds != null && booksIds.indexOf("" + booksMst.id) > -1) hasAccess = true
                        else if (userManagementService.isInstitutePublisher()) {
                            if (springSecurityService.currentUser.username.equals(booksMst.createdBy)) hasAccess = true
                        }
                        // if wonderslate content creator or publisher of the book
                        else if (("" + user.publisherId).equals("" + booksMst.publisherId) || (user.authorities.any {
                            it.authority == "ROLE_WS_CONTENT_CREATOR"
                        } && user.publisherId == null)||(user.authorities.any {
                            it.authority == "ROLE_WS_SALES_TEAM"
                        } && user.publisherId == null)) {
                            hasAccess = true
                        }

                    }
                }
            }
        }
        return  hasAccess

    }

    //check whether the user has library access

    def hasLibraryAccessToBook(bookId,addToLibrary){
        boolean hasAccess = false
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if(springSecurityService.currentUser!=null) {
        //check whether the user belongs to any institute
         dataProviderService.getInstitutesForUser()

        List institutes = new JsonSlurper().parseText(redisService.("institutesList_"+springSecurityService.currentUser.username))
        if(institutes!=null && institutes.size()>0){
            // Collect all batch IDs for the user's institutes
            List<Long> batchIds = []
            for(int i=0;i<institutes.size();i++){
                Map institute = institutes[i]
                batchIds.add(institute.batchId as Long)
            }

            // Get all book IDs available to these batches (including package books)
            Set<Long> availableBookIds = getAllBookIdsForBatches(batchIds)

            // Check if the requested book ID is in the available books
            if(availableBookIds.contains(bookId as Long)){
                hasAccess = true
            }
        }
     }
       return  hasAccess
    }

    private Set<Long> getAllBookIdsForBatches(List<Long> batchIds) {
        Set<Long> allBookIds = new HashSet<>()

        if(batchIds.isEmpty()) {
            return allBookIds
        }

        String batchIdsStr = batchIds.join(',')

        // SQL to get both direct books and package books
        String sql = """
            SELECT DISTINCT book_id FROM (
                SELECT bbd.book_id
                FROM wsuser.books_batch_dtl bbd
                WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
                  AND bbd.batch_id IN (${batchIdsStr})
                UNION
                SELECT bm1.id as book_id
                FROM wsuser.books_batch_dtl bbd
                JOIN wsshop.books_mst bm ON bbd.book_id = bm.id
                JOIN wsshop.books_mst bm1 ON bm.package_book_ids IS NOT NULL
                    AND FIND_IN_SET(bm1.id, bm.package_book_ids) != 0
                WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
                  AND bbd.batch_id IN (${batchIdsStr})
                AND bm1.site_id = bm.site_id
            ) AS combined_books
        """

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource)
        def results = sql2.rows(sql)

        results.each { row ->
            allBookIds.add(row.book_id as Long)
        }

        return allBookIds
    }

  def addInstituteBookToUser(Long batchId,Long bookId){
      Calendar c = Calendar.getInstance()
      def expiry=true
     Integer checkoutDays =
              (InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).checkOutDays != ""
                      && InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).checkOutDays != null&&
                      !"null".equals(InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).checkOutDays))
                      ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).checkOutDays)
                      : null
      if(checkoutDays!=null && checkoutDays!="") {
          c.add(Calendar.DATE, checkoutDays)
      }else{
          c.add(Calendar.DATE, 14)
      }

      BooksPermission booksPermission = new BooksPermission(
              bookId: bookId,
              username: springSecurityService.currentUser.username,
              poType: 'ADDEDFROMINSTITUTE',
              batchId:batchId,
              addedBy: springSecurityService.currentUser.username,
              expiryDate: expiry==true?c.getTime():null
      )
      booksPermission.save(failOnError: true, flush: true)
      BooksPermissionCopy booksPermissionCopy = new BooksPermissionCopy(
              bookId: bookId,
              username: springSecurityService.currentUser.username,
              poType: 'ADDEDFROMINSTITUTE',
              batchId: batchId,
              addedBy: springSecurityService.currentUser.username,
              bpId: booksPermission.id,
              expiryDate: expiry==true?c.getTime():null
      )
      booksPermissionCopy.save(failOnError: true, flush: true)
      BooksQueueDtl booksQueueDtl=BooksQueueDtl.findByBookIdAndBatchIdAndUsername(bookId,batchId,springSecurityService.currentUser.username)
      if(booksQueueDtl!=null) booksQueueDtl.delete()
      redisService.("userMyLibraryInstituteBooks_"+batchId) = null
      redisService.("lastReadBooksIns_"+batchId+"_"+ springSecurityService.currentUser.username)=null
  }

  def canSeeResourceCheck(ResourceDtl resourceDtl,session,request,response) {
      boolean hasAccess = false
      //simple checks first
      if (springSecurityService.currentUser != null && resourceDtl.createdBy.equals(springSecurityService.currentUser.username)) {
          hasAccess = true
      }else if("createdbyuser".equals(resourceDtl.sharing)&&"public".equals(resourceDtl.privacyLevel)){
          hasAccess = true
      }else if(resourceDtl.chapterId!=null){
          ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
          if("true".equals("" + chaptersMst.previewChapter)){
              hasAccess = true
          }
          else if(chaptersMst.bookId!=null){
              Integer siteId = utilService.getSiteId(request,session)
              if(hasAccessToBook(chaptersMst.bookId,session,request,false,response)) hasAccess = true
              else if(hasLibraryAccessToBook(chaptersMst.bookId,false)) hasAccess = true
              else if(doesIPAddressHasAccessForBook(chaptersMst.bookId,request,siteId)) hasAccess = true
          }
      }else if(resourceDtl.chapterId==null&&"createdbyuser".equals(resourceDtl.sharing)){
          //check for WS content admin or approver
          if (session["userdetails"].publisherId==null && session["userdetails"].authorities.any {
              it.authority == "ROLE_WS_CONTENT_CREATOR" })
              hasAccess = true
      }else if(resourceDtl.chapterId==null&&resourceDtl.sharing==null){
          //ws content creator created book independent content
          hasAccess = true

      }
       return hasAccess
  }

    def doesIPAddressHasAccessForBook(bookId,request,siteId){
        boolean hasAccess = false

        String ipAddress = utilService.getIPAddressOfClient(request)

        List instituteIPAddress = InstituteIpAddress.findAllByIpAddressAndSiteId(ipAddress,siteId)
        if(instituteIPAddress.size()>0){
            // Collect all batch IDs for IP-based access
            List<Long> batchIds = []
            for(int i=0;i<instituteIPAddress.size();i++){
                CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndNameAndStatus(instituteIPAddress[i].institute_id,"Default","active")
                if(courseBatchesDtl!=null){
                    batchIds.add(courseBatchesDtl.id)
                }
            }

            if(!batchIds.isEmpty()) {
                // Get all book IDs available to these batches (including package books)
                Set<Long> availableBookIds = getAllBookIdsForBatches(batchIds)

                // Check if the requested book ID is in the available books
                if(availableBookIds.contains(bookId as Long)){
                    hasAccess = true
                }
            }
        }
        return hasAccess
    }

    def doesRemoteAddressHasAccessForBook(bookId,request,siteId,response,session){
         boolean hasAccess = false
        if(redisService.(""+request.getParameter("sessionKey"))!=null&&!"null".equals(redisService.(""+request.getParameter("sessionKey")))) {
            InstituteMst instituteMst = InstituteMst.findById(new Integer(redisService.(""+request.getParameter("sessionKey"))))
            if (instituteMst != null) {
                CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndNameAndStatus(instituteMst.id, "Default", "active")
                if (courseBatchesDtl != null) {
                    // Get all book IDs available to this batch (including package books)
                    Set<Long> availableBookIds = getAllBookIdsForBatches([courseBatchesDtl.id])

                    // Check if the requested book ID is in the available books
                    if(availableBookIds.contains(bookId as Long)){
                        hasAccess = true
                        User user
                        if(redisService.(""+request.getParameter("sessionKey")+"Username")!=null&&!"null".equals(redisService.(""+request.getParameter("sessionKey")+"Username"))){
                            user = User.findByUsername(redisService.(""+request.getParameter("sessionKey")+"Username"))
                        }
                        else user = User.findByUsername(instituteMst.siteId + "_institute" + instituteMst.id)
                        session["userdetails"] = user
                        springSecurityService.reauthenticate(user.username, user.password)
                        def authentication = SecurityContextHolder.context.authentication
                        rememberMeServices.loginSuccess(request, response, authentication)
                        userManagementService.registerUserLogin(user.username, siteId)
                    }
                }
            }
           redisService.(""+request.getParameter("sessionKey"))="null"
        }
        return hasAccess
    }

    def saveNewUser(instituteMst,User user, siteId,password,request,username,params,instituteUrl){
        String instituteName="";
        String wsLink="";
        String userType=""
        User user1 = User.findByUsername(user.username)
        if(user1==null){
            //new user
            WinGenerator winGenerator = new WinGenerator()
            winGenerator.save(failOnError: true)
            user.win=winGenerator.id
            user.save(failOnError: true, flush: true)
            Role role = Role.findByAuthority("ROLE_USER")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_ADD")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_UPLOAD")
            UserRole.create(user, role, true)
            if (username.indexOf('@') > -1 && "<EMAIL>"!=user.email && validateEmail(user.email, siteId)&&siteId.intValue()!=80) {
                if (instituteMst.eduWonder == "true") {
                    userManagementService.sendEmailToInstituteUser(instituteMst.name, instituteMst.logo, instituteMst.id, user.email, password, SiteMst.findById(siteId).siteName, instituteUrl)
                } else {
                    userManagementService.sendEmailToUserWS(user.email, password, SiteMst.findById(siteId).siteName)
                }
            }else if(username.indexOf('@') < 0) {
                if(siteId==1) {
                    try {
                        userType =  "instructor".equals(params.userType) ? "Teacher" : " Student"
                        wsLink = "https://bit.ly/3PFCuGb"
                        def message = "Dear ${user.name}, Your Wonderslate ${userType} account has been created on your institution website. Click on the below link ${instituteUrl} and login using ${user.mobile} and Password: ${password}. Contact Us: ${wsLink}"
                        utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                    } catch (Exception e) {
                        println("exception in sending institute sms" + e.toString())
                    }
                }
            }
            return true
        }else{
            return false
        }
    }

    def getInstitutesForUser(siteId,ipAddress){
        String username='Default'

        if(springSecurityService.currentUser!=null) {
            username=springSecurityService.currentUser.username
            String tempUsername = username.toLowerCase().trim()
            if(tempUsername.toLowerCase().indexOf("select")!=-1||tempUsername.toLowerCase().indexOf("sleep(")!=-1||tempUsername.toLowerCase().indexOf(" or ")!=-1||tempUsername.indexOf("||")!=-1) username='null'

        }
        List instituteDetails
        if(ipAddress==null||ipAddress.toLowerCase().indexOf("and")!=-1||ipAddress.toLowerCase().indexOf("=")!=-1||ipAddress.toLowerCase().indexOf("or")!=-1||ipAddress.indexOf("||")!=-1) ipAddress=null
        String sql =
                " SELECT bud.batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view," +
                        "cbd.syllabus,cbd.grade,im.level," +
                        "im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,bud.user_type," +
                        "im.enable_test,im.enable_analytics,im.enable_question_paper " +
                        " FROM wsuser.batch_user_dtl bud, wsuser.course_batches_dtl cbd, wsuser.institute_mst im " +
                        " where im.site_id="+siteId+" and bud.username='"+username+"' " +
                        " and cbd.id=bud.batch_id and im.id=cbd.conducted_by " +
                        " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) " +
                        " union " +
                        " select cbd.id batch_id, im.id institute_id, im.name,im.publisher_id publisherId,im.logo,im.edu_wonder,cbd.name batchName,im.full_library_view," +
                        "cbd.syllabus,cbd.grade,im.level," +
                        "im.syllabus instituteSyllabus,im.drive_for_instructor,drive_for_student,ra_for_instructor,ra_for_student,show_reference_section,'false' user_type," +
                        "im.enable_test,im.enable_analytics,im.enable_question_paper" +
                        " from wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.institute_ip_address iia " +
                        " where iia.ip_address= '"+ipAddress+"' and im.id =iia.institute_id and  im.id=cbd.conducted_by " +
                        " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and im.site_id="+siteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String instituteIds=""
        instituteDetails = results.collect{
           instituteIds = instituteIds+it.institute_id+","
            return [batchId: it.batch_id, id: it.institute_id, name:"Default".equals(it.batchName)? it.name:it.batchName,publisherId:it.publisherId,batchName:it.batchName,
                    fullLibraryView:it.full_library_view, syllabus:it.syllabus,grade:it.grade,logo:it.logo,isEduWonder:it.edu_wonder,
                    isInstructor:"Instructor".equals(it.user_type)?"true":"false",level:it.level, instituteSyllabus:it.instituteSyllabus,
                    driveForInstructor:it.drive_for_instructor,driveForStudent:it.drive_for_student,raForInstructor:it.ra_for_instructor,raForStudent:it.ra_for_student,
                    showReferenceSection:it.show_reference_section,enableTest:it.enable_test,enableAnalytics:it.enable_analytics,enableQuestionPaper:it.enable_question_paper,userType:it.user_type]
        }
        //remove comma at the end


            return  instituteDetails;

    }

    def sendAmazonShippedEmail(String toEmail, String user, orderId,url){
        if("<EMAIL>"!=toEmail) {
            mailService.sendMail {
                async true
                to toEmail
                from "Wonderslate eBooks <<EMAIL>>"
                subject " [Important] Amazon Order confirmation"
                body(view: "/wsshop/amazonorderconfirmation", model:[orderId: orderId, user: user,url:url])
            }
        }
    }

    String encrypt(String text) {
        try {
            if (redisService.("securityKeys_1") == null) dataProviderService.getSecurityKeys("1")
            String securityKey = redisService.("securityKeys_1")
            Cipher ecipher = Cipher.getInstance("AES/CBC/PKCS5PADDING", "SunJCE");
            IvParameterSpec iv = new IvParameterSpec((securityKey).getBytes());
            SecretKey secretKey = new SecretKeySpec((securityKey).getBytes(), "AES");
            ecipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

            return new String(Base64.getEncoder().encode(ecipher.doFinal(text.getBytes("UTF-8"))));
        } catch (Exception e) {
            println("Exception=" + e.toString())
        }

        return "";
    }

    String decrypt(String text) {
        try {
            if (redisService.("securityKeys_1") == null) dataProviderService.getSecurityKeys("1")
            String securityKey = redisService.("securityKeys_1")
            byte[] decodedBytes = Base64.getDecoder().decode(text);
            Cipher ecipher = Cipher.getInstance("AES/CBC/PKCS5PADDING", "SunJCE");
            IvParameterSpec iv = new IvParameterSpec((securityKey).getBytes());
            SecretKey secretKey = new SecretKeySpec(securityKey.getBytes(), "AES");
            ecipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

            return new String(ecipher.doFinal(decodedBytes));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }

    def sendChapterDownloadError( String instituteName, String siteName,Long siteId){
        String fromEmail = "SAGE eVidya <<EMAIL>>"
        String subjectText = "Update - 100 download complete - eVidya"
        if (siteId.intValue() == 23) {
            fromEmail = "SAGE eTexts <<EMAIL>>"
            subjectText ="Update - 100 download complete - eText"
        }
        mailService.sendMail {
            async true
            to "<EMAIL>"
            from fromEmail
            cc "<EMAIL>","<EMAIL>"
            subject subjectText
            body(view: "/creation/chapterDownloadError", model:[instituteName: instituteName, siteName: siteName])
        }
    }

    def addUserGrades(request) {
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            def json
            JSONObject contents = new JSONObject(jsonObj.contents);
            UserGradesDtl userGradesDtl
            userGradesDtl = UserGradesDtl.findByUserName(springSecurityService.currentUser.username)
            if (userGradesDtl != null) {
                userGradesDtl.contents=contents
                userGradesDtl.save(failOnError: true, flush: true)
                json=["status":'updated']
            } else {
                userGradesDtl = new UserGradesDtl(userName: springSecurityService.currentUser.username, contents:contents)
                userGradesDtl.save(failOnError: true, flush: true)
                json=["status":'added']
            }
            dataProviderService.getUserGrades(springSecurityService.currentUser.username)
            return json
        }
    }

    def getUserGrades(username){
        def json
        if(redisService.("getUserGrades_"+username)==null){
            dataProviderService.getUserGrades(springSecurityService.currentUser.username)
        }
        json=["userGrades":redisService.("getUserGrades_"+username)]
        return json
    }


    def sendCartActiveEmail (String toEmail,Integer siteId,String encodedString){
        if("<EMAIL>"!=toEmail) {
            SiteMst siteMst = SiteMst.findById(siteId)
            String    view = "/creation/userActiveCartEmail"
            String webUrl = siteMst.siteDomainName
            if(webUrl.indexOf("wonderslate.com")==0) webUrl = "https://www.wonderslate.com"
            else webUrl="https://"+webUrl+"/"
            String  fromEmail = siteMst.fromEmail?siteMst.fromEmail:"Wonderslate <<EMAIL>>"
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject "Your Book is waiting for you in your cart!"
                    body(view: view,
                            model: [siteId      : siteId, siteName  : siteMst.siteName ,encodedUsername:encodedString,webUrl:webUrl])
                }
            }catch(Exception e){
                println("Exception in sending sendCartActiveEmail to "+toEmail+" and exception is "+e.toString())
            }
        }
    }



    def sendPurchasedBookEmail (String toEmail,Integer siteId,List relatedBooks,String encodedString){
        if("<EMAIL>"!=toEmail) {
            SiteMst siteMst = SiteMst.findById(siteId)
            String    view = "/creation/userPurchasedBookEmail"
            String  fromEmail = siteMst.fromEmail?siteMst.fromEmail:"Wonderslate <<EMAIL>>"
            String webUrl = siteMst.siteDomainName
            if(webUrl.indexOf("wonderslate.com")==0) webUrl = "https://www.wonderslate.com"
            else webUrl="https://"+webUrl+"/"
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject "Trending Books for you!"
                    body(view: view,
                            model: [siteId      : siteId, siteName  : siteMst.siteName ,relatedBooks:relatedBooks,encodedUsername:encodedString,webUrl:webUrl])
                }
            }catch(Exception e){
                println("Exception in sending sendPurchasedBookEmail to "+toEmail+" and exception is "+e.toString())
            }
        }
    }


    def sendPreviewedBookEmail (String toEmail,Integer siteId,List relatedBooks,String encodedString){
        if("<EMAIL>"!=toEmail) {
            SiteMst siteMst = SiteMst.findById(siteId)
            String    view = "/creation/userPaidPreviewEmail"
            String  fromEmail = siteMst.fromEmail?siteMst.fromEmail:"Wonderslate <<EMAIL>>"
            String webUrl = siteMst.siteDomainName
            if(webUrl.indexOf("wonderslate.com")==0) webUrl = "https://www.wonderslate.com"
            else webUrl="https://"+webUrl+"/"
            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject " Recommended Books for you, experience the Joy of Learning!"
                    body(view: view,
                            model: [siteId      : siteId, siteName  : siteMst.siteName ,bookDtl:relatedBooks,encodedUsername:encodedString,webUrl:webUrl])
                }
            }catch(Exception e){
                println("Exception in sending sendPreviewedBookEmail to "+toEmail+" and exception is "+e.toString())
            }
        }
    }

    def checkAccessCodeStatus(accessCode,siteId){
        def json
        String username,dateAdded
        String bookId=""
        String codebookId=""
        String status="Not found"
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd")
        try {
            String code = accessCode.substring(0, 5)
            bookId = accessCode.substring(5)
            BooksPermission booksPermission = BooksPermission.findByBookCodeAndBookId(code, new Integer(bookId))
            if (booksPermission != null) {
                username = booksPermission.username
                codebookId=booksPermission.bookId+""
                dateAdded = dateFormat.format(booksPermission.dateCreated)
                status = "OK"
            }
            json=["status":status,username:username?username.split(siteId+"_")[1]:"",bookId:codebookId,dateAdded:dateAdded?dateAdded:""]
        }
        catch(Exception e){
            json=["status":"Not found",username:"",bookId:"",dateAdded:""]
        }
        return json
    }

    def   addTeacherNomineeDetails(request,siteId){
        def json
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            TeacherNomineeDtl teacherNomineeDtl = new TeacherNomineeDtl()
            teacherNomineeDtl.userType = jsonObj.userType
            teacherNomineeDtl.userName = jsonObj.userName
            teacherNomineeDtl.userCity = jsonObj.userCity
            teacherNomineeDtl.userEmail = jsonObj.userEmail
            teacherNomineeDtl.userMobile = jsonObj.userMobile
            teacherNomineeDtl.userPincode = jsonObj.userPincode
            teacherNomineeDtl.userState = jsonObj.userState
            teacherNomineeDtl.teacherClass = jsonObj.teacherClass
            teacherNomineeDtl.teacherEmail = jsonObj.teacherEmail
            teacherNomineeDtl.teacherName = jsonObj.teacherName
            teacherNomineeDtl.teacherMobile = jsonObj.teacherMobile
            teacherNomineeDtl.teacherSubject = jsonObj.teacherSubject
            teacherNomineeDtl.description = jsonObj.description
            teacherNomineeDtl.schoolName = jsonObj.schoolName
            teacherNomineeDtl.comments = jsonObj.comments
            teacherNomineeDtl.siteId = siteId
            teacherNomineeDtl.save(failOnError: true, flush: true)
            try {
                def message = "Congratulations, your nomination for Favourite Teachers Award is accepted. Please share this link with your friends and get them to vote for your teacher.https://www.wonderslate.com/favouriteteachers/${teacherNomineeDtl.id} - Wonderslate Technologies"
                utilService.sendSMSForInstituteUser(siteId, message, teacherNomineeDtl.userMobile)
            } catch (Exception e) {
                println("exception in sending user  sms nominee" + e.toString())
            }
            try {
                def message = "Congratulations. ${teacherNomineeDtl.userName} has nominated you for Favourite Teacher Award. Please click on this link to see. https://www.wonderslate.com/favouriteteachers/${teacherNomineeDtl.id} - Wonderslate Technologies"
                utilService.sendSMSForInstituteUser(siteId, message, teacherNomineeDtl.teacherMobile)
            } catch (Exception e) {
                println("exception in sending teacher sms nominee" + e.toString())

            }
            dataProviderService.getTeachersNomineeDetails(siteId)
            json=["status":"OK",id:teacherNomineeDtl.id]
        }
        return json
    }


    def addTeacherImage(request,params,siteId){
        def json
        try {
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            TeacherNomineeDtl teacherNomineeDtl = TeacherNomineeDtl.findById(new Long(params.id))
            File uploadDir = new File("upload/Teacher/" + teacherNomineeDtl.id)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            BufferedImage image = ImageIO.read(file.getInputStream())
            String filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            //creating directory to process images
            File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
            if (!uploadDir1.exists()) uploadDir1.mkdirs()
            //saving original image finally to webp
            String webPImage = filename.substring(0, filename.indexOf("."))
            webPImage = "teacherimage_"+teacherNomineeDtl.id
            ImageIO.write(image, "webp", new File("upload/Teacher/" + teacherNomineeDtl.id + "/processed/" + webPImage + ".webp"));
            teacherNomineeDtl.teacherImage = "teacherimage_"+teacherNomineeDtl.id+".webp"
            teacherNomineeDtl.save(failOnError: true, flush: true)
            dataProviderService.getTeachersNomineeDetails(siteId)
            json = ["status": teacherNomineeDtl.id ? "success" : "failed",id:teacherNomineeDtl.id]
        } catch(Exception e){
                json=["status":"image upload failed"]
            }
        return json
    }

    def getTeachersNomineeDetails(siteId){
        if(redisService.("teachersNomineeForSite_"+siteId)==null){
            dataProviderService.getTeachersNomineeDetails(siteId)
        }
        List nomineeList= new JsonSlurper().parseText(redisService.("teachersNomineeForSite_"+siteId))
        def json=["teachersNomineeList":nomineeList?nomineeList:"no records"]
        return json
    }

    def getNomineeDetailsById(params) {
        def json
        TeacherNomineeDtl teacherNomineeDtl = TeacherNomineeDtl.findById(new Long(params.nomineeId))
        if (teacherNomineeDtl!=null){
            if (redisService.("getNomineeIdVoteCount_" + params.nomineeId) == null) {
                dataProviderService.getNomineeIdVoteCount(new Long(params.nomineeId))
            }
            json = ["status":'OK',id: teacherNomineeDtl.id, studentName: teacherNomineeDtl.userName, studentMobile: teacherNomineeDtl.userMobile, studentCity: teacherNomineeDtl.userCity,
                teacherPhoto: teacherNomineeDtl.teacherImage, teacherName: teacherNomineeDtl.teacherName, schoolName: teacherNomineeDtl.schoolName, description: teacherNomineeDtl.description, voteCount: redisService.("getNomineeIdVoteCount_" + params.nomineeId)]
         }else{
            json=["status":"Fail"]
        }
        return json
    }

    def voteByNomineeId(params){
        NomineeVotingDtl nomineeVotingDtl=new NomineeVotingDtl()
        nomineeVotingDtl.nomineeId=new Long(params.nomineeId)
        nomineeVotingDtl.username=springSecurityService.currentUser?springSecurityService.currentUser.username:null
        nomineeVotingDtl.save(failOnError: true, flush: true)
        dataProviderService.getNomineeIdVoteCount(nomineeVotingDtl.nomineeId)
        dataProviderService.getAllModeratedTeachersNomineeDetails(1)
        dataProviderService.getTeachersNomineeDetails(1)
        def json=['status':"OK"]
        return json
    }

    def getNomineeIdVoteCount(params){
        if( redisService.("getNomineeIdVoteCount_"+params.nomineeId) ==null){
            dataProviderService.getNomineeIdVoteCount(new Long(params.nomineeId))
        }
        def json=["count":redisService.("getNomineeIdVoteCount_"+params.nomineeId)]
        return json
    }

    def getAllModeratedTeachersNomineeDetails(params,siteId){
        if(redisService.("allModeratedTeachersNomineeDetails_"+siteId)==null){
            dataProviderService.getAllModeratedTeachersNomineeDetails(siteId)
        }
        List nomineeList= new JsonSlurper().parseText(redisService.("allModeratedTeachersNomineeDetails_"+siteId))
        def json = [status: nomineeList ? "OK" : "Not present", data: nomineeList, recordsTotal: nomineeList.size(), recordsFiltered: nomineeList.size(), draw: params.draw]
        return json
    }

    def getUserDefaultTimeLog(username){
        int days = 7

        String sql = "SELECT sum(duration) FROM user_time_log where DATE(logged_date) > date_add(CURDATE() , INTERVAL -"+days+" day)" +
                " and username='"+username+"'"


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String numberOfSeconds="0"
        if(results.size()>0) numberOfSeconds=""+results[0][0]

        redisService.("userDefaultTimeLog_"+username)=numberOfSeconds
    }

    def getUserQuizDefaultTimeLog(username){
        int days = 7

        String sql = "SELECT sum(qrd.user_time) FROM quiz_rec_dtl qrd,quiz_rec_mst qrm where DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL -"+days+" day)" +
                " and qrm.username='"+username+"' and qrd.quiz_rec_id=qrm.id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String numberOfSeconds="0"
        if(results.size()>0) numberOfSeconds=""+results[0][0]

        redisService.("userQuizDefaultTimeLog_"+username)=numberOfSeconds
    }

    def getUserTimeLog(username,noOfDays){
        int days = Integer.parseInt(noOfDays)

        String sql = "SELECT sum(duration) FROM user_time_log where DATE(logged_date) > date_add(CURDATE() , INTERVAL -"+days+" day)" +
                " and username='"+username+"'"


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String numberOfSeconds="0"
        if(results.size()>0) numberOfSeconds=""+results[0][0]

        redisService.("userDefaultTimeLog_"+username)=numberOfSeconds
    }

    def getUserQuizTimeLog(username,noOfDays){
        int days = Integer.parseInt(noOfDays)

        String sql = "SELECT sum(qrd.user_time) FROM quiz_rec_dtl qrd,quiz_rec_mst qrm where DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL -"+days+" day)" +
                " and qrm.username='"+username+"' and qrd.quiz_rec_id=qrm.id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        String numberOfSeconds="0"
        if(results.size()>0) numberOfSeconds=""+results[0][0]

        redisService.("userQuizDefaultTimeLog_"+username)=numberOfSeconds
    }

    def updateUserDisplayInformation(String username,session){
        if(username.indexOf("_temp")==-1) {
            if (redisService.("userPrepjoydetails_" + username) == null) prepjoyService.getUserPrepJoyDetails(username)
            if (redisService.("userDefaultTimeLog_" + username) == null) userManagementService.getUserDefaultTimeLog(username)
            if (redisService.("userQuizDefaultTimeLog_" + username) == null) userManagementService.getUserQuizDefaultTimeLog(username)
            if (redisService.("subjectwiseInfoTotal_" + username + "_7") == null) progressService.getSubjectwiseInfoTotal(username, "7")
            if (redisService.("testAttemptInfo_" + username + "_7") == null) progressService.getTestAttemptInfo(username, "7")

            session["userPrepjoydetails"] = redisService.("userPrepjoydetails_" + username)
            String[] prepJoyDetails = redisService.("userPrepjoydetails_" + username).split(',')

            session["totalPoints"] = prepJoyDetails[0].substring(prepJoyDetails[0].indexOf(':') + 2)
            session["currentBadge"] = prepJoyDetails[1].substring(prepJoyDetails[0].indexOf(':') + 2)
            session["totalMedals"] = prepJoyDetails[2]

            session["userDefaultTimeLog"] = redisService.("userDefaultTimeLog_" + username)
            session["userQuizDefaultTimeLog"] = redisService.("userQuizDefaultTimeLog_" + username)
            session["subjectwiseInfoTotal_" + username + "_7"] = redisService.("subjectwiseInfoTotal_" + username + "_7")
            session["testAttemptInfo_" + username + "_7"] = redisService.("testAttemptInfo_" + username + "_7")
        }

    }

    def getLatestReadBooks(String username){

        String sql = "SELECT max(id) maxId, book_id FROM wslog.books_view_dtl where username='"+username+"' group by book_id order by maxId desc limit 5"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)



        List booksList = results.collect {
            BooksMst booksMst = dataProviderService.getBooksMst(it.book_id)
            if (booksMst == null) {
                return null  // Indicate that this item should not be added to the final list
            } else {
                // If booksMst is not null, return the book details
                return [bookId: it.book_id, title: booksMst.title, coverImage: booksMst.coverImage,bookType:booksMst.bookType]
            }
        }.findAll { it != null }  // Remove all null items from the list

        Gson gson = new Gson();
        String element = gson.toJson(booksList,new TypeToken<List>() {}.getType())
        redisService.("latestReadBooks_"+username) = element

    }

    def numberOfBooksInLibrary(username){
        String sql = "SELECT count(distinct(book_id)) noOfBooks FROM books_permission where username='"+username+"'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
       if(results.size()==0) redisService.("noOfBooksInLibrary_"+username) ="0"
       else  redisService.("noOfBooksInLibrary_"+username) = ""+results[0].noOfBooks
    }

    int getInstituteForInstituteManager(String username,siteId){
       //get the institute for the institute manager equate site_id in institute_mst table, and username,user_type='Manager',name='Default', status='active' from course_batches_dtl
        String sql = "SELECT im.id institute_id FROM wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.batch_user_dtl bud " +
                " where im.site_id="+siteId+" and bud.username='"+username+"' and bud.user_type='Manager' and cbd.name='Default' and cbd.status='active' and im.id=cbd.conducted_by and cbd.id=bud.batch_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        SafeSql sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        if(results.size()>0) return results[0].institute_id.intValue()
        else return -1

    }


}
