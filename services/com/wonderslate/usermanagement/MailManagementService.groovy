package com.wonderslate.usermanagement

import grails.plugins.mail.MailService
import grails.transaction.Transactional

@Transactional
class MailManagementService {
    MailService mailService

    def serviceMethod() {

    }

    def activitiesUpdate() {
        List allUsers = User.findAll();
        String sql;
        allUsers.each {
            sql = "select u.name,u.profilepic,tm.topicName,tm.id,rd.id,u.id,rd.resType,rd.resourceName,rgd.dateCreated,tm.syllabusType from" +
                    " User u,ResourceGroupDtl rgd,TopicMst tm, ResourceDtl rd,Groups gd " +
                    " where rgd.groupId in (select distinct groupId from GroupsDtl where username='" + it.username + "')" +
                    " and gd.id=rgd.groupId" +
                    " and rd.id=rgd.resourceId" +
                    " and tm.id = rd.topicId" +
                    " and u.username=rgd.username and (rd.sharing is null or rd.sharing not in ('deleted'))" +
                     " and rgd.dateCreated > (select max(loggedInDate) from wslog.UserLog where action in ('login','updatemail') and username='" + it.username + "') " +
                    " order by rgd.dateCreated desc"
            List results = Groups.executeQuery(sql)
            if (results.size() > 0) {
                List activities = results.collect { group ->
                    return [name: group[0], profilepic: group[1], topicName: group[2], topicId: group[3], resourceId: group[4], userId: group[5], resType: group[6], resourceName: group[7], dateCreated: group[8]]
                }
                String emailId = it.email
                String name = it.name
                if (!"<EMAIL>".equals(emailId)) {
                    println "sending mail to "+emailId
                    UserLog log = new UserLog(username:it.username, action:'updatemail');
                    log.save(flush: true);
                    mailService.sendMail {
                        async true
                        to emailId
                        subject "Update from your groups"
                        body(view: "/mail/activitiesUpdate", model: [activities: activities, name:name])

                    }
                }
            }
        }

    }

  def sendInvite(String inviterUsername,String toEmail,String inviterName,String inviteeName, String personalMessage){
      def generator = { String alphabet, int n ->
          new Random().with {
              (1..n).collect { alphabet[ nextInt( alphabet.length() ) ] }.join()
          }
      }
      String token = generator( (('A'..'Z')+('0'..'9')).join(), 30 );
      FriendInvite friendInvite = new FriendInvite(username:inviterUsername,inviteeName:inviteeName,inviteeEmail: toEmail,token: token);
      friendInvite.save(failOnError: true,flush: true);

            String subjectText = inviterName+" invites you to join Wonderslate";

            mailService.sendMail {
                async true
                to toEmail
                subject subjectText
                body(view: "/mail/sendInvite", model: [inviterName:inviterName, inviteeName:inviteeName,personalMessage:personalMessage])
            }
    }
    
    
    def emailQuizIssueReport(String userName,String toEmail,String siteName, String fileName){
        String subjectText = "Quiz issues daily report from "+siteName;

        def file = new File(fileName)
        mailService.sendMail {
            multipart true        
            async true
            to toEmail
            subject subjectText
            text subjectText
            attach(file.getName(), file)
        }
    }    
}
