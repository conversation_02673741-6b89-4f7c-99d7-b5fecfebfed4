package com.wonderslate.usermanagement

import grails.transaction.Transactional
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.context.request.RequestContextHolder
import org.grails.web.util.WebUtils
import javax.servlet.http.Cookie

@Transactional
class UserLogService {
    static transactional = true
    def springSecurityService
    def sessionRegistry
    def concurrentSessionControlAuthenticationStrategy
    def grailsApplication

    @Transactional
    def addUserLog(String action) {
        def session = RequestContextHolder.currentRequestAttributes().getSession()

        //this method is getting called twice in due to the event interactivelogin....  a logic to discard the second call
        if(session["AlreadyAuthenticated"]==null) {
            boolean allowLogin = true
            def authentication = SecurityContextHolder.context.authentication

            try {
                concurrentSessionControlAuthenticationStrategy.onAuthentication(
                        authentication, WebUtils.retrieveGrailsWebRequest().getCurrentRequest(),
                        WebUtils.retrieveGrailsWebRequest().getCurrentResponse())
            } catch (Exception e) {
                if("Do not allow"==checkNumberOfSimultaneousUsers(authentication.principal.username)) allowLogin = false
                if(!allowLogin) println("Exception is=" + e.toString())
            }

            if(allowLogin) {
                sessionRegistry.registerNewSession(session.getId(), authentication.getPrincipal())
                User user = springSecurityService.getCurrentUser()
                UserLog log = new UserLog(username: user.username, action: action)
                log.save(failOnError: true,flush: true)
                session["NumberExceeded"] = "false"
            } else {
                session["NumberExceeded"] = "true"
            }

            Cookie cookie = new Cookie("SimulError", !allowLogin?"Fail":"Pass")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)

            session["AlreadyAuthenticated"]="true"
        } else{
            session.removeAttribute("AlreadyAuthenticated")
        }
    }

    //This function is used to check number of users already logged in and decide whether to allow new login or not.
    String checkNumberOfSimultaneousUsers(String username){
        // in future if you want to forcefully logout web user ... some info here at https://stackoverflow.com/questions/28051887/force-logout-for-authenticated-user-using-spring-security-plugin
        springSecurityService.reauthenticate(username)
        List sessionsList = sessionRegistry.getAllSessions(springSecurityService.principal,false)
        int noOfWebUsers=0,noOfMobileUsers

        if(sessionsList!=null){
            noOfWebUsers=sessionsList.size()
            println("number of active sessions="+sessionsList.size())
        } else {
            println("no active users")
        }

        noOfMobileUsers = AuthenticationToken.findAllByUsername(username).size()

        int maxUsers= (new Integer(grailsApplication.config.grails.appServer.maximumSessions)).intValue()
        User user = User.findByUsername(username)
        if(user.authorities.any { it.authority == "ROLE_WS_CONTENT_CREATOR" }){
             maxUsers++
        }

        //user based login limitation if no of logins exceed
        if(noOfMobileUsers+noOfWebUsers>=maxUsers && user.maxLogins!=null){
            //if set to -1, unlimited which means just setting +1 of current logins
            if(user.maxLogins==-1)
                maxUsers = noOfMobileUsers+noOfWebUsers+1
            else //how much ever set on db
                maxUsers = user.maxLogins
        }

        println("username="+username+" noOfWebUsers="+noOfWebUsers+" noOfMobileUsers="+noOfMobileUsers+" maxUsers="+maxUsers)
        return noOfMobileUsers+noOfWebUsers<maxUsers?"Allow":"Do not allow"
    }
}
