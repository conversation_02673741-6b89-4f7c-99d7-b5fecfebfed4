package com.wonderslate.usermanagement

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class ProgressService {
    def grailsApplication
    def redisService

    def getSubjectwiseInfo(String username,String numberOfDays) {
          String sql = "select  (CASE user_option \n" +
                "    WHEN -1 THEN 'Skipped'\n" +
                "    WHEN correct_option  THEN 'Correct'\n" +
                "    ELSE 'Wrong' END) result,qrd.subject,count(subject) numberOfQuestions\n" +
                "from wsuser.quiz_rec_dtl qrd, wsuser.quiz_rec_mst qrm\n" +
                "where qrd.username='"+username+"' and qrd.latest='true'  \n" +
                "and qrm.username='"+username+"' and DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - "+numberOfDays+" day)\n" +
                "and qrd.quiz_rec_id=qrm.id group by subject,result order by subject"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        return element

    }

    def getQuizTimePerDay(String username,String numberOfDays){
        String sql = "select sum(qrd.user_time) userTime,DATE(convert_tz(date_created,'+00:00','+05:30')) quizDate from quiz_rec_mst qrm,quiz_rec_dtl qrd\n" +
                "where DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - "+numberOfDays+" day) \n" +
                "and qrm.username='"+username+"' and qrd.quiz_rec_id=qrm.id\n" +
                "group by quizDate "

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        return element
    }

    def getNumberOfQuizzesPerDay(String username,String numberOfDays){
        String sql = "select count(qrd.id) noOfQuestions,DATE(convert_tz(date_created,'+00:00','+05:30')) quizDate from quiz_rec_mst qrm,quiz_rec_dtl qrd\n" +
                "where DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - "+numberOfDays+" day) \n" +
                "and qrm.username='"+username+"' and qrd.quiz_rec_id=qrm.id\n" +
                "group by quizDate "

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        return element
    }

    def getSubjectQuizTimePerDay(String username,String numberOfDays){
        String sql = "select sum(qrd.user_time) userTime,DATE(convert_tz(date_created,'+00:00','+05:30')) quizDate,subject from quiz_rec_mst qrm,quiz_rec_dtl qrd\n" +
                "where DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - "+numberOfDays+" day) \n" +
                "and qrm.username='"+username+"' and qrd.quiz_rec_id=qrm.id\n" +
                "group by quizDate,subject"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        return element
    }

    def getSubjectQuizTime(String username,String numberOfDays){
        String sql = "select sum(qrd.user_time) userTime,subject from quiz_rec_mst qrm,quiz_rec_dtl qrd\n" +
                "where DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - "+numberOfDays+" day) \n" +
                "and qrm.username='"+username+"' and qrd.quiz_rec_id=qrm.id\n" +
                "group by subject"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        return element
    }

    def getSubjectwiseInfoTotal(String username,String numberOfDays) {
        String sql = "select  count(qrd.subject) numberOfQuestions,qrd.subject\n" +
                "from wsuser.quiz_rec_dtl qrd, wsuser.quiz_rec_mst qrm\n" +
                "where qrd.username='"+username+"'\n" +
                "and qrm.username='"+username+"' and DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - "+numberOfDays+" day)\n" +
                "and qrd.quiz_rec_id=qrm.id group by subject order by subject"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("subjectwiseInfoTotal_"+username+"_"+numberOfDays) = element
    }

    def getTestAttemptInfo(String username,String numberOfDays) {
        String sql = "select  sum(qrm.correct_answers) correct,sum(qrm.incorrect_answers) incorrect,sum(qrm.skipped) skipped\n" +
                "from wsuser.quiz_rec_mst qrm\n" +
                 "where qrm.username='"+username+"' and DATE(qrm.date_created) > date_add(CURDATE() , INTERVAL - "+numberOfDays+" day)\n"

        println("sql="+sql)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)


        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("testAttemptInfo_"+username+"_"+numberOfDays) = element

    }
}
