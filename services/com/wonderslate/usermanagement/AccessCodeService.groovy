package com.wonderslate.usermanagement

import grails.transaction.Transactional

@Transactional
class AccessCodeService {
    def redisService
    def generatePlayAccessCode(Integer lastNumber, Integer challengeMstCode,Integer siteId){
        int newNumber = lastNumber +1
        if(lastNumber > 999999) newNumber = 100000
        String abc = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random rd = new Random()
        char first = abc.charAt(rd.nextInt(abc.length()));
        char second = abc.charAt(rd.nextInt(abc.length()));
        StringBuilder sb = new StringBuilder(""+newNumber);
        sb.insert(3, first);
        sb.insert(6,second)

        String accessCode  = sb.toString()

        PlayAccessCodes playAccessCodes = new PlayAccessCodes(generatedCode: accessCode,actualCode: new Integer(newNumber),
                challengeMstCode:challengeMstCode,siteId: siteId )
        playAccessCodes.save(failOnError: true, flush: true)

        redisService.("latestAccessCode_"+siteId)=""+newNumber
        return accessCode

    }
}
