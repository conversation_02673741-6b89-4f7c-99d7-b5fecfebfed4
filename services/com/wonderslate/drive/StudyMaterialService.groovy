package com.wonderslate.drive

import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.usermanagement.User
import grails.transaction.Transactional
import org.springframework.web.multipart.MultipartFile
import grails.core.GrailsApplication

@Transactional
class StudyMaterialService {

    GrailsApplication grailsApplication
    def springSecurityService
    DataProviderService dataProviderService

    /**
     * Uploads a study material for a user.
     *
     * @param username   The username of the user uploading the material.
     * @param file       The file to be uploaded.
     * @param folderPath The path to the folder where the file should be stored.
     * @return           The saved StudyMaterial instance.
     */
    StudyMaterial uploadStudyMaterial(String username, MultipartFile file, String folderPath = null) {
        // Validate inputs
        if (!username || !file || file.empty) {
            throw new IllegalArgumentException("Invalid input parameters.")
        }

        // Determine file type
        String fileType = getFileType(file)

        // Create StudyMaterial instance without filePath yet
        def studyMaterial = new StudyMaterial(
                username: username,
                fileName: file.originalFilename,
                fileType: fileType,
                folderPath: folderPath
        )

        // Save to database to get the materialId
        if (!studyMaterial.save(flush: true)) {
            throw new RuntimeException("Failed to save study material: ${studyMaterial.errors.allErrors}")
        }

        // Save file to storage
        String filePath = saveFileToStorage(username, file, folderPath, studyMaterial.id)

        // Update the filePath in the studyMaterial
        studyMaterial.filePath = filePath
        studyMaterial.save(flush: true)

        return studyMaterial
    }

    /**
     * Creates a new folder for a user.
     *
     * @param username   The username of the user.
     * @param folderName The name of the new folder.
     * @param parentPath The path to the parent folder.
     * @return           The saved Folder instance.
     */
    Folder createFolder(String username, String folderName, String parentPath = null) {
        if (!username || !folderName) {
            throw new IllegalArgumentException("Invalid input parameters.")
        }

        String folderPath = parentPath ? "${parentPath}/${folderName}" : folderName

        // Check if folder already exists
        def existingFolder = Folder.findByUsernameAndFolderPath(username, folderPath)
        if (existingFolder) {
            throw new IllegalArgumentException("Folder already exists.")
        }

        // Create Folder instance
        def folder = new Folder(
                username: username,
                folderName: folderName,
                folderPath: folderPath
        )

        // Save to database
        if (!folder.save(flush: true)) {
            throw new RuntimeException("Failed to create folder: ${folder.errors.allErrors}")
        }

        return folder
    }

    /**
     * Deletes a study material owned by the user.
     *
     * @param username      The username of the user.
     * @param studyMaterial The StudyMaterial instance to delete.
     * @return              true if deletion was successful.
     */
    boolean deleteStudyMaterial(String username, StudyMaterial studyMaterial) {
        if (studyMaterial.username != username) {
            throw new SecurityException("You do not have permission to delete this material.")
        }

        // Delete the file from storage
        deleteFileFromStorage(studyMaterial)

        //delete the shared material
        deleteSharedMaterial(studyMaterial.id)
        // Delete the StudyMaterial from the database
        studyMaterial.delete(flush: true)

        return true
    }

    def deleteSharedMaterial(Long materialId){
        def sharedMaterials = MaterialShare.findAllByMaterialId(materialId)
        sharedMaterials.each {
            it.delete(flush: true)
        }
    }

    /**
     * Deletes a folder and all its contents.
     *
     * @param username The username of the user.
     * @param folder   The Folder instance to delete.
     * @return         true if deletion was successful.
     */
    boolean deleteFolder(String username, Folder folder) {
        if (folder.username != username) {
            throw new SecurityException("You do not have permission to delete this folder.")
        }

        String folderPath = folder.folderPath

        // Find all subfolders and materials under this folder
        def subFolders = Folder.findAllByUsernameAndFolderPathLike(username, folderPath + '%')
        def materials = StudyMaterial.findAllByUsernameAndFolderPathLike(username, folderPath + '%')

        // Delete files from storage
        materials.each { material ->
            //delete the shared material
            deleteSharedMaterial(material.id)
            deleteFileFromStorage(material)
        }

        // Delete materials from database
        StudyMaterial.executeUpdate("delete from StudyMaterial where username = :username and folderPath like :folderPath", [username: username, folderPath: folderPath + '%'])

        // Delete folders from database
        Folder.executeUpdate("delete from Folder where username = :username and folderPath like :folderPath", [username: username, folderPath: folderPath + '%'])

        return true
    }

    /**
     * Shares a study material with one or more batches.
     *
     * @param username   The username of the user sharing the material.
     * @param materialId The ID of the study material to share.
     * @param batchIds   A list of batch IDs to share with.
     * @return           A list of saved MaterialShare instances.
     */
    List<MaterialShare> shareStudyMaterial(String username, Long materialId, Long batchId) {
        if (!username || !materialId || !batchId) {
            throw new IllegalArgumentException("Invalid input parameters.")
        }

        def studyMaterial = StudyMaterial.get(materialId)
        if (!studyMaterial) {
            throw new IllegalArgumentException("Study material not found.")
        }

        if (studyMaterial.username != username) {
            throw new SecurityException("You do not have permission to share this material.")
        }



        def existingShare = MaterialShare.findByMaterialIdAndBatchId(materialId, batchId)
            if (!existingShare) {
                // Create MaterialShare instance
                def materialShare = new MaterialShare(
                        materialId: materialId,
                        batchId: batchId,
                        sharedByUsername: username,
                        sharedAt: new Date()
                )

                if (!materialShare.save(flush: true)) {
                    throw new RuntimeException("Failed to share material: ${materialShare.errors.allErrors}")
                }

            }

        return
    }

    /**
     * Retrieves study materials for a user, optionally filtered by folder.
     *
     * @param username   The username of the user.
     * @param folderPath Optional. The folder path to filter materials.
     * @return           A list of StudyMaterial instances.
     */
    List<StudyMaterial> getStudyMaterials(String username, String folderPath = null) {
        if (folderPath) {
            return StudyMaterial.findAllByUsernameAndFolderPath(username, folderPath)
        } else {
            return StudyMaterial.findAllByUsernameAndFolderPathIsNull(username)
        }
    }

    /**
     * Retrieves folders for a user, optionally under a specific parent folder.
     *
     * @param username  The username of the user.
     * @param parentPath Optional. The parent folder path to filter folders.
     * @return          A list of Folder instances.
     */
    List<Folder> getFolders(String username, String parentPath = null) {
        if (parentPath) {
            // Only get folders directly under the parentPath
            def pattern = parentPath + '/%'
            return Folder.findAllByUsernameAndFolderPathLike(username, pattern).findAll {
                it.folderPath.count('/') == parentPath.count('/') + 1
            }
        } else {
            // Get top-level folders
            return Folder.findAllByUsername(username).findAll {
                !it.folderPath.contains('/')
            }
        }
    }

    /**
     * Updates the last accessed time for a study material.
     *
     * @param studyMaterial The StudyMaterial instance to update.
     */
    void updateLastAccessed(StudyMaterial studyMaterial) {
        studyMaterial.lastAccessed = new Date()
        studyMaterial.save(flush: true)
    }

    /**
     * Checks if a material is shared with the user.
     *
     * @param materialId The ID of the study material.
     * @param username   The username of the user.
     * @return           True if the material is shared with the user.
     */
    boolean isMaterialSharedWithUser(Long materialId, String username) {

        def userBatches = getUserBatches(username)
        if (!userBatches) {
            return false
        }

        def shared = MaterialShare.createCriteria().get {
            eq('materialId', materialId)
            'in'('batchId', userBatches*.batchId)
        }

        return shared != null
    }

    /**
     * Retrieves recently accessed study materials for a user.
     *
     * @param username   The username of the user.
     * @param maxResults The maximum number of results to return.
     * @return           A list of StudyMaterial instances.
     */
    List<StudyMaterial> getRecentlyAccessedMaterials(String username, int maxResults = 4) {
            def criteria = StudyMaterial.createCriteria()
            return criteria.list(max: maxResults) {
                or {
                    eq('username', username)
                }
                isNotNull('lastAccessed')
                order('lastAccessed', 'desc')
            }

    }

    /**
     * Determines the file type based on the file's content type or extension.
     *
     * @param file The file to check.
     * @return     The file type as a string.
     */
    private String getFileType(MultipartFile file) {
        String contentType = file.contentType
        if (contentType == 'application/pdf') {
            return 'PDF'
        } else if (contentType in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']) {
            return 'DOCX'
        } else {
            throw new IllegalArgumentException("Unsupported file type.")
        }
    }

    /**
     * Saves the uploaded file to storage.
     *
     * @param username   The username of the user.
     * @param file       The file to save.
     * @param folderPath The folder path where the file should be saved.
     * @param materialId The ID of the study material.
     * @return           The path where the file was saved.
     */
    private String saveFileToStorage(String username, MultipartFile file, String folderPath, Long materialId) {
        // Base path: grailsApplication.config.grails.basedir.path + "/drive/" + materialId
        String baseDir = grailsApplication.config.grails.basedir.path + "/supload/drive/" + materialId
        if (folderPath) {
            baseDir += "/${folderPath}"
        }

        File directory = new File(baseDir)
        if (!directory.exists()) {
            directory.mkdirs()
        }
        // Save the file
        String filePath = "${baseDir}/${file.originalFilename}"
        File destinationFile = new File(filePath)
        file.transferTo(destinationFile)
        if(folderPath){
            filePath="supload/drive/" + materialId+"/${folderPath}/"+file.originalFilename
        }else{
            filePath="supload/drive/" + materialId+"/"+file.originalFilename
        }
        return filePath
    }

    /**
     * Deletes a file from storage.
     *
     * @param studyMaterial The StudyMaterial instance whose file should be deleted.
     */
    private void deleteFileFromStorage(StudyMaterial studyMaterial) {
        File file = new File(grailsApplication.config.grails.basedir.path+"/"+studyMaterial.filePath)
        if (file.exists()) {
            file.delete()
        }
    }

    /**
     * Retrieves a file from storage.
     *
     * @param studyMaterial The StudyMaterial instance.
     * @return              The File object.
     */
    File getFileFromStorage(StudyMaterial studyMaterial) {
        return new File(studyMaterial.filePath)
    }

    /**
     * Retrieves the list of material IDs shared with the user.
     *
     * @param username The username of the user.
     * @return         A list of material IDs.
     */
    private List<Long> getMaterialIdsSharedWithUser(String username) {
        def userBatches = getUserBatches(username)
        if (!userBatches) {
            return []
        }
        def sharedMaterials = MaterialShare.createCriteria().list {
            'in'('batchId', userBatches*.id)
        }
        return sharedMaterials*.materialId
    }

    /**
     * Retrieves the list of batches the user belongs to.
     *
     * @param username The username of the user.
     * @return         A list of CourseBatchDtl instances.
     */
    private List<BatchUserDtl> getUserBatches(String username) {
        // Implement logic to retrieve batches the user is enrolled in
        // Assuming there's a UserBatch domain class linking users to batches

        def userBatches = BatchUserDtl.findAllByUsername(username)
        println("number of batches "+userBatches.size())
        //list all the batches the user is enrolled in
        userBatches.each {
            println("batch id "+it.batchId)
        }
        return userBatches
    }

    List getSharedMaterialsForBatch(Long batchId) {
        def sharedMaterials = MaterialShare.findAllByBatchId(batchId)
        return sharedMaterials.collect {
           // get the name of the person by get the username
            StudyMaterial studyMaterial = StudyMaterial.get(it.materialId)
            User user = dataProviderService.getUserMst(it.sharedByUsername)
            [materialId: it.materialId,name: user.name, fileName:studyMaterial.fileName,lastUpdated:studyMaterial.lastUpdated]
        }
    }
}
