package com.wonderslate.admin

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.log.BooksCodeLog
import com.wonderslate.usermanagement.User
import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class ReportsService {
    def grailsApplication
    DataProviderService dataProviderService

    def getScratchCardReport(String bookId = null, String inputFromDate, String inputToDate, Integer siteId) {

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        String bookCode=""
        if (bookId) {
            sql = "SELECT bm.book_id as bookId,bm.username,bm.no_of_codes  as noOfCodes,bm.code_name as campaignName," +
                    " DATE_FORMAT(DATE_ADD(bm.date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y') as dateCreated from books_code_log bm where  bm.book_id=" +bookId+
                    " and date(DATE_ADD(bm.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + inputFromDate + "','%d-%m-%Y')" +
                    " and date(DATE_ADD(bm.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + inputToDate + "','%d-%m-%Y')" +
                    " and bm.site_id="+siteId+
                    " order by bm.date_created desc"
        } else {
            sql = "SELECT bm.book_id as bookId,bm.username,bm.no_of_codes  as noOfCodes,bm.code_name as campaignName," +
                    " DATE_FORMAT(DATE_ADD(bm.date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y') as dateCreated from books_code_log bm where "+
                    "  date(DATE_ADD(bm.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + inputFromDate + "','%d-%m-%Y')" +
                    " and date(DATE_ADD(bm.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + inputToDate + "','%d-%m-%Y')" +
                    " and bm.site_id="+siteId+
                    " order by bm.date_created desc"

        }
        println(sql)
        def res = sql1.rows(sql)
        String bookTitle = ""
        String createdBy=""
        def data = res.collect { codes ->
            bookTitle = ""
            createdBy = ""
            if(codes.bookId!=null){
                BooksMst booksMst = dataProviderService.getBooksMst(new Integer(""+codes.bookId))
                if(booksMst!=null) bookTitle = booksMst.title
            }
            if(codes.username!=null){
                User user = dataProviderService.getUserMst(codes.username)
                if(user!=null) createdBy = user.name
            }
            return [
                    bookId: codes.bookId, title: bookTitle,  name: createdBy,
                    dateCreated: codes.dateCreated,campaignName:codes.campaignName,noOfCodes:codes.noOfCodes
            ]
        }
        return data
    }

}
