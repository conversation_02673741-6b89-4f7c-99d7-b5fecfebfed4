package com.wonderslate.admin

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.razorpay.Payment
import com.razorpay.RazorpayClient
import com.wonderslate.DataNotificationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.EmailMst
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.RelatedVideos
import com.wonderslate.data.RelatedVideosNew
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.information.InformationMst
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.log.BooksCodeLog
import com.wonderslate.log.TeacherNomineeDtl
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.ExamMst
import com.wonderslate.shop.DiscountBookDtl
import com.wonderslate.shop.DiscountMst
import com.wonderslate.shop.DiscountPriceDtl
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import groovy.sql.Sql
import net.minidev.json.JSONArray
import net.minidev.json.parser.JSONParser
import org.grails.web.json.JSONObject

import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.stream.Collectors

@Transactional
class AdminService {
    SpringSecurityService springSecurityService
    UtilService utilService
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    def grailsApplication
    def redisService


    def addDiscount(params,request,session,siteId){
        List discountData=null
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date endDate = null
        Date startDate = null
        def allBooks=null
        String invalidBookId=""
        String refDiscountId=""
        String existCouponCode=""
        def couponCode=params.couponCode
        if((couponCode!=null && !"".equals(couponCode)  && !DiscountMst.findByCouponCodeAndStatusAndSiteId(couponCode,'active',siteId)) || "Reference Code".equals(params.type)) {
            if (params.discountData != null && !"".equals(params.discountData)) discountData = JSON.parse(params.discountData);
            DiscountPriceDtl discountPriceDtl
            if (params.endDate != null && params.endDate != "") {
                endDate = df.parse(params.endDate);
            }
            if ("true".equals(params.allBooks)) allBooks = "true"
            if (params.startDate != null && params.startDate != "") startDate = df.parse(params.startDate)
            if (!"".equals(params.bookId) && params.bookId != null && !"true".equals(allBooks) && discountData == null) {
                String[] books = params.bookId.split(",")
                for (int i = 0; i < books.length; i++) {
                    BooksMst booksMst = BooksMst.findByIdAndSiteIdAndStatus(new Long(books[i]), utilService.getSiteId(request, session), 'published')
                    if (booksMst != null) {
                        DiscountMst discountMst = new DiscountMst(type: params.type, name: params.discountName, startDate: startDate, endDate: endDate, allBooks: allBooks, status: "active", createdBy: springSecurityService.currentUser.username, couponCode: couponCode ? couponCode : null,siteId: siteId)
                        discountMst.save(failOnError: true, flush: true)
                        DiscountBookDtl discountBookDtl = new DiscountBookDtl(discountId: discountMst.id, bookId: books[i])
                        discountBookDtl.save(failOnError: true, flush: true)
                        discountPriceDtl = new DiscountPriceDtl(discountId: discountMst.id, discountValue: params.discountValue, discountPercentage: params.discountPercentage, spentUpto: params.spentUpto)
                        discountPriceDtl.save(failOnError: true, flush: true)
                    } else {
                        invalidBookId += " " + books[i] + ","
                    }
                }
            } else if (discountData != null) {
                for (int i = 0; i <= discountData.size() - 1; i++) {
                    if (discountData[i].spentupto != "" && discountData[i].spentupto != null) {
                        if (!"".equals(params.bookId) && params.bookId != null) {
                            String[] books = params.bookId.split(",")
                            for (int j = 0; j < books.length; j++) {
                                BooksMst booksMst = BooksMst.findByIdAndSiteIdAndStatus(new Long(books[j]), utilService.getSiteId(request, session), 'published')
                                if (booksMst != null) {
                                    DiscountMst discountMst = new DiscountMst(type: params.type, name: params.discountName, startDate: startDate, endDate: endDate, allBooks: allBooks, status: "active", createdBy: springSecurityService.currentUser.username, couponCode: couponCode ? couponCode : null,siteId: siteId)
                                    discountMst.save(failOnError: true, flush: true)
                                    DiscountBookDtl discountBookDtl = new DiscountBookDtl(discountId: discountMst.id, bookId: books[j])
                                    discountBookDtl.save(failOnError: true, flush: true)
                                    discountPriceDtl = new DiscountPriceDtl(discountId: discountMst.id, discountValue: discountData[i].discvalue, discountPercentage: discountData[i].discperc, spentUpto: discountData[i].spentupto)
                                    discountPriceDtl.save(failOnError: true, flush: true)
                                } else {
                                    invalidBookId += " " + books[j] + ","
                                }
                            }
                        } else {
                            DiscountMst discountMst = new DiscountMst(type: params.type, name: params.discountName, startDate: startDate, endDate: endDate, allBooks: allBooks, status: "active", createdBy: springSecurityService.currentUser.username, couponCode: couponCode ? couponCode : null,siteId: siteId)
                            discountMst.save(failOnError: true, flush: true)
                            discountPriceDtl = new DiscountPriceDtl(discountId: discountMst.id, discountValue: discountData[i].discvalue, discountPercentage: discountData[i].discperc, spentUpto: discountData[i].spentupto)
                            discountPriceDtl.save(failOnError: true, flush: true)
                        }
                    }
                }
            } else {
                if ("Reference Code".equals(params.type)) {
                    List referenceCode = DiscountMst.findAllByTypeAndSiteId('Reference Code', siteId)
                    if (referenceCode.size() > 0) {
                        for (int i = 0; i <= referenceCode.size() - 1; i++) {
                            refDiscountId += " " + referenceCode[i].id + ","
                        }
                        DiscountMst.executeUpdate("update DiscountMst set status='closed' where site_id ="+siteId+" and id IN (" + refDiscountId.substring(0, refDiscountId.length() - 1) + ")")
                    }

                }
                DiscountMst discountMst = new DiscountMst(type: params.type, name: params.discountName, startDate: startDate, endDate: endDate, allBooks: allBooks, status: "active", createdBy: springSecurityService.currentUser.username, couponCode: couponCode ? couponCode : null,siteId: siteId)
                discountMst.save(failOnError: true, flush: true)
                discountPriceDtl = new DiscountPriceDtl(discountId: discountMst.id, discountValue: params.discountValue, discountPercentage: params.discountPercentage)
                discountPriceDtl.save(failOnError: true, flush: true)
            }
        }else{
            existCouponCode="codeExist"
        }

        def json = ['status':"OK",'invalidBookId': invalidBookId ? String.join(",",Arrays.asList(invalidBookId.split(",")).stream().distinct().collect(Collectors.toList())):"",existCouponCode:existCouponCode]
        return json
    }

    def listDiscounts(params,siteId,session){
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String siteIds
        String sql =    "SELECT dm.id,dm.name,dm.type,dm.created_by,dm.date_created,dm.start_date,dm.end_date,dm.status,coalesce(dpd.discount_value,' ') as discount_value,coalesce(dpd.discount_percentage,' ') as discount_percentage,coalesce(dpd.spent_upto,' ') as spent_upto,coalesce(dm.coupon_code,'') as couponCode,dm.redeem_count, COALESCE(ps.name, '') as publisher_name" +
                " FROM discount_mst dm INNER  join discount_price_dtl dpd ON dm.id=dpd.discount_id LEFT JOIN publishers ps ON dm.publisher_id = ps.id " ;
        if(!"".equals(params.addStatus) && params.addStatus!=null){
            sql += " WHERE dm.status='"+params.addStatus+"'";
        }

        if (siteMst.allBooksLibrary=="true"){
            siteIds = siteId
        }else if(siteId == 1){
            siteIds = getSiteIdList(siteId)
        }else {
            siteIds = siteId+","+"1"
        }
        if(siteMst.allBooksLibrary!="true" && session["userdetails"].publisherId!=null) sql +=" and dm.publisher_id="+session["userdetails"].publisherId
        sql += " and dm.site_id IN ("+siteIds+")";
        sql +=  " order by dm.id desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List data = results.collect {
            return [
                    discountId         : it.id,
                    name         : it.name,
                    couponCode        : it.couponCode,
                    type: it.type,
                    createdBy:dataProviderService.getUserMst(it.created_by).name,
                    dateCreated: it.date_created!=null?(new SimpleDateFormat("dd-MM-yyyy")).format(it.date_created):"",
                    startDate:  it.start_date!=null?(new SimpleDateFormat("dd-MM-yyyy")).format(it.start_date):"",
                    endDate:  it.end_date!=null?(new SimpleDateFormat("dd-MM-yyyy")).format(it.end_date):"",
                    status: it.status,
                    discountValue: it.discount_value,
                    discountPercentage: it.discount_percentage,
                    spentUpto: it.spent_upto,
                    bookId: DiscountBookDtl.findByDiscountId(new Long(it.id))?DiscountBookDtl.findByDiscountId(new Long(it.id)).bookId:"All Books",
                    redeemCount: it.redeem_count,
                    publisherId: it.publisher_name
            ]

        }
        def json = ['status': data?"OK":"norecords",'discountList':data]
        return json
    }

    def closeDiscountById(params){
        DiscountMst discountMst=DiscountMst.findById(new Long(params.discountId))
        discountMst.status="closed"
        discountMst.closedBy=springSecurityService.currentUser.username
        discountMst.save(failOnError: true, flush: true)
        def json = ['status': "OK"]
        return json
    }


        
    def saveInformation(InformationMst informationMst ) {
        def responseMessage
        try {
            informationMst.save (failOnError: true, flush: true)
            resetCacheForResType(informationMst.resourceType)
            String pattern = "dd-MM-yyyy";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);

            String inputDate = simpleDateFormat.format(informationMst.dateCreated);

            if("CURRENT AFFAIRS".equals(informationMst.resourceType)) {
                getCurrentAffairsLatestAndStartDates(informationMst.currentAffairsType)
                getCurrentAffairsReadingMaterials(inputDate,informationMst.currentAffairsType)
                dataNotificationService.prepjoyContentNotification(informationMst.currentAffairsType)
            }
            if("CURRENT AFFAIRS VIDEOS".equals(informationMst.resourceType)) {
                getCurrentAffairsLatestAndStartDates(informationMst.currentAffairsType)
                getCurrentAffairsVideos(inputDate,informationMst.currentAffairsType)
                dataNotificationService.prepjoyContentNotification(informationMst.currentAffairsType)
            }
            if("CURRENT AFFAIRS AUDIO".equals(informationMst.resourceType)) {
                getCurrentAffairsLatestAndStartDates(informationMst.currentAffairsType)
                getCurrentAffairsAudios(inputDate,informationMst.currentAffairsType)
                dataNotificationService.prepjoyContentNotification(informationMst.currentAffairsType)
            }
            responseMessage = ["status": "Success"]
        } catch(Exception e){
            responseMessage = ["error" : e.message, "status": "Fail"]
        }
        return responseMessage
    }

    def getInformationList(int pageNo, String resourceType){
        int noOfItems = 20
        int startingIndex = noOfItems*pageNo
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "SELECT id,title,description,resource_type,show_full_details, " +
                "COALESCE(reference_link,''),COALESCE(answer,''),show_answer,COALESCE(video_link,''),COALESCE(deep_link,''), DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y'), " +
                "COALESCE(level,''),COALESCE( syllabus,''),COALESCE( grade,''),COALESCE( subject,'') " +
                "FROM information_mst im WHERE resource_type= '"+ resourceType+"' order by id desc limit "+startingIndex+","+noOfItems
        def results = sql1.rows(sql).collect{info->
            return [
                    id:info[0], title: info[1], description: info[2], resourceType: info[3], showFullDetails: info[4],
                    referenceLink: info[5], answer: info[6],showAnswer: info[7],videoLink: info[8],deepLink: info[9], dateCreated: info[10],
                    tag:[level: info[11],syllabus: info[12],grade: info[13],subject: info[14]],
            ]
        }

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("info_"+resourceType.replaceAll(" ","-")+"_List_"+pageNo) = element

    }

    def resetCacheForResType(String resourceType){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "SELECT count(*) " +
                "FROM information_mst im WHERE resource_type= '"+ resourceType+"' "
        def  results = sql1.rows(sql);
        int numberOfItems = Integer.parseInt(""+results[0][0]).intValue()
        int noOfRedisItems = numberOfItems / 20
        for(int i=0;i<=noOfRedisItems;i++){
            redisService.("info_"+resourceType.replaceAll(" ","-")+"_List_"+i)=null
        }
    }

   def getInformationDetails(Integer id,String resType){
       String sql
       def dataSource = grailsApplication.mainContext.getBean('dataSource')
       def sql1 = new Sql(dataSource)

       //for sql data
       sql = "SELECT id,title,description,resource_type,show_full_details, " +
               "COALESCE(reference_link,''),COALESCE(answer,''),show_answer,COALESCE(video_link,''),COALESCE(deep_link,''), DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y'), " +
               "COALESCE(level,''),COALESCE( syllabus,''),COALESCE( grade,''),COALESCE( subject,''),'actual'  " +
               " from information_mst where resource_type='" +resType+"' "+
               "AND (" +
               "    id = " +id+
               "    OR id = (" +
               "      SELECT MAX(id)" +
               "      FROM information_mst " +
               "      WHERE resource_type = '"+resType+"' AND id <" +id+
               "    )" +
               "    OR id = (" +
               "      SELECT MIN(id)\n" +
               "      FROM information_mst" +
               "      WHERE resource_type = '"+resType+"' AND id > " +id+
               "    )" +
               "  ) order by id"
        def results = sql1.rows(sql).collect{info->
           return [
                   id:info[0], title: info[1], description: info[2], resourceType: info[3], showFullDetails: info[4],
                   referenceLink: info[5], answer: info[6],showAnswer: info[7],videoLink: info[8],deepLink: info[9], dateCreated: info[10],
                   tag:[level: info[11],syllabus: info[12],grade: info[13],subject: info[14]], position:info[15]
           ]
       }

       Gson gson = new Gson();
       String element = gson.toJson(results,new TypeToken<List>() {}.getType())
       redisService.("infoDetails_"+id) = element
   }

    def getDiscountForUser(params,siteId){
        double amount=0
        String status="OK"
        String couponCode=params.couponCode
        String username=siteId+"_"+params.username
        double discountValue=0
        String discountId=""
        Integer discountPercentage=0
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))

        if(couponCode !=null && couponCode.matches('^[0-9]*$') && couponCode.length() == 10) {
            PurchaseOrder purchaseOrder = PurchaseOrder.findByUsernameAndSiteId(siteId + "_" + couponCode, siteId)
            if(purchaseOrder!=null){
                DiscountMst discountMst =DiscountMst.findByTypeAndStatusAndStartDateLessThanEquals('Reference Code','active',new Date())
                if (discountMst != null) {
                    DiscountPriceDtl discountPriceDtl = DiscountPriceDtl.findByDiscountId(discountMst.id)
                    if (discountPriceDtl != null) {
                        if (discountPriceDtl.discountValue != null) {
                            discountValue = discountPriceDtl.discountValue
                        } else if (discountPriceDtl.discountPercentage != null) {
                            discountPercentage=discountPriceDtl.discountPercentage
                            //calcalute the amount by percentage
                            Integer perc = discountPriceDtl.discountPercentage;
                            discountValue = ((booksMst.price * perc) / 100)
                        }
                    }
                    discountId = discountMst.id
                }
            }
        }else{
            def sql = "SELECT dm.id,dm.type "+
                    " FROM wsshop.discount_mst dm" +
                    "  INNER JOIN wsshop.discount_price_dtl dpd ON dpd.discount_id=dm.id   LEFT JOIN wsshop.discount_book_dtl  dbd ON dbd.discount_id=dm.id  WHERE  dm.type != 'Reference Code' and dm.status = 'active' and dm.start_date <= SYSDATE() and (dm.all_books = 'true' OR dbd.book_id = " + params.bookId + ") and dm.coupon_code= '" + params.couponCode + "' and dm.site_id= '" + siteId + "' LIMIT 1"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            if(results.size()==1 && results[0][1]=="New Users"){
                PurchaseOrder purchaseOrder = PurchaseOrder.findByUsernameAndSiteId(username,siteId)
                if(purchaseOrder==null){
                    DiscountPriceDtl discountPriceDtl =DiscountPriceDtl.findByDiscountId(results[0][0])
                    if(discountPriceDtl !=null){
                        if(discountPriceDtl.discountValue!=null){
                            discountValue=discountPriceDtl.discountValue
                        }else if(discountPriceDtl.discountPercentage!=null){
                            discountPercentage=discountPriceDtl.discountPercentage
                            //calcalute the amount by percentage
                            Integer perc= discountPriceDtl.discountPercentage;
                            discountValue=((booksMst.price * perc) / 100)
                        }
                    }
                    discountId=results[0][0]
                }
            }else if(results.size()==1 && results[0][1]=="Book Level"){
                DiscountPriceDtl discountPriceDtl =DiscountPriceDtl.findByDiscountId(results[0][0]);
                if(discountPriceDtl !=null && discountPriceDtl.spentUpto==null){
                    if(discountPriceDtl.discountValue!=null){
                        discountValue=discountPriceDtl.discountValue
                    }else if(discountPriceDtl.discountPercentage!=null){
                        discountPercentage=discountPriceDtl.discountPercentage
                        //calcalute the amount by percentage
                        Integer perc= discountPriceDtl.discountPercentage;
                        discountValue=((booksMst.price * perc) / 100)
                    }
                    discountId=results[0][0]
                }else if(discountPriceDtl !=null && discountPriceDtl.spentUpto!=null){
                    //spent upto logic starts here
                    String spentUptoUser="select SUM(amount) AS TotalItemsOrdered FROM wsshop.purchase_order where username='" + username + "'";
                    def dataSourceUptoUser = grailsApplication.mainContext.getBean('dataSource_wsshop')
                    def sqlUptoUser = new Sql(dataSourceUptoUser)
                    def resultUptoUser = sqlUptoUser.rows(spentUptoUser)
                    if(resultUptoUser[0][0]!=null && resultUptoUser.size()==1) {
                        amount=resultUptoUser[0][0]
                    }

                    if(amount!=0 && amount!=0.0) {
                        def spentUptoAmountResults = getSpentUpto("Book Level","discount_value",amount,params)
                        if(spentUptoAmountResults.size()>=1) {
                            discountId=spentUptoAmountResults[0][0]
                            discountValue= new Double(spentUptoAmountResults[0][1])
                        }
                        def spentUptoPercentageResults = getSpentUpto("Book Level","discount_percentage",amount,params)
                        if(spentUptoPercentageResults.size()>=1) {
                            discountPercentage=spentUptoPercentageResults[0][0]
                            discountId=spentUptoPercentageResults[0][0]
                            //calcalute the amount by percentage
                            Integer perc= spentUptoPercentageResults[0][1]
                            discountValue=((booksMst.price * perc) / 100)
                        }
                    }

                }

            }
        }
        String discountedPrice = String.format("%.2f",discountValue)
        def json =['status':status,discountValue:discountedPrice,discountId:discountId,discountPercentage:discountPercentage]
        return json
    }

    def getBookDiscount(params,siteId) {
        double discountValue = 0

        Integer discountPercentage = 0
        String discountedPrice
        String couponCode=""
        String siteIds
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))

        if (siteId == 21){
            siteIds = siteId
        }else if(siteId == 1){
            siteIds = getSiteIdList(siteId)
        }else {
            siteIds = siteId+","+"1"
        }

        def sql = "SELECT dm.id,dm.type,dm.coupon_code,dm.redeem_count " +
                " FROM wsshop.discount_mst dm" +
                "  INNER JOIN wsshop.discount_price_dtl dpd ON dpd.discount_id=dm.id   LEFT JOIN wsshop.discount_book_dtl  dbd ON dbd.discount_id=dm.id  WHERE  dm.type = 'Book Level' and dm.status = 'active' and dm.start_date <= SYSDATE() and " +
                " (dm.all_books = 'true' OR dbd.book_id = " + params.bookId + ") AND (dm.publisher_id is null OR dm.publisher_id=" + booksMst.publisherId + ") AND dm.site_id IN ("+siteIds+") order by id desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List poCount
        List couponcodelist
        couponcodelist = results.collect {
            String discountId = null
            boolean  codeCount = true
            if(it.redeem_count!=null){
                Integer redeemCount= new Integer(it.redeem_count)
                 poCount=PurchaseOrder.findAllByDiscountId(new Long(it.id))
                if(redeemCount<=poCount.size()) codeCount=false
            }
            DiscountPriceDtl discountPriceDtl = DiscountPriceDtl.findByDiscountId(it.id);
            if (discountPriceDtl != null && codeCount) {
                if (discountPriceDtl.discountValue != null) {
                    discountValue = discountPriceDtl.discountValue
                } else if (discountPriceDtl.discountPercentage != null) {
                    discountPercentage = discountPriceDtl.discountPercentage
                    //calcalute the amount by percentage
                    Integer perc = discountPriceDtl.discountPercentage;
                    discountValue = ((booksMst.price * perc) / 100)
                }
                couponCode = it.coupon_code
                discountId = it.id
                discountedPrice = String.format("%.2f",discountValue)
            }
          if(discountId!=null) {
             return [discountValue: discountedPrice, discountId: discountId, discountPercentage: discountPercentage, couponCode: couponCode]
          }
        }
        while (couponcodelist.remove(null)) {
        }
        def json =['status':couponcodelist?"OK":"No records",bookDiscounts:couponcodelist]
        return json
    }



    def checkManualCoupon(params,siteId){
        double discountValue = 0

        Integer discountPercentage = 0
        String discountedPrice
        String couponCode=""
        String siteIds
        BooksMst booksMst = BooksMst.findById(new Long(params.bookId))

        if (siteId == 21){
            siteIds = siteId
        }else if(siteId == 1){
            siteIds = getSiteIdList(siteId)
        }else{
            siteIds = siteId+","+"1"
        }

        def sql = "SELECT dm.id,dm.type,dm.coupon_code,dm.redeem_count " +
                " FROM wsshop.discount_mst dm" +
                " INNER JOIN wsshop.discount_price_dtl dpd ON dpd.discount_id=dm.id   LEFT JOIN wsshop.discount_book_dtl  dbd ON dbd.discount_id=dm.id  WHERE  dm.type = 'Book Level' and dm.status = 'active' and dm.start_date <= SYSDATE() " +
                " and (dm.all_books = 'true' OR dbd.book_id = " + params.bookId + ") and  (dm.publisher_id is null OR dm.publisher_id=" + booksMst.publisherId + ") and dm.coupon_code='"+params.coupon+"' and dm.site_id IN ("+siteIds+")"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List poCount
        List couponcodelist
        couponcodelist = results.collect {
            String discountId = null
            boolean  codeCount = true
            if(it.redeem_count!=null){
                Integer redeemCount= new Integer(it.redeem_count)
                poCount=PurchaseOrder.findAllByDiscountId(new Long(it.id))
                if(redeemCount<=poCount.size()) codeCount=false
            }
            DiscountPriceDtl discountPriceDtl = DiscountPriceDtl.findByDiscountId(it.id);
            if (discountPriceDtl != null && codeCount) {
                if (discountPriceDtl.discountValue != null) {
                    discountValue = discountPriceDtl.discountValue
                } else if (discountPriceDtl.discountPercentage != null) {
                    discountPercentage = discountPriceDtl.discountPercentage
                    //calcalute the amount by percentage
                    Integer perc = discountPriceDtl.discountPercentage;
                    discountValue = ((booksMst.price * perc) / 100)
                }
                couponCode = it.coupon_code
                discountId = it.id
                discountedPrice = String.format("%.2f",discountValue)
            }
            if(discountId!=null) {
                return [discountValue: discountedPrice, discountId: discountId, discountPercentage: discountPercentage, couponCode: couponCode]
            }
        }
        while (couponcodelist.remove(null)) {
        }
        def json =['status':couponcodelist?"OK":"No records","manualCoupons":couponcodelist]
        return json
    }


    def getSpentUpto(String type,String columnName,Double amount,params){
        String sql
        String spentUptoSql
        String maxspentUptoSql
        def results=null
            spentUptoSql = "SELECT discount_id," + columnName + " " +
                    " FROM wsshop.discount_price_dtl dpd,wsshop.discount_mst dm " +
                    " WHERE  dpd.discount_id=dm.id and dm.type='" + type + "' and dm.coupon_code= '" + params.couponCode + "' and dm.status = 'active' and dm.start_date <= SYSDATE() and " + columnName + " =(select greatest(COALESCE((SELECT  MIN(" + columnName + ")" +
                    " FROM  wsshop.discount_price_dtl dpd, wsshop.discount_book_dtl dbd, wsshop.discount_mst dm" +
                    " WHERE" +
                    " dbd.discount_id = dpd.discount_id  AND dpd.discount_id = dm.id  AND dm.type = '" + type + "' and dm.coupon_code= '" + params.couponCode + "' AND dm.status = 'active' and  dm.start_date <= SYSDATE() AND dbd.book_id = " + params.bookId + " and  dpd.spent_upto>=" + amount + "),0)" +
                    ", COALESCE((SELECT MIN(" + columnName + ")  FROM  wsshop.discount_price_dtl dpd,  wsshop.discount_mst dm" +
                    " WHERE  dpd.discount_id = dm.id  AND dm.type = '" + type + "' and  dm.coupon_code= '" + params.couponCode + "' AND dm.status = 'active' and  dm.start_date <= SYSDATE() AND dm.all_books = 'true' and  dpd.spent_upto>=" + amount + "),0))) ORDER BY discount_id " +
                    " LIMIT 1";
            def dataSourcespentUptoSql = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1spentUptoSql = new Sql(dataSourcespentUptoSql)
            def resultsspentUptoSql = sql1spentUptoSql.rows(spentUptoSql);
            if(resultsspentUptoSql.size()==0) {
                maxspentUptoSql = "SELECT discount_id," + columnName + " " +
                        " FROM wsshop.discount_price_dtl dpd,wsshop.discount_mst dm " +
                        " WHERE  dpd.discount_id=dm.id and dm.type='" + type + "'  and dm.coupon_code= '" + params.couponCode + "' and dm.status = 'active' and dm.start_date <= SYSDATE()  and " + columnName + " =(select greatest(COALESCE((SELECT  MAX(" + columnName + ")" +
                        " FROM  wsshop.discount_price_dtl dpd, wsshop.discount_book_dtl dbd, wsshop.discount_mst dm" +
                        " WHERE" +
                        " dbd.discount_id = dpd.discount_id  AND dpd.discount_id = dm.id  AND dm.type = '" + type + "'  and dm.coupon_code= '" + params.couponCode + "' AND dm.status = 'active' and dm.start_date <= SYSDATE() AND dbd.book_id = " + params.bookId + " and  dpd.spent_upto <= " + amount + "),0)" +
                        ", COALESCE((SELECT MAX(" + columnName + ")  FROM  wsshop.discount_price_dtl dpd,  wsshop.discount_mst dm" +
                        " WHERE  dpd.discount_id = dm.id  AND dm.type = '" + type + "'  and  dm.coupon_code= '" + params.couponCode + "' AND dm.status = 'active'  and dm.start_date <= SYSDATE() AND dm.all_books = 'true' and  dpd.spent_upto <= " + amount + "),0))) ORDER BY discount_id " +
                        " LIMIT 1";
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1 = new Sql(dataSource)
                results = sql1.rows(maxspentUptoSql);

            }else{
                results = resultsspentUptoSql;
            }

        return results
       }


    def getCurrentAffairsLatestAndStartDates(String currentAffairsType){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "SELECT DATE(date_created) as date_created FROM information_mst where resource_type in ('CURRENT AFFAIRS','CURRENT AFFAIRS VIDEOS','CURRENT AFFAIRS AUDIO') and current_affairs_type='"+currentAffairsType+"' " +
                " union\n" +
                " select DATE(date_created) as date_created from resource_dtl where res_sub_type='Current Affairs' and res_type='Multiple Choice Questions' and current_affairs_type='" +currentAffairsType+"' " +
                " order by date_created desc\n" +
                " limit 1"
        def results = sql1.rows(sql);
        def latestDate = results[0][0];

        sql = "SELECT DATE(date_created) as date_created FROM information_mst where resource_type in ('CURRENT AFFAIRS','CURRENT AFFAIRS VIDEOS','CURRENT AFFAIRS AUDIO') and current_affairs_type='"+currentAffairsType+"'" +
                " union\n" +
                " select DATE(date_created) as date_created from resource_dtl where res_sub_type='Current Affairs' and res_type='Multiple Choice Questions' and current_affairs_type='"+currentAffairsType+"' " +
                " order by date_created asc\n" +
                " limit 1"
        results = sql1.rows(sql);

        def startingDate = results[0][0]
        redisService.("currentAffairsLatestDate"+"_"+currentAffairsType) = latestDate
        redisService.("currentAffairsStartingDate"+"_"+currentAffairsType) = startingDate
    }

    def getCurrentAffairsQuizId(String inputDate, String currentAffairsType){

        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "select id from resource_dtl where res_sub_type='Current Affairs' and res_type='Multiple Choice Questions'  and current_affairs_type='"+currentAffairsType+"'"+
                " and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('"+inputDate+"','%d-%m-%Y')" +
                " limit 1"

        def results = sql1.rows(sql);
        Integer resId = new Integer(-1)
        if(results.size()>0) resId = results[0][0]

        redisService.("currentAffairsQuiz_"+inputDate+"_"+currentAffairsType) = ""+resId

    }

    def getCurrentAffairsReadingMaterials(String inputDate,String currentAffairsType){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "SELECT id,title,description,resource_type,show_full_details, " +
                " COALESCE(reference_link,''),COALESCE(answer,''),show_answer,COALESCE(video_link,''),COALESCE(deep_link,''), DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y'), " +
                " COALESCE(level,''),COALESCE( syllabus,''),COALESCE( grade,''),COALESCE( subject,''),COALESCE( plain_description,''),COALESCE( language,'') " +
                " FROM information_mst im WHERE resource_type= 'CURRENT AFFAIRS' " +
                " and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('"+inputDate+"','%d-%m-%Y')" +
                " and current_affairs_type='"+currentAffairsType+"' " +
                " order by id desc  "
        def results = sql1.rows(sql).collect{info->
            return [
                    id:info[0], title: info[1], description: info[2], resourceType: info[3], showFullDetails: info[4],
                    referenceLink: info[5], answer: info[6],showAnswer: info[7],videoLink: info[8],deepLink: info[9], dateCreated: info[10],
                    tag:[level: info[11],syllabus: info[12],grade: info[13],subject: info[14],plainDescription:info[15],language:info[16]],
            ]
        }

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("currentAffairsRead_"+inputDate+"_"+currentAffairsType) = element
    }

    def getCurrentAffairsVideos(String inputDate,String currentAffairsType){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "SELECT id,title,description,resource_type,show_full_details, " +
                " COALESCE(reference_link,''),COALESCE(answer,''),show_answer,COALESCE(video_link,''),COALESCE(deep_link,''), DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y'), " +
                " COALESCE(level,''),COALESCE( syllabus,''),COALESCE( grade,''),COALESCE( subject,''),COALESCE( language,'') " +
                " FROM information_mst im WHERE resource_type= 'CURRENT AFFAIRS VIDEOS' " +
                " and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('"+inputDate+"','%d-%m-%Y')" +
                " and current_affairs_type='"+currentAffairsType+"' " +
                " order by id desc  "

        def results = sql1.rows(sql).collect{info->
            return [
                    id:info[0], title: info[1], description: info[2], resourceType: info[3], showFullDetails: info[4],
                    referenceLink: info[5], answer: info[6],showAnswer: info[7],videoLink: info[8],deepLink: info[9], dateCreated: info[10],
                    tag:[level: info[11],syllabus: info[12],grade: info[13],subject: info[14],language:info[15]],
            ]
        }

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("currentAffairsVideos_"+inputDate+"_"+currentAffairsType) = element
    }

    def getCurrentAffairsAudios(String inputDate,String currentAffairsType){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "SELECT id,title,description,resource_type,show_full_details, " +
                " COALESCE(reference_link,''),COALESCE(answer,''),show_answer,COALESCE(video_link,''),COALESCE(deep_link,''), DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y'), " +
                " COALESCE(level,''),COALESCE( syllabus,''),COALESCE( grade,''),COALESCE( subject,''),COALESCE( language,'') " +
                " FROM information_mst im WHERE resource_type= 'CURRENT AFFAIRS AUDIO' " +
                " and date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('"+inputDate+"','%d-%m-%Y')" +
                " and current_affairs_type='"+currentAffairsType+"'" +
                " order by id desc  "

        def results = sql1.rows(sql).collect{info->
            return [
                    id:info[0], title: info[1], description: info[2], resourceType: info[3], showFullDetails: info[4],
                    referenceLink: info[5], answer: info[6],showAnswer: info[7],videoLink: info[8],deepLink: info[9], dateCreated: info[10],
                    tag:[level: info[11],syllabus: info[12],grade: info[13],subject: info[14],language:info[15]],
            ]
        }

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("currentAffairsAudios_"+inputDate+"_"+currentAffairsType) = element
    }

    def getUsedAccessCodes(String bookId = null, String inputFromDate, String inputToDate, Integer siteId) {

            String sql
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            String bookCode=""
            if (!bookId) {
                sql = "SELECT bm.id as bookId,bm.title as bookTitle,bp.book_code as bookCode,u.name as userName,coalesce(u.email,'') as userMail,coalesce(u.mobile,'') as mobile," +
                        " DATE_FORMAT(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y') as date_redeemed,bp.book_code_log_id as bookCodeLogId  " +
                        "from user u, wslog.books_code_usage_log bp,books_mst bm where bp.book_code is not null and bp.username=u.username and bm.id=bp.book_id" +
                        " and date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + inputFromDate + "','%d-%m-%Y')" +
                        " and date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + inputToDate + "','%d-%m-%Y')" +
                        " and bm.site_id="+siteId+
                        " order by bp.date_created desc"
            } else {
                sql = "SELECT bm.id as bookId,bm.title as bookTitle,bp.book_code as bookCode,u.name as userName,coalesce(u.email,'') as userMail,coalesce(u.mobile,'') as mobile," +
                        " DATE_FORMAT(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y') as date_redeemed,bp.book_code_log_id as bookCodeLogId " +
                        "from user u, wslog.books_code_usage_log bp,books_mst bm where bp.book_id=" + bookId + " and bp.book_code is not null and bp.username=u.username and bm.id=bp.book_id" +
                        " and date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + inputFromDate + "','%d-%m-%Y')" +
                        " and date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + inputToDate + "','%d-%m-%Y')" +
                        " and bm.site_id="+siteId+
                        " order by bp.date_created desc"

            }
            def res = sql1.rows(sql)
            String campaignName = ""
            def data = res.collect { codes ->
                bookCode=codes[2]
                campaignName = ""
                if(codes.bookCodeLogId!=null){
                    BooksCodeLog booksCodeLog = BooksCodeLog.findById(new Integer(""+codes.bookCodeLogId))
                    if(booksCodeLog!=null) campaignName = booksCodeLog.codeName
                }
                return [

                        bookId: codes[0], title: codes[1], accessCode: bookCode.length()>=13?bookCode:codes[2]+codes[0], name: codes[3], email: codes[4], mobile: codes[5],
                        dateRedeemed: codes[6],campaignName:campaignName
                ]
            }
        return data
    }

    def deleteRelatedVid(String chapterId,String videoId){
        RelatedVideosNew relatedVideos = RelatedVideosNew.findByChapterIdAndVideoId(new Long(chapterId),videoId)
        if(relatedVideos!=null) relatedVideos.delete(flush: true)
        dataProviderService.getRelatedVideosFromDB(chapterId)
        return true
    }

    def deleteDuplicateRelatedVideos(String chapterId) {
        def responseMessage
        try {
            // Find all related videos for the chapter
            List<RelatedVideosNew> allVideos = RelatedVideosNew.findAllByChapterId(new Integer(chapterId))

            if (allVideos == null || allVideos.size() == 0) {
                return ["status": "No videos found", "deletedCount": 0]
            }

            // Group videos by videoId
            Map<String, List<RelatedVideosNew>> videoGroups = allVideos.groupBy { it.videoId }

            int deletedCount = 0

            // For each group, keep the first record and delete the rest
            videoGroups.each { videoId, videos ->
                if (videos.size() > 1) {
                    // Sort by id to keep the oldest record (first created)
                    videos.sort { it.id }

                    // Delete all except the first one
                    for (int i = 1; i < videos.size(); i++) {
                        videos[i].delete(flush: true)
                        deletedCount++
                    }
                }
            }

            // Refresh cache
            dataProviderService.getRelatedVideosFromDB(chapterId)

            responseMessage = ["status": "Success", "deletedCount": deletedCount, "message": "Deleted " + deletedCount + " duplicate videos"]
        } catch(Exception e) {
            responseMessage = ["status": "Error", "message": e.message, "deletedCount": 0]
        }
        return responseMessage
    }

    def jwplayerLive(params,siteId){
            def json
            def search = params."search[value]";
            String sql = "select rd.id,rd.resource_name,rd.chapter_id,DATE_ADD( rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE) start_time,DATE_ADD( rd.test_end_date, INTERVAL '5:30' HOUR_MINUTE) end_time,cm.book_id,rd.channel_Id,rd.stream_key" +
                    " from resource_dtl rd,chapters_mst cm where cm.id=rd.chapter_id" ;
            if(search!=null && search!="") {
                sql += " and  (rd.resource_name  LIKE '%" + search + "%' OR rd.id  LIKE '%" + search + "%')";
            }
            sql +=   " and  rd.video_player='custom' and rd.res_link='blank' and rd.site_id="+siteId;
            def sqlCount = "select count(rd.id) from resource_dtl rd,chapters_mst cm where cm.id=rd.chapter_id " ;
            if(search!=null && search!="") {
                sqlCount += " and  (rd.resource_name  LIKE '%" + search + "%' OR rd.id  LIKE '%" + search + "%')";
            }
            sqlCount +=    " and rd.video_player='custom' and rd.res_link='blank' and rd.site_id="+siteId;
            def dataSourceCount = grailsApplication.mainContext.getBean('dataSource')
            def sqlCount1 = new Sql(dataSourceCount)
            def countResults = sqlCount1.rows(sqlCount)
            def count = countResults.size() > 0 ? countResults.get(0).values() : 0
            sql += " order by rd.id desc limit "+ params.start+" , "+params.length+""
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            List quizlist = new ArrayList()
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy:MM:dd HH:mm")
            if (results != null && results.size() > 0) {
                quizlist = results.collect { comp ->
                    return [id: comp[0], resourceName: comp[1],chapterId:comp[2], testStartDate: comp.start_time?dateFormat.format(comp.start_time):"",testEndDate: comp.end_time?dateFormat.format(comp.end_time):"",bookId:comp[5],channelId:comp[6],streamKey:comp[7]]
                }
                while (quizlist.remove(null)) {
                }
            }
            return json = [status: quizlist ? "OK" : "Not present", data: quizlist, recordsTotal: count, recordsFiltered: count, draw: params.draw]
    }

    def getChannelDetailsByChannelId(params,siteId){
        def json
        SiteMst siteMst = dataProviderService.getSiteMst(new Long(siteId))
        RestBuilder rest = new RestBuilder()
        def resp = rest.get("https://api.jwplayer.com/v2/sites/"+siteMst.jwChannelId+"/channels/" + params.channelId + "") {
            accept("application/json")
            contentType("application/json")
            header("Authorization",siteMst.jwAuthId)
        }
        def mediaData = resp.json;
        if(mediaData.recent_events!=null){
            def mediaId=mediaData.recent_events.media_id[0];
            if(mediaId!=null && !"".equals(mediaId)) {
                ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
                resourceDtl.resLink = "https://content.jwplatform.com/videos/"+mediaId+"-notrbHqj.mp4"
                resourceDtl.downloadlink1="https://content.jwplatform.com/videos/"+mediaId+"-notrbHqj.mp4"
                resourceDtl.save(failOnError: true,flush: true)
                dataProviderService.resourceCacheUpdate(resourceDtl.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)
                String siteIdList = siteId.toString()
                dataProviderService.getLatestVideos(siteIdList,siteId)
                dataProviderService.getLiveVideos(siteId)
            }

        }
        return json =[channelVideoDetails:resp.json]
    }

    def createChannel(params,siteId){
        def json
        SiteMst siteMst = dataProviderService.getSiteMst(new Long(siteId))
        RestBuilder rest = new RestBuilder()
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        Gson gson = new Gson();
        def resp = rest.post("https://api.jwplayer.com/v2/sites/"+siteMst.jwChannelId+"/channels/") {
            def temp = [ metadata:[title: booksMst.title,dvr:'on']]
            String element = gson.toJson(temp)
            accept("application/json")
            contentType("application/json")
            header("Authorization",siteMst.jwAuthId)
            body(element)
        }
        def data=resp.json;
        if(data.id!=null && data.id!=""){
            ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
            resourceDtl.channelId = data.id
            resourceDtl.streamKey = data.stream_key
            resourceDtl.save(failOnError: true,flush: true)
        }

        return json =[channelDetails:resp.json]
    }

    def userBooks(params,siteId){
            def json
            String userName=siteId+"_"+params.userName
            String sql = "select bm.id,bm.title,DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created,po.username,po.payment_id" +
                    " from wsshop.books_mst bm, wsshop.purchase_order po where"+
                    " bm.id=po.item_code and po.username='"+userName+"' order by po.date_created desc limit 5"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            def date
            List userBooks = results.collect { userdt ->
                User user = dataProviderService.getUserMst(userdt.username)
                return [bookId:  userdt.id,title:userdt.title,paymentId:userdt.payment_id,dateCreated:(new SimpleDateFormat("dd-MM-yyyy  hh.mm aa")).format(userdt.date_created),email:user.email?user.email:'',mobile:user.mobile?user.mobile:'',name:user.name]
            }
            return json = [status: userBooks ? "OK" : "Not present", userBooksList: userBooks]
    }


    def getNextexamLatestAndStartDates() {
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        SiteMst siteMst = dataProviderService.getSiteMst(new Integer(29))

        //for sql data
        sql = "SELECT DATE(cm.date_created) as date_created FROM chapters_mst cm,books_mst bm  where bm.publisher_id=" + siteMst.publisherId + " and cm.book_id=bm.id and bm.status='published'"+
                " order by cm.date_created desc\n" +
                " limit 1"

        def results = sql1.rows(sql);
        def latestDate = results[0][0];

        sql = "SELECT DATE(cm.date_created) as date_created FROM chapters_mst cm,books_mst bm  where bm.publisher_id=" + siteMst.publisherId + " and cm.book_id=bm.id and bm.status='published'"+
        " order by cm.date_created asc\n" +
                " limit 1"
        results = sql1.rows(sql);

        def startingDate = results[0][0]
        redisService.("nextexamLatestDate") = latestDate
        redisService.("nextexamStartingDate") = startingDate
    }

    def getNextexamMaterials(String inputDate){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        SiteMst siteMst = dataProviderService.getSiteMst(new Integer(29))

        //for sql data
        sql = "SELECT rd.id,rd.res_type,rd.resource_name,rd.res_link from resource_dtl rd, chapters_mst cm,books_mst bm  where bm.publisher_id=" + siteMst.publisherId + " and cm.book_id=bm.id and bm.status='published'"+
                " and rd.chapter_id=cm.id and date(DATE_ADD(cm.date_created, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('"+inputDate+"','%d-%m-%Y')" +
                " order by rd.id   "
       def results = sql1.rows(sql).collect{info->
            return [resId:info.id, resType: info.res_type, resName: info.resource_name, resLink: info.res_link]

        }

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("nextexamMaterials_"+inputDate) = element
    }


    def getBatchUsersByMonths(params){
        Integer month=Integer.parseInt(params.month)
        String sql = "Select id,batch_id,username,date_created from wsuser.batch_user_dtl\n" +
                "   Where  date(sysdate()) >= DATE_ADD(date_created , INTERVAL "+month+" MONTH) \n" +
                " and  batch_id = "+params.batchId;
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List batches = results.collect { batch ->
            return [id:batch.id,batchId: batch.batch_id, username: batch.username,dateAdded:(new SimpleDateFormat("dd-MM-yyyy")).format(batch.date_created)]
        }
        def json = [ status: batches?"OK":"not found", batchUsers: batches]
        return json
    }

    def deleteBatchUsers(params){
        String sql = "Select id,batch_id,username,date_created from wsuser.batch_user_dtl where username IN ("+params.username+") and batch_id="+params.batchId;
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { batch ->
            BatchUserDtl batchUserDtl = BatchUserDtl.findById(new Long(batch.id))
            redisService.(batchUserDtl.username+"_"+"booksList")=null;
            redisService.("userBatchIds_"+batchUserDtl.username)=null;
            batchUserDtl.delete(flush: true);
        }
        def json = [ status:"OK"]
        return json
    }

    def manageBookExpiry(params,siteId){
        Integer timeDiff = 0;
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date expiryDate
        def json
        if(BooksMst.findByIdAndSiteId(new Long(params.bookId),siteId)) {
            if (params.startDate != null && params.startDate != "") expiryDate = df.parse(params.startDate)
            List purchaseOrderList = PurchaseOrder.findAllBySiteIdAndItemCode(new Integer(siteId), new Integer(params.bookId))
            purchaseOrderList.each {
                BooksPermission booksPermission = BooksPermission.findByUsernameAndBookIdAndExpiryDateIsNotNullAndExpiryDateLessThan(it.username, it.itemCode, expiryDate)
                if (booksPermission != null) {
                    booksPermission.expiryDate = expiryDate
                    booksPermission.poType = "ADDEDFORFREE"
                    booksPermission.save(failOnError: true, flush: true)
                } else if (!BooksPermission.findByUsernameAndBookIdAndExpiryDateIsNull(it.username, it.itemCode) && !BooksPermission.findByUsernameAndBookIdAndExpiryDateGreaterThan(it.username, it.itemCode, expiryDate)) {
                    BooksPermission bm = new BooksPermission()
                    bm.dateCreated = new Date()
                    bm.username = it.username
                    bm.expiryDate = expiryDate
                    bm.poType = "ADDEDFORFREE"
                    bm.addedBy = springSecurityService.currentUser.username
                    bm.bookId = new Integer(it.itemCode)
                    bm.save(failOnError: true, flush: true)
                }
                redisService.(it.username + "_" + "booksList") = null;
            }
            json=["status":"OK"]
        }else{
            json=["status":"invalid"]
        }
        return json
    }

    def paymentDetails(params,siteId){
        String razorPayId = params.razorPayId
        Integer discountId = 0
        Integer bookId = 0
        String username = ""
        String status=""
        double amount
        Payment payment = null
        try {
            RazorpayClient razorpayClient = getRazorpayClient(siteId)
            payment = razorpayClient.Payments.fetch(razorPayId)
            if (payment != null) {
                amount= payment.get("amount") / 100
                if (payment.get("notes").has("discountId")) discountId = new Integer(payment.get("notes").discountId)
                if (payment.get("notes").has("bookId")) bookId = new Integer(payment.get("notes").bookId)
                if (payment.get("notes").has("username")) username = payment.get("notes").username
                status =payment.get("status")
            }
        }catch(exception){
            payment=null
        }
        def json=[payment:payment!=null?"OK":"fail",discountId:discountId,bookId:bookId,username:username,amount:amount,status:status]
        return json

    }

    RazorpayClient getRazorpayClient(siteId){
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        return new RazorpayClient(siteMst.razorPayKeyId,siteMst.razorPaySecretKey)
    }

    def getSiteIdList(siteId){
        String siteIdList="";
        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                List sites = SiteMst.findAllByDisplayInMainSite("Y")

                sites.each{site->
                    siteIdList += site.id+","

                }

                siteIdList = siteIdList.substring(0,(siteIdList.length()-1))
                redisService.("siteIdList_"+siteId) = siteIdList
            }else{
                siteIdList = redisService.("siteIdList_"+siteId)
            }
        }else{
            siteIdList = siteId+""
            redisService.("siteIdList_"+siteId)=siteIdList
        }
        return siteIdList
    }

    def addDiscountWS(params,request,session,siteId){
        List discountData=null
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date endDate = null
        Date startDate = null
        def allBooks=null
        String invalidBookId=""
        String refDiscountId=""
        String existCouponCode=""
        def couponCode=params.couponCode
        String siteIdList;
        if(redisService.("siteIdList_"+siteId)==null) {
            getSiteIdList(siteId)
        }
        siteIdList = redisService.("siteIdList_"+siteId)

        if("".equals(couponCode)  || (!"".equals(couponCode) &&!DiscountMst.findByCouponCodeAndStatusAndSiteIdInList(couponCode,'active',Arrays.asList(siteIdList.split(","))))) {
            if (params.discountData != null && !"".equals(params.discountData)) discountData = JSON.parse(params.discountData);
            DiscountPriceDtl discountPriceDtl
            if (params.endDate != null && params.endDate != "") {
                endDate = df.parse(params.endDate);
            }
            if ("true".equals(params.allBooks)) allBooks = "true"
            if (params.startDate != null && params.startDate != "") startDate = df.parse(params.startDate)
            if (!"".equals(params.bookId) && params.bookId != null && !"true".equals(allBooks) && discountData == null) {
                String[] books = params.bookId.split(",")
                BooksMst booksMst
                for (int i = 0; i < books.length; i++) {
                    if(params.publisherId!=null && !"".equals(params.publisherId)){
                        booksMst = BooksMst.findByIdAndSiteIdInListAndStatusAndPublisherId(new Long(books[i]), Arrays.asList(siteIdList.split(",")), 'published',new Integer(params.publisherId))
                    }
                    else{
                        booksMst = BooksMst.findByIdAndSiteIdInListAndStatus(new Long(books[i]), Arrays.asList(siteIdList.split(",")), 'published')
                    }
                    if (booksMst != null) {
                        DiscountMst discountMst = new DiscountMst(type: params.type, name: params.discountName, startDate: startDate, endDate: endDate, allBooks: allBooks, status: "active", createdBy: springSecurityService.currentUser.username, couponCode: couponCode ? couponCode : null,siteId: siteId,redeemCount: params.redeemCount,publisherId: params.publisherId)
                        discountMst.save(failOnError: true, flush: true)
                        DiscountBookDtl discountBookDtl = new DiscountBookDtl(discountId: discountMst.id, bookId: books[i])
                        discountBookDtl.save(failOnError: true, flush: true)
                        discountPriceDtl = new DiscountPriceDtl(discountId: discountMst.id, discountValue: params.discountValue, discountPercentage: params.discountPercentage, spentUpto: params.spentUpto)
                        discountPriceDtl.save(failOnError: true, flush: true)
                    } else {
                        invalidBookId += " " + books[i] + ","
                    }
                }
            }  else{
                DiscountMst discountMst = new DiscountMst(type: params.type, name: params.discountName, startDate: startDate, endDate: endDate, allBooks: allBooks, status: "active", createdBy: springSecurityService.currentUser.username,
                        couponCode: couponCode ? couponCode : null,siteId: siteId,redeemCount: params.redeemCount ,publisherId: params.publisherId)
                discountMst.save(failOnError: true, flush: true)
                discountPriceDtl = new DiscountPriceDtl(discountId: discountMst.id, discountValue: params.discountValue, discountPercentage: params.discountPercentage)
                discountPriceDtl.save(failOnError: true, flush: true)
            }
        }else{
            existCouponCode="codeExist"
        }

        def json = ['status':"OK",'invalidBookId': invalidBookId ? String.join(",",Arrays.asList(invalidBookId.split(",")).stream().distinct().collect(Collectors.toList())):"",existCouponCode:existCouponCode]
        return json
    }


    def getAutoDiscountForUser(String type,String columnName,String spentUpto,Double amount,bookId,siteId){
        String siteIds
         if(siteId == 1){
            siteIds = getSiteIdList(siteId)
        }else{
            siteIds = siteId+","+"1"
        }
        BooksMst booksMst = BooksMst.findById(new Long(bookId))
        def results=null
        String sql = "SELECT discount_id," + columnName + " " +
                        " FROM wsshop.discount_price_dtl dpd,wsshop.discount_mst dm " +
                        " WHERE  dpd.discount_id=dm.id and dm.type='" + type + "' AND (dm.publisher_id is null OR dm.publisher_id=" + booksMst.publisherId + ") and dm.site_id IN ("+siteIds+") and dm.status = 'active' and dm.start_date <= SYSDATE()  and " + columnName + " =(select greatest(COALESCE((SELECT  MAX(" + columnName + ")" +
                        " FROM  wsshop.discount_price_dtl dpd, wsshop.discount_book_dtl dbd, wsshop.discount_mst dm" +
                        " WHERE" +
                        " dbd.discount_id = dpd.discount_id  AND dpd.discount_id = dm.id  AND dm.type = '" + type + "' AND (dm.publisher_id is null OR dm.publisher_id=" + booksMst.publisherId + ") and dm.site_id IN ("+siteIds+")  AND dm.status = 'active' and dm.start_date <= SYSDATE() AND dbd.book_id = " + bookId + "),0)" +
                        ", COALESCE((SELECT MAX(" + columnName + ")  FROM  wsshop.discount_price_dtl dpd,  wsshop.discount_mst dm" +
                        " WHERE  dpd.discount_id = dm.id  AND dm.type = '" + type + "' and  (dm.publisher_id is null OR dm.publisher_id=" + booksMst.publisherId + ") and dm.site_id IN ("+siteIds+") AND dm.status = 'active'  and dm.start_date <= SYSDATE() AND dm.all_books = 'true'),0))) ORDER BY discount_id " +
                        " LIMIT 1";
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1 = new Sql(dataSource)
                results = sql1.rows(sql);
        return results
    }

    @Transactional
    def emailWhitelist(params,siteId){
        String status="ok"
            EmailMst emailMst =EmailMst.findByEmailAndSiteId(params.userEmail,siteId)
            if(emailMst!=null) {
                if(emailMst.validated !="true") {
                    emailMst.validated = 'true'
                    emailMst.save(failOnError: true, flush: true)
                }else{
                    status="already done"
                }
            }else{
                status="not found"
            }
            def json =['status':status]
            return json
    }


    def cartPurchase(params,siteId){
        String sql = "select bm.id bookId,bm.title title,po.amount,po.username,po.book_price from purchase_order po,books_mst bm where po.item_code=bm.id and  po.cart_mst_id='"+  params.cartMstId+"'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql).collect{info->
            String userName=info.username
            return [bookId: info.bookId, title: info.title, paidAmount: info.amount, userName: userName,bookPrice:info.book_price]
        }
        def json = [ status: results?"OK":"not found", cartDetails: results]
        return json
    }

    def moderation(siteId,params){
        List teachers = TeacherNomineeDtl.findAllBySiteIdAndStatusIsNull(siteId, [sort: "id", order: "desc"])
        List teachersNominee = teachers.collect{it ->
            if( redisService.("getNomineeIdVoteCount_"+it.id) ==null){
                dataProviderService.getNomineeIdVoteCount(new Long(it.id))
            }
            return [id:it.id,userType: it.userType,userName:it.userName,teacherName:it.teacherName,teacherImage:it.teacherImage,teacherSubject:it.teacherSubject,dateCreated:it.dateCreated,description:it.description,teacherClass:it.teacherClass,teacherSchool:it.schoolName,voteCount:redisService.("getNomineeIdVoteCount_"+it.id)]
        }
        def json = [status: teachersNominee ? "OK" : "Not present", data: teachersNominee, recordsTotal: teachersNominee.size(), recordsFiltered: teachersNominee.size(), draw: params.draw]
        return json
    }

    def moderatePostById(siteId,params){
        TeacherNomineeDtl teacherNomineeDtl =TeacherNomineeDtl.findById(new Long(params.postId))
        if("moderate".equals(params.mode)) {
            teacherNomineeDtl.status = "moderated"
            teacherNomineeDtl.save(failOnError: true, flush: true)
        }else{
            teacherNomineeDtl.delete(flush: true)
        }
        dataProviderService.getTeachersNomineeDetails(siteId)
        dataProviderService.getAllModeratedTeachersNomineeDetails(siteId)
        dataProviderService.getTeachersNomineeDetails(siteId)
        def json=["status":"OK"]
        return json
    }



    def refundDetails(params,siteId,session){
        String sql =""
        String sqlCount = ""
        List sales = null

        if(params.salesSiteId!=null){
            if(!"".equals(params.salesSiteId)) siteId = Integer.parseInt(params.salesSiteId)
        }
        List sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
        HashMap sites = new HashMap()
        sitesList.each {site->
            sites.put(""+site.id,site.clientName)
        }
            if (params.poStartDate != null || params.poEndDate != null) {

                sqlCount = "select count(po.id)" +
                        " from wsshop.books_mst bm, wsshop.purchase_order_refund po, wsshop.publishers ph where"

                if (params.poStartDate != null && params.poStartDate != "" )
                    sqlCount += " date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y') and"

                if (params.poEndDate != null && params.poEndDate != "")
                    sqlCount += " date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') and"

                sqlCount += " bm.id=po.item_code  and bm.publisher_id=ph.id and " ;
                    if (params.salesSiteId != null && !"".equals(params.salesSiteId) && session["userdetails"].publisherId == null)
                        sqlCount += " po.site_id in (" + siteId + ")"
                    else sqlCount += " bm.site_id in (" + dataProviderService.getSiteIdList(siteId) + ")"

                    if (session["userdetails"].publisherId != null) {
                        sqlCount += " and  bm.publisher_id=" + session["userdetails"].publisherId
                    } else if (params.publisherId != null && params.publisherId != "") {
                        sqlCount += " and bm.publisher_id=" + params.publisherId
                    }

                def dataSourceCount = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1count = new Sql(dataSourceCount)
                def resultsCount = sql1count.rows(sqlCount)
                def count = resultsCount.get(0).values()


                sql = "select po.id, po.payment_id, po.username, bm.title, po.amount," +
                        " DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE), ph.name publisher,po.book_price,po.discount_id,po.discount_amount,po.site_id,COALESCE(bm.isbn,''),po.purchased_date" +
                        " from wsshop.books_mst bm, wsshop.purchase_order_refund po, wsshop.publishers ph where"

                if (params.poStartDate != null && params.poStartDate != "")
                    sql += " date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y') and"

                if (params.poEndDate != null && params.poEndDate != "")
                    sql += " date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') and"

                sql += " bm.id=po.item_code  and bm.publisher_id=ph.id and "

                    if (params.salesSiteId != null && !"".equals(params.salesSiteId) && session["userdetails"].publisherId == null)
                        sql += " po.site_id in (" + siteId + ")"
                    else sql += " bm.site_id in (" + dataProviderService.getSiteIdList(siteId) + ")"

                    if (session["userdetails"].publisherId != null) {
                        sql += " and  bm.publisher_id=" + session["userdetails"].publisherId
                    } else if (params.publisherId != null && params.publisherId != "") {
                        sql += " and bm.publisher_id=" + params.publisherId
                }

                sql += " order by po.id desc limit " + params.start + " , " + params.length + ""

                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                def date
                def purchasedDate
                sales = results.collect { sale ->
                    date = sale[5]
                    if (date != "") {
                        date = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(date)
                    }
                    purchasedDate = sale[12]
                    if (purchasedDate != "") {
                        purchasedDate = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(purchasedDate)
                    }
                   User  user = dataProviderService.getUserMst(sale[2])
                    if (user != null)
                        return [poNo        : sale[0], paymentId: sale[1], state: user.state, district: user.district != null ? user.district : "", name: user.name, email: user.email, mobile: user.mobile, title: sale[3],
                                price       : sale[4], salesDate: date, publisher: sale[6], bookPrice: sale[7], discountAmount: sale[9] ? sale[9] : "",
                                discountType: sale[8] ? DiscountMst.findById(new Long(sale[8])).type : "", siteId: sale.site_id, siteName: sites.get("" + sale.site_id),isbn: sale[11],purchasedDate:purchasedDate]
                }
                while (sales.remove(null)) {
                }
                def json = [data: sales, recordsTotal: count, draw: params.draw, recordsFiltered: count]
                return json
        }
    }

    def updateBookPrice(BooksMst booksMst,valueHolder){
        try {
            Double listPrice = booksMst.price
            Double price = booksMst.listprice
            if (valueHolder.Price != null && Double.compare(booksMst.price, valueHolder.Price) != 0) {
                booksMst.price = valueHolder.Price
                BooksMst.wsuser.executeUpdate("update BooksMst set price=" + valueHolder.Price + " where id=" + booksMst.id)
                BooksMst.wsshop.executeUpdate("update BooksMst set price=" + valueHolder.Price + " where id=" + booksMst.id)
            };
            if (valueHolder.ListPrice != null && Double.compare(booksMst.listprice, valueHolder.ListPrice) != 0) {
                booksMst.listprice = valueHolder.ListPrice
                BooksMst.wsuser.executeUpdate("update BooksMst set listprice=" + valueHolder.ListPrice + " where id=" + booksMst.id)
                BooksMst.wsshop.executeUpdate("update BooksMst set listprice=" + valueHolder.ListPrice + " where id=" + booksMst.id)
            };
            booksMst.save(flush: true, failOnError: true)
        }catch(Exception e){
            println("The price update exception happened in updateBookPrice method for bookId="+booksMst.id)
            println("The exception is "+e.toString())
        }

    }


}
