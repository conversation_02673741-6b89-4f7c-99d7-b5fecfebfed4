package com.wonderslate.qp

import grails.transaction.Transactional
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.QuestionTypes

@Transactional
class QuestionPaperService {

    // Service method to create a Question Paper Pattern
    def createPattern(String name, String description, String createdBy, Long instituteId, Long bookId = null) {
        try {
            def pattern = new QuestionPaperPattern(
                    name: name,
                    description: description,
                    createdBy: createdBy,
                    instituteId: instituteId,
                    bookId: bookId
            )
            if (pattern.save(flush: true)) {
                return [success: true, pattern: pattern]
            } else {
                return [success: false, errors: pattern.errors]
            }
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }

    // Service method to add a section to a pattern
    def addSection(Long patternId, String sectionName, String instructions, Integer totalMarks, String createdBy) {
        try {
            def pattern = QuestionPaperPattern.get(patternId)
            if (!pattern) {
                return [success: false, message: "Pattern not found"]
            }

            def section = new Section(
                    name: sectionName,
                    instructions: instructions,
                    totalMarks: totalMarks,
                    createdBy: createdBy,
                    pattern: pattern
            )
            if (section.save(flush: true)) {
                return [success: true, section: section]
            } else {
                return [success: false, errors: section.errors]
            }
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }

    // Service method to add a question type to a section
    def addQuestionType(Long sectionId, String type, Integer numberOfQuestions, Integer marksPerQuestion, String createdBy) {
        try {
            def section = Section.get(sectionId)
            if (!section) {
                return [success: false, message: "Section not found"]
            }

            def questionType = new QuestionType(
                    type: type,
                    numberOfQuestions: numberOfQuestions,
                    marksPerQuestion: marksPerQuestion,
                    createdBy: createdBy,
                    section: section
            )
            if (questionType.save(flush: true)) {
                return [success: true, questionType: questionType]
            } else {
                return [success: false, errors: questionType.errors]
            }
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }

    // Service method to delete a pattern
    def deletePattern(Long patternId, String username) {
        try {
            def pattern = QuestionPaperPattern.get(patternId)
            if (!pattern) {
                return [success: false, message: "Pattern not found"]
            }
            pattern.delete(flush: true)
            return [success: true]
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }

    // Service method to delete a section
    def deleteSection(Long sectionId, String username) {
        try {
            def section = Section.get(sectionId)
            if (!section) {
                return [success: false, message: "Section not found"]
            }
            if (section.createdBy != username) {
                return [success: false, message: "Unauthorized deletion attempt"]
            }
            section.delete(flush: true)
            return [success: true]
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }

    // Service method to delete a question type
    def deleteQuestionType(Long questionTypeId, String username) {
        try {
            def questionType = QuestionType.get(questionTypeId)
            if (!questionType) {
                return [success: false, message: "Question type not found"]
            }
            if (questionType.createdBy != username) {
                return [success: false, message: "Unauthorized deletion attempt"]
            }
            questionType.delete(flush: true)
            return [success: true]
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }



    def generateAndStoreQuestionPapers(Long patternId, int numberOfSets, String createdBy, List<Long> chapterIds, String name, Long instituteId) {
        def pattern = QuestionPaperPattern.get(patternId)
        if (!pattern) {
            log.error("Pattern with ID ${patternId} not found.")
            return
        }

        // Generate a unique groupId for this batch of sets
        String groupId = UUID.randomUUID().toString()

        // Map to track used question IDs for each question type
        // Structure: [questionType: Set<Long>]
        def usedQuestionIds = [:]

        // Map to store all available questions by question type
        // Structure: [questionType: List<Map>]
        def allQuestionsPoolByType = [:]
        //sort the sections in the pattern in the order that they created using id
        pattern.sections = pattern.sections.sort { it.id }
        // First, gather all available questions for each question type
        pattern.sections.each { section ->
            section.questionTypes.each { questionType ->
                def questionsPool = [] // Pool of available questions for this question type

                chapterIds.each { chapterId ->
                    // Fetch questions for this chapter and question type
                    def resource = ResourceDtl.findAllByChapterIdAndResTypeInList(chapterId, ['Multiple Choice Questions', 'QA'])
                    if (resource.size() > 0) {
                        //get comma seperated resLinks into a string from resource
                        def resLinks = resource.collect { it.resLink }.join(',')
                        // create ArrayList of resLinks
                        def resLinksList = resLinks.split(',').collect { it.trim().toLong() }

                        def matchedQuestions = ObjectiveMst.findAllByQuizIdInListAndQuestionType([resLinksList], questionType.type)
                        if(matchedQuestions.size()==0) matchedQuestions = ObjectiveMst.findAllByQuizIdInList([resLinksList])
                        if (matchedQuestions) {
                            matchedQuestions.each { question ->
                                def chapterName = ChaptersMst.findById(chapterId)?.name ?: "Unknown Chapter"
                                questionsPool << [
                                        text: question.question,
                                        marks: question.marks,
                                        chapter: chapterName,
                                        objId: question.id
                                ]
                            }
                        }
                    }
                }

                // Store all available questions for this question type
                allQuestionsPoolByType[questionType.id] = questionsPool
                // Initialize the set of used question IDs for this question type
                usedQuestionIds[questionType.id] = [] as Set
            }
        }

        // Now generate each set
        (1..numberOfSets).each { setIndex ->
            println("Generating set ${setIndex}...")
            def questionPaperSet = new QuestionPaperSet(
                    name: name,
                    groupId: groupId,
                    isSelected: (setIndex == 1), // Mark the first set as selected by default
                    pattern: pattern,
                    header: pattern.description,
                    createdBy: createdBy,
                    instituteId: instituteId
            )
            if (!questionPaperSet.save(flush: true)) {
                log.error("Failed to save QuestionPaperSet: ${questionPaperSet.errors}")
                return
            }

            pattern.sections.each { section ->
                def questionPaperSection = new QuestionPaperSection(
                        questionPaperSet: questionPaperSet,
                        name: section.name,
                        description: section.instructions,
                        createdBy: createdBy
                )

                if (!questionPaperSection.save(flush: true)) {
                    log.error("Failed to save QuestionPaperSection: ${questionPaperSection.errors}")
                    return
                }

                section.questionTypes.each { questionType ->
                    // Get all available questions for this question type
                    def allQuestionsPool = allQuestionsPoolByType[questionType.id]

                    // Filter out questions that have already been used in previous sets
                    def unusedQuestionsPool = allQuestionsPool.findAll { questionData ->
                        !(questionData.objId in usedQuestionIds[questionType.id])
                    }

                    // If we don't have enough unused questions, we need to allow some duplication
                    def questionsPool = unusedQuestionsPool
                    def questionsToAllocate = questionType.numberOfQuestions

                    // Check if we have enough unused questions
                    if (unusedQuestionsPool.size() < questionsToAllocate) {
                        println("Warning: Not enough unique questions available for question type '${questionType.type}'. Some questions may be repeated.")

                        // If we don't have enough unused questions, use all available questions
                        // but prioritize unused ones
                        questionsPool = unusedQuestionsPool + allQuestionsPool.findAll { questionData ->
                            questionData.objId in usedQuestionIds[questionType.id]
                        }
                    }

                    // Shuffle the questions pool to randomize selection
                    Collections.shuffle(questionsPool)

                    // Allocate questions to this section
                    questionsToAllocate = Math.min(questionsToAllocate, questionsPool.size())
                    def allocatedQuestions = questionsPool.take(questionsToAllocate)

                    // Add the allocated question IDs to the used set
                    allocatedQuestions.each { questionData ->
                        usedQuestionIds[questionType.id] << questionData.objId

                        def questionPaperQuestion = new QuestionPaperQuestion(
                                section: questionPaperSection,
                                objId: questionData.objId,
                                marks: questionData.marks,
                                chapterName: questionData.chapter,
                                createdBy: createdBy
                        )

                        if (!questionPaperQuestion.save(flush: true)) {
                            log.error("Failed to save QuestionPaperQuestion: ${questionPaperQuestion.errors}")
                        }
                    }
                }
            }
        }

        println("Successfully generated and stored ${numberOfSets} question paper sets for pattern ID ${patternId}.")
    }

    // Service method to get all question types sorted alphabetically
    def getAllQuestionTypes() {
        try {
            def questionTypes = QuestionTypes.createCriteria().list {
                order('questionType', 'asc')
            }
            return [success: true, questionTypes: questionTypes]
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }

    // Service method to create a new question type
    def createQuestionType(String questionType, String createdBy) {
        try {
            def newQuestionType = new QuestionTypes(
                    questionType: questionType,
                    createdBy: createdBy
            )
            if (newQuestionType.save(flush: true)) {
                return [success: true, questionType: newQuestionType]
            } else {
                return [success: false, errors: newQuestionType.errors]
            }
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }

    // Service method to delete a question type
    def deleteQuestionTypeFromMaster(Long id) {
        try {
            def questionType = QuestionTypes.get(id)
            if (!questionType) {
                return [success: false, message: "Question type not found"]
            }
            questionType.delete(flush: true)
            return [success: true]
        } catch (Exception e) {
            return [success: false, message: e.message]
        }
    }
}
