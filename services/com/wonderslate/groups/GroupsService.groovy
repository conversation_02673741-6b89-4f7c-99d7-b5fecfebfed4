package com.wonderslate.groups
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.DataNotificationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.KeyValueMst
import com.wonderslate.usermanagement.User
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec

@Transactional
class GroupsService {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    def redisService
    def grailsApplication

    String encrypt(String text) {
        try {
            if (redisService.("securityKeys_1") == null) dataProviderService.getSecurityKeys("1")
            String securityKey = redisService.("securityKeys_1")

            Cipher ecipher = Cipher.getInstance("AES/CBC/PKCS5PADDING", "SunJCE");
            IvParameterSpec iv = new IvParameterSpec((securityKey).getBytes());
            SecretKey secretKey = new SecretKeySpec((securityKey).getBytes(), "AES");
            ecipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);

            return new String(Base64.getEncoder().encode(ecipher.doFinal(text.getBytes("UTF-8"))));
        } catch (Exception e) {
            println("Exception=" + e.toString())
        }

        return "";
    }

    String decrypt(String text) {
        try {
            if (redisService.("securityKeys_1") == null) dataProviderService.getSecurityKeys("1")
            String securityKey = redisService.("securityKeys_1")
            byte[] decodedBytes = Base64.getDecoder().decode(text);
            Cipher ecipher = Cipher.getInstance("AES/CBC/PKCS5PADDING", "SunJCE");
            IvParameterSpec iv = new IvParameterSpec((securityKey).getBytes());
            SecretKey secretKey = new SecretKeySpec(securityKey.getBytes(), "AES");
            ecipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

            return new String(ecipher.doFinal(decodedBytes));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }

    boolean canEditGroup(username, groupId) {
        GroupsMembersDtl groupsMembersDtl=GroupsMembersDtl.findByGroupIdAndUsername(new Long(groupId), username)
        if(groupsMembersDtl!=null && groupsMembersDtl.role=='admin') {
            return true
        }else{
            return false
        }
    }

    boolean canAccessGroup(username, groupId) {
        if (GroupsMembersDtl.findByGroupIdAndUsername(new Long(groupId), username)) {
            return true
        }
        return false
    }

    boolean canEditPost(username, postId) {
        if (GroupsPostDtl.findByIdAndCreatedBy(new Long(postId), username)) {
            return true
        }
        return false
    }


    def groupCreate(request) {
        String status = "failed"
        Integer groupId = 0
        Integer siteId
        GroupsMst groupsMst
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (jsonObj.groupId != null && jsonObj.groupId != "") {
                groupsMst = GroupsMst.findById(new Long(jsonObj.groupId))
                groupsMst.name = jsonObj.name ? jsonObj.name : groupsMst.name
                groupsMst.privacyType = jsonObj.privacyType ? jsonObj.privacyType : groupsMst.privacyType
                groupsMst.visibility = jsonObj.visibility ? jsonObj.visibility : jsonObj.visibility
                groupsMst.allPost = jsonObj.allPost ? jsonObj.allPost : groupsMst.allPost
                status = "success"
                groupsMst.save(failOnError: true, flush: true)
                groupId = groupsMst.id
                if (jsonObj.privacyType != null && jsonObj.privacyType != "") {
                    approveAllGroupRequest(request)
                }
            }else {
                siteId=getSiteId(request)
                groupsMst = new GroupsMst(name: jsonObj.name? jsonObj.name:null,
                        privacyType: jsonObj.privacyType?jsonObj.privacyType:null, visibility: jsonObj.visibility?jsonObj.visibility:null, createdBy: springSecurityService.currentUser.username,siteId:siteId,allPost: jsonObj.allPost?jsonObj.allPost:"true",groupType: "group")
                groupsMst.save(failOnError: true, flush: true)
                groupId = groupsMst.id
                GroupsMembersDtl groupsMembersDtl = new GroupsMembersDtl(username: springSecurityService.currentUser.username, role: 'admin', profilepic: User.findByUsername(springSecurityService.currentUser.username).profilepic ? User.findByUsername(springSecurityService.currentUser.username).profilepic : null, groupId: groupId, name: User.findByUsername(springSecurityService.currentUser.username).name, userId: User.findByUsername(springSecurityService.currentUser.username).id)
                groupsMembersDtl.save(failOnError: true, flush: true)
                status = "success"
                getGroupAdminCount(groupsMst.id)
            }
            def json = ["status": status, "groupId": groupId]
            return json
        }

    }

    def updateGroupDescription(request) {
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            def json
            if (canEditGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                GroupsMst groupsMst = GroupsMst.findById(new Long(jsonObj.groupId))
                groupsMst.description = jsonObj.description ? jsonObj.description : null
                if(jsonObj.colorCode!=null) {
                    groupsMst.colorCode = jsonObj.colorCode ? jsonObj.colorCode : null
                    groupsMst.image =  null
                }
                groupsMst.save(failOnError: true, flush: true)
                json = ["status": groupsMst.id ? "success" : "", "groupId": groupsMst.id ? groupsMst.id : ""]
            }else{
                json=["status":'access denied']
            }
            return json
        }
    }

    def createPostDescriptionForGroup(request) {
        def json
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            def groupWallId
            def type = "group"
            def postCount=0
            Integer siteId = getSiteId(request)
            if (canAccessGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                groupWallId = getGroupWallIdForSite(siteId)
                GroupsPostDtl groupsPostDtl
                MultipartFile file
                if (jsonObj.postId != null && !"".equals(jsonObj.postId)) {
                    groupsPostDtl = GroupsPostDtl.findById(new Long(jsonObj.postId))
                } else {
                    groupsPostDtl = new GroupsPostDtl()
                }
                groupsPostDtl.groupId = new Long(jsonObj.groupId)
                groupsPostDtl.description = jsonObj.description ? jsonObj.description : null
                groupsPostDtl.createdBy = springSecurityService.currentUser.username
                groupsPostDtl.save(failOnError: true, flush: true)
                resetCacheForGroupPosts(groupsPostDtl.groupId)
                if (groupWallId == jsonObj.groupId ) {
                    type = "channel"
                    if(jsonObj.postId == null || "".equals(jsonObj.postId))
                    {
                        if(redisService.("groupWallId_" + siteId + "_latestPostCount")==null || redisService.("groupWallId_" + siteId + "_latestPostCount")=="null") getlatestPostCountForGroupWall(siteId)
                        redisService.("groupWallId_" + siteId + "_latestPostCount") = new Long(redisService.("groupWallId_" + siteId + "_latestPostCount"))+1
                        postCount=redisService.("groupWallId_" + siteId + "_latestPostCount")
                    }
                }
                if(groupsPostDtl.pinned.equals("true")) getGroupPinnedPosts(groupsPostDtl.groupId)
                boolean adminUser="false"
                if(canEditGroup(springSecurityService.currentUser.username, groupsPostDtl.groupId)) adminUser="true"
                if("true".equals(adminUser)) dataNotificationService.sendGroupPostNotification(groupsPostDtl.groupId+"",siteId,type,postCount,groupsPostDtl.id,groupsPostDtl.description,groupsPostDtl.image,adminUser)
                json = ["status": groupsPostDtl.id ? "success" : "", "groupId": groupsPostDtl.groupId, "postId": groupsPostDtl.id]
            }else{
                json=["status":'access denied']
            }
            return json
        }
    }

    def createPostImgFileForGroup(request,params, flash) {
        def json
        boolean canEdit = true
        boolean adminUser=false
        if(canEditGroup(springSecurityService.currentUser.username, params.groupId)) adminUser=true
        println("username="+springSecurityService.currentUser.username+ "adminUser="+adminUser)
        if (canAccessGroup(springSecurityService.currentUser.username, params.groupId)) {
            GroupsPostDtl groupsPostDtl
            MultipartFile file
            def groupWallId
            def type = "group"
            def postCount=0
            Integer siteId = getSiteId(request)
            if (params.postId != null && !"".equals(params.postId)) {
                if (!canEditPost(springSecurityService.currentUser.username, params.postId)) {
                    canEdit = false
                }
                groupsPostDtl = GroupsPostDtl.findById(new Long(params.postId))
            } else {
                groupsPostDtl = new GroupsPostDtl()
            }
            if (canEdit) {
                groupWallId = getGroupWallIdForSite(siteId)
                groupsPostDtl.groupId = new Long(params.groupId)
                groupsPostDtl.image = null
                groupsPostDtl.fileName = null
                groupsPostDtl.filePath = null
                groupsPostDtl.description = params.description
                groupsPostDtl.createdBy = springSecurityService.currentUser.username
                groupsPostDtl.save(failOnError: true, flush: true)
                if (params.type == "image" || params.type == "file") {

                    final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                    file = multiRequest.getFile("file");
                    if (!file.empty) {
                        if (params.type == "image") {
                            File uploadDir = new File("upload/grouppostimg/" + groupsPostDtl.groupId + "/" + groupsPostDtl.id)
                            if (!uploadDir.exists()) uploadDir.mkdirs()
                            String filename = file.originalFilename
                            filename = filename.replaceAll("\\s+", "")
                            //creating directory to process images
                            File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                            if (!uploadDir1.exists()) uploadDir1.mkdirs()
                            //saving original image finally
                            file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
                            //saving original image finally
                            groupsPostDtl.image = file.originalFilename
                            groupsPostDtl.save(failOnError: true, flush: true)


                        } else if (params.type == "file") {
                            groupsPostDtl.image = null
                            String filename = file.originalFilename
                            filename = filename.replaceAll("\\s+", "")
                            groupsPostDtl.fileName = filename
                            groupsPostDtl.filePath = "upload/grouppostfile/" + groupsPostDtl.groupId + "/" + groupsPostDtl.id + "/" + groupsPostDtl.fileName
                            groupsPostDtl.save(failOnError: true, flush: true)
                            File uploadDir = new File(grailsApplication.config.grails.basedir.path + "upload/grouppostfile/" + groupsPostDtl.groupId + "/" + groupsPostDtl.id)
                            if (!uploadDir.exists()) uploadDir.mkdirs()
                            file.transferTo(new File(grailsApplication.config.grails.basedir.path + groupsPostDtl.filePath))

                        }

                    }
                }
                if (groupWallId!=null&&!"null".equals(""+groupWallId)&&new Integer(groupWallId) == groupsPostDtl.groupId) {
                    type = "channel"
                    if(params.postId == null || "".equals(params.postId))
                    {
                        if(redisService.("groupWallId_" + siteId + "_latestPostCount")==null || redisService.("groupWallId_" + siteId + "_latestPostCount")=="null") getlatestPostCountForGroupWall(siteId)
                        redisService.("groupWallId_" + siteId + "_latestPostCount") = new Long(redisService.("groupWallId_" + siteId + "_latestPostCount"))+1
                        postCount=redisService.("groupWallId_" + siteId + "_latestPostCount")
                    }
                }

                    if(adminUser) dataNotificationService.sendGroupPostNotification(groupsPostDtl.groupId+"",siteId,type,postCount,groupsPostDtl.id,groupsPostDtl.description,groupsPostDtl.image,""+adminUser)


                resetCacheForGroupPosts(groupsPostDtl.groupId)
                if(groupsPostDtl.pinned.equals("true")) getGroupPinnedPosts(groupsPostDtl.groupId)
                json = ["status": groupsPostDtl.id ? "success" : "", "groupId": groupsPostDtl.groupId, "postId": groupsPostDtl.id]
            } else {
                json = ["status": 'access denied']
            }
        }else{
            json = ["status": 'access denied']
        }
        return json
    }

    def addGroupCoverImage(request,params){
        def json
        if (canEditGroup(springSecurityService.currentUser.username, params.groupId)) {
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            GroupsMst groupsMst = GroupsMst.findById(new Long(params.groupId))
            File uploadDir = new File("upload/usergroup/" + groupsMst.id)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            String filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            //creating directory to process images
            File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
            if (!uploadDir1.exists()) uploadDir1.mkdirs()
            //saving original image finally
            file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
            groupsMst.colorCode=null
            groupsMst.image = file.originalFilename
            groupsMst.save(failOnError: true, flush: true)
            json = ["status": groupsMst.id ? "success" : "failed", "groupId": groupsMst.id ? groupsMst.id : ""]
        }else{
            json= ["status":'access denied']
        }
        return json
    }

    def joinGroup(request){
        String status="already present"
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (GroupsMst.findById(new Long(jsonObj.groupId)).privacyType != "private" && !GroupsMembersDtl.findByUsernameAndGroupId(springSecurityService.currentUser.username, new Long(jsonObj.groupId))) {
                GroupsMembersDtl groupsMembersDtl = new GroupsMembersDtl(username: springSecurityService.currentUser.username, role: 'user', profilepic: User.findByUsername(springSecurityService.currentUser.username).profilepic ? User.findByUsername(springSecurityService.currentUser.username).profilepic : null,
                        groupId: jsonObj.groupId,name:User.findByUsername(springSecurityService.currentUser.username).name,userId: User.findByUsername(springSecurityService.currentUser.username).id)
                groupsMembersDtl.save(failOnError: true, flush: true)
                getGroupMembersCount(new Long(jsonObj.groupId))
                getGroupAdminCount(new Long(jsonObj.groupId))
                resetGroupsPostedByUser(User.findByUsername(springSecurityService.currentUser.username).id)
                status = "success"
            } else if (GroupsMst.findById(new Long(jsonObj.groupId)).privacyType == "private") {
                GroupsRequestDtl groupsRequestDtl = new GroupsRequestDtl(requestedBy: springSecurityService.currentUser.username, groupId: new Long(jsonObj.groupId), reason: jsonObj.reason ? jsonObj.reason : null)
                groupsRequestDtl.save(failOnError: true, flush: true)
                status = "private"
            }
        }
        def json = ["status":status]
        return json
    }

    def acceptJoinRequest(request){
        String status="fail"
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (canEditGroup(springSecurityService.currentUser.username,jsonObj.groupId)) {
                GroupsRequestDtl groupsRequestDtl = GroupsRequestDtl.findByGroupIdAndRequestedByAndStatusIsNull(new Long(jsonObj.groupId), jsonObj.username)
                if (groupsRequestDtl != null) {
                    status = "OK"
                    groupsRequestDtl.status = "accepted"
                    groupsRequestDtl.save(failOnError: true, flush: true)
                    GroupsMembersDtl groupsMembersDtl = new GroupsMembersDtl(username: jsonObj.username, role: 'user', profilepic: User.findByUsername(jsonObj.username).profilepic ? User.findByUsername(jsonObj.username).profilepic : null, groupId: jsonObj.groupId, name: User.findByUsername(jsonObj.username).name, userId: User.findByUsername(jsonObj.username).id)
                    groupsMembersDtl.save(failOnError: true, flush: true)
                    getGroupMembersCount(new Long(jsonObj.groupId))
                    resetGroupsPostedByUser(User.findByUsername(jsonObj.username).id)
                }
            }
        }
        def json = ["status":status]
        return json
    }

    def deleteJoinRequest(request){
        String status="fail"
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (canEditGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                GroupsRequestDtl groupsRequestDtl = GroupsRequestDtl.findByGroupIdAndRequestedByAndStatusIsNull(new Long(jsonObj.groupId), jsonObj.username)
                if (groupsRequestDtl != null) {
                    status = "Deleted"
                    groupsRequestDtl.status = "deleted"
                    groupsRequestDtl.save(failOnError: true, flush: true)
                }
            }
        }
        def json = ["status":status]
        return json
    }


    def likePostOfGroup(params) {
        String status = "fail"
        if (!GroupsPostsLikesDtl.findByGroupIdAndPostIdAndLikedBy(new Long(params.groupId), new Long(params.postId), springSecurityService.currentUser.username)) {
            GroupsPostsLikesDtl groupsPostsLikesDtl = new GroupsPostsLikesDtl(groupId: params.groupId, postId: params.postId, likedBy: springSecurityService.currentUser.username)
            groupsPostsLikesDtl.save(failOnError: true, flush: true)
            userPostLikeOrDislikeCache(groupsPostsLikesDtl.groupId,groupsPostsLikesDtl.postId,groupsPostsLikesDtl.likedBy)
            getGroupPostLikeCount(params.postId)
            status = "OK"
        }
        def json = ['status': status]
        return json
    }

    def dislikePostOfGroup(params) {
        String status = "fail"
        GroupsPostsLikesDtl groupsPostsLikesDtl=  GroupsPostsLikesDtl.findByGroupIdAndPostIdAndLikedBy(new Long(params.groupId), new Long(params.postId), springSecurityService.currentUser.username)
        if(groupsPostsLikesDtl!=null){
            groupsPostsLikesDtl.delete()
            getGroupPostLikeCount(params.postId)
            userPostLikeOrDislikeCache(new Long(params.groupId),new Long(params.postId), springSecurityService.currentUser.username)
            getGroupPostLikeCount(params.postId)
            status = "OK"
        }
        def json = ['status': status]
        return json
    }

    def userPostLikeOrDislikeCache(Long groupId,Long postId,String username){
        GroupsPostsLikesDtl groupsPostsLikesDtl =GroupsPostsLikesDtl.findByGroupIdAndPostIdAndLikedBy(groupId,postId,username)
        if(groupsPostsLikesDtl!=null)  redisService.("userPostLike_"+postId+"_"+username) = "true"
        else  redisService.("userPostLike_"+postId+"_"+username) = "false"
    }

    def commentPostOfGroup(request) {
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            GroupsPostCommentsDtl groupsPostCommentsDtl = new GroupsPostCommentsDtl(groupId: jsonObj.groupId, postId: jsonObj.postId, createdBy: springSecurityService.currentUser.username, description: jsonObj.description)
            groupsPostCommentsDtl.save(failOnError: true, flush: true)
            getGroupPostCommentsCount(jsonObj.postId)
            resetCacheForPostComments(jsonObj.postId)
            def json = ['status': "OK"]
            return json
        }
    }

    def replyForCommentOfGroup(request) {
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            GroupsCommentsReplyDtl GroupsCommentsReplyDtl = new GroupsCommentsReplyDtl(groupId: jsonObj.groupId, commentId: jsonObj.commentId, postId: jsonObj.postId, createdBy: springSecurityService.currentUser.username, description: jsonObj.description)
            GroupsCommentsReplyDtl.save(failOnError: true, flush: true)
            getGroupPostCommentsReplyCount(jsonObj.commentId)
            repliedDetailsByCommentsId(new Integer(jsonObj.commentId))
            resetCacheForPostComments(jsonObj.postId)
            def json = ['status': "OK"]
            return json
        }
    }


    def reportPostAsSpam(request) {
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            GroupsPostsSpamDtl groupsPostsSpamDtl = new GroupsPostsSpamDtl(groupId: new Long(jsonObj.groupId), postId: jsonObj.postId, createdBy: springSecurityService.currentUser.username, description: jsonObj.description ? jsonObj.description : null)
            groupsPostsSpamDtl.save(failOnError: true, flush: true)
            def json = ['status': 'OK']
            return json
        }
    }

    def reportGroupUser(request) {
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            GroupsUserSpamDtl groupsUserSpamDtl = new GroupsUserSpamDtl(groupId: new Long(jsonObj.groupId), reportedBy: springSecurityService.currentUser.username, description: jsonObj.description ? jsonObj.description : null, username: jsonObj.username ? jsonObj.username : null)
            groupsUserSpamDtl.save(failOnError: true, flush: true)
            def json = ['status': 'OK']
            return json
        }
    }

    def getGroupReportedUser(params) {
        def sql ="SELECT " +
                "    distinct(gusd.username),gmd.name,gusd.group_id,gmd.profilepic,gmd.user_id,gmd.name " +
                "FROM" +
                "    wsuser.groups_user_spam_dtl gusd," +
                "    wsuser.groups_members_dtl gmd" +
                " WHERE" +
                "    gusd.username = gmd.username and gusd.group_id=gmd.group_id " +
                "        AND gusd.group_id = "+params.groupId+"" +
                "        AND gusd.status is null  " +
                " GROUP BY gusd.username,gusd.id,gmd.name,gusd.group_id,gusd.date_created,gmd.profilepic,gmd.user_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List users = results.collect { groupusers ->
            return [id:groupusers.group_id ,username: groupusers.username,profilepic:groupusers.profilepic, userId:groupusers.user_id,name:groupusers.name]
        }
        def json = ["reportedUsers":users?users:"no records found"]
        return json
    }

    def deletePostFromGroup(params) {
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if (canAccessGroup(springSecurityService.currentUser.username, params.groupId) && (canEditPost(springSecurityService.currentUser.username, params.postId)||GroupsMembersDtl.findByGroupIdAndUsername(new Long(params.groupId), springSecurityService.currentUser.username).role=='admin') || (user.authorities.any {
            it.authority == "ROLE_WS_GROUP_ADMIN"
        })) {
            List reportedPosts = GroupsPostsSpamDtl.findAllByPostId(new Long(params.postId))
            def pinnedRefresh=-1
            reportedPosts.collect { groupposts ->
                GroupsPostsSpamDtl groupsPostsSpamDtl = GroupsPostsSpamDtl.findById(groupposts.id)
                groupsPostsSpamDtl.status = "deleted"
                groupsPostsSpamDtl.save(failOnError: true, flush: true)
            }
            GroupsPostDtl groupsPostDtl=GroupsPostDtl.findById(new Long(params.postId))
            if(groupsPostDtl.pinned.equals("true")) pinnedRefresh=1
            groupsPostDtl.delete(flush: true);
            if(pinnedRefresh) getGroupPinnedPosts(params.groupId)
            resetCacheForGroupPosts(new Long(params.groupId))
            def json = ['status': 'OK']
            return json
        }
    }

    def getGroupReportedPosts(params) {
        def sql ="SELECT  distinct  (gpsd.post_id)," +
                "    gpd.description,gpd.file_path,gpd.image, gpd.file_name, " +
                "    gpd.id as postId,gpd.created_by,gpd.date_created " +
                "    FROM wsuser.groups_posts_spam_dtl gpsd " +
                "    INNER JOIN wsuser.groups_post_dtl gpd ON gpd.id=gpsd.post_id " +
                "    where 1=1 AND gpsd.group_id = "+params.groupId+" and gpsd.status is null group by gpsd.post_id,gpsd.id, gpd.description"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List reportPost = results.collect { posts ->
            GroupsMembersDtl groupsMembersDtl =GroupsMembersDtl.findByUsernameAndGroupId(posts.created_by,new Long(params.groupId))
            return [id:posts.post_id ,createdBy: groupsMembersDtl?groupsMembersDtl.name:"",'dateAdded':posts.date_created,profilepic:groupsMembersDtl?groupsMembersDtl.profilepic:"", userId: groupsMembersDtl?groupsMembersDtl.userId:"",name:groupsMembersDtl?groupsMembersDtl.name:"",
                    description:posts.description?posts.description:null,postFilePath:posts.file_path,postFileName:posts.file_name,postFileImage:posts.image]
        }
        def json = ["reportedGroupsPost":reportPost?reportPost:"no records found"]
        return json
    }

    def deleteUserFromGroup(params){
        def json
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if (canEditGroup(springSecurityService.currentUser.username,params.groupId) || (user.authorities.any {
            it.authority == "ROLE_WS_GROUP_ADMIN"
        })) {
            List reportedUsers = GroupsUserSpamDtl.findAllByGroupIdAndUsername(new Long(params.groupId), params.username)
            reportedUsers.collect { groupusers ->
                GroupsUserSpamDtl groupsUserSpamDtl = GroupsUserSpamDtl.findById(groupusers.id)
                groupsUserSpamDtl.status = "deleted"
                groupsUserSpamDtl.save(failOnError: true, flush: true)
            }
            GroupsMembersDtl.executeUpdate("delete GroupsMembersDtl where groupId=" + params.groupId + " and username='" + params.username + "'")
            getGroupMembersCount(new Long(params.groupId))
            getGroupAdminCount(new Long(params.groupId))
            resetGroupsPostedByUser(User.findByUsername(params.username).id)
            json = ['status': 'OK']
        }else{
            json = ['status': 'access denied']
        }
        return json
    }

    def getGroupDetailsById(request){
        if (request.getHeader('Content-Type')!=null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            GroupsMst groupsMst = GroupsMst.findById(new Long(jsonObj.groupId))
            GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(new Long(jsonObj.groupId),springSecurityService.currentUser.username)
            GroupsMembersDtl groupsMembersDtl1
            if(groupsMst.groupType=="channel" && groupsMembersDtl==null&&groupsMst.batchId==null&&groupsMst.instituteId==null) {
                groupsMembersDtl1 = new GroupsMembersDtl(username: springSecurityService.currentUser.username, role: 'user',
                        profilepic: User.findByUsername(springSecurityService.currentUser.username).profilepic ? User.findByUsername(springSecurityService.currentUser.username).profilepic : null,
                        groupId: jsonObj.groupId, name: User.findByUsername(springSecurityService.currentUser.username).name, userId: User.findByUsername(springSecurityService.currentUser.username).id)
                groupsMembersDtl1.save(failOnError: true, flush: true)
                getGroupMembersCount(new Long(groupsMst.id))
            }
            def json = ["id": groupsMst.id, "groupName": groupsMst.name, "colorCode": groupsMst.colorCode, "createdBy": groupsMst.createdBy, "visibility": groupsMst.visibility,
                        "privacyType": groupsMst.privacyType, "image": groupsMst.image, "dateCreated": groupsMst.dateCreated ,"description":groupsMst.description?groupsMst.description:null,
                        "userExist":groupsMembersDtl?'yes':((groupsMembersDtl1?'yes':'No')),userType: groupsMembersDtl?groupsMembersDtl.role:((groupsMembersDtl1?groupsMembersDtl1.role:'')),
                        allPost: groupsMst.allPost?groupsMst.allPost:"true",groupType:groupsMst.groupType, batchId:groupsMst.batchId, instituteId:groupsMst.instituteId]
            if(user.authorities.any {
                it.authority == "ROLE_WS_GROUP_ADMIN"
            } && jsonObj?.mode=="monitor")
            {
                json["userType"]="super_admin"
                json["userExist"]="yes"
            }
            return json
        }
    }

    def rejectAllGroupRequest(request){
        String status="fail"
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (canEditGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                GroupsRequestDtl.executeUpdate("update GroupsRequestDtl set status='deleted' where groupId=" + jsonObj.groupId + " and status is null")
                status="OK"
            }
        }
        def json = ["status":status]
        return json
    }

    def approveAllGroupRequest(request){
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (canEditGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                List groupsRequestDtl = GroupsRequestDtl.findAllByGroupIdAndStatusIsNull(new Long(jsonObj.groupId))
                groupsRequestDtl.collect { groupusers ->
                    if (!GroupsMembersDtl.findByUsernameAndGroupId(groupusers.requestedBy, new Long(jsonObj.groupId))) {
                        GroupsMembersDtl groupsMembersDtl = new GroupsMembersDtl(username: groupusers.requestedBy, role: 'user', profilepic: User.findByUsername(groupusers.requestedBy).profilepic ? User.findByUsername(groupusers.requestedBy).profilepic : null, groupId: jsonObj.groupId, name: User.findByUsername(groupusers.requestedBy).name, userId: User.findByUsername(groupusers.requestedBy).id)
                        groupsMembersDtl.save(failOnError: true, flush: true)
                        resetGroupsPostedByUser(User.findByUsername(groupusers.requestedBy).id)
                    }
                }
                GroupsRequestDtl.executeUpdate("update GroupsRequestDtl set status='accepted' where groupId=" + jsonObj.groupId + " and status is null")
                getGroupMembersCount(new Long(jsonObj.groupId))
            }
        }
        def json = ["status":"OK"]
        return json
    }

    def reportGroupAsSpam(request){
        def json
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            GroupsSpamDtl groupsSpamDtl = new GroupsSpamDtl(groupId: new Long(jsonObj.groupId), reason: jsonObj.reason ? jsonObj.reason : null, reportedBy: springSecurityService.currentUser.username)
            groupsSpamDtl.save(failOnError: true, flush: true)
            json = ["status": "OK"]
        }
        return json
    }

    def viewGroupUsers(params){
        List groupsMembersDtl = GroupsMembersDtl.findAllByGroupId(new Long(params.groupId))
        List groupsMembers = groupsMembersDtl.collect { groupusers ->
            return [id: groupusers.id, username: groupusers.username,'dateAdded':groupusers.dateCreated,name: groupusers.name,profilepic: User.findByUsername(groupusers.username).profilepic ? User.findByUsername(groupusers.username).profilepic : null,userType: groupusers.role,userId: User.findByUsername(groupusers.username).id]
        }
        def json = ["groups":groupsMembers?groupsMembers:"no records found"]
        return json
    }

    def getGroupsUsers(groupId){
        List groupsMembersDtl = GroupsMembersDtl.findAllByGroupId(new Long(groupId))
        List groupsMembers = groupsMembersDtl.collect { groupusers ->
            return [id: groupusers.id, username: groupusers.username,'dateAdded':groupusers.dateCreated,name: groupusers.name,profilepic: User.findByUsername(groupusers.username).profilepic ? User.findByUsername(groupusers.username).profilepic : null,userType: groupusers.role,userId: User.findByUsername(groupusers.username).id]
        }
        Gson gson = new Gson();
        String element = gson.toJson(groupsMembers,new TypeToken<List>() {}.getType())
        redisService.("getGroupsUsers_"+groupId) = element
    }

    def fetchGroupPendingRequests(params){
        List groupsRequestDtl = GroupsRequestDtl.findAllByGroupIdAndStatusIsNull(new Long(params.groupId))
        List groupsReq = groupsRequestDtl.collect { groupusers ->
            return [ id:groupusers.id ,username: groupusers.requestedBy,'dateAdded':groupusers.dateCreated,profilepic: User.findByUsername(groupusers.requestedBy).profilepic ? User.findByUsername(groupusers.requestedBy).profilepic : null,userId: User.findByUsername(groupusers.requestedBy).id,name: User.findByUsername(groupusers.requestedBy).name,reason:groupusers.reason?groupusers.reason:'']
        }
        def json = ["groupsRequest":groupsReq?groupsReq:"no records found"]
        return json
    }

    def makeUserAsGroupAdmin(request){
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            String[] users = jsonObj.username.split(",")
            for(int i=0;i<users.length;i++) {
                if (users[i] == null || (users[i].trim().length() == 0)) {
                    continue
                } else {
                    if (canEditGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                        GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByUsernameAndGroupId(users[i], new Long(jsonObj.groupId))
                        groupsMembersDtl.role = "admin"
                        groupsMembersDtl.save(failOnError: true, flush: true)
                    }
                }
            }
            getGroupAdminCount(new Long(jsonObj.groupId))
            def json = ['status': "OK"]
            return json
        }
    }

    def removeUserAsGroupAdmin(request){
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (canEditGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByUsernameAndGroupId(jsonObj.username, new Long(jsonObj.groupId))
                groupsMembersDtl.role = "user"
                groupsMembersDtl.save(failOnError: true, flush: true)
                getGroupMembersCount(new Long(jsonObj.groupId))
                getGroupAdminCount(new Long(jsonObj.groupId))
                def json = ['status': "OK"]
                return json
            }
        }
    }

    def getGroupPostCount(groupId){
        String sql = " Select count(id) from groups_post_dtl gpd where gpd.group_id="+groupId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String postCount= results[0][0]
        redisService.("getGroupPostCount_"+groupId) = postCount+""
    }


    def getGroupPostLikeCount(postId){
        String sql = " Select count(id) from groups_posts_likes_dtl gpd where gpd.post_id="+postId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String postCount= results[0][0]
        redisService.("getGroupPostLikeCount_"+postId) = postCount+""
    }

    def getGroupPostCommentsCount(postId){
        String sql = " Select count(id) from groups_post_comments_dtl gpcd where gpcd.post_id="+postId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String postCommentsCount= results[0][0]
        redisService.("getGroupPostCommentsCount_"+postId) = postCommentsCount+""
    }

    def getGroupPostCommentsReplyCount(commentId){
        String sql = " Select count(id) from groups_comments_reply_dtl gpcrd where gpcrd.comment_id="+commentId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String postCommentsReplyCount= results[0][0]
        redisService.("getGroupPostCommentsReplyCount_"+commentId) = postCommentsReplyCount+""
    }

    @Transactional
    def repliedDetailsByCommentsId(commentId){
        List postCommentsReplyList
        String sql = "select gcrd.id,DATE_ADD(gcrd.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created,gmd.name,gmd.user_id,gmd.profilepic,gmd.username," +
                "                gcrd.created_by,gcrd.description" +
                "                  from groups_comments_reply_dtl gcrd LEFT JOIN groups_members_dtl  gmd ON gcrd.group_id = gmd.group_id and gcrd.created_by = gmd.username" +
                "                 where gcrd.comment_id IN (" + commentId + ")"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        postCommentsReplyList = results.collect { comments ->
            return [id   : comments.id, dateCreated: comments.date_created, createdBy: comments.created_by, description: comments.description,name:comments.name,userId:comments.user_id,profilepic:comments.profilepic,username:comments.username
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(postCommentsReplyList,new TypeToken<List>() {}.getType())
        redisService.("repliedDetailsByCommentsId_"+commentId) = element
        redisService.("repliedDetailsByCommentsIdCount_"+commentId) = postCommentsReplyList.size()+""
    }


    @Transactional
    def commentsDetailsByGroupId(postId,pageNo){
        int noOfItems = 10
        int startingIndex = noOfItems*pageNo
        List postCommentsList
        String sql = "select gpcd.id,DATE_ADD(gpcd.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created,gmd.name,gmd.user_id,gmd.profilepic,gmd.username," +
                "                gpcd.created_by,gpcd.description" +
                "                  from groups_post_comments_dtl gpcd LEFT JOIN groups_members_dtl  gmd ON gpcd.group_id = gmd.group_id and gpcd.created_by = gmd.username" +
                "                 where gpcd.post_id IN (" + postId + ") order by id desc limit "+startingIndex+","+noOfItems
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        postCommentsList = results.collect { comments ->
            if (redisService.("repliedDetailsByCommentsId_" + comments.id) == null || redisService.("repliedDetailsByCommentsId_" + comments.id) == "null") {
                repliedDetailsByCommentsId(comments.id)
            }
            if (redisService.("getGroupPostCommentsReplyCount_" + comments.id) == null || redisService.("getGroupPostCommentsReplyCount_" + comments.id) == "null" ) {
                getGroupPostCommentsReplyCount(comments.id)
            }
            List repliedDetailsByCommentsId= new JsonSlurper().parseText(redisService.("repliedDetailsByCommentsId_" + comments.id))
            return [id   : comments.id, dateCreated: comments.date_created, createdBy: comments.created_by, description: comments.description,name:comments.name,userId:comments.user_id,profilepic:comments.profilepic,username:comments.username,
                    repliedDetailsByCommentsId:repliedDetailsByCommentsId,repliedCount:redisService.("getGroupPostCommentsReplyCount_" + comments.id)]
        }
        Gson gson = new Gson();
        String element = gson.toJson(postCommentsList,new TypeToken<List>() {}.getType())
        redisService.("commentsDetailsByGroupId_" + postId+"_"+pageNo) = element
        redisService.("commentsDetailsByGroupIdCount_"+postId) = postCommentsList.size()+""
    }

    @Transactional
    def getMyGroupsList(params){
        String sql = "select gm.id, gm.name, gm.privacy_type, gm.visibility, gm.image, gm.date_created,gmd.role,gm.description,gm.color_code,gm.group_type "+
                " from groups_mst gm,groups_members_dtl gmd"+
                "  WHERE gmd.group_id=gm.id and  gmd.username='"+springSecurityService.currentUser.username+"' ";
        println(sql)

        if(params.search!=null && params.search!="") {
            sql += " and  (gm.name LIKE '%"+params.search+"%' )";
        }
        if(params.start!=null && params.start!="" && params.length!=null && params.length!="") {
            sql +=  " order by  gm.date_created  desc limit " + params.start + "," +  params.length + "";
        }else{
            sql +=  " order by  gm.date_created desc";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List groups = results.collect { group ->
            if(redisService.("getGroupMembersCount_"+group.id)==null || redisService.("getGroupMembersCount_"+group.id)=="null") {
                getGroupMembersCount(group.id)
            }
            return [id: group.id, groupName: group.name,colorCode: group.color_code,privacyType: group.privacy_type,visibility:group.visibility,'image':group.image,description:group.description?group.description:null,'dateAdded':group.date_created,'membersCount':redisService.("getGroupMembersCount_"+group.id),'userType':group.role,'postCount':0,groupType: group.group_type?group.group_type:'']
        }
        def json = ["status":groups?"success":"No records","groupsList":groups?groups:""]
        return json
    }

    @Transactional
    def getGroupsList(params){
        String userGroupIds=""
        String groupIds=""
        List groupsMembersDtl = GroupsMembersDtl.findAllByUsername(springSecurityService.currentUser.username)
        groupsMembersDtl.each { qrdtl ->
            userGroupIds +=  " "+qrdtl.groupId+","
        }
        if(!userGroupIds.equals("")){
            groupIds  = userGroupIds?userGroupIds.substring(0, userGroupIds.length() - 1):""
        }

        String sql = "select gm.id, gm.name, gm.privacy_type, gm.visibility, gm.image, gm.date_created,gm.description,gm.color_code,gm.group_type,(SELECT COUNT(*) FROM " +
                " groups_members_dtl gmd WHERE gm.id = gmd.group_id) AS memberCount "+
                " from groups_mst gm "+
                " WHERE 1=1 and gm.site_id="+params.siteId
        if(!groupIds.equals("")) {
            sql += " and gm.id NOT IN (" + groupIds + ") ";
        }
        sql +=" and (gm.visibility!='hidden' OR gm.visibility is null)";
        if(params.search!=null && params.search!="") {
            sql += " and  (gm.name LIKE '%"+params.search+"%' )";
        }
        if(params.start!=null && params.start!="" && params.length!=null && params.length!="") {
            sql +=  " order by memberCount  desc limit " + params.start + "," +  params.length + "";
        }else{
            sql +=  " order by memberCount desc";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List groups = results.collect { group ->
            if(redisService.("getGroupMembersCount_"+group.id)==null || redisService.("getGroupMembersCount_"+group.id)=="null") {
                getGroupMembersCount(group.id)
            }
            return [id: group.id, groupName: group.name,colorCode: group.color_code,privacyType: group.privacy_type,visibility:group.visibility,'image':group.image,description:group.description?group.description:null,'dateAdded':group.date_created,'membersCount':redisService.("getGroupMembersCount_"+group.id),'postCount':0,groupType: group.group_type?group.group_type:'']
        }
        def json = ["status":groups?"success":"No records","groupsList":groups?groups:""]
        return json
    }

    def getPostDetails(groupId,int pageNo){
        int noOfItems = 10
        int startingIndex = noOfItems*pageNo
        def sql ="select gpd.id,gpd.description," +
                "DATE_ADD(gpd.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created,gpd.created_by,gpd.image,gpd.file_path,gpd.file_name,gpd.pinned,gmd.name,gmd.user_id,gmd.profilepic,gmd.username,gmd.role from groups_post_dtl gpd LEFT JOIN groups_members_dtl  gmd ON gpd.group_id = gmd.group_id and gpd.created_by = gmd.username  where 1=1 and " +
                "gpd.group_id="+groupId+" order by id desc limit "+startingIndex+","+noOfItems
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List postsDetails = results.collect { post ->
            User user = dataProviderService.getUserMst(post.created_by)
            return [id : post.id,description:post.description?post.description:null,createdBy:post.created_by,dateCreated:post.date_created,postImage:post.image,fileName:post.file_name,filePath:post.file_path,name:user.name,userId:post.user_id,profilepic:post.profilepic,username:post.username,userType:post.role,pinned:post.pinned,
                    commentsCount:redisService.("getGroupPostCommentsCount_" + post.id)]
        }
        Gson gson = new Gson();
        String element = gson.toJson(postsDetails,new TypeToken<List>() {}.getType())
        redisService.("getPostDetailsByGroupId_"+groupId+"_"+pageNo) = element
    }


    def getGroupMembersCount(Long groupId){
        List membersCount = GroupsMembersDtl.findAllByGroupId(new Long(groupId))
        redisService.("getGroupMembersCount_"+groupId) = membersCount.size()+""
    }


    def resetCacheForGroupPosts(groupId){
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        String sql = " Select count(id) from groups_post_dtl gpd where gpd.group_id="+groupId
        def  results = sql1.rows(sql);
        int numberOfItems = Integer.parseInt(""+results[0][0]).intValue()
        int noOfRedisItems = numberOfItems / 10
        for(int i=0;i<=noOfRedisItems;i++){
            redisService.deleteKeysWithPattern("getPostDetailsByGroupId_"+groupId+"_"+i)
        }
    }

    def ignoreReportedGroupUser(params){
        GroupsUserSpamDtl groupsUserSpamDtl = GroupsUserSpamDtl.findByUsernameAndGroupIdAndStatusIsNull(params.username,new Long(params.groupId))
        if( groupsUserSpamDtl!=null ){
            groupsUserSpamDtl.status="ignored"
            groupsUserSpamDtl.save(failOnError: true, flush: true)
            def json = [ status:"OK"]
            return json
        }
    }

    def ignoreReportedGroupPost(params){
        List groupsPostsSpamDtl = GroupsPostsSpamDtl.findAllByPostId(new Long(params.postId))
        groupsPostsSpamDtl.collect { groupusers ->
            GroupsPostsSpamDtl groupsPostsSpamDtls = GroupsPostsSpamDtl.findById(new Long(groupusers.id))
            groupsPostsSpamDtls.status="ignored"
            groupsPostsSpamDtls.save(failOnError: true, flush: true)
        }
        def json = [ status:"OK"]
        return json
    }

    def userExitGroup(params){
        GroupsMembersDtl groupsMembersDtl =GroupsMembersDtl.findByGroupIdAndUsername(new Long(params.groupId),springSecurityService.currentUser.username)
        groupsMembersDtl.delete(failOnError: true, flush: true)
        getGroupMembersCount(new Long(params.groupId))
        getGroupAdminCount(new Long(params.groupId))
        resetGroupsPostedByUser(User.findByUsername(springSecurityService.currentUser.username).id)
        def json = [ status:"OK"]
        return json
    }

    def getCommentListForPost(params){
        if (redisService.("commentsDetailsByGroupId_" + params.postId+"_"+params.pageNo) == null || redisService.("commentsDetailsByGroupId_" + params.postId+"_"+params.pageNo) == "null") {
            commentsDetailsByGroupId(params.postId,new Integer(params.pageNo))
        }
        List commentsDetailsByGroupId= new JsonSlurper().parseText(redisService.("commentsDetailsByGroupId_" + params.postId+"_"+params.pageNo))
        def json=["commentsList":commentsDetailsByGroupId?commentsDetailsByGroupId:"no records"]
        return json
    }

    def getUserRequestStatus(params){
        String status=""
        GroupsRequestDtl groupsRequestDtl = GroupsRequestDtl.findByGroupIdAndRequestedByAndStatusIsNull(new Long(params.groupId),springSecurityService.currentUser.username)
        if(groupsRequestDtl!=null){
            status="Requested"
        }
        def json=["status":status]
        return json
    }

    def getPostDetailsById(params){
        def json
        GroupsPostDtl groupsPostDtl = GroupsPostDtl.findById(new Long(params.postId))
        if(groupsPostDtl!=null) {
            json = ['postId': groupsPostDtl.id, 'description':groupsPostDtl.description?groupsPostDtl.description:null, 'image': groupsPostDtl.image, 'filePath': groupsPostDtl.filePath, 'fileName': groupsPostDtl.fileName]
        }else{
            json=[status: 'invalid id']
        }
        return json
    }

    @Transactional
    def getGroupIdsUser(){
        String groupIds=""
        List userGroups=GroupsMembersDtl.findAllByUsername(springSecurityService.currentUser.username)
        userGroups.collect { groupusers ->
            groupIds +=groupusers.groupId+","
        }
        def json=["userGroups":groupIds?groupIds.substring(0,(groupIds.length()-1)):"No records"]
        return json
    }

    @Transactional
    def updateGroupUser(Long userId){
        User user=User.findById(userId)
        if(user){
            List groupsmember=GroupsMembersDtl.findAllByUsername(user.username)
            if(groupsmember) {
                groupsmember.collect { groupmember ->
                    groupmember.name = user.name
                    groupmember.profilepic = user.profilepic
                    groupmember.save(failOnError: true, flush: true)
                }
                resetGroupsPostedByUser(user.id)
            }
        }
    }

    @Transactional
    def resetGroupsPostedByUser(Long userId){
        User user=User.findById(userId)
        if(user){
            def sql ="select distinct(gpd.group_id) from groups_post_dtl gpd where gpd.created_by='"+user.username+"'"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            results.collect { groups ->
                resetCacheForGroupPosts(new Long(groups.group_id))
                getGroupPinnedPosts(groups.group_id)
            }
            resetCacheForGroupCommentsByUser(user.username)
            resetCacheForCommentRepliesByUser(user.username)
        }
    }

    def resetCacheForGroupCommentsByUser(username){
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        String sql2 = "select distinct(gpcd.post_id) from groups_post_comments_dtl gpcd where gpcd.created_by='"+username+"'"
        def results1 = sql1.rows(sql2)
        results1.collect{posts ->
            resetCacheForPostComments(posts.post_id)
        }
    }

    def resetCacheForCommentRepliesByUser(username){
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        String sql = "select distinct(gcrd.comment_id) from groups_comments_reply_dtl gcrd where gcrd.created_by='"+username+"'"
        def  results = sql1.rows(sql);
        results.collect{comments ->
            redisService.("repliedDetailsByCommentsId_" + comments.comment_id)=null
        }
    }

        def getGroupAdminCount(Long groupId) {
            List adminsCount = GroupsMembersDtl.findAllByGroupIdAndRole(new Long(groupId),"admin")
            redisService.("getGroupAdminsCount_"+groupId) = adminsCount.size()+""
       }

    def Integer getSiteId(request) {
        Integer siteId = new Integer(1)
        def jsonObj = request.JSON
        if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
        else if(request.getParameter("siteId")!=null) siteId = new Integer(request.getParameter("siteId"));
        return siteId
    }


    def getLatestPostForGroupWall(Integer siteId) {
        def groupId
        def latestPostId
        groupId = getGroupWallIdForSite(siteId)
        def sql = "select max(id) from groups_post_dtl where group_id=" + groupId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        results.each { result ->
            latestPostId = result[0]
        }
        redisService.("groupWallId_" + siteId + "_latestPostId") = latestPostId
    }

    def getlatestPostCountForGroupWall(Integer siteId) {
        def groupId
        def latestPostId
        groupId = getGroupWallIdForSite(siteId)
        def sql = "SELECT count(id) as postCount FROM groups_post_dtl where group_id=" + groupId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        results.each { result ->
            latestPostId = result[0]
        }
        redisService.("groupWallId_" + siteId + "_latestPostCount") = latestPostId
    }

    def getGroupWallIdForSite(Integer siteId){
        def groupId
        if((redisService.("groupWallId_"+siteId)==null) || (redisService.("groupWallId_"+siteId)=="null"))
        {
            KeyValueMst keyValueMst=KeyValueMst.findByKeyNameAndSiteId("groupWallId",siteId)
            redisService.("groupWallId_"+siteId) = keyValueMst?.keyValue
        }
        groupId=redisService.("groupWallId_"+siteId)
        return groupId
    }


    def getGroupWallDetailsForSite(params) {
        def siteId = params?.siteId
        def json = []
        if (siteId != null) {
            def groupId
            if ((redisService.("groupWallId_" + siteId) == null) || (redisService.("groupWallId_" + siteId) == "null")) {
                KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("groupWallId", siteId)
                redisService.("groupWallId_" + siteId) = keyValueMst?.keyValue
            }
            groupId = redisService.("groupWallId_" + siteId)
            if (groupId != null && groupId != "null") {
                GroupsMst groupsMst = GroupsMst.findById(new Long(groupId))
                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(new Long(groupId), springSecurityService.currentUser.username)
                GroupsMembersDtl groupsMembersDtl1
                if (groupsMst.groupType == "channel" && groupsMembersDtl == null) {
                    groupsMembersDtl1 = new GroupsMembersDtl(username: springSecurityService.currentUser.username, role: 'user', profilepic: User.findByUsername(springSecurityService.currentUser.username).profilepic ? User.findByUsername(springSecurityService.currentUser.username).profilepic : null,
                            groupId: groupId, name: User.findByUsername(springSecurityService.currentUser.username).name, userId: User.findByUsername(springSecurityService.currentUser.username).id)
                    groupsMembersDtl1.save(failOnError: true, flush: true)
                    getGroupMembersCount(new Long(groupsMst.id))
                }
                json = ["id": groupsMst.id, "groupName": groupsMst.name, "colorCode": groupsMst.colorCode, "createdBy": groupsMst.createdBy, "visibility": groupsMst.visibility, "privacyType": groupsMst.privacyType, "image": groupsMst.image, "dateCreated": groupsMst.dateCreated, "description": groupsMst.description ? groupsMst.description : null, "userExist": groupsMembersDtl ? 'yes' : ((groupsMembersDtl1 ? 'yes' : 'No')), userType: groupsMembersDtl ? groupsMembersDtl.role : ((groupsMembersDtl1 ? groupsMembersDtl1.role : '')), allPost: groupsMst.allPost ? groupsMst.allPost : "true", groupType: groupsMst.groupType]
                return json
            }
        }
        return json
    }

    def makePostAsPinned(request){
        String status="fail"
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            if (canEditGroup(springSecurityService.currentUser.username, jsonObj.groupId)) {
                if(jsonObj.pinned.equals("true")){
                    GroupsPostDtl.executeUpdate("update GroupsPostDtl set pinned='true' where id=" + jsonObj.postId)
                    status="OK"
                }
                else if(jsonObj.pinned.equals("false")){
                    GroupsPostDtl.executeUpdate("update GroupsPostDtl set pinned='false' where id=" + jsonObj.postId)
                    status="OK"
                }
                resetCacheForGroupPosts(jsonObj.groupId)
                getGroupPinnedPosts(jsonObj.groupId)
            }
        }
        def json = ["status":status]
        return json
    }

    def getGroupPinnedPosts(groupId) {
            def sql ="select gpd.id,gpd.description," +
                    "DATE_ADD(gpd.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created,gpd.created_by,gpd.image,gpd.file_path,gpd.file_name,gpd.pinned,gmd.name,gmd.user_id,gmd.profilepic,gmd.username,gmd.role from groups_post_dtl gpd LEFT JOIN groups_members_dtl  gmd ON gpd.group_id = gmd.group_id and gpd.created_by = gmd.username  where 1=1 and " +
                    "gpd.group_id="+groupId+" and gpd.pinned='true' order by id"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            List postsDetails = results.collect { post ->
                return [id : post.id,description:post.description?post.description:null,createdBy:post.created_by,dateCreated:post.date_created,postImage:post.image,fileName:post.file_name,filePath:post.file_path,name:post.name,userId:post.user_id,profilepic:post.profilepic,username:post.username,userType:post.role,pinned:post.pinned,
                        commentsCount:redisService.("getGroupPostCommentsCount_" + post.id)]
            }
            Gson gson = new Gson();
            String element = gson.toJson(postsDetails,new TypeToken<List>() {}.getType())
            redisService.("getGroupPinnedPosts_"+groupId) = element
    }


    def getAllGroupDetails(params){
        def sql = "SELECT gm.id,gm.name,gm.privacy_type,gm.date_created,(SELECT COUNT(*) FROM groups_posts_likes_dtl WHERE group_id = gm.id) AS likesCount,"+
                "(SELECT COUNT(*) FROM groups_post_dtl WHERE group_id = gm.id) AS postsCount,(SELECT COUNT(*) FROM groups_post_comments_dtl WHERE group_id = gm.id) AS commentsCount,"+
                "(SELECT COUNT(*) FROM groups_members_dtl WHERE group_id = gm.id) AS membersCount"+
                " FROM groups_mst gm WHERE gm.group_type='group'"
        if(params.startDate!=null && params.startDate!='') sql+=" and date(DATE_ADD(gm.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.startDate +"','%d-%m-%Y')"
        if(params.endDate!=null && params.endDate!='') sql+=" and date(DATE_ADD(gm.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.endDate +"','%d-%m-%Y')"
        sql+=" order by gm.id desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        results.each { result ->
            return [id : result.id,name:result.name?result.name:null,privacy_type:result.privacy_type,dateCreated:result.date_created,likesCount:result.likesCount,postsCount:result.postsCount,commentsCount:result.commentsCount,membersCount:result.membersCount]
        }
        def json=["groupList":results?results:"No records"]
        return json
    }

    def resetCacheForPostComments(postId){
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        String sql = " Select count(id) from groups_post_comments_dtl gpcd where gpcd.post_id="+postId
        def  results = sql1.rows(sql);
        int numberOfItems = Integer.parseInt(""+results[0][0]).intValue()
        int noOfRedisItems = numberOfItems / 10
        for(int i=0;i<=noOfRedisItems;i++){
            redisService.("commentsDetailsByGroupId_"+postId+"_"+i)=null
        }
    }

    def getCompletePostDetails(params){
        List postsDetails
        def groupId = params?.groupId
        def postId = params?.postId
        if(groupId) {
            if (canAccessGroup(springSecurityService.currentUser.username, groupId)) {
                def sql = "select gpd.id,gpd.description," +
                        "DATE_ADD(gpd.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created,gpd.created_by,gpd.image,gpd.file_path,gpd.file_name,gpd.pinned,gmd.name,gmd.user_id,gmd.profilepic,gmd.username,gmd.role from groups_post_dtl gpd LEFT JOIN groups_members_dtl  gmd ON gpd.group_id = gmd.group_id and gpd.created_by = gmd.username  where 1=1 and " +
                        "gpd.group_id=" + groupId + " and gpd.id=" + postId
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                postsDetails = results.collect { post ->
                    return [id           : post.id, description: post.description ? post.description : null, createdBy: post.created_by, dateCreated: post.date_created, postImage: post.image, fileName: post.file_name, filePath: post.file_path, name: post.name, userId: post.user_id, profilepic: post.profilepic, username: post.username, userType: post.role, pinned: post.pinned,
                            commentsCount: redisService.("getGroupPostCommentsCount_" + post.id)]
                }
            }
        }
        return postsDetails
    }
}
