package com.wonderslate.comparison

import grails.converters.JSON
import grails.transaction.Transactional

import java.util.regex.Pattern

@Transactional
class SorensenDiceService {

    private static final int DEFAULT_K = 3;

    private static final int k=2;

    /**
     * Pattern for finding multiple following spaces.
     */
    private static final Pattern SPACE_REG = Pattern.compile("\\s+");

    /**
     *
     * @param k
     * @throws IllegalArgumentException if k is <= 0
     */

    public static final int getK() {
        return k;
    }

    public final Map<String, Integer> getProfile(final String string) {
        HashMap<String, Integer> shingles = new HashMap<String, Integer>();

        String string_no_space = SPACE_REG.matcher(string).replaceAll(" ");
        for (int i = 0; i < (string_no_space.length() - k + 1); i++) {
            String shingle = string_no_space.substring(i, i + k);
            Integer old = shingles.get(shingle);
            if (old != null) {
                shingles.put(shingle, old + 1);
            } else {
                shingles.put(shingle, 1);
            }
        }

        return Collections.unmodifiableMap(shingles);
    }

    /**
     * Similarity is computed as 2 * |A inter B| / (|A| + |B|).
     *
     * @param s1 The first string to compare.
     * @param s2 The second string to compare.
     * @return The computed Sorensen-Dice similarity.
     * @throws NullPointerException if s1 or s2 is null.
     */
    public final double similarity(final String s1, final String s2) {

        if (s1 == null) {
            throw new NullPointerException("s1 must not be null");
        }

        if (s2 == null) {
            throw new NullPointerException("s2 must not be null");
        }

        if (s1.equals(s2)) {
            return 1;
        }

        Map<String, Integer> profile1 = getProfile(s1);
        Map<String, Integer> profile2 = getProfile(s2);

        Set<String> union = new HashSet<String>();
        union.addAll(profile1.keySet());
        union.addAll(profile2.keySet());

        int inter = 0;

        for (String key : union) {
            if (profile1.containsKey(key) && profile2.containsKey(key)) {
                inter++;
            }
        }

        return 2.0 * inter / (profile1.size() + profile2.size());
    }

    /**
     * Returns 1 - similarity.
     *
     * @param s1 The first string to compare.
     * @param s2 The second string to compare.
     * @return 1.0 - the computed similarity
     * @throws NullPointerException if s1 or s2 is null.
     */
    public final double distance(final String s1, final String s2) {
        return 1 - similarity(s1, s2);
    }

    public JSON comparePassage(String idealAnswer,String userAnswer){
     //   println("idealAnswer="+idealAnswer)
       // println("userAnswer="+userAnswer)

        //remove the conjunctions
        idealAnswer = idealAnswer.replaceAll(" and ",".").replaceAll(" or ",".").trim()
        userAnswer = userAnswer.replaceAll(" and ",".").replaceAll(" or ",".").trim()

        String[] idealAnswers = idealAnswer.split("\\.")
        String[] userAnswers = userAnswer.split("\\.")
    //    println("first part of idealAnswer="+idealAnswers[0])
   //     println("the size of ideal answers="+idealAnswers.length)
    //    println("the size of user answers="+userAnswers.length)
       // println(" first idealAnswer="+idealAnswers[0].replaceAll("\\n"," "))
//        println(" first  userAnswer="+userAnswers[0].replaceAll("\\n"," "))


     //   println("1st comparison is "+similarity(idealAnswers[0].replaceAll("\\n"," "),userAnswers[0].replaceAll("\\n"," ")))

        String[] closestToIdealAnswer = new String[idealAnswers.length]
        double[] highestScores = new double [idealAnswers.length]
        String[] failedSentences = new String[userAnswers.length];
        double highestSimilarity
        double tempSimilarity;
        double totalSimilarity=0;
        for(int i=0;i<idealAnswers.length;i++){

            highestSimilarity=0;
            for(int j=0;j<userAnswers.length;j++){
                tempSimilarity = similarity(idealAnswers[i],userAnswers[j])
                if(tempSimilarity>highestSimilarity) {
                    highestSimilarity=tempSimilarity
                    closestToIdealAnswer[i]= userAnswers[j]
                    highestScores[i] = highestSimilarity
                }}
            if(highestSimilarity<0.3){
                highestScores[i] = 0;
                closestToIdealAnswer[i]="";

            }else{
                totalSimilarity +=highestSimilarity;
            }
            println("the highest similarity for "+i+"="+highestScores[i])
            println(idealAnswers[i]+"="+closestToIdealAnswer[i])

        }

        double percentageCorrect = totalSimilarity/idealAnswers.length*100;
        println("percentageCorrect="+percentageCorrect)

        //loop for failed sentences
        for(int i=0;i<userAnswers.length;i++){
            boolean rightAnswer=false;
            for(int j=0;j<closestToIdealAnswer.length;j++){
                if(closestToIdealAnswer[j]==userAnswers[i]){
                    rightAnswer=true;
                    break;
                }
            }
            if(!rightAnswer) {
                failedSentences[i] = userAnswers[i];
                println("failed sentences="+userAnswers[i])

            }
        }
     List highScores = Arrays.asList(highestScores)
     List closeAnswers = Arrays.asList(closestToIdealAnswer)
     List failSentences = Arrays.asList(failedSentences)
        println("closeAnswers="+closeAnswers)
        def json =  [
                'highScores':  highScores,
                'closeAnswers': closeAnswers,
                'failSentences': failSentences,
                'idealAnswer': Arrays.asList(idealAnswers),
                'userAnswer': Arrays.asList(userAnswers),
                'totalSimilarity':totalSimilarity
        ]
        println("json ="+json)

        return json as JSON


    }
}
