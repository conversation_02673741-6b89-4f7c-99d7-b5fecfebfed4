package com.wonderslate.toDo


import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ChaptersMst
import com.wonderslate.usermanagement.ToDo
import com.wonderslate.data.UtilService
import com.wonderslate.usermanagement.User
import grails.transaction.Transactional
import grails.web.api.ServletAttributes
import groovy.sql.Sql

import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat

@Transactional
class ToDoService implements  ServletAttributes{
    def springSecurityService
    DataProviderService dataProviderService
    UtilService utilService
    def redisService




    def addToDOTask(String taskName, Date taskDate, String fromTime, String toTime, String priority, long resId, String resLink, String resType, long batchId){
        ToDo toDoTask = new ToDo()
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        toDoTask.taskName = taskName
        toDoTask.taskDate = utilService.convertDate(taskDate,"IST","UTC")
        toDoTask.fromTime = fromTime
        toDoTask.toTime = toTime
        toDoTask.priority = priority
        toDoTask.resId = resId
        toDoTask.resLink = resLink
        toDoTask.resType = resType
        toDoTask.status = "Pending"
        toDoTask.userId = user.username
        toDoTask.dateCreated = new Date()
        toDoTask.batchId = batchId!=0?batchId:null
        toDoTask.save(failOnError: true, flush: true)
        pendingToDoCount()
        return toDoTask
    }

    def editToDoTask(String[] taskId, String action, String status, String taskName, Date taskDate, String fromTime, String toTime, String priority){
        if(action.equals("addStatus")) return addStatus(taskId,status)
        else if(action.equals("editTask")) {
            def toDoTask = ToDo.findById(Long.parseLong(taskId[0]))
           return editToDo(toDoTask,taskName,taskDate,fromTime,toTime,priority)
        }
    }

    def editToDo(ToDo toDoTask ,String taskName, Date taskDate, String fromTime, String toTime, String priority){
        if(taskName != null && !taskName.isEmpty()) toDoTask.taskName = taskName
        if(taskDate != null) toDoTask.taskDate = utilService.convertDate(taskDate,"IST","UTC")
        if(!fromTime.isEmpty()) toDoTask.fromTime = fromTime
        if(!toTime.isEmpty()) toDoTask.toTime = toTime
        if(priority != null && !priority.isEmpty()) toDoTask.priority = priority
        toDoTask = toDoTask.save(failOnError: true, flush: true)
        pendingToDoCount()
        return toDoTask
    }

    def addStatus(String[] taskId, String status){
        StringBuffer sb = new StringBuffer()
        sb.append(taskId[0])
        for(int i=1;i<taskId.length;i++){
            sb.append(",")
            sb.append(taskId[i])
        }
        String sql1 = "update ToDo set status='" + status + "' where id In(" + sb.toString() +")";
        Integer updateCount = ToDo.executeUpdate(sql1)
        pendingToDoCount()
       return updateCount
        // returns the number of records updated
    }

    def listToDoTask(String filterType,String filterValue,String status){
        List toDoList = null
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if(filterType == null || filterType.isEmpty()) {
            toDoList = ToDo.findAllByStatusAndUserId("Pending",user.username,[sort:"taskDate" ])
        }
        else if(filterType.equals("status")) toDoList = ToDo.findAllByStatusAndUserId(filterValue,user.username,[sort:"taskDate" ])
        else if(filterType.equals("date")) {
            DateFormat df1 = new SimpleDateFormat("dd-MM-yyyy");
            Date columnValue = df1.parse(filterValue);
            columnValue = utilService.convertDate(columnValue,"IST","UTC")
            if(status.isEmpty() || status == null) toDoList = ToDo.findAllByTaskDateAndUserId(columnValue,user.username,[sort:"taskDate" ])
            else toDoList = ToDo.findAllByTaskDateAndUserIdAndStatus(columnValue,user.username,status,[sort:"taskDate" ])
        }
        if(toDoList == null) toDoList = new ArrayList()
        List sendToDoList = new ArrayList();
                toDoList.each { task ->
            String pattern = "dd/MM/yyyy HH:mm:ss"
                    if(task.resId!=null){
//                        ChaptersMst chaptersMst=ChaptersMst.findby

                    }
            SimpleDateFormat sdfFrom = new SimpleDateFormat(pattern)
                    sendToDoList.add(['id':task.id,'fromTime':task.fromTime,'priority':task.priority,'status':task.status,'resId':task.resId,'taskName':task.taskName,'toTime':task.toTime,'taskDate':utilService.convertDate(task['taskDate'],"UTC","IST"),'date':task['taskDate']
            ,'date1':sdfFrom.format(convertDate(task['taskDate'], "UTC", "IST")),'resType':task.resType,'resLink':task.resLink,'batchId':task.batchId])
        }
        return sendToDoList
    }

    def pendingToDoCount(){

        String sql =
                "select count(*) from to_do where user_id='"+springSecurityService.currentUser.username+"' and status='Pending'"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        if(results.size()==0)  redisService.("userPendingTodoCount_"+springSecurityService.currentUser.username) = "0"
        else redisService.("userPendingTodoCount_"+springSecurityService.currentUser.username)=""+results[0][0]
        if(session.getAttribute("userdetails")!=null) session.setAttribute("userPendingTodoCount",redisService.("userPendingTodoCount_"+springSecurityService.currentUser.username))


    }



    def deleteTask(long id){
        def todo = ToDo.findById(id)
        todo.delete(failOnError: true,flush: true)
        pendingToDoCount()
    }

    private Date convertDate(Date dateFrom, String fromTimeZone, String toTimeZone) throws ParseException {
        String pattern = "yyyy/MM/dd HH:mm:ss"
        SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern)
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone))

        SimpleDateFormat sdfTo = new SimpleDateFormat (pattern)
        sdfTo.setTimeZone(TimeZone.getTimeZone(toTimeZone))
        Date dateTo = sdfFrom.parse(sdfTo.format(dateFrom))
        return dateTo
    }

}
