package com.wonderslate.cache

import com.ibookso.products.CategoryLevel1
import com.ibookso.products.CategoryLevel2
import com.ibookso.products.CategoryLevel3
import com.ibookso.products.CategoryLevel4
import com.ibookso.products.ExtPublishers
import com.ibookso.products.PrintBooksMst
import com.wonderslate.librarybooks.LibraryBooksService
import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class SearchService {
    def redisService
    def grailsApplication
    def servletContext
    LibraryBooksService libraryBooksService

    static HashMap getResourceTypes(){
        HashMap searchFilters = new HashMap()
        searchFilters.put("KeyValues","'KeyValues','Multiple Choice Questions'")
        searchFilters.put("Solutions","'Notes','QA','Short QA'")
        searchFilters.put("Multiple Choice Questions","'Multiple Choice Questions'")
        searchFilters.put("Notes","'Notes'")
        searchFilters.put("Reference Videos","'Reference Videos'")
        searchFilters.put("all","'KeyValues','Multiple Choice Questions','Notes','Reference Videos','Reference Web Links'")
        return searchFilters
    }

    def buildResourcesSearch(Integer siteId,String siteIdList,String resourceType){
        //resource specific searches to be specified here. Remember to send resourceType as one word
        HashMap searchFilters = getResourceTypes()
        println("resource type is "+resourceType+" and search thingy is "+searchFilters.get(resourceType))
           //first start with syllabus
            String sql  = "select distinct(syllabus)" +
                    " from resource_dtl rd,chapters_mst cm,books_mst bm" +
                    " where bm.site_id in ("+siteIdList+")" +
                    " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                    " and rd.res_link not like ('%.%') "+
                    " and cm.id=rd.chapter_id" +
                    " and cm.book_id=bm.id" +
                    " and bm.status='published'"+
                    " and syllabus is not null"

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)

            HashMap searchMainMap = new HashMap()
            results.each { book ->
                HashMap value = new HashMap()
                value.put("syllabus",book[0])
                searchMainMap.put(book[0],value)
            }

            //grade
           sql  = "select distinct(grade)" +
                " from resource_dtl rd,chapters_mst cm,books_mst bm" +
                " where bm.site_id in ("+siteIdList+")" +
                " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                   " and rd.res_link not like ('%.%') "+
                " and cm.id=rd.chapter_id" +
                " and cm.book_id=bm.id" +
                " and bm.status='published' and grade is not null"
            dataSource = grailsApplication.mainContext.getBean('dataSource')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql)

            results.each { book ->
                HashMap value = new HashMap()
                value.put("grade",book[0])
                searchMainMap.put(book[0],value)
            }

            //syllabus and grade
            sql  = "select distinct syllabus,grade" +
                    " from resource_dtl rd,chapters_mst cm,books_mst bm" +
                    " where bm.site_id in ("+siteIdList+")" +
                    " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                    " and rd.res_link not like ('%.%') "+
                    " and cm.id=rd.chapter_id" +
                    " and cm.book_id=bm.id" +
                    " and bm.status='published' and syllabus is not null and grade is not null"+
                    " group by syllabus,grade "
            dataSource = grailsApplication.mainContext.getBean('dataSource')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql)

            results.each { book ->
                HashMap value = new HashMap()
                value.put("syllabus",book[0])
                value.put("grade",book[1])
                searchMainMap.put(book[0]+" "+book[1],value)
            }

            //grade and subject
            sql  = "select distinct grade,subject" +
                    " from resource_dtl rd,chapters_mst cm,books_mst bm" +
                    " where bm.site_id in ("+siteIdList+")" +
                    " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                    " and rd.res_link not like ('%.%') "+
                    " and cm.id=rd.chapter_id" +
                    " and cm.book_id=bm.id" +
                    " and bm.status='published' and grade is not null and subject is not null"+
                    " group by grade,subject "
            dataSource = grailsApplication.mainContext.getBean('dataSource')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql)

            results.each { book ->
                HashMap value = new HashMap()
                value.put("grade",book[0])
                value.put("subject",book[1])
                searchMainMap.put(book[0]+" "+book[1],value)
            }

            //syllabus,grade and subject
            sql  = "select distinct syllabus,grade,subject" +
                    " from resource_dtl rd,chapters_mst cm,books_mst bm" +
                    " where bm.site_id in ("+siteIdList+")" +
                    " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                    " and rd.res_link not like ('%.%') "+
                    " and cm.id=rd.chapter_id" +
                    " and cm.book_id=bm.id" +
                    " and bm.status='published' and syllabus is not null and grade is not null and subject is not null"+
                    " group by syllabus,grade,subject "
            dataSource = grailsApplication.mainContext.getBean('dataSource')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql)

            results.each { book ->
                HashMap value = new HashMap()
                value.put("syllabus",book[0])
                value.put("grade",book[1])
                value.put("subject",book[2])
                searchMainMap.put(book[0]+" "+book[1]+" "+book[2],value)
            }

            //authors - have to add for user created and published. But two schemas are different. Not adding as of now.


            //titles
        //syllabus,grade and subject
        sql  = "select distinct bm.id,bm.title" +
                " from resource_dtl rd,chapters_mst cm,books_mst bm" +
                " where bm.site_id in ("+siteIdList+")" +
                " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                " and rd.res_link not like ('%.%') "+
                " and cm.id=rd.chapter_id" +
                " and cm.book_id=bm.id" +
                " and bm.status='published'"+
                " group by bm.id,bm.title "
        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new Sql(dataSource)
        results = sql1.rows(sql)

            results.each { book ->
                HashMap value = new HashMap()
                value.put("bookId",book[0])
                 searchMainMap.put(book[1],value)
            }

            //chapter names

        sql  = "select distinct cm.id,cm.name" +
                " from resource_dtl rd,chapters_mst cm,books_mst bm" +
                " where bm.site_id in ("+siteIdList+")" +
                " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                " and rd.res_link not like ('%.%') "+
                " and cm.id=rd.chapter_id" +
                " and cm.book_id=bm.id" +
                " and bm.status='published'"+
                " group by cm.id,cm.name "
            dataSource = grailsApplication.mainContext.getBean('dataSource')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql)

            results.each { book ->
                HashMap value = new HashMap()
                 value.put("chapterId",book[0])
                searchMainMap.put(book[1],value)
            }


        //resource names

        sql  = "select distinct rd.id,rd.resource_name" +
                " from resource_dtl rd" +
                " where rd.site_id in ("+siteIdList+")" +
                " and (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                " and rd.res_link not like ('%.%') "+
                " group by rd.id,rd.resource_name"
        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new Sql(dataSource)
        results = sql1.rows(sql)

        results.each { book ->
            HashMap value = new HashMap()
            value.put("resId",book[0])
            searchMainMap.put(book[1],value)
        }
        servletContext.setAttribute("resourceSearchMap"+resourceType+"_"+siteId,searchMainMap);
    }

    def updatePrintSearchMap(){
        //to control what to search first, create a list of hashmaps along with main HashMap
       HashMap searchMainMap = new HashMap()
         servletContext.setAttribute("searchPrintMainMap",searchMainMap);
        //first start with categories
        List categoriesList = CategoryLevel1.findAll()
        categoriesList.each{category ->
            HashMap value = new HashMap()
            value.put("categId",category.browseNodeId)
            value.put("type","category")
            value.put("baseCategory",category.categoryType)
            searchMainMap.put(category.nodeName,value)

        }

        categoriesList = CategoryLevel2.findAll()
        categoriesList.each{category ->
            HashMap value = new HashMap()
            value.put("categId",category.browseNodeId)
            value.put("type","category")
            value.put("baseCategory",category.categoryType)
            searchMainMap.put(category.nodeName,value)

        }

        categoriesList = CategoryLevel3.findAll()
        categoriesList.each{category ->
            HashMap value = new HashMap()
            value.put("categId",category.browseNodeId)
            value.put("type","category")
            value.put("baseCategory",category.categoryType)
            searchMainMap.put(category.nodeName,value)

        }

        categoriesList = CategoryLevel4.findAll()
        categoriesList.each{category ->
            HashMap value = new HashMap()
            value.put("categId",category.browseNodeId)
            value.put("type","category")
            value.put("baseCategory",category.categoryType)
            searchMainMap.put(category.nodeName,value)

        }

        //get books
        int printBooksCount = PrintBooksMst.count()
        int noOfPages = printBooksCount/1000
        int max=1000;
         for(int i=0;i<(noOfPages+1);i++) {
            List printBooks = PrintBooksMst.findAll([max: max, offset: i * max])
             printBooks.each { printBook ->
                HashMap value = new HashMap()
                value.put("bookId", printBook.id)
                value.put("type", "book")
                value.put("title", printBook.title)
                 value.put("baseCategory",printBook.baseCategory)
                searchMainMap.put(printBook.title, value)
                if (printBook.isbn != null) {
                    searchMainMap.put(printBook.isbn, value)

                }
            }
        }

        //get publishers

        List publishersList = ExtPublishers.findAll()

        publishersList.each {publisher ->
            HashMap value = new HashMap()
            value.put("pubId",publisher.id)
            value.put("type","publisher")
            searchMainMap.put(publisher.name,value)
        }

        servletContext.setAttribute("searchPrintMainMap",searchMainMap);
        redisService.("searchPrintMainMap") = null
    }

    def librarySearch(String batchId){
        HashMap searchMainMap = new HashMap()
        servletContext.setAttribute("searchLibraryMap_"+batchId,searchMainMap);
        String sql ="select bm.id,bm.title,bm.language,bm.isbn  from wsuser.books_batch_dtl bbd, wsuser.books_mst bm where\n" +
                "(bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate()) and   bbd.batch_id In (" + libraryBooksService.getAllBatchIds(batchId) + ") and bm.id=bbd.book_id order by id desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource)
        def results = sql2.rows(sql)
        results.each { book ->
            //add title
            HashMap value = new HashMap()
            value.put("bookId",""+book[0])
            value.put("language",book[2])
            searchMainMap.put(book[1],value)


            //add isbn
            if (book[3]!=null&&!"".equals(book[3])) {
                value = new HashMap()
                value.put("bookId", book[0])
                value.put("language", book[2])
                searchMainMap.put(book[3], value)
            }
        }
        servletContext.setAttribute("searchLibraryMap_"+batchId,searchMainMap);
    }
}
