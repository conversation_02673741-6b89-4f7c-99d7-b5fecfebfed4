package com.wonderslate.cache

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.sqlutil.SafeSql

class SecondDataProviderService {
    def redisService
    def grailsApplication

    def getAllChapterIdResId(bookId){

        String sql = "select rd.id,  cm.name, cm.id chapterId\n" +
                "from resource_dtl rd\n" +
                "inner join chapters_mst cm on rd.chapter_id = cm.id\n" +
                "where cm.book_id = "+bookId+"\n" +
                "and rd.sharing is null\n" +
                "and rd.res_type = 'Notes'\n" +
                "and rd.res_link like '%.pdf'\n" +
                "and rd.id = (\n" +
                "    select min(rd2.id)\n" +
                "    from resource_dtl rd2\n" +
                "    where rd2.chapter_id = rd.chapter_id\n" +
                "    and rd2.res_type = 'Notes'\n" +
                "    and rd2.res_link like '%.pdf'\n" +
                ")\n" +
                "order by cm.sort_order, rd.id asc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        List chapterDetails = results.collect { resource ->
            return [resId: resource.id, chapterName: (""+resource.name),
                    chapterId:resource.chapterId
            ]
        }
        Gson gson = new Gson();
        String element = gson.toJson(chapterDetails,new TypeToken<List>() {}.getType())
        redisService.("chapterId_resId_" +bookId) = element
    }

    def tellTheTime(){
        def result = "The current time is "+new Date()
        println "\n\n*** SecondDataProviderService.tellTheTime() called at "+new Date()+" ***\n\n"
        System.out.println("SecondDataProviderService.tellTheTime() called: ${result}")
        return result
    }
}
