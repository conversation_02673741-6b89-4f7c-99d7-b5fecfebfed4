package com.wonderslate.data

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.prepjoy.NewsUserDtl
import com.wonderslate.prepjoy.NewsUserLang
import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class NewsService {

    def springSecurityService
    def redisService
    def grailsApplication
    UtilService utilService
    DataProviderService dataProviderService

    def getNewsLanguages(){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "select language,language_display from news_lang_mst"

        def results = sql1.rows(sql);
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("newsLanguages") = element
    }

    def getNewsSource(){
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = "select id,name,rss_feed,language,news_type from news_source_mst"

        def results = sql1.rows(sql);
        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("newsSource") = element
    }

    def addUserNewsLangPreference(String siteId,String username,String languages){
        String [] langList = languages.split(",")

        List currentLangSelection = NewsUserLang.findAllByUsername(username)
        currentLangSelection.each{ userLang ->
            userLang.delete(flush: true)
        }

        for(int i=0;i<langList.length;i++){
            NewsUserLang newsUserLang = new NewsUserLang(username:username,language: langList[i],siteId:new Integer(siteId))
            newsUserLang.save(flush: true, failOnError: true)
        }

    }

    def addUserNewsSourcePreference(String siteId,String username,String newsSourceIds){
        String [] sourceList = newsSourceIds.split(",")
        List currentSourceSelection = NewsUserDtl.findAllByUsername(username)
        currentSourceSelection.each{ userSource ->
            userSource.delete(flush: true)
        }

        for(int i=0;i<sourceList.length;i++){
            NewsUserDtl newsUserDtl = new NewsUserDtl(username:username,newsSourceId: new Integer(sourceList[i]),siteId:new Integer(siteId))
            newsUserDtl.save(flush: true, failOnError: true)
        }

    }

}
