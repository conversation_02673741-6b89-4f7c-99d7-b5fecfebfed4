package com.wonderslate.data

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import com.ibookso.products.CategoryLevel1
import com.ibookso.products.CategoryLevel2
import com.ibookso.products.CategoryLevel3
import com.ibookso.products.CategoryLevel4
import com.ibookso.products.ExtPublishers
import com.ibookso.products.PrintBooksMst
import com.wonderslate.DataNotificationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.publish.Blogs
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.Publishers
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.publish.SyllabusSubject
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.time.Duration
import java.time.Instant
import java.util.stream.Collectors
import java.util.stream.Stream

@Transactional
class PrintBooksService {

    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    def servletContext
    def grailsApplication
    def redisService
    PrintBooksService printBooksService

    def getSample(keyword){
        def res;
        def baseUrl = new URL('https://affiliate-api.flipkart.net/affiliate/1.0/search.json?query='+keyword+'&resultCount=1')

        HttpURLConnection connection = (HttpURLConnection) baseUrl.openConnection()
        connection.addRequestProperty("Content-type", "application/x-www-form-urlencoded")
        connection.addRequestProperty("Fk-Affiliate-Id", "wonderslate")
        connection.addRequestProperty("Fk-Affiliate-Token", "3c706e064a724c49bc4c9482de6dc99f")
        connection.with {
            doOutput = true
            requestMethod = 'GET'
            res = content.text

           // res = res.substring(17, res.length() - 2) // to get the data string



        }
        def booksReceived = new JsonSlurper().parseText(addDoubleQuotes(res))
        def books = booksReceived.products
        println("the size of products="+books.size())
        println("title alva="+books[0].productBaseInfoV1.title)
        println("url="+books[0].productBaseInfoV1.productUrl)
        return res
    }

    def getPrintedBooksDetailAndSave(keyword,level,syllabus,grade,subject){
        Instant start = Instant.now();
            def res;
            def baseUrl = new URL('https://ws-in.amazon-adsystem.com/widgets/q?Operation=GetResults&' +
                    'Keywords=' + URLEncoder.encode(keyword, "UTF-8") + // keywords parameter should contain the search string in url encoded form
                    '&SearchIndex=Books&multipageStart=0&InstanceId=0&multipageCount=10&TemplateId=MobileSearchResults&ServiceVersion=20070822&MarketPlace=IN')

            HttpURLConnection connection = (HttpURLConnection) baseUrl.openConnection()
            connection.addRequestProperty("Content-type", "application/x-www-form-urlencoded")
            connection.with {
                doOutput = true
                requestMethod = 'GET'
                res = content.text

                res = res.substring(17, res.length() - 2) // to get the data string



            }
        Instant end = Instant.now();
        Duration timeElapsed = Duration.between(start, end);
        System.out.println("Time taken for getting results: "+ timeElapsed.toString());
        start = Instant.now();
            //   def json = JsonOutput.toJson(res)
            res = removeCommaInTitle(res);
        end = Instant.now();
        timeElapsed = Duration.between(start, end);
        System.out.println("Time taken for removing commas: "+ timeElapsed.toString());


        try {
            start = Instant.now();
            def booksReceived = new JsonSlurper().parseText(addDoubleQuotes(res))
            end = Instant.now();
            timeElapsed = Duration.between(start, end);
            System.out.println("Time taken for json thingy: "+ timeElapsed.toString());
            def books = booksReceived.results
            boolean savedBook
            start = Instant.now();
            for (int i = 0; i < books.size(); i++) {

                String title = books[i].Title;
                String coverImage = books[i].ImageUrl
           //     coverImage = coverImage.substring(0, coverImage.indexOf("._")) + ".jpg";

                def bookLink = books[i].DetailPageURL + "?tag=wonderslate-21&linkCode=w13&linkId=&ref_=assoc_res_sw_zz_dka_cra_t0_result_1&ref-refURL=https%3A%2F%2Faffiliate-program.amazon.in%2Fhome%2Fwidgets%2F8002";
                def asin = books[i].ASIN;
                def rating = books[i].Rating;
                def totalReviews = books[i].TotalReviews;
                savedBook = addPrintBooks(asin, title, coverImage, bookLink, rating, totalReviews, level, syllabus, grade, subject)


            }
            end = Instant.now();
            timeElapsed = Duration.between(start, end);
            System.out.println("Time taken for saving books: "+ timeElapsed.toString());
            }catch(Exception e){
             println("Exception while parsing amazon search results "+e.toString())
             }


        return res
    }
    @Transactional
    def getAndAddAllPrintBooks(){
        Instant start = Instant.now();
        // all school books
        List allSyllabus = LevelSyllabus.findAllByLevel('School')
        List allSubjects = SyllabusSubject.findAllBySyllabus("School")
        String keyword
        boolean bookSaved
        allSyllabus.each{syllabus->

            allSubjects.each{ subject ->
                if(!"All subjects".equals(subject.subject)){
                    for(int i=1;i<13;i++){
                        keyword = syllabus.syllabus+" class "+i+" "+subject.subject

                      getPrintedBooksDetailAndSave(keyword,"School",syllabus.syllabus,""+i,subject.subject)
                    }

                }

            }
        }

        //all college books
        allSyllabus = LevelSyllabus.findAllByLevel('College')

        allSyllabus.each { syllabus ->

            allSubjects = SyllabusSubject.findAllBySyllabus(syllabus.syllabus)
            allSubjects.each { subject ->
                if (subject.subject.equals("All") || subject.subject.equals("All subjects")) {
                    keyword = syllabus.syllabus
                }
                else {
                    keyword = syllabus.syllabus+" "+subject.subject
                }

                 getPrintedBooksDetailAndSave(keyword,"College",syllabus.syllabus,"All",subject.subject)
            }
            // if no syllabus added to this degree course
            if(allSubjects.size()==0){
                keyword = syllabus.syllabus
                getPrintedBooksDetailAndSave(keyword,"College",syllabus.syllabus,"All","All subjects")
            }
        }

        //all test preparation books
        allSyllabus = LevelSyllabus.findAllByLevel('Competitive Exams')

        allSyllabus.each { syllabus ->
            List allExams = SyllabusGradeDtl.findAllBySyllabus(syllabus.syllabus)
            allSubjects = SyllabusSubject.findAllBySyllabus(syllabus.syllabus)
            allExams.each {exam ->
                allSubjects.each { subject ->
                    if (subject.subject.equals("All") || subject.subject.equals("All subjects")) {
                        keyword = exam.grade
                    }
                    else {
                        keyword = exam.grade + " " + subject.subject
                    }
                    getPrintedBooksDetailAndSave(keyword,"Competitive Exams",syllabus.syllabus,exam.grade,subject.subject)
                }
            }
            // if no syllabus added to this exam course
            if(allSubjects.size()==0){
                keyword = syllabus.syllabus
                getPrintedBooksDetailAndSave(keyword,"Competitive Exams",syllabus.syllabus,exam.grade,"All subjects")
            }
        }

        Instant end = Instant.now();
        Duration timeElapsed = Duration.between(start, end);
        System.out.println("Time taken for adding amazon books: "+ timeElapsed.toString());
        redisService.flushDB()
    }

    def removeCommaInTitle(String inputString){

        boolean hasTitle=true
        int startingIndex=0;
        String title
        String newTitle

        while(hasTitle){

            startingIndex = inputString.indexOf("Title : \"",startingIndex)
            title = inputString.substring(startingIndex+9,inputString.indexOf("\"",(startingIndex+12)))
            newTitle = title.replaceAll(","," ").replaceAll(":"," ").replace("["," ").replace("]"," ")
            inputString = inputString.replace(title,newTitle)
            startingIndex = startingIndex +title.length()+2
            if(inputString.indexOf("Title : \"",startingIndex)==-1) hasTitle=false


        }
        return inputString;
    }

    def addDoubleQuotes(inputString){
        return inputString.replace("results","\"results\"").replace(" , "," , \"").replace(" : \"","\" : \"").replace("\"{","{").replace("ASIN\"","\"ASIN\"").replace("NumRecords\"","\"NumRecords\"").replace("CorrectedQuery\"","\"CorrectedQuery\"").replace("MarketPlace","\"MarketPlace\"").replace("InstanceId","\"InstanceId\"");
    }

    def addPrintBooks(asin,title,coverImage,bookLink,rating,totalReviews,level,syllabus,grade,subject){
        BooksMst booksMst = BooksMst.findByAsin(asin)
        if(booksMst==null) {
            booksMst = new BooksMst(title: title, description: title,
                    coverImage: coverImage, buylink1: bookLink, status: 'published', asin: asin, datePublished: new Date(),
                    siteId: new Integer(1),bookType: "print",rating:rating,totalReviews:totalReviews)

            booksMst.save(failOnError: true, flush: true)

        }else{
           //do nothing
        }
        BooksTagDtl booksTagDtl = BooksTagDtl.findByBookIdAndLevelAndSyllabusAndGradeAndSubject(booksMst.id,level,syllabus,grade,subject)
        if(booksTagDtl==null) {
            booksTagDtl = new BooksTagDtl(bookId: booksMst.id, level: level, syllabus: syllabus, grade: grade, subject: subject)
            booksTagDtl.save(failOnError: true, flush: true)
            booksTagDtl = new BooksTagDtl(bookId: booksMst.id, level: level, syllabus: syllabus, grade: grade, subject: subject)
            booksTagDtl.wsshop.save(failOnError: true, flush: true)
        }

        //without written the control was not waiting, so sometimes duplicate books were added. So added return statement to complete the save before going to next one.

       return true
    }

    //PN101

    def getPrintBooksMst(Long pBookId){
        PrintBooksMst pBook  = redisService.memoizeDomainObject(PrintBooksMst, "pBooksMst_"+pBookId) {
            return PrintBooksMst.findById(pBookId)
        }
        return pBook
    }

    def getEbookId(String title, String isbn){
        Integer siteId = new Integer(1)
        String siteIdList=siteId.toString();
        HashMap searchMainMap = new HashMap()
        HashMap bookDetails = null
            if(redisService.("siteIdList_"+siteId)==null) {
                dataProviderService.getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)

        if (servletContext.getAttribute("searchMainMap_" + siteId) == null)
            dataProviderService.updateSearchMap(siteId, siteIdList);
        searchMainMap = servletContext.getAttribute("searchMainMap_"+siteId);


        if(searchMainMap.size()>0){
            //check for title first
            if(searchMainMap.get(title)!=null){
                //found the eBook
                bookDetails = searchMainMap.get(title)
            }else{
                //check of isbn
                if(isbn!=null){
                    String [] isbns  = isbn.split(",")
                    for(int i=0;i<isbns.length;i++){
                        if(searchMainMap.get(isbns[i])!=null){
                            //found the eBook
                            bookDetails = searchMainMap.get(isbns[i])
                            break
                        }
                    }
                }
            }
        }
        if(bookDetails!=null){
          return bookDetails.get("bookId")
        }
        else{
         return -1
        }
    }

    def getExtPublishers(String publisherId){
         ExtPublishers extPublishers  = redisService.memoizeDomainObject(ExtPublishers, "pBooksMst_"+publisherId) {
            return ExtPublishers.findById(publisherId)
        }
        return extPublishers
    }

    def getPrintRelatedBooks(String categoryId){

        def sql = "select * from print_books_mst where category_id='"+categoryId+"' order by id desc limit 10";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List printBooks = results.collect { printBook ->
            return [id: printBook.id, publisher: printBook.publisher, coverImage: printBook.cover_image!=null?printBook.cover_image:"",
                    status: printBook.status!=null?printBook.status:"",isbn: printBook.isbn!=null?printBook.isbn:"",asin: printBook.asin!=null?printBook.asin:"",
            categoryId:printBook.category_id,title:printBook.title]
        }
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        String element = gson.toJson(printBooks,new TypeToken<List>() {}.getType())
        redisService.("relatedPrintBooks_"+categoryId) = element
    }



    def getAllLeafCategory(String categoryId) {
        String categoryIds = "," + categoryId
        String categoryMap
        //step1 find where the category belongs to. Start from back
        CategoryLevel4 categoryLevel4 = CategoryLevel4.findByBrowseNodeId(categoryId)
        if (categoryLevel4 == null) {
            CategoryLevel3 categoryLevel3 = CategoryLevel3.findByBrowseNodeId(categoryId)
            if (categoryLevel3 != null) {
                if (!"true".equals(categoryLevel3.lastChild)) {
                    //now check for child nodes
                    List categ4 = CategoryLevel4.findAllByParentId(categoryId)
                    if (categ4.size() > 0) {
                        categ4.each { categ ->
                            categoryIds += "," + categ.browseNodeId
                        }
                    }
                }

            } else {
                CategoryLevel2 categoryLevel2 = CategoryLevel2.findByBrowseNodeId(categoryId)
                if (categoryLevel2 != null) {
                    if (!"true".equals(categoryLevel2.lastChild)) {
                        List categ3 = CategoryLevel3.findAllByParentId(categoryId)
                        categ3.each { categ ->
                            categoryIds += "," + categ.browseNodeId
                            if (!"true".equals(categ.lastChild)) {
                                List categ4 = CategoryLevel4.findAllByParentId(categ.browseNodeId)
                                if (categ4.size() > 0) {
                                    categ4.each { categ1 ->
                                        categoryIds += "," + categ1.browseNodeId
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // this is definitely category 1 item.. so get all child nodes
                    List categs2 = CategoryLevel2.findAllByParentId(categoryId)
                    categs2.each { categ2 ->
                        categoryIds += "," + categ2.browseNodeId
                        if (!"true".equals(categ2.lastChild)) {
                            List categ3 = CategoryLevel3.findAllByParentId(categ2.browseNodeId)
                            categ3.each { categ ->
                                categoryIds += "," + categ.browseNodeId
                                if (!"true".equals(categ.lastChild)) {
                                    List categ4 = CategoryLevel4.findAllByParentId(categ.browseNodeId)
                                    if (categ4.size() > 0) {
                                        categ4.each { categ1 ->
                                            categoryIds += "," + categ1.browseNodeId
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        }else{
            //this is category 4
        }
        categoryIds = categoryIds.substring(1)
        redisService.("printBookCategories_"+categoryId) = categoryIds
        return categoryIds
    }

    def getPrintBooks(String categoryId,String pageNo,String publisherId,String baseCategory){
        int noOfBooksPerPage = 20
        int offset = 20*Integer.parseInt(pageNo)
        String addlCondition =""
        if(publisherId!=null) addlCondition =" where publisher='"+publisherId+"'"
        else addlCondition= " where base_category='"+baseCategory+"' "
        String sql = "select id, title,publisher,cover_image,status,isbn,asin,category_id from print_books_mst " +addlCondition+
                " order by id desc limit "+offset+","+noOfBooksPerPage
        if(!"all".equals(categoryId)) {
            if(publisherId!=null) addlCondition =" and publisher='"+publisherId+"'"
            else addlCondition= " and base_category='"+baseCategory+"' "
            if (redisService.("printBookCategories_" + categoryId) == null) getAllLeafCategory(categoryId)
            String categoryIds = redisService.("printBookCategories_" + categoryId)
            if(publisherId!=null) addlCondition =" and publisher='"+publisherId+"'"
            sql = "select id, title,publisher,cover_image,status,isbn,asin,category_id from print_books_mst where category_id in (" + toSingleQuotes(categoryIds) + ")  " +addlCondition+
                    " order by id desc limit " + offset + "," + noOfBooksPerPage
        }
        println(sql)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def  results = sql1.rows(sql)
        String publisherName=""
        List books = results.collect { book ->
            publisherName=""
            if(book.publisher!=null&&!"".equals(book.publisher)) {
                ExtPublishers extPublishers = printBooksService.getExtPublishers(book.publisher)
                publisherName=extPublishers.name
            }

            return [id: book.id, title: book.title, coverImage:""+book.cover_image,publisher:""+publisherName,status:""+book.status,isbn:""+book.isbn,
                    asin:""+book.asin,publisherId:""+book.publisher]
        }

        books = books.unique()
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        String element = gson.toJson(books.unique(),new TypeToken<List>() {}.getType())
        if(publisherId!=null)
            redisService.("printBooksList_"+baseCategory.replace(' ','')+"_"+categoryId+"_"+publisherId+"_"+pageNo) = element
        else
            redisService.("printBooksList_"+baseCategory.replace(' ','')+"_"+categoryId+"_"+pageNo) = element
    }

    def createCategoryMap(String baseCategory){

        //structure = level_lastChild_name_parentId

        String firstLevelCategories=""
        List categ1 = CategoryLevel1.findAllByCategoryType(baseCategory,[sort: "nodeName", order: "asc"])
        categ1.each {categ ->
            redisService.("categMap_"+categ.browseNodeId)="1_false_"+categ.nodeName.replaceAll(" ","-")
            firstLevelCategories +=categ.browseNodeId+"_"+categ.categoryType+","
         }
        List categ2 = CategoryLevel2.findAllByCategoryType(baseCategory,[sort: "nodeName", order: "asc"])
        categ2.each {categ->
            redisService.("categMap_"+categ.browseNodeId)="2_"+("true".equals(categ.lastChild)?"true":"false")+"_"+categ.nodeName.replaceAll(" ","-")+"_"+categ.parentId
        }
        List categ3 = CategoryLevel3.findAllByCategoryType(baseCategory,[sort: "nodeName", order: "asc"])
        categ3.each {categ->
            redisService.("categMap_"+categ.browseNodeId)="3_"+("true".equals(categ.lastChild)?"true":"false")+"_"+categ.nodeName.replaceAll(" ","-")+"_"+categ.parentId
        }
        List categ4 = CategoryLevel4.findAllByCategoryType(baseCategory,[sort: "nodeName", order: "asc"])
        categ4.each {categ->
            redisService.("categMap_"+categ.browseNodeId)="4_true_"+categ.nodeName.replaceAll(" ","-")+"_"+categ.parentId
        }

        redisService.("firstLevelCategories_"+baseCategory.replace(' ',''))=firstLevelCategories.substring(0,firstLevelCategories.length()-1)


    }



    String toSingleQuotes(String source){
        String[] parts = source.split(",")
        return Stream.of(parts).collect(Collectors.joining("','", "'", "'"));
    }


   HashMap getHashMap(keyName){
       HashMap tester = redisService.memoizeHash(keyName) {
           println("first time enters the thingy")
           HashMap inside = [anand:"string"]

           return [foo: inside]

       }
       return tester
   }

    def getCategoryNames(String id){
        if(redisService.("categName_"+id)==null) {
            String catName = ''
            List levelFour = CategoryLevel4.findAllByBrowseNodeId(id)
            if (levelFour.size() > 0) {
                try {
                    catName = levelFour[0] != null ? levelFour[0].nodeName.replace(',', '+') : ''
                    if (levelFour[0] != null) {
                        List levelThree = CategoryLevel3.findAllByBrowseNodeId(levelFour[0].parentId)
                        catName = levelThree[0] != null ? levelThree[0].nodeName.replace(',', ' +') + ' > ' + catName : ''

                        if (levelThree[0] != null) {
                            List levelTwo = CategoryLevel2.findAllByBrowseNodeId(levelThree[0].parentId)
                            catName = levelTwo[0] != null ? levelTwo[0].nodeName.replace(',', ' +') + ' > ' + catName : ''

                            if (levelTwo[0] != null) {
                                List levelOne = CategoryLevel1.findAllByBrowseNodeId(levelTwo[0].parentId)
                                catName = levelOne[0] != null ? levelOne[0].nodeName.replace(',', ' +') + ' > ' + catName : ''
                            }
                        }
                    } else {
                        List levelThree = CategoryLevel3.findAllByBrowseNodeId(id)
                        if (levelThree[0] != null) {
                            catName = levelThree[0].nodeName.replace(',', ' +')

                            if (levelThree[0] != null) {
                                List levelTwo = CategoryLevel2.findAllByBrowseNodeId(levelThree[0].parentId)
                                catName = levelTwo[0] != null ? levelTwo[0].nodeName.replace(',', ' +') + ' > ' + catName : ''
                            }
                        }
                    }
                }
                catch (Exception e) {
                    println("Exception happened :" + e)
                    println(levelFour, levelFour[0])
                }

            } else if (levelFour.size() == 0) {
                List levelThree = CategoryLevel3.findAllByBrowseNodeId(id)
                if (levelThree.size() > 0) {
                    catName = levelThree[0] != null ? levelThree[0].nodeName.replace(',', ' +') : ''

                    List levelTwo = CategoryLevel2.findAllByBrowseNodeId(levelThree[0].parentId)
                    catName = levelTwo[0] != null ? levelTwo[0].nodeName.replace(',', ' +') + ' > ' + catName : ''

                    List levelOne = CategoryLevel1.findAllByBrowseNodeId(levelTwo[0].parentId)
                    catName = levelOne[0] != null ? levelOne[0].nodeName.replace(',', ' +') + ' > ' + catName : ''

                } else if (levelThree.size() == 0) {
                    List levelTwo = CategoryLevel2.findAllByBrowseNodeId(id)
                    if (levelTwo.size() > 0) {
                        catName = levelTwo[0] != null ? levelTwo[0].nodeName.replace(',', '+') : ''

                        List levelOne = CategoryLevel1.findAllByBrowseNodeId(levelTwo[0].parentId)
                        catName = levelOne[0] != null ? levelOne[0].nodeName.replace(',', ' +') + ' > ' + catName : ''

                    } else if (levelTwo.size() == 0) {
                        List levelOne = CategoryLevel1.findAllByBrowseNodeId(id)
                        if (levelOne.size() > 0) {
                            catName = levelOne[0] != null ? levelOne[0].nodeName.replace(',', '+') : ''
                        }
                    }
                }
            }
            redisService.("categName_"+id) = catName
        }

        return redisService.("categName_"+id)
    }

    def getEbookPrice(isbns){
        String isbnVal=""
        String ebookPrice
        if (isbns!=null){
            def isbnList = isbns.split(',');
            for (int i=0;i<isbnList.size();i++){
                if (i != isbnList.size()-1){
                    isbnVal += "'"+isbnList[i]+"'".replace(' ','') +','
                }else{
                    isbnVal += "'"+isbnList[i]+"'".replace(' ','')
                }
            }
            def sql = "SELECT * FROM wsshop.books_mst where isbn in ("+isbnVal+")"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            ebookPrice = results.price[0]
        }else{
            ebookPrice = ""
        }
        return ebookPrice
    }

    def getPublisherBaseCategory(String pubId){
        if(redisService.("extPublisher_"+pubId)==null) {
            PrintBooksMst printBooksMst = PrintBooksMst.findByPublisherAndBaseCategoryIsNotNull(pubId)
            redisService.("extPublisher_"+pubId) = printBooksMst.baseCategory
        }

        return redisService.("extPublisher_"+pubId)
    }

    def getPublisherLeafCategories(String pubId){

        String sql ="select browse_node_id,node_name,'4' category_level from category_level4 a, (select distinct category_id from print_books_mst where publisher="+pubId+") b where a.browse_node_id=b.category_id\n" +
                "union\n" +
                "select browse_node_id,node_name,'3' category_level from category_level3 a, (select distinct category_id from print_books_mst where publisher="+pubId+") b where a.browse_node_id=b.category_id\n" +
                "union\n" +
                "select browse_node_id,node_name,'2' category_level from category_level2 a, (select distinct category_id from print_books_mst where publisher="+pubId+") b where a.browse_node_id=b.category_id"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String categories=""
        HashMap tempMap = new HashMap()
        results.each{ categ ->
            if(tempMap.get(categ.browse_node_id)==null) {
                tempMap.put(categ.browse_node_id,categ.browse_node_id)
                categories += categ.category_level + "_" + categ.node_name.replaceAll(" ", "-").replaceAll(",", "-")  + "_" + categ.browse_node_id + ","
            }
        }
        if(categories.length()>0) categories = categories.substring(0,categories.length()-1)
        redisService.("publisherLeafCategories_"+pubId)=categories
    }

    String getBaseCategoryUrl(String baseCategory){
        String baseCategoryUrl="printbooks"
        if("Books".equals(baseCategory)) baseCategoryUrl="printbooks"
        else if("Electronics".equals(baseCategory)) baseCategoryUrl="electronics"
        else if("Video Games".equals(baseCategory)) baseCategoryUrl="video-games"
        else if("Office Supplies".equals(baseCategory)) baseCategoryUrl="office-supplies"
        else if("Sports and Fitness".equals(baseCategory)) baseCategoryUrl="sports-and-fitness"
        else if("Bags Wallets and Luggage".equals(baseCategory)) baseCategoryUrl="bags-wallets-luggage"
        else if("Toys and Games".equals(baseCategory)) baseCategoryUrl="toys-and-games"
        else if("Shoes".equals(baseCategory)) baseCategoryUrl="shoes"
        return baseCategoryUrl
    }

    def createFullCategoryMap(){
         List categ1 = CategoryLevel1.findAll([sort: "nodeName", order: "asc"])
        categ1.each {categ ->
            redisService.("categMap_"+categ.browseNodeId)="1_false_"+categ.nodeName.replaceAll(" ","-")
        }
        List categ2 = CategoryLevel2.findAll([sort: "nodeName", order: "asc"])
        categ2.each {categ->
            redisService.("categMap_"+categ.browseNodeId)="2_"+("true".equals(categ.lastChild)?"true":"false")+"_"+categ.nodeName.replaceAll(" ","-")+"_"+categ.parentId
        }
        List categ3 = CategoryLevel3.findAll([sort: "nodeName", order: "asc"])
        categ3.each {categ->
            redisService.("categMap_"+categ.browseNodeId)="3_"+("true".equals(categ.lastChild)?"true":"false")+"_"+categ.nodeName.replaceAll(" ","-")+"_"+categ.parentId
        }
        List categ4 = CategoryLevel4.findAll([sort: "nodeName", order: "asc"])
        categ4.each {categ->
            redisService.("categMap_"+categ.browseNodeId)="4_true_"+categ.nodeName.replaceAll(" ","-")+"_"+categ.parentId
        }

        redisService.("allLevelCategoriesSet")="true"

    }

    def getCategoryInfo(String browseNodeId){
        String categDescription=null
        def categoryLevelTable=null
        String blogLink
        try {

            categoryLevelTable = CategoryLevel2.findByBrowseNodeId(browseNodeId)
            if (categoryLevelTable==null||(categoryLevelTable.syllabusId==null&&categoryLevelTable.gradeId==null&&categoryLevelTable.subjectId==null)) {
                categoryLevelTable = CategoryLevel3.findByBrowseNodeId(browseNodeId)
            }
            if (categoryLevelTable==null||(categoryLevelTable.syllabusId==null&&categoryLevelTable.gradeId==null&&categoryLevelTable.subjectId==null)) {
                categoryLevelTable = CategoryLevel4.findByBrowseNodeId(browseNodeId)
            }

            if (categoryLevelTable != null) {
                Blogs blogs = null
                if (categoryLevelTable.syllabusId != null) {
                    LevelSyllabus levelSyllabus = LevelSyllabus.findByIdAndSiteId(categoryLevelTable.syllabusId, new Integer(1))
                    blogs = Blogs.findBySyllabusAndColName(levelSyllabus.syllabus, "introduction")
                    if (blogs != null) {
                        categDescription = blogs.colValue
                        blogLink = "/${("best-books-for-" + levelSyllabus.syllabus).replaceAll(' ', '-').toLowerCase()}/information/s/"+levelSyllabus.id
                    }
                } else if (categoryLevelTable.gradeId != null) {
                    SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findById(categoryLevelTable.gradeId)
                    blogs = Blogs.findByGradeAndColName(syllabusGradeDtl.grade, "introduction")
                    if (blogs != null) {
                        categDescription = blogs.colValue
                        blogLink = "/${("best-books-for-" + syllabusGradeDtl.syllabus).replaceAll(' ', '-').toLowerCase()}-${("" + syllabusGradeDtl.grade).replaceAll(' ', '-').toLowerCase()}/information/g/"+syllabusGradeDtl.id

                    }
                } else if (categoryLevelTable.subjectId != null) {
                    blogs = Blogs.findBySubjectIdAndColName(categoryLevelTable.subjectId, "introduction")
                    if (blogs != null) {
                        categDescription = blogs.colValue
                        blogLink = "/index"
                    }
                }
                if(blogs!=null) categDescription +="~"+blogLink+"~"+categoryLevelTable.nodeName

            }
        }catch (Exception e){
            println("Exception in get category description - getCategoryInfo")
        }
         return categDescription
    }


}
