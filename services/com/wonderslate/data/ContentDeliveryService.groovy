package com.wonderslate.data

import grails.transaction.Transactional

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import software.amazon.awssdk.services.cloudfront.CloudFrontUtilities;
import software.amazon.awssdk.services.cloudfront.model.CannedSignerRequest;
import software.amazon.awssdk.services.cloudfront.url.SignedUrl;


@Transactional
class ContentDeliveryService {
    String keyPairId = "K1K3820LSJ1OXC" // CloudFront key pair ID
    String privateKeyPath = "supload/pk-APKA2OPFHBCSMBP7Q7RL.pem" // Path to your private key file

   def generateSignedURL(String resourceUrl) {
        try {
            CloudFrontUtilities cloudFrontUtilities = CloudFrontUtilities.create();
            Instant expirationDate = Instant.now().plus(2, ChronoUnit.MINUTES);
            CannedSignerRequest cannedRequest = CannedSignerRequest.builder()
                    .resourceUrl(resourceUrl)
                    .privateKey(new java.io.File(privateKeyPath).toPath())
                    .keyPairId(keyPairId)
                    .expirationDate(expirationDate)
                    .build();
            SignedUrl signedUrl = cloudFrontUtilities.getSignedUrlWithCannedPolicy(cannedRequest);
            System.out.println(signedUrl.url());

            return signedUrl.url()
        } catch (Exception e) {
            log.error("Error generating CloudFront signed cookies: ${e.message}", e)
            throw e
        }
    }
}
