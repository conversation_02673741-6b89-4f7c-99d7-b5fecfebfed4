package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import grails.transaction.Transactional
import jxl.Cell
import jxl.Sheet
import org.apache.commons.io.FileUtils
import org.apache.commons.lang.StringUtils

import java.nio.file.Path

@Transactional
class QuizExtractorService {
    def grailsApplication
    DataProviderService dataProviderService
    def checkFillintheblanks(Sheet sheet) {
        String msgs=""
        Cell[] cells
        int ansCnt

        for(int row = 2; row < sheet.getRows(); row++) {
            ansCnt = 0
            cells = sheet.getRow(row)

            if(cells[0].getContents()!=null && cells[0].getContents().length()!=0) {
                for(int j =0;j<3;j++) {
                    if(cells.length>(j+1))  {
                        ansCnt += (cells[j+1].getContents()!=null && cells[j+1].getContents().length()!=0)?1:0;
                    }
                };
                
                if(StringUtils.countMatches(cells[0].getContents(), "__")!=ansCnt) {
                    msgs += "<br><font color=red>Halting the process as number of blanks ("+StringUtils.countMatches(cells[0].getContents(), "__")+") in the question not equal to answers provided ("+ansCnt+") on row "+(row+1)+"</font>"; 
                    break;                    
                }
                
                //Check to see if atleast one answer is present
                if(cells[1].getContents()==null || cells[1].getContents().length()==0){
                    msgs += "<br><font color=red>Halting the process as no answer was found on row "+(row+1)+"</font>";
                    break;
                }
            } else {
               // msgs += "<br><font color=red>Halting the process as no question was detected on row "+(row+1)+"</font>";
               // break
            }
        }
        
        return msgs;
    }    

    def extractFillintheblanks(Sheet sheet, Long quizId,String resourceType) {        
        Cell[] cells

        for(int row = 2; row < sheet.getRows(); row++) {
            cells = sheet.getRow(row)

            if(cells[0].getContents()!=null && cells[0].getContents().length()!=0) {                            
                //Check to see if atleast one answer is present
                if(cells[1].getContents()!=null && cells[1].getContents().length()!=0){
                    ObjectiveMst om =   new ObjectiveMst(quizId:quizId, quizType : resourceType,
                            question:cells[0].getContents(), answer1:cells[1].getContents(),
                            answer2:(cells[2].getContents()==null?'':cells[2].getContents()), 
                            answer3 : (cells[3].getContents()==null?'':cells[3].getContents()))
                    om.save(failOnError: true )
                } 
            }
        }       
    }

    def checkMCQ(Sheet sheet) {
        String msgs=""       
        Cell[] cells

        for (int row = 1; row < sheet.getRows(); row++) {
            cells = sheet.getRow(row)

            if (cells[0].getContents()!=null && cells[0].getContents().length()!=0) {
                //Check to see if atleast one answer is present               
                if(!"Yes".equals(cells[2].getContents()) && !"Yes".equals(cells[4].getContents()) && 
                        !"Yes".equals(cells[6].getContents()) && !"Yes".equals(cells[8].getContents())){                
                    msgs += "<br><font color=red>Halting the process as no correct answer was found on row "+(row+1)+"</font>";
                    break;
                }
                
                if(("".equals(cells[1].getContents())) || ("".equals(cells[3].getContents())) || 
                        ("".equals(cells[5].getContents())) || ("".equals(cells[7].getContents()))){                
                    msgs += "<br><font color=red>Halting the process as one or more options was not found on row "+(row+1)+"</font>";
                    break;
                }                
            } else {
               // msgs += "<br><font color=red>Halting the process as no question was detected on row "+(row+1)+"</font>";
               // break
            }
        }
        
        return msgs;        
    }    
    
    def extractMCQ(Sheet sheet, Long quizId,String resourceType,String topicId,String username) {
        Cell[] cells

        for (int row = 1; row < sheet.getRows(); row++) {
            cells = sheet.getRow(row)



            if (cells[0].getContents() != null && cells[0].getContents().length() != 0) {
                //Check to see if atleast one answer is present
                if(("Yes".equals(cells[2].getContents()))||("Yes".equals(cells[4].getContents()))||("Yes".equals(cells[6].getContents()))||("Yes".equals(cells[8].getContents()))){
                    Integer newTopicId=null;
                    //to get the topic Id of the subtopic
                    if(cells.length>13&&!"".equals(cells[13].getContents())){
                        TopicMst topicMst = TopicMst.findById(new Integer(topicId));
                        //find out if there is topic id associated with the topic given in the spread sheet
                        String topicName = cells[13].getContents();
                        String sql = "select id from TopicMst where lower(topicName)='"+topicName.toLowerCase()+"' and syllabus='"+topicMst.syllabus+"' and grade='"+topicMst.grade+"' and subject='"+topicMst.subject+"' and syllabusType='"+topicMst.syllabusType+"'";
                        List results = TopicMst.executeQuery(sql)
                        if(results.size()==0){
                            topicMst =  new TopicMst(topicName: topicName, syllabus: topicMst.syllabus, grade: topicMst.grade, subject: topicMst.subject, createdBy: username, syllabusType: topicMst.syllabusType, country:topicMst.country);
                            topicMst.save(failOnError: true)
                            newTopicId = (Integer)topicMst.id;
                        }else{

                            newTopicId = new Integer(""+results[0])
                        }
                    }

                    ObjectiveMst om =   new ObjectiveMst( quizId : quizId ,quizType : resourceType ,question : cells[0].getContents(),
                            option1 : cells[1].getContents(), option2: cells[3].getContents(),option3 : cells[5].getContents(),  option4 :cells[7].getContents() ,
                            answer1 : cells[2].getContents() ,answer2: cells[4].getContents(),answer3 : cells[6].getContents(), answer4 : (cells.length>8) ?cells[8].getContents():'',
                            answerDescription : (cells.length>9) ?cells[9].getContents():'', hint : (cells.length>10) ?cells[10].getContents():'', examYear : (cells.length>11) ?cells[11].getContents():'', difficultylevel : (cells.length>12) ?cells[12].getContents():'',
                            topicId : (cells.length>13) ?newTopicId:null)
                    om.save(failOnError: true )
                }
            }
        }
    }
    
    def checkOthers(Sheet sheet) {
        String msgs=""        
        Cell[] cells

        for (int row = 1; row < sheet.getRows(); row++) {
            cells = sheet.getRow(row)

            if(cells[0].getContents()!=null && cells[0].getContents().length()!=0) {
                //Check to see if atleast one answer is present
                if(cells[1].getContents()==null || cells[1].getContents().length()==0){
                    msgs += "<br><font color=red>Halting the process as no answer was found on row "+(row+1)+"</font>";
                    break;
                } else if(!((String)cells[1].getContents()).toLowerCase().equals("true") && !((String)cells[1].getContents()).toLowerCase().equals("false")) {
                    msgs += "<br><font color=red>Halting the process as the answer not <u>true</u> or <u>false</u> on row "+(row+1)+"</font>"; 
                    break;                    
                }
            } else {
             //   msgs += "<br><font color=red>Halting the process as no question was detected on row "+(row+1)+"</font>";
               // break
            }
        }
        
        return msgs;        
    }    

    def extractOthers(Sheet sheet, Long quizId,String resourceType) {
        Cell[] cells

        for (int row = 1; row < sheet.getRows(); row++) {
            cells = sheet.getRow(row)

            if (cells[0].getContents() != null && cells[0].getContents().length() != 0) {
                //Check to see if atleast one answer is present
                if (cells[1].getContents() != null && cells[1].getContents().length() != 0) {
                    ObjectiveMst om = new ObjectiveMst(quizId: quizId, quizType: resourceType, question: cells[0].getContents(),
                            answer1: cells[1].getContents())
                    om.save(failOnError: true)
                }
            }
        }
    }

    def extractImage(String text,String bookId,String chapterId,String resId,String ignoreFormula){
        boolean hasImage=true
        int urlStartingIndex
        int urlEndingIndex
        String urlString
        String firstPart,lastPart
        String queryPart
        String uploadParentDir="supload"

        if(bookId!=null&&!"".equals(bookId)){
            BooksMst booksMst = dataProviderService.getBooksMst(new Integer(bookId))
            if(!"Yes".equals(booksMst.newStorage)) uploadParentDir = "upload"
        }

        def savePath = grailsApplication.config.grails.basedir.path+"/"+uploadParentDir+"/books/"+bookId+"/chapters/"+chapterId+"/"+resId+"/extract/OEBPS/Images/"
        def dbSavePath =uploadParentDir+"/books/"+bookId+"/chapters/"+chapterId+"/"+resId+"/extract/OEBPS/Images/"
        String cdnPath = ""+grailsApplication.config.grails.cdn.path
        if(text!=null&&!"".equals(text)&&text.indexOf(".jpg")>-1) {
            while (hasImage) {
                if (text.indexOf("[](https://") != -1) text = text.replace("[]", "")

                urlStartingIndex = text.indexOf("https://")
                if (urlStartingIndex != -1) {
                    urlEndingIndex = text.indexOf(')', urlStartingIndex)
                    urlString = text.substring(urlStartingIndex, urlEndingIndex)


                    try {
                        def imageBytes = new URL(urlString).bytes


                        // Generate a unique filename
                        def filename = "${System.currentTimeMillis()}.jpg"
                        def filePath = "${savePath}${filename}"
                        File uploadDir = new File(savePath)
                        if (!uploadDir.exists()) {
                            uploadDir.mkdirs()
                        } else {
                        }
                        // Save the image to the local file system
                        FileUtils.writeByteArrayToFile(new File(filePath), imageBytes)

                        //now get height and width
                        queryPart = urlString.substring(urlString.indexOf('?') + 1)
                        firstPart = text.substring(0, urlStartingIndex-2)
                        lastPart = text.substring(urlEndingIndex+1)
                        //replace it with proper url
                        if("localhost".equals(cdnPath))
                        text = firstPart + "<p><img  src='/funlearn/downloadEpubImage?source=" + dbSavePath+filename + "' " + queryPart.split('&')[0] + " " + queryPart.split('&')[1] + " /></p>" + lastPart
                       else
                            text = firstPart + "<p><img  src='" +cdnPath+"/"+dbSavePath+filename + "' " + queryPart.split('&')[0] + " " + queryPart.split('&')[1] + " /></p>" + lastPart


                        if (text.indexOf("https://") == -1) hasImage = false


                    } catch (Exception e) {
                        println("the exception is " + e.toString())
                    }
                } else {
                    hasImage = false
                }
            }


        }

        if(text!=null&&!"".equals(text)){
            //replace the single quote thingy
            text = text.replaceAll("&#39;","'")
        }
        if("true".equals(ignoreFormula)) return text
        else return convertFormula(text)
    }

    def extractImage(String text,String bookId,String chapterId,String resId){
        extractImage(text,bookId,chapterId,resId,"false")
    }
    
    String convertFormula(String textInput){
        def text = textInput


            //replace double $$
            if (text != null) {
                if(text.indexOf("\$\\")>-1||text.indexOf("\$\$")>-1) {
                text = text.replace('#', '`')
                text = text.replace("begin{tabular}", "begin{array}").replace("end{tabular}", "end{array}")
                text = text.replace('$', "#")
                if (text.indexOf("##") > -1) {
                    boolean hasFormula = true

                    while (hasFormula) {
                        text = text.replaceFirst("##", "<span class=\"math-tex\">\\\\(")
                        text = text.replaceFirst("##", "\\\\)</span>")
                        if (text.indexOf('##') == -1) hasFormula = false
                    }
                }


                if (countSubstringOccurrences(text, "#") > 1) {
                    boolean hasFormula = true

                    //add additional logic to findout if there is opening and closing $ to change replace
                    while (hasFormula) {
                        text = text.replaceFirst("#", "<span class=\"math-tex\">\\\\(")
                        text = text.replaceFirst("#", "\\\\)</span>")
                        if (countSubstringOccurrences(text, "#") < 2) hasFormula = false
                    }
                }
                text = text.replace('#', '$')
                text = text.replace('`', '#')

            }
        }
        return text
    }

      int countSubstringOccurrences(String text, String substring) {
        int count = 0;
        int index = text.indexOf(substring);

        while (index != -1) {
            count++;
            index = text.indexOf(substring, index + 1);
        }

        return count;
    }

    def copyFile(String sourcePath, String destinationPath) {
        try {
            def sourceFile = new File(sourcePath)
            def destinationFile = new File(destinationPath)

            destinationFile.withOutputStream { outputStream ->
                sourceFile.withInputStream { inputStream ->
                    outputStream << inputStream
                }
            }
            sourceFile.delete()
            println("File copied successfully.")
        } catch (IOException e) {
            println("An error occurred while copying the file: ${e.message}")
        }
    }

    def appendImgLinks(String text, def imagesList){
        String cdnPath = ""+grailsApplication.config.grails.cdn.path

        if(imagesList.size()>0){
            for(int i=0;i<imagesList.size();i++){
                def extractedUrl = imagesList[i].replaceFirst(".*source=", "")
                text += "\n"+"<p><img  src='" +cdnPath+"/"+extractedUrl+ "' /></p>"
            }
            return text
        }
    }
}
