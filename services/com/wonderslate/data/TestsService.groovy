package com.wonderslate.data

import com.wonderslate.groups.GroupsMst
import com.wonderslate.prepjoy.DailyTestsMst
import grails.transaction.Transactional

@Transactional
class TestsService {
    def redisService
    def getDailyTestsMst(String dailyTestId){
        DailyTestsMst dailyTestsMst  = redisService.memoizeDomainObject(DailyTestsMst, "dailyTestsMst_"+dailyTestId) {
            return DailyTestsMst.findById(new Integer(dailyTestId))
        }

        return dailyTestsMst
    }
}
