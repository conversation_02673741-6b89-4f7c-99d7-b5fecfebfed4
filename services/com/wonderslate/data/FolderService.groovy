package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

@Transactional
class FolderService {

    SpringSecurityService springSecurityService
    UtilService utilService
    DataProviderService dataProviderService
    def redisService


    def addFolder(folderName,siteId){
        FolderMst folderMst = new FolderMst(username: springSecurityService.currentUser.username,folderName: folderName,siteId: siteId)
        folderMst.save(flush: true, failOnError: true)
        dataProviderService.getUserFolders(springSecurityService.currentUser.username)
        return folderMst.id
    }

    def deleteFolder(){

    }


    def addResourceToFolder(folderId,resId){
        FolderDtl folderDtl = FolderDtl.findByFolderIdAndResId(new Long(folderId),new Long(resId))
         if(folderDtl==null) {
             println("entering it sir")
             folderDtl = new FolderDtl(folderId: new Long(folderId), resId: new Long(resId))
             folderDtl.save(flush: true, failOnError: true)
             dataProviderService.getFolderContents(folderId)
         }
    }

}
