package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.AutogptLog
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.logs.AutogptErrorLoggerService
import com.wonderslate.sqlutil.SafeSql
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.grails.web.json.JSONObject
import org.springframework.transaction.annotation.Propagation
import grails.async.Promises
import static grails.async.Promises.task
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.StringEntity
import org.apache.http.util.EntityUtils


class AutogptService {

    DataProviderService dataProviderService
    def grailsApplication
    PromptService promptService
    AutogptErrorLoggerService autogptErrorLoggerService
    TheoryBooksService theoryBooksService
    AsyncLogsService asyncLogsService

    // Connection pool for HTTP requests - no timeouts for LLM calls
    private static final PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager()
    private static final CloseableHttpClient httpClient

    static {
        connectionManager.setMaxTotal(20)
        connectionManager.setDefaultMaxPerRoute(10)

        httpClient = HttpClients.custom()
            .setConnectionManager(connectionManager)
            .disableConnectionState() // For better performance
            .build()
    }

    // ==================== COMMON UTILITY METHODS ====================

    /**
     * Validates and prepares resource data with common error checking
     */
    private Map validateAndPrepareResource(params) {
        if (!params.resId) {
            return [status: "ERROR", message: "resId parameter is required"]
        }

        ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        if (!resourceDtl) {
            return [status: "ERROR", message: "Resource not found for resId: " + params.resId]
        }
        if(resourceDtl.vectorStored==null){
            println("vectorStored is null for chapter " + resourceDtl.chapterId+" so we will call storePdfVectors")
            //call storePdfVectors
            storePdfVectors(params)
            ResourceDtl.withSession { session ->
                session.clear()
            }

        }


        ChaptersMst chaptersMst = ChaptersMst.findById(resourceDtl.chapterId)
        if (!chaptersMst) {
            return [status: "ERROR", message: "Chapter not found for chapterId: " + resourceDtl.chapterId]
        }

        return [status: "OK", resourceDtl: resourceDtl, chaptersMst: chaptersMst]
    }

    /**
     * Builds extract path with fallback logic
     */
    private String buildExtractPath(ChaptersMst chaptersMst, ResourceDtl resourceDtl) {
        String extractPath = resourceDtl.extractPath
        if (!extractPath || extractPath.trim().isEmpty()) {
            extractPath = "supload/pdfextracts/${chaptersMst.bookId}/${chaptersMst.id}/${resourceDtl.id}/extractedImages/${resourceDtl.id}.txt"
            File sourceFile = new File(grailsApplication.config.grails.basedir.path + "/" + extractPath)
            if (sourceFile.exists()) {
                resourceDtl.extractPath = extractPath
                resourceDtl.save(failOnError: true, flush: true)
            }
        }
        return extractPath
    }

    /**
     * Ensures vector file exists, creates if missing
     */
    private void ensureVectorFileExists(params, String extractPath) {
        File sourceFile = new File(grailsApplication.config.grails.basedir.path + "/" + extractPath)
        if (!sourceFile.exists()) {
            storePdfVectors(params)
        }
    }

    /**
     * Gets full file path from extract path
     */
    private String getFullFilePath(String extractPath) {
        return grailsApplication.config.grails.basedir.path + "/" + extractPath
    }

    /**
     * Gets folder path from extract path
     */
    private String getFolderPath(String extractPath) {
        return extractPath.substring(0, extractPath.lastIndexOf("/"))
    }

    // ==================== JSON PROCESSING UTILITIES ====================

    /**
     * Safely parses JSON response with error handling and logging
     */
    private List parseJsonResponseSafely(String response, String operation, ResourceDtl resourceDtl) {
        if (!response || response.trim().isEmpty() || "[]\\n[]\\n".equals(response)) {
            return []
        }

        try {
            return new JsonSlurper().parseText(jsonCleaner(response))
        } catch (Exception e) {
            String truncatedOperation = truncatePromptType(operation)
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedOperation,
                    "Exception in parsing JSON: ${e.toString()}", response)

            // Try to fix JSON and parse again
            try {
                String fixedResponse = fixJSONFromLLM(response)
                return new JsonSlurper().parseText(jsonCleaner(fixedResponse))
            } catch (Exception e1) {
                String truncatedOperationFixed = truncatePromptType("${operation}Fixed")
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedOperationFixed,
                        "Exception in parsing fixed JSON: ${e1.toString()}", response)
                return []
            }
        }
    }

    /**
     * Processes LLM response with error handling and retry logic
     */
    private String processLLMResponseSafely(ResourceDtl resourceDtl, String inputText, String promptType, String serverIPAddress) {
        String response = getLLMResponse(resourceDtl, inputText, promptType, serverIPAddress)

        if (response?.startsWith("Error-")) {
            String errorMessage = response.substring(6)
            String truncatedPromptType = truncatePromptType(promptType)
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedPromptType, errorMessage, inputText)

            // Try with fixed input
            try {
                String fixedInput = fixJSONFromLLM(inputText)
                response = getLLMResponse(resourceDtl, fixedInput, promptType, serverIPAddress)
            } catch (Exception e) {
                String truncatedPromptTypeFixed = truncatePromptType("${promptType}Fixed")
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, truncatedPromptTypeFixed,
                        "Exception in getLLMResponse", inputText)
                return null
            }
        }

        return response
    }

    /**
     * Truncates prompt type to fit database column constraints
     */
    private String truncatePromptType(String promptType) {
        // Assuming prompt_type column has a limit (commonly 50 or 100 characters)
        int maxLength = 50
        return promptType?.length() > maxLength ? promptType.substring(0, maxLength) : promptType
    }

    /**
     * Safely truncates exception messages for logging
     */
    private String truncateException(Exception e, int maxLength = 1000) {
        String message = e.toString()
        return message.length() > maxLength ? message.substring(0, maxLength) : message
    }

    // ==================== QUESTION PROCESSING UTILITIES ====================

    /**
     * Processes unanswered questions for a chapter across PYQs and QuestionBank
     * @param chapterId - The chapter ID to process
     * @param serverIPAddress - The server IP address for LLM calls
     * @return Map with processing results
     */
    def processUnansweredQuestionsForChapter(Long chapterId, String serverIPAddress) {
        println("Starting processUnansweredQuestionsForChapter for chapter ${chapterId}")

        // Get chapter and reading material
        ChaptersMst chaptersMst = ChaptersMst.findById(chapterId)
        if (!chaptersMst) {
            return [status: "ERROR", message: "Chapter not found for chapterId: ${chapterId}"]
        }

        // Find reading material (Notes) for this chapter
        ResourceDtl readingMaterial = ResourceDtl.findByChapterIdAndResType(chapterId, "Notes")
        if (!readingMaterial) {
            return [status: "ERROR", message: "No reading material found for chapter ${chapterId}"]
        }

        // Ensure vector file exists
        if (readingMaterial.vectorStored == null) {
            println("vectorStored is null for chapter ${chapterId}, calling storePdfVectors")
            def params = [resId: readingMaterial.id, serverIPAddress: serverIPAddress,chapterId:chapterId]
            storePdfVectors(params)
            ResourceDtl.withSession { session ->
                session.clear()
            }
        }

        def results = [
            status: "OK",
            chapterId: chapterId,
            processedResources: [],
            totalProcessed: 0,
            errors: []
        ]

        // Process PYQs
        def pyqResult = processUnansweredForResourceType(chapterId, "PYQs", "QA", readingMaterial, serverIPAddress)
        if (pyqResult.processed > 0 || pyqResult.error) {
            results.processedResources.add(pyqResult)
            results.totalProcessed += pyqResult.processed
        }

        // Process QuestionBank QnA
        def qnaResult = processUnansweredForResourceType(chapterId, "QuestionBank QnA", "QA", readingMaterial, serverIPAddress)
        if (qnaResult.processed > 0 || qnaResult.error) {
            results.processedResources.add(qnaResult)
            results.totalProcessed += qnaResult.processed
        }

        // Process QuestionBank MCQs
      /**  def mcqResult = processUnansweredForResourceType(chapterId, "QuestionBank MCQs", "Multiple Choice Questions", readingMaterial, serverIPAddress)
        if (mcqResult.processed > 0 || mcqResult.error) {
            results.processedResources.add(mcqResult)
            results.totalProcessed += mcqResult.processed
        }*/

        println("Completed processUnansweredQuestionsForChapter for chapter ${chapterId}. Total processed: ${results.totalProcessed}")
        return results
    }

    /**
     * Processes unanswered questions for a specific resource type
     */
    private Map processUnansweredForResourceType(Long chapterId, String resourceName, String resType, ResourceDtl readingMaterial, String serverIPAddress) {
        def result = [
            resourceName: resourceName,
            resType: resType,
            totalQuestions: 0,
            unansweredQuestions: 0,
            processed: 0,
            error: null
        ]

        try {
            // Find the resource
            ResourceDtl questionResource = ResourceDtl.findByChapterIdAndResourceNameAndResType(chapterId, resourceName, resType)
            if (!questionResource) {
                println("No ${resourceName} found for chapter ${chapterId}")
                return result
            }

            // Count total and unanswered questions
            result.totalQuestions = ObjectiveMst.countByQuizId(new Integer(questionResource.resLink))

            // Count unanswered questions (null or empty answer)
            result.unansweredQuestions = ObjectiveMst.createCriteria().count {
                eq('quizId', new Integer(questionResource.resLink))
                or {
                    isNull('answer')
                    eq('answer', '')
                }
            }

            println("Found ${result.unansweredQuestions} unanswered questions out of ${result.totalQuestions} total in ${resourceName} for chapter ${chapterId}")

            if (result.unansweredQuestions == 0) {
                println("All questions already have answers in ${resourceName} for chapter ${chapterId}")
                return result
            }

            // Process unanswered questions using existing method pattern
            def params = [resId: readingMaterial.id, serverIPAddress: serverIPAddress]
            def processResult = addAnswersToPYQs(readingMaterial, ChaptersMst.findById(chapterId), questionResource, params)

            if (processResult.status == "OK") {
                result.processed = processResult.totalProcessed ?: 0
                println("Successfully processed ${result.processed} questions for ${resourceName} in chapter ${chapterId}")
            } else {
                result.error = processResult.message ?: "Unknown error processing ${resourceName}"
                println("Error processing ${resourceName} for chapter ${chapterId}: ${result.error}")
            }

        } catch (Exception e) {
            result.error = "Exception processing ${resourceName}: ${e.message}"
            println("Exception processing ${resourceName} for chapter ${chapterId}: ${e.message}")
        }

        return result
    }

    /**
     * Creates or gets ResourceDtl for question storage
     */
    private ResourceDtl getOrCreateQuestionResource(ResourceDtl sourceResource, String resourceName, String resType, String gptResourceType = null) {
        ResourceDtl existingResource = ResourceDtl.findByChapterIdAndResourceName(sourceResource.chapterId, resourceName)

        if (existingResource) {
            return existingResource
        }

        QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
        quizIdGenerator.save()

        ResourceDtl newResource = new ResourceDtl()
        newResource.resLink = quizIdGenerator.id
        newResource.createdBy = "System"
        newResource.resType = resType
        newResource.chapterId = sourceResource.chapterId
        newResource.resourceName = resourceName
        if (gptResourceType) {
            newResource.gptResourceType = gptResourceType
        }
        newResource.save(failOnError: true, flush: true)

        return newResource
    }

    /**
     * Saves question to database with language and formula handling
     */
    private void saveQuestionToDatabase(Map questionData, ResourceDtl targetResource, BooksMst booksMst, String questionType) {
        ObjectiveMst om

        if (questionType == "MCQ") {
            // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
            String correctAnswerStr = questionData.correctAnswer.toString()
            boolean isOption1 = correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")
            boolean isOption2 = correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")
            boolean isOption3 = correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")
            boolean isOption4 = correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")

            om = new ObjectiveMst(
                    quizId: new Integer(targetResource.resLink),
                    quizType: "MCQ",
                    question: fixFormulas(questionData.questionText ?: questionData.question),
                    answer: fixFormulas(questionData.solution),
                    difficultylevel: questionData.difficultyLevel,
                    qType: questionData.questionType,
                    answerDescription: fixFormulas(questionData.explanation),
                    bloomType: questionData.bloomLevel,
                    option1: questionData.option1,
                    option2: questionData.option2,
                    option3: questionData.option3,
                    option4: questionData.option4,
                    answer1: isOption1 ? "Yes" : null,
                    answer2: isOption2 ? "Yes" : null,
                    answer3: isOption3 ? "Yes" : null,
                    answer4: isOption4 ? "Yes" : null
            )
        } else {
            om = new ObjectiveMst(
                    quizId: new Integer(targetResource.resLink),
                    quizType: "QA",
                    question: fixFormulas(questionData.question),
                    answer: fixFormulas(questionData.solution),
                    difficultylevel: questionData.difficultyLevel,
                    qType: questionData.questionType,
                    answerDescription: fixFormulas(questionData.explanation),
                    bloomType: questionData.bloomLevel
            )
        }

        om.save(failOnError: true, flush: true)

        // Handle language corrections
        if (booksMst?.language && !"English".equals(booksMst.language) && !"".equals(booksMst.language)) {
            fixLanguage("${om.id}", questionType.toLowerCase(), booksMst.language)
        }

        // Handle formula corrections
        if ("true".equals("${questionData.hasFormula}")) {
            fixQuestion("${om.id}", questionType.toLowerCase())
        }
    }



    /**
     * Checks if exercise is already created for a chapter
     */
    private boolean isExerciseAlreadyCreated(Long chapterId) {
        ResourceDtl exerciseResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chapterId, "Exercise Solutions")
        if (exerciseResourceDtl) {
            try {
                ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(exerciseResourceDtl.resLink))
                return objectiveMst != null
            } catch (Exception e) {
                return false
            }
        }
        println("Exercise not created for chapter ${chapterId}")
        return false
    }

    /**
     * Processes file content in chunks and extracts exercises
     */
    private int processFileInChunks(File textFile, ResourceDtl resourceDtl, BooksMst booksMst, String serverIPAddress) {
        int totalQuestions = 0
        int chunkSize = 20000
        ResourceDtl exerciseResourceDtl = null

        textFile.withReader('UTF-8') { reader ->
            char[] buffer = new char[chunkSize]
            StringBuilder currentChunk = new StringBuilder()
            int charsRead

            while ((charsRead = reader.read(buffer, 0, chunkSize)) != -1) {
                currentChunk.append(buffer, 0, charsRead)

                // If we have read exactly chunkSize characters, look for next paragraph break
                if (charsRead == chunkSize) {
                    int nextChar
                    while ((nextChar = reader.read()) != -1) {
                        currentChunk.append((char) nextChar)
                        String content = currentChunk.toString()
                        if (content.endsWith("\n\n") || content.endsWith("\r\n\r\n")) {
                            break
                        }
                    }
                }

                List response = extractExercisesFromChunk(resourceDtl, currentChunk.toString(), serverIPAddress)

                if (response && response.size() > 0) {
                    if (!exerciseResourceDtl) {
                        exerciseResourceDtl = getOrCreateQuestionResource(resourceDtl, "Exercise Solutions", "QA")
                    }

                    totalQuestions += processExtractedExercises(response, exerciseResourceDtl, resourceDtl, booksMst, serverIPAddress)
                }

                currentChunk.setLength(0) // Clear the buffer for next chunk
            }
        }
      println("number of questions added are ${totalQuestions}")
        return totalQuestions
    }

    /**
     * Extracts exercises from a text chunk
     */
    private List extractExercisesFromChunk(ResourceDtl resourceDtl, String inputText, String serverIPAddress) {
        Prompts prompts = Prompts.findByPromptType("exerciseExtractor")
        String response = processLLMResponseSafely(resourceDtl, inputText, prompts.basePrompt, serverIPAddress)

        if (!response) {
            return []
        }

        return parseJsonResponseSafely(response, "exerciseExtractor", resourceDtl)
    }

    /**
     * Processes extracted exercises and saves them to database
     */
    private int processExtractedExercises(List exercises, ResourceDtl exerciseResourceDtl, ResourceDtl sourceResourceDtl, BooksMst booksMst, String serverIPAddress) {
        int totalQuestions = 0
        int noOfQuestionsPerIteration = 5
        int maxParallelTasks = 5

        // Extract database data upfront to avoid Hibernate session issues in async tasks
        String namespace = sourceResourceDtl.vectorStored
        Long resId = sourceResourceDtl.id
        Long chapterId = sourceResourceDtl.chapterId
        Long bookId = booksMst.id
        String customPrompt = buildCustomPrompt("solutionCreator", bookId, 1)

        // Prepare all batches
        List<Map> allBatches = []
        int currentIndex = 0
        while (currentIndex < exercises.size()) {
            int endIndex = Math.min(currentIndex + noOfQuestionsPerIteration, exercises.size())
            List batch = exercises.subList(currentIndex, endIndex)

            String inputQuestions = "The questions are \n"
            batch.eachWithIndex { exercise, index ->
                String question = exercise.question
                inputQuestions += "${currentIndex + index + 1}. ${exercise.question.replace("\"", "'")}\n"
            }
            inputQuestions = jsonCleaner(inputQuestions)


            allBatches.add([
                inputQuestions: inputQuestions,
                startIndex: currentIndex
            ])
            currentIndex = endIndex
        }

        // Process in chunks of 5 parallel tasks
        for (int i = 0; i < allBatches.size(); i += maxParallelTasks) {
            int endIndex = Math.min(i + maxParallelTasks, allBatches.size())
            List<Map> currentChunk = allBatches.subList(i, endIndex)

            println("Processing ${currentChunk.size()} batches in parallel (${i + 1}-${endIndex} of ${allBatches.size()})")

            // Create parallel tasks for this chunk
            def promises = currentChunk.collect { batchData ->
                task {
                    return callLLMForSolution(serverIPAddress, namespace, resId, chapterId, bookId, customPrompt, batchData.inputQuestions)
                }
            }

            // Wait for all tasks in this chunk to complete
            def results = promises.collect { it.get() }

            // Process results sequentially (database operations)
            results.each { result ->
                if (result?.answer) {
                    List solutions = parseJsonResponseSafely(result.answer, "solutionCreator", sourceResourceDtl)
                    solutions.each { solution ->
                        saveQuestionToDatabase(solution, exerciseResourceDtl, booksMst, "QA")
                        totalQuestions++
                    }
                }
            }
        }

        return totalQuestions
    }

    /**
     * Simple LLM call method for parallel processing (no database access)
     */
    private Map callLLMForSolution(String serverIPAddress, String namespace, Long resId, Long chapterId, Long bookId, String customPrompt, String inputQuestions) {
        println("the namespace that we are calling with "+namespace)
        try {
            String url = "http://${serverIPAddress}:8000/api/retrieveDataForBook"
            HttpPost httpPost = new HttpPost(url)

            // Set headers
            httpPost.setHeader("Content-Type", "application/json")

            // Create request body
            JSONObject requestBody = new JSONObject()
            requestBody.put("namespace", namespace)
            requestBody.put("resId", resId)
            requestBody.put("resType", "userInput")
            requestBody.put("chatHistory", "")
            requestBody.put("query", inputQuestions)
            requestBody.put("chapterId", chapterId)
            requestBody.put("bookId", bookId)
            requestBody.put("customPrompt", customPrompt)
            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8")
            httpPost.setEntity(entity)
            // Execute request with no timeout (LLM responses can be very long)
            def response = httpClient.execute(httpPost)
            try {
                if (response.getStatusLine().getStatusCode() == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity())
                    def jsonSlurper = new JsonSlurper()
                    try {
                        def jsonResponse = jsonSlurper.parseText(responseBody)
                        jsonResponse.answer = jsonResponse.answer.replaceAll("\\\\n", "<br>")
                        return jsonResponse
                    } catch (Exception e) {
                        println("Exception parsing response: ${e.message}")
                        return null
                    }
                } else {
                    println("HTTP Error: ${response.getStatusLine().getStatusCode()} for namespace ${namespace}")
                    println("HTTP Error: Full response: ${response}")
                    return null
                }
            } finally {
                response.close()
            }
        } catch (Exception e) {
            println("Exception in callLLMForSolution: ${e.message}")
            return null
        }
    }

    /**
     * Simple LLM call method for question generation (no database access)
     */
    private Map callLLMForQuestionGeneration(String serverIPAddress, String promptText, int subtopicNumber, Long chapterId, String questionType) {
        try {
            String url = grailsApplication.config.grails.aiserver.url + "/chat-completion"
            HttpPost httpPost = new HttpPost(url)

            // Set headers
            httpPost.setHeader("Content-Type", "application/json")

            // Create request body
            JSONObject requestBody = new JSONObject()
            requestBody.put("prompt", promptText)

            StringEntity entity = new StringEntity(requestBody.toString(), "UTF-8")
            httpPost.setEntity(entity)

            // Execute request with no timeout (LLM responses can be very long)
            def response = httpClient.execute(httpPost)

            try {
                if (response.getStatusLine().getStatusCode() == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity())
                    def jsonSlurper = new JsonSlurper()
                    try {
                        def jsonResponse = jsonSlurper.parseText(responseBody)

                        // Parse the response to extract questions
                        List<String> questions = []
                        if (jsonResponse.response) {
                            try {
                                def responseList = new JsonSlurper().parseText(jsonCleaner(jsonResponse.response))
                                responseList.each { questionItem ->
                                    String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                                    if (questionText && !questions.contains(questionText)) {
                                        questions.add(questionText)
                                    }
                                }
                            } catch (Exception e) {
                                println("Exception parsing question response for subtopic ${subtopicNumber}: ${e.message}")
                            }
                        }

                        println("Generated ${questions.size()} ${questionType} questions for subtopic ${subtopicNumber} for chapter ${chapterId}")
                        return [questions: questions, subtopicNumber: subtopicNumber, questionType: questionType]
                    } catch (Exception e) {
                        println("Exception parsing response for subtopic ${subtopicNumber}: ${e.message}")
                        return [questions: [], subtopicNumber: subtopicNumber, questionType: questionType]
                    }
                } else {
                    println("HTTP Error for subtopic ${subtopicNumber}: ${response.getStatusLine().getStatusCode()}")
                    return [questions: [], subtopicNumber: subtopicNumber, questionType: questionType]
                }
            } finally {
                response.close()
            }
        } catch (Exception e) {
            println("Exception in callLLMForQuestionGeneration for subtopic ${subtopicNumber}: ${e.message}")
            return [questions: [], subtopicNumber: subtopicNumber, questionType: questionType]
        }
    }

    def getChapterMetaData(params) {
        // Validate and prepare resources
        Map validation = validateAndPrepareResource(params)
        if (validation.status == "ERROR") {
            return validation
        }

        ResourceDtl resourceDtl = validation.resourceDtl
        ChaptersMst chaptersMst = validation.chaptersMst

        String extractPath = buildExtractPath(chaptersMst, resourceDtl)
        String folderPath = getFolderPath(extractPath)
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/chapterMetadata${chaptersMst.id}.txt")
        //if file exists, delete it and create a new one
        if (metadataFile.exists()) {
            println("Metadata file exists for chapter ${chaptersMst.id} and we will delete it first")
            metadataFile.delete()
        }
        metadataFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/chapterMetadata${chaptersMst.id}.txt")
        ensureVectorFileExists(params, extractPath)
        String filePath = getFullFilePath(extractPath)
        File textFile = new File(filePath)
        String fileContent = textFile.text

        Prompts prompts = Prompts.findByPromptType("chapterMetadataExtractor")
        String response = getLLMResponse(resourceDtl, fileContent, prompts.basePrompt, params.serverIPAddress)
        metadataFile.write(response)


        return [status: "OK"]
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def exerciseCollector(params) {
        try {
            // Validate and prepare resources
            Map validation = validateAndPrepareResource(params)
            if (validation.status == "ERROR") {
                return validation
            }

            ResourceDtl resourceDtl = validation.resourceDtl
            ChaptersMst chaptersMst = validation.chaptersMst

            // Check if exercises already exist
            if (isExerciseAlreadyCreated(chaptersMst.id)) {
                return [status: "OK", message: "Exercises already created"]
            }

            BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
            String extractPath = buildExtractPath(chaptersMst, resourceDtl)
            ensureVectorFileExists(params, extractPath)

            String filePath = getFullFilePath(extractPath)
            File textFile = new File(filePath)

            if (!textFile.exists()) {
                return [status: "ERROR", message: "Text file not found at path: " + filePath]
            }

            int totalQuestions = processFileInChunks(textFile, resourceDtl, booksMst, params.serverIPAddress)

            dataProviderService.getChaptersList(chaptersMst.bookId)
            return [status: "OK", message: "File processed successfully", totalQuestions: totalQuestions]

        } catch (Exception e) {
            println("Exception in exerciseCollector: ${truncateException(e)}")
            return [status: "ERROR", message: "Error processing file: " + e.message]
        }
    }

    def examplesAndExercisesExtractor(ResourceDtl resourceDtl, String inputText, String serverIPAddress) {
        try {
            Prompts prompts = Prompts.findByPromptType("exerciseExtractor")
            String response = processLLMResponseSafely(resourceDtl, inputText, prompts.basePrompt, serverIPAddress)

            if (!response) {
                return []
            }

            return parseJsonResponseSafely(response, "exerciseExtractor", resourceDtl)
        } catch (Exception e) {
            println("Exception in examplesAndExercisesExtractor: ${truncateException(e)}")
            return []
        }
    }

    def getLLMResponse(ResourceDtl resourceDtl, String inputText, String basePrompt, String serverIPAddress = null) {
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        String extractPath = buildExtractPath(chaptersMst, resourceDtl)
        String folderPath = getFolderPath(extractPath)

        File tempFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/extractData${resourceDtl.id}.txt")

        try {
            // Clean up existing file and write new content
            if (tempFile.exists()) {
                tempFile.delete()
            }
            tempFile.write(inputText, "UTF-8")

            // Make HTTP request
            String response = makeMultipartRequest(serverIPAddress, basePrompt, tempFile)
            def json = new JsonSlurper().parseText(response)
            String responseAnswer = jsonCleaner(json.response)

            return responseAnswer
        } catch (Exception e) {
            println("Exception in getLLMResponse: ${truncateException(e)}")
            return "Error-${e.getMessage()}"
        } finally {
            // Always clean up temp file
            if (tempFile.exists()) {
                tempFile.delete()
            }
        }
    }

    /**
     * Makes multipart HTTP request with optimized connection handling
     */
    private String makeMultipartRequest(String serverIPAddress, String basePrompt, File file) {
        URL url = new URL("http://${serverIPAddress}:8000/api/retrieveDataAdminText")
        HttpURLConnection conn = (HttpURLConnection) url.openConnection()

        try {
            conn.setRequestMethod("POST")
            conn.setDoOutput(true)
            conn.setDoInput(true)

            String boundary = "===${System.currentTimeMillis()}==="
            conn.setRequestProperty("Content-Type", "multipart/form-data; boundary=${boundary}")

            String prompt = fixCurlyBrackets(basePrompt)

            conn.getOutputStream().withWriter("UTF-8") { writer ->
                // Write prompt part
                writer.write("--${boundary}\r\n")
                writer.write("Content-Disposition: form-data; name=\"prompt\"\r\n")
                writer.write("Content-Type: text/plain; charset=UTF-8\r\n\r\n")
                writer.write(prompt)
                writer.write("\r\n")

                // Write file part
                writer.write("--${boundary}\r\n")
                writer.write("Content-Disposition: form-data; name=\"file\"; filename=\"file.txt\"\r\n")
                writer.write("Content-Type: text/plain; charset=UTF-8\r\n\r\n")
                writer.write(file.text)
                writer.write("\r\n")

                // End boundary
                writer.write("--${boundary}--\r\n")
                writer.flush()
            }

            // Check response code before reading response
            int responseCode = conn.getResponseCode()
            if (responseCode == 200) {
                return conn.getInputStream().getText("UTF-8")
            } else {
                // Read error stream for better error reporting
                String errorResponse = ""
                try {
                    errorResponse = conn.getErrorStream()?.getText("UTF-8") ?: "No error details available"
                } catch (Exception e) {
                    errorResponse = "Could not read error response: ${e.message}"
                }
                throw new IOException("Server returned HTTP response code: ${responseCode}. Error: ${errorResponse}")
            }
        } finally {
            conn.disconnect()
        }
    }

    def getSolution(ResourceDtl resourceDtl, question, chapterId, bookId, String promptName, int noOfSimilarQuestions = 1, String serverIPAddress = null) {
        try {
            // Build custom prompt
            String customPrompt = buildCustomPrompt(promptName, bookId, noOfSimilarQuestions)

            // Build request body
            Map requestBody = [
                    namespace: resourceDtl.vectorStored,
                    resId: resourceDtl.id,
                    resType: "userInput",
                    chatHistory: "",
                    query: question,
                    chapterId: chapterId,
                    bookId: bookId,
                    customPrompt: customPrompt
            ]

            // Make HTTP request
            return makeJsonRequest(serverIPAddress, requestBody, resourceDtl, promptName, question)
        } catch (Exception e) {
            println("Exception in getSolution: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "Exception in getSolution: ${e.getMessage()}", question)
            return null
        }
    }

    /**
     * Builds custom prompt based on prompt type and parameters
     */
    private String buildCustomPrompt(String promptName, Long bookId, int noOfSimilarQuestions) {
        Prompts prompt = Prompts.findByPromptType(promptName)
        String customPrompt = prompt.basePrompt

        // Add language-specific instructions
        if (promptName in ["additionQuestionsCreator", "questionBankBuilder"]) {
            BooksMst booksMst = dataProviderService.getBooksMst(bookId)
            if (booksMst?.language && !"English".equals(booksMst.language) && !"".equals(booksMst.language)) {
                customPrompt += " All questions created should be in ${booksMst.language} language."
            }
        }

        // Replace question count placeholder
        if ("additionQuestionsCreator".equals(promptName)) {
            customPrompt = customPrompt.replaceAll("NOOFQUESTIONS", "${noOfSimilarQuestions}")
        }

        return customPrompt
    }

    /**
     * Makes JSON HTTP request with optimized connection handling
     */
    private Map makeJsonRequest(String serverIPAddress, Map requestBody, ResourceDtl resourceDtl, String promptName, String question) {
        URL url = new URL("http://${serverIPAddress}:8000/api/retrieveDataForBook")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()

        try {
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write request body
            connection.getOutputStream().withWriter("UTF-8") { writer ->
                writer.write(new JsonBuilder(requestBody).toString())
            }

            int responseCode = connection.getResponseCode()
            if (responseCode == 200) {
                String response = connection.getInputStream().getText("UTF-8")
                def jsonResponse = new JsonSlurper().parseText(response)

                // Replace newlines with HTML breaks
                jsonResponse.answer = jsonResponse.answer.replaceAll("\\\\n", "<br>")
                return jsonResponse
            } else {
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "HTTP Error in getSolution: ${responseCode}", question)
                println("HTTP Error in getSolution: ${responseCode}")
                return null
            }
        } catch (Exception e) {
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, promptName, "Exception in makeJsonRequest: ${e.getMessage()}", question)
            println("Exception in makeJsonRequest: ${truncateException(e)}")
            return null
        } finally {
            connection.disconnect()
        }
    }



    def removeExtraCommas(String json) {
        String cleaned = json.replaceAll(",\\s*,+", ","); // Replace multiple commas with a single comma
        cleaned = cleaned.replaceAll(",\\s*]", "]");      // Remove commas just before a closing bracket

        return cleaned;
    }

    def jsonCleaner(String jsonInput){
        jsonInput = jsonInput.replaceAll("`", "")
        jsonInput = jsonInput.replaceAll("json", "")
        jsonInput = jsonInput.replaceAll("\n", "")
        jsonInput = jsonInput.replaceAll("\r", "")
        //next i want to replace ][ with comma
        jsonInput = jsonInput.replaceAll("\\]\\[", ",")
        // replace },] with }]
        jsonInput = jsonInput.replaceAll("},]", "}]")

        //replace  .^ with blank
        jsonInput = jsonInput.replaceAll("\\.\\^", "")
        jsonInput = removeExtraCommas(jsonInput)
        jsonInput = fixJsonString(jsonInput)
        return jsonInput
    }

    def createAdditionalQuestions(ResourceDtl resourceDtl,ChaptersMst chaptersMst,questionList,String serverIPAddress = null){
        println("createAdditionalQuestions for chapter " + chaptersMst.id)
        List<String> additionalQuestions = []
        int noOfSimilarQuestions = 1
        int maxParallelTasks = 5

        if(questionList.size()<200) {
            if (questionList.size() < 100) noOfSimilarQuestions = 2

            // Extract database data upfront to avoid Hibernate session issues in async tasks
            String namespace = resourceDtl.vectorStored
            Long resId = resourceDtl.id
            Long chapterId = chaptersMst.id
            Long bookId = chaptersMst.bookId
            String customPrompt = buildCustomPrompt("additionQuestionsCreator", bookId, noOfSimilarQuestions)

            try {
                int noOfQuestionsPerIteration = 5
                int numberOfQuestions = questionList.size()

                // Prepare all batches
                List<Map> allBatches = []
                int currentQuestionIndex = 0
                while (currentQuestionIndex < numberOfQuestions) {
                    int noOfQuestionsForLoop = Math.min(noOfQuestionsPerIteration, numberOfQuestions - currentQuestionIndex)

                    String inputQuestions = "The subtopic questions are \n"
                    for (int i = currentQuestionIndex; i < currentQuestionIndex + noOfQuestionsForLoop; i++) {
                        inputQuestions += (i + 1) + ". " + questionList[i] + "\n"
                    }
                    inputQuestions = jsonCleaner(inputQuestions)

                    allBatches.add([
                        inputQuestions: inputQuestions,
                        startIndex: currentQuestionIndex
                    ])
                    currentQuestionIndex += noOfQuestionsForLoop
                }

                // Process in chunks of 5 parallel tasks
                for (int i = 0; i < allBatches.size(); i += maxParallelTasks) {
                    int endIndex = Math.min(i + maxParallelTasks, allBatches.size())
                    List<Map> currentChunk = allBatches.subList(i, endIndex)

                    println("Processing ${currentChunk.size()} additional question batches in parallel (${i + 1}-${endIndex} of ${allBatches.size()})")

                    // Create parallel tasks for this chunk
                    def promises = currentChunk.collect { batchData ->
                        task {
                            return callLLMForSolution(serverIPAddress, namespace, resId, chapterId, bookId, customPrompt, batchData.inputQuestions)
                        }
                    }

                    // Wait for all tasks in this chunk to complete
                    def results = promises.collect { it.get() }

                    // Process results sequentially (add to additional questions list)
                    results.each { result ->
                        if (result?.answer) {
                            try {
                                def json1 = new JsonSlurper().parseText(jsonCleaner(result.answer))
                                json1.each { question1 ->
                                    String questionText = (question1 instanceof String) ? question1 : question1.question?.toString()
                                    if(questionText && !additionalQuestions.contains(questionText)) {
                                        additionalQuestions.add(questionText)
                                    }
                                }
                            } catch (Exception e) {
                                println("Exception parsing additional questions: ${e.message}")
                            }
                        }
                    }
                }
            } catch (Exception e) {
                println("Exception in createAdditionalQuestions: " + e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
            }
        }

        println("Total additional questions are " + additionalQuestions.size()+" for chapter " + chaptersMst.id)
        if(additionalQuestions.size()>0){
            //add additional questions to question list
            questionList.addAll(additionalQuestions)
        }
        return questionList
    }

    /**
     * Processes a solution immediately instead of storing it in a list
     */
    private void processSolutionImmediately(String solution, ResourceDtl resourceDtl, ChaptersMst chaptersMst) {
        def jsonAnswerList
        try {
            try {
                jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
            } catch (Exception e) {
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
                solution = fixJSONFromLLM(jsonCleaner(solution))
                jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
            }

            jsonAnswerList.each { json1 ->
                ObjectiveMst om = ObjectiveMst.findById(new Integer("" + json1.questionNo))
                if(om!=null){
                    om.answer = json1.solution
                    om.difficultylevel = json1.difficultyLevel
                    om.qType = json1.questionType
                    om.answerDescription = json1.explanation
                    om.bloomType = json1.bloomLevel
                    om.save(failOnError: true, flush: true)
                    if("true".equals(""+json1.hasFormula)){
                        fixQuestion(""+om.id, "subjective")
                    }
                }
            }
        } catch (Exception e){
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderFixed", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
        }
    }

    /**
     * Processes a solution with batch transaction support for better fault tolerance
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    private def processSolutionWithBatchTransaction(String solution, ResourceDtl resourceDtl, ChaptersMst chaptersMst) {
        def result = [
            status: "success",
            successfulUpdates: 0,
            failedQuestions: [],
            error: null
        ]

        def jsonAnswerList
        try {
            try {
                jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
            } catch (Exception e) {
                println("Exception in processSolutionWithBatchTransaction: for chapter " + chaptersMst.id + " is " + e.toString())
                println("Solution is " + jsonCleaner(solution))
                autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
                solution = fixJSONFromLLM(jsonCleaner(solution))
                jsonAnswerList = new JsonSlurper().parseText(jsonCleaner(solution))
            }

            jsonAnswerList.each { json1 ->
                try {
                    ObjectiveMst om = ObjectiveMst.findById(new Integer("" + json1.questionNo))
                    if(om != null) {
                        om.answer = json1.solution
                        om.difficultylevel = json1.difficultyLevel
                        om.qType = json1.questionType
                        om.answerDescription = json1.explanation
                        om.bloomType = json1.bloomLevel
                        om.save(failOnError: true, flush: true)

                        if("true".equals(""+json1.hasFormula)){
                            fixQuestion(""+om.id, "subjective")
                        }
                        result.successfulUpdates++
                    } else {
                        result.failedQuestions.add([
                            questionId: json1.questionNo,
                            error: "Question not found"
                        ])
                    }
                } catch (Exception e) {
                    result.failedQuestions.add([
                        questionId: json1.questionNo ?: "unknown",
                        error: e.message
                    ])
                    println("Error updating question ${json1.questionNo}: ${e.message}")
                }
            }

            if (result.failedQuestions.size() > 0 && result.successfulUpdates == 0) {
                result.status = "failed"
                result.error = "All questions in batch failed to update"
            } else if (result.failedQuestions.size() > 0) {
                result.status = "partial"
                result.error = "Some questions in batch failed to update"
            }

        } catch (Exception e) {
            println("Exception in processSolutionWithBatchTransaction:Fixed for chapter " + chaptersMst.id + " is " + e.toString())
            println("Solution is " + jsonCleaner(solution))
            result.status = "failed"
            result.error = "Error processing batch response: ${e.message}"
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderFixed", "Exception in parsing jsonAnswer in solution part " + e.toString(), jsonCleaner(solution))
        }

        return result
    }

    private def processAnswersWithPagination(ResourceDtl pyqResourceDtl, ResourceDtl resourceDtl, ChaptersMst chaptersMst, params, int totalUnprocessedCount, String namespace, Long resId, Long chapterId, Long bookId, String customPrompt) {
        int pageSize = 100  // Process 100 questions at a time to manage memory
        int noOfQuestionsPerIteration = 5
        int maxParallelTasks = 5
        int totalProcessed = 0
        int successfulBatches = 0
        int failedBatches = 0
        List<Map> failedBatchDetails = []
        List<Map> batchResults = []
        int currentOffset = 0

        println("Processing ${totalUnprocessedCount} unprocessed questions in pages of ${pageSize}")

        try {
            while (currentOffset < totalUnprocessedCount) {
                // Load a page of unprocessed questions
                List pageQuestions = ObjectiveMst.createCriteria().list(max: pageSize, offset: currentOffset) {
                    eq('quizId', new Integer(pyqResourceDtl.resLink))
                    or {
                        isNull('answer')
                        eq('answer', '')
                    }
                    order('id', 'asc')  // Consistent ordering for pagination
                }

                if (pageQuestions.size() == 0) {
                    break  // No more questions to process
                }

                println("Processing page ${(currentOffset / pageSize) + 1}: ${pageQuestions.size()} questions (offset: ${currentOffset})")

                // Prepare batches from this page
                List<Map> allBatches = []
                int currentIndex = 0
                while (currentIndex < pageQuestions.size()) {
                    int endIndex = Math.min(currentIndex + noOfQuestionsPerIteration, pageQuestions.size())
                    List batch = pageQuestions.subList(currentIndex, endIndex)

                    String inputQuestions = "The questions are \n"
                    batch.eachWithIndex { question, index ->
                        inputQuestions += " ${question.id}. ${question.question.replace("\"", "'")}\n"
                    }
                    inputQuestions = jsonCleaner(inputQuestions)

                    allBatches.add([
                        inputQuestions: inputQuestions,
                        startIndex: currentIndex,
                        questionIds: batch.collect { it.id }
                    ])
                    currentIndex = endIndex
                }

                // Process batches in chunks of 5 parallel tasks
                for (int i = 0; i < allBatches.size(); i += maxParallelTasks) {
                    int endIndex = Math.min(i + maxParallelTasks, allBatches.size())
                    List<Map> currentChunk = allBatches.subList(i, endIndex)

                    println("Processing ${currentChunk.size()} PYQ answer batches in parallel (${i + 1}-${endIndex} of ${allBatches.size()}) from page ${(currentOffset / pageSize) + 1}")

                    // Create parallel tasks for this chunk
                    def promises = currentChunk.withIndex().collect { batchData, batchIndex ->
                        task {
                            int globalBatchIndex = (currentOffset / pageSize) * (pageSize / noOfQuestionsPerIteration) + i + batchIndex
                            return [
                                batchIndex: globalBatchIndex,
                                llmResult: callLLMForSolution(params.serverIPAddress, namespace, resId, chapterId, bookId, customPrompt, batchData.inputQuestions)
                            ]
                        }
                    }

                    // Wait for all tasks in this chunk to complete
                    def results = promises.collect { it.get() }

                    // Process results sequentially with individual batch transactions
                    results.each { result ->
                        if (result?.llmResult?.answer) {
                            try {
                                def batchResult = processSolutionWithBatchTransaction(result.llmResult.answer, resourceDtl, chaptersMst)
                                batchResult.batchIndex = result.batchIndex
                                batchResults.add(batchResult)

                                if (batchResult.status == "success") {
                                    successfulBatches++
                                    totalProcessed += batchResult.successfulUpdates
                                } else {
                                    failedBatches++
                                    failedBatchDetails.add([
                                        batchIndex: result.batchIndex,
                                        error: batchResult.error,
                                        failedQuestions: batchResult.failedQuestions
                                    ])
                                }
                            } catch (Exception e) {
                                failedBatches++
                                failedBatchDetails.add([
                                    batchIndex: result.batchIndex,
                                    error: "Exception processing batch: ${e.message}",
                                    failedQuestions: []
                                ])
                                println("Exception processing PYQ solutions batch ${result.batchIndex}: ${e.message}")
                            }
                        } else {
                            failedBatches++
                            failedBatchDetails.add([
                                batchIndex: result.batchIndex,
                                error: "No LLM response received",
                                failedQuestions: []
                            ])
                        }
                    }
                }

                currentOffset += pageQuestions.size()

                // Clear session periodically to prevent memory buildup
                ObjectiveMst.withSession { session ->
                    session.clear()
                }
            }
        } catch (Exception e) {
            println("Exception in paginated PYQ answers processing: ${e.message}")
        }

        return [
            status: successfulBatches > 0 ? "OK" : "Error",
            totalProcessed: totalProcessed,
            successfulBatches: successfulBatches,
            failedBatches: failedBatches,
            totalBatches: successfulBatches + failedBatches,
            failedBatchDetails: failedBatchDetails,
            batchResults: batchResults
        ]
    }

    def addAnswersToPYQs(ResourceDtl resourceDtl,ChaptersMst chaptersMst, ResourceDtl pyqResourceDtl, params){
        Map validation = validateAndPrepareResource(params)
        if (validation.status == "ERROR") {
            println("Validation error in addAnswersToPYQs: " + validation.message+" for chapter "+chaptersMst.id)
            return validation
        }
        println("starting addAnswersToPYQs for chapter "+chaptersMst.id)
        // Get total count of all questions for reporting
        int totalQuestions = ObjectiveMst.countByQuizId(new Integer(pyqResourceDtl.resLink))

        if(totalQuestions > 0) {
            // Get count of unprocessed questions without loading them into memory
            int unprocessedCount = ObjectiveMst.createCriteria().count {
                eq('quizId', new Integer(pyqResourceDtl.resLink))
                or {
                    isNull('answer')
                    eq('answer', '')
                }
            }

            if (unprocessedCount == 0) {
                println("All questions already have answers for chapter ${chaptersMst.id}")
                return [
                    status: "OK",
                    message: "All questions already have answers",
                    totalQuestions: totalQuestions,
                    unprocessedQuestions: 0,
                    alreadyProcessed: totalQuestions
                ]
            }

            int alreadyProcessed = totalQuestions - unprocessedCount

            println("Found ${unprocessedCount} unprocessed questions out of ${totalQuestions} total questions (${alreadyProcessed} already processed) for chapter ${chaptersMst.id}")

            // Extract database data upfront to avoid Hibernate session issues in async tasks
            String namespace = resourceDtl.vectorStored
            if(namespace==null||"null".equals(namespace)){
                println("vectorStored is null for chapter " + chaptersMst.id+" so we will call direct sql to get it")
                //make a direct sql call to get the resourceDtl
                String sql = "SELECT vector_stored FROM resource_dtl WHERE id = " + resourceDtl.id
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new SafeSql(dataSource)
                def results = sql1.rows(sql)
                if(results.size()>0 && results[0]!=null && results[0].vector_stored!=null) {
                    namespace = results[0].vector_stored
                }
                println("namespace for chapter after making the call is " + chaptersMst.id+" is "+namespace)
            }
            Long resId = resourceDtl.id
            Long chapterId = chaptersMst.id
            Long bookId = chaptersMst.bookId
            String customPrompt = buildCustomPrompt("solutionCreator", bookId, 1)

            // Process unprocessed questions with pagination to avoid memory issues
            def result = processAnswersWithPagination(pyqResourceDtl, resourceDtl, chaptersMst, params, unprocessedCount, namespace, resId, chapterId, bookId, customPrompt)

            println("Completed addAnswersToPYQs for chapter ${chaptersMst.id} - Processed: ${result.totalProcessed}, Successful batches: ${result.successfulBatches}, Failed batches: ${result.failedBatches}")

            // Add summary information to result
            result.totalQuestions = totalQuestions
            result.unprocessedQuestions = unprocessedCount
            result.alreadyProcessed = alreadyProcessed

            return result
        } else {
            return [status: "Error", message: "No questions found for chapter ${chaptersMst.id}"]
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def questionBankBuilder(params) {
        // Validate and prepare resources
        Map validation = validateAndPrepareResource(params)
        if (validation.status == "ERROR") {
            return validation
        }

        ResourceDtl resourceDtl = validation.resourceDtl

        ChaptersMst chaptersMst = validation.chaptersMst
        println("questionBankBuilder for chapter inside questionBankBuilder ${chaptersMst.id}")
        // Handle PYQs if they exist
        handlePYQsIfNeeded(resourceDtl, chaptersMst, params)
        // Check if question bank already exists
        ResourceDtl questionBankResourceDtl = ResourceDtl.findByChapterIdAndResourceNameLike(chaptersMst.id, "QuestionBank%")
        if (questionBankResourceDtl) {
            return [status: "OK", message: "Question bank already created", totalQuestions: "Not Counted"]
        }



        try {
            // Generate question bank
            List<String> questionList = generateQuestionBank(resourceDtl, chaptersMst, params)

            // Process and save questions with streaming approach to avoid memory buildup
            def result = generateAndProcessSolutionsStreaming(questionList, resourceDtl, chaptersMst, params.serverIPAddress)

            return [status: "OK", totalQuestions: questionList.size(), processedSolutions: result.processedSolutions]
        } catch (Exception e) {
            println("Exception in questionBankBuilder: ${truncateException(e)}")
            return [status: "ERROR", message: "Error in question bank builder: ${e.message}"]
        }
    }

    /**
     * Handles PYQs processing if needed
     */
    private void handlePYQsIfNeeded(ResourceDtl resourceDtl, ChaptersMst chaptersMst, params) {
        // Clear session cache to ensure we get fresh data from database
        ResourceDtl.withSession { session ->
            session.clear()
        }

        ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chaptersMst.id, "PYQs")
        println("pyqResourceDtl: ${pyqResourceDtl}"+" for chapter " + chaptersMst.id)

        if (pyqResourceDtl) {
            ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(pyqResourceDtl.resLink))
            if (objectiveMst) {
                //get number of questions which does not have answer
                int noOfQuestionsWithoutAnswer = ObjectiveMst.countByQuizIdAndAnswerIsNull(new Integer(pyqResourceDtl.resLink))
                println("noOfQuestionsWithoutAnswer: ${noOfQuestionsWithoutAnswer} for chapter " + chaptersMst.id)
                if (noOfQuestionsWithoutAnswer > 0) {
                    String extractPath = buildExtractPath(chaptersMst, resourceDtl)
                    ensureVectorFileExists(params, extractPath)
                    addAnswersToPYQs(resourceDtl, chaptersMst, pyqResourceDtl, params)
                }
                ObjectiveMst.withSession { session ->
                    session.clear()
                }
            }
        }
    }



    def addQuestionBank(ResourceDtl resourceDtl, ChaptersMst chaptersMst, List<String> solutionList) {
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        ResourceDtl mcqResourceDtl = null
        ResourceDtl qaResourceDtl = null

        for (String solution : solutionList) {
            List jsonAnswerList = parseJsonResponseSafely(solution, "questionBankBuilder", resourceDtl)

            jsonAnswerList.each { questionData ->
                if (questionData.questionType == "MCQ") {
                    if (!mcqResourceDtl) {
                        mcqResourceDtl = createMCQResource(resourceDtl)
                    }
                    saveQuestionToDatabase(questionData, mcqResourceDtl, booksMst, "MCQ")
                } else {
                    if (!qaResourceDtl) {
                        qaResourceDtl = createQAResource(resourceDtl)
                    }
                    saveQuestionToDatabase(questionData, qaResourceDtl, booksMst, "QA")
                }
            }
        }
    }

    /**
     * Creates MCQ resource with GPT default log
     */
    private ResourceDtl createMCQResource(ResourceDtl sourceResource) {
        ResourceDtl mcqResource = getOrCreateQuestionResource(sourceResource, "QuestionBank MCQs", "Multiple Choice Questions", "mcq")

        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(sourceResource.id, "mcq")
        if (!gptDefaultCreateLog) {
            gptDefaultCreateLog = new GptDefaultCreateLog(
                    resId: mcqResource.id,
                    promptType: "mcq",
                    prompt: "Create MCQs (Multiple Choice Questions)",
                    response: "MCQ",
                    readingMaterialResId: sourceResource.id,
                    username: "System",
                    promptLabel: "Create MCQs (Multiple Choice Questions)"
            )
        } else {
            gptDefaultCreateLog.resId = mcqResource.id
        }
        gptDefaultCreateLog.save(failOnError: true, flush: true)

        return mcqResource
    }

    /**
     * Creates QA resource with GPT default log
     */
    private ResourceDtl createQAResource(ResourceDtl sourceResource) {
        ResourceDtl qaResource = getOrCreateQuestionResource(sourceResource, "QuestionBank QnA", "QA", "qna")

        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(sourceResource.id, "qna")
        if (!gptDefaultCreateLog) {
            gptDefaultCreateLog = new GptDefaultCreateLog(
                    resId: qaResource.id,
                    promptType: "qna",
                    prompt: "Create Question & Answers",
                    response: "QA",
                    readingMaterialResId: sourceResource.id,
                    username: "System",
                    promptLabel: "Create Question & Answers"
            )
        } else {
            gptDefaultCreateLog.resId = qaResource.id
        }
        gptDefaultCreateLog.save(failOnError: true, flush: true)

        return qaResource
    }

    // ==================== QUESTION BANK BUILDER UTILITIES ====================

    /**
     * Loads and parses chapter metadata
     */
    private Map loadChapterMetadata(String folderPath, ChaptersMst chaptersMst, ResourceDtl resourceDtl) {
        File metadataFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/chapterMetadata${chaptersMst.id}.txt")
        String metadataString = metadataFile.text

        try {
            metadataString = jsonCleaner(metadataString)
            return new JsonSlurper().parseText(metadataString)
        } catch (Exception e) {
            println("Exception in questionBankBuilder metadata parsing: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in parsing metadata", metadataString)

            metadataString = fixJSONFromLLM(metadataString)
            return new JsonSlurper().parseText(metadataString)
        }
    }

    /**
     * Generates questions for a subtopic
     */
    private List<String> generateQuestionsForSubtopic(ResourceDtl resourceDtl, String fileContent, def subtopic, Prompts prompts, String serverIPAddress, int subTopicNumber, Long chapterId) {
        List<String> questions = []

        try {
            println("Question generation started for subtopic ${subTopicNumber} for chapter ${chapterId}")
            String subTopicText = jsonCleaner(subtopic.toString())
            String promptText = "${prompts.basePrompt} \n${subTopicText}"
            promptText = jsonCleaner(promptText)

            String response = processLLMResponseSafely(resourceDtl, fileContent, promptText, serverIPAddress)
            println("Got the response for subtopic ${subTopicNumber} for chapter ${chapterId}")

            if (response) {
                List responseList = parseJsonResponseSafely(response, "questionBankBuilder", resourceDtl)
                responseList.each { questionItem ->
                    String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                    if (questionText && !questions.contains(questionText)) {
                        questions.add(questionText)
                    }
                }
            }

            println("Total questions are ${questions.size()} for subtopic ${subTopicNumber} for chapter ${chapterId}")
        } catch (Exception e) {
            println("Exception in questionBankBuilder subtopic processing: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilder", "Exception in subtopic processing", e.toString())
        }

        return questions
    }

    /**
     * Generates MCQ questions for a subtopic
     */
    private List<String> generateMCQsForSubtopic(ResourceDtl resourceDtl, String fileContent, def subtopic, Prompts mcqPrompts, String serverIPAddress, int subTopicNumber, Long chapterId) {
        List<String> mcqQuestions = []

        try {
            String subTopicText = jsonCleaner(subtopic.toString())
            String promptText = "${mcqPrompts.basePrompt} \n${subTopicText}"
            println("Calling MCQ prompt for subtopic ${subTopicNumber} for chapter ${chapterId}")
            promptText = jsonCleaner(promptText)

            String response = processLLMResponseSafely(resourceDtl, fileContent, promptText, serverIPAddress)

            if (response) {
                List responseList = parseJsonResponseSafely(response, "questionBankBuilderMCQ", resourceDtl)
                println("Total MCQ questions are ${responseList.size()} for subtopic ${subTopicNumber} for chapter ${chapterId}")

                responseList.each { questionItem ->
                    String questionText = (questionItem instanceof String) ? questionItem : questionItem.question?.toString()
                    if (questionText) {
                        mcqQuestions.add(questionText)
                    }
                }
            }

            println("Total questions after MCQ are ${mcqQuestions.size()} for subtopic ${subTopicNumber} for chapter ${chapterId}")
        } catch (Exception e) {
            println("Exception in MCQ generation: ${truncateException(e)}")
            autogptErrorLoggerService.createLog(resourceDtl.chapterId, resourceDtl.id, "questionBankBuilderMCQ", "Exception in MCQ generation", e.toString())
        }

        return mcqQuestions
    }

    /**
     * Generates complete question bank for a chapter
     */
    private List<String> generateQuestionBank(ResourceDtl resourceDtl, ChaptersMst chaptersMst, params) {
        String extractPath = buildExtractPath(chaptersMst, resourceDtl)
        ensureVectorFileExists(params, extractPath)

        String folderPath = getFolderPath(extractPath)
        File questionBankFile = new File(grailsApplication.config.grails.basedir.path + "/${folderPath}/questionBank.txt")
        if (questionBankFile.exists()) {
            questionBankFile.delete()
        }

        // Load metadata
        Map metadata = loadChapterMetadata(folderPath, chaptersMst, resourceDtl)
        def subtopics = metadata.metadata.subtopics

        // Get prompts
        Prompts prompts = Prompts.findByPromptType("questionBankBuilder")
        Prompts mcqPrompts = Prompts.findByPromptType("questionBankBuilderMCQ")

        // Load file content
        String filePath = getFullFilePath(extractPath)
        File textFile = new File(filePath)
        String fileContent = textFile.text

        List<String> questionList = []
        int maxParallelTasks = 5

        println("Number of subtopics are ${subtopics.size()} for chapter ${chaptersMst.id}")

        // Prepare subtopic data for parallel processing
        List<Map> subtopicData = []
        subtopics.eachWithIndex { subtopic, index ->
            subtopicData.add([
                subtopic: subtopic,
                subtopicNumber: index + 1,
                promptText: jsonCleaner("${prompts.basePrompt} \n${jsonCleaner(subtopic.toString())}"),
                mcqPromptText: jsonCleaner("${mcqPrompts.basePrompt} \n${jsonCleaner(subtopic.toString())}")
            ])
        }

        // Process subtopics in chunks of 5 parallel tasks
        for (int i = 0; i < subtopicData.size(); i += maxParallelTasks) {
            int endIndex = Math.min(i + maxParallelTasks, subtopicData.size())
            List<Map> currentChunk = subtopicData.subList(i, endIndex)

            println("Processing ${currentChunk.size()} subtopics in parallel (${i + 1}-${endIndex} of ${subtopicData.size()})")

            // Create parallel tasks for regular questions
            def regularPromises = currentChunk.collect { data ->
                task {
                    return callLLMForQuestionGeneration(params.serverIPAddress, data.promptText, data.subtopicNumber, chaptersMst.id, "regular")
                }
            }

            // Create parallel tasks for MCQ questions
            def mcqPromises = currentChunk.collect { data ->
                task {
                    return callLLMForQuestionGeneration(params.serverIPAddress, data.mcqPromptText, data.subtopicNumber, chaptersMst.id, "MCQ")
                }
            }

            // Wait for all tasks in this chunk to complete
            def regularResults = regularPromises.collect { it.get() }
            def mcqResults = mcqPromises.collect { it.get() }

            // Process results sequentially (add to question list)
            regularResults.each { result ->
                if (result?.questions) {
                    questionList.addAll(result.questions)
                }
            }

            mcqResults.each { result ->
                if (result?.questions) {
                    questionList.addAll(result.questions)
                }
            }
        }

        println("Total questions are ${questionList.size()} for chapter ${chaptersMst.id}")

        // Add additional questions if needed
        if (questionList.size() < 200) {
            try {
                questionList = createAdditionalQuestions(resourceDtl, chaptersMst, questionList, params.serverIPAddress)
            } catch (Exception e) {
                println("Exception in createAdditionalQuestions for chapter ${chaptersMst.id}: ${truncateException(e)}")
            }
        }

        // Save question bank file
        questionBankFile.write(questionList.join("~~"))

        return questionList
    }

    /**
     * Generates and processes solutions with streaming approach to avoid memory buildup
     */
    private def generateAndProcessSolutionsStreaming(List<String> questionList, ResourceDtl resourceDtl, ChaptersMst chaptersMst, String serverIPAddress) {
        int noOfQuestionsPerIteration = 5
        int maxParallelTasks = 5
        int processedSolutions = 0

        // Initialize resources for question bank
        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        ResourceDtl mcqResourceDtl = null
        ResourceDtl qaResourceDtl = null

        // Extract database data upfront to avoid Hibernate session issues in async tasks
        String namespace = resourceDtl.vectorStored
        Long resId = resourceDtl.id
        Long chapterId = chaptersMst.id
        Long bookId = chaptersMst.bookId
        String customPrompt = buildCustomPrompt("solutionCreator", bookId, 1)

        // Prepare all batches
        List<Map> allBatches = []
        int currentIndex = 0
        while (currentIndex < questionList.size()) {
            int endIndex = Math.min(currentIndex + noOfQuestionsPerIteration, questionList.size())
            List batch = questionList.subList(currentIndex, endIndex)

            String inputQuestions = "The questions are \n"
            batch.eachWithIndex { question, index ->
                inputQuestions += "${currentIndex + index + 1}. ${question.replace("\"", "'")}\n"
            }
            inputQuestions = jsonCleaner(inputQuestions)

            allBatches.add([
                inputQuestions: inputQuestions,
                startIndex: currentIndex
            ])
            currentIndex = endIndex
        }

        // Process in chunks of 5 parallel tasks
        for (int i = 0; i < allBatches.size(); i += maxParallelTasks) {
            int endIndex = Math.min(i + maxParallelTasks, allBatches.size())
            List<Map> currentChunk = allBatches.subList(i, endIndex)

            println("Processing ${currentChunk.size()} solution batches in parallel (${i + 1}-${endIndex} of ${allBatches.size()})")

            // Create parallel tasks for this chunk
            def promises = currentChunk.collect { batchData ->
                task {
                    return callLLMForSolution(serverIPAddress, namespace, resId, chapterId, bookId, customPrompt, batchData.inputQuestions)
                }
            }

            // Wait for all tasks in this chunk to complete
            def results = promises.collect { it.get() }

            // Process results immediately instead of accumulating in memory
            results.each { result ->
                if (result?.answer) {
                    try {
                        // Process solution immediately
                        List jsonAnswerList = parseJsonResponseSafely(result.answer, "questionBankBuilder", resourceDtl)

                        jsonAnswerList.each { questionData ->
                            if (questionData.questionType == "MCQ") {
                                if (!mcqResourceDtl) {
                                    mcqResourceDtl = createMCQResource(resourceDtl)
                                }
                                saveQuestionToDatabase(questionData, mcqResourceDtl, booksMst, "MCQ")
                            } else {
                                if (!qaResourceDtl) {
                                    qaResourceDtl = createQAResource(resourceDtl)
                                }
                                saveQuestionToDatabase(questionData, qaResourceDtl, booksMst, "QA")
                            }
                        }
                        processedSolutions++
                    } catch (Exception e) {
                        println("Error processing solution batch: ${e.message}")
                    }
                }
            }

            // Clear session periodically to prevent memory buildup
            ObjectiveMst.withSession { session ->
                session.clear()
            }
        }

        return [processedSolutions: processedSolutions]
    }

    /**
     * Generates solutions for a list of questions (DEPRECATED - use generateAndProcessSolutionsStreaming)
     */
    private List<String> generateSolutionsForQuestions(List<String> questionList, ResourceDtl resourceDtl, ChaptersMst chaptersMst, String serverIPAddress) {
        List<String> solutionList = []
        int noOfQuestionsPerIteration = 5
        int maxParallelTasks = 5

        // Extract database data upfront to avoid Hibernate session issues in async tasks
        String namespace = resourceDtl.vectorStored

        Long resId = resourceDtl.id
        Long chapterId = chaptersMst.id
        Long bookId = chaptersMst.bookId
        String customPrompt = buildCustomPrompt("solutionCreator", bookId, 1)

        // Prepare all batches
        List<Map> allBatches = []
        int currentIndex = 0
        while (currentIndex < questionList.size()) {
            int endIndex = Math.min(currentIndex + noOfQuestionsPerIteration, questionList.size())
            List batch = questionList.subList(currentIndex, endIndex)

            String inputQuestions = "The questions are \n"
            batch.eachWithIndex { question, index ->
                inputQuestions += "${currentIndex + index + 1}. ${question.replace("\"", "'")}\n"
            }
            inputQuestions = jsonCleaner(inputQuestions)

            allBatches.add([
                inputQuestions: inputQuestions,
                startIndex: currentIndex
            ])
            currentIndex = endIndex
        }

        // Process in chunks of 5 parallel tasks
        for (int i = 0; i < allBatches.size(); i += maxParallelTasks) {
            int endIndex = Math.min(i + maxParallelTasks, allBatches.size())
            List<Map> currentChunk = allBatches.subList(i, endIndex)

            println("Processing ${currentChunk.size()} solution batches in parallel (${i + 1}-${endIndex} of ${allBatches.size()})")

            // Create parallel tasks for this chunk
            def promises = currentChunk.collect { batchData ->
                task {
                    return callLLMForSolution(serverIPAddress, namespace, resId, chapterId, bookId, customPrompt, batchData.inputQuestions)
                }
            }

            // Wait for all tasks in this chunk to complete
            def results = promises.collect { it.get() }

            // Process results sequentially (add to solution list)
            results.each { result ->
                if (result?.answer) {
                    solutionList.add(result.answer)
                }
            }
        }

        return solutionList
    }

    String fixJsonString(String inputJson) {
        // Replace LaTeX-specific sequences with properly escaped versions
        String fixedJson = inputJson
                .replace("\\\\\\\\", "FOURBACKSLASH")
                .replace("\\\\", "TWOBACKSLASH")
                .replace("\\", "ONEBACKSLASH")
        fixedJson = fixedJson.replace("FOURBACKSLASH", "\\\\")
                .replace("TWOBACKSLASH", "\\\\")
                .replace("ONEBACKSLASH", "\\\\")
        return fixedJson;

    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def storePdfVectors(params){
        def chapterId = params.chapterId
        if(params.resId==null){
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(""+chapterId),"Notes", [sort: "id", order: "asc"])
            if(readingMaterials.size()>0)
                params.put("resId",""+readingMaterials[0].id)
            else {
                def json = [status: "Error", message: "No PDF found for this chapter"]
                return json
            }
        }
        def resId = params.resId
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))
        if (documentInstance == null) {
            def json = [status: "Error", message: "Document not found."]
            return json

        } else {
            try {
                String namespace
                int resCode
                if(documentInstance.vectorStored==null||documentInstance.extractPath==null||"".equals(documentInstance.extractPath)) {
                    println("have to create vectors " + chapterId)
                    File pdfFile = new File(grailsApplication.config.grails.basedir.path + "/" + documentInstance.resLink)
                    if(!pdfFile.exists()){
                        println("pdf file does not exist for chapter id " + chapterId)
                        // get the content of the directory and see if there is any pdf file in it. if yes, then use that file.
                        File dir = new File(grailsApplication.config.grails.basedir.path + "/" + documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf("/")))
                        dir.listFiles().each { file ->
                            if(file.name.endsWith(".pdf")){
                                println("found pdf file " + file.name+" for chapter id " + chapterId)
                                documentInstance.resLink = documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf("/"))+"/"+file.name
                                documentInstance.save(failOnError: true, flush: true)
                                pdfFile = file
                            }
                        }

                    }


                    String index = promptService.getIndex("users")
                    namespace = index + "_" + chapterId + "_" + resId
                    String filePath = documentInstance.resLink
                    resCode = newUserPDFCreation( filePath, namespace,params.serverIPAddress)

                }else{
                    namespace = documentInstance.vectorStored
                    resCode = 200
                }
                def res = ["status":"OK","message":"PDF Vectors stored successfully","resCode":resCode,namespace: namespace,resId:documentInstance.id]
                return res
            }catch(Exception e){
                println("Exception in storePdfVectors: for chapter " + chapterId + " and the exception is " + e.toString())
                def err = ["status":"Error","message":e.message]
                return err
            }
        }
    }

    def newUserPDFCreation(filePath,namespace,String serverIPAddress){
        URL url = new URL("http://"+serverIPAddress+":8000/api"+"/processPDFVectorNew")
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

        def json = new JsonBuilder([namespace: namespace, filePath: filePath])
        writer.write(json.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        return responseCode
    }





    @Transactional(propagation = Propagation.REQUIRES_NEW)

    int getNextChapterForProcessing(int serverIndex,String serverNo){
        println("getNextChapterForProcessing for server " + serverNo)
        if(serverNo==null) serverNo = "0"
        KeyValueMst runGPTJob = KeyValueMst.findByKeyName("runGPTJob")
        if(runGPTJob!=null&&runGPTJob.keyValue.equals("true")) {
            int noOfParallelTasks = 3
            KeyValueMst keyValueMst = KeyValueMst.findByKeyName("numberOfParallelAutoGPTTasks")
            if (keyValueMst != null) {
                String[] noOfParallelTasksStr = keyValueMst.keyValue.split(",")
                if(serverNo!=null&&noOfParallelTasksStr.length>Integer.parseInt(serverNo)) noOfParallelTasks = Integer.parseInt(noOfParallelTasksStr[Integer.parseInt(serverNo)])
                else noOfParallelTasks = Integer.parseInt(noOfParallelTasksStr[0])

            }
            def autogptLogs = AutogptLog.findAllByGptStatusAndServerIndexAndServerNumber("running", serverIndex, serverNo)
            if (autogptLogs.size() < noOfParallelTasks) {
                String sql = " SELECT id " +
                        " FROM wslog.autogpt_log " +
                        " WHERE gpt_status = 'vectorCreated' " +
                        " ORDER BY CASE " +
                        "   WHEN higher_priority = 'true' THEN 1 " +
                        "   WHEN higher_priority = 'false' THEN 2 " +
                        "   ELSE 3 END, id ASC " +
                        " LIMIT 1";
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                def sql1 = new SafeSql(dataSource)
                def results = sql1.rows(sql)
                if(results.size()>0){
                    AutogptLog autogptLog = AutogptLog.findById(new Long(""+results[0].id))
                    autogptLog.gptStatus = "running"
                    autogptLog.dateStarted = new Date()
                    autogptLog.dateCompleted = null
                    autogptLog.serverIndex = serverIndex
                    autogptLog.serverNumber = serverNo
                    autogptLog.save(failOnError: true, flush: true)
                    return autogptLog.chapterId.intValue()
                }
                else {
                    return 0
                }
            }
            else {
                return 0
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)

    int getNextChapterForPDFProcessing(int serverIndex){

        KeyValueMst runGPTJob = KeyValueMst.findByKeyName("runGPTJob")
        if(runGPTJob!=null&&runGPTJob.keyValue.equals("true")) {

            AutogptLog autogptLog = AutogptLog.findByGptStatusAndServerIndex("vectorCreating", serverIndex)
            if(autogptLog==null) {

                String sql = " SELECT id " +
                        " FROM wslog.autogpt_log " +
                        " WHERE gpt_status IS NULL " +
                        " ORDER BY CASE " +
                        "   WHEN higher_priority = 'true' THEN 1 " +
                        "   WHEN higher_priority = 'false' THEN 2 " +
                        "   ELSE 3 END, id ASC " +
                        " LIMIT 1";
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                def sql1 = new SafeSql(dataSource)
                def results = sql1.rows(sql)
                if (results.size() > 0) {
                    autogptLog = AutogptLog.findById(new Long("" + results[0].id))
                    autogptLog.gptStatus = "vectorCreating"
                    autogptLog.dateStarted = new Date()
                    autogptLog.serverIndex = serverIndex
                    autogptLog.save(failOnError: true, flush: true)
                    return autogptLog.chapterId.intValue()
                } else {
                    return 0
                }

            }else{
                return 0
            }
        }
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def deleteEmbeddings(ResourceDtl resourceDtl) {
        if (resourceDtl.vectorStored != null) {
            JSONObject requestBody = new JSONObject()

            requestBody.put("namespace", resourceDtl.vectorStored)
            URL url = new URL(grailsApplication.config.grails.aiserver.url + "/delete-namespace")

            HttpURLConnection connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)
            connection.connect()
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            writer.write(requestBody.toString())
            writer.flush()
            writer.close()
            def responseCode = connection.getResponseCode()
            if (responseCode == 200) {
                resourceDtl.vectorStored = null
                resourceDtl.save(failOnError: true, flush: true)
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = reader.readLine()
                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            } else {
                return null
            }

        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def checkPendingJobs(){
        String sql  = " SELECT id " +
                " FROM wslog.autogpt_log\n" +
                " WHERE TIMESTAMPDIFF(HOUR, date_started, SYSDATE()) > 3\n" +
                " and gpt_status='running'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        results.each { log ->
            AutogptLog autogptLog = AutogptLog.findById(new Long(log.id))
            //delete
            autogptLog.delete(flush: true)
        }
    }

    def getChapterResources(String bookId){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if(booksMst.packageBookIds!=null) bookId = booksMst.packageBookIds

        List chapters = new ArrayList()
        String[] bookIds = bookId.split(",")
        for(int i=0;i<bookIds.length;i++){
            String tempBookId = bookIds[i]
            BooksDtl booksDtl = BooksDtl.findByBookId(new Integer(tempBookId))
            if(booksDtl!=null&&booksDtl.masterBookId!=null) tempBookId = ""+booksDtl.masterBookId
            chapters.addAll(ChaptersMst.findAllByBookId(new Integer(tempBookId)))
        }

        List chapterDetails = []
        chapters.each { chapter ->
            int noOfExercises = 0
            int noOfQB = 0
            int noOfQBMCQs = 0
            int noOfPYQs = 0
            String hasTheory = "No"
            int noOfUnanswered = 0

            ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","Exercise Solutions")
            if(resourceDtl!=null) {
                noOfExercises = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
            }

            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"Multiple Choice Questions","QuestionBank MCQs")
            if(resourceDtl!=null) {
                noOfQBMCQs = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
                noOfUnanswered += ObjectiveMst.countByQuizIdAndAnswerDescriptionIsNull(new Integer(resourceDtl.resLink));
            }
            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","QuestionBank QnA")
            if(resourceDtl!=null) {
                noOfQB = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
                noOfUnanswered += ObjectiveMst.countByQuizIdAndAnswerIsNull(new Integer(resourceDtl.resLink));
            }

            //check if PYQs are created - separate count from Question Bank questions
            resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndResourceName(new Integer(""+chapter.id),"QA","PYQs")
            if(resourceDtl!=null) {
                noOfPYQs = ObjectiveMst.countByQuizId(new Integer(resourceDtl.resLink));
                noOfUnanswered += ObjectiveMst.countByQuizIdAndAnswerIsNull(new Integer(resourceDtl.resLink));
            }

            // Check if chapter file exists for Theory column
            // Logic from wpmain/getChapterContent - check if chapterFilePath exists
            String chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chapter.bookId + "/chapter_" + chapter.id + ".html"
            File chapterFile = new File(chapterFilePath)
            if (chapterFile.exists()) {
                hasTheory = "Yes"
            }


            chapterDetails << [chapterId: chapter.id, chapterName: chapter.name, noOfExercises: noOfExercises, noOfQB: noOfQB, noOfQBMCQs: noOfQBMCQs, noOfPYQs: noOfPYQs,
                               hasTheory: hasTheory, noOfUnanswered: noOfUnanswered]
        }
        return chapterDetails
    }

    def fixFormulas(String input) {
        return input
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def updateAutoGPTLog(Long chapterId,String gptStatus,String highPriority=null){
        AutogptLog autogptLog = AutogptLog.findByChapterId(chapterId)
        if(autogptLog!=null){
            if("vectorCreated".equals(gptStatus)) {
                //update attempts
                if(autogptLog.attempts==null) autogptLog.attempts = 0
                autogptLog.attempts = autogptLog.attempts + 1
            }
            autogptLog.gptStatus = gptStatus
            if(highPriority!=null) {
                autogptLog.higherPriority = highPriority
            }
            autogptLog.dateCompleted = new Date()
            autogptLog.save(failOnError: true, flush: true)
        }
        AutogptLog.withSession { session ->
            session.clear()
        }
    }

    def fixCurlyBrackets(String input) {
        if (!input) return input

        // Replace curly brackets that might interfere with JSON parsing
        return input
                .replace("{", "LEFTBRACE")
                .replace("}", "RIGHTBRACE")
    }

    def fixQuestion(String objId,String questionType){
        ObjectiveMst objectiveMst = ObjectiveMst.findById(new Integer(objId))
        Prompts prompts = Prompts.findByPromptType("questionFixer_"+questionType)


        String prompt
        if("mcq".equals(questionType)){
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n option1:\n"+objectiveMst.option1+"\n"+
                    "\n option2:\n"+objectiveMst.option2+"\n"+
                    "\n option3:\n"+objectiveMst.option3+"\n"+
                    "\n option4:\n"+objectiveMst.option4+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }else{
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+"\n"+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }

        def fixResponse = getQuestionFix(prompt, objId)
        def jsonResponse = new JsonSlurper().parseText( jsonCleaner(fixResponse.response))
        if(jsonResponse!=null){
            if("mcq".equals(questionType)){
                objectiveMst.question = jsonResponse.question
                objectiveMst.option1 = jsonResponse.option1
                objectiveMst.option2 = jsonResponse.option2
                objectiveMst.option3 = jsonResponse.option3
                objectiveMst.option4 = jsonResponse.option4
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.correctAnswer

                // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
                String correctAnswerStr = jsonResponse.correctAnswer.toString()
                objectiveMst.answer1 = (correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")) ? "Yes" : null
                objectiveMst.answer2 = (correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")) ? "Yes" : null
                objectiveMst.answer3 = (correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")) ? "Yes" : null
                objectiveMst.answer4 = (correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")) ? "Yes" : null
            }else{
                objectiveMst.question = jsonResponse.question
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.answer
            }

            objectiveMst.save(failOnError: true, flush: true)

        }
        return objectiveMst
    }

    def getQuestionFix(String questionInput,String objId){
        JSONObject requestBody = new JSONObject()
        requestBody.put("prompt", questionInput)
        URL url = new URL(grailsApplication.config.grails.aiserver.url+"/chat-completion")

        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            try{
                def jsonResponse = jsonSlurper.parseText(response)
                return jsonResponse
            }catch (Exception e){
                println("Exception in getQuestionFix: "+e.toString().length()>1000?e.toString().substring(0,1000):e.toString())
                return null
            }

        }else{
            println("Error in getQuestionFix: "+responseCode)
            return null
        }

    }

    String fixJSONFromLLM(String input) {
        Prompts prompts = Prompts.findByPromptType("jsonFixer")
        String prompt = prompts.basePrompt + " \n "+input
        String output = getQuestionFix(prompt, "-1").response
        return output
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def runAutoGPT(int chapterId,String serverIPAddress) {
        ChaptersMst chaptersMst = ChaptersMst.findById(new Long(chapterId))
        AutogptLog autogptLog = AutogptLog.findByChapterId(chapterId)
        boolean restartRequired = false;
        String restartReason=""
        def params = new HashMap()
        params.put("serverIPAddress", serverIPAddress)
        params.put("chapterId", "" + chapterId)
        ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes")

        if (resourceDtl != null) {
            params.put("resId", "" + resourceDtl.id)
        } else {
            def json = [status: "Error", message: "No PDF found for this chapter"]
            return json
        }


        if("true".equals(autogptLog.fixQuestions)){
            println("runAutoGPT for chapter id " + chapterId + " on server " + serverIPAddress + " to fix questions")
            processUnansweredQuestionsForChapter(new Long(chapterId), serverIPAddress)
        }else {
            println("runAutoGPT for chapter id " + chapterId + " on server " + serverIPAddress)
            boolean theoryPresent = false
            String chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chaptersMst.bookId + "/chapter_" + chapterId + ".html"
            File chapterFile = new File(chapterFilePath)
            if (chapterFile.exists()) {
                theoryPresent = true
            }

            println("Running getChapterMetaData for chapter id " + chapterId + " on server " + serverIPAddress)
            getChapterMetaData(params)

            if ("true".equals(autogptLog.createSnapshot)) {
                if (!theoryPresent) {
                    params.put("subjectSyllabus", autogptLog.subjectSyllabus)
                    params.put("boardExam", autogptLog.boardExam)
                    theoryBooksService.getPYQsForBoard(params)
                }
            }
            println("Running exerciseCollector for chapter id " + chapterId + " on server " + serverIPAddress)
            def response = exerciseCollector(params)
            println("exerciseCollector response: " + response + " for chapter id " + chapterId + " on server " + serverIPAddress)
            def response1 = questionBankBuilder(params)
            println("questionBankBuilder response: " + response1 + " for chapter id " + chapterId + " on server " + serverIPAddress)

            // let us check if the theory is created for the chapter
            if ("true".equals(autogptLog.createSnapshot)) {

                // Chapter content exists, get the content
                chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chaptersMst.bookId + "/chapter_" + chapterId + ".html"
                chapterFile = new File(chapterFilePath)
                if (!chapterFile.exists()) {
                    restartRequired = true
                    restartReason = "Theory not created"
                } else {
                    //check if PYQs is present
                    ResourceDtl pyqResourceDtl = ResourceDtl.findByChapterIdAndResourceName(chapterId, "PYQs")
                    if (pyqResourceDtl == null) {
                        restartRequired = true;
                        restartReason = "PYQs not created"
                    } else {
                        ObjectiveMst.withSession { session ->
                            session.clear()
                        }
                        //get number of questions which does not have answer
                        int noOfQuestionsWithoutAnswer = ObjectiveMst.countByQuizIdAndAnswerIsNull(new Integer(pyqResourceDtl.resLink))
                        if (noOfQuestionsWithoutAnswer > 5) {
                            restartRequired = true;
                            restartReason = "PYQs not answered"
                        } else if (noOfQuestionsWithoutAnswer > 0) {
                            restartRequired = false;
                            restartReason = "PYQs partially answered for chapter " + chapterId + " and no of questions without answer are " + noOfQuestionsWithoutAnswer
                        }
                    }
                };
            }
        }
        if (restartRequired && autogptLog != null && autogptLog.attempts != null && autogptLog.attempts < 3) {

            updateAutoGPTLog(new Long("" + chapterId), "vectorCreated")
            //let us add the reason to the log
            autogptErrorLoggerService.createLog(new Long("" + chapterId), null, "runAutoGPT", "Restarting AutoGPT for chapter " + chapterId + " because " + restartReason, "")
        } else {
            deleteEmbeddings(resourceDtl)
            updateAutoGPTLog(new Long("" + chapterId), "completed")
            autogptLog = AutogptLog.findByChapterId(chapterId)
            try {
                chapterId = getNextChapterForProcessing(autogptLog.serverIndex.intValue(), autogptLog.serverNumber)
                if (chapterId > 0) {
                    println("Running AutoGPT for chapter id after completing the old one " + chapterId + " on server " + params.serverIPAddress)
                    asyncLogsService.runAutoGPT(chapterId, params.serverIPAddress)
                }
            } catch (Exception e) {
                println("Exception in runAutoGPT: for chapter " + chapterId + " on server " + autogptLog.serverIndex + " is " + e.toString())
            }
        }
        def json = [status: "OK", message: "AutoGPT task completed"]
        return json
    }


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    def autoGPTPdfProcessorRunner(params) {
        try {
            println("Running autoGPTPdfProcessorRunner for chapter id " + params.chapterId)
            def json = storePdfVectors(params)
            AutogptLog autogptLog = AutogptLog.findByChapterId(new Long(""+params.chapterId))
            updateAutoGPTLog(new Long("" + params.chapterId), "vectorCreated")
            int chapterId = getNextChapterForPDFProcessing(autogptLog.serverIndex.intValue())
            if (chapterId > 0) {
                params.put("chapterId", "" + chapterId)
                ResourceDtl resourceDtl = ResourceDtl.findByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId), "Notes")
                if (resourceDtl != null) {
                    params.put("resId", "" + resourceDtl.id)
                    println("Running autoGPTPdfProcessorRunner for chapter id after the previous one completed " + chapterId + " on server " + autogptLog.serverIndex)
                    asyncLogsService.autoGPTPdfProcessorRunner(params)
                } else {
                    println("No PDF found for chapter id " + chapterId + " so we will not run autoGPTPdfProcessorRunner for this chapter")
                }

            }
        } catch (Exception e) {
            println("Exception in autoGPTPdfProcessorRunner: " + e.toString())
        }
        return "completed"
    }

    def fixLanguage(String objId,String questionType,String language){
        ObjectiveMst objectiveMst = ObjectiveMst.findById(new Integer(objId))
        Prompts prompts = Prompts.findByPromptType("languageCorrections_"+questionType)


        String prompt
        if("mcq".equals(questionType)){
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n option1:\n"+objectiveMst.option1+"\n"+
                    "\n option2:\n"+objectiveMst.option2+"\n"+
                    "\n option3:\n"+objectiveMst.option3+"\n"+
                    "\n option4:\n"+objectiveMst.option4+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }else{
            prompt = prompts.basePrompt + " \n question:\n"+objectiveMst.question+"\n"+
                    "\n answer:\n"+objectiveMst.answer+"\n"+
                    "\n answer Explanation:\n"+objectiveMst.answerDescription+"\n"
        }
        prompt = prompt.replaceAll("BOOKLANGUAGE", language)

        def fixResponse = getQuestionFix(prompt, objId)
        def jsonResponse = new JsonSlurper().parseText( jsonCleaner(fixResponse.response))
        if(jsonResponse!=null){
            if("mcq".equals(questionType)){
                objectiveMst.question = jsonResponse.question
                objectiveMst.option1 = jsonResponse.option1
                objectiveMst.option2 = jsonResponse.option2
                objectiveMst.option3 = jsonResponse.option3
                objectiveMst.option4 = jsonResponse.option4
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.correctAnswer

                // Handle correctAnswer as either Integer (1,2,3,4) or String ("option1","option2","option3","option4")
                String correctAnswerStr = jsonResponse.correctAnswer.toString()
                objectiveMst.answer1 = (correctAnswerStr.equals("option1") || correctAnswerStr.equals("1")) ? "Yes" : null
                objectiveMst.answer2 = (correctAnswerStr.equals("option2") || correctAnswerStr.equals("2")) ? "Yes" : null
                objectiveMst.answer3 = (correctAnswerStr.equals("option3") || correctAnswerStr.equals("3")) ? "Yes" : null
                objectiveMst.answer4 = (correctAnswerStr.equals("option4") || correctAnswerStr.equals("4")) ? "Yes" : null
            }else{
                objectiveMst.question = jsonResponse.question
                objectiveMst.answerDescription = jsonResponse.answerDescription
                objectiveMst.answer = jsonResponse.answer
            }

            objectiveMst.save(failOnError: true, flush: true)

        }
        return objectiveMst
    }



}
