package com.wonderslate.data

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ibookso.products.CategoryLevel1
import com.ibookso.products.CategoryLevel2
import com.ibookso.products.CategoryLevel3
import com.ibookso.products.CategoryLevel4
import com.ibookso.products.ExtPublishers
import com.ibookso.products.PrintBooksMst
import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.InstituteMst
import com.wonderslate.publish.Blogs
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.Publishers
import com.wonderslate.seo.UrlMapping
import com.wonderslate.shop.WsshopService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import java.time.YearMonth

@Transactional
class SitemapService {
    DataProviderService dataProviderService
    def grailsApplication
    def redisService
    WsshopService wsshopService
    static noOfStorePageLinks=0
    PrintBooksService printBooksService
    PrepjoyService prepjoyService

    def sitemap(url) {

        if (!"true".equals(redisService.("siteMapBuilding"))) {
            redisService.("siteMapBuilding")="true"

            String serverBaseUrlIndex = url
            String serverBaseUrl = "https://www.wonderslate.com"
            File sitemapIndex = new File("upload/sitemapIndex.xml")
            if (sitemapIndex.exists()) sitemapIndex.delete()
            sitemapIndex << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                    "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n" +
                    "  <sitemap>\n" +
                    "    <loc>https://www.wonderslate.com/statistics/sitemap.txt</loc>\n" +
                    "  </sitemap>\n"

            File sitemap = new File("upload/sitemap.txt")
            String desc
            if (sitemap.exists()) sitemap.delete()
            sitemap << "https://www.wonderslate.com\n"
            sitemap << "https://www.wonderslate.com/ebooks\n"
            sitemap << "https://www.wonderslate.com/funlearn/termsandconditions\n"
            sitemap << "https://www.wonderslate.com/funlearn/privacy\n"
            sitemap << "https://www.wonderslate.com/creation/returnPolicy\n"
            sitemap << "https://www.wonderslate.com/books/faq\n"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)




            def sites = SiteMst.findAllByDisplayInMainSite("Y")
            List siteList = []
            sites.each { site ->
                siteList << site.id
            }

            String siteListString = "";
            sites.each { site ->
                siteListString = siteListString + site.id + ","
            }
            siteListString = siteListString.substring(0, siteListString.length() - 1)

            //site map for chapters
            File sitemapChapters
            File sitemapPrepjoyChapters

            int index = 0
            int maxRecords = 20000
            int numberOfRecords = 0
            //print book
            File eBooks
            File prepjoyEbooks
            println("siteListString is "+siteListString)
            //first for book details page.
            String sql = "SELECT bm.title,bm.id,bm.isbn,bm.testTypeBook FROM BooksMst bm " +
                    " where bm.status='published' and bm.siteId in (" + siteListString + ")  order by title"

            List books = BooksMst.executeQuery(sql)
            println("number of books "+books.size())
            books.each { book ->
                if (numberOfRecords > maxRecords || numberOfRecords == 0) {
                    numberOfRecords = 1;
                    index++
                    eBooks = new File("upload/sitemapeBooks" + index + ".txt")
                    if (eBooks.exists()) eBooks.delete()
                    sitemapIndex << "<sitemap>\n" +
                            "    <loc>https://www.wonderslate.com/statistics/sitemapeBooks" + index + ".txt</loc>\n" +
                            "  </sitemap>\n"
                }
                if(book[3]!=null && "true".equals(book[3]))
                    eBooks << "https://www.wonderslate.com/wpmain/aiBookDtl?siteName=books&bookId=${book[1]}&preview=true\n"
                else
                    eBooks << "https://www.wonderslate.com/${("" + book[0]).replaceAll(' ', '-').replaceAll("--", "-").replaceAll("\\.",'').replaceAll('#','').replaceAll('%','').toLowerCase()}/ebook-details?siteName=books&bookId=${book[1]}&preview=true\n"

                numberOfRecords++;
            }

            //level urls
            createCategoriesSiteMap( siteListString,"wonderslateCategories.txt",null,"https://www.wonderslate.com",null,null,"/books/")
            sitemapIndex << "<sitemap>\n" +
                    "    <loc>https://www.wonderslate.com/statistics/sitemapwonderslateCategories.txt</loc>\n" +
                    "  </sitemap>\n"

            //publisher urls
            numberOfRecords = 0

            index = 0;
            sql = "SELECT p.name,p.id,p.name,p.site_title FROM wsshop.publishers p, books_mst bm " +
                    "where bm.site_id in (" + siteListString + ") and bm.status='published' and bm.publisher_id=p.id\n" +
                    "group by p.id order by p.name;"
            dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)


            results.each { publisher ->
                if (numberOfRecords > maxRecords || numberOfRecords == 0) {
                    numberOfRecords = 1;
                    index++
                    sitemapChapters = new File("upload/sitemapPublisher" + index + ".txt")
                    if (sitemapChapters.exists()) sitemapChapters.delete()
                    sitemapIndex << "<sitemap>\n" +
                            "    <loc>https://www.wonderslate.com/statistics/sitemapPublisher" + index + ".txt</loc>\n" +
                            "  </sitemap>\n"
                }

                sitemapChapters << "https://www.wonderslate.com/${("" + publisher[0]).replaceAll(' ', '-').replaceAll("--", "-")}/publisher/${publisher.id}\n"
                numberOfRecords++;
                noOfStorePageLinks++
                //level urls
                createCategoriesSiteMap( siteListString,"wonderslatePublisher"+publisher.id+".txt",""+publisher.id,"https://www.wonderslate.com",publisher.name,publisher.site_title,"/books/")
                sitemapIndex << "<sitemap>\n" +
                        "    <loc>https://www.wonderslate.com/statistics/sitemapwonderslatePublisher"+publisher.id+".txt</loc>\n" +
                        "  </sitemap>\n"
                createCategoriesSiteMap( siteListString,"prepjoyPublisher"+publisher.id+".txt",""+publisher.id,"https://www.prepjoy.com",publisher.name,publisher.site_title,"/books/prepjoy/")
            }

            createGradeBooksSiteMap("https://www.wonderslate.com","books",new Long(1))

            sitemapIndex << "</sitemapindex>"
            //siteMaps for whitelabels
            List whiteLabelSites = SiteMst.findAllBySiteDomainNameIsNotNullAndDisplayInMainSite("Y")
            int whiteLabelChapterIndex=0
            File sitemapWhiteLabelIndex = new File("upload/sitemapWhitelabelIndex.xml")
            if (sitemapWhiteLabelIndex.exists()) sitemapWhiteLabelIndex.delete()
            sitemapWhiteLabelIndex << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                    "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n"
            String flexiPath=""
            whiteLabelSites.each { siteMst ->
                if(siteMst.id.intValue()!=1&&siteMst.id.intValue()!=27) {
                    if ("true".equals(siteMst.commonWhiteLabel)) flexiPath = "/books/sp/" + siteMst.siteName + "/"
                    else flexiPath = "/books/" + siteMst.siteName + "/"
                    generateWhiteLabelSiteMap(siteMst.id, url, siteMst.siteName, "https://" + siteMst.siteDomainName, siteMst.commonWhiteLabel)

                    sitemapWhiteLabelIndex << "  <sitemap>\n" +
                            "    <loc>https://www.wonderslate.com/statistics/sitemap" + siteMst.siteName + ".txt</loc>\n" +
                            "  </sitemap>\n"
                    createCategoriesSiteMap("" + siteMst.id, siteMst.siteName + "Categories.txt", null, "https://" + siteMst.siteDomainName, null, null,flexiPath)
                    sitemapWhiteLabelIndex << "  <sitemap>\n" +
                            "    <loc>https://www.wonderslate.com/statistics/sitemap" + siteMst.siteName + "Categories.txt</loc>\n" +
                            "  </sitemap>\n"
                    createGradeBooksSiteMap("https://"+siteMst.siteDomainName,siteMst.siteName,siteMst.id)
                    sitemapWhiteLabelIndex << "  <sitemap>\n" +
                            "    <loc>https://www.wonderslate.com/statistics/sitemapGradeBooks" + siteMst.siteName + ".txt</loc>\n" +
                            "  </sitemap>\n"
                }

            }

            sitemapWhiteLabelIndex << "</sitemapindex>"

            createGradeAndSyllabusSiteMap("https://www.wonderslate.com")
            redisService.("siteMapBuilding")=null
            KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("noOfStorePageLinks",new Integer(1))
            if(keyValueMst==null) keyValueMst = new KeyValueMst(siteId: new Integer(1),keyName: "noOfStorePageLinks",keyValue: (""+noOfStorePageLinks))
            else keyValueMst.keyValue=""+noOfStorePageLinks
            keyValueMst.save(failOnError: true, flush: true)

        }

    }




    def generatePrintPublishersSiteMap(String url){
        if (!"true".equals(redisService.("printSiteMapBuilding"))) {
            redisService.("printSiteMapBuilding") = "true"




            File sitemapIndex = new File("upload/sitemapPrintIndex.xml")
            if (sitemapIndex.exists()) sitemapIndex.delete()
            sitemapIndex << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                    "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n"
            File sitemapPrepjoyIndex = new File("upload/sitemapPrintPrepjoyIndex.xml")
            if (sitemapPrepjoyIndex.exists()) sitemapPrepjoyIndex.delete()
            sitemapPrepjoyIndex << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                    "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n"

     //print book publishers
            KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("printPublisherLastFileIndex", new Integer(1))
            int numberOfRecords = 0
            int index = keyValueMst!=null?Integer.parseInt(keyValueMst.keyValue):1;
            int maxRecords = 5000

            for(int i=1;i<=(index-1);i++){
                sitemapIndex << "<sitemap>\n" +
                        "    <loc>https://www.wonderslate.com/statistics/sitemapPrintPublishers" + i +".txt</loc>\n" +
                        "  </sitemap>\n"
                sitemapPrepjoyIndex << "<sitemap>\n" +
                        "    <loc>https://www.prepjoy.com/statistics/sitemapPrepjoyPrintPublishers" + i +".txt</loc>\n" +
                        "  </sitemap>\n"
            }

            String sql = "SELECT ep.id ,ep.name FROM ExtPublishers ep where id >" + ((index - 1)*maxRecords) + " order by id asc"
            List printPublishers = ExtPublishers.executeQuery(sql)

            File prepjoyPrintPublishers
            File sitemapChapters
            index--;
            printPublishers.each { publisher ->

                if (numberOfRecords > maxRecords || numberOfRecords == 0) {
                    numberOfRecords = 1;
                    index++

                    sitemapChapters = new File("upload/sitemapPrintPublishers" + index +".txt")
                    prepjoyPrintPublishers = new File("upload/sitemapPrepjoyPrintPublishers" + index +".txt")
                    if (sitemapChapters.exists()) sitemapChapters.delete()
                    sitemapIndex << "<sitemap>\n" +
                            "    <loc>https://www.wonderslate.com/statistics/sitemapPrintPublishers" + index +".txt</loc>\n" +
                            "  </sitemap>\n"
                    if (prepjoyPrintPublishers.exists()) prepjoyPrintPublishers.delete()
                    sitemapPrepjoyIndex << "<sitemap>\n" +
                            "    <loc>https://www.prepjoy.com/statistics/sitemapPrepjoyPrintPublishers" + index +".txt</loc>\n" +
                            "  </sitemap>\n"

                    //add file index
                    keyValueMst = KeyValueMst.findByKeyNameAndSiteId("printPublisherLastFileIndex", new Integer(1))
                    if (keyValueMst == null) {
                        keyValueMst = new KeyValueMst(keyName: "printPublisherLastFileIndex", keyValue: "" + index, siteId: new Integer(1))
                    } else {
                        keyValueMst.keyValue = "" + index
                    }
                    keyValueMst.save(failOnError: true, flush: true)

                }

                sitemapChapters << "https://www.wonderslate.com/${publisher[1].replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/seller/${publisher[0]}\n"
                prepjoyPrintPublishers << "https://www.prepjoy.com/${publisher[1].replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/seller/${publisher[0]}\n"
                numberOfRecords++
                //now lets get categories for the publisher
                if(redisService.("publisherLeafCategories_"+publisher[0])==null) printBooksService.getPublisherLeafCategories(""+publisher[0])
                String categoryString = redisService.("publisherLeafCategories_"+publisher[0])
                String[] categories =  categoryString.split(',')
                String[] tempCategory
                String tempCategoryName,tempCategoryId
                HashMap tempMap = new HashMap()

                for(int i=0;i<categories.length;i++) {
                    try {
                        if (tempMap.get(categories[i]) == null) {
                            tempMap.put(categories[i], categories[i])
                            if (!"".equals(categories[i]) && categories[i].indexOf('_') > -1) {
                                tempCategory = categories[i].split('_')
                                if (tempCategory.size() > 2) {
                                    tempCategoryName = tempCategory[1].replaceAll(' ', '-').toLowerCase()
                                    tempCategoryId = tempCategory[2]
                                    sitemapChapters << "https://www.wonderslate.com/${publisher[1].replaceAll(' ', '-').replaceAll('/', '-').toLowerCase() + "-" + tempCategoryName}/seller/${publisher[0]}/${tempCategoryId}\n"
                                    prepjoyPrintPublishers << "https://www.prepjoy.com/${publisher[1].replaceAll(' ', '-').replaceAll('/', '-').toLowerCase() + "-" + tempCategoryName}/seller/${publisher[0]}/${tempCategoryId}\n"

                                }
                            }
                        }
                    }catch(Exception e){
                        println("*** print category creation exception for "+categories[i]+" and the exception is ${e.toString()}")
                    }
                }
            }

          println("why isn't this added")
            sitemapIndex << "</sitemapindex>"
            sitemapPrepjoyIndex << "</sitemapindex>"
            redisService.("printSiteMapBuilding") = "null"
        }
    }



      def generateWhiteLabelSiteMap(siteId,url,siteName,serverBaseUrl,commonWhiteLabel){

          File sitemap = new File("upload/sitemap"+siteName+".txt")
        String desc
        if(sitemap.exists()) sitemap.delete()
          sitemap << serverBaseUrl+"\n"
          if("true".equals(commonWhiteLabel))
              sitemap << serverBaseUrl+"/sp/"+siteName+"/store\n"
              else
          sitemap << serverBaseUrl+"/"+siteName+"/store\n"

          String siteListString=""+siteId;

        //first for book details page.
        String  sql = "SELECT bm.title,bm.id,bm.isbn,bm.testTypeBook FROM BooksMst bm " +
                " where bm.status='published' and bm.siteId in ("+siteListString+")  order by title"

        List books = BooksMst.executeQuery(sql)
        String itemName =""

        books.each{ book ->
            itemName = (""+book[0]).replaceAll(' ', '-').replaceAll("--","-").replaceAll("\\.",'').replaceAll('#','').replaceAll('%','').replaceAll('/','-').toLowerCase()
            if(book[3]!=null && "true".equals(book[3]))
                sitemap << serverBaseUrl+"/wpmain/aiBookDtl?siteName="+siteName+"&bookId=${book[1]}&preview=true\n"
            else
            sitemap << serverBaseUrl+"/wpmain/aiBookDtl?siteName="+siteName+"&bookId=${book[1]}&preview=true\n"

            if(book[2]!=null&&!"".equals(book[2])) {
                sitemap << serverBaseUrl + "/isbn/${book[2]}?siteName=${siteName}&bookTitle=${itemName}\n"
            }
        }

    }

    HashMap getSEO(params,Integer siteId){
        boolean publisherPresent=false, levelsPresent=false
        boolean wsSite = true
        String browserTitle="Online bookstore",
                browserDescription="Explore a vast selection of original books from top publishers. Your one-stop shop for textbooks, question papers, mock tests, exemplars, crash courses, and study materials. Enjoy the best deals, immediate delivery, and official, original books. 📚🚚✅",
                browserKeywords=null,
                onPageTitle="Online bookstore",
                onPageDescription="Online mock tests and study materials",
                publisherId=null,
                level=null,
                syllabus=null,
                grade=null,
                publisherName=null,
                publisherDisplayName="Best",
                publisherDescription=null,
                subject=null

        if (params.level != null && !"null".equals(params.leve)&& !"".equals(params.leve)) {
            level = params.level
            level = level.replace('-',' ')
            if(level.toLowerCase().indexOf("select")!=-1||level.toLowerCase().indexOf("sleep")!=-1||level.toLowerCase().indexOf(" or ")!=-1||level.indexOf("||")!=-1) level=null
        }

        if (params.syllabus != null && !"null".equals(params.syllabus)&& !"".equals(params.syllabus)) {
            syllabus = params.syllabus
            syllabus = syllabus.replace('-',' ')
            if(syllabus.toLowerCase().indexOf("select")!=-1||syllabus.toLowerCase().indexOf("sleep")!=-1||syllabus.toLowerCase().indexOf(" or ")!=-1||syllabus.indexOf("||")!=-1) syllabus=null
        }

        if (params.grade != null && !"null".equals(params.grade)&& !"".equals(params.grade)) {
            grade = params.grade
            grade = grade.replace('-',' ')
            if(grade.toLowerCase().indexOf("select")!=-1||grade.toLowerCase().indexOf("sleep")!=-1||grade.toLowerCase().indexOf(" or ")!=-1||grade.indexOf("||")!=-1) grade=null
        }
        Publishers publishers
        LevelsMst levelsMst
        SyllabusMst syllabusMst
        GradeMst gradeMst
        BooksTagDtl booksTagDtl
        String tagKeys=""
        String[] tags
        String prefix=""
        HashMap seoMap = new HashMap()
        String aboutTitle=null
        String aboutDescription = null
        //find the exam year
        def currentYearMonth = YearMonth.now()
        def year = currentYearMonth.year
        def month = currentYearMonth.monthValue

        if(month>3) year++

        if(siteId.intValue()!=1&&siteId.intValue()!=27){
           SiteDtl siteDtl = dataProviderService.getSiteDtl(siteId)
            if(siteDtl!=null&&siteDtl.siteTitle!=null&&!"".equals(siteDtl.siteTitle)) publisherDisplayName=siteDtl.siteTitle
            else{
                if(siteId.intValue()==11) publisherDisplayName = "EduGorilla"
                else if(siteId.intValue()==39) publisherDisplayName = "Oswal - Gurukul"
                else if(siteId.intValue()==37) publisherDisplayName = "Radian Book Compamy"
                else if(siteId.intValue()==38) publisherDisplayName = "Oswaal"
                else if(siteId.intValue()==46) publisherDisplayName = "MTG"
                else{

                    SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                    String[] names = siteMst.clientName.split(' ')
                    publisherDisplayName = names[0]
                }
            }

            publisherPresent = true
            wsSite = false

        }
        publisherDisplayName = publisherDisplayName.replaceAll("books","")
        //get publisher info
        if(params.publisherId!=null){
            publisherId = params.publisherId
            publishers = dataProviderService.getPublisher(new Integer(publisherId))
             if(publishers.publisherNameForTitle!=null&&!"".equals(publishers.publisherNameForTitle)){
                publisherDisplayName = publishers.publisherNameForTitle
            }
            else {
                publisherDisplayName = publishers.name.split(' ')[0]
            }
            if(publishers.tagline!=null) publisherDescription = publishers.tagline
            publisherPresent = true
            publisherName = publishers.name
        }

        if(params.booksTagId!=null) {
           booksTagDtl = getBooksTagDtl(params.booksTagId)
            if(booksTagDtl!=null) {
                level = booksTagDtl.level
                if ("2".equals(params.booksTagLevel)) syllabus = booksTagDtl.syllabus
                if ("3".equals(params.booksTagLevel)) {
                    syllabus = booksTagDtl.syllabus
                    grade = booksTagDtl.grade
                }
                if ("4".equals(params.booksTagLevel)) {
                    syllabus = booksTagDtl.syllabus
                    grade = booksTagDtl.grade
                    subject=booksTagDtl.subject

                }
            }
        }
              if(subject!=null){
                  if(redisService.("subjectsForGrade_" +grade.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))==null){
                      getSubjectsForGrade(level,syllabus,grade,siteId,publisherId)
                  }
                  tagKeys = redisService.("subjectsForGrade_" +grade.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))

                  tags = tagKeys.split(',')
                  if(publisherPresent) prefix = publisherDisplayName+" "
                  prefix +=subject
                      Blogs blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndSubjectAndColName(new Integer(1),syllabus,grade,subject,"introduction")
                      if(blogs!=null) {
                          aboutDescription = blogs.colValue
                          aboutTitle = syllabus
                      }


                  browserTitle = prefix+" Books, eBooks and Practice mock tests for "+year+" Exam Preparation"
                  browserDescription = prefix +" Books, eBooks and Practice mock tests - Find a wide selection of textbooks and study materials at India's premier online bookstore. Discover official and original books with fast delivery and exclusive offers.\n"
                  onPageTitle = prefix+" books, eBooks and Practice mock tests"
                  onPageDescription = prefix+" text books, online test series and study materials"

                  //keywords
                  if(publisherPresent) prefix = publisherDisplayName+" " else prefix = ""
                  browserKeywords = grade+" books,"
                  for(int i=0;i<tags.length;i++){
                      browserKeywords +=prefix+" "+tags[i]+" books,"
                  }
                  seoMap.put("level",level)
                  seoMap.put("syllabus",syllabus)
                  seoMap.put("grade",grade)
                  seoMap.put("subject",subject)
              }
              else if(grade!=null){
                if(redisService.("subjectsForGrade_" +grade.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))==null){
                    getSubjectsForGrade(level,syllabus,grade,siteId,publisherId)
                }
                tagKeys = redisService.("subjectsForGrade_" +grade.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))

                tags = tagKeys.split(',')
                if(publisherPresent) prefix = publisherDisplayName+" "
                if("School".equals(level)) {
                     prefix+=" "+syllabus+" Class "+grade
                    Blogs blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1),syllabus,"null","introduction")
                    if(blogs!=null) {
                        aboutDescription = blogs.colValue
                        aboutTitle = syllabus
                    }
                }
                else if("College".equals(level)) {
                    if(grade.trim().size()==1) prefix+=" "+syllabus+" Semester "+grade
                    prefix+=" "+syllabus+" "+grade
                    Blogs blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1),syllabus,"null","introduction")
                    if(blogs!=null) {
                        aboutDescription = blogs.colValue
                        aboutTitle = syllabus
                    }
                }
                else {
                    prefix +=grade
                    Blogs blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1),syllabus,grade,"introduction")
                    if(blogs!=null) {
                        aboutDescription = blogs.colValue
                        aboutTitle = syllabus
                    }
                }

                browserTitle = prefix+" Books, eBooks and Practice mock tests for "+year+" Exam Preparation"
                browserDescription = prefix +" Books, eBooks and Practice mock tests - Find a wide selection of textbooks and study materials at India's premier online bookstore. Discover official and original books with fast delivery and exclusive offers.\n"
                onPageTitle = prefix+" books, eBooks and Practice mock tests"
                onPageDescription = prefix+" text books, online test series and study materials"

                //keywords
                if(publisherPresent) prefix = publisherDisplayName+" " else prefix = ""
                browserKeywords = grade+" books,"
                for(int i=0;i<tags.length;i++){
                     browserKeywords +=prefix+" "+tags[i]+" books,"
                }
                seoMap.put("level",level)
                seoMap.put("syllabus",syllabus)
                seoMap.put("grade",grade)
            }
            else if(syllabus!=null){
                if(redisService.("gradesForSyllabus_" +syllabus.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))==null){
                    getGradesForSyllabus(level,syllabus,siteId,publisherId)
                }
                tagKeys = redisService.("gradesForSyllabus_" +syllabus.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))

                 tags = tagKeys.split(',')
                if(publisherPresent) prefix = publisherDisplayName+" "
                    prefix +=" "+syllabus
                browserTitle = prefix+" Books, eBooks and Practice mock tests for "+year+" Exam Preparation"
                browserDescription = prefix +" Books, eBooks and Practice mock tests - Find a wide selection of textbooks and study materials at India's premier online bookstore. Discover official and original books with fast delivery and exclusive offers.\n"
                onPageTitle = prefix+" books"
                onPageDescription = prefix+" text books, online test series and study materials"

                //keywords
                browserKeywords = level+" books,"
                for(int i=0;i<tags.length;i++){
                    browserKeywords +=prefix+" "+tags[i]+" books,"
                }

                Blogs blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1),syllabus,"null","introduction")
                if(blogs!=null) {
                    aboutDescription = blogs.colValue
                    aboutTitle = syllabus
                }
                seoMap.put("level",level)
                seoMap.put("syllabus",syllabus)
            }
            else if(level!=null){
                if(redisService.("syllabusForLevel_" +level.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))==null){
                    getSyllabusForLevel(level,siteId,publisherId)
                }
                tagKeys = redisService.("syllabusForLevel_" +level.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:""))
                tags = tagKeys.split(',')
                if(publisherPresent) prefix = publisherDisplayName+" "
                prefix +=" "+level
                browserTitle = prefix+" Books, eBooks and Practice mock tests for "+year+" Exam Preparation"
                browserDescription = prefix +" Books, eBooks and Practice mock tests - Find a wide selection of textbooks and study materials at India's premier online bookstore. Discover official and original books with fast delivery and exclusive offers.\n"
                onPageTitle = prefix+" books"
                onPageDescription = prefix+" text books, online test series and study materials"

                //keywords
                browserKeywords = level+" books,"
                for(int i=0;i<tags.length;i++){
                    browserKeywords +=prefix+tags[i]+" books,"
                }
                seoMap.put("level",level)
            }
           else {
                if (redisService.("levels_" + siteId + (publisherId != null ? "_" + publisherId : "")) == null) {
                    getLevels(siteId, publisherId)
                }
                tagKeys = redisService.("levels_" + siteId + (publisherId != null ? "_" + publisherId : ""))
                tags = tagKeys.split(',')
                if (publisherDisplayName != null) prefix = publisherDisplayName + " "
                browserTitle = prefix + " Books, eBooks and Practice mock tests for " + year + " Exam Preparation"
                browserDescription = prefix + " Books, eBooks and Online mock tests - Find a wide selection of textbooks and study materials at India's premier online bookstore. Discover official and original books with fast delivery and exclusive offers.\n"
                if (publisherPresent) {

                    if(publisherDisplayName.toLowerCase().indexOf("books")==-1)
                    onPageTitle = publisherDisplayName + " books & eBooks"
                    else {
                        onPageTitle = publisherDisplayName +" & eBooks"
                    }
                    //site specific stuff
                    if(siteId.intValue()==46){
                        onPageTitle = "MTG eBooks"
                    }
                    if(siteId.intValue()==88){
                        onPageTitle = publisherDisplayName
                        if(onPageDescription.indexOf("text books, online test series and study materials")>-1) onPageDescription = onPageDescription.replaceAll("text books, online test series and study materials","iBookGPT , AI enabled books")
                    }

                    if(publisherDescription!=null) browserDescription = publisherDescription
                    else if(!wsSite){
                        //get the default publisher for the
                        publishers = getDefaultPublisher(siteId)
                        if(publishers!=null&&publishers.tagline!=null&&!"".equals(publishers.tagline)) {
                            browserDescription = publishers.tagline
                            publisherDescription = publishers.tagline
                        }
                    }
                }

                //keywords
                browserKeywords = ""
                for (int i = 0; i < tags.length; i++) {
                    browserKeywords += prefix + " " + tags[i] + " books,"
                    browserKeywords += prefix + " " + tags[i] + " eBooks,"
                    browserKeywords += prefix + " " + tags[i] + " mock tests,"
                }
                seoMap.put("level",null)
                seoMap.put("syllabus",null)
                seoMap.put("grade",null)
            }

        //site specific stuff
        if(siteId.intValue()==46){
            if(onPageTitle.indexOf("eBooks")==-1) onPageTitle = onPageTitle.replaceAll("books","eBooks")
            if(onPageDescription.indexOf("text books, online test series and study materials")>-1) onPageDescription = onPageDescription.replaceAll("text books, online test series and study materials","Online mock tests and study materials")
        }
        else if(siteId.intValue()==115){
            onPageDescription=""
        }

        seoMap.put("browserTitle",browserTitle)
        seoMap.put("browserDescription",browserDescription)
        seoMap.put("browserKeywords",browserKeywords)
        seoMap.put("onPageTitle",onPageTitle)
        seoMap.put("onPageDescription",onPageDescription)
        seoMap.put("publisherDescription",publisherDescription)
        seoMap.put("publisherName",publisherName)
        seoMap.put("aboutTitle",aboutTitle)
        seoMap.put("aboutDescription",aboutDescription)


        return seoMap
    }


    @Transactional
    def getBooksTagDtl(String bookTagDtlId){
        BooksTagDtl booksTagDtl  = redisService.memoizeDomainObject(BooksTagDtl, "booksTagDtl_"+bookTagDtlId) {
            return BooksTagDtl.findById(new Integer(bookTagDtlId));
        }
        return booksTagDtl
    }

    def getSubjectsForGrade(String level,String syllabus,String grade,Integer siteId,String publisherId){
        def sql
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String addlCondition=""
        if(publisherId!=null) addlCondition=" and bm.publisher_id="+publisherId
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=""+siteId;

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }

        sql = "SELECT group_concat(distinct(subject)) FROM wsshop.books_tag_dtl btd, books_mst bm " +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and btd.level='" +level+"' and btd.syllabus='"+syllabus+"' and grade='"+grade+"' "+addlCondition
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        redisService.("subjectsForGrade_" +grade.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:"")) = results[0][0]
        }
    def getGradesForSyllabus(String level,String syllabus,Integer siteId,String publisherId){
        def sql
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String addlCondition=""
        if(publisherId!=null) addlCondition=" and bm.publisher_id="+publisherId
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=""+siteId;

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }

        sql = "SELECT group_concat(distinct(grade)) FROM wsshop.books_tag_dtl btd, books_mst bm " +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and btd.level='" +level+"' and btd.syllabus='"+syllabus+"'"+addlCondition
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        redisService.("gradesForSyllabus_" +syllabus.replace(' ','-')+"_"+ siteId+(publisherId!=null?"_"+publisherId:"")) = results[0][0]

    }

    def getSyllabusForLevel(String level,Integer siteId,String publisherId){
        def sql
        String addlCondition=""
        if(publisherId!=null) addlCondition=" and bm.publisher_id="+publisherId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=""+siteId;

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }

        sql = "SELECT group_concat(distinct(syllabus)) FROM wsshop.books_tag_dtl btd, books_mst bm " +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'  and btd.level='" +level+"'"+addlCondition
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        redisService.("syllabusForLevel_"+level.replace(' ','-')+"_" + siteId+(publisherId!=null?"_"+publisherId:"")) = results[0][0]

    }

    def getLevels(Integer siteId,String publisherId){
        def sql
        String addlCondition=""
        if(publisherId!=null) addlCondition=" and bm.publisher_id="+publisherId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=""+siteId;

        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
            siteIdList = redisService.("siteIdList_" + siteId)
        }

        sql = "SELECT group_concat(distinct(level)) FROM wsshop.books_tag_dtl btd, books_mst bm " +
                " where bm.site_id in(" + siteIdList + ") and bm.id=btd.book_id  and bm.status='published'"+addlCondition
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        redisService.("levels_" + siteId+(publisherId!=null?"_"+publisherId:"")) = results[0][0]

    }

    @Transactional
    def createCategoriesSiteMap(String siteListString,String fileName,String publisherId,String siteName,String publisherName,String publisherSiteTitle,String flexiPath){
       String optionSQL = ""
        String url=""

        if(publisherId!=null) {
            optionSQL =" and bm.publisher_id="+publisherId
            if(publisherSiteTitle!=null&&!"".equalsIgnoreCase(publisherSiteTitle)) publisherName=publisherSiteTitle+"-"
            else publisherName = publisherName.split(' ')[0]+"-"
        }else publisherName=""

       String sql = "SELECT max(btd.id) btdId,btd.level FROM books_mst bm,books_tag_dtl btd " +
                " where bm.status='published' and bm.site_id in (" + siteListString + ")  and bm.id=btd.book_id " +optionSQL+
               " group by level"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def  books = sql1.rows(sql)

        File siteMapCategory = new File("upload/sitemap"+fileName)
        if (siteMapCategory.exists()) siteMapCategory.delete()
        books.each { book ->
            url = siteName+"/"+publisherName+book.level.replace(' ','-')+flexiPath+book.btdId+"/1"
            if(publisherId!=null) url +="/"+publisherId
            siteMapCategory << url+"\n"
            noOfStorePageLinks++
        }

        //syllabus
        sql = "SELECT max(btd.id) btdId,btd.level,btd.syllabus FROM books_mst bm,books_tag_dtl btd " +
                " where bm.status='published' and bm.site_id in (" + siteListString + ")  and bm.id=btd.book_id " +optionSQL+
                " group by level,syllabus"

         dataSource = grailsApplication.mainContext.getBean('dataSource')
         sql1 = new Sql(dataSource)
         books = sql1.rows(sql)

        books.each { book ->
            url = siteName+"/"+publisherName+book.syllabus.replace(' ','-')+flexiPath+book.btdId+"/2"
            if(publisherId!=null) url +="/"+publisherId
            siteMapCategory << url+"\n"
            noOfStorePageLinks++
        }

        //grade
        sql = "SELECT max(btd.id) btdId,btd.level,btd.syllabus,btd.grade FROM books_mst bm,books_tag_dtl btd " +
                " where bm.status='published' and bm.site_id in (" + siteListString + ")  and bm.id=btd.book_id " +optionSQL+
                " group by level,syllabus,grade"

        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new Sql(dataSource)
        books = sql1.rows(sql)
        String gradeString
        books.each { book ->
            if("School".equals(book.level)) gradeString=book.syllabus.replace(' ','-')+"-class-"+book.grade
            else gradeString=book.grade.replace(' ','-')
            url = siteName+"/"+publisherName+gradeString+flexiPath+book.btdId+"/3"
            if(publisherId!=null) url +="/"+publisherId
            siteMapCategory << url+"\n"
            noOfStorePageLinks++
        }

        //subject for state exam
        sql = "SELECT max(btd.id) btdId,btd.level,btd.syllabus,btd.grade,btd.subject FROM books_mst bm,books_tag_dtl btd " +
                " where bm.status='published' and bm.site_id in (" + siteListString + ")  and bm.id=btd.book_id and btd.syllabus='State Level' " +optionSQL+
                " group by level,syllabus,grade,subject"

        dataSource = grailsApplication.mainContext.getBean('dataSource')

        sql1 = new Sql(dataSource)
        books = sql1.rows(sql)
        println("number of rows "+books.size())
        String subjectString
        books.each { book ->
            gradeString=book.grade.replace(' ','-')
            subjectString = book.subject.replace(' ','-')
            url = siteName+"/"+publisherName+gradeString+"-"+subjectString+flexiPath+book.btdId+"/4"
            if(publisherId!=null) url +="/"+publisherId
            siteMapCategory << url+"\n"
            noOfStorePageLinks++
        }
    }

    def createGradeAndSyllabusSiteMap(serverUrl){
        File sitemap = new File("upload/sitemapSyllabusAndGradeBlogs.txt")
        if (sitemap.exists()) sitemap.delete()

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)



        String sql = "select b.syllabus,ls.id from wsshop.level_syllabus ls,wsshop.blogs b where ls.syllabus=b.syllabus and ls.site_id=1 and b.grade='null' and b.col_name='introduction'"

        List blogs = sql1.rows(sql);
        blogs.each { blog ->
            sitemap << serverUrl+"/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').toLowerCase()}/information/s/"+blog.id+"\n"
        }


        //hindi
        sql = "select b.syllabus,ls.id from wsshop.level_syllabus ls,wsshop.blogs b where ls.syllabus=b.syllabus and ls.site_id=1 and b.grade='null' and b.col_name='introductionHindi'"

         blogs = sql1.rows(sql);
        blogs.each { blog ->
            sitemap << serverUrl+"/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').toLowerCase()}/hindi/s/"+blog.id+"\n"
        }

        //grade level
        sql = "select b.syllabus,b.grade,ls.id from wsshop.syllabus_grade_dtl ls,wsshop.blogs b \n" +
                " where ls.syllabus=b.syllabus and ls.site_id=1   and ls.grade=b.grade and b.col_name='introduction'"

         blogs = sql1.rows(sql);
        String additionalText
        blogs.each { blog ->
            additionalText="";
            LevelSyllabus levelSyllabus = LevelSyllabus.findBySiteIdAndSyllabus(new Integer(1),blog.syllabus)

            if("School".equals(levelSyllabus.level)) additionalText = "class-";
            else if ("College".equals(levelSyllabus.syllabus)) additionalText = "semester-";
            sitemap << serverUrl+"/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').toLowerCase()}-"+additionalText+"${("" + blog.grade).replaceAll(' ', '-').toLowerCase()}/information/g/"+blog.id+"\n"
        }

        //grade hindi
        sql = "select b.syllabus,b.grade,ls.id from wsshop.syllabus_grade_dtl ls,wsshop.blogs b \n" +
                " where ls.syllabus=b.syllabus and ls.site_id=1   and ls.grade=b.grade and b.col_name='introductionHindi'"

        blogs = sql1.rows(sql);

        blogs.each { blog ->
            additionalText="";
            LevelSyllabus levelSyllabus = LevelSyllabus.findBySiteIdAndSyllabus(new Integer(1),blog.syllabus)
            if("School".equals(levelSyllabus.level)) additionalText = "class-";
            else if ("College".equals(levelSyllabus.syllabus)) additionalText = "semester-";
            sitemap << serverUrl+"/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').toLowerCase()}-"+additionalText+"${("" + blog.grade).replaceAll(' ', '-').toLowerCase()}/hindi/g/"+blog.id+"\n"
        }

        //subject level
        sql = "select b.syllabus,b.subject,ls.id from wsshop.syllabus_subject ls,wsshop.blogs b \n" +
                " where ls.syllabus=b.syllabus   and ls.subject=b.subject and b.col_name='introduction'"
       println(sql)
        blogs = sql1.rows(sql);

        blogs.each { blog ->
            sitemap << serverUrl+"/best-books-for-${("" + blog.subject).replaceAll(' ', '-').toLowerCase()}/information/su/"+blog.id+"\n"
        }

        //grade hindi
        sql = "select b.syllabus,b.subject,ls.id from wsshop.syllabus_subject ls,wsshop.blogs b \n" +
                " where ls.syllabus=b.syllabus    and ls.subject=b.subject and b.col_name='introductionHindi'"

        blogs = sql1.rows(sql);

        blogs.each { blog ->
            sitemap << serverUrl+"/best-books-for-${("" + blog.subject).replaceAll(' ', '-').toLowerCase()}/hindi/su/"+blog.id+"\n"
        }
    }

    def createGradeBooksSiteMap(String serverUrl,String siteName,Long siteId){
        def params = new HashMap()
        List grades  = wsshopService.getActiveGrades(siteId,params.publisherId)
        List subjects  = wsshopService.getActiveSubjects(siteId,params.publisherId)

        File sitemap = new File("upload/sitemapGradeBooks"+siteName+".txt")
        if (sitemap.exists()) sitemap.delete()
        String title=""

        //first get the grade items
        grades.each {grade ->
            title =""
            if("School".equals(grade.level)) title +=grade.syllabus+"-class-"+grade.grade
            else if("College".equals(grade.level)) title +=grade.syllabus+"-semester-"+grade.grade
            else title +=grade.grade
            title = title.replace(' ','-').replace('/','-').toLowerCase()
            if(grade.id!=null){
                sitemap << serverUrl+"/${title}/books/g/${grade.id}\n"
            }
        }

        subjects.each{subject ->
            title =subject.subject.replace(' ','-').replace('/','-').toLowerCase()
            sitemap << serverUrl+"/${title}/books/su/${subject.id}\n"
        }

        //publishers only if wonderslate or prepjoy
        if(siteId.intValue()==1||siteId.intValue()==27){
            HashMap booksAndPublishers = wsshopService.getBooksList(params,siteId,0)
            List publishers  = new JsonSlurper().parseText(booksAndPublishers.get("publishers"))
            publishers.each { publisher ->
                grades  = wsshopService.getActiveGrades(siteId,""+publisher.publisherId)
                subjects  = wsshopService.getActiveSubjects(siteId,""+publisher.publisherId)
                grades.each { grade ->
                    title=""
                    if ("School".equals(grade.level)) title += grade.syllabus + "-class-" + grade.grade
                    else if ("College".equals(grade.level)) {
                        if((""+grade.grade).trim().size()==1)
                        title += grade.syllabus + "-semester-" + grade.grade
                        else
                            title += grade.syllabus + " " + grade.grade
                    }
                    else title += grade.grade
                    title = title.replace(' ', '-').replace('/','-').toLowerCase()
                    if (grade.id != null) {
                        sitemap << serverUrl + "/${title}/books/g/${grade.id}/${publisher.publisherId}\n"
                    }
                }

                subjects.each{subject->
                    title =subject.subject.replace(' ','-').replace('/','-').toLowerCase()
                    sitemap << serverUrl+"/${title}/books/su/${subject.id}/${publisher.publisherId}\n"
                }
            }
        }




    }

    def createSubjectBooksSiteMap(String serverUrl,String siteName,Long siteId){
        def params = new HashMap()
        List subjects  = wsshopService.getActiveSubjects(siteId,params.publisherId)

        File sitemap = new File("upload/sitemapSubjectBooks"+siteName+".txt")
        if (sitemap.exists()) sitemap.delete()
        String title=""

        //first get the grade items
        subjects.each {subject ->
            title =subject.grade+"-"+subject.subject

            title = title.replace(' ','-').replace('/','-').toLowerCase()
            if(subject.id!=null){
                sitemap << serverUrl+"/${title}/books/su/${subject.id}\n"
            }
        }

        //publishers only if wonderslate or prepjoy
        if(siteId.intValue()==1||siteId.intValue()==27){
            HashMap booksAndPublishers = wsshopService.getBooksList(params,siteId,0)
            List publishers  = new JsonSlurper().parseText(booksAndPublishers.get("publishers"))
            publishers.each { publisher ->
                subjects  = wsshopService.getActiveSubjects(siteId,""+publisher.publisherId)
                subjects.each { subject ->
                    title= subject.grade+"-"+subject.subject
                    title = title.replace(' ', '-').replace('/','-').toLowerCase()
                    if (grade.id != null) {
                        sitemap << serverUrl + "/${title}/books/su/${subject.id}/${publisher.publisherId}\n"
                    }
                }
            }
        }


    }

    def getDefaultPublisher(Integer siteId){
        Publishers publishers  = redisService.memoizeDomainObject(Publishers, "siteMstPublisher_"+siteId) {
            return Publishers.findBySiteId(siteId)
        }

        return publishers
    }

    String removeNonUTF8Chars(String input) {
        return input.replaceAll("[^\\x00-\\x7F]", "");
    }

    def generateMockTestsSiteMap(){
        File sitemapIndex = new File("upload/sitemapMockTestsIndex.xml")
        File prepJoysitemapIndex = new File("upload/sitemapPrepjoyMockTestsIndex.xml")
        if(sitemapIndex.exists()) sitemapIndex.delete()
        sitemapIndex << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n" +
                "  <sitemap>\n" +
                "    <loc>https://www.wonderslate.com/statistics/sitemapMockTest.txt</loc>\n" +
                "  </sitemap>\n"

        if(prepJoysitemapIndex.exists()) prepJoysitemapIndex.delete()
        prepJoysitemapIndex << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n" +
                "  <sitemap>\n" +
                "    <loc>https://www.prepjoy.com/statistics/sitemapPrepjoyMockTest.txt</loc>\n" +
                "  </sitemap>\n"
        File sitemap = new File("upload/sitemapMockTest.txt")
        if(sitemap.exists()) sitemap.delete()

        File prepjoySitemap = new File("upload/sitemapPrepjoyMockTest.txt")
        if(prepjoySitemap.exists()) prepjoySitemap.delete()

        sitemap << "https://www.wonderslate.com/mocktests\n"
        prepjoySitemap << "https://www.prepjoy.com/mocktests\n"

        //add main tests

        Integer siteId = new Integer(1)
        String sql
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)

        //for sql data
        sql = " select exam_group examGroup,description,image_src imageSrc from daily_exam_group where exam_group in (SELECT distinct(exam_group) examGroup" +
                " from daily_tests_site_dtl dtsd,daily_tests_mst dtm where dtsd.site_id="+siteId+" and dtm.id=dtsd.daily_test_id) order by examGroup"
        def results = sql1.rows(sql);
        results.each { exam ->
            sitemap << "https://www.wonderslate.com/"+(""+exam.examGroup).replaceAll(" ","-").toLowerCase()+"/mocktests\n"
            prepjoySitemap << "https://www.prepjoy.com/"+(""+exam.examGroup).replaceAll(" ","-").toLowerCase()+"/mocktests\n"

             dataSource = grailsApplication.mainContext.getBean('dataSource')
             sql1 = new Sql(dataSource)

            //for sql data
            sql = " select id,test_name testName, level,syllabus,grade,subject,COALESCE(test_details,'') testDetails,test_subject testSubject from daily_tests_mst dtm where  lower(dtm.exam_group)='"+exam.examGroup+"' order by testName"
            def results1 = sql1.rows(sql);

            results1.each { examType ->
                sitemap << "https://www.wonderslate.com/"+(""+examType.testName).replaceAll(" ","-").toLowerCase()+"/mocktests/all/${examType.id}\n"
                prepjoySitemap << "https://www.prepjoy.com/"+(""+examType.testName).replaceAll(" ","-").toLowerCase()+"/mocktests/all/${examType.id}\n"

                //now for each test
                String dailyTestId = ""+examType.id
                if(redisService.("dailyTestsLatestDate"+"_"+dailyTestId)==null) prepjoyService.getDailyTestsLatestAndStartDates(dailyTestId)
                String startingDate = redisService.("dailyTestStartingDate"+"_"+dailyTestId)
                String latestDate = redisService.("dailyTestsLatestDate"+"_"+dailyTestId)
                int startingYear = Integer.parseInt(startingDate.split('-')[0])
                int startingMonth = Integer.parseInt(startingDate.split('-')[1])
                int latestYear = Integer.parseInt(latestDate.split('-')[0])
                int latestMonth = Integer.parseInt(latestDate.split('-')[1])
                for(int i=startingYear;i<=latestYear;i++){
                    int firstMonth=1
                    int lastMonth=12
                    if(i==startingYear) firstMonth = startingMonth
                    else if(i==latestYear) lastMonth = latestMonth
                    for(int j=firstMonth;j<=lastMonth;j++){
                        sitemap << "https://www.wonderslate.com/"+(""+examType.testName).replaceAll(" ","-").toLowerCase()+"/mocktests/${i}-${j}/${examType.id}\n"
                        prepjoySitemap << "https://www.prepjoy.com/"+(""+examType.testName).replaceAll(" ","-").toLowerCase()+"/mocktests/${i}-${j}/${examType.id}\n"
                    }
                }

            }
        }

        sitemapIndex << "</sitemapindex>"
        prepJoysitemapIndex << "</sitemapindex>"

    }

    def createPrintbookCategoroiesSitemap(){
        //print book categories
        File sitemapChapters = new File("upload/sitemapPrintCategories.txt")
        File prepjoyPrintCategories = new File("upload/sitemapPrepjoyPrintCategories.txt")



        List categoryLevel = CategoryLevel1.findAll()
        categoryLevel.each { category ->
            sitemapChapters << "https://www.wonderslate.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?baseCategory=${category.categoryType}\n"
            prepjoyPrintCategories << "https://www.prepjoy.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?siteName=prepjoy&baseCategory=${category.categoryType}\n"
        }
        categoryLevel = CategoryLevel2.findAll()
        categoryLevel.each { category ->
            sitemapChapters << "https://www.wonderslate.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?baseCategory=${category.categoryType}\n"
            prepjoyPrintCategories << "https://www.prepjoy.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?siteName=prepjoy&baseCategory=${category.categoryType}\n"
        }

        categoryLevel = CategoryLevel3.findAll()
        categoryLevel.each { category ->
            sitemapChapters << "https://www.wonderslate.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?baseCategory=${category.categoryType}\n"
            prepjoyPrintCategories << "https://www.prepjoy.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?siteName=prepjoy&baseCategory=${category.categoryType}\n"
        }
        categoryLevel = CategoryLevel4.findAll()
        categoryLevel.each { category ->
            sitemapChapters << "https://www.wonderslate.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?baseCategory=${category.categoryType}\n"
            prepjoyPrintCategories << "https://www.prepjoy.com/${category.nodeName.replaceAll(' ','-').replaceAll('/','-').toLowerCase()}/${printBooksService.getBaseCategoryUrl(category.categoryType)}/${category.browseNodeId}?siteName=prepjoy&baseCategory=${category.categoryType}\n"
        }
    }


}

