package com.wonderslate.data

import com.google.api.client.googleapis.json.GoogleJsonResponseException
import com.google.api.client.http.HttpRequest
import com.google.api.client.http.HttpRequestInitializer
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.jackson2.JacksonFactory
import com.google.api.services.youtube.YouTube
import com.google.api.services.youtube.model.SearchListResponse
import com.google.api.services.youtube.model.SearchResult
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ibookso.products.CategoryLevel1
import com.ibookso.products.CategoryLevel2
import com.ibookso.products.CategoryLevel3
import com.ibookso.products.CategoryLevel4
import com.ibookso.products.ExtPublishers
import com.ibookso.products.PrintBooksMst
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.ChaptersDownloadDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteIpAddress
import com.wonderslate.institute.InstituteMst
import com.wonderslate.log.Quizrecorder
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.publish.SyllabusSubject
import com.wonderslate.seo.UrlMapping
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugins.redis.RedisService
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import java.text.ParseException
import java.text.SimpleDateFormat
import groovy.json.JsonBuilder
import groovy.json.JsonOutput

@Transactional
class UtilService {
   def grailsApplication
    static def redisService
    static DataProviderService dataProviderService
    SpringSecurityService springSecurityService
    UserManagementService userManagementService
    WsLibraryService wsLibraryService




    public static double diceCoefficient(String s1, String s2)
    {
        Set<String> nx = new HashSet<String>();
        Set<String> ny = new HashSet<String>();

        for (int i=0; i < s1.length()-1; i++) {
            char x1 = s1.charAt(i);
            char x2 = s1.charAt(i+1);
            String tmp = "" + x1 + x2;
            nx.add(tmp);
        }
        for (int j=0; j < s2.length()-1; j++) {
            char y1 = s2.charAt(j);
            char y2 = s2.charAt(j+1);
            String tmp = "" + y1 + y2;
            ny.add(tmp);
        }

        Set<String> intersection = new HashSet<String>(nx);
        intersection.retainAll(ny);
        double totcombigrams = intersection.size();

        return (2*totcombigrams) / (nx.size()+ny.size());
    }

/**
 * Here's an optimized version of the dice coefficient calculation. It takes
 * advantage of the fact that a bigram of 2 chars can be stored in 1 int, and
 * applies a matching algorithm of O(n*log(n)) instead of O(n*n).
 *
 * <p>Note that, at the time of writing, this implementation differs from the
 * other implementations on this page. Where the other algorithms incorrectly
 * store the generated bigrams in a set (discarding duplicates), this
 * implementation actually treats multiple occurrences of a bigram as unique.
 * The correctness of this behavior is most easily seen when getting the
 * similarity between "GG" and "GGGGGGGG", which should obviously not be 1.
 *
 * @param s The first string
 * @param t The second String
 * @return The dice coefficient between the two input strings. Returns 0 if one
 *         or both of the strings are {@code null}. Also returns 0 if one or both
 *         of the strings contain less than 2 characters and are not equal.
 * <AUTHOR> Fresen
 */
    public static double diceCoefficientOptimized(String s, String t)
    {
        // Verifying the input:
        if (s == null || t == null)
            return 0;
        // Quick check to catch identical objects:
        if (s == t)
            return 1;
        // avoid exception for single character searches
        if (s.length() < 2 || t.length() < 2)
            return 0;

        // Create the bigrams for string s:
        final int n = s.length()-1;
        final int[] sPairs = new int[n];
        for (int i = 0; i <= n; i++)
            if (i == 0)
                sPairs[i] = s.charAt(i) << 16;
            else if (i == n)
                sPairs[i-1] |= s.charAt(i);
            else
                sPairs[i] = (sPairs[i-1] |= s.charAt(i)) << 16;

        // Create the bigrams for string t:
        final int m = t.length()-1;
        final int[] tPairs = new int[m];
        for (int i = 0; i <= m; i++)
            if (i == 0)
                tPairs[i] = t.charAt(i) << 16;
            else if (i == m)
                tPairs[i-1] |= t.charAt(i);
            else
                tPairs[i] = (tPairs[i-1] |= t.charAt(i)) << 16;

        // Sort the bigram lists:
        Arrays.sort(sPairs);
        Arrays.sort(tPairs);

        // Count the matches:
        int matches = 0, i = 0, j = 0;
        while (i < n && j < m)
        {
            if (sPairs[i] == tPairs[j])
            {
                matches += 2;
                i++;
                j++;
            }
            else if (sPairs[i] < tPairs[j])
                i++;
            else
                j++;
        }
        return (double)matches/(n+m);
    }

    String getCommaSeparatedFromList(List list,String key){

        String[] keyStringArray = new String[list.size()]
        for(int i=0;i<list.size();i++){
            keyStringArray[i] = list.get(i).(""+key)
        }
        if(list.size()>0) return String.join(",", keyStringArray);
        else return ""
    }

    Date convertDate(Date dateFrom, String fromTimeZone, String toTimeZone) throws ParseException {
        String pattern = "yyyy/MM/dd HH:mm:ss";
        SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern);
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone));

        SimpleDateFormat sdfTo = new SimpleDateFormat (pattern);
        sdfTo.setTimeZone(TimeZone.getTimeZone(toTimeZone));
        Date dateTo = sdfFrom.parse(sdfTo.format(dateFrom));
        return dateTo;
    }
    Date convertDateWithPattern(String dateFrom, String fromTimeZone, String toTimeZone, String pattern) throws ParseException {
        SimpleDateFormat sdfFrom = new SimpleDateFormat(pattern);
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone));
        Date parsedDate = sdfFrom.parse(dateFrom);
        println("****** parsedDate: "+parsedDate)

        return parsedDate;
}

    String getTodaysDate(){
        Date today =  new Date()

            String pattern = "dd-MM-yyyy";
            SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern);
            sdfFrom.setTimeZone(TimeZone.getTimeZone("UTC"));

            SimpleDateFormat sdfTo = new SimpleDateFormat (pattern);
            sdfTo.setTimeZone(TimeZone.getTimeZone("IST"));

            today = sdfFrom.parse(sdfTo.format(today));


        return new SimpleDateFormat("yyyy-MM-dd").format(today)

    }




    @Transactional
    def calculateRank(Long resId){
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(resId)
        if(resourceDtl!=null) {
            new Sql(grailsApplication.mainContext.getBean('dataSource_wslog')).call("{call calculateRank($resId,${resourceDtl.testEndDate},${Sql.INTEGER})}") { vNoOfTestTakers ->
            resourceDtl.noOfTestTakers = vNoOfTestTakers
            resourceDtl.save(flush: true, failOnError: true)            
            }
        }
    }

    String getIPAddressOfClient(request){
        //https://www.oodlestechnologies.com/blogs/Java-Get-Client-IP-Address
        String url = request.getRequestURL()
        if (url.indexOf("http://localhost") > -1) {
            return "127.0.0.1"
        }else {
            String remoteAddr = request.getHeader("X-FORWARDED-FOR");
            if (remoteAddr == null || "".equals(remoteAddr)) {
                remoteAddr = request.getRemoteAddr();
                if (remoteAddr.equalsIgnoreCase("0:0:0:0:0:0:0:1")) {
                    InetAddress inetAddress = InetAddress.getLocalHost();
                    String ipAddress = inetAddress.getHostAddress();
                    remoteAddr = ipAddress;
                }
            }

            return remoteAddr
        }
    }

    @Transactional
    boolean hasLibraryAccess(request,siteId){
        String ipAddress = getIPAddressOfClient(request);

        InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress,siteId)
        if(instituteIPAddress==null){
            if(springSecurityService.currentUser!=null) {
                String sql = "select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                        " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and  im.site_id=" +siteId;
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                if (results.size() > 0) {
                    return true
                }else {
                    return false
                }
            }

        }else{
                String sql = "select cbd.conducted_by from wsuser.institute_mst im, wsuser.course_batches_dtl cbd " +
                        " where im.id='" + instituteIPAddress.institute_id + "' and im.id=cbd.conducted_by and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and im.site_id=" + siteId;

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                if (results.size() > 0) {
                    return true
                }else{
                    return false
                }
        }
    }

   boolean userHasBookAccess(bookId,username){
       boolean hasAccess = false;

       BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(bookId,username)
       if(booksPermission!=null) {
           hasAccess = true
       }
       else{
           List batches  = BatchUserDtl.findAllByUsername(username)
           // Collect all active batch IDs for the user
           List<Long> batchIds = []
           for (int i = 0; i < batches.size(); i++) {
               CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByIdAndStatus(batches[i].id,"active")
               if(courseBatchesDtl!=null) {
                   batchIds.add(courseBatchesDtl.id)
               }
           }

           if(!batchIds.isEmpty()) {
               // Get all book IDs available to these batches (including package books)
               Set<Long> availableBookIds = getAllBookIdsForBatchesUtil(batchIds)

               // Check if the requested book ID is in the available books
               if(availableBookIds.contains(bookId as Long)){
                   hasAccess = true
               }
           }
       }

       return hasAccess
   }

   private Set<Long> getAllBookIdsForBatchesUtil(List<Long> batchIds) {
       Set<Long> allBookIds = new HashSet<>()

       if(batchIds.isEmpty()) {
           return allBookIds
       }

       String batchIdsStr = batchIds.join(',')

       // SQL to get both direct books and package books
       String sql = """
           SELECT DISTINCT book_id FROM (
               SELECT bbd.book_id
               FROM wsuser.books_batch_dtl bbd
               WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
                 AND bbd.batch_id IN (${batchIdsStr})
               UNION
               SELECT bm1.id as book_id
               FROM wsuser.books_batch_dtl bbd
               JOIN wsshop.books_mst bm ON bbd.book_id = bm.id
               JOIN wsshop.books_mst bm1 ON bm.package_book_ids IS NOT NULL
                   AND FIND_IN_SET(bm1.id, bm.package_book_ids) != 0
               WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
                 AND bbd.batch_id IN (${batchIdsStr})
                 AND (bm.show_in_library = 'Yes' OR bm.show_in_library IS NULL)
                 AND bm1.show_in_library = 'Yes'
                 AND bm1.site_id = bm.site_id
           ) AS combined_books
       """

       def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
       def sql2 = new groovy.sql.Sql(dataSource)
       def results = sql2.rows(sql)

       results.each { row ->
           allBookIds.add(row.book_id as Long)
       }

       return allBookIds
   }

    Integer getSiteIdIgnoreSiteName(request,session){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(request.getParameter("siteId")!=null) {
                siteId = new Integer(request.getParameter("siteId"))
                session["siteId"] = siteId
            };
            else{
                navigationHelper(session,request,request.getParameter("siteName"))
                if(session["siteId"]!=null) siteId = session["siteId"]
            }
        }

        return siteId;
    }
     Integer getSiteId(request,session){
         Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(request.getParameter("siteId")!=null) {
                siteId = new Integer(request.getParameter("siteId"))
                session["siteId"] = siteId
            };
            else if(request.getParameter("siteName")!=null){
                SiteMst siteMst = dataProviderService.getSiteMstBySiteName(request.getParameter("siteName"))
                if(siteMst!=null) {
                    siteId = new Integer(siteMst.id.intValue())
                    session["siteId"] = siteId
                }
            }else{
                navigationHelper(session,request,null)
                if(session["siteId"]!=null) siteId = session["siteId"]
            }
        }

        return siteId;
    }

    boolean sendSMS(siteId,String message,mobile,resend){
        SiteMst sm = dataProviderService.getSiteMst(siteId)

        String sms_um = sm.smsUsername
        String sms_pw = sm.smsPassword
        String sms_sid = sm.smsSenderId

        RestBuilder rest = new RestBuilder()

        if("true" != resend)
        {
            def requestParams = [message:message]
            if(sm.smsResendUrl !=null && sm.id!=38){
                def jsonObject = [
                        "authorization":"yHUXYRwiDyNvpluluVt1HWjKkZmVD6Xe2xyPShENlCCY3sYavVS1hH4U0fmd",
                        "route": "dlt_manual",
                        "sender_id": sm.smsSenderId,
                        "entity_id": "1401483070000010924",
                        "template_id": sm.smsResendUrl,
                        "message": message,
                        "flash": 0,
                        "numbers": mobile
                ]

                def jsonString = JsonOutput.toJson(jsonObject)

                def postRes = rest.post("https://api.fast2sms.in/send.php"){
                    auth("yHUXYRwiDyNvpluluVt1HWjKkZmVD6Xe2xyPShENlCCY3sYavVS1hH4U0fmd")
                    contentType("application/json")
                    body(jsonString)
                }
            }else if(sm.smsResendUrl !=null && sm.id==38){
                rest.post(sm.smsUrl + mobile + '&' + sm.smsUrl1 + '=' + message)
            } else {
                rest.post('http://api.smscountry.com/SMSCwebservice_bulk.aspx?User=' + sms_um + '&passwd=' + sms_pw + '&mobilenumber=' + mobile + '&message={message}&sid=' + sms_sid + '&mtype=N&DR=Y', requestParams)
            }
        }
        else if("true".equals(resend))
        {
               def requestParams = [message:message]
                if (sm.smsUrl != null && sm.smsUrl != "") {
                    rest.post(sm.smsUrl + mobile + '&' + sm.smsUrl1 + '=' + message)
                } else {
                    rest.post('http://api.smscountry.com/SMSCwebservice_bulk.aspx?User=' + sms_um + '&passwd=' + sms_pw + '&mobilenumber=' + mobile + '&message={message}&sid=' + sms_sid + '&mtype=N&DR=Y', requestParams)
                }
        }
        return  true
    }

    boolean sendSMSForInstituteUser(siteId,String message,mobile){
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        RestBuilder rest = new RestBuilder()
            if (sm.smsUrl != null && sm.smsUrl != "") {
                rest.post(sm.smsUrl + mobile + '&' + sm.smsUrl1 + '=' + message)
            }
        return  true
    }
    boolean hasBoughtTestSeries(username,bookId){
        boolean boughtTestSeries = false
        BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(bookId,username)
        if(booksPermission!=null) {
            if("true".equals(booksPermission.testsPurchased)) boughtTestSeries=true
        }else{
           // need to add library / batch related conditions here
        }
    }

    boolean    canSeeResource(ResourceDtl resourceDtl,request,session) {
        boolean showDebug=false
        Integer siteId = getSiteId(request,session)
        if(resourceDtl!=null) {
            if((siteId==25 || siteId == 12 || siteId == 23) && hasLibraryAccess(request,siteId))return true
            if(siteId==21 && ("currentaffairs".equals(resourceDtl.quizMode) || "jobalerts".equals(resourceDtl.quizMode)))   return true
            if ("public".equals(resourceDtl.privacyLevel)) {
                if(showDebug) println(" public for resourceId=" + resourceDtl.id)
                return true
            }else if(springSecurityService.currentUser != null&&resourceDtl.createdBy.equals(springSecurityService.currentUser.username)){
                return true
            } else { // wonderslate check
                if(showDebug) println(" Wonderslate check=" + resourceDtl.id)
                if (resourceDtl.topicId != null && !"".equals("" + resourceDtl.topicId)) {
                    return (springSecurityService.currentUser != null &&
                            userManagementService.canSeeResource(springSecurityService.currentUser.username, resourceDtl.createdBy, resourceDtl.id, resourceDtl.sharing))
                } else if (resourceDtl.chapterId != null && !"".equals("" + resourceDtl.chapterId)) { //wonderpublish check
                    if(showDebug) println(" wonderpublish for resourceId=" + resourceDtl.id)
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                    BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)

                    if("true".equals("" + chaptersMst.previewChapter)||("published".equals(booksMst.status) &&(booksMst.price==null||booksMst.price.doubleValue()==0))) {
                        if(showDebug)     println(" preview chapter for resourceId=" + resourceDtl.id)
                        return true
                    } else {
                        if (springSecurityService.currentUser != null) {
                            boolean hasBookAccess = false
                            List booksList
                            if(siteId==1 || siteId==3 || siteId==28 || siteId==37 || siteId==38 || siteId==39 || siteId==46 || siteId==11){
                                if (wsLibraryService.bookAccessForUser(booksMst.id+"",request,session)) {
                                    hasBookAccess = true;
                                }
                            }else {
                                if (redisService.(springSecurityService.currentUser.username + "_" + "booksList") == null) {
                                    dataProviderService.getBooksListForUser()
                                }

                                booksList = new JsonSlurper().parseText(redisService.(springSecurityService.currentUser.username + "_" + "booksList"))
                                booksList.each { book ->
                                    if (Long.parseLong(""+book.id) == chaptersMst.bookId.longValue() && "book".equals(book.permissionType)) hasBookAccess = true
                                }
                            }

                            if (hasBookAccess) {
                                if(showDebug)    println(" booksPermission for resourceId=" + resourceDtl.id)
                                return true
                            } else {
                                //check for publishing thingy
                                if(showDebug)   println(" wonderpublish check  not bought this chapter")
                                User user =   dataProviderService.getUserMst(springSecurityService.currentUser.username)

                                if (("" + user.publisherId).equals("" + booksMst.publisherId) || user.authorities.any {
                                    it.authority == "ROLE_WS_CONTENT_CREATOR"
                                }) {
                                    if(showDebug) println(" checking publisher for resourceId=" + resourceDtl.id+" username="+springSecurityService.currentUser.username);
                                    return true
                                } else {
                                    if(showDebug) println("entered the last step and session siteId="+session["siteId"]);

                                    if(session["siteId"]!=null && session["siteId"].intValue()==9 && session["siteId"].intValue()==booksMst.siteId.intValue()){
                                        if(showDebug) println "its sage site and book"
                                        return true
                                    }

                                    return false
                                }
                            }
                        } else return false
                    }
                } else return false
            }
        } else {
            return  false
        }
    }

    def canSendOTP(String ipAddress){
        boolean canSend = false
        Date currentDate = new Date();
        int expirySeconds = 300
        if(redisService.("ipAddress_"+ipAddress)==null){
            redisService.memoize("ipAddress_"+ipAddress,[expire:expirySeconds]){
                "1"
            }
            redisService.memoize("ipAddress_"+ipAddress+"_time",[expire:expirySeconds]){
                ""+(currentDate.getTime()+(expirySeconds*1000))  // seconds to milliseconds and adding it to current time
            }
            canSend = true
        }
        else{
            int currentCount = Integer.parseInt(redisService.("ipAddress_"+ipAddress))
            if(currentCount<5){
                //delete the key first
                redisService.deleteKeysWithPattern("ipAddress_"+ipAddress)
                //calculate time pending.
                if(redisService.("ipAddress_"+ipAddress+"_time")!=null) {
                    long otpExpiryTime = Long.parseLong(redisService.("ipAddress_" + ipAddress + "_time"))
                    int newExpiry = Math.round((otpExpiryTime-currentDate.getTime()) / 1000)
                    redisService.memoize("ipAddress_" + ipAddress, [expire: newExpiry]) {
                        "" + (currentCount + 1)
                    }
                    canSend = true
                }

            }
        }

        return canSend
    }

    def addRelatedVideos(Long chapterId,String chapterName,String newChapter,SiteMst siteMst){
        YouTube youtube;
        ChaptersMst chaptersMst = ChaptersMst.findById(chapterId)
        long NUMBER_OF_VIDEOS_RETURNED = 25
        def apiKey
        def query
        boolean exceptionHappened = false
        BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
        if(booksTagDtl) query=chapterName+" class "+booksTagDtl.grade+" of "+booksTagDtl.syllabus;
        else query=chapterName
        String apiKeys = siteMst.googleApiKey;
        if(apiKeys.contains(",")){
            String[] apiKeysArray = apiKeys.split(',');
            int randomIndex =  Math.floor(Math.random() * 5)/1;
            apiKey = apiKeysArray[randomIndex];
        }else{
            apiKey = apiKeys;
        }
        List<SearchResult> searchResultList
        if(newChapter=="true")
        {
            try {
                    youtube = new YouTube.Builder(new NetHttpTransport(), new JacksonFactory(), new HttpRequestInitializer() {
                        public void initialize(HttpRequest request) throws IOException {
                        }
                    }).setApplicationName("YoutubeSearch").build();
                    // Define the API request for retrieving search results.
                    YouTube.Search.List search = youtube.search().list("id,snippet");
                    search.setKey(apiKey);
                    search.setQ(query)
                    // Restrict the search results to only include videos. See:
                    // https://developers.google.com/youtube/v3/docs/search/list#type
                    search.setType("video");
                    // To increase efficiency, only retrieve the fields that the
                    // application uses.
                    search.setFields("items(id/kind,id/videoId,snippet/title)");
                    search.setMaxResults(NUMBER_OF_VIDEOS_RETURNED);
                    // Call the API and print results.
                    SearchListResponse searchResponse = search.execute();
                    searchResultList = searchResponse.getItems();
                    Gson gson = new Gson();
                    String element = gson.toJson(searchResultList,new TypeToken<List>() {}.getType())
                    RelatedVideos relatedVideos = new RelatedVideos(chapterId: new Long(chaptersMst.id), videos: element.getBytes("UTF-8"))
                    relatedVideos.save(failOnError: true, flush: true)
            } catch (GoogleJsonResponseException e) {
                System.err.println("There was a service error: " + e.getDetails().getCode() + " : "
                        + e.getDetails().getMessage());
                exceptionHappened = true
            } catch (IOException e) {
                System.err.println("There was an IO error: " + e.getCause() + " : " + e.getMessage());
                exceptionHappened = true
            } catch (Throwable t) {
                t.printStackTrace();
                exceptionHappened = true
            }
            if(exceptionHappened){
                return true
            }
            else {
                return false
            }
        }
        else
        {
            RelatedVideos relatedVideos = RelatedVideos.findByChapterId(new Long(chaptersMst.id))
                try {
                    youtube = new YouTube.Builder(new NetHttpTransport(), new JacksonFactory(), new HttpRequestInitializer() {
                        public void initialize(HttpRequest request) throws IOException {
                        }
                    }).setApplicationName("YoutubeSearch").build();
                    // Define the API request for retrieving search results.
                    YouTube.Search.List search = youtube.search().list("id,snippet");
                    search.setKey(apiKey);
                    search.setQ(query)
                    // Restrict the search results to only include videos. See:
                    // https://developers.google.com/youtube/v3/docs/search/list#type
                    search.setType("video");
                    // To increase efficiency, only retrieve the fields that the
                    // application uses.
                    search.setFields("items(id/kind,id/videoId,snippet/title)");
                    search.setMaxResults(NUMBER_OF_VIDEOS_RETURNED);
                    // Call the API and print results.
                    SearchListResponse searchResponse = search.execute();
                    searchResultList = searchResponse.getItems();
                    Gson gson = new Gson();
                    String element = gson.toJson(searchResultList, new TypeToken<List>() {}.getType())
                    relatedVideos.videos = element.getBytes("UTF-8")
                    relatedVideos.save(failOnError: true, flush: true)
                } catch (GoogleJsonResponseException e) {
                    System.err.println("There was a service error: " + e.getDetails().getCode() + " : "
                            +e.getDetails().getMessage());
                    exceptionHappened = true
                } catch (IOException e) {
                    System.err.println("There was an IO error: " + e.getCause() + " : " + e.getMessage());
                    exceptionHappened = true
                } catch (Throwable t) {
                    t.printStackTrace();
                    exceptionHappened = true
                }
                if (exceptionHappened) {
                    return true
                } else {
                    return false
                }


        }
    }

    String decrypt(String text) {
        try {
            if (redisService.("securityKeys_1") == null) dataProviderService.getSecurityKeys("1")
            String securityKey = redisService.("securityKeys_1")
            byte[] decodedBytes = Base64.getDecoder().decode(text);
            Cipher ecipher = Cipher.getInstance("AES/CBC/PKCS5PADDING", "SunJCE");
            IvParameterSpec iv = new IvParameterSpec((securityKey).getBytes());
            SecretKey secretKey = new SecretKeySpec(securityKey.getBytes(), "AES");
            ecipher.init(Cipher.DECRYPT_MODE, secretKey, iv);

            return new String(ecipher.doFinal(decodedBytes));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "";
    }

    boolean isPublishServer(String url){
        boolean isPublishServer=false

        if(url.indexOf("localhost")>-1||url.indexOf("qa.")>-1||url.indexOf("staging.")>-1||url.indexOf("publish")>-1||url.indexOf("dev.")>-1||url.indexOf("test.")>-1) isPublishServer = true
        return isPublishServer
    }

    boolean isWinnersPublishServer(String url){
        boolean isPublishServer=false

        if(url.indexOf("localhost")>-1||url.indexOf("qa.")>-1||url.indexOf("staging.")>-1||url.indexOf("winnerspublish.")>-1||url.indexOf("dev.")>-1) isPublishServer = true
        return isPublishServer
    }

    @Transactional
    def getInstituteId(request,siteId){
        String ipAddress = getIPAddressOfClient(request)
        def instituteId = null
        InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress,siteId)
        if(instituteIPAddress==null){
            if(springSecurityService.currentUser!=null) {
                String sql = "select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                        " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'  and im.site_id=" + siteId;
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                if (results.size() > 0) {
                    instituteId = results[0][0]
                }
            }
        }else{
            instituteId = instituteIPAddress.institute_id
        }
        return instituteId
    }



    static navigationHelper(session,request, String entryController) {

        String hostName = request.getServerName()
        String siteId = null
        //step 1 , find the client domain existence.
        if(redisService.("siteDomainNames")==null) {
            redisService.("siteDomainNames") = "set"
            String domainName
            List sites = SiteMst.findAllBySiteDomainNameIsNotNull()
            sites.each { site ->
                domainName = site.siteDomainName
                redisService.("siteDomainNames_"+site.siteDomainName) = ""+site.id
                //now look if the domain name has www
                if(domainName.indexOf("www")==-1){
                    //domain doesn't have www so add one more check for www
                    redisService.("siteDomainNames_www."+site.siteDomainName) = ""+site.id
                }else{
                    //it has domain name so remove www and add it
                    domainName = domainName.substring(4)
                    redisService.("siteDomainNames_"+domainName) = ""+site.id
                }

            }
        }
        if(redisService.("siteDomainNames_"+hostName)!=null){
            //found the client server url
            siteId = redisService.("siteDomainNames_"+hostName)
        }else {
            //subdomain check
            String url = request.getRequestURL()
            String subdomain = ""
            if (url.indexOf("http://localhost") > -1) {
                subdomain = "localhost"
            } else if (url.indexOf("www") > -1) {
                subdomain = url.substring(url.indexOf('.') + 1, url.indexOf(".", url.indexOf(".") + 1))
            } else {
                subdomain = url.substring(url.indexOf(':') + 3, url.indexOf('.'))
            }
            if (redisService.("siteNames") == null) {
                redisService.("siteNames") = "set"
                List sites = SiteMst.findAll()
                sites.each { site ->
                    redisService.("siteNames_" + site.siteName) = "" + site.id

                }
            }

            if (redisService.("siteNames_" + subdomain) != null) {
                //add the entry controller information
                siteId = redisService.("siteNames_" + subdomain)
            } else {
                if (redisService.("institutionsUrl") == null) {
                    redisService.("institutionsUrl") = "set"

                    List institutes = InstituteMst.findAllByUrlnameIsNotNull()
                    institutes.each { institute ->
                        redisService.("institutionsUrl_" + institute.urlname) = "" + institute.id

                    }
                }
                if (redisService.("institutionsUrl_" + subdomain) != null) {
                    siteId = "1"
                    session['siteId'] = new Integer(1);
                    session.setAttribute("entryController", "books")
                    session.setAttribute("siteName", "Wonderslate")
                    session.setAttribute("loginType", "email,mobile")
                    session.setAttribute("siteNameUrl", "books")
                    InstituteMst instituteMst = dataProviderService.getInstituteMstByUrl(session['siteId'], subdomain)
                    if (instituteMst.urlname != null) {
                        session.setAttribute('instituteUrlName', instituteMst.urlname)
                        session.setAttribute('instituteId', instituteMst.id)
                        session.setAttribute('instituteLogo', instituteMst.logo)
                    }
                    session['fromInstitutePageinstituteId'] = instituteMst.id
                    if (instituteMst.level != null) {
                        session['instituteLevel'] = instituteMst.level
                    }
                    if (instituteMst.syllabus != null) {
                        session['instituteSyllabus'] = instituteMst.syllabus
                    }
                }
            }

        }
            SiteMst siteMst
            if(siteId!=null){
                siteMst = dataProviderService.getSiteMst(new Integer(siteId))
            }
            else{
                siteMst = dataProviderService.getSiteMstBySiteName(entryController)
            }

            if(siteMst!=null){
                session['entryController'] = siteMst.siteName;
                session['siteId'] = siteMst.id;

                if("true".equals(siteMst.prepjoySite)){
                    session['entryController'] ="prepjoy"
                    session['siteName'] = siteMst.siteName
                    session.setAttribute("prepjoySite", "true")
                }
                else if("true".equals(siteMst.commonWhiteLabel)){
                    session['entryController'] ="privatelabel"
                    session['siteName'] = siteMst.siteName
                    session.setAttribute("commonWhiteLabel", "true")
                }
            }
            else{
                //worst case
                session['entryController'] = "books";
                session['siteId'] = new Integer(1);
            }


    }

   def convertAndFormatDateToIST(Date date) {
       if(date!=null) {
           // Create a SimpleDateFormat instance for the desired format
           SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm");

           // Set the time zone of the SimpleDateFormat instance to GMT
           sdf.setTimeZone(TimeZone.getTimeZone("GMT"));

           // Format the date in GMT
           String dateInGMT = sdf.format(date);

           // Change the time zone of the SimpleDateFormat instance to IST
           sdf.setTimeZone(TimeZone.getTimeZone("IST"));

           // Parse the date in GMT to get a Date object
           Date dateInGMTObject;
           try {
               dateInGMTObject = sdf.parse(dateInGMT);
           } catch (ParseException e) {
               e.printStackTrace();
               return null;
           }

           // Format the date in IST
           String dateInIST = sdf.format(dateInGMTObject);

           return dateInIST;
       }else return ""
    }


}
