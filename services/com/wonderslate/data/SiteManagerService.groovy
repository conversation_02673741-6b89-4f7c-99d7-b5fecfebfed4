package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.shop.WsshopService
import com.wonderslate.toDo.ToDoService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.transaction.Transactional

import javax.servlet.http.Cookie

@Transactional
class SiteManagerService {

    DataProviderService dataProviderService
    def springSecurityService
    UserManagementService userManagementService
    def redisService
    ToDoService toDoService
    UtilService utilService
    WsshopService wsshopService

    def setPrepjoySiteInformation(String siteName,session,response,servletContext){
        SiteMst siteMst = dataProviderService.getSiteMst(new Integer(27))
        boolean siteFound = false
        if(siteMst!=null) {
            println("siteName="+siteMst.siteName)
            siteFound = true
            Cookie cookie = new <PERSON>ie("siteName", siteMst.siteName)
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = siteMst.id;
            session.setAttribute("entryController", "prepjoy");
            session.setAttribute("wonderpublish", "true");
            session.setAttribute("siteName", siteMst.siteName)
            session.setAttribute("prepjoySite", siteMst.prepjoySite) // To know prepjoy site or not
            if (servletContext.getAttribute("googleLogEnabled") == null) {
                if ("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled", "true")
                else servletContext.setAttribute("googleLogEnabled", "false")
            }


            if (springSecurityService.currentUser != null) {
                if (session.getAttribute("userdetails") == null) {
                    session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
                }
                String username=springSecurityService.currentUser.username
                userManagementService.updateUserDisplayInformation(springSecurityService.currentUser.username,session)
                if(redisService.("usersCartBooksDetails_"+username)==null){
                    dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
                }
                session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
            }else{
                String username = session["siteId"]+session.getId()+"_temp"
                if(redisService.("usersCartBooksDetails_"+username)==null){
                    dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
                }
                session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
            }
            if(session["activeCategories_" + session["siteId"]] == null) {
                if(redisService.("activeCategories_" + session["siteId"]) == null) wsshopService.activeCategories(new Integer(""+session["siteId"]))
                session["activeCategories_" + session["siteId"]] = redisService.("activeCategories_" + session["siteId"])
            }
            // Active Categories & Syllabus
            if(session["activeCategoriesSyllabus_" + session["siteId"]] == null) {
                if(redisService.("activeCategoriesSyllabus_" + session["siteId"]) == null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(""+session["siteId"]))
                session["activeCategoriesSyllabus_" + session["siteId"]] = redisService.("activeCategoriesSyllabus_" + session["siteId"])
            }

            if(session["activeGrades_" + session["siteId"]] == null) {
                if(redisService.("activeGrades_" + session["siteId"])==null) wsshopService.getActiveGrades(new Integer(""+session["siteId"]))
                session["activeGrades_" + session["siteId"]] = redisService.("activeGrades_" + session["siteId"])
            }
            if(session["activeSubjects_" + session["siteId"]] == null) {
                if(redisService.("activeSubjects_" + session["siteId"])==null) wsshopService.getActiveSubjects(new Integer(""+session["siteId"]))
                session["activeSubjects_" + session["siteId"]] = redisService.("activeSubjects_" + session["siteId"])
            }
            if (session["googleUAId_" + siteMst.id] == null) {
                if (redisService.("googleUAId_" + siteMst.id) == null) dataProviderService.getGoogleUniversalAnalytics("" + siteMst.id)
                session["googleUAId_" + siteMst.id] = redisService.("googleUAId_" + siteMst.id)
            }
        }
        return siteFound
    }

    def setBooksUserSession(request,session,response,servletContext){
        session['siteId'] = new Integer(1);
        if (springSecurityService.currentUser != null&&session["userSessionDetailsSet"]==null) {
            String username=springSecurityService.currentUser.username

            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            } else if (session["userdetails"].publisherId != null && session["isInstitutePublisher"] == null) {
                session["isInstitutePublisher"] = userManagementService.isInstitutePublisher()
            }
            if(session.getAttribute("userdetails").authorities.any {
                it.authority == "ROLE_INSTITUTE_MANAGER"
            }) {
                def instituteManagerInstituteId = userManagementService.getInstituteForInstituteManager(username, session["siteId"])
                if(instituteManagerInstituteId!=-1) session["instituteManagerInstituteId"] = ""+instituteManagerInstituteId else session["instituteManagerInstituteId"] = null
            }
            if (session.getAttribute("userPendingTodoCount") == null) {
                toDoService.pendingToDoCount()
            }
            //get user basic analytics
            userManagementService.updateUserDisplayInformation(springSecurityService.currentUser.username,session)
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
            //institutes for user
            List userInstitutes
            if (session["userInstitutes"] != null) userInstitutes = session["userInstitutes"]
            else {
                userInstitutes = userManagementService.getInstitutesForUser(1, utilService.getIPAddressOfClient(request))
                if (userInstitutes.size() > 0) session["userInstituteId"] = userInstitutes[0].id
                if (userInstitutes.size() > 1) {
                    for (int i = 0; i < userInstitutes.size(); i++) {
                        if (!("Default".equals("" + userInstitutes[i].batchName))) {
                            userInstitutes.remove(i--)
                        }

                    }
                }
                session["userInstitutes"] = userInstitutes
            }
            session["userSessionDetailsSet"] = "true"
        }else{
            String username = session["siteId"]+session.getId()+"_temp"
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }

        if(session["userSessionSet"]==null) {
            if (servletContext.getAttribute("googleLogEnabled") == null) {
                if ("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled", "true")
                else servletContext.setAttribute("googleLogEnabled", "false")
            }
            if (session["activeCategories_1"] == null) {
                if (redisService.("activeCategories_1") == null) wsshopService.activeCategories(new Integer(1))
                session["activeCategories_1"] = redisService.("activeCategories_1")
            }
            if (session["activeCategoriesSyllabus_1"] == null) {
                if (redisService.("activeCategoriesSyllabus_1") == null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(1))
                session["activeCategoriesSyllabus_1"] = redisService.("activeCategoriesSyllabus_1")
            }
            if(session["activeGrades_1"] == null) {
                if(redisService.("activeGrades_1")==null) wsshopService.getActiveGrades(new Integer(1))
                session["activeGrades_1"] = redisService.("activeGrades_1")
            }
            if(session["activeSubjects_1"] == null) {
                if(redisService.("activeSubjects_1")==null) wsshopService.getActiveSubjects(new Integer(1))
                session["activeSubjects_1"] = redisService.("activeSubjects_1")
            }



            session.setAttribute("entryController", "books")
            session.setAttribute("siteName", "Wonderslate")
            session.setAttribute("loginType", "email,mobile")
            session.setAttribute("siteNameUrl", "books")
            session["googleUAId_1"] = null
            if (session["googleUAId_1"] == null) {
                if (redisService.("googleUAId_1") == null) {
                    dataProviderService.getGoogleUniversalAnalytics("1")
                }
                session["googleUAId_1"] = redisService.("googleUAId_1")
            }
            Cookie cookie = new Cookie("wlSiteName", "")

            cookie.path = "/"
            cookie.maxAge = 0
            response.addCookie(cookie)
            cookie = new Cookie("siteName", "books")
            cookie.path = "/"
            response.addCookie(cookie)
            utilService.navigationHelper(session, request, null)
            session["userSessionSet"] = "true"
        }

        return
    }
}
