package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.Quizrecorder
import com.wonderslate.publish.ExamDtl
import com.wonderslate.publish.ExamMst
import com.wonderslate.shop.BookPriceDtl
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import javax.xml.crypto.Data
import java.nio.file.Files
import java.nio.file.Paths
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

@Transactional
class MetainfoService {
    DataProviderService dataProviderService
    def redisService
    def grailsApplication
    UtilService utilService

    def getResourceDetails(chapterId,username){
        ExamMst examMst = null
        def examDtl = null
        List resources
        if(username!=null)
            resources = ResourceDtl.findAllByChapterIdAndCreatedByAndGptResourceTypeIsNull(chapterId,username)
            else
         resources = ResourceDtl.findAllByChapterIdAndSharingAndGptResourceTypeIsNull(chapterId,null)
        List jsonChapter = resources.collect { comp ->
            String fileType = "";

            if (comp.resLink != null && ("" + comp.resLink).lastIndexOf('.') != -1) {
                fileType = ("" + comp.resLink).substring(("" + comp.resLink).lastIndexOf('.') + 1)
            };
            boolean ebook=false
            if(comp.filename!=null&&(comp.filename.indexOf(".pdf")!=-1||comp.filename.indexOf(".zip")!=-1)) ebook=true
            def testStartDate="",testEndDate="",testResultDate=""
            if(comp.testStartDate!=null) testStartDate = utilService.convertDate(comp.testStartDate,"UTC","IST")
            if(comp.testEndDate!=null) testEndDate = utilService.convertDate(comp.testEndDate,"UTC","IST")
            if(comp.testResultDate!=null) testResultDate = utilService.convertDate(comp.testResultDate,"UTC","IST")
            if (comp.examId != null) {
                examMst = dataProviderService.getExamMst(comp.examId)
                if (examMst != null) {
                    if(redisService.("examDtl_" +examMst.id)==null) dataProviderService.getExamDtls(examMst.id)
                    examDtl = redisService.("examDtl_" +examMst.id)
                }
            }
            return [id                 : comp.id, topicId: comp.chapterId, resType: comp.resType, resLink:comp.resLink!=null && comp.resLink!="blank"?URLEncoder.encode( comp.resLink, "UTF-8" ):null,
                    resName: (comp.resourceName!=null&&!"".equals(comp.resourceName))?(comp.resourceName):"",
                    dateCreated: (""+comp.dateCreated).replaceAll(':','#'),
                    downloadlink1:  comp.downloadlink1!=null?comp.downloadlink1.replaceAll(':','#'):"",
                    downloadlink2:   comp.downloadlink2!=null?comp.downloadlink2.replaceAll(':','#'):"",
                    downloadlink3:  comp.downloadlink3!=null?comp.downloadlink3.replaceAll(':','#'):"",
                    language1   : (comp.language1 != null) ? comp.language1 : "",
                    language2   : (comp.language2 != null) ? comp.language2 : "",
                    totalMarks : examMst?examMst.totalMarks:"",
                    totalQuestion : examMst?examMst.noOfQuestions:"",
                    totalTime :examMst?examMst.totalTime:"",
                    fileType: fileType, fileSize: comp.fileSize,
                    eBook : ebook,filename:comp.filename!=null?URLEncoder.encode( comp.filename,"UTF-8"):null,
                    testStartDate: (""+testStartDate).replaceAll(':','#'),testEndDate: (""+testEndDate).replaceAll(':','#'),
                    videoPlayer:comp.videoPlayer==null?"youtube":comp.videoPlayer,
                    allowComments:comp.allowComments, displayComments:comp.displayComments,
                    testResultDate: (""+testResultDate).replaceAll(':','#'),
                    chapterDesc:(" "+comp.chapterDesc).replace(':', ' ').replace(',', '').replace('[', ' ').replace(']', ' ').replace('{', ' ').replace('}', ' '),
                    subType:comp.resSubType,
                    allowReAttempt:comp.allowReAttempt!=null?comp.allowReAttempt:"",
                    ebupChapterLink:comp.ebupChapterLink]

        }

        redisService.("defaultChapterDetailOptimised_"+chapterId) = dataProviderService.addDoubleQuotes(dataProviderService.convertToJsonString(jsonChapter.toString()))
    }

    def collectSingleChapterDetails(chapterId){
        getResourceDetails(chapterId,null)
        def defaultResources = redisService.("defaultChapterDetailOptimised_"+chapterId)
        if(redisService.("suggestedVideos_"+chapterId)==null) dataProviderService.getRelatedVideosFromDB((""+chapterId))


        def json =
                [
                        'defaultResources': defaultResources,
                        'status' : defaultResources ? "OK" : "Nothing present",
                         chapterId: chapterId,
                         suggestedVideos:redisService.("suggestedVideos_"+chapterId)

                ]

        return json


    }

    def getAllChaptersMetaInfo(bookId)
    {
        dataProviderService.getChaptersList(new Long(bookId));
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        List bookPriceDtls = BookPriceDtl.findAllByBookId(booksMst.id)
        Boolean testSeriesBook = false
        Double upgradePrice = null
        bookPriceDtls.each{ bookPrice->
            if("testSeries".equals(bookPrice.bookType)){
                testSeriesBook = true
            }
            if("upgrade".equals(bookPrice.bookType)){
                upgradePrice = bookPrice.sellPrice
            }
        }
       List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" +bookId))
        def jsonChapterDetails = []

        for(int i=0;i<chaptersList.size();i++) {
            jsonChapterDetails << collectSingleChapterDetails(new Long(chaptersList[i].id))
        }
        def json = ['jsonChapterDetails':jsonChapterDetails,
                    'chaptersList':chaptersList,testSeriesBook:testSeriesBook,upgradePrice:upgradePrice,genericReader: booksMst.genericReader?true:false]

        String uploadPath = grailsApplication.config.grails.basedir.path + "upload/books/" + bookId + "/metadata"
        createJsonZipFile(json as JSON,uploadPath,"json.txt")
        redisService.("bookDefaultResources_"+bookId)="true"
    }

    def createJsonZipFile(JSON json, String uploadPath,String filename){
        File uploadDir = new File(uploadPath)
        if(!uploadDir.exists()) uploadDir.mkdirs()
        try {
            File jsonFile = new File(uploadPath+"/"+filename)
            FileWriter myWriter = new FileWriter(jsonFile);
            myWriter.write(json);
            myWriter.close();
            String zipFileName = jsonFile.getName().concat(".zip");

            FileOutputStream fos = new FileOutputStream(uploadPath+"/"+zipFileName);
            ZipOutputStream zos = new ZipOutputStream(fos);

            zos.putNextEntry(new ZipEntry(jsonFile.getName()));

            byte[] bytes = Files.readAllBytes(Paths.get(uploadPath+"/"+filename));
            zos.write(bytes, 0, bytes.length);
            zos.closeEntry();
            zos.close();
            jsonFile.delete()
        } catch (IOException e) {
            System.out.println("An error occurred.");
            e.printStackTrace();
        }
    }

    def getPriceAndDescription(bookId){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        def json = [
                'bookDesc': booksMst != null ? booksMst.description : null, 'price': booksMst != null ? booksMst.price : null
        ]

        String uploadPath = grailsApplication.config.grails.basedir.path + "upload/books/" + bookId + "/metadata"
        createJsonZipFile(json as JSON,uploadPath,"desc.txt")
        redisService.("bookDescription_"+bookId)="true"
    }

    def quizQuestionAnswers(resId) {
        ResourceDtl resourceDtl;
        List answers
        String isPassage = "false";
        String passage = "";
        String description;
         String resourceName
        ExamMst examMst = null
        List examDtl = null
        String testSeries="false";
        def testEndDate=null
        String chapterName;

            resourceDtl = dataProviderService.getResourceDtl(new Long(resId))
            resourceName = resourceDtl.resourceName
            description = resourceDtl.description
            if(resourceDtl.examId!=null) {
                examMst = ExamMst.findById(resourceDtl.examId)
                if(examMst!=null) examDtl = ExamDtl.findAllByExamId(examMst.id)
            }

                answers = ObjectiveMst.findAllByQuizId(new Integer(resourceDtl.resLink), [sort: "id"])
                // logic to remove expired questions
                for (Iterator<String> iter = answers.listIterator(); iter.hasNext(); ) {
                    ObjectiveMst question = iter.next();
                    if(question.expiryDate!=null&&question.expiryDate.compareTo(new Date())<0){
                        iter.remove()
                    }
                }
                if ("passage".equals(resourceDtl.quizMode)) {
                    isPassage = "true";
                    passage = resourceDtl.chapterDesc;
                }
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                chapterName = chaptersMst.name
                BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                if(resourceDtl.testStartDate!=null&&resourceDtl.testEndDate!=null){
                    testSeries="true"
                    testEndDate = utilService.convertDate(resourceDtl.testEndDate,"UTC","IST")
                }
                if("test".equals(booksMst.bookType)) {
                    testSeries="true"
                    testEndDate= booksMst.testEndDate
                };

            String directions, section
            int noOfAnswers = 0;
            List jsonAnswers = answers.collect { quiz ->
                noOfAnswers = 0;
                if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
                    directions = quiz.directions
                    section = quiz.section
                return [id         : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                        resType    : quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox",  ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                        directions: quiz.directions, section: quiz.section,
                        answerDescription: quiz.answerDescription, answer: (quiz.answer!=null? new String(quiz.answer, "UTF-8"):""),subject:quiz.subject,
                        chapterId: quiz.chapter, quizId: quiz.quizId,marks:quiz.marks,negativeMarks: quiz.negativeMarks,explainLink: quiz.explainLink,startTime: quiz.startTime,endTime: quiz.endTime]
            }

            def json =
                    [
                            'results'    : jsonAnswers,
                            'status'     : jsonAnswers ? "OK" : "Nothing present",
                            'isPassage'  : isPassage,
                            'passage'    : passage,
                            'description': description,
                            'resourceName':resourceName,
                            'language1' : (resourceDtl!=null)?resourceDtl.language1:"",
                            'language2' : (resourceDtl!=null)?resourceDtl.language2:"",
                            'examSubject' : (resourceDtl!=null)?resourceDtl.examSubject:"",
                            'examMst' : examMst,
                            'examDtl' : examDtl,
                            'testSeries':testSeries,
                            'testEndDate':testEndDate,
                            'chapterName':chapterName

                    ]

        String uploadPath = grailsApplication.config.grails.basedir.path + "upload/quiz/" + resId
        createJsonZipFile(json as JSON,uploadPath,"quiz.txt")
        redisService.("quiz_"+resId)="true"


    }
}
