package com.wonderslate.data

import com.wonderslate.log.McqExtractLog
import com.wonderslate.log.McqTranslationLog
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.apache.commons.io.FileUtils
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@Transactional
class PdfExtractorService {
    PromptService promptService
    def springSecurityService
    UtilService utilService
    def grailsApplication

    def serviceMethod() {

    }

    def processPdfImages(Long bookId, Long chapterId, Long resId, List<MultipartFile> imageFiles, boolean isQuizImages, boolean isExtractedImages, List<MultipartFile> croppedImages) {
        try {
            String uploadParentDir = "supload"
            def baseDir = "${uploadParentDir}/pdfextracts/${bookId}/${chapterId}/${resId}"
            def imageDir = isQuizImages ? "${baseDir}/extractedQuizImages" : "${baseDir}/full_images"

            // Ensure image directory exists
            File imageUploadDir = new File(imageDir)
            if (!imageUploadDir.exists()) {
                imageUploadDir.mkdirs()
            }

            // Store croppedImages in page-specific folders
            def pageFolders = [:]

            croppedImages.each { MultipartFile file ->
                if (!file.empty) {
                    String filename = file.originalFilename
                    def pageMatcher = filename =~ /(page_\d+)/

                    if (pageMatcher.find()) {
                        String pageFolder = pageMatcher.group(1)
                        File pageDir = new File(baseDir, pageFolder) // Store cropped images in baseDir/page_X
                        if (!pageDir.exists()) {
                            pageDir.mkdirs()
                            pageFolders[pageFolder] = true
                        }

                        FileUtils.writeByteArrayToFile(new File(pageDir, filename), file.bytes)
                    } else {
                        FileUtils.writeByteArrayToFile(new File(baseDir, filename), file.bytes)
                    }
                }
            }

            // Store imageFiles in the appropriate folder (extractedQuizImages or fullImages)
            imageFiles.each { MultipartFile file ->
                if (!file.empty) {
                    String filename = file.originalFilename
                    FileUtils.writeByteArrayToFile(new File(imageUploadDir, filename), file.bytes)
                }
            }

            def images = getImagePaths(bookId, chapterId, resId, isQuizImages, isExtractedImages)
            return [
                    success: true,
                    message: "Files uploaded successfully",
                    data: [
                            bookId: bookId,
                            totalFiles: imageFiles.size(),
                            images: images["data"]["images"],
                            croppedImages: images["data"]["croppedImages"],
                            extractedImages: images["data"]["extractedImages"]
                    ]
            ]
        } catch (Exception e) {
            log.error("Error processing PDF images: ${e.message}", e)
            return [success: false, message: e.message]
        }
    }

    def getImagePaths(Long bookId, Long chapterId, Long resId, boolean isQuizImages, boolean isExtractedImages) {
        try {
            // Define the base directory path
            String uploadParentDir = "supload"
            def baseDir = "${uploadParentDir}/pdfextracts/${bookId}/${chapterId}/${resId}"
            def fullImagesDir = "${baseDir}/full_images"
            def quizImagesDir = "${baseDir}/extractedQuizImages"

            File directory = new File(baseDir)
            File fullImagesDirectory = new File(fullImagesDir)
            File quizImagesDirectory = new File(quizImagesDir)

            if (!directory.exists()) {
                return [
                        success: false,
                        message: "Directory not found for bookId: ${bookId}"
                ]
            }

            // Base URL for image links
            def baseUrl = "/funlearn"

            // Lists to store image URLs
            def imageLinks = []
            def croppedImages = []
            def extractedImages = []

            // Retrieve cropped images from page-specific folders
            directory.listFiles()?.each { File file ->
                if (file.isDirectory() && file.name.startsWith('page_')) {
                    file.listFiles()?.each { File imageFile ->
                        if (imageFile.isFile() && imageFile.name.toLowerCase().endsWith('.png')) {
                            def relativePath = "${baseDir}/${file.name}/${imageFile.name}"
                            def imageUrl = "${baseUrl}/downloadEpubImage?source=${relativePath}"
                            croppedImages << imageUrl
                        }
                    }
                }
            }

            // Retrieve full images
            if (fullImagesDirectory.exists()) {
                fullImagesDirectory.listFiles()?.each { File imageFile ->
                    if (imageFile.isFile() && imageFile.name.toLowerCase().endsWith('.png')) {
                        def relativePath = "${fullImagesDir}/${imageFile.name}"
                        def imageUrl = "${baseUrl}/downloadEpubImage?source=${relativePath}"
                        imageLinks << imageUrl  // Always store in imageLinks
                    }
                }
            }

            // Retrieve extracted quiz images if isQuizImages is true
            if (isQuizImages && quizImagesDirectory.exists()) {
                quizImagesDirectory.listFiles()?.each { File imageFile ->
                    if (imageFile.isFile() && imageFile.name.toLowerCase().endsWith('.png')) {
                        def relativePath = "${quizImagesDir}/${imageFile.name}"
                        def imageUrl = "${baseUrl}/downloadEpubImage?source=${relativePath}"
                        extractedImages << imageUrl  // Store in extractedImages
                    }
                }
            }

            // Return the structured response
            return [
                    success: true,
                    data: [
                            bookId: bookId,
                            imageCount: imageLinks.size() + extractedImages.size(),
                            images: imageLinks, // Always return full images
                            croppedImages: croppedImages,
                            extractedImages: extractedImages // Only populated if isQuizImages is true
                    ]
            ]

        } catch (Exception e) {
            log.error("Error getting image paths: ${e.message}", e)
            return [success: false, message: e.message]
        }
    }

    def fetchQuestions(def request, String apiEndpoint, def requestBody) {
        String baseUrl = promptService.getGPTServerUrl(request) + "/" + apiEndpoint
        URL url = new URL(baseUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write the JSON body - directly pass the complete requestBody
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)  // Use the entire requestBody
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                        success: true,
                        data: jsonResponse,
                        message: "MCQs extracted successfully"
                ]
            } else {
                // Read the error response body to get more details
                def reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                return [
                        success: false,
                        data: null,
                        message: "Error from server: ${responseCode}. Details: ${response.toString()}"
                ]
            }
        } catch (Exception e) {
            log.error("Error fetching questions: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def startExtraction(def request, String resId, boolean showProcessStatus = true, String username = null) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/extract"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            // Create a log entry with 'started' status
            McqExtractLog logEntry = new McqExtractLog(
                username: username,
                resId: resId ? Long.parseLong(resId) : null,
                startTime: new Date(),
                status: 'started'
            )
            logEntry.save(flush: true, failOnError: true)

            // Make the API call
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write the JSON body
            def requestBody = [
                resId: resId,
                showProcessStatus: showProcessStatus
            ]

            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())

                // Get the task ID from the response
                String taskId = jsonResponse.task_id

                // Update the log entry with the task ID from the response
                logEntry.taskId = taskId
                logEntry.status = 'in_progress'
                logEntry.save(flush: true)

                return taskId
            } else {
                // Read the error response body to get more details
                def reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                logEntry.status = 'error'
                logEntry.endTime = new Date()
                logEntry.save(flush: true)

                throw new Exception("Error from server: ${responseCode}. Details: ${response.toString()}")
            }

        } catch (Exception e) {
            log.error("Error starting extraction: ${e.message}", e)
            throw e
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def getTaskStatus(def request, String taskId) {
        try {
            // Find the log entry for this task
            McqExtractLog logEntry = McqExtractLog.findByTaskId(taskId)

            // If not found by taskId, try to find by other means
            if (!logEntry) {
                // Try to find by resId
                try {
                    Long resIdLong = Long.parseLong(taskId)
                    logEntry = McqExtractLog.findByResId(resIdLong)
                } catch (NumberFormatException e) {
                    // taskId is not a valid Long, so we can't find by resId
                }

                // If still not found, get the most recent extraction log
                if (!logEntry) {
                    logEntry = McqExtractLog.list(sort: 'startTime', order: 'desc', max: 1)[0]
                }
            }

            if (!logEntry) {
                return [success: false, status: 'not_found', message: "Task ID not found"]
            }

            // Always check the API for the latest status
            // This ensures we get real-time updates even if the database status hasn't changed
            String apiUrl = promptService.getGPTServerUrl(request) + "/task/" + taskId + "/status"
            URL url = new URL(apiUrl)
            HttpURLConnection connection = null

            try {
                connection = (HttpURLConnection) url.openConnection()
                connection.setRequestMethod("GET")
                connection.setRequestProperty("Content-Type", "application/json")
                connection.setRequestProperty("Accept", "application/json")
                connection.setDoOutput(false)
                connection.setDoInput(true)

                // No request body needed for GET request

                def responseCode = connection.getResponseCode()

                // Read response
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                    def response = new StringBuilder()
                    String line
                    while ((line = reader.readLine()) != null) {
                        response.append(line)
                    }
                    reader.close()

                    def jsonSlurper = new JsonSlurper()
                    def jsonResponse = jsonSlurper.parseText(response.toString())

                    // Update the log entry with the latest status
                    if (jsonResponse.status == 'completed') {
                        logEntry.status = 'completed'
                        logEntry.endTime = new Date()
                        logEntry.save(flush: true)
                    } else if (jsonResponse.status == 'failed') {
                        logEntry.status = 'error'
                        logEntry.endTime = new Date()
                        logEntry.save(flush: true)
                    } else {
                        logEntry.status = jsonResponse.status ?: 'in_progress'
                        logEntry.save(flush: true)
                    }

                    // Extract any steps or messages from the response
                    def steps = jsonResponse.steps ?: []
                    def currentStep = jsonResponse.current_step ?: jsonResponse.message ?: ""

                    // Normalize progress value if needed
                    def progress = jsonResponse.progress ?: 0

                    // Log the progress value for debugging
                    log.info("Progress value from API: ${progress}")

                    // Check for data in different possible locations
                    def resultData = null
                    if (jsonResponse.result) {
                        resultData = jsonResponse.result
                    } else if (jsonResponse.data) {
                        resultData = jsonResponse.data
                    }

                    // If status is completed but no data, try to get data from other sources
                    if (logEntry.status == 'completed' && !resultData) {
                        log.info("Status is completed but no data found in API response")
                    }

                    return [
                        success: true,
                        status: logEntry.status,
                        progress: progress,
                        message: currentStep,
                        steps: steps,
                        data: resultData,
                        result: resultData  // Include data in both fields for compatibility
                    ]
                } else {
                    // Read the error response body to get more details
                    def reader = new BufferedReader(new InputStreamReader(
                            responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                    ))
                    def response = new StringBuilder()
                    String line
                    while ((line = reader.readLine()) != null) {
                        response.append(line)
                    }
                    reader.close()

                    log.warn("API returned non-OK status: ${responseCode}. Details: ${response.toString()}")

                    // Even if the API call fails, return the current status from the database
                    // with a warning message
                    return [
                        success: true,
                        status: logEntry.status,
                        message: "Checking status... (API returned: ${responseCode})",
                        progress: 0
                    ]
                }
            } catch (Exception e) {
                log.error("Error checking task status from API: ${e.message}", e)

                // If the API call fails, return the current status from the database
                // with a warning message
                return [
                    success: true,
                    status: logEntry.status,
                    message: "Checking status... (API error: ${e.message})",
                    progress: 0,
                    startTime: logEntry.startTime,
                    endTime: logEntry.endTime,
                    duration: logEntry.endTime ? (logEntry.endTime.time - logEntry.startTime.time) / 1000 : null
                ]
            } finally {
                if (connection != null) {
                    connection.disconnect()
                }
            }

        } catch (Exception e) {
            log.error("Error getting task status: ${e.message}", e)
            return [success: false, error: e.message]
        }
    }

    def createMcqSolution(String sampleText, String questionText, List multipartFiles, def request) {

        String apiUrl = promptService.getGPTServerUrl(request) + "/create-solution"

        try {
            def http = new URL(apiUrl).openConnection() as HttpURLConnection
            def boundary = "----WebKitFormBoundary" + UUID.randomUUID().toString().replace("-", "")

            http.setRequestMethod("POST")
            http.setDoOutput(true)
            http.setRequestProperty("Content-Type", "multipart/form-data; boundary=${boundary}")

            def outputStream = http.getOutputStream()
            def writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true)

            // Add sample text if provided
            if (sampleText) {
                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"sample_text\"\r\n\r\n")
                writer.append("${sampleText}\r\n")
            }

            // Add question text if provided
            if (questionText) {
                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"question_text\"\r\n\r\n")
                writer.append("${questionText}\r\n")
            }

            // Add files
            multipartFiles.each { item ->
                def file = item.file
                def name = item.name

                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"${name}\"; filename=\"${file.originalFilename}\"\r\n")
                writer.append("Content-Type: ${file.contentType ?: 'application/octet-stream'}\r\n\r\n")
                writer.flush()

                // Write file data
                outputStream.write(file.bytes)
                outputStream.flush()

                writer.append("\r\n")
            }

            // End of multipart data
            writer.append("--${boundary}--\r\n")
            writer.flush()

            // Get response
            def status = http.getResponseCode()
            def response

            if (status == 200) {
                response = new JsonSlurper().parseText(http.getInputStream().getText())
            } else {
                throw new RuntimeException("API call failed with status ${status}: ${http.getErrorStream()?.getText()}")
            }

            return response
        } catch (Exception e) {
            log.error("Error in createMcqSolution: ${e.message}", e)
            return [success: false, message: "Error processing request: ${e.message}"]
        }
    }

    def extractAndValidateMcq(def request, String resId, def session, def totalQuestions = null, def explanationStartPage = null, def forceReextract = false) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/mcq-text-extractor"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)
            Integer siteId = utilService.getSiteId(request,session);
            // Create the request body
            def requestBody = [
                resId: resId,
                from_ws: true,
                username: springSecurityService.currentUser ? springSecurityService.currentUser.username : siteId+"_8754178781"
            ]

            // Add optional parameters if provided
            if (totalQuestions != null) {
                requestBody.total_questions = totalQuestions
            }
            if (explanationStartPage != null) {
                requestBody.explanation_start_page = explanationStartPage
            }
            if (forceReextract != null) {
                requestBody.force_reextract = forceReextract
            }

            // Write the JSON body
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                    success: true,
                    data: jsonResponse
                ]
            } else {
                // Read the error response body to get more details
                def reader = new BufferedReader(new InputStreamReader(
                    responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                return [
                    success: false,
                    data: null,
                    message: "Error from server: ${responseCode}. Details: ${response.toString()}"
                ]
            }
        } catch (Exception e) {
            log.error("Error extracting and validating MCQs: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def startMcqTranslation(MultipartFile pdfFile, Integer totalQuestions, String sourceLanguage, String destinationLanguage, String username, def request) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/mcq-translator-file"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {

            String translationId = UUID.randomUUID().toString()

            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Set up multipart form data
            String boundary = "----WebKitFormBoundary" + System.currentTimeMillis()
            connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary)

            OutputStream outputStream = connection.getOutputStream()
            PrintWriter writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true)

            // Add form fields
            addFormField(writer, boundary, "total_questions", totalQuestions.toString())
            addFormField(writer, boundary, "source_language", sourceLanguage)
            addFormField(writer, boundary, "destination_language", destinationLanguage)
            addFormField(writer, boundary, "username", username)
            addFormField(writer, boundary, "translationId", translationId)

            // Add file field
            addFileField(writer, outputStream, boundary, "pdfFile", pdfFile.originalFilename, pdfFile.bytes)

            // End multipart
            writer.append("--").append(boundary).append("--").append("\r\n")
            writer.close()

            def responseCode = connection.getResponseCode()

            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                    success: true,
                    data: jsonResponse,
                    translationId: translationId
                ]
            } else {
                def reader = new BufferedReader(new InputStreamReader(
                    responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                return [
                    success: false,
                    message: "Error from server: ${responseCode}. Details: ${response.toString()}"
                ]
            }
        } catch (Exception e) {
            log.error("Error starting MCQ translation: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def translateMcqFile(MultipartFile pdfFile, Integer totalQuestions, String sourceLanguage, String destinationLanguage, String username, def request) {
        try {
            // Generate unique translation ID
            String translationId = UUID.randomUUID().toString()

            // Create log entry
            McqTranslationLog logEntry = new McqTranslationLog(
                username: username,
                translationId: translationId,
                originalFileName: pdfFile.originalFilename,
                sourceLanguage: sourceLanguage,
                destinationLanguage: destinationLanguage,
                totalQuestions: totalQuestions,
                startTime: new Date(),
                status: 'started'
            )
            logEntry.save(flush: true, failOnError: true)

            // Call external API for MCQ translation
            String apiUrl = promptService.getGPTServerUrl(request) + "/mcq-translator-file"
            URL url = new URL(apiUrl)
            HttpURLConnection connection = null

            try {
                def boundary = "----WebKitFormBoundary" + UUID.randomUUID().toString().replace("-", "")

                connection = (HttpURLConnection) url.openConnection()
                connection.setRequestMethod("POST")
                connection.setDoOutput(true)
                connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=${boundary}")

                def outputStream = connection.getOutputStream()
                def writer = new PrintWriter(new OutputStreamWriter(outputStream, "UTF-8"), true)

                // Add form fields
                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"total_questions\"\r\n\r\n")
                writer.append("${totalQuestions}\r\n")

                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"source_language\"\r\n\r\n")
                writer.append("${sourceLanguage}\r\n")

                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"destination_language\"\r\n\r\n")
                writer.append("${destinationLanguage}\r\n")

                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"username\"\r\n\r\n")
                writer.append("${username}\r\n")

                // Add PDF file
                writer.append("--${boundary}\r\n")
                writer.append("Content-Disposition: form-data; name=\"pdfFile\"; filename=\"${pdfFile.originalFilename}\"\r\n")
                writer.append("Content-Type: application/pdf\r\n\r\n")
                writer.flush()

                // Write file bytes
                outputStream.write(pdfFile.bytes)
                outputStream.flush()

                writer.append("\r\n")
                writer.append("--${boundary}--\r\n")
                writer.flush()

                // Get response
                def responseCode = connection.getResponseCode()

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                    def response = new StringBuilder()
                    String line
                    while ((line = reader.readLine()) != null) {
                        response.append(line)
                    }
                    reader.close()

                    def jsonSlurper = new JsonSlurper()
                    def jsonResponse = jsonSlurper.parseText(response.toString())

                    // Update log entry with success
                    logEntry.status = 'completed'
                    logEntry.endTime = new Date()
                    logEntry.originalS3Path = jsonResponse.original_s3_path
                    logEntry.translatedS3Path = jsonResponse.translated_s3_path
                    logEntry.save(flush: true)

                    return [
                        success: true,
                        translationId: translationId,
                        originalS3Path: jsonResponse.original_s3_path,
                        translatedS3Path: jsonResponse.translated_s3_path,
                        message: "Translation completed successfully"
                    ]
                } else {
                    // Read error response
                    def reader = new BufferedReader(new InputStreamReader(
                        responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                    ))
                    def response = new StringBuilder()
                    String line
                    while ((line = reader.readLine()) != null) {
                        response.append(line)
                    }
                    reader.close()

                    // Update log entry with error
                    logEntry.status = 'error'
                    logEntry.endTime = new Date()
                    logEntry.errorMessage = "API Error: ${responseCode}. Details: ${response.toString()}"
                    logEntry.save(flush: true)

                    return [
                        success: false,
                        message: "Error from translation server: ${responseCode}. Details: ${response.toString()}"
                    ]
                }
            } finally {
                if (connection != null) {
                    connection.disconnect()
                }
            }

        } catch (Exception e) {
            log.error("Error in translateMcqFile: ${e.message}", e)
            return [success: false, message: "Internal error: ${e.message}"]
        }
    }

    def getTranslatedContent(String translationId, def request) {
        try {
            // Find the translation log entry
            McqTranslationLog logEntry = McqTranslationLog.findByTranslationId(translationId)

            if (!logEntry) {
                return [success: false, message: "Translation ID not found"]
            }

            if (logEntry.status != 'completed') {
                return [success: false, message: "Translation not completed yet"]
            }

            // Call external API to get translated content
            String apiUrl = promptService.getGPTServerUrl(request) + "/get-translated-mcq-content/" + translationId
            URL url = new URL(apiUrl)
            HttpURLConnection connection = null

            try {
                connection = (HttpURLConnection) url.openConnection()
                connection.setRequestMethod("GET")
                connection.setRequestProperty("Accept", "application/json")

                def responseCode = connection.getResponseCode()

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                    def response = new StringBuilder()
                    String line
                    while ((line = reader.readLine()) != null) {
                        response.append(line)
                    }
                    reader.close()

                    def jsonSlurper = new JsonSlurper()
                    def jsonResponse = jsonSlurper.parseText(response.toString())

                    return [
                        success: true,
                        data: jsonResponse
                    ]
                } else {
                    return [success: false, message: "Error fetching content from server: ${responseCode}"]
                }
            } finally {
                if (connection != null) {
                    connection.disconnect()
                }
            }

        } catch (Exception e) {
            log.error("Error in getTranslatedContent: ${e.message}", e)
            return [success: false, message: "Internal error: ${e.message}"]
        }
    }

    def downloadTranslatedFile(String translationId, def request, def response) {
        try {
            // Find the translation log entry
            McqTranslationLog logEntry = McqTranslationLog.findByTranslationId(translationId)

            if (!logEntry) {
                return [success: false, message: "Translation ID not found"]
            }

            if (logEntry.status != 'completed') {
                return [success: false, message: "Translation not completed yet"]
            }

            // Call external API to download translated file
            String apiUrl = promptService.getGPTServerUrl(request) + "/download-translated-mcq/" + translationId
            URL url = new URL(apiUrl)
            HttpURLConnection connection = null

            try {
                connection = (HttpURLConnection) url.openConnection()
                connection.setRequestMethod("GET")

                def responseCode = connection.getResponseCode()

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // Get content type and filename from headers
                    String contentType = connection.getHeaderField("Content-Type") ?: "application/octet-stream"
                    String contentDisposition = connection.getHeaderField("Content-Disposition")
                    String filename = "translated_mcq_" + translationId + ".txt"

                    if (contentDisposition && contentDisposition.contains("filename=")) {
                        filename = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9)
                        if (filename.startsWith("\"") && filename.endsWith("\"")) {
                            filename = filename.substring(1, filename.length() - 1)
                        }
                    }

                    // Set response headers
                    response.setContentType(contentType)
                    response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"")

                    // Stream the file content
                    def inputStream = connection.getInputStream()
                    def outputStream = response.getOutputStream()

                    byte[] buffer = new byte[4096]
                    int bytesRead
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead)
                    }

                    inputStream.close()
                    outputStream.flush()
                    outputStream.close()

                    return [success: true]
                } else {
                    return [success: false, message: "Error downloading file from server: ${responseCode}"]
                }
            } finally {
                if (connection != null) {
                    connection.disconnect()
                }
            }

        } catch (Exception e) {
            log.error("Error in downloadTranslatedFile: ${e.message}", e)
            return [success: false, message: "Internal error: ${e.message}"]
        }
    }

    def startMcqExtraction(def request, def requestBody) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/mcq-text-extractor"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write the JSON body
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                    success: true,
                    data: jsonResponse
                ]
            } else {
                // Read the error response body to get more details
                def reader = new BufferedReader(new InputStreamReader(
                    responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()
                ))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                return [
                    success: false,
                    message: "Error from server: ${responseCode}. Details: ${response.toString()}"
                ]
            }
        } catch (Exception e) {
            log.error("Error starting MCQ extraction: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def getMcqExtractorStatus(def request, String taskId) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/mcq-text-extractor/status"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Create the request body with task_id
            def requestBody = [task_id: taskId]

            // Write the JSON body
            def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
            def json = new JsonBuilder(requestBody)
            writer.write(json.toString())
            writer.flush()
            writer.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                    success: true,
                    data: jsonResponse
                ]
            } else {
                return [
                    success: false,
                    message: "Error from server: ${responseCode}"
                ]
            }
        } catch (Exception e) {
            log.error("Error getting MCQ extractor status: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def getMcqText(def request, String chapterId, String resId) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/get-mcq-text/${chapterId}/${resId}"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("GET")
            connection.setRequestProperty("Accept", "application/json")

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                    success: true,
                    data: jsonResponse
                ]
            } else {
                return [
                    success: false,
                    message: "Error from server: ${responseCode}"
                ]
            }
        } catch (Exception e) {
            log.error("Error getting MCQ text: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def downloadMcqTextFile(def request, def response, String chapterId, String resId) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/download-mcq-text/${chapterId}/${resId}"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("GET")

            def responseCode = connection.getResponseCode()

            if (responseCode == HttpURLConnection.HTTP_OK) {
                // Get content type and filename from headers
                String contentType = connection.getHeaderField("Content-Type") ?: "text/plain"
                String contentDisposition = connection.getHeaderField("Content-Disposition")
                String filename = "${chapterId}_${resId}.txt"

                if (contentDisposition && contentDisposition.contains("filename=")) {
                    filename = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9)
                    if (filename.startsWith("\"") && filename.endsWith("\"")) {
                        filename = filename.substring(1, filename.length() - 1)
                    }
                }

                // Set response headers
                response.setContentType(contentType)
                response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"")

                // Stream the file content
                def inputStream = connection.getInputStream()
                def outputStream = response.getOutputStream()

                byte[] buffer = new byte[4096]
                int bytesRead
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead)
                }

                inputStream.close()
                outputStream.flush()
                outputStream.close()

                return [success: true]
            } else {
                return [success: false, message: "Error downloading file from server: ${responseCode}"]
            }
        } catch (Exception e) {
            log.error("Error downloading MCQ text file: ${e.message}", e)
            return [success: false, message: "Internal error: ${e.message}"]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    def getMcqTranslatorStatus(def request, String taskId) {
        String apiUrl = promptService.getGPTServerUrl(request) + "/mcq-translator-file/status"
        URL url = new URL(apiUrl)
        HttpURLConnection connection = null

        try {
            connection = (HttpURLConnection) url.openConnection()
            connection.setRequestMethod("POST")
            connection.setRequestProperty("Content-Type", "application/json")
            connection.setRequestProperty("Accept", "application/json")
            connection.setDoOutput(true)
            connection.setDoInput(true)

            // Write the JSON body
            def requestBody = [task_id: taskId]
            def jsonBuilder = new JsonBuilder(requestBody)
            def outputStream = connection.getOutputStream()
            outputStream.write(jsonBuilder.toString().getBytes("UTF-8"))
            outputStream.flush()
            outputStream.close()

            def responseCode = connection.getResponseCode()

            // Read response
            if (responseCode == HttpURLConnection.HTTP_OK) {
                def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                def response = new StringBuilder()
                String line
                while ((line = reader.readLine()) != null) {
                    response.append(line)
                }
                reader.close()

                def jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(response.toString())
                return [
                    success: true,
                    data: jsonResponse
                ]
            } else {
                return [
                    success: false,
                    message: "Error from server: ${responseCode}"
                ]
            }
        } catch (Exception e) {
            log.error("Error getting MCQ translator status: ${e.message}", e)
            return [success: false, message: e.message]
        } finally {
            if (connection != null) {
                connection.disconnect()
            }
        }
    }

    private void addFormField(PrintWriter writer, String boundary, String name, String value) {
        writer.append("--").append(boundary).append("\r\n")
        writer.append("Content-Disposition: form-data; name=\"").append(name).append("\"").append("\r\n")
        writer.append("Content-Type: text/plain; charset=UTF-8").append("\r\n")
        writer.append("\r\n")
        writer.append(value).append("\r\n")
        writer.flush()
    }

    private void addFileField(PrintWriter writer, OutputStream outputStream, String boundary, String fieldName, String fileName, byte[] fileData) {
        writer.append("--").append(boundary).append("\r\n")
        writer.append("Content-Disposition: form-data; name=\"").append(fieldName).append("\"; filename=\"").append(fileName).append("\"").append("\r\n")
        writer.append("Content-Type: application/pdf").append("\r\n")
        writer.append("Content-Transfer-Encoding: binary").append("\r\n")
        writer.append("\r\n")
        writer.flush()

        outputStream.write(fileData)
        outputStream.flush()

        writer.append("\r\n")
        writer.flush()
    }
}
