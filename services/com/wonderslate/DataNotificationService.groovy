package com.wonderslate

import com.google.auth.oauth2.GoogleCredentials
import com.google.firebase.FirebaseApp
import com.google.firebase.FirebaseOptions
import com.google.firebase.messaging.ApnsConfig
import com.google.firebase.messaging.Aps
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.Message
import com.google.firebase.messaging.Notification
import com.google.firebase.messaging.TopicManagementResponse
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.MetainfoService
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.groups.GroupsPostDtl
import com.wonderslate.log.DeviceInformation
import com.wonderslate.log.NotificationDtl
import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class DataNotificationService {
    def grailsApplication
    def redisService
    DataProviderService dataProviderService
    MetainfoService metainfoService
    def initFSM(int siteId){
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String messageKeyFile = siteMst.fbMessageKeyFile

        String messageDatabaseUrl = siteMst.fbMessageDatabaseUrl
        println("messageKeyFile="+messageKeyFile)
        println("messageDatabaseUrl="+messageDatabaseUrl)
        FileInputStream serviceAccount =
                new FileInputStream(""+messageKeyFile);

        FirebaseOptions options = new FirebaseOptions.Builder()
                .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                .setDatabaseUrl(messageDatabaseUrl)
                .build();

        FirebaseApp.initializeApp(options,"siteId_"+siteId);

    }
    def initMessagingInstance(int siteId){
        try{
            FirebaseApp.getInstance("siteId_"+siteId)
        }catch (Exception e){
            println("Exception in getting the instance so initializing it")
            initFSM(siteId)
            FirebaseApp.getInstance("siteId_"+siteId)
        }
    }

    def infoChangedForCategoryBook(){

    }

    def latestBooksChanged(String bookId){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        Long siteId = booksMst.siteId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String topic = "Topic_Debug_User";
        if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
        if("true".equals(""+siteMst.sendNotification)) {
            boolean sendToOneAlso=false;
            if(siteId.intValue()!=1&&"Y".equals(siteMst.displayInMainSite)){
                sendToOneAlso=true
            }
            String sql = "select level,syllabus,grade from wsshop.books_tag_dtl where book_id=" + bookId + " group by level,syllabus,grade"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            initMessagingInstance(siteId.intValue())

            Message message
            String response
            List tags = results.collect { tag ->
                message = Message.builder()
                        .putData("title", "\"silent\"")
                        .putData("body", "\"silent\"")
                        .putData("level", tag.level)
                        .putData("syllabus", tag.syllabus)
                        .putData("grade", tag.grade)
                        .putData("messageType", "syllabusListChanged")
                           .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                        .setTopic(topic)
                        .build();
                try {
                    response = FirebaseMessaging.getInstance().send(message);
                    if(sendToOneAlso) {
                        initMessagingInstance(1)
                        response = FirebaseMessaging.getInstance().send(message)
                    };
                } catch (Exception e) {
                    println("Exception in latestBooksChanged for bookId=" + bookId)
                    println("Exception is " + e.toString())
                }
            }

            sql = "select distinct(level) from wsshop.books_tag_dtl where book_id=" + bookId
            dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql);
       
            tags = results.collect { tag ->
                message = Message.builder()
                        .putData("title", "\"silent\"")
                        .putData("body", "\"silent\"")
                        .putData("level", tag.level)
                        .putData("syllabus", "latest")
                        .putData("messageType", "syllabusListChanged")
                           .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                        .setTopic(topic)
                        .build();
                try {
                    response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);
                    if(sendToOneAlso) {
                        initMessagingInstance(1)
                        response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_1")).send(message)
                    };

                    //send to prepjoy sites
                    if(siteId.intValue()==1||"Y".equals(siteMst.displayInMainSite)){
                        List prepjoySites = SiteMst.findAllByPrepjoySite("true")
                        prepjoySites.each {prepjoySite->
                            initMessagingInstance(prepjoySite.id.intValue())
                            response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+prepjoySite.id)).send(message)
                        }
                    }
                } catch (Exception e) {
                    println("Exception in latestBooksChanged for bookId=" + bookId)
                    println("Exception is " + e.toString())
                }
            }
        }

    }

    def tagUpdated(level,syllabus){
        if("true".equals(grailsApplication.config.grails.appServer.sendNotification)) {
            initMessagingInstance()
            String topic = "Topic_Debug_User";
            if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
            Message message = Message.builder()
                    .putData("title", "\"silent\"")
                    .putData("body", "\"silent\"")
                    .putData("level", level)
                    .putData("syllabus", syllabus)
                    .putData("messageType", "syllabusListChanged")
                       .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                    .setTopic(topic)

                    .build();
            try {
                String response = FirebaseMessaging.getInstance().send(message);

            } catch (Exception e) {
                println("Exception in tagUpdated for level=" + level + " and syllabus=" + syllabus)
                println("Exception is " + e.toString())
            }
        }
    }

    def resourceUpdated(chapterId,bookId){
         redisService.deleteKeysWithPattern("defaultChapterDetail_"+chapterId)
        redisService.deleteKeysWithPattern("defaultChapterDetailOptimised_"+chapterId)
        metainfoService.getAllChaptersMetaInfo(bookId)

		//dataProviderService.getLiveVideos(new Integer(1))
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        Long siteId = booksMst.siteId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(""+siteMst.sendNotification)) {

            boolean sendToOneAlso=false;
            if(siteId.intValue()!=1&&"Y".equals(siteMst.displayInMainSite)){
                sendToOneAlso=true
            }
            initMessagingInstance(siteId.intValue())
            if(sendToOneAlso) initMessagingInstance(1)
            String topic = "Topic_Debug_User";
            if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
            Message message = Message.builder()
                    .putData("title", "\"silent\"")
                    .putData("body", "\"silent\"")
                    .putData("bookId", ""+bookId)
                    .putData("chapterId", ""+chapterId)
                    .putData("messageType", "resourceUpdated")
                       .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                    .setTopic(topic)
                    .build();
            try {

                String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);
                if(sendToOneAlso) {
                    initMessagingInstance(1)
                    response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_1")).send(message)

                }
                //send to prepjoy sites
                if(siteId.intValue()==1||"Y".equals(siteMst.displayInMainSite)){
                    List prepjoySites = SiteMst.findAllByPrepjoySite("true")
                    prepjoySites.each {prepjoySite->
                        initMessagingInstance(prepjoySite.id.intValue())
                        response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+prepjoySite.id)).send(message)
                    }
                }
            } catch (Exception e) {
                println("Exception in resourceUpdated for bookId=" + bookId + " and chapterId=" + chapterId)
                println("Exception is in resource updated " + e.toString())
            }
        }
    }

    def independentResourceUpdated(resId,siteId){
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(""+siteMst.sendNotification)) {

            initMessagingInstance(siteId.intValue())
            String topic = "Topic_Debug_User";
            if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
            ResourceDtl resourceDtl  = dataProviderService.getResourceDtl(new Long(resId))
            Message message = Message.builder()
                    .putData("title", "\"silent\"")
                    .putData("body", "\"silent\"")
                    .putData("bookId", "-1")
                    .putData("chapterId", "-1")
                    .putData("resId", ""+resId)
                    .putData("resType", ""+resourceDtl.quizMode)
                    .putData("messageType", "resourceUpdated")
                    .setApnsConfig(ApnsConfig.builder()
                    .setAps(Aps.builder()
                    .setContentAvailable(true)
                    .build())
                    .build())
                    .setTopic(topic)
                    .build();
            try {

                String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);
            } catch (Exception e) {
                println("Exception in resourceUpdated for resId=" + resId)
                println("Exception is in resource updated " + e.toString())
            }
        }
    }

    def prepjoyContentNotification(String currentAffairsType){
        List sites = SiteMst.findAllByCurrentAffairsType(currentAffairsType)
        sites.each { siteMst ->
             if ("true".equals("" + siteMst.sendNotification)) {

                initMessagingInstance(siteMst.id.intValue())
                String topic = "Topic_Debug_User";
                if ("true".equals(grailsApplication.config.grails.liveServer)) topic = "Topic_All_User"
                //ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(resId))
                Message message = Message.builder()
                        .putData("title", "\"silent\"")
                        .putData("body", "\"silent\"")
                        .putData("bookId", "-1")
                        .putData("chapterId", "-1")
                        .putData("resId", "-1" )
                        .putData("resType", "-1")
                        .putData("messageType", "resourceUpdated")
                        .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder()
                        .setContentAvailable(true)
                        .build())
                        .build())
                        .setTopic(topic)
                        .build();
                try {

                    String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + siteMst.id)).send(message);
                } catch (Exception e) {
                    println("Exception in prepjoyContentNotification ")
                    println("Exception is in resource updated " + e.toString())
                }
            }
        }
    }

    def readingMaterialUpdated(chapterId,bookId,resId){
        redisService.deleteKeysWithPattern("defaultChapterDetail_"+chapterId)
        redisService.deleteKeysWithPattern("defaultChapterDetailOptimised_"+chapterId)

        metainfoService.getAllChaptersMetaInfo(bookId)

        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        Long siteId = booksMst.siteId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(""+siteMst.sendNotification)) {
            boolean sendToOneAlso=false;
            if(siteId.intValue()!=1&&"Y".equals(siteMst.displayInMainSite)){
                sendToOneAlso=true
            }
            initMessagingInstance(siteId.intValue())
            if(sendToOneAlso) initMessagingInstance(1)
            String topic = "Topic_Debug_User";
            if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
            Message message = Message.builder()
                    .putData("title", "\"silent\"")
                    .putData("body", "\"silent\"")
                    .putData("bookId", ""+bookId)
                    .putData("chapterId", ""+chapterId)
                    .putData("resId", ""+resId)
                    .putData("messageType", "readingMaterialUpdated")
                       .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                    .setTopic(topic)
                    .build();
            try {
                String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);
                if(sendToOneAlso) {
                    initMessagingInstance(1)
                    response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_1")).send(message)
                }
                //send to prepjoy sites
                if(siteId.intValue()==1||"Y".equals(siteMst.displayInMainSite)){
                    List prepjoySites = SiteMst.findAllByPrepjoySite("true")
                    prepjoySites.each {prepjoySite->
                        initMessagingInstance(prepjoySite.id.intValue())
                        response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+prepjoySite.id)).send(message)
                    }
                }

            } catch (Exception e) {
                println("Exception in resourceUpdated for bookId=" + bookId + " and chapterId=" + chapterId)
                println("Exception is " + e.toString())
            }
        }
    }

    def quizUpdated(chapterId,bookId,resId){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        Long siteId = booksMst.siteId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(""+siteMst.sendNotification)) {
            boolean sendToOneAlso=false;
            if(siteId.intValue()!=1&&"Y".equals(siteMst.displayInMainSite)){
                sendToOneAlso=true
            }
            initMessagingInstance(siteId.intValue())
            if(sendToOneAlso) initMessagingInstance(1)
            String topic = "Topic_Debug_User";
            if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
            Message message = Message.builder()
                    .putData("title", "\"silent\"")
                    .putData("body", "\"silent\"")
                    .putData("bookId", ""+bookId)
                    .putData("chapterId", ""+chapterId)
                    .putData("resId", ""+resId)
                    .putData("messageType", "quizUpdated")
                       .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                    .setTopic(topic)
                    .build();
            try {
                String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);
                if(sendToOneAlso) {
                    initMessagingInstance(FirebaseApp.getInstance("siteId_1"))
                    response = FirebaseMessaging.getInstance().send(message)
                }
                //send to prepjoy sites
                if(siteId.intValue()==1||"Y".equals(siteMst.displayInMainSite)){
                    List prepjoySites = SiteMst.findAllByPrepjoySite("true")
                    prepjoySites.each {prepjoySite->
                        initMessagingInstance(prepjoySite.id.intValue())
                        response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+prepjoySite.id)).send(message)
                    }
                }

            } catch (Exception e) {
                println("Exception in resourceUpdated for bookId=" + bookId + " and chapterId=" + chapterId)
                println("Exception is " + e.toString())
            }
        }
    }

    @Transactional
    def sendNotificationToAll(NotificationDtl notificationDtl){
        initMessagingInstance(notificationDtl.siteId.intValue())
        String topic = "Topic_Debug_User";
        if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
        Message message = Message.builder()
                .putData("title", "\""+notificationDtl.title+"\"")
                .putData("body", "\""+notificationDtl.body+"\"")
                .putData("imageUrl", "\""+notificationDtl.imageUrl+"\"")
                .putData("id","\""+notificationDtl.id+"\"")
                .putData("link","\""+notificationDtl.link+"\"")
                .putData("userType","\"all\"")
                .putData("notificationId", "\"" + notificationDtl.id + "\"")
                   .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                .setTopic(topic)
                .build();
        try {
            String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+notificationDtl.siteId)).send(message);

        } catch (Exception e) {
            println("Exception in sendNotificationToAll ")
            println("Exception is " + e.toString())
        }
    }

    @Transactional
    def sendDeleteNotification(NotificationDtl notificationDtl){
        initMessagingInstance(notificationDtl.siteId.intValue())
        String topic = "Topic_Debug_User";
        if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
        Message message = Message.builder()
                .putData("title", "\"delete\"")
                .putData("body", "\"delete\"")
                .putData("imageUrl", "\""+notificationDtl.imageUrl+"\"")
                .putData("id","\""+notificationDtl.id+"\"")
                .putData("link","\""+notificationDtl.link+"\"")
                .putData("notificationId", "\"" + notificationDtl.id + "\"")
                .putData("messageType", "delete")
                .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                .setTopic(topic)
                .build();
        try {
            String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+notificationDtl.siteId)).send(message);

        } catch (Exception e) {
            println("Exception in sendDeleteNotification ")
            println("Exception is " + e.toString())
        }
    }

    @Transactional
    def sendNotificationToBook(NotificationDtl notificationDtl, String bookId){
        initMessagingInstance(notificationDtl.siteId.intValue())
        String topic = "Topic_Debug_User";
        if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"

        Long siteId = notificationDtl.siteId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        boolean sendToOneAlso=false;
        if(siteId.intValue()!=1&&"Y".equals(siteMst.displayInMainSite)){
            sendToOneAlso=true
        }
        Message message = Message.builder()
                .putData("title", "\""+notificationDtl.title+"\"")
                .putData("body", "\""+notificationDtl.body+"\"")
                .putData("imageUrl", "\""+notificationDtl.imageUrl+"\"")
                .putData("id","\""+notificationDtl.id+"\"")
                .putData("link","\""+notificationDtl.link+"\"")
                .putData("notificationId", "\"" + notificationDtl.id + "\"")
                .putData("bookId", "\"" + bookId + "\"")
                .putData("userType","\"book\"")
                .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                .setTopic(topic)
                .build();
        try {
            String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+notificationDtl.siteId)).send(message);
            if(sendToOneAlso) {
                initMessagingInstance(FirebaseApp.getInstance("siteId_1"))
                response = FirebaseMessaging.getInstance().send(message)
            }
            //send to prepjoy sites
            if(siteId.intValue()==1||"Y".equals(siteMst.displayInMainSite)){
                List prepjoySites = SiteMst.findAllByPrepjoySite("true")
                prepjoySites.each {prepjoySite->
                    initMessagingInstance(prepjoySite.id.intValue())
                    response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+prepjoySite.id)).send(message)
                }
            }

        } catch (Exception e) {
            println("Exception in sendNotificationToAll ")
            println("Exception is " + e.toString())
        }
    }

    @Transactional
    def sendNotificationToBatch(NotificationDtl notificationDtl, String batchId){
        initMessagingInstance(notificationDtl.siteId.intValue())
        String topic = "Topic_Debug_User";
        if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
        Message message = Message.builder()
                .putData("title", "\""+notificationDtl.title+"\"")
                .putData("body", "\""+notificationDtl.body+"\"")
                .putData("imageUrl", "\""+notificationDtl.imageUrl+"\"")
                .putData("id","\""+notificationDtl.id+"\"")
                .putData("link","\""+notificationDtl.link+"\"")
                .putData("notificationId", "\"" + notificationDtl.id + "\"")
                .putData("batchId", "\"" + batchId + "\"")
                .putData("userType","\"batch\"")
                .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                .setTopic(topic)
                .build();
        try {
            String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+notificationDtl.siteId)).send(message);

        } catch (Exception e) {
            println("Exception in sendNotificationToAll ")
            println("Exception is " + e.toString())
        }
    }

    @Transactional
    def sendNotificationToUser(NotificationDtl notificationDtl,String username){
        initMessagingInstance(notificationDtl.siteId.intValue())
        List deviceInformation = DeviceInformation.findAllByUsername(username)

        deviceInformation.each { deviceInfo ->
            Message message = Message.builder()
                    .putData("title", "\"" + notificationDtl.title + "\"")
                    .putData("body", "\"" + notificationDtl.body + "\"")
                    .putData("imageUrl", "\"" + notificationDtl.imageUrl + "\"")
                    .putData("id", "\"" + notificationDtl.id + "\"")
                    .putData("link", "\"" + notificationDtl.link + "\"")
                    .putData("notificationId", "\"" + notificationDtl.id + "\"")
                     .putData("userType","\"singleuser\"")
                       .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                    .setToken(deviceInfo.deviceId)
                    .build();
            try {

                String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + notificationDtl.siteId)).send(message);
            } catch (Exception e) {
                println("Exception in sendNotificationToUser. User="+username+" Device Id="+deviceInfo.deviceId)
                println("Exception is " + e.toString())
            }
        }
    }
    @Transactional
    def sendChatBlockNotificationToUser(String username,String blocked,String tempBlocked){
        initMessagingInstance(1)
        List deviceInformation = DeviceInformation.findAllByUsername(username)
        deviceInformation.each { deviceInfo ->
            Message message = Message.builder()
                    .putData("title", "\"silent\"")
                    .putData("body", "\"silent\"")
                    .putData("blocked", "\"" + blocked + "\"")
                    .putData("tempBlocked", "\"" + tempBlocked + "\"")
                    .setApnsConfig(ApnsConfig.builder()
                    .setAps(Aps.builder()
                    .setContentAvailable(true)
                    .build())
                    .build())
                    .setToken(deviceInfo.deviceId)
                    .build();
            try {

                String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + notificationDtl.siteId)).send(message);
            } catch (Exception e) {
                println("Exception in sendNotificationToUser. User="+username+" Device Id="+deviceInfo.deviceId)
                println("Exception is " + e.toString())
            }
        }
    }

    def sendCommentNotification(messageText,sender,resId,messageType,siteId,deviceId,messageId,adminUser){
        initMessagingInstance(siteId)
        Message message = Message.builder()
                .putData("title", "\"silent\"")
                .putData("body", "\"silent\"")
                .putData("message", "\"" +messageText+ "\"")
                .putData("sender", "\"" +sender+ "\"")
                .putData("resId", "\"" +resId+ "\"")
                .putData("messageId", "\"" +messageId+ "\"")
                .putData("messageType", "\"" +messageType+ "\"")
                .putData("adminUser", "\"" +adminUser+ "\"")
                .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                .setTopic(deviceId)
                .build();
        try {
            FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);
        } catch (Exception e) {
            println("Exception in sendCommentNotification")
            println("Exception is " + e.toString())
        }
    }
    def sendCommentNotificationToTopic(messageText,sender,resId,messageType,siteId,messageId,adminUser,chatMessageNo,toUser,pollId,pollDuration){
        initMessagingInstance(siteId)
        Message message = Message.builder()
                .putData("title", "\"silent\"")
                .putData("body", "\"silent\"")
                .putData("message", "\"" +messageText+ "\"")
                .putData("sender", "\"" +sender+ "\"")
                .putData("resId", "\"" +resId+ "\"")
                .putData("messageId", "\"" +messageId+ "\"")
                .putData("messageType", "\"" +messageType+ "\"")
                .putData("adminUser", "\"" +adminUser+ "\"")
                .putData("chatMessageNo", "\"" +chatMessageNo+ "\"")
                .putData("pollId", "\"" +pollId+ "\"")
                .putData("pollDuration", "\"" +pollDuration+ "\"")
                .putData("toUser", "\"" +toUser+ "\"")
                .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                .setTopic("chat"+resId)
                .build();
        try {
            FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);
        } catch (Exception e) {
            println("Exception in sendCommentNotification")
            println("Exception is " + e.toString())
        }
    }
    def sendChatStopStartToTopic(resId,startStop){
        initMessagingInstance(1)
        Message message = Message.builder()
                .putData("title", "\"silent\"")
                .putData("body", "\"silent\"")
                .putData("chat", "\"" +startStop+ "\"")
                .setApnsConfig(ApnsConfig.builder()
                .setAps(Aps.builder()
                .setContentAvailable(true)
                .build())
                .build())
                .setTopic("chat"+resId)
                .build();
        try {
            FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_1")).send(message);
        } catch (Exception e) {
            println("Exception in sendCommentNotification")
            println("Exception is " + e.toString())
        }
    }
    def addLiveVideoUser(deviceId,resId,siteId){
        try {
            if (deviceId != null && !"".equals(deviceId)) {
                List<String> registrationTokens = Arrays.asList(
                        "" + deviceId
                );
                initMessagingInstance(siteId)
                TopicManagementResponse response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + siteId)).subscribeToTopic(registrationTokens, "chat" + resId);
            } else {

            }
        }catch(Exception e){
            //do nothing
        }
        return ""
    }
    def removeLiveVideoUser(deviceId,resId,siteId){
        try {
            if (deviceId != null && !"".equals(deviceId)) {
                List<String> registrationTokens = Arrays.asList(
                        "" + deviceId
                );
                initMessagingInstance(siteId)
                TopicManagementResponse response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + siteId)).unsubscribeFromTopic(registrationTokens, "chat" + resId);
            } else {

            }
        }catch(Exception e){
            //do nothing
        }
       return ""
    }
    @Transactional
    def sendLogoutNotificationToUser(username,siteId,authenticationTokenString){
        initMessagingInstance(siteId.intValue())
        List deviceInformation = DeviceInformation.findAllByUsername(username)
        deviceInformation.each { deviceInfo ->
            Message message = Message.builder()
                    .putData("title", "\"Logout\"")
                    .putData("body", "\"Logout\"")
                    .putData("messageType", "\"Logout\"")
                    .putData("authenticationTokenString", "\"" +authenticationTokenString+ "\"")
                    .setApnsConfig(ApnsConfig.builder()
                    .setAps(Aps.builder()
                    .setContentAvailable(true)
                    .build())
                    .build())
                    .setToken(deviceInfo.deviceId)
                    .build();
            try {

               FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + siteId)).send(message);

            } catch (Exception e) {
                println("Exception in sendNotificationToUser. User="+username+" Device Id="+deviceInfo.deviceId)
                println("Exception is " + e.toString())
            }
        }
    }

    @Transactional
    def sendGroupPostNotification(String groupId,siteId,type="group",postCount,postId,description,postImage,adminUser){
        initMessagingInstance(siteId.intValue())

        String topic = "Topic_Debug_User";
        if("true".equals(grailsApplication.config.grails.liveServer)) topic="Topic_All_User"
        Message message = Message.builder()
                .putData("title", "\"New Post Created\"")
                .putData("groupId","\"" +groupId+ "\"")
                .putData("type","\"" +type+ "\"")
                .putData("postCount","\"" +postCount+ "\"")
                .putData("postId","\"" +postId+ "\"")
                .putData("description","\"" +description+ "\"")
                .putData("postImage","\"" +postImage+ "\"")
                .putData("adminUser","\"" +postImage+ "\"")
                .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder()
                                .setContentAvailable(true)
                                .build())
                        .build())
                .setTopic(topic)
                .build();
        try {
            String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_"+siteId)).send(message);

        } catch (Exception e) {
            println("Exception in sendNotification in Groups")
            println("Exception is " + e.toString())
        }
    }
    def prepjoyAlertNotification(String alertType){
        List sites = SiteMst.findAllByPrepjoySite("true")
        sites.each { siteMst ->
            if ("true".equals("" + siteMst.sendNotification)) {
                initMessagingInstance(siteMst.id.intValue())
                String topic = "Topic_Debug_User";
                if ("true".equals(grailsApplication.config.grails.liveServer)) topic = "Topic_All_User"
                Message message = Message.builder()
                        .putData("title", "\"silent\"")
                        .putData("body", "\"silent\"")
                        .putData("bookId", "-1")
                        .putData("chapterId", "-1")
                        .putData("resId", "-1" )
                        .putData("resType", "-1")
                        .putData("messageType", ""+alertType)
                        .setApnsConfig(ApnsConfig.builder()
                        .setAps(Aps.builder()
                        .setContentAvailable(true)
                        .build())
                        .build())
                        .setTopic(topic)
                        .build();
                try {
                    String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + siteMst.id)).send(message);
                } catch (Exception e) {
                    println("Exception in prepjoyContentNotification ")
                    println("Exception is in resource updated " + e.toString())
                }
            }
        }
    }

    @Transactional
    def sendSilentNotificationToUser(String subject,String messageText, String username, String siteId,String challengerName,String bookId){
        initMessagingInstance(Integer.parseInt(siteId))
        List deviceInformation = DeviceInformation.findAllByUsername(username)
        deviceInformation.each { deviceInfo ->
            Message message = Message.builder()
                    .putData("title", "\"" + subject + "\"")
                    .putData("body", "\"" + messageText + "\"")
                    .putData("bookId", "\"" + bookId + "\"")
                    .putData("challengerName", "\"" + challengerName + "\"")
                    .putData("chapterId", "-1")
                    .putData("resId", "-1" )
                    .putData("resType", "-1")
                    .putData("userType","\"singleuser\"")
                    .setApnsConfig(ApnsConfig.builder()
                    .setAps(Aps.builder()
                    .setContentAvailable(true)
                    .build())
                    .build())
                    .setToken(deviceInfo.deviceId)
                    .build();
            try {
                String response = FirebaseMessaging.getInstance(FirebaseApp.getInstance("siteId_" + siteId)).send(message);
            } catch (Exception e) {
                println("Exception in sendNotificationToUser. User="+username+" Device Id="+deviceInfo.deviceId)
                println("Exception is " + e.toString())
            }
        }
    }


}

