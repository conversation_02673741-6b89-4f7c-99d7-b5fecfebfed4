package com.wonderslate.librarybooks

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import grails.transaction.Transactional
import groovy.sql.Sql

import java.util.stream.Collectors
import java.util.stream.Stream

@Transactional
class LibraryBooksService {

    def redisService
    def grailsApplication
    DataProviderService dataProviderService

    String getAllBatchIds(String batchId){
        String batchIds = batchId
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(batchId))
        if(courseBatchesDtl!=null&&"Default".equals(courseBatchesDtl.name)){
            InstituteMst instituteMst = dataProviderService.getInstituteMst(courseBatchesDtl.conductedBy)
            if(instituteMst.associatedLibraries!=null&&!"".equals(instituteMst.associatedLibraries)){
                String [] associatedLibraries = instituteMst.associatedLibraries.split(",")
                for(String associatedLibrary:associatedLibraries){
                    batchIds += ","+CourseBatchesDtl.findByConductedBy(new Long(associatedLibrary)).id
                }
            }
        }
        return batchIds
    }

    def getInstituteBooksPagination(batchId, int pageNumber = 1) {
    String batchIds = getAllBatchIds(""+batchId)
    int noOfBooksPerPage = 30
    int offset = (pageNumber - 1) * noOfBooksPerPage

    String sql = """
    select bm.id, bm.title, bm.isbn, bm.status, bm.publisher_id, bbd.number_of_licenses,
           bbd.validity, bbd.batch_id, bm.cover_image, bm.price, bm.book_type,
           bm.language, bm.external_link, bm.test_type_book
    from wsshop.books_mst bm
    join wsuser.books_batch_dtl bbd on bm.id = bbd.book_id
    where (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())
      and bbd.batch_id in (${batchIds})
      and (bm.show_in_library='Yes' or bm.show_in_library is null)
    union
    select bm1.id, bm1.title, bm1.isbn, bm1.status, bm1.publisher_id, bbd.number_of_licenses,
           bbd.validity, bbd.batch_id, bm1.cover_image, bm1.price, bm1.book_type,
           bm1.language, bm1.external_link, bm1.test_type_book
    from wsshop.books_mst bm
    join wsuser.books_batch_dtl bbd on bm.id = bbd.book_id
    join wsshop.books_mst bm1 on bm.package_book_ids is not null and FIND_IN_SET(bm1.id, bm.package_book_ids) != 0
    where (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())
      and bbd.batch_id in (${batchIds})
     and bm1.site_id = bm.site_id
    order by id desc
    limit ${noOfBooksPerPage} offset ${offset}


    """

    def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
    def sql2 = new Sql(dataSource)
    def results = sql2.rows(sql)
        def instituteMst = null

        List books = results.collect { comp ->
            if(instituteMst==null)
            instituteMst = InstituteMst.findById(CourseBatchesDtl.findById(comp.batch_id.toLong()).conductedBy)
        String publisherName = ''
        if (comp.publisher_id) {
            def publishers = dataProviderService.getPublisher(comp.publisher_id.toLong())
            if (publishers) publisherName = publishers.name
        }

        def booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)

        [
            id: comp.id, title: comp.title, coverImage: comp.cover_image ?: "",
            bookStatus: comp.status ?: 'unpublished', batchId: comp.batch_id,
            publisher: publisherName, bookType: comp.book_type ?: "",
            bookLangauge: comp.language ?: "", noOfLic: comp.number_of_licenses ?: "",
            validity: comp.validity ?: "", level: booksTagDtl?.level ?: "",
            syllabus: booksTagDtl?.syllabus ?: "", grade: booksTagDtl?.grade ?: "",
            subject: booksTagDtl?.subject ?: "", instituteId: instituteMst?.id,
            showtabs: instituteMst?.paidFreeTab ?: "", forceUserValidity: instituteMst?.forceUserValidity ?: "false",
            externalLink: comp.external_link, testTypeBook: comp.test_type_book
        ]
    }
    // Cache the results for the current page
    Gson gson = new Gson()
    String element = gson.toJson(books, new TypeToken<List>() {}.getType())
    redisService.set("instituteLibraryBooklist_${batchId}_page_${pageNumber}", element)

    // Calculate and cache the total number of books only for the first page
    if (pageNumber == 1) {
        //reset the time here
        String countSql = """
            select count(*) as total from (
                select bm.id
                from wsshop.books_mst bm
                join wsuser.books_batch_dtl bbd on bm.id = bbd.book_id
                where (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())
                  and bbd.batch_id in (${batchIds})
                  and (bm.show_in_library='Yes' or bm.show_in_library is null)
                union
                select bm1.id
                from wsshop.books_mst bm
                join wsuser.books_batch_dtl bbd on bm.id = bbd.book_id
                join wsshop.books_mst bm1 on bm.package_book_ids is not null and FIND_IN_SET(bm1.id, bm.package_book_ids) != 0
                where (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())
                  and bbd.batch_id in (${batchIds})
                and bm1.site_id = bm.site_id
            ) as combined_books
        """
        println("the count sql is $countSql")
        def totalResults = sql2.firstRow(countSql)
        int totalBooks = totalResults.total
        redisService.set("instituteLibrary_${batchId}_totalBooks", totalBooks.toString())
    }
}


    String getDefaultBatchId(String batchId){
        String defaultBatchId = batchId
        CourseBatchesDtl courseBatchesDtl = dataProviderService.getCourseBatchesDtl(new Integer(batchId))
        if(courseBatchesDtl!=null&&!"Default".equals(courseBatchesDtl.name)){

            CourseBatchesDtl defaultCBD = dataProviderService.getDefaultCourseBatchesDtl(courseBatchesDtl.conductedBy)
            defaultBatchId = ""+defaultCBD.id
        }
        return defaultBatchId
    }


    def userShelfBooks(String userName){
        String tempUsername = userName.toLowerCase().trim()
        if(tempUsername.toLowerCase().indexOf("select")!=-1||tempUsername.toLowerCase().indexOf("sleep(")!=-1||tempUsername.toLowerCase().indexOf(" or ")!=-1||tempUsername.indexOf("||")!=-1) userName=null

        int noOfBooksPerPage = 30
        String booksList=","
        //username will be in format of siteId_username, split it and get the siteId
        String siteId = userName.split("_")[0]
        def sql = "select bm.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled,-1 batchId,bm.title," +
                " bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,bp.expiry_date,bm.package_book_ids," +
                "'' batch_name,bm.show_in_library,bp.package_book_id,bm.publisher_id,bm.book_type,bm.language,bm.test_type_book" +
                "  from books_mst bm, wsuser.books_permission bp" +
                " where bp.book_id=bm.id " +
                " and  (bm.show_in_library='Yes' or bm.show_in_library is null)  and bp.username='"+userName+"'"+
                " union "+
                // to get package books
                "select bm1.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId," +
                " bm1.title,bm1.book_type,bm1.test_start_date,bm1.test_end_date,bm1.cover_image,bm1.price,bm1.site_id," +
                "bp.expiry_date,bm1.package_book_ids,'' batch_name,bm1.show_in_library,bm.id package_book_id,bm1.publisher_id,bm1.book_type,bm.language,bm.test_type_book" +
                " from  books_mst bm, books_permission bp, books_mst bm1 " +
                " where  bm1.show_in_library='Yes'  and bp.username='"+userName+"' and bm.id=bp.book_id  " +
                " and bm.package_book_ids is not null and FIND_IN_SET(bm1.id,bm.package_book_ids)!=0 " ;

        if(Integer.parseInt(siteId)==71){
            sql += " union "+
                    "select bm.id,bm.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId," +
                    " bm.title,bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id," +
                    "null expiry_date,bm.package_book_ids,'' batch_name,bm.show_in_library,null package_book_id,bm.publisher_id,bm.book_type,bm.language,bm.test_type_book" +
                    " from  books_mst bm,wsshop.books_tag_dtl btd, wsshop.book_price_dtl bpd " +
                    " where  bm.status='published' and btd.book_id=bm.id and bpd.book_id=bm.id   " +
                    " and  btd.syllabus='Current Affairs'  and bpd.sell_price=0 and bm.site_id=71";
        }
        sql += " order by dateCreated desc";

        println("sql="+sql)

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String publisherName
        Publishers publishers = null
        List books =  results.collect {  comp ->
            BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
            publisherName = ''
            if(comp.publisher_id!=null && comp.publisher_id!= ""){
                publishers = dataProviderService.getPublisher(new Long(comp.publisher_id))
                if (publishers!=null) publisherName = publishers.name
            }
            return [id   : comp.id, title: comp.title, coverImage: comp.cover_image != null ? comp.cover_image : "", level: (booksTagDtl != null) ? booksTagDtl.level : "", syllabus: (booksTagDtl != null) ? booksTagDtl.syllabus : "",
                    publisher: publisherName,
                    bookType: comp.book_type!=null?comp.book_type:"",bookLanguage: comp.language!=null?comp.language:"",
                    grade: (booksTagDtl != null) ? booksTagDtl.grade : "", subject: (booksTagDtl != null) ? booksTagDtl.subject : "",'packageBookId':comp.package_book_id,
                    'packageBookIds':comp.package_book_ids,price:comp.price,showInLibrary:comp.show_in_library,
                    expiryDate:comp.expiry_date!=null?(""+comp.expiry_date).replace(':','~'):"",testTypeBook:comp.test_type_book]

        }

        List books1 = new ArrayList();
        if (books != null && books.size() >= 0) {
            int totalNumberOfPages  =  Math.floor(books.size()/noOfBooksPerPage);
            if(books.size()%noOfBooksPerPage>0) totalNumberOfPages++;
            List tempBooks
            int booksIndex=0;
            for(int i=0;i<=totalNumberOfPages;i++){
                tempBooks = new ArrayList()
                for(int j=0;j<noOfBooksPerPage;j++){
                    if(booksIndex<books.size()){
                        tempBooks.add(books.get(booksIndex))
                        booksList +=books[booksIndex].id+","
                        booksIndex++;
                    }
                }
                Gson gson = new Gson();
                String element = gson.toJson(tempBooks,new TypeToken<List>() {}.getType())
                if(i==0) redisService.("userShelfBooks_"+ userName) = element
                redisService.("userShelfBooks_"+ userName+"_page_"+(i+1)) = element
            }

            int booksLength = books.size()
            for (int i = 0; i < booksLength; i++) {
                if(books.size <= i) break;
                books1.add(books.get(i));
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(books1,new TypeToken<List>() {}.getType())
        redisService.("userShelfBooksAll_"+userName) = element
        redisService.("userShelfBooks_"+ userName+"_totalBooks") = ""+books.size()
        getUserShelfBookIds(userName,results)
    }

    def getUserShelfBookIds(username,results){
        List books = results.collect { book ->
            return [id: book[0]]
        }
        books.unique()
        String userBooksListString

        String[] userBooksArray = new String[books.size()]
        for(int i=0;i<books.size();i++){
            userBooksArray[i] = books.get(i).id
        }

        if(books.size()>0) userBooksListString = String.join(",", userBooksArray);
        else userBooksListString = ""
        redisService.("userShelfBooks_"+username+"_"+"bookIds") = userBooksListString

    }


    def instituteLibraryFilters(int pageNo, params) {
        String batchIds = getAllBatchIds(params.batchId)
        pageNo = pageNo - 1

        int noOfBooksPerPage = 30
        String optionalCondition = ""
        String searchSuccessful = "true"

        if ("yes".equals(params.freeBooks)) {
            optionalCondition += " and ifnull(bk.price,0)=0"
        }
        if ("yes".equals(params.paidBooks)) {
            optionalCondition += " and ifnull(bk.price,0)!=0"
        }
        if ("yes".equals(params.testSeries)) {
            optionalCondition += " and bk.has_quiz='true'"
        }
        if (params.syllabus && params.syllabus != 'null') {
            optionalCondition += " and btd.syllabus in (${toSingleQuotes(params.syllabus)})"
        }
        if (params.grade && params.grade != 'null') {
            optionalCondition += " and btd.grade in (${toSingleQuotes(params.grade)})"
        }
        if (params.subject && params.subject != 'null' && params.subject != '') {
            optionalCondition += " and btd.subject in (${toSingleQuotes(params.subject)})"
        }
        if (params.searchBookId && params.searchBookId != 'null' && params.searchBookId != '-1') {
            optionalCondition += " and bk.id = ${params.searchBookId}"
        } else if ("-1".equals(params.searchBookId)) {
            searchSuccessful = "false"
        }

        String limitCondition = " limit ${pageNo * noOfBooksPerPage}, ${noOfBooksPerPage}"

        // Using MAX(...) on columns not in GROUP BY
        // If your MySQL version is < 5.7.8, replace MAX with MAX/MIN as needed
        String sql = """
    SELECT
       bk.id,
       bk.title,
       MAX(bk.isbn)            AS isbn,
       MAX(bk.status)          AS status,
       MAX(bk.publisher_id)    AS publisher_id,
       MAX(bk.cover_image)     AS cover_image,
       MAX(bk.price)           AS price,
       MAX(bk.book_type)       AS book_type,
       MAX(bk.language)        AS language,
       MAX(bk.external_link)   AS external_link,
       MAX(bk.number_of_licenses) AS number_of_licenses,
       MAX(bk.validity)       AS validity,
       MAX(bk.batch_id)       AS batch_id,
       MAX(btd.level)          AS level,
       MAX(btd.syllabus)       AS syllabus,
       MAX(btd.grade)          AS grade,
       GROUP_CONCAT(DISTINCT btd.subject SEPARATOR ', ') AS subject,
       MAX(bk.test_type_book)  AS test_type_book
    FROM (
        SELECT bk.id, bk.title, bk.isbn, bk.status, bk.publisher_id, bk.cover_image,
               bk.price, bk.book_type, bk.language, bk.external_link, bk.test_type_book,
               bbd.number_of_licenses, bbd.validity, bbd.batch_id
        FROM wsshop.books_mst bk
        JOIN wsuser.books_batch_dtl bbd ON bk.id = bbd.book_id
        WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
          AND bbd.batch_id IN (${batchIds})
          AND (bk.show_in_library = 'Yes' OR bk.show_in_library IS NULL)
        UNION
        SELECT bk1.id, bk1.title, bk1.isbn, bk1.status, bk1.publisher_id, bk1.cover_image,
               bk1.price, bk1.book_type, bk1.language, bk1.external_link, bk1.test_type_book,
               bbd.number_of_licenses, bbd.validity, bbd.batch_id
        FROM wsshop.books_mst bk
        JOIN wsuser.books_batch_dtl bbd ON bk.id = bbd.book_id
        JOIN wsshop.books_mst bk1 ON bk.package_book_ids IS NOT NULL AND FIND_IN_SET(bk1.id, bk.package_book_ids) != 0
        WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
          AND bbd.batch_id IN (${batchIds})
           AND bk1.site_id = bk.site_id
    ) bk
    LEFT JOIN wsshop.books_tag_dtl btd ON bk.id = btd.book_id
    WHERE 1=1 ${optionalCondition}
    GROUP BY bk.id, bk.title
    ${limitCondition}
"""


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new groovy.sql.Sql(dataSource)
        def results = sql2.rows(sql)

        List filteredBooks = results.collect { comp ->
            def courseBatch = CourseBatchesDtl.findById(params.batchId as Long)
            InstituteMst instituteMst = InstituteMst.findById(courseBatch?.conductedBy)
            return [
                    id               : comp.id,
                    title            : comp.title,
                    coverImage       : comp.cover_image ?: "",
                    bookStatus       : comp.status ?: 'unpublished',
                    batchId          : (params.batchId ?: "") + "",
                    publisher        : comp.publisher_id
                            ? dataProviderService.getPublisher(comp.publisher_id as Long).name
                            : '',
                    bookType         : comp.book_type ?: "",
                    bookLangauge     : comp.language ?: "",
                    level            : comp.level ?: "",
                    syllabus         : comp.syllabus ?: "",
                    noOfLic          : comp.number_of_licenses,
                    validity         : comp.validity,
                    grade            : comp.grade ?: "",
                    subject          : comp.subject ?: "",
                    instituteId      : instituteMst?.id,
                    showtabs         : instituteMst?.paidFreeTab ?: "",
                    forceUserValidity: instituteMst?.forceUserValidity ?: "false",
                    externalLink     : comp.external_link,
                    testTypeBook     : comp.test_type_book
            ]
        }

        sql = """
            SELECT count(*) as total FROM (
                SELECT DISTINCT bk.id
                FROM (
                    SELECT bk.id
                    FROM wsshop.books_mst bk
                    JOIN wsuser.books_batch_dtl bbd ON bk.id = bbd.book_id
                    WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
                      AND bbd.batch_id IN (${batchIds})
                      AND (bk.show_in_library = 'Yes' OR bk.show_in_library IS NULL)
                    UNION
                    SELECT bk1.id
                    FROM wsshop.books_mst bk
                    JOIN wsuser.books_batch_dtl bbd ON bk.id = bbd.book_id
                    JOIN wsshop.books_mst bk1 ON bk.package_book_ids IS NOT NULL AND FIND_IN_SET(bk1.id, bk.package_book_ids) != 0
                    WHERE (bbd.book_expiry_date IS NULL OR bbd.book_expiry_date > sysdate())
                      AND bbd.batch_id IN (${batchIds})
                      AND bk1.site_id = bk.site_id
                ) bk
                LEFT JOIN wsshop.books_tag_dtl btd ON bk.id = btd.book_id
                WHERE 1=1 ${optionalCondition}
            ) as combined_books
            """


         dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
         sql2 = new groovy.sql.Sql(dataSource)
         results = sql2.rows(sql)
      
        // Convert to JSON
        def gson = new Gson()
        def element = gson.toJson(filteredBooks, new TypeToken<List>() {}.getType())
        def json = [
                books           : element,
                count           : results[0].total,
                searchSuccessful: searchSuccessful
        ]
        return json
    }



    def userBooksFilters(int pageNo, params, String userName) {
        String tempUsername = userName.toLowerCase().trim()
        if(tempUsername.toLowerCase().indexOf("select")!=-1||tempUsername.toLowerCase().indexOf("sleep(")!=-1||tempUsername.toLowerCase().indexOf(" or ")!=-1||tempUsername.indexOf("||")!=-1) userName=null

        pageNo = pageNo - 1
        int noOfBooksPerPage = 30
        String optionalCondition = ""
        String searchSuccessful = "true"
        String siteId = userName.split("_")[0]

        // Build filter conditions
        if (params.syllabus && params.syllabus != 'null') {
            optionalCondition += " and btd.syllabus in (${toSingleQuotes(params.syllabus)})"
        }
        if (params.grade && params.grade != 'null') {
            optionalCondition += " and btd.grade in (${toSingleQuotes(params.grade)})"
        }
        if (params.subject && params.subject != 'null' && params.subject != '') {
            optionalCondition += " and btd.subject in (${toSingleQuotes(params.subject)})"
        }

        String limitCondition = ""
        if (pageNo >= 0) {
            int offset = pageNo * noOfBooksPerPage
            limitCondition = " LIMIT ${noOfBooksPerPage} OFFSET ${offset}"
        }

        String sql = """
        SELECT bk.id, bk.title, bk.isbn, bk.status, bk.publisher_id, bk.cover_image,
               bk.price, bk.book_type, bk.language, bk.external_link, bk.test_type_book,
               bk.dateCreated, bk.permissionType, bk.instructorControlled, bk.batchId,
               bk.test_start_date, bk.test_end_date, bk.site_id, bk.expiry_date,
               bk.package_book_ids, bk.batch_name, bk.show_in_library, bk.package_book_id,
               MAX(btd.level) AS level,
               MAX(btd.syllabus) AS syllabus,
               MAX(btd.grade) AS grade,
               GROUP_CONCAT(DISTINCT btd.subject SEPARATOR ', ') AS subject
        FROM (
            SELECT bm.id, bm.title, bm.isbn, bm.status, bm.publisher_id, bm.cover_image,
                   bm.price, bm.book_type, bm.language, bm.external_link, bm.test_type_book,
                   bp.date_created as dateCreated, 'book' as permissionType, '' as instructorControlled,
                   -1 as batchId, bm.test_start_date, bm.test_end_date, bm.site_id,
                   bp.expiry_date, bm.package_book_ids, '' as batch_name, bm.show_in_library,
                   bp.package_book_id
            FROM wsshop.books_mst bm    
            JOIN wsuser.books_permission bp ON bp.book_id = bm.id
            WHERE (bm.show_in_library = 'Yes' OR bm.show_in_library IS NULL)
              AND bp.username = '${userName}'
            UNION
            SELECT bm1.id, bm1.title, bm1.isbn, bm1.status, bm1.publisher_id, bm1.cover_image,
                   bm1.price, bm1.book_type, bm1.language, bm1.external_link, bm1.test_type_book,
                   bp.date_created as dateCreated, 'book' as permissionType, '' as instructorControlled,
                   -1 as batchId, bm1.test_start_date, bm1.test_end_date, bm1.site_id,
                   bp.expiry_date, bm1.package_book_ids, '' as batch_name, bm1.show_in_library,
                   bm.id as package_book_id
            FROM wsshop.books_mst bm
            JOIN wsuser.books_permission bp ON bm.id = bp.book_id
            JOIN wsshop.books_mst bm1 ON bm.package_book_ids IS NOT NULL AND FIND_IN_SET(bm1.id, bm.package_book_ids) != 0
            WHERE bm1.show_in_library = 'Yes'
              AND bp.username = '${userName}'
        """

        // Add site-specific books for site 71
        if(Integer.parseInt(siteId) == 71) {
            sql += """
            UNION
            SELECT bm.id, bm.title, bm.isbn, bm.status, bm.publisher_id, bm.cover_image,
                   bm.price, bm.book_type, bm.language, bm.external_link, bm.test_type_book,
                   bm.date_created as dateCreated, 'book' as permissionType, '' as instructorControlled,
                   -1 as batchId, bm.test_start_date, bm.test_end_date, bm.site_id,
                   null as expiry_date, bm.package_book_ids, '' as batch_name, bm.show_in_library,
                   null as package_book_id
            FROM wsshop.books_mst bm
            JOIN wsshop.books_tag_dtl btd2 ON btd2.book_id = bm.id
            JOIN wsshop.book_price_dtl bpd ON bpd.book_id = bm.id
            WHERE bm.status = 'published'
              AND btd2.syllabus = 'Current Affairs'
              AND bpd.sell_price = 0
              AND bm.site_id = 71
            """
        }

        sql += """
        ) bk
        LEFT JOIN wsshop.books_tag_dtl btd ON bk.id = btd.book_id
        WHERE 1=1 ${optionalCondition}
        GROUP BY bk.id, bk.title, bk.isbn, bk.status, bk.publisher_id, bk.cover_image,
                 bk.price, bk.book_type, bk.language, bk.external_link, bk.test_type_book,
                 bk.dateCreated, bk.permissionType, bk.instructorControlled, bk.batchId,
                 bk.test_start_date, bk.test_end_date, bk.site_id, bk.expiry_date,
                 bk.package_book_ids, bk.batch_name, bk.show_in_library, bk.package_book_id
        ORDER BY bk.dateCreated DESC
        ${limitCondition}
        """

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new groovy.sql.Sql(dataSource)
        def results = sql2.rows(sql)

        List filteredBooks = []
        results.each { book ->
            filteredBooks.add([
                id: book.id,
                title: book.title,
                isbn: book.isbn,
                status: book.status,
                publisherId: book.publisher_id,
                coverImage: book.cover_image,
                price: book.price,
                bookType: book.book_type,
                language: book.language,
                externalLink: book.external_link,
                testTypeBook: book.test_type_book,
                dateCreated: book.dateCreated,
                permissionType: book.permissionType,
                instructorControlled: book.instructorControlled,
                batchId: book.batchId,
                testStartDate: book.test_start_date,
                testEndDate: book.test_end_date,
                siteId: book.site_id,
                expiryDate: book.expiry_date,
                packageBookIds: book.package_book_ids,
                batchName: book.batch_name,
                showInLibrary: book.show_in_library,
                packageBookId: book.package_book_id,
                level: book.level,
                syllabus: book.syllabus,
                grade: book.grade,
                subject: book.subject
            ])
        }

        // Get total count
        String countSql = """
        SELECT count(*) as total FROM (
            SELECT DISTINCT bk.id
            FROM (
                SELECT bm.id
                FROM wsshop.books_mst bm
                JOIN wsuser.books_permission bp ON bp.book_id = bm.id
                WHERE (bm.show_in_library = 'Yes' OR bm.show_in_library IS NULL)
                  AND bp.username = '${userName}'
                UNION
                SELECT bm1.id
                FROM wsshop.books_mst bm
                JOIN wsuser.books_permission bp ON bm.id = bp.book_id
                JOIN wsshop.books_mst bm1 ON bm.package_book_ids IS NOT NULL AND FIND_IN_SET(bm1.id, bm.package_book_ids) != 0
                WHERE bm1.show_in_library = 'Yes'
                  AND bp.username = '${userName}'
        """

        if(Integer.parseInt(siteId) == 71) {
            countSql += """
                UNION
                SELECT bm.id
                FROM wsshop.books_mst bm
                JOIN wsshop.books_tag_dtl btd2 ON btd2.book_id = bm.id
                JOIN wsshop.book_price_dtl bpd ON bpd.book_id = bm.id
                WHERE bm.status = 'published'
                  AND btd2.syllabus = 'Current Affairs'
                  AND bpd.sell_price = 0
                  AND bm.site_id = 71
            """
        }

        countSql += """
            ) bk
            LEFT JOIN wsshop.books_tag_dtl btd ON bk.id = btd.book_id
            WHERE 1=1 ${optionalCondition}
        ) as combined_books
        """

        def countResults = sql2.rows(countSql)

        // Convert to JSON
        def gson = new Gson()
        def element = gson.toJson(filteredBooks, new TypeToken<List>() {}.getType())
        def json = [
                books           : element,
                count           : countResults[0].total,
                searchSuccessful: searchSuccessful
        ]
        return json
    }

    String toSingleQuotes(String source){
        String[] parts = source.split(",")
        return Stream.of(parts).collect(Collectors.joining("','", "'", "'"));
    }



    @Transactional
    def getInstituteBooksTags(batchId) {
        String batchIds = getAllBatchIds(""+batchId)

        Map<String, List> usersMyLibraryBooks = new HashMap<String, List>()
        Map<String, List<String>> subjectsBySyllabusGrade = new HashMap<String, List<String>>()
        List<String> batchIdsList = batchIds.split(',').collect { it as Long }
        List batchBooks = BooksBatchDtl.findAllByBatchIdInList(batchIdsList)

        String books = ""
        batchBooks.each {
            books += it.bookId + ","
        }
        if (!books.equals("")) {
            String sql = "SELECT distinct syllabus as syllabus,grade,subject FROM wsshop.books_tag_dtl btd where  book_id in (" + org.apache.commons.lang.StringUtils.removeEnd(books, ",") + ") order by syllabus,grade,subject"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            results.each { book ->
                // Handle syllabus-grade mapping (existing logic)
                if (usersMyLibraryBooks.get("" + book.syllabus) == null) {
                    List bList = new ArrayList()
                    bList.add(book.grade)
                    usersMyLibraryBooks.put("" + book.syllabus, bList)
                } else {
                    List existingGrades = usersMyLibraryBooks.get("" + book.syllabus)
                    if (!existingGrades.contains(book.grade)) {
                        existingGrades.add(book.grade)
                    }
                }

                // Handle subjects by syllabus-grade combination
                if (book.subject != null && book.subject != "") {
                    String key = book.syllabus + "|" + book.grade
                    if (subjectsBySyllabusGrade.get(key) == null) {
                        List<String> subjectList = new ArrayList<String>()
                        subjectList.add(book.subject)
                        subjectsBySyllabusGrade.put(key, subjectList)
                    } else {
                        List<String> existingSubjects = subjectsBySyllabusGrade.get(key)
                        if (!existingSubjects.contains(book.subject)) {
                            existingSubjects.add(book.subject)
                        }
                    }
                }
            }

            // Sort subjects for each syllabus-grade combination
            subjectsBySyllabusGrade.each { key, subjectList ->
                subjectList.sort()
            }

            // Create combined data structure with syllabus/grades and subjects mapping
            Map<String, Object> combinedData = new HashMap<String, Object>()
            combinedData.put("syllabusGrades", usersMyLibraryBooks)
            combinedData.put("subjectsBySyllabusGrade", subjectsBySyllabusGrade)

            Gson gson = new Gson();
            String element = gson.toJson(combinedData, new TypeToken<HashMap<String, Object>>() {}.getType())
            if (results != null) redisService.("batchBooksFilters_" + batchId) = element
        }
    }

    @Transactional
    def getUserBooksTags(String userName) {
        String tempUsername = userName.toLowerCase().trim()
        if(tempUsername.toLowerCase().indexOf("select")!=-1||tempUsername.toLowerCase().indexOf("sleep(")!=-1||tempUsername.toLowerCase().indexOf(" or ")!=-1||tempUsername.indexOf("||")!=-1) userName=null

        Map<String, List> usersMyLibraryBooks = new HashMap<String, List>()
        Map<String, List<String>> subjectsBySyllabusGrade = new HashMap<String, List<String>>()
        String siteId = userName.split("_")[0]

        // Get all book IDs for the user
        String sql = """
        SELECT DISTINCT bk.id
        FROM (
            SELECT bm.id
            FROM wsshop.books_mst bm
            JOIN wsuser.books_permission bp ON bp.book_id = bm.id
            WHERE (bm.show_in_library = 'Yes' OR bm.show_in_library IS NULL)
              AND bp.username = '${userName}'
            UNION
            SELECT bm1.id
            FROM wsshop.books_mst bm
            JOIN wsuser.books_permission bp ON bm.id = bp.book_id
            JOIN wsshop.books_mst bm1 ON bm.package_book_ids IS NOT NULL AND FIND_IN_SET(bm1.id, bm.package_book_ids) != 0
            WHERE bm1.show_in_library = 'Yes'
              AND bp.username = '${userName}'
        """

        if(Integer.parseInt(siteId) == 71) {
            sql += """
            UNION
            SELECT bm.id
            FROM wsshop.books_mst bm
            JOIN wsshop.books_tag_dtl btd2 ON btd2.book_id = bm.id
            JOIN wsshop.book_price_dtl bpd ON bpd.book_id = bm.id
            WHERE bm.status = 'published'
              AND btd2.syllabus = 'Current Affairs'
              AND bpd.sell_price = 0
              AND bm.site_id = 71
            """
        }

        sql += ") bk"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new groovy.sql.Sql(dataSource)
        def bookResults = sql1.rows(sql)

        if (bookResults && bookResults.size() > 0) {
            String books = bookResults.collect { it.id }.join(",")

            String tagsSql = "SELECT distinct syllabus as syllabus,grade,subject FROM wsshop.books_tag_dtl btd where book_id in (${books}) order by syllabus,grade,subject"
            def dataSource2 = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql2 = new groovy.sql.Sql(dataSource2)
            def results = sql2.rows(tagsSql)

            results.each { book ->
                // Handle syllabus-grade mapping
                if (usersMyLibraryBooks.get("" + book.syllabus) == null) {
                    List bList = new ArrayList()
                    bList.add(book.grade)
                    usersMyLibraryBooks.put("" + book.syllabus, bList)
                } else {
                    List existingGrades = usersMyLibraryBooks.get("" + book.syllabus)
                    if (!existingGrades.contains(book.grade)) {
                        existingGrades.add(book.grade)
                    }
                }

                // Handle subjects by syllabus-grade combination
                if (book.subject != null && book.subject != "") {
                    String key = book.syllabus + "|" + book.grade
                    if (subjectsBySyllabusGrade.get(key) == null) {
                        List<String> subjectList = new ArrayList<String>()
                        subjectList.add(book.subject)
                        subjectsBySyllabusGrade.put(key, subjectList)
                    } else {
                        List<String> existingSubjects = subjectsBySyllabusGrade.get(key)
                        if (!existingSubjects.contains(book.subject)) {
                            existingSubjects.add(book.subject)
                        }
                    }
                }
            }

            // Sort subjects for each syllabus-grade combination
            subjectsBySyllabusGrade.each { key, subjectList ->
                subjectList.sort()
            }

            // Create combined data structure with syllabus/grades and subjects mapping
            Map<String, Object> combinedData = new HashMap<String, Object>()
            combinedData.put("syllabusGrades", usersMyLibraryBooks)
            combinedData.put("subjectsBySyllabusGrade", subjectsBySyllabusGrade)

            Gson gson = new Gson();
            String element = gson.toJson(combinedData, new TypeToken<HashMap<String, Object>>() {}.getType())
            if (results != null) redisService.("userBooksFilters_" + userName) = element
        }
    }

}
