package com.wonderslate.discussion

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.data.UtilService
import com.wonderslate.discussions.DiscussionLevelDtl
import com.wonderslate.discussions.DiscussionQuestions
import com.wonderslate.discussions.DiscussionQuestionsProperties
import com.wonderslate.usermanagement.User
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.util.stream.*
@Transactional
class DiscussionBoardCacheService {
    Integer discussionPropertiesMstIdForAbuse = 2
    Integer discussionPropertiesMstIdForIncorrect = 1
    def grailsApplication
    def redisService
    UtilService utilService
    def limit = 100
    def skip = 100
    def serviceMethod() {

    }

    def updateQuestionsFallowingCache(def siteId,int batchIndex, String userName){
        String sql = " SELECT dq.id _id," +
                " dq.question," +
                " dq.tags," +
                " dq.created_by createdBy," +
                " dq.img_name," +
                " dq.date_created dateCreated," +
                " dq.upvote_count" +
                " FROM discussion_questions dq, discussion_questions_user_following dquf" +
                " WHERE 1 =1 " +
                " AND dq.id = dquf.qId" +
                " AND dq.site_id = " + siteId +
                " AND dq.created_by = '" + userName + "'"+
                " ORDER BY dq.id DESC"
//                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)
        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())
        if(batchIndex == 0) {
            redisService.("questionsFallowing_"+siteId) = element
//            long count = DiscussionQuestions.countByFreeAndSiteIdAndShowQuestion(true,siteId,true)
            redisService.("questionsFallowingCount_"+siteId) = questions.size()
        }
        else if(batchIndex > 0) return questions
    }

    def updateRecentAnsweredDiscussionQuestionCache(params,def siteId,int batchIndex){
        DiscussionLevelDtl discussionLevelDtl
        String dldIds=""
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
             if(params.batchId!=null && !"".equals(params.batchId)){
                 discussionLevelDtl = DiscussionLevelDtl.findByBatchId(new Long(params.batchId))
                dldIds=discussionLevelDtl.id+""
            }else if(params.instituteId!=null && !"".equals(params.instituteId)){
                String sql ="SELECT group_concat(id) FROM wsuser.discussion_level_dtl where institute_id="+params.instituteId
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                dldIds= results[0][0]
            }
        }
        String sql = " SELECT dq.id _id," +
                " dq.question," +
                " dq.tags," +
                " dq.created_by createdBy," +
                " dq.img_name," +
                " dq.date_created dateCreated," +
                " dq.upvote_count," +
                " dq.dld_id dldId" ;
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            sql +=    " FROM discussion_questions dq,discussion_level_dtl dld" +
                " WHERE dq.dld_id=dld.id " ;
                sql +=    " AND dq.dld_id IN  (" + dldIds + ") AND ";
        }else{
            sql +=  " FROM discussion_questions dq WHERE dq.dld_id is null AND " ;
        }
            sql +=   " dq.free = true AND dq.show_question = true AND dq.site_id = " + siteId +
                " ORDER BY dq.id DESC" +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)

        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }
//        Bson freeBooksFilter = Filters.eq("free",true)
//        Bson siteIdFilter = Filters.eq("siteId",siteId)
//        Bson moderationFilter = Filters.eq("show",true)
//        Bson answerListFilter = Filters.or(Filters.elemMatch("qaList",Filters.eq("show",true)),Filters.elemMatch("qaList",Filters.eq("show",false)))
//        long countWithAnswers = 0//DiscussionQuestions.collection.count(Filters.and(freeBooksFilter,siteIdFilter,moderationFilter,answerListFilter))
        int skipCountForNoAnswers = 0, limitForNoAnswers = 0
        ArrayList dummy = new ArrayList()
      /*  if((batchIndex * skip) > countWithAnswers && questions.size() == 0) {
            skipCountForNoAnswers = (batchIndex * skip) - countWithAnswers
            limitForNoAnswers = limit
        }else limitForNoAnswers = limit - questions.size()

        if(questions.size() < limit){
            answerListFilter = Filters.or(Filters.exists("qaList",false),Filters.size("qaList",0))
            outputResults = DiscussionQuestions.collection.aggregate(Arrays.asList(
                    Aggregates.match(siteIdFilter),
                    Aggregates.match(freeBooksFilter),
                    Aggregates.match(moderationFilter),
                    Aggregates.match(answerListFilter),
                    Aggregates.sort(Sorts.descending("_id")),
                    Aggregates.skip(skipCountForNoAnswers),
                    Aggregates.limit(limitForNoAnswers),
                    Aggregates.project(
                            Projections.fields(
                                    Projections.include("_id"),
                                    Projections.include("question"),
                                    Projections.include("title"),
                                    Projections.include("tags"),
                                    Projections.include("userName"),
                                    Projections.include("userId"),
                                    Projections.include("uImgId"),
                                    Projections.include("uImgName"),
                                    Projections.include("qaList"),
                                    Projections.include("qImgId"),
                                    Projections.include("qImgName"),
                                    Projections.include("upVote"),
                                    Projections.computed("answerCount", new Document(
                                            "\$"+"cond", new Document(
                                            "if", new Document("\$"+"isArray", "\$"+"qaList" )
                                    )
                                            .append("then", new Document("\$"+"size","\$"+"qaList"))
                                            .append("else", 0)
                                    )
                                    )
//
                            )
                    )
            )).allowDiskUse(true)
//            questions = new ArrayList()
            outputResults.each { def r ->
                questions.add(r)
            }
        }
*/
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())
       if(batchIndex == 0) {
               if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
                   if(params.batchId!=null && !"".equals(params.batchId)){
                       redisService.("recentAnswer_" + params.batchId) = element
                       long count = DiscussionQuestions.countByFreeAndSiteIdAndShowQuestionAndDldId(true, siteId, true,new Long(dldIds))
                       redisService.("recentAnswerCount_" + params.batchId) = count
                   }
                   else if(params.instituteId!=null && !"".equals(params.instituteId)) {
                       redisService.("recentAnswer_" + params.instituteId) = element
                       String sqlCount ="SELECT count(id) FROM wsuser.discussion_questions where free=true and show_question=true and dld_id in ("+params.dldIds+")"
                       def dataSourceCount = grailsApplication.mainContext.getBean('dataSource_wsuser')
                       def sql1Count = new Sql(dataSourceCount)
                       def resultsCount = sql1Count.rows(sqlCount);
                       long count= resultsCount[0][0]
                       redisService.("recentAnswerCount_" + params.instituteId) = count
                   }
           }else{
               redisService.("recentAnswer_" + siteId) = element
               long count = DiscussionQuestions.countByFreeAndSiteIdAndShowQuestion(true, siteId, true)
               redisService.("recentAnswerCount_" + siteId) = count
           }
       }
        else if(batchIndex > 0) return questions
    }

    def getRecentAnsweredDiscussionQuestionCache(params,def siteId, int batchIndex){
        List questions = null
       if(params.batchId!=null && !"".equals(params.batchId)){
            if((redisService.("recentAnswer_"+params.batchId) == null || redisService.("recentAnswer_"+params.batchId) == "null") && batchIndex == 0){//redisService.("recentAnswer_"+siteId) == null && batchIndex == 0
                updateRecentAnsweredDiscussionQuestionCache(params,siteId,batchIndex)
            }
            // send unparsed json text
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentAnswer_"+params.batchId))
            else if(batchIndex > 0) questions = updateRecentAnsweredDiscussionQuestionCache(params,siteId,batchIndex)
        }else if(params.instituteId!=null && !"".equals(params.instituteId)){
            if((redisService.("recentAnswer_"+params.instituteId) == null || redisService.("recentAnswer_"+params.instituteId) == "null") && batchIndex == 0){//redisService.("recentAnswer_"+siteId) == null && batchIndex == 0
                updateRecentAnsweredDiscussionQuestionCache(params,siteId,batchIndex)
            }
            // send unparsed json text
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentAnswer_"+params.instituteId))
            else if(batchIndex > 0) questions = updateRecentAnsweredDiscussionQuestionCache(params,siteId,batchIndex)
        }else{
            if(redisService.("recentAnswer_"+siteId) == null && batchIndex == 0){//redisService.("recentAnswer_"+siteId) == null && batchIndex == 0
                updateRecentAnsweredDiscussionQuestionCache(params,siteId,batchIndex)
            }
            // send unparsed json text
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentAnswer_"+siteId))
            else if(batchIndex > 0) questions = updateRecentAnsweredDiscussionQuestionCache(params,siteId,batchIndex)
        }
        return questions
    }

    def updateRecentAnsweredFilterDiscussionQuestionCache(params,def siteId,int batchIndex){
        DiscussionLevelDtl discussionLevelDtl
        String dldIds=""
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            if(params.batchId!=null && !"".equals(params.batchId)){
                discussionLevelDtl = DiscussionLevelDtl.findByBatchId(new Long(params.batchId))
                dldIds=discussionLevelDtl.id+""
            }else if(params.instituteId!=null && !"".equals(params.instituteId)){
                String sql ="SELECT group_concat(id) FROM wsuser.discussion_level_dtl where institute_id="+params.instituteId
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                dldIds= results[0][0]
            }
        }

        String sql = " SELECT DISTINCT dq.id _id," +
                " question," +
                " tags," +
                " dq.created_by createdBy," +
                " dq.img_name img_name," +
                " dq.date_created dateCreated," +
                " da.date_created," +
                " dq.upvote_count upvote_count" ;
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            sql +=    " FROM discussion_questions dq,discussion_level_dtl dld,discussion_answers da" +
                    " WHERE dq.dld_id=dld.id " ;
            sql +=    " AND dq.dld_id IN  (" + dldIds + ") AND ";
          }else{
            sql +=  " FROM discussion_questions dq, discussion_answers da WHERE dq.dld_id is null AND " ;
            }
        sql +=   " dq.id = da.discussion_question_id" +
                " AND dq.free = true" +
                " AND dq.site_id = " + siteId +
                " AND dq.show_question = true" +
                " ORDER BY da.date_created DESC" +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)

        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }
        List distinctQuestions = questions.stream()
                .distinct()
                .collect( Collectors.toList() )
        Gson gson = new Gson();
        String element = gson.toJson(distinctQuestions,new TypeToken<List>() {}.getType())
        if(batchIndex == 0) {
            if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
                 if(params.batchId!=null && !"".equals(params.batchId)){
                    redisService.("recentAnswerFilter_"+params.batchId) = element
                    long count = DiscussionQuestions.countByFreeAndSiteIdAndShowQuestionAndDldId(true, siteId, true,new Long(discussionLevelDtl.id))
                    redisService.("recentAnswerFilterCount_"+params.batchId) = count
                }else if (params.instituteId!=null && !"".equals(params.instituteId)) {
                    redisService.("recentAnswerFilter_"+params.instituteId) = element
                     String sqlCount ="SELECT count(id) FROM wsuser.discussion_questions where free=true and show_question=true and dld_id in ("+dldIds+")"
                     def dataSourceCount = grailsApplication.mainContext.getBean('dataSource_wsuser')
                     def sql1Count = new Sql(dataSourceCount)
                     def resultsCount = sql1Count.rows(sqlCount);
                     long count= resultsCount[0][0]
                     redisService.("recentAnswerFilterCount_" + params.instituteId) = count
                }
            }else{
                redisService.("recentAnswerFilter_"+siteId) = element
                long count = DiscussionQuestions.countByShowQuestionAndSiteIdAndFree(true,siteId,true)
                redisService.("recentAnswerFilterCount_"+siteId) = count
            }
        }
        else if(batchIndex > 0) return questions
    }

    def getRecentAnsweredFilterDiscussionQuestionCache(params,def siteId, int batchIndex){
        List questions = null
        if(params.batchId!=null && !"".equals(params.batchId)){
            if(redisService.("recentAnswerFilter_"+params.batchId) == null && batchIndex == 0){
                updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
            }
            // send unparsed json text
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentAnswerFilter_"+params.batchId))
            else if(batchIndex > 0) questions = updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        } else if(params.instituteId!=null && !"".equals(params.instituteId)){
            if(redisService.("recentAnswerFilter_"+params.instituteId) == null && batchIndex == 0){
                updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
            }
            // send unparsed json text
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentAnswerFilter_"+params.instituteId))
            else if(batchIndex > 0) questions = updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        }
        else{
            if(redisService.("recentAnswerFilter_"+siteId) == null && batchIndex == 0){
                updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
            }
            // send unparsed json text
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentAnswerFilter_"+siteId))
            else if(batchIndex > 0) questions = updateRecentAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        }
        return questions
    }

    def updateRecentUnAnsweredFilterDiscussionQuestionCache(params,def siteId,int batchIndex){
        DiscussionLevelDtl discussionLevelDtl
        String dldIds=""
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            if(params.batchId!=null && !"".equals(params.batchId)){
                discussionLevelDtl = DiscussionLevelDtl.findByBatchId(new Long(params.batchId))
                dldIds=discussionLevelDtl.id+""
            }else if(params.instituteId!=null && !"".equals(params.instituteId)){
                String sql ="SELECT group_concat(id) FROM wsuser.discussion_level_dtl where institute_id="+params.instituteId
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                dldIds= results[0][0]
            }
        }
        String sql = " SELECT dq.id _id," +
                " dq.question," +
                " dq.tags," +
                " dq.created_by createdBy," +
                " dq.img_name," +
                " dq.date_created dateCreated," +
                " dq.upvote_count";
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            sql +=    " FROM discussion_questions dq,discussion_level_dtl dld " +
                    " WHERE dq.dld_id=dld.id AND free = true" +
                     " AND dq.dld_id IN  (" + dldIds+ ") AND ";
            }else{
            sql +=  " FROM discussion_questions dq WHERE dq.dld_id is null AND " ;
             }
            sql +=   " dq.site_id = " + siteId +
                " AND dq.show_question = true" +
                " AND dq.id NOT IN (" +
                " SELECT discussion_question_id" +
                " FROM discussion_answers" +
                " )" +
                " ORDER BY dq.id DESC" +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)
        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())
        if(batchIndex == 0) {
            if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
                if(params.batchId!=null && !"".equals(params.batchId)){
                    redisService.("recentUnAnswerFilter_"+params.batchId) = element
                    long count = DiscussionQuestions.countByFreeAndSiteIdAndShowQuestionAndDldId(true, siteId, true,new Long(dldIds))
                    redisService.("recentUnAnswerFilterCount_"+params.batchId) = count
                }else if (params.instituteId!=null && !"".equals(params.instituteId)) {
                    redisService.("recentUnAnswerFilter_"+params.instituteId) = element
                    String sqlCount ="SELECT count(id) FROM wsuser.discussion_questions where free=true and show_question=true and dld_id in ("+dldIds+")"
                    def dataSourceCount = grailsApplication.mainContext.getBean('dataSource_wsuser')
                    def sql1Count = new Sql(dataSourceCount)
                    def resultsCount = sql1Count.rows(sqlCount);
                    long count= resultsCount[0][0]
                    redisService.("recentUnAnswerFilterCount_" + params.instituteId) = count
                }
            }
            else{
                redisService.("recentUnAnswerFilter_"+siteId) = element
                long count = DiscussionQuestions.countByShowQuestionAndSiteIdAndFree(true,siteId,true)
                redisService.("recentUnAnswerFilterCount_"+siteId) = count
            }
        }
        else if(batchIndex > 0) return questions
    }

    def getRecentUnAnsweredFilterDiscussionQuestionCache(params,def siteId, int batchIndex){
        List questions = null
        if(params.batchId!=null && !"".equals(params.batchId)){
            if(redisService.("recentUnAnswerFilter_"+params.batchId) == null && batchIndex == 0){
                updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
            }
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentUnAnswerFilter_"+params.batchId))
            else if(batchIndex > 0) questions = updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        } else if (params.instituteId!=null && !"".equals(params.instituteId)){
            if(redisService.("recentUnAnswerFilter_"+params.instituteId) == null && batchIndex == 0){
                updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
            }
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentUnAnswerFilter_"+params.instituteId))
            else if(batchIndex > 0) questions = updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        }
        else{
            if(redisService.("recentUnAnswerFilter_"+siteId) == null && batchIndex == 0){
                updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
            }
            if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("recentUnAnswerFilter_"+siteId))
            else if(batchIndex > 0) questions = updateRecentUnAnsweredFilterDiscussionQuestionCache(params,siteId,batchIndex)
        }
        return questions
    }

    def updateUnModeratedDiscussionQuestionCache(def siteId, int batchIndex){

        String sql = " SELECT id _id," +
                " question," +
                " tags," +
                " created_by createdBy," +
                " img_name," +
                " date_created dateCreated," +
                " upvote_count upVote" +
                " FROM discussion_questions" +
                " WHERE free = true" +
                " AND site_id = " + siteId +
                " AND show_question = false AND dld_id is null" +
                " AND id NOT IN (" +
                " SELECT discussion_question_id" +
                " FROM discussion_questions_properties" +
                " WHERE discussion_board_properties_id IN (" +
                discussionPropertiesMstIdForAbuse + "," + discussionPropertiesMstIdForIncorrect +
                " )" +
                " )" +
                " ORDER BY id DESC" +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)

        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            questions.add(r)
        }
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())
        if(batchIndex == 0) {
            long count = 0 ;
            sql = " SELECT count(id) _idCount" +
                    " FROM discussion_questions" +
                    " WHERE free = true" +
                    " AND site_id = " + siteId +
                    " AND show_question = false" +
                    " AND id NOT IN (" +
                    " SELECT discussion_question_id" +
                    " FROM discussion_questions_properties" +
                    " WHERE discussion_board_properties_id IN (" +
                    discussionPropertiesMstIdForAbuse + "," + discussionPropertiesMstIdForIncorrect +
                    " )" +
                    " )" +
                    "GROUP BY id"
            results = sql1.rows(sql);
            results.collect { r ->
                count = r['_idCount']
            }
            redisService.("unModeratedQuestions_"+siteId) = element
            redisService.("unModeratedQuestionsCount_"+siteId) = count
        }
        else if(batchIndex > 0) return questions
    }

    def getUnModeratedDiscussionQuestionCache(def siteId, int batchIndex){
//        if(redisService.("unModeratedQuestions_"+siteId) == null){
            updateUnModeratedDiscussionQuestionCache(siteId,batchIndex)
//        }
        List questions = null
        if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("unModeratedQuestions_"+siteId))
        else if(batchIndex > 0) questions = updateUnModeratedDiscussionQuestionCache(siteId,batchIndex)
        return questions
    }

    def updateUnAnsweredDiscussionQuestionCache(def siteId,int batchIndex){


        String sql = " SELECT id _id," +
                " question," +
                " tags," +
                " created_by createdBy," +
                " img_name," +
                " date_created dateCreated," +
                " upvote_count" +
                " FROM discussion_questions" +
                " WHERE free = true" +
                " AND site_id = " + siteId +
//                " AND show_question = true" +
                " AND id NOT IN (" +
                " SELECT discussion_question_id" +
                " FROM discussion_answers" +
                " )" +
                " ORDER BY id DESC" +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)

        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect { a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())
        if(batchIndex == 0){
            long count = 0 ;
            sql = " SELECT COUNT(id) countQid" +
                    " FROM discussion_questions" +
                    " WHERE free = true" +
                    " AND site_id = " + siteId +
                    " AND show_question = true" +
                    " AND id NOT IN (" +
                    " SELECT discussion_question_id" +
                    " FROM discussion_answers" +
                    " )"
                    results = sql1.rows(sql);
            results.collect{ c ->
                count = c['countQid']
            }
            redisService.("unAnsweredQuestions_"+siteId) = element
            redisService.("unAnsweredQuestionsCount_"+siteId) = count
        } else if(batchIndex > 0) return questions
    }

    def updateUnTagedDiscussionQuestionCache(def siteId,int batchIndex){
        String sql = " SELECT id _id," +
                " question," +
                " tags," +
                " created_by createdBy," +
                " img_name," +
                " date_created dateCreated," +
                " upvote_count upVote" +
                " FROM discussion_questions" +
                " WHERE free = true and dld_id is null" +
                " AND site_id = " + siteId +
                " AND (tags IS NULL OR tags = '')" +
//                " AND show_question = true" +
                " ORDER BY id DESC" +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)

        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }

        questions = new ArrayList()
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())
        if(batchIndex == 0){
            long count = DiscussionQuestions.countBySiteIdAndTags(siteId,null)
            redisService.("unTagedQuestions_"+siteId) = element
            redisService.("unTagedQuestionsCount_"+siteId) = count
        } else if(batchIndex > 0) return questions
    }

    def getUnAnsweredDiscussionQuestionCache(def siteId,int batchIndex){
//        if(redisService.("unAnsweredQuestions_"+siteId) == null){
            updateUnAnsweredDiscussionQuestionCache(siteId,batchIndex)
//        }
        List questions = null
        if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("unAnsweredQuestions_"+siteId))
        else if(batchIndex > 0) questions = updateUnAnsweredDiscussionQuestionCache(siteId,batchIndex)
        return questions
    }

    def getUnTagedDiscussionQuestionCache(def siteId,int batchIndex){
//        if(redisService.("unTagedQuestions_"+siteId) == null){

            updateUnTagedDiscussionQuestionCache(siteId,batchIndex)

//        }
        List questions = null
        if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("unTagedQuestions_"+siteId))
        else if(batchIndex > 0) questions = updateUnTagedDiscussionQuestionCache(siteId,batchIndex)
        return questions
    }

    def updateAbuseDiscussionQuestionCache(params,def siteId,int batchIndex){
        DiscussionLevelDtl discussionLevelDtl
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            if(params.instituteId!=null && !"".equals(params.instituteId)) {
                discussionLevelDtl = DiscussionLevelDtl.findByInstituteId(new Long(params.instituteId))
            }else{
                discussionLevelDtl = DiscussionLevelDtl.findByBatchId(new Long(params.batchId))
            }
        }
        String sql = " SELECT DISTINCT dq.id _id," +
                " question," +
                " tags," +
                " dq.created_by createdBy," +
                " dq.img_name img_name," +
                " dq.date_created dateCreated," +
                " dqp.date_created," +
                " dq.upvote_count upvote_count" +
                " FROM discussion_questions dq, discussion_questions_properties dqp" +
                " WHERE dq.id = dqp.discussion_question_id" ;
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            sql +=    "AND dq.dld_id = " + discussionLevelDtl.id + " ";
        }else{
            sql +=  " AND dq.dld_id is null " ;
        }
        sql +=   " AND dqp.discussion_board_properties_id = " + discussionPropertiesMstIdForAbuse +
                " AND dq.free = true" +
                " AND dq.site_id = " + siteId +
                " ORDER BY dqp.date_created DESC" +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)

        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())
        if(batchIndex == 0){
            long count = DiscussionQuestionsProperties.countByDiscussionBoardPropertiesId(discussionPropertiesMstIdForAbuse)
            redisService.("abuseQuestions_"+siteId) = element
            redisService.("abuseQuestionsCount_"+siteId) = count
        }else return questions
    }

    def getAbuseDiscussionQuestionCache(params,def siteId, int batchIndex){
//        if(redisService.("abuseQuestions_"+siteId) == null){
            updateAbuseDiscussionQuestionCache(params,siteId,batchIndex)
//        }
        List questions = null
        if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("abuseQuestions_"+siteId))
        else if(batchIndex > 0) questions = updateAbuseDiscussionQuestionCache(params,siteId,batchIndex)
        return questions
    }

    def updateIncorrectDiscussionQuestionCache(params,def siteId,int batchIndex){
        DiscussionLevelDtl discussionLevelDtl
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            if(params.instituteId!=null && !"".equals(params.instituteId)) {
                discussionLevelDtl = DiscussionLevelDtl.findByInstituteId(new Long(params.instituteId))
            }else{
                discussionLevelDtl = DiscussionLevelDtl.findByBatchId(new Long(params.batchId))
            }
        }
        String sql = " SELECT DISTINCT dq.id _id," +
                " question," +
                " tags," +
                " dq.created_by createdBy," +
                " dq.img_name img_name," +
                " dq.date_created dateCreated," +
                " dqp.date_created," +
                " dq.upvote_count upvote_count" +
                " FROM discussion_questions dq, discussion_questions_properties dqp" +
                " WHERE dq.id = dqp.discussion_question_id" ;
        if((params.batchId!=null && !"".equals(params.batchId))|| (params.instituteId!=null && !"".equals(params.instituteId))){
            sql +=    "AND dq.dld_id = " + discussionLevelDtl.id + " ";
        }else{
            sql +=  " AND dq.dld_id is null " ;
        }
        sql +=    " AND dqp.discussion_board_properties_id = " + discussionPropertiesMstIdForIncorrect +
                " AND dq.free = true" +
                " AND dq.site_id = " + siteId +
                " ORDER BY dqp.date_created DESC " +
                " LIMIT "+limit+" OFFSET " + (batchIndex * skip)

        List questions = null
        questions = new ArrayList()
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { r ->
            Long _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE discussion_question_id = " + _id
            results = sql1.rows(sql);
            List ansqwersOfAQuestion = new ArrayList()
            results.collect{ a ->
                ansqwersOfAQuestion.add(a)
            }
            r['qaList'] = ansqwersOfAQuestion
            r['answerCount'] = ansqwersOfAQuestion.size()
            questions.add(r)
        }
        Gson gson = new Gson();
        String element = gson.toJson(questions,new TypeToken<List>() {}.getType())

        if(batchIndex == 0){
            long count = DiscussionQuestionsProperties.countByDiscussionBoardPropertiesId(discussionPropertiesMstIdForIncorrect)
            redisService.("incorrectQuestions_"+siteId) = element
            redisService.("incorrectQuestionsCount_"+siteId) = count
        }else return questions
    }

    def getIncorrectDiscussionQuestionCache(params,def siteId,int batchIndex){
//        if(redisService.("incorrectQuestions_"+siteId) == null){
            updateIncorrectDiscussionQuestionCache(params,siteId,batchIndex)
//        }
        List questions = null
        if(batchIndex == 0) questions = new JsonSlurper().parseText(redisService.("incorrectQuestions_"+siteId))
        else if(batchIndex > 0) questions = updateIncorrectDiscussionQuestionCache(params,siteId,batchIndex)
        return questions
    }

    def updateUnModeratedDiscussionAnswersCache(def siteId){

        String sql = " SELECT DISTINCT dq.id _id," +
                " question," +
                " tags," +
                " dq.created_by createdBy," +
                " dq.img_name img_name," +
                " dq.date_created dateCreated," +
                " da.date_created," +
                " dq.upvote_count upvote_count" +
                " FROM discussion_questions dq, discussion_answers da" +
                " WHERE dq.id = da.discussion_question_id" +
                " AND da.show_answer = false AND dq.dld_id is null" +
                " AND dq.free = true" +
                " AND dq.site_id = " + siteId +
                " AND dq.show_question = true" +
                " AND da.id NOT IN (" +
                " SELECT discussion_answer_id" +
                " FROM discussion_answers_properties" +
                " WHERE discussion_board_properties_id IN ( " + discussionPropertiesMstIdForAbuse + "," + discussionPropertiesMstIdForIncorrect+
                " )"+
                ")" +
                " ORDER BY da.date_created DESC"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        User uQ = null
        List sendQuestions = new ArrayList()
        results.collect { r ->
            Long _id = r['_id']
            def dateCreated = r['dateCreated'];
            List questions = new ArrayList()
            Map answerListMap = new HashMap<String,List>()
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE show_answer = false AND discussion_question_id = " + _id
            results = sql1.rows(sql);
            results.collect{ a ->
                List ansqwersOfAQuestion = new ArrayList()
                uQ = User.findByUsername(r['createdBy'])
                r['userId'] = r['createdBy']
                r['userName'] = uQ.name
                r['uImgId'] = uQ.id
                r['uImgName'] = uQ.profilepic
                r['qImgId'] = r['_id']
                r['qImgName'] = r['img_name']
                uQ = User.findByUsername(a['created_by'])
                a['userId'] = a['created_by']
                a['userName'] = uQ.name
                a['uImgId'] = uQ.id
                a['uImgName'] = uQ.profilepic
                ansqwersOfAQuestion.add(a)
                r['qaList'] = ansqwersOfAQuestion
                r['answerCount'] = ansqwersOfAQuestion.size()
                r['userId'] = r['createdBy']
                questions.add(r)
                answerListMap.put('answersList',questions)
            }
            sendQuestions.add(['answersList':answerListMap.get('answersList'),'_id':_id,'dateCreated':dateCreated])
        }
        Gson gson = new Gson();
        String element = gson.toJson(sendQuestions,new TypeToken<List>() {}.getType())
        redisService.("unModeratedAnswers_"+siteId) = element
    }

    def getUnModeratedDiscussionAnswersCache(def siteId){
        if(true){//redisService.("unModeratedAnswers_"+siteId) == null
            updateUnModeratedDiscussionAnswersCache(siteId)
        }
        List questions = new JsonSlurper().parseText(redisService.("unModeratedAnswers_"+siteId))
        return questions
    }

    def updateDiscussionAbuseAnswersCache(def siteId){

        String sql = " SELECT distinct dq.id _id," +
                " dq.question," +
                " dq.tags," +
                " dq.created_by createdBy," +
                " dq.img_name," +
                " dq.date_created dateCreated," +
                " dap.date_created," +
                " dq.upvote_count" +
                " FROM discussion_questions dq,discussion_answers a, discussion_answers_properties dap" +
                " WHERE dq.id = a.discussion_question_id" +
                " AND a.id = dap.discussion_answer_id AND dq.dld_id is null" +
                " AND dap.discussion_board_properties_id = " + discussionPropertiesMstIdForAbuse +
                " AND dq.site_id = " + siteId +
                " ORDER BY dap.date_created DESC"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

                User uQ = null
        List sendQuestions = new ArrayList()
        results.collect { r ->
            List questions = new ArrayList()
            Map answerListMap = new HashMap<String,List>()
            def _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE id IN ( " +
                    " SELECT discussion_answer_id" +
                    " FROM discussion_answers_properties" +
                    " WHERE discussion_board_properties_id = " + discussionPropertiesMstIdForAbuse + " )" +
                    " AND discussion_question_id = " + _id

            results = sql1.rows(sql);
            results.collect{ a ->
                List ansqwersOfAQuestion = new ArrayList()
                uQ = User.findByUsername(r['createdBy'])
                r['userId'] = r['createdBy']
                r['userName'] = uQ.name
                r['uImgId'] = uQ.id
                r['uImgName'] = uQ.profilepic
                r['qImgId'] = r['_id']
                r['qImgName'] = r['img_name']
                uQ = User.findByUsername(a['created_by'])
                a['userId'] = a['created_by']
                a['userName'] = uQ.name
                a['uImgId'] = uQ.id
                a['uImgName'] = uQ.profilepic
                ansqwersOfAQuestion.add(a)
                r['qaList'] = ansqwersOfAQuestion
                r['answerCount'] = ansqwersOfAQuestion.size()
                r['userId'] = r['createdBy']
                questions.add(r)
                answerListMap.put('answersList',questions)
            }
            sendQuestions.add(['answersList':answerListMap.get('answersList'),'_id':_id])
        }

//            ansList.each { def ans ->
//                ans.qaList.createdOn = utilService.convertDate(ans.qaList.createdOn,"UTC","IST")
//            }
        Gson gson = new Gson();
        String element = gson.toJson(sendQuestions,new TypeToken<List>() {}.getType())
        redisService.("abuseAnswers_"+siteId) = element
    }

    def getDiscussionAbuseAnswersCache(def siteId){
        if(true){//redisService.("abuseAnswers_"+siteId) == null
            updateDiscussionAbuseAnswersCache(siteId)
        }
        List questions = new JsonSlurper().parseText(redisService.("abuseAnswers_"+siteId))
        return questions
    }

    def updateDiscussionIncorrectAnswersCache(def siteId){


        String sql = " SELECT distinct dq.id _id," +
                " dq.question," +
                " dq.tags," +
                " dq.created_by createdBy," +
                " dq.img_name," +
                " dq.date_created dateCreated," +
                " dap.date_created," +
                " dq.upvote_count" +
                " FROM discussion_questions dq,discussion_answers a, discussion_answers_properties dap" +
                " WHERE dq.id = a.discussion_question_id" +
                " AND a.id = dap.discussion_answer_id AND dq.dld_id is null" +
                " AND dap.discussion_board_properties_id = " + discussionPropertiesMstIdForIncorrect +
                " AND dq.site_id = " + siteId +
                " ORDER BY dap.date_created DESC"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        User uQ = null
        List sendQuestions = new ArrayList()
        results.collect { r ->
            List questions = new ArrayList()
            Map answerListMap = new HashMap<String,List>()
            def _id = r['_id']
            sql = "SELECT *" +
                    " FROM discussion_answers " +
                    " WHERE id IN ( " +
                    " SELECT discussion_answer_id" +
                    " FROM discussion_answers_properties" +
                    " WHERE discussion_board_properties_id = " + discussionPropertiesMstIdForIncorrect +
                    " )"
                    " AND discussion_question_id = " + _id
            results = sql1.rows(sql);
            results.collect{ a ->
                List ansqwersOfAQuestion = new ArrayList()
                uQ = User.findByUsername(r['createdBy'])
                r['userId'] = r['createdBy']
                r['userName'] = uQ.name
                r['uImgId'] = uQ.id
                r['uImgName'] = uQ.profilepic
                r['qImgId'] = r['_id']
                r['qImgName'] = r['img_name']
                uQ = User.findByUsername(a['created_by'])
                a['userId'] = a['created_by']
                a['userName'] = uQ.name
                a['uImgId'] = uQ.id
                a['uImgName'] = uQ.profilepic
                ansqwersOfAQuestion.add(a)
                r['qaList'] = ansqwersOfAQuestion
                r['answerCount'] = ansqwersOfAQuestion.size()
                r['userId'] = r['createdBy']
                questions.add(r)
                answerListMap.put('answersList',questions)
            }
            sendQuestions.add(['answersList':answerListMap.get('answersList'),'_id':_id])
        }

//            ansList.each { def ans ->
//                ans.qaList.createdOn = utilService.convertDate(ans.qaList.createdOn,"UTC","IST")
//            }
        Gson gson = new Gson();
        String element = gson.toJson(sendQuestions,new TypeToken<List>() {}.getType())
        redisService.("incorrectAnswers_"+siteId) = element
    }

    def getDiscussionIncorrectAnswersCache(def siteId){
        if(true){ //redisService.("incorrectAnswers_"+siteId) == null
            updateDiscussionIncorrectAnswersCache(siteId)
        }
        List questions = new JsonSlurper().parseText(redisService.("incorrectAnswers_"+siteId))
        return questions
    }
}
