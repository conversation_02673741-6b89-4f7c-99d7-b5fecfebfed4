/*
 * This Grails service class handles the calculation of delivery charges for items in a shopping cart.
 */
package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.SiteMst
import com.wonderslate.publish.Publishers
import grails.converters.JSON
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import groovy.json.JsonSlurper

import java.text.SimpleDateFormat

@Transactional
class DeliveryChargesService {
    DataProviderService dataProviderService
    BookPriceService bookPriceService
    MailService mailService


    /**
     * Calculates the delivery charges based on the items in the shopping cart for the given username.
     *
     * @param username The username of the user.
     * @return The total shipping costs as a Double.
     */
    Double deliveryChargesCalculator(String username,boolean addVendorRecords,Integer cartMstId) {
        List items = ShoppingCartActiveDtl.findAllByUsername(username)
        Double shippingCosts = new Double(0.0)
        // Separate out items by vendor
        HashMap vendorItems = new HashMap()
        Long vendorId
        String status
        Integer siteId
        HashMap books = new HashMap()

        if (items.size() > 0) {
            siteId = items[0].siteId
            items.each { item ->
                // Check if the item in the shopping cart has a publisher ID
                if ("printbook".equals(item.bookType) || "combo".equals(item.bookType)) {
                    BooksMst booksMst = dataProviderService.getBooksMst(item.bookId)
                    if (item.publisherId == null) {
                        vendorId = booksMst.publisherId
                    }else vendorId = item.publisherId

                    //maintaining the quantity list to update stock
                    if(booksMst.currentStock!=null) {
                        if (books.get(booksMst.id) == null) {
                            HashMap bookDetails = new HashMap()
                            bookDetails.put("quantity", new Integer(1))
                            bookDetails.put("currentStock",booksMst.currentStock)
                            books.put(booksMst.id, bookDetails)
                        } else {
                            HashMap bookDetails = books.get(booksMst.id)
                            Integer quantity = new Integer(bookDetails.get("quantity").intValue() + 1)
                            bookDetails.put("quantity", quantity)
                            books.put(booksMst.id, bookDetails)
                        }
                    }
                    BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer("" + item.bookId), item.bookType)
                    if (vendorItems.get(vendorId) == null) {
                        // Vendor not present, add vendor details
                        HashMap itemDetails = new HashMap()
                        itemDetails.put("price", bookPriceDtl != null ? bookPriceDtl.sellPrice : new Double(0.0))
                        itemDetails.put("weight", item.bookWeight != null ? item.bookWeight : new Double(0.0))
                        itemDetails.put("bookTitle",booksMst.title)
                         vendorItems.put(vendorId, itemDetails)
                    } else {
                        // Vendor already present
                        HashMap itemDetails = vendorItems.get(vendorId)
                        Double price = itemDetails.get("price")
                        Double weight = itemDetails.get("weight")

                        String bookTitles = itemDetails.get("bookTitle")
                        if (bookPriceDtl != null) price = new Double(price.doubleValue() + bookPriceDtl.sellPrice.doubleValue())
                        if (item.bookWeight != null) weight = new Double(weight.doubleValue() + item.bookWeight.doubleValue())
                        itemDetails.put("price", price)
                        itemDetails.put("weight", weight)
                        itemDetails.put("bookTitle",bookTitles+","+booksMst.title)
                        vendorItems.put(vendorId, itemDetails)
                    }
                }
            }

            // Now call the calculation method for each vendor
            HashMap deliveryCosts
            BillShipAddressHolder billShipAddressHolder = BillShipAddressHolder.findByUsername(username)
            vendorItems.each { publisherIdTemp, itemDetails ->
                // Access the values in itemDetails HashMap
                Double price = itemDetails.price
                Double weight = itemDetails.weight
                String bookTitle = itemDetails.bookTitle
                deliveryCosts = calculateDeliveryCosts(
                        publisherIdTemp,
                        price,
                        weight,
                        billShipAddressHolder != null ? billShipAddressHolder.shipCity : "Default",
                        billShipAddressHolder != null ? billShipAddressHolder.shipState : "Default"
                )
                shippingCosts = new Double(shippingCosts.doubleValue() + deliveryCosts.get("deliveryCosts").doubleValue())
                // Code to add vendor-specific database inserts
                if(addVendorRecords){
                    VendorDeliveryCosts vendorDeliveryCosts = new VendorDeliveryCosts(vendorId: publisherIdTemp,cartMstId: cartMstId,deliverCost:deliveryCosts.get("deliveryCosts"))
                    vendorDeliveryCosts.save(failOnError: true, flush: true)

                    //this is the place to send out email to publisher.
                    Publishers publishers = dataProviderService.getPublisher(publisherIdTemp)
                    String toEmail = "<EMAIL>"
                    if(publishers!=null&&publishers.email!=null) toEmail +=","+publishers.email

                            SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                            String    fromEmail
                            if(siteMst.fromEmail!=null&&!"".equals(siteMst.fromEmail))
                                fromEmail = siteMst.fromEmail
                            else
                                fromEmail = "Wonderslate <<EMAIL>>"
                            String mailText = "Hello \n\n Order for following books are received.\n\n"
                            String[] bookTitles = bookTitle.split(',')
                            for(int i=0;i<bookTitles.length;i++){
                                mailText +=bookTitles[0]+"\n"
                            }
                            mailText +="\n\n The delivery address is \n"+billShipAddressHolder.shipFirstName+"\n"+billShipAddressHolder.shipAddressLine1+"\n"+
                                    billShipAddressHolder.shipAddressLine2+"\n"+billShipAddressHolder.shipCity+"\n"+billShipAddressHolder.shipState+" "+billShipAddressHolder.shipPincode+
                                    "\n \n Please login to system to check full details."
                            try {
                                String[] toEmails = toEmail.split(',')
                                for(int i=0;i<toEmails.length;i++) {
                                    if("<EMAIL>"!=toEmails[i]) {
                                        mailService.sendMail {
                                            async true
                                            to toEmails[i]
                                            from fromEmail
                                            subject "Order received " + cartMstId
                                            text mailText
                                        }
                                    }
                                }
                            }catch(Exception e){
                                println("Exception in sending userBookPurchase email to "+toEmail+" and exception is "+e.toString())
                            }



                    //update stock
                    books.each{bookIdTemp,book->
                        int currentStock = book.currentStock.intValue()-book.quantity.intValue()
                       BooksMst.executeUpdate("update BooksMst set current_stock=" + currentStock +" where id=" + bookIdTemp)
                        BooksMst.wsuser.executeUpdate("update BooksMst set current_stock=" + currentStock +" where id=" + bookIdTemp)
                        BooksMst.wsshop.executeUpdate("update BooksMst set current_stock=" + currentStock +" where id=" +bookIdTemp)
                    }

                }
            }

            return shippingCosts
        } else {
            return shippingCosts
        }
    }

/**
 * Calculates the delivery costs based on the given parameters.
 *
 * @param vendorId The ID of the vendor.
 * @param bookPrice The price of the book.
 * @param weight The weight of the book.
 * @param city The city for delivery.
 * @param state The state for delivery.
 * @return A HashMap containing the status and delivery costs.
 */
    HashMap calculateDeliveryCosts(Long vendorId, Double bookPrice, Double weight, String city, String state) {
        HashMap deliveryCosts = new HashMap()
        String status
        VendorDeliveryDetails vendorDeliveryDetails = VendorDeliveryDetails.findByVendorId(vendorId)
       if (vendorDeliveryDetails == null) {
            deliveryCosts.put("status", "No delivery costs added to publisher")
            deliveryCosts.put("deliveryCosts", new Double(0.0))
            return deliveryCosts
        } else {
            if (vendorDeliveryDetails.threshold != null && (vendorDeliveryDetails.threshold.doubleValue() == 0.0 || bookPrice.doubleValue() > vendorDeliveryDetails.threshold.doubleValue())) {
                deliveryCosts.put("status", "Greater than threshold value")
                deliveryCosts.put("deliveryCosts", new Double(0.0))
                return deliveryCosts
            } else {
                // Now we have to calculate the details. Check if there is a flat fee
                if (vendorDeliveryDetails.flatFee != null) {
                    deliveryCosts.put("status", "Flat fees applied")
                    deliveryCosts.put("deliveryCosts", vendorDeliveryDetails.flatFee)
                    return deliveryCosts
                } else {
                    // Now let's see the calculation type
                    if ("Location".equals(vendorDeliveryDetails.calculationType)) {
                        // Do location-based price calculation
                        return getLocationBasedDeliveryCharges(vendorId, city, state)
                    } else if ("Weight".equals(vendorDeliveryDetails.calculationType)) {
                        // Do weight-based price calculation
                        return getWeightBasedDeliveryCharges(vendorId, weight)
                    } else {
                        deliveryCosts.put("status", "This case shouldn't have happened. No logic for price calculation is found")
                        deliveryCosts.put("deliveryCosts", new Double(0.0))
                    }
                }
            }
        }
        return deliveryCosts
    }

/**
 * Calculates the delivery costs based on the vendor, city, and state.
 *
 * @param vendorId The ID of the vendor.
 * @param city The city for delivery.
 * @param state The state for delivery.
 * @return A HashMap containing the status and delivery costs.
 */
    HashMap getLocationBasedDeliveryCharges(Long vendorId, String city, String state) {
        HashMap deliveryCosts = new HashMap()
        DeliveryCostsByLocation deliveryCostsByLocation = DeliveryCostsByLocation.findByVendorIdAndLocationTypeAndLocationName(vendorId, "State", state)
        if (deliveryCostsByLocation != null) {
            deliveryCosts.put("status", "Location charges for state " + state + " applied")
            deliveryCosts.put("deliveryCosts", deliveryCostsByLocation.fee)
            return deliveryCosts
        } else {
            deliveryCostsByLocation = DeliveryCostsByLocation.findByVendorIdAndLocationTypeAndLocationName(vendorId, "City", city)
            if (deliveryCostsByLocation != null) {
                deliveryCosts.put("status", "Location charges for city " + city + " applied")
                deliveryCosts.put("deliveryCosts", deliveryCostsByLocation.fee)
                return deliveryCosts
            } else {
                deliveryCostsByLocation = DeliveryCostsByLocation.findByVendorIdAndLocationTypeAndLocationName(vendorId, "Default", "Default")
                if (deliveryCostsByLocation != null
                ) {
                    deliveryCosts.put("status", "Default location charges applied")
                    deliveryCosts.put("deliveryCosts", deliveryCostsByLocation.fee)
                    return deliveryCosts
                } else {
                    deliveryCosts.put("status", "Did not find appropriate price as Default price was not added")
                    deliveryCosts.put("deliveryCosts", new Double(0.0))
                    return deliveryCosts
                }
            }
        }
        return deliveryCosts
    }
    /**
     * Calculates the delivery costs based on the vendor and weight.
     *
     * @param vendorId The ID of the vendor.
     * @param weight The weight of the book.
     * @return A HashMap containing the status and delivery costs.
     */
    HashMap getWeightBasedDeliveryCharges(Long vendorId, Double weight) {
        HashMap deliveryCosts = new HashMap()
        DeliveryCostsByWeight deliveryCostsByWeight = DeliveryCostsByWeight.findByVendorIdAndWeightFromLessThanEqualsAndWeightToGreaterThanEquals(vendorId, weight, weight)
        if (deliveryCostsByWeight != null) {
            deliveryCosts.put("status", "Weight-based prices applied")
            deliveryCosts.put("deliveryCosts", deliveryCostsByWeight.fee)
            return deliveryCosts
        } else {
            deliveryCosts.put("status", "No appropriate weight-based fees found")
            deliveryCosts.put("deliveryCosts", new Double(0.0))
            return deliveryCosts
        }
    }


}

