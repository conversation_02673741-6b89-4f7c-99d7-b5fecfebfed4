package com.wonderslate.shop

import com.wonderslate.data.BooksMst
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.usermanagement.UserManagementService
import grails.async.Promise
import grails.async.Promises
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import groovy.sql.Sql
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async

import java.text.DateFormat
import java.text.SimpleDateFormat

@Transactional
class PurchaseSubService {

    def updateLastSold(bookId){
        BooksMst.executeUpdate("update BooksMst set lastSold =now() where id=" + bookId)
        BooksMst.wsshop.executeUpdate("update BooksMst set lastSold =now() where id=" + bookId)
        BooksMst.wsuser.executeUpdate("update BooksMst set lastSold =now() where id=" + bookId)
    }





}
