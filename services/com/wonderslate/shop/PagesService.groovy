package com.wonderslate.shop

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ResourceDtl
import com.wonderslate.usermanagement.User
import grails.transaction.Transactional

import java.nio.file.Files
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import grails.converters.JSON

@Transactional
class PagesService {
    DataProviderService dataProviderService
    def springSecurityService
    def redisService
    def grailsApplication

    def addPage(String pageName, Integer siteId, String username){
        String convertedName = pageName.trim().toLowerCase().replaceAll("\\s", "-")

        PagesMst pagesMst = PagesMst.findBySiteIdAndLink(siteId,convertedName)
        if(pagesMst==null){
            pagesMst = new PagesMst(name:pageName,link: convertedName,createdBy: username,siteId: siteId,resLink: 'empty',fileName: 'empty')
            pagesMst.save(flush: true, failOnError: true)
            return ""+pagesMst.id
        }else{
            return "exists"
        }
    }

    def addPageDetails(params,session){
        PagesMst pagesMst = PagesMst.findById(new Long(params.pageId))
        pagesMst.showInFooter = params.showInFooter
        pagesMst.showInHeader = params.showInHeader
        pagesMst.dateCreated = new Date()
        pagesMst.status = 'unpublished'
        String fileData

        //add the logic to move the ckeditor contents to its file location
        pagesMst.resLink = 'empty'
        pagesMst.save(failOnError: true, flush: true)
        pagesMst.fileName = "res"+pagesMst.id
        pagesMst.resLink = "upload/pages/" + params.siteId + "/" + pagesMst.id
        fileData = moveExtractedImages(pagesMst.id,params.page,params.siteId)
        pagesMst.save(failOnError: true, flush: true)

        if (fileData) {
            File uploadDir

            uploadDir = new File("upload/pages/" + params.siteId + "/" + pagesMst.id+ "/extract/")

            if (!uploadDir.exists()) uploadDir.mkdirs()
            Files.write(Paths.get(pagesMst.resLink.substring(0, pagesMst.resLink.length()) + ".ws"), fileData.getBytes("UTF-8"))

            File zipFile = new File(pagesMst.resLink.substring(0, pagesMst.resLink.length()) + "_zippied.zip")
            if (zipFile.exists()) zipFile.delete()

            if (!System.properties['os.name'].toLowerCase().contains('windows') && !System.properties['os.name'].toLowerCase().contains('mac')) {
                def scriptCom = "${grailsApplication.config.grails.basedir.path}/optimiseRes.sh ${pagesMst.resLink.substring(0, pagesMst.resLink.lastIndexOf("/") + 1)}"
                def proc = scriptCom.execute()

                //kill after 10 minutes if still running
                proc.waitForOrKill(600000)

                if (proc.exitValue() != 0) {
                    println "[[return code: ${proc.exitValue()}]]"
                    println "[[stderr: ${proc.err.text}]]"
                }
            }
            redisService.("pageDtl_"+pagesMst.id) = pagesMst
        }

        return pagesMst.id
    }

    String moveExtractedImages(Long pageId,String notes,String siteId){
        boolean extractedImageExists = true
        String fileData = notes
        int startingIndex = 0
        int startIndex = 0
        while(extractedImageExists){
            if(fileData.indexOf("imagesfrompdf",startingIndex)!=-1){

                //Step1 . extract the source file path

                int startSourceIndex = fileData.indexOf("source=",startIndex)
                String src = fileData.substring((startSourceIndex+7),(fileData.indexOf('.png',startSourceIndex)+4))

                File srcFile= new File(src)
                startIndex = fileData.indexOf("imagesfrompdf/",startingIndex)
                String fileName = fileData.substring((startIndex+14),(fileData.indexOf('.png',startIndex)+4))

                //step 2. create destination path
                String destDir = "upload/pages/" + siteId + "/" + pageId
                File uploadDir = new File(destDir)
                if(!uploadDir.exists()) uploadDir.mkdirs()
                //step 3. copy file
                File dest = new File(destDir+fileName)
                Files.copy(srcFile.toPath(), dest.toPath(), StandardCopyOption.REPLACE_EXISTING)
                //moving the index
                startingIndex = startIndex+14

            }else extractedImageExists=false
        }
        fileData = fileData.replace("/funlearn/downloadEpubImage?source=upload/pages/" + siteId + "/" + pageId + "/imagesfrompdf/", "Images/")
        return fileData
    }

    def getAllPagesForSite(session){
        return PagesMst.findAllBySiteId(new Long(session['siteId']))
    }

    def updateStatus(params){
        String pageStatus = params.pageStatus
        PagesMst pagesMst = PagesMst.findById(new Long(params.pageId))
        if (pageStatus=='published'){
            pagesMst.status = 'unpublished'
        }else if (pageStatus=='unpublished'){
            pagesMst.status = 'published'
        }
        pagesMst.save(failOnError: true, flush: true)
        redisService.("pageDtl_"+pagesMst.id) = pagesMst
        return [status:"Ok"]
    }
    def getPageTitle(params){
        def title = PagesMst.findById(new Long(params.pageId))
        String pageTitle = title.name
        return pageTitle
    }
    def getCustomPageMenus(Long siteId){
        String pageStatus = 'published'
        List updatePagesList = PagesMst.findAllBySiteIdAndStatus(new Long(siteId),pageStatus)
        Gson gson = new Gson();
        String element = gson.toJson(updatePagesList, new TypeToken<List>() {}.getType())
        redisService.("customPageMenus_"+siteId) = element
    }

    def getCustomPageDtl(Integer pageId){
        PagesMst pagesMst  = redisService.memoizeDomainObject(PagesMst, "pageDtl_"+pageId) {
            return PagesMst.findById(pageId)
        }
        return pagesMst
    }

}
