package com.wonderslate.shop

import com.amazon.SellingPartnerAPIAA.AWSAuthenticationCredentials
import com.amazon.SellingPartnerAPIAA.AWSAuthenticationCredentialsProvider
import com.amazon.SellingPartnerAPIAA.LWAAuthorizationCredentials
import com.amazon.sellingpartner.api.MessagingApi
import com.amazon.sellingpartner.api.OrdersV0Api
import com.amazon.sellingpartner.api.ReportsApi
import com.amazon.sellingpartner.api.SellersApi
import com.amazon.sellingpartner.api.TokensApi
import com.squareup.okhttp.MediaType
import com.squareup.okhttp.OkHttpClient
import com.squareup.okhttp.Request
import com.squareup.okhttp.RequestBody
import com.squareup.okhttp.Response
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.usermanagement.UserManagementService
import grails.transaction.Transactional

import javax.annotation.PostConstruct
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import com.amazon.sellingpartner.ApiException;
import com.amazon.sellingpartner.api.FeedsApi;
import com.amazon.sellingpartner.model.CreateFeedDocumentResponse;
import com.amazon.sellingpartner.model.CreateFeedDocumentSpecification;
import com.amazon.sellingpartner.model.CreateFeedResponse;
import com.amazon.sellingpartner.model.CreateFeedSpecification;
import com.amazon.sellingpartner.model.GetOrderBuyerInfoResponse;
import com.amazon.sellingpartner.model.GetOrderItemsResponse;
import com.amazon.sellingpartner.model.GetOrdersResponse;
import com.amazon.sellingpartner.model.Order;
import com.amazon.sellingpartner.model.OrderItem;
import com.amazon.spapi.documents.UploadHelper;







@Transactional
class AmazonService {

    WsshopService wsshopService
    UtilService utilService
    UserManagementService userManagementService
    DataProviderService dataProviderService
    def springSecurityService

    private String awsAccessKeyId = "********************";
    private String awsSecretKey = "OIAzzesL8cm1g/s8tmc4HO3hjKDR02Q2WBbuqKfV";
    private String awsRegion = "eu-west-1";
    private String awsRoleARN = "arn:aws:iam::718277118116:role/selling.partner";
    private String authEndpoint = "https://api.amazon.com/auth/o2/token";
    private String lwaClientId = "amzn1.application-oa2-client.90f5a51c79bc4e4e9ec4816a6a4f1e66";
    private String lwaClientSecret = "acc90c2af2b2ba80af5b8ff3f908633182210a3e354974de783b09b7450f2efa";
    private String refreshToken = "Atzr|IwEBIP04TYDdyOrWT8SvQF-luh0yYCOeF6Oml9nfa561lElCa5MrggiFeDp0vw19uxbGFcMr0IiL7m7fOQZ4ELDXWqWxZPmrbB5l4viBF3WkXDIz_N4f29iCfnjjq5WacLCxjKcMRPw3XAFoG7USvna4U09rtNHqo26_YcHKEgru6YgLHlA2UiD016kgJEKunGa4vEMlQgJbtm3ZeFVPOavDRzgESktrVtiW-kEWCta44h8my48LFQMxeXojrSL_RQ2oYviNYFT7eZgsiuGR8WZVRghUn74XDh8zBaI_-Z718CvIXg";
    private String endpoint = "https://sellingpartnerapi-eu.amazon.com";
    private String marketplaceId ="A21TJRUUN4KGV";
    private SellersApi api;
    private ReportsApi reportApi;
    private AWSAuthenticationCredentials awsAuthCreds;
    private AWSAuthenticationCredentialsProvider awsAuthCredsProvider;
    private LWAAuthorizationCredentials lwaAuthCreds;
    private OrdersV0Api orderApi;
    private FeedsApi feedsApi;
    private MessagingApi messagingApi;
    private TokensApi tokenApi;

    private void _authenticate() {
        // Configure AWS credentials
        awsAuthCreds = AWSAuthenticationCredentials.builder()
                .accessKeyId(awsAccessKeyId)
                .secretKey(awsSecretKey)
                .region(awsRegion)
                .build();

        // Configure AWS credentials provider
        awsAuthCredsProvider = AWSAuthenticationCredentialsProvider.builder()
                .roleArn(awsRoleARN)
                .roleSessionName(UUID.randomUUID().toString())
                .build();

        // Configure LWA credentials
        lwaAuthCreds = LWAAuthorizationCredentials.builder()
                .clientId(lwaClientId)
                .clientSecret(lwaClientSecret)
                .refreshToken(refreshToken)
                .endpoint(authEndpoint)
                .build();
    }

    @PostConstruct
    private void init() {
        // Authenticate with Amazon
        this._authenticate();

        // Each application role corresponds to one or more ___Api classes. In this case, we're
        // using the SellersApi class, which maps to the Selling Partner Insights role.
        api = new SellersApi.Builder()
                .awsAuthenticationCredentials(this.awsAuthCreds)
                .lwaAuthorizationCredentials(this.lwaAuthCreds)
                .awsAuthenticationCredentialsProvider(this.awsAuthCredsProvider)
                .endpoint(endpoint)
                .build();

        reportApi = new ReportsApi.Builder()
                .awsAuthenticationCredentials(this.awsAuthCreds)
                .lwaAuthorizationCredentials(this.lwaAuthCreds)
                .awsAuthenticationCredentialsProvider(this.awsAuthCredsProvider)
                .endpoint(endpoint)
                .build();

        orderApi = new OrdersV0Api.Builder()
                .awsAuthenticationCredentials(this.awsAuthCreds)
                .lwaAuthorizationCredentials(this.lwaAuthCreds)
                .awsAuthenticationCredentialsProvider(this.awsAuthCredsProvider)
                .endpoint(endpoint)
                .build();

        feedsApi = new FeedsApi.Builder()
                .awsAuthenticationCredentials(this.awsAuthCreds)
                .lwaAuthorizationCredentials(this.lwaAuthCreds)
                .awsAuthenticationCredentialsProvider(this.awsAuthCredsProvider)
                .endpoint(endpoint)
                .build();

        messagingApi = new MessagingApi.Builder()
                .awsAuthenticationCredentials(this.awsAuthCreds)
                .lwaAuthorizationCredentials(this.lwaAuthCreds)
                .awsAuthenticationCredentialsProvider(this.awsAuthCredsProvider)
                .endpoint(endpoint)
                .build();

        tokenApi = new TokensApi.Builder()
                .awsAuthenticationCredentials(this.awsAuthCreds)
                .lwaAuthorizationCredentials(this.lwaAuthCreds)
                .awsAuthenticationCredentialsProvider(this.awsAuthCredsProvider)
                .endpoint(endpoint)
                .build();
    }



     void downloadAmazonOrder() {
        println("### In AmazonOrderService :: downloadAmazonOrder ###");
        println("Starting to check for new Amazon Orders...");

        GetOrdersResponse response;
        try {
            init()
            response = orderApi.getOrders(Arrays.asList(marketplaceId),
                    DateTimeFormatter.ISO_DATE_TIME.format(LocalDateTime.now().minusDays(17)), null, null, null,
                    Arrays.asList("Unshipped","Shipped"), null, null, null, null, 100, null, null, null,null,false,null);
            if (null != response) {
                if (null != response.getErrors()) {
                    log.error(response.getErrors().stream().toString());
                    throw new Exception(response.getErrors().stream().toString());
                } else if (!response.getPayload().getOrders().isEmpty()) {
                    StringBuilder headerBuilder = new StringBuilder();
                    headerBuilder.append("order-id").append("\t").append("order-item-id").append("\t").append("quantity").append("\t")
                            .append("ship-date").append("\t").append("carrier-code").append("\t").append("carrier-name").append("\t").append("tracking-number")
                            .append("\t").append("ship-method").append("\t").append("transparency_code").append("\t").append("\n");
                    String userName = "System"
                    Integer siteId = new Integer(1)
                    if(springSecurityService.currentUser!=null) userName = springSecurityService.currentUser.username
                    for (Order order : response.getPayload().getOrders()) {
                        println("order id ="+order.getAmazonOrderId()+" and status="+order.getOrderStatus())
                        try {

                            GetOrderBuyerInfoResponse buyerInfoResponse = orderApi.getOrderBuyerInfo(order.getAmazonOrderId());
                            GetOrderItemsResponse itemResponse = orderApi.getOrderItems(order.getAmazonOrderId(),null);
                           def orderAckNo
                            for (OrderItem item : itemResponse.getPayload().getOrderItems()) {
                                double itemPrice=0;
                                if(null != item.getItemPrice().getAmount()) {
                                    itemPrice= Double.sum(Double.valueOf(item.getItemPrice().getAmount()), Double.valueOf(item.getItemTax().getAmount()));
                                }

                                orderAckNo = wsshopService.addExternalOrder("Amazon",order.getAmazonOrderId(),item.getSellerSKU(),""+itemPrice,"systemCreated",siteId)
                                println("order acknowledgement number="+orderAckNo)
                            }


                            println("Sending email to customer with the download url..");
                            println("buyer email="+buyerInfoResponse.getPayload().getBuyerEmail()+" and buyer name="+buyerInfoResponse.getPayload().getBuyerName())
                            SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                            String url = siteMst.domainUriPrefix + "/externalOrder?id="+orderAckNo
                            userManagementService.sendAmazonShippedEmail(buyerInfoResponse.getPayload().getBuyerEmail(),buyerInfoResponse.getPayload().getBuyerName(),order.getAmazonOrderId(),url)

                            for (OrderItem item : itemResponse.getPayload().getOrderItems()) {
                                headerBuilder.append(order.getAmazonOrderId()).append("\t").append(item.getOrderItemId()).append("\t").append(item.getQuantityOrdered()).append("\t")
                                        .append(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDateTime.now())).append("\t").append("Other").append("\t").append("EmailDelivery")
                                        .append("\t").append(orderAckNo)
                                        .append("\t").append("Email").append("\t").append("").append("\n");
                            }

                        } catch (NumberFormatException e) {
                            e.printStackTrace();
                            log.error(e.getMessage());
                        } catch (IOException e) {
                            e.printStackTrace();
                            log.error(e.getMessage());
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error(e.getMessage());
                        }
                    }
                    println("Updating Amazon to mark Order as shipped");
                    println(headerBuilder.toString());

                        String feedId = uploadFeed(headerBuilder.toString(),"POST_FLAT_FILE_FULFILLMENT_DATA",Arrays.asList(marketplaceId),"text/tab-separated-values; charset=UTF-8");
                        if(null != feedId) {
                            println("Order Shipment Feed uploaded succesfully and Feed Id: " + feedId);
                        }

                }else {
                    println("No new Unshipped Order");
                }
            }
        } catch (ApiException e1) {
            e1.printStackTrace();
            log.error(e1.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }

        println("Finished checking for new Amazon Orders...");
        println("### Exiting from AmazonOrderService :: downloadAmazonOrder ###");
    }

    private void uploadFeedToAmazon(String url, String content,String charset) throws Exception {
        final UploadHelper uploadHelper = new UploadHelper.Builder().build();
        if(!content.isEmpty()) {
            println("Encrytping the feed file before upload...");
            OkHttpClient client = new OkHttpClient();
            try {
                Request request = new Request.Builder()
                        .url(url)
                        .put(RequestBody.create(MediaType.parse(charset), content.getBytes(StandardCharsets.UTF_8)))
                        .build();

                Response response = client.newCall(request).execute();
                if (!response.isSuccessful()) {
                    System.out.println(
                            String.format("Call to upload document failed with response code: %d and message: %s",
                                    response.code(), response.message()));
                }
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
            //
            println("Feed file uploaded succesfully...");
            println("Feed upload response:: " + uploadHelper.toString());
        }
    }

    private String uploadFeed(String content, String feedType, List<String> marketplaces, String charset) {
        try {
            FeedsApi feedsApi = feedsApi;
            CreateFeedDocumentSpecification feed = new CreateFeedDocumentSpecification();
            feed.setContentType(charset);

            CreateFeedDocumentResponse feedDocumentResponse = feedsApi.createFeedDocument(feed);
            println("Create Feed Document ::" + feedDocumentResponse);
            if (null != feedDocumentResponse) {
                println("Uploading Feed file to Amazon. Feed Document Id::" + feedDocumentResponse.getFeedDocumentId()+", URL:: " + feedDocumentResponse.getUrl());
                //Call to encrypt and Upload the Doc
                uploadFeedToAmazon(feedDocumentResponse.getUrl(), content,charset);
                CreateFeedSpecification createFeedSpecification = new CreateFeedSpecification();
                createFeedSpecification.setFeedType(feedType);
                createFeedSpecification.setInputFeedDocumentId(feedDocumentResponse.getFeedDocumentId());
                createFeedSpecification.setMarketplaceIds(marketplaces);

                CreateFeedResponse createFeedResponse = feedsApi.createFeed(createFeedSpecification);
                println("Feed file uploaded succesfully to Amazon and feed id: "+createFeedResponse.getFeedId());
                return createFeedResponse.getFeedId();
            }
        } catch (com.amazon.sellingpartner.ApiException e) {
            log.error("ApiException Error while trying to upload the feed file: " + e.getResponseBody());
            e.printStackTrace();
        } catch (Exception e) {
            log.error("Error while trying to upload the feed file: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    private boolean isProdProfile(){
        true;
    }


}
