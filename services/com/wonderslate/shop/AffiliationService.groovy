package com.wonderslate.shop

import com.amazon.paapi5.v1.BrowseNode
import com.amazon.paapi5.v1.BrowseNodeChild
import com.amazon.paapi5.v1.GetBrowseNodesRequest
import com.amazon.paapi5.v1.GetBrowseNodesResource
import com.amazon.paapi5.v1.GetBrowseNodesResponse
import com.amazon.paapi5.v1.Item
import com.amazon.paapi5.v1.ItemInfo
import com.amazon.paapi5.v1.MultiValuedAttribute
import com.amazon.paapi5.v1.SortBy

import com.google.gson.JsonParser
import com.ibookso.products.CategoryLevel4
import com.ibookso.products.CategoryLevel1
import com.ibookso.products.CategoryLevel2
import com.ibookso.products.CategoryLevel3
import com.ibookso.products.ExtPublishers
import com.ibookso.products.PrintBooksMst
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksAffiliationDtl
import com.wonderslate.data.BooksMst
import com.wonderslate.data.PrintBooksService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.util.concurrent.TimeUnit
import java.util.regex.Matcher
import java.util.regex.Pattern
import com.amazon.paapi5.v1.ApiClient
import com.amazon.paapi5.v1.ApiException
import com.amazon.paapi5.v1.ErrorData
import com.amazon.paapi5.v1.SearchItemsRequest
import com.amazon.paapi5.v1.SearchItemsResource
import com.amazon.paapi5.v1.SearchItemsResponse
import com.amazon.paapi5.v1.api.DefaultApi
import com.amazon.paapi5.v1.PartnerType;

@Transactional
class AffiliationService {
    def grailsApplication
    def redisService
    DataProviderService dataProviderService
    def servletContext
    static DefaultApi api
    static String partnerTag
    static LinkedHashSet categoriesSet = null
    static int currentIndex
    static int currentPage
    PrintBooksService printBooksService
    static String categoryType=null


    def getBooksAndUpdate(String categoryTypeInput){

        if(categoryTypeInput!=null&&!"".equals(categoryTypeInput)) categoryType=categoryTypeInput
        else categoryType=null
        categoriesSet=null
        println("categoryType=${categoryType}")
        collectCategoriesSet()
        int iterCount = 1
        String currentCategory
        String baseCategory
        boolean itemAdded = false
        CategoryLevel2 categoryLevel2
        CategoryLevel3 categoryLevel3
        CategoryLevel4 categoryLevel4
        for(int i=0;i<10;i++){
            if(categoriesSet==null||categoriesSet.size()==0) {
                collectCategoriesSet()
            }
            currentCategory = categoriesSet.getAt(0)

            //get the baseCategory
             categoryLevel2  = CategoryLevel2.findByBrowseNodeId(currentCategory)
             if(categoryLevel2!=null) baseCategory = categoryLevel2.categoryType
             else{
                 categoryLevel3 = CategoryLevel3.findByBrowseNodeId(currentCategory)
                 if(categoryLevel3!=null) baseCategory = categoryLevel3.categoryType
                 else{
                     categoryLevel4 = CategoryLevel4.findByBrowseNodeId(currentCategory)
                     baseCategory = categoryLevel4.categoryType
                 }
             }
            itemAdded = getAllBooks(currentCategory,currentPage,baseCategory)
            Date d2 = new Date()
            Date d1 = new Date()
            long diff
            if(!itemAdded||currentPage==10) {
                //update the category for completed
                 categoryLevel2 = CategoryLevel2.findByBrowseNodeId(currentCategory)
                if(categoryLevel2!=null){
                    categoryLevel2.updated="true"
                    d1 = categoryLevel2.lastUpdated
                    if(d1!=null){
                        diff = d2.getTime() - d1.getTime();
                        categoryLevel2.updateInterval = new Integer(""+ TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS))
                    }
                    categoryLevel2.lastUpdated = new Date()
                    categoryLevel2.save(failOnError: true, flush: true)
                }
                     categoryLevel3 = CategoryLevel3.findByBrowseNodeId(currentCategory)
                    if(categoryLevel3!=null){
                        categoryLevel3.updated="true"
                        d1 = categoryLevel3.lastUpdated
                        if(d1!=null){
                            diff = d2.getTime() - d1.getTime();
                            categoryLevel3.updateInterval = new Integer(""+ TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS))
                        }
                        categoryLevel3.lastUpdated = new Date()
                        categoryLevel3.save(failOnError: true, flush: true)
                    }
                         categoryLevel4 = CategoryLevel4.findByBrowseNodeId(currentCategory)
                        if(categoryLevel4!=null){
                           categoryLevel4.updated="true"
                            d1 = categoryLevel4.lastUpdated
                            if(d1!=null){
                                diff = d2.getTime() - d1.getTime();
                                categoryLevel4.updateInterval = new Integer(""+ TimeUnit.DAYS.convert(diff, TimeUnit.MILLISECONDS))
                            }
                            categoryLevel4.lastUpdated = new Date()
                            categoryLevel4.save(failOnError: true, flush: true)
                        }
                categoriesSet.remove(currentCategory)
                currentPage = 1
            }else{
                currentPage++
            }
           Thread.sleep(60000)
        }

    }


    def initMethod(){

    }
    def collectCategoriesSet(){
        categoriesSet = new LinkedHashSet()
        String additionalCondition=""
        if(categoryType!=null) additionalCondition = " where category_type='"+categoryType+"'"
        else additionalCondition = " where category_type not in ('Books')"

        String sql="select browse_node_id,'4',last_updated  from category_level4 " +additionalCondition+
                " union\n" +
                " select browse_node_id,'3',last_updated  from category_level3 " +additionalCondition+
                " union\n" +
                " select browse_node_id,'2',last_updated  from category_level2 " +additionalCondition+
                " order by last_updated limit 20\n"
        println("sql="+sql)
         def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.each{category->
            categoriesSet.add(category.browse_node_id)
        }

        currentIndex = 0
        currentPage = 1

    }

    def getBooksAffiliationDtl(Long bookId,String bookType) {
        return servletContext.getAttribute("booksAffiliationDtl_"+bookType+"_" + bookId);
    }


    def getAffiliationPrices(bookId,String bookType) {
        String searchKey
        String titleSearchkey
        if("print".equals(bookType)){
            PrintBooksMst printBooksMst = printBooksService.getPrintBooksMst(bookId)
            if(printBooksMst.isbn!=null){
                String [] isbns  = printBooksMst.isbn.split(",")
                searchKey = isbns[0]
            }else searchKey = printBooksMst.asin
            titleSearchkey = printBooksMst.title
        }else{
            BooksMst booksMst = dataProviderService.getBooksMst(bookId)
            searchKey = booksMst.isbn != null && !"0".equals(booksMst.isbn) ? booksMst.isbn : booksMst.title
            titleSearchkey = booksMst.title
        }

        BooksAffiliationDtl booksAffiliationDtl = BooksAffiliationDtl.findByBookIdAndBookType(bookId,bookType)
        if (booksAffiliationDtl == null) {

            if (searchKey.length() > 100) searchKey = searchKey.substring(0, 99)
            def res;
            booksAffiliationDtl = new BooksAffiliationDtl(bookId: bookId,bookType: bookType)
            HttpURLConnection connection
            def booksReceived
            def books

            //first get Amazon price
            booksAffiliationDtl = getAmazonPriceInfo(searchKey,booksAffiliationDtl)
            booksAffiliationDtl.save(failOnError: true, flush: true)
        }
        servletContext.setAttribute("booksAffiliationDtl_" +bookType+"_"+ bookId, booksAffiliationDtl);
        return booksAffiliationDtl


    }

    def addDoubleQuotes(inputString) {
        def jsonObject = new JsonParser().parse(inputString)
        return jsonObject.toString()
      }

    String extractInt(String str) {
        // Replacing every non-digit number
        // with a space(" ")
        str = str.replaceAll(",","")
        if (str != null) {
            Pattern decimalNumPattern = Pattern.compile("-?\\d+(\\.\\d+)?");
            Matcher matcher = decimalNumPattern.matcher(str);

            List<String> decimalNumList = new ArrayList<>();
            while (matcher.find()) {
                decimalNumList.add(matcher.group());
            }


            return decimalNumList[0];
        } else return str
    }

    def amazonSearch(searchKey) {
        def baseUrl = new URL('https://ws-in.amazon-adsystem.com/widgets/q?Operation=GetResults&' +
                'Keywords=' + URLEncoder.encode(searchKey, "UTF-8") + // keywords parameter should contain the search string in url encoded form
                '&SearchIndex=Books&multipageStart=0&InstanceId=0&multipageCount=10&TemplateId=MobileSearchResults&ServiceVersion=20070822&MarketPlace=IN')
        HttpURLConnection connection = (HttpURLConnection) baseUrl.openConnection()
        def res
        try {
            connection = (HttpURLConnection) baseUrl.openConnection()
            connection.addRequestProperty("Content-type", "application/x-www-form-urlencoded")
            connection.with {
                doOutput = true
                requestMethod = 'GET'
                res = content.text

                res = res.substring(17, res.length() - 2) // to get the data string
            }
        } catch (Exception e) {
            println("amazon exception " + e.toString())
        }

        def booksReceived = new JsonSlurper().parseText(addDoubleQuotes(res))
        def books = booksReceived.results
        return books
    }

    static setAmazonClient(){
        ApiClient client = new ApiClient();
        String ACCESS_KEY = "********************"
        String SECRET_KEY = "/tk1Sl49IwH2rY2s+9cZevydaP/LFjQvsqTojd5u"
        String REGION = "eu-west-1"
        String HOST = "webservices.amazon.in"
        String PARTNERTAG = "wonderslate-21"
        client.setAccessKey(ACCESS_KEY);
        client.setSecretKey(SECRET_KEY);
        partnerTag = PARTNERTAG;
        client.setHost(HOST);
        client.setRegion(REGION);
         api = new DefaultApi(client);
    }
    def getAmazonProducts(searchKeywords){
        setAmazonClient()
        List<SearchItemsResource> searchItemsResources = new ArrayList<SearchItemsResource>();
        searchItemsResources.add(SearchItemsResource.ITEMINFO_TITLE);
        searchItemsResources.add(SearchItemsResource.OFFERS_LISTINGS_PRICE);
        searchItemsResources.add(SearchItemsResource.CUSTOMERREVIEWS_COUNT);
        searchItemsResources.add(SearchItemsResource.CUSTOMERREVIEWS_STARRATING)
        searchItemsResources.add(SearchItemsResource.BROWSENODEINFO_BROWSENODES);
        searchItemsResources.add(SearchItemsResource.IMAGES_PRIMARY_MEDIUM);
        searchItemsResources.add(SearchItemsResource.ITEMINFO_PRODUCTINFO);
        searchItemsResources.add(SearchItemsResource.ITEMINFO_TECHNICALINFO);


        String searchIndex = "Books";
        SearchItemsResponse response
// Specify keywords
        String keywords = searchKeywords //Ex. "CBSE class 12"
        String nodeId="**********"
        String brandName=""
// Sending the request
        //add browsenode id to get all items
     SearchItemsRequest searchItemsRequest = new SearchItemsRequest().partnerTag(partnerTag).keywords(keywords)
                .resources(searchItemsResources).partnerType(PartnerType.ASSOCIATES);
    /**    SearchItemsRequest searchItemsRequest = new SearchItemsRequest().partnerTag(partnerTag).browseNodeId(nodeId)
                .searchIndex(searchIndex).resources(searchItemsResources).partnerType(PartnerType.ASSOCIATES).sortBy(SortBy.NEWESTARRIVALS).itemCount(new Integer(10)).itemPage(new Integer(1));*/

        try {
             response = api.searchItems(searchItemsRequest);

// Parsing the request
            if (response.getSearchResult() != null) {
                 for(int i=0;i<response.getSearchResult().getItems().size();i++) {
                    brandName=""
                    Item item = response.getSearchResult().getItems().get(i);
                //    BrowseNodeInfo browseNodeInfo = item.getBrowseNodeInfo()

                    if (item != null) {
                        if(item.getItemInfo().getByLineInfo()!=null&&item.getItemInfo().getByLineInfo().manufacturer!=null) brandName = item.getItemInfo().getByLineInfo().manufacturer.getDisplayValue()
                        if (item.getOffers() != null && item.getOffers().getListings() != null
                                && item.getOffers().getListings().get(0).getPrice() != null
                                && item.getOffers().getListings().get(0).getPrice().getDisplayAmount() != null) {
                            println("the price is "+item.getOffers().getListings().get(0).getPrice().getAmount())
                        }

                       ItemInfo itemInfo = item.getItemInfo()
                        if(itemInfo.getFeatures()!=null){
                            MultiValuedAttribute multiValuedAttribute = itemInfo.getFeatures()
                         }

                    }
                }
            }
            if (response.getErrors() != null) {
                println("Printing errors:\nPrinting Errors from list of Errors");
                for (ErrorData error : response.getErrors()) {
                    println("Error code: " + error.getCode());
                    println("Error message: " + error.getMessage());
                }
            }
        } catch (ApiException exception) {
// Exception handling
            println("Error calling PA-API 5.0!");
            println("Status code: " + exception.getCode());
            println("Errors: " + exception.getResponseBody());
            println("Message: " + exception.getMessage());
            if (exception.getResponseHeaders() != null) {
                println("Request ID: " +
                        exception.getResponseHeaders().get("x-amzn-RequestId"));
            }
        } catch (Exception exception) {
            println("Exception message: " + exception.getMessage());
        }

        return response
    }

    def getBrowseInfo(String browseNodeId){

       setAmazonClient()

       println(browseNodeId)
        List<GetBrowseNodesResource> getBrowseNodesResources = new ArrayList<GetBrowseNodesResource>();
        getBrowseNodesResources.add(GetBrowseNodesResource.CHILDREN);

        // Choose browsenode id(s)
        List<String> browseNodeIds = new ArrayList<String>();

        //books
      //  browseNodeIds.add("976389031")

        //categories
        browseNodeIds.add("browseNodeId")
       // browseNodeIds.add("**********");
        //exam preparation
     //   browseNodeIds.add("**********");
        //medical entrance
     //   browseNodeIds.add("**********");

        //aiims and neet
    //    browseNodeIds.add("**********");

        //nursing
    //    browseNodeIds.add("**********")


        // Forming the request
        GetBrowseNodesRequest getBrowseNodesRequest = new GetBrowseNodesRequest().browseNodeIds(browseNodeIds)
                .partnerTag(partnerTag).resources(getBrowseNodesResources).partnerType(PartnerType.ASSOCIATES);

        try {
            // Sending the request
            GetBrowseNodesResponse response = api.getBrowseNodes(getBrowseNodesRequest);

            if (response.getBrowseNodesResult() != null && response.getBrowseNodesResult().getBrowseNodes() != null) {
                Map<String, BrowseNode> responseList = parse_response(response.getBrowseNodesResult().getBrowseNodes());


            }
            if (response.getErrors() != null) {
                System.out.println("Printing Errors:\nPrinting Errors from list of Errors");
                for (ErrorData error : response.getErrors()) {
                    System.out.println("Error code: " + error.getCode());
                    System.out.println("Error message: " + error.getMessage());
                }
            }
        } catch (ApiException exception) {
            // Exception handling
            System.out.println("Error calling PA-API 5.0!");
            System.out.println("Status code: " + exception.getCode());
            System.out.println("Errors: " + exception.getResponseBody());
            System.out.println("Message: " + exception.getMessage());
            if (exception.getResponseHeaders() != null) {
                // Printing request reference
                System.out.println("Request ID: " + exception.getResponseHeaders().get("x-amzn-RequestId"));
            }
            // exception.printStackTrace();
        } catch (Exception exception) {
            System.out.println("Exception message: " + exception.getMessage());
            // exception.printStackTrace();
        }
        return "Anand"
    }
    private static Map<String, BrowseNode> parse_response(List<BrowseNode> browseNodes) {
        Map<String, BrowseNode> mappedResponse = new HashMap<String, BrowseNode>();

        for (BrowseNode browseNode : browseNodes) {
            List childNodes = browseNode.getChildren()

            mappedResponse.put(browseNode.getId(), browseNode);
        }
        return mappedResponse;
    }

    def addBookToDB(Item item,String categoryId,String baseCategory){
         //check if the item is present in the db
        PrintBooksMst printBooksMst = PrintBooksMst.findByAsin(item.getASIN())
        boolean itemAdded = false
        if(printBooksMst==null){
          try {
              printBooksMst = new PrintBooksMst()
              printBooksMst.title = removeNonUTF8Chars(item.getItemInfo().getTitle().getDisplayValue().replace('.', '').replaceAll("\\.",""))
              printBooksMst.asin = item.getASIN()
              printBooksMst.categoryId = categoryId
              printBooksMst.baseCategory = baseCategory

              if (item.getItemInfo().getByLineInfo() != null && item.getItemInfo().getByLineInfo().manufacturer != null) {
                  String brandName = item.getItemInfo().getByLineInfo().manufacturer.getDisplayValue()
                  ExtPublishers extPublishers = ExtPublishers.findByName(brandName)
                  if (extPublishers == null) {
                      extPublishers = new ExtPublishers(name: brandName)
                      extPublishers.save(failOnError: true, flush: true)

                  }
                  printBooksMst.publisher = "" + extPublishers.id
              }
              if (item.getImages() != null && item.getImages().getPrimary() != null && item.getImages().getPrimary().getLarge() != null) {
                  printBooksMst.coverImage = item.getImages().getPrimary().getLarge().getURL()
              }
              ItemInfo itemInfo = item.getItemInfo()
              if (itemInfo.getExternalIds() != null && itemInfo.getExternalIds().getIsBNs() != null) {
                  MultiValuedAttribute multiValuedAttribute = itemInfo.getExternalIds().getIsBNs()
                  List displayValues = multiValuedAttribute.displayValues
                  if (displayValues != null) {
                      printBooksMst.isbn = displayValues.toString().replace('[', '').replace(']', '')
                  }

              }
              printBooksMst.save(failOnError: true, flush: true)
          }catch (Exception e){
              println("Exception happened while trying to add "+  item.getItemInfo().getTitle().getDisplayValue())
          }
            itemAdded = true

        }
        return itemAdded
    }


    def getAllBooks(String browseNodeId,int pageNo,String baseCategory) {
        setAmazonClient()
        int itemPage = pageNo
        List<SearchItemsResource> searchItemsResources = new ArrayList<SearchItemsResource>();
        searchItemsResources.add(SearchItemsResource.ITEMINFO_TITLE);
        searchItemsResources.add(SearchItemsResource.OFFERS_LISTINGS_PRICE);
        searchItemsResources.add(SearchItemsResource.BROWSENODEINFO_BROWSENODES);
        searchItemsResources.add(SearchItemsResource.ITEMINFO_EXTERNALIDS)
        searchItemsResources.add(SearchItemsResource.ITEMINFO_BYLINEINFO)
        searchItemsResources.add(SearchItemsResource.IMAGES_PRIMARY_LARGE)
        boolean itemAdded = false

        String searchIndex = "All";

        SearchItemsResponse response
        String nodeId = browseNodeId


        SearchItemsRequest searchItemsRequest = new SearchItemsRequest().partnerTag(partnerTag).browseNodeId(nodeId)
                    .searchIndex(searchIndex).resources(searchItemsResources).partnerType(PartnerType.ASSOCIATES).sortBy(SortBy.NEWESTARRIVALS).itemCount(new Integer(10)).itemPage(new Integer(itemPage));



        try {
                response = api.searchItems(searchItemsRequest);
                 if (response.getSearchResult() != null&&response.getSearchResult().getItems()!=null) {
                     for (int i = 0; i < response.getSearchResult().getItems().size(); i++) {
                        Item item = response.getSearchResult().getItems().get(i);
                        if (item != null) {
                           itemAdded =  addBookToDB(item,browseNodeId,baseCategory)
                        }
                    }
                }
                if (response.getErrors() != null) {
                    println("Printing errors:\nPrinting Errors from list of Errors");
                    for (ErrorData error : response.getErrors()) {
                        println("Error code: " + error.getCode());
                        println("Error message: " + error.getMessage());
                    }
                }
            } catch (ApiException exception) {
// Exception handling
                println("Error calling PA-API 5.0!");
                println("Status code: " + exception.getCode());
                println("Errors: " + exception.getResponseBody());
                println("Message: " + exception.getMessage());
                if (exception.getResponseHeaders() != null) {
                    println("Request ID: " +
                            exception.getResponseHeaders().get("x-amzn-RequestId"));
                }
            } catch (Exception exception) {
                println("Exception message: " + exception.getMessage());
            }

        return itemAdded

    }

    def addCategories(String browseId,String categoryType){
        setAmazonClient()
        List<GetBrowseNodesResource> getBrowseNodesResources = new ArrayList<GetBrowseNodesResource>();
        getBrowseNodesResources.add(GetBrowseNodesResource.CHILDREN);
        List<String> browseNodeIds = new ArrayList<String>();
        browseNodeIds.add(browseId)
        GetBrowseNodesRequest getBrowseNodesRequest = new GetBrowseNodesRequest().browseNodeIds(browseNodeIds)
                .partnerTag(partnerTag).resources(getBrowseNodesResources).partnerType(PartnerType.ASSOCIATES);

        try {
            // Sending the request
            GetBrowseNodesResponse response = api.getBrowseNodes(getBrowseNodesRequest);
            if (response.getBrowseNodesResult() != null && response.getBrowseNodesResult().getBrowseNodes() != null) {
                for (BrowseNode browseNode : response.getBrowseNodesResult().getBrowseNodes()) {
                    List childNodes = browseNode.getChildren()
                    for (BrowseNodeChild browseNodeChild : childNodes) {
                        CategoryLevel1 categorylevel1 = CategoryLevel1.findByBrowseNodeId(browseNodeChild.getId())
                        CategoryLevel2 categorylevel2 = CategoryLevel2.findByBrowseNodeId(browseNodeChild.getId())
                        CategoryLevel3 categorylevel3 = CategoryLevel3.findByBrowseNodeId(browseNodeChild.getId())
                        CategoryLevel4 categorylevel4 = CategoryLevel4.findByBrowseNodeId(browseNodeChild.getId())

                        if(categorylevel1==null&&categorylevel2==null&&categorylevel3==null&&categorylevel4==null){
                          categorylevel1 = new CategoryLevel1(browseNodeId:browseNodeChild.getId(),nodeName: browseNodeChild.getDisplayName(),categoryType: categoryType )
                          categorylevel1.save(failOnError: true, flush: true)
                        }
                    }
                }
            }
            if (response.getErrors() != null) {
                System.out.println("Printing Errors:\nPrinting Errors from list of Errors");
                for (ErrorData error : response.getErrors()) {
                    System.out.println("Error code: " + error.getCode());
                    System.out.println("Error message: " + error.getMessage());
                }
            }
        } catch (Exception exception) {
            System.out.println("Exception message: " + exception.getMessage());
        }
    }

    def populateProductCategories(String categoryType,String rootBrowseId,String step){
        if("1".equals(step))  addCategories(rootBrowseId,categoryType)
        else if("2".equals(step)) addCategoriesLevel2(categoryType)
        else if("3".equals(step))addCategoriesLevel3(true,categoryType)
        else if("4".equals(step)) addCategoriesLevel4(true,categoryType)
        println("category created for "+categoryType+" and step "+step)
        return true
    }

    def addCategoriesLevel2(String categoryType){
        setAmazonClient()
        List<GetBrowseNodesResource> getBrowseNodesResources = new ArrayList<GetBrowseNodesResource>();
        getBrowseNodesResources.add(GetBrowseNodesResource.CHILDREN);
        List<String> browseNodeIds = new ArrayList<String>();
        List categories = CategoryLevel1.findAllByCategoryType(categoryType)
        for(CategoryLevel1 categorylevel1 : categories) {
            CategoryLevel2 tempCategorylevel2 = CategoryLevel2.findByParentIdAndCategoryType(categorylevel1.browseNodeId,categoryType)
            if(tempCategorylevel2!=null) continue
            browseNodeIds = new ArrayList<String>();
            browseNodeIds.add(categorylevel1.browseNodeId)
            GetBrowseNodesRequest getBrowseNodesRequest = new GetBrowseNodesRequest().browseNodeIds(browseNodeIds)
                    .partnerTag(partnerTag).resources(getBrowseNodesResources).partnerType(PartnerType.ASSOCIATES);

            try {
                // Sending the request
                GetBrowseNodesResponse response = api.getBrowseNodes(getBrowseNodesRequest);
                if (response.getBrowseNodesResult() != null && response.getBrowseNodesResult().getBrowseNodes() != null) {
                    for (BrowseNode browseNode : response.getBrowseNodesResult().getBrowseNodes()) {
                        List childNodes = browseNode.getChildren()
                        for (BrowseNodeChild browseNodeChild : childNodes) {
                            CategoryLevel2 categorylevel2 = CategoryLevel2.findByBrowseNodeId(browseNodeChild.getId())
                            CategoryLevel3 categorylevel3 = CategoryLevel3.findByBrowseNodeId(browseNodeChild.getId())
                            CategoryLevel4 categorylevel4 = CategoryLevel4.findByBrowseNodeId(browseNodeChild.getId())

                            if(categorylevel2==null&&categorylevel3==null&&categorylevel4==null){
                                categorylevel2 = new CategoryLevel2(browseNodeId: browseNodeChild.getId(), nodeName: browseNodeChild.getDisplayName(),parentId: categorylevel1.browseNodeId,
                                        categoryType:categoryType)
                                categorylevel2.save(failOnError: true, flush: true)
                            }
                        }
                    }
                }
                if (response.getErrors() != null) {
                    System.out.println("Printing Errors:\nPrinting Errors from list of Errors");
                    for (ErrorData error : response.getErrors()) {
                        System.out.println("Error code: " + error.getCode());
                        System.out.println("Error message: " + error.getMessage());
                    }
                    Thread.sleep(10000);
                }
            } catch (Exception exception) {
                System.out.println("Exception message: " + exception.getMessage());
            }
            Thread.sleep(2000);
        }
    }
    def addCategoriesLevel3(boolean skipAlreadyUpdated,String categoryType){
        setAmazonClient()
        List<GetBrowseNodesResource> getBrowseNodesResources = new ArrayList<GetBrowseNodesResource>();
        getBrowseNodesResources.add(GetBrowseNodesResource.CHILDREN);
        List<String> browseNodeIds = new ArrayList<String>();
        List categories = CategoryLevel2.findAllByLastChildIsNullAndCategoryType(categoryType)
        int count=0;
         for(CategoryLevel2 categorylevel2 : categories) {
             if(skipAlreadyUpdated){
                 CategoryLevel3 tempCategorylevel3 = CategoryLevel3.findByParentIdAndCategoryType(categorylevel2.browseNodeId,categoryType)
                 if(tempCategorylevel3!=null) continue
             }
            browseNodeIds = new ArrayList<String>();
            browseNodeIds.add(categorylevel2.browseNodeId)
            GetBrowseNodesRequest getBrowseNodesRequest = new GetBrowseNodesRequest().browseNodeIds(browseNodeIds)
                    .partnerTag(partnerTag).resources(getBrowseNodesResources).partnerType(PartnerType.ASSOCIATES);

            try {
                // Sending the request
                GetBrowseNodesResponse response = api.getBrowseNodes(getBrowseNodesRequest);
                if (response.getBrowseNodesResult() != null && response.getBrowseNodesResult().getBrowseNodes() != null) {
                    for (BrowseNode browseNode : response.getBrowseNodesResult().getBrowseNodes()) {
                        List childNodes = browseNode.getChildren()
                        if(childNodes!=null) {
                            for (BrowseNodeChild browseNodeChild : childNodes) {
                                CategoryLevel3 categorylevel3 = CategoryLevel3.findByBrowseNodeId(browseNodeChild.getId())
                                CategoryLevel4 categorylevel4 = CategoryLevel4.findByBrowseNodeId(browseNodeChild.getId())

                                if(categorylevel3==null&&categorylevel4==null) {
                                    categorylevel3 = new CategoryLevel3(browseNodeId: browseNodeChild.getId(), nodeName: browseNodeChild.getDisplayName(),
                                            parentId: categorylevel2.browseNodeId,categoryType: categoryType)
                                    categorylevel3.save(failOnError: true, flush: true)
                                 }
                            }
                        }else{
                            categorylevel2.lastChild="true"
                            categorylevel2.save(failOnError: true, flush: true)
                         }
                    }
                }

                if (response.getErrors() != null) {
                    System.out.println("Printing Errors:\nPrinting Errors from list of Errors");
                    for (ErrorData error : response.getErrors()) {
                        System.out.println("Error code: " + error.getCode());
                        System.out.println("Error message: " + error.getMessage());
                    }
                    if(count==10) break
                    count++
                    Thread.sleep(200000);
                }
            } catch (Exception exception) {
                System.out.println("Exception message: " + exception.getMessage());
            }
             Thread.sleep(10000);
         }
     }
    def addCategoriesLevel4(boolean skipAlreadyUpdated, String categoryType){
        setAmazonClient()
        List<GetBrowseNodesResource> getBrowseNodesResources = new ArrayList<GetBrowseNodesResource>();
        getBrowseNodesResources.add(GetBrowseNodesResource.CHILDREN);
        List<String> browseNodeIds = new ArrayList<String>();
        List categories = CategoryLevel3.findAllByLastChildIsNullAndCategoryType(categoryType)
        int count=0;
        int rightCount=0;
        for(CategoryLevel3 categorylevel3 : categories) {
            if(skipAlreadyUpdated){
                CategoryLevel4 tempCategorylevel4 = CategoryLevel4.findByParentIdAndCategoryType(categorylevel3.browseNodeId,categoryType)
                if(tempCategorylevel4!=null) continue
            }
            browseNodeIds = new ArrayList<String>();
            browseNodeIds.add(categorylevel3.browseNodeId)
            GetBrowseNodesRequest getBrowseNodesRequest = new GetBrowseNodesRequest().browseNodeIds(browseNodeIds)
                    .partnerTag(partnerTag).resources(getBrowseNodesResources).partnerType(PartnerType.ASSOCIATES);

            try {
                // Sending the request
                GetBrowseNodesResponse response = api.getBrowseNodes(getBrowseNodesRequest);
                if (response.getBrowseNodesResult() != null && response.getBrowseNodesResult().getBrowseNodes() != null) {
                    for (BrowseNode browseNode : response.getBrowseNodesResult().getBrowseNodes()) {
                        List childNodes = browseNode.getChildren()
                        if(childNodes!=null) {
                            for (BrowseNodeChild browseNodeChild : childNodes) {
                                CategoryLevel4 categorylevel4 = CategoryLevel4.findByBrowseNodeId(browseNodeChild.getId())
                                if (categorylevel4 == null) {
                                    categorylevel4 = new CategoryLevel4(browseNodeId: browseNodeChild.getId(), nodeName: browseNodeChild.getDisplayName(),
                                            parentId: categorylevel3.browseNodeId,categoryType:categoryType)
                                    categorylevel4.save(failOnError: true, flush: true)
                                 }
                            }
                        }else{
                            categorylevel3.lastChild="true"
                            categorylevel3.save(failOnError: true, flush: true)
                        }
                    }
                    rightCount++
                }

                if (response.getErrors() != null) {
                    System.out.println("Printing Errors:\nPrinting Errors from list of Errors");
                    for (ErrorData error : response.getErrors()) {
                        System.out.println("Error code: " + error.getCode());
                        System.out.println("Error message: " + error.getMessage());
                    }

                }
            } catch (Exception exception) {
                System.out.println("Exception message: " + exception.getMessage());
            }

            if(count==140) break
            count++
            Thread.sleep(60000);
        }
    }

    def updatePublisherDomain(ExtPublishers extPublishers){
        String publisherName = extPublishers.name
        def baseUrl = new URL('https://autocomplete.clearbit.com/v1/companies/suggest?query=' + URLEncoder.encode(publisherName, "UTF-8") )
        def connection
        def res
        try {
            connection = (HttpURLConnection) baseUrl.openConnection()
            connection.addRequestProperty("Content-type", "application/x-www-form-urlencoded")
            connection.with {
                doOutput = true
                requestMethod = 'GET'
                res = content.text
                def webList  = new JsonSlurper().parseText(res)
                if(webList.size()>0){
                    extPublishers.url = webList[0].domain
                    extPublishers.save(failOnError: true, flush: true)
                }


            }
        }
        catch(Exception e){
            println(e.toString())
        }
    }
    def getAmazonPriceInfo(searchKeywords, BooksAffiliationDtl booksAffiliationDtl){
        setAmazonClient()
        List<SearchItemsResource> searchItemsResources = new ArrayList<SearchItemsResource>();
        searchItemsResources.add(SearchItemsResource.ITEMINFO_TITLE);
        searchItemsResources.add(SearchItemsResource.OFFERS_LISTINGS_PRICE);
        searchItemsResources.add(SearchItemsResource.CUSTOMERREVIEWS_COUNT);
        searchItemsResources.add(SearchItemsResource.CUSTOMERREVIEWS_STARRATING)



        SearchItemsResponse response
        String keywords = searchKeywords //Ex. "CBSE class 12"
        SearchItemsRequest searchItemsRequest = new SearchItemsRequest().partnerTag(partnerTag).keywords(keywords)
                .resources(searchItemsResources).partnerType(PartnerType.ASSOCIATES);

        try {
            response = api.searchItems(searchItemsRequest);

            if (response.getSearchResult() != null) {
                for(int i=0;i<response.getSearchResult().getItems().size();i++) {

                    Item item = response.getSearchResult().getItems().get(i);
                    //    BrowseNodeInfo browseNodeInfo = item.getBrowseNodeInfo()

                    if (item != null) {
                        if (item.getOffers() != null && item.getOffers().getListings() != null
                                && item.getOffers().getListings().get(0).getPrice() != null
                                && item.getOffers().getListings().get(0).getPrice().getDisplayAmount() != null) {
                                booksAffiliationDtl.amazonPrice = new Double(""+item.getOffers().getListings().get(0).getPrice().getAmount())
                             booksAffiliationDtl.amazonLink = item.getDetailPageURL()
                            if(item.getCustomerReviews()!=null) {
                                booksAffiliationDtl.ratings = item.getCustomerReviews().getStarRating()
                                booksAffiliationDtl.reviews = item.getCustomerReviews().getCount()
                            }
                        }

                        ItemInfo itemInfo = item.getItemInfo()
                        if(itemInfo.getFeatures()!=null){
                            MultiValuedAttribute multiValuedAttribute = itemInfo.getFeatures()
                        }

                    }
                }
            }
            if (response.getErrors() != null) {
                println("Printing errors:\nPrinting Errors from list of Errors");
                for (ErrorData error : response.getErrors()) {
                    println("Error code: " + error.getCode());
                    println("Error message: " + error.getMessage());
                }
            }
        } catch (ApiException exception) {
// Exception handling
            println("Error calling PA-API 5.0!");
            println("Status code: " + exception.getCode());
            println("Errors: " + exception.getResponseBody());
            println("Message: " + exception.getMessage());
            if (exception.getResponseHeaders() != null) {
                println("Request ID: " +
                        exception.getResponseHeaders().get("x-amzn-RequestId"));
            }
        } catch (Exception exception) {
            println("Exception message: " + exception.getMessage());
        }

        return booksAffiliationDtl
    }

    String removeNonUTF8Chars(String input) {
        return input.replaceAll("[^\\x00-\\x7F]", "");
    }

}
