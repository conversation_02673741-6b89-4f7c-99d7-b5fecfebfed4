package com.wonderslate.shop

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.SiteMst
import grails.transaction.Transactional
import groovy.sql.Sql
import org.hibernate.criterion.Order

@Transactional
class BookPriceService {
    def redisService
    DataProviderService dataProviderService
    def grailsApplication

    def addBookPrice(params){
          //add bookPrice
         BookPriceDtl   bookPriceDtl = new BookPriceDtl(bookId: new Integer(params.bookId),bookType: params.bookType,currencyCd:params.currencyCd,
                    sellPrice: new Double(params.sellPrice),listPrice: params.listPrice!=null&&!"".equals(params.listPrice)&&!"null".equals(params.listPrice)?new Double(params.listPrice):null,
         freeChatTokens: params.freeChatTokens!=null&&!"".equals(params.freeChatTokens)&&!"null".equals(params.freeChatTokens)?new Integer(params.freeChatTokens):null)
        bookPriceDtl.save(failOnError: true, flush: true)
        BooksMst booksMst = dataProviderService.getBooksMst(bookPriceDtl.bookId)
        if("published".equals(booksMst.status)) dataProviderService.refreshCacheForPublishUnpublish(""+bookPriceDtl.bookId,booksMst.siteId)
    }

    def updateBookPrice(params){
        BookPriceDtl bookPriceDtl = BookPriceDtl.findById(new Integer(params.bookPriceId))
        if(bookPriceDtl!=null){
            //update bookPrice
            bookPriceDtl.sellPrice = new Double(params.sellPrice)
            if(params.listPrice!=null&&!"".equals(params.listPrice)) bookPriceDtl.listPrice = new Double(params.listPrice)
            if(params.freeChatTokens!=null&&!"".equals(params.freeChatTokens)) bookPriceDtl.freeChatTokens = new Integer(params.freeChatTokens)
        }
        bookPriceDtl.save(failOnError: true, flush: true)
        BooksMst booksMst = dataProviderService.getBooksMst(bookPriceDtl.bookId)
        dataProviderService.refreshCacheForPublishUnpublish(""+bookPriceDtl.bookId,booksMst.siteId)
    }

    def removeBookPrice(Integer bookPriceId){
        def bookId
        BookPriceDtl bookPriceDtl = BookPriceDtl.findById(bookPriceId)

        if(bookPriceDtl!=null) {
            bookId = bookPriceDtl.bookId
            bookPriceDtl.delete()
            BooksMst booksMst = dataProviderService.getBooksMst(bookId)
            dataProviderService.refreshCacheForPublishUnpublish(""+bookId,booksMst.siteId)
        }
        return bookId
    }

    def migrateBookPrices(){
        List sites = SiteMst.findAll()
        BookPriceDtl bookPriceDtl
        sites.each { site->
            println("The migration of "+site.siteName+" started")
            List books = BooksMst.findAllBySiteIdAndStatus(site.id,"published")

            books.each { book->
                println("updating for bookId="+book.id)
                if(book.price!=null) {
                    bookPriceDtl = new BookPriceDtl(bookId: book.id, bookType: "eBook", currencyCd: "INR",
                            sellPrice: book.price, listPrice: book.listprice != null ? book.listprice : null)
                    bookPriceDtl.save(failOnError: true, flush: true)
                }
                else{
                    println("book id "+book.id+" does not have price")
                }
                if(book.testsPrice!=null&&book.testsPrice.doubleValue()>0){
                    println("updating test series price for bookId="+book.id)
                    bookPriceDtl = new BookPriceDtl(bookId: book.id,bookType: "testSeries",currencyCd:"INR",
                            sellPrice: book.testsPrice,listPrice: book.testsListprice!=null?book.testsListprice:null)
                    bookPriceDtl.save(failOnError: true, flush: true)
                }
                if(book.upgradePrice!=null&&book.testsPrice.doubleValue()>0){
                    println("updating for upgrade price for bookId="+book.id)
                    bookPriceDtl = new BookPriceDtl(bookId: book.id,bookType: "upgrade",currencyCd:"INR",
                            sellPrice: book.upgradePrice)
                    bookPriceDtl.save(failOnError: true, flush: true)
                }

            }

        }
    }

    def getBooksPriceDtl(Integer bookId,String bookType){
        BookPriceDtl bookPriceDtl
        try {
            bookPriceDtl = redisService.memoizeDomainObject(BookPriceDtl, "bookPriceDtl_" + bookId+"_"+bookType) {
                return BookPriceDtl.findByBookIdAndBookType(bookId,bookType)
            }
        }catch (Exception e){
            println("Exception to get the book id="+bookId)
            bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(bookId,bookType)
        }

        return bookPriceDtl
    }

    def getBookPrices(Integer bookId){
        String sql = " select id,book_type bookType,list_price  listPrice,sell_price sellPrice,currency_cd currencyCd,free_chat_tokens freeChatTokens FROM wsshop.book_price_dtl  where " +
                "book_id=" +bookId+
                " ORDER BY FIELD(bookType, 'printbook', 'eBook','testSeries', 'combo','upgrade','bookGPT')"
          println("sql="+sql)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        results.each { result->
          println("result id="+result.id+" bookType="+result.bookType+" listPrice="+result.listPrice+" sellPrice="+result.sellPrice+" currencyCd="+result.currencyCd+" freeChatTokens="+result.freeChatTokens)
        }

        Gson gson = new Gson();
        String element = gson.toJson(results,new TypeToken<List>() {}.getType())
        redisService.("bookPriceDetails_"+bookId) = element
    }

}
