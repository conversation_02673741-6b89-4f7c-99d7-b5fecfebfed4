package com.wonderslate.shop

import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class PrintOrderManagementService {

    def grailsApplication

    def orderDetails(String cartMstId,String publisherId) {
        String sql =   "  select po.id,bm.title,bm.isbn,po.date_created,po.status,po.book_type,po.book_price,scom.deliver_costs from purchase_order po,books_mst bm, shopping_cart_orders_mst scom "+
                " WHERE po.cart_mst_id="+cartMstId +
                " AND bm.id=po.item_code and scom.id=po.cart_mst_id";
        if(publisherId!=null&&!"null".equals(publisherId)&&!"".equals(publisherId)) sql+=" and bm.publisher_id="+publisherId
        sql+= " order by po.id asc";
       println(sql)

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        return results
    }

    def getOpenOrdersList(String cartMstId,String publisherId) {
        String sql =   "select po.id,po.item_code itemCode from purchase_order po,books_mst bm "+
                " WHERE po.cart_mst_id="+cartMstId +
                " and po.book_type in ('printbook','combo') AND bm.id=po.item_code and po.status='Active' ";
        if(publisherId!=null&&!"null".equals(publisherId)&&!"".equals(publisherId)) sql+=" and bm.publisher_id="+publisherId
        sql+= " order by po.id asc";


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        return results
    }

    def getDispatchedOrdersList(String cartMstId,String publisherId) {
        String sql =   "select po.id,po.item_code itemCode from purchase_order po,books_mst bm "+
                " WHERE po.cart_mst_id="+cartMstId +
                " and po.book_type in ('printbook','combo') AND bm.id=po.item_code and po.status='Dispatched' ";
        if(publisherId!=null&&!"null".equals(publisherId)&&!"".equals(publisherId)) sql+=" and bm.publisher_id="+publisherId
        sql+= " order by po.id asc";


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        return results
    }

    def dispatchDetails(String cartMstId,String publisherId) {
        String sql =   "  select po.id,bm.title,bm.isbn,po.date_created,po.status,po.book_type," +
                "dd.delivery_type,partner_details,tracking_code,tracking_link from purchase_order po,books_mst bm,dispatch_details dd"+
                " WHERE po.cart_mst_id="+cartMstId +
                " AND bm.id=po.item_code "+
                " and dd.po_no=po.id "
        if(publisherId!=null&&!"null".equals(publisherId)&&!"".equals(publisherId)) sql+=" and bm.publisher_id="+publisherId
        sql+= " order by po.id asc";


        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        return results
    }

    def deliveredDetails(String cartMstId,String publisherId) {
        String sql =   "  select po.id,bm.title,bm.isbn,po.date_created,po.status,po.book_type," +
                "dd.received_by from purchase_order po,books_mst bm,delivered_details dd"+
                " WHERE po.cart_mst_id="+cartMstId +
                " AND bm.id=po.item_code "+
                " and dd.po_no=po.id "
        if(publisherId!=null&&!"null".equals(publisherId)&&!"".equals(publisherId)) sql+=" and bm.publisher_id="+publisherId
        sql+= " order by po.id asc";

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        return results
    }
}
