package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SiteMst
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional

@Transactional
class LinksService {
    DataProviderService dataProviderService
    def redisService

    def getAffliationDeepLink(String affiliationCd){
        AffiliationMst affiliationMst = AffiliationMst.findByAffiliationCd(affiliationCd)
        if(affiliationMst.affiliationDeepLink==null){

        SiteMst siteMst = dataProviderService.getSiteMst(affiliationMst.siteId)

        def firebaseKey="${siteMst!=null?siteMst.fbFirebaseWebAPI:""}";
        def parameters="&utm_campaign=affiliation&utm_content="+affiliationCd;


        def params = "{" +
                "\"dynamicLinkInfo\": {" +
                " \"domainUriPrefix\":\""+siteMst.domainUriPrefix+"\"," +
                " \"androidInfo\": {" +
                "\"androidPackageName\":\""+siteMst.androidPackageName+"\"" +
                " }," +
                " \"iosInfo\":{\"iosBundleId\": \""+siteMst.iosBundleId+"\"" +
                "}," +
                "\"link\":\""+(siteMst.siteBaseUrl+"/links/affilMaster?"+parameters+"\"") +
                "}," +
                "\"suffix\":{" +
                "\"option\":\"SHORT\"" +
                "}" +
                "}"

        RestBuilder rest = new RestBuilder()


        def resp = rest.post('https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key='+firebaseKey) {
            accept("application/json")
            contentType("application/json")
            body(params)
        }
        def json = resp.json

            affiliationMst.affiliationDeepLink = json.shortLink
            affiliationMst.save(flush: true, failOnError: true)

        }
            return affiliationMst.affiliationDeepLink
    }

    def getAffiliationMst(String  affiliationCd){
        AffiliationMst affiliationMst  = redisService.memoizeDomainObject(AffiliationMst, "affiliationCd_"+affiliationCd) {
            return AffiliationMst.findByAffiliationCd(affiliationCd)
        }

        return affiliationMst
    }

    def getAccessCodeDeepLink(String siteId){


            SiteMst siteMst = dataProviderService.getSiteMst(new Integer(siteId))

            def firebaseKey="${siteMst!=null?siteMst.fbFirebaseWebAPI:""}";
            def parameters="&utm_campaign=accesscode&utm_content=accesscode&siteId="+siteId;


            def params = "{" +
                    "\"dynamicLinkInfo\": {" +
                    " \"domainUriPrefix\":\""+siteMst.domainUriPrefix+"\"," +
                    " \"androidInfo\": {" +
                    "\"androidPackageName\":\""+siteMst.androidPackageName+"\"" +
                    " }," +
                    " \"iosInfo\":{\"iosBundleId\": \""+siteMst.iosBundleId+"\"" +
                    "}," +
                    "\"link\":\""+(siteMst.siteBaseUrl+"/links/accessCode?"+parameters+"\"") +
                    "}," +
                    "\"suffix\":{" +
                    "\"option\":\"SHORT\"" +
                    "}" +
                    "}"

            RestBuilder rest = new RestBuilder()


            def resp = rest.post('https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key='+firebaseKey) {
                accept("application/json")
                contentType("application/json")
                body(params)
            }
            def json = resp.json

        return json.shortLink
    }
}
