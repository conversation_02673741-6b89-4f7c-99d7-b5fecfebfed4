package com.wonderslate.shop

import com.razorpay.Payment

//import com.ccavenue.security.AesCryptUtil

import com.razorpay.RazorpayClient
import com.razorpay.RazorpayException
import com.razorpay.Refund
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.*
import com.wonderslate.log.FixedPayments
import com.wonderslate.logs.LogsService
import com.wonderslate.logs.RazorPaymentService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.Publishers
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.async.DelegateAsync
import grails.async.Promise
import grails.async.Promises
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import groovy.sql.Sql
import org.grails.web.json.JSONObject
import org.springframework.scheduling.annotation.Async

import java.sql.Timestamp
import java.text.DateFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat


@Transactional
class PurchaseService {
    def grailsApplication
    def springSecurityService
    DataProviderService dataProviderService
    UtilService utilService
    def redisService
    UserManagementService userManagementService
    WsshopService wsshopService
    BookPriceService bookPriceService
    @DelegateAsync PurchaseSubService purchaseSubService
    DeliveryChargesService deliveryChargesService
    RazorPaymentService razorPaymentService
    MailService mailService

    double calculateTotalCost(params){
        double calculatedCost=0
        String bookId
        BooksMst booksMst
        String bookType = params.bookType
        String shoppingCartId=params.shoppingCartId
        DecimalFormat df = new DecimalFormat("##.##")

            ShoppingCartOrdersMst shoppingCartOrdersMst = ShoppingCartOrdersMst.findById(new Integer(params.shoppingCartId))
            if(shoppingCartOrdersMst!=null) {
                calculatedCost=shoppingCartOrdersMst.totalPrice.doubleValue()
            }


        return calculatedCost
    }

    def getPayment(siteId,calculatedCost,params){
        HashMap paymentDetails = new HashMap()
        paymentDetails.put("exceptionHappened","false")

        // Initialize client
        RazorpayClient razorpayClient = getRazorpayClient(siteId)

        JSONObject options = new JSONObject()
        double bookPrice = calculatedCost * 100
        String pBookprice = String.format("%.2f", bookPrice)
        options.put("amount", Double.parseDouble(pBookprice))
        try {
            razorpayClient.Payments.capture(params.razorpay_payment_id, (org.json.JSONObject) options)
        }catch(Exception e){
            paymentDetails.put("exceptionHappened","true")
            PurchaseOrder purchaseOrder = PurchaseOrder.findByPaymentId(params.razorpay_payment_id)
            if(purchaseOrder!=null)  paymentDetails.put("poNo",purchaseOrder.id)

        }
        paymentDetails.put("payment",razorpayClient.Payments.fetch(params.razorpay_payment_id))
        return paymentDetails

    }

    def purchase(params,siteId,request) {
        double calculatedCost=calculateTotalCost(params)
        HashMap paymentDetails = getPayment(siteId,calculatedCost,params)
        Payment payment=paymentDetails.get("payment")
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if("false".equals(paymentDetails.get("exceptionHappened"))) {
            //add a parameter to request
           def poNo = completeThePOTransaction(payment, params.payId, user, params.bookId, siteId, request)
            paymentDetails.put("poNo", poNo)
        }
        return paymentDetails.get("poNo")
    }

    RazorpayClient getRazorpayClient(siteId){
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        return new RazorpayClient(siteMst.razorPayKeyId,siteMst.razorPaySecretKey)
    }

    def refund(razorPaymentId,siteId){
        String status = "Record not found"
        try {
            RazorpayClient razorpayClient = getRazorpayClient(siteId)
            Payment payment = razorpayClient.Payments.fetch(razorPaymentId)
            SiteMst siteMst = dataProviderService.getSiteMst(siteId)
            //if payment is in authorized state , first capture it
            if(payment!=null) {
                if(payment.get("status").equals("refunded")){
                    status = "Already Refunded"
                }else {
                    JSONObject options = new JSONObject()
                    options.put("amount", payment.get("amount"))
                    if (payment != null && payment.get("status").equals("authorized")) {
                        razorpayClient.Payments.capture(razorPaymentId, (org.json.JSONObject) options)
                    }
                    Refund refund = razorpayClient.Payments.refund(razorPaymentId, (org.json.JSONObject) options);

                    if(refund!=null) {
                        status = "Refund successfull. Reference "+refund.get("id")
                        User user = dataProviderService.getUserMst(payment.get("notes").username)

                        //delete the po and books permission
                        List purchaseOrder = PurchaseOrder.findAllByPaymentId(razorPaymentId)
                        if (purchaseOrder != null) {
                            for (int k = 0; k < purchaseOrder.size(); k++) {
                                try{
                                    PurchaseOrderRefund pof = new PurchaseOrderRefund()
                                    pof.dateCreated = new Date()
                                    pof.purchasedDate = purchaseOrder[k].dateCreated
                                    pof.itemCode = purchaseOrder[k].itemCode
                                    pof.bookPrice = purchaseOrder[k].bookPrice
                                    pof.amount = purchaseOrder[k].amount
                                    pof.discountId=purchaseOrder[k].discountId
                                    pof.discountAmount = purchaseOrder[k].discountAmount
                                    pof.currency = purchaseOrder[k].currency
                                    pof.status = purchaseOrder[k].status
                                    pof.username = purchaseOrder[k].username
                                    pof.siteId = purchaseOrder[k].siteId
                                    pof.paymentId = purchaseOrder[k].paymentId
                                    pof.cartMstId=purchaseOrder[k].cartMstId
                                    pof.poFor=purchaseOrder[k].poFor
                                    pof.affiliationCd=purchaseOrder[k].affiliationCd
                                    pof.instituteId=purchaseOrder[k].instituteId
                                    pof.poMethod=purchaseOrder[k].poMethod
                                    pof.gstPercentage=purchaseOrder[k].gstPercentage
                                    pof.createdBy=springSecurityService.currentUser.username
                                    pof.save(failOnError: true, flush: true)
                                    List booksPermissions = BooksPermission.findAllByPoNo(purchaseOrder[k].id)
                                    booksPermissions.each { booksPermission ->
                                        booksPermission.delete()
                                    }

                                    if(purchaseOrder[k].subscriptionId!=null){
                                        SubscriptionDtl subscriptionDtl = SubscriptionDtl.findByPoNo(purchaseOrder[k].id)
                                        if(subscriptionDtl!=null){
                                            subscriptionDtl.delete()
                                        }
                                    }
                                    userManagementService.numberOfBooksInLibrary(pof.username)
                                }catch(Exception e){
                                    println("Refund failed for "+e.printStackTrace())
                                }
                            }
                            PurchaseOrder.wsshop.executeUpdate("delete PurchaseOrder where payment_id='" + razorPaymentId+"'")
                            if(siteMst.allBooksLibrary=="true") dataProviderService.getBooksListForUser(user.username)
                            else redisService.("userShelfBooks_"+user.username)=null
                        }
                        //send sms and email
                        if(siteId==1 && user.mobile!=null){
                            try {
                                def message = "Refund has been initiated for Rs ${payment.get("amount") / 100}. Your should receive the refund in your account in 3 working days."
                                utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                            } catch (Exception e) {
                                println("exception in sending user  refund sms " + e.toString())
                            }
                        }
                    }else{
                        status = "Refund failed"
                    }
                }
            }
        }catch(RazorpayException re){
            println("the razor pay exception is "+re.toString());
        }
        def json = ["STATUS":status]
        return json
    }

    def addSubscriptionPurchaseOrder(payment,user,siteId,payablePrice,subscriptionId,subsDuration,subsBookId,request){
        SubscriptionMst subscriptionMst =  wsshopService.getSubscriptionMst(subscriptionId)
        String poType
        Integer poNo=null
        PurchaseOrder po = new PurchaseOrder()
        po.dateCreated = new Date()
        po.itemCode = new Integer(subsBookId)
        po.bookPrice = payablePrice
        po.amount = payablePrice
        po.currency = payment!=null?payment.get("currency"):""
        po.status = 'Active'
        po.username = user.username
        po.siteId = siteId
        po.paymentId = payment!=null?payment.id:""
        po.poFor="subscription"
        po.subscriptionId = new Integer(subscriptionId)
        po.paymentFrom=request.getParameter("paymentFrom")!=null?request.getParameter("paymentFrom"):"web"

        if(payment.get("notes")!=null&&payment.get("notes").has("shoppingCartId")) po.cartMstId = new Long(payment.get("notes").shoppingCartId)
        if(payment.get("notes")!=null&&payment.get("notes").has("affiliationCd")) po.affiliationCd=""+payment.get("notes").affiliationCd
        if(payment.get("notes")!=null&&payment.get("notes").has("instituteId")) {
            po.instituteId=new Integer(""+payment.get("notes").instituteId)
        }

        DateFormat df
        df = new SimpleDateFormat("dd.MM.yyyy / hh.mm aa")
        po.poMethod=payment!=null?payment.get("method"):null
        po.gstPercentage=5
        //   if(booksMst.isbn!=null && booksMst.isbn!="") po.gstPercentage=5
        // else po.gstPercentage=18
        //now let us add the subscription details

        po.save(failOnError: true, flush: true)
        poNo = (Integer) po.id

        addSubsDetails(subsBookId,subsDuration,subscriptionId,po,user)
        if(user.email==null || "null".equals(user.email) || "".equals(user.email)){
            user.email=payment!=null&&!"<EMAIL>".equals(payment.get("email"))?payment.get("email"):null
            user.save(failOnError: true, flush: true)
        }else if(user.mobile==null){
            String userMobile=payment.get("contact")
            if(userMobile.contains('+91')) {
                user.mobile = payment != null ? userMobile.split('\\+91')[1] : null
                user.save(failOnError: true, flush: true)
            }
        }
        return po.id
    }

    def addSubsDetails(subsBookId,subsDuration,subscriptionId,po,user){
        BooksMst subsStartBooksMst = dataProviderService.getBooksMst(new Integer(subsBookId))

        Date datePublished = subsStartBooksMst.datePublished
        Calendar calendar = Calendar.getInstance()
        calendar.setTime(datePublished)
        calendar.set(Calendar.DAY_OF_MONTH,1)
        Date subsStartDate = calendar.getTime()
        //mathe add duration
        calendar.add(Calendar.YEAR,Integer.parseInt(subsDuration))
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        Date subsEndDate = calendar.getTime()
        //calculate the end date for book subscription. Let's give it two years from the last edition. We can change it later
        calendar.add(Calendar.YEAR,2)
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

        Date expiryDate = calendar.getTime()
        SubscriptionDtl subscriptionDtl = SubscriptionDtl.findByPoNoAndUsernameAndSubscriptionIdAndSiteId(po.id,user.username,subscriptionId,po.siteId)
        if(subscriptionDtl==null) {
            subscriptionDtl = new SubscriptionDtl(username: user.username, subscriptionId: new Integer(subscriptionId), siteId: po.siteId, startDate: subsStartDate, endDate: subsEndDate, poNo: po.id, bookExpiryDate: expiryDate)
            subscriptionDtl.save(failOnError: true, flush: true)
        }

        //now add the books the user library
        List subsBooks = BooksMst.findAllBySubscriptionIdAndDatePublishedBetween(subscriptionId,subsStartDate,subsEndDate)
        subsBooks.each{book->
            BooksPermission booksPermission = BooksPermission.findByUsernameAndBookIdAndSubscriptionId(user.username, book.id,new Integer(subscriptionId))
            if(booksPermission==null) {
                //if the book has been deleted from user's library
                booksPermission = new BooksPermission()
                booksPermission.bookId = book.id
                booksPermission.username = user.username
                booksPermission.poNo = po.id
                booksPermission.poType = "PURCHASE"
                booksPermission.subscriptionId = new Integer(subscriptionId)


                booksPermission.expiryDate = expiryDate
                booksPermission.save(failOnError: true, flush: true)
            }
        }
        userManagementService.numberOfBooksInLibrary(user.username)
    }

    def addToPurchaseOrder(payment,payId,user,bookId,siteId,request,booksMst,publisher,payablePrice,discountIdcs,discountAmountcs,bookType){
        String poType
        Integer poNo=null
        Integer discountId=null
        double discountAmount=0
        PurchaseOrder po = new PurchaseOrder()
        po.dateCreated = new Date()
        po.itemCode = booksMst.id
        if(bookType==null||"".equals(bookType)) bookType="eBook"
        BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(""+bookId),bookType)

        po.bookPrice = bookPriceDtl!=null?bookPriceDtl.sellPrice:new Double(0.0)
          po.amount = payablePrice!=null?payablePrice:0.0
            po.discountId= discountIdcs?new Integer(discountIdcs):null
            po.discountAmount = discountAmountcs!=0?discountAmountcs:null

        po.currency = payment!=null?payment.get("currency"):""
        if(siteId==21 || siteId==1 || siteId==3 || siteId==34 || siteId==37)
        {
            if(payment.get("notes")!=null&&payment.get("notes").has("discountId"))
            {
                discountId= new Integer(payment.get("notes").discountId)
                po.discountId= new Integer(payment.get("notes").discountId)
            }
        }
        po.status = 'Active'
        po.username = user.username
        po.siteId = siteId
        po.paymentId = payment!=null?payment.id:""
        if(payment.get("notes")!=null&&payment.get("notes").has("shoppingCartId")) po.cartMstId = new Long(payment.get("notes").shoppingCartId)
        if(payment.get("notes")!=null&&payment.get("notes").has("priceId")) po.poFor="extension"
        else po.poFor="book"
        if(request.getAttribute("scd")!=null) po.affiliationCd=""+request.getAttribute("scd")
        if(payment.get("notes")!=null&&payment.get("notes").has("instituteId")) {
            po.instituteId=new Integer(""+payment.get("notes").instituteId)

        }

        DateFormat df
        df = new SimpleDateFormat("dd.MM.yyyy / hh.mm aa")
        po.poMethod=payment!=null?payment.get("method"):null
        po.gstPercentage=5
        if("printbook".equals(bookType)) po.gstPercentage=0
        else if (siteId.intValue() == 66||"recharge".equals(bookType)) {
            po.gstPercentage=18
        }
        po.bookType = bookType


        if(discountId!=null){
            DiscountPriceDtl discountPriceDtl =DiscountPriceDtl.findByDiscountId(new Integer(discountId));

            if(discountPriceDtl !=null){
                if(discountPriceDtl.discountValue!=null){
                    discountAmount=discountPriceDtl.discountValue
                }else if(discountPriceDtl.discountPercentage!=null){
                    //calcalute the amount by percentage
                    Integer perc= discountPriceDtl.discountPercentage;

                    discountAmount=((bookPriceDtl.sellPrice * perc) / 100)
                    String pAchualPrice = String.format("%.2f",discountAmount)
                    discountAmount = Double.parseDouble(pAchualPrice)
                }
            }
            po.discountAmount=discountAmount == 0 ? null:discountAmount
        }
        po.paymentFrom=request.getParameter("paymentFrom")!=null?request.getParameter("paymentFrom"):"web"
        po.save(failOnError: true, flush: true)
        poNo = (Integer) po.id
        if(user.email==null || "null".equals(user.email) || "".equals(user.email)){
            user.email=payment!=null&&!"<EMAIL>".equals(payment.get("email"))?payment.get("email"):null
            user.save(failOnError: true, flush: true)
        }else if(user.mobile==null){
            String userMobile=payment.get("contact")
            if(userMobile.contains('+91')) {
                user.mobile = payment != null ? userMobile.split('\\+91')[1] : null
                user.save(failOnError: true, flush: true)
            }
        }
        return po.id
    }

    def completeThePOTransaction(Payment payment,payId,User user,bookId,siteId,request){
        PurchaseOrder po = new PurchaseOrder()
        String poType
        String entryController
        Integer poNo=null
        Integer discountId=null
        double discountAmount=0
        double payablePrice=0
        boolean freebook = false;
        BooksMst booksMst
        Publishers publisher
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        SiteDtl siteDtl = dataProviderService.getSiteDtl(siteId)
        if("true".equals(siteMst.commonWhiteLabel)) {
            entryController = "privatelabel"
        } else {
            entryController = siteMst.siteName
        }
        def str1 = ""
        double totalAmount=0
        List purchaseOrder
        DateFormat df
        String cartBooks =""
        String bookType
        df = new SimpleDateFormat("dd.MM.yyyy / hh.mm aa")
        if("freebook".equals(payId)){
            booksMst = dataProviderService.getBooksMst(new Long(bookId))
            publisher = dataProviderService.getPublisher(booksMst.publisherId)
            poType="ADDEDFORFREE"
            freebook = true
            BooksPermission booksPermission = new BooksPermission()
            booksPermission.bookId = booksMst.id
            booksPermission.username = user.username
            booksPermission.poNo = poNo
            booksPermission.poType = poType

            if (booksMst != null && booksMst.bookExpiry != null && booksMst.bookExpiry != ""){
                booksPermission.expiryDate = booksMst.bookExpiry
            }
            else if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                Calendar c = Calendar.getInstance()
                c.add(Calendar.DATE, booksMst.validityDays)
                booksPermission.expiryDate = c.getTime()
            }

            booksPermission.save(failOnError: true, flush: true)
            userManagementService.numberOfBooksInLibrary(user.username)
            if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
        } else {

                boolean hasPrintBooks = false;
                String cartMstId = payment.get("notes").shoppingCartId
                List shoppingCartOrdersDtl = ShoppingCartOrdersDtl.findAllByCartMstId(new Long(cartMstId))
                for (int k = 0; k < shoppingCartOrdersDtl.size(); k++) {
                    try{
                        payablePrice = shoppingCartOrdersDtl[k].payablePrice
                        discountAmount = shoppingCartOrdersDtl[k].discountAmount!=null?shoppingCartOrdersDtl[k].discountAmount:0
                        discountId = shoppingCartOrdersDtl[k].discountId
                        booksMst = dataProviderService.getBooksMst(new Long(shoppingCartOrdersDtl[k].bookId))
                        publisher = dataProviderService.getPublisher(booksMst.publisherId)
                        bookType = shoppingCartOrdersDtl[k].bookType
                        if(bookType==null||"".equals(bookType)) bookType="eBook"
                        if("printbook".equals(bookType)||"combo".equals(bookType)||"comboGPT".equals(bookType)) hasPrintBooks = true
                        if("subscription".equals(bookType)) {
                            poNo = addSubscriptionPurchaseOrder(payment, user, siteId, payablePrice, shoppingCartOrdersDtl[k].subscriptionId, shoppingCartOrdersDtl[k].subsDuration, shoppingCartOrdersDtl[k].subsStartingBookId,request)
                        }else {
                            poNo = addToPurchaseOrder(payment, payId, user, booksMst.id, siteId, request, booksMst, publisher, payablePrice, discountId, discountAmount, bookType)
                            if(!"printbook".equals(bookType)&&!"recharge".equals(bookType)) {
                               addBookToUser(user, poNo, poType, booksMst,bookType)
                            }else if("recharge".equals(bookType)) {
                                BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(""+booksMst.id),bookType)
                                updateUserChatTokens(user, bookPriceDtl)
                            }
                            updateLastSold(booksMst.id)
                        }
                    }catch(Exception e){
                        println("puchase failed for "+e.printStackTrace())
                    }
                }

                def sqlcart = "SELECT po.amount,po.gst_percentage,po.discount_amount,po.book_price,bm.title,po.date_created,po_method,po.payment_id FROM purchase_order po,books_mst bm where po.item_code=bm.id and po.cart_mst_id=" + cartMstId

                def dataSourcecart = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1cart = new Sql(dataSourcecart)
                def resultscart = sql1cart.rows(sqlcart)
                List purchaseOrdersList = resultscart.collect { cart ->
                    totalAmount += cart.amount
                    return [amount: cart.amount, gstPercentage: cart.gst_percentage, discountAmount: cart.discount_amount, bookPrice: cart.book_price, title: cart.title,
                            dateCreated:cart.date_created,poMethod:cart.po_method,paymentId:cart.payment_id]
                }
                sendEmailAndCompleteCleanup(poNo,hasPrintBooks,user,siteId,payment,siteDtl,siteMst,entryController,cartMstId,purchaseOrdersList,request)
                if(hasPrintBooks) addPurchaseBillShipAddress(user.username, siteId,cartMstId)

        }


        if(siteMst.allBooksLibrary=="true") dataProviderService.getBooksListForUser(user.username)
        else redisService.("userShelfBooks_"+user.username)=null

        dataProviderService.usersCartBooksDetailsByUsername(user.username, siteId)
        return poNo
    }
    void updateUserChatTokens(User user, BookPriceDtl bookPriceDtl) {
        if(user.chatTokensBalance == null) user.chatTokensBalance = 0
        user.chatTokensBalance = new Integer(user.chatTokensBalance.intValue() + bookPriceDtl.freeChatTokens.intValue())
        user.save(failOnError: true, flush: true)
    }

    def addBookToUser(user,poNo,poType,booksMst,bookType){
        BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(booksMst.id,user.username)

        if(booksPermission==null) {
            booksPermission = new BooksPermission()
            booksPermission.bookId = booksMst.id
            booksPermission.username = user.username
            booksPermission.poNo = poNo
            booksPermission.poType = poType
        }

        if(booksMst.packageBookIds!=null){
            BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(""+booksMst.id),bookType)
            if(bookPriceDtl.freeChatTokens!=null){
                if(user.chatTokensBalance==null) user.chatTokensBalance=bookPriceDtl.freeChatTokens.intValue()
               else  user.chatTokensBalance = new Integer(user.chatTokensBalance.intValue() + bookPriceDtl.freeChatTokens.intValue())
                user.save(failOnError: true, flush: true)
            }
        }

        if("upgrade".equals(bookType)||"testSeries".equals(bookType)){
            booksPermission.testsPurchased="true"
            booksPermission.testsPoNo=poNo
        }
        if("bookGPT".equals(bookType)||"comboGPT".equals(bookType)||"ebookGPTUpgrade".equals(bookType)||"ibookgptpro".equals(bookType) ){
            BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(""+booksMst.id),bookType)
            if(bookPriceDtl.freeChatTokens!=null)
            booksPermission.chatTokensBalance=bookPriceDtl.freeChatTokens
            else {
                if("ibookgptpro".equals(bookType))
                booksPermission.chatTokensBalance=99999
                else booksPermission.chatTokensBalance=250
            }
        }else{
            //add this by default to all types of ebooks
            booksPermission.chatTokensBalance=10
        }


        if (booksMst != null && booksMst.bookExpiry != null && booksMst.bookExpiry != ""){
            booksPermission.expiryDate = booksMst.bookExpiry
        }else if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
            Calendar c = Calendar.getInstance()
            c.add(Calendar.DATE, booksMst.validityDays)
            booksPermission.expiryDate = c.getTime()
        }
        booksPermission.bookType=bookType
        booksPermission.save(failOnError: true, flush: true)
        userManagementService.numberOfBooksInLibrary(user.username)

        if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
    }

    def selfService(params,siteId,request){
        try {
            String razorPayId = params.razorPayId
            String status = "The razor payment id provided is not valid."
            Payment payment = null
            BooksMst booksMst = null
            User user = null
            Integer priceId = null
            Integer discountId = null
            Integer shoppingCartId = null
            double calculatedCost
            String cartBooks =""
            Double tamount=null
            if(params.po!=null){
                PurchaseOrder purchaseOrder = PurchaseOrder.findBySequencePo(new Integer(params.po))
                if(purchaseOrder!=null) razorPayId = purchaseOrder.paymentId
            }

            if (razorPayId != null) {
                RazorpayClient razorpayClient = getRazorpayClient(siteId)
                payment = razorpayClient.Payments.fetch(razorPayId)

                SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                if(payment.get("notes").has("shoppingCartId")) shoppingCartId = new Integer(payment.get("notes").shoppingCartId)

                    user = dataProviderService.getUserMst(payment.get("notes").username)

                    if(payment.get("notes").has("discountId")) discountId = new Integer(payment.get("notes").discountId)
                    if(payment.get("notes").has("priceId")) priceId = new Integer(payment.get("notes").priceId)


                        List booksList=ShoppingCartOrdersDtl.findAllByCartMstId(new Long(shoppingCartId))
                        user = dataProviderService.getUserMst(booksList[0].username)
                        booksList.each { cartBooksDtl ->
                            cartBooks += BooksMst.findById(cartBooksDtl.bookId).title + ","
                        }

                siteId = user.siteId
                if (payment != null && payment.get("status").equals("failed")) {
                    status = "The payment is failed. Please try again to purchase the course."
                } else if (payment != null && payment.get("status").equals("refunded")) {
                    status = "The refund is already initiated for this purchase. The amount will reach  your account in 3 working days."
                } else if (payment != null && payment.get("status").equals("authorized")) {
                    JSONObject options = new JSONObject()

                        ShoppingCartOrdersMst shoppingCartOrdersMst = ShoppingCartOrdersMst.findById(new Integer(shoppingCartId))
                        if(shoppingCartOrdersMst!=null)  {
                            calculatedCost=shoppingCartOrdersMst.totalPrice.doubleValue()
                        }

                    if(priceId!=null){
                        ValidityExtensionMst validityExtensionMst = ValidityExtensionMst.findById(priceId)
                        if(validityExtensionMst!=null) calculatedCost=validityExtensionMst.price.doubleValue()
                    }
                    if(discountId!=null){
                        DiscountPriceDtl discountPriceDtl =DiscountPriceDtl.findByDiscountId(new Integer(discountId));
                        if(discountPriceDtl !=null){
                            if(discountPriceDtl.discountValue!=null){
                                calculatedCost=booksMst.price-discountPriceDtl.discountValue
                            }else if(discountPriceDtl.discountPercentage!=null){
                                //calcalute the amount by percentage
                                Integer perc= discountPriceDtl.discountPercentage;
                                tamount=Math.round((booksMst.price * perc) / 100)
                                calculatedCost=booksMst.price-tamount
                            }
                        }
                    }

                    double bookPrice = calculatedCost * 100
                    String pBookprice = String.format("%.2f", bookPrice)
                    options.put("amount", Double.parseDouble(pBookprice))
                    razorpayClient.Payments.capture(razorPayId, (org.json.JSONObject) options)
                    payment = razorpayClient.Payments.fetch(razorPayId)
                    if (payment != null && payment.get("status").equals("captured")) {
                        if(shoppingCartId!=null) {
                            completeThePOTransaction(payment, razorPayId, user, null, siteId, request)
                            status = "There was a delay in payment process. The purchase is successful and the course added to your library. Kindly check your library now."
                        }
                    } else {
                        status = "Issue with the transaction. Kindly contact the support team."
                    }
                } else if (payment != null && payment.get("status").equals("captured")) {
                    PurchaseOrder purchaseOrder = PurchaseOrder.findByPaymentId(razorPayId)
                    if (purchaseOrder == null) {
                           completeThePOTransaction(payment, razorPayId, user, null, siteId, request)

                    }  else {
                            if(purchaseOrder.subscriptionId!=null){
                                String subscriptionId = payment.get("notes").subscriptionId
                                SubscriptionMst subscriptionMst =  wsshopService.getSubscriptionMst(subscriptionId)
                                String subsDuration = payment.get("notes").subscriptionDuration
                                String subsBookId = payment.get("notes").subsStartingBookId
                                addSubsDetails(subsBookId,subsDuration,subscriptionId,purchaseOrder,user)
                            }
                            else {
                                List purchaseOrderslist = PurchaseOrder.findAllByPaymentId(razorPayId)

                                for (int k = 0; k < purchaseOrderslist.size(); k++) {
                                    if ("eBook".equals(purchaseOrderslist[k].bookType) || "testSeries".equals(purchaseOrderslist[k].bookType) || "combo".equals(purchaseOrderslist[k].bookType)) {
                                        BooksPermission booksPermission = BooksPermission.findByPoNo(purchaseOrderslist[k].id)
                                        booksMst = dataProviderService.getBooksMst(new Long(purchaseOrderslist[k].itemCode))
                                        if (booksPermission == null) {
                                            booksPermission = new BooksPermission()
                                            booksPermission.bookId = booksMst.id
                                            booksPermission.username = user.username
                                            booksPermission.poNo = purchaseOrderslist[k].id
                                            booksPermission.poType = "PURCHASE"
                                            if (booksMst != null && booksMst.bookExpiry != null && booksMst.bookExpiry != ""){
                                                booksPermission.expiryDate = booksMst.bookExpiry
                                            }else if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                                                Calendar c = Calendar.getInstance()
                                                c.add(Calendar.DATE, booksMst.validityDays)
                                                booksPermission.expiryDate = c.getTime()
                                            }
                                            booksPermission.save(failOnError: true, flush: true)
                                            userManagementService.numberOfBooksInLibrary(user.username)
                                            if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
                                        }
                                    }
                                }
                            }

                        if(siteMst.allBooksLibrary=="true") dataProviderService.getBooksListForUser(user.username)
                        else redisService.("userShelfBooks_"+user.username)=null
                    }
                    status = "The purchase is successful. Kindly check My Books now."
                }
            }
            if ("api".equals(params.mode)) {
                def json = [razorPaymentId: razorPayId, status: status,
                            title         : (booksMst != null ? booksMst.title : null),
                            cartBooks :cartBooks?cartBooks.substring(0, cartBooks.length() - 1):null,
                            mobile        : (user != null ? user.mobile : ""),
                            name          : (user != null ? user.name : "")]
                return json
            } else {

                def json =  [razorPaymentId: razorPayId, status: status,
                             title         : (booksMst != null ? booksMst.title : null),
                             mobile        : (user != null ? user.mobile : ""),
                             username      : (user != null ? user.username : ""),
                             cartBooks :cartBooks?cartBooks.substring(0, cartBooks.length() - 1):null,
                             name          : (user != null ? user.name : "")]
                return json
            }
        } catch(Exception ex){
            return "Bad razor payment id "+ex.toString()
        }
    }

    def getPaymentReports(siteId,request){
        RazorpayClient razorpay = getRazorpayClient(siteId)
        try {
            KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId("lastPaymentId",siteId)
            String dbPaymentId  = ""
            if(keyValueMst!=null) dbPaymentId  = keyValueMst.keyValue
            int count = 10
            int skip = 0
            int fromTime = 600 //10 minutes
            int toTime = 180 //3 minutes
            boolean runLoop = true
            String latestPaymentId = null
            def params =[]
            org.json.JSONObject paymentRequest = new org.json.JSONObject();
            //supported option filters (from, to, count, skip)
            Date date= new Date();

            long time = date.getTime();

            Timestamp ts = new Timestamp(time);
            int currentTimeInSeconds =  ts.getTime()/1000
            paymentRequest.put("count", count);
            paymentRequest.put("from", currentTimeInSeconds-fromTime);
            paymentRequest.put("to", currentTimeInSeconds-toTime);
            def bookId = null
            def username = null
            def description = null
            boolean transactionCompleted=true

            while(runLoop){
                paymentRequest.put("skip", skip);
                List<Payment> payments = razorpay.Payments.fetchAll(paymentRequest);
                if(payments.size()==0){
                    runLoop = false
                }
                payments.each { payment ->
                    transactionCompleted=true

                    if(latestPaymentId==null) {
                        latestPaymentId = payment.id
                    }

                    if(dbPaymentId.equals(payment.id)){
                        runLoop = false
                    } else {

                        try {
                            if (payment.get("notes") != null && payment.get("notes").length() > 0) {
                                username = ""+payment.get("notes").username
                                if(username!=null&&username.indexOf("_")>0) {
                                    siteId = new Integer(username.split("_")[0])
                                }
                            }

                            params = ["razorPayId": payment.id, "mobile": username]
                            if ("captured".equals(payment.get("status"))) {
                                PurchaseOrder purchaseOrder = PurchaseOrder.findByPaymentId(payment.get("id"))
                                if(purchaseOrder==null) transactionCompleted=false
                            }

                            if (!transactionCompleted || "authorized".equals(payment.get("status"))) {
                                selfService(params, siteId, request)
                                FixedPayments fixedPayments = new FixedPayments(paymentId: payment.get("id"), state: payment.get("status"))
                                fixedPayments.save(failOnError: true, flush: true)
                            }
                        }catch(Exception e){
                            println("doctor failed for "+payment.id)
                        }
                    }

                }

                skip +=count
            }

            if(latestPaymentId!=null) {
                if(keyValueMst==null) keyValueMst = new KeyValueMst(keyName: "lastPaymentId",keyValue: latestPaymentId,siteId: siteId)
                else  keyValueMst.keyValue = latestPaymentId
                keyValueMst.save(failOnError: true, flush: true)
            }

            return 0
        } catch (RazorpayException e) {
            // Handle Exception
            System.out.println(e.getMessage());
        }
    }

    def getHourlyPaymentReports(siteId,request){
        RazorpayClient razorpay = getRazorpayClient(siteId)
        try {
            int count = 10
            int skip = 0
            int fromTime = 3900 //1 hour 5 minutes
            int toTime = 300 //5 minutes
            boolean runLoop = true
            def params =[]
            org.json.JSONObject paymentRequest = new org.json.JSONObject();
            //supported option filters (from, to, count, skip)
            Date date= new Date();

            long time = date.getTime();

            Timestamp ts = new Timestamp(time);
            int currentTimeInSeconds =  ts.getTime()/1000
            paymentRequest.put("count", count);
            paymentRequest.put("from", currentTimeInSeconds-fromTime);
            paymentRequest.put("to", currentTimeInSeconds-toTime);
            def bookId = null
            def username = null
            def description = null
            boolean transactionCompleted=true

            while(runLoop){
                paymentRequest.put("skip", skip);
                List<Payment> payments = razorpay.Payments.fetchAll(paymentRequest);
                if(payments.size()==0){
                    runLoop = false
                }

                payments.each { payment ->
                    transactionCompleted=true
                    if ("authorized".equals(payment.get("status"))) transactionCompleted = false
                    else if ("captured".equals(payment.get("status"))){
                        PurchaseOrder purchaseOrder = PurchaseOrder.findByPaymentId(payment.get("id"))
                        if(purchaseOrder==null) transactionCompleted=false
                    }
                    if (!transactionCompleted) {
                        //first get book Id
                        try {
                            if (payment.get("notes") != null && payment.get("notes").length() > 0 ) {
                                username = payment.get("notes").username
                                if(username!=null&&username.indexOf("_")>0) {
                                    siteId = new Integer(username.split("_")[0])
                                }
                            }

                            params = ["razorPayId": payment.id, "mobile": username]
                            selfService(params, siteId, request)
                            FixedPayments fixedPayments = new FixedPayments(paymentId: payment.get("id"), state: payment.get("status")+"-Hourly",dateCreated: new Date())
                            fixedPayments.save(failOnError: true, flush: true)
                        } catch (Exception e) {
                            println("doctor failed for " + payment.id)
                        }
                    }

                }

                skip +=count
            }

            return 0
        } catch (RazorpayException e) {
            // Handle Exception
            System.out.println(e.getMessage());
        }
    }

    def addBookToUser(String bookId,siteId,request,session,String paymentId,String discountId,User user,String directSales) {
        double discountAmount=0
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)

        Integer poNo
        String status = "error"
        PurchaseOrder po = new PurchaseOrder()
        if(session["userdetails"].publisherId==null||(booksMst.publisherId.intValue()==session["userdetails"].publisherId.intValue())) {
            if (paymentId != null && !"".equals(paymentId) && booksMst != null && user != null) {
                po.dateCreated = new Date()
                po.itemCode = booksMst.id
                po.currency = "INR"
                po.status = 'Active'
                po.username = user.username
                po.siteId = siteId
                po.paymentId = paymentId
                po.poFor = "book"
                po.gstPercentage=5
                //   if(booksMst.isbn!=null && booksMst.isbn!="") po.gstPercentage=5
                //  else po.gstPercentage=18
                if(discountId!=null && !"".equals(discountId)){
                    DiscountPriceDtl discountPriceDtl =DiscountPriceDtl.findByDiscountId(new Integer(discountId));
                    if(discountPriceDtl !=null){
                        if(discountPriceDtl.discountValue!=null){
                            discountAmount=discountPriceDtl.discountValue
                        }else if(discountPriceDtl.discountPercentage!=null){
                            //calcalute the amount by percentage
                            Integer perc= discountPriceDtl.discountPercentage;
                            discountAmount=((booksMst.price * perc) / 100)
                            String pAchualPrice = String.format("%.2f",discountAmount)
                            discountAmount = Double.parseDouble(pAchualPrice)
                        }
                    }
                    po.bookPrice = booksMst.price
                    po.amount = booksMst.price-discountAmount
                    po.discountAmount=discountAmount
                    po.discountId= new Integer(discountId)
                }else{
                    po.amount = booksMst.price
                }
                po.directSales=directSales
                po.save(failOnError: true, flush: true)
                poNo = po.id
            }

            if (booksMst != null && user != null) {
                BooksPermission booksPermission = new BooksPermission()
                booksPermission.bookId = booksMst.id
                booksPermission.username = user.username
                if (paymentId != null && !"".equals(paymentId)) {
                    booksPermission.poNo = poNo
                    booksPermission.poType = 'PURCHASE'
                } else {
                    booksPermission.poType = 'ADDEDFORFREE'
                }
                booksPermission.addedBy = springSecurityService.currentUser.username
                if (booksMst != null && booksMst.bookExpiry != null && booksMst.bookExpiry != ""){
                    booksPermission.expiryDate = booksMst.bookExpiry
                }
                else if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                    Calendar c = Calendar.getInstance()
                    c.add(Calendar.DATE, booksMst.validityDays)
                    booksPermission.expiryDate = c.getTime()
                }
                booksPermission.save(failOnError: true, flush: true)
                userManagementService.numberOfBooksInLibrary(user.username)
                if (siteId.intValue() == 1 && paymentId != null && !"".equals(paymentId)) {
                    if (userManagementService.validateEmail(user.email, siteId)) {
                        String loginId = user.username
                        try {
                            userManagementService.userBookPurchase(booksMst.price + "", user.email,
                                    user.name, po.paymentId, loginId.split(siteId + "_")[1], booksMst.title)
                        } catch (Exception e) {
                            println "Purchase user  email failed " + e.toString()
                        }
                    }
                    if (user.mobile != null && user.mobile != "") {
                        try {
                            def message = "Dear ${user.name}, Thank you for purchasing the Book on Wonderslate. Your Book is available in My Books section.-- Wonderslate Technologies Pvt Ltd"
                            utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                        }catch (Exception e) {
                            println "Purchase user  sms failed " + e.toString()
                        }
                    }
                }
                //code to add package books
                if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
                if(siteMst.allBooksLibrary=="true") dataProviderService.getBooksListForUser(user.username)
                else redisService.("userShelfBooks_"+user.username)=null
                status = "Ok"
            }
        }
        return status
    }

    private static final String[] tensNames = [
            "",
            " ten",
            " twenty",
            " thirty",
            " forty",
            " fifty",
            " sixty",
            " seventy",
            " eighty",
            " ninety"
    ]

    private static final String[] numNames = [
            "",
            " one",
            " two",
            " three",
            " four",
            " five",
            " six",
            " seven",
            " eight",
            " nine",
            " ten",
            " eleven",
            " twelve",
            " thirteen",
            " fourteen",
            " fifteen",
            " sixteen",
            " seventeen",
            " eighteen",
            " nineteen"
    ]



    static String convertLessThanOneThousand(int number) {
        String soFar;

        if (number % 100 < 20){
            soFar = numNames[number % 100];
            number /= 100;
        }
        else {
            soFar = numNames[number % 10];
            number /= 10;

            soFar = tensNames[number % 10] + soFar;
            number /= 10;
        }
        if (number == 0) return soFar;
        return numNames[number] + " hundred" + soFar;
    }


    static String convert(long number) {
        // 0 to 999 999 999 999
        if (number == 0) { return "zero"; }

        String snumber = Long.toString(number);

        // pad with "0"
        String mask = "000000000000";
        DecimalFormat df = new DecimalFormat(mask);
        snumber = df.format(number);

        // XXXnnnnnnnnn
        int billions = Integer.parseInt(snumber.substring(0,3));
        // nnnXXXnnnnnn
        int millions  = Integer.parseInt(snumber.substring(3,6));
        // nnnnnnXXXnnn
        int hundredThousands = Integer.parseInt(snumber.substring(6,9));
        // nnnnnnnnnXXX
        int thousands = Integer.parseInt(snumber.substring(9,12));

        String tradBillions;
        switch (billions) {
            case 0:
                tradBillions = "";
                break;
            case 1 :
                tradBillions = convertLessThanOneThousand(billions)
                + " billion ";
                break;
            default :
                tradBillions = convertLessThanOneThousand(billions)
                + " billion ";
        }
        String result =  tradBillions;

        String tradMillions;
        switch (millions) {
            case 0:
                tradMillions = "";
                break;
            case 1 :
                tradMillions = convertLessThanOneThousand(millions)
                + " million ";
                break;
            default :
                tradMillions = convertLessThanOneThousand(millions)
                + " million ";
        }
        result =  result + tradMillions;

        String tradHundredThousands;
        switch (hundredThousands) {
            case 0:
                tradHundredThousands = "";
                break;
            case 1 :
                tradHundredThousands = "one thousand ";
                break;
            default :
                tradHundredThousands = convertLessThanOneThousand(hundredThousands)
                + " thousand ";
        }
        result =  result + tradHundredThousands;

        String tradThousand;
        tradThousand = convertLessThanOneThousand(thousands);
        result =  result + tradThousand;

        // remove extra spaces!
        return result.replaceAll("^\\s+", "").replaceAll("\\b\\s{2,}\\b", " ");
    }


    def sendEmailAndCompleteCleanup(poNo, hasPrintBooks, user, siteId, payment, siteDtl, siteMst, entryController,cartMstId,purchaseOrdersList,request) {

        PurchaseOrder poTemp = purchaseOrdersList[0]
        Double deliveryCosts = new Double(0.0)
        double totalAmount
        String str1
        String cartBooks = ""
        if (hasPrintBooks) {
            //add delivery details
            deliveryCosts = deliveryChargesService.deliveryChargesCalculator(user.username, true, new Integer(payment.get("notes").shoppingCartId))
        }

        purchaseOrdersList.collect { cart ->
            totalAmount += cart.amount

        }
        totalAmount += deliveryCosts.doubleValue()
        try {

            Integer rupee = (Integer) totalAmount
            str1 = "Rupees " + convert(rupee.intValue())
            Integer paisa = (Integer) Math.round((totalAmount - rupee) * 100)
            if (paisa > 0) str1 += " AND " + convert(paisa.intValue()) + " Paise "
        } catch (Exception e) {
            str1 = ""
        }
        String email = payment.get("email");
        if (email == null && user.email != null) email = user.email
        try {
            DateFormat df = new SimpleDateFormat("dd.MM.yyyy / hh.mm aa")
             if (userManagementService.validateEmail(email, siteId)) {
                 userManagementService.invoiceEmail(siteMst,siteDtl,user,poTemp,str1,request,entryController,deliveryCosts,totalAmount,purchaseOrdersList,poNo)
             }
        } catch (Exception e) {
            println "Purchase Invoice email failed " + e.toString()
        }
        HashMap vendorItems = new HashMap()
        HashMap books = new HashMap()
        BooksMst booksMst
        List booksList = ShoppingCartOrdersDtl.findAllByCartMstIdAndBookTypeInList(new Long(payment.get("notes").shoppingCartId), ["eBook", "testSeries", "combo"])
            if (booksList.size() > 0) {
                booksList.each { cartBooksDtl ->
                    booksMst = dataProviderService.getBooksMst(cartBooksDtl.bookId)
                    cartBooks += booksMst.title + ","
                    if (vendorItems.get(booksMst.publisherId) == null) {
                        HashMap itemDetails = new HashMap()
                          itemDetails.put("bookTitle",booksMst.title)
                        vendorItems.put(booksMst.publisherId, itemDetails)
                    }else{
                        // Vendor already present
                        HashMap itemDetails = vendorItems.get(booksMst.publisherId)
                        itemDetails.put("bookTitle",booksMst.title)
                        vendorItems.put(booksMst.publisherId, itemDetails)
                    }
                }
                //send vendor emails
                vendorItems.each { publisherIdTemp, itemDetails ->
                    //this is the place to send out email to publisher.
                    Publishers publishers = dataProviderService.getPublisher(publisherIdTemp)

                    if(publishers!=null&&publishers.email!=null) {
                        String toEmail =publishers.email
                         siteMst = dataProviderService.getSiteMst(siteId)
                        String    fromEmail
                        if(siteMst.fromEmail!=null&&!"".equals(siteMst.fromEmail))
                            fromEmail = siteMst.fromEmail
                        else
                            fromEmail = "Wonderslate <<EMAIL>>"
                        String mailText = "Hello \n\n Sale for the following eBooks have happened.\n\n"
                        String[] bookTitles = itemDetails.bookTitle.split(',')
                        for(int i=0;i<bookTitles.length;i++){
                            mailText +=bookTitles[0]+"\n"
                        }
                        mailText += "\n \n Please login to system to check full details."
                        try {
                            String[] toEmails = toEmail.split(',')
                            for(int i=0;i<toEmails.length;i++) {
                                if("<EMAIL>"!=toEmails[i]) {
                                    mailService.sendMail {
                                        async true
                                        to toEmails[i]
                                        from fromEmail
                                        subject "Order received " + cartMstId
                                        text mailText
                                    }
                                }
                            }
                        }catch(Exception e){
                            println("Exception in sending userBookPurchase email to "+toEmail+" and exception is "+e.toString())
                        }
                    }
                }


                cartBooks = cartBooks ? cartBooks.substring(0, cartBooks.length() - 1) : null
                if (siteId.intValue() == 1) {
                    String loginId = user.username
                    try {
                        userManagementService.userBookPurchase(totalAmount + "", email,
                                user.name, poTemp.paymentId, loginId.split(siteId + "_")[1], cartBooks)
                    } catch (Exception e) {
                        println "Purchase user  email failed " + e.toString()
                    }


                    if (user.mobile != null && user.mobile != "") {
                        try {
                            def message = "Dear ${user.name}, Thank you for purchasing the Book on Wonderslate. Your Book is available in My Books section.-- Wonderslate Technologies Pvt Ltd"
                            utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                        } catch (Exception e) {
                            println "Purchase user  sms failed " + e.toString()
                        }
                    }
                }

            }






        ShoppingCartActiveDtl.executeUpdate("update ShoppingCartActiveDtl set username='delete_-"+user.username+"' where username='"+user.username+"'")

    }

    Promise addPurchaseBillShipAddress(username, siteId,cartMstId) {
        Promises.task {
            wsshopService.addPurchaseBillShipAddress(username, siteId,cartMstId)
        }
    }

    Promise updateLastSold(bookId) {
        Promises.task {
            purchaseSubService.updateLastSold(bookId)
        }
    }

    def sendPublisherEmails(){

    }



}
