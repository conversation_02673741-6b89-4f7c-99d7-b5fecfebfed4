package com.wonderslate.report

import com.wonderslate.sqlutil.SafeSql
import grails.transaction.Transactional
import java.text.SimpleDateFormat

/**
 * Service for book-level analytics functionality
 */

class BookAnalyticsService {

    def grailsApplication

    /**
     * Get book analytics data
     * @param username User's username
     * @param bookId Book ID to filter by
     * @param fromDate Start date for filtering (optional)
     * @param toDate End date for filtering (optional)
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing book analytics data
     */
    def getBookAnalytics(String username, Long bookId, Date fromDate = null, Date toDate = null, Long batchId = null, boolean isDemoMode = false) {
        log.info("Service - getBookAnalytics called with isDemoMode=${isDemoMode}")
        // Return demo data if requested
        if (isDemoMode) {
            log.info("Service - Generating demo data for bookId=${bookId}")
            def demoData = generateDemoBookAnalytics(bookId)
            log.info("Service - Demo data generated: ${demoData}")
            return demoData
        }
        try {
            log.info("getBookAnalytics called with username=${username}, bookId=${bookId}")

            // Get chapter data for the book (from default schema)
            String chapterSql = """
                SELECT
                    cm.id as chapter_id,
                    cm.name as chapter_name,
                    COUNT(DISTINCT rd.id) as resource_count
                FROM
                    chapters_mst cm
                LEFT JOIN
                    resource_dtl rd ON rd.chapter_id = cm.id AND rd.res_type NOT IN ('Reference Web Links')
                WHERE
                    cm.book_id = ${bookId}
                GROUP BY
                    cm.id, cm.name
                ORDER BY
                    cm.sort_order
            """

            def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql = new SafeSql(defaultDataSource)
            def chapterResults = sql.rows(chapterSql)

            // Get resource IDs for the book to filter log results
            String resourceSql = """
                SELECT
                    rd.id as resource_id,
                    rd.chapter_id
                FROM
                    resource_dtl rd
                JOIN
                    chapters_mst cm ON rd.chapter_id = cm.id
                WHERE
                    cm.book_id = ${bookId}
                    AND rd.res_type NOT IN ('Reference Web Links')
            """
            def resourceResults = sql.rows(resourceSql)
            def bookResourceIds = resourceResults.collect { it.resource_id }
            def resourceChapterMap = [:]
            resourceResults.each { resource ->
                resourceChapterMap[resource.resource_id] = resource.chapter_id
            }

            // Add a dummy ID if the list is empty to avoid SQL errors
            if (bookResourceIds.isEmpty()) {
                bookResourceIds.add(-1) // Using -1 as a dummy ID that won't match any real resource
            }

            // Get resource view data from wslog schema
            String resourceViewSql = """
                SELECT
                    rv.resource_dtl_id,
                    COUNT(DISTINCT rv.id) as view_count
                FROM
                    wslog.resource_view rv
                WHERE
                    rv.resource_dtl_id IN (${bookResourceIds.join(',')})
                    AND rv.username = '${username}'
                GROUP BY
                    rv.resource_dtl_id
            """

            def logDataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
            def logSql = new SafeSql(logDataSource)
            def resourceViewResults = logSql.rows(resourceViewSql)

            // Create a map for quick lookup of view counts by resource ID
            def resourceViewMap = [:]
            resourceViewResults.each { view ->
                resourceViewMap[view.resource_dtl_id] = view.view_count
            }

            // Get GPT interaction data for the book from wslog schema
            StringBuilder gptSqlBuilder = new StringBuilder("""
                SELECT
                    gl.prompt_type,
                    gl.res_id,
                    COUNT(*) as interaction_count
                FROM
                    wslog.gpt_log gl
                WHERE
                    gl.username = '${username}'
                    AND gl.res_id IN (${bookResourceIds.join(',')})
            """)

            // Add date range filters if provided
            if (fromDate) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
                gptSqlBuilder.append(" AND gl.date_created >= '${sdf.format(fromDate)}'")
            }

            if (toDate) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
                gptSqlBuilder.append(" AND gl.date_created <= '${sdf.format(toDate)}'")
            }

            // Add batch filter if provided
            if (batchId) {
                gptSqlBuilder.append("""
                    AND EXISTS (
                        SELECT 1 FROM wsuser.batch_user_dtl bud
                        WHERE bud.username = '${username}'
                        AND bud.batch_id = ${batchId}
                    )
                """)
            }

            gptSqlBuilder.append("""
                GROUP BY
                    gl.prompt_type, gl.res_id
            """)

            String gptSql = gptSqlBuilder.toString()
            def gptResults = logSql.rows(gptSql)

            // Create a map for quick lookup of GPT counts by resource ID
            def resourceGptMap = [:]
            gptResults.each { gpt ->
                if (!resourceGptMap[gpt.res_id]) {
                    resourceGptMap[gpt.res_id] = 0
                }
                resourceGptMap[gpt.res_id] += gpt.interaction_count
            }

            // Get book title
            String bookSql = "SELECT title FROM books_mst WHERE id = ${bookId}"
            def bookResult = sql.rows(bookSql)
            String bookTitle = bookResult.size() > 0 ? bookResult[0].title : "Unknown Book"

            // Process chapter data
            def chapterLabels = []
            def chapterData = []
            def chapterDetails = []
            def total = 0

            chapterResults.each { chapter ->
                def chapterId = chapter.chapter_id
                def chapterName = chapter.chapter_name
                def resourceCount = chapter.resource_count ?: 0

                // Calculate viewed count for this chapter by summing views of all resources in the chapter
                def chapterResourceIds = resourceResults.findAll { it.chapter_id == chapterId }.collect { it.resource_id }
                def viewedCount = chapterResourceIds.sum { resourceId ->
                    resourceViewMap[resourceId] ?: 0
                } ?: 0

                // Calculate GPT interactions for this chapter by summing GPT counts of all resources in the chapter
                def chapterGptCount = chapterResourceIds.sum { resourceId ->
                    resourceGptMap[resourceId] ?: 0
                } ?: 0

                def count = viewedCount + chapterGptCount
                total += count

                if (count > 0 || resourceCount > 0) {
                    chapterLabels.add(chapterName)
                    chapterData.add(count)
                    chapterDetails.add([
                        id: chapterId,
                        name: chapterName,
                        count: count,
                        resourceCount: resourceCount,
                        viewedCount: viewedCount,
                        gptCount: chapterGptCount
                    ])
                }
            }

            return [
                chapterData: [
                    labels: chapterLabels,
                    data: chapterData,
                    total: total
                ],
                chapterDetails: chapterDetails,
                bookTitle: bookTitle
            ]
        } catch (Exception e) {
            log.error("Error in getBookAnalytics: ${e.message}")
            e.printStackTrace()
            throw e
        }
    }

    /**
     * Get chapter analytics data
     * @param username User's username
     * @param chapterId Chapter ID to filter by
     * @param fromDate Start date for filtering (optional)
     * @param toDate End date for filtering (optional)
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing chapter analytics data
     */
    def getChapterAnalytics(String username, Long chapterId, Date fromDate = null, Date toDate = null, Long batchId = null, boolean isDemoMode = false) {
        log.info("Service - getChapterAnalytics called with isDemoMode=${isDemoMode}")
        // Return demo data if requested
        if (isDemoMode) {
            log.info("Service - Generating demo data for chapterId=${chapterId}")
            def demoData = generateDemoChapterAnalytics(chapterId)
            log.info("Service - Chapter demo data generated: ${demoData}")
            return demoData
        }
        try {
            log.info("getChapterAnalytics called with username=${username}, chapterId=${chapterId}")

            // Get resource data for the chapter (from default schema)
            String resourceSql = """
                SELECT
                    rd.id as resource_id,
                    rd.resource_name,
                    rd.res_type
                FROM
                    resource_dtl rd
                WHERE
                    rd.chapter_id = ${chapterId}
                ORDER BY
                    rd.sort_order
            """

            def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql = new SafeSql(defaultDataSource)
            def resourceResults = sql.rows(resourceSql)

            // Get resource IDs for the chapter to filter log results
            def chapterResourceIds = resourceResults.collect { it.resource_id }

            // Add a dummy ID if the list is empty to avoid SQL errors
            if (chapterResourceIds.isEmpty()) {
                chapterResourceIds.add(-1) // Using -1 as a dummy ID that won't match any real resource
            }

            def logDataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
            def logSql = new SafeSql(logDataSource)

            // Get resource view data from wslog schema
            String resourceViewSql = """
                SELECT
                    rv.resource_dtl_id,
                    MIN(rv.date_created) as first_viewed,
                    COUNT(rv.id) as view_count
                FROM
                    wslog.resource_view rv
                WHERE
                    rv.resource_dtl_id IN (${chapterResourceIds.join(',')})
                    AND rv.username = '${username}'
                GROUP BY
                    rv.resource_dtl_id
            """

            def resourceViewResults = logSql.rows(resourceViewSql)

            // Create a map for quick lookup of view data by resource ID
            def resourceViewMap = [:]
            resourceViewResults.each { view ->
                resourceViewMap[view.resource_dtl_id] = [
                    view_count: view.view_count,
                    first_viewed: view.first_viewed
                ]
            }

            // Get GPT interaction data for the chapter from wslog schema
            StringBuilder gptSqlBuilder = new StringBuilder("""
                SELECT
                    gl.res_id,
                    gl.prompt_type,
                    COUNT(*) as interaction_count
                FROM
                    wslog.gpt_log gl
                WHERE
                    gl.username = '${username}'
                    AND gl.res_id IN (${chapterResourceIds.join(',')})
            """)

            // Add date range filters if provided
            if (fromDate) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
                gptSqlBuilder.append(" AND gl.date_created >= '${sdf.format(fromDate)}'")
            }

            if (toDate) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
                gptSqlBuilder.append(" AND gl.date_created <= '${sdf.format(toDate)}'")
            }

            // Add batch filter if provided
            if (batchId) {
                gptSqlBuilder.append("""
                    AND EXISTS (
                        SELECT 1 FROM wsuser.batch_user_dtl bud
                        WHERE bud.username = '${username}'
                        AND bud.batch_id = ${batchId}
                    )
                """)
            }

            gptSqlBuilder.append("""
                GROUP BY
                    gl.res_id, gl.prompt_type
            """)

            String gptSql = gptSqlBuilder.toString()
            def gptResults = logSql.rows(gptSql)

            // Create a map for quick lookup of GPT counts by resource ID
            def resourceGptMap = [:]
            gptResults.each { gpt ->
                if (!resourceGptMap[gpt.res_id]) {
                    resourceGptMap[gpt.res_id] = 0
                }
                resourceGptMap[gpt.res_id] += gpt.interaction_count
            }

            // Get chapter and book info
            String chapterSql = """
                SELECT
                    cm.name as chapter_name,
                    cm.book_id,
                    bm.title as book_title
                FROM
                    chapters_mst cm
                JOIN
                    books_mst bm ON cm.book_id = bm.id
                WHERE
                    cm.id = ${chapterId}
            """

            def chapterResult = sql.rows(chapterSql)
            String chapterName = chapterResult.size() > 0 ? chapterResult[0].chapter_name : "Unknown Chapter"
            String bookTitle = chapterResult.size() > 0 ? chapterResult[0].book_title : "Unknown Book"
            Long bookId = chapterResult.size() > 0 ? chapterResult[0].book_id : 0

            // Process resource data
            def resourceDetails = []
            def totalViews = 0
            def totalInteractions = 0

            resourceResults.each { resource ->
                def resourceId = resource.resource_id
                def resourceName = resource.resource_name
                def resourceType = resource.res_type

                // Get view data for this resource
                def viewData = resourceViewMap[resourceId]
                def viewCount = viewData?.view_count ?: 0
                def firstViewed = viewData?.first_viewed

                // Get GPT interactions for this resource
                def gptCount = resourceGptMap[resourceId] ?: 0

                totalViews += viewCount
                totalInteractions += gptCount

                resourceDetails.add([
                    id: resourceId,
                    name: resourceName,
                    type: resourceType,
                    viewCount: viewCount,
                    firstViewed: firstViewed,
                    gptCount: gptCount,
                    totalInteractions: viewCount + gptCount
                ])
            }

            // Get prompt type distribution
            def promptTypes = gptResults.collect { it.prompt_type }.unique()
            def promptTypeLabels = []
            def promptTypeData = []

            promptTypes.each { promptType ->
                def count = gptResults.findAll { it.prompt_type == promptType }.sum { it.interaction_count ?: 0 } ?: 0
                promptTypeLabels.add(promptType)
                promptTypeData.add(count)
            }

            // Get time-based data (last 4 weeks)
            def weeklyData = []
            def weekLabels = []

            for (int i = 3; i >= 0; i--) {
                def cal = Calendar.instance
                cal.add(Calendar.WEEK_OF_YEAR, -i)
                def weekStart = cal.time
                cal.add(Calendar.DAY_OF_YEAR, 6)
                def weekEnd = cal.time

                def weekLabel = "Week ${4-i}"
                weekLabels.add(weekLabel)

                // Format dates for SQL
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
                String weekStartStr = sdf.format(weekStart)
                String weekEndStr = sdf.format(weekEnd)

                // Get view count for this week
                StringBuilder weekViewSqlBuilder = new StringBuilder("""
                    SELECT
                        COUNT(*) as count
                    FROM
                        wslog.resource_view rv
                    WHERE
                        rv.resource_dtl_id IN (${chapterResourceIds.join(',')})
                        AND rv.username = '${username}'
                        AND rv.date_created BETWEEN '${weekStartStr}' AND '${weekEndStr}'
                """)

                // Add batch filter if provided
                if (batchId) {
                    weekViewSqlBuilder.append("""
                        AND EXISTS (
                            SELECT 1 FROM wsuser.batch_user_dtl bud
                            WHERE bud.username = '${username}'
                            AND bud.batch_id = ${batchId}
                        )
                    """)
                }

                String weekViewSql = weekViewSqlBuilder.toString()

                def weekViewResult = logSql.rows(weekViewSql)
                def weekViewCount = weekViewResult.size() > 0 ? weekViewResult[0].count : 0

                // Get GPT count for this week
                StringBuilder weekGptSqlBuilder = new StringBuilder("""
                    SELECT
                        COUNT(*) as count
                    FROM
                        wslog.gpt_log gl
                    WHERE
                        gl.res_id IN (${chapterResourceIds.join(',')})
                        AND gl.username = '${username}'
                        AND gl.date_created BETWEEN '${weekStartStr}' AND '${weekEndStr}'
                """)

                // Add batch filter if provided
                if (batchId) {
                    weekGptSqlBuilder.append("""
                        AND EXISTS (
                            SELECT 1 FROM wsuser.batch_user_dtl bud
                            WHERE bud.username = '${username}'
                            AND bud.batch_id = ${batchId}
                        )
                    """)
                }

                String weekGptSql = weekGptSqlBuilder.toString()

                def weekGptResult = logSql.rows(weekGptSql)
                def weekGptCount = weekGptResult.size() > 0 ? weekGptResult[0].count : 0

                weeklyData.add(weekViewCount + weekGptCount)
            }

            return [
                chapterInfo: [
                    id: chapterId,
                    name: chapterName,
                    bookId: bookId,
                    bookTitle: bookTitle,
                    totalResources: resourceResults.size(),
                    totalViews: totalViews,
                    totalInteractions: totalInteractions
                ],
                resourceDetails: resourceDetails,
                promptTypeData: [
                    labels: promptTypeLabels,
                    data: promptTypeData
                ],
                timeData: [
                    labels: weekLabels,
                    data: weeklyData
                ],
                metrics: [
                    [name: "Total Resources", value: resourceResults.size()],
                    [name: "Resources Viewed", value: resourceDetails.count { it.viewCount > 0 }],
                    [name: "Total Views", value: totalViews],
                    [name: "GPT Interactions", value: totalInteractions],
                    [name: "Completion Rate", value: Math.round((resourceDetails.count { it.viewCount > 0 } / (resourceResults.size() ?: 1)) * 100) + "%"]
                ]
            ]
        } catch (Exception e) {
            log.error("Error in getChapterAnalytics: ${e.message}")
            e.printStackTrace()
            throw e
        }
    }

    /**
     * Generate demo data for book analytics
     * @param bookId Book ID
     * @return Map containing demo book analytics data
     */
    private def generateDemoBookAnalytics(Long bookId) {
        log.info("Generating demo book analytics data for bookId=${bookId}")

        // Generate random chapter names and data
        def chapterNames = [
            "Introduction",
            "Basic Concepts",
            "Advanced Topics",
            "Practical Applications",
            "Case Studies",
            "Review and Practice"
        ]

        def chapterLabels = []
        def chapterData = []
        def chapterDetails = []
        def total = 0

        // Generate random data for each chapter
        chapterNames.eachWithIndex { chapterName, index ->
            def chapterId = index + 1
            def count = 20 + (int)(Math.random() * 80) // Random count between 20 and 100
            total += count

            chapterLabels.add(chapterName)
            chapterData.add(count)
            chapterDetails.add([
                id: chapterId,
                name: chapterName,
                count: count,
                resourceCount: 10 + (int)(Math.random() * 20), // Random resource count between 10 and 30
                viewedCount: (int)(count * 0.7), // 70% of count
                gptCount: (int)(count * 0.3) // 30% of count
            ])
        }

        // Generate practice data for the book
        def practiceData = generateDemoPracticeDataForBook(bookId)

        return [
            chapterData: [
                labels: chapterLabels,
                data: chapterData,
                total: total
            ],
            chapterDetails: chapterDetails,
            bookTitle: "Demo Book",
            practiceData: practiceData
        ]
    }

    /**
     * Generate demo data for chapter analytics
     * @param chapterId Chapter ID
     * @return Map containing demo chapter analytics data
     */
    private def generateDemoChapterAnalytics(Long chapterId) {
        log.info("Generating demo chapter analytics data for chapterId=${chapterId}")

        // Generate random resource names and types
        def resourceTypes = ["Text", "Image", "Video", "Interactive", "Quiz"]
        def resourceNames = [
            "Introduction to the Topic",
            "Key Concepts Explained",
            "Detailed Analysis",
            "Practical Example 1",
            "Practical Example 2",
            "Summary and Review",
            "Self-Assessment Quiz"
        ]

        def resourceDetails = []
        def totalViews = 0
        def totalInteractions = 0

        // Generate random data for each resource
        resourceNames.eachWithIndex { resourceName, index ->
            def resourceId = index + 1
            def resourceType = resourceTypes[(int)(Math.random() * resourceTypes.size())]
            def viewCount = 5 + (int)(Math.random() * 45) // Random view count between 5 and 50
            def gptCount = 2 + (int)(Math.random() * 18) // Random GPT count between 2 and 20

            totalViews += viewCount
            totalInteractions += gptCount

            resourceDetails.add([
                id: resourceId,
                name: resourceName,
                type: resourceType,
                viewCount: viewCount,
                firstViewed: new Date() - (int)(Math.random() * 30), // Random date within last 30 days
                gptCount: gptCount,
                totalInteractions: viewCount + gptCount
            ])
        }

        // Generate practice data for the demo
        def practiceData = generateDemoPracticeDataForChapter(chapterId)

        // Add practice data to the result

        // Generate prompt type data
        def promptTypes = ["Explain", "Summarize", "Quiz", "Examples", "Practice"]
        def promptTypeLabels = []
        def promptTypeData = []

        promptTypes.each { promptType ->
            def count = 5 + (int)(Math.random() * 15) // Random count between 5 and 20
            promptTypeLabels.add(promptType)
            promptTypeData.add(count)
        }

        // Generate weekly data
        def weekLabels = ["Week 1", "Week 2", "Week 3", "Week 4"]
        def weeklyData = []

        weekLabels.each { weekLabel ->
            def count = 10 + (int)(Math.random() * 40) // Random count between 10 and 50
            weeklyData.add(count)
        }

        return [
            chapterInfo: [
                id: chapterId,
                name: "Demo Chapter ${chapterId}",
                bookId: 1,
                bookTitle: "Demo Book",
                totalResources: resourceDetails.size(),
                totalViews: totalViews,
                totalInteractions: totalInteractions
            ],
            resourceDetails: resourceDetails,
            promptTypeData: [
                labels: promptTypeLabels,
                data: promptTypeData
            ],
            timeData: [
                labels: weekLabels,
                data: weeklyData
            ],
            metrics: [
                [name: "Total Resources", value: resourceDetails.size()],
                [name: "Resources Viewed", value: resourceDetails.size()],
                [name: "Total Views", value: totalViews],
                [name: "GPT Interactions", value: totalInteractions],
                [name: "Completion Rate", value: "85%"]
            ],
            practiceData: practiceData
        ]
    }

    /**
     * Generate demo practice data for a book
     * @param bookId Book ID
     * @return Map containing demo practice data
     */
    private def generateDemoPracticeDataForBook(Long bookId) {
        log.info("Generating demo practice data for bookId=${bookId}")

        // Generate random chapter names
        def chapterNames = [
            "Introduction",
            "Basic Concepts",
            "Advanced Topics",
            "Practical Applications",
            "Case Studies",
            "Review and Practice"
        ]

        def practiceDetails = []
        def totalQuestions = 0
        def totalCorrect = 0

        // Generate random data for each chapter
        chapterNames.eachWithIndex { chapterName, index ->
            def chapterId = index + 1
            def questionCount = 10 + (int)(Math.random() * 40) // Random count between 10 and 50
            def correctCount = (int)(questionCount * (0.6 + new Random().nextDouble() * 0.3)) // 60-90% correct
            def accuracy = (correctCount / questionCount) * 100

            totalQuestions += questionCount
            totalCorrect += correctCount

            practiceDetails.add([
                id: chapterId,
                name: chapterName,
                questionCount: questionCount,
                correctCount: correctCount,
                accuracy: Math.round(accuracy * 10) / 10 // Round to 1 decimal place
            ])
        }

        // Calculate overall accuracy
        def overallAccuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 1000) / 10 : 0

        // Generate time series data for practice trends
        def timeLabels = []
        def practiceData = []
        def accuracyData = []

        // Generate data for the last 4 weeks
        for (int i = 3; i >= 0; i--) {
            def weekLabel = "Week ${4-i}"
            timeLabels.add(weekLabel)

            // Random practice count for this week
            def weekCount = 5 + (int)(Math.random() * 45) // Random count between 5 and 50
            practiceData.add(weekCount)

            // Random accuracy for this week
            def weekAccuracy = 60 + (int)(Math.random() * 35) // Random accuracy between 60% and 95%
            accuracyData.add(weekAccuracy)
        }

        return [
            summary: [
                totalQuestions: totalQuestions,
                totalCorrect: totalCorrect,
                overallAccuracy: overallAccuracy
            ],
            chapterDetails: practiceDetails,
            timeSeriesData: [
                labels: timeLabels,
                practiceData: practiceData,
                accuracyData: accuracyData
            ]
        ]
    }

    /**
     * Generate demo practice data for a chapter
     * @param chapterId Chapter ID
     * @return Map containing demo practice data
     */
    private def generateDemoPracticeDataForChapter(Long chapterId) {
        log.info("Generating demo practice data for chapterId=${chapterId}")

        // Generate random topic names
        def topicNames = [
            "Topic 1: Fundamentals",
            "Topic 2: Key Principles",
            "Topic 3: Applications",
            "Topic 4: Problem Solving",
            "Topic 5: Advanced Concepts"
        ]

        def practiceDetails = []
        def totalQuestions = 0
        def totalCorrect = 0

        // Generate random data for each topic
        topicNames.eachWithIndex { topicName, index ->
            def topicId = index + 1
            def questionCount = 5 + (int)(Math.random() * 15) // Random count between 5 and 20
            def correctCount = (int)(questionCount * (0.6 + new Random().nextDouble() * 0.3)) // 60-90% correct
            def accuracy = (correctCount / questionCount) * 100

            totalQuestions += questionCount
            totalCorrect += correctCount

            practiceDetails.add([
                id: topicId,
                name: topicName,
                questionCount: questionCount,
                correctCount: correctCount,
                accuracy: Math.round(accuracy * 10) / 10 // Round to 1 decimal place
            ])
        }

        // Calculate overall accuracy
        def overallAccuracy = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 1000) / 10 : 0

        // Generate time series data for practice trends
        def timeLabels = []
        def practiceData = []
        def accuracyData = []

        // Generate data for the last 4 weeks
        for (int i = 3; i >= 0; i--) {
            def weekLabel = "Week ${4-i}"
            timeLabels.add(weekLabel)

            // Random practice count for this week
            def weekCount = 3 + (int)(Math.random() * 17) // Random count between 3 and 20
            practiceData.add(weekCount)

            // Random accuracy for this week
            def weekAccuracy = 60 + (int)(Math.random() * 35) // Random accuracy between 60% and 95%
            accuracyData.add(weekAccuracy)
        }

        // Generate question type distribution
        def questionTypes = ["Multiple Choice", "True/False", "Fill in the Blank", "Short Answer", "Essay"]
        def questionTypeData = []

        questionTypes.each { type ->
            questionTypeData.add((int)(Math.random() * 30) + 5) // Random count between 5 and 35
        }

        return [
            summary: [
                totalQuestions: totalQuestions,
                totalCorrect: totalCorrect,
                overallAccuracy: overallAccuracy
            ],
            topicDetails: practiceDetails,
            timeSeriesData: [
                labels: timeLabels,
                practiceData: practiceData,
                accuracyData: accuracyData
            ],
            questionTypeData: [
                labels: questionTypes,
                data: questionTypeData
            ]
        ]
    }

    /**
     * Get practice data for a book
     * @param username User's username
     * @param bookId Book ID to filter by
     * @param fromDate Start date for filtering (optional)
     * @param toDate End date for filtering (optional)
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing book practice data
     */
    def getBookPracticeData(String username, Long bookId, Date fromDate = null, Date toDate = null, Long batchId = null, boolean isDemoMode = false) {
        log.info("Service - getBookPracticeData called with isDemoMode=${isDemoMode}")
        // Return demo data if requested
        if (isDemoMode) {
            log.info("Service - Generating demo practice data for bookId=${bookId}")
            def demoData = generateDemoPracticeDataForBook(bookId)
            log.info("Service - Demo practice data generated")
            return demoData
        }

        try {
            log.info("getBookPracticeData called with username=${username}, bookId=${bookId}")

            // TODO: Implement real data retrieval from database
            // For now, we need to return empty data when not in demo mode
            // This will show the "No data available" message
            return [
                summary: [
                    totalQuestions: 0,
                    totalCorrect: 0,
                    overallAccuracy: 0
                ],
                chapterDetails: [],
                timeSeriesData: [
                    labels: [],
                    practiceData: [],
                    accuracyData: []
                ]
            ]

        } catch (Exception e) {
            log.error("Error in getBookPracticeData: ${e.message}")
            e.printStackTrace()
            throw e
        }
    }

    /**
     * Get practice data for a chapter
     * @param username User's username
     * @param chapterId Chapter ID to filter by
     * @param fromDate Start date for filtering (optional)
     * @param toDate End date for filtering (optional)
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing chapter practice data
     */
    def getChapterPracticeData(String username, Long chapterId, Date fromDate = null, Date toDate = null, Long batchId = null, boolean isDemoMode = false) {
        log.info("Service - getChapterPracticeData called with isDemoMode=${isDemoMode}")
        // Return demo data if requested
        if (isDemoMode) {
            log.info("Service - Generating demo practice data for chapterId=${chapterId}")
            def demoData = generateDemoPracticeDataForChapter(chapterId)
            log.info("Service - Demo practice data generated")
            return demoData
        }

        try {
            log.info("getChapterPracticeData called with username=${username}, chapterId=${chapterId}")

            // TODO: Implement real data retrieval from database
            // For now, we need to return empty data when not in demo mode
            // This will show the "No data available" message
            return [
                summary: [
                    totalQuestions: 0,
                    totalCorrect: 0,
                    overallAccuracy: 0
                ],
                topicDetails: [],
                timeSeriesData: [
                    labels: [],
                    practiceData: [],
                    accuracyData: []
                ],
                questionTypeData: [
                    labels: [],
                    data: []
                ]
            ]

        } catch (Exception e) {
            log.error("Error in getChapterPracticeData: ${e.message}")
            e.printStackTrace()
            throw e
        }
    }
}
