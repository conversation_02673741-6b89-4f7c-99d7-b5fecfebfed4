package com.wonderslate.report

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.log.GptLog
// import com.wonderslate.log.Quizrecorder - replaced with QuizRecMst
import com.wonderslate.prepjoy.QuizRecDtl
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.sqlutil.SafeSql
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.text.SimpleDateFormat

@Transactional
class AireportService {

    def grailsApplication
    def redisService

    /**
     * Generate demo interaction data for the dashboard
     * @return Map containing demo interaction data
     */
    private def generateDemoInteractionData() {
        // Generate demo data for subject interactions
        def subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'Computer Science', 'English']
        def subjectLabels = []
        def subjectData = []
        def subjectTotal = 0

        // Generate random data for each subject
        subjects.each { subject ->
            def count = 50 + new Random().nextInt(150)  // Random count between 50 and 200
            subjectLabels << subject
            subjectData << count
            subjectTotal += count
        }

        // Generate demo data for prompt types
        def promptTypes = ['Ask Doubt', 'Summary', 'MCQs', 'Study Tips', 'Common Mistakes']
        def promptTypeLabels = []
        def promptTypeData = []
        def promptTypeTotal = 0

        // Generate random data for each prompt type
        promptTypes.each { promptType ->
            def count = 30 + new Random().nextInt(120)  // Random count between 30 and 150
            promptTypeLabels << promptType
            promptTypeData << count
            promptTypeTotal += count
        }

        return [
            subjectData: [labels: subjectLabels, data: subjectData, total: subjectTotal],
            promptTypeData: [labels: promptTypeLabels, data: promptTypeData, total: promptTypeTotal]
        ]
    }

    /**
     * Get interaction data for the dashboard
     * @param username User's username
     * @param fromDate Start date for filtering
     * @param toDate End date for filtering
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing interaction data
     */
    def getInteractionData(String username, Date fromDate, Date toDate, Long batchId = null, boolean isDemoMode = false) {
        // Return demo data if requested
        if (isDemoMode) {
            return generateDemoInteractionData()
        }
        // Format dates for SQL query
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        String fromDateStr = sdf.format(fromDate)
        String toDateStr = sdf.format(toDate)

        // No need to build a batchFilter string since we're using a subquery

        // First, get GptLog data from wslog database
        String gptLogSql = """
            SELECT
                gl.id,
                gl.prompt_type as promptType,
                gl.res_id,
                gl.username
            FROM
                wslog.gpt_log gl
        """;

        // Add date filter
        // Add 1 day to toDate to include records created on the end date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DATE, 1);
        String adjustedToDateStr = sdf.format(calendar.getTime());
        gptLogSql += " WHERE gl.date_created BETWEEN '${fromDateStr}' AND '${adjustedToDateStr}'";

        // Apply filtering based on request parameters
        if (batchId) {
            if (batchId == 'personal') {
                // If 'personal' is selected, show only the user's own data
                gptLogSql += " AND gl.username = '${username}'";
            } else {
                // If a regular batch ID is provided, filter by batch
                gptLogSql += """
                    AND gl.username IN (
                        SELECT bud.username
                        FROM wsuser.batch_user_dtl bud
                        WHERE bud.batch_id = ${batchId}
                    )
                """
            }
        } else {
            // If no batch ID, show only the user's own data
            gptLogSql += " AND gl.username = '${username}'";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def gptLogResults = sql1.rows(gptLogSql)

        // Get resource details from default database
        def resourceIds = gptLogResults.collect { it.res_id }.unique()

        if (resourceIds.isEmpty()) {
            return [
                promptTypeData: [labels: [], data: [], total: 0],
                subjectData: [labels: [], data: [], total: 0]
            ]
        }

        String resourceSql = """
            SELECT
                rd.id as res_id,
                rd.chapter_id,
                cm.name as chapter_name,
                cm.book_id,
                btd.subject
            FROM
                resource_dtl rd
            LEFT JOIN
                chapters_mst cm ON rd.chapter_id = cm.id
            LEFT JOIN
                books_tag_dtl btd ON cm.book_id = btd.book_id
            WHERE
                rd.id IN (${resourceIds.join(',')})
        """

        def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql2 = new SafeSql(defaultDataSource)
        def resourceResults = sql2.rows(resourceSql)

        // Get batch information if needed
        def usernames = gptLogResults.collect { it.username }.unique()

        String batchSql = """
            SELECT
                bud.username,
                bud.batch_id
            FROM
                wsuser.batch_user_dtl bud
            WHERE
                bud.username IN ('${usernames.join("','")}')
        """

        def userDataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql3 = new SafeSql(userDataSource)
        def batchResults = sql3.rows(batchSql)

        // Join the data in memory
        def results = []

        gptLogResults.each { gptLog ->
            def resource = resourceResults.find { it.res_id == gptLog.res_id }
            if (resource) {
                def batchInfo = batchResults.find { it.username == gptLog.username }

                // No need to filter by batch here as it's already done in the SQL query
                results << [
                    promptType: gptLog.promptType,
                    subject: resource.subject ?: 'Unknown',
                    chapterName: resource.chapter_name ?: 'Unknown',
                    count: 1
                ]
            }
        }

        // Group and count
        def groupedByPromptAndSubject = results.groupBy { [it.promptType, it.subject, it.chapterName] }

        def processedResults = groupedByPromptAndSubject.collect { key, items ->
            [
                promptType: key[0],
                subject: key[1],
                chapterName: key[2],
                count: items.size()
            ]
        }.sort { -it.count }

        // Results are already processed above

        // Process results for visualization
        def promptTypeData = processPromptTypeData(results)
        def subjectData = processSubjectData(results)

        return [
            promptTypeData: promptTypeData,
            subjectData: subjectData
        ]
    }

    /**
     * Process prompt type data for visualization
     * @param results SQL query results
     * @return Map with formatted prompt type data
     */
    private def processPromptTypeData(def results) {
        def promptTypes = results.groupBy { it.promptType }
        def labels = []
        def data = []
        def total = 0

        promptTypes.each { promptType, entries ->
            def count = entries.sum { it.count }
            total += count
            labels.add(formatPromptType(promptType))
            data.add(count)
        }

        return [
            labels: labels,
            data: data,
            total: total
        ]
    }

    /**
     * Process subject data for visualization
     * @param results SQL query results
     * @return Map with formatted subject data
     */
    private def processSubjectData(def results) {
        def subjects = results.groupBy { it.subject }
        def labels = []
        def data = []
        def total = 0

        subjects.each { subject, entries ->
            def count = entries.sum { it.count }
            total += count
            labels.add(subject)
            data.add(count)
        }

        return [
            labels: labels,
            data: data,
            total: total
        ]
    }

    /**
     * Format prompt type for readability
     * @param promptType Raw prompt type from database
     * @return Formatted prompt type
     */
    private String formatPromptType(String promptType) {
        if (!promptType) return "Other"

        // Convert snake_case to Title Case
        return promptType.replaceAll("_", " ")
            .split(" ")
            .collect { word -> word.capitalize() }
            .join(" ")
    }

    /**
     * Generate demo practice data for the dashboard
     * @return Map containing demo practice data
     */
    private def generateDemoPracticeData() {
        // Generate demo data for subject performance
        def subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'Computer Science', 'English']
        def subjectPerformance = []

        // Generate random data for each subject
        subjects.each { subject ->
            def total = 50 + new Random().nextInt(100)  // Random total between 50 and 150
            def correct = (int)(total * (0.6 + new Random().nextDouble() * 0.3))  // 60-90% correct
            def incorrect = (int)((total - correct) * 0.7)  // Most of remaining are incorrect
            def skipped = total - correct - incorrect  // Rest are skipped
            def accuracy = (correct / total) * 100

            subjectPerformance << [
                subject: subject,
                correct: correct,
                incorrect: incorrect,
                skipped: skipped,
                total: total,
                accuracy: accuracy
            ]
        }

        // Generate time series data for the last 30 days
        def labels = []
        def correctData = []
        def incorrectData = []
        def skippedData = []

        // Create date formatter
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd")

        // Generate data for each day
        Calendar cal = Calendar.instance
        cal.add(Calendar.DAY_OF_MONTH, -30)

        31.times { day ->
            cal.add(Calendar.DAY_OF_MONTH, 1)
            labels << sdf.format(cal.time)

            // Generate random data with an upward trend for correct answers
            def dayTotal = 10 + new Random().nextInt(40) + (day / 3)  // Increasing trend
            def dayCorrect = (int)(dayTotal * (0.5 + (day / 60) + (new Random().nextDouble() * 0.2)))  // Improving accuracy
            def dayIncorrect = (int)((dayTotal - dayCorrect) * 0.7)
            def daySkipped = dayTotal - dayCorrect - dayIncorrect

            correctData << dayCorrect
            incorrectData << dayIncorrect
            skippedData << daySkipped
        }

        def timeSeriesData = [
            labels: labels,
            datasets: [
                [label: 'Correct', data: correctData, backgroundColor: 'rgba(75, 192, 192, 0.7)'],
                [label: 'Incorrect', data: incorrectData, backgroundColor: 'rgba(255, 99, 132, 0.7)'],
                [label: 'Skipped', data: skippedData, backgroundColor: 'rgba(255, 205, 86, 0.7)']
            ]
        ]

        return [
            subjectPerformance: subjectPerformance,
            timeSeriesData: timeSeriesData
        ]
    }

    /**
     * Get practice data for the dashboard
     * @param username User's username
     * @param fromDate Start date for filtering
     * @param toDate End date for filtering
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing practice data
     */
    def getPracticeData(String username, Date fromDate, Date toDate, Long batchId = null, boolean isDemoMode = false) {
        // Return demo data if requested
        if (isDemoMode) {
            return generateDemoPracticeData()
        }
        // Format dates for SQL query
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        String fromDateStr = sdf.format(fromDate)
        String toDateStr = sdf.format(toDate)

        // No need to build a batchFilter string since we're using a subquery

        // First, get QuizRecorder data from wsuser database
        String quizRecorderSql = """
            SELECT
                qr.id,
                qr.username,
                qr.date_created as quizDate,
                qr.correct_answers as correct,
                qr.incorrect_answers as incorrect,
                qr.skipped,
                qr.no_of_questions as total
            FROM
                wsuser.quiz_rec_mst qr
        """;

        // Add date filter
        // Add 1 day to toDate to include records created on the end date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DATE, 1);
        String adjustedToDateStr = sdf.format(calendar.getTime());
        quizRecorderSql += " WHERE qr.date_created BETWEEN '${fromDateStr}' AND '${adjustedToDateStr}'";

        // Apply filtering based on request parameters
        if (batchId) {
            if (batchId == 'personal') {
                // If 'personal' is selected, show only the user's own data
                quizRecorderSql += " AND qr.username = '${username}'";
            } else {
                // If a regular batch ID is provided, filter by batch
                quizRecorderSql += """
                    AND qr.username IN (
                        SELECT bud.username
                        FROM wsuser.batch_user_dtl bud
                        WHERE bud.batch_id = ${batchId}
                    )
                """
            }
        } else {
            // If no batch ID, show only the user's own data
            quizRecorderSql += " AND qr.username = '${username}'";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def quizRecorderResults = sql1.rows(quizRecorderSql)

        if (quizRecorderResults.isEmpty()) {
            return [
                subjectPerformance: [],
                timeSeriesData: [labels: [], datasets: []]
            ]
        }

        // Get quiz details from wsuser database
        def quizIds = quizRecorderResults.collect { it.id }.unique()

        String quizDetailSql = """
            SELECT
                qrd.quiz_rec_id as quizrecorderid,
                qrd.obj_id as objectivemstid
            FROM
                wsuser.quiz_rec_dtl qrd
            WHERE
                qrd.quiz_rec_id IN (${quizIds.join(',')})
        """

        def quizDetailResults = sql1.rows(quizDetailSql)

        // Get objective details from default database
        def objectiveIds = quizDetailResults.collect { it.objectivemstid }.unique()

        if (objectiveIds.isEmpty()) {
            return [
                subjectPerformance: [],
                timeSeriesData: [labels: [], datasets: []]
            ]
        }

        String objectiveSql = """
            SELECT
                om.id,
                om.subject,
                om.quiz_id
            FROM
                objective_mst om
            WHERE
                om.id IN (${objectiveIds.join(',')})
        """

        def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql2 = new SafeSql(defaultDataSource)
        def objectiveResults = sql2.rows(objectiveSql)

        // Get resource and chapter details if needed
        def resourceIds = objectiveResults.collect { it.quiz_id }.findAll { it != null }.unique()

        def resourceResults = []
        if (!resourceIds.isEmpty()) {
            String resourceSql = """
                SELECT
                    rd.id as res_id,
                    rd.chapter_id,
                    cm.name as chapter_name
                FROM
                    resource_dtl rd
                LEFT JOIN
                    chapters_mst cm ON rd.chapter_id = cm.id
                WHERE
                    rd.id IN (${resourceIds.join(',')})
            """

            resourceResults = sql2.rows(resourceSql)
        }

        // Get batch information if needed
        def usernames = quizRecorderResults.collect { it.username }.unique()

        String batchSql = """
            SELECT
                bud.username,
                bud.batch_id
            FROM
                wsuser.batch_user_dtl bud
            WHERE
                bud.username IN ('${usernames.join("','")}')
        """

        def userDataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql3 = new SafeSql(userDataSource)
        def batchResults = sql3.rows(batchSql)

        // Join the data in memory
        def results = []

        quizRecorderResults.each { quizRecorder ->
            def quizDetails = quizDetailResults.findAll { it.quizrecorderid == quizRecorder.id }

            quizDetails.each { quizDetail ->
                def objective = objectiveResults.find { it.id == quizDetail.objectivemstid }

                if (objective) {
                    def resource = resourceResults.find { it.res_id == objective.quiz_id }
                    def batchInfo = batchResults.find { it.username == quizRecorder.username }

                    // No need to filter by batch here as it's already done in the SQL query
                    results << [
                        username: quizRecorder.username,
                        quizDate: quizRecorder.quizDate,
                        correct: quizRecorder.correct,
                        incorrect: quizRecorder.incorrect,
                        skipped: quizRecorder.skipped,
                        total: quizRecorder.total,
                        subject: objective.subject ?: 'Unknown',
                        chapterName: resource?.chapter_name ?: 'Unknown'
                    ]
                }
            }
        }

        // Results are already processed above

        // Process results for visualization
        def subjectPerformance = processSubjectPerformance(results)
        def timeSeriesData = processTimeSeriesData(results, fromDate, toDate)

        return [
            subjectPerformance: subjectPerformance,
            timeSeriesData: timeSeriesData
        ]
    }

    /**
     * Process subject performance data for visualization
     * @param results SQL query results
     * @return Map with formatted subject performance data
     */
    private def processSubjectPerformance(def results) {
        def subjects = results.groupBy { it.subject }
        def data = []

        subjects.each { subject, entries ->
            def totalCorrect = entries.sum { it.correct ?: 0 }
            def totalIncorrect = entries.sum { it.incorrect ?: 0 }
            def totalSkipped = entries.sum { it.skipped ?: 0 }
            def totalQuestions = entries.sum { it.total ?: 0 }

            def accuracy = totalQuestions > 0 ? (totalCorrect / totalQuestions) * 100 : 0

            data.add([
                subject: subject,
                correct: totalCorrect,
                incorrect: totalIncorrect,
                skipped: totalSkipped,
                total: totalQuestions,
                accuracy: Math.round(accuracy * 100) / 100 // Round to 2 decimal places
            ])
        }

        return data
    }

    /**
     * Process time series data for visualization
     * @param results SQL query results
     * @param fromDate Start date for filtering
     * @param toDate End date for filtering
     * @return Map with formatted time series data
     */
    private def processTimeSeriesData(def results, Date fromDate, Date toDate) {
        // Group results by date
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        def dateGrouped = results.groupBy { sdf.format(it.quizDate) }

        // Generate all dates in range
        def allDates = []
        def currentDate = fromDate
        while (currentDate <= toDate) {
            allDates.add(sdf.format(currentDate))
            currentDate = currentDate + 1
        }

        // Prepare data for chart
        def labels = []
        def correctData = []
        def incorrectData = []
        def skippedData = []

        allDates.each { dateStr ->
            labels.add(dateStr)

            def entries = dateGrouped[dateStr]
            if (entries) {
                correctData.add(entries.sum { it.correct ?: 0 })
                incorrectData.add(entries.sum { it.incorrect ?: 0 })
                skippedData.add(entries.sum { it.skipped ?: 0 })
            } else {
                correctData.add(0)
                incorrectData.add(0)
                skippedData.add(0)
            }
        }

        return [
            labels: labels,
            datasets: [
                [
                    label: "Correct",
                    data: correctData,
                    backgroundColor: "rgba(75, 192, 192, 0.2)",
                    borderColor: "rgba(75, 192, 192, 1)"
                ],
                [
                    label: "Incorrect",
                    data: incorrectData,
                    backgroundColor: "rgba(255, 99, 132, 0.2)",
                    borderColor: "rgba(255, 99, 132, 1)"
                ],
                [
                    label: "Skipped",
                    data: skippedData,
                    backgroundColor: "rgba(255, 206, 86, 0.2)",
                    borderColor: "rgba(255, 206, 86, 1)"
                ]
            ]
        ]
    }

    /**
     * Get batches for a user based on their role
     * @param username User's username
     * @param siteId Site ID
     * @return List of batches
     */
    def getBatchesForUser(String username, Integer siteId) {
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)

        // Check if user is a Manager
        def managerBatchSql = """
            SELECT bud.batch_id
            FROM wsuser.batch_user_dtl bud
            WHERE bud.username = '${username}'
            AND bud.user_type = 'Manager'
        """

        def managerResults = sql1.rows(managerBatchSql)

        if (managerResults) {
            // User is a Manager, get all batches for their institute
            def managerBatchId = managerResults[0].batch_id

            def instituteSql = """
                SELECT cbd.conducted_by
                FROM wsuser.course_batches_dtl cbd
                WHERE cbd.id = ${managerBatchId}
            """

            def instituteResults = sql1.rows(instituteSql)
            def instituteId = instituteResults[0].conducted_by

            def allBatchesSql = """
                SELECT cbd.id, cbd.name
                FROM wsuser.course_batches_dtl cbd
                WHERE cbd.conducted_by = ${instituteId}
                AND cbd.status = 'active'
                ORDER BY cbd.name
            """

            def batchResults = sql1.rows(allBatchesSql)

            return batchResults.collect { batch ->
                [id: batch.id, name: "Default".equals(batch.name)?"Institution":batch.name]
            }
        } else {
            // Check if user is an Instructor
            def instructorBatchSql = """
                SELECT cbd.id, cbd.name
                FROM wsuser.batch_user_dtl bud
                JOIN wsuser.course_batches_dtl cbd ON bud.batch_id = cbd.id
                WHERE bud.username = '${username}'
                AND bud.user_type = 'Instructor'
                AND cbd.status = 'active'
                ORDER BY cbd.name
            """

            def instructorResults = sql1.rows(instructorBatchSql)

            if (instructorResults) {
                // User is an Instructor
                return instructorResults.collect { batch ->
                    [id: batch.id, name: batch.name]
                }
            } else {
                // User is an Individual
                return []
            }
        }
    }

    /**
     * Get user role based on institutes
     * @param username User's username
     * @param siteId Site ID
     * @return User role (Individual/Instructor/Manager)
     */
    def getUserRole(String username, Integer siteId) {
        if (!username) {
            return 'Individual'
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)

        def roleSql = """
            SELECT bud.user_type
            FROM wsuser.batch_user_dtl bud
            WHERE bud.username = '${username}'
            AND bud.user_type IN ('Manager', 'Instructor')
        """

        def results = sql1.rows(roleSql)

        if (results) {
            if (results.any { it.user_type == 'Manager' }) {
                return 'Manager'
            } else {
                return 'Instructor'
            }
        } else {
            return 'Individual'
        }
    }

    /**
     * Generate demo books data for a specific subject
     * @param subject Subject to generate data for
     * @return Map containing demo book data
     */
    private def generateDemoBooksData(String subject) {
        // Define book titles based on subject
        def bookTitles = [
            'Mathematics': ['Algebra Fundamentals', 'Calculus Made Easy', 'Geometry Essentials', 'Statistics and Probability', 'Trigonometry Basics'],
            'Physics': ['Mechanics and Motion', 'Electricity and Magnetism', 'Thermodynamics', 'Quantum Physics', 'Optics and Waves'],
            'Chemistry': ['Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Biochemistry', 'Analytical Chemistry'],
            'Biology': ['Cell Biology', 'Genetics and Evolution', 'Human Anatomy', 'Ecology', 'Microbiology'],
            'Computer Science': ['Programming Fundamentals', 'Data Structures', 'Algorithms', 'Database Systems', 'Artificial Intelligence'],
            'English': ['Grammar and Composition', 'Literature Analysis', 'Creative Writing', 'Communication Skills', 'Vocabulary Building']
        ]

        // Get books for the selected subject or use default if not found
        def books = bookTitles[subject] ?: ['Book 1', 'Book 2', 'Book 3', 'Book 4', 'Book 5']

        def bookLabels = []
        def bookData = []
        def bookDetails = []
        def total = 0

        // Generate random data for each book
        books.eachWithIndex { book, index ->
            def count = 20 + new Random().nextInt(80)  // Random count between 20 and 100
            def correctCount = (int)(count * (0.6 + new Random().nextDouble() * 0.3))  // 60-90% correct
            def totalQuestions = count
            def accuracy = (correctCount / totalQuestions) * 100

            total += count
            bookLabels << book
            bookData << count
            bookDetails << [
                id: index + 1,
                title: book,
                count: count,
                correct: correctCount,
                total: totalQuestions,
                accuracy: Math.round(accuracy * 100) / 100  // Round to 2 decimal places
            ]
        }

        return [
            bookData: [
                labels: bookLabels,
                data: bookData,
                total: total
            ],
            bookDetails: bookDetails
        ]
    }

    /**
     * Get books for a specific subject
     * @param username User's username
     * @param fromDate Start date for filtering
     * @param toDate End date for filtering
     * @param subject Subject to filter by
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing book data
     */
    def getBooksBySubject(String username, Date fromDate, Date toDate, String subject, def batchId = null, boolean isDemoMode = false) {
        // Return demo data if requested
        if (isDemoMode) {
            return generateDemoBooksData(subject)
        }
        // Format dates for SQL query
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        String fromDateStr = sdf.format(fromDate)
        String toDateStr = sdf.format(toDate)

        // First, get GptLog data from wslog database
        String gptLogSql = """
            SELECT
                gl.id,
                gl.prompt_type as promptType,
                gl.res_id,
                gl.username
            FROM
                wslog.gpt_log gl
        """;

        // Add date filter
        // Add 1 day to toDate to include records created on the end date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DATE, 1);
        String adjustedToDateStr = sdf.format(calendar.getTime());
        gptLogSql += " WHERE gl.date_created BETWEEN '${fromDateStr}' AND '${adjustedToDateStr}'";

        // Apply filtering based on request parameters
        if (batchId) {
            if (batchId == 'personal') {
                // If 'personal' is selected, show only the user's own data
                gptLogSql += " AND gl.username = '${username}'";
            } else {
                // If a regular batch ID is provided, filter by batch
                gptLogSql += """
                    AND gl.username IN (
                        SELECT bud.username
                        FROM wsuser.batch_user_dtl bud
                        WHERE bud.batch_id = ${batchId}
                    )
                """
            }
        } else {
            // If no batch ID, show only the user's own data
            gptLogSql += " AND gl.username = '${username}'";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def gptLogResults = sql1.rows(gptLogSql)

        // Get resource details from default database
        def resourceIds = gptLogResults.collect { it.res_id }.unique()

        if (resourceIds.isEmpty()) {
            return [
                bookData: [labels: [], data: [], total: 0],
                bookDetails: []
            ]
        }

        String resourceSql = """
            SELECT
                rd.id as res_id,
                rd.chapter_id,
                cm.name as chapter_name,
                cm.book_id,
                bm.title as book_title,
                btd.subject
            FROM
                resource_dtl rd
            LEFT JOIN
                chapters_mst cm ON rd.chapter_id = cm.id
            LEFT JOIN
                books_mst bm ON cm.book_id = bm.id
            LEFT JOIN
                books_tag_dtl btd ON cm.book_id = btd.book_id
            WHERE
                rd.id IN (${resourceIds.join(',')}) AND btd.subject = '${subject}'
        """

        def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql2 = new SafeSql(defaultDataSource)
        def resourceResults = sql2.rows(resourceSql)

        // Join the data in memory
        def results = []

        gptLogResults.each { gptLog ->
            def resource = resourceResults.find { it.res_id == gptLog.res_id }
            if (resource) {
                results << [
                    promptType: gptLog.promptType,
                    bookId: resource.book_id,
                    bookTitle: resource.book_title ?: 'Unknown',
                    chapterName: resource.chapter_name ?: 'Unknown',
                    count: 1
                ]
            }
        }

        // Group and count by book
        def groupedByBook = results.groupBy { it.bookId }

        def bookLabels = []
        def bookData = []
        def bookDetails = []
        def total = 0

        groupedByBook.each { bookId, items ->
            def count = items.size()
            total += count
            bookLabels.add(items[0].bookTitle)
            bookData.add(count)
            bookDetails.add([
                id: bookId,
                title: items[0].bookTitle,
                count: count
            ])
        }

        return [
            bookData: [
                labels: bookLabels,
                data: bookData,
                total: total
            ],
            bookDetails: bookDetails
        ]
    }

    /**
     * Get practice books for a specific subject
     * @param username User's username
     * @param fromDate Start date for filtering
     * @param toDate End date for filtering
     * @param subject Subject to filter by
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing book data with practice metrics
     */
    def getPracticeBooksBySubject(String username, Date fromDate, Date toDate, String subject, def batchId = null, boolean isDemoMode = false) {
        // Return demo data if requested
        if (isDemoMode) {
            return generateDemoBooksData(subject)  // Reuse the same demo data generator
        }
        println "getPracticeBooksBySubject called with username=${username}, subject=${subject}, fromDate=${fromDate}, toDate=${toDate}, batchId=${batchId}"

        try {
            // Format dates for SQL query
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            String fromDateStr = sdf.format(fromDate)
            String toDateStr = sdf.format(toDate)

            println "Formatted dates: fromDateStr=${fromDateStr}, toDateStr=${toDateStr}"

        // First, get QuizRecorder data from wsuser database
        String quizRecorderSql = """
            SELECT
                qr.id,
                qr.username,
                qr.date_created as quizDate,
                qr.correct_answers as correct,
                qr.incorrect_answers as incorrect,
                qr.skipped,
                qr.no_of_questions as total
            FROM
                wsuser.quiz_rec_mst qr
        """;

        // Add date range filter
        // Add 1 day to toDate to include records created on the end date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DATE, 1);
        String adjustedToDateStr = sdf.format(calendar.getTime());
        quizRecorderSql += """
            WHERE
                qr.date_created BETWEEN '${fromDateStr}' AND '${adjustedToDateStr}'
        """

        // Add username filter if not a batch query
        if (batchId == null || batchId == 'personal') {
            quizRecorderSql += """
                AND qr.username = '${username}'
            """
        } else {
            // Add batch filter using a subquery
            quizRecorderSql += """
                AND qr.username IN (
                    SELECT bud.username
                    FROM wsuser.batch_user_dtl bud
                    WHERE bud.batch_id = ${batchId}
                )
            """
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def quizRecorderResults = sql1.rows(quizRecorderSql)

        if (quizRecorderResults.isEmpty()) {
            return [
                bookData: [labels: [], data: [], total: 0],
                bookDetails: []
            ]
        }

        // Get quiz details from wsuser database
        def quizIds = quizRecorderResults.collect { it.id }.unique()

        String quizDetailSql = """
            SELECT
                qrd.quiz_rec_id as quizrecorderid,
                qrd.obj_id as objectivemstid
            FROM
                wsuser.quiz_rec_dtl qrd
            WHERE
                qrd.quiz_rec_id IN (${quizIds.join(',')})
        """

        def quizDetailResults = sql1.rows(quizDetailSql)

        // Get objective details from default database
        def objectiveIds = quizDetailResults.collect { it.objectivemstid }.unique()

        if (objectiveIds.isEmpty()) {
            return [
                bookData: [labels: [], data: [], total: 0],
                bookDetails: []
            ]
        }

        String objectiveSql = """
            SELECT
                om.id,
                om.subject,
                om.quiz_id
            FROM
                objective_mst om
            WHERE
                om.id IN (${objectiveIds.join(',')})
        """

        def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql2 = new SafeSql(defaultDataSource)
        def objectiveResults = sql2.rows(objectiveSql)

        // Get resource and chapter details if needed
        def resourceIds = objectiveResults.collect { it.quiz_id }.findAll { it != null }.unique()

        if (resourceIds.isEmpty()) {
            return [
                bookData: [labels: [], data: [], total: 0],
                bookDetails: []
            ]
        }

        String resourceSql = """
            SELECT
                rd.id as res_id,
                rd.chapter_id,
                cm.name as chapter_name,
                cm.book_id,
                bm.title as book_title
            FROM
                resource_dtl rd
            LEFT JOIN
                chapters_mst cm ON rd.chapter_id = cm.id
            LEFT JOIN
                books_mst bm ON cm.book_id = bm.id
            WHERE
                rd.id IN (${resourceIds.join(',')})
        """

        def resourceResults = sql2.rows(resourceSql)

        // Join the data in memory
        def results = []

        quizRecorderResults.each { quizRecorder ->
            def quizDetails = quizDetailResults.findAll { it.quizrecorderid == quizRecorder.id }

            quizDetails.each { quizDetail ->
                def objective = objectiveResults.find { it.id == quizDetail.objectivemstid }

                if (objective) {
                    def resource = resourceResults.find { it.res_id == objective.quiz_id }
                    if (resource) {
                        results << [
                            correct: quizRecorder.correct,
                            incorrect: quizRecorder.incorrect,
                            skipped: quizRecorder.skipped,
                            total: quizRecorder.total,
                            bookId: resource.book_id,
                            bookTitle: resource.book_title ?: 'Unknown',
                            chapterName: resource.chapter_name ?: 'Unknown',
                            count: 1
                        ]
                    }
                }
            }
        }

        // Group and count by book
        def groupedByBook = results.groupBy { it.bookId }

        def bookLabels = []
        def bookData = []
        def bookDetails = []
        def total = 0

        groupedByBook.each { bookId, items ->
            def count = items.size()
            def correctCount = items.sum { it.correct ?: 0 }
            def totalQuestions = items.sum { it.total ?: 0 }
            def accuracy = totalQuestions > 0 ? (correctCount / totalQuestions) * 100 : 0

            total += count
            bookLabels.add(items[0].bookTitle)
            bookData.add(count)
            bookDetails.add([
                id: bookId,
                title: items[0].bookTitle,
                count: count,
                correct: correctCount,
                total: totalQuestions,
                accuracy: Math.round(accuracy * 100) / 100 // Round to 2 decimal places
            ])
        }

        def result = [
            bookData: [
                labels: bookLabels,
                data: bookData,
                total: total
            ],
            bookDetails: bookDetails
        ]

        println "Returning result: ${result}"
        return result
        } catch (Exception e) {
            println "Error in getPracticeBooksBySubject: ${e.message}"
            e.printStackTrace()
            throw e
        }
    }

    /**
     * Generate demo chapters data for a specific book
     * @param bookId Book ID to generate data for
     * @return Map containing demo chapter data
     */
    private def generateDemoChaptersData(Long bookId) {
        // Generate chapter names (5-10 chapters)
        def chapterCount = 5 + new Random().nextInt(6)  // Random between 5 and 10 chapters
        def chapterLabels = []
        def chapterData = []
        def chapterDetails = []
        def total = 0

        chapterCount.times { index ->
            def chapterName = "Chapter ${index + 1}"
            def count = 10 + new Random().nextInt(40)  // Random count between 10 and 50
            def correctCount = (int)(count * (0.6 + new Random().nextDouble() * 0.3))  // 60-90% correct
            def totalQuestions = count
            def accuracy = (correctCount / totalQuestions) * 100

            total += count
            chapterLabels << chapterName
            chapterData << count
            chapterDetails << [
                id: index + 1,
                name: chapterName,
                count: count,
                correct: correctCount,
                total: totalQuestions,
                accuracy: Math.round(accuracy * 100) / 100  // Round to 2 decimal places
            ]
        }

        return [
            labels: chapterLabels,
            data: chapterData,
            total: total,
            details: chapterDetails
        ]
    }

    /**
     * Get chapters for a specific book
     * @param username User's username
     * @param fromDate Start date for filtering
     * @param toDate End date for filtering
     * @param bookId Book ID to filter by
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing chapter data
     */
    def getChaptersByBook(String username, Date fromDate, Date toDate, Long bookId, def batchId = null, boolean isDemoMode = false) {
        // Return demo data if requested
        if (isDemoMode) {
            return generateDemoChaptersData(bookId)
        }
        // Format dates for SQL query
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        String fromDateStr = sdf.format(fromDate)
        String toDateStr = sdf.format(toDate)

        // First, get GptLog data from wslog database
        String gptLogSql = """
            SELECT
                gl.id,
                gl.prompt_type as promptType,
                gl.res_id,
                gl.username
            FROM
                wslog.gpt_log gl
        """;

        // Add date filter
        // Add 1 day to toDate to include records created on the end date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DATE, 1);
        String adjustedToDateStr = sdf.format(calendar.getTime());
        gptLogSql += " WHERE gl.date_created BETWEEN '${fromDateStr}' AND '${adjustedToDateStr}'";

        // Apply filtering based on request parameters
        if (batchId) {
            if (batchId == 'personal') {
                // If 'personal' is selected, show only the user's own data
                gptLogSql += " AND gl.username = '${username}'";
            } else {
                // If a regular batch ID is provided, filter by batch
                gptLogSql += """
                    AND gl.username IN (
                        SELECT bud.username
                        FROM wsuser.batch_user_dtl bud
                        WHERE bud.batch_id = ${batchId}
                    )
                """
            }
        } else {
            // If no batch ID, show only the user's own data
            gptLogSql += " AND gl.username = '${username}'";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new SafeSql(dataSource)
        def gptLogResults = sql1.rows(gptLogSql)

        // Get resource details from default database
        def resourceIds = gptLogResults.collect { it.res_id }.unique()

        if (resourceIds.isEmpty()) {
            return [
                chapterData: [labels: [], data: [], total: 0],
                chapterDetails: []
            ]
        }

        String resourceSql = """
            SELECT
                rd.id as res_id,
                rd.chapter_id,
                cm.name as chapter_name,
                cm.book_id,
                bm.title as book_title
            FROM
                resource_dtl rd
            LEFT JOIN
                chapters_mst cm ON rd.chapter_id = cm.id
            LEFT JOIN
                books_mst bm ON cm.book_id = bm.id
            WHERE
                rd.id IN (${resourceIds.join(',')}) AND cm.book_id = ${bookId}
        """

        def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql2 = new SafeSql(defaultDataSource)
        def resourceResults = sql2.rows(resourceSql)

        // Join the data in memory
        def results = []

        gptLogResults.each { gptLog ->
            def resource = resourceResults.find { it.res_id == gptLog.res_id }
            if (resource) {
                results << [
                    promptType: gptLog.promptType,
                    chapterId: resource.chapter_id,
                    chapterName: resource.chapter_name ?: 'Unknown',
                    bookTitle: resource.book_title ?: 'Unknown',
                    count: 1
                ]
            }
        }

        // Group and count by chapter
        def groupedByChapter = results.groupBy { it.chapterId }

        def chapterLabels = []
        def chapterData = []
        def chapterDetails = []
        def total = 0

        groupedByChapter.each { chapterId, items ->
            def count = items.size()
            total += count
            chapterLabels.add(items[0].chapterName)
            chapterData.add(count)
            chapterDetails.add([
                id: chapterId,
                name: items[0].chapterName,
                count: count
            ])
        }

        return [
            chapterData: [
                labels: chapterLabels,
                data: chapterData,
                total: total
            ],
            chapterDetails: chapterDetails,
            bookTitle: resourceResults.size() > 0 ? resourceResults[0].book_title : 'Unknown'
        ]
    }

    /**
     * Get practice chapters for a specific book
     * @param username User's username
     * @param fromDate Start date for filtering
     * @param toDate End date for filtering
     * @param bookId Book ID to filter by
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing chapter data with practice metrics
     */
    def getPracticeChaptersByBook(String username, Date fromDate, Date toDate, Long bookId, def batchId = null, boolean isDemoMode = false) {
        // Return demo data if requested
        if (isDemoMode) {
            return generateDemoChaptersData(bookId)  // Reuse the same demo data generator
        }
        println "getPracticeChaptersByBook called with username=${username}, bookId=${bookId}, fromDate=${fromDate}, toDate=${toDate}, batchId=${batchId}"

        try {
            // Format dates for SQL query
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            String fromDateStr = sdf.format(fromDate)
            String toDateStr = sdf.format(toDate)

            println "Formatted dates: fromDateStr=${fromDateStr}, toDateStr=${toDateStr}"

        // First, get QuizRecorder data from wsuser database
        String quizRecorderSql = """
            SELECT
                qr.id,
                qr.username,
                qr.date_created as quizDate,
                qr.correct_answers as correct,
                qr.incorrect_answers as incorrect,
                qr.skipped,
                qr.no_of_questions as total
            FROM
                wsuser.quiz_rec_mst qr
        """;

        // Add date range filter
        // Add 1 day to toDate to include records created on the end date
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DATE, 1);
        String adjustedToDateStr = sdf.format(calendar.getTime());
        quizRecorderSql += """
            WHERE
                qr.date_created BETWEEN '${fromDateStr}' AND '${adjustedToDateStr}'
        """

        // Add username filter if not a batch query
        if (batchId == null || batchId == 'personal') {
            quizRecorderSql += """
                AND qr.username = '${username}'
            """
        } else {
            // Add batch filter using a subquery
            quizRecorderSql += """
                AND qr.username IN (
                    SELECT bud.username
                    FROM wsuser.batch_user_dtl bud
                    WHERE bud.batch_id = ${batchId}
                )
            """
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def quizRecorderResults = sql1.rows(quizRecorderSql)

        if (quizRecorderResults.isEmpty()) {
            return [
                chapterData: [labels: [], data: [], total: 0],
                chapterDetails: []
            ]
        }

        // Get quiz details from wsuser database
        def quizIds = quizRecorderResults.collect { it.id }.unique()

        String quizDetailSql = """
            SELECT
                qrd.quiz_rec_id as quizrecorderid,
                qrd.obj_id as objectivemstid
            FROM
                wsuser.quiz_rec_dtl qrd
            WHERE
                qrd.quiz_rec_id IN (${quizIds.join(',')})
        """

        def quizDetailResults = sql1.rows(quizDetailSql)

        // Get objective details from default database
        def objectiveIds = quizDetailResults.collect { it.objectivemstid }.unique()

        if (objectiveIds.isEmpty()) {
            return [
                chapterData: [labels: [], data: [], total: 0],
                chapterDetails: []
            ]
        }

        String objectiveSql = """
            SELECT
                om.id,
                om.subject,
                om.quiz_id
            FROM
                objective_mst om
            WHERE
                om.id IN (${objectiveIds.join(',')})
        """

        def defaultDataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql2 = new SafeSql(defaultDataSource)
        def objectiveResults = sql2.rows(objectiveSql)

        // Get resource and chapter details if needed
        def resourceIds = objectiveResults.collect { it.quiz_id }.findAll { it != null }.unique()

        if (resourceIds.isEmpty()) {
            return [
                chapterData: [labels: [], data: [], total: 0],
                chapterDetails: []
            ]
        }

        String resourceSql = """
            SELECT
                rd.id as res_id,
                rd.chapter_id,
                cm.name as chapter_name,
                cm.book_id,
                bm.title as book_title
            FROM
                resource_dtl rd
            LEFT JOIN
                chapters_mst cm ON rd.chapter_id = cm.id
            LEFT JOIN
                books_mst bm ON cm.book_id = bm.id
            WHERE
                rd.id IN (${resourceIds.join(',')}) AND cm.book_id = ${bookId}
        """

        def resourceResults = sql2.rows(resourceSql)

        // Join the data in memory
        def results = []

        quizRecorderResults.each { quizRecorder ->
            def quizDetails = quizDetailResults.findAll { it.quizrecorderid == quizRecorder.id }

            quizDetails.each { quizDetail ->
                def objective = objectiveResults.find { it.id == quizDetail.objectivemstid }

                if (objective) {
                    def resource = resourceResults.find { it.res_id == objective.quiz_id }
                    if (resource) {
                        results << [
                            correct: quizRecorder.correct,
                            incorrect: quizRecorder.incorrect,
                            skipped: quizRecorder.skipped,
                            total: quizRecorder.total,
                            chapterId: resource.chapter_id,
                            chapterName: resource.chapter_name ?: 'Unknown',
                            bookTitle: resource.book_title ?: 'Unknown',
                            count: 1
                        ]
                    }
                }
            }
        }

        // Group and count by chapter
        def groupedByChapter = results.groupBy { it.chapterId }

        def chapterLabels = []
        def chapterData = []
        def chapterDetails = []
        def total = 0

        groupedByChapter.each { chapterId, items ->
            def count = items.size()
            def correctCount = items.sum { it.correct ?: 0 }
            def totalQuestions = items.sum { it.total ?: 0 }
            def accuracy = totalQuestions > 0 ? (correctCount / totalQuestions) * 100 : 0

            total += count
            chapterLabels.add(items[0].chapterName)
            chapterData.add(count)
            chapterDetails.add([
                id: chapterId,
                name: items[0].chapterName,
                count: count,
                correct: correctCount,
                total: totalQuestions,
                accuracy: Math.round(accuracy * 100) / 100 // Round to 2 decimal places
            ])
        }

        def result = [
            chapterData: [
                labels: chapterLabels,
                data: chapterData,
                total: total
            ],
            chapterDetails: chapterDetails,
            bookTitle: resourceResults.size() > 0 ? resourceResults[0].book_title : 'Unknown'
        ]

        println "Returning result: ${result}"
        return result
        } catch (Exception e) {
            println "Error in getPracticeChaptersByBook: ${e.message}"
            e.printStackTrace()
            throw e
        }
    }

    /**
     * Generate demo learning progress data
     * @return Map containing demo learning progress data
     */
    private def generateDemoLearningProgressData() {
        // Generate overall accuracy data
        def currentAccuracy = 65 + new Random().nextInt(25)  // Random between 65% and 90%
        def previousAccuracy = currentAccuracy - (5 + new Random().nextInt(15))  // Previous accuracy 5-20% lower
        def accuracyChange = currentAccuracy - previousAccuracy

        // Generate activity level data
        def currentActivity = 100 + new Random().nextInt(150)  // Random between 100 and 250 activities
        def previousActivity = currentActivity - (10 + new Random().nextInt(50))  // Previous activity 10-60 lower
        def activityChange = ((currentActivity - previousActivity) / previousActivity) * 100

        // Generate subject progress data
        def subjects = ['Mathematics', 'Physics', 'Chemistry', 'Biology', 'Computer Science', 'English']
        def subjectProgress = []

        subjects.each { subject ->
            def currentSubjectAccuracy = 60 + new Random().nextInt(30)  // Random between 60% and 90%
            def previousSubjectAccuracy = currentSubjectAccuracy - (new Random().nextInt(20) - 5)  // Previous accuracy -5% to 15% different
            def improvement = currentSubjectAccuracy - previousSubjectAccuracy

            def total = 30 + new Random().nextInt(70)  // Random total between 30 and 100
            def correct = (int)(total * (currentSubjectAccuracy / 100))
            def incorrect = (int)((total - correct) * 0.8)
            def skipped = total - correct - incorrect

            subjectProgress << [
                subject: subject,
                currentAccuracy: currentSubjectAccuracy,
                previousAccuracy: previousSubjectAccuracy,
                improvement: improvement,
                status: getImprovementStatus(improvement),
                correct: correct,
                incorrect: incorrect,
                skipped: skipped,
                total: total
            ]
        }

        // Sort by improvement (descending)
        subjectProgress = subjectProgress.sort { -it.improvement }

        return [
            overallAccuracy: [
                current: currentAccuracy,
                previous: previousAccuracy,
                change: accuracyChange,
                trend: accuracyChange >= 0 ? 'Improving' : 'Declining'
            ],
            activityLevel: [
                current: currentActivity,
                previous: previousActivity,
                percentChange: Math.round(activityChange * 100) / 100,
                trend: activityChange >= 0 ? 'Increasing' : 'Decreasing'
            ],
            subjectProgress: subjectProgress
        ]
    }

    /**
     * Get learning progress metrics for a user
     * @param username User's username
     * @param fromDate End date for current period
     * @param days Number of days to include in current period
     * @param batchId Optional batch ID for filtering (null for all batches)
     * @param isDemoMode Whether to return demo data
     * @return Map containing progress metrics
     */
    def getLearningProgress(String username, Date toDate, Integer days, def batchId = null, boolean isDemoMode = false) {
        // Return demo data if requested
        if (isDemoMode) {
            return generateDemoLearningProgressData()
        }
        // Calculate date ranges for current and previous periods
        Calendar cal = Calendar.instance
        cal.time = toDate
        Date currentToDate = cal.time

        cal.add(Calendar.DAY_OF_MONTH, -days)
        Date currentFromDate = cal.time

        cal.add(Calendar.DAY_OF_MONTH, -days)
        Date previousToDate = cal.time

        cal.add(Calendar.DAY_OF_MONTH, -days)
        Date previousFromDate = cal.time

        println "Date ranges: Current period: ${currentFromDate} to ${currentToDate}, Previous period: ${previousFromDate} to ${previousToDate}"

        // Convert batchId to Long if it's not 'personal'
        Long batchIdLong = null
        if (batchId) {
            if (batchId instanceof String && batchId != 'personal') {
                try {
                    batchIdLong = Long.parseLong(batchId)
                } catch (NumberFormatException e) {
                    println "Error converting batchId to Long: ${e.message}"
                }
            } else if (batchId instanceof Long) {
                batchIdLong = batchId
            } else if (batchId == 'personal') {
                batchIdLong = null
            }
        }

        println "Using batchId: ${batchIdLong} (original: ${batchId})"

        // Get practice data for both time periods
        def currentData = getPracticeData(username, currentFromDate, currentToDate, batchIdLong)
        def previousData = getPracticeData(username, previousFromDate, previousToDate, batchIdLong)

        // Calculate improvement metrics
        def progressMetrics = [:]

        // Overall accuracy change
        def currentAccuracy = calculateOverallAccuracy(currentData)
        def previousAccuracy = calculateOverallAccuracy(previousData)
        progressMetrics.accuracyChange = currentAccuracy - previousAccuracy
        progressMetrics.currentAccuracy = currentAccuracy
        progressMetrics.previousAccuracy = previousAccuracy

        // Most improved subject
        progressMetrics.subjectProgress = calculateSubjectProgress(previousData, currentData)

        // Activity level change
        progressMetrics.activityChange = calculateActivityChange(previousData, currentData)

        // Time period information
        progressMetrics.currentPeriod = [from: currentFromDate, to: currentToDate]
        progressMetrics.previousPeriod = [from: previousFromDate, to: previousToDate]

        return progressMetrics
    }

    /**
     * Calculate overall accuracy from practice data
     * @param practiceData Practice data from getPracticeData method
     * @return Overall accuracy percentage
     */
    private def calculateOverallAccuracy(def practiceData) {
        def totalCorrect = 0
        def totalQuestions = 0

        practiceData.subjectPerformance.each { subject ->
            totalCorrect += subject.correct ?: 0
            totalQuestions += (subject.correct ?: 0) + (subject.incorrect ?: 0) + (subject.skipped ?: 0)
        }

        return totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100 * 100) / 100 : 0
    }

    /**
     * Calculate subject progress by comparing previous and current data
     * @param previousData Practice data from previous period
     * @param currentData Practice data from current period
     * @return List of subjects with improvement metrics
     */
    private def calculateSubjectProgress(def previousData, def currentData) {
        def subjectProgress = []

        // Map previous data by subject for easy lookup
        def previousBySubject = previousData.subjectPerformance.collectEntries {
            [(it.subject): it]
        }

        currentData.subjectPerformance.each { current ->
            def previous = previousBySubject[current.subject]
            if (previous) {
                def currentAccuracy = current.accuracy ?: 0
                def previousAccuracy = previous.accuracy ?: 0
                def improvement = currentAccuracy - previousAccuracy

                subjectProgress << [
                    subject: current.subject,
                    currentAccuracy: currentAccuracy,
                    previousAccuracy: previousAccuracy,
                    improvement: improvement,
                    status: getImprovementStatus(improvement),
                    correct: current.correct ?: 0,
                    incorrect: current.incorrect ?: 0,
                    skipped: current.skipped ?: 0,
                    total: current.total ?: 0
                ]
            } else {
                // New subject, no previous data
                subjectProgress << [
                    subject: current.subject,
                    currentAccuracy: current.accuracy ?: 0,
                    previousAccuracy: 0,
                    improvement: current.accuracy ?: 0,
                    status: "New",
                    correct: current.correct ?: 0,
                    incorrect: current.incorrect ?: 0,
                    skipped: current.skipped ?: 0,
                    total: current.total ?: 0
                ]
            }
        }

        // Sort by improvement (descending)
        return subjectProgress.sort { -it.improvement }
    }

    /**
     * Get improvement status based on accuracy change
     * @param improvement Accuracy improvement percentage
     * @return Status string
     */
    private String getImprovementStatus(def improvement) {
        if (improvement >= 10) return "Significant Improvement"
        if (improvement > 0) return "Improving"
        if (improvement == 0) return "Stable"
        return "Needs Focus"
    }

    /**
     * Calculate activity change between two periods
     * @param previousData Practice data from previous period
     * @param currentData Practice data from current period
     * @return Activity change metrics
     */
    private def calculateActivityChange(def previousData, def currentData) {
        def currentTotal = currentData.subjectPerformance.sum { (it.correct ?: 0) + (it.incorrect ?: 0) + (it.skipped ?: 0) } ?: 0
        def previousTotal = previousData.subjectPerformance.sum { (it.correct ?: 0) + (it.incorrect ?: 0) + (it.skipped ?: 0) } ?: 0

        def percentChange = previousTotal > 0 ?
            Math.round(((currentTotal - previousTotal) / previousTotal) * 100 * 100) / 100 :
            (currentTotal > 0 ? 100 : 0)

        return [
            currentPeriodCount: currentTotal,
            previousPeriodCount: previousTotal,
            percentChange: percentChange,
            trend: percentChange > 0 ? "Increasing" : (percentChange < 0 ? "Decreasing" : "Stable")
        ]
    }


}
