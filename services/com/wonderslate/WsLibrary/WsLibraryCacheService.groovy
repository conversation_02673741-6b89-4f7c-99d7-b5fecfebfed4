package com.wonderslate.WsLibrary

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibraryController
import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

@Transactional
class WsLibraryCacheService {
    def redisService
    def grailsApplication
    def servletContext
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def serviceMethod() {
    }



    def getUserBooks(username){
        Map<String,List> books = new HashMap<String,List>()
        if(redisService.("userShelfBooks_"+username)==null || redisService.("userShelfBooks_"+username)=="null"){
            return null
        }
        books = redisService.("userShelfBooks_"+username)
        return books
    }

    def getUserInstituteBooks(batchId){
        Map<String,List> books = new HashMap<String,List>()
        if(redisService.("userMyLibraryInstituteBooks_"+batchId)==null || redisService.("userMyLibraryInstituteBooks_"+batchId)=="null"){
            return null
        }
        books = new JsonSlurper().parseText(redisService.("userMyLibraryInstituteBooks_"+batchId))
        return books
    }

    def instituteBooksforUser(batchId){
        Map<String,List> usersMyLibraryBooks = new HashMap<String,List>()
       String sql = " select bm.id,bm.title,bm.isbn,bm.status, bm.publisher_id,bbd.number_of_licenses,bbd.validity,bbd.batch_id,bm.cover_image" +
                " from books_mst bm, wsuser.books_batch_dtl bbd" +
                " where bm.id=bbd.book_id and (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())" +
                " and bbd.batch_id In ("+batchId+")" +
                " order by id desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource)
        def results1 = sql2.rows(sql)
        List  booklist1 =null
        def instituteId
        if(results1!=null) {
            results1.collect { comp ->
                instituteId =InstituteMst.findById(CourseBatchesDtl.findById(new Long(comp.batch_id)).conductedBy).id;
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
                if(usersMyLibraryBooks.get(""+comp.batch_id) == null){
                    List bList = new ArrayList()
                    bList.add([id: comp.id, title: comp.title, coverImage:comp.cover_image!=null?comp.cover_image:"", bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: comp.batch_id,
                               publisher: (comp.publisher_id!=null && comp.publisher_id!= "")? Publishers.findById(new Long(comp.publisher_id)).name:'',
                               noOfLic: (comp.number_of_licenses!=null)?comp.number_of_licenses:"", validity: (comp.validity!=null)?comp.validity:"",  level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                               grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",instituteId:instituteId])
                    usersMyLibraryBooks.put(""+comp.batch_id,bList)
                }else{
                    usersMyLibraryBooks.get(""+comp.batch_id).add([id: comp.id, title: comp.title,coverImage:comp.cover_image, bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: comp.batch_id,
                                                                   publisher: (comp.publisher_id!=null && comp.publisher_id!= "")? Publishers.findById(new Long(comp.publisher_id)).name:'',
                                                                   noOfLic: (comp.number_of_licenses!=null)?comp.number_of_licenses:"", validity: (comp.validity!=null)?comp.validity:"",  level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                                                                   grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",instituteId:instituteId])
                }
            }

        }

        Gson gson = new Gson();
        String element = gson.toJson(usersMyLibraryBooks,new TypeToken<HashMap<String,List>>() {}.getType())
        if(results1 != null) redisService.("userMyLibraryInstituteBooks_"+batchId) = element
        getUserInstituteBookIds(batchId,results1)
    }



    def getInstituteBooksPagination(batchId){
        int noOfBooksPerPage = 30
        String booksList=","
        String sql = " select bm.id,bm.title,bm.isbn,bm.status, bm.publisher_id,bbd.number_of_licenses,bbd.validity,bbd.batch_id,bm.cover_image,bm.price,bm.book_type,bm.language" +
                " from books_mst bm, wsuser.books_batch_dtl bbd" +
                " where bm.id=bbd.book_id and (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())" +
                " and bbd.batch_id In ("+batchId+")" +
                " order by bbd.id desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource)
        def results1 = sql2.rows(sql)
        List  booklist1 =null
        def instituteId
        List books = results1.collect { comp ->
            InstituteMst instituteMst =InstituteMst.findById(CourseBatchesDtl.findById(new Long(comp.batch_id)).conductedBy);
            BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
            return  [id: comp.id, title: comp.title, coverImage:comp.cover_image!=null?comp.cover_image:"", bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: comp.batch_id,
                     publisher: (comp.publisher_id!=null && comp.publisher_id!= "")? dataProviderService.getPublisher(new Long(comp.publisher_id)).name:'', bookType: comp.book_type!=null?comp.book_type:"",bookLangauge: comp.language!=null?comp.language:"",
                     noOfLic: (comp.number_of_licenses!=null)?comp.number_of_licenses:"", validity: (comp.validity!=null)?comp.validity:"",  level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                     grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",instituteId:instituteMst.id,showtabs:instituteMst.paidFreeTab!=null?instituteMst.paidFreeTab:""]

        }
        List books1 = new ArrayList();
        if (books != null && books.size() >= 0) {
            int totalNumberOfPages  =  Math.floor(books.size()/noOfBooksPerPage);
            if(books.size()%noOfBooksPerPage>0) totalNumberOfPages++;
            List tempBooks
            int booksIndex=0;
            for(int i=0;i<=totalNumberOfPages;i++){
                tempBooks = new ArrayList()
                for(int j=0;j<noOfBooksPerPage;j++){
                    if(booksIndex<books.size()){
                        tempBooks.add(books.get(booksIndex))
                        booksList +=books[booksIndex].id+","
                        booksIndex++;
                    }
                }
                Gson gson = new Gson();
                String element = gson.toJson(tempBooks,new TypeToken<List>() {}.getType())
                if(i==0) redisService.("instituteLibraryBooklist_"+ batchId) = element
                redisService.("instituteLibraryBooklist_"+ batchId+"_page_"+(i+1)) = element
            }

            int booksLength = books.size()
            for (int i = 0; i < booksLength; i++) {
                if(books.size <= i) break;
                books1.add(books.get(i));
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(books1,new TypeToken<List>() {}.getType())
        redisService.("InstituteLibraryAllBooksList_"+batchId) = element
        redisService.("instituteLibrary_"+ batchId+"_totalBooks") = ""+books.size()
    }


    def getInstituteBooksPaginationNew(batchId){
        int noOfBooksPerPage = 30
        String booksList=","
        String sql = " select bm.id,bm.title,bm.isbn,bm.status, bm.publisher_id,bbd.number_of_licenses,bbd.validity,bbd.batch_id,bm.cover_image,bm.price,bm.book_type,bm.language" +
                " from books_mst bm, wsuser.books_batch_dtl bbd" +
                " where bm.id=bbd.book_id and (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())" +
                " and bbd.batch_id In ("+batchId+")" +
                " order by bbd.id desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource)
        def results1 = sql2.rows(sql)
        List  booklist1 =null
        InstituteMst instituteMst
        Map<String, HashMap> mainBooks = new HashMap<String, HashMap>()
        Map<String, HashMap> combinedBooks = new HashMap<String, HashMap>()
        List freeList = new ArrayList()
        List paidList = new ArrayList()
        List books = results1.collect { comp ->
            instituteMst =InstituteMst.findById(CourseBatchesDtl.findById(new Long(comp.batch_id)).conductedBy);
            BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
            if (comp.price == 0.0 || comp.price == null || comp.price == 0) {
                freeList.add([id: comp.id, title: comp.title, coverImage:comp.cover_image!=null?comp.cover_image:"", bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: comp.batch_id,
                              publisher: (comp.publisher_id!=null && comp.publisher_id!= "")? dataProviderService.getPublisher(new Long(comp.publisher_id)).name:'', bookType: comp.book_type!=null?comp.book_type:"",bookLangauge: comp.language!=null?comp.language:"",
                              noOfLic: (comp.number_of_licenses!=null)?comp.number_of_licenses:"", validity: (comp.validity!=null)?comp.validity:"",  level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                              grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",instituteId:instituteMst.id]
                )
            }else if (comp.price != 0.0 || comp.price != null || comp.price != 0) {
                paidList.add([id: comp.id, title: comp.title, coverImage:comp.cover_image!=null?comp.cover_image:"", bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: comp.batch_id,
                              publisher: (comp.publisher_id!=null && comp.publisher_id!= "")? dataProviderService.getPublisher(new Long(comp.publisher_id)).name:'', bookType: comp.book_type!=null?comp.book_type:"",bookLangauge: comp.language!=null?comp.language:"",
                              noOfLic: (comp.number_of_licenses!=null)?comp.number_of_licenses:"", validity: (comp.validity!=null)?comp.validity:"",  level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                              grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",instituteId:instituteMst.id]
                )

            }
        }
        List books1 = new ArrayList();
        if (freeList != null && freeList.size() >= 0) {
            int totalNumberOfPages  =  Math.floor(freeList.size()/noOfBooksPerPage);
            if(freeList.size()%noOfBooksPerPage>0) totalNumberOfPages++;
            List tempBooks
            int booksIndex=0;
            for(int i=0;i<=totalNumberOfPages;i++){
                tempBooks = new ArrayList()
                for(int j=0;j<noOfBooksPerPage;j++){
                    if(booksIndex<freeList.size()){
                        tempBooks.add(freeList.get(booksIndex))
                        booksList +=freeList[booksIndex].id+","
                        booksIndex++;
                    }
                }
                Gson gson = new Gson();
                String element = gson.toJson(tempBooks,new TypeToken<List>() {}.getType())
                if(i==0) redisService.("instituteLibraryBooklistFree_"+ batchId) = element
                redisService.("instituteLibraryBooklistFree_"+ batchId+"_page_"+(i+1)) = element
            }

            int booksLength = books.size()
            for (int i = 0; i < booksLength; i++) {
                if(freeList.size <= i) break;
                books1.add(freeList.get(i));
            }
        }
        if (paidList != null && paidList.size() >= 0) {
            int totalNumberOfPages  =  Math.floor(paidList.size()/noOfBooksPerPage);
            if(paidList.size()%noOfBooksPerPage>0) totalNumberOfPages++;
            List tempBooks
            int booksIndex=0;
            for(int i=0;i<=totalNumberOfPages;i++){
                tempBooks = new ArrayList()
                for(int j=0;j<noOfBooksPerPage;j++){
                    if(booksIndex<paidList.size()){
                        tempBooks.add(paidList.get(booksIndex))
                        booksList +=paidList[booksIndex].id+","
                        booksIndex++;
                    }
                }
                Gson gson = new Gson();
                String element = gson.toJson(tempBooks,new TypeToken<List>() {}.getType())
                if(i==0) redisService.("instituteLibraryBooklistPaid_"+ batchId) = element
                redisService.("instituteLibraryBooklistPaid_"+ batchId+"_page_"+(i+1)) = element
            }

            int booksLength = books.size()
            for (int i = 0; i < booksLength; i++) {
                if(paidList.size <= i) break;
                books1.add(paidList.get(i));
            }
        }
        redisService.("instituteLibrary_"+ batchId+"_totalBooksFree") = ""+freeList.size()
        redisService.("instituteLibrary_"+ batchId+"_totalBooksPaid") = ""+paidList.size()
    }



    def userShelfBooks(String userName){
        String tempUsername = userName.toLowerCase().trim()
        if(tempUsername.toLowerCase().indexOf("select")!=-1||tempUsername.toLowerCase().indexOf("sleep(")!=-1||tempUsername.toLowerCase().indexOf(" or ")!=-1||tempUsername.indexOf("||")!=-1) userName=null

        int noOfBooksPerPage = 30
        String booksList=","
        def sql = "select bm.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled,-1 batchId,bm.title," +
                " bm.book_type,bm.test_start_date,bm.test_end_date,bm.cover_image,bm.price,bm.site_id,bp.expiry_date,bm.package_book_ids," +
                "'' batch_name,bm.show_in_library,bp.package_book_id,bm.publisher_id,bm.book_type,bm.language,bm.has_quiz" +
                "  from books_mst bm, wsuser.books_permission bp" +
                " where bp.book_id=bm.id and (bp.po_type is null OR bp.po_type='ADDEDFORFREE' OR bp.po_type='PURCHASE'  or bp.po_type='PURCHASEFROMCLIENTSITE')and bp.username='"+userName+"'"+
                " union "+
                // to get package books
                "select bm1.id,bp.date_created dateCreated,'book' permissionType,'' instructorControlled, -1 batchId," +
                " bm1.title,bm1.book_type,bm1.test_start_date,bm1.test_end_date,bm1.cover_image,bm1.price,bm1.site_id," +
                "bp.expiry_date,bm1.package_book_ids,'' batch_name,bm1.show_in_library,bm.id package_book_id,bm1.publisher_id,bm1.book_type,bm.language,bm1.has_quiz" +
                " from  books_mst bm, books_permission bp, books_mst bm1 " +
                " where (bp.po_type is null OR bp.po_type='ADDEDFORFREE' OR bp.po_type='PURCHASE'  or bp.po_type='PURCHASEFROMCLIENTSITE')and bp.username='"+userName+"' and bm.id=bp.book_id  " +
                " and bm.package_book_ids is not null and FIND_IN_SET(bm1.id,bm.package_book_ids)!=0 " +
                " order by dateCreated desc";



        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String publisherName
        Publishers publishers = null
            List books =  results.collect {  comp ->
                publisherName = ''
                if(comp.publisher_id!=null && comp.publisher_id!= ""){
                    publishers = dataProviderService.getPublisher(new Long(comp.publisher_id))
                    if (publishers!=null) publisherName = publishers.name
                }
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
               return [id   : comp.id, title: comp.title, coverImage: comp.cover_image != null ? comp.cover_image : "", level: (booksTagDtl != null) ? booksTagDtl.level : "", syllabus: (booksTagDtl != null) ? booksTagDtl.syllabus : "",
                           publisher: publisherName,
                           bookType: comp.book_type!=null?comp.book_type:"",bookLanguage: comp.language!=null?comp.language:"",
                           grade: (booksTagDtl != null) ? booksTagDtl.grade : "", subject: (booksTagDtl != null) ? booksTagDtl.subject : "",'packageBookId':comp.package_book_id,
                           'packageBookIds':comp.package_book_ids,price:comp.price,showInLibrary:comp.show_in_library,
                           expiryDate:comp.expiry_date!=null?(""+comp.expiry_date).replace(':','~'):"",hasQuiz:comp.has_quiz!=null?comp.has_quiz:""]

            }

        List books1 = new ArrayList();
        if (books != null && books.size() >= 0) {
            int totalNumberOfPages  =  Math.floor(books.size()/noOfBooksPerPage);
            if(books.size()%noOfBooksPerPage>0) totalNumberOfPages++;
            List tempBooks
            int booksIndex=0;
            for(int i=0;i<=totalNumberOfPages;i++){
                tempBooks = new ArrayList()
                for(int j=0;j<noOfBooksPerPage;j++){
                    if(booksIndex<books.size()){
                        tempBooks.add(books.get(booksIndex))
                        booksList +=books[booksIndex].id+","
                        booksIndex++;
                    }
                }
                Gson gson = new Gson();
                String element = gson.toJson(tempBooks,new TypeToken<List>() {}.getType())
                if(i==0) redisService.("userShelfBooks_"+ userName) = element
                redisService.("userShelfBooks_"+ userName+"_page_"+(i+1)) = element
            }

            int booksLength = books.size()
            for (int i = 0; i < booksLength; i++) {
                if(books.size <= i) break;
                books1.add(books.get(i));
            }
        }
        Gson gson = new Gson();
        String element = gson.toJson(books1,new TypeToken<List>() {}.getType())
        redisService.("userShelfBooksAll_"+userName) = element
        redisService.("userShelfBooks_"+ userName+"_totalBooks") = ""+books.size()
        getUserShelfBookIds(userName,results)
    }




    def getUserShelfBookIds(username,results){
        List books = results.collect { book ->
            return [id: book[0]]
        }
        books.unique()
        String userBooksListString

        String[] userBooksArray = new String[books.size()]
        for(int i=0;i<books.size();i++){
            userBooksArray[i] = books.get(i).id
        }

        if(books.size()>0) userBooksListString = String.join(",", userBooksArray);
        else userBooksListString = ""
        redisService.("userShelfBooks_"+username+"_"+"bookIds") = userBooksListString

    }

    def getUserInstituteBookIds(batchId,results){
        List books = results.collect { book ->
            return [id: book[0]]
        }
        books.unique()
        String userInstituteBooksListString

        String[] userBooksArray = new String[books.size()]
        for(int i=0;i<books.size();i++){
            userBooksArray[i] = books.get(i).id
        }

        if(books.size()>0) userInstituteBooksListString = String.join(",", userBooksArray);
        else userInstituteBooksListString = ""

        redisService.("userInstituteBooks_"+batchId+"_"+"bookIds") = userInstituteBooksListString
    }




}
