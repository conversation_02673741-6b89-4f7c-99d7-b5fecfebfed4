package com.wonderslate.WsLibrary

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.UtilService
import com.wonderslate.institute.AccessCode
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.util.regex.Pattern
import java.util.stream.Collectors
import grails.plugins.rest.client.RestBuilder

@Transactional
class WsLibraryService {
    def redisService
    def grailsApplication
    def servletContext
    SpringSecurityService springSecurityService
    WsLibraryCacheService wsLibraryCacheService
    DataProviderService dataProviderService
    UtilService utilService
    UserManagementService userManagementService
    def serviceMethod() {
    }

    def getUsersSelfBooks(){
        Map<String,List> books = new HashMap<String,List>()
        books = wsLibraryCacheService.getUserBooks()
        if(books != null) return books.get("self")
        else null
    }

    def getUsersBooksByBatchId(String batchId){
        Map<String,List> books = new HashMap<String,List>()
        books = wsLibraryCacheService.getUserBooks()
        if(books != null) return books.get(batchId)
        else null
    }

    def getUsersInsBooksByBatchId(String batchId){
        Map<String,List> books = new HashMap<String,List>()
        books = wsLibraryCacheService.getUserInstituteBooks(batchId)
        if(books != null) return books.get(batchId)
        else null
    }

    def getUsersInsBooksByUsername(String username){
        Map<String,List> books = new HashMap<String,List>()
        books = wsLibraryCacheService.getUserBooks(username)
        if(books != null) return books.get(username)
        else null
    }

    def updateUsersBooks(String userName,List usersInstituteDtl){
        wsLibraryCacheService.updateUsersBooks(userName,usersInstituteDtl)
    }

    def instituteBooksforUser(String batchId){
        wsLibraryCacheService.instituteBooksforUser(batchId)
    }

    def userShelfBooks(String username){
        wsLibraryCacheService.userShelfBooks(username)
    }

    def getMyLibrarySearchSuggestion(String query,bId){
        Map<String,List> books = new HashMap<String,List>()
        Map<String,List> instituteBooks = new HashMap<String,List>()
        List<String> matchingBooks = new ArrayList<String>()
        instituteBooks=wsLibraryCacheService.getUserInstituteBooks(bId)
        if(instituteBooks != null){
            for(Map.Entry<String,List> map:instituteBooks){
                matchingBooks.addAll(map.getValue().stream()
                        .filter({ b -> Pattern.compile(Pattern.quote(query), Pattern.CASE_INSENSITIVE).matcher(b.title).find() })
                        .map({ b -> b.title })
                        .collect(Collectors.toList()))
            }
        }
        return matchingBooks
    }

    def getMyLibrarySearchSuggestionForMultipleInstituteIds(String query, List batchIds){
        Map<String,List> books = new HashMap<String,List>()
        Map<String,List> instituteBooks = new HashMap<String,List>()
        List<String> matchingBooks = new ArrayList<String>()
        instituteBooks=getUsersInstituteBooksBatchIds(batchIds)
        if(instituteBooks != null){
            for(Map.Entry<String,List> map:instituteBooks){
                matchingBooks.addAll(map.getValue().stream()
                        .filter({ b -> Pattern.compile(Pattern.quote(query), Pattern.CASE_INSENSITIVE).matcher(b.title).find() })
                        .map({ b -> b.title })
                        .collect(Collectors.toList()))
            }
        }
        return matchingBooks
    }


    def getUsersInstituteBooksBatchIds(List usersInstituteDtl){
        Map<String,List> usersMyLibraryBooks = new HashMap<String,List>()
        String idsStr = ""
        if(usersInstituteDtl != null && usersInstituteDtl.size()>0) idsStr = usersInstituteDtl.get(0).batchId
        else idsStr = "0"
        for(int i=1;i< usersInstituteDtl.size();i++){
            idsStr = idsStr + "," + usersInstituteDtl.get(i).batchId
        }
       String sql = " select bm.id,bm.title,bm.isbn,bm.status, bm.publisher_id,bbd.number_of_licenses,bbd.validity,bbd.batch_id,bm.cover_image" +
                " from books_mst bm, wsuser.books_batch_dtl bbd" +
                " where bm.id=bbd.book_id and (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())" +
                " and bbd.batch_id In ("+idsStr+")" +
                " order by id desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource)
        def results1 = sql2.rows(sql)
        def instituteId
        if(results1!=null) {
            results1.collect { comp ->
                instituteId = InstituteMst.findById(CourseBatchesDtl.findById(new Long(comp.batch_id)).conductedBy).id;
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
                if(usersMyLibraryBooks.get(""+comp.batch_id) == null){
                    List bList = new ArrayList()
                    bList.add([id: comp.id, title: comp.title, coverImage:comp.cover_image!=null?comp.cover_image:"", bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: comp.batch_id,
                               publisher: (comp.publisher_id!=null && comp.publisher_id!= "")? Publishers.findById(new Long(comp.publisher_id)).name:'',
                               noOfLic: (comp.number_of_licenses!=null)?comp.number_of_licenses:"", validity: (comp.validity!=null)?comp.validity:"",  level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                               grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",instituteId:instituteId])
                    usersMyLibraryBooks.put(""+comp.batch_id,bList)
                }else{
                    usersMyLibraryBooks.get(""+comp.batch_id).add([id: comp.id, title: comp.title,coverImage:comp.cover_image, bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: comp.batch_id,
                                                                   publisher: (comp.publisher_id!=null && comp.publisher_id!= "")? Publishers.findById(new Long(comp.publisher_id)).name:'',
                                                                   noOfLic: (comp.number_of_licenses!=null)?comp.number_of_licenses:"", validity: (comp.validity!=null)?comp.validity:"",  level:(booksTagDtl!=null)?booksTagDtl.level:"",syllabus:(booksTagDtl!=null)?booksTagDtl.syllabus:"",
                                                                   grade:(booksTagDtl!=null)?booksTagDtl.grade:"",subject:(booksTagDtl!=null)?booksTagDtl.subject:"",instituteId:instituteId])
                }
            }
        }

        return usersMyLibraryBooks;
    }

    def getMyLibrarySearchResults(String query,bId){
        Map<String,List> books = new HashMap<String,List>()
        Map<String,List> instituteBooks = new HashMap<String,List>()
        List<String> matchingBooks = new ArrayList<String>()
        instituteBooks=wsLibraryCacheService.getUserInstituteBooks(bId)
        if(instituteBooks != null){
            for(Map.Entry<String,List> map:instituteBooks){
                matchingBooks.addAll(map.getValue().stream()
                        .filter({ b -> Pattern.compile(Pattern.quote(query), Pattern.CASE_INSENSITIVE).matcher(b.title).find() })
                        .collect(Collectors.toList()))
            }
        }
        return matchingBooks
    }

    def getMyLibrarySearchSuggestionForMultipleInstituteIdsbooks(String query, List batchIds){
        Map<String,List> books = new HashMap<String,List>()
        Map<String,List> instituteBooks = new HashMap<String,List>()
        List<String> matchingBooks = new ArrayList<String>()
        instituteBooks=getUsersInstituteBooksBatchIds(batchIds)
        if(instituteBooks != null){
            for(Map.Entry<String,List> map:instituteBooks){
                matchingBooks.addAll(map.getValue().stream()
                        .filter({ b -> Pattern.compile(Pattern.quote(query), Pattern.CASE_INSENSITIVE).matcher(b.title).find() })
                        .collect(Collectors.toList()))
            }
        }
        return matchingBooks
    }

    def bookAccessForUser(bookId,request,session){
        String username=springSecurityService.currentUser.username;
        boolean hasUserBookAccess = false
        List batchIdList = getInstitutesForUser(request,session);
        if (redisService.("userShelfBooks_" + username) == null  || redisService.("userShelfBooks_" + username) == "null") wsLibraryCacheService.userShelfBooks(username)
       def userSelfCache=redisService.("userShelfBooks_"+springSecurityService.currentUser.username+"_"+"bookIds")
        List booksIds = userSelfCache!=null?Arrays.asList(redisService.("userShelfBooks_"+springSecurityService.currentUser.username+"_"+"bookIds").split("\\s*,\\s*")):null
        BooksPermission booksPermission
        if(batchIdList != null && batchIdList.size() <= 1) {
            long bId = batchIdList.size() == 1? batchIdList.get(0).batchId:0
            booksPermission= BooksPermission.findByBookIdAndUsernameAndBatchId(new Long(bookId),username,new Long(bId))
        }else if(batchIdList != null || batchIdList.size() > 1){
            for(int i=0;i<batchIdList.size();i++){
                booksPermission= BooksPermission.findByBookIdAndUsernameAndBatchId(new Long(bookId),username,new Long(batchIdList[i].batchId))
                if(booksPermission!=null)break;
            }
        }
        if ((booksIds!=null && booksIds.indexOf(bookId) > -1) || booksPermission!=null) {
            hasUserBookAccess = true;
        }else{
            hasUserBookAccess = false;
        }
        return hasUserBookAccess
    }


    def getUsersInstituteBooksIds(List usersInstituteDtl){
        Map<String,List> usersMyLibraryBooks = new HashMap<String,List>()
        String idsStr = ""
        if(usersInstituteDtl != null && usersInstituteDtl.size()>0) idsStr = usersInstituteDtl.get(0).batchId
        else idsStr = "0"
        for(int i=1;i< usersInstituteDtl.size();i++){
            idsStr = idsStr + "," + usersInstituteDtl.get(i).batchId
        }
        String sql = " select bm.id,bm.title,bm.isbn,bm.status, bm.publisher_id,bbd.number_of_licenses,bbd.validity,bbd.batch_id,bm.cover_image" +
                " from books_mst bm, wsuser.books_batch_dtl bbd" +
                " where bm.id=bbd.book_id and (bbd.book_expiry_date is null OR bbd.book_expiry_date > sysdate())" +
                " and bbd.batch_id In ("+idsStr+")" +
                " order by id desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource)
        def results1 = sql2.rows(sql)
        if(results1!=null) {
            results1.collect { comp ->
                BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(comp.id)
                if(usersMyLibraryBooks.get(""+comp.batch_id) == null){
                    List bList = new ArrayList()
                    bList.add([id: comp.id])
                    usersMyLibraryBooks.put(""+comp.batch_id,bList)
                }else{
                    usersMyLibraryBooks.get(""+comp.batch_id).add([id: comp.id])
                }
            }
        }

        List books = results1.collect { book ->
            return [id: book[0]]
        }
        books.unique()
        String userBooksListString

        String[] userBooksArray = new String[books.size()]
        for(int i=0;i<books.size();i++){
            userBooksArray[i] = books.get(i).id
        }

        if(books.size()>0) userBooksListString = String.join(",", userBooksArray);
        else userBooksListString = ""
        return userBooksListString;
    }

    @Transactional
    def getInstitutesForUser(request,session){
        List instituteDetails

        String   ipAddress = getIPAddressOfClient(request)
        String sql =
                " SELECT bud.batch_id, im.id institute_id, im.name " +
                        " FROM wsuser.batch_user_dtl bud, wsuser.course_batches_dtl cbd, wsuser.institute_mst im " +
                        " where im.site_id="+getSiteId(request,session)+" and bud.username='"+springSecurityService.currentUser.username+"' " +
                        " and cbd.id=bud.batch_id and im.id=cbd.conducted_by " +
                        " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) " +
                        " union " +
                        " select cbd.id batch_id, im.id institute_id, im.name from wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.institute_ip_address iia " +
                        " where iia.ip_address= '"+ipAddress+"' and im.id =iia.institute_id and  im.id=cbd.conducted_by " +
                        " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and im.site_id="+getSiteId(request,session)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        instituteDetails = results.collect{
            return [batchId: it.batch_id, id: it.institute_id, name: it.name]
        }

            return  instituteDetails;

    }

    String getIPAddressOfClient(request){
        //https://www.oodlestechnologies.com/blogs/Java-Get-Client-IP-Address
        String remoteAddr = request.getHeader("X-FORWARDED-FOR");
        if(remoteAddr == null || "".equals(remoteAddr)) {
            remoteAddr = request.getRemoteAddr();
            if(remoteAddr.equalsIgnoreCase("0:0:0:0:0:0:0:1")) {
                InetAddress inetAddress = InetAddress.getLocalHost();
                String ipAddress = inetAddress.getHostAddress();
                remoteAddr = ipAddress;
            }
        }
        return remoteAddr
    }

    Integer getSiteId(request,session){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(request.getParameter("siteId")!=null) siteId = new Integer(request.getParameter("siteId"));
        }

        return siteId;
    }

    String getDefaultBatchId(String batchId){
        String defaultBatchId = batchId
        CourseBatchesDtl courseBatchesDtl = dataProviderService.getCourseBatchesDtl(new Integer(batchId))
         if(courseBatchesDtl!=null&&!"Default".equals(courseBatchesDtl.name)){

            CourseBatchesDtl defaultCBD = dataProviderService.getDefaultCourseBatchesDtl(courseBatchesDtl.conductedBy)
            defaultBatchId = ""+defaultCBD.id
       }
        return defaultBatchId
    }

    def checkInstituteAccessCode(params,session,siteId){
        String accessCodeCheck="Invalid scratch code! Please re-enter."
        AccessCode accessCode=AccessCode.findByCodeAndSiteId(params.accessCode,siteId)
        if(accessCode!=null) {
            if (accessCode.status != "Redeemed") {
                CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedBy(accessCode.instituteId)
                if (!BatchUserDtl.findByUsernameAndBatchId(springSecurityService.currentUser.username, new Long(courseBatchesDtl.id))) {
                    BatchUserDtl batchUserDtl = new BatchUserDtl(username: springSecurityService.currentUser.username, batchId: new Long(courseBatchesDtl.id), createdBy: springSecurityService.currentUser.username)
                    batchUserDtl.save(failOnError: true, flush: true)
                    accessCode.username = springSecurityService.currentUser.username
                    accessCode.status = "Redeemed"
                    accessCode.dateRedeemed = new Date();
                    accessCodeCheck = "OK"
                    session["userInstitutes"] = null
                } else {
                    accessCodeCheck = "You are already added to this institute."
                }
            }else{
                accessCodeCheck ="Entered access code is already used"
            }
        }
        def json = ["status":accessCodeCheck,instituteId:accessCode?accessCode.instituteId:null]
        return json
    }


    @Transactional
    def getUsersInstitute(request,session){
        List instituteDetails
        String ipAddress
        ipAddress = utilService.getIPAddressOfClient(request)
        instituteDetails = userManagementService.getInstitutesForUser(getSiteId(request,session),ipAddress)
        return  instituteDetails;
    }

}
