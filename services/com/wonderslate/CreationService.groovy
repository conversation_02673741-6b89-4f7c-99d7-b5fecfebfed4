package com.wonderslate

import com.wonderslate.usermanagement.AuthenticationOtp
import grails.transaction.Transactional
import groovy.sql.Sql

@Transactional
class CreationService {
    def grailsApplication
    def generateOTP (String contact, Long siteId){
        String validOTP
        String query =
                " SELECT otp FROM wsuser.authentication_otp WHERE site_id='"+siteId+"' AND contact='" +contact+
                "' AND date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) =date(DATE_ADD(SYSDATE(), INTERVAL '5:30' HOUR_MINUTE)) order by id desc limit 1"
        def result = new Sql(grailsApplication.mainContext.getBean('dataSource_wsuser')).rows(query)
        if(result[0]==null){
            //if otp is not present for the current date
            AuthenticationOtp otp = new AuthenticationOtp(
                    otp: new Random().nextInt(900000)+100000,
                    contact: contact,
                    siteId: siteId
            )
            otp.save(failOnError: true,flush: true)
            validOTP=""+otp.otp
        }else{
            validOTP=""+result[0][0]
        }
        return validOTP
    }
    def deleteAllOTP(){
        String query = "delete from  wsuser.authentication_otp where date(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE))<date(DATE_ADD(SYSDATE(), INTERVAL '5:30' HOUR_MINUTE))"
        def result = new Sql(grailsApplication.mainContext.getBean('dataSource_wsuser')).rows(query)
    }
    def deleteOTPForUser(contact,siteId){
        AuthenticationOtp.executeUpdate("delete from AuthenticationOtp where contact='"+contact+"' and site_id ="+siteId)
    }
}
