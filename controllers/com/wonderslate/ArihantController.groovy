package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

import javax.servlet.http.Cookie

class ArihantController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def redisService
    WsshopService wsshopService
    UserManagementService userManagementService
    @Transactional
    def setUserSession(){
        Cookie cookie = new Cookie("siteName","arihant")
        cookie.path = "/"
        response.addCookie(cookie)
        session['siteId'] = new Integer(3);
        session.setAttribute("entryController", "arihant");
        session.setAttribute("wonderpublish","true");
        session.setAttribute("siteName", "Arihant")
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }


        if(springSecurityService.currentUser!=null){
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }

            //get user basic analytics
            String username=springSecurityService.currentUser.username
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }else{
            String username = session["siteId"]+session.getId()+"_temp"
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }


        if(session["activeCategories_3"] == null) {
            if(redisService.("activeCategories_3")==null) wsshopService.activeCategories(new Integer(3))
            session["activeCategories_3"] = redisService.("activeCategories_3")
        }
        if(session["activeCategoriesSyllabus_3"] == null) {
            if(redisService.("activeCategoriesSyllabus_3")==null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(3))
            session["activeCategoriesSyllabus_3"] = redisService.("activeCategoriesSyllabus_3")
        }
        if(session["activeGrades_3"] == null) {
            if(redisService.("activeGrades_3")==null) wsshopService.getActiveGrades(new Integer(3))
            session["activeGrades_3"] = redisService.("activeGrades_3")
        }
        if(session["activeSubjects_3"] == null) {
            if(redisService.("activeSubjects_3")==null) wsshopService.getActiveSubjects(new Integer(3))
            session["activeSubjects_3"] = redisService.("activeSubjects_3")
        }

        if(session["googleUAId_3"]==null){
            if(redisService.("googleUAId_3")==null) dataProviderService.getGoogleUniversalAnalytics("3")
            session["googleUAId_3"] = redisService.("googleUAId_3")
        }

        return
    }
    def index(){
          setUserSession()
        if(redisService.("bannerList_"+session['siteId'])==null) dataProviderService.getBanners(""+session['siteId'])
        def bannerList = redisService.("bannerList_"+session['siteId'])
        [commonTemplate:"true",bannerList:bannerList]
    }
    @Transactional
    def store() {

        setUserSession()
        int pageNo=0
        if(params.level!=null) params.put("level",params.level.replace('-',' '))
        if(params.syllabus!=null) params.put("syllabus",params.syllabus.replace('-',' '))
        if(params.grade!=null) params.put("grade",params.grade.replace('-',' '))
        if(params.subject!=null) params.put("subject",params.subject.replace('-',' '))
        if(params.pageNo!=null&&!"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
        HashMap booksAndPublishers = wsshopService.getBooksList(params,new Integer(3),pageNo)
        [commonTemplate:"true",booksList:booksAndPublishers]

    }
    def footer(){}

    def navheader(){}
    def adminHome(){

    }

    @Transactional
    def examCalendar() {

    }

    def shoppingCart(){
        setUserSession()
        [title:'Cart - Arihant',commonTemplate:"true"]
    }

}
