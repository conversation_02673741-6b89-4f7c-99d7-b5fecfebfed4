package com.wonderslate.toDo

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.UtilService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat

class ToDoController {

    ToDoService toDoService
    UtilService utilService
    DataProviderService dataProviderService

    def index() { }

    @Transactional
    @Secured(['ROLE_USER'])
    def addToDoTask(){
        String taskName = "", priority = "default", fromTime = "", toTime = "", resType = "", resLink = ""
        String taskDate = ""
        Long resId = 0
        Long batchId = 0
        Date taskDateolumnValue = null
        def json = null
        if(params.resId != null && params.resId != "") {
            resId = Long.parseLong(params.resId)
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(resId)
            taskName = resourceDtl.resourceName
            resType = resourceDtl.resType
            resLink = resourceDtl.resLink
            DateFormat df1 = new SimpleDateFormat("dd-MM-yyyy");
            taskDateolumnValue = df1.parse(new SimpleDateFormat("dd-MM-yyyy").format(new Date()));
        }
        if(params.taskName != null && (resId == null || resId == 0)) taskName = params.taskName
        else if(resId == null || resId == 0){
            json = [status:'error',message:'Task name cannot be empty']
            if(params.taskDate == null) json = [status:'error',message:'Task name and Task Date cannot be empty']
            render json as JSON
        }
        if(params.priority != null) {
            priority = params.priority
        }
        if((resId == null || resId == 0) && params.taskDate != null) {
            taskDate = params.taskDate
            DateFormat df1 = new SimpleDateFormat("dd-MM-yyyy");
            taskDateolumnValue = df1.parse(taskDate);
//            taskDateolumnValue = utilService.convertDate(taskDateolumnValue,"IST","UTC")
        }
        else if(resId == null || resId == 0){
            json = [status:'error',message:'Task Task Date cannot be empty']
            render json as JSON
        }
        if(params.fromTime != null) fromTime = params.fromTime
        if(params.toTime != null) toTime = params.toTime
        if(params.batchId != null && !"".equals(params.batchId)) batchId =Long.parseLong(params.batchId)
        json = ['toDoTask':toDoService.addToDOTask(taskName,taskDateolumnValue,fromTime,toTime,priority,resId,resLink,resType,batchId)]
        render json as JSON
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def editToDoTask(){
        String taskName = "", priority = "", action = "", status = ""
        String taskDate = ""
        String [] toDoTaskId = null
        String fromTime = "", toTime = ""
        Date taskDateolumnValue = null
        def json = null
        if(params.toDoTaskId != null) {
            String toDoTaskIdStr = params.toDoTaskId
            toDoTaskId = toDoTaskIdStr.split(",")
        }
        else {
            json = [status:'error',message:'Task ID cannot be empty']
            render json as JSON
        }
        if(params.type != null) action = params.type
        else{
            json = [status:'error',message:'Specify type.... type can have type = addStatus to add status or type = editTask to edit ToDo task']
            render json as JSON
        }
        if(params.taskName != null) taskName = params.taskName
        if(params.taskDate != null) {
            taskDate = params.taskDate
            DateFormat df1 = new SimpleDateFormat("dd-MM-yyyy");
            taskDateolumnValue = df1.parse(taskDate);
//            taskDateolumnValue = utilService.convertDate(taskDateolumnValue,"IST","UTC")
        }
        if(params.priority != null) priority = params.priority
        if(params.status != null) status = params.status
        if(params.fromTime != null) fromTime = params.fromTime
        if(params.toTime != null) toTime = params.toTime

        json = ['toDoTask':toDoService.editToDoTask(toDoTaskId,action,status,taskName,taskDateolumnValue,fromTime,toTime,priority)]
        render json as JSON
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def getToDoList(){
        def json = null
        String filterType = "", filterValue = "", status = ""
        if(params.filterType != null && params.filterValue == null){
            String message = ""
            if(params.filterValue == null) message = "Filter value cannot be empty when filter type is specified"
            json = [status:'error',message:message]
            render json as JSON
        }else{
            if(params.filterType != null) filterType = params.filterType
            if(params.filterValue != null) filterValue = params.filterValue
            if(params.status != null) status = params.status
            json = ['toDoTask':toDoService.listToDoTask(filterType,filterValue,status)]
            render json as JSON
        }
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def deleteToDo(){
        def json = null
        String idStr = ""
        long id
        String message = ""
        if(params.id != null) idStr = params.id
                try{
                    if(!idStr.isEmpty()) id = Long.parseLong(idStr)
                    if(id >0) {
                        json = ['toDoTask':toDoService.deleteTask(id)]
                        render json as JSON
                    }else{
                        message = "Please send correct ID"
                        json = [status:'error',message:message]
                        render json as JSON
                    }
                }catch(Exception ex){
                    message = "Please send correct ID"
                    json = [status:'error',message:message,error:ex.getMessage() + ex.toString()]
                    render json as JSON
                }
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def toDoListAndUpdate(){
        ["title":"My To-Do - Wonderslate", commonTemplate:"true"]
    }

}
