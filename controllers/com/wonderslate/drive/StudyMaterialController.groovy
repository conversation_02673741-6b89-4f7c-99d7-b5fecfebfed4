package com.wonderslate.drive
import com.wonderslate.institute.CourseBatchesDtl
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional


class StudyMaterialController {

    StudyMaterialService studyMaterialService

    /**
     * Displays the "My Materials" page, showing folders and materials.
     * Corresponds to GSP view: 'index.gsp'
     */
    @Transactional @Secured(['ROLE_USER'])
    def index() {
        println("Entered index")
        String username = session["userdetails"].username
        String folderPath = params.folderPath ?: ''

        // Retrieve folders and materials
        def folders = studyMaterialService.getFolders(username, folderPath)
        if(folders!=null){
            println("Folders are not null")
        }else {
            println("Folders are null")
        }
        def materials = studyMaterialService.getStudyMaterials(username, folderPath)

        // Retrieve recently accessed materials
        def recentMaterials = studyMaterialService.getRecentlyAccessedMaterials(username)

        // Pass data to the view
        [folders: folders, materials: materials, folderPath: folderPath, recentMaterials: recentMaterials]
    }

    @Transactional @Secured(['ROLE_USER'])
    def loadFolderData() {
        println("Entered loadFolderData")
        String username = session["userdetails"].username
        String folderPath = params.folderPath ?: ''

        // Retrieve folders and materials
        def folders = studyMaterialService.getFolders(username, folderPath)
        def materials = studyMaterialService.getStudyMaterials(username, folderPath)

        // Retrieve recently accessed materials
        def recentMaterials = studyMaterialService.getRecentlyAccessedMaterials(username)

        // Render JSON for the response
      def json = [folders: folders, materials: materials, folderPath: folderPath, recentMaterials: recentMaterials]
        render json as JSON
    }
    /**
     * Shows the upload form.
     * Corresponds to GSP view: 'upload.gsp'
     */
    def uploadForm() {
        String folderPath = params.folderPath ?: ''
        [folderPath: folderPath]
    }

    /**
     * Handles file upload.
     */
    @Transactional @Secured(['ROLE_USER'])
    def upload() {
        def file = request.getFile('file')
        String folderPath = params.folderPath
        println("Folder path: $folderPath")
        try {
            println("entered here")
            def studyMaterial = studyMaterialService.uploadStudyMaterial(session["userdetails"].username, file, folderPath)
            def json = [status:"success",id: studyMaterial.id, fileName: studyMaterial.fileName, folderPath: studyMaterial.folderPath]
            println("even this is true")
            render json as JSON
        } catch (Exception e) {
            flash.message = "Error uploading file: ${e.message}"
            render(view: 'uploadForm', model: [folderPath: folderPath])
        }
    }

    /**
     * Shows the form to create a new folder.
     * Corresponds to GSP view: 'createFolder.gsp'
     */
    @Transactional @Secured(['ROLE_USER'])
    def createFolderForm() {
        String parentPath = params.parentPath ?: ''
        [parentPath: parentPath]
    }

    /**
     * Handles folder creation.
     */
    @Transactional @Secured(['ROLE_USER'])
    def createFolder() {
        String folderName = params.folderName
        String parentPath = params.parentPath
        try {
            def folder = studyMaterialService.createFolder(session["userdetails"].username, folderName, parentPath)
            flash.message = "Folder created successfully."
            redirect(action: 'index', params: [folderPath: parentPath])
        } catch (Exception e) {
            flash.message = "Error creating folder: ${e.message}"
            render(view: 'createFolderForm', model: [parentPath: parentPath])
        }
    }

    /**
     * Deletes a study material.
     */
    @Transactional @Secured(['ROLE_USER'])
    def deleteMaterial(Long id) {
        def studyMaterial = StudyMaterial.get(id)
        String folderPath = studyMaterial.folderPath
        try {
            studyMaterialService.deleteStudyMaterial(session["userdetails"].username, studyMaterial)
            redirect(action: 'index', params: [folderPath: folderPath])
        } catch (Exception e) {
            redirect(action: 'index', params: [folderPath: folderPath])
        }
    }

    /**
     * Deletes a folder and its contents.
     */
    @Transactional @Secured(['ROLE_USER'])
    def deleteFolder(Long id) {
        def folder = Folder.get(id)
        String parentPath = folder.folderPath.contains('/') ? folder.folderPath.substring(0, folder.folderPath.lastIndexOf('/')) : ''
        try {
            studyMaterialService.deleteFolder(session["userdetails"].username, folder)
            flash.message = "Folder deleted successfully."
            redirect(action: 'index', params: [folderPath: parentPath])
        } catch (Exception e) {
            flash.message = "Error deleting folder: ${e.message}"
            redirect(action: 'index', params: [folderPath: parentPath])
        }
    }

    /**
     * Shows the form to share a material.
     * Corresponds to GSP view: 'shareMaterial.gsp'
     */
    @Transactional @Secured(['ROLE_USER'])
    def shareMaterialForm(Long id) {
        def studyMaterial = StudyMaterial.get(id)
        // Retrieve list of batches for the user's institute
        def batches = getBatchesForUserInstitute(session["userdetails"].username)
        [studyMaterial: studyMaterial, batches: batches]
    }

    /**
     * Handles sharing of a study material.
     */
    @Transactional @Secured(['ROLE_USER'])
    def shareMaterial() {
        Long materialId = params.long('materialId')
        Long batchId = params.long('batchId')
        try {
            studyMaterialService.shareStudyMaterial(session["userdetails"].username, materialId, batchId)
        } catch (Exception e) {
           println("exception while sharing "+e.toString())
        }
        def json = [status:"success"]
        render json as JSON
    }

    /**
     * Displays the "Shared Materials" page, showing materials shared with the user's batches.
     * Corresponds to GSP view: 'sharedMaterials.gsp'
     */
    @Transactional @Secured(['ROLE_USER'])
    def sharedMaterials() {
        String username = session["userdetails"].username
        // Get batches the user belongs to
        def userBatches = getUserBatches(username)
        // Get materials shared with these batches
        def sharedMaterials = MaterialShare.createCriteria().list {
            'in'('batchId', userBatches*.id)
        }.collect { share ->
            StudyMaterial.get(share.materialId)
        }
        [sharedMaterials: sharedMaterials]
    }

    /**
     * Displays a study material, integrating with the AI Assistant.
     * Corresponds to GSP view: 'viewMaterial.gsp'
     */
    @Transactional @Secured(['ROLE_USER'])
    def viewMaterial(Long id) {

        def studyMaterial = StudyMaterial.get(id)
        if (!studyMaterial) {
            println("Material not found.")
            redirect(action: 'index')
            return
        }

        // Check if the user has access to the material
        if (studyMaterial.username != session["userdetails"].username&&!studyMaterialService.isMaterialSharedWithUser(studyMaterial.id, session["userdetails"].username)) {
            println("You do not have access to this material.")
            redirect(action: 'index')
            return
        }
        // Update last accessed time
        if (studyMaterial.username.equals(""+session["userdetails"].username)) studyMaterialService.updateLastAccessed(studyMaterial)
        // Pass the material to the view
        //[studyMaterial: studyMaterial]
        render "done"
    }

    @Transactional @Secured(['ROLE_USER'])
    def download(Long id) {
        def studyMaterial = StudyMaterial.get(id)
        if (!studyMaterial) {
            redirect(action: 'index')
            return
        }
        // Check access permissions
        if (studyMaterial.username != session["userdetails"].username) {
            redirect(action: 'index')
            return
        }
        // Retrieve the file from storage
        File file = new File(grailsApplication.config.grails.basedir.path+"/"+studyMaterial.filePath)
        if (!file.exists()) {
            redirect(action: 'index')
            return
        }
        response.setContentType("application/octet-stream")
        response.setHeader("Content-disposition", "attachment;filename=${studyMaterial.fileName}")
        response.outputStream << file.bytes
        response.outputStream.flush()
    }

    @Transactional @Secured(['ROLE_USER'])
    /**
     * Provides the batch list for sharing materials.
     */
    def getBatchesForSharing() {
        String username = session.username
        def batches = getBatchesForUserInstitute(username)
        render(template: '/drive/batchList', model: [batches: batches])
    }

    @Transactional @Secured(['ROLE_USER'])
    def getSharedMaterialsForBatch(){
        List sharedMaterials = studyMaterialService.getSharedMaterialsForBatch(new Long(params.batchId))
        def json = [sharedMaterials: sharedMaterials]
        render json as JSON
    }
    // Helper methods

    /**
     * Retrieves the list of batches for the user's institute.
     * You need to implement this method according to your data model.
     */
    private List<CourseBatchesDtl> getBatchesForUserInstitute(String username) {
        // Retrieve the user's institute
        def user = User.findByUsername(username)
        def instituteId = user.instituteId // Assuming there's an instituteId in User
        // Get batches conducted by the user's institute
        return CourseBatchesDtl.findAllByConductedBy(instituteId)
    }


}
