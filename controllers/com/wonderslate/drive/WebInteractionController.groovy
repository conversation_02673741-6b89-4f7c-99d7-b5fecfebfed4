package com.wonderslate.drive

import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import com.wonderslate.institute.BatchUserDtl


class WebInteractionController {

    SpringSecurityService springSecurityService
    def redisService

    // Action to record interactions from the browser extension
    @Transactional
    def saveInteraction() {
        response.setHeader('Access-Control-Allow-Origin', '*')
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        // Extract parameters from the request
        def requestBody = request.JSON
        String usercode = requestBody.usercode
        String username=null
        username = getUsername(usercode).username
        String webpageUrl = requestBody.webpageUrl
        String userQuery = requestBody.userQuery
        String aiResponse = requestBody.aiResponse

        // Handle null or blank parameters
        if (!username || !webpageUrl || !userQuery || !aiResponse) {
            render status: 400, text: 'Missing required parameters.'
            return
        }

        // Split aiResponse if necessary
        int maxPartSize = 60000  // Adjust based on your database limitations
        String part1 = aiResponse
        String part2 = null

        if (aiResponse.size() > maxPartSize) {
            part1 = aiResponse.substring(0, maxPartSize)
            part2 = aiResponse.substring(maxPartSize)
        }

        def interaction = new WebInteraction(
                username: username,
                webpageUrl: webpageUrl,
                userQuery: userQuery,
                aiResponsePart1: part1,
                aiResponsePart2: part2,
                timestamp: new Date()
        )

        if (interaction.save(flush: true)) {
            render status: 200, text: 'Interaction saved successfully.'
        } else {
            render status: 500, text: 'Error saving interaction.'
        }
    }

    // Dashboard overview action
    @Transactional
    def dashboard() {
        String username = springSecurityService.currentUser?.username

        if (!username) {
            redirect(controller: 'login', action: 'index')
            return
        }

        // Render the dashboard view
        render(view: 'dashboard', model: [username: username])
    }

    // Action to load dashboard data via AJAX
    @Transactional
    def loadDashboardData() {
        String username = springSecurityService.currentUser?.username

        if (!username) {
            render status: 401, text: 'Unauthorized'
            return
        }

        // Handle pagination parameters
        int max = params.int('max') ?: 10  // Number of records per page
        int pageNum = params.int('pageNum') ?: 1
        int offset = (pageNum - 1) * max

        log.info "loadDashboardData called with params.pageNum='${params.pageNum}', pageNum=${pageNum}, max=${max}, offset=${offset}"

        // Fetch recent interactions
        def interactions = WebInteraction.createCriteria().list {
            eq('username', username)
            order('timestamp', 'desc')
            maxResults(max)
            firstResult(offset)
        }

        // Prepare data for rendering
        def interactionData = interactions.collect { interaction ->
            [
                    webpageUrl: interaction.webpageUrl,
                    timestamp: interaction.timestamp.format('dd-MM-yyyy HH:mm'),
                    userQuery: interaction.userQuery,
                    aiResponse: interaction.aiResponsePart1 + (interaction.aiResponsePart2 ?: '')
            ]
        }

        // Fetch total counts for summary statistics
        def totalInteractions = WebInteraction.countByUsername(username)

        def pagesVisited = WebInteraction.createCriteria().get {
            eq('username', username)
            projections {
                countDistinct('webpageUrl')
            }
        } ?: 0
        def json = [
                totalInteractions: totalInteractions,
                pagesVisited: pagesVisited,
                interactions: interactionData
        ]
        render json as JSON

    }






    // Action to display interactions for a specific web page
    @Transactional
    def loadPageInteractions() {
        String username = springSecurityService.currentUser?.username
        String webpageUrl = params.webpageUrl

        if (!username) {
            render status: 401, text: 'Unauthorized'
            return
        }

        if (!webpageUrl) {
            render status: 400, text: 'Webpage URL is required.'
            return
        }

        // Handle pagination parameters
        int max = params.int('max') ?: 10
        int offset = params.int('offset') ?: 0

        def interactions = WebInteraction.findAllByUsernameAndWebpageUrl(
                username, webpageUrl,
                [sort: 'timestamp', order: 'asc', max: max, offset: offset]
        )

        // Prepare data for rendering
        def interactionData = interactions.collect { interaction ->
            [
                    timestamp: interaction.timestamp.format('dd-MM-yyyy HH:mm'),
                    userQuery: interaction.userQuery,
                    aiResponse: interaction.aiResponsePart1 + (interaction.aiResponsePart2 ?: '')
            ]
        }

        // Return data as JSON
        def json = [
                interactions: interactionData
        ]
        render json as JSON
    }

    // Action to render the page interactions view
    @Transactional
    def pageInteractions() {
        String username = springSecurityService.currentUser?.username
        String webpageUrl = params.webpageUrl

        if (!username) {
            redirect(controller: 'login', action: 'index')
            return
        }

        if (!webpageUrl) {
            flash.message = 'Webpage URL is required.'
            redirect(action: 'dashboard')
            return
        }

        render(view: 'pageInteractions', model: [webpageUrl: webpageUrl])
    }

    @Transactional
    def getUsername(String win){
        User user  = redisService.memoizeDomainObject(User, "user_"+win) {
            return User.findByWin(win);
        }
        return user
    }
    /**
     * Adds a new link to the SitePermission table.
     * Only accessible by admin users.
     */
    @Secured(['ROLE_LIBRARY_ADMIN']) @Transactional
    def addLink() {
        String link = params.newLink
        String batchId = params.batchId

        if (!link||!batchId) {
            render(status: 400, text: 'Link is required.')
            return
        }

        // Normalize the link to base URL
        String baseUrl = extractBaseUrl(link)

        if (!baseUrl) {
            render(status: 400, text: 'Invalid URL format.')
            return
        }

        // Check if the link already exists
        if (SitePermission.findByLinkAndBatchId(baseUrl,new Long(batchId))) {
            render(status: 409, text: 'Link already exists.')
            return
        }

        // Create a new SitePermission entry
        def sitePermission = new SitePermission(
                link: baseUrl,
                batchId: new Long(batchId),
                username: springSecurityService.currentUser.username,
                dateCreated: new Date()
        )

        if (sitePermission.save(flush: true)) {
            render(status: 201, text: 'Link added successfully.')
        } else {
            render(status: 500, text: 'Failed to add link.')
        }
    }

    /**
     * Deletes a link from the SitePermission table.
     * Only accessible by admin users.
     */
    @Secured(['ROLE_LIBRARY_ADMIN']) @Transactional
    def deleteLink() {
        String id = params.id

        if (!id) {
            render(status: 400, text: 'Id is required.')
            return
        }
       def sitePermission = SitePermission.findById(new Long(id))

        if (!sitePermission) {
            render(status: 404, text: 'Link not found.')
            return
        }

        sitePermission.delete(flush: true)
        render(status: 200, text: 'Link deleted successfully.')
    }

    /**
     * Checks whether a link exists in the SitePermission table.
     * Accessible by authenticated users.
     */
   @Transactional
    def checkLinkExists() {
        String link = params.link
        String win = params.win


        if (!link||!win) {
            render(status: 400, text: 'Link and WIN is required.')
            return
        }

        // Normalize the link to base URL
        String baseUrl = extractBaseUrl(link)

        if (!baseUrl) {
            render(status: 400, text: 'Invalid URL format.')
            return
        }
        String username = getUsername(win).username
        //get the batch ids of this user from BatchUserDtl table and check if the correspnding link exists in SitePermission table
        def batchIds = BatchUserDtl.findAllByUsername(username).batchId
        boolean exists = SitePermission.exists {
            link == baseUrl && batchId in batchIds
        }



        render(contentType: 'application/json') {
            [linkExists: exists]
        }
    }

    /**
     * Utility method to extract the base URL from a full URL.
     * Removes path, query parameters, and fragments.
     */
    private String extractBaseUrl(String urlString) {
        try {
            def url = new URL(urlString)
            String protocol = url.getProtocol()
            String host = url.getHost()
            int port = url.getPort()

            // Construct the base URL
            String baseUrl = "${protocol}://${host}"
            if (port != -1 && port != url.getDefaultPort()) {
                baseUrl += ":${port}"
            }
            return baseUrl
        } catch (MalformedURLException e) {
            return null
        }
    }

    // Action to render the manageLinks page
    @Transactional
    def manageLinks() {
        String batchId = params.batchId ?: 'default'
        boolean isLibrarian = false
        if (session.getAttribute("userdetails") == null) {
            if(springSecurityService.currentUser!=null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
            }

        }
        if(session["userdetails"]!=null) {
            User user = session["userdetails"]
            if (user.authorities.any {
                it.authority == "ROLE_LIBRARY_ADMIN"
            }) {
                isLibrarian = true
            }
        }
        [batchId: batchId, isLibrarian: isLibrarian]
    }

    // Action to load links via AJAX with sorting
    @Transactional
    def loadLinks() {
        String batchId = params.batchId
        String sortField = params.sort ?: 'link'
        String sortOrder = params.order ?: 'asc'

        // Fetch links for the given batchId with sorting
        def links = SitePermission.createCriteria().list {
            eq('batchId', new Long(batchId))
            order(sortField, sortOrder)
        }

        // Prepare data for rendering
        def linkData = links.collect { link ->
            [
                    id: link.id,
                    link: link.link,
                    username: link.username,
                    dateCreated: link.dateCreated?.format('dd-MM-yyyy')
            ]
        }

        def json = [
                links: linkData
        ]
        render json as JSON
    }


}

