package com.wonderslate.drive

import com.wonderslate.cache.DataProviderService
import com.wonderslate.usermanagement.User
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import grails.converters.JSON

class RaaccessController {

    def springSecurityService
    def redisService
    DataProviderService dataProviderService

    /**
     * Action to generate a 6-digit alphanumeric code and store it with the current user's username.
     * URL: /raaccess/generateCode
     * Method: GET
     */
    @Transactional @Secured('ROLE_USER')
    def generateCode() {


        // Get the current user's username
        String username = springSecurityService.currentUser.username
        if (!username) {
            render(status: 500, text: 'Unable to retrieve user information')
            return
        }

        // Generate a 6-character alphanumeric code
        String code = generateRandomCode(6)

        // Store the code and username in Redis with an expiration time (e.g., 5 minutes)
        int expirationTime = 300 // seconds (5 minutes)
        String redisKey = "RA_CODE:${code}"
        try {
            redisService.set(redisKey, username)
            redisService.expire(redisKey, expirationTime)
        } catch (Exception e) {
            render(status: 500, text: 'Error storing code in Redis')
            return
        }

        // Return the code as JSON
        render([code: code] as JSON)
    }

    /**
     * Action to validate the code sent from the extension.
     * URL: /raaccess/validateCode
     * Method: POST
     * Parameters:
     * - code: The 6-digit alphanumeric code to validate
     */
    @Transactional
    def validateCode() {
        // Retrieve the code from request parameters
        String code = params.code

        if (!code) {
            render(status: 400, text: 'Code parameter is missing')
            return
        }

        // Construct Redis key
        String redisKey = "RA_CODE:${code}"

        // Retrieve the username associated with the code from Redis
        String username
        try {
            username = redisService.get(redisKey)
        } catch (Exception e) {
            render(status: 500, text: 'Error retrieving code from Redis')
            return
        }

        if (!username) {
            render(status: 400, text: 'Invalid or expired code')
            return
        }

        // Optionally, authenticate the user in the application context here

        // Remove the code from Redis to prevent reuse
        try {
            redisService.delete(redisKey)
        } catch (Exception e) {
            // Log error if necessary, but proceed
        }

        User user = dataProviderService.getUserMst(username)
        // Return success response with username
        render([usercode: user.win, status: 'Authenticated'] as JSON)
    }

    /**
     * Helper method to generate a random alphanumeric code of specified length.
     * @param length The length of the code to generate
     * @return A random alphanumeric string
     */
    private String generateRandomCode(int length) {
        String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        Random random = new Random()
        StringBuilder code = new StringBuilder()

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length())
            code.append(chars.charAt(index))
        }

        return code.toString()
    }
}
