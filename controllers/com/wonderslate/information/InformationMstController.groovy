package com.wonderslate.information

import grails.transaction.Transactional

class InformationMstController {




    @Transactional
    def image(){
        def file = new File("upload/information/"+params.fileName+"/")
        if (file.exists()) {
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${params.filename}\"")
            def fileInputStream = new FileInputStream(file)
            def outputStream = response.getOutputStream()
            byte[] buffer = new byte[4096];
            int len;
            while ((len = fileInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }

            outputStream.flush()
            outputStream.close()
            fileInputStream.close()
        } else render "";
    }
}
