package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.GptLogService
import com.wonderslate.data.MetainfoService
import com.wonderslate.data.UtilService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookPriceService
import grails.converters.JSON
import grails.transaction.Transactional

import javax.servlet.http.HttpServletRequest
import javax.sound.midi.Track
import java.beans.Transient

class MetainfoController {


    def redisService
    MetainfoService metainfoService
    DataProviderService dataProviderService
    UtilService utilService
    def springSecurityService
    GptLogService gptLogService
    BookPriceService bookPriceService

    def resetBookMetaInfo(){
        metainfoService.getAllChaptersMetaInfo(params.bookId)
        def json = ["status":"ok"]
        render json as JSON
    }

    @Transactional
    def hasBoughtTestSeries(){
        metainfoService.getAllChaptersMetaInfo(params.bookId)
        def json = ["hasBoughtTestSeries":utilService.hasBoughtTestSeries(springSecurityService.currentUser.username,new Integer(params.bookId))]
        render json as JSON
    }
    def getAllChaptersMetaInfo(){
        def bookId = params.bookId
        String jsonFilePath = grailsApplication.config.grails.basedir.path + "upload/books/" + bookId + "/metadata/json.txt.zip"
        File jsonFile = new File(jsonFilePath)
        if(redisService.("bookDefaultResources_"+bookId)==null){
            if(!jsonFile.exists()) metainfoService.getAllChaptersMetaInfo(bookId)
            else redisService.("bookDefaultResources_"+bookId) = "true"
        }
        response.setContentType("APPLICATION/OCTET-STREAM")
        response.setHeader("Content-Disposition", "Attachment;Filename=json.txt.zip")
        response.setHeader("Content-Length", "${jsonFile.length()}")
        def fileInputStream = new FileInputStream(jsonFile)
        def outputStream = response.getOutputStream()
        byte[] buffer = new byte[4096];
        int len;
        while ((len = fileInputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, len);
        }
        outputStream.flush()
        outputStream.close()
        fileInputStream.close()
    }

    def getPriceAndDescription(){
        def bookId = params.bookId
        String jsonFilePath = grailsApplication.config.grails.basedir.path + "upload/books/" + bookId + "/metadata/quiz/desc.txt.zip"
        File jsonFile = new File(jsonFilePath)
        if(redisService.("bookDescription_"+bookId)==null){
            if(!jsonFile.exists()) metainfoService.getPriceAndDescription(bookId)
            else redisService.("bookDescription_"+bookId) = "true"
        }
        response.setContentType("APPLICATION/OCTET-STREAM")
        response.setHeader("Content-Disposition", "Attachment;Filename=desc.txt.zip")
        response.setHeader("Content-Length", "${jsonFile.length()}")
        def fileInputStream = new FileInputStream(jsonFile)
        def outputStream = response.getOutputStream()
        byte[] buffer = new byte[4096];
        int len;
        while ((len = fileInputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, len);
        }
        outputStream.flush()
        outputStream.close()
        fileInputStream.close()
    }

    def quizQuestionAnswers(){
        def resId = params.resId
        String jsonFilePath = grailsApplication.config.grails.basedir.path + "upload/quiz/" + resId + "/quiz.txt.zip"
        File jsonFile = new File(jsonFilePath)
        if(redisService.("quiz_"+resId)==null){
            if(!jsonFile.exists()) metainfoService.quizQuestionAnswers(resId)
            else redisService.("quiz_"+resId) = "true"
        }
        response.setContentType("APPLICATION/OCTET-STREAM")
        response.setHeader("Content-Disposition", "Attachment;Filename=quiz.txt.zip")
        response.setHeader("Content-Length", "${jsonFile.length()}")
        def fileInputStream = new FileInputStream(jsonFile)
        def outputStream = response.getOutputStream()
        byte[] buffer = new byte[4096];
        int len;
        while ((len = fileInputStream.read(buffer)) > 0) {
            outputStream.write(buffer, 0, len);
        }
        outputStream.flush()
        outputStream.close()
        fileInputStream.close()
    }

    @Transactional
    def hasPurchasedGPT(){
        boolean purchasedGPT = false
        boolean showUpgrade = false

        if(springSecurityService.currentUser != null) {
            BooksPermission booksPermission = gptLogService.getBooksPermission(new Integer(params.bookId),springSecurityService.currentUser.username)
            if(booksPermission!=null){
                if(booksPermission.bookType!=null&&("bookgpt".equals(booksPermission.bookType.toLowerCase()) || "ebookgptupgrade".equals(booksPermission.bookType.toLowerCase())||"ibookgptpro".equals(booksPermission.bookType.toLowerCase()))) purchasedGPT = true
                else{
                    BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(params.bookId), "ebookGPTUpgrade")
                    println(bookPriceDtl)
                    if(bookPriceDtl!=null) showUpgrade = true
                }
            }
        }
        def json = ["purchasedGPT":purchasedGPT,"showUpgrade":showUpgrade,"prices": redisService.("bookPriceDetails_" + params.bookId)]
        render json as JSON
    }


}
