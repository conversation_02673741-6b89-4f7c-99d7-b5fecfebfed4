package com.wonderslate.qp

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.institute.OnlineTestService
import com.wonderslate.librarybooks.LibraryBooksService
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.transaction.Transactional
import grails.plugin.springsecurity.annotation.Secured
import groovy.json.JsonSlurper
import com.wonderslate.data.ChaptersMst

class QuestionPaperController {

    QuestionPaperService questionPaperService
    def springSecurityService
    DataProviderService dataProviderService
    def redisService
    OnlineTestService onlineTestService
    LibraryBooksService libraryBooksService


    @Secured(['ROLE_USER']) @Transactional
    def createPattern() {
       if(hasAccess()){
           HashMap userDtl = getInstituteDtl()
            def name = params.questionPaperName
            def description = params.description
            def createdBy = springSecurityService.currentUser.username
            def bookId = params.bookId ? new Long(params.bookId) : null

            def result = questionPaperService.createPattern(name, description, createdBy, userDtl!=null?userDtl.get("instituteId"):null, bookId)
            if (result.success) {
                flash.message = "Pattern created successfully."
                redirect(action: "listPatterns", params: [bookId: bookId])
            } else {
                flash.message = "Failed to create pattern: ${result.errors ?: result.message}"
                render(view: "createPattern")
            }
        }else{
            render("You are not authorized to create a pattern")
        }
    }


    @Secured(['ROLE_USER']) @Transactional
    def listPatterns() {
        if(hasAccess()) {
            // Check if user has ROLE_GPT_MANAGER role
            User user = User.findByUsername(springSecurityService.currentUser.username)
            boolean isGptManager = user.authorities.any { it.authority == "ROLE_GPT_MANAGER" }

            def patterns
            def bookTitle = null


                // Original logic for non-GPT_MANAGER users
                HashMap userDtl = getInstituteDtl()

                if (userDtl==null) {
                    flash.message = "Unauthorized access."
                    render "Unauthorized access."
                    return
                }

                def queryClosure = {
                    eq("instituteId", userDtl.get("instituteId")) // Restrict to user's institute
                    if (userDtl.get("userType") == "Instructor") {
                        eq("createdBy", springSecurityService.currentUser.username) // Restrict to papers created by the user
                    }
                    order('id', 'desc') // Show latest first
                }

                patterns = QuestionPaperPattern.createCriteria().list(queryClosure) // Fetch all patterns

                // If bookId is provided, fetch the book title
                if (params.bookId) {
                    try {
                        def booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
                        if (booksMst) {
                            bookTitle = booksMst.title
                        }
                    } catch (Exception e) {
                        log.error("Error fetching book title: ${e.message}")
                    }
                }
              if(patterns.size()==0){
                  patterns = QuestionPaperPattern.findAllByInstituteId(new Integer(1),[sort: 'name', order: 'desc'])
            }

            [patterns: patterns, bookTitle: bookTitle]
        } else {
            render("You are not authorized to view patterns")
        }
    }


    def addSection() {
        [patternId: params.long('patternId')]
    }


    @Secured(['ROLE_USER']) @Transactional
    def saveSection() {
        if(hasAccess()) {
            def patternId = params.long('patternId')
            def sectionName = params.sectionName
            def instructions = params.instructions
            def totalMarks = params.int('totalMarks')
            def createdBy = springSecurityService.currentUser.username

            def result = questionPaperService.addSection(patternId, sectionName, instructions, totalMarks, createdBy)
            if (result.success) {
                flash.message = "Section added successfully."
                redirect(action: "viewPattern", params: [patternId: patternId])
            } else {
                flash.message = "Failed to add section: ${result.errors ?: result.message}"
                render(view: "addSection", model: [patternId: patternId])
            }
        }else{
            render("You are not authorized to add a section")
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def addQuestionType() {
        def result = questionPaperService.getAllQuestionTypes()
        def questionTypes = result.success ? result.questionTypes : []
        [sectionId: params.long('sectionId'), questionTypes: questionTypes]
    }

    @Secured(['ROLE_USER']) @Transactional
    def saveQuestionType() {
        if(hasAccess()) {
            def sectionId = params.long('sectionId')
            def type = params.type
            def numberOfQuestions = params.int('numberOfQuestions')
            def marksPerQuestion = params.marksPerQuestion ? params.int('marksPerQuestion') : null
            def createdBy = springSecurityService.currentUser.username

            def result = questionPaperService.addQuestionType(sectionId, type, numberOfQuestions, marksPerQuestion, createdBy)
            if (result.success) {
                flash.message = "Question type added successfully."
                redirect(action: "viewPattern", params: [patternId: Section.get(sectionId).pattern.id])
            } else {
                flash.message = "Failed to add question type: ${result.errors ?: result.message}"
                redirect(action: "viewPattern", params: [patternId: Section.get(sectionId).pattern.id])
            }
        }else{
            render("You are not authorized to add a question type")
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def deleteSection() {
        if(hasAccess()) {
            Section section = Section.findById(params.long('sectionId'))
             section.delete(flush: true)
           def json = [status: "success"]
            render json as JSON
        }else{
            render("You are not authorized to delete a pattern")
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def deletePattern() {
        if(hasAccess()) {
            def patternId = params.long('patternId')
            def username = params.username

            def result = questionPaperService.deletePattern(patternId, username)
            if (result.success) {
                flash.message = "Pattern deleted successfully."
            } else {
                flash.message = "Failed to delete pattern: ${result.message}"
            }
            redirect(action: "listPatterns")
        }else{
            render("You are not authorized to delete a pattern")
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def viewPattern() {
        if(hasAccess()) {
            def patternId = params.long('patternId')
            def pattern = QuestionPaperPattern.findById(patternId)
            def creatorName = dataProviderService.getUserMst(pattern.createdBy).name

            if (!pattern) {
                flash.message = "Pattern not found."
                redirect(action: "listPatterns")
                return
            }
            // Sort sections by their creation order
            def sortedSections = pattern.sections.sort { it.id } // Or it.dateCreated

            [pattern: pattern, sections: sortedSections, creatorName: creatorName]
        }else{
            render("You are not authorized to view a pattern")
        }

    }

    @Secured(['ROLE_USER']) @Transactional
    def generateQuestionPaper(){
        if(hasAccess()) {
            //convert comma seperated chapterIds to list
            List chapterIds = params.chapterIds.split(',').collect { it as Long }
            int noOfSets = params.int('noOfSets')
            HashMap userDtl = getInstituteDtl()
            println("name: ${params.name}")
            questionPaperService.generateAndStoreQuestionPapers(new Long(params.patternId), noOfSets, springSecurityService.currentUser.username, chapterIds,params.name,userDtl.get("instituteId"))
            redirect(action: "listQuestionPapers")
        }else{
            render("You are not authorized to generate a question paper")
        }
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def viewQuestionPaper() {
        if(hasAccess()) {
            Long setId = params.long('id')
            def questionPaperSet = QuestionPaperSet.findById(setId)
            if (!questionPaperSet) {
                flash.message = "Question Paper Set not found."
                redirect(action: "listSets") // Redirect to list page if set not found
                return
            }

            //sort the sections inside the QuestionPaperSet based on the order of sections in the pattern
            questionPaperSet.sections = questionPaperSet.sections.sort { it.id }
            // current questions has objId, need to fetch the question details from ObjectiveMst
            HashMap questions = [:]
            questionPaperSet.sections.each { section ->
                section.questions.each { question ->
                    ObjectiveMst objectiveMst = ObjectiveMst.findById(question.objId)
                    questions.put(question.objId, objectiveMst.question)
                }
            }

            render(view: "viewQuestionPaper", model: [questionPaperSet: questionPaperSet, questions: questions])
        }else{
            render("You are not authorized to view a question paper")
        }
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def printQuestionPaper() {
        if(hasAccess()) {
            Long setId = params.long('id')
            def questionPaperSet = QuestionPaperSet.findById(setId)
            if (!questionPaperSet) {
                flash.message = "Question Paper Set not found."
                redirect(action: "listSets") // Redirect to list page if set not found
                return
            }
            // current questions has objId, need to fetch the question details from ObjectiveMst
            HashMap questions = [:]
            questionPaperSet.sections.each { section ->
                section.questions.each { question ->
                    ObjectiveMst objectiveMst = ObjectiveMst.findById(question.objId)
                    questions.put(question.objId, objectiveMst.question)
                }
            }

        [questionPaperSet: questionPaperSet, questions: questions]
        }else{
            render("You are not authorized to view a question paper")
        }
    }
    @Secured(['ROLE_USER'])
    @Transactional
    def saveQuestionPaper() {
        if(hasAccess()) {
            Long setId = params.long('id')
            def questionPaperSet = QuestionPaperSet.get(setId)
            if (!questionPaperSet) {
                flash.message = "Question Paper Set not found."
                redirect(action: "listSets")
                return
            }

            // Update header and section descriptions
            questionPaperSet.header = params.header
            questionPaperSet.sections.each { section ->
                section.description = params["section_${section.id}"]
                section.save(flush: true)
            }
            questionPaperSet.save(flush: true)

            flash.message = "Question Paper updated successfully."
            def json =  [id: setId]
            render json as JSON
        }else{
            render("You are not authorized to save a question paper")
        }
    }



    @Secured(['ROLE_USER']) @Transactional
    def hasAccess(){
        String username = springSecurityService.currentUser.username
        User user = User.findByUsername(springSecurityService.currentUser.username)

        // Check if user has ROLE_GPT_MANAGER role - return true immediately if they do
        if(user.authorities.any { it.authority == "ROLE_GPT_MANAGER" }) {
            return true
        }

        // Otherwise, check the original conditions
        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndUserType(username,"Instructor")
        if(user.authorities.any { it.authority == "ROLE_INSTITUTE_MANAGER" } || batchUserDtl != null) {
            return true
        } else {
            return false
        }
    }


    @Secured(['ROLE_USER']) @Transactional
    def getInstituteDtl(){
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsername(user.username)
        if(batchUserDtl!=null){
            HashMap userDtl = new HashMap()
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchUserDtl.batchId)
            userDtl.put("instituteId",courseBatchesDtl.conductedBy)
            userDtl.put("userType",batchUserDtl.userType)
            return userDtl
        }else{
            return null
        }
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def listQuestionPapers() {
        int max = params.int('max') ?: 20
        int offset = params.int('offset') ?: 0
        HashMap userDtl = getInstituteDtl()

        if (userDtl==null) {
            flash.message = "Unauthorized access."
            render "Unauthorized access."
            return
        }

        def queryClosure = {
            eq("instituteId", userDtl.get("instituteId")) // Restrict to user's institute
            if (userDtl.get("userType") == "Instructor") {
                eq("createdBy", springSecurityService.currentUser.username) // Restrict to papers created by the user
            }
              order('id', 'desc') // Show latest first
        }

        // Fetch a detached listz
        def questionPaperSets = QuestionPaperSet.createCriteria().list(max: max, offset: offset, queryClosure).collect { set ->
            [
                    id        : set.id,
                    groupId   : set.groupId,
                    createdBy : set.createdBy,
                    dateCreated: set.dateCreated,
                    name : dataProviderService.getUserMst(set.createdBy).name,
                    questionPaperName: set.name
            ]
        }

        int totalSets = QuestionPaperSet.createCriteria().count(queryClosure)

        // Fetch all sets grouped by groupId
        def groupedQuestionPapers = [:] // Map to hold sets for each groupId
        questionPaperSets.each { set ->
            def setsInGroup = QuestionPaperSet.findAllByGroupId(set.groupId)
            groupedQuestionPapers[set.groupId] = setsInGroup
        }

        [
                questionPaperSets: questionPaperSets,
                groupedQuestionPapers: groupedQuestionPapers, // Send grouped sets
                totalSets: totalSets,
                max: max,
                offset: offset
        ]
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def createQuestionPaperPage() {
        HashMap userDtl = getInstituteDtl()

        // Fetch available question paper patterns, sorted by name
        def questionPaperPatterns = QuestionPaperPattern.createCriteria().list {
            eq("instituteId", userDtl.get("instituteId"))
            order("name", "asc")
        }

        if(questionPaperPatterns.size()==0){
            questionPaperPatterns = QuestionPaperPattern.findAllByInstituteId(new Integer(1),[sort: 'name', order: 'desc'])
        }

        // Fetch batches
        String username = springSecurityService.currentUser.username

        List institutes = onlineTestService.getInstitutesForUser("" + session['siteId'])

        // Check if user is an Instructor or Manager
        boolean isInstructor = false
        institutes.each { institute ->


            if ("Instructor".equals(institute.userType) || "Manager".equals(institute.userType)) {
                isInstructor = true
            }
        }

        render(view: "createQuestionPaperPage", model: [
                questionPaperPatterns: questionPaperPatterns,
                institutes: institutes,
                isInstructor: isInstructor
        ])
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getChaptersForBooks() {

        def bookIds = params.bookIds.split(',').collect { it as Long }
        if (!bookIds) {
            render(status: 400, text: "No books selected.")
            return
        }
        println("number of bookIds: ${bookIds.size()}")

        // Fetch chapters grouped by bookId
        def chaptersByBook = [:]
        bookIds.each { bookId ->
            def chapters = ChaptersMst.findAllByBookId(bookId)
            chaptersByBook[bookId] = chapters.collect { chapter ->
                [id: chapter.id, name: chapter.name]
            }
        }
def json = [chaptersByBook: chaptersByBook]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def getMarksForQA(chapterId){
        if(redisService.("marksForQA_"+chapterId)!=null) return redisService.("marksForQA_"+chapterId)
        else {
            String sql = "select marks,count(marks) from objective_mst om, resource_dtl rd \n" +
                    "where rd.chapter_id="+chapterId+" and rd.res_type='QA'\n" +
                    "and om.quiz_id=rd.res_link \n" +
                    "group by marks\n" +
                    "order by marks "
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            String marks = ""
            String tempMarks

            results.each { res ->
                tempMarks = "" + res[0]
                if (tempMarks.contains(".")) tempMarks = tempMarks.substring(0, tempMarks.indexOf("."))
                marks += " Questions: " + res[1] + " : Marks: " + tempMarks+", "
            }
            if (!"".equals(marks)) marks = " (" + marks + ")"
            redisService.("marksForQA_" + chapterId) = marks
            return marks
        }
    }
    @Transactional @Secured(['ROLE_USER'])
    def getSubjectsForClass() {
        String sql ="select group_concat(book_id) from books_batch_dtl where batch_id in ("+ libraryBooksService.getAllBatchIds(params.batchId)+")"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

        sql = "select distinct(btd.subject) from  books_tag_dtl btd, chapters_mst cm,resource_dtl rd where "+
                " cm.book_id in ("+results[0][0]+") and btd.book_id=cm.book_id and rd.chapter_id=cm.id "+
                " order by btd.subject"
        println("sql: "+sql)
        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new SafeSql(dataSource)
        results = sql1.rows(sql)
        render results as JSON
    }
}
