package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.discussions.Questions
import com.wonderslate.publish.Polls
import com.wonderslate.publish.PollsDetails
import com.wonderslate.usermanagement.PollRanks
import com.wonderslate.usermanagement.PollsResults
import com.wonderslate.usermanagement.PollsUserAnswers
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.apache.commons.io.FileUtils
import org.imgscalr.Scalr
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest

import javax.imageio.ImageIO
import java.awt.image.BufferedImage

class PollsController {
    def springSecurityService
    def redisService
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService

    def index() { }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def polls(){
        long resId = Long.parseLong(params.id)
        [resId:resId]
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def addPolls(){
//        ModelMapper modelMapper = new ModelMapper()
        String jsonObj = request.getJSON()
        def jsonList = new JsonSlurper().parseText(jsonObj)
        String resIdStr = jsonList.resId
        Polls polls = new Polls() //modelMapper.map(jsonList,Polls.class)
        polls.name = jsonList.name
        polls.question = jsonList.question
        polls.imgName = jsonList.imgName
        polls.resId = Long.parseLong(resIdStr)
        polls.save(failOnError: true, flush: true)

        List details = jsonList.details
        for(def detail:details){
            PollsDetails pollsDetails = new PollsDetails()
            pollsDetails.optionText = detail.optionText
            pollsDetails.correctOption = detail.correctOption
            pollsDetails.imgName = detail.imgName
            pollsDetails.pollsId = polls.id
            pollsDetails.save(failOnError: true, flush: true)
        }

        def json = [polls:polls]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def updatePolls(){
//        ModelMapper modelMapper = new ModelMapper()
        String jsonObj = request.getJSON(),imgName = ""
        def jsonList = new JsonSlurper().parseText(jsonObj)
        imgName = jsonList.imgName
        Polls polls = Polls.findById(jsonList.id)
        polls.name = jsonList.name
        polls.question = jsonList.question
        if(imgName != null && !imgName.isEmpty()) polls.imgName = imgName
        PollsDetails.executeUpdate("delete PollsDetails pd where pd.pollsId = :pollsId", [pollsId:polls.id])
        polls.save(failOnError: true, flush: true)

        List details = jsonList.details
        for(def detail:details){
            PollsDetails pollsDetails = new PollsDetails()
            pollsDetails.optionText = detail.optionText
            pollsDetails.correctOption = detail.correctOption
            if(detail.imgName != null && !detail.imgName.isEmpty()) pollsDetails.imgName = detail.imgName
            pollsDetails.pollsId = polls.id
            pollsDetails.save(failOnError: true, flush: true)
        }

        def json = [polls:polls]
        render json as JSON
    }


    @Secured(['ROLE_USER'])
    @Transactional
    def getPollingByResId(){
        String resIdStr = params.id
        long resId = Long.parseLong(resIdStr)
        List<Polls> polls = Polls.findAllByResId(resId)
        def json = [polls:polls]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getPollingDetailsByPollId(){
        String pollsIdStr = params.id
        long pollsId = Long.parseLong(params.id)
        Polls polls = Polls.findById(pollsId)
        List<PollsDetails> details = PollsDetails.findAllByPollsId(pollsId)
        def json = [details:details,polls:polls]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getAllPollingByResId(){
        Integer resId = new Integer(params.resId)
        if(redisService.("allPollsForResId_"+resId)==null) dataProviderService.getAllPollingByResId(resId)
        def json = [polls:redisService.("allPollsForResId_"+resId)]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def resetPollsCache(){
        Integer resId = new Integer(params.resId)
        dataProviderService.getAllPollingByResId(resId)
        def json = [polls:redisService.("allPollsForResId_"+resId)]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def deletePolls(){
        String pollsId = params.id
        Polls polls = Polls.findById(Long.parseLong(pollsId))
        PollsDetails.executeUpdate("delete PollsDetails pd where pd.pollsId = :pollsId", [pollsId:polls.id])
        def json = [polls:polls.delete()]
        render json as JSON
    }

    @Transactional
    def savePollsImages(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file")
        if(file != null) savePollsImage(file,"upload/polls/"+params.id+"/"+params.pollsId+"/")
        file = multiRequest.getFile("option1")
        if(file != null) savePollsImage(file,"upload/polls/"+params.id+"/options/"+params.pollsId+"/")
        file = multiRequest.getFile("option2")
        if(file != null) savePollsImage(file,"upload/polls/"+params.id+"/options/"+params.pollsId+"/")
        file = multiRequest.getFile("option3")
        if(file != null) savePollsImage(file,"upload/polls/"+params.id+"/options/"+params.pollsId+"/")
        file = multiRequest.getFile("option4")
        if(file != null) savePollsImage(file,"upload/polls/"+params.id+"/options/"+params.pollsId+"/")
        def json = [polls:'']
        render json as JSON
    }

     void savePollsImage(MultipartFile file,String path){
        File uploadDir = new File(path)
        if(!uploadDir.exists()) uploadDir.mkdirs()
        //creating directory to process images
        File uploadDir1 = new File(uploadDir.absolutePath+"/processed")
        if(!uploadDir1.exists()) uploadDir1.mkdirs()
        String filename=file.originalFilename
        filename=filename.replaceAll("\\s+","")
        BufferedImage image = ImageIO.read(file.getInputStream())

        ByteArrayOutputStream baos = new ByteArrayOutputStream()
        ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
        baos.flush()
        byte[] scaledImageInByte = baos.toByteArray()
        baos.close()

        baos = new ByteArrayOutputStream()
        ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
        baos.flush()
        byte[] scaledImageInByte1 = baos.toByteArray()
        baos.close()

        baos = new ByteArrayOutputStream()
        ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
        baos.flush()
        byte[] scaledImageInByte2 = baos.toByteArray()
        baos.close()

        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

        //saving original image finally
        file.transferTo(new File(uploadDir.absolutePath+"/"+file.filename))
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def addUserPollsAnswer(){
        boolean quiz = false;
        def json

        boolean valid = true
        if (redisService.("poll_"+params.resId) == null) {
            valid = false
            json = [status:"completed"]
        }else {
            Polls polls = Polls.findById(new Integer(""+redisService.("poll_"+params.resId)))
            Integer pollsId = new Integer(""+redisService.("poll_"+params.resId))
            Integer pollDetailId = new Integer(-1)
            PollsUserAnswers pollsUserAnswer = PollsUserAnswers.findByUserNameAndPollsId(springSecurityService.currentUser.username, pollsId)
            if (pollsUserAnswer != null) {
                valid = false
                json = [status: "duplicate"]

            }

            if (valid) {
                if (redisService.("CorrectAnswerPollDetails_" + pollsId) == null) dataProviderService.getPollCorrectAnswer(pollsId)
                PollsUserAnswers pollsUserAnswers = new PollsUserAnswers()
                pollsUserAnswers.pollsId = pollsId
                if(redisService.("PollDetailsId_"+pollsId+"_"+params.pollSelected)!=null) pollDetailId = new Integer(redisService.("PollDetailsId_"+pollsId+"_"+params.pollSelected))
                 pollsUserAnswers.pollsDetailsId =  pollDetailId
                pollsUserAnswers.userName = springSecurityService.currentUser.username
                Date currentDate = new Date();
                int timeDiffInMillSeconds = (currentDate.getTime() - Long.parseLong(redisService.("pollStartTime_" + params.resId)));
                pollsUserAnswers.duration = timeDiffInMillSeconds
                if (redisService.("CorrectAnswerPollDetailsId_" + pollsId).equals(""+pollDetailId)) pollsUserAnswers.correctAnswer = "true"
                pollsUserAnswers.save(failOnError: true, flush: true)
                json = [status: "submitted"]

            }
        }
        render json as JSON
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def submitPollsAndCalculateResults(pollId){
        Polls polls = Polls.findById(pollId)
        List allEntries = PollsUserAnswers.findAllByPollsId(pollId)
        List pollDetails = PollsDetails.findAllByPollsId(pollId,[sort: "id", order: "asc"])
        int[] votesCountArray = new int[pollDetails.size()]
        //initialize the count to 0 first
        for(int i=0;i<pollDetails.size();i++) votesCountArray[i]=0;

        //go through all votes and add it to corresponding options
       allEntries.each {entry->
           for(int i=0;i<pollDetails.size();i++){
               if(pollDetails[i].id.intValue()==entry.pollsDetailsId.intValue()) {
                   votesCountArray[i] +=1
                   break
               }
           }
       }
       PollsResults pollsResults = new PollsResults()
        pollsResults.pollId = pollId
        pollsResults.totalCount = allEntries.size()
        if(pollDetails.size()>0) pollsResults.option1Count = votesCountArray[0]
        if(pollDetails.size()>1) pollsResults.option2Count = votesCountArray[1]
        if(pollDetails.size()>2) pollsResults.option3Count = votesCountArray[2]
        if(pollDetails.size()>3) pollsResults.option4Count = votesCountArray[3]
        pollsResults.save(failOnError: true, flush: true)

        List topTen = PollsUserAnswers.findAllByPollsIdAndCorrectAnswer(pollId,"true",[max: 10, sort: "dateCreated", order: "asc"])
        int rank = 1
        topTen.each { userAnswer->
            int milliSecondsDifference = userAnswer.dateCreated.getTime() - polls.dateCreated.getTime()
            PollRanks pollRanks = new PollRanks()
            pollRanks.pollId = pollId
            pollRanks.rank = new Integer(rank)
            pollRanks.userName = userAnswer.userName
            pollRanks.timeTaken = milliSecondsDifference
            pollRanks.save(failOnError: true, flush: true)
            rank++
        }

        polls.status="resultsComputed"
        polls.save(failOnError: true, flush: true)
       return
    }

    @Secured(['ROLE_USER']) @Transactional
    def pollResults(){
        if(params.resId!=null){
            int pollCount = 0;
            List polls = Polls.findAllByResId(new Integer(params.resId))
            polls.each { poll->
                if("completed".equals(poll.status)||"resultsComputed".equals(poll.status)||"published".equals(poll.status)) pollCount++
                if("completed".equals(poll.status)) submitPollsAndCalculateResults(poll.id)

            }
            dataProviderService.getConsolidatedPollRanks(new Integer(params.resId))
            [resId: params.resId, pollCount:pollCount]
        }else {
            Integer pollId = new Integer(params.pollId)
            Polls polls = Polls.findById(pollId)
            List pollsDetails = PollsDetails.findAllByPollsId(pollId)
            if ("completed".equals(polls.status)) submitPollsAndCalculateResults(pollId)
            [polls: polls, details: pollsDetails]
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def getPollResults(){
        Integer pollId = new Integer(params.pollId)
        def rankDetails = null
        if(redisService.("pollResults_" +pollId)==null) dataProviderService.getPollResults(pollId)
        if(redisService.("CorrectAnswerPollDetails_"+pollId)==null) dataProviderService.getPollCorrectAnswer(pollId)

        //get ranks only for quiz not for polls
        if("quiz".equals(redisService.("CorrectAnswerPollDetails_"+pollId))){
            if(redisService.("pollRank_"+pollId)==null) dataProviderService.getPollRanks(pollId)
            rankDetails = redisService.("pollRank_"+pollId)
        }
       def json = [results: redisService.("pollResults_"+pollId), rankDetails:rankDetails, options:redisService.("CorrectAnswerPollDetailsId_"+pollId)]
        render json as JSON

    }
    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def publishPollResults(){
        Integer pollId = new Integer(params.pollId)
        Polls polls = Polls.findById(pollId)
        polls.status = "published"
        polls.save(failOnError: true, flush: true)

        def json = ['status':'published']
        render json as JSON

    }
    @Secured(['ROLE_USER'])
    @Transactional
    def getPollResultsAll(){
        Integer resId = new Integer(params.resId)
        def rankDetails = null
        if(redisService.("consolidatedPollRank_"+resId)==null) dataProviderService.getConsolidatedPollRanks(resId)

        List polls = Polls.findAllByResIdAndStatus(new Integer(params.resId),"published")

        List pollResults = polls.collect{ poll ->
             rankDetails = null
            if(redisService.("pollResults_" +poll.id)==null) dataProviderService.getPollResults(poll.id)
            if(redisService.("CorrectAnswerPollDetails_"+poll.id)==null) dataProviderService.getPollCorrectAnswer(new Integer(""+poll.id))

            //get ranks only for quiz not for polls
            if("quiz".equals(redisService.("CorrectAnswerPollDetails_"+poll.id))){
                if(redisService.("pollRank_"+poll.id)==null) dataProviderService.getPollRanks(new Integer(""+poll.id))
                rankDetails = redisService.("pollRank_"+poll.id)
            }
            PollsUserAnswers pollsUserAnswers = PollsUserAnswers.findByPollsIdAndUserName(poll.id,springSecurityService.currentUser.username)
             return [results: redisService.("pollResults_"+poll.id), rankDetails:rankDetails,userAnswerId:pollsUserAnswers!=null?pollsUserAnswers.pollsDetailsId:""]
        }
        if(redisService.("allPollsForResId_"+resId)==null) dataProviderService.getAllPollingByResId(resId)

        def json = [rankDetails:redisService.("consolidatedPollRank_"+resId), pollResults : pollResults, allPollDetails:redisService.("allPollsForResId_"+resId)]
        render json as JSON

    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getPollsImages(String resId, String fileName, String pollsId, String imgType) {
        try {
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
//                String picFileName = fileName.substring(0, fileName.indexOf(".")) + '_' + imgType + fileName.substring(fileName.indexOf("."))
                def file = null
                if(imgType.equals("options")) file = new File("upload/polls/" + resId + "/options/" + pollsId + "/" + fileName)
                else if(imgType.equals("polls"))file = new File("upload/polls/" + resId + "/" + pollsId + "/" + fileName)

                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showDoubtImage "+e.toString())
            render "";

        }
    }
}
