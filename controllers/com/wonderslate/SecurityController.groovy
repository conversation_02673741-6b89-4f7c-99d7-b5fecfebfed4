package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.usermanagement.AuthenticationToken
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserLogService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.userdetails.NoStackUsernameNotFoundException
import grails.transaction.Transactional
import org.grails.web.util.WebUtils
import org.springframework.security.authentication.CredentialsExpiredException
import org.springframework.security.authentication.DisabledException
import org.springframework.security.authentication.LockedException
import org.springframework.security.web.WebAttributes
import org.springframework.security.web.authentication.session.SessionAuthenticationException

import javax.security.auth.login.AccountExpiredException
import javax.servlet.http.Cookie
import java.time.Instant

class SecurityController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserLogService userLogService
    def redisService
    def sessionRegistry

    @Transactional
    def loginmanager() {
        println("coming here man for ogin")
        if("false".equals(session["NumberExceeded"])) {
            String entryController = g.cookie(name: 'siteName')
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
            }

            if("sage".equals(entryController)) {
                session['siteId'] = new Integer(9)
                session.setAttribute("entryController", "sage")
                redirect([uri: '/sage?siteName=sage&isbn='+session.getAttribute("isbn")])
            } else {

                  if("evidya".equals(entryController) || "etexts".equals(entryController)){
                      redirect([uri: '/'+entryController+'/store'])
                  } else if("ebouquet".equals(entryController)){
                      redirect([uri: '/library'])
                  } else if("wolterskluwer".equals(entryController)){
                      redirect([uri: '/wlibrary/library'])
                  }else if("books".equals(entryController) ){
                      if("true".equals(params.forgottenPassword)){
                          redirect([uri: '/creation/userProfile?forgottenPassword=true'])
                      }
                      else redirect([uri: '/wsLibrary/myLibrary'])
                  } else if("ebooksaccess".equals(entryController)){
                      redirect([uri: '/'+entryController+'/index'])
                  } else {
                      if("true".equals(params.forgottenPassword)){
                          redirect([uri: '/creation/userProfile?forgottenPassword=true'])
                      }
                      else redirect([uri: '/wsLibrary/myLibrary'])
                  }

            }
        } else {
            flash.message="Sorry, you have already signed in from  ["+grailsApplication.config.grails.appServer.maximumSessions+"] devices. To login here, please sign out from other device!"
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect( [uri: '/logoff'])
        }
    }

    def loginfailedmanager(){
        String entryController=g.cookie(name: 'siteName')
        String wlEntryController = g.cookie(name: 'wlSiteName')
        String plEntryController = g.cookie(name: 'plSiteName')

        if(g.cookie(name: 'siteName')!=null) {
            if("sage".equals(entryController)) {
                if("student".equals(session["sageUserType"]))
                    redirect(controller: entryController, action: 'studentResourcesLocked', params: ['mode': 'loginFailed', isbn: session.getAttribute("isbn")])
                else if("instructor".equals(session["sageUserType"]))
                    redirect(controller: entryController,action:'instructorResourcesLocked', params: ['mode': 'loginFailed', isbn: session.getAttribute("isbn")])
                else
                    redirect(controller: entryController, params: ['mode': 'loginFailed', isbn: session.getAttribute("isbn")])
            } else {
                if(g.cookie(name: 'SimulError')!=null && g.cookie(name: 'SimulError')=="Fail") {
                    Cookie[] cookies = request.getCookies()

                    flash.message="Sorry, you have already signed in from  ["+grailsApplication.config.grails.appServer.maximumSessions+"] devices. To login here, please sign out from other device!"
                } else {
                    flash.message=captureAuthFailMsg()
                }
                if(wlEntryController!=null&&!"".equals(wlEntryController)){
                    redirect( [uri: '/' +wlEntryController+ '/welcome?loginFailed=true'])
                } else if("privatelabel".equals(entryController)){
                    redirect([uri: '/sp/' + plEntryController + '/store?loginFailed=true'])
                }else {
                    redirect([uri: '/' + entryController + '/store?loginFailed=true'])
                }
            }
        } else {
            redirect( [uri: '/register'])
         }
    }

    /** Callback after a failed login. Redirects to the auth page with a warning message. */
    def captureAuthFailMsg() {
        String msg = ''
        def exception = session[WebAttributes.AUTHENTICATION_EXCEPTION]
        if (exception) {
            if (exception instanceof AccountExpiredException) {
                msg = message(code: 'springSecurity.errors.login.expired')
            } else if (exception instanceof CredentialsExpiredException) {
                msg = message(code: 'springSecurity.errors.login.passwordExpired')
            } else if (exception instanceof DisabledException) {
                msg = message(code: 'springSecurity.errors.login.disabled')
            } else if (exception instanceof LockedException) {
                msg = message(code: 'springSecurity.errors.login.locked')
            } else if (exception instanceof SessionAuthenticationException){
                msg = exception.getMessage()
            } else {
                msg = message(code: 'springSecurity.errors.login.fail')
            }
        }

        return msg
    }

    def loginform(){
        String entryController=g.cookie(name: 'siteName');
        String plEntryController = g.cookie(name: 'plSiteName')

        if("currentaffairs".equals(entryController) || "neet".equals(entryController) || "enggentrances".equals(entryController) || "karnataka".equals(entryController) || "cacscma".equals(entryController) || "ctet".equals(entryController)){
            redirect( [uri: '/'+entryController+'/eBooks?mode=loginform'])
        }else if("privatelabel".equals(entryController)) {
            redirect( [uri: '/sp/'+plEntryController+'/store?mode=loginform'])
        }else{
            redirect( [uri: '/'+entryController+'/store?mode=loginform'])
        }
    }

   def logout(){
        sessionRegistry.removeSessionInformation(request.getSession().getId())
        String entryController=g.cookie(name: 'siteName')
        String wlEntryController = g.cookie(name: 'wlSiteName')
        String plEntryController = g.cookie(name: 'plSiteName')
        String instituteUrlName = g.cookie(name: 'instituteUrlName')
          if(instituteUrlName!=null&&!"".equals(instituteUrlName)) {
              redirect( [uri: '/institution/' +instituteUrlName])
          }
           else if(wlEntryController!=null&&!"".equals(wlEntryController)){
               redirect( [uri: '/' +wlEntryController+ '/welcome'])
           }else {
               String url = ""
               if ("books".equals(entryController)) {
                   url = "/store"
               }else if("currentaffairs".equals(entryController) || "neet".equals(entryController) || "enggentrances".equals(entryController) ||"karnataka".equals(entryController) || "cacscma".equals(entryController) || "ctet".equals(entryController)){
                   url = '/prepjoy/'+entryController
               } else if("privatelabel".equals(entryController)){
                   url = "/sp/"+plEntryController
               } else url = "/index"
               if (entryController != null && !"".equals(entryController)) {
                   if("sage".equals(entryController)) {
                           String userType = g.cookie(name: 'userType')
                           String isbn = g.cookie(name: 'isbn')

                           if(isbn ==null){
                               redirect([uri: '/sage/badLink?error=noisbn&siteName=sage'])
                           } else {
                               session["isbn"] =isbn

                               if(userType ==null){
                                   redirect([uri: '/sage?isbn= ' +isbn +'&siteName=sage'])
                               } else {
                                   redirect([uri: '/sage?isbn= ' +isbn])
                               }
                           }

                       }else if("currentaffairs".equals(entryController) || "neet".equals(entryController) || "enggentrances".equals(entryController) || "karnataka".equals(entryController) || "cacscma".equals(entryController)|| "ctet".equals(entryController)){
                           redirect( [uri: url])
                       } else if("privatelabel".equals(entryController)) {
                            redirect( [uri: url])
                       } else {
                       String baseUrl = request.getRequestURL().substring(0,request.getRequestURL().indexOf(request.getRequestURI()))

                       redirect([url: baseUrl+'/' + entryController + url])
                   }

               } else {
                   // println(request.getRequestURL());
                   redirect( [uri: url])
               }
           }

    }

    @Transactional
    def mobileLogout(){
        AuthenticationToken authenticationToken = AuthenticationToken.findByToken(params.token)
        if(authenticationToken!=null) authenticationToken.delete(flush: true)

        def json = ["status":"logged out"]
        render json as JSON
    }

    @Transactional
    def  checkNumberOfSimultaneousUsers(){
        def allowLogin
        def otpDone = null
        def email = null

        try {
            allowLogin=userLogService.checkNumberOfSimultaneousUsers(params.username)
        } catch (NoStackUsernameNotFoundException e) {
            allowLogin="User does not exist"
        }

        if("Allow".equals(allowLogin)) {
            otpDone = checkOtpDone(params.username)

            if (otpDone != null && otpDone.contains('^')) {
                email = otpDone.substring(0, otpDone.indexOf('^'))
                otpDone = otpDone.substring(otpDone.indexOf('^') + 1) == "null" ? null : otpDone.substring(otpDone.indexOf('^') + 1)
            }
        }
        if("null".equals(email)||"".equals(email)||email==null) email="Does not exist"
        def json = [
                allowLogin : allowLogin,
                otpDone : otpDone,
                noOfMobileUsers : AuthenticationToken.findAllByUsername(params.username).size(),
                email : email
        ]

        render json as JSON
    }

    def checkOtpDone(String username){
        User user = User.findByUsername(username);
        return user!=null?user.email+"^"+user.otpFinished:null
    }
    def Integer getSiteId(request){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(params.siteId!=null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

}
