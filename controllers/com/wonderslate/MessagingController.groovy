package com.wonderslate
import com.wonderslate.data.Message

import com.wonderslate.data.MessageDtl
import com.wonderslate.data.MessageTimeUserMap
import com.wonderslate.usermanagement.GroupsDtl
import com.wonderslate.usermanagement.User
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import groovy.sql.Sql
import org.joda.time.*
import grails.converters.JSON

class MessagingController {
    SpringSecurityService springSecurityService
    def index() {}

    @Secured(['ROLE_USER'])
	def messages(){

        if(springSecurityService.currentUser!=null&&session.getAttribute("userdetails")==null){
            session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
        }
        if(params.friendId!=null){
            [title: 'Messages',friendId:params.friendId,friendNm:params.friendNm,profilepic:params.profilepic,usertype:params.usertype]
        }else{
            [title: 'Messages']
        }
	}



    @Secured(['ROLE_USER'])
    def submitMessage() {
	    Message msg = new Message(message:params.message,messagetype: params.messagetype);
		msg.save(failOnError: true,flush: true)
        List unreadMessages;
		boolean groupMessage=true;
		if("user".equals(params.usertype)) groupMessage=false;

		MessageTimeUserMap messageTimeUserMap
		if(groupMessage){
			 messageTimeUserMap = MessageTimeUserMap.findByGroupIdAndMessagetype(new Long(params.id),params.messagetype)
			if(messageTimeUserMap!=null){
				messageTimeUserMap.lastUpdated = new Date()
				messageTimeUserMap.save(flush: true)
			}
			else {
				messageTimeUserMap = new MessageTimeUserMap(groupId:new Long(params.id),messagetype: params.messagetype)
				messageTimeUserMap.save(failOnError: true,flush: true)
			}

            List groupMembers = GroupsDtl.findAllByGroupId(new Integer(params.id))
            Long touserid;
            groupMembers.each { groupMember ->
                    touserid = (User.findByUsername(groupMember.username)).id
                MessageDtl messageDtl

                if((""+session['userdetails'].id).equals(""+touserid))
                    messageDtl = new MessageDtl(msgId: msg.id,fromUserId:session['userdetails'].id,toUserId: touserid, groupId:new Long(params.id) , groupMsg: "true",dateChecked: new Date())
                else messageDtl = new MessageDtl(msgId: msg.id,fromUserId:session['userdetails'].id,toUserId: touserid, groupId:new Long(params.id) , groupMsg: "true")

                messageDtl.save(failOnError: true,flush: true)
            }
		}else{

			int from = Integer.parseInt(""+session['userdetails'].id)
			int to = Integer.parseInt(""+params.id)

			//logic to avoid two database reads
			if(from > to )  messageTimeUserMap = MessageTimeUserMap.findByGreaterUserIdAndLesserUserIdAndMessagetype(new Long(from),new Long(to),params.messagetype)
			else messageTimeUserMap = MessageTimeUserMap.findByGreaterUserIdAndLesserUserIdAndMessagetype(new Long(to),new Long(from),params.messagetype)

			if(messageTimeUserMap!=null) {
				messageTimeUserMap.lastUpdated = new Date()
				messageTimeUserMap.save(flush: true)
			}
			else {
				if(from > to ) messageTimeUserMap = new MessageTimeUserMap(greaterUserId: new Long(from),lesserUserId: new Long(to),messagetype: params.messagetype)
				else messageTimeUserMap = new MessageTimeUserMap(greaterUserId: new Long(to),lesserUserId: new Long(from),messagetype: params.messagetype)
				messageTimeUserMap.save(failOnError: true,flush: true)
			}

			MessageDtl messageDtl = new MessageDtl(msgId: msg.id,fromUserId:session['userdetails'].id,toUserId: new Long(params.id),groupMsg: "false")
			messageDtl.save(failOnError: true,flush: true)


        }
        unreadMessages = getUnreadMessages(params.id,params.messagetype);
        def json =[
                'results': unreadMessages.reverse(),
                'status': unreadMessages ? "OK" : "Nothing present"
        ]
        render json as JSON

    }


    @Secured(['ROLE_USER'])
	def getLatestMessageUsersList(){
		def sql="select mt.last_updated updated,u.name,u.id,u.profilepic,'user' usertype from message_time_user_map mt, user u " +
				" where mt.greater_user_id=" +session['userdetails'].id+
				" and u.id=mt.lesser_user_id" +
                " and mt.messagetype='" +params.messagetype+"'"+
				" union " +
				" select mt.last_updated updated,u.name,u.id,u.profilepic, 'user' usertype from message_time_user_map mt, user u " +
				" where mt.lesser_user_id=" +session['userdetails'].id+
                " and mt.messagetype='" +params.messagetype+"'"+
				" and u.id=mt.greater_user_id " +
                " union " +
                " select mt.last_updated updated,g.name,g.id,g.profilepic, 'group' usertype from message_time_user_map mt,groups g,groups_dtl gd " +
                " where gd.username='" +session['userdetails'].username+"'"+
                " and g.id=gd.group_id " +
                " and mt.group_id=g.id " +
                " and mt.messagetype='" +params.messagetype+"'"+
                "order by updated desc"
      	def dataSource = grailsApplication.mainContext.getBean('dataSource')
		def sql1 = new Sql(dataSource)
		def usersList = sql1.rows(sql);

		List userMessagesList = usersList.collect{comp ->
			return [lastMessageDate: comp[0], name: comp[1],messagerId:comp[2],profilepic:comp[3],usertype:comp[4]]
		}
		def json =[
				'results': userMessagesList,
				'status': userMessagesList ? "OK" : "Nothing present"
		]
        render json as JSON

	}

    @Secured(['ROLE_USER'])
	def getLatestUnreadMessageCount(){
		def sql="select md.from_user_id,md.from_user_id friendid, count(md.from_user_id),max(md.date_created), 'user' usertype from message_dtl md, message m " +
                " where md.to_user_id=" +session['userdetails'].id+" and md.date_checked is  null and md.group_id is null " +
                " and m.messagetype='" +params.messagetype+"'"+
                " and md.msg_id=m.id "+
                " group by md.from_user_id"+
                " union "+
                " select md.group_id,md.group_id friendid, count(md.group_id),max(md.date_created), 'group' usertype from message_dtl md, message m " +
                " where md.to_user_id=" +session['userdetails'].id+" and md.date_checked is  null and md.group_id is not null " +
                " and m.messagetype='" +params.messagetype+"'"+
                " and md.msg_id=m.id "+
                " group by md.group_id"


        def dataSource = grailsApplication.mainContext.getBean('dataSource')
		def sql1 = new Sql(dataSource)
		def messagesList = sql1.rows(sql);

		List unreadMessagesList = messagesList.collect{comp ->
			return [fromUserId: comp[1], unreadCount: comp[2],lastMessageDate: comp[3], usertype: comp[4]]
		}
		def json =[
				'results': unreadMessagesList,
				'status': unreadMessagesList ? "OK" : "Nothing present"
		]
        render json as JSON

	}

    @Secured(['ROLE_USER'])
    def getMessages(){
        def sql;
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        List messageList
        if("user".equals(params.usertype)){
            sql="SELECT md.date_created date_created,m.message,'you' sender FROM message_dtl md, " +
                    " message m where from_user_id=" +session['userdetails'].id+
                    " and to_user_id="+params.id+" and m.id=md.msg_id and date_from_deleted is null " +
                    " and m.messagetype='" +params.messagetype+"' and md.group_id is null"+
                    " union  " +
                    "SELECT md.date_created date_created,m.message, 'me' sender FROM message_dtl md, " +
                    " message m where from_user_id=" +params.id+
                    " and m.messagetype='" +params.messagetype+"'"+
                    " and to_user_id="+session['userdetails'].id+" and m.id=md.msg_id and date_to_deleted is null and md.group_id is null order by date_created desc ";
           def messagesList = sql1.rows(sql);

             messageList = messagesList.collect{comp ->
                return [messageDate: comp[0], message: comp[1],sender: comp[2]]
            }
            sql = "update MessageDtl set dateChecked=sysdate() where fromUserId="+params.id+" and toUserId="+session['userdetails'].id+" and group_id is null";

            MessageDtl.executeUpdate(sql);
        }else{
            sql=    "SELECT md.date_created date_created,m.message, u.name,u.id FROM message_dtl md,user u, " +
                    " message m where group_id=" +params.id+
                    " and m.messagetype='" +params.messagetype+"'"+
                    " and to_user_id="+session['userdetails'].id+" and m.id=md.msg_id" +
                    " and date_to_deleted is null" +
                    " and u.id=md.from_user_id order by date_created desc ";

             def messagesList = sql1.rows(sql);
             def senderType;
            def nameofsender;
             messageList = messagesList.collect{comp ->
                 if((""+session['userdetails'].id).equals(""+comp[3])) {
                     senderType = "you"
                     nameofsender=""
                 }
                 else {
                     senderType="me"
                     nameofsender=comp[2]
                 }
                return [messageDate: comp[0], message: comp[1],name:nameofsender,sender: senderType]
            }
            sql = "update MessageDtl set dateChecked=sysdate() where group_id="+params.id+" and toUserId="+session['userdetails'].id;
            MessageDtl.executeUpdate(sql);
        }



        def json =[
                'results': messageList.reverse(),
                'status': messageList ? "OK" : "Nothing present"
        ]
        render json as JSON

    }

    @Secured(['ROLE_USER'])
    def getUnreadMessages(fromuserid,messagetype){
        def sql;
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def  unreadMessages

        if("user".equals(params.usertype)){
            sql= "SELECT md.date_created date_created,m.message, 'me' sender FROM message_dtl md, " +
                    " message m where from_user_id=" +fromuserid+
                    " and m.messagetype='" +messagetype+"'"+
                    " and to_user_id="+session['userdetails'].id+" and m.id=md.msg_id and date_checked is null order by date_created desc ";
            def messagesList = sql1.rows(sql);

            unreadMessages = messagesList.collect{comp ->
                return [messageDate: comp[0], message: comp[1],sender: comp[2]]
            }
            sql = "update MessageDtl set dateChecked=sysdate() where fromUserId=" + fromuserid + " and toUserId=" + session['userdetails'].id;
        }else{
            sql= "SELECT md.date_created date_created,m.message,m.message, u.name,u.id FROM message_dtl md,user u, " +
                    " message m where group_id=" +fromuserid+
                    " and m.messagetype='" +messagetype+"'"+
                    " and to_user_id="+session['userdetails'].id+" and m.id=md.msg_id and date_checked is null" +
                    "  and u.id=md.from_user_id order by date_created desc ";
            def messagesList = sql1.rows(sql);
            def senderType;
            def nameofsender;
            unreadMessages = messagesList.collect{comp ->
                if((""+session['userdetails'].id).equals(""+comp[3])) {
                    senderType = "you"
                    nameofsender=""
                }
                else {
                    senderType="me"
                    nameofsender=comp[2]
                }
                return [messageDate: comp[0], message: comp[1],name:nameofsender,sender: senderType]
            }
            sql = "update MessageDtl set dateChecked=sysdate() where groupId=" + fromuserid + " and toUserId=" + session['userdetails'].id;
        }

        MessageDtl.executeUpdate(sql);

        return unreadMessages;
    }

    @Secured(['ROLE_USER'])
    def getUnreadMessagesForView(){
        def unreadMessages = getUnreadMessages(params.id,params.messagetype);
        def json =[
                'results': unreadMessages.reverse(),
                'status': unreadMessages ? "OK" : "Nothing present"
        ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
	def getTotalUnreadMessageCount(){
        if(springSecurityService.currentUser!=null&&session.getAttribute("userdetails")==null){
            session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
        }
		def sql="select  count(md.to_user_id) from message_dtl md, message m " +
				" where md.to_user_id=" +session['userdetails'].id+" and md.date_checked is  null " +
				" and m.messagetype='" +params.messagetype+"'"+
				" and md.msg_id=m.id "+
				" group by md.to_user_id"
      	def dataSource = grailsApplication.mainContext.getBean('dataSource')
		def sql1 = new Sql(dataSource)
		def messagesList = sql1.rows(sql);

		List unreadMessagesList = messagesList.collect{comp ->
			return [unreadCount: comp[0]]
		}
		def json =[
				'results': unreadMessagesList,
				'status': unreadMessagesList ? "OK" : "Nothing present"
		]
        render json as JSON

	}
}
