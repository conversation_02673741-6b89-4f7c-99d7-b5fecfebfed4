package com.wonderslate


import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.*
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BlockedStudent
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.librarybooks.LibraryBooksService
import com.wonderslate.prepjoy.QuizRecMst
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

class TestgeneratorController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def redisService
    WsLibraryService wsLibraryService
    WsLibraryCacheService wsLibraryCacheService
    LibraryBooksService libraryBooksService
    UtilService utilService
    def index() {}

    @Secured(['ROLE_USER']) @Transactional
    def getUserBooks(){

        Integer siteId = wsLibraryService.getSiteId(request,session)
        List userBooksList
        if(siteId==1){
            if (redisService.("userShelfBooks_" + springSecurityService.currentUser.username) == null || redisService.("userShelfBooks_" + springSecurityService.currentUser.username)=="null") {
                wsLibraryCacheService.userShelfBooks(springSecurityService.currentUser.username)
            }
            userBooksList =new JsonSlurper().parseText(redisService.("userShelfBooks_" + springSecurityService.currentUser.username))
        }else{
            if(redisService.(springSecurityService.currentUser.username+"_"+"booksList")==null) {
                dataProviderService.getBooksListForUser()
            }
            userBooksList = new JsonSlurper().parseText(redisService.(springSecurityService.currentUser.username+"_"+"booksList"))

        }
        List books =[]
        userBooksList.each { book ->
            if(!("test".equals(book.bookType))) {
                if (redisService.("quizPresent_" + book.id) == null) dataProviderService.quizPresent(new Long(book.id))
                if ("Present".equals(redisService.("quizPresent_" + book.id))) {
                    books << book
                }
            }
        }

        def json =[
                'books': books,
                'status': books ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

    //from single of multiple books
    @Secured(['ROLE_USER']) @Transactional
    def getTestChapters(){
        Integer siteId = wsLibraryService.getSiteId(request,session)
        String sql="select cm.name,cm.id, count(cm.id) cnt,cm.book_id,cm.sort_order from chapters_mst cm, resource_dtl rd where cm.book_id in (" + params.bookId+ ") and cm.id=rd.chapter_id" +
                    " and rd.res_type in ('Multiple Choice Questions') and rd.quiz_mode is null and (rd.test_end_date is null or rd.test_end_date < sysdate()) group by cm.id having cnt>=1  "+
                    " order by cm.book_id,cm.id asc"
        println("*******" +sql)
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def chapters = sql1.rows(sql)

        List chaptersMst = chapters.collect{chapter ->
            return [name: chapter[0], id:chapter[1], bookId:[chapter[3]]]
        }

        def json =[
                'results': chaptersMst,
                'status': chaptersMst ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

    //from single of multiple books
    @Secured(['ROLE_USER'])
    def getTestInfoForBook(){
        def hasBookAccess= false;
        if (wsLibraryService.bookAccessForUser(params.bookId+"",request,session)) {
                    hasBookAccess = true;
        }
            String sql = "select cm.name,cm.id, count(cm.id) cnt,coalesce(cm.preview_chapter,'') preview from chapters_mst cm, resource_dtl rd where book_id in (" + params.bookId + ") and cm.id=rd.chapter_id" +
                    " and rd.res_type='Multiple Choice Questions' and rd.quiz_mode is null and (rd.test_end_date is null or rd.test_end_date < sysdate()) group by cm.id having cnt>=1  order by book_id,cm.sort_order"
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def chapters = sql1.rows(sql)
            String chaptersList = ""

            List chaptersMst = chapters.collect { chapter ->
                chaptersList += chapter[1] + ","
                return [name: chapter[0], id: chapter[1], previewChapter: chapter.preview]
            }
            chaptersList = chaptersList.substring(0, chaptersList.length() - 1)

            sql = "SELECT count(om.id) FROM objective_mst om,resource_dtl rd where om.quiz_id=rd.res_link and rd.chapter_id in (" + chaptersList + ")" +
                    " and rd.res_type='Multiple Choice Questions' "

            dataSource = grailsApplication.mainContext.getBean('dataSource')
            sql1 = new Sql(dataSource)
            def questionsCount = sql1.rows(sql)
            int noOfQuestions = 0;
            questionsCount.each { q ->
                noOfQuestions += q[0]

            }


           def json = [
                    'results'       : chaptersMst,
                    'questionCount' : noOfQuestions,
                    'testChapterIds': chaptersList,
                    'hasBookAccess' : hasBookAccess,
                    'status'        : chaptersMst ? "OK" : "Nothing present",
            ]

        render json as JSON
    }

    //from single of multiple books
    @Secured(['ROLE_USER'])
    def getTestChaptersForRestype(){
        String sql = "select cm.name,cm.id, count(cm.id) cnt from chapters_mst cm, resource_dtl rd where book_id in ("+params.bookId+") and cm.id=rd.chapter_id" +
                " and rd.res_type='"+params.resType+"' and rd.quiz_mode is null and (rd.test_end_date is null or rd.test_end_date < sysdate()) is null group by cm.id having cnt>=1  order by book_id,sort_order"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def chapters = sql1.rows(sql)

        List chaptersMst = chapters.collect{chapter ->
            return [name: chapter[0], id:chapter[1]]
        }

        def json =[
                'results': chaptersMst,
                'status': chaptersMst ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

    //get available quizzes from given chapters (single or many)
    @Secured(['ROLE_USER'])
    def getTestQuizTypes(){
        String sql = "select distinct(res_type) from resource_dtl  where res_type in ('Multiple Choice Questions') and chapter_id in ("+params.chaptersList+") and quiz_mode is null and (test_end_date is null or test_end_date < sysdate())  order by res_type"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def resourceTypes = sql1.rows(sql)

        List resTypes = resourceTypes.collect{resType ->
            return [resType: resType[0]]
        }

        def json =[
                'results': resTypes,
                'status': resTypes ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

    //get available quizzes from given chapters (single or many)
    @Secured(['ROLE_USER'])
    def getTestQuizTypesForUser(){
        String sql = "select distinct(res_type) from resource_dtl  where res_type in ('Multiple Choice Questions') and chapter_id in ("+params.chaptersList+") and quiz_mode is null and (rd.test_end_date is null or rd.test_end_date < sysdate())  order by res_type"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def resourceTypes = sql1.rows(sql)

        List resTypes = resourceTypes.collect{resType ->
            return [resType: resType[0]]
        }

        def json =[
                'results': resTypes,
                'status': resTypes ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

   // get the available difficulty leves in given quizzes
    @Secured(['ROLE_USER'])
    def getTestDifficultyLevels(){
        String sql = "SELECT distinct(om.difficultylevel),count(om.id) FROM objective_mst om,resource_dtl rd where om.quiz_id=rd.res_link and rd.chapter_id in ("+params.chaptersList+") and om.difficultylevel is not null" +
                " and rd.res_type='"+params.resType+"' group by om.difficultylevel"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def difficultyLevels = sql1.rows(sql)

        List diffLevels = difficultyLevels.collect{dl ->
            return [diffLevel: dl[0],count:dl[1]]
        }

        def json =[
                'results': diffLevels,
                'status': diffLevels ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

    //get total number questions for the given parameters
    @Secured(['ROLE_USER'])
    def getTestNumberOfQuestions(){
        String sql

        if("all".equals(params.difficultyLevel))
            sql="SELECT count(om.id) FROM objective_mst om,resource_dtl rd where om.quiz_id=rd.res_link and rd.chapter_id in ("+params.chaptersList+")" +
                    " and rd.res_type='"+params.resType+"' "
        else sql = "SELECT count(om.id) FROM objective_mst om,resource_dtl rd where om.quiz_id=rd.res_link and rd.chapter_id in ("+params.chaptersList+") and om.difficultylevel in ("+params.difficultyLevel+") " +
                " and rd.res_type='"+params.resType+"' "
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def questionsCount = sql1.rows(sql)

        List questionCount = questionsCount.collect{q ->
            return [questions: q[0]]
        }

        def json =[
                'results': questionCount,
                'status': questionCount ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def getTest(){

        def sql = "select res_link from resource_dtl  where res_type='"+params.resType+"' and chapter_id in ("+params.chaptersList+")"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def quizIdsList = sql1.rows(sql)
        def quizIds=[]
        quizIdsList.each { quiz ->
            quizIds << new Integer(""+quiz[0])
        }

       def objectiveMst = ObjectiveMst.findAllByQuizIdInList(quizIds)
    }

    def testgen(){}

    //get available quizzes from given chapters (single or many)
    @Secured(['ROLE_USER'])
    def getTestQuizTypesForUserNew(){
        String sql = "select distinct(res_type) from resource_dtl rd,books_mst bm, wsuser.books_permission bp, chapters_mst cm" +
                " where bp.username='"+springSecurityService.currentUser.username+"' and bm.id=bp.book_id and bm.id=cm.book_id and cm.id=rd.chapter_id" +
                "  and rd.res_type in ('Match the answers','Opposites','Fill in the blanks'," +
                "'True or False','Multiple Choice Questions')  and rd.quiz_mode is null order by rd.res_type"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def resourceTypes = sql1.rows(sql)

        List resTypes = resourceTypes.collect{resType ->
            return [resType: resType[0]]
        }

        def json =[
                'results': resTypes,
                'status': resTypes ? "OK" : "Nothing present",
        ]

        render json as JSON
    }

    def nextExamMonthlyQuiz(){
        if(redisService.("nextexamMonthlyBooksList")==null) dataProviderService.nextExamBooks()
        def json = ["nextExamBooks":redisService.("nextexamMonthlyBooksList")]
        render json as JSON
    }

    def monthlyQuiz(){
        ["title":"Monthly Quiz - Wonderslate", commonTemplate:"true"]
    }

    @Transactional
    def createTest() {
        println("Entereing this ")
        def sql
        List questions

        String canReorderQuestions;
        TestsMst testsMst = null
        HashMap chaptersMap =  new HashMap()
        String instituteId = ""
        sql = "select res_link,chapter_id,cm.name chapterName,bm.title from resource_dtl rd,chapters_mst cm,books_mst bm  where res_type='" + params.resType + "' and chapter_id in (" + params.chaptersList + ")  and cm.id=rd.chapter_id\n" +
                        "  and bm.id=cm.book_id";
                def dataSource = grailsApplication.mainContext.getBean('dataSource')
                def sql1 = new Sql(dataSource)
                def quizIdsList = sql1.rows(sql);
                def quizIds = [];
                def objIds = [];
                String stringQuizIds="";
                quizIdsList.each { quiz ->
                    quizIds << new Integer("" + quiz[0])
                    stringQuizIds +=quiz[0]+","
                    chaptersMap.put(""+quiz[0],""+quiz[1])
                }
                stringQuizIds = stringQuizIds.substring(0,stringQuizIds.length()-1)
                sql = "SELECT t1.id FROM objective_mst AS t1 JOIN (SELECT id FROM objective_mst  where quiz_id in" +
                        " ("+stringQuizIds+") ORDER BY RAND() LIMIT "+params.noOfQuestions+") as t2 ON t1.id=t2.id"
                dataSource = grailsApplication.mainContext.getBean('dataSource')
                sql1 = new Sql(dataSource)
                def quizList = sql1.rows(sql);
                quizList.each { quiz ->
                    objIds << new Integer("" + quiz[0])
                }
                questions = ObjectiveMst.findAllByIdInList(objIds)

            long seed = System.nanoTime();
            if ("true".equals(canReorderQuestions)) Collections.shuffle(questions, new Random(seed));
                Collections.shuffle(questions, new Random(seed));
                int extraQuestions = questions.size() - Integer.parseInt(params.noOfQuestions);

                for (int i = 0; i < extraQuestions; i++) questions.remove((questions.size() - 1));

                testsMst = new TestsMst(createdBy: springSecurityService.currentUser.username, name: (params.testName != null && params.testName.length() > 0) ? params.testName : "Created Test")
                testsMst.save(failOnError: true, flush: true)
                    if (params.batchId != null && params.batchId.length() > 0) {
                        //create variables for startDateTime, endDateTime and resultDateTime. If the values are present in the params, then get the timezone from the timeZone parameter and convert the date to GMT
                        Date startDateTime = null
                        Date endDateTime = null
                        Date resultDateTime = null
                        String timeZone = "IST"
                        //get the client timezone from the timeZone parameter and then convert to GMT according to the timezone.
                        if (params.startDateTime != null && params.startDateTime.length() > 0) {
                            startDateTime = utilService.convertDateWithPattern(params.startDateTime, timeZone, "UTC","dd-MM-yyyy HH:mm")
                        }
                        if (params.endDateTime != null && params.endDateTime.length() > 0) {
                            endDateTime = utilService.convertDateWithPattern(params.endDateTime, timeZone, "UTC","dd-MM-yyyy HH:mm")
                        }
                        if (params.resultDateTime != null && params.resultDateTime.length() > 0) {
                            resultDateTime = utilService.convertDateWithPattern(params.resultDateTime, timeZone, "UTC","dd-MM-yyyy HH:mm")
                        }
                        println("the startDateTime is "+startDateTime+" and the params.startDateTime is "+params.startDateTime)
                        TestsShared testsShared = new TestsShared(testId: testsMst.id, name: (params.testName != null && params.testName.length() > 0) ? params.testName : "Created Test",
                                batchId: new Long(params.batchId), createdBy: springSecurityService.currentUser.username, startDateTime: startDateTime, endDateTime: endDateTime, resultDateTime: resultDateTime, duration: new Integer(params.duration))
                        testsShared.save(failOnError: true, flush: true)
                        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.get(new Long(params.batchId))
                        instituteId = ""+courseBatchesDtl.conductedBy
                    }

                questions.each { quiz ->
                                TestsDtl testsDtl = new TestsDtl(testId: testsMst.id, objId: quiz.id)
                                testsDtl.save(failOnError: true, flush: true)
                                }

            def json =
                    [
                           'status':"test created",
                            'testId':testsMst.id,
                            'instituteId':instituteId
                    ]
            render json as JSON

    }

    @Transactional
    def getTestDetails(){
        // first get the test details TestsShared using testId

        TestsShared testsShared = TestsShared.findByTestId(new Long(params.testId))
        int duration = testsShared.duration!=null?testsShared.duration.intValue()*60:0

        String username = springSecurityService.currentUser.username
        // check if the user has access to the test
        BatchUserDtl batchUserDtl = BatchUserDtl.findByBatchIdAndUsername(testsShared.batchId, username)
        if(batchUserDtl == null){
            def json = [
                'status': "No access to the test"
            ]
            render json as JSON
        }else{
           //now check the start date and end date of the test
            Date currentDate = new Date()
            if(testsShared.startDateTime != null && testsShared.startDateTime.after(currentDate)){
                def json = [
                    'status': "Test not started yet"
                ]
                render json as JSON
            }else if(testsShared.endDateTime != null && testsShared.endDateTime.before(currentDate)){
                println("testEndTIme is "+testsShared.endDateTime+" and current date is "+currentDate)
                def json = [
                    'status': "Test has ended"
                ]
                render json as JSON
            }else {
                BlockedStudent blockedStudent = BlockedStudent.findByTestIdAndUsername(new Long(params.testId), username)
                if (blockedStudent != null) {
                    def json = [
                            'status': "You are blocked from taking the test"
                    ]
                    render json as JSON
                } else {
                    //now check the user has already taken the test
                    QuizRecMst quizRecMst = QuizRecMst.findByTestGenIdAndUsername(new Integer(params.testId), username)
                    if (quizRecMst != null) {
                        def json = [
                                'status': "Test already taken"
                        ]
                        render json as JSON
                    } else {
                        // get the test details TestsMst using testId
                        TestsMst testsMst = TestsMst.get(new Long(params.testId))
                        // get the questions from TestsDtl using testId
                        List testsDtl = TestsDtl.findAllByTestId(new Long(params.testId))
                        String language1 = "", language2 = ""
                        boolean languageFound = false
                        int noOfAnswers = 0
                        List jsonQuestions = testsDtl.collect { testDtl ->
                            ObjectiveMst quiz = ObjectiveMst.get(testDtl.objId)
                            noOfAnswers = 0;

                            //logic to see if any of the questions has multiple language for create test question
                            if (!languageFound) {
                                if (quiz.question.indexOf("~~") > -1) {
                                    languageFound = true
                                    ResourceDtl resourceDtl = ResourceDtl.findByResLink("" + quiz.quizId)
                                    if (resourceDtl.language1 != null) language1 = resourceDtl.language1
                                    if (resourceDtl.language1 != null) language2 = resourceDtl.language2
                                }
                            }
                            if (quiz.answer1 == "Yes") noOfAnswers++; if (quiz.answer2 == "Yes") noOfAnswers++; if (quiz.answer3 == "Yes") noOfAnswers++; if (quiz.answer4 == "Yes") noOfAnswers++;
                            return [id               : quiz.id, ps: quiz.question, op1: quiz.option1, op2: quiz.option2, op3: quiz.option3, op4: quiz.option4, op5: quiz.option5,
                                    resType          : quiz.quizType, optionType: (noOfAnswers == 1) ? "radio" : "checkbox", ans1: quiz.answer1, ans2: quiz.answer2, ans3: quiz.answer3, ans4: quiz.answer4, ans5: quiz.answer5,
                                    directions       : null, section: quiz.section,
                                    answerDescription: quiz.answerDescription, answer: (quiz.answer != null ? new String(quiz.answer, "UTF-8") : ""), subject: quiz.subject,
                                    chapterId        : "-1", quizId: quiz.quizId, marks: quiz.marks, negativeMarks: quiz.negativeMarks, explainLink: quiz.explainLink, startTime: quiz.startTime,
                                    endTime          : quiz.endTime, difficultyLevel: quiz.difficultylevel]
                        }
                        session['quizquestionanswers'] = jsonQuestions;
                         if(testsShared.endDateTime != null) {
                             //get the time difference between the current date and the end date of the test in seconds
                                int timeDifference = (testsShared.endDateTime.getTime() - new Date().getTime()) / 1000
                                 if(duration>timeDifference) duration = timeDifference

                         }
                        println("the duration is "+duration)
                        def json = [
                                'testDetails'   : testsShared,
                                'results'       : jsonQuestions,
                                'status'        : jsonQuestions ? "OK" : "Nothing present",
                                'isPassage'     : false,
                                'passage'       : '',
                                'description'   : '',
                                'chaptersList'  : null,
                                'testgenid'     : testsMst.id,
                                'language1'     : language1,
                                'language2'     : language2,
                                'examSubject'   : "",
                                'examMst'       : null,
                                'examDtl'       : null,
                                'testSeries'    : 'true',
                                'testEndDate'   : testsShared.endDateTime != null ? utilService.convertDate(testsShared.endDateTime, "UTC", "IST") : "",
                                'testEndDateWeb': testsShared.endDateTime,
                                'duration':duration
                        ]
                        render json as JSON
                    }
                }
            }
        }
    }
}
