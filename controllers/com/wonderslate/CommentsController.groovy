package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.discussions.Questions
import com.wonderslate.log.DeviceInformation
import com.wonderslate.publish.Polls
import com.wonderslate.usermanagement.SecurityService
import grails.transaction.Transactional
import groovy.sql.Sql
import org.apache.commons.lang.StringEscapeUtils
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.simp.SimpMessagingTemplate
import pl.touk.excel.export.WebXlsxExporter

import java.text.SimpleDateFormat

class CommentsController {

@Autowired
        SimpMessagingTemplate simpMessagingTemplate;

    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    UserManagementService userManagementService
    SecurityService securityService
    def springSecurityService
    def redisService
    int chatMessageBucketSize = 50
    def index() {


    }

    def checkChatNo(){
        def chatNo = getNextChatNumber(new Integer(params.chatNo))
        render chatNo
    }

    @Transactional
     int getNextChatNumber(resId)
    {    int chatNo = Questions.countByResId(resId)
         chatNo++
        redisService.("chatNo_"+resId) = ""+chatNo;
        return  chatNo

    }

    @Secured(['ROLE_USER']) @Transactional
    def getChatMessages(){
        int currentChatNo=0
        int requestChatNo = Integer.parseInt(params.requestChatNo)
        def resId = params.resId
        def json
        int quotient = 0
        if("back".equals(params.chatDirection)){
            requestChatNo--;
            quotient = requestChatNo / chatMessageBucketSize
            if(redisService.("chatMessages_" +resId+"_"+quotient)==null){
                dataProviderService.getChatMessages(resId,chatMessageBucketSize,new Integer(requestChatNo))
            }
            json = ['chatMessages':redisService.("chatMessages_" +resId+"_"+quotient),
                    'status':'OK']
        }else{
            if(redisService.("chatNo_"+resId)!=null){
                currentChatNo = Integer.parseInt(redisService.("chatNo_"+resId))
            }else{
                currentChatNo = Questions.countByResId(new Long(resId))
                redisService.("chatNo_"+resId) = ""+currentChatNo
            }
            //now check is there any new chat
            int latestChatNoWithClient = Integer.parseInt(params.requestChatNo)
            if(currentChatNo > latestChatNoWithClient){
                quotient = currentChatNo / chatMessageBucketSize
                if(redisService.("chatMessages_" +resId+"_"+quotient)==null){
                    dataProviderService.getChatMessages(resId,chatMessageBucketSize,new Integer(currentChatNo))
                }
                json = ['chatMessages':redisService.("chatMessages_" +resId+"_"+quotient),
                        'status':'OK',
                        chatStopped:"true".equals(redisService.("stopChat_"+params.resId))?"true":"false"]
            }
            else{
                json = ['status':'NO_NEW',
                        chatStopped:"true".equals(redisService.("stopChat_"+params.resId))?"true":"false"]
            }
        }
        render json as JSON

    }


    @Secured(['ROLE_USER']) @Transactional
    def addQuestion(){

        Long chapterId
        def json
        String status = ""
        String toUser = ""
        boolean commentsBlocked=false;
        Integer bookId = new Integer(-1);
        Integer pollId = new Integer(-1)
        if(params.pollId!=null) pollId = new Integer(params.pollId)
        Integer pollDuration = new Integer(-1)
        try {
            if(params.chapterId!=null) chapterId = new Long(params.chapterId)
            if (params.resId != null && !"".equals(params.resId)) {
                ResourceDtl resource = ResourceDtl.findById(new Integer(params.resId))
                chapterId = resource.chapterId
            }

            if("true".equals(redisService.("stopChat_"+params.resId))) {
                if(redisService.("autoStoppedTime_"+params.resId)!=null){
                    Date currentDate = new Date();
                    long timeDiffInSeconds = (currentDate.getTime() - Long.parseLong(redisService.("autoStoppedTime_" + params.resId))) / 1000;

                    //check for the set stop time is over or not.
                    if (timeDiffInSeconds > Integer.parseInt(redisService.("autoStoppedDuration_" + params.resId))) {
                        redisService.deleteKeysWithPattern("stopChat_"+params.resId)
                        redisService.deleteKeysWithPattern("autoStoppedTime_"+params.resId)
                        redisService.deleteKeysWithPattern("autoStoppedDuration_"+params.resId)
                        dataNotificationService.sendChatStopStartToTopic(params.resId,"start")
                    }
                }
            }
            if("true".equals(redisService.("stopChat_"+params.resId))){
                json = ["status":"stopped"]

            } else {
                User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                if (userManagementService.canSeeChapter(chapterId) || user.authorities.any {
                    it.authority == "ROLE_WEB_CHAT"
                } || user.authorities.any {
                    it.authority == "ROLE_WS_CONTENT_ADMIN"
                }) {
                    String adminUser = ""
                    if (user.authorities.any {
                        it.authority == "ROLE_WEB_CHAT"
                    } || user.authorities.any {
                        it.authority == "ROLE_WS_CONTENT_ADMIN"
                    }) {
                        adminUser = "true";
                        bookId = new Integer(-1)
                        if (params.status == null) status = "replyall"
                        else {
                            status = "reply"
                            toUser = params.toUser
                        }

                    }


                    if (redisService.("tempBlocked_" + user.username) != null) {
                        Date currentDate = new Date();
                        long timeDiffInSeconds = (currentDate.getTime() - Long.parseLong(redisService.("tempBlocked_" + user.username))) / 1000;

                        //10 minutes is the block time
                        if (timeDiffInSeconds > 600) {
                            redisService.deleteKeysWithPattern("tempBlocked_" + params.username)
                        } else {
                            commentsBlocked = true
                        }
                    }
                    if ("true".equals(user.commentsBlocked) || commentsBlocked) {
                        json = ['result': "Comments Blocked", "blocked": "true", "tempBlocked": commentsBlocked ? "true" : "false"]
                    } else {
                        boolean questionAdded = false
                        Integer chatNo = new Integer(getNextChatNumber(new Long(params.resId)))

                        Questions question
                        try {
                            Long resId = new Long(params.resId)

                            if(params.bookId!=null) bookId = new Integer(params.bookId)

                            new Sql(grailsApplication.mainContext.getBean('dataSource_wscomm')).call("{call add_question_poll($chapterId,$params.question,$resId,$springSecurityService.currentUser.username,$user.name,$adminUser,$status,$toUser,$bookId,$pollId,$pollDuration, ${Sql.INTEGER})}") { vQuestionId ->
                                question = Questions.findById(vQuestionId)
                                questionAdded = true
                                chatNo = question.chatNo
                                redisService.("chatNo_" + params.resId) = "" + (chatNo)
                            }
                        } catch (Exception e) {
                            println("the exception is " + e.toString())
                        }
                        if (questionAdded) {
                            dataProviderService.getChatMessages(params.resId, chatMessageBucketSize, chatNo)
                            //send the notification for all the live users
                            dataNotificationService.sendCommentNotificationToTopic(question.question, user.name, question.resId,
                                    "new_message", getSiteId(request), question.id, adminUser, chatNo,
                                    question.toUser != null && !"".equals(question.toUser) ? userManagementService.encrypt(question.toUser) : "",pollId,pollDuration)

                            def message;
                            try {

                                json = ['result': "success", id: question.id, data: message]


                            } catch (Exception ex) {
                                json = ['result': "error", id: question.id, data: message]
                                println(ex.getMessage() + ex.toString())
                            }
                        }

                    }

                } else {
                    json =
                            ['result': "noaccess"
                            ]

                }
            }
        }catch(NumberFormatException nfex){
            json = ['result': "NumberFormatException for resId: " + params.resId + " and chapter Id: " +chapterId, exception:nfex.getMessage() + nfex.toString()]
        }catch(NullPointerException npex){
            json = ['result': "NullPointerException for resId: " + params.resId + " and chapter Id: " +chapterId, exception:npex.getMessage() + npex.toString()]
        }catch(Exception exception){
            json = ['result': "exception for resId: " + params.resId + " and chapter Id: " +chapterId, exception:exception.getMessage() + exception.toString()]
        }
        render json as JSON
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def startPoll(){
       Integer pollId = new Integer(params.pollId)
        Polls polls = Polls.findById(pollId)
        polls.status="started"
        polls.save(failOnError: true, flush: true)
        redisService.("poll_"+params.resId)=params.pollId
        Date currentDate = new Date();
        redisService.("pollStartTime_"+params.resId)=""+currentDate.getTime()
        def json = ['status':'started']
        render json as JSON
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def pollCompleted(){
       stopPoll(params.resId)
        def json = ['status':'completed']
        render json as JSON

    }

    def stopPoll(resId){
        Polls polls = Polls.findById(new Integer(redisService.("poll_"+resId)))
        polls.status="completed"
        polls.save(failOnError: true, flush: true)
        redisService.deleteKeysWithPattern("poll_"+resId)
        redisService.deleteKeysWithPattern("pollStartTime_"+resId)

    }
    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def removeQuestion(){

        Questions question = Questions.findById(new Long(params.messageId))
        question.status="deleted"
        question.save(failOnError: true, flush: true)
        dataNotificationService.sendCommentNotificationToTopic(question.question,"",question.resId,"delete_message",
                getSiteId(request),question.id,null,question.chatNo,
                question.toUser!=null&&!"".equals(question.toUser)? userManagementService.encrypt(question.toUser): "","-1","-1")

        def json = ["results":"removed"]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def addLiveVideoUser(){
        def deviceId = params.deviceId
        if ((deviceId == null ||"".equals(deviceId))&&springSecurityService.currentUser!=null){
            DeviceInformation deviceInformation = DeviceInformation.findByUsername(springSecurityService.currentUser.username)
            if(deviceInformation!=null) deviceId = deviceInformation.deviceId
        }
            if(redisService.("videoUserCount_"+params.resId)!=null){
                int currentCount =  Integer.parseInt(redisService.("videoUserCount_"+params.resId))
                currentCount++
                redisService.("videoUserCount_"+params.resId) = ""+currentCount
            }else{
                redisService.("videoUserCount_"+params.resId) = "1";
            }
            dataNotificationService.addLiveVideoUser(deviceId,params.resId,getSiteId(request))
         def json = ["result":"added"]
        render json as JSON
    }

    def subscribeToChat(){
        dataNotificationService.addLiveVideoUser(params.deviceId,params.resId,getSiteId(request))

    }

    @Secured(['ROLE_USER']) @Transactional
    def removeLiveVideoUser(){
        dataNotificationService.removeLiveVideoUser(params.deviceId,params.resId,getSiteId(request))
        if(redisService.("videoUserCount_"+params.resId)!=null){
            int currentCount =  Integer.parseInt(redisService.("videoUserCount_"+params.resId))
            currentCount--
            redisService.("videoUserCount_"+params.resId) = ""+currentCount
        }else{
            redisService.("videoUserCount_"+params.resId) = "0";
        }
            def json = ["result": "removed"]
            render json as JSON

    }
    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def blockUserComments(){
        User user = null
        def result="failed"
        if(params.username!=null)
        user = dataProviderService.getUserMst(params.username)
        else if(params.messageId!=null){
            Questions  question = Questions.findById(new Long(params.messageId))
            user = dataProviderService.getUserMst(question.username)
            result="blocked"
        }
        if(user!=null) {
            user.commentsBlocked = "true"
            user.commentsBlockedDate = new Date()
            if(params.resId!=null) user.commentsBlockedResId = new Integer(params.resId)
            user.save(failOnError: true, flush: true)
            if(params.resId!=null) dataProviderService.getCommentsBlockedUsers(params.resId)
        }
        dataNotificationService.sendChatBlockNotificationToUser(user.username,"true","false")
        def json = ["result":result]
        render json as JSON
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def blockUserCommentsTemp(){
        User user = dataProviderService.getUserMst(params.username)
        Date currentDate = new Date();
        redisService.("tempBlocked_" +params.username) = ""+currentDate.getTime()
        dataNotificationService.sendChatBlockNotificationToUser(user.username,"false","true")
        def json = ["result":"blocked"]
        render json as JSON
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def unblockUserComments(){
        User user = dataProviderService.getUserMst(params.username)
        def resId = null
        if(user!=null){
            if(user.commentsBlockedResId!=null) resId = user.commentsBlockedResId
        user.commentsBlocked=null
        user.commentsBlockedDate = null
        user.commentsBlockedResId = null

        user.save(failOnError: true, flush: true)
        redisService.deleteKeysWithPattern("tempBlocked_" + params.username)
            dataNotificationService.sendChatBlockNotificationToUser(user.username,"false","false")
            if(resId!=null) dataProviderService.getCommentsBlockedUsers(resId)
        def json = ["result":"unblocked"]
        render json as JSON
        }
        else{
            def json = ["result":"user not found"]
            render json as JSON
        }
    }

      Integer getSiteId(request){
        Integer siteId = new Integer(1);

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }

    @Secured(['ROLE_USER']) @Transactional
    def getAllUserComments(){

        List commentsList = Questions.findAllByResIdAndStatusIsNull(new Long(params.resId), [sort:"id",order:"desc"])
        List comments = commentsList.collect { comment ->
            return [messageId: comment.id, sender: comment.name,message:comment.question,resId:comment.resId,messageType:'all messages',dateCreated:comment.dateCreated,
                    username: comment.username,
            adminUser: comment.adminUser]
        }
        def json = ["comments":comments,chatStopped:"true".equals(redisService.("stopChat_"+params.resId))?"true":"false"]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getLatestCommentsForChatAdmin() {
        def currentChatNumber="0";
        if(redisService.("chatNo_"+params.resId)!=null) currentChatNumber = redisService.("chatNo_"+params.resId)
        int liveVideoUsersCount = 0;
        if(redisService.("videoUserCount_"+params.resId)!=null){
            liveVideoUsersCount =  Integer.parseInt(redisService.("videoUserCount_"+params.resId))
        }
            try{
        List commentsList = new ArrayList();

        //if question id is 0, latest 300
        //if question id is some number , then greater than id from the top, till the number or latest 300 whichever is greater

        int limit = 300;
        int latestQuestionId = 0;
        if(params.questionId!=null) latestQuestionId = Integer.parseInt(params.questionId)
        if("-1".equals(params.currentPageIndex)){

        if(latestQuestionId==0) commentsList = Questions.findAllByResId(new Long(params.resId), [sort:"id",order:"desc",max: limit])
        else commentsList = Questions.findAllByIdGreaterThanAndResId(new Integer(latestQuestionId), new Long(params.resId), [sort: "id", order: "desc",max: limit])
            }else{
            if(Integer.parseInt(params.currentlyDisplayedPageIndex)!=Integer.parseInt(params.currentPageIndex))
            commentsList = Questions.findAllByResId(new Long(params.resId), [sort:"id",offset: Integer.parseInt(params.currentPageIndex)*limit,max: limit])
        }
                if("true".equals(redisService.("stopChat_"+params.resId))) {
                    if(redisService.("autoStoppedTime_"+params.resId)!=null){
                        Date currentDate = new Date();
                        long timeDiffInSeconds = (currentDate.getTime() - Long.parseLong(redisService.("autoStoppedTime_" + params.resId))) / 1000;
                        //check for the set stop time is over or not.
                        if (timeDiffInSeconds > Integer.parseInt(redisService.("autoStoppedDuration_" + params.resId))) {

                            redisService.deleteKeysWithPattern("stopChat_"+params.resId)
                            redisService.deleteKeysWithPattern("autoStoppedTime_"+params.resId)
                            redisService.deleteKeysWithPattern("autoStoppedDuration_"+params.resId)
                            dataNotificationService.sendChatStopStartToTopic(params.resId,"start")
                        }
                    }
                }

        List comments = commentsList.collect { comment ->
            return [messageId: comment.id, sender: comment.name, message: comment.question, resId: comment.resId,
                    messageType: 'all messages', dateCreated: comment.dateCreated,
                    username : comment.username,
                    adminUser: comment.adminUser,
                    course:comment.bookId!=null&&comment.bookId.intValue()!=-1?dataProviderService.getBooksMst(comment.bookId).title:"",status:comment.status,
                    pollId:comment.pollId,
                    pollDuration:comment.pollDuration,
                    chatNo:comment.chatNo]
        }
        def json = ["comments": comments,
                    chatStopped:"true".equals(redisService.("stopChat_"+params.resId))?"true":"false", currentChatNo:currentChatNumber,
                    liveVideoUsersCount:liveVideoUsersCount]
            render json as JSON
        }catch(Exception ex){
            def json = ["comments": [],
                        chatStopped:"true".equals(redisService.("stopChat_"+params.resId))?"true":"false",liveVideoUsersCount:liveVideoUsersCount]
            render json as JSON
        }
    }
    @Secured(['ROLE_USER']) @Transactional
    def getNewCommentsById() {
        try {

            Integer limit = 300;
            Integer offset = Integer.parseInt(params.offset);
            Long questionId = 0, olldestIdForPagination = 0
            if(params.olldestIdForPagination != null) olldestIdForPagination = Long.parseLong(params.olldestIdForPagination)
            if(params.questionId != null) questionId = new Long(params.questionId);
            List commentsList = new ArrayList();
            if(questionId > 0) {
                commentsList = Questions.findAllByIdGreaterThanAndResId(questionId, new Long(params.resId), [sort: "id", order: "desc",max: limit])
            }
            else if(questionId == 0 && olldestIdForPagination > 0) {
                commentsList = Questions.findAllByResIdAndStatusNotEqualAndIdLessThan(new Long(params.resId),"deleted", olldestIdForPagination,[sort:"id",order:"desc",max: limit])
            }
            else if(questionId == 0) {
                commentsList = Questions.findAllByResIdAndStatusNotEqual(new Long(params.resId),"deleted", [sort:"id",order:"desc",offset: offset*limit,max: limit])
            }
            if("true".equals(redisService.("stopChat_"+params.resId))) {
                if(redisService.("autoStoppedTime_"+params.resId)!=null){
                    Date currentDate = new Date();
                    long timeDiffInSeconds = (currentDate.getTime() - Long.parseLong(redisService.("autoStoppedTime_" + params.resId))) / 1000;
                    //check for the set stop time is over or not.
                    if (timeDiffInSeconds > Integer.parseInt(redisService.("autoStoppedDuration_" + params.resId))) {
                        println("stopping and sending the notificaiton")
                        redisService.deleteKeysWithPattern("stopChat_"+params.resId)
                        redisService.deleteKeysWithPattern("autoStoppedTime_"+params.resId)
                        redisService.deleteKeysWithPattern("autoStoppedDuration_"+params.resId)
                        dataNotificationService.sendChatStopStartToTopic(params.resId,"start")
                    }
                }
            }
            List comments = commentsList.collect { comment ->
                return [messageId: comment.id, sender: comment.name, message: comment.question, resId: comment.resId,
                        messageType: 'all messages', dateCreated: comment.dateCreated,
                        username : comment.username,
                        adminUser: comment.adminUser,
                        course:comment.bookId!=null&&comment.bookId.intValue()!=-1?dataProviderService.getBooksMst(comment.bookId).title:"",status:comment.status,
                        pollId:comment.pollId,
                        pollDuration:comment.pollDuration]
            }
            def json = ["comments": comments,
                        chatStopped:"true".equals(redisService.("stopChat_"+params.resId))?"true":"false"]
            render json as JSON
        }catch(Exception ex){
            def json = ["comments": [],
                        chatStopped:"true".equals(redisService.("stopChat_"+params.resId))?"true":"false"]
            render json as JSON
        }
    }


    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN'])
    def chatApp(){}
    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN'])
    def chatLogin(){}

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def livechat(){
        ResourceDtl resourceDtl = null
        ChaptersMst chaptersMst = null

        resourceDtl = ResourceDtl.findById(new Integer(params.resId))
        chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)

        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        if(session["userdetails"].publisherId==null||session["userdetails"].publisherId.intValue()==booksMst.publisherId.intValue()) {
            ['result': "success", 'videoName': resourceDtl.resourceName, 'chapterName': chaptersMst.name, 'bookName': booksMst.title]
        }else{
            redirect([uri: '/comments/chatLogin'])
        }
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def livechatPolling(){
        ResourceDtl resourceDtl = null
        ChaptersMst chaptersMst = null

        resourceDtl = ResourceDtl.findById(new Integer(params.resId))
        chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)

        BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
        if(session["userdetails"].publisherId==null||session["userdetails"].publisherId.intValue()==booksMst.publisherId.intValue()) {
            ['result': "success", 'videoName': resourceDtl.resourceName, 'chapterName': chaptersMst.name, 'bookName': booksMst.title]
        }else{
            redirect([uri: '/comments/chatLogin'])
        }
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def downloadWebChat() {
        if (params.resId!=null && !"".equals(params.resId)) {
            def resourceDtl = ResourceDtl.findById(new Integer(params.resId))
            def chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)

            BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
            if (session["userdetails"].publisherId == null || session["userdetails"].publisherId.intValue() == booksMst.publisherId.intValue()) {
                String sql = "select qr.name,qr.username,qr.question,DATE_ADD(qr.date_created,INTERVAL '5:30' HOUR_MINUTE) date_created,qr.status" +
                        " from wscomm.questions qr where qr.res_id= " + params.resId +
                        "  order by id desc";
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wscomm')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                def date
                if (results != null) {
                    List comments = results.collect { comment ->
                        User user = dataProviderService.getUserMst(comment.username);
                        date = comment.date_created
                        if (date != "") {
                            date = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(date)
                        }
                        return [sender: comment.name, message: comment.question, dateCreated: date,
                                mobile: user.mobile, email: user.email, district: user.district != null ? user.district : "", state: user.state, name: user.name,
                                status: comment.status != null ? comment.status : ""]
                    }

                    List headers
                    List withProperties
                    headers = ["Name", "Mobile", "Email", "Message", "Date", "Status", "State", "District"]
                    withProperties = ["name", "mobile", "email", "message", "dateCreated", "status", "state", "district"]
                    def fileName = "WebChat_" + params.resId + ".xlsx";

                    new WebXlsxExporter().with {
                        setResponseHeaders(response, fileName)
                        fillHeader(headers)
                        add(comments, withProperties)
                        save(response.outputStream)
                    }
                }
            }
        }
    }
    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def stopChat(){
        redisService.("stopChat_"+params.resId)="true"
        if(params.autoStopDuration!=null){
            Date currentDate = new Date();
            redisService.("autoStoppedTime_" +params.resId) = ""+currentDate.getTime()
            //in seconds
            redisService.("autoStoppedDuration_" +params.resId) = params.autoStopDuration
        }
        dataNotificationService.sendChatStopStartToTopic(params.resId,"stop")
        def json = ["status":"stopped"]
        render json as JSON
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def startChat(){
        redisService.deleteKeysWithPattern("stopChat_"+params.resId)
        redisService.deleteKeysWithPattern("autoStoppedTime_"+params.resId)
        redisService.deleteKeysWithPattern("autoStoppedDuration_"+params.resId)
        dataNotificationService.sendChatStopStartToTopic(params.resId,"start")
        def json = ["status":"started"]
        render json as JSON
    }

    @Secured(['ROLE_WEB_CHAT','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def getBlockedUsers(){
        if(redisService.("blockedUsers_" +params.resId)==null) dataProviderService.getCommentsBlockedUsers(params.resId)
        def json = ["blockedUsers":redisService.("blockedUsers_" +params.resId)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def isUserBlocked(){
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        String userBlocked="false"
        if("true".equals(user.commentsBlocked)) userBlocked="true"
        def json = ["userBlocked": userBlocked]
        render json as JSON

    }
    @Secured(['ROLE_USER']) @Transactional
    def getSecurityKeys(){
        if(redisService.("securityKeys_1")==null) dataProviderService.getSecurityKeys("1")
        def json = ["securityKeys":redisService.("securityKeys_1")]
        render json as JSON
    }
}
