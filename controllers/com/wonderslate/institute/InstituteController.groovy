package com.wonderslate.institute

import com.wonderslate.CreationService
import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.*
import com.wonderslate.discussions.DiscussionLevelDtl
import com.wonderslate.discussions.InstituteSubjectDtl
import com.wonderslate.groups.GroupsMembersDtl
import com.wonderslate.groups.GroupsMst
import com.wonderslate.institute.*
import com.wonderslate.librarybooks.LibraryBooksService
import com.wonderslate.log.BooksViewDtl
import com.wonderslate.log.Quizrecorder
import com.wonderslate.log.Quizrecorderdtl
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksPermissionCopy
import com.wonderslate.publish.BooksQueueDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.usermanagement.*
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.apache.commons.io.FileUtils
import org.apache.commons.lang.RandomStringUtils
import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.imgscalr.Scalr
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import pl.touk.excel.export.WebXlsxExporter
import pl.touk.excel.export.XlsxExporter

import javax.imageio.ImageIO
import java.awt.image.BufferedImage
import java.security.SecureRandom
import java.text.DateFormat
import java.text.SimpleDateFormat

class InstituteController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService
    InstituteService instituteService
    UtilService utilService
    def redisService
    WsLibraryCacheService wsLibraryCacheService
    CreationService creationService
    LibraryBooksService libraryBooksService
    MailService mailService


    @Secured(['ROLE_INSTITUTE_ADMIN']) @Transactional
    def admin(){
        if(session.getAttribute("userdetails")==null){
            session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
        }

        def instituteIds = []

        List instituteUserDtl = InstituteUserDtl.findAllByUsername(springSecurityService.currentUser.username)
            instituteUserDtl.each{ institute ->
            instituteIds << institute.instituteId

        }
        if(instituteIds.size()>0) {
            List batchesList = CourseBatchesDtl.findAllByStatusAndConductedForInList("active", instituteIds,[sort:"name" ])
            List institutes = InstituteMst.findAllByIdInList(instituteIds,[sort:"name" ])


            [batchesList: batchesList, institutes: institutes]
        }else [batchesList: [], institutes: []]
    }

    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addUserToBatch(){
        boolean userAdded=false;
        User user
        String usersAdded="",usersAddedNotRegistered="";
        String userType="";
        Integer siteId = getSiteId(request)
        String userNotAdded=""
        if(params.batchId!=null&&params.useremail!=null){
            String[] users = params.useremail.split(",")
            for(int i=0;i<users.length;i++) {
               if(users[i]==null||(users[i].trim().length()==0)) {
                    continue
                };
                if(users[i].indexOf('@')>-1) {
                    user = User.findByEmailAndSiteId(users[i].trim().toLowerCase(),siteId)
                }else {
                    if(grailsApplication.config.grails.appServer.default=="eutkarsh") {
                        user = User.findByUsernameAndSiteId(users[i].trim().toLowerCase(),siteId)
                    }else{
                        user = User.findByMobileAndSiteId(users[i].trim().toLowerCase(), siteId)
                    }
                }
                if (user != null) {
                    CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))
                    if (courseBatchesDtl != null) {
                        //check if the user is already added
                        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user.username, new Long(params.batchId))
                        if (batchUserDtl == null) {

                            batchUserDtl = new BatchUserDtl(username: user.username, batchId: new Long(params.batchId),
                                    createdBy: springSecurityService.currentUser.username,instructor: "instructor".equals(params.userType)?"true":null)
                            batchUserDtl.save(failOnError: true, flush: true)
                            dataProviderService.getBooksListForUser(user.username)
                            dataProviderService.getUserBatchesIds(user.username)
                            if(batchUserDtl.instructor==null){
                                dataProviderService.getUserBatchesAsStudent(user.username);
                            }
                            if(courseBatchesDtl.groupId!=null&&!"Default".equals(courseBatchesDtl.name)){
                                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                                if(groupsMembersDtl==null){
                                     groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "instructor".equals(params.userType)?"admin":"user",
                                             profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                            groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                                    groupsMembersDtl.save(failOnError: true, flush: true)
                                }
                            }

                            //if instructor then add to the institute teachers group
                            if(courseBatchesDtl.groupId!=null&&"Default".equals(courseBatchesDtl.name)&&"instructor".equals(params.userType)){
                                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                                if(groupsMembersDtl==null){
                                    groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "admin",
                                            profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                            groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                                    groupsMembersDtl.save(failOnError: true, flush: true)
                                }
                            }
                        }
                        userAdded = true
                        usersAdded +=" "+user.name+","
                    }
                }else{
                    //add to the back up table
                    if(siteId == 12 || siteId == 23){
                        userNotAdded+=users[i]+', '
                    }else{
                        String userToBeAdded = users[i].trim().toLowerCase()
                        if(!"eutkarsh".equals(""+dataProviderService.getSiteMst(siteId).siteName)) userToBeAdded = ""+siteId+"_"+userToBeAdded
                        BatchUserDtlNotRegistered batchUserDtlNotRegistered = BatchUserDtlNotRegistered.findByEmailAndBatchId(userToBeAdded, new Long(params.batchId))
                        if(batchUserDtlNotRegistered==null) {
                            batchUserDtlNotRegistered = new BatchUserDtlNotRegistered(email: userToBeAdded, batchId: new Long(params.batchId),
                                    createdBy: springSecurityService.currentUser.username,instructor: "instructor".equals(params.userType)?"true":null,siteId: siteId)
                            batchUserDtlNotRegistered.save(failOnError: true, flush: true)
                        }
                        usersAdded +=" "+users[i]+","
                    }
                }
            }
        }

        def json = [
                status: usersAdded.length()>0?usersAdded.substring(0,(usersAdded.length()-1))+" added.":"",
                userNotAdded: userNotAdded
        ]
        render json as JSON

    }
    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addUsersToBatch(){
        String[] userIds = params.userIds.split(",")
        for(int i=0;i<userIds.length;i++) {
           User user = User.findById(new Integer(userIds[i]))
            instituteService.addUserToBatch(user.username, new Long(params.batchId),null,"instructor".equals(params.userType) ? "true" : null,null,getSiteId(request))
        }


        def json = [
                status: "OK"
        ]
        render json as JSON

    }

@Transactional
    def genarateUsertoBatch(){
        String alreadyPresent="";
        String usersAdded="";
        int loginLimit=0;
        int password=0;
        Integer siteId = getSiteId(request)
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        boolean showErrorMessage = false
        showErrorMessage=(sm.sageOnly!="true")
        if(params.batchId!=null&&params.useremail!=null){
            Random rand = new Random();
            User user;
            // Generate random integers in range 0 to 999

            String[] users = params.useremail.split(",")
            for(int i=0;i<users.length;i++) {
                if(users[i]==null||(users[i].trim().length()==0)) {
                    continue
                };
                password = rand.nextInt(100000);
                WinGenerator winGenerator = new WinGenerator()
                winGenerator.save(failOnError: true)
                User user1=User.findByUsername(siteId+"_"+ users[i])
                InstituteMst instituteMst=InstituteMst.findById( new Long(params.instituteId))
                if(instituteMst!=null && instituteMst.usersLoginlimit!=null) {
                    loginLimit =instituteMst.usersLoginlimit

                }
                if(user1==null) {
                    user = new User(username: "" + siteId + "_" + users[i], name: "user", email: users[i], password: password + "", siteId: siteId,maxLogins: loginLimit?loginLimit:null,userType: "instructor".equals(params.userType)? params.userType : null)
                    user.win = winGenerator.id
                    user.save(failOnError: true, flush: true)
                    Role role = Role.findByAuthority("ROLE_USER")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_ADD")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                    UserRole.create(user, role, true)
                }
                if (user != null || user1!=null) {
                    CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))
                    if (courseBatchesDtl != null) {
                        BatchUserDtl batchUserDtl
                        //check if the user is already added
                        if(user1!=null) {
                             batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user1.username, new Long(params.batchId))
                        }else{
                             batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user.username, new Long(params.batchId))
                        }
                        if (batchUserDtl == null) {
                            if(user1!=null) {
                                batchUserDtl = new BatchUserDtl(username: user1.username, batchId: new Long(params.batchId),
                                        createdBy: springSecurityService.currentUser.username)
                            }else{
                                batchUserDtl = new BatchUserDtl(username: user.username, batchId: new Long(params.batchId),
                                        createdBy: springSecurityService.currentUser.username)
                            }

                            batchUserDtl.save(failOnError: true, flush: true)
                            if(user1!=null) {
                                usersAdded += " " + user1.email + ","
                            }else {
                                usersAdded += " " + user.email + ","
                            }
                            if(user1!=null) {
                                dataProviderService.getBooksListForUser(user1.username)
                            }else {
                                if(userManagementService.validateEmail(user.email,(Integer)session["siteId"],false)) {
                                    if(user.userType=='instructor'){
                                        userManagementService.sendEmailToUserBouquetRetriever(user.email,user?.id+"",SiteMst.findById(getSiteId(request)).siteName)
                                    }else{
                                        userManagementService.sendEmailToUserEbouquet(user.email,user?.id+"", SiteMst.findById(getSiteId(request)).siteName)
                                    }
                                }

                                dataProviderService.getBooksListForUser(user.username)
                            }
                        }else{
                            if(user1!=null) {
                                alreadyPresent += " "+user1.email+","
                            }else{
                                alreadyPresent += " "+user.email+","
                            }

                        }

                    }
                }
            }
        }

        def json = [
                status: usersAdded.length()>0?usersAdded.substring(0,(usersAdded.length()-1))+" added.":"",'alreadyPresent':alreadyPresent?alreadyPresent.substring(0, alreadyPresent.length() - 1)+"  is  already present in this Institute":"",showErrorMessage: showErrorMessage
        ]
        render json as JSON


    }

    @Transactional
    def updateUser(){
        def json
        def pass = params?.password
        def mobile = params?.mobile
        def state = params?.state
        def name = params?.userName
        def termsCondition =  params?.termsCondition
        def userId
        def user
        try {
            userId = Long?.parseLong(params?.userId)
            user = User?.findById(userId)
            if(user && user.sageLogin!="true"){
                user.password = springSecurityService.encodePassword(pass)
                user.mobile = mobile
                user.state = state
                user.name = name
                user.sageLogin = "true"
                if(termsCondition!=null && !"".equals(termsCondition)){
                    user.termsCondition='true'
                    user.tcAcceptedDate=new Date()
                    String ipAddress = utilService.getIPAddressOfClient(request)
                    user.ipAddress=ipAddress
                }
                if(user.validate()){
                    user.save(failOnError: true, flush: true)
                    json = ["status": "Success"]
                }else{
                    json = ["status": "Failed"]
                }
            }
            else{
                json = ["status": "Failed","msg":"User already registered"]
            }
            render json as JSON
        } catch (Exception ex) {
            json = ["status": "Failed"]
            ex.printStackTrace()
            render json as JSON
        }

    }


    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addInstitiuteAdmin(){
        def status="";
        boolean userAdded=false;
        User user
        String usersAdded="",usersAddedNotRegistered="";
        String userType="";
        Integer siteId = getSiteId(request)
        if(params.batchId!=null&&params.useremail!=null){
           def username1 = params.useremail;
                if(username1.indexOf('@')>-1)
                    user = User.findByEmailAndSiteId(username1.trim().toLowerCase(),siteId)
                else
                    user = User.findByMobileAndSiteId(username1.trim().toLowerCase(),siteId)

                //add if user does not exist
                if(user==null){
                    WinGenerator winGenerator = new WinGenerator()
                    winGenerator.save(failOnError: true)
                    user = new User(username: "" + siteId + "_" + username1.trim(), name: params.password!=null&&!"".equals(params.name)?params.name: "Institute Admin", email: username1.indexOf('@')>-1?username1:null, mobile:username1.indexOf('@')>-1?null:username1,password: params.password!=null&&!"".equals(params.password)?params.password:"password" + "", siteId: siteId)
                    user.win = winGenerator.id
                    user.save(failOnError: true, flush: true)
                    Role role = Role.findByAuthority("ROLE_USER")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_ADD")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                    UserRole.create(user, role, true)
                }
                if (user != null) {
                    CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))

                    if (courseBatchesDtl != null) {
                        //check if the user is already added
                        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user.username, new Long(params.batchId))
                        InstituteUserDtl  instituteUserDtl=InstituteUserDtl.findByUsernameAndInstituteId(user.username,courseBatchesDtl.conductedBy);
                        if (batchUserDtl == null && instituteUserDtl== null) {
                            batchUserDtl = new BatchUserDtl(username: user.username, batchId: new Long(params.batchId),
                                    createdBy: springSecurityService.currentUser.username,instructor: "instructor".equals(params.userType)?"true":null)
                            batchUserDtl.save(failOnError: true, flush: true)
                             instituteUserDtl = new InstituteUserDtl(username: user.username, instituteId: new Long(courseBatchesDtl.conductedBy))
                            instituteUserDtl.save(failOnError: true, flush: true)
                            Role role

                            boolean libraryAdmin =  true

                            if("userUploader".equals(params.libAdminOption)){
                                role = Role.findByAuthority("ROLE_LIBRARY_USER_UPLOADER")
                                libraryAdmin=false
                            }
                            else
                                role = Role.findByAuthority("ROLE_LIBRARY_ADMIN")


                            UserRole.create(user, role, true)

                            //add publishing admin access to the user.
                            InstituteMst instituteMst = InstituteMst.findById(new Long(courseBatchesDtl.conductedBy))
                            if(instituteMst.publisherId!=null&&siteId.intValue()==1){
                                user.publisherId = instituteMst.publisherId
                                user.save(failOnError: true, flush: true)
                                if(libraryAdmin) {
                                    role = Role.findByAuthority("ROLE_BOOK_CREATOR")
                                    UserRole.create(user, role, true)
                                    role = Role.findByAuthority("ROLE_PUBLISHER")
                                    UserRole.create(user, role, true)
                                    role = Role.findByAuthority("ROLE_WS_CONTENT_ADMIN")
                                    UserRole.create(user, role, true)
                                    if (courseBatchesDtl.groupId != null) {
                                        GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId, batchUserDtl.username)
                                        if (groupsMembersDtl == null) {
                                            groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "admin",
                                                    profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                                    groupId: courseBatchesDtl.groupId, name: User.findByUsername(batchUserDtl.username).name, userId: User.findByUsername(batchUserDtl.username).id)
                                            groupsMembersDtl.save(failOnError: true, flush: true)
                                        }
                                    }
                                }
                            }
                            dataProviderService.getBooksListForUser(user.username)
                            dataProviderService.getUserBatchesIds(user.username)
                            if(batchUserDtl.instructor==null){
                                dataProviderService.getUserBatchesAsStudent(user.username);
                            }
                            userAdded = true
                            usersAdded +=" "+user.name+","
                        }else{
                            status="admin present"
                        }

                    }
                }else{
                    status="no user"
                }

        }
        def json = [
                status: usersAdded.length()>0?usersAdded.substring(0,(usersAdded.length()-1))+" added.":status
        ]
        render json as JSON

    }

    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN',"ROLE_LIBRARY_ADMIN","ROLE_LIBRARY_USER_UPLOADER"]) @Transactional
    def showAllUsersForBatch()
    {
        def instituteName;
        Integer siteId = getSiteId(request)
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        InstituteMst instituteMst
        String fileNamePrefix = "Institute_Users_"
        if(params.instituteId!=null && !"".equals(params.instituteId)) {
            instituteMst = dataProviderService.getInstituteMst(new Long(params.instituteId));
            instituteName = instituteMst.name;
            fileNamePrefix = instituteName.replace(' ','_')
        }

        String additionalCondition=" and bud.instructor is null";
        if("instructor".equals(params.userType)) additionalCondition=" and bud.instructor='true'";
        String sql = "select u.name,u.email,u.id,u.mobile,u.username,COALESCE(u.max_logins,' '), COALESCE(u.user_type,' '),COALESCE(bud.admission_no,' '),COALESCE(u.sage_login,' '),bud.validity_date from wsuser.user u,wsuser.batch_user_dtl bud" +
                " where bud.batch_id="+params.batchId+" and u.username=bud.username AND RIGHT(u.username, 1) <> '_' "+additionalCondition
        if(params.sourceBatchId!=null){
            sql +=" AND u.username not in (select u.username from wsuser.user u,wsuser.batch_user_dtl bud" +
                    " where bud.batch_id="+params.sourceBatchId+" and u.username=bud.username"+additionalCondition+")"
        }

        println("sql: "+sql)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List users = results.collect { user ->

            return [name: user[0], email: user[1],userId: user[2],mobile: user[3],username: user[4].toString().split('_')[1],maxlogins: user[5],instituteName: instituteName,
                    userType: user[6], admissionNo:user[7],sageLogin:user[8],
                    validityDate:user.validity_date!=null?(new SimpleDateFormat("dd-MM-yyyy")).format(user.validity_date):""]
        }
        if("true".equals(params.download)) {
            List headers
            List withProperties
            if(siteId==12||siteId==23||siteId==24){
                headers=[(siteId==24?"Organization Name":"Institute Name"),"Name", "Email","Mobile","Username","Login Limit"]
                withProperties=["instituteName","name", "email","mobile","username","maxlogins"]
            }else if(siteId==1||sm.instituteLibrary=="true"||("true".equals(""+session["commonWhiteLabel"]))){
                if("instructor".equals(params.userType)){
                    headers=["Institute Name","Name", "Email","Mobile","Login Id","Validity"]
                    withProperties=["instituteName","name", "email","mobile","username","validityDate"]
                }else{
                    headers=["Institute Name","Name", "Email","Mobile","Login Id","Admission No.","Validity"]
                    withProperties=["instituteName","name", "email","mobile","username","admissionNo","validityDate"]
                }
            }else{
                headers=["Institute Name","Name", "Email","Mobile","Username"]
                withProperties=["instituteName","name", "email","mobile","username"]
            }
            def fileName = ""+fileNamePrefix  + (new Random()).nextInt(9999999) + ".xlsx";

            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(users, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: users ? "OK" : "not found", users: users, userType: params.userType, batchId: params.batchId]
            render json as JSON
        }
    }

    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN',"ROLE_LIBRARY_ADMIN"]) @Transactional
    def showAllUsersForInstituteWithClass()
    {
        def instituteName;
        Integer siteId = getSiteId(request)
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        InstituteMst instituteMst
        if(params.instituteId!=null && !"".equals(params.instituteId)) {
            instituteMst = dataProviderService.getInstituteMst(new Long(params.instituteId));
            instituteName = instituteMst.name;
        }

        String additionalCondition=" and bud.instructor is null";

        String sql = "select u.name,u.email,u.id,u.mobile,u.username,COALESCE(u.max_logins,' '), u.user_type,bud.admission_no,u.sage_login,cbd.name className" +
                " from wsuser.user u,wsuser.batch_user_dtl bud,course_batches_dtl cbd " +
                " where cbd.conducted_by = "+params.instituteId+" and bud.batch_id=cbd.id and cbd.name not in ('Default') and u.username=bud.username"+additionalCondition




        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List users = results.collect { user ->
            return [name: user[0], email: user[1],userId: user[2],mobile: user[3],username: user[4].toString().split('_')[1],maxlogins: user[5],instituteName: instituteName, userType: user[6], admissionNo:user[7],className:user.className]
        }
        if("true".equals(params.download)) {
            List headers
            List withProperties

                    headers=["Institute Name","Name", "Email","Mobile","Login Id","Admission No.","Class"]
                    withProperties=["instituteName","name", "email","mobile","username","admissionNo","className"]


            def fileName = ""+"Institute_Users_" + (new Random()).nextInt(9999999) + ".xlsx";

            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(users, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: users ? "OK" : "not found", users: users, userType: params.userType, batchId: params.batchId]
            render json as JSON
        }
    }

    
    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addUserLoginLimit()
    {
        Long siteId = session["siteId"]
        if (!"".equals(params.loginLimit)){
           User user = User.findByUsernameAndSiteId(siteId+"_"+params.username,siteId)
            user.maxLogins=new Long(params.loginLimit);
            user.save(failOnError: true, flush: true)
        }
        def json = [status:"OK"]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def getBatchesForInstructor(){
        String sql = "SELECT cbd.name,bbd.batch_id FROM wsuser.course_batches_dtl cbd, wsuser.books_batch_dtl bbd, wsuser.batch_user_dtl bud" +
                " where bud.username='"+springSecurityService.currentUser.username+"' and bud.user_type='true' " +
                " and bbd.batch_id=bud.batch_id and bbd.book_id="+params.bookId+" and cbd.id=bud.batch_id  and cbd.status='active';"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List batches = results.collect { batch ->
            return [name: batch[0], batchId: batch[1]]
        }

        def json = [ status: batches?"OK":"not found", batches: batches]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getAllBatchesForInstructor(){
        String sql = "SELECT cbd.name,bud.batch_id FROM wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                " where bud.username='"+springSecurityService.currentUser.username+"' and bud.user_type='instructor' " +
                " and cbd.id=bud.batch_id and cbd.status='active';"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List batches = results.collect { batch ->
            return [name: batch[0], batchId: batch[1]]
        }

        def json = [ status: batches?"OK":"not found", batches: batches]
        render json as JSON
    }
    @Secured(['ROLE_USER']) @Transactional
    def getAssignmentsForStudents(){
        List assignments
        List completedTests
        String userBatches

            List userBatchesList  = BatchUserDtl.findAllByUsername(springSecurityService.currentUser.username)
           if(userBatchesList.size()>0) {
               def tempUserBatches = []
               userBatchesList.each { userBatch ->
                   tempUserBatches << userBatch.batchId
               }

               String sql = "select bad.res_id,null resource_name, bad.date_created,null res_type,null res_link,u.name,'notquiz',null quiz_mode \n" +
                       " from  wsuser.batch_assignments_dtl bad,wsuser.user u where  bad.batch_id in ("+tempUserBatches.toString().replace('[','').replace(']','')+")\n" +
                       " and u.username=bad.created_by" +
                       " union\n" +
                       " select ts.test_id,ts.name,ts.date_created,'type','link',u.name,'quiz','quizMode' \n" +
                       " from wsuser.tests_shared ts,wsuser.user u where batch_id in ("+tempUserBatches.toString().replace('[','').replace(']','')+")\n" +
                       " and u.username=ts.created_by" +
                       " order by date_created desc "
               def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
               def sql1 = new Sql(dataSource)
               def results = sql1.rows(sql);

                assignments = results.collect { assignment ->
                    if(assignment.resource_name==null){
                        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(assignment.res_id))
                        return [testId  : assignment.res_id, name: resourceDtl.resourceName, created_date: assignment.date_created, res_type: resourceDtl.resType,
                                res_link: resourceDtl.resLink, batch_name: assignment.name, assignment_type: 'notquiz', quiz_mode: resourceDtl.quizMode]
                    }else {
                        return [testId  : assignment.res_id, name: assignment.name, created_date: assignment.date_created, res_type: assignment.res_type,
                                res_link: assignment.res_link, batch_name: assignment.name, assignment_type: "quiz", quiz_mode: "quizMode"]

                    }
               }
                if (assignments.size() > 0) {
                   def tests = []
                   assignments.each { assignment ->
                       if("type".equals(assignment.res_type))
                       tests << assignment.testId
                   }
                   completedTests = Quizrecorder.findAllByUsernameAndTestgenidInList(springSecurityService.currentUser.username, tests)
               }
           }
        def json = [assignments: assignments, completedTests: completedTests]
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def getAssignmentsForInstructors(){

        String sql = "select bad.res_id,null resource_name, bad.date_created,null res_type,null res_link,cbd.name,'notquiz' " +
                " from wsuser.batch_assignments_dtl bad,wsuser.course_batches_dtl cbd where  bad.created_by='"+springSecurityService.currentUser.username+"'\n" +
                " and cbd.id=bad.batch_id " +
                " union\n" +
                " select ts.test_id,ts.name,ts.date_created,'type' res_type,'link' res_link,cbd.name,'quiz'" +
                " from wsuser.tests_shared ts,wsuser.course_batches_dtl cbd  where created_by='"+springSecurityService.currentUser.username+"'\n" +
                " and cbd.id=ts.batch_id order by date_created desc "
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List assignments = results.collect { assignment ->
            if(assignment.resource_name==null){
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(assignment.res_id))
                return [testId  : assignment.res_id, name: resourceDtl.resourceName, created_date: assignment.date_created, res_type: resourceDtl.resType,
                        res_link: resourceDtl.resLink, batch_name: assignment.name, assignment_type: 'notquiz', quiz_mode: null]
            }else {
                return [testId  : assignment.res_id, name: assignment.name, created_date: assignment.date_created, res_type: assignment.res_type,
                        res_link: assignment.res_link, batch_name: assignment.name, assignment_type: "quiz", quiz_mode: null]

            }
        }
         def json = [assignments: assignments]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getAssementDetails() {
        List assessmentsHeaders = Quizrecorder.findAllByTestgenid(new Long(params.testId))
        TestsShared testsShared = TestsShared.findByTestId(new Long(params.testId))
        List assessmentDetails
        List objectiveMstIds
        List questions
        def quizRecorderIds = []
        def objMstIds = []
        def usernames = []
        if (assessmentsHeaders.size() > 0) {
            assessmentsHeaders.each { assessment ->
                quizRecorderIds << assessment.id
                usernames.add(["username": assessment.username, "name": User.findByUsername(assessment.username).name])

            }
            assessmentDetails = Quizrecorderdtl.findAllByQuizrecorderidInList(quizRecorderIds)
            objectiveMstIds = TestsDtl.findAllByTestId(new Long(params.testId))

            objectiveMstIds.each { ojbMstId ->
                objMstIds << ojbMstId.objId
            }

            questions = ObjectiveMst.findAllByIdInList(objMstIds)
    }
        def json = [assessmentHeaders: assessmentsHeaders, assessmentDetails: assessmentDetails, objIds: objectiveMstIds,
                    questions:questions,testsShared:testsShared,usernames:usernames]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getBatchesToShare(){
        String sql = "SELECT cbd.name,bud.batch_id FROM wsuser.books_batch_dtl bbd,wsuser.batch_user_dtl bud,wsuser.course_batches_dtl cbd" +
                " where bud.username='"+springSecurityService.currentUser.username+"' and bud.instructor='true' " +
                " and bbd.book_id="+params.bookId+" and bbd.batch_id=bud.batch_id and cbd.id=bbd.batch_id and cbd.status='active'"
         def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List instructorBatches = results.collect { batch ->
            return [name: batch[0], batchId: batch[1]]
        }
        def batches = []
        //check the batches for which the given resource is not yet shared.
        instructorBatches.each{
            instructorBatch ->
                BatchResourcesDtl batchResourcesDtl = BatchResourcesDtl.findByBatchIdAndResId(instructorBatch.batchId,new Long(params.resId))
            //if the resource is not already shared then add to the array.
            if(batchResourcesDtl==null)
            batches.add([name:instructorBatch.name, batchId:instructorBatch.batchId])
        }

        def json = [status: batches?"OK":"not found",batches: batches]
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def createAssignmentFromQuiz(){
        TestsMst testsMst = new TestsMst(createdBy: springSecurityService.currentUser.username,name: (params.testName!=null&&params.testName.length()>0)?params.testName:"Created Test")
        testsMst.save(failOnError: true, flush: true)
        if(params.batchIds!=null&&params.batchIds.length()>0){
            String[] batchIds = params.batchIds.split(",")
            for(int i=0;i<batchIds.length;i++){
                TestsShared testsShared =  new TestsShared(testId: testsMst.id,name:(params.testName!=null&&params.testName.length()>0)?params.testName:"Created Test",
                        batchId: new Long(batchIds[i]),createdBy:springSecurityService.currentUser.username )
                testsShared.save(failOnError: true, flush: true)
            }

        }
        List objectivesMst  = ObjectiveMst.findAllByQuizId(new Integer(params.quizId))
        objectivesMst.each {objectiveMst ->
            TestsDtl testsDtl =  new TestsDtl(testId: testsMst.id,objId: objectiveMst.id)
            testsDtl.save(failOnError: true, flush: true)

        }
        def json = ["status":"success"]
        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def shareResouces(){
        if(params.batchIds!=null&&params.batchIds.length()>0){
            String[] batchIds = params.batchIds.split(",")
            for(int i=0;i<batchIds.length;i++){
                BatchResourcesDtl batchResourcesDtl = new BatchResourcesDtl(batchId: new Long(batchIds[i]),
                        resId: new Integer(params.resId), chapterId: new Integer(params.chapterId))
                batchResourcesDtl.save(flush: true, failOnError: true)
                dataProviderService.getBatchResourcesForChapter(new Long(params.chapterId),new Long(batchIds[i]))
            }
        }
        def json = ["status":"success"]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def shareAssignment(){
        if(params.batchIds!=null&&params.batchIds.length()>0){
            String[] batchIds = params.batchIds.split(",")
            for(int i=0;i<batchIds.length;i++){
                BatchAssignmentsDtl batchAssignmentsDtl = new BatchAssignmentsDtl(batchId: new Long(batchIds[i]),
                        resId: new Integer(params.resId),createdBy:springSecurityService.currentUser.username)
                batchAssignmentsDtl.save(flush: true, failOnError: true)
            }
        }
        def json = ["status":"success"]
        render json as JSON
    }
    @Secured(['ROLE_USER']) @Transactional
    def addBatch(){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date endDate=null
        if(params.endDate!=null&&!"".equals(params.endDate)){
            endDate = df.parse(params.endDate);
        }
        CourseBatchesDtl courseBatchesDtl = new CourseBatchesDtl(courseId: new Long(1),conductedBy: new Long(params.instituteId),conductedFor: new Long(params.instituteId),
        status: "active",name: params.batchName,endDate: endDate,syllabus: params.syllabus,grade: params.grade)
        courseBatchesDtl.save(failOnError: true, flush: true)
        dataProviderService.getInstituteMembersCount(new Long(params.instituteId))
        //add the group for this class. Currently this functionality will be available only for Wonderslate main site
        InstituteMst instituteMst = InstituteMst.findById(new Long(params.instituteId))
        if(instituteMst!=null&&instituteMst.siteId.intValue()==1){
            // add group for this batch.
            GroupsMst groupsMst = new GroupsMst(name: courseBatchesDtl.name,
                    privacyType: "private", visibility: "hidden", createdBy: springSecurityService.currentUser.username,
                    siteId:instituteMst.siteId,allPost: "false",groupType: "channel",batchId: courseBatchesDtl.id,instituteId: instituteMst.id)
            groupsMst.save(failOnError: true, flush: true)

            courseBatchesDtl.groupId = groupsMst.id
            courseBatchesDtl.save(failOnError: true, flush: true)
            DiscussionLevelDtl discussionLevelDtl = new DiscussionLevelDtl(level: "institute",
                    siteId:instituteMst.siteId,batchId: courseBatchesDtl.id,instituteId: instituteMst.id)
            discussionLevelDtl.save(failOnError: true, flush: true)

            if("true".equals(instituteMst.copyDefaultBooks)&&instituteMst.level!=null&&instituteMst.syllabus!=null&&courseBatchesDtl.grade!=null){
               instituteService.addDefaultBooksToBatch(courseBatchesDtl,instituteMst.level,instituteMst.syllabus)
            }
        }
        def json = ["status":"success"]
        render json as JSON
    }



    @Secured(['ROLE_USER']) @Transactional
    def batchCompleted(){
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))
        courseBatchesDtl.status="completed"
        courseBatchesDtl.save(failOnError: true, flush: true)
        def json = ["status":"success"]
        render json as JSON
    }
    def deleteBatchUser(){


    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN','ROLE_LIBRARY_USER_UPLOADER']) @Transactional
    def getBooksForBatch(){
        Integer siteId = getSiteId(request)
        String optionalCondition=""
        String fileNamePrefix = "Institute_"
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Integer(params.batchId))

        if(courseBatchesDtl!=null){
            InstituteMst instituteMst = InstituteMst.findById(courseBatchesDtl.conductedBy)
            if(instituteMst!=null){
                fileNamePrefix = instituteMst.name.replace(' ','_')
            }

        }
        //do not show books created by the institute to WS admin
        if(session["userdetails"]!=null&&session["userdetails"].publisherId==null) optionalCondition =" and (bm.status in ('published','private') or bm.status is null) "
        String sql = " select bm.id,bm.title,bm.isbn,bm.status, bm.publisher_id,bbd.number_of_licenses,bbd.validity,bm.authors" +
                " from books_mst bm, wsuser.books_batch_dtl bbd" +
                " where bm.id=bbd.book_id" +
                optionalCondition+
                " and  bbd.batch_id="+params.batchId;
        if(params.sourceBatchId!=null){
            sql += " and bm.id not in (select bm.id" +
                    " from books_mst bm, wsuser.books_batch_dtl bbd" +
                    " where bm.id=bbd.book_id" +
                    optionalCondition+
                    " and  bbd.batch_id="+params.sourceBatchId+")"
        }
        sql +=" order by id desc"
        
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String link =""
        List  booklist =null
        if(results!=null) {
            booklist = results.collect { comp ->
                if(siteId.intValue()==80){
                    link = "https://knimbus.wonderslate.com/"+comp.title.trim().toLowerCase().replaceAll(' ','-')+"/ebook?bookId="+comp.id+"&site=knimbus"
                }
                return [bookId: comp.id, Isbn: comp.isbn ? comp.isbn : "", title: comp.title, bookStatus: (comp.status != null) ? comp.status : 'unpublished', batchId: params.batchId,
                        publisher: (comp.publisher_id!=null && comp.publisher_id!= "")?Publishers.findById(new Long(comp.publisher_id)).name:'',
                        noOfLic: comp.number_of_licenses, validity: comp.validity,link:link,authors:comp.authors]
                  }
            }
        if("true".equals(params.download)) {
            List headers
            List withProperties
            if(siteId.intValue()==12||siteId.intValue()==23||siteId.intValue()==24) {
                headers = ["Book Id", "Title", "Isbn", "Book Status"]
                withProperties = ["bookId", "title", "Isbn", "bookStatus"]
            }else if(siteId.intValue()==80) {
                headers = ["Book Id", "Title", "Isbn", "Publisher","Author","Link"]
                withProperties = ["bookId", "title", "Isbn", "publisher","authors","link"]
            }  else {
                headers = ["BookId", "Title", "Isbn", "Publisher", "Book Status", "Number of Copies", "Validity (in days)"]
                withProperties = ["bookId", "title", "Isbn", "publisher", "bookStatus", "noOfLic", "validity"]
            }
            def fileName = ""+fileNamePrefix  + (new Random()).nextInt(9999999) + ".xlsx";

            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(booklist, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: booklist ? "OK" : "no books", books: booklist]
            render json as JSON
        }
    }

    @Secured ('ROLE_USER')
    def getBooksReport(){
        String sql = " select bm.title,u.name,coalesce(cbd.end_date,' ')" +
                " from books_mst bm, wsuser.books_batch_dtl bbd,wsuser.batch_user_dtl bud, wsuser.user u,wsuser.course_batches_dtl cbd " +
                " where bm.id=bbd.book_id" +
                " and bbd.batch_id=bud.batch_id and u.username=bud.username and bbd.batch_id="+params.batchId+" and cbd.id=bbd.batch_id group by bm.title,u.name";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List  booklist =null
        if (results != null && results.size() > 0) {
            booklist = results.collect { comp ->
                return [title   : comp[0],  username: comp[1], date: comp[2]
                         ]
            }
        }
        def json = [status:booklist ? "OK" : "Not present", booklist: booklist]
        render json as JSON
    }




    @Secured ('ROLE_USER') @Transactional
    def addBookForBatch(){

        boolean addBook=false
        def status="NOT OK"
       if(params.bookId!=null&&params.batchId!=null){
           BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
           if(booksMst!=null) {
               if (booksMst.price==null||booksMst.price.doubleValue() == 0) {
                   addBook = true
               } else {
                   //check super admin
                   if (("" + session["userdetails"].publisherId).equals("" + booksMst.publisherId) || session["userdetails"].authorities.any {
                       it.authority == "ROLE_WS_CONTENT_CREATOR"
                   }) {
                       //its the publisher who is adding so its ok
                      addBook = true
                   }
               }

               if (addBook) {
                   BooksBatchDtl booksBatchDtl = new BooksBatchDtl(batchId: new Long(params.batchId), bookId: new Long(params.bookId))
                   booksBatchDtl.save(failOnError: true, flush: true)
                    dataProviderService.booksInBatch(new Long(params.batchId))
                   dataProviderService.bookInBatch(new Long(params.bookId))
                   status = "OK"

                   //resetting the library of users
                   List users = BatchUserDtl.findAllByBatchId(new Long(params.batchId))

                   users.each {user ->
                       redisService.(user.username+"_"+"booksList")=null;
                   }

               }
               redisService.("userMyLibraryInstituteBooks_"+params.batchId) = null
               wsLibraryCacheService.getInstituteBooksPagination(params.batchId)
               wsLibraryCacheService.getInstituteBooksPaginationNew(params.batchId)
           }
       }
        def json = ["status":status]
        render json as JSON


    }

    @Secured ('ROLE_USER') @Transactional
    def  removeBookFromBatch(){
        boolean addBook=false
        def status="NOT OK"
        Long siteId = session["siteId"]
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String[] bookId = params.bookId.split(",")
        for(int i=0;i<bookId.length;i++) {
            if (bookId != null && params.batchId != null) {
                BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBookIdAndBatchId(bookId[i], new Long(params.batchId))
                booksBatchDtl.delete(flush: true)
                status = "OK"
                //resetting the library of users
                List users = BatchUserDtl.findAllByBatchId(new Long(params.batchId))

                users.each { user ->
                    redisService.(user.username + "_" + "booksList") = null;
                    BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(bookId[i], user.username)
                    if (booksPermission != null) booksPermission.delete(flush: true)
                }
                dataProviderService.booksInBatch(new Long(params.batchId))
                dataProviderService.bookInBatch(new Long(bookId[i]))
                CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))
                if(siteId.intValue()==1 || siteMst.instituteLibrary=="true") {
                    redisService.("userMyLibraryInstituteBooks_" + params.batchId) = null
                    wsLibraryCacheService.getInstituteBooksPagination(params.batchId)
                    wsLibraryCacheService.getInstituteBooksPaginationNew(params.batchId)
                }
                if (courseBatchesDtl != null) {
                    dataProviderService.getLibraryBooks(courseBatchesDtl.conductedBy,siteId)
                    dataProviderService.getBooksTagListForInstitute(courseBatchesDtl.conductedBy);
                }
            }
        }
        def json = ["status":status]
        render json as JSON
    }

    @Secured ('ROLE_USER') @Transactional
    def  removeBookFromInstitute(){
        boolean addBook=false
        def status="NOT OK"
        Long siteId = session["siteId"]
        String[] bookId = params.bookId.split(",")
        List batches = CourseBatchesDtl.findAllByConductedBy(new Integer(params.instituteId))
        batches.each { batch ->
            for (int i = 0; i < bookId.length; i++) {
                if (bookId != null) {
                    BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBookIdAndBatchId(bookId[i], batch.id)
                    if (booksBatchDtl != null) {
                        booksBatchDtl.delete(flush: true)
                        status = "OK"
                        //resetting the library of users
                        List users = BatchUserDtl.findAllByBatchId(batch.id)

                        users.each { user ->
                            redisService.(user.username + "_" + "booksList") = null;
                            BooksPermission booksPermission = BooksPermission.findByBookIdAndUsername(bookId[i], user.username)
                            if (booksPermission != null) booksPermission.delete(flush: true)
                        }
                        dataProviderService.booksInBatch(batch.id)
                        dataProviderService.bookInBatch(new Long(bookId[i]))
                        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batch.id)
                        if (siteId.intValue() == 1 || siteId.intValue() == 25 ||  siteId.intValue()==26) {
                        redisService.("userMyLibraryInstituteBooks_" + batch.id) = null
                            wsLibraryCacheService.getInstituteBooksPagination(batch.id)
                            wsLibraryCacheService.getInstituteBooksPaginationNew(batch.id)
                        }
                        if (courseBatchesDtl != null) {
                            dataProviderService.getLibraryBooks(courseBatchesDtl.conductedBy, siteId)
                            dataProviderService.getBooksTagListForInstitute(courseBatchesDtl.conductedBy);
                        }
                    }
                }
            }
        }
        def json = ["status":status]
        render json as JSON
    }


    @Secured ('ROLE_USER') @Transactional
    def  removeBookFromBatchSage(){
        def status="NOT OK"
        Long siteId = session["siteId"]
            if (params.bookId != null && params.batchId != null) {
                CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))
                List courses = []
                if ("Default".equals(courseBatchesDtl.name)) {
                    courses = CourseBatchesDtl.findAllByConductedByAndStatus(courseBatchesDtl.conductedBy, "active")
                } else {
                    courses.add(courseBatchesDtl)
                }
                String batchId = ""
                for (int i = 0; i < courses.size(); i++) {
                    batchId = "" + courses[i].id
                    courseBatchesDtl = courses[i]
                    BooksBatchDtl.executeUpdate("delete BooksBatchDtl where bookId IN (" + params.bookId + ") and batchId='" + batchId + "'")
                    status = "OK"
                    //resetting the library of users
                    List users = BatchUserDtl.findAllByBatchId(new Long(batchId))

                    users.each { user ->
                        if (siteId.intValue() == 1 || siteId.intValue() == 25 ||  siteId.intValue()==26) redisService.("lastReadBooksIns_" + batchId + "_" + user.username) = null;
                        redisService.(user.username + "_" + "booksList") = null;
                        BooksPermission.executeUpdate("delete BooksPermission where bookId IN (" + params.bookId + ") and username='" + user.username + "'")
                        dataProviderService.getLastReadBooks(user.username)
                    }
                    if (siteId.intValue() == 1 || siteId.intValue() == 25 ||  siteId.intValue()==26) {
                        List booksPermission = BooksPermission.findAllByBatchId(new Long(batchId))
                        booksPermission.each { booksPermissions ->
                            redisService.("lastReadBooksIns_" + batchId + "_" + booksPermissions.username) = null;
                            BooksPermission.executeUpdate("delete  BooksPermission where bookId IN (" + params.bookId + ") and batchId='" + batchId + "'")
                        }
                        BooksQueueDtl.executeUpdate("delete  BooksQueueDtl where bookId IN (" + params.bookId + ") and batchId='" + batchId + "'")
                        redisService.("userMyLibraryInstituteBooks_" + batchId) = null
                        wsLibraryCacheService.getInstituteBooksPagination(batchId)
                        wsLibraryCacheService.getInstituteBooksPaginationNew(batchId)
                        libraryBooksService.getInstituteBooksPagination(batchId)
                        libraryBooksService.getInstituteBooksTags(batchId)
                    }



                if (courseBatchesDtl != null) {
                    dataProviderService.getLibraryBooks(courseBatchesDtl.conductedBy, siteId)
                    dataProviderService.getLibraryBooksForUser(courseBatchesDtl.conductedBy)

                }

                    wsLibraryCacheService.instituteBooksforUser(""+batchId)
            }
        }
        def json = ["status":status]
        render json as JSON
    }

    @Secured ('ROLE_USER') @Transactional
    def removeUserFromBatch(){

        if(params.userId!=null&&params.batchId!=null){
           instituteService.removeUserFromBatch(new Integer(params.userId),new Integer(params.batchId))
        }
        def json = ["status":"OK"]
        render json as JSON

    }

    @Secured ('ROLE_USER') @Transactional
    def removeUsersFromBatch(){
        String[] userIds = params.userIds.split(",")
        for(int i=0;i<userIds.length;i++) {
            instituteService.removeUserFromBatch(new Integer(userIds[i]),new Integer(params.batchId))
        }

        def json = ["status":"OK"]
        render json as JSON

    }
    @Transactional
    def getLibraryBooks(){
        Long siteId = session["siteId"]
        def books,booksTagDtl;
        InstituteMst instituteMst
        def instituteId = getInstituteId(siteId)

        if(instituteId!=null)
        {
            String username=null
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndStatus(new Long(instituteId),"active");
            if(courseBatchesDtl!=null) {
                instituteMst = dataProviderService.getInstituteMst(instituteId)
                if (redisService.("librarybooklist_" + instituteId) == null) dataProviderService.getLibraryBooks(instituteId,siteId)
                books = redisService.("librarybooklist_" + instituteId);
                if (springSecurityService.currentUser != null) {
                    username = springSecurityService.currentUser.username
                    if (redisService.("librarybooklist_" + username) == null) dataProviderService.getLibraryBooksForUser(instituteId)
                    if(redisService.("lastReadBooks_"+springSecurityService.currentUser.username)==null){
                        dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
                    }
                }

            }
             def json = [
                                 'books':books,
                                 'status' : books ? "OK" : "Nothing present",
                                 'booksTag' : booksTagDtl,
                                 'institutionEmail':instituteMst?instituteMst.contactEmail:"",
                                 'instituteName': instituteMst?instituteMst.name:"",
                                'userBookIds':username?redisService.("librarybookIdslistforuser_" + username):"",
                                 'totalBooks':redisService.("librarybooklist_"+ instituteId+"_totalBooks"),
                                 'lastReadBooks':username?redisService.("lastReadBooks_"+springSecurityService.currentUser.username):""
                         ]

            render json as JSON
        }
    }
    @Transactional
    def getLibraryBookIds(){
        def siteId;
        if(params.siteId!=null && params.siteId!=""){
            siteId=params.siteId;
        }else{
             siteId = session["siteId"]
        }

        InstituteMst instituteMst
         def instituteId = getInstituteId(siteId)
        if(instituteId!=null)
        {
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndStatus(new Long(instituteId),"active");
            if(courseBatchesDtl!=null) {
                instituteMst = dataProviderService.getInstituteMst(instituteId)
                if (redisService.("librarybooklist_" + instituteId) == null) dataProviderService.getLibraryBooks(instituteId,siteId)
            }
            def json = [

                    'status' : "OK",
                    'institutionEmail':instituteMst?instituteMst.contactEmail:"",
                    'instituteName': instituteMst?instituteMst.name:"",
                    'libraryBookIds':instituteMst?redisService.("librarybookidlist_" + instituteId):""
            ]
            render json as JSON
        }
    }
    @Transactional
    def getInstituteId(siteId){
        String ipAddress = getIPAddressOfClient()
        def instituteId = null
        InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress,siteId)
        if(instituteIPAddress==null){
            if(springSecurityService.currentUser!=null) {
                String sql = "select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                        " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'  and im.site_id=" + siteId;
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                if (results.size() > 0) {
                    instituteId = results[0][0]

                } else {
                    def json = ['status': "Nothing present"]
                    render json as JSON
                }
            }
            else {
                def json = ['status': "Nothing present"]
                render json as JSON
            }
        }else{
            instituteId = instituteIPAddress.institute_id
        }

        return instituteId
    }
    def getPaginatedLibraryBooks()
    {
        Long siteId = session["siteId"]
        def pageNo = params.pageNo
        def instituteId = getInstituteId(siteId)
        if(instituteId!=null){
            if(redisService.("librarybooklist_" + instituteId)==null) dataProviderService.getLibraryBooks(instituteId,siteId)
            def books = redisService.("librarybooklist_"+ instituteId+"_page_"+pageNo);
           
            def json = [books: books]

            render json as JSON

        }

    }

    @Transactional
    def getUserLibraryBooks(){
        Long siteId = session["siteId"]
        def instituteId = getInstituteId(siteId)
        if(instituteId!=null&&springSecurityService.currentUser!=null){
            String username=springSecurityService.currentUser.username

            if(redisService.("librarybooklist_"+username)==null) dataProviderService.getLibraryBooksForUser(instituteId)

            def books = redisService.("librarybooklistforuser_" + username);
           def json = [
                    'books':books,
                    'status' : books ? "OK" : "Nothing present",

           ]
            render json as JSON
        }
    }


    String getIPAddressOfClient() {
        //https://www.oodlestechnologies.com/blogs/Java-Get-Client-IP-Address
        String remoteAddr = request.getHeader("X-FORWARDED-FOR");
        if(remoteAddr == null || "".equals(remoteAddr)) {
            remoteAddr = request.getRemoteAddr();
            if(remoteAddr.equalsIgnoreCase("0:0:0:0:0:0:0:1")) {
                InetAddress inetAddress = InetAddress.getLocalHost();
                String ipAddress = inetAddress.getHostAddress();
                remoteAddr = ipAddress;
            }
        }

        return remoteAddr
    }


    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def getInstituteDetails(String instituteId){
        InstituteMst instituteMst = dataProviderService.getInstituteMst(Long.parseLong(instituteId))
        if(instituteMst.secretKey==null) instituteMst.secretKey = createSecretKey()
        instituteMst.save(failOnError: true, flush: true)
        CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedBy(new Long(instituteId))
        String endDate = ""
        String startDate = ""
        if(courseBatchesDtl.endDate != null && !courseBatchesDtl.endDate.equals("")) endDate = (new SimpleDateFormat("yyyy-MM-dd")).format(courseBatchesDtl.endDate)
        if(courseBatchesDtl.startDate != null && !courseBatchesDtl.startDate.equals("")) startDate = (new SimpleDateFormat("yyyy-MM-dd")).format(courseBatchesDtl.startDate)
        def json = [
                'instituteName': instituteMst.name,
                'instituteNoOfusers': instituteMst.noOfUsers,
                'instituteContactName':instituteMst.contactName,
                'instituteContactEmail':instituteMst.contactEmail,
                'instituteIPRestricted':instituteMst.ipRestricted,
                'endDate':endDate,
                'startDate':startDate,
                'checkOutDays': instituteMst.checkOutDays,
                'status' : instituteMst ? "OK" : "Nothing present",
                'fullLibraryView' : instituteMst.fullLibraryView,
                'paidFreeTab' : instituteMst.paidFreeTab,
                'eduWonder' : instituteMst.eduWonder,
                'level' :instituteMst.level,
                'syllabus' : instituteMst.syllabus,
                'leaderBoardEnabled' : instituteMst.leaderBoardEnabled,
                'shopEnabled':instituteMst.shopEnabled,
                'copyDefaultBooks':instituteMst.copyDefaultBooks,
                'defaultBooksTemplateInstitute':instituteMst.defaultBooksTemplateInstitute,
                'goneLive':instituteMst.goneLive,
                'preUniversity':instituteMst.preUniversity,
                'secretKey':instituteMst.secretKey
        ]
        render json as JSON
    }

    @Transactional
    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_IBOOKGPT_SITE_ADMIN'])
    def addInstitute(){
        Integer noOfusers;
        String instituteName = params.name
        String contactName = params.contactName
        String contactEmail = params.contactEmail
        String checkOutDays = params.checkOutDays
        String fullLibraryView = params.fullLibraryView
        String paidFreeTab = params.paidFreeTab
        String eduWonder = params.eduWonder
        String level = params.level
        String syllabus = params.syllabus
        String leaderBoardEnabled = params.leaderBoardEnabled
        String shopEnabled = params.shopEnabled
        String defaultBooksTemplateInstitute = params.defaultBooksTemplateInstitute
        String copyDefaultBooks = params.copyDefaultBooks
        String goneLive = params.goneLive
        String preUniversity = params.preUniversity
        String driveForInstructor = params.driveForInstructor
        String driveForStudent = params.driveForStudent
        String raForInstructor = params.raForInstructor
        String raForStudent = params.raForStudent
        String enableTest = params.enableTest
        String enableAnalytics = params.enableAnalytics
        String enableQuestionPaper  = params.enableQuestionPaper
        String showReferenceSection = params.showReferenceSection
        if(params.noOfusers!=""&& params.noOfusers!=null) {
           noOfusers = new Integer(params.noOfusers);
        }else{
            noOfusers=null;;
        }
        boolean institutionEdit = Boolean.parseBoolean(params.institutionEdit);
        String ipRestricted = "false";
        Long siteId = session["siteId"]

        if(params.ipRestricted!=null){
            ipRestricted = params.ipRestricted
        }
        String institutionId = params.institutionId;
        if(institutionEdit == false) {
            InstituteMst instituteMst = new InstituteMst(name: instituteName, type: "Default", siteId: siteId, ipRestricted: ipRestricted, contactName: contactName, contactEmail: contactEmail,
                    noOfUsers: noOfusers, publisherId: params.publisherId ? params.publisherId : null, checkOutDays: checkOutDays,fullLibraryView:fullLibraryView,paidFreeTab:paidFreeTab,
                    eduWonder:eduWonder,level:level,syllabus:syllabus,shopEnabled:shopEnabled,leaderBoardEnabled: leaderBoardEnabled,
                    defaultBooksTemplateInstitute:defaultBooksTemplateInstitute,copyDefaultBooks:copyDefaultBooks,goneLive: goneLive,
                    preUniversity:preUniversity,secretKey: createSecretKey(),driveForInstructor: driveForInstructor,driveForStudent: driveForStudent,
                    raForInstructor: raForInstructor,raForStudent: raForStudent,enableTest: enableTest,enableAnalytics: enableAnalytics,
                    enableQuestionPaper: enableQuestionPaper,showReferenceSection: showReferenceSection)
            instituteMst.save(failOnError: true, flush: true)
            institutionId = "" + instituteMst.id
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Date endDate = null
            Date startDate = null
            if (params.endDate != null && params.endDate != "") {
                endDate = df.parse(params.endDate);
            }
            if (params.startDate != null && params.startDate != "") startDate = df.parse(params.startDate)
            CourseBatchesDtl courseBatchesDtl = new CourseBatchesDtl(courseId: new Long(1), conductedBy: instituteMst.id, conductedFor: instituteMst.id,
                    status: "active", name: "Default", endDate: endDate, startDate: startDate)
            courseBatchesDtl.save(failOnError: true, flush: true)
            if(eduWonder=="true") {
                DiscussionLevelDtl discussionLevelDtl = new DiscussionLevelDtl(level: "institute",
                        siteId:instituteMst.siteId,instituteId: instituteMst.id,batchId:courseBatchesDtl.id)
                discussionLevelDtl.save(failOnError: true, flush: true)
            }
            if(siteId.intValue()==1) {
                //add institute as the publisher also. This is important for the institute to create the books.
                Publishers publishers = new Publishers(
                        siteId: getSiteId(request),
                        name: instituteName,
                        contactPerson: contactName,
                        email: contactEmail,
                        publisherType: "systemCreated"
                )
                publishers.save(failOnError: true, flush: true)
                instituteMst.publisherId = publishers.id
                instituteMst.save(failOnError: true, flush: true)

                GroupsMst groupsMst = new GroupsMst(name: "Super group",
                        privacyType: "private", visibility: "hidden", createdBy: springSecurityService.currentUser.username,
                        siteId:instituteMst.siteId,allPost: "true",groupType: "channel",batchId: courseBatchesDtl.id,instituteId: instituteMst.id)
                groupsMst.save(failOnError: true, flush: true)

                courseBatchesDtl.groupId = groupsMst.id
                courseBatchesDtl.save(failOnError: true, flush: true)
                //copy default books
                if("true".equals(instituteMst.copyDefaultBooks)){
                    instituteService.addDefaultBooksToInstitute(instituteMst.id)
                }

                //add default subjects
                String[] subjects= ["English","Hindi","General","Kannada","Mathematics","Science","Social Science"]
                for(int i=0;i<subjects.length;i++){
                    InstituteSubjectDtl instituteSubjectDtl = new InstituteSubjectDtl(siteId:instituteMst.siteId,instituteId: instituteMst.id,name:subjects[i])
                    instituteSubjectDtl.save(failOnError: true, flush: true)
                }

            }

        }else{
             institutionId = params.institutionId;
            InstituteMst instituteMst = InstituteMst.findById(new Long(institutionId))
            instituteMst.name = instituteName
            instituteMst.contactEmail = contactEmail
            instituteMst.contactName = contactName
            instituteMst.ipRestricted = ipRestricted;
            instituteMst.noOfUsers = noOfusers;
            instituteMst.checkOutDays = checkOutDays;
            instituteMst.fullLibraryView = fullLibraryView;
            instituteMst.paidFreeTab = paidFreeTab;
            instituteMst.eduWonder =  eduWonder
            instituteMst.level = level
            instituteMst.syllabus = syllabus
            instituteMst.shopEnabled = shopEnabled
            instituteMst.leaderBoardEnabled = leaderBoardEnabled
            instituteMst.driveForInstructor = driveForInstructor
            instituteMst.driveForStudent = driveForStudent
            instituteMst.raForInstructor = raForInstructor
            instituteMst.raForStudent = raForStudent
            instituteMst.enableTest = enableTest
            instituteMst.enableAnalytics = enableAnalytics
            instituteMst.enableQuestionPaper = enableQuestionPaper
            instituteMst.showReferenceSection = showReferenceSection

            if(copyDefaultBooks!=null&&!copyDefaultBooks.equals(instituteMst.copyDefaultBooks)&&"true".equals(copyDefaultBooks)){
                //if the settings has changed to copy default books call the default copy method
                instituteService.addDefaultBooksToInstitute(instituteMst.id)
            }
            instituteMst.copyDefaultBooks = copyDefaultBooks
            if(!"".equals(defaultBooksTemplateInstitute)&&!defaultBooksTemplateInstitute.equals(instituteMst.defaultBooksTemplateInstitute)){
                instituteMst.defaultBooksTemplateInstitute = defaultBooksTemplateInstitute

            }
            if(goneLive!=null&&!goneLive.equals(instituteMst.goneLive)&&"true".equals(goneLive)){
                instituteMst.goneLiveDate = new Date()
                instituteService.getLatestReleasedInstitutes("min")
                instituteService.getLatestReleasedInstitutes("max")
            }
            instituteMst.goneLive = goneLive
            instituteMst.preUniversity = preUniversity
            instituteMst.save(failOnError: true, flush: true)

            DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
            Date endDate=null
            Date startDate = null
            if(params.endDate!=null&&params.endDate!=""){
                endDate = df.parse(params.endDate);
            }
            if(params.startDate != null && params.startDate != "") startDate = df.parse(params.startDate)
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedBy(new Long(institutionId))
            courseBatchesDtl.endDate = endDate
            courseBatchesDtl.startDate = startDate
            if(params.endDate!=null){
                courseBatchesDtl.status="active"
            }
            courseBatchesDtl.save(failOnError: true, flush: true)

            //for the institutes created before creating default publishing for them.
            if(instituteMst.publisherId==null&&siteId.intValue()==1){
                Publishers publishers = new Publishers(
                        siteId: instituteMst.siteId,
                        name: instituteMst.name,
                        contactPerson: instituteMst.contactName,
                        email: instituteMst.contactEmail,
                        publisherType: "systemCreated"
                )
                publishers.save(failOnError: true, flush: true)
                instituteMst.publisherId = publishers.id
                instituteMst.save(failOnError: true, flush: true)

                //add publishing admin access to the user.
                Role role
               List adminUsers = InstituteUserDtl.findAllByInstituteId(instituteMst.id)
                adminUsers.each { adminUser ->
                    User user = dataProviderService.getUserMst(adminUser.username)
                        user.publisherId = instituteMst.publisherId
                        user.save(failOnError: true, flush: true)
                        role = Role.findByAuthority("ROLE_BOOK_CREATOR")
                        UserRole.create(user, role, true)
                        role = Role.findByAuthority("ROLE_PUBLISHER")
                        UserRole.create(user, role, true)
                        role = Role.findByAuthority("ROLE_WS_CONTENT_ADMIN")
                        UserRole.create(user, role, true)

                }
            }
            List courses = []
            if ("Default".equals(courseBatchesDtl.name)) {
                courses = CourseBatchesDtl.findAllByConductedByAndStatus(courseBatchesDtl.conductedBy, "active")
            } else {
                courses.add(courseBatchesDtl)
            }
            String batchId = ""
            for (int i = 0; i < courses.size(); i++) {
                batchId = "" + courses[i].id
                wsLibraryCacheService.getInstituteBooksPagination(batchId)
            }

            dataProviderService.getLibraryBooks(institutionId,siteId)
        }

        def json = ["status":"success",institutionId:institutionId]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def libAdmin(){
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        String sql="";
        boolean wsSite=false, libWonder=false,accessCode=false,sageSite=false
        if(sm.sageOnly=="true")sageSite=true
        if(sm.id.intValue()==1) wsSite = true
        else if(sm.id.intValue()==25) libWonder=true
        boolean isDefaultBookSetter = false
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)

        if(user.authorities.any {
            it.authority == "ROLE_DEFAULT_BOOKS_SETTER"
        }) {
            isDefaultBookSetter = true
        }

        if(session["userdetails"].publisherId != null && (!sageSite)){
            sql ="select im.id,im.name,im.ip_restricted,cbd.end_date,cbd.id batchId,im.default_books_template_institute from wsuser.institute_mst im, wsuser.course_batches_dtl cbd where im.site_id="+sm.id +
                    " and im.publisher_id='"+session["userdetails"].publisherId+"'"+
                    " and cbd.conducted_by=im.id and cbd.name='Default' order by im.name";
        }else if(session["userdetails"].publisherId == null && (sm.id.intValue()==1 ||sm.instituteLibrary=="true")){
            sql ="select im.id,im.name,im.ip_restricted,cbd.end_date,cbd.id batchId,im.default_books_template_institute from wsuser.institute_mst im, wsuser.course_batches_dtl cbd where im.site_id="+sm.id +
                    "  and cbd.name='Default'"+
                    " and cbd.conducted_by=im.id order by im.name";
        }else {
            sql ="select im.id,im.name,im.ip_restricted,cbd.end_date,cbd.id batchId,im.default_books_template_institute from wsuser.institute_mst im, wsuser.course_batches_dtl cbd where im.site_id="+sm.id +
                    " and cbd.conducted_by=im.id order by im.name";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List institutes = results.collect { institute ->
         //   if("true".equals(institute.defaultBooksTemplateInstitute))
                return [id: institute[0], name: institute[1],ipRestricted: institute[2],endDate:institute[3],batchId:institute[4],defaultBooksTemplateInstitute:institute[5]]
        }

        // logic to remove default institutes for regular admins
        for (Iterator<String> iter = institutes.listIterator(); iter.hasNext();) {
            def institute = iter.next();
            if ("true".equals(institute.defaultBooksTemplateInstitute)&&!isDefaultBookSetter) {
                iter.remove()
            }
        }

        if(sm.id==1 || sm.id==24 || sm.instituteLibrary=="true"||"true".equals(""+session["commonWhiteLabel"])) accessCode = true


        [institutes: institutes,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)),publisherId:session["userdetails"].publisherId,
         wsSite:wsSite,libWonder:libWonder,accessCode:accessCode,sageSite:sageSite,levelsMstList: LevelsMst.listOrderByName(),isDefaultBookSetter:isDefaultBookSetter]
    }


    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def downloadInstituteDetails(){
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        String sql="";
        boolean sageSite=false
        if(sm.sageOnly=="true")sageSite=true
        if(session["userdetails"].publisherId != null  && (!sageSite)) {
           sql = "select im.id,im.name,im.contact_name,im.contact_email,im.ip_restricted,cbd.end_date endDate,cbd.start_date startDate,cbd.id batchId,im.secret_key secretKey from wsuser.institute_mst im, wsuser.course_batches_dtl cbd where im.site_id=" + siteId +
                   " and im.publisher_id='"+session["userdetails"].publisherId+"'"+
                    " and cbd.conducted_by=im.id order by im.name";
        }else{
           sql = "select im.id,im.name,im.contact_name,im.contact_email,im.ip_restricted,cbd.end_date endDate,cbd.start_date startDate,cbd.id batchId,im.secret_key secretKey from wsuser.institute_mst im, wsuser.course_batches_dtl cbd where im.site_id=" + siteId +
                    " and cbd.conducted_by=im.id order by im.name";
        }
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        String startDate=""
        String endDate=""
        String noOfUsers=""
        String noOfBooks = ""
        CourseBatchesDtl courseBatchesDtl
        List institutes = results.collect { institute ->
             startDate=""
             endDate=""
            noOfUsers=""
            noOfBooks = ""
              courseBatchesDtl  = dataProviderService.getDefaultCourseBatchesDtl(institute.id)
            if(courseBatchesDtl!=null) {
                sql = " select count(*) from wsuser.books_batch_dtl bbd " +
                        " where bbd.batch_id =" + courseBatchesDtl.id
                println(sql)
                sql1 = new Sql(dataSource)
                results = sql1.rows(sql);
                if (results.size() > 0) noOfBooks = "" + results[0][0]

                println(sql)
                sql = " select count(*) from wsuser.batch_user_dtl bud" +
                        " where bud.batch_id =" + courseBatchesDtl.id

                sql1 = new Sql(dataSource)
                results = sql1.rows(sql);
                if (results.size() > 0) noOfUsers = "" + results[0][0]

            }

            if(institute.endDate != null && !institute.endDate.equals("")) endDate = (new SimpleDateFormat("dd-MM-yyyy")).format(institute.endDate)
            if(institute.startDate != null && !institute.startDate.equals("")) startDate = (new SimpleDateFormat("dd-MM-yyyy")).format(institute.startDate)

            return [id: institute[0], name: institute[1],contactName: institute[2],contactEmail: institute[3], ipRestricted: institute[4],endDate:endDate,startDate: startDate,
                    secretKey:institute.secretKey,noOfBooks:noOfBooks,noOfUsers:noOfUsers]
        }

        List headers
        List withProperties
        headers = [(sm.id==24?"Organization Name":"Institute Name"),(sm.id==24?"Contact person name":"Librarian Name"),(sm.id==24?"Contact person email id":"Librarian Email"),"IP Restriction","Subscription Start Date","Subscription End Date","Secret Key","Books","Users"]
        withProperties = ["name", "contactName","contactEmail","ipRestricted","startDate","endDate","secretKey","noOfBooks","noOfUsers"]
        def fileName = ""+(sm.id==24?"Organization_Details_":"Institute_Details_")  + (new Random()).nextInt(9999999) + ".xlsx";

        new WebXlsxExporter().with {
            setResponseHeaders(response, fileName)
            fillHeader(headers)
            add(institutes, withProperties)
            save(response.outputStream)
        }
    }




    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def getIPAddresses(){
        Long siteId = session["siteId"]
        List ipAddresses = InstituteIpAddress.findAllByInstitute_idAndSiteId(new Long(params.instituteId),siteId)
        List  ipAddresses1 =null
        if(ipAddresses.size()>0) {
            ipAddresses1 = ipAddresses.collect { ip ->
                return [name: ip.name, ipAddress:ip.ipAddress ,id:ip.id]
            }
        }
        if("true".equals(params.download)) {
            List headers
            List withProperties
            headers = ["Location", "Ip Address"]
            withProperties = ["name", "ipAddress"]
            def fileName = "Institute_ipaddress_"  + (new Random()).nextInt(9999999) + ".xlsx";

            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(ipAddresses1, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status:ipAddresses.size()>0?"OK":"",   ipAddresses: ipAddresses1]
            render json as JSON
        }


    }


    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addIP(){
        Long siteId = session["siteId"]
        String[] ipAddresses = params.ipAddress.split(",")
        for(int i=0;i<ipAddresses.length;i++) {
            if(ipAddresses[i]==null||(ipAddresses[i].trim().length()==0)) {
                continue
            }
            InstituteIpAddress instituteIpAddress = new InstituteIpAddress(ipAddress: ipAddresses[i], institute_id: new Long(params.instituteId), name: params.locationName,siteId: siteId)
            instituteIpAddress.save(failOnError: true, flush: true)
        }

        def json = ["status":"OK"]
        render json as JSON
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_PUBLISHER']) @Transactional
    def addBooks(){
        def bookIdMethod=params.bookIdMethod;
        boolean userAdded=false;
        BooksMst booksMst
        String booksAdded="",booksNotAdded="",booksReAdded=""
        
        Long siteId = session["siteId"]
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        def defaultBatchId = null
        if(params.instituteId!=null){
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndName(new Long(params.instituteId),"Default")
            defaultBatchId = courseBatchesDtl.id
            String[] books = params.bookIds.split(",")
            SiteDtl siteDtl = dataProviderService.getSiteDtl(siteId)
            for(int i=0;i<books.length;i++) {
                if(books[i]==null||(books[i].trim().length()==0)) {
                    continue
                };
                if((siteId.intValue()==1||siteId.intValue()==25||(siteDtl!=null&&"true".equals(siteDtl.canAddBooksFromAllSites))) && (params.publisherId==null||"".equals(params.publisherId))) {
                    if ("true".equals(bookIdMethod)) booksMst = BooksMst.findById(new Long(books[i]))
                    else booksMst = BooksMst.findByIsbn(books[i])
                }
                else if((siteId.intValue()==1||siteId.intValue()==25) && params.publisherId!=null && !"".equals(params.publisherId)) {
                    if ("true".equals(bookIdMethod)) booksMst = BooksMst.findByIdAndPublisherId(new Long(books[i]),params.publisherId)
                    else booksMst = BooksMst.findByIsbnAndPublisherId(books[i],params.publisherId)
                }else{
                    if ("true".equals(bookIdMethod)) booksMst = BooksMst.findByIdAndSiteId(new Long(books[i]), siteId)
                    else booksMst = BooksMst.findByIsbnAndSiteId(books[i], siteId)
                }

                boolean canAdd = true
                InstituteMst instituteMst = InstituteMst.findById(courseBatchesDtl.conductedBy)
                //checks to see whether this book can be added
                //1. Wonderslate admin cannot add instituteCreated books
                if(booksMst!=null&&instituteMst!=null&&session["userdetails"]!=null&&session["userdetails"].publisherId==null){
                    //Wonderslate admin
                    if(booksMst.publisherId!=null&&instituteMst.publisherId!=null&&booksMst.publisherId.longValue()==instituteMst.publisherId.longValue()){
                        canAdd = false
                    }
                }
                else if(booksMst!=null&&session["userdetails"]!=null&&session["userdetails"].publisherId!=null){
                    //publisher user trying to add some other publisher's book
                    if(booksMst.publisherId!=null&&session["userdetails"].publisherId!=null&&booksMst.publisherId.longValue()!=session["userdetails"].publisherId.longValue()){
                        canAdd = false
                    }
                }

                if (booksMst != null&&canAdd) {
                    BooksBatchDtl booksBatchDtl = BooksBatchDtl.findByBatchIdAndBookId(courseBatchesDtl.id,booksMst.id)
                    if (booksBatchDtl == null) {
                        booksBatchDtl = new BooksBatchDtl(batchId:  courseBatchesDtl.id, bookId:  booksMst.id)
                        booksBatchDtl.save(failOnError: true, flush: true)
                        if(siteId.intValue()==1 || siteMst.instituteLibrary=="true") {
                            redisService.("userMyLibraryInstituteBooks_" + courseBatchesDtl.id) = null
                            wsLibraryCacheService.getInstituteBooksPagination(courseBatchesDtl.id)
                            libraryBooksService.getInstituteBooksPagination(courseBatchesDtl.id)
                            libraryBooksService.getInstituteBooksTags(courseBatchesDtl.id)
                            wsLibraryCacheService.getInstituteBooksPaginationNew(courseBatchesDtl.id)
                        }
                        booksAdded += " " + booksMst.title + ","
                    }else{
                        booksReAdded+=" "+booksMst.title+","
                    }
                }else{
                   booksNotAdded +=books[i]+","
                }
            }
            dataProviderService.booksInBatch(courseBatchesDtl.id)
            dataProviderService.getLibraryBooks(params.instituteId,siteId)
            dataProviderService.getBooksTagListForInstitute(params.instituteId);
            //resetting the library of users
            List users = BatchUserDtl.findAllByBatchId(new Long(courseBatchesDtl.id))

            users.each {user ->
                redisService.(user.username+"_"+"booksList")=null;
            }
            if(siteId.intValue()==1 || siteMst.instituteLibrary=="true")redisService.("userMyLibraryInstituteBooks_"+courseBatchesDtl.id) = null
        }
        
        def json = [
                status:  booksNotAdded.length() == 0?"OK":"",booksNotAdded:booksNotAdded,booksReAdded:booksReAdded, defaultBatchId:defaultBatchId,bookIds:params.bookIds
        ]
        render json as JSON
    }







    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def removeIPAddress(){
        if(params.ipIds!=null){
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            new Sql(dataSource).execute("DELETE from wsuser.institute_ip_address WHERE id IN ("+params.ipIds+")")
        }
        def json = ["status":"OK"]
        render json as JSON

    }


    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def addIPAddressRange(){
        Long siteId = session["siteId"]
        String ipAddressFrom=params.ipAddressFrom
        String ipAddressTo = params.ipAddressTo
        int from = Integer.parseInt(ipAddressFrom.substring(ipAddressFrom.lastIndexOf('.')+1))
        int to = Integer.parseInt(ipAddressTo.substring(ipAddressFrom.lastIndexOf('.')+1))
        def constantPart = ipAddressFrom.substring(0,ipAddressFrom.lastIndexOf('.')+1)
        for(int i=from;i<=to;i++) {
            InstituteIpAddress instituteIpAddress = new InstituteIpAddress(ipAddress: constantPart+i, institute_id: new Long(params.instituteId), name: params.locationName,siteId: siteId)
            instituteIpAddress.save(failOnError: true, flush: true)
        }
        def json = ["status":"OK"]
        render json as JSON
    }


    def downloadbooksreportData(){
        String sql = " select bm.title,u.name,coalesce(cbd.end_date,' ')" +
                " from books_mst bm, wsuser.books_batch_dtl bbd,wsuser.batch_user_dtl bud, wsuser.user u,wsuser.course_batches_dtl cbd " +
                " where bm.id=bbd.book_id" +
                " and bbd.batch_id=bud.batch_id and u.username=bud.username and bbd.batch_id="+params.batchId+" and cbd.id=bbd.batch_id group by bm.title,u.name";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        if (results != null && results.size() > 0) {
            List data= results.collect { comp ->
                return [title: comp[0], username: comp[1], date: comp[2]
                ]
            }
            List headers = ["Title","UserName", "End Date"]
            List withProperties = ["title", "username", "date"]
            def fileName = "Book_Data_"+
                    (params.batchId!=""?params.batchId+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"
            new WebXlsxExporter(grailsApplication.config.grails.basedir.path+'/upload/Books_Data_Template.xlsx').with {
                setResponseHeaders(response, fileName)
                    fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
        }
        
    }
    def isbnKeyword(){

    }

    @Transactional
    def getIsbn(){
        List isbn;
        def status
        isbn = BooksMst.findAllByIsbn(params.isbn)
        if(isbn!=null && isbn.size()>0){
            status="success"
        }else{
            status="No"
        }
        def json = ["status":status]
        render json as JSON
    }

    @Transactional
    def updateKeywordtoIsbn(){
        String keywordStr = ""
        List<IsbnKeyword> isbn = IsbnKeyword.findAllByIsbn(params.isbn)
        if(isbn.size()>0) {
            for (int i = 0; i < isbn.size(); i++) {
                keywordStr = keywordStr + isbn.get(i).keyword + ","
            }
            keywordStr = keywordStr.substring(0, keywordStr.length() - 1);
        }
        def json = [status:isbn.size()>0?"OK":"",   isbn: keywordStr]
        render json as JSON
    }

    @Transactional
    def addKeywordtoIsbn() {
        def valid="error";
        if (!"".equals(params.isbn)) {
            List<IsbnKeyword> isbnKeyword1 = IsbnKeyword.findAllByIsbn(params.isbn)
            for(int i=0;i < isbnKeyword1.size();i++) {
                isbnKeyword1.get(i).delete(flush: true)
            }
        String[] keyword = params.keyword.split(",")
        for (int i = 0; i < keyword.length; i++) {

            if (keyword[i] == null || (keyword[i].trim().length() == 0)) {
                continue
            }
            IsbnKeyword isbnKeyword = new IsbnKeyword(isbn: params.isbn, keyword: keyword[i])
            isbnKeyword.save(failOnError: true, flush: true)
            valid="success"
        }
    }

        Integer siteId = getSiteId(request)
        String siteIdList=siteId.toString();

        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                dataProviderService.getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)
        }
        dataProviderService.updateSearchMap(siteId,siteIdList);

        def json = ["status": "OK",valid:valid]
        render json as JSON
    }
    
    def  Integer getSiteId(request){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(params.siteId!=null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    @Secured ('ROLE_MASTER_LIBRARY_ADMIN')
    def usageReport(){
        [showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }

    @Secured ('ROLE_MASTER_LIBRARY_ADMIN')
    def usageReportDoris(){
        [hideBanner:true,disciplines:getDisciplines()]
    }

    @Transactional
    def getDisciplines(){
        def sql="SELECT discipline,count(id) FROM books_mst where site_id=9 and discipline is not null and status='published' group by discipline";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);


        List discipline = results.collect{ temp ->
            return [discipline:temp[0],noOfBooks:temp[1]];
        }

        return discipline;
    }

    def getInstituteNames(){
        String sql ="select im.id,im.name,im.ip_restricted from wsuser.institute_mst im where im.site_id="+session["siteId"] +
                "  order by im.name";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List instituteList = results.collect { institute ->
            return [ name: institute.name,id:institute.id ]
        }
        def json = [status:instituteList?"OK":"Not present" ,instituteList: instituteList]
        render json as JSON
    }


    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def registeredUserReportData(){
        Integer siteId = getSiteId(request);
        String sql = "select name,COALESCE(email,''),COALESCE(mobile,''),(case when user_type is null then 'student' else user_type end) user_type,\n" +
                "DATE_FORMAT(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE),'%d-%m-%Y') registered_on,COALESCE(department,''),COALESCE(institution,'')," +
                " COALESCE(interests,''),COALESCE(country,''),COALESCE(state,''),COALESCE(district,''),COALESCE(country,''),COALESCE(ip_address,''),DATE_FORMAT(DATE_ADD(tc_accepted_date, INTERVAL '5:30' HOUR_MINUTE),'%d-%m-%Y') tcAcceptedDate" +
                " from wsuser.user  where site_id="+siteId+"" +
                " AND DATE(DATE_ADD(date_created," +
                " INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.startDate+"','%d-%m-%Y')" +
                " AND DATE(DATE_ADD(date_created," +
                " INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.endDate+"','%d-%m-%Y') order by date_created; "
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List usagereport = results.collect{ report ->
            return [name:report[0],email:report[1], mobile: report[2],userType: report[3],registeredon:report[4],department:report[5],institutions:report[6],interests:report[7],country:report[8],state:report[9],district:report[10],ipAddress:report[11],tcAcceptedDate:report[12]?report[12]:"Pending"]
        }
        if("true".equals(params.download)) {
            List headers
            List withProperties
            if(siteId==9) {
                headers = ["Name", "Email", "Mobile", "User Type","Department","Institute","Area of interests","Country","Registered on"]
                withProperties = ["name", "email", "mobile", "userType","department","institutions","interests","country","registeredon"]
            }else if(siteId==12 ||siteId==23){
                headers = ["name", "email", "mobile", "State","Registered on","T&C Date of Acceptence"]
                withProperties = ["name", "email", "mobile","state", "registeredon","tcAcceptedDate"]
            }else if(siteId==24){
                headers = ["name", "email", "mobile", "State","Registered on"]
                withProperties = ["name", "email", "mobile","state", "registeredon"]
            }

            def fileName = "Data_" + (params.startDate != "" ? params.startDate + "_" : "FromAny_") +
                    (params.endDate != "" ? params.endDate + "_" : "ToAny_") + (new Random()).nextInt(9999999) + ".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(usagereport, withProperties)
                save(response.outputStream)
            }
        }else {

            def json = [
                    'results': usagereport,
                    'status' : usagereport ? "OK" : "Nothing present",
            ]

            render json as JSON

        }
    }

    @Secured ('ROLE_MASTER_LIBRARY_ADMIN') @Transactional
    def usageReportBooksView() {
        Integer siteId = getSiteId(request);
        def var="";
        String sql = "select bvd.book_id,bvd.username,bvd.date_created from wslog.books_view_dtl bvd where bvd.site_id="+siteId+""+
                "    AND DATE(DATE_ADD(bvd.date_created,\n" +
                "        INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.startDate+"','%d-%m-%Y')\n" +
                "    AND DATE(DATE_ADD(bvd.date_created,\n" +
                "        INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.endDate+"','%d-%m-%Y')" ;
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List usage = results.collect { report ->
            if(report[0]!=null && report[1]!=null) {
                var += "select "+report[0]+" book_id, '"+report[1]+"' username ,'"+report[2]+"' date_created  from dual  union ";
            }
        }
if(var!="") {
    def queryappend = var.substring(0, var.length() - 6);
    String sql2 = "select bm.id,isbn,title,(case when um.user_type is null then 'student' else um.user_type end) user_type,count(bm.id) no_of_views\n" +
            " from wsuser.books_mst bm , wsuser.user um,(" + queryappend + ")x where " +
            " bm.site_id="+siteId+" and " +
            " bm.id=x.book_id and um.username=x.username GROUP BY bm.id , (CASE WHEN um.user_type IS NULL THEN 'student' ELSE um.user_type END)";
    def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsuser')
    def sql3 = new Sql(dataSource1)
    def results2 = sql3.rows(sql2)
    List usagereport1 = results2.collect { report1 ->
        return [id: report1[0], isbn: report1[1], title: report1[2], userType: report1[3], views: report1[4]]
    }
        def json = [status: usagereport1 ? "OK" : "Not present", userList: usagereport1]
        render json as JSON
}
        def json = [status:"Not present"]
        render json as JSON
        }



    @Secured ('ROLE_MASTER_LIBRARY_ADMIN') @Transactional
    def downloadusageReportBooksView() {
        Integer siteId = getSiteId(request);
        def var = "";
        String sql = "select bvd.book_id,bvd.username,bvd.date_created from wslog.books_view_dtl bvd where bvd.site_id=" + siteId + "" +
                "    AND DATE(DATE_ADD(bvd.date_created,\n" +
                "        INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y')\n" +
                "    AND DATE(DATE_ADD(bvd.date_created,\n" +
                "        INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y')";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List usage = results.collect { report ->
            if (report[0] != null && report[1] != null) {
                var += "select " + report[0] + " book_id, '" + report[1] + "' username ,'" + report[2] + "' date_created  from dual  union ";
            }
        }
        if (var != "") {
            def queryappend = var.substring(0, var.length() - 6);
            String sql2 = "select bm.id,isbn,title,(case when um.user_type is null then 'student' else um.user_type end) user_type,count(bm.id) no_of_views\n" +
                    " from wsuser.books_mst bm , wsuser.user um,(" + queryappend + ")x where " +
                    " bm.site_id=" + siteId + "  " +
                    "  and bm.id=x.book_id and um.username=x.username GROUP BY bm.id , (CASE WHEN um.user_type IS NULL THEN 'student' ELSE um.user_type END)";
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql2)
            List usagereport1 = results2.collect { report1 ->
                return [id: report1[0], isbn: report1[1], title: report1[2], userType: report1[3], views: report1[4]]
            }

            List headers
            List withProperties
            if (siteId == 9) {
                headers = ["Id", "Isbn", "Title", "userType", "No. of views"]
                withProperties = ["id", "isbn", "title", "userType", "views"]
            } else {
                headers = ["Id", "Isbn", "Title", "No. of views"]
                withProperties = ["id", "isbn", "title", "views"]
            }

            def fileName = "Data_" + (params.startDate != "" ? params.startDate + "_" : "FromAny_") +
                    (params.endDate != "" ? params.endDate + "_" : "ToAny_") + (new Random()).nextInt(9999999) + ".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(usagereport1, withProperties)
                save(response.outputStream)
            }

        }
    }

    @Secured ('ROLE_MASTER_LIBRARY_ADMIN') @Transactional
    def usageReportResourceView() {
        Integer siteId = getSiteId(request);
        def var="";
        String sql = "select rv.id,rv.resource_dtl_id,rv.date_created from wslog.resource_view rv where rv.site_id="+siteId+""+
                "    AND DATE(rv.date_created\n" +
                "        ) >= STR_TO_DATE('"+params.startDate+"','%d-%m-%Y')\n" +
                "    AND DATE(rv.date_created\n" +
                "        ) <= STR_TO_DATE('"+params.endDate+"','%d-%m-%Y')" ;
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List usage = results.collect { report ->
            if(report[0]!=null && report[1]!=null) {
                var += "select "+report[1]+" resource_dtl_id, '"+report[2]+"' date_created  from dual  union ";
            }
        }
        if(var!="") {
            def queryappend = var.substring(0, var.length() - 6);
            String sql2 = "select rd.id,rd.res_type,count(rd.id) no_of_views,rd.chapter_id,cm.name,cm.book_id,bm.title" +
                    " from resource_dtl rd ,chapters_mst cm,books_mst bm, (" + queryappend + ")x where 1=1 " +
                    " and rd.id=x.resource_dtl_id  AND cm.id=rd.chapter_id AND bm.id=cm.book_id GROUP BY rd.id,rd.res_type;"
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql2)
            List usagereport1 = results2.collect { report1 ->
                def resType=report1[1];
                if(resType=="QA"){
                    resType="Long Answer"
                }else if(resType=="Short QA"){
                    resType="Short Answer"
                }else if(resType=="KeyValues"){
                    resType="Flash Cards"
                }else{
                    resType= report1[1];
                }
                return [restype: resType, views: report1[2],chapterName: report1[4],bookTitle: report1[6]]
            }
                def json = [status: usagereport1 ? "OK" : "Not present", report: usagereport1]
                render json as JSON
        }
        def json = [status:"Not present"]
        render json as JSON
    }



 @Secured ('ROLE_MASTER_LIBRARY_ADMIN') @Transactional
    def usageReportInstructorResourceView(){
        Integer siteId = getSiteId(request);

        String sql = "select count(rv.instructor_res_id),rv.instructor_res_id from wslog.instructor_resource_view rv where rv.site_id="+siteId+""+
                "    AND DATE(rv.date_created\n" +
                "        ) >= STR_TO_DATE('"+params.startDate+"','%d-%m-%Y')\n" +
                "    AND DATE(rv.date_created\n" +
                "        ) <= STR_TO_DATE('"+params.endDate+"','%d-%m-%Y') group by rv.instructor_res_id" ;
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List resreport = results.collect{sale ->

            String sql2 = "select ir.id,ir.book_id,bm.title,ir.sub_tab" +
                    " from instructor_resources ir,books_mst bm where ir.id="+sale[1] +
                    " and ir.book_id=bm.id;"
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql2)
            List usagereport1 = results2.collect { report1 ->

                return [views: sale[0],bookTitle: report1[2],subtab:report1[3]]
            }

        }
        def json = [status: resreport ? "OK" : "Not present", report: resreport ]
        render json as JSON
    }

    @Secured ('ROLE_MASTER_LIBRARY_ADMIN') @Transactional
    def downloadusageReportInstructorResourceView() {
        Integer siteId = getSiteId(request);

        String sql = "select count(rv.instructor_res_id),rv.instructor_res_id from wslog.instructor_resource_view rv where rv.site_id=" + siteId + "" +
                "    AND DATE(rv.date_created\n" +
                "        ) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y')\n" +
                "    AND DATE(rv.date_created\n" +
                "        ) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y') group by rv.instructor_res_id";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List resreport = results.collect { sale ->

            String sql2 = "select ir.id,ir.book_id,bm.title,ir.sub_tab" +
                    " from instructor_resources ir,books_mst bm where ir.id=" + sale[1] +
                    " and ir.book_id=bm.id;"
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql2)
            List usagereport1 = results2.collect { report1 ->
                def resType=report1[3];
                if(resType=="teachingSlides"){
                    resType="Teaching Slides"
                }else if(resType=="teachingNotes"){
                    resType="Teaching Notes"
                }else if(resType=="sampleChapters"){
                    resType="Sample Chapters"
                }else if(resType=="mediaLinks"){
                    resType="Media Links"
                }else if(resType=="exercises"){
                    resType="Exercises"
                }else{
                    resType= report1[3];
                }

                return [views: sale[0], bookTitle: report1[2], subtab: resType]
            }

        }
        List headers = ["Book Title", "Resource Type","No. of views"]
        List withProperties = ["bookTitle", "subtab","views"]

        def fileName = "Data_" + (params.startDate != "" ? params.startDate + "_" : "FromAny_") +
                (params.endDate != "" ? params.endDate + "_" : "ToAny_") + (new Random()).nextInt(9999999) + ".xlsx"
        new WebXlsxExporter().with {
            setResponseHeaders(response, fileName)
            fillHeader(headers)
            add(resreport, withProperties)
            save(response.outputStream)
        }
    }


    @Secured ('ROLE_MASTER_LIBRARY_ADMIN') @Transactional
    def downloadusageReportResourceView() {
        Integer siteId = getSiteId(request);
        def var="";
        String sql = "select rv.id,rv.resource_dtl_id,rv.date_created from wslog.resource_view rv where rv.site_id="+siteId+""+
                "    AND DATE(rv.date_created\n" +
                "        ) >= STR_TO_DATE('"+params.startDate+"','%d-%m-%Y')\n" +
                "    AND DATE(rv.date_created\n" +
                "        ) <= STR_TO_DATE('"+params.endDate+"','%d-%m-%Y')" ;
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List usage = results.collect { report ->
            if(report[0]!=null && report[1]!=null) {
                var += "select "+report[1]+" resource_dtl_id, '"+report[2]+"' date_created  from dual  union ";
            }
        }
        if(var!="") {
            def queryappend = var.substring(0, var.length() - 6);
            String sql2 = "select rd.id,rd.res_type,count(rd.id) no_of_views,rd.chapter_id,cm.name,cm.book_id,bm.title" +
                    " from resource_dtl rd ,chapters_mst cm,books_mst bm, (" + queryappend + ")x where 1=1 " +
                    " and rd.id=x.resource_dtl_id  AND cm.id=rd.chapter_id AND bm.id=cm.book_id GROUP BY rd.id,rd.res_type;"
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql2)
            List usagereport1 = results2.collect { report1 ->
                def resType=report1[1];
                if(resType=="QA"){
                    resType="Long Answer"
                }else if(resType=="Short QA"){
                    resType="Short Answer"
                }else if(resType=="KeyValues"){
                    resType="Flash Cards"
                }else{
                    resType= report1[1];
                }
                return [restype: resType, views: report1[2],chapterName: report1[4],bookTitle: report1[6]]
            }
                List headers
                List withProperties
                headers = ["Book Title","Chapter Name","Resource Type",  "No. of views"]
                withProperties = ["bookTitle","chapterName","restype", "views"]
                def fileName = "Data_" + (params.startDate != "" ? params.startDate + "_" : "FromAny_") +
                        (params.endDate != "" ? params.endDate + "_" : "ToAny_") + (new Random()).nextInt(9999999) + ".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response, fileName)
                    fillHeader(headers)
                    add(usagereport1, withProperties)
                    save(response.outputStream)
                }
        }
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def usageReportData(){
        Integer siteId = getSiteId(request);
        def var="";
        String sql = "select bvd.id,bvd.institute_id,bvd.book_id,bvd.date_created " +
                " from wslog.books_view_dtl bvd " +
                " where bvd.site_id="+siteId+" AND bvd.institute_id IN("+params.instituteids+")" +
                " AND DATE(DATE_ADD(bvd.date_created," +
                "INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y')\n" +
                "AND DATE(DATE_ADD(bvd.date_created," +
                "INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y')";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results1 = sql1.rows(sql)
        List usagereport = results1.collect { report ->
                var += "select "+report[2]+" book_id, '"+report[1]+"' institute_id ,'"+report[3]+"' date_created  from dual  union ";
        }
        if(var!="") {
            def queryappend = var.substring(0, var.length() - 6);
            String sql2 = "select im.name,bm.id,COALESCE(bm.isbn,''),bm.title,count(bm.id) no_of_views" +
                    " from wsuser.books_mst bm , wsuser.institute_mst im,(" + queryappend + ")x where " +
                    "   bm.site_id ="+siteId+"\n" +
                    "    AND bm.id =x.book_id" +
                    "    and im.id =x.institute_id"+
                    " group by im.name,bm.id ";
            def dataSource2 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql3 = new Sql(dataSource2)
            def results2 = sql3.rows(sql2);
            List usagereport2 = results2.collect { report1 ->
                return [institutename: report1[0], bookId: report1[1], isbn: report1[2], title: report1[3], noofviews: report1[4]]
            }
            if ("true".equals(params.download)) {
                List headers = ["name", "Bookid", "Isbn", "Title", "No. of views" ]
                List withProperties = ["institutename", "bookId", "isbn", "title", "noofviews"]

                def fileName = "Data_"+(params.startDate!=""?params.startDate+"_":"FromAny_")+
                        (params.endDate!=""?params.endDate+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response,fileName)
                    fillHeader(headers)
                    add(usagereport2, withProperties)
                    save(response.outputStream)
                }
            } else {
                def json = [
                        'results': usagereport2,
                        'status' : usagereport2 ? "OK" : "Nothing present",
                ]
                render json as JSON
            }
        }else {
            def json = [status: "Not present"]
            render json as JSON
        }
    }


    def userLoginLimit() {

    }

    @Transactional
    def getUsers(){
        User users;
        users=User.findByUsernameAndSiteId(params.userValue, 12)

        if(users!=null) {
            users = users.each { user ->
                return [name: user.name, email: user.email, mobile: user.mobile, id: user.id, username: user.username, old: user.state]
            }
        }

        def json = [status:users?"OK":"Not present" ,userList: users]
        render json as JSON
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) 
    def registeredUserReport(){

    }

    @Secured(['ROLE_BOOK_CREATOR','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def getAllBooks(){
        Integer siteId = getSiteId(request)
       //cache is not required for this. As this will have both and unpublished books and its very expensive to maintain cache for this.
        List booksList = dataProviderService.getAllBooks(siteId,params.publisherId,params.level,params.syllabus,params.grade,params.subject);
        def json = [books : booksList,status: "OK"]
        render json as JSON
    }


    @Secured(['ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def userManagement(){
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        boolean sageSite=false
        if(sm.sageOnly=="true")sageSite=true
        def instituteName;
        Integer noOfUsers;
        def instituteId;
        def batchId;
        def ipRestricted
        String userType="Users"
        String pageTitle="";

        def manageUsers=false,manageBooks=false,manageWaitingList=false


        if(params.userType!=null&&session["siteId"].intValue()==1) userType=params.userType
        String sql =""
        if(params.instituteId!=null) {
            sql = "select im.id,im.name,cbd.id batchId,im.no_of_users,im.ip_restricted from wsuser.institute_mst im,wsuser.course_batches_dtl cbd  " +
                    "where im.site_id=" + session["siteId"] +
                    " and cbd.conducted_by=im.id  and cbd.name='default' and im.id="+params.instituteId;
        }else{
            sql = "select im.id,im.name,cbd.id batchId,im.no_of_users,im.ip_restricted from wsuser.institute_mst im, wsuser.institute_user_dtl iud,wsuser.course_batches_dtl cbd  " +
                    "where im.site_id=" + session["siteId"] +
                    " and cbd.conducted_by=im.id and iud.username='" + springSecurityService.currentUser.username + "' and iud.institute_id=im.id and cbd.name='default'";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List institutes = results.collect { institute ->
            instituteName=institute[1]+"";
            instituteId=institute[0];
            batchId=institute[2];
            noOfUsers=institute[3];
            ipRestricted=institute[4]
        }
        pageTitle = instituteName;
        if("users".equals(params.accessMode)) {
            manageUsers=true
            pageTitle = "Manage users"
            if("Instructors".equals(params.userType)) pageTitle = "Manage Instructors"
        }
        else if("books".equals(params.accessMode)) {
            manageBooks=true
            pageTitle = "Manage Books"
        }
        else if("waitingList".equals(params.accessMode)) {
            manageWaitingList=true
            pageTitle = "Manage Waiting List"
        }
        if("Instructors".equals(userType)&&session["siteId"].intValue()==1) ipRestricted = false
        [institutes: institutes,instituteName:instituteName,batchId:batchId,instituteId:instituteId,
         ipRestricted:ipRestricted,
         showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)), userType: userType,
         manageUsers:manageUsers,manageBooks:manageBooks,manageWaitingList:manageWaitingList,pageTitle:pageTitle,sageSite:sageSite,commonTemplate:"true"]

    }

    @Secured(['ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def showAllUsersForInstitute()
    {
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        String sql = "select u.name,COALESCE(u.email,' '),u.id,COALESCE(u.mobile,' '),u.username,COALESCE(u.max_logins,' '), bud.admission_no,bud.validity_date from wsuser.user u,wsuser.batch_user_dtl bud" +
                " where bud.batch_id="+params.batchId+" and u.username=bud.username"
        String optionalSql="";
        if("instructor".equals(params.userType)) optionalSql =" and bud.instructor='true'"
        else optionalSql =" and bud.instructor is null"
        if(params.mobile!=null&&!"".equals(params.mobile)) optionalSql += " and u.mobile='"+params.mobile+"'"
        sql +=optionalSql
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List users = results.collect { user ->
            return [name: user[0], email: user[1],userId: user[2],mobile: user[3],username: user[4],maxlogins: user[5],admissionNo: user[6],
            validityDate:user.validity_date!=null?(new SimpleDateFormat("dd-MM-yyyy")).format(user.validity_date):""]
        }
        def json = [status: users ? "OK" : "not found", users: users, userType: params.userType, batchId: params.batchId, download: (sm.sageOnly!="true"), delete: (sm.sageOnly!="true")]
        render json as JSON

    }



    @Secured(['ROLE_LIBRARY_ADMIN']) @Transactional
    def addUserToInstitute(){
        def status="";
        boolean userAdded=false;
        User user
        String usersAdded="",usersAddedNotRegistered="";
        String userType="";
        Integer siteId = getSiteId(request)
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        boolean showErrorMessage = false
        showErrorMessage=(sm.sageOnly!="true")
        if(params.batchId!=null&&params.useremail!=null){
            String[] users = params.useremail.split(",")
            for(int i=0;i<users.length;i++) {
                if(users[i]==null||(users[i].trim().length()==0)) {
                    continue
                };
                if(users[i].indexOf('@')>-1)
                    user = User.findByEmailAndSiteId(users[i].trim().toLowerCase(),siteId)
                else
                    user = User.findByMobileAndSiteId(users[i].trim().toLowerCase(),siteId)
                if (user != null) {
                    CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))
                    if (courseBatchesDtl != null) {
                        //check if the user is already added
                        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user.username, new Long(params.batchId))
                        if (batchUserDtl == null) {

                            batchUserDtl = new BatchUserDtl(username: user.username, batchId: new Long(params.batchId),
                                    createdBy: springSecurityService.currentUser.username,instructor: "instructor".equals(params.userType)?"true":null)
                            batchUserDtl.save(failOnError: true, flush: true)
                            dataProviderService.getBooksListForUser(user.username)
                            dataProviderService.getUserBatchesIds(user.username)
                            if(batchUserDtl.instructor==null){
                                dataProviderService.getUserBatchesAsStudent(user.username);
                            }
                            userAdded = true
                            usersAdded +=" "+user.name+","
                            if(courseBatchesDtl.groupId!=null&&!"Default".equals(courseBatchesDtl.name)){
                                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                                if(groupsMembersDtl==null){
                                    groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "instructor".equals(params.userType)?"admin":"user",
                                            profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                            groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                                    groupsMembersDtl.save(failOnError: true, flush: true)
                                }
                            }

                            //if instructor then add to the institute teachers group
                            if(courseBatchesDtl.groupId!=null&&"Default".equals(courseBatchesDtl.name)&&"instructor".equals(params.userType)){
                                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                                if(groupsMembersDtl==null){
                                    groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "admin",
                                            profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                            groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                                    groupsMembersDtl.save(failOnError: true, flush: true)
                                }
                            }
                        }else{
                            status="user present"
                        }

                    }
                }else{
                    status="no user"
                }
            }
        }

        def json = [
                status: usersAdded.length()>0?usersAdded.substring(0,(usersAdded.length()-1))+" added.":status,showErrorMessage: showErrorMessage
        ]
        render json as JSON

    }



@Transactional
    def updateUserloginlimit(){
    String status="error";
    InstituteMst instituteMst=InstituteMst.findById( new Long(params.instituteId))
    if(instituteMst!=null) {
        instituteMst.usersLoginlimit = Integer.parseInt(params.userloginlimit)
        instituteMst.save(failOnError: true, flush: true)
        status="OK"
    }
    String sql = "select u.name,u.email,u.id,u.mobile,u.username,COALESCE(u.max_logins,' ') from wsuser.user u,wsuser.batch_user_dtl bud" +
                " where bud.batch_id="+params.batchId+" and u.username=bud.username and bud.instructor is null";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List users = results.collect { userlist ->
            User user=User.findByUsername(userlist.username)
            user.maxLogins=Integer.parseInt(params.userloginlimit);
            user.save(failOnError: true, flush: true)
        }
    def json = [
            status: status
    ]
    render json as JSON

    }


    @Transactional
   def  getInstituteUsersLoginlimitDetails() {
        def limit;
        InstituteMst instituteMst=InstituteMst.findById( new Long(params.instituteId))
        limit =instituteMst.usersLoginlimit;
        def json = [
                limit: limit
        ]
        render json as JSON

   }

    def getUserStats() {
        def instituteName;
        Integer noOfUsers;
        def instituteId = params.instituteId
        def batchId;
        def sql = "select im.id,im.name,cbd.id batchId, im.no_of_users " +
                "from wsuser.institute_mst im, wsuser.course_batches_dtl cbd" +
                " where im.site_id= " + session["siteId"] + " and cbd.name='Default' and cbd.conducted_by=im.id and cbd.conducted_by = " + instituteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        results.collect { institute ->
            instituteName = institute[1] + "";
            instituteId = institute[0];
            batchId = institute[2];
            noOfUsers = (institute[3]!=null)?institute[3]:-1;
        }
        String sql3 = "select count(u.id) from wsuser.user u,wsuser.batch_user_dtl bud" +
                " where bud.batch_id=" + batchId + " and u.username=bud.username"
        def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql2 = new Sql(dataSource1)
        def results1 = sql2.rows(sql3);
        def count = results1.get(0).values();
        String sql4 = "select count(code) from wsuser.access_code where status is null and institute_id ='"+instituteId+"'"
        def results2 = sql2.rows(sql4);
        def count1 = results2.get(0).values();
        def json = [noOfUsers: noOfUsers, usedlicenses: count[0]+count1[0], pendinglicenses: (noOfUsers - count[0]-count1[0])]
        render json as JSON
    }


    @Transactional
    def generateUserAddByExcel() {

        try {
            String alreadyPresent = "";
            String usersAdded = "";
            String alreadyPresentUsernames = "";
            String usersAddedUsernames = "";
            List usersNotAdded = new ArrayList()
            String fileName = (new Random()).nextInt(9999999)
            String username = "";
            String instituteUrl=""
            String instituteId=params.instituteId
            Integer siteId = getSiteId(request)
            if ("submit".equals(params.mode)) {
                InstituteMst instituteMst = InstituteMst.findById(new Long(instituteId))
                if(instituteMst.urlname!=null && instituteMst.urlname!="") {
                    instituteUrl="https://www.wonderslate.com/inst/${instituteMst.id}"
                }else{
                    instituteUrl="https://www.wonderslate.com"
                }
                def password = params.password
                password = password ? password.replaceAll('~', '&') : "password1"
                final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                MultipartFile file = multiRequest.getFile("file")
                if (!file.empty) {
                   def sheetheader = []
                    def values = []
                    def workbook = new XSSFWorkbook(file.getInputStream())
                    def sheet = workbook.getSheetAt(0)
                    println("file not empty 1")
                    for (cell in sheet.getRow(0).cellIterator()) {
                        println("cell "+cell.stringCellValue)
                        sheetheader << cell.stringCellValue
                    }
                   def headerFlag = true
                    for (row in sheet.rowIterator()) {

                        if (headerFlag) {
                            headerFlag = false
                            continue
                        }
                        def value = ''
                        def map = [:]
                        for (cell in row.cellIterator()) {

                            try {
                                value = cell.stringCellValue
                            }catch (Exception e){
                                value=""+cell.numericCellValue
                            }
                            map["${sheetheader[cell.columnIndex]}"] = value
                        }
                        values.add(map)
                    }
                    def updateBooks = []
                    boolean existingUser
                   values.each { v ->
                        if (params.batchId != null && v."Login Id(Email/Mobile)" != null) {
                            String userCheck = checkUserDetailsForExcel(v, params.batchId,siteId)
                           if(userCheck.equals("OK")){
                                User user;
                                String[] users
                                if(v."Login Id(Email/Mobile)".getClass().getName().equals("".getClass().getName())){
                                    users = v."Login Id(Email/Mobile)".split(",")
                                }else{
                                    users = ((BigInteger) v."Login Id(Email/Mobile)").toString().split(',')
                                }
                                println("users "+users.length)
                                for (int i = 0; i < users.length; i++) {
                                    existingUser = true
                                    username = users[i];
                                    println("username="+username)
                                    user = new User(
                                            username: "" + siteId + "_" +username,
                                            name: (v.Name!=null && v.Name!="")?v.Name:"user",
                                            email: (v.Email!=null && v.Email!="")?(v.Email):((users[i].indexOf('@')>-1)?username:null),
                                            mobile: (v.Mobile!=null && v.Mobile!="")?((BigInteger) new Double(v.Mobile)):(!(users[i].indexOf('@')>-1)?username:null),
                                            password: password,
                                            siteId: siteId,
                                            pincode: (v.Pincode!=null && v.Pincode!=""&&siteId.intValue()==1)?(Integer) new Double(v.Pincode):null,
                                            school: (v."School/College"!=null && v."School/College"!=""&&siteId.intValue()==1)?v."School/College":null
                                    )
                                    //returns false if user exists else saves the user and returns true
                                    existingUser=!userManagementService.saveNewUser(instituteMst,user, siteId, password,request,username,params,instituteUrl)
                                    //convert admission number to string if its not string
                                    if(!v."Admission No.".getClass().getName().equals("".getClass().getName())) v."Admission No." = (String) (BigInteger) v."Admission No."
                                    //admission number already checked in validation hence adding user to a batch
                                    def success = instituteService.addUserToBatch(user.username, new Long(params.batchId),null,"instructor".equals(params.userType) ? "true" : null,v."Admission No.",siteId)
                                    Date validityDate = null
                                    DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
                                    if (v.Validity != null && v.Validity != "") {
                                        validityDate = df.parse(v.Validity)
                                        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user.username,new Long(batchId))
                                        if(batchUserDtl!=null){
                                            batchUserDtl.validityDate = validityDate
                                            batchUserDtl.save(failOnError: true, flush: true)
                                        }
                                    }
                                    if(params.classBatchId!=null&&!"null".equals(params.classBatchId)){
                                        instituteService.addUserToBatch(user.username, new Long(params.classBatchId),null, null,null,getSiteId(request))

                                    }


                                    if(success.equals("OK")) {
                                        usersAdded += " " + (user.email ? user.email : user.mobile) + ","
                                        usersAddedUsernames+=" "+(user.username.split('_')[1])+","
                                    }
                                    if(success.equals("PRESENT")) {
                                        alreadyPresent += " " + (user.email ? user.email : user.mobile) + ","
                                        alreadyPresentUsernames+=" "+(user.username.split('_')[1])+","
                                    }
                                    if (existingUser) redisService.deleteKeysWithPattern(user.username+"_"+"booksList")
                                }
                            }else{
                                v.putAt("Error", userCheck)
                                usersNotAdded.add(v)
                            }
                        }
                    }

                }
            }
            List withProperties = getSiteId(request).intValue()==1?["Login Id(Email/Mobile)","Name", "Email","Mobile","Pincode","School/College","Admission No.","Error"]:["Login Id(Email/Mobile)","Name", "Email","Mobile","Error"]
            List headers = getSiteId(request).intValue()==1?["Login Id(Email/Mobile)","Name", "Email","Mobile","Pincode","School/College","Admission No.","Error"]:["Login Id(Email/Mobile)","Name", "Email","Mobile","Error"]

            File fileDir = new File("upload/temp/excel")
            if(!fileDir.exists()) fileDir.mkdirs()
            String notAdded = ""
            if(usersNotAdded.size()>0){
                usersNotAdded.forEach({ user -> notAdded += user."Login Id(Email/Mobile)"+", " })
                new XlsxExporter('upload/temp/excel/File'+ fileName + ".xlsx")
                        .fillHeader(headers)
                        .add(usersNotAdded, withProperties)
                        .save()
            }
            def json = [
                    status: usersAdded.length()>0?usersAdded.substring(0,(usersAdded.length()-1))+" added.":"",'alreadyPresent':alreadyPresent?alreadyPresent.substring(0, alreadyPresent.length() - 1)+"  is  already present in this Institute":"",
                    errorFile: fileName,
                    usersAdded: usersAddedUsernames.length()>0?usersAddedUsernames.substring(0,(usersAddedUsernames.length()-1)):"",
                    usersAlreadyPresent: alreadyPresentUsernames.length()>0?alreadyPresentUsernames.substring(0, alreadyPresentUsernames.length() - 1):"",
                    usersNotAdded: notAdded.length()>0?notAdded.substring(0,notAdded.length()-2):""
            ]
            render json as JSON
        } catch (Exception e){
            def json = [
                    status: "FILE_ERROR",
                    alreadyPresent:"",
                    message: e.toString()
            ]
            render json as JSON
        }

    }

    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])
    def addUsersAndBooks() {
        try {
            String username = "";
            Integer siteId = getSiteId(request)
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
                MultipartFile file = multiRequest.getFile("file")
                if (!file.empty) {
                    def sheetheader = []
                    def values = []
                    def workbook = new XSSFWorkbook(file.getInputStream())
                    def sheet = workbook.getSheetAt(0)
                    for (cell in sheet.getRow(0).cellIterator()) {
                        sheetheader << cell.stringCellValue
                    }
                    def headerFlag = true
                    for (row in sheet.rowIterator()) {
                        if (headerFlag) {
                            headerFlag = false
                            continue
                        }
                        def value = ''
                        def map = [:]
                        for (cell in row.cellIterator()) {
                            switch (cell.cellType) {
                                case 1:
                                    value = cell.stringCellValue
                                    map["${sheetheader[cell.columnIndex]}"] = value
                                    break
                                case 0:
                                    value = cell.numericCellValue
                                    map["${sheetheader[cell.columnIndex]}"] = ""+value
                                    break
                                default:
                                    value = ''
                            }
                        }
                        values.add(map)
                    }

                    boolean existingUser
                    values.each { v ->
                        if (v."Mobile" != null) {
                                User user;
                                    existingUser = true
                                    username = "" + siteId + "_" +v.Mobile;
                                    user = User.findByUsername(username)
                            if(user==null) {
                                WinGenerator winGenerator = new WinGenerator()
                                winGenerator.save(failOnError: true)
                                user = new User(
                                        username:username,
                                        name: (v.Name != null && v.Name != "") ? v.Name : "user",
                                        email: (v.Email != null && v.Email != "") ? v.Email : null,
                                        mobile: (v.Mobile != null && v.Mobile != "") ? v.Mobile : null,
                                        password: v.Mobile,
                                        siteId: siteId,
                                        win: winGenerator.id,
                                        teacher:"true"
                                )
                                user.save(failOnError: true, flush: true)
                                //add appropriate roles
                                Role role = Role.findByAuthority("ROLE_USER")
                                UserRole.create(user, role, true)
                                role = Role.findByAuthority("ROLE_CAN_ADD")
                                UserRole.create(user, role, true)
                                role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                                UserRole.create(user, role, true)

                            }else{
                                redisService.deleteKeysWithPattern(user.username+"_"+"booksList")
                            }
                           if(v."Isbn"!=null) {
                                String[] isbns = (""+(v."Isbn")).split(",")
                                BooksMst booksMst
                                String isbn
                                for (int i = 0; i < isbns.length; i++) {
                                    isbn = (""+isbns[i]).trim()
                                    booksMst = dataProviderService.getBooksMstByIsbnAndPublished(isbn)
                                    if (booksMst != null) {
                                        BooksPermission booksPermission = new BooksPermission()
                                        booksPermission.bookId = booksMst.id
                                        booksPermission.username = user.username
                                        booksPermission.addedBy = "System"
                                        if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                                            Calendar c = Calendar.getInstance()
                                            c.add(Calendar.DATE, booksMst.validityDays)
                                            booksPermission.expiryDate = c.getTime()
                                        }
                                        booksPermission.save(failOnError: true, flush: true)
                                        //code to add package books
                                        if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
                                        redisService.("userShelfBooks_" + user.username) = null

                                    }
                                }

                            }
                            //send sms
                            try {
                                def message = "Dear Incredible Teacher, Your free digital specimen ebooks account has been created on ebooks.oswaalbooks.com Login ID - ${user.mobile} Password - ${user.mobile} Be the first one to access our latest updated books and releases digitally. You will also receive your paperback specimen copy soon. - Oswaal Books"
                                utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                            } catch (Exception e) {
                                println("exception in sending institute sms" + e.toString())
                            }
                        }


                    }

                }


            def json = [
                    status: "upload successful",
                    ]
            render json as JSON
        } catch (Exception e){
            println("the exception is "+e.toString())
            def json = [
                    status: "FILE_ERROR",
                    alreadyPresent:"",
                    message: e.toString()
            ]
            render json as JSON
        }

    }
    def checkUserDetailsForExcel(user, batchId,siteId){
        String errors = ""
        def loginId = user."Login Id(Email/Mobile)"
        def email = user."Email"
        def mobile = user."Mobile"
        def pincode = user."Pincode"
        def admissionNo = user."Admission No."
        if(loginId!=null && !loginId.toString().trim().equals('')){
           if(loginId.toString().indexOf('@')>-1){
               if("<EMAIL>"==loginId.toString()) errors+= "Invalid Login Id (Email), "
           }else{
               try{
                   loginId = (BigInteger) new Double(loginId)
                   if(loginId.toString().length()!=10) errors+= "Invalid Login Id (Mobile), "
               }catch(Exception e){
                   errors+= "Invalid Login Id (Mobile),1 "
               }
           }
        }
        if(email!=null && !email.toString().trim().equals('')) {
            if (email.toString().indexOf('@') > -1) {
                if("<EMAIL>"==email) errors+= "Invalid Login Id (Email), "
            }else{
                errors+= "Invalid Email, "
            }
        }
        if(mobile!=null && !mobile.toString().trim().equals('')){
            try{
                mobile = (BigInteger) new Double(mobile)
                if(mobile.toString().length()!=10){
                    errors+= "Invalid Mobile, "
                }
            }catch(Exception e){
                errors+= "Invalid Mobile, "
            }
        }
        if(pincode!=null && !pincode.toString().trim().equals('')){
            try{
                pincode = (Integer) new Double(pincode)
                if(pincode.toString().length()!=6){
                    errors+= "Invalid Pincode, "
                }
            }catch(Exception e){
                errors+= "Invalid Pincode, "
            }
        }
        if(admissionNo!=null && !admissionNo.toString().trim().equals('')){
            if(!admissionNo.getClass().getName().equals("".getClass().getName())) admissionNo = (String) (BigInteger) admissionNo
            def result = instituteService.checkAdmissionNumber(batchId, admissionNo)
            if(!result.equals(true)){
                if(!result.equals(loginId)){
                    errors+="Admission No. already used for "+result+", "
                }
            }
        }
        return (errors.length()==0)?("OK"):(errors.substring(0, errors.length()-2)+".")
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    @Transactional
    def genarateUsertoBatchWS() {
        String alreadyPresent = "";
        String usersAdded = "";
        int loginLimit = 0;
        Integer siteId = getSiteId(request)
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        boolean showErrorMessage = false
        String instituteUrl="";
        String wsLink="";
        String userType=""
        showErrorMessage=(sm.sageOnly!="true")
        if (params.batchId != null && params.useremail != null) {
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(new Long(params.batchId))
            InstituteMst instituteMst = InstituteMst.findById(courseBatchesDtl.conductedBy)
            if(instituteMst.urlname!=null && instituteMst.urlname!="") {
                instituteUrl="https://www.wonderslate.com/inst/${instituteMst.id}"
            }else{
                instituteUrl="https://www.wonderslate.com"
            }
            def password=params.password
            password=password?password.replaceAll('~','&'):"password1"
            User user;
            String username = "";
            String[] users = params.useremail.split(",")
            boolean existingUser = true
            for (int i = 0; i < users.length; i++) {
                if (users[i] == null || (users[i].trim().length() == 0)) {
                    continue
                };
                existingUser = true
                WinGenerator winGenerator = new WinGenerator()
                winGenerator.save(failOnError: true)
                username = users[i];

                //On WS site - we should not check the user's presence seperately for email and mobile. For institute it has to be one or or the other
                user = User.findByUsername(siteId + "_" + username)

                if (user == null) {
                    existingUser = false
                    if (users[i].indexOf('@') > -1) {
                        user = new User(username: "" + siteId + "_" + username, name: "user",
                                email: username, password: password, siteId: siteId)
                    } else {
                        user = new User(username: "" + siteId + "_" + username, name: "user",
                                password: password, siteId: siteId,
                                mobile: username)
                    }
                    user.win = winGenerator.id
                    user.save(failOnError: true, flush: true)
                    Role role = Role.findByAuthority("ROLE_USER")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_ADD")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                    UserRole.create(user, role, true)
                    usersAdded += " " + user.email?user.email:user.mobile + ","
                }
                if (user != null) {
                    if (courseBatchesDtl != null) {
                        BatchUserDtl batchUserDtl
                        //check if the user is already added

                         batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(user.username, new Long(params.batchId))

                        if (batchUserDtl == null) {

                                batchUserDtl = new BatchUserDtl(username: user.username, batchId: new Long(params.batchId),
                                        createdBy: springSecurityService.currentUser.username, instructor: "instructor".equals(params.userType) ? "true" : null)


                            batchUserDtl.save(failOnError: true, flush: true)

                            //for instructor type user add publisher access
                            //add publishing admin access to the user.
                            Role role
                            if(instituteMst.publisherId!=null&&"instructor".equals(params.userType)&&siteId.intValue()==1){
                                user.publisherId = instituteMst.publisherId
                                user.save(failOnError: true, flush: true)
                                role = Role.findByAuthority("ROLE_BOOK_CREATOR")
                                UserRole.create(user, role, true)
                                role = Role.findByAuthority("ROLE_PUBLISHER")
                                UserRole.create(user, role, true)

                            }

                            if(courseBatchesDtl.groupId!=null&&!"Default".equals(courseBatchesDtl.name)){
                                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                                if(groupsMembersDtl==null){
                                    groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "instructor".equals(params.userType)?"admin":"user",
                                            profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                            groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                                    groupsMembersDtl.save(failOnError: true, flush: true)
                                }
                            }

                            //if instructor then add to the institute teachers group
                            if(courseBatchesDtl.groupId!=null&&"Default".equals(courseBatchesDtl.name)&&"instructor".equals(params.userType)){
                                GroupsMembersDtl groupsMembersDtl = GroupsMembersDtl.findByGroupIdAndUsername(courseBatchesDtl.groupId,batchUserDtl.username)
                                if(groupsMembersDtl==null){
                                    groupsMembersDtl = new GroupsMembersDtl(username: batchUserDtl.username, role: "admin",
                                            profilepic: User.findByUsername(batchUserDtl.username).profilepic ? User.findByUsername(batchUserDtl.username).profilepic : null,
                                            groupId: courseBatchesDtl.groupId,name:User.findByUsername(batchUserDtl.username).name,userId: User.findByUsername(batchUserDtl.username).id)
                                    groupsMembersDtl.save(failOnError: true, flush: true)
                                }
                            }


                            if (users[i].indexOf('@') > -1&&!existingUser&&siteId.intValue()!=80) {
                                   if(instituteMst.eduWonder=="true"){
                                        userManagementService.sendEmailToInstituteUser(instituteMst.name,instituteMst.logo,instituteMst.id,user.email, password, SiteMst.findById(getSiteId(request)).siteName,instituteUrl)
                                    }else {
                                        userManagementService.sendEmailToUserWS(user.email, password, SiteMst.findById(getSiteId(request)).siteName)
                                    }

                                } else if(users[i].indexOf('@') < 0&&!existingUser&&siteId.intValue()!=80) {
                                if(siteId==1) {
                                    try {
                                        userType =  "instructor".equals(params.userType) ? "Teacher" : " Student"
                                        wsLink = "https://bit.ly/3PFCuGb"
                                        def message = "Dear ${user.name}, Your Wonderslate ${userType} account has been created on your institution website. Click on the below link ${instituteUrl} and login using ${user.mobile} and Password: ${password}. Contact Us: ${wsLink}"
                                        utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                                    } catch (Exception e) {
                                        println("exception in sending institute sms" + e.toString())
                                    }
                                }
                            }
                                dataProviderService.getBooksListForUser(user.username)
                                dataProviderService.getInstituteMembersCount(new Long(instituteMst.id))

                        } else {

                            alreadyPresent += " " + user.email?user.email:user.mobile + ","

                        }

                    }
                    if(params.classBatchId!=null&&!"null".equals(params.classBatchId)){
                        instituteService.addUserToBatch(user.username, new Long(params.classBatchId),null, null,null,getSiteId(request))
                    }
                }
            }

            def json = [
                    status: usersAdded.length() > 0 ? usersAdded.substring(0, (usersAdded.length() - 1)) + " added." : "", 'alreadyPresent': alreadyPresent ? alreadyPresent.substring(0, alreadyPresent.length() - 1) + "  is  already present in this Institute" : "",showErrorMessage: showErrorMessage
            ]
            render json as JSON


        }

    }

    @Transactional @Secured(['ROLE_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def manageInstitutePage(){
        List bannersMstList = new ArrayList()
        List galleryImageList = new ArrayList()
        InstituteMst instituteMst = null

        boolean wsAdmin = false
        User user = session['userdetails']
        if(user.authorities.any {
            it.authority == "ROLE_MASTER_LIBRARY_ADMIN"
        }) {
            println("has access man")
            wsAdmin = true
        }else{
            println("doesnt have acess")
        }
        //if wonderslate admin then take the institute id other wise if institute admin, then it has pick from db. Security reason.
        if(session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_ADMIN" }&&session["userdetails"].publisherId==null){
            instituteMst = InstituteMst.findById(new Long(params.instituteId))

        }else{
            InstituteUserDtl instituteUserDtl = InstituteUserDtl.findByUsername(springSecurityService.currentUser.username)
            if(instituteUserDtl!=null) instituteMst = InstituteMst.findById(instituteUserDtl.instituteId)
        }
        String sql = "SELECT urlname FROM wsuser.institute_mst;";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List insUrls = []
        results.collect { rows ->
            if (rows.urlname != null && rows.urlname !="") {
                insUrls.add("" + rows.urlname)
            }
        }
        if(instituteMst!=null) {
            bannersMstList = BannersMst.findAllByInstituteId(instituteMst.id);
            galleryImageList = InstituteGalleryDtl.findAllByInstituteId(instituteMst.id)
        }
        [institute:instituteMst, instituteUrls: insUrls ,bannersMstList:bannersMstList,galleryImageList:galleryImageList,wsAdmin:wsAdmin]
    }

    @Transactional
    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN'])
    def updateInstitute(){
        def tagline = params.tagline
        def  addressLine1 = params.addressLine1
        def addressLine2 = params.addressLine2
        def yt = params.yt
        def twt = params.twt
        def li = params.li
        def fb = params.fb
        def seoDescription = params.seoDescription
        def website=params.website
        InstituteMst institute = InstituteMst.findById(new Long(params.instituteId))
        institute.tagline = tagline?tagline.replaceAll('~','&'):null
        institute.name = ""+params.name
        institute.contactName = ""+params.cname
        institute.contactNumber = ""+params.cnumber
        institute.contactEmail = ""+params.cemail
        institute.addressLine1 = addressLine1?addressLine1.replaceAll('~','&'):null
        institute.addressLine2 = addressLine2?addressLine2.replaceAll('~','&'):null
        institute.town = ""+params.town
        institute.zipcode = ""+params.zip
        institute.state = ""+params.state
        institute.country = ""+params.country
        institute.urlname = ""+params.urlname
        institute.website = website?website.replaceAll('~','&'):null
        institute.facebook = fb?fb.replaceAll('~','&'):null
        institute.fax = ""+params.fax
        institute.linkedin = li?li.replaceAll('~','&'):null
        institute.youtube = yt?yt.replaceAll('~','&'):null
        institute.twitter = twt?twt.replaceAll('~','&'):null
        institute.checkOutDays = ""+params.checkOutDays
        institute.seoDescription =seoDescription?seoDescription.replaceAll('~','&'):null
        institute.save(failOnError: true, flush: true)
        if(institute.urlname!=null) redisService.("institutionsUrl_"+institute.urlname) = ""+institute.id
        def json = ["status":"success"]
        render json as JSON
    }

    @Transactional
    def uploadInstituteLogo() {
        Long id = new Long(""+params.instituteId)

        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");

        if (id != "" && id != null && !file.empty) {
            InstituteMst instituteMst = InstituteMst.findById(id)
            File uploadDir = new File("upload/institute/" + instituteMst.id)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            def filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            file.transferTo(new File(uploadDir.absolutePath + "/" + file.filename))
                instituteMst.logo = filename
            instituteMst.save(failOnError: true, flush: true)
        }
        redirect(controller: "institute", action: "manageInstitutePage", params: [instituteId: id])
    }
    @Transactional
    def instituteImage(){
        try {
            if(params.instituteId!='' && params.instituteId!=null) {
                InstituteMst instituteMst = InstituteMst.findById(params.instituteId)
                def file = new File("upload/institute/" + instituteMst.id + "/" + instituteMst.logo)
                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${instituteMst.logo}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }
                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            }
        } catch (Exception e) {
            render "Exception in showPublisherImage " + e.toString();
        }
    }

    @Transactional
    def index() {
        session['siteId'] = new Integer(1);
        session.setAttribute("entryController", "books")
        session.setAttribute("siteName", "Wonderslate")
        session.setAttribute("loginType", "email,mobile")
        session.setAttribute("siteNameUrl", "books")
        InstituteMst instituteMst = new InstituteMst()
        if(params.instituteId!="" && params.instituteId!=null){
            instituteMst = dataProviderService.getInstituteMst(new Long(params.instituteId))
        }
        else if(params.urlname!="" && params.urlname!=null){
            instituteMst = dataProviderService.getInstituteMstByUrl(session['siteId'], params.urlname.toString())
            if(instituteMst.urlname!=null) {
                session.setAttribute('instituteUrlName', instituteMst.urlname)
                session.setAttribute('instituteId', instituteMst.id)
                session.setAttribute('instituteLogo', instituteMst.logo)
            }

        }else{
            return redirect(controller: "books", action: "index")
        }
        session['fromInstitutePageinstituteId'] = instituteMst.id
        if(instituteMst.level!=null) {
            session['instituteLevel'] = instituteMst.level
        }
        if(instituteMst.syllabus!=null) {
            session['instituteSyllabus'] = instituteMst.syllabus
        }
        if("false".equals(instituteMst.shopEnabled)||instituteMst.shopEnabled==null){
            session['instituteShopEnabled'] = "true"
        }
        String title = instituteMst.name+" on Wonderslate"
        if(instituteMst==null) return redirect(controller: "books", action: "index")
        [institute: instituteMst, insituteHomePageInstitute:instituteMst, commonTemplate: "true",title:title]
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def getInstituteforUser(){
        String access="false";
        if(springSecurityService.currentUser!=null) {
            String sql = "select im.id,im.publisher_id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                    " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'and " +
                    "im.site_id=" + params.siteId+"";
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            List pub=results.collect { publisher ->
                access="true"
               return  [publisherId:publisher[1] ]
            }
            def json = ["access":access,"publishers":pub]
            render json as JSON
        }
    }


    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def generateAccessCodeForInstitute(){
        Integer siteId = getSiteId(request)
        String codeGen="";
        String count=params.accessCount;
        Integer count1=Integer.parseInt(count)
        Random rand = new Random();
        for(int i=0;i<count1;i++){
            if(getSiteId(request)==24) {
                codeGen = rand.nextInt(900000) + 100000
            }else{
                codeGen  = RandomStringUtils.randomAlphanumeric(6);
                codeGen=codeGen.toUpperCase();
            }
            if(!AccessCode.findByCode(codeGen)) {
                AccessCode accessCode = new AccessCode(instituteId: new Long(params.instituteId),
                        code: codeGen,siteId: siteId)
                accessCode.save(failOnError: true, flush: true)
            }
        }
        def json = ["status":"OK"]
        render json as JSON
    }


    @Transactional
    def validateEmailAndAccessCode(){
        Integer siteId = getSiteId(request)
        def email=params.email;
        def accessCode=params.accessCode;
        String emailcheck="OK"
        String accessCodeCheck="OK"
        User user=User.findByUsernameAndSiteId(siteId + "_"+email,siteId);
        AccessCode accessCode1=AccessCode.findByCodeAndStatusIsNull(accessCode)
        if(user!=null){
            emailcheck="Already Exist"
        }
        if(accessCode1==null){
            accessCodeCheck="Invalid Access Code"
        }
        def json = ["emailCheck":emailcheck,"accessCodeCheck":accessCodeCheck]
        render json as JSON

    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def getAllAccessCodes(){
        List<AccessCode> accessCodeList = AccessCode.findAllByInstituteId(params.instituteId)
        def json = ['accessCodes': accessCodeList, status: accessCodeList.size()>0?"OK":"No Access Codes Found"]
        render json as JSON
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def downloadAccessCodeDetails(){
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String sql = "SELECT id,code,status,username,date_created,date_redeemed FROM wsuser.access_code where institute_id='"+params.instituteId+"'";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        int i = 0;
        if(results!=null && results.size()>0){
            List data = results.collect{accessCode ->
                i++
                return [code: accessCode.code, status: accessCode.status,id: accessCode.id, slno: i,
                        username: (accessCode.username?accessCode.username.toString().substring(3):""),
                        dateC: (new SimpleDateFormat("dd/MM/yyyy")).format(accessCode.date_created),
                        dateR: (accessCode.date_redeemed?(new SimpleDateFormat("dd/MM/yyyy")).format(accessCode.date_redeemed):"")]
            }
            List headers = ["Serial No.","Code","Status","Username","Date Created","Date Redeemed"]
            List withProperties = ['slno',"code","status","username","dateC","dateR"]
            def fileName = "AccessCode_Details_"  + (new Random()).nextInt(9999999) + ".xlsx";

            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
        }
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def usageReportInstituteAdmin(){
        def instituteName;
        def instituteId=null;
        boolean showInstituteNames=false
        if (session["userdetails"].authorities.any {
            it.authority == "ROLE_MASTER_LIBRARY_ADMIN"
        }){
            showInstituteNames=true
        }
        if(!showInstituteNames) {
        String sql ="select im.id,im.name,cbd.id batchId,im.no_of_users from wsuser.institute_mst im, wsuser.institute_user_dtl iud,wsuser.course_batches_dtl cbd  " +
                "where im.site_id="+session["siteId"] +
                " and cbd.conducted_by=im.id and iud.username='"+springSecurityService.currentUser.username+"' and iud.institute_id=im.id ";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List institutes = results.collect { institute ->
            instituteName=institute[1]+"";
            instituteId=institute[0];
        }
        }
        [instituteId: instituteId, instituteName: instituteName,showInstituteNames:showInstituteNames]
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def usageReportCorporateWise(){
        def instituteName;
        def instituteId=null;
        String sql ="select im.id,im.name,cbd.id batchId,im.no_of_users from wsuser.institute_mst im, wsuser.institute_user_dtl iud,wsuser.course_batches_dtl cbd  " +
                "where im.site_id="+session["siteId"] +
                " and cbd.conducted_by=im.id and iud.username='"+springSecurityService.currentUser.username+"' and iud.institute_id=im.id ";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List institutes = results.collect { institute ->
            instituteName=institute[1]+"";
            instituteId=institute[0];
        }
        [instituteId: instituteId, instituteName: instituteName,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def usageReportTitleWise(){
        def instituteName;
        def instituteId=null;
        String sql ="select im.id,im.name,cbd.id batchId,im.no_of_users from wsuser.institute_mst im, wsuser.institute_user_dtl iud,wsuser.course_batches_dtl cbd  " +
                "where im.site_id="+session["siteId"] +
                " and cbd.conducted_by=im.id and iud.username='"+springSecurityService.currentUser.username+"' and iud.institute_id=im.id ";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List institutes = results.collect { institute ->
            instituteName=institute[1]+"";
            instituteId=institute[0];
        }
        [instituteId: instituteId, instituteName: instituteName,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    @Secured (['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def usageReportCorporateWiseData(){
        Integer siteId = getSiteId(request);
        def var="";
        String sql = "select bvd.institute_id,bvd.book_id,max(bvd.date_created) date_created1" +
                " from wslog.books_view_dtl bvd " +
                " where bvd.site_id="+siteId+" AND bvd.institute_id IN("+params.instituteids+")" +
                " AND DATE(DATE_ADD(bvd.date_created," +
                "INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y') " +
                "AND DATE(DATE_ADD(bvd.date_created," +
                "INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y') "+
                " group by username,book_id,bvd.institute_id " +
                " order by date_created1 desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results1 = sql1.rows(sql)
        List usagereport = results1.collect { report ->
            var += "select "+report[1]+" book_id, '"+report[0]+"' institute_id ,'"+report[2]+"' date_created  from dual  union ";
        }
        if(var!="") {
            def queryappend = var.substring(0, var.length() - 6);
            String sql2 = "select im.name,bm.id,isbn, title,count(bm.id) no_of_views,im.no_of_users" +
                    " from wsuser.books_mst bm , wsuser.institute_mst im,(" + queryappend + ")x where " +
                    "   bm.site_id ="+siteId+"\n" +
                    "    AND bm.id =x.book_id" +
                    "    and im.id =x.institute_id"+
                    " group by im.name,bm.id,im.no_of_users "+
                    " order by im.name"
            def dataSource2 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql3 = new Sql(dataSource2)
            def results2 = sql3.rows(sql2);
            List usagereport2 = results2.collect { report1 ->
                return [institutename: report1[0], bookId: report1[1], isbn: report1[2], title: report1[3], noofviews: report1[4],noOfUsers: report1[5]]
            }
            if ("true".equals(params.download)) {
                List headers = ["Organization", "ISBN", "Title", "Total Licenses", "Accessed by" ]
                List withProperties = ["institutename", "isbn", "title", "noOfUsers", "noofviews"]

                def fileName = "Data_"+(params.startDate!=""?params.startDate+"_":"FromAny_")+
                        (params.endDate!=""?params.endDate+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response,fileName)
                    fillHeader(headers)
                    add(usagereport2, withProperties)
                    save(response.outputStream)
                }
            } else {
                def json = [
                        'results': usagereport2,
                        'status' : usagereport2 ? "OK" : "Nothing present",
                ]
                render json as JSON
            }
        }else {
            def json = [status: "Not present"]
            render json as JSON
        }
    }
    @Secured (['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def usageReportTitleWiseData() {
        Integer siteId = getSiteId(request);
        def var="";
        String sql = "select bvd.book_id,bvd.username,bvd.date_created,bvd.institute_id from wslog.books_view_dtl bvd " +
                " where bvd.site_id="+siteId+" AND bvd.institute_id IN("+params.instituteids+")"+
                "    AND DATE(DATE_ADD(bvd.date_created, " +
                "        INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.startDate+"','%d-%m-%Y') " +
                "    AND DATE(DATE_ADD(bvd.date_created, " +
                "        INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.endDate+"','%d-%m-%Y') " ;
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List usage = results.collect { report ->
            if(report[0]!=null && report[1]!=null) {
                var += "select "+report[0]+" book_id, '"+report[1]+"' username ,'"+report[2]+"' date_created, '"+report[3]+"' institute_id from dual  union ";
            }
        }
        if(var!="") {
            def queryappend = var.substring(0, var.length() - 6);
            String sql2 = "select isbn,title, um.name,um.email,  DATE_FORMAT(max(x.date_created),'%d/%m/%Y') date_created, im.name institutename" +
                    " from wsuser.books_mst bm , wsuser.user um, wsuser.institute_mst im,(" + queryappend + ")x where " +
                    " bm.site_id="+siteId+" " +
                    "  and bm.id=x.book_id and um.username=x.username and im.id=x.institute_id " +
                    " GROUP BY bm.id,um.id,im.name "+
                    " order by im.name asc, max(x.date_created) desc"
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql2)
            List usagereport1 = results2.collect { report1 ->
                AccessCode accessCode = AccessCode.findByUsername(''+getSiteId(request)+'_'+report1[3])
                return [institutename: report1[5], isbn: report1[0], title: report1[1], dateCreated: report1[4],name: report1[2],email: accessCode==null?report1[3]:report1[3]+'/'+accessCode.code]
            }
            if ("true".equals(params.download)) {
                List headers = ["Organization", "ISBN", "Title", "Accessed by email id/code", "User Profile Name","Last Accessed On" ]
                List withProperties = ["institutename", "isbn", "title", "email", "name","dateCreated"]

                def fileName = "Data_"+(params.startDate!=""?params.startDate+"_":"FromAny_")+
                        (params.endDate!=""?params.endDate+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response,fileName)
                    fillHeader(headers)
                    add(usagereport1, withProperties)
                    save(response.outputStream)
                }
            }else{
                def json = [status: usagereport1 ? "OK" : "Not present", results: usagereport1]
                render json as JSON
            }
        }
        def json = [status:"Not present"]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN'])
    def updateBookBatchDetailData(){
        Integer siteId = getSiteId(request);
        def batchId = params.batchId
        def bookId = params.bookId
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if(batchId!=null && batchId!="" && bookId!=null && batchId!=""){
            CourseBatchesDtl dtl = CourseBatchesDtl.findById(new Long(batchId))
            SimpleDateFormat DateFormatter = new SimpleDateFormat('yyyy-MM-dd');
            String query = ""
            if(params.validity!="" && params.validity!=null){
                query+=" validity = '"+params.validity+"', book_expiry_date = '" + DateFormatter.format(new Date( (dtl.startDate>new Date()?dtl.startDate.getTime():new Date().getTime()) + (new Long(params.validity) * new Long(86400000))))+"'"
            }
            if(params.validity!="" && params.validity!=null) query+=','
            query+=" number_of_licenses = "+(params.noOfLic.toString().equals('')?null:params.noOfLic)
            BooksBatchDtl.wsuser.executeUpdate("update BooksBatchDtl set "+query+" where batch_id = "+batchId +" and book_id = "+bookId)
            Integer noOfcopies=BooksBatchDtl.findByBatchIdAndBookId(new Long(batchId),new Long(bookId)).numberOfLicenses
            Integer usersNumber=BooksPermission.findAllByBatchIdAndBookIdAndPoType(new Long(batchId),new Long(batchId),"ADDEDFROMINSTITUTE").size()
            if(noOfcopies!=null && usersNumber!=null) {
            Integer diff=noOfcopies-usersNumber;
            List booksQueues
            if(noOfcopies>usersNumber) {
                booksQueues= BooksQueueDtl.findAllByBatchIdAndBookId(new Long(batchId),new Long(bookId),[sort:"dateCreated",order:"asc",max:diff])
                booksQueues.collect {
                        def expiry=true;
                        Calendar c = Calendar.getInstance()
                            //if(BooksBatchDtl.findByBatchIdAndBookId(new Long(params.batchId),new Long(params.bookId)).numberOfLicenses!=null) {
                    Integer checkoutDays =
                                    (InstituteMst.findById(CourseBatchesDtl.findById(new Long(params.batchId)).conductedBy).checkOutDays != ""
                                            && InstituteMst.findById(CourseBatchesDtl.findById(new Long(params.batchId)).conductedBy).checkOutDays != null)
                                            ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(new Long(params.batchId)).conductedBy).checkOutDays)
                                            : null
                            if(checkoutDays!=null && checkoutDays!="") {
                                c.add(Calendar.DATE, checkoutDays)
                            }else{
                                c.add(Calendar.DATE, 14)
                            }
//                        }else{
//                            expiry=false
//                        }
                            BooksPermission booksPermission = new BooksPermission(
                                    bookId: new Long(params.bookId),
                                    username: it.username,
                                    poType: 'ADDEDFROMINSTITUTE',
                                    batchId: new Long(params.batchId),
                                    addedBy: springSecurityService.currentUser.username,
                                    expiryDate: expiry==true?c.getTime():null
                            )
                            booksPermission.save()
                            BooksPermissionCopy booksPermissionCopy = new BooksPermissionCopy(
                                    bookId: new Long(params.bookId),
                                    username: it.username,
                                    poType: 'ADDEDFROMINSTITUTE',
                                    batchId: new Long(params.batchId),
                                    addedBy: springSecurityService.currentUser.username,
                                    bpId: booksPermission.id,
                                    expiryDate: expiry==true?c.getTime():null
                            )
                            booksPermissionCopy.save()
                            BooksViewDtl booksViewDtl = new BooksViewDtl(bookId:new Long(params.bookId), viewSource: "web", viewType:"library",username:it.username,
                            siteId:siteId,instituteId:InstituteMst.findById(CourseBatchesDtl.findById(new Long(params.batchId)).conductedBy).id)
                            booksViewDtl.save(flush:true, failOnError: true)
                            redisService.("lastReadBooksIns_"+params.batchId+"_"+ it.username)=null
                            if (User.findByUsername(it.username).email != null && !User.findByUsername(it.username).email.equals("")) {
                                if (userManagementService.validateEmail(User.findByUsername(it.username).email, getSiteId(request))) {
                                    try {
                                        userManagementService.sendInstituteUserEmail(
                                                User.findByUsername(it.username).email,
                                                User.findByUsername(it.username).name,
                                                "This book '" + BooksMst.findById(new Long(params.bookId)).title + "' is added to your library.",
                                                new Long(params.bookId),
                                                SiteMst.findById(getSiteId(request)).siteName
                                        )
                                    } catch (Exception e) {
                                        println "sending library email failed " + e.toString()
                                    }
                                }
                            }
                    BooksQueueDtl.executeUpdate("delete  BooksQueueDtl where bookId IN (" + params.bookId + ") and batchId='" + params.batchId + "' and username= '" + it.username + "'")
                        }
            }
            }
            if(siteId.intValue()==1 || siteMst.instituteLibrary=="true")redisService.("userMyLibraryInstituteBooks_"+batchId) = null
            def json = [status: 'OK']
            render json as JSON
        }else{
            def json = [status: 'Failed']
            render json as JSON
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    @Transactional
    def downloadUserUploadSample(){
        SiteMst sm = dataProviderService.getSiteMst(getSiteId(request))
        List headers
        List withProperties
        int siteId = getSiteId(request).intValue()
        String userType="Users"
        if(params.userType!=null&&session["siteId"].intValue()==1) userType=params.userType
        if(siteId==1||("true".equals(""+session["commonWhiteLabel"]))){
            if(userType.equals("Instructors")){
                headers = ["Login Id(Email/Mobile)","Name", "Email","Mobile","Pincode","School/College"]
                def fileName = "UserUploadTemplate" + ".xlsx";
                new WebXlsxExporter(grailsApplication.config.grails.basedir.path+'/upload/templates/UserUploadTemplateInstructorWS.xlsx')
                        .with {
                            setResponseHeaders(response, fileName)
                            fillHeader(headers)
                            add(null, withProperties)
                            save(response.outputStream)
                        }
            }else{
                headers = ["Login Id(Email/Mobile)","Name", "Email","Mobile","Pincode","School/College","Admission No.","Validity"]
                def fileName = "UserUploadTemplate" + ".xlsx";
                new WebXlsxExporter(grailsApplication.config.grails.basedir.path+'/upload/templates/UserUploadTemplate.xlsx')
                        .with {
                            setResponseHeaders(response, fileName)
                            fillHeader(headers)
                            add(null, withProperties)
                            save(response.outputStream)
                        }
            }
        }else
        {
            headers = ["Login Id(Email/Mobile)","Name", "Email","Mobile","Admission No."]
            def fileName = "UserUploadTemplateLibWonder" + ".xlsx";
            new WebXlsxExporter(grailsApplication.config.grails.basedir.path+'/upload/templates/UserUploadTemplateLibWonder.xlsx')
                    .with {
                        setResponseHeaders(response, fileName)
                        fillHeader(headers)
                        add(null, withProperties)
                        save(response.outputStream)
                    }
        }

    }

    @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN'])
    @Transactional
    def downloadErrorUsers(){
        if(params.file!=null){
            String fileName = "File"+params.file+".xlsx"
            def file = new File("upload/temp/excel/"+fileName)
            if(file.exists()){
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }
                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
            }
        }
    }

    @Transactional
    def deleteTempExcels(){
        File file = new File("upload/temp/excel")
        if(file.exists()){
            FileUtils.cleanDirectory(file)
        }
        def json = [status: 'ok']
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN'])
    @Transactional
    def updateAdmissionNumber(){
        def json
        if(params.batchId!=null && params.batchId!="" && params.username!=null && params.username!="" && params.admissionNo!=null && params.admissionNo!=""){
            def result = instituteService.checkAdmissionNumber(params.batchId, params.admissionNo)
            if(result!=true){
                 json = [error: "Admission No.("+params.admissionNo+") already used for "+result]
            }else{
                if(instituteService.updateAdmissionNumber(params.batchId, getSiteId(request)+"_"+params.username, params.admissionNo)){
                     json = [message: "Updated Successfully."]
                }else{

                }
            }
        }
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def home(){
        if(params.instituteId==null){
            redirect(controller: "books", action: "home")
            return
        }
        boolean isInstructor=false, isLibrarian=false, ipRestricted=false,isEduWonder=false
        if(springSecurityService.currentUser!=null){
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }else if(session["userdetails"].publisherId!=null&&session["isInstitutePublisher"]==null){
                session["isInstitutePublisher"] = userManagementService.isInstitutePublisher()
            }
        }
        Integer instituteId = new Integer(params.instituteId)
        InstituteMst instituteMst = dataProviderService.getInstituteMst(instituteId)

        /** user this when implement next level classes
        List batches
        if(redisService.("batchesList_"+springSecurityService.currentUser.username)==null) dataProviderService.getBatchesForUser(springSecurityService.currentUser.username,instituteId)
        batches = new JsonSlurper().parseText(redisService.("batchesList_"+springSecurityService.currentUser.username)) */

        if(instituteMst!=null&&"true".equals(instituteMst.ipRestricted)) ipRestricted = true
        if(instituteMst!=null&&"true".equals(instituteMst.eduWonder)) isEduWonder = true

        if(session["isInstructor"]==null){
            session["isInstructor"] = userManagementService.isInstructorForInstitute(instituteId)
        }

        //check instructor
        if(session["isInstructor"]) isInstructor=true

        //check librarian
        User user = session["userdetails"]
        if(user.authorities.any {
            it.authority == "ROLE_LIBRARY_ADMIN"
        }) {
            isLibrarian = true
        }
        if(!isLibrarian && !isEduWonder){
            redirect(controller: "libraryBooks", action: "myLibrary", params: [instituteId: instituteId])
            return
        }
            [title: instituteMst.name + " - Wonderslate", isInstructor: isInstructor, isLibrarian: isLibrarian,
            instituteId: instituteId,ipRestricted: ipRestricted,instituteName:instituteMst.name, commonTemplate:"true", isEduWonder:isEduWonder]


    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def instituteReportsForWS() {
        Integer siteId = getSiteId(request);
        def var = "";
        boolean showInstituteNames = false
        if (session["userdetails"].authorities.any {
            it.authority == "ROLE_MASTER_LIBRARY_ADMIN"
        }) {
            showInstituteNames = true
        }
        String sql = "select bvd.book_id,\n" +
                " COALESCE(bm.isbn, ''),\n" +
                "        bm.title,\n" +
                " COUNT(bvd.id) AS book_count,\n" +
                "        bvd.username,\n" +
                "        COALESCE(u.name, '') AS uName,\n" +
                "        COALESCE(u.email, '') AS email,\n" +
                "        COALESCE(u.mobile, '') AS mobile,\n" +
                "        DATE_FORMAT(DATE_ADD(MAX(bvd.date_created), INTERVAL '5:30' HOUR_MINUTE), '%Y-%m-%d %h:%i %p') AS lastAccessed,\n" +
                "        bud.admission_no\n" +
                " from\n" +
                " reports.books_view_dtl bvd \n" +
                " left join reports.user u on bvd.username=u.username\n" +
                " left join reports.batch_user_dtl bud on bud.username = bvd.username\n" +
                " left join reports.course_batches_dtl cbd on bud.batch_id = cbd.id \n" +
                " join reports.books_mst bm on bm.id=bvd.book_id\n" +
                " where bvd.institute_id IN ("+params.instituteids+")\n" +
                " AND DATE(DATE_ADD(bvd.date_created,INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.startDate+"','%d-%m-%Y')\n" +
                " AND DATE(DATE_ADD(bvd.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.endDate+"','%d-%m-%Y') \t\n" +
                " GROUP BY\n" +
                " bvd.institute_id, bvd.book_id, bvd.username, u.name, u.email, u.mobile, bud.admission_no";

        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results1 = sql1.rows(sql)
        List usagereport2 = results1.collect { report1 ->
            return [institutename: report1[0], bookId: report1[1], isbn: report1[2], title: report1[3], noofviews: report1[4], username: report1[5].split('_')[1], uName: report1[6], uEmail: report1[7], uMobile: report1[8], lastAccessed: report1[9], admissionNo: report1[10]]
        }
        if ("true".equals(params.download)) {
            List headers
            List withProperties
            headers = showInstituteNames ? ["Institute Name", "Login Id", "Admission No.", "Name", "Email", "Mobile", "Book Id", "ISBN", "Title", "No. of views", "Last Accessed On"] : ["Login Id", "Admission No.", "Name", "Email", "Mobile", "Book Id", "ISBN", "Title", "No. of views", "Last Accessed On"]
            withProperties = showInstituteNames ? ["institutename", "username", "admissionNo", "uName", "uEmail", "uMobile", "bookId", "isbn", "title", "noofviews", "lastAccessed"] : ["username", "admissionNo", "uName", "uEmail", "uMobile", "bookId", "isbn", "title", "noofviews", "lastAccessed"]
            def fileName = "Data_" + (params.startDate != "" ? params.startDate + "" : "FromAny") +
                    (params.endDate != "" ? params.endDate + "" : "ToAny") + (new Random()).nextInt(9999999) + ".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(usagereport2, withProperties)
                save(response.outputStream)
            }
        } else {
            def json = [
                    'results'           : usagereport2,
                    'status'            : usagereport2 ? "OK" : "Nothing present",
                    'showInstituteNames': showInstituteNames
            ]
            render json as JSON
        }

    }



    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_LIBRARY_ADMIN','ROLE_PUBLISHER','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def manageClasses(){
    
        boolean hasPermission = false
        InstituteMst instituteMst = dataProviderService.getInstituteMst((new Integer(params.instituteId)))
        def isEduWonder = false
        if(instituteMst!=null&&"true".equals(instituteMst.eduWonder)) isEduWonder = true
        if(params.instituteId==null||springSecurityService.currentUser==null){
            hasPermission = false
        }else {
            //check if the user has access to this institute
            InstituteUserDtl instituteUserDtl = InstituteUserDtl.findByUsernameAndInstituteId(springSecurityService.currentUser.username,new Integer(params.instituteId))
            if(instituteUserDtl!=null){
               hasPermission = true
            }
            else {
               //WS admin
                if(session["userdetails"].authorities.any {
                    it.authority == "ROLE_WS_CONTENT_ADMIN"
                }&&session["userdetails"].publisherId==null){
                    hasPermission=true
                }else{
                    //libwonder and created by publisher

                    if(instituteMst.publisherId!=null&&session["userdetails"].publisherId!=null&&instituteMst.publisherId.intValue()==session["userdetails"].publisherId.intValue()){
                        hasPermission = true
                    }
                }
            }

        }
        if(!hasPermission){
            redirect(controller: "books", action: "index")
            return
        }
        Integer instituteId = new Integer(params.instituteId)
        List classes = CourseBatchesDtl.findAllByConductedBy(instituteId)
        [classes:classes,commonTemplate:"true",isEduWonder: isEduWonder,level:instituteMst.level,syllabus: instituteMst.syllabus]
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_PUBLISHER','ROLE_MASTER_LIBRARY_ADMIN']) @Transactional
    def addBooksToBatch(){
       instituteService.addBooksToBatch(new Integer(params.batchId),new Integer(params.defaultBatchId),params.bookIds)
        def json = ["status":"OK"]
        render json as JSON
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN','ROLE_PUBLISHER']) @Transactional
    def getBatchDetails(){
       CourseBatchesDtl courseBatchesDtl = instituteService.getBatchDetails(new Integer(params.batchId))
        def json = ["batchName":courseBatchesDtl.name, "endDate":courseBatchesDtl.endDate!=null?(new SimpleDateFormat("dd-MM-yyyy")).format(courseBatchesDtl.endDate):"","syllabus":courseBatchesDtl.syllabus,
        "grade":courseBatchesDtl.grade]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def editBatch(){
        instituteService.editBatch(new Integer(params.batchId),params.batchName,params.endDate,params.syllabus,params.grade)
        def json = ["status":"success"]
        render json as JSON
    }


    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def viewInstituteAdmin(){
        String sql = "SELECT u.name as name,COALESCE(u.email,'') as email,COALESCE(u.mobile,'') as mobile from user u, institute_user_dtl iu where iu.institute_id="+params.instituteId+" and u.username=iu.username";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        List admin = results.collect { admin ->
            return [name: admin.name, email: admin.email,mobile: admin.mobile]
        }
        def json = [
                status: admin?"OK":"No admin present for this institute", adminData: admin
        ]
        render json as JSON
    }

    @Transactional
    def generateSignUpEmail(){
        User user=User.findById(Long.parseLong(params?.userId))
        if(user?.userType=='instructor'){
            userManagementService.sendEmailToUserBouquetRetriever(user.email,user?.id+"", SiteMst.findById(getSiteId(request)).siteName)
        }else{
            userManagementService.sendEmailToUserEbouquet(user.email,user?.id+"", SiteMst.findById(getSiteId(request)).siteName)
        }
        def json=["status":"OK"]
        render json as JSON
    }

     @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
      def downloadUsageReport(){
        [showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
       }

     @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
      def getinstituteChapterDownloads(){
        String sql ="select cdt.book_id bookId,cdt.chapter_id chapterId,DATE_ADD(cdt.date_created, INTERVAL '5:30' HOUR_MINUTE) dateCreated,im.name,bm.title,cdt.username from" +
                "    wsuser.institute_mst im, wsuser.chapters_download_dtl cdt,wsuser.books_mst bm,wsuser.user " +
                "   where cdt.institute_id=im.id and cdt.book_id=bm.id and cdt.username=user.username and cdt.site_id="+session["siteId"] +
                "   and cdt.institute_id IN ("+params.instituteids+")  " +
                "   AND DATE(DATE_ADD(cdt.date_created, " +
                "   INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y')\n" +
                "   AND DATE(DATE_ADD(cdt.date_created," +
                "   INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y')";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        def date
        List downloadDetails = results.collect { user ->
            if (date != "") {
                date = (new SimpleDateFormat("dd-MM-yyyy hh.mm aa")).format(user.dateCreated)
            }
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(user.chapterId))
            return [bookId: user.bookId,bookTitle: user.title, chapterId: user.chapterId,dateCreated: date,instituteName:user.name,chapterName: chaptersMst?chaptersMst.name:"",userName:user.username.split('_')[1]]
        }
        if ("true".equals(params.download)) {
            List headers = ["Institute Name", "Login Id","Book Id","Book Name", "Chapter Id", "Chapter Name","Download Date"]
            List withProperties = ["instituteName","userName","bookId","bookTitle","chapterId", "chapterName","dateCreated"]

            def fileName = "Data_"+(params.startDate!=""?params.startDate+"_":"FromAny_")+
                    (params.endDate!=""?params.endDate+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response,fileName)
                fillHeader(headers)
                add(downloadDetails, withProperties)
                save(response.outputStream)
            }
         } else {
             def json = [status: downloadDetails ? "OK" : "Not present", downloadDetails: downloadDetails]
             render json as JSON
         }
     }



    @Transactional @Secured(['ROLE_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def updateAboutUs(){
              String  section="";
             String fieldvalue=""
             if(params.aboutUs!=null && !"".equals(params.aboutUs)){
                 section=params.aboutUs
                 fieldvalue="aboutUs"
             }else if(params.contactDetails!=null && !"".equals(params.contactDetails)){
                 section=params.contactDetails
                 fieldvalue="contactDetails"
             }else if(params.section3!=null && !"".equals(params.section3)){
                 section=params.section3
                 fieldvalue="section3"
             }else if(params.section4!=null && !"".equals(params.section4)){
                 section=params.section4
                 fieldvalue="section4"
             }
             InstituteMst.wsuser.executeUpdate("update InstituteMst set "+fieldvalue+" ='" + section + "' where id=" + params.instituteId)
                 redirect(controller: "institute", action: "manageInstitutePage", params: [instituteId: params.instituteId])
             }


    @Transactional @Secured(['ROLE_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def uploadInstituteImage() {
        String  galleryImageId =params.galleryImageId
        Long instituteId = new Long(""+params.instituteId)
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
        InstituteMst instituteMst = InstituteMst.findById(instituteId)
        if (galleryImageId != "" && galleryImageId != null && !file.empty) {
            File uploadDir = new File("upload/instituteGallery/" + instituteMst.id)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            def filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            file.transferTo(new File(uploadDir.absolutePath + "/" + file.filename))
            InstituteGalleryDtl instituteGalleryDtl= InstituteGalleryDtl.findById(new Long(galleryImageId))
            instituteGalleryDtl.imageName = filename
            instituteGalleryDtl.save(failOnError: true, flush: true)
            dataProviderService.getInstituteGalleryImages(new Long(instituteId))
        }else {
            File uploadDir = new File("upload/instituteGallery/" + instituteMst.id)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            def filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            file.transferTo(new File(uploadDir.absolutePath + "/" + file.filename))
            InstituteGalleryDtl instituteGalleryDtl= new InstituteGalleryDtl()
            instituteGalleryDtl.instituteId = instituteId
            instituteGalleryDtl.imageName = filename
            instituteGalleryDtl.save(failOnError: true, flush: true)
            dataProviderService.getInstituteGalleryImages(new Long(instituteMst.id))
        }
        redirect(controller: "institute", action: "manageInstitutePage", params: [instituteId: instituteId])
      }

    @Transactional @Secured(['ROLE_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def addBannersForInstitute() {
        def bannerId = params.bannerId;
        def instituteId = params.instituteId
        String siteId=getSiteId(request)
        def bookId = null
        def page=params?.bannerPage
        def actionType = params?.actionType
        if(params.bookId!="" && params.bookId!=null) bookId = new Long(params.bookId)
        if (bannerId != "" && bannerId != null) {
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            if (file.empty) {
                if(!"".equals(params.namename)){
                    BannersMst bannersMst = BannersMst.findById(new Long(bannerId))
                    bannersMst.imageName = params.name;
                    bannersMst.bookId = bookId
                    bannersMst.action = actionType
                    bannersMst.instituteId = new Long(instituteId)
                    bannersMst.save(flush: true, failOnError: true)
                    redirect(controller: "institute", action: "manageInstitutePage", params: [instituteId: instituteId])
                }
                flash.message = "File cannot be empty"
            } else {
                BannersMst bannersMst = BannersMst.findById(new Long(bannerId))
                if(!file.empty) {
                    File  uploadDir = new File("upload/instituteBanner/"+instituteId)
                    if (!uploadDir.exists()) uploadDir.mkdirs()
                    //creating directory to process images
                    File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                    if (!uploadDir1.exists()) uploadDir1.mkdirs()
                    String filename = file.originalFilename
                    filename = filename.replaceAll("\\s+", "")
                    if (instituteId != null && instituteId != "") {
                        filename =  filename.replaceAll("\\s+", "")
                    }
                    BufferedImage image = ImageIO.read(file.getInputStream())

                    ByteArrayOutputStream baos = new ByteArrayOutputStream()
                    ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                    baos.flush()
                    byte[] scaledImageInByte = baos.toByteArray()
                    baos.close()
                    FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
                    file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
                }
                bannersMst.imageName = params.name;
                if(bannersMst.imagePath!=null && bannersMst.imagePath!=" " && params.file.filename==null && params.file==""){
                    bannersMst.imagePath = bannersMst.imagePath
                }else if(params.file!=null && params.file.filename!=""){
                    bannersMst.imagePath =params.file.filename.replaceAll("\\s+", "")
                }
                bannersMst.bookId = bookId
                bannersMst.save(flush: true, failOnError: true)
                redirect(controller: "institute", action: "manageInstitutePage", params: [instituteId: instituteId])
            }
        } else {
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            if (file.empty) {
                flash.message = "File cannot be empty"
            } else {
                if(instituteId!=null && instituteId!=""){
                    BannersMst bannersMst = new BannersMst(imageName: params.name, siteId: getSiteId(request), instituteId: new Long(instituteId), bookId: bookId, page: page, imagePath: "",action:actionType)
                    bannersMst.save(failOnError: true, flush: true)
                    if(!file.empty) {
                        File uploadDir = new File("upload/instituteBanner/"+instituteId)
                        if (!uploadDir.exists()) uploadDir.mkdirs()

                        //creating directory to process images
                        File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                        if (!uploadDir1.exists()) uploadDir1.mkdirs()
                        String filename = file.originalFilename
                        filename = filename.replaceAll("\\s+", "")
                        BufferedImage image = ImageIO.read(file.getInputStream())

                        ByteArrayOutputStream baos = new ByteArrayOutputStream()
                        ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                        baos.flush()
                        byte[] scaledImageInByte = baos.toByteArray()
                        baos.close()

                        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)

                        //saving original image finally
                        file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
                        BannersMst.executeUpdate("update BannersMst set imagePath ='" + filename + "' where id=" + bannersMst.id)
                    }
                    redirect(controller: "institute", action: "manageInstitutePage", params: [instituteId: instituteId])

                }
            }
        }
        dataProviderService.getBannersForInstitute(new Long(instituteId))
    }

    @Transactional
    def showInstituteBannerImage(String fileName, String id,String instituteId) {
        if(fileName!=null&&!"null".equals(fileName)&&fileName.length()>0) {
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
            def file = new File("upload/instituteBanner/"+instituteId+"/"+fileName)
            if (file.exists()) {
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }   else render "";
        } else render "";
    }

    @Transactional
    def showInstituteGalleryImage(String fileName, String id,String instituteId) {
        if(fileName!=null&&!"null".equals(fileName)&&fileName.length()>0) {
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
            def file = new File("upload/instituteGallery/"+instituteId+"/"+fileName)
            if (file.exists()) {
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }   else render "";
        } else render "";
    }

    @Transactional
    def getInstituteBannerdetails(){
        def json
        List bannerList
        String instituteId=params.instituteId
        if(redisService.("bannerListInstitute_"+instituteId)==null) dataProviderService.getBannersForInstitute(new Long(instituteId))
        bannerList = new JsonSlurper().parseText(redisService.("bannerListInstitute_"+instituteId))
        json = ['status': bannerList? "OK" : "Nothing Present", 'banners': bannerList]
        render json as JSON
    }


    @Transactional
    def getInstituteGalleryImage(){
        def json
        List imageGalleryList
        String instituteId=params.instituteId
        if(redisService.("instituteGalleryImages_"+instituteId)==null) dataProviderService.getInstituteGalleryImages(new Long(instituteId))
        imageGalleryList = new JsonSlurper().parseText(redisService.("instituteGalleryImages_"+instituteId))
        json = ['status': imageGalleryList? "OK" : "Nothing Present", 'imageGalleryList': imageGalleryList]
        render json as JSON

    }


    @Transactional @Secured(['ROLE_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def deleteInstituteBannerById(){
        BannersMst bannerMst = BannersMst.get(new Long(params.id))
        bannerMst.delete(flush: true)
        dataProviderService.getBannersForInstitute(new Long(params.instituteId))
        def json = ['status' :"OK"]
        render json as JSON
    }


    @Transactional @Secured(['ROLE_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def deleteInstituteGalleryImageById(){
        InstituteGalleryDtl instituteGalleryDtl = InstituteGalleryDtl.get(new Long(params.id))
        instituteGalleryDtl.delete(flush: true)
        dataProviderService.getInstituteGalleryImages(new Long(params.instituteId))
        def json = ['status' :"OK"]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def uploadInstituteContent() {
        Integer instituteId=Integer.parseInt(params.instituteId)
        def file = request.getFile('upload')
        String resLink = "upload/institute/"+instituteId+"/extract/OEBPS/Images/"+file.originalFilename
        if(file!=null && !file.empty) {
            File  uploadDir =  new File(grailsApplication.config.grails.basedir.path+"upload/institute/"+instituteId+"/extract/OEBPS/Images/")
            if(!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path+resLink))
        }

        def json =  [
                "uploaded": 1,
                "fileName": file.originalFilename,
                "url": '/funlearn/downloadEpubImage?source='+resLink
        ]

        render json as JSON
    }


    def getSyllabusList(){
        String level = params.level
        if(redisService.("levelSyllabus_"+level.replaceAll("\\s+", ""))==null) dataProviderService.getSyllabusList(level)

        def json = [syllabus:redisService.("levelSyllabus_"+level.replaceAll("\\s+", ""))]
        render json as JSON

    }

    def getGradesList(){
        String level = params.level
        String syllabus = params.syllabus
        if(redisService.("levelSyllabusGrade_"+level.replaceAll("\\s+", "")+"_"+syllabus.replaceAll("\\s+", ""))==null) dataProviderService.getGradesList(level,syllabus)

        def json = [grades:redisService.("levelSyllabusGrade_"+level.replaceAll("\\s+", "")+"_"+syllabus.replaceAll("\\s+", ""))]
        render json as JSON

    }

    def getSubjectsList(){
        String level = params.level
        String syllabus = params.syllabus
        String grade = params.grade
        if(redisService.("levelSyllabusGradeSubject_"+level.replaceAll("\\s+", "")+"_"+syllabus.replaceAll("\\s+", "")+"_"+grade.replaceAll("\\s+", ""))==null) dataProviderService.getSubjectsList(level,syllabus,grade)

        def json = [subjects:redisService.("levelSyllabusGradeSubject_"+level.replaceAll("\\s+", "")+"_"+syllabus.replaceAll("\\s+", "")+"_"+grade.replaceAll("\\s+", ""))]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])
    def validateInstituteUrl(){
        def json =  instituteService.validateInstituteUrl(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getAllBatchesForInstitute(){
        String sql = "SELECT cbd.name,cbd.id  FROM wsuser.course_batches_dtl cbd" +
                " where cbd.status='active' and cbd.conducted_by="+params.instituteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List batches = results.collect { batch ->
            return [name: batch[0], batchId: batch[1]]
        }

        def json = [ status: batches?"OK":"not found", batches: batches]
        render json as JSON
    }

    def getLatestReleasedInstitutes(){
        String totalRequired = params.limit

        if(redisService.("latestReleasedInstitutes_"+totalRequired)==null) {
            instituteService.getLatestReleasedInstitutes(totalRequired)
        }

        def json = ['institutes':redisService.("latestReleasedInstitutes_"+totalRequired)]
        render json as JSON

    }

    @Transactional
    def recentInstitutes() {
        [commonTemplate: "true",title:"Recent Institutes - Wonderslate"]
    }

    @Transactional
    def ntse(){
        [title:'NTSE',commonTemplate: "true"]
    }

    @Secured(['ROLE_LIBRARY_USER_UPLOADER']) @Transactional
    def libraryUserUploader(){
       String sql ="select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd where im.site_id=1"+
                    " and im.publisher_id='"+session["userdetails"].publisherId+"'"+
                    " and cbd.conducted_by=im.id and cbd.name='Default' order by im.name";

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        def instituteId = results[0][0]


        [instituteId: instituteId,defaultBatchId:(CourseBatchesDtl.findByConductedByAndName(instituteId, "Default")).id]
    }

    @Secured(['ROLE_LIBRARY_USER_UPLOADER','ROLE_LIBRARY_ADMIN']) @Transactional
    def createUserAndAddToLibrary() {
        Integer siteId = session["siteId"]

        //first check the user exists
        String username = "" + siteId + "_" + params.mobile
        User user = User.findByUsername(username)
        if (user == null) {
            //create the user
            String name = params.name
            String password = params.mobile
            String mobile = params.mobile
            String email = params.email
            String pincode = params.pincode
            String city = params.city
            WinGenerator winGenerator = new WinGenerator()
            winGenerator.save(failOnError: true)
            user = new User(username: username, name: name, password: password, mobile: mobile, email: email, win: winGenerator.id, siteId: siteId, pincode: pincode, city: city)
            user.save(failOnError: true, flush: true)
            //add appropriate roles
            Role role = Role.findByAuthority("ROLE_USER")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_ADD")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_UPLOAD")
            UserRole.create(user, role, true)
        }

        // if the institute present then add the user to institute if he doesn't exist
        if (params.instituteId != null && !"".equals(params.instituteId) && !"null".equals(params.instituteId)) {
            Integer defaultBatchId = (CourseBatchesDtl.findByConductedByAndName(new Integer(params.instituteId), "Default")).id
            instituteService.addUserToBatch(username, defaultBatchId, null, null, null, siteId)
            Date validityDate = null
            DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
            if (params.validityDate != null && params.validityDate != "") {
                validityDate = df.parse(params.validityDate)
                BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(username,defaultBatchId)
                if(batchUserDtl!=null){
                    batchUserDtl.validityDate = validityDate
                    batchUserDtl.save(failOnError: true, flush: true)
                }
                if(user.email!=null && user.email!="") {
                    SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                    String email = user.email
                    String subjectText = "Welcome to " + siteMst.clientName
                    String body = "Your library account is created.\n Your library validity is till " + validityDate + " . \n."+
                            "You can access your digital library at https://"+siteMst.siteDomainName+"\n"
                            "Login Id: "+params.mobile+"\n Password: "+params.mobile
                    String fromEmail
                    if(siteMst.fromEmail!=null&&!"".equals(siteMst.fromEmail))
                        fromEmail = siteMst.fromEmail
                    else
                        fromEmail = "Wonderslate <<EMAIL>>"
                    if ("<EMAIL>" != email) {
                        mailService.sendMail {
                            async true
                            to email
                            from fromEmail
                            subject subjectText
                            text body
                        }
                    }
                }

            }

        }

        def json = ["user":user.mobile]
        render json as JSON
    }

    @Transactional
    @Secured(['ROLE_MASTER_LIBRARY_ADMIN','ROLE_WS_CONTENT_ADMIN'])

    def copyBooks(){
        String[] sourceInstitutes = params.sourceInstitutes.split(",")
        Integer targetDefaultBatchId = (CourseBatchesDtl.findByConductedByAndName(new Integer(params.targetInstituteId),"Default")).id
        Integer defaultBatchId

        for(int i=0;i<sourceInstitutes.length;i++){
             defaultBatchId = (CourseBatchesDtl.findByConductedByAndName(new Integer(sourceInstitutes[i]),"Default")).id
             instituteService.copyBooks(defaultBatchId,targetDefaultBatchId)
        }

        def json = ["status":"Books added from "+sourceInstitutes.length+" sources."]
        render json as JSON
    }

    def instituteProduct(){

    }

    def createSecretKey(){
        boolean keyExists = true
        String secretKey=""


        while(keyExists){
            def chars = (('0'..'9') + ('A'..'Z') + ('a'..'z')).flatten()
            def random = new SecureRandom()
            secretKey = (1..12).collect { chars[random.nextInt(chars.size())] }.join()
            InstituteMst instituteMst = InstituteMst.findBySecretKey(secretKey)
            if(instituteMst==null) keyExists=false
        }
        return secretKey
    }


}

