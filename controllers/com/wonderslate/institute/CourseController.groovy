package com.wonderslate.institute

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.log.Quizrecorder
import com.wonderslate.log.Quizrecorderdtl
import com.wonderslate.log.UserChapterDtl
import com.wonderslate.log.UserResourceDtl
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional
import groovy.json.JsonSlurper

class CourseController {
    UserManagementService userManagementService
    SpringSecurityService springSecurityService
    def redisService
    DataProviderService dataProviderService

    //need to add control parameters

    def index() { }

    def getActiveCourses(){
        Integer siteId = getSiteId(request)
        println("the siteId in active courses is="+siteId)
        if (redisService.("getActiveCourses_"+siteId) == null) {
            dataProviderService.getActiveCourses(siteId);
        }
        List courses = new JsonSlurper().parseText(redisService.("getActiveCourses_"+siteId));
        def json = [
                'courses':courses,
                'status' : courses ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    def  Integer getSiteId(request){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(params.siteId!=null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    def assessmentBookId(){
        if(redisService.("assessmentBookId")==null){
            dataProviderService.getAssessmentBookId()
        }
        def json = ["bookId":redisService.("assessmentBookId")]
        render json as JSON
    }

    def getAssessmentId(){
        if (redisService.("assessmentChapter_"+params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId));
        }
        Long chapterId = new Long(redisService.("assessmentChapter_"+params.bookId))
        if(chapterId!=null){
            ResourceDtl resourceDtl = ResourceDtl.findByChapterId(chapterId)
            def json = ['resId':resourceDtl.id, 'quizId':resourceDtl.resLink]
            render json as JSON
        }
    }

    @Transactional
    def isAssessmentTaken(){
        if (redisService.("assessmentChapter_"+params.bookId) == null) {
            dataProviderService.getChaptersList(new Long(params.bookId));
        }
        Long chapterId = new Long(redisService.("assessmentChapter_"+params.bookId))
        if(chapterId!=null){
            ResourceDtl resourceDtl = ResourceDtl.findByChapterId(chapterId)
            Quizrecorder quizrecorder = Quizrecorder.findByQuizidAndUsername(resourceDtl.id,springSecurityService.currentUser.username)

            def json = ['assessmentTaken':quizrecorder!=null?"Yes":"No"]
            render json as JSON
        }
    }

    @Transactional
    def getCourseChapters(){
        if(redisService.("chapters_"+params.bookId)==null){
            dataProviderService.getChaptersList(new  Long(params.bookId),"sortOrder");
        }
        List chapters = new JsonSlurper().parseText(redisService.("chapters_"+params.bookId));
        List userChapters = UserChapterDtl.findAllByUsername(springSecurityService.currentUser.username)
        def json = [
                'chapters':chapters,
                'userChapters':userChapters,
                'status' : chapters ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    @Transactional
    def getChapterResources(){
        if(redisService.("resources_"+params.chapterId)==null){
            dataProviderService.getChapterResources(new  Long(params.chapterId),"sortOrder");
        }
        List resources = new JsonSlurper().parseText(redisService.("resources_"+params.chapterId));
        List userResources = UserResourceDtl.findAllByUsername(springSecurityService.currentUser.username)
        def json = [
                'chapters':resources,
                'userResources':userResources,
                'status' : resources ? "OK" : "Nothing present"
        ]

        render json as JSON

    }

   @Transactional
   def updateChapterStatus(){
       UserChapterDtl userChapterDtl = UserChapterDtl.findByChapterIdAndUsername(new Long(params.chapterId),springSecurityService.currentUser.username)
       if(userChapterDtl==null) userChapterDtl = new UserChapterDtl(chapterId: new Long(params.chapterId), username:springSecurityService.currentUser.username)
       userChapterDtl.status = params.status
       if("COMPLETED".equals(params.status)) userChapterDtl.dateCompleted = new Date()
       userChapterDtl.save(failOnError: true, flush: true)
   }

    @Transactional
    def updateResourceStatus(){
        UserResourceDtl userResourceDtl = UserResourceDtl.findByResIdAndUsername(new Long(params.resId),springSecurityService.currentUser.username)
        if(userResourceDtl==null) userResourceDtl = new UserResourceDtl(resId:  new Long(params.resId),username:  springSecurityService.currentUser.username)
        userResourceDtl.status = params.status
        if("COMPLETED".equals(params.status)) userResourceDtl.dateCompleted = new Date()
        userResourceDtl.save(failOnError: true, flush: true)
    }

    def initialAssessment(){
       def bookId
        def resId
        def quizLink
        if(redisService.("assessmentBookId")==null){
            dataProviderService.getAssessmentBookId()
        }
        bookId = redisService.("assessmentBookId")

        if (redisService.("assessmentChapter_"+bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId));
        }
        Long chapterId = new Long(redisService.("assessmentChapter_"+bookId))
        if(chapterId!=null){
            ResourceDtl resourceDtl = ResourceDtl.findByChapterId(chapterId)
            resId = resourceDtl.id
            quizLink = resourceDtl.resLink
        }
        [resId:resId,resLink:quizLink]

    }
}
