package com.wonderslate.institute

import com.wonderslate.cache.DataProviderService
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserRole
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import com.wonderslate.publiclibrary.ReferenceSection
import com.wonderslate.data.Prompts

class InstManagerController {

    InstManagerService instManagerService
    DataProviderService dataProviderService
    def springSecurityService
    grails.core.GrailsApplication grailsApplication

    private static final log = org.apache.commons.logging.LogFactory.getLog(this)

    /**
     * Course Management Actions
     */

    // Create a new course

    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def createCourse() {
        if("true".equals(params.submit)) {
            def name = params.name
            def gradeType = params.gradeType
            def gradeStart = params.int('gradeStart')
            def gradeEnd = params.int('gradeEnd')
            def customGrades = params.customGrades

            InstituteCourseMst course = instManagerService.createCourse(name, gradeType, gradeStart, gradeEnd, customGrades)
            if (course?.id) {
                flash.message = "Course created successfully."
            } else {
                flash.message = "Failed to create course."
            }
            redirect(action: "listCourses")
        }
    }

    // Update an existing course
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def updateCourse() {
        Long id = params.long('id')
        if (!id) {
            flash.message = "Course ID is required."
            redirect(action: "listCourses")
            return
        }

        Map updateParams = params.subMap('name', 'gradeType', 'gradeStart', 'gradeEnd', 'customGrades')

        // Convert gradeStart and gradeEnd to Integer if they are provided
        updateParams.gradeStart = params.gradeStart ? params.int('gradeStart') : null
        updateParams.gradeEnd = params.gradeEnd ? params.int('gradeEnd') : null

        // Update the course using the service
        InstituteCourseMst course = instManagerService.updateCourse(id, updateParams)
        if (course) {
            flash.message = "Course updated successfully."
        } else {
            flash.message = "Failed to update course."
        }
        redirect(action: "listCourses")
    }

    // Delete a course
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def deleteCourse() {
        Long id = params.long('id')
        if (instManagerService.deleteCourse(id)) {
            flash.message = "Course deleted successfully."
        } else {
            flash.message = "Failed to delete course."
        }
        redirect(action: "listCourses")
    }

    // Get course details
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def getCourse() {
        Long id = params.long('id')
        InstituteCourseMst course = instManagerService.getCourse(id)
        if (course) {
            [course: course]
        } else {
            flash.message = "Course not found."
            redirect(action: "listCourses")
        }
    }

    // List all courses
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def listCourses() {
        List<InstituteCourseMst> courses = instManagerService.listCourses()
        [courses: courses]
    }

    /**
     * Batch (Section) Management Actions
     */

    // Create a new batch
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def createBatch() {
        if(params.conductedBy!=null) {
            Long courseId = params.long('courseId')
            Long conductedBy = params.long('conductedBy')
            String name = params.name
            String grade = params.grade
            Date startDate = params.date('startDate', 'yyyy-MM-dd')
            Date endDate = params.date('endDate', 'yyyy-MM-dd')
            String status = params.status
            Integer groupId = params.int('groupId')
            String syllabus = params.syllabus
            CourseBatchesDtl batch = instManagerService.createBatch(courseId, conductedBy, name, grade, startDate, endDate, status, groupId, syllabus)
            if (batch?.id) {
                flash.message = "Batch created successfully."
            } else {
                flash.message = "Failed to create batch."
            }
            redirect(action: "listBatches", params: [instituteId: conductedBy])
        }else{
            Long instituteId = params.long('instituteId')

            String instituteName = InstituteMst.get(instituteId)?.name ?: 'Unknown Institute'
            List courses = InstituteCourseMst.findAll()
            [instituteId: instituteId, instituteName: instituteName, courses: courses]
        }
    }

    // Update an existing batch
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def updateBatch() {
        Long id = params.long('id')
        Map updateParams = params.subMap('courseId', 'conductedBy', 'name', 'grade', 'startDate', 'endDate', 'status', 'groupId', 'syllabus')
        CourseBatchesDtl batch = instManagerService.updateBatch(id, updateParams)
        if (batch) {
            flash.message = "Batch updated successfully."
        } else {
            flash.message = "Failed to update batch."
        }
        redirect(action: "listBatches", params: [instituteId: batch?.conductedBy])
    }

    // Delete a batch
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def deleteBatch() {
        Long id = params.long('id')
        CourseBatchesDtl batch = instManagerService.getBatch(id)
        if (instManagerService.deleteBatch(id)) {
            flash.message = "Batch deleted successfully."
        } else {
            flash.message = "Failed to delete batch."
        }
        redirect(action: "listBatches", params: [instituteId: batch?.conductedBy])
    }

    // Get batch details
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def getBatch() {
        Long id = params.long('id')
        if (!id) {
            flash.message = "Batch ID is required."
            redirect(action: "listBatches", params: [instituteId: params.instituteId])
            return
        }

        CourseBatchesDtl batch = instManagerService.getBatch(id)
        if (!batch) {
            flash.message = "Batch not found."
            redirect(action: "listBatches", params: [instituteId: params.instituteId])
            return
        }

        // Fetch the course name
        String courseName = InstituteCourseMst.get(batch.courseId)?.name ?: 'Unknown Course'

        // Prepare batch data
        Map batchData = [
                id         : batch.id,
                name       : batch.name,
                grade      : batch.grade,
                courseName : courseName,
                conductedBy: batch.conductedBy
        ]

        // Fetch user in the batch
        List<BatchUserDtl> allUsers = BatchUserDtl.findAllByBatchId(id)
        User user
        List<Map> students = allUsers.collect { student ->
            user = dataProviderService.getUserMst(student.username)
            [
                    id         : student.id,
                    username   : student.username,
                    admissionNo: student.admissionNo,
                    name       : user?.name ?: 'Unknown User',
                    userType : student.userType==null?"Student":student.userType
            ]
        }

        // Fetch instructors in the batch
        List<BatchUserDtl> instructorUsers = BatchUserDtl.findAllByBatchIdAndInstructor(id, 'true')
        List<Map> instructors = instructorUsers.collect { instructor ->
            user = dataProviderService.getUserMst(instructor.username)
            [
                    id      : instructor.id,
                    username: instructor.username,
                    name    : user?.name ?: 'Unknown User'
            ]
        }

        // Fetch books assigned to the batch
        List<BooksBatchDtl> booksBatchList = BooksBatchDtl.findAllByBatchId(id)
        List<Map> books = booksBatchList.collect { bookBatch ->
            String bookTitle = dataProviderService.getBooksMst(bookBatch.bookId)?.title ?: 'Unknown Book'
            [
                    id         : bookBatch.id,
                    bookTitle  : bookTitle,
                    dateCreated: bookBatch.dateCreated
            ]
        }

        // Pass all data to the GSP
        [batch: batchData, students: students, instructors: instructors, books: books]
    }

    // List all batches for an institute
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def listBatches() {
        Long instituteId = params.long('instituteId')
        List<CourseBatchesDtl> batches = instManagerService.listBatches(instituteId)
        String instituteName = InstituteMst.get(instituteId)?.name ?: 'Unknown Institute'

        // Prepare data for the GSP
        List<Map> batchList = batches.collect { batch ->
            def courseName = InstituteCourseMst.get(batch.courseId)?.name ?: 'Unknown Course'
            [
                    id        : batch.id,
                    name      : "Default".equals(batch.name)?"Institution":batch.name,
                    courseName: courseName,
                    grade     : batch.grade
            ]
        }

        [batches: batchList, instituteId: instituteId, instituteName: instituteName]
    }

    /**
     * User Assignment Actions
     */

    // Assign a user to a batch
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def assignUserToBatch() {
        if("submit".equals(params.mode)) {
            Long batchId = params.long('batchId')
            String username = params.username
            String userType = params.userType // 'true' or 'false'
            String classTeacher = params.classTeacher
            String admissionNo = params.admissionNo
            Date validityDate = params.date('validityDate', 'yyyy-MM-dd')

             BatchUserDtl batchUser = instManagerService.assignUserToBatch(batchId, username, userType, classTeacher, admissionNo, validityDate)
            //check batch is default batch or not. if not add user to default batch
            if(batchUser){
                CourseBatchesDtl batch = CourseBatchesDtl.get(batchId)

                if(batchUser.batchId!=instManagerService.getDefaultBatchId(batch.conductedBy)){
                    instManagerService.assignUserToBatch(instManagerService.getDefaultBatchId(batch.conductedBy), username, userType, classTeacher, admissionNo, validityDate)
                }
            }
            def json = [username:username,status:"success"]
            render json as JSON
        }else{
            Long batchId = params.long('batchId')
            CourseBatchesDtl batch = CourseBatchesDtl.get(batchId)
            Long defaultBatchId = instManagerService.getDefaultBatchId(batch.conductedBy)
            [batchName: batch.name, batchId: batchId, defaultBatchId: defaultBatchId]
        }
    }


    // Remove a user from a batch
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def removeUserFromBatch() {
        Long id = params.long('id')
        BatchUserDtl batchUser = BatchUserDtl.get(id)
        if (instManagerService.removeUserFromBatch(id)) {
            flash.message = "User removed from batch successfully."
        } else {
            flash.message = "Failed to remove user from batch."
        }
        redirect(action: "getBatch", params: [id: batchUser?.batchId])
    }

    /**
     * List Users in Batch (Manage Users Page)
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def listUsersInBatch() {
        Integer max = params.int('max') ?: 10
        Integer offset = params.int('offset') ?: 0
        String search = params.search?.trim()
        String userType = params.userType
        Long batchId = params.long('batchId')
        Long instituteId = params.long('instituteId')

        // Determine batchId
        if (!batchId) {
            if (instituteId) {
                // Get default batch for the institute
                batchId = instManagerService.getDefaultBatchId(instituteId)
                if (!batchId) {
                    flash.message = "Default batch not found for the institute."
                    redirect(action: "listInstitutes")
                    return
                }
            } else {
                flash.message = "Batch ID or Institute ID is required."
                redirect(action: "listInstitutes")
                return
            }
        }

        // Get siteId from session
        Integer siteId = new Integer("" + session['siteId'])

        // Fetch users with pagination, search, and filter
        Map result = instManagerService.getUsersList(batchId, max, offset, search, userType, siteId)
        // get all batches for the institute
        List<CourseBatchesDtl> batches = instManagerService.listBatches(instituteId)

        //remove the default batch from the list
        batches.removeIf { it.name == "Default" }


        [users: result.users, totalCount: result.totalCount, max: max, offset: offset, search: search, userType: userType, params: params, batchId: batchId, batches: batches]
    }

    /**
     * Autocomplete Suggestions for User Search
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def searchUserSuggestions() {
        String term = params.term?.trim()
        Long batchId = params.long('batchId')
        Integer siteId = new Integer(""+session['siteId'])
        Boolean onlyBatchUsers = params.boolean('onlyBatchUsers') ?: false
        if (!term || !batchId || !siteId) {
            render([])
            return
        }

        def suggestions = instManagerService.getUserSuggestions(batchId, term, siteId,onlyBatchUsers)
        render suggestions as JSON
    }
    /**
     * Set User as Instructor Action
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def setAsInstructor() {
        String username = params.username
        Long batchId = params.long('batchId')
        if (!username || !batchId) {
            flash.message = "Username and Batch ID are required."
            redirect(action: "listUsersInBatch", params: [batchId: batchId])
            return
        }

        // Implementation to set user as instructor
        boolean success = instManagerService.setUserAsInstructor(username, batchId)
        if (success) {
            flash.message = "User set as instructor successfully."
        } else {
            flash.message = "Failed to set user as instructor."
        }
        redirect(action: "listUsersInBatch", params: [batchId: batchId])
    }

    /**
     * Book Assignment Actions
     */

    // Assign a book to a batch
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def assignBookToBatch() {
        Long batchId = params.long('batchId')
        Long instituteId = CourseBatchesDtl.get(batchId)?.conductedBy
        if (!batchId ) {
            flash.message = "Batch ID and Institute ID are required."
            redirect(action: "listBatches", params: [instituteId: instituteId])
            return
        }

        // Fetch batch details
        Map batchData = instManagerService.getBatchData(batchId)
        if (!batchData) {
            flash.message = "Batch not found."
            redirect(action: "listBatches", params: [instituteId: instituteId])
            return
        }

        // Initial parameters
        Integer max = params.int('max') ?: 10
        Integer offset = params.int('offset') ?: 0
        String search = params.search?.trim()

        // Fetch books to display
        Map booksResult = instManagerService.getAvailableBooksForBatch(batchId, instituteId, max, offset, search)

        [batch       : batchData,
         books       : booksResult.books,
         totalCount  : booksResult.totalCount,
         max         : max,
         offset      : offset,
         search      : search,
         instituteId : instituteId,
         params      : params]
    }



    /**
     * List Books in Batch Action
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def listBooksInBatch() {
        Long batchId = instManagerService.getDefaultBatchId(new Long(params.instituteId))
        InstituteMst institute = InstituteMst.get(params.instituteId)
         if (!batchId) {
            flash.message = "Batch ID is required."
            redirect(action: "listBatches") // Redirect to a relevant action
            return
        }

        Long max = params.long('max') ?: 10L
        Long offset = params.long('offset') ?: 0L
        String search = params.search?.trim()

        // Fetch books in the batch with pagination and search
        Map result = instManagerService.getBooksInBatch(batchId, max, offset, search)

        [books      : result.books,
         totalCount : result.totalCount,
         max        : max,
         offset     : offset,
         search     : search,
         batchId    : batchId,
         params     : params,
        instituteName: institute.name,
         instituteId: params.instituteId]
    }

    /**
     * Search Books in Batch (AJAX)
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def searchBooksInBatch() {
        Long batchId = params.long('batchId')
        if (!batchId) {
            render(status: 400, text: 'Batch ID is required.')
            return
        }

        Long max = params.long('max') ?: 10L
        Long offset = params.long('offset') ?: 0L
        String search = params.search?.trim()

        Map result = instManagerService.getBooksInBatch(batchId, max, offset, search)

        // Return the books and totalCount as JSON
        render([books: result.books, totalCount: result.totalCount] as JSON)
    }

    // Institute Admin Dashboard
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def adminDashboard() {
        def defaultBatchId
        Long instituteId = params.long('instituteId')
        if (!instituteId) {
            flash.message = "Institute ID is required."
            redirect(action: "listInstitutes")
            return
        }
        User user = session["userdetails"]
        if(user.authorities.any {
            it.authority == "ROLE_INSTITUTE_MANAGER"
        }){
            Long defaultBatchIdTemp = instManagerService.getDefaultBatchId(instituteId)
            BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchIdAndUserType(user.username, defaultBatchIdTemp,"Manager")
            if(batchUserDtl==null){
                flash.message = "Access Denied"
                redirect(controller:"privatelabel", action: "admin")
                return
            }
        }
        Map dashboardData = instManagerService.getDashboardData(instituteId)

        // Fetch the institute name
        String instituteName = InstituteMst.get(instituteId)?.name ?: 'Unknown Institute'

        // Prepare data for recent batches
        dashboardData.recentBatches = dashboardData.recentBatches.collect { batch ->
            def courseName = InstituteCourseMst.get(batch.courseId)?.name ?: 'Unknown Course'
            if("Default".equals(batch.name)){
                defaultBatchId = batch.id
            }
            [
                    name       : "Default".equals(batch.name)?"Institution":batch.name,
                    courseName : courseName,
                    grade      : batch.grade
            ]
        }

        // Prepare data for recent users
        dashboardData.recentUsers = dashboardData.recentUsers.collect { recentUser ->
            def batch = CourseBatchesDtl.get(recentUser.batchId)
            def batchName = batch?.name ?: 'Unknown Batch'
            [
                    username : recentUser.username,
                    userType: recentUser.userType==null?"Student":recentUser.userType,
                    batchName: "Default".equals(batchName)?"Institution":batchName,
                    name: dataProviderService.getUserMst(recentUser.username)?.name ?: 'Unknown User'
            ]
        }

        // Prepare data for recent books assigned
        dashboardData.recentBooksAssigned = dashboardData.recentBooksAssigned.collect { bookBatch ->
            def batchName = CourseBatchesDtl.get(bookBatch.batchId)?.name ?: 'Unknown Batch'
            def bookTitle = dataProviderService.getBooksMst(bookBatch.bookId)?.title ?: 'Unknown Book'
            [
                    batchName: "Default".equals(batchName)?"Institution":batchName,
                    bookTitle  : bookTitle
            ]
        }

        boolean iBookGPTSiteAdmin = false
        //iBookGPT Site admin
        if(user.authorities.any {
            it.authority == "ROLE_IBOOKGPT_SITE_ADMIN"
        }) iBookGPTSiteAdmin = true
        // Pass all necessary data to the GSP
        [dashboardData: dashboardData, instituteId: instituteId, instituteName: instituteName, defaultBatchId: defaultBatchId, iBookGPTSiteAdmin: iBookGPTSiteAdmin]
    }

    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def editCourse() {
        Long id = params.long('id')
        if (!id) {
            flash.message = "Course ID is required."
            redirect(action: "listCourses")
            return
        }

        // Fetch the course using the service
        InstituteCourseMst course = instManagerService.getCourse(id)
        if (!course) {
            flash.message = "Course not found."
            redirect(action: "listCourses")
            return
        }

        // Prepare data for the GSP
        Map courseData = [
                id          : course.id,
                name        : course.name,
                gradeType   : course.gradeType,
                gradeStart  : course.gradeStart,
                gradeEnd    : course.gradeEnd,
                customGrades: course.customGrades
        ]

        // Pass the data to the GSP
        [course: courseData]
    }

    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def getGradesForCourse() {
        Long courseId = params.long('courseId')
        String selectedGrade = params.selectedGrade // Used in editBatch
        if (!courseId) {
            render(status: 400, text: 'Course ID is required.')
            return
        }

        // Fetch the course using the service
        InstituteCourseMst course = instManagerService.getCourse(courseId)
        if (!course) {
            render(status: 404, text: 'Course not found.')
            return
        }

        // Generate the grade options based on the gradeType
        List<Map> gradeOptions = []
        if (course.gradeType == 'Year' || course.gradeType == 'Semester') {
            if (course.gradeStart && course.gradeEnd) {
                (course.gradeStart..course.gradeEnd).each { grade ->
                    gradeOptions << [value: grade.toString(), text: "Grade ${grade}", selected: (selectedGrade == grade.toString())]
                }
            }
        } else if (course.gradeType == 'Custom') {
            if (course.customGrades) {
                def customGrades = course.customGrades.split(',').collect { it.trim() }
                customGrades.each { grade ->
                    gradeOptions << [value: grade, text: grade, selected: (selectedGrade == grade)]
                }
            }
        }

        // Render the options as HTML
        String optionsHtml = '<option value="">Select Grade</option>'
        gradeOptions.each { option ->
            String selectedAttr = option.selected ? ' selected' : ''
            optionsHtml += "<option value=\"${option.value}\"${selectedAttr}>${option.text}</option>"
        }

        render optionsHtml
    }

    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def editBatch() {
        Long id = params.long('id')
        if (!id) {
            flash.message = "Batch ID is required."
            redirect(action: "listBatches")
            return
        }

        CourseBatchesDtl batch = instManagerService.getBatch(id)
        if (!batch) {
            flash.message = "Batch not found."
            redirect(action: "listBatches")
            return
        }

        Map batchData = [
                id         : batch.id,
                name       : batch.name,
                courseId   : batch.courseId,
                conductedBy: batch.conductedBy,
                grade      : batch.grade
        ]

        String instituteName = InstituteMst.get(batch.conductedBy)?.name ?: 'Unknown Institute'
        List courses = InstituteCourseMst.findAll([sort: 'name', order: 'asc'])
        [batch: batchData, instituteName: instituteName, courses: courses]
    }

   @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def addUser(){
       User user = instManagerService.createUser(params,session)
       if("Manager".equals(params.userType)){
           Role role = Role.findByAuthority("ROLE_INSTITUTE_MANAGER")
           UserRole.create(user, role, true)
       }
       String instructor = "Instructor".equals(params.userType)?"true":null
       instManagerService.addUserToBatch(user.username,params.userType,params.admissionNo,new Integer(params.defaultBatchId))
       if(params.batchId&&!"Manager".equals(params.userType)) instManagerService.addUserToBatch(user.username,params.userType,params.admissionNo,new Integer(params.batchId))
       def json = [username:user.username.split('_')[1],status:"success"]
       render json as JSON
   }

    def downloadSampleFile(){
        response.contentType = 'application/vnd.ms-excel'
        response.setHeader("Content-disposition", "attachment; filename=InstituteUserUploadTemplate.xlsx")
        def file = new File(grailsApplication.config.grails.basedir.path + "/supload/templates/InstituteUserUploadTemplate.xlsx")
        file.withInputStream { response.outputStream << it }
    }

    /**
     * Edit User Action
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def editUser() {
        Long userId = params.long('id')
        Long siteId = session['siteId'] as Long
        if (!userId || !siteId) {
            flash.message = "User ID and Site ID are required."
            redirect(action: "listUsersInBatch", params: [batchId: params.batchId])
            return
        }

        // Fetch the user using the service
        Map userData = instManagerService.getUserData(userId, siteId)
        if (!userData) {
            flash.message = "User not found or access denied."
            redirect(action: "listUsersInBatch", params: [batchId: params.batchId])
            return
        }

        [user: userData, instituteId: params.instituteId]
    }

    /**
     * Update User Action
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def updateUser() {
        Long userId = params.long('id')
        Long siteId = session['siteId'] as Long
        if (!userId || !siteId) {
           flash.message = "User ID and Site ID are required."
            redirect(action: "listUsersInBatch", params: [instituteId: params.instituteId])
            return
        }


        Map updateParams = params.subMap('name', 'email', 'mobileNumber')
        updateParams.id = userId

        // Update the user using the service
        boolean success = instManagerService.updateUser(updateParams, siteId)
        if (success) {
            flash.message = "User updated successfully."
        } else {
            flash.message = "Failed to update user."
        }
        redirect(action: "listUsersInBatch", params: [instituteId: params.instituteId])
    }

    /**
     * Search Books for Batch (AJAX)
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def searchBooksForBatch() {
        Long batchId = params.long('batchId')
        Long instituteId = params.long('instituteId')
        if (!batchId || !instituteId) {
            render(status: 400, text: 'Batch ID and Institute ID are required.')
            return
        }

        String search = params.search?.trim()

        // Fetch books to display
        Map booksResult = instManagerService.getAvailableBooksForBatch(batchId, instituteId, 50, 0, search)

        // Return books as JSON
        render booksResult.books as JSON
    }

    /**
     * Save Assigned Books Action
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def saveAssignedBooks() {
        Long batchId = params.long('batchId')
        Long instituteId = params.long('instituteId')
        if (!batchId || !instituteId) {
            flash.message = "Batch ID and Institute ID are required."
            redirect(action: "listBatches", params: [instituteId: instituteId])
            return
        }

        List<Long> bookIds = params.list('bookIds')*.toLong()

        boolean success = instManagerService.assignBooksToBatch(batchId, bookIds)
        if (success) {
            flash.message = "Books assigned successfully."
        } else {
            flash.message = "Failed to assign books."
        }
        redirect(action: "getBatch", params: [id: batchId])
    }

    /**
     * List Institutes Action
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def listInstitutes() {
        Integer max = params.long('max') ?: 10
        Integer offset = params.long('offset') ?: 0
        String search = params.search?.trim()
        String status = params.status ?: 'active' // Default to 'active'

        // Fetch institutes with pagination, search, and filter
        Map result = instManagerService.getInstitutesList(max, offset, search, status,new Integer(""+session['siteId']))

        [institutes: result.institutes, totalCount: result.totalCount, max: max, offset: offset, search: search, status: status, params: params]
    }

    /**
     * Search Institutes (AJAX)
     */
    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def searchInstitutes() {
        Integer max = params.long('max') ?: 10
        Integer offset = params.long('offset') ?: 0
        String search = params.search?.trim()
        String status = params.status ?: 'active' // Default to 'active'

        Map result = instManagerService.getInstitutesList(max, offset, search, status,new Integer(""+session['siteId']))

        // Return the institutes and totalCount as JSON
        render([institutes: result.institutes, totalCount: result.totalCount] as JSON)
    }

    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN'])
    def addInstitute(){
        List referenceSections = ReferenceSection.list()
        if(params.id!=null){
            InstituteMst instituteInstance = InstituteMst.get(params.id)
            //get default batch for the institute
            Long defaultBatchId = instManagerService.getDefaultBatchId(instituteInstance.id)
            CourseBatchesDtl defaultBatch = CourseBatchesDtl.get(defaultBatchId)
            //need to add startDate, endDate, status to instituteInstance
            [instituteInstance:instituteInstance,isEdit:true,defaultBatch:defaultBatch,referenceSections:referenceSections]
        }else{
            [referenceSections:referenceSections]
        }

    }

    /**
     * Manage Institute Prompts Action
     * Allows administrators to select and arrange prompts for an institute
     */
    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def manageInstitutePrompts() {
        Long instituteId = params.long('instituteId')
        if (!instituteId) {
            flash.message = "Institute ID is required."
            redirect(action: "listInstitutes")
            return
        }

        // Get institute details
        InstituteMst institute = InstituteMst.get(instituteId)
        if (!institute) {
            flash.message = "Institute not found."
            redirect(action: "listInstitutes")
            return
        }

        // Load default prompts from Prompts table where isDefault='Yes'
        def defaultPrompts = Prompts.findAllByIsDefault('Yes', [sort: 'sortOrder', order: 'asc'])

        // Since prompts don't have userType, we'll use the same prompts for both students and teachers
        def allPrompts = defaultPrompts

        // Get selected prompts for this institute
        def selectedStudentPrompts = InstitutePromptMst.findAllByInstituteIdAndUserType(instituteId, 'Student', [sort: 'sortOrder', order: 'asc'])
        def selectedTeacherPrompts = InstitutePromptMst.findAllByInstituteIdAndUserType(instituteId, 'Teacher', [sort: 'sortOrder', order: 'asc'])

        println("***** Found ${selectedStudentPrompts.size()} student prompts and ${selectedTeacherPrompts.size()} teacher prompts for institute ${instituteId}")

        // Get the prompt types of selected student prompts
        def selectedStudentPromptTypes = selectedStudentPrompts.collect { it.promptType }

        // Filter available prompts to exclude already selected ones for students
        def availableStudentPrompts = allPrompts.findAll { !selectedStudentPromptTypes.contains(it.promptType) }

        // For teachers, we'll load them via AJAX
        def availableTeacherPrompts = []

        [institute: institute,
         availableStudentPrompts: availableStudentPrompts,
         availableTeacherPrompts: availableTeacherPrompts,
         selectedStudentPrompts: selectedStudentPrompts,
         selectedTeacherPrompts: selectedTeacherPrompts]
    }

    /**
     * Get Teacher Data Action
     * Returns teacher prompts data for an institute
     */
    @Transactional @Secured(['ROLE_GPT_MANAGER', 'ROLE_IBOOKGPT_SITE_ADMIN', 'ROLE_INSTITUTE_MANAGER'])
    def getTeacherData() {
        Long instituteId = params.long('instituteId')
        if (!instituteId) {
            def json = [status: 'error', message: 'Institute ID is required.']
            render json as JSON
            return
        }

        try {
            // Get institute details
            InstituteMst institute = InstituteMst.get(instituteId)
            if (!institute) {
                def json = [status: 'error', message: 'Institute not found.']
                render json as JSON
                return
            }

            // Get available prompts for this institute
            def defaultPrompts = Prompts.findAllByIsDefault('Yes', [sort: 'sortOrder', order: 'asc'])

            // Get selected prompts for this institute
            def selectedPrompts = InstitutePromptMst.findAllByInstituteIdAndUserType(instituteId, 'Teacher', [sort: 'sortOrder', order: 'asc'])
            println("***** Found ${selectedPrompts.size()} selected teacher prompts for institute ${instituteId}")

            // Get the prompt types of selected prompts
            def selectedPromptTypes = selectedPrompts.collect { it.promptType }

            // Filter available prompts to exclude already selected ones
            def availablePrompts = defaultPrompts.findAll { !selectedPromptTypes.contains(it.promptType) }

            // Convert to the format expected by the frontend
            List teacherPrompts = availablePrompts.collect { prompt ->
                [promptType: prompt.promptType, promptLabel: prompt.promptLabel]
            }

            // Convert selected prompts to the format expected by the frontend
            List selectedTeacherPrompts = []
            selectedPrompts.each { promptMst ->
                def prompt = Prompts.findByPromptType(promptMst.promptType)
                if (prompt) {
                    selectedTeacherPrompts << [promptType: promptMst.promptType, promptLabel: prompt.promptLabel]
                }
            }

            // No need for sample data anymore
            println("***** Available teacher prompts: ${teacherPrompts.size()}, Selected teacher prompts: ${selectedTeacherPrompts.size()}")

            def json = [status: 'success', data: teacherPrompts, selectedData: selectedTeacherPrompts]
            render json as JSON
        } catch (Exception e) {
            log.error("Error fetching teacher data: ${e.message}", e)
            def json = [status: 'error', message: "An error occurred: ${e.message}"]
            render json as JSON
        }
    }

    /**
     * Save Institute Prompts Action
     * Saves the selected prompts for an institute
     */
    @Transactional @Secured(['ROLE_GPT_MANAGER', 'ROLE_IBOOKGPT_SITE_ADMIN', 'ROLE_INSTITUTE_MANAGER'])
    def saveInstitutePrompts() {
        println("***** Entered saveInstitutePrompts")
        Long instituteId = params.long('instituteId')
        String userType = params.userType
        String promptTypesStr = params.promptTypes

        if (!instituteId || !userType || promptTypesStr == null) {
            def json = [status: 'error', message: 'Missing required parameters.']
            render json as JSON
            return
        }

        try {
            // Get institute details
            InstituteMst institute = InstituteMst.get(instituteId)
            if (!institute) {
                def json = [status: 'error', message: 'Institute not found.']
                render json as JSON
                return
            }

            // Parse prompt types
            List<String> promptTypes = promptTypesStr ? promptTypesStr.split(',').collect { it.trim() } : []

            try {
                     InstitutePromptMst.executeUpdate("DELETE FROM InstitutePromptMst WHERE instituteId = :instituteId AND userType = :userType",
                                        [instituteId: instituteId, userType: userType])

                // Save new prompt selections with sort order
                promptTypes.eachWithIndex { promptType, index ->
                    if (promptType) {
                         new InstitutePromptMst(
                             promptType: promptType,
                             instituteId: instituteId,
                             userType: userType,
                            sortOrder: index + 1
                         ).save(flush: true)

                        println("***** Saved promptType: $promptType, instituteId: $instituteId, userType: $userType, sortOrder: ${index + 1}")
                    }
                }

                def json = [status: 'success', message: "${userType} prompts saved successfully. Total prompts: ${promptTypes.size()}"]
                render json as JSON
            } catch (Exception ex) {
                log.error("Error in inner try block: ${ex.message}", ex)
                def json = [status: 'error', message: "Failed to save prompts: ${ex.message}"]
                render json as JSON
            }
        } catch (Exception e) {
            log.error("Error saving institute prompts: ${e.message}", e)
            def json = [status: 'error', message: "An error occurred: ${e.message}"]
            render json as JSON
        }
    }



    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def resetPassword(){
        User user = User.findById(params.userId)
        if(user){
            String password = springSecurityService.encodePassword(params.newPassword)
            user.password = password
            user.save(flush:true,failOnError:true)
            flash.message = "Password reset successfully."
        }else{
            flash.message = "User not found."
        }
        def json = [status:"success"]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_IBOOKGPT_SITE_ADMIN','ROLE_INSTITUTE_MANAGER'])
    def deleteUser(){
        def json = [status:instManagerService.deleteUser(new Long(params.userId),new Long(params.batchId))]
        render json as JSON
    }


}
