package com.wonderslate.institute

import com.wonderslate.data.ObjectiveMst

import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService

import java.text.SimpleDateFormat
import java.util.concurrent.ThreadLocalRandom


class FormstestingController {
    SpringSecurityService springSecurityService

    def index() { }

    def gstRegistration(){
        //FormsMst formsMst = FormsMst.findByUsername(springSecurityService.currentUser.username);
        //[mode: formsMst!=null?(formsMst.trnId!=null && formsMst.trnId!=""?"trn":"complete"):"new"]
    }

    def formDocuments(){
        ObjectiveMst objectiveMst = ObjectiveMst.findByQuizId(new Integer(params.quizId))
        render objectiveMst.answerDescription
    }

    def otpFirst(){}
    def trnCreated(){ }

    def otpSecond(){}

    def savedApplications(){
        FormsMst formsMst = FormsMst.findByUsernameAndTrnId(springSecurityService.currentUser.username,session["trn"]);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/y");
        Calendar c = Calendar.getInstance()
        c.setTime(formsMst.dateCreated)
        c.add(Calendar.DATE,15)
        [dateCreated:simpleDateFormat.format(formsMst.dateCreated),dateExpiry: simpleDateFormat.format(c.getTime())]
    }

    def generateOTP() {
        def json = ['status':"OK"]
        render json as JSON
    }

    def generateOTP1() {
        FormsMst formsMst = FormsMst.findByUsernameAndTrnId(springSecurityService.currentUser.username,params.trn);
        def email, mobile
        def formsDtl = FormsDtl.findAllByFormsMstId(formsMst.id)

        if(formsDtl!=null) {
            formsDtl.each{result ->
                if(result.keyname.equals("email")) email = result.keyvalue;
                if(result.keyname.equals("mobile")) mobile = result.keyvalue;
            }
        }

        def json = ['status':formsDtl!=null?"OK":"Failed"]
        render json as JSON
    }

    def checkOTP() {
        def json = ['status':"OK"]
        render json as JSON
    }

    def checkOTP1() {
        session["trn"] = params.trn
        def json = ['status': "OK"]
        render json as JSON
    }

    def checkTrn() {
        FormsMst formsMst = FormsMst.findByUsernameAndTrnId(springSecurityService.currentUser.username,params.trn);
        def json = ['status':formsMst!=null?"OK":"Failed"]
        render json as JSON
    }


    def addForm() {
        Calendar c = Calendar.getInstance()
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/y");

        FormsMst formsMst = new FormsMst()
        formsMst.resId = new Integer(params.resId)
        formsMst.username = springSecurityService.currentUser.username;
        formsMst.trnId = ThreadLocalRandom.current().nextLong( 110000000001L, 910999999999L )
        formsMst.dateCreated = c.getTime();
        formsMst.save(failOnError: true,flush: true)

        FormsDtl formsDtl = new FormsDtl()
        formsDtl.formsMstId = formsMst.id
        formsDtl.keyname = 'applnType'
        formsDtl.keyvalue = params.app_type
        formsDtl.save(failOnError: true,flush: true)

        formsDtl = new FormsDtl()
        formsDtl.formsMstId = formsMst.id
        formsDtl.keyname = 'applnState'
        formsDtl.keyvalue = params.applnState
        formsDtl.save(failOnError: true,flush: true)

        formsDtl = new FormsDtl()
        formsDtl.formsMstId = formsMst.id
        formsDtl.keyname = 'applnDistr'
        formsDtl.keyvalue = params.applnDistr
        formsDtl.save(failOnError: true,flush: true)

        formsDtl = new FormsDtl()
        formsDtl.formsMstId = formsMst.id
        formsDtl.keyname = 'lgnmbzpan'
        formsDtl.keyvalue = params.lgnmbzpan
        formsDtl.save(failOnError: true,flush: true)

        formsDtl = new FormsDtl()
        formsDtl.formsMstId = formsMst.id
        formsDtl.keyname = 'pan_card'
        formsDtl.keyvalue = params.pan_card
        formsDtl.save(failOnError: true,flush: true)

        formsDtl = new FormsDtl()
        formsDtl.formsMstId = formsMst.id
        formsDtl.keyname = 'email'
        formsDtl.keyvalue = params.email
        formsDtl.save(failOnError: true,flush: true)

        formsDtl = new FormsDtl()
        formsDtl.formsMstId = formsMst.id
        formsDtl.keyname = 'mobile'
        formsDtl.keyvalue = params.mobile
        formsDtl.save(failOnError: true,flush: true)

        c.add(Calendar.DATE,15)

        def data=[status:"OK", trn:formsMst.trnId, deadline: simpleDateFormat.format(c.getTime())]

        render (view: "trnCreated", model: [data: data])
    }

    def detailedForm(){

    }
}