package com.wonderslate.institute

import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.TestsShared
import com.wonderslate.data.UtilService
import com.wonderslate.librarybooks.LibraryBooksService
import com.wonderslate.prepjoy.QuizRecDtl
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.sqlutil.SafeSql
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.apache.poi.hssf.usermodel.HSSFWorkbook
import org.apache.poi.ss.usermodel.Workbook
import com.wonderslate.usermanagement.User

class OnlineTestController {

    OnlineTestService onlineTestService
    def springSecurityService
    UtilService utilService
    LibraryBooksService libraryBooksService

    // 1. Test List Page
    @Transactional @Secured(['ROLE_USER'])
    def listTests() {
        Long instituteId = params.long('instituteId')
        Integer max = params.int('max') ?: 20
        Integer offset = params.int('offset') ?: 0
        String filter = params.filter ?: '' // upcoming, ongoing, awaitingResults, completed, or blank for all

        def result = onlineTestService.listTests( max, offset, filter,session['siteId'])
        [ testList   : result.tests,
          totalCount : result.totalCount,
          max        : max,
          offset     : offset,
          filter     : filter,
          instituteId: instituteId ]
    }

    // 2. Delete Test (Instructor Only)
    @Transactional @Secured(['ROLE_USER'])
    def deleteTest() {
        Long testId = params.long('testId')
        if (!testId) {
            flash.message = "Test ID is required."
            redirect(action: "listTests", params: [instituteId: params.instituteId])
            return
        }

        boolean success = onlineTestService.deleteTest(testId)
        flash.message = success ? "Test deleted successfully." : "Failed to delete test."
        redirect(action: "listTests", params: [instituteId: params.instituteId])
    }

    // 3. Blocking Students Page
    // Show the list of batch users, highlight who is blocked, allow bulk block/unblock
    @Transactional @Secured(['ROLE_USER'])
    def blockStudents() {
        Long testId = params.long('testId')
        if (!testId) {
            flash.message = "Test ID is required."
            return
        }
        TestsShared test = TestsShared.findByTestId(testId)
        // Fetch the user list from BatchUserDtl, plus who is blocked
        String sql = "select u.username,u.name from User u inner join BatchUserDtl b on u.username=b.username where b.batchId=${test.batchId} and (b.userType is null or b.userType='Student') order by u.name"
        def batchUsers = User.executeQuery(sql)
        //use collect and crate a new list
        batchUsers = batchUsers.collect { u ->
            [ username: u[0], name: u[1] ]
        }

        def blockedMap = [:]
        BlockedStudent.findAllByTestId(testId).each { blk ->
            blockedMap[blk.username] = blk
        }

        [ batchId   : test.batchId,
          batchUsers: batchUsers,
          blockedMap: blockedMap,
            testName: test.name]
    }

    // 4. Perform Bulk Block
    @Transactional @Secured(['ROLE_USER'])
    def doBlockStudents() {
        Long testId = params.long('testId')
        String reason = params.reason
        String blockedBy = springSecurityService.currentUser.username
        def usernames = params.list('usernames') // checkboxes from form
        onlineTestService.bulkBlockUsers(testId, usernames, blockedBy, reason)
        flash.message = "Selected users blocked successfully."
        redirect(action: "blockStudents", params: [testId: testId])
    }

    // 5. Perform Bulk Unblock
    @Transactional @Secured(['ROLE_USER'])
    def doUnblockStudents() {
        Long testId = params.long('testId')
        def usernames = params.list('blockedUsernames')
        onlineTestService.bulkUnblockUsers(testId, usernames)
        flash.message = "Selected users unblocked successfully."
        redirect(action: "blockStudents", params: [testId: testId])
    }

    // 6. Results Page (Instructor/Manager)
    @Transactional @Secured(['ROLE_USER'])
    def testResults() {
        Long testId = params.long('testId')
        TestsShared testsShared = TestsShared.findByTestId(testId)
        CourseBatchesDtl batch = CourseBatchesDtl.findById(testsShared.batchId)
        InstituteMst institute = InstituteMst.findById(batch.conductedBy)
        Long batchId = params.long('batchId')
        if (!testId || !batchId) {
            flash.message = "Test ID and Batch ID are required."
            return
        }
       println("**** enableAnalytics: "+institute.enableAnalytics)

        List results = onlineTestService.getResultsForTest(testId, batchId)
        [ resultsList: results,
          testId     : testId,
          batchId    : batchId,
          testName   : testsShared.name,
          showAnalytics: institute.enableAnalytics?institute.enableAnalytics:false]
    }

    // 7. Download Excel
    @Transactional @Secured(['ROLE_USER'])
    def downloadResultsExcel() {
        Long testId = params.long('testId')
        Long batchId = params.long('batchId')
        if (!testId || !batchId) {
            flash.message = "Test ID and Batch ID are required."
            redirect(action: "testResults", params: [testId: testId, batchId: batchId])
            return
        }

        def rows = onlineTestService.getResultsForExcel(testId, batchId)
        // Build an Excel file using Apache POI (example)
        Workbook workbook = new HSSFWorkbook()
        def sheet = workbook.createSheet("TestResults")
        def header = sheet.createRow(0)
        header.createCell(1).setCellValue("Name")
        header.createCell(2).setCellValue("Status")
        header.createCell(4).setCellValue("Correct")
        header.createCell(5).setCellValue("Incorrect")
        header.createCell(6).setCellValue("Skipped")
        header.createCell(7).setCellValue("TimeTaken")

        int rowNum = 1
        rows.each { r ->
            def row = sheet.createRow(rowNum++)
            row.createCell(1).setCellValue(r.name ?: "")
            row.createCell(2).setCellValue(r.status ?: "")
            row.createCell(4).setCellValue(r.correctAnswers?.toString() ?: "0")
            row.createCell(5).setCellValue(r.incorrectAnswers?.toString() ?: "0")
            row.createCell(6).setCellValue(r.skipped?.toString() ?: "0")
            row.createCell(7).setCellValue(r.timeTaken?.toString() ?: "0")
        }

        // Set response headers
        response.contentType = "application/vnd.ms-excel"
        response.setHeader("Content-Disposition", "attachment; filename=\"testResults-${testId}.xls\"")
        workbook.write(response.outputStream)
        response.outputStream.flush()
        response.outputStream.close()
        workbook.close()
    }

    // Helper: fetch batch users from BatchUserDtl joined with User
    @Transactional @Secured(['ROLE_USER'])
    private List fetchBatchUsers(Long batchId) {
        def batchUsers = BatchUserDtl.findAllByBatchId(batchId)*.username
        if (!batchUsers) return []
        def users = User.withCriteria {
            'in'('username', batchUsers)
            order('name', 'asc')
        }
        // Return a list of maps
        users.collect { u ->
            [ username: u.username, name: u.name, email: u.email ]
        }
    }

    /**
     * Show question-level analytics for instructors/managers
     * Columns: questionText, correctCount, incorrectCount, skippedCount, avgTime
     * We can pass sortColumn, sortDir for server-side sorting
     */
    @Transactional @Secured(['ROLE_USER'])
    def testQuestionAnalytics() {
        Integer testId = new Integer(params.testId)
        TestsShared testsShared = TestsShared.findByTestId(testId)
        if (!testId) {
            flash.message = "Test ID is required."
            return
        }

        // Optional sort parameters
        String sortColumn = params.sortColumn ?: 'questionText'
        String sortDir = params.sortDir in ['asc','desc'] ? params.sortDir : 'asc'

        List analytics = onlineTestService.getQuestionAnalyticsForTest(testId, sortColumn, sortDir)
// We'll call the service to get aggregated data
        Map analyticsData = onlineTestService.getAnalyticsDataForTest(testId,analytics)
        [ testId      : testId,
          analytics   : analytics,
          sortColumn  : sortColumn,
          sortDir     : sortDir,
          scoreDistribution: analyticsData.scoreDistribution,
          questionStats    : analyticsData.questionStats,
          testName: testsShared.name ]
    }

    @Transactional @Secured(['ROLE_USER'])
    def getSubjectsForClass() {
        String sql ="select group_concat(book_id) from books_batch_dtl where batch_id in (${libraryBooksService.getAllBatchIds(params.batchId)})"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)

         sql = "select distinct(btd.subject) from  books_tag_dtl btd, chapters_mst cm,resource_dtl rd where "+
                " cm.book_id in ("+results[0][0]+") and btd.book_id=cm.book_id and rd.chapter_id=cm.id and rd.res_type='Multiple Choice Questions'"+
                " order by btd.subject"
        println("sql: "+sql)
         dataSource = grailsApplication.mainContext.getBean('dataSource')
         sql1 = new SafeSql(dataSource)
         results = sql1.rows(sql)
        render results as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def getUserBooksForTestGenerator(){
        // Combined query: Join books_batch_dtl (wsuser) with books_mst and books_tag_dtl (wsshop)
        // This avoids group_concat limitations and gets book details directly
        String sql = "select distinct bm.cover_image coverImage, bm.id, bm.title " +
                     "from wsuser.books_batch_dtl bbd " +
                     "inner join wsshop.books_mst bm on bbd.book_id = bm.id " +
                     "inner join wsshop.books_tag_dtl btd on bm.id = btd.book_id " +
                     "where bbd.batch_id in (${libraryBooksService.getAllBatchIds(params.batchId)}) " +
                     "and btd.subject = '${params.subject}' " +
                     "order by bm.title"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def booksResults = sql1.rows(sql)
        println("the number of books in combined query "+booksResults.size())
        // If no books found, return empty result
        if (!booksResults || booksResults.isEmpty()) {
            def json = [
                'books': [],
                'status': "Nothing present",
                'defaultBooks': []
            ]
            render json as JSON
            return
        }
        // for each book check if it has a question and answer

        def filteredBooks = []
        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new Sql(dataSource)
        booksResults = booksResults.findAll { book ->
            // Check if there are questions and answers for this book
            sql = "select count(*) from objective_mst om, resource_dtl rd where om.quiz_id=rd.res_link and rd.res_type='QA' and rd.chapter_id in (select id from chapters_mst where book_id=${book.id})"
            def countResults = sql1.rows(sql)
            if(countResults[0][0] > 0){
                // add id,coverImage,title to finalBooks
                filteredBooks.add([id: book.id, coverImage: book.coverImage, title: book.title])
            }
        }
        println("the number of books in step2 "+filteredBooks.size())
        def json = [
            'books': filteredBooks,
            'status': filteredBooks ? "OK" : "Nothing present",
            'defaultBooks': []
        ]

        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def checkTestTimeConflict(){
        Date startDateTime
        Date endDateTime
        if (params.startDateTime != null && params.startDateTime.length() > 0) {
            startDateTime = utilService.convertDateWithPattern(params.startDateTime, "IST", "UTC","dd-MM-yyyy HH:mm")
        }
        if (params.endDateTime != null && params.endDateTime.length() > 0) {
            endDateTime = utilService.convertDateWithPattern(params.endDateTime, "IST", "UTC","dd-MM-yyyy HH:mm")
        }
        String startDate, endDate
        //convert startDateTime and endDateTime to YYYY-MM-DD HH:mm:ss format
        if (startDateTime != null) {
            startDate = startDateTime.format("yyyy-MM-dd HH:mm:ss")
        }
        if (endDateTime != null) {
            endDate = endDateTime.format("yyyy-MM-dd HH:mm:ss")
        }

        def batchId = params.batchId
         def sql = "select test_id from tests_shared where batch_id=${batchId} and (('${startDate}' between start_date_time  and end_date_time) or ('${endDate}' between start_date_time and end_date_time ))"
         println(sql)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
         def json =[
                'status': results ? "Conflict" : "OK"
        ]
        render json as JSON
    }

    /**
     * View all questions for a specific test
     * This action is accessible to instructors and managers
     */
    @Transactional @Secured(['ROLE_USER'])
    def viewQuestions() {
        Long testId = params.long('testId')
        if (!testId) {
            flash.message = "Test ID is required."
            redirect(action: "listTests")
            return
        }

        // Get test details
        TestsShared test = TestsShared.findByTestId(testId)
        if (!test) {
            flash.message = "Test not found."
            redirect(action: "listTests")
            return
        }

        [testId: testId,
         testName: test.name,
         batchId: test.batchId]
    }

    /**
     * AJAX endpoint to get questions for a test in JSON format
     * This allows us to load questions asynchronously and render math formulas properly
     */
    @Transactional @Secured(['ROLE_USER'])
    def getQuestionsJson() {
        Long testId = params.long('testId')


        // Get all questions for this test
        def questions = onlineTestService.getQuestionsForTest(testId)


          def json =   [success: true, questions: questions]
           render json as JSON

    }

    @Transactional @Secured(['ROLE_USER'])
    def getUserBooksForTestGeneratorForMCQ(){
        String sql ="select group_concat(book_id) from books_batch_dtl where batch_id in (${libraryBooksService.getAllBatchIds(params.batchId)})"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        sql = "select bm.cover_image coverImage,bm.id,bm.title from  books_tag_dtl btd, books_mst bm where "+
                " bm.id in ("+results[0][0]+") and btd.book_id=bm.id and btd.subject='${params.subject}'"+
                " order by bm.title"
        println("sql: "+sql)
        dataSource = grailsApplication.mainContext.getBean('dataSource')
        sql1 = new Sql(dataSource)
        results = sql1.rows(sql)


        def json =[
                'books': results,
                'status': results ? "OK" : "Nothing present",
                'defaultBooks' : []
        ]

        render json as JSON

    }
}
