package com.wonderslate

import com.wonderslate.cache.DataProviderService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class DiscussionBoardAdminController {
    def redisService
    DataProviderService dataProviderService

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def index() {
        if(session['siteId']==null) session['siteId'] = new Integer(1)
        if(redisService.("bookCategories_"+session['siteId'])==null) {
            dataProviderService.getBookCategories(session['siteId'])
        }

        ["School":redisService.("bookCategories_"+session['siteId']+"_School"),
         "College":redisService.("bookCategories_"+session['siteId']+"_College"),
         "CompetitiveExams":redisService.("bookCategories_"+session['siteId']+"_CompetitiveExams")]

    }
}
