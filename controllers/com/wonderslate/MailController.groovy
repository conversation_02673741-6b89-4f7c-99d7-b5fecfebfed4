package com.wonderslate

import com.wonderslate.usermanagement.Groups
import com.wonderslate.usermanagement.MailManagementService
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured

class  MailController {
   SpringSecurityService springSecurityService
    MailManagementService mailManagementService
    def index() {}

    def activitiesUpdate() {

    }

    def tester(){
        mailManagementService.activitiesUpdate()
        render "Hello"
    }

    def sendInvite(){

    }

}
