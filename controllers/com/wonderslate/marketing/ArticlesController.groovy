package com.wonderslate.marketing

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.LevelsMst
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.publish.Blogs
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.Publishers
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.publish.SyllabusSubject
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.UserManagementService
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.time.YearMonth

class ArticlesController {

    DataProviderService dataProviderService
    UtilService utilService
    WsshopService wsshopService
    UserManagementService userManagementService

    def showBlogPages(){
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        String sql = "select b.syllabus,ls.id from wsshop.level_syllabus ls,wsshop.blogs b where ls.syllabus=b.syllabus and ls.site_id=1 and b.grade='null' and b.col_name='introduction' order by b.syllabus"
        List syllabusEnglishBlogs = sql1.rows(sql);

        //Syllabus hindi blogs
        sql = "select b.syllabus,ls.id from wsshop.level_syllabus ls,wsshop.blogs b where ls.syllabus=b.syllabus and ls.site_id=1 and b.grade='null' and b.col_name='introductionHindi'  order by b.syllabus"

        List syllabusHindiBlogs = sql1.rows(sql);

        //Grade English blogs
        sql = "select b.syllabus,b.grade,ls.id from wsshop.syllabus_grade_dtl ls,wsshop.blogs b \n" +
                " where ls.syllabus=b.syllabus and ls.site_id=1   and ls.grade=b.grade and b.col_name='introduction' order by b.syllabus,b.grade"

        List gradeEnglishBlogs = sql1.rows(sql);

        //Grade Hindi blogs
        sql = "select b.syllabus,b.grade,ls.id from wsshop.syllabus_grade_dtl ls,wsshop.blogs b \n" +
                " where ls.syllabus=b.syllabus and ls.site_id=1   and ls.grade=b.grade and b.col_name='introductionHindi'"

        List gradeHindiBlogs = sql1.rows(sql);

        [syllabusEnglishBlogs:syllabusEnglishBlogs,syllabusHindiBlogs:syllabusHindiBlogs,gradeEnglishBlogs:gradeEnglishBlogs,gradeHindiBlogs:gradeHindiBlogs,title: "Blog index"]

    }

    @Transactional
    def getGradeBooks(){
        SiteMst siteMst = dataProviderService.getSiteMst(utilService.getSiteIdIgnoreSiteName(request, session))
        if ("true".equals(siteMst.commonWhiteLabel)){
            userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
        }
        String pageTitle = ""
        String topLevelTitle ="";
        String fullTitle=""
         def currentYearMonth = YearMonth.now()
        def year = currentYearMonth.year
        def month = currentYearMonth.monthValue
        String seoDesc = "The source for best study materials including textbooks, sample papers, mocktests, guides, free download pdf etc."
        String keywords =""
        if(month>3) year++

        String introduction=null
        String faq = null
        String grade=null
        String subject=null
        LevelSyllabus levelSyllabus

        if(params.gradeId!=null)
        {
            SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findById(new Integer(params.gradeId))
            levelSyllabus = LevelSyllabus.findBySiteIdAndSyllabus(syllabusGradeDtl.siteId,syllabusGradeDtl.syllabus)
            params.put("syllabus",syllabusGradeDtl.syllabus)
            params.put("grade",syllabusGradeDtl.grade)
            grade = syllabusGradeDtl.grade
        }else{
            SyllabusSubject syllabusSubject = SyllabusSubject.findById(new Integer(params.subjectId))
            levelSyllabus = LevelSyllabus.findBySiteIdAndSyllabus(new Integer(1),syllabusSubject.syllabus)
            params.put("syllabus",syllabusSubject.syllabus)
            params.put("subject",syllabusSubject.subject)
            subject = syllabusSubject.subject
        }


        params.put("level",levelSyllabus.level)
        params.put("gradeId",null)
        params.put("noOfBooks","100")

        Publishers publishers = null
        if(params.publisherId!=null)
        {
            publishers = Publishers.findById(new Integer(params.publisherId))
            pageTitle = publishers.name.replace("Books","")+" "
            keywords = publishers.name.replace("Books","")+" books,"+publishers.name.replace("Books","")+" free pdf download,"
            seoDesc += publishers.name.replace("Books","")+" books."
        }

        if("School".equals(levelSyllabus.level)) {
            pageTitle +=levelSyllabus.syllabus+" Class "+grade
            topLevelTitle =levelSyllabus.syllabus
            seoDesc +=" Best resources to prepare for "+levelSyllabus.syllabus+" Class "+grade+" "+year+ " exams."
            keywords +=levelSyllabus.syllabus+" Class "+grade+" books"

        }
        else if("College".equals(levelSyllabus.level)) {

            pageTitle +=levelSyllabus.syllabus+" Semester "+grade
            topLevelTitle =levelSyllabus.syllabus
            seoDesc +=" Best resources to prepare for "+levelSyllabus.syllabus+" Semester "+grade+" "+year+ " exams."
            keywords +=levelSyllabus.syllabus+" Semester "+grade+" books"
            if((""+grade).trim().size()==1) params.put("grade","Semester "+grade)
        }
        else {
            pageTitle += grade
            topLevelTitle = grade
            seoDesc +=" Best resources to prepare for "+grade+" "+year+ " exams."
            keywords += grade+" books"
        }

        if(subject!=null){
            pageTitle += subject
            topLevelTitle = subject
            seoDesc +=" Best resources to prepare for "+subject+" "+year+ " exams."
            keywords += subject+" books"
        }

        if("General".equals(levelSyllabus.level)) pageTitle +=" Books | Free PDF Download "
        else  {
            pageTitle +=" Books | For "+year+" exams | Free PDF Download "
            keywords += " ,For "+year+" exams,Free PDF Download"
        }

         HashMap booksAndPublishers = wsshopService.getBooksList(params,siteMst.id,0)

        //Wonderslate and Prepjoy Specific information

        boolean wsAndPrepjoySite = true

            if("School".equals(levelSyllabus.level)||"College".equals(levelSyllabus.level)){
                Blogs blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1),levelSyllabus.syllabus,"null","introduction")
                if(blogs!=null) introduction = blogs.colValue
                blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1),levelSyllabus.syllabus,"null","faq")
                if(blogs!=null) faq = blogs.colValue
            }else{
                if(subject!=null){
                    Blogs blogs = Blogs.findBySiteIdAndSyllabusAndSubjectAndColName(new Integer(1), levelSyllabus.syllabus, subject, "introduction")
                    if (blogs != null) introduction = blogs.colValue
                    blogs = Blogs.findBySiteIdAndSyllabusAndSubjectAndColName(new Integer(1), levelSyllabus.syllabus, subject, "faq")
                    if (blogs != null) faq = blogs.colValue
                }else {
                    Blogs blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1), levelSyllabus.syllabus, grade, "introduction")
                    if (blogs != null) introduction = blogs.colValue
                    blogs = Blogs.findBySiteIdAndSyllabusAndGradeAndColName(new Integer(1), levelSyllabus.syllabus, grade, "faq")
                    if (blogs != null) faq = blogs.colValue
                }
            }


        [title:pageTitle,seoDesc:seoDesc,keywords:keywords,booksList:booksAndPublishers,wsAndPrepjoySite:wsAndPrepjoySite,introduction:introduction,
         publishers:publishers,topLevelTitle:topLevelTitle,faq:faq]

    }

    @Transactional
    def showGradeBooks(){
        SiteMst siteMst = dataProviderService.getSiteMst(utilService.getSiteIdIgnoreSiteName(request, session))
        if ("true".equals(siteMst.commonWhiteLabel)){
            userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
        }
        HashMap booksAndPublishers = wsshopService.getBooksList(params,siteMst.id,0)
        List grades  = wsshopService.getActiveGrades(siteMst.id,params.publisherId)

        String publisherName=null
        if(params.publisherId!=null) {
            Publishers publishers = Publishers.findById(new Integer(params.publisherId))
            publisherName = publishers.name
        }
        List publishers  = new JsonSlurper().parseText(booksAndPublishers.get("publishers"))


        [grades:grades,publishers: publishers,publisherName:publisherName,title: "Grade books index"]
    }
}
