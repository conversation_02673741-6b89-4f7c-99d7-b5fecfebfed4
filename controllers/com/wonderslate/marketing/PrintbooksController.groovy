package com.wonderslate.marketing

import com.ibookso.products.ExtPublishers
import com.wonderslate.DataNotificationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.PrintBooksService
import com.wonderslate.data.SiteManagerService
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

class PrintbooksController {
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService
    PrintBooksService printBooksService
    def redisService
    UserManagementService userManagementService
    SiteManagerService siteManagerService

  @Transactional
    def index() {
        session["entryController"] = "books"
        [test:"test"]
    }

    def printbooksmanagement(){

    }



    def getAndAddAllPrintBooks()
    {
        printBooksService.getAndAddAllPrintBooks();
        render("done")
    }


    def getEbookId(){
        render printBooksService.getEbookId(params.title,params.isbn)
    }

    def getAllLeafCategory(){
        printBooksService.createCategoryMap()
        render printBooksService.getAllLeafCategory(params.categoryId)
    }
    def getPrintBooks()
    {
        String url = request.getRequestURL()
        if (url.indexOf("http://localhost") > -1 || url.indexOf("wonderslate.com") > -1 || url.indexOf("prepjoy.com") > -1 || url.indexOf("ibookso.com") > -1) {
            String pageNo = "0"
            if (params.pageNo != null) pageNo = params.pageNo
            String categoryId = params.categoryId
            String publisherId = params.publisherId
            if("".equals(categoryId)||categoryId==null) categoryId="all"
            def json
            String baseCategory = params.baseCategory!=null?params.baseCategory:""
            if (publisherId == null) {
                if (redisService.("printBooksList_" + baseCategory.replace(' ', '') + "_" + categoryId + "_" + pageNo) == null) {
                    printBooksService.getPrintBooks(categoryId, pageNo, null, baseCategory)
                }
            } else {
                if (redisService.("printBooksList_" + baseCategory.replace(' ', '') + "_" + categoryId + "_" + publisherId + "_" + pageNo) == null) {
                    printBooksService.getPrintBooks(categoryId, pageNo, publisherId, baseCategory)
                }
            }
            if (publisherId == null) json = ["booksList": redisService.("printBooksList_" + baseCategory.replace(' ', '') + "_" + categoryId + "_" + pageNo)]
            else json = ["booksList": redisService.("printBooksList_" + baseCategory.replace(' ', '') + "_" + categoryId + "_" + publisherId + "_" + pageNo)]

            render json as JSON
        }else{
            redirect(controller:"books",action:"wrongPage")
        }
    }

    @Transactional
    def getPrintBooksCategories(){
        String url = request.getRequestURL()
        if (url.indexOf("http://localhost") > -1 || url.indexOf("wonderslate.com") > -1 || url.indexOf("prepjoy.com") > -1 || url.indexOf("ibookso.com") > -1) {
            ArrayList categories = new ArrayList();
            String[] firstCategories
            String categoryId
            String categoryName
            String[] categoryMap
            String allowId
            String defaultCategoryId
            String baseCategory = params.baseCategory

            if (redisService.("firstLevelCategories_" + baseCategory.replace(' ', '')) == null) printBooksService.createCategoryMap(baseCategory)
            defaultCategoryId = params.catId != null ? params.catId : params.categoryId
            if (redisService.("printBooksList_" + categoryId + "_" + params.pageNo) == null) printBooksService.getPrintBooks(defaultCategoryId, params.pageNo)
            if (params.categoryId == null) {

                firstCategories = redisService.("firstLevelCategories_" + baseCategory.replace(' ', '')).split(",")
                for (int i = 0; i < firstCategories.length; i++) {
                    categoryId = firstCategories[i].split("_")[0]
                    categoryMap = redisService.("categMap_" + categoryId).split("_")
                    categories.add(redisService.("categMap_" + categoryId))
                    //if the category is the second level, then continue
                    if (categoryMap[0].indexOf('2') == 0) continue
                }
            } else {
                categoryId = params.categoryId
                categoryMap = redisService.("categMap_" + categoryId).split("_")
                categoryName = categoryMap[2]
                if (redisService.("printBookCategories_" + categoryId) == null) printBooksService.getAllLeafCategory(categoryId)
                firstCategories = redisService.("printBookCategories_" + categoryId).split(',')
                String childCategoryId
                for (int i = 0; i < firstCategories.length; i++) {
                    childCategoryId = firstCategories[i]
                    categories.add(redisService.("categMap_" + childCategoryId))
                    if (i == 0) {
                        categoryMap = redisService.("categMap_" + childCategoryId).split("_")
                        allowId = "" + (Integer.parseInt(categoryMap[0]) + 1)
                        if (Integer.parseInt(allowId) > 2) {
                            String parentCategId = categoryMap[3]
                            categoryMap = redisService.("categMap_" + parentCategId).split("_")
                        }
                        if (Integer.parseInt(allowId) > 3) {
                            String parentCategId = categoryMap[3]
                            categoryMap = redisService.("categMap_" + parentCategId).split("_")
                        }
                        if (Integer.parseInt(allowId) > 4) {
                            String parentCategId = categoryMap[3]
                            categoryMap = redisService.("categMap_" + parentCategId).split("_")
                        }
                    }
                }
            }
            def json = [categories: categories, firstCategories: firstCategories, categoryName: categoryName, "booksList": redisService.("printBooksList_" + defaultCategoryId + "_" + params.pageNo)]
            render json as JSON
        }else{
            redirect(controller:"books",action:"wrongPage")
        }
    }

    @Transactional
    def getPrintBooksList(startDate,endDate,download){
        String url = request.getRequestURL()
        if (url.indexOf("http://localhost") > -1 || url.indexOf("wonderslate.com") > -1 || url.indexOf("prepjoy.com") > -1 || url.indexOf("ibookso.com") > -1) {
            String sql = "SELECT *" +
                    " FROM " +
                    " wsoutsideproducts.print_books_mst pbm " +
                    " WHERE" +
                    " DATE(DATE_ADD(pbm.date_created,INTERVAL '5:30' HOUR_MINUTE)) >= DATE('" + startDate + "')" +
                    " AND " +
                    " DATE(DATE_ADD(pbm.date_created,INTERVAL '5:30' HOUR_MINUTE)) <= DATE('" + endDate + "')"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)

            List bookList = results.collect { book ->
                def categoryList = printBooksService.getCategoryNames(book.category_id)
                String categoryListVal = categoryList
                ExtPublishers publisherList
                String publisherName
                String ebookPrice = ""
                String shortDescription = ""
                String description = ""

                if (book.publisher != null) {
                    publisherList = ExtPublishers.findById(new Long(book.publisher))
                    publisherName = publisherList.name != null ? publisherList.name : ''
                    description = "<p><strong>Title</strong> : " + book.title + "</p>" + "<p><strong>Publisher Name</strong> : " + publisherName + "</p>"
                    shortDescription = "<p><strong>Publisher Name</strong> : " + publisherName + "</p>"
                } else {
                    description = "<p><strong>Title</strong> : " + book.title + "</p>"
                }
                if (book.isbn != null) {
                    description += "<p><strong>ISBN</strong> : " + book.isbn + "</p>"
                    shortDescription += "<p><strong>ISBN</strong> : " + book.isbn + "</p>"
                }

                String title = "" + book.title + ""
                ebookPrice = printBooksService.getEbookPrice(book.isbn)
                ebookPrice = ebookPrice != null ? ebookPrice : ""
                return [categories             : categoryListVal, id: book.id, coverImage: book.cover_image, title: title, isbn: book.isbn,
                        simple                 : 'simple', visible: 'visible', position: '0', inStock: '1', published: '1',
                        featured               : '0', taxStatus: 'taxable', taxClass: 'parent',
                        backOrder              : '0', soldInd: '0', cusReview: '1', atrGlobal1: '1', pageTemplate: 'default',
                        atr1Visible            : '1', woobt_checked_all: 'off', woobt_separately: 'off', woobt_selection: 'multiple',
                        woobt_custom_qty       : 'off', woobt_sync_qty: 'off', woobt_limit_each_min_default: 'off', woost_overwrite: 'off', woosw_count: '1',
                        elementor_edit_mode    : 'builder', elementor_template_type: 'product-post', elementor_version: '3.5.0', elementor_pro_version: '3.4.2',
                        shortDescription       : shortDescription, ebookPrice: ebookPrice, isbn: book.isbn, description: description,
                        type                   : 'simple', sku: '', empty: "", stock: "", lowStock: "", weight: "", length: "", width: "", height: "", purchaseNote: "",
                        salePrice              : "", tags: "", shippingClass: "", downloadLimit: "", downloadExpiry: "", parent: "", grouped: "",
                        upsell                 : "", crossSells: "", extUrl: "", btnText: "", atrName1: "", atrVal: "", atrDefault: "", mtIns: "", variation_loop: "", woosw_add: "",
                        elementor_data         : "", wp_old_date: "", woosb_ids: "", disable_auto_price: "", woosb_discount: "", shipping_fee: "",
                        woosb_optional_products: "", woosb_manage_stock: ""
                ]
            }
            def json = bookList
            return json as JSON
        }else{
            redirect(controller:"books",action:"wrongPage")
        }
    }

    @Transactional
    def downloadPrintBooks(){
        String startDate = params.startDate
        String endDate = params.endDate
        def newData = getPrintBooksList(startDate,endDate,false)
        def jsonData = newData.toString()
        try {
            if(jsonData!=null){
                def slurper = new JsonSlurper()
                def data = slurper.parseText(jsonData)
                def fileName = "wp_products"  + (new Random()).nextInt(9999999) + ".csv";
                response.setContentType('text/csv')
                response.setHeader('Content-Disposition', 'attachment; filename='+fileName)
                def output = new StringBuilder()

                output.append("ID,Type,SKU,Name,Published," +
                        "Is featured?,Visibility in catalog,Short description,Description,Date sale price starts," +
                        "Date sale price ends,Tax status,Tax class,In stock?,Stock," +
                        "Low stock amount,Backorders allowed?,Sold individually?,Weight (kg),Length (cm)," +
                        "Width (cm),Height (cm),Allow customer reviews?,Purchase note,Sale price," +
                        "Regular price,Categories,Tags,Shipping class,Images," +
                        "Download limit,Download expiry days,Parent,Grouped products,Upsells," +
                        "Cross-sells,External URL,Button text,Position,Attribute 1 name," +
                        "Attribute 1 value(s),Attribute 1 visible,Attribute 1 global,Attribute 1 default,Meta: _wp_page_template," +
                        "Meta: insight_product_options,Meta: woobt_checked_all,Meta: woobt_separately,Meta: woobt_selection,Meta: woobt_custom_qty," +
                        "Meta: woobt_sync_qty,Meta: woobt_limit_each_min_default,Meta: woost_overwrite,Meta: variation_attributes_show_on_loop,Meta: woosw_count," +
                        "Meta: woosw_add,Meta: _elementor_edit_mode,Meta: _elementor_template_type,Meta: _elementor_version,Meta: _elementor_pro_version," +
                        "Meta: _elementor_data,Meta: _wp_old_date,Meta: woosb_ids,Meta: woosb_disable_auto_price,Meta: woosb_discount," +
                        "Meta: woosb_shipping_fee,Meta: woosb_optional_products,Meta: woosb_manage_stock,Meta: woosb_limit_each_min_default,Meta: woosw_remove," +
                        "Meta: woopb_id,Meta: wooeBook_price,Meta: wooeBook_isbn\n")

                data.each { row ->
                    String title = clearCommas(row.title)
                    String shortDescription = clearCommas(row.shortDescription)
                    String description = clearCommas(row.description)
                    String isbn = clearCommas(row.isbn)
                    output.append("${row.empty},${row.type},${row.sku},${title},${row.published}," +
                                "${row.featured},${row.visible},${shortDescription},${description},${row.empty}," +
                                "${row.empty},${row.taxStatus},${row.taxClass},${row.inStock},${row.stock}," +
                                "${row.lowStock},${row.backOrder},${row.soldInd},${row.weight},${row.length}," +
                                "${row.width},${row.height},${row.cusReview},${row.purchaseNote},${row.salePrice},"+
                                "${row.ebookPrice},${row.categories},${row.tags},${row.shippingClass},${row.coverImage},"+
                                "${row.downloadLimit},${row.downloadExpiry},${row.parent},${row.grouped},${row.upsell},"+
                                "${row.crossSells},${row.extUrl},${row.btnText},${row.position},${row.atrName1},"+
                                "${row.atrVal},${row.atr1Visible},${row.atrGlobal1},${row.atrDefault},${row.pageTemplate},"+
                                "${row.mtIns},${row.woobt_checked_all},${row.woobt_separately},${row.woobt_selection},${row.woobt_custom_qty},"+
                                "${row.woobt_sync_qty},${row.woobt_limit_each_min_default},${row.woost_overwrite},${row.variation_loop},${row.woosw_count},"+
                                "${row.woosw_add},${row.elementor_edit_mode},${row.elementor_template_type},${row.elementor_version},${row.elementor_pro_version},"+
                                "${row.elementor_data},${row.wp_old_date},${row.woosb_ids},${row.disable_auto_price},${row.woosb_discount},"+
                                "${row.shipping_fee},${row.woosb_optional_products},${row.woosb_manage_stock},${row.empty},${row.empty},"+
                                "${row.id},${row.ebookPrice},${isbn}\n")
                }


                response.writer.write(output.toString())
                render ""
            }else render ""
        }
        catch(Exception e){
            println("Exception happened at :"+e)
            render "SOMETHING WENT WRONG :("
        }
    }

    def clearCommas(str){
        if (str!=null && str!=""){
            if(str.indexOf(",") >= 0){
                str = "\""+str+"\"";
            }
        }else{
            str = ""
        }
        return str
    }


}
