package com.wonderslate.marketing

import com.wonderslate.cache.DataProviderService
import com.wonderslate.seo.HeaderMst
import grails.plugin.springsecurity.annotation.Secured
import groovy.json.JsonSlurper
import groovy.sql.Sql

class HeaderController {
    DataProviderService dataProviderService
    def redisService
    @Secured(['ROLE_DIGITAL_MARKETER'])
    def index() {
        String sql = "select bk.id,bk.title,bk.cover_image,hm.header " +
                " from books_mst bk,header_mst hm " +
                " where bk.status in ('free','published')" +
                " and bk.id=hm.type_id and hm.header_for='book' "+
                "  union " +
                "  select bk.id,bk.title,bk.cover_image,null  from books_mst bk " +
                "  where bk.status in ('free','published') and bk.id not in (select type_id from header_mst where header_for='book')"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List books = results.collect { book ->
            return [id: book[0], title: (book[1]).replace(':',' ').replace(',',' '),coverImage:book[2],header:(book[3]!=null&&book[3].length()>0?"hasHeader":"No")]
        }

       [books:books]

    }
    @Secured(['ROLE_DIGITAL_MARKETER'])
    def editIndex(){
        HeaderMst headerMst = HeaderMst.findByTypeIdAndHeaderFor(new Long(params.bookId),"book")

        if("submit".equals(params.mode)){
            String header = params.header
            if(headerMst==null){
                header = header.replaceAll("<head>","")
                header = header.replaceAll("</head>","")
                headerMst = new HeaderMst(typeId:new Long(params.bookId),headerFor: "book",header: params.header )

            }
            else{
                header = header.replaceAll("<head>","")
                header = header.replaceAll("</head>","")
                headerMst.header=header
            }
            headerMst.save(flush: true, failOnError: true)
            dataProviderService.getBookHeader(new Long(params.bookId))
            redirect (controller: 'header' , action: 'index')
        }else{
            [header:headerMst==null?"":headerMst.header]
        }
    }


}
