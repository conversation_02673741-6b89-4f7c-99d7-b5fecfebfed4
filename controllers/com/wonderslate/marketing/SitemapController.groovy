package com.wonderslate.marketing

import com.wonderslate.data.SitemapService

class SitemapController {
    SitemapService sitemapService

    def createPrintbookCategoroiesSitemap() {
        sitemapService.createPrintbookCategoroiesSitemap()
        render "Completed createPrintbookCategoroiesSitemap"
    }

    def generateMockTestsSiteMap(){
        sitemapService.generateMockTestsSiteMap()
        render "Completed generateMockTestsSiteMap"
    }

    def generatePrintPublishersSiteMap(){
        sitemapService.generatePrintPublishersSiteMap()
        render "Completed generatePrintPublishersSiteMap"
    }

}
