package com.wonderslate.marketing

import com.ibookso.products.CategoryLevel2
import com.ibookso.products.CategoryLevel3
import com.ibookso.products.CategoryLevel4
import com.ibookso.products.ExtPublishers
import com.wonderslate.data.LevelsMst
import com.wonderslate.publish.LevelSyllabus
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.publish.SyllabusSubject
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.sql.Sql

class AffiliationAdminController {

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def index() {
        String sql = "select distinct(category_type) category_type from category_level1 order by category_type"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        [topLevelCategories:results]
    }

    @Transactional
    def getTopLevelCategories(){
        String sql = "select distinct(category_type) category_type from category_level1 order by category_type"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        def json = [topLevelCategories:results]
        render json as JSON
    }

    @Transactional
    def getSecondLevelCategories(){
        String categoryType = params.categoryType
        String sql = "select node_name nodeName,browse_node_id browseNodeId from category_level1 where category_type='"+categoryType+"' order by node_name"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        def json = [secondLevelCategories:results]
        render json as JSON
    }

    @Transactional
    def getSellersForCategory() {
        String browseNodeId = params.browseNodeId
        //collect categLevel2 nodes
        String sql = "select group_concat(browse_node_id) from category_level2 where parent_id='" + browseNodeId + "'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        String categLevel2Nodes = results[0][0]

        //collect categLevel3 nodes
        sql = "select group_concat(browse_node_id) from category_level3 where parent_id in (" + categLevel2Nodes + ")"
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        sql1 = new Sql(dataSource)
        results = sql1.rows(sql);
        String categLevel3Nodes = results[0][0]

        //collect categLevel4 nodes
        sql = "select group_concat(browse_node_id) from category_level4 where parent_id in (" + categLevel3Nodes + ")"
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        sql1 = new Sql(dataSource)
        results = sql1.rows(sql);
        String categLevel4Nodes = results[0][0]


        String allCategoryIds = categLevel2Nodes +( (!"".equals(categLevel3Nodes)&&categLevel3Nodes!=null) ? "," + categLevel3Nodes : "") + ((!"".equals(categLevel4Nodes)&&categLevel4Nodes!=null) ? "," + categLevel4Nodes : "")

        //now collect sellers
        sql = "select group_concat(publisher) from print_books_mst where category_id in (" + allCategoryIds + ")"
        dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        sql1 = new Sql(dataSource)
        results = sql1.rows(sql);
        String sellerIds = results[0][0]
        if(sellerIds!=null&&sellerIds.lastIndexOf(',')==(sellerIds.length()-1)) sellerIds =sellerIds.substring(0,(sellerIds.length()-1))

        //now get seller details and something
        if(sellerIds==null){
            def json = [sellers: []]
            render json as JSON
        }
        else {
            String addlCondition=""
            if("Updated".equals(params.filterOption)) addlCondition =" and description is not null "
            else if("Notupdated".equals(params.filterOption)) addlCondition = " and description is null "
            sql = "select id,name,description from ext_publishers where id in (" + sellerIds + ") " +addlCondition+
                    " order by name"
            dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql);
            def json = [sellers: results]
            render json as JSON
        }

    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def updateSellerDescription(){
        ExtPublishers extPublishers = ExtPublishers.findById(new Integer(params.sellerId))
        extPublishers.description = params.description
        extPublishers.save(failOnError: true, flush: true)
        def json = [status:"updated"]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def searchResults(){
        String sql = "SELECT s.search_string,s.results_found,s.username,sm.site_name,s.date_created FROM search s , site_mst sm where sm.id=s.site_id order by s.id desc limit 200"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        [searchResults:results]
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def categoryManager(){
        String sql = "select distinct(category_type) category_type from category_level1 order by category_type"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        [levelsMstList: LevelsMst.findAllBySiteId(new Integer(1)),topLevelCategories:results]
    }

    @Transactional
    def getLevel2Categories(){
        String browseNodeId = params.browseNodeId
        String sql = "select node_name nodeName,browse_node_id browseNodeId,syllabus_id,grade_id,subject_id from category_level2 where parent_id='"+browseNodeId+"' order by node_name"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        String mappedCategory=""
        List level2Categories = results.collect { categ ->
            mappedCategory=""
            if(categ.syllabus_id!=null){
                LevelSyllabus levelSyllabus = LevelSyllabus.findById(new Integer(categ.syllabus_id))
                mappedCategory = levelSyllabus.level+"->"+levelSyllabus.syllabus
            }
            else if(categ.grade_id!=null){
                SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findById(new Integer(categ.grade_id))
                mappedCategory =  syllabusGradeDtl.syllabus+"->"+syllabusGradeDtl.grade
            }
            else if(categ.subject_id!=null){
                SyllabusSubject syllabusSubject = SyllabusSubject.findById(new Integer(categ.subject_id))
                mappedCategory = syllabusSubject.syllabus+"->"+syllabusSubject.subject
            }
            return [nodeName: categ.nodeName, browseNodeId: categ.browseNodeId,mappedCategory:mappedCategory]
        }
        def json = [level2Categories:level2Categories]
        render json as JSON
    }

    @Transactional
    def getLevel3Categories(){
        String browseNodeId = params.browseNodeId
        String sql = "select node_name nodeName,browse_node_id browseNodeId,syllabus_id,grade_id,subject_id from category_level3 where parent_id='"+browseNodeId+"' order by node_name"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        String mappedCategory=""
        List level3Categories = results.collect { categ ->
            mappedCategory=""
            if(categ.syllabus_id!=null){
                LevelSyllabus levelSyllabus = LevelSyllabus.findById(new Integer(categ.syllabus_id))
                mappedCategory = levelSyllabus.level+"->"+levelSyllabus.syllabus
            }
            else if(categ.grade_id!=null){
                SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findById(new Integer(categ.grade_id))
                mappedCategory =  syllabusGradeDtl.syllabus+"->"+syllabusGradeDtl.grade
            }
            else if(categ.subject_id!=null){
                SyllabusSubject syllabusSubject = SyllabusSubject.findById(new Integer(categ.subject_id))
                mappedCategory = syllabusSubject.syllabus+"->"+syllabusSubject.subject
            }
            return [nodeName: categ.nodeName, browseNodeId: categ.browseNodeId,mappedCategory:mappedCategory]
        }
        def json = [level3Categories:level3Categories]
        render json as JSON
    }

    @Transactional
    def getLevel4Categories(){
        String browseNodeId = params.browseNodeId
        String sql = "select node_name nodeName,browse_node_id browseNodeId,syllabus_id,grade_id,subject_id from category_level4 where parent_id='"+browseNodeId+"' order by node_name"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsoutsideproducts')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);
        String mappedCategory=""
        List level4Categories = results.collect { categ ->
            mappedCategory=""
            if(categ.syllabus_id!=null){
                LevelSyllabus levelSyllabus = LevelSyllabus.findById(new Integer(categ.syllabus_id))
                mappedCategory = levelSyllabus.level+"->"+levelSyllabus.syllabus
            }
            else if(categ.grade_id!=null){
                SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findById(new Integer(categ.grade_id))
                mappedCategory =  syllabusGradeDtl.syllabus+"->"+syllabusGradeDtl.grade
            }
            else if(categ.subject_id!=null){
                SyllabusSubject syllabusSubject = SyllabusSubject.findById(new Integer(categ.subject_id))
                mappedCategory = syllabusSubject.syllabus+"->"+syllabusSubject.subject
            }
            return [nodeName: categ.nodeName, browseNodeId: categ.browseNodeId,mappedCategory:mappedCategory]
        }
        def json = [level4Categories:level4Categories]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def updateAffiliationCategory(){
        String categoryLevel = params.categoryLevel
        String browseNodeId = params.browseNodeId
        String level = params.level
        String syllabus = params.syllabus
        String grade = params.grade
        String subject = params.subject
        String syllabusId=null,gradeId=null,subjectId=null
        String status = "";

        //see if it as at the subject level
        if(subject!=null&&!"".equals(subject)&&!"null".equals(subject)){
            SyllabusSubject syllabusSubject = SyllabusSubject.findBySyllabusAndSubjectAnd(syllabus,subject)
            if(syllabusSubject!=null) subjectId = ""+syllabusSubject.id
        }
        else if(grade!=null&&!"".equals(grade)&&!"null".equals(grade)){
            SyllabusGradeDtl syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGrade(syllabus,grade)
            if(syllabusGradeDtl!=null) gradeId = ""+syllabusGradeDtl.id
        }
        else if(syllabus!=null&&!"".equals(syllabus)&&!"null".equals(syllabus)){
            LevelSyllabus levelSyllabus = LevelSyllabus.findByLevelAndSyllabusAndSiteId(level,syllabus,new Integer(1))
            if(levelSyllabus!=null) syllabusId = ""+levelSyllabus.id
        }else{
            status = "No Wonderslate category matched."
        }
        println("subjectId=${subjectId} gradeId=${gradeId} syllabusId=${syllabusId}")

        def categoryLevelTable=null
        if("2".equals(categoryLevel)){
            categoryLevelTable = CategoryLevel2.findByBrowseNodeId(browseNodeId)
        }
        else if("3".equals(categoryLevel)){
            categoryLevelTable = CategoryLevel3.findByBrowseNodeId(browseNodeId)
        }
        else if("4".equals(categoryLevel)){
            categoryLevelTable = CategoryLevel4.findByBrowseNodeId(browseNodeId)
        }
        if(categoryLevelTable!=null){
            categoryLevelTable.subjectId = subjectId!=null?new Integer(subjectId):null
            categoryLevelTable.gradeId = gradeId!=null?new Integer(gradeId):null
            categoryLevelTable.syllabusId = syllabusId!=null?new Integer(syllabusId):null
            categoryLevelTable.save(failOnError: true, flush: true)
            status = "Afflilation category updated"
        }else{
            status = "No affiliation status matched"
        }
        def json = [status: status]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def addCollegeSemesters()
    {
        String syllabus = params.syllabus
        SyllabusGradeDtl syllabusGradeDtl
        String status="Not added"
        for(int i=1;i<9;i++){
            syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGrade(syllabus,"Semester "+i)
            if(syllabusGradeDtl==null){
                syllabusGradeDtl = SyllabusGradeDtl.findBySyllabusAndGrade(syllabus,""+i)
                if(syllabusGradeDtl==null){
                    syllabusGradeDtl = new SyllabusGradeDtl(siteId: new Integer("1"),syllabus: syllabus,grade: "Semester "+i,sort: new Integer(0))
                    syllabusGradeDtl.save(failOnError: true, flush: true)
                    status = "Added"
                }
            }
        }
        render status
    }



}
