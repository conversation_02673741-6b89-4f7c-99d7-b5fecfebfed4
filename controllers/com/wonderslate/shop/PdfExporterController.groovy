package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksDtl
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ContentDeliveryService
import com.wonderslate.data.WpmainService
import com.wonderslate.data.BooksMst
import grails.plugin.springsecurity.annotation.Secured
import groovy.json.JsonSlurper
import grails.transaction.Transactional

import javax.servlet.http.HttpServletResponse
import grails.converters.JSON

class PdfExporterController {

    PdfExporterService pdfExporterService
    WpmainService wpmainService
    DataProviderService dataProviderService
    def redisService
    ContentDeliveryService contentDeliveryService

    def chapterPdf() {
        // No data needed - GSP will fetch via AJAX
        // Default chapter ID for testing if not provided
        Long chapterIdLong = Long.parseLong(params.chapterId)
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterIdLong)

        // Get all required data using existing WpmainService methods
        def exerciseSolutions = wpmainService.getExerciseSolutions(chapterIdLong)
        def questionBankData = wpmainService.getQuestionBankData(chapterIdLong)
        def questionTypeCounts = wpmainService.getQuestionTypeCounts(chapterIdLong)

      [
                chapterName: "${chaptersMst.name}",
                exerciseSolutions: exerciseSolutions ?: [],
                questionBank: questionBankData ?: [:],
                questionTypeCounts: questionTypeCounts ?: [:]
        ]
    }

    def exportBookTitlePdf(){
        println("started exportChapterPdf")
        Long bookId = params.bookId as Long

        try {
            File uploadDir =  new File(grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+bookId)
            if(!uploadDir.exists()) uploadDir.mkdirs()
            String htmlPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+bookId+"/book_"+bookId+".html"
            String pdfPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+bookId+"/book_"+bookId+".pdf"
            println("htmlPath="+htmlPath)
            BooksMst booksMst = dataProviderService.getBooksMst(bookId)
            def file = pdfExporterService.renderBookTitleHtml(
                    ""+params.bookId,
                    htmlPath,"/pdfExporter/bookTitle"
            )
            // Generate PDF
            boolean success = pdfExporterService.generatePdf(htmlPath, pdfPath)
            if (success) {
                File pdfFile = new File(pdfPath)
                if (pdfFile.exists()) {
                    response.setContentType('application/pdf')
                    response.setHeader('Content-Disposition', "attachment; filename=\"book_${bookId}.pdf\"")
                    response.outputStream << pdfFile.bytes
                    response.outputStream.flush()

                    // Clean up temporary files
                    //   htmlFile.delete()
                    //   pdfFile.delete()
                } else {
                    render status: 500, text: "PDF file not generated"
                }
            } else {
                render status: 500, text: "PDF generation failed"
            }
        } catch (Exception e) {
            log.error("Error generating chapter PDF: ${e.message}", e)
            render status: 500, text: "Error generating PDF: ${e.message}"
        }
        render "Done"
    }

    def exportChapterPdf() {
        println("started exportChapterPdf")
        Long chapterId = params.chapterId as Long
        if (!chapterId) {
            render status: 400, text: "Chapter ID is required"
            return
        }

        try {
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
            File uploadDir =  new File(grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+chaptersMst.bookId)
            if(!uploadDir.exists()) uploadDir.mkdirs()
			String htmlPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+chaptersMst.bookId+"/chapter_"+chapterId+".html"
			String pdfPath = grailsApplication.config.grails.basedir.path+"supload/bookspdf/"+""+chaptersMst.bookId+"/chapter_"+chapterId+".pdf"
            println("htmlPath="+htmlPath)
            def file = pdfExporterService.renderSampleToHtmlFile(
                    chaptersMst.name,
                    htmlPath,"/pdfExporter/chapterPdf",params.chapterId
            )
            // Generate PDF
            boolean success = pdfExporterService.generatePdf(htmlPath, pdfPath)
            if (success) {
                File pdfFile = new File(pdfPath)
                if (pdfFile.exists()) {
                    response.setContentType('application/pdf')
                    response.setHeader('Content-Disposition', "attachment; filename=\"chapter_${chapterId}.pdf\"")
                    response.outputStream << pdfFile.bytes
                    response.outputStream.flush()

                    // Clean up temporary files
                 //   htmlFile.delete()
                 //   pdfFile.delete()
                } else {
                    render status: 500, text: "PDF file not generated"
                }
            } else {
                render status: 500, text: "PDF generation failed"
            }
        } catch (Exception e) {
            log.error("Error generating chapter PDF: ${e.message}", e)
            render status: 500, text: "Error generating PDF: ${e.message}"
        }
        render "Done"
    }

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def bookTitle() {
        String bookId = params.bookId
        if (!bookId) {
            render status: 400, text: "Book ID is required"
            return
        }

        try {
            // Get book details using the same methods as aiBookDtl
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))

            // Get chapters list for book overview
            if (redisService.("chapters_" + bookId) == null) {
                dataProviderService.getChaptersList(new Long(bookId))
            }
            List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))

            // Get book overview data for all chapters
            def bookOverviewData = wpmainService.getBookOverviewData(new Long(bookId), chaptersList)

            // Get book-level summary
            def bookLevelSummary = wpmainService.getBookLevelSummary(new Long(bookId), chaptersList)

            [
                booksMst: booksMst,
                bookOverviewData: bookOverviewData,
                bookLevelSummary: bookLevelSummary,
                chaptersList: chaptersList
            ]
        } catch (Exception e) {

            render status: 500, text: "Error loading book data: ${e.message}"
        }
    }

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def generateBookPdfFiles(){
        String filePath = pdfExporterService.generateBookPdfFiles(params.bookId)
        def json = [status:"Success",filePath:filePath]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def downloadPDF(){
        println("downloadPDF")
        try {
            BooksDtl booksDtl = BooksDtl.findByBookId(new Long(params.bookId))
            String cdnLink = contentDeliveryService.generateSignedURL(grailsApplication.config.grails.cdn.pdf.path + booksDtl.pdfPath)
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY)
            response.setHeader("Location", cdnLink)
        }catch (Exception e) {
            println("Exception in downloadPDF " + e.toString())
            render ""
        }
    }

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def theoryChapter() {
        Long chapterId = params.chapterId as Long
        if (!chapterId) {
            render status: 400, text: "Chapter ID is required"
            return
        }

        try {
            // Get chapter details to find bookId
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
            if (!chaptersMst) {
                render status: 404, text: "Chapter not found"
                return
            }

            // Construct the path to the theory chapter HTML file
            String chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chaptersMst.bookId + "/chapter_" + chapterId + ".html"

            // Read the HTML content
            File chapterFile = new File(chapterFilePath)
            String chapterContent = ""
            if (chapterFile.exists()) {
                chapterContent = chapterFile.text
            }

            [
                chaptersMst: chaptersMst,
                chapterContent: chapterContent,
                chapterFilePath: chapterFilePath
            ]
        } catch (Exception e) {
            log.error("Error loading theory chapter: ${e.message}", e)
            render status: 500, text: "Error loading chapter: ${e.message}"
        }
    }




}
