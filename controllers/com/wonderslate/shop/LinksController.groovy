package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SiteMst
import grails.converters.JSON
import grails.transaction.Transactional


class LinksController {
     LinksService linksService
    DataProviderService dataProviderService

    @Transactional
    def affilMaster(){
        AffiliationMst affiliationMst = linksService.getAffiliationMst(params.utm_content)
        if(affiliationMst!=null){
            SiteMst siteMst = dataProviderService.getSiteMst(affiliationMst.siteId)
             String userAgent = request.getHeader("User-agent");
             if(userAgent.indexOf("Android")!=-1&&siteMst.playStoreInstallUrl!=null){
                 String url = siteMst.playStoreInstallUrl
                 url += "&referrer=utm_source%3Dsample-source%26utm_campaign%3Daffiliation%26utm_content%3D"+params.utm_content
                 redirect(url:url)
             }
            else {
                 if (affiliationMst.siteId.intValue() == 1) {
                     redirect(controller: 'books', action: 'ebooks', params: [affiliationCd: affiliationMst.affiliationCd])

                 } else {

                     if ("true".equals(siteMst.prepjoySite)) {
                         redirect(url: "/" + siteMst.siteName + "/eBooks?affiliationCd=" + affiliationMst.affiliationCd)
                     }
                 }
             }

        }else{
            redirect(action: '/prepjoy')
        }
    }

    def getAffliationDeepLink(){
       String link = linksService.getAffliationDeepLink(params.affiliationCd)
       def json = [link:link]
        render json as JSON
    }

    def accessCode(){
        if(params.siteId!=null) {
            SiteMst siteMst = dataProviderService.getSiteMst(new Integer(params.siteId))
            String userAgent = request.getHeader("User-agent");
            if (userAgent.indexOf("Android") != -1 && siteMst.playStoreInstallUrl != null) {
                String url = siteMst.playStoreInstallUrl
                url += "&referrer=utm_source%3Dsample-source%26utm_campaign%3Daccesscode%26utm_content%3Daccesscode"
                println("url=" + url)
                redirect(url: url)
            }else{
                redirect(url: '/arihant')
            }
        }else{
            def json = ['link':'Invalid url']
            render json as JSON
        }
    }

    def getAccessCodeDeepLink(){
        String link = linksService.getAccessCodeDeepLink(params.siteId)
        def json = [link:link]
        render json as JSON
    }

    def test(){

    }

}
