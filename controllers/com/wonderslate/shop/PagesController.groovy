package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ResourceDtl
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class PagesController {
    PagesService pagesService
    def springSecurityService
    def redisService
    DataProviderService dataProviderService

    @Transactional @Secured(['ROLE_USER'])
    def addPage(String pageName, Integer siteId, String username){
        String status = pagesService.addPage(params.pageName,siteId,springSecurityService.currentUser.username)
        def json = [status:status!='exists'?'OK':status,pageId:status]
        render json as JSO<PERSON>
    }

    def addPageDetails(String pageName){
        String status = pagesService.addPageDetails(params,session)

        def json = [status:status]

        redirect(controller: 'privatelabel', action: 'pageManager')
    }

    @Transactional
    def getAllPagesList(){
        List pageList = pagesService.getAllPagesForSite(session)
        def json = [pageList:pageList]
        render json as J<PERSON><PERSON>
    }

    @Transactional
    def getPageContent(){
        PagesMst pagesMstInstance = PagesMst.findById(new Integer(params.pageId))
        if(pagesMstInstance != null && pagesMstInstance.status=='published') {

            def filename = pagesMstInstance.resLink.substring(0,
                    (pagesMstInstance.fileName.toLowerCase().endsWith(".pdf") || pagesMstInstance.fileName.toLowerCase().endsWith(".zip") ||
                            pagesMstInstance.fileName.toLowerCase().endsWith(".epub")?pagesMstInstance.resLink.lastIndexOf("."):pagesMstInstance.resLink.length()))+".ws"

            def file =  new File(filename)
            boolean fileExists = false
            if(file.exists()) fileExists = true
            else{
                //for the copied resource case
                filename = pagesMstInstance.resLink.substring(0,pagesMstInstance.resLink.lastIndexOf('/'))+"/"+pagesMstInstance.filename+".ws"
                file  = new File(filename)
                if(file.exists()) fileExists = true
            }

            if(fileExists) {
                //def startTime = new Date()
                response.setContentType("text/html;charset=UTF-8")
                response.setHeader("Content-Disposition", "inline;Filename=\"${file.getName().replaceAll(".ws",".html")}\"")
                response.setHeader("Content-Length", "${file.length()}")

                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096]
                int len
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len)
                }

                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            } else {
            }
        }else{
            redirect(controller: 'privatelabel', action: 'index')
            render ""
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def publishUnpublishSite(){
        def status  = pagesService.updateStatus(params)
        session["customPageMenus_" + session["siteId"]] = redisService.("customPageMenus_" + session["siteId"])
        render status as JSON
    }

    def uploadContent() {
        def file = request.getFile('upload')
        def pageId,htmlId,resLink

        if(session.getAttribute("htmlId")==null) {
            def pageInstance = new PagesMst()
            htmlId = pageInstance.id
            session.setAttribute("htmlId",htmlId)
        } else {
            htmlId = session.getAttribute("htmlId")
        }

        resLink = "upload/pages/"+session['siteId']+"/"+htmlId+"/extract/OEBPS/Images/"+file.originalFilename
        if(file!=null && !file.empty) {

            File uploadDir

            uploadDir = new File(grailsApplication.config.grails.basedir.path+"upload/pages/"+session['siteId']+"/"+htmlId+"/extract/OEBPS/Images/")
            if(!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path+resLink))
        }

        def json =  [
                "uploaded": 1,
                "fileName": file.originalFilename,
                "url": '/funlearn/downloadEpubImage?source='+resLink
        ]

        render json as JSON
    }

}
