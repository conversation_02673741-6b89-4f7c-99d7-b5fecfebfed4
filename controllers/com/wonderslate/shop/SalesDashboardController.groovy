package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.sql.Sql
import java.time.LocalDate
import java.time.Month
import java.time.format.DateTimeFormatter

import com.wonderslate.data.SiteMst


class SalesDashboardController {
    DataProviderService dataProviderService

    @Secured(['ROLE_FINANCE','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def index() {
        // Get the list of months in descending order from current month till the month the sale started
        def currentMonth = LocalDate.now().withDayOfMonth(1)
        def saleStartDate = LocalDate.of(2020, Month.JANUARY, 1)
        def months = []
        while (currentMonth.isAfter(saleStartDate) || currentMonth.isEqual(saleStartDate)) {
            months.add(currentMonth.format(DateTimeFormatter.ofPattern("MMMM yyyy")))
            currentMonth = currentMonth.minusMonths(1)
        }

        [months: months,selectedMonth:currentMonth]
    }

    @Secured(['ROLE_FINANCE','ROLE_WS_CONTENT_ADMIN']) @Transactional
    def filterData() {
        def selectedMonth = params.selectedMonth
        def section1Data = retrieveFilteredSalesByDay(selectedMonth)
        def section2Data = retrieveFilteredPublisherSales(selectedMonth)
        def section3Data = retrieveFilteredLevelSyllabusSales(selectedMonth)
        def section4Data = retrieveFilteredSiteSales(selectedMonth)

        def json=    [
                    section1Data: section1Data,
                    section2Data: section2Data,
                    section3Data: section3Data,
                    section4Data: section4Data
            ]
        render json as JSON

    }


    private List<Map<String, Object>> retrieveFilteredSalesByDay(String selectedMonth) {
        def sql = new Sql(grailsApplication.mainContext.getBean('dataSource_wsshop'))
        def startDate
        def endDate

        if (selectedMonth) {
            def trimmedMonthYear = selectedMonth.trim()
            def monthYear = trimmedMonthYear.split(" ")
            if (monthYear.size() == 2) {
                def month = monthYear[0].trim().toUpperCase()
                def year = monthYear[1].trim().toUpperCase()
                def validMonth = Month.values().find { it.name().toUpperCase() == month }
                if (validMonth) {
                    def parsedYear = tryParseInt(year)
                    if (parsedYear != null) {
                        def monthNumber = validMonth.getValue()
                        def firstDayOfMonth = LocalDate.of(parsedYear, monthNumber, 1)
                        def lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth())

                        startDate = firstDayOfMonth
                        endDate = lastDayOfMonth
                    }
                }
            }
        }


        if (!startDate) {
            startDate = LocalDate.now().withDayOfMonth(1)
            endDate = LocalDate.now().plusDays(1)
        }

        def query = """
            SELECT DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) AS date,
                   COUNT(*) AS booksSold,
                   SUM(amount) AS salesValue
            FROM purchase_order
            WHERE DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) BETWEEN :startDate AND :endDate
            GROUP BY DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE))
            ORDER BY DATE(DATE_ADD(date_created, INTERVAL '5:30' HOUR_MINUTE)) desc
        """
        def result = sql.rows(query, [startDate: startDate.format(DateTimeFormatter.ISO_DATE), endDate: endDate.format(DateTimeFormatter.ISO_DATE)])
        sql.close()
        return result
    }

    private List<Map<String, Object>> retrieveFilteredPublisherSales(String selectedMonth) {
        def sql = new Sql(grailsApplication.mainContext.getBean('dataSource_wsshop'))
        def startDate
        def endDate

        if (selectedMonth) {
            def trimmedMonthYear = selectedMonth.trim()
            def monthYear = trimmedMonthYear.split(" ")
            if (monthYear.size() == 2) {
                def month = monthYear[0].trim().toUpperCase()
                def year = monthYear[1].trim().toUpperCase()
                def validMonth = Month.values().find { it.name().toUpperCase() == month }
                if (validMonth) {
                    def parsedYear = tryParseInt(year)
                    if (parsedYear != null) {
                        def monthNumber = validMonth.getValue()
                        def firstDayOfMonth = LocalDate.of(parsedYear, monthNumber, 1)
                        def lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth())

                        startDate = firstDayOfMonth
                        endDate = lastDayOfMonth
                    }
                }
            }
        }

        if (!startDate) {
            startDate = LocalDate.now().withDayOfMonth(1)
            endDate = LocalDate.now().plusDays(1)
        }

        def query = """
            SELECT p.name AS publisher,
                   COUNT(*) AS booksSold,
                   SUM(po.amount) AS salesValue
            FROM purchase_order po
            INNER JOIN books_mst bm ON po.item_code = bm.id
            INNER JOIN publishers p ON bm.publisher_id = p.id
            WHERE DATE(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) BETWEEN :startDate AND :endDate
            GROUP BY p.name
            ORDER BY salesValue desc
        """
        def result = sql.rows(query, [startDate: startDate.format(DateTimeFormatter.ISO_DATE), endDate: endDate.format(DateTimeFormatter.ISO_DATE)])

        sql.close()
        return result
    }

    private List<Map<String, Object>> retrieveFilteredLevelSyllabusSales(String selectedMonth) {
        def sql = new Sql(grailsApplication.mainContext.getBean('dataSource_wsshop'))
        def startDate
        def endDate

        if (selectedMonth) {
            def trimmedMonthYear = selectedMonth.trim()
            def monthYear = trimmedMonthYear.split(" ")
            if (monthYear.size() == 2) {
                def month = monthYear[0].trim().toUpperCase()
                def year = monthYear[1].trim().toUpperCase()
                def validMonth = Month.values().find { it.name().toUpperCase() == month }
                if (validMonth) {
                    def parsedYear = tryParseInt(year)
                    if (parsedYear != null) {
                        def monthNumber = validMonth.getValue()
                        def firstDayOfMonth = LocalDate.of(parsedYear, monthNumber, 1)
                        def lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth())

                        startDate = firstDayOfMonth
                        endDate = lastDayOfMonth
                    }
                }
            }
        }

        if (!startDate) {
            startDate = LocalDate.now().withDayOfMonth(1)
            endDate = LocalDate.now().plusDays(1)
        }

        def query = """
            SELECT btd.level,
                   btd.syllabus,
                   COUNT(*) AS booksSold
            FROM purchase_order po
            INNER JOIN books_mst bm ON po.item_code = bm.id
            INNER JOIN books_tag_dtl btd ON bm.id = btd.book_id
            WHERE DATE(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) BETWEEN :startDate AND :endDate
            GROUP BY btd.level, btd.syllabus
            ORDER BY booksSold desc
        """
        def result = sql.rows(query, [startDate: startDate.format(DateTimeFormatter.ISO_DATE), endDate: endDate.format(DateTimeFormatter.ISO_DATE)])

        sql.close()
        return result
    }

    private List<Map<String, Object>> retrieveFilteredSiteSales(String selectedMonth) {
        def sql = new Sql(grailsApplication.mainContext.getBean('dataSource_wsshop'))
        def startDate
        def endDate

        if (selectedMonth) {
            def trimmedMonthYear = selectedMonth.trim()
            def monthYear = trimmedMonthYear.split(" ")
            if (monthYear.size() == 2) {
                def month = monthYear[0].trim().toUpperCase()
                def year = monthYear[1].trim().toUpperCase()
                def validMonth = Month.values().find { it.name().toUpperCase() == month }
                if (validMonth) {
                    def parsedYear = tryParseInt(year)
                    if (parsedYear != null) {
                        def monthNumber = validMonth.getValue()
                        def firstDayOfMonth = LocalDate.of(parsedYear, monthNumber, 1)
                        def lastDayOfMonth = firstDayOfMonth.withDayOfMonth(firstDayOfMonth.lengthOfMonth())

                        startDate = firstDayOfMonth
                        endDate = lastDayOfMonth
                    }
                }
            }
        }

        if (!startDate) {
            startDate = LocalDate.now().withDayOfMonth(1)
            endDate = LocalDate.now().plusDays(1)
        }

        def query = """
            SELECT po.site_id,
                   COUNT(*) AS booksSold,
                   SUM(po.amount) AS salesValue
            FROM purchase_order po
            WHERE DATE(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) BETWEEN :startDate AND :endDate
            GROUP BY po.site_id
            ORDER BY salesValue desc
        """
        def result = sql.rows(query, [startDate: startDate.format(DateTimeFormatter.ISO_DATE), endDate: endDate.format(DateTimeFormatter.ISO_DATE)])

        sql.close()
        List siteOrders = result.collect{ order ->
            SiteMst siteMst = dataProviderService.getSiteMst(new Integer(""+order.site_id))
            return [booksSold:order.booksSold, salesValue:order.salesValue, site_id:order.site_id,siteName:siteMst.siteName]
        }
        return siteOrders
    }

    Integer tryParseInt(String value) {
        try {
            return Integer.parseInt(value)
        } catch (NumberFormatException e) {
            return null
        }
    }

}
