package com.wonderslate.shop

import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class AmazonController {
    AmazonService amazonService
    WsshopService wsshopService
    def checkAmazonOrder(){
        amazonService.downloadAmazonOrder()
        render "Done Swami"
    }

    def amazonOrder(){

    }

    @Secured(['ROLE_USER']) @Transactional
    def addBookForAmazonOrder(){
        def json
      if(params.amazonOrderId!=null){
         json = ["status":wsshopService.addBookForAmazonOrder(params.amazonOrderId)]
      }
        else{
           json = ["status":"Order id not provided"]
      }
        render json as JSON
    }


}
