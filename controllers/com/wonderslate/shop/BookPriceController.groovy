package com.wonderslate.shop

import grails.converters.JSON
import grails.transaction.Transactional

class BookPriceController {

    BookPriceService bookPriceService

    def migrateBookPrices(){
        bookPriceService.migrateBookPrices()
    }

    @Transactional
    def getBookAllPrices(){
        List bookPrices = BookPriceDtl.findAllByBookId(new Integer(params.bookId))
        def json = [bookPrices:bookPrices]
        render json as JSON
    }

    @Transactional
    def addBookPrice(){
        bookPriceService.addBookPrice(params)
        List bookPrices = BookPriceDtl.findAllByBookId(new Integer(params.bookId))
        def json = [bookPrices:bookPrices]
        render json as JSON
    }

    @Transactional
    def updateBookPrice(){
        bookPriceService.updateBookPrice(params)
        List bookPrices = BookPriceDtl.findAllByBookId(new Integer(params.bookId))
        def json = [bookPrices:bookPrices]
        render json as JSO<PERSON>
    }

    @Transactional
    def deleteBookPrice(){
        Integer bookId =bookPriceService.removeBookPrice(new Integer(params.bookPriceId))
        List bookPrices = BookPriceDtl.findAllByBookId(bookId)
        def json = [bookPrices:bookPrices]
        render json as JSON
    }
}
