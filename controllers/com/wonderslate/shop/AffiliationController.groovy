package com.wonderslate.shop

import com.ibookso.products.ExtPublishers
import com.ibookso.products.PrintBooksMst
import com.wonderslate.data.BooksAffiliationDtl
import com.wonderslate.data.PrintBooksService
import com.wonderslate.data.UtilService
import grails.converters.JSON
import grails.transaction.Transactional
import groovy.json.JsonSlurper






class AffiliationController {

    AffiliationService affiliationService
    def redisService
    PrintBooksService printBooksService
    UtilService utilService


    @Transactional
    def getAffiliationPrices(){
        Integer siteId = utilService.getSiteId(request,session)
        Integer bookId = new Integer(params.bookId)
        BooksAffiliationDtl booksAffiliationDtl = affiliationService.getBooksAffiliationDtl(bookId,params.bookType)
        if(booksAffiliationDtl==null){
            booksAffiliationDtl = affiliationService.getAffiliationPrices(bookId,params.bookType)
        }
        def json
        if(booksAffiliationDtl==null){
            json = [status:"No data found"]
        }else{
            String amazonLink = booksAffiliationDtl.amazonLink
            if(amazonLink!=null&&!"".equals(amazonLink)){
                if(siteId.intValue()==27) amazonLink= amazonLink.replace("wonderslate-21","prepjoy-21")
                else if(siteId.intValue()==64) amazonLink = amazonLink.replace("wonderslate-21","ibookso-21")
            }
            json =[status:"OK",amazonPrice:booksAffiliationDtl.amazonPrice,amazonLink:amazonLink,flipkartPrice:booksAffiliationDtl.flipkartPrice,flipkartLink:booksAffiliationDtl.flipkartLink,
            ratings:booksAffiliationDtl.ratings,reviews:booksAffiliationDtl.reviews]
        }
        render json as JSON
    }

    def testFlipkart() {
        def  baseUrl = new URL('https://affiliate-api.flipkart.net/affiliate/1.0/search.json?query=' +  URLEncoder.encode(params.searchKey, "UTF-8") + '&resultCount=1')
        HttpURLConnection connection = (HttpURLConnection) baseUrl.openConnection()
        def res;
        connection = (HttpURLConnection) baseUrl.openConnection()
        connection.addRequestProperty("Content-type", "application/x-www-form-urlencoded")
        connection.addRequestProperty("Fk-Affiliate-Id", "wonderslate")
        connection.addRequestProperty("Fk-Affiliate-Token", "3c706e064a724c49bc4c9482de6dc99f")
        connection.with {
            doOutput = true
            requestMethod = 'GET'
            res = content.text


        }
        def booksReceived = new JsonSlurper().parseText(affiliationService.addDoubleQuotes(res))
        def books = booksReceived.products

        if (books.size() > 0) {
            println(books[0].productBaseInfoV1.flipkartSpecialPrice.amount)
            println(new Double("" + books[0].productBaseInfoV1.flipkartSpecialPrice.amount))

        }
        render res
    }
    def testAmazon() {
        def  baseUrl = new URL('https://ws-in.amazon-adsystem.com/widgets/q?Operation=GetResults&' +
                'Keywords=' + URLEncoder.encode(params.searchKey, "UTF-8") + // keywords parameter should contain the search string in url encoded form
                '&SearchIndex=Books&multipageStart=0&InstanceId=0&multipageCount=1&TemplateId=MobileSearchResults&ServiceVersion=20070822&MarketPlace=IN')
        HttpURLConnection connection = (HttpURLConnection) baseUrl.openConnection()
        def res
        try {
            connection = (HttpURLConnection) baseUrl.openConnection()
            connection.addRequestProperty("Content-type", "application/x-www-form-urlencoded")
            connection.with {
                doOutput = true
                requestMethod = 'GET'
                res = content.text

                res = res.substring(17, res.length() - 2) // to get the data string
            }
            }catch(Exception e){
                println("amazon exception "+e.toString())
            }
        render res
            }

        def amazonSearch(){

            def json = ["results":affiliationService.amazonSearch(params.searchKey)]
            render json as JSON

        }

    def getAmazonProducts(){
        def json = ["results":affiliationService.getAmazonProducts(params.searchKey)]
        render json as JSON
    }

    def getAmazonNodes(){
        def json = ["results":affiliationService.getBrowseInfo()]
        render json as JSON
    }

    @Transactional
    def getAllBooks(){
        affiliationService.getBooksAndUpdate(params.categoryType)
        render "done"
    }

   def  populateProductCategories(){
        affiliationService.populateProductCategories(params.categoryType,params.rootId,params.step)
        render "done "+params.categoryType+" step "+params.step
    }

    def initMethod(){
        affiliationService.initMethod()
        //somethg
        render "done"
    }

    @Transactional
    def getPrintRelatedBooks(){
        PrintBooksMst printBooksMst = printBooksService.getPrintBooksMst(new Integer(params.bookId))
      if(redisService.("relatedPrintBooks_"+printBooksMst.categoryId)==null) printBooksService.getPrintRelatedBooks(printBooksMst.categoryId)
        def books = redisService.("relatedPrintBooks_"+printBooksMst.categoryId)
        def json = [
                'books':books,
                'status' : books ? "OK" : "Nothing present"
        ]
        render json as JSON
    }

    @Transactional
    def updatePublisherUrls(){
        println("hello")
        List extPubs = ExtPublishers.findAllByUrlIsNull()
//
        extPubs.each {extPub->
            affiliationService.updatePublisherDomain(extPub)
        }
        render "total updated="+extPubs.size()
    }
}
