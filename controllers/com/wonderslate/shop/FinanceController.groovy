package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.UtilService
import com.wonderslate.publish.Publishers
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import pl.touk.excel.export.WebXlsxExporter

class FinanceController {
    UtilService utilService
    DataProviderService dataProviderService
    def redisService
    def index() { }

    @Secured(['ROLE_ACCOUNTS']) @Transactional
    def salesReportForAccounts() {

        String sql="SELECT \n" +
                "    po.payment_id,\n" +
                "    SUM(CASE WHEN po.book_type IN ('printbook', 'combo') THEN 1 ELSE 0 END) AS num_printbooks,\n" +
                "    SUM(CASE WHEN po.book_type NOT IN ('printbook', 'combo', 'recharge') THEN 1 ELSE 0 END) AS num_ebooks,\n" +
                "    SUM(CASE WHEN po.book_type = 'recharge' THEN 1 ELSE 0 END) AS num_recharges,\n" +
                "    SUM(CASE WHEN po.book_type IN ('printbook', 'combo') THEN po.book_price ELSE 0 END) AS total_value_printbooks,\n" +
                "    SUM(CASE WHEN po.book_type NOT IN ('printbook', 'combo', 'recharge') THEN po.book_price ELSE 0 END) AS total_value_ebooks,\n" +
                "    SUM(CASE WHEN po.book_type = 'recharge' THEN po.book_price ELSE 0 END) AS total_value_recharges,\n" +
                "    MAX(sc.deliver_costs) as deliver_costs,\n" +
                "    (SUM(CASE WHEN po.book_type IN ('printbook', 'combo') THEN po.book_price ELSE 0 END) +\n" +
                "     SUM(CASE WHEN po.book_type NOT IN ('printbook', 'combo', 'recharge') THEN po.book_price ELSE 0 END) +\n" +
                "     SUM(CASE WHEN po.book_type = 'recharge' THEN po.book_price ELSE 0 END) +\n" +
                "     MAX(sc.deliver_costs)) AS total_order_cost\n" +
                "FROM \n" +
                "    purchase_order po\n" +
                "JOIN \n" +
                "    shopping_cart_orders_mst sc ON po.cart_mst_id = sc.id\n" +
                " AND DATE(DATE_ADD(po.date_created," +
                "INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y')\n" +
                "AND DATE(DATE_ADD(po.date_created," +
                "INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y')"+
                "GROUP BY \n" +
                "    po.payment_id\n" +
                "ORDER BY \n" +
                "    MAX(po.id) DESC\n"
        println("sql: " + sql)
        if("submit".equals(params.mode)||"download".equals(params.mode)){
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            if ("download".equals(params.mode)) {
                List headers = ["Payment Id", "Printbooks", "eBooks", "Recharge", "Printbooks Value", "eBooks Value", "Recharge Value", "Delivery charges", "Total Value"]
                List withProperties = ["payment_id", "num_printbooks", "num_ebooks", "num_recharges", "total_value_printbooks", "total_value_ebooks", "total_value_recharges", "deliver_costs", "total_order_cost"]

                def fileName = "Data_" + (params.startDate != "" ? params.startDate + "_" : "FromAny_") +
                        (params.endDate != "" ? params.endDate + "_" : "ToAny_") + (new Random()).nextInt(9999999) + ".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response, fileName)
                    fillHeader(headers)
                    add(results, withProperties)
                    save(response.outputStream)
                }
            }else {
                    def json = [results: results, 'status' : results ? "OK" : "Nothing present"]
                    render json as JSON
                }
        }
        else{
            [title: "Sales Report for Accounts",]
        }

    }

    @Secured(['ROLE_ACCOUNTS']) @Transactional
    def salesReportByPublisher(){
        String sql="SELECT po.id,po.payment_id,DATE_FORMAT(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as date_created,po.amount,sc.deliver_costs,po.book_type,bm.title,bm.id bookId,po.cart_mst_id,po.site_id\n" +
                "FROM purchase_order po,shopping_cart_orders_mst sc,books_mst bm\n" +
                "where   po.cart_mst_id = sc.id\n" +
                "and bm.id=po.item_code\n" +
                "and bm.publisher_id=" +params.publisherId+"\n" +
                "AND DATE(DATE_ADD(po.date_created,INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y')\n" +
                "AND DATE(DATE_ADD(po.date_created,INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y')"
        println("sql: " + sql)
        if("submit".equals(params.mode)||"download".equals(params.mode)){
            Publishers publishers = dataProviderService.getPublisher(new Integer(params.publisherId))
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            if ("download".equals(params.mode)) {
                List headers = ["Po no", "Payment id", "Date Created", "Amount", "Delivery costs", "Book Type", "Book Id",  "Site Id"]
                List withProperties = ["id", "payment_id", "date_created", "amount", "deliver_costs", "book_type", "bookId", "site_id"]

                def fileName = "Data_" + (params.startDate != "" ? params.startDate + "_" : "FromAny_") +
                        (params.endDate != "" ? params.endDate + "_" : "ToAny_") + (new Random()).nextInt(9999999) + ".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response, fileName)
                    fillHeader(headers)
                    add(results, withProperties)
                    save(response.outputStream)
                }
            }else {
                int printBookShare = publishers.printBookShare!=null?publishers.printBookShare:90
                int ebookShareOnWS = publishers.ebookShareOnWS!=null?publishers.ebookShareOnWS:70
                int ebookShareOnWhitelabel = publishers.ebookShareOnWhitelabel!=null?publishers.ebookShareOnWhitelabel:70
                def json = [results: results, 'status' : results ? "OK" : "Nothing present",printBookShare:printBookShare,ebookShareOnWS:ebookShareOnWS,ebookShareOnWhitelabel:ebookShareOnWhitelabel]
                render json as JSON
            }
        }
        else{
            Integer siteId = utilService.getSiteId(request,session)
            List publishers = []
            if (session["userdetails"].publisherId != null) {
                publishers = Publishers.findAllById(session["userdetails"].publisherId)
            } else {
                if (redisService.("publishers_" + siteId) == null)
                    dataProviderService.getPublishers(siteId)

                publishers = new JsonSlurper().parseText(redisService.("publishers_" + siteId))
            }
            [title: "Sales Report For Publishers",publishers:publishers]
        }
    }


}
