package com.wonderslate.shop

import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import grails.converters.JSON

@Transactional
class DeliveryChargesController {
    def springSecurityService
    DeliveryChargesService deliveryChargesService

    @Transactional @Secured(['ROLE_USER'])
    def deliveryChargesCalculator(){
        String userName
        if(springSecurityService.currentUser!=null)  username=springSecurityService.currentUser.username
        else username =""+siteId+"_"+session.getId()+"_temp"

        deliveryChargesService.deliveryChargesCalculator(userName,false,null)

    }

    def calculateDeliveryCharges() {
        def cartMstId = params.cartMstId

        // Retrieve cart master and associated order items
        def cartMaster = CartMaster.get(cartMstId)
        def orderItems = cartMaster?.orderItems

        if (!orderItems) {
            // Handle scenario when order items are not found
            render status: 404, text: "Order items not found"
            return
        }

        // Iterate over each order item and calculate delivery charges per vendor
        def vendorDeliveryChargesMap = [:]
        orderItems.each { orderItem ->
            def vendorId = orderItem.vendorId
            def vendorDeliveryDetails = VendorDeliveryDetails.findByVendorId(vendorId)

            if (vendorDeliveryDetails) {
                def calculationType = vendorDeliveryDetails.calculationType

                Double deliveryCharge = Double.ZERO

                if (calculationType == 'Weight') {
                    // Calculate delivery charges based on weight
                    deliveryCharge = calculateDeliveryChargesByWeight(orderItem.weight, vendorDeliveryDetails)
                } else if (calculationType == 'Location') {
                    // Calculate delivery charges based on location
                    deliveryCharge = calculateDeliveryChargesByLocation(orderItem.location, vendorDeliveryDetails)
                }

                // Update or accumulate delivery charges per vendor
                vendorDeliveryChargesMap[vendorId] = (vendorDeliveryChargesMap[vendorId] ?: Double.ZERO) + deliveryCharge
            }
        }

        // Create or update order delivery charges for each vendor
        vendorDeliveryChargesMap.each { vendorId, deliveryCharge ->
            def orderDeliveryCharges = OrderDeliveryCharges.findByCartMstIdAndVendorId(cartMstId, vendorId) ?: new OrderDeliveryCharges(
                    cartMstId: cartMstId,
                    vendorId: vendorId
            )

            orderDeliveryCharges.deliveryCharge = deliveryCharge
            orderDeliveryCharges.save(flush: true)
        }

        // Render a success response
        render status: 200, text: "Delivery charges calculated and updated for the order"
    }

    Double calculateDeliveryChargesByWeight(Double weight, VendorDeliveryDetails vendorDeliveryDetails) {
        // Retrieve delivery costs for the vendor based on weight

        def deliveryCosts = DeliveryCostsByWeight.findByVendorIdAndWeightFromLessThanEqualsAndWeightToGreaterThanEquals(
                vendorDeliveryDetails.vendorId,
                weight,
                weight
        )

        if (deliveryCosts) {
            def threshold = vendorDeliveryDetails.threshold
            if (threshold && weight <= threshold) {
                return deliveryCosts.fee
            }
        }

        return Double.ZERO
    }

    Double calculateDeliveryChargesByLocation(String location, VendorDeliveryDetails vendorDeliveryDetails) {
        // Retrieve delivery costs by location for the vendor
        def deliveryCostsByLocation = DeliveryCostsByLocation.findByVendorIdAndLocationName(
                vendorDeliveryDetails.vendorId,
                location
        )

        if (deliveryCostsByLocation) {
                return deliveryCostsByLocation.fee
        }
        else {
             deliveryCostsByLocation = DeliveryCostsByLocation.findByVendorIdAndLocationName(
                    vendorDeliveryDetails.vendorId,
                    "Default"
            )
            if (deliveryCostsByLocation) {
                return deliveryCostsByLocation.fee
            }
            else return Double.ZERO
        }
    }

    @Transactional
    def manageDeliveryCharges(){
        Integer vendorId = new Integer(params.pubId)
        VendorDeliveryDetails vendorDeliveryDetails = VendorDeliveryDetails.findByVendorId(vendorId)

        [vendorDeliveryDetails:vendorDeliveryDetails,vendorId:params.pubId]
    }

    @Transactional
    def deleleDeliveryLocation(){
        DeliveryCostsByLocation deliveryCostsByLocation = DeliveryCostsByLocation.findById(new Integer(params.locationId))
        if(deliveryCostsByLocation!=null) deliveryCostsByLocation.delete(flush: true)

        def json = [status:"OK"]
        render json as JSON
    }
    @Transactional
    def deleleDeliveryWeight(){
        DeliveryCostsByWeight deliveryCostsByWeight = DeliveryCostsByWeight.findById(new Integer(params.weightId))
        if(deliveryCostsByWeight!=null) deliveryCostsByWeight.delete(flush: true)
        def json = [status:"OK"]
        render json as JSON
    }
    @Transactional
    def getDeliveryLocations(){
        Integer vendorId = new Integer(params.vendorId)
        List deliveryLocations = DeliveryCostsByLocation.findAllByVendorId(vendorId)
        def json = [deliveryLocations:deliveryLocations]
        render json as JSON
    }

    @Transactional
    def getDeliveryWeights(){
        Integer vendorId = new Integer(params.vendorId)
        List deliveryWeights = DeliveryCostsByWeight.findAllByVendorId(vendorId)

        def json = [deliveryWeights:deliveryWeights]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_USER'])
    def saveLocationDeliveryFeesForVendor() {

        Integer vendorId = new Integer(params.vendorId)
        String locationType = params.locationType
        String locationName = params.locationName
        Double fee = new Double(params.fee)
        String currencyCd = params.currencyCd!=null?params.currencyCd:"INR"

        DeliveryCostsByLocation deliveryCostsByLocation = DeliveryCostsByLocation.findByVendorIdAndCurrencyCdAndLocationNameAndLocationType(vendorId,currencyCd,locationName,locationType)
        if(deliveryCostsByLocation!=null) deliveryCostsByLocation.delete(flush: true)

        deliveryCostsByLocation = new DeliveryCostsByLocation(vendorId: vendorId,locationName: locationName,locationType: locationType,fee: fee,currencyCd: currencyCd)
        deliveryCostsByLocation.save(failOnError: true, flush: true)

        def json = [status:"OK"]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_USER'])
    def saveWeightDeliveryFeesForVendor() {

        Integer vendorId = new Integer(params.vendorId)
        Double weightFrom = new Integer(params.weightFrom)
        Double weightTo = new Integer(params.weightTo)
        Double fee = new Double(params.fee)
        String currencyCd = params.currencyCd!=null?params.currencyCd:"INR"

        DeliveryCostsByWeight deliveryCostsByWeight = DeliveryCostsByWeight.findByVendorIdAndWeightFromAndWeightToAndCurrencyCd(vendorId,weightFrom,weightTo,currencyCd)
        if(deliveryCostsByWeight!=null) deliveryCostsByWeight.delete(flush: true)

        deliveryCostsByWeight = new DeliveryCostsByWeight(vendorId: vendorId,weightFrom: weightFrom,weightTo: weightTo,currencyCd: currencyCd,fee: fee)
        deliveryCostsByWeight.save(failOnError: true, flush: true)

        def json = [status:"OK"]
        render json as JSON

    }
    @Transactional @Secured(['ROLE_USER'])
    def saveDeliveryCalculationTypeForVendor() {
        def vendorId = params.vendorId
        def calculationType = params.calculationType
        Double threshold = params.threshold ? new Double(params.threshold) : null
        Double flatFee = params.flatFee ? new Double(params.flatFee) : null
        String username = springSecurityService.currentUser.username

       // Check if VendorDeliveryDetails already exist for the vendor
        def existingDetails = VendorDeliveryDetails.findByVendorId(new Integer(vendorId))

        if (existingDetails) {
            // Update existing VendorDeliveryDetails
            existingDetails.calculationType = calculationType
            existingDetails.threshold = threshold
            existingDetails.createdBy = username
            existingDetails.dateUpdated = new Date()
            existingDetails.flatFee = flatFee
            existingDetails.save(failOnError: true, flush: true)
        } else {
            // Create new VendorDeliveryDetails
            println("new user")
            def newDetails = new VendorDeliveryDetails(
                    vendorId: new Integer(vendorId),
                    calculationType: calculationType,
                    threshold: threshold,
                    createdBy: username,
                    dateUpdated: new Date(),
                    flatFee: flatFee
            )
            newDetails.save(failOnError: true, flush: true)
        }
        def json = [status : "OK"]
        render json as JSON
    }

    def getDeliveryPriceByWeight(){
        HashMap deliveryCosts = deliveryChargesService.getWeightBasedDeliveryCharges(new Long(params.vendorId),new Double(params.totalWeight))

        def json = [fees:deliveryCosts.deliveryCosts,information:deliveryCosts.status]
        render json as JSON
    }

    def getDeliveryPriceByLocation(){
        HashMap deliveryCosts = deliveryChargesService.getLocationBasedDeliveryCharges(new Long(params.vendorId),params.locationName,params.locationName)

        def json = [fees:deliveryCosts.deliveryCosts,information:deliveryCosts.status]
        render json as JSON
    }

    def calculateDeliveryCosts(){
        Double weight = null
        if(params.totalWeight!=null&&!"".equals(params.totalWeight)) weight = new Double(params.totalWeight)
        HashMap deliveryCosts = deliveryChargesService.calculateDeliveryCosts(new Long(params.vendorId), new Double(params.bookPrice),weight,params.locationName,params.locationName)
        def json = [fees:deliveryCosts.deliveryCosts,information:deliveryCosts.status]
        render json as JSON
    }
}

