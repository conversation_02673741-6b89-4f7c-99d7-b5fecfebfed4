package com.wonderslate.shop

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.usermanagement.User
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import grails.converters.JSON

class PrintOrderManagementController {
    UtilService utilService
    PrintOrderManagementService printOrderManagementService
    DataProviderService dataProviderService
    def springSecurityService
    MailService mailService

    @Secured(['ROLE_FINANCE','ROLE_WS_CONTENT_ADMIN','ROLE_AFFILIATION_SALES','ROLE_LIBRARY_ADMIN']) @Transactional
    def orderDetails() {
        Integer siteId = session["siteId"]
        Integer poNo = new Integer(params.poNo)
        PurchaseOrder purchaseOrder = dataProviderService.getPurchaseOrder(poNo)

        def orders = printOrderManagementService.orderDetails(""+purchaseOrder.cartMstId,""+session["userdetails"].publisherId)
        def dispatchDetails = printOrderManagementService.dispatchDetails(""+purchaseOrder.cartMstId,""+session["userdetails"].publisherId)
        def deliveredDetails = printOrderManagementService.deliveredDetails(""+purchaseOrder.cartMstId,""+session["userdetails"].publisherId)
        ShippingAddressMst shippingAddressMst = ShippingAddressMst.findByCartMstId(purchaseOrder.cartMstId)
        [orders: orders,shippingAddressMst:shippingAddressMst,dispatchDetails:dispatchDetails,deliveredDetails:deliveredDetails]
    }

    @Secured(['ROLE_FINANCE','ROLE_WS_CONTENT_ADMIN','ROLE_AFFILIATION_SALES','ROLE_LIBRARY_ADMIN']) @Transactional
    def addDispatchDetails() {

        String orderId = params.orderId
        String deliveryType = params.deliveryType
        String partnerDetails = params.partnerDetails
        String trackingCode = params.trackingCode
        String trackingLink = params.trackingLink
        String applyToAll = params.applyToAll
        String username = springSecurityService.currentUser.username
        String bookTitles ="";

        // Retrieve the order
        PurchaseOrder order = PurchaseOrder.findById(new Integer(orderId))
        BooksMst booksMst = dataProviderService.getBooksMst(order.itemCode)
        if(booksMst!=null) bookTitles = booksMst.title
        // Create a new dispatch detail instance
        def dispatch = new DispatchDetails(
                poNo: orderId,
                deliveryType: deliveryType,
                partnerDetails: partnerDetails,
                trackingCode: trackingCode,
                trackingLink: trackingLink,
                createdBy: username
        )

        // Save the dispatch detail
        dispatch.save(flush: true)
        order.status = "Dispatched"
        order.save(flush: true)
        // Apply the same dispatch details to all items if applyToAll is true.. here we have to put the logic of publisher id thingy.
        if ("on".equals(applyToAll)) {
            List orders = printOrderManagementService.getOpenOrdersList(""+order.cartMstId,""+session["userdetails"].publisherId)
            orders.each { item ->
                booksMst = dataProviderService.getBooksMst(item.itemCode)
                if(booksMst!=null) bookTitles +="\n"+ booksMst.title
                def itemDispatch = new DispatchDetails(
                        poNo: item.id,
                        deliveryType: deliveryType,
                        partnerDetails: partnerDetails,
                        trackingCode: trackingCode,
                        trackingLink: trackingLink,
                        createdBy: username
                )

                itemDispatch.save(flush: true)
                PurchaseOrder purchaseOrder = PurchaseOrder.findById(item.id)
                purchaseOrder.status = "Dispatched"
                purchaseOrder.save(flush: true)



            }
        }
        User user = dataProviderService.getUserMst(order.username)
        if(user!=null&&user.email!=null&&!"".equals(user.email)){
            SiteMst siteMst = dataProviderService.getSiteMst(order.siteId)
            String    fromEmail
            if(siteMst.fromEmail!=null&&!"".equals(siteMst.fromEmail))
                fromEmail = siteMst.fromEmail
            else
                fromEmail = "Wonderslate <<EMAIL>>"
            String mailText = "Hello ${user.name}\n\n The following books has been dispatched.\n\n"+bookTitles+"\n\n";



            mailText +="\n The shipping  details are \n\n"+ "Delivery type: "+ deliveryType+
                    "\n Courier name: "+partnerDetails+
                    "\n Tracking code: "+trackingCode+
                    "\n Tracking link: "+trackingLink+
                    "\n\n";
            if(siteMst!=null&&siteMst.siteDomainName!=null&&!"".equals(siteMst.siteDomainName)){
                mailText +="\n\n For more details log on to "+siteMst.siteDomainName
            }
            try {
                mailService.sendMail {
                    async true
                    to user.email,"<EMAIL>"
                    from fromEmail
                    subject "Books shipped for order " + order.cartMstId
                    text mailText
                }

            }catch(Exception e){
                println("Exception in sending userBookPurchase email to "+user.email+" and exception is "+e.toString())
            }
        }

        def json = [status:"OK"]
        render json as JSON

    }

    @Secured(['ROLE_FINANCE','ROLE_WS_CONTENT_ADMIN','ROLE_AFFILIATION_SALES','ROLE_LIBRARY_ADMIN']) @Transactional
    def addDeliveryCompletedDetails() {

        String orderId = params.orderId
        String receivedBy = params.receivedBy
        String username = springSecurityService.currentUser.username
        String applyToAll = params.applyToAllDelivery

        // Retrieve the order
        PurchaseOrder order = PurchaseOrder.findById(new Integer(orderId))

        // Create a new dispatch detail instance
        def deliveryCompletion = new DeliveredDetails(
                poNo: orderId,
                receivedBy: receivedBy,
                createdBy: username
        )

        // Save the dispatch detail
        deliveryCompletion.save(flush: true)
        order.status = "Delivered"
        order.save(flush: true)
        // Apply the same dispatch details to all items if applyToAll is true.. here we have to put the logic of publisher id thingy.
        if ("on".equals(applyToAll)) {
            List orders = printOrderManagementService.getDispatchedOrdersList(""+order.cartMstId,""+session["userdetails"].publisherId)
            orders.each { item ->
                def itemDelivered =  new DeliveredDetails(
                        poNo: item.id,
                        receivedBy: receivedBy,
                        createdBy: username
                )

                itemDelivered.save(flush: true)
                PurchaseOrder purchaseOrder = PurchaseOrder.findById(item.id)
                purchaseOrder.status = "Dispatched"
                purchaseOrder.save(flush: true)
            }
        }

        def json = [status:"OK"]
        render json as JSON

    }
}
