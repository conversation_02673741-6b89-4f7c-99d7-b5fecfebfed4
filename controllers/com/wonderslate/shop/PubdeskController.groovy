package com.wonderslate.shop

import com.wonderslate.DataNotificationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BannersMst
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.KeyValues
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.UtilService
import com.wonderslate.log.Quizrecorder
import com.wonderslate.log.Quizrecorderdtl
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.apache.commons.io.FileUtils
import org.imgscalr.Scalr
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest
import pl.touk.excel.export.WebXlsxExporter

import javax.imageio.ImageIO
import java.awt.image.BufferedImage
import java.text.SimpleDateFormat


class PubdeskController {

    UtilService utilService
    def springSecurityService
    def redisService
    DataProviderService dataProviderService
    DataNotificationService dataNotificationService

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def downloadBooksUnPublished(){

        int siteId=utilService.getSiteId(request,session)
        String sql = "select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher,COALESCE(bk.isbn,' ') "+
                "from books_mst bk where bk.status is null and bk.site_id="+siteId+" and bk.publisher_id is null  and (bk.book_type not in ('print') or bk.book_type is null) "+
                "UNION "+
                "select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher,COALESCE(bk.isbn,' ') "+
                "from books_mst bk, publishers p where bk.status is  null and bk.site_id="+siteId+" and bk.publisher_id=p.id "+
                "order by id desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        int i=0
        def results = sql1.rows(sql)
        String serverUrl = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        if(serverUrl.indexOf("publish.")>-1) {
            if(siteId==12){
                serverUrl="https://evidya.sagepub.in"
            }else if(siteId==23){
                serverUrl="https://etext.sagepub.in"
            }else if(siteId==24){
                serverUrl="https://ebouquet.sagepub.in"
            }
        }
        List data = results.collect{book ->
            String bookUrl = serverUrl + "/library/" + URLEncoder.encode(book.title,"UTF-8").replace("'","&#39;").toLowerCase() + "?bookId=${book.id}&siteName=${session['entryController']}"
            i++
            return [bookId: book[0], title: (book[1]),isbn:book[9],
                    publisher:book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",slno: i,bookUrl :bookUrl]
        }
        List headers = ['Serial No.',"Book Id","Title","ISBN","Publisher","Book Url"]
        List withProperties = ['slno','bookId',"title","isbn","publisher","bookUrl"]
        def fileName = "Unpublished_Books_Details_"  + (new Random()).nextInt(9999999) + ".xlsx";
        new WebXlsxExporter().with {
            setResponseHeaders(response, fileName)
            fillHeader(headers)
            add(data, withProperties)
            save(response.outputStream)
        }

    }

    @Secured(['ROLE_BOOK_CREATOR'])
    @Transactional
    def downloadBooksPublished(){

        int siteId=utilService.getSiteId(request,session)
        String sql = "select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,null publisher,COALESCE(bk.isbn,' ') "+
                "from books_mst bk where bk.status='published' and bk.publisher_id is null  and (bk.book_type not in ('print') or bk.book_type is null) and bk.site_id="+siteId+" "+
                "UNION "+
                "select bk.id id,bk.title,bk.cover_image,bk.subjectyear,bk.listprice,bk.rating,bk.price,bk.status,p.name publisher,COALESCE(bk.isbn,' ') "+
                "from books_mst bk, publishers p where (bk.status='published' ||bk.status='institutePublished' ) and bk.publisher_id=p.id and bk.site_id="+siteId+" "+
                "order by id desc"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        int i=0
        String serverUrl = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        if(serverUrl.indexOf("publish.")>-1) {
            if(siteId==12){
                serverUrl="https://evidya.sagepub.in"
            }else if(siteId==23){
                serverUrl="https://etext.sagepub.in"
            }else if(siteId==24){
                serverUrl="https://ebouquet.sagepub.in"
            }
        }
        def results = sql1.rows(sql)
        List data = results.collect{book ->
            String bookUrl = serverUrl + "/library/" + URLEncoder.encode(book.title,"UTF-8").replace("'","&#39;").toLowerCase() + "?bookId=${book.id}&siteName=${session['entryController']}"
            i++
            return [bookId: book[0], title: (book[1]),isbn:book[9],
                    publisher:book[8]!=null? (book[8]).replace(':',' ').replace(',',' '):"",slno: i,bookUrl :bookUrl]
        }
        List headers = ['Serial No.',"Book Id","Title","ISBN","Publisher","Book Url"]
        List withProperties = ['slno','bookId',"title","isbn","publisher","bookUrl"]
        def fileName = "Published_Books_Details_"  + (new Random()).nextInt(9999999) + ".xlsx";
        new WebXlsxExporter().with {
            setResponseHeaders(response, fileName)
            fillHeader(headers)
            add(data, withProperties)
            save(response.outputStream)
        }

    }

    @Secured(['ROLE_USER'])  @Transactional
    def addFlashCard(){
        def resId
        def keyValueId=-1
        ResourceDtl resourceDtlInstance
        if("-1".equals(params.resId)){
            //create the resource dtl first
            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "KeyValues"
            resourceDtlInstance.chapterId = new Integer(params.chapterId)
            resourceDtlInstance.resourceName = params.revisionTitle
            resourceDtlInstance.resLink="blank"
            resourceDtlInstance.save(flush: true, failOnError: true)
            resId = resourceDtlInstance.id
            if(resourceDtlInstance.sharing==null){
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
                dataNotificationService.resourceUpdated(chaptersMst.id, chaptersMst.bookId)

            }else{
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }

        }else{
            resId =  new Long(params.resId)
            resourceDtlInstance = dataProviderService.getResourceDtl(resId)
        }

        if("add".equals(params.mode)){
            String definitionvalue=params.definition
            String definition = definitionvalue
            KeyValues keyValues = new KeyValues(term: params.term, definition:definition ,resId: resId,status: "active")
            keyValues.save(flush: true, failOnError: true)
            keyValueId = keyValues.id
            resourceDtlInstance.resLink=keyValueId
            if(params.revisionTitle!=null) resourceDtlInstance.resourceName = params.revisionTitle
            resourceDtlInstance.save(flush: true, failOnError: true)

        }

        def json = ["resId":resId, "keyValueId":keyValueId]
        render json as JSON
    }

    @Secured(['ROLE_BOOK_CREATOR','ROLE_DIGITAL_MARKETER'])
    def addBanners() {
        def bannerId = params.bannerId;
        def pubId = params.pubId
        String siteId=utilService.getSiteId(request,session)
        def bookId = null
        def page=params?.bannerPage
        def actionType = params?.actionType
        if(params.bookId!="" && params.bookId!=null) bookId = new Long(params.bookId)
        if (bannerId != "" && bannerId != null) {
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            MultipartFile mobfile = multiRequest.getFile("filesss");
            if (file.empty && mobfile.empty) {
                if(!"".equals(params.namename)){
                    BannersMst bannersMst = BannersMst.findById(new Long(bannerId))
                    bannersMst.imageName = params.name;
                    bannersMst.bookId = bookId
                    bannersMst.action = actionType
                    bannersMst.save(flush: true, failOnError: true)
                    if(pubId!=null && pubId!=""){
                        redirect(controller: "publisherManagement", action: "addPublisher", params: [pubId: pubId])
                    }else{
                        redirect(controller: "wonderpublish", action: "bannerManagement")
                    }
                }
                flash.message = "File cannot be empty"
            } else {
                BannersMst bannersMst = BannersMst.findById(new Long(bannerId))
                if(!file.empty) {
                    File uploadDir
                    if (Integer.parseInt(siteId) != 21 && pubId == null ) uploadDir = new File("upload/banner/" + bannersMst.id)
                    else uploadDir = new File("upload/banner/")
                    if (!uploadDir.exists()) uploadDir.mkdirs()
                    //creating directory to process images
                    File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                    if (!uploadDir1.exists()) uploadDir1.mkdirs()
                    String filename = file.originalFilename
                    filename = filename.replaceAll("\\s+", "")
                    if (pubId != null && pubId != "") {
                        filename = "" + pubId + "_" + filename.replaceAll("\\s+", "")
                    }
                    BufferedImage image = ImageIO.read(file.getInputStream())

                    ByteArrayOutputStream baos = new ByteArrayOutputStream()
                    ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                    baos.flush()
                    byte[] scaledImageInByte = baos.toByteArray()
                    baos.close()

                    FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)


                    //saving original image finally
                    if (pubId != "" && pubId != null) {
                        file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
                    } else {
                        file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
                    }
                    String webPImage=filename.substring(0, filename.indexOf("."))
                    ImageIO.write(image, "webp", new File("upload/banner/"+bannersMst.id+"/"+webPImage+".webp"));
                }
                if(!mobfile.empty){
                    File uploadDirMob
                    if(Integer.parseInt(siteId)!=21 && pubId==null)  uploadDirMob = new File("upload/banner/"+ bannersMst.id)
                    else uploadDirMob = new File("upload/banner/")
                    if (!uploadDirMob.exists()) uploadDirMob.mkdirs()

                    //creating directory to process images
                    File uploadDir1Mob = new File(uploadDirMob.absolutePath + "/processed")
                    if (!uploadDir1Mob.exists()) uploadDir1Mob.mkdirs()
                    String filenameMob = mobfile.originalFilename
                    filenameMob = filenameMob.replaceAll("\\s+", "")
                    if(pubId!=null && pubId!=""){
                        filenameMob = ""+pubId+"_"+filenameMob.replaceAll("\\s+", "")
                    }

                    BufferedImage image = ImageIO.read(mobfile.getInputStream())

                    ByteArrayOutputStream baos = new ByteArrayOutputStream()
                    ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filenameMob.substring(filenameMob.indexOf(".") + 1), baos)
                    baos.flush()
                    byte[] scaledImageInByte = baos.toByteArray()
                    baos.close()

                    FileUtils.writeByteArrayToFile(new File(uploadDirMob.absolutePath + "/" + filenameMob.substring(0, filenameMob.indexOf(".")) + '_thumbnail' + filenameMob.substring(filenameMob.indexOf("."))), scaledImageInByte)

                    //saving original image finally
                    if(pubId!="" && pubId!=null){
                        mobfile.transferTo(new File(uploadDirMob.absolutePath + "/" + filenameMob))
                    }else{
                        mobfile.transferTo(new File(uploadDirMob.absolutePath + "/" + mobfile.filename))
                    }
                    String webPImage=filenameMob.substring(0, filenameMob.indexOf("."))
                    ImageIO.write(image, "webp", new File("upload/banner/"+bannersMst.id+"/"+webPImage+".webp"));

                }
                bannersMst.imageName = params.name;
                if(bannersMst.imagePath!=null && bannersMst.imagePath!=" " && params.file.filename==null && params.file==""){
                    bannersMst.imagePath = bannersMst.imagePath
                }else if(params.file!=null && params.file.filename!=""){
                    bannersMst.imagePath = pubId!=null?""+pubId+"_"+params.file.filename.replaceAll("\\s+", ""):params.file.filename.replaceAll("\\s+", "")
                }
                if(bannersMst.imagePathMobile!=null  && bannersMst.imagePathMobile!=" " && params.filesss==null &&  params.filesss==""){
                    bannersMst.imagePathMobile = bannersMst.imagePathMobile
                }else if(params.filesss!=null && params.filesss.filename!=""){
                    bannersMst.imagePathMobile =pubId!=null?""+pubId+"_"+params.filesss.filename.replaceAll("\\s+", ""):params.filesss.filename.replaceAll("\\s+", "")
                }
                bannersMst.bookId = bookId
                bannersMst.save(flush: true, failOnError: true)
                if(pubId!="" && pubId!=null){
                    redirect(controller: "publisherManagement", action: "addPublisher", params: [pubId: pubId])
                }else{
                    redirect(controller: "wonderpublish", action: "bannerManagement")
                }
            }
        } else {
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            MultipartFile mobfile = multiRequest.getFile("filesss");
            if (file.empty && mobfile.empty) {
                flash.message = "File cannot be empty"
            } else {
                if(pubId!=null && pubId!=""){
                    BannersMst bannersMst = new BannersMst(imageName: params.name, siteId:utilService.getSiteId(request,session), publisherId: new Long(pubId), bookId: bookId, page: page, imagePath: "",action:actionType)
                    bannersMst.save(failOnError: true, flush: true)
                    if(!file.empty) {
                        File uploadDir = new File("upload/banner/")
                        if (!uploadDir.exists()) uploadDir.mkdirs()

                        //creating directory to process images
                        File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                        if (!uploadDir1.exists()) uploadDir1.mkdirs()
                        String filename = file.originalFilename
                        filename = "" + pubId + "_" + filename.replaceAll("\\s+", "")
                        BufferedImage image = ImageIO.read(file.getInputStream())

                        ByteArrayOutputStream baos = new ByteArrayOutputStream()
                        ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                        baos.flush()
                        byte[] scaledImageInByte = baos.toByteArray()
                        baos.close()

                        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)

                        //saving original image finally
                        file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
                        BannersMst.executeUpdate("update BannersMst set imagePath ='" + filename + "' where id=" + bannersMst.id)
                        String webPImage=filename.substring(0, filename.indexOf("."))
                        ImageIO.write(image, "webp", new File("upload/banner/"+webPImage+".webp"));
                    }

                    if(!mobfile.empty){
                        File uploadDirMob = new File("upload/banner/")
                        if (!uploadDirMob.exists()) uploadDirMob.mkdirs()
                        //creating directory to process images
                        File uploadDir1Mob = new File(uploadDirMob.absolutePath + "/processed")
                        if (!uploadDir1Mob.exists()) uploadDir1Mob.mkdirs()
                        String filenameMob = mobfile.originalFilename
                        filenameMob = "" + pubId + "_" + filenameMob.replaceAll("\\s+", "")
                        //saving original image finally
                        BufferedImage image = ImageIO.read(mobfile.getInputStream())

                        ByteArrayOutputStream baos = new ByteArrayOutputStream()
                        ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filenameMob.substring(filenameMob.indexOf(".") + 1), baos)
                        baos.flush()
                        byte[] scaledImageInByte = baos.toByteArray()
                        baos.close()
                        FileUtils.writeByteArrayToFile(new File(uploadDirMob.absolutePath + "/" + filenameMob.substring(0, filenameMob.indexOf(".")) + '_thumbnail' + filenameMob.substring(filenameMob.indexOf("."))), scaledImageInByte)
                        mobfile.transferTo(new File(uploadDirMob.absolutePath + "/" + filenameMob))
                        BannersMst.executeUpdate("update BannersMst set imagePathMobile ='" + filenameMob + "' where id=" + bannersMst.id)
                        String webPImage=filenameMob.substring(0, filenameMob.indexOf("."))
                        ImageIO.write(image, "webp", new File("upload/banner/"+webPImage+".webp"));
                    }
                    redirect(controller: "publisherManagement", action: "addPublisher", params: [pubId: pubId])

                }else{
                    BannersMst bannersMst = new BannersMst(imageName: params.name, siteId: utilService.getSiteId(request,session), bookId: bookId, page: page, imagePath: "",action:actionType)
                    bannersMst.save(failOnError: true, flush: true)
                    if(!file.empty) {
                        File uploadDir
                        if (Integer.parseInt(siteId) != 21) uploadDir = new File("upload/banner/" + bannersMst.id)
                        else uploadDir = new File("upload/banner/")
                        if (!uploadDir.exists()) uploadDir.mkdirs()

                        //creating directory to process images
                        File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
                        if (!uploadDir1.exists()) uploadDir1.mkdirs()
                        String filename = file.originalFilename
                        filename = filename.replaceAll("\\s+", "")
                        //saving original image finally
                        BufferedImage image = ImageIO.read(file.getInputStream())

                        ByteArrayOutputStream baos = new ByteArrayOutputStream()
                        ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                        baos.flush()
                        byte[] scaledImageInByte = baos.toByteArray()
                        baos.close()
                        FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
                        file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
                        BannersMst.executeUpdate("update BannersMst set imagePath ='" + filename + "' where id=" + bannersMst.id)
                        String webPImage=filename.substring(0, filename.indexOf("."))
                        ImageIO.write(image, "webp", new File("upload/banner/"+bannersMst.id+"/"+webPImage+".webp"));
                    }

                    if(!mobfile.empty){

                        File uploadDirMob
                        if(Integer.parseInt(siteId)!=21 || pubId==null) uploadDirMob = new File("upload/banner/"+ bannersMst.id)
                        else uploadDirMob = new File("upload/banner/")
                        if (!uploadDirMob.exists()) uploadDirMob.mkdirs()
                        //creating directory to process images
                        File uploadDir1Mob = new File(uploadDirMob.absolutePath + "/processed")
                        if (!uploadDir1Mob.exists()) uploadDir1Mob.mkdirs()
                        String filenameMob = mobfile.originalFilename
                        filenameMob = filenameMob.replaceAll("\\s+", "")
                        //saving original image finally
                        BufferedImage image = ImageIO.read(mobfile.getInputStream())

                        ByteArrayOutputStream baos = new ByteArrayOutputStream()
                        ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 1000, 1000, Scalr.OP_ANTIALIAS), filenameMob.substring(filenameMob.indexOf(".") + 1), baos)
                        baos.flush()
                        byte[] scaledImageInByte = baos.toByteArray()
                        baos.close()
                        FileUtils.writeByteArrayToFile(new File(uploadDirMob.absolutePath + "/" + filenameMob.substring(0, filenameMob.indexOf(".")) + '_thumbnail' + filenameMob.substring(filenameMob.indexOf("."))), scaledImageInByte)
                        mobfile.transferTo(new File(uploadDirMob.absolutePath + "/" + filenameMob))
                        BannersMst.executeUpdate("update BannersMst set imagePathMobile ='" + filenameMob + "' where id=" + bannersMst.id)
                        String webPImage=filenameMob.substring(0, filenameMob.indexOf("."))
                        ImageIO.write(image, "webp", new File("upload/banner/"+bannersMst.id+"/"+webPImage+".webp"));
                    }
                    redirect(controller: "wonderpublish", action: "bannerManagement")

                }
            }
        }
        dataProviderService.getBanners(siteId)
    }

    def getindependentResourcDetails(){
        def resource="";
        def recordCountString = "";
        def search=params."search[value]";
        def redisValue=redisService.("getindependentResourcDetails"+params.pageType+"_"+params.siteId);
        if(params.start=="0"&&redisValue!=null&&!"null".equals(redisValue)&&!(search!=null && search!="")){
            resource = redisService.("getindependentResourcDetails"+params.pageType+"_"+params.siteId)
            recordCountString= redisService.("getindependentResourcDetails"+params.pageType+"_"+params.siteId+"_recordCount")
        }else {
            def sqlCount = "select count(rd.id)" +
                    " from resource_dtl rd" +
                    " where rd.site_id='" + params.siteId + "'" +
                    " AND rd.quiz_mode='" + params.pageType + "'";
            if (search != null && search != "") {
                sqlCount += " AND (rd.resource_name  LIKE '%" + search + "%' OR rd.id  LIKE '%" + search + "%')";
            }

            def dataSourceCount = grailsApplication.mainContext.getBean('dataSource')
            def sql1count = new Sql(dataSourceCount)
            def resultsCount = sql1count.rows(sqlCount)
            def count = resultsCount.get(0).values()
            def sql = "select rd.id,rd.resource_name,rd.date_created,rd.res_link,rd.quiz_mode,rd.ind_content_type,rd.downloadlink1," +
                    " rd.downloadlink2,rd.downloadlink3,DATE_ADD( rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE) start_time,DATE_ADD( rd.test_end_date, INTERVAL '5:30' HOUR_MINUTE) end_time,rd.video_player,rd.allow_comments,rd.display_comments,rd.filename" +
                    " from resource_dtl rd" +
                    " where rd.site_id='" + params.siteId + "'" +
                    " AND rd.quiz_mode='" + params.pageType + "'";
            if (params."search[value]" != null && params."search[value]" != "") {
                sql += " AND (rd.resource_name  LIKE '%" + params."search[value]" + "%' OR rd.id  LIKE '%" + params."search[value]" + "%')";
            }
            if(!"".equals(params.start) && !"".equals(params.length) && params.start!=null && params.length!=null ){
                sql += " order by rd.id Desc limit " + params.start + " , " + params.length + "";
            }else{
                sql += " order by rd.id Desc ";
            }
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH=mm")
            List resources = results.collect { res ->
                boolean ebook=false
                if(res[14]!=null&&(res[14].indexOf(".pdf")!=-1||res[14].indexOf(".zip")!=-1)) ebook=true
                return [id: res[0], resourceName: (""+res[1]).replaceAll(':', ' ').replaceAll(',', ' ').replace('[', ' ').replace(']', ' '),
                        dateCreated : ("" + res[2]).replaceAll(':', '#'),
                        resLink:res[3]!=null?(""+res[3]).replaceAll(':', '#').replaceAll(',', ' ').replace('[', ' ').replace(']', ' '):res[3],
                        resType:res[4],indContentType:res[5]?res[5]:"",downloadlink1:res[6]?(res[6]+"").replaceAll(':','#'):"",
                        downloadlink2:res[7]?(res[7]+"").replaceAll(':','#'):"",downloadlink3:res[8]?(res[8]+"").replaceAll(':','#'):"",
                        testStartDate: res.start_time?dateFormat.format(res.start_time):"",testEndDate: res.end_time?dateFormat.format(res.end_time):"",
                        videoPlayer:res[11]?res[11]:"youtube",
                        allowComments:res[12], displayComments:res[13],ebook:ebook]
            }
            resource = addDoubleQuotes1(convertToJsonString(resources.toString()))
            recordCountString = ""+count
            if(params.start=="0" && !(search!=null && search!="")) {
                redisService.("getindependentResourcDetails" + params.pageType+"_"+params.siteId) = resource;
                redisService.("getindependentResourcDetails"+params.pageType+"_"+params.siteId+"_recordCount") = recordCountString
            }
        }

        recordCountString = recordCountString.substring(1, recordCountString.length());
        recordCountString = recordCountString.substring(0, recordCountString.length() - 1);

        def json = [data: resource, recordsTotal: recordCountString, draw: params.draw, recordsFiltered: recordCountString]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getRankDetailsForTestSeriesBook(String resId) {
        ResourceDtl resourceDtl =  dataProviderService.getResourceDtl(new Long(resId))
        utilService.calculateRank(resourceDtl.id)
        dataProviderService.getTestRanks(resId)
        if (redisService.("testRanks_" + resId) == null) dataProviderService.getTestRanks(resId)

        List testRanks = new JsonSlurper().parseText(redisService.("testRanks_" + resId))
        String userRank = ""
        String userScore = ""
        String userAnswers = ""
        String quizRecorderId = ""
        if (springSecurityService.currentUser != null) {
            //get loggedInUser rank
            Quizrecorder quizrecorder = Quizrecorder.findByQuizidAndUsernameAndRankIsNotNull(new Integer(resId), springSecurityService.currentUser.username)
            if (quizrecorder == null) quizrecorder = Quizrecorder.findByQuizidAndUsername(new Integer(resId), springSecurityService.currentUser.username)
            if (quizrecorder != null) {
                userRank = "" + quizrecorder.rank
                userScore = "" + quizrecorder.score
                quizRecorderId = "" + quizrecorder.id
                userAnswers = "" + getUserAnswers(quizrecorder.id)
            }
        }
        def json = ['testRanks'  : testRanks, 'userRank': userRank, 'userScore': userScore, 'status': 'ok', 'totalParticipants': resourceDtl.noOfTestTakers,
                    'userAnswers': userAnswers, 'quizRecorderId': quizRecorderId, 'resId': "" + resId]
        render json as JSON
    }

    @Transactional
    def getUserAnswers(quizRecorderId)
    {


        Quizrecorder quizrecorder = Quizrecorder.findById(quizRecorderId)


        String userAnswers = null

        List qrdtls = Quizrecorderdtl.findAllByQuizrecorderid(quizRecorderId)

        if(qrdtls.size()>0) userAnswers = "["

        qrdtls.each{ qrdtl ->
            userAnswers += "{\"skipped\":"+("skipped".equals(qrdtl.correctanswer)?"\"true\"":"\"false\"")+
                    ",\"ans1\":"+(qrdtl.option1!=null&&!"null".equals(qrdtl.option1)?"\""+qrdtl.option1+"\",":"null,")+
                    "\"ans2\":"+(qrdtl.option2!=null&&!"null".equals(qrdtl.option2)?"\""+qrdtl.option2+"\",":"null,")+
                    "\"ans3\":"+(qrdtl.option3!=null&&!"null".equals(qrdtl.option3)?"\""+qrdtl.option3+"\",":"null,")+
                    "\"ans4\":"+(qrdtl.option4!=null&&!"null".equals(qrdtl.option4)?"\""+qrdtl.option4+"\",":"null,")+
                    "\"ans5\":"+(qrdtl.option5!=null&&!"null".equals(qrdtl.option5)?"\""+qrdtl.option5+"\",":"null,")+
                    "\"correctAnswer\":"+("skipped".equals(qrdtl.correctanswer)?"\"false\",":"\""+qrdtl.correctanswer+"\",")+
                    "\"id\":"+qrdtl.objectivemstid+"},"



        }
        if(qrdtls.size()>0){
            userAnswers = userAnswers.substring(0,userAnswers.length()-1)+"]"
        }

        return userAnswers
    }

    @Secured(['ROLE_BOOK_CREATOR','ROLE_DIGITAL_MARKETER'])
    def deleteBannerById(){
        BannersMst bannerMst = BannersMst.get(new Long(params.id))
        String siteId=utilService.getSiteId(request,session)
        bannerMst.delete(flush: true)
        dataProviderService.getBanners(siteId)
        def json = ['status' :"OK"]
        render json as JSON

    }

    def getLatestTestSeries() {
        def json

        Integer siteId = utilService.getSiteId(request,session)
        String siteIdList = siteId.toString()
        if(redisService.("testSeriesList_"+utilService.getSiteId(request,session))==null) {
            dataProviderService.getLatestTestSeries(siteIdList,siteId)
        }

        List books = new JsonSlurper().parseText(redisService.("testSeriesList_"+utilService.getSiteId(request,session)))
        json = [
                'books':books,
                'status' : books ? "OK" : "Nothing present",
                'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST"),
                'totalBooks':books.size()
        ]

        render json as JSON

    }

    def updateTestSeriesList(){
        Integer siteId = utilService.getSiteId(request,session)
        String siteIdList = siteId.toString()
        dataProviderService.getLatestTestSeries(siteIdList,siteId)
    }

    @Transactional
    def getRankDetails() {
        if("true".equals(params.testSeriesBook)){
            //logic for rank

            getRankDetailsForTestSeriesBook(params.resId)
        }else {
            Date serverTime = convertDate(Calendar.getInstance().getTime(), "UTC", "IST")
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            if (booksMst.testEndDate != null && booksMst.testEndDate.compareTo(serverTime) <= 0 && !"NOBODY".equals(booksMst.rankingDone)) {
                if (redisService.("test_series_" + params.bookId) == null) dataProviderService.getTestSeriesDtl(params.bookId)
                List testSeriesDtl = new JsonSlurper().parseText(redisService.("test_series_" + params.bookId))
                if (!"true".equals(booksMst.rankingDone)) {
                    //code for test ranking
                    String sql = "select id,username,endtime,score " +
                            "from wslog.quizrecorder qr, " +
                            "(select username un,min(endtime) et from quizrecorder, books_mst bm where quizid=" + testSeriesDtl[0].resourceID +
                            " and bm.id=" + params.bookId + " and bm.test_end_date>endtime group by username) qr1 " +
                            "where qr.username=qr1.un and qr.endtime=qr1.et " +
                            "order by score desc"

                    def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                    def sql1 = new Sql(dataSource)
                    def results = sql1.rows(sql)
                    if (results.size() > 0) {
                        int currentRank = 1
                        int nextRank = 0
                        double oldScore = 0
                        results.each { test ->
                            double newScore = (test[3] != null) ? test[3].doubleValue() : 0
                            nextRank += 1
                            //first time
                            if (currentRank == 0) {
                                oldScore = newScore
                                currentRank = 1

                            } else {
                                //check if the next score is same as old score
                                if (oldScore != newScore) {
                                    oldScore = newScore
                                    currentRank = nextRank

                                } else {
                                    // no changes to the rank
                                }
                            }


                            Quizrecorder quizrecorder = Quizrecorder.findById(test[0])
                            quizrecorder.rank = new Integer(currentRank)
                            quizrecorder.save(flush: true, failOnError: true)

                        }
                        BooksMst.executeUpdate("update BooksMst set rankingDone ='true',noOfTestTakers=' " +results.size() + "' where id=" + params.bookId)
                        BooksMst.wsshop.executeUpdate("update BooksMst set rankingDone ='true',noOfTestTakers= '" +results.size() + "' where id=" + params.bookId)
                        BooksMst.wsuser.executeUpdate("update BooksMst set rankingDone ='true',noOfTestTakers' '" + results.size() + "' where id=" + params.bookId)

                    } else {
                        BooksMst.executeUpdate("update BooksMst set rankingDone ='NORANK' where id=" + params.bookId)
                        BooksMst.wsshop.executeUpdate("update BooksMst set rankingDone ='NORANK' where id=" + params.bookId)
                        BooksMst.wsuser.executeUpdate("update BooksMst set rankingDone ='NORANK' where id=" + params.bookId)

                    }



                }

                if (redisService.("testRanks_" + testSeriesDtl[0].resourceID) == null) dataProviderService.getTestRanks(testSeriesDtl[0].resourceID)

                List testRanks = new JsonSlurper().parseText(redisService.("testRanks_" + testSeriesDtl[0].resourceID))
                String userRank = ""
                String userScore = ""
                String userAnswers = ""
                String quizRecorderId = ""
                if (springSecurityService.currentUser != null) {
                    //get loggedInUser rank
                    Quizrecorder quizrecorder = Quizrecorder.findByQuizidAndUsernameAndRankIsNotNull(new Integer(testSeriesDtl[0].resourceID), springSecurityService.currentUser.username)
                    if (quizrecorder == null) quizrecorder = Quizrecorder.findByQuizidAndUsername(new Integer(testSeriesDtl[0].resourceID), springSecurityService.currentUser.username)
                    if (quizrecorder != null) {
                        userRank = "" + quizrecorder.rank
                        userScore = "" + quizrecorder.score
                        userAnswers = "" + getUserAnswers(quizrecorder.id)
                        quizRecorderId = "" + quizrecorder.id
                    }
                }
                def json = ['testRanks'  : testRanks, 'userRank': userRank, 'userScore': userScore, 'status': 'ok', 'totalParticipants': booksMst.noOfTestTakers,
                            'userAnswers': userAnswers, 'quizRecorderId': quizRecorderId, 'resId': "" + testSeriesDtl[0].resourceID]
                render json as JSON
            } else {
                def json = [status: 'Ranking will be done after the test end time.']
                render json as JSON
            }
        }
    }

    @Transactional
    def getLiveTestRanks() {
        ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.resId))
        def json
        if (resourceDtl != null) {
            if(resourceDtl.testEndDate==null || Calendar.getInstance().getTime().compareTo(resourceDtl.testEndDate)>=0) {
                utilService.calculateRank(resourceDtl.id)
                dataProviderService.getTestRanks(""+resourceDtl.id)
                String sql = "select qr.rank,qr.score,qr.timetaken,qr.total_questions,qr.wrong_answers," +
                        "qr.correct_answers,qr.skipped,DATE_ADD(qr.endtime,INTERVAL '5:30' HOUR_MINUTE) end_time, qr.username from wslog.quizrecorder qr where qr.quizid= " +params.resId+
                        " and qr.rank is not null  order by qr.rank asc,qr.timetaken*1 asc limit 1000"
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                def duration
                def test
                def date
                double  score
                def resultscore
                if (results != null && results.size() > 0) {
                    List testRanks = results.collect { rank ->
                        User user = dataProviderService.getUserMst(rank.username)
                        test = rank.timetaken
                        long durationtest = Long.parseLong(test)
                        score = rank.score
                        resultscore = Math.round(score * 100) / 100
                        date = rank.end_time
                        if (date != "") {
                            date = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(date)
                        }
                        duration = millisecondstoTime(durationtest)
                        return [name: user.name, rank: (rank.rank), userid: user.id, profilepic: user.profilepic, marks: resultscore, timetaken: duration,
                                mobile: user.mobile, totalquestions: rank.total_questions, wronganswers: rank.wrong_answers, correctanswers: rank.correct_answers, skipped: rank.skipped, submittime: date,state: user.state!=null?user.state:"",district: user.district!=null?user.district:""]
                    }
                    json = ['testRanks'  : testRanks]

                    render json as JSON
                }
            }else{
                json = ['testRanksend': resourceDtl.testEndDate]
                render json as JSON
            }
        } else {
            json = ['testRanks': null]
            render json as JSON

        }
    }

    @Transactional
    def downloadLiveTestRanks(){
        String sql = "select qr.rank,qr.score,qr.timetaken,qr.total_questions,qr.wrong_answers," +
                "qr.correct_answers,qr.skipped,DATE_ADD(qr.endtime,INTERVAL '5:30' HOUR_MINUTE) end_time, qr.username from wslog.quizrecorder qr where qr.quizid= " +params.resId+
                " and qr.rank is not null  order by qr.rank asc,qr.timetaken*1 asc limit 5000"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def duration
        def test
        def date
        double  score
        def resultscore

        if (results != null && results.size() > 0) {
            List data = results.collect { rank ->
                User user = dataProviderService.getUserMst(rank.username)
                test = rank.timetaken
                long durationtest = Long.parseLong(test)
                score = rank.score
                resultscore = Math.round(score * 100) / 100
                date = rank.end_time
                if (date != "") {
                    date = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(date)
                }
                duration = millisecondstoTime(durationtest)
                return [name: user.name, rank: (rank.rank), userid: user.id, profilepic: user.profilepic, marks: resultscore, timetaken: duration,
                        mobile: user.mobile, totalquestions: rank.total_questions, wronganswers: rank.wrong_answers, correctanswers: rank.correct_answers, skipped: rank.skipped, submittime: date,state: user.state!=null?user.state:"",district: user.district!=null?user.district:""]
            }

            List headers = ["Username","Mobile", "Rank","Score","Total Questions","Wrong Answers","Correct Answers","Skipped Questions","Test Duration","Test Submit Time","State","District"]
            List withProperties = ["name","mobile", "rank","marks","totalquestions","wronganswers","correctanswers","skipped","timetaken","submittime","state","district"]
            def fileName = "Live_Test_Data_"+
                    (params.resId!=""?params.resId+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response,fileName)
                fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
        }
    }

    @Transactional
    def addNewArrivals(){
        String siteId=utilService.getSiteId(request,session)
        def bookIds = params.bookIds.split(',')
        def bookList = ""
        def validBookIds = []
        def invalidBookIds = []
        def json
        bookIds.eachWithIndex {id,index->
            bookList = dataProviderService.getBooksMst(new Long(id))
            if (bookList.siteId == new Integer(siteId)){
                validBookIds.add(id)
            }else{
                invalidBookIds.add(id)
            }
        }
        if (!validBookIds.isEmpty()){
            KeyValueMst keyValueMst = KeyValueMst.findBySiteIdAndKeyName(new Integer(siteId),'newlyReleasedEbook')
            if (keyValueMst!=null){
                keyValueMst.keyValue = validBookIds.join(',')
                keyValueMst.save()
            }else{
                keyValueMst = new KeyValueMst(keyName: 'newlyReleasedEbook',keyValue: validBookIds.join(','),siteId: siteId)
                keyValueMst.save()
            }
             json = [status: 'Ok',invalidId:invalidBookIds.join(',')]
        }else{
            json = [status: 'ok',invalidId: invalidBookIds.join(',')]
        }

        render json as JSON
    }
}
