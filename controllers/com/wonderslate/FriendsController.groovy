package com.wonderslate

import com.wonderslate.data.ResourceGroupDtl
import com.wonderslate.data.ResourceDtl
import com.wonderslate.log.Quizrecorder
import com.wonderslate.log.Quizrecorderdtl
import com.wonderslate.usermanagement.Friends
import com.wonderslate.usermanagement.Groups
import com.wonderslate.usermanagement.GroupsDtl
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import com.wonderslate.data.ResourceGroupDtl
import grails.converters.JSON
import grails.transaction.Transactional


class FriendsController {
    SpringSecurityService springSecurityService
    UserManagementService userManagementService
    def redisService

    def index() {}

    @Secured(['ROLE_USER'])
    def addFriend() {

        if (params.friendid == null) {
            redirect(controller: "funlearn", action: "index")
        } else {
            User friend = User.findById(new Integer(params.friendid))
            Friends friends = new Friends(friend1: springSecurityService.currentUser.username, friend2: friend.username, status: userManagementService.FRIEND_REQUESTED)
            friends.save(failOnError: true, flush: true)
            def json =
                [
                        'status': "OK"
                ]
            render json as JSON
        }


    }

    @Secured(['ROLE_USER'])
    def acceptFriend() {

        if (params.friendid == null) {
            redirect(controller: "funlearn", action: "index")
        } else {
            User friend1 = User.findById(new Integer(params.friendid))
            Friends friend = Friends.findByFriend1AndFriend2(friend1.username, springSecurityService.currentUser.username)
            if ("1".equals(params.status))
                friend.status = new Integer(userManagementService.FRIEND_ACCEPTED)
            else friend.status = new Integer(userManagementService.FRIEND_IGNORED)
            friend.friendAccepted = new Date();
            friend.save(failOnError: true, flush: true)
            def json =
                [
                        'status': "OK"
                ]
            render json as JSON
        }

    }

    @Secured(['ROLE_USER'])@Transactional
    def getFriendRequests() {


        String sql = "select um.name,um.profilepic,um.id from User um, Friends f where " +
                "  f.friend1 = um.username" +
                " and f.friend2 ='" + springSecurityService.currentUser.username + "' " +
                " and f.status = " + userManagementService.FRIEND_REQUESTED

        def friendsList = User.executeQuery(sql)

        List friends = friendsList.collect { friend ->

            return [name: friend[0], profilepic: friend[1], friendId: friend[2]]
        }

        def json =
            [
                    'results': friends,
                    'status' : friends ? "OK" : "Nothing present"
            ]
        render json as JSON


    }

    @Secured(['ROLE_USER'])
    def currentFriends() {

        String sql = "select um.name, um.profilepic,um.id from User um,Friends f where " +
                "((um.username = f.friend1 and f.friend2='" + springSecurityService.currentUser.username + "')\n" +
                " or (um.username = f.friend2 and f.friend1='" + springSecurityService.currentUser.username + "'))\n" +
                " and f.status=" + userManagementService.FRIEND_ACCEPTED + " order by um.name";
        List results = User.executeQuery(sql)
        List friends = results.collect { friend ->

            return [name: friend[0], profilepic: friend[1], friendId: friend[2]]
        }

        def json =
            [
                    'results': friends,
                    'status' : friends ? "OK" : "Nothing present"
            ]
        render json as JSON

    }

    @Secured(['ROLE_USER'])
    def pendingRequests() {

        String sql = "select um.name, um.profilepic,um.id from User um,Friends f where " +
                "um.username = f.friend1 and f.friend2='" + springSecurityService.currentUser.username + "'\n" +
                " and f.status=" + userManagementService.FRIEND_REQUESTED;


        List results = User.executeQuery(sql)
        List friends = results.collect { friend ->

            return [name: friend[0], profilepic: friend[1], friendId: friend[2]]
        }

        def json =
            [
                    'results': friends,
                    'status' : friends ? "OK" : "Nothing present"
            ]
        render json as JSON

    }

    @Secured(['ROLE_USER'])
    def creategroup() {
        Groups group = new Groups(name: params.name, status: new Integer(userManagementService.GROUP_ACTIVE));
        group.save(failOnError: true, flush: true)

        GroupsDtl groupDtl = new GroupsDtl(groupId: group.id, username: springSecurityService.currentUser.username, role: new Integer(userManagementService.GROUP_CREATOR));
        groupDtl.save(failOnError: true, flush: true)

        params.each { name, value ->
            if (("" + name).startsWith("members_")) {
                groupDtl = new GroupsDtl(groupId: group.id, username: (User.findById(new Integer(value))).username, role: new Integer(userManagementService.GROUP_MEMBER));
                groupDtl.save(failOnError: true, flush: true)
            }

        }
        redirect(controller: "funlearn", action: "groupdtl", params: [groupId: group.id])
    }

    @Secured(['ROLE_USER'])
    def getOwnedGroups() {
        def sql = "select g.name,g.profilepic, g.id from Groups g, GroupsDtl gd where g.id=gd.groupId and gd.username='" + springSecurityService.currentUser.username + "' " +
                " and gd.role in (" + userManagementService.GROUP_CREATOR + "," + userManagementService.GROUP_ADMIN + ")";

        List results = Groups.executeQuery(sql)
        List groups = results.collect { group ->
            return [name: group[0], profilepic: group[1], groupId: group[2]]
        }

        def json =
            [
                    'results': groups,
                    'status' : groups ? "OK" : "Nothing present"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def getMemberGroups() {
        def sql = "select g.name,g.profilepic, g.id from Groups g, GroupsDtl gd where g.id=gd.groupId and gd.username='" + springSecurityService.currentUser.username + "' " +
                " and gd.role in (" + userManagementService.GROUP_MEMBER + ")";

        List results = Groups.executeQuery(sql)
        List groups = results.collect { group ->
            return [name: group[0], profilepic: group[1], groupId: group[2]]
        }

        def json =
            [
                    'results': groups,
                    'status' : groups ? "OK" : "Nothing present"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def getAllGroupsForUser() {
        def sql = "select g.name,g.profilepic, g.id from Groups g, GroupsDtl gd where g.id=gd.groupId and gd.username='" + springSecurityService.currentUser.username + "' " +
                " and gd.role in (" + userManagementService.GROUP_CREATOR + "," + userManagementService.GROUP_ADMIN + "," + userManagementService.GROUP_MEMBER + ")";

        List results = Groups.executeQuery(sql)
        List groups = results.collect { group ->
            return [name: group[0], profilepic: group[1], groupId: group[2]]
        }

        def json =
            [
                    'results': groups,
                    'status' : groups ? "OK" : "Nothing present"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])

    def nonMembers() {
        if (userManagementService.canSeeGroup(params.groupId)) {
            String sql = "select um.name, um.profilepic,um.id from User um,Friends f where " +
                    "((um.username = f.friend1 and f.friend2='" + springSecurityService.currentUser.username + "')\n" +
                    " or (um.username = f.friend2 and f.friend1='" + springSecurityService.currentUser.username + "'))\n" +
                    " and f.status=" + userManagementService.FRIEND_ACCEPTED + " and  um.username not in (select username from GroupsDtl gd where gd.groupId=" + params.groupId + ") order by um.name";

            List results = User.executeQuery(sql)
            List friends = results.collect { friend ->
                return [name: friend[0], profilepic: friend[1], friendId: friend[2]]
            }

            def json =
                [
                    'results': friends,
                    'status' : friends ? "OK" : "Nothing present"
                ]
            render json as JSON
        }

    }

    @Secured(['ROLE_USER'])
    def addMember() {
        if (userManagementService.canSeeGroup(params.groupId)) {
            GroupsDtl groupDtl = new GroupsDtl(groupId: new Integer(params.groupId), username: (User.findById(new Integer(params.friendId))).username,
                    role: new Integer(userManagementService.GROUP_MEMBER), addedBy: springSecurityService.currentUser.username);
            groupDtl.save(failOnError: true, flush: true)
        }
		
        def json =
            [
                'friendId': params.friendId,
                'status'  : "OK"
            ]
        render json as JSON

    }
	
    @Secured(['ROLE_USER'])
    def removeMember() {
        if (userManagementService.canSeeGroup(params.groupId)) {
			def user = User.findById(params.friendId)
            def groupDtl = GroupsDtl.findByGroupIdAndUsername(params.groupId,user.username);
			groupDtl.delete(failOnError: true, flush: true)
        }

        def json =
            [
                'friendId': params.friendId,
                'status'  : "OK"
            ]
        render json as JSON
    }	
	
    @Secured(['ROLE_USER'])
    def makeAdmin() {
        if (userManagementService.canSeeGroup(params.groupId)) {
			def user = User.findById(params.friendId)
            def groupDtl = GroupsDtl.findByGroupIdAndUsername(params.groupId,user.username)
			groupDtl.role = 1
            groupDtl.save(failOnError: true, flush: true)			
        }

        def json =
            [
                'friendId': params.friendId,
                'status'  : "OK"
            ]
        render json as JSON
    }	

    @Secured(['ROLE_USER'])
    def members() {
        if (userManagementService.canSeeGroup(params.groupId)) {
            String sql = "select um.name, um.profilepic,um.id, gd.role from User um,GroupsDtl gd where " +
                    "  um.username= gd.username and gd.groupId=" + params.groupId + " order by um.name";

            List results = User.executeQuery(sql)
			
            List friends = results.collect { friend ->
                return [name: friend[0], profilepic: friend[1], friendId: friend[2], friendRole: friend[3]]
            }

            def json =
                [
                    'results': friends,
                    'status' : friends ? "OK" : "Nothing present"
                ]
            render json as JSON
        }
    }

    @Secured(['ROLE_USER'])
    def getGroupsForSharing() {
        def sql = "select g.name,g.profilepic, g.id from Groups g, GroupsDtl gd where g.id=gd.groupId and gd.username='" + springSecurityService.currentUser.username + "' " +
                " and gd.role in (" + userManagementService.GROUP_CREATOR + "," + userManagementService.GROUP_ADMIN + "," + userManagementService.GROUP_MEMBER + ") and g.id not in (select groupId from ResourceGroupDtl where resourceId=" + params.id + ")";
        List results = Groups.executeQuery(sql)
        List groups = results.collect { group ->
            return [name: group[0], profilepic: group[1], groupId: group[2]]
        }

        def json =
            [
                'results'   : groups,
                'resourceId': params.id,
                'status'    : groups ? "OK" : "Nothing present"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def addGroupShare() {
        ResourceGroupDtl resourceGroupDtl = new ResourceGroupDtl(groupId: new Integer(params.groupId), resourceId: new Integer(params.resourceId), username: springSecurityService.currentUser.username)
        resourceGroupDtl.save(failOnError: true, flush: true);
		
		def rd = ResourceDtl.findById(new Integer(params.resourceId));
		if(rd!=null) {		
			userManagementService.addPoints(springSecurityService.currentUser.username,"SP",(rd.createdBy.equals(springSecurityService.currentUser.username)?"SHAREOWN":"SHAREOTHER"),params.resourceId);
		}		
		
        def json =
            [
                'groupId': params.groupId,
                'status' : "OK"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def groupActivity() {
        String sql = "select u.name,u.profilepic,tm.topicName,tm.id,rd.id,u.id,rd.resType,rd.resourceName,rgd.dateCreated" +
                " from User u,ResourceGroupDtl rgd,TopicMst tm, ResourceDtl rd,Groups gd " +
                " where rgd.groupId=" + params.id +
                " and gd.id=rgd.groupId" +
                " and rd.id=rgd.resourceId" +
                " and tm.id = rd.topicId" +
                " and u.username=rgd.username order by rgd.dateCreated desc"


        List results = Groups.executeQuery(sql)
        List groups = results.collect { group ->
            return [name: group[0], profilepic: group[1], topicName: group[2], topicId: group[3], resourceId: group[4], userId: group[5], resType: group[6], resourceName: group[7], dateCreated: group[8]]
        }

        def json =
            [
                    'results': groups,
                    'status' : groups ? "OK" : "Nothing present"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def userActivity() {
        String sql = "select tm.topicName,tm.id,rd.id,u.id,rd.resType,rd.resourceName,rv.dateCreated" +
                " from User u,ResourceView rv,TopicMst tm, ResourceDtl rd " +
                " where rv.username='" + springSecurityService.currentUser.username + "'" +
                " and rd.id=rv.resourceDtlId" +
                " and tm.id = rd.topicId" +
                " and u.username=rv.username and (rd.sharing is null or rd.sharing not in ('deleted')) order by rv.dateCreated desc"



        List results = Groups.executeQuery(sql,[max:99])
        List activities = results.collect { activity ->
            return [topicName: activity[0], topicId: activity[1], resourceId: activity[2], userId: activity[3], resType: activity[4], resourceName: activity[5], dateCreated: activity[6]]
        }

        def json =
            [
                    'results': activities,
                    'status' : activities ? "OK" : "Nothing present"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def userAllGroupActivity() {
        String sql = "select u.name,u.profilepic,tm.topicName,tm.id,rd.id,u.id,rd.resType,rd.resourceName,rgd.dateCreated from" +
                " User u,ResourceGroupDtl rgd,TopicMst tm, ResourceDtl rd,Groups gd " +
                " where rgd.groupId in (select distinct groupId from GroupsDtl where username='" + springSecurityService.currentUser.username + "')" +
                " and gd.id=rgd.groupId" +
                " and rd.id=rgd.resourceId" +
                " and tm.id = rd.topicId" +
                " and u.username=rgd.username and (rd.sharing is null or rd.sharing not in ('deleted')) order by rgd.dateCreated desc"



        List results = Groups.executeQuery(sql)
        List groups = results.collect { group ->
            return [name: group[0], profilepic: group[1], topicName: group[2], topicId: group[3], resourceId: group[4], userId: group[5], resType: group[6], resourceName: group[7], dateCreated: group[8]]
        }

        def json =
            [
                    'results': groups,
                    'status' : groups ? "OK" : "Nothing present"
            ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def sendInvite(){

    }

    @Secured(['ROLE_WS_CONTENT_CREATOR'])
    def flushRedis(){
        redisService.flushDB()
    }

}
