package com.wonderslate.usermanagement

import grails.converters.JSON
import grails.transaction.Transactional

class ProgressController {

    def springSecurityService
    def redisService
    ProgressService progressService
    AnalyticsService analyticsService
    UserManagementService userManagementService

    @Transactional
    def progressReport()
    {
        String username =  springSecurityService.currentUser.username
        double totalTime = 0 , practiceTime =0
        if(redisService.("userDefaultTimeLog_"+username)!=null&&!"null".equals(redisService.("userDefaultTimeLog_"+username))) totalTime = Double.parseDouble(redisService.("userDefaultTimeLog_"+username))
        if(redisService.("userQuizDefaultTimeLog_"+username)!=null&&!"null".equals(redisService.("userQuizDefaultTimeLog_"+username))) practiceTime = Double.parseDouble(redisService.("userQuizDefaultTimeLog_"+username))
        if(redisService.("latestQuizInfo_"+username)==null) analyticsService.getUsersLastQuiz(username)
        if(redisService.("latestReadBooks_"+username)==null) userManagementService.getLatestReadBooks(username)
        [title:"Progress Report - Wonderslate", totalTime:totalTime, practiceTime:practiceTime, commonTemplate:"true",lastQuizInfo:redisService.("latestQuizInfo_"+username),
         latestReadBooks:redisService.("latestReadBooks_"+username)]
    }

    @Transactional
    def getSubjectwiseInfo() {
        String username = params.username
        if(username==null) username = springSecurityService.currentUser.username
        def json =[results:progressService.getSubjectwiseInfo(username,params.numberOfDays!=null?params.numberOfDays:"7")]
       render json as JSON

    }

    @Transactional
    def progressReportInfo(){
        String username = params.username
        if(username==null) username = springSecurityService.currentUser.username
        def subjectwiseInfo = progressService.getSubjectwiseInfo(username,params.numberOfDays!=null?params.numberOfDays:"7")
        def daywiseInfo = progressService.getQuizTimePerDay(username,params.numberOfDays!=null?params.numberOfDays:"7")
        def quizzesPerDay = progressService.getNumberOfQuizzesPerDay(username,params.numberOfDays!=null?params.numberOfDays:"7")
        def subjectQuizzesTime = progressService.getSubjectQuizTime(username,params.numberOfDays!=null?params.numberOfDays:"7")
        double totalTime = 0 , practiceTime = 0
        String totalTimeString = userManagementService.getUserTimeLog(username,params.numberOfDays!=null?params.numberOfDays:"7")
        String practiceTimeString = userManagementService.getUserQuizTimeLog(username,params.numberOfDays!=null?params.numberOfDays:"7")

         if(totalTimeString!=null&&!"null".equals(totalTimeString)) totalTime = Double.parseDouble(totalTimeString)
          if(practiceTimeString!=null&&!"null".equals(practiceTimeString)) practiceTime = Double.parseDouble(practiceTimeString)

        def json =[subjectwiseInfo:subjectwiseInfo,daywiseInfo:daywiseInfo,quizzesPerDay:quizzesPerDay,subjectQuizzesTime:subjectQuizzesTime,totalTime:totalTime,practiceTime:practiceTime]
        render json as JSON
    }

    @Transactional
    def getHomeInfo(){
        String username = params.username
        if(username==null) username = springSecurityService.currentUser.username
        if(redisService.("testAttemptInfo_"+username+"_7")==null)  progressService.getTestAttemptInfo(username,"7")
        if( redisService.("latestReadBooks_"+username)==null) userManagementService.getLatestReadBooks(username)
        if(redisService.("subjectwiseInfoTotal_"+username+"_7")==null) progressService.getSubjectwiseInfoTotal(username,"7")
        if( redisService.("noOfBooksInLibrary_"+username)==null) userManagementService.numberOfBooksInLibrary(username)
        if(redisService.("latestQuizInfo_"+username)==null) analyticsService.getUsersLastQuiz(username)



        double totalTime = 0 , practiceTime = 0
        String totalTimeString = userManagementService.getUserTimeLog(username,params.numberOfDays!=null?params.numberOfDays:"7")
        String practiceTimeString = userManagementService.getUserQuizTimeLog(username,params.numberOfDays!=null?params.numberOfDays:"7")

        if(totalTimeString!=null&&!"null".equals(totalTimeString)) totalTime = Double.parseDouble(totalTimeString)
        if(practiceTimeString!=null&&!"null".equals(practiceTimeString)) practiceTime = Double.parseDouble(practiceTimeString)

        def json = [totalTime:totalTime,
                    practiceTime:practiceTime,testAttemptInfo:redisService.("testAttemptInfo_"+username+"_7"),
                    latestReadBooks:redisService.("latestReadBooks_"+username),noOfBooksInLibrary:redisService.("noOfBooksInLibrary_"+username),
                    lastQuizInfo:redisService.("latestQuizInfo_"+username), subjectwiseInfo:redisService.("subjectwiseInfoTotal_"+username+"_7")]
        render json as JSON
    }
}
