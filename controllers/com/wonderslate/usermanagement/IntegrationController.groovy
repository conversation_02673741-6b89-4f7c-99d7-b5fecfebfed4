package com.wonderslate.usermanagement

import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import grails.transaction.Transactional

import java.security.SecureRandom
import grails.converters.JSON

class IntegrationController {
    def redisService
    def index() { }

    @Transactional
    def validate(){
        InstituteMst instituteMst = InstituteMst.findBySecretKey(params.secretKey)
        if(instituteMst!=null){
            String sessionKey = createSessionKey()
            redisService.(""+sessionKey)=""+instituteMst.id
            User user = null
            if(params.email!=null&&!"".equals(params.email)) user = User.findByUsername(instituteMst.siteId+"_"+params.email)
            else if(params.mobile!=null&&!"".equals(params.mobile)) user = User.findByUsername(instituteMst.siteId+"_"+params.mobile)
            else  user = User.findByUsername(instituteMst.siteId+"_institute"+instituteMst.id)
            String name = "User of "+instituteMst.name
            if(params.name!=null&&!"".equals(params.name)) name=params.name
            if(user==null){
                String username,mobile=null,email=null
                Integer maxLogins  = null
                if(params.email!=null&&!"".equals(params.email)) {
                    username=instituteMst.siteId+"_"+params.email
                    email = params.email


                }
                else if(params.mobile!=null&&!"".equals(params.mobile)) {
                    username=instituteMst.siteId+"_"+params.mobile
                    mobile = params.mobile
                }
                else {
                    username = instituteMst.siteId+"_institute"+instituteMst.id
                    maxLogins = new Integer(100)
                }
                String plainPassword = UUID.randomUUID().toString().substring(0, 5).toLowerCase();
                String password = plainPassword

                WinGenerator winGenerator = new WinGenerator()
                winGenerator.save(failOnError: true)

                user = new User(username: username, name: name, password: password, win: winGenerator.id, siteId: instituteMst.siteId,maxLogins: maxLogins,mobile: mobile,email:email)
                user.save(failOnError: true, flush: true)
                //add appropriate roles
                Role role = Role.findByAuthority("ROLE_USER")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_ADD")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                UserRole.create(user, role, true)
                CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedByAndNameAndStatus(instituteMst.id, "Default", "active")
                BatchUserDtl batchUserDtl = new BatchUserDtl(username: user.username, batchId: courseBatchesDtl.id,
                        createdBy: "Integrtion")
                batchUserDtl.save(failOnError: true, flush: true)

            }
            redisService.(""+sessionKey+"Username")=""+user.username
            def json = [status:"OK",sessionKey: sessionKey]
            render json as JSON
        }else{
            def json = ["status":"INVALID_KEY"]
            render json as JSON
        }
    }

    def createSessionKey(){
        boolean keyExists = true
        String sessionKey=""
       def chars = (('0'..'9') + ('A'..'Z') + ('a'..'z')).flatten()
            def random = new SecureRandom()
            sessionKey = (1..16).collect { chars[random.nextInt(chars.size())] }.join()
        return sessionKey
    }

}
