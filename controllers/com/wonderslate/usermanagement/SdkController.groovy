package com.wonderslate.usermanagement

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.publish.BooksPermission
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.mail.MailService
import grails.transaction.Transactional

class SdkController {
    def springSecurityService
    MailService mailService
    DataProviderService dataProviderService

    def redisService
    def index() { }

    @Transactional
    def appLogin(){
        String username
        String secretKey
        String authenticationTokenString
        Integer siteId

        def json
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            secretKey=jsonObj.secretKey
            username=jsonObj.siteId+"_"+jsonObj.uniqueId
            siteId = new Integer(""+jsonObj.siteId)

        } else {
            secretKey=params.secretKey
            username=params.siteId+"_"+params.uniqueId
            siteId = new Integer(""+params.siteId)
        }

        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+siteId)
        if(secretKey==null||secretKey.trim().length()==0||keyValueMst==null||!secretKey.equals(keyValueMst.keyValue)) {
            json = ["status":"Failed","message":"Invalid secret key"]
            render json as JSON
            return
        }
        User user = User.findByUsername(username)

        if (user!=null) {
            try {
                springSecurityService.reauthenticate(user.username)
                //valid user

                //first do the force logout of already logged in users
                UUID gfg = UUID.randomUUID();
                authenticationTokenString = gfg.toString()

                AuthenticationToken authenticationToken = new AuthenticationToken(username: user.username, token: authenticationTokenString)
                authenticationToken.save(failOnError: true, flush: true)
                UserLog log = new UserLog(username: user.username, action: "login")
                log.save(failOnError: true, flush: true)
            } catch (Exception e) {
                println " login  failed " + e.toString()
            }



            json = ["username": user.username, "access_token": authenticationTokenString,
                    "roles"   : springSecurityService.getPrincipal().authorities*.authority, "status"  : "ok"]

        }else{
            //password fail
            json = ["status":"Failed","message":"Invalid username"]

        }
        render json as JSON

    }

    @Transactional
    def addUserAndCourse(){
        def siteId = new Integer(params.siteId)
        String uniqueId = params.uniqueId
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+siteId)
        if(keyValueMst!=null&&keyValueMst.keyValue.equals(params.secretKey)&&uniqueId!=null) {
               String username = "" + siteId + "_" + uniqueId.trim()
                User user = User.findByUsername(username)
                String plainPassword
                if (user == null) {
                    String name = params.name
                    plainPassword = UUID.randomUUID().toString().substring(0, 5).toLowerCase();
                    String password = plainPassword
                    String mobile = params.mobile.trim().replace('+','').replace(' ','')
                    mobile = mobile.replaceAll(' ','')
                    String email = params.email
                    String teacher = params.teacher!=null?"true":null
                    String school = params.school
                    String classStudying = params.classStudying
                    WinGenerator winGenerator = new WinGenerator()
                    winGenerator.save(failOnError: true)

                    user = new User(username: username, name: name, password: password, mobile: mobile, email: email, win: winGenerator.id,
                            siteId: siteId,teacher: teacher,school:school,classStudying:classStudying)
                    user.save(failOnError: true, flush: true)
                    //add appropriate roles
                    Role role = Role.findByAuthority("ROLE_USER")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_ADD")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                    UserRole.create(user, role, true)
                }else{
                    if(params.email!=null) user.email = params.email
                    if(params.mobile!=null&&user.mobile==null) user.mobile=params.mobile
                    user.save(failOnError: true, flush: true)
                }

                BooksMst booksMst
                if (params.courseId != null) {
                        booksMst = dataProviderService.getBooksMst(new Long(params.courseId))
                        if (booksMst != null) {
                            BooksPermission booksPermission = new BooksPermission()
                            booksPermission.bookId = booksMst.id
                            booksPermission.username = user.username
                            booksPermission.clientPo = params.paymentReference
                            booksPermission.poType = 'SDKINTEGRATION'
                            booksPermission.addedBy = "System"
                            Calendar c = Calendar.getInstance()
                            c.add(Calendar.DATE, 365)
                            booksPermission.expiryDate = c.getTime()
                            booksPermission.chatTokensBalance=99999
                            booksPermission.save(failOnError: true, flush: true)
                            booksPermission.bookType="bookGPT"

                            redisService.("userShelfBooks_"+user.username)=null
                        } else {

                            try {
                                mailService.sendMail {
                                    async true
                                    to "<EMAIL>", "<EMAIL>", "<EMAIL>"
                                    from "Wonderslate <<EMAIL>>"
                                    subject "Missing bookId " + siteId
                                    text "This bookId is not found " + params.courseId + " order ref " + params.uniqueId
                                }

                            } catch (Exception e) {
                                println("Exception in sending welcome <NAME_EMAIL> and exception is " + e.toString())
                            }
                        }

                    def json = [status: "success"]
                    render json as JSON

                } else {
                    def json = [status: "fail", reason: "Invalid courseId"]
                    render json as JSON
                }

        }else{
            def json = [status:"fail",reason:"Invalid secret key or mobile number not provided"]
            render json as JSON
        }
    }

    @Secured(['ROLE_MASTER_LIBRARY_ADMIN']) @Transactional
    def sdkIntegrationReport(Date startDate, Date endDate, String uniqueId) {
        // Get the session siteId
        def siteId = session["siteId"]

        // Get parameters
        uniqueId = uniqueId?.trim()
        def max = params.int('max') ?: 20
        def offset = params.int('offset') ?: 0

        // Time zone conversion: User's local time to GMT
        TimeZone userTimeZone = TimeZone.getTimeZone(session['timeZone'] ?: 'UTC')
        TimeZone gmtTimeZone = TimeZone.getTimeZone('GMT')

        // Construct startDate from date components
        if (params.startDate_year && params.startDate_month && params.startDate_day) {
            startDate = new GregorianCalendar(
                    params.int('startDate_year'),
                    params.int('startDate_month') - 1, // Months are zero-based
                    params.int('startDate_day')
            ).time
            startDate = convertToGMT(startDate, userTimeZone, gmtTimeZone)
        }

        // Construct endDate from date components
        // Build criteria query
        def criteria = BooksPermission.createCriteria()
        def results = criteria.list(max: max, offset: offset) {
            if (uniqueId) {
                // Filter by uniqueId only, ignore date filters
                eq('username', "${siteId}_${uniqueId}")
            } else {
                // Filter by dateCreated
                if (startDate) {
                    ge('dateCreated', startDate)
                }
                if (endDate) {
                    lt('dateCreated', endDate)
                }
                // Only include records with the current siteId
                like('username', "${siteId}_%")
            }
            eq('poType','SDKINTEGRATION')
            // Order by dateCreated descending
            order('dateCreated', 'desc')
        }

        // Fetch associated data
        def reportData = results.collect { bp ->
            def username = bp.username
            def uniqueIdPart = username.replaceFirst("${siteId}_", "")
            def user = User.findByUsername(username)
            def book = BooksMst.findById(bp.bookId)
            [
                    dateCreated: bp.dateCreated,
                    uniqueId  : uniqueIdPart,
                    name      : user?.name ?: '',
                    bookTitle : book?.title ?: ''
            ]
        }

        // Pass data to view
        [reportData: reportData, total: results.totalCount, max: max, offset: offset, params: params]
    }

    private Date convertToGMT(Date date, TimeZone userTimeZone, TimeZone gmtTimeZone) {
        def userOffset = userTimeZone.getOffset(date.time)
        def gmtOffset = gmtTimeZone.getOffset(date.time)
        def offsetDifference = userOffset - gmtOffset
        return new Date(date.time - offsetDifference)
    }
}
