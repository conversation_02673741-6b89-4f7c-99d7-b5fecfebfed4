package com.wonderslate.usermanagement

import com.github.scribejava.core.model.OAuth2AccessToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BatchUserDtlNotRegistered
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.oauth2.SpringSecurityOauth2BaseService
import grails.plugin.springsecurity.oauth2.exception.OAuth2Exception
import grails.transaction.Transactional
import org.grails.web.util.WebUtils
import org.springframework.security.core.context.SecurityContextHolder
import grails.plugin.springsecurity.oauth2.token.OAuth2SpringToken
import org.springframework.security.web.authentication.session.ConcurrentSessionControlAuthenticationStrategy

import javax.servlet.http.Cookie

class SpringSecurityOAuth2Controller {

    public static final String SPRING_SECURITY_OAUTH_TOKEN = 'springSecurityOAuthToken'

    SpringSecurityOauth2BaseService springSecurityOauth2BaseService
    SpringSecurityService springSecurityService
    def rememberMeServices
    UserManagementService userManagementService
    UserLogService userLogService
    DataProviderService dataProviderService
    def concurrentSessionControlAuthenticationStrategy
    def sessionRegistry
    def securityContextLogoutHandler



    def callback() {
        String providerName = params.provider

        // Check if we got an AuthCode from the server query
        String authCode = params.code
        if (!authCode || authCode.isEmpty()) {
            throw new OAuth2Exception("No AuthCode in callback for provider '${providerName}'")
        }


        // Create the relevant authentication token and attempt to log in.
        OAuth2SpringToken oAuthToken =  createAuthToken(providerName,authCode)

        // Create the relevant authentication token and attempt to log in.
        SecurityContextHolder.context.authentication = oAuthToken
        session[SPRING_SECURITY_OAUTH_TOKEN] = oAuthToken
        def authentication = SecurityContextHolder.context.authentication
        boolean allowLogin=true
        try{
            println("authentication username="+authentication.principal.username);
          if("Do not allow".equals(userLogService.checkNumberOfSimultaneousUsers(authentication.principal.username))) allowLogin = false;

        }catch (Exception e){
            allowLogin=false;

        }
        if(allowLogin) {
            rememberMeServices.loginSuccess(request, response, authentication)
            userLogService.addUserLog("login")
            redirect([url: '/security/loginmanager'])

        }
        else{
            SecurityContextHolder.clearContext()
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
           redirect( [url: '/security/loginfailedmanager'])
        }
    }

   @Transactional
    protected OAuth2SpringToken createAuthToken(providerName, authCode) {
        def providerService = springSecurityOauth2BaseService.getProviderService(providerName)
        OAuth2AccessToken oAuthToken = providerService.getAccessToken(authCode)
        String username ="";
        def user,authType;
        def response = providerService.getResponse(oAuthToken)
        OAuth2SpringToken oAuth2SpringToken;
        try {
            user = JSON.parse(response.body)
             if ("facebook".equals(providerName)) {
                username="Facebook"+user.id
                authType = "Facebook"
            }
            else if ("google".equals(providerName)) {
                username="Google"+user.email
                authType = "Google"
            }
            authType = "Google"
        } catch (Exception e) {
            log.error "Error parsing response from Google. Response:\n${response.body}"
            throw new OAuth2Exception('Error parsing response from Google', e)
        }


        def oUser
        if ("google".equals(providerName)){
             oUser = User.findByEmail(user.email)

        }else{
            oUser = User.findByUsername(username)
        }

        if (oUser) {
         //   oAuthToken = updateOAuthToken(oAuthToken, oUser)
            springSecurityService.reauthenticate(oUser.username)
            oAuth2SpringToken = springSecurityOauth2BaseService.createAuthToken(providerName, oAuthToken)
            oAuth2SpringToken = springSecurityOauth2BaseService.updateOAuthToken(oAuth2SpringToken, oUser)
           if(oUser.name.equals("Name not provided")){
               oUser.name=user.name
               oUser.save(failOnError: true,flush: true)
           }
        }
        else{
            String email = "";
            String name="";
            String facebook="";
            email = user.email
            name = (user.name!=null&&!"".equals(user.name))?user.name:"Name not provided";
            facebook = user.link
            WinGenerator winGenerator = new WinGenerator()
            winGenerator.save(failOnError: true)
            User person = new User(
                    username: username,
                    password: user.id, //not really necessary
                    enabled: true,
                    accountExpired:  false,
                    accountLocked: false,
                    passwordExpired: false,
                    name: name,
                    email: (email!=null?email:"<EMAIL>"),
                    facebook: facebook,
                    authType: authType,
                    win : winGenerator.id,
                    registeredFrom: "web",
                    siteId: session["siteId"]!=null?session["siteId"]:new Integer(1)
            )
            person.save(failOnError: true)

            UserRole.create(person, Role.findByAuthority('ROLE_USER'),true)
            UserRole.create(person, Role.findByAuthority('ROLE_CAN_ADD'),true)
            UserRole.create(person, Role.findByAuthority('ROLE_CAN_UPLOAD'),true)
             springSecurityService.reauthenticate(person.username)

            oAuth2SpringToken = springSecurityOauth2BaseService.createAuthToken(providerName, oAuthToken)
            oAuth2SpringToken = springSecurityOauth2BaseService.updateOAuthToken(oAuth2SpringToken, person)
            List batchesToBeAdded = BatchUserDtlNotRegistered.findAllByEmail(email)
            batchesToBeAdded.each{batch->
                BatchUserDtl batchUserDtl =  new  BatchUserDtl(username: person.username, batchId: batch.batchId, createdBy: batch.createdBy,instructor: batch.instructor)
                batchUserDtl.save(failOnError: true, flush: true)

            }
            dataProviderService.getBooksListForUser(person.username)
            try {
            userManagementService.sendWelcomeEmail(person.email,person.name,session["siteId"]!=null?session["siteId"]:new Integer(1));
            } catch (Exception e){
                println "sending welcome email failed "+e.toString()
            }

        }


        return oAuth2SpringToken

    }



}
