package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.comparison.SorensenDiceService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.SiteManagerService
import com.wonderslate.data.SiteMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SitemapService
import com.wonderslate.data.UtilService
import com.wonderslate.groups.GroupsService
import com.wonderslate.information.InformationMst
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.institute.InstituteService
import com.wonderslate.publish.ChapterAccess
import com.wonderslate.publish.Publishers
import com.wonderslate.publish.SyllabusGradeDtl
import com.wonderslate.shop.DirectSaleOrders
import com.wonderslate.shop.PurchaseService
import com.wonderslate.shop.WsshopService
import com.wonderslate.toDo.ToDoService
import com.wonderslate.usermanagement.AnalyticsService
import com.wonderslate.usermanagement.AuthenticationToken
import com.wonderslate.usermanagement.ProgressService
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.usermanagement.UserRole
import com.wonderslate.usermanagement.WinGenerator
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import groovy.sql.Sql
import grails.converters.JSON
import org.grails.web.util.WebUtils
import org.springframework.security.core.context.SecurityContextHolder
import pl.touk.excel.export.WebXlsxExporter

import javax.servlet.http.Cookie
import java.text.SimpleDateFormat
import groovy.json.JsonOutput


class BooksController {
    SpringSecurityService springSecurityService
    UserManagementService userManagementService
    DataProviderService dataProviderService
    UtilService utilService
    SorensenDiceService sorensenDiceService
    ToDoService toDoService
    WsshopService wsshopService
    GroupsService groupsService
    def redisService
    def rememberMeServices
    PurchaseService purchaseService
    InstituteService instituteService
    AnalyticsService analyticsService
    ProgressService progressService
    SiteManagerService siteManagerService
    SitemapService sitemapService


    @Transactional
    def setUserSession(){
         siteManagerService.setBooksUserSession(request,session,response,servletContext)
    }
    def store() {
        setUserSession();
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }
        if("true".equals(grailsApplication.config.grails.appServer.main)||"books".equals(grailsApplication.config.grails.appServer.default)) {

            if (params.tagName == null && !"browse".equals(params.mode) && !"grade".equals(params.mode)&& springSecurityService.currentUser != null) redirect([uri: '/library'])
            def siteMst = SiteMst.findById(session['siteId'])
            [otpReg: siteMst != null && siteMst.otpReg != null && siteMst.otpReg]
        }else{
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }

    }

    @Transactional
    def ebooks(){
        String url = request.getRequestURL()
        if (url.indexOf("http://localhost") > -1 || url.indexOf("wonderslate.com") > -1 || url.indexOf("prepjoy.com") > -1 || url.indexOf("ibookso.com") > -1) {


        String publisherId=params.publisherId
        String publisherDescription  =  null
        setUserSession();

       if(params.affiliationCd!=null) session.setAttribute("affiliationCd",params.affiliationCd)
        //to show download catalogue option for ws sales team
        boolean showDownloadCatalogue = false;
        User user = session["userdetails"]
        if(user!=null && user.authorities.any({ it.authority == "ROLE_WS_SALES_TEAM"})) showDownloadCatalogue=true

        //lets get the book categories
        if(session['siteId']==null) session['siteId'] = new Integer(1)
        //coming from mobile
        if(params.tokenId!=null){
            if(springSecurityService.currentUser==null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if(authenticationToken!=null){
                    springSecurityService.reauthenticate(authenticationToken.username)
                }
            }
            session['appType']=params.appType
        }

        //lets get the latest books also. If the cookies are present then will call the books according the category otherwise just the latest books.

        if(redisService.("siteIdList_"+session['siteId'])==null) {
            dataProviderService.getSiteIdList(session['siteId'])
        }
        Publishers publishers = null
        if(params.publisherId!=null) {
            publisherId = params.publisherId
            publishers = dataProviderService.getPublisher(new Integer(publisherId))

        }
        else if(params.publisher!=null){
             publishers = wsshopService.getPublisherByName(params.publisher.split("-").join(" "))
            publisherId = ""+publishers.id
            params.put("publisherId",publisherId)
        }
        if(publishers!=null&&publishers.tagline!=null&&!"".equals(publishers.tagline)&&("1".equals(""+session["siteId"])||"27".equals(""+session["siteId"]))) publisherDescription=publishers.tagline

        int pageNo=0
        params.put("publisherId",publisherId)
        HashMap seo = sitemapService.getSEO(params,new Integer(""+session["siteId"]))
        params.put("level",seo.get("level"))
        params.put("syllabus",seo.get("syllabus"))
        params.put("grade",seo.get("grade"))


        if(params.pageNo!=null&&!"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
        HashMap booksAndPublishers = wsshopService.getBooksList(params,session["siteId"],pageNo)

        if(redisService.("bannerList_"+session['siteId'])==null) dataProviderService.getBanners(""+session['siteId'])
        def bannerList = redisService.("bannerList_"+session['siteId'])

        params.put('hideJS','true')
        ["title":seo.get("browserTitle"), commonTemplate:"true", showDownloadCatalogue:showDownloadCatalogue,publisherId:publisherId, showPublishers:"true",
         seoDesc:seo.get("browserDescription"),bannerList:bannerList,booksList:booksAndPublishers,keywords:seo.get("browserKeywords"),onPageTitle:seo.get("onPageTitle"),
         onPageDescription:seo.get("onPageDescription"),storeUrl:"/ebooks",publisherDescription:seo.get("publisherDescription")!=null?seo.get("publisherDescription"):null,
         publisherName:seo.get("publisherName"),aboutTitle:seo.get("aboutTitle"),aboutDescription:seo.get("aboutDescription")]
        } else {
            redirect(controller: "books", action: 'index')
            return
        }
    }


    @Transactional
    def index(){
        String url = (request.getRequestURL()).toString()
        if (url.contains("wonderslate") || url.contains("localhost")) {

        setUserSession();



        //to make the user go to dashboard when he logs in using remember me services

        if(springSecurityService.currentUser!=null&&springSecurityService.currentUser.username!=null&&!"true".equals(session.getAttribute("visitedDashboard"))) {
            userManagementService.registerUserLogin(springSecurityService.currentUser.username,session.getId())
            session["visitedDashboard"]="true"
        }
            boolean showSearchAtTop=false
            if (session.getAttribute("userdetails") != null) {
                String username =  springSecurityService.currentUser.username

                double totalTime = 0 , practiceTime = 0
                if(redisService.("userDefaultTimeLog_"+username)!=null&&!"null".equals(redisService.("userDefaultTimeLog_"+username))) totalTime = Double.parseDouble(redisService.("userDefaultTimeLog_"+username))
                if(redisService.("userQuizDefaultTimeLog_"+username)!=null&&!"null".equals(redisService.("userQuizDefaultTimeLog_"+username))) practiceTime = Double.parseDouble(redisService.("userQuizDefaultTimeLog_"+username))
                if( redisService.("latestReadBooks_"+username)==null) userManagementService.getLatestReadBooks(username)
                if( redisService.("noOfBooksInLibrary_"+username)==null) userManagementService.numberOfBooksInLibrary(username)
                if(redisService.("latestQuizInfo_"+username)==null) analyticsService.getUsersLastQuiz(username)
                if(redisService.("testAttemptInfo_"+username+"_7")==null)  progressService.getTestAttemptInfo(username,"7")
                if(redisService.("subjectwiseInfoTotal_"+username+"_7")==null) progressService.getSubjectwiseInfoTotal(username,"7")

                if(session["userdetails"]==null||"0".equals(redisService.("noOfBooksInLibrary_"+username))){
                    showSearchAtTop=true
                }
                 [commonTemplate: "true", currentBadge: session["currentBadge"], totalTime:totalTime,
                 practiceTime:practiceTime,testAttemptInfo:redisService.("testAttemptInfo_"+username+"_7"), subjectwiseInfo:redisService.("subjectwiseInfoTotal_"+username+"_7"),
                latestReadBooks:redisService.("latestReadBooks_"+username),noOfBooksInLibrary:redisService.("noOfBooksInLibrary_"+username),
                lastQuizInfo:redisService.("latestQuizInfo_"+username),showSearchAtTop:showSearchAtTop]
            }else{

                [commonTemplate: "true", currentBadge: session["currentBadge"],showSearchAtTop:true]
            }
        }else {
            if(url.contains(".com")) {
                String baseUrl = url.split('.com/')[0]
                redirect([uri: baseUrl + '.com'])
            }
        }

    }

    @Transactional
    def toppers(){

        String userName = ""
        User user = null
        //coming from mobile
        if(params.tokenId!=null){
            if(springSecurityService.currentUser==null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if(authenticationToken!=null){
                    springSecurityService.reauthenticate(authenticationToken.username)
                } else{
                    println("AuthenticationToken is empty for tokenId===== "+tokenId)
                    println("appType======"+params.appType)
                }
            }
            session['appType']=params.appType
        }
        if(user == null){
            println("user is empty for tokenId===== "+ params.tokenId)
            println("appType======"+params.appType)
        }
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            userName = user.username
        }
        setUserSession();
        ["user":userName, "title":"Toppers Secrets - Wonderslate"]
    }

    @Transactional
    def careercounselling(){
        String userName = ""
        User user = null
        //coming from mobile
        if(params.tokenId!=null){
            if(springSecurityService.currentUser==null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if(authenticationToken!=null){
                    springSecurityService.reauthenticate(authenticationToken.username)
                } else{
                    println("AuthenticationToken is empty for tokenId===== "+tokenId)
                    println("appType======"+params.appType)
                }
            }
            session['appType']=params.appType
        }
        if(user == null){
            println("user is empty for tokenId===== "+ params.tokenId)
            println("appType======"+params.appType)
        }
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            userName = user.username
        }
        setUserSession();
        ["user":userName, "title":"Career Counselling - Wonderslate"]
    }

    @Transactional
    def studentproblems(){
        String userName = ""
        User user = null
        //coming from mobile
        if(params.tokenId!=null){
            if(springSecurityService.currentUser==null) {
                String tokenId = params.tokenId
                AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                if(authenticationToken!=null){
                    springSecurityService.reauthenticate(authenticationToken.username)
                } else{
                    println("AuthenticationToken is empty for tokenId===== "+tokenId)
                    println("appType======"+params.appType)
                }
            }
            session['appType']=params.appType
        }
        if(user == null){
            println("user is empty for tokenId===== "+ params.tokenId)
            println("appType======"+params.appType)
        }
        if(springSecurityService.currentUser != null) {
            user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            userName = user.username
        }
        setUserSession();
        ["user":userName, "title":"Student Problems - Wonderslate"]
    }
    @Secured(['ROLE_USER']) @Transactional
    def dashboard(){

        //this line is required to take user to the dashboard
        session.setAttribute("visitedDashboard","true")
        if(userManagementService.isValidSession(springSecurityService.currentUser.username,session.getId())) {
            setUserSession();
            ["title": "My Dashboard - Wonderslate"]
        }else{
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect([uri: '/logoff'])
        }
    }
    @Secured(['ROLE_USER']) @Transactional
    def getInstitutesForUser(){

        if(redisService.("institutesList_"+springSecurityService.currentUser.username)==null) dataProviderService.getInstitutesForUser()

        def json = ['institutesList':userManagementService.getInstitutesForUser(1,request)]
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def myActivity(){
        ["title":"My Learning History - Wonderslate", commonTemplate:"true"]
    }

    @Secured(['ROLE_USER']) @Transactional
    def home() {
        session.setAttribute("visitedDashboard","true")
        if(userManagementService.isValidSession(springSecurityService.currentUser.username,session.getId())) {
            setUserSession();
            User user = session["userdetails"]
            boolean showPublisherControls=false,hasSalesAccess=false,wsAdmin=false,instituteAdmin=false,showPublisherAdminControls=false,informationAdmin=false,
                    guestUser=false,customerSupport=false,groupsAdmin=false,salesSupport=false,libraryUserUploader=false,externalSalesAccess=false,gptManagerAccess=false,accountsAccess=false,
            iBookGPTSiteAdmin=false,masterLibraryAdmin=false,supportManager=false

            // for publisher admin controlls
            if(session["userdetails"].publisherId!=null&&!session["isInstitutePublisher"]&&user.authorities.any {

                it.authority == "ROLE_WS_CONTENT_ADMIN"
            }) showPublisherAdminControls = true

            // for publisher content uploaders
            if(session["userdetails"].publisherId!=null&&!session["isInstitutePublisher"]&&user.authorities.any {
                it.authority == "ROLE_BOOK_CREATOR"
            }) showPublisherControls = true
            //for wonderslate content creators
            else if(session["userdetails"].publisherId==null &&user.authorities.any {
                it.authority == "ROLE_WS_CONTENT_ADMIN"
            }) wsAdmin = true

            //support manager
            if(user.authorities.any {
                it.authority == "ROLE_SUPPORT_MANAGER"
            }) supportManager = true

            //iBookGPT Site admin
            if(user.authorities.any {
                it.authority == "ROLE_IBOOKGPT_SITE_ADMIN"
            }) iBookGPTSiteAdmin = true

            def existCount=session.getAttribute("groupWallNotificationCount")
            if(redisService.("groupWallId_"+session['siteId']+"_latestPostCount")==null || redisService.("groupWallId_"+session['siteId']+"_latestPostCount")=="null")
            {
                groupsService.getlatestPostCountForGroupWall(session['siteId'])
            }
            if(existCount==null) session.setAttribute("groupWallNotificationCount",redisService.("groupWallId_"+session['siteId']+"_latestPostCount"))
            session.setAttribute("groupWallPendingCount",(new Integer(redisService.("groupWallId_"+session['siteId']+"_latestPostCount"))-new Integer(session.getAttribute("groupWallNotificationCount")))>0?(new Integer(redisService.("groupWallId_"+session['siteId']+"_latestPostCount"))-new Integer(session.getAttribute("groupWallNotificationCount"))):"0")

            //institutes for user
            List userInstitutes
            if(session["userInstitutes"]!=null) userInstitutes = session["userInstitutes"]
           else    {
                userInstitutes=userManagementService.getInstitutesForUser(1,utilService.getIPAddressOfClient(request))
                if(userInstitutes.size()>1){
                    for(int i=0;i<userInstitutes.size();i++){
                        if(!("Default".equals(""+userInstitutes[i].batchName))) userInstitutes.remove(i--)
                    }
                }
                session["userInstitutes"] = userInstitutes
            };
            //sales access
            if(user.authorities.any {
                it.authority == "ROLE_FINANCE" || it.authority == "ROLE_AFFILIATION_SALES"
            }) {
                hasSalesAccess = true
            }

            //instituteAdmin users - eClass+
            if(user.authorities.any {
                it.authority == "ROLE_INSTITUTE_ADMIN"||it.authority == "ROLE_INSTITUTE_REPORT_MANAGER"
            }) {
                instituteAdmin = true
            }

            //library user uploader
            if(user.authorities.any {
                it.authority == "ROLE_LIBRARY_USER_UPLOADER"
            }) {
                libraryUserUploader = true
            }
            if(user.username.contains("1_cookie_"))guestUser=true
            //information admin users
            if(user.authorities.any {
                it.authority == "ROLE_INFORMATION_ADMIN"
            }) {
                informationAdmin = true
            }

            //customer support access
            if(user.authorities.any {
                it.authority == "ROLE_CUSTOMER_SUPPORT"
            }) {
                customerSupport = true
            }

            //sales team access
            if(user.authorities.any {
                it.authority == "ROLE_WS_SALES_TEAM"
            }) {
                salesSupport = true
            }

            //group admin control
            if(user.authorities.any {
                it.authority == "ROLE_WS_GROUP_ADMIN"
            }) {
                groupsAdmin = true
            }

            //external sales report viewer
            //information admin users
            if(user.authorities.any {
                it.authority == "ROLE_EXTERNAL_SALES_VIEWER"
            }) {
                externalSalesAccess = true
            }
            def groupWallId=null
            if(redisService.("defaultGroupWallId_1")==null){
                KeyValueMst keyValueMst= KeyValueMst.findByKeyNameAndSiteId("groupWallId",1)
                if(keyValueMst!=null ) redisService.("defaultGroupWallId_1")=keyValueMst.keyValue
            }
            groupWallId = redisService.("defaultGroupWallId_1")

            //gpt manager access
            if(user.authorities.any {
                it.authority == "ROLE_GPT_MANAGER"
            }) {
                gptManagerAccess = true
            }

            //accounts access
            //information admin users
            if(user.authorities.any {
                it.authority == "ROLE_ACCOUNTS"
            }) {
                accountsAccess = true
            }

            if(user.authorities.any {
                it.authority == "ROLE_MASTER_LIBRARY_ADMIN"||it.authority == "ROLE_LIBRARY_ADMIN"
            }) masterLibraryAdmin = true

            ["title": "Home - Wonderslate",userInstitutes:userInstitutes,showPublisherControls:showPublisherControls,hasSalesAccess:hasSalesAccess,
             wsAdmin:wsAdmin,instituteAdmin:instituteAdmin, commonTemplate:"true", showPublisherAdminControls:showPublisherAdminControls,informationAdmin:informationAdmin,guestUser:guestUser,
             customerSupport:customerSupport,groupWallId:groupWallId,groupsAdmin:groupsAdmin,salesSupport:salesSupport,libraryUserUploader:libraryUserUploader,
             externalSalesAccess:externalSalesAccess,gptManagerAccess:gptManagerAccess,accountsAccess:accountsAccess,iBookGPTSiteAdmin:iBookGPTSiteAdmin,masterLibraryAdmin:masterLibraryAdmin,
             supportManager:supportManager]
        }else{
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect([uri: '/logoff'])
        }


    }

    @Secured(['ROLE_WS_SALES_TEAM']) @Transactional
    def downloadCatalogue(){
        String serverUrl = request.getScheme()+"://"+request.getServerName()+
                ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                        "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                        request.getServerPort())
        Integer siteId=utilService.getSiteId(request, session)
        String level,syllabus, grade, subject,publisherId = null
        boolean freeBooks = false
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String mcqBook=null
        String catalogueName="Wonderslate"
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results
        def publisherCondition=""
        def optionalCondition="";
        String siteIdList = siteId.toString()
        if (siteId.intValue() == 1) {
            if (redisService.("siteIdList_1") == null) {
                dataProviderService.getSiteIdList(new Integer(1))
            }
            siteIdList = redisService.("siteIdList_1")
        }
        String keyName = "";
        if ("true".equals(params.freeBooks)) {
            freeBooks = true

        }
        if (params.level != null && !"null".equals(params.level)&& !"".equals(params.level)) {
            keyName = params.level.replaceAll("\\s+", "")
            level = params.level
            catalogueName=level

        }
        if (params.syllabus != null && !"null".equals(params.syllabus)&& !"".equals(params.syllabus)) {
            keyName = keyName + "_" + params.syllabus.replaceAll("\\s+", "")
            syllabus = params.syllabus
        }
        if (params.grade != null && !"null".equals(params.grade)&& !"".equals(params.grade)) {
            keyName = keyName + "_" + params.grade.replaceAll("\\s+", "")
            grade = params.grade
        }
        if (params.subject != null && !"null".equals(params.subject)&& !"".equals(params.subject)) {
            keyName = keyName + "_" + params.subject.replaceAll("\\s+", "")
            subject = params.subject
        }
        if (params.gradeId != null && !"null".equals(params.gradeId)) {
            SyllabusGradeDtl syllabusGradeDtl = dataProviderService.getSyllabusGradeDtl(new Long(params.gradeId))
            if (syllabusGradeDtl != null && syllabusGradeDtl.syllabus != null) {
                keyName = keyName + "_" + syllabusGradeDtl.syllabus.replaceAll("\\s+", "")
                syllabus = syllabusGradeDtl.syllabus
            }
            if (syllabusGradeDtl != null && syllabusGradeDtl.grade != null) {
                keyName = keyName + "_" + syllabusGradeDtl.grade.replaceAll("\\s+", "")
                grade = syllabusGradeDtl.grade
            }
        }
        if (params.publisherId != null && !"null".equals(publisherId)) {
            keyName = keyName + "_" + params.publisherId.replaceAll("\\s+", "")
            publisherId = params.publisherId
        }
        if (params.mcqBook != null && !"null".equals(mcqBook)) {
            keyName = keyName + "_" + params.mcqBook
            mcqBook = params.mcqBook
        }

        if(keyName.indexOf("related_")==0||keyName.indexOf("bestSellers")==0) limitCondition = " limit 10"
        if(keyName.indexOf("bestSellers")==0) {

        }
        if(level!=null&&!"null".equals(level)&&!"".equals(level)) {
            optionalCondition +=" and btd.level in ("+dataProviderService.toSingleQuotes(level)+")"


        }
        if(syllabus!=null&&!"null".equals(syllabus)&&!"".equals(syllabus)) {
            optionalCondition +=" and btd.syllabus in ("+dataProviderService.toSingleQuotes(syllabus)+")"


        }
        if(grade!=null&&!"null".equals(grade)&&!"".equals(grade)) {
            optionalCondition +=" and btd.grade in ("+dataProviderService.toSingleQuotes(grade)+")"

        }
        if(subject!=null&&!"null".equals(subject)&&!"".equals(subject)) {
            optionalCondition +=" and btd.subject in ("+dataProviderService.toSingleQuotes(subject)+")"

        }
        if("true".equals(mcqBook)){
            optionalCondition +=" and bk.has_quiz='true'"
        }
        if(freeBooks){
            optionalCondition +=" and (bk.price=null or bk.price=0 or bk.price=0.0)"
        }
        publisherCondition = optionalCondition
        if(publisherId!=null&&!"null".equals(publisherId)&&!"".equals(publisherId)) {
            optionalCondition +=" and bk.publisher_id="+publisherId


        };
       //collect all the books received in params
        String sql = "select bk.id,bk.title,bpd.list_price listprice,bpd.sell_price price,p.name,bk.authors,bk.isbn " +
                " from books_mst bk,books_tag_dtl btd, publishers p,book_price_dtl bpd  where " +
                " bk.site_id in(" + siteIdList + ") and bk.status in ('free','published')\n" +
                " and p.id=bk.publisher_id\n" +
                " and btd.book_id=bk.id\n" +
                " and bpd.book_id=bk.id \n" +
                " and bpd.book_type='eBook'\n "+ optionalCondition +
                " GROUP BY bk.id , bk.title ,bpd.list_price , bpd.sell_price , p.name , bk.authors order by date_published  desc"
        println("Sql="+sql)
        results = sql1.rows(sql)
        println("number of rows "+results.size())
        List booksData = results.collect { book ->
            String link=serverUrl+"/"+book.title.replace(' ','-').toLowerCase().replace('\'','')+"/ebook-details?siteName="+siteMst.siteName+"&bookId="+book.id+"&publisher="+book.name+"&preview=true"
            return [bookId: book.id, title: book.title,
                    listPrice:book.listprice,offerPrice: book.price,
                    publisher:book.name,author:book.authors,link:link,isbn:book.isbn]
        }
        //create and send excel file for the books data
        List headers = ["BookId","Title", "Publisher", "Author", "List Price", "Selling Price","isbn", "Link"]
        List withProperties=["bookId","title", "publisher","author","listPrice","offerPrice","isbn","link"]
        def fileName = catalogueName+"_Catalogue.xlsx";
        new WebXlsxExporter().with {
            setResponseHeaders(response, fileName)
            fillHeader(headers)
            add(booksData, withProperties)
            save(response.outputStream)
        }
    }

    @Transactional
    def informationPage(){
        String url = request.getRequestURL()
        if(url.indexOf("prepjoy.com")>-1||"27".equals(""+session["siteId"])) {
            setSiteInformation("prepjoy")
        }
        else setUserSession()
        boolean showDetails = true
        boolean showTitle = true
        boolean showAnswerOption = false
        if("jokes".equals(params.pageName)||"puzzles".equals(params.pageName)){
            showDetails = false
            showTitle = false
            if("puzzles".equals(params.pageName))
                showAnswerOption = true
        }
        [commonTemplate:"true",showDetails:showDetails,showTitle:showTitle,showAnswerOption:showAnswerOption,
         title:params.resType+" - Wonderslate"]
    }

    @Transactional
    def informationPageDtl(){
        String url = request.getRequestURL()
        if(url.indexOf("prepjoy.com")>-1||"27".equals(""+session["siteId"])) {
            setSiteInformation("prepjoy")
        }
        else setUserSession()
        InformationMst informationMst = InformationMst.findById(new Long(params.id))
        [commonTemplate:"true",informationMst:informationMst,
         title:informationMst.title+" - Wonderslate"]

    }

    def shoppingCart(){
        setUserSession()
        [title:'Cart - Wonderslate',commonTemplate:"true"]
    }
    @Transactional
 def faq(){
     [title:'FAQ',commonTemplate:"true"]

 }
    @Transactional
    def aboutus(){
        [title:'About Us',commonTemplate:"true"]
    }

    def eduWonder(){
        [ "title":"EduWonder", commonTemplate:"true"]
    }

    @Transactional
    def eBooksStoreIntegration(){
        [title:'eBooks Store Integration - Wonderslate',commonTemplate:"true"]
    }

    @Transactional
    def appLink(){
        [title:'Download Apps - Wonderslate',commonTemplate:"true"]
    }

    @Transactional @Secured(['ROLE_WS_SALES_TEAM'])
    def startOrderProcess(){
        Integer siteId = session["siteId"]

        //first check the user exists
        String username = ""+siteId+"_"+params.userMobile
        User user = User.findByUsername(username)
        if(user==null){
            //create the user
            String name = params.studentName
            String password = params.userMobile
            String mobile = params.userMobile
            String email = params.userEmail
            WinGenerator winGenerator = new WinGenerator()
            winGenerator.save(failOnError: true)
            user  = new User(username: username,name: name,password: password,mobile:mobile,email:email,win: winGenerator.id,siteId: siteId)
            user.save(failOnError: true, flush: true)
            //add appropriate roles
            Role role = Role.findByAuthority("ROLE_USER")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_ADD")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_UPLOAD")
            UserRole.create(user, role, true)
        }

        // if the institute present then add the user to institute if he doesn't exist
        if(params.instituteId!=null&&!"".equals(params.instituteId)&&!"null".equals(params.instituteId)){
            Integer defaultBatchId = (CourseBatchesDtl.findByConductedByAndName(new Integer(params.instituteId),"Default")).id
            instituteService.addUserToBatch(username,defaultBatchId,null,null,null,siteId)
        }

        // first let's add the order to direct sales table
        String orderStatus = "open"
        if("cash".equals(params.paymentMethod)) orderStatus = "closed"
        DirectSaleOrders directSaleOrders = new DirectSaleOrders(username: username,siteId:siteId,createdBy: springSecurityService.currentUser.username,
                instituteId: (params.instituteId!=null && !"".equals(params.instituteId) ? new Integer(params.instituteId):null),status:orderStatus,paymentMode: params.paymentMethod,bookId:new Integer(params.bookId))
        directSaleOrders.save(failOnError: true, flush: true)

        String status
        //now add the  book if it is cash.
        if("cash".equals(params.paymentMethod)){
            purchaseService.addBookToUser(params.bookId,siteId,request,session,"CASH",null,user,"true")
            status="Book added."
        }else{
            //generate the link
            String urlKey = Base64.getEncoder().encodeToString((""+directSaleOrders.id).getBytes())
            try {
                def message = "${session["userdetails"].name} from Wonderslate, has shared the link. Please click on the link https://www.wonderslate.com/trans/${urlKey} to complete the purchase."
                utilService.sendSMSForInstituteUser(siteId, message, user.mobile)
                status = message
            }catch (Exception e) {
                println "Purchase user  sms failed " + e.toString()
            }
        }

        def json = [status:status]
        render json as JSON


    }

    @Transactional
    def userOrderProcess(){
        byte[] decodedBytes = Base64.getDecoder().decode(params.transId);
        String decodedString = new String(decodedBytes);
        InstituteMst instituteMst
        DirectSaleOrders directSaleOrders = DirectSaleOrders.findById(new Integer(decodedString))

        if(directSaleOrders!=null&&!"purchased".equals(directSaleOrders.status)){
            directSaleOrders.status = "Link clicked"
            directSaleOrders.dateUpdated = new Date()
            directSaleOrders.save(failOnError: true, flush: true)
            session.setAttribute("userdetails",null)
            User user = User.findByUsername(directSaleOrders.username)
            springSecurityService.reauthenticate(user.username, user.password)
            def authentication = SecurityContextHolder.context.authentication
            rememberMeServices.loginSuccess(request, response, authentication)
            userManagementService.registerUserLogin(user.username,session.getId())
            setUserSession()
            if(directSaleOrders.instituteId!=null) {
                 instituteMst = InstituteMst.findById(new Long(directSaleOrders.instituteId))
            }
            BooksMst booksMst = dataProviderService.getBooksMst(directSaleOrders.bookId)
            session["directSalesId"] = directSaleOrders.id
            [booksMst:booksMst,instituteName: instituteMst?instituteMst.name:"",instituteId:instituteMst?instituteMst.id:"",commonTemplate: "true"]
        }else if(directSaleOrders!=null&&"purchased".equals(directSaleOrders.status)){
            //purchase has already happened
            redirect(uri: "")
        }else{
            redirect(uri: "")
        }

    }


    @Transactional
    def directSales(){
        Integer siteId = session["siteId"]
        List institutes = InstituteMst.findAllBySiteIdAndEduWonder(siteId,"true",[sort: "name"])
        List subscriptionPackages  = BooksMst.findAllBySiteIdAndSubscriptionPackage(siteId,"Yes",[sort: "title"])
        [institutes:institutes,subscriptionPackages:subscriptionPackages,commonTemplate: "true"]
    }

    @Transactional
    def packageBooks(){
        Integer siteId = session["siteId"]
        List subscriptionPackages  = BooksMst.findAllBySiteIdAndSubscriptionPackage(siteId,"Yes",[sort: "title"])
        [subscriptionPackages:subscriptionPackages,commonTemplate: "true"]
    }

    @Transactional
    def leaderboard(){
        ['title': 'Leaderboard - Wondkerslate', commonTemplate:"true"]
    }

    def contactus(){
        ['title': "Contact Us",commonTemplate: "true"]
    }

    def publishersProduct(){
        ['title': "Publishers Product", commonTemplate:"true"]
    }
    def teachersProduct(){
        boolean freeBooks = false
        String keyName = "";
        def publishers = redisService.("publishers_"+session["siteId"]+"_"+ keyName+"_"+freeBooks)
        ['title': "Free Specimen Books For Teachers ", 'description': "Fill the form and request a FREE specimen teacher book & get access to the best Academic books- CBSE, ICSE, NCERT, NEET  & other Competitive books in the industry. ", commonTemplate:"true",publishers:publishers]
    }
    def schoolProducts(){
        ['title': "Schools Product", commonTemplate:"true"]
    }

    def description(){
        ['title': "Sell Books on Wonderslate",commonTemplate: "true"]
    }
    @Transactional
    def landingPage(){
        setUserSession()
        if (springSecurityService.currentUser!=null){
            String username =  springSecurityService.currentUser.username
            if( redisService.("noOfBooksInLibrary_"+username)==null) userManagementService.numberOfBooksInLibrary(username)
            [commonTemplate: "true",noOfBooksInLibrary:redisService.("noOfBooksInLibrary_"+username)]
        }else{
            [commonTemplate: "true"]
        }
    }

    @Transactional
    def myHome(){
        String url = (request.getRequestURL()).toString()
        if (url.contains("wonderslate") || url.contains("localhost")) {

            setUserSession();



            //to make the user go to dashboard when he logs in using remember me services

            if(springSecurityService.currentUser!=null&&!"true".equals(session.getAttribute("visitedDashboard"))) {
                userManagementService.registerUserLogin(springSecurityService.currentUser.username,session.getId())
                session["visitedDashboard"]="true"
            }
            boolean showSearchAtTop=false
            if (session.getAttribute("userdetails") != null) {
                String username =  springSecurityService.currentUser.username

                double totalTime = 0 , practiceTime = 0
                if(redisService.("userDefaultTimeLog_"+username)!=null&&!"null".equals(redisService.("userDefaultTimeLog_"+username))) totalTime = Double.parseDouble(redisService.("userDefaultTimeLog_"+username))
                if(redisService.("userQuizDefaultTimeLog_"+username)!=null&&!"null".equals(redisService.("userQuizDefaultTimeLog_"+username))) practiceTime = Double.parseDouble(redisService.("userQuizDefaultTimeLog_"+username))
                if( redisService.("latestReadBooks_"+username)==null) userManagementService.getLatestReadBooks(username)
                if( redisService.("noOfBooksInLibrary_"+username)==null) userManagementService.numberOfBooksInLibrary(username)
                if(redisService.("latestQuizInfo_"+username)==null) analyticsService.getUsersLastQuiz(username)
                if(redisService.("testAttemptInfo_"+username+"_7")==null)  progressService.getTestAttemptInfo(username,"7")
                if(redisService.("subjectwiseInfoTotal_"+username+"_7")==null) progressService.getSubjectwiseInfoTotal(username,"7")

                if(session["userdetails"]==null||"0".equals(redisService.("noOfBooksInLibrary_"+username))){
                    showSearchAtTop=true
                }
                [commonTemplate: "true", currentBadge: session["currentBadge"], totalTime:totalTime,
                 practiceTime:practiceTime,testAttemptInfo:redisService.("testAttemptInfo_"+username+"_7"), subjectwiseInfo:redisService.("subjectwiseInfoTotal_"+username+"_7"),
                 latestReadBooks:redisService.("latestReadBooks_"+username),noOfBooksInLibrary:redisService.("noOfBooksInLibrary_"+username),
                 lastQuizInfo:redisService.("latestQuizInfo_"+username),showSearchAtTop:showSearchAtTop]
            }else{

                [commonTemplate: "true", currentBadge: session["currentBadge"],showSearchAtTop:true]
            }
        }else {
            if(url.contains(".com")) {
                String baseUrl = url.split('.com/')[0]
                redirect([uri: baseUrl + '.com'])
            }
        }
    }
    @Transactional
    def setSiteInformation(String siteName){
        siteManagerService.setPrepjoySiteInformation(params.siteName,session,response,servletContext)
    }

    def wrongPage(){

    }

    def pdftoepub(){
        ['title': "PDF to EPUB Conversion Service",commonTemplate: "true", description: "Convert PDF to EPUB- We provide best in class PDF to EPUB conversion. We work with more than 200+ satisfied publishers. We convert both English and Hindi PDFs. "]
    }

    def blogs(){

    }

}
