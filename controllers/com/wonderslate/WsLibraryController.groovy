package com.wonderslate

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.WsLibrary.WsLibraryCacheService
import com.wonderslate.WsLibrary.WsLibraryService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.institute.BooksBatchDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.log.BooksViewDtl
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.BooksQueueDtl
import com.wonderslate.publish.BooksPermissionCopy
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.publish.BooksPermission
import com.wonderslate.shop.WsshopService
import com.wonderslate.toDo.ToDoService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.grails.web.util.WebUtils
import javax.servlet.http.Cookie
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import grails.converters.JSON
import groovy.json.JsonSlurper
import groovy.json.JsonBuilder


@Transactional
class WsLibraryController {
    UserManagementService userManagementService
    SpringSecurityService springSecurityService
    def redisService
    DataProviderService dataProviderService
    UtilService utilService
    WsLibraryService wsLibraryService
    WsLibraryCacheService wsLibraryCacheService
    ToDoService toDoService
    WsshopService wsshopService


   @Transactional
    def myLibrary() {
        Integer siteId = getSiteId(request)
        boolean wsSite=false, libWonder=false, arihant=false ,instituteLibrary=false,oswal=false,oswaal=false,showLibrary=false
        if(siteId.intValue()==1) wsSite = true
        else if(siteId.intValue()==3) arihant=true
        else if(siteId.intValue()==25) libWonder=true
        else if(siteId.intValue()==22) oswal=true
        else if(siteId.intValue()==38) oswaal=true
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }

        if(!"true".equals(session["NumberExceeded"])||((wsSite) && springSecurityService.currentUser!=null && userManagementService.isValidSession(springSecurityService.currentUser.username,session.getId()))) {

            List usersInstituteDtl=getInstitutesForUser();

            if (session.getAttribute("publisherLogoId")== null && libWonder&&usersInstituteDtl.size()>0) {
                for(int i=0;i<usersInstituteDtl.size();i++){
                    if(usersInstituteDtl[i].publisherId!=null){
                        session["publisherLogoId"]=""+usersInstituteDtl[i].publisherId;
                        break
                    }
                }
            }
            def showMyshelf=false,nameGreeting=false,showAccessCode=false
            if(libWonder || arihant ||oswal || oswaal) {
                if(springSecurityService.currentUser!=null) {
                    nameGreeting = true
                    showAccessCode = true
                }
            }


            if(libWonder){
                showLibrary= utilService.hasLibraryAccess(request,25)
            }

            showMyshelf = true

             if(params.instituteId!=null){
              for(int i=0;i<usersInstituteDtl.size();i++){
                  if(!(params.instituteId.equals(""+usersInstituteDtl[i].id))) usersInstituteDtl.remove(i--)
              }
                 //found the correct institute
                 if(usersInstituteDtl.size()==1){
                     showMyshelf = false
                 }
             }
             //logic to check if the full library should be allowed or not
            //check librarian
            boolean isLibrarian = false
            if (session.getAttribute("userdetails") == null) {
                if(springSecurityService.currentUser!=null) {
                    session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
                }

            }
            if(session["userdetails"]!=null) {
                User user = session["userdetails"]
                if (user.authorities.any {
                    it.authority == "ROLE_LIBRARY_ADMIN"
                }) {
                    isLibrarian = true
                }
            }
            if(usersInstituteDtl!=null){
                for(int i=0;i<usersInstituteDtl.size();i++){
                    if("false".equals(usersInstituteDtl[i].fullLibraryView)&&!isLibrarian&&"Default".equals(usersInstituteDtl[i].batchName)) usersInstituteDtl.remove(i--)
                }
            }
            // active categories
            if (session["activeCategories_1"] == null) {
                wsshopService.activeCategories(new Integer(1))
                session["activeCategories_1"] = redisService.("activeCategories_1")
            }
            def siteMst = dataProviderService.getSiteMst(new Long(session['siteId']!=null?""+session['siteId']:"1"))
            if(siteMst.instituteLibrary=='true')instituteLibrary=true

            if(redisService.("usersCartBooksDetails_"+springSecurityService.currentUser.username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(springSecurityService.currentUser.username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+springSecurityService.currentUser.username))

                [siteMst: siteMst,institutes:usersInstituteDtl?usersInstituteDtl:null, showMyShelf:showMyshelf,nameGreeting:nameGreeting, commonTemplate:"true",showAccessCode:showAccessCode,instituteLibrary:instituteLibrary,showLibrary:showLibrary]

        }
        else{
            Cookie cookie = new Cookie("SimulError", "Fail")
            cookie.path = "/"
            WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
            redirect(uri: "/logoff")
        }
    }



    def  Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }
    private Date convertDate(Date dateFrom, String fromTimeZone, String toTimeZone) throws ParseException {
        String pattern = "yyyy/MM/dd HH:mm:ss"
        SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern)
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone))

        SimpleDateFormat sdfTo = new SimpleDateFormat (pattern)
        sdfTo.setTimeZone(TimeZone.getTimeZone(toTimeZone))
        Date dateTo = sdfFrom.parse(sdfTo.format(dateFrom))
        return dateTo
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getInstitutesForUser(){
        List instituteDetails
        String ipAddress
        if("yes".equals(params.app) && !"".equals(params.ipAddress)){
            ipAddress=params.ipAddress
        }else{
            ipAddress = utilService.getIPAddressOfClient(request)
        }
       instituteDetails = userManagementService.getInstitutesForUser(getSiteId(request),ipAddress)
        if("yes".equals(params.app)){
            if(redisService.("userPendingTodoCount_"+springSecurityService.currentUser.username)==null) toDoService.pendingToDoCount()
            def json = [ 'institutes' : instituteDetails ? instituteDetails : "Nothing present",
                         "noOfPendingTodo":redisService.("userPendingTodoCount_"+springSecurityService.currentUser.username)]
            render json as JSON
        }else{
            return  instituteDetails;
        }
    }


    @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def bookUsageDetailsData(){
        if(params.bookId!=null && params.bookId!="" && params.batchId != null && params.batchId != ""){
            Long bookId = new Long(params.bookId)
            Long batchId = new Long(params.batchId)

            String sql = " SELECT bp.id, bp.book_id, bp.username, bp.expiry_date, bp.date_created FROM wsuser.books_permission bp " +
                    " where bp.book_id="+bookId+" and bp.po_type='ADDEDFROMINSTITUTE' and bp.batch_id="+batchId
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            def data = results.collect{
                return [permissionId: it.id, username: it.username, expiryDate: it.expiry_date, bookId: it.book_id, dateCreated: it.date_created]
            }
            def json = [
                    status: data.size()>0?'OK':'No records found',
                    users: data,
                    bookId: params.bookId
            ]
            render json as JSON
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def deleteBookFromUser(){
        if(params.permissionId!=null && params.permissionId!=""){
            Long permissionId = new Long(params.permissionId)
            BooksPermission.findById(permissionId).delete()
            BooksPermissionCopy booksPermissionCopy =  BooksPermissionCopy.findByBpId(new Long(permissionId))
            booksPermissionCopy.status='deleted'
            booksPermissionCopy.returnDate = new Date()
            booksPermissionCopy.save(flush: true, failOnError: true)
            redisService.("lastReadBooksIns_"+booksPermissionCopy.batchId+"_"+ booksPermissionCopy.username)=null
            if(User.findByUsername(booksPermissionCopy.username).email!=null && !User.findByUsername(booksPermissionCopy.username).email.equals("")) {
                if(userManagementService.validateEmail(User.findByUsername(booksPermissionCopy.username).email,getSiteId(request))) {
                    try {
                        userManagementService.sendInstituteUserEmail(
                                User.findByUsername(booksPermissionCopy.username).email,
                                User.findByUsername(booksPermissionCopy.username).name,
                                "This book '"+ BooksMst.findById(booksPermissionCopy.bookId).title+"' is removed from your library.",
                                'false',
                                SiteMst.findById(getSiteId(request)).siteName
                        )
                    } catch (Exception e) {
                        println "sending library email failed " + e.toString()
                    }
                }
            }
            def json = [
                    status: 'OK',
            ]
            render json as JSON
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN']) @Transactional
    def addBookToUserByAdmin(){
        Integer checkoutDays;
        Integer bookvalidity;
        Calendar c = Calendar.getInstance()
        if(params.bookId!=null && params.bookId!=""&&params.username!=null && params.username!=""&&params.batchId!=null && params.batchId!=""){
            String username = ""+getSiteId(request)+"_"+params.username
            String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)
            BatchUserDtl dtl = BatchUserDtl.findByBatchIdAndUsername(new Long(defaultBatchId), username)
            if(dtl!=null){
                def dtl1=BooksPermission.findByBatchIdAndBookIdAndPoTypeAndUsername(new Long(defaultBatchId), new Long(params.bookId),'ADDEDFROMINSTITUTE', username)
                if(dtl1==null){
                    if(BooksBatchDtl.findByBatchIdAndBookId(new Long(defaultBatchId),new Long(params.bookId)).numberOfLicenses==null || BooksBatchDtl.findByBatchIdAndBookId(new Long(defaultBatchId),new Long(params.bookId)).numberOfLicenses!='' && BooksBatchDtl.findByBatchIdAndBookId(new Long(defaultBatchId),new Long(params.bookId)).numberOfLicenses>BooksPermission.findAllByBatchIdAndBookIdAndPoType(new Long(defaultBatchId),new Long(params.bookId),"ADDEDFROMINSTITUTE").size()) {
                        checkoutDays =
                                (InstituteMst.findById(CourseBatchesDtl.findById(new Long(defaultBatchId)).conductedBy).checkOutDays != ""
                                        && InstituteMst.findById(CourseBatchesDtl.findById(new Long(defaultBatchId)).conductedBy).checkOutDays != null)
                                        ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(new Long(defaultBatchId)).conductedBy).checkOutDays)
                                        : null
                        if(checkoutDays!=null && checkoutDays!="") {
                            c.add(Calendar.DATE, checkoutDays)
                        }else{
                            c.add(Calendar.DATE, 14)
                        }
                        BooksPermission booksPermission = new BooksPermission(
                                bookId: new Long(params.bookId),
                                username: username,
                                poType: 'ADDEDFROMINSTITUTE',
                                batchId: new Long(defaultBatchId),
                                addedBy: springSecurityService.currentUser.username,
                                expiryDate: c.getTime()
                        )
                        booksPermission.save(flush: true, failOnError: true)
                        BooksPermissionCopy booksPermissionCopy = new BooksPermissionCopy(
                                bookId: new Long(params.bookId),
                                username: username,
                                poType: 'ADDEDFROMINSTITUTE',
                                batchId: new Long(defaultBatchId),
                                addedBy: springSecurityService.currentUser.username,
                                bpId: booksPermission.id,
                                expiryDate: c.getTime()
                        )
                        booksPermissionCopy.save(flush: true, failOnError: true)
                        BooksViewDtl booksViewDtl = new BooksViewDtl(bookId:new Long(params.bookId), viewSource: "web", viewType:"library",username:username,
                                siteId:getSiteId(request),instituteId:InstituteMst.findById(CourseBatchesDtl.findById(new Long(defaultBatchId)).conductedBy).id)
                        booksViewDtl.save(flush:true, failOnError: true)
                        BooksQueueDtl.executeUpdate("delete  BooksQueueDtl where bookId IN (" + params.bookId + ") and batchId='" + defaultBatchId + "' and username= '" + username + "'" )
                        redisService.("lastReadBooksIns_"+defaultBatchId+"_"+ username)=null
                        if (User.findByUsername(username).email != null && !User.findByUsername(username).email.equals("")) {
                            if (userManagementService.validateEmail(User.findByUsername(username).email, getSiteId(request))) {
                                try {
                                    userManagementService.sendInstituteUserEmail(
                                            User.findByUsername(username).email,
                                            User.findByUsername(username).name,
                                            "This book '" + BooksMst.findById(new Long(params.bookId)).title + "' is added to your library.",
                                            new Long(params.bookId),
                                            SiteMst.findById(getSiteId(request)).siteName
                                    )
                                } catch (Exception e) {
                                    println "sending library email failed " + e.toString()
                                }
                            }
                        }

                        def json = [
                                status: 'OK',
                        ]
                        render json as JSON
                    }else{
                        def json = [
                                status: 'Failed',
                                message: 'No license left.'
                        ]
                        render json as JSON
                    }
                }else{
                    def json = [
                            status: 'Failed',
                            message: 'Book already exists in the user library.'
                    ]
                    render json as JSON
                }

            }else{
                def json = [
                        status: 'Failed',
                        message: 'User does not belong to this institute. Please add the user to institute.'
                ]
                render json as JSON
            }
        }
    }


    @Transactional
    def addBookToUserByUser(){
        def expiry=true;
        Integer checkoutDays;
        Integer bookvalidity;
        Calendar c = Calendar.getInstance()
        String batchId = params.batchId
        if(params.bookId!=null && params.bookId!="" && batchId!=null && batchId!="" && springSecurityService.currentUser!=null){
            batchId = wsLibraryService.getDefaultBatchId(params.batchId)
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            String username = springSecurityService.currentUser.username
                def dtl1=BooksPermission.findByBatchIdAndBookIdAndPoTypeAndUsername(new Long(batchId), new Long(params.bookId),'ADDEDFROMINSTITUTE', username)
                if(dtl1==null){
                    if(BooksBatchDtl.findByBatchIdAndBookId(new Long(batchId),new Long(params.bookId)).numberOfLicenses==null|| BooksBatchDtl.findByBatchIdAndBookId(new Long(batchId),new Long(params.bookId)).numberOfLicenses>BooksPermission.findAllByBatchIdAndBookIdAndPoType(new Long(batchId),new Long(params.bookId),"ADDEDFROMINSTITUTE").size()) {
//                        if(BooksBatchDtl.findByBatchIdAndBookId(new Long(batchId),new Long(params.bookId)).numberOfLicenses!=null) {
                             checkoutDays =
                                    (InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).checkOutDays != ""
                                            && InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).checkOutDays != null
                                            && !"null".equals(InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).checkOutDays))
                                            ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).checkOutDays)
                                            : null
                        if(checkoutDays!=null && checkoutDays!="") {
                            c.add(Calendar.DATE, checkoutDays)
                        }else{
                            c.add(Calendar.DATE, 14)
                        }
//                        }else{
//                            expiry=false
//                        }
                        BooksPermission booksPermission = new BooksPermission(
                                bookId: new Long(params.bookId),
                                username: username,
                                poType: 'ADDEDFROMINSTITUTE',
                                batchId: new Long(batchId),
                                addedBy: springSecurityService.currentUser.username,
                                expiryDate: expiry==true?c.getTime():null,

                        )
                        booksPermission.save(flush: true, failOnError: true)
                        BooksPermissionCopy booksPermissionCopy = new BooksPermissionCopy(
                                bookId: new Long(params.bookId),
                                username: username,
                                poType: 'ADDEDFROMINSTITUTE',
                                batchId: new Long(batchId),
                                addedBy: springSecurityService.currentUser.username,
                                bpId: booksPermission.id,
                                expiryDate: expiry==true?c.getTime():null
                        )
                        booksPermissionCopy.save(flush: true, failOnError: true)
                        if(booksMst.price != null && booksMst.price.doubleValue() == 0 && "published".equals(booksMst.status) && "true".equals(params.shelfAdd)){
                            //adding book to user's library
                            BooksPermission shelfbooksPermission = new BooksPermission()
                            shelfbooksPermission.bookId = booksMst.id
                            shelfbooksPermission.username = springSecurityService.currentUser.username
                            shelfbooksPermission.poType = 'ADDEDFORFREE'
                            shelfbooksPermission.save(failOnError: true, flush: true)
                            redisService.("userShelfBooks_"+springSecurityService.currentUser.username)=null
                        }
                        BooksQueueDtl booksQueueDtl=BooksQueueDtl.findByBookIdAndBatchIdAndUsername(new Long(params.bookId),new Long(batchId),springSecurityService.currentUser.username)
                        if(booksQueueDtl!=null) booksQueueDtl.delete()
                        redisService.("lastReadBooksIns_"+batchId+"_"+ springSecurityService.currentUser.username)=null
                        def json = [
                                instituteId:InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).id,
                                batchId :batchId,
                                status: 'Ok',
                                message: 'added',
                                bookType:booksMst.bookType
                        ]
                        render json as JSON
                    }else{
                        def json = [
                                status: 'Not added',
                                message: 'No license left'
                        ]
                        render json as JSON
                    }
                }else{
                    def json = [
                            instituteId:InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).id,
                            batchId :batchId,
                            status: 'Failed',
                            message: 'Book already exists in the user library.',
                            bookType:booksMst.bookType
                    ]
                    render json as JSON
                }
        }else{
            def json = [
                    instituteId:InstituteMst.findById(CourseBatchesDtl.findById(new Long(batchId)).conductedBy).id,
                    batchId :batchId,
                    status: 'Failed',
                    message: 'Book already exists in the user library.'
            ]
            render json as JSON
        }
    }



    @Transactional
    def myLibrarySearchSuggestion(){
        List batchIdList = getInstitutesForUser();
        String query = params.query
        def matchingBooks
        long bId
        if(batchIdList == null || batchIdList.size() <= 1) {
             bId = batchIdList.size() == 1? batchIdList.get(0).batchId:0
            List booklist2 = wsLibraryService.getUsersInsBooksByBatchId(bId+"")
            if(booklist2 == null) {
                wsLibraryService.instituteBooksforUser(bId+"")
            }

            matchingBooks = wsLibraryService.getMyLibrarySearchSuggestion(query,bId)
        }
        else matchingBooks = wsLibraryService.getMyLibrarySearchSuggestionForMultipleInstituteIds(query,batchIdList)
        if(matchingBooks == null) matchingBooks = new ArrayList<>()
        def json = ['searchList':matchingBooks,'status':"OK"]
        render json as JSON
    }


    @Transactional
    def getMyLibrarySearchResults(){
        Boolean bookAcess = false,bookscount = false
        List batchIdList = getInstitutesForUser();
        String query = params.searchString
        def matchingBooks
        if(batchIdList == null || batchIdList.size() <= 1) {
            long bId = batchIdList.size() == 1? batchIdList.get(0).batchId:0
            matchingBooks = wsLibraryService.getMyLibrarySearchResults(query,bId)
        }
        else matchingBooks = wsLibraryService.getMyLibrarySearchSuggestionForMultipleInstituteIdsbooks(query,batchIdList)
        if(matchingBooks == null) matchingBooks = new ArrayList<>()
        if(springSecurityService.currentUser!=null) {
            if (redisService.("lastReadBooks_" + springSecurityService.currentUser.username) == null) {
                dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
            }
        }
        if(matchingBooks!=null) {
            for (int i = 0; i < matchingBooks.size(); i++) {
                def bookTemp = matchingBooks.get(i)
                if (bookTemp.batchId != null && bookTemp.batchId != "") {
                    List booksPermission = BooksPermission.findAllByBatchIdAndBookIdAndPoType(new Long(bookTemp.batchId), new Long(bookTemp.id), "ADDEDFROMINSTITUTE");
                    if(springSecurityService.currentUser!=null) {
                    BooksPermission booksPermission1 = BooksPermission.findByUsernameAndBatchIdAndBookId(springSecurityService.currentUser.username, new Long(bookTemp.batchId), new Long(bookTemp.id));
                        if (booksPermission1 != null) {
                            bookAcess = true
                        } else {
                            bookAcess = false
                        }
                    }
                    def noOflic = bookTemp.noOfLic != null && bookTemp.noOfLic != "" ? Integer.parseInt(bookTemp.noOfLic + "") : null
                    if (noOflic == null || booksPermission.size() < noOflic) bookscount = true
                    bookTemp.bookAcess = bookAcess
                    bookTemp.bookscount = bookscount
                    matchingBooks.set(i, bookTemp)
                }
            }
        }
        def json = [ books:matchingBooks,'lastReadBooks':springSecurityService.currentUser!=null?redisService.("lastReadBooks_"+springSecurityService.currentUser.username):null,'status' : matchingBooks ? "OK" : "Nothing present"]
        render json as JSON
    }



    @Secured(['ROLE_USER']) @Transactional
    def addInstituteBookToUserQueue(){
        def status="error"
        String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)
        BooksQueueDtl booksQueueDtl=BooksQueueDtl.findByUsernameAndBookIdAndBatchId(springSecurityService.currentUser.username, new Long(params.bookId),new Long(defaultBatchId))
        if (booksQueueDtl==null){
            BooksQueueDtl booksQueueDtl1 = new BooksQueueDtl(
                    bookId: new Long(params.bookId),
                    username: springSecurityService.currentUser.username,
                    batchId: new Long(defaultBatchId),
                    addedBy:springSecurityService.currentUser.username,
                    action:"queue",
                    estimatedDate:null)
            booksQueueDtl1.save(flush: true, failOnError: true)
            status="OK"
        }
        def json = [
                status: status,
        ]
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def checkBookQueueValidity(){
        def expiry=true;
        List BooksQueueDtl1
        Integer checkoutDays;
        Integer bookvalidity;
        Calendar c = Calendar.getInstance()
        String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)
        if(BooksBatchDtl.findByBatchIdAndBookId(new Long(defaultBatchId),new Long(params.bookId)).numberOfLicenses==null|| BooksBatchDtl.findByBatchIdAndBookId(new Long(defaultBatchId),new Long(params.bookId)).numberOfLicenses>BooksPermission.findAllByBatchIdAndBookIdAndPoType(new Long(defaultBatchId),new Long(params.bookId),"ADDEDFROMINSTITUTE").size()) {
            //                        if(BooksBatchDtl.findByBatchIdAndBookId(new Long(params.batchId),new Long(params.bookId)).numberOfLicenses!=null) {
            checkoutDays =
                    (InstituteMst.findById(CourseBatchesDtl.findById(new Long(defaultBatchId)).conductedBy).checkOutDays != ""
                            && InstituteMst.findById(CourseBatchesDtl.findById(new Long(defaultBatchId)).conductedBy).checkOutDays != null)
                            ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(new Long(defaultBatchId)).conductedBy).checkOutDays)
                            : null
            if(checkoutDays!=null && checkoutDays!="") {
                c.add(Calendar.DATE, checkoutDays)
            }else{
                c.add(Calendar.DATE, 14)
            }
//                        }else{
//                            expiry=false
//                        }
            BooksPermission booksPermission = new BooksPermission(
                    bookId: new Long(params.bookId),
                    username: springSecurityService.currentUser.username,
                    poType: 'ADDEDFROMINSTITUTE',
                    batchId: new Long(defaultBatchId),
                    addedBy: springSecurityService.currentUser.username,
                    expiryDate: expiry==true?c.getTime():null
            )
            booksPermission.save(flush: true, failOnError: true)
            BooksPermissionCopy booksPermissionCopy = new BooksPermissionCopy(
                    bookId: new Long(params.bookId),
                    username: springSecurityService.currentUser.username,
                    poType: 'ADDEDFROMINSTITUTE',
                    batchId: new Long(defaultBatchId),
                    addedBy: springSecurityService.currentUser.username,
                    bpId: booksPermission.id,
                    expiryDate: expiry==true?c.getTime():null
            )
            booksPermissionCopy.save(flush: true, failOnError: true)
            BooksQueueDtl booksQueueDtl=BooksQueueDtl.findByBookIdAndBatchIdAndUsername(new Long(params.bookId),new Long(defaultBatchId),springSecurityService.currentUser.username)
            if(booksQueueDtl!=null) booksQueueDtl.delete()
            redisService.("lastReadBooksIns_"+defaultBatchId+"_"+ springSecurityService.currentUser.username)=null
            def json = [
                    status: 'Ok',
                    message: 'added'
            ]
            render json as JSON
        }
        else {
            BooksQueueDtl booksQueueDtl = BooksQueueDtl.findByBookIdAndBatchIdAndUsernameAndAction(new Long(params.bookId), new Long(defaultBatchId), springSecurityService.currentUser.username, "queue")
            if (booksQueueDtl == null) {
                BooksQueueDtl1 = BooksQueueDtl.findAllByBatchIdAndBookIdAndAction(new Long(defaultBatchId), new Long(params.bookId), "queue")
                BooksQueueDtl1.size()
            }
            def json = [
                    message:"addToQueue",  queuePosition: BooksQueueDtl1 ? BooksQueueDtl1.size() + 1 : 1, booksQueueDtl: booksQueueDtl ? "Already Exist" : ""
            ]
            render json as JSON
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def getBooksInQueueForUser(){
        String username = springSecurityService.currentUser.username
        String sql = "SELECT bqd.book_id id,bm.title,bm.cover_image,bqd.batch_id,bm.publisher_id,bm.book_type" +
                " FROM wsuser.books_queue_dtl bqd, wsuser.books_mst bm " +
                " WHERE bm.id=bqd.book_id AND bqd.action='queue' AND  bqd.username='"+username+"'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def data = results.collect{
            return [id: it.id, title: it.title, coverImage: it.cover_image,batchId:it.batch_id, publisher: (it.publisher_id!=null && it.publisher_id!= "")? dataProviderService.getPublisher(new Long(it.publisher_id)).name:'',bookType:it.book_type]
        }
        def json = [
                QueueBooks: data
        ]
        render json as JSON


    }

    @Secured(['ROLE_USER']) @Transactional
    def returnBookFromUser(){
        Integer siteId = getSiteId(request)
        Integer checkoutDays;
        Integer bookvalidity;
        if(params.batchId!=null && params.batchId!="" && params.bookId!=null && params.bookId!="" && springSecurityService.currentUser!=null){
            Long batchId = new Long(wsLibraryService.getDefaultBatchId(params.batchId))
            Long bookId = new Long(params.bookId)
            def viewSource="web"
            BooksPermission bp = BooksPermission.findByBatchIdAndBookIdAndUsername(batchId, bookId, springSecurityService.currentUser.username)
            BooksPermissionCopy bpc =  BooksPermissionCopy.findByBpId(new Long(bp.id))
            bpc.status='returned'
            bpc.returnDate = new Date()
            bpc.save(flush: true, failOnError: true)
            bp.delete()
            BooksQueueDtl booksQueueDtl=BooksQueueDtl.findByBookIdAndBatchIdAndUsername(new Long(bookId),new Long(batchId),springSecurityService.currentUser.username)
            if(booksQueueDtl!=null) booksQueueDtl.delete()
            BooksQueueDtl queuebooksQueueDtl1=BooksQueueDtl.findByBookIdAndBatchIdAndAction(new Long(bookId),new Long(batchId),"queue",[sort: "dateCreated", order:"asc"])
            if(queuebooksQueueDtl1!=null) {
                def expiry=true;
                Calendar c = Calendar.getInstance()
                if(BooksBatchDtl.findByBatchIdAndBookId(batchId,new Long(params.bookId)).numberOfLicenses==null|| BooksBatchDtl.findByBatchIdAndBookId(batchId,new Long(params.bookId)).numberOfLicenses>BooksPermission.findAllByBatchIdAndBookIdAndPoType(batchId,new Long(params.bookId),"ADDEDFROMINSTITUTE").size()) {
                    //if(BooksBatchDtl.findByBatchIdAndBookId(new Long(params.batchId),new Long(params.bookId)).numberOfLicenses!=null) {
                    checkoutDays =
                            (InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).checkOutDays != ""
                                    && InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).checkOutDays != null)
                                    ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).checkOutDays)
                                    : null
                    if(checkoutDays!=null && checkoutDays!="") {
                        c.add(Calendar.DATE, checkoutDays)
                    }else{
                        c.add(Calendar.DATE, 14)
                    }
//                        }else{
//                            expiry=false
//                        }
                    BooksPermission booksPermission = new BooksPermission(
                            bookId: new Long(params.bookId),
                            username: queuebooksQueueDtl1.username,
                            poType: 'ADDEDFROMINSTITUTE',
                            batchId: batchId,
                            addedBy: queuebooksQueueDtl1.username,
                            expiryDate: expiry==true?c.getTime():null
                    )
                    booksPermission.save(flush: true, failOnError: true)
                    BooksPermissionCopy booksPermissionCopy = new BooksPermissionCopy(
                            bookId: new Long(params.bookId),
                            username: queuebooksQueueDtl1.username,
                            poType: 'ADDEDFROMINSTITUTE',
                            batchId:batchId,
                            addedBy: queuebooksQueueDtl1.username,
                            bpId: booksPermission.id,
                            expiryDate: expiry==true?c.getTime():null
                    )
                    booksPermissionCopy.save(flush: true, failOnError: true)
                    BooksViewDtl booksViewDtl = new BooksViewDtl(bookId:new Long(params.bookId), viewSource: params.viewSource?params.viewSource:viewSource, viewType:"library",username:queuebooksQueueDtl1.username,
                            siteId:siteId,instituteId:InstituteMst.findById(CourseBatchesDtl.findById(batchId).conductedBy).id)
                    booksViewDtl.save(flush:true, failOnError: true)
                    redisService.("lastReadBooksIns_"+batchId+"_"+ queuebooksQueueDtl1.username)=null
                    if (User.findByUsername(queuebooksQueueDtl1.username).email != null && !User.findByUsername(queuebooksQueueDtl1.username).email.equals("")) {
                        if (userManagementService.validateEmail(User.findByUsername(queuebooksQueueDtl1.username).email, getSiteId(request))) {
                            try {
                                userManagementService.sendInstituteUserEmail(
                                        User.findByUsername(queuebooksQueueDtl1.username).email,
                                        User.findByUsername(queuebooksQueueDtl1.username).name,
                                        "This book '" + BooksMst.findById(new Long(params.bookId)).title + "' is added to your library.",
                                        new Long(params.bookId),
                                        SiteMst.findById(getSiteId(request)).siteName
                                )
                            } catch (Exception e) {
                                println "sending library email failed " + e.toString()
                            }
                        }
                    }
                    queuebooksQueueDtl1.delete()
                }
            }
            redisService.("lastReadBooksIns_"+batchId+"_"+ springSecurityService.currentUser.username)=null
            if(User.findByUsername(bpc.username).email!=null && !User.findByUsername(bpc.username).email.equals("")) {
                if(userManagementService.validateEmail(User.findByUsername(bpc.username).email,getSiteId(request))) {
                    try {
                        userManagementService.sendInstituteUserEmail(
                                User.findByUsername(bpc.username).email,
                                User.findByUsername(bpc.username).name,
                                "Thanks! You've successfully returned the book.",
                                'false',
                                SiteMst.findById(getSiteId(request)).siteName
                        )
                    } catch (Exception e) {
                        println "sending library email failed " + e.toString()
                    }
                }
            }
            def json = [
                    status: 'OK',
            ]
            render json as JSON
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def removeBookFromUserQueue(){
        if(params.batchId!=null && params.batchId!='' && params.bookId!=null && params.bookId!='' && springSecurityService.currentUser!=null){
            BooksQueueDtl
                    .findByBatchIdAndBookIdAndUsername(new Long(params.batchId), new Long(params.bookId), springSecurityService.currentUser.username)
                    .delete()
            def json = [
                    status: 'OK'
            ]
            render json as JSON
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def getInstitutesBooksForUser(){
        Boolean bookAcess = false,bookscount = false
        List booklist1 = wsLibraryService.getUsersInsBooksByBatchId(params.batchId)

        if(booklist1 == null) {
            wsLibraryService.instituteBooksforUser(params.batchId)
            booklist1 = wsLibraryService.getUsersInsBooksByBatchId(params.batchId)
        }
        if(booklist1!=null) {
            for (int i = 0; i < booklist1.size(); i++) {
                def bookTemp = booklist1.get(i)
                List booksPermission = BooksPermission.findAllByBatchIdAndBookIdAndPoType(new Long(bookTemp.batchId), new Long(bookTemp.id), "ADDEDFROMINSTITUTE");
                BooksPermission booksPermission1 = BooksPermission.findByUsernameAndBatchIdAndBookId(springSecurityService.currentUser.username, new Long(bookTemp.batchId), new Long(bookTemp.id));
                if (booksPermission1 != null) {
                    bookAcess = true
                } else {
                    bookAcess = false
                }
                def noOflic = bookTemp.noOfLic != null && bookTemp.noOfLic != "" ? Integer.parseInt(bookTemp.noOfLic + "") : null
                if (noOflic == null || booksPermission.size() < noOflic) {
                    bookscount = true
                }else{
                    bookscount = false
                }
                bookTemp.bookAcess = bookAcess
                bookTemp.bookscount = bookscount
                booklist1.set(i, bookTemp)
            }
        }
        String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)
        if(redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username)==null || redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username)=="null"){
            dataProviderService.getLastReadBooksForInstitute(springSecurityService.currentUser.username,defaultBatchId)
        }
        def json = [ books:booklist1,'lastReadBooksForInstitute':redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username),  'status' : booklist1 ? "OK" : "Nothing present"]
        render json as JSON
    }


    @Transactional
    def getUsersBooks(){
        def json
        if(springSecurityService.currentUser!=null) {
            def pageNo = params.pageNo
            def userBookslist
            String username = springSecurityService.currentUser.username;
            if (redisService.("userShelfBooks_" + username) == null || redisService.("userShelfBooks_" + username) == "null") wsLibraryCacheService.userShelfBooks(username)
            if (pageNo != null) {
                userBookslist = new JsonSlurper().parseText(redisService.("userShelfBooks_" + username + "_page_" + pageNo))
            } else {
                userBookslist = new JsonSlurper().parseText(redisService.("userShelfBooksAll_" + username))
            }
            if (redisService.("lastReadBooks_" + username) == null) {
                dataProviderService.getLastReadBooks(username)
            }
             json = [books: userBookslist, 'lastReadBooks': redisService.("lastReadBooks_" + username), 'status': userBookslist ? "OK" : "Nothing present", count: redisService.("userShelfBooks_" + username + "_totalBooks")]
        }else {
            json = [books: null]
        }
           render json as JSON

    }


    @Transactional
    def autoAddBooksFromQueue() {
        Calendar c = Calendar.getInstance();
        def expiry = true;
        def siteId=params.siteId
        String sql = "SELECT bp.id,bp.book_id,bp.batch_id" +
                " FROM wsuser.books_permission bp, wsuser.books_mst bm " +
                " WHERE bp.book_id=bm.id AND bm.site_id="+siteId+" AND SUBSTRING_INDEX(bp.expiry_date, ' ', 1) <= sysdate() and bp.batch_id is not NULL";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def data = results.collect {
            BooksPermissionCopy booksPermissionCopy = BooksPermissionCopy.findByBpId(new Long(it.id))
            booksPermissionCopy.status = "removed"
            booksPermissionCopy.save(flush: true, failOnError: true)
            BooksQueueDtl booksQueueDtl = BooksQueueDtl.findByBookIdAndBatchIdAndAction(new Long(it.book_id), new Long(it.batch_id), "queue", [sort: "dateCreated", order: "asc"])
            if (booksQueueDtl != null) {
                    Integer checkoutDays =
                            (InstituteMst.findById(CourseBatchesDtl.findById(new Long(it.batch_id)).conductedBy).checkOutDays != ""
                                    && InstituteMst.findById(CourseBatchesDtl.findById(new Long(it.batch_id)).conductedBy).checkOutDays != null)
                                    ? Integer.parseInt(InstituteMst.findById(CourseBatchesDtl.findById(new Long(it.batch_id)).conductedBy).checkOutDays)
                                    : null
                    c.add(Calendar.DATE, checkoutDays != null ? checkoutDays : 14)
                BooksPermission booksPermission = new BooksPermission(
                        bookId: new Long(it.book_id),
                        username: booksQueueDtl.username,
                        poType: 'ADDEDFROMINSTITUTE',
                        batchId: new Long(it.batch_id),
                        addedBy: 'system',
                        expiryDate: expiry == true ? c.getTime() : null
                )
                booksPermission.save(flush: true, failOnError: true)
                BooksPermissionCopy booksPermissionCopy1 = new BooksPermissionCopy(
                        bookId: new Long(it.book_id),
                        username: booksQueueDtl.username,
                        poType: 'ADDEDFROMINSTITUTE',
                        batchId: new Long(it.batch_id),
                        addedBy: 'system',
                        bpId: booksPermission.id,
                        expiryDate: expiry==true?c.getTime():null
                )
                booksPermissionCopy1.save(flush: true, failOnError: true)
                BooksViewDtl booksViewDtl = new BooksViewDtl(bookId:new Long(it.book_id), viewSource: "web", viewType:"library",username:booksQueueDtl.username,
                        siteId:Integer.parseInt(siteId),instituteId:InstituteMst.findById(CourseBatchesDtl.findById(new Long(it.batch_id)).conductedBy).id)
                booksViewDtl.save(flush:true, failOnError: true)
                if (userManagementService.validateEmail(User.findByUsername(booksQueueDtl.username).email,Integer.parseInt(siteId))) {
                    try {
                        userManagementService.sendInstituteUserEmail(
                                User.findByUsername(booksQueueDtl.username).email,
                                User.findByUsername(booksQueueDtl.username).name,
                                "This book '" + BooksMst.findById(new Long(it.book_id)).title + "' is added to your library.",
                                new Long(it.book_id),
                                SiteMst.findById(siteId).siteName
                        )
                    } catch (Exception e) {
                        println "sending library email failed " + e.toString()
                    }
                }
                redisService.("lastReadBooksIns_"+it.batch_id+"_"+ booksQueueDtl.username)=null
                booksQueueDtl.delete()
            }
            BooksPermission
                    .findById(new Long(it.id))
                    .delete()
            redisService.("lastReadBooksIns_"+it.batch_id+"_"+ booksPermissionCopy.username)=null
        }
    }

    @Transactional
    def autoDeleteInstituteBookValidityUsers() {
        String sql = "SELECT bbd.id,bbd.batch_id,bbd.book_id " +
                " FROM books_batch_dtl bbd " +
                " WHERE  bbd.book_expiry_date <= sysdate()";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def data = results.collect {
            List books=BooksPermission.findAllByBatchIdAndBookId(new Long(it.batch_id), new Long(it.book_id))
            redisService.("userMyLibraryInstituteBooks_"+it.batch_id) = null
            wsLibraryCacheService.getInstituteBooksPagination(it.batch_id)
            wsLibraryCacheService.getInstituteBooksPaginationNew(it.batch_id)
            for(int i=0;i<books.size();i++){
                redisService.("lastReadBooksIns_"+books[i].batchId+"_"+ books[i].username)=null
                BooksPermissionCopy booksPermissionCopy = BooksPermissionCopy.findByBpId(new Long(books[i].id))
                booksPermissionCopy.status = "removed"
                booksPermissionCopy.save(flush: true, failOnError: true)
                BooksPermission.executeUpdate("delete BooksPermission where bookId = (" + books[i].bookId+") and batchId='"+books[i].batchId+"'")
            }
            List booksQueue=BooksQueueDtl.findAllByBatchIdAndBookId(new Long(it.batch_id), new Long(it.book_id))
            for(int i=0;i<booksQueue.size();i++){
                BooksQueueDtl.executeUpdate("delete BooksQueueDtl where bookId = (" + booksQueue[i].bookId+") and batchId='"+booksQueue[i].batchId+"'")
            }
            BooksBatchDtl booksBatchDtl=BooksBatchDtl.findById(new Long(it.id))
            booksBatchDtl.delete();
        }

    }


    @Transactional
    def getQueueListByBookIdAndBatchId(){
        String sql = "SELECT SUBSTRING_INDEX(bqd.username, \"_\", -1) AS username,u.name,COALESCE(u.mobile,' ') as mobile,COALESCE(u.email,' ') as email" +
                " FROM wsuser.books_queue_dtl bqd, user u" +
                " WHERE bqd.username=u.username and  bqd.action='queue' AND  bqd.book_id='"+params.bookId+"' and bqd.batch_id='"+params.batchId+"'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def data = results.collect{
            return [username: it.username, name: it.name, mobile: it.mobile,email:it.email]
        }
        def json = [
                QueueBooksByBookId: data
        ]
        render json as JSON

    }


    @Transactional
    def getQueueListByBatchId(){
        String sql = "SELECT SUBSTRING_INDEX(bqd.username, \"_\", -1) AS username,us.name,COALESCE(us.mobile,' ') as mobile,COALESCE(us.email,' ') as email,bm.title,bm.id" +
                " FROM wsuser.books_queue_dtl bqd, wsuser.user us,wsuser.books_mst bm " +
                " WHERE bqd.username=us.username and bm.id=bqd.book_id AND bqd.action='queue' AND bqd.batch_id='"+params.batchId+"'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def data = results.collect{
            return [username: it.username, name: it.name, mobile: it.mobile,email:it.email,title:it.title,bookId:it.id]
        }
        def json = [
                QueueBooksByBookId: data
        ]
        render json as JSON

    }

    @Secured(['ROLE_USER'])
    @Transactional
    def updateAppBookViews(){
        Integer siteId = getSiteId(request)
        BooksViewDtl  booksViewDtl = new BooksViewDtl(bookId: new Long(params.bookId), viewSource: "mobile", viewType: "library", username: (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""),
                siteId: getSiteId(request), instituteId: params.instituteId)
        booksViewDtl.save(flush: true, failOnError: true)
        dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
        def json = [
                status: 'Ok'
        ]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def getInstitutesForUserApp(String ipAddress,Integer siteId){
        List instituteDetails
        String sql =
                " SELECT bud.batch_id, im.id institute_id, im.name " +
                        " FROM wsuser.batch_user_dtl bud, wsuser.course_batches_dtl cbd, wsuser.institute_mst im " +
                        " where im.site_id="+siteId+" and bud.username='"+springSecurityService.currentUser.username+"' " +
                        " and cbd.id=bud.batch_id and im.id=cbd.conducted_by " +
                        " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) " +
                        " union " +
                        " select cbd.id batch_id, im.id institute_id, im.name from wsuser.institute_mst im, wsuser.course_batches_dtl cbd, wsuser.institute_ip_address iia " +
                        " where iia.ip_address= '"+ipAddress+"' and im.id =iia.institute_id and  im.id=cbd.conducted_by " +
                        " and cbd.status='active' and (cbd.start_date <= SYSDATE() or cbd.start_date is null) and im.site_id="+siteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        instituteDetails = results.collect{
            return [batchId: it.batch_id, id: it.institute_id, name: it.name]
        }
            return  instituteDetails;
    }

    @Secured(['ROLE_USER'])
    @Transactional
    def bookAccessForUserBooks(){
        def bookId=params.bookId
        String username=springSecurityService.currentUser.username;
        boolean hasUserBookAccess = false
        List batchIdList = getInstitutesForUser();
        if (redisService.("userShelfBooks_" + username) == null  || redisService.("userShelfBooks_" + username) == "null") wsLibraryCacheService.userShelfBooks(username)
        def userSelfCache=redisService.("userShelfBooks_"+springSecurityService.currentUser.username+"_"+"bookIds")
        List booksIds = userSelfCache!=null?Arrays.asList(redisService.("userShelfBooks_"+springSecurityService.currentUser.username+"_"+"bookIds").split("\\s*,\\s*")):null
        BooksPermission booksPermission
        if(batchIdList != null && batchIdList.size() <= 1) {
            long bId = batchIdList.size() == 1? batchIdList.get(0).batchId:0
            booksPermission= BooksPermission.findByBookIdAndUsernameAndBatchId(new Long(bookId),username,new Long(bId))
        }else if(batchIdList != null || batchIdList.size() > 1){
            for(int i=0;i<batchIdList.size();i++){
                booksPermission= BooksPermission.findByBookIdAndUsernameAndBatchId(new Long(bookId),username,new Long(batchIdList[i].batchId))
                if(booksPermission!=null)break;
            }
        }
        if ((booksIds!=null && booksIds.indexOf(bookId) > -1) || booksPermission!=null) {
            hasUserBookAccess = true;
        }else{
            hasUserBookAccess = false;
        }
        def json = [ 'hasUserBookAccess' : hasUserBookAccess]
        render json as JSON
    }

    def index(){
        [ "title":"Digital Library - Wonderslate", commonTemplate:"true"]
    }

    @Secured(['ROLE_USER']) @Transactional
    def getInstitutesBooksForUserOptimised(){
        List booklist1 = wsLibraryService.getUsersInsBooksByBatchId(params.batchId)
        if(booklist1 == null) {
            wsLibraryService.instituteBooksforUser(params.batchId)
            booklist1 = wsLibraryService.getUsersInsBooksByBatchId(params.batchId)
        }
        String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)
        if(redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username)==null || redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username)=="null"){
            dataProviderService.getLastReadBooksForInstitute(springSecurityService.currentUser.username,defaultBatchId)
        }
        def json = [ books:booklist1,'lastReadBooksForInstitute':redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username),  'status' : booklist1 ? "OK" : "Nothing present"]
        render json as JSON
    }

    
     @Transactional
    def  getPaginatedInstituteBooks(){
        def pageNo = params.pageNo
        def booklist1
        if(redisService.("instituteLibraryBooklist_" + params.batchId)==null) wsLibraryCacheService.getInstituteBooksPagination(params.batchId)
        if(pageNo!=null) {
            booklist1 = redisService.("instituteLibraryBooklist_" + params.batchId + "_page_" + pageNo)
        }else{
            booklist1=redisService.("InstituteLibraryAllBooksList_"+params.batchId)
        }
        String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)

     if(springSecurityService.currentUser!=null) {
         if (redisService.("lastReadBooksIns_" + defaultBatchId + "_" + springSecurityService.currentUser.username) == null || redisService.("lastReadBooksIns_" + defaultBatchId + "_" + springSecurityService.currentUser.username) == "null") {
             dataProviderService.getLastReadBooksForInstitute(springSecurityService.currentUser.username, defaultBatchId)
         }
     }
        def json = [ books:booklist1,'lastReadBooksForInstitute':springSecurityService.currentUser!=null?redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username):null,  'status' : booklist1 ? "OK" : "Nothing present",totalBooks: redisService.("instituteLibrary_"+params.batchId+"_totalBooks"), totalBooksPaid:redisService.("instituteLibrary_"+params.batchId+"_totalBooksPaid"),totalBooksFree: redisService.("instituteLibrary_"+params.batchId+"_totalBooksFree")]
        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def checkInstituteAccessCode(){
        Integer siteId = getSiteId(request)
        def json=wsLibraryService.checkInstituteAccessCode(params,session,siteId)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def accessCode(){
        [ "title":"Scratch/Access code page", commonTemplate:"true"]
      }

    @Secured(['ROLE_USER']) @Transactional
    def  getPaginatedInstituteBooksNew(){
        def pageNo = params.pageNo
        def type = params.type
        def booklist1
        if("free".equals(type)) {
            if (redisService.("instituteLibraryBooklistFree_" + params.batchId) == null) wsLibraryCacheService.getInstituteBooksPaginationNew(params.batchId)
            if (pageNo != null) {
                booklist1 = redisService.("instituteLibraryBooklistFree_" + params.batchId + "_page_" + pageNo)
            } else {
                booklist1 = redisService.("instituteLibraryBooklistFree_" + params.batchId)
            }
        }else{
            if (redisService.("instituteLibraryBooklistPaid_" + params.batchId) == null) wsLibraryCacheService.getInstituteBooksPaginationNew(params.batchId)
            if (pageNo != null) {
                booklist1 = redisService.("instituteLibraryBooklistPaid_" + params.batchId + "_page_" + pageNo)
            } else {
                booklist1 = redisService.("instituteLibraryBooklistPaid_" + params.batchId)
            }
        }
        String defaultBatchId = wsLibraryService.getDefaultBatchId(params.batchId)
        if(redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username)==null || redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username)=="null"){
            dataProviderService.getLastReadBooksForInstitute(springSecurityService.currentUser.username,defaultBatchId)
        }
        def json = [ books:booklist1,'lastReadBooksForInstitute':redisService.("lastReadBooksIns_"+defaultBatchId+"_"+springSecurityService.currentUser.username),  'status' : booklist1 ? "OK" : "Nothing present",totalBooksFree: redisService.("instituteLibrary_"+params.batchId+"_totalBooksFree"),
                     totalBooksPaid:redisService.("instituteLibrary_"+params.batchId+"_totalBooksPaid")]
        render json as JSON
    }

}
