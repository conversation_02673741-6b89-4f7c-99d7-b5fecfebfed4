package com.wonderslate

import com.wonderslate.data.PrepjoyService
import com.wonderslate.data.UtilService
import com.wonderslate.log.Quizrecorder
import com.wonderslate.prepjoy.QuizRecMst
import com.wonderslate.usermanagement.AnalyticsService
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import grails.converters.JSON
import org.apache.commons.lang.StringUtils

class AnalyticsController {
    SpringSecurityService springSecurityService
    UserManagementService userManagementService
    AnalyticsService analyticsService
    def redisService
    PrepjoyService prepjoyService
    UtilService utilService

    def index() {}

    @Secured(['ROLE_USER'])
    def latestReports() {
        def reportsList = Quizrecorder.findByUsername(springSecurityService.currentUser.username);
        List reports = reportsList.collect { report ->

            return [id: report.id, assignmentid: report.quiz]
        }

        def json =
            [
                    'results': reports,
                    'status' : reports ? "OK" : "Nothing present"
            ]
        render json as JSON

    }

    def reportDetails() {}

    @Transactional
    def getQuizScoreAnalytics() {
        if (springSecurityService.currentUser != null) {
            String totalQuestions
            def previousAttempts = Quizrecorder.findAllByQuizidAndUsername(new Integer(params.quizid), springSecurityService.currentUser.username, [max: 15]);
            List jsonPreviousAttempts = previousAttempts.collect { previousAttempt ->
                totalQuestions = previousAttempt.totalQuestions;
                return [correctAnswers: previousAttempt.correctAnswers, skipped: previousAttempt.skipped, totalQuestions: previousAttempt.totalQuestions, timetaken: previousAttempt.timetaken, wrongAnswers: previousAttempt.wrongAnswers, quizDate: previousAttempt.endtime]
            }

            def sql = "SELECT max(correct_answers), avg(correct_answers),(select avg(correct_answers) from  quizrecorder where quizid="+params.quizid+" and username='"+springSecurityService.currentUser.username+"'), count(correct_answers)  FROM quizrecorder where quizid="+params.quizid;

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            String topperScore="";
            String averageScore="";
            String userAverage="";
            String totalAttempts="0";
            results.collect { score ->
                topperScore = ""+score[0]
                averageScore = ""+score[1]
                userAverage=""+score[2]
                totalAttempts=""+score[3]
            }
             sql = "SELECT max(correct_answers), min(correct_answers)  FROM quizrecorder where quizid="+params.quizid+" and username='"+springSecurityService.currentUser.username+"'";

             dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
             sql1 = new Sql(dataSource)
             results = sql1.rows(sql);
            String userHighest="";
            String userLowest="";
            results.collect { score ->
                userHighest = ""+score[0]
                userLowest = ""+score[1]

            }

            def json =
                [
                        'results': jsonPreviousAttempts.reverse(),
                        'status' : jsonPreviousAttempts ? "OK" : "Nothing present",
                        'topperScore':topperScore ,
                        'averageScore':averageScore,
                        'userAverage':userAverage,
                        'userHighest':userHighest,
                        'userLowest':userLowest,
                        'totalQuestions': totalQuestions,
                        "totalAttempts":totalAttempts

                ]
            render json as JSON
        } else {
            def json =
                [
                        'status': "NOT_LOGGED"
                ]
            render json as JSON
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def getUsersLastQuiz(){
        String username = springSecurityService.currentUser.username
        if(redisService.("latestQuizInfo_"+username)==null) analyticsService.getUsersLastQuiz(username)
        def json = ['lastQuizDetails':redisService.("latestQuizInfo_"+username)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getUsersHistoryForAQuiz(){
        String username = springSecurityService.currentUser.username
        String quizId  = params.resId
        String quizType = params.quizType
        String quizTypeName = "regular"
        if("dailyTests".equals(quizType)) quizTypeName="dailyTests"
       //quizType can be dailyTests, regular
        if(redisService.("userQuizHistoryFor_"+quizTypeName+"_"+username+"_"+quizId)==null||"null".equals(redisService.("userQuizHistoryFor_"+quizTypeName+"_"+username+"_"+quizId))) analyticsService.getUsersHistoryForAQuiz(username,quizId,quizType)

        def json = ['lastQuizDetails':redisService.("userQuizHistoryFor_"+quizTypeName+"_"+username+"_"+quizId)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def totalQuizzesAttempted(){
        String username = springSecurityService.currentUser.username
        if(redisService.("userTotalQuizCount_"+username)==null) analyticsService.totalQuizzesAttempted(username)
        def json = ['totalQuizzesAttempted':redisService.("userTotalQuizCount_"+username)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getUsersHistoryForAllQuizzes(){
        String username = springSecurityService.currentUser.username
        if(redisService.("userTotalQuizCountWithResId_"+username+"_"+params.resId)==null) analyticsService.totalQuizzesAttemptedWithResId(username,params.resId)
        def json = ['quizHistory':analyticsService.getUsersHistoryForAllQuizzes(username,params.startIndex, params.resId),'totalQuizzesAttempted':redisService.("userTotalQuizCountWithResId_"+username+"_"+params.resId)]

        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def getLeaderBoardForAdmins(){
        String rankDate = params.rankDate
        List dailyRanks,weeklyRanks,monthlyRanks
        String instituteId = null
        String institutePrefix=""
        if(params.instituteId!=null&& StringUtils.isNumeric(instituteId)) {
            instituteId = params.instituteId
            institutePrefix="institute_"+instituteId+"_"
        }
        Integer siteId = utilService.getSiteId(request,session)
        if(redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + rankDate)==null) prepjoyService.getDailyLeaderBoard(rankDate,siteId,instituteId)
        if(redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + rankDate)!=null&&!"No Ranks".equals(redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + rankDate))){
             dailyRanks=new JsonSlurper().parseText(redisService.(institutePrefix+"prepJoyTodaysRank_"+siteId+"_" + rankDate))
             dailyRanks.each{ dailyRank ->
                dailyRank.username = userManagementService.decrypt(dailyRank.username)
            }
        }
        if(redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getWeeklyLeaderBoard(rankDate,siteId,instituteId)
        if(redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + rankDate)!=null&&!"No Ranks".equals(redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + rankDate))){
            weeklyRanks=new JsonSlurper().parseText(redisService.(institutePrefix+"prepJoyWeeklyRank_"+siteId+"_" + rankDate))
            weeklyRanks.each{ weeklyRank ->
                weeklyRank.username = userManagementService.decrypt(weeklyRank.username)
            }
        }

        if(redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + rankDate)==null) prepjoyService.getMonthlyLeaderBoard(rankDate,siteId,instituteId)
        if(redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + rankDate)!=null&&!"No Ranks".equals(redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + rankDate))){
            monthlyRanks=new JsonSlurper().parseText(redisService.(institutePrefix+"prepJoyMonthlyRank_"+siteId+"_" + rankDate))
            monthlyRanks.each{ monthlyRank ->
                monthlyRank.username = userManagementService.decrypt(monthlyRank.username)
            }
        }

        def json =[dailyRanks:dailyRanks,weeklyRanks:weeklyRanks,monthlyRanks:monthlyRanks]
        render json as JSON

    }

    @Secured(['ROLE_CUSTOMER_SUPPORT']) @Transactional
    def leaderBoardForAdmins() {
      String  sql ="select im.id,im.name from wsuser.institute_mst im, wsuser.course_batches_dtl cbd where im.site_id="+session["siteId"] +
              "  and cbd.name='Default' and (im.leader_board_enabled='true' or im.leader_board_enabled is null) "+
              " and cbd.conducted_by=im.id order by im.name"
      def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);

        List institutes = results.collect { institute ->
            return [id: institute[0], name: institute[1],ipRestricted: institute[2],endDate:institute[3],batchId:institute[4],defaultBooksTemplateInstitute:institute[5]]
        }

        [institutes:institutes]
    }

}
