package com.wonderslate.librarybooks

import com.wonderslate.data.UtilService
import com.wonderslate.publiclibrary.*
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class DigitalLibraryController {
    def springSecurityService
    UtilService utilService

     @Transactional
    def index() {
         if (springSecurityService.currentUser != null||utilService.hasLibraryAccess(request,new Integer(""+session["siteId"]))) {
             boolean masterLibraryAdmin = false
             Integer siteId = new Integer(""+session["siteId"])
             def categories = LibraryCategory.findAllBySiteId(siteId)
             if(categories.size()==0){
                 categories = LibraryCategory.findAllBySiteId(new Integer(1))
             }

             if(session["userdetails"]!=null){
             User user = session["userdetails"]
                 //master library admin
                 if (user.authorities.any {
                     it.authority == "ROLE_MASTER_LIBRARY_ADMIN"
                 }) masterLibraryAdmin = true
            }
         [categories: categories, masterLibraryAdmin: masterLibraryAdmin]
     }else {
            redirect(controller: "privatelabel", action: "index")
        }
    }

   @Transactional
    def showCategory(Long id) {
        if (springSecurityService.currentUser != null||utilService.hasLibraryAccess(request,new Integer(""+session["siteId"]))) {
            def category = LibraryCategory.get(id)
            if (!category) {
                flash.message = "Category not found"
                redirect(action: "index")
                return
            }
            [category: category]
        }else {
            redirect(controller: "privatelabel", action: "index")
        }
    }
    @Transactional
    def getCategory(Long id) {
        def category = LibraryCategory.get(id)
        if (category) {
            render(contentType: "application/json") {
                id category.id
                name category.name
                icon category.icon
                color category.color
            }
        } else {
            render(status: 404, text: 'Category not found')
        }
    }



    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def adminIndex() {

        List referenceSections = ReferenceSection.list()

        // Load all categories to display in the dropdown
        Integer siteId = new Integer(""+session["siteId"])
        def categories = LibraryCategory.findAllBySiteId(siteId)
        if(categories.size()==0){
            categories = LibraryCategory.findAllBySiteId(new Integer(1))
        }
        [categories: categories,referencesections:referenceSections]
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def getCategories(){
        def categories = LibraryCategory.findAllByReferenceSectionId(params.referenceSectionId as Integer)
        def json = [categories: categories]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def getSubcategories() {
        def categoryId = params.categoryId as Long
        def category = LibraryCategory.get(categoryId)
        def subcategories = LibrarySubcategory.findAllByCategory(category)
        String options = ''
        subcategories.each { subcategory ->
            options += "<option value='${subcategory.id}'>${subcategory.name}</option>"
        }
        render options
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def getResources() {
        def subcategoryId = params.subcategoryId as Long
        def subcategory = LibrarySubcategory.get(subcategoryId)
        def resources = LibraryResource.findAllBySubcategory(subcategory)
        String rows = ''
        resources.each { resource ->
            rows += """
            <tr>
                <td>${resource.name}</td>
                <td>${resource.link}</td>
                <td>${resource.icon}</td>
                <td>
                    <button class="btn btn-sm btn-warning edit-resource-btn" data-id="${resource.id}">Edit</button>
                    <button class="btn btn-sm btn-danger delete-resource-btn" data-id="${resource.id}">Delete</button>
                </td>
            </tr>
            """
        }
        render rows
    }
    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def addCategory() {
        Integer siteId = utilService.getSiteId(request, session)
        def category = new LibraryCategory(
                name: params.name,
                icon: params.icon,
                color: params.color,
                siteId: siteId,
                referenceSectionId: new Integer(params.referenceSectionId)
        )
        if (category.save(flush: true)) {
            render(status: 200, text: 'Category added successfully')
        } else {
            render(status: 400, text: 'Failed to add category')
        }
    }
    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def addSubcategory() {
        def categoryId = params.categoryId as Long
        def category = LibraryCategory.get(categoryId)
        def subcategory = new LibrarySubcategory(
                name: params.name,
                category: category
        )
        if (subcategory.save(flush: true)) {
            render(status: 200, text: 'Subcategory added successfully')
        } else {
            render(status: 400, text: 'Failed to add subcategory')
        }
    }
    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def addResource() {
        def subcategoryId = params.subcategoryId as Long
        def subcategory = LibrarySubcategory.get(subcategoryId)
        def resource = new LibraryResource(
                name: params.name,
                link: params.link,
                icon: params.icon,
                subcategory: subcategory
        )
        if (resource.save(flush: true)) {
            render(status: 200, text: 'Resource added successfully')
        } else {
            render(status: 400, text: 'Failed to add resource')
        }
    }
    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def getResource() {
        def resourceId = params.id as Long
        def resource = LibraryResource.get(resourceId)
        if (resource) {
            render(contentType: "application/json") {
                id resource.id
                name resource.name
                link resource.link
                icon resource.icon
            }
        } else {
            render(status: 404, text: 'Resource not found')
        }
    }
    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def editResource() {
        def resourceId = params.id as Long
        def resource = LibraryResource.get(resourceId)
        resource.name = params.name
        resource.link = params.link
        resource.icon = params.icon
        if (resource.save(flush: true)) {
            render(status: 200, text: 'Resource updated successfully')
        } else {
            render(status: 400, text: 'Failed to update resource')
        }
    }
    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN', 'ROLE_LIBRARY_ADMIN','ROLE_MASTER_LIBRARY_ADMIN'])
    def deleteResource() {
        def resourceId = params.id as Long
        def resource = LibraryResource.get(resourceId)
        if (resource) {
            resource.delete(flush: true)
            render(status: 200, text: 'Resource deleted successfully')
        } else {
            render(status: 400, text: 'Failed to delete resource')
        }
    }
}
