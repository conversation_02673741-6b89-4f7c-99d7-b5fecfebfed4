package com.wonderslate.librarybooks

import com.wonderslate.publiclibrary.*
import grails.converters.JSON

class BookmarkController {

    def index() {
        def username = session.username ?: 'guest'
        def bookmarks = LibraryBookmark.findAllByUsername(username)
        [bookmarks: bookmarks]
    }

    def addBookmark(Long resourceId) {
        def username = session.username ?: 'guest'
        def resource = LibraryResource.get(resourceId)
        if (!resource) {
            render([status: 'error', message: 'Resource not found'] as JSON)
            return
        }

        if (!LibraryBookmark.findByUsernameAndResource(username, resource)) {
            new LibraryBookmark(username: username, resource: resource).save(flush: true)
            render([status: 'success', message: 'Bookmark added'] as JSON)
        } else {
            render([status: 'error', message: 'Bookmark already exists'] as JSON)
        }
    }

    def deleteBookmark(Long id) {
        def bookmark = LibraryBookmark.get(id)
        if (bookmark) {
            bookmark.delete(flush: true)
            flash.message = "Bookmark removed"
        } else {
            flash.message = "Bookmark not found"
        }
        redirect(action: "index")
    }
}
