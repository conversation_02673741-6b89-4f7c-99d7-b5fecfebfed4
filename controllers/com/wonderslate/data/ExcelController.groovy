package com.wonderslate.data

import com.ibookso.products.PrintBooksMst
import com.maxmind.geoip2.DatabaseReader
import com.wonderslate.cache.DataProviderService
import com.wonderslate.institute.InstManagerService
import com.wonderslate.log.FailedIsbns
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.publish.*
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.shop.BookProductTypes
import com.wonderslate.usermanagement.*
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.mail.MailService
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import groovy.transform.TupleConstructor
import org.apache.commons.io.FileUtils
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.PDPage
import org.apache.pdfbox.pdmodel.PDPageTree
import org.apache.pdfbox.pdmodel.interactive.documentnavigation.outline.PDOutlineItem
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.apache.poi.xwpf.usermodel.XWPFDocument
import org.imgscalr.Scalr
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.web.client.RestTemplate

import javax.imageio.ImageIO
import java.awt.image.BufferedImage
import java.nio.file.Path
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import java.util.regex.Matcher
import java.util.regex.Pattern
import java.nio.file.Files
import pl.touk.excel.export.WebXlsxExporter
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

@TupleConstructor
class ChapterMark {
    String name
    int startPage

}


class ExcelController {

    UtilService utilService
    def redisService
    DataProviderService dataProviderService
    def springSecurityService
    UserManagementService userManagementService
    MailService mailService
    QuizExtractorService quizExtractorService
    ResourceCreatorService resourceCreatorService
    InstManagerService instManagerService
    PromptService promptService

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def fileUploader() {
        List publishers
        Integer siteId = utilService.getSiteId(request, session)
        if (session["userdetails"].publisherId != null) {
            publishers = Publishers.findAllById(session["userdetails"].publisherId)
        } else {
            if (redisService.("publishers_" + siteId) == null)
                dataProviderService.getPublishers(siteId)

            publishers = new JsonSlurper().parseText(redisService.("publishers_" + siteId))
        }
        List bookTypes = BookProductTypes.findAll()
        bookTypes.push(["bookType": "StockUpdate"])


        [publishers: publishers, bookTypes: bookTypes]
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def processRow() {
        def requestBody = request.JSON
        if("epubUpload".equals(requestBody.bookType)){
            def bookId = uploadSplitEpubRemote(requestBody)
            def status = "OK"
            def response = [status: status, information: "Book id updated is "+bookId]
            render response as JSON
        }
        else {
            def requestName = requestBody.requestName
            requestBody.remove('requestName')
            def parameters = requestBody
            String information = ""
            def status = ""


            //first get isbn and check if book exists
            String bookId = requestBody.Book_Id
            String isbn = requestBody.ISBN
            if (isbn != null && isbn.indexOf(',') > 0) {
                String[] isbns = isbn.split(',')
                isbn = isbns[0]
            }
            String bookCode = requestBody.Vendor_Book_Id
            String publisherId = requestBody.publisherId
            String title = requestBody.Book_Title
            String bookDesciption = requestBody.Book_Description
            String coverImage = requestBody.Cover_Image
            String level = requestBody.Filter_Level_1
            String syllabus = requestBody.Filter_Level_2
            String grade = requestBody.Filter_Level_3
            String subject = requestBody.Filter_Level_4
            String language = requestBody.Language
            String currentStock = requestBody.Current_Stock
            String externalLink = requestBody.External_Link
            String publish = requestBody.Publish
            BooksMst booksMst

            if (bookId != null && !"".equals(bookId)) {
                booksMst = BooksMst.findById(new Integer(bookId))
            } else if (isbn != null && !"".equals(isbn)) {
                booksMst = BooksMst.findByIsbn(isbn)
            } else if (bookCode != null && !"".equals(bookCode)) {
                booksMst = BooksMst.findByBookCode(bookCode)
            }else if(title!=null&&!"".equals(title)){
                booksMst = BooksMst.findByTitleAndPublisherId(title,new Integer(publisherId))
            }

            Integer bookWeight = requestBody.Weight_of_the_Book != null && !"".equals(requestBody.Weight_of_the_Book) ? new Integer(requestBody.Weight_of_the_Book) : null
            boolean publishBook = false
            if (booksMst != null) {
                 //book present do the update thingy .. right now let's update only the price
                if ("StockUpdate".equals(requestBody.bookType)) {
                    if (currentStock != null && !"".equals(currentStock)) {
                        BooksMst.executeUpdate("update BooksMst set current_stock=" + currentStock + " where id=" + booksMst.id)
                        BooksMst.wsuser.executeUpdate("update BooksMst set current_stock=" + currentStock + " where id=" + booksMst.id)
                        BooksMst.wsshop.executeUpdate("update BooksMst set current_stock=" + currentStock + " where id=" + booksMst.id)
                        information += "Stock updated for book id ${booksMst.id}\n"
                    }
                } else {
                    BookPriceDtl bookPriceDtl = BookPriceDtl.findByBookIdAndBookTypeAndCurrencyCd(new Integer("" + booksMst.id), requestBody.bookType, requestBody.currency != null ? requestBody.currency : "INR")
                    if (bookPriceDtl == null) {
                        bookPriceDtl = new BookPriceDtl(bookType: requestBody.bookType, currencyCd: requestBody.currency != null ? requestBody.currency : "INR",
                                bookId: new Integer("" + booksMst.id), sellPrice: new Double(requestBody.Sell_Price), listPrice: new Double(requestBody.List_Price))
                        information += "Book price added for book id ${booksMst.id}\n"
                    } else {
                        bookPriceDtl.listPrice = new Double(requestBody.List_Price)
                        bookPriceDtl.sellPrice = new Double(requestBody.Sell_Price)
                        information += "Book price updated for book id ${booksMst.id}\n"
                    }
                    bookPriceDtl.save(failOnError: true, flush: true)
                }
                //now see if the pdf thingy has to be uploaded
                if(requestBody.Book_Link!=null&&!"".equals(requestBody.Book_Link)) {
                    ChaptersMst chaptersMst = ChaptersMst.findByBookId(new Integer("" + booksMst.id))
                    if(chaptersMst==null) bookUploadFromPDF(requestBody, ""+booksMst.id)
                }
                //extract image if it is from google drive
                if (requestBody.Cover_Image != null&&requestBody.Cover_Image.indexOf("drive.google")!=-1) {
                    def fileId = extractFileIdFromGoogleDriveUrl(requestBody.Cover_Image)
                    def fileUrl = "https://drive.google.com/uc?export=download&id=" + fileId
                    downloadCoverImage(booksMst.id, fileUrl)
                }
                status = "OK"
            } else {
                //cover image logic
                if ((coverImage == null || "".equals(coverImage)) && (isbn != null && !"".equals(isbn))) {
                    //now lets if we have that book in external books
                    PrintBooksMst printBooksMst = PrintBooksMst.findByIsbn(isbn)
                    if (printBooksMst != null) {
                        coverImage = printBooksMst.coverImage
                        information += "Got the cover image from external print book \n"
                    }
                }
                //first lets create the book with title.
                BookIdGenerator bookIdGenerator = new BookIdGenerator()
                bookIdGenerator.save(failOnError: true, flush: true)
                booksMst = new BooksMst();
                booksMst.setId(bookIdGenerator.id);
                booksMst.setTitle(title);
                booksMst.setSiteId(new Integer("" + session["siteId"]));
                booksMst.setDescription(bookDesciption);
                booksMst.setPublisherId(new Integer(publisherId));
                booksMst.setCoverImage(coverImage);
                booksMst.setCreatedBy(springSecurityService.currentUser.username);
                booksMst.setAuthors(requestBody.Author);
                booksMst.setBookWeight(bookWeight);
                booksMst.setIsbn(isbn);
                booksMst.setLanguage(language);
                booksMst.setBookCode(bookCode);
                booksMst.setShowInLibrary("Yes")
                booksMst.setNewStorage("Yes")
                booksMst.setExternalLink(externalLink)
                booksMst.setCurrentStock(currentStock != null && !"".equals(currentStock) ? new Integer(currentStock) : null)
                booksMst.save(failOnError: true, flush: true)
                information += "New book with id ${booksMst.id} created. \n"

                // check if author present
                if (requestBody.Author != null && !"".equals(requestBody.Author)) {
                    String[] authorsList = requestBody.Author.split(',')
                    for (int i = 0; i < authorsList.length; i++) {
                        Authors authors = Authors.findByNameAndPublisherId(authorsList[i], new Integer(publisherId))
                        if (authors == null) {
                            //author doesn't exist, will create
                            authors = new Authors(name: authorsList[i], publisherId: new Integer(publisherId))
                            authors.save(failOnError: true, flush: true)
                        }
                        BooksAuthorDtl booksAuthorDtl = new BooksAuthorDtl(authorId: authors.id, bookId: booksMst.id)
                        booksAuthorDtl.save(failOnError: true, flush: true)
                    }
                }
                //now check the book Type and add the price
                if (!"externalBook".equals(requestBody.bookType)) {
                    BookPriceDtl bookPriceDtl = new BookPriceDtl(bookType: requestBody.bookType, currencyCd: requestBody.currency != null ? requestBody.currency : "INR",
                            bookId: new Integer("" + booksMst.id), sellPrice: new Double(requestBody.Sell_Price), listPrice: new Double(requestBody.List_Price))
                    bookPriceDtl.save(failOnError: true, flush: true)
                }
                //now add categories
                if (level != null && !"".equals(level) && syllabus != null && !"".equals(syllabus) && grade != null && !"".equals(grade) && subject != null && !"".equals(subject)) {
                    BooksTagDtl booksTagDtl = new BooksTagDtl(bookId: booksMst.id, level: level, syllabus: syllabus, grade: grade, subject: subject)
                    booksTagDtl.save(failOnError: true, flush: true)
                    BooksTagDtl booksTagDtl1 = new BooksTagDtl(bookId: booksMst.id, level: level, syllabus: syllabus, grade: grade, subject: subject)
                    booksTagDtl1.wsshop.save(failOnError: true, flush: true)
                    if("Yes".equals(publish)) {
                        booksMst.status = "published"
                        booksMst.datePublished = new Date()
                        booksMst.save(failOnError: true, flush: true)
                        publishBook = true
                        information += "Book published \n"
                    }

                } else if ("externalBook".equals(requestBody.bookType)) {
                    BooksTagDtl booksTagDtl = new BooksTagDtl(bookId: booksMst.id, syllabus: syllabus)
                    booksTagDtl.save(failOnError: true, flush: true)
                    BooksTagDtl booksTagDtl1 = new BooksTagDtl(bookId: booksMst.id, syllabus: syllabus)
                    booksTagDtl1.wsshop.save(failOnError: true, flush: true)
                } else {
                    information += "Book not published as category information is not given\n"
                }

                //now copy the books mst to two other schemas
                BooksMst booksMst2 = new BooksMst();
                booksMst2.setId(booksMst.getId());
                booksMst2.setTitle(title);
                booksMst2.setSiteId(new Integer("" + session["siteId"]));
                booksMst2.setDescription(bookDesciption);
                booksMst2.setPublisherId(new Integer(publisherId));
                booksMst2.setCoverImage(coverImage);
                booksMst2.setCreatedBy(springSecurityService.currentUser.username);
                booksMst2.setAuthors(requestBody.Author);
                booksMst2.setBookWeight(bookWeight);
                booksMst2.setIsbn(isbn);
                booksMst2.setLanguage(language);
                booksMst2.setStatus(publishBook ? "published" : null);
                booksMst2.setDatePublished(publishBook ? new Date() : null);
                booksMst2.setBookCode(bookCode);
                booksMst2.setCurrentStock(currentStock != null && !"".equals(currentStock) ? new Integer(currentStock) : null)
                booksMst2.setShowInLibrary("Yes")
                booksMst2.setExternalLink(externalLink)
                booksMst2.wsuser.save(failOnError: true, flush: true)
                BooksMst booksMst3 = new BooksMst();
                booksMst3.setId(booksMst.getId());
                booksMst3.setTitle(title);
                booksMst3.setSiteId(new Integer("" + session["siteId"]));
                booksMst3.setDescription(bookDesciption);
                booksMst3.setPublisherId(new Integer(publisherId));
                booksMst3.setCoverImage(coverImage);
                booksMst3.setCreatedBy(springSecurityService.currentUser.username);
                booksMst3.setAuthors(requestBody.Author);
                booksMst3.setBookWeight(bookWeight);
                booksMst3.setIsbn(isbn);
                booksMst3.setLanguage(language);
                booksMst3.setStatus(publishBook ? "published" : null);
                booksMst3.setDatePublished(publishBook ? new Date() : null);
                booksMst3.setBookCode(bookCode);
                booksMst3.setCurrentStock(currentStock != null && !"".equals(currentStock) ? new Integer(currentStock) : null)
                booksMst3.setShowInLibrary("Yes")
                booksMst3.setExternalLink(externalLink)
                booksMst3.wsshop.save(failOnError: true, flush: true)

                //now see if the pdf thingy has to be uploaded
                if(requestBody.Book_Link!=null&&!"".equals(requestBody.Book_Link)) {
                    bookUploadFromPDF(requestBody, ""+booksMst.id)
                }
                //extract image if it is from google drive
                if (requestBody.Cover_Image != null&&requestBody.Cover_Image.indexOf("drive.google")!=-1) {
                    def fileId = extractFileIdFromGoogleDriveUrl(requestBody.Cover_Image)
                    def fileUrl = "https://drive.google.com/uc?export=download&id=" + fileId
                    downloadCoverImage(booksMst.id, fileUrl)
                }
            }
            status = "OK"
            def response = [status: status, information: information]
            render response as JSON
        }
    }
    @Transactional
    def filenameFix(ResourceDtl documentInstance){
        String rightFileName = ""+documentInstance.id+".pdf"

            //copy the file to the right file name
            Path source = Paths.get(grailsApplication.config.grails.basedir.path + documentInstance.resLink)
            Path destination = Paths.get(grailsApplication.config.grails.basedir.path + documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf("/")+1)+rightFileName)
            Files.copy(source, destination, StandardCopyOption.REPLACE_EXISTING);

            //delete old file
            File file = new File(grailsApplication.config.grails.basedir.path + documentInstance.resLink)
            file.delete()
            //update filename and resLink in the database
            documentInstance.filename = rightFileName
            documentInstance.resLink = documentInstance.resLink.substring(0,documentInstance.resLink.lastIndexOf("/")+1)+rightFileName
            documentInstance.save(flush: true, failOnError: true)
    }
    @Transactional
    def actualSplitPdf(document,ArrayList splitData,String bookId){
        splitData.sort { it.endPageNo }

        // Iterate over the split data
        for (int i = 0; i < splitData.size(); i++) {
            // Define start and end pages
            int startPage = splitData[i].startPageNo
            int endPage = splitData[i].endPageNo

            //create the chapter
            String chapterName = splitData[i].chapterName
            if(chapterName==null||"".equals(chapterName.trim())) chapterName = "Chapter "+(i+1)
            println("the chapter name is "+chapterName)
            ChaptersMst chaptersMst = new ChaptersMst(name: chapterName, bookId: new Long(bookId),sortOrder:null)
            chaptersMst.save(failOnError: true)

            //create resourcedtl
            def resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.chapterId = chaptersMst.id
            resourceDtlInstance.siteId = new Integer("" + session["siteId"])

            String filename = chapterName
            filename = filename.replaceAll("\\s+", "").toLowerCase()+"-noopt.pdf"

            //replace / with - in the filename
            filename = filename.replaceAll("/", "-")
            //replace all special characters with -
            filename = filename.replaceAll("[^a-zA-Z0-9-]", "-")

            resourceDtlInstance.filename = filename
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "Notes"
            resourceDtlInstance.resourceName = chapterName
            resourceDtlInstance.noOfPages = 0
            resourceDtlInstance.resLink = "blank"
            resourceDtlInstance.resSubType ="lesson"
            resourceDtlInstance.zoomLevel = null

            resourceDtlInstance.save(flush: true, failOnError: true)

            def resourceId = resourceDtlInstance.id
            resourceDtlInstance.resLink = "supload/books/" + bookId + "/chapters/" + chaptersMst.id + "/" + resourceId + "/" + resourceDtlInstance.filename

            File uploadDir = new File(grailsApplication.config.grails.basedir.path + "/supload/books/" + bookId + "/chapters/" + chaptersMst.id + "/" + resourceId)
            if(!uploadDir.exists()) uploadDir.mkdirs()
            String outputPath = new File(uploadDir, filename).getAbsolutePath()
            saveDocument(document, startPage - 1, endPage - 1, outputPath) // -1 because the pages are 0-indexed
            resourceDtlInstance.save(flush: true, failOnError: true)
            filenameFix(resourceDtlInstance)

        }

// Close the original document
        document.close()
        dataProviderService.getChaptersList(new Long(bookId))
    }
    @Transactional
    def splitPdf() {
        def bookId = params.bookId
        def pdfFile = request.getFile('pdfFile')
        def excelFile = request.getFile('excelFile')

        if (!bookId || !pdfFile) {
            render status: 400, text: "All parameters (bookId, pdfFile, excelFile) are required."
            return
        }
        def splitData = []
        PDDocument document = PDDocument.load(pdfFile.inputStream)
        //check if excelFile is empty


       if(excelFile&&excelFile.getSize()>0) {
           // Parse Excel file
           def workbook = new XSSFWorkbook(excelFile.inputStream)
           def sheet = workbook.getSheetAt(0)


           def rowIterator = sheet.iterator()
           if (rowIterator.hasNext()) {
               rowIterator.next()
           }


           while (rowIterator.hasNext()) {
               def row = rowIterator.next()

               def chapterNameCell = row.getCell(0)
               def startPageNoCell = row.getCell(1)
               def endPageNoCell = row.getCell(2)
               if (chapterNameCell == null) break
               def chapterName = chapterNameCell.getStringCellValue()
               def startPageNo = startPageNoCell.getNumericCellValue().intValue()
               def endPageNo = endPageNoCell.getNumericCellValue().intValue()

               splitData << [chapterName: chapterName, startPageNo: startPageNo, endPageNo: endPageNo]
           }
       }else {
           if (params.tocPageNos != null && !"".equals(params.tocPageNos)) {
               //save the file first
               String filename = pdfFile.originalFilename
                filename = filename.replaceAll("\\s+", "")
               File uploadDir = new File(grailsApplication.config.grails.basedir.path +"/supload/books/" + params.bookId )
               if(!uploadDir.exists()) {
                   println("dir doesn't exist, so creating")
                   uploadDir.mkdirs()
               }
               String targetFilePath = grailsApplication.config.grails.basedir.path +"/supload/books/" + params.bookId+"/"+filename
               pdfFile.transferTo(new File(grailsApplication.config.grails.basedir.path +"/supload/books/" + params.bookId+"/"+filename))
               println("file transferred")
               URL url = new URL(promptService.getGPTServerUrl(request)+"/extract-toc-by-resource")
               HttpURLConnection connection = (HttpURLConnection) url.openConnection()
               connection.setRequestMethod("POST")
               connection.setRequestProperty("Content-Type", "application/json")
               connection.setDoOutput(true)
               connection.setDoInput(true)
               connection.connect()
               def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))

               def json = new JsonBuilder([s3_path: "supload/books/" + params.bookId+"/"+filename,
                                           toc_page_numbers: params.tocPageNos,
                                           starting_page_number: params.firstChapterPageNo])
               writer.write(json.toString())
               writer.flush()
               writer.close()
               def responseCode = connection.getResponseCode()

               if(responseCode==200){
                   def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
                   def response = reader.readLine()
                   def jsonSlurper = new JsonSlurper()
                   def jsonResponse = jsonSlurper.parseText(response)
                   println("***** the response is "+jsonResponse)
                   def chaptersList = jsonResponse.results
                   println("***** the chaptersList is "+chaptersList)
                   splitData << [chapterName: "Preface", startPageNo: 1, endPageNo: chaptersList[0].actual_start_page - 1]

                   chaptersList.each { chapter ->
                       splitData << [chapterName: chapter.title, startPageNo: chapter.actual_start_page, endPageNo: chapter.actual_end_page]
                   }


               }else{
                   println("extraction failed")
               }
                File targetFile = new File(targetFilePath)
               targetFile.delete()

           }else{
               List<ChapterMark> chapterMarks = []
               if (document.getDocumentCatalog().getDocumentOutline() != null) {
                   PDOutlineItem current = document.getDocumentCatalog().getDocumentOutline().getFirstChild()
                   while (current != null) {
                       handleOutlineItem(document, current, chapterMarks)
                       current = current.getNextSibling()
                   }
               }
               // Process the list of chapters to calculate start and end page numbers.


               int endPageNo
               for (int i = 0; i < chapterMarks.size(); i++) {
                   if (i == chapterMarks.size() - 1) {
                       endPageNo = document.getNumberOfPages()
                   } else {
                       endPageNo = chapterMarks[i + 1].startPage - 1
                   }
                   splitData << [chapterName: chapterMarks[i].name, startPageNo: chapterMarks[i].startPage, endPageNo: endPageNo]
               }
       }
       }

        actualSplitPdf(document,splitData,params.bookId)
        // Sort split data by end page number
        document.close()
        redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: ['bookId': params.bookId])
    }

    private void saveDocument(PDDocument document, int startPage, int endPage, String outputPath) {
        PDDocument newDocument = new PDDocument()
        for (int i = startPage; i <= endPage; i++) {
            newDocument.addPage(document.getPage(i))
        }
        newDocument.save(outputPath)
        newDocument.close()
    }
    def bookInput(){

    }

    List bookUploadFromPDF(requestBody,String bookId){
        String fileURL = requestBody.Book_Link
        //if url is http change it to https
        if(fileURL.indexOf("http://")!=-1) fileURL = fileURL.replace("http://","https://")
        if(fileURL.indexOf("drive.google")!=-1) {
            //extract the file id from the url
            String fileId = extractFileIdFromGoogleDriveUrl(fileURL)
            fileURL = "https://drive.google.com/uc?export=download&id=" + fileId
        }

        def splitData = []
        PDDocument document
        try {
            URL url = new URL(fileURL);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            int responseCode = httpConn.getResponseCode();

            // Check if the request was successful
            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = httpConn.getInputStream();
                document = PDDocument.load(inputStream)
                // Process the list of chapters to calculate start and end page numbers.


                    List<ChapterMark> chapterMarks = []
                    if (document.getDocumentCatalog().getDocumentOutline() != null) {
                        PDOutlineItem current = document.getDocumentCatalog().getDocumentOutline().getFirstChild()
                        while (current != null) {
                            handleOutlineItem(document, current, chapterMarks)
                            current = current.getNextSibling()
                        }
                    }


                    int endPageNo
                    for (int i = 0; i < chapterMarks.size(); i++) {
                        if (i == chapterMarks.size() - 1) {
                            endPageNo = document.getNumberOfPages()
                        } else {
                            endPageNo = chapterMarks[i + 1].startPage - 1
                        }

                        splitData << [chapterName: chapterMarks[i].name, startPageNo: chapterMarks[i].startPage, endPageNo: endPageNo]
                    }
                    //if the split data is empty then populate splitData with start and end page numbers of the full document
                    if (splitData.size() == 0) {
                        splitData << [chapterName: "Full Book", startPageNo: 1, endPageNo: document.getNumberOfPages()]
                    }

                // now call the thingy
                actualSplitPdf(document,splitData,bookId)
                ChaptersMst chaptersMst = ChaptersMst.findByBookId(new Long(""+bookId))
                if(chaptersMst==null) {
                    splitData = []
                    splitData << [chapterName: "Full Book", startPageNo: 1, endPageNo: document.getNumberOfPages()]
                    actualSplitPdf(document,splitData,bookId)
                }
                document.close();
            }
        }catch (Exception e){
            println("**** bookUploadFromPDF Exception is "+e.toString())
            try {
                splitData = []
                splitData << [chapterName: "Full Book", startPageNo: 1, endPageNo: document.getNumberOfPages()]
                actualSplitPdf(document, splitData, bookId)
            }catch(Exception e1){
                println("**** bookUploadFromPDF second level Exception is "+e.toString())
            }
            document.close();
        }
    }
    def bookMetaInfoCreator() {
        def file = request.getFile('pdfFile')
        PDDocument document = PDDocument.load(file.getInputStream())
        XSSFWorkbook workbook = new XSSFWorkbook()
        def sheet = workbook.createSheet("Chapters")

        // Adding column names to the first row.
        Row headerRow = sheet.createRow(0)
        headerRow.createCell(0).setCellValue("ChapterName")
        headerRow.createCell(1).setCellValue("StartingPageNo")
        headerRow.createCell(2).setCellValue("EndPageNo")

        if (document.getDocumentCatalog().getDocumentOutline() != null) {
            PDOutlineItem current = document.getDocumentCatalog().getDocumentOutline().getFirstChild()
            List<ChapterMark> chapterMarks = []
            while (current != null) {
                handleOutlineItem(document, current, chapterMarks)
                current = current.getNextSibling()
            }

            // Process the list of chapters to calculate start and end page numbers.
            for (int i = 0; i < chapterMarks.size(); i++) {
                Row row = sheet.createRow(i+1)
                row.createCell(0).setCellValue(chapterMarks[i].name)
                row.createCell(1).setCellValue(chapterMarks[i].startPage)
                // If this is the last chapter, its end page is the last page of the document.
                if (i == chapterMarks.size() - 1) {
                    row.createCell(2).setCellValue(document.getNumberOfPages())
                } else {
                    row.createCell(2).setCellValue(chapterMarks[i+1].startPage - 1)
                }
            }
        }

        document.close()

        // Creating a byte array output stream to hold the Excel data
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
        workbook.write(outputStream)
        byte[] bytes = outputStream.toByteArray()

        // Sending the data to the client
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        response.setHeader("Content-disposition", "attachment; filename=Chapters.xlsx")
        response.outputStream << bytes
        response.outputStream.flush()
    }

    private void handleOutlineItem(PDDocument document, PDOutlineItem bookmark, List<ChapterMark> chapterMarks) {
        int startPageNumber = findStartPageNumber(document, bookmark)
        String cleanedName = cleanChapterName(bookmark.getTitle())
        chapterMarks.add(new ChapterMark(cleanedName, startPageNumber))


        // If the bookmark has children, process them
        PDOutlineItem child = bookmark.getFirstChild()
      /**  while (child != null) {
            handleOutlineItem(document, child, chapterMarks)
            child = child.getNextSibling()
        }*/
    }

    private int findStartPageNumber(PDDocument document, PDOutlineItem bookmark) {
        PDPageTree allPages = document.getPages()
        PDPage bookmarkPage = bookmark.findDestinationPage(document)
        return allPages.indexOf(bookmarkPage) + 1
    }

    private String cleanChapterName(String rawName) {
        // Removing the leading chapter number with or without the word "Chapter" or "Unit", accounting for "-", "." symbol and variations of spacing.
        String cleanedName = rawName.replaceAll(/^(Chapter|Unit|Section)\s?\s?-?\s?\d+\.?\s*-?\s*/, "")

        // Removing any non-alphanumeric characters, replace them with space.
        cleanedName = cleanedName.replaceAll(/[^a-zA-Z0-9\s]/, " ")

        // Convert to title case if the word is all uppercase
        cleanedName = cleanedName.split(' ').collect { it == it.toUpperCase() ? it.capitalize() : it }.join(' ')

        return cleanedName
    }

    def ipAddressTester(){
        // Step 2: Obtain the user's IP address
        def userIP = params.ipAdress
        if("0:0:0:0:0:0:0:1".equals(userIP)) println("its localhost")
        // Step 3: Fetch the country information based on the IP address
        def database = new File("upload/GeoLite2-Country.mmdb") // Provide the path to the MaxMind GeoIP2 database file
        def databaseReader = new DatabaseReader.Builder(database).build()

        def country
        try {
            def response = databaseReader.tryCountry(InetAddress.getByName(userIP))
            if (response.isPresent()) {
                country = response.get().getCountry()
            }
        } catch (Exception e) {
            log.error("Error retrieving country information: $e.message")
            // Handle the error appropriately
        }


        render "Country code ="+country?.isoCode

    }

    @Transactional
    def test(){



        redisService.hset("yourCacheKey","first", "second")
        redisService.hset("yourCacheKey","third", "fourth")



        render redisService.hget("yourCacheKey","first")
    }



    def formulaFixer(){

    }

    def mathConverter(){
        String contentToBeCleaned = params.inputContent
        contentToBeCleaned = contentToBeCleaned.replace('~', "&")
        contentToBeCleaned = contentToBeCleaned.replace('$', "#")
        if (contentToBeCleaned.indexOf("##") > -1) {
            boolean hasFormula = true
            while (hasFormula) {
                contentToBeCleaned = contentToBeCleaned.replaceFirst("##", "<span class=\"math-tex\">\\\\(")
                contentToBeCleaned = contentToBeCleaned.replaceFirst("##", "\\\\)</span>")
                if (contentToBeCleaned.indexOf('##') == -1) hasFormula = false
            }
        }

        if (contentToBeCleaned.indexOf('#') > -1) {
            boolean hasFormula = true

            while (hasFormula) {
                contentToBeCleaned = contentToBeCleaned.replaceFirst("#", "<span class=\"math-tex\">\\\\(")
                contentToBeCleaned = contentToBeCleaned.replaceFirst("#", "\\\\)</span>")
                if (contentToBeCleaned.indexOf('#') == -1) hasFormula = false
            }
        }
        def json = [convertedContent : contentToBeCleaned ]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])
    def addBulkUsersAndBooks(){

    }

    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER','ROLE_WS_CONTENT_ADMIN','ROLE_LIBRARY_ADMIN','ROLE_INSTITUTE_MANAGER','ROLE_IBOOKGPT_SITE_ADMIN'])
    def addBulkUsersAndBooksProcess(){
        println("Inside addBulkUsersAndBooksProcess");
        def requestBody = request.JSON
        String name  = requestBody.Name
        String emailId  = requestBody.Email_Id
        String mobile  = requestBody.Contact_Number
        String schoolCode  = requestBody.School_Code
        String schoolName  = requestBody.School_Name
        String address  = requestBody.Address
        String product  = requestBody.Product
        String paymentID  = requestBody.Payment_ID
        String sendNotification = requestBody.sendNotification
        String validity  = requestBody.Validity
        String batchId  = requestBody.batchId
        String userType  = requestBody.userType
        String admissionNo  = requestBody.Admission_No
        String selectedBatchId  = requestBody.selectedBatchId
        String inputLoginid = requestBody.Login_Id
        String inputPassword = requestBody.Password
        String password = ""
        boolean checkUserExists = true
        boolean userExists = false
        def information = ""
        int noOfBooksAdded=0
        boolean incorrectEmail = false
        String loginId
        Integer siteId = utilService.getSiteId(request,session)
        User user
        def response
        if(inputLoginid!=null && !"".equals(inputLoginid)) {
            loginId = inputLoginid.trim().replace(' ','')
        }else {
            if (mobile != null && !"".equals(mobile))
                loginId = mobile.trim().replace(' ', '')
            else if (emailId != null && !"".equals(emailId))
                if (isValidEmail(emailId))
                    loginId = emailId.trim().replace(' ', '')
                else {
                    incorrectEmail = true
                    information = "Incorrect Email"
                }
            else {
                loginId = name.replace(' ', '').toLowerCase().trim()
                checkUserExists = false

                //now create add random number to login id
                boolean loginIdExists = true
                String tempLoginId
                while (loginIdExists) {
                    tempLoginId = loginId + (new Random()).nextInt(9999)
                    user = User.findByUsername(siteId + "_" + tempLoginId)
                    if (user == null) {
                        loginId = tempLoginId
                        loginIdExists = false
                    }
                }
            }
        }


        if(checkUserExists){
            user = User.findByUsername(siteId+"_"+loginId)
            if(user!=null){
                user.student="bulkUpload"
                user.school=schoolName
                user.institution = schoolCode
                userExists = true
            }
        }
        if(!userExists&&!incorrectEmail){
            if(inputPassword!=null && !"".equals(inputPassword)) password = inputPassword
            else password = name.replace(' ','').toLowerCase().trim()

            WinGenerator winGenerator = new WinGenerator()
            winGenerator.save(failOnError: true)
            if(address!=null&&address.length()>255) address = address.substring(0,254)
            user = new User(
                    username:siteId+"_"+loginId,
                    name: name,
                    email: emailId,
                    mobile: mobile,
                    password: password,
                    siteId: siteId,
                    win: winGenerator.id,
                    student:"bulkUpload",
                    address1: address,
                    school: schoolName,
                    institution: schoolCode
            )
            user.save(failOnError: true, flush: true)
            //add appropriate roles
            Role role = Role.findByAuthority("ROLE_USER")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_ADD")
            UserRole.create(user, role, true)
            role = Role.findByAuthority("ROLE_CAN_UPLOAD")
            UserRole.create(user, role, true)

        }
        if(user!=null) {
            if (product != null && !"".equals(product)) {
                String[] isbns = product.split(",")
                BooksMst booksMst
                String isbn

                for (int i = 0; i < isbns.length; i++) {
                    isbn = ("" + isbns[i]).trim().replace(' ', '')
                    booksMst = dataProviderService.getBooksMstByIsbnAndPublished(isbn)
                    if(booksMst==null) booksMst = BooksMst.findByBookCode(isbn)
                    if (booksMst==null&&isInteger(isbn)) booksMst = dataProviderService.getBooksMst(new Integer(isbn))
                    if (booksMst != null) {
                        if (BooksPermission.findByUsernameAndBookId(user.username, booksMst.id) == null) {
                            BooksPermission booksPermission = new BooksPermission()
                            booksPermission.bookId = booksMst.id
                            booksPermission.username = user.username
                            booksPermission.addedBy = "System"
                            booksPermission.clientPo = paymentID
                            if(validity!=null&&!"".equals(validity)) {
                                Calendar c = Calendar.getInstance()
                                c.add(Calendar.DATE, new Integer(""+validity))
                                booksPermission.expiryDate = c.getTime()
                            }
                           else{
                                if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                                    Calendar c = Calendar.getInstance()
                                    c.add(Calendar.DATE, booksMst.validityDays)
                                    booksPermission.expiryDate = c.getTime()
                                }
                            }
                            booksPermission.save(failOnError: true, flush: true)
                            noOfBooksAdded++;
                            //code to add package books
                            if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
                        }

                    } else {
                        information += " Incorrect isbn:" + isbn
                    }
                }


            }
            if(batchId!=null&&!"".equals(batchId)){
                String instructor = "Instructor".equals(userType)?"true":null
                instManagerService.addUserToBatch(user.username,userType,admissionNo,new Integer(batchId))
                if(selectedBatchId!=null&&!"".equals(selectedBatchId)){
                    instManagerService.addUserToBatch(user.username,userType,admissionNo,new Integer(selectedBatchId))
                }
            }
            //send email
            if("Yes".equals(sendNotification)) {
                SiteMst sm = dataProviderService.getSiteMst(siteId)
                if (user.email != null && !"".equals(user.email)&&isValidEmail(user.email)) {
                    String mailSubject = "Welcome to ${sm.clientName}"
                    String mailFrom = sm.fromEmail != null ? sm.fromEmail : "Wonderslate <<EMAIL>>"
                    if (siteId.intValue() == 46) mailFrom = "MTG <<EMAIL>>"
                    try {
                        String mailText = "Hello " + user.name + "\n\n Your account has been created. Here are the details.\n\nLogin Id: ${loginId}\nPassword: ${password}\n\nPlease click on ${sm.siteDomainName} to access your account.\n\nThanks\nTeam ${sm.id.intValue()==66?"Wonderslate":sm.clientName} "
                        if("".equals(password.trim())) mailText = "Hello " + user.name + "\n\n Your account has been created. Here are the details.\n\nLogin Id: ${loginId}\n\nPlease click on ${sm.siteDomainName} to access your account.\n\nThanks\nTeam ${sm.id.intValue()==66?"Wonderslate":sm.clientName} "
                        mailService.sendMail {
                            async true
                            to user.email
                            from mailFrom
                            subject mailSubject
                            text mailText
                        }

                    } catch (Exception e) {
                        println("Exception in sending welcome <NAME_EMAIL> and exception is " + e.toString())
                    }
                } else if (user.mobile != null && !"".equals(user.mobile)) {
                    try {

                        RestBuilder rest = new RestBuilder()
                        String message = " Dear ${user.name}, Your account is created. Click on the below link ${sm.siteDomainName} and login using ${loginId} and Password: ${password}."
                        if("".equals(password.trim())) message = "Dear ${user.name}, Your account is created. Click on the below link ${sm.siteDomainName} and login using ${loginId}."
                        if (sm.smsUrl != null && sm.smsUrl != "") {
                            rest.post(sm.smsUrl + mobile + '&' + sm.smsUrl1 + '=' + message)
                        }

                    } catch (Exception e) {
                        println("Exception sending sms to "+mobile)
                    }
                }
            }

            response = [status: "OK", information: information, additionalInformation: [loginId: loginId, password: password, BooksAdded: noOfBooksAdded]]
        }else{
            response = [status: "Failed", information: information, additionalInformation: [loginId: "", password: "", BooksAdded: 0]]
        }
        render response as JSON



    }

    static boolean isValidEmail(String email) {
        // Regular expression pattern for a valid email
        def regex = '^[A-Za-z0-9+_.-]+@(.+)$'

        return email =~ regex
    }

    static boolean isInteger(String input) {
        try {
            Integer.parseInt(input)
            return true
        } catch (NumberFormatException e) {
            return false
        }
    }

    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])
    def imageFixer(){

    }

    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])
    def imageFixerProcess(){
        def requestBody = request.JSON
        String bookId  = requestBody.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Integer(bookId))
        def response
        if(booksMst!=null){
            File uploadDir = new File("supload/books/" + bookId)
            if (!uploadDir.exists()) uploadDir.mkdirs()

            //creating directory to process images
            File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
            if (!uploadDir1.exists()) uploadDir1.mkdirs()
            //saving original image finally
            //   file.transferTo(new File(uploadDir.absolutePath + "/" + filename))
            boolean imageExists = false
            String filename = booksMst.coverImage
            String webPImage=filename.substring(0, filename.indexOf("."))
            File webpFile = new File("supload/books/"+booksMst.id+"/processed/"+webPImage+".webp")
            if(webpFile.exists()) {
                imageExists = true
            }
            else {
                File originalFile = new File("supload/books/"+booksMst.id+"/processed/"+booksMst.coverImage)
                if(originalFile.exists())
                {
                    ImageIO.write(ImageIO.read(originalFile), "webp", new File("supload/books/"+booksMst.id+"/processed/"+webPImage+".webp"))
                    imageExists = true
                };
            }
            if(imageExists) {
                String status="normal"
                filename = webPImage + ".webp"
                def image = ImageIO.read(webpFile)
                ByteArrayOutputStream baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 150, 150, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                baos.flush()
                byte[] scaledImageInByte = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image, Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 300, 300, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".") + 1), baos)
                baos.flush()
                byte[] scaledImageInByte1 = baos.toByteArray()
                baos.close()



                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf("."))), scaledImageInByte1)
                //check to see if the size of the generated file is greater than the source file

                long sourceFileLength = webpFile.length()
                File passportFile = new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf(".")))
                long passportFileLength = passportFile.length()

                if(passportFileLength>sourceFileLength){
                    status = "passport size bigger"
                    FileInputStream inputStream = new FileInputStream(webpFile);
                    FileOutputStream outputStream = new FileOutputStream(passportFile);

                    byte[] buffer = new byte[1024];
                    int bytesRead;

                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }

                    // Close the input and output streams
                    inputStream.close();
                    outputStream.close();
                }

                BooksMst.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + bookId)
                BooksMst.wsuser.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + bookId)
                BooksMst.wsshop.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + bookId)

                dataProviderService.getBooksMst(booksMst.id)
                response = [status: "OK", information: status, additionalInformation: [bookId: bookId]]
            }
            else{
                response = [status: "Failed", information: "Image not found", additionalInformation: [bookId: bookId]]
            }
        }else{

            response = [status: "Failed", information: "Book not found", additionalInformation: [bookId:bookId]]
        }
        render response as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def uploadMCQ()
    {

    }

    @Transactional @Secured(['ROLE_USER'])
    def uploadQA()
    {

    }
    @Transactional
    def processMCQ(){

        def requestBody = request.JSON
        print("Request body\n"+requestBody)
        String chapterId  = requestBody.chapterId
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Integer(chapterId))
        String bookId = ""+chaptersMst.bookId
        String resId = requestBody.resId
        def resourceDtlInstance
        String status="Resource Id not found"
        Integer siteId =utilService.getSiteId(request,session);
        try {
            if("-1".equals(resId)){
                //first question from the file. Create resource dtl
                QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                quizIdGenerator.save()
                resourceDtlInstance = new ResourceDtl()
                resourceDtlInstance.resLink = quizIdGenerator.id
                resourceDtlInstance.createdBy = springSecurityService.currentUser ? springSecurityService.currentUser.username : requestBody.username
                resourceDtlInstance.resType = "Multiple Choice Questions"
                resourceDtlInstance.chapterId = new Integer(chapterId)
                resourceDtlInstance.resourceName = "Quiz"
                resourceDtlInstance.siteId = utilService.getSiteId(request,session)
                resourceDtlInstance.save(failOnError: true, flush: true)
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)

            }else{
                resourceDtlInstance = ResourceDtl.findById(new Integer(resId))
            }
            if(resourceDtlInstance!=null){
                String question = quizExtractorService.extractImage(requestBody.Question,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)

                String option1 = quizExtractorService.extractImage(requestBody.option1,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
                String option2 = quizExtractorService.extractImage(requestBody.option2,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
                String option3 = quizExtractorService.extractImage(requestBody.option3,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
                String option4 = quizExtractorService.extractImage(requestBody.option4,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
                String option5 = quizExtractorService.extractImage(requestBody.option5,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
                String correctAnswer = requestBody.correctAnswer != null ? requestBody.correctAnswer.trim() : ""
                String answerDescription = quizExtractorService.extractImage(requestBody.answerDescription,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
                Double marks = requestBody.marks!=null?new Double(requestBody.marks.trim()):null
                Double negativeMarks = requestBody.negativeMarks!=null?new Double(requestBody.negativeMarks.trim()):null
                Integer directionId = requestBody.directionId!=null?new Integer(requestBody.directionId):null

                String answer1=null,answer2=null,answer3=null,answer4=null,answer5=null
                if("a".equals(correctAnswer.toLowerCase())||"1".equals(correctAnswer)||"A".equals(correctAnswer)) answer1="Yes"
                if("b".equals(correctAnswer.toLowerCase())||"2".equals(correctAnswer)||"B".equals(correctAnswer)) answer2="Yes"
                if("c".equals(correctAnswer.toLowerCase())||"3".equals(correctAnswer)||"C".equals(correctAnswer)) answer3="Yes"
                if("d".equals(correctAnswer.toLowerCase())||"4".equals(correctAnswer)||"D".equals(correctAnswer)) answer4="Yes"
                if("e".equals(correctAnswer.toLowerCase())||"5".equals(correctAnswer)||"E".equals(correctAnswer)) answer5="Yes"
                if(requestBody.question_images && requestBody.question_images.size()>0) question = quizExtractorService.appendImgLinks(question, requestBody.question_images)
                if(requestBody.option_images && requestBody.option_images.option_1 && requestBody.option_images.option_1.size()>0) option1 = quizExtractorService.appendImgLinks(option1, requestBody.option_images.option_1)
                if(requestBody.option_images && requestBody.option_images.option_2 && requestBody.option_images.option_2.size()>0) option2 = quizExtractorService.appendImgLinks(option2, requestBody.option_images.option_2)
                if(requestBody.option_images && requestBody.option_images.option_3 && requestBody.option_images.option_3.size()>0) option3 = quizExtractorService.appendImgLinks(option3, requestBody.option_images.option_3)
                if(requestBody.option_images && requestBody.option_images.option_4 && requestBody.option_images.option_4.size()>0) option4 = quizExtractorService.appendImgLinks(option4, requestBody.option_images.option_4)
                if(requestBody.option_images && requestBody.option_images.option_5 && requestBody.option_images.option_5.size()>0) option5 = quizExtractorService.appendImgLinks(option5, requestBody.option_images.option_5)
                if(requestBody.explanation_images && requestBody.explanation_images.size()>0) answerDescription = quizExtractorService.appendImgLinks(answerDescription, requestBody.explanation_images)

                ObjectiveMst om = new ObjectiveMst(quizId: new Integer(resourceDtlInstance.resLink), quizType: "Multiple Choice Questions", question: question,
                        option1: option1, option2: option2, option3: option3, option4: option4, option5: option5,
                        answer1:answer1, answer2: answer2, answer3: answer3, answer4: answer4, answer5: answer5,
                        answerDescription: answerDescription, difficultylevel: "Medium", marks: marks,negativeMarks: negativeMarks, isValidAnswerKey: requestBody.isValidAnswerKey, directionId: directionId)
                        .save(failOnError: true, flush: true)
                status = "Added"

                if (requestBody.directions && requestBody.directions !=null &&  requestBody.directions !="") {
                    DirectionsMst  directionsMst = new DirectionsMst(directions: requestBody.directions)
                    directionsMst.save(failOnError: true, flush: true)
                    om.directionId = directionsMst.id
                }
                om.save(failOnError: true , flush: true)

                def response  = [status: "OK", information: status, additionalInformation: [resId: resourceDtlInstance.id]]
                render response as JSON
            }else{
                def response  = [status: "Failed", information: status, additionalInformation: []]
                render response as JSON
            }
        }catch (Exception e){
            println(e)
            def response  = [status: "Failed", information: status, additionalInformation: [], message:e.message]
            render response as JSON
        }

    }

    @Transactional
    def processDirections(){

        def requestBody = request.JSON


                DirectionsMst  directionsMst = new DirectionsMst(directions: requestBody.directions)
                directionsMst.save(failOnError: true, flush: true)


        def response  = [status: "OK", directionId: directionsMst.id]
            render response as JSON

    }

    @Transactional @Secured(['ROLE_USER'])
    def processQA(){
        def requestBody = request.JSON
        String chapterId  = requestBody.chapterId
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Integer(chapterId))
        String bookId = ""+chaptersMst.bookId
        String resId = requestBody.resId
        def resourceDtlInstance
        String status="Resource Id not found"
        if("-1".equals(resId)){
            //first question from the file. Create resource dtl
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()
            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.resLink = quizIdGenerator.id
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = "QA"
            resourceDtlInstance.chapterId = new Integer(chapterId)
            resourceDtlInstance.resourceName = "Question and Answers"
            resourceDtlInstance.siteId = utilService.getSiteId(request,session)
            resourceDtlInstance.save(failOnError: true, flush: true)
            dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)

        }else{
            resourceDtlInstance = ResourceDtl.findById(new Integer(resId))
        }
        if(resourceDtlInstance!=null){
            String question = quizExtractorService.extractImage(requestBody.Question,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
            String answer = quizExtractorService.extractImage(""+requestBody.Answer,bookId,chapterId,""+resourceDtlInstance.id,requestBody.ignoreFormula)
            Double marks = requestBody.marks!=null?new Double(requestBody.Marks.trim()):null

            ObjectiveMst om = new ObjectiveMst(quizId: new Integer(resourceDtlInstance.resLink), quizType: "QA", question: question,
                    answer: answer, marks: marks)
                    .save(failOnError: true, flush: true)
             status = "Added"

            def response  = [status: "OK", information: status, additionalInformation: [resId: resourceDtlInstance.id]]
            render response as JSON
        }else{
            def response  = [status: "Failed", information: status, additionalInformation: []]
            render response as JSON
        }
    }

    @Transactional @Secured(['ROLE_USER'])
    def extractPDF(){

    }

    @Transactional @Secured(['ROLE_USER'])
    def quizExtractor(){

    }


    String formatText(String text){
        if(text!=null&&!"".equals(text)&&text.length()>5) {
            text=text.trim()
            String regex = "([IVXLCDM0-9]+)\\.(\\s|\$)";

            String firstPart = text.substring(0,text.length()-1)
            String secondPart = text.substring(text.length()-1)


            // Create a pattern and a matcher
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(firstPart);

            // Use the matcher to replace the matched substrings
            firstPart = matcher.replaceAll("<br>\$1. ");
            text = firstPart+secondPart
        }
        return text;
    }
    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def wordUpload() {
        // Render the upload form GSP
        [title:'MCQs from Word']
    }
    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def upload() {
        def file = request.getFile('file')
        String filename=file.originalFilename
        filename=filename.replaceAll("\\s+","")
        String chapterId = params.chapterId
        String bookId = params.bookId
        String uploadParentDir="supload"
        ChaptersMst chaptersMst = null
        BooksMst booksMst
        if(params.bookId!=null&&!"".equals(params.bookId)){
            booksMst = dataProviderService.getBooksMst(new Integer(params.bookId))
            if(!"Yes".equals(booksMst.newStorage)) uploadParentDir = "supload"
        }

        // Perform your logic to store the file data, for example, save it to the filesystem
        // You can replace this with your preferred storage method like database, AWS S3, etc.
        // For simplicity, let's just save it to the 'uploads' directory in the project root
       QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
        quizIdGenerator.save()
        ResourceDtl resourceDtlInstance = new ResourceDtl()
        resourceDtlInstance.resLink = quizIdGenerator.id
        resourceDtlInstance.createdBy = springSecurityService.currentUser.username
        resourceDtlInstance.resType = "Multiple Choice Questions"
        resourceDtlInstance.chapterId = new Integer(chapterId)
        resourceDtlInstance.resourceName = params.quizName!=null&&!"".equals(params.quizName)?params.quizName:"Quiz"
        resourceDtlInstance.siteId = utilService.getSiteId(request,session)
        resourceDtlInstance.save(failOnError: true, flush: true)

        //if the book as ai counterpart add this to that also.
        if(resourceDtlInstance.chapterId!=null){

            if(booksMst.vendor!=null){
                //check the value in booksMst.vendor is number
                try {
                   Long aiBookId = new Long(""+booksMst.vendor.trim())
                    chaptersMst = dataProviderService.getChaptersMst(new Integer(chapterId))
                    ChaptersMst chaptersMst1 = new ChaptersMst(name: chaptersMst.name, bookId: aiBookId,sortOrder:chaptersMst.sortOrder )
                    chaptersMst1.save(failOnError: true,flush: true)
                   def readingMaterial = new ResourceDtl(chapterId: chaptersMst1.id, resType: "Notes",filename: "dummy.pdf",resLink: "supload/dummy.pdf",resourceName: chaptersMst1.name,
                            createdBy: springSecurityService.currentUser.username,siteId: booksMst.siteId)
                    readingMaterial.save(flush:true,failOnError:true)

                    //create another instance of resourceDtl
                    ResourceDtl resourceDtlInstance1 = new ResourceDtl(resLink: resourceDtlInstance.resLink, createdBy: resourceDtlInstance.createdBy,
                            resType: resourceDtlInstance.resType, chapterId: chaptersMst1.id, resourceName: resourceDtlInstance.resourceName,
                             siteId: resourceDtlInstance.siteId, gptResourceType: "mcq",
                            vectorStored: resourceDtlInstance.vectorStored)
                    resourceDtlInstance1.save(failOnError: true,flush: true)
                    dataProviderService.getChaptersList(aiBookId)
                    def gptDefaultCreateLog = new GptDefaultCreateLog(readingMaterialResId: readingMaterial.id,promptType: "mcq",promptLabel: "Create MCQs (Multiple Choice Questions)",
                            prompt: "MCQ",response: "MCQ",username: springSecurityService.currentUser.username,resId: resourceDtlInstance1.id)
                    gptDefaultCreateLog.save(flush:true,failOnError:true)

                } catch (Exception e) {
                    println("**** entered into the catch block "+e.toString())
                    //dont do anything
                }
            }
        }
        dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)

        def fileBytes = file.getBytes()
        def content = ""
        def imageCounter = 1

        // Extract text content and images using Apache POI
        XWPFDocument doc = new XWPFDocument(new ByteArrayInputStream(fileBytes))
        String tempImageFolder= "pdf" + (int)(Math.random()*((9999-0000)+1))
        String uploadPath  = grailsApplication.config.grails.basedir.path+"/"+uploadParentDir+"/books/"+bookId+"/chapters/"+chapterId+"/"+resourceDtlInstance.id+"/extract/OEBPS/Images/"
        def dbSavePath =uploadParentDir+"/books/"+bookId+"/chapters/"+chapterId+"/"+resourceDtlInstance.id+"/extract/OEBPS/Images/"
        String cdnPath = ""+grailsApplication.config.grails.cdn.path
        File uploadDir = new File(uploadPath)
        if(!uploadDir.exists()) uploadDir.mkdirs()
        // Iterate over all paragraphs and runs in the document
        doc.paragraphs.each { paragraph ->

            paragraph.runs.each { run ->
               // Check if the run contains an embedded picture
                if (!run.embeddedPictures.isEmpty()) {
                    run.embeddedPictures.each { picture ->
                        def pictureData = picture.pictureData
                        def imageBytes = pictureData.data
                        def imageExtension = pictureData.suggestFileExtension()
                        def imageName = "image_${imageCounter}.${imageExtension}"
                        try {
                            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
                            int width = bufferedImage.getWidth();
                            int height = bufferedImage.getHeight();

                            saveImage(imageBytes, uploadPath, imageName)
                            if("localhost".equals(cdnPath))
                            content += "<img  src='/funlearn/downloadEpubImage?source=" + dbSavePath + imageName + "' height='" + height + "' width='" + width + "'/>"
                            else content += "<img  src='"+cdnPath+"/" + dbSavePath + imageName + "' height='" + height + "' width='" + width + "'/>"

                            imageCounter++
                        }catch (Exception e){
                            println("Exception happened in reading the image")
                        }
                    }
                } else {

                    // Extract text content from the run
                    if ("superscript".equals(""+run.getVerticalAlignment())) {
                        content += "<sup>"+run.getText(0)+"</sup>"
                    }
                    else if ("subscript".equals(""+run.getVerticalAlignment())) {
                        content += "<sub>"+run.getText(0)+"</sub>"
                    }
                    else if(run.isBold()){
                        content += "<b>"+run.getText(0)+"</b>"
                    }
                    else content += run.getText(0)


                }
            }
            // Add a newline after each paragraph
            content += "<br>"
        }

        [extractedContent:content,resId:resourceDtlInstance.id,title:'MCQs from Word',startQuestionNo:params.startQuestionNo,endQuestionNo:params.endQuestionNo]
    }

    @Secured(['ROLE_BOOK_CREATOR']) @Transactional
    def extractContentAndImages(byte[] fileBytes, def bookId,def chapterId,def resId) {
        def content = ""
        def imageCounter = 1

        // Extract text content and images using Apache POI
        XWPFDocument doc = new XWPFDocument(new ByteArrayInputStream(fileBytes))
        String tempImageFolder= "pdf" + (int)(Math.random()*((9999-0000)+1))
        String uploadPath  = grailsApplication.config.grails.basedir.path+"/supload/books/"+bookId+"/chapters/"+chapterId+"/"+resId+"/extract/OEBPS/Images/"
        def dbSavePath ="supload/books/"+bookId+"/chapters/"+chapterId+"/"+resId+"/extract/OEBPS/Images/"

        File uploadDir = new File(uploadPath)
        if(!uploadDir.exists()) uploadDir.mkdirs()
        // Iterate over all paragraphs and runs in the document
        doc.paragraphs.each { paragraph ->
            paragraph.runs.each { run ->
                // Check if the run contains an embedded picture
                if (!run.embeddedPictures.isEmpty()) {
                    run.embeddedPictures.each { picture ->
                        def pictureData = picture.pictureData
                        def imageBytes = pictureData.data
                        def imageExtension = pictureData.suggestFileExtension()
                        def imageName = "image_${imageCounter}.${imageExtension}"
                        saveImage(imageBytes, uploadPath, imageName)
                        content += "<img  src='/funlearn/downloadEpubImage?source=" + dbSavePath +imageName+ "' height='40'/>"
                        imageCounter++
                    }
                } else {
                    // Extract text content from the run
                    content += run.getText(0)
                }
            }
            // Add a newline after each paragraph
            content += "\n"
        }

        return content
    }
    private void saveImage(byte[] imageData, String outputDirectory, String imageName) {
        def outputFilePath = "${outputDirectory}/${imageName}"
        def imageFile = new File(outputFilePath)
        imageFile.withOutputStream { outputStream ->
            outputStream.write(imageData)
        }
    }

    @Transactional
    def uploadSplitEpubRemote(requestBody) {
        String fileUrl = requestBody.Book_Link
        String fileId = extractFileIdFromGoogleDriveUrl(fileUrl)

        fileUrl = "https://drive.google.com/uc?export=download&id="+fileId

        BooksMst booksMst
        if(requestBody.Book_Id!=null)
            booksMst = dataProviderService.getBooksMst(new Long(""+requestBody.Book_Id))
       else  booksMst = dataProviderService.getBooksMstByIsbn(""+requestBody.ISBN)
        if(booksMst!=null) {
           ChaptersMst chaptersMst = ChaptersMst.findByBookId(booksMst.id)

            if(chaptersMst==null) {
                params.put("bookId", "" + booksMst.id)
                File uploadDir = new File("supload/epub/" + booksMst.id)
                if (!uploadDir.exists()) {
                    uploadDir.mkdirs()
                }

                try {

                    String filename = fileId + ".epub"

                    String savePath = uploadDir.absolutePath + "/" + filename
                    File originalFile = new File(uploadDir.absolutePath + "/" + filename)

                    // Save the downloaded file to the designated directory
                    downloadFile(fileUrl, savePath)
                    // Existing logic to handle the uploaded file
                    resourceCreatorService.extractEpubContents(originalFile, uploadDir, params)
                    dataProviderService.getChaptersList(booksMst.id)
                    dataProviderService.refreshCacheForPublishUnpublish("" + booksMst.id, utilService.getSiteId(request, session))

                    //cover image
                    if (requestBody.CoverImage != null) {
                        fileId = extractFileIdFromGoogleDriveUrl(requestBody.CoverImage)
                        fileUrl = "https://drive.google.com/uc?export=download&id=" + fileId
                        downloadCoverImage(booksMst.id, fileUrl)
                        //update template
                        String level = "College"
                        String syllabus = "Computer Engineering"
                        String grade = "All Semester"
                        String subject = "Software Development"

                        if (level != null && !"".equals(level) && syllabus != null && !"".equals(syllabus) && grade != null && !"".equals(grade) && subject != null && !"".equals(subject)) {
                            BooksTagDtl booksTagDtl = new BooksTagDtl(bookId: booksMst.id, level: level, syllabus: syllabus, grade: grade, subject: subject)
                            booksTagDtl.save(failOnError: true, flush: true)
                            BooksTagDtl booksTagDtl1 = new BooksTagDtl(bookId: booksMst.id, level: level, syllabus: syllabus, grade: grade, subject: subject)
                            booksTagDtl1.wsshop.save(failOnError: true, flush: true)
                        }
                    }

                    return booksMst.id

                } catch (Exception e) {
                    println("Error splitting EPUB: ${e.message}")
                    render "Error uploading EPUB"
                }
            }else{
                return -9
            }
        }else{
                FailedIsbns failedIsbns = new FailedIsbns(isbn:""+requestBody.ISBN)
                failedIsbns.save(failOnError: true, flush: true)
                return -1
        }
    }

    String extractFileIdFromGoogleDriveUrl(String url) {
       String downloadId = url.substring(url.indexOf("/d/")+3,url.indexOf("/view"))
        return downloadId
    }

    def downloadFile(fileURL,saveDir)  {
        try {
            URL url = new URL(fileURL);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            int responseCode = httpConn.getResponseCode();

            // Check if the request was successful
            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = httpConn.getInputStream();
                String disposition = httpConn.getHeaderField("Content-Disposition");
                String fileName = "";

                if (disposition != null) {
                    // Extracts file name from header field
                    int index = disposition.indexOf("filename=");
                    if (index > 0) {
                        fileName = disposition.substring(index + 10, disposition.length() - 1);
                    }
                } else {
                    // Extracts file name from URL
                    fileName = fileURL.substring(fileURL.lastIndexOf("/") + 1);
                }

                // Open output stream to save file
                FileOutputStream outputStream = new FileOutputStream(saveDir);

                int bytesRead = -1;
                byte[] buffer = new byte[4096];
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                outputStream.close();
                inputStream.close();

                System.out.println("File downloaded: " + fileName);
            } else {
                System.out.println("No file to download. Server replied HTTP code: " + responseCode);
            }
            httpConn.disconnect();
        }catch (Exception e){
            println("the exception is "+e.toString())
        }
         return
    }

    def downloadCoverImage(bookId,String fileURL){
        String uploadParentDir="supload"

        File uploadDir = new File(uploadParentDir+"/books/" + bookId)
        if (!uploadDir.exists()) uploadDir.mkdirs()

        //creating directory to process images
        File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
        if (!uploadDir1.exists()) uploadDir1.mkdirs()

        try{
            URL url = new URL(fileURL);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            int responseCode = httpConn.getResponseCode();

            // Check if the request was successful
            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inputStream = httpConn.getInputStream();

                BufferedImage image = ImageIO.read(inputStream)
                String webPImage="coverImage"
                ImageIO.write(image, "webp", new File(uploadParentDir+"/books/" + bookId+"/processed/"+webPImage+".webp"));
                inputStream.close();
                File webpFile = new File(uploadParentDir+"/books/"+bookId+"/processed/"+webPImage+".webp")
                String filename = webPImage+".webp"
                image = ImageIO.read(webpFile)
                ByteArrayOutputStream baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 150, 150, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 300, 300, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte1 = baos.toByteArray()
                baos.close()

                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_thumbnail' + filename.substring(filename.indexOf("."))), scaledImageInByte)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf("."))), scaledImageInByte1)

//check to see if the size of the generated file is greater than the source file

                long sourceFileLength = webpFile.length()
                File passportFile = new File(uploadDir1.absolutePath + "/" + filename.substring(0, filename.indexOf(".")) + '_passport' + filename.substring(filename.indexOf(".")))
                long passportFileLength = passportFile.length()

                if(passportFileLength>sourceFileLength){
                    FileInputStream inputStream1 = new FileInputStream(webpFile);
                    FileOutputStream outputStream = new FileOutputStream(passportFile);

                    byte[] buffer = new byte[1024];
                    int bytesRead;

                    while ((bytesRead = inputStream1.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }

                    // Close the input and output streams
                    inputStream.close();
                    outputStream.close();
                }
                BooksMst.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + bookId)
                BooksMst.wsuser.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + bookId)
                BooksMst.wsshop.executeUpdate("update BooksMst set coverImage='" + filename + "' where id=" + bookId)


                //  System.out.println("File downloaded: " + fileName);
            } else {
                System.out.println("No file to download. Server replied HTTP code: " + responseCode);
            }
            httpConn.disconnect();
        }catch(Exception e){
            println("exception in getting cover image "+e.toString())
        }
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def exportmcqs() {
        File tempDir = Files.createTempDirectory("chapter_exports").toFile()
        try {

            def request = request.JSON
            if (!redisService.get("chapters_" + request.bookId)) {
                dataProviderService.getChaptersList(request.bookId as Long)
            }
            def bookMst = dataProviderService.getBooksMst(new Long(request.bookId))

            List chaptersList = request.chapterIds
            def excelFiles = []

            chaptersList.each { chapter ->
                def mcqsArray = []

                List headers = ["QUESTION", "OPTION 1", "OPTION 2", "OPTION 3", "OPTION 4", "CORRECT ANSWER",  "ANSWER DESCRIPTION"]
                List withProperties = ["question", "option1", "option2", "option3", "option4", "correctAnswer", "answerDescription"]

                def resourceDtl = ResourceDtl.findAllByChapterIdAndResourceName(chapter.id as Long, "GPT MCQ")

                resourceDtl.each { resource ->
                    def mcqs = ObjectiveMst.findAllByQuizId(resource.resLink as Integer)
                    mcqs.each { mcq ->
                        def correctAnswer = ""

                        if (mcq.answer1 == "Yes") correctAnswer = mcq.option1
                        else if (mcq.answer2 == "Yes") correctAnswer = mcq.option2
                        else if (mcq.answer3 == "Yes") correctAnswer = mcq.option3
                        else if (mcq.answer4 == "Yes") correctAnswer = mcq.option4

                        mcqsArray << [
                                question          : mcq.question,
                                option1           : mcq.option1,
                                option2           : mcq.option2,
                                option3           : mcq.option3,
                                option4           : mcq.option4,
                                correctAnswer     : correctAnswer,
                                answerDescription : mcq.answerDescription
                        ]
                    }
                }

                if (!mcqsArray.isEmpty()) {
                    def exporter = new WebXlsxExporter()
                    exporter.with {
                        fillHeader(headers)
                        add(mcqsArray, withProperties)
                    }

                    File excelFile = new File(tempDir, "${chapter.name} MCQs.xlsx")
                    exporter.save(excelFile.newOutputStream())
                    excelFiles << excelFile
                }
            }

            File zipFile = new File(tempDir, "${bookMst.title} MCQs.zip")
            zipFile.withOutputStream { fos ->
                ZipOutputStream zos = new ZipOutputStream(fos)
                excelFiles.each { file ->
                    zos.putNextEntry(new ZipEntry(file.name))
                    file.withInputStream { fis ->
                        zos << fis
                    }
                    zos.closeEntry()
                }
                zos.close()
            }

            response.setContentType("application/zip")
            response.setHeader("Content-Disposition", "attachment; filename=${bookMst.title} MCQs.zip")
            zipFile.withInputStream { response.outputStream << it }

        } catch (Exception e) {
            println("Exception while exporting MCQs: ${e.message}")
            return [error: true, status: 500]
        } finally {
            println("deleting temp dir")
            tempDir?.deleteDir()
        }
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def exportmcqspage(){
        if (!redisService.get("chapters_" + params.bookId)) {
            dataProviderService.getChaptersList(params.bookId as Long)
        }
        [chaptersList: redisService.get("chapters_" + params.bookId)]
    }

}
