package com.wonderslate.data

import com.ccavenue.security.AesCryptUtil
import com.wonderslate.cache.DataProviderService
import com.wonderslate.drive.StudyMaterial
import com.wonderslate.drive.StudyMaterialService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteUserDtl
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.GptLog
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.logs.LogsService
import com.wonderslate.publish.BooksPermission
import com.wonderslate.publish.Publishers
import com.wonderslate.shop.BookPriceService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonBuilder
import groovy.json.JsonSlurper
import org.apache.commons.io.FileUtils
import org.springframework.web.multipart.MultipartHttpServletRequest
import org.springframework.web.multipart.MultipartFile
import com.wonderslate.shop.BookPriceDtl
import javax.servlet.http.HttpServletResponse
import java.util.Base64
import java.util.UUID


class PromptController {
    UserManagementService userManagementService
    DataProviderService dataProviderService
    UtilService utilService
    PromptService promptService
    BookPriceService bookPriceService
    def springSecurityService
    def redisService
    GptLogService gptLogService
    LogsService logsService
    AsyncLogsService asyncLogsService
    StudyMaterialService studyMaterialService

    def index() {
        redirect(action: "list")
    }

    def list() {
       def prompts = Prompts.list()
        List parentPrompts = prompts.findAll { it.parentPromptType == null }
        [prompts: prompts,title:"Prompt Manager",parentPrompts:parentPrompts]
    }


    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def save() {
        if(params.parentPromptType=="") params.parentPromptType = null
        def prompt = new Prompts(params)
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile icon = multiRequest.getFile("icon");
        prompt.iconPath = icon.originalFilename
        prompt.save(flush: true)
        if(icon != null) {
            def iconFileName = saveIconImage(icon,"upload/promptIcons/"+prompt.id)
            if(iconFileName!=""){
                prompt.iconPath = iconFileName
            }
        }
        redirect(action: "list")
    }

    def saveIconImage(MultipartFile file,String path){
        if(!file.empty){
            File uploadDir = new File(path)
            if(!uploadDir.exists()) uploadDir.mkdirs()
            String iconName = file.originalFilename.replaceAll("\\s+", "")
            try{
                file.transferTo(new File(uploadDir.absolutePath + "/" + iconName))
                return file.originalFilename
            }catch(Exception e){
                println "Uploading icon image is failed " + e.toString()
            }
        }else {
            return ""
        }
    }

    def showPromptIcon(Integer promptId,String fileName) {
        try {
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                def file
                file = new File("upload/promptIcons/"+promptId+"/"+fileName)
                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }
                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        } catch (Exception e) {
            println("Exception in prompt icon " + e.toString())
            render "";
        }
    }
    def edit() {
        def prompt = Prompts.get(params.id)
        if (!prompt) {
            flash.message = "Prompt not found"
            redirect(action: "list")
            return
        }
        [prompt: prompt]
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def update() {
      def prompt = Prompts.get(params.id)
        if (!prompt) {
            flash.message = "Prompt not found"
            redirect(action: "list")
            return
        }
        prompt.properties = params
        if (prompt.save(flush: true)) {
           render "Prompt updated successfully"
        } else {
           render "Error updating prompt"
        }

    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def delete() {
        def prompt = Prompts.get(params.id)
        if (prompt) {
            prompt.delete(flush: true)
            flash.message = "Prompt deleted successfully"
        } else {
            flash.message = "Error deleting prompt"
        }
        redirect(action: "list")
    }

    def getChapterGPTResources(){
        List  resources = ResourceDtl.findAllByChapterIdAndGptResourceTypeIsNotNull(new Integer(params.chapterId), [sort: "id", order: "asc"])
        List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId),"Notes", [sort: "id", order: "asc"])
        def json = [resources:resources,readingMaterials:readingMaterials]
        render json as JSON
    }

    @Transactional
    def createFlashCard(){
        ResourceDtl resourceDtl = promptService.createFlashCard(request)
        def json = [status:"OK",resId:resourceDtl.id]
        render json as JSON
    }

    @Transactional
    def createQAResource(){
        ResourceDtl resourceDtl = promptService.createQAResource(request,session)
        def json = [status:"OK",resId:resourceDtl.id]
        render json as JSON
    }

    @Transactional
    def createMCQResource(){
        def requestBody = request.JSON
        def parsedJson = new JsonSlurper().parseText(requestBody.answer)
         ResourceDtl resourceDtl = promptService.createMCQResource(request,session,parsedJson.questions,requestBody)
        def json = [status:"OK",resId:resourceDtl.id]
        render json as JSON
    }
    @Transactional
    def createGPTResource(){
        def resId
        def requestBody = request.JSON
        def parsedJson = new JsonSlurper().parseText(requestBody.answer)

        if("mcqs".equals(requestBody.promptType) || "mcq".equals(requestBody.promptType)) {
              resId = promptService.createMCQResource(request, session,parsedJson.questions,requestBody).id
        }else if("qna".equals(requestBody.promptType)||"pns".equals(requestBody.promptType)){
            resId = promptService.createQAResource(request, session,parsedJson.qna,requestBody).id
        }else if("flashcards".equals(requestBody.promptType)){
            resId = promptService.createFlashCard(request,requestBody,parsedJson.flashcards).id
        }else resId = promptService.createReadingResource(request,session,params,request.JSON)
        gptLogService.getGPTResources(new Long(requestBody.resId))
        def json = [status:"OK",resId:resId]
        render json as JSON

    }

    @Transactional
    def getGPTsForResource(){
        String siteId = utilService.getSiteIdIgnoreSiteName(request, session)
        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(new Integer(params.resId),params.promptType)
        def json
        if(gptDefaultCreateLog!=null) {
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(gptDefaultCreateLog.resId)
            def requestBody = request.JSON
            if (springSecurityService.currentUser != null) {
                params.put('username', springSecurityService.currentUser.username)
            } else {
                params.put('username', siteId + "_guest")
            }
            params.put('resId', new Integer(resourceDtl.id.intValue()))
            params.put('readingMaterialResId', new Integer("" + params.resId))
            params.put('response', "From default GPT")
            params.put('siteId', siteId)
            def gptLog = new GptLog(params)
            def savedGptLog = null
            if (!"chapter_snapshot".equals(params.promptType))
                savedGptLog = gptLogService.saveGptLog(gptLog)
            json = [hasResource:true, answer  : gptDefaultCreateLog.response, resId: gptDefaultCreateLog.resId, quizId: resourceDtl.resLink, resourceName: resourceDtl.resourceName,
                        gptLogId: savedGptLog != null ? savedGptLog.id : null]

        }else{
            json = [hasResource:false]
        }
        render json as JSON
    }

    @Transactional
    def getGPTsListForResource(){
        boolean isTeacher = false
        def defaultPrompts
        String siteId = session["siteId"] ? "" + session["siteId"] : params.siteId
        isTeacher = promptService.checkIsTeacher(siteId, request, params)

        if(redisService.("gptResources_"+params.resId+"_"+isTeacher)==null) gptLogService.getGPTResources(new Long(params.resId), isTeacher)

        def suggestedVideos = []
        if(params.chapterId && params.chapterId!=null && params.chapterId!=""){
            if(redisService.("suggestedVideos_"+params.chapterId)==null) dataProviderService.getRelatedVideosFromDB((""+params.chapterId))
            suggestedVideos = redisService.("suggestedVideos_"+params.chapterId)
        }
        if(params.bookId!=null){
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            if(booksMst != null&&booksMst.language!=null&&!"English".equals(booksMst.language)) {
                if(redisService.("defaultPrompts_"+booksMst.language+"_"+isTeacher)==null) gptLogService.getDefaultPromptListLanguage(booksMst.language,isTeacher)
                defaultPrompts = redisService.("defaultPrompts_"+booksMst.language+"_"+isTeacher)
            }
        }else{
            if(redisService.("defaultPrompts_"+isTeacher)==null) gptLogService.getDefaultPromptList(isTeacher)
            defaultPrompts = redisService.("defaultPrompts_"+isTeacher)
        }
        def json = [gpts:redisService.("gptResources_"+params.resId+"_"+isTeacher),suggestedVideos:suggestedVideos,
                    defaultPrompts:defaultPrompts]
        render json as JSON
    }

    @Transactional
    def bookgpt(){
        boolean isTeacher = false
        handleSalesAffiliation(params, session)

        boolean hasBookAccess = false
        def previewMode = "true".equals(params.preview) ? true : false
        Integer siteId = utilService.getSiteId(request, session)
        setupUserSession(siteId, params, session, servletContext, response)
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        def priceDetails = processBookPriceDetails(booksMst.id)
        boolean eBookPriceZero = priceDetails.eBookPriceZero
        Double upgradePrice = priceDetails.upgradePrice
        Double eBookPrice = priceDetails.eBookPrice
        def accessResult = checkBookAccess(params.bookId, booksMst, eBookPriceZero, siteId, session, request, response)
        if (accessResult.redirectRequired) {
            redirect(accessResult.redirectParams)
            return
        }
        hasBookAccess = accessResult.hasBookAccess
        previewMode = accessResult.previewMode

        loadChapterData(params.bookId)

        def pdfEncryptionResult = setupPdfEncryption(session)
        String encryptPdfStr = pdfEncryptionResult.encryptPdfStr
        String bookType = booksMst.bookType


        def bookLevelPrompt = booksMst.basePrompt
        boolean purchasedGPT = false
        boolean showUpgrade = false

        def resourceResult = setupChapterAndResourceIds(params, booksMst, siteId)
        if (resourceResult.redirectRequired) {
            redirect(resourceResult.redirectParams)
            return
        }
        if (checkAndRedirectLibwonder(previewMode, request)) {
            return
        }
        else {
            def tokenResult = processUserTokensAndPermissions(params.bookId, hasBookAccess, session)
            int freeTokenCount = tokenResult.freeTokenCount
            int paidTokenCount = tokenResult.paidTokenCount
            boolean paidUser = tokenResult.paidUser
            isTeacher = tokenResult.isTeacher
            purchasedGPT = tokenResult.purchasedGPT
            showUpgrade = tokenResult.showUpgrade
            if (tokenResult.updateBookType) {
                bookType = tokenResult.bookType
            }
            previewMode = tokenResult.previewMode || previewMode
            logBookView(params.bookId, hasBookAccess, params.instituteId, request, session)
            boolean showPdf = true
            if("false".equals(booksMst.showPdf)){
                showPdf = false
            }
            if(!isTeacher) isTeacher = promptService.checkIsTeacher(""+siteId, request, params)
            def defaultPrompts = loadDefaultPrompts(booksMst, isTeacher, siteId)
            String gptServer = promptService.getGPTServerUrl(request)

            isTeacher = checkIfUserIsTeacher(params, request, session, isTeacher)
            def uiConfig = configureUI(booksMst, siteId)
            SiteDtl siteDtl = uiConfig.siteDtl
            String pageTitle = uiConfig.pageTitle
            boolean enableCompiler = uiConfig.enableCompiler
            boolean hideChat = uiConfig.hideChat
            boolean showReadingContent = uiConfig.showReadingContent

            if(params.chapterId==null){
                def chaptersList = new JsonSlurper().parseText("allChapterDetails_" +params.bookId)
                params.put("chapterId",""+chaptersList[0].chapterId)
                params.put("resId",""+chaptersList[0].resId)
            }

            [
                    encryptPdfKey    : encryptPdfStr,
                    hasBookAccess    : hasBookAccess,
                    chapters:redisService.("allChapterDetails_" +params.bookId),
                    defaultPrompts:defaultPrompts,
                    bookLevelPrompt:booksMst.basePrompt,
                    previewMode:previewMode,
                    bookTitle:booksMst.title,
                    eBookPrice:eBookPrice,
                    prices: redisService.("bookPriceDetails_" + booksMst.id),
                    freeTokenCount:freeTokenCount,
                    paidTokenCount:paidTokenCount,
                    showNotificaiton: booksMst.showNotification,
                    title: pageTitle,
                    bookType: bookType,
                    gptServer:gptServer,
                    purchasedGPT:purchasedGPT,
                    showUpgrade:showUpgrade,
                    showSnapshot:booksMst.showSnapshot!=null?booksMst.showSnapshot:"false",
                    showMcq:booksMst.showMcq!=null?booksMst.showMcq:"false",
                    showQa:booksMst.showQa!=null?booksMst.showQa:"false",
                    showPdf:showPdf,
                    isTeacher:isTeacher,
                    bookLang:booksMst.language,
                    enableToken: booksMst.enableToken!=null?booksMst.enableToken:"false",
                    enableCompiler: enableCompiler,
                    gptloaderName: siteDtl!=null ? siteDtl.gptLoaderDisplayName: null,
                    gptloaderpath: siteDtl!=null ? siteDtl.gptLoaderPath: null,
                    gptcustomloader: siteDtl!=null ? siteDtl.customGptLoader: null,
                    hideChat:hideChat,
                    showReadingContent:showReadingContent
        ]
        }
    }

    def hideChat(BooksMst booksMst){
        println("the snapshot and mcq values are ${booksMst.showSnapshot} and ${booksMst.showMcq}")
        if((booksMst.showSnapshot==null || !"true".equals(booksMst.showSnapshot))&&"true".equals(booksMst.showMcq)){
            return true
        }
        return false
    }

    /**
     * Sets up the user session with site and user details
     * @param siteId The site ID
     * @param params The request parameters
     * @param session The HTTP session
     * @param servletContext The servlet context
     * @param response The HTTP response
     */
    private void setupUserSession(Integer siteId, params, session, servletContext, response) {
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if ("true".equals(siteMst.commonWhiteLabel)) {
            String siteName = params.siteName!=null?params.siteName:siteMst.siteName
            if(!"true".equals(""+session["userSessionSet"])) userManagementService.setUserSession(siteName, session, servletContext, response)
        }
        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
    }

    /**
     * Handles sales affiliation tracking
     * @param params The request parameters
     * @param session The HTTP session
     */
    private void handleSalesAffiliation(params, session) {
        if(params.scd!=null) {
            if(session["scd"]==null||!params.scd.equals(""+session["scd"])) {
                session["scd"] = params.scd
                asyncLogsService.updateSalesAffiliation(params.scd, params.bookId, springSecurityService.currentUser!=null?springSecurityService.currentUser.username:null)
            }
        }
    }

    /**
     * Processes book price details
     * @param bookId The book ID
     * @return Map containing eBookPriceZero, upgradePrice, and eBookPrice
     */
    private Map processBookPriceDetails(Long bookId) {
        List bookPriceDtls = BookPriceDtl.findAllByBookId(bookId)
        boolean eBookPriceZero = false
        Double upgradePrice = null
        Double eBookPrice = null
        bookPriceDtls.each { bookPrice ->
            if ("eBook".equals(bookPrice.bookType)) {
                if (bookPrice.sellPrice != null && bookPrice.sellPrice.doubleValue() == 0) eBookPriceZero = true
                eBookPrice = bookPrice.sellPrice
            } else if ("upgrade".equals(bookPrice.bookType)) {
                upgradePrice = bookPrice.sellPrice
            }
        }
        return [eBookPriceZero: eBookPriceZero, upgradePrice: upgradePrice, eBookPrice: eBookPrice]
    }

    /**
     * Checks if the user has access to the book
     * @param bookId The book ID
     * @param booksMst The BooksMst object
     * @param eBookPriceZero Whether the eBook price is zero
     * @param siteId The site ID
     * @param session The HTTP session
     * @param request The HTTP request
     * @param response The HTTP response
     * @return Map containing hasBookAccess, previewMode, redirectRequired, and redirectParams
     */
    private Map checkBookAccess(bookId, BooksMst booksMst, boolean eBookPriceZero, Integer siteId, session, request, response) {
        boolean hasBookAccess = false
        boolean previewMode = "true".equals(request.getParameter("preview")) ? true : false
        boolean redirectRequired = false
        def redirectParams = [:]
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)

        if (springSecurityService.currentUser == null || siteId.intValue()==80 || userManagementService.isValidSession(springSecurityService.currentUser.username, session.getId())) {
            hasBookAccess = userManagementService.hasAccessToBook(new Long(bookId), session, request, true, response)
            if (!hasBookAccess) {
                hasBookAccess = userManagementService.hasLibraryAccessToBook(new Long(bookId), true)
            }

            if (!hasBookAccess) {
                if (!eBookPriceZero && "published".equals(booksMst.status)) {
                    previewMode = true
                } else {
                    redirectRequired = true
                    if ("true".equals(siteMst.commonWhiteLabel)) {
                        redirectParams = [uri: "/sp/${siteMst.siteName}/store"]
                    } else {
                        redirectParams = [controller: siteMst.siteName, action: 'index']
                    }
                }
            }
        }
        if (hasBookAccess) previewMode = false

        return [hasBookAccess: hasBookAccess, previewMode: previewMode, redirectRequired: redirectRequired, redirectParams: redirectParams]
    }

    /**
     * Loads chapter data from Redis or database
     * @param bookId The book ID
     */
    private void loadChapterData(bookId) {
        if(redisService.("allChapterDetails_"+bookId)==null) {
            dataProviderService.getAllChapterDetails(new Long(bookId))
        }
        if (redisService.("chapters_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId));
        }
    }

    /**
     * Sets up PDF encryption
     * @param session The HTTP session
     * @return Map containing encryptPdfStr
     */
    private Map setupPdfEncryption(session) {
        String workingKey = "9E74748742AAB8342432FCF15E225793"
        String pdfResp = "pdf" + (int) (Math.random() * ((9999 - 0000) + 1)) + 0000
        session.setAttribute('pdfKey', pdfResp)
        AesCryptUtil aesUtilPdf = new AesCryptUtil(workingKey)
        String encryptPdfStr = aesUtilPdf.encrypt(pdfResp)
        String sessionEncPdfKey = session.getAttribute('pdfEncKey')
        if (sessionEncPdfKey != null && !sessionEncPdfKey.isEmpty()) session.removeAttribute('pdfEncKey')

        return [encryptPdfStr: encryptPdfStr]
    }

    /**
     * Sets up chapter and resource IDs
     * @param params The request parameters
     * @param booksMst The BooksMst object
     * @param siteId The site ID
     * @return Map containing redirectRequired and redirectParams
     */
    private Map setupChapterAndResourceIds(params, BooksMst booksMst, Integer siteId) {
        boolean redirectRequired = false
        def redirectParams = [:]
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)

        List promptList = Prompts.findAllByIsDefault("Yes")
        if(params.chapterId==null){
            if (redisService.("chapters_" + booksMst.id) == null) {
                dataProviderService.getChaptersList(booksMst.id)
            }
            if(redisService.("previewchapter_" + booksMst.id)!=null&&!"".equals(redisService.("previewchapter_" + booksMst.id)))
            params.put("chapterId",""+redisService.("previewchapter_" + booksMst.id))
            else{
                def chaptersMst = new JsonSlurper().parseText(redisService.("chapters_" + booksMst.id))
                params.put("chapterId",""+chaptersMst[0].id)
            }
        }
        if(booksMst != null){
            if (redisService.("bookPriceDetails_" + booksMst.id) == null) bookPriceService.getBookPrices(new Integer("" + booksMst.id))
        }
        if(params.resId==null){
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId),"Notes", [sort: "id", order: "asc"])
            if(readingMaterials.size()>0)
                params.put("resId",""+readingMaterials[0].id)
            else {
                redirectRequired = true
                if ("true".equals(siteMst.commonWhiteLabel)) {
                    redirectParams = [uri: "/sp/${siteMst.siteName}/store"]
                } else {
                    redirectParams = [controller: siteMst.siteName, action: 'index']
                }
            }
        }

        return [redirectRequired: redirectRequired, redirectParams: redirectParams]
    }

    /**
     * Checks if the request should be redirected from libwonder.com to wonderslate.com
     * @param previewMode Whether the book is in preview mode
     * @param request The HTTP request
     * @return boolean indicating whether a redirect was performed
     */
    private boolean checkAndRedirectLibwonder(boolean previewMode, request) {
        if(previewMode && request.getRequestURL().indexOf("libwonder.com")>-1){
            String redirectUrl = (""+request.getRequestURL()).replace("libwonder.com","wonderslate.com")+"?"+request.queryString
            redirect(url:redirectUrl)
            return true
        }
        return false
    }

    /**
     * Processes user tokens and permissions
     * @param bookId The book ID
     * @param hasBookAccess Whether the user has access to the book
     * @param session The HTTP session
     * @return Map containing token and permission details
     */
    private Map processUserTokensAndPermissions(bookId, boolean hasBookAccess, session) {
        int freeTokenCount = 0
        int paidTokenCount = 0
        boolean paidUser = false
        boolean isTeacher = false
        boolean purchasedGPT = false
        boolean showUpgrade = false
        boolean updateBookType = false
        String bookType = null
        boolean previewMode = false

        if(springSecurityService.currentUser != null) {
            BooksPermission booksPermission = gptLogService.getBooksPermission(new Integer(bookId),springSecurityService.currentUser.username)
            if(booksPermission!=null&&!"ADDEDFROMINSTITUTE".equals(booksPermission.poType)) paidUser = true
            User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
            if(user.teacher!=null&&!"".equals(user.teacher)&&!"null".equals(user.teacher)&&!"false".equals(user.teacher)) isTeacher = true
            if(user.chatTokensBalance!=null) {
                paidTokenCount = user.chatTokensBalance.intValue()
            }
            if(booksPermission!=null&&booksPermission.chatTokensBalance!=null) {
                  freeTokenCount = booksPermission.chatTokensBalance.intValue()
                   if(booksPermission.bookType!=null&&("bookgpt".equals(booksPermission.bookType.toLowerCase()) || "ebookgptupgrade".equals(booksPermission.bookType.toLowerCase())||"ibookgptpro".equals(booksPermission.bookType.toLowerCase()))) purchasedGPT = true
                   else{
                       BookPriceDtl bookPriceDtl = bookPriceService.getBooksPriceDtl(new Integer(bookId), "ebookGPTUpgrade")
                       if(bookPriceDtl!=null) showUpgrade = true
                   }
            }

            if(session["userdetails"]!=null&&session["userdetails"].authorities.any { it.authority == "ROLE_GPT_MANAGER" }) {
                purchasedGPT=true
                previewMode=false
                freeTokenCount=100
                bookType="bookgpt"
                updateBookType = true
            }
        }else{
            if(session["previewFreeTokens"] ==null) session["previewFreeTokens"] = 10
            freeTokenCount = session["previewFreeTokens"]
        }
        if(!paidUser&&hasBookAccess && !"71".equals(""+session["siteId"])) {
            println("fixed")
            purchasedGPT = true
            showUpgrade= false
            freeTokenCount=999
        }

        return [freeTokenCount: freeTokenCount, paidTokenCount: paidTokenCount, paidUser: paidUser,
                isTeacher: isTeacher, purchasedGPT: purchasedGPT, showUpgrade: showUpgrade,
                updateBookType: updateBookType, bookType: bookType, previewMode: previewMode]
    }

    /**
     * Logs book view
     * @param bookId The book ID
     * @param hasBookAccess Whether the user has access to the book
     * @param instituteId The institute ID
     * @param request The HTTP request
     * @param session The HTTP session
     */
    private void logBookView(bookId, boolean hasBookAccess, instituteId, request, session) {
        if (hasBookAccess) {
            asyncLogsService.updateBookView(bookId, logsService.detectDevice(request), "library", utilService.getSiteId(request, session),
                    (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""), instituteId)
        } else {
             asyncLogsService.updateBookView(bookId, logsService.detectDevice(request), "preview", utilService.getSiteId(request, session),
                    (springSecurityService.currentUser != null ? springSecurityService.currentUser.username : ""), instituteId)
        }
    }

    /**
     * Loads default prompts
     * @param booksMst The BooksMst object
     * @param isTeacher Whether the user is a teacher
     * @param siteId The site ID
     * @return The default prompts
     */
    private def loadDefaultPrompts(BooksMst booksMst, boolean isTeacher, Integer siteId) {
        def defaultPrompts
        if(booksMst != null&&booksMst.language!=null&&!"English".equals(booksMst.language)) {
            if(redisService.("defaultPrompts_"+booksMst.language+"_"+isTeacher)==null) gptLogService.getDefaultPromptListLanguage(booksMst.language,isTeacher)
            defaultPrompts = redisService.("defaultPrompts_"+booksMst.language+"_"+isTeacher)
        }
        else{
            if(redisService.("defaultPrompts")==null) gptLogService.getDefaultPromptList(isTeacher)
            defaultPrompts = redisService.("defaultPrompts_"+isTeacher)
        }
        return defaultPrompts
    }

    /**
     * Checks if the user is a teacher
     * @param params The request parameters
     * @param request The HTTP request
     * @param session The HTTP session
     * @param currentIsTeacher The current teacher status
     * @return boolean indicating whether the user is a teacher
     */
    private boolean checkIfUserIsTeacher(params, request, session, boolean currentIsTeacher) {
        boolean isTeacher = currentIsTeacher
        String ipAddress
        if("yes".equals(params.app) && !"".equals(params.ipAddress)){
            ipAddress=params.ipAddress
        }else{
            ipAddress = utilService.getIPAddressOfClient(request)
        }
        List usersInstituteDtl = userManagementService.getInstitutesForUser(new Integer(""+session["siteId"]),ipAddress)
        if(springSecurityService.currentUser!=null) {
            for (int i = 0; i < usersInstituteDtl.size(); i++) {
                if("true".equals(""+usersInstituteDtl[i].isInstructor)){
                    isTeacher = true
                    break
                }
            }
        }
        return isTeacher
    }

    /**
     * Configures UI elements
     * @param booksMst The BooksMst object
     * @param siteId The site ID
     * @return Map containing UI configuration
     */
    private Map configureUI(BooksMst booksMst, Integer siteId) {
        SiteDtl siteDtl = dataProviderService.getSiteDtl(siteId)
        String pageTitle = "iBookGPT for "+booksMst.title

        if(siteDtl!=null){
            if("Yes".equals(siteDtl.customGptLoader) && siteDtl.gptLoaderDisplayName!=null){
                pageTitle = siteDtl.gptLoaderDisplayName + " "+booksMst.title
            }
        }

        boolean enableCompiler = false
        if("true".equals(booksMst.enableCompiler)){
            enableCompiler = true
        }

        boolean hideChat = hideChat(booksMst)
        boolean showReadingContent = true
        if("false".equals(booksMst.showPdf)) showReadingContent = false
        println("hide chat is ${hideChat}")
        println("show reading content is ${showReadingContent}")

        return [siteDtl: siteDtl, pageTitle: pageTitle, enableCompiler: enableCompiler,
                hideChat: hideChat, showReadingContent: showReadingContent]
    }
    def getQuestionAndAnswers(){
        def qaList = promptService.getQuestionAndAnswers(params, session)
        def json = [qaList:qaList]
        render json as JSON
    }

    def getPyqs(){
        def qaList = promptService.getPyqs(params, session)
        def json = [qaList:qaList]
        render json as JSON
    }

    @Transactional
    def getPyqsResourceData(){
        try {
            Long chapterId = params.chapterId as Long
            if (!chapterId) {
                render([error: true, message: "Chapter ID is required"] as JSON)
                return
            }

            // Using GORM to query ResourceDtl for PYQS data
            List<ResourceDtl> pyqsResources = ResourceDtl.findAllByChapterIdAndResTypeAndResourceName(
                chapterId,
                "QA",
                "PYQs"
            )

            if (pyqsResources.isEmpty()) {
                render([error: false, message: "No PYQS resources found", data: []] as JSON)
                return
            }

            // Transform the results to match the required format
            List resultData = pyqsResources.collect { resource ->
                [
                    resId: resource.id,
                    quizId: resource.resLink
                ]
            }

            render([error: false, data: resultData] as JSON)
        } catch (Exception e) {
            log.error("Error fetching PYQS resource data: ${e.message}", e)
            render([error: true, message: "Internal server error"] as JSON)
        }
    }

    def createTest(){
        HashMap testMap = promptService.createTest(params, session)
        List exercises = []
        if("true".equals(params.questionBank)){
           ResourceDtl resourceDtl = ResourceDtl.findById(new Long(params.readingMaterialResId))
           ResourceDtl exerciseSolutions = ResourceDtl.findByChapterIdAndResTypeAndResourceName(resourceDtl.chapterId,"QA","Exercise Solutions")
           if(exerciseSolutions!=null) {
                exercises = ObjectiveMst.findAllByQuizId(exerciseSolutions.resLink)


           }
        }
        def json = [status:"OK",mcqs:testMap.mcqs!=null?testMap.mcqs:[],qna:testMap.qna!=null?testMap.qna:[],
                    qnaLanguages:testMap.qnaLanguages!=null?testMap.qnaLanguages:[], mcqLanguages:testMap.mcqLanguages!=null?testMap.mcqLanguages:[],
                    mcqQuizId:testMap.mcqQuizId, mcqResId:testMap.mcqResId, exercises:exercises]
        render json as JSON
    }

    //function to call findGptLogsForUser from promptService
    def findGptLogsForUser(){
        def gptLogs = promptService.findGptLogsForUser(params.username, Integer.parseInt(params.max), Integer.parseInt(params.offset),params.resId)
        def json = [gptLogs:gptLogs]
        render json as JSON
    }

   //function to call findGptLogsForResId from promptService with support for DataTables
    def findGptLogsForResId(){
        def max = params.int('max') ?: params.int('length') ?: 10
        def offset = params.int('offset') ?: params.int('start') ?: 0
        def startDate = params.startDate
        def endDate = params.endDate

        def gptLogs = promptService.findGptLogsForResId(params.resId, max, offset, startDate, endDate)
        def totalCount = promptService.countGptLogsForResId(params.resId, startDate, endDate)

        def json = [
            gptLogs: gptLogs,
            recordsTotal: totalCount,
            recordsFiltered: totalCount,
            draw: params.int('draw')
        ]

        render json as JSON
    }

    //gsp page to show the list of gpt logs for resId
    def showGptLogsForResId(){

    }

    //gsp page for showing the list of gpt logs for a user
    def showGptLogsForUser(){
        HashMap userData = promptService.getUserData(params.username)
        [userData:userData]
    }
    def findGptLogsForFeedback(){
        def gptLogs = promptService.getFeedbacks(Integer.parseInt(params.max), Integer.parseInt(params.offset))
        def json = [gptLogs:gptLogs]
        render json as JSON
    }

    //gsp page for showing the list of gpt logs for feedback
    def showGptLogsForFeedback(){

    }

    def manualGpt(){
        List promptList =Prompts.list()
        [basePromptArray:promptList as JSON, title:"Manual GPT"]
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def download() {
        ResourceDtl documentInstance = ResourceDtl.get(new Long(params.resId))
        if (documentInstance == null) {
            flash.message = "Document not found."
            redirect(action: 'list')
        } else {
            if (utilService.canSeeResource(documentInstance,request,session)) {
                def file = new File(documentInstance.resLink)
                response.setHeader("Content-Disposition", "inline;Filename=\"${documentInstance.filename}\"")
                response.setHeader("Content-Length", "${file.length()}")

                def fileInputStream = new FileInputStream(grailsApplication.config.grails.basedir.path +file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;

                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }

                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            } else {
                redirect(action: 'index')
            }
        }
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def deleteGPTResource(){
        GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByResId(new Long(params.resId))
        if(gptDefaultCreateLog!=null){
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(gptDefaultCreateLog.resId)
            resourceDtl.delete(flush: true)
            def readingMaterialResId = gptDefaultCreateLog.readingMaterialResId
            gptDefaultCreateLog.delete(flush: true)
            gptLogService.getGPTResources(readingMaterialResId)

            def json = [status:"OK"]
            render json as JSON
        }else{
            def json = [status:"Error"]
            render json as JSON
        }



    }

    @Transactional
    def storePdfVectors(){
        def bookId = params.bookId
        def chapterId = params.chapterId
        if(params.resId==null||"null".equals(params.resId)){
            List readingMaterials = ResourceDtl.findAllByChapterIdAndResTypeAndSharingIsNullAndGptResourceTypeIsNull(new Integer(params.chapterId),"Notes", [sort: "id", order: "asc"])
            if(readingMaterials.size()>0)
                params.put("resId",""+readingMaterials[0].id)
            else {
                redirect(controller: 'resources', action: 'ebook', params: [bookId: params.bookId])
                return
            }
        }
        def resId = params.resId
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))
        if (documentInstance == null) {
            flash.message = "Document not found."
            redirect(action: 'list')
        } else {

                def file = new File(documentInstance.resLink)
                try {
                    String index =promptService.getIndex("users")
                    String namespace = index+"_"+chapterId+"_"+resId
                    String filePath = documentInstance.resLink
                    def resCode= promptService.newUserPDFCreation(request,filePath,namespace)
                    def res = ["status":"OK","message":"PDF Vectors stored successfully","resCode":resCode,namespace: namespace]
                    render res as JSON
                }catch(Exception e){
                    def err = ["status":"Error","message":e.message]
                    render err as JSON
                }

        }
    }

    @Transactional
    def checkPDFExists(){
        String namespace = params.namespace
        String resId = namespace.split("_")[1]
        ResourceDtl documentInstance = dataProviderService.getResourceDtl(new Long(resId))
        if("true".equals(documentInstance.vectorStored)) {
            documentInstance.vectorStored = null
            documentInstance.save(flush: true, failOnError: true)
        }
        if(documentInstance.vectorStored!=null){
            def res = ["statusCode": 200, "isExist": true,resMessage:"pdf exists",namespace:documentInstance.vectorStored]
            render res as JSON
        }else{
            def res = ["statusCode": 500, "isExist": false,resMessage:"pdf does not exists"]
            render res as JSON
        }
    }

    @Transactional
    def retrieveData(){
        def requestBody = request.JSON
        println("******* the type of class is "+requestBody.getClass())
        println("******* the type of body is "+requestBody.toString())
        String namespace = requestBody.namespace
        try {
            if (namespace != null && !"".equals(namespace)) {
                String[] namespaceArray = namespace.split("_")
                String chapterId = null
                if (namespaceArray.size() == 3) {
                    chapterId = namespaceArray[1]
                } else if (namespaceArray.size() == 2) {
                    chapterId = namespaceArray[0]
                }
                if (chapterId != null) {
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(new Long(chapterId))
                    BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                    if (booksMst != null && booksMst.publisherId != null) {
                        Publishers publishers = dataProviderService.getPublisher(booksMst.publisherId)
                        if (publishers != null && publishers.customPrompt != null && !"".equals(publishers.customPrompt)) {
                            //add customPrompt to requestbody
                            requestBody.put("customPrompt", publishers.customPrompt)
                        }
                    }
                }
            }
        }catch(Exception e){
            println("Exception in retrieveData "+e.toString())
        }
        URL url = new URL(promptService.getGPTServerUrl(request)+"/retrieveData")
        if(requestBody.imgData){
            requestBody = promptService.savesnippedImg(grailsApplication.config.grails.basedir.path, requestBody)
            url = new URL(promptService.getGPTServerUrl(request)+"/img_chat")
        }else if(requestBody.mcq){
            url = new URL(promptService.getGPTServerUrl(request)+"/mcq-chat")
        }
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            render jsonResponse as JSON
        }else{  return null}

    }

    @Transactional
    def extensionMiddleware(){
        response.setHeader('Access-Control-Allow-Origin', '*')
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        def requestBody = request.JSON
        String apiURL = "/extension-embedding"
        if(requestBody.type=="chat"){
            apiURL = "/extension-chat"
        }
        URL url = new URL(promptService.getGPTServerUrl(request)+apiURL)
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        println("****** the response code is "+responseCode)
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            render jsonResponse as JSON
        }else{  return null}
    }

    @Transactional @Secured(['ROLE_USER'])
    def myDriveReader(){
        def studyMaterial = StudyMaterial.get(new Long(params.id))
        if (!studyMaterial) {
            println("Material not found.")
            redirect(controller: 'studyMaterial', action: 'index')
            return
        }

        if (studyMaterial.username != session["userdetails"].username&&!studyMaterialService.isMaterialSharedWithUser(studyMaterial.id, session["userdetails"].username)) {
            println("You do not have access to this material.")
            redirect(controller: 'studyMaterial', action: 'index')
            return
        }

        if (studyMaterial.username.equals(""+session["userdetails"].username)) studyMaterialService.updateLastAccessed(studyMaterial)

        SiteDtl siteDtl = dataProviderService.getSiteDtl(new Long(""+session["siteId"]))

        [materialId: studyMaterial.id,title:"AI - Studio",
         gptloaderName: siteDtl!=null ? siteDtl.gptLoaderDisplayName: null,
         gptloaderpath: siteDtl!=null ? siteDtl.gptLoaderPath: null,
         gptcustomloader: siteDtl!=null ? siteDtl.customGptLoader: null]
    }

    @Transactional
    def getDriveFile(){
        def studyMaterial = StudyMaterial.get(new Long(params.materialId))
        if (!studyMaterial) {
            redirect(controller: 'studyMaterial', action: 'index')
            return
        }

        if (studyMaterial.username != session["userdetails"].username) {
            redirect(controller: 'studyMaterial', action: 'index')
            return
        }
        // Retrieve the file from storage
        String filePath = studyMaterial.filePath
        File file = new File(filePath)
        if (!file.exists()) {
            redirect(controller: 'studyMaterial', action: 'index')
            return
        }
        try{
            if (file.exists()) {
                String uri = request.getHeader("referer")
                String serverURL = request.getScheme()+"://"+request.getServerName()+
                        ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                                "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                                request.getServerPort())

                long fileLength = file.length()
                String rangeHeader = request.getHeader("Range")
                long start = 0
                long end = fileLength - 1

                if (rangeHeader != null) {
                    // Parse the Range header
                    String[] rangeParts = rangeHeader.split("=")[1].split("-")
                    start = Long.parseLong(rangeParts[0])
                    if (rangeParts.length > 1 && !rangeParts[1].isEmpty()) {
                        end = Long.parseLong(rangeParts[1])
                    }
                }
                end = Math.min(end, fileLength - 1)
                long contentLength = end - start + 1
                response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT)
                response.setContentType("application/pdf")
                response.setHeader("Content-Range", "bytes ${start}-${end}/${fileLength}")
                response.setHeader("Accept-Ranges", "bytes")
                response.setHeader("Content-Length", contentLength.toString())

                RandomAccessFile randomAccessFile = new RandomAccessFile(file, "r")
                randomAccessFile.seek(start)

                byte[] buffer = new byte[4096]
                int bytesRead
                OutputStream outputStream = response.getOutputStream()

                while (contentLength > 0 && (bytesRead = randomAccessFile.read(buffer, 0, (int) Math.min(buffer.length, contentLength))) != -1) {
                    outputStream.write(buffer, 0, bytesRead)
                    contentLength -= bytesRead
                }

                outputStream.flush()
                outputStream.close()
                randomAccessFile.close()
            }else {
                response.sendError(HttpServletResponse.SC_NOT_FOUND)
            }
        }catch(Exception ex){
            println("Exception in getPdfFile "+ex.toString())
            render "";
        }

    }

    def pdfReader(){

    }

    @Transactional
    def checkDrivePdfExists() {
        String namespace = params.namespace
        println(namespace)
        URL url = new URL(promptService.getGPTServerUrl(request) + "/checkDrivePdf?namespace=" + namespace)
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("GET")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()

        try {
            int responseCode = connection.getResponseCode()
            println(responseCode)
            if (responseCode == 200) {
                // Read the response from the input stream
                InputStream inputStream = connection.getInputStream()
                String responseText = inputStream.text
                inputStream.close()

                JsonSlurper jsonSlurper = new JsonSlurper()
                def jsonResponse = jsonSlurper.parseText(responseText)

                render jsonResponse as JSON
            } else {
                println("Failed to check PDF. Response code: ${responseCode}")
                def errJson = ["error": "Received response code ${responseCode}"]
                render errJson as JSON
            }
        } catch (Exception e) {
            println("Error while checking PDF: ${e.message}", e)
            render "Error: ${e.message}"
        } finally {
            connection.disconnect()
        }
    }

    @Transactional
    def storeDrivePdfVectors(){
        def studyMaterial = StudyMaterial.get(new Long(params.materialId))
        if (!studyMaterial) {
            redirect(controller: 'studyMaterial', action: 'index')
            return
        }

        if (studyMaterial.username != session["userdetails"].username) {
            redirect(controller: 'studyMaterial', action: 'index')
            return
        }
        String namespace = params.namespace
        try {
            String filePath = studyMaterial.filePath
            String urlStr = promptService.getGPTServerUrl(request)
            def resCode= promptService.drivePdfVectorCreation(urlStr,filePath,namespace)
            def res = ["status":"OK","message":"PDF Vectors stored successfully","resCode":resCode,namespace: namespace]
            render res as JSON
        }catch(Exception e){
            println(e.message)
            def err = ["status":"Error","message":e.message]
            render err as JSON
        }
    }

    @Transactional
    def driveChat(){
        def requestBody = request.JSON

        String apiURL = "/driveChat"
        if(requestBody.type=="voice-mod"){
            apiURL = "/modify-content"
        }else if(requestBody.imgData){
            requestBody = promptService.savesnippedImg(grailsApplication.config.grails.basedir.path, requestBody)
            apiURL = "/img_chat"
        }
        URL url = new URL(promptService.getGPTServerUrl(request)+apiURL)
        HttpURLConnection connection = (HttpURLConnection) url.openConnection()
        connection.setRequestMethod("POST")
        connection.setRequestProperty("Content-Type", "application/json")
        connection.setDoOutput(true)
        connection.setDoInput(true)
        connection.connect()
        def writer = new BufferedWriter(new OutputStreamWriter(connection.getOutputStream()))
        writer.write(requestBody.toString())
        writer.flush()
        writer.close()
        def responseCode = connection.getResponseCode()
        if(responseCode==200){
            def reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))
            def response = reader.readLine()
            def jsonSlurper = new JsonSlurper()
            def jsonResponse = jsonSlurper.parseText(response)
            render jsonResponse as JSON
        }else{  return null}

    }

    def updateDefaultPrompt(){
        def prompt = Prompts.get(params.promptId)
        try {
            String message = ""
            if(prompt){
                println(prompt.promptLabel)
                prompt.isDefault = params.isDefault;
                if (prompt.save(flush: true)) {
                    message = "Updated default Prompt status"
                } else {
                    message = "Error updating default Prompt status"
                }

            }else{
                message = "No prompts object found"
            }

            def json = [status: "OK", message: message ]
            render json as JSON
        }catch(Exception e){
            println("Exception while updating default prompt status")
            println(e)
        }
    }

    @Transactional
    def quizGptInteraction(){
        def requestBody = request.JSON
        try {
            ObjectiveMst objectiveMst = ObjectiveMst.findById(new Long(requestBody.qId))
            if (objectiveMst == null) {
                render([error: true, message: "Objective not found"] as JSON)
                return
            }

            def response = null
            switch (requestBody.type) {
                case "explain":
                    response = objectiveMst.gptExplanation
                    break
                case "similarMCQ":
                    response = objectiveMst.gptSimilarQuestions
                    break
                case "hint":
                    response = objectiveMst.gptHint
                    break
                default:
                    retrieveData()
                    return
            }

            if (response != null && !"".equals(response)) {
                render([answer: response, query: requestBody.query, isExist: true] as JSON)
            } else {
                retrieveData()
            }
        } catch (Exception e) {
            println("Exception while fetching quiz GPT contents"+e)
            render([error: true, message: "Something went wrong"] as JSON)
        }
    }
    @Transactional
    def storeQuizGptContents(){
        try {
            String message = "Unknown error occurred"
            boolean isStored = false
            def requestBody = request.JSON
            if (!requestBody?.qId || !requestBody?.type || !requestBody?.response) {
                render([status: "ERROR", message: "Invalid request payload"] as JSON)
                return
            }
            ObjectiveMst objectiveMst = ObjectiveMst.findById(requestBody.qId as Long)
            if (objectiveMst) {
                switch (requestBody.type) {
                    case "explain":
                        objectiveMst.gptExplanation = requestBody.response
                        break
                    case "similarMCQ":
                        objectiveMst.gptSimilarQuestions = requestBody.response
                        break
                    case "hint":
                        objectiveMst.gptHint  = requestBody.response
                        break
                    default:
                        render([status: "ERROR", message: "Invalid request type"] as JSON)
                        return
                }
                objectiveMst.save(failOnError: true, flush: true)
                message = "Response stored successfully"
                isStored = true
            } else {
                message = "No MCQ found for the given ID"
            }
            render([status: "OK", message: message, isStored: isStored] as JSON)
        }catch(Exception e){
            println("Exception while adding quiz gpt contents")
            println(e)
        }
    }
    @Transactional
    def getTestInfo(){
        String siteId = utilService.getSiteIdIgnoreSiteName(request, session)
        List defaultGptCreateLogs = GptDefaultCreateLog.findAllByReadingMaterialResIdAndPromptTypeInList(new Integer(params.readingMaterialResId),["mcq","qna"])
        HashMap testMap = new HashMap()
        def mcqCounts = ["difficultylevels": ""]
        def qnaCounts = ["difficultylevels": ""]

        def difficultyMapping = promptService.getDifficultyMapping()

        def reverseMapping = difficultyMapping.collectEntries { key, values ->
            values.collectEntries { [(it): key] }
        }

        defaultGptCreateLogs.each {
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer("" + it.resId))
            if (resourceDtl.resType == "Multiple Choice Questions") {
                def results = promptService.groupMcqByLevels(resourceDtl.resLink)
                def difficultyLevels = promptService.mapGroupedMcqs(results, reverseMapping)
                def jsonAnswers = [difficultylevels: difficultyLevels]
                testMap.mcqs = jsonAnswers
            }else if(resourceDtl.resType == "QA"){
                def results = promptService.groupMcqByLevels(resourceDtl.resLink)
                def difficultyLevels = promptService.mapGroupedMcqs(results, reverseMapping)
                def jsonAnswers = [difficultylevels: difficultyLevels]
                testMap.qna = jsonAnswers
            }
        }
        testMap.mcqs = testMap.mcqs ?: mcqCounts
        testMap.qna = testMap.qna ?: qnaCounts
        render testMap as JSON
    }

    @Transactional
    def getGptResourceOptions(){
        try {
            BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
            def json
            if(booksMst!=null){
                json = [showSnapshot:booksMst.showSnapshot,
                        showMcq: booksMst.showMcq,
                        showQa: booksMst.showQa,
                        showPdf: booksMst.showPdf,
                        showNotificaiton: booksMst.showNotification,
                        status: 200
                        ]

            }else{
                json = [status: 200, error: true, message: "No book found for the id"]
            }
            render json as JSON
        }catch(Exception e){
            println("Exception while getting gpt resource options for book")
            println(e)
        }
    }

    @Transactional
    def getTokenDetails(){
        def json
        try {
            def details = promptService.getTokenDetails(params.bookId, session)

            json = [freeTokenCount:details.freeTokenCount,
                        paidTokenCount:details.paidTokenCount,
                        previewMode:details.previewMode,
                        bookType:details.bookType,
                        purchasedGPT:details.purchasedGPT,
                        showUpgrade:details.showUpgrade,
                        paidUser:details.paidUser
            ]
        }catch(Exception e){
            println(e)
            json = [error:true,message: "something went wrong"]
        }

        render json as JSON
    }

    @Transactional
    def getInstituteForUser(String username){
        BatchUserDtl batchUserDtl = BatchUserDtl.findByUsername(username)
        if(batchUserDtl!=null){
            HashMap userDtl = new HashMap()
            CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findById(batchUserDtl.batchId)
            session["userInstituteId"]=courseBatchesDtl.conductedBy
        }else{
            InstituteUserDtl instituteUserDtl = InstituteUserDtl.findByUsername(username)
            if(instituteUserDtl!=null)
            session["userInstituteId"]=instituteUserDtl.instituteId
            else session["userInstituteId"]="notFound"
        }
    }
    @Transactional
    def getDefaultPromptListForResource(){
        String resId = params.resId
        String bookId = params.bookId
        Long instituteId = null
        if(springSecurityService.currentUser!=null) {
            if (session["userInstituteId"] == null) getInstituteForUser(springSecurityService.currentUser.username)
            if (!"notFound".equals(session["userInstituteId"])) instituteId = new Long(""+session["userInstituteId"])
        }

        boolean isTeacher = promptService.checkIsTeacher(""+session["siteId"], request, params)
        String defaultPrompts=""

        // Include instituteId in Redis key if provided
        String instituteIdSuffix = instituteId != null ? "_inst" + instituteId : "_"

        if(resId != null){
            String redisKey = "defaultPromptsForResource_" + resId + "_" + isTeacher + instituteIdSuffix
            if(redisService.(""+redisKey) == null) {
                gptLogService.getDefaultPromptListForResource(resId, bookId, isTeacher, instituteId,redisKey)
            }
            defaultPrompts = redisService.(""+redisKey)

        } else if(bookId != null){
            String redisKey = "defaultPromptsForBook_" + bookId + "_" + isTeacher + instituteIdSuffix
            if(redisService.(""+redisKey) == null) {
                gptLogService.getDefaultPromptListForResource(null, bookId, isTeacher, instituteId,redisKey)
            }
            defaultPrompts = redisService.(""+redisKey)
        }

        def suggestedVideos = []
        if(params.chapterId && params.chapterId!=null && params.chapterId!=""){
            if(redisService.("suggestedVideos_"+params.chapterId)==null) dataProviderService.getRelatedVideosFromDB((""+params.chapterId))
            suggestedVideos = redisService.("suggestedVideos_"+params.chapterId)
        }

        def json = [suggestedVideos:suggestedVideos, gpts:defaultPrompts]
        render json as JSON
    }

    @Transactional
    def getSuggestedVideos(){
        def suggestedVideos = []
        if(params.chapterId && params.chapterId!=null && params.chapterId!=""){
            if(redisService.("suggestedVideos_"+params.chapterId)==null) dataProviderService.getRelatedVideosFromDB((""+params.chapterId))
            suggestedVideos = redisService.("suggestedVideos_"+params.chapterId)
        }
        def json = [suggestedVideos:suggestedVideos]
        render json as JSON
    }

}
