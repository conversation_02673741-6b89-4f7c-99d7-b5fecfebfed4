package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.GptDefaultCreateLog
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class PromptLanguagesController {
    def springSecurityService
    DataProviderService dataProviderService

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def index() {
        // Get distinct, sorted languages from BooksMst

        def languages = BooksMst.executeQuery("select distinct b.language from BooksMst b where b.language is not null and b.language != '' and b.language != 'English' order by b.language asc")

        // Get only default prompts
        def defaultPrompts = Prompts.findAllByParentPromptTypeIsNull()

        // Check if a language is selected
        def selectedLanguage = params.language ?: ""
        def translationsMap = [:]

        if (selectedLanguage) {
            // Load translations for the selected language
            def langPrompts = PromptsLangMst.findAllByLanguage(selectedLanguage)
            langPrompts.each { lp ->
                translationsMap[lp.promptType] = lp.promptLabel
            }
        }

        render view: "index", model: [
                languages: languages,
                defaultPrompts: defaultPrompts,
                selectedLanguage: selectedLanguage,
                translationsMap: translationsMap
        ]
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def saveTranslations() {
        def selectedLanguage = params.language ?: ""
        if (!selectedLanguage) {
            flash.message = "Please select a language before saving translations."
            redirect(action: "index")
            return
        }

        // Retrieve arrays of promptTypes and promptLabels from form submission
        def promptTypes = params.list('promptType')
        def promptLabels = params.list('promptLabel')

        promptTypes.eachWithIndex { pType, idx ->
            def pLabel = promptLabels[idx]
            if (pType && pLabel) {
                // Check if a translation already exists
                def existing = PromptsLangMst.findByPromptTypeAndLanguage(pType, selectedLanguage)
                if (!existing) {
                    existing = new PromptsLangMst(promptType: pType, language: selectedLanguage, promptLabel: pLabel)
                } else {
                    existing.promptLabel = pLabel
                }
                existing.save(flush:true, failOnError:true)
            }
        }

        flash.message = "Translations saved successfully."
        redirect(action: "index", params: [language: selectedLanguage])
    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def migrateBook()
    {
        def bookId = params.bookId
        def booksMst = BooksMst.get(bookId)
        if (!booksMst) {
            render "Book not found"
        }else{
            //get all chapters of book
            def chapters = ChaptersMst.findAllByBookId(bookId)
            //list through all chapters
            chapters.each { chapter ->
                // get the reading material of chapter

                def readingMaterial = ResourceDtl.findByChapterIdAndResType(chapter.id,"Notes")
                if(readingMaterial==null){
                     readingMaterial = new ResourceDtl(chapterId: chapter.id, resType: "Notes",filename: "dummy.pdf",resLink: "supload/dummy.pdf",resourceName: chapter.name,
                            createdBy: springSecurityService.currentUser.username,siteId: booksMst.siteId)
                    readingMaterial.save(flush:true,failOnError:true)
                }
                def readingMaterialResId = readingMaterial.id
                ResourceDtl mcqResource = ResourceDtl.findByChapterIdAndResType(chapter.id,"Multiple Choice Questions")
                if(mcqResource){
                    mcqResource.gptResourceType="mcq"
                    mcqResource.save(flush:true,failOnError:true)
                    //add the gpt log
                    GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(readingMaterialResId,"mcq")
                    if(!gptDefaultCreateLog){
                        gptDefaultCreateLog = new GptDefaultCreateLog(readingMaterialResId: readingMaterialResId,promptType: "mcq",promptLabel: "Create MCQs (Multiple Choice Questions)",
                                prompt: "MCQ",response: "MCQ",username: springSecurityService.currentUser.username,resId: mcqResource.id)
                        gptDefaultCreateLog.save(flush:true,failOnError:true)
                    }
                }
            }
            BooksMst.executeUpdate("update BooksMst set bookType='bookgpt',showMcq='true',showQa='false',showSnapshot='false',showPdf='false' where id=" + params.bookId)
            BooksMst.wsshop.executeUpdate("update BooksMst set bookType='bookgpt',showMcq='true',showQa='false',showSnapshot='false',showPdf='false' where id=" + params.bookId)
            BooksMst.wsuser.executeUpdate("update BooksMst set bookType='bookgpt',showMcq='true',showQa='false',showSnapshot='false',showPdf='false' where id=" + params.bookId)
            dataProviderService.getChaptersList(new Long(params.bookId))
            dataProviderService.getAllChapterDetails(new Long(params.bookId))
            render "Book migrated successfully."
        }


    }

    @Secured(['ROLE_GPT_MANAGER']) @Transactional
    def singulariseChapters() {
        def bookId = params.bookId
        def booksMst = BooksMst.get(bookId)
        if (!booksMst) {
            render "Book not found"
        } else {
            List chapters = ChaptersMst.findAllByBookId(bookId)
            int seqNo=0
            chapters.each { chapter ->
                chapter.sortOrder = new Integer(seqNo)
                chapter.save(flush:true,failOnError:true)
                seqNo++
                List resources = ResourceDtl.findAllByChapterId(chapter.id)
                if(resources.size()>1){
                    for(int i=1;i<resources.size();i++){
                        ResourceDtl resource = resources.get(i)
                        //create new chapter
                        ChaptersMst chaptersMst = new ChaptersMst(bookId: booksMst.id, name: resource.resourceName  , sortOrder: new Integer(seqNo), createdBy: springSecurityService.currentUser.username, siteId: booksMst.siteId)
                        chaptersMst.save(flush:true,failOnError:true)
                        seqNo++
                        resource.chapterId = chaptersMst.id
                        resource.save(flush:true,failOnError:true)
                    }
                }

            }
        }
        dataProviderService.getChaptersList(new Long(params.bookId))
        render "Chapters singularised successfully."
    }

}
