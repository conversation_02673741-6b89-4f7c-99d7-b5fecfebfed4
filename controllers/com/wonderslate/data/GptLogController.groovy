package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.log.GptLog
import com.wonderslate.publish.BooksPermission
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.rest.*
import grails.converters.*
import grails.transaction.Transactional

class GptLogController {

    GptLogService gptLogService
    DataProviderService dataProviderService
    def redisService
    def springSecurityService
    UtilService utilService
    UserManagementService userManagementService

    static responseFormats = ['json', 'xml']

    @Transactional
    def save() {
        def requestBody = request.JSON
        String siteId = utilService.getSiteIdIgnoreSiteName(request, session)
        if(springSecurityService.currentUser!=null){
            params.put('username',springSecurityService.currentUser.username)
        }else{
            params.put('username',siteId+"_guest")
        }
        params.put('userPrompt',requestBody.userPrompt)
        params.put('systemPrompt',requestBody.systemPrompt)
        params.put('response',requestBody.response)
        params.put('resId',requestBody.resId)
        params.put('readingMaterialResId',requestBody.readingMaterialResId)
        params.put('promptType',requestBody.promptType)
        params.put('siteId',siteId)
        params.put('imgLink',requestBody.imgLink)
        params.put('quizObjId',requestBody.quizObjId)
        def gptLog = new GptLog(params)
        try {
            def savedGptLog = gptLogService.saveGptLog(gptLog)
            if(springSecurityService.currentUser!=null)
            gptLogService.updateTokenUsage(springSecurityService.currentUser.username,new Integer(""+requestBody.readingMaterialResId))
            else {
                if(session["previewFreeTokens"]!=null)
                session["previewFreeTokens"] = session["previewFreeTokens"] - 1
            }
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(""+requestBody.readingMaterialResId))
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
            int freeTokenCount = 0
            int paidTokenCount = 0
            boolean paidUser = false
            boolean hasBookAccess = false
            if(springSecurityService.currentUser!=null){
                User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
                if(user!=null){
                    if(user.chatTokensBalance!=null){
                        paidTokenCount = user.chatTokensBalance
                    }
                }


                BooksPermission booksPermission = gptLogService.getBooksPermission(chaptersMst.bookId,springSecurityService.currentUser.username)
                if(booksPermission!=null){
                    paidUser = true
                    if(booksPermission.chatTokensBalance!=null){
                        freeTokenCount = booksPermission.chatTokensBalance
                    }
                }
                hasBookAccess = userManagementService.hasAccessToBook(chaptersMst.bookId, session, request, true,response)
                if (!hasBookAccess) {
                    hasBookAccess = userManagementService.hasLibraryAccessToBook(chaptersMst.bookId, true)
                }
                if(!paidUser&&hasBookAccess && !"71".equals(""+session["siteId"])) {
                    freeTokenCount=999
                }

            }
            else{
                if(session["previewFreeTokens"]!=null) freeTokenCount = session["previewFreeTokens"]
            }
            def json = [GptLog:savedGptLog,
                        freeTokenCount:freeTokenCount,
                        paidTokenCount:paidTokenCount]

            render json as JSON
        } catch (Exception e) {
            render status: 400, text: e.message
        }
    }

    @Transactional
    def saveDriveChatLog(){
        def requestBody = request.JSON
        String siteId = utilService.getSiteIdIgnoreSiteName(request, session)
        if(springSecurityService.currentUser!=null){
            params.put('username',springSecurityService.currentUser.username)
        }else{
            params.put('username',siteId+"_guest")
        }
        params.put('userPrompt',requestBody.userPrompt)
        params.put('systemPrompt',requestBody.systemPrompt)
        params.put('response',requestBody.response)
        params.put('materialId',requestBody.materialId)
        params.put('readingMaterialResId',null)
        params.put('promptType',requestBody.promptType)
        params.put('siteId',siteId)
        params.put('imgLink',requestBody.imgLink)
        def gptLog = new GptLog(params)
        try {
            def savedGptLog = gptLogService.saveGptLog(gptLog)
            def json = [GptLog:savedGptLog]

            render json as JSON
        } catch (Exception e) {
            render status: 400, text: e.message
        }
    }

    @Transactional
    def find() {
        String username = springSecurityService.currentUser ? springSecurityService.currentUser.username : ''
        String resId = params.resId
        String pageNo= params.pageNo!=null?params.pageNo:"0"
        String quizObjId = params.quizObjId
        int max = params.int('max', 10)
        int offset = params.int('offset', Integer.parseInt(pageNo))
        ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Integer(resId))
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
        BooksPermission booksPermission = gptLogService.getBooksPermission(chaptersMst.bookId,username)
        User user = dataProviderService.getUserMst(username)
        if(user && user.chatTokensBalance!=null) {
            session["chatTokensBalance"] = user.chatTokensBalance
        }
        int freeTokenCount = 0
        int paidTokenCount = 0
        boolean paidUser = false
        boolean hasBookAccess = false
        if(booksPermission!=null){
            paidUser = true
            if(booksPermission.chatTokensBalance!=null){
                freeTokenCount = booksPermission.chatTokensBalance
            }
        }
        if(user!=null){
            if(user.chatTokensBalance!=null){
                paidTokenCount = user.chatTokensBalance
            }
            hasBookAccess = userManagementService.hasAccessToBook(chaptersMst.bookId, session, request, true,response)
            if (!hasBookAccess) {
                hasBookAccess = userManagementService.hasLibraryAccessToBook(chaptersMst.bookId, true)
            }
            if(!paidUser&&hasBookAccess && !"71".equals(""+session["siteId"])) {
                freeTokenCount=999
            }
        }
        def gptLogs = gptLogService.findGptLogs(username, max, offset,resId, quizObjId)
        def json = [GptLogs:gptLogs,
                    freeTokenCount:freeTokenCount,
                    paidTokenCount:paidTokenCount]
        render json as JSON
    }

    @Transactional
    def findDriveChat() {
        String username = springSecurityService.currentUser ? springSecurityService.currentUser.username : ''
        String materialId = params.materialId
        String pageNo= params.pageNo!=null?params.pageNo:"0"
        int max = params.int('max', 10)
        int offset = params.int('offset', Integer.parseInt(pageNo))
        def gptLogs = gptLogService.findDriveGptLogs(username, max, offset,materialId)
        def json = [GptLogs:gptLogs]
        render json as JSON
    }

    def updateGPTLog(){
            String response = gptLogService.updateGPTLog(params)
            def json = [status:response]
            render json as JSON
    }






}
