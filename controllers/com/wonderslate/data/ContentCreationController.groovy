package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import com.wonderslate.content.ContentExamDtl
import com.wonderslate.content.ContentExamMst
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.publish.BooksTagDtl
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import com.wonderslate.content.ContentExamSolutions

class ContentCreationController {
    def springSecurityService
    DataProviderService dataProviderService

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def createChapters() {
        def bookId = params.bookId
        def booksMst = BooksMst.get(bookId)
        String qPaperIds = params.qPaperIds
        String[] qPaperIdArray = qPaperIds.split(",")
        println("***** qPaperIds="+qPaperIds)
        println("***** bookId="+bookId)
        qPaperIdArray.each { qPaperId ->
            ContentExamDtl contentExamDtl = ContentExamDtl.findById(new Long(qPaperId))
            if (contentExamDtl != null) {
                String chapterName = contentExamDtl.year
                //add month as name if present
                if (contentExamDtl.month != null && !"".equals(contentExamDtl.month.trim())) chapterName += " " + contentExamDtl.month
                //add shift as name if present
                if (contentExamDtl.shift != null && !"".equals(contentExamDtl.shift.trim())) chapterName += " " + contentExamDtl.shift
                println("***** chapterName="+chapterName)
                ChaptersMst chaptersMst = new ChaptersMst(name: chapterName, bookId: booksMst.id)
                chaptersMst.save(failOnError: true, flush: true)
                println("***** chaptersMst.id="+chaptersMst.id)
                //new let us add dummy pdf
                def readingMaterial = new ResourceDtl(chapterId: chaptersMst.id, resType: "Notes", filename: "dummy.pdf", resLink: "supload/dummy.pdf", resourceName: chaptersMst.name,
                        createdBy: springSecurityService.currentUser.username, siteId: new Integer("" + session['siteId']))
                readingMaterial.save(failOnError: true, flush: true)
                println("***** readingMaterial.id="+readingMaterial.id)
                //create the mcq resource
                QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
                quizIdGenerator.save()
                ResourceDtl resourceDtlInstance = new ResourceDtl()
                resourceDtlInstance.resLink = quizIdGenerator.id
                resourceDtlInstance.createdBy = springSecurityService.currentUser.username
                resourceDtlInstance.resType = "Multiple Choice Questions"
                resourceDtlInstance.chapterId = new Integer("" + chaptersMst.id)
                resourceDtlInstance.resourceName = "Quiz"
                resourceDtlInstance.siteId = booksMst.siteId
                resourceDtlInstance.gptResourceType = "mcq"
                resourceDtlInstance.save(failOnError: true, flush: true)
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
                println("***** resourceDtlInstance.id="+resourceDtlInstance.id)
                GptDefaultCreateLog gptDefaultCreateLog = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(readingMaterial.id, "mcq")
                if (!gptDefaultCreateLog) {
                    gptDefaultCreateLog = new GptDefaultCreateLog(readingMaterialResId: readingMaterial.id, promptType: "mcq", promptLabel: "Create MCQs (Multiple Choice Questions)",
                            prompt: "MCQ", response: "MCQ", username: springSecurityService.currentUser.username, resId: resourceDtlInstance.id)
                    gptDefaultCreateLog.save(flush: true, failOnError: true)
                }

                // now get list of ContentExamSolutions
                List contentExamSolutions = ContentExamSolutions.findAllByContentExamDtlId(new Long(qPaperId))
                println("***** contentExamSolutions.size()="+contentExamSolutions.size())
                contentExamSolutions.each { contentExamSolution ->
                    //create objective mst
                    ObjectiveMst om = new ObjectiveMst(quizId: new Integer(resourceDtlInstance.resLink), quizType: "Multiple Choice Questions", question: contentExamSolution.question,
                            option1: contentExamSolution.option1, option2: contentExamSolution.option2, option3: contentExamSolution.option3, option4: contentExamSolution.option4, option5: contentExamSolution.option5,
                            answer1: contentExamSolution.answer!=null && contentExamSolution.answer.equals("a") ? "Yes" : null,
                            answer2: contentExamSolution.answer!=null && contentExamSolution.answer.equals("b") ? "Yes" : null,
                            answer3: contentExamSolution.answer!=null && contentExamSolution.answer.equals("c") ? "Yes" : null,
                            answer4: contentExamSolution.answer!=null && contentExamSolution.answer.equals("d") ? "Yes" : null,
                            answer5: contentExamSolution.answer!=null && contentExamSolution.answer.equals("e") ? "Yes" : null,
                            answerDescription: contentExamSolution.solution, difficultylevel: contentExamSolution.difficultyLevel,
                            marks: contentExamSolution.marks, negativeMarks: contentExamSolution.negativeMark,
                            directionId: contentExamSolution.directionId)
                            .save(failOnError: true, flush: true)
                }
            }


        }
        dataProviderService.getChaptersList(new Long(bookId))
        redirect(controller: 'wonderpublish', action: 'bookCreateNew', params: [bookId: bookId, printBooks: false])
    }

    @Transactional @Secured(['ROLE_GPT_MANAGER'])
    def getSolvedPaperList(){
        BooksTagDtl booksTagDtl = BooksTagDtl.findById(new Long(params.bookTagId))
        String level = booksTagDtl.level
        String syllabus = booksTagDtl.syllabus
        String grade = booksTagDtl.grade
        String subject = booksTagDtl.subject
        ContentExamMst contentExamMst = ContentExamMst.findByLevelAndSyllabusAndGradeAndSubject(level, syllabus, grade, subject)
        if(contentExamMst!=null) {

            List contentExamDtls = ContentExamDtl.findAllByContentExamMstId(contentExamMst.id)
            [
                    'contentExamDtls': contentExamDtls,
                    'bookId'         : params.bookId,
                    'status'         : contentExamDtls ? "OK" : "Nothing present"
            ]

        }else{
            [
                    'contentExamDtls': null,
                    'bookId'         : params.bookId,
                    'status'         : "Nothing present"
            ]
        }
    }
}
