package com.wonderslate.data

import com.wonderslate.cache.DataProviderService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class FolderController {

    SpringSecurityService springSecurityService
    UtilService utilService
    DataProviderService dataProviderService
    def redisService
    FolderService folderService


    @Secured(['ROLE_USER']) @Transactional
    def addFolder(){
      def folderId = folderService.addFolder(params.folderName.replace('\'',''),utilService.getSiteId(request,session))
        def json = ['folderId':folderId]
        render json as JSON

    }
    @Secured(['ROLE_USER']) @Transactional
    def deleteFolder(){

    }
    @Secured(['ROLE_USER']) @Transactional
    def getUserFolders(){
        if(redisService.("folders_"+springSecurityService.currentUser.username)==null){
            dataProviderService.getUserFolders(springSecurityService.currentUser.username)
        }
        def json = ['folders':redisService.("folders_"+springSecurityService.currentUser.username)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def addResourceToFolder(){
        folderService.addResourceToFolder(params.folderId,params.resId)
        dataProviderService.getFolderContents(params.folderId)
    }

    @Secured(['ROLE_USER']) @Transactional
    def getFolderContents(){
        if(redisService.("folderDtl_"+params.folderId)==null){
            dataProviderService.getFolderContents(params.folderId)
        }

        def json = ['folderDtl':redisService.("folderDtl_"+params.folderId)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def folderHome(){
        if(redisService.("folderDtl_"+params.folderId)==null){
            dataProviderService.getFolderContents(params.folderId)
        }
        if(redisService.(springSecurityService.currentUser.username+"_favResIds")==null){
            dataProviderService.getUserFavResIds()
        }
        String resIds = redisService.(springSecurityService.currentUser.username+"_favResIds")
        [folderDtl:redisService.("folderDtl_"+params.folderId),resIds:resIds,commonTemplate:"true"]
    }
}
