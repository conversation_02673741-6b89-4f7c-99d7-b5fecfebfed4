package com.wonderslate.data

import com.wonderslate.qp.QuestionPaperService
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class QuestionTypesController {

    QuestionPaperService questionPaperService
    def springSecurityService

    @Secured(['ROLE_GPT_MANAGER'])
    def index() {
        redirect(action: "list")
    }

    @Secured(['ROLE_GPT_MANAGER'])
    def list() {
        def result = questionPaperService.getAllQuestionTypes()
        if (result.success) {
            [questionTypes: result.questionTypes]
        } else {
            flash.message = "Failed to retrieve question types: ${result.message}"
            [questionTypes: []]
        }
    }

    @Secured(['ROLE_GPT_MANAGER'])
    def create() {
        [questionType: new QuestionTypes()]
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def save() {
        def questionType = params.questionType
        def createdBy = springSecurityService.currentUser.username

        def result = questionPaperService.createQuestionType(questionType, createdBy)
        if (result.success) {
            flash.message = "Question type created successfully."
            redirect(action: "list")
        } else {
            flash.message = "Failed to create question type: ${result.errors ?: result.message}"
            render(view: "create", model: [questionType: new QuestionTypes(questionType: questionType)])
        }
    }

    @Secured(['ROLE_GPT_MANAGER'])
    @Transactional
    def delete() {
        def id = params.long('id')
        def result = questionPaperService.deleteQuestionTypeFromMaster(id)
        
        if (result.success) {
            flash.message = "Question type deleted successfully."
        } else {
            flash.message = "Failed to delete question type: ${result.message}"
        }
        
        if (request.xhr) {
            def json = [success: result.success, message: flash.message]
            render json as JSON
        } else {
            redirect(action: "list")
        }
    }
}
