package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.PrepInviteRequest
import grails.converters.JSON
import grails.transaction.Transactional

import javax.servlet.http.Cookie

class PrepJoyController {
     def redisService
    DataProviderService dataProviderService
    def index() {
        session['siteId'] = new Integer(27);
        if(session["googleUAId_27"]==null){
            if(redisService.("googleUAId_27")==null) dataProviderService.getGoogleUniversalAnalytics("27")
            session["googleUAId_27"] = redisService.("googleUAId_27")
        }
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }
        ['title':'PrepJoy - Best current affairs preparation']
    }

    def prepJoyNew(){
        if(session["googleUAId_27"]==null){
            if(redisService.("googleUAId_27")==null) dataProviderService.getGoogleUniversalAnalytics("27")
            session["googleUAId_27"] = redisService.("googleUAId_27")
        }
        ['title':'PrepJoy - Best current affairs preparation']
    }


    def currentAffairsDaily(){
        ['title':'Current Affairs - Wonderslate']

    }
    def currentAffairs(){
        ['title':'Current Affairs - Wonderslate']
    }
    @Transactional
    def addUserInvitation() {
        def json
        if(PrepInviteRequest.findByMobile(params.mobile)==null)
        {
            PrepInviteRequest prepInviteRequest = new PrepInviteRequest(name:params.name,mobile:params.mobile)
            prepInviteRequest.save(flush: true, failOnError: true)
        }
        json=["status" :"ok"]
        render json as JSON
    }
}
