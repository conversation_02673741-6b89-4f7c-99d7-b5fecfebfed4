package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.PurchaseOrder
import com.wonderslate.data.ResourceCreatorService
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.log.RazorPayment
import com.wonderslate.publish.BooksPermission
import com.wonderslate.shop.PurchaseService
import com.wonderslate.shop.ShoppingCartOrdersDtl
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.usermanagement.UserRole
import com.wonderslate.usermanagement.WinGenerator
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import groovy.sql.Sql
import org.apache.commons.validator.routines.EmailValidator
import pl.allegro.finance.tradukisto.ValueConverters

import java.text.DateFormat
import java.text.SimpleDateFormat


class SupportController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService
    UtilService utilService
    PurchaseService purchaseService
    ResourceCreatorService resourceCreatorService

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def getUserDetails() {
        Integer siteId = getSiteId(request)
        List users;
        if(params.userName !=null){
            users = User.findAllByUsernameAndSiteId(params.userName,siteId)
        }else if(params.mobile !=null){
            users = User.findAllByMobileAndSiteId(params.mobile,siteId)
        }else if(params.email !=null){
            users = User.findAllByEmailAndSiteId(params.email,siteId)
        }
        List  userlist =null
        if(users.size()>0) {
            userlist = users.collect { user ->
                return [name: user.name,userName:user.username,mobile:user.mobile,email:user.email?user.email:"",state:user.state?user.state:"",district:user.district?user.district:""]
            }
        }
        def json = [ status: userlist?"OK":"not found", usersDetails: userlist]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def getCourseByPaymentId(){
        def course="";
        String sql= "select bm.id,bm.title"+
                " from wsshop.books_mst bm, wsshop.purchase_order po where bm.id=po.item_code and "+
                " po.payment_id='" + params.paymentId + "'"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        if(results.size()>0 ) {
            course = results.collect { courses ->
                return [Id: courses[0],course_name:courses[1]]
            }
        }

        def json = [ status: course?"OK":"No course present for this payment Id",course:course]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def getDateWiseClasses(){
        Integer siteId = getSiteId(request)
        String sql = "SELECT rd.id,rd.res_link,rd.resource_name,rd.res_type,DATE_ADD( rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE) start_time, " +
                " DATE_ADD( rd.test_end_date, INTERVAL '5:30' HOUR_MINUTE) end_time, " +
                " cm.name chapter_name, cm.id chapter_id,bm.title,bm.site_id," +
                "rd.allow_comments,rd.display_comments,rd.video_player,bm.id bookId \n" +
                "FROM resource_dtl rd, chapters_mst cm, books_mst bm\n" +
                "where res_type='Reference Videos' and sharing is null \n" +
                "and rd.test_start_date is not null\n " +
                "and  date(DATE_ADD(rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('" + params.date + "','%d-%m-%Y') \n"+
                "and rd.chapter_id =cm.id and cm.book_id =bm.id and bm.site_id=" + siteId
        " order by start_time desc;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List videoDetails = results.collect { resource ->
           return  [start_time:(""+resource.start_time).replace(':', '~'),end_time:resource.end_time!=null?(""+resource.end_time).replace(':', '~'):null,
                                 class_name: (" "+resource.resource_name).replace(':', ' ').replace(',', '').replace('[', ' ').replace(']', ' ').replace('{', ' ').replace('}', ' '),
                                 chapter_name: (""+resource.chapter_name).replace(':', '~').replace(',', ' ').replace('[', ' ').replace(']', ' ').replace('{', ' ').replace('}', ' '),
                                 course_name: (""+resource.title).replace(':', ' ').replace(',', ' ').replace('[', ' ').replace(']', ' ').replace('{', ' ').replace('}', ' '),
                                 webchat_id: resource.id,
                                 course_id:resource.bookId,
                    deep_link :getDeepLink(resource.chapter_id,resource.id,resource.res_type,resource.bookId,resource.site_id)



           ]


        }

        def json = [status: videoDetails ? "OK" : "No classes Present", "course": videoDetails]
        render json as JSON



    }


    def getDeepLink(chapterId,Long resId,resType,bookId,siteId){
        SiteMst siteMst
        if(siteId!=null) {
            siteMst = dataProviderService.getSiteMst(siteId)
        }
        def firebaseKey="${siteMst!=null?siteMst.fbFirebaseWebAPI:""}";
        def parameters="bookId="+bookId;

        if(chapterId!=null) parameters +="&chapterId="+chapterId;
        if(resId!=null) parameters +="&resId="+resId;
        if(resType!=null) parameters +="&resType="+resType;
        def params = "{" +
                "\"dynamicLinkInfo\": {" +
                " \"domainUriPrefix\":\""+siteMst.domainUriPrefix+"\"," +
                " \"androidInfo\": {" +
                "\"androidPackageName\":\""+siteMst.androidPackageName+"\"" +
                " }," +
                " \"iosInfo\":{\"iosBundleId\": \""+siteMst.iosBundleId+"\"" +
                "}," +
                "\"link\":\""+(siteMst.siteBaseUrl+"/wonderpublish/bookdtl?"+parameters+"\"") +
                "}," +
                "\"suffix\":{" +
                "\"option\":\"SHORT\"" +
                "}" +
                "}"

        RestBuilder rest = new RestBuilder()


        def resp = rest.post('https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key='+firebaseKey) {
            accept("application/json")
            contentType("application/json")
            body(params)
        }
        def json = resp.json

        return json.shortLink

    }
    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def getDateWiseTests() {
        Integer siteId = getSiteId(request)
        String sql = "SELECT rd.id,rd.res_link,rd.resource_name,rd.res_type,DATE_ADD( rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE) start_time, " +
                " DATE_ADD( rd.test_end_date, INTERVAL '5:30' HOUR_MINUTE) end_time, " +
                " DATE_ADD( rd.test_result_date, INTERVAL '5:30' HOUR_MINUTE) result_time, " +
                " cm.name chapter_name, cm.id chapter_id, bm.title,bm.site_id," +
                " bm.id bookId \n" +
                "FROM resource_dtl rd, chapters_mst cm, books_mst bm\n" +
                "where res_type='Multiple Choice Questions' and sharing is null \n" +
                "and rd.test_start_date is not null \n" +
                "and  date(DATE_ADD(rd.test_start_date, INTERVAL '5:30' HOUR_MINUTE)) = STR_TO_DATE('" + params.date + "','%d-%m-%Y') \n" +
                "and rd.chapter_id =cm.id and cm.book_id =bm.id and bm.site_id=" + siteId
        " order by start_time desc;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List videoDetails = results.collect { resource ->
            return [test_id       : resource.id,  start_time: ("" + resource.start_time).replace(':', '~'), end_time: resource.end_time != null ? ("" + resource.end_time).replace(':', '~') : null,
                    class_name        : (" " + resource.resource_name).replace(':', ' ').replace(',', '').replace('[', ' ').replace(']', ' ').replace('{', ' ').replace('}', ' '),
                    chapter_name: ("" + resource.chapter_name).replace(':', '~').replace(',', ' ').replace('[', ' ').replace(']', ' ').replace('{', ' ').replace('}', ' '),
                    course_name : ("" + resource.title).replace(':', ' ').replace(',', ' ').replace('[', ' ').replace(']', ' ').replace('{', ' ').replace('}', ' '),
                    course_id   : resource.bookId,deep_link :getDeepLink(resource.chapter_id,resource.id,resource.res_type,resource.bookId,resource.site_id)]


        }
        def json = [status: videoDetails ? "OK" : "No Test Present", "course": videoDetails]
        render json as JSON
    }




    def  Integer getSiteId(request){
        Integer siteId = new Integer(1)
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }


        return siteId
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def updateUserInfo(){
         Integer siteId = getSiteId(request)
        String status="";
        String password=params.password;
        User user = User.findByUsernameAndSiteId(params.userName,siteId)
        if(user!=null) {
            if (params.email != null && !"".equals(params.email)) {
                user.email = params.email;
            }
            if (password != null && !"".equals(password)) {
                user.password = params.password;
                user.password = springSecurityService.encodePassword(password)
            }
            user.save(failOnError: true, flush: true)
            status="OK"
        }else{
            status="No User Found"
        }
        def json = [ 'status':status]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def addBooktoUser(){
        String status="";
        String invalidbook="";
        String alreadyPresent="";
        String invalidUser="";
        String[] courseId = params.courseId.split(",")
        String[] usernname = params.userName.split(",")
        BooksPermission booksPermission
        for (int j = 0; j < usernname.length; j++) {
            User user=User.findByUsername(usernname[j])
            if(user!=null){
                for (int i = 0; i < courseId.length; i++) {
                    BooksMst booksMst = BooksMst.findById(courseId[i]);
                    if (booksMst != null) {
                        BooksPermission booksPermission1 = BooksPermission.findByBookIdAndUsername(new Long(courseId[i]), usernname[j])
                        if (booksPermission1 == null) {
                            booksPermission = new BooksPermission()
                            booksPermission.bookId = new Long(courseId[i])
                            booksPermission.username =usernname[j]
                            booksPermission.poType= 'ADDEDFORFREE'
                            booksPermission.addedBy = springSecurityService.currentUser.username
                            if(booksMst.validityDays!=null && booksMst.validityDays!="") {
                                Calendar c = Calendar.getInstance()
                                c.add(Calendar.DATE, booksMst.validityDays)
                                booksPermission.expiryDate = c.getTime()
                            }
                            booksPermission.save(failOnError: true, flush: true)
                            status = "Ok"
                        } else {
                            alreadyPresent += "course Id "+courseId[i] + " already present for user "+usernname[j]+""+","
                        }


                    } else {
                        invalidbook += courseId[i]() + ","
                    }
                }
                dataProviderService.getBooksListForUser(usernname[j])
        }else{
                invalidUser += usernname[j] + ","
            }
        }

        def json = [ 'status':status,'invalidCourse':invalidbook?invalidbook.substring(0, invalidbook.length() - 1):"",'alreadyPresentCourse':alreadyPresent?alreadyPresent.substring(0, alreadyPresent.length() - 1):"",'invalidUser':invalidUser?invalidUser.substring(0, invalidUser.length() - 1):""]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])  @Transactional
    def deleteMainandPackageCourseforUser(){
        String invalidBook="";
        String status=""
        String[] mainCourseId = params.mainCourseId.split(",")
       User user=User.findByUsername(params.userName)
        if(user!=null) {
            for (int i = 0; i < mainCourseId.length; i++) {
                BooksMst booksMst = BooksMst.findById(new Long(mainCourseId[i]));
                if (booksMst != null) {

                    BooksPermission.executeUpdate("delete BooksPermission where bookId=" + mainCourseId[i] + " and username='" + params.userName + "'")
                    def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')

                    new Sql(dataSource).execute("DELETE from chapter_access WHERE book_id IN (" + mainCourseId[i] + ") and username='" + params.userName + "'")
                    status="deleted"
                } else {
                    invalidBook = mainCourseId[i] + ","

                }
            }
        }else {
            status="user not found";

        }
        dataProviderService.getBooksListForUser(params.userName);
        def json = ['status':status,'invalidCourse':invalidBook?invalidBook.substring(0, invalidBook.length() - 1):""]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def chapterUpdate() {
        ChaptersMst chaptersMst
        def json
        if(params.chapterName!=null && params.bookId!=null && params.chapterName!="" && params.bookId!="") {
            BooksMst booksMst = BooksMst.findById(new Long(params.bookId));
            if(booksMst!=null) {
                chaptersMst = new ChaptersMst(name: params.chapterName, bookId: new Long(params.bookId))
                chaptersMst.save(failOnError: true)
                dataProviderService.getChaptersList(new Long(params.bookId))
                json = [
                        status: 'OK', 'chapterId': chaptersMst.id, 'newChapter': chaptersMst.name
                ]
            }else{
                json = [
                status: 'fail'
                        ]
            }
        }else{
            json = [
                    status: 'fail'
            ]
        }

        render json as JSON
    }


    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def getUserCourseDetails(){
        def username=params.userName
        User user = User.findByUsername(username)
        if(user!=null) {
            def sql ="select bm.id,bm.title,bm.price,bm.package_book_ids,bp.date_created" +
                    " from books_mst bm, wsuser.books_permission bp" +
                    " where bp.book_id=bm.id and bp.username='" + username + "'   and bp.po_type='PURCHASE' "+
                    " order by bm.id desc";
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            def paymentId
            def date
            List paidbooks = results.collect { book ->
                List packagebooks=null
                PurchaseOrder purchaseOrder=PurchaseOrder.findByItemCodeAndUsername(book[0],username);
                if(purchaseOrder!=null){paymentId=purchaseOrder.paymentId}
                date = book[4];
                if (date != "") {
                    date = (new SimpleDateFormat("yyyy-MM-dd")).format(date)
                }
                if(book[3]!=null && book[3]!=""){
                    String packagebook2=book[3]+"";
                    if(packagebook2.substring(packagebook2.length() - 1).equals(",")){
                        packagebook2=packagebook2.substring(0, packagebook2.length() - 1)
                    }
                    def sqll ="select bm.id,bm.title" +
                            " from books_mst bm"+
                            " where bm.id IN ("+packagebook2+") order by bm.id desc";
                    def dataSourcee= grailsApplication.mainContext.getBean('dataSource_wsuser')
                    def sql11= new Sql(dataSourcee)
                    def resultss = sql11.rows(sqll)
                    packagebooks=resultss.collect { packagebook ->
                        return [package_name: packagebook[1],id: packagebook[0]]
                    }



                }
                return [main_coursename: book[1],id: book[0],payment_amount:book[2],payment_id:paymentId,package_courses:packagebooks,dateAdded:date]
            }
            def sql2 ="select bm.id,bm.title,bm.price,bm.package_book_ids,bp.date_created" +
                    " from books_mst bm, wsuser.books_permission bp" +
                    " where bp.book_id=bm.id and bp.username='" + username + "'  and bp.po_type!='PURCHASE' "+
                    " order by bm.id desc";
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql3 = new Sql(dataSource1)
            def results1 = sql3.rows(sql2)
                  def date1
            List freebooks = results1.collect { book ->
                date1 = book[4];
                if (date1 != "") {
                    date1 = (new SimpleDateFormat("yyyy-MM-dd")).format(date1)
                }
                List packagebooks1=null
                if(book[3]!=null && book[3]!=""){
                    String packagebook1=book[3]+"";
                    if(packagebook1.substring(packagebook1.length() - 1).equals(",")){
                        packagebook1=packagebook1.substring(0, packagebook1.length() - 1)
                    }
                    def sqll ="select bm.id,bm.title" +
                            " from books_mst bm"+
                            " where bm.id IN ("+packagebook1+") order by bm.id desc";
                    def dataSourcee= grailsApplication.mainContext.getBean('dataSource_wsuser')
                    def sql11= new Sql(dataSourcee)
                    def resultss = sql11.rows(sqll)
                    packagebooks1=resultss.collect { packagebook ->
                        return [package_name: packagebook[1],id: packagebook[0]]
                    }
                }
                return [main_coursename: book[1],id: book[0],payment_amount:"Free",package_courses:packagebooks1,dateAdded:date1]
            }

            def sql4 ="SELECT bm.id,bm.title,bud.batch_id,bud.date_created," +
                    " bm.price,bm.package_book_ids," +
                    " cbd.name batch_name " +
                    " FROM wsuser.books_batch_dtl bbd,wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud, books_mst bm" +
                    " where bm.id=bbd.book_id and cbd.id=bbd.batch_id" +
                    " and cbd.status='active' and bud.username='"+username+"'  and bud.batch_id=cbd.id ";
            def dataSource2 = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql5 = new Sql(dataSource2)
            def results2 = sql5.rows(sql4)
            def date2
            List batchbooks = results2.collect { book ->
                date2 = book.date_created;
                if (date2 != "") {
                    date2 = (new SimpleDateFormat("yyyy-MM-dd")).format(date2)
                }
                List batchpackagebooks=null
                if(book[5]!=null && book[5]!=""){
                    def sqll ="select bm.id,bm.title" +
                            " from books_mst bm"+
                            " where bm.id IN ("+book[5]+") order by bm.id desc";
                    def dataSourcee= grailsApplication.mainContext.getBean('dataSource_wsuser')
                    def sql11= new Sql(dataSourcee)
                    def resultss = sql11.rows(sqll)
                    batchpackagebooks=resultss.collect { packagebook ->
                        return [package_name: packagebook[1],id: packagebook[0]]
                    }
                }
                return [main_coursename: book.title,id: book.id,batch_name:book.batch_name,batch_id:book.batch_id,dateAdded:date2,package_courses:batchpackagebooks]
            }



            def json = ['freeCourses':freebooks,'paidCourses':paidbooks,'batchcourses':batchbooks]
            render json as JSON
        }
        else{
            def json = ['status':"User not found"]
            render json as JSON
        }
    }

    def getChapterlistByBookId(){
        List chaptersMst = ChaptersMst.findAllByBookId(new Long(params.bookId))
        def chapters = chaptersMst.collect{chapter ->
            return [chapter_name:chapter.name,chapter_id:chapter.id]
        }
        def json =
                [       'chapters':chapters,
                ]
        render json as JSON

    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def removeUserFromBatch(){
        String status="";
        String invalid="";
      String[] batchId = params.batchId.split(",")
        User user=User.findByUsername(params.userName);
        if(user!=null) {
            if (params.userName != null && batchId != null) {
                for (int i = 0; i < batchId.length; i++) {
                    BatchUserDtl batchUserDtl = BatchUserDtl.findByUsernameAndBatchId(params.userName, new Long(batchId[i]))
                    if (batchUserDtl != null) {
                        batchUserDtl.delete(flush: true)
                        status="OK"
                    }else{
                        invalid +=batchId[i]+","
                    }
                }
                dataProviderService.getBooksListForUser(params.userName)
                dataProviderService.getUserBatchesAsStudent(params.userName);
                dataProviderService.getUserBatchesIds(params.userName)

            }
        }else{
            status="user not found!"

        }
        def json = ["status":status,'error':invalid?invalid.substring(0, invalid.length() - 1)+" either the batch id is incorrect or the user is not present in this batch":""]
        render json as JSON


    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])
    def getCourseList(){
        Integer siteId = getSiteId(request)
        def sql ="select bm.id,bm.title,bm.price,bm.package_book_ids" +
                " from books_mst bm" +
                " where site_id="+siteId+"  and ifnull(bm.price,0)!=0"+
                " order by bm.id desc limit " + params.limit + "";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List paidbooks = results.collect { book ->
            List packagebooks=null
            if(book[3]!=null && book[3]!=""){
                String packagebook=book[3]+"";
                if(packagebook.substring(packagebook.length() - 1).equals(",")){
                    packagebook=packagebook.substring(0, packagebook.length() - 1)
                }
                def sqll ="select bm.id,bm.title" +
                        " from books_mst bm"+
                        " where bm.id IN ("+packagebook+")";
                def dataSourcee= grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql11= new Sql(dataSourcee)
                def resultss = sql11.rows(sqll)
                packagebooks=resultss.collect { piadpackagebook ->
                    return [packagecourse_name: piadpackagebook[1],packagecourse_id: piadpackagebook[0]]
                }
            }
            return [main_course_name: book[1],course_id: book[0],course_amount:book[2],package_courses:packagebooks]
        }
        def sql2 ="select bm.id,bm.title,bm.price,bm.package_book_ids" +
                " from books_mst bm"+
                " where site_id="+siteId+"  and ifnull(price,0)=0"+
                " order by  bm.id desc limit " + params.limit + "";
        def dataSource1 = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql3 = new Sql(dataSource1)
        def results1 = sql3.rows(sql2)
        List freebooks = results1.collect { book1 ->
            List packagebooks1=null
            if(book1[3]!=null && book1[3]!=""){
                String packagebooks12=book1[3]+"";
                if(packagebooks12.substring(packagebooks12.length() - 1).equals(",")){
                    packagebooks12=packagebooks12.substring(0, packagebooks12.length() - 1)
                }
                def sqls ="select bm.id,bm.title" +
                        " from books_mst bm"+
                        " where bm.id IN ("+packagebooks12+")";
                def dataSources= grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql12= new Sql(dataSources)
                def results12 = sql12.rows(sqls)
                packagebooks1=results12.collect { freepackagebook ->
                    return [package_name: freepackagebook[1],id: freepackagebook[0]]
                }
            }
            return [main_coursename: book1[1],id: book1[0],course_amount:book1[2],package_courses:packagebooks1]
        }


        def json = ['paid':paidbooks,'free':freebooks]
        render json as JSON


    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def getexpiryList(){
       String sql = "select bp.book_id, bp.username,"+
                " DATE_ADD(bp.expiry_date, INTERVAL '5:30' HOUR_MINUTE) as expiry_date"+
                " from wsuser.books_permission bp  where bp.book_id="+params.courseId+" and "
        if(params.startTime!=null && params.endTime!=""){
            sql += " date(DATE_ADD(bp.expiry_date, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.startTime+"','%d-%m-%Y') and"+
            " date(DATE_ADD(bp.expiry_date, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.endTime+"','%d-%m-%Y') "
        }
        sql += " order by bp.expiry_date desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def date
        List expiryList = results.collect { resource ->
            User user = dataProviderService.getUserMst(resource.username)
            date = resource.expiry_date;
            if (date != "") {
                date = (new SimpleDateFormat("yyyy-MM-dd")).format(date)
            }
            return ["username":resource.username,"mobile":user.mobile,"expiry_date":date]
        }
        def json = ["data": expiryList ? expiryList : "No Records Found"]
        render json as JSON

    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def transferCourse(){
        String status="";
        Integer siteId = getSiteId(request)
        BooksPermission booksPermission=BooksPermission.findByUsernameAndBookId(params.oldusername,params.courseId)
        BooksPermission booksPermission1=BooksPermission.findByUsernameAndBookId(params.newusername,params.courseId)
        User user=User.findByUsernameAndSiteId(params.oldusername,siteId)
        User user1=User.findByUsernameAndSiteId(params.newusername,siteId)
        if(user!=null && user1!=null) {
            if (booksPermission != null) {
                if(booksPermission1 ==null) {
                    booksPermission.username = params.newusername;
                    booksPermission.save(failOnError: true, flush: true)
                    dataProviderService.getBooksListForUser(params.oldusername)
                    dataProviderService.getBooksListForUser(params.newusername)
                    status = "OK"
                }else{
                    status = "course Id is already present in new username."
                }
            } else {
                status = "course Id is not present in old username to transfer."
            }
        }else
        {
            status = "Failed!Either new username or old username is incorrect."
        }

        def json = ["status":status]
        render json as JSON


    }


    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def addBookToUserByPaymentId(){
        Integer siteId = getSiteId(request)
        def bookId;
        String status = "error"
        BooksMst  booksMst;
        PurchaseOrder purchaseOrder=PurchaseOrder.findByPaymentId(params.paymentId)
        User user = User.findByUsernameAndSiteId(params.userName,siteId)
        if(user!=null && purchaseOrder!=null) {
            if (purchaseOrder != null) bookId = purchaseOrder.itemCode;
            booksMst = dataProviderService.getBooksMst(new Long(bookId))
            Integer poNo
            if (booksMst != null && user != null) {
                BooksPermission booksPermission1=BooksPermission.findByBookIdAndUsername( booksMst.id,user.username)
                if(booksPermission1==null) {
                    BooksPermission booksPermission = new BooksPermission()
                    booksPermission.bookId = booksMst.id
                    booksPermission.username = user.username

                    if (params.paymentId != null && !"".equals(params.paymentId)) {
                        booksPermission.poNo = poNo
                        booksPermission.poType = 'PURCHASE'
                    } else {
                        booksPermission.poType = 'ADDEDFORFREE'
                    }
                    booksPermission.addedBy = springSecurityService.currentUser.username
                    if(booksMst!=null && booksMst.validityDays!=null && booksMst.validityDays!="") {
                        Calendar c = Calendar.getInstance()
                        c.add(Calendar.DATE, booksMst.validityDays)
                        booksPermission.expiryDate = c.getTime()
                    }
                    booksPermission.save(failOnError: true, flush: true)
                    dataProviderService.getBooksListForUser(user.username)
                    status = "Ok"
                }else {
                    status = "book associated with payment id is already present in user library"
                }
            }
        }else{
            status="payment id or username is incorrect"
        }
        def json = ["Status" : status]
        render json as JSON
    }


    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def sendInvoiceToUser(){
        String status="error"
        String entryController=null
        if(params.username!=null && params.courseId!=null && params.correctEmail!=null) {
            String sql = "select if(po.sequence_po is not null,CONCAT('UT',po.sequence_po),po.id) id, po.username,null dummy1,null dummy2, bm.title, po.amount, po.po_method, null dummy3, ph.name publisher,"+
                    " DATE_FORMAT(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE),'%d-%m-%Y %H\\:%i\\:%s'),"+
                    " bm.cover_Image, po.payment_Id, bm.id book_id,po.sequence_po"+
                    " from books_mst bm, purchase_order po,  publishers ph where"+
                    " bm.id=po.item_code and"+
                    " po.item_code = '" + params.courseId + "' and  po.username = '" + params.username + "'" +
                    "and ph.id=bm.publisher_id"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            DateFormat df
            Integer siteId = getSiteId(request)
            SiteMst sm = dataProviderService.getSiteMst(siteId)
            SiteDtl siteDtl = dataProviderService.getSiteDtl(siteId)
            entryController = session["entryController"]
            def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
            def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
            if(results!=null && results.size()>0) {
                def str1 = ""
                List data = results.collect {sale ->
                    if("eutkarsh".equals(""+siteName) ||"thewinnersinstitute".equals(""+siteName)) {
                        try {
                            ValueConverters converter = ValueConverters.ENGLISH_INTEGER
                            Integer rupee = (Integer) sale[5]
                            str1 = "Rupees "+converter.asWords(rupee)
                            Integer paisa = (Integer) Math.round((sale[5] - rupee) * 100)
                            if(paisa>0) str1 += "Paise "+converter.asWords(paisa)
                        } catch (Exception e){
                            str1 = ""
                        }
                    }
                    User user = dataProviderService.getUserMst(sale[1])
                    try {
                        if(userManagementService.validateEmail(params.correctEmail,siteId)) {
                            String ipAddress = utilService.getIPAddressOfClient(request);
                            if (siteDtl !=null){
                                userManagementService.invoiceEmail(siteName,siteId, user.mobile,params.correctEmail,
                                        user.name, grailsApplication.config.grails.po.invoice.bcc,
                                        sale.id, sale[11],sale[5]+"",sale[12],
                                        sale[4], sale[10], sale[8], sale[6], str1, sale[9],
                                        springSecurityService.currentUser.state, clientName, ipAddress,
                                        entryController, siteDtl.logo, siteDtl.addressLine1, siteDtl.addressLine2, siteDtl.gstNumber, siteDtl.emailAddress, siteDtl.websiteLink, siteDtl.jurisdictionPlace, siteDtl.jurisdictionState, siteDtl.companyName)
                            }else{
                                userManagementService.invoiceEmail(siteName,siteId, user.mobile,params.correctEmail,
                                        user.name, grailsApplication.config.grails.po.invoice.bcc,
                                        sale.id, sale[11],sale[5]+"",sale[12],
                                        sale[4], sale[10], sale[8], sale[6], str1, sale[9],
                                        springSecurityService.currentUser.state, clientName, ipAddress,
                                        entryController, null, null, null, null, null, null, null, null, null)
                            }

                        }
                    } catch (Exception e){
                        println "Purchase Invoice email failed "+e.toString()
                    }
                    status="Ok"
                }
            }
        }
            def json = ["Status" : status]
            render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
     def refund(){
        Integer siteId = getSiteId(request)
        def paymentId=params.paymentId;
        def json = purchaseService.refund(paymentId,siteId)
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def changeUserCourseValidity(){
        String status="error"
        Integer days=Integer.parseInt(params.days)
        BooksMst booksMst=BooksMst.findById(new Long(params.bookId));
        User user=User.findByUsername(params.username);
        if(user!=null && booksMst!=null) {
            BooksPermission booksPermission = BooksPermission.findByUsernameAndBookId(params.username, new Long(booksMst.id))
            if (booksMst.validityDays != null && booksMst.validityDays != "" && booksPermission != null) {
                Calendar c = Calendar.getInstance()
                c.add(Calendar.DATE, days)
                booksPermission.expiryDate = c.getTime()
                booksPermission.save(failOnError: true, flush: true)
                status = "OK"
                dataProviderService.getBooksListForUser(params.username)
            }else{
                status="course is not present in user library or course doesn't have validity days"
            }
        }else{
            status="no user found or invalid course id"
        }
        def json = ["Status" : status]
        render json as JSON

    }


    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def  changeCourseValidity(){
        String status="No records";
        Integer days=Integer.parseInt(params.days)
        String sql = "select bp.book_id, bp.username,"+
                " DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created"+
                " from wsuser.books_permission bp, books_mst bm  where bp.book_id=bm.id and bm.validity_days>0 and bp.book_id="+params.courseId+" and "
        if(params.purchasingStartTime!=null && params.purchasingEndTime!=""){
            sql += " date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('"+params.purchasingStartTime+"','%d-%m-%Y') and"+
                    " date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('"+params.purchasingEndTime+"','%d-%m-%Y') "
        }
        sql += " order by bp.date_created desc";
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List dateadded = results.collect { user ->
            BooksPermission booksPermission=BooksPermission.findByUsernameAndBookId(user.username,new Long(user.book_id))
            Calendar c = Calendar.getInstance()
            c.add(Calendar.DATE, days)
            booksPermission.expiryDate = c.getTime()
            booksPermission.save(failOnError: true, flush: true)
            status="OK"
            dataProviderService.getBooksListForUser(user.username)
        }
        def json = ["Status" : status]
        render json as JSON


    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER'])@Transactional
    def addVideos(){
        def resId = resourceCreatorService.addlink(params,request,session)
            def json =  ["Status":"OK", 'resId':resId]
            render json as JSON
    }

    def checkEmailValidation(){
        def email =params.email
        def status="OK"
        //added below to avoid unnecessary email check for invalid formats
        if(!EmailValidator.getInstance().isValid(email)) {
            status ="Fail"
        }
        def json =  ["Status":status]
        render json as JSON
    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def getAllPayments() {
        if("submit".equals(params.mode)) {
            def id = params.id
            def email = params.email?.toLowerCase()
            def mobile = params.mobile?.toLowerCase()
            def name = params.name?.toLowerCase()
            def fromDate = params.fromDate ? Date.parse("dd/MM/yyyy", params.fromDate) : null
            def toDate = params.toDate ? Date.parse("dd/MM/yyyy", params.toDate) : null


            def criteria = RazorPayment.createCriteria()
            def payments = criteria.list(maxResults: 20) {
                if (id) {
                    eq('razorPaymentId', id)
                }
                if (email) {
                    ilike('email', "%${email}%")
                }
                if (mobile) {
                    ilike('contact', "%${mobile}%")
                }
                if (fromDate && toDate) {
                    between('dateCreated', fromDate, toDate)
                } else if (fromDate) {
                    ge('dateCreated', fromDate)
                } else if (toDate) {
                    le('dateCreated', toDate)
                }
                order('dateCreated', 'desc')
            }
            def json = [payments: payments]

            render json as JSON
        }


    }

    @Secured(['ROLE_USER_LOGIN_RESET_MANAGER']) @Transactional
    def getPaymentDetails(){
        String status = "Payment Id not found"
        RazorPayment razorPayment = RazorPayment.findByRazorPaymentId(params.razorPayId)
        if(razorPayment!=null){
            status = "<b>Shopping Cart Id=${razorPayment.shoppingCartId}</b><br>";
            List cart  = ShoppingCartOrdersDtl.findAllByCartMstId(Integer.parseInt(razorPayment.shoppingCartId))
            cart.each { item ->
                BooksMst booksMst = dataProviderService.getBooksMst(new Integer(""+item.bookId))
                status +="<b>Book Id</b> : ${item.bookId} <b>Title:</b> ${booksMst.title}<br>"
            }
        }
        def json = [details : status]
        render json as JSON
    }

   @Secured(['ROLE_SUPPORT_MANAGER']) @Transactional
    def addUser(){
       if("submit".equals(params.mode)) {
           Integer siteId = new Integer(params.siteId)
           String username

           if (params.mobile != null && !"".equals(params.mobile)) username = "" + siteId + "_" + params.mobile
           else username = "" + siteId + "_" + params.email
           User user = User.findByUsername(username)
           //first check the user exists
           if (user == null) {
               //create the user
               String name = params.name
               String password = params.password
               String mobile = params.mobile
               String email = params.email
               String pincode = params.pincode
               String city = params.city
               String teacher = params.teacher
               WinGenerator winGenerator = new WinGenerator()
               winGenerator.save(failOnError: true)
               user = new User(username: username, name: name, password: password, mobile: mobile, email: email, win: winGenerator.id,
                       siteId: siteId, pincode: pincode, city: city, teacher: teacher)
               user.save(failOnError: true, flush: true)
               //add appropriate roles
               Role role = Role.findByAuthority("ROLE_USER")
               UserRole.create(user, role, true)
               role = Role.findByAuthority("ROLE_CAN_ADD")
               UserRole.create(user, role, true)
               role = Role.findByAuthority("ROLE_CAN_UPLOAD")
               UserRole.create(user, role, true)
               def json = ["Status": "User created", "username": username]
               render json as JSON
           } else {
               def json = ["Status": "User already exists"]
               render json as JSON
           }
       }else{
           List sitesList
           if("1".equals(""+session["siteId"])) sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
           else sitesList = SiteMst.findAllById(new Integer(""+session["siteId"]))
           [title:"Manage Users",sitesList:sitesList]
       }
   }

    @Secured(['ROLE_SUPPORT_MANAGER']) @Transactional
    def updateUser(){
        String username = params.siteId+"_"+params.user
        println("username = "+username)
        User user = User.findByUsername(username)
        if(user!=null){
            String addedBooks=""

            if(params.chatTokensBalance) user.chatTokensBalance = new Integer(params.chatTokensBalance)
            if(params.bookId){
                String[] bookIds = params.bookId.split(",")
                for(String bookId : bookIds){
                    BooksPermission booksPermission = BooksPermission.findByUsernameAndBookId(params.username, new Long(bookId))
                    if(booksPermission==null){
                        BooksMst booksMst = BooksMst.findById(new Long(bookId))
                        if(!"".equals(addedBooks)) addedBooks+=","+booksMst.title
                        else addedBooks+=booksMst.title
                        booksPermission = new BooksPermission()
                        booksPermission.username = user.username
                        booksPermission.bookId = new Long(bookId)
                        booksPermission.poType = "ADDEDFORFREE"
                        booksPermission.addedBy = springSecurityService.currentUser.username
                        booksPermission.bookType = params.bookType
                        booksPermission.chatTokensBalance = 10
                        booksPermission.save(failOnError: true, flush: true)
                    }
                }

            }
            if(params.affliationCd) user.affliationCd = params.affliationCd
            user.save(failOnError: true, flush: true)
            def json = ["Status": "User updated", "addedBooks": addedBooks]
            render json as JSON
        }else{
            def json = ["Status": "User not found"]
            render json as JSON
        }
    }

    @Secured(['ROLE_SUPPORT_MANAGER']) @Transactional
    def getUserInfo(){
        String username = params.siteId+"_"+params.user
        User user = User.findByUsername(username)
        if(user){
            def json = ["Status": "User found", "user": user]
            render json as JSON
        }else{
            def json = ["Status": "User not found"]
            render json as JSON
        }
    }

    @Secured(['ROLE_SUPPORT_MANAGER'])
    def findScratchCode() {
        // Display the form
        [title: "Find Scratch Code Usage"]
    }

    @Secured(['ROLE_SUPPORT_MANAGER']) @Transactional
    def getScratchCodeDetails() {
        if("submit".equals(params.mode)) {
            Integer siteId = getSiteId(request)
            String scratchCode = params.scratchCode
            List usageDetails = []

            if(scratchCode) {
                // Find all books_permission records with matching book_code
                String sql = "SELECT bp.book_id, bp.username, DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE) as date_created, " +
                        "u.name, u.mobile, bm.title " +
                        "FROM wsuser.books_permission bp " +
                        "JOIN wsuser.user u ON bp.username = u.username " +
                        "JOIN wsuser.books_mst bm ON bp.book_id = bm.id " +
                        "WHERE bp.book_code = '" + scratchCode + "' " +
                        "AND bp.username LIKE '" + session["siteId"] + "_%'"

                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)

                if(results && results.size() > 0) {
                    usageDetails = results.collect { record ->
                        def dateAdded = ""
                        if(record.date_created) {
                            // Format as day-month(in words)-year
                            dateAdded = new SimpleDateFormat("dd-MMMM-yyyy").format(record.date_created)
                        }

                        return [
                            name: record.name,
                            mobile: record.mobile,
                            bookTitle: record.title,
                            dateAdded: dateAdded
                        ]
                    }
                }
            }

            def json = [status: usageDetails ? "OK" : "not found", usageDetails: usageDetails]
            render json as JSON
        }
    }
}
