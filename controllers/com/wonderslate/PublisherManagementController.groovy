package com.wonderslate.usermanagement

import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BannersMst
import com.wonderslate.data.BooksMst
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.publish.Publishers
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest

class PublisherManagementController {
    DataProviderService dataProviderService
    def redisService
    UtilService utilService
    Gson gson = new Gson();

    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    @Transactional
    def addPublisher() {
        SiteMst siteMst = SiteMst.findById(getSiteId(request))
        Boolean hasAccess=false
        if(session["userdetails"].publisherId==null||params.pubId.equals(""+session["userdetails"].publisherId))hasAccess=true
        if(hasAccess){
            Publishers publishers = new Publishers()
            List bannersMstList = new ArrayList()

            String sql = "SELECT urlname FROM wsshop.publishers;";
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            List pubUrls = []
            results.collect { rows ->
                if (rows.urlname != null)pubUrls.add("" + rows.urlname)
            }
            if (params.pubId) {
                //edit
                publishers = Publishers.findById(new Long(params.pubId))
                bannersMstList = BannersMst.findAllBySiteIdAndPublisherId(getSiteId(request), new Long(params.pubId));
                if (publishers != null) {
                    [
                            pub           : publishers,
                            puburls       : pubUrls,
                            bannersMstList: bannersMstList,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))
                    ]
                }
            }else{
                //add new
                [
                        pub           : publishers,
                        puburls       : pubUrls,
                        bannersMstList: bannersMstList,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))
                ]
            }
        }else{
            redirect(controller: siteMst.siteName, action: "index")
        }
    }

    @Transactional
    @Secured(['ROLE_WS_CONTENT_ADMIN'])
    def updatePublisher() {
        SiteMst siteMst = SiteMst.findById(getSiteId(request))
        Boolean hasAccess=false
        if(session["userdetails"].publisherId==null||params.id.equals(""+session["userdetails"].publisherId))hasAccess=true
        if(hasAccess){
            Long id
            String name, website, contactPerson, email, mobile, urlname, city, state, country, backgroundColor, tagline,  filename, showVideoOnlyInApp,
                    publisherNameForTitle,siteTitle,siteDescription,keywords,sendEbookEmail,printBookShare,ebookShareOnWS,ebookShareOnWhitelabel
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
                //todo
            } else {
                if (params.id != "" && params.id != null) id = new Long(params.id)
                name = params.name
                website = params.website
                contactPerson = params.contactPerson
                email = params.email
                mobile = params.mobile
                urlname = params.urlname
                city = params.city
                state = params.state
                country = params.country
                backgroundColor = params.backgroundColor
                tagline = params.tagline
                showVideoOnlyInApp = params.showVideoOnlyInApp
                publisherNameForTitle = params.publisherNameForTitle
                siteTitle = params.siteTitle
                siteDescription = params.siteDescription
                keywords = params.keywords
                sendEbookEmail = params.sendEbookEmail
                printBookShare = params.printBookShare
                ebookShareOnWS = params.ebookShareOnWS
                ebookShareOnWhitelabel = params.ebookShareOnWhitelabel
            }

            if (params.id != "" && params.id != null) {
                Publishers publishers = Publishers.findById(id)
                if (name != null) publishers.name = name
                if (website != null) publishers.website = website
                if (contactPerson != null) publishers.contactPerson = contactPerson
                if (mobile != null) publishers.mobile = mobile
                if (email != null) {
                     publishers.email = email
                }
                if (urlname != null) publishers.urlname = urlname
                if (city != null) publishers.city = city
                if (state != null) publishers.state = state
                if (country != null) publishers.country = country
                if (backgroundColor != null) publishers.backgroundColor = backgroundColor
                if (tagline != null) publishers.tagline = tagline
                if(showVideoOnlyInApp!=null) publishers.showVideoOnlyInApp = showVideoOnlyInApp
                if(publisherNameForTitle!=null) publishers.publisherNameForTitle = publisherNameForTitle
                if(siteTitle!=null) publishers.siteTitle = siteTitle
                if(siteDescription!=null) publishers.siteDescription = siteDescription
                if(keywords!=null) publishers.keywords = keywords
                if(sendEbookEmail!=null) publishers.sendEbookEmail = sendEbookEmail
                if(printBookShare!=null) publishers.printBookShare = new Integer(printBookShare)
                if(ebookShareOnWS!=null) publishers.ebookShareOnWS = new Integer(ebookShareOnWS)
                if(ebookShareOnWhitelabel!=null) publishers.ebookShareOnWhitelabel = new Integer(ebookShareOnWhitelabel)
                publishers.siteId = getSiteId(request)

                if (!file.empty) {
                    File uploadDir = new File("upload/publisher/" + publishers.id)
                    if (!uploadDir.exists()) uploadDir.mkdirs()
                    filename = file.originalFilename
                    filename = filename.replaceAll("\\s+", "")
                    //saving original image finally
                    file.transferTo(new File(uploadDir.absolutePath + "/" + file.filename))
                    publishers.logo = filename
                }
                publishers.save(failOnError: true, flush: true)
                dataProviderService.getPublishers(getSiteId(request));
                redirect(controller: "publisherManagement", action: "addPublisher", params: [pubId: publishers.id])
            } else {
                Publishers publishers = new Publishers(
                        siteId: getSiteId(request),
                        name: name,
                        website: website,
                        contactPerson: contactPerson,
                        email: email,
                        mobile: mobile,
                        urlname: urlname,
                        city: city,
                        state: state,
                        country: country,
                        backgroundColor: backgroundColor,
                        tagline: tagline,
                        showVideoOnlyInApp: showVideoOnlyInApp,
                        publisherNameForTitle:publisherNameForTitle,
                        siteTitle: siteTitle,
                        siteDescription: siteDescription,
                        keywords: keywords,
                        sendEbookEmail: sendEbookEmail,
                        printBookShare: printBookShare!=null?new Integer(printBookShare):null,
                        ebookShareOnWS: ebookShareOnWS!=null?new Integer(ebookShareOnWS):null,
                        ebookShareOnWhitelabel: ebookShareOnWhitelabel!=null?new Integer(ebookShareOnWhitelabel):null
                )

                publishers.save(failOnError: true, flush: true)

                if (!file.empty) {
                    File uploadDir = new File("upload/publisher/" + publishers.id)
                    if (!uploadDir.exists()) uploadDir.mkdirs()
                    filename = file.originalFilename
                    filename = filename.replaceAll("\\s+", "")
                    //saving original image finally
                    file.transferTo(new File(uploadDir.absolutePath + "/" + file.filename))
                    publishers.logo = filename
                    publishers.save(failOnError: true, flush: true)
                }

                dataProviderService.getPublishers(getSiteId(request))
                redirect(controller: "publisherManagement", action: "addPublisher", params: [pubId: publishers.id])
            }
        }else{
            redirect(controller: siteMst.siteName, action: "index")
        }
    }

    def Integer getSiteId(request) {
        Integer siteId = new Integer(1);
        if (session["siteId"] != null) {
            siteId = (Integer) session["siteId"]
        } else {
            def jsonObj = request.JSON
            if (jsonObj.siteId != null) siteId = new Integer(jsonObj.siteId);
            else if (params.siteId != null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    @Transactional
    def showPublisherImage(long id) {
        try {
            Publishers publishers = Publishers.findById(id)
            if (publishers.id != null) {
                def file = new File("upload/publisher/" + publishers.id + "/" + publishers.logo)

                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${publishers.logo}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        } catch (Exception e) {
            println("Exception in showPublisherImage " + e.toString())
            render "";

        }
    }

    def getPublisherCategories() {
        def siteId = utilService.getSiteId(request, session)
        def publisherId = params.publisherId
        if (redisService.("publisherBookCategories_" + siteId + "_" + publisherId) == null) {
            dataProviderService.getPublisherBookCategories(siteId, publisherId)
        }

        def json = [categories: redisService.("publisherBookCategories_" + siteId + "_" + publisherId), topLevel: redisService.("publisherTopLevel_" + siteId + "_" + publisherId)]
        render json as JSON
    }

    def getPublisherLatestBooks() {
        def siteId = utilService.getSiteId(request, session)
        def publisherId = params.publisherId
        if (redisService.("latestPublisherbooksList_" + siteId + "_" + publisherId) == null) {
            dataProviderService.getPublisherLatestBooks(siteId, publisherId)
        }

        def json = [latestBooks: dataProviderService.getPublisherLatestBooks(siteId, publisherId)]
        render json as JSON
    }

    @Transactional
    def publisher() {
        Publishers publishers
        if(params.id !=null && params.id!=""){
            publishers = Publishers.findById(new Long(params.id))
        }
        else if(params.urlname !=null && params.urlname !=""){
            publishers = Publishers.findByUrlname(params.urlname)
            if(publishers!=null){
                session.setAttribute('publisherLogo', publishers.id)
            }
        }else{
            return redirect(controller: "books", action: "index")
        }
        if(publishers==null) return redirect(controller: "books", action: "index")

        if (redisService.("publisherBookCategories_" + utilService.getSiteId(request, session) + "_" + publishers.id) == null) {
            dataProviderService.getPublisherBookCategories(utilService.getSiteId(request, session), publishers.id)
        }
        if (redisService.("latestPublisherbooksList_" + utilService.getSiteId(request, session) + "_" + publishers.id) == null) {
            dataProviderService.getPublisherLatestBooks(utilService.getSiteId(request, session), publishers.id)
        }
        String title = publishers.name+" eBooks on Wonderslate"
        [
                publisher  : publishers,
                banners    : gson.toJson(BannersMst.findAllBySiteIdAndPublisherId(getSiteId(request), publishers.id),new TypeToken<List>() {}.getType()),
                categories : redisService.("publisherBookCategories_" + utilService.getSiteId(request, session) + "_" + publishers.id),
                topLevel   : redisService.("publisherTopLevel_" + utilService.getSiteId(request, session) + "_" + publishers.id),
                latestBooks: redisService.("latestPublisherbooksList_" + utilService.getSiteId(request, session) + "_" + publishers.id),
                title : title,
                commonTemplate : "true"
        ]

    }

    def checkBookId(){
        String siteIdList;
        Integer siteId=getSiteId(request)
            if (redisService.("siteIdList_" + siteId) == null) {
                dataProviderService.getSiteIdList(siteId)
            }
        siteIdList = redisService.("siteIdList_"+siteId)
        def bookId = params.bookId
        def booksMst = BooksMst.findByIdAndSiteIdInList(new Long(bookId),Arrays.asList(siteIdList.split(",")))
        if(booksMst==null){
            def json = [
                    status: "Book with given Id not Available. Please check book Id"
            ]
            render json as JSON
        }else{
            def json = [
                    status: "OK",
            ]
            render json as JSON
        }
    }

    @Transactional
    @Secured(["ROLE_WS_CONTENT_ADMIN"])
    def publisherReport(){
        List publishers
        if(session["userdetails"].publisherId==null) {
            if (redisService.("publishers_" + getSiteId(request)) == null) {
                dataProviderService.getPublishers(getSiteId(request))
            }
            publishers =new JsonSlurper().parseText(redisService.("publishers_"+getSiteId(request)))
        }else{
            publishers = Publishers.findAllById(session["userdetails"].publisherId)
        }
        [publishers: publishers]
    }
}
