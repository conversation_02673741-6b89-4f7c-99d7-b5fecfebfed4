package com.wonderslate

import com.wonderslate.publish.Publishers
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import pl.touk.excel.export.WebXlsxExporter

class PartnerController {

    @Transactional @Secured(['ROLE_USER'])
    def index() {
        User user = session["userdetails"]
        if(user!=null && user.affliationCd!=null) {
            String sql = "SELECT \n" +
                    "    DATE_FORMAT(DATE(DATE_ADD(date_created, INTERVAL 330 MINUTE)), '%d-%m-%Y') AS day, \n" +
                    "    COUNT(*) AS record_count \n" +
                    "FROM \n" +
                    "    wslog.sales_affiliation_log \n" +
                    "WHERE \n" +
                    "    DATE_ADD(date_created, INTERVAL 330 MINUTE) >= NOW() - INTERVAL 7 DAY \n" +
                    "    AND scd = '"+user.affliationCd+"'\n" +
                    "GROUP BY \n" +
                    "    day \n" +
                    "ORDER BY \n" +
                    "    day DESC;"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            String serverUrl = request.getScheme()+"://"+request.getServerName()+
                    ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                            "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                            request.getServerPort())

            String   siteLink=serverUrl+"?scd="+session["userdetails"].affliationCd
            String storeLink=serverUrl+"/sp/"+session["siteName"]+"?scd="+session["userdetails"].affliationCd+"&mode=store"
            [results:results,siteLink:siteLink,storeLink:storeLink]
        }else{
            render "Invalid Access"
        }
    }

    @Transactional @Secured(['ROLE_USER'])
    def salesReportByPartner(){
        User user = session["userdetails"]
        if(user!=null && user.affliationCd!=null) {
            String sql = "SELECT po.id,po.payment_id,DATE_FORMAT(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE), '%d-%m-%Y %H:%i') as date_created,po.amount,sc.deliver_costs,po.book_type,bm.title,bm.id bookId,po.cart_mst_id,po.site_id\n" +
                    "FROM purchase_order po,shopping_cart_orders_mst sc,books_mst bm\n" +
                    "where   po.cart_mst_id = sc.id\n" +
                    "and bm.id=po.item_code\n" +
                    "and po.affiliation_cd='" + user.affliationCd + "'\n" +
                    "AND DATE(DATE_ADD(po.date_created,INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.startDate + "','%d-%m-%Y')\n" +
                    "AND DATE(DATE_ADD(po.date_created,INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.endDate + "','%d-%m-%Y')"
            println("sql: " + sql)

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new SafeSql(dataSource)
            def results = sql1.rows(sql)
            if ("download".equals(params.mode)) {
                List headers = ["Date Created", "Amount",  "Book Type"]
                List withProperties = [ "date_created", "amount",  "book_type"]

                def fileName = "Data_" + (params.startDate != "" ? params.startDate + "_" : "FromAny_") +
                        (params.endDate != "" ? params.endDate + "_" : "ToAny_") + (new Random()).nextInt(9999999) + ".xlsx"
                new WebXlsxExporter().with {
                    setResponseHeaders(response, fileName)
                    fillHeader(headers)
                    add(results, withProperties)
                    save(response.outputStream)
                }
            } else {
                def json = [results: results, 'status': results ? "OK" : "Nothing present"]
                render json as JSON
            }
        }else{
            render "Invalid Access"
        }
    }
}
