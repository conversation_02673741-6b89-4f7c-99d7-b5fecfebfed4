package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.UtilService
import com.wonderslate.usermanagement.User
import grails.converters.JSON

class WlibraryController {

    DataProviderService dataProviderService
    def redisService
    UtilService utilService
    def springSecurityService
    def library(){
        if (session.getAttribute("userdetails") == null&&springSecurityService.currentUser!=null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
        [showLibrary:utilService.hasLibraryAccess(request)]
    }

    def getBookCategories(){
        if(redisService.("bookCategories_"+getSiteId(request))==null) {
            dataProviderService.getBookCategories(getSiteId(request))

        }

        if(redisService.("startingBooksList_"+getSiteId(request))==null) {
            dataProviderService.getBooksListEvidya(getSiteId(request));
        }
        def books,booksTagDtl
        books = redisService.("startingBooksList_"+getSiteId(request));

        if(redisService.("bookstag_"+params.level.replaceAll("\\s+",""))==null){
            dataProviderService.getBooksTagList('College');
        }
        String categories = redisService.("bookCategories_"+getSiteId(request))

        booksTagDtl = redisService.("bookstag_" + params.level.replaceAll("\\s+", ""))

        def json
        json = ['results':categories,
                'books':books,
                'booksTag' : booksTagDtl,
                'status':(categories)?"OK":"Nothing present",
                'bookCategories':'true'
        ]


        render json as JSON
    }

    def Integer getSiteId(request){
        Integer siteId = new Integer(1)
         println("Session site id="+session["siteId"])
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }
}
