package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.User
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional

import javax.servlet.http.Cookie

class PrepcapsuleController {

    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def redisService
    WsshopService wsshopService


    @Transactional
    def setUserSession(){
        Cookie cookie = new Cookie("siteName","prepcapsule")
        cookie.path = "/"
        response.addCookie(cookie)
        session['siteId'] = new Integer(45);
        session.setAttribute("entryController", "prepcapsule");
        session.setAttribute("siteName", "PrepCapsule")
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }


        if(springSecurityService.currentUser!=null){
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }
        }


        if(session["activeCategories_45"] == null) {
            if(redisService.("activeCategories_45")==null) wsshopService.activeCategories(new Integer(45))
            session["activeCategories_45"] = redisService.("activeCategories_45")
        }
        if(session["activeCategoriesSyllabus_45"] == null) {
            if(redisService.("activeCategoriesSyllabus_45")==null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(45))
            session["activeCategoriesSyllabus_45"] = redisService.("activeCategoriesSyllabus_45")
        }


        if(session["googleUAId_45"]==null){
            if(redisService.("googleUAId_45")==null) dataProviderService.getGoogleUniversalAnalytics("45")
            session["googleUAId_45"] = redisService.("googleUAId_45")
        }
        return
    }
    def index(){
        setUserSession()
        println("in prepcapsule index")
        [commonTemplate:"true"]
    }
    @Transactional
    def store() {

        setUserSession()
        println("in prepcapsule store")
        [commonTemplate:"true"]

    }
}
