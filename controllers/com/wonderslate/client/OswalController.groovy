package com.wonderslate.client

import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import grails.plugin.springsecurity.SpringSecurityService

import javax.servlet.http.Cookie

class OswalController {

    SpringSecurityService springSecurityService
    UtilService utilService

    def index() {
        Cookie cookie = new <PERSON>ie("siteName", "oswal")
        cookie.path = "/"
        response.addCookie(cookie)
        session['siteId'] = new Integer(22);
        session.setAttribute("entryController", "oswal");
        if ("browse".equals(params.mode) && springSecurityService.currentUser!=null) {
            redirect([uri: '/library'])
            return
        }
        def siteMst = SiteMst.findById(session['siteId'])
        [otpReg: siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,showLibrary:utilService.hasLibraryAccess(request,22),title:"Oswal"]
    }
}
