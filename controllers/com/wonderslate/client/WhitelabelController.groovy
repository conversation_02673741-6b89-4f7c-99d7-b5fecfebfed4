package com.wonderslate.client

import com.google.api.client.json.Json
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.institute.InstituteMst
import com.wonderslate.publish.BooksPermission
import com.wonderslate.seo.UrlMapping
import com.wonderslate.shop.WsshopService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import grails.converters.JSON

class WhitelabelController {

    def redisService
    DataProviderService dataProviderService
    WsshopService wsshopService

    @Transactional
    def index() {
        println("WhitelabelController.index()")
        String url = request.getRequestURL()
        //String url = params.url
        String controller = "books"
        String action = "ebooks"
        boolean paramsPresent = false
        String parameters = ""


        String subdomain = ""
        if(url.indexOf("http://localhost")>-1){
             subdomain = "localhost"
        }
        else if(url.indexOf("www")>-1) {
             subdomain = url.substring(url.indexOf('.') + 1, url.indexOf(".", url.indexOf(".") + 1))

        }
        else{
             subdomain = url.substring(url.indexOf(':') + 3, url.indexOf('.'))

         }
         //Step 1 -  check the subdmain mapping from UrlMapping table
        if(redisService.("url_"+subdomain)==null){
            UrlMapping urlMapping = UrlMapping.findBySubDomain(subdomain)
            if(urlMapping!=null){
                redisService.("url_"+subdomain)  = urlMapping.url
            }
        }
        if(redisService.("url_"+subdomain)!=null) {
            forward(uri:redisService.("url_"+subdomain))
        }else{
            println("subdomain : "+subdomain)
            //Step 2 : Check if this is whitelabel
            if(redisService.("siteNames")==null) {
                redisService.("siteNames") = "set"
                List sites = SiteMst.findAll()
                sites.each { site ->
                    redisService.("siteNames_"+site.siteName) = ""+site.id

                }
            }

            if(redisService.("siteNames_"+subdomain)!=null){
                //add the entry controller information
                SiteMst siteMst = dataProviderService.getSiteMst(new Integer(redisService.("siteNames_"+subdomain)))
                if("true".equals(siteMst.prepjoySite)){
                     controller = "prepjoy"
                     action = siteMst.siteName
                }else {
                    println("siteMst : "+siteMst.siteName)
                    SiteDtl siteDtl = SiteDtl.findBySiteId(siteMst.id)
                    if(siteDtl!=null){
                        if(siteDtl.showLandingPage!=null)
                        forward(uri:"/sp/"+siteMst.siteName)
                        else forward(uri:"/sp/"+siteMst.siteName+"/store")
                        return
                    }
                    else{
                        println("siteMst in else: "+siteMst.siteName)
                        controller = siteMst.siteName
                        action = "index"
                    }

                }
            }else{
                // Step 3 - check institution subdomain.
                if(redisService.("institutionsUrl_"+subdomain)==null){
                    String sql = "select id,urlname from institute_mst where urlname='"+subdomain+"'";
                    def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                    def sql1 = new Sql(dataSource)
                    def results = sql1.rows(sql);
                    if(results.size()>0) redisService.("institutionsUrl_"+subdomain) = results[0][0]
                }
                if(redisService.("institutionsUrl_"+subdomain)!=null){
                        controller = "institute"
                        action = "index"
                        paramsPresent = true
                       forward(controller:controller, action:action, params:[urlname:subdomain])
                }
            }
        }
        if(!paramsPresent)
        forward(controller:controller, action:action)
        return
    }

    @Transactional
    def redirector(){

        String redirectionType = params.redirectionType
        String redirectionTarget = params.redirectionTarget
        if("inst".equals(redirectionType)){
            InstituteMst instituteMst = dataProviderService.getInstituteMst(new Long(redirectionTarget))
            if(instituteMst.urlname!=null)
            redirect(url: "https://"+instituteMst.urlname+".wonderslate.com")
            else
                redirect(uri: '/')
        }else{
            redirect(uri: '/')
        }

    }

    @Transactional
    def recharge(){
        List rechargeOptions =  new JsonSlurper().parseText(wsshopService.getRechargeOptions(session["siteId"]))
        println("number of recharge options : "+rechargeOptions.size())
        [rechargeOptions:rechargeOptions]
    }

    @Transactional
    def getRechargeOptions(){
        List rechargeOptions =  new JsonSlurper().parseText(wsshopService.getRechargeOptions(new Integer(params.siteId)))
        def json = [rechargeOptions:rechargeOptions]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def migrateBooks(){
        if("submit".equals(params.mode)){
            String[] bookIds = params.bookIds.split(",")
            String siteId = params.siteId
            for(String bookId : bookIds){
                BooksMst.executeUpdate("update BooksMst set siteId ='" +siteId + "' where id=" + bookId)
                BooksMst.wsshop.executeUpdate("update BooksMst set siteId ='" +siteId + "' where id=" + bookId)
                BooksMst.wsuser.executeUpdate("update BooksMst set siteId ='" +siteId + "' where id=" + bookId)
            }
            def json =[status:"success"]
            render json as JSON

        }else {
            List sitesList = SiteMst.findAll([sort: "clientName", order: "asc"])
            [sitesList: sitesList]
        }
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def setBookType(){
        if("submit".equals(params.mode)){
            String publisherId = params.publisherId
                BooksMst.executeUpdate("update BooksMst set bookType ='" +params.bookType + "' where publisher_id=" + publisherId+" and status='published' and (language is null or language='English') and (bookType='ebook' or book_type is null)" )
                BooksMst.wsshop.executeUpdate("update BooksMst set bookType ='" +params.bookType + "' where publisher_id=" + publisherId+" and status='published' and (language is null or language='English')  and (bookType='ebook' or book_type is null)" )
                BooksMst.wsuser.executeUpdate("update BooksMst set bookType ='" +params.bookType + "' where publisher_id=" + publisherId+" and status='published' and (language is null or language='English')  and (bookType='ebook' or book_type is null)" )

            def json =[status:"success"]
            render json as JSON

        }else {
            //select distinct publisher_id from books_mst for the books whose status is set to published and join it with publishers table column id. Get the id and the name of the publisher
            def sql = "select distinct publisher_id from books_mst where status='published'"
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            List publishers = []
            results.each { result ->
                 def publisher = [:]
                publisher.id = result.publisher_id
                def publisherName = sql1.rows("select name from publishers where id="+result.publisher_id)
                if(publisherName.size()>0) {
                    publisher.name = publisherName[0].name
                    publishers.add(publisher)
                }
            }
            //sort publishers by name
            publishers.sort{it.name}
            [publishers:publishers]
        }
    }


}
