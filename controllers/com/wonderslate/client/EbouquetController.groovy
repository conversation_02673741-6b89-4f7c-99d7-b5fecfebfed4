package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.institute.InstituteIpAddress
import com.wonderslate.usermanagement.AuthenticationToken
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional
import groovy.sql.Sql

import javax.servlet.http.Cookie

class EbouquetController {

    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UtilService utilService
    def redisService

    def index() {
        if("true".equals(grailsApplication.config.grails.appServer.main)||"ebouquet".equals(grailsApplication.config.grails.appServer.default)) {
            Cookie cookie = new Cookie("siteName", "ebouquet")
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = new Integer(24);
            session.setAttribute("entryController", "ebouquet");

            if ("browse".equals(params.mode) && springSecurityService.currentUser!=null) {
                redirect([uri: '/library'])
                return
            }
            def siteMst = SiteMst.findById(session['siteId'])
            [otpReg: siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
        } else {
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }
    }

    @Transactional
    def store() {
        if("true".equals(grailsApplication.config.grails.appServer.main) ||
                "ebouquet".equals(grailsApplication.config.grails.appServer.default)) {
            Cookie cookie = new Cookie("siteName","ebouquet")
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = new Integer(24);
            session.setAttribute("entryController", "ebouquet");

//            if("browse".equals(params.mode) && springSecurityService.currentUser!=null) {
//                redirect([uri: '/ebouquet/library'])
//                return
//            }
            //coming from mobile
            if(params.tokenId!=null){
                if(springSecurityService.currentUser==null) {
                    String tokenId = params.tokenId
                    AuthenticationToken authenticationToken = AuthenticationToken.findByToken(tokenId)
                    if(authenticationToken!=null){
                        println("authenticating "+authenticationToken.username)
                        springSecurityService.reauthenticate(authenticationToken.username)
                    }
                }

            }
            def siteMst = SiteMst.findById(session['siteId'])
            User user
            def userID=null
            if(params.userId) {
                userID=utilService.decrypt(params.userId)
                user = User.findById(Long.parseLong(userID))

            }
            session.setAttribute("wonderpublish","true");
            [categories:dataProviderService.getSiteMst(new Long(24)).categories,
             otpReg: siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)),userEmail:user?.email,userId:userID,sageLogin:user?.sageLogin]
        } else {
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }
    }

    @Transactional
    def ebouquetMobileStore() {
        if("true".equals(grailsApplication.config.grails.appServer.main) ||
                "ebouquet".equals(grailsApplication.config.grails.appServer.default)) {
            Cookie cookie = new Cookie("siteName","ebouquet")
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = new Integer(24);
            session.setAttribute("entryController", "ebouquet");

//            if("browse".equals(params.mode) && springSecurityService.currentUser!=null) {
//                redirect([uri: '/ebouquet/library'])
//                return
//            }

            def siteMst = SiteMst.findById(session['siteId'])

            session.setAttribute("wonderpublish","true");
            [categories:dataProviderService.getSiteMst(new Long(24)).categories,
             otpReg: siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,showLibrary:getInstituteAccessforUser()]
        } else {
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }
    }

    def ebouquetMobileStoreNew() {
        if("true".equals(grailsApplication.config.grails.appServer.main) ||
                "ebouquet".equals(grailsApplication.config.grails.appServer.default)) {
            Cookie cookie = new Cookie("siteName","ebouquet")
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = new Integer(24);
            session.setAttribute("entryController", "ebouquet");

//            if("browse".equals(params.mode) && springSecurityService.currentUser!=null) {
//                redirect([uri: '/ebouquet/library'])
//                return
//            }

            def siteMst = SiteMst.findById(session['siteId'])

            session.setAttribute("wonderpublish","true");
            [categories:dataProviderService.getSiteMst(new Long(24)).categories,
             otpReg: siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,showLibrary:getInstituteAccessforUser()]
        } else {
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }
    }


    def Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }
    def getBookCategories(){
        Integer siteId=getSiteId(request);
        if(redisService.("bookCategories_"+getSiteId(request))==null) {
            dataProviderService.getBookCategories(getSiteId(request))

        }

        if(redisService.("startingBooksList_"+siteId)==null) {
            dataProviderService.getBooksListEvidya(getSiteId(request));
        }
        def books,booksTagDtl
        books = redisService.("startingBooksList_"+siteId);

        if(redisService.("bookstag_"+params.level.replaceAll("\\s+",""))==null){
            dataProviderService.getBooksTagList('College');
        }
        String categories = redisService.("bookCategories_"+getSiteId(request))

        booksTagDtl = redisService.("bookstag_" + params.level.replaceAll("\\s+", ""))
        def json
        json = ['results':categories,
                'books':books,
                'booksTag' : booksTagDtl,
                'status':(categories)?"OK":"Nothing present",
                'bookCategories':'true'
        ]


        render json as JSON
    }
    def footer(){}
    def navheader(){}
    def about(){
       [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def contact(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def privacy(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def terms(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def feedback(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def information(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def requestDemo(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }

    @Transactional
    def getInstituteforUser(){
        def json
        if(springSecurityService.currentUser!=null) {
            String sql = "select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                    " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'  and im.site_id=" + params.siteId;
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);
            if (results.size() > 0) {
               json = ['access':"true"]
            } else {
                json = ['access':"false"]
            }
            render json as JSON
        }
    }

    def storeHolder(){}
    def decision(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def virtualLibrary(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def cookies(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def privacyPolicy(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def termsOfUse(){
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def howItWorks(){[ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    def faq(){[ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}

    def ebouquetReviewerLogin() {
        [ showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
}
