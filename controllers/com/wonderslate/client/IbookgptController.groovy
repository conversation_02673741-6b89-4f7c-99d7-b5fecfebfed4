package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import com.wonderslate.shop.BookPriceDtl
import grails.transaction.Transactional

class IbookgptController {
    def redisService
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService

    def index() {
        params.siteName = "ibookgpt"
        userManagementService.setUserSession(params.siteName, session, servletContext, response)

    }
    def features(){}
    def howItWorks(){}
    def contact(){}
    def benefits(){}
    def faq(){}
    def printBookBundling(){
        [title:"Print Book Bundling - iBookGPT"]
    }

    @Transactional
    def istore(){
        println("istore")
        params.siteName = "ibookgpt"
        userManagementService.setUserSession(params.siteName, session, servletContext, response)
        [title:"iStore - Choose your AI Tutor"]
    }

    @Transactional
    def ibookDtl() {
        params.siteName = "ibookgpt"
        userManagementService.setUserSession(params.siteName, session, servletContext, response)

        // Get book details using the same logic as aiBookDtl
        def bookId = params.id
        if (!bookId) {
            flash.error = "Book ID is required"
            redirect(action: "istore")
            return
        }

        try {
            // Get book details from dataProviderService
            def booksMst = dataProviderService.getBooksMst(new Long(bookId))
            if (!booksMst) {
                flash.error = "Book not found"
                redirect(action: "istore")
                return
            }

            // Get pricing information
            def bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Integer(bookId), "bookGPT")
            def price = bookPriceDtl?.sellPrice ?: booksMst.price ?: 0

            // Get package book details if available
            def packageBooks = []
            if (booksMst.packageBookIds) {
                def packageBookIds = booksMst.packageBookIds.split(",")
                packageBookIds.each { id ->
                    def trimmedId = id.trim()
                    if (trimmedId) {
                        try {
                            def packageBook = dataProviderService.getBooksMst(new Long(trimmedId))
                            if (packageBook) {
                                def subject = extractSubjectFromTitle(packageBook.title)
                                packageBooks.add([
                                    id: packageBook.id,
                                    title: packageBook.title,
                                    subject: subject,
                                    coverImage: packageBook.coverImage,
                                    icon: getRandomSubjectIcon(packageBook.id),
                                    coverColor: getRandomCoverColor(packageBook.id)
                                ])
                            }
                        } catch (Exception e) {
                            // Skip invalid book IDs
                            println("Error loading package book ${trimmedId}: ${e.message}")
                        }
                    }
                }
            }

            [
                title: "Book Details - iBookGPT",
                bookDetails: [
                    id: booksMst.id,
                    title: booksMst.title,
                    description: booksMst.description,
                    price: price,
                    coverImage: booksMst.coverImage,
                    authors: booksMst.authors,
                    packageBookIds: booksMst.packageBookIds,
                    packageBooks: packageBooks,
                    coverColor: getRandomCoverColor(booksMst.id)
                ]
            ]
        } catch (Exception e) {
            println("Error in ibookDtl: ${e.message}")
            flash.error = "Error loading book details"
            redirect(action: "istore")
            return
        }
    }

    private String extractSubjectFromTitle(String title) {
        def subjects = ["Mathematics", "Math", "Science", "Physics", "Chemistry", "Biology",
                       "English", "Hindi", "Social Studies", "History", "Geography", "Economics"]

        for (subject in subjects) {
            if (title.toLowerCase().contains(subject.toLowerCase())) {
                return subject
            }
        }

        // If no subject found, use first two words of title
        def words = title.split(" ")
        return words.length >= 2 ? "${words[0]} ${words[1]}" : (words[0] ?: "Subject")
    }

    private String getRandomSubjectIcon(Long bookId) {
        def icons = ["📐", "🔬", "⚛️", "🧪", "🧬", "📚", "🇮🇳", "🌍", "📜", "🗺️", "💰", "📖", "🎯", "🎨", "🎵", "⚽", "🏆", "🌟"]

        // Use book ID for deterministic "random" selection so same book always gets same icon
        def index = (bookId % icons.size()) as int
        return icons[index]
    }

    private String getRandomCoverColor(Long bookId) {
        def colors = ["#FF5722", "#2196F3", "#3F51B5", "#E91E63", "#009688", "#FF9800", "#4CAF50", "#9C27B0", "#F44336", "#607D8B", "#795548", "#FFC107"]

        // Use book ID for deterministic "random" selection so same book always gets same color
        def index = (bookId % colors.size()) as int
        return colors[index]
    }
}
