package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional

import javax.servlet.http.Cookie

class EtextsController {

    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UtilService utilService
    def redisService

    @Transactional
    def index() {
        if("true".equals(grailsApplication.config.grails.appServer.main)||"etexts".equals(grailsApplication.config.grails.appServer.default)) {
            Cookie cookie = new Cookie("siteName", "etexts")
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = new Integer(23);
            session.setAttribute("entryController", "etexts");

            if ("browse".equals(params.mode) && springSecurityService.currentUser!=null) {
                redirect([uri: '/library'])
                return
            }
            def siteMst = SiteMst.findById(session['siteId'])
            User user
            def userID=null
            if(params.userId) {
                userID=utilService.decrypt(params.userId)
                user = User.findById(Long.parseLong(userID))
            }
            [otpReg: siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request)),userEmail:user?.email,userId:userID,sageLogin:user?.sageLogin]
        } else {
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }
    }

    def store() {
        if("true".equals(grailsApplication.config.grails.appServer.main) ||
                "etexts".equals(grailsApplication.config.grails.appServer.default)) {
            Cookie cookie = new Cookie("siteName","etexts")
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = new Integer(23);
            session.setAttribute("entryController", "etexts");

            if("browse".equals(params.mode) && springSecurityService.currentUser!=null) {
                redirect([uri: '/etexts/library'])
                return
            }

            def siteMst = SiteMst.findById(session['siteId'])

            session.setAttribute("wonderpublish","true");
            [categories:dataProviderService.getSiteMst(new Long(23)).categories,
             otpReg: siteMst!=null && siteMst.otpReg!=null && siteMst.otpReg,showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
        } else {
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }
    }


    def Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }
    def getBookCategories(){
        Integer siteId=getSiteId(request);
        if(redisService.("bookCategories_"+getSiteId(request))==null) {
            dataProviderService.getBookCategories(getSiteId(request))

        }

        if(redisService.("startingBooksList_"+siteId)==null) {
            dataProviderService.getBooksListEvidya(getSiteId(request));
        }
        def books,booksTagDtl
        books = redisService.("startingBooksList_"+siteId);

        if(redisService.("bookstag_"+params.level.replaceAll("\\s+",""))==null){
            dataProviderService.getBooksTagList('College');
        }
        String categories = redisService.("bookCategories_"+getSiteId(request))

        booksTagDtl = redisService.("bookstag_" + params.level.replaceAll("\\s+", ""))
        def json
        json = ['results':categories,
                'books':books,
                'booksTag' : booksTagDtl,
                'status':(categories)?"OK":"Nothing present",
                'bookCategories':'true'
        ]


        render json as JSON
    }
    def footer(){}
    def navheader(){}
    def about(){[showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    def contact(){[showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    def privacy(){[showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    def terms(){[showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    def feedback(){[showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    def help(){[showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    def packages(){[showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]}
    @Transactional
    def library(){
        if (session.getAttribute("userdetails") == null&&springSecurityService.currentUser!=null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
        [showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }
    def storeHolder(){}
    def termsCondition(){}
}
