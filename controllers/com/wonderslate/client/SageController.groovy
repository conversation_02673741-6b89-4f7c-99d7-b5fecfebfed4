package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.DisciplinesMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.publish.BookRatingReviews
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.ChapterAccess
import com.wonderslate.publish.InstructorResources
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

class SageController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def redisService
    @Transactional
    def index() {
        if("true".equals(grailsApplication.config.grails.appServer.main)||"sage".equals(grailsApplication.config.grails.appServer.default)) {
        session['siteId'] = new Integer(9);
        session.setAttribute("entryController", "sage");
        if(params.isbn==null&&params.bookId==null) redirect([uri: '/sage/badLink?error=noisbn&siteName=sage']);
        else {
            BooksMst booksMst
            if(params.isbn!=null){
                session["isbn"] = params.isbn
                booksMst = BooksMst.findByIsbnAndSiteId(params.isbn,getSiteId(request))
            }else{
                booksMst = BooksMst.findById(new Long(params.bookId))
                session["isbn"] = booksMst.isbn
            }

            if(booksMst==null||!"published".equals(booksMst.status)) redirect([uri: '/sage/badLink?error=incorrectisbn&siteName=sage']);
            else {
                if(springSecurityService.currentUser!=null&&params.usertype!=null){
                    if(session.getAttribute("userdetails")==null){
                        session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
                    }
                    if("student".equals(session["userdetails"].userType))
                        redirect([uri: '/sage/doris?siteName=sage&isbn='+session.getAttribute("isbn")])
                    else redirect([uri: '/sage/instructorResources?siteName=sage&isbn='+session.getAttribute("isbn")])
                }
                else {
                    [hideBanner: true, hideMaterialCss: true, disciplines:getDisciplines()]
                }
            }
        }
        }else{
            redirect( [uri: '/'+grailsApplication.config.grails.appServer.default+'/store'])
        }
    }
    def navheader() {}
    def userLogin(){}
    def register(){}
    @Transactional
    def instructorResources(){
        session['siteId'] = new Integer(9);
        session.setAttribute("entryController", "sage");
        BooksMst booksMst
        if(params.isbn!=null){
            booksMst = BooksMst.findByIsbnAndSiteId(params.isbn,getSiteId(request))
        };
        else if(params.bookId!=null) booksMst = BooksMst.findById(new Long(params.bookId))
        else {
            redirect([uri: '/sage/badLink?error=noisbn&siteName=sage'])
            return
        }

        session["isbn"] = booksMst.isbn

            if(booksMst==null||!"published".equals(booksMst.status)) redirect([uri: '/sage/badLink?error=incorrectisbn&siteName=sage']);
            else {
                if(springSecurityService.currentUser==null) redirect([uri: '/sage/instructorResourcesLocked?siteName=sage'])
                else{
                    if(session.getAttribute("userdetails")==null){
                        session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
                    }
                    
                    if("student".equals(session["userdetails"].userType)) redirect([uri: '/sage/instructorResourcesLocked?siteName=sage'])
                    else  [bookId: booksMst.id,book: booksMst,headerImage : booksMst.headerImage, title : booksMst.title, disciplines:getDisciplines(),
                           authors: booksMst.authors,siteMst: dataProviderService.getSiteMst(session["siteId"])]

                }

            }


    }

    @Transactional
    def studentResources(){
        session['siteId'] = new Integer(9);
        session.setAttribute("entryController", "sage");
        if(params.isbn==null) redirect([uri: '/sage/badLink?error=noisbn&siteName=sage']);
        else {
           session["isbn"] = params.isbn
            BooksMst booksMst = BooksMst.findByIsbnAndSiteId(params.isbn,getSiteId(request))
            if(booksMst==null||!"published".equals(booksMst.status)) redirect([uri: '/sage/badLink?error=incorrectisbn&siteName=sage']);
            else {
               if(springSecurityService.currentUser==null) redirect([uri: '/sage/studentResourcesLocked?siteName=sage'])
                else{
                    if(session.getAttribute("userdetails")==null){
                        session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
                    }
                    if("instructor".equals(session["userdetails"].userType)) redirect([uri: '/sage/instructorResources?siteName=sage&isbn='+session.getAttribute("isbn")])
                    else {
                        session["disciplines"] = getDisciplines()
                        redirect([uri: '/sage/doris?siteName=sage&bookId='+booksMst.id])
                    }

                }

            }
        }
    }
    def instructorResourcesContent(){}
    def askAuthorModal(){}
    @Transactional
    def instructorResourcesLocked(){
        session['siteId'] = new Integer(9);
        session.setAttribute("entryController", "sage");
        session.setAttribute("sageUserType", "instructor");
        String isbn
        if(params.isbn!=null) isbn = params.isbn
        else isbn = session["isbn"]
        if(springSecurityService.currentUser!=null){
            redirect([uri: '/sage/instructorResources?siteName=sage&isbn='+isbn])
        }else {
            BooksMst booksMst = BooksMst.findByIsbnAndSiteId(isbn,getSiteId(request))
            [bookId: booksMst.id, book: booksMst, headerImage: booksMst.headerImage, title: booksMst.title, disciplines: getDisciplines(), authors: booksMst.authors]
        }
    }
    @Transactional
    def studentResourcesLocked(){
        session['siteId'] = new Integer(9);
        session.setAttribute("entryController", "sage");
        session.setAttribute("sageUserType", "student");
        String isbn
        if(params.isbn!=null) isbn = params.isbn
        else isbn = session["isbn"]
        if(springSecurityService.currentUser!=null){
            redirect([uri: '/sage/studentResources?siteName=sage&isbn='+isbn])
        }else {
            BooksMst booksMst = BooksMst.findByIsbnAndSiteId(isbn,getSiteId(request))
            [bookId: booksMst.id, book: booksMst, headerImage: booksMst.headerImage, title: booksMst.title, disciplines: getDisciplines(), authors: booksMst.authors]
        }
    }

    @Transactional
    def disciplines(){
        String sort="title";
        String order="asc"
        if(params.sort!=null) sort=params.sort;
        if(params.order!=null) order=params.order;
        List books = BooksMst.findAllByDisciplineAndSiteIdAndStatus(params.discipline,new Long(9),"published",[sort: sort, order:order])
        SiteMst siteMst = dataProviderService.getSiteMst(new Long(9))
        [disciplines:getDisciplines(),books:books,disciplineMst:getDisciplineDetails(params.discipline),
         disciplinePage:true,siteMst:siteMst ]
    }
    @Transactional
    def verification(){
        BooksMst booksMst = BooksMst.findByIsbnAndSiteId(session["isbn"],getSiteId(request))
        [booksMst: booksMst, disciplines:getDisciplines()]
    }
    def footer(){}

    @Transactional
    def getDisciplines(){
        def sql="SELECT discipline,count(id) FROM books_mst where site_id=9 and discipline is not null and status='published' group by discipline";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);


        List discipline = results.collect{ temp ->
           return [discipline:temp[0],noOfBooks:temp[1]];
        }

        return discipline;
    }
    @Transactional
    def getDisciplineDetails(discipline){
        DisciplinesMst disciplinesMst = DisciplinesMst.findByDiscipline(discipline)
        return disciplinesMst;
    }
    @Secured(['ROLE_BOOK_CREATOR'])
    def instructorResourcesAdmin(){
        [hideBanner:true,disciplines:getDisciplines()]
    }

    @Transactional
    def insertInstructorResource(){
        InstructorResources instructorResources = new InstructorResources(subTab: params.subTab,link: params.link,linkName: params.linkName,createdBy: springSecurityService.currentUser.username,bookId: new Long(params.bookId))
        instructorResources.save(failOnError: true, flush: true)
        def json = [ status: "Link added" ]
        render json as JSON
    }

    @Transactional
    def updateInstructorResource(){
        InstructorResources instructorResources = InstructorResources.findById(new Long(params.id))
        instructorResources.link = params.link
        instructorResources.createdBy = springSecurityService.currentUser.username
        instructorResources.save(failOnError: true, flush: true)
    }

    @Transactional
    def deleteInstructorResource(){
        InstructorResources instructorResources = InstructorResources.findById(new Long(params.id))
        instructorResources.delete(flush: true)
        
        def json = [ status: "Link deleted" ]
        render json as JSON        
    }

    def Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }


    def additionalStudentInfo(){}

    def aboutus(){}
    def faq(){}
    def privacy(){}
    def terms(){}

    @Transactional
    def doris(){
         def createdByWsEditor = false;
        if(springSecurityService.currentUser!=null && session.getAttribute("userdetails")==null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
        if(!params.notesCreationMode){
            session["bookRefererPage"] = request.getHeader('referer')
        }

        if(params.resId!=null){
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(new Long(params.resId))
            if(resourceDtl!=null&&"Multiple Choice Questions".equals(resourceDtl.resType))
                redirect (controller: 'funlearn' , action: 'quiz' , params: ['resId':resourceDtl.id,'quizMode':"practice",'quizId':resourceDtl.resLink])
        }

        if ((params.bookId == null || ''.equals(params.bookId))) redirect(action: 'index')
        else {
            Boolean allowReview=false
            def chapterId = null
            def lastReadTopicId=null
            def previewMode = "true".equals(params.preview)?true:false
            List boughtChapters
            Boolean fullBookBought=false
            BooksMst booksMst  = dataProviderService.getBooksMst(new Long(params.bookId))
            Boolean instructor = false
            Boolean instructorControlledBook = false
            BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(booksMst.id)



            if(springSecurityService.currentUser != null) {

                boolean hasBookAccess=false
                if(session["siteId"]!=null&&(session["siteId"].intValue()==9)&&(session["siteId"].intValue()==booksMst.siteId.intValue())){
                    hasBookAccess = true
                    fullBookBought = true
                }


                if(!hasBookAccess){
                    if(!(""+session["userdetails"].publisherId).equals(""+booksMst.publisherId)||session["userdetails"].authorities.any { it.authority == "ROLE_WS_CONTENT_CREATOR" })
                        previewMode = true
                } else {
                    if(hasBookAccess) fullBookBought=true

                    ChapterAccess chapterAccess = ChapterAccess.findByBookIdAndUsername(booksMst.id, springSecurityService.currentUser.username)
                    if(chapterAccess != null){
                        chapterId = chapterAccess.chapterId
                        lastReadTopicId = chapterAccess.resourceId
                    }

                    BookRatingReviews bookRatingReviews = BookRatingReviews.findByBookIdAndUsername(new Long(params.bookId),springSecurityService.currentUser.username)
                    if(bookRatingReviews==null) allowReview=true
                }
            }

            if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }


            def resType
            def link



            def keywords = booksMst.title + " on Wonderslate"
            //logic for addind the seo friendly title
            String seoFriendlyTitle = booksMst.title



            if(springSecurityService.currentUser!=null) {
                dataProviderService.getLastReadBooks(springSecurityService.currentUser.username)
            }



            if(redisService.("chapters_"+booksMst.id)==null){
                dataProviderService.getChaptersList(booksMst.id)
            }
            def chaptersMst = new JsonSlurper().parseText(redisService.("chapters_"+booksMst.id))
            chapterId = chaptersMst[0].id


            [topicId   : chapterId,
             topicMst  : chaptersMst,
              title     : seoFriendlyTitle,
             keywords  : keywords,
             id        : params.id,
             authors   : booksMst.authors,
             resType   : resType,
             link      : link,
             booksPage : "true",
             bookId    : params.bookId,
             headerImage : booksMst.headerImage,
             coverImage : booksMst.coverImage,
             description : booksMst.description,
             addlMenu : "yes",
             bookName:booksMst.title,
             lastReadTopicId:lastReadTopicId==null?"-1":lastReadTopicId,
             previewMode: previewMode,
             book: booksMst,
             newCss : true,
             hideFooter : true,
             allowReview : allowReview,
             boughtChapters:boughtChapters,
             fullBookBought:fullBookBought,
             hideBottomIcons : true,
             hideSearch : true,
             disciplines: session["disciplines"],
             instructor:instructor,
             instructorControlledBook:instructorControlledBook,
             hideTopNavigation:false,
             notesCreationMode:(params.notesCreationMode?true:""),
             referer:session["bookRefererPage"],
             resId: params.resId,
              isBookPage:true,
             booksTagDtl: booksTagDtl,
             createdByWsEditor:createdByWsEditor,
             sageOnly:null
            ]
        }
    }

}
