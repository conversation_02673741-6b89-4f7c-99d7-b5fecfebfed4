package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SitemapService
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional

import javax.servlet.http.Cookie

class OswalpublisherController {

    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def redisService
    WsshopService wsshopService
    UserManagementService userManagementService
    SitemapService sitemapService

    @Transactional
    def setUserSession(){
        Cookie cookie = new Cookie("siteName","oswalpublisher")
        cookie.path = "/"
        response.addCookie(cookie)
        session['siteId'] = new Integer(39);
        session.setAttribute("entryController", "oswalpublisher");
        session.setAttribute("siteName", "Oswal")
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }


        if(springSecurityService.currentUser!=null){
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }
            //get user basic analytics
            String username=springSecurityService.currentUser.username
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }else{
            String username = session["siteId"]+session.getId()+"_temp"
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }


        if(session["activeCategories_39"] == null) {
            if(redisService.("activeCategories_39")==null) wsshopService.activeCategories(new Integer(39))
            session["activeCategories_39"] = redisService.("activeCategories_39")
        }
        if(session["activeCategoriesSyllabus_39"] == null) {
            if(redisService.("activeCategoriesSyllabus_39")==null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(39))
            session["activeCategoriesSyllabus_39"] = redisService.("activeCategoriesSyllabus_39")
        }
        if(session["activeGrades_39"] == null) {
            if(redisService.("activeGrades_39")==null) wsshopService.getActiveGrades(new Integer(39))
            session["activeGrades_39"] = redisService.("activeGrades_39")
        }
        if(session["activeSubjects_39"] == null) {
            if(redisService.("activeSubjects_39")==null) wsshopService.getActiveSubjects(new Integer(39))
            session["activeSubjects_39"] = redisService.("activeSubjects_39")
        }

        if(session["googleUAId_39"]==null){
            if(redisService.("googleUAId_39")==null) dataProviderService.getGoogleUniversalAnalytics("39")
            session["googleUAId_39"] = redisService.("googleUAId_39")
        }
        return
    }

    def index(){
        setUserSession()
        if(redisService.("bannerList_"+session['siteId'])==null) dataProviderService.getBanners(""+session['siteId'])
        def bannerList = redisService.("bannerList_"+session['siteId'])
        [commonTemplate:"true",bannerList:bannerList]
    }

    @Transactional
    def store() {
        setUserSession()
        int pageNo=0
        HashMap seo = sitemapService.getSEO(params, new Integer(""+session["siteId"]))
        params.put("level",seo.get("level"))
        params.put("syllabus",seo.get("syllabus"))
        params.put("grade",seo.get("grade"))
        if(params.pageNo!=null&&!"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
        HashMap booksAndPublishers = wsshopService.getBooksList(params,new Integer(39),pageNo)
        [commonTemplate:"true",booksList:booksAndPublishers,
         "title":seo.get("browserTitle"),seoDesc:seo.get("browserDescription"),booksList:booksAndPublishers,keywords:seo.get("browserKeywords"),onPageTitle:seo.get("onPageTitle"),
         onPageDescription:seo.get("onPageDescription"),storeUrl:"/oswalpublisher/store",publisherDescription:seo.get("publisherDescription")!=null?seo.get("publisherDescription"):null,
         publisherName:seo.get("publisherName"),aboutTitle:seo.get("aboutTitle"),aboutDescription:seo.get("aboutDescription")]

    }
}
