package com.wonderslate.client

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.SitemapService
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional

import javax.servlet.http.Cookie

class RadianbooksController {

    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def redisService
    WsshopService wsshopService
    UserManagementService userManagementService
    SitemapService sitemapService
    @Transactional
    def setUserSession(){
        Cookie cookie = new Cookie("siteName","radianbooks")
        cookie.path = "/"
        response.addCookie(cookie)
        session['siteId'] = new Integer(37);
        session.setAttribute("entryController", "radianbooks");
        session.setAttribute("siteName", "Radian Books")
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }


        if(springSecurityService.currentUser!=null){
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }
            //get user basic analytics
            String username=springSecurityService.currentUser.username
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }else{
            String username = session["siteId"]+session.getId()+"_temp"
            if(redisService.("usersCartBooksDetails_"+username)==null){
                dataProviderService.usersCartBooksDetailsByUsername(username,session["siteId"])
            }
            session["userCartCount"]=Integer.parseInt(redisService.("usersCartBooksDetailsCount_"+username))
        }


        if(session["activeCategories_37"] == null) {
            if(redisService.("activeCategories_37")==null) wsshopService.activeCategories(new Integer(37))
            session["activeCategories_37"] = redisService.("activeCategories_37")
        }
        if(session["activeCategoriesSyllabus_37"] == null) {
            if(redisService.("activeCategoriesSyllabus_37")==null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(37))
            session["activeCategoriesSyllabus_37"] = redisService.("activeCategoriesSyllabus_37")
        }
        if(session["activeGrades_37"] == null) {
            if(redisService.("activeGrades_37")==null) wsshopService.getActiveGrades(new Integer(37))
            session["activeGrades_37"] = redisService.("activeGrades_37")
        }
        if(session["activeSubjects_37"] == null) {
            if(redisService.("activeSubjects_37")==null) wsshopService.getActiveSubjects(new Integer(37))
            session["activeSubjects_37"] = redisService.("activeSubjects_37")
        }

        if(session["googleUAId_37"]==null){
            if(redisService.("googleUAId_37")==null) dataProviderService.getGoogleUniversalAnalytics("37")
            session["googleUAId_37"] = redisService.("googleUAId_37")
        }
        return
    }
    def index(){
        setUserSession()
        if(redisService.("bannerList_"+session['siteId'])==null) dataProviderService.getBanners(""+session['siteId'])
        def bannerList = redisService.("bannerList_"+session['siteId'])
        [commonTemplate:"true",bannerList:bannerList]
    }
    @Transactional
    def store() {
        setUserSession()
        int pageNo=0
        HashMap seo = sitemapService.getSEO(params, new Integer(""+session["siteId"]))
        params.put("level",seo.get("level"))
        params.put("syllabus",seo.get("syllabus"))
        params.put("grade",seo.get("grade"))
        if(params.pageNo!=null&&!"null".equals(params.pageNo)) pageNo = Integer.parseInt(params.pageNo)
        HashMap booksAndPublishers = wsshopService.getBooksList(params,new Integer(37),pageNo)
        [commonTemplate:"true",booksList:booksAndPublishers,
         "title":seo.get("browserTitle"),seoDesc:seo.get("browserDescription"),booksList:booksAndPublishers,keywords:seo.get("browserKeywords"),onPageTitle:seo.get("onPageTitle"),onPageDescription:seo.get("onPageDescription"),
         storeUrl:"/radianbooks/store",publisherDescription:seo.get("publisherDescription")!=null?seo.get("publisherDescription"):null,
         publisherName:seo.get("publisherName"),aboutTitle:seo.get("aboutTitle"),aboutDescription:seo.get("aboutDescription")]

    }
}
