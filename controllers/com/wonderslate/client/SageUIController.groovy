package com.wonderslate.client

import grails.plugin.springsecurity.SpringSecurityService

import javax.servlet.http.Cookie

class SageUIController {
  SpringSecurityService springSecurityService
  def index() {
    Cookie cookie = new <PERSON><PERSON>("siteName","sageUI")
    cookie.path = "/"
    response.addCookie(cookie)
    session['siteId'] = new Integer(6);
    session.setAttribute("entryController", "sageUI");
    if(!"browse".equals(params.mode) && springSecurityService.currentUser!=null) redirect( [uri: '/wonderpublish/mybooks'])
    session.setAttribute("wonderpublish","true");
  }
  def footer(){}
  def navheader(){}

  
}