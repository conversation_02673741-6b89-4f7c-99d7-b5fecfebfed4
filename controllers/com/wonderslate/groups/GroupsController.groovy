package com.wonderslate.groups
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper


class GroupsController {
    SpringSecurityService springSecurityService
    GroupsService groupsService
    def redisService

    @Secured(['ROLE_USER']) @Transactional
    def groupCreate() {
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def json = groupsService.groupCreate(request)
            render json as JSON
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def addGroupCoverImage(){
        def json = groupsService.addGroupCoverImage(request,params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def updateGroupDescription(){
        def json = groupsService.updateGroupDescription(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def createPostDescriptionForGroup(){
        Integer siteId = getSiteId(request)
        def json = groupsService.createPostDescriptionForGroup(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def createPostImgFileForGroup(){
        def json = groupsService.createPostImgFileForGroup(request,params,flash)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getGroupsList(){
        def json = groupsService.getGroupsList(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getMyGroupsList(){
        def json = groupsService.getMyGroupsList(params)
        render json as JSON
    }

    def encrpy(){
        String message=params.msg
        def json =["enc":groupsService.encrypt(message),"dyc":groupsService.decrypt(message)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def joinGroup(){
        def json = groupsService.joinGroup(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def fetchGroupPendingRequests(){
        def json = groupsService.fetchGroupPendingRequests(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def acceptJoinRequest(){
        def json = groupsService.acceptJoinRequest(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def deleteJoinRequest(){
        def json = groupsService.deleteJoinRequest(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def viewGroupUsers(){
        def json = groupsService.viewGroupUsers(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def reportGroupAsSpam(){
        def json = groupsService.reportGroupAsSpam(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def approveAllGroupRequest(){
        def json = groupsService.approveAllGroupRequest(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def rejectAllGroupRequest(){
        def json = groupsService.rejectAllGroupRequest(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getGroupDetailsById(){
        def json = groupsService.getGroupDetailsById(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def reportGroupUser() {
        def json = groupsService.reportGroupUser(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def deleteUserFromGroup(){
        def json = groupsService.deleteUserFromGroup(params)
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def getGroupReportedUser() {
        def json = groupsService.getGroupReportedUser(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def reportPostAsSpam(){
        def json = groupsService.reportPostAsSpam(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getGroupReportedPosts() {
        def json = groupsService.getGroupReportedPosts(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def deletePostFromGroup(){
        def json = groupsService.deletePostFromGroup(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def  likePostOfGroup(){
        def json = groupsService.likePostOfGroup(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def dislikePostOfGroup(){
        def json = groupsService.dislikePostOfGroup(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def commentPostOfGroup(){
        def json = groupsService.commentPostOfGroup(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def replyForCommentOfGroup(){
        def json = groupsService.replyForCommentOfGroup(request)
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def makeUserAsGroupAdmin(){
        def json = groupsService.makeUserAsGroupAdmin(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def removeUserAsGroupAdmin(){
        def json = groupsService.removeUserAsGroupAdmin(request)
        render json as JSON
    }

    @Transactional
    def showGroupImage(long id,String fileName) {
        try {
            GroupsMst  groupsMst  = GroupsMst.findById(new Long(params.id))
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                def file = new File("upload/usergroup/"+groupsMst.id+"/"+fileName)
                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showGroupImage "+e.toString())
            render "";

        }
    }


    @Secured(['ROLE_USER']) @Transactional
    def getGroupPostsDetails(){
        def groupId=params.groupId
        int pageNo=0
        if(params.pageNo!=null) pageNo = Integer.parseInt(params.pageNo)
        if (redisService.("getPostDetailsByGroupId_"+groupId+"_"+pageNo) == null || redisService.("getPostDetailsByGroupId_"+groupId+"_"+pageNo)=="null") {
            groupsService.getPostDetails(params.groupId,pageNo)
        }

        List  results=new JsonSlurper().parseText(redisService.("getPostDetailsByGroupId_"+groupId+"_"+pageNo))
        List postsDetails = results.collect { post ->
            if (redisService.("commentsDetailsByGroupId_" + post.id+"_0") == null || redisService.("commentsDetailsByGroupId_" + post.id+"_0")=="null") {
                groupsService.commentsDetailsByGroupId(post.id,0)
            }
            List commentsDetailsByGroupId= new JsonSlurper().parseText(redisService.("commentsDetailsByGroupId_" + post.id+"_0"))
            if (redisService.("getGroupPostLikeCount_"+post.id) == null || redisService.("getGroupPostLikeCount_"+post.id)=="null")  {
                groupsService.getGroupPostLikeCount(post.id)
            }
            if (redisService.("userPostLike_"+post.id+"_"+springSecurityService.currentUser.username) == null || redisService.("userPostLike_"+post.id+"_"+springSecurityService.currentUser.username)=="null") {
                groupsService.userPostLikeOrDislikeCache(new Long(params.groupId),new Long(post.id),springSecurityService.currentUser.username)
            }
            if (redisService.("getGroupPostCommentsCount_" + post.id) == null || redisService.("getGroupPostCommentsCount_" + post.id)=="null") {
                groupsService.getGroupPostCommentsCount(post.id)
            }
            return [id              : post.id,description:post.description,createdBy:post.createdBy,dateCreated:post.dateCreated,postImage:post.postImage,fileName:post.fileName,filePath:post.filePath,name:post.name,userId:post.userId,profilepic:post.profilepic,username:post.username,userType:post.userType,pinned:post.pinned,comments:commentsDetailsByGroupId,
                    commentsCount:redisService.("getGroupPostCommentsCount_" + post.id),postLikesCount:redisService.("getGroupPostLikeCount_"+post.id),likedPost:redisService.("userPostLike_"+post.id+"_"+springSecurityService.currentUser.username)]
        }
        if(redisService.("getGroupPinnedPosts_" + groupId) == null || redisService.("getGroupPinnedPosts_" + groupId)=="null") groupsService.getGroupPinnedPosts(groupId)
        List pinnedResults=new JsonSlurper().parseText(redisService.("getGroupPinnedPosts_"+groupId))
        List pinnedPostsDetails = pinnedResults.collect { post ->
            if (redisService.("commentsDetailsByGroupId_" + post.id+"_0") == null || redisService.("commentsDetailsByGroupId_" + post.id+"_0")=="null") {
                groupsService.commentsDetailsByGroupId(post.id,0)
            }
            List commentsDetailsByGroupId= new JsonSlurper().parseText(redisService.("commentsDetailsByGroupId_" + post.id+"_0"))
            if (redisService.("getGroupPostLikeCount_"+post.id) == null || redisService.("getGroupPostLikeCount_"+post.id)=="null")  {
                groupsService.getGroupPostLikeCount(post.id)
            }
            if (redisService.("userPostLike_"+post.id+"_"+springSecurityService.currentUser.username) == null || redisService.("userPostLike_"+post.id+"_"+springSecurityService.currentUser.username)=="null") {
                groupsService.userPostLikeOrDislikeCache(new Long(params.groupId),new Long(post.id),springSecurityService.currentUser.username)
            }
            if (redisService.("getGroupPostCommentsCount_" + post.id) == null || redisService.("getGroupPostCommentsCount_" + post.id)=="null") {
                groupsService.getGroupPostCommentsCount(post.id)
            }
            return [id              : post.id,description:post.description,createdBy:post.createdBy,dateCreated:post.dateCreated,postImage:post.postImage,fileName:post.fileName,filePath:post.filePath,name:post.name,userId:post.userId,profilepic:post.profilepic,username:post.username,userType:post.userType,pinned:post.pinned,comments:commentsDetailsByGroupId,
                    commentsCount:redisService.("getGroupPostCommentsCount_" + post.id),postLikesCount:redisService.("getGroupPostLikeCount_"+post.id),likedPost:redisService.("userPostLike_"+post.id+"_"+springSecurityService.currentUser.username)]
        }
        def json = [ groupPostDetails:postsDetails,groupPinnedPosts:pinnedPostsDetails]
        render json as JSON
    }


    @Transactional
    def downloadPostFile(long id) {
        GroupsPostDtl groupsPostDtl = GroupsPostDtl.get(id)
        if (groupsPostDtl == null) {
            flash.message = "Document not found."
        } else {
            def file = new File(groupsPostDtl.filePath)
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${groupsPostDtl.fileName}\"")
            response.setHeader("Content-Length", "${file.length()}")
            def fileInputStream = new FileInputStream(file)
            def outputStream = response.getOutputStream()
            byte[] buffer = new byte[4096];
            int len;
            while ((len = fileInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush()
            outputStream.close()
            fileInputStream.close()
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def ignoreReportedGroupUser(){
        def json = groupsService.ignoreReportedGroupUser(params)
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def ignoreReportedGroupPost(){
        def json = groupsService.ignoreReportedGroupPost(params)
        render json as JSON
    }

    @Transactional
    def showGroupPostImage(long id,String fileName) {
        try {
            GroupsPostDtl  groupsPostDtl  = GroupsPostDtl.findById(new Long(params.id))
            if (fileName != null && !"null".equals(fileName) && fileName.length() > 0) {
                def file = new File("upload/grouppostimg/" + groupsPostDtl.groupId + "/" + groupsPostDtl.id+"/"+fileName)
                if (file.exists()) {
                    response.setContentType("APPLICATION/OCTET-STREAM")
                    response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
                    def fileInputStream = new FileInputStream(file)
                    def outputStream = response.getOutputStream()
                    byte[] buffer = new byte[4096];
                    int len;
                    while ((len = fileInputStream.read(buffer)) > 0) {
                        outputStream.write(buffer, 0, len);
                    }

                    outputStream.flush()
                    outputStream.close()
                    fileInputStream.close()
                } else render "";
            } else render "";
        }
        catch (Exception e)
        {
            println("Exception in showGroupPostImage "+e.toString())
            render "";

        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def userExitGroup(){
        def json = groupsService.userExitGroup(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getCommentListForPost(){
        def json = groupsService.getCommentListForPost(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getUserRequestStatus(){
        def json = groupsService.getUserRequestStatus(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getPostDetailsById(){
        def json = groupsService.getPostDetailsById(params)
        render json as JSON
    }


    def getGroupIdsUser(){
        def json = groupsService.getGroupIdsUser()
        render json as JSON
    }

    def  Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }

    @Transactional
    def index(){}

    @Secured(['ROLE_USER']) @Transactional
    def members(){}

    @Secured(['ROLE_USER']) @Transactional
    def notifications(){}

    @Secured(['ROLE_USER']) @Transactional
    def memberRequests(){}

    @Secured(['ROLE_USER']) @Transactional
    def reported(){}

    @Transactional
    def groupDtl() {
        def groupId
        Integer siteId = getSiteId(request)
        if (params?.groupId == null) {
            if ((redisService.("groupWallId_" + siteId) == null) || (redisService.("groupWallId_" + siteId) == "null")) {
                groupsService.getGroupWallIdForSite(siteId)
            }
            groupId = redisService.("groupWallId_" + siteId)
            if (redisService.("groupWallId_" + siteId + "_latestPostCount") == null || redisService.("groupWallId_" + siteId + "_latestPostCount") == "null") {
                groupsService.getlatestPostCountForGroupWall(siteId)
            }
            session.setAttribute("groupWallNotificationCount", redisService.("groupWallId_" + siteId + "_latestPostCount"))
        } else {
            groupId = params?.groupId
        }
        if (redisService.("getGroupMembersCount_" + groupId) == null) groupsService.getGroupMembersCount(new Long(groupId))
        if (redisService.("getGroupAdminsCount_" + groupId) == null) groupsService.getGroupAdminCount(new Long(groupId))
        def adminCount = redisService.("getGroupAdminsCount_" + groupId) ? Integer.parseInt(redisService.("getGroupAdminsCount_" + groupId)) : 0
        def membersCount = (redisService.("getGroupMembersCount_" + groupId) ? Integer.parseInt(redisService.("getGroupMembersCount_" + groupId)) : 0) - adminCount
        [membersCount: membersCount, adminCount: adminCount, groupId: groupId,commonTemplate:"true"]
    }

    def getGroupWallDetails()
    {
        def json=groupsService.getGroupWallDetailsForSite(params)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def pinPost(){
        def json = groupsService.makePostAsPinned(request)
        render json as JSON
    }

    @Secured(['ROLE_WS_GROUP_ADMIN']) @Transactional
    def getAllGroupDetails()
    {
        def json=groupsService.getAllGroupDetails(params)
        render json as JSON
    }

    @Secured(['ROLE_WS_GROUP_ADMIN']) @Transactional
    def groupReport(){

    }

    @Secured(['ROLE_USER']) @Transactional
    def postDetail(){
    }
	
    @Secured(['ROLE_USER']) @Transactional
    def getCompletePostDetails()
    {
        List postDetails=[]
        def results=groupsService.getCompletePostDetails(params)
        if(results!=null) {
            postDetails = results.collect { post ->
                if (redisService.("commentsDetailsByGroupId_" + post.id + "_0") == null || redisService.("commentsDetailsByGroupId_" + post.id + "_0") == "null") {
                    groupsService.commentsDetailsByGroupId(post.id, 0)
                }
                List commentsDetailsByGroupId = new JsonSlurper().parseText(redisService.("commentsDetailsByGroupId_" + post.id + "_0"))
                if (redisService.("getGroupPostLikeCount_" + post.id) == null || redisService.("getGroupPostLikeCount_" + post.id) == "null") {
                    groupsService.getGroupPostLikeCount(post.id)
                }
                if (redisService.("userPostLike_" + post.id + "_" + springSecurityService.currentUser.username) == null || redisService.("userPostLike_" + post.id + "_" + springSecurityService.currentUser.username) == "null") {
                    groupsService.userPostLikeOrDislikeCache(new Long(params.groupId), new Long(post.id), springSecurityService.currentUser.username)
                }
                if (redisService.("getGroupPostCommentsCount_" + post.id) == null || redisService.("getGroupPostCommentsCount_" + post.id) == "null") {
                    groupsService.getGroupPostCommentsCount(post.id)
                }
                return [id           : post.id, description: post.description, createdBy: post.createdBy, dateCreated: post.dateCreated, postImage: post.postImage, fileName: post.fileName, filePath: post.filePath, name: post.name, userId: post.userId, profilepic: post.profilepic, username: post.username, userType: post.userType, pinned: post.pinned, comments: commentsDetailsByGroupId,
                        commentsCount: redisService.("getGroupPostCommentsCount_" + post.id), postLikesCount: redisService.("getGroupPostLikeCount_" + post.id), likedPost: redisService.("userPostLike_" + post.id + "_" + springSecurityService.currentUser.username)]
            }
        }
        def json = [ groupPostDetails:postDetails]
        render json as JSON
    }

}