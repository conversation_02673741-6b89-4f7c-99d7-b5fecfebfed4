package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.ResourceDtlSub
import com.wonderslate.data.SiteMst
import com.wonderslate.data.VideoExplanation
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import groovy.json.JsonSlurper
import groovy.sql.Sql

class AutoVideoController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService

    @Secured(['ROLE_VIDEO_CREATOR'])
    def index() {
        SiteMst siteMst = dataProviderService.getSiteMst(1)
        [awsAccessKeyId:siteMst.awsAccessKeyId,awsSecretAccessKey:siteMst.awsSecretAccessKey]

    }

    def automatedVideo() {
        SiteMst siteMst = dataProviderService.getSiteMst(1)
        [awsAccessKeyId:siteMst.awsAccessKeyId,awsSecretAccessKey:siteMst.awsSecretAccessKey]

    }

    def slidesVideo() {
        SiteMst siteMst = dataProviderService.getSiteMst(1)
        [awsAccessKeyId:siteMst.awsAccessKeyId,awsSecretAccessKey:siteMst.awsSecretAccessKey,booksType:"NEET Exam"]

    }

    def getVideoSlides(){
        List resources = [[id:new Integer(0),slide:"",description: "Title of book1",slide1: "adaf",description1: "",slide2:"YCT",description2: "This books is from YCT"]]
        def json = [ status: resources?"OK":"not found", resources: resources]
        render json as JSON
    }

//    @Secured(['ROLE_USER'])
    def getResourceSlide(){
        List resources
       println("resId="+params)
        if(params.resId!=null&&!"null".equals(params.resId)&&!"".equals(params.resId)&&!"undefined".equals(params.resId)) {
            String sql = "SELECT ve.id,ve.image,ve.slide_name,ve.point1,ve.explanation1,ve.point2,ve.explanation2,ve.point3," +
                    " ve.explanation3,ve.point4,ve.explanation4,ve.point5,ve.explanation5,rd.resource_name ,ve.resource_id FROM video_explanation ve,resource_dtl rd" +
                    " where ve.resource_id=rd.id AND ve.resource_id=" + params.resId;

            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql);

            resources = results.collect { slide ->
                return [id          : slide[0], slide: slide[1], description: slide[2], slide1: slide[3], description1: slide[4], slide2: slide[5], description2: slide[6],
                        slide3      : slide[7], description3: slide[8], slide4: slide[9], description4: slide[10], slide5: slide[11], description5: slide[12],
                        resourceName: slide[13]]
            }
        }else if("slidesVideo".equals(params.videoMode)){
            def booksList = session["videosBookList"]
            def books =  new JsonSlurper().parseText(booksList.get("books"))
            String imgSrc=""
            resources = books.collect { book ->
                imgSrc = "<p><img src='/funlearn/showProfileImage?id=" + book.id + "&fileName=" + book.coverImage + "&type=books&imgType=passport'></p>";
                return [id          : book.id, slide: imgSrc, description: book.title, slide1: book.publisher, description1: "This book is from "+book.publisher, slide2: null, description2: null,
                        slide3      : null, description3: null, slide4: null, description4: null, slide5: null, description5: null,
                        resourceName: params.title]
            }
        }

        def json = [ status: resources?"OK":"not found", resources: resources]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def addVideoHTML(){
        def quizId, resourceDtlId, objectiveMstId;
        ResourceDtl resourceDtlInstance
        VideoExplanation videoexplanation
        String fileData
        println("MODE======"+params.mode)
        println("resourceDtlId======"+params.resourceDtlId)
        if("create".equals(params.mode)) {
            resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.dateCreated = new Date()
            resourceDtlInstance.resLink="empty"
            resourceDtlInstance.resType = params.resourceType
            println("params.chapterId======="+params.chapterId)
            resourceDtlInstance.chapterId = new Integer(params.chapterId)
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.save(failOnError: true,flush: true)
            Integer resId=resourceDtlInstance.getId();
            videoexplanation = new VideoExplanation()
            videoexplanation.slide = params.notes
            videoexplanation.description = params.description
            videoexplanation.resourceId = resId
            videoexplanation.save(failOnError: true,flush: true)
            resourceDtlId = resourceDtlInstance.id
            println("resourceDtlId====here it s=="+resourceDtlId);
        } else if("edit".equals(params.mode)) {
            resourceDtlInstance =  dataProviderService.getResourceDtl(new Long(params.resourceDtlId))

            resourceDtlInstance.dateCreated = new Date()
            resourceDtlInstance.resLink = "upload/books/"+params.bookId + "/chapters/" +params.chapterId + "/" +resourceDtlInstance.id + "/" +params.resourceName
            resourceDtlInstance.filename = params.resourceName
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.save(failOnError: true,flush: true)

            session.removeAttribute("htmlId")
            fileData = moveExtractedImages(params.bookId,params.chapterId,resourceDtlInstance.id,params.notes)

            def c = ResourceDtlSub.countByResourceId(new Integer(params.resourceDtlId))
            if(c>1) {
                String sql = "update ResourceDtlSub set resourceId=-"+params.resourceDtlId+" where resourceId="+params.resourceDtlId
                ResourceDtlSub.executeUpdate(sql)

                resourceDtlSub = new ResourceDtlSub()
            } else if(c==1) {
                resourceDtlSub = ResourceDtlSub.findByResourceId(new Integer(params.resourceDtlId))
            }else{
                resourceDtlSub = new ResourceDtlSub()
            }

            resourceDtlSub.filename = params.resourceName
            resourceDtlSub.filedata = fileData.getBytes("UTF-8")
            resourceDtlSub.resourceId = resourceDtlInstance.id
            resourceDtlSub.save(flush: true, failOnError: true)
            ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtlInstance.chapterId)
            BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
            if("published".equals(booksMst.status)) {
                dataNotificationService.readingMaterialUpdated(chaptersMst.id, chaptersMst.bookId,resourceDtlInstance.id)
            }
        }
        else if ("add".equals(params.mode)) {
            println("resourceDtlId=========+"+resourceDtlId)
            println("params.notes=======inside======"+params.notes)
            println(" params.description=======inside======"+ params.description)
            println(" params.resourceDtlId=======inside======"+ params.resourceDtlId)
            videoexplanation = new VideoExplanation()
            videoexplanation.slide = params.notes
            videoexplanation.description = params.description
            videoexplanation.resourceId = new Integer(params.resourceDtlId)
            videoexplanation.save(failOnError: true, flush: true)
            resourceDtlId=params.resourceDtlId;
        }

        def json =  [
                "resourceDtlId": resourceDtlId
        ]

        render json as JSON
        return
        redirect (controller: 'wonderpublish' , action: 'bookCreate', params: [chapterId: params.chapterId,bookId: params.bookId])
    }

    def autoImageVideo() {}
}
