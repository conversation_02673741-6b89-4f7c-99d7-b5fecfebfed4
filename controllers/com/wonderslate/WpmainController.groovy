package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.cache.SecondDataProviderService
import com.wonderslate.data.BooksDtl
import com.wonderslate.data.BooksMst
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.data.WpmainService
import com.wonderslate.shop.BookPriceDtl
import com.wonderslate.sqlutil.SafeSql
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import com.wonderslate.data.ObjectiveMst
import grails.transaction.Transactional
import com.wonderslate.data.ChaptersMst
import com.wonderslate.shop.BookPriceDtl
import groovy.json.JsonSlurper

class WpmainController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    SecondDataProviderService secondDataProviderService
    WpmainService wpmainService
    UtilService utilService
    UserManagementService userManagementService
    def redisService

    def index() {
        session["siteId"] = new Integer(1);
        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
    }

    def unmarkedQA() {
        String sql = " select count(*) from objective_mst where question is not null and answer is not null and marks is null"

        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new SafeSql(dataSource)
        def results = sql1.rows(sql)
        [numberOfQA: results[0][0]]
    }

    def updateUnMarkedQA() {
        //find all from ObjectiveMst where question and answer is not null and marks is null order by id desc and limit 100
        List questions = ObjectiveMst.findAllByQuestionIsNotNullAndAnswerIsNotNullAndMarksIsNull([sort: "id", order: "desc", max: 500])
        int numberUpdated = 0, numberFailed = 0;
        questions.each { question ->
            //logic to update marks
            try {
                def sentences = question.answer.split("\\.")
                if (sentences.length == 1) question.marks = new Double(1)
                else if (sentences.length == 2 || sentences.length == 3) question.marks = new Double(2)
                else if (sentences.length == 4 || sentences.length == 5) question.marks = new Double(3)
                else if (sentences.length == 6 || sentences.length == 7) question.marks = new Double(4)
                else question.marks = new Double(5)
                ObjectiveMst.executeUpdate("update ObjectiveMst set marks=" + question.marks + " where id=" + question.id)
                numberUpdated++
            }
            catch (Exception e) {
                numberFailed++
                println("Exception while updating marks for question id " + question.id)
            }
        }
        def json = [status: "success", updatedTime: new Date(), numberUpdated: numberUpdated, numberFailed: numberFailed]
        render json as JSON
    }

    @Transactional
    def bookai() {
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        if (redisService.("chapterId_resId_" + bookId) == null) {
            secondDataProviderService.getAllChapterIdResId(new Long(params.bookId));
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapterId_resId_" + bookId))
        String userAgent = request.getHeader("User-Agent");
        Boolean isMobile = userAgent =~ /Mobile|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/;
        def previewMode = true, hasBookAccess = true
        def showAIWindow = false

        // Get user roles for the template
        def userRoles = []
        if (springSecurityService.isLoggedIn()) {
            def user = springSecurityService.currentUser
            if (user) {
                userRoles = user.authorities?.collect { it.authority }
            }
        }

        // Check if book has questions for Question Papers button
        def hasQuestions = wpmainService.hasQuestionsInBook(new Long(bookId))

        [chaptersList   : chaptersList, bookId: bookId, isMobile: isMobile, previewMode: previewMode, hasBookAccess: hasBookAccess,
         bookTitle      : booksMst.title, showAIWindow: showAIWindow, userRoles: userRoles, title: "Teacher AI Assistant for " + booksMst.title,
         gptloaderName  : null,
         gptloaderpath  : null,
         gptcustomloader: null,
         hasQuestions   : hasQuestions]
    }

    @Transactional
    def aibook() {
        Integer siteId = utilService.getSiteId(request, session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if(params.siteName==null) params.siteName = siteMst.siteName
        if ("true".equals(siteMst.commonWhiteLabel)) {
            userManagementService.setUserSession(params.siteName, session, servletContext, response)
        }

        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }

        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        BooksDtl booksDtl = dataProviderService.getBooksDtl(new Long(bookId))
        String title = "AI Book for " + booksMst.title
        boolean hasBookAccess = false
        def previewMode = false
        def gptManager = false
        if (springSecurityService.currentUser != null) {
            if (springSecurityService.currentUser.authorities.any {
                it.authority == "ROLE_GPT_MANAGER"
            }) {
                gptManager = true
            }
        }


        if (springSecurityService.currentUser == null  || userManagementService.isValidSession(springSecurityService.currentUser.username, session.getId())) {
            println("Entered the permission check thingy")
            hasBookAccess = userManagementService.hasAccessToBook(new Long(bookId), session, request, true, response)
            if (!hasBookAccess) {
                hasBookAccess = userManagementService.hasLibraryAccessToBook(new Long(bookId), true)
            }
        }
        if (!hasBookAccess&&"published".equals(booksMst.status)) {
            previewMode = true
        }
        else if(!hasBookAccess){
            if ("true".equals(siteMst.commonWhiteLabel)) {
               redirect(uri: "/sp/${siteMst.siteName}/store")
            } else {
                redirect(controller: siteMst.siteName, action: 'index')
            }
            return
        }


        if (redisService.("chapters_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId))
        }
        println("chaptersList=" + redisService.("chapters_" + bookId))
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))
        BookPriceDtl bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Integer(bookId), "bookGPT")

        // Handle chapter-only mode
        def chapterOnlyMode = params.chapterOnly == 'true'
        def specificChapterId = params.chapterId

        [booksMst: booksMst, chaptersList: chaptersList, previewMode: previewMode, bookPriceDtl: bookPriceDtl,
         gptManager: gptManager, chapterOnlyMode: chapterOnlyMode, specificChapterId: specificChapterId, title: title,booksDtl:booksDtl]
    }

    @Transactional
    def getChapterContent() {
        println("getChapterContent called with params: ${params}")
        Long chapterId = params.chapterId as Long
        String loadType = params.loadType ?: 'overview' // 'overview', 'exerciseSolutions', 'questionBank', 'full'

        def result = [:]

        // Always include basic chapter info
        result.namespace = wpmainService.getNameSpace(chapterId)
        result.resId = wpmainService.getResId(chapterId)
        result.questionTypeCounts = wpmainService.getQuestionTypeCounts(chapterId)

        // Check if chapter content exists and include it
        ChaptersMst chaptersMst = dataProviderService.getChaptersMst(chapterId)
        if (chaptersMst) {
            BooksDtl booksDtl = BooksDtl.findByBookId(chaptersMst.bookId)
            if (booksDtl != null && "Yes".equals(booksDtl.showTheory)) {
                // Chapter content exists, get the content
                String chapterFilePath = grailsApplication.config.grails.basedir.path + "/supload/books/theoryBooks/" + chaptersMst.bookId + "/chapter_" + chapterId + ".html"
                File chapterFile = new File(chapterFilePath)
                if (chapterFile.exists()) {
                    result.chapterContent = chapterFile.text
                    result.chapterFilePath = chapterFilePath
                    result.hasChapterContent = true
                } else {
                    result.chapterContent = ""
                    result.chapterFilePath = chapterFilePath
                    result.hasChapterContent = false
                }
            } else {
                result.hasChapterContent = false
            }
        }

        // Load data based on loadType
        if (loadType == 'overview' || loadType == 'full') {
            // For overview, only load exercise solutions (first section)
            result.exerciseSolutions = wpmainService.getExerciseSolutions(chapterId)
        }

        if (loadType == 'exerciseSolutions' || loadType == 'full') {
            result.exerciseSolutions = wpmainService.getExerciseSolutions(chapterId)
        }

        boolean showTheory = false, showPyqs = false, showQuestionPaperGenerator = false

        if (params.bookId != null) {

            BooksDtl booksDtl = dataProviderService.getBooksDtl(new Integer(params.bookId))
            if (booksDtl != null && "Yes".equals(booksDtl.showTheory) && result.hasChapterContent) {
                showTheory = true
            }  else {
                result.hasChapterContent = false
            }
            if (booksDtl != null && "Yes".equals(booksDtl.showPyqs)) {
                showPyqs = true
            }
            else{
                result.questionTypeCounts['Previous Year Questions']=0
            }

        }


        if (loadType == 'questionBank' || loadType == 'full') {
            result.questionBank = wpmainService.getQuestionBankData(chapterId)
        }

        // Support loading specific question types with pagination
        if (loadType.startsWith('questionType:')) {
            String questionType = loadType.substring('questionType:'.length())

            // Get pagination parameters
            Integer offset = params.offset ? params.offset as Integer : 0
            Integer limit = params.limit ? params.limit as Integer : null

            result.questionTypeData = wpmainService.getQuestionTypeData(chapterId, questionType, offset, limit)
            result.offset = offset
            result.limit = limit
        }

        render result as JSON
    }

    @Transactional
    def getExplanation() {
        Long questionId = params.questionId as Long
        String explanation = wpmainService.getExplanation(questionId)

        render([explanation: explanation] as JSON)
    }

    @Transactional
    def qpcreator() {
        if(params.bookId==null) params.bookId="312814"
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))

        if (redisService.("chapters_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId))
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))

        // Get next paper number for auto-naming
        def existingPapers = wpmainService.getQuestionPapersList(new Long(bookId))
        def nextPaperNumber = (existingPapers?.size() ?: 0) + 1

        [chaptersList: chaptersList, bookId: bookId, bookTitle: booksMst.title, nextPaperNumber: nextPaperNumber]
    }

    @Transactional
    def getAvailableQuestionCounts() {
        def chapterIds = params.chapterIds?.split(',')?.collect { it as Long } ?: []

        if (chapterIds.isEmpty()) {
            render([success: false, message: "No chapters selected"] as JSON)
            return
        }

        def counts = wpmainService.getAvailableQuestionCounts(chapterIds)
        render([success: true, counts: counts] as JSON)
    }

    @Transactional
    def generateQuestionPaper() {
        try {
            println("=== generateQuestionPaper called ===")
            println("All params: ${params}")

            def paperName = params.paperName
            def paperHeader = params.paperHeader
            def chapterIds = params.chapterIds?.split(',')?.collect { it as Long } ?: []
            def bookId = params.bookId as Long

            println("paperName: ${paperName}")
            println("paperHeader: ${paperHeader}")
            println("chapterIds: ${chapterIds}")
            println("bookId: ${bookId}")

            // Parse sections data - dynamically find all sections instead of limiting to 5
            def sections = []
            int sectionIndex = 1
            while (params["section${sectionIndex}Header"] != null) {
                def sectionHeader = params["section${sectionIndex}Header"]
                def marksPerQuestion = params["section${sectionIndex}Marks"]
                def totalQuestions = params["section${sectionIndex}Questions"]
                def questionType = params["section${sectionIndex}Type"]
                def difficultyLevel = params["section${sectionIndex}Difficulty"]

                println("Section ${sectionIndex}: header=${sectionHeader}, marks=${marksPerQuestion}, questions=${totalQuestions}, type=${questionType}, difficulty=${difficultyLevel}")

                if (sectionHeader && marksPerQuestion && totalQuestions && questionType) {
                    sections << [
                        sectionHeader: sectionHeader,
                        marksPerQuestion: marksPerQuestion,
                        totalQuestions: totalQuestions,
                        questionType: questionType,
                        difficultyLevel: difficultyLevel
                    ]
                }
                sectionIndex++
            }

            println("Parsed sections: ${sections}")

            if (sections.isEmpty()) {
                println("No sections found - returning error")
                render([success: false, message: "At least one section must be configured"] as JSON)
                return
            }

            def createdBy = springSecurityService.currentUser?.username ?: "system"
            println("createdBy: ${createdBy}")

            def result = wpmainService.generateQuestionPaper(
                paperName, paperHeader, chapterIds, sections, createdBy,  bookId)

            println("Service result: ${result}")
            render(result as JSON)
        } catch (Exception e) {
            println("Error in generateQuestionPaper: ${e.message}")
            e.printStackTrace()
            render([success: false, message: e.message] as JSON)
        }
    }

    @Transactional
    def qpview() {
        def questionPaperId = params.id as Long
        if (!questionPaperId) {
            flash.message = "Question paper ID is required"
            redirect(action: "qplist", params: [bookId: params.bookId])
            return
        }

        def result = wpmainService.getQuestionPaperDetails(questionPaperId)
        if (!result.success) {
            flash.message = result.message
            redirect(action: "qplist", params: [bookId: params.bookId])
            return
        }

        [questionPaper: result.questionPaper, sections: result.sections, bookId: params.bookId]
    }

    @Transactional
    def qpprint() {
        def questionPaperId = params.id as Long
        if (!questionPaperId) {
            render "Question paper ID is required"
            return
        }

        def result = wpmainService.getQuestionPaperDetails(questionPaperId)
        if (!result.success) {
            render result.message
            return
        }

        [questionPaper: result.questionPaper, sections: result.sections]
    }

    @Transactional
    def qplist() {
        def bookId = params.bookId as Long
        if (!bookId) {
            render "Book ID is required"
            return
        }

        BooksMst booksMst = dataProviderService.getBooksMst(bookId)
        def questionPapers = wpmainService.getQuestionPapersList(bookId)

        [questionPapers: questionPapers, bookId: bookId, bookTitle: booksMst?.title]
    }

    @Transactional
    def mockpapercreator() {
        if(params.bookId==null) params.bookId="312814"
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))

        if (redisService.("chapters_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId))
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))

        // Get next paper number for auto-naming (count all papers for now)
        def existingPapers = wpmainService.getQuestionPapersList(new Long(bookId))
        def nextPaperNumber = (existingPapers?.size() ?: 0) + 1

        [chaptersList: chaptersList, bookId: bookId, bookTitle: booksMst.title, nextPaperNumber: nextPaperNumber]
    }

    @Transactional
    def mockpaperlist() {
        def bookId = params.bookId as Long
        if (!bookId) {
            render "Book ID is required"
            return
        }

        BooksMst booksMst = dataProviderService.getBooksMst(bookId)
        // For now, use the same method as regular question papers
        // In future, we can filter by a mock paper flag
        def mockPapers = wpmainService.getQuestionPapersList(bookId)

        [mockPapers: mockPapers, bookId: bookId, bookTitle: booksMst?.title]
    }

    @Transactional
    def generateNewQuestionPaper() {
        try {
            def originalPaperId = params.originalPaperId as Long
            def newPaperName = params.newPaperName

            if (!originalPaperId || !newPaperName) {
                render([success: false, message: "Original paper ID and new paper name are required"] as JSON)
                return
            }

            // Get original paper details to reuse configuration
            def originalResult = wpmainService.getQuestionPaperDetails(originalPaperId)
            if (!originalResult.success) {
                render([success: false, message: "Original question paper not found"] as JSON)
                return
            }

            def originalPaper = originalResult.questionPaper
            def originalSections = originalResult.sections

            // Get chapters from original paper
            def chapterIds = wpmainService.getChapterIdsFromQuestionPaper(originalPaperId)

            // Recreate sections configuration
            def sections = []
            originalSections.each { section ->
                sections << [
                    sectionHeader: section.sectionHeading,
                    marksPerQuestion: section.totalMarks / section.noOfQuestions,
                    totalQuestions: section.noOfQuestions,
                    questionType: section.questionType,
                    difficultyLevel: 'All' // Default to All for new generation
                ]
            }

            def createdBy = springSecurityService.currentUser?.username ?: "system"

            def result = wpmainService.generateQuestionPaper(
                newPaperName, originalPaper.header, chapterIds, sections, createdBy,  originalPaper.bookId
            )

            render(result as JSON)
        } catch (Exception e) {
            println("Error in generateNewQuestionPaper: ${e.message}")
            render([success: false, message: e.message] as JSON)
        }
    }

    @Transactional
    def deleteQuestion() {
        try {
            Long objId = params.objId as Long

            if (!objId) {
                render([success: false, message: "Question ID is required", objId: objId] as JSON)
                return
            }

            def result = wpmainService.deleteQuestion(objId)

            render([success: result.success, message: result.message, objId: objId] as JSON)

        } catch (Exception e) {
            log.error("Error deleting question: ${e.message}", e)
            render([success: false, message: "An error occurred while deleting the question", objId: params.objId] as JSON)
        }
    }

    @Transactional
    def aiBookDtl(){
        Integer siteId = utilService.getSiteId(request, session)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if ("true".equals(siteMst.commonWhiteLabel)) {
            userManagementService.setUserSession(params.siteName, session, servletContext, response)
        }

        if (springSecurityService.currentUser != null && session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }
        String bookId = params.bookId
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(bookId))
        BooksDtl booksDtl = dataProviderService.getBooksDtl(new Long(bookId))

        // Get chapters list for book overview
        if (redisService.("chapters_" + bookId) == null) {
            dataProviderService.getChaptersList(new Long(bookId))
        }
        List chaptersList = new JsonSlurper().parseText(redisService.("chapters_" + bookId))

        // Get book overview data for all chapters
        def bookOverviewData = wpmainService.getBookOverviewData(new Long(bookId), chaptersList)

        // Get book-level summary
        def bookLevelSummary = wpmainService.getBookLevelSummary(new Long(bookId), chaptersList)

        // Get testimonials
        def testimonials = wpmainService.getTestimonials()

        // Get pricing information from BookPriceDtl
        BookPriceDtl bookPriceDtl = BookPriceDtl.findByBookIdAndBookType(new Integer(bookId), "bookGPT")

        // Check for book variants (Exam Pro, Exam Master, Mega Question Bank)
        def bookVariants = wpmainService.getBookVariants(new Long(bookId))

        [booksMst: booksMst, booksDtl: booksDtl, chaptersList: chaptersList, bookOverviewData: bookOverviewData,
         bookLevelSummary: bookLevelSummary, testimonials: testimonials,
         bookPriceDtl: bookPriceDtl, bookVariants: bookVariants, title: booksMst.title + " - Book Details"]
    }

}
