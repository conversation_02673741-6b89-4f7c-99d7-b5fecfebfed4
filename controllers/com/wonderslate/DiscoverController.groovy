package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.cache.SearchService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.institute.InstituteIpAddress
import com.wonderslate.log.Search
import com.wonderslate.logs.AsyncLogsService
import com.wonderslate.publish.Authors
import com.wonderslate.publish.BooksAuthorDtl
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.shop.BookPriceDtl
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.transaction.Transactional
import groovy.sql.Sql

import javax.sql.DataSource
import java.text.ParseException
import java.text.SimpleDateFormat

class DiscoverController {
    DataProviderService dataProviderService
    def redisService
    SpringSecurityService springSecurityService
    SearchService searchService
    AsyncLogsService asyncLogsService

    def index() { }

    def searchList(){
        Integer siteId = getSiteId(request)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=siteId.toString();
        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                dataProviderService.getSiteIdList(siteId)
            }
            
            siteIdList = redisService.("siteIdList_"+siteId)
        }
        else if(siteMst.associatedSites!=null&&!"".equals(siteMst.associatedSites)) siteIdList += ","+siteMst.associatedSites
        HashMap searchMainMap
         def json
        if(params.batchId!=null&&!"".equals(params.batchId)){
            if (servletContext.getAttribute("searchLibraryMap_" + params.batchId) == null) searchService.librarySearch(params.batchId)
            json = processSearch(servletContext.getAttribute("searchLibraryMap_"+params.batchId)!=null?servletContext.getAttribute("searchLibraryMap_"+params.batchId):new HashMap())
        }
       else if(params.printSearchType!=null||"ibookso".equals(siteMst.siteName)){
            if (servletContext.getAttribute("searchPrintMainMap") == null&&!"searchListBuilding".equals(redisService.("searchPrintMainMap"))) {
                redisService.("searchPrintMainMap") = "searchListBuilding"
                asyncLogsService.updatePrintSearchMap();
                json = processSearch(new HashMap())
            }else {
                json = processSearch(servletContext.getAttribute("searchPrintMainMap")!=null?servletContext.getAttribute("searchPrintMainMap"):new HashMap())
            }
      } else {
            if (servletContext.getAttribute("searchMainMap_" + siteId) == null&&!"searchListBuilding".equals(redisService.("searchMainMap_" + siteId)))
            {   redisService.("searchMainMap_" + siteId) = "searchListBuilding"
                asyncLogsService.updateSearchMap(siteId, siteIdList);
                json = processSearch(new HashMap())
            };
                else{
                    json = processSearcEbooks(servletContext.getAttribute("searchMainMap_"+siteId)!=null?servletContext.getAttribute("searchMainMap_"+siteId):new HashMap(),
                            servletContext.getAttribute("searchMainMapList_"+siteId)!=null?servletContext.getAttribute("searchMainMapList_"+siteId):new HashMap())
                };

        }
        render json as JSON
    }

    def processSearcEbooks(HashMap searchMainMap,List sortedHashMaps){
        println("getting from here man")
        String[] queryStrings
        List searchStrings = []
        List oneWordMatchStrings = []
        List searchValues = []
        if(params.query!=null) {
            queryStrings = params.query.split("\\s+")
            int noOfSearchStrings = 10;
            for(int j;j<sortedHashMaps.size();j++) {
                for (String it : sortedHashMaps[j].keySet()) {
                    boolean matchFound = true;
                    boolean oneMatchFound = false;

                    //first attempt to see if the search matches with the starting thingy
                    if(it.toLowerCase().indexOf(params.query)==0){
                        searchStrings.add(it)
                        searchValues.add(searchMainMap.get(it))
                    }
                    for (int i = 0; i < queryStrings.length; i++) {
                        if (it.toLowerCase().indexOf(queryStrings[i].toLowerCase()) == -1) {
                            matchFound = false
                            //break;
                            continue;
                        }
                        oneMatchFound = true
                    }

                    if (matchFound&&searchMainMap.get(it)!=null&&searchStrings.indexOf(it)==-1) {
                        searchStrings.add(it)
                        searchValues.add(searchMainMap.get(it))
                    }
                    if (oneMatchFound && oneWordMatchStrings.indexOf(it) == -1) {
                        oneWordMatchStrings.add(it)
                    }

                    if (searchStrings.size() >= noOfSearchStrings) break;
                }
                if (searchStrings.size() >= noOfSearchStrings) break;
            }
            if(searchStrings.size() < noOfSearchStrings){
                for(int i = 0;i < oneWordMatchStrings.size(); i++){
                    if(searchStrings.indexOf(oneWordMatchStrings.get(i)) == -1 && searchStrings.size() < noOfSearchStrings&&searchMainMap.get(oneWordMatchStrings.get(i))!=null) {
                        searchStrings.add(oneWordMatchStrings.get(i))
                        searchValues.add(searchMainMap.get(oneWordMatchStrings.get(i)))
                    }
                }
            }
        }
        def json =  [
                'searchList':  searchStrings,
                'searchValues': searchValues,
                'status':  searchMainMap ? "OK" : "Nothing present"
        ]

        return json
    }

    def processSearch(HashMap searchMainMap){
       String[] queryStrings
        List searchStrings = []
        List oneWordMatchStrings = []
        List searchValues = []
        if(params.query!=null) {
            queryStrings = params.query.split("\\s+")
            int noOfSearchStrings = 10;
                for (String it : searchMainMap.keySet()) {
                    boolean matchFound = true;
                    boolean oneMatchFound = false;

                    //first attempt to see if the search matches with the starting thingy
                    if(it.toLowerCase().indexOf(params.query)==0){
                        searchStrings.add(it)
                        searchValues.add(searchMainMap.get(it))
                    }
                    for (int i = 0; i < queryStrings.length; i++) {
                        if (it.toLowerCase().indexOf(queryStrings[i].toLowerCase()) == -1) {
                            matchFound = false
                            //break;
                            continue;
                        }
                        oneMatchFound = true
                    }

                    if (matchFound&&searchMainMap.get(it)!=null&&searchStrings.indexOf(it)==-1) {
                        searchStrings.add(it)
                        searchValues.add(searchMainMap.get(it))
                    }
                    if (oneMatchFound && oneWordMatchStrings.indexOf(it) == -1) {
                        oneWordMatchStrings.add(it)
                    }

                    if (searchStrings.size() >= noOfSearchStrings) break;
                }


            if(searchStrings.size() < noOfSearchStrings){
                for(int i = 0;i < oneWordMatchStrings.size(); i++){
                    if(searchStrings.indexOf(oneWordMatchStrings.get(i)) == -1 && searchStrings.size() < noOfSearchStrings&&searchMainMap.get(oneWordMatchStrings.get(i))!=null) {
                        searchStrings.add(oneWordMatchStrings.get(i))
                        searchValues.add(searchMainMap.get(oneWordMatchStrings.get(i)))
                    }
                }
            }
        }
        def json =  [
                'searchList':  searchStrings,
                'searchValues': searchValues,
                'status':  searchMainMap ? "OK" : "Nothing present"
        ]
        return json
    }
    @Transactional
    def resourceSearch(){
        int noOfResourcesToSearch=10;
        Integer siteId = getSiteId(request)
        String siteIdList=siteId.toString()
        List searchStrings = []


        HashMap searchParameters
        List resourcesList = []
        List resourceIds=[]



        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
                SiteMst siteMst = dataProviderService.getSiteMst(siteId)
                dataProviderService.getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)
        }

        if (servletContext.getAttribute("resourceSearchMap"+params.resourceType+"_"+siteId) == null) searchService.buildResourcesSearch(siteId,siteIdList,params.resourceType)
        HashMap searchMainMap = servletContext.getAttribute("resourceSearchMap"+params.resourceType+"_"+siteId)
        if(searchMainMap.containsKey(params.searchString)) {
            searchStrings.add(params.searchString)
            }
        else searchStrings = getMatchingKeys(params.searchString.split("\\s+"), searchMainMap)
        for(String searchString:searchStrings) {
            if(searchMainMap.containsKey(searchString) && resourceIds.size() <= 25){
                searchParameters = searchMainMap.get(searchString)
                getSearchResourceList(searchParameters, resourceIds, noOfResourcesToSearch,params.resourceType)
            }
        }
        if(resourceIds.size() > 0){
            String bookTitle,chapterTitle,price,bookId,chapterId,previewChapter,createdBy,createdByUsername

            resourceIds.each {
                bookTitle=null
                chapterTitle=null
                price=null
                bookId=null
                chapterId=null
                previewChapter=null
                ResourceDtl resourceDtl = dataProviderService.getResourceDtl(it)
               if(resourceDtl.chapterId!=null){
                    ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                    BooksMst booksMst = dataProviderService.getBooksMst(chaptersMst.bookId)
                    bookTitle = booksMst.title
                    bookId = ""+booksMst.id
                    price = ""+booksMst.price
                    chapterTitle = chaptersMst.name
                    chapterId = ""+chaptersMst.id
                    previewChapter = chaptersMst.previewChapter
                }
                println("resId="+resourceDtl.id)
                resourcesList.add([resId : resourceDtl.id, title: resourceDtl.resourceName, dateCreated: resourceDtl.dateCreated,
                                    resType  : resourceDtl.resType, resSubType: resourceDtl.resSubType,
                bookTitle:bookTitle,bookId:bookId,price:price,chapterTitle:chapterTitle,chapterId:chapterId,previewChapter:previewChapter,
                                   createdBy:resourceDtl.sharing!=null?dataProviderService.getUserMst(resourceDtl.createdBy).name:"",
                                   createdByUsername:resourceDtl.sharing!=null?resourceDtl.createdBy:"",
                                   profilePic:resourceDtl.sharing!=null?dataProviderService.getUserMst(resourceDtl.createdBy).profilepic:""]);


            }

            Search search =  new Search(
                    searchString:params.searchString,
                    username:(springSecurityService.currentUser!=null)?springSecurityService.currentUser.username:"",
                    resultsFound:(resourcesList.size()==0)?"No":"Yes");
            search.save(failOnError: true, flush: true)



            def json = [
                    'resources':resourcesList,
                    'status' : resourcesList ? "OK" : "Nothing present",
                    'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST"),
                    'search' : true
            ]

            render json as JSON
        } else {
            def json =  [
                    'status': "Nothing present"
            ]

            render json as JSON
        }

    }

    @Transactional
    def search(){
        int noOfBooksToSearch=10;
        Integer siteId = getSiteId(request)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        String searchStr = params.searchString
        searchStr = searchStr.replaceAll("undefined","")
        if("true".equals(siteMst.appInApp)) siteId = new Integer(1)

        String siteIdList=siteId.toString()
        List searchStrings = []

        if (servletContext.getAttribute("searchMainMap_" + siteId) == null) dataProviderService.updateSearchMap(siteId, siteIdList);

        HashMap searchMainMap = servletContext.getAttribute("searchMainMap_"+siteId)

        HashMap searchParameters
        List booksList = []
        List bookIds=[]
        BooksTagDtl booksTagDtl
        List bookTags=[]
        def publisherName
        def publisherId

        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {

                dataProviderService.getSiteIdList(siteId)
            }
        
            siteIdList = redisService.("siteIdList_"+siteId)
        }


        if(searchMainMap.containsKey(searchStr)) searchStrings.add(searchStr)
        else searchStrings = getMatchingKeys(searchStr.split("\\s+"), searchMainMap)
        for(String searchString:searchStrings) {
            if(searchMainMap.containsKey(searchString) && bookIds.size() <= 25){
                searchParameters = searchMainMap.get(searchString)

                getSearchBookList(searchParameters, bookIds, booksTagDtl, bookTags, noOfBooksToSearch)
            }
        }
        if(bookIds.size() > 0){
            String[] siteIds=siteIdList.split(",");

            bookIds.each {
                BooksMst booksMst = dataProviderService.getBooksMst(it)
                if(booksMst!=null) {
                    boolean canShowBookFromThisSite = Arrays.asList(siteIds).contains("" + booksMst.siteId);

                    if ("published".equals(booksMst.status) && canShowBookFromThisSite) {
                        publisherId = null
                        publisherName = null
                        if (booksMst.publisherId != null) {
                            Publishers publishers = dataProviderService.getPublisher(booksMst.publisherId)
                            publisherId = publishers.id
                            publisherName = publishers.name
                        }

                        if (siteId.intValue() == 12 || siteId.intValue() == 23) {
                            booksList.add([id           : booksMst.id, title: booksMst.title, coverImage: booksMst.coverImage,
                                           subjectyear  : booksMst.subjectyear, 'listPrice': booksMst.listprice, 'rating': booksMst.rating,
                                           'offerPrice' : booksMst.price, 'publisher': publisherName, 'publisherId': publisherId,
                                           'chapterType': booksMst.testTypeBook, 'bookType': booksMst.bookType,
                                           'authors'    : ("" + booksMst.authors).replace(':', ' ').replace(',', '-'),
                                           'language'   : booksMst.language]);
                        } else {
                            Double upgradePrice = null
                            Double bookPrice =  null
                            Double bookListPrice = null
                            Double testSeriesPrice = null
                            Double testSeriesListPrice = null
                            List bookPriceDtls = BookPriceDtl.findAllByBookId(booksMst.id)
                            bookPriceDtls.each{ price->
                                if("printbook".equals(price.bookType)){
                                    bookPrice = price.sellPrice
                                    bookListPrice = price.listPrice
                                }
                                else if("upgrade".equals(price.bookType)){
                                    upgradePrice = price.sellPrice
                                }
                                else if("testSeries".equals(price.bookType)){
                                    testSeriesPrice = price.sellPrice
                                    testSeriesListPrice = price.listPrice
                                }
                            }
                            if(bookPrice==null){
                                bookPriceDtls.each{ price->
                                    if("eBook".equals(price.bookType)){
                                        bookPrice = price.sellPrice
                                        bookListPrice = price.listPrice
                                    }

                                }
                            }
                            booksList.add([id           : booksMst.id, title: booksMst.title, coverImage: booksMst.coverImage,
                                           subjectyear  : booksMst.subjectyear, 'listPrice': bookListPrice, 'rating': booksMst.rating,
                                           'offerPrice' : bookPrice, 'publisher': publisherName, 'publisherId': publisherId,
                                           'chapterType': booksMst.testTypeBook, 'bookType': booksMst.bookType,
                                           'authors'    : ("" + booksMst.authors).replace(':', ' ').replace(',', '-'),
                                           'buylink1': booksMst.buylink1,showDiscount : booksMst.showDiscount,
                                           testsPrice:testSeriesPrice,testsListprice:testSeriesListPrice,upgradePrice:upgradePrice,isbn:booksMst.isbn]);
                        }
                    }
                }
            }
            try {
                Search search = new Search(
                        siteId: getSiteId(request),
                        searchString: searchStr,
                        username: (springSecurityService.currentUser != null) ? springSecurityService.currentUser.username : "",
                        resultsFound: (booksList.size() == 0) ? "No" : "Yes");
                search.save(failOnError: true, flush: true)
            }catch(Exception e){
                //dont anything right now
            }

            List booksList1 = []
            if((siteId.intValue()==12||siteId.intValue()==23) && params.page=="library" ) {
                if (redisService.("librarybooklist_" + instituteId) == null) dataProviderService.getLibraryBooks(instituteId)
                String libraryids = redisService.("librarybookidlist_" + instituteId)
                for (int i = 0; i < booksList.size(); i++ ) {
                    if (libraryids.indexOf("," + booksList[i].id+ ",") > -1) booksList1.push(booksList[i])

                }
                booksList=booksList1;
            }

            def json = [
                    'books':booksList,
                    'booksTag' : bookTags,
                    'status' : booksList ? "OK" : "Nothing present",
                    'serverTime':convertDate(Calendar.getInstance().getTime(),"UTC","IST"),
                    'search' : true
            ]

            render json as JSON
        } else {
            Search search =  new Search(
                    siteId: getSiteId(request),
                    searchString:searchStr,
                    username:(springSecurityService.currentUser!=null)?springSecurityService.currentUser.username:"",
                    resultsFound:(booksList.size()==0)?"No":"Yes");
            search.save(failOnError: true, flush: true)
            def json =  [
                    'status': "Nothing present"
            ]

            render json as JSON
        }
    }
    @Transactional
    void getSearchBookList(HashMap searchParameters,List bookIds,BooksTagDtl booksTagDtl,List bookTags1, int noOfBooksToSearch){

        List bookTags = []
        if(searchParameters.containsKey("bookId")) {
           String[] tempBookIds = (""+searchParameters.get("bookId")).split(",")
            for(int i=0;i<tempBookIds.length;i++)
            bookIds.add(new Integer(tempBookIds[i]))

        } else if(searchParameters.containsKey("syllabus") && searchParameters.containsKey("grade")
                && searchParameters.containsKey("subject")) {
            // search parameters with syllabus,grade,subject
            bookTags = getSubjectBooks(searchParameters.get("syllabus"),searchParameters.get("grade"),searchParameters.get("subject"))
            bookTags.each {book->
                if(!bookIds.contains(book.bookId)) bookIds.add(book.bookId)
            }
             if(bookIds.size()<noOfBooksToSearch) {
                bookTags = getGradeBooks(searchParameters.get("syllabus"),searchParameters.get("grade"))
                bookTags.each { book ->
                    if(!bookIds.contains(book.bookId)) bookIds.add(book.bookId)
                }

                if(bookIds.size()<noOfBooksToSearch) {
                    bookTags = getSyllabusBooks(searchParameters.get("syllabus"))
                    bookTags.each { book ->
                        if(!bookIds.contains(book.bookId)) bookIds.add(book.bookId)
                    }
                }
            }
        } else if(searchParameters.containsKey("syllabus") && searchParameters.containsKey("grade")){
            // search parameters with syllabus,grade
            bookTags = getGradeBooks(searchParameters.get("syllabus"),searchParameters.get("grade"))
            bookTags.each {book->
                if(!bookIds.contains(book.bookId)) bookIds.add(book.bookId)
            }

            if(bookIds.size()<noOfBooksToSearch) {
                bookTags = getSyllabusBooks(searchParameters.get("syllabus"))

                bookTags.each { book ->
                    if(!bookIds.contains(book.bookId)){
                        bookIds.add(book.bookId)
                    }
                }
            }
        } else if(searchParameters.containsKey("grade") && searchParameters.containsKey("subject")){
            // search parameters with syllabus,grade
            bookTags = getGradeSubjectBooks(searchParameters.get("grade"),searchParameters.get("subject"))
            bookTags.each {book->
                if(!bookIds.contains(book.bookId)) bookIds.add(book.bookId)
            }
        } else if(searchParameters.containsKey("syllabus")){
            // search parameters with syllabus
            bookTags = getSyllabusBooks(searchParameters.get("syllabus"))
            bookTags.each {book->
                if(!bookIds.contains(book.bookId)) bookIds.add(book.bookId)
            }
        } else if(searchParameters.containsKey("grade")){
            // search parameters with grade
            bookTags = getGradeOnlyBooks(searchParameters.get("grade"))
            bookTags.each {book->
                if(!bookIds.contains(book.bookId)) bookIds.add(book.bookId)
            }
        } else if(searchParameters.containsKey("author")){
            // search parameters with syllabus
            List authors = Authors.findAllByName(searchParameters.get("author"))
            authors.each { author ->
                List books = BooksAuthorDtl.findAllByAuthorId(author.id)

                books.each{ book->
                    if(!bookIds.contains(book.bookId)) {
                        BooksTagDtl booksTagDtl1 = BooksTagDtl.findByBookId(book.bookId);
                        if(booksTagDtl1!=null) {
                            bookIds.add(book.bookId)
                            bookTags.add(booksTagDtl1)
                        }
                    }
                }
            }
        } else if(searchParameters.containsKey("publisher")){
            // search parameters with syllabus
          //  println("publisher only");
            String sql  = "select id" +
                    " from books_mst " +
                    " where  publisher_id="+searchParameters.get("publisherId")+" and status='published'"
            DataSource dataSource = grailsApplication.mainContext.getBean('dataSource')
            Sql sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)


            results.each{ book->
                if(!bookIds.contains(book.id)) {
                    BooksTagDtl booksTagDtl1 = BooksTagDtl.findByBookId(book.id);

                    if(booksTagDtl1!=null) {
                        bookIds.add(book.id)
                        bookTags.add(booksTagDtl1)
                    }
                }
            }
        }
        for(int i=0; i<bookTags.size();i++){
            bookTags1.add(bookTags.get(i))
        }


    }

     List getMatchingKeys(String [] queryStrings, HashMap searchMainMap){
        List searchStrings = []
        List oneWordMatchStrings = []
//        queryStrings = params.query.split("\\s+")
        int noOfSearchStrings = 10;

        for (String it : searchMainMap.keySet()) {
            boolean matchFound = true;
            boolean oneMatchFound = false;
            for (int i = 0; i < queryStrings.length; i++) {
                if (it.toLowerCase().indexOf(queryStrings[i].toLowerCase()) == -1) {
                    matchFound = false
                    //break;
                    continue;
                }
                oneMatchFound = true
            }

            if(matchFound) {
                searchStrings.add(it)
            }
            if(oneMatchFound && oneWordMatchStrings.indexOf(it) == -1) {
                oneWordMatchStrings.add(it)
            }

            if (searchStrings.size() >= noOfSearchStrings) break;
        }
        if(searchStrings.size() < noOfSearchStrings){
            for(int i = 0;i < oneWordMatchStrings.size(); i++){
                if(searchStrings.indexOf(oneWordMatchStrings.get(i)) == -1 && searchStrings.size() < noOfSearchStrings) searchStrings.add(oneWordMatchStrings.get(i))
            }
        }
        return searchStrings;
    }

    def getGradeResources(syllabus,grade){
        return ResourceDtl.findAllBySyllabusAndGrade(syllabus,grade)
    }
    def getSyllabusGradeResources(syllabus,grade){
        return ResourceDtl.findAllBySyllabusAndGrade(syllabus,grade)
    }
    def getSyllabusResources(syllabus){
        return ResourceDtl.findAllBySyllabus(syllabus)
    }
    def getSubjectResources(syllabus,grade,subject){
        return ResourceDtl.findAllBySyllabusAndGradeAndSubject(syllabus,grade,subject)
    }
    def getGradeSubjectResources(grade,subject){
        return ResourceDtl.findAllByGradeAndSubject(grade,subject)
    }
    def getGradeOnlyResources(grade){
        return ResourceDtl.findAllByGrade(grade)
    }

    @Transactional
    void getSearchResourceList(HashMap searchParameters,List resourceIds, int noOfResourcesToSearch,String resourceType){

        HashMap searchFilters = searchService.getResourceTypes()
        List resources
        if(searchParameters.containsKey("resId")) {
            println("this is suppose to be true "+searchParameters.get("resId"));
            ResourceDtl resourceDtl = dataProviderService.getResourceDtl(searchParameters.get("resId"))
            resourceIds.add(resourceDtl.id)

        }else if(searchParameters.containsKey("chapterId")){
            String sql  = "select id" +
                    " from resource_dtl rd" +
                    " where  (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                    " and rd.chapter_id=" +searchParameters.get("chapterId")
             DataSource dataSource = grailsApplication.mainContext.getBean('dataSource')
            Sql sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            results.each{ resource ->
                if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
            }

        } else if(searchParameters.containsKey("bookId")){
            String sql  = "select rd.id" +
                    " from resource_dtl rd,chapters_mst cm" +
                    " where  (rd.res_type in ("+searchFilters.get(resourceType)+") or rd.res_sub_type in ("+searchFilters.get(resourceType)+")) "+
                    " and cm.id=rd.chapter_id" +
                    " and cm.book_id="+searchParameters.get("bookId")
            DataSource dataSource = grailsApplication.mainContext.getBean('dataSource')
            Sql sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            results.each{ resource ->
                if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
            }

        } else if(searchParameters.containsKey("syllabus") && searchParameters.containsKey("grade")
                && searchParameters.containsKey("subject")) {
            // search parameters with syllabus,grade,subject
            resources = getSubjectResources(searchParameters.get("syllabus"),searchParameters.get("grade"),searchParameters.get("subject"))
            resources.each { resource ->
                if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
            }
            if(resourceIds.size()<noOfResourcesToSearch) {
                resources = getGradeResources(searchParameters.get("syllabus"),searchParameters.get("grade"))
                resources.each { resource ->
                    if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
                }

                if(resourceIds.size()<noOfResourcesToSearch) {
                    resources = getSyllabusResources(searchParameters.get("syllabus"))
                    resources.each { resource ->
                        if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
                    }
                }
            }
        } else if(searchParameters.containsKey("syllabus") && searchParameters.containsKey("grade")){
            // search parameters with syllabus,grade
            resources = getGradeResources(searchParameters.get("syllabus"),searchParameters.get("grade"))
            resources.each { resource ->
                if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
            }

            if(resourceIds.size()<noOfResourcesToSearch) {
                resources = getSyllabusResources(searchParameters.get("syllabus"))

                resources.each { resource ->
                    if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
                }
            }
        } else if(searchParameters.containsKey("grade") && searchParameters.containsKey("subject")){
            // search parameters with syllabus,grade
            resources = getGradeSubjectResources(searchParameters.get("grade"),searchParameters.get("subject"))
            resources.each { resource ->
                if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
            }
        } else if(searchParameters.containsKey("syllabus")){
            // search parameters with syllabus
            resources = getSyllabusResources(searchParameters.get("syllabus"))
            resources.each { resource ->
                if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
            }
        } else if(searchParameters.containsKey("grade")){
            // search parameters with grade
            resources = getGradeOnlyResources(searchParameters.get("grade"))
            resources.each { resource ->
                if (!resourceIds.contains(resource.id)) resourceIds.add(resource.id)
            }
        }



    }


    //duplicate method. Original in Wonderpublish.. short cut for dev
    def  Integer getSiteId(request){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(params.siteId!=null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    def getSubjectBooks(syllabus,grade,subject){
        return BooksTagDtl.findAllBySyllabusAndGradeAndSubject(syllabus,grade,subject)

    }

    def getGradeBooks(syllabus,grade){
        return BooksTagDtl.findAllBySyllabusAndGrade(syllabus,grade)
    }
    
    def getSyllabusBooks(syllabus){
        return BooksTagDtl.findAllBySyllabus(syllabus)
    }

    def getGradeSubjectBooks(grade,subject){
        return BooksTagDtl.findAllByGradeAndSubject(grade,subject);
    }

    def getGradeOnlyBooks(grade){
        return BooksTagDtl.findAllByGrade(grade)
    }

    Date convertDate(Date dateFrom, String fromTimeZone, String toTimeZone) throws ParseException {
        String pattern = "yyyy/MM/dd HH:mm:ss";
        SimpleDateFormat sdfFrom = new SimpleDateFormat (pattern);
        sdfFrom.setTimeZone(TimeZone.getTimeZone(fromTimeZone));

        SimpleDateFormat sdfTo = new SimpleDateFormat (pattern);
        sdfTo.setTimeZone(TimeZone.getTimeZone(toTimeZone));
        Date dateTo = sdfFrom.parse(sdfTo.format(dateFrom));
        return dateTo;
    }

    @Transactional
    def getInstituteId(){
        Integer siteId = getSiteId(request)
        String ipAddress = getIPAddressOfClient()
        def instituteId = null
        InstituteIpAddress instituteIPAddress = InstituteIpAddress.findByIpAddressAndSiteId(ipAddress,siteId)
        if(instituteIPAddress==null){
            if(springSecurityService.currentUser!=null) {
                String sql = "select im.id from wsuser.institute_mst im, wsuser.course_batches_dtl cbd,wsuser.batch_user_dtl bud" +
                        " where bud.username='" + springSecurityService.currentUser.username + "' and cbd.id=bud.batch_id and im.id=cbd.conducted_by and cbd.status='active'  and im.site_id=" + session["siteId"];
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);
                if (results.size() > 0) {
                    instituteId = results[0][0]

                } else {
                    def json = ['status': "Nothing present"]
                    render json as JSON
                }
            }
            else {
                def json = ['status': "Nothing present"]
                render json as JSON
            }
        }else{
            instituteId = instituteIPAddress.institute_id
        }

        return instituteId
    }

    String getIPAddressOfClient() {
        //https://www.oodlestechnologies.com/blogs/Java-Get-Client-IP-Address
        String remoteAddr = request.getHeader("X-FORWARDED-FOR");
        if(remoteAddr == null || "".equals(remoteAddr)) {
            remoteAddr = request.getRemoteAddr();
            if(remoteAddr.equalsIgnoreCase("0:0:0:0:0:0:0:1")) {
                InetAddress inetAddress = InetAddress.getLocalHost();
                String ipAddress = inetAddress.getHostAddress();
                remoteAddr = ipAddress;


            }
        }

        return remoteAddr
    }



}
