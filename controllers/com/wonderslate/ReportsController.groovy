package com.wonderslate

import com.wonderslate.admin.ReportsService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.institute.InstituteUserDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.shop.DiscountMst
import com.wonderslate.shop.ExOrderUser
import com.wonderslate.shop.OutsidePurchase
import com.wonderslate.shop.PurchaseService
import com.wonderslate.shop.ShippingAddressMst
import com.wonderslate.shop.ShoppingCartOrdersMst
import com.wonderslate.shop.SubscriptionMst
import com.wonderslate.shop.VendorDeliveryCosts
import com.wonderslate.shop.WsshopService
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import pl.touk.excel.export.WebXlsxExporter

import java.text.DateFormat
import java.text.SimpleDateFormat
import grails.orm.PagedResultList
import com.wonderslate.data.ExternalOrders
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import groovy.transform.CompileStatic
import java.text.SimpleDateFormat

class ReportsController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService
    UtilService utilService
    WsshopService wsshopService
    PurchaseService purchaseService
    ReportsService reportsService
    def redisService
    def index() { }

    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_INSTITUTE_REPORT_MANAGER']) @Transactional
    def instituteReport(){
        if(session.getAttribute("userdetails")==null){
            session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
        }

        def instituteIds = []

        List instituteUserDtl = InstituteUserDtl.findAllByUsername(springSecurityService.currentUser.username)
        instituteUserDtl.each{ institute ->
            instituteIds << institute.instituteId

        }
        if(instituteIds.size()>0) {
            List batchesList = CourseBatchesDtl.findAllByStatusAndConductedForInList("active", instituteIds,[sort:"name" ])
            List institutes = InstituteMst.findAllByIdInList(instituteIds,[sort:"name" ])


            [batchesList: batchesList, institutes: institutes]
        }else [batchesList: [], institutes: []]
    }

    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_INSTITUTE_REPORT_MANAGER']) @Transactional
    def numberOfLoginsReport(){
        String sql = "SELECT " +
                "    COUNT(bvd.id) loginCount," +
                "    DATE_FORMAT(bvd.date_created, '%M %Y') monthName," +
                "    DATE_FORMAT(bvd.date_created, '%Y-%m') monthNumber" +
                "  FROM" +
                "    reports.books_view_dtl bvd,reports.course_batches_dtl cbd,reports.batch_user_dtl bud " +
                "WHERE " ;
        if(params.instituteId!=null){
            sql+=  "cbd.conducted_by="+params.instituteId+""
        }else{
            sql+=  "bud.batch_id ="+params.batchId+""
        }
        sql+=    " AND bud.batch_id = cbd.id AND " +
                "  DATE(bvd.date_created) > DATE(DATE_ADD(SYSDATE(), INTERVAL '-365' DAY)) " +
                "  AND bvd.username =bud.username" +
                "  GROUP BY monthName , monthNumber" +
                " ORDER BY monthNumber DESC";
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_reports')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql)
            List logins = results2.collect { login ->
                return [count: login.loginCount, month: login.monthName]
            }
            def json = [logins: logins ? "OK" : "Not present", logins: logins]
            render json as JSON
      }

    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_INSTITUTE_REPORT_MANAGER']) @Transactional
    def resourcesUsedReportByResType(){
            String sql ="SELECT " +
                    "    COUNT(rd.id) resourceCount," +
                    "    rd.res_type," +
                    "    DATE_FORMAT(rd.date_created, '%M %Y') monthName," +
                    "    DATE_FORMAT(rd.date_created, '%Y-%m') monthNumber" +
                    " FROM" +
                    "    reports.resource_dtl rd, reports.md_resource_view rv,reports.course_batches_dtl cbd,reports.batch_user_dtl bud WHERE " +
                    "  bud.username=rv.username AND bud.batch_id = cbd.id AND" ;
               if(params.instituteId!=null){
                sql+=  " cbd.conducted_by="+params.instituteId+""
             }else{
              sql+=  " bud.batch_id ="+params.batchId+""
            }
              sql+=     " AND rd.id = rv.resource_dtl_id" +
                    " AND date(rv.date) > date(DATE_ADD(SYSDATE(), INTERVAL '-365' DAY))" +
                    " GROUP BY monthName , monthNumber , rd.res_type" +
                    " ORDER BY monthNumber DESC"
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_reports')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql)
             List  resources = results2.collect { resource ->
                return [count: resource.resourceCount, month: resource.monthName,resType:resource.res_type]
            }
           def json = [resources: resources ? "OK" : "Not present", resources: resources]
            render json as JSON
    }

    @Secured(['ROLE_INSTITUTE_ADMIN','ROLE_INSTITUTE_REPORT_MANAGER']) @Transactional
    def resourcesUsedReport(){
            String sql = "SELECT " +
                    "    COUNT(rv.id) resourceCount," +
                    "    DATE_FORMAT(rv.date, '%M %Y') monthName," +
                    "    DATE_FORMAT(rv.date, '%Y-%m') monthNumber" +
                    " FROM" +
                    "    reports.md_resource_view rv,reports.course_batches_dtl cbd,reports.batch_user_dtl bud\n" +
                    "WHERE " +
                    " bud.batch_id = cbd.id AND " ;
              if(params.instituteId!=null){
                      sql+=  "cbd.conducted_by="+params.instituteId+"";
                 }else{
                     sql+=  "bud.batch_id ="+params.batchId+"";
                  }
               sql+= "   AND  DATE(rv.date) > DATE(DATE_ADD(SYSDATE(), INTERVAL '-365' DAY))" +
                    "        AND rv.username =bud.username" +
                    " GROUP BY monthName , monthNumber" +
                    " ORDER BY monthNumber DESC";
            def dataSource1 = grailsApplication.mainContext.getBean('dataSource_reports')
            def sql3 = new Sql(dataSource1)
            def results2 = sql3.rows(sql)
            List resources = results2.collect { resource ->
             return [count: resource.resourceCount, month: resource.monthName]
             }
            def json = [resources: resources ? "OK" : "Not present", resources: resources]
            render json as JSON
    }

    @Transactional @Secured(['ROLE_BOOK_CREATOR'])
    def getBooksCountForPublisher(){
        Integer siteId = utilService.getSiteId(request,session)
        Integer publisherId = params.publisherId!=null?new Integer(params.publisherId):session["userdetails"].publisherId
        String siteIdList=siteId.toString()

        if(siteId.intValue()==1){
            if(redisService.("siteIdList_"+siteId)==null) {
               dataProviderService.getSiteIdList(siteId)
            }

            siteIdList = redisService.("siteIdList_"+siteId)
        }
        if(redisService.("publishedCount_"+siteId)==null || redisService.("publishedCount_"+siteId+"_pub_"+publisherId)==null) dataProviderService.getBooksCountForPublisher(siteId,siteIdList,publisherId)
        def publishCount , unpublishCount
        if(publisherId==null){
            publishCount = redisService.("publishedCount_"+siteId)
            unpublishCount =  redisService.("unpublishedCount_"+siteId)
        }else{
            publishCount = redisService.("publishedCount_"+siteId+"_pub_"+publisherId)
            unpublishCount =  redisService.("unpublishedCount_"+siteId+"_pub_"+publisherId)
        }
        def json = [publishCount:publishCount, unpublishCount:unpublishCount]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_BOOK_CREATOR'])

    def getBooksAccessReportForPublisher(){
        String optionalQuery=""
        String sql = ""
        int days = 30
        if(params.noOfDays!=null) days = Integer.parseInt(params.noOfDays)
        if(params.publisherId!=null) {
            sql = "select GROUP_CONCAT(id) from books_mst where publisher_id="+params.publisherId
            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
            Sql sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            optionalQuery = " and book_id in ("+results[0][0]+")"
        }
        else if(params.bookId!=null){
            optionalQuery +=" and book_id="+params.bookId
        }

        sql = "SELECT count(*) viewCount,view_type viewType, DATE_FORMAT(DATE(convert_tz(date_created,'+00:00','+05:30')),'%d/%m/%Y') viewDate FROM wslog.books_view_dtl where DATE(date_created) > date_add(CURDATE() , INTERVAL -"+days+" day)" +
                optionalQuery +
                " GROUP BY viewDate,viewType order by viewDate"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wslog')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        def json = [results: results]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_BOOK_CREATOR'])

    def getSalesReportForPublisher(){
        String optionalQuery=""
        String sql = ""
        int days = 30
        if(params.noOfDays!=null) days = Integer.parseInt(params.noOfDays)
        if(params.publisherId!=null) {
            optionalQuery = " and bm.publisher_id="+params.publisherId
        }
        else if(params.bookId!=null){
            optionalQuery +=" and bm.id="+params.bookId
        }

        sql = "select count(*) poCount,sum(amount) poAmount, DATE_FORMAT(DATE(convert_tz(po.date_created,'+00:00','+05:30')),'%d/%m/%Y') poDate from \n" +
                " wsshop.purchase_order  po, wsshop.books_mst bm where  DATE(po.date_created) > date_add(CURDATE() , INTERVAL - "+days+" day) and po.item_code=bm.id " +optionalQuery+
                "  group by poDate " +
                " order by poDate desc limit  "+days

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        def json = [results: results]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_BOOK_CREATOR'])

    def getSalesReportForTopCourses(){
        String optionalQuery=""
        String sql = ""
        int days = 30
        if(params.noOfDays!=null) days = Integer.parseInt(params.noOfDays)
        if(params.publisherId!=null) {
            optionalQuery = " and bm.publisher_id="+params.publisherId+" "
        }
        else if(params.bookId!=null){
            optionalQuery +=" and bm.id="+params.bookId
        }

        sql = "select bm.title,count(*) poCount,sum(amount) poAmount,COALESCE(bm.isbn,'') isbn from " +
                " purchase_order  po, books_mst bm where  DATE(po.date_created) > date_add(CURDATE() , INTERVAL -"+days+" day) and po.item_code=bm.id  " +optionalQuery+
                " group by bm.title,bm.isbn" +
                " order by poAmount desc limit 10"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        def json = [results: results]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def reports(){
        List sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
            [ hideSearch: true,sitesList:sitesList,commonTemplate:"true"]
        }

    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def sendCartActiveDetails() {
        String sql = " SELECT DISTINCT" +
                "    (scad.username),u.name,bm.id as bookId,p.name as publisherName,bm.price,bm.title " +
                "  FROM " +
                "  reports.shopping_cart_active_dtl scad," +
                "    reports.user u,reports.books_mst bm,reports.publishers p " +
                "  WHERE   scad.username = u.username and scad.book_id=bm.id and bm.publisher_id=p.id " +
                " AND date(DATE_ADD(scad.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y') "+
                " AND date(DATE_ADD(scad.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') AND  scad.site_id="+params.siteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List purchasedBooks= results.collect { comp ->
            return [username: comp.username!=null?comp.username.split('_')[1]:"",bookId: comp.bookId,bookTitle: comp.title, publisherName: comp.publisherName,price: comp.price,viewCount:"",uName:comp.name]
        }
        if("true".equals(params.download)) {
            List headers    = ["Username", "Name","Book Id", "Book Title", "Publisher Name","Price","View Count"]
            List withProperties   = ["username","uName", "bookId", "bookTitle", "publisherName","price","viewCount"]
            def fileName = "CartActive"  + (new Random()).nextInt(9999999) + ".xlsx";
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(purchasedBooks, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: purchasedBooks ? "OK" : "Not present",data: purchasedBooks, recordsTotal: purchasedBooks.size(), draw: params.draw, recordsFiltered: purchasedBooks.size()]
            render json as JSON
        }

    }


    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def purchasedBookDetails(){
        String sql = " SELECT DISTINCT" +
                "    (po.username),u.name,bm.id as bookId,p.name as publisherName,po.amount,bm.title " +
                " FROM reports.purchase_order po," +
                "   reports.user u,reports.books_mst bm,reports.publishers p " +
                " WHERE  po.username = u.username and po.item_code=bm.id and bm.publisher_id=p.id" +
                " AND date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y')"+
                " AND date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') AND  po.site_id="+params.siteId
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
       List purchasedBooks= results.collect { comp ->
            return [username: comp.username!=null?comp.username.split('_')[1]:"",bookId: comp.bookId,bookTitle: comp.title, publisherName: comp.publisherName,price: comp.amount,viewCount:"",uName:comp.name]
        }
        if("true".equals(params.download)) {
            List headers    = ["Username", "Name","Book Id", "Book Title", "Publisher Name","Price","View Count"]
            List withProperties   = ["username","uName", "bookId", "bookTitle", "publisherName","price","viewCount"]
            def fileName = "purchasedBook"  + (new Random()).nextInt(9999999) + ".xlsx";
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(purchasedBooks, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: purchasedBooks ? "OK" : "Not present", data: purchasedBooks, recordsTotal: purchasedBooks.size(), draw: params.draw, recordsFiltered: purchasedBooks.size()]
            render json as JSON
        }
    }


    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def purchasedLibraryBookDetails(){
        String sql ="SELECT " +
                "    count(bvd.username) as count,bvd.book_id,bvd.username,bm.title,p.name as publisherName,u.name as name " +
                " FROM " +
                "    reports.books_view_dtl bvd,reports.books_mst bm,reports.publishers p,reports.user u " +
                "WHERE bvd.book_id=bm.id and bvd.username=u.username and bm.publisher_id=p.id and bvd.username is not null and bvd.view_type='library' and (DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE))  >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y')"+
                "        AND DATE(DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y')  bvd.site_id="+params.siteId+" group by bvd.username,bvd.book_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List purchasedBooks= results.collect { comp ->
            return [username: comp.username!=null?comp.username.split('_')[1]:"",bookId: comp.bookId,bookTitle: comp.title, publisherName: comp.publisherName,price: "",viewCount:comp.count,uName:comp.name]
        }
        if("true".equals(params.download)) {
            List headers    = ["Username", "Name","Book Id", "Book Title", "Publisher Name","Price","View Count"]
            List withProperties   = ["username","uName", "bookId", "bookTitle", "publisherName","price","viewCount"]
            def fileName = "purchasedLibraryBook"  + (new Random()).nextInt(9999999) + ".xlsx";
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(purchasedBooks, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: purchasedBooks ? "OK" : "Not present", data: purchasedBooks, recordsTotal: purchasedBooks.size(), draw: params.draw, recordsFiltered: purchasedBooks.size()]
            render json as JSON
        }
    }



    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def storeBooksUsersView(){
        String sql ="SELECT " +
                "    count(bvd.username) as count,bvd.book_id as bookId,bvd.username,bm.title,p.name as publisherName,u.name as name " +
                " FROM " +
                "    reports.books_view_dtl bvd,reports.books_mst bm,reports.publishers p,reports.user u " +
                "WHERE bvd.book_id=bm.id and bvd.username=u.username and bm.publisher_id=p.id and bvd.username is not null and bvd.view_type='store' and (DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE))  >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y')"+
                "        AND DATE(DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') AND  bvd.site_id="+params.siteId+" group by bvd.username,bvd.book_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List purchasedBooks= results.collect { comp ->
            return [username: comp.username!=null?comp.username.split('_')[1]:"",bookId: comp.bookId,bookTitle: comp.title, publisherName: comp.publisherName,price: "",viewCount:comp.count,uName:comp.name]
        }
        if("true".equals(params.download)) {
            List headers    = ["Username", "Name","Book Id", "Book Title", "Publisher Name","Price","View Count"]
            List withProperties   = ["username","uName", "bookId", "bookTitle", "publisherName","price","viewCount"]
            def fileName = " storeBooksUsers"  + (new Random()).nextInt(9999999) + ".xlsx";
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(purchasedBooks, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: purchasedBooks ? "OK" : "Not present", data: purchasedBooks, recordsTotal: purchasedBooks.size(), draw: params.draw, recordsFiltered: purchasedBooks.size()]
            render json as JSON
        }


    }

    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def storeBooksView(){
        String sql ="SELECT " +
                "    count(bvd.book_id) as count ,bvd.book_id as bookId,bm.title,p.name as publisherName" +
                " FROM" +
                "    reports.books_view_dtl bvd,reports.books_mst bm,reports.publishers p" +
                "  WHERE bvd.book_id=bm.id  and bm.publisher_id=p.id  and view_type='store' and (DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y')"+
                "        AND DATE(DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') and  bvd.site_id="+params.siteId+" group by bvd.book_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List purchasedBooks= results.collect { comp ->
            return [username: "",bookId: comp.bookId,bookTitle: comp.title, publisherName: comp.publisherName,price: "",viewCount:comp.count,uName:""]
        }
        if("true".equals(params.download)) {
            List headers    = ["Username", "Name","Book Id", "Book Title", "Publisher Name","Price","View Count"]
            List withProperties   = ["username","uName", "bookId", "bookTitle", "publisherName","price","viewCount"]
            def fileName = "storeBooks"  + (new Random()).nextInt(9999999) + ".xlsx";
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(purchasedBooks, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: purchasedBooks ? "OK" : "Not present", data: purchasedBooks, recordsTotal: purchasedBooks.size(), draw: params.draw, recordsFiltered: purchasedBooks.size()]
            render json as JSON
        }

    }



    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def instituteLibraryBookDetails(){
        String sql ="SELECT " +
                "    count(bvd.username)as count,bvd.book_id as bookId,bvd.username,bm.title,p.name as publisherName,u.name as name " +
                " FROM " +
                "    reports.books_view_dtl bvd,reports.books_mst bm,reports.publishers p,reports.user u " +
                "WHERE bvd.book_id=bm.id and bvd.username=u.username and bm.publisher_id=p.id and bvd.username is not null and bvd.view_type='library' and (DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE))  >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y')"+
                "        AND DATE(DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') AND  bvd.site_id="+params.siteId+" group by bvd.username,bvd.book_id"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List purchasedBooks= results.collect { comp ->
            return [username: comp.username!=null?comp.username.split('_')[1]:"",bookId: comp.bookId,bookTitle: comp.title, publisherName: comp.publisherName,price: "",viewCount:comp.count,uName:comp.name]
        }
        if("true".equals(params.download)) {
            List headers    = ["Username", "Name","Book Id", "Book Title", "Publisher Name","Price","View Count"]
            List withProperties   = ["username","uName", "bookId", "bookTitle", "publisherName","price","viewCount"]
            def fileName = "instituteLibraryBook"  + (new Random()).nextInt(9999999) + ".xlsx";
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(purchasedBooks, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: purchasedBooks ? "OK" : "Not present", data: purchasedBooks, recordsTotal: purchasedBooks.size(), draw: params.draw, recordsFiltered: purchasedBooks.size()]
            render json as JSON
        }
    }

    @Transactional @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def instituteBooksview(){
        String sql ="SELECT " +
                "    bvd.book_id," +
                "    bm.title," +
                "    COUNT(bvd.id) book_count," +
                "    bvd.username," +
                "    COALESCE(u.name, '') uName," +
                "    COALESCE(u.email, '')," +
                "    COALESCE(u.mobile, ''),COALESCE(p.name, '') publisherName" +
                " FROM" +
                "    reports.books_view_dtl bvd" +
                "    INNER JOIN reports.books_mst bm ON bm.id=bvd.book_id" +
                " LEFT JOIN  reports.publishers p on p.id=bm.publisher_id"+
                " INNER JOIN reports.institute_mst im  on im.id=bvd.institute_id" +
                "    LEFT JOIN  reports.user u on u.username=bvd.username " +
                " WHERE" +
                "    bvd.site_id = "+params.siteId+
                "        AND bvd.institute_id IN ('" + params.instituteId + "')" +
                "         AND DATE(DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "', '%d-%m-%Y')" +
                "        AND DATE(DATE_ADD(bvd.date_created," +
                "            INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "', '%d-%m-%Y')" +
                "GROUP BY bvd.institute_id , bvd.book_id , bvd.username , u.name , u.email , u.mobile "
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        List purchasedBooks= results.collect { comp ->
            return [username: comp.username!=null?comp.username.split('_')[1]:"",bookId: comp.book_id,bookTitle: comp.title, publisherName: comp.publisherName,price: "",viewCount:comp.book_count,uName:comp.uName]
        }
        if("true".equals(params.download)) {
            List headers    = ["Username", "Name","Book Id", "Book Title", "Publisher Name","Price","View Count"]
            List withProperties   = ["username","uName", "bookId", "bookTitle", "publisherName","price","viewCount"]
            def fileName = "instituteBooks"  + (new Random()).nextInt(9999999) + ".xlsx";
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(purchasedBooks, withProperties)
                save(response.outputStream)
            }
        }else {
            def json = [status: purchasedBooks ? "OK" : "Not present", data: purchasedBooks, recordsTotal: purchasedBooks.size(), draw: params.draw, recordsFiltered: purchasedBooks.size()]
            render json as JSON
        }
    }

    @Transactional
    def institutesList(){
        List institutes = InstituteMst.findAllBySiteId(new Long(params.siteId),[sort:"name" ])
        def json =[institutes:institutes]
        render json as JSON

    }

    @Secured(['ROLE_FINANCE','ROLE_WS_CONTENT_ADMIN','ROLE_AFFILIATION_SALES','ROLE_LIBRARY_ADMIN']) @Transactional
    def pubSales(){
        List publishers
        String sql =""
        String sqlCount = ""
        List sales = null

        Integer siteId = utilService.getSiteId(request,session)
        boolean isAffiliateSales = false
        boolean institutionSales = false
        boolean wsSuperuser = false
        def instituteId

        boolean printBookReport = false
        //        String bookTypeSQL = " (po.book_type IS NULL OR po.book_type <> 'printbook') "
        // going back on separating ebooks and print sales report
        String bookTypeSQL = " (po.book_type IS NULL OR po.book_type IS NOT NULL) "
        if("printbooks".equals(params.reportType)){
            printBookReport = true
            bookTypeSQL = " po.book_type in ('printbook','combo') "
        }


        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if(user.affliationCd!=null)
            isAffiliateSales=true
        if(user.authorities.any {
            it.authority == "ROLE_LIBRARY_ADMIN"
        }) {
            institutionSales = true
        }

        if(user.authorities.any {
            it.authority == "ROLE_ALL_SALES"
        }) {
            wsSuperuser = true
        }


        if(params.salesSiteId!=null){

            if(!"".equals(params.salesSiteId)) siteId = Integer.parseInt(params.salesSiteId)
        }
        List sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
        HashMap sites = new HashMap()
        sitesList.each {site->
            sites.put(""+site.id,site.clientName)
        }

        if(session.getAttribute("userdetails")==null){
            session["userdetails"]= User.findByUsername(springSecurityService.currentUser.username)
        }

        if("submit".equals(params.mode)) {
            if (params.poStartDate != null || params.poEndDate != null) {

                sqlCount = "select count(po.id)" +
                        " from wsshop.books_mst bm, wsshop.purchase_order po, wsshop.publishers ph where "+bookTypeSQL+" and "

                if (params.poStartDate != null && params.poStartDate != "" && ''.equals(params.paymentId))
                    sqlCount += " date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y') and"

                if (params.poEndDate != null && params.poEndDate != "" && ''.equals(params.paymentId))
                    sqlCount += " date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') and"
//if email id or mobile is given.. then add this additional thingy
                if (params.mobile != null &&  !''.equals(params.mobile)){
                    User user1 = User.findByMobileAndSiteId(params.mobile,siteId)
                    if(user1==null) {
                        //making the sql invalid so that they don't return any values as the user doesn't exist
                        sqlCount += "  po.username='-1invalidjunk' and "
                    }else{
                        sqlCount += "  po.username='" + user1.username + "' and "
                    }
                }
                //if email id or mobile is given.. then add this additional thingy
                if (params.emailId != null  && !''.equals(params.emailId)){
                    User user1 = User.findByEmailAndSiteId(params.emailId,siteId)
                    if(user1==null) {
                        //making the sql invalid so that they don't return any values as the user doesn't exist
                        sqlCount += "  po.username='-1invalidjunk' and  "
                    }else{
                        sqlCount += "  po.username='" + user1.username + "' and "
                    }
                }
                if (params.paymentId != null && params.paymentId != "") {
                    if (params.select == "paymentId") {
                        sqlCount += " po.payment_id='" + params.paymentId + "' and "
                    } else if (params.select == "bookId") {
                        sqlCount += " bm.id='" + params.paymentId + "' and "
                    } else if (params.select == "orderno") {
                        sqlCount += " (po.id = '" + params.paymentId + "' OR po.sequence_po= SUBSTRING_INDEX('" + params.paymentId + "', 'T', -1)) and"
                    }
                }
                sqlCount += " bm.id=po.item_code  and bm.publisher_id=ph.id and " ;
                if(!wsSuperuser) sqlCount  +=" po.direct_sales is null and "
                if(isAffiliateSales){
                    sqlCount += " po.affiliation_cd='"+user.affliationCd+"' "
                }
                else if(institutionSales){
                    List userInstitutes
                    if(session["userInstitutes"]!=null) userInstitutes = session["userInstitutes"]
                    else    {
                        userInstitutes=userManagementService.getInstitutesForUser(1,utilService.getIPAddressOfClient(request))
                        if(userInstitutes.size()>1){
                            for(int i=0;i<userInstitutes.size();i++){
                                if(!("Default".equals(""+userInstitutes[i].batchName))) userInstitutes.remove(i--)
                            }
                        }
                        session["userInstitutes"] = userInstitutes
                    }
                    instituteId = userInstitutes[0].id
                    sqlCount += " po.institute_id="+userInstitutes[0].id
                }else {
                    if (params.salesSiteId != null && !"".equals(params.salesSiteId) && session["userdetails"].publisherId == null)
                        sqlCount += " po.site_id in (" + siteId + ")"
                    else sqlCount += " bm.site_id in (" + dataProviderService.getSiteIdList(siteId) + ")"


                    if (session["userdetails"].publisherId != null) {
                        sqlCount += " and  bm.publisher_id=" + session["userdetails"].publisherId
                    } else if (params.publisherId != null && params.publisherId != "") {
                        sqlCount += " and bm.publisher_id=" + params.publisherId
                    }
                }

                if(printBookReport&&params.status!=null&&!"".equals(params.status)) sql += " and po.status='"+params.status+"'"

                def dataSourceCount = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1count = new Sql(dataSourceCount)
                def resultsCount = sql1count.rows(sqlCount)
                def count = resultsCount.get(0).values()


                sql = getSalesReportsSql(params,"display")

                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql)
                def date
                String itemTitle
                def itemPrice
                String status
                sales = results.collect { sale ->
                    status =  "Delivered"
                    if("printbook".equals(sale.book_type)||"combo".equals(sale.book_type)) {
                        if ("Active".equals(sale.status)) status = "Order received"
                        else status = sale.status
                    }
                    date = sale[5]
                    if (date != "") {
                        date = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(date)
                    }
                    user = dataProviderService.getUserMst(sale[2])
                    itemTitle = sale[3]
                    itemPrice = sale[7]
                    if(sale.subscription_id!=null){
                        SubscriptionMst subscriptionMst = wsshopService.getSubscriptionMst(""+sale.subscription_id)
                        itemTitle = subscriptionMst.title+" (Subscription)"
                        itemPrice = sale.amount
                    }
                    if (user != null)
                        return [poNo        : sale[0], paymentId: sale[1], state: user.state, district: user.district != null ? user.district : "", name: user.name, email: user.email, mobile: user.mobile, title: itemTitle,
                                price       : sale[4], salesDate: date, publisher: sale[6], bookPrice: itemPrice, discountAmount: sale[9] ? sale[9] : "",
                                discountType: sale[8] ? DiscountMst.findById(new Long(sale[8])).type : "", siteId: sale.site_id, siteName: sites.get("" + sale.site_id), isbn: sale[11],
                                directSales:sale[12],status:sale.status,bookType: sale.book_type,status:status,cartMstId:sale.cart_mst_id,deliveryCosts:sale.delivery_costs]

                }
                while (sales.remove(null)) {
                }
                def json = [data: sales, recordsTotal: count, draw: params.draw, recordsFiltered: count]
                render json as JSON
            }
        }else {
            if (session["userdetails"].publisherId != null) {
                publishers = Publishers.findAllById(session["userdetails"].publisherId)
            } else {
                if (redisService.("publishers_" + siteId) == null)
                    dataProviderService.getPublishers(siteId)

                publishers = new JsonSlurper().parseText(redisService.("publishers_" + siteId))
            }

            [sales    : sales, hideSearch: true, displayName: "<a href='pubDesk'>SALES</a>", poStartDate: params.poStartDate,
             poEndDate: params.poEndDate, publishers: publishers, publisherId: params.publisherId, select: params.select,
             paymentId: params.paymentId,sitesList:sitesList,isAffiliateSales:isAffiliateSales,commonTemplate:"true",institutionSales:institutionSales,
             wsSuperUser:wsSuperuser,mobile:params.mobile,emailId:params.mobileId]
        }

    }

    String getSalesReportsSql(params,String mode) {
        List publishers
        String sql = ""
        String sqlCount = ""
        List sales = null

        Integer siteId = utilService.getSiteId(request, session)
        boolean isAffiliateSales = false
        boolean institutionSales = false
        boolean wsSuperuser = false
        def instituteId

        boolean printBookReport = false

//        String bookTypeSQL = " (po.book_type IS NULL OR po.book_type <> 'printbook') "
        // going back on separating ebooks and print sales report
        String bookTypeSQL = " (po.book_type IS NULL OR po.book_type IS NOT NULL) and scom.id=po.cart_mst_id "
        if ("printbooks".equals(params.reportType)) {
            printBookReport = true
            bookTypeSQL = " po.book_type in ('printbook','combo') and scom.id=po.cart_mst_id "
        }
        println("bookTypeSQL="+bookTypeSQL)

        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if (user.affliationCd != null)
            isAffiliateSales = true
        if (user.authorities.any {
            it.authority == "ROLE_LIBRARY_ADMIN"
        }) {
            institutionSales = true
        }

        if (user.authorities.any {
            it.authority == "ROLE_ALL_SALES"
        }) {
            wsSuperuser = true
        }


        if (params.salesSiteId != null) {

            if (!"".equals(params.salesSiteId)) siteId = Integer.parseInt(params.salesSiteId)
        }
        List sitesList = SiteMst.findAll([sort: "clientName", order: "asc"])
        HashMap sites = new HashMap()
        sitesList.each { site ->
            sites.put("" + site.id, site.clientName)
        }

        if (session.getAttribute("userdetails") == null) {
            session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)
        }


            if (params.poStartDate != null || params.poEndDate != null) {


                sql = "select if(po.sequence_po is not null,CONCAT('UT',po.sequence_po),po.id) id, po.payment_id, po.username, bm.title, po.amount," +
                        " DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE), ph.name publisher,po.book_price,po.discount_id,po.discount_amount," +
                        "po.site_id,COALESCE(bm.isbn,'') isbn,po.direct_sales,po.status,po.book_type,po.status,po.gst_percentage," +
                        "po.subscription_id,po.book_type,po.cart_mst_id,COALESCE(scom.deliver_costs,0.0) delivery_costs " +
                        " from wsshop.books_mst bm, wsshop.purchase_order po, wsshop.publishers ph, shopping_cart_orders_mst scom where " + bookTypeSQL

                if (params.poStartDate != null && params.poStartDate != "" && ''.equals(params.paymentId))
                    sql += " and  date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.poStartDate + "','%d-%m-%Y') "

                if (params.poEndDate != null && params.poEndDate != "" && ''.equals(params.paymentId))
                    sql += " and date(DATE_ADD(po.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.poEndDate + "','%d-%m-%Y') "
                //if email id or mobile is given.. then add this additional thingy
                if (params.mobile != null &&  !''.equals(params.mobile)){
                    User user1 = User.findByMobileAndSiteId(params.mobile,siteId)
                    if(user1==null) {
                        //making the sql invalid so that they don't return any values as the user doesn't exist
                        sql += " and po.username='-1invalidjunk'  "
                    }else{
                        sql += " and po.username='" + user1.username + "'  "
                    }
                }
                //if email id or mobile is given.. then add this additional thingy
                if (params.emailId != null  && !''.equals(params.emailId)){
                    User user1 = User.findByEmailAndSiteId(params.emailId,siteId)
                    if(user1==null) {
                        //making the sql invalid so that they don't return any values as the user doesn't exist
                        sql += " and po.username='-1invalidjunk'  "
                    }else{
                        sql += " and po.username='" + user1.username + "'  "
                    }
                }

                if (params.paymentId != null && params.paymentId != "") {
                    if (params.select == "paymentId") {
                        sql += " and po.payment_id='" + params.paymentId + "' "
                    } else if (params.select == "bookId") {
                        sql += " and bm.id='" + params.paymentId + "'  "
                    } else if (params.select == "orderno") {
                        sql += " and  (po.id = '" + params.paymentId + "' OR po.sequence_po= SUBSTRING_INDEX('" + params.paymentId + "', 'T', -1)) "
                    }
                }

                sql += " and bm.id=po.item_code  and bm.publisher_id=ph.id  "
                if (!wsSuperuser) sql += " and po.direct_sales is null  "

                if (isAffiliateSales) {
                    sql += " and po.affiliation_cd='" + user.affliationCd + "' "
                } else if (institutionSales) {
                    sql += " and po.institute_id=" + instituteId
                } else {
                    if (params.salesSiteId != null && !"".equals(params.salesSiteId) && session["userdetails"].publisherId == null)
                        sql += " and po.site_id in (" + siteId + ")"
                    else if(siteId.intValue()!=1) sql += " and bm.site_id in (" + dataProviderService.getSiteIdList(siteId) + ")"

                    if (session["userdetails"].publisherId != null) {
                        sql += " and  bm.publisher_id=" + session["userdetails"].publisherId
                    } else if (params.publisherId != null && params.publisherId != "") {
                        sql += " and bm.publisher_id=" + params.publisherId
                    }
                }

                if(printBookReport&&params.status!=null&&!"".equals(params.status)) sql += " and po.status='"+params.status+"'"

                sql += " order by po.id desc " ;
               if("display".equals(mode))     sql +=    " limit " + params.start + " , " + params.length + ""

            }

        println(sql)

        return sql
    }

    @Secured(['ROLE_FINANCE','ROLE_AFFILIATION_SALES','ROLE_LIBRARY_ADMIN'])  @Transactional
    def downloadSalesData(){
        Integer siteId =utilService.getSiteId(request,session)
        boolean isAffiliateSales = false
        boolean institutionSales = false
        def instituteId
        boolean wsSuperuser = false
        boolean printBookReport = false

        if("printbooks".equals(params.reportType)){
            printBookReport = true
        }
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)

        if(user.affliationCd!=null)
            isAffiliateSales=true
        if(user.authorities.any {
            it.authority == "ROLE_LIBRARY_ADMIN"
        }) {
            institutionSales = true
        }

        if(user.authorities.any {
            it.authority == "ROLE_ALL_SALES"
        }) {
            wsSuperuser = true
        }
        if(params.salesSiteId!=null){

            if(!"".equals(params.salesSiteId)) siteId = Integer.parseInt(params.salesSiteId)
        }
        List sitesList = SiteMst.findAll([sort:"clientName", order:"asc"])
        HashMap sites = new HashMap()
        sitesList.each {site->
            sites.put(""+site.id,site.clientName)
        }

        String sql = getSalesReportsSql(params,"download")
              def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            def date
            Float igst = 0
            Float tamount=0
            String itemTitle
            def itemPrice
            String status = "Delivered"
            if(results!=null && results.size()>0) {
                List data = results.collect {sale ->
                    status = "Delivered"
                    date = sale[5]
                    if (sale.gst_percentage == 5) {
                        tamount =   (Float)((new Float(sale[4]))*(100/105)).trunc(2)
                    } else if (sale.gst_percentage == 18) {
                        tamount =   (Float)((new Float(sale[4]))*(100/105)).trunc(2)
                    }else {
                        tamount =   new Float(sale[4]).trunc(2)
                    }
                    if (sale.gst_percentage == 5) {
                        igst = (Float) ((5 * tamount) / 100).trunc(2)
                    }
                    else if (sale.gst_percentage == 18) {
                        igst = (Float) ((18 * tamount) / 100).trunc(2)
                    }else {
                        igst = 0;
                    }
                    if (date != "") {
                        date = (new SimpleDateFormat("yyyy-MM-dd HH:mm")).format(date)
                    }
                    user = dataProviderService.getUserMst(sale[2])
                    itemTitle = sale[3]
                    itemPrice = sale[7]?sale[7]:""
                    if(sale.subscription_id!=null){
                        SubscriptionMst subscriptionMst = wsshopService.getSubscriptionMst(""+sale.subscription_id)
                        itemTitle = subscriptionMst.title+" (Subscription)"
                        itemPrice = sale.amount
                    }
                    if(user!=null)
                    {
                        String deliveryAddress=""
                        if(printBookReport){
                            if("Active".equals(sale.status)) status="Order received"
                            else status = sale.status
                              ShippingAddressMst shippingAddressMst = ShippingAddressMst.findByCartMstId(sale.cart_mst_id)
                            if(shippingAddressMst!=null) {
                                deliveryAddress = shippingAddressMst.shipFirstName + "\n" +
                                        shippingAddressMst.shipAddressLine1 + "\n" +
                                        shippingAddressMst.shipAddressLine2 + "\n"
                                shippingAddressMst.shipCity +","+shippingAddressMst.shipState+ "\n"
                                "Pincode : "+shippingAddressMst.shipPincode + "\n"
                                "Mobile : "+shippingAddressMst.shipMobile;

                            }
                        }
                        return [poNo:sale[0],title:itemTitle,publisher:sale[6],price:sale.amount,salesDate:date,name:user.name, email:user.email,
                                mobile:user.mobile,paymentId:sale[1],bookPrice:itemPrice,discountAmount:sale[9]?sale[9]:"",
                                discountType:sale[8]? DiscountMst.findById(new Long(sale[8])).type : "",gstPercentage:sale.gst_percentage,gstValue:igst,state:user.state,
                                district:user.district!=null?user.district:"",siteId:sale.site_id, siteName: sites.get("" + sale.site_id),isbn: sale[12], directSales:sale[13],
                                deliveryAddress:deliveryAddress,bookType:sale.book_type,status:status,isbn:sale.isbn,deliveryCosts: sale.delivery_costs]
                    }
                }
                while (data.remove(null)) {
                }
                List headers
                List withProperties
                if(siteId!=21) {

                    if(wsSuperuser){
                        headers = ["Book name", "Book Type","Publisher", "Price", "Purchased date/time", "Book Price", "Discount Amount", "Discount Type", "Gst Percentage", " Gst Value",
                                   "State", "District", "Site", "ISBN","Direct Sales"]
                        withProperties = ["title", "bookType","publisher", "price", "salesDate", "bookPrice", "discountAmount", "discountType", "gstPercentage", "gstValue", "state", "district", "siteName", "isbn","directSales"]

                    }
                    else if(isAffiliateSales) {
                        headers = ["Book name", "Book Type", "Publisher", "Price", "Purchased date/time", "Book Price", "Discount Amount", "Discount Type", "Gst Percentage", " Gst Value",
                                   "State", "District", "Site", "ISBN"]
                        withProperties = ["title", "bookType", "publisher", "price", "salesDate", "bookPrice", "discountAmount", "discountType", "gstPercentage", "gstValue", "state", "district", "siteName", "isbn"]

                    }
                    else if(printBookReport) {
                      if(params.status!=null&&!"".equals(params.status)&&"Active".equals(params.status)) {
                          headers = ["Book name", "Book Type", "Status", "Publisher", "Price", "Delivery Costs","Purchased date/time", "Book Price", "Site", "ISBN", "Delivery Address","Delivery Type","Courier name","Tracking Link","Tracking Code"]
                          withProperties = ["title", "bookType", "status", "publisher", "price", "deliveryCosts","salesDate", "bookPrice", "siteName", "isbn", "deliveryAddress"]

                      }   else {
                          headers = ["Book name", "Book Type", "Status", "Publisher", "Price", "Delivery Costs", "Purchased date/time", "Book Price", "Site", "ISBN", "Delivery Address"]
                          withProperties = ["title", "bookType", "status", "publisher", "price", "deliveryCosts", "salesDate", "bookPrice", "siteName", "isbn", "deliveryAddress"]
                      }

                    }
                    else{
                        headers = ["Invoice/Order number","Book name", "Book Type", "Publisher","Price","Purchased date/time","Name", "Email", "Mobile number","Payment Id", "Book Price","Discount Amount","Discount Type","Gst Percentage"," Gst Value",
                                   "State", "District","Site","ISBN"]
                        withProperties = ["poNo", "title", "bookType","publisher","price","salesDate","name", "email", "mobile","paymentId","bookPrice","discountAmount","discountType","gstPercentage","gstValue", "state", "district","siteName","isbn"]

                    }
                }else{
                    headers = ["Invoice/Order number","Book name", "Book Type", "Publisher","Price","Purchased date/time","Name", "Email", "Mobile number","Payment Id", "Book Price","Discount Amount","Discount Type",
                               "State", "District","ISBN"]
                    withProperties = ["poNo", "title", "bookType","publisher","price","salesDate","name", "email", "mobile","paymentId","bookPrice","discountAmount","discountType", "state", "district","isbn"]
                }

                def fileName = "Sales_Data_"+(params.poStartDate!=""?params.poStartDate+"_":"FromAny_")+
                        (params.poEndDate!=""?params.poEndDate+"_":"ToAny_")+(new Random()).nextInt(9999999)+".xlsx"

                new WebXlsxExporter(grailsApplication.config.grails.basedir.path+'/upload/Sales_Data_Template.xlsx').with {
                    setResponseHeaders(response,fileName)
                    fillHeader(headers)
                    add(data, withProperties)
                    save(response.outputStream)
                }
            }

    }

    @Secured(['ROLE_FINANCE','ROLE_LIBRARY_ADMIN','ROLE_USER']) @Transactional
    def downloadInvoice(){
        if(params.poNo!=null && params.poNo!="") {
            DateFormat df
            Double deliveryCosts= new Double(0.0)
            String optionSql = " (po.id = '" + params.poNo + "' OR po.sequence_po= SUBSTRING_INDEX('" + params.poNo + "', 'T', -1))"
            if(params.cartMstId!=null&&!"".equals(params.cartMstId)&&!"null".equals(params.cartMstId)) {
                optionSql = " po.cart_mst_id="+params.cartMstId
                ShoppingCartOrdersMst shoppingCartOrdersMst = ShoppingCartOrdersMst.findById(new Integer(params.cartMstId))
                if(shoppingCartOrdersMst!=null&&shoppingCartOrdersMst.deliverCosts!=null) deliveryCosts = shoppingCartOrdersMst.deliverCosts
            }
            df = new SimpleDateFormat("dd.MM.yyyy / hh.mm aa")
            String sql = "select if(po.sequence_po is not null,CONCAT('UT',po.sequence_po),po.id) id, po.username,null dummy1,null dummy2, bm.title, po.amount, po.po_method, null dummy3, ph.name publisher,"+
                    " po.date_created,"+
                    " bm.cover_Image, po.payment_Id, bm.id book_id,po.sequence_po,po.gst_percentage,po.discount_amount,po.cart_mst_id,po.book_price"+
                    " from books_mst bm, purchase_order po,  publishers ph where"+
                    " bm.id=po.item_code and"+
                    optionSql +
                    " and ph.id=bm.publisher_id"

            def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            SiteMst sm = dataProviderService.getSiteMst(utilService.getSiteId(request,session))
            def siteName=sm.siteName
            SiteDtl siteDtl = dataProviderService.getSiteDtl(utilService.getSiteId(request,session))
            List purchaseOrder
            if(results!=null && results.size()>0) {
                def str1 = ""
                String   view = "/creation/wsinvoice"
                def fileName = "Invoice_"+params.poNo
                double totalAmount=0

               def sale = results[0]
                    if(sale.cart_mst_id!=null ){
                        totalAmount = 0;
                        def sqlcart="SELECT po.amount,po.gst_percentage,po.discount_amount,po.book_price,bm.title FROM purchase_order po,books_mst bm where po.item_code=bm.id and po.cart_mst_id="+sale.cart_mst_id
                        if(params.publisherId!=null && !"".equals(params.publisherId)){
                            sqlcart+=" and bm.publisher_id="+params.publisherId
                            VendorDeliveryCosts vendorDeliveryCosts = VendorDeliveryCosts.findByCartMstIdAndVendorId(new Integer(params.cartMstId),new Integer(params.publisherId))
                            if(vendorDeliveryCosts!=null) deliveryCosts = vendorDeliveryCosts.deliverCost
                            else deliveryCosts = new Double(0.0)
                        }
                        def dataSourcecart = grailsApplication.mainContext.getBean('dataSource_wsshop')
                        def sql1cart = new Sql(dataSourcecart)
                        def resultscart = sql1cart.rows(sqlcart)
                        purchaseOrder = resultscart.collect{  cart ->
                            totalAmount +=cart.amount
                            return [amount:cart.amount,gstPercentage:cart.gst_percentage,discountAmount:cart.discount_amount!=null?cart.discount_amount:0,bookPrice:cart.book_price,title:cart.title]
                        }

                    }
                    totalAmount +=deliveryCosts.doubleValue()
                    view = "/creation/wsinvoice"
                    if("jbclasses".equals(""+siteName)){
                        view = "/creation/jbclassInvoice"
                    }else if("arihant".equals(""+siteName)){
                        view = "/creation/arihantInvoice"
                    }else if("radianbooks".equals(""+siteName)){
                        view = "/creation/radianBooksInvoice"
                    }else if("books".equals(""+siteName)){
                        view = "/creation/wsinvoice"
                    }else if("privatelabel".equals(""+session['entryController'])){
                        view = "/creation/privateLabelInvoice"
                    }else {
                        view = "/creation/"+siteName+"Invoice"
                    }


                    try {
                        Integer rupee = new Integer((int)totalAmount)
                        str1 = "Rupees "+purchaseService.convert(rupee)
                        Integer paisa = (Integer) Math.round((totalAmount - rupee) * 100)
                        if(paisa>0) str1 += " AND " +purchaseService.convert(paisa.intValue()) + " Paise "
                    } catch (Exception e){
                        str1 = ""
                    }
                    String ipAddress = utilService.getIPAddressOfClient(request);
                    User user = dataProviderService.getUserMst(sale[1])
                     String state = user.state
                     if(params.cartMstId!=null) {
                         ShippingAddressMst shippingAddressMst = ShippingAddressMst.findByCartMstId(new Integer(params.cartMstId))
                         if(shippingAddressMst!=null){
                            state =  shippingAddressMst.shipAddressLine1+","+
                                    shippingAddressMst.shipAddressLine2+"\n"+
                                    shippingAddressMst.shipCity+","+
                                    shippingAddressMst.shipState+"\n"+
                                    shippingAddressMst.shipPincode
                         }
                     }

                if (siteDtl!=null){
                        String bookExpiry=null
                        if(siteDtl.siteId.intValue()==66){
                            if(redisService.("wileyCollectionBook")==null) wsshopService.getWileyCollectionMainBook()
                            def wileyCollectionBookId = redisService.("wileyCollectionBook")
                            BooksMst booksMst = dataProviderService.getBooksMst(new Integer(wileyCollectionBookId))
                            if (booksMst.bookExpiry != null && !"".equals(booksMst.bookExpiry)) {
                                SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
                                bookExpiry = sdf.format(booksMst.bookExpiry);
                            }
                        }
                        render(filename:"${fileName}.pdf",
                                view:view,
                                model:[name:user.name, account:user.email, poNo:sale[0], paymentId:sale[11], amount:sale[5],
                                       id:sale[12], title:sale[4], coverImage:sale[10], publisher:sale[11],price:sale[17],
                                       method:sale[6], createdAt:df.format(utilService.convertDate(sale[9],"UTC","IST")), amtStr: str1,state:state,mobile:user.mobile,ipAddress:ipAddress,
                                       gstPercentage:sale[14],discountAmount:sale[15]!=null?sale[15]:0,shoppingCartOrdersDtl:purchaseOrder,totalAmount:totalAmount,
                                       siteId:siteDtl.siteId,clientName: sm.clientName,entryController:session['entryController'], logo:siteDtl.logo, addressLine1:siteDtl.addressLine1,addressLine2: siteDtl.addressLine2,
                                       gstNumber:siteDtl.gstNumber,emailAddress:siteDtl.emailAddress,websiteLink: siteDtl.websiteLink, jurisdictionPlace: siteDtl.jurisdictionPlace,
                                       jurisdictionState: siteDtl.jurisdictionState, companyName: siteDtl.companyName,deliveryCosts:deliveryCosts,bookExpiry:bookExpiry])
                    }else{
                        render(filename:"${fileName}.pdf",
                                view:view,
                                model:[name:user.name, account:user.email, poNo:sale[0], paymentId:sale[11], amount:sale[5],
                                       id:sale[12], title:sale[4], coverImage:sale[10], publisher:sale[11],price:sale[17],
                                       method:sale[6], createdAt:df.format(utilService.convertDate(sale[9],"UTC","IST")), amtStr: str1,state:state,mobile:user.mobile,
                                       ipAddress:ipAddress,gstPercentage:sale[14],discountAmount:sale[15]!=null?sale[15]:0,shoppingCartOrdersDtl:purchaseOrder,totalAmount:totalAmount,
                                       siteId:sm.id,clientName: sm.clientName,entryController:session['entryController'], logo:null, addressLine1:null,addressLine2: null, gstNumber:null,emailAddress:null,websiteLink: null,
                                       jurisdictionPlace: null, jurisdictionState: null, companyName: null,deliveryCosts:deliveryCosts])
                    }

            }
        }
    }

    @Transactional
    def getExternalOrders() {
        Integer max = params.max ? params.max.toInteger() : 10
        max = Math.min(max, 100)
        Integer page = params.page ? params.page.toInteger() : 1
        Date fromDate = params.date("fromDate", 'yyyy-MM-dd')
        Date toDate = params.date("toDate", 'yyyy-MM-dd')

        PagedResultList orders = ExternalOrders.createCriteria().list(max: max, offset: (page-1) * max) {
            if(fromDate && toDate){
                between('dateCreated', fromDate, toDate)
            }

            if(params.siteId) {
                eq('siteId', Integer.parseInt(params.siteId))
            }

            if(params.status) {
                if(params.status.equals("Cancelled")) {
                    eq('status', 'cancelled')
                } else {
                    ne('status', 'cancelled')
                }
            }

            order('dateCreated', 'desc')
        }

        def json = [orders: orders, total: orders.totalCount]
        render  json as JSON
    }

    @Transactional @Secured(['ROLE_EXTERNAL_SALES_VIEWER'])
    def externalReportInput(){
        String externalReportSiteIds = KeyValueMst.findByKeyName("externalReportSiteIds").keyValue
        List<Integer> siteIds = externalReportSiteIds.split(",").collect { it.toInteger() }
        List sites = SiteMst.findAllByIdInList(siteIds)
        [sites:sites]
    }
 

    @Transactional  @Secured(['ROLE_EXTERNAL_SALES_VIEWER'])
    def getPurchases(Integer max, Integer page, String fromDate, String toDate, Integer siteId, String status,String paymentReference) {
        Closure criteria = createCriteria(fromDate, toDate, siteId, status,paymentReference)
        int total = OutsidePurchase.createCriteria().count(criteria)
        List<OutsidePurchase> purchases = OutsidePurchase.createCriteria().list(max: max ?: 10, offset: ((page ?: 1) - 1) * (max ?: 10), criteria)
        def json = [purchases: purchases, total: total]
        render json as JSON
    }

    @Transactional  @Secured(['ROLE_EXTERNAL_SALES_VIEWER'])
    def downloadPurchases(String fromDate, String toDate, Integer siteId, String status,String paymentReference) {
        Closure criteria = createCriteria(fromDate, toDate, siteId, status,paymentReference)
        List<OutsidePurchase> purchases = OutsidePurchase.createCriteria().list(criteria)
        Map params = [fromDate: fromDate, toDate: toDate]
        createAndDownloadExcel(purchases, params)
    }

    private Closure createCriteria(String fromDate, String toDate, Integer siteId, String status,String paymentReference) {
        return {
            if (fromDate) {
                try {

                    SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    inputDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata")); // Set input time zone to IST

                    SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    outputDateFormat.setTimeZone(TimeZone.getTimeZone("GMT")); // Set output time zone to GMT

                    Date parsedDate = inputDateFormat.parse(fromDate);

                    // Calculate the time difference between IST and GMT
                    long timeDifference = TimeZone.getTimeZone("Asia/Kolkata").getRawOffset() - TimeZone.getTimeZone("GMT").getRawOffset();

                    // Adjust the date by adding the time difference
                    Date adjustedDate = new Date(parsedDate.getTime() + timeDifference);

                    ge("dateCreated", adjustedDate);

                } catch (Exception e) {
                    // Handle exception, probably by logging it
                    println("the exception is "+e.toString())
                }
            }
            if (toDate) {
                println("Am i not even coming here to date")
                try {
                    SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    inputDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Kolkata")); // Set input time zone to IST

                    SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    outputDateFormat.setTimeZone(TimeZone.getTimeZone("GMT")); // Set output time zone to GMT

                    Date parsedToDate = inputDateFormat.parse(toDate);

                    // Calculate the time difference between IST and GMT
                    long timeDifference = TimeZone.getTimeZone("Asia/Kolkata").getRawOffset() - TimeZone.getTimeZone("GMT").getRawOffset();

                    // Adjust the date by adding the time difference
                    Date adjustedToDate = new Date(parsedToDate.getTime() + timeDifference + 24 * 60 * 60 * 1000 - 1);
                    le("dateCreated", adjustedToDate);


                } catch (Exception e) {
                    // Handle exception, probably by logging it
                    println("the exception is "+e.toString())
                }

            }
            if (siteId) {
                eq("siteId", siteId)
            }
            if (status) {
                if (status == 'Cancelled') {
                    eq("status", "cancelled")
                } else  if (status == 'Purchased') {
                    or {
                        ne("status", "cancelled")
                        isNull("status")
                    }
                }
            }
            if(paymentReference){
                eq("paymentReference",paymentReference)
            }
            order("dateCreated", "desc")
        }
    }



    private void createAndDownloadExcel(List<OutsidePurchase> purchases, Map params) {
        Workbook workbook = new XSSFWorkbook()
        Sheet sheet = workbook.createSheet("Purchases")
        createHeaderRow(sheet)
        createDataRows(purchases, sheet)
        downloadWorkbook(workbook, params)
    }

    private void createHeaderRow(Sheet sheet) {
        Row headerRow = sheet.createRow(0)
        headerRow.createCell(0).setCellValue("Site Id")
        headerRow.createCell(1).setCellValue("Name")
        headerRow.createCell(2).setCellValue("Mobile")
        headerRow.createCell(3).setCellValue("Email")
        headerRow.createCell(4).setCellValue("Price")
        headerRow.createCell(5).setCellValue("Payment Reference")
        headerRow.createCell(6).setCellValue("Date Created")
        headerRow.createCell(7).setCellValue("Isbns")
        headerRow.createCell(8).setCellValue("Status")
        headerRow.createCell(9).setCellValue("Missed Isbns")
        }

    private void createDataRows(List<OutsidePurchase> purchases, Sheet sheet) {
        int rowNum = 1
        SimpleDateFormat format = new SimpleDateFormat('dd/MM/yyyy HH:mm')
        for (OutsidePurchase purchase : purchases) {
            Row row = sheet.createRow(rowNum++)
            row.createCell(0).setCellValue(purchase.siteId)
            row.createCell(1).setCellValue(purchase.name)
            row.createCell(2).setCellValue(purchase.mobile)
            row.createCell(3).setCellValue(purchase.email)
            row.createCell(4).setCellValue(purchase.price)
            row.createCell(5).setCellValue(purchase.paymentReference)
            row.createCell(6).setCellValue(format.format(purchase.dateCreated))
            row.createCell(7).setCellValue(purchase.isbns)
            row.createCell(8).setCellValue(purchase.status)
            row.createCell(9).setCellValue(purchase.missedIsbns)
        }
    }

    private void downloadWorkbook(Workbook workbook, Map params) {
        String fileName = "outside_purchases_" + params.fromDate + "_" + params.toDate + ".xlsx"
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        response.setHeader("Content-disposition", "attachment; filename=${fileName}")
        workbook.write(response.outputStream)
        workbook.close()
    }

    @Transactional
    def getDirectLink(){
        ExOrderUser exOrderUser = ExOrderUser.findByOrderId(new Integer(params.purchaseId))
        OutsidePurchase outsidePurchase = OutsidePurchase.findById(new Integer(params.purchaseId))
        SiteMst siteMst = dataProviderService.getSiteMst(outsidePurchase.siteId)
        String directUrl="https://"+  request.getServerName()+"/exOrder/"
        if(siteMst.siteDomainName!=null){
            if(siteMst.siteDomainName.indexOf("http")==0) directUrl = siteMst.siteDomainName+"/exOrder/"
            else directUrl="https://"+ siteMst.siteDomainName+"/exOrder/"
        }

        String urlKey = Base64.getEncoder().encodeToString((""+(exOrderUser!=null?""+exOrderUser.orderKey:""+outsidePurchase.id)).getBytes())
        directUrl+=urlKey
        def json = [directUrl:directUrl]
        render json as JSON
    }


    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])
    def getBulkUsersAddedReportInput(){
        String sql = "select distinct(school) from user where site_id="+session["siteId"]+" and student='bulkUpload' and school is not null order by school"
        def dataSource
        Sql sql1
        def schools

        dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        sql1 = new Sql(dataSource)
        schools = sql1.rows(sql)
        [schools:schools]

    }
    private void createAndDownloadExcelUser(List<OutsidePurchase> purchases, Map params) {
        Workbook workbook = new XSSFWorkbook()
        Sheet sheet = workbook.createSheet("Purchases")
        createHeaderRowUsers(sheet)
        createDataRowsUsers(purchases, sheet)
        downloadWorkbookUser(workbook, params)
    }

    private void createHeaderRowUsers(Sheet sheet) {
        Row headerRow = sheet.createRow(0)
        headerRow.createCell(0).setCellValue("Name")
        headerRow.createCell(1).setCellValue("Mobile")
        headerRow.createCell(2).setCellValue("Email")
        headerRow.createCell(3).setCellValue("School")
        headerRow.createCell(4).setCellValue("Product")
        headerRow.createCell(5).setCellValue("PaymentID")
        headerRow.createCell(6).setCellValue("Date Created")
    }

    private void createDataRowsUsers( users, Sheet sheet) {
        int rowNum = 1
        SimpleDateFormat format = new SimpleDateFormat('dd/MM/yyyy HH:mm')
        for (def user : users) {
            Row row = sheet.createRow(rowNum++)
            row.createCell(0).setCellValue(user.name)
            row.createCell(1).setCellValue(user.mobile)
            row.createCell(2).setCellValue(user.email)
            row.createCell(3).setCellValue(user.school)
            row.createCell(4).setCellValue(user.isbn)
            row.createCell(5).setCellValue(user.client_po)
            row.createCell(6).setCellValue(format.format(user.date_created))
        }
    }

    private void downloadWorkbookUser(Workbook workbook, Map params) {
        String fileName = "users_" + params.fromDate + "_" + params.toDate + ".xlsx"
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        response.setHeader("Content-disposition", "attachment; filename=${fileName}")
        workbook.write(response.outputStream)
        workbook.close()
    }
    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])
    def getBulkUsersAddedReport() {
        Integer page = params.page ? params.page.toInteger() : 1
        Integer length = params.length ? params.length.toInteger() : 10
        int start = (page.intValue()-1)*length.intValue()
        int totalRows = params.totalRows ? params.totalRows.toInteger() : -1

        String sql = "select u.name,u.school,u.mobile,u.email,bm.isbn,bp.date_created,bp.client_po from books_mst bm,books_permission bp,user u where\n" +
                " u.student='bulkUpload' and bp.username=u.username and bm.id=bp.book_id and bp.added_by='System' and u.site_id="+session["siteId"]
        if (params.fromDate != null && params.fromDate != "")
            sql += " and  date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) >= STR_TO_DATE('" + params.fromDate + "','%Y-%m-%d') "

        if (params.toDate != null && params.toDate != "")
            sql += " and date(DATE_ADD(bp.date_created, INTERVAL '5:30' HOUR_MINUTE)) <= STR_TO_DATE('" + params.toDate + "','%Y-%m-%d') "
        if (params.school != null && params.school != "")
            sql += " and u.school='"+params.school+"'"

        def dataSource
        Sql sql1
        def results
        if(totalRows==-1&&"display".equals(params.mode)){
            dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
            sql1 = new Sql(dataSource)
            results = sql1.rows(sql)
            totalRows = results.size().intValue()
        }
        if ("display".equals(params.mode)) sql += " limit " + start + " , " + length + ""
         dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
         sql1 = new Sql(dataSource)

         results = sql1.rows(sql)
        if ("display".equals(params.mode)) {
            def json = [total: totalRows, results: results]
            render json as JSON
        }
        else{
            //download
            Map params = [fromDate: params.fromDate, toDate: params.toDate]
            createAndDownloadExcelUser(results, params)
        }
    }

    @Transactional @Secured(['ROLE_WS_CONTENT_ADMIN','ROLE_BOOK_CREATOR'])
    def scratchCardReport(){
        if("submit".equals(params.mode)){
            def results;
            def json;
            results= reportsService.getScratchCardReport(params?.bookId,params?.inputFromDate,params?.inputToDate,new Integer(""+session['siteId']))
            if(results!=null){
                json= ["status": results ? "OK" : "norecords", "codeList": results]
            }
            else{
                json=["status":"failed"]
            }
            render json as JSON
        }
        else if("download".equals(params.mode)){
            def inputFromDate=params?.inputFromDate
            def inputToDate=params?.inputToDate
            List data = reportsService.getScratchCardReport(params?.bookId, params?.inputFromDate, params?.inputToDate, new Integer(""+session['siteId']))
            List headers = ["Book Id", "Title","Campaign", "Number of Codes", "Created By", "Date Created"]
            List withProperties = ["bookId", "title","campaignName", "noOfCodes", "name", "dateCreated"]
            def fileName = "ScratchCardReport_" + inputFromDate + "_" + inputToDate + "_" + (new Random()).nextInt(9999999) + ".xlsx"
            new WebXlsxExporter().with {
                setResponseHeaders(response, fileName)
                fillHeader(headers)
                add(data, withProperties)
                save(response.outputStream)
            }
        }
    }


}
