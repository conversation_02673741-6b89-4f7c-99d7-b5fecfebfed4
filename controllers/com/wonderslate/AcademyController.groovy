package com.wonderslate

import grails.transaction.Transactional

import javax.servlet.http.Cookie

class AcademyController {

    @Transactional
    def index() {
        session['siteId'] = new Integer(1);
        session.setAttribute("entryController", "books")
        session.setAttribute("siteName", "Wonderslate")
        session.setAttribute("loginType", "email,mobile")
        session.setAttribute("siteNameUrl", "books")
        Cookie cookie = new <PERSON><PERSON>("wlSiteName", "")
        cookie.path = "/"
        cookie.maxAge = 0
        response.addCookie(cookie)
        cookie = new <PERSON>ie("siteName", "books")
        cookie.path = "/"
        response.addCookie(cookie)
        ['title':'Wonderslate - Academy',commonTemplate:"true"]
    }

}
