package com.wonderslate.games

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.PrepjoyService
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.TestsService
import com.wonderslate.data.UtilService
import com.wonderslate.prepjoy.DailyExamGroup
import com.wonderslate.prepjoy.DailyTestsMst
import com.wonderslate.prepjoy.DailyTestsSiteDtl
import grails.converters.JSON
import grails.transaction.Transactional

class MocktestsController {
    def redisService
    PrepjoyService prepjoyService
    UtilService utilService
    TestsService testsService
    DataProviderService dataProviderService

    @Transactional
    def index() {
        Integer siteId = utilService.getSiteId(request,session)
        DailyTestsSiteDtl dailyTestsSiteDtl = DailyTestsSiteDtl.findBySiteId(siteId)
        if(dailyTestsSiteDtl==null) {
            siteId = new Integer(1)
        }
        if (redisService.("dailyTestTypesForSite_" + siteId) == null) prepjoyService.getDailyTestTypesForSite(""+siteId)
          ['dailyTestTypes': redisService.("dailyTestTypesForSite_" + siteId), title:"Best mock tests",commonTemplate:"true"]
    }

    def showImage(String fileName) {
        if(fileName!=null&&!"null".equals(fileName)&&fileName.length()>0) {
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
            def  file = new File("upload/examGroup/" + fileName)
            if (file.exists()) {
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }   else render "";
        } else render "";
    }

    def listExams(){
        Integer siteId = utilService.getSiteId(request,session)
        if (redisService.("dailyTestTypesForSite_" + siteId) == null) prepjoyService.getDailyTestTypesForSite(""+siteId)

        String examGroup =  (""+params.examGroup).toLowerCase()
        if (redisService.("dailyTestsForExamGroup_" + examGroup) == null) prepjoyService.getTestsOfExamGroup(examGroup.replace('-',' '))
        [testsList: redisService.("dailyTestsForExamGroup_" + examGroup), title:"Best mock tests for "+examGroup.replace('-',' '),
         seoDesc:"Best mock tests for "+examGroup.replace('-',' '),'dailyTestTypes': redisService.("dailyTestTypesForSite_" + siteId),commonTemplate:"true",examGroup:examGroup]
    }

    def examPage(){
        Integer siteId = utilService.getSiteId(request,session)
        if (redisService.("dailyTestTypesForSite_" + siteId) == null) prepjoyService.getDailyTestTypesForSite(""+siteId)

        String dailyTestId = params.dailyTestId
        if(redisService.("dailyTestsLatestDate"+"_"+dailyTestId)==null) prepjoyService.getDailyTestsLatestAndStartDates(dailyTestId)
        DailyTestsMst dailyTestsMst = testsService.getDailyTestsMst(dailyTestId)
        String title = dailyTestsMst.examGroup+" - "+dailyTestsMst.testName
        String seoDesc = "Best mock tests for "+dailyTestsMst.examGroup+" "+dailyTestsMst.testName
        String selectedDate =null
        String testName=dailyTestsMst.testName
        String examTitle = dailyTestsMst.examGroup+" - "+dailyTestsMst.testName
        if(!"all".equals(params.dateRange)){
            String[]  inputMonthYear = params.dateRange.split("-")
            String month = getMonthsName(Integer.parseInt(inputMonthYear[1])-1)
            if(inputMonthYear.length==3){
                title = dailyTestsMst.examGroup + " - " + dailyTestsMst.testName + " - "+inputMonthYear[2]+ " - " + month + " " + inputMonthYear[0]
                seoDesc = "Best mock tests for " + dailyTestsMst.examGroup + " " + dailyTestsMst.testName + " "+inputMonthYear[2]+ " - " + month + " " + inputMonthYear[0]
                selectedDate = inputMonthYear[2]+"-"+inputMonthYear[1]+"-"+inputMonthYear[0]

            }else {
                title = dailyTestsMst.examGroup + " - " + dailyTestsMst.testName + " - " + month + " " + inputMonthYear[0]
                seoDesc = "Best mock tests for " + dailyTestsMst.examGroup + " " + dailyTestsMst.testName + " - " + month + " " + inputMonthYear[0]
            }
        }
         ['latestDate':redisService.("dailyTestsLatestDate"+"_"+dailyTestId),'startingDate':redisService.("dailyTestStartingDate"+"_"+dailyTestId),dailyTestsMst:dailyTestsMst,
         title:title,seoDesc: seoDesc,'dailyTestTypes': redisService.("dailyTestTypesForSite_" + siteId),selectedDate:selectedDate,commonTemplate:"true",testName:testName,examTitle:examTitle]
    }

    def getMonthsName(index)
    {
        def months = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
        ];

        if (index >= 0 && index < months.size()) {
            return months[index];
        } else {
            return 'Invalid index';
        }
    }
}
