package com.wonderslate.games

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.NewsService
import com.wonderslate.data.UtilService
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

class NewsController {
    NewsService newsService
    def redisService
    def springSecurityService
    DataProviderService dataProviderService
    UtilService utilService
    def index() { }

    def getNewsLanguageAndSource(){
        if(redisService.("newsLanguages")==null) newsService.getNewsLanguages()
        if(redisService.("newsSource")==null) newsService.getNewsSource()
        def json = ['newsLanguages':redisService.("newsLanguages"),'newsSource':redisService.("newsSource")]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def addUserNewsLangPreference(){
        String siteId = params.siteId
        String username = springSecurityService.currentUser.username
        String languages = params.languages
        newsService.addUserNewsLangPreference(siteId,username,languages)

        def json = [status:"updated"]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def addUserNewsSourcePreference(){
        String siteId = params.siteId
        String username = springSecurityService.currentUser.username
        String newsSourceIds = params.newsSourceIds
        newsService.addUserNewsSourcePreference(siteId,username,newsSourceIds)

        def json = [status:"updated"]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def addUserNewsSourceAndLanguagePreference(){
        String siteId = params.siteId
        String username = springSecurityService.currentUser.username
        String newsSourceIds = params.newsSourceIds
        String languages = params.languages
        newsService.addUserNewsLangPreference(siteId,username,languages)
        newsService.addUserNewsSourcePreference(siteId,username,newsSourceIds)

        def json = [status:"updated"]
        render json as JSON
    }

}
