package com.wonderslate.report

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.UtilService
import com.wonderslate.institute.OnlineTestService
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional

import java.text.SimpleDateFormat

class AireportController {

    SpringSecurityService springSecurityService
    AireportService aireportService
    OnlineTestService onlineTestService
    DataProviderService dataProviderService
    UtilService utilService
    def redisService

    /**
     * Main dashboard view
     * Shows analytics dashboard with role-based access control
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def dashboard() {
        // Get current user
        User user = springSecurityService.currentUser
        Integer siteId = utilService.getSiteId(request, session)

        // Check if demo mode is requested
        boolean isDemoMode = params.showReport == 'demo'

        // Get user role and available batches
        String userRole = aireportService.getUserRole(user.username, siteId)
        def batches = aireportService.getBatchesForUser(user.username, siteId)

        // Add "My Data" option for instructors and managers
        if (userRole == 'Manager' || userRole == 'Instructor') {
            batches = [[id: 'personal', name: 'My Personal Data']] + batches
        }

        // Get institutes for user
        List institutes = onlineTestService.getInstitutesForUser(siteId)

        // Default date range (last 7 days)
        Calendar cal = Calendar.instance
        Date toDate = cal.time
        cal.add(Calendar.DAY_OF_MONTH, -7)
        Date fromDate = cal.time

        // Format dates for display
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
        String fromDateStr = sdf.format(fromDate)
        String toDateStr = sdf.format(toDate)

        // Initialize empty data structures for the view
        def emptySubjectData = [labels: [], data: [], total: 0]
        def emptyPromptTypeData = [labels: [], data: [], total: 0]
        def emptyPracticeData = []
        def emptyTimeSeriesData = [labels: [], datasets: []]

        // Return model for view
        [
            title: "Analytics Dashboard",
            userRole: userRole,
            batches: batches,
            fromDate: fromDateStr,
            toDate: toDateStr,
            institutes: institutes,
            commonTemplate: "true",
            subjectData: emptySubjectData,
            promptTypeData: emptyPromptTypeData,
            practiceData: emptyPracticeData,
            timeSeriesData: emptyTimeSeriesData,
            isDemoMode: isDemoMode
        ]
    }

    /**
     * AJAX endpoint to get interaction data
     * Returns data for the Interaction Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getInteractionData() {
        try {
            // Get parameters
            String username = springSecurityService.currentUser.username
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null
            def batchId = params.batchId ?: null

            // Convert batchId to Long if it's not 'personal'
            Long batchIdLong = null
            if (batchId) {
                if (batchId instanceof String && batchId != 'personal') {
                    try {
                        batchIdLong = Long.parseLong(batchId)
                        log.info("Converted batchId ${batchId} to Long: ${batchIdLong}")
                        batchId = batchIdLong
                    } catch (NumberFormatException e) {
                        log.error("Error converting batchId to Long: ${e.message}")
                    }
                } else if (batchId instanceof Long) {
                    batchIdLong = batchId
                } else if (batchId == 'personal') {
                    batchIdLong = null
                }
            }

            log.info("Using batchId: ${batchId} (type: ${batchId?.getClass()?.name})")

            // Validate dates
            if (!fromDate || !toDate) {
                Calendar cal = Calendar.instance
                toDate = cal.time
                cal.add(Calendar.DAY_OF_MONTH, -7)
                fromDate = cal.time
            }

            // Check if demo mode is requested
            boolean isDemoMode = params.showReport == 'demo'

            // Get interaction data
            def interactionData = aireportService.getInteractionData(username, fromDate, toDate, batchIdLong, isDemoMode)

            // Return JSON response
            def json = [
                status: 'success',
                data: interactionData
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting interaction data", e)
            def json = [
                status: 'error',
                message: "Failed to retrieve interaction data: ${e.message}"
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get practice data
     * Returns data for the Practice Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getPracticeData() {
        try {
            // Get parameters
            String username = springSecurityService.currentUser.username
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null
            def batchId = params.batchId ?: null

            // Convert batchId to Long if it's not 'personal'
            Long batchIdLong = null
            if (batchId) {
                if (batchId instanceof String && batchId != 'personal') {
                    try {
                        batchIdLong = Long.parseLong(batchId)
                        log.info("Converted batchId ${batchId} to Long: ${batchIdLong}")
                        batchId = batchIdLong
                    } catch (NumberFormatException e) {
                        log.error("Error converting batchId to Long: ${e.message}")
                    }
                } else if (batchId instanceof Long) {
                    batchIdLong = batchId
                } else if (batchId == 'personal') {
                    batchIdLong = null
                }
            }

            log.info("Using batchId: ${batchId} (type: ${batchId?.getClass()?.name})")

            // Validate dates
            if (!fromDate || !toDate) {
                Calendar cal = Calendar.instance
                toDate = cal.time
                cal.add(Calendar.DAY_OF_MONTH, -7)
                fromDate = cal.time
            }

            // Check if demo mode is requested
            boolean isDemoMode = params.showReport == 'demo'

            // Get practice data
            def practiceData = aireportService.getPracticeData(username, fromDate, toDate, batchIdLong, isDemoMode)

            // Return JSON response
            def json = [
                status: 'success',
                data: practiceData
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting practice data", e)
            def json = [
                status: 'error',
                message: "Failed to retrieve practice data: ${e.message}"
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get batches for user
     * Returns list of batches based on user role
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getBatchesForUser() {
        try {
            // Get parameters
            String username = springSecurityService.currentUser.username
            Integer siteId = utilService.getSiteId(request, session)

            // Get batches
            def batches = aireportService.getBatchesForUser(username, siteId)

            // Return JSON response
            def json = [
                status: 'success',
                batches: batches
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting batches", e)
            def json = [
                status: 'error',
                message: "Failed to retrieve batches: ${e.message}"
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get books for a specific subject
     * Returns data for the Books Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getBooksBySubject() {
        try {
            // Get parameters
            String username = springSecurityService.currentUser.username
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null
            def batchId = params.batchId ?: null
            String subject = params.subject

            // Convert batchId to Long if it's not 'personal'
            Long batchIdLong = null
            if (batchId) {
                if (batchId instanceof String && batchId != 'personal') {
                    try {
                        batchIdLong = Long.parseLong(batchId)
                        log.info("Converted batchId ${batchId} to Long: ${batchIdLong}")
                        batchId = batchIdLong
                    } catch (NumberFormatException e) {
                        log.error("Error converting batchId to Long: ${e.message}")
                    }
                } else if (batchId instanceof Long) {
                    batchIdLong = batchId
                } else if (batchId == 'personal') {
                    batchIdLong = null
                }
            }

            log.info("Using batchId: ${batchId} (type: ${batchId?.getClass()?.name})")

            // Validate dates
            if (!fromDate || !toDate) {
                Calendar cal = Calendar.instance
                toDate = cal.time
                cal.add(Calendar.DAY_OF_MONTH, -7)
                fromDate = cal.time
            }

            // Check if demo mode is requested
            boolean isDemoMode = params.showReport == 'demo'

            // Get books data
            def booksData = aireportService.getBooksBySubject(username, fromDate, toDate, subject, batchIdLong, isDemoMode)

            // Return JSON response
            def json = [
                status: 'success',
                data: booksData,
                subject: subject
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting books data", e)
            def json = [
                status: 'error',
                message: "Failed to retrieve books data: ${e.message}"
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get chapters for a specific book
     * Returns data for the Chapters Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getChaptersByBook() {
        try {
            // Get parameters
            String username = springSecurityService.currentUser.username
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null
            def batchId = params.batchId ?: null
            Long bookId = params.long('bookId')

            // Convert batchId to Long if it's not 'personal'
            Long batchIdLong = null
            if (batchId) {
                if (batchId instanceof String && batchId != 'personal') {
                    try {
                        batchIdLong = Long.parseLong(batchId)
                        log.info("Converted batchId ${batchId} to Long: ${batchIdLong}")
                        batchId = batchIdLong
                    } catch (NumberFormatException e) {
                        log.error("Error converting batchId to Long: ${e.message}")
                    }
                } else if (batchId instanceof Long) {
                    batchIdLong = batchId
                } else if (batchId == 'personal') {
                    batchIdLong = null
                }
            }

            log.info("Using batchId: ${batchId} (type: ${batchId?.getClass()?.name})")

            // Validate dates
            if (!fromDate || !toDate) {
                Calendar cal = Calendar.instance
                toDate = cal.time
                cal.add(Calendar.DAY_OF_MONTH, -7)
                fromDate = cal.time
            }

            // Check if demo mode is requested
            boolean isDemoMode = params.showReport == 'demo'

            // Get chapters data
            def chaptersData = aireportService.getChaptersByBook(username, fromDate, toDate, bookId, batchIdLong, isDemoMode)

            // Return JSON response
            def json = [
                status: 'success',
                data: chaptersData,
                bookId: bookId
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting chapters data", e)
            def json = [
                status: 'error',
                message: "Failed to retrieve chapters data: ${e.message}"
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get practice books for a specific subject
     * Returns data for the Practice Books Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getPracticeBooksBySubject() {
        try {
            log.info("getPracticeBooksBySubject called with params: ${params}")

            // Get parameters
            String username = springSecurityService.currentUser.username
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null
            def batchId = params.batchId ?: null
            String subject = params.subject

            log.info("Processing request for subject: ${subject}, username: ${username}, fromDate: ${fromDate}, toDate: ${toDate}, batchId: ${batchId}")

            // Convert batchId to Long if it's not 'personal'
            Long batchIdLong = null
            if (batchId) {
                if (batchId instanceof String && batchId != 'personal') {
                    try {
                        batchIdLong = Long.parseLong(batchId)
                        log.info("Converted batchId ${batchId} to Long: ${batchIdLong}")
                        batchId = batchIdLong
                    } catch (NumberFormatException e) {
                        log.error("Error converting batchId to Long: ${e.message}")
                    }
                } else if (batchId instanceof Long) {
                    batchIdLong = batchId
                } else if (batchId == 'personal') {
                    batchIdLong = null
                }
            }

            log.info("Using batchId: ${batchId} (type: ${batchId?.getClass()?.name})")

            // Validate dates
            if (!fromDate || !toDate) {
                Calendar cal = Calendar.instance
                toDate = cal.time
                cal.add(Calendar.DAY_OF_MONTH, -7)
                fromDate = cal.time
            }

            // Check if demo mode is requested
            boolean isDemoMode = params.showReport == 'demo'

            // Get practice books data
            log.info("Calling aireportService.getPracticeBooksBySubject")
            def booksData = aireportService.getPracticeBooksBySubject(username, fromDate, toDate, subject, batchIdLong, isDemoMode)
            log.info("Received data from service: ${booksData}")

            // Return JSON response
            def json = [
                status: 'success',
                data: booksData,
                subject: subject
            ]
            log.info("Returning JSON response: ${json}")
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting practice books data", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve practice books data: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get practice chapters for a specific book
     * Returns data for the Practice Chapters Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getPracticeChaptersByBook() {
        try {
            log.info("getPracticeChaptersByBook called with params: ${params}")

            // Get parameters
            String username = springSecurityService.currentUser.username
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null
            def batchId = params.batchId ?: null
            Long bookId = params.long('bookId')

            log.info("Processing request for bookId: ${bookId}, username: ${username}, fromDate: ${fromDate}, toDate: ${toDate}, batchId: ${batchId}")

            // Convert batchId to Long if it's not 'personal'
            Long batchIdLong = null
            if (batchId) {
                if (batchId instanceof String && batchId != 'personal') {
                    try {
                        batchIdLong = Long.parseLong(batchId)
                        log.info("Converted batchId ${batchId} to Long: ${batchIdLong}")
                        batchId = batchIdLong
                    } catch (NumberFormatException e) {
                        log.error("Error converting batchId to Long: ${e.message}")
                    }
                } else if (batchId instanceof Long) {
                    batchIdLong = batchId
                } else if (batchId == 'personal') {
                    batchIdLong = null
                }
            }

            log.info("Using batchId: ${batchId} (type: ${batchId?.getClass()?.name})")

            // Validate dates
            if (!fromDate || !toDate) {
                Calendar cal = Calendar.instance
                toDate = cal.time
                cal.add(Calendar.DAY_OF_MONTH, -7)
                fromDate = cal.time
            }

            // Check if demo mode is requested
            boolean isDemoMode = params.showReport == 'demo'

            // Get practice chapters data
            log.info("Calling aireportService.getPracticeChaptersByBook")
            def chaptersData = aireportService.getPracticeChaptersByBook(username, fromDate, toDate, bookId, batchIdLong, isDemoMode)
            log.info("Received data from service: ${chaptersData}")

            // Return JSON response
            def json = [
                status: 'success',
                data: chaptersData,
                bookId: bookId
            ]
            log.info("Returning JSON response: ${json}")
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting practice chapters data", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve practice chapters data: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get learning progress metrics
     * Returns data for the Progress Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getLearningProgress() {
        try {
            log.info("getLearningProgress called with params: ${params}")

            // Get parameters
            String username = springSecurityService.currentUser.username
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date toDate = params.toDate ? sdf.parse(params.toDate) : new Date()
            Integer days = params.int('days', 30)
            def batchId = params.batchId ?: null

            log.info("Processing request for username: ${username}, toDate: ${toDate}, days: ${days}, batchId: ${batchId}")

            // Convert batchId to Long if it's not 'personal'
            Long batchIdLong = null
            if (batchId) {
                if (batchId instanceof String && batchId != 'personal') {
                    try {
                        batchIdLong = Long.parseLong(batchId)
                        log.info("Converted batchId ${batchId} to Long: ${batchIdLong}")
                        batchId = batchIdLong
                    } catch (NumberFormatException e) {
                        log.error("Error converting batchId to Long: ${e.message}")
                    }
                } else if (batchId instanceof Long) {
                    batchIdLong = batchId
                } else if (batchId == 'personal') {
                    batchIdLong = null
                }
            }

            log.info("Using batchId: ${batchId} (type: ${batchId?.getClass()?.name})")

            // Check if demo mode is requested
            boolean isDemoMode = params.showReport == 'demo'

            // Get learning progress data
            log.info("Calling aireportService.getLearningProgress")
            def progressData = aireportService.getLearningProgress(username, toDate, days, batchIdLong, isDemoMode)
            log.info("Received data from service: ${progressData}")

            // Return JSON response
            def json = [
                status: 'success',
                data: progressData
            ]
            log.info("Returning JSON response")
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting learning progress data", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve learning progress data: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }
}
