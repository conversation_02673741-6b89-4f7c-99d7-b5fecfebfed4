package com.wonderslate.report

import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import com.wonderslate.usermanagement.User
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.CourseBatchesDtl
import java.text.SimpleDateFormat

/**
 * Controller for book-level analytics functionality
 */

class BookAnalyticsController {
    def springSecurityService
    BookAnalyticsService bookAnalyticsService
    def utilService

    /**
     * AJAX endpoint to get book analytics data
     * Returns data for the Book Analytics section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getBookAnalytics() {
        try {
            log.info("getBookAnalytics called with params: ${params}")

            // Get parameters
            String username = springSecurityService.currentUser.username
            Long bookId = params.long('bookId')

            // Get date range parameters
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null

            // Get batch parameter
            def batchId = params.batchId ?: null

            // Convert batchId to Long if it's not null and not 'personal'
            Long batchIdLong = null
            if (batchId && batchId != 'personal') {
                batchIdLong = Long.parseLong(batchId)
            }

            // Check if demo mode is requested
            log.info("Raw params: ${params}")
            log.info("showReport param: ${params.showReport}")
            boolean isDemoMode = params.showReport == 'demo'

            log.info("Processing request for bookId: ${bookId}, username: ${username}, fromDate: ${fromDate}, toDate: ${toDate}, batchId: ${batchIdLong}, isDemoMode: ${isDemoMode}")

            // Get book analytics data
            def analyticsData = bookAnalyticsService.getBookAnalytics(username, bookId, fromDate, toDate, batchIdLong, isDemoMode)
            log.info("Received data from service: ${analyticsData}")

            // Return JSON response
            def json = [
                status: 'success',
                data: analyticsData,
                bookId: bookId
            ]
            log.info("Returning JSON response: ${json}")
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting book analytics data", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve book analytics data: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get chapter analytics data
     * Returns data for the Chapter Analytics section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getChapterAnalytics() {
        try {
            log.info("getChapterAnalytics called with params: ${params}")

            // Get parameters
            String username = springSecurityService.currentUser.username
            Long chapterId = params.long('chapterId')

            // Get date range parameters
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null

            // Get batch parameter
            def batchId = params.batchId ?: null

            // Convert batchId to Long if it's not null and not 'personal'
            Long batchIdLong = null
            if (batchId && batchId != 'personal') {
                batchIdLong = Long.parseLong(batchId)
            }

            // Check if demo mode is requested
            log.info("Chapter - Raw params: ${params}")
            log.info("Chapter - showReport param: ${params.showReport}")
            boolean isDemoMode = params.showReport == 'demo'

            log.info("Processing request for chapterId: ${chapterId}, username: ${username}, fromDate: ${fromDate}, toDate: ${toDate}, batchId: ${batchIdLong}, isDemoMode: ${isDemoMode}")

            // Get chapter analytics data
            def analyticsData = bookAnalyticsService.getChapterAnalytics(username, chapterId, fromDate, toDate, batchIdLong, isDemoMode)
            log.info("Received data from service: ${analyticsData}")

            // Return JSON response
            def json = [
                status: 'success',
                data: analyticsData,
                chapterId: chapterId
            ]
            log.info("Returning JSON response: ${json}")
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting chapter analytics data", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve chapter analytics data: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get book practice data
     * Returns data for the Book Practice Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getBookPracticeData() {
        try {
            log.info("getBookPracticeData called with params: ${params}")

            // Get parameters
            String username = springSecurityService.currentUser.username
            Long bookId = params.long('bookId')

            // Get date range parameters
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null

            // Get batch parameter
            def batchId = params.batchId ?: null

            // Convert batchId to Long if it's not null and not 'personal'
            Long batchIdLong = null
            if (batchId && batchId != 'personal') {
                batchIdLong = Long.parseLong(batchId)
            }

            // Check if demo mode is requested
            log.info("Raw params: ${params}")
            log.info("showReport param: ${params.showReport}")
            boolean isDemoMode = params.showReport == 'demo'
            println("isDemoMode: ${isDemoMode}")
            log.info("Processing request for bookId: ${bookId}, username: ${username}, fromDate: ${fromDate}, toDate: ${toDate}, batchId: ${batchIdLong}, isDemoMode: ${isDemoMode}")

            // Get book practice data
            def practiceData = bookAnalyticsService.getBookPracticeData(username, bookId, fromDate, toDate, batchIdLong, isDemoMode)
            log.info("Received practice data from service")

            // Return JSON response
            def json = [
                status: 'success',
                data: practiceData,
                bookId: bookId
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting book practice data", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve book practice data: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get chapter practice data
     * Returns data for the Chapter Practice Dashboard section
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getChapterPracticeData() {
        try {
            log.info("getChapterPracticeData called with params: ${params}")

            // Get parameters
            String username = springSecurityService.currentUser.username
            Long chapterId = params.long('chapterId')

            // Get date range parameters
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd")
            Date fromDate = params.fromDate ? sdf.parse(params.fromDate) : null
            Date toDate = params.toDate ? sdf.parse(params.toDate) : null

            // Get batch parameter
            def batchId = params.batchId ?: null

            // Convert batchId to Long if it's not null and not 'personal'
            Long batchIdLong = null
            if (batchId && batchId != 'personal') {
                batchIdLong = Long.parseLong(batchId)
            }

            // Check if demo mode is requested
            log.info("Chapter Practice - Raw params: ${params}")
            log.info("Chapter Practice - showReport param: ${params.showReport}")
            boolean isDemoMode = params.showReport == 'demo'

            log.info("Processing request for chapterId: ${chapterId}, username: ${username}, fromDate: ${fromDate}, toDate: ${toDate}, batchId: ${batchIdLong}, isDemoMode: ${isDemoMode}")

            // Get chapter practice data
            def practiceData = bookAnalyticsService.getChapterPracticeData(username, chapterId, fromDate, toDate, batchIdLong, isDemoMode)
            log.info("Received practice data from service")

            // Return JSON response
            def json = [
                status: 'success',
                data: practiceData,
                chapterId: chapterId
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting chapter practice data", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve chapter practice data: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }

    /**
     * AJAX endpoint to get batches for the current user
     * Returns list of batches for filtering
     */
    @Secured(['ROLE_USER'])
    @Transactional
    def getBatchesForUser() {
        try {
            log.info("getBatchesForUser called with params: ${params}")

            // Get current user
            User user = springSecurityService.currentUser
            Integer siteId = utilService.getSiteId(request, session)

            // Get user role
            def userRoles = user.authorities.collect { it.authority }
            boolean isManager = userRoles.any { it == 'ROLE_INSTITUTE_ADMIN' || it == 'ROLE_INSTITUTE_REPORT_MANAGER' || it == 'ROLE_GPT_MANAGER' }

            def batches = []

            // Add "My Data" option for managers
            if (isManager) {
                batches << [id: 'personal', name: 'My Personal Data']
            }

            // Get batches for the user
            def userBatches = []
            if (isManager) {
                // For managers, get all batches they have access to
                def instituteIds = []
                def instituteUserDtls = com.wonderslate.institute.InstituteUserDtl.findAllByUsername(user.username)
                instituteUserDtls.each { instituteUserDtl ->
                    instituteIds << instituteUserDtl.instituteId
                }

                if (instituteIds) {
                    userBatches = CourseBatchesDtl.findAllByStatusAndConductedForInList("active", instituteIds, [sort: "name"])
                }
            } else {
                // For regular users, get batches they are enrolled in
                def batchUserDtls = BatchUserDtl.findAllByUsername(user.username)
                def batchIds = batchUserDtls.collect { it.batchId }

                if (batchIds) {
                    userBatches = CourseBatchesDtl.findAllByIdInList(batchIds, [sort: "name"])
                }
            }

            // Add batches to the list
            userBatches.each { batch ->
                batches << [id: batch.id, name: batch.name]
            }

            // Return JSON response
            def json = [
                status: 'success',
                batches: batches
            ]
            render json as JSON
        } catch (Exception e) {
            log.error("Error getting batches for user", e)
            e.printStackTrace()
            def json = [
                status: 'error',
                message: "Failed to retrieve batches: ${e.message}",
                stackTrace: e.stackTrace.toString()
            ]
            render json as JSON
        }
    }
}
