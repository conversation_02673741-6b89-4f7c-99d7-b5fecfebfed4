package com.wonderslate

import com.wonderslate.cache.DataProviderService
import grails.transaction.Transactional

class AppinappController {

    DataProviderService dataProviderService
    def index() {
        println("hello")
        render session["siteId"]
    }

    @Transactional
    def store(){

            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")

        [commonTemplate:"true", showPublishers:"true"]

    }
}
