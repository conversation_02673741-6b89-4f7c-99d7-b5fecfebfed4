package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.BooksCodeMst
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.KeyValueMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UtilService
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BatchUserDtlNotRegistered
import com.wonderslate.log.BookPricesPing
import com.wonderslate.log.GptDefaultCreateLog
import com.wonderslate.log.SecureLogins
import com.wonderslate.publish.BooksPermission
import com.wonderslate.shop.BookPriceService
import com.wonderslate.shop.ExOrderUser
import com.wonderslate.shop.OutsidePurchase
import com.wonderslate.shop.WsshopService
import com.wonderslate.toDo.ToDoService
import com.wonderslate.usermanagement.Role
import com.wonderslate.usermanagement.User
import com.wonderslate.usermanagement.UserManagementService
import com.wonderslate.usermanagement.UserRole
import com.wonderslate.usermanagement.WinGenerator
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.plugins.mail.MailService
import grails.plugins.rest.client.RestBuilder
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql
import org.grails.datastore.mapping.query.Query.In
import org.springframework.security.core.context.SecurityContextHolder

import javax.servlet.http.Cookie

class IntelligenceController {
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    UserManagementService userManagementService
    MailService mailService
    def rememberMeServices
    def redisService
    WsshopService wsshopService
    ToDoService toDoService
    UtilService utilService
    BookPriceService bookPriceService
    def index() { }


    @Transactional
    def externalPurchase(){
        boolean userExists= true
        def siteId = new Integer(params.siteId)
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+siteId)
        String directUrl="https://"+ request.getServerName()+"/exOrder/"
        if(keyValueMst!=null&&keyValueMst.keyValue.equals(params.secretKey)&&params.mobile!=null) {
            String clientPO = params.paymentReference.trim()
            OutsidePurchase outsidePurchase = OutsidePurchase.findByPaymentReferenceAndSiteId(clientPO,siteId)
            if (outsidePurchase == null) {

                String username = "" + siteId + "_" + params.mobile.trim().replace('+','').replace(' ','')
                 username = username.replace('+','')
                User user = User.findByUsername(username)
                String plainPassword
                if (user == null) {
                    userExists = false
                    String name = params.name
                    plainPassword = UUID.randomUUID().toString().substring(0, 5).toLowerCase();
                    String password = plainPassword
                    String mobile = params.mobile.trim().replace('+','').replace(' ','')
                    mobile = mobile.replaceAll(' ','')
                    String email = params.email
                    WinGenerator winGenerator = new WinGenerator()
                    winGenerator.save(failOnError: true)
                    user = new User(username: username, name: name, password: password, mobile: mobile, email: email, win: winGenerator.id, siteId: siteId)
                    user.save(failOnError: true, flush: true)
                    //add appropriate roles
                    Role role = Role.findByAuthority("ROLE_USER")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_ADD")
                    UserRole.create(user, role, true)
                    role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                    UserRole.create(user, role, true)

                }else{
                    if(params.email!=null) user.email = params.email
                    if(params.mobile!=null&&user.mobile==null) user.mobile=params.mobile
                    user.save(failOnError: true, flush: true)
                }

                BooksMst booksMst
                if (params.isbn != null) {
                    String[] isbns = params.isbn.split(",")

                    try {
                        outsidePurchase = new OutsidePurchase(siteId: siteId, name: params.name, mobile: params.mobile.trim().replace('+','').replace(' ',''), email: params.email,
                                price: new Double(params.price), paymentReference: params.paymentReference, bookId: new Integer(-1), isbns: params.isbn,saleSource: params.saleSource)
                        outsidePurchase.save(failOnError: true, flush: true)
                        //create a 16 digit alphanumeric random String for the direct url
                        String randomString = UUID.randomUUID().toString().replaceAll("-","").substring(0,16)
                        //generate the link
                        boolean duplicateKey = true
                        while(duplicateKey){
                            ExOrderUser exOrderUser = ExOrderUser.findByOrderKey(randomString)
                            if(exOrderUser==null) duplicateKey = false
                            else randomString = UUID.randomUUID().toString().replaceAll("-","").substring(0,16)
                        }
                        String urlKey = Base64.getEncoder().encodeToString((randomString).getBytes())
                        ExOrderUser exOrderUser = new ExOrderUser(orderId:outsidePurchase.id,orderKey:randomString)
                        exOrderUser.save(failOnError: true, flush: true)
                        directUrl+=urlKey

                    } catch (Exception e) {
                        println(e.toString())
                        def json = [status: "fail", reason: "Not all parameters provided"]
                        render json as JSON
                        return
                    }
                    String missingIsbns = "";
                    String isbn
                    for (int i = 0; i < isbns.length; i++) {
                        isbn = (""+isbns[i]).trim()
                        booksMst = dataProviderService.getBooksMstByIsbnAndPublished(isbn)
                        if (booksMst != null) {
                            BooksPermission booksPermission = new BooksPermission()
                            booksPermission.bookId = booksMst.id
                            booksPermission.username = user.username
                            if (params.paymentReference != null && !"".equals(params.paymentReference)) {
                                booksPermission.clientPo = params.paymentReference
                                booksPermission.poType = 'PURCHASEFROMCLIENTSITE'
                            } else {
                                booksPermission.poType = 'ADDEDFORFREE'
                            }
                            booksPermission.addedBy = "System"

                            if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                                Calendar c = Calendar.getInstance()
                                c.add(Calendar.DATE, booksMst.validityDays)
                                booksPermission.expiryDate = c.getTime()
                            }
                            booksPermission.chatTokensBalance=20
                            booksPermission.save(failOnError: true, flush: true)

                            //code to add package books
                            if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
                            redisService.("userShelfBooks_"+user.username)=null

                        } else {
                            missingIsbns += isbns[i] + ","
                            try {
                                mailService.sendMail {
                                    async true
                                    to "<EMAIL>", "<EMAIL>", "<EMAIL>"
                                    from "Wonderslate <<EMAIL>>"
                                    subject "Missing isbns on " + siteId
                                    text "This isbn are not found " + isbns[i] + " order ref " + params.paymentReference
                                }
                                if("39".equals(""+siteId)){
                                    mailService.sendMail {
                                        async true
                                        to "<EMAIL>"
                                        from "Wonderslate <<EMAIL>>"
                                        subject "Missing isbns on order " + params.paymentReference
                                        text "This isbn are not found " + isbns[i] + " order ref " + params.paymentReference

                                    }
                                }
                            } catch (Exception e) {
                                println("Exception in sending welcome <NAME_EMAIL> and exception is " + e.toString())
                            }
                        }

                    }
                    outsidePurchase.missedIsbns = missingIsbns
                    outsidePurchase.save(failOnError: true, flush: true)

                    if (siteId.intValue() == 46) {
                        SiteMst sm = dataProviderService.getSiteMst(siteId)
                        if (user.email != null && !"".equals(user.email) && isValidEmail(user.email)) {
                            String mailSubject = "Welcome to ${sm.clientName}"
                            String mailFrom = sm.fromEmail != null ? sm.fromEmail : "Wonderslate <<EMAIL>>"
                            if (siteId.intValue() == 46) mailFrom = "MTG <<EMAIL>>"
                            try {
                                String mailText
                                if(userExists){
                                    mailText = "Hello " + user.name + "\n\n Your purchase is successful and ebooks are added to your account. Here are the details.\n\nLogin Id: ${params.mobile}\n\nPlease click on https://${sm.siteDomainName} to access your account. "
                                } else {
                                    mailText = "Hello " + user.name + "\n\n Your account has been created and your purchased ebooks are added to your account. Here are the details.\n\nLogin Id: ${params.mobile}\nPassword: ${plainPassword}\n\nPlease click on https://${sm.siteDomainName} to access your account."
                                }
                                mailText += "\nPlease note: This is Smart eBook and cannot be downloaded as PDF. For offline access use our Android app.\n https://play.google.com/store/apps/details?id=com.mtg.publication&pli=1\n\nThanks\nTeam ${sm.clientName}"
                                mailService.sendMail {
                                    async true
                                    to user.email
                                    from mailFrom
                                    subject mailSubject
                                    text mailText
                                }

                            } catch (Exception e) {
                                println("Exception in sending welcome <NAME_EMAIL> and exception is " + e.toString())
                            }
                        } else if (user.mobile != null && !"".equals(user.mobile)) {
                            try {

                                RestBuilder rest = new RestBuilder()
                                String message = " Dear ${user.name}, Your account is created. Click on the below link ${sm.siteDomainName} and login using ${params.mobile} and Password: ${plainPassword}."
                                if (userExists) message = "Dear ${user.name}, Your account is created. Click on the below link ${sm.siteDomainName} and login using ${params.mobile}."
                                if (sm.smsUrl != null && sm.smsUrl != "") {
                                    rest.post(sm.smsUrl + params.mobile + '&' + sm.smsUrl1 + '=' + message)
                                }

                            } catch (Exception e) {
                                println("Exception sending sms to " + params.mobile)
                            }
                        }
                    }
                    def json = [status: "success", loginId: params.mobile, password: plainPassword, missingIsbns: missingIsbns,directUrl:directUrl]
                    if (userExists) json = [status: "success", loginId: params.mobile, missingIsbns: missingIsbns,directUrl:directUrl]
                    render json as JSON

                } else {
                    def json = [status: "fail", reason: "Invalid ISBN"]
                    render json as JSON
                }
            }else{
                def json = [status: "fail", reason: "Duplicate order."]
                render json as JSON
            }
        }else{
            def json = [status:"fail",reason:"Invalid secret key or mobile number not provided"]
            render json as JSON
        }
    }

    @Transactional

    def cancelOrder(){
        String clientPO = params.paymentReference
        String status = "Order not found"
         OutsidePurchase outsidePurchase = OutsidePurchase.findByPaymentReference(clientPO)
        if(outsidePurchase!=null){
            outsidePurchase.status="cancelled"
            outsidePurchase.dateUpdated = new Date()
            outsidePurchase.save(failOnError: true, flush: true)
            BooksPermission booksPermission = BooksPermission.findByClientPo(clientPO)
            String username =""
            if(booksPermission!=null) username = booksPermission.username
            BooksPermission.executeUpdate("delete BooksPermission where clientPo='" + clientPO + "'")
            status = "Order cancelled"
            redisService.("userShelfBooks_"+username)=null

        }
        def json = [status:status]
        render json as JSON

    }

    @Transactional
    def createBookAccessCodeForPurchase(){
        def siteId = new Integer(params.siteId)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+siteId)
        if(keyValueMst!=null&&keyValueMst.keyValue.equals(params.secretKey)) {

            BooksMst booksMst = dataProviderService.getBooksMstByIsbn(params.isbn)
            if(booksMst!=null&&params.isbn!=null){
                //check if the free accesscode exists
                String sql='select count(*) from books_code_mst where code is not null and book_id is null'
                def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
                def sql1 = new Sql(dataSource)
                def results = sql1.rows(sql);

                if(results.get(0).values()[0]==0){
                    wsshopService.createBookAccessCode(siteId,1)
                }

                BooksCodeMst booksCodeMst = BooksCodeMst.findByCodeIsNotNullAndBookIdIsNull()
                booksCodeMst.bookId =  booksMst.id
                booksCodeMst.siteId =  siteId
                booksCodeMst.save(failOnError: true, flush: true)

                try{
                    OutsidePurchase outsidePurchase = new OutsidePurchase(siteId:siteId,name:params.name,mobile:params.mobile,email:params.email,
                            price:new Double(params.price),paymentReference: params.paymentReference,accessCode: booksCodeMst.code, bookId: booksMst.id)
                    outsidePurchase.save(failOnError: true, flush: true)
                    def json = [status:"success",accessCode: (booksCodeMst.code+booksMst.id)]
                    render json as JSON

                }catch(Exception e){
                    println(e.toString())
                    def json = [status:"fail",reason:"Not all parameters provided"]
                    render json as JSON
                }


            }
            else{
                def json = [status:"fail",reason:"Invalid ISBN"]
                render json as JSON
            }
        }else{
            def json = [status:"fail",reason:"Invalid secret key"]
            render json as JSON
        }
    }

    @Transactional
    def getOrderDetails(){
        String clientPO = params.paymentReference
        String status = "Order was not received"
        OutsidePurchase outsidePurchase = OutsidePurchase.findByPaymentReference(clientPO)
        if(outsidePurchase!=null){
            if("cancelled".equals(outsidePurchase.status)){
                status = "This order was cancelled."
            }
            else {
                BooksPermission booksPermission = BooksPermission.findByClientPo(clientPO)
                if (booksPermission != null) {
                    String directUrl="https://"+ request.getServerName()+"/exOrder/"
                    ExOrderUser exOrderUser = ExOrderUser.findByOrderId(new Integer(""+outsidePurchase.id))
                    String urlKey = Base64.getEncoder().encodeToString((""+(exOrderUser!=null?""+exOrderUser.orderKey:""+outsidePurchase.id)).getBytes())
                    directUrl+=urlKey
                    status = "This order has been successfully processed. The eBook has been successfully added to user account " + outsidePurchase.mobile + " (" + outsidePurchase.name + "). The password to this account has already been sent."
                    status +=" Please use this link to access "+directUrl
                } else {
                    status = "Issues with this order. Kindly contact Wonderslate support."
                }
            }
        }
        def json = [status:status,missingIsbns: outsidePurchase!=null&&outsidePurchase.missedIsbns!=null?outsidePurchase.missedIsbns:""]
        render json as JSON
    }

    @Transactional
    def sessionGenerator(){
        def siteId = new Integer(params.siteId)
        SiteMst siteMst = dataProviderService.getSiteMst(siteId)
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+siteId)
        if(keyValueMst!=null&&keyValueMst.keyValue.equals(params.secretKey)) {
            User user = User.findByUsername(siteId+"_"+params.loginId)
            if(user==null){
                //create the new user
                String name = params.name
                String username =  siteId+"_"+params.loginId
                String password = springSecurityService.encodePassword(username)
                String mobile = params.mobile
                String email = params.email
                WinGenerator winGenerator = new WinGenerator()
                winGenerator.save(failOnError: true)
                user  = new User(username: username,name: name,password: password,mobile:mobile,email:email,win: winGenerator.id,siteId: siteId)
                user.save(failOnError: true, flush: true)
                //add appropriate roles
                Role role = Role.findByAuthority("ROLE_USER")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_ADD")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                UserRole.create(user, role, true)

                List batchesToBeAdded
                batchesToBeAdded = BatchUserDtlNotRegistered.findAllByEmailAndSiteId(email,siteId)
                batchesToBeAdded.each { batch ->
                    BatchUserDtl batchUserDtl = new BatchUserDtl(username: user.username, batchId: batch.batchId, createdBy: batch.createdBy, instructor: batch.instructor)
                    batchUserDtl.save(failOnError: true, flush: true)
                }

            }
            //remove the old key

            SecureLogins secureLogins = SecureLogins.findByUsername(user.username)
            if(secureLogins!=null) secureLogins.delete()

            //create the secret key and send
            Random generator = new Random()
            String sessionNumber = ""+(generator.nextInt(9000000)+1000000)*3
            secureLogins = new SecureLogins(username: user.username,sessionNumber:sessionNumber)
            secureLogins.save(failOnError: true, flush: true)
          //  def json = [status:"success",sessionNumber:sessionNumber]
            session['userdetails'] = user
            session['appInApp']="true"
            session['siteId'] = siteId;
            session.setAttribute("entryController", "appinapp")
            session.setAttribute("siteName", siteMst.siteName)
            session.setAttribute("loginType", "email,mobile")
            session.setAttribute("siteNameUrl", "appinapp")
            if(siteMst.categories!=null) session.setAttribute("defaultLevel",siteMst.categories)
            springSecurityService.reauthenticate(user.username, user.password)
            def authentication = SecurityContextHolder.context.authentication
            rememberMeServices.loginSuccess(request, response, authentication)
            userManagementService.registerUserLogin(user.username,session.getId())

            //setting up store related stuff
            if(session["activeCategories_"+siteId] == null) {
                if(redisService.("activeCategories_"+siteId)==null) wsshopService.activeCategories(siteId)
                session["activeCategories_"+siteId] = redisService.("activeCategories_"+siteId)
            }
            if(session["activeCategoriesSyllabus_"+siteId] == null) {
                if(redisService.("activeCategoriesSyllabus_"+siteId)==null) wsshopService.getActiveCategoriesAndSyllabus(siteId)
                session["activeCategoriesSyllabus_"+siteId] = redisService.("activeCategoriesSyllabus_"+siteId)
            }

                redirect (controller: 'appinapp',action: 'store')


        }else{
            def json = [status:"fail",reason:"Invalid secret key"]
            render json as JSON
        }
    }

   def test(){
       render "test works"
   }
    @Transactional
    def addBook(){
        String isbn = params.isbn
        String siteId = params.siteId
        String username =  siteId+"_"+params.loginId
        String validity = params.validity
        String poNo = params.poNo
        String price = params.price

        BooksMst booksMst = BooksMst.findByIsbnAndSiteId(isbn,new Integer(siteId))
        if(booksMst!=null){
            BooksPermission booksPermission = new BooksPermission()
            booksPermission.bookId = booksMst.id
            booksPermission.username = username
            booksPermission.clientPo = poNo
            booksPermission.price = price!=null?new Double(price):null
            booksPermission.poType = "PURCHASE"
            booksPermission.chatTokensBalance=20

            if (validity != null  && validity != "") {
                Calendar c = Calendar.getInstance()
                c.add(Calendar.DATE, Integer.parseInt(validity))
                booksPermission.expiryDate = c.getTime()
            }

            booksPermission.save(failOnError: true, flush: true)
            dataProviderService.getBooksListForUser(username)
            def json = [status: "Success", refId:booksPermission.id]
            render json as JSON
        }else{
            def json = [status: "Fail", reason: "Incorrect ISBN number"]
            render json as JSON
        }

    }

    @Transactional
    def library(){
        def sessionNumber = params.sessionNumber
        def siteId =  params.siteId
        SecureLogins secureLogins = SecureLogins.findBySessionNumber(sessionNumber)
        if(secureLogins!=null){
            SiteMst siteMst = dataProviderService.getSiteMst(new Long(siteId))
            if(siteMst.siteBaseUrl!=null) session.setAttribute("siteBaseUrl", siteMst.siteBaseUrl);
            Cookie cookie = new Cookie("siteName",siteMst.siteName)
            cookie.path = "/"
            response.addCookie(cookie)
            session['siteId'] = new Integer(siteId);

            session.setAttribute("entryController", "welcome");
            User user = dataProviderService.getUserMst(secureLogins.username)
            springSecurityService.reauthenticate(user.username, user.password)
            session['userdetails'] = user
            if(params.isbn!=null){
                BooksMst booksMst = BooksMst.findByIsbnAndSiteId(params.isbn,new Integer(siteId))
                redirect(controller: 'wonderpublish', action: 'book', params: ['bookId': booksMst.id])
            }else{
                redirect(controller: 'wonderpublish', action: 'mybooks')
            }

        }else{
            def json = [status: "Fail", reason: "Incorrect session information"]
            render json as JSON
        }
    }

    @Transactional
    def switchUser(){
        String demoUser = params.demoUser

        session.invalidate()
        request.getSession(true)
        redirect(controller: 'intelligence', action: 'switchUserAction', params: [demoUser:demoUser])

    }

    @Transactional
    def switchUserAction(){
        KeyValueMst keyValueMst = KeyValueMst.findByKeyNameAndSiteId(params.demoUser,new Integer(1))
       if(keyValueMst!=null){
           String [] keysArray = keyValueMst.keyValue.split(",")
           User user = User.findByUsername(keysArray[1])
           session['userdetails'] = user
           session['siteId'] = new Integer(1);
           session.setAttribute("entryController", "books")
           session.setAttribute("siteName", "Wonderslate")
           session.setAttribute("loginType", "email,mobile")
           session.setAttribute("siteNameUrl", "books")
           SiteMst siteMst = dataProviderService.getSiteMst(new Integer(1))
           if(siteMst.categories!=null) session.setAttribute("defaultLevel",siteMst.categories)
           springSecurityService.reauthenticate(user.username, user.password)
           def authentication = SecurityContextHolder.context.authentication
           rememberMeServices.loginSuccess(request, response, authentication)
           redirect(uri: '/instituteHome', params: [instituteId:keysArray[0]])
       }
        else  redirect(controller: 'books', action: 'store', params: [demoUser:params.demoUser])

    }

    @Transactional
    def demo(){

    }

    @Transactional
    def outsidePurchaseRedirector(){
        byte[] decodedBytes = Base64.getDecoder().decode(params.transId);
        String decodedString = new String(decodedBytes);
        ExOrderUser exOrderUser = ExOrderUser.findByOrderKey(decodedString)
        if(exOrderUser!=null) {
            OutsidePurchase outsidePurchase = OutsidePurchase.findById(exOrderUser.orderId)
            SiteMst siteMst
            if (outsidePurchase != null) {
                outsidePurchase.status = "Link clicked"
                outsidePurchase.dateUpdated = new Date()
                outsidePurchase.save(failOnError: true, flush: true)
                session.setAttribute("userdetails", null)
                User user = User.findByUsername("" + outsidePurchase.siteId + "_" + outsidePurchase.mobile)
                springSecurityService.reauthenticate(user.username, user.password)
                def authentication = SecurityContextHolder.context.authentication
                rememberMeServices.loginSuccess(request, response, authentication)
                userManagementService.registerUserLogin(user.username, session.getId())
                siteMst = SiteMst.findById(new Integer(outsidePurchase.siteId))
                if (siteMst.commonWhiteLabel == 'true' || siteMst.commonWhiteLabel == true) {
                    userManagementService.setUserSession(siteMst.siteName, session, servletContext, response)
                } else {
                    setUserSession(user)
                }

                forward(controller: "wsLibrary", action: "myLibrary")
            }
        }else{
            redirect(uri: "")
        }
    }

    @Transactional
    def setUserSession(User user){
        println("username ois "+user.username)
        SiteMst siteMst = dataProviderService.getSiteMst(user.siteId)
        if(springSecurityService.currentUser!=null){
            if (session.getAttribute("userdetails") == null) {
                session["userdetails"] = User.findByUsername(springSecurityService.currentUser.username)

            }else if(session["userdetails"].publisherId!=null&&session["isInstitutePublisher"]==null){
                session["isInstitutePublisher"] = userManagementService.isInstitutePublisher()
            }
            if(session.getAttribute("userPendingTodoCount")==null){
                toDoService.pendingToDoCount()
            }
            //institutes for user
            List userInstitutes
            if (session["userInstitutes"] != null) userInstitutes = session["userInstitutes"]
            else {
                userInstitutes = userManagementService.getInstitutesForUser(siteMst.id, utilService.getIPAddressOfClient(request))
                if (userInstitutes.size() > 1) {
                    session["userInstituteId"]= userInstitutes[0].id
                    for (int i = 0; i < userInstitutes.size(); i++) {
                        if (!("Default".equals("" + userInstitutes[i].batchName))) {
                            userInstitutes.remove(i--)
                        }

                    }
                }
                session["userInstitutes"] = userInstitutes
            }
        }
        if(servletContext.getAttribute("googleLogEnabled")==null)
        {
            if("true".equals(dataProviderService.isGoogleAnayticsEnabled())) servletContext.setAttribute("googleLogEnabled","true")
            else servletContext.setAttribute("googleLogEnabled","false")
        }
        if(session["activeCategories_"+siteMst.id] == null) {
            if(redisService.("activeCategories_"+siteMst.id)==null) wsshopService.activeCategories(new Integer(""+siteMst.id))
            session["activeCategories_"+siteMst.id] = redisService.("activeCategories_"+siteMst.id)
        }
        if(session["activeCategoriesSyllabus_"+siteMst.id] == null) {
            if(redisService.("activeCategoriesSyllabus_"+siteMst.id)==null) wsshopService.getActiveCategoriesAndSyllabus(new Integer(""+siteMst.id))
            session["activeCategoriesSyllabus_"+siteMst.id] = redisService.("activeCategoriesSyllabus_"+siteMst.id)
        }
        session['siteId'] = new Integer(""+siteMst.id);
        session.setAttribute("entryController", siteMst.siteName)
        session.setAttribute("siteName", siteMst.clientName)
        session.setAttribute("loginType", "mobile")
        session.setAttribute("siteNameUrl", siteMst.siteName)
        session["googleUAId_"+siteMst.id] = null
        if(session["googleUAId_"+siteMst.id]==null){
            if(redisService.("googleUAId_"+siteMst.id)==null){
                dataProviderService.getGoogleUniversalAnalytics(""+siteMst.id)
            }
            session["googleUAId_"+siteMst.id] = redisService.("googleUAId_"+siteMst.id)
        }
        Cookie cookie = new Cookie("wlSiteName", "")

        cookie.path = "/"
        cookie.maxAge = 0
        response.addCookie(cookie)
        cookie = new Cookie("siteName", siteMst.siteName)
        cookie.path = "/"
        response.addCookie(cookie)
        return
    }

    @Transactional
    def receiveExternalOrders(){
        def jsonObj = request.JSON
        Long orderId = jsonObj.id
        String siteId = params.siteId
        String secretKey = params.secretKey
        String phone = jsonObj.customer.default_address.phone
        String email = jsonObj.email
        String isbn = params.isbn
        String name = jsonObj.customer.first_name + " "+ jsonObj.customer.last_name
        String price = jsonObj.total_price
        String saleSource = params.saleSource
        String bookCode =""
        String logo = ""
        String siteName=""
        String clientName = ""
        String fromEmail = ""
        def bookType = []
        String financialStatus = jsonObj.financial_status
        def paymentGatewayList = jsonObj.payment_gateway_names

        def bookListCode = jsonObj.line_items.collect { item ->
            if (jsonObj.line_items.size()>1){
                bookCode += item.product_id+","
            }else{
                bookCode += item.product_id
            }
            item.variant_title!=null? bookType.push(item.variant_title):""
            return bookCode
        }
        def siteDtl = SiteDtl.findBySiteId(new Long(siteId))
        def siteMst = SiteMst.findById(new Long(siteId))
        logo = siteDtl.logo
        siteName = siteMst.siteName
        clientName = siteMst.clientName
        fromEmail = siteMst.fromEmail

        if (bookType.contains('eBook') || bookType.contains('ebook') || bookType.contains('Ebook')){
            exOrderSupport(orderId,siteId,secretKey,phone,email,isbn,name,price,saleSource,bookCode,logo,siteName,clientName,fromEmail,paymentGatewayList,financialStatus)
        }
        render "success"
    }

    def exOrderSupport(orderId,siteId,secretKey,userMobile,userEmail,isbn,userName,price,saleSource,bookCode,logo,siteName,clientName,fromEmail,paymentGatewayList,financialStatus){
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+siteId)
        String directUrl="https://"+ request.getServerName()+"/exOrder/"

        if(keyValueMst!=null&&keyValueMst.keyValue.equals(secretKey)&&userMobile!=null) {
            String clientPO = orderId
            OutsidePurchase outsidePurchase = OutsidePurchase.findByPaymentReferenceAndSiteId(clientPO,siteId)

            userMobile = userMobile.trim().replace('+','').replace(' ','')
            String username = "" + siteId + "_" + userMobile
            username = username.replace('+','')
            boolean userExists = true
            User user = User.findByUsername(username)
            String plainPassword
            BooksMst booksMst

            if (user == null) {
                userExists = false
                String name = userName
                plainPassword = UUID.randomUUID().toString().substring(0, 5).toLowerCase();
                String password = plainPassword
                String mobile = userMobile.trim().replace('+','').replace(' ','')

                String email = userEmail
                WinGenerator winGenerator = new WinGenerator()
                winGenerator.save(failOnError: true)
                user = new User(username: username, name: name, password: password, mobile: mobile, email: email, win: winGenerator.id, siteId: siteId)
                user.save(failOnError: true, flush: true)
                //add appropriate roles
                Role role = Role.findByAuthority("ROLE_USER")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_ADD")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                UserRole.create(user, role, true)

            }

            if (outsidePurchase == null) {
                if (bookCode!=null){
                    String[] bookCodes = bookCode.split(",")
                    String missedCodes =""
                    try {
                        outsidePurchase = new OutsidePurchase(siteId: siteId, name: userName, mobile: userMobile.trim().replace('+','').replace(' ',''), email: userEmail,
                                price: new Double(price), paymentReference: orderId, bookId: new Integer(-1), isbns: bookCode,saleSource: saleSource,bookCode: bookCode,paymentStatus: financialStatus,isBookAdded: false)
                        outsidePurchase.save(failOnError: true, flush: true)

                        //create a 16 digit alphanumeric random String for the direct url
                        String randomString = UUID.randomUUID().toString().replaceAll("-","").substring(0,16)
                        //generate the link
                        boolean duplicateKey = true
                        while(duplicateKey){
                            ExOrderUser exOrderUser = ExOrderUser.findByOrderKey(randomString)
                            if(exOrderUser==null) duplicateKey = false
                            else randomString = UUID.randomUUID().toString().replaceAll("-","").substring(0,16)
                        }
                        String urlKey = Base64.getEncoder().encodeToString((randomString).getBytes())
                        ExOrderUser exOrderUser = new ExOrderUser(orderId:outsidePurchase.id,orderKey:randomString)
                        exOrderUser.save(failOnError: true, flush: true)
                        directUrl+=urlKey

                    } catch (Exception e) {
                        println(e.toString())
                        def json = [status: "fail", reason: "Not all parameters provided"]
                        render json as JSON
                        return
                    }

                    boolean isBookAdded = addingBookToLibLogic(booksMst,outsidePurchase,bookCodes,user,orderId,siteId,financialStatus,paymentGatewayList,missedCodes)
                    def json = [status: "success", loginId:userMobile, password: plainPassword,directUrl:directUrl,missedBookCodes:missedCodes]
                    externalPurchaseMail(userName,plainPassword,userMobile,userEmail,orderId,userExists,directUrl,logo,siteName,siteId,clientName,fromEmail,financialStatus,isBookAdded)
                    render json as JSON
                }else {
                    def json = [status: "fail", reason: "Invalid Book Code"]
                    render json as JSON
                }
            }else if(outsidePurchase!=null && !outsidePurchase.isBookAdded && outsidePurchase.paymentStatus=='pending'){

                String[] bookCodes = bookCode.split(",")
                String missedCodes =""
                //create a 16 digit alphanumeric random String for the direct url
                String randomString = UUID.randomUUID().toString().replaceAll("-","").substring(0,16)
                //generate the link
                boolean duplicateKey = true
                while(duplicateKey){
                    ExOrderUser exOrderUser = ExOrderUser.findByOrderKey(randomString)
                    if(exOrderUser==null) duplicateKey = false
                    else randomString = UUID.randomUUID().toString().replaceAll("-","").substring(0,16)
                }
                String urlKey = Base64.getEncoder().encodeToString((randomString).getBytes())
                ExOrderUser exOrderUser = new ExOrderUser(orderId:outsidePurchase.id,orderKey:randomString)
                exOrderUser.save(failOnError: true, flush: true)
                directUrl+=urlKey
                boolean isBookAdded = addingBookToLibLogic(booksMst,outsidePurchase,bookCodes,user,orderId,siteId,financialStatus,paymentGatewayList,missedCodes)
                def json = [status: "success", loginId:userMobile, password: plainPassword,directUrl:directUrl,missedBookCodes:missedCodes]
                externalPurchaseMail(userName,plainPassword,userMobile,userEmail,orderId,userExists,directUrl,logo,siteName,siteId,clientName,fromEmail,financialStatus,isBookAdded)
                render json as JSON
            } else{
                def json = [status: "fail", reason: "Duplicate order."]
                render json as JSON
            }
        }else{
            def json = [status:"fail",reason:"Invalid secret key or mobile number not provided"]
            render json as JSON
        }
    }

    def addingBookToLibLogic(booksMst,outsidePurchase,bookCodes,user,orderId,siteId,financialStatus,paymentGatewayList,missedCodes){

        if (financialStatus!='pending' || financialStatus=='paid' && paymentGatewayList.contains('Cash on Delivery (COD)') || paymentGatewayList.contains('COD') || paymentGatewayList.contains('Cash on Delivery') ){
            for (int i = 0; i < bookCodes.length; i++) {
                booksMst = BooksMst.findByBookCodeAndStatus(bookCodes[i],"published")
                if (booksMst != null) {
                    BooksPermission booksPermission = new BooksPermission()
                    booksPermission.bookId = booksMst.id
                    booksPermission.username = user.username
                    if (orderId != null && !"".equals(orderId)) {
                        booksPermission.clientPo = orderId
                        booksPermission.poType = 'PURCHASEFROMCLIENTSITE'
                    } else {
                        booksPermission.poType = 'ADDEDFORFREE'
                    }
                    booksPermission.addedBy = "System"
                    if (booksMst != null && booksMst.validityDays != null && booksMst.validityDays != "") {
                        Calendar c = Calendar.getInstance()
                        c.add(Calendar.DATE, booksMst.validityDays)
                        booksPermission.expiryDate = c.getTime()
                    }
                    booksPermission.chatTokensBalance=20
                    booksPermission.save(failOnError: true, flush: true)
                    //code to add package books
                    if (booksMst.packageBookIds != null) userManagementService.addPackageBooksToUser(user.username, booksMst)
                    redisService.("userShelfBooks_"+user.username)=null
                }else{
                    missedCodes +=bookCodes[i]+","
                    try {
                        mailService.sendMail {
                            async true
                            to "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"
                            from "Wonderslate <<EMAIL>>"
                            subject "Missing book codes on " + siteId
                            text "This book code are not found " + bookCodes[i] + " order ref " + params.paymentReference
                        }

                    } catch (Exception e) {
                        println("Exception in sending welcome <NAME_EMAIL> and exception is " + e.toString())
                    }
                }
            }
            outsidePurchase.missedIsbns=missedCodes
            outsidePurchase.isBookAdded = true
            outsidePurchase.paymentStatus = financialStatus
            outsidePurchase.save(failOnError: true, flush: true)
            return true
        }
        return false
    }

    def externalPurchaseMail(String userName,String plainPassword,String userMobile,String toEmail,Long orderId,Boolean userExists,String directURL,String logo,String siteName,String siteId, String clientName,String fromEmailStr,financialStatus,isBookAdded){
        if("<EMAIL>"!=toEmail) {
            String view = ""
            String fromEmail = fromEmailStr

            if (isBookAdded && financialStatus=='paid'){
                view = "/creation/externalPurchaseMail"
            }else{
                view = "/creation/paymentPendingEmail"
            }

            try {
                mailService.sendMail {
                    async true
                    to toEmail
                    from fromEmail
                    subject "Successful purchase of your eBook"
                    body(view: view,
                            model: [name      : userName,  account: toEmail, paymentId: orderId,loginId:userMobile,password: plainPassword,userExists:userExists,directUrl:directURL,logo:logo,siteName:siteName,siteId: siteId,clientName:clientName])
                }
            }catch(Exception e){
                println("Exception in sending userBookPurchase email to "+toEmail+" and exception is "+e.toString())
            }
        }
    }

    @Transactional
    def exPurchaseCancelOrder(){
        def jsonObj = request.JSON
        Long clientPO = jsonObj.id
        String status = "Order not found"
        OutsidePurchase outsidePurchase = OutsidePurchase.findByPaymentReference(clientPO)
        if(outsidePurchase!=null){
            outsidePurchase.status="cancelled"
            outsidePurchase.dateUpdated = new Date()
            outsidePurchase.save(failOnError: true, flush: true)
            BooksPermission booksPermission = BooksPermission.findByClientPo(clientPO)
            String username =""
            if(booksPermission!=null) username = booksPermission.username
            BooksPermission.executeUpdate("delete BooksPermission where clientPo='" + clientPO + "'")
            status = "Order cancelled"
            redisService.("userShelfBooks_"+username)=null
        }
        def json = [status:status]
        render json as JSON
    }

    @Transactional
    def getBookPrices(){
        BooksMst booksMst =null
        def bookId
        def json = [hasPrices: "No"]
        if(params.bookId!=null||params.bookCode!=null||params.isbn!=null) {
            def bookFound = false
            if (params.bookId != null) {
                if ("noBook".equals(redisService.("bookId_" + params.bookId))) {
                    bookFound = false
                } else {
                    booksMst = dataProviderService.getBooksMst(new Integer(params.bookId))
                    if (booksMst == null) {
                        bookFound = false
                        redisService.("bookId_" + params.bookId) = "noBook"
                    } else {
                        bookFound = true
                    }
                }
                BookPricesPing bookPricesPing = new BookPricesPing(siteId: params.siteId != null ? new Integer(params.siteId) : null, bookIdType: "bookId", bookFound: "" + bookFound, idValue: params.bookId)
                bookPricesPing.save(failOnError: true, flush: true)
            } else if (params.isbn != null) {
                if ("noBook".equals(redisService.("isbn_" + params.isbn))) {
                    bookFound = false
                } else {
                    booksMst = dataProviderService.getBooksMstByIsbn(params.isbn)
                    if (booksMst == null) {
                        bookFound = false
                        redisService.("isbn_" + params.bookId) = "noBook"
                    } else {
                        bookFound = true
                    }
                }
                BookPricesPing bookPricesPing = new BookPricesPing(siteId: params.siteId != null ? new Integer(params.siteId) : null, bookIdType: "isbn", bookFound: "" + bookFound, idValue: params.isbn)
                bookPricesPing.save(failOnError: true, flush: true)
            } else if (params.bookCode != null) {
                if ("noBook".equals(redisService.("bookCode_" + params.bookCode))) {
                    bookFound = false
                } else {
                    booksMst = dataProviderService.getBooksMstByBookCode(params.bookCode)
                    if (booksMst == null) {
                        bookFound = false
                        redisService.("bookCode_" + params.bookCode) = "noBook"
                    } else {
                        bookFound = true
                    }
                }
                BookPricesPing bookPricesPing = new BookPricesPing(siteId: params.siteId != null ? new Integer(params.siteId) : null, bookIdType: "bookCode", bookFound: "" + bookFound, idValue: params.bookCode)
                bookPricesPing.save(failOnError: true, flush: true)
            }

            if (booksMst != null) {
                if (redisService.("bookPriceDetails_" + booksMst.id) == null) bookPriceService.getBookPrices(new Integer("" + booksMst.id))
                json = [hasPrices: "Yes", prices: redisService.("bookPriceDetails_" + booksMst.id)]
            } else {
                json = [hasPrices: "No"]
            }
        }
        response.setHeader('Access-Control-Allow-Origin', '*')
        render json as JSON
    }

    static boolean isValidEmail(String email) {
        // Regular expression pattern for a valid email
        def regex = '^[A-Za-z0-9+_.-]+@(.+)$'

        return email =~ regex
    }

    @Transactional
    def getPublisherBooksWithType(){
        def json
        def isValidRequest = verifySecretKey(params.siteId,params.key)
        if(isValidRequest){
            try {
                int max = 12
                int offset = params.int('offset') ?: 0
                Long publisherId = new Long(params.publisherId)
                String bookType = params.bookType

                if (!publisherId || !bookType) {
                    throw new IllegalArgumentException("Publisher ID and type must be provided.")
                }

                List books = BooksMst.findAllByPublisherIdAndBookType(new Long(params.publisherId), params.bookType,[max: max, offset: offset, sort: "id", order: "desc"])
                println(books)
                json = [books:books,error: false,message: "Books fetched successfully"]
                render json as JSON
            }catch (Exception e) {
                println("Error fetching books: ${e.message}")
                json =  [error:true,message:e.message]
                render json as JSON
            }
        }else{
            json =  [error:true,message:"Invalid Secret key"]
            render json as JSON
        }

    }

    @Secured(['ROLE_GPT_PAGE_MANAGER'])
    def admin(){
        [pageTitle: "GPT Books"]
    }

    @Secured(['ROLE_GPT_PAGE_MANAGER']) @Transactional
    def bookChapters(){
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+params.siteId)
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        [bookTitle:booksMst.title,secretKey:keyValueMst.keyValue]
    }

    @Secured(['ROLE_GPT_PAGE_MANAGER']) @Transactional
    def getGptChapterDetails(){
        def json
        try {
            Long chapterId = new Long(params.chapterId)
            List gptResources = ResourceDtl.findAllByChapterIdAndGptResourceTypeIsNull(chapterId)

            List result = []
            gptResources.each { resource ->
                Long resourceId = resource.id
                List gptContents = GptDefaultCreateLog.findAllByReadingMaterialResId(resourceId)

                result << [
                        gptContents: gptContents
                ]
            }

            json = [data: result, error: false, message: "Data fetched successfully"]
            render json as JSON
        }catch (Exception e) {
            println "Error fetching gpt resources: ${e.message}"
            json =  [error:true,message:e.message]
            render json as JSON
        }
    }

    @Transactional
    def gptContent(){
        def isValidRequest = verifySecretKey(params.siteId,params.key)
        if(isValidRequest){
            String pageTitle = "iBookGPT - Resources"
            String promptType = params.promptType
            switch (promptType) {
                case "gpt_chat":
                    pageTitle = "Chat - iBookGPT"
                    break

                case "giveTest":
                    pageTitle = "Create Question Paper"
                    break

                default:
                    def gptContentInstance = GptDefaultCreateLog.findByReadingMaterialResIdAndPromptType(new Long(params.readingMaterialResId), promptType)
                    if (gptContentInstance != null) {
                        pageTitle = gptContentInstance.promptLabel
                    }
                    break
            }
            [pageTitle:pageTitle]
        }else{
            def json =  [error:true,message:"Invalid Secret key"]
            render json as JSON
        }
    }

    @Transactional
    def chat(){
        def isValidRequest = verifySecretKey(params.siteId,params.key)
        String chapterName = ""
        try {
            Long chapterId = new Long(params.chapterId)
            ChaptersMst chaptersMst = ChaptersMst.findById(chapterId)
            if (chaptersMst !=null){
                chapterName = chaptersMst.name
            }
        }catch(Exception e){
            println(e)
        }

        if(isValidRequest){
            [pageTitle:"Chat - iBookGPT", chapterTitle:chapterName]
        }else{
            def json =  [error:true,message:"Invalid Secret key"]
            render json as JSON
        }
    }

    def verifySecretKey(siteId,key){
        KeyValueMst keyValueMst = KeyValueMst.findByKeyName("secretKey_"+siteId)
        if(keyValueMst!=null&&keyValueMst.keyValue.equals(key)){
            return true
        }
        return false
    }
}
