package com.wonderslate

import com.google.api.client.googleapis.json.GoogleJsonResponseException
import com.google.api.client.http.HttpRequest
import com.google.api.client.http.HttpRequestInitializer
import com.google.api.client.http.javanet.NetHttpTransport
import com.google.api.client.json.jackson.JacksonFactory
import com.google.api.services.youtube.YouTube

import com.google.api.services.youtube.model.ResourceId;
import com.google.api.services.youtube.model.SearchListResponse;
import com.google.api.services.youtube.model.SearchResult;
import com.google.api.services.youtube.model.Thumbnail
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ChannelMst
import com.wonderslate.data.RelatedVideos
import com.wonderslate.data.RelatedVideosNew
import com.wonderslate.data.SiteMst
import com.wonderslate.data.VideoMst;
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import com.google.api.client.util.DateTime
import grails.transaction.Transactional

import java.nio.channels.Channels
import java.text.SimpleDateFormat

class YoutubeController {

    private static final long NUMBER_OF_VIDEOS_RETURNED = 25;
    private static YouTube youtube;
    private static String apiKey = "";
    SpringSecurityService springSecurityService
    DataProviderService dataProviderService
    def redisService

    
    @Secured(['ROLE_BOOK_CREATOR'])
    def index() { }

    @Secured(['ROLE_BOOK_CREATOR'])
    def search(){
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        String apiKeys = siteMst.googleApiKey;
        if(apiKeys.contains(",")){
            String[] apiKeysArray = apiKeys.split(',');
            int randomIndex =  Math.floor(Math.random() * 5)/1;
            apiKey = apiKeysArray[randomIndex];
        }else{
            apiKey = apiKeys;
        }
        List<SearchResult> searchResultList
        try {
            // This object is used to make YouTube Data API requests. The last
            // argument is required, but since we don't need anything
            // initialized when the HttpRequest is initialized, we override
            // the interface and provide a no-op function.
            youtube = new YouTube.Builder(new NetHttpTransport(), new JacksonFactory(), new HttpRequestInitializer() {
                public void initialize(HttpRequest request) throws IOException {
                }
            }).setApplicationName("Wonderslate").build();

                    // Define the API request for retrieving search results.
                    YouTube.Search.List search = youtube.search().list("id,snippet");
                    String queryTerm = "Tushhar kapoor"
                    // Set your developer key from the {{ Google Cloud Console }} for
                    // non-authenticated requests. See:
                    // {{ https://cloud.google.com/console }}
                    search.setKey(apiKey);
                   // search.setQ(queryTerm);
                   search.setChannelId("UCSHLoG-bXj1aVA2T5y8t84A")

                    // Restrict the search results to only include videos. See:
                    // https://developers.google.com/youtube/v3/docs/search/list#type
                    search.setType("video");

                    // To increase efficiency, only retrieve the fields that the
                    // application uses.
                    search.setFields("items(id/kind,id/videoId,snippet/title,snippet/thumbnails/default/url)");
                    search.setMaxResults(NUMBER_OF_VIDEOS_RETURNED);

                    // Call the API and print results.
                    SearchListResponse searchResponse = search.execute();
                     searchResultList = searchResponse.getItems();
            if (searchResultList != null) {
                prettyPrint(searchResultList.iterator(), queryTerm);
            }
        } catch (GoogleJsonResponseException e) {
            System.err.println("There was a service error: " + e.getDetails().getCode() + " : "
                    + e.getDetails().getMessage());
        } catch (IOException e) {
            System.err.println("There was an IO error: " + e.getCause() + " : " + e.getMessage());
        } catch (Throwable t) {
            t.printStackTrace();
        }

        render searchResultList

    }

    @Secured(['ROLE_BOOK_CREATOR'])
   def prettyPrint(Iterator<SearchResult> iteratorSearchResults, String query) {

        System.out.println("\n=============================================================");
        System.out.println(
                "   First " + NUMBER_OF_VIDEOS_RETURNED + " videos for search on \"" + query + "\".");
        System.out.println("=============================================================\n");

        if (!iteratorSearchResults.hasNext()) {
            System.out.println(" There aren't any results for your query.");
        }

        while (iteratorSearchResults.hasNext()) {

            SearchResult singleVideo = iteratorSearchResults.next();
            ResourceId rId = singleVideo.getId();

            // Confirm that the result represents a video. Otherwise, the
            // item will not contain a video ID.
            if (rId.getKind().equals("youtube#video")) {
                Thumbnail thumbnail = singleVideo.getSnippet().getThumbnails().getDefault();

                System.out.println(" Video Id" + rId.getVideoId());
                System.out.println(" Title: " + singleVideo.getSnippet().getTitle());
                System.out.println(" Thumbnail: " + thumbnail.getUrl());
                System.out.println("\n-------------------------------------------------------------\n");
            }
        }
    }
    @Secured(['ROLE_BOOK_CREATOR'])
    def getChannelDetails(){
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        String apiKeys = siteMst.googleApiKey;
        if(apiKeys.contains(",")){
            String[] apiKeysArray = apiKeys.split(',');
            int randomIndex =  Math.floor(Math.random() * 5)/1;
            apiKey = apiKeysArray[randomIndex];
        }else{
            apiKey = apiKeys;
        }
        List<SearchResult> searchResultList
        try {
            // This object is used to make YouTube Data API requests. The last
            // argument is required, but since we don't need anything
            // initialized when the HttpRequest is initialized, we override
            // the interface and provide a no-op function.
            youtube = new YouTube.Builder(new NetHttpTransport(), new JacksonFactory(), new HttpRequestInitializer() {
                public void initialize(HttpRequest request) throws IOException {
                }
            }).setApplicationName("Wonderslate").build();

            // Define the API request for retrieving search results.
            YouTube.Channels.List search = youtube.channels().list("id,snippet");
            String queryTerm = ""
            // Set your developer key from the {{ Google Cloud Console }} for
            // non-authenticated requests. See:
            // {{ https://cloud.google.com/console }}

            search.setKey(apiKey);
            // search.setQ(queryTerm);
            search.setId(params.channelId)



            // To increase efficiency, only retrieve the fields that the
            // application uses.
            search.setFields("items(snippet/title,snippet/description,snippet/thumbnails/default/url,snippet/customUrl)");
            search.setMaxResults(NUMBER_OF_VIDEOS_RETURNED);

            // Call the API and print results.
            SearchListResponse searchResponse = search.execute();
            searchResultList = searchResponse.getItems();

        } catch (GoogleJsonResponseException e) {
            System.err.println("There was a service error: " + e.getDetails().getCode() + " : "
                    + e.getDetails().getMessage());
        } catch (IOException e) {
            System.err.println("There was an IO error: " + e.getCause() + " : " + e.getMessage());
        } catch (Throwable t) {
            t.printStackTrace();
        }

        def json = [status: searchResultList?"OK":"Fail", searchResultList: searchResultList]
        render json as JSON
    }

    @Transactional
    def addChannelDetails(){
        def status = "Already present";
     if(ChannelMst.findByLanguageAndChannelId(params.channelLanguage,params.channelId)==null){
         ChannelMst channelMst = new ChannelMst(name: params.channelName, language: params.channelLanguage, channelType:params.channelType, channelId: params.channelId, description: params.channelDescription )
         channelMst.save(failOnError: true, flush: true)
         status ="saved"
     }
        def json = [status: status]
        render json as JSON
    }
    @Transactional
    def getLatestVideosOfAllChannels(){
       List channels = ChannelMst.findAllByLanguage(params.language)
        println("number of channels returned="+channels.size())

        List videosList = []
        channels.each{ channel ->
             List channelVideos = getChannelVideos(channel.channelId,Integer.parseInt(params.numberOfDays))
            channelVideos.each { channelVideo ->
                 if(dataProviderService.getVideoMst(channelVideo.id.videoId)==null)
                videosList.push(channelVideo)

            }
        }

        def json = ['allVideos':videosList]
        render json as JSON
    }

    def addVideosForLanguage(){
        def status = "Already present";
        // check if video is already added
      if(dataProviderService.getVideoMst(params.videoId)==null){
          VideoMst videoMst = new VideoMst(videoId: params.videoId,title: params.title, channelId: params.channelId,language: params.language, videoType: params.videoType, createdBy: springSecurityService.currentUser.username)
          videoMst.save(failOnError: true, flush: true)
          dataProviderService.getVideos(params.language)

          status ="saved"
      }
        def json = [status: status]
        render json as JSON
    }

    @Transactional
    def getChannelVideosForSite(){
        SiteMst siteMst = dataProviderService.getSiteMst(new Long(params.siteId))
        int noOfDays = 1;
        if(params.noOfDays!=null) noOfDays = Integer.parseInt(params.noOfDays)
        if(siteMst.channelId!=null){
            List allVideos = getChannelVideos(siteMst.channelId,noOfDays)
            allVideos.each{ video ->
                 if(dataProviderService.getVideoMst(video.id.videoId)==null){
                    VideoMst videoMst = new VideoMst(videoId: video.id.videoId,title: video.snippet.title, channelId: siteMst.channelId,language: "English", videoType: "Channel Video", createdBy: "system")
                    videoMst.save(failOnError: true, flush: true)

                }
            }
            dataProviderService.getChannelVideos(siteMst.channelId)
        }
        render "done"
    }

    @Transactional
    def getLatestVideosForChannel(){
        SiteMst siteMst = dataProviderService.getSiteMst(new Long(params.siteId))
        if( redisService.("channelVideos_"+siteMst.channelId)==null)
                dataProviderService.getChannelVideos(siteMst.channelId)
        def json = ['allVideos':redisService.("channelVideos_"+siteMst.channelId)]
        render json as JSON
    }

    List<SearchResult> getChannelVideos(channelId,numberOfDays){
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        String apiKeys = siteMst.googleApiKey;
        if(apiKeys.contains(",")){
            String[] apiKeysArray = apiKeys.split(',');
            int randomIndex =  Math.floor(Math.random() * 5)/1;
            apiKey = apiKeysArray[randomIndex];
        }else{
            apiKey = apiKeys;
        }

        List<SearchResult> searchResultList
        try {
            // This object is used to make YouTube Data API requests. The last
            // argument is required, but since we don't need anything
            // initialized when the HttpRequest is initialized, we override
            // the interface and provide a no-op function.
            youtube = new YouTube.Builder(new NetHttpTransport(), new JacksonFactory(), new HttpRequestInitializer() {
                public void initialize(HttpRequest request) throws IOException {
                }
            }).setApplicationName("Wonderslate").build();

            // Define the API request for retrieving search results.
            YouTube.Search.List search = youtube.search().list("id,snippet");

            search.setKey(apiKey);
            // number of days earlier
            if(numberOfDays>0){
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, (-1*numberOfDays));
                DateTime sendingDate = new DateTime(calendar.getTime())
                println("the date thingy is "+sendingDate);
                search.setPublishedAfter(sendingDate)

            }

            search.setChannelId(channelId)

            // Restrict the search results to only include videos. See:
            // https://developers.google.com/youtube/v3/docs/search/list#type
            search.setType("video");

            // To increase efficiency, only retrieve the fields that the
            // application uses.
            search.setFields("items(id/kind,id/videoId,snippet/title,snippet/thumbnails/default/url,snippet/channelId)");
            search.setMaxResults(NUMBER_OF_VIDEOS_RETURNED);

            // Call the API and print results.
            SearchListResponse searchResponse = search.execute();
            searchResultList = searchResponse.getItems();

        } catch (GoogleJsonResponseException e) {
            System.err.println("There was a service error: " + e.getDetails().getCode() + " : "
                    + e.getDetails().getMessage());
        } catch (IOException e) {
            System.err.println("There was an IO error: " + e.getCause() + " : " + e.getMessage());
        } catch (Throwable t) {
            t.printStackTrace();
        }

        return searchResultList

    }

    def getRhymeLanguages(){
        if(redisService.("rhymelanguages")==null){
            dataProviderService.getRhymeLanguages()
        }

        def json = [languages: redisService.("rhymelanguages")]
        render json as JSON
    }

    def getVideos(){
        StringTokenizer languages = new StringTokenizer(params.languages, ",")
        String language
        def videos=[]
        while (languages.hasMoreTokens()) {
            language = (String)languages.nextToken()

            if(language!=null) {
                if(redisService.("languagevideos_"+language)==null){
                    dataProviderService.getVideos(language)
                }
                videos.push(dataProviderService.getVideos(language))
            }
        }

        def json = [videos: videos]
        render json as JSON
    }

    @Transactional
    def channelAttribution(){
        String channelType = params.channelType!=null?params.channelType:"Kids"
        List channels = ChannelMst.findAllByChannelType(channelType,[sort: "name"])

        [channels:channels]


    }

    @Transactional
    def getRelatedVideos(){
        SiteMst siteMst = dataProviderService.getSiteMst(getSiteId(request))
        String apiKeys = siteMst.googleApiKey;
        boolean exceptionHappened = false
        if(apiKeys.contains(",")){
            String[] apiKeysArray = apiKeys.split(',');
            int randomIndex =  Math.floor(Math.random() * 5)/1;
            apiKey = apiKeysArray[randomIndex];
        }else{
            apiKey = apiKeys;
        }
        List<SearchResult> searchResultList
        RelatedVideos relatedVideos = RelatedVideos.findByChapterId(new Integer(params.chapterId))

        try {

            if(relatedVideos==null) {
                youtube = new YouTube.Builder(new NetHttpTransport(), new JacksonFactory(), new HttpRequestInitializer() {
                    public void initialize(HttpRequest request) throws IOException {
                    }
                }).setApplicationName("YoutubeSearch").build();
                   // Define the API request for retrieving search results.
                YouTube.Search.List search = youtube.search().list("id,snippet");

                search.setKey(apiKey);

                search.setQ(params.query)


                // Restrict the search results to only include videos. See:
                // https://developers.google.com/youtube/v3/docs/search/list#type
                search.setType("video");

                // To increase efficiency, only retrieve the fields that the
                // application uses.
                search.setFields("items(id/kind,id/videoId,snippet/title)");
                search.setMaxResults(NUMBER_OF_VIDEOS_RETURNED);

                // Call the API and print results.
                SearchListResponse searchResponse = search.execute();
                searchResultList = searchResponse.getItems();
                Gson gson = new Gson();
                String element = gson.toJson(searchResultList,new TypeToken<List>() {}.getType())
                relatedVideos = new RelatedVideos(chapterId: new Integer(params.chapterId), videos: element.getBytes("UTF-8"))
                relatedVideos.save(failOnError: true, flush: true)
            }


        } catch (GoogleJsonResponseException e) {
            System.err.println("There was a service error: " + e.getDetails().getCode() + " : "
                    + e.getDetails().getMessage());
            exceptionHappened = true
        } catch (IOException e) {
            System.err.println("There was an IO error: " + e.getCause() + " : " + e.getMessage());
            exceptionHappened = true
        } catch (Throwable t) {
            t.printStackTrace();
            exceptionHappened = true
        }
        if(exceptionHappened){
            def json = ['exceptionHappened': "true"]
            render json as JSON
        }
        else {
            def json = ['searchResultList': new String(relatedVideos.videos, "UTF-8"),'exceptionHappened': "false"]
            render json as JSON
        }

    }

    def getRelatedVideosFromDB(){

        boolean exceptionHappened = true
        List relatedVideos = RelatedVideosNew.findAllByChapterId(new Integer(params.chapterId))
        def json = ['searchResultList': relatedVideos.size()>0?relatedVideos:null,'exceptionHappened': relatedVideos.size()>0?"false":"true"]
        render json as JSON


    }

      Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }

    def updateYoutubeLink(){
        redisService.("youtubeStreamingUrl_"+params.resId)=params.youtubeUrl
        def json = ['status':'success']
        render json as JSON
    }

    def getYoutubeLink(){
        String youtubeUrl  = redisService.("youtubeStreamingUrl_"+params.resId)
        def json = [status:youtubeUrl!=null?"success":"fail",youtubeUrl:youtubeUrl]
        render json as JSON

    }


}
