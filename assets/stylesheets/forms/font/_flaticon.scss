    /*
    Flaticon icon font: Flaticon
    Creation date: 13/07/2018 09:39
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-rupee:before { content: "\f100"; }
.flaticon-add-user:before { content: "\f101"; }
.flaticon-mic:before { content: "\f102"; }
.flaticon-accept-circular-button-outline:before { content: "\f103"; }
.flaticon-personal-profile:before { content: "\f104"; }
.flaticon-boxes:before { content: "\f105"; }
.flaticon-route:before { content: "\f106"; }
.flaticon-marker:before { content: "\f107"; }
.flaticon-user:before { content: "\f108"; }
.flaticon-briefcase:before { content: "\f109"; }
    
    $font-Flaticon-rupee: "\f100";
    $font-Flaticon-add-user: "\f101";
    $font-Flaticon-mic: "\f102";
    $font-Flaticon-accept-circular-button-outline: "\f103";
    $font-Flaticon-personal-profile: "\f104";
    $font-Flaticon-boxes: "\f105";
    $font-Flaticon-route: "\f106";
    $font-Flaticon-marker: "\f107";
    $font-Flaticon-user: "\f108";
    $font-Flaticon-briefcase: "\f109";