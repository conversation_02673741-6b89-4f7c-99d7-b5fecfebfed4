#applnType_error,#appintype_error,#appinstate_error,#appindistr_error,#bnm_error,#pan_card_error,#ba2_error,#ba_error,#trnm_error,#chrs_error{
    border: 1px solid red;
}
#new-registration .form-control + span ,.input-group + span{
    color: red;
    border: none;
    display: none;
}
#trn-registration .form-control + span{
    color: red;
    border: none;
    display: none;
}
.has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline {
    color: #a94442;
}
 p.wrong {
        display: none;
    }
    
    p.wrong.shake {
        display: block;
    }
    
    p.wrong.shake {
        animation: shake .4s cubic-bezier(.36, .07, .19, .97) both;
        transform: translate3d(0, 0, 0);
        backface-visibility: hidden;
        perspective: 1000px;
    }
    
    @keyframes shake {
        10%,
        90% {
            transform: translate3d(-1px, 0, 0);
        }
        20%,
        80% {
            transform: translate3d(1px, 0, 0);
        }
        30%,
        50%,
        70% {
            transform: translate3d(-2px, 0, 0);
        }
        40%,
        60% {
            transform: translate3d(2px, 0, 0);
        }
    }
    
    .controls img {
        height: 20px;
    }
    #captcha{
        display: flex;
        margin-top: 10px;
    }
    #captcha .controls{
        display: flex;
    position: relative;
    top: -5px;
    left: 10px;
    align-items: flex-end;
    }
    #success_message{display: none;}
    .select-wrapper.form-control{
        padding: initial;
        border: initial;

    }
    .browser-default{
        display: none;
    }