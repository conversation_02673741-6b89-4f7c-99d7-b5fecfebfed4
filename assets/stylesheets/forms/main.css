/*!
 * Bootstrap v3.3.6 (http://getbootstrap.com)
 * Copyright 2011-2015 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */


/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */

html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
    display: block
}

audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline
}

audio:not([controls]) {
    display: none;
    height: 0
}

[hidden],
template {
    display: none
}

a {
    background-color: transparent
}

a:active,
a:hover {
    outline: 0
}

abbr[title] {
    border-bottom: 1px dotted
}

b,
strong {
    font-weight: bold
}

dfn {
    font-style: italic
}

h1 {
    font-size: 2em;
    margin: 0.67em 0
}

mark {
    background: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sup {
    top: -0.5em
}

sub {
    bottom: -0.25em
}

img {
    border: 0
}

svg:not(:root) {
    overflow: hidden
}

figure {
    margin: 1em 40px
}

hr {
    box-sizing: content-box;
    height: 0
}

pre {
    overflow: auto
}

code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font: inherit;
    margin: 0
}

button {
    overflow: visible
}

button,
select {
    text-transform: none
}

button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer
}

button[disabled],
html input[disabled] {
    cursor: default
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0
}

input {
    line-height: normal
}

input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;
    padding: 0
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    height: auto
}

input[type="search"] {
    -webkit-appearance: textfield;
    box-sizing: content-box
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none
}

fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em
}

legend {
    border: 0;
    padding: 0
}

textarea {
    overflow: auto
}

optgroup {
    font-weight: bold
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

td,
th {
    padding: 0
}


/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */

@media print {
    *,
    *:before,
    *:after {
        background: transparent !important;
        color: #000 !important;
        box-shadow: none !important;
        text-shadow: none !important
    }
    a,
    a:visited {
        text-decoration: underline
    }
    a[href]:after {
        content: " (" attr(href) ")"
    }
    abbr[title]:after {
        content: " (" attr(title) ")"
    }
    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: ""
    }
    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid
    }
    thead {
        display: table-header-group
    }
    tr,
    img {
        page-break-inside: avoid
    }
    img {
        max-width: 100% !important
    }
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3
    }
    h2,
    h3 {
        page-break-after: avoid
    }
    .navbar {
        display: none
    }
    .btn>.caret,
    .dropup>.btn>.caret {
        border-top-color: #000 !important
    }
    .label {
        border: 1px solid #000
    }
    .table {
        border-collapse: collapse !important
    }
    .table td,
    .table th {
        background-color: #fff !important
    }
    .table-bordered th,
    .table-bordered td {
        border: 1px solid #ddd !important
    }
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

*:before,
*:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

html {
    font-size: 10px;
    -webkit-tap-highlight-color: transparent
}

body {
    font-family: verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    background-color: #fff;
    line-height: 1.428571429;
    color: #212121
}

@media screen and (max-width: 480px) {
    body {
        font-size: 12px;
        background: #fff !important
    }
}

input,
button,
select,
textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

a {
    color: #337ab7;
    text-decoration: none;
    cursor: pointer
}

a:hover,
a:focus {
    color: #23527c;
    text-decoration: underline;
    cursor: pointer
}

figure {
    margin: 0
}

img {
    vertical-align: middle
}

.img-responsive {
    display: block;
    max-width: 100%;
    height: auto
}

.img-rounded {
    border-radius: 0px
}

.img-thumbnail {
    padding: 4px;
    line-height: 1.428571429;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 0px;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    display: inline-block;
    max-width: 100%;
    height: auto
}

.img-circle {
    border-radius: 50%
}

hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}

[role="button"] {
    cursor: pointer
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit
}

h1 small,
h1 .small,
h2 small,
h2 .small,
h3 small,
h3 .small,
h4 small,
h4 .small,
h5 small,
h5 .small,
h6 small,
h6 .small,
.h1 small,
.h1 .small,
.h2 small,
.h2 .small,
.h3 small,
.h3 .small,
.h4 small,
.h4 .small,
.h5 small,
.h5 .small,
.h6 small,
.h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #777
}

h1,
.h1,
h2,
.h2,
h3,
.h3 {
    margin-top: 20px;
    margin-bottom: 10px
}

h1 small,
h1 .small,
.h1 small,
.h1 .small,
h2 small,
h2 .small,
.h2 small,
.h2 .small,
h3 small,
h3 .small,
.h3 small,
.h3 .small {
    font-size: 65%
}

h4,
.h4,
h5,
.h5,
h6,
.h6 {
    margin-top: 10px;
    margin-bottom: 10px
}

h4 small,
h4 .small,
.h4 small,
.h4 .small,
h5 small,
h5 .small,
.h5 small,
.h5 .small,
h6 small,
h6 .small,
.h6 small,
.h6 .small {
    font-size: 75%
}

h1,
.h1 {
    font-size: 36px
}

h2,
.h2 {
    font-size: 30px
}

h3,
.h3 {
    font-size: 24px
}

h4,
.h4 {
    font-size: 18px
}

h5,
.h5 {
    font-size: 14px
}

h6,
.h6 {
    font-size: 12px
}

p {
    margin: 0 0 10px
}

.lead {
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 300;
    line-height: 1.4
}

@media (min-width: 768px) {
    .lead {
        font-size: 21px
    }
}

small,
.small {
    font-size: 85%
}

mark,
.mark {
    background-color: #fcf8e3;
    padding: .2em
}

.text-left {
    text-align: left
}

.text-right {
    text-align: right
}

.text-center {
    text-align: center
}

.text-justify {
    text-align: justify
}

.text-nowrap {
    white-space: nowrap
}

.text-lowercase {
    text-transform: lowercase
}

.text-uppercase,
.initialism {
    text-transform: uppercase
}

.text-capitalize {
    text-transform: capitalize
}

.text-muted {
    color: #777
}

.text-primary {
    color: #337ab7
}

a.text-primary:hover,
a.text-primary:focus {
    color: #286090
}

.text-success {
    color: #3c763d
}

a.text-success:hover,
a.text-success:focus {
    color: #2b542c
}

.text-info {
    color: #31708f
}

a.text-info:hover,
a.text-info:focus {
    color: #245269
}

.text-warning {
    color: #8a6d3b
}

a.text-warning:hover,
a.text-warning:focus {
    color: #66512c
}

.text-danger {
    color: #a94442
}

a.text-danger:hover,
a.text-danger:focus {
    color: #843534
}

.bg-primary {
    color: #fff
}

.bg-primary {
    background-color: #337ab7
}

a.bg-primary:hover,
a.bg-primary:focus {
    background-color: #286090
}

.bg-success {
    background-color: #dff0d8
}

a.bg-success:hover,
a.bg-success:focus {
    background-color: #c1e2b3
}

.bg-info {
    background-color: #d9edf7
}

a.bg-info:hover,
a.bg-info:focus {
    background-color: #afd9ee
}

.bg-warning {
    background-color: #fcf8e3
}

a.bg-warning:hover,
a.bg-warning:focus {
    background-color: #f7ecb5
}

.bg-danger {
    background-color: #f2dede
}

a.bg-danger:hover,
a.bg-danger:focus {
    background-color: #e4b9b9
}

.page-header {
    padding-bottom: 9px;
    margin: 40px 0 20px;
    border-bottom: 1px solid #eee
}

ul,
ol {
    margin-top: 0;
    margin-bottom: 10px
}

ul ul,
ul ol,
ol ul,
ol ol {
    margin-bottom: 0
}

.list-unstyled {
    padding-left: 0;
    list-style: none
}

.list-inline {
    padding-left: 0;
    list-style: none;
    margin-left: -5px
}

.list-inline>li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px
}

dl {
    margin-top: 0;
    margin-bottom: 20px
}

dt,
dd {
    line-height: 1.428571429
}

dt {
    font-weight: bold
}

dd {
    margin-left: 0
}

.dl-horizontal dd:before,
.dl-horizontal dd:after {
    content: " ";
    display: table
}

.dl-horizontal dd:after {
    clear: both
}

@media (min-width: 768px) {
    .dl-horizontal dt {
        float: left;
        width: 160px;
        clear: left;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap
    }
    .dl-horizontal dd {
        margin-left: 180px
    }
}

abbr[title],
abbr[data-original-title] {
    cursor: help;
    border-bottom: 1px dotted #777
}

.initialism {
    font-size: 90%
}

blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee
}

blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
    margin-bottom: 0
}

blockquote footer,
blockquote small,
blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.428571429;
    color: #777
}

blockquote footer:before,
blockquote small:before,
blockquote .small:before {
    content: '\2014 \00A0'
}

.blockquote-reverse,
blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    border-right: 5px solid #eee;
    border-left: 0;
    text-align: right
}

.blockquote-reverse footer:before,
.blockquote-reverse small:before,
.blockquote-reverse .small:before,
blockquote.pull-right footer:before,
blockquote.pull-right small:before,
blockquote.pull-right .small:before {
    content: ''
}

.blockquote-reverse footer:after,
.blockquote-reverse small:after,
.blockquote-reverse .small:after,
blockquote.pull-right footer:after,
blockquote.pull-right small:after,
blockquote.pull-right .small:after {
    content: '\00A0 \2014'
}

address {
    margin-bottom: 20px;
    font-style: normal;
    line-height: 1.428571429
}

code,
kbd,
pre,
samp {
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace
}

code {
    padding: 2px 4px;
    font-size: 90%;
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 0px
}

kbd {
    padding: 2px 4px;
    font-size: 90%;
    color: #fff;
    background-color: #333;
    border-radius: 0px;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25)
}

kbd kbd {
    padding: 0;
    font-size: 100%;
    font-weight: bold;
    box-shadow: none
}

pre {
    display: block;
    padding: 9.5px;
    margin: 0 0 10px;
    font-size: 13px;
    line-height: 1.428571429;
    word-break: break-all;
    word-wrap: break-word;
    color: #333;
    background-color: #f5f5f5;
    border: 1px solid #ccc;
    border-radius: 0px
}

pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0
}

.pre-scrollable {
    max-height: 340px;
    overflow-y: scroll
}

.container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}

.container:before,
.container:after {
    content: " ";
    display: table
}

.container:after {
    clear: both
}

@media (min-width: 768px) {
    .container {
        width: 750px
    }
}

@media (min-width: 992px) {
    .container {
        width: 970px
    }
}

@media (min-width: 1200px) {
    .container {
        width: 1170px
    }
}

.container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px
}

.container-fluid:before,
.container-fluid:after {
    content: " ";
    display: table
}

.container-fluid:after {
    clear: both
}

.row {
    margin-left: -15px;
    margin-right: -15px
}

.row:before,
.row:after {
    content: " ";
    display: table
}

.row:after {
    clear: both
}

.col-xs-1,
.col-sm-1,
.col-md-1,
.col-lg-1,
.col-xs-2,
.col-sm-2,
.col-md-2,
.col-lg-2,
.col-xs-3,
.col-sm-3,
.col-md-3,
.col-lg-3,
.col-xs-4,
.col-sm-4,
.col-md-4,
.col-lg-4,
.col-xs-5,
.col-sm-5,
.col-md-5,
.col-lg-5,
.col-xs-6,
.col-sm-6,
.col-md-6,
.col-lg-6,
.col-xs-7,
.col-sm-7,
.col-md-7,
.col-lg-7,
.col-xs-8,
.col-sm-8,
.col-md-8,
.col-lg-8,
.col-xs-9,
.col-sm-9,
.col-md-9,
.col-lg-9,
.col-xs-10,
.col-sm-10,
.col-md-10,
.col-lg-10,
.col-xs-11,
.col-sm-11,
.col-md-11,
.col-lg-11,
.col-xs-12,
.col-sm-12,
.col-md-12,
.col-lg-12 {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px
}

.col-xs-1,
.col-xs-2,
.col-xs-3,
.col-xs-4,
.col-xs-5,
.col-xs-6,
.col-xs-7,
.col-xs-8,
.col-xs-9,
.col-xs-10,
.col-xs-11,
.col-xs-12 {
    float: left
}

.col-xs-1 {
    width: 8.3333333333%
}

.col-xs-2 {
    width: 16.6666666667%
}

.col-xs-3 {
    width: 25%
}

.col-xs-4 {
    width: 33.3333333333%
}

.col-xs-5 {
    width: 41.6666666667%
}

.col-xs-6 {
    width: 50%
}

.col-xs-7 {
    width: 58.3333333333%
}

.col-xs-8 {
    width: 66.6666666667%
}

.col-xs-9 {
    width: 75%
}

.col-xs-10 {
    width: 83.3333333333%
}

.col-xs-11 {
    width: 91.6666666667%
}

.col-xs-12 {
    width: 100%
}

.col-xs-pull-0 {
    right: auto
}

.col-xs-pull-1 {
    right: 8.3333333333%
}

.col-xs-pull-2 {
    right: 16.6666666667%
}

.col-xs-pull-3 {
    right: 25%
}

.col-xs-pull-4 {
    right: 33.3333333333%
}

.col-xs-pull-5 {
    right: 41.6666666667%
}

.col-xs-pull-6 {
    right: 50%
}

.col-xs-pull-7 {
    right: 58.3333333333%
}

.col-xs-pull-8 {
    right: 66.6666666667%
}

.col-xs-pull-9 {
    right: 75%
}

.col-xs-pull-10 {
    right: 83.3333333333%
}

.col-xs-pull-11 {
    right: 91.6666666667%
}

.col-xs-pull-12 {
    right: 100%
}

.col-xs-push-0 {
    left: auto
}

.col-xs-push-1 {
    left: 8.3333333333%
}

.col-xs-push-2 {
    left: 16.6666666667%
}

.col-xs-push-3 {
    left: 25%
}

.col-xs-push-4 {
    left: 33.3333333333%
}

.col-xs-push-5 {
    left: 41.6666666667%
}

.col-xs-push-6 {
    left: 50%
}

.col-xs-push-7 {
    left: 58.3333333333%
}

.col-xs-push-8 {
    left: 66.6666666667%
}

.col-xs-push-9 {
    left: 75%
}

.col-xs-push-10 {
    left: 83.3333333333%
}

.col-xs-push-11 {
    left: 91.6666666667%
}

.col-xs-push-12 {
    left: 100%
}

.col-xs-offset-0 {
    margin-left: 0%
}

.col-xs-offset-1 {
    margin-left: 8.3333333333%
}

.col-xs-offset-2 {
    margin-left: 16.6666666667%
}

.col-xs-offset-3 {
    margin-left: 25%
}

.col-xs-offset-4 {
    margin-left: 33.3333333333%
}

.col-xs-offset-5 {
    margin-left: 41.6666666667%
}

.col-xs-offset-6 {
    margin-left: 50%
}

.col-xs-offset-7 {
    margin-left: 58.3333333333%
}

.col-xs-offset-8 {
    margin-left: 66.6666666667%
}

.col-xs-offset-9 {
    margin-left: 75%
}

.col-xs-offset-10 {
    margin-left: 83.3333333333%
}

.col-xs-offset-11 {
    margin-left: 91.6666666667%
}

.col-xs-offset-12 {
    margin-left: 100%
}

@media (min-width: 768px) {
    .col-sm-1,
    .col-sm-2,
    .col-sm-3,
    .col-sm-4,
    .col-sm-5,
    .col-sm-6,
    .col-sm-7,
    .col-sm-8,
    .col-sm-9,
    .col-sm-10,
    .col-sm-11,
    .col-sm-12 {
        float: left
    }
    .col-sm-1 {
        width: 8.3333333333%
    }
    .col-sm-2 {
        width: 16.6666666667%
    }
    .col-sm-3 {
        width: 25%
    }
    .col-sm-4 {
        width: 33.3333333333%
    }
    .col-sm-5 {
        width: 41.6666666667%
    }
    .col-sm-6 {
        width: 50%
    }
    .col-sm-7 {
        width: 58.3333333333%
    }
    .col-sm-8 {
        width: 66.6666666667%
    }
    .col-sm-9 {
        width: 75%
    }
    .col-sm-10 {
        width: 83.3333333333%
    }
    .col-sm-11 {
        width: 91.6666666667%
    }
    .col-sm-12 {
        width: 100%
    }
    .col-sm-pull-0 {
        right: auto
    }
    .col-sm-pull-1 {
        right: 8.3333333333%
    }
    .col-sm-pull-2 {
        right: 16.6666666667%
    }
    .col-sm-pull-3 {
        right: 25%
    }
    .col-sm-pull-4 {
        right: 33.3333333333%
    }
    .col-sm-pull-5 {
        right: 41.6666666667%
    }
    .col-sm-pull-6 {
        right: 50%
    }
    .col-sm-pull-7 {
        right: 58.3333333333%
    }
    .col-sm-pull-8 {
        right: 66.6666666667%
    }
    .col-sm-pull-9 {
        right: 75%
    }
    .col-sm-pull-10 {
        right: 83.3333333333%
    }
    .col-sm-pull-11 {
        right: 91.6666666667%
    }
    .col-sm-pull-12 {
        right: 100%
    }
    .col-sm-push-0 {
        left: auto
    }
    .col-sm-push-1 {
        left: 8.3333333333%
    }
    .col-sm-push-2 {
        left: 16.6666666667%
    }
    .col-sm-push-3 {
        left: 25%
    }
    .col-sm-push-4 {
        left: 33.3333333333%
    }
    .col-sm-push-5 {
        left: 41.6666666667%
    }
    .col-sm-push-6 {
        left: 50%
    }
    .col-sm-push-7 {
        left: 58.3333333333%
    }
    .col-sm-push-8 {
        left: 66.6666666667%
    }
    .col-sm-push-9 {
        left: 75%
    }
    .col-sm-push-10 {
        left: 83.3333333333%
    }
    .col-sm-push-11 {
        left: 91.6666666667%
    }
    .col-sm-push-12 {
        left: 100%
    }
    .col-sm-offset-0 {
        margin-left: 0%
    }
    .col-sm-offset-1 {
        margin-left: 8.3333333333%
    }
    .col-sm-offset-2 {
        margin-left: 16.6666666667%
    }
    .col-sm-offset-3 {
        margin-left: 25%
    }
    .col-sm-offset-4 {
        margin-left: 33.3333333333%
    }
    .col-sm-offset-5 {
        margin-left: 41.6666666667%
    }
    .col-sm-offset-6 {
        margin-left: 50%
    }
    .col-sm-offset-7 {
        margin-left: 58.3333333333%
    }
    .col-sm-offset-8 {
        margin-left: 66.6666666667%
    }
    .col-sm-offset-9 {
        margin-left: 75%
    }
    .col-sm-offset-10 {
        margin-left: 83.3333333333%
    }
    .col-sm-offset-11 {
        margin-left: 91.6666666667%
    }
    .col-sm-offset-12 {
        margin-left: 100%
    }
}

@media (min-width: 992px) {
    .col-md-1,
    .col-md-2,
    .col-md-3,
    .col-md-4,
    .col-md-5,
    .col-md-6,
    .col-md-7,
    .col-md-8,
    .col-md-9,
    .col-md-10,
    .col-md-11,
    .col-md-12 {
        float: left
    }
    .col-md-1 {
        width: 8.3333333333%
    }
    .col-md-2 {
        width: 16.6666666667%
    }
    .col-md-3 {
        width: 25%
    }
    .col-md-4 {
        width: 33.3333333333%
    }
    .col-md-5 {
        width: 41.6666666667%
    }
    .col-md-6 {
        width: 50%
    }
    .col-md-7 {
        width: 58.3333333333%
    }
    .col-md-8 {
        width: 66.6666666667%
    }
    .col-md-9 {
        width: 75%
    }
    .col-md-10 {
        width: 83.3333333333%
    }
    .col-md-11 {
        width: 91.6666666667%
    }
    .col-md-12 {
        width: 100%
    }
    .col-md-pull-0 {
        right: auto
    }
    .col-md-pull-1 {
        right: 8.3333333333%
    }
    .col-md-pull-2 {
        right: 16.6666666667%
    }
    .col-md-pull-3 {
        right: 25%
    }
    .col-md-pull-4 {
        right: 33.3333333333%
    }
    .col-md-pull-5 {
        right: 41.6666666667%
    }
    .col-md-pull-6 {
        right: 50%
    }
    .col-md-pull-7 {
        right: 58.3333333333%
    }
    .col-md-pull-8 {
        right: 66.6666666667%
    }
    .col-md-pull-9 {
        right: 75%
    }
    .col-md-pull-10 {
        right: 83.3333333333%
    }
    .col-md-pull-11 {
        right: 91.6666666667%
    }
    .col-md-pull-12 {
        right: 100%
    }
    .col-md-push-0 {
        left: auto
    }
    .col-md-push-1 {
        left: 8.3333333333%
    }
    .col-md-push-2 {
        left: 16.6666666667%
    }
    .col-md-push-3 {
        left: 25%
    }
    .col-md-push-4 {
        left: 33.3333333333%
    }
    .col-md-push-5 {
        left: 41.6666666667%
    }
    .col-md-push-6 {
        left: 50%
    }
    .col-md-push-7 {
        left: 58.3333333333%
    }
    .col-md-push-8 {
        left: 66.6666666667%
    }
    .col-md-push-9 {
        left: 75%
    }
    .col-md-push-10 {
        left: 83.3333333333%
    }
    .col-md-push-11 {
        left: 91.6666666667%
    }
    .col-md-push-12 {
        left: 100%
    }
    .col-md-offset-0 {
        margin-left: 0%
    }
    .col-md-offset-1 {
        margin-left: 8.3333333333%
    }
    .col-md-offset-2 {
        margin-left: 16.6666666667%
    }
    .col-md-offset-3 {
        margin-left: 25%
    }
    .col-md-offset-4 {
        margin-left: 33.3333333333%
    }
    .col-md-offset-5 {
        margin-left: 41.6666666667%
    }
    .col-md-offset-6 {
        margin-left: 50%
    }
    .col-md-offset-7 {
        margin-left: 58.3333333333%
    }
    .col-md-offset-8 {
        margin-left: 66.6666666667%
    }
    .col-md-offset-9 {
        margin-left: 75%
    }
    .col-md-offset-10 {
        margin-left: 83.3333333333%
    }
    .col-md-offset-11 {
        margin-left: 91.6666666667%
    }
    .col-md-offset-12 {
        margin-left: 100%
    }
}

@media (min-width: 1200px) {
    .col-lg-1,
    .col-lg-2,
    .col-lg-3,
    .col-lg-4,
    .col-lg-5,
    .col-lg-6,
    .col-lg-7,
    .col-lg-8,
    .col-lg-9,
    .col-lg-10,
    .col-lg-11,
    .col-lg-12 {
        float: left
    }
    .col-lg-1 {
        width: 8.3333333333%
    }
    .col-lg-2 {
        width: 16.6666666667%
    }
    .col-lg-3 {
        width: 25%
    }
    .col-lg-4 {
        width: 33.3333333333%
    }
    .col-lg-5 {
        width: 41.6666666667%
    }
    .col-lg-6 {
        width: 50%
    }
    .col-lg-7 {
        width: 58.3333333333%
    }
    .col-lg-8 {
        width: 66.6666666667%
    }
    .col-lg-9 {
        width: 75%
    }
    .col-lg-10 {
        width: 83.3333333333%
    }
    .col-lg-11 {
        width: 91.6666666667%
    }
    .col-lg-12 {
        width: 100%
    }
    .col-lg-pull-0 {
        right: auto
    }
    .col-lg-pull-1 {
        right: 8.3333333333%
    }
    .col-lg-pull-2 {
        right: 16.6666666667%
    }
    .col-lg-pull-3 {
        right: 25%
    }
    .col-lg-pull-4 {
        right: 33.3333333333%
    }
    .col-lg-pull-5 {
        right: 41.6666666667%
    }
    .col-lg-pull-6 {
        right: 50%
    }
    .col-lg-pull-7 {
        right: 58.3333333333%
    }
    .col-lg-pull-8 {
        right: 66.6666666667%
    }
    .col-lg-pull-9 {
        right: 75%
    }
    .col-lg-pull-10 {
        right: 83.3333333333%
    }
    .col-lg-pull-11 {
        right: 91.6666666667%
    }
    .col-lg-pull-12 {
        right: 100%
    }
    .col-lg-push-0 {
        left: auto
    }
    .col-lg-push-1 {
        left: 8.3333333333%
    }
    .col-lg-push-2 {
        left: 16.6666666667%
    }
    .col-lg-push-3 {
        left: 25%
    }
    .col-lg-push-4 {
        left: 33.3333333333%
    }
    .col-lg-push-5 {
        left: 41.6666666667%
    }
    .col-lg-push-6 {
        left: 50%
    }
    .col-lg-push-7 {
        left: 58.3333333333%
    }
    .col-lg-push-8 {
        left: 66.6666666667%
    }
    .col-lg-push-9 {
        left: 75%
    }
    .col-lg-push-10 {
        left: 83.3333333333%
    }
    .col-lg-push-11 {
        left: 91.6666666667%
    }
    .col-lg-push-12 {
        left: 100%
    }
    .col-lg-offset-0 {
        margin-left: 0%
    }
    .col-lg-offset-1 {
        margin-left: 8.3333333333%
    }
    .col-lg-offset-2 {
        margin-left: 16.6666666667%
    }
    .col-lg-offset-3 {
        margin-left: 25%
    }
    .col-lg-offset-4 {
        margin-left: 33.3333333333%
    }
    .col-lg-offset-5 {
        margin-left: 41.6666666667%
    }
    .col-lg-offset-6 {
        margin-left: 50%
    }
    .col-lg-offset-7 {
        margin-left: 58.3333333333%
    }
    .col-lg-offset-8 {
        margin-left: 66.6666666667%
    }
    .col-lg-offset-9 {
        margin-left: 75%
    }
    .col-lg-offset-10 {
        margin-left: 83.3333333333%
    }
    .col-lg-offset-11 {
        margin-left: 91.6666666667%
    }
    .col-lg-offset-12 {
        margin-left: 100%
    }
}

table {
    background-color: transparent
}

caption {
    padding-top: 8px;
    padding-bottom: 8px;
    color: #777;
    text-align: left
}

th {
    text-align: left
}

.table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 20px
}

.table>thead>tr>th,
.table>thead>tr>td,
.table>tbody>tr>th,
.table>tbody>tr>td,
.table>tfoot>tr>th,
.table>tfoot>tr>td {
    padding: 8px;
    line-height: 1.428571429;
    vertical-align: top;
    border-top: 1px solid #ddd
}

.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd
}

.table>caption+thead>tr:first-child>th,
.table>caption+thead>tr:first-child>td,
.table>colgroup+thead>tr:first-child>th,
.table>colgroup+thead>tr:first-child>td,
.table>thead:first-child>tr:first-child>th,
.table>thead:first-child>tr:first-child>td {
    border-top: 0
}

.table>tbody+tbody {
    border-top: 2px solid #ddd
}

.table .table {
    background-color: #fff
}

.table-condensed>thead>tr>th,
.table-condensed>thead>tr>td,
.table-condensed>tbody>tr>th,
.table-condensed>tbody>tr>td,
.table-condensed>tfoot>tr>th,
.table-condensed>tfoot>tr>td {
    padding: 5px
}

.table-bordered {
    border: 1px solid #ddd
}

.table-bordered>thead>tr>th,
.table-bordered>thead>tr>td,
.table-bordered>tbody>tr>th,
.table-bordered>tbody>tr>td,
.table-bordered>tfoot>tr>th,
.table-bordered>tfoot>tr>td {
    border: 1px solid #ddd
}

.table-bordered>thead>tr>th,
.table-bordered>thead>tr>td {
    border-bottom-width: 2px
}

.table-striped>tbody>tr:nth-of-type(even) {
    background-color: #f2f4f6
}

.table-hover>tbody>tr:hover {
    background-color: #f5f5f5
}

table col[class*="col-"] {
    position: static;
    float: none;
    display: table-column
}

table td[class*="col-"],
table th[class*="col-"] {
    position: static;
    float: none;
    display: table-cell
}

.table>thead>tr>td.active,
.table>thead>tr>th.active,
.table>thead>tr.active>td,
.table>thead>tr.active>th,
.table>tbody>tr>td.active,
.table>tbody>tr>th.active,
.table>tbody>tr.active>td,
.table>tbody>tr.active>th,
.table>tfoot>tr>td.active,
.table>tfoot>tr>th.active,
.table>tfoot>tr.active>td,
.table>tfoot>tr.active>th {
    background-color: #f5f5f5
}

.table-hover>tbody>tr>td.active:hover,
.table-hover>tbody>tr>th.active:hover,
.table-hover>tbody>tr.active:hover>td,
.table-hover>tbody>tr:hover>.active,
.table-hover>tbody>tr.active:hover>th {
    background-color: #e8e8e8
}

.table>thead>tr>td.success,
.table>thead>tr>th.success,
.table>thead>tr.success>td,
.table>thead>tr.success>th,
.table>tbody>tr>td.success,
.table>tbody>tr>th.success,
.table>tbody>tr.success>td,
.table>tbody>tr.success>th,
.table>tfoot>tr>td.success,
.table>tfoot>tr>th.success,
.table>tfoot>tr.success>td,
.table>tfoot>tr.success>th {
    background-color: #dff0d8
}

.table-hover>tbody>tr>td.success:hover,
.table-hover>tbody>tr>th.success:hover,
.table-hover>tbody>tr.success:hover>td,
.table-hover>tbody>tr:hover>.success,
.table-hover>tbody>tr.success:hover>th {
    background-color: #d0e9c6
}

.table>thead>tr>td.info,
.table>thead>tr>th.info,
.table>thead>tr.info>td,
.table>thead>tr.info>th,
.table>tbody>tr>td.info,
.table>tbody>tr>th.info,
.table>tbody>tr.info>td,
.table>tbody>tr.info>th,
.table>tfoot>tr>td.info,
.table>tfoot>tr>th.info,
.table>tfoot>tr.info>td,
.table>tfoot>tr.info>th {
    background-color: #d9edf7
}

.table-hover>tbody>tr>td.info:hover,
.table-hover>tbody>tr>th.info:hover,
.table-hover>tbody>tr.info:hover>td,
.table-hover>tbody>tr:hover>.info,
.table-hover>tbody>tr.info:hover>th {
    background-color: #c4e3f3
}

.table>thead>tr>td.warning,
.table>thead>tr>th.warning,
.table>thead>tr.warning>td,
.table>thead>tr.warning>th,
.table>tbody>tr>td.warning,
.table>tbody>tr>th.warning,
.table>tbody>tr.warning>td,
.table>tbody>tr.warning>th,
.table>tfoot>tr>td.warning,
.table>tfoot>tr>th.warning,
.table>tfoot>tr.warning>td,
.table>tfoot>tr.warning>th {
    background-color: #fcf8e3
}

.table-hover>tbody>tr>td.warning:hover,
.table-hover>tbody>tr>th.warning:hover,
.table-hover>tbody>tr.warning:hover>td,
.table-hover>tbody>tr:hover>.warning,
.table-hover>tbody>tr.warning:hover>th {
    background-color: #faf2cc
}

.table>thead>tr>td.danger,
.table>thead>tr>th.danger,
.table>thead>tr.danger>td,
.table>thead>tr.danger>th,
.table>tbody>tr>td.danger,
.table>tbody>tr>th.danger,
.table>tbody>tr.danger>td,
.table>tbody>tr.danger>th,
.table>tfoot>tr>td.danger,
.table>tfoot>tr>th.danger,
.table>tfoot>tr.danger>td,
.table>tfoot>tr.danger>th {
    background-color: #f2dede
}

.table-hover>tbody>tr>td.danger:hover,
.table-hover>tbody>tr>th.danger:hover,
.table-hover>tbody>tr.danger:hover>td,
.table-hover>tbody>tr:hover>.danger,
.table-hover>tbody>tr.danger:hover>th {
    background-color: #ebcccc
}

.table-responsive {
    overflow-x: auto;
    min-height: 0.01%
}

@media screen and (max-width: 767px) {
    .table-responsive {
        width: 100%;
        margin-bottom: 15px;
        overflow-y: hidden;
        -ms-overflow-style: -ms-autohiding-scrollbar;
        border: 1px solid #ddd
    }
    .table-responsive>.table {
        margin-bottom: 0
    }
    .table-responsive>.table>thead>tr>th,
    .table-responsive>.table>thead>tr>td,
    .table-responsive>.table>tbody>tr>th,
    .table-responsive>.table>tbody>tr>td,
    .table-responsive>.table>tfoot>tr>th,
    .table-responsive>.table>tfoot>tr>td {
        white-space: nowrap
    }
    .table-responsive>.table-bordered {
        border: 0
    }
    .table-responsive>.table-bordered>thead>tr>th:first-child,
    .table-responsive>.table-bordered>thead>tr>td:first-child,
    .table-responsive>.table-bordered>tbody>tr>th:first-child,
    .table-responsive>.table-bordered>tbody>tr>td:first-child,
    .table-responsive>.table-bordered>tfoot>tr>th:first-child,
    .table-responsive>.table-bordered>tfoot>tr>td:first-child {
        border-left: 0
    }
    .table-responsive>.table-bordered>thead>tr>th:last-child,
    .table-responsive>.table-bordered>thead>tr>td:last-child,
    .table-responsive>.table-bordered>tbody>tr>th:last-child,
    .table-responsive>.table-bordered>tbody>tr>td:last-child,
    .table-responsive>.table-bordered>tfoot>tr>th:last-child,
    .table-responsive>.table-bordered>tfoot>tr>td:last-child {
        border-right: 0
    }
    .table-responsive>.table-bordered>tbody>tr:last-child>th,
    .table-responsive>.table-bordered>tbody>tr:last-child>td,
    .table-responsive>.table-bordered>tfoot>tr:last-child>th,
    .table-responsive>.table-bordered>tfoot>tr:last-child>td {
        border-bottom: 0
    }
}

fieldset {
    padding: 0;
    margin: 0;
    border: 0;
    min-width: 0
}

legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: 20px;
    font-size: 21px;
    line-height: inherit;
    color: #333;
    border: 0;
    border-bottom: 1px solid #e5e5e5
}

label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold
}

input[type="search"] {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

input[type="radio"],
input[type="checkbox"] {
    margin: 4px 0 0;
    margin-top: 1px \9;
    line-height: normal
}

input[type="file"] {
    display: block
}

input[type="range"] {
    display: block;
    width: 100%
}

select[multiple],
select[size] {
    height: auto
}

input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

output {
    display: block;
    padding-top: 7px;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555
}

.form-control {
    display: block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s
}

.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6)
}

.form-control::-moz-placeholder {
    color: #999;
    opacity: 1
}

.form-control:-ms-input-placeholder {
    color: #999
}

.form-control::-webkit-input-placeholder {
    color: #999
}

.form-control::-ms-expand {
    border: 0;
    background-color: transparent
}

.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
    background-color: #eee;
    opacity: 1
}

.form-control[disabled],
fieldset[disabled] .form-control {
    cursor: not-allowed
}

@media screen and (max-width: 752px) {
    .form-control {
        font-size: 12px
    }
}

textarea.form-control {
    height: auto
}

input[type="search"] {
    -webkit-appearance: none
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
    input[type="date"].form-control,
    input[type="time"].form-control,
    input[type="datetime-local"].form-control,
    input[type="month"].form-control {
        line-height: 34px
    }
    input[type="date"].input-sm,
    .input-group-sm>input[type="date"].form-control,
    .input-group-sm>input[type="date"].input-group-addon,
    .input-group-sm>.input-group-btn>input[type="date"].btn,
    .input-group-sm input[type="date"],
    input[type="time"].input-sm,
    .input-group-sm>input[type="time"].form-control,
    .input-group-sm>input[type="time"].input-group-addon,
    .input-group-sm>.input-group-btn>input[type="time"].btn,
    .input-group-sm input[type="time"],
    input[type="datetime-local"].input-sm,
    .input-group-sm>input[type="datetime-local"].form-control,
    .input-group-sm>input[type="datetime-local"].input-group-addon,
    .input-group-sm>.input-group-btn>input[type="datetime-local"].btn,
    .input-group-sm input[type="datetime-local"],
    input[type="month"].input-sm,
    .input-group-sm>input[type="month"].form-control,
    .input-group-sm>input[type="month"].input-group-addon,
    .input-group-sm>.input-group-btn>input[type="month"].btn,
    .input-group-sm input[type="month"] {
        line-height: 30px
    }
    input[type="date"].input-lg,
    .input-group-lg>input[type="date"].form-control,
    .input-group-lg>input[type="date"].input-group-addon,
    .input-group-lg>.input-group-btn>input[type="date"].btn,
    .input-group-lg input[type="date"],
    input[type="time"].input-lg,
    .input-group-lg>input[type="time"].form-control,
    .input-group-lg>input[type="time"].input-group-addon,
    .input-group-lg>.input-group-btn>input[type="time"].btn,
    .input-group-lg input[type="time"],
    input[type="datetime-local"].input-lg,
    .input-group-lg>input[type="datetime-local"].form-control,
    .input-group-lg>input[type="datetime-local"].input-group-addon,
    .input-group-lg>.input-group-btn>input[type="datetime-local"].btn,
    .input-group-lg input[type="datetime-local"],
    input[type="month"].input-lg,
    .input-group-lg>input[type="month"].form-control,
    .input-group-lg>input[type="month"].input-group-addon,
    .input-group-lg>.input-group-btn>input[type="month"].btn,
    .input-group-lg input[type="month"] {
        line-height: 46px
    }
}

.form-group {
    margin-bottom: 15px
}

.radio,
.checkbox {
    position: relative;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px
}

.radio label,
.checkbox label {
    min-height: 20px;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer
}

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
    position: absolute;
    margin-left: -20px;
    margin-top: 4px \9
}

.radio+.radio,
.checkbox+.checkbox {
    margin-top: -5px
}

.radio-inline,
.checkbox-inline {
    position: relative;
    display: inline-block;
    padding-left: 20px;
    margin-bottom: 0;
    vertical-align: middle;
    font-weight: normal;
    cursor: pointer
}

.radio-inline+.radio-inline,
.checkbox-inline+.checkbox-inline {
    margin-top: 0;
    margin-left: 10px
}

input[type="radio"][disabled],
input[type="radio"].disabled,
fieldset[disabled] input[type="radio"],
input[type="checkbox"][disabled],
input[type="checkbox"].disabled,
fieldset[disabled] input[type="checkbox"] {
    cursor: not-allowed
}

.radio-inline.disabled,
fieldset[disabled] .radio-inline,
.checkbox-inline.disabled,
fieldset[disabled] .checkbox-inline {
    cursor: not-allowed
}

.radio.disabled label,
fieldset[disabled] .radio label,
.checkbox.disabled label,
fieldset[disabled] .checkbox label {
    cursor: not-allowed
}

.form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
    min-height: 34px
}

.form-control-static.input-lg,
.input-group-lg>.form-control-static.form-control,
.input-group-lg>.form-control-static.input-group-addon,
.input-group-lg>.input-group-btn>.form-control-static.btn,
.form-control-static.input-sm,
.input-group-sm>.form-control-static.form-control,
.input-group-sm>.form-control-static.input-group-addon,
.input-group-sm>.input-group-btn>.form-control-static.btn {
    padding-left: 0;
    padding-right: 0
}

.input-sm,
.input-group-sm>.form-control,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.btn {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 0px
}

select.input-sm,
.input-group-sm>select.form-control,
.input-group-sm>select.input-group-addon,
.input-group-sm>.input-group-btn>select.btn {
    height: 30px;
    line-height: 30px
}

textarea.input-sm,
.input-group-sm>textarea.form-control,
.input-group-sm>textarea.input-group-addon,
.input-group-sm>.input-group-btn>textarea.btn,
select[multiple].input-sm,
.input-group-sm>select[multiple].form-control,
.input-group-sm>select[multiple].input-group-addon,
.input-group-sm>.input-group-btn>select[multiple].btn {
    height: auto
}

.form-group-sm .form-control {
    height: 30px;
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 0px
}

.form-group-sm select.form-control {
    height: 30px;
    line-height: 30px
}

.form-group-sm textarea.form-control,
.form-group-sm select[multiple].form-control {
    height: auto
}

.form-group-sm .form-control-static {
    height: 30px;
    min-height: 32px;
    padding: 6px 10px;
    font-size: 12px;
    line-height: 1.5
}

.input-lg,
.input-group-lg>.form-control,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.btn {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 0px
}

select.input-lg,
.input-group-lg>select.form-control,
.input-group-lg>select.input-group-addon,
.input-group-lg>.input-group-btn>select.btn {
    height: 46px;
    line-height: 46px
}

textarea.input-lg,
.input-group-lg>textarea.form-control,
.input-group-lg>textarea.input-group-addon,
.input-group-lg>.input-group-btn>textarea.btn,
select[multiple].input-lg,
.input-group-lg>select[multiple].form-control,
.input-group-lg>select[multiple].input-group-addon,
.input-group-lg>.input-group-btn>select[multiple].btn {
    height: auto
}

.form-group-lg .form-control {
    height: 46px;
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 0px
}

.form-group-lg select.form-control {
    height: 46px;
    line-height: 46px
}

.form-group-lg textarea.form-control,
.form-group-lg select[multiple].form-control {
    height: auto
}

.form-group-lg .form-control-static {
    height: 46px;
    min-height: 38px;
    padding: 11px 16px;
    font-size: 18px;
    line-height: 1.3333333
}

.has-feedback {
    position: relative
}

.has-feedback .form-control {
    padding-right: 42.5px
}

.form-control-feedback {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    display: block;
    width: 34px;
    height: 34px;
    line-height: 34px;
    text-align: center;
    pointer-events: none
}

.input-lg+.form-control-feedback,
.input-group-lg>.form-control+.form-control-feedback,
.input-group-lg>.input-group-addon+.form-control-feedback,
.input-group-lg>.input-group-btn>.btn+.form-control-feedback,
.input-group-lg+.form-control-feedback,
.form-group-lg .form-control+.form-control-feedback {
    width: 46px;
    height: 46px;
    line-height: 46px
}

.input-sm+.form-control-feedback,
.input-group-sm>.form-control+.form-control-feedback,
.input-group-sm>.input-group-addon+.form-control-feedback,
.input-group-sm>.input-group-btn>.btn+.form-control-feedback,
.input-group-sm+.form-control-feedback,
.form-group-sm .form-control+.form-control-feedback {
    width: 30px;
    height: 30px;
    line-height: 30px
}

.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
    color: #3c763d
}

.has-success .form-control {
    border-color: #3c763d;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.has-success .form-control:focus {
    border-color: #2b542c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168
}

.has-success .input-group-addon {
    color: #3c763d;
    border-color: #3c763d;
    background-color: #dff0d8
}

.has-success .form-control-feedback {
    color: #3c763d
}

.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
    color: #8a6d3b
}

.has-warning .form-control {
    border-color: #8a6d3b;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.has-warning .form-control:focus {
    border-color: #66512c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b
}

.has-warning .input-group-addon {
    color: #8a6d3b;
    border-color: #8a6d3b;
    background-color: #fcf8e3
}

.has-warning .form-control-feedback {
    color: #8a6d3b
}

.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label {
    color: #a94442
}

.has-error .form-control {
    border-color: #a94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.has-error .form-control:focus {
    border-color: #843534;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483
}

.has-error .input-group-addon {
    color: #a94442;
    border-color: #a94442;
    background-color: #f2dede
}

.has-error .form-control-feedback {
    color: #a94442
}

.has-feedback label ~ .form-control-feedback {
    top: 25px
}

.has-feedback label.sr-only ~ .form-control-feedback {
    top: 0
}

.help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #616161
}

@media (min-width: 768px) {
    .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .form-inline .form-control-static {
        display: inline-block
    }
    .form-inline .input-group {
        display: inline-table;
        vertical-align: middle
    }
    .form-inline .input-group .input-group-addon,
    .form-inline .input-group .input-group-btn,
    .form-inline .input-group .form-control {
        width: auto
    }
    .form-inline .input-group>.form-control {
        width: 100%
    }
    .form-inline .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .radio,
    .form-inline .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }
    .form-inline .radio label,
    .form-inline .checkbox label {
        padding-left: 0
    }
    .form-inline .radio input[type="radio"],
    .form-inline .checkbox input[type="checkbox"] {
        position: relative;
        margin-left: 0
    }
    .form-inline .has-feedback .form-control-feedback {
        top: 0
    }
}

.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 7px
}

.form-horizontal .radio,
.form-horizontal .checkbox {
    min-height: 27px
}

.form-horizontal .form-group {
    margin-left: -15px;
    margin-right: -15px
}

.form-horizontal .form-group:before,
.form-horizontal .form-group:after {
    content: " ";
    display: table
}

.form-horizontal .form-group:after {
    clear: both
}

@media (min-width: 768px) {
    .form-horizontal .control-label {
        text-align: right;
        margin-bottom: 0;
        padding-top: 7px
    }
}

.form-horizontal .has-feedback .form-control-feedback {
    right: 15px
}

@media (min-width: 768px) {
    .form-horizontal .form-group-lg .control-label {
        padding-top: 11px;
        font-size: 18px
    }
}

@media (min-width: 768px) {
    .form-horizontal .form-group-sm .control-label {
        padding-top: 6px;
        font-size: 12px
    }
}

.btn {
    display: inline-block;
    margin-bottom: 0;
    text-transform: uppercase;
    font-family: inherit;
    margin: 10px 0;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 25px;
    font-size: 14px;
    line-height: 1.428571429;
    border-radius: 0px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

@media screen and (max-width: 768px) {
    .btn {
        font-size: 11px;
        padding: 5px 10px
    }
}

.btn:focus,
.btn.focus,
.btn:active:focus,
.btn:active.focus,
.btn.active:focus,
.btn.active.focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px
}

.btn:hover,
.btn:focus,
.btn.focus {
    color: #333;
    text-decoration: none
}

.btn:active,
.btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125)
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
    cursor: not-allowed;
    opacity: 0.3;
    filter: alpha(opacity=30);
    -webkit-box-shadow: none;
    box-shadow: none
}

a.btn.disabled,
fieldset[disabled] a.btn {
    pointer-events: none
}

.btn-default {
    color: #333;
    background-color: #fff;
    border-color: #152840
}

.btn-default:focus,
.btn-default.focus {
    color: #333;
    background-color: #e6e6e6;
    border-color: #000
}

.btn-default:hover {
    color: #333;
    background-color: #e6e6e6;
    border-color: #060b12
}

.btn-default:active,
.btn-default.active,
.open>.btn-default.dropdown-toggle {
    color: #333;
    background-color: #e6e6e6;
    border-color: #060b12
}

.btn-default:active:hover,
.btn-default:active:focus,
.btn-default:active.focus,
.btn-default.active:hover,
.btn-default.active:focus,
.btn-default.active.focus,
.open>.btn-default.dropdown-toggle:hover,
.open>.btn-default.dropdown-toggle:focus,
.open>.btn-default.dropdown-toggle.focus {
    color: #333;
    background-color: #d4d4d4;
    border-color: #000
}

.btn-default:active,
.btn-default.active,
.open>.btn-default.dropdown-toggle {
    background-image: none
}

.btn-default.disabled:hover,
.btn-default.disabled:focus,
.btn-default.disabled.focus,
.btn-default[disabled]:hover,
.btn-default[disabled]:focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default:hover,
fieldset[disabled] .btn-default:focus,
fieldset[disabled] .btn-default.focus {
    background-color: #fff;
    border-color: #152840
}

.btn-default .badge {
    color: #fff;
    background-color: #333
}

.btn-primary {
    color: #fff;
    background-color: #2c4e86;
    border-color: #264373
}

.btn-primary:focus,
.btn-primary.focus {
    color: #fff;
    background-color: #1f3860;
    border-color: #060b13
}

.btn-primary:hover {
    color: #fff;
    background-color: #1f3860;
    border-color: #172845
}

.btn-primary:active,
.btn-primary.active,
.open>.btn-primary.dropdown-toggle {
    color: #fff;
    background-color: #1f3860;
    border-color: #172845
}

.btn-primary:active:hover,
.btn-primary:active:focus,
.btn-primary:active.focus,
.btn-primary.active:hover,
.btn-primary.active:focus,
.btn-primary.active.focus,
.open>.btn-primary.dropdown-toggle:hover,
.open>.btn-primary.dropdown-toggle:focus,
.open>.btn-primary.dropdown-toggle.focus {
    color: #fff;
    background-color: #172845;
    border-color: #060b13
}

.btn-primary:active,
.btn-primary.active,
.open>.btn-primary.dropdown-toggle {
    background-image: none
}

.btn-primary.disabled:hover,
.btn-primary.disabled:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled]:hover,
.btn-primary[disabled]:focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary:hover,
fieldset[disabled] .btn-primary:focus,
fieldset[disabled] .btn-primary.focus {
    background-color: #2c4e86;
    border-color: #264373
}

.btn-primary .badge {
    color: #2c4e86;
    background-color: #fff
}

.btn-success {
    color: #fff;
    background-color: #5cb85c;
    border-color: #4cae4c
}

.btn-success:focus,
.btn-success.focus {
    color: #fff;
    background-color: #449d44;
    border-color: #255625
}

.btn-success:hover {
    color: #fff;
    background-color: #449d44;
    border-color: #398439
}

.btn-success:active,
.btn-success.active,
.open>.btn-success.dropdown-toggle {
    color: #fff;
    background-color: #449d44;
    border-color: #398439
}

.btn-success:active:hover,
.btn-success:active:focus,
.btn-success:active.focus,
.btn-success.active:hover,
.btn-success.active:focus,
.btn-success.active.focus,
.open>.btn-success.dropdown-toggle:hover,
.open>.btn-success.dropdown-toggle:focus,
.open>.btn-success.dropdown-toggle.focus {
    color: #fff;
    background-color: #398439;
    border-color: #255625
}

.btn-success:active,
.btn-success.active,
.open>.btn-success.dropdown-toggle {
    background-image: none
}

.btn-success.disabled:hover,
.btn-success.disabled:focus,
.btn-success.disabled.focus,
.btn-success[disabled]:hover,
.btn-success[disabled]:focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success:hover,
fieldset[disabled] .btn-success:focus,
fieldset[disabled] .btn-success.focus {
    background-color: #5cb85c;
    border-color: #4cae4c
}

.btn-success .badge {
    color: #5cb85c;
    background-color: #fff
}

.btn-info {
    color: #fff;
    background-color: #5bc0de;
    border-color: #46b8da
}

.btn-info:focus,
.btn-info.focus {
    color: #fff;
    background-color: #31b0d5;
    border-color: #1b6d85
}

.btn-info:hover {
    color: #fff;
    background-color: #31b0d5;
    border-color: #269abc
}

.btn-info:active,
.btn-info.active,
.open>.btn-info.dropdown-toggle {
    color: #fff;
    background-color: #31b0d5;
    border-color: #269abc
}

.btn-info:active:hover,
.btn-info:active:focus,
.btn-info:active.focus,
.btn-info.active:hover,
.btn-info.active:focus,
.btn-info.active.focus,
.open>.btn-info.dropdown-toggle:hover,
.open>.btn-info.dropdown-toggle:focus,
.open>.btn-info.dropdown-toggle.focus {
    color: #fff;
    background-color: #269abc;
    border-color: #1b6d85
}

.btn-info:active,
.btn-info.active,
.open>.btn-info.dropdown-toggle {
    background-image: none
}

.btn-info.disabled:hover,
.btn-info.disabled:focus,
.btn-info.disabled.focus,
.btn-info[disabled]:hover,
.btn-info[disabled]:focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info:hover,
fieldset[disabled] .btn-info:focus,
fieldset[disabled] .btn-info.focus {
    background-color: #5bc0de;
    border-color: #46b8da
}

.btn-info .badge {
    color: #5bc0de;
    background-color: #fff
}

.btn-warning {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #eea236
}

.btn-warning:focus,
.btn-warning.focus {
    color: #fff;
    background-color: #ec971f;
    border-color: #985f0d
}

.btn-warning:hover {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512
}

.btn-warning:active,
.btn-warning.active,
.open>.btn-warning.dropdown-toggle {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512
}

.btn-warning:active:hover,
.btn-warning:active:focus,
.btn-warning:active.focus,
.btn-warning.active:hover,
.btn-warning.active:focus,
.btn-warning.active.focus,
.open>.btn-warning.dropdown-toggle:hover,
.open>.btn-warning.dropdown-toggle:focus,
.open>.btn-warning.dropdown-toggle.focus {
    color: #fff;
    background-color: #d58512;
    border-color: #985f0d
}

.btn-warning:active,
.btn-warning.active,
.open>.btn-warning.dropdown-toggle {
    background-image: none
}

.btn-warning.disabled:hover,
.btn-warning.disabled:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled]:hover,
.btn-warning[disabled]:focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning:hover,
fieldset[disabled] .btn-warning:focus,
fieldset[disabled] .btn-warning.focus {
    background-color: #f0ad4e;
    border-color: #eea236
}

.btn-warning .badge {
    color: #f0ad4e;
    background-color: #fff
}

.btn-danger {
    color: #fff;
    background-color: #d84d4b;
    border-color: #d33836
}

.btn-danger:focus,
.btn-danger.focus {
    color: #fff;
    background-color: #c52d2b;
    border-color: #711a19
}

.btn-danger:hover {
    color: #fff;
    background-color: #c52d2b;
    border-color: #a82624
}

.btn-danger:active,
.btn-danger.active,
.open>.btn-danger.dropdown-toggle {
    color: #fff;
    background-color: #c52d2b;
    border-color: #a82624
}

.btn-danger:active:hover,
.btn-danger:active:focus,
.btn-danger:active.focus,
.btn-danger.active:hover,
.btn-danger.active:focus,
.btn-danger.active.focus,
.open>.btn-danger.dropdown-toggle:hover,
.open>.btn-danger.dropdown-toggle:focus,
.open>.btn-danger.dropdown-toggle.focus {
    color: #fff;
    background-color: #a82624;
    border-color: #711a19
}

.btn-danger:active,
.btn-danger.active,
.open>.btn-danger.dropdown-toggle {
    background-image: none
}

.btn-danger.disabled:hover,
.btn-danger.disabled:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled]:hover,
.btn-danger[disabled]:focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger:hover,
fieldset[disabled] .btn-danger:focus,
fieldset[disabled] .btn-danger.focus {
    background-color: #d84d4b;
    border-color: #d33836
}

.btn-danger .badge {
    color: #d84d4b;
    background-color: #fff
}

.btn-link {
    color: #337ab7;
    font-weight: normal;
    border-radius: 0
}

.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none
}

.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
    border-color: transparent
}

.btn-link:hover,
.btn-link:focus {
    color: #23527c;
    text-decoration: underline;
    background-color: transparent
}

.btn-link[disabled]:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:hover,
fieldset[disabled] .btn-link:focus {
    color: #777;
    text-decoration: none
}

.btn-lg,
.btn-group-lg>.btn {
    padding: 10px 50px;
    font-size: 18px;
    line-height: 1.3333333;
    border-radius: 1px
}

@media screen and (max-width: 768px) {
    .btn-lg,
    .btn-group-lg>.btn {
        font-size: 11px;
        padding: 5px 10px
    }
}

.btn-sm,
.btn-group-sm>.btn {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 0px
}

@media screen and (max-width: 768px) {
    .btn-sm,
    .btn-group-sm>.btn {
        font-size: 11px;
        padding: 5px 10px
    }
}

.btn-xs,
.btn-group-xs>.btn {
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 0px
}

@media screen and (max-width: 768px) {
    .btn-xs,
    .btn-group-xs>.btn {
        font-size: 11px;
        padding: 5px 10px
    }
}

.btn-block {
    display: block;
    width: 100%
}

.btn-block+.btn-block {
    margin-top: 5px
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
    width: 100%
}

.fade {
    opacity: 0;
    -webkit-transition: opacity 0.15s linear;
    -o-transition: opacity 0.15s linear;
    transition: opacity 0.15s linear
}

.fade.in {
    opacity: 1
}

.collapse {
    display: none
}

.collapse.in {
    display: block
}

tr.collapse.in {
    display: table-row
}

tbody.collapse.in {
    display: table-row-group
}

.collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition-property: height, visibility;
    transition-property: height, visibility;
    -webkit-transition-duration: 0.35s;
    transition-duration: 0.35s;
    -webkit-transition-timing-function: ease;
    transition-timing-function: ease
}

.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-top: 4px solid \9;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent
}

.dropup,
.dropdown {
    position: relative
}

.dropdown-toggle:focus {
    outline: 0
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 14px;
    text-align: left;
    background-color: #fff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    background-clip: padding-box
}

.dropdown-menu.pull-right {
    right: 0;
    left: auto
}

.dropdown-menu .divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.dropdown-menu>li>a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.428571429;
    color: #333;
    white-space: nowrap
}

.dropdown-menu>li>a:hover,
.dropdown-menu>li>a:focus {
    text-decoration: none;
    color: #262626;
    background-color: #f5f5f5
}

.dropdown-menu>.active>a,
.dropdown-menu>.active>a:hover,
.dropdown-menu>.active>a:focus {
    color: #fff;
    text-decoration: none;
    outline: 0;
    background-color: #337ab7
}

.dropdown-menu>.disabled>a,
.dropdown-menu>.disabled>a:hover,
.dropdown-menu>.disabled>a:focus {
    color: #777
}

.dropdown-menu>.disabled>a:hover,
.dropdown-menu>.disabled>a:focus {
    text-decoration: none;
    background-color: transparent;
    background-image: none;
    filter: progid: DXImageTransform.Microsoft.gradient(enabled=false);
    cursor: not-allowed
}

.open>.dropdown-menu {
    display: block
}

.open>a {
    outline: 0
}

.dropdown-menu-right {
    left: auto;
    right: 0
}

.dropdown-menu-left {
    left: 0;
    right: auto
}

.dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: 12px;
    line-height: 1.428571429;
    color: #777;
    white-space: nowrap
}

.dropdown-backdrop {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 990
}

.pull-right>.dropdown-menu {
    right: 0;
    left: auto
}

.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
    border-top: 0;
    border-bottom: 4px dashed;
    border-bottom: 4px solid \9;
    content: ""
}

.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 2px
}

@media (min-width: 768px) {
    .navbar-right .dropdown-menu {
        right: 0;
        left: auto
    }
    .navbar-right .dropdown-menu-left {
        left: 0;
        right: auto
    }
}

.btn-group,
.btn-group-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle
}

.btn-group>.btn,
.btn-group-vertical>.btn {
    position: relative;
    float: left
}

.btn-group>.btn:hover,
.btn-group>.btn:focus,
.btn-group>.btn:active,
.btn-group>.btn.active,
.btn-group-vertical>.btn:hover,
.btn-group-vertical>.btn:focus,
.btn-group-vertical>.btn:active,
.btn-group-vertical>.btn.active {
    z-index: 2
}

.btn-group .btn+.btn,
.btn-group .btn+.btn-group,
.btn-group .btn-group+.btn,
.btn-group .btn-group+.btn-group {
    margin-left: -1px
}

.btn-toolbar {
    margin-left: -5px
}

.btn-toolbar:before,
.btn-toolbar:after {
    content: " ";
    display: table
}

.btn-toolbar:after {
    clear: both
}

.btn-toolbar .btn,
.btn-toolbar .btn-group,
.btn-toolbar .input-group {
    float: left
}

.btn-toolbar>.btn,
.btn-toolbar>.btn-group,
.btn-toolbar>.input-group {
    margin-left: 5px
}

.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0
}

.btn-group>.btn:first-child {
    margin-left: 0
}

.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn:last-child:not(:first-child),
.btn-group>.dropdown-toggle:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group>.btn-group {
    float: left
}

.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
    outline: 0
}

.btn-group>.btn+.dropdown-toggle {
    padding-left: 8px;
    padding-right: 8px
}

.btn-group>.btn-lg+.dropdown-toggle,
.btn-group-lg.btn-group>.btn+.dropdown-toggle {
    padding-left: 12px;
    padding-right: 12px
}

.btn-group.open .dropdown-toggle {
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125)
}

.btn-group.open .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    box-shadow: none
}

.btn .caret {
    margin-left: 0
}

.btn-lg .caret,
.btn-group-lg>.btn .caret {
    border-width: 5px 5px 0;
    border-bottom-width: 0
}

.dropup .btn-lg .caret,
.dropup .btn-group-lg>.btn .caret {
    border-width: 0 5px 5px
}

.btn-group-vertical>.btn,
.btn-group-vertical>.btn-group,
.btn-group-vertical>.btn-group>.btn {
    display: block;
    float: none;
    width: 100%;
    max-width: 100%
}

.btn-group-vertical>.btn-group:before,
.btn-group-vertical>.btn-group:after {
    content: " ";
    display: table
}

.btn-group-vertical>.btn-group:after {
    clear: both
}

.btn-group-vertical>.btn-group>.btn {
    float: none
}

.btn-group-vertical>.btn+.btn,
.btn-group-vertical>.btn+.btn-group,
.btn-group-vertical>.btn-group+.btn,
.btn-group-vertical>.btn-group+.btn-group {
    margin-top: -1px;
    margin-left: 0
}

.btn-group-vertical>.btn:not(:first-child):not(:last-child) {
    border-radius: 0
}

.btn-group-vertical>.btn:first-child:not(:last-child) {
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn:last-child:not(:first-child) {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px
}

.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn {
    border-radius: 0
}

.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,
.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.btn-group-justified {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: separate
}

.btn-group-justified>.btn,
.btn-group-justified>.btn-group {
    float: none;
    display: table-cell;
    width: 1%
}

.btn-group-justified>.btn-group .btn {
    width: 100%
}

.btn-group-justified>.btn-group .dropdown-menu {
    left: auto
}

[data-toggle="buttons"]>.btn input[type="radio"],
[data-toggle="buttons"]>.btn input[type="checkbox"],
[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],
[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"] {
    position: absolute;
    clip: rect(0, 0, 0, 0);
    pointer-events: none
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate
}

.input-group[class*="col-"] {
    float: none;
    padding-left: 0;
    padding-right: 0
}

.input-group .form-control {
    position: relative;
    z-index: 0;
    float: left;
    width: 100%;
    margin-bottom: 0
}

.input-group .form-control:focus {
    z-index: 3
}

.input-group-addon,
.input-group-btn,
.input-group .form-control {
    display: table-cell
}

.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0
}

.input-group-addon,
.input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle
}

.input-group-addon {
    padding: 6px 12px;
    font-size: 14px;
    font-weight: normal;
    line-height: 1;
    color: #555;
    text-align: center;
    background-color: #eee;
    border: 1px solid #ccc;
    border-radius: 0px
}

.input-group-addon.input-sm,
.input-group-sm>.input-group-addon,
.input-group-sm>.input-group-btn>.input-group-addon.btn {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 0px
}

.input-group-addon.input-lg,
.input-group-lg>.input-group-addon,
.input-group-lg>.input-group-btn>.input-group-addon.btn {
    padding: 10px 16px;
    font-size: 18px;
    border-radius: 0px
}

.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
    margin-top: 0
}

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group>.btn,
.input-group-btn:first-child>.dropdown-toggle,
.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child>.btn-group:not(:last-child)>.btn {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0
}

.input-group-addon:first-child {
    border-right: 0
}

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group>.btn,
.input-group-btn:last-child>.dropdown-toggle,
.input-group-btn:first-child>.btn:not(:first-child),
.input-group-btn:first-child>.btn-group:not(:first-child)>.btn {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0
}

.input-group-addon:last-child {
    border-left: 0
}

.input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap
}

.input-group-btn>.btn {
    position: relative
}

.input-group-btn>.btn+.btn {
    margin-left: -1px
}

.input-group-btn>.btn:hover,
.input-group-btn>.btn:focus,
.input-group-btn>.btn:active {
    z-index: 0
}

.input-group-btn:first-child>.btn,
.input-group-btn:first-child>.btn-group {
    margin-right: -1px
}

.input-group-btn:last-child>.btn,
.input-group-btn:last-child>.btn-group {
    z-index: 0;
    margin-left: -1px
}

.nav {
    margin-bottom: 0;
    padding-left: 0;
    list-style: none
}

.nav:before,
.nav:after {
    content: " ";
    display: table
}

.nav:after {
    clear: both
}

.nav>li {
    position: relative;
    display: block
}

.nav>li>a {
    position: relative;
    display: block;
    padding: 10px 20px
}

.nav>li>a:hover,
.nav>li>a:focus {
    text-decoration: none;
    background-color: #eee
}

.nav>li.disabled>a {
    color: #777
}

.nav>li.disabled>a:hover,
.nav>li.disabled>a:focus {
    color: #777;
    text-decoration: none;
    background-color: transparent;
    cursor: not-allowed
}

.nav .open>a,
.nav .open>a:hover,
.nav .open>a:focus {
    background-color: #eee;
    border-color: #337ab7
}

.nav .nav-divider {
    height: 1px;
    margin: 9px 0;
    overflow: hidden;
    background-color: #e5e5e5
}

.nav>li>a>img {
    max-width: none
}

.nav-tabs {
    border-bottom: 1px solid #ddd
}

.nav-tabs.land-tabs>li {
    width: 50%
}

.nav-tabs.land-tabs>li a {
    padding: 10px 20px
}

@media screen and (max-width: 1200px) {
    .nav-tabs.land-tabs>li a {
        padding: 8px 20px
    }
}

.nav-tabs.land-tabs>li a img {
    display: inline-block
}

.nav-tabs.land-tabs>li a p {
    display: inline-block;
    font-family: Tahoma;
    font-weight: 300
}

.nav-tabs.land-tabs+.tab-content {
    padding-top: 30px
}

.nav-tabs.tp-tabs>li.active a,
.nav-tabs.tp-tabs>li.active a:hover,
.nav-tabs.tp-tabs>li.active a:active,
.nav-tabs.tp-tabs>li.active a:focus {
    border-bottom: 2px solid #17c4bb !important;
    margin-bottom: -2px;
    border-left: none;
    border-right: none;
    border-top: none
}

@media screen and (min-width: 770px) and (max-width: 990px) {
    .nav-tabs.tp-tabs>li a {
        padding: 10px 12px
    }
}

.nav-tabs.num-tabs>li.active a,
.nav-tabs.num-tabs>li.active a:hover,
.nav-tabs.num-tabs>li.active a:active {
    border-top: 2px solid #17c4bb !important;
    margin-top: -2px
}

.nav-tabs.num-tabs>li a {
    border-radius: 0px;
    margin-right: 0px;
    padding: 0px 30px 10px 30px
}

.nav-tabs.num-tabs>li a span {
    display: block;
    text-align: center;
    font-family: tahoma;
    font-size: 36px
}

.nav-tabs.num-tabs>li a span.ttl {
    font-size: 18px
}

.nav-tabs.num-tabs>li:hover a {
    background-color: #fff
}

.nav-tabs.reg-tabs.r1>li {
    width: 10%
}

@media screen and (max-width: 751px) {
    .nav-tabs.reg-tabs.r1>li {
        min-height: 40px;
        width: 100%;
        border-left: 1px solid #8ba8ba
    }
}

@media screen and (min-width: 752px) and (max-width: 991px) {
    .nav-tabs.reg-tabs.r1>li {
        min-height: 96px
    }
}

.nav-tabs.reg-tabs>li {
    width: 10%;
    min-height: 115px;
    text-align: center;
    border-right: 1px solid #8ba8ba;
    border-top: 1px solid #8ba8ba;
    border-bottom: 1px solid #8ba8ba
}

.nav-tabs.reg-tabs>li.active>a,
.nav-tabs.reg-tabs>li.active>a:hover,
.nav-tabs.reg-tabs>li.active>a:focus {
    border: none;
    cursor: default
}

.nav-tabs.reg-tabs>li a {
    font-family: verdana;
    font-size: 12px;
    border: none;
    color: #212121;
    padding: 10px
}

.nav-tabs.reg-tabs>li a:hover {
    background-color: transparent
}

@media screen and (max-width: 991px) {
    .nav-tabs.reg-tabs>li {
        min-height: 75px;
        width: 100%
    }
}

.nav-tabs.reg-tabs>li .navicon {
    background-repeat: no-repeat;
    background-position: center;
    height: 36px;
    display: block;
    margin-top: 2px
}

@media screen and (max-width: 991px) {
    .nav-tabs.reg-tabs>li .navicon {
        display: none
    }
}

.nav-tabs.reg-tabs>li .navicon.partners {
    background-image: url("/uiassets/images/icons/Partner_Details_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.business {
    background-image: url("/uiassets/images/icons/bussgrey.png")
}

.nav-tabs.reg-tabs>li .navicon.auth-sig {
    background-image: url("/uiassets/images/icons/Authorized_Signatory_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.auth-sig-rep {
    background-image: url("/uiassets/images/icons/Authorized_rep_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.bplace {
    background-image: url("/uiassets/images/icons/Principal-Place_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.abplace {
    background-image: url("/uiassets/images/icons/Additional-places_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.goods {
    background-image: url("/uiassets/images/icons/Goods_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.bank {
    background-image: url("/uiassets/images/icons/bank_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.state {
    background-image: url("/uiassets/images/icons/state_grey.png")
}

.nav-tabs.reg-tabs>li .navicon.declaration {
    background-image: url("/uiassets/images/icons/Declaration_grey.png")
}

.nav-tabs.reg-tabs>li.complete {
    background-color: #2c4e86;
    background-image: url("/uiassets/images/icons/fa-check.png");
    background-position: right top;
    background-repeat: no-repeat;
    background-origin: content-box;
    padding: 5px 5px 0 0
}

.nav-tabs.reg-tabs>li.complete.active {
    background-color: #fff;
    background-image: url("/uiassets/images/icons/fa-check-gray.png")
}

.nav-tabs.reg-tabs>li.complete.active a {
    color: #212121
}

.nav-tabs.reg-tabs>li.complete.active a:hover {
    color: #212121
}

.nav-tabs.reg-tabs>li.complete.active .partners {
    background-image: url("/uiassets/images/icons/Partner_Details_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .business {
    background-image: url("/uiassets/images/icons/bussgrey.png")
}

.nav-tabs.reg-tabs>li.complete.active .auth-sig {
    background-image: url("/uiassets/images/icons/Authorized_Signatory_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .auth-sig-rep {
    background-image: url("/uiassets/images/icons/Authorized_rep_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .bplace {
    background-image: url("/uiassets/images/icons/Principal-Place_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .abplace {
    background-image: url("/uiassets/images/icons/Additional-places_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .goods {
    background-image: url("/uiassets/images/icons/Goods_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .bank {
    background-image: url("/uiassets/images/icons/bank_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .state {
    background-image: url("/uiassets/images/icons/state_grey.png")
}

.nav-tabs.reg-tabs>li.complete.active .declaration {
    background-image: url("/uiassets/images/icons/Declaration_grey.png")
}

.nav-tabs.reg-tabs>li.complete a,
.nav-tabs.reg-tabs>li.complete a:hover {
    color: #fff;
    border: none;
    background: none;
    cursor: pointer
}

.nav-tabs.reg-tabs>li.complete .partners {
    background-image: url("/uiassets/images/icons/Partner_Details_white.png")
}

.nav-tabs.reg-tabs>li.complete .business {
    background-image: url("/uiassets/images/icons/busswhite.png")
}

.nav-tabs.reg-tabs>li.complete .auth-sig {
    background-image: url("/uiassets/images/icons/Authorized_Signatory_white.png")
}

.nav-tabs.reg-tabs>li.complete .auth-sig-rep {
    background-image: url("/uiassets/images/icons/Authorized_rep_white.png")
}

.nav-tabs.reg-tabs>li.complete .bplace {
    background-image: url("/uiassets/images/icons/Principal-Place_white.png")
}

.nav-tabs.reg-tabs>li.complete .abplace {
    background-image: url("/uiassets/images/icons/Additional-places_white.png")
}

.nav-tabs.reg-tabs>li.complete .goods {
    background-image: url("/uiassets/images/icons/Goods_white.png")
}

.nav-tabs.reg-tabs>li.complete .bank {
    background-image: url("/uiassets/images/icons/bank_white.png")
}

.nav-tabs.reg-tabs>li.complete .state {
    background-image: url("/uiassets/images/icons/state_white.png")
}

.nav-tabs.reg-tabs>li.complete .declaration {
    background-image: url("/uiassets/images/icons/Declaration_white.png")
}

.nav-tabs.reg-tabs>li.incomplete {
    background-color: #ebf0f2;
    padding: 0
}

.nav-tabs.reg-tabs>li.incomplete.active {
    background-color: #fff
}

.nav-tabs.reg-tabs>li.incomplete.active a:hover,
.nav-tabs.reg-tabs>li.incomplete.active a:focus {
    background-color: #fff
}

.nav-tabs.reg-tabs>li.incomplete .triangle {
    border-top: 26px solid #ffdb49;
    border-left: 25px solid transparent;
    float: right
}

.nav-tabs.reg-tabs>li.incomplete .triangle>span {
    position: absolute;
    top: 1px;
    right: 1px;
    font-size: 12px;
    color: #212121;
    font-family: verdana
}

.nav-tabs.reg-tabs>li:first-child {
    border-left: 1px solid #8ba8ba
}

.nav-tabs.reg-tabs>li:hover {
    border-color: #8ba8ba;
    box-shadow: 0 0 10px #8ba8ba
}

.nav-tabs>li {
    float: left;
    margin-bottom: -1px
}

.nav-tabs>li>a {
    margin-right: 2px;
    line-height: 1.428571429;
    border: 1px solid transparent;
    border-radius: 0px 0px 0 0
}

.nav-tabs>li>a:hover {
    border-color: #eee #eee #ddd
}

.nav-tabs>li.active>a,
.nav-tabs>li.active>a:hover,
.nav-tabs>li.active>a:focus {
    color: #555;
    background-color: #fff;
    border: 1px solid #ddd;
    border-bottom-color: transparent;
    cursor: default
}

.nav-pills>li {
    float: left
}

.nav-pills>li>a {
    border-radius: 0px
}

.nav-pills>li+li {
    margin-left: 2px
}

.nav-pills>li.active>a,
.nav-pills>li.active>a:hover,
.nav-pills>li.active>a:focus {
    color: #fff;
    background-color: #337ab7
}

.nav-stacked>li {
    float: none
}

.nav-stacked>li+li {
    margin-top: 2px;
    margin-left: 0
}

.nav-justified,
.nav-tabs.nav-justified {
    width: 100%
}

.nav-justified>li,
.nav-tabs.nav-justified>li {
    float: none
}

.nav-justified>li>a,
.nav-tabs.nav-justified>li>a {
    text-align: center;
    margin-bottom: 5px
}

.nav-justified>.dropdown .dropdown-menu {
    top: auto;
    left: auto
}

@media (min-width: 768px) {
    .nav-justified>li,
    .nav-tabs.nav-justified>li {
        display: table-cell;
        width: 1%
    }
    .nav-justified>li>a,
    .nav-tabs.nav-justified>li>a {
        margin-bottom: 0
    }
}

.nav-tabs-justified,
.nav-tabs.nav-justified {
    border-bottom: 0
}

.nav-tabs-justified>li>a,
.nav-tabs.nav-justified>li>a {
    margin-right: 0;
    border-radius: 0px
}

.nav-tabs-justified>.active>a,
.nav-tabs.nav-justified>.active>a,
.nav-tabs-justified>.active>a:hover,
.nav-tabs.nav-justified>.active>a:hover,
.nav-tabs-justified>.active>a:focus,
.nav-tabs.nav-justified>.active>a:focus {
    border: 1px solid #ddd
}

@media (min-width: 768px) {
    .nav-tabs-justified>li>a,
    .nav-tabs.nav-justified>li>a {
        border-bottom: 1px solid #ddd;
        border-radius: 0px 0px 0 0
    }
    .nav-tabs-justified>.active>a,
    .nav-tabs.nav-justified>.active>a,
    .nav-tabs-justified>.active>a:hover,
    .nav-tabs.nav-justified>.active>a:hover,
    .nav-tabs-justified>.active>a:focus,
    .nav-tabs.nav-justified>.active>a:focus {
        border-bottom-color: #fff
    }
}

.tab-content>.tab-pane {
    display: none
}

.tab-content>.active {
    display: block
}

.nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.navbar {
    position: relative;
    max-height: 50px;
    border: 1px solid transparent
}

.navbar:before,
.navbar:after {
    content: " ";
    display: table
}

.navbar:after {
    clear: both
}

@media screen and (max-width: 768px) {
    .navbar .container {
        padding: 0
    }
}

@media screen and (max-width: 767px) {
    .navbar {
        background-color: transparent !important;
        border-color: transparent !important
    }
}

@media screen and (min-width: 769px) {
    .navbar.sticky {
        left: 0;
        position: fixed;
        right: 0;
        z-index: 9999;
        top: 0;
        transition: all 0.3s ease-in
    }
}

.navbar-header:before,
.navbar-header:after {
    content: " ";
    display: table
}

.navbar-header:after {
    clear: both
}

@media (min-width: 768px) {
    .navbar-header {
        float: left
    }
}

.navbar-collapse {
    overflow-x: visible;
    z-index: 23;
    background: #2c4e86;
    position: relative;
    padding-right: 0;
    padding-left: 0;
    border-top: 1px solid transparent;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    -webkit-overflow-scrolling: touch
}

.navbar-collapse:before,
.navbar-collapse:after {
    content: " ";
    display: table
}

.navbar-collapse:after {
    clear: both
}

.navbar-collapse.in {
    overflow-y: auto
}

@media (min-width: 768px) {
    .navbar-collapse {
        width: auto;
        border-top: 0;
        box-shadow: none
    }
    .navbar-collapse.collapse {
        display: block !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important
    }
    .navbar-collapse.in {
        overflow-y: visible
    }
    .navbar-fixed-top .navbar-collapse,
    .navbar-static-top .navbar-collapse,
    .navbar-fixed-bottom .navbar-collapse {
        padding-left: 0;
        padding-right: 0
    }
}

.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
    max-height: 340px
}

@media (max-device-width: 480px) and (orientation: landscape) {
    .navbar-fixed-top .navbar-collapse,
    .navbar-fixed-bottom .navbar-collapse {
        max-height: 200px
    }
}

.container>.navbar-header,
.container>.navbar-collapse,
.container-fluid>.navbar-header,
.container-fluid>.navbar-collapse {
    margin-right: 0;
    margin-left: 0
}

@media (min-width: 768px) {
    .container>.navbar-header,
    .container>.navbar-collapse,
    .container-fluid>.navbar-header,
    .container-fluid>.navbar-collapse {
        margin-right: 0;
        margin-left: 0
    }
}

.navbar-static-top {
    z-index: 1000;
    border-width: 0 0 1px
}

@media (min-width: 768px) {
    .navbar-static-top {
        border-radius: 0
    }
}

.navbar-fixed-top,
.navbar-fixed-bottom {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030
}

@media (min-width: 768px) {
    .navbar-fixed-top,
    .navbar-fixed-bottom {
        border-radius: 0
    }
}

.navbar-fixed-top {
    top: 0;
    border-width: 0 0 1px
}

.navbar-fixed-bottom {
    bottom: 0;
    margin-bottom: 0;
    border-width: 1px 0 0
}

.navbar-brand {
    float: left;
    padding: 15px 15px;
    font-size: 18px;
    line-height: 20px;
    height: 50px
}

.navbar-brand:hover,
.navbar-brand:focus {
    text-decoration: none
}

.navbar-brand>img {
    display: block
}

@media (min-width: 768px) {
    .navbar>.container .navbar-brand,
    .navbar>.container-fluid .navbar-brand {
        margin-left: -15px
    }
}

.navbar-toggle {
    position: relative;
    float: right;
    margin-right: 0;
    padding: 9px 10px;
    margin-top: 8px;
    margin-bottom: 8px;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 0px
}

@media screen and (max-width: 420px) {
    .navbar-toggle {
        padding: 1px 6px
    }
}

.navbar-toggle:focus {
    outline: 0
}

.navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px;
    background: #fff
}

.navbar-toggle .icon-bar+.icon-bar {
    margin-top: 4px
}

@media (min-width: 768px) {
    .navbar-toggle {
        display: none
    }
}

.navbar-toggle .icon-bar:nth-of-type(2) {
    top: 1px
}

.navbar-toggle .icon-bar:nth-of-type(3) {
    top: 2px
}

.navbar-toggle .icon-bar {
    position: relative;
    transition: all 500ms ease-in-out
}

.navbar-toggle.active .icon-bar:nth-of-type(1) {
    top: 6px;
    transform: rotate(45deg)
}

.navbar-toggle.active .icon-bar:nth-of-type(2) {
    background-color: transparent
}

.navbar-toggle.active .icon-bar:nth-of-type(3) {
    top: -6px;
    transform: rotate(-45deg)
}

.navbar-nav {
    margin: 7.5px -15px
}

@media screen and (max-width: 768px) {
    .navbar-nav {
        margin: 0
    }
}

.navbar-nav>li.mnav {
    display: none
}

@media screen and (max-width: 767px) {
    .navbar-nav>li.mnav {
        display: block
    }
}

.navbar-nav>li>a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 18px;
    padding-right: 18px
}

@media (max-width: 767px) {
    .navbar-nav .open .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none
    }
}

@media screen and (max-width: 767px) and (max-width: 767px) {
    .navbar-nav .open .dropdown-menu {
        background-color: #99e5e1
    }
}

@media (max-width: 767px) {
    .navbar-nav .open .dropdown-menu>li>a,
    .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 15px 5px 25px
    }
    .navbar-nav .open .dropdown-menu>li>a {
        line-height: 20px
    }
    .navbar-nav .open .dropdown-menu>li>a:hover,
    .navbar-nav .open .dropdown-menu>li>a:focus {
        background-image: none
    }
}

@media (min-width: 768px) {
    .navbar-nav {
        float: left;
        margin: 0
    }
    .navbar-nav>li {
        float: left
    }
    .navbar-nav>li>a {
        padding-top: 15px;
        padding-bottom: 15px;
        border-right: 1px solid #475f82
    }
    .navbar-nav>li:last-child>a {
        border-right: none
    }
}

.navbar-form {
    margin-left: -15px;
    margin-right: -15px;
    padding: 10px 15px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
    margin-top: 8px;
    margin-bottom: 8px
}

@media (min-width: 768px) {
    .navbar-form .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle
    }
    .navbar-form .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle
    }
    .navbar-form .form-control-static {
        display: inline-block
    }
    .navbar-form .input-group {
        display: inline-table;
        vertical-align: middle
    }
    .navbar-form .input-group .input-group-addon,
    .navbar-form .input-group .input-group-btn,
    .navbar-form .input-group .form-control {
        width: auto
    }
    .navbar-form .input-group>.form-control {
        width: 100%
    }
    .navbar-form .control-label {
        margin-bottom: 0;
        vertical-align: middle
    }
    .navbar-form .radio,
    .navbar-form .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle
    }
    .navbar-form .radio label,
    .navbar-form .checkbox label {
        padding-left: 0
    }
    .navbar-form .radio input[type="radio"],
    .navbar-form .checkbox input[type="checkbox"] {
        position: relative;
        margin-left: 0
    }
    .navbar-form .has-feedback .form-control-feedback {
        top: 0
    }
}

@media (max-width: 767px) {
    .navbar-form .form-group {
        margin-bottom: 5px
    }
    .navbar-form .form-group:last-child {
        margin-bottom: 0
    }
}

@media (min-width: 768px) {
    .navbar-form {
        width: auto;
        border: 0;
        margin-left: 0;
        margin-right: 0;
        padding-top: 0;
        padding-bottom: 0;
        -webkit-box-shadow: none;
        box-shadow: none
    }
}

.navbar-nav>li>.dropdown-menu {
    margin-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.navbar-fixed-bottom .navbar-nav>li>.dropdown-menu {
    margin-bottom: 0;
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.navbar-btn {
    margin-top: 8px;
    margin-bottom: 8px
}

.navbar-btn.btn-sm,
.btn-group-sm>.navbar-btn.btn {
    margin-top: 10px;
    margin-bottom: 10px
}

.navbar-btn.btn-xs,
.btn-group-xs>.navbar-btn.btn {
    margin-top: 14px;
    margin-bottom: 14px
}

.navbar-text {
    margin-top: 15px;
    margin-bottom: 15px
}

@media (min-width: 768px) {
    .navbar-text {
        float: left;
        margin-left: 15px;
        margin-right: 15px
    }
}

@media (min-width: 768px) {
    .navbar-left {
        float: left !important
    }
    .navbar-right {
        float: right !important;
        margin-right: -15px
    }
    .navbar-right ~ .navbar-right {
        margin-right: 0
    }
}

.navbar-default {
    background-color: #2c4e86;
    border-color: #243f6d
}

.navbar-default .navbar-brand {
    color: #fff
}

.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
    color: #e6e6e6;
    background-color: transparent
}

.navbar-default .navbar-text {
    color: #777
}

.navbar-default .navbar-nav>li>a {
    color: #fff;
    font-family: verdana;
    font-size: 14px
}

.navbar-default .navbar-nav>li>a:hover,
.navbar-default .navbar-nav>li>a:focus {
    color: #17c4bb;
    background-color: transparent
}

.navbar-default .navbar-nav>.active>a {
    padding-top: 15px;
    padding-bottom: 16px;
    margin-bottom: 0px;
    margin-top: 0px
}

.navbar-default .navbar-nav>.active>a,
.navbar-default .navbar-nav>.active>a:hover,
.navbar-default .navbar-nav>.active>a:focus {
    color: #0b1e59;
    background-color: #17c4bb
}

.navbar-default .navbar-nav>.disabled>a,
.navbar-default .navbar-nav>.disabled>a:hover,
.navbar-default .navbar-nav>.disabled>a:focus {
    color: #ccc;
    background-color: transparent
}

.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
    border-color: #ddd
}

.navbar-default .navbar-toggle .icon-bar {
    background-color: #fff
}

.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
    border-color: #243f6d
}

.navbar-default .navbar-nav>.open>a,
.navbar-default .navbar-nav>.open>a:hover,
.navbar-default .navbar-nav>.open>a:focus {
    background-color: #ebf0f2;
    color: #0b1e59
}

@media screen and (max-width: 767px) {
    .navbar-default .navbar-nav>.open>a,
    .navbar-default .navbar-nav>.open>a:hover,
    .navbar-default .navbar-nav>.open>a:focus {
        color: #0b1e59;
        background-color: #99e5e1;
        border-bottom: 1px solid #0b1e59
    }
}

@media (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu>li>a {
        color: #fff
    }
}

@media screen and (max-width: 767px) and (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu>li>a {
        color: #0B1E59
    }
}

@media (max-width: 767px) {
    .navbar-default .navbar-nav .open .dropdown-menu>li>a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu>li>a:focus {
        color: #17c4bb;
        background-color: transparent
    }
    .navbar-default .navbar-nav .open .dropdown-menu>.active>a,
    .navbar-default .navbar-nav .open .dropdown-menu>.active>a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu>.active>a:focus {
        color: #0b1e59;
        background-color: #17c4bb
    }
    .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a,
    .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:hover,
    .navbar-default .navbar-nav .open .dropdown-menu>.disabled>a:focus {
        color: #ccc;
        background-color: transparent
    }
}

.navbar-default .navbar-link {
    color: #fff
}

.navbar-default .navbar-link:hover {
    color: #17c4bb
}

.navbar-default .btn-link {
    color: #fff
}

.navbar-default .btn-link:hover,
.navbar-default .btn-link:focus {
    color: #17c4bb
}

.navbar-default .btn-link[disabled]:hover,
.navbar-default .btn-link[disabled]:focus,
fieldset[disabled] .navbar-default .btn-link:hover,
fieldset[disabled] .navbar-default .btn-link:focus {
    color: #ccc
}

.navbar-inverse {
    background-color: #222;
    border-color: #090909
}

.navbar-inverse .navbar-brand {
    color: #9d9d9d
}

.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-brand:focus {
    color: #fff;
    background-color: transparent
}

.navbar-inverse .navbar-text {
    color: #9d9d9d
}

.navbar-inverse .navbar-nav>li>a {
    color: #9d9d9d
}

.navbar-inverse .navbar-nav>li>a:hover,
.navbar-inverse .navbar-nav>li>a:focus {
    color: #fff;
    background-color: transparent
}

.navbar-inverse .navbar-nav>.active>a,
.navbar-inverse .navbar-nav>.active>a:hover,
.navbar-inverse .navbar-nav>.active>a:focus {
    color: #fff;
    background-color: #090909
}

.navbar-inverse .navbar-nav>.disabled>a,
.navbar-inverse .navbar-nav>.disabled>a:hover,
.navbar-inverse .navbar-nav>.disabled>a:focus {
    color: #444;
    background-color: transparent
}

.navbar-inverse .navbar-toggle {
    border-color: #333
}

.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
    background-color: #333
}

.navbar-inverse .navbar-toggle .icon-bar {
    background-color: #fff
}

.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
    border-color: #101010
}

.navbar-inverse .navbar-nav>.open>a,
.navbar-inverse .navbar-nav>.open>a:hover,
.navbar-inverse .navbar-nav>.open>a:focus {
    background-color: #090909;
    color: #fff
}

@media (max-width: 767px) {
    .navbar-inverse .navbar-nav .open .dropdown-menu>.dropdown-header {
        border-color: #090909
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
        background-color: #090909
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a {
        color: #9d9d9d
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:hover,
    .navbar-inverse .navbar-nav .open .dropdown-menu>li>a:focus {
        color: #fff;
        background-color: transparent
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:hover,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.active>a:focus {
        color: #fff;
        background-color: #090909
    }
    .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:hover,
    .navbar-inverse .navbar-nav .open .dropdown-menu>.disabled>a:focus {
        color: #444;
        background-color: transparent
    }
}

.navbar-inverse .navbar-link {
    color: #9d9d9d
}

.navbar-inverse .navbar-link:hover {
    color: #fff
}

.navbar-inverse .btn-link {
    color: #9d9d9d
}

.navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link:focus {
    color: #fff
}

.navbar-inverse .btn-link[disabled]:hover,
.navbar-inverse .btn-link[disabled]:focus,
fieldset[disabled] .navbar-inverse .btn-link:hover,
fieldset[disabled] .navbar-inverse .btn-link:focus {
    color: #444
}

.breadcrumb {
    padding: 8px 15px 8px 0;
    margin-bottom: 0px;
    list-style: none;
    border-radius: 0px
}

.breadcrumb>li {
    display: inline-block
}

.breadcrumb>li+li:before {
    font-family: FontAwesome;
    content: "\f105";
    padding: 0 5px;
    color: #ccc
}

.breadcrumb>.active {
    color: #777
}

@media screen and (max-width: 350px) {
    .breadcrumb {
        display: none
    }
}

.lang {
    padding: 8px 0;
    float: right;
    cursor: pointer
}

.lang span:before {
    font: normal normal normal 14px/1 FontAwesome;
    content: "\f0ac";
    margin-right: 5px
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 0px
}

.pagination>li {
    display: inline
}

.pagination>li>a,
.pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    line-height: 1.428571429;
    text-decoration: none;
    color: #337ab7;
    background-color: #fff;
    border: 1px solid #ddd;
    margin-left: -1px
}

.pagination>li:first-child>a,
.pagination>li:first-child>span {
    margin-left: 0;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px
}

.pagination>li:last-child>a,
.pagination>li:last-child>span {
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px
}

.pagination>li>a:hover,
.pagination>li>a:focus,
.pagination>li>span:hover,
.pagination>li>span:focus {
    z-index: 2;
    color: #23527c;
    background-color: #eee;
    border-color: #ddd
}

.pagination>.active>a,
.pagination>.active>a:hover,
.pagination>.active>a:focus,
.pagination>.active>span,
.pagination>.active>span:hover,
.pagination>.active>span:focus {
    z-index: 3;
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
    cursor: default
}

.pagination>.disabled>span,
.pagination>.disabled>span:hover,
.pagination>.disabled>span:focus,
.pagination>.disabled>a,
.pagination>.disabled>a:hover,
.pagination>.disabled>a:focus {
    color: #777;
    background-color: #fff;
    border-color: #ddd;
    cursor: not-allowed
}

.pagination-lg>li>a,
.pagination-lg>li>span {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.3333333
}

.pagination-lg>li:first-child>a,
.pagination-lg>li:first-child>span {
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px
}

.pagination-lg>li:last-child>a,
.pagination-lg>li:last-child>span {
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px
}

.pagination-sm>li>a,
.pagination-sm>li>span {
    padding: 5px 10px;
    font-size: 12px;
    line-height: 1.5
}

.pagination-sm>li:first-child>a,
.pagination-sm>li:first-child>span {
    border-bottom-left-radius: 0px;
    border-top-left-radius: 0px
}

.pagination-sm>li:last-child>a,
.pagination-sm>li:last-child>span {
    border-bottom-right-radius: 0px;
    border-top-right-radius: 0px
}

.pager {
    padding-left: 0;
    margin: 20px 0;
    list-style: none;
    text-align: center
}

.pager:before,
.pager:after {
    content: " ";
    display: table
}

.pager:after {
    clear: both
}

.pager li {
    display: inline
}

.pager li>a,
.pager li>span {
    display: inline-block;
    padding: 5px 14px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 15px
}

.pager li>a:hover,
.pager li>a:focus {
    text-decoration: none;
    background-color: #eee
}

.pager .next>a,
.pager .next>span {
    float: right
}

.pager .previous>a,
.pager .previous>span {
    float: left
}

.pager .disabled>a,
.pager .disabled>a:hover,
.pager .disabled>a:focus,
.pager .disabled>span {
    color: #777;
    background-color: #fff;
    cursor: not-allowed
}

.label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: bold;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

.label:empty {
    display: none
}

.btn .label {
    position: relative;
    top: -1px
}

a.label:hover,
a.label:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.label-default {
    background-color: #777
}

.label-default[href]:hover,
.label-default[href]:focus {
    background-color: #5e5e5e
}

.label-primary {
    background-color: #337ab7
}

.label-primary[href]:hover,
.label-primary[href]:focus {
    background-color: #286090
}

.label-success {
    background-color: #5cb85c
}

.label-success[href]:hover,
.label-success[href]:focus {
    background-color: #449d44
}

.label-info {
    background-color: #5bc0de
}

.label-info[href]:hover,
.label-info[href]:focus {
    background-color: #31b0d5
}

.label-warning {
    background-color: #f0ad4e
}

.label-warning[href]:hover,
.label-warning[href]:focus {
    background-color: #ec971f
}

.label-danger {
    background-color: #d9534f
}

.label-danger[href]:hover,
.label-danger[href]:focus {
    background-color: #c9302c
}

.badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: bold;
    color: #fff;
    line-height: 1;
    vertical-align: middle;
    white-space: nowrap;
    text-align: center;
    background-color: #777;
    border-radius: 10px
}

.badge:empty {
    display: none
}

.btn .badge {
    position: relative;
    top: -1px
}

.btn-xs .badge,
.btn-group-xs>.btn .badge,
.btn-group-xs>.btn .badge {
    top: 0;
    padding: 1px 5px
}

.list-group-item.active>.badge,
.nav-pills>.active>a>.badge {
    color: #337ab7;
    background-color: #fff
}

.list-group-item>.badge {
    float: right
}

.list-group-item>.badge+.badge {
    margin-right: 5px
}

.nav-pills>li>a>.badge {
    margin-left: 3px
}

a.badge:hover,
a.badge:focus {
    color: #fff;
    text-decoration: none;
    cursor: pointer
}

.jumbotron {
    padding-top: 30px;
    padding-bottom: 30px;
    margin-bottom: 30px;
    color: inherit;
    background-color: #eee
}

.jumbotron h1,
.jumbotron .h1 {
    color: inherit
}

.jumbotron p {
    margin-bottom: 15px;
    font-size: 21px;
    font-weight: 200
}

.jumbotron>hr {
    border-top-color: #d5d5d5
}

.container .jumbotron,
.container-fluid .jumbotron {
    border-radius: 0px;
    padding-left: 15px;
    padding-right: 15px
}

.jumbotron .container {
    max-width: 100%
}

@media screen and (min-width: 768px) {
    .jumbotron {
        padding-top: 48px;
        padding-bottom: 48px
    }
    .container .jumbotron,
    .container-fluid .jumbotron {
        padding-left: 60px;
        padding-right: 60px
    }
    .jumbotron h1,
    .jumbotron .h1 {
        font-size: 63px
    }
}

.thumbnail {
    display: block;
    padding: 4px;
    margin-bottom: 20px;
    line-height: 1.428571429;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 0px;
    -webkit-transition: border 0.2s ease-in-out;
    -o-transition: border 0.2s ease-in-out;
    transition: border 0.2s ease-in-out
}

.thumbnail>img,
.thumbnail a>img {
    display: block;
    max-width: 100%;
    height: auto;
    margin-left: auto;
    margin-right: auto
}

.thumbnail .caption {
    padding: 9px;
    color: #212121
}

a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
    border-color: #337ab7
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 0px
}

.alert h4 {
    margin-top: 0;
    color: inherit
}

.alert .alert-link {
    font-weight: bold
}

.alert>p,
.alert>ul {
    margin-bottom: 0
}

.alert>p+p {
    margin-top: 5px
}

.alert-dismissable,
.alert-dismissible {
    padding-right: 35px
}

.alert-dismissable .close,
.alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit
}

.alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d
}

.alert-success hr {
    border-top-color: #c9e2b3
}

.alert-success .alert-link {
    color: #2b542c
}

.alert-info {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f
}

.alert-info hr {
    border-top-color: #a6e1ec
}

.alert-info .alert-link {
    color: #245269
}

.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b
}

.alert-warning hr {
    border-top-color: #f7e1b5
}

.alert-warning .alert-link {
    color: #66512c
}

.alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442
}

.alert-danger hr {
    border-top-color: #e4b9c0
}

.alert-danger .alert-link {
    color: #843534
}

@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 40px 0
    }
    to {
        background-position: 0 0
    }
}

@keyframes progress-bar-stripes {
    from {
        background-position: 40px 0
    }
    to {
        background-position: 0 0
    }
}

.progress {
    overflow: hidden;
    height: 20px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1)
}

.progress-bar {
    float: left;
    width: 0%;
    height: 100%;
    font-size: 12px;
    line-height: 20px;
    color: #fff;
    text-align: center;
    background-color: #337ab7;
    -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -webkit-transition: width 0.6s ease;
    -o-transition: width 0.6s ease;
    transition: width 0.6s ease
}

.progress-striped .progress-bar,
.progress-bar-striped {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px
}

.progress.active .progress-bar,
.progress-bar.active {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    -o-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite
}

.progress-bar-success {
    background-color: #5cb85c
}

.progress-striped .progress-bar-success {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.progress-bar-info {
    background-color: #5bc0de
}

.progress-striped .progress-bar-info {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.progress-bar-warning {
    background-color: #f0ad4e
}

.progress-striped .progress-bar-warning {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.progress-bar-danger {
    background-color: #d9534f
}

.progress-striped .progress-bar-danger {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent)
}

.media {
    margin-top: 15px
}

.media:first-child {
    margin-top: 0
}

.media,
.media-body {
    zoom: 1;
    overflow: hidden
}

.media-body {
    width: 10000px
}

.media-object {
    display: block
}

.media-object.img-thumbnail {
    max-width: none
}

.media-right,
.media>.pull-right {
    padding-left: 10px
}

.media-left,
.media>.pull-left {
    padding-right: 10px
}

.media-left,
.media-right,
.media-body {
    display: table-cell;
    vertical-align: top
}

.media-middle {
    vertical-align: middle
}

.media-bottom {
    vertical-align: bottom
}

.media-heading {
    margin-top: 0;
    margin-bottom: 5px
}

.media-list {
    padding-left: 0;
    list-style: none
}

.list-group {
    margin-bottom: 20px;
    padding-left: 0
}

.list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #fff;
    border: 1px solid #ddd
}

.list-group-item:first-child {
    border-top-right-radius: 0px;
    border-top-left-radius: 0px
}

.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px
}

a.list-group-item,
button.list-group-item {
    color: #555
}

a.list-group-item .list-group-item-heading,
button.list-group-item .list-group-item-heading {
    color: #333
}

a.list-group-item:hover,
a.list-group-item:focus,
button.list-group-item:hover,
button.list-group-item:focus {
    text-decoration: none;
    color: #555;
    background-color: #f5f5f5
}

button.list-group-item {
    width: 100%;
    text-align: left
}

.list-group-item.disabled,
.list-group-item.disabled:hover,
.list-group-item.disabled:focus {
    background-color: #eee;
    color: #777;
    cursor: not-allowed
}

.list-group-item.disabled .list-group-item-heading,
.list-group-item.disabled:hover .list-group-item-heading,
.list-group-item.disabled:focus .list-group-item-heading {
    color: inherit
}

.list-group-item.disabled .list-group-item-text,
.list-group-item.disabled:hover .list-group-item-text,
.list-group-item.disabled:focus .list-group-item-text {
    color: #777
}

.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
    z-index: 2;
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7
}

.list-group-item.active .list-group-item-heading,
.list-group-item.active .list-group-item-heading>small,
.list-group-item.active .list-group-item-heading>.small,
.list-group-item.active:hover .list-group-item-heading,
.list-group-item.active:hover .list-group-item-heading>small,
.list-group-item.active:hover .list-group-item-heading>.small,
.list-group-item.active:focus .list-group-item-heading,
.list-group-item.active:focus .list-group-item-heading>small,
.list-group-item.active:focus .list-group-item-heading>.small {
    color: inherit
}

.list-group-item.active .list-group-item-text,
.list-group-item.active:hover .list-group-item-text,
.list-group-item.active:focus .list-group-item-text {
    color: #c7ddef
}

.list-group-item-success {
    color: #3c763d;
    background-color: #dff0d8
}

a.list-group-item-success,
button.list-group-item-success {
    color: #3c763d
}

a.list-group-item-success .list-group-item-heading,
button.list-group-item-success .list-group-item-heading {
    color: inherit
}

a.list-group-item-success:hover,
a.list-group-item-success:focus,
button.list-group-item-success:hover,
button.list-group-item-success:focus {
    color: #3c763d;
    background-color: #d0e9c6
}

a.list-group-item-success.active,
a.list-group-item-success.active:hover,
a.list-group-item-success.active:focus,
button.list-group-item-success.active,
button.list-group-item-success.active:hover,
button.list-group-item-success.active:focus {
    color: #fff;
    background-color: #3c763d;
    border-color: #3c763d
}

.list-group-item-info {
    color: #31708f;
    background-color: #d9edf7
}

a.list-group-item-info,
button.list-group-item-info {
    color: #31708f
}

a.list-group-item-info .list-group-item-heading,
button.list-group-item-info .list-group-item-heading {
    color: inherit
}

a.list-group-item-info:hover,
a.list-group-item-info:focus,
button.list-group-item-info:hover,
button.list-group-item-info:focus {
    color: #31708f;
    background-color: #c4e3f3
}

a.list-group-item-info.active,
a.list-group-item-info.active:hover,
a.list-group-item-info.active:focus,
button.list-group-item-info.active,
button.list-group-item-info.active:hover,
button.list-group-item-info.active:focus {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f
}

.list-group-item-warning {
    color: #8a6d3b;
    background-color: #fcf8e3
}

a.list-group-item-warning,
button.list-group-item-warning {
    color: #8a6d3b
}

a.list-group-item-warning .list-group-item-heading,
button.list-group-item-warning .list-group-item-heading {
    color: inherit
}

a.list-group-item-warning:hover,
a.list-group-item-warning:focus,
button.list-group-item-warning:hover,
button.list-group-item-warning:focus {
    color: #8a6d3b;
    background-color: #faf2cc
}

a.list-group-item-warning.active,
a.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus,
button.list-group-item-warning.active,
button.list-group-item-warning.active:hover,
button.list-group-item-warning.active:focus {
    color: #fff;
    background-color: #8a6d3b;
    border-color: #8a6d3b
}

.list-group-item-danger {
    color: #a94442;
    background-color: #f2dede
}

a.list-group-item-danger,
button.list-group-item-danger {
    color: #a94442
}

a.list-group-item-danger .list-group-item-heading,
button.list-group-item-danger .list-group-item-heading {
    color: inherit
}

a.list-group-item-danger:hover,
a.list-group-item-danger:focus,
button.list-group-item-danger:hover,
button.list-group-item-danger:focus {
    color: #a94442;
    background-color: #ebcccc
}

a.list-group-item-danger.active,
a.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus,
button.list-group-item-danger.active,
button.list-group-item-danger.active:hover,
button.list-group-item-danger.active:focus {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442
}

.list-group-item-heading {
    margin-top: 0;
    margin-bottom: 5px
}

.list-group-item-text {
    margin-bottom: 0;
    line-height: 1.3
}

.panel {
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 0px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05)
}

.panel-body {
    padding: 15px
}

.panel-body:before,
.panel-body:after {
    content: " ";
    display: table
}

.panel-body:after {
    clear: both
}

.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-right-radius: -1px;
    border-top-left-radius: -1px
}

.panel-heading>.dropdown .dropdown-toggle {
    color: inherit
}

.panel-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 16px;
    color: inherit
}

.panel-title>a,
.panel-title>small,
.panel-title>.small,
.panel-title>small>a,
.panel-title>.small>a {
    color: inherit
}

.panel-footer {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
    border-bottom-right-radius: -1px;
    border-bottom-left-radius: -1px
}

.panel>.list-group,
.panel>.panel-collapse>.list-group {
    margin-bottom: 0
}

.panel>.list-group .list-group-item,
.panel>.panel-collapse>.list-group .list-group-item {
    border-width: 1px 0;
    border-radius: 0
}

.panel>.list-group:first-child .list-group-item:first-child,
.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child {
    border-top: 0;
    border-top-right-radius: -1px;
    border-top-left-radius: -1px
}

.panel>.list-group:last-child .list-group-item:last-child,
.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child {
    border-bottom: 0;
    border-bottom-right-radius: -1px;
    border-bottom-left-radius: -1px
}

.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0
}

.panel-heading+.list-group .list-group-item:first-child {
    border-top-width: 0
}

.list-group+.panel-footer {
    border-top-width: 0
}

.panel>.table,
.panel>.table-responsive>.table,
.panel>.panel-collapse>.table {
    margin-bottom: 0
}

.panel>.table caption,
.panel>.table-responsive>.table caption,
.panel>.panel-collapse>.table caption {
    padding-left: 15px;
    padding-right: 15px
}

.panel>.table:first-child,
.panel>.table-responsive:first-child>.table:first-child {
    border-top-right-radius: -1px;
    border-top-left-radius: -1px
}

.panel>.table:first-child>thead:first-child>tr:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child {
    border-top-left-radius: -1px;
    border-top-right-radius: -1px
}

.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child {
    border-top-left-radius: -1px
}

.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,
.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child {
    border-top-right-radius: -1px
}

.panel>.table:last-child,
.panel>.table-responsive:last-child>.table:last-child {
    border-bottom-right-radius: -1px;
    border-bottom-left-radius: -1px
}

.panel>.table:last-child>tbody:last-child>tr:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child {
    border-bottom-left-radius: -1px;
    border-bottom-right-radius: -1px
}

.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child {
    border-bottom-left-radius: -1px
}

.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,
.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child {
    border-bottom-right-radius: -1px
}

.panel>.panel-body+.table,
.panel>.panel-body+.table-responsive,
.panel>.table+.panel-body,
.panel>.table-responsive+.panel-body {
    border-top: 1px solid #ddd
}

.panel>.table>tbody:first-child>tr:first-child th,
.panel>.table>tbody:first-child>tr:first-child td {
    border-top: 0
}

.panel>.table-bordered,
.panel>.table-responsive>.table-bordered {
    border: 0
}

.panel>.table-bordered>thead>tr>th:first-child,
.panel>.table-bordered>thead>tr>td:first-child,
.panel>.table-bordered>tbody>tr>th:first-child,
.panel>.table-bordered>tbody>tr>td:first-child,
.panel>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-bordered>tfoot>tr>td:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child {
    border-left: 0
}

.panel>.table-bordered>thead>tr>th:last-child,
.panel>.table-bordered>thead>tr>td:last-child,
.panel>.table-bordered>tbody>tr>th:last-child,
.panel>.table-bordered>tbody>tr>td:last-child,
.panel>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-bordered>tfoot>tr>td:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,
.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,
.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child {
    border-right: 0
}

.panel>.table-bordered>thead>tr:first-child>td,
.panel>.table-bordered>thead>tr:first-child>th,
.panel>.table-bordered>tbody>tr:first-child>td,
.panel>.table-bordered>tbody>tr:first-child>th,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,
.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th {
    border-bottom: 0
}

.panel>.table-bordered>tbody>tr:last-child>td,
.panel>.table-bordered>tbody>tr:last-child>th,
.panel>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-bordered>tfoot>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,
.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th {
    border-bottom: 0
}

.panel>.table-responsive {
    border: 0;
    margin-bottom: 0
}

.panel-group {
    margin-bottom: 20px
}

.panel-group .panel {
    margin-bottom: 0;
    border-radius: 0px
}

.panel-group .panel+.panel {
    margin-top: 5px
}

.panel-group .panel-heading {
    border-bottom: 0
}

.panel-group .panel-heading+.panel-collapse>.panel-body,
.panel-group .panel-heading+.panel-collapse>.list-group {
    border-top: 1px solid #ddd
}

.panel-group .panel-footer {
    border-top: 0
}

.panel-group .panel-footer+.panel-collapse .panel-body {
    border-bottom: 1px solid #ddd
}

.panel-default {
    border-color: #ddd
}

.panel-default>.panel-heading {
    color: #333;
    background-color: #f5f5f5;
    border-color: #ddd
}

.panel-default>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #ddd
}

.panel-default>.panel-heading .badge {
    color: #f5f5f5;
    background-color: #333
}

.panel-default>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #ddd
}

.panel-primary {
    border-color: #337ab7
}

.panel-primary>.panel-heading {
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7
}

.panel-primary>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #337ab7
}

.panel-primary>.panel-heading .badge {
    color: #337ab7;
    background-color: #fff
}

.panel-primary>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #337ab7
}

.panel-success {
    border-color: #d6e9c6
}

.panel-success>.panel-heading {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6
}

.panel-success>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #d6e9c6
}

.panel-success>.panel-heading .badge {
    color: #dff0d8;
    background-color: #3c763d
}

.panel-success>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #d6e9c6
}

.panel-info {
    border-color: #bce8f1
}

.panel-info>.panel-heading {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1
}

.panel-info>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #bce8f1
}

.panel-info>.panel-heading .badge {
    color: #d9edf7;
    background-color: #31708f
}

.panel-info>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #bce8f1
}

.panel-warning {
    border-color: #faebcc
}

.panel-warning>.panel-heading {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc
}

.panel-warning>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #faebcc
}

.panel-warning>.panel-heading .badge {
    color: #fcf8e3;
    background-color: #8a6d3b
}

.panel-warning>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #faebcc
}

.panel-danger {
    border-color: #ebccd1
}

.panel-danger>.panel-heading {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1
}

.panel-danger>.panel-heading+.panel-collapse>.panel-body {
    border-top-color: #ebccd1
}

.panel-danger>.panel-heading .badge {
    color: #f2dede;
    background-color: #a94442
}

.panel-danger>.panel-footer+.panel-collapse>.panel-body {
    border-bottom-color: #ebccd1
}

.embed-responsive {
    position: relative;
    display: block;
    height: 0;
    padding: 0;
    overflow: hidden
}

.embed-responsive .embed-responsive-item,
.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    border: 0
}

.embed-responsive-16by9 {
    padding-bottom: 56.25%
}

.embed-responsive-4by3 {
    padding-bottom: 75%
}

.well {
    min-height: 20px;
    padding: 19px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05)
}

.well blockquote {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, 0.15)
}

.well-lg {
    padding: 24px;
    border-radius: 0px
}

.well-sm {
    padding: 9px;
    border-radius: 0px
}

.close {
    float: right;
    font-size: 21px;
    font-weight: bold;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.2;
    filter: alpha(opacity=20)
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
    opacity: 0.5;
    filter: alpha(opacity=50)
}

button.close {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none
}

.modal-open {
    overflow: hidden
}

.modal {
    display: none;
    overflow: hidden;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    -webkit-overflow-scrolling: touch;
    outline: 0
}

.modal.fade .modal-dialog {
    -webkit-transform: translate(0, -25%);
    -ms-transform: translate(0, -25%);
    -o-transform: translate(0, -25%);
    transform: translate(0, -25%);
    -webkit-transition: -webkit-transform 0.3s ease-out;
    -moz-transition: -moz-transform 0.3s ease-out;
    -o-transition: -o-transform 0.3s ease-out;
    transition: transform 0.3s ease-out
}

.modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0)
}

.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
    z-index: 9999999999
}

.modal-dialog.sweet .modal-content h2 {
    color: #575757;
    font-size: 30px;
    text-align: center;
    font-weight: 600;
    text-transform: none;
    position: relative;
    margin: 10px 0;
    padding: 0;
    line-height: 40px;
    display: block
}

.modal-dialog.sweet .modal-content .m-icon {
    width: 60px;
    height: 60px;
    border: 4px solid gray;
    -webkit-border-radius: 40px;
    border-radius: 40px;
    border-radius: 50%;
    margin: 20px auto;
    padding: 0;
    position: relative;
    box-sizing: content-box
}

.modal-dialog.sweet .modal-content .m-warning {
    border-color: #F8BB86
}

.modal-dialog.sweet .modal-content .m-warning .micon-body {
    position: absolute;
    width: 5px;
    height: 30px;
    left: 50%;
    top: 10px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    margin-left: -2px;
    background-color: #F8BB86
}

.modal-dialog.sweet .modal-content .m-warning .micon-dot {
    position: absolute;
    width: 7px;
    height: 7px;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    margin-left: -3px;
    left: 50%;
    bottom: 10px;
    background-color: #F8BB86
}

.modal-dialog.sweet .modal-content .m-error {
    border-color: #F27474
}

.modal-dialog.sweet .modal-content .m-error .x-mark {
    position: relative;
    display: block
}

.modal-dialog.sweet .modal-content .m-error .x-mark .m-line {
    position: absolute;
    height: 5px;
    width: 45px;
    background-color: #F27474;
    display: block;
    top: 28px;
    border-radius: 2px
}

.modal-dialog.sweet .modal-content .m-error .x-mark .m-left {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -ms-transform: rotate(45deg) \9;
    left: 8px
}

.modal-dialog.sweet .modal-content .m-error .x-mark .m-right {
    -webkit-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg) \9;
    transform: rotate(-45deg);
    right: 8px
}

.modal-dialog.sweet .modal-content .pulseWarning {
    -webkit-animation: pulseWarning 0.75s infinite alternate;
    animation: pulseWarning 0.75s infinite alternate
}

.modal-dialog.sweet .modal-content .pulseWarningIns {
    -webkit-animation: pulseWarningIns 0.75s infinite alternate;
    animation: pulseWarningIns 0.75s infinite alternate
}

.modal-dialog.sweet .modal-footer {
    text-align: center;
    border-top: none;
    padding: 0
}

.modal-content {
    position: relative;
    background-color: #fff;
    border: 1px solid #999;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0px;
    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    background-clip: padding-box;
    outline: 0
}

.modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000
}

.modal-backdrop.fade {
    opacity: 0;
    filter: alpha(opacity=0)
}

.modal-backdrop.in {
    opacity: 0.5;
    filter: alpha(opacity=50)
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5
}

.modal-header:before,
.modal-header:after {
    content: " ";
    display: table
}

.modal-header:after {
    clear: both
}

.modal-header .close {
    margin-top: -2px
}

.modal-title {
    margin: 0;
    line-height: 1.428571429
}

.modal-body {
    position: relative;
    padding: 15px
}

.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5
}

.modal-footer:before,
.modal-footer:after {
    content: " ";
    display: table
}

.modal-footer:after {
    clear: both
}

.modal-footer .btn+.btn {
    margin-left: 5px
}

.modal-footer .btn-group .btn+.btn {
    margin-left: -1px
}

.modal-footer .btn-block+.btn-block {
    margin-left: 0
}

.modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll
}

@media (min-width: 768px) {
    .modal-dialog {
        width: 600px;
        margin: 60px auto
    }
    .modal-content {
        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5)
    }
    .modal-sm {
        width: 300px
    }
}

@media (min-width: 992px) {
    .modal-lg {
        width: 900px
    }
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    font-family: verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.428571429;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: 12px;
    opacity: 0;
    filter: alpha(opacity=0)
}

.tooltip.in {
    opacity: 0.9;
    filter: alpha(opacity=90)
}

.tooltip.top {
    margin-top: -3px;
    padding: 5px 0
}

.tooltip.right {
    margin-left: 3px;
    padding: 0 5px
}

.tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0
}

.tooltip.left {
    margin-left: -3px;
    padding: 0 5px
}

.tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 0px
}

.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.tooltip.top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.top-left .tooltip-arrow {
    bottom: 0;
    right: 5px;
    margin-bottom: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.top-right .tooltip-arrow {
    bottom: 0;
    left: 5px;
    margin-bottom: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000
}

.tooltip.right .tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000
}

.tooltip.left .tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #000
}

.tooltip.bottom .tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.bottom-left .tooltip-arrow {
    top: 0;
    right: 5px;
    margin-top: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.tooltip.bottom-right .tooltip-arrow {
    top: 0;
    left: 5px;
    margin-top: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000
}

.popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: none;
    max-width: 276px;
    padding: 1px;
    font-family: verdana, "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-style: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-break: auto;
    line-height: 1.428571429;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    white-space: normal;
    word-break: normal;
    word-spacing: normal;
    word-wrap: normal;
    font-size: 14px;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2)
}

.popover.top {
    margin-top: -10px
}

.popover.right {
    margin-left: 10px
}

.popover.bottom {
    margin-top: 10px
}

.popover.left {
    margin-left: -10px
}

.popover-title {
    margin: 0;
    padding: 8px 14px;
    font-size: 14px;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-radius: -1px -1px 0 0
}

.popover-content {
    padding: 9px 14px
}

.popover>.arrow,
.popover>.arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid
}

.popover>.arrow {
    border-width: 11px
}

.popover>.arrow:after {
    border-width: 10px;
    content: ""
}

.popover.top>.arrow {
    left: 50%;
    margin-left: -11px;
    border-bottom-width: 0;
    border-top-color: #999;
    border-top-color: rgba(0, 0, 0, 0.25);
    bottom: -11px
}

.popover.top>.arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #fff
}

.popover.right>.arrow {
    top: 50%;
    left: -11px;
    margin-top: -11px;
    border-left-width: 0;
    border-right-color: #999;
    border-right-color: rgba(0, 0, 0, 0.25)
}

.popover.right>.arrow:after {
    content: " ";
    left: 1px;
    bottom: -10px;
    border-left-width: 0;
    border-right-color: #fff
}

.popover.bottom>.arrow {
    left: 50%;
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: #999;
    border-bottom-color: rgba(0, 0, 0, 0.25);
    top: -11px
}

.popover.bottom>.arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #fff
}

.popover.left>.arrow {
    top: 50%;
    right: -11px;
    margin-top: -11px;
    border-right-width: 0;
    border-left-color: #999;
    border-left-color: rgba(0, 0, 0, 0.25)
}

.popover.left>.arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-color: #fff;
    bottom: -10px
}

.carousel {
    position: relative
}

.carousel-inner {
    position: relative;
    overflow: hidden;
    width: 100%
}

.carousel-inner>.item {
    display: none;
    position: relative;
    -webkit-transition: 0.6s ease-in-out left;
    -o-transition: 0.6s ease-in-out left;
    transition: 0.6s ease-in-out left
}

.carousel-inner>.item>img,
.carousel-inner>.item>a>img {
    display: block;
    max-width: 100%;
    height: auto;
    line-height: 1
}

@media all and (transform-3d),
(-webkit-transform-3d) {
    .carousel-inner>.item {
        -webkit-transition: -webkit-transform 0.6s ease-in-out;
        -moz-transition: -moz-transform 0.6s ease-in-out;
        -o-transition: -o-transform 0.6s ease-in-out;
        transition: transform 0.6s ease-in-out;
        -webkit-backface-visibility: hidden;
        -moz-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        -moz-perspective: 1000px;
        perspective: 1000px
    }
    .carousel-inner>.item.next,
    .carousel-inner>.item.active.right {
        -webkit-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        left: 0
    }
    .carousel-inner>.item.prev,
    .carousel-inner>.item.active.left {
        -webkit-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        left: 0
    }
    .carousel-inner>.item.next.left,
    .carousel-inner>.item.prev.right,
    .carousel-inner>.item.active {
        -webkit-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        left: 0
    }
}

.carousel-inner>.active,
.carousel-inner>.next,
.carousel-inner>.prev {
    display: block
}

.carousel-inner>.active {
    left: 0
}

.carousel-inner>.next,
.carousel-inner>.prev {
    position: absolute;
    top: 0;
    width: 100%
}

.carousel-inner>.next {
    left: 100%
}

.carousel-inner>.prev {
    left: -100%
}

.carousel-inner>.next.left,
.carousel-inner>.prev.right {
    left: 0
}

.carousel-inner>.active.left {
    left: -100%
}

.carousel-inner>.active.right {
    left: 100%
}

.carousel-control {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 15%;
    opacity: 0.5;
    filter: alpha(opacity=50);
    font-size: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
    background-color: transparent
}

.carousel-control.left {
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1)
}

.carousel-control.right {
    left: auto;
    right: 0;
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1)
}

.carousel-control:hover,
.carousel-control:focus {
    outline: 0;
    color: #fff;
    text-decoration: none;
    opacity: 0.9;
    filter: alpha(opacity=90)
}

.carousel-control .icon-prev,
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right {
    position: absolute;
    top: 50%;
    margin-top: -10px;
    z-index: 5;
    display: inline-block
}

.carousel-control .icon-prev,
.carousel-control .glyphicon-chevron-left {
    left: 50%;
    margin-left: -10px
}

.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-right {
    right: 50%;
    margin-right: -10px
}

.carousel-control .icon-prev,
.carousel-control .icon-next {
    width: 20px;
    height: 20px;
    line-height: 1;
    font-family: serif
}

.carousel-control .icon-prev:before {
    content: '\2039'
}

.carousel-control .icon-next:before {
    content: '\203a'
}

.carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    z-index: 15;
    width: 60%;
    margin-left: -30%;
    padding-left: 0;
    list-style: none;
    text-align: center
}

.carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #fff;
    border-radius: 10px;
    cursor: pointer;
    background-color: #000 \9;
    background-color: transparent
}

.carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #fff
}

.carousel-caption {
    position: absolute;
    left: 15%;
    right: 15%;
    bottom: 20px;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #fff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6)
}

.carousel-caption .btn {
    text-shadow: none
}

@media screen and (min-width: 768px) {
    .carousel-control .glyphicon-chevron-left,
    .carousel-control .glyphicon-chevron-right,
    .carousel-control .icon-prev,
    .carousel-control .icon-next {
        width: 30px;
        height: 30px;
        margin-top: -10px;
        font-size: 30px
    }
    .carousel-control .glyphicon-chevron-left,
    .carousel-control .icon-prev {
        margin-left: -10px
    }
    .carousel-control .glyphicon-chevron-right,
    .carousel-control .icon-next {
        margin-right: -10px
    }
    .carousel-caption {
        left: 20%;
        right: 20%;
        padding-bottom: 30px
    }
    .carousel-indicators {
        bottom: 20px
    }
}

.clearfix:before,
.clearfix:after {
    content: " ";
    display: table
}

.clearfix:after {
    clear: both
}

.center-block {
    display: block;
    margin-left: auto;
    margin-right: auto
}

.pull-right {
    float: right !important
}

.pull-left {
    float: left !important
}

.hide {
    display: none !important
}

.show {
    display: block !important
}

.invisible {
    visibility: hidden
}

.text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0
}

.hidden {
    display: none !important
}

.affix {
    position: fixed
}

.success-loading,
.modal-dialog.sweet .modal-content .m-success {
    border-color: #A5DC86
}

.success-loading:before,
.modal-dialog.sweet .modal-content .m-success:before {
    -webkit-border-radius: 120px 0 0 120px;
    border-radius: 120px 0 0 120px;
    top: -17px;
    left: -44px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform-origin: 60px 60px;
    transform-origin: 60px 60px
}

.success-loading:after,
.modal-dialog.sweet .modal-content .m-success:after {
    -webkit-border-radius: 0 120px 120px 0;
    border-radius: 0 120px 120px 0;
    top: -23px;
    left: 18px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -webkit-transform-origin: 0px 60px;
    transform-origin: 0px 60px
}

.success-loading:before,
.modal-dialog.sweet .modal-content .m-success:before,
.success-loading:after,
.modal-dialog.sweet .modal-content .m-success:after {
    content: '';
    position: absolute;
    width: 62px;
    height: 114px;
    background: white;
    -webkit-transform: rotate(45deg);
    transform: rotate(-44deg)
}

.success-loading .m-line,
.modal-dialog.sweet .modal-content .m-success .m-line {
    height: 5px;
    background-color: #A5DC86;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 2
}

.success-loading .m-long,
.modal-dialog.sweet .modal-content .m-success .m-long {
    width: 39px;
    left: 16px;
    top: 30px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    -ms-transform: rotate(-45deg) \9
}

.success-loading .m-tip,
.modal-dialog.sweet .modal-content .m-success .m-tip {
    width: 19px;
    left: 8px;
    top: 36px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -ms-transform: rotate(45deg) \9
}

.success-loading .animateSuccessTip,
.modal-dialog.sweet .modal-content .m-success .animateSuccessTip {
    -webkit-animation: animateSuccessTip 0.7s;
    animation: animateSuccessTip 0.7s
}

.success-loading .animateSuccessLong,
.modal-dialog.sweet .modal-content .m-success .animateSuccessLong {
    -webkit-animation: animateSuccessLong 0.8s;
    animation: animateSuccessLong 0.8s
}

.success-loading .m-placeholder,
.modal-dialog.sweet .modal-content .m-success .m-placeholder {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(165, 220, 134, 0.2);
    -webkit-border-radius: 40px;
    border-radius: 40px;
    border-radius: 50%;
    box-sizing: content-box;
    position: absolute;
    left: -4px;
    top: -4px;
    z-index: 2
}

.success-loading .m-fix,
.modal-dialog.sweet .modal-content .m-success .m-fix {
    width: 5px;
    height: 86px;
    background-color: white;
    position: absolute;
    left: 16px;
    top: -3px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.success-loading.loaded:after,
.modal-dialog.sweet .modal-content .loaded.m-success:after {
    -webkit-animation: rotatePlaceholder 0.7s ease-in;
    animation: rotatePlaceholder 0.7s ease-in;
    animation: rotatePlaceholder 0.7s ease-in
}

.success-loading.loading .m-placeholder,
.modal-dialog.sweet .modal-content .loading.m-success .m-placeholder {
    border: 4px solid #a5dc86;
    border-left-color: transparent;
    animation: rotate .4s infinite linear
}

.success-loading.loading .m-line,
.modal-dialog.sweet .modal-content .loading.m-success .m-line {
    display: none
}

@-webkit-keyframes animateSuccessTip {
    0% {
        width: 0;
        left: 1px;
        top: 28px
    }
    60% {
        width: 0;
        left: 1px;
        top: 28px
    }
    80% {
        width: 17px;
        left: 8px;
        top: 35px
    }
    100% {
        width: 19px;
        left: 8px;
        top: 36px
    }
}

@-webkit-keyframes animateSuccessLong {
    0% {
        width: 0;
        left: 16px;
        top: 39px
    }
    65% {
        width: 0px;
        left: 20px;
        top: 39px
    }
    84% {
        width: 25px;
        left: 20px;
        top: 32px
    }
    100% {
        width: 39px;
        left: 16px;
        top: 30px
    }
}

@-webkit-keyframes rotatePlaceholder {
    80% {
        transform: rotate(-390deg);
        -webkit-transform: rotate(-390deg)
    }
    100% {
        transform: rotate(-407deg);
        -webkit-transform: rotate(-407deg)
    }
}

@-webkit-keyframes rotate {
    0% {
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
    100% {
        -moz-transform: rotate(-360deg);
        -ms-transform: rotate(-360deg);
        -webkit-transform: rotate(-360deg);
        transform: rotate(-360deg)
    }
}

@-ms-viewport {
    width: device-width
}

.visible-xs {
    display: none !important
}

.visible-sm {
    display: none !important
}

.visible-md {
    display: none !important
}

.visible-lg {
    display: none !important
}

.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
    display: none !important
}
.has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline {
    color: #a94442;
}

@media (max-width: 767px) {
    .visible-xs {
        display: block !important
    }
    table.visible-xs {
        display: table !important
    }
    tr.visible-xs {
        display: table-row !important
    }
    th.visible-xs,
    td.visible-xs {
        display: table-cell !important
    }
}

@media (max-width: 767px) {
    .visible-xs-block {
        display: block !important
    }
}

@media (max-width: 767px) {
    .visible-xs-inline {
        display: inline !important
    }
}

@media (max-width: 767px) {
    .visible-xs-inline-block {
        display: inline-block !important
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .visible-sm {
        display: block !important
    }
    table.visible-sm {
        display: table !important
    }
    tr.visible-sm {
        display: table-row !important
    }
    th.visible-sm,
    td.visible-sm {
        display: table-cell !important
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .visible-sm-block {
        display: block !important
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .visible-sm-inline {
        display: inline !important
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .visible-sm-inline-block {
        display: inline-block !important
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .visible-md {
        display: block !important
    }
    table.visible-md {
        display: table !important
    }
    tr.visible-md {
        display: table-row !important
    }
    th.visible-md,
    td.visible-md {
        display: table-cell !important
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .visible-md-block {
        display: block !important
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .visible-md-inline {
        display: inline !important
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .visible-md-inline-block {
        display: inline-block !important
    }
}

@media (min-width: 1200px) {
    .visible-lg {
        display: block !important
    }
    table.visible-lg {
        display: table !important
    }
    tr.visible-lg {
        display: table-row !important
    }
    th.visible-lg,
    td.visible-lg {
        display: table-cell !important
    }
}

@media (min-width: 1200px) {
    .visible-lg-block {
        display: block !important
    }
}

@media (min-width: 1200px) {
    .visible-lg-inline {
        display: inline !important
    }
}

@media (min-width: 1200px) {
    .visible-lg-inline-block {
        display: inline-block !important
    }
}

@media (max-width: 767px) {
    .hidden-xs {
        display: none !important
    }
}

@media (min-width: 768px) and (max-width: 991px) {
    .hidden-sm {
        display: none !important
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .hidden-md {
        display: none !important
    }
}

@media (min-width: 1200px) {
    .hidden-lg {
        display: none !important
    }
}

.visible-print {
    display: none !important
}

@media print {
    .visible-print {
        display: block !important
    }
    table.visible-print {
        display: table !important
    }
    tr.visible-print {
        display: table-row !important
    }
    th.visible-print,
    td.visible-print {
        display: table-cell !important
    }
}

.visible-print-block {
    display: none !important
}

@media print {
    .visible-print-block {
        display: block !important
    }
}

.visible-print-inline {
    display: none !important
}

@media print {
    .visible-print-inline {
        display: inline !important
    }
}

.visible-print-inline-block {
    display: none !important
}

@media print {
    .visible-print-inline-block {
        display: inline-block !important
    }
}

@media print {
    .hidden-print {
        display: none !important
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto
}


























form>.row div[class*='col-'] {
    padding-top: 5px;
    padding-bottom: 5px
}

.reg {
    font-weight: 400;
    word-wrap: break-word !important
}

.m-cir:after {
    content: "\f111";
    font-family: 'fontAwesome';
    color: red;
    font-size: 6px;
    vertical-align: super;
    padding-left: 3px
}

.m-hash:after {
    content: "\f198";
    font-family: 'fontAwesome';
    color: red;
    font-size: 10px;
    vertical-align: super;
    padding-left: 3px
}

.tbl-format {
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc
}

.tbl-format .row {
    margin: 0
}

.tbl-format .row .inner div[class*='col-'] {
    padding-bottom: 20px;
    padding-top: 10px
}

.tbl-format .row .inner div[class*='col-'].has-error {
    padding-bottom: 0px
}

.tbl-format .row:nth-child(odd) {
    background-color: #f7f7f7
}

.tbl-format .row:nth-child(even) {
    background-color: #fff
}

.tbl-format:last-child {
    border-bottom: none
}

.tabpane {
    background-color: #fff;
    padding: 20px;
    min-height: 380px !important;
    height: auto
}

.tabpane.tds {
    padding: 20px 0px
}

.tabpane h4 {
    font-family: arial;
    color: #0b1e59
}

.tabpane h4.ptitle {
    font-weight: 600
}

.next-tab-nav {
    margin-top: 20px
}

legend.reg {
    font-size: 14px;
    margin-bottom: 0;
    border: none
}

.datepicker-icon {
    position: relative
}

.datepicker-icon input {
    padding-right: 30px
}

@media screen and (max-width: 1199px) {
    .datepicker-icon input {
        padding-right: 0px;
        padding-left: 4px;
        font-size: 12px
    }
}

.datepicker-icon i {
    position: absolute;
    right: 0px;
    padding: 10px 12px;
    pointer-events: none
}

textarea {
    resize: none
}

.regular-checkbox,
.chkbx {
    opacity: 0;
    position: absolute;
    height: 20px;
    width: 23px;
    margin: 6px 0px 0 !important
}

.regular-checkbox+label,
.chkbx+label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
    font-weight: 500
}

.regular-checkbox+label:before,
.chkbx+label:before {
    content: '';
    background: #fff;
    border: 2px solid #ddd;
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    margin-right: 10px;
    text-align: center
}

.regular-checkbox:checked+label:before,
.chkbx:checked+label:before {
    content: '\2714';
    font-family: 'FontAwesome';
    background: #41a910;
    color: #fff;
    font-size: 12px
}

.regular-checkbox:disabled+label:before,
.chkbx:disabled+label:before {
    background: #cacaca
}

.regular-checkbox:focus+label,
.chkbx:focus+label {
    outline: 1px solid #ddd
}

input[type=radio] {
    opacity: 0;
    position: absolute;
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
    width: 15px
}

input[type=radio]+label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
    font-weight: 500
}

input[type=radio]+label:before {
    content: '';
    background: #f1f1f1;
    border: 1px solid #ddd;
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    padding: 2px;
    margin-right: 10px;
    text-align: center;
    border-radius: 50%
}

input[type=radio]:checked+label:before {
    background: #41a910;
    box-shadow: inset 0px 0px 0px 4px #f1f1f1
}

input[type=radio]:disabled+label:before {
    background: #cacaca
}

input[type=radio]:focus+label {
    outline: 1px solid #ddd
}

.list-child-inline {
    padding-left: 0;
    list-style: none
}

.list-child-inline li {
    display: inline-block;
    padding-top: 5px
}

@media screen and (max-width: 760px) {
    .list-child-inline li {
        width: 100%
    }
}

@media screen and (min-width: 761px) and (max-width: 1199px) {
    .list-child-inline li {
        width: 49%
    }
}

@media screen and (min-width: 1200px) {
    .list-child-inline li {
        width: 33%
    }
}

.switch {
    border: none;
    outline: 0;
    padding: 0;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.switch label {
    background: #7e7e7e;
    color: transparent;
    cursor: pointer;
    display: block;
    position: relative;
    text-indent: 100%;
    width: 64px;
    height: 33px;
    transition: left 0.15s ease-out
}

.switch label:after {
    background: #FFFFFF;
    content: "";
    display: block;
    height: 27px;
    left: 4px;
    position: absolute;
    top: 3px;
    width: 27px;
    -webkit-transition: left 0.15s ease-out;
    -moz-transition: left 0.15s ease-out;
    -o-transition: translate3d(0, 0, 0);
    transition: left 0.15s ease-out;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.switch input {
    left: 10px;
    opacity: 0;
    padding: 0;
    position: absolute;
    top: 9px
}

.switch input:checked+label {
    background: #41a910
}

.switch input:checked+label:after {
    left: 35px
}

.switch input:disabled+label {
    cursor: not-allowed
}

.switch.radius label {
    border-radius: 4px
}

.switch.radius label:after {
    border-radius: 3px
}

.switch.round {
    border-radius: 1000px
}

.switch.round label {
    border-radius: 32px
}

.switch.round label:after {
    border-radius: 32px
}

.switch-on {
    position: absolute;
    left: -60px;
    top: 8px;
    color: white;
    font-weight: bold;
    font-size: 13px;
    width: 0%
}

.switch-off {
    position: absolute;
    left: -29px;
    top: 8px;
    color: white;
    font-weight: bold;
    font-size: 13px;
    width: 0%
}

.wrapper {
    z-index: 5;
    text-transform: uppercase;
    color: #555;
    cursor: help;
    font-size: 20px;
    padding: 15px -50px;
    position: relative;
    text-align: center;
    -webkit-transform: translateZ(0);
    -webkit-font-smoothing: antialiased
}

.wrapper .tooltip {
    background: #fff;
    top: -200%;
    color: #000;
    display: block;
    left: 0px;
    margin-left: 100%;
    opacity: 0;
    padding: 10px 20px;
    pointer-events: none;
    position: absolute;
    min-width: 200px;
    width: 35%;
    -webkit-transform: translateY(10px);
    -moz-transform: translateY(10px);
    -ms-transform: translateY(10px);
    -o-transform: translateY(10px);
    transform: translateY(10px);
    -webkit-transition: all .25s ease-out;
    -moz-transition: all .25s ease-out;
    -ms-transition: all .25s ease-out;
    -o-transition: all .25s ease-out;
    transition: all .25s ease-out;
    -webkit-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    -moz-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    -ms-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    -o-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28)
}

@media screen and (max-width: 950px) {
    .wrapper .tooltip {
        top: auto;
        bottom: 100%;
        left: auto;
        right: 0px
    }
}

.wrapper .tooltip:before {
    bottom: -20px;
    content: " ";
    display: block;
    height: 20px;
    left: 0;
    position: absolute;
    width: 100%
}

.wrapper .tooltip:after {
    border-top: solid transparent 10px;
    border-bottom: solid transparent 10px;
    border-right: solid #C9C9C9 10px;
    bottom: 42%;
    content: " ";
    height: 0;
    left: 0%;
    margin-left: -10px;
    position: absolute;
    width: 0
}

@media screen and (max-width: 950px) {
    .wrapper .tooltip:after {
        border-right: solid transparent 10px;
        border-left: solid transparent 10px;
        border-top: solid #C9C9C9 10px;
        border-bottom: none;
        border: auto;
        top: 100%;
        left: 48%;
        position: absolute;
        width: 0
    }
}

.wrapper input:focus+.tooltip {
    opacity: 1;
    pointer-events: auto;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px)
}

.captcha {
    background: none;
    opacity: 1
}

.captcha-loading {
    background: transparent url("/uiassets/images/cloading.gif") center no-repeat;
    opacity: 0.3;
    object-position: -99999px 99999px;
    border: 1px solid gray
}

.fade-scale {
    transform: scale(0);
    opacity: 0;
    -webkit-transition: all .25s linear;
    -o-transition: all .25s linear;
    transition: all .25s linear
}

.fade-scale.in {
    opacity: 1;
    transform: scale(1)
}

.no-drop {
    cursor: no-drop
}

.no-drop a,
.no-drop * {
    cursor: no-drop
}

span.clearer {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    cursor: pointer
}

.tooltip-inner {
    white-space: nowrap;
    max-width: inherit;
    text-align: left
}

.pdg-box {
    padding: 5px;
    cursor: pointer
}

.pdg-box-suc {
    color: #3c763d
}

.pdg-box-war {
    color: #8a6d3b
}

.pdg-box-inf {
    color: #31708f
}

.pdg-box-dgr {
    color: #a94442
}

.type-ico {
    font-size: large
}

.valign-super {
    vertical-align: super
}

.no-pad {
    padding: 0
}

.flt-right {
    float: right
}

.flt-left {
    float: left
}

.brd-tp {
    border-top: 1px
}

.no-list-style {
    padding: 0;
    list-style: none
}

#ngProgress {
    margin: 0;
    padding: 0;
    z-index: 99998;
    background-color: green;
    color: green;
    box-shadow: 0 0 10px 0;
    height: 2px;
    opacity: 0;
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out
}

#ngProgress-container {
    position: fixed;
    margin: 0;
    padding: 0;
    top: 0;
    left: 0;
    right: 0;
    z-index: 99999
}

.dimmer-holder {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background: gray;
    display: none;
    z-index: 99
}

#dimmer {
    position: absolute;
    margin: auto;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    width: 19px;
    height: 19px;
    border-radius: 100%;
    box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -o-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -ms-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -webkit-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -moz-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    animation: cssload-spin ease infinite 3.4s;
    -o-animation: cssload-spin ease infinite 3.4s;
    -ms-animation: cssload-spin ease infinite 3.4s;
    -webkit-animation: cssload-spin ease infinite 3.4s;
    -moz-animation: cssload-spin ease infinite 3.4s
}

@keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-o-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-ms-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-webkit-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-moz-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

header {
    background-color: #0B1E59;
    padding: 0 0 10px 0
}

header .skip {
    background: #051547;
    color: #fff;
    text-align: right
}

header .skip a {
    color: #fff
}

header .skip a:link,
header .skip a:visited {
    text-decoration: none !important
}

header .skip a:link:active,
header .skip a:visited:active {
    text-decoration: none !important
}

header .skip ul {
    margin: 5px 0
}

header .skip ul li {
    cursor: pointer;
    font-size: 12px
}

@media screen and (max-width: 768px) {
    header .skip {
        display: none
    }
}

header .branding {
    padding-top: 10px
}

@media screen and (max-width: 320px) {
    header .branding .col-xs-12 {
        padding: 0 0 0 10px
    }
}

@media screen and (max-width: 420px) {
    header .logo {
        width: 21px
    }
}

header .site-title {
    font-size: 2em;
    display: inline;
    font-family: verdana;
    vertical-align: middle;
    margin-left: 15px
}

@media screen and (max-width: 768px) {
    header .site-title {
        font-size: 1.8em
    }
}

@media screen and (max-width: 500px) {
    header .site-title {
        font-size: 1.6em;
        margin-left: 5px
    }
}

@media screen and (max-width: 350px) {
    header .site-title {
        font-size: 1.4em
    }
}

@media screen and (max-width: 300px) {
    header .site-title {
        font-size: 1.2em
    }
}

header .site-title a {
    color: #fff
}

header .site-title a:link,
header .site-title a:visited {
    text-decoration: none !important
}

header .site-title a:link:active,
header .site-title a:visited:active {
    text-decoration: none !important
}

header .sup {
    margin-right: 10px
}

header .sup.plus:after {
    content: "+";
    vertical-align: top
}

header .sup.minus:after {
    content: "-";
    vertical-align: super
}

header .mlinks {
    display: inline;
    float: right;
    text-align: right;
    margin: 0;
    padding: 16px 0;
    font-size: 12px
}

header .mlinks a {
    color: #fff
}

@media screen and (max-width: 767px) {
    header .mlinks {
        display: none
    }
}

header .mlinks>li {
    border-right: 1px solid white;
    min-height: 22px;
    vertical-align: top;
    padding: 0 20px
}

header .mlinks>li:last-child {
    border-right: 0;
    padding-right: 0
}

@media screen and (max-width: 550px) {
    header .mlinks>li:last-child {
        display: none
    }
}

header .mlinks>li:last-child a.login {
    vertical-align: -webkit-baseline-middle
}

header .mlinks>li .dropdown {
    padding-top: 4px
}

header .mlinks>li .dropdown .lang-dpwn span {
    margin-left: 4px;
    color: #fff
}

header .mlinks>li .dropdown .lang-dpwn:after {
    font-family: 'FontAwesome';
    content: "\f107"
}

header .mlinks>li .dropdown .lang-dpwn:hover {
    text-decoration: none
}

header .mlinks>li .dropdown.open .lang-dpwn span {
    margin-left: 4px
}

header .mlinks>li .dropdown.open .lang-dpwn:after {
    font-family: 'FontAwesome';
    content: "\f106"
}

.profile-dpdwn {
    right: 0;
    min-width: 200px;
    left: inherit;
    box-shadow: 1px 10px 26px #ABABAB;
    -webkit-box-shadow: 1px 10px 26px #ABABAB;
    -moz-box-shadow: 1px 10px 26px #ABABAB;
    border-radius: 4px
}

.profile-dpdwn>li>a {
    padding: 6px 20px;
    color: #000
}

.lst-log {
    font-size: 11px;
    padding-left: 18px
}

.lst-log-dt {
    font-size: 11px;
    padding-left: 18px;
    font-weight: 600
}

.stickytop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    transition: all 2s;
    animation: slide-down 2s;
    opacity: 0.9
}

.no-stickytop {
    top: -100px
}

.lang .lang-dpdwn {
    right: 0;
    left: auto;
    top: 80%
}

.impcont {
    margin-top: 11px;
    float: left;
    width: 40%;
    padding-left: 12px
}

@media (max-width: 1200px) {
    .impcont {
        padding-left: 6px;
        width: 100%
    }
    .impcont.ex {
        margin-top: 2px
    }
}

@media (max-width: 769px) and (max-width: 981px) {
    .impcont {
        width: 100%
    }
}

@media (min-width: 768px) and (max-width: 1200px) {
    .impcont {
        width: 42%;
        margin-left: 10px;
        margin-top: 15px
    }
}

@media (max-width: 1200px) {
    .impcont {
        width: 86%;
        padding-left: 15px
    }
}

@media (min-width: 320px) and (max-width: 768px) {
    .impcont {
        width: 50%
    }
}

.w3-container {
    padding: 24px 20px 20px 18px
}

.w3-container a {
    color: #fff
}

.container-footer {
    bottom: 10px;
    position: absolute;
    left: 68%
}

.w3-blue,
.w3-hover-blue:hover {
    color: #fff;
    background-color: #0B1E59;
    transition: box-shadow .25s;
    border-radius: 2px
}

.w3-blue-small {
    color: #fff;
    background-color: #2C4E86;
    transition: box-shadow .25s;
    border-radius: 2px;
    width: 362px;
    height: 65px
}

@media (min-width: 320px) and (max-width: 1199px) {
    .w3-blue-small {
        width: 100%
    }
}

.w3-card-4,
.w3-hover-shadow:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
    height: 150px
}

.w3-card-4 a,
.w3-hover-shadow:hover a {
    color: #fff
}

.player-play:before {
    color: rgba(255, 0, 0, 0.68);
    opacity: 0.9
}

.video-img-container {
    height: 300px;
    width: 534px;
    line-height: 300px;
    position: relative;
    cursor: pointer
}

.video-img-container:hover img {
    opacity: 0.5
}

.video-img-container:hover .video-img-i-container i {
    color: #fff
}

.video-img-i-container {
    position: absolute;
    top: 0px;
    left: 0px;
    display: block;
    height: 100%;
    width: 100%;
    z-index: 500;
    text-align: center
}

.video-img-i-container i {
    display: block;
    line-height: inherit
}

.cards a {
    color: #000
}

.cards a.link {
    text-decoration: none !important
}

.cards a:hover {
    text-decoration: none;
    color: #23527c
}

.cards a:visited {
    text-decoration: none !important
}

.row-separator .row {
    margin-top: 20px
}

.help-mod-pg {
    padding-left: 0px
}

.help-mod-pg li {
    list-style: none;
    margin: 10px 0
}

.help-mod-pg li img {
    vertical-align: baseline;
    position: absolute;
    display: inline-block
}

.help-mod-pg li div {
    display: inline-block;
    width: 85%;
    margin-left: 8%
}

@media screen and (max-width: 991px) {
    .help-mod-pg li div {
        margin-left: 13%
    }
}

@media screen and (max-width: 570px) {
    .help-mod-pg li div {
        margin-left: 16%
    }
}

@media screen and (max-width: 570px) {
    .help-mod-pg li div {
        margin-left: 20%
    }
}

@media screen and (max-width: 350px) {
    .help-mod-pg li div {
        margin-left: 26%
    }
}

.help-mod-pg li div ul {
    padding-left: 14px
}

.help-mod-pg li div p {
    font-weight: 600;
    margin-left: 14px;
    border-bottom: 1px solid #337ab7
}

.l-widgets {
    background-color: #fff
}

.l-widgets .panels .panel {
    background-color: #B9EDEA;
    border-bottom: 2px solid #2ec9c1;
    color: #2c4e86;
    border-bottom: 2px solid #2ec9c1
}

.l-widgets .panels .panel .bl {
    color: #000
}

.l-widgets .panels .panel.news {
    background-color: #F5F5F5;
    padding: 10px 10px 0px 10px;
    border-bottom: 2px solid #333333;
    min-height: 205px
}

.l-widgets .panels .panel.news p.dt {
    color: #505050;
    font-size: 12px
}

.l-widgets .panels .panel .panel-heading {
    background-position: left top;
    background-repeat: no-repeat;
    background-origin: content-box;
    background-color: #fff;
    padding: 12px 12px 0 12px;
    border-bottom: 1px solid #eeeeee
}

.l-widgets .panels .panel .panel-heading p {
    margin-top: 5px;
    font-family: Tahoma;
    font-size: 15px;
    font-weight: bold;
    margin-left: 50px
}

.l-widgets .panels .panel .panel-body {
    padding: 0px 20px 0 10px
}

.l-widgets .panels .panel .panel-footer {
    background-color: #80dfff;
    padding: 0px 15px 0 0;
    border-top: 0;
    text-align: right
}

.l-widgets .panels .panel .panel-footer a {
    color: #252525
}

.l-widgets .panels .panel .shrt-info {
    list-style: none;
    padding: 0 0 0 10px;
    margin-bottom: 0;
    font-family: tahoma
}

.l-widgets .panels .panel .shrt-info .icon {
    padding-right: 9px
}

.l-widgets .panels .panel .shrt-info li {
    padding: 12px 0 10px 0;
    border-bottom: 1px solid #ebebeb;
    min-height: 45px
}

.l-widgets .panels .panel .shrt-info li:last-child {
    border: none
}

.lbanner {
    padding: 0
}

.impdates {
    margin-top: 0px;
    background-color: #fff
}

.impdates h3 {
    border-bottom: 1px solid #ebebeb;
    margin-bottom: 20px
}

.impdates .dates-list {
    padding: 0;
    list-style: none
}

.impdates .dates-list li {
    width: 49%;
    display: inline-block;
    background: #ebebeb;
    font-size: 14px;
    padding: 0px;
    font-family: tahoma
}

@media screen and (max-width: 768px) {
    .impdates .dates-list li {
        width: 100%
    }
}

.impdates .dates-list li .square-box {
    display: inline-block;
    float: left;
    width: 186px;
    height: 44px
}

.impdates .dates-list li .square-box.b1 {
    background: #1B998B
}

.impdates .dates-list li .square-box.b2 {
    background: #6474a0
}

.impdates .dates-list li .square-box.b3 {
    background: #51a066
}

.impdates .dates-list li .square-box.b4 {
    background: #18436b
}

.impdates .dates-list li .square-box.b5 {
    background: #205479
}

@media screen and (min-width: 768px) and (max-width: 1199px) {
    .impdates .dates-list li .square-box {
        width: 100%
    }
}

@media screen and (max-width: 768px) {
    .impdates .dates-list li .square-box {
        width: 100%
    }
}

@media screen and (min-width: 320px) and (max-width: 768px) {
    .impdates .dates-list li .square-box {
        width: 50%
    }
}

.impdates .dates-list li .square-content {
    color: #fff;
    font-size: 15px;
    text-align: center;
    line-height: 2em;
    margin-top: 3px
}

@media screen and (max-width: 1199px) {
    .impdates .dates-list li .square-content {
        margin-top: 7px
    }
}

.t {
    color: #fff;
    width: 47%;
    margin: 5px;
    text-align: center;
    float: left;
    display: inline-block;
    height: 40px;
    border-radius: 6px
}

@media screen and (max-width: 981px) {
    .t {
        width: 100%
    }
}

.t p {
    margin: 10px
}

.t1 {
    background: #E84855
}

.t2 {
    background: #252f4a
}

.t3 {
    background: #1B998B
}

.t4 {
    background: rgba(244, 71, 8, 0.8)
}

.hlpico i {
    color: #252f4a;
    float: left;
    text-align: justify;
    margin-left: 12%
}

.land {
    width: auto !important
}

.marquee {
    width: 95%;
    margin: 0 auto;
    margin-top: -23px;
    padding: 5px;
    height: 25px;
    overflow: hidden;
    white-space: nowrap;
    box-sizing: border-box;
    animation: marquee 50s linear infinite
}

.marquee:hover {
    animation-play-state: paused
}

@media (min-width: 320px) and (max-width: 768px) {
    .marquee {
        width: 80%
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .marquee {
        width: 90%
    }
}

@keyframes marquee {
    0% {
        text-indent: 99%
    }
    100% {
        text-indent: -75em
    }
}

.land-alerts {
    background-color: #FCF1CA;
    padding-top: 5px;
    color: #D80101;
    font-size: 12px
}

.alerts-bell {
    vertical-align: super;
    display: inline-block
}

.alerts-bell+p {
    display: inline-block
}

.lbanner-img {
    background-image: url(../images/banner/banner_image_large.jpg);
    width: 100%;
    background-repeat: no-repeat;
    background-position: center top;
    height: 550px
}

@media screen and (min-width: 1081px) and (max-width: 1279px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_image.jpg);
        height: 1000px
    }
}

@media screen and (min-width: 929px) and (max-width: 1080px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_image.jpg);
        height: 1000px
    }
}

@media screen and (min-width: 871px) and (max-width: 928px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_image.jpg);
        height: 950px
    }
}

@media screen and (min-width: 770px) and (max-width: 870px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_image.jpg);
        height: 950px
    }
}

@media screen and (min-width: 768px) and (max-width: 769px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_image.jpg);
        height: 650px
    }
}

@media screen and (min-width: 691px) and (max-width: 767px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        height: 650px
    }
}

@media screen and (min-width: 601px) and (max-width: 690px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        height: 700px
    }
}

@media screen and (min-width: 521px) and (max-width: 600px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        height: 650px
    }
}

@media screen and (min-width: 431px) and (max-width: 520px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        height: 850px
    }
}

@media screen and (min-width: 330px) and (max-width: 430px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        height: 850px;
        background-size: 100% auto
    }
}

@media screen and (max-width: 331px) {
    .lbanner-img {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        background-size: 100% auto
    }
}

.lbanner-img-r1 {
    background-image: url(../images/banner/banner_image_large.jpg);
    width: 100%;
    background-repeat: no-repeat;
    background-position: center top;
    height: 330px
}

@media screen and (min-width: 1080px) and (max-width: 1279px) {
    .lbanner-img-r1 {
        background-image: url(../images/banner/banner_image.jpg);
        height: 330px;
        background-position: center;
        background-size: 100% 330px
    }
}

@media screen and (min-width: 929px) and (max-width: 1080px) {
    .lbanner-img-r1 {
        background-image: url(../images/banner/banner_image.jpg);
        background-position: center;
        background-size: 100% 330px
    }
}

@media screen and (min-width: 871px) and (max-width: 928px) {
    .lbanner-img-r1 {
        background-image: url(../images/banner/banner_image.jpg);
        background-position: center;
        background-size: 100% 330px;
        height: 330px
    }
}

@media screen and (min-width: 768px) and (max-width: 870px) {
    .lbanner-img-r1 {
        background-image: url(../images/banner/banner_image.jpg);
        background-size: 100% 330px;
        height: 330px
    }
}

@media screen and (min-width: 691px) and (max-width: 767px) {
    .lbanner-img-r1 {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        height: 330px
    }
}

@media screen and (max-width: 690px) {
    .lbanner-img-r1 {
        background-image: url(../images/banner/banner_mobile_image.jpg);
        background-position: center;
        background-size: 100% 330px;
        height: 330px
    }
}

.lbnr-text {
    padding: 0.5% 2%;
    background: #fff
}

@media screen and (min-width: 1681px) {
    .lbnr-text {
        margin-top: 30px;
        margin-left: 16%
    }
}

@media screen and (min-width: 1331px) and (max-width: 1680px) {
    .lbnr-text {
        margin-top: 30px;
        margin-left: 10%
    }
}

@media screen and (min-width: 1280px) and (max-width: 1330px) {
    .lbnr-text {
        margin-left: 7%;
        margin-top: 16%
    }
}

@media screen and (min-width: 1080px) and (max-width: 1279px) {
    .lbnr-text {
        margin-left: 7%;
        margin-top: 5%
    }
}

@media screen and (min-width: 941px) and (max-width: 1079px) {
    .lbnr-text {
        margin-left: 7%;
        margin-top: 6%
    }
}

@media screen and (min-width: 941px) and (max-width: 991px) {
    .lbnr-text {
        width: 55%
    }
}

@media screen and (min-width: 871px) and (max-width: 940px) {
    .lbnr-text {
        margin-left: 7%;
        margin-top: 4%;
        width: 55%
    }
}

@media screen and (min-width: 769px) and (max-width: 870px) {
    .lbnr-text {
        margin-left: 7%;
        margin-top: 2%;
        width: 60%
    }
}

@media screen and (min-width: 768px) and (max-width: 768px) {
    .lbnr-text {
        margin-left: 7%;
        margin-top: 3.5%;
        width: 60%
    }
}

@media screen and (max-width: 767px) {
    .lbnr-text {
        display: block;
        margin-top: 30px
    }
}

.lbnr-text-mob {
    display: none
}

@media screen and (max-width: 767px) {
    .lbnr-text-mob {
        display: block;
        background: #fff
    }
}

.loader-div {
    margin-top: 5%
}

@media screen and (max-width: 1200px) {
    .loader-div {
        margin-top: 7%
    }
}

@media screen and (max-width: 991px) {
    .loader-div {
        margin-top: 10%
    }
}

@media screen and (max-width: 767px) {
    .loader-div {
        margin-top: 5%
    }
}

.loader-div .waiting {
    position: relative;
    margin-left: 40%
}

.loader-div h4 {
    padding-top: 18%;
    text-align: center
}

@media screen and (max-width: 1200px) {
    .loader-div h4 {
        padding-top: 24%
    }
}

@media screen and (max-width: 991px) {
    .loader-div h4 {
        padding-top: 31%
    }
}

@media screen and (max-width: 767px) {
    .loader-div h4 {
        padding-top: 31%
    }
}

@media screen and (max-width: 600px) {
    .loader-div h4 {
        padding-top: 20%
    }
}

@media screen and (max-width: 500px) {
    .loader-div h4 {
        padding-top: 25%
    }
}

@media screen and (max-width: 400px) {
    .loader-div h4 {
        padding-top: 30%
    }
}

.news-updts {
    list-style: none
}

.news-updts .dt {
    font-size: 11px
}

.news-updts li {
    padding: 10px 17px
}

.news-updts li:nth-child(odd) {
    background: #f3f3f3
}

.w3-help {
    background-color: #e4e4e4;
    border: 1px solid #d3d3d3;
    margin-left: 0px;
    width: 99%
}

.pad-t-24 {
    padding-top: 24px;
    margin-top: 0px
}

.help-modules {
    background-color: #f8f8f8;
    border: 1px solid #cccccc;
    margin-left: 0px;
    width: 100%
}

.panel-heading-help {
    color: #fff;
    background-color: #2c4e86;
    border-color: #ddd;
    border-bottom: 0;
    padding: 10px 15px
}

.pad-t-10 {
    padding-top: 10px;
    margin-top: 0px
}

.pad-l-10 {
    padding-left: 10px
}

footer {
    position: relative;
    padding-top: 0px;
    bottom: 0;
    left: 0;
    right: 0;
    background: #14315D
}

footer .f1 {
    padding-top: 25px
}

footer.bo {
    padding-top: 0
}

footer .expbtn {
    background: #14315D;
    position: absolute;
    height: 24px;
    width: 42px;
    left: 90.3%;
    top: -23px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px
}

@media screen and (max-width: 767px) {
    footer .expbtn {
        left: 80%
    }
}

footer .expbtn i {
    color: #fff;
    padding: 0 13px;
    font-size: 23px;
    cursor: pointer
}

footer.fix {
    position: relative
}

footer ul {
    padding: 0;
    list-style: none
}

footer .fsep {
    border-top: 1px solid #31425b;
    margin: 0
}

footer .follow {
    margin: 10px 0 0 0;
    color: #56caf0;
    display: inline-block;
    font-size: 16px
}

@media screen and (max-width: 450px) {
    footer .follow {
        font-size: 14px
    }
}

footer .social a {
    color: #14315D !important;
    display: inline-block;
    margin: 10px 20px 0 0
}

@media screen and (min-width: 768px) and (max-width: 1080px) {
    footer .social a {
        margin: 10px 10px 0 0
    }
}

footer .social a i {
    font-size: 18px;
    color: #959FB0
}

@media screen and (max-width: 450px) {
    footer .social a i {
        font-size: 16px
    }
}

footer .fhead {
    font-size: 14px;
    font-family: inherit;
    font-weight: 600;
    line-height: 30px;
    padding: 10px 0;
    color: #56caf0;
    margin: 0
}

@media screen and (max-width: 450px) {
    footer .fhead {
        font-size: 12px
    }
}

footer .fhead.l1 {
    font-size: 18px
}

footer .fhead.toll-free {
    font-size: 22px;
    color: #fff;
    border-bottom: 1px solid #475f82
}

footer .fhead.toll-free:before {
    content: "\f095";
    font: normal normal normal 22px/1 FontAwesome;
    padding-right: 10px;
    vertical-align: baseline
}

footer .fhead+ul li {
    padding: 12px 0px
}

footer .fhead+ul li a {
    color: #bed2dd;
    font-size: 13px
}

footer span.contact {
    color: #bed2dd;
    font-size: 12px;
    word-wrap: break-word
}

footer p {
    color: gray;
    display: inline-block
}

@media screen and (max-width: 300px) {
    footer .col-xs-6 {
        width: 100%
    }
}

footer .f3 {
    background: #2c4e86
}

footer .f3 p.site {
    padding: 17px 0;
    color: #FFF;
    margin: 0;
    font-size: 12px
}

@media screen and (max-width: 550px) {
    footer .f3 {
        display: none
    }
}

footer .f2 {
    border-top: 1px solid #475f82
}

footer .f2 p {
    color: #8ba8ba;
    padding: 15px 0;
    margin: 0;
    font-size: 14px;
    margin-right: 10em;
    display: inline-block;
    float: left
}

footer .f2 p.notab {
    display: none
}

@media screen and (min-width: 991px) {
    footer .f2 p.notab {
        display: block
    }
}

footer .f2 p.nomob {
    display: none
}

@media screen and (min-width: 600px) {
    footer .f2 p.nomob {
        display: block
    }
}

footer .f2 p:last-child {
    margin-right: 0
}

@media screen and (min-width: 991px) and (max-width: 1199px) {
    footer .f2 p {
        margin-right: 3em
    }
}

@media screen and (max-width: 991px) {
    footer .f2 p {
        margin-right: 2em;
        font-size: 12px
    }
    footer .f2 p:nth-child(2) {
        margin-right: 0
    }
}

@media screen and (max-width: 300px) {
    footer .f2 p {
        margin: 0;
        text-align: center
    }
}

@media screen and (max-width: 480px) {
    .no-mobile ul {
        display: none
    }
    .no-mobile p {
        padding: 10px 0
    }
    .no-mobile.scl {
        display: block
    }
    .no-mobile.scl ul {
        display: block
    }
    .no-mobile.scl li {
        display: none
    }
    .no-mobile.scl li.social {
        display: block
    }
}

.back-to-top {
    position: fixed;
    cursor: pointer;
    color: #fff;
    padding: 5px;
    background: #ccc;
    border-radius: 29px;
    width: 51px;
    height: 52px;
    text-align: center;
    display: none;
    bottom: 10px;
    right: 2%;
    z-index: 999999
}

.back-to-top p {
    margin-top: -7px;
    color: #000
}

.back-to-top i {
    font-size: 25px;
    color: #333333;
    font-weight: 600
}

.fm-links {
    display: none
}

@media screen and (max-width: 480px) {
    .fm-links {
        display: block
    }
}

body {
    background: #E4E8EB
}

[data-ng-view] {
    padding: 0 15px 15px 15px
}

@media screen and (max-width: 480px) {
    [data-ng-view] {
        padding: 0px
    }
}

ul.no-list {
    list-style: none
}

ul.st-vat li {
    width: 33%;
    display: inline-block
}

@media screen and (max-width: 768px) {
    ul.st-vat li {
        width: 49%
    }
}

@media screen and (max-width: 540px) {
    ul.st-vat li {
        width: 100%
    }
}

.regapp {
    height: 100px;
    margin: 0 0 20px 0;
    color: #212121;
    padding: 18px 0
}

.regapp .col-xs-12 {
    background-color: #ffee77
}

@media screen and (max-width: 991px) {
    .regapp .appmodify,
    .regapp .appperc {
        display: none
    }
}

@media screen and (min-width: 545px) and (max-width: 991px) {
    .regapp .headapptype,
    .regapp .appdue {
        width: 50%
    }
}

@media screen and (min-width: 401px) and (max-width: 545px) {
    .regapp .headapptype,
    .regapp .appdue {
        width: 100%
    }
    .regapp .headapptype p,
    .regapp .appdue p {
        display: inline-block;
        font-size: 16px
    }
    .regapp .headapptype p:last-child,
    .regapp .appdue p:last-child {
        margin-left: 15px
    }
    .regapp .headapptype p:last-child:before,
    .regapp .appdue p:last-child:before {
        content: ":";
        padding-right: 15px
    }
}

@media screen and (max-width: 400px) {
    .regapp .headapptype p,
    .regapp .appdue p {
        display: block;
        font-size: 16px
    }
}

.regapp .apptype {
    font-size: 20px;
    font-family: verdana
}

.regapp .lbl {
    font-family: inherit;
    font-size: 18px;
    padding: 10px 0;
    margin: 0
}

.regapp .date {
    font-size: 20px;
    min-height: 28px
}

.regapp .date .dleft {
    font-size: 16px
}

@media screen and (max-width: 610px) {
    .regapp .date .dleft {
        display: none
    }
}

.regapp .status {
    font-size: 18px
}

.container-pane {
    background-color: #fff
}

.content-pane {
    background-color: #fff;
    padding: 0px 15px 30px 15px;
    margin-top: 0px;
    height: auto;
}

@media screen and (max-width: 480px) {
    .content-pane {
        padding: 0px 0px 30px 0px
    }
}

.mand-text {
    width: 100%;
    text-align: right;
    margin: 5px 0;
    font-size: 13px;

}

.mand-text:before {
    content: "\f111";
    font-family: 'fontAwesome';
    color: red;
    font-size: 8px;
    vertical-align: super;
    padding-right: 5px
}

.mand-text-opt {
    width: 100%;
    text-align: right;
    margin: 5px 0;
    font-size: 13px
}

.mand-text-opt:before {
    content: "\f198";
    font-family: 'fontAwesome';
    color: red;
    font-size: 8px;
    vertical-align: super;
    padding-right: 5px
}

[ng\:cloak],
[ng-cloak],
[data-ng-cloak],
[x-ng-cloak],
.ng-cloak,
.x-ng-cloak {
    display: none !important
}

code.success {
    color: #fff;
    background-color: #2f7e00
}

code.failure {
    color: #fff;
    background: #ff6666
}

hr {
    margin-top: 5px;
    margin-bottom: 5px
}

.err {
    font-size: 12px;
    color: red
}

table.tpd thead {
    background-color: #f4f4f6;
    border: 1px solid #c0c4cc
}

table.tpd .currency {
    text-align: right
}

.tbl-pgnt {
    text-align: right;
    padding-right: 6px;
    vertical-align: text-top
}

.tbl-pgnt p {
    margin-right: 5px
}

.tbl-pgnt * {
    display: inline-block;
    font-weight: 600;
    font-size: 16px
}

.tbl-pgnt button {
    border: 1px solid #c0c4cc;
    padding: 3px 10px
}

.upper-input {
    text-transform: uppercase
}

::-webkit-input-placeholder {
    text-transform: initial
}

:-moz-placeholder {
    text-transform: initial
}

::-moz-placeholder {
    text-transform: initial
}

:-ms-input-placeholder {
    text-transform: capitalize
}

@media screen and (min-width: 760px) {
    :-ms-input-placeholder {
        font-size: 12px
    }
}

.radio-order.has-error {
    border: 1px solid red
}

.radio-order p {
    width: 50%;
    display: inline-block
}

@media screen and (max-width: 550px) {
    .radio-order p {
        width: 100%;
        display: block
    }
}

.input-group-btn button {
    margin: 0
}

.box-shadow {
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05) inset, 0px 0px 8px rgba(82, 168, 236, 0.6)
}

.help {
    color: #0b1e59;
    font-size: 12px
}

@media screen and (max-width: 768px) {
    header {
        transition: all 0.8s ease-out
    }
    header.sticky {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        z-index: 9999;
        transition: all 0.8s ease-in
    }
}

@media screen and (max-width: 768px) {
    .content-wrapper {
        transition: all 0.1s ease-out
    }
    .content-wrapper.sticky {
        padding-top: 100px;
        transition: all 0.1s ease-in
    }
}

.vseparator {
    position: relative;
    width: 100%;
    height: 100px;
    margin: 10px 0
}

@media screen and (max-width: 760px) {
    .vseparator {
        display: none
    }
}

.vseparator .line {
    position: absolute;
    left: 49%;
    top: 0;
    bottom: 0;
    width: 1px;
    background: #ccc;
    z-index: 1
}

.vseparator .wordwrapper {
    text-align: center;
    height: 12px;
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    margin-top: -12px;
    z-index: 2
}

.vseparator .wordwrapper .word {
    color: #ccc;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 3px;
    font: bold 12px arial, sans-serif;
    background: #fff
}

.selfie {
    text-align: center
}

@media screen and (max-width: 790px) {
    .selfie {
        text-align: left
    }
}

@media screen and (min-width: 790px) {
    .selfie .hseparator {
        display: none
    }
}

@media screen and (max-width: 790px) {
    .selfie-seprator {
        width: 100%
    }
}

.hseparator {
    overflow: hidden;
    text-align: center
}

.hseparator.upload {
    display: none
}

@media screen and (max-width: 790px) {
    .hseparator.upload {
        display: block
    }
}

.hseparator:before,
.hseparator:after {
    background-color: #000;
    content: "";
    display: inline-block;
    height: 1px;
    position: relative;
    vertical-align: middle;
    width: 50%
}

.hseparator:before {
    right: 0.5em;
    margin-left: -50%
}

.hseparator:after {
    left: 0.5em;
    margin-right: -50%
}

.autocomplete-holder {
    position: relative
}

.autocomplete-holder .autocomplete-dropdown {
    border-color: #ececec;
    border-width: 1px;
    border-style: solid;
    border-radius: 2px;
    width: 100%;
    cursor: pointer;
    z-index: 9999;
    position: absolute;
    margin-top: -6px;
    background-color: #ffffff
}

.autocomplete-holder .autocomplete-dropdown.dpdwn {
    overflow-y: scroll;
    max-height: 205px
}

.autocomplete-holder .autocomplete-dropdown .autocomplete-dropdown-all {
    padding: 6px 0;
    cursor: pointer;
    z-index: 9999;
    background-color: #ffffff;
    margin: 0;
    text-align: center;
    background: #eee
}

.autocomplete-holder .autocomplete-dropdown .autocomplete-dropdown-all p {
    margin: 0
}

.autocomplete-holder .autocomplete-dropdown .autocomplete-row {
    padding: 10px;
    color: #000000
}

.autocomplete-holder .autocomplete-selected-row {
    background-color: lightblue;
    color: #ffffff
}

.autocomplete-holder .highlight {
    color: red;
    background: yellow
}

.autocomplete-holder .autocomplete-searching {
    color: #acacac;
    font-size: 14px;
    padding: 7px
}

.autocomplete-holder .autocomplete-desc {
    font-size: 14px
}

.autocomplete-holder .autocomplete-title,
.autocomplete-holder .autocomplete-desc {
    display: inline-block
}

.gs-popup {
    list-style: none
}

.gs-popup li {
    padding: 5px 0
}

.mh-300 {
    min-height: 300px
}

.mh-100 {
    min-height: 100px
}

.mh-200 {
    min-height: 200px
}

.padded-box {
    padding: 10px
}

.page-format {
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
    padding-top: 15px
}

.page-format .row .inner {
    padding-left: 15px;
    padding-right: 15px
}

.page-format .row div[class*='col-'] {
    padding-top: 10px;
    padding-bottom: 10px
}

.page-format:last-child {
    border-bottom: none
}

.progressbar {
    counter-reset: step;
    padding-left: 0px
}

.progressbar li {
    list-style-type: none;
    width: 33%;
    float: left;
    font-size: 12px;
    position: relative;
    text-align: center;
    text-transform: capitalize;
    color: #7d7d7d;
    padding-top: 8px
}

.progressbar li:before {
    width: 30px;
    height: 30px;
    line-height: 30px;
    border: 2px solid #7d7d7d;
    display: block;
    text-align: center;
    margin: 0 auto 10px auto;
    border-radius: 50%;
    background-color: white
}

.progressbar li:after {
    width: 100%;
    height: 2px;
    content: '';
    position: absolute;
    background-color: #7d7d7d;
    top: 15px;
    left: -50%;
    z-index: -1 !important
}

.progressbar li:first-child:after {
    content: none;
    border: 1px solid black
}

.progressbar li.active {
    color: green
}

.progressbar li.active:before {
    border-color: #55b776
}

.progressbar li.active+li:after {
    background-color: #55b776
}

.progressbar li .ttl {
    font-size: inherit
}

@media screen and (max-width: 295px) {
    .progressbar li .ttl {
        font-size: 11px
    }
}

.progressbar.four li {
    width: 25%
}

.z1 {
    z-index: 3
}

.z2 {
    z-index: 2
}

.z3 {
    z-index: 1
}

.z4 {
    z-index: 0
}

.circle {
    border: 2px solid #408E1C;
    border-radius: 50%;
    padding: 10px;
    background-color: #408E1C;
    color: white
}

.on-page {
    background-color: #EC9D0A;
    border-color: #EC9D0A;
    padding: 10px 13px
}

.not-active {
    background-color: #7f7f7f;
    border-color: #7f7f7f;
    padding: 10px 13px
}

.progressbar li.step-done {
    color: #408E1C !important
}

.progressbar li.step-on {
    color: #EC9D0A !important
}

.ui-card {
    box-shadow: 3px 4px 9px #888888;
    background: #f8f8f8;
    padding: 20px
}

.ui-card .ui-card-icon p {
    text-align: center
}

.ui-card .ui-card-icon p i {
    font-size: 40px;
    padding: 16px 24px;
    border-radius: 54%;
    background-color: #cccccc;
    color: #fff
}

.ui-card .ui-card-icon h5 {
    font-size: 18px;
    font-weight: 300;
    margin-bottom: 10px;
    color: #fff;
    text-align: center;
    padding: 20px
}

.ui-card .ui-card-icon li {
    color: #fff
}

.ui-card.vert-card {
    padding: 0px;
    margin-top: 4%
}

.ui-card.vert-card .left {
    width: 49%;
    vertical-align: top;
    background: #00b9f5;
    display: inline-block
}

@media screen and (max-width: 768px) {
    .ui-card.vert-card .left {
        width: 100%
    }
}

.ui-card.vert-card .left .ui-card-icon p {
    padding: 35px 0 0px 0
}

.ui-card.vert-card .left ul {
    padding: 0 30px 50px 30px
}

.ui-card.vert-card .left ul li {
    line-height: 1.76em;
    padding: 5px
}

.ui-card.vert-card .right {
    width: 49%;
    display: inline-table;
    padding: 0 20px
}

@media screen and (max-width: 768px) {
    .ui-card.vert-card .right {
        width: 100%
    }
}

.ui-card.vert-card .help {
    text-align: left;
    font-size: 12px
}

.ui-card.vert-card .help.info:before {
    content: "\f05a";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    padding-right: 5px
}

.ui-card .ui-card-text {
    margin-top: 5%
}

.ui-card .ui-card-text h4 {
    text-align: center
}

.ui-card .ui-card-text p {
    margin: 20px 0
}

.ui-card.pg-center {
    margin-top: 13%
}

@media screen and (max-width: 768px) {
    .tbl-desktop {
        display: none
    }
}

.tbl-tab {
    display: none
}

@media screen and (max-width: 768px) {
    .tbl-tab {
        display: block
    }
}

.multiselect {
    position: absolute;
    width: 90%
}

.multiselect dd {
    position: relative
}

.multiselect-content ul {
    width: 100%;
    background-color: #f5f5f5;
    border: 1px solid #dedede;
    color: #212121;
    left: 0px;
    padding: 2px 15px 2px 5px;
    position: absolute;
    top: -9px;
    list-style: none;
    height: 200px;
    overflow-y: auto;
    Z-INDEX: 100;
    margin: -1px 0 0 0
}

.bigcheckbox {
    width: 17px;
    height: 17px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    margin: 7px !important
}

.bigchecklabel {
    margin-top: 5px;
    vertical-align: top
}

.hida {
    cursor: default
}

button#caret {
    border: none;
    background: none;
    position: absolute;
    top: 8px;
    right: 0px;
    color: #555555;
    font-size: 10px
}

button#caret:active,
button#caret:focus {
    outline: 0
}

.statelisterr {
    font-weight: normal
}

.statelisterr:hover {
    text-decoration: none
}

.statecancel {
    color: red;
    cursor: pointer;
    padding: 0px 5px
}

.stateselected {
    background-color: #eee;
    color: grey;
    font-size: 16px;
    margin: 4px;
    padding: 2px 10px;
    display: inline-block
}

.nobg {
    background: none
}

.mar-tb0 {
    margin-top: 0px
}

.well {
    background-color: #D0CFCF;
    margin-bottom: 0px
}

@media screen and (max-width: 759px) {
    .smenu .isubmenu {
        display: none
    }
}

@media screen and (min-width: 760px) {
    .smenu {
        margin: 0 -68em 0 -8.15em;
        background: #ebf0f2;
        padding: 0;
        width: 81.6em
    }
    .smenu.post {
        margin: 0 -68em 0 -8.15em
    }
    .smenu li {
        display: inline-block;
        padding: 0
    }
    .smenu li.has-sub {
        padding: 0
    }
    .smenu li.has-sub a {
        padding: 10px 15px;
        color: #0B1E59
    }
    .smenu li.has-sub a:hover {
        border-bottom: 2px solid #0B1E59
    }
    .smenu li.has-sub ul.isubmenu {
        display: none;
        padding: 0;
        position: absolute;
        background: #fff;
        box-shadow: 0px 6px 10px #888888
    }
    .smenu li.has-sub ul.isubmenu li {
        width: 49%;
        padding: 8px 0px
    }
    .smenu li.has-sub ul.isubmenu li a:hover {
        border: none
    }
    .smenu li.has-sub ul.isubmenu.serv {
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.ret {
        width: 100%;
        margin-left: -15.5em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.ret {
        margin-left: -13.99em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.ret.pre {
        margin-left: -13.99em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.ret.post {
        margin-left: -14.96em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.ret.oidr {
        margin-left: -6.79em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.state {
        margin-left: -5.84em
    }
    .smenu li.has-sub ul.isubmenu.state li {
        width: 32%
    }
    .smenu li.has-sub ul.isubmenu.ut {
        margin-left: -11.01em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.ut li {
        width: 32%
    }
    .smenu li.has-sub ul.isubmenu.oth {
        margin-left: -29.85em;
        width: 100%
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth {
        padding-right: 25em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.oth {
        padding-right: 9.5em;
        margin-left: -28.7em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.oth {
        margin-left: -28.6em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.oth.pre {
        margin-left: -26.97em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.oth.pre {
        margin-left: -25.3em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.down {
        margin-left: 0em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.leg {
        margin-left: -7.9em;
        width: 67.42em
    }
    .smenu li.has-sub ul.isubmenu.leg.post {
        margin-left: -8.47em
    }
    .smenu li.has-sub ul.isubmenu.leg.oidr {
        margin-left: -0.3em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.leg {
        width: 81.3em;
        margin-left: -7.86em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.leg {
        margin-left: -7.89em;
        width: 100%
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.pay {
        margin-left: -8.5em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.pay.post {
        margin-left: -21.34em
    }
    .smenu li.has-sub ul.isubmenu.pay.pan {
        margin-left: -14.95em
    }
    .smenu li.has-sub ul.isubmenu.pay.oidr {
        margin-left: -13.22em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.pay.pre {
        margin-left: -20em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.pay.pre {
        margin-left: -19.99em;
        width: 67.3em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.services {
        margin-left: -15.85em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.services.post {
        margin-left: -22.34em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.services.pre {
        margin-left: -20em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.services.pre {
        margin-left: -19.99em;
        width: 67.3em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.oth {
        width: 100%
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth {
        margin-left: -28.7em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth.post {
        margin-left: -22.3em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth.pre {
        margin-left: -26.9em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.ref {
        width: 100%;
        margin-left: -25.2em
    }
    .smenu li.has-sub ul.isubmenu.ref.post {
        width: 100%;
        margin-left: -38em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.prof {
        margin: 0 -19.6em 0 -48.4em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub:hover+a {
        border-bottom: 2px solid #0B1E59
    }
    .smenu li.has-sub:hover ul.isubmenu {
        display: block
    }
    .smenu.not {
        margin: 0 -68.1em 0 -16.2em
    }
    .smenu.not.post {
        margin-left: -16.2em
    }
    .smenu.act {
        margin: 0 -68em 0 -32em
    }
    .smenu.act.post {
        margin-left: -32em
    }
    .smenu.searchtxp {
        margin: 0 -68em 0 -32.7em
    }
    .smenu.searchtxp.post {
        margin-left: -23.5em
    }
    .smenu.helpmenu {
        margin: 0 -68em 0 -44.9em
    }
    .smenu.helpmenu.post {
        margin-left: -35.6em
    }
    .smenu.helpmenu.pan {
        margin-left: -44.9em
    }
    .smenu.down {
        margin: 0 -68em 0 -23.4em
    }
    .smenu.down.post {
        margin-left: -42.1em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu.down.post {
        width: -42.1em
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .smenu {
        width: 67.4em
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .smenu {
        width: 51.5em
    }
}


form>.row div[class*='col-'] {
    padding-top: 5px;
    padding-bottom: 5px
}

.reg {
    font-weight: 400;
    word-wrap: break-word !important
}

.m-cir:after {
    content: "\f111";
    font-family: 'fontAwesome';
    color: red;
    font-size: 6px;
    vertical-align: super;
    padding-left: 3px
}

.m-hash:after {
    content: "\f198";
    font-family: 'fontAwesome';
    color: red;
    font-size: 10px;
    vertical-align: super;
    padding-left: 3px
}

.tbl-format {
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc
}

.tbl-format .row {
    margin: 0
}

.tbl-format .row .inner div[class*='col-'] {
    padding-bottom: 20px;
    padding-top: 10px
}

.tbl-format .row .inner div[class*='col-'].has-error {
    padding-bottom: 0px
}

.tbl-format .row:nth-child(odd) {
    background-color: #f7f7f7
}

.tbl-format .row:nth-child(even) {
    background-color: #fff
}

.tbl-format:last-child {
    border-bottom: none
}

.tabpane {
    background-color: #fff;
    padding: 20px;
    min-height: 380px !important;
    height: auto
}

.tabpane.tds {
    padding: 20px 0px
}

.tabpane h4 {
    font-family: arial;
    color: #0b1e59
}

.tabpane h4.ptitle {
    font-weight: 600
}

.next-tab-nav {
    margin-top: 20px
}

legend.reg {
    font-size: 14px;
    margin-bottom: 0;
    border: none
}

.datepicker-icon {
    position: relative
}

.datepicker-icon input {
    padding-right: 30px
}

@media screen and (max-width: 1199px) {
    .datepicker-icon input {
        padding-right: 0px;
        padding-left: 4px;
        font-size: 12px
    }
}

.datepicker-icon i {
    position: absolute;
    right: 0px;
    padding: 10px 12px;
    pointer-events: none
}

textarea {
    resize: none
}

.regular-checkbox,
.chkbx {
    opacity: 0;
    position: absolute;
    height: 20px;
    width: 23px;
    margin: 6px 0px 0 !important
}

.regular-checkbox+label,
.chkbx+label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
    font-weight: 500
}

.regular-checkbox+label:before,
.chkbx+label:before {
    content: '';
    background: #fff;
    border: 2px solid #ddd;
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    margin-right: 10px;
    text-align: center
}

.regular-checkbox:checked+label:before,
.chkbx:checked+label:before {
    content: '\2714';
    font-family: 'FontAwesome';
    background: #41a910;
    color: #fff;
    font-size: 12px
}

.regular-checkbox:disabled+label:before,
.chkbx:disabled+label:before {
    background: #cacaca
}

.regular-checkbox:focus+label,
.chkbx:focus+label {
    outline: 1px solid #ddd
}

input[type=radio] {
    opacity: 0;
    position: absolute;
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
    width: 15px
}

input[type=radio]+label {
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
    font-weight: 500
}

input[type=radio]+label:before {
    content: '';
    background: #f1f1f1;
    border: 1px solid #ddd;
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    padding: 2px;
    margin-right: 10px;
    text-align: center;
    border-radius: 50%
}

input[type=radio]:checked+label:before {
    background: #41a910;
    box-shadow: inset 0px 0px 0px 4px #f1f1f1
}

input[type=radio]:disabled+label:before {
    background: #cacaca
}

input[type=radio]:focus+label {
    outline: 1px solid #ddd
}

.list-child-inline {
    padding-left: 0;
    list-style: none
}

.list-child-inline li {
    display: inline-block;
    padding-top: 5px
}

@media screen and (max-width: 760px) {
    .list-child-inline li {
        width: 100%
    }
}

@media screen and (min-width: 761px) and (max-width: 1199px) {
    .list-child-inline li {
        width: 49%
    }
}

@media screen and (min-width: 1200px) {
    .list-child-inline li {
        width: 33%
    }
}

.switch {
    border: none;
    outline: 0;
    padding: 0;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.switch label {
    background: #7e7e7e;
    color: transparent;
    cursor: pointer;
    display: block;
    position: relative;
    text-indent: 100%;
    width: 64px;
    height: 33px;
    transition: left 0.15s ease-out
}

.switch label:after {
    background: #FFFFFF;
    content: "";
    display: block;
    height: 27px;
    left: 4px;
    position: absolute;
    top: 3px;
    width: 27px;
    -webkit-transition: left 0.15s ease-out;
    -moz-transition: left 0.15s ease-out;
    -o-transition: translate3d(0, 0, 0);
    transition: left 0.15s ease-out;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0)
}

.switch input {
    left: 10px;
    opacity: 0;
    padding: 0;
    position: absolute;
    top: 9px
}

.switch input:checked+label {
    background: #41a910
}

.switch input:checked+label:after {
    left: 35px
}

.switch input:disabled+label {
    cursor: not-allowed
}

.switch.radius label {
    border-radius: 4px
}

.switch.radius label:after {
    border-radius: 3px
}

.switch.round {
    border-radius: 1000px
}

.switch.round label {
    border-radius: 32px
}

.switch.round label:after {
    border-radius: 32px
}

.switch-on {
    position: absolute;
    left: -60px;
    top: 8px;
    color: white;
    font-weight: bold;
    font-size: 13px;
    width: 0%
}

.switch-off {
    position: absolute;
    left: -29px;
    top: 8px;
    color: white;
    font-weight: bold;
    font-size: 13px;
    width: 0%
}

.wrapper {
    z-index: 5;
    text-transform: uppercase;
    color: #555;
    cursor: help;
    font-size: 20px;
    padding: 15px -50px;
    position: relative;
    text-align: center;
    -webkit-transform: translateZ(0);
    -webkit-font-smoothing: antialiased
}

.wrapper .tooltip {
    background: #fff;
    top: -200%;
    color: #000;
    display: block;
    left: 0px;
    margin-left: 100%;
    opacity: 0;
    padding: 10px 20px;
    pointer-events: none;
    position: absolute;
    min-width: 200px;
    width: 35%;
    -webkit-transform: translateY(10px);
    -moz-transform: translateY(10px);
    -ms-transform: translateY(10px);
    -o-transform: translateY(10px);
    transform: translateY(10px);
    -webkit-transition: all .25s ease-out;
    -moz-transition: all .25s ease-out;
    -ms-transition: all .25s ease-out;
    -o-transition: all .25s ease-out;
    transition: all .25s ease-out;
    -webkit-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    -moz-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    -ms-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    -o-box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28);
    box-shadow: 2px 2px 18px rgba(0, 0, 0, 0.28)
}

@media screen and (max-width: 950px) {
    .wrapper .tooltip {
        top: auto;
        bottom: 100%;
        left: auto;
        right: 0px
    }
}

.wrapper .tooltip:before {
    bottom: -20px;
    content: " ";
    display: block;
    height: 20px;
    left: 0;
    position: absolute;
    width: 100%
}

.wrapper .tooltip:after {
    border-top: solid transparent 10px;
    border-bottom: solid transparent 10px;
    border-right: solid #C9C9C9 10px;
    bottom: 42%;
    content: " ";
    height: 0;
    left: 0%;
    margin-left: -10px;
    position: absolute;
    width: 0
}

@media screen and (max-width: 950px) {
    .wrapper .tooltip:after {
        border-right: solid transparent 10px;
        border-left: solid transparent 10px;
        border-top: solid #C9C9C9 10px;
        border-bottom: none;
        border: auto;
        top: 100%;
        left: 48%;
        position: absolute;
        width: 0
    }
}

.wrapper input:focus+.tooltip {
    opacity: 1;
    pointer-events: auto;
    -webkit-transform: translateY(0px);
    -moz-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px)
}

.captcha {
    background: none;
    opacity: 1
}

.captcha-loading {
    background: transparent url("/uiassets/images/cloading.gif") center no-repeat;
    opacity: 0.3;
    object-position: -99999px 99999px;
    border: 1px solid gray
}

.fade-scale {
    transform: scale(0);
    opacity: 0;
    -webkit-transition: all .25s linear;
    -o-transition: all .25s linear;
    transition: all .25s linear
}

.fade-scale.in {
    opacity: 1;
    transform: scale(1)
}

.no-drop {
    cursor: no-drop
}

.no-drop a,
.no-drop * {
    cursor: no-drop
}

span.clearer {
    position: absolute;
    top: 0;
    right: 0;
    padding: 10px;
    cursor: pointer
}

.tooltip-inner {
    white-space: nowrap;
    max-width: inherit;
    text-align: left
}

.pdg-box {
    padding: 5px;
    cursor: pointer
}

.pdg-box-suc {
    color: #3c763d
}

.pdg-box-war {
    color: #8a6d3b
}

.pdg-box-inf {
    color: #31708f
}

.pdg-box-dgr {
    color: #a94442
}

.type-ico {
    font-size: large
}

.valign-super {
    vertical-align: super
}

.no-pad {
    padding: 0
}

.flt-right {
    float: right
}

.flt-left {
    float: left
}

.brd-tp {
    border-top: 1px
}

.no-list-style {
    padding: 0;
    list-style: none
}

#ngProgress {
    margin: 0;
    padding: 0;
    z-index: 99998;
    background-color: green;
    color: green;
    box-shadow: 0 0 10px 0;
    height: 2px;
    opacity: 0;
    -webkit-transition: all 0.5s ease-in-out;
    -moz-transition: all 0.5s ease-in-out;
    -o-transition: all 0.5s ease-in-out;
    transition: all 0.5s ease-in-out
}

#ngProgress-container {
    position: fixed;
    margin: 0;
    padding: 0;
    top: 0;
    left: 0;
    right: 0;
    z-index: 99999
}

.dimmer-holder {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background: gray;
    display: none;
    z-index: 99
}

#dimmer {
    position: absolute;
    margin: auto;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    width: 19px;
    height: 19px;
    border-radius: 100%;
    box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -o-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -ms-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -webkit-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    -moz-box-shadow: 19px 19px #0b1f59, -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf;
    animation: cssload-spin ease infinite 3.4s;
    -o-animation: cssload-spin ease infinite 3.4s;
    -ms-animation: cssload-spin ease infinite 3.4s;
    -webkit-animation: cssload-spin ease infinite 3.4s;
    -moz-animation: cssload-spin ease infinite 3.4s
}

@keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-o-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-ms-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-webkit-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

@-moz-keyframes cssload-spin {
    0%,
    100% {
        box-shadow: 19px 19px #17c4bb, -19px 19px #dfdfdf, -19px -19px #17c4bb, 19px -19px #dfdfdf
    }
    25% {
        box-shadow: -19px 19px #dfdfdf, -19px -19px #0b1f59, 19px -19px #dfdfdf, 19px 19px #0b1f59
    }
    50% {
        box-shadow: -19px -19px #17c4bb, 19px -19px #dfdfdf, 19px 19px #17c4bb, -19px 19px #dfdfdf
    }
    75% {
        box-shadow: 19px -19px #dfdfdf, 19px 19px #4f4d49, -19px 19px #dfdfdf, -19px -19px #4f4d49
    }
}

header {
    background-color: #0B1E59;
    padding: 0 0 10px 0
}

header .skip {
    background: #051547;
    color: #fff;
    text-align: right
}

header .skip a {
    color: #fff
}

header .skip a:link,
header .skip a:visited {
    text-decoration: none !important
}

header .skip a:link:active,
header .skip a:visited:active {
    text-decoration: none !important
}

header .skip ul {
    margin: 5px 0
}

header .skip ul li {
    cursor: pointer;
    font-size: 12px
}

@media screen and (max-width: 768px) {
    header .skip {
        display: none
    }
}

header .branding {
    padding-top: 10px
}

@media screen and (max-width: 320px) {
    header .branding .col-xs-12 {
        padding: 0 0 0 10px
    }
}

@media screen and (max-width: 420px) {
    header .logo {
        width: 21px
    }
}

header .site-title {
    font-size: 2em;
    display: inline;
    font-family: verdana;
    vertical-align: middle;
    margin-left: 15px
}

@media screen and (max-width: 768px) {
    header .site-title {
        font-size: 1.8em
    }
}

@media screen and (max-width: 500px) {
    header .site-title {
        font-size: 1.6em;
        margin-left: 5px
    }
}

@media screen and (max-width: 350px) {
    header .site-title {
        font-size: 1.4em
    }
}

@media screen and (max-width: 300px) {
    header .site-title {
        font-size: 1.2em
    }
}

header .site-title a {
    color: #fff
}

header .site-title a:link,
header .site-title a:visited {
    text-decoration: none !important
}

header .site-title a:link:active,
header .site-title a:visited:active {
    text-decoration: none !important
}

header .sup {
    margin-right: 10px
}

header .sup.plus:after {
    content: "+";
    vertical-align: top
}

header .sup.minus:after {
    content: "-";
    vertical-align: super
}

header .mlinks {
    display: inline;
    float: right;
    text-align: right;
    margin: 0;
    padding: 16px 0;
    font-size: 12px
}

header .mlinks a {
    color: #fff
}

@media screen and (max-width: 767px) {
    header .mlinks {
        display: none
    }
}

header .mlinks>li {
    border-right: 1px solid white;
    min-height: 22px;
    vertical-align: top;
    padding: 0 20px
}

header .mlinks>li:last-child {
    border-right: 0;
    padding-right: 0
}

@media screen and (max-width: 550px) {
    header .mlinks>li:last-child {
        display: none
    }
}

header .mlinks>li:last-child a.login {
    vertical-align: -webkit-baseline-middle
}

header .mlinks>li .dropdown {
    padding-top: 4px
}

header .mlinks>li .dropdown .lang-dpwn span {
    margin-left: 4px;
    color: #fff
}

header .mlinks>li .dropdown .lang-dpwn:after {
    font-family: 'FontAwesome';
    content: "\f107"
}

header .mlinks>li .dropdown .lang-dpwn:hover {
    text-decoration: none
}

header .mlinks>li .dropdown.open .lang-dpwn span {
    margin-left: 4px
}

header .mlinks>li .dropdown.open .lang-dpwn:after {
    font-family: 'FontAwesome';
    content: "\f106"
}

.profile-dpdwn {
    right: 0;
    min-width: 200px;
    left: inherit;
    box-shadow: 1px 10px 26px #ABABAB;
    -webkit-box-shadow: 1px 10px 26px #ABABAB;
    -moz-box-shadow: 1px 10px 26px #ABABAB;
    border-radius: 4px
}

.profile-dpdwn>li>a {
    padding: 6px 20px;
    color: #000
}

.lst-log {
    font-size: 11px;
    padding-left: 18px
}

.lst-log-dt {
    font-size: 11px;
    padding-left: 18px;
    font-weight: 600
}

.stickytop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    transition: all 2s;
    animation: slide-down 2s;
    opacity: 0.9
}

.no-stickytop {
    top: -100px
}

.lang .lang-dpdwn {
    right: 0;
    left: auto;
    top: 80%
}

footer {
    position: relative;
    padding-top: 0px;
    bottom: 0;
    left: 0;
    right: 0;
    background: #14315D
}

footer .f1 {
    padding-top: 25px
}

footer.bo {
    padding-top: 0
}

footer .expbtn {
    background: #14315D;
    position: absolute;
    height: 24px;
    width: 42px;
    left: 90.3%;
    top: -23px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px
}

@media screen and (max-width: 767px) {
    footer .expbtn {
        left: 80%
    }
}

footer .expbtn i {
    color: #fff;
    padding: 0 13px;
    font-size: 23px;
    cursor: pointer
}

footer.fix {
    position: relative
}

footer ul {
    padding: 0;
    list-style: none
}

footer .fsep {
    border-top: 1px solid #31425b;
    margin: 0
}

footer .follow {
    margin: 10px 0 0 0;
    color: #56caf0;
    display: inline-block;
    font-size: 16px
}

@media screen and (max-width: 450px) {
    footer .follow {
        font-size: 14px
    }
}

footer .social a {
    color: #14315D !important;
    display: inline-block;
    margin: 10px 20px 0 0
}

@media screen and (min-width: 768px) and (max-width: 1080px) {
    footer .social a {
        margin: 10px 10px 0 0
    }
}

footer .social a i {
    font-size: 18px;
    color: #959FB0
}

@media screen and (max-width: 450px) {
    footer .social a i {
        font-size: 16px
    }
}

footer .fhead {
    font-size: 14px;
    font-family: inherit;
    font-weight: 600;
    line-height: 30px;
    padding: 10px 0;
    color: #56caf0;
    margin: 0
}

@media screen and (max-width: 450px) {
    footer .fhead {
        font-size: 12px
    }
}

footer .fhead.l1 {
    font-size: 18px
}

footer .fhead.toll-free {
    font-size: 22px;
    color: #fff;
    border-bottom: 1px solid #475f82
}

footer .fhead.toll-free:before {
    content: "\f095";
    font: normal normal normal 22px/1 FontAwesome;
    padding-right: 10px;
    vertical-align: baseline
}

footer .fhead+ul li {
    padding: 12px 0px
}

footer .fhead+ul li a {
    color: #bed2dd;
    font-size: 13px
}

footer span.contact {
    color: #bed2dd;
    font-size: 12px;
    word-wrap: break-word
}

footer p {
    color: gray;
    display: inline-block
}

@media screen and (max-width: 300px) {
    footer .col-xs-6 {
        width: 100%
    }
}

footer .f3 {
    background: #2c4e86
}

footer .f3 p.site {
    padding: 17px 0;
    color: #FFF;
    margin: 0;
    font-size: 12px
}

@media screen and (max-width: 550px) {
    footer .f3 {
        display: none
    }
}

footer .f2 {
    border-top: 1px solid #475f82
}

footer .f2 p {
    color: #8ba8ba;
    padding: 15px 0;
    margin: 0;
    font-size: 14px;
    margin-right: 10em;
    display: inline-block;
    float: left
}

footer .f2 p.notab {
    display: none
}

@media screen and (min-width: 991px) {
    footer .f2 p.notab {
        display: block
    }
}

footer .f2 p.nomob {
    display: none
}

@media screen and (min-width: 600px) {
    footer .f2 p.nomob {
        display: block
    }
}

footer .f2 p:last-child {
    margin-right: 0
}

@media screen and (min-width: 991px) and (max-width: 1199px) {
    footer .f2 p {
        margin-right: 3em
    }
}

@media screen and (max-width: 991px) {
    footer .f2 p {
        margin-right: 2em;
        font-size: 12px
    }
    footer .f2 p:nth-child(2) {
        margin-right: 0
    }
}

@media screen and (max-width: 300px) {
    footer .f2 p {
        margin: 0;
        text-align: center
    }
}

@media screen and (max-width: 480px) {
    .no-mobile ul {
        display: none
    }
    .no-mobile p {
        padding: 10px 0
    }
    .no-mobile.scl {
        display: block
    }
    .no-mobile.scl ul {
        display: block
    }
    .no-mobile.scl li {
        display: none
    }
    .no-mobile.scl li.social {
        display: block
    }
}

.back-to-top {
    position: fixed;
    cursor: pointer;
    color: #fff;
    padding: 5px;
    background: #ccc;
    border-radius: 29px;
    width: 51px;
    height: 52px;
    text-align: center;
    display: none;
    bottom: 10px;
    right: 2%;
    z-index: 999999
}

.back-to-top p {
    margin-top: -7px;
    color: #000
}

.back-to-top i {
    font-size: 25px;
    color: #333333;
    font-weight: 600
}

.fm-links {
    display: none
}

@media screen and (max-width: 480px) {
    .fm-links {
        display: block
    }
}

.dash-pane {
    background: #fff
}

.dash-pane:first-child {
    margin-bottom: 40px
}

.tp-dash-logo {
    border-bottom: 1px solid gray
}

.tp-dash-logo img {
    padding: 5% 0 6% 8%;
    max-width: 90%
}

@media screen and (min-width: 770px) and (max-width: 991px) {
    .tp-dash-ttl,
    .tp-dash-gstin,
    .tp-pfl-lnk {
        font-size: 12px
    }
}

.tp-dash-gstin {
    border-bottom: 1px solid gray;
    padding-bottom: 5%
}

.arw-ryt {
    background: #1976d2;
    border-radius: 50px;
    padding: 0px 4px 0px 5px;
    color: #fff
}

.dp-widgt {
    padding: 5% 8%
}

.dp-con-widgt-ttl {
    padding: 1.5%;
    border-bottom: 1px solid gray
}

.dp-con-widgt-ttl h4 {
    font-family: Arial;
    display: inline-block;
    font-size: 24px;
    color: black;
    font-weight: 500;
    padding-right: 20px;
    border-right: 1px solid gray
}

.dp-con-widgt-ttl a {
    color: #1976d2;
    font-weight: 600;
    vertical-align: super
}

.dp-con-widgt-ttl .dropdown {
    display: inline-block;
    padding: 0 10px
}

.dp-con-widgt-ttl .dropdown:after {
    content: "\f107";
    font: normal normal normal 14px/1 FontAwesome;
    vertical-align: super
}

.grid-btn {
    float: right;
    display: inline-block;
    padding: 1% 0 0 0;
    margin: 0
}

.grid-btn li {
    cursor: pointer;
    list-style: none;
    display: inline-block;
    border: 1px solid #e8e5e5;
    padding: 5px 10px
}

.dp-con-widgt {
    padding: 1.5%
}

.dp-console {
    width: 32%;
    display: inline-table
}

@media screen and (max-width: 680px) {
    .dp-console {
        width: 47.5%;
        margin-right: 2%
    }
}

@media screen and (max-width: 520px) {
    .dp-console {
        width: 100%
    }
}

.dash-content {
    background: #fff;
    float: left;
    width: 72%;
    display: inline-block
}

@media screen and (max-width: 768px) {
    .dash-content {
        width: 100%
    }
}

.wbg {
    background: #fff
}

.dash-tables ul {
    background: #17c4bb;
    padding: 15px;
    margin-bottom: 0
}

.dash-tables ul li {
    list-style: none;
    display: inline-block
}

.dash-tables ul li p {
    font-family: Arial;
    color: #0b1e59;
    font-weight: 500;
    margin: 0
}

.dash-tables ul li p.lbl {
    font-size: 12px
}

.dash-tables ul li p.ttl {
    font-size: 24px
}

@media screen and (max-width: 991px) {
    .dash-tables ul li p.ttl {
        font-size: 18px
    }
}

@media screen and (max-width: 680px) {
    .dash-tables ul li p.ttl {
        font-size: 14px
    }
}

.dash-tables ul li:first-child {
    width: 30%
}

@media screen and (max-width: 551px) {
    .dash-tables ul li:first-child {
        width: 48%
    }
}

.dash-tables ul li:nth-child(2) {
    width: 20%
}

@media screen and (max-width: 551px) {
    .dash-tables ul li:nth-child(2) {
        width: 48%
    }
}

.dash-tables ul li:nth-child(3),
.dash-tables ul li:nth-child(4) {
    width: 20%
}

@media screen and (max-width: 551px) {
    .dash-tables ul li:nth-child(3),
    .dash-tables ul li:nth-child(4) {
        width: 47%
    }
}

.dash-tables ul li:nth-child(5) {
    width: 7%
}

@media screen and (max-width: 551px) {
    .dash-tables ul li:nth-child(5) {
        width: 2%
    }
}

@media screen and (max-width: 600px) {
    .dash-tables ul li:nth-child(5) {
        width: 3%
    }
}

.dash-tables ul li:nth-child(5) a {
    text-align: center;
    color: #0b1e59
}

.dash-tables ul li:nth-child(5) a i {
    font: normal normal normal 14px/1 FontAwesome
}

.dash-tables ul li:nth-child(5) a i:before {
    content: "\f106";
    font-size: 50px
}

@media screen and (max-width: 600px) {
    .dash-tables ul li:nth-child(5) a i:before {
        content: "\f106";
        font-size: 30px
    }
}

.dash-tables ul li:nth-child(5) a.collapsed i:before {
    content: "\f107";
    font-size: 50px
}

@media screen and (max-width: 600px) {
    .dash-tables ul li:nth-child(5) a.collapsed i:before {
        content: "\f107";
        font-size: 30px
    }
}

.dash-tables ul li a.down {
    color: #0b1e59;
    font-size: 12px
}

.dash-tables ul li a.down i {
    padding: 1px 4px 1px 6px;
    border-radius: 50%;
    background: #0b1e59;
    color: #fff
}

.profile {
    float: right;
    width: 25%;
    display: inline-block
}

@media screen and (max-width: 768px) {
    .profile {
        display: none
    }
}

.dp-btns {
    text-align: center;
    margin: 30px 0
}

.dp-btns div {
    display: inline-block
}

.dp-con-ftr {
    border-top: 1px solid gray;
    text-align: center
}

.dp-con-ftr p {
    margin: 10px 0
}

ul.notif {
    padding-left: 0
}

ul.notif li {
    list-style: none
}

ul.notif li p {
    font-size: 12px;
    margin: 0
}

table.dptbl {
    font-family: Verdana;
    font-size: 14px;
    font-weight: 500
}

table.dptbl caption {
    background-color: #d6d6d6;
    font-family: Arial;
    color: #212121;
    font-weight: 400;
    font-size: 18px;
    padding-left: 10px
}

@media screen and (min-width: 680px) and (max-width: 991px) {
    table.dptbl {
        font-size: 14px
    }
}

table.dptbl tr.tp-btm-brdr {
    border-bottom: 1px solid gray
}

table.dptbl tr td:last-child {
    text-align: right;
    font-weight: 600
}

.rowtp-mar {
    margin-top: 20px
}

.tbl-search {
    text-align: right;
    padding: 10px 5px
}

.tbl-search * {
    display: inline-block
}

.tbl-search input {
    width: 200px
}

.dash-tables ul li p.ttl {
    font-size: 20px !important
}

.dash-tables ul li p.lbl {
    font-size: 16px !important
}

.dash-tables ul li:first-child {
    padding-bottom: 10px
}

.no-mar,
.invsumm {
    margin: 0 !important
}

.txtblack {
    color: #000 !important
}

.disp-in {
    display: inline
}

p.f-wt {
    font-weight: 600
}

.row.search>button {
    margin-right: 15px;
    margin-bottom: 15px
}

.numb {
    position: absolute;
    top: 10px;
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    float: right;
    right: 8%
}

.invsumm {
    background-color: #17C4BB;
    padding: 3px 25px 0 0;
    color: #fff
}

.invsumm h4 {
    text-align: left;
    line-height: 1.3
}

.invsumm h4 span {
    font-size: 12px
}

.invsumm p {
    font-size: 12px;
    padding-top: 15px
}

.invsumm>.col-xs-12.taxp {
    margin-top: 10px;
    font-size: 18px
}

.invsumm>.col-xs-12.taxp h4 {
    display: inline
}

.invsumm>.col-xs-12.taxp h4+span {
    float: right
}

.nav-tabs.ret-tabs>li.active,
.nav-tabs.ret-tabs>li.active a,
.nav-tabs.ret-tabs>li.active a:focus {
    border-bottom-color: #2C4E86;
    border-left: 0;
    border-right: 0;
    border-top: 0;
    border-bottom-width: 3px;
    background-color: #17C4BB !important;
    color: #0b1e59 !important;
    cursor: pointer
}

.nav-tabs.ret-tabs li>a {
    color: #fff !important
}

.nav-tabs.ret-tabs li>a:hover,
.nav-tabs.ret-tabs li>a:focus {
    background-color: #17C4BB;
    border-color: #17C4BB;
    color: #0b1e59 !important
}

.row.mar-b {
    margin-bottom: 15px
}

.rettbl-format {
    border-bottom: transparent !important
}

.rettbl-format .row {
    margin-left: 0 !important;
    margin-right: 0 !important
}

.rettbl-format .row div[class*='col-'] {
    padding-top: 10px;
    padding-bottom: 10px
}

.rettbl-format>h4 {
    padding: 0 15px
}

input.formedit.grs {
    width: auto !important;
    float: right
}

select.formedit {
    width: auto
}

.headdashb {
    margin: 10px 0 0 0
}

.table.exp {
    margin-bottom: 20px
}

.table.exp>tbody>tr>td {
    vertical-align: middle !important
}

table .currency {
    text-align: right
}

.table.inv>thead {
    background-color: #f4f4f6 !important;
    border: 1px solid #c0c4cc
}

.table.inv>thead>tr th {
    text-align: center !important;
    vertical-align: top !important
}

.table.inv tbody>tr>td {
    vertical-align: middle !important
}

.table.inv tbody>tr>td input[title="Rate"],
.table.inv tbody>tr>td input[title="rate"] {
    width: 58px !important
}

#toggleIDcred table thead .caret-returns {
    margin: 0px;
    border-top-width: 0px;
    border-bottom-width: 0px
}

.inv>thead {
    background-color: #f4f4f6 !important;
    border: 1px solid #c0c4cc;
    vertical-align: top !important
}

:last-child>a .inv tbody>tr td {
    width: 60px
}

.newinv {
    width: 110px;
    min-width: 100%
}

.newinv>a {
    padding: 5px 5px
}

.col-sm-12 .ver {
    padding-top: 15px
}

.hide-ele {
    display: none
}

.hide-ele>td {
    display: none;
    padding: 0 !important
}

.input-group.srch {
    margin-bottom: 20px !important
}

.srch .btn {
    border-color: #ccc;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075)
}

.srch .fa-search {
    float: right;
    margin-right: 10px;
    margin-top: -25px;
    position: relative;
    color: #ccc
}

.row .addrow {
    padding: 0 !important
}

.row .addrow .btn-sm {
    margin: 5px 0 0 12px !important
}

span.ret-t-info {
    font-size: 12px
}

p.ret-info {
    font-size: 12px;
    margin: 10px 0 0 0
}

label+span.dispblock {
    display: block
}

.card {
    margin: 20px 0
}

.card .col-xs-12 {
    margin: 0 0 20px 0
}

.card .hd {
    background-color: #14385D;
    color: #fff;
    padding: 15px;
    height: 94px
}

.card .hd .inv {
    font-weight: 600;
    font-size: 16px
}

.card .hd .inv.pull-right {
    float: right;
    font-size: 20px
}

.card p {
    margin: 0 0 5px 0
}

.card .ct {
    background-color: #fff;
    padding: 15px;
    min-height: 125px;
    color: black
}

.card .ct span {
    color: #0097A7;
    padding-left: 5px;
    font-weight: 600
}

.card .ct .val {
    font-size: 14px
}

.card a:hover {
    text-decoration: none
}

.card .inner {
    border: 1px solid #14385D
}

.dev-tab {
    list-style-type: none;
    margin: 0;
    padding: 0;
    width: 100%;
    background-color: #f1f1f1;
    border-bottom: 4px solid #ddd;
    border-right: 0px solid #ddd;
    border-top: 4px solid #ddd;
    border-left: 4px solid #ddd
}

.dev-tab>li {
    text-align: left;
    border-bottom: 3px solid #ddd;
    border-right: 2px solid #ddd
}

.dev-tab>li>a {
    display: block;
    color: #000;
    padding: 8px 16px;
    text-decoration: none
}

.dev-tab>li>active>a {
    border-left: 2px solid #ffff00
}

.dev-tab>li:last-child {
    border-bottom: none
}

.dev-tab>active {
    border-right: 0px !important
}

ul.no-list {
    list-style: none
}

ul.bl-list li {
    width: 50%;
    display: inline-block;
    font-size: 12px
}

@media screen and (max-width: 768px) {
    ul.bl-list li {
        width: 50%
    }
}

@media screen and (max-width: 540px) {
    ul.bl-list li {
        width: 100%;
        font-size: 10px
    }
}

input[type=text]::-ms-clear {
    display: none
}

body {
    background: #E4E8EB
}

[data-ng-view] {
    padding: 0 15px 15px 15px
}

@media screen and (max-width: 480px) {
    [data-ng-view] {
        padding: 0px
    }
}

ul.no-list {
    list-style: none
}

ul.st-vat li {
    width: 33%;
    display: inline-block
}

@media screen and (max-width: 768px) {
    ul.st-vat li {
        width: 49%
    }
}

@media screen and (max-width: 540px) {
    ul.st-vat li {
        width: 100%
    }
}

.regapp {
    height: 100px;
    margin: 0 0 20px 0;
    color: #212121;
    padding: 18px 0
}

.regapp .col-xs-12 {
    background-color: #ffee77
}

@media screen and (max-width: 991px) {
    .regapp .appmodify,
    .regapp .appperc {
        display: none
    }
}

@media screen and (min-width: 545px) and (max-width: 991px) {
    .regapp .headapptype,
    .regapp .appdue {
        width: 50%
    }
}

@media screen and (min-width: 401px) and (max-width: 545px) {
    .regapp .headapptype,
    .regapp .appdue {
        width: 100%
    }
    .regapp .headapptype p,
    .regapp .appdue p {
        display: inline-block;
        font-size: 16px
    }
    .regapp .headapptype p:last-child,
    .regapp .appdue p:last-child {
        margin-left: 15px
    }
    .regapp .headapptype p:last-child:before,
    .regapp .appdue p:last-child:before {
        content: ":";
        padding-right: 15px
    }
}

@media screen and (max-width: 400px) {
    .regapp .headapptype p,
    .regapp .appdue p {
        display: block;
        font-size: 16px
    }
}

.regapp .apptype {
    font-size: 20px;
    font-family: verdana
}

.regapp .lbl {
    font-family: inherit;
    font-size: 18px;
    padding: 10px 0;
    margin: 0
}

.regapp .date {
    font-size: 20px;
    min-height: 28px
}

.regapp .date .dleft {
    font-size: 16px
}

@media screen and (max-width: 610px) {
    .regapp .date .dleft {
        display: none
    }
}

.regapp .status {
    font-size: 18px
}

.container-pane {
    background-color: #fff
}

.content-pane {
    background-color: #fff;
    padding: 0px 15px 30px 15px;
}

@media screen and (max-width: 480px) {
    .content-pane {
        padding: 0px 0px 30px 0px
    }
}

.mand-text {
    width: 100%;
    text-align: right;
    margin: 5px 0;
    font-size: 13px
}

.mand-text:before {
    content: "\f111";
    font-family: 'fontAwesome';
    color: red;
    font-size: 8px;
    vertical-align: super;
    padding-right: 5px
}

.mand-text-opt {
    width: 100%;
    text-align: right;
    margin: 5px 0;
    font-size: 13px
}

.mand-text-opt:before {
    content: "\f198";
    font-family: 'fontAwesome';
    color: red;
    font-size: 8px;
    vertical-align: super;
    padding-right: 5px
}

[ng\:cloak],
[ng-cloak],
[data-ng-cloak],
[x-ng-cloak],
.ng-cloak,
.x-ng-cloak {
    display: none !important
}

code.success {
    color: #fff;
    background-color: #2f7e00
}

code.failure {
    color: #fff;
    background: #ff6666
}

hr {
    margin-top: 5px;
    margin-bottom: 5px
}

.err {
    font-size: 12px;
    color: red
}

table.tpd thead {
    background-color: #f4f4f6;
    border: 1px solid #c0c4cc
}

table.tpd .currency {
    text-align: right
}

.tbl-pgnt {
    text-align: right;
    padding-right: 6px;
    vertical-align: text-top
}

.tbl-pgnt p {
    margin-right: 5px
}

.tbl-pgnt * {
    display: inline-block;
    font-weight: 600;
    font-size: 16px
}

.tbl-pgnt button {
    border: 1px solid #c0c4cc;
    padding: 3px 10px
}

.upper-input {
    text-transform: uppercase
}

::-webkit-input-placeholder {
    text-transform: initial
}

:-moz-placeholder {
    text-transform: initial
}

::-moz-placeholder {
    text-transform: initial
}

:-ms-input-placeholder {
    text-transform: capitalize
}

@media screen and (min-width: 760px) {
    :-ms-input-placeholder {
        font-size: 12px
    }
}

.radio-order.has-error {
    border: 1px solid red
}

.radio-order p {
    width: 50%;
    display: inline-block
}

@media screen and (max-width: 550px) {
    .radio-order p {
        width: 100%;
        display: block
    }
}

.input-group-btn button {
    margin: 0
}

.box-shadow {
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05) inset, 0px 0px 8px rgba(82, 168, 236, 0.6)
}

.help {
    color: #0b1e59;
    font-size: 12px
}

@media screen and (max-width: 768px) {
    header {
        transition: all 0.8s ease-out
    }
    header.sticky {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        z-index: 9999;
        transition: all 0.8s ease-in
    }
}

@media screen and (max-width: 768px) {
    .content-wrapper {
        transition: all 0.1s ease-out
    }
    .content-wrapper.sticky {
        padding-top: 100px;
        transition: all 0.1s ease-in
    }
}

.vseparator {
    position: relative;
    width: 100%;
    height: 100px;
    margin: 10px 0
}

@media screen and (max-width: 760px) {
    .vseparator {
        display: none
    }
}

.vseparator .line {
    position: absolute;
    left: 49%;
    top: 0;
    bottom: 0;
    width: 1px;
    background: #ccc;
    z-index: 1
}

.vseparator .wordwrapper {
    text-align: center;
    height: 12px;
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    margin-top: -12px;
    z-index: 2
}

.vseparator .wordwrapper .word {
    color: #ccc;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 3px;
    font: bold 12px arial, sans-serif;
    background: #fff
}

.selfie {
    text-align: center
}

@media screen and (max-width: 790px) {
    .selfie {
        text-align: left
    }
}

@media screen and (min-width: 790px) {
    .selfie .hseparator {
        display: none
    }
}

@media screen and (max-width: 790px) {
    .selfie-seprator {
        width: 100%
    }
}

.hseparator {
    overflow: hidden;
    text-align: center
}

.hseparator.upload {
    display: none
}

@media screen and (max-width: 790px) {
    .hseparator.upload {
        display: block
    }
}

.hseparator:before,
.hseparator:after {
    background-color: #000;
    content: "";
    display: inline-block;
    height: 1px;
    position: relative;
    vertical-align: middle;
    width: 50%
}

.hseparator:before {
    right: 0.5em;
    margin-left: -50%
}

.hseparator:after {
    left: 0.5em;
    margin-right: -50%
}

.autocomplete-holder {
    position: relative
}

.autocomplete-holder .autocomplete-dropdown {
    border-color: #ececec;
    border-width: 1px;
    border-style: solid;
    border-radius: 2px;
    width: 100%;
    cursor: pointer;
    z-index: 9999;
    position: absolute;
    margin-top: -6px;
    background-color: #ffffff
}

.autocomplete-holder .autocomplete-dropdown.dpdwn {
    overflow-y: scroll;
    max-height: 205px
}

.autocomplete-holder .autocomplete-dropdown .autocomplete-dropdown-all {
    padding: 6px 0;
    cursor: pointer;
    z-index: 9999;
    background-color: #ffffff;
    margin: 0;
    text-align: center;
    background: #eee
}

.autocomplete-holder .autocomplete-dropdown .autocomplete-dropdown-all p {
    margin: 0
}

.autocomplete-holder .autocomplete-dropdown .autocomplete-row {
    padding: 10px;
    color: #000000
}

.autocomplete-holder .autocomplete-selected-row {
    background-color: lightblue;
    color: #ffffff
}

.autocomplete-holder .highlight {
    color: red;
    background: yellow
}

.autocomplete-holder .autocomplete-searching {
    color: #acacac;
    font-size: 14px;
    padding: 7px
}

.autocomplete-holder .autocomplete-desc {
    font-size: 14px
}

.autocomplete-holder .autocomplete-title,
.autocomplete-holder .autocomplete-desc {
    display: inline-block
}

.gs-popup {
    list-style: none
}

.gs-popup li {
    padding: 5px 0
}

.mh-300 {
    min-height: 300px
}

.mh-100 {
    min-height: 100px
}

.mh-200 {
    min-height: 200px
}

.padded-box {
    padding: 10px
}

.page-format {
    border-top: 1px solid #cccccc;
    border-bottom: 1px solid #cccccc;
    padding-top: 15px
}

.page-format .row .inner {
    padding-left: 15px;
    padding-right: 15px
}

.page-format .row div[class*='col-'] {
    padding-top: 10px;
    padding-bottom: 10px
}

.page-format:last-child {
    border-bottom: none
}

.progressbar {
    counter-reset: step;
    padding-left: 0px
}

.progressbar li {
    list-style-type: none;
    width: 33%;
    float: left;
    font-size: 12px;
    position: relative;
    text-align: center;
    text-transform: capitalize;
    color: #7d7d7d;
    padding-top: 8px
}

.progressbar li:before {
    width: 30px;
    height: 30px;
    line-height: 30px;
    border: 2px solid #7d7d7d;
    display: block;
    text-align: center;
    margin: 0 auto 10px auto;
    border-radius: 50%;
    background-color: white
}

.progressbar li:after {
    width: 100%;
    height: 2px;
    content: '';
    position: absolute;
    background-color: #7d7d7d;
    top: 15px;
    left: -50%;
    z-index: -1 !important
}

.progressbar li:first-child:after {
    content: none;
    border: 1px solid black
}

.progressbar li.active {
    color: green
}

.progressbar li.active:before {
    border-color: #55b776
}

.progressbar li.active+li:after {
    background-color: #55b776
}

.progressbar li .ttl {
    font-size: inherit
}

@media screen and (max-width: 295px) {
    .progressbar li .ttl {
        font-size: 11px
    }
}

.progressbar.four li {
    width: 25%
}

.z1 {
    z-index: 3
}

.z2 {
    z-index: 2
}

.z3 {
    z-index: 1
}

.z4 {
    z-index: 0
}

.circle {
    border: 2px solid #408E1C;
    border-radius: 50%;
    padding: 10px;
    background-color: #408E1C;
    color: white
}

.on-page {
    background-color: #EC9D0A;
    border-color: #EC9D0A;
    padding: 10px 13px
}

.not-active {
    background-color: #7f7f7f;
    border-color: #7f7f7f;
    padding: 10px 13px
}

.progressbar li.step-done {
    color: #408E1C !important
}

.progressbar li.step-on {
    color: #EC9D0A !important
}

.ui-card {
    box-shadow: 3px 4px 9px #888888;
    background: #f8f8f8;
    padding: 20px
}

.ui-card .ui-card-icon p {
    text-align: center
}

.ui-card .ui-card-icon p i {
    font-size: 40px;
    padding: 16px 24px;
    border-radius: 54%;
    background-color: #cccccc;
    color: #fff
}

.ui-card .ui-card-icon h5 {
    font-size: 18px;
    font-weight: 300;
    margin-bottom: 10px;
    color: #fff;
    text-align: center;
    padding: 20px
}

.ui-card .ui-card-icon li {
    color: #fff
}

.ui-card.vert-card {
    padding: 0px;
    margin-top: 4%
}

.ui-card.vert-card .left {
    width: 49%;
    vertical-align: top;
    background: #00b9f5;
    display: inline-block
}

@media screen and (max-width: 768px) {
    .ui-card.vert-card .left {
        width: 100%
    }
}

.ui-card.vert-card .left .ui-card-icon p {
    padding: 35px 0 0px 0
}

.ui-card.vert-card .left ul {
    padding: 0 30px 50px 30px
}

.ui-card.vert-card .left ul li {
    line-height: 1.76em;
    padding: 5px
}

.ui-card.vert-card .right {
    width: 49%;
    display: inline-table;
    padding: 0 20px
}

@media screen and (max-width: 768px) {
    .ui-card.vert-card .right {
        width: 100%
    }
}

.ui-card.vert-card .help {
    text-align: left;
    font-size: 12px
}

.ui-card.vert-card .help.info:before {
    content: "\f05a";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    padding-right: 5px
}

.ui-card .ui-card-text {
    margin-top: 5%
}

.ui-card .ui-card-text h4 {
    text-align: center
}

.ui-card .ui-card-text p {
    margin: 20px 0
}

.ui-card.pg-center {
    margin-top: 13%
}

@media screen and (max-width: 768px) {
    .tbl-desktop {
        display: none
    }
}

.tbl-tab {
    display: none
}

@media screen and (max-width: 768px) {
    .tbl-tab {
        display: block
    }
}

.multiselect {
    position: absolute;
    width: 90%
}

.multiselect dd {
    position: relative
}

.multiselect-content ul {
    width: 100%;
    background-color: #f5f5f5;
    border: 1px solid #dedede;
    color: #212121;
    left: 0px;
    padding: 2px 15px 2px 5px;
    position: absolute;
    top: -9px;
    list-style: none;
    height: 200px;
    overflow-y: auto;
    Z-INDEX: 100;
    margin: -1px 0 0 0
}

.bigcheckbox {
    width: 17px;
    height: 17px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    margin: 7px !important
}

.bigchecklabel {
    margin-top: 5px;
    vertical-align: top
}

.hida {
    cursor: default
}

button#caret {
    border: none;
    background: none;
    position: absolute;
    top: 8px;
    right: 0px;
    color: #555555;
    font-size: 10px
}

button#caret:active,
button#caret:focus {
    outline: 0
}

.statelisterr {
    font-weight: normal
}

.statelisterr:hover {
    text-decoration: none
}

.statecancel {
    color: red;
    cursor: pointer;
    padding: 0px 5px
}

.stateselected {
    background-color: #eee;
    color: grey;
    font-size: 16px;
    margin: 4px;
    padding: 2px 10px;
    display: inline-block
}

.nobg {
    background: none
}

.mar-tb0 {
    margin-top: 0px
}

.well {
    background-color: #D0CFCF;
    margin-bottom: 0px
}

@media screen and (max-width: 759px) {
    .smenu .isubmenu {
        display: none
    }
}

@media screen and (min-width: 760px) {
    .smenu {
        margin: 0 -68em 0 -8.15em;
        background: #ebf0f2;
        padding: 0;
        width: 81.6em
    }
    .smenu.post {
        margin: 0 -68em 0 -8.15em
    }
    .smenu li {
        display: inline-block;
        padding: 0
    }
    .smenu li.has-sub {
        padding: 0
    }
    .smenu li.has-sub a {
        padding: 10px 15px;
        color: #0B1E59
    }
    .smenu li.has-sub a:hover {
        border-bottom: 2px solid #0B1E59
    }
    .smenu li.has-sub ul.isubmenu {
        display: none;
        padding: 0;
        position: absolute;
        background: #fff;
        box-shadow: 0px 6px 10px #888888
    }
    .smenu li.has-sub ul.isubmenu li {
        width: 49%;
        padding: 8px 0px
    }
    .smenu li.has-sub ul.isubmenu li a:hover {
        border: none
    }
    .smenu li.has-sub ul.isubmenu.serv {
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.ret {
        width: 100%;
        margin-left: -15.5em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.ret {
        margin-left: -13.99em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.ret.pre {
        margin-left: -13.99em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.ret.post {
        margin-left: -14.96em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.ret.oidr {
        margin-left: -6.79em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.state {
        margin-left: -5.84em
    }
    .smenu li.has-sub ul.isubmenu.state li {
        width: 32%
    }
    .smenu li.has-sub ul.isubmenu.ut {
        margin-left: -11.01em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.ut li {
        width: 32%
    }
    .smenu li.has-sub ul.isubmenu.oth {
        margin-left: -29.85em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.ret.uinu {
        width: 100%;
        margin-left: -8.5em
    }
    .smenu li.has-sub ul.isubmenu.pay.uinu {
        width: 100%;
        margin-left: -15em
    }
    .smenu li.has-sub ul.isubmenu.us.uinu {
        width: 100%;
        margin-left: -22.3em
    }
    .smenu li.has-sub ul.isubmenu.ref.uinu {
        width: 100%;
        margin-left: -31.6em
    }
    .smenu li.has-sub ul.isubmenu.pay.uinu.pre {
        width: 100%;
        margin-left: -8.5em
    }
    .smenu li.has-sub ul.isubmenu.us.uinu.pre {
        width: 100%;
        margin-left: -16em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth {
        padding-right: 25em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.oth {
        padding-right: 9.5em;
        margin-left: -28.7em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.oth {
        margin-left: -28.6em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.oth.pre {
        margin-left: -26.97em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.oth.pre {
        margin-left: -25.3em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.down {
        margin-left: 0em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.leg {
        margin-left: -7.9em;
        width: 67.42em
    }
    .smenu li.has-sub ul.isubmenu.leg.post {
        margin-left: -8.47em
    }
    .smenu li.has-sub ul.isubmenu.leg.oidr {
        margin-left: -0.3em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.leg {
        width: 81.3em;
        margin-left: -7.86em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu li.has-sub ul.isubmenu.leg {
        margin-left: -7.89em;
        width: 100%
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.pay {
        margin-left: -8.5em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.pay.post {
        margin-left: -21.34em
    }
    .smenu li.has-sub ul.isubmenu.pay.pan {
        margin-left: -14.95em
    }
    .smenu li.has-sub ul.isubmenu.pay.oidr {
        margin-left: -13.22em
    }
    .smenu li.has-sub ul.isubmenu.pay.uin {
        margin-left: -6.4em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.pay.pre {
        margin-left: -20em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.pay.pre {
        margin-left: -19.99em;
        width: 67.3em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.services {
        margin-left: -15.85em;
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.services.post {
        margin-left: -22.34em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.services.pre {
        margin-left: -20em
    }
}

@media screen and (min-width: 760px) and (min-width: 992px) and (max-width: 1199px) {
    .smenu li.has-sub ul.isubmenu.services.pre {
        margin-left: -19.99em;
        width: 67.3em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.us {
        width: 100%
    }
    .smenu li.has-sub ul.isubmenu.us.uin {
        margin-left: -13.7em
    }
    .smenu li.has-sub ul.isubmenu.oth {
        width: 100%
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth {
        margin-left: -28.7em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth.post {
        margin-left: -22.3em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.oth.pre {
        margin-left: -26.9em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.ref {
        width: 100%;
        margin-left: -25.2em
    }
    .smenu li.has-sub ul.isubmenu.ref.post {
        width: 100%;
        margin-left: -38em
    }
}

@media screen and (min-width: 760px) and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.ref.ntp {
        width: 100%;
        margin-left: -38em
    }
}

@media screen and (min-width: 760px) and (min-width: 760px) {
    .smenu li.has-sub ul.isubmenu.ref.uin {
        width: 100%;
        margin-left: -23em
    }
}

@media screen and (min-width: 760px) and (min-width: 1200px) {
    .smenu li.has-sub ul.isubmenu.prof {
        margin: 0 -19.6em 0 -48.4em
    }
}

@media screen and (min-width: 760px) {
    .smenu li.has-sub:hover+a {
        border-bottom: 2px solid #0B1E59
    }
    .smenu li.has-sub:hover ul.isubmenu {
        display: block
    }
    .smenu.not {
        margin: 0 -68.1em 0 -16.2em
    }
    .smenu.not.post {
        margin-left: -16.2em
    }
    .smenu.act {
        margin: 0 -68em 0 -32em
    }
    .smenu.act.post {
        margin-left: -32em
    }
    .smenu.searchtxp {
        margin: 0 -68em 0 -32.7em
    }
    .smenu.searchtxp.post {
        margin-left: -23.5em
    }
    .smenu.helpmenu {
        margin: 0 -68em 0 -44.9em
    }
    .smenu.helpmenu.post {
        margin-left: -35.6em
    }
    .smenu.helpmenu.pan {
        margin-left: -44.9em
    }
    .smenu.down {
        margin: 0 -68em 0 -23.4em
    }
    .smenu.down.post {
        margin-left: -42.1em
    }
}

@media screen and (min-width: 760px) and (min-width: 768px) and (max-width: 991px) {
    .smenu.down.post {
        width: -42.1em
    }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {
    .smenu {
        width: 67.4em
    }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
    .smenu {
        width: 51.5em
    }
}