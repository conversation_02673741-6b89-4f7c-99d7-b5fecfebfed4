	/*
  	Flaticon icon font: Flaticon
  	Creation date: 13/07/2018 09:39
  	*/

@font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

[class^="flaticon-"]:before, [class*=" flaticon-"]:before,
[class^="flaticon-"]:after, [class*=" flaticon-"]:after {   
  font-family: Flaticon;
        font-size: 20px;
font-style: normal;
margin-left: 20px;
}

.flaticon-rupee:before { content: "\f100"; }
.flaticon-add-user:before { content: "\f101"; }
.flaticon-mic:before { content: "\f102"; }
.flaticon-accept-circular-button-outline:before { content: "\f103"; }
.flaticon-personal-profile:before { content: "\f104"; }
.flaticon-boxes:before { content: "\f105"; }
.flaticon-route:before { content: "\f106"; }
.flaticon-marker:before { content: "\f107"; }
.flaticon-user:before { content: "\f108"; }
.flaticon-briefcase:before { content: "\f109"; }