.yellowtable{
    width: 100%;
    background-color: #ffe479;
    height: auto;
    margin-bottom: 15px;
    font-size: 20px;
}
.td{
    padding: 10px;
}
.table-custom {
    height: auto;
    grid-row: auto;
    overflow: auto;
    white-space: nowrap;
    width: 100%;
}
.details{
    color: #2c4e86;
    font-size: 16px;
    font-weight: 700;
}
.textcolor{
   color: #2c4e86;
}
.mandpos{
    top:5px;
}
.bgcolor{
     background-color: #F5F5F5;
     border-top:1px solid #bbbbbb;
     margin: 0 auto;
}
.bgcolor1{
     background-color: #F5F5F5;
     margin: 0 auto;
}
.fleftbg{
    float: left;
    background-color: #F5F5F5;
    margin-left: 0px;
    height: auto;
    padding:20px;
}
.fleftnbg{
    float: left;
    padding-top: 5px;
    height:auto;
    padding:20px;
}
.fleft{
  float: left;
}
.fntextbox{
    display:inline;
    width: 100%;
    height: 32px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 0px;
}
.valign{
    padding-top: 5px;
}
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}
.th{
    text-align: center;
    border: 1px solid #f2f2f2;
    border-collapse: collapse;
}
.table-custom {
    height: auto;
    grid-row: auto;
    overflow: auto;
    white-space: nowrap;
    width: 100%;
    border: 1px solid #f2f2f2;
}
.generate_challan{
    display:block;
    color: white;
    position: relative;
    right: 0px;
    float:right;
    margin-bottom: 0;
    text-transform: uppercase;
    font-family: inherit;
    margin: 10px 0;
    text-align: center;
    cursor: pointer;
    background-color:#2c4e86;
    border: 1px solid transparent;
}
.addbtn{
  position: relative;
  top:37px;
  width:auto;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-right: 15px;
  padding-left: 15px;
}
.btnright{
  float:right;
    margin-right: 15px;
}
.lstbtn{
  margin-bottom: 35px;
}
.imguploadview{
  font-size: 40px;
}
.iconview{
  font-size: 20px;
}
.iconrotate{
  transform:rotateY(180deg);
}
.others{
  height: 20px;
  width: 20px;
  color: #0B1E59;
}
.bottomborder{
  border-bottom: 1px solid #ccc;
}

.checkboxes  {
  font-family:Open Sans Italic;
  font-size: 12px;
  color: #666;
  border-radius: 20px 20px 20px 20px;
  background: #f0f0f0;
  padding: 3px 10px;
  text-align: left;
}
input[type=checkbox]:checked + label {
  color: white;
  background: #86b3c1;
}
.content-pane label{
  color: #2c4e86;
}
.content_flex{
  display: flex;
  align-items: stretch;
}
input[type=file]#upload{
  display: none;
}
.upload{
  background:#2c4e86;
  color: white;
  width:100px;
  height:34px;
  padding:6px 12px;
  text-align: center;
  margin-top: 5px;
}
.imgupload{
  height: 50px;
  width: 80px;
  margin-top: 5px;
}
.btn-margin{
  margin-right: 5px;
}
.imagetxt-margin{
  display:table-row-group;
}
.text_align_center{
 margin:0 auto;
 text-align: center;
}
.ascbox{
  color: #2c4e86;
}
.linkcolor{
  color: #198df8;
}
.switch input {display:none;}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #888888;
  -webkit-transition: .4s;
  transition: .4s;
   border-radius: 34px;
}
.successicon{
  width: 15px;
  height: 15px;
}
.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #2ab934;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  -webkit-transform: translateX(26px);
  -ms-transform: translateX(26px);
  transform: translateX(25px);
}
.slider:after
{
 content:'No';
 color: white;
 display: block;
 position: absolute;
 transform: translate(-50%,-50%);
 top: 50%;
 left: 70%;
 font-size: 10px;
 font-family: Verdana, sans-serif;
}

input:checked + .slider:after
{
  content:'Yes';
  top: 50%;
 left: 30%;
}

.button-wrapper{
    display: flex;
    flex-wrap:wrap;
}
#accountForm .form-control{

    padding: auto;
    background: none;
    box-shadow: none;
}
.other-radio{
    display: inline-block;
    vertical-align: middle;
    margin: 5px;
    cursor: pointer;
    font-weight: 500;
}
.other-radio:before{
    background: #41a910;
    content: '';
    border: 1px solid #ddd;
    display: inline-block;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    padding: 2px;
    margin-right: 10px;
    text-align: center;
    border-radius: 50%;
}
.step.bv-tab-success {
    background: #2c4e86;
    color:#ffffff;

}
.step.bv-tab-success>a{
    color:#fff;
}
.step.bv-tab-success.active{
    background: #ffffff;
}

.nav-tabs>li.active>a, .nav-tabs>li.active>a:hover, .nav-tabs>li.active>a:focus{
    height:100%;

}
#goodservice .nav-tabs>li.active>a, .nav-tabs>li.active>a:hover, .nav-tabs>li.active>a:focus{
    height:auto;
}
.fa-times{
    color:#c12525;
}
.p4_btn{
    border:none;
    height:32px;
}
.help-block{
    color:#c12525;
}
.fa-check{
    color:green;
}
.nav-tabs>li>a:hover{
    background: none;
    border:none;
}
/* Make circles that indicate the steps of the form: */
.progress-tab li{
    width:10%;
    height:120px;
    text-align: center;
  background-color: #ebf0f2;
  border: 1px solid #8ba8ba;
    border-right:none;
}
.progress-tab li:last-child{
   border-right:1px solid #8ba8ba;
}
.progress-tab li i:before{
   margin-left:0;
   font-size: 25px;
  }
.progress-tab li i{

    display: block;
    text-align: center;
}
.nav-tabs>li>a{
    font-size:12px;
}

.nav-tabs{
    border:none;
}
/*.step.active {*/
 /*background-color:white;*/
 /*margin-left:0;*/
 /*border: 1px solid #0B1E59;*/
 /*!*box-shadow: 1px 1px 8px 1px #888888;*!*/
/*}*/

/* Mark the steps that are finished and valid: */
.step.finish {
  background-color:#0B1E59;
  color: white;
  border-right:1px solid white;
}

a:hover,a:focus{
    outline: none;
    text-decoration: none;
}
.tab .nav-tabs{ border-bottom: 2px solid #e8e8e8; }
.tab .nav-tabs li a{
    padding: 15px 15px;
    margin: 0 5px 1px 0;
    background: #fff;
    font-size: 13px;
    font-weight: 600;
    color: #0B1E59;
    text-align: center;
    border: none;
    border-radius: 0;
    z-index: 2;
    position: relative;
    transition:all 0.3s ease 0s;
}
.tab .nav-tabs li a:hover,
.tab .nav-tabs li.active a{
    color: #198df8;
    border: none;
}
.tab .nav-tabs li.active a:after{
    content: "";
    width: 100%;
    height: 3px;
    background: #198df8;
    position: absolute;
    bottom: -1px;
    left: 0;
    z-index: -1;
    transition: all 0.3s ease 0s;
}
.tab .tab-content{
    padding:0px;
    margin-top: 0;
    background: #fff;
    color:#2c4e86;
}
.tab .tab-content h3{
    font-size: 24px;
    margin-top: 0;
}
.padding{
  padding-top:30px;
  padding-bottom: 30px;
}
.btnalign{
  height: 32px;
  padding: 6px;
  background-color: #2c4e86;
  color: white;
  border: 1px solid #2c4e86;
  margin: 0px;
}
.fntextboxbtn{
    display:inline;
    width: 200px;
    height: 32px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 0px;
}
.hidetabs{
    display: none;
}
#tab1[data-show="on"]{
  display: block !important;
}
#tab2[data-show="on"]{
  display: block !important;
}
#tab3[data-show="on"]{
  display: block !important;
}
#tab4[data-show="on"]{
  display: block !important;
}
#tab5[data-show="on"]{
  display: block !important;
}
#tab6[data-show="on"]{
  display: block !important;
}
#tab7[data-show="on"]{
  display: block !important;
}
#tab8[data-show="on"]{
  display: block !important;
}
#tab9[data-show="on"]{
  display: block !important;
}
#tab10[data-show="on"]{
  display: block !important;
}
.display-screen{
  display: block !important;
}

.breadcrumb{
    background: none;
}
.update{
    position: absolute;
    top:0;
    right:0;
    font-size: 10px;
}
.update:before{
    font-size:10px;
}