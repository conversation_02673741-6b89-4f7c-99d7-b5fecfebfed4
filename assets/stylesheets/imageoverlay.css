.image-wrapper {
    width: 100%;
    height: 200px;
    border: 1px solid rgba(0, 0, 0, 0.4);
    overflow: hidden;
    position: relative;
    text-align: center;
    border-radius: 4px;

    display: flex;
    align-items: center;
}
.image-wrapper > a{
    font-size: 12px;
width: 100%;
}
.image-wrapper a span{

display: block;
    margin-top: 1rem;
}
.image-overlay-content {
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
    top: 0;
    left: 0; }
.justify-center{
    justify-content: center;
    display: flex;
}
.overlay-fade-in p {
    letter-spacing: 0.15em;
    color: #f4f4f4;
    font-size: 28px;
    opacity: 0;
    transition: all 0.2s linear; }
.overlay-fade-in img {
    transition: all 0.2s linear; 
    width: 100%;
}
.overlay-fade-in .image-overlay-content {
    opacity: 0;
    background-color: rgba(0, 0, 0, 0.4);
    transition: all 0.4s ease-in-out; }
.overlay-fade-in h2 {
    color: #f2f2f2;
    font-size: 1.8rem;
    margin-top: 40%;
    opacity: 0;
    transition: all 0.2s ease-in-out;
    background: rgba(0, 0, 0, 0.7); }
.overlay-fade-in .button {
    display: inline-block;
    text-decoration: none;
    padding: 7px 14px;
    background: #FFF;
    color: #222;
    text-transform: uppercase;
    box-shadow: 0 0 1px #000;
    position: relative;
    border: 1px solid #999;
    opacity: 0;
    transition: all 0.2s ease-in-out; }
.overlay-fade-in .button:hover {
    box-shadow: 0 0 5px #000; }
.overlay-fade-in:hover img {
    transform: scale(1.2); }
.overlay-fade-in:hover .image-overlay-content {
    opacity: 1; }
.overlay-fade-in:hover h2, .overlay-fade-in p, .overlay-fade-in .button {
    opacity: 1; }
.overlay-fade-in:hover p {
    transition-delay: 0.1s; }
.overlay-fade-in:hover .button {
    transition-delay: 0.2s; }

.overlay-slide-in-left img {
    transition: all 0.3s ease-in-out; }
.overlay-slide-in-left .image-overlay-content {
    background-image: url(http://www.awwwards.com/awards/images/2015/04/pattern.jpg);
    background-color: rgba(0, 0, 0, 0.3);
    transform: translateX(-110%);
    opacity: 1;
    transition: all 0.4s ease-in-out;
    box-shadow: 0 0 7px #ccc; }
.overlay-slide-in-left h2 {
    font-size: 1.8rem;
    background: rgba(255, 255, 255, 0.7);
    margin-top: 40%;
    color: #000;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.1); }
.overlay-slide-in-left p {
    opacity: 0;
    color: #333;
    transition: all 0.2s linear; }
.overlay-slide-in-left .button {
    display: inline-block;
    text-decoration: none;
    padding: 7px 14px;
    background: #000;
    color: #fff;
    text-transform: uppercase;
    box-shadow: 0 0 1px #000;
    position: relative;
    border-radius: 7px; }
.overlay-slide-in-left .button:hover {
    background-color: #444; }
.overlay-slide-in-left:hover .image-overlay-content {
    transform: translateX(0px); }
.overlay-slide-in-left:hover img {
    transform: translateX(80%);
    transition-delay: 0.1s; }
.overlay-slide-in-left:hover p {
    opacity: 1;
    transition-delay: 0.4s; }

.overlay-fade-in-new-background .button {
    display: inline-block;
    text-decoration: none;
    padding: 7px 14px;
    background: #000;
    color: #fff;
    text-transform: uppercase;
    border-radius: 5px;
    box-shadow: 0 0 1px #000;
    position: relative;
    opacity: 0;
    transition: all 0.2s ease-in-out; }
.overlay-fade-in-new-background .button:hover {
    box-shadow: 0 0 5px #fff;
    background-color: #222; }
.overlay-fade-in-new-background p {
    font-size: 28px; }
.overlay-fade-in-new-background .image-overlay-content {
    opacity: 0;
    background-image: url(http://subtlepatterns.com/patterns/gplaypattern.png);
    background-color: rgba(255, 255, 255, 0.5);
    transition: all 0.4s ease-in-out; }
.overlay-fade-in-new-background h2 {
    color: #000;
    font-size: 1.6rem;
    margin-top: 30%;
    opacity: 0;
    transition: all 0.2s ease-in-out; }
.overlay-fade-in-new-background p {
    opacity: 0;
    transition: all 0.2s linear; }
.overlay-fade-in-new-background img {
    transition: all 0.2s ease-in-out; }
.overlay-fade-in-new-background:hover img {
    filter: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg'><filter id='grayscale'><feColorMatrix type='matrix' values='0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0'/></filter></svg>#grayscale");
    filter: gray;
    -webkit-filter: grayscale(100%);
    transform: scale(1.5); }
.overlay-fade-in-new-background:hover .image-overlay-content {
    opacity: 1; }
.overlay-fade-in-new-background:hover h2, .overlay-fade-in-new-background p, .overlay-fade-in-new-background .button {
    opacity: 1; }
.overlay-fade-in-new-background:hover p {
    transition-delay: 0.1s; }
.overlay-fade-in-new-background:hover .button {
    transition-delay: 0.2s; }

label .smallText{
    font-size: 12px;
    height: 150px;
    display: flex;
    align-items: center;
}
.panelTab{
    margin-top: 1rem;
    border: none;
}
.panelTab > li.active > a{
    color:#F79420;
}
.panelTab > li.active > a, .panelTab > li.active > a:hover, .panelTab > li.active > a:focus{
    color:#F79420;
    background: transparent;
    border: none;
    border-bottom: 2px solid #F79420;;
}
#chapterssection{
    margin-top: 3rem;
}
.red{
    color:darkred;
    margin-left: 20px;

}
.dflex{
    display: flex;
}