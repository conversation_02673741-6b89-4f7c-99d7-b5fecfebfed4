
p {
    margin: 0 0 20px
}
.feature .face img {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 180px;
    height: 80px;
    margin-top: -40px;
    margin-left: -90px
}

.feature .face:after {
    width: 100%;
    height: 50px;
    content: "";
    display: block;
    position: absolute;
    top: -50px;
    right: 0
}

.feature.one .face {
    background: #111 url(../images/flip/appready.jpg) no-repeat left bottom
}



.feature.two .face {
    background: #111 url(../images/flip/free-ebook-image_small.jpg) no-repeat left bottom
}



.feature.three .face {
    background: url(../images/flip/interactive.jpg) no-repeat left bottom
}



.feature.four .face {
    background: url(../images/flip/analytics.jpg) no-repeat right bottom
}


.feature.four .face img {
    left: 38%
}



.face {
    position: absolute;
    height: 100%;
    width: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.face.primary {
    background: #fff;
    overflow: hidden;
    z-index: 2
}

.face.secondary {
    background-size: cover
}

#work {
    width: 100%;
    height: 100px;
    background: #fff;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    display: none
}

.trigger {
    width: 25%;
    float: left;
    position: relative;
    height: 100%;
    cursor: pointer;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.feature {
    width: 100%;
    height: 100%;
    -webkit-transition: -webkit-transform .4s ease-in-out, opacity 1.5s ease-in-out;
    -webkit-transition: opacity 1.5s ease-in-out, -webkit-transform .4s ease-in-out;
    transition: opacity 1.5s ease-in-out, -webkit-transform .4s ease-in-out;
    transition: transform .4s ease-in-out, opacity 1.5s ease-in-out;
    transition: transform .4s ease-in-out, opacity 1.5s ease-in-out, -webkit-transform .4s ease-in-out;
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    opacity: 0;
    -webkit-transform: rotateX(0deg) translateZ(0);
    transform: rotateX(0deg) translateZ(0)
}

.feature.show {
    opacity: 1
}

.feature.rotate {
    -webkit-transform: rotateX(-90deg) translateZ(0);
    transform: rotateX(-90deg) translateZ(0)
}

.trigger:hover .feature,
.stick .feature {
    -webkit-transform: rotateX(90deg) translateZ(0);
    transform: rotateX(90deg) translateZ(0);
    bottom: 10px
}

.ie .trigger:hover .feature,
.ie .stick .feature {
    -webkit-transform: rotateX(0) translateZ(0);
    transform: rotateX(0) translateZ(0)
}

.feature .face {
    background: #e73692;
    -webkit-transform: rotateX(0deg) translateZ(50px);
    transform: rotateX(0deg) translateZ(50px);
    -webkit-transition: box-shadow .5s ease-in-out;
    transition: box-shadow .5s ease-in-out;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 2;
    background: #111
}

.ie .feature .face {
    -webkit-transform: rotateX(0) translateZ(0);
    transform: rotateX(0) translateZ(0);
    position: relative
}

.feature.rotate .face {
    box-shadow: inset 0px 30px 60px transparent
}

.feature .face.hover {
    box-shadow: inset 0px -30px 100px rgba(0, 0, 0, 0.2);
    -webkit-transform: rotateX(-90deg) translateZ(50px);
    transform: rotateX(-90deg) translateZ(50px);
    overflow: hidden
}

.ie .feature .face.hover {
    -webkit-transform: rotateX(0) translateZ(0);
    transform: rotateX(0) translateZ(0)
}

.feature .face.hover .info {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75);
    color: #fff;
    padding: 15px 30px;
    box-sizing: border-box
}

.ie .feature .face.hover .info {
    opacity: 0;
    -webkit-transition: opacity .3s linear;
    transition: opacity .3s linear
}

.ie .trigger:hover .feature .face.hover .info {
    opacity: 1
}

.feature-shadow {
    box-shadow: 2px 2px 2px #888888;


}