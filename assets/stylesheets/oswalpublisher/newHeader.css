.transition {
  transition: all 0.5s ease;
}
.bounce {
  animation: headerBounce 0.5s ease;
  transition: all 0.5s ease-in;
}
.mobile_cart_icon {
  position: relative;
}
.account .dropdown-toggle::after {
  display: none;
}
.cart_count {
  position: absolute;
  top: -10px;
  left: 15px;
  background: #000;
  color: #fff;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  text-align: center;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.9;
}
@media screen and (max-width: 768px) {
  #loginOpen .modal-content {
    margin-top: 50px;
  }
}
.oswal__header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky !important;
  top: 0;
  z-index: 999999!important;
  background: #fff;
}
.oswal__header-navbar {
  display: flex;
  align-items: center;
  padding: 1rem 1.2rem;
  justify-content: space-between;
}
@media screen and (max-width: 768px) {
  .oswal__header-navbar {
    justify-content: space-between;
  }
}
.oswal__header-navbar__list {
  display: flex;
  list-style: none;
  margin-bottom: 0;
  padding-left: 0;
  gap: 0.5rem;
  align-items: center;
}
@media screen and (max-width: 768px) {
  .oswal__header-navbar__list {
    gap: 0.3rem;
  }
}
.oswal__header-navbar__list-item {
  margin-right: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.oswal__header-navbar__list-item:after {
  content: '';
  display: block;
  border-bottom: 2px solid orange;
  transform: scaleX(0);
  transition: transform 0.3s ease-in-out;
}
.oswal__header-navbar__list-item:hover:after {
  transform: scaleX(1);
}
.oswal__header-navbar__list-item a {
  color: #000;
  display: flex;
  align-items: center;
}
.oswal__header-navbar__list-item a:hover {
  text-decoration: none;
}
@media screen and (max-width: 768px) {
  .oswal__header-navbar__list-item a i {
    font-size: 22px;
  }
}
.oswal__header-navbar__list-item .dropdown-menu {
  width: 220px;
  font-size: 12px;
  padding: 0;
  border-radius: 5px !important;
}
.oswal__header-navbar__list-item .dropdown-menu .dropdown-menu-wrapper {
  display: flex;
  flex-direction: column;
}
.oswal__header-navbar__list-item .dropdown-menu .dropdown-menu-wrapper a {
  transition: background 0.3s ease;
  padding: 5px;
}
.oswal__header-navbar__logo .logo-imgText {
  width: 200px;
}
@media screen and (max-width: 768px) {
  .oswal__header-navbar__logo .logo-imgText {
    width: 130px !important;
  }
}
.oswal__header-searchBar {
  padding-bottom: 10px;
  padding-top: 10px;
  background: #fff;
  position: relative;
  text-align: center;
}
.oswal__header-searchBar:after {
  content: '';
  position: absolute;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  width: 75%;
  top: 0;
  left: 50%;
  right: 50%;
  transform: translate(-50%, -50%);
}
.oswal__header-searchBar input {
  width: 90%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  outline: none;
  border-radius: 5px;
  padding: 8px;
}
@media screen and (max-width: 768px) {
  .oswal__header-searchBar input {
    width: 100%;
    padding: 4px;
  }
}
@keyframes headerBounce {
  0% {
    transform: translateY(-20px);
  }
  50% {
    transform: translateY(-40px);
  }
  100% {
    transform: translateY(0);
  }
}
