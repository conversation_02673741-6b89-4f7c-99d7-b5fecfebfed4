.oswal_publisher .white-logo-anchor-white img {
  width: 60px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswal_publisher .white-logo-anchor-white img {
    width: 100%;
  }
}
.oswal_publisher .manage-logo-big-menu-arihant img {
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswal_publisher .manage-logo-big-menu-arihant img {
    width: 80%;
  }
}
.oswal_publisher .categories-section-disc-title-pera {
  color: #212121;
}
.oswal_publisher a {
  font-weight: normal;
}
.oswal_publisher p {
  line-height: normal;
}
.oswal_publisher .global-search input[type="text"] {
  padding-left: 10px;
  padding-right: 40px;
  z-index: 3;
}
@media (max-width: 320px) {
  .oswal_publisher .global-search input[type="text"] {
    padding-right: 20px;
  }
}
.oswal_publisher .global-search button {
  width: auto;
  height: 33px;
  margin-left: -38px;
  padding: 4px;
  position: relative;
  z-index: 10;
  color: #FF7201 !important;
}
.oswal_publisher .global-search button .material-icons {
  line-height: normal;
}
.oswal_publisher .add-tabs {
  top: 75px;
}
.oswal_publisher #allAddButton {
  display: none !important;
}
.oswal_publisher.hasScrolled .main-menu-wrp {
  position: relative !important;
}
.oswal_publisher.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 0;
}
.oswal_publisher.hasScrolled .bookTemplate .export-notes {
  top: 52px;
}
.oswal_publisher.hasScrolled .user-menu-wrp .menu-actives a.menu-dots-img-wrp {
  position: fixed;
}
.oswal_publisher .bookTemplate .content-wrapper {
  height: calc(100vh - 50px);
}
@media (max-width: 767px) {
  .oswal_publisher .bookTemplate.book_preview .content-wrapper .read-content.col-md-12 .price-wrapper {
    display: none !important;
  }
  .oswal_publisher .bookTemplate.book_preview .content-wrapper #book-read-material {
    padding-bottom: 70px;
  }
  .oswal_publisher .bookTemplate.book_preview .content-wrapper #book-sidebar {
    height: calc(100vh - 70px);
    padding-bottom: 0;
  }
  .oswal_publisher .bookTemplate.book_preview .content-wrapper .price-wrapper {
    padding: 0.5rem 0;
    position: fixed;
    width: 100%;
    z-index: 991;
  }
  .oswal_publisher .bookTemplate.book_preview .content-wrapper .price-wrapper .section-btns {
    padding: 0.5rem 0 0;
    background: #FFFFFF;
  }
  .oswal_publisher .bookTemplate.book_preview .content-wrapper .price-wrapper .preview-book-btns {
    margin-left: 0;
  }
}
.oswal_publisher .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 100px;
}
.oswal_publisher .bookTemplate .export-notes {
  top: 130px;
}
.oswal_publisher .bookTemplate .mobChapname #chapters-toggle.left i {
  transform: rotate(0deg);
}
.oswal_publisher .bookTemplate .preview-book-btns .btn-book-buy {
  background: #FF7201 !important;
}
.oswal_publisher .bookTemplate .ChapterHeader .bookTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.oswal_publisher .web-mcq .sub-header {
  top: 90px;
  padding-top: 10px;
}
.oswal_publisher .bg-wsTheme {
  background: #FF7201 !important;
}
.oswal_publisher #question-block .question-wrapper {
  margin-top: 2rem;
}
.oswal_publisher #question-block .question-wrapper img {
  max-width: 100%;
}
.oswal_publisher .tab-wrappers {
  top: 190px;
}
.oswal_publisher .web-mcq .result-menu {
  top: 90px;
}
.oswal_publisher #quizQuestionSection {
  padding-bottom: 50px;
}
.oswal_publisher .book_details_info #filelabel1,
.oswal_publisher .book_details_info #filelabel2 {
  right: 0;
  left: 0;
}
.oswal_publisher #bookcover .smallText {
  justify-content: center;
}
.oswal_publisher .orders .payment-details > div:last-child p .rupees {
  display: none;
}
.oswal_publisher .users-orders > p {
  padding-right: 15px;
  padding-left: 15px;
}
.oswal_publisher .user_profile .tab-content .jumbotron form .media .continue,
.oswal_publisher .btn-starts,
.oswal_publisher #answer-block .button-wrapper a,
.oswal_publisher #answer-block .button-wrapper a:hover {
  background: #FF7201 !important;
}
.oswal_publisher .test-gen-box-main .test-gen-box .btn-info {
  background: #FF7201 !important;
  border-color: #FF7201 !important;
}
.oswal_publisher .test-gen-box-main .test-gen-box .btn-info:active:focus {
  box-shadow: 0 0 0 0.2rem rgba(239, 114, 21, 0.5) !important;
}
.oswal_publisher .dropdown-menu a:active,
.oswal_publisher .dropdown-menu span:active,
.oswal_publisher .dropdown-menu li:active {
  background-color: #fce5d4;
}
.oswal_publisher .all-container .container-wrapper .media .quiz-practice-btn,
.oswal_publisher .all-container .container-wrapper .media .showRank {
  text-transform: uppercase;
  color: #FF7201;
  padding: 0;
  display: inherit;
  border-radius: 0;
}
.oswal_publisher .all-container .container-wrapper .media .showRank {
  padding-left: 10px;
  margin-left: 10px;
  border-left: 1px solid #212121;
}
.oswal_publisher .all-container .container-wrapper .d-flex p.testStarts {
  margin: 0;
  top: 0;
  font-size: 12px;
  flex: none;
}
.oswal_publisher .play::before {
  left: -20px;
}
.oswal_publisher .backfromgenerator {
  margin-top: 20px;
}
.oswal_publisher .backfromgenerator i {
  color: #FF7201;
}
.oswal_publisher #htmlContent {
  margin-top: 2rem;
}
.oswal_publisher .purchase-details-container .purchase-heading {
  background: none !important;
  -webkit-text-fill-color: unset !important;
}
.oswal_publisher .purchase-details-container .browse-purchase-book a.learn-btn {
  background: #FF7201 !important;
  color: #FFFFFF;
}
.oswal_publisher .web-mcq .mt-fixed {
  margin-top: 3.5rem !important;
  padding-top: 0;
}
.oswal_publisher.custom-fix .bookTemplate .shadowHeader {
  position: fixed;
  top: 0;
}
.oswal_publisher .start-test .header p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  padding: 0 15px;
}
.oswal_publisher #quizQuestionSection .result-menu > div h2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding: 0 10px;
}
.oswal_publisher .mt-fixed #resourceTitle {
  margin-top: 50px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  padding: 0 15px;
}
.oswal_publisher .notes-creation-header {
  z-index: 1;
}
.oswal_publisher .index-page .main-menu-wrp {
  position: relative;
}
.oswal_publisher .index-page .header-menu-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  text-align: center;
  width: 100%;
}
.oswal_publisher .index-page .this-is-a-web-view-slider {
  display: block;
  overflow: hidden;
}
@media (max-width: 991px) {
  .oswal_publisher .index-page .this-is-a-web-view-slider {
    display: none;
  }
}
.oswal_publisher .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  min-height: 80vh;
  background-size: 100% auto !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}
@media (min-width: 1600px) {
  .oswal_publisher .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
    background-position: left top !important;
  }
}
.oswal_publisher .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
  width: 100%;
  max-width: 100%;
  min-height: auto;
  max-height: 70vh;
}
@media (min-width: 2000px) {
  .oswal_publisher .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
    min-height: auto;
    max-height: 70vh;
  }
}
@media (max-width: 1199px) {
  .oswal_publisher .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
    min-height: auto;
    max-height: 100%;
  }
}
.oswal_publisher .index-page .this-is-a-responsive-view-slider {
  display: none;
}
@media (max-width: 991px) {
  .oswal_publisher .index-page .this-is-a-responsive-view-slider {
    display: block;
  }
}
.oswal_publisher .index-page .this-is-a-responsive-view-slider img {
  width: 100%;
}
.oswal_publisher .index-page .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #ffffff;
}
.oswal_publisher .index-page .carousel-indicators li.active {
  background: #FF7201;
}
.oswal_publisher .ebooks .ebooks_filter {
  width: 100%;
}
.mozilla .arihant .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  background-attachment: unset !important;
}
#guestUser,
#loginOpen,
#signup,
#forgotPasswordmodal,
#shareContentModal,
#deleteBook,
#change-password-modal,
#currentPomodoro,
#pomodoroSessionCompletion,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal,
#bookQueueModal,
#test-gen-modal,
#PlayAudiOnlyModal,
#quizModal {
  z-index: 9992;
}
.mobile-footer-nav {
  background: #FF7201;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -webkit-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -moz-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px 20px 0 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.mobile-footer-nav.hide-menus {
  bottom: -75px;
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
}
.mobile-footer-nav a {
  text-decoration: none;
  flex-wrap: wrap;
  text-align: center;
  color: #FFFFFF;
}
.mobile-footer-nav a:focus {
  text-decoration: none;
}
.mobile-footer-nav a:visited {
  text-decoration: none;
}
.mobile-footer-nav a img {
  margin: 0 auto;
  width: 22px;
}
.mobile-footer-nav a p {
  width: 100%;
  font-size: 13px;
}
.mobile-footer-nav i {
  color: white;
}
.mobile-footer-nav .active-menu {
  opacity: 1;
}
.mobile-footer-nav .common-footer-nav {
  opacity: 0.8;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
#test-gen-modal .overlay-testgen-book .book-selected {
  width: 40px;
  height: 40px;
}
#quizQuestionSection #submit-test .modal-footer button {
  font-family: 'Poppins', sans-serif !important;
}
#quizQuestionSection #submit-test .modal-footer .submit {
  background: #FF7201;
  color: #FFFFFF;
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: portrait), only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .arihant #book-read-material #content-data-all {
    padding: 0;
  }
  .arihant #book-read-material #content-data-all > .container {
    padding: 0;
  }
  .arihant .all-container .container-wrapper {
    margin-top: 0;
    border: none;
    border-bottom: 1px solid #ededed;
    box-shadow: none;
    border-radius: 0;
  }
  .arihant .all-container .container-wrapper .media i {
    margin: 0 1rem;
  }
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .arihant .bookTemplate .export-notes {
    top: 51px !important;
  }
}
@media only screen and (max-width: 767px) {
  .arihant h2#expiry-date {
    margin-top: 0 !important;
  }
}
.footer .download-app-links .android-app-link {
  border: 1px solid #ddd;
  padding: 2px 0 5px;
  border-radius: 7px;
  min-width: 140px;
  background-color: #e5e5e5;
}
.footer .download-app-links .android-app-link:hover {
  background-color: #e7e7e7;
}
.footer .download-app-links .android-app-link img {
  width: 20px;
  margin-right: 10px;
  height: auto;
}
.footer .download-app-links .android-app-link span {
  line-height: normal;
  font-size: 15px;
}
.footer .download-app-links .android-app-link span small {
  position: relative;
  top: 3px;
}
.footer .image-wrapper-footer-logo img {
  width: 180px;
}
.oswal_publisher #total-books-of-user {
  display: none;
}
.oswal_publisher #loginOpen .modal-header .close,
.oswal_publisher #signup .modal-header .close,
.oswal_publisher #forgotPasswordmodal .modal-header .close {
  font-size: 20px;
}
.oswal_publisher .main-menu-wrp {
  background: linear-gradient(90deg, #ef3e08 0%, #FF7201 100%);
}
.oswal_publisher .main-menu-wrp .posi-static-respons {
  display: flex;
  align-items: center;
}
.oswal_publisher p,
.oswal_publisher a,
.oswal_publisher button {
  font-weight: initial;
}
.oswal_publisher .bookTemplate .side-content ol li.chapter-name i {
  position: relative;
  top: 4px;
}
.oswal_publisher .bookTemplate .chapterSection a.slide-toggle {
  top: 190px;
}
.oswal_publisher .manage-count-wrp-box:after {
  background: linear-gradient(50deg, #FF7201 0%, #ef3e08 100%);
}
.oswal_publisher .connect-section {
  background: linear-gradient(90deg, #FF7201 0%, #ef3e08 100%);
}
.oswal_publisher .red-color-fill-bg {
  background: #FF7201;
}
.oswal_publisher .responsive-padding-none:nth-child(even) a {
  background-color: #ee3539 !important;
}
.oswal_publisher ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss,
.oswal_publisher ul.this-is-side-wrp-ul-big-menu-arihant li:hover {
  background: linear-gradient(90deg, #FF7201 0%, #ef3e08 100%);
}
.oswal_publisher .categories-section {
  background: none;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: auto;
}
.oswal_publisher .categories-section .line-box-category {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 50px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswal_publisher .categories-section .line-box-category {
    bottom: 20px;
  }
}
.oswal_publisher .ebook_detail .book_info .book_buttons .col #buyNow {
  color: #FFFFFF;
}
.oswal_publisher #okBuy {
  color: #FFFFFF;
}
.oswal_publisher .there-social-footer-link-wrp li a:hover {
  color: #FF7201;
}
.oswal_publisher .my_books .no-books-available .click-here-link {
  background-color: #FF7201 !important;
  color: #FFFFFF;
}
.oswal_publisher #cartModalBtns .btn-primary {
  background-color: #FF7201 !important;
  border-color: #FF7201 !important;
  box-shadow: none !important;
  color: #FFFFFF !important;
}
.oswal_publisher .bookTemplate .shadowHeader {
  height: 50px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswal_publisher .bookTemplate {
    height: auto;
  }
  .oswal_publisher .bookTemplate .mobChapname {
    transition: all 0.3s;
    z-index: 991;
  }
  .oswal_publisher .bookTemplate .shadowHeader {
    z-index: 991;
    height: 45px !important;
    position: fixed;
    transition: all 0.3s;
  }
  .oswal_publisher .bookTemplate .shadowHeader .tab-header .navbar {
    height: auto;
    padding-top: 0.15rem;
  }
  .oswal_publisher .bookTemplate .shadowHeader .tab-header .contentEdit {
    position: fixed;
    top: 50px;
    right: 0;
    transition: all 0.3s;
    overflow: hidden;
  }
  .oswal_publisher .bookTemplate .shadowHeader .prevnextbtn {
    position: fixed;
    top: 50px;
    width: 100% !important;
    justify-content: center !important;
    transition: all 0.3s;
  }
  .oswal_publisher .bookTemplate .shadowHeader .prevnextbtn button {
    margin: 0 5px;
    width: 80px;
    font-size: 13px;
  }
  .oswal_publisher .bookTemplate .chapterSection {
    z-index: 991;
  }
  .oswal_publisher .bookTemplate #book-sidebar .backtolibrary {
    font-weight: normal !important;
  }
  .oswal_publisher .bookTemplate #book-sidebar .mobile-title {
    z-index: 98;
  }
  .oswal_publisher .bookTemplate #book-sidebar .mobile-title p {
    line-height: normal;
    padding-left: 10px;
  }
  .oswal_publisher .bookTemplate #book-sidebar .side-content ol {
    padding-left: 10px !important;
  }
  .oswal_publisher .bookTemplate #book-sidebar .side-content ol li.chapter-name {
    font-size: 15px;
    position: relative;
    margin-right: 1.5rem;
    padding-right: 20px;
  }
  .oswal_publisher .bookTemplate #book-sidebar .side-content ol li.chapter-name.orangeText a {
    font-size: 15px;
  }
  .oswal_publisher .bookTemplate #book-sidebar ul.chapter-sections {
    display: none;
  }
  .oswal_publisher .bookTemplate #book-read-material #content-data-all {
    position: relative;
    z-index: 99;
  }
  .oswal_publisher .bookTemplate #book-read-material .all-container {
    margin-top: 2rem;
  }
  .oswal_publisher .bookTemplate #book-read-material .all-container .container-wrapper {
    width: 100%;
    margin-top: 1rem;
    min-height: auto;
  }
  .oswal_publisher .bookTemplate #book-read-material .all-container .container-wrapper .media {
    padding: 0;
  }
  .oswal_publisher .bookTemplate #book-read-material .all-container .container-wrapper .media i {
    margin-left: 0;
  }
  .oswal_publisher .bookTemplate #book-read-material .all-container .container-wrapper .media .title {
    margin-bottom: 5px;
  }
  .oswal_publisher .bookTemplate #book-read-material .all-container .container-wrapper .media .readnow {
    padding-right: 15px;
  }
  .oswal_publisher .bookTemplate #htmlreadingcontent iframe {
    height: 100vh !important;
    border: 1px solid #6C757D;
    margin-top: 0 !important;
  }
  .oswal_publisher .bookTemplate .export-notes {
    top: 90px !important;
  }
  .oswal_publisher .bookTemplate .export-notes .notes-creation-header {
    padding: 0.5rem 0;
  }
  .oswal_publisher .bookTemplate .export-notes .notes-creation-header-title {
    font-size: 16px;
    color: #212121;
  }
  .oswal_publisher.hasScrolled .bookTemplate .tab-header .contentEdit {
    top: 5px;
  }
  .oswal_publisher.hasScrolled .bookTemplate .export-notes {
    top: 45px !important;
  }
  .oswal_publisher.hasScrolled .bookTemplate .shadowHeader .prevnextbtn {
    top: 7px;
  }
  .oswal_publisher .logo-wrapper {
    display: none;
  }
  .oswal_publisher .my_books #subjectFilter .dropdown #sortBy {
    width: auto;
  }
}
.oswal_publisher .purchase-details-container .purchase-details-wrapper .learn-btn {
  background: #FF7201;
}
.oswal_publisher .menu-overlay-big-menus.actv {
  background: rgba(0, 0, 0, 0.7);
}
.oswal_publisher .menu-bar-wrp ul li a {
  color: #6C757D;
  border-color: #6C757D;
}
.oswal_publisher .menu-bar-wrp ul li a:hover {
  color: #FF7201;
}
.footer-container {
  background-color: #FFFFFF;
  background-image: none;
}
.footer-container strong {
  font-weight: 600;
}
.footer-container p,
.footer-container small,
.footer-container address {
  color: #777;
}
.footer-container .main-footer .top-footer {
  border-bottom: 1px dashed #FF7201;
}
.footer-container .main-footer .top-footer img {
  width: 200px;
  height: auto;
}
.footer-container .main-footer .top-footer address {
  font-size: 14px;
  margin: 0;
  position: relative;
}
.footer-container .main-footer .top-footer address a {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.footer-container .main-footer .top-footer p {
  margin-bottom: 10px;
  position: relative;
}
.footer-container .main-footer .top-footer p a {
  color: #777;
}
.footer-container .main-footer .middle-footer .row {
  margin-left: -10px;
  margin-right: -10px;
}
.footer-container .main-footer .middle-footer .row .col-md-3 {
  padding: 10px;
}
.footer-container .main-footer .middle-footer h5 {
  font-weight: normal;
  margin-bottom: 15px;
}
.footer-container .main-footer .middle-footer ul li {
  color: #777;
  margin-bottom: 10px;
}
.footer-container .main-footer .middle-footer ul li:hover {
  color: #FF7201;
}
.footer-container .main-footer .middle-footer ul li a {
  color: #777;
}
.footer-container .main-footer .middle-footer ul li a:hover {
  color: #FF7201;
}
.footer-container .main-footer .middle-footer .icon-with-links a {
  border-radius: 50%;
  width: 36px;
  height: 36px;
  background-color: #f2f2f2;
  color: #949494;
  display: inline-block;
  margin: 0 3px 3px 0;
  vertical-align: middle;
  text-align: center;
  font-size: 0;
  transition: all 0.25s ease;
}
.footer-container .main-footer .middle-footer .icon-with-links a i {
  font-size: 16px;
  line-height: 36px;
}
.footer-container .main-footer .middle-footer .icon-with-links .social-links a:hover {
  background-color: #FF7201;
  color: #FFFFFF;
}
.footer-container .main-footer .middle-footer .icon-with-links .available-links a {
  padding: 11px;
}
.footer-container .main-footer .middle-footer .icon-with-links .available-links a img {
  width: 1rem;
  height: 1rem;
  position: relative;
  display: block;
}
.footer-container .main-footer .middle-footer .icon-with-links .available-links a.amazon:hover {
  background-color: #c6c6c6;
}
.footer-container .main-footer .middle-footer .icon-with-links .available-links a.flipkart:hover {
  background-color: #cad9f0;
}
.footer-container .main-footer .middle-footer .icon-with-links .available-links a.google-books:hover {
  background-color: #d1eacf;
}
.footer-container .main-footer .middle-footer .icon-with-links .available-links a.kindle:hover {
  background-color: #ffcdc0;
}
.footer-container .main-footer .middle-footer .icon-with-links .available-links a.snapdeal:hover {
  background-color: #f6c6d5;
}
.footer-container .copyrights-wrapper {
  border-top: 1px solid rgba(0, 0, 0, 0.105);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .footer-container .copyrights-wrapper {
    padding-bottom: 75px;
  }
}
.footer-container .copyrights-wrapper .col-12 {
  line-height: 1.2;
}
.footer-container .copyrights-wrapper small a {
  font-size: 110%;
  color: #212121;
}
.footer-container .copyrights-wrapper small.confident {
  font-weight: 600;
  color: #ff7856;
}
.footer-container .copyrights-wrapper .payments {
  width: 255px;
  height: 22px;
}
.category_list {
  display: flex;
  flex-direction: column;
}
.category_list .category_level {
  margin-bottom: 2rem;
}
.category_list .category_level h4 {
  position: relative;
  margin-bottom: 1.4rem;
}
.category_list .category_level h4:after {
  width: 50px;
  height: 2px;
  content: '';
  position: absolute;
  background: #FF7201;
  left: 0;
  bottom: -4px;
}
.category_list .category_level .category_cards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 1.2rem;
}
@media (max-width: 768px) {
  .category_list .category_level .category_cards {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 1rem;
  }
}
.category_list .category_level .category_cards .category_card {
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 80px;
  background: #eee;
  transition: all 0.3s ease-in;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000;
}
.category_list .category_level .category_cards .category_card:active {
  transform: scale(0.7);
}
.category_list .category_level .category_cards .category_card:hover {
  background: transparent;
}
.category_list .category_level .category_cards .category_card a {
  color: black;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.category_list .category_level .category_cards .category_card-title {
  width: 100%;
  text-align: center;
}
@media (max-width: 768px) {
  .category_list .category_level .category_cards .category_card {
    width: 100%;
  }
}
