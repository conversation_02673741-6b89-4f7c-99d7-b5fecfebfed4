@font-face {
  font-family: "wonderslate";
  src: url("../fonts/wonderslate/fonts/wonderslate.eot");
  src: url("../fonts/wonderslate/fonts/wonderslate.eot?#iefix") format("embedded-opentype"), url("../fonts/wonderslate/fonts/wonderslate.woff") format("woff"), url("../fonts/wonderslate/fonts/wonderslate.ttf") format("truetype"), url("../fonts/wonderslate/fonts/wonderslate.svg#wonderslate") format("svg");
  font-weight: normal;
  font-style: normal;
}
[data-icon]:before {
  font-family: "wonderslate" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "wonderslate" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
  content: "\61";
  vertical-align: middle;
}

.icon-checkbox-deselected:before {
  content: "\62";
  vertical-align: middle;
}

.icon-checkbox-selected:before {
  content: "\63";
  vertical-align: middle;
}

.icon-search-light:before {
  content: "\64";
  vertical-align: middle;
}

.icon-search-dark:before {
  content: "\65";
  vertical-align: middle;
}

.icon-close:before {
  content: "\66";
  vertical-align: middle;
}

.icon-comment:before {
  content: "\67";
  vertical-align: middle;
}

.icon-done:before {
  content: "\68";
  vertical-align: middle;
}

.icon-error-dark:before {
  content: "\69";
  vertical-align: middle;
}

.icon-error-light:before {
  content: "\6a";
  vertical-align: middle;
}

.icon-filter:before {
  content: "\6b";
  vertical-align: middle;
}

.icon-help:before {
  content: "\6c";
  vertical-align: middle;
}

.icon-text-format:before {
  content: "\6d";
  vertical-align: middle;
}

.icon-list:before {
  content: "\6f";
  vertical-align: middle;
}

.icon-sort:before {
  content: "\70";
  vertical-align: middle;
}

.icon-settings:before {
  content: "\71";
  vertical-align: middle;
}

.icon-radio-selected:before {
  content: "\72";
  vertical-align: middle;
}

.icon-radio-deselected:before {
  content: "\73";
  vertical-align: middle;
}

.icon-add:before {
  content: "\6e";
  vertical-align: middle;
}

.icon-bookmark:before {
  content: "\74";
  vertical-align: middle;
}

.icon-chevron:before {
  content: "\75";
  vertical-align: middle;
}

.icon-dropdown:before {
  content: "\76";
  vertical-align: middle;
}

.icon-favorite:before {
  content: "\77";
  vertical-align: middle;
}

.icon-fullscreen:before {
  content: "\78";
  vertical-align: middle;
}

.icon-grid:before {
  content: "\79";
  vertical-align: middle;
}

.icon-hamburger:before {
  content: "\7a";
  vertical-align: middle;
}

.icon-reload:before {
  content: "\41";
  vertical-align: middle;
}

html, body {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  overflow: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

a, p {
  color: #444;
}

::-webkit-scrollbar {
  width: 7px;
  height: 4px;
}

::-webkit-scrollbar-track {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.05);
}

/*.question {*/
  /*font-family: 'Cardo', serif;*/
  /*font-size: 14px;*/
  /*font-weight: bold;*/
  /*padding: 16px 10px;*/
  /*border-bottom: 1px solid rgba(189, 189, 189, 0.55);*/
/*}*/
.question img {
  margin-top: 10px;
}

.option-string {
  color: #444;
  font-weight: 300;
  font-size: 14px;
}

.passage {
  font-weight: 500;
  line-height: normal;
  font-size: 12px;
  color: #000000;
  margin-top: 5px;
}

.directions {
  font-weight: 500;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.54);
  color: #9a9a9a;
  margin-top: 16px;
  margin-bottom: 5px;
}

.question-box {
  width: 100%;
  float: left;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12);
  margin-bottom: 16px;
  border-radius: 4px;
}

.practice-score-container {
  color: #fff;
  text-align: center;
  background: linear-gradient(74.18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -webkit-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -o-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: linear-gradient(18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  padding: 26px 0;
}
.practice-score-container .practice-score {
  width: 274px;
  margin: 0 auto;
}
.practice-score-container .practice-score .medal-picture {
  width: 135px;
  height: 138px;
  margin: 0 auto;
}
.practice-score-container .practice-score .medal-picture img {
  width: 100%;
}
.practice-score-container .practice-score .practice-score-string p {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}
.practice-score-container .practice-score .practice-score-string .practice-score-string-score {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.answer-summary {
  max-height: 440px;
  padding: 24px 0;
  text-align: center;
}
.answer-summary p {
  margin: 0;
}
.answer-summary .summary-heading {
  font-size: 15px;
  font-weight: bold;
}
.answer-summary .short-heading {
  font-size: 16px;
}
.answer-summary .score-summary {
  max-width: 634px;
  margin: 0 auto;
  margin-top: 16px;
}
.answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
  width: 100px;
  height: 100px;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(43.98deg, #2A7E0D 14.96%, #4DEB17 100%);
  padding: 34px 35px 36px 34px;
  margin: 0 auto;
  margin-top: 8px;
  border-radius: 16px;
}
.answer-summary .score-summary .wrong-answers {
  background: linear-gradient(45deg, #97160D 20.42%, #F76E64 100%);
}
.answer-summary .score-summary .skipped-answers {
  color: #444444;
  background: #fff;
  border: 2px solid #444444;
}

.accuracy-summary {
  max-width: 634px;
  padding-bottom: 16px;
  margin: 0 auto;
  margin-top: 26px;
}
.accuracy-summary .time-taken, .accuracy-summary .answer-accuracy {
  display: inline-block;
  width: 32px;
  height: 32px;
  color: #fff;
  font-size: 18px;
  font-weight: bold;
  background: url("../images/booksmojo/clock-mobile.png");
  background-repeat: no-repeat;
  background-size: 100%;
  padding: 15px;
  border-radius: 500px;
  margin-top: 8px;
}
.accuracy-summary .answer-accuracy {
  background: url("../images/booksmojo/accuracy-mobile.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.accuracy-summary .time-span {
  display: inline-block;
  font-size: 16px;
  font-weight: bold;
  vertical-align: top;
  margin-top: 15px;
  margin-left: 5px;
  padding-right: 10px;
  border-right: 1px solid rgba(68, 68, 68, 0.54);
}

.correct-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #46B520;
  padding: 10px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.wrong-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #B72319;
  padding: 10px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.question-summary-help-icon {
  display: inline-block;
  width: 25px;
  height: 25px;
  background: url("../images/booksmojo/help-icon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-indent: -9999999px;
}

.question-summary-help {
  position: relative;
  float: right;
}

.question-summary-tooltip {
  display: none;
  position: relative;
  right: 0;
  width: 232px;
  list-style: none;
  text-align: left;
  background: #fff;
  padding: 16px;
  margin: 0;
  margin-top: 30px;
  border-radius: 6px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
}
.question-summary-tooltip li {
  display: block;
  padding: 10px;
}
.question-summary-tooltip li .indicator-div {
  display: inline-block;
  width: 40px;
  height: 40px;
}
.question-summary-tooltip li p {
  display: inline-block;
  margin-left: 10px;
  -webkit-transform: translateY(-55%);
  -moz-transform: translateY(-55%);
  -ms-transform: translateY(-55%);
  -o-transform: translateY(-55%);
  transform: translateY(-55%);
}
.question-summary-tooltip li .correct {
  background: url("../images/booksmojo/correct.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.question-summary-tooltip li .wrong {
  background: url("../images/booksmojo/wrong.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.question-summary-tooltip li .skipped {
  background: url("../images/booksmojo/skipped.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.btn-review {
  display: block;
  width: 173px;
  height: 48px;
  color: #444444;
  font-size: 16px;
  font-weight: bold;
  background: #FFFFFF;
  padding: 15px;
  margin: 0 auto;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}
.btn-review:hover {
  color: #444444 !important;
}

.chapter-details-area {
  height: 100%;
}

.answer-holder-inner {
  float: left;
}

.correct-answer-label {
  font-size: 12px;
  font-weight: bold;
  color: #888888;
}
.correct-answer-label .correct-answer {
  color: #444444;
  font-size: 12px;
}

.correct-answer-by-user {
  color: #46B520 !important;
  font-size: 12px;
}

.wrong-answer-by-user {
  color: #B72319 !important;
  font-size: 12px;
}

.wrong-answer-label {
  color: #B72319 !important;
  font-size: 12px;
  font-weight: bold;
}

.mcq-learn {
  border-left: 0 !important;
}
.mcq-learn::before {
  content: '';
  border-left: 0 !important;
}

.show-explanation {
  color: #888;
  float: right;
}

.show-explanation-btn {
  color: #888;
  font-size: 12px;
  font-weight: bold;
}

.correct-answer-explanation {
  display: none;
  width: 100%;
  background-color: #FCFCFC;
  padding: 10px;
  float: left;
  overflow: auto;
  box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
}

@media screen and (min-width: 768px) and (max-width: 1536px) {
  .quiz-modal-body {
    min-height: 525px;
    max-height: 525px;
  }

  .score-container {
    min-height: 525px;
    max-height: 525px;
  }
}
@media screen and (max-width: 768px) {
  .submit-quiz-btn {
    width: auto;
  }

  .mcq-question-div::before {
    content: '';
    border-left: 0;
  }

  .btn-review {
    width: 100%;
  }

  .close-modal.next-btn.pull-right {
    width: 100%;
  }

  .answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
    width: 64px;
    height: 64px;
    padding: 15px;
  }
  .answer-summary .score-summary .wrong-answers {
    width: 64px;
    height: 64px;
    padding: 15px;
  }
  .answer-summary .score-summary .skipped-answers {
    width: 64px;
    height: 64px;
    padding: 15px;
  }
}
.question-div-mcq img {
  height: 200px !important;
  width: 200px !important;
  margin-top: 15px;
}

/*.options-string {*/
  /*border-bottom: 1px solid rgba(189, 189, 189, 0.55);*/
/*}*/

/*.options-string.active {*/
  /*background: rgba(240, 90, 42, 0.1);*/
  /*border-top: 1px solid #F05A2A;*/
/*}*/

.with-less-with {
  width: 90%;
  float: none;
  margin: 0 auto;
}

.answer-explanation-row {
  margin: 0 !important;
}



input[type=checkbox] {
  position: absolute;
  opacity: 0;
}

.checkmark {
  position: absolute;
  top: 20px;
  right: 40px;
  height: 22px;
  width: 22px;
  background-color: transparent;
  border: 2px solid #888;
}

.checkmark::after {
  left: 3px;
  top: 4px;
  width: 12px;
  height: 7px;
  border: solid #F05A2A;
  border-width: 3px 3px 0 0;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}

.checkmark-radio {
  position: absolute;
  top: 50%;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: rgba(68, 68, 68, 0.74);
  border-radius: 50px !important;
  transform: translateY(-50%);
}

input[type="radio"] ~ .checkmark {
  color: #fff;
  font-size: 14px;
  text-align: center;
  background-color: rgba(68, 68, 68, 0.74);
  border-radius: 50px;
}

input[type="radio"]:checked ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
  border-radius: 50px;
}

input[type="checkbox"] ~ .checkmark {
  color: #fff;
  font-size: 14px;
  text-align: center;
  background-color: rgba(68, 68, 68, 0.74);
  border-radius: 0 !important;
}

.user-input::after {
  content: '';
  position: absolute;
  display: none;
}

input[type="radio"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block;
  z-index: -1;
}

.checkmark-radio::after {
  left: 0;
  top: 0;
  width: 5px;
  height: 5px;
  border: 10px solid #F05A2A;
  border-radius: 10px;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}

input[type="checkbox"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block;
  z-index: -1;
}

.checkmark-checkbox::after {
  left: 0;
  top: 0;
  width: 5px;
  height: 5px;
  border: 10px solid #F05A2A;
  border-radius: 0;
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}

.analysis-summary {
  float: left;
  width: 100%;
  background: #fff;
  text-align: center;
  padding: 24px 0;
}

.row-heading {
  font-size: 22px;
  font-weight: 500;
}

.analysis-by-book-detail {
  margin-top: 16px;
}

.collapsed-detail-container {
  text-align: center;
}

.collapsed-detail-wrapper {
  list-style-position: inside;
  max-width: 503px;
  text-align: center;
  padding: 0;
  padding-left: 16px;
  margin: 0 auto;
}

.collapsed-detail-list-item-chapter {
  display: inline-block;
  font-weight: 300;
  font-size: 16px;
  margin: 0 0 4px;
}

.collapsed-detail-list-item-chapter-score {
  color: rgba(68, 68, 68, 0.74);
  font-weight: bold;
  margin-left: 24px;
}

.analysis-book-name {
  display: block;
  font-weight: 300;
  font-size: 18px;
  line-height: 26px;
  padding-bottom: 16px;
}
.analysis-book-name:hover {
  text-decoration: none;
  color: #444;
}
.analysis-book-name:active {
  text-decoration: none;
  color: #444;
}
.analysis-book-name:focus {
  text-decoration: none;
  color: #444;
}
.analysis-book-name i {
  display: inline-block;
  margin-left: 12px;
}
.analysis-book-name i.rotated-i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.analysis-book-name i.simple {
  -webkit-transform: rotate(0deg) !important;
  -moz-transform: rotate(0deg) !important;
  -ms-transform: rotate(0deg) !important;
  -o-transform: rotate(0deg) !important;
  transform: rotate(0deg) !important;
}

.badge-success, .badge-warning, .badge-danger {
  color: #fff;
  background: linear-gradient(0deg, #46B520, #46B520), #B72319;
  margin-left: 16px;
  border-radius: 8px;
}

.badge-warning {
  background: linear-gradient(0deg, #F2C94C, #F2C94C), #B72319;
}

.badge-danger {
  background: #B72319;
}

.collapse-hr {
  width: 256px;
  margin: 0 auto 16px;
}

.suggestions-for-user {
  float: left;
  width: 100%;
  text-align: center;
  background: #fff;
  margin: 24px 0;
}

.div-separator {
  float: left;
  width: 100%;
  height: 24px;
  background: #F8F8F8;
}

.suggested-topics {
  font-weight: 300;
  text-align: left;
  line-height: 26px;
  font-size: 16px;
  padding: 0 32px;
}
.suggested-topics .icon-chevron {
  display: inline-block;
  margin-left: 8px;
  -webkit-transform: rotate(270deg);
  -moz-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  -o-transform: rotate(270deg);
  transform: rotate(270deg);
}

.topic-link {
  color: #2EBAC6;
}

.depth-details {
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 14px;
  color: #888;
  padding-right: 16px;
}
.depth-details:hover {
  text-decoration: none;
}
.depth-details .icon-chevron {
  display: inline-block;
  margin-left: 4px;
  -webkit-transform: rotate(270deg);
  -moz-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  -o-transform: rotate(270deg);
  transform: rotate(270deg);
}

.table-div {
  background: #F8F8F8;
  padding: 16px;
  margin-top: 16px;
  margin-bottom: 16px;
  overflow: auto;
}

.detailed-table {
  width: 100%;
  text-align: center;
}
.detailed-table tr, .detailed-table td {
  color: #444444;
  text-align: center;
  padding: 8px;
}
.detailed-table tr {
  border-bottom: 1px solid rgba(68, 68, 68, 0.74);
}
.detailed-table th.table-chapters {
  text-align: center;
}
.detailed-table td.table-chapters-data {
  text-align: center;
}
.detailed-table th {
  font-weight: 500;
  color: rgba(68, 68, 68, 0.74);
  text-align: center;
  padding: 8px;
}
.detailed-table .correct {
  color: #46B520;
}
.detailed-table .incorrect {
  color: #B72319;
}

.expand-table-btn {
  font-size: 12px;
  background: #FFFFFF;
  border: 0.5px solid rgba(68, 68, 68, 0.54);
  padding: 6px;
  box-sizing: border-box;
  border-radius: 4px;
}
.expand-table-btn:hover {
  color: #444;
  text-decoration: none;
}
.expand-table-btn:active {
  color: #444;
  text-decoration: none;
}
.expand-table-btn:focus {
  color: #444;
  text-decoration: none;
}

/*# sourceMappingURL=webquiz.css.map */
