h1,
h2,
h3,
h4,
p,
input,
span,
button {
  font-family: '<PERSON>pin<PERSON>', sans-serif;
}
.main-section {
  min-height: 600px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .main-section {
    min-height: auto;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .navbar-brand img {
    width: 100px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .navbar button {
    font-size: 8px;
  }
}
.navbar .footer-logos {
  width: 85px;
  margin-right: 5px;
}
.banner {
  background: url("../../images/wslibrary/banner.svg") right no-repeat;
  background-size: contain;
  height: 400px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .banner .container-fluid {
    padding: 0;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .banner {
    background: none;
    border-radius: 0px 0px 20px 20px;
    height: 100%;
    margin-top: 2rem;
    padding-bottom: 0;
  }
}
.banner .banner-img {
  min-height: 450px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .banner .banner-img {
    height: 220px;
    position: relative;
    bottom: -70px;
    min-height: 100%;
  }
}
.btn-login {
  background: none;
  color: black;
  border: 1.25px solid rgba(68, 68, 68, 0.85);
  box-sizing: border-box;
  filter: drop-shadow(0px 0px 10px rgba(0, 0, 0, 0.1));
  border-radius: 10px;
  font-size: 10px;
}
.banner-section {
  margin-top: 4rem;
  padding-bottom: 4rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .banner-section {
    margin-top: 0;
  }
}
.banner-section h1 {
  font-weight: bold;
  font-size: 36px;
  color: #444444;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .banner-section h1 {
    font-size: 24px;
  }
}
.banner-section h1 span {
  color: rgba(68, 68, 68, 0.48);
  font-weight: normal;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
}
.banner-section p {
  color: #2ec1c0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
}
.banner-section .btn-catalog {
  background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #2FD1AE 0%, #2C8FB7 100%);
  color: white;
  font-size: 14px;
  border-radius: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .banner-section .btn-catalog {
    background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #358EF0 0%, #394696 100%);
    background: -webkit-gradient(196.34% 2285.13% at -0.25% 0%, #358EF0 0%, #394696 100%);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }
}
.ebook-banner {
  margin-top: 6rem;
  background: url("../../images/wslibrary/bgOrange.svg") right no-repeat;
  background-size: cover;
  height: 400px;
  display: flex;
  align-items: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .ebook-banner {
    margin-top: 4rem;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ebook-banner .container {
    text-align: center;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ebook-banner {
    height: 100%;
    padding: 2rem 0;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ebook-banner img {
    margin: 2rem auto;
  }
}
.features {
  min-height: 600px;
}
.features .offer {
  font-size: 24px;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.85);
  padding: 3rem 0;
}
.features .card {
  min-height: 270px;
  margin-top: 1rem;
  border: none;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}
.features .card img {
  width: 116px;
  height: 92px;
  margin: 0 auto;
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.features .card h4 {
  color: rgba(68, 68, 68, 0.85);
  font-weight: 700;
  font-size: 18px;
}
.features .card p {
  font-size: 12px;
  font-weight: 400;
  color: rgba(68, 68, 68, 0.85);
}
.setup {
  margin-top: 6rem;
  background: url("../../images/wslibrary/bgOrange.svg") right no-repeat;
  background-size: cover;
  min-height: 400px;
}
.setup .row {
  margin: 0 auto;
}
.setup .setup-simple {
  font-weight: bold;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
  padding: 2rem 0;
}
.setup .card {
  border: none;
  background: none;
  align-self: center;
}
.setup .card h4 {
  color: white;
  font-weight: 700;
  font-size: 30px;
  margin-top: 3rem;
  margin-bottom: 0;
}
.setup .card p {
  font-size: 12px;
  font-weight: 400;
  color: rgba(68, 68, 68, 0.85);
}
.setup .card .circle {
  width: 22px;
  height: 22px;
  border-radius: 50px;
  position: relative;
  left: 0.8rem;
}
.setup .card .circle.bg-blue {
  background: #86B1F2;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
.setup .card .circle.bg-blue h2 {
  left: -6px;
}
.setup .card .circle.bg-pink {
  background: #F47458;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
.setup .card .circle.bg-thickBlue {
  background: #466EB6;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
.setup .card .circle h2 {
  color: rgba(255, 255, 255, 0.63);
  font-size: 52px;
  font-weight: bold;
  position: absolute;
  top: -8px;
  left: -15px;
}
.price-layer {
  min-height: 600px;
  padding: 4rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .price-layer {
    padding: 0;
  }
}
.price-layer .card {
  background: #FFFFFF;
  border-radius: 10px;
  border: none;
}
.price-layer .card h1 {
  color: #444444;
  font-size: 30px;
  text-align: center;
  font-weight: 400;
  margin-top: 4rem;
}
.price-layer .card h1 span {
  color: #FF6677;
  display: block;
  font-size: 36px;
  font-weight: bold;
}
.price-layer .card.pink-bg {
  background: radial-gradient(159.88% 172.05% at -21.41% -39.3%, #FF6677 0%, #D21982 100%);
  min-height: 600px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .price-layer .card.pink-bg {
    border-radius: 0;
  }
}
.price-layer .card.pink-bg ul {
  margin: 4rem auto;
}
.price-layer .card.pink-bg ul li {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  padding: 0.5rem;
}
.price-layer .card.pink-bg ul li span {
  font-weight: 400;
  color: rgba(255, 255, 255, 0.63);
}
.price-layer .card.whiteBg {
  background: #FFFFFF;
  box-shadow: 0px 4px 10px rgba(236, 68, 124, 0.15);
  border-radius: 10px;
  min-height: 600px;
}
.price-layer .card.whiteBg img {
  height: 300px;
}
.price-layer .card .btn-green {
  background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #2FD1AE 0%, #2C8FB7 100%);
  background: -webkit-gradient(196.34% 2285.13% at -0.25% 0%, #2FD1AE 0%, #2C8FB7 100%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  margin: 2rem auto;
  color: white;
}
.elibplus {
  min-height: 600px;
  background: #00BCD6;
  border-radius: 10px;
  padding: 2rem 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus {
    background: white;
  }
}
.elibplus > h1 {
  color: white;
  font-weight: 700;
  text-align: center;
  font-size: 24px;
  padding-top: 2rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus > h1 {
    color: #00BCD6;
  }
}
.elibplus > p {
  color: white;
  font-weight: 400;
  text-align: center;
  font-size: 14px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus > p {
    color: #00BCD6;
  }
}
.elibplus .card {
  border: none;
  background: none;
  align-self: center;
  width: 20%;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .elibplus .card {
    width: 100%;
  }
}
.elibplus .card h4 {
  color: white;
  font-weight: 700;
  font-size: 30px;
  margin-top: 3rem;
  margin-bottom: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus .card h4 {
    color: #00BCD6;
  }
}
.elibplus .card p {
  margin-top: 3rem;
  font-size: 14px;
  font-weight: 700;
  color: white;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus .card p {
    color: black;
  }
}
.elibplus .card p span {
  display: block;
  font-weight: 400;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.63);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus .card p span {
    color: black;
  }
}
.elibplus .card .circle {
  width: 22px;
  height: 22px;
  border-radius: 50px;
  position: relative;
  left: 0.8rem;
}
.elibplus .card .circle.bg-blue {
  background: #86B1F2;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
.elibplus .card .circle.bg-blue h2 {
  left: -6px;
}
.elibplus .card .circle.bg-pink {
  background: #F47458;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
.elibplus .card .circle.bg-thickBlue {
  background: #466EB6;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus .card .circle.bg-thickBlue {
    float: right;
  }
}
.elibplus .card .circle.bg-green {
  background: #a9f286;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .elibplus .card .circle.bg-green {
    float: right;
  }
}
.elibplus .card .circle.bg-violet {
  background: #AF86F2;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
.elibplus .card .circle h2 {
  color: rgba(221, 221, 221, 0.6);
  font-size: 52px;
  font-weight: bold;
  position: absolute;
  top: -8px;
  left: -15px;
}
.mt-6 {
  margin-top: 5rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .mt-6 {
    margin-top: 0;
  }
}
.priceList {
  min-height: 600px;
}
.priceList .btn-demo {
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  color: #c300f3;
  font-size: 14px;
  margin: 0 auto;
}
.priceList ul {
  list-style-image: url('../../images/wslibrary/correction.svg');
}
.priceList ul li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 5px;
  padding-top: 10px;
}
.priceList .col-12 {
  margin: 0 auto;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .priceList .col-12 {
    padding: 0;
  }
}
.priceList .pricing-text {
  position: relative;
  z-index: 99;
  text-align: center;
}
.priceList .pricing-text p {
  color: #fff;
  font-size: 24px;
  font-weight: 400;
  text-align: center;
}
.priceList .pricing-text h1 {
  font-size: 64px;
  font-weight: 700;
  color: #fff;
}
.priceList .pricing-text h1 sub {
  font-size: 24px;
  font-weight: normal;
}
.priceList .price-img {
  background: url("../../images/wslibrary/price-image.svg") left no-repeat;
  background-size: contain;
  min-height: 520px;
  position: relative;
  border-radius: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .priceList .price-img {
    display: none;
  }
}
.priceList .price-image1 {
  position: absolute;
  top: 0;
  left: 0;
  background: url("../../images/wslibrary/priceimage_1.svg") left no-repeat;
  background-size: contain;
  min-height: 520px;
  border-radius: 10px;
  width: 755px;
}
.priceList .price-agenda {
  color: rgba(68, 68, 68, 0.85);
  font-weight: 400;
}
.priceList .col-8 {
  margin: 0 auto;
}
.priceList .cover-price {
  background: url("../../images/wslibrary/coverprice.svg") left no-repeat;
  background-size: cover;
  width: 100%;
  position: relative;
  margin: 10px;
}
.priceList .coverprice1 {
  width: 100%;
}
.priceList .price-wrappers {
  display: flex;
  position: absolute;
  top: 30px;
  width: 100%;
  align-items: center;
}
.priceList .price-wrappers > div {
  width: 50%;
  padding: 0 10px;
  text-align: center;
}
.priceList .price-wrappers p {
  font-size: 14px;
  color: #fff;
  font-weight: 400;
}
.priceList .price-wrappers h1 {
  color: #fff;
  font-size: 18px;
  font-weight: 700;
}
.btn-signup {
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  color: #80DF53;
  margin-left: 10px;
  font-size: 12px;
}
.banner_wrap {
  width: 90%;
  margin: 0 auto;
  padding-bottom: 3rem;
}
@media (max-width: 1200px) {
  .banner_wrap {
    width: 100%;
  }
}
.banner_wrap .banner_info h2 {
  color: #FF5700;
  text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  line-height: 1.3;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_info h2 {
    font-size: 1.5rem;
    line-height: 1.5;
    text-align: center;
  }
}
@media (max-width: 340px) {
  .banner_wrap .banner_info h2 {
    font-size: 1.35rem;
  }
}
.banner_wrap .banner_info p {
  color: #A8A8A8;
}
.banner_wrap .banner_info p br {
  display: none;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_info p br {
    display: block;
  }
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_info p {
    color: #212121;
    line-height: 22px;
    text-align: center;
    font-size: 15px;
  }
}
@media (max-width: 340px) {
  .banner_wrap .banner_info p {
    font-size: 13.4px;
  }
}
.banner_wrap .banner_info .icon-lists .icon-list {
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_info .icon-lists .icon-list {
    margin-bottom: 15px;
  }
}
.banner_wrap .banner_info .icon-lists .icon-list p {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.48);
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_info .icon-lists .icon-list p {
    color: #212121;
  }
}
.banner_wrap .banner_info .icon-lists .icon-list p br {
  display: block;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_info .bg-white {
    justify-content: center;
    background: transparent !important;
  }
}
.banner_wrap .banner_info .bg-white p {
  font-size: 14px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}
.banner_wrap .banner_register_form {
  margin-top: -80px;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_register_form {
    margin-top: 0;
  }
}
.banner_wrap .banner_register_form .form-info {
  background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
  background: -webkit-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
  backdrop-filter: blur(10px);
  border-radius: 20px;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_register_form .form-info {
    background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
    background: -webkit-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
  }
}
.banner_wrap .banner_register_form .form-info .form-horizontal {
  margin-top: 0px;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_register_form .form-info .form-horizontal {
    margin-top: 0px;
  }
}
.banner_wrap .banner_register_form .form-info .form-horizontal .form-group {
  background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #FFD000 0%, #FF5700 100%);
  background: -webkit-gradient(155.78% 433.79% at -20.98% -8.24%, #FFD000 0%, #FF5700 100%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}
.banner_wrap .banner_register_form .form-info .form-horizontal .form-group h3 {
  color: rgba(255, 255, 255, 0.48);
  font-size: 1.5rem;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_register_form .form-info .form-horizontal .form-group h3 {
    font-size: 1.25rem;
  }
}
.banner_wrap .banner_register_form .form-info .form-horizontal .form-group input {
  color: #212121;
  height: 35px;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input {
    font-size: 15px;
  }
}
.banner_wrap .banner_register_form .form-info .form-horizontal .form-group input:focus {
  box-shadow: none;
}
@media (max-width: 340px) {
  .banner_wrap .banner_register_form .form-info .form-horizontal .form-group .alert {
    font-size: 14px !important;
  }
}
.banner_wrap .banner_register_form .form-info button.submit-btn {
  background: #FF9901;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  width: 85%;
}
.banner_wrap .banner_register_form .form-info .bottom-image {
  margin-bottom: -70px;
}
@media only screen and (max-width: 767px) {
  .banner_wrap .banner_register_form .form-info .bottom-image {
    margin-bottom: 0;
  }
}
footer {
  min-height: 400px;
  background: #212121;
  margin-top: 4rem;
  position: relative;
  display: flex;
  align-items: center;
}
@media screen and (max-width: 991px) {
  footer {
    min-height: 400px;
    z-index: inherit;
    padding-bottom: 4rem;
  }
}
footer img {
  width: auto;
  height: auto;
}
.social-icons {
  display: flex;
  justify-content: center;
  padding: 0;
  margin: 0;
}
.social-icons li {
  list-style-type: none;
  padding: 10px;
  padding-top: 0;
}
.social-icons li a {
  text-decoration: none;
}
.social-icons li a i {
  color: #fff;
  font-size: 10px;
}
[class^="flaticon-"]:before,
[class*=" flaticon-"]:before,
[class^="flaticon-"]:after,
[class*=" flaticon-"]:after {
  margin-left: 0;
}
.support-link p {
  font-weight: 300;
}
.support-link a {
  text-decoration: underline;
  color: #ffffff;
  font-weight: 500;
}
.support-link a:hover {
  color: #F79420;
}
#download-app-btn-container {
  background-color: #ffffff;
  position: fixed;
  bottom: 80px;
  right: 0px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  z-index: 99;
}
.new-download-app-btn {
  padding: 3px 5px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  border: 2px solid #AE3591;
  border-right-color: transparent;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  display: none;
  font-size: 12px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .new-download-app-btn {
    display: block;
  }
}
.contactus {
  color: #ffffff;
  font-size: 12px;
  text-align: center;
}
.wsborder {
  border-right: 0.3px solid rgba(255, 255, 255, 0.5);
  padding-right: 20px;
  height: 75px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .wsborder {
    border-right: none;
    padding-right: 0;
    text-align: center;
  }
}
.mobile-no {
  opacity: 0.8;
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  text-align: center;
  padding: 0;
  margin: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .system {
    height: 150px;
  }
}
.footer-menus {
  min-height: auto;
  margin: 0;
}
.footer-menus .logo-wrapper,
.footer-menus .container,
.footer-menus #download-app-btn-container {
  display: none !important;
}
.available {
  color: rgba(68, 68, 68, 0.48) !important;
  font-size: 14px;
}
@media (min-width: 576px), (max-width: 575px) and (orientation: landscape) {
  .mdl-layout__header {
    background-image: none;
  }
}
