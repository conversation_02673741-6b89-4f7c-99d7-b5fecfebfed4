
#registerModal #inputIcon i {
    padding-left: 20px;
    padding-top: 12px;
    font-size: 18px;
}

.group { 
  position:relative; 
  margin-left: 20px;
  padding-right: 25px;
}

.iconColor {
    color: #009688;
}

.inputMaterial {
  font-size:18px;
  padding:10px 10px 10px 0;
  display:block;
  width: 97%;
  border:none;
  border-bottom: 1px solid #9e9e9e;
}

.inputMaterial:focus { outline:none;}

/* LABEL ======================================= */

font {
  color: #00bfa5;
  font-size:16px;
  font-weight: normal;
  position:absolute;
  pointer-events:none;
  top:10px;
  transition:0.2s ease all; 
  -moz-transition:0.2s ease all; 
  -webkit-transition:0.2s ease all;
}


/* active state */
.inputMaterial:focus ~ font, .inputMaterial:valid ~ font {
  top:-20px;
  font-size:14px;
  color: #009688;
}

/* BOTTOM BARS ================================= */
.bar 	{ position:relative; display:block; width:97%; }
.bar:before, .bar:after 	{
  content:'';
  height:2px; 
  width:0;
  bottom:1px; 
  position:absolute;
  background: #00bfa5; 
  transition:0.2s ease all; 
  -moz-transition:0.2s ease all; 
  -webkit-transition:0.2s ease all;
}
.bar:before {
  left:50%;
}
.bar:after {
  right:50%; 
}

/* active state */
.inputMaterial:focus ~ .bar:before, .inputMaterial:focus ~ .bar:after {
  width:50%;
}


/* active state */
.inputMaterial:focus ~ .highlight {
  -webkit-animation:inputHighlighter 0.3s ease;
  -moz-animation:inputHighlighter 0.3s ease;
  animation:inputHighlighter 0.3s ease;
}

/* ANIMATIONS ================ */
@-webkit-keyframes inputHighlighter {
	from { background:#5264AE; }
  to 	{ width:0; background:transparent; }
}
@-moz-keyframes inputHighlighter {
	from { background:#5264AE; }
  to 	{ width:0; background:transparent; }
}
@keyframes inputHighlighter {
	from { background:#5264AE; }
  to 	{ width:0; background:transparent; }
}