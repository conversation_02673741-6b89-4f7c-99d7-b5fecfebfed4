body {
  background: #fff !important;
}
main {
  min-height: 75vh;
  margin-top: 4rem;
}
.ws_container {
  width: calc(100% - 30%);
  margin: 0 auto;
}
.mockTest {
  margin-top: 2rem;
}
.mockTestList {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.mockTest__title {
  font-size: 1.7rem;
}
.mockTest__examCards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 1rem;
  align-items: center;
  margin-top: 1rem;
  background: #e9e8ef;
  padding: 10px 16px;
  border-radius: 10px;
  min-height: 150px;
}
.examCard {
  background: #fff;
  box-shadow: 0 3px 4px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 10px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 2px solid;
}
.examCard.examCardListExam,
.examCard.currentMonthItem,
.examCard.previousMonthsItem {
  padding: 20px;
}
.examCard__img img {
  width: 35px;
  height: 35px;
  object-fit: contain;
}
.examCard__examName {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  flex-direction: column;
  gap: 16px;
}
.examCard__examName p {
  margin-bottom: 0;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
  max-width: 180px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 1;
}
.examCard__arrow {
  transition: all 0.3s ease;
}
.examCard.examCardListExam .examCard__examName p {
  text-align: start;
}
.examCard:hover {
  box-shadow: none;
}
.examCard:hover .examCard__arrow {
  transform: translateX(4px);
}
.mockTestList__items {
  gap: 4rem;
  padding-left: 18px;
}
.mockTestListItem {
  margin-bottom: 1.4rem;
}
.mockTestList__items .mockTestListItem::marker {
  font-size: 1.2rem;
}
.mockTestListItem__title {
  font-size: 1.2rem;
  margin-bottom: 0;
  font-weight: 500;
}
.mockTestListItem a {
  text-decoration: underline !important;
  color: #000;
}
.booksList__items {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 10px 24px;
  overflow: scroll;
}
.booksList__items-item {
  display: flex;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  margin-bottom: 16px;
  gap: 10px;
  width: 320px;
}
.mockTestListItem__description {
  margin-top: 8px;
}
.booksListItem__img {
  position: relative;
}
.booksListItem__img img {
  object-fit: fill;
  cursor: pointer;
  border-radius: 5px;
}
.booksListItem__img-price {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin-top: 8px;
}
.booksListItem__details {
  width: 100%;
  padding: 0 10px 0 0px;
}
.booksListItem__details a {
  color: #000;
}
.booksListItem__details a p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  max-width: 185px;
}
.booksListItem__details a span {
  font-weight: 300;
  font-size: 13px;
  color: #aaa;
}
.booksListItem__details a:hover {
  text-decoration: none !important;
}
.booksListItem__img-price p {
  margin-bottom: 0;
}
.mockTestListItem a p {
  margin-bottom: 0;
}
.booksList h3 {
  font-size: 18px;
}
.showMoreText {
  text-decoration: underline !important;
  display: flex;
  justify-content: end;
}
.digitalLib-btn a {
  color: #000 !important;
}
.leaderboard-info {
  padding: 0 !important;
}
.leaderboard-sidebar {
  display: none;
}
.suggestedBooksTitle {
  margin-bottom: 1rem;
}
.allMockTests {
  margin-top: 2rem;
}
.mockTests__currentMonth-title {
  font-size: 18px;
  margin-bottom: 18px;
}
.examCard__examName h3 {
  font-size: 16px;
  font-weight: lighter;
}
.mockTests__currentMonth {
  margin-top: 2rem;
}
.mockTestPreviousMonthsListItems {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.mockTestPreviousMonthsListItems h5 {
  margin-bottom: 0 !important;
  font-weight: lighter;
  font-size: 15px;
}
.examPage {
  margin-top: 4rem;
}
.examActionItem {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e9e8ef;
  gap: 1.2rem;
  margin-top: 1.5rem;
  padding: 2rem 1rem;
}
.examActionItem .currentMonthItem {
  padding: 16px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  background: #fff;
}
.daily__test-dates__card {
  text-align: center;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 10px;
}
.card-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1rem;
  margin-top: auto;
  padding-top: 15px;
  border-top: 1px solid rgba(198, 198, 198, 0.61);
}
.card-title p {
  text-align: center;
}
.card-options button {
  border: none;
  border-radius: 100px;
  padding: 8px 12px;
  transition: all 0.3s ease-in;
  display: flex;
  gap: 10px;
  width: 150px;
  text-align: center;
  align-items: center;
}
.card-options button:hover {
  background: #25cd71;
  color: #fff;
}
.leaderboard {
  margin-top: 2rem;
}
.card-quizInfo {
  display: flex;
  gap: 1rem;
  margin-bottom: 16px;
  justify-content: center;
}
.leaderboard-info .card {
  max-width: 100%;
}
.currentMonthItem {
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  cursor: pointer;
  margin-bottom: 0px;
}
.currentMonthItemWrapper {
  display: flex;
  height: 100%;
  justify-content: space-between;
}
.currentMonthItemWrapper h3 {
  font-size: 16px;
  font-weight: 400;
}
.currentMonthItemWrapper i {
  font-size: 14px;
}
.freeTag {
  display: flex;
  align-items: center;
  gap: 5px;
}
.freeTag p {
  background: #25cd71;
  border-radius: 2px;
  color: #fff;
  width: 50px;
  text-align: center;
  line-height: initial;
  padding: 1px;
}
#mockTestCurrentMonthItems {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 1.2rem;
}
.openTestBtn {
  border: 1px solid #25cd71;
  background: transparent;
  padding: 4px;
  width: 100px;
  height: 35px;
  border-radius: 3px;
}
.test__nameWrapper {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  gap: 10px;
}
.quizInfo {
  display: flex;
  gap: 5px;
}
.quizInfo p {
  color: rgba(0, 0, 0, 0.3);
}
.accordion-container {
  margin-top: 2rem;
  width: 65%;
}
.faqTitle {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 1rem;
}
.accordion-item {
  background-color: #FFFFFF;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.accordion-header {
  padding: 15px;
  font-size: 14px;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  outline: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
  transition: background-color 0.3s ease;
}
.accordion-header:hover {
  background-color: #e9e8ef;
}
.accordion-content {
  background-color: #FAFAFA;
  overflow: hidden;
  padding: 0 15px;
  max-height: 0;
  transition: max-height 0.3s ease;
}
.accordion-content p {
  margin: 15px 0;
  line-height: 1.5;
}
.icon {
  transition: transform 0.3s ease;
}
.active .icon {
  transform: rotate(45deg);
}
.mockBanner {
  height: 220px;
  width: 100%;
  background: hsl(282, 36%, 25%);
  background: linear-gradient(90deg, hsl(282, 36%, 25%) 0%, hsl(310, 22%, 37%) 100%);
  background: -moz-linear-gradient(90deg, hsl(282, 36%, 25%) 0%, hsl(310, 22%, 37%) 100%);
  background: -webkit-linear-gradient(90deg, hsl(282, 36%, 25%) 0%, hsl(310, 22%, 37%) 100%);
  height: 200px;
  border-radius: 10px;
}
.mockBannerWrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex-direction: column;
  gap: 12px;
  padding: 18px;
}
.testBannerTitle {
  font-size: 28px;
  color: #fff;
  text-align: center;
}
.testBannerSub {
  color: #fff;
  margin-top: 6px;
  font-size: 18px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  main,
  .examPage {
    margin-top: 3rem;
  }
  .ws_container {
    width: calc(100% - 4%);
    margin: 0 auto;
  }
  .mockBannerWrap {
    gap: 0;
  }
  .testBannerTitle {
    font-size: 1.5rem;
    line-height: 40px;
    text-align: center;
  }
  .testBannerSub {
    line-height: 30px;
    text-align: center;
    font-size: 16px;
  }
  .mockTest__examCards {
    grid-template-columns: repeat(1, 1fr);
  }
  .examCard {
    gap: 10px;
  }
  .examCard__examName p {
    font-size: 16px;
    text-align: center;
  }
  .mockTest__title {
    font-size: 20px;
  }
  .examCard__img img {
    width: 30px;
    height: 30px;
  }
  .mockTestList__items {
    gap: 10px 4rem;
  }
  .mockTestList__items .mockTestListItem::marker {
    font-size: 1.2rem;
  }
  .mockTestListItem__title {
    font-size: 18px;
    margin-bottom: 0;
  }
  .footer-menus ul {
    line-height: 35px;
  }
  .navbar-header .navbar-nav .nav-item .nav-link.login-menu-link {
    background: #000 !important;
  }
  .card-options {
    grid-template-columns: repeat(2, 1fr);
  }
  .booksList__items {
    grid-gap: 1rem;
    overflow: scroll;
  }
  .booksList__items-item {
    width: 300px;
  }
  .examActionItem {
    grid-template-columns: repeat(1, 1fr);
  }
  #mockTestCurrentMonthItems {
    grid-template-columns: repeat(1, 1fr) !important;
  }
  .currentMonthItemWrapper {
    gap: 20px !important;
  }
  .test__nameWrapper {
    width: 70% !important;
  }
  .card-options button {
    width: auto;
  }
  .accordion-container {
    margin-top: 2rem;
    width: 100%;
  }
}
