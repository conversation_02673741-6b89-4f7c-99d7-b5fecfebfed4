/* Update panel and container to support bottom input */
.right-panel .panel-content {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    padding: 0 !important;
    overflow: hidden !important;
}

.chat-container {
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    position: relative !important;
}

/* Messages area should be scrollable */
.chat-messages-area {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* Chapter Question Input Component */
.chapter-question-input-container {
    margin-top: auto; /* Push to bottom */
    padding: 10px;
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.05);
    flex-shrink: 0; /* Prevent shrinking */
    position: sticky;
    bottom: 0;
    z-index: 10;
}

.chapter-question-input {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

.chapter-question-input {
    width: 100%;
}
input[type='text']{
    border: none !important;
}

.chapter-question-input__form {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 10px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
}

.chapter-question-input__form:focus-within {
    border-color: #dee2e6;
}

.chapter-question-input__container {
    display: flex;
    gap: 12px;
}

.chapter-question-input__actions-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chapter-question-input__action-btn {
    background: #f1f3f5;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.chapter-question-input__action-btn:hover {
    background-color: #e9ecef;
    color: #495057;
}

.chapter-question-input__field-wrapper {
    flex: 1;
    display: flex;
}

.chapter-question-input__field {
    flex: 1;
    background: none;
    border: none;
    color: #495057;
    font-size: 16px;
    line-height: 1.5;
    outline: none;
    padding: 8px 5px;
    font-family: inherit;
}
.chapter-question-input__options{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.chapter-question-input__field::placeholder {
    color: #6c757d;
    font-weight: 400;
}

.chapter-question-input__send {
    background-color: #f1f3f5;
    border: none;
    color: #7b68ee;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.chapter-question-input__send:hover {
    background-color: #e9ecef;
    color: #6a5acd;
    transform: translateY(-1px);
}

.chapter-question-input__send:disabled {
    background-color: #f8f9fa;
    color: #adb5bd;
    cursor: not-allowed;
    transform: none;
}

.chapter-question-input__disclaimer {
    text-align: center;
    font-size: 12px;
    color: #9ca3af;
    margin-top: 12px;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chapter-question-input-container {
        padding: 10px;
        position: sticky;
        bottom: 0;
    }

    .chapter-question-input__form {
        padding: 10px 10px;
        border-radius: 10px;
    }

    .chapter-question-input__container {
        gap: 10px;
    }

    .chapter-question-input__actions-left {
        gap: 6px;
    }

    .chapter-question-input__action-btn {
        min-width: 28px;
        height: 28px;
        padding: 6px;
    }

    .chapter-question-input__field {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 6px 10px;
    }

    .chapter-question-input__send {
        min-width: 32px;
        height: 32px;
        padding: 6px;
    }

    .chapter-question-input__disclaimer {
        font-size: 11px;
        margin-top: 10px;
    }

    .chat-messages-area {
        padding: 16px;
    }
}

/* Focus states for accessibility */
.chapter-question-input__action-btn:focus,
.chapter-question-input__send:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.chapter-question-input__field:focus {
    outline: none;
}


