.group-title {
  width: 100%;
}
#memsssection {
  position: sticky;
  background: #fff;
  z-index: 10;
  padding: 10px 0;
}
.custom-fix #memsssection {
  top: 63px;
  box-shadow: 0px 2px 2px #eee;
  border-radius: 0 0 10px 10px;
}
.back-btn {
  color: #C633A2;
}
a {
  text-decoration: none !important;
}
.createGroup-btn {
  border-radius: 10px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.25);
  padding: 10px;
}
.createGroup-btn a {
  color: rgba(68, 68, 68, 0.85);
}
.header-contents h2 {
  font-size: 20px;
  color: #C633A2;
  font-weight: bold;
}
.settings-icon {
  border: none;
  background: #ae378e;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
}
.header-title h4 {
  color: #af378f;
  font-weight: bold;
}
.header-title img {
  width: 30px;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
.optionList li a {
  color: rgba(68, 68, 68, 0.85);
  font-size: 12px;
}
.div {
  width: 1px;
  height: 15px;
  background: #999;
  opacity: 0.3;
}
.memsec-header a {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.48);
  text-decoration: none;
}
.active {
  color: #af378f !important;
  font-weight: bold;
}
.userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #ff6565;
}
.btn-outline-primary {
  border-color: #af378f !important;
  border-radius: 5px;
  font-size: 12px;
  color: #af378f;
}
.reqList ul li {
  margin-bottom: 30px;
}
.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #ff6565;
}
.back-btn {
  color: #af378f;
}
.back-btn:active {
  text-decoration: none;
}
.create-group p {
  font-family: Poppins;
  font-style: normal;
  font-weight: normal;
  font-size: 11px;
  line-height: 16px;
  text-align: center;
  color: rgba(68, 68, 68, 0.48);
}
.create-group .create-group-button {
  background: #ffd602;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  width: calc(100% - 40%);
  padding: 6px;
  margin-left: 20% !important;
}
.create-group .create-group-button a {
  color: rgba(68, 68, 68, 0.85);
  font-size: 1.1rem;
  font-weight: 700;
}
.posts-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 2rem;
}
.post-card {
  position: relative;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(142, 142, 142, 0.15);
}
.post-card-header {
  background: #ae3691;
  overflow: visible;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  text-align: center;
  color: #fff;
}
.post-card-body,
.post-card-footer {
  padding: 1rem;
}
.post-pending {
  position: absolute;
  width: 25px;
  height: 25px;
  background: #ff0000;
  border: 1px solid #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: -12px;
  left: 20px;
  color: #fff;
}
.divider {
  border-bottom: 1px solid rgba(142, 142, 142, 0.15);
  margin: 12px 12px 0;
}
.width-size {
  width: 60%;
}
.search input {
  border: none;
  background: rgba(232, 232, 232, 0.33);
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
}
@media only screen and (max-width: 767px) {
  .posts-cards {
    grid-template-columns: repeat(1, 1fr);
  }
  .width-size {
    width: 100%;
  }
  .header-contents {
    justify-content: space-between;
  }
}
