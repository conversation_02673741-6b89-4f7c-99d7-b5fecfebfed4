.uimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #ff6565;
}
.notification-msge p {
  color: rgba(68, 68, 68, 0.85);
}
.msge {
  color: #8d20cd;
}
.header-content h3 {
  color: #ae3691;
}
.notification-ul li {
  border-bottom: 1px solid #cfcfcf;
}
.header-wrapper a {
  color: #C633A2;
}
.width-size {
  width: 40%;
}
@media only screen and (max-width: 767px) {
  .width-size {
    width: 100%;
  }
}
