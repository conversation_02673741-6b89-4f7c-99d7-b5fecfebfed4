.reqList {
  min-height: 700px;
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 999;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #ddd;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message.show {
  transform: translateY(0px);
}
.alert-message.hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #7aee7a;
}
#errorMsg {
  background: #ff8e8e;
}
.back-btn {
  color: #af378f;
  text-decoration: none;
}
.header-title h4 {
  color: #af378f;
  font-weight: bold;
}
.header-title img {
  width: 25px;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
.optionList li a {
  color: rgba(68, 68, 68, 0.85);
  font-size: 12px;
}
.btn-outline-primary {
  border-color: #af378f !important;
  border-radius: 5px;
  font-size: 12px;
  color: #af378f;
}
.btn-primary {
  background: #af378f !important;
  border: none;
  font-size: 12px;
}
.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #ff6565;
}
.bt button {
  width: 120px;
}
.reqList ul li {
  margin-bottom: 30px;
}
.back-btn {
  color: #af378f;
}
.back-btn:active {
  text-decoration: none;
}
.width-size {
  width: 50%;
}
@media only screen and (max-width: 767px) {
  .width-size {
    width: 100%;
  }
  .reqList {
    position: relative;
    top: 50px;
  }
  .admin-panel-header {
    position: relative;
    top: 0px;
  }
}
