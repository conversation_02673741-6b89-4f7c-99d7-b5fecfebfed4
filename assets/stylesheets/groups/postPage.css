#errorPostEditor,
#errorUploadFile {
  margin-top: -15px;
}
.error-text {
  font-size: 12px;
  font-weight: 500;
}
.inside-comments {
  max-height: 300px;
  overflow-y: auto;
}
#post-image-preview,
#editPostImage {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}
#image-preview .close-img,
#editPostImagePreview .close-img {
  position: absolute;
  top: -10px;
  right: -15px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  border: none;
}
#image-preview #imgFileName,
#editPostImagePreview #editPostImgFileName {
  font-size: 12px;
  color: #444;
  padding-top: 5px;
}
#fileNamePreview p,
#editPostFileName p {
  font-size: 12px;
  color: #444;
}
.editor {
  width: 100%;
  height: 150px;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px !important;
  border: none !important;
  padding-bottom: 45px !important;
}
.blury {
  -webkit-filter: blur(7px);
  -moz-filter: blur(7px);
  -o-filter: blur(7px);
  -ms-filter: blur(7px);
  filter: blur(7px);
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 9992;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #ddd;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message.show {
  transform: translateY(0px);
}
.alert-message.hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #7aee7a;
}
#errorMsg {
  background: #ff8e8e;
}
#groupReportModal,
#joinRequestModal,
#groupPostReportModal,
#groupPostDeleteModal,
#groupUserReportModal,
#groupUserExitModal {
  z-index: 9992;
}
#groupPostEditModal {
  background: rgba(0, 0, 0, 0.7) !important;
}
#reportReason,
#joinReason {
  min-height: 150px;
  max-height: 150px;
  resize: none;
}
#reportReason:focus,
#joinReason {
  border: 1px solid #ced4da !important;
}
.admin-panel-header .back-btn {
  color: #C633A2;
}
.admin-panel-header .settings-icon {
  background: #ae378e;
}
#adminoptions {
  position: fixed;
  z-index: 100;
  background: #fff;
  width: 100%;
  bottom: 0;
  padding-top: 10px;
  top: 155px;
  width: 25%;
  height: 200px;
  right: 90px;
  border-radius: 5px;
}
#adminoptions::before {
  content: "";
  position: absolute;
  width: 25px;
  height: 25px;
  transform: rotate(45deg);
  background: #fff;
  top: -10px;
  left: 47%;
}
.header-title h4 {
  color: #af378f;
  font-weight: bold;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
.optionList li a {
  color: rgba(68, 68, 68, 0.85);
  font-size: 12px;
}
.optionList li a:hover {
  text-decoration: none !important;
}
.groupBanner {
  height: 150px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}
.groupBanner::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}
.groupBannerContent {
  z-index: 2;
  position: relative;
  padding-top: 10px;
  height: 40px;
}
.header {
  /*margin-right: 60px;*/
}
.header a {
  text-decoration: none;
  color: #fff;
}
.rpt-btn {
  background: rgba(255, 255, 255, 0.36);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 38px;
  padding: 5px 15px;
  font-size: 11px;
  height: 32px;
}
.grpBannerName {
  position: absolute;
  bottom: 40px;
  z-index: 2;
}
.grpBannerName p {
  font-size: 13px;
  color: #eee;
}
.invite-btn {
  background: #ffffff;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: rgba(68, 68, 68, 0.85);
  width: 40%;
  margin-right: 10px;
  display: flex;
}
.invite-btn:hover {
  text-decoration: none;
}
.join-btn {
  background: #ffffff;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: rgba(68, 68, 68, 0.85);
  width: 40%;
  margin-left: 10px;
  display: flex;
}
.join-btn:hover {
  text-decoration: none;
}
.request-btn {
  background: #ffffff;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: rgba(68, 68, 68, 0.85);
  width: 40%;
  margin-left: 10px;
  display: flex;
}
.request-btn:hover {
  text-decoration: none;
}
.search {
  background: rgba(232, 232, 232, 0.33);
  border-radius: 7px;
  border: none;
  padding: 10px 15px;
  width: 100%;
}
.search:focus {
  outline: none;
}
.user-img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid #8888;
}
.user-img img {
  width: 100%;
}
.userNm .usrnm {
  font-size: 14px;
}
.userNm .posttm {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.48);
}
.more-btn {
  background: transparent;
}
.more-btn:active {
  outline: none;
  border: none;
}
.more-btn:focus {
  outline: none;
  border: none;
}
.more-option {
  position: absolute;
  width: 150px;
  height: 41px;
  left: 60%;
  top: 10%;
  background: #ffffff;
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  color: #ed2f2f;
  font-size: 12px;
}
.card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.like-count {
  font-size: 10px;
  font-weight: bold;
}
.like-btn {
  background: transparent;
  border: none;
  font-size: 10px;
  color: rgba(68, 68, 68, 0.85);
}
.comment-btn {
  background: transparent;
  border: none;
  font-size: 10px;
}
.comment-btn:focus {
  outline: none;
  border: none;
}
.comment-section {
  background: #f1f1f1;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  padding: 5px;
  /* margin-top: 30px; */
}
.crd-grp {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1rem;
}
.settings-btn {
  background: rgba(255, 255, 255, 0.35);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 6px;
  display: flex;
}
.comment-input {
  border: none !important;
  background: #ffffff;
  border-radius: 10px 0 0 10px !important;
  width: 100%;
  height: 30px;
  padding: 10px;
  font-size: 12px;
}
.comment-input::placeholder {
  font-style: italic;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.48);
}
.comment-input:focus {
  border: none;
  outline: none;
}
.total-replies {
  font-size: 10px;
  font-style: italic;
}
.reply-btn {
  color: #c633a2;
  background: transparent;
  border: none;
}
.reply-btn:focus {
  outline: none;
}
.reply {
  border-left: 2px solid rgba(0, 0, 0, 0.1);
  padding: 5px;
}
.reply .reply-text {
  font-size: 14px;
}
.reply-section {
  transition: all 0.3s ease;
}
.img {
  width: auto;
  border-radius: 10px;
  overflow: hidden;
}
.img img {
  width: 100%;
  border-radius: 10px;
}
.dpup label {
  cursor: pointer;
  margin-top: -36px;
  position: absolute;
  left: 78%;
  font-size: 14px;
  margin-bottom: 0 !important;
}
.dpup label img {
  width: 15px;
}
#upload-photo {
  opacity: 0;
  z-index: -1;
}
.postbutton {
  position: absolute;
  margin-top: -72px;
  left: 79.5%;
  border: none;
  background: #9a309b;
  border-radius: 10px 0px;
  width: 56px;
  height: 45px;
  color: #fff;
}
.post {
  border-radius: 10px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border: none;
}
.post:focus {
  border: none !important;
  outline: none !important;
}
.post::placeholder {
  font-size: 14px;
}
.form-control:focus {
  border: none !important;
  outline: none !important;
  border-color: #fff;
}
.attach-btn {
  position: absolute;
  margin-top: -11%;
  left: 70%;
  border: none;
  border-radius: 10px 0px;
  width: 56px;
  height: 45px;
  color: #fff;
  background: transparent;
}
.up-btn {
  background: transparent;
  border: none;
  font-size: 14px;
}
.at-btn {
  width: 200px;
}
.reportbtn {
  /*margin-left: 175px;*/
}
.user-info-img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.dropleft .dropdown-toggle::before {
  display: none  !important;
}
.dropleft button {
  background: transparent;
}
.dropdown-menu {
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  border: none;
}
.dropdown-menu a {
  font-size: 12px;
  color: #ED2F2F;
}
.post-img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 10px;
}
.postOptions button {
  background: transparent;
  border: none;
  cursor: pointer;
}
.posts-card {
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  position: relative;
  z-index: 2;
}
.comment-bttn button {
  background: transparent;
  border: none;
  cursor: pointer;
}
.divide {
  width: 1px;
  height: 20px;
  background: rgba(68, 68, 68, 0.48);
  opacity: 0.7;
  margin-right: 5px;
  margin-left: 5px;
}
button:focus {
  border: none;
  outline: none;
}
.comments-sec {
  background: #F1F1F1;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 10px 10px;
  z-index: 1;
  position: relative;
  margin: 0 15px;
}
.reply-sec p {
  font-size: 12px;
  font-style: italic;
  color: #919191;
  margin-right: 10px;
}
.reply-sec button {
  background: transparent;
  border: none;
  color: #A73397;
  font-size: 13px;
  padding: 0;
  font-weight: 600;
  cursor: pointer;
}
.dropdown-menu ul li {
  list-style: none;
}
.dropdown-menu ul li a:hover {
  background: transparent;
}
.textarea-container {
  position: relative;
}
.textarea-container textarea {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border: none;
  padding-bottom: 45px !important;
}
.textarea-container textarea:focus {
  outline: 0;
}
.textarea-container textarea::placeholder {
  font-size: 12px;
  font-style: italic;
  color: rgba(68, 68, 68, 0.48);
}
.textarea-container button {
  position: absolute;
  right: 15px;
  background: #9A309B;
  border-radius: 10px 0px;
  border: none;
  color: #fff;
  width: 56px;
  height: 45px;
  bottom: 5px;
}
.textarea-container label {
  position: absolute;
  right: 70px;
  bottom: 5px;
  width: 40px;
  height: 40px;
  margin: 0;
  padding: 5px;
  text-align: center;
  background: #fff;
  border-radius: 10px 0 0;
}
.textarea-container label img {
  width: 15px;
  cursor: pointer;
}
.textarea-container input {
  opacity: 0;
  visibility: hidden;
  display: none;
}
.width-size {
  width: 80%;
}
.postmodalfooter input {
  opacity: 0;
  visibility: hidden;
}
.time {
  color: rgba(68, 68, 68, 0.48);
  font-size: 12px;
  font-style: italic;
}
.modal .textarea-container button {
  width: 100px;
  right: 0;
}
.modal .textarea-container label {
  right: 100px;
}
.editPostImg {
  width: 100%;
}
.joined-btn {
  background: #9A309B;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: rgba(68, 68, 68, 0.85);
  width: 40%;
  margin-left: 10px;
}
#admin {
  background: #9A309B;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: rgba(68, 68, 68, 0.85);
  width: 40%;
  margin-left: 10px;
  display: flex;
}
#admin:hover {
  text-decoration: none;
}
.joined-dropdownBtn {
  background: transparent;
  color: #ffff;
  font-style: normal;
  font-weight: 400;
}
.drpDwnMenu {
  height: 100px;
  width: 100%;
}
.drpDwnMenu a {
  padding: 1rem;
}
.drpDwnMenu a:nth-child(1) {
  border-bottom: 1px solid #4444;
}
.dropdown-menu a img {
  width: 10px;
}
.width-size {
  width: 40% !important;
}
.searchGif {
  display: flex;
  align-items: center;
  justify-content: center;
}
.groupPrivacyType img {
  width: 30px;
  height: 30px;
}
.searchGif img {
  width: 80px;
}
.notification {
  position: relative;
}
.notificationCount {
  position: absolute;
  left: 70%;
  top: 0;
  background: red;
  width: 15px;
  height: 15px;
  text-align: center;
  border-radius: 50%;
  color: #fff;
  font-size: 10px;
}
.postList {
  min-height: 500px;
}
.groupBanner {
  height: 200px;
}
.rotate {
  transform: rotate(100deg);
  transition: all 0.3s ease;
}
.fa-lock-open {
  color: #00c000;
}
.statusText {
  margin-top: 5px;
}
.fa-lock {
  color: red;
}
.noComt {
  width: 5%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
@media only screen and (max-width: 990px) {
  .width-size {
    width: 100% !important;
  }
  .textarea-container {
    top: 0;
  }
}
@media only screen and (max-width: 990px) {
  .width-size {
    width: 100%;
  }
  .img-preview {
    top: 50px !important;
  }
  .edPostPrev {
    margin-top: 70px !important;
  }
}
@media only screen and (max-width: 768px) {
  .dpup label {
    left: 75%;
  }
  .grp-btns {
    width: 100% !important;
  }
  #adminoptions {
    width: 100%;
    height: 100%;
    right: 0;
  }
  #adminoptions::before {
    left: 91%;
  }
}
@media only screen and (max-width: 452px) {
  .dpup label {
    left: 75%;
  }
  admin-panel-header {
    top: 0;
  }
  .textarea-container {
    top: 60px;
  }
  .groupBanner {
    top: 0;
  }
  .grp-btns {
    position: relative;
    top: 0;
  }
  #adminoptions {
    top: 110px;
  }
  #adminoptions::before {
    left: 90%;
  }
  #postList {
    position: relative;
    top: 60px;
    margin-bottom: 150px;
  }
}
@media only screen and (max-width: 375px) {
  #adminoptions::before {
    left: 89%;
  }
}
