#errorPostEditor,
#errorUploadFile {
  margin-top: -15px;
}
.error-text {
  font-size: 12px;
  font-weight: 500;
}
#post-image-preview {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}
#image-preview .close-img {
  position: absolute;
  top: -10px;
  right: -15px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  border: none;
}
#image-preview #imgFileName {
  font-size: 12px;
  color: #444;
  padding-top: 5px;
}
.editor {
  width: 100%;
  height: 150px;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px !important;
  border: none !important;
  padding-bottom: 40px !important;
}
.editor.ck-focused {
  border: none !important;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1) !important;
  border-radius: 10px !important;
}
.editor .ck-placeholder::before {
  color: rgba(68, 68, 68, 0.3);
  font-style: italic;
}
.show-profile-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
}
.show-profile-image p {
  position: relative;
  color: #C9C9C9;
}
.show-profile-image img {
  position: relative;
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 999;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #ddd;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message.show {
  transform: translateY(0px);
}
.alert-message.hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #7aee7a;
}
#errorMsg {
  background: #ff8e8e;
}
.createGroupSection {
  min-height: 500px;
  position: relative;
  top: 20px;
}
input[type="radio"]:checked + label {
  background-color: transparent;
  /*color: #fff;*/
  border: none;
}
.visibility input[type="radio"]:checked + label {
  background-color: transparent;
  color: #888;
  border: none;
}
.back-btn {
  color: #C633A2;
}
a {
  text-decoration: none !important;
}
.createGroup-btn {
  border-radius: 10px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.25);
  padding: 10px;
}
.createGroup-btn a {
  color: rgba(68, 68, 68, 0.85);
}
.header-contents h2 {
  font-size: 20px;
  color: #C633A2;
  font-weight: bold;
}
.form__group {
  position: relative;
  padding: 15px 0 0;
  margin-top: 10px;
  width: 100%;
}
.form__field {
  font-family: inherit;
  width: 100%;
  border: 0;
  border-bottom: 2px solid #8029a8;
  outline: 0;
  font-size: 14px;
  color: #8029a8;
  padding: 3px 0;
  background: transparent;
  transition: border-color 0.2s;
}
.form__field::placeholder {
  color: transparent;
}
.form__field:placeholder-shown ~ .form__label {
  font-size: 14px;
  cursor: text;
  top: 10px;
  color: #8029a8;
}
.form__label {
  position: absolute;
  top: 5px;
  display: block;
  transition: 0.2s;
  font-size: 14px;
  color: gray;
}
.form__field:focus {
  padding-bottom: 3px;
  font-weight: 700;
  border-width: 3px;
  border-image: linear-gradient(to right, #8029a8, #7F28A8);
  border-image-slice: 1;
}
.form__field:focus ~ .form__label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #8029a8;
}
/* reset input */
.form__field:required,
.form__field:invalid {
  box-shadow: none;
}
.form-check-label {
  background: transparent;
}
.crt-grp input {
  border: none !important;
  border-bottom: 2px solid #8029a8 !important;
  border-radius: 0px;
  margin-top: -10px;
}
.crt-grp input:focus {
  outline: none;
  border: none;
}
.bgHighlight {
  background: #7f28a8;
  color: #fff;
}
.color {
  color: #333;
}
.chk-privacy {
  background: #ffffff;
  box-shadow: 0px 4px 10px rgba(142, 142, 142, 0.15);
  border-radius: 10px;
}
.privacy-option {
  padding: 10px;
  box-shadow: 0px 4px 10px rgba(142, 142, 142, 0.15);
  border-radius: 10px;
}
.form-control:focus {
  outline: none;
  border: none;
}
.pr-text {
  font-size: 14px;
  color: #c633a2;
}
.pr-lab {
  color: rgba(68, 68, 68, 0.48);
  font-size: 10px;
  display: block;
}
/*******************  Description     *******************/
.dp-cards {
  display: flex;
  flex-direction: row;
  gap: 10px;
}
.crd {
  /*width: 70px;*/
  height: 63px;
  border-radius: 5px;
  cursor: pointer;
}
.dp-card1 {
  background: #ff6565;
}
.dp-card2 {
  background: #48e200;
}
.dp-card3 {
  background: #e756ff;
}
.dp-card4 {
  background: #ffb82f;
}
.bttns {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.btn-skip {
  width: 50%;
}
.btn-next-1 {
  width: 50%;
}
.btn-next {
  margin-top: 0 !important;
}
.dpup label {
  cursor: pointer;
  width: 100%;
  height: 138px;
  background: #E9E9E9;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  flex-direction: column;
  color: rgba(68, 68, 68, 0.85);
  text-align: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}
.dpup label p {
  font-size: 12px;
}
#upload-photo {
  opacity: 0;
  z-index: -1;
}
.dpup-1 {
  position: relative;
  top: -35px;
}
.dpup-1 label {
  cursor: pointer;
  position: absolute;
  right: 15px;
}
.dpup-1 img {
  width: 14px;
}
#upload-photo-1 {
  opacity: 0;
  z-index: -1;
}
.createGroupPost .form-control {
  outline: none;
  border: none;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
.createGroupPost .form-control:focus {
  border: none;
  outline: none;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}
.width-size {
  width: 30%;
}
.dp-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 1rem;
}
.crd img {
  width: 100%;
  height: 100%;
}
.createForm,
.coverSelection,
.groupPostsection {
  border: 0.2px solid rgba(68, 68, 68, 0.15);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.25);
}
@media only screen and (max-width: 1024px) {
  .width-size {
    width: 60%;
  }
}
@media only screen and (max-width: 767px) {
  .width-size {
    width: 100%;
  }
  .createGroupSection {
    top: 50px;
  }
  .createGroupSection {
    top: 0px;
  }
}
