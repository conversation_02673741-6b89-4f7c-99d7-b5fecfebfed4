main {
  height: 100vh;
}
.settings-icon {
  border: none;
  background: #ae378e;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
}
.header-title h4 {
  color: #af378f;
  font-weight: bold;
}
.header-title img {
  width: 30px;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
.optionList li a {
  color: rgba(68, 68, 68, 0.85);
  font-size: 12px;
}
.div {
  width: 2px;
  height: 15px;
  background: #999;
  opacity: 0.3;
}
.memsec-header a {
  font-size: 12px;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.48);
  text-decoration: none;
}
.active {
  color: #af378f !important;
}
.userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #ff6565;
}
.btn-outline-primary {
  border-color: #af378f !important;
  border-radius: 5px;
  font-size: 12px;
  color: #af378f;
}
.reqList ul li {
  margin-bottom: 30px;
}
.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #ff6565;
}
.back-btn {
  color: #af378f;
}
.back-btn:active {
  text-decoration: none;
}
.width-size {
  width: 50% !important;
}
.user-profile-img {
  width: 30px;
}
@media only screen and (max-width: 767px) {
  .user-dp {
    width: auto;
    height: auto;
    padding: 5px;
  }
  .user-dp img {
    width: 35px;
    height: 35px;
  }
  .width-size {
    width: 100%;
  }
  .bt button {
    width: auto !important;
  }
  .reqList {
    top: 0 !important;
  }
}
