.group-title {
  width: 100%;
}
#memsssection {
  position: sticky;
  z-index: 10;
  padding: 10px 0;
}
.custom-fix #memsssection {
  top: 63px;
  border-radius: 0 0 10px 10px;
}
.back-btn {
  color: #000000b3;
}
a {
  text-decoration: none !important;
}
.createGroup-btn {
  border-radius: 10px;
  box-shadow: 0 2px 3px #00000040;
  padding: 10px;
}
.createGroup-btn a {
  color: #848484;
}
.header-contents h2 {
  font-size: 20px;
  color: #000000b3;
  font-weight: 700;
}
.settings-icon {
  border: none;
  background: #000000b3;
  box-shadow: 0 0 10px #00000040;
  border-radius: 50%;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
}
.header-title h4 {
  color: #000000b3;
  font-weight: 700;
}
.header-title img {
  width: 30px;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid #9d9d9d;
}
.optionList li a {
  color: #848484;
  font-size: 12px;
}
.div {
  width: 1px;
  height: 15px;
  background: #949494;
  opacity: 0.3;
}
.memsec-header a {
  font-size: 12px;
  color: #848484;
  text-decoration: none;
}
.active {
  color: #000000b3 !important;
  font-weight: 700;
}
.userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #FF4B33;
}
.btn-outline-primary {
  border-color: #000000b3 !important;
  border-radius: 5px;
  font-size: 12px;
  color: #000000b3;
}
.reqList ul li {
  margin-bottom: 30px;
}
.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #FF4B33;
}
.back-btn {
  color: #000000b3;
}
.back-btn:active {
  text-decoration: none;
}
.create-group p {
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 16px;
  text-align: center;
  color: #848484;
}
.create-group .create-group-button {
  background: #FFD602;
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 5px;
  width: calc(100% - 40%);
  padding: 6px;
  margin-left: 20% !important;
}
.create-group .create-group-button a {
  color: #848484;
  font-size: 1.1rem;
  font-weight: 700;
}
.posts-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 2rem;
}
.post-card {
  position: relative;
  border-radius: 10px;
  box-shadow: 0 4px 10px #0000001A;
  background-color: #FFFFFF;
}
.post-card-header {
  background: #000000b3;
  overflow: visible;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  text-align: center;
  color: #FFFFFF;
}
.post-card-body,
.post-card-footer {
  padding: 1rem;
}
.post-card-body .members p,
.post-card-footer .members p,
.post-card-body .pb-pr p,
.post-card-footer .pb-pr p {
  margin: 0;
}
.post-pending {
  position: absolute;
  width: 25px;
  height: 25px;
  background: #FF4B33;
  border: 1px solid #FFFFFF;
  box-shadow: 0 4px 4px #00000040;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: -12px;
  left: 20px;
  color: #FFFFFF;
}
.divider {
  border-bottom: 1px solid #848484;
  margin: 12px 12px 0;
}
.width-size {
  width: 60%;
}
.search input {
  border: none;
  background: #FFFFFF;
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 2px 4px #0000001A;
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .posts-cards {
    grid-template-columns: repeat(1, 1fr);
  }
  .width-size {
    width: 100%;
  }
  .header-contents {
    justify-content: space-between;
  }
}
.validation-message p {
  margin: 0;
}
.uimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #FF4B33;
}
.notification-msge p {
  color: #848484;
}
.msge {
  color: #000000b3;
}
.header-content h3 {
  color: #000000b3;
}
.notification-ul li {
  border-bottom: 1px solid #d4d4d4;
}
.header-wrapper a {
  color: #000000b3;
}
.width-size {
  width: 40%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .width-size {
    width: 100%;
  }
}
main {
  height: 100vh;
}
.settings-icon {
  border: none;
  background: #000000b3;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 50%;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
}
.header-title h4 {
  color: #000000b3;
  font-weight: 700;
}
.header-title img {
  width: 30px;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid #9d9d9d;
}
.optionList li a {
  color: #848484;
  font-size: 12px;
}
.div {
  width: 2px;
  height: 15px;
  background: #949494;
  opacity: 0.3;
}
.memsec-header a {
  font-size: 12px;
  font-weight: 700;
  color: #9d9d9d;
  text-decoration: none;
}
.active {
  color: #000000b3 !important;
}
.userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #FF4B33;
}
.btn-outline-primary {
  border-color: #000000b3 !important;
  border-radius: 5px;
  font-size: 12px;
  color: #000000b3;
}
.reqList ul li {
  margin-bottom: 30px;
}
.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #FF4B33;
}
.back-btn {
  color: #000000b3;
}
.back-btn:active {
  text-decoration: none;
}
.width-size {
  width: 50% !important;
}
.user-profile-img {
  width: 30px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .user-dp {
    width: auto;
    height: auto;
    padding: 5px;
  }
  .user-dp img {
    width: 35px;
    height: 35px;
  }
  .width-size {
    width: 100%;
  }
  .bt button {
    width: auto !important;
  }
  .reqList {
    top: 0 !important;
  }
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 999;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #0000001A;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message .show {
  transform: translateY(0px);
}
.alert-message .hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #06D781;
}
#errorMsg {
  background: #FF4B33;
}
.back-btn {
  color: #000000b3;
}
.settings-icon {
  border: none;
  background: #000000b3;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 50%;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
}
.header-title h4 {
  color: #000000b3;
  font-weight: 700;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid #9d9d9d;
}
.optionList li a {
  color: #848484;
  font-size: 12px;
}
.header-title img {
  width: 25px;
}
.reportsec-header a {
  font-size: 12px;
  font-weight: 700;
  color: #848484;
  text-decoration: none;
}
.div {
  width: 2px;
  height: 15px;
  background: #949494;
  opacity: 0.3;
}
.active {
  color: #000000b3 !important;
}
.userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #FF4B33;
}
.btn-outline-primary {
  border-color: #000000b3 !important;
  border-radius: 5px;
  font-size: 12px;
  color: #000000b3;
}
.ul li {
  margin-bottom: 15px;
}
.usn {
  color: #444444;
}
.user-cred-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.user-cred-img img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.post-time p {
  font-size: 13px;
  font-style: italic;
  color: #848484;
}
.rpt-post-card {
  background: #FFFFFF;
  box-shadow: 0 2px 4px #00000040;
  border-radius: 10px;
  padding: 12px;
}
.reportedpostlist {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1rem;
}
.back-btn {
  color: #000000b3;
}
.back-btn:active {
  text-decoration: none;
}
.uImg {
  width: 30px;
  height: 30px;
}
.repPost {
  border-radius: 10px;
  box-shadow: 0 2px 4px #00000040;
}
.optionBtns button {
  width: 150px;
}
.reportedTime {
  font-size: 12px;
  font-style: italic;
  color: #848484;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .repPost {
    width: 100% !important;
  }
  .optionBtns button {
    width: 120px;
  }
  .reportedpostlist {
    grid-template-columns: repeat(1, 1fr);
  }
}
.reqList {
  min-height: 700px;
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 999;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #FFFFFF;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message.show {
  transform: translateY(0px);
}
.alert-message.hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #06D781;
}
#errorMsg {
  background: #FF4B33;
}
.back-btn {
  color: #000000b3;
  text-decoration: none;
}
.header-title h4 {
  color: #000000b3;
  font-weight: 700;
}
.header-title img {
  width: 25px;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid lighgten(#444444, 35%);
}
.optionList li a {
  color: lighgten(#444444, 25%);
  font-size: 12px;
}
.btn-outline-primary {
  border-color: #000000b3 !important;
  border-radius: 5px;
  font-size: 12px;
  color: #000000b3;
}
.btn-primary {
  background: #000000b3 !important;
  border: none;
  font-size: 12px;
}
.user-dp {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #FF4B33;
}
.bt button {
  width: 120px;
}
.reqList ul li {
  margin-bottom: 30px;
}
.back-btn {
  color: #000000b3;
}
.back-btn:active {
  text-decoration: none;
}
.width-size {
  width: 50%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .width-size {
    width: 100%;
  }
  .reqList {
    position: relative;
    top: 50px;
  }
  .admin-panel-header {
    position: relative;
    top: 0;
  }
}
#errorPostEditor,
#errorUploadFile {
  margin-top: -15px;
}
.error-text {
  font-size: 12px;
  font-weight: 500;
}
.inside-comments {
  max-height: 300px;
  overflow-y: auto;
}
#post-image-preview,
#editPostImage {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  box-shadow: 0 0 10px #0000001A;
}
#image-preview .close-img,
#editPostImagePreview .close-img {
  position: absolute;
  top: -10px;
  right: -15px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  border: none;
}
#image-preview #imgFileName,
#editPostImagePreview #editPostImgFileName {
  font-size: 12px;
  color: #444444;
  padding-top: 5px;
}
#fileNamePreview p,
#editPostFileName p {
  font-size: 12px;
  color: #444444;
}
.editor {
  width: 100%;
  height: 150px;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px !important;
  border: none !important;
  padding-bottom: 45px !important;
}
.blury {
  -webkit-filter: blur(7px);
  -moz-filter: blur(7px);
  -o-filter: blur(7px);
  -ms-filter: blur(7px);
  filter: blur(7px);
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 9992;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #FFFFFF;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message.show {
  transform: translateY(0px);
}
.alert-message.hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #06D781;
}
#errorMsg {
  background: #FF4B33;
}
#groupReportModal,
#joinRequestModal,
#groupPostReportModal,
#groupPostDeleteModal,
#groupUserReportModal,
#groupUserExitModal {
  z-index: 9992;
}
#groupPostEditModal {
  background: #000000B3 !important;
}
#reportReason,
#joinReason {
  min-height: 150px;
  max-height: 150px;
  resize: none;
}
#reportReason:focus,
#joinReason {
  border: 1px solid #FFFFFF !important;
}
.admin-panel-header .back-btn {
  color: #000000b3;
}
.admin-panel-header .settings-icon {
  background: #000000b3;
}
#adminoptions {
  position: fixed;
  z-index: 100;
  background: #FFFFFF;
  width: 100%;
  bottom: 0;
  padding-top: 10px;
  top: 155px;
  width: 25%;
  height: 200px;
  right: 90px;
  border-radius: 5px;
}
#adminoptions::before {
  content: "";
  position: absolute;
  width: 25px;
  height: 25px;
  transform: rotate(45deg);
  background: #FFFFFF;
  top: -10px;
  left: 47%;
}
.header-title h4 {
  color: #000000b3;
  font-weight: 700;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid #9d9d9d;
}
.optionList li a {
  color: #848484;
  font-size: 12px;
}
.optionList li a:hover {
  text-decoration: none !important;
}
.groupBanner {
  height: 150px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
}
.groupBanner::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: #000000B3;
  z-index: 1;
}
.groupBannerContent {
  z-index: 2;
  position: relative;
  padding-top: 10px;
  height: 40px;
}
.header {
  /*margin-right: 60px;*/
}
.header a {
  text-decoration: none;
  color: #FFFFFF;
}
.rpt-btn {
  background: #949494;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 38px;
  padding: 5px 15px;
  font-size: 11px;
  height: 32px;
}
.grpBannerName {
  position: absolute;
  bottom: 40px;
  z-index: 2;
}
.grpBannerName p {
  font-size: 13px;
  color: #FFFFFF;
}
.invite-btn {
  background: #FFFFFF;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px #0000001A;
  color: #848484;
  width: 40%;
  margin-right: 10px;
  display: flex;
}
.invite-btn:hover {
  text-decoration: none;
}
.join-btn {
  background: #FFFFFF;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px #0000001A;
  color: #848484;
  width: 40%;
  margin-left: 10px;
  display: flex;
}
.join-btn:hover {
  text-decoration: none;
}
.request-btn {
  background: #FFFFFF;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px #0000001A;
  color: #848484;
  width: 40%;
  margin-left: 10px;
  display: flex;
}
.request-btn:hover {
  text-decoration: none;
}
.search {
  background: #FFFFFF;
  border-radius: 7px;
  border: none;
  padding: 10px 15px;
  width: 100%;
}
.search:focus {
  outline: none;
}
.user-img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 1px solid #949494;
}
.user-img img {
  width: 100%;
}
.userNm .usrnm {
  font-size: 14px;
}
.userNm .posttm {
  font-size: 14px;
  color: #848484;
}
.more-btn {
  background: transparent;
}
.more-btn:active {
  outline: none;
  border: none;
}
.more-btn:focus {
  outline: none;
  border: none;
}
.more-option {
  position: absolute;
  width: 150px;
  height: 41px;
  left: 60%;
  top: 10%;
  background: #FFFFFF;
  box-shadow: 0 3px 10px #0000001A;
  border-radius: 5px;
  color: #FF4B33;
  font-size: 12px;
}
.card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 2px 4px #0000001A;
}
.like-count {
  font-size: 10px;
  font-weight: 700;
}
.like-btn {
  background: transparent;
  border: none;
  font-size: 10px;
  color: #848484;
}
.comment-btn {
  background: transparent;
  border: none;
  font-size: 10px;
}
.comment-btn:focus {
  outline: none;
  border: none;
}
.comment-section {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  padding: 5px;
  /* margin-top: 30px; */
}
.crd-grp {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1rem;
}
.settings-btn {
  background: #949494;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 6px;
  display: flex;
}
.comment-input {
  border: none !important;
  background: #FFFFFF;
  border-radius: 10px 0 0 10px !important;
  width: 100%;
  height: 30px;
  padding: 10px;
  font-size: 12px;
}
.comment-input::placeholder {
  font-style: italic;
  font-size: 12px;
  color: #848484;
}
.comment-input:focus {
  border: none;
  outline: none;
}
.total-replies {
  font-size: 10px;
  font-style: italic;
}
.reply-btn {
  color: #000000b3;
  background: transparent;
  border: none;
}
.reply-btn:focus {
  outline: none;
}
.reply {
  border-left: 2px solid #0000001A;
  padding: 5px;
}
.reply .reply-text {
  font-size: 14px;
}
.reply-section {
  transition: all 0.3s ease;
}
.img {
  width: auto;
  border-radius: 10px;
  overflow: hidden;
}
.img img {
  width: 100%;
  border-radius: 10px;
}
.dpup label {
  cursor: pointer;
  margin-top: -36px;
  position: absolute;
  left: 78%;
  font-size: 14px;
  margin-bottom: 0 !important;
}
.dpup label img {
  width: 15px;
}
#upload-photo {
  opacity: 0;
  z-index: -1;
}
.postbutton {
  position: absolute;
  margin-top: -72px;
  left: 79.5%;
  border: none;
  background: #000000b3;
  border-radius: 10px 0;
  width: 56px;
  height: 45px;
  color: #FFFFFF;
}
.post {
  border-radius: 10px;
  box-shadow: 0 0 10px #0000001A;
  border: none;
}
.post:focus {
  border: none !important;
  outline: none !important;
}
.post::placeholder {
  font-size: 14px;
}
.form-control:focus {
  border: none !important;
  outline: none !important;
  border-color: #FFFFFF;
}
.attach-btn {
  position: absolute;
  margin-top: -11%;
  left: 70%;
  border: none;
  border-radius: 10px 0;
  width: 56px;
  height: 45px;
  color: #FFFFFF;
  background: transparent;
}
.up-btn {
  background: transparent;
  border: none;
  font-size: 14px;
}
.at-btn {
  width: 200px;
}
.reportbtn {
  /*margin-left: 175px;*/
}
.user-info-img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.dropleft .dropdown-toggle::before {
  display: none  !important;
}
.dropleft button {
  background: transparent;
}
.wonderslate_wall .dropdown-menu {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  border: none;
}
.wonderslate_wall .dropdown-menu a {
  font-size: 12px;
  color: #FF4B33;
}
.post-img {
  max-width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 10px;
}
.postOptions button {
  background: transparent;
  border: none;
  cursor: pointer;
}
.posts-card {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  position: relative;
  z-index: 2;
}
.comment-bttn button {
  background: transparent;
  border: none;
  cursor: pointer;
}
.divide {
  width: 1px;
  height: 20px;
  background: #444444;
  opacity: 0.7;
  margin-right: 5px;
  margin-left: 5px;
}
button:focus {
  border: none;
  outline: none;
}
.comments-sec {
  background: #F1F1F1;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 0 0 10px 10px;
  z-index: 1;
  position: relative;
  margin: 0 15px;
}
.reply-sec p {
  font-size: 12px;
  font-style: italic;
  color: #949494;
  margin-right: 10px;
}
.reply-sec button {
  background: transparent;
  border: none;
  color: #000000b3;
  font-size: 13px;
  padding: 0;
  font-weight: 600;
  cursor: pointer;
}
.dropdown-menu ul li {
  list-style: none;
}
.dropdown-menu ul li a:hover {
  background: transparent;
}
.textarea-container {
  position: relative;
}
.textarea-container textarea {
  width: 100%;
  height: auto;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  border: none;
  padding-bottom: 45px !important;
}
.textarea-container textarea:focus {
  outline: 0;
}
.textarea-container textarea::placeholder {
  font-size: 12px;
  font-style: italic;
  color: #848484;
}
.textarea-container button {
  position: absolute;
  right: 15px;
  background: #000000b3;
  border-radius: 10px 0;
  border: none;
  color: #FFFFFF;
  width: 56px;
  height: 45px;
  bottom: 5px;
}
.textarea-container label {
  position: absolute;
  right: 70px;
  bottom: 5px;
  width: 40px;
  height: 40px;
  margin: 0;
  padding: 5px;
  text-align: center;
  background: #FFFFFF;
  border-radius: 10px 0 0;
}
.textarea-container label img {
  width: 15px;
  cursor: pointer;
}
.textarea-container input {
  opacity: 0;
  visibility: hidden;
  display: none;
}
.width-size {
  width: 80%;
}
.postmodalfooter input {
  opacity: 0;
  visibility: hidden;
}
.time {
  color: #848484;
  font-size: 12px;
  font-style: italic;
}
.modal .textarea-container button {
  width: 100px;
  right: 0;
}
.modal .textarea-container label {
  right: 100px;
}
.editPostImg {
  width: 100%;
}
.joined-btn {
  background: #212121;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px #0000001A;
  color: #FFFFFF;
  width: 40%;
  margin-left: 10px;
}
#admin {
  background: #212121;
  border-radius: 10px;
  height: 40px;
  position: relative;
  z-index: 2;
  margin-top: -20px;
  box-shadow: 0 2px 4px #0000001A;
  color: #FFFFFF;
  width: 40%;
  margin-left: 10px;
  display: flex;
}
#admin:hover {
  text-decoration: none;
}
.joined-dropdownBtn {
  background: transparent;
  color: #FFFFFF;
  font-style: normal;
  font-weight: 400;
}
.drpDwnMenu {
  height: 100px;
  width: 100%;
}
.drpDwnMenu a {
  padding: 1rem;
}
.drpDwnMenu a:nth-child(1) {
  border-bottom: 1px solid #444444;
}
.dropdown-menu a img {
  width: 10px;
}
.width-size {
  width: 40% !important;
}
.searchGif {
  display: flex;
  align-items: center;
  justify-content: center;
}
.groupPrivacyType img {
  width: 30px;
  height: 30px;
}
.searchGif img {
  width: 80px;
}
.notification {
  position: relative;
}
.notificationCount {
  position: absolute;
  left: 70%;
  top: 0;
  background: #FF4B33;
  width: 15px;
  height: 15px;
  text-align: center;
  border-radius: 50%;
  color: #FFFFFF;
  font-size: 10px;
}
.postList {
  min-height: 500px;
}
.groupBanner {
  height: 200px;
}
.rotate {
  transform: rotate(100deg);
  transition: all 0.3s ease;
}
.fa-lock-open {
  color: #27AE60;
}
.statusText {
  margin-top: 5px;
}
.fa-lock {
  color: #FF4B33;
}
.noComt {
  width: 5%;
  margin-left: auto;
  margin-right: auto;
  display: block;
}
@media only screen and (max-width: 990px) {
  .width-size {
    width: 100% !important;
  }
  .textarea-container {
    top: 0;
  }
}
@media only screen and (max-width: 990px) {
  .width-size {
    width: 100%;
  }
  .img-preview {
    top: 50px !important;
  }
  .edPostPrev {
    margin-top: 70px !important;
  }
}
@media only screen and (max-width: 768px) {
  .dpup label {
    left: 75%;
  }
  .grp-btns {
    width: 100% !important;
  }
  #adminoptions {
    width: 100%;
    height: 100%;
    right: 0;
  }
  #adminoptions::before {
    left: 91%;
  }
}
@media only screen and (max-width: 452px) {
  .dpup label {
    left: 75%;
  }
  .admin-panel-header {
    top: 0;
  }
  .textarea-container {
    top: 60px;
  }
  .groupBanner {
    top: 0;
  }
  .grp-btns {
    position: relative;
    top: 0;
  }
  #adminoptions {
    top: 110px;
  }
  #adminoptions::before {
    left: 90%;
  }
  #postList {
    position: relative;
    top: 60px;
    margin-bottom: 150px;
  }
}
@media only screen and (max-width: 375px) {
  #adminoptions::before {
    left: 89%;
  }
}
\ #errorPostEditor,
#errorUploadFile {
  margin-top: -15px;
}
.error-text {
  font-size: 12px;
  font-weight: 500;
}
#post-image-preview {
  max-width: 100%;
  height: auto;
  border-radius: 5px;
  box-shadow: 0 0 10px #0000001A;
}
#image-preview .close-img {
  position: absolute;
  top: -10px;
  right: -15px;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  border: none;
}
#image-preview #imgFileName {
  font-size: 12px;
  color: #444444;
  padding-top: 5px;
}
.editor {
  width: 100%;
  height: 150px;
  box-sizing: border-box;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px !important;
  border: none !important;
  padding-bottom: 40px !important;
}
.editor.ck-focused {
  border: none !important;
  box-shadow: 0 0 10px #0000001A !important;
  border-radius: 10px !important;
}
.editor .ck-placeholder::before {
  color: #919191;
  font-style: italic;
}
.show-profile-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #00000040;
  border-radius: 5px;
}
.show-profile-image p {
  position: relative;
  color: #d4d4d4;
}
.show-profile-image img {
  position: relative;
}
.alert-message {
  position: fixed;
  top: 15px;
  z-index: 999;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #FFFFFF;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message.show {
  transform: translateY(0px);
}
.alert-message.hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #06D781;
}
#errorMsg {
  background: #FF4B33;
}
.createGroupSection {
  min-height: 500px;
  position: relative;
  top: 20px;
}
input[type="radio"]:checked + label {
  background-color: transparent;
  border: none;
}
.visibility input[type="radio"]:checked + label {
  background-color: transparent;
  color: #949494;
  border: none;
}
.back-btn {
  color: #000000b3;
}
a {
  text-decoration: none !important;
}
.createGroup-btn {
  border-radius: 10px;
  box-shadow: 0 2px 3px #0000001A;
  padding: 10px;
  background-color: #FFFFFF;
}
.createGroup-btn a {
  color: #848484;
}
.header-contents h2 {
  font-size: 20px;
  color: #000000b3;
  font-weight: 700;
}
.form__group {
  position: relative;
  padding: 15px 0 0;
  margin-top: 10px;
  width: 100%;
}
.form__field {
  width: 100%;
  border: 0;
  border-bottom: 2px solid #000000b3;
  outline: 0;
  font-size: 14px;
  color: #000000b3;
  padding: 3px 0;
  background: transparent;
  transition: border-color 0.2s;
}
.form__field::placeholder {
  color: transparent;
}
.form__field:placeholder-shown ~ .form__label {
  font-size: 14px;
  cursor: text;
  top: 10px;
  color: #000000b3;
}
.form__label {
  position: absolute;
  top: 5px;
  display: block;
  transition: 0.2s;
  font-size: 14px;
  color: #6C757D;
}
.form__field:focus {
  padding-bottom: 3px;
  font-weight: 700;
  border-width: 3px;
  border-color: #000000b3;
}
.form__field:focus ~ .form__label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #000000b3;
}
/* reset input */
.form__field:required,
.form__field:invalid {
  box-shadow: none;
}
.form-check-label {
  background: transparent;
}
.crt-grp input {
  border: none !important;
  border-bottom: 2px solid #000000b3 !important;
  border-radius: 0;
  margin-top: -10px;
}
.crt-grp input:focus {
  outline: none;
  border: none;
}
.bgHighlight {
  background: #000000b3;
  color: #FFFFFF;
}
.color {
  color: #0000001A;
}
.chk-privacy {
  background: #FFFFFF;
  box-shadow: 0 4px 10px #444444;
  border-radius: 10px;
}
.privacy-option {
  padding: 10px;
  box-shadow: 0 4px 10px #444444;
  border-radius: 10px;
}
.form-control:focus {
  outline: none;
  border: none;
}
.pr-text {
  font-size: 14px;
  color: #000000b3;
}
.pr-lab {
  color: #848484;
  font-size: 10px;
  display: block;
}
/*******************  Description     *******************/
.dp-cards {
  display: flex;
  flex-direction: row;
  gap: 10px;
}
.crd {
  /*width: 70px;*/
  height: 63px;
  border-radius: 5px;
  cursor: pointer;
}
.dp-card1 {
  background: #FF4B33;
}
.dp-card2 {
  background: #06D781;
}
.dp-card3 {
  background: #000000b3;
}
.dp-card4 {
  background: #FFC107;
}
.bttns {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.btn-skip {
  width: 50%;
}
.btn-next-1 {
  width: 50%;
}
.btn-next {
  margin-top: 0 !important;
}
.dpup label {
  cursor: pointer;
  width: 100%;
  height: 138px;
  background: #FFFFFF;
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  flex-direction: column;
  color: #848484;
  text-align: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}
.dpup label p {
  font-size: 12px;
}
#upload-photo {
  opacity: 0;
  z-index: -1;
}
.dpup-1 {
  position: relative;
  top: -35px;
}
.dpup-1 label {
  cursor: pointer;
  position: absolute;
  right: 15px;
}
.dpup-1 img {
  width: 14px;
}
#upload-photo-1 {
  opacity: 0;
  z-index: -1;
}
.createGroupPost .form-control {
  outline: none;
  border: none;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
}
.createGroupPost .form-control:focus {
  border: none;
  outline: none;
  box-shadow: 0 0 10px #0000001A;
}
.width-size {
  width: 30%;
}
.dp-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 1rem;
}
.crd img {
  width: 100%;
  height: 100%;
}
.createForm,
.coverSelection,
.groupPostsection {
  border: 0.2px solid #919191;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 2px #0000001A;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .width-size {
    width: 60%;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .width-size {
    width: 100%;
  }
  .createGroupSection {
    top: 50px;
  }
  .createGroupSection {
    top: 0;
  }
}
