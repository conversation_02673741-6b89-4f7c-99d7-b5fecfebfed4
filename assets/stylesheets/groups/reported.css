.alert-message {
  position: fixed;
  top: 15px;
  z-index: 999;
  width: 95%;
  padding: 10px 15px;
  right: 0;
  left: 0;
  margin: 0 auto;
  border-radius: 7px;
  box-shadow: 1px 1px 4px #ddd;
  transform: translateY(-70px);
  transition: all 0.3s linear;
}
.alert-message .show {
  transform: translateY(0px);
}
.alert-message .hide {
  transform: translateY(-70px);
}
#successMsg {
  background: #7aee7a;
}
#errorMsg {
  background: #ff8e8e;
}
.back-btn {
  color: #af378f;
}
.settings-icon {
  border: none;
  background: #ae378e;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 50%;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
}
.header-title h4 {
  color: #af378f;
  font-weight: bold;
}
.optionList li {
  list-style: none;
  padding: 15px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
.optionList li a {
  color: rgba(68, 68, 68, 0.85);
  font-size: 12px;
}
.header-title img {
  width: 25px;
}
.reportsec-header a {
  font-size: 12px;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.48);
  text-decoration: none;
}
.div {
  width: 2px;
  height: 15px;
  background: #999;
  opacity: 0.3;
}
.active {
  color: #af378f !important;
}
.userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: #ff6565;
}
.btn-outline-primary {
  border-color: #af378f !important;
  border-radius: 5px;
  font-size: 12px;
  color: #af378f;
}
.ul li {
  margin-bottom: 15px;
}
.usn {
  color: #444444;
}
.user-cred-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}
.user-cred-img img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.post-time p {
  font-size: 13px;
  font-style: italic;
  color: rgba(68, 68, 68, 0.48);
}
.rpt-post-card {
  background: #ffffff;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 12px;
}
.reportedpostlist {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1rem;
}
.back-btn {
  color: #af378f;
}
.back-btn:active {
  text-decoration: none;
}
.uImg {
  width: 30px;
  height: 30px;
}
.repPost {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}
.optionBtns button {
  width: 150px;
}
.reportedTime {
  font-size: 12px;
  font-style: italic;
  color: rgba(68, 68, 68, 0.48);
}
@media only screen and (max-width: 767px) {
  .repPost {
    width: 100% !important;
  }
  .remFrGrp {
    margin-top: 10px;
  }
}
@media only screen and (max-width: 964px) {
  .optionBtns button {
    width: 120px;
  }
}
@media only screen and (max-width: 767px) {
  .repPost {
    width: 100% !important;
  }
  .optionBtns button {
    width: 120px;
  }
}
@media only screen and (max-width: 767px) {
  .reportedpostlist {
    grid-template-columns: repeat(1, 1fr);
  }
}
