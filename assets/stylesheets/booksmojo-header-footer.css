header {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  font-weight: normal; }
  header .navbar-wonderslate {
    padding: 0;
    font-weight: normal;
    background-color: #fff;
    box-shadow: 0 4px rgba(0, 0, 0, 0.1);
    border-radius: 0; }
    header .navbar-wonderslate .navbar-header {
      margin-left: 112px; }
      header .navbar-wonderslate .navbar-header .navbar-toggle {
        float: left; }
        header .navbar-wonderslate .navbar-header .navbar-toggle .icon-bar {
          background-color: #000; }
  header .navbar-brand {
    width: 210px;
    height: 75px;
    font-family: 'Exo';
    font-weight: 300;
    text-transform: uppercase;
    margin: 20px 0 20px 0;
    padding: 0; }
    header .navbar-brand:hover {
      color: #888; }
    header .navbar-brand img {
      max-width: 100%;
      display: inline-block;
      height: auto; }
  header .navbar-right.header-menus {
    padding-right: 112px; }
  header .header-menus {
    margin-left: 80px; }
    header .header-menus .header-menu-item {
      margin-right: 40px; }
      header .header-menus .header-menu-item a {
        padding: 47px 0 44px 0;
        color: #888;
        font-size: 16px; }
        header .header-menus .header-menu-item a:hover {
          color: #444;
          background-color: transparent; }
        header .header-menus .header-menu-item a.active {
          border-bottom: 5px solid #444;
          color: #444; }

footer {
  width: 100%;
  padding: 40px 0 40px 0;
  background-color: #fff;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
  position: relative;
  bottom: 0; }

.footer-wonderslate {
  font-family: 'Montserrat', sans-serif;
  padding-left: 112px; }
  .footer-wonderslate a {
    text-decoration: none; }

.download-links-wrapper, .footer-links, .footer-extra-links {
  list-style: none;
  color: #444;
  font-weight: bold; }

.download-links-item {
  margin-bottom: 15px; }

.download-links {
  width: 247px;
  height: 73px;
  display: block;
  text-indent: -20000px; }

.apple-store {
  background: url("../images/booksmojo/app-store.png");
  background-repeat: no-repeat;
  background-size: 100% 100%; }

.play-store {
  background: url("../images/booksmojo/play-store.png");
  background-repeat: no-repeat;
  background-size: 100% 100%; }

.footer-links {
  padding-left: 160px; }

.footer-title-footer p, .footer-link-item p, .footer-title-footer a, .footer-link-item a {
  color: #888;
  font-size: 14px; }

.footer-title p {
  color: #444;
  font-size: 24px;
  font-weight: bold; }

.footer-link-item {
  margin-bottom: 14px; }
  .footer-link-item a:hover {
    color: #444; }

.footer-extra-links {
  padding: 0; }
  .footer-extra-links li a {
    display: inline-block;
    color: #888;
    font-size: 16px;
    font-weight: 300;
    margin-right: 24px; }
    .footer-extra-links li a:hover {
      color: #444; }

@media screen and (min-width: 320px) and (max-width: 767px) {
  header .navbar-wonderslate .navbar-header {
    margin-left: -15px !important;
    padding-bottom: 15px; }
    header .navbar-wonderslate .navbar-header .navbar-brand {
      margin: 20px auto;
      text-align: center;
      display: block;
      position: relative;
      float: none; }
  header .navbar-wonderslate .navbar-collapse {
    padding-right: 0;
    padding-left: 0;
    overflow-x: hidden; }
  header .navbar-wonderslate .header-menus.navbar-right {
    padding-right: 0; }
  header .navbar-wonderslate .header-menus {
    margin-left: 0; }
    header .navbar-wonderslate .header-menus .header-menu-item {
      margin-right: 0;
      margin-bottom: 5px;
      border-bottom: 1px solid #ccc; }
      header .navbar-wonderslate .header-menus .header-menu-item a {
        padding: 10px 0 10px 5px; }

  footer {
    padding: 5px; }
    footer .footer-wonderslate {
      padding: 0; }

  .download-links-wrapper, .footer-links, .footer-extra-links {
    padding: 0; }

  .download-links-item {
    display: inline-block;
    margin-right: 17px; }

  .download-links {
    width: 120px;
    height: 45px;
    display: block; }

  .footer-extra-links li a {
    font-size: 12px;
    margin-right: 8px; } }
@media screen and (min-width: 768px) and (max-width: 1100px) {
  header .navbar-wonderslate .navbar-header {
    margin-left: 10px; }
  header .header-menus {
    margin-left: 50px; }
    header .header-menus .header-menu-item {
      margin-right: 15px; }
      header .header-menus .header-menu-item a {
        font-size: 14px; }
  header .navbar-right.header-menus {
    padding-right: 0; } }
@media screen and (min-width: 1100px) and (max-width: 1500px) {
  header .navbar-wonderslate .navbar-header {
    margin-left: 10px; }
  header .navbar-right.header-menus {
    padding-right: 0; } }

/*# sourceMappingURL=booksmojo-header-footer.css.map */
