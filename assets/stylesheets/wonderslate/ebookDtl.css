.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
.ebook_detail .container-fluid {
  width: 90%;
}
@media only screen and (max-width: 767px) {
  .ebook_detail .container-fluid {
    width: 100%;
  }
}
.ebook_detail .image_wrapper .book_image {
  width: 165px;
  height: 225px;
  position: relative;
  z-index: 10;
  margin: 0 auto;
}
.ebook_detail .image_wrapper .book_image .bookShadow img {
  width: 100%;
  height: 225px;
  border-radius: 3px 0 0 3px;
}
.ebook_detail .image_wrapper .book_image .bookShadow::after {
  width: 3px;
}
.ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
  height: 225px;
  padding: 10px;
}
.ebook_detail .image_wrapper .book_image .bookShadow .book_price {
  position: absolute;
  z-index: 10;
  color: #ffffff;
  background-color: #27AE60;
  padding: 5px 15px;
  bottom: 32px;
  left: -6px;
  text-align: left;
  line-height: normal;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
.ebook_detail .image_wrapper .book_image .bookShadow .book_price .offer_price {
  font-family: 'Rubik', sans-serif !important;
  font-size: 14px;
  display: block;
  font-weight: 500;
}
.ebook_detail .image_wrapper .book_image .bookShadow .book_price .list_price {
  font-family: 'Rubik', sans-serif !important;
  font-size: 11px;
}
.ebook_detail .image_wrapper .book_image .bookShadow .book_price::after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #27AE60 #27AE60 transparent transparent;
}
.ebook_detail .image_wrapper .book_image_bottom {
  margin-top: -12px;
  position: relative;
  z-index: 11;
}
.ebook_detail .image_wrapper .book_image_bottom img {
  width: 100%;
}
@media only screen and (max-width: 767px) {
}
.ebook_detail .image_wrapper .book_buttons .col {
  padding: 0 5px;
}
.ebook_detail .image_wrapper .book_buttons .col:first-child a {
  border-color: #9B51E0;
  color: #9B51E0;
  background-color: #ffffff;
}
.ebook_detail .image_wrapper .book_buttons .col:last-child a {
  border-color: #9B51E0;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  color: #ffffff;
}
.ebook_detail .image_wrapper .book_buttons .col a {
  width: 100%;
  height: 50px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  font-family: 'Poppins', sans-serif;
  line-height: 2.5;
}
.ebook_detail .image_wrapper .book_buttons .col a:hover {
  box-shadow: 0 0.2rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.ebook_detail .book_info .book_description h4 {
  font-weight: bold;
  font-size: 22px;
}
.ebook_detail .book_info .book_description .book-desc {
  color: #949494;
  font-size: 14px;
  font-weight: 300;
}
.ebook_detail .book_info .book_description .book-desc a.exp {
  font-weight: normal;
  font-style: normal;
  font-size: 15px;
}
.ebook_detail .book_info .chapter_lists .dropdown {
  border-radius: 5px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
}
.ebook_detail .book_info .chapter_lists button {
  height: 40px;
  /*border-color: @outline-btn;
        color: @outline-btn;*/
  background-color: #ffffff;
}
.ebook_detail .book_info .chapter_lists button span {
  font-weight: bold;
  display: inline-block;
  width: 95%;
}
.ebook_detail .book_info .chapter_lists button span img {
  padding-right: 10px;
}
.ebook_detail .book_info .chapter_lists button:after {
  color: #733EBA;
  vertical-align: 0.15em;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu {
  width: 100%;
  padding: 0;
  transform: translate3d(0px, 40px, 0px) !important;
  max-height: 330px;
  overflow-y: scroll;
  z-index: 99;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu span {
  white-space: pre-wrap;
  color: #733EBA;
  padding-top: 10px;
  padding-bottom: 10px;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu span:first-child {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu span:last-child {
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu span:active {
  background-color: transparent;
}
.ebook_detail .book_info .book_contains .card {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border: none;
  height: 100%;
}
.ebook_detail .book_info .book_contains .card p {
  font-size: 12px;
  color: #949494;
}
.ebook_detail .book_info .book_contains .card p img {
  padding-right: 5px;
}
.ebook_detail .book_info .book_adding .card {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border: none;
  height: 100%;
}
.ebook_detail .book_info .book_adding .card p {
  font-size: 12px;
  color: #949494;
}
.ebook_detail .book_info .book_adding .card p img {
  padding-right: 5px;
}
.popular_searches {
  height: fit-content;
}
.popular_searches h4 {
  color: #7F28A8;
  font-size: 20px;
}
.popular_searches .bg-light {
  background: #F7F7F7;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.14);
}
.popular_searches .bg-light .popular_search_lists .slick-track {
  margin-left: 0;
  margin-right: 0;
}
.popular_searches .bg-light .popular_search_lists .slick-prev {
  left: -41px;
}
.popular_searches .bg-light .popular_search_lists .slick-prev:hover {
  background-color: #ffffff;
}
.popular_searches .bg-light .popular_search_lists .slick-prev:active {
  background-color: #ffffff;
}
.popular_searches .bg-light .popular_search_lists .slick-prev:focus {
  background-color: #ffffff;
}
@media screen and (max-width: 1024px) {
  .popular_searches .bg-light .popular_search_lists .slick-prev {
    left: 0;
    z-index: 9;
  }
}
.popular_searches .bg-light .popular_search_lists .slick-next {
  right: -41px;
}
.popular_searches .bg-light .popular_search_lists .slick-next:hover {
  background-color: #ffffff;
}
.popular_searches .bg-light .popular_search_lists .slick-next:active {
  background-color: #ffffff;
}
.popular_searches .bg-light .popular_search_lists .slick-next:focus {
  background-color: #ffffff;
}
@media screen and (max-width: 1024px) {
  .popular_searches .bg-light .popular_search_lists .slick-next {
    right: 0;
    z-index: 9;
  }
}
.popular_searches .bg-light .popular_search_lists .slick-arrow {
  height: 70px;
}
.popular_searches .bg-light .popular_search_lists .img-wrapper {
  padding: 15px;
  width: 155px;
  margin: 0 auto;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
.popular_searches .bg-light .popular_search_lists .img-wrapper .bookShadow {
  margin-bottom: 10px;
}
.popular_searches .bg-light .popular_search_lists .img-wrapper .bookShadow img {
  width: 100%;
  height: 165px;
}
.popular_searches .bg-light .popular_search_lists a:hover .img-wrapper .uncover p {
  text-decoration: none;
}
.popular_searches .bg-light .popular_search_lists a:hover .content-wrapper p {
  text-decoration: underline;
}
.popular_searches .bg-light .popular_search_lists .content-wrapper p {
  font-size: 12px;
  color: #444444;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.popular_searches .bg-light .popular_search_lists .uncover {
  padding: 5px;
}
.popular_searches .bg-light .popular_search_lists .uncover p {
  font-size: 12px;
}
.book-publisher-name {
  text-transform: uppercase;
  color: #ABABAB !important;
}
@media only screen and (max-width: 767px) {
  .shapemobile {
    display: none;
  }
  .shapemobile.backMenu {
    display: flex !important;
  }
}
