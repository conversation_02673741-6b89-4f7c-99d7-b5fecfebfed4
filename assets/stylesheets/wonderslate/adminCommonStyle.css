.modal.fade .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(0.5);
  transform: translate(0, 0) scale(0.5);
}
.modal.show .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(1);
  transform: translate(0, 0) scale(1);
}
.modal-modifier .modal-header-modifier {
  border: none;
}
.modal-modifier .modal-body-modifier h1,
.modal-modifier .modal-body-modifier h2,
.modal-modifier .modal-body-modifier h3,
.modal-modifier .modal-body-modifier h4,
.modal-modifier .modal-body-modifier h5 {
  color: #212121;
  font-weight: 500;
  font-size: 18px;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-body-modifier h1,
  .modal-modifier .modal-body-modifier h2,
  .modal-modifier .modal-body-modifier h3,
  .modal-modifier .modal-body-modifier h4,
  .modal-modifier .modal-body-modifier h5 {
    font-size: 16px;
  }
}
.modal-modifier .modal-body-modifier p {
  color: #6C757D;
}
.modal-modifier .modal-body-modifier p strong {
  font-weight: 600;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-body-modifier p {
    font-size: 13px;
  }
}
.modal-modifier .modal-header-modifier .close {
  margin: 0;
}
.modal-modifier .close {
  position: absolute;
  right: 15px;
  top: 5px;
  padding: 0;
  font-weight: 100;
  font-size: 30px;
  opacity: 1;
  z-index: 10;
  color: #212121 !important;
}
.modal-modifier .close:focus,
.modal-modifier .close:active {
  outline: 0;
  box-shadow: none;
}
.modal-modifier .btn,
.modal-modifier a.btn {
  font-size: 14px;
}
.modal-modifier .modal-content-modifier {
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-dialog-modifier {
    align-items: flex-end;
    padding-bottom: 0;
    max-width: 100%;
    margin: 0;
    min-height: 100%;
  }
  .modal-modifier .modal-content-modifier {
    border-radius: 20px 20px 0 0;
  }
}
.modal-modifier .btn-shadow {
  box-shadow: 0 2px 4px #0000001A;
  -webkit-box-shadow: 0 2px 4px #0000001A;
  -moz-box-shadow: 0 2px 4px #0000001A;
}
.modal-modifier .btn-outline-secondary {
  color: #8E8E8E;
  border-color: #8E8E8E;
}
.modal-modifier .btn-outline-secondary:hover {
  color: #8E8E8E;
  background-color: transparent;
  border-color: #8E8E8E;
}
.modal-modifier .btn-outline-secondary:not(:disabled):not(.disabled):active,
.modal-modifier .btn-outline-secondary:not(:disabled):not(.disabled):active:focus {
  color: #8E8E8E;
  background-color: transparent;
  border-color: #8E8E8E;
  box-shadow: none;
}
.modal-modifier .btn-outline-secondary:focus:not(:active) {
  background-color: transparent;
}
.modal-modifier .btn-success {
  background-color: #27AE60;
  border-color: #27AE60;
}
.modal-modifier .btn-success:hover {
  background-color: #27AE60;
  border-color: #27AE60;
}
.modal-modifier .btn-success:not(:disabled):not(.disabled):active,
.modal-modifier .btn-success:not(:disabled):not(.disabled):active:focus {
  background-color: #27AE60;
  border-color: #27AE60;
  box-shadow: none;
}
.modal-modifier .btn-success.disabled,
.modal-modifier .btn-success:disabled {
  background-color: #27AE60 !important;
  color: #FFFFFF !important;
  border-color: #27AE60;
}
.modal-modifier .btn-success:focus:not(:active) {
  background-color: #27AE60;
}
.modal-modifier .btn-danger {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.modal-modifier .btn-danger:hover {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.modal-modifier .btn-danger:not(:disabled):not(.disabled):active,
.modal-modifier .btn-danger:not(:disabled):not(.disabled):active:focus {
  background-color: #FF4B33;
  border-color: #FF4B33;
  box-shadow: none;
}
.modal-modifier .btn-danger.disabled,
.modal-modifier .btn-danger:disabled {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.modal-modifier .btn-danger:focus:not(:active) {
  background-color: #FF4B33;
}
.dataTables_wrapper {
  width: 100%;
}
.dataTables_wrapper .page-item.active .page-link {
  color: #FFFFFF !important;
  background-color: #2F80ED !important;
}
.dataTables_wrapper input.form-control,
.dataTables_wrapper select.form-control {
  height: auto !important;
}
.dataTables_wrapper > .row:nth-child(2) {
  overflow-x: scroll;
}
table.dataTable {
  width: 100% !important;
}
table.dataTable thead th {
  white-space: nowrap !important;
}
table.dataTable thead th:first-child {
  width: 10% !important;
}
table.dataTable thead th:nth-child(2) {
  width: 50% !important;
}
table.dataTable td {
  white-space: normal !important;
}
table.dataTable.table-sm .sorting:before,
table.dataTable.table-sm .sorting_asc:before,
table.dataTable.table-sm .sorting_desc:before {
  right: 1.2em;
  font-size: 14px;
}
table.dataTable.table-sm .sorting:after,
table.dataTable.table-sm .sorting_asc:after,
table.dataTable.table-sm .sorting_desc:after {
  font-size: 14px;
}
.loading-icon {
  z-index: 999999;
}
.modal {
  z-index: 9992;
}
input.form-control,
select.form-control {
  height: 35px !important;
}
.input-error {
  border-color: #FF4B33 !important;
}
.input-success {
  border-color: #27AE60 !important;
}
.bootstrap-select {
  width: 100% !important;
}
.bootstrap-select .dropdown-toggle {
  font-size: 14px;
  box-shadow: 0 2px 4px #ECECFB;
  background-color: #FFFFFF !important;
  color: #212121 !important;
  border-color: #B4CDDE !important;
  height: 35px;
}
.bootstrap-select .dropdown-toggle:hover,
.bootstrap-select .dropdown-toggle:focus {
  color: #212121 !important;
  outline: 0 !important;
}
.bootstrap-select .dropdown-toggle::after {
  vertical-align: 0.5em !important;
}
.bootstrap-select .filter-option {
  font-weight: normal;
  padding-left: 4px;
}
.bootstrap-select .bs-searchbox input.form-control {
  height: 30px !important;
  background-color: #f9f9f9;
}
.bootstrap-select .dropdown-menu.open {
  background-color: #e6eff4;
}
.bootstrap-select .dropdown-menu .inner {
  background-color: #e6eff4;
  padding: 4px 8px !important;
  max-height: 300px !important;
}
.bootstrap-select .dropdown-menu li.no-results {
  border-radius: 3px;
  padding: 3px 10px;
  margin: 0;
  font-size: 13px;
}
.bootstrap-select .dropdown-menu li a {
  border-radius: 3px;
  margin-bottom: 2px;
  padding: 3px 10px;
  font-size: 13px;
}
.bootstrap-select .dropdown-menu li a:hover {
  background-color: #20c99740;
}
.bootstrap-select .dropdown-menu li.selected a {
  color: #FFFFFF;
  font-weight: 600;
  background-color: #20C997;
  border-radius: 3px;
}
.bootstrap-select .dropdown-menu li.selected a:hover {
  background-color: #FF4B33;
}
.readonly-datepicker {
  border-color: #B4CDDE !important;
}
#createPdfBook .container {
  width: 60% ;
}
#createPdfBook .lightblue_bg {
  background-color: #F3F7FA;
  box-shadow: 0 2px 10px #e9eef5;
  padding: 0.1rem;
}
#createPdfBook .nav-pills {
  box-shadow: 0 2px 4px #b9b9c8;
}
#createPdfBook #UploadSizeAlert,
#createPdfBook #UploadPdfFile,
#createPdfBook #notesUploadAlert {
  padding-left: 0px !important;
}
#createPdfBook h4 {
  font-family: 'Quicksand', sans-serif !important;
  color: black !important;
}
#createPdfBook .form-group label {
  font-weight: 500;
  font-size: 12px;
  font-family: 'Quicksand', sans-serif !important;
}
#createPdfBook input {
  font-family: 'Quicksand', sans-serif !important;
}
#createPdfBook button {
  font-family: 'Quicksand', sans-serif !important;
  margin-bottom: 1rem !important;
}
#createPdfBook .invalid-feedback {
  font-size: 15px !important;
}
#createPdfBook .form-fields {
  padding-top: 2rem;
  padding-left: 2rem;
  padding-right: 2rem;
}
#createPdfBook .form-control:focus {
  box-shadow: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #createPdfBook #actionButtons {
    display: flex;
    padding-left: 0px !important;
    margin: 0.25rem;
  }
  #createPdfBook #cancelBtn {
    margin-right: 0.25rem;
  }
  #createPdfBook .container {
    width: 100% ;
  }
}
.videoExplanation .container-fluid {
  padding: 5rem;
  background-color: #F3F7FA;
}
.videoExplanation .staticContainer {
  border: 1px solid #dee2e6 !important;
  box-shadow: 0 2px 10px #e9eef5;
}
.videoExplanation label {
  font-weight: 500;
  font-size: 12px;
  font-family: 'Quicksand', sans-serif !important;
}
.videoExplanation input {
  border: 1px solid #B4CDDE;
}
.videoExplanation textarea {
  height: 120px !important;
  resize: unset !important;
  border: 1px solid #B4CDDE;
}
.videoExplanation .notes {
  padding: 0 !important;
}
.videoExplanation .cke_chrome {
  border: 1px solid #B4CDDE !important;
}
.videoExplanation .buttonsVideos {
  padding: 1.1em 3em 5px 16em;
  margin-top: 2rem;
}
.videoExplanation .buttonsVideos .btn {
  text-transform: uppercase;
  font-size: 13px;
  color: #007bff !important;
  border: 1px solid #007bff !important;
  background-color: #FFFFFF !important;
}
.videoExplanation .buttonsVideos .finishBtn {
  background: #27AE60 !important;
  box-shadow: 0 2px 4px #0000001a;
  border-color: #27AE60 !important;
  color: #FFFFFF !important;
}
.videoExplanation .points {
  padding-top: 1rem;
}
.videoExplanation #containerRow {
  padding: 3rem;
}
.videoExplanation .pagenumber-green {
  height: 30px;
  width: 35px;
  padding: 4px;
  float: left;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-bottom: 10px;
  border-width: 2px;
  font-weight: bold;
  border-color: #27AE60;
  color: #27AE60;
  background-color: white !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .videoExplanation .container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 3rem;
  }
  .videoExplanation .btn {
    margin-bottom: 10px;
    width: 100%;
  }
  .videoExplanation .mobile_column_swap {
    flex-direction: column-reverse;
  }
}
.qandaCreator .container-fluid {
  background-color: #F3F7FA;
}
.qandaCreator #resourceName {
  font-size: 15px;
}
.qandaCreator #static-content {
  padding: 3rem !important;
  box-shadow: 0 2px 10px #e9eef5;
}
.qandaCreator label {
  font-weight: 500;
  font-size: 12px;
  font-family: 'Quicksand', sans-serif !important;
}
.qandaCreator .qanda {
  padding: 0.1em 3em 5px 1em;
}
.qandaCreator .qanda1 {
  padding: 0.1em 2em 1px 2em;
}
.qandaCreator .qandaAlert {
  padding: 1rem;
}
.qandaCreator .cke_chrome {
  border: 1px solid #B4CDDE !important;
}
.qandaCreator .buttonsQandA {
  margin-top: 2rem;
}
.qandaCreator .buttonsQandA .btn {
  text-transform: uppercase;
  font-size: 13px;
  color: #007bff !important;
  border: 1px solid #007bff !important;
  background-color: #FFFFFF !important;
}
.qandaCreator .buttonsQandA .finishBtn {
  background: #27AE60 !important;
  box-shadow: 0 2px 4px #0000001a;
  border-color: #27AE60 !important;
  color: #FFFFFF !important;
}
.qandaCreator .pagenumber-green {
  height: 30px;
  width: 35px;
  padding: 4px;
  float: left;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-bottom: 10px;
  border-width: 2px;
  font-weight: bold;
  border-color: #27AE60;
  color: #27AE60;
  background-color: white !important;
}
.qandaCreator #alertbox {
  width: 100%;
  margin-left: 12px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .qandaCreator .container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 3rem;
  }
  .qandaCreator .btn {
    margin-bottom: 10px;
    width: 90%;
  }
  .qandaCreator .mobile_column_swap {
    flex-direction: column-reverse;
  }
}
.notesCreator .container {
  padding: 5rem;
}
.notesCreator #static-content {
  background-color: #F3F7FA;
  padding: 3rem !important;
  border: 1px solid #dee2e6 !important;
  box-shadow: 0 2px 10px #e9eef5;
}
.notesCreator #static-content h4 {
  font-size: 1.5rem !important;
}
.notesCreator #subType {
  box-shadow: 0 2px 4px #ececfb;
  color: #212121;
  border-color: #B4CDDE !important;
}
.notesCreator label {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 12px;
  font-family: 'Quicksand', sans-serif !important;
}
.notesCreator .btn {
  width: 15%;
}
.notesCreator input {
  font-size: 15px;
}
.notesCreator .cke_chrome {
  border: 1px solid #B4CDDE !important;
}
.notesCreator .notes {
  padding: 0;
}
.notesCreator .alertRow {
  padding: 0 1rem 0 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .notesCreator .container {
    padding: 0rem;
  }
  .notesCreator .container-fluid {
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 3rem;
  }
  .notesCreator .btn {
    margin-bottom: 10px;
    width: 100%;
  }
  .notesCreator #alertbox {
    margin-right: 0px;
  }
}
.quizCreatorBulkInput .main {
  background-color: #F3F7FA !important;
  padding: 5rem;
  border: 1px solid #dee2e6 !important;
  box-shadow: 0 2px 10px #e9eef5 !important;
}
.quizCreatorBulkInput .main #quizcreatorbulk {
  padding: 0 !important;
}
.quizCreatorBulkInput .main #quizcreatorbulk .row {
  padding: 0 !important;
}
.quizCreatorBulkInput .main textarea {
  height: 120px !important;
  resize: unset !important;
  border: 1px solid #B4CDDE;
}
.quizCreatorBulkInput .main .btn {
  width: 15%;
}
.quizCreatorBulkInput .main .red {
  margin-left: 5px !important;
}
.quizCreatorBulkInput .main .form-group input {
  background: #fff;
  padding: 0.375rem 0.75rem;
  border-radius: 5px;
  border: 1px solid #B4CDDE;
  box-shadow: 0 2px 4px #ececfb;
  font-size: 14px;
  width: 100%;
}
.quizCreatorBulkInput .main input[type="radio"]:checked + label {
  background-color: transparent !important;
}
.quizCreatorBulkInput .main .form-check-inline {
  margin-right: 1rem !important;
}
.quizCreatorBulkInput .main .form-check-inline .form-check-input {
  margin-right: 0 !important;
}
.quizCreatorBulkInput .main .alertRow {
  margin: 0;
}
.quizCreatorBulkInput .main .alertRow .alert {
  margin: 0;
}
.quizCreatorBulkInput .main .radioQuestion {
  display: flex;
}
.quizCreatorBulkInput .main .radioQuestion p {
  margin: 0 1rem 0 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .quizCreatorBulkInput .main {
    padding: 3rem !important;
  }
  .quizCreatorBulkInput .btn {
    width: 100% !important;
  }
  .quizCreatorBulkInput .radioQuestion {
    display: block !important;
  }
}
.quizCreatorBulk .quizDirection {
  padding: 3rem;
}
.quizCreatorBulk .quizDirection .main {
  margin-left: 0 !important;
  margin-right: 0 !important;
  background-color: #F3F7FA !important;
  border: 1px solid #dee2e6 !important;
  box-shadow: 0 2px 10px #e9eef5 !important;
  padding: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .quizCreatorBulk .quizDirection {
    padding: 1rem;
  }
}
.studySet {
  padding: 3rem;
  background-color: #F3F7FA !important;
  border: 1px solid #dee2e6 !important;
  box-shadow: 0 2px 10px #e9eef5 !important;
}
.studySet .study-set-main,
.studySet .study-set-textarea {
  background-color: #FFFFFF !important;
}
.studySet .invalid-feedback {
  margin-top: 0 !important;
  font-size: 14px;
}
.studySet .study-set-wrapper-continer {
  margin-top: 10px;
}
.studySet #revisionTitle {
  width: 100vh !important;
}
.studySet .termAndDef {
  margin-left: 0 !important;
}
.studySet .termAndDef:hover {
  border-color: #6C757D !important;
  cursor: pointer;
}
.studySet .study-set-item {
  margin: 0 !important;
  padding: 0 !important;
}
.studySet .term-counter:before {
  left: 0 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .studySet .studySet {
    padding: 1rem !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .studySet #revisionTitle {
    width: 40vh !important;
  }
}
@media (max-width: 575.98px) {
  .studySet .termAndDef {
    width: 40vh !important;
  }
  .studySet .form-group {
    margin-bottom: 0 !important;
  }
  .studySet .term-counter:before {
    bottom: 20px !important;
  }
  .studySet .add-study-card-btn-wrapper {
    flex-wrap: wrap !important;
    display: flex !important;
  }
  .studySet .add-study-card-btn {
    margin-right: 0 !important;
    width: 100% !important;
    margin-bottom: 1rem !important;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .studySet .termAndDef {
    width: 20vh !important;
  }
}
.sage-body h4.whitetext {
  margin-top: 2rem !important;
}
.sage-body .studySet {
  width: 100% !important;
}
.sage-body .studySet .study-set-item {
  padding: 16px 24px 16px 48px !important;
}
.sage-body .studySet .invalid-feedback {
  color: #FF4B33;
  margin-bottom: 20px;
}
.mergeQuizzes .mainText {
  text-align: left;
}
.mergeQuizzes .table-container {
  background-color: #F3F7FA;
  padding: 3rem !important;
  border: 1px solid #dee2e6 !important;
  box-shadow: 0 2px 10px #e9eef5;
}
.mergeQuizzes table {
  width: 100%;
  border: 1px solid #dee2e6;
}
.mergeQuizzes table td,
.mergeQuizzes table th {
  border: 1px solid #dee2e6;
  border-bottom-width: 2px !important;
}
.mergeQuizzes table thead {
  background-color: #F79420 !important;
}
.mergeQuizzes table thead th {
  border: 2px solid #dee2e6 !important;
}
.mergeQuizzes table tbody td {
  text-align: center;
}
.mergeQuizzes .submitBtn {
  width: 15%;
  color: #ffffff !important;
  background-color: #F79420 !important;
  border-color: #F79420 !important;
  cursor: pointer;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .mergeQuizzes .submitBtn {
    width: 100% !important;
  }
}
/* Admin Common Styles */
/* Book Create Page Styles */
@keyframes blink {
  0% {
    opacity: 0.2;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0.2;
  }
}
.new_book_create {
  min-height: 75vh;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .new_book_create {
    min-height: 80vh !important;
  }
}
.new_book_create #pills-tab {
  position: relative;
}
.new_book_create #pills-tab #bookSaving {
  position: absolute;
  top: 0;
  right: 20px;
}
@media (min-width: 576px) and (max-width: 767.98px), (max-width: 575.98px) {
  .new_book_create #pills-tab #bookSaving {
    top: -50px;
  }
}
.new_book_create #pills-tab #bookSaving p {
  font-size: 15px;
  color: #27AE60;
  font-weight: 600;
  margin: 0;
}
.new_book_create #pills-tab #bookSaving span {
  color: #27AE60;
  font-size: 30px;
  animation-name: blink;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-fill-mode: both;
}
.new_book_create #pills-tab #bookSaving span:nth-child(2) {
  animation-delay: 0.2s;
}
.new_book_create #pills-tab #bookSaving span:nth-child(3) {
  animation-delay: 0.4s;
}
.new_book_create div.form-group label {
  font-weight: 500;
  font-size: 12px;
  margin-bottom: 0.2rem;
  text-transform: uppercase;
  width: 100%;
}
.new_book_create div.form-group input,
.new_book_create div.form-group select,
.new_book_create div.form-group textarea {
  font-size: 14px;
  box-shadow: 0 2px 4px #ececfb;
  color: #212121;
  border-color: #B4CDDE;
}
.new_book_create div.form-group .clickable-label {
  width: auto !important;
  height: auto !important;
  display: inline-flex;
  align-items: center;
  text-transform: unset;
  line-height: normal;
  margin-right: 15px;
  cursor: pointer;
}
.new_book_create div.form-group .clickable-label input {
  margin-right: 5px;
  margin-bottom: 0;
  cursor: pointer;
}
@media (min-width: 576px) and (max-width: 767.98px), (max-width: 575.98px) {
  .new_book_create .book_details_info h5.create-book-title {
    width: 100%;
  }
}
.new_book_create .book_details_info div.form-group input.input-error,
.new_book_create .book_details_info div.form-group textarea.input-error,
.new_book_create .book_details_info div.form-group select.input-error {
  border-color: #FF4B33 !important;
}
.new_book_create .book_details_info div.form-group input.input-success,
.new_book_create .book_details_info div.form-group textarea.input-success,
.new_book_create .book_details_info div.form-group select.input-success {
  border-color: #27AE60 !important;
}
.new_book_create .book_details_info div.form-group .clickable-label {
  width: auto !important;
  height: auto !important;
}
.new_book_create .author-select .dropdown-toggle.input-error {
  border-color: #FF4B33 !important;
}
.new_book_create .author-select .dropdown-toggle.input-success {
  border-color: #27AE60 !important;
}
.new_book_create .author-select select.selectpicker {
  display: block !important;
  opacity: 0;
  left: 0;
  width: 100% !important;
  height: 35px !important;
}
.new_book_create .ebook-title .invalid-feedback,
.new_book_create .publisher-select .invalid-feedback,
.new_book_create .author-select .invalid-feedback {
  margin-bottom: 10px;
  margin-top: -10px;
}
.new_book_create .coverimageWrapper h4 {
  font-weight: 500;
}
.new_book_create #bookcover {
  justify-content: center;
}
.new_book_create #bookcover i,
.new_book_create #bookcover span {
  color: #6C757D;
}
.new_book_create #bookcover #filelabel1,
.new_book_create #bookcover #filelabel2 {
  right: 0;
  left: 0;
}
.new_book_create #bookcover .smallText {
  justify-content: center;
}
.new_book_create .subject-tags-select option {
  border-radius: 2px;
  padding: 2px 5px;
  margin-bottom: 1px;
}
.new_book_create .subject-tags-select option:checked {
  background-color: #27AE60;
  color: #FFFFFF;
}
.new_book_create #addedtags tbody tr td {
  font-size: 14px;
}
.new_book_create #addedtags tbody tr td a {
  font-size: 12px;
}
.new_book_create #addedtags tbody tr td a i.delete {
  color: #FF4B33;
}
.new_book_create #displayEpubUpload #epubFileInput {
  height: 40px !important;
}
.new_book_create #displayEpubUpload #epubUploadButton {
  height: 38px;
}
.new_book_create .list-chapters i {
  color: #FF4B33;
}
.new_book_create #publisherrors {
  font-size: 14px;
}
.new_book_create .bukerror {
  white-space: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .new_book_create .add-contents {
    margin-top: 3rem;
  }
}
.createBook-modal .btn-close {
  position: absolute;
  right: 5px;
  top: 5px;
  cursor: pointer;
}
.createBook-modal #resId,
.createBook-modal #readResId {
  border-color: #B4CDDE;
}
.createBook-modal #file3,
.createBook-modal #file10 {
  font-size: 12px;
}
.createBook-modal #resource3Form i {
  font-size: 13px;
}
.createBook-modal #render_as_pdf.d-flex {
  width: 100px;
}
.createBook-modal #render_as_pdf label {
  cursor: pointer;
  text-align: center;
}
.createBook-modal #allow_pdf_download .d-flex {
  width: 100px;
}
.createBook-modal #allow_pdf_download label {
  cursor: pointer;
  text-align: center;
  border: 1px solid #007bff;
  border-radius: 4px;
  padding: 5px;
}
.createBook-modal #allow_pdf_download label.pdf_download_yes {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.createBook-modal #allow_pdf_download label.pdf_download_no {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -4px;
}
#chapter-resources tr th:nth-child(5)::before {
  display: none;
}
#chapter-resources tr th:nth-child(5)::after {
  display: none;
}
#chapter-resources tr td:nth-child(4) {
  border-right-width: 0;
}
#chapter-resources tr td:nth-child(5) {
  border-left-width: 1px;
}
#chapter-resources tr td a.btn {
  padding: 3px 8px;
}
#chapter-resources tr td a.btn.btn-primary,
#chapter-resources tr td a.btn.btn-secondary {
  border-color: #2F80ED !important;
  color: #2F80ED !important;
}
#chapter-resources tr td a.btn.btn-danger {
  border-color: #FF4B33 !important;
  color: #FF4B33 !important;
}
#chapter-resources.dataTable.table-sm > thead > tr > th {
  padding-right: 30px;
  font-size: 14px;
}
#chapter-resources.dataTable.table-sm > thead > tr > th:before,
#chapter-resources.dataTable.table-sm > thead > tr > th:after {
  top: 7px;
}
.wonderslate_main .new_book_create #bookSaving {
  top: 10px !important;
}
@media (min-width: 576px) and (max-width: 767.98px), (max-width: 575.98px) {
  .wonderslate_main .new_book_create #bookSaving {
    top: -40px !important;
  }
}
.evidya .open > .dropdown-menu {
  display: block;
}
.evidya .new_book_create .invalid-feedback {
  font-weight: normal !important;
  line-height: normal;
}
