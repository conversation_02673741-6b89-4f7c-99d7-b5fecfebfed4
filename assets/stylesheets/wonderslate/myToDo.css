.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif;
  font-style: italic;
  font-size: 14px;
  color: #C13B87 !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif;
  font-style: italic;
  font-size: 14px;
  color: #C13B87 !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif;
  font-style: italic;
  font-size: 14px;
  color: #C13B87 !important;
}
.todo_section .page_title h3 {
  font-weight: bold;
}
.todo_section .todo_filter h4 {
  font-size: 16px;
}
.todo_section .todo_filter a {
  font-size: 12px;
  border-color: #9B51E0;
  color: #9B51E0;
  background-color: #ffffff;
  margin-left: 0.5rem;
  border-radius: 50px;
  padding: 0.25rem 1rem;
}
.todo_section .todo_filter a:hover {
  border-color: #AE3691;
  background-color: #AE3691;
  color: #ffffff;
}
.todo_section .todo_filter a:focus {
  color: #ffffff !important;
  border-color: #AE3691 !important;
  background-color: #AE3691 !important;
}
.todo_section .todo_filter a:focus:hover {
  border-color: #AE3691 !important;
  background-color: #AE3691 !important;
  color: #ffffff !important;
}
.todo_section .todo_filter a.active {
  border-color: #AE3691;
  background-color: #AE3691;
  color: #ffffff;
}
.todo_section .todo_lists .empty_todo_list p {
  color: #808080;
}
.todo_section .todo_lists .card {
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}
.todo_section .todo_lists .card .custom-checkbox {
  position: absolute;
  top: 0;
}
.todo_section .todo_lists .card .custom-checkbox .custom-control-label::before {
  top: 0;
  background-color: transparent;
  border: 2px solid rgba(68, 68, 68, 0.48);
  width: 1.3em;
  height: 1.3rem;
  border-radius: 3px;
}
.todo_section .todo_lists .card .custom-checkbox .custom-control-label::after {
  top: 0;
  width: 1.3rem;
  height: 1.3rem;
  border-radius: 3px;
}
.todo_section .todo_lists .card .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  border-color: transparent;
}
.todo_section .todo_lists .card div.todo_info {
  padding-right: 4rem;
}
.todo_section .todo_lists .card div.todo_info p {
  margin-bottom: 0.75rem;
}
.todo_section .todo_lists .card div.todo_info em {
  color: rgba(0, 0, 0, 0.6);
}
.todo_section .todo_lists .card div.todo_info .high,
.todo_section .todo_lists .card div.todo_info .High {
  color: #B72319;
}
.todo_section .todo_lists .card div.todo_info .medium,
.todo_section .todo_lists .card div.todo_info .Medium {
  color: #F79420;
}
.todo_section .todo_lists .card div.todo_info .low,
.todo_section .todo_lists .card div.todo_info .Low {
  color: #46B520;
}
.todo_section .todo_lists .card del.todo_info {
  padding-right: 4rem;
  color: rgba(68, 68, 68, 0.48);
}
.todo_section .todo_lists .card del.todo_info p {
  margin-bottom: 0.75rem;
}
.todo_section .todo_lists .card del.todo_info em {
  color: rgba(68, 68, 68, 0.48);
}
.todo_section .todo_lists .card del.todo_info .high,
.todo_section .todo_lists .card del.todo_info .High {
  color: rgba(68, 68, 68, 0.48);
}
.todo_section .todo_lists .card del.todo_info .medium,
.todo_section .todo_lists .card del.todo_info .Medium {
  color: rgba(68, 68, 68, 0.48);
}
.todo_section .todo_lists .card del.todo_info .low,
.todo_section .todo_lists .card del.todo_info .Low {
  color: rgba(68, 68, 68, 0.48);
}
.todo_section .add_todo .add_todo_btn {
  border-radius: 50px;
  height: 75px;
  width: 75px;
  display: flex;
  flex-wrap: wrap;
  font-size: 12px;
  line-height: 12px;
  font-weight: 300;
  align-items: center;
  justify-content: center;
  flex-direction: column-reverse;
  background: radial-gradient(91.33% 91.33% at 0% 20.67%, #6F58D8 0%, #7636B5 100%) !important;
  box-shadow: 0px 0px 10px rgba(95, 95, 95, 0.15);
  border-color: #7636B5 !important;
}
.todo_section .add_todo .add_todo_btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(118, 54, 181, 0.4) !important;
}
.todo_section .add_todo .add_todo_btn:active:focus {
  box-shadow: 0 0 0 0.2rem rgba(118, 54, 181, 0.4) !important;
}
.todo_section .add_todo .add_todo_btn:before {
  display: none;
}
.todo_section .add_todo .add_todo_btn img {
  margin-bottom: 5px;
}
.todo_section .add_todo .add_todo_form {
  z-index: 99;
  min-width: 400px;
}
.todo_section .add_todo .add_todo_form h4 {
  font-weight: bold;
}
.todo_section .add_todo .add_todo_form input {
  border-color: #9B51E0;
  font-size: 14px;
}
.todo_section .add_todo .add_todo_form .input-group .input-group-text {
  background: none;
}
.todo_section .add_todo .add_todo_form .input-group .calendar_input {
  border-bottom: 1px solid #9B51E0 !important;
  font-weight: 600;
}
.todo_section .add_todo .add_todo_form .input-group .calendar_input:focus {
  box-shadow: none;
}
.todo_section .add_todo .add_todo_form .input-group .time_input {
  border-bottom: 1px solid #9B51E0 !important;
  border-radius: 0;
}
.todo_section .add_todo .add_todo_form .input-group .time_input:focus {
  box-shadow: none;
}
.todo_section .add_todo .add_todo_form .input-group .priority_input {
  border-bottom: 1px solid #9B51E0 !important;
  border-radius: 0;
  font-size: 14px;
  font-style: italic;
}
.todo_section .add_todo .add_todo_form .input-group .priority_input:focus {
  box-shadow: none;
}
.todo_section .add_todo .add_todo_form .submit_btn {
  border-color: #9B51E0;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  color: #ffffff;
  font-weight: 300;
  height: 50px;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
.todo_section .add_todo .add_todo_form .submit_btn:hover {
  box-shadow: 0 0.2rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.todo_section .bootstrap-datetimepicker-widget .btn-primary {
  border: none !important;
  width: 40px;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
}
.todo_section .bootstrap-datetimepicker-widget .btn-primary:hover {
  box-shadow: 0 0.2rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.todo_section #recordsData .edit-access {
  position: absolute;
  right: 20px;
}
.todo_section #recordsData .edit-access a {
  display: inline-block;
}
.todo_section #recordsData .edit-access a.delete-icon {
  margin-right: 10px;
}
.todo_section #recordsData .edit-access a.delete-icon:hover i {
  color: #ff0000;
}
.todo_section #recordsData .edit-access a.edit-icon:hover i {
  color: #0A72E8;
}
.todo_section #recordsData .edit-access a i {
  font-size: 22px;
  color: rgba(68, 68, 68, 0.48);
}
.todo_section .calendar_filter {
  width: 100%;
}
.todo_section .calendar_filter .datepicker {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  font-family: 'Poppins', sans-serif;
}
.todo_section .calendar_filter .datepicker .table-condensed {
  width: 100%;
  font-size: 14px;
}
.todo_section .calendar_filter .datepicker .datepicker-days .prev {
  font-size: 24px;
  color: #9A309B;
}
.todo_section .calendar_filter .datepicker .datepicker-days .next {
  font-size: 24px;
  color: #9A309B;
}
.todo_section .calendar_filter .datepicker .datepicker-days .datepicker-switch {
  height: 40px;
  cursor: default;
  color: #9A309B;
}
.todo_section .calendar_filter .datepicker .datepicker-days .datepicker-switch:hover {
  background: none;
}
.todo_section .calendar_filter .datepicker .datepicker-days .dow {
  font-size: 12px;
  font-weight: normal;
  color: #949494;
  padding: 10px;
}
.todo_section .calendar_filter .datepicker .datepicker-days .day {
  width: 40px;
  height: 40px;
  border-radius: 50px;
  font-weight: 500;
}
.todo_section .calendar_filter .datepicker .datepicker-days .day.active {
  background: radial-gradient(100% 100% at 13.51% 0%, #972F9C 0%, #C53C85 100%);
}
.todo_section .calendar_filter .datepicker .datepicker-days .day.active:hover {
  color: white;
}
.todo_section .calendar_filter .datepicker .datepicker-days .day:hover {
  color: #cb3e82;
}
.todo_section .calendar_filter .datepicker .datepicker-days .old.day,
.todo_section .calendar_filter .datepicker .datepicker-days .new.day {
  color: #DDDDDD;
  font-weight: normal;
}
.todo_section .todo_calendar_filter {
  display: flex;
  flex: 1 0 0;
}
.todo_section #addToDoModal,
.todo_section #editToDoModal {
  z-index: 9999;
}
@media only screen and (max-width: 992px) {
  .todo_section .todo_filter {
    margin-bottom: 0 !important;
    margin-top: 20px;
  }
  .todo_section .add_todo .add_todo_form {
    min-width: auto;
  }
}
@media only screen and (max-width: 768px) {
  .todo_section .page_title {
    background: #ffffff;
    border-radius: 0px 0px 10px 10px;
    margin-bottom: 15px;
  }
  .todo_section .page_title h3 {
    font-size: 22px;
  }
  .todo_section .flex-md-row-reverse {
    margin-right: 0;
    margin-left: 0;
  }
  .todo_section .todo_filter h4 {
    font-size: 14px;
  }
  .todo_section .todo_filter a {
    font-size: 10px;
    margin-left: 0.3rem;
    padding: 0.2rem 0.7rem;
  }
  .todo_section #recordsData .edit-access {
    right: 10px;
    top: 10px;
  }
  .todo_section #recordsData .edit-access a i {
    font-size: 20px;
  }
  .todo_section #recordsData .edit-access a.delete-icon {
    margin-right: 5px;
  }
  .todo_section .todo_lists .card .custom-checkbox .custom-control-label:before,
  .todo_section .todo_lists .card .custom-checkbox .custom-control-label:after {
    width: 1.2rem;
    height: 1.2rem;
  }
  .todo_section .todo_lists .card .todo_info {
    padding-right: 2.8rem !important;
  }
  .todo_section .todo_lists .card .todo_info p {
    margin-bottom: 0.5rem !important;
    font-size: 13px;
  }
}
.todo_section .todo_mobile_action {
  background: radial-gradient(94.93% 433.83% at 1.6% 0%, #D13F7F 0%, #8129A7 100%);
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px;
  bottom: 5px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 98%;
  height: 65px;
  position: fixed;
  z-index: 9991;
  padding: 15px 20px;
}
.todo_section .todo_mobile_action a.add_todo_btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  color: #9A309B;
  border-color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  height: 36px;
  border-radius: 9px;
  min-width: 230px;
}
.todo_section .todo_mobile_action a.add_todo_btn i {
  font-size: 16px;
  font-weight: bold;
}
.todo_section .todo_mobile_action a.add_todo_btn:focus {
  background: #ffffff !important;
}
.todo_section .todo_mobile_action span i {
  color: #ffffff;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker .table-condensed .fa {
  font-family: "FontAwesome" !important;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker .table-condensed a:hover {
  background-color: transparent;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker .table-condensed a:hover .fa {
  color: #cb3e82;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker .table-condensed .timepicker-hour,
.todo_section .bootstrap-datetimepicker-widget .timepicker .table-condensed .timepicker-minute {
  border: 1px solid #ddd;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker .table-condensed .timepicker-hour:hover,
.todo_section .bootstrap-datetimepicker-widget .timepicker .table-condensed .timepicker-minute:hover {
  color: #cb3e82;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker-hours .hour:hover {
  color: #cb3e82;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker-minutes .minute:hover {
  color: #cb3e82;
}
.todo_section #mobile_calendar #selectedMonthYear {
  font-weight: bold;
}
.todo_section #mobile_calendar .material-icons {
  color: #7F28A8;
}
.sweet-overlay {
  background-color: rgba(0, 0, 0, 0.7) !important;
}
.sweet-alert h2 {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-flex !important;
  justify-content: center;
  font-size: 26px !important;
  margin: 0px 0 10px !important;
  color: transparent !important;
}
@media only screen and (max-width: 768px) {
  .sweet-alert h2 {
    font-size: 24px !important;
  }
}
.sweet-alert p {
  color: #212121 !important;
  font-size: 15px !important;
}
@media only screen and (max-width: 768px) {
  .sweet-alert p {
    font-size: 14px !important;
  }
}
.sweet-alert button {
  font-size: 15px !important;
  font-weight: normal !important;
}
@media only screen and (max-width: 768px) {
  .sweet-alert button {
    font-size: 14px !important;
    padding: 7px 20px !important;
    margin: 15px 5px 0px 5px !important;
  }
}
