.ws-input {
  position: relative;
  padding: 15px 0 0;
  margin-top: 10px;
}
.ws-input label.ws-label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #848484;
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ws-input label.ws-label {
    width: 100%;
  }
}
.ws-input input {
  color: #212121;
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ws-input input {
    width: 100%;
  }
}
.ws-input input.form-controls {
  width: 100%;
  border: 0;
  border-bottom: 1px solid #848484;
  border-radius: 0;
  outline: 0;
  font-size: 14px;
  padding: 7px 0;
  background: transparent;
  transition: border-color 0.2s;
}
.ws-input input.form-controls::placeholder {
  color: transparent;
}
.ws-input input.form-controls:placeholder-shown ~ label.ws-label {
  font-size: 12px;
  cursor: text;
  top: 20px;
  font-style: italic;
}
.ws-input input:focus {
  padding-bottom: 6px;
  border-bottom: 2px solid #000000b3;
}
.ws-input input:focus ~ label.ws-label {
  position: absolute;
  top: 0;
  display: block;
  transition: 0.2s;
  font-size: 12px;
  color: #000000b3;
}
.ws-input input:required,
.ws-input input:invalid {
  box-shadow: none;
}
.switch {
  display: flex;
  position: absolute;
  right: 0;
  top: 0;
}
.switch.reset-switch {
  position: static;
  display: flex;
  align-items: center;
  margin-left: 3rem;
}
.switch.reset-switch label {
  margin-bottom: 0 !important;
}
.public-text {
  margin-right: 10px;
  display: block;
  font-style: italic;
  font-size: 12px;
  color: #000000b3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .public-text {
    color: #FFFFFF;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .setname-wrapper .public-text {
    color: #212121;
  }
}
.toggleSwitch {
  /* Rounded sliders */
}
.toggleSwitch .switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 15px;
}
.toggleSwitch .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.toggleSwitch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch .slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 1px;
  background-color: #FFFFFF;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch input:checked + .slider {
  background: #27AE60;
}
.toggleSwitch input:focus + .slider {
  box-shadow: 0 0 1px #17A2B8;
}
.toggleSwitch input:checked + .slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}
.toggleSwitch .slider.round {
  border-radius: 10px;
}
.toggleSwitch .slider.round:before {
  border-radius: 50%;
}
::-webkit-scrollbar {
  width: 0;
  /* Remove scrollbar space */
  background: transparent;
  /* Optional: just make scrollbar invisible */
}
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
#htmlreadingcontent {
  margin-top: 0;
}
.btn {
  font-size: 12px;
  font-weight: 400;
}
.btn:hover {
  transition: all 0.5s;
}
.flashcards {
  position: relative;
  width: 500px;
  height: 300px;
  margin-top: 2rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .flashcards {
    height: 400px;
    width: 250px;
  }
}
.flashcards .flash-card {
  position: absolute;
  width: 500px;
  height: 300px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
  -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
  -moz-box-shadow: 0 0 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .flashcards .flash-card {
    height: 400px;
    width: 250px;
  }
}
.flashcards .flash-card:last-child {
  margin-bottom: 4rem;
}
.flashcards .slide-left {
  z-index: 99999;
  animation-name: slideLeft;
  animation-duration: 0.2s;
  transform-origin: bottom left;
  transform-style: preserve-3D;
}
.flashcards .slide-right {
  z-index: 99999;
  animation-name: slideRight;
  animation-duration: 0.2s;
  transform-origin: bottom left;
  transform-style: preserve-3D;
}
@keyframes slideLeft {
  0% {
    opacity: 0.9;
    left: 0;
    top: 0;
    transform: rotate(-2deg);
  }
  100% {
    opacity: 0.5;
    left: -100px;
    top: 0;
    transform: rotate(-3deg);
  }
}
@keyframes slideRight {
  0% {
    opacity: 0.5;
    left: -100px;
    top: 0;
    transform: rotate(-3deg);
  }
  100% {
    opacity: 0.9;
    left: 0;
    top: 0;
    transform: rotate(-2deg);
  }
}
.flashcards .color-1 {
  background: #2F80ED;
}
.flashcards .color-2 {
  background: #FFC107;
}
.flashcards .color-3 {
  background: #20C997;
}
.card-previous {
  position: absolute;
  z-index: 98;
  left: 0;
  top: 50%;
  background: #FFFFFF;
  box-shadow: 0 0 4px #0000001A;
  -webkit-box-shadow: 0 0 4px #0000001A;
  -moz-box-shadow: 0 0 4px #0000001A;
  border-radius: 28px 0 0 28px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: 0;
  border: none;
}
.card-previous:hover {
  cursor: pointer;
}
.card-previous i {
  color: #2F80ED;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  font-size: 18px;
}
.card-next {
  position: absolute;
  z-index: 98;
  right: 0;
  top: 50%;
  background: #FFFFFF;
  box-shadow: 0 0 4px #0000001A;
  -webkit-box-shadow: 0 0 4px #0000001A;
  -moz-box-shadow: 0 0 4px #0000001A;
  border-radius: 28px 0 0 28px;
  width: 28px;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  outline: 0;
  border: none;
}
.card-next:hover {
  cursor: pointer;
}
.card-next i {
  color: #2F80ED;
  font-size: 18px;
}
.slide1 {
  z-index: 92;
}
.slide2 {
  z-index: 91;
  transform: rotate(1.94deg);
  display: block !important;
}
.slide3 {
  z-index: 90;
  transform: rotate(3.49deg);
  display: block !important;
}
button:focus {
  outline: 0;
}
.flip-box-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}
.flipCard {
  transform: rotateY(180deg);
}
.rotate-icon {
  transform: rotate(90deg);
  transition: transform 0.5s ease-in;
  -webkit-transition: -webkit-transform 0.5s ease-in;
}
.flip-box-front,
.flip-box-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  flex-direction: column;
  overflow: hidden;
}
.flip-box-front p,
.flip-box-back p {
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  padding: 1.7rem;
}
.flip-box-front p {
  padding: 0;
}
.flip-box-front.removeFlex,
.flip-box-back.removeFlex {
  display: table-cell;
  overflow: hidden;
}
.flip-box-front p,
.flip-box-back p {
  font-size: 12px;
}
.flip-box-front > p,
.flip-box-back > p {
  margin-top: 1rem;
}
.flip-box-front td,
.flip-box-back td {
  font-size: 12px;
  color: #FFFFFF;
}
.flip-box-front table,
.flip-box-back table {
  margin: 1rem auto 0;
}
.flip-box-back {
  background: radial-gradient(94.76% 94.76% at 7.57% 6.19%, #E9E9E9 0%, #FFFFFF 89.06%);
}
.flip-box-back p {
  color: #212121;
}
.flip-box-back td p {
  padding: 0;
}
.flip-box-front td p {
  padding: 0;
}
.flip-box-front td {
  padding: 0;
}
.flip-box-back {
  transform: rotateY(180deg);
}
.flashcard-wrapper {
  margin: 0 auto;
}
.flashcard-wrapper .setname {
  border: 1px solid #848484;
  width: 300px;
  border-top: none;
  border-left: none;
  border-right: none;
  border-radius: 0;
  padding: 5px;
}
.dots::before {
  content: '';
  background: url('../../images/ws/dots.svg') center center no-repeat;
  width: 20px;
  position: absolute;
  top: 0;
  left: -15px;
  height: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .dots::before {
    left: -10px;
  }
}
#empty.card-box {
  border: none;
}
#empty.card-box:hover .delete-card {
  display: none;
}
#empty .card-box {
  border: none;
}
#empty .card-box:hover .delete-card {
  display: none;
}
.card-box {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  padding: 10px 10px 15px;
  width: 95%;
  border: 1px solid #27AE60;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .card-box {
    width: 100%;
  }
}
.card-box:hover .delete-card {
  display: block;
}
.card-box .card-count {
  color: #848484;
  font-size: 10px;
  margin-bottom: 5px;
  display: block;
}
.card-box .cardHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-box .cardHeader p {
  color: #848484;
  font-size: 10px;
  margin-bottom: 10px;
}
.card-box .cardHeader form p {
  display: flex;
  align-items: center;
}
.card-box input {
  border: none;
  box-shadow: inset 0 0 5px #0000001A;
  -webkit-box-shadow: inset 0 0 5px #0000001A;
  -moz-box-shadow: inset 0 0 5px #0000001A;
  border-radius: 5px;
}
.card-box textarea {
  background: #FFFFFF;
  box-shadow: inset 0 0 5px #0000001A;
  -webkit-box-shadow: inset 0 0 5px #0000001A;
  -moz-box-shadow: inset 0 0 5px #0000001A;
  border-radius: 5px;
  font-size: 14px;
  font-style: italic;
  color: #444444;
  border: none;
  resize: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .card-box textarea {
    font-size: 16px;
  }
}
.revision-answer p {
  font-size: 12px;
  color: #17A2B8;
}
.revision-answer textarea {
  background: #FFFFFF;
  box-shadow: inset 0 0 5px #0000001A;
  -webkit-box-shadow: inset 0 0 5px #0000001A;
  -moz-box-shadow: inset 0 0 5px #0000001A;
  border-radius: 5px;
  font-size: 14px;
  font-style: italic;
  color: #444444;
  border: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .revision-answer textarea {
    font-size: 16px;
  }
}
.revision-question textarea {
  background: #FFFFFF;
  box-shadow: inset 0 0 5px #0000001A;
  -webkit-box-shadow: inset 0 0 5px #0000001A;
  -moz-box-shadow: inset 0 0 5px #0000001A;
  border-radius: 5px;
  font-size: 14px;
  font-style: italic;
  color: #444444;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .revision-question textarea {
    font-size: 16px;
  }
}
.add-text {
  background: transparent;
  border: none;
  color: #848484;
  font-size: 10px;
}
.add-text:focus {
  outline: 0;
}
.btn-flashcard {
  border: 1px solid #17A2B8;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  font-size: 12px;
  color: #17A2B8;
  display: flex;
  align-items: center;
  background: transparent;
}
.btn-flashcard i {
  font-size: 16px;
  margin-right: 5px;
}
.btn-flashcard.print {
  color: #000000b3;
  border: 1px solid #000000b3;
}
.btn-flashcard.print i {
  margin-left: 5px;
  color: #000000b3;
  font-size: 16px;
}
.back-flashCard {
  border: none;
  background: none;
}
.back-flashCard i {
  font-size: 18px;
  color: #848484;
}
.back-flashCard:focus {
  outline: 0;
}
.submit-button {
  display: flex;
  justify-content: center;
}
.submit-button .btn {
  border: 1px solid #000000b3;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  color: #000000b3;
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.submit-button .btn:hover {
  background: #000000b3;
  color: #FFFFFF;
}
.submit-button .btn.cancel {
  border: 1px solid #FF4B33;
  color: #FF4B33;
}
.submit-button .btn.cancel:hover {
  background: #FFFFFF;
}
.submit-buttons {
  display: flex;
  justify-content: center;
  margin: 0 auto;
}
.submit-buttons .btn {
  border: 1px solid #000000b3;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  color: #000000b3;
  margin-right: 10px;
  display: flex;
  align-items: center;
}
.submit-buttons .btn:hover {
  background: #000000b3;
  color: #FFFFFF;
}
.submit-buttons .btn.cancel {
  border: 1px solid #FF4B33;
  color: #FF4B33;
}
.submit-buttons .btn.cancel:hover {
  background: #FFFFFF;
}
.submit-buttons .btn.revise {
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
}
.add-card {
  border: 1px solid #17A2B8;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  color: #17A2B8;
  width: 85px;
}
.add-card:hover {
  background: #17A2B8;
  color: #FFFFFF;
}
.setname-wrapper p {
  display: flex;
  color: #848484;
  font-weight: 400;
  margin: 0;
  padding: 0;
  align-items: center;
  position: absolute;
  right: 0;
  top: 15px;
  font-size: 12px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .setname-wrapper p {
    right: 15px;
  }
}
.delete-card {
  background: none;
  border: none;
  display: none;
  cursor: pointer;
}
.delete-card i {
  color: darkred;
  font-size: 18px;
  line-height: 0;
}
#my-cards {
  counter-reset: section;
}
.col-md-6 .card-count::after {
  counter-increment: section;
  content: "Set " counter(section) ": ";
}
.flashcards {
  margin: 0 auto;
}
.flashcards .flash-card.active {
  z-index: 92;
}
.turn-slide {
  border: none;
  color: #2F80ED;
  border-radius: 50px;
  width: 50px;
  height: 50px;
  position: absolute;
  bottom: 25px;
  z-index: 98;
  background: #FFFFFF;
  box-shadow: 0 2px 10px #0000001A;
  -webkit-box-shadow: 0 2px 10px #0000001A;
  -moz-box-shadow: 0 2px 10px #0000001A;
  left: 45%;
  right: 45%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.turn-slide i:not(.rotate-icon) {
  transform: rotate(-90deg);
  transition: transform 0.5s ease-in;
  -webkit-transition: -webkit-transform 0.5s ease-in;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .turn-slide {
    left: 42%;
    right: 42%;
    bottom: 20px;
  }
}
#back-slider {
  background: none;
  border: none;
}
#back-slider i {
  color: #848484;
}
#back-slider:focus {
  outline: 0;
}
.edit {
  background: #2F80ED;
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  border: none;
  color: #FFFFFF;
}
.edit i {
  color: #FFFFFF;
  font-size: 15px;
  margin-right: 4px;
}
.saveCard {
  margin: 0 auto;
}
.saveCard .saveSubmit {
  background: #F79420;
  box-shadow: 0 2px 4px #0000001A;
  -webkit-box-shadow: 0 2px 4px #0000001A;
  -moz-box-shadow: 0 2px 4px #0000001A;
  border-radius: 4px;
  color: #FFFFFF;
  font-size: 14px;
  height: 45px;
  padding: 10px 25px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}
#successModal .modal-header {
  border: none;
}
#successModal .modal-footer {
  border: none;
}
#successModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#successModal .modal-body {
  text-align: center;
}
#successModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#successModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#successModal .modal-body p span {
  font-weight: 700;
}
#publicModal .modal-header {
  border: none;
}
#publicModal .modal-footer {
  border: none;
}
#publicModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#publicModal .modal-body {
  text-align: center;
}
#publicModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#publicModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#publicModal .modal-body p span {
  font-weight: 700;
}
#publicModal .modal-body p {
  color: #000000b3;
}
#publicModal .modal-footer {
  border: none;
}
#publicModal .modal-footer .close-btn {
  background: #000000b3;
  border-radius: 5px;
  color: #FFFFFF;
}
#publicModal .modal-footer .cancel-btn {
  border: 1px solid #000000b3;
  color: #000000b3;
  background: #FFFFFF;
}
#deleteModal .modal-header {
  border: none;
}
#deleteModal .modal-footer {
  border: none;
}
#deleteModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteModal .modal-body {
  text-align: center;
}
#deleteModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#deleteModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#deleteModal .modal-body p span {
  font-weight: 700;
}
#deleteModal .modal-body p {
  color: #000000b3;
}
#deleteModal .modal-footer {
  border: none;
}
#deleteModal .modal-footer .close-btn {
  background: #000000b3;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteModal .modal-footer .cancel-btn {
  border: 1px solid #000000b3;
  color: #000000b3;
  background: #FFFFFF;
}
#deleteSetModal .modal-header {
  border: none;
}
#deleteSetModal .modal-footer {
  border: none;
}
#deleteSetModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteSetModal .modal-body {
  text-align: center;
}
#deleteSetModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#deleteSetModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#deleteSetModal .modal-body p span {
  font-weight: 700;
}
#deleteSetModal .modal-body p {
  color: #000000b3;
}
#deleteSetModal .modal-footer {
  border: none;
}
#deleteSetModal .modal-footer .close-btn {
  background: #000000b3;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteSetModal .modal-footer .cancel-btn {
  border: 1px solid #000000b3;
  color: #000000b3;
  background: #FFFFFF;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .no-padding {
    padding: 0;
  }
}
.non-ws #revisionTitle {
  border: none;
  border-bottom: 1px solid #848484;
  border-radius: 0;
}
.non-ws.submit-buttons .btn {
  border-color: #17A2B8;
  color: #17A2B8;
}
.non-ws.submit-buttons .btn:hover {
  background: #17A2B8;
  color: #FFFFFF;
}
.non-ws .flip-box-back p {
  color: #FFFFFF;
}
.expand {
  position: absolute;
  z-index: 999;
  right: 5px;
  bottom: 5px;
  margin: 0 auto;
}
#updateDefinition p,
#updateDefinition td {
  font-weight: 700;
}
#updateDefinition table,
#updateTerms table,
#updateDefinition image,
#updateTerms image {
  margin: 0 auto;
}
.modal td,
.modal p,
.modal h4 {
  color: #000000b3;
}
