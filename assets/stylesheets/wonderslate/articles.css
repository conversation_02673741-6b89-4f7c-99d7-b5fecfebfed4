html,
body {
  background: #fff !important;
}
.blog_container {
  width: calc(100% - 40%);
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .blog_container {
    width: calc(100% - 5%);
  }
}
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
li,
a,
input,
textarea,
select,
label,
span,
small,
strong,
button,
th,
td,
dl,
dt,
dd,
address {
  font-family: 'Open Sans', sans-serif !important;
}
h3 {
  margin: 12px 0;
  font-size: 22px;
}
h2 {
  font-size: 24px;
}
h1 {
  font-size: 28px;
  margin-bottom: 10px;
  line-height: 38px;
}
aside {
  margin-top: 1.4rem;
}
p {
  margin: 0;
}
.blog_bannerImg {
  margin: 14px 0;
}
.blog_bannerImg img {
  width: 850px;
  height: 325px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .blog_bannerImg img {
    width: 100%;
    height: auto;
  }
}
.tableOfContent li {
  margin-bottom: 10px;
}
section a {
  color: #155FB7 !important;
}
.tableOfContent li a {
  color: #155FB7 !important;
}
.blog_tags {
  display: flex;
  gap: 14px;
  flex-wrap: wrap;
  margin-top: 1.4rem;
}
.blog_tag {
  background: #d3d3d3;
  padding: 5px 10px;
  border-radius: 5px;
}
.blog_tag a {
  text-decoration: none;
  color: #000;
  font-size: 12px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .faq {
    padding-left: 0px !important;
  }
}
.faq h4 {
  font-size: 18px;
}
.faq-item {
  margin: 14px 0;
}
.faq-item::marker {
  font-size: 18px;
}
main {
  margin-top: 1.4rem !important;
}
.widget-ftr ul li {
  margin: 10px 0 !important;
}
.digitalLib-btn a {
  color: #000 !important;
}
section {
  margin: 2rem 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-header .navbar-nav .nav-item .nav-link.login-menu-link {
    background: #000 !important;
  }
}
