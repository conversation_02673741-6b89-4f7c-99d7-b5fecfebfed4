.switch {
  display: flex;
  position: absolute;
  right: 0;
  top: 0;
}
.switch.reset-switch {
  position: static;
  display: flex;
  align-items: center;
  margin-left: 3rem;
}
.switch.reset-switch label {
  margin-bottom: 0 !important;
}
.public-text {
  margin-right: 10px;
  display: block;
  font-style: italic;
  font-size: 12px;
  color: #000000b3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .public-text {
    color: #FFFFFF;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .setname-wrapper .public-text {
    color: #212121;
  }
}
.toggleSwitch {
  /* Rounded sliders */
}
.toggleSwitch .switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 15px;
}
.toggleSwitch .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.toggleSwitch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch .slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 1px;
  background-color: #FFFFFF;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch input:checked + .slider {
  background: #27AE60;
}
.toggleSwitch input:focus + .slider {
  box-shadow: 0 0 1px #17A2B8;
}
.toggleSwitch input:checked + .slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}
.toggleSwitch .slider.round {
  border-radius: 10px;
}
.toggleSwitch .slider.round:before {
  border-radius: 50%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  body {
    overflow-x: hidden;
  }
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.create-card #htmlreadingcontent .main-wrapper {
  margin-top: 0 !important;
}
.hero-title {
  color: #000000b3;
  font-size: 24px;
  text-transform: capitalize;
  display: flex;
  align-items: center;
  font-weight: 700;
  padding: 5px 15px;
}
.hero-title i {
  font-weight: 700;
  cursor: pointer;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .hero-title span.align-items-center {
    font-size: 20px;
  }
}
.btn-flashcard {
  background: #FFFFFF;
  border: 1px solid #000000b3;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  color: #000000b3;
  font-size: 12px;
  width: 120px;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}
.btn-flashcard i {
  font-size: 12px;
  margin-right: 5px;
  color: #000000b3;
}
.btn-flashcard:hover {
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
}
.flashcard-home,
.flashcard-folder {
  min-height: 500px;
}
.flashcard-home .search,
.flashcard-folder .search {
  width: 65%;
  height: 36px;
  border: 1px solid #000000b3;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-right: none;
  color: #000000b3;
  font-size: 12px;
}
.flashcard-home .search:focus,
.flashcard-folder .search:focus {
  box-shadow: 0 0 5px 1px #0000001A;
  -webkit-box-shadow: 0 0 5px 1px #0000001A;
  -moz-box-shadow: 0 0 5px 1px #0000001A;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .flashcard-home .search,
  .flashcard-folder .search {
    padding-right: 0 !important;
  }
}
@media (max-width: 767px) {
  .flashcard-home .search,
  .flashcard-folder .search {
    width: 40%;
  }
}
@media (max-width: 480px) {
  .flashcard-home .search,
  .flashcard-folder .search {
    width: 35%;
  }
}
.flashcard-home #search-btn,
.flashcard-folder #search-btn {
  background: none;
  border: 1px solid #000000b3;
  height: 36px;
  border-left: none;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.flashcard-home #search-btn.disabled,
.flashcard-folder #search-btn.disabled,
.flashcard-home #search-btn:disabled,
.flashcard-folder #search-btn:disabled {
  opacity: 1;
}
.flashcard-home #search-btn.disabled i,
.flashcard-folder #search-btn.disabled i,
.flashcard-home #search-btn:disabled i,
.flashcard-folder #search-btn:disabled i {
  opacity: 0.65;
}
.flashcard-home #search-btn i,
.flashcard-folder #search-btn i {
  color: #000000b3;
}
.flashcard-home #htmlreadingcontent.quiz-section,
.flashcard-folder #htmlreadingcontent.quiz-section {
  padding: 0 20px!important;
}
.flashcard-folder .search {
  width: 100%;
}
.new-folder {
  background: #F79420 !important;
  color: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  font-size: 12px;
  width: 120px;
  height: 36px;
  font-weight: 500;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.new-folder:hover {
  color: #FFFFFF;
  box-shadow: 0 0 10px #00000040;
  -webkit-box-shadow: 0 0 10px #00000040;
  -moz-box-shadow: 0 0 10px #00000040;
}
.new-folder i {
  color: #212121;
  font-size: 18px;
  margin-right: 10px;
}
.info-text {
  color: #444444;
  font-size: 12px;
  font-style: italic;
}
.info-text.reset-color {
  color: #000000b3;
}
.folder-box {
  border: 1px solid #000000b3;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  display: block;
  background: #FFFFFF;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 14px;
}
.folder-box:hover {
  box-shadow: 0 0 10px #00000040;
  -webkit-box-shadow: 0 0 10px #00000040;
  -moz-box-shadow: 0 0 10px #00000040;
  text-decoration: none;
}
.folder-box span {
  display: block;
  width: 75%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  text-transform: capitalize;
  color: #212121;
  padding: 10px;
}
.folder-box i {
  color: #000000b3;
}
.addFlashCard {
  background: #000000b3;
  box-shadow: 0 0 10px #0000001A;
  width: 75px;
  height: 75px;
  border-radius: 50px;
  display: block;
  text-align: center;
  color: #FFFFFF;
  font-size: 10px;
}
.addFlashCard:focus {
  background: #000000b3 !important;
}
.addFlashCard:hover {
  text-decoration: none;
  color: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
}
.addFlashCard i {
  color: #FFFFFF;
  font-size: 18px;
  margin-top: 15px;
}
.addFlashCard span {
  font-size: 10px;
  display: block;
  text-align: center;
}
.flashcard-set {
  display: flex !important;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  width: 100%;
  min-height: 100px;
  position: relative;
}
.flashcard-set:hover {
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  transition: all 0.5s;
}
.flashcard-set h4 {
  font-size: 14px;
  color: #212121;
  font-weight: 400;
  display: block;
  -webkit-line-clamp: 2;
  overflow: scroll;
  width: 250px;
}
@media (max-width: 1199px) {
  .flashcard-set h4 {
    width: 190px;
  }
}
@media (max-width: 767px) {
  .flashcard-set h4 {
    width: 220px;
    padding-left: 5px;
  }
}
@media (max-width: 400px) and (orientation: portrait) {
  .flashcard-set h4 {
    width: 185px;
  }
}
@media (max-width: 350px) and (orientation: portrait) {
  .flashcard-set h4 {
    width: 150px;
  }
}
.flashcard-set p {
  font-size: 12px;
  color: rgba(33, 33, 33, 0.48);
  margin-top: 5px;
}
@media (max-width: 767px) {
  .flashcard-set .chapter_txt {
    padding-left: 5px;
  }
}
.flashcard-set .set_public_btn {
  position: absolute;
  bottom: 0;
  right: 0;
}
.flashcard-set .set_public_btn .public-text {
  font-size: 10px;
}
.flashcard-set .set_public_btn .toggleSwitch .switch {
  width: 25px;
  height: 13px;
}
.flashcard-set .set_public_btn .toggleSwitch .slider:before {
  height: 11px;
  width: 10px;
}
.flashcard-set .set_public_btn .toggleSwitch input:checked + .slider:before {
  -webkit-transform: translateX(12px);
  -ms-transform: translateX(12px);
  transform: translateX(12px);
}
.flashcard-set .flashcard-actions {
  width: 25%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  top: 0;
  right: 0;
}
.flashcard-set .flashcard-actions .active i {
  color: #27AE60;
}
.flashcard-set .flashcard-actions i {
  font-size: 16px;
  color: #d4d4d4;
}
.flashcard-set .flashcard-actions i:hover {
  cursor: pointer;
}
.flashcard-set .flashcard-actions i.active {
  color: #27AE60;
}
.flashcard-set .flashcard-actions a {
  margin-left: 7px;
}
.flashcard-set .flashcard-actions a:first-child:hover i {
  color: #2F80ED;
}
.flashcard-set .flashcard-actions a:nth-child(2):hover i {
  color: #FF4B33;
}
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.75);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1);
  }
}
.flashcard-set .heart {
  animation: heartbeat 0.5s;
}
.flashcard-set .progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border-radius: 0 0 5px 5px;
  height: 4px;
}
.flashcard-set .progress .progress-bar {
  background: none;
}
.flashcard-set .progress .progress-bar.green {
  background-color: #27AE60;
}
.flashcard-set .progress .progress-bar.yellow {
  background: #FFC107;
}
.flashcard-set .progress .progress-bar.red {
  background: #FF4B33;
}
.flashcards a:hover {
  text-decoration: none;
}
#addFolder .modal-sm {
  max-width: 400px;
}
#addFolder .modal-header {
  border-bottom: none;
}
#addFolder .modal-header .modal-title {
  font-size: 16px;
  color: #000000b3;
  display: flex;
  align-items: center;
  font-weight: 400;
}
#addFolder .modal-header .modal-title i {
  font-size: 28px;
  margin-right: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #addFolder .modal-header .modal-title i {
    font-size: 24px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #addFolder .modal-header .modal-title {
    font-size: 12px;
  }
}
#addFolder .modal-content {
  background: #FFFFFF;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  min-height: 158px;
  padding: 5px 10px;
}
#addFolder .modal-footer {
  border: none;
}
#addFolder .modal-footer .create-folder {
  background: #000000b3;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  width: 73px;
  color: #FFFFFF;
  font-weight: 400;
  font-size: 14px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #addFolder .modal-footer .create-folder {
    font-size: 12px;
  }
}
#addFolder .modal-footer .cancel {
  border: none;
  background: none;
  color: #000000b3;
  font-weight: 400;
  font-size: 14px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #addFolder .modal-footer .cancel {
    font-size: 12px;
  }
}
#addFolder input {
  background: #FFFFFF;
  box-shadow: inset 0 0 5px #0000001A;
  -webkit-box-shadow: inset 0 0 5px #0000001A;
  -moz-box-shadow: inset 0 0 5px #0000001A;
  border-radius: 5px;
  color: rgba(33, 33, 33, 0.48);
  height: 45px;
}
#addFolder input::-webkit-input-placeholder {
  color: rgba(33, 33, 33, 0.48);
  font-size: 14px;
  font-family: 'DM Sans', sans-serif !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #addFolder input::-webkit-input-placeholder {
    font-size: 16px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #folders,
  #flashCardSets,
  #folderDtl {
    padding: 0;
    margin: 0 !important;
  }
  #folders .row > div,
  #flashCardSets .row > div,
  #folderDtl .row > div {
    padding: 0;
  }
  #folders .row > div > .col-12,
  #flashCardSets .row > div > .col-12,
  #folderDtl .row > div > .col-12 {
    padding: 0;
  }
}
.flashcard-mobile-action {
  background: #000000b3;
  box-shadow: 0 -4px 10px #0000001A;
  -webkit-box-shadow: 0 -4px 10px #0000001A;
  -moz-box-shadow: 0 -4px 10px #0000001A;
  border-radius: 20px;
  bottom: 5px;
  left: 0;
  right: 0;
  width: 98%;
  margin: 0 auto;
  height: 65px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.flashcard-mobile-action a {
  background: #FFFFFF;
  border-radius: 9px;
  color: #000000b3;
  font-size: 12px;
  min-width: 230px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.flashcard-mobile-action a i {
  font-size: 16px;
  font-weight: 700;
}
.flashcard-mobile-action a:last-child {
  font-weight: 700;
  width: 160px;
}
.flashcard-mobile-action span {
  font-size: 12px;
}
.flashcard-mobile-action span i {
  color: #FFFFFF;
}
.footer-menu-popover {
  background: rgba(0, 0, 0, 0.85) !important;
  bottom: 0;
}
.footer-menu-popover .modal-dialog {
  position: absolute;
  bottom: 80px;
  right: -10px;
  z-index: 1200 !important;
}
.footer-menu-popover .modal-dialog .modal-content {
  width: 50%;
  border: none;
  background: transparent;
}
.footer-menu-popover .modal-dialog .modal-content .modal-body {
  position: relative;
  bottom: 0px;
  top: 60px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .footer-menu-popover .modal-dialog .modal-content .modal-body {
    top: 25px;
  }
}
@media (min-width: 576px) {
  .footer-menu-popover .modal-dialog .modal-content .modal-body {
    top: 45px;
  }
}
.footer-menu-popover .modal-dialog .modal-content .modal-body .top-drop-menu {
  background: transparent;
}
.footer-menu-popover .modal-dialog .modal-content .modal-body .top-drop-menu div {
  margin-top: 10px;
  padding: 8px 15px;
  min-width: 170px;
  min-height: 40px;
  background: #000000b3;
  color: #FFFFFF;
  border-radius: 20px;
  display: flex;
  align-items: center;
}
.footer-menu-popover .modal-dialog .modal-content .modal-body .top-drop-menu div i {
  margin-right: 10px;
  font-size: 22px;
}
.footer-menu-popover .modal-dialog .modal-content .modal-body .top-drop-menu div img {
  margin-right: 10px;
  font-size: 18px;
}
.footer-menu-popover .modal-dialog .modal-content .modal-body .top-drop-menu div p {
  font-size: 12px;
}
.modal-backdrop {
  background: #FFFFFF !important;
  top: unset;
  z-index: 999 !important;
}
.modal-backdrop .show {
  opacity: 1;
}
.mobile-stocker.edit-back {
  margin-top: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .mobile-stocker.edit-back {
    margin-top: 0;
    justify-content: start;
    padding-left: 15px !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .mobile-stocker.edit-back p {
    width: auto;
    text-align: left;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ws-progressbar {
    margin-top: 4rem !important;
  }
}
.ws-progressbar .ws-next,
.ws-progressbar .ws-previous {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #212121;
  font-size: 10px;
}
.ws-progressbar .ws-next i,
.ws-progressbar .ws-previous i {
  margin-right: 5px;
  color: #444444;
  font-weight: 400;
  font-size: 20px;
}
.ws-progressbar .ws-next i {
  margin-left: 5px;
  margin-right: 0;
}
.ws-progressbar .progress {
  width: 293px;
  height: 7px;
  background: rgba(0, 0, 0, 0.08);
  box-shadow: inset 0 0 4px #0000001A;
  -webkit-box-shadow: inset 0 0 4px #0000001A;
  -moz-box-shadow: inset 0 0 4px #0000001A;
  border-radius: 5px;
}
.ws-progressbar .progress-bar {
  background: #17A2B8;
}
.ws-progressbar .slider-roller {
  font-size: 10px;
  color: #212121;
}
.slider-actions {
  margin-top: 4rem;
}
.slider-actions a {
  cursor: pointer;
}
.slider-actions button {
  background: none;
  border: none;
  display: block;
  text-align: center;
  margin: 2rem auto 0;
}
.slider-actions button::after {
  display: none;
}
.slider-actions button:focus,
.slider-actions button:hover {
  outline: 0;
  color: #000000b3;
}
.slider-actions button:focus span,
.slider-actions button:hover span,
.slider-actions button:focus i,
.slider-actions button:hover i {
  color: #000000b3;
  transition: all 1s;
}
.slider-actions button i {
  color: #949494;
}
.slider-actions button span {
  display: block;
  font-size: 10px;
  color: #949494;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .slider-actions {
    background: #FFFFFF;
    box-shadow: 0 -4px 4px #0000001A;
    -webkit-box-shadow: 0 -4px 4px #0000001A;
    -moz-box-shadow: 0 -4px 4px #0000001A;
    display: flex;
    justify-content: space-between;
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
    margin-top: 0;
    padding: 8px 0;
  }
  .slider-actions button {
    margin-top: 0;
  }
}
.displaySliderTemplate #my-cards {
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .displaySliderTemplate #my-cards {
    padding-left: 0;
    padding-right: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .displaySliderTemplate .backfromcard {
    -webkit-text-fill-color: #FFFFFF;
    font-size: 24px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .displaySliderTemplate .set-app-slider .backfromcard {
    -webkit-text-fill-color: transparent;
    left: 0;
  }
}
@page {
  size: auto;
  margin: 0;
}
@media print {
  body * {
    visibility: hidden;
  }
  .logotext {
    display: block !important;
    background: url('../../images/ws/wonderslateLogo.svg') center center no-repeat;
    width: 175px;
    height: 50px;
    visibility: visible;
    position: absolute;
    left: 0;
    top: 0;
  }
  .hero-title {
    visibility: visible;
    position: absolute;
    left: 0;
    top: 60px;
    color: #212121;
    -webkit-text-fill-color: #212121;
  }
  .hero-title i {
    display: none;
  }
  #print-cards,
  #print-cards * {
    visibility: visible;
  }
  #print-cards {
    counter-reset: section;
    position: absolute;
    left: 30px;
    top: 70px;
  }
  #print-cards > div:before {
    counter-increment: section;
    content: "Set " counter(section) " :";
    white-space: nowrap;
    margin-top: 26px;
    font-weight: 700;
    font-size: 14px;
  }
  #print-cards > div > div {
    right: 27px;
  }
  #print-cards > div > div:last-child {
    border: 1px solid #212121;
  }
  #print-cards > div > div:first-child {
    border: 1px solid #212121;
  }
}
.mobile-stocker h4,
.mobile-stocker h2 {
  margin: 0;
}
.mobile-stocker h4 i,
.mobile-stocker h2 i {
  margin-right: 7px;
}
.mobile-stocker h4 i:hover,
.mobile-stocker h2 i:hover {
  cursor: pointer;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .mobile-stocker {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    background: #444444;
    box-shadow: 0 4px 10px #0000001A;
    -webkit-box-shadow: 0 4px 10px #0000001A;
    -moz-box-shadow: 0 4px 10px #0000001A;
    border-radius: 17px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 60px;
    z-index: 99;
    transition: all 0.35s linear;
  }
  .mobile-stocker h4,
  .mobile-stocker h2 {
    color: #FFFFFF;
    -webkit-text-fill-color: #FFFFFF;
    font-weight: 400;
    font-size: 14px;
  }
  .mobile-stocker h4 i,
  .mobile-stocker h2 i {
    position: absolute;
    left: 15px;
    top: 18px;
    font-weight: 400;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #print-cards > div:last-child {
    padding-bottom: 6rem;
  }
}
.iconStyle {
  color: #444444;
}
.down-slider i:last-child {
  position: relative;
  animation: jump 3s infinite;
  font-size: 35px;
  transition: 0.3s ease-in-out;
}
@keyframes jump {
  0% {
    top: -10px;
  }
  50% {
    top: 0px;
  }
  100% {
    top: -10px;
  }
}
#addToFlashCard {
  z-index: 9999999999;
}
#addToFlashCard h4 {
  font-size: 20px;
  color: #000000b3;
}
#addToFlashCard label {
  font-size: 14px;
  color: rgba(33, 33, 33, 0.8);
}
#addToFlashCard textarea {
  font-size: 16px;
  resize: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .flashcard-home #htmlreadingcontent {
    padding: 0 !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .flashcard-home .no-padding {
    padding: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .create-card .main-wrapper.reset-app {
    margin-top: 0 !important;
  }
}
#htmlreadingcontent .main-wrapper {
  margin-top: 3rem !important;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  #htmlreadingcontent .main-wrapper {
    margin-top: 5rem !important;
    padding: 20px !important;
  }
}
.flashcard-empty-msg {
  font-size: 13px;
}
.flashcard-empty-msg.bold {
  font-weight: 700;
}
.empty-info .addFlashCard {
  margin: 0 auto;
}
.flashcard-mobile-action a.addCard {
  width: 90%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .flashcards > div:last-child {
    margin-bottom: 5rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .no-padding {
    padding: 0;
  }
}
#print-cards .flashcard-set h4 {
  width: 100%;
}
.displaySliderTemplate .card-box {
  border: none;
}
.card-border {
  border: 1px solid #27AE60 !important;
}
.backfromcard {
  position: relative;
  color: #000000b3;
  left: -50px;
  font-size: 40px;
  cursor: pointer;
}
#allCards > div:first-child {
  padding: 0 2rem;
}
#htmlreadingcontent .backfromcard {
  left: -30px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #htmlreadingcontent .mobile-stocker .backfromcard {
    left: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .edit-back .cancel .backfromcard {
    -webkit-text-fill-color: #FFFFFF;
    left: -10px !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .edit-back p {
    width: 65%;
    text-align: center;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 700;
  }
}
.flashcard-print > div {
  padding-right: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #play-match .backfromcard {
    left: 0;
  }
}
.match-cards {
  margin-top: 2rem !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .match-cards {
    margin-top: 5rem !important;
  }
}
section.displaySliderTemplate {
  min-height: 100vh;
}
.playmatch {
  max-width: 700px;
  margin: 0 auto;
}
#play-match {
  display: none;
  padding-bottom: 2rem;
}
#play-match #draggable {
  width: 150px;
  height: 150px;
  padding: 0.5em;
}
#play-match .card {
  width: 150px;
  margin-top: 25px;
  box-shadow: 0 0 10px 0 rgba(208, 63, 128, 0.22);
  cursor: pointer;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #play-match .card {
    width: 110px;
  }
}
#play-match .card.matched {
  border: 1px solid rgba(64, 64, 64, 0.70196078);
}
#play-match .ui-draggable-dragging {
  z-index: 999;
}
#play-match .card-body {
  min-height: 215px;
  max-height: 215px;
  overflow: hidden;
  margin: 5px;
  font-size: 12px;
  text-align: center;
  padding: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #play-match .card-body {
    min-height: 110px;
    width: 92px;
  }
}
#play-match .card-body p,
#play-match .card-body span {
  color: #000000b3;
}
#play-match .card-text {
  font-size: 12px;
  border: none !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 12;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-align: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #play-match .card-text {
    font-size: 10px;
    -webkit-line-clamp: 8;
  }
}
#play-match .back-button-play-match {
  display: flex;
}
#play-match .back-button-play-match > i {
  left: 0;
  top: 18px;
}
#play-match .border-card {
  border: 1px solid rgba(64, 64, 64, 0.70196078);
}
#play-match .card-anim {
  /* Start the shake animation and make the animation last for 0.5 seconds */
  animation: shake 0.5s;
  /* When the animation is finished, start again */
  animation-iteration-count: 1;
}
@keyframes shake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }
  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }
  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }
  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }
  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }
  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }
  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }
  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }
  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }
  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }
  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }
}
#play-match .ui-draggable-helper {
  opacity: 0.7;
}
.time-header p {
  color: #000000b3;
  font-size: 12px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .time-header p {
    color: #FFFFFF;
    margin: 0;
  }
}
.time-header span {
  color: #000000b3;
  font-size: 14px;
}
.time-header span#timer {
  font-weight: bold;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .time-header span {
    color: #FFFFFF;
  }
}
.ws-text {
  color: #000000b3;
  font-size: 14px;
}
.ws-modal .modal-header {
  border: none;
}
.ws-modal .modal-footer {
  border: none;
}
.ws-dragdrop {
  width: 200px;
  height: 100px;
  display: block;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .randomMix {
    padding-right: 5px;
    padding-left: 5px;
  }
}
.randomMix table td {
  color: #000000b3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #play-match {
    padding-right: 5px;
    padding-left: 5px;
  }
}
#publicModal .modal-header {
  border: none;
}
#publicModal .modal-footer {
  border: none;
}
#publicModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#publicModal .modal-body {
  text-align: center;
}
#publicModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#publicModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#publicModal .modal-body p span {
  font-weight: 700;
}
#publicModal .modal-body p {
  color: #000000b3;
}
#publicModal .modal-footer {
  border: none;
}
#publicModal .modal-footer .close-btn {
  background: #000000b3;
  border-radius: 5px;
  color: #FFFFFF;
}
#publicModal .modal-footer .cancel-btn {
  border: 1px solid #000000b3;
  color: #000000b3;
  background: #FFFFFF;
}
#deleteModal .modal-header {
  border: none;
}
#deleteModal .modal-footer {
  border: none;
}
#deleteModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteModal .modal-body {
  text-align: center;
}
#deleteModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#deleteModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#deleteModal .modal-body p span {
  font-weight: 700;
}
#deleteModal .modal-body p {
  color: #000000b3;
}
#deleteModal .modal-footer {
  border: none;
}
#deleteModal .modal-footer .close-btn {
  background: #000000b3;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteModal .modal-footer .cancel-btn {
  border: 1px solid #000000b3;
  color: #000000b3;
  background: #FFFFFF;
}
#deleteSetModal .modal-header {
  border: none;
}
#deleteSetModal .modal-footer {
  border: none;
}
#deleteSetModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteSetModal .modal-body {
  text-align: center;
}
#deleteSetModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#deleteSetModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#deleteSetModal .modal-body p span {
  font-weight: 700;
}
#deleteSetModal .modal-body p {
  color: #000000b3;
}
#deleteSetModal .modal-footer {
  border: none;
}
#deleteSetModal .modal-footer .close-btn {
  background: #000000b3;
  border-radius: 5px;
  color: #FFFFFF;
}
#deleteSetModal .modal-footer .cancel-btn {
  border: 1px solid #000000b3;
  color: #000000b3;
  background: #FFFFFF;
}
#successModal .modal-header {
  border: none;
}
#successModal .modal-footer {
  border: none;
}
#successModal .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#successModal .modal-body {
  text-align: center;
}
#successModal .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#successModal .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#successModal .modal-body p span {
  font-weight: 700;
}
.btn-reset {
  background: none;
  border: none;
  display: block;
  text-align: center;
  margin-left: 7px;
}
.btn-reset:focus {
  outline: 0;
}
.btn-reset.dropdown-toggle::after {
  display: none;
}
#share-dropdown_explorer li a {
  width: 100%;
  cursor: pointer;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #recentFlashCardSets .row {
    padding: 0;
  }
  #recentFlashCardSets .row .col-12 {
    padding: 0;
  }
  #recentFlashCardSets .row .col-12 .col-12 {
    padding: 0;
  }
  #recentFlashCardSets .row .col-12 .col-12 .d-flex {
    padding: 0;
  }
}
.modal {
  background: rgba(0, 0, 0, 0.85) !important;
}
.video-url .modal .modal-body input {
  margin-bottom: 0 !important;
}
.web-url .modal .modal-body input {
  margin-bottom: 0.5rem !important;
}
.web-url .modal .modal-header,
.video-url .modal .modal-header {
  display: none;
}
.web-url .modal .modal-content,
.video-url .modal .modal-content {
  width: 100%;
}
.web-url .modal .modal-body form,
.video-url .modal .modal-body form {
  margin-top: 0;
}
.web-url .modal .modal-body input,
.video-url .modal .modal-body input {
  width: 100%;
  border-bottom: 1px solid #444444;
}
.web-url .modal .modal-footer button,
.video-url .modal .modal-footer button {
  background: transparent;
}
.web-url .modal .modal-footer button.cancel,
.video-url .modal .modal-footer button.cancel {
  color: #444444;
  text-transform: capitalize;
}
.web-url .modal .modal-footer button.saveLink,
.video-url .modal .modal-footer button.saveLink {
  color: #000000b3;
  font-size: 14px;
  font-weight: 700;
}
.box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 52px;
  border-radius: 5px;
}
.box p {
  font-size: 9px;
  margin: 0;
  text-align: center;
  top: 0;
  color: #FFFFFF;
  font-weight: 700;
}
.box i {
  width: 32px;
  height: 24px;
  margin: 0 auto;
  display: flex;
}
.box.blue {
  background: radial-gradient(109.09% 109.09% at 0% 0%, #2D9CDB 0%, #2F80ED 100%);
}
.box.blue i {
  background: url("../../images/ws/pdf.svg") center center no-repeat;
}
.box.green {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, rgba(85, 115, 0, 0.8) 76.95%);
}
.box.green i {
  background: url("../../images/ws/link.svg") center center no-repeat;
}
.box.yellow {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F2C74C 0%, #F2994A 46.86%);
}
.box.yellow i {
  background: url("../../images/ws/flashcard.svg") center center no-repeat;
}
.box.pink {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F24CE1 0%, rgba(183, 7, 206, 0.9) 76.95%);
}
.box.pink i {
  background: url("../../images/ws/video.svg") center center no-repeat;
}
.box.lightgreen {
  background: radial-gradient(100% 100% at 0% 0%, #49E859 0%, #007D0C 100%);
}
.box.lightgreen i {
  background: url("../../images/ws/notes.svg") center center no-repeat;
}
.box.violet {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #BB6BD9 0%, #7B24CD 99.48%);
}
.box.violet i {
  background: url("../../images/ws/mcq_icon.svg") center center no-repeat;
}
.box.darkgreen {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #4CF2E8 5.08%, #006963 70.31%);
}
.box.darkgreen i {
  background: url("../../images/ws/mindmap.svg") center center no-repeat;
}
.h-90 {
  height: 55%;
}
.ds-flex {
  display: flex;
}
#folderDtl .flashcard-set .flashcard-actions {
  top: 10px;
  right: 10px;
}
.notes-head {
  width: 100vw;
}
.materialsAddDropdown button {
  background: #000000b3;
  color: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  font-size: 12px;
  width: 90px;
  height: 36px;
  font-weight: 400;
  margin-left: 1rem;
}
.materialsAddDropdown button:hover {
  color: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
}
.materialsAddDropdown button i {
  color: #FFFFFF;
  font-size: 18px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.materialsAddDropdown button i.rotated {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
}
.materialsAddDropdown button:after {
  margin-left: 0.5em;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.materialsAddDropdown.show {
  z-index: 999;
}
.materialsAddDropdown.show button {
  background: #212121;
}
.materialsAddDropdown.show button:after {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
}
.materialsAddDropdown .dropdown-menu {
  padding: 0;
  margin: 0;
  background: none;
  left: auto !important;
}
.materialsAddDropdown .dropdown-menu a.dropdown-item {
  margin: 5px 0;
  padding: 0.375rem 0.75rem;
  width: 100%;
  justify-content: flex-start;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
.materialsAddDropdown .dropdown-menu a.dropdown-item:hover {
  margin-left: 5px;
}
#overlay-bg {
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
.add-resource-btn {
  width: 90px;
}
.add-resource-btn i {
  margin-right: 0;
}
#addVideo .mdl-textfield,
#webForm .mdl-textfield {
  width: 100%;
}
#webForm .mdl-textfield {
  padding: 14px 0;
}
.expandDragDrop {
  position: absolute;
  bottom: 0;
  width: 80%;
}
.flip-box-inner.flipCard .flip-box-front .expand {
  display: none;
}
.title-wrapper {
  padding: 10px !important;
}
.box {
  min-height: 100px !important;
  height: 100% !important;
  border-radius: 10px 0px 0px 10px !important;
}
.cardLabel {
  writing-mode: vertical-lr;
  text-orientation: mixed;
  transform: rotate(180deg);
  padding: 12px;
  text-align: center;
  color: white !important;
  letter-spacing: 2px;
  font-weight: 800 !important;
  font-size: 12px !important;
}
.align-self-center {
  transform: rotate(270deg);
  margin-top: 5px !important;
  margin-left: 5px !important;
}
