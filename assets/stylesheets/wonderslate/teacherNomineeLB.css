.btn-secondary:not(:disabled):not(.disabled).active:focus,
.btn-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: none !important;
}
.center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  font-family: "Red Hat Display", sans-serif;
}
.top3 {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4b4168;
}
.top3 .item {
  box-sizing: border-box;
  position: relative;
  background: #f4f4f4;
  width: 190px;
  min-height: 200px;
  text-align: center;
  padding: 2.8rem 0 0;
  margin: 1rem 1rem 2rem;
  border-radius: 0.5rem;
  transform-origin: bottom;
  cursor: pointer;
  transition: transform 200ms ease-in-out;
  box-shadow: 0 0 2rem 0 rgba(0, 0, 0, 0.1), 0 1rem 1rem -1rem rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}
@media (max-width: 767px) {
  .top3 .item {
    width: 110px;
    min-height: 180px;
    padding: 5px;
    margin: 10px;
  }
}
.top3 .item .pic {
  position: absolute;
  top: -2rem;
  left: 4.4rem;
  width: 4.5rem;
  height: 4.5rem;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  margin-right: 1rem;
  border: 2px solid;
}
@media (max-width: 767px) {
  .top3 .item .pic {
    width: 50px;
    height: 50px;
    left: 2rem;
  }
}
.top3 .item .pos {
  font-weight: 900;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}
@media (max-width: 767px) {
  .top3 .item .pos {
    margin-bottom: 0;
    margin-top: 30px;
  }
}
.top3 .item .name {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
}
.top3 .item .score {
  opacity: 0.5;
}
.top3 .item .score:after {
  display: block;
  content: "points";
  opacity: 0.5;
}
.top3 .item.one {
  transform: translateY(-20px);
}
.top3 .item.one .pic {
  width: 5rem;
  height: 5rem;
}
@media (max-width: 767px) {
  .top3 .item.one .pic {
    width: 50px;
    height: 50px;
  }
}
.top3 .item:hover {
  transform: scale(1.05);
}
.list {
  padding-left: 2rem;
  margin: 0 auto;
  width: 55%;
  height: 70vh;
  overflow-y: scroll;
}
@media (max-width: 767px) {
  .list {
    width: 100%;
    margin-top: 30px;
  }
}
.list .item {
  position: relative;
  display: flex;
  align-items: center;
  height: auto;
  border-radius: 4rem;
  margin-bottom: 2rem;
  background: rgba(247, 148, 32, 0.6);
  transform-origin: left;
  cursor: pointer;
  transition: transform 200ms ease-in-out;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.list .item .pos {
  font-weight: 900;
  position: absolute;
  left: -2rem;
  text-align: center;
  font-size: 1.25rem;
  width: 1.5rem;
  color: #000;
  opacity: 0.6;
  transition: opacity 200ms ease-in-out;
  font-style: italic;
}
.list .item .pic {
  width: 4.5rem;
  height: 4.5rem;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  box-shadow: 0 0 1rem 0 rgba(0, 0, 0, 0.2), 0 1rem 1rem -0.5rem rgba(0, 0, 0, 0.3);
}
.list .item .name {
  flex-grow: 2;
  flex-basis: 10rem;
  font-size: 1.1rem;
  padding: 0.5rem;
}
.list .item .score {
  margin-right: 1.5rem;
  opacity: 0.5;
}
.list .item .score:after {
  margin-right: 1rem;
  content: "";
  opacity: 0.5;
}
.list .item:hover .pos {
  opacity: 0.8;
}
.badgeImg {
  width: 40px;
  opacity: 0.3;
  position: absolute;
  top: 0;
  left: 155px;
}
@media (max-width: 767px) {
  .badgeImg {
    width: 30px;
    left: 80px;
  }
}
.user__place {
  opacity: 0.5;
}
@media (max-width: 767px) {
  .topc {
    padding: 0!important;
  }
}
.item:nth-child(1) {
  order: 2;
}
.item:nth-child(3) {
  order: 3;
}
footer {
  display: none !important;
}
.customDate {
  border: 1px solid #000;
  border-radius: 2px;
}
.tabs-content {
  width: 50% !important;
}
@media (max-width: 767px) {
  .tabs-content {
    width: 100% !important;
    margin-top: 2rem;
  }
}
.tabs-wrapper {
  justify-content: center !important;
  margin-bottom: 30px;
}
.tab-btn {
  width: 175px;
  padding: 0.7rem !important;
  border-radius: 100px !important;
  background: transparent !important;
  color: #000 !important;
}
.active {
  background: #000 !important;
  color: #fff !important;
}
.lb-content {
  border-color: #fff;
  color: #fff !important;
}
.t-position {
  color: #fff;
}
#nextList h3 {
  color: #fff !important;
}
