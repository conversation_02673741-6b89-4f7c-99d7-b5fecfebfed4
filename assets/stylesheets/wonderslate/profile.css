.overlay-profile {
  background: #000000B3;
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 9991;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .overlay-profile {
    background: #FFFFFF;
    overflow-y: auto;
  }
}
.leaderboard-layer {
  background: #FFFFFF;
  margin: 4rem;
  height: calc(100vh - 200px);
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-layer {
    margin: 0;
    box-shadow: unset !important;
    height: 100%;
    padding-bottom: 1rem;
  }
}
.leaderboard-layer .jumbotron {
  background: #FFFFFF;
  padding: 0;
}
.leaderboard-layer .jumbotron table {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
}
.leaderboard-layer .jumbotron table td {
  color: #444444;
  font-size: 12px;
}
.leaderboard-layer .jumbotron table tr:first-child td,
.leaderboard-layer .jumbotron table tr:nth-child(2) td,
.leaderboard-layer .jumbotron table tr:nth-child(3) td {
  font-size: 14px;
  font-weight: bold;
  color: #212121 !important;
}
.leaderboard-layer .jumbotron .table thead th {
  color: #444444;
  font-size: 10px;
  font-weight: normal;
}
.leaderboard-layer .jumbotron .table thead th:last-child {
  border-right: none;
}
.leaderboard-layer .leaderboard-left {
  height: calc(100vh - 200px);
}
.leaderboard-layer .leaderboard-right {
  height: calc(100vh - 200px);
}
.profile-layer {
  background: #FFFFFF;
  margin: 4rem;
  height: calc(100vh - 130px);
  overflow-y: auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .profile-layer {
    margin: 0;
    box-shadow: unset !important;
    height: 100%;
    padding-bottom: 1rem;
  }
}
.profile-layer {
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 10px;
}
.profile-layer > .jumbotron {
  background: #FFFFFF;
  box-shadow: 0 4px 10px #0000001A;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .profile-layer > .jumbotron {
    padding-bottom: 10px;
  }
}
.rank {
  background: -webkit-linear-gradient(#F2994A, #F2C94C);
  background: -moz-linear-gradient(#F2994A, #F2C94C);
  background: -webkit-gradient(#F2994A, #F2C94C);
  background: -o-linear-gradient(#F2994A, #F2C94C);
  background: -ms-linear-gradient(#F2994A, #F2C94C);
  background: linear-gradient(#F2994A, #F2C94C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-weight: bold;
  font-size: 40px;
}
.btn-leaderboard {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 36px;
  margin: 0 auto;
  font-size: 12px;
  margin-top: 1rem !important;
  display: flex;
  align-items: center;
}
.btn-leaderboard i {
  color: #000000b3;
  font-size: 16px;
  margin-right: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .btn-leaderboard {
    margin-top: 0 !important;
  }
}
.btn-leaderboard span {
  color: #000000b3;
}
.profile {
  text-align: center;
}
.profile .image-wrapper {
  width: 85px;
  height: 85px;
  background: #FFFFFF;
  border: 3px solid #FFFFFF;
  filter: drop-shadow(0 0 10px #00000040);
  position: relative;
  border-radius: 50%;
  margin: 0 auto;
}
.profile .image-wrapper img {
  width: 80px;
  height: 80px;
}
.profile .image-wrapper i {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 14px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000000b3;
}
.profile .user-name {
  color: #000000b3;
  font-size: 24px;
}
.profile .user-name #firstname {
  font-weight: bold;
}
.profile .user-name #secondname {
  font-weight: normal;
}
.profile #grade-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0;
}
.profile #grade-rating i {
  background: -webkit-linear-gradient(#F2994A, #F2C94C);
  background: -moz-linear-gradient(#F2994A, #F2C94C);
  background: -webkit-gradient(#F2994A, #F2C94C);
  background: -o-linear-gradient(#F2994A, #F2C94C);
  background: -ms-linear-gradient(#F2994A, #F2C94C);
  background: linear-gradient(#F2994A, #F2C94C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-size: 18px;
  margin-right: 5px;
}
.profile .wonderwonk {
  color: #444444;
  font-weight: normal;
  font-size: 11px;
}
.points-earned-text {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #444444;
  padding: 0 0.1rem;
  margin-bottom: 0;
}
.points-earned-text span {
  padding: 0.2rem 0.75rem;
  border-right: 0.5px solid #444444;
}
.points-earned {
  color: #000000b3;
  padding: 0.375rem 0.75rem;
  font-weight: bold;
  font-size: 24px;
}
.title-profile {
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .title-profile {
    margin: 0;
    margin-left: 1rem;
    width: 107px;
    text-align: center;
  }
}
.title-profile p {
  color: #444444;
  font-size: 12px;
  margin-bottom: 0;
}
#total-doubts {
  color: #2F80ED;
  font-size: 12px;
}
#total-flashcard {
  color: #2F80ED;
  font-size: 12px;
}
.btn-makepoint,
.btn-makepoint:focus,
.btn-makepoint:hover,
.btn-makepoint:active {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 36px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2F80ED;
}
.btn-makepoint i,
.btn-makepoint:focus i,
.btn-makepoint:hover i,
.btn-makepoint:active i {
  margin-left: 5px;
  font-size: 16px;
  color: #2F80ED;
}
.amt-profile {
  padding: 0 2rem;
  border-left: 0.5px solid #444444;
}
.amt-profile p {
  color: #000000b3;
  margin-bottom: 0;
}
.mtProfile {
  margin-top: 2rem !important;
}
.more-for-you-cards {
  background: none !important;
  box-shadow: unset !important;
}
.user-name {
  font-size: 14px;
  color: #000000b3;
  text-align: center;
}
.margin-reset {
  margin: 0;
}
.margin-reset-auto {
  margin: 0 auto;
}
.btn-backward {
  background: none;
  border: none;
}
.my-profile button,
.my-profile p {
  color: #444444;
  font-weight: 700;
  margin-bottom: 0;
}
.btn-tap-details {
  margin: 0 auto;
  background: none;
  color: #2F80ED;
  font-weight: 400;
  font-size: 13px;
}
form label {
  color: #444444;
  font-size: 11px;
  font-style: italic;
}
form input.form-control {
  border: 1px solid #000000b3;
}
form select.form-control {
  border: 1px solid #000000b3;
}
form .btn-submit {
  background: #000000b3;
  box-shadow: 0 2px 4px #0000001A;
  border-radius: 5px;
  color: #FFFFFF;
  padding: 0.5rem;
  font-weight: normal;
}
.profile-wrapper .profile {
  margin: 0 auto;
}
.profile-wrapper .profile .user-name {
  color: #000000b3;
}
#morePoints {
  background: none !important;
}
#morePoints .modal-dialog {
  height: 100%;
  margin: 0;
  max-width: 100%;
}
#morePoints .modal-dialog h1,
#morePoints .modal-dialog p {
  color: #FFFFFF;
}
#morePoints .modal-dialog .modal-header {
  border: none;
}
#morePoints .modal-dialog .modal-header .close {
  font-weight: normal;
  color: #FFFFFF;
  text-shadow: unset;
  opacity: 1;
}
#morePoints .modal-dialog .modal-content {
  height: 100%;
  background: #000000B3;
  border-radius: 0;
}
#morePoints .modal-dialog .modal-content .modal-body h2 {
  text-align: center;
  color: #FFFFFF;
  font-weight: bold;
}
#morePoints .modal-dialog .modal-content .modal-body p {
  text-align: center;
  font-size: 12px;
}
#morePoints .modal-dialog .modal-content .modal-body p strong {
  font-weight: bold;
}
#morePoints .modal-dialog .modal-content .modal-body ul {
  margin-top: 2rem;
  padding: 0;
}
#morePoints .modal-dialog .modal-content .modal-body ul li {
  text-align: center;
  color: #FFFFFF;
  font-size: 10px;
  list-style-type: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
#morePoints .modal-dialog .modal-content .modal-body ul li i {
  background: -webkit-linear-gradient(#F2994A, #F2C94C);
  background: -moz-linear-gradient(#F2994A, #F2C94C);
  background: -webkit-gradient(#F2994A, #F2C94C);
  background: -o-linear-gradient(#F2994A, #F2C94C);
  background: -ms-linear-gradient(#F2994A, #F2C94C);
  background: linear-gradient(#F2994A, #F2C94C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-size: 16px;
}
#morePoints .modal-dialog .modal-content .modal-body ul li span {
  display: block;
  margin-left: 10px;
}
#morePoints .modal-dialog .modal-footer {
  border: none;
}
.leaderboard-left {
  background: radial-gradient(98.27% 195.3% at 0% 5.64%, #5A5A5A 0%, #2F2F2F 80.73%);
  box-shadow: 4px 0 4px #00000040;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-left {
    border-radius: 0;
  }
}
.leaderboard-left .switch {
  position: relative;
  display: inline-block;
  width: 90px;
  height: 24px;
}
.leaderboard-left .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.leaderboard-left .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  border-radius: 6px;
  font-size: 8px;
}
.leaderboard-left .slider p {
  font-size: 8px;
  margin-bottom: 0;
}
.leaderboard-left .slider:before {
  position: absolute;
  content: "All";
  height: 24px;
  width: 45px;
  left: 0px;
  bottom: 0px;
  background: #212121;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  font-size: 8px;
  font-weight: bold;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leaderboard-left input:checked + .slider {
  background-color: #FFFFFF;
}
.leaderboard-left input:focus + .slider {
  box-shadow: 0 0 1px #FFFFFF;
}
.leaderboard-left input:checked + .slider:before {
  content: '7 days';
  -webkit-transform: translateX(45px);
  -ms-transform: translateX(45px);
  transform: translateX(45px);
  font-size: 8px;
  font-weight: bold;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leaderboard-left .leader-img {
  text-align: center;
}
.leaderboard-left .leader-img.mt-4 {
  margin-top: 4rem !important;
}
.leaderboard-left .leader-img.big h4 {
  font-size: 18px;
}
.leaderboard-left .leader-img.big h3 {
  font-size: 18px;
}
.leaderboard-left .leader-img img {
  width: 65px;
  height: 65px;
  border: 3px solid #FFFFFF;
  filter: drop-shadow(0 0 10px #00000040);
  margin: 0 auto;
}
.leaderboard-left .leader-img img.big {
  width: 99px;
  height: 100px;
}
.leaderboard-left .leader-img h4 {
  background: -webkit-linear-gradient(#F2994A, #F2C94C);
  background: -moz-linear-gradient(#F2994A, #F2C94C);
  background: -webkit-gradient(#F2994A, #F2C94C);
  background: -o-linear-gradient(#F2994A, #F2C94C);
  background: -ms-linear-gradient(#F2994A, #F2C94C);
  background: linear-gradient(#F2994A, #F2C94C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 18px;
  text-align: center;
  font-weight: bold;
}
.leaderboard-left .leader-img h3 {
  text-align: center;
  background: -webkit-linear-gradient(#F2994A, #F2C94C);
  background: -moz-linear-gradient(#F2994A, #F2C94C);
  background: -webkit-gradient(#F2994A, #F2C94C);
  background: -o-linear-gradient(#F2994A, #F2C94C);
  background: -ms-linear-gradient(#F2994A, #F2C94C);
  background: linear-gradient(#F2994A, #F2C94C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 12px;
  font-weight: bold;
}
.leaderboard-left .leader-img p {
  font-size: 10px;
  color: #FFFFFF;
  margin-bottom: 0;
}
.leader-board .btn-backward i {
  color: #FFFFFF;
}
.leader-board p {
  color: #FFFFFF;
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 0;
}
.card-wrappers {
  background: #FFFFFF;
  padding: 1rem;
  border-radius: 5px;
  position: absolute;
  bottom: 20px;
  width: 92%;
  box-shadow: 4px 0 4px #00000040;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .card-wrappers {
    bottom: -45px;
  }
}
.card-wrappers > div {
  text-align: center;
  border-left: 0.5px solid #444444;
  width: 100%;
}
.card-wrappers > div:first-child {
  border-left: none;
}
.card-wrappers .profile-img img {
  width: 38px;
  height: 38px;
  border-radius: 50px;
  border: 3px solid #FFFFFF;
  filter: drop-shadow(0 0 10px #00000040);
}
.card-wrappers p {
  color: #444444;
  font-size: 10px;
  margin-bottom: 0;
}
.card-wrappers h3 {
  font-size: 18px;
  color: #000000b3;
}
.card-wrappers h3 span {
  color: #444444;
  font-size: 10px;
}
.calc-height {
  height: calc(100vh - 400px);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .calc-height {
    margin-top: 2rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .reset-padding {
    padding: 0;
    margin-top: 4rem;
  }
}
#tutorial .modal-dialog {
  height: 100%;
  margin: 0;
}
#tutorial .modal-dialog .modal-content {
  height: 100%;
  width: 100vw;
  background: #000000B3;
  border: none;
  border-radius: 0;
}
#tutorial .modal-dialog .modal-content h2 {
  font-size: 36px;
  font-weight: bold;
}
#tutorial .modal-dialog .modal-content p {
  font-size: 12px;
  margin-bottom: 0;
}
#tutorial .modal-dialog .modal-content p i {
  font-size: 16px;
  position: relative;
  top: 0;
}
#tutorial .modal-header {
  border: none;
  display: flex;
  align-items: center;
  width: 75%;
  margin: 0 auto;
}
#tutorial .modal-header .close {
  text-shadow: none;
  color: #FFFFFF;
  opacity: 1;
}
#tutorial .modal-header .btn-next {
  border: 1px solid #FFFFFF;
  color: #FFFFFF;
  border-radius: 4px;
  background: none;
  font-size: 9px;
}
#tutorial .modal-footer {
  border: none;
}
.carousel-item h2,
.carousel-item p {
  color: #FFFFFF;
}
.carousel-item.tutor .circle {
  width: 26.25px;
  height: 26.25px;
  border-radius: 50px;
  border: 1px solid #FFFFFF;
  display: flex;
  align-self: start;
  align-items: center;
  justify-content: center;
}
.carousel-item.tutor .circle i {
  color: #FFFFFF;
  font-size: 16px;
}
.carousel-item.tutor .btn-answer {
  background: #FFFFFF;
  border: 1.25px solid #212121;
  box-sizing: border-box;
  border-radius: 5px;
  color: #212121;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.carousel-item.tutor .btn-answer i {
  margin-right: 10px;
}
.carousel-item.tutor h2 {
  font-size: 36px;
  margin-bottom: 1rem;
}
.carousel-item.tutor p {
  font-size: 14px;
  color: #FFFFFF;
}
.carousel-item.tutor .wonderwonk {
  color: #FFFFFF;
  font-size: 12px;
}
.carousel-item.tutor #grade-rating {
  display: flex;
  margin-right: 5px;
}
.carousel-item.tutor #grade-rating i {
  background: -webkit-linear-gradient(#FFB002, #FF7B02);
  background: -moz-linear-gradient(#FFB002, #FF7B02);
  background: -webkit-gradient(#FFB002, #FF7B02);
  background: -o-linear-gradient(#FFB002, #FF7B02);
  background: -ms-linear-gradient(#FFB002, #FF7B02);
  background: linear-gradient(#FFB002, #FF7B02);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-size: 16px;
}
.indicator-override {
  position: static;
  margin-bottom: 0;
}
.indicator-override li {
  width: 14px;
  height: 8px;
  border-radius: 50%;
  background: #949494;
}
.indicator-override li.active {
  background: #FFFFFF;
}
.modal-backdrop {
  z-index: 99 !important;
}
.modal-open .overlay-profile {
  z-index: 9992;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #leaderBoard {
    margin-bottom: 100px;
  }
}
