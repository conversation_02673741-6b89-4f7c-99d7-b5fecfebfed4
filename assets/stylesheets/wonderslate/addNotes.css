.modal-backdrop {
  display: none !important;
}
.btn-success {
  color: #FFFFFF !important;
  background-color: #27AE60 !important;
  border-color: #27AE60 !important;
}
.btn-success:hover,
.btn-success:focus {
  color: #FFFFFF !important;
  background-color: #27AE60 !important;
  border-color: #27AE60 !important;
}
.btn-light {
  color: #212121 !important;
  background-color: #FFFFFF !important;
  border-color: #949494 !important;
}
.btn-light:hover,
.btn-light:focus {
  color: #212121 !important;
  background-color: #FFFFFF !important;
  border-color: #949494 !important;
}
#namesSection {
  background: rgba(0, 0, 0, 0.9) !important;
}
#namesSection .modal-content {
  border-radius: 10px;
}
#namesSection .modal-header .close {
  position: absolute;
  right: 30px;
  top: -25px;
  padding: 0;
  font-weight: 300;
  color: #FFFFFF;
  opacity: 1;
}
@media (max-width: 991px) {
  #namesSection .modal-header .close {
    position: relative;
    right: 0;
    top: 0;
    padding: 1rem;
    font-weight: 400;
    color: 300;
  }
}
#namesSection .modal-header .close:focus {
  outline: 0;
}
#namesSection .modal-header .modal-title {
  color: #000000b3;
}
#namesSection .modal-body label.control-label {
  font-size: 13px;
  color: #949494;
}
#namesSection .modal-body input {
  font-size: 15px;
  color: #212121;
}
#namesSection .modal-body .form-control:focus {
  background-color: rgba(0, 0, 0, 0.03);
  border-color: rgba(0, 0, 0, 0.33);
  outline: 0;
  box-shadow: none;
}
#namesSection .modal-footer button {
  font-weight: 400;
}
#namesSection .modal-footer button.btn-success {
  min-width: 135px;
  letter-spacing: 1px;
  font-weight: 600;
}
#namesSection .bootstrap-select .dropdown-toggle {
  border: 1px solid #ced4da;
  color: #212121;
  font-weight: 400;
  height: 33.5px;
}
#namesSection .bootstrap-select .dropdown-toggle:hover {
  background-color: rgba(0, 0, 0, 0.03) !important;
  border-color: rgba(0, 0, 0, 0.33) !important;
}
#namesSection .bootstrap-select .dropdown-toggle:focus {
  background-color: #FFFFFF !important;
  outline: 0 !important;
}
#namesSection .bootstrap-select .dropdown-toggle:focus:hover {
  background-color: rgba(0, 0, 0, 0.03) !important;
}
#namesSection .bootstrap-select.show > .btn-light.dropdown-toggle {
  background-color: rgba(0, 0, 0, 0.03) !important;
  border-color: rgba(0, 0, 0, 0.33) !important;
}
#namesSection .bootstrap-select .dropdown-menu {
  border-color: #212121;
  width: 100%;
}
#namesSection .bootstrap-select .dropdown-menu li {
  border-color: #212121;
}
#namesSection .bootstrap-select .dropdown-menu li.no-results {
  font-size: 12px;
  color: #FF4B33;
  background: none;
}
#namesSection .bootstrap-select .dropdown-menu li .dropdown-item.active,
#namesSection .bootstrap-select .dropdown-menu li .dropdown-item .active:hover,
#namesSection .bootstrap-select .dropdown-menu li .dropdown-item :active {
  background-color: #17A2B8;
  color: #FFFFFF;
}
#namesSection .bootstrap-select .dropdown-menu li a {
  padding: 5px 20px;
  white-space: normal;
}
#namesSection .bootstrap-select .dropdown-menu li a:hover {
  background-color: #eee;
}
#namesSection .bootstrap-select .bs-searchbox .form-control {
  height: 30px;
}
@media (max-width: 575px) {
  #namesSection .modal-dialog {
    margin: 0;
  }
  #namesSection .modal-content {
    height: 100vh;
    border: none;
    border-radius: 0;
  }
}
.create_notes_section #editNotesBtn {
  text-decoration: none;
}
.create_notes_section #editNotesBtn i {
  font-size: 20px;
}
.create_notes_section .edit_notes span {
  font-size: 20px;
}
.create_notes_section .row-editor {
  box-shadow: 0 2px 4px #0000001A;
  border-radius: 7px;
}
.create_notes_section .row-editor .editor {
  min-height: 450px;
  border-radius: 7px !important;
}
.create_notes_section .row-editor .editor.ck-focused {
  box-shadow: none;
  border-color: rgba(0, 0, 0, 0.33) !important;
}
@media (max-width: 767px) {
  .create_notes_section .row-editor .editor {
    min-height: 350px;
  }
}
.create_notes_section #submit {
  letter-spacing: 1px;
}
