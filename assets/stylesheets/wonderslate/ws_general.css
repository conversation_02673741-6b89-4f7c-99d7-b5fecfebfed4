html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
li,
a,
input,
textarea,
select,
label,
span,
small,
strong,
button,
th,
td,
dl,
dt,
dd,
address {
  font-family: 'DM Sans', sans-serif !important;
}
.modal {
  z-index: 9992;
}
.loading-icon {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background: #000000B3;
  z-index: 9999;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-icon .loader-wrapper {
  background-image: url("../../images/wonderslate/preloader.webp");
  background-repeat: no-repeat;
  width: 200px;
  height: 200px;
  background-color: transparent;
  top: 0 !important;
  -webkit-transform: unset;
  -moz-transform: unset;
  -ms-transform: unset;
  -o-transform: unset;
  transform: unset;
  border-radius: 0;
  box-shadow: none;
}
.loading-icon .loader {
  display: none;
}
.loading-icon .loader::before,
.loading-icon .loader::after {
  display: none;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'DM Sans', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: #d4d4d4 !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'DM Sans', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: #d4d4d4 !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'DM Sans', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: #d4d4d4 !important;
}
.user_profile #profile #updateButton {
  background: #F79420;
  color: #FFFFFF;
  font-weight: 500;
}
.user_profile .change-password {
  color: #000000b3;
}
#change-password-modal .password-content button:last-child {
  color: #000000b3;
}
