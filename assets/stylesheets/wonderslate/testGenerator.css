.test-gen-main .test-gen-box-main .test-gen-box {
  border-radius: 7px;
}
.test-gen-main .test-gen-box-main .test-gen-box .test-gen-label {
  margin-bottom: 10px;
  font-size: 16px;
  background: #000000b3;
}
.test-gen-main .test-gen-box-main .test-gen-box .test-gen-desc {
  margin-bottom: 10px;
  line-height: 20px;
}
.test-gen-main .test-gen-box-main .test-gen-box .btn-info {
  background: #000000b3;
  border: none;
}
.test-gen-main .test-gen-box-main .test-gen-box .btn-info.btn-info:not(:disabled):not(.disabled):active:focus {
  box-shadow: 0 0 0 0.2rem #00000040;
}
#test-gen-modal .modal-header {
  text-align: center;
}
#test-gen-modal .modal-header #test-gen-modal-label {
  font-weight: 700;
  background: #000000b3;
  display: inline-flex;
}
#test-gen-modal .test-gen-modal-body .test-gen-books .book-item-link:hover {
  color: #000000b3;
}
#test-gen-modal .test-gen-modal-body .test-gen-books .book-item-link:hover .book-name-author p {
  color: #000000b3;
}
#test-gen-modal .test-gen-modal-body .test-gen-books .book-item-link .book-selected {
  background: #000000b3;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters {
  margin-bottom: 20px;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters .book-name {
  color: #000000b3;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters .chapter-selection-table {
  width: 80%;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters .chapter-selection-table .chapter-name:hover {
  color: #000000b3;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters .chapter-selection-table .chapter-name:hover input[type="checkbox"]:checked ~ .checkmark {
  border-color: #000000b3 !important;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters .chapter-selection-table .chapter-name label:hover {
  color: #000000b3 !important;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters .chapter-selection-table .chapter-name .checkmark:after {
  border-color: #000000b3 !important;
}
#test-gen-modal .test-gen-modal-body .test-gen-chapters .chapter-selection-table .chapter-name input[type="checkbox"]:checked ~ .checkmark {
  border-color: #000000b3 !important;
}
#test-gen-modal .modal-footer #next-button {
  background: #000000b3 !important;
  border: none !important;
}
#test-gen-modal .modal-footer #next-button:focus {
  background: #000000b3 !important;
  border: none !important;
}
#test-gen-modal .modal-footer #error_message {
  font-size: 16px;
  padding: 22px 10px !important;
  width: 100%;
}
#test-gen-modal .modal-footer #error_message i {
  line-height: 25px;
}
#quizModal .modal-header .quizTitle {
  font-weight: 700;
  background: #000000b3;
}
#quizModal .quiz-modal-body .current-question {
  background: #000000b3 !important;
  border: none !important;
}
#quizModal .quiz-modal-body .answered-question {
  background: #F79420 !important;
  border: none !important;
  color: #FFFFFF !important;
}
#quizModal .quiz-modal-body .mcq-question-div input[type="radio"]:checked ~ .checkmark {
  background: #F79420 !important;
  border: none !important;
  color: #FFFFFF !important;
}
#quizModal .quiz-modal-body .question-number-help-wrapper .question-number-help-dropdown .question-number-help-dropdown-list-item {
  display: flex;
  align-items: center;
}
#quizModal .quiz-modal-body .question-number-help-wrapper .question-number-help-dropdown .answered-indicator {
  background: #F79420 !important;
}
#quizModal .quiz-modal-body .question-number-help-wrapper .question-number-help-dropdown .current-indicator {
  background: #000000b3 !important;
}
#quizModal .quiz-modal-footer #quiz-Done,
#quizModal .quiz-modal-footer .btn-review {
  color: #000000b3 !important;
}
#quizModal .quiz-modal-footer .next-btn {
  background: #000000b3 !important;
  border: none !important;
}
#quizModal #score-container-div .practice-score-container {
  background: #000000b3 !important;
}
.book-content-wrapper {
  max-width: 220px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .test-gen-books-flex .col-md-3 {
    max-width: 50%;
  }
}
.book-image-wrapper {
  height: 200px;
  position: relative;
}
@media (max-width: 330px) {
  .book-image-wrapper {
    height: 132px;
  }
}
@media (max-width: 575.98px) {
  .book-image-wrapper {
    height: 152px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .book-image-wrapper {
    height: 172px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .book-image-wrapper {
    height: 182px;
  }
}
.book-image-wrapper .book-image {
  width: 100%;
}
.book-item-test-gen {
  padding: 8px;
  border: 3px solid transparent;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  transition: all 0.3s linear;
}
.book-item-test-gen:hover {
  box-shadow: 0 0 5px #0000001A;
  text-decoration: underline;
}
.border-css {
  padding: 8px;
  border: 3px solid #212121;
  border-radius: 8px;
}
.overlay-testgen-book {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.5);
}
.overlay-testgen-book .book-selected {
  text-align: center;
  width: 30px;
  height: 30px;
  left: -15px;
  margin: auto;
  top: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top-left-radius: 10px;
  background: transparent;
  border-radius: 0px;
  padding: 0;
}
.overlay-testgen-book .book-selected i {
  color: #FFFFFF;
}
.overlay-testgen-book .book-selected .text-radial-gradient {
  background: linear-gradient(to left, #000000b3 0%, #000000b3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.chapter-name {
  margin: 10px 20px;
  width: 100% !important;
  box-shadow: 0 2px 7px #0000001A;
  padding: 0 !important;
  margin-left: 0 !important;
  border-radius: 5px;
  transition: background-color 0.25s linear;
}
.chapter-name:hover {
  background-color: #f5f5f5;
}
.chapter-name label {
  width: 100%;
  padding: 16px;
}
.overlay-testgen-chapter {
  display: none;
  position: absolute;
  top: -6px;
  left: -4px;
  background-color: transparent;
}
.overlay-testgen-chapter .chapter-selected i {
  color: #000000b3;
}
.table-bordered {
  border: none;
}
.book-info .book-name {
  overflow: initial;
  text-overflow: initial;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-weight: 400;
  white-space: initial;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  text-align: left;
  margin-top: 8px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .book-info .book-name {
    font-size: 12px;
  }
}
.test-gen-chapters .chapter-selection-table {
  padding: 0 1rem;
}
.test-gen-chapters .chapter-selection-table .chapter-selection {
  border-top: none !important;
}
.test-gen-chapters .chapter-selection-table .chapter-name label {
  font-size: 14px;
  margin-bottom: 0;
  color: #212121;
  padding: 12px 16px;
  font-weight: 400;
}
.test-gen-chapters .chapter-selection-table .chapter-name label:hover {
  color: #212121;
}
.test-gen-chapters .chapter-selection-table .selected-chapter-name {
  border: 3px solid #000000b3 !important;
  border-radius: 5px;
}
.show-checkbox {
  display: flex;
}
.book-accordion {
  margin: 10px 0;
  padding: 16px;
  box-shadow: 0 2px 7px #0000001A;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  background: #fafafa;
  color: #212121;
  font-weight: 600;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.25s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book-accordion {
    font-size: 15px;
    padding: 12px 15px;
  }
}
.hidden {
  display: none !important;
}
#divi6 {
  margin-bottom: 40px;
}
#divi3 {
  margin-bottom: 40px;
}
.next-btn-testgen {
  padding: 8px 100px;
  background: #F79420 !important;
  color: #FFFFFF;
  min-width: 100px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px #0000001A;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .next-btn-testgen {
    background: #F79420 !important;
    position: fixed;
    z-index: 999;
    bottom: 10px;
    width: 90%;
    padding: 8px 50px;
  }
}
.custom-select {
  margin-bottom: 20px;
  border-color: 2px solid #000000b3 !important;
}
.test-type {
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .full-wdth-label p {
    font-size: 12px;
  }
}
.testgen-previous {
  display: flex;
  align-items: center;
}
.testgen-next-pre {
  display: flex !important;
  justify-content: center;
  flex-direction: row;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .testgen-next-pre {
    align-items: center;
    margin-bottom: 20px;
  }
}
#count-div {
  margin: 0 1rem 1rem;
  color: #6C757D;
}
#count-div p {
  font-size: 14px;
}
.test_generator {
  min-height: 500px;
  padding-top: 2rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .test_generator {
    padding-top: 1rem;
  }
}
.test_generator .page_title .back-arrow {
  color: #000000b3;
  font-size: 32px;
  margin-right: 1rem;
  cursor: pointer;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .test_generator .page_title .back-arrow {
    font-size: 26px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .test_generator .page_title h3 {
    font-size: 20px !important;
  }
}
.test_generator #score-container-div {
  max-height: unset;
}
.test_generator .quiz-modal-body {
  min-height: unset;
  max-height: unset;
  height: unset;
}
.test_generator #review-footer,
.test_generator #quiz-modal-footer {
  display: flex;
}
.test_generator .stpe-count {
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .test_generator .stpe-count {
    font-size: 12px;
    font-weight: 600;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .test_generator .mcq-question-div p.question-string {
    font-size: 14px;
  }
}
.test_generator .book-info .book-name {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.test_generator #error_message {
  font-size: 16px;
  max-width: 100%;
}
.test_generator #error_message i {
  font-size: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .test_generator #divi2 p.alert {
    font-size: 13px;
    line-height: normal;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .page_title {
    padding-bottom: 0 !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #count-div {
    text-align: center;
    box-shadow: 0 2px 4px #0000001A;
    margin: 1rem;
    border-radius: 4px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #count-div p {
    padding: 5px;
    font-size: 12px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .moreforyou,
  footer,
  .prepjoy_cta {
    display: none;
  }
}
@media (max-width: 330px) {
  .next-btn-testgen {
    padding: 8px 25px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .mobile-footer-nav {
    display: none !important;
  }
}
