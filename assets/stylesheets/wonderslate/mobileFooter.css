.mobile-footer-resource {
  background: #000000b3;
  box-shadow: 0 -4px 10px #0000001A;
  border-radius: 20px;
  height: 65px;
  position: fixed;
  width: 98%;
  bottom: 5px;
  z-index: 9991;
  left: 0;
  right: 0;
  margin: 0 auto;
}
.mobile-footer-resource button {
  background: none;
  border: none;
}
.mobile-footer-resource button:focus {
  outline: 0;
}
.mobile-footer-resource button i {
  color: #FFFFFF;
}
.mobile-footer-resource button span {
  display: block;
  color: #FFFFFF;
  font-size: 10px;
  margin-top: 5px;
}
.footer-menu-popover {
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.85) !important;
}
.footer-menu-popover.modal .modal-dialog {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
@media (min-width: 992px) {
  .footer-menu-popover {
    display: none !important;
  }
}
.top-drop-menu > div {
  box-shadow: 0 0 10px #0000001A;
  border-radius: 20px;
  padding: 8px 15px !important;
  min-height: 40px;
  display: flex;
  align-items: center;
  min-width: 170px !important;
}
.top-drop-menu > div i {
  font-size: 18px;
}
.top-drop-menu > div p {
  font-size: 12px;
}
div#addVideo,
div#addWeburl {
  z-index: 9992;
}
