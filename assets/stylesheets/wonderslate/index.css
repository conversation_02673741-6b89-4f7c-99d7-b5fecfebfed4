.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
#ws_landing .ask_doubts {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  z-index: 9992;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #ws_landing .ask_doubts {
    right: 0;
  }
}
#ws_landing .ask_doubts a {
  background-color: #9A309B;
  box-sizing: border-box;
  border-radius: 10px;
  padding: 12px 25px;
  height: 45px;
  color: #ffffff;
  font-weight: 300;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
#ws_landing .ask_doubts a:hover {
  text-decoration: none;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
#ws_landing .ask_doubts a:focus {
  background-color: #9A309B !important;
}
#ws_landing .ask_doubts a p {
  padding-left: 10px;
  width: 100%;
}
#ws_landing .hero_section h2 {
  font-weight: bold;
  padding-bottom: 0.5rem;
  font-size: 36px;
}
#ws_landing .hero_section .hero_subtitle h6 {
  font-size: 18px;
}
#ws_landing .hero_section h6 {
  font-family: 'Poppins', sans-serif;
}
#ws_landing .hero_section a .card {
  border-radius: 10px;
  color: #fff;
  border: none;
  height: 160px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
#ws_landing .hero_section a .card img {
  position: absolute;
  width: 38px;
  height: 50px;
  right: 0px;
  top: 0px;
}
#ws_landing .hero_section a .card .card-body {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
#ws_landing .hero_section a .card .card-body h5.card-title {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  margin-bottom: 7px;
}
#ws_landing .hero_section a .card .card-body p.card-text {
  font-size: 13px;
  line-height: 1.2;
}
#ws_landing .hero_section a .card.ebooks_bg {
  background: #ffffff;
}
#ws_landing .hero_section a .card.ebooks_bg h5 {
  color: #AA15EB;
  font-weight: bold;
}
#ws_landing .hero_section a .card.ebooks_bg p {
  color: #444444;
}
#ws_landing .hero_section a .card.live_classes_bg {
  background: #ffffff;
}
#ws_landing .hero_section a .card.live_classes_bg h5 {
  color: #E9AC00;
  font-weight: bold;
}
#ws_landing .hero_section a .card.live_classes_bg p {
  color: #444444;
}
#ws_landing .hero_section a .card.mymaterials_bg {
  background: #ffffff;
}
#ws_landing .hero_section a .card.mymaterials_bg h5 {
  color: #05B717;
  font-weight: bold;
}
#ws_landing .hero_section a .card.mymaterials_bg p {
  color: #444444;
}
#ws_landing .hero_section a .card.current_affairs_bg {
  background: #ffffff;
}
#ws_landing .hero_section a .card.current_affairs_bg h5 {
  color: #8CBD00;
  font-weight: bold;
}
#ws_landing .hero_section a .card.current_affairs_bg p {
  color: #444444;
}
#ws_landing .hero_section a:hover {
  text-decoration: none;
}
#ws_landing .hero_section a:hover .card {
  margin-top: -5px;
}
#ws_landing .hero_section .icon_modules div a:hover {
  text-decoration: none;
}
#ws_landing .hero_section .icon_modules img {
  height: 110px;
}
#ws_landing .partners_section .partner_title p {
  color: #444444;
  margin-bottom: 15px;
}
#ws_landing .partners_section .partner_title h2 {
  font-weight: bold;
}
#ws_landing .partners_section .partner_title h2 img {
  width: 220px;
}
#ws_landing .partners_section a .card {
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  height: 170px;
  margin: 0 auto;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
#ws_landing .partners_section a .card img {
  position: absolute;
  left: 0;
  right: 0;
  top: 40px;
  width: 60px;
  margin: 0 auto;
}
#ws_landing .partners_section a .card .card-body {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}
#ws_landing .partners_section a .card .card-body p {
  color: #949494;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
#ws_landing .partners_section a:hover {
  text-decoration: none;
}
#ws_landing .partners_section a:hover .card {
  margin-top: -5px;
}
#ws_landing .partners_section a:hover .card p {
  color: #7F28A8;
  font-weight: 500;
}
#ws_landing .partners_section .partners_message p {
  color: #444444;
}
#ws_landing .partners_section .know_more_btn a {
  background-color: #2EBAC6;
  padding: 15px 40px;
  border-radius: 50px;
  color: #FFFFFF;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
#ws_landing .partners_section .know_more_btn a:hover {
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
#ws_landing .partners_section .know_more_btn a:focus {
  background-color: #2EBAC6;
}
#ws_landing .partners_logos_section .partner_logos div {
  padding: 0 30px;
  margin-bottom: 20px;
}
#ws_landing .partners_logos_section .partner_logos div img {
  width: 180px;
}
#ws_landing .ws-cards-0 .card {
  box-shadow: 0px 4px 10px rgba(170, 21, 235, 0.3);
}
#ws_landing .ws-cards-1 .card {
  box-shadow: 0px 4px 10px rgba(5, 183, 23, 0.3);
}
#ws_landing .ws-cards-2 .card {
  box-shadow: 0px 4px 10px rgba(140, 189, 0, 0.3);
}
#ws_landing .ws-cards-3 .card {
  box-shadow: 0px 4px 10px rgba(233, 172, 0, 0.3);
}
.home-search {
  position: absolute;
  right: 0px;
  top: 1.5px;
  background-color: transparent !important;
}
