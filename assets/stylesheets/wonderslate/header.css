.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
a,
h1,
p {
  font-family: 'Poppins', sans-serif;
}
header {
  background: #ffffff;
  width: 100%;
  border-bottom: 1px solid #ECECEC;
  border-top: 1px solid #ECECEC;
  align-items: center;
  z-index: 999;
  position: relative;
  padding: 0 !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  header {
    background: #ffffff;
    height: 71px;
    width: 100%;
    position: sticky;
    top: 0;
  }
}
header.addSticky {
  position: fixed;
  top: 0;
  z-index: 999;
  height: 65px;
  box-shadow: 0px 1px 5px rgba(190, 190, 190, 0.46);
  -webkit-box-shadow: 0px 1px 5px rgba(190, 190, 190, 0.46);
  -moz-box-shadow: 0px 1px 5px rgba(190, 190, 190, 0.46);
  border-color: transparent;
  transition: all 1s;
}
header.addSticky:before {
  height: 90px;
}
header.addSticky:after {
  height: 35px;
}
header.addSticky .shape,
header.addSticky .shape1 {
  height: 70px;
}
header:before {
  content: '';
  position: absolute;
  right: -20px;
  top: -20px;
  background-image: url('../../images/ws/baloon.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 120px;
  height: 100px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  animation: float_left_right 10s infinite;
  -webkit-animation: float_left_right 10s infinite;
  -moz-animation: float_left_right 10s infinite;
  -ms-animation: float_left_right 10s infinite;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  header:before {
    height: 85px;
  }
}
header:after {
  content: '';
  position: absolute;
  right: 85px;
  top: -10px;
  background-image: url('../../images/ws/smallbaloon.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 40px;
  height: 45px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  animation: float_left_right 8s infinite;
  -webkit-animation: float_left_right 8s infinite;
  -moz-animation: float_left_right 8s infinite;
  -ms-animation: float_left_right 8s infinite;
}
header.normalHeader {
  display: flex;
}
.ws-header .right-menu {
  display: flex;
}
.ws-header .right-menu img {
  width: 32px;
  height: 32px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ws-header .right-menu {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    background: #000;
    margin: 0 !important;
    padding: 0 !important;
    z-index: 999;
    height: 100%;
    display: none;
  }
}
@media only screen and (min-width: 1024px) and (max-height: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) and (hover: none) {
  .ws-header .right-menu {
    position: static;
    display: flex;
    background: none;
    justify-content: flex-end;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ws-header .right-menu li:first-child {
    margin-top: 3rem !important;
  }
}
@media only screen and (min-width: 1024px) and (max-height: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) and (hover: none) {
  .ws-header .right-menu li:first-child {
    margin-top: 0 !important;
  }
}
.ws-header .right-menu li.dropdown .dropdown-menu {
  padding-bottom: 0;
  margin-top: -5px;
  margin-left: auto;
}
.ws-header .right-menu li.dropdown .dropdown-menu.dropdown-menu-right {
  min-width: 370px;
}
.ws-header .right-menu li.dropdown .dropdown-menu .media {
  position: relative;
}
.ws-header .right-menu li.dropdown .dropdown-menu .media .media-body p {
  color: #212121;
}
.ws-header .right-menu li.dropdown .dropdown-menu .dropdown-item {
  padding: 0.5rem 1rem;
  font-weight: normal;
}
.ws-header .right-menu li.dropdown .dropdown-menu .dropdown-item:last-child {
  border-bottom: none;
}
.ws-header .right-menu li.dropdown .dropdown-menu a {
  position: relative;
}
.ws-header .right-menu li.dropdown .dropdown-menu .edit-btn {
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 50%;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: absolute;
  left: 70px;
  bottom: 30px;
  text-align: center;
  background: #fff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ws-header .right-menu li.dropdown .dropdown-menu .edit-btn {
    right: 80px;
  }
}
.ws-header .right-menu li.dropdown .dropdown-menu .edit-btn i {
  font-size: 10px;
  margin-top: 3px;
}
.ws-header .right-menu li a img {
  width: 46px;
  height: 46px;
}
.ws-header .right-menu li img.drop-profile {
  width: 72px;
  height: 72px;
}
.ws-header .right-menu li:last-child a {
  cursor: pointer;
}
.ws-header .navbar-brand {
  margin-left: 4rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ws-header .navbar-brand {
    margin-left: 3rem;
  }
}
.ws-header .logo {
  width: 32px;
  height: 32px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ws-header .logo {
    width: 18px;
    height: 18px;
  }
}
.ws-header .logotext {
  width: 175px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ws-header .logotext {
    width: 100px;
  }
}
.body-background {
  background: url('../../images/ws/wsbodybg.svg') center center no-repeat;
  height: 600px;
  background-size: cover;
  position: fixed;
  top: 0;
  width: 100%;
  opacity: 0.5;
}
.shaper {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
}
.baloon {
  position: fixed;
  right: -15px;
  z-index: 999;
  top: -15px;
  width: 120px;
}
.shape {
  position: absolute;
  z-index: 99999;
}
.shapemobile {
  background: url("../../images/ws/shapemobile.svg") center center no-repeat;
  width: 85px;
  height: 100px;
  position: fixed;
  z-index: 999999;
  left: -12px;
  top: -12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.shapemobile i {
  color: #fff;
  font-size: 30px;
  position: absolute;
  bottom: 40px;
}
.admin-wrapper {
  background: none !important;
}
.navbar-expand-lg .navbar-nav .nav-link {
  padding-right: 1rem;
  padding-left: 0.5rem;
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;
  white-space: nowrap;
  cursor: pointer;
}
.navbar-expand-lg .navbar-nav .nav-link:focus {
  -webkit-text-fill-color: #CE3E81;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .navbar-expand-lg .navbar-nav .nav-link:focus {
    -webkit-text-fill-color: #fff;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .navbar-expand-lg .navbar-nav .nav-link {
    background: none;
    color: #fff;
    -webkit-text-fill-color: #fff;
  }
}
@media only screen and (min-width: 1024px) and (max-height: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) and (hover: none) {
  .navbar-expand-lg .navbar-nav .nav-link {
    background: -webkit-linear-gradient(#862AA5, #CE3E81);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.navbar-expand-lg .navbar-nav .nav-link.loginButton {
  font-weight: 700;
}
.navbar-expand-lg .navbar-nav .nav-link.signupbutton {
  font-weight: 700;
}
.navbar-expand-lg .navbar-nav .nav-item.active.active .nav-link {
  font-weight: 500;
}
.navbar-expand-lg .dropdown-toggle::after {
  color: #862AA5;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .navbar-expand-lg .dropdown-toggle::after {
    color: #fff;
  }
}
.navbar-expand-lg .mobile-profile img {
  width: 24px;
}
.close-mobile {
  background: none;
  border: none;
  position: absolute;
  right: 10px;
  top: 10px;
}
.close-mobile i {
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .mobileModalFix {
    position: fixed;
    bottom: 0;
    margin: 0;
    left: 0;
  }
  .mobileModalFix > div {
    padding: 0;
  }
}
.modal-mobile-drop {
  position: absolute;
  width: 100%;
  height: 100%;
  background: black;
}
@-webkit-keyframes float_left_right {
  0% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
}
@keyframes float_left_right {
  0% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
  50% {
    -webkit-transform: translateX(10px);
    transform: translateX(10px);
  }
  100% {
    -webkit-transform: translateX(-10px);
    transform: translateX(-10px);
  }
}
.material-icons {
  font-family: 'Material Icons' !important;
}
