.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
.my_books .page_title .back-arrow {
  color: #7F28A8;
  font-size: 32px;
  margin-right: 1rem;
  cursor: pointer;
}
.my_books .nav-tabs {
  background: radial-gradient(99.37% 2306.89% at 2.52% 6.82%, #962F9D 0%, #C73D84 100%);
  color: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  border-radius: 5px;
  padding-right: 0;
  position: relative;
}
.my_books .nav-tabs .divider {
  display: block !important;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 5px;
  margin: 0 auto;
}
.my_books .nav-tabs .divider img {
  height: 60px;
  -webkit-filter: brightness(0) invert(1);
  filter: brightness(0) invert(1);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .my_books .nav-tabs .divider img {
    height: 50px;
  }
}
.my_books .nav-tabs li.nav-item {
  width: 100%;
}
.my_books .nav-tabs li.nav-item:last-child {
  margin-left: 0;
}
.my_books .nav-tabs li.nav-item .nav-link {
  color: #ffffff;
  border: none;
  margin: 0;
  padding: 15px 0;
  font-weight: 300;
  font-size: 18px;
  height: 60px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .my_books .nav-tabs li.nav-item .nav-link {
    padding: 12px 0;
    height: 50px;
    font-size: 16px;
  }
}
.my_books .nav-tabs li.nav-item .nav-link:hover {
  border: none;
}
.my_books #books .username p.total-books {
  font-size: 18px;
  color: #98309C;
}
.my_books #books #subjectFilter .dropdown {
  font-size: 13px;
  font-family: 'Poppins', sans-serif;
}
.my_books #books #subjectFilter .dropdown #sortBy {
  border-radius: 5px;
  color: #98309C;
  font-size: 13px;
  border-color: #98309C;
}
.my_books #books .generate {
  background: radial-gradient(99.37% 2306.89% at 2.52% 6.82%, #962F9D 0%, #C73D84 100%);
  box-shadow: inset 0px 2px 4px rgba(0, 0, 0, 0.15);
  border: none;
  color: #fff;
}
.my_books #books .generate:active:focus {
  box-shadow: none !important;
}
.my_books #content-data-books h4 {
  color: #7F28A8;
  font-weight: 600;
  font-size: 18px;
}
.my_books #content-data-books .card {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background-color: #ffffff;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  margin-bottom: 20px;
}
.my_books #content-data-books .card:hover {
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .my_books .tab-content {
    margin-top: 1rem;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .my_books {
    padding-top: 1rem;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .my_books #libraryAssignment .additional-assignment-item .additional-assignment-teacher-img {
    width: 140px;
  }
}
@media only screen and (max-width: 767px) {
  .shapemobile {
    display: none;
  }
  .shapemobile.backMenu {
    display: flex !important;
  }
  .back-arrow {
    display: none;
  }
  .show-queue .shapemobile {
    display: flex;
  }
  .show-queue .shapemobile i {
    right: 25px;
  }
  .library .tab-content .card .lib-showcase img {
    height: 192px;
  }
  .recent-read-books-list {
    overflow: auto;
    flex-wrap: nowrap;
  }
}
.img-hero {
  position: absolute;
  /* left: 0px; */
  z-index: 99;
  border-radius: 4px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
}
.img-child {
  position: absolute;
  left: 7px;
  top: 5px;
  border-radius: 4px;
}
.no-books-available p {
  color: rgba(68, 68, 68, 0.48);
}
.no-books-available .click-here-link {
  background: radial-gradient(117.19% 9782.23% at 3.77% 0%, #FFE457 0%, #FFB800 100%);
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  border: none;
  color: rgba(68, 68, 68, 0.85);
  font-weight: bold;
  font-size: 15px;
}
#infiniteLoading .loading-div p {
  position: relative;
  letter-spacing: 1px;
  color: #2C3E50;
  font-size: 15px;
}
#infiniteLoading .loading-div img {
  width: 120px;
  margin-top: -55px;
}
#infiniteLoading .loading-div .alert {
  font-size: 14px;
}
.showMore,
.showLess {
  float: right;
  margin-right: 35px;
  font-size: 14px;
  color: #C73D84;
  cursor: pointer;
}
@media (max-width: 1024px) {
  .showMore,
  .showLess {
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  .showMore,
  .showLess {
    display: none;
  }
}
.search-icon-lib {
  background: none;
  border-radius: 50px;
  width: 36px;
  height: 36px;
  border: 2px solid #9B319A;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
}
.search-icon-lib:focus {
  outline: 0;
}
.search-icon-lib i {
  color: #9B319A;
}
.search-box input.search {
  border-color: #98309C;
  color: #98309C;
}
.search-box input.search:focus {
  box-shadow: none;
  border-right: none;
}
.search-box .submit-search-btn {
  height: 33.5px;
  border: 1px solid #98309C;
  border-left: 0;
  color: #98309C;
  background: none;
}
.search-box .submit-search-btn:focus,
.search-box .submit-search-btn:active {
  outline: 0 !important;
  box-shadow: none !important;
}
.search-box ul.typeahead {
  top: 53px !important;
  border-color: #98309C;
  width: 100%;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
}
.search-box ul.typeahead li a {
  white-space: pre-wrap;
}
.search-box ul.typeahead li a:active {
  color: #16181b;
}
#content-data-institute-books h4,
#institute-recent-read-books h4,
#content-data-search-books h4,
#content-data-books-queue h4 {
  color: #2C8FB7;
  font-weight: normal;
  font-size: 18px;
  padding: 10px 0;
}
@media (max-width: 767px) {
  #content-data-institute-books h4,
  #institute-recent-read-books h4,
  #content-data-search-books h4,
  #content-data-books-queue h4 {
    font-size: 16px;
  }
}
#content-data-institute-books .card,
#institute-recent-read-books .card,
#content-data-search-books .card,
#content-data-books-queue .card {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background-color: #ffffff;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  margin-bottom: 20px;
}
#content-data-institute-books .card:hover,
#institute-recent-read-books .card:hover,
#content-data-search-books .card:hover,
#content-data-books-queue .card:hover {
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
#content-data-institute-books .card a,
#institute-recent-read-books .card a,
#content-data-search-books .card a,
#content-data-books-queue .card a {
  cursor: pointer;
}
#content-data-books-queue .no-books-available p {
  font-size: 15px;
}
#content-data-books-queue .card-body .dropdown .dropdown-toggle::after {
  display: none;
}
#content-data-books-queue .card-body .dropdown-menu {
  top: -15px !important;
  left: -110px !important;
}
#content-data-books-queue .card-body .dropdown-menu .delete {
  font-size: 12px;
}
#content-data-books-queue .card-body .card-text:hover {
  text-decoration: none;
  color: #444444;
}
#content-data-books-queue .card img {
  border-radius: 4px;
}
#bookQueueModal {
  background: rgba(0, 0, 0, 0.85) !important;
  z-index: 9991;
}
#bookQueueModal .modal-body h5 {
  color: #27AE60;
  font-weight: normal;
  font-size: 18px;
  font-family: 'Poppins', sans-serif;
  line-height: normal;
  margin-top: 20px;
}
@media (max-width: 767px) {
  #bookQueueModal .modal-body h5 {
    font-size: 16px;
  }
}
#bookQueueModal .modal-body p {
  color: rgba(68, 68, 68, 0.48);
}
#bookQueueModal .modal-body p strong {
  font-weight: 600;
}
@media (max-width: 767px) {
  #bookQueueModal .modal-body p {
    font-size: 13px;
  }
  #bookQueueModal .modal-body p br {
    display: none;
  }
}
#bookQueueModal .close {
  position: absolute;
  right: 15px;
  top: -50px;
  padding: 0;
  font-weight: 100;
  font-size: 30px;
  color: #fff;
  opacity: 1;
}
#bookQueueModal .close:focus,
#bookQueueModal .close:active {
  outline: 0;
  box-shadow: none;
}
#bookQueueModal .modal-green-btn {
  background: #27AE60;
  font-weight: normal;
}
#bookQueueModal .modal-green-btn-outline:hover,
#bookQueueModal .modal-green-btn-outline:focus,
#bookQueueModal .modal-green-btn-outline:active,
#bookQueueModal .modal-green-btn-outline:focus:active {
  color: #27AE60;
  background-color: transparent;
  background-image: none;
  border-color: #27AE60;
}
@media (max-width: 767px) {
  #bookQueueModal .modal-dialog-queue-content {
    align-items: flex-end;
    padding-bottom: 0px;
    max-width: 100%;
    margin: 0px;
    height: 100%;
  }
  #bookQueueModal .modal-queue-content {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
  }
}
.select-institute-dropdown {
  width: 250px;
}
@media (max-width: 767px) {
  .select-institute-dropdown {
    width: 175px;
  }
}
@media (max-width: 575px) {
  .select-institute-dropdown {
    width: 210px;
  }
}
@media (max-width: 350px) {
  .select-institute-dropdown {
    width: 175px;
  }
}
.select-institute-dropdown .dropdown-toggle {
  height: 44px;
  color: #444;
  width: 250px;
  text-align: left;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
  background: #fff;
  border-radius: 36px;
  border: none;
}
@media (max-width: 767px) {
  .select-institute-dropdown .dropdown-toggle {
    height: 42px;
    width: 175px;
    font-size: 12px;
  }
}
@media (max-width: 575px) {
  .select-institute-dropdown .dropdown-toggle {
    width: 210px;
  }
}
@media (max-width: 350px) {
  .select-institute-dropdown .dropdown-toggle {
    width: 175px;
  }
}
.select-institute-dropdown .dropdown-toggle.generate {
  background: radial-gradient(102.7% 1720.67% at 3.21% 8.11%, #BF3B89 0%, #932E8C 100%) !important;
}
.select-institute-dropdown .dropdown-toggle:after {
  position: absolute;
  right: 15px;
  top: 20px;
}
@media (max-width: 767px) {
  .select-institute-dropdown .dropdown-toggle:after {
    right: 10px;
  }
}
.select-institute-dropdown .dropdown-toggle #selectedInstitute {
  width: 200px;
  position: relative;
  display: block;
  word-break: break-word;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
@media (max-width: 575px) {
  .select-institute-dropdown .dropdown-toggle #selectedInstitute {
    width: 180px;
  }
}
@media (max-width: 350px) {
  .select-institute-dropdown .dropdown-toggle #selectedInstitute {
    width: 145px;
  }
}
#institute-list {
  transform: translate3d(0px, 36px, 0px) !important;
  background: #FFFFFF;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  width: 100%;
  z-index: 991;
}
#institute-list li.dropdown-item {
  white-space: pre-wrap;
}
@media (max-width: 767px) {
  #institute-list li.dropdown-item {
    font-size: 12px;
  }
}
#institute-list li.dropdown-item:hover {
  cursor: pointer;
  color: #7F28A8;
}
#institute-list li.dropdown-item:focus,
#institute-list li.dropdown-item:active {
  background-color: transparent;
  outline: 0;
}
.myself-btn {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
  color: #444;
  border-radius: 50px;
  width: 120px;
}
.myself-btn:focus {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
}
@media (max-width: 767px) {
  .myself-btn {
    width: 100px;
    font-size: 12px;
    padding: 5px;
  }
}
.queue-list-btn {
  position: relative;
  z-index: 99;
}
.queue-list-btn a {
  background: #FFFFFF;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15) !important;
  border-radius: 36px;
  color: #444444;
  margin-right: 30px;
}
.queue-list-btn a:hover {
  color: #98309C;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25) !important;
}
@media (max-width: 1024px) {
  .queue-list-btn a {
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  .queue-list-btn a {
    margin-right: 0;
  }
}
@media (max-width: 350px) {
  .queue-list-btn a {
    font-size: 12px;
    padding: 0.5rem;
  }
}
.new_way {
  position: relative;
  background: #88b7d5;
  width: 100px;
  text-align: center;
  margin: 0 auto;
  transform: rotate(180deg);
  box-shadow: rgba(0, 0, 0, 0.3) 0 1px 4px -1px;
}
.new_way:after {
  content: '';
  position: absolute;
  top: -5px;
  left: calc(50% - 10px);
  background: #fff;
  width: 20px;
  height: 20px;
  box-shadow: rgba(0, 0, 0, 0.3) 0 1px 4px -1px;
  clip-path: polygon(0 0, 100% 0, 50% 100%);
}
.dropup .dropdown-menu .delete:focus,
.dropdown .dropdown-menu .delete:focus,
.dropup .dropdown-menu .delete:active,
.dropdown .dropdown-menu .delete:active {
  outline: 0;
  background-color: transparent;
}
.submit_icon span {
  font-size: 70px;
}
.submit_icon p {
  color: #777;
}
.fetch_icon span,
.invalid_icon span {
  opacity: 0.7;
}
.package_book_collapse {
  position: relative;
  margin: 0;
}
.package_books {
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  position: relative;
  left: 0;
  right: 0;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  z-index: 99;
}
.package_books .package_book_list {
  padding-bottom: 0;
}
.package_books .package_book_list:last-child {
  padding-bottom: 0;
}
.package_books .package_book_list a {
  color: #000000;
  width: 85px;
  font-size: 11px;
  margin: 0 10px;
}
.package_books .package_book_list a:hover {
  color: #F79420;
}
.package_books .package_book_list a span {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
#search-book {
  margin-bottom: -25px;
}
.fadein-animated {
  animation: fadein 1.5s;
  -moz-animation: fadein 1.5s;
  -webkit-animation: fadein 1.5s;
  -o-animation: fadein 1.5s;
}
@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-o-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.libwonder .select-institute-dropdown .dropdown-toggle.generate {
  display: block !important;
  background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #358EF0 0%, #394696 100%) !important;
}
.libwonder #content-data-books-queue .generate {
  display: inline-block !important;
  color: #444444 !important;
}
.libwonder #content-data-books-queue .generate:hover {
  color: #358EF0 !important;
}
.libwonder #institute-list li.dropdown-item:hover {
  color: #358EF0;
}
.libwonder .show-queue .shapemobile i {
  font-size: 28px;
  margin-right: 10px;
}
.libwonder .show-queue .no-books-available .click-here-link {
  background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #358EF0 0%, #394696 100%);
  color: #ffffff;
  font-weight: normal;
}
.libwonder .queue-list-btn a:hover {
  color: #358EF0;
}
.libwonder .my_books .page_title {
  padding-bottom: 0 !important;
}
.libwonder .my_books .page_title h3 {
  -webkit-text-fill-color: inherit;
}
.libwonder .my_books .page_title.show-library .back-arrow {
  display: none;
}
.libwonder .my_books .page_title .back-arrow {
  color: #444444;
}
.libwonder .search-icon-lib,
.libwonder .search-icon-lib i,
.libwonder .search-box input.search,
.libwonder .search-box .submit-search-btn,
.libwonder .search-box ul.typeahead {
  border-color: #444444;
  color: #444444;
}
.libwonder .book-publisher-name {
  text-transform: uppercase;
  color: #ABABAB;
}
.libwonder #books .username p.total-books {
  color: #444444;
}
.my_books #books .card-body .card-text {
  height: auto;
}
