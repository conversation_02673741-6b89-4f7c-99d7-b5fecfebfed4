.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
@media only screen and (max-width: 767px) {
}
.dashboard_section .dashboard_notify {
  position: relative;
}
@media only screen and (max-width: 767px) {
  .dashboard_section .dashboard_notify {
    position: absolute;
    right: 20px;
  }
}
.dashboard_section .dashboard_notify a span {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  border-radius: 50px;
  font-size: 18px;
  color: #ffffff;
}
.dashboard_section .dashboard_notify a small {
  position: absolute;
  background: #fff;
  padding: 1px;
  width: 18px;
  height: 18px;
  text-align: center;
  font-size: 8px;
  border-radius: 50px;
  border: 2px solid #B03790;
  color: #B03790;
  font-weight: bold;
  right: -6px;
  top: -3px;
}
.dashboard_section .test_generate_btn a {
  background-color: #7F28A8;
  color: #ffffff;
  font-weight: normal;
  font-size: 13px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}
.dashboard_section .test_generate_btn a i {
  font-size: 16px;
}
.dashboard_section .library-btn {
  position: relative;
}
.dashboard_section .library-btn a {
  background: radial-gradient(99.37% 2306.89% at 2.52% 6.82%, #962F9D 0%, #C73D84 100%);
  color: #ffffff;
  height: 45px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}
.dashboard_section .library-btn a span {
  border: 1.5px solid #fff;
  padding: 5px;
  border-radius: 50px;
  font-size: 16px;
  font-weight: normal;
}
.dashboard_section .library-btn a:before {
  content: '';
  position: absolute;
  top: -30px;
  right: -30px;
  background-image: url("../../images/ws/library-btn-bg.svg");
  background-repeat: no-repeat;
  width: 150px;
  height: 120px;
}
.dashboard_section .todo-btn a {
  background: radial-gradient(165.91% 995.76% at 82.35% 156.82%, #219653 0%, #6FCF97 100%);
  color: #ffffff;
  height: 45px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}
.dashboard_section .tracker-btn a {
  background: radial-gradient(107.95% 1161.73% at 3.92% 7.95%, #FAC421 0%, #FC851B 100%);
  color: #ffffff;
  height: 45px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}
.dashboard_section .dashboard-records .card {
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
.dashboard_section .dashboard-records #dashboardTabs {
  position: relative;
}
.dashboard_section .dashboard-records #dashboardTabs li.line-seperator {
  position: absolute;
  right: 0;
  top: 10px;
  bottom: 0;
  left: 0;
  height: 65px;
  width: 1px;
  margin: 0 auto;
}
.dashboard_section .dashboard-records #dashboardTabs .nav-link {
  border: none !important;
  padding: 1rem;
  color: #949494;
  font-size: 15px;
}
.dashboard_section .dashboard-records #dashboardTabs .nav-link.active {
  font-weight: bold;
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.dashboard_section .dashboard-records #dashboardTabs .nav-link:focus,
.dashboard_section .dashboard-records #dashboardTabs .nav-link:active,
.dashboard_section .dashboard-records #dashboardTabs .nav-link:focus:active {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  color: #7F28A8 !important;
}
.dashboard_section .dashboard-records .all-container .container-wrapper {
  width: auto;
  box-shadow: none;
  border: none;
  margin-top: 0 !important;
  position: relative;
  min-height: 108px;
}
.dashboard_section .dashboard-records .all-container .container-wrapper:after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  background-image: url("../../images/ws/horizontal-separator-line.svg");
  background-repeat: no-repeat;
  background-size: contain;
  width: 100%;
  height: 4px;
}
.dashboard_section .dashboard-records .all-container .container-wrapper:last-child:after {
  display: none;
}
.dashboard_section .dashboard-records .all-container .container-wrapper .media {
  box-shadow: none;
}
.dashboard_section .dashboard-records .all-container .container-wrapper .media .readnow {
  text-transform: none !important;
}
.dashboard_section .dashboard-records .all-container .container-wrapper .media p {
  margin: 0;
}
.dashboard_section .dashboard-records .all-container .container-wrapper .media .media-body p.title {
  color: #212121;
  font-size: 14px;
}
.dashboard_section .dashboard-records .all-container .container-wrapper .media .media-body p.book_name {
  color: #444444;
}
.dashboard_section .dashboard-records .all-container .container-wrapper .media .media-body p.updated_info {
  color: #949494;
}
.dashboard_section .dashboard-records #showMore a {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  color: #ffffff;
  font-size: 12px;
  padding: 2px;
  border-radius: 0 0 10px 10px;
}
.dashboard_section .dashboard-records #showMore a:hover {
  text-decoration: none;
}
.dashboard_section .dashboard-records .empty_activity p {
  color: #949494;
}
.dashboard_section .test_generate_mobile_action {
  background: radial-gradient(94.93% 433.83% at 1.6% 0%, #D13F7F 0%, #8129A7 100%);
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px;
  bottom: 5px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 98%;
  height: 65px;
  position: fixed;
  z-index: 9991;
  padding: 15px 20px;
}
.dashboard_section .test_generate_mobile_action a.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  color: #9A309B;
  border-color: #ffffff;
  font-size: 12px;
  font-weight: bold;
  height: 36px;
  border-radius: 9px;
  min-width: 230px;
}
.dashboard_section .test_generate_mobile_action a.btn i {
  font-size: 16px;
  font-weight: bold;
}
.dashboard_section .test_generate_mobile_action a.btn:focus {
  background: #ffffff !important;
}
.dashboard_section .test_generate_mobile_action span i {
  color: #ffffff;
}
