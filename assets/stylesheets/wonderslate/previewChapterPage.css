body {
  background: #fff !important;
}
main {
  min-height: 74vh;
}
.previewChapter__title {
  margin-top: 2rem;
}
.previewChapter__title h1 a {
  font-size: 28px;
}
.mobile-footer-nav {
  display: none;
}
.previewChapter__buyBtnSec {
  background: #fff;
  position: fixed;
  width: 100%;
  bottom: 0;
  padding: 1rem;
  display: flex;
  justify-content: center;
  z-index: 999;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}
.previewChapter__buyBtnSec a {
  display: block;
  background: #FBBC04;
  width: 200px;
  padding: 10px 12px;
  border-radius: 5px;
  text-align: center;
  color: #000;
  font-weight: 500;
  font-size: 16px;
  text-decoration: none;
}
.bookChaptersList {
  width: 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0 auto;
}
.tocTitle {
  text-align: center;
}
.chaptersList {
  list-style: none;
  padding-left: 0px;
}
.chaptersList-item {
  padding: 1rem;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.chaptersList-item:last-child {
  border-bottom: none;
}
iframe {
  border: none;
}
.previewTag {
  background: #f5f6f7;
  padding: 6px;
  width: 120px;
  text-align: center;
  border-radius: 5px;
  font-weight: lighter;
}
.footer-menus ul {
  line-height: 35px;
}
.digitalLib-btn a {
  color: #000 !important;
}
.previewChapter__reader {
  min-height: 963px;
  width: 100%;
}
.previewWrapper {
  display: flex;
  gap: 1rem;
}
.coverImg {
  margin-top: 2rem;
}
.coverImgMob {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .bookChaptersList {
    width: 100%;
  }
  .previewChapter__title h1,
  .previewChapter__title p {
    display: flex;
    justify-content: center;
  }
  .previewChapter__title p a {
    margin-left: 2px;
  }
  .navbar-header .navbar-nav .nav-item .nav-link.login-menu-link {
    background: #000 !important;
  }
  .previewChapter__reader {
    min-height: 800px;
  }
  .coverImgMob {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
  }
  .coverImg {
    display: none;
  }
}
