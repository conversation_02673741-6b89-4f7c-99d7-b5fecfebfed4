.notes-by-user li {
  padding: 10px;
}
.notes-by-user li .comment-bg {
  background: rgba(255, 255, 10, 0.3);
}
h2 {
  font-size: 16px;
}
.chapter-notes {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.mynotes h3 {
  margin-top: 1rem;
  margin-bottom: 8px !important;
  color: #000;
  font-size: 16px;
  font-weight: 700;
  text-align: left !important;
  text-transform: capitalize;
  margin-left: 1rem;
}
html {
  padding: 0;
  margin: 0;
  border: 1px solid #000;
}
body {
  padding: 0 1rem;
  margin: 0;
}
.logotext {
  padding: 0.5rem;
  width: 150px;
}
.line {
  border-bottom: 1px solid #444444;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
body {
  counter-reset: section;
}
.flashcard-print {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  align-items: center;
  padding: 1rem;
  width: 100%;
}
.flashcard-print::before {
  counter-increment: section;
  content: counter(section) ": ";
  font-size: 18px;
}
.flashcard-print > div {
  margin: 0 auto;
  width: 40%;
  border: 1px solid #000;
  padding: 5px;
}
.flashcard-print > div:last-child {
  margin-left: 10px;
}
.flashcard-print h4 {
  font-size: 14px;
  font-weight: normal;
}
.hero-title {
  font-size: 18px;
  font-weight: bold;
  margin-top: 1rem;
  text-align: center;
}
