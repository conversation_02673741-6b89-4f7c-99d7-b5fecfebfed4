footer {
  background: #212121;
  min-height: 344px;
  position: relative;
}
footer .footer-fluid {
  padding-top: 2rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  footer .footer-fluid {
    padding-bottom: 5rem;
    padding-top: 0;
  }
}
footer .footer_logo_wrapper {
  padding: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  footer .footer_logo_wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  footer .footer_logo_wrapper .desktop {
    padding-left: 1rem;
    margin-left: 1rem;
    border-left: 1px solid #949494;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  footer .ipadVis {
    display: flex;
  }
}
footer .ipadVis #explore p {
  color: #F79420;
  font-size: 1.5rem;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  footer .ipadVis #explore {
    margin-left: 100px;
  }
}
footer #vl {
  border-left: 2px solid #FFFFFF;
  height: 150px;
  display: none;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  footer #vl {
    display: block;
  }
}
footer #featuredPublishers ul li {
  color: white;
}
footer .explore-section {
  display: flex;
  justify-content: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  footer .explore-section {
    justify-content: left;
  }
}
footer .mobile {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  footer .mobile {
    display: flex;
  }
}
@media screen and (max-width: 991px) {
  footer {
    min-height: 400px;
    z-index: inherit;
  }
}
.widget-ftr ul {
  padding: 0;
  list-style: none;
  margin-bottom: 1rem;
}
.widget-ftr h4,
.widget-ftr p {
  color: #F76e11 !important;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}
.widget-ftr a {
  color: #FFFFFF !important;
}
.widget-ftr a:hover {
  color: #F79420 !important;
  text-decoration: underline;
}
.widget-ftr button {
  font-size: 16px;
  font-weight: 700;
  border: none;
}
.publisher-btn {
  background: #F76E11;
  color: #FFFFFF;
  margin-bottom: 1rem;
}
.digitalLib-btn {
  background: #2fbdca;
  color: #FFFFFF !important;
}
.digitalLib-btn a {
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.digitalLib-btn a:hover {
  color: #FFFFFF !important;
  text-decoration: none;
}
.copyright-footer {
  font-size: 12px;
  color: #FFFFFF !important;
  text-align: left !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .copyright-footer {
    text-align: center !important;
  }
}
@media (max-width: 575.98px) {
  .accept-payments img {
    width: 100% !important;
    height: auto !important;
  }
}
.footer-secRow {
  border-top: 1px solid #FFFFFF;
  margin: 20px 0 0;
  padding: 10px 0 0;
}
.company-name {
  font-size: 12px;
  display: flex;
  justify-content: end;
  color: #FFFFFF;
  align-self: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .company-name {
    justify-content: center !important;
  }
}
.social-icons {
  justify-content: flex-start !important;
}
.logo-wrapper-new {
  text-align: center;
}
.download-btns {
  margin: 0 auto;
  display: flex;
  padding: 0.5rem;
  justify-content: center;
  background-color: transparent !important;
  align-items: center;
  width: 80%;
  border: none;
  justify-content: space-evenly;
}
.download-btns p {
  font-size: 10px;
}
.download-btns p strong {
  font-size: 14px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .download-btns p strong {
    font-size: 20px !important;
  }
}
.logo-wrapper {
  text-align: center;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.logo-wrapper .footer-logo {
  position: absolute;
  margin: 0 auto;
}
.social-icons {
  display: flex;
  justify-content: center;
  padding: 0;
  margin: 0;
}
.social-icons li {
  list-style-type: none;
  padding: 0 8px 10px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .social-icons li {
    padding: 0 7px 10px;
  }
}
.social-icons li a {
  text-decoration: none;
}
.social-icons li a i {
  color: #FFFFFF;
  font-size: 24px;
}
[class^="flaticon-"]:before,
[class*=" flaticon-"]:before,
[class^="flaticon-"]:after,
[class*=" flaticon-"]:after {
  margin-left: 0;
}
.support-link p {
  font-weight: 300;
  color: #FFFFFF;
}
.support-link a {
  text-decoration: underline;
  color: #FFFFFF;
  font-weight: 500;
}
.support-link a:hover {
  color: #F79420;
}
.mobile-footer-nav {
  background: #000;
  box-shadow: 0 -4px 10px #0000001A;
  -webkit-box-shadow: 0 -4px 10px #0000001A;
  -moz-box-shadow: 0 -4px 10px #0000001A;
  border-radius: 20px 20px 0 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.mobile-footer-nav.hide-menus {
  bottom: -75px;
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
}
.mobile-footer-nav a {
  text-decoration: none;
  flex-wrap: wrap;
  text-align: center;
  color: #FFFFFF;
}
.mobile-footer-nav a:focus {
  text-decoration: none;
}
.mobile-footer-nav a:visited {
  text-decoration: none;
}
.mobile-footer-nav a img {
  margin: 0 auto;
  width: 22px;
  height: 22px;
}
.mobile-footer-nav a p {
  width: 100%;
  font-size: 13px;
  line-height: 1.2;
  margin-bottom: 0;
}
.mobile-footer-nav a.institute-menu img {
  width: 26px;
}
.mobile-footer-nav a.cart-menu {
  justify-content: center;
}
.mobile-footer-nav a.my_material_btn {
  position: relative;
  top: -30px;
  opacity: 1;
}
.mobile-footer-nav a.my_material_btn p {
  color: #000000b3;
}
.mobile-footer-nav a.my_material_btn:before {
  content: '';
  position: absolute;
  width: 75px;
  height: 75px;
  right: 0;
  left: 0;
  border-radius: 50%;
  background: #fff;
  z-index: -1;
}
.mobile-footer-nav a.my_material_btn:after {
  content: '';
  position: absolute;
  width: 70px;
  height: 70px;
  right: 0;
  left: 0;
  background: #FFFFFF;
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 50%;
  margin: 0 auto;
  z-index: -1;
}
.mobile-footer-nav i {
  color: #FFFFFF;
}
.mobile-footer-nav .active-menu {
  opacity: 1;
}
.mobile-footer-nav .common-footer-nav {
  opacity: 0.7;
}
.mobile-footer-nav #toggleModules i {
  margin: 0 auto;
  font-size: 18px;
  width: 18px;
  height: 18px;
}
#toggleModulesList {
  overflow: hidden;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s linear;
  height: 0;
  display: flex;
  flex-wrap: wrap;
  position: fixed;
  justify-content: flex-end;
  bottom: 110px;
  right: 15px;
  z-index: 9991;
}
#toggleModulesList #list {
  list-style: none;
  margin-bottom: 0;
  padding-left: 0;
}
#toggleModulesList #list li {
  width: 100%;
  background: #000000b3;
  margin-top: 10px;
  border-radius: 50px;
}
#toggleModulesList a {
  width: 100%;
  padding: 8px 15px;
  color: #FFFFFF;
  font-size: 12px;
  min-width: 170px;
  min-height: 40px;
  display: flex;
  align-items: center;
  text-decoration: none !important;
}
#toggleModulesList a i {
  font-size: 20px;
}
#toggleModulesList.open {
  visibility: visible;
  opacity: 1;
}
.toggleModulesBg {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #000000b3;
  z-index: 999;
}
#guestUser,
#loginOpen,
#signup,
#forgotPasswordmodal,
#shareContentModal,
#deleteBook,
#change-password-modal,
#currentPomodoro,
#pomodoroSessionCompletion,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal,
#bookQueueModal {
  z-index: 9992;
}
.time-saved {
  font-size: 40px;
  color: #949494;
  font-weight: 600;
}
.timer-text {
  color: #949494;
  font-size: 12px;
}
#pomodoroSessionCompletion .modal-body {
  background: #000000b3;
  border: 2px solid #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  text-align: center;
}
#pomodoroSessionCompletion .modal-body p {
  color: #FFFFFF;
}
#pomodoroSessionCompletion .modal-body p:first-child {
  font-weight: 600;
}
#pomodoroTimer {
  background: #FFFFFF;
  box-shadow: 0 2px 4px #0000001A;
  border-radius: 36px;
  width: 77px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.48);
}
#pomodoroTimer:hover {
  text-decoration: none;
}
.tree3 {
  top: -65px;
  left: 12px;
  position: relative;
}
.btn-forest {
  background: #FFFFFF;
  border: 1px solid #000000b3;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  color: #000000b3;
  font-size: 9px;
}
.info-rank p {
  font-size: 11px;
  color: #949494;
  font-weight: 400;
}
#relatedBooks .slick-track,
#bestSellerBooks .slick-track {
  width: 1900px !important;
}
#relatedBooks .slick-track .slick-slide,
#bestSellerBooks .slick-track .slick-slide {
  width: 190px !important;
}
.guest-user-alert {
  background: #FF6B6B;
  text-align: center;
  padding: 4px;
  position: absolute;
  width: 100%;
  max-width: 100%;
  top: 0;
}
.guest-user-alert span {
  font-size: 13px;
  color: black;
}
.guest-user-alert a {
  background: #FFFFFF;
  box-shadow: 0 2px 10px rgba(128, 128, 128, 0.25);
  border-radius: 10px;
  margin-left: 10px;
  color: black;
  font-size: 12px;
  line-height: normal;
  padding: 2px 10px;
}
.modal-backdrop.show {
  opacity: 0.85;
  z-index: 9991;
}
.mdl-layout__container_ebook .prepjoy_cta {
  margin-top: 0rem !important;
}
.prepjoy_cta {
  background-image: linear-gradient(229deg, #212121, #000000d1);
  position: relative;
  margin-top: 3rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .prepjoy_cta {
    padding: 2.5rem 1.5rem 3rem;
  }
}
.prepjoy_cta .curved-bg {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  width: 100%;
  height: 400px;
  position: absolute;
  opacity: 0.15;
  top: 20px;
  right: 0;
  left: 0;
}
.prepjoy_cta .cta_inner {
  border: 1px solid #F79420;
  padding: 20px;
  border-radius: 20px;
  box-shadow: 0 2px 4px #000;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .prepjoy_cta .cta_info {
    width: 100%;
  }
}
.prepjoy_cta .cta_info h3 {
  color: #FFFFFF;
  font-weight: 100;
  line-height: 35px;
}
.prepjoy_cta .cta_info h3 span {
  font-weight: 600;
}
.prepjoy_cta .cta_info h4,
.prepjoy_cta .cta_info p {
  font-weight: normal;
  font-size: 1.4rem;
}
.prepjoy_cta .cta_info h4 span,
.prepjoy_cta .cta_info p span {
  font-size: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .prepjoy_cta .cta_info h4 span,
  .prepjoy_cta .cta_info p span {
    padding: 5px 0;
    display: inline-block;
  }
}
.prepjoy_cta .cta_info h4 a,
.prepjoy_cta .cta_info p a {
  color: #F79420 !important;
}
.prepjoy_cta .cta_info h4 a:hover,
.prepjoy_cta .cta_info p a:hover {
  opacity: 0.7;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .prepjoy_cta .cta_btn {
    width: 100%;
  }
}
.prepjoy_cta .cta_btn p {
  color: #FFFFFF;
}
.prepjoy_cta .cta_btn a {
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #F79420;
  border-color: #F79420;
  border-radius: 7px;
  box-shadow: 0 2px 4px #00000040;
  -webkit-box-shadow: 0 2px 4px #00000040;
  -moz-box-shadow: 0 2px 4px #00000040;
}
.prepjoy_cta .cta_btn a:hover,
.prepjoy_cta .cta_btn a:active,
.prepjoy_cta .cta_btn a:focus,
.prepjoy_cta .cta_btn a:active:focus {
  background-color: #F79420 !important;
  border-color: #F79420 !important;
  box-shadow: none !important;
  outline: 0 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .prepjoy_cta .cta_btn a {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }
}
