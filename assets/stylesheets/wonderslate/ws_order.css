.purchase-details-container {
  min-height: 600px;
}
.purchase-details-container.ws-orders .purchase-heading {
  margin-top: 40px;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-heading {
    margin-top: 30px;
  }
}
.purchase-details-container.ws-orders .purchase-heading h3 {
  font-weight: 600;
  font-size: 24px;
  color: #000000b3;
}
.purchase-details-container.ws-orders .purchase-details-wrapper {
  margin: 0 auto 40px;
  min-height: auto;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-details-wrapper .purchase-details,
  .purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book {
    width: 100%;
  }
}
.purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
  padding: 0 40px 0 0;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
    padding: 0;
  }
}
.purchase-details-container.ws-orders .purchase-details-wrapper .learn-btn {
  display: inline-block;
  margin-top: 0;
}
.purchase-details-container .purchase-details-wrapper {
  margin: 40px auto;
  min-height: 400px;
}
.purchase-details-container .purchase-details-wrapper .purchase-heading {
  font-weight: 500;
  font-size: 24px;
  background: #000000b3;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.purchase-details-container .purchase-details-wrapper .purchase-success-confirmation {
  font-size: 16px;
  margin-bottom: 7px;
}
@media (max-width: 575px) {
  .purchase-details-container .purchase-details-wrapper .purchase-success-confirmation {
    font-size: 15px;
    line-height: normal;
  }
}
.purchase-details-container .purchase-details-wrapper .purchase-success-confirmation strong {
  font-weight: 600;
}
.purchase-details-container .purchase-details-wrapper .purchase-success-id {
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.01em;
  margin-bottom: 16px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: start;
  align-items: start;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper {
  width: 100%;
  border-radius: 6px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-item {
  display: flex;
  margin-bottom: 15px;
  padding: 10px;
  background: #FFFFFF;
  border-radius: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-img-wrapper img {
  width: 75px;
  box-shadow: 0 0 14px #0000001A;
  border-radius: 4px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info {
  vertical-align: top;
  padding: 0 15px 15px 15px;
  max-height: inherit;
  text-align: left;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .purchased-book-name {
  font-style: normal;
  font-weight: 500;
  line-height: 21px;
  font-size: 16px;
  letter-spacing: 0.01em;
  margin-bottom: 7px;
}
@media (max-width: 767px) {
  .purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .purchased-book-name {
    font-size: 15px;
  }
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .detail-book-author-name {
  text-align: left;
  margin-bottom: 7px;
  color: #6C757D;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .offer-price {
  display: inline-block;
  font-size: 20px;
  font-weight: 500;
  color: #FF4B33;
  letter-spacing: 0.01em;
  margin-right: 4px;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .offer-price i {
  font-size: 18px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .original-price {
  display: inline-block;
  font-size: 16px;
  font-weight: 300;
  color: #949494;
  letter-spacing: 0.01em;
  text-decoration: line-through;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchase-details {
  float: left;
  margin-left: 50px;
}
@media screen and (max-width: 991px) {
  .purchase-details-container .purchase-details-wrapper .purchase-details {
    width: 100%;
    margin-left: 0;
  }
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book {
  float: left;
  width: 60%;
  border-right: 1px solid #848484;
}
@media screen and (max-width: 991px) {
  .purchase-details-container .purchase-details-wrapper .browse-purchase-book {
    width: 100%;
    border: 0;
  }
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
  padding: 24px 40px;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .continue-browse {
  display: block;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.01em;
  color: #000000b3;
  padding: 24px 0;
  margin-bottom: 24px;
  border-top: 1px solid #848484;
  border-bottom: 1px solid #848484;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .read-on-app {
  font-weight: 400;
  line-height: normal;
  font-size: 12px;
  text-align: center;
  letter-spacing: -0.01em;
  color: #848484;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .download-app-btn {
  display: block;
  text-align: center;
  max-width: 122px;
  height: 40px;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .download-app-btn .download-app-btn-img {
  width: 100%;
  height: auto;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  vertical-align: middle;
  z-index: 1;
  transition: 0.3s ease-out;
}
.purchase-details-container .purchase-details-wrapper .learn-btn {
  font-size: 14px;
  display: block;
  text-align: center;
  font-weight: 500;
  color: #FFFFFF;
  background: #F79420;
  letter-spacing: 0.01em;
  padding: 11px 25px;
  border-radius: 4px;
  margin-bottom: 24px;
  margin-top: -10px;
}
.purchase-details-container .purchase-details-wrapper .learn-btn:hover {
  text-decoration: none;
  box-shadow: 0 2px 8px #0000001A;
}
.purchase-details-container .purchase-details-wrapper .instructions h5 {
  font-weight: 600;
}
.purchase-details-container .purchase-details-wrapper .instructions ol li {
  padding-bottom: 7px;
  font-size: 15px;
}
.purchase-details-container .purchase-details-wrapper .instructions ol li strong {
  font-weight: 600;
}
#celebrationAnim {
  display: none;
  width: 100%;
  height: 100%;
  position: absolute;
  top: -50px;
}
