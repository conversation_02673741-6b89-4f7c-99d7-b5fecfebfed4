body,
html {
  background: #fff !important;
}
.headerCategoriesMenu {
  display: none !important;
}
.mdl-js-layout.is-upgraded {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: none !important;
}
.ws__aboutus-main_wrapper {
  scroll-snap-type: y mandatory;
  scroll-snap-stop: always;
  height: 92vh;
  overflow-x: scroll;
  position: relative;
}
.ws__aboutus-main_wrapper .ws__aboutus-sec {
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
  align-items: center;
}
.ws__aboutus-main_wrapper .ws__aboutus-sec.mission {
  grid-template-columns: revert;
}
.ws__aboutus-main_wrapper > .ws__aboutus-sec {
  scroll-snap-align: center;
}
.ws__aboutus-main_wrapper .ws__aboutus-sec__content {
  width: calc(100% - 20%);
  margin: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.ws__aboutus-main_wrapper .ws__aboutus-sec.mission .ws__aboutus-sec__content {
  width: calc(100% - 40%);
}
.ws__aboutus-main_wrapper .ws__aboutus-sec__content h1 {
  font-size: 2.75rem;
  text-transform: uppercase;
}
.ws__aboutus-main_wrapper .ws__aboutus-sec__content p {
  color: rgba(0, 0, 0, 0.5);
  font-size: 1.09rem;
  line-height: unset;
  margin-top: 1rem;
}
.ws__aboutus-sec .ws__aboutus-sec__img {
  display: flex;
  justify-content: center;
  align-items: center;
}
.ws__aboutus-sec .ws__aboutus-sec__img img {
  width: 400px;
}
.ws__aboutus-sec .ws__aboutus-sec__img.publishersImg img {
  width: 500px;
}
.scrollIcon_wrapper,
.scrollIcon_wrapperOne {
  width: 20px;
  height: 20px;
  background: #fff;
  transform: rotate(45deg);
  position: fixed;
  left: 50%;
  border-top: 2px solid rgba(0, 0, 0, 0.6);
  border-left: 2px solid rgba(0, 0, 0, 0.6);
  border-bottom: none;
  transition: all 0.3s ease;
}
.scrollIcon_wrapper {
  bottom: 15px;
  animation: scrollFadeInOutOne 2s ease infinite;
}
.scrollIcon_wrapperOne {
  bottom: 25px;
  animation: scrollFadeInOutTwo 2s ease infinite;
}
.ws__aboutus-sec.sec_services {
  grid-template-columns: revert;
}
.sec_services-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.sec_services-wrap_img {
  display: flex;
  justify-content: center;
  align-items: center;
}
.sec_services-wrap_img img {
  width: 200px;
}
@media (max-width: 768px) {
  .ws__aboutus-main_wrapper .ws__aboutus-sec__content {
    width: calc(100% - 10%);
  }
  .ws__aboutus-main_wrapper .ws__aboutus-sec {
    grid-template-columns: repeat(1, 1fr);
  }
  .ws__aboutus-sec .ws__aboutus-sec__img {
    width: calc(100% - 20%);
    margin: 0 auto 5rem auto;
  }
  .ws__aboutus-sec .ws__aboutus-sec__img img {
    width: 100% !important;
  }
  .ws__aboutus-sec.publishersContent .ws__aboutus-sec__content {
    grid-row: 1;
  }
  .sec_services-wrap {
    grid-template-columns: repeat(1, 1fr);
  }
  .ws__aboutus-main_wrapper .ws__aboutus-sec {
    height: auto;
  }
  .sec_services-wrap.printBooks .ws__aboutus-sec__content {
    grid-row: 1;
  }
  .ws__aboutus-main_wrapper .ws__aboutus-sec.mission .ws__aboutus-sec__content {
    width: calc(100% - 10%);
  }
  .sec_services-wrap_img.testSeriesImg {
    margin-bottom: 5rem;
  }
}
@keyframes scrollFadeInOutOne {
  0% {
    bottom: 15px;
    opacity: 0;
  }
  10% {
    bottom: 20px;
    opacity: 1;
  }
  25% {
    bottom: 25px;
  }
  50% {
    bottom: 30px;
    opacity: 0.5;
  }
  75% {
    bottom: 35px;
    opacity: 0;
  }
  100% {
    bottom: 15px;
    opacity: 0;
  }
}
@keyframes scrollFadeInOutTwo {
  0% {
    bottom: 25px;
    opacity: 0;
  }
  10% {
    bottom: 30px;
    opacity: 1;
  }
  25% {
    bottom: 35px;
  }
  50% {
    bottom: 40px;
    opacity: 0.5;
  }
  75% {
    bottom: 45px;
    opacity: 0;
  }
  100% {
    bottom: 25px;
    opacity: 0;
  }
}
