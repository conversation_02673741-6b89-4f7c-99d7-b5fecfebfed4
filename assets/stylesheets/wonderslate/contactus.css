.container {
    max-width: 900px;
}
.jumbotron {
    background: transparent;
}
.contactus-form {
    flex-direction: row;
}

.contactus-form input {
    border: 1px solid #98309C;
}
.contactus-form input[type="text"] {
    position: relative;
    z-index: 10;
    padding-left: 20px;
    border-radius: 5px;
    box-shadow: none;
    color: #98309C;
}
.contactus-form > button {
    position: absolute;
    right: 0px;
    z-index: 100;
    top: 5px;
}
.contactus-form > button > i {
    color: #98309C;
}
.question{
    color: rgba(68, 68, 68, 0.48);
    font-weight: 700;
    font-size: 18px;
}
.answer {
    color:rgba(68, 68, 68, 0.48);
    font-size: 12px;
}
.show-more {
    background: #98309C;
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px;
    color: white;
    font-size: 8px;
}
.contact-address {
    flex-direction: row;
    margin: 0 auto;
}
.address-info {
    flex-direction: column;
    border-left: 1px solid rgba(68, 68, 68, 0.20);
}
.address-info h2, .address-info address span {
    font-size: 14px;
    color: rgba(68, 68, 68, 0.48);
}
.address-info address {
    color: rgba(68, 68, 68, 0.48);
    font-size: 12px;
}
.address-info address a {
    color: #98309C;
    font-size: 12px;
}