@media (min-width: 768px) and (max-width: 991.98px) {
  .mcq_creation #static-content .quiz6 .btn {
    margin-bottom: 10px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .mobile_page_title {
    box-shadow: 0 2px 4px #0000001A;
    -webkit-box-shadow: 0 2px 4px #0000001A;
    -moz-box-shadow: 0 2px 4px #0000001A;
    border-radius: 0 0 20px 20px;
    top: 0;
    left: 0;
    width: 100%;
    height: 70px;
    z-index: 999;
  }
  .mobile_page_title i {
    color: #444444;
  }
  .mobile_page_title h4 {
    font-size: 18px;
    font-weight: 400;
    color: #444444;
  }
  .mcq_creation .pag_title {
    display: none;
  }
  .mcq_creation #static-content .quiz6 .btn {
    margin-bottom: 10px;
    width: 100%;
  }
  #download-app-btn-container {
    display: none;
  }
  .mobile-footer-nav {
    display: none !important;
  }
  header.normalHeader {
    border-bottom: none;
  }
  .mcq_creation #static-content {
    border: none !important;
    box-shadow: none;
    background-color: transparent;
  }
  .mcq_creation #static-content .firstPage {
    background-color: transparent;
    box-shadow: none;
    border: none !important;
  }
  .mcq_creation #static-content .firstPage select,
  .mcq_creation #static-content .firstPage input {
    font-size: 13px;
  }
  .mcq_creation #static-content .firstPage input#resourceName {
    border: none;
    box-shadow: none;
    border-bottom: 1px solid #000000b3;
    border-radius: 0;
    padding: 0;
    background: transparent;
  }
  .mcq_creation #static-content .firstPage select#language1 {
    background: #FFF9EB;
    border-radius: 5px;
    border: none;
  }
  .mcq_creation #static-content .firstPage select#language1 option {
    background: #FFFFFF;
  }
  .firstPage .row {
    flex-direction: column-reverse;
  }
  .mcq_creation #static-content .mobile_view_border {
    border: 1px solid #27AE60;
    border-radius: 5px;
    margin-top: 10px;
    padding-bottom: 15px;
    margin-bottom: 20px;
  }
  .mcq_creation #static-content .quiz7 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-top: 10px;
  }
  .mcq_creation #static-content .quiz7 .col-sm-12 {
    padding: 0 5px;
  }
  .mcq_creation #static-content #questionLabel {
    font-size: 10px;
    color: #848484;
  }
  .mcq_creation #static-content .quiz7 .inlineEditor {
    box-shadow: rgba(0, 0, 0, 0.08) 0 0 5px inset;
    border-color: transparent;
    border-radius: 5px;
    min-height: 80px;
    padding: 10px;
  }
  .mcq_creation #static-content .quiz .inlineEditor {
    box-shadow: rgba(0, 0, 0, 0.08) 0 0 5px inset;
    border-color: transparent;
    border-radius: 5px;
    padding: 5px;
  }
  .mcq_creation #static-content .quiz {
    position: relative;
    margin-bottom: 0 !important;
  }
  .mcq_creation #static-content .quiz .col-md-1 {
    position: relative;
    left: 0;
    top: 5px;
    margin-bottom: -50px;
    width: 25px;
    padding-left: 5px;
    padding-right: 0;
    z-index: 99;
  }
  .mcq_creation #static-content .quiz .col-md-5 {
    padding-left: 35px;
    margin-bottom: 10px;
    padding-right: 5px;
  }
  .mcq_creation #static-content .quiz .col-md-5 b {
    font-size: 10px;
    font-weight: 500;
    color: #848484;
    text-transform: uppercase;
  }
  .mcq_creation #static-content .quiz input[type="checkbox"] {
    width: 16px;
    height: 16px;
  }
  .mcq_creation #static-content .quiz2 {
    margin-bottom: 0 !important;
  }
  .mcq_creation #static-content .quiz2 .col-sm-12 {
    padding: 0;
  }
  .mcq_creation #static-content .quiz2 b {
    font-size: 11px;
    text-transform: uppercase;
    color: #848484;
    font-weight: 500;
  }
  .mcq_creation #static-content .quiz2 .inlineEditor {
    border-color: #000000b3;
  }
  .mcq_creation #static-content .quiz4 .col-lg-3 {
    padding: 0;
  }
  .mcq_creation #static-content .quiz6 .col-sm-12 {
    padding: 0;
  }
  .mcq_creation #static-content .smallerText label {
    color: #848484;
    font-size: 11px;
  }
  .mobile_column_swap {
    flex-direction: column-reverse;
  }
  #sidebar {
    margin-bottom: 20px;
  }
  .quiz,
  .quiz4,
  .quiz5 {
    display: block;
  }
  .quiz,
  .quiz4,
  .quiz5 .col-12 {
    padding: 0;
  }
  .quiz3 .col-12 {
    padding: 0;
  }
}
.mcq_creation #static-content .quiz6 .finish_btn {
  background: #27AE60 !important;
  box-shadow: 0 2px 4px #0000001A;
  border-color: #27AE60 !important;
}
.mcq_creation #static-content .quiz6 .finish_btn:active:focus {
  box-shadow: none !important;
}
