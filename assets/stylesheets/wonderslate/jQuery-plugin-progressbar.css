
.position {
  float: left;
  margin: 100px 20px;
}

.progress-bar {
  position: relative;
  height: 200px;
  width: 200px;
}

.progress-bar div {
  position: absolute;
  height: 200px;
  width: 200px;
  border-radius: 50%;
}

.progress-bar div span {
  position: absolute;
  font-family: Arial;
  font-size: 25px;
  line-height: 175px;
  height: 175px;
  width: 175px;
  left: 12.5px;
  top: 12.5px;
  text-align: center;
  border-radius: 50%;
  background-color: white;
}

.progress-bar .background { background-color: #b3cef6; }

.progress-bar .rotate {
  clip: rect(0 100px 200px 0);
  background-color: #4b86db;
}

.progress-bar .left {
  clip: rect(0 100px 200px 0);
  opacity: 1;
  background-color: #b3cef6;
}

.progress-bar .right {
  clip: rect(0 100px 200px 0);
  transform: rotate(180deg);
  opacity: 0;
  background-color: #4b86db;
}
 @keyframes 
toggle {  0% {
 opacity: 0;
}
 100% {
 opacity: 1;
}
}
