.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
.topper,
.career,
.student-problems {
  margin-top: 50px;
}
@media (max-width: 575px), (min-width: 576px) and (max-width: 767px) {
  .topper,
  .career,
  .student-problems {
    margin-top: 20px;
  }
}
.topper a,
.career a,
.student-problems a {
  text-decoration: none;
}
.topper .hero-text,
.career .hero-text,
.student-problems .hero-text {
  padding: 0px 20px;
}
.topper .hero-text h1,
.career .hero-text h1,
.student-problems .hero-text h1 {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.topper .hero-text h1,
.career .hero-text h1,
.student-problems .hero-text h1 {
  font-size: 24px;
  font-weight: bold;
}
.topper .hero-text p,
.career .hero-text p,
.student-problems .hero-text p {
  color: #949494;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0.9px;
  padding: 20px 0px;
  margin-bottom: 30px;
}
.topper .features .row,
.career .features .row,
.student-problems .features .row {
  padding-left: 15px;
}
.topper .features .row .card,
.career .features .row .card,
.student-problems .features .row .card {
  border-radius: 10px !important;
  height: 79px;
  color: #ffffff;
  border: none;
  margin-bottom: 16px;
  position: relative;
  display: flex;
  margin-right: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  list-style-type: none;
  transition: all 0.3s ease-in-out;
}
.topper .features .row .card:hover,
.career .features .row .card:hover,
.student-problems .features .row .card:hover {
  transform: translateY(-7%);
  opacity: 1;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .topper .features .row .card:hover,
  .career .features .row .card:hover,
  .student-problems .features .row .card:hover {
    transform: none;
  }
}
.topper .features .row .card:nth-child(1),
.career .features .row .card:nth-child(1),
.student-problems .features .row .card:nth-child(1) {
  background: radial-gradient(142.42% 142.42% at 8.23% 0%, #F2C74C 0%, #F2994A 46.86%) !important;
}
.topper .features .row .card:nth-child(1):active,
.career .features .row .card:nth-child(1):active,
.student-problems .features .row .card:nth-child(1):active {
  background: radial-gradient(142.42% 142.42% at 8.23% 0%, #F2C74C 0%, #F2994A 46.86%);
}
.topper .features .row .card img,
.career .features .row .card img,
.student-problems .features .row .card img {
  position: absolute;
  width: 28px;
  height: 27px;
  right: 10px;
  top: 10px;
  opacity: 1;
}
.topper .features .row .card:nth-child(2),
.career .features .row .card:nth-child(2),
.student-problems .features .row .card:nth-child(2) {
  background: radial-gradient(100% 100% at 0% 0%, #49E859 0%, #007D0C 100%) !important;
}
.topper .features .row .card:nth-child(3),
.career .features .row .card:nth-child(3),
.student-problems .features .row .card:nth-child(3) {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #DB8AFA 0%, #4E0096 99.48%) !important;
}
.topper .features .row .card .card-body,
.career .features .row .card .card-body,
.student-problems .features .row .card .card-body {
  justify-content: flex-end;
  padding-bottom: 0.5rem;
  padding-left: 0rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  cursor: pointer;
}
.topper .features .row .card .card-body .card-title,
.career .features .row .card .card-body .card-title,
.student-problems .features .row .card .card-body .card-title {
  font-weight: bold;
  font-size: 14px;
  line-height: 24px;
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .topper .features .row .card .card-body .card-title,
  .career .features .row .card .card-body .card-title,
  .student-problems .features .row .card .card-body .card-title {
    font-size: 16px;
  }
}
.topper .recent-reading,
.career .recent-reading,
.student-problems .recent-reading {
  margin-top: 50px;
  margin-bottom: 50px;
  position: relative;
}
.topper .recent-reading p,
.career .recent-reading p,
.student-problems .recent-reading p {
  font-size: 10px;
  font-style: italic;
  color: #7F28A8;
  padding-left: 10px;
}
.topper .recent-reading ul.posts-scroller,
.career .recent-reading ul.posts-scroller,
.student-problems .recent-reading ul.posts-scroller {
  padding-left: 20px;
}
.topper .recent-reading ul.posts-scroller .card,
.career .recent-reading ul.posts-scroller .card,
.student-problems .recent-reading ul.posts-scroller .card {
  width: 318px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  margin-right: 16px;
  font-size: 16px;
  color: rgba(68, 68, 68, 0.48);
  padding-top: 10px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  cursor: pointer;
}
.topper .recent-reading ul.posts-scroller .card li,
.career .recent-reading ul.posts-scroller .card li,
.student-problems .recent-reading ul.posts-scroller .card li {
  list-style-type: none;
}
.topper .recent-reading ul.posts-scroller .card span,
.career .recent-reading ul.posts-scroller .card span,
.student-problems .recent-reading ul.posts-scroller .card span {
  font-weight: normal;
  font-size: 12px;
  margin-top: 8px;
}
.topper .doubts,
.career .doubts,
.student-problems .doubts {
  position: relative;
}
.topper .doubts .doubts-title,
.career .doubts .doubts-title,
.student-problems .doubts .doubts-title {
  font-size: 10px;
  font-style: italic;
  color: #7F28A8;
  padding-left: 10px;
}
.topper .doubts .card,
.career .doubts .card,
.student-problems .doubts .card {
  box-shadow: 0px 0px 10px rgba(72, 72, 72, 0.1);
  height: fit-content;
  padding: 0;
}
.topper .doubts .card .card-body,
.career .doubts .card .card-body,
.student-problems .doubts .card .card-body {
  padding: 10px;
  flex-direction: column;
}
.topper .doubts .card .card-body .doubts-header,
.career .doubts .card .card-body .doubts-header,
.student-problems .doubts .card .card-body .doubts-header {
  padding: 0px;
  border-bottom: 0.8px solid rgba(51, 51, 51, 0.137);
}
.topper .doubts .card .card-body .doubts-header p,
.career .doubts .card .card-body .doubts-header p,
.student-problems .doubts .card .card-body .doubts-header p {
  font-size: 12px;
  color: #949494;
  padding: 0px;
  margin: 5px;
}
.topper .doubts .card .card-body .doubts-header .add-doubt,
.career .doubts .card .card-body .doubts-header .add-doubt,
.student-problems .doubts .card .card-body .doubts-header .add-doubt {
  color: #2F80ED;
  cursor: pointer;
}
.topper .doubts .card .card-body .name,
.career .doubts .card .card-body .name,
.student-problems .doubts .card .card-body .name {
  align-content: center;
}
.topper .doubts .card .card-body .name img,
.career .doubts .card .card-body .name img,
.student-problems .doubts .card .card-body .name img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #949494;
}
.topper .doubts .card .card-body .name p,
.career .doubts .card .card-body .name p,
.student-problems .doubts .card .card-body .name p {
  padding-left: 10px;
  font-size: 10px;
}
.topper .doubts .card .card-body .name span,
.career .doubts .card .card-body .name span,
.student-problems .doubts .card .card-body .name span {
  font-size: 8px;
  margin: 0px;
  padding-left: 10px;
  color: #33333367;
  padding-top: 4px;
}
.topper .doubts .card .card-body .doubts-info .comment,
.career .doubts .card .card-body .doubts-info .comment,
.student-problems .doubts .card .card-body .doubts-info .comment {
  font-size: 12px;
  margin: 5px 0;
}
.topper .doubts .card .card-body .doubts-info img,
.career .doubts .card .card-body .doubts-info img,
.student-problems .doubts .card .card-body .doubts-info img {
  width: 100%;
  height: 130px;
  background-color: #949494;
}
.topper .doubt-status span,
.career .doubt-status span,
.student-problems .doubt-status span {
  font-size: 10px;
  padding: 5px;
}
.topper .doubt-status span i,
.career .doubt-status span i,
.student-problems .doubt-status span i {
  font-size: 12px;
  color: #949494;
  margin-right: 5px;
}
.topper .more-for-you-heading .doubts-title,
.career .more-for-you-heading .doubts-title,
.student-problems .more-for-you-heading .doubts-title {
  font-size: 10px;
  font-style: italic;
  color: #7F28A8;
  padding-left: 10px;
}
.topper .more-for-you-cards,
.career .more-for-you-cards,
.student-problems .more-for-you-cards {
  background-color: #F7F7F7;
  box-shadow: 0px 2px 10px rgba(189, 30, 173, 0.14);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .topper .more-for-you-cards,
  .career .more-for-you-cards,
  .student-problems .more-for-you-cards {
    background-color: #ffffff;
    padding: 0;
    box-shadow: none;
  }
  .topper .more-for-you-cards .container,
  .career .more-for-you-cards .container,
  .student-problems .more-for-you-cards .container {
    background-color: #F7F7F7;
    border-radius: 10px;
    box-shadow: 0px 2px 10px rgba(189, 30, 173, 0.14);
  }
}
.topper .arrows,
.career .arrows,
.student-problems .arrows {
  background: linear-gradient(270deg, #FFFFFF 16.94%, rgba(255, 255, 255, 0) 96.45%);
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
}
.topper .arrows .left-arrow,
.career .arrows .left-arrow,
.student-problems .arrows .left-arrow {
  width: 40px;
  max-width: 40px;
  height: 40px;
  border-radius: 50px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  position: absolute;
  margin: -20px 1rem 0.5rem;
  top: 50%;
  cursor: pointer;
}
.topper .arrows .right-arrow,
.career .arrows .right-arrow,
.student-problems .arrows .right-arrow {
  width: 40px;
  max-width: 40px;
  height: 40px;
  border-radius: 50px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  position: absolute;
  top: 50%;
  margin: -20px 1rem 0.5rem;
  right: 41px;
  display: flex !important;
  align-items: center;
  cursor: pointer;
}
.topper .arrows .right-arrow i,
.career .arrows .right-arrow i,
.student-problems .arrows .right-arrow i {
  color: #ffffff;
}
@media only screen and (max-width: 575px) {
  #student-problems-section .mobile-drop {
    margin-top: 0 !important;
  }
  #student-problems-section.reset-app {
    margin-top: 2rem !important;
  }
  #student-problems-section .mobile-drop.reset-app .discussion-card:first-child {
    margin-top: 2rem !important;
  }
}
.app-back-btn {
  display: none;
}
