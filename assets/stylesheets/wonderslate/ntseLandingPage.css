p {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.8;
}
h2 {
  font-size: 40px;
}
.gap {
  gap: 1rem;
}
.first {
  width: 100%;
}
.showcase {
  /*height: 50vh;*/
  display: flex;
  justify-content: center;
  align-items: center;
}
.showcase img {
  width: 500px;
}
@media screen and (max-width: 768px) {
  .showcase {
    height: auto;
    align-items: start;
  }
  .showcase img {
    width: 100%;
  }
}
.section {
  margin-bottom: 6rem;
  margin-top: 2rem;
  padding: 2rem 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}
.section p {
  text-align: justify;
  margin-top: 1rem;
}
@media screen and (max-width: 768px) {
  .section {
    margin-bottom: 3rem;
    margin-top: 3rem;
  }
}
.second {
  width: 100%;
}
.second img {
  width: 350px;
}
@media screen and (max-width: 768px) {
  .second img {
    width: 100%;
  }
}
.reverse-row {
  flex-direction: row-reverse;
}
@media screen and (max-width: 768px) {
  .reverse-row {
    flex-direction: column;
  }
}
.explore {
  padding: 7px 20px;
  border: 1px solid #c7c7c7;
  border-radius: 7px;
  display: inline-block;
  margin-right: 1rem;
  box-shadow: 0 1px 2px #0000001a;
  color: #444444;
  transition: all 0.3s;
  margin-bottom: 1rem;
  font-size: 16px;
}
.explore:hover {
  color: #F79420;
  box-shadow: 0 2px 6px #0000001a;
  border-color: #F79420;
  background-color: #FFFFFF;
}
.explore a {
  color: #444444;
}
.section__odd {
  background: linear-gradient(to right, #feeedc, #fff);
}
.section__even {
  background: linear-gradient(to left, #feeedc, #fff);
}
/* video */
.vcenter {
  align-items: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  justify-content: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  -ms-justify-content: center;
  display: flex;
  display: -webkit-flex;
  -webkit-display: flex;
  -moz-display: flex;
  -ms-display: flex;
  height: 240px;
}
section.video-promo-hero {
  position: relative;
  height: 240px;
  background: rgba(0, 0, 0, 0.6);
  overflow: hidden;
  border-radius: 10px;
}
.institute-homepage .buttons-wrapper a::after {
  z-index: -1 !important;
}
section.video-promo-hero.playing-video .video-promo-hero-bg {
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  -o-transform: translate3d(100%, 0, 0);
  -ms-transform: translate3d(100%, 0, 0);
  transform: translate3d(100%, 0, 0);
}
section.video-promo-hero .video-promo-hero-bg {
  /*background: url('../../assets/images/ntse/ntse_thumbnail.jpeg') no-repeat 50% 100%;*/
  background-size: cover;
  height: 100%;
  -webkit-transition: all 1s;
  -moz-transition: all 1s;
  -o-transition: all 1s;
  transition: all 1s;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  position: relative;
  z-index: 10;
}
section.video-promo-hero .video-promo-hero-bg a.video-promo-watch {
  display: flex;
  display: -webkit-flex;
  -webkit-display: flex;
  -moz-display: flex;
  -ms-display: flex;
  align-items: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  -ms-align-items: center;
  font-size: 45px;
  color: #fff;
  text-transform: uppercase;
  text-align: center;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
section.video-promo-hero .video-promo-hero-bg a.video-promo-watch i {
  -webkit-transform: rotate(0deg) scale(1);
  -moz-transform: rotate(0deg) scale(1);
  -ms-transform: rotate(0deg) scale(1);
  transform: rotate(0deg) scale(1);
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  transition: all 0.5s;
}
section.video-promo-hero .video-promo-hero-bg a.video-promo-watch:hover {
  text-decoration: none;
  font-size: 48px;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -ms-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}
section.video-promo-hero .video-promo-hero-bg a.video-promo-watch:hover i {
  -webkit-transform: rotate(120deg) scale(1);
  -moz-transform: rotate(120deg) scale(1);
  -ms-transform: rotate(120deg) scale(1);
  transform: rotate(120deg) scale(1);
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  transition: all 0.5s;
}
section.video-promo-hero .video-promo-video-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
section.video-promo-hero .video-promo-video-wrapper a.video-promo-video-close {
  color: #fff;
  z-index: 20000;
  position: absolute;
  top: 10px;
  left: 30px;
  font-size: 4rem;
  opacity: 1;
  font-weight: lighter;
  line-height: 50px;
  margin: 0;
  padding: 0;
}
section.video-promo-hero .video-promo-video-wrapper a.video-promo-video-close:hover {
  color: #fff;
}
section.video-promo-hero .video-promo-video-wrapper .video-promo-youtube-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
section.video-promo-hero .video-promo-video-wrapper .video-promo-youtube-wrapper .video-container {
  width: 100%;
  height: 100%;
  text-align: center;
}
section.video-promo-hero .video-promo-video-wrapper .video-promo-youtube-wrapper .video-container #youtube-promo-video {
  width: 100%;
  height: 100%;
}
section.video-promo-hero .video-promo-play-button {
  font-size: 80px;
  margin-right: 26px;
}
/*video*/
@media screen and (max-width: 768px) {
  .first,
  .second {
    width: auto;
  }
  iframe {
    width: 100%;
  }
  section.video-promo-hero {
    height: 200px;
    width: 90%;
  }
  .vcenter {
    height: 200px;
  }
}
