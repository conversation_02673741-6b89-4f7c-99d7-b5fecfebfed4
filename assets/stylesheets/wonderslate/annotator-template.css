.annotator-notice,
.annotator-filter *,
.annotator-widget * {
    margin: 0;
    padding: 0;
    background: 0;
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    transition: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
    color: #909090;
    font-family: "Montserrat", sans-serif;
}
.annotator-resize,
.annotator-widget::after,
.annotator-editor a::after,
.annotator-viewer .annotator-controls button,
.annotator-viewer .annotator-controls a,
.annotator-filter .annotator-filter-navigation button::after,
.annotator-filter .annotator-filter-property .annotator-filter-clear {
    background-image: url("data:image/png;base64,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");
    background-repeat: no-repeat;
}
.annotator-hl {
    background: rgba(255, 255, 10, 0.3);
}
.annotator-hlh {
    background-color: rgba(33, 150, 83, 0.24);
}
.annotator-hl-temporary {
    background: rgba(0, 124, 255, 0.3);
}
.annotator-wrapper {
    position: relative;
    padding: 3rem 0;
}
.annotator-adder,
.annotator-outer,
.annotator-notice {
    z-index: 1020;
}
.annotator-filter {
    z-index: 1010;
}
.annotator-adder,
.annotator-outer,
.annotator-widget,
.annotator-notice {
    position: absolute;
    font-size: 10px;
    line-height: 1;
}
.annotator-hide {
    display: none;
    visibility: hidden;
}
.annotator-adder {
    min-width: 285px;
    background: radial-gradient(261.94% 6870.17% at -55.38% -114.52%, #45EAFF 0%, #6867F6 100%);
    border-radius: 20px;
    border:none;
    box-sizing: border-box;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.5);
    padding: 4px 16px;
    margin-top: 0;
    margin-left: 0;
}

.annotator-adder:hover {
    background-position: center top;
}
.annotator-adder:active {
    background-position: center right;
}
.annotator-adder button {
    display: inline-block;
    margin: 0 auto;
    border: 0;
    background: 0;
    cursor: pointer;
    padding: 8px 32px;
    font-weight: 300;
    font-size: 14px;
    text-indent: 0;
    width: auto;
    white-space: nowrap;
    color:#fff;
}
.annotator-adder button:first-child {
    border-right: 1px solid #ededed;
}
.annotator-outer {
    width: 0;
    height: 0;
}
.annotator-widget {
    margin: 0;
    padding: 0 8px;
    left: -18px;
    min-width: 265px;
    background-color: #FFFFFF;
    border: 1px solid rgba(122, 122, 122, 0.6);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    -o-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
.annotator-invert-x .annotator-widget {
    left: auto;
    right: -18px;
}
.annotator-invert-y .annotator-widget {
    bottom: auto;
    top: 8px;
}
.annotator-widget strong {
    font-weight: bold;
}
.annotator-widget .annotator-listing,
.annotator-widget .annotator-item {
    padding: 0;
    margin: 0;
    list-style: none;
}
.annotator-widget::after {
    content: "";
    display: block;
    width: 18px;
    height: 10px;
    background-position: 0 0;
    position: absolute;
    bottom: -10px;
    left: 8px;
    background-image: none;
}
.annotator-invert-x .annotator-widget::after {
    left: auto;
    right: 8px;
}
.annotator-invert-y .annotator-widget::after {
    background-position: 0 -15px;
    bottom: auto;
    top: -9px;
}
.annotator-widget .annotator-item,
.annotator-editor .annotator-item input,
.annotator-editor .annotator-item textarea {
    position: relative;
    font-size: 12px;
}
.annotator-viewer .annotator-item {
    border-top: 2px solid rgba(122, 122, 122, 0.2);
}
.annotator-widget .annotator-item:first-child {
    border-top: 0;
}
.annotator-editor .annotator-item,
.annotator-viewer div {
    border-top: 1px solid rgba(68, 68, 68, 0.24);
}
.annotator-viewer div {
    padding: 6px 6px;
}
.annotator-viewer .annotator-item ol,
.annotator-viewer .annotator-item ul {
    padding: 4px 16px;
}
.annotator-viewer div:first-of-type,
.annotator-editor .annotator-item:first-child textarea {
    padding-top: 12px;
    padding-bottom: 12px;
    color: #3c3c3c;
    font-size: 13px;
    font-style: italic;
    line-height: 1.3;
    border-top: 0;
}
.annotator-viewer .annotator-controls {
    position: relative;
    top: 5px;
    right: 5px;
    padding-left: 5px;
    opacity: 0;
    -webkit-transition: opacity 0.2s ease-in;
    -moz-transition: opacity 0.2s ease-in;
    -o-transition: opacity 0.2s ease-in;
    transition: opacity 0.2s ease-in;
    float: right;
}
.annotator-viewer li:hover .annotator-controls,
.annotator-viewer li .annotator-controls.annotator-visible {
    opacity: 1;
}
.annotator-viewer .annotator-controls button,
.annotator-viewer .annotator-controls a {
    cursor: pointer;
    display: inline-block;
    width: 13px;
    height: 13px;
    margin-left: 2px;
    border: 0;
    opacity: 0.2;
    text-indent: -900em;
    background-color: transparent;
    outline: 0;
}
.annotator-viewer .annotator-controls button:hover,
.annotator-viewer .annotator-controls button:focus,
.annotator-viewer .annotator-controls a:hover,
.annotator-viewer .annotator-controls a:focus {
    opacity: 0.9;
}
.annotator-viewer .annotator-controls button:active,
.annotator-viewer .annotator-controls a:active {
    opacity: 1;
}
.annotator-viewer .annotator-controls button[disabled] {
    display: none;
}
.annotator-viewer .annotator-controls .annotator-edit {
    background-position: 0 -60px;
}
.annotator-viewer .annotator-controls .annotator-delete {
    background-position: 0 -75px;
}
.annotator-viewer .annotator-controls .annotator-link {
    background-position: 0 -270px;
}
.annotator-editor .annotator-item {
    position: relative;
}
.annotator-editor .annotator-item label {
    top: 0;
    display: inline;
    cursor: pointer;
    font-size: 12px;
}
.annotator-editor .annotator-item input,
.annotator-editor .annotator-item textarea {
    display: block;
    min-width: 100%;
    padding: 10px 8px;
    border: 0;
    margin: 0;
    color: #3c3c3c;
    background: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    -o-box-sizing: border-box;
    box-sizing: border-box;
    resize: none;
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar-track-piece {
    margin: 13px 0 3px;
    background-color: #e5e5e5;
    -webkit-border-radius: 4px;
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar-thumb:vertical {
    height: 25px;
    background-color: #ccc;
    -webkit-border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar-thumb:horizontal {
    width: 25px;
    background-color: #ccc;
    -webkit-border-radius: 4px;
}
.annotator-editor .annotator-item:first-child textarea {
    min-height: 5.5em;
    -webkit-border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -o-border-radius: 5px 5px 0 0;
    border-radius: 5px 5px 0 0;
}
.annotator-editor .annotator-item input:focus,
.annotator-editor .annotator-item textarea:focus {
    background-color: #FFFFFF;
    outline: 0;
    resize: none;
}
.annotator-editor .annotator-item input[type=radio],
.annotator-editor .annotator-item input[type=checkbox] {
    width: auto;
    min-width: 0;
    padding: 0;
    display: inline;
    margin: 0 4px 0 0;
    cursor: pointer;
}
.annotator-editor .annotator-checkbox {
    padding: 8px 6px;
}
.annotator-filter,
.annotator-filter .annotator-filter-navigation button,
.annotator-editor .annotator-controls {
    text-align: right;
    padding: 3px;
    border-top: 1px solid #d4d4d4;
    background-color: #d4d4d4;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), color-stop(0.6, #dcdcdc), to(#d2d2d2));
    background-image: -moz-linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
    background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
    background-image: linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
    -webkit-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
    -moz-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
    -o-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
    box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
    -webkit-border-radius: 0 0 5px 5px;
    -moz-border-radius: 0 0 5px 5px;
    -o-border-radius: 0 0 5px 5px;
    border-radius: 0 0 5px 5px;
}
.annotator-widget .annotator-controls {
    background-color: transparent;
    background-image: none;
    padding: 8px 0;
    border-top: 0;
}
.annotator-editor.annotator-invert-y .annotator-controls {
    border-top: 0;
    border-bottom: 1px solid rgba(68, 68, 68, 0.24);
    -webkit-border-radius: 5px 5px 0 0;
    -moz-border-radius: 5px 5px 0 0;
    -o-border-radius: 5px 5px 0 0;
    border-radius: 5px 5px 0 0;
}
.annotator-editor a,
.annotator-filter .annotator-filter-property label {
    position: relative;
    display: inline-block;
    padding: 0 6px 0 22px;
    color: #363636;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
    text-decoration: none;
    line-height: 24px;
    font-size: 12px;
    font-weight: bold;
    border: 1px solid #a2a2a2;
    background-color: #d4d4d4;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), color-stop(0.5, #d2d2d2), color-stop(0.5, #bebebe), to(#d2d2d2));
    background-image: -moz-linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
    background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
    background-image: linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
    -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
    -moz-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
    -o-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    -o-border-radius: 5px;
    border-radius: 5px;
}
.annotator-controls .annotator-cancel {
    text-transform: uppercase;
    font-family: "Montserrat", sans-serif;
    font-style: normal;
    font-weight: 600 !important;
    line-height: normal;
    font-size: 12px;
    box-shadow: none;
    border: 0;
    background-color: transparent;
    background-image: none;
    color: #B72319;
    letter-spacing: 0.04em;
}
.annotator-controls .annotator-cancel:after {
    content: "";
    background-image: none;
}
.annotator-controls .annotator-cancel:hover {
    color: #B72319;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none;
}
.annotator-controls .annotator-cancel:active {
    color: #B72319;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none;
}
.annotator-controls .annotator-cancel:focus {
    color: #B72319;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none;
}
.annotator-controls .annotator-save {
    text-transform: uppercase;
    font-family: "Montserrat", sans-serif;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    font-size: 12px;
    box-shadow: none;
    border: 0;
    background-color: #FFFFFF;
    background-image: none;
    color: #30C465;
}
.annotator-controls .annotator-save:after {
    content: "";
    background-image: none;
}
.annotator-controls .annotator-save:hover {
    color: #30C465;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none;
}
.annotator-controls .annotator-save:active {
    color: #30C465;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none;
}
.annotator-controls .annotator-save:focus {
    color: #30C465;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none;
}
.annotator-controls .annotator-focus {
    font-weight: 600 !important;
    text-transform: uppercase;
    color: #30C465 !important;
    border-color: transparent !important;
    background-color: #FFFFFF !important;
    background-image: none !important;
    text-shadow: none !important;
    letter-spacing: 0.04em;
}
.annotator-editor a::after {
    position: absolute;
    top: 50%;
    left: 5px;
    display: block;
    content: "";
    width: 15px;
    height: 15px;
    margin-top: -7px;
    background-position: 0 -90px;
}
.annotator-editor a:hover,
.annotator-editor a:focus,
.annotator-editor a.annotator-focus,
.annotator-filter .annotator-filter-active label,
.annotator-filter .annotator-filter-navigation button:hover {
    outline: 0;
    border-color: #435aa0;
    background-color: #3865f9;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#7691fb), color-stop(0.5, #5075fb), color-stop(0.5, #3865f9), to(#3665fa));
    background-image: -moz-linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
    background-image: -webkit-linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
    background-image: linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.42);
}
.annotator-editor a:hover::after,
.annotator-editor a:focus::after {
    margin-top: -8px;
    background-position: 0 -105px;
}
.annotator-editor a:active,
.annotator-filter .annotator-filter-navigation button:active {
    border-color: #700c49;
    background-color: #d12e8e;
    background-image: -webkit-gradient(linear, left top, left bottom, from(#fc7cca), color-stop(0.5, #e85db2), color-stop(0.5, #d12e8e), to(#ff009c));
    background-image: -moz-linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
    background-image: -webkit-linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
    background-image: linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
}
.annotator-editor a.annotator-save::after {
    background-position: 0 -120px;
}
.annotator-editor a.annotator-save:hover::after,
.annotator-editor a.annotator-save:focus::after,
.annotator-editor a.annotator-save.annotator-focus::after {
    margin-top: -8px;
    background-position: 0 -135px;
}
.annotator-editor .annotator-widget::after {
    background-position: 0 -30px;
}
.annotator-editor.annotator-invert-y .annotator-widget .annotator-controls {
    background-color: #FFFFFF;
}
.annotator-editor.annotator-invert-y .annotator-widget::after {
    background-position: 0 -45px;
    height: 11px;
}
.annotator-resize {
    position: absolute;
    top: 0;
    right: 0;
    width: 12px;
    height: 12px;
    background-position: 2px -150px;
    display: none;
}
.annotator-invert-x .annotator-resize {
    right: auto;
    left: 0;
    background-position: 0 -195px;
}
.annotator-invert-y .annotator-resize {
    top: auto;
    bottom: 0;
    background-position: 2px -165px;
}
.annotator-invert-y.annotator-invert-x .annotator-resize {
    background-position: 0 -180px;
}
.annotator-notice {
    color: #fff;
    position: absolute;
    position: fixed;
    top: -54px;
    left: 0;
    width: 100%;
    font-size: 14px;
    line-height: 50px;
    text-align: center;
    background: black;
    background: rgba(0, 0, 0, 0.9);
    border-bottom: 4px solid #d4d4d4;
    -webkit-transition: top 0.4s ease-out;
    -moz-transition: top 0.4s ease-out;
    -o-transition: top 0.4s ease-out;
    transition: top 0.4s ease-out;
}
.ie6 .annotator-notice {
    position: absolute;
}
.annotator-notice-success {
    border-color: #3665f9;
}
.annotator-notice-error {
    border-color: #ff7e00;
}
.annotator-notice p {
    margin: 0;
}
.annotator-notice a {
    color: #fff;
}
.annotator-notice-show {
    top: 0;
}
.annotator-tags {
    margin-bottom: -2px;
}
.annotator-tags .annotator-tag {
    display: inline-block;
    padding: 0 8px;
    margin-bottom: 2px;
    line-height: 1.6;
    font-weight: bold;
    background-color: #e6e6e6;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    border-radius: 8px;
}
.annotator-filter {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    text-align: left;
    line-height: 0;
    border: 0;
    border-bottom: 1px solid #878787;
    padding-left: 10px;
    padding-right: 10px;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    -o-border-radius: 0;
    border-radius: 0;
    -webkit-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
    -moz-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
    -o-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
    box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
}
.annotator-filter strong {
    font-size: 12px;
    font-weight: bold;
    color: #3c3c3c;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
    position: relative;
    top: -9px;
}
.annotator-filter .annotator-filter-property,
.annotator-filter .annotator-filter-navigation {
    position: relative;
    display: inline-block;
    overflow: hidden;
    line-height: 10px;
    padding: 2px 0;
    margin-right: 8px;
}
.annotator-filter .annotator-filter-property label,
.annotator-filter .annotator-filter-navigation button {
    text-align: left;
    display: block;
    float: left;
    line-height: 20px;
    -webkit-border-radius: 10px 0 0 10px;
    -moz-border-radius: 10px 0 0 10px;
    -o-border-radius: 10px 0 0 10px;
    border-radius: 10px 0 0 10px;
}
.annotator-filter .annotator-filter-property label {
    padding-left: 8px;
}
.annotator-filter .annotator-filter-property input {
    display: block;
    float: right;
    -webkit-appearance: none;
    background-color: #fff;
    border: 1px solid #878787;
    border-left: none;
    padding: 2px 4px;
    line-height: 16px;
    min-height: 16px;
    font-size: 12px;
    width: 150px;
    color: #333;
    background-color: #f8f8f8;
    -webkit-border-radius: 0 10px 10px 0;
    -moz-border-radius: 0 10px 10px 0;
    -o-border-radius: 0 10px 10px 0;
    border-radius: 0 10px 10px 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
    -o-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
}
.annotator-filter .annotator-filter-property input:focus {
    outline: 0;
    background-color: #fff;
}
.annotator-filter .annotator-filter-clear {
    position: absolute;
    right: 3px;
    top: 6px;
    border: 0;
    text-indent: -900em;
    width: 15px;
    height: 15px;
    background-position: 0 -90px;
    opacity: 0.4;
}
.annotator-filter .annotator-filter-clear:hover,
.annotator-filter .annotator-filter-clear:focus {
    opacity: 0.8;
}
.annotator-filter .annotator-filter-clear:active {
    opacity: 1;
}
.annotator-filter .annotator-filter-navigation button {
    border: 1px solid #a2a2a2;
    padding: 0;
    text-indent: -900px;
    width: 20px;
    min-height: 22px;
    -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
    -moz-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
    -o-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
}
.annotator-filter .annotator-filter-navigation button,
.annotator-filter .annotator-filter-navigation button:hover,
.annotator-filter .annotator-filter-navigation button:focus {
    color: transparent;
}
.annotator-filter .annotator-filter-navigation button::after {
    position: absolute;
    top: 8px;
    left: 8px;
    content: "";
    display: block;
    width: 9px;
    height: 9px;
    background-position: 0 -210px;
}
.annotator-filter .annotator-filter-navigation button:hover::after {
    background-position: 0 -225px;
}
.annotator-filter .annotator-filter-navigation .annotator-filter-next {
    -webkit-border-radius: 0 10px 10px 0;
    -moz-border-radius: 0 10px 10px 0;
    -o-border-radius: 0 10px 10px 0;
    border-radius: 0 10px 10px 0;
    border-left: none;
}
.annotator-filter .annotator-filter-navigation .annotator-filter-next::after {
    left: auto;
    right: 7px;
    background-position: 0 -240px;
}
.annotator-filter .annotator-filter-navigation .annotator-filter-next:hover::after {
    background-position: 0 -255px;
}
.annotator-hl-active {
    background: rgba(255, 255, 10, 0.8);
}
.annotator-hl-filtered {
    background-color: transparent;
}
/*  Annotator Touch Plugin - v1.1.1
 *  Copyright 2012-2015, Compendio <www.compendio.ch>
 *  Released under the MIT license
 *  More Information: https://github.com/aron/annotator.touch.js
 */
.annotator-viewer .annotator-touch-controls .annotator-edit::after {
    /* Assign the image once to ensure data uri is not repeated. */
    background-image: url("data:image/png;base64,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");
}
.annotator-selection-handle::after {
    background-image: url("data:image/png;base64,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");
}
.annotator-button::after {
    background-image: url("data:image/png;base64,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");
}
/* Generic Touch Widget Styles */
.annotator-touch-widget * {
    font-family: "Montserrat", sans-serif !important;
    font-weight: 400 !important;
    font-size: 14px !important;
    font-style: normal !important;
}
.annotator-touch-widget {
    font-family: "Montserrat", sans-serif;
    font-weight: 400 !important;
    font-size: 14px !important;
    border: 1px solid rgba(0, 0, 0, 0.8);
    background: rgba(0, 0, 0, 0.85);
    background: -webkit-gradient(linear, left top, left bottom, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)), color-stop(0.5, rgba(0, 0, 0, 0.85)), to(rgba(0, 0, 0, 0.85)));
    background: -moz-linear-gradient(-90deg, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)), color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
    background: -webkit-linear-gradient(-90deg, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)) 50%, color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
    background: linear-gradient(to bottom, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)) 50%, color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border-radius: 4px;
    -webkit-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
    -moz-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
    -ms-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
    -o-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
    /* Removes the tap outline on elements that have bound touch events */
    -webkit-tap-highlight-color: transparent;
}
.annotator-touch-widget #annotator-field-1 {
    display: none;
}
/*.annotator-touch-widget-inner {
  background: #efefef;
  border: 1px solid rgba(0, 0, 0, 0.8);
  margin: 7px;
  padding: 6px;
  line-height: 0;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -ms-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -o-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
}*/
/*.annotator-touch-widget .annotator-button {
  cursor: pointer;
  font-size: 16px;
  line-height: 44px;
  padding-left: 40px;
  padding-right: 20px;
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}*/
.annotator-touch-widget .annotator-button[disabled] {
    opacity: 0.3;
    cursor: default;
}
.annotator-touch-widget .annotator-button::after {
    left: 15px;
}
.annotator-touch-widget .annotator-add::after,
.annotator-touch-widget .annotator-add:hover::after,
.annotator-touch-widget .annotator-add:focus::after,
.annotator-touch-widget .annotator-add.annotator-focus::after {
    margin-top: -7px;
    background-position: 0 -270px;
}
/* Adder Styles */
.annotator-touch-controls {
    position: fixed;
    top: 30%;
    font-size: 10px;
    line-height: 1;
    min-width: auto;
    background: #FEFEFE;
    border:none;
    box-sizing: border-box;
    box-shadow: 8px 2px 32px rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    padding: 4px 16px;
    margin-top: 0;
    margin-left: 0;
    z-index: 3;
    left: 4px;
}
.annotator-touch-controls.annotator-touch-hide {
    right: -9999em;
    opacity: 0;
    -webkit-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
    -moz-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
    -ms-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
    -o-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
    transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
}
/*.annotator-touch-controls .annotator-button {
  line-height: 56px;
}*/
/* Viewer Overrides*/
.annotator-touch-viewer .annotator-widget {
    min-width: 380px;
}
.annotator-touch-viewer div {
    padding: 12px;
}
.annotator-touch-viewer div:first-of-type {
    font-size: 18px;
    padding-top: 20px;
    padding-bottom: 20px;
}
.annotator-touch-viewer .annotator-touch-controls {
    position: absolute;
    top: 0;
    left: auto;
    right: 0;
    display: none;
    background: #fff;
    -webkit-box-pack: end;
    -webkit-box-align: center;
    -webkit-box-orient: horizontal;
    -moz-box-pack: end;
    -moz-box-align: center;
    -moz-box-orient: horizontal;
    box-pack: end;
    box-align: center;
    box-orient: horizontal;
    padding: 10px;
    bottom: 0;
    padding: 0 10px 0 20px;
}
.annotator-touch-viewer li.annotator-visible .annotator-touch-controls {
    display: -webkit-box;
    display: -moz-box;
    display: box;
}
.annotator-touch-viewer .annotator-touch-controls button {
    line-height: 44px;
    padding-left: 40px;
    padding-right: 20px;
    margin-left: 6px;
    border: 0;
}
.annotator-touch-viewer .annotator-touch-controls .annotator-edit::after {
    background-position: 0 -15px;
}
.annotator-touch-controls .annotator-edit {
    color: #2F80ED;
}
.annotator-touch-controls .annotator-delete {
    color: #B72319;
}
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:hover::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:focus::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:active::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit.annotator-focus::after {
    background-position: 0 -30px;
}
.annotator-touch-viewer .annotator-touch-controls button::after {
    left: 15px;
}
/* Editor Overrides */
.annotator-touch-editor {
    position: fixed;
    top: -1000px !important;
    left: 0 !important;
    right: 0;
    bottom: -1000px;
    padding: 1000px 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    display: -webkit-box;
    display: -moz-box;
    display: box;
    -webkit-box-pack: center;
    -webkit-box-align: center;
    -moz-box-pack: center;
    -moz-box-align: center;
    box-pack: center;
    box-align: center;
}
.annotator-touch-editor .annotator-touch-widget {
    pointer-events: all;
    position: relative;
    width: 80%;
    max-width: 680px;
}
.annotator-touch-editor .annotator-touch-widget-inner {
    position: static;
    width: auto;
    padding: 0;
    background: #fff;
}
.annotator-touch-editor .annotator-widget::after {
    display: none;
}
.annotator-touch-editor .annotator-widget .annotator-item {
    border-top-color: rgba(33, 150, 83, 0.24);
}
.annotator-touch-editor .annotator-widget .annotator-item,
.annotator-touch-editor.annotator-editor .annotator-item label,
.annotator-touch-editor.annotator-editor .annotator-item input,
.annotator-touch-editor.annotator-editor .annotator-item textarea {
    font-size: 18px;
}
.annotator-touch-editor.annotator-editor .annotator-item input,
.annotator-touch-editor.annotator-editor .annotator-item label {
    line-height: 30px;
    margin-left: 8px;
}
.annotator-touch-editor.annotator-editor .annotator-item input[checkbox] {
    font-size: large;
}
.annotator-touch-editor .annotator-widget .annotator-item:first-child textarea {
    font-size: 18px;
    background-color: #fff;
    -webkit-border-radius: 3px 3px 0 0;
    -moz-border-radius: 3px 3px 0 0;
    -o-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
}
.annotator-touch-editor .annotator-resize {
    display: none;
}
.annotator-touch-editor .annotator-controls {
    padding: 7px;
    background-color: #fff;
    background-image: none;
}
.annotator-touch-editor .annotator-item-quote {
    font-size: 16px;
    line-height: 1.2;
    border-color: rgba(33, 150, 83, 0.24);
    background-color: rgba(33, 150, 83, 0.24);
    color: #000;
    padding: 10px 7px;
}
.annotator-item-quote span {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #000;
    font-style: italic !important;
}
.annotator-item-quote.annotator-touch-expand span {
    overflow: visible;
    text-overflow: inherit;
    white-space: inherit;
}
.annotator-item-quote button {
    font-size: 14px;
    line-height: 44px;
    margin-top: -13px;
    float: right;
    text-transform: uppercase;
    font-weight: bold;
    color: #a58129;
    border: none;
    background: none;
    margin-left: 10px;
    cursor: pointer;
}
.annotator-button::after {
    background-repeat: no-repeat;
}
/*.annotator-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  position: relative;
  display: inline-block;
  padding: 0 6px 0 22px;
  color: rgb(54, 54, 54);
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
  text-decoration: none;
  line-height: 24px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid rgb(162, 162, 162);
  background-color: rgb(212, 212, 212);
  background-image: -webkit-gradient(
    linear, left top, left bottom,
    from(rgb(245, 245, 245)),
    color-stop(0.5, rgb(210, 210, 210)),
    color-stop(0.5, rgb(190, 190, 190)),
    to(rgb(210, 210, 210))
  );
  background-image: -moz-linear-gradient(
      -90deg,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  background-image: -webkit-linear-gradient(
      -90deg,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  background-image: linear-gradient(
      to bottom,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  -webkit-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -moz-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -o-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
}*/
/*.annotator-button::after {
  position: absolute;
  top: 50%;
  left: 5px;
  display: block;
  content: "";
  width: 15px;
  height: 15px;
  margin-top: -7px;
  background-position: 0 -90px;
}*/
.annotator-button:hover,
.annotator-button:focus,
.annotator-button.annotator-focus {
    color: #fff;
    display: inline-block;
    margin: 0 auto;
    border: 0;
    background: 0;
    cursor: pointer;
    padding: 8px 8px;
    font-weight: 300;
    font-size: 14px;
}
.annotator-button.annotator-focus:first-child {
    border-right: 1px solid #ededed;
}
/*.annotator-button:hover::after,
.annotator-button:focus::after {
  margin-top: -8px;
  background-position: 0 -105px;
}*/
/*.annotator-button:active {
  border-color: rgb(112, 12, 73);
  background-color: rgb(209, 46, 142);
  background-image: -webkit-gradient(
    linear, left top, left bottom,
    from(rgb(252, 124, 202)),
    color-stop(0.5, rgb(232, 93, 178)),
    color-stop(0.5, rgb(209, 46, 142)),
    to(rgb(255, 0, 156))
  );
  background-image: -moz-linear-gradient(
      -90deg,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
  background-image: -webkit-linear-gradient(
      -90deg,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
  background-image: linear-gradient(
      to bottom,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
}

.annotator-button.annotator-save::after {
  background-position: 0 -120px;
}*/
.annotator-button.annotator-save::after,
.annotator-button.annotator-save:focus::after,
.annotator-button.annotator-save.annotator-focus::after {
    margin-top: -8px;
    background-position: 0 -135px;
}
/* Icon only button styles */
[data-annotator-use-icons] .annotator-touch-widget .annotator-button {
    /* width & overflow is required by Android WebKit */
    width: 1px;
    overflow: hidden;
    text-indent: -999em;
    padding-left: 25px;
}
[data-annotator-use-icons] .annotator-touch-controls .annotator-button {
    padding-left: 35px;
}
[data-annotator-use-icons] .annotator-touch-controls .annotator-button::after {
    left: 20px;
}
[data-annotator-use-icons] .annotator-touch-viewer .annotator-touch-controls button {
    padding-left: 25px;
    text-indent: -9000em;
}
[data-annotator-use-icons] .annotator-touch-viewer .annotator-touch-controls button::after {
    left: 15px;
}
[data-annotator-use-icons] .annotator-touch-viewer .annotator-widget {
    min-width: 320px;
}
/* Highlighter Selection Styles */
.annotator-selection-handle {
    cursor: pointer;
    display: block;
    position: absolute;
    width: 44px;
    height: 44px;
    top: 0;
    left: 0;
    padding: 0;
    margin-left: -22px;
    margin-top: -22px;
    border-radius: 50%;
    /* Removes the tap outline on elements that have bound touch events */
    -webkit-tap-highlight-color: transparent;
}
.annotator-selection-handle::after {
    content: "";
    display: block;
    width: 20px;
    height: 20px;
    position: absolute;
    left: 50%;
    margin-left: -10px;
    bottom: -5px;
    background-position: 0 0;
    background-repeat: no-repeat;
}
.annotator-selection-start::after {
    top: -5px;
    bottom: auto;
    background-position: 0 -20px;
}
.annotator-selection-hide .annotator-selection-handle {
    display: none;
}
/* Styles for smaller screens */
@media only screen and (max-width: 480px) {
    .annotator-touch-viewer {
        left: 0 !important;
        width: 100%;
        background: none;
        min-width: 0;
        border: none;
    }
    .annotator-touch-viewer .annotator-widget {
        position: static;
        left: 0;
        width: 100%;
        height: auto;
        min-width: 0;
        -webkit-box-sizing: border-box;
        -webkit-border-radius: none;
        border-radius: none;
    }
    .annotator-touch-viewer .annotator-widget::after {
        display: none;
    }
    .annotator-touch-editor {
        border: none;
        -webkit-box-align: start;
        -moz-box-align: start;
        box-align: start;
    }
    .annotator-touch-editor .annotator-touch-widget {
        width: 100%;
        max-width: auto;
        margin: 0;
        border-color: #333;
        border-left: none;
        border-right: none;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        -ms-box-shadow: none;
        -o-box-shadow: none;
        box-shadow: none;
    }
    .annotator-touch-editor .annotator-touch-widget-inner {
        width: 100%;
        max-width: auto;
        margin: 0;
        border: 0;
    }
    .annotator-touch-editor .annotator-controls {
        border-bottom: 1px solid #D4D4D4;
    }
    .annotator-touch-editor .annotator-touch-widget,
    .annotator-touch-editor .annotator-touch-widget-inner,
    .annotator-touch-editor .annotator-touch-widget .annotator-item:first-child textarea,
    .annotator-touch-editor .annotator-controls {
        border-radius: 0;
    }
}
#htmlContent .annotator-hl {
    background: rgba(255, 255, 10, 0.3);
}
#htmlContent .annotator-hlh {
    background-color: rgba(33, 150, 83, 0.24);
}
#htmlContent .annotator-hl-temporary {
    background: rgba(0, 124, 255, 0.3);
}
.annotator-adder .annotate-btn {
    border-right: 1px solid #ededed;
    display: inline-block;
}
.annotator-adder .annotate-btn:last-child {
    border-right: 0;
}
.annotator-adder a {
    display: block;
    white-space: nowrap;
    color: black;
}
.annotator-adder button,
.annotator-adder a {
    padding: 4px 12px;
}
.annotator-touch-widget-inner .annotator-button {
    width: 25%;
    white-space: nowrap;
    border-right: 1px solid #EDEDED;
    text-align: center;
}
.annotator-touch-widget-inner .annotator-button:last-child {
    border-right: none;
}
.annotator-touch-widget {
    width: 97%;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
    .annotator-touch-widget {
        width: 47%;

    }
}
.annotator-touch-widget .annotator-touch-widget-inner {
    width: 100%;
}
.epub-body{
    padding: 2rem 61.75px !important;
}
#htmlreadingcontent{
    padding-bottom: 2rem;
}
.annotator-notice{
    display: none !important;
}
.annotator-touch-controls{
    background: radial-gradient(261.94% 6870.17% at -55.38% -114.52%, #45EAFF 0%, #6867F6 100%);
    border-radius: 20px;
}
.annotator-touch-controls a {
color:#fff;
}


.annotateColorGreen{
    background-color: #D3E6D1;
    border-radius: 3px;
}
.annotateColorBlue{
    background-color: #CBD1FF;
    border-radius: 3px;
}
.annotateColorYellow{
    background-color: #FEFFD9;
    border-radius: 3px;
}
.annotateColorPicker{
    position: absolute;
    top: -55px;
    background: #fff;
    width: auto;
    padding: 10px;
    border-radius: 5px;
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    border: 1px solid rgba(0,0,0,0.2)
}
.annotateColorPicker:after{
    content: '';
    width: 20px;
    height: 20px;
    position: absolute;
    background: #fff;
    top: 35px;
    transform: rotate(45deg);
    left: 35px;
}
.annotateColorPicker button,
.notesColorBtn,
.annotator-editor .notesColorBtn{
    width: 30px;
    height: 30px;
    border-radius: 50%;
    opacity: 0.6;
    cursor: pointer;
}
.annotator-editor .notesColorBtn:after{
    position: revert;
    width: 0;
    height: 0;
    background-image: none;
}
.annotator-editor .notesColorBtn{
    padding: 0;
}
.annotator-editor .notesColorBtn.green:hover{
    padding: 0;
    background: green;
}
.annotator-editor .notesColorBtn.blue:hover{
    padding: 0;
    background: blue;
}
.annotateColorPicker button.blue,
.notesColorBtn.blue{
    background: blue;
    border: 2px solid transparent;
}
.annotateColorPicker button.green,
.notesColorBtn.green{
    background: green;
    border: 2px solid transparent;
}
.annotateColorPicker button.yellow,
.notesColorBtn.yellow{
    background: yellow;
    border: 2px solid transparent;
}
.annotator-adder .annotate-btn{
    border-right: 1px solid #ededed;
}
.annotateColorPicker button.selected,
.notesColorBtn.selected{
    border-color: black;
}
.notesColorBtn.yellow:hover,
.notesColorBtn.yellow:focus,
.notesColorBtn.yellow:active{
    background: yellow !important;
}
.notesColorBtn.green:hover,
.notesColorBtn.green:focus,
.notesColorBtn.green:active{
    background: green !important;
}
.notesColorBtn.blue:hover,
.notesColorBtn.blue:focus,
.notesColorBtn.blue:active{
    background: blue !important;
}