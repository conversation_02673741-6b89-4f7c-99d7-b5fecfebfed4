html,
body {
  background: #16152F !important;
}
body,
html {
  background: #16152F !important;
}
h2 {
  margin: 0;
}
#multi-step-form-container {
  margin-top: 0rem;
}
.text-center {
  text-align: center;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.pl-0 {
  padding-left: 0;
}
.formSection-wrapper {
  padding-bottom: 30px;
}
.headerSection-wrapper {
  padding: 2rem;
  padding-bottom: 0;
}
@media (max-width: 768px) {
  .headerSection-wrapper {
    padding: 0.2rem;
  }
}
@media (max-width: 768px) {
  .headerSection-wrapper h1 {
    margin-top: 20px;
    font-size: 28px;
  }
}
.button {
  padding: 0.7rem 1.5rem;
  border: 1px solid #F79420;
  background-color: #F79420;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
}
.submit-btn {
  border: 1px solid #0e9594;
  background-color: #F79420;
}
.mt-3 {
  margin-top: 2rem;
}
.d-none {
  display: none;
}
.form-step {
  border: 1px solid #999;
  border-radius: 20px;
  padding: 3rem;
  color: #fff;
}
.font-normal {
  font-weight: normal;
}
ul.form-stepper {
  counter-reset: section;
  margin-bottom: 3rem;
}
ul.form-stepper .form-stepper-circle {
  position: relative;
}
ul.form-stepper .form-stepper-circle span {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
}
.form-stepper-horizontal {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
ul.form-stepper > li:not(:last-of-type) {
  margin-bottom: 0.625rem;
  -webkit-transition: margin-bottom 0.4s;
  -o-transition: margin-bottom 0.4s;
  transition: margin-bottom 0.4s;
}
.form-stepper-horizontal > li:not(:last-of-type) {
  margin-bottom: 0 !important;
}
.form-stepper-horizontal li {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}
.form-stepper-horizontal li:not(:last-child):after {
  position: relative;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 1px;
  content: "";
  top: 32%;
}
.form-stepper-horizontal li:after {
  background-color: #dee2e6;
}
.form-stepper-horizontal li.form-stepper-completed:after {
  background-color: #4da3ff;
}
.form-stepper-horizontal li:last-child {
  flex: unset;
}
ul.form-stepper li a .form-stepper-circle {
  display: inline-block;
  width: 40px;
  height: 40px;
  margin-right: 0;
  line-height: 1.7rem;
  text-align: center;
  background: rgba(0, 0, 0, 0.38);
  border-radius: 50%;
}
.form-stepper .form-stepper-active .form-stepper-circle {
  background-color: #4361ee !important;
  color: #fff;
}
.form-stepper .form-stepper-active .label {
  color: #4361ee !important;
}
.form-stepper .form-stepper-active .form-stepper-circle:hover {
  background-color: #4361ee !important;
  color: #fff !important;
}
.form-stepper .form-stepper-unfinished .form-stepper-circle {
  background-color: #f8f7ff;
}
.form-stepper .form-stepper-completed .form-stepper-circle {
  background-color: #02a802 !important;
  color: #fff;
  font-weight: bolder;
}
.form-stepper .form-stepper-completed .label {
  color: #0e9594 !important;
}
.form-stepper .form-stepper-completed .form-stepper-circle:hover {
  background-color: #0e9594 !important;
  color: #fff !important;
}
.form-stepper .form-stepper-active span.text-muted {
  color: #fff !important;
}
.form-stepper .form-stepper-completed span.text-muted {
  color: #fff !important;
}
.form-stepper .label {
  font-size: 1rem;
  margin-top: 0.5rem;
}
.form-stepper a {
  cursor: default;
}
input[type="radio"]:checked + label {
  background-color: transparent !important;
  color: #fff !important;
}
input[type="radio"]:focus {
  outline: none !important;
  border: none !important;
}
footer,
.mobile-footer-nav {
  display: none !important;
}
#userAccountSetupForm {
  min-height: 400px;
}
p {
  margin-bottom: 5px!important;
}
.button {
  display: block;
  width: 100%;
}
.button:active {
  border: none;
  outline: none;
}
.button:focus {
  border: none;
  outline: none;
}
.btn-prev {
  background: transparent;
  border: 1px solid #F79420;
  color: #fff;
}
.paragraphContent {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  margin-top: 50px;
}
@media (max-width: 768px) {
  .paragraphContent {
    grid-template-columns: repeat(1, 1fr);
    margin-top: 20px;
  }
}
.headerImg {
  width: 100%;
}
.headerImg img {
  width: 100%;
}
@media (max-width: 768px) {
  .form-step {
    padding: 1rem;
  }
}
@media (max-width: 768px) {
  .form-step,
  .form-stepper {
    width: 100% !important;
  }
}
p {
  color: #999;
}
input[type="radio"]:focus + label {
  border: none;
}
.swal-footer {
  text-align: center !important;
}
.section {
  padding: 50px 0 10px 0;
  position: relative;
}
@media (max-width: 767px) {
  .section {
    padding: 10px 0;
    position: relative;
  }
}
.gray-bg {
  background-color: #f5f5f5;
}
img {
  max-width: 100%;
}
img {
  vertical-align: middle;
  border-style: none;
}
/* About Me
---------------------*/
.about-text h3 {
  font-size: 45px;
  font-weight: 700;
  margin: 0 0 6px;
}
@media (max-width: 767px) {
  .about-text {
    padding: 30px;
  }
  .about-text h3 {
    font-size: 35px;
  }
}
.about-text h6 {
  font-weight: 600;
  margin-bottom: 15px;
}
@media (max-width: 767px) {
  .about-text h6 {
    font-size: 18px;
  }
}
.about-text p {
  font-size: 18px;
  max-width: 450px;
}
.about-text p mark {
  font-weight: 600;
  color: #20247b;
}
.about-list {
  padding-top: 10px;
}
.about-list .media {
  padding: 5px 0;
}
.about-list label {
  color: #20247b;
  font-weight: 600;
  width: 160px;
  margin: 0;
  position: relative;
}
.about-list p {
  margin: 0;
  font-size: 15px;
}
.about-avatar {
  height: 350px;
  display: flex;
  justify-content: end;
}
.about-avatar img {
  height: 100%;
  object-fit: cover;
}
@media (max-width: 991px) {
  .about-avatar {
    margin-top: 30px;
    display: flex;
    justify-content: center;
    padding: 5px;
  }
}
.about-section .counter {
  padding: 22px 20px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 0 30px rgba(31, 45, 61, 0.125);
}
@media (max-width: 991px) {
  .about-section .counter {
    padding: 22px 10px;
  }
}
.about-section .counter .count-data {
  margin-top: 10px;
  margin-bottom: 10px;
}
.about-section .counter .count {
  font-weight: 700;
  color: #20247b;
  margin: 0 0 5px;
}
.about-section .counter p {
  font-weight: 600;
  margin: 0;
}
mark {
  background-image: linear-gradient(rgba(252, 83, 86, 0.6), rgba(252, 83, 86, 0.6));
  background-size: 100% 3px;
  background-repeat: no-repeat;
  background-position: 0 bottom;
  background-color: transparent;
  padding: 0;
  color: currentColor;
}
.theme-color {
  color: #fc5356;
}
.dark-color {
  color: #20247b;
}
#shareBtn {
  width: 200px !important;
}
#teacherImg {
  border-radius: 5px;
}
.swal-button--catch {
  background: #F79420 !important;
}
@media (max-width: 991px) {
  .headerContent {
    padding-left: 2rem;
  }
  .morebtn {
    padding-left: 0!important;
  }
}
h3,
p {
  margin-bottom: 0 !important;
}
.lb-wrapper {
  width: 600px;
  margin: 0 auto;
  cursor: pointer;
  transition: all 0.2s ease;
}
.lb-wrapper:hover {
  transform: scale(1.05);
}
.lb-wrapper:hover .lb-content {
  background: rgba(247, 148, 32, 0.2);
}
.lb-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  /*background: rgba(247, 148, 32, 0.6);*/
  background: transparent;
  border-radius: 100px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.2);
  border: 1px solid;
}
.lb-content__img-wrapper {
  width: 6rem;
  height: 5rem;
  margin-right: 1rem;
}
.lb-content__img-wrapper img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}
.lb-content__list {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}
.lb-content__list-votes {
  margin-right: 2rem ;
}
.t-position {
  margin-right: 10px;
}
.t-position h3 {
  font-style: italic;
  font-size: 1.2rem;
}
.top3 .item .score:after {
  content: "Votes" !important;
}
.card-img {
  width: 150px !important;
  height: 150px !important;
  border-radius: 50% !important;
  object-fit: cover;
  border: 2px solid black;
}
.ttNmd {
  font-size: 16px;
  opacity: 0.8;
  font-style: italic;
}
.vBtn {
  width: 200px;
  margin-top: 20px;
}
.top3 .item .name {
  font-weight: bold !important;
}
@media (max-width: 768px) {
  .lb-content__img-wrapper {
    width: 5rem;
    height: 4rem;
    margin-right: 10px;
  }
  .lb-wrapper {
    width: auto;
    margin: 0 auto;
  }
  h3 {
    font-size: 1rem !important;
  }
  .lb-content__list-votes {
    margin-right: 18px!important;
  }
  .mobile-footer-nav {
    display: none !important;
  }
  #nextList {
    padding-left: 5px !important;
    padding-right: 5px !important;
  }
  .t-position {
    margin-right: 5px;
  }
  .top3 .item {
    min-height: 150px;
    margin: 5px;
  }
  .top3 .item .name {
    padding: 0 !important;
  }
}
.close:not(:disabled):not(.disabled):focus,
.close:not(:disabled):not(.disabled):hover {
  outline: none !important;
}
.outline-btn {
  background: transparent;
  border: 1px solid #000 !important;
  color: #000 !important;
}
.outline-btn:hover {
  background: #F79420;
  border-color: #F79420 !important;
  color: #fff !important;
}
.shareBtn-mob {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.shareBtn-mob i {
  margin-right: 0 !important;
}
@media (max-width: 768px) {
  .media-comment {
    display: flex;
    flex-direction: column;
  }
  #comment {
    margin-left: 0px;
  }
  .shareText {
    font-size: 1.1rem;
    font-weight: bolder;
  }
}
.close-icon {
  z-index: 999999999 !important;
}
.item {
  word-wrap: break-word;
}
@media (max-width: 767px) {
  .top3 .item {
    width: 120px !important;
    margin: 2px !important;
  }
}
.outline-btn-1 {
  background: transparent;
  border: 1px solid #fff !important;
  color: #fff !important;
}
.schl {
  font-size: 12px;
}
.modal.fade .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(0.5);
  transform: translate(0, 0) scale(0.5);
}
.modal.show .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(1);
  transform: translate(0, 0) scale(1);
}
