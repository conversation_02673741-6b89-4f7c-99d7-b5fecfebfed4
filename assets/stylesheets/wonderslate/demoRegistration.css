.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
@media only screen and (max-width: 767px) {
  body {
    background: #e59efa;
    background: linear-gradient(0deg, rgba(173, 34, 222, 0.8) 0%, rgba(229, 158, 250, 0.7) 30%, #ffffff 100%);
  }
}
.digital-library-page header {
  position: relative;
  width: 90%;
  margin: 10px auto !important;
  background: transparent;
}
@media (max-width: 1200px) {
  .digital-library-page header {
    width: 100%;
    margin: 0 auto !important;
  }
}
.digital-library-page header .digital-library-logo {
  display: flex;
  align-items: center;
}
.digital-library-page header .digital-library-logo span.logo {
  padding: 0;
  display: inline-block;
  align-items: center;
  height: 60px;
}
.digital-library-page header .digital-library-logo span.logo img {
  width: 180px;
  height: 100%;
}
@media only screen and (max-width: 767px) {
  .digital-library-page header .digital-library-logo span.logo img {
    width: 120px;
  }
}
.digital-library-page header .digital-library-logo span.divider img {
  height: 60px;
}
.digital-library-page header .digital-library-logo span.tagline {
  color: #C7C7C7;
  font-size: 18px;
  letter-spacing: 1px;
}
@media only screen and (max-width: 767px) {
  .digital-library-page header .digital-library-logo span.tagline {
    font-size: 14px;
    color: #777;
  }
}
.digital-library-page .curve-bg {
  position: absolute;
  top: -30px;
  right: 0;
  z-index: -1;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .curve-bg {
    display: none;
  }
}
.digital-library-page .curve-bg img {
  width: 800px;
}
.digital-library-page .banner_wrap {
  width: 90%;
  margin: 0 auto;
}
@media (max-width: 1200px) {
  .digital-library-page .banner_wrap {
    width: 100%;
  }
}
.digital-library-page .banner_wrap .banner_info h2 {
  color: #FF5700;
  text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  line-height: 1.3;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_info h2 {
    font-size: 1.5rem;
    line-height: 1.5;
    text-align: center;
  }
}
@media (max-width: 340px) {
  .digital-library-page .banner_wrap .banner_info h2 {
    font-size: 1.35rem;
  }
}
.digital-library-page .banner_wrap .banner_info p {
  color: #A8A8A8;
}
.digital-library-page .banner_wrap .banner_info p br {
  display: none;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_info p br {
    display: block;
  }
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_info p {
    color: #212121;
    line-height: 22px;
    text-align: center;
    font-size: 15px;
  }
}
@media (max-width: 340px) {
  .digital-library-page .banner_wrap .banner_info p {
    font-size: 13.4px;
  }
}
.digital-library-page .banner_wrap .banner_info .icon-lists .icon-list {
  text-align: center;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_info .icon-lists .icon-list {
    margin-bottom: 15px;
  }
}
.digital-library-page .banner_wrap .banner_info .icon-lists .icon-list p {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.48);
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_info .icon-lists .icon-list p {
    color: #212121;
  }
}
.digital-library-page .banner_wrap .banner_info .icon-lists .icon-list p br {
  display: block;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_info .bg-white {
    justify-content: center;
    background: transparent !important;
  }
}
.digital-library-page .banner_wrap .banner_info .bg-white p {
  font-size: 14px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
}
.digital-library-page .banner_wrap .banner_register_form {
  margin-top: -80px;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_register_form {
    margin-top: 0;
  }
}
.digital-library-page .banner_wrap .banner_register_form .form-info {
  background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
  backdrop-filter: blur(10px);
  border-radius: 20px;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_register_form .form-info {
    background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
  }
}
.digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal {
  margin-top: -70px;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal {
    margin-top: -60px;
  }
}
.digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal .form-group {
  background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #FFD000 0%, #FF5700 100%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
}
.digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal .form-group h3 {
  color: rgba(255, 255, 255, 0.48);
  font-size: 1.5rem;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal .form-group h3 {
    font-size: 1.25rem;
  }
}
.digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input {
  color: #212121;
  height: 35px;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input {
    font-size: 15px;
  }
}
.digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input:focus {
  box-shadow: none;
}
@media (max-width: 340px) {
  .digital-library-page .banner_wrap .banner_register_form .form-info .form-horizontal .form-group .alert {
    font-size: 14px !important;
  }
}
.digital-library-page .banner_wrap .banner_register_form .form-info button.submit-btn {
  background: #FF9901;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  width: 85%;
}
.digital-library-page .banner_wrap .banner_register_form .form-info .bottom-image {
  margin-bottom: -70px;
}
@media only screen and (max-width: 767px) {
  .digital-library-page .banner_wrap .banner_register_form .form-info .bottom-image {
    margin-bottom: 0;
  }
}
