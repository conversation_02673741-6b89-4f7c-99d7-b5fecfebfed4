.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
.nextexam_section .store1_topbanner .publisher_logo {
  border-radius: 10px;
}
.nextexam_section .store1_topbanner .publisher_logo h1,
.nextexam_section .store1_topbanner .publisher_logo h2 {
  font-family: "Times New Roman" !important;
}
.nextexam_section .store1_topbanner .bg-banner {
  border-radius: 10px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  position: relative;
  overflow: hidden;
}
.nextexam_section .store1_topbanner .bg-banner:before {
  content: '';
  position: absolute;
  top: -30px;
  right: -30px;
  background-image: url("../../images/ws/library-btn-bg.svg");
  background-repeat: no-repeat;
  width: 150px;
  height: 120px;
  opacity: 0.5;
}
.nextexam_section .store1_topbanner .bg-banner:after {
  content: '';
  position: absolute;
  bottom: -40px;
  left: 0;
  background-image: url("../../images/ws/library-btn-bg.svg");
  background-repeat: no-repeat;
  width: 150px;
  height: 120px;
  opacity: 0.5;
}
@media only screen and (max-width: 767px) {
  .nextexam_section .store1_topbanner .bg-banner {
    min-height: 150px;
  }
}
.nextexam_section .store1_topbanner .bg-banner h1 {
  font-size: 40px;
}
@media only screen and (max-width: 767px) {
  .nextexam_section .store1_topbanner .bg-banner h1 {
    font-size: 30px;
  }
}
.nextexam_section .store1_topbanner .bg-banner h3 {
  font-size: 22px;
}
@media only screen and (max-width: 767px) {
  .nextexam_section .store1_topbanner .bg-banner h3 {
    font-size: 18px;
  }
}
.nextexam_section .store1_index_accordion .card {
  border-radius: 0 0 10px 10px;
  box-shadow: none !important;
}
.nextexam_section .store1_index_accordion .card-header a {
  background: radial-gradient(99.37% 2306.89% at 2.52% 6.82%, #962F9D 0%, #C73D84 100%) !important;
  border-radius: 10px;
}
.nextexam_section .store1_index_accordion .card-header a.collapsed {
  background: radial-gradient(99.37% 2306.89% at 2.52% 6.82%, #962F9D 0%, #C73D84 100%) !important;
}
.nextexam_section .store1_index_accordion .card-body .name_of_chapter {
  background: none !important;
  border: none;
  text-align: center;
}
.nextexam_section .store1_index_accordion .card-body .name_of_chapter a {
  width: 100%;
  height: 100px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px 15px 15px 15px;
  border: 1px solid;
  padding: 10px;
  font-size: 14px;
  color: #AE3691;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
.nextexam_section .store1_index_accordion .card-body .name_of_chapter a:hover {
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.15);
  color: #FFBF02;
  text-decoration: none;
}
@media only screen and (max-width: 767px) {
  .nextexam_section .store1_social_icons .social-icons {
    width: 100%;
  }
}
.nextexam_section .store1_social_icons .social-icons li {
  padding: 0;
  margin: 0 5px;
}
.nextexam_section .store1_social_icons .social-icons li a {
  display: flex;
  align-items: center;
  justify-content: center;
}
.nextexam_section h5 {
  font-family: 'Poppins', sans-serif;
}
.nextexam_section .monthly-test-btn {
  background: radial-gradient(99.37% 2306.89% at 2.52% 6.82%, #962F9D 0%, #C73D84 100%);
  color: #ffffff;
  border: none;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}
.nextexam_section .monthly-test-btn:hover {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
}
.nextexam_section .monthly-test-btn:focus,
.nextexam_section .monthly-test-btn:active {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}
.nextexam_section p {
  font-size: 16px;
  margin-bottom: 0.75rem;
}
@media (max-width: 767px) {
  .nextexam_section p {
    font-size: 15px;
  }
}
@media (max-width: 768px) {
  .nextexam_section .page_title h3 {
    font-size: 22px;
  }
}
.nextexam_section .page_title .back-arrow {
  color: #7F28A8;
  font-size: 32px;
  margin-right: 0.75rem;
  cursor: pointer;
}
@media (max-width: 767px) {
  .nextexam_section .page_title .back-arrow {
    display: none;
  }
}
.nextexam_section .show-all,
.nextexam_section .show-less {
  float: right;
  font-size: 15px;
  margin-top: -15px;
  cursor: pointer;
  color: #C73D84;
}
