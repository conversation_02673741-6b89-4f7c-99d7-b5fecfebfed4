.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
* {
  margin: 0;
}
main {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  min-height: 500px;
}
main .institute-main-img {
  position: absolute;
  right: 0;
  top: 0;
  width: 50%;
  z-index: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main .institute-main-img {
    width: 70%;
  }
}
main div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: row;
  z-index: 1;
}
main div .logo {
  display: flex;
  flex-direction: row;
  width: 40%;
  margin-left: 100px;
  margin-top: 30px;
  align-items: center;
  justify-content: flex-start;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main div .logo {
    flex-direction: column;
    width: unset;
    margin-left: 20px;
  }
}
main div .logo .ws-logo-align {
  margin-left: 20px;
}
main div .logo .ws-logo-align img {
  width: 100%;
}
main div .logo .pub-logo {
  max-height: 100px;
  width: auto;
}
main div .logo img {
  margin-right: 30px;
}
main div button {
  padding: 10px 40px;
  border-radius: 15px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  color: white;
  margin-right: 100px;
  border: 2px solid #fff;
  margin-top: 30px;
  outline: none !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main div button {
    font-size: 32px;
  }
}
main .institute-info {
  display: flex;
  flex-direction: column;
  margin-top: 20vh;
  width: 40%;
  margin-left: 100px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main .institute-info {
    width: 80%;
    margin-top: 30vh;
    margin-left: 40px;
  }
}
main .institute-info h1 {
  padding-bottom: 20px;
  font-weight: 700;
  font-size: 22px;
  color: #444444;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main .institute-info h1 {
    font-size: 32px;
  }
}
main .institute-info p {
  padding-bottom: 20px;
  font-weight: 400;
  font-size: 14px;
  line-height: 25px;
  color: #444444;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main .institute-info p {
    font-size: 30px;
  }
}
main .institute-info .button-box {
  margin-top: 30px;
}
main .institute-info .button-box .login {
  padding: 10px 50px;
  border-radius: 15px;
  background: transparent;
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  outline: none;
  border: 2px solid #333;
  color: #333;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main .institute-info .button-box .login {
    padding: 20px 90px;
    font-size: 32px;
  }
}
main .institute-info .button-box .logout {
  padding: 10px 50px;
  border-radius: 15px;
  background: radial-gradient(235.67% 2987.36% at -21.77% -33.82%, #DA6AFF 0%, #9B05CE 100%);
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  outline: none;
  border-color: transparent;
  color: white;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  main .institute-info .button-box .logout {
    padding: 20px 90px;
    font-size: 32px;
  }
}
.aboutus {
  display: flex;
  justify-content: center;
  width: 80%;
  flex-direction: column;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  position: relative;
  border-radius: 15px;
  margin-top: 250px;
}
.aboutus .section-heading {
  position: absolute;
  top: -14px;
  right: 15%;
  background-color: white;
  font-size: 22px;
  color: rgba(68, 68, 68, 0.34);
  font-weight: 700;
}
.aboutus .row {
  display: flex;
  justify-content: space-between;
  padding: 50px;
  margin-top: 70px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .aboutus .row {
    flex-direction: column;
  }
}
.aboutus .row-reverse {
  flex-direction: row-reverse;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .aboutus .row-reverse {
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
.aboutus .left {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .aboutus .left {
    width: 100%;
  }
}
.aboutus .right {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-self: center;
  padding: 20px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .aboutus .right {
    width: 100%;
  }
}
.aboutus .right h1 {
  padding-bottom: 20px;
  font-size: 22px;
  font-weight: 700;
  color: #444444;
  line-height: 25px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .aboutus .right h1 {
    font-size: 32px;
  }
}
.aboutus .right p {
  font-size: 14px;
  font-weight: 400;
  color: #444444;
  line-height: 25px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .aboutus .right p {
    font-size: 20px;
  }
}
.institute-footer {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
  background: #212121;
  color: white;
  padding: 100px 0px;
  margin-top: 100px;
}
.institute-footer .institute-footer-logo {
  text-align: center;
}
.institute-footer .institute-footer-logo img {
  margin-bottom: 30px;
  max-height: 70px;
  height: auto;
}
.institute-footer .footer-right-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.institute-footer .footer-right-content .sm {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
}
.institute-footer .footer-right-content .sm ul {
  display: flex;
  flex-direction: row;
  padding-left: 0px;
}
.institute-footer .footer-right-content .sm ul li {
  list-style-type: none;
  display: flex;
}
.institute-footer .footer-right-content .sm ul li a {
  text-decoration: none;
}
.institute-footer .footer-right-content .sm ul li a i {
  color: white;
  font-size: 12px;
}
.institute-footer .footer-right-content .contact-info {
  display: flex;
  flex-direction: column;
}
.institute-footer .footer-right-content .contact-info .phone {
  display: flex;
  flex-direction: row;
  margin: 10px;
  justify-content: center;
  align-items: center;
}
.institute-footer .footer-right-content .contact-info .phone p {
  padding: 10px;
}
.institute-footer .footer-right-content .contact-info .email-id {
  display: flex;
  justify-content: center;
}
.store-link .d-flex {
  border: 1px solid #CCC;
  border-radius: 20px;
  background-color: #FFF;
  box-shadow: 0 2px 4px #DDD;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store-link h3 {
    font-size: 28px;
  }
}
.store-link button {
  padding: 10px 40px;
  border-radius: 15px;
  border: 1px solid #9B05CE;
  cursor: pointer;
  font-size: 15px;
  font-weight: 400;
  outline: none;
  color: #9B05CE;
  background-color: #FFF;
  box-shadow: 0 2px 4px #DDD;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store-link button {
    font-size: 30px;
  }
}
.store-link lottie-player {
  width: 300px;
}
