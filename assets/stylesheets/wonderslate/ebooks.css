.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif;
  font-style: italic;
  font-size: 14px;
  color: #C13B87 !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif;
  font-style: italic;
  font-size: 14px;
  color: #C13B87 !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif;
  font-style: italic;
  font-size: 14px;
  color: #C13B87 !important;
}
.ebooks .jumbotron h1 {
  font-family: 'Poppins', sans-serif;
  text-align: left;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait) {
  .ebooks .jumbotron h1 {
    font-size: 20px;
  }
}
.ebooks .search-ebooks {
  border-radius: 50px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.08);
}
.ebooks .search-ebooks i {
  color: #9B51E0;
  font-size: 20px;
}
.ebooks .form-inline {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.24), 0px 0px 2px rgba(0, 0, 0, 0.12);
  border-radius: 5px;
}
.ebooks .form-inline input[type="text"] {
  position: relative;
  z-index: 10;
  padding-left: 20px;
  border-radius: 5px;
  box-shadow: none;
  color: #98309C;
}
.ebooks .form-inline #search-btn {
  position: relative;
  z-index: 10;
  background-color: transparent;
}
.ebooks .form-inline #search-btn i {
  position: relative;
  z-index: 10;
  color: #98309C;
  line-height: 1.5;
}
.ebooks .form-inline ul.typeahead {
  right: 0;
  box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.08);
}
.ebooks .form-inline ul.typeahead li a {
  white-space: normal;
  padding-top: 7px;
  padding-bottom: 7px;
}
.ebooks .ebooks_filter select {
  border: 2px solid rgba(68, 68, 68, 0.48);
  box-sizing: border-box;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  color: grey;
  font-style: italic;
  font-size: 14px;
  font-family: 'Poppins', sans-serif;
  height: 45px;
}
.ebooks .ebooks_filter select option {
  font-style: normal;
  font-weight: normal;
}
.ebooks .ebooks_filter select:hover {
  border-color: #FFD602;
  color: #212121;
}
.ebooks .ebooks_filter select:focus {
  border-color: #FFD602;
  color: #212121;
}
.ebooks .ebooks_filter select.background-bg {
  background: radial-gradient(131.94% 887.35% at 7.6% 9.72%, #FFD602 0%, #FFBF02 100%);
  color: #212121;
  font-style: normal;
  font-weight: normal;
  border-color: #FFD602;
}
.ebooks .store #ebooksHeader {
  color: #7F28A8;
}
.ebooks .store .topSchoolBooks {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  background-color: #ffffff;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
}
.ebooks .store .topSchoolBooks:hover {
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
.ebooks .store .topSchoolBooks .image-wrapper .uncover {
  padding: 5px;
}
.ebooks .store .topSchoolBooks .content-wrapper h3 {
  font-family: 'Poppins', sans-serif;
  color: #212121;
  font-size: 13px;
}
.ebooks .store .topSchoolBooks .content-wrapper p.price {
  font-family: 'Rubik', sans-serif !important;
  color: #CE0000;
  padding-top: 7px;
}
.ebooks .store .topSchoolBooks .content-wrapper p.price span {
  font-family: 'Rubik', sans-serif !important;
  font-size: 12px;
}
.ebooks .more_for_you h4 {
  color: #7F28A8;
}
.ebooks .more_for_you .bg-light .more_lists {
  overflow-x: auto;
}
.ebooks .more_for_you .bg-light .more_lists a img {
  margin-bottom: 10px;
  width: 70px;
  height: 70px;
}
.ebooks .more_for_you .bg-light .more_lists a:hover {
  text-decoration: none;
}
@media only screen and (max-width: 767px) {
  .shapemobile {
    display: none;
  }
  .shapemobile.backMenu {
    display: flex !important;
  }
}
.sub-section {
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin-top: 50px;
}
.sub-section .row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 80%;
  padding: 50px 0px;
  margin-top: 60px;
  border-radius: 10px;
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  margin-bottom: 100px;
  margin-top: 100px;
  min-height: 400px;
}
@media (max-width: 900px) {
  .sub-section .row {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
    position: static;
    height: 600px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}
@media (max-width: 600px) {
  .sub-section .row {
    -webkit-box-orient: vertical;
    -webkit-box-direction: reverse;
    -ms-flex-direction: column-reverse;
    flex-direction: column-reverse;
    position: static;
    height: 540px;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}
.sub-section .row .section-img {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 50%;
}
@media (max-width: 900px) {
  .sub-section .row .section-img {
    width: 58%;
  }
}
@media (max-width: 700px) {
  .sub-section .row .section-img {
    width: 90%;
  }
}
.sub-section .row .section-img img {
  width: 388px;
}
@media (max-width: 700px) {
  .sub-section .row .section-img img {
    margin-bottom: -85px;
    width: 100%;
  }
}
.sub-section .row .section-img h2 {
  font-size: 46px;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.48);
  font-size: mixed;
  z-index: 100;
  text-align: left;
  margin-right: 110px;
  margin-bottom: 0px;
}
.sub-section .row .section-img p {
  font-size: 16px;
  color: rgba(68, 68, 68, 0.48);
  font-weight: 300;
  padding-top: 10px;
  z-index: 100;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  margin-right: 100px;
  padding-top: 0px;
  margin-top: 0px;
}
.sub-section .row .form-container .para-space {
  padding-bottom: 20px;
}
.sub-section .row .section-box {
  display: -webkit-box;
  display: -ms-flexbox;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  max-width: 380px;
  min-width: 250px;
  width: 50%;
  min-height: 300px;
  margin-top: -235px;
  border-radius: 20px;
  color: #fff;
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  background: #fff;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: flex-start;
  padding: 30px;
}
@media (max-width: 900px) {
  .sub-section .row .section-box {
    margin-top: -180px;
    width: 80%;
    margin-bottom: 50px;
    padding: 0px;
    padding-bottom: 30px;
  }
}
@media (max-width: 700px) {
  .sub-section .row .section-box {
    margin-top: -150px;
    width: 80%;
    padding-top: 30px;
  }
}
.sub-section .row .section-box h3 {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #808080;
  text-align: left;
  padding: 10px 32px;
}
@media (max-width: 700px) {
  .sub-section .row .section-box h3 {
    padding: 20px;
    margin-top: 20px;
  }
}
.sub-section .row .section-box p {
  padding: 10px 32px;
  font-size: 14px;
  color: rgba(68, 68, 68, 0.48);
  width: 80%;
}
@media (max-width: 700px) {
  .sub-section .row .section-box p {
    padding: 10px 20px;
  }
}
.sub-section .row .section-box form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  width: 100%;
  padding-bottom: 30px;
}
.sub-section .row .section-box form input,
.sub-section .row .section-box form textarea {
  width: 87%;
  padding: 12px;
  border-radius: 5px;
  outline: none;
  border: none;
  margin-bottom: 1rem;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  font-family: "poppins", sans-serif;
}
.sub-section .row .section-box form input::placeholder,
.sub-section .row .section-box form textarea::placeholder {
  font-style: italic;
  font-size: 12px;
}
.sub-section .row .section-box form .send-btn {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  width: 80%;
  color: black;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  padding: 12px;
  border-radius: 5px;
  outline: none;
  border: 1px solid #fff;
  color: #fff;
  background: #ff9901;
  -webkit-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  -webkit-transition: 1s;
  transition: 1s;
}
.sub-section .row .section-box form .send-btn:hover {
  border-color: transparent;
}
.sub-section .row .form-heading {
  color: rgba(255, 255, 255, 0.48);
  font-weight: bold;
  font-size: 27px;
  margin-top: 40px;
}
.box-orange {
  background: radial-gradient(168.8% 414.17% at -18.32% -15.74%, #ffcea0 0%, #ff5e70 100%) !important;
}
.box-orange h3 {
  color: #fff !important;
}
.box-orange p {
  color: #fff !important;
}
.form-box {
  background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #ffd000 0%, #ff5700 100%) !important;
}
.ebooks-selection {
  background: #FAFAFA;
  border: 1px solid #ffd000;
}
.book-publisher-name {
  font-weight: normal;
  font-size: 7px;
  letter-spacing: -0.015em;
  text-transform: uppercase;
  color: #ABABAB !important;
}
.filter-icon-show-hide {
  position: absolute;
  top: -30px;
  right: 0;
}
.hiddenFilters {
  display: none;
}
@media (max-width: 900px) {
  .form-box {
    max-height: 300px !important;
  }
}
@media (max-width: 700px) {
  .form-box {
    max-height: 300px !important;
  }
}
@media (max-width: 700px) {
  .form-box {
    max-height: 300px !important;
  }
}
.form-box h3 {
  color: #fff !important;
}
.form-box p {
  color: #fff !important;
}
@media (min-width: 1024px) {
  .mobile {
    display: block;
  }
}
@media (max-width: 900px) {
  .mobile {
    display: none;
  }
}
.box-orange-row {
  background: radial-gradient(102.75% 341.89% at 4.15% 8.58%, #ffae00 0%, #f84b4c 100%);
}
.sharebooks {
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: radial-gradient(100% 100% at 0% 37.1%, #942F9E 0%, #C93D83 100%);
  cursor: pointer;
}
.sharebooks i {
  font-size: 18px;
}
.filter-icon-show-hide {
  position: absolute;
  top: -12px;
  right: 10px;
}
