/* App In App Styles */
.app_in_app {
  padding-bottom: 50px;
}
.app_in_app .mdl-layout__container {
  z-index: 10000;
}
.app_in_app .search-btn-mobile {
  color: #212121 !important;
  display: flex;
  align-items: center;
}
.app_in_app .mdl-layout__header {
  margin-bottom: 0;
  background-color: white !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .app_in_app .mdl-layout__header .mdl-navigation a.mdl-button {
    padding: 0;
    font-size: 16px;
    text-transform: unset;
    margin: 0 15px;
    width: auto;
    min-width: auto;
    border-radius: 0;
    line-height: 55px;
    height: 55px;
    color: #212121;
  }
}
.app_in_app .mdl-layout__header .mdl-navigation a.mdl-button:hover,
.app_in_app .mdl-layout__header .mdl-navigation a.mdl-button:focus,
.app_in_app .mdl-layout__header .mdl-navigation a.mdl-button:focus:active {
  background-color: transparent;
  color: #153252;
}
.app_in_app .mdl-layout__header .mdl-navigation a.mdl-button.active {
  color: #153252;
  border-bottom: 2px solid #153252;
}
@media (max-width: 575.98px) {
  .app_in_app .mdl-layout__header .mdl-layout-spacer {
    position: fixed;
    left: 0;
    right: 0;
    top: 7px;
    width: 100%;
    min-width: 100%;
    z-index: 10;
    transform: translateX(100%);
    transition: all 0.3s linear;
  }
}
@media (max-width: 575.98px) {
  .app_in_app .mdl-layout__header .mdl-layout__header-row {
    justify-content: center;
  }
}
@media (max-width: 575.98px) {
  .app_in_app .mdl-layout__header .showing-mobile-search .mdl-layout-spacer {
    transform: translateX(0);
    padding-left: 40px !important;
  }
}
@media (max-width: 575.98px) {
  .app_in_app .mdl-layout__header .showing-mobile-search .ebooks .search-close-btn {
    display: block;
    color: #6C757D !important;
  }
}
@media (max-width: 575.98px) {
  .app_in_app .mdl-layout__header .showing-mobile-search .mdl-navigation {
    display: none !important;
  }
}
.app_in_app .mdl-layout__header .ebooks .global-search button {
  color: #153252 !important;
}
.app_in_app .mdl-layout__header .ebooks .global-search input[type="text"] {
  font-size: 15px;
}
@media (max-width: 575.98px) {
  .app_in_app .mdl-layout__header .ebooks .search-close-btn {
    margin-left: 0 !important;
    position: absolute;
    z-index: 10;
    top: 4px;
    left: -35px;
    padding: 0 !important;
    height: 22px !important;
    display: none;
  }
}
.app_in_app .bookTemplate #book-read-material .tab-content > .tab-pane {
  min-height: calc(100vh - 150px);
}
.app_in_app .bookTemplate #htmlreadingcontent {
  margin-top: 0 !important;
}
.app_in_app .bookTemplate #htmlreadingcontent .epub-view iframe {
  height: inherit !important;
}
.app_in_app .bookTemplate #htmlreadingcontent iframe {
  height: 100vh !important;
  border: 1px solid #4A4A4A;
  margin-top: 0 !important;
}
.app_in_app .bookTemplate #htmlreadingcontent .mdl-button.download {
  margin-top: 20px;
  width: 200px !important;
  background: #153252;
  color: #FFFFFF;
  border-radius: 5px;
  box-shadow: 0 2px 4px #0000001A;
}
.app_in_app .bookTemplate #htmlContent .pr-back-btn {
  top: -10px;
  left: -15px;
}
.app_in_app .bookTemplate .export-notes {
  top: 102px;
}
.app_in_app .bookTemplate .export-notes .no-notes-created {
  padding: 10px;
}
.app_in_app .bookTemplate .export-notes .notes-creation-header {
  padding: 0.5rem 0;
}
.app_in_app .bookTemplate .export-notes .notes-creation-header-title {
  font-size: 16px;
  color: #212121;
}
.app_in_app .bookTemplate .export-notes .notes-list-wrapper .notes-list-item {
  padding: 1rem 1rem 0;
}
.app_in_app .bookTemplate .export-notes .notes-list-wrapper .notes-list-item span,
.app_in_app .bookTemplate .export-notes .notes-list-wrapper .notes-list-item p {
  font-size: 14px !important;
}
.app_in_app .bookTemplate .export-notes .notes-list-wrapper .notes-list-item .notes-created-by-user {
  margin-left: 10px;
  width: 100%;
  padding-bottom: 7px;
}
.app_in_app .bookTemplate .export-notes .notes-list-wrapper .notes-list-item .comment-by-user {
  padding: 10px;
}
.app_in_app.hasScrolled .bookTemplate .export-notes {
  top: 45px;
}
.app_in_app.hasScrolled .bookTemplate .shadowHeader {
  top: 45px;
}
.app_in_app .ebooks .ebooks_filter {
  width: 100%;
  margin: 0 auto !important;
  padding-top: 10px !important;
}
.app_in_app .ebooks .ebooks_filter .align-items-center {
  padding-bottom: 5px !important;
}
.app_in_app .ebooks .ebooks_filter h5 {
  font-size: 14px;
}
.app_in_app .ebooks .ebooks_filter #resetFilter {
  font-size: 12px;
}
.app_in_app .ebooks .ebooks_filter select {
  height: auto;
  border-width: 1px;
  margin-bottom: 10px !important;
}
.app_in_app .ebooks .ebooks_filter select.background-bg {
  background-color: beige;
  background-image: none !important;
}
.app_in_app .loader-wrapper .loader {
  color: beige !important;
}
.app_in_app .my_books {
  margin-top: 30px;
}
.app_in_app .my_books .page_title {
  display: none;
}
.app_in_app .my_books .no-books-available .click-here-link {
  background-color: #153252 !important;
  color: #FFFFFF;
}
.app_in_app .my_books p#total-books-of-user {
  display: inline;
  font-size: 12px;
  padding-right: 10px;
}
.app_in_app .my_books p#total-books-of-user:before {
  content: '(';
}
.app_in_app .my_books p#total-books-of-user:after {
  content: ')';
}
.app_in_app .my_books #subjectFilter .dropdown {
  display: flex;
}
.app_in_app .my_books #subjectFilter .dropdown #sortBy {
  width: auto;
}
.app_in_app .my_books #content-data-books {
  margin-top: -55px;
}
.app_in_app .my_books #content-data-books h4 {
  margin-bottom: 20px;
}
.app_in_app .ebook_detail .container .mt-3 {
  margin-top: 0 !important;
}
.app_in_app .ebook_detail .book_info .book_buttons .col #buyNow,
.app_in_app .ebook_detail .book_info .book_buttons .col #linkOthers {
  color: #FFFFFF;
}
.app_in_app .ebook_detail .book_info .book_buttons .col #linkTestandLibrary,
.app_in_app .ebook_detail .book_info .book_buttons .col #linkLibrary {
  width: 100% !important;
}
.app_in_app .bookTemplate {
  background-color: #F4F5FA;
  height: auto;
}
.app_in_app .bookTemplate .chapterSection {
  z-index: 991;
}
.app_in_app .bookTemplate #book-sidebar {
  background: #F4F5FA;
  padding-bottom: 0;
  height: calc(100vh - 80px);
}
.app_in_app .bookTemplate #book-sidebar .mobile-title {
  background: #F4F5FA;
  margin-top: 56px;
  z-index: 98;
}
.app_in_app .bookTemplate #book-sidebar .mobile-title p {
  line-height: normal;
}
.app_in_app .bookTemplate #book-sidebar .backtolibrary {
  font-weight: normal !important;
}
.app_in_app .bookTemplate #book-sidebar ul.chapter-sections {
  display: none;
}
.app_in_app .bookTemplate #book-sidebar .side-content ol {
  padding-left: 10px !important;
}
.app_in_app .bookTemplate #book-sidebar .side-content ol li.chapter-name {
  font-size: 15px;
  position: relative;
  margin-right: 1.5rem;
  padding-right: 20px;
}
.app_in_app .bookTemplate #book-sidebar .side-content ol li.chapter-name i {
  position: absolute;
  right: 0;
  top: 10px;
}
.app_in_app .bookTemplate #book-sidebar .side-content ol li.chapter-name.orangeText a {
  font-size: 15px;
}
.app_in_app .bookTemplate .mobChapname {
  top: 56px;
  transition: all 0.3s;
  z-index: 991;
}
.app_in_app .bookTemplate .shadowHeader {
  z-index: 991;
  height: 0;
  position: fixed;
  top: 55px !important;
  background: #FFF;
  box-shadow: 0 2px 4px #0000001A;
  transition: all 0.3s;
}
.app_in_app .bookTemplate .shadowHeader .tab-header .navbar {
  height: auto;
}
.app_in_app .bookTemplate .shadowHeader .tab-header .contentEdit {
  position: fixed;
  top: 60px;
  right: 0;
  transition: all 0.3s;
  overflow: hidden;
}
.app_in_app .bookTemplate .shadowHeader .prevnextbtn {
  position: fixed;
  top: 65px;
  width: 100% !important;
  justify-content: center !important;
  transition: all 0.3s;
}
.app_in_app .bookTemplate .shadowHeader .prevnextbtn button {
  margin: 0 5px;
  width: 70px;
}
.app_in_app .bookTemplate #book-read-material {
  padding: 0;
  margin-bottom: 70px !important;
}
.app_in_app .bookTemplate #book-read-material .all-container .container-wrapper .media {
  padding: 0;
}
.app_in_app .bookTemplate #book-read-material .all-container .container-wrapper .media i {
  margin-left: 0;
}
.app_in_app .bookTemplate #book-read-material .all-container .container-wrapper .media .title {
  margin-bottom: 5px;
}
.app_in_app .bookTemplate #book-read-material .all-container .container-wrapper .media .readnow {
  padding-right: 15px;
}
.app_in_app .bookTemplate #book-read-material .all-container .container-wrapper a.listen-btn {
  border-color: #6C757D;
  padding-left: 20px;
}
.app_in_app .bookTemplate #book-read-material #content-data-all {
  position: relative;
  z-index: 99;
}
.app_in_app .bookTemplate #book-read-material #htmlContent {
  padding: 0 10px;
  margin-bottom: 20px;
}
.app_in_app .bookTemplate #book-read-material #back-slider {
  position: fixed;
  top: 65px;
  z-index: 991;
  left: 0;
  background: #FFFFFF;
  transition: all 0.3s;
}
.app_in_app .bookTemplate #book-read-material #back-slider i {
  font-size: 16px;
  margin-right: 0 !important;
}
.app_in_app .bookTemplate .price-wrapper {
  padding: 0.5rem 0;
  bottom: 80px;
  z-index: 991;
}
.app_in_app .bookTemplate .price-wrapper .preview-book-btns {
  margin-left: 0;
}
.app_in_app .bookTemplate .price-wrapper .preview-book-btns .btn-book-buy {
  margin: 0.75rem auto;
  background: #153252 !important;
  width: 130px;
  font-weight: 500;
}
.app_in_app .bookTemplate.book_preview #book-sidebar {
  height: calc(100vh - 140px) !important;
}
.app_in_app .bookTemplate.book_preview .read-content.col-md-12 .price-wrapper {
  display: none !important;
}
.app_in_app.hasScrolled .bookTemplate .shadowHeader {
  top: 0 !important;
  background: #153252;
  box-shadow: 0 2px 4px #0000001A;
}
.app_in_app.hasScrolled .bookTemplate #book-sidebar .mobile-title {
  background: #153252;
}
.app_in_app.hasScrolled .bookTemplate .mobChapname {
  background: #153252;
  top: 0;
}
.app_in_app.hasScrolled .bookTemplate .tab-header .prevnextbtn {
  top: 8px;
}
.app_in_app.hasScrolled .bookTemplate .tab-header .contentEdit {
  top: 5px;
}
.app_in_app.hasScrolled .bookTemplate .tab-header .contentEdit i {
  color: #FFFFFF !important;
}
.app_in_app.hasScrolled .bookTemplate .tab-header .contentEdit li.active i {
  color: #F79420 !important;
}
.app_in_app.hasScrolled #back-slider {
  top: 8px !important;
  background: #153252 !important;
  color: #FFFFFF;
}
.app_in_app.hasScrolled #back-slider i {
  color: #FFFFFF;
}
.app_in_app.hasScrolled .loading-icon {
  margin-top: 0;
}
.app_in_app .loading-icon {
  background: #F4F5FA;
  margin-top: 56px;
}
.app_in_app .loading-icon .spinner {
  animation: rotateSpinner 2s linear infinite;
  z-index: 2;
  position: absolute;
  top: 40%;
  left: 50%;
  margin: -30px 0 0 -30px;
  width: 50px;
  height: 50px;
}
.app_in_app .loading-icon .spinner .path {
  stroke: #153252;
  stroke-linecap: round;
  animation: spinDash 1.5s ease-in-out infinite;
}
.app_in_app .loading-icon .loader-wrapper {
  display: none !important;
}
.app_in_app #videoModal #youtubeclose {
  padding: 2px 8px;
  margin-bottom: 5px;
  position: absolute;
  top: -20px;
  right: -20px;
  border-radius: 50px;
}
.app_in_app #videoModal #youtubeclose span {
  font-size: 20px;
  line-height: normal;
}
.app_in_app .web-mcq {
  margin-bottom: 50px;
}
.app_in_app .web-mcq .sub-header {
  top: 56px;
}
.app_in_app .web-mcq .result-menu {
  top: 50px;
  box-shadow: 0 2px 4px #0000001A;
}
.app_in_app .web-mcq .que-side-menu {
  z-index: 10001;
}
.app_in_app .web-mcq .que-side-menu .indicator p {
  align-items: center;
}
.app_in_app .web-mcq .overlay-container {
  position: fixed;
  z-index: 10001;
}
.app_in_app .web-mcq #question-block .question-wrapper {
  margin-top: 1.5rem;
}
.app_in_app .web-mcq .onclickScrollsList:last-child {
  padding-bottom: 0;
}
.app_in_app .web-mcq .grid,
.app_in_app .web-mcq .list {
  padding: 0;
}
.app_in_app .web-mcq #collapseOne {
  padding-bottom: 2rem;
}
.app_in_app .web-mcq .answer-summary {
  background: #FFFFFF;
  margin: 3rem 0 2rem;
}
.app_in_app .web-mcq .analysis {
  background: #FFFFFF;
}
.app_in_app .web-mcq .analysis h2 {
  margin-bottom: 15px;
}
.app_in_app .web-mcq #answer-block {
  padding-bottom: 1rem;
  margin-top: 20px;
}
.app_in_app .web-mcq #answer-block .directions {
  margin-bottom: 0;
  padding: 0;
}
.app_in_app .web-mcq #answer-block .button-wrapper #resultsCreateTestBtn,
.app_in_app .web-mcq #answer-block .button-wrapper .viewsolution {
  display: none !important;
}
.app_in_app .web-mcq .ans-tab {
  margin-top: 0;
}
.app_in_app .web-mcq .ans-tab .nav-tabs {
  background: #FFFFFF;
}
.app_in_app .web-mcq .question-box {
  margin-top: 1.5rem;
  background: #FFFFFF;
  padding: 1rem 0 0;
}
.app_in_app .web-mcq .tab-wrappers {
  padding-bottom: 50px !important;
}
.app_in_app #load-more-button {
  text-align: center;
}
.app_in_app #load-more-button .view-more {
  background: #153252 !important;
  box-shadow: none !important;
}
.app_in_app .annotator-touch-viewer .annotator-touch-controls button {
  padding-left: 10px !important;
  padding-right: 10px !important;
  margin-left: 0 !important;
}
.app_in_app .annotator-touch-widget-inner .google-search-btn {
  display: none !important;
}
.app_in_app .annotator-touch-widget-inner .highlight-btn,
.app_in_app .annotator-touch-widget-inner .annotator-add {
  width: 50% !important;
}
.app_in_app .annotator-touch-widget-inner .annotator-add {
  border-right: none !important;
}
#deleteBook,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#PlayAudiOnlyModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal {
  z-index: 10002 !important;
}
.modal-backdrop.show {
  z-index: 10001 !important;
}
.modal-modifier .modal-dialog-modifier {
  align-items: center !important;
  max-width: inherit !important;
  margin: 0.5rem !important;
  height: auto !important;
}
.modal-modifier .modal-content-modifier {
  border-radius: 15px !important;
}
.modal-modifier .close {
  font-size: 25px !important;
  right: 10px !important;
  top: 3px !important;
  color: #212121 !important;
  z-index: 10;
}
@keyframes rotateSpinner {
  100% {
    transform: rotate(360deg);
  }
}
@keyframes spinDash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
