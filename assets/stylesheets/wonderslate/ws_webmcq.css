body.guest-mode #quizQuestionSection .sub-header {
  top: 140px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  body.guest-mode #quizQuestionSection .sub-header {
    top: 120px;
  }
}
body.guest-mode #quizQuestionSection .mt-fixed .que-side-menu {
  top: 230px;
}
body.guest-mode #quizQuestionSection .result-menu {
  top: 130px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  body.guest-mode #quizQuestionSection .result-menu {
    top: 110px !important;
  }
}
body.guest-mode.fixed-navbar #quizQuestionSection .sub-header {
  top: 55px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  body.guest-mode.fixed-navbar #quizQuestionSection .sub-header {
    top: 120px;
  }
}
body.guest-mode.fixed-navbar #quizQuestionSection .mt-fixed .que-side-menu {
  top: 150px;
}
body.guest-mode.fixed-navbar #quizQuestionSection .result-menu {
  top: 55px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  body.guest-mode.fixed-navbar #quizQuestionSection .result-menu {
    top: 110px !important;
  }
}
#learnInformationSection .btn-starts {
  font-weight: 600;
  color: #FFFFFF;
  box-shadow: 0 2px 4px #0000001A;
}
#quizInformationSection .card {
  box-shadow: 0 2px 4px #0000001A;
}
#quizInformationSection .language p:before {
  margin-right: 10px;
}
#quizInformationSection .btn-starts {
  font-weight: 600;
  color: #FFFFFF;
  box-shadow: 0 2px 4px #0000001A;
}
#quizQuestionSection .overlay-container {
  position: fixed;
  background-color: #212121;
  z-index: 9991;
}
#quizQuestionSection .sub-header {
  top: 70px;
  transition: all 0.35s linear;
  box-shadow: 0 1px 7px #e1e1e1;
  border-bottom: 1px solid #000000b3;
  position: sticky;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .sub-header {
    top: 60px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  #quizQuestionSection .sub-header {
    top: 55px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .sub-header .svg-timer {
    padding: 0;
  }
}
#quizQuestionSection .sub-header .svg-timer .tim-wrapper .normal-time {
  position: relative;
}
#quizQuestionSection .sub-header .svg-timer .tim-wrapper .normal-time #pause {
  position: absolute;
  width: 28px;
  height: 28px;
  left: 0;
  border-radius: 50px;
}
#quizQuestionSection .sub-header .svg-timer .tim-wrapper .normal-time #pause::before {
  left: 5px;
  position: relative;
  right: 0;
  top: 0;
}
#quizQuestionSection .sub-header .svg-timer .tim-wrapper .normal-time #pause::after {
  top: 9px;
  left: 10px;
  width: 8px;
}
#quizQuestionSection .sub-header .svg-timer .tim-wrapper .sectiontime-wrapper {
  margin-left: 10px;
}
#quizQuestionSection .sub-header .svg-timer .tim-wrapper .sectiontime-wrapper .timeLeft {
  color: #6C757D;
  font-weight: 500;
  line-height: 1.2;
}
#quizQuestionSection .sub-header .svg-timer .tim-wrapper .sectiontime-wrapper .timer {
  font-weight: 500;
  font-size: 14px;
}
#quizQuestionSection .sub-header .options {
  border-color: #e1e1e1;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .sub-header .options {
    padding: 0;
  }
}
#quizQuestionSection .sub-header .options p span.totalquestion {
  padding-left: 5px;
}
#quizQuestionSection .sub-header .submitWrapper .bg-wsTheme {
  background: #F79420 !important;
  color: #FFFFFF !important;
  box-shadow: 0 2px 4px #0000001A;
  text-transform: uppercase;
  font-weight: normal;
  letter-spacing: 0.5px;
}
#quizQuestionSection .sub-header .total-time-wrapper {
  display: flex;
}
#quizQuestionSection .sub-header .total-time-wrapper .total-test-time {
  width: 60px;
}
#quizQuestionSection .result-menu {
  top: 69px !important;
  transition: all 0.35s linear;
  box-shadow: 0 1px 7px #e1e1e1;
  border-bottom: 1px solid #000000b3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .result-menu {
    top: 50px !important;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  #quizQuestionSection .result-menu {
    top: 55px !important;
  }
}
#quizQuestionSection .result-menu > div {
  height: 50px;
}
#quizQuestionSection .result-menu h2 {
  font-weight: 600;
}
#quizQuestionSection .mt-fixed {
  margin-top: 0;
  padding-top: 0;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  #quizQuestionSection .mt-fixed > .container {
    max-width: 90%;
  }
}
#quizQuestionSection .mt-fixed .que-side-menu {
  position: sticky;
  top: 180px;
  height: 100%;
  margin-top: 30px;
  border-radius: 5px;
  border: none;
  box-shadow: 1px 1px 4px #e1e1e1;
  transition: all 0.35s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1024px) {
  #quizQuestionSection .mt-fixed .que-side-menu {
    position: fixed;
    top: 0 !important;
    margin-top: 0 !important;
    border-radius: 7px 0 0 7px;
    box-shadow: 0px -1px 10px #212121;
    z-index: 9991;
  }
  #quizQuestionSection .mt-fixed .que-side-menu .close-menu {
    width: 24px;
    height: 24px;
    top: 10px;
    right: 10px;
  }
  #quizQuestionSection .mt-fixed .que-side-menu .close-menu span {
    width: 10px;
    height: 10px;
  }
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers {
  position: relative;
  width: 100%;
  top: 1rem;
  height: auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1024px) {
  #quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers {
    top: 2rem;
  }
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .container {
  padding: 0;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .indicator {
  margin-top: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1024px) {
  #quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .indicator {
    justify-content: start !important;
  }
  #quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .indicator .answered {
    padding-right: 50px;
  }
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .indicator p {
  align-items: center;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab {
  padding-bottom: 1rem;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .nav-tabs {
  justify-content: space-around;
  background-color: transparent;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .nav-tabs li {
  width: 48%;
  text-align: center;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .nav-tabs li a {
  background-color: #ededed;
  transition: all 0.2s linear;
  color: #212121;
  border: 0;
  border-bottom: 2px solid transparent;
  opacity: 0.5;
  padding: 5px;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .nav-tabs li a:hover,
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .nav-tabs li a.active {
  opacity: 1;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid #collapseOne {
  padding-bottom: 20px;
  max-height: 400px;
  overflow-y: scroll;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1024px) {
  #quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid #collapseOne {
    max-height: 100%;
    height: calc(80vh - 15px);
  }
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid .que-wrapper {
  justify-content: start;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid .que-wrapper .que-no {
  margin: 0.3rem 0.75rem 0.75rem 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid .que-wrapper .que-no {
    margin: 0.3rem 0.5rem 0.5rem 0;
  }
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid .que-wrapper .que-no a {
  width: 30px;
  height: 30px;
  display: flex !important;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .grid .que-wrapper .que-no + .que-no a:first-child {
  display: none !important;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper {
  max-height: 400px;
  overflow-y: scroll;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1024px) {
  #quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper {
    max-height: 100%;
    height: calc(80vh - 15px);
    padding-bottom: 20px;
  }
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper .onclickScrollsList {
  align-items: start !important;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper .onclickScrollsList .align-self-start {
  padding: 0;
  margin: 0 !important;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper .onclickScrollsList:last-child {
  padding-bottom: 0;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper .que-no-list {
  margin-right: 0.75rem;
  margin-bottom: 0.25rem;
  cursor: pointer;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper .question {
  width: auto;
  cursor: pointer;
}
#quizQuestionSection .mt-fixed .que-side-menu .tab-wrappers .tab-content .list .que-wrapper .question p {
  width: auto;
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper {
  margin-top: 30px;
  border-radius: 5px;
  box-shadow: 1px 1px 7px #d4d4d4;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .mt-fixed #question-block .question-wrapper {
    margin-top: 20px;
  }
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper p {
  color: #212121;
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper .question {
  font-weight: 500;
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper .border-start p {
  border-color: #d4d4d4;
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper .options-string label {
  border-color: #ededed;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .mt-fixed #question-block .question-wrapper .options-string label {
    min-height: 50px;
  }
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper .options-string.active {
  border: none;
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper .options-string.active label {
  border-color: #2EBAC6;
  border-width: 3px;
}
#quizQuestionSection .mt-fixed #question-block .question-wrapper .options-string.active .answer {
  font-weight: 500;
}
#quizQuestionSection .mt-fixed #question-block .btn-starts {
  padding: 7px 25px;
  font-weight: 600;
  text-transform: uppercase;
  margin-top: 10px;
  color: #FFFFFF;
  box-shadow: 0 2px 4px #0000001A;
}
#quizQuestionSection .mt-fixed #answer-block {
  padding-top: 4rem;
}
#quizQuestionSection .mt-fixed #answer-block .practice-score-container {
  border-radius: 5px;
  justify-content: center;
  align-items: center;
}
#quizQuestionSection .mt-fixed #answer-block .answer-summary {
  background-color: #FFFFFF;
  border-radius: 5px;
}
#quizQuestionSection .mt-fixed #answer-block .answer-summary .score-summary .correct-answers,
#quizQuestionSection .mt-fixed #answer-block .answer-summary .score-summary .wrong-answers,
#quizQuestionSection .mt-fixed #answer-block .answer-summary .score-summary .skipped-answers {
  font-family: 'DM Sans', sans-serif !important;
}
#quizQuestionSection .mt-fixed #answer-block .answer-summary .accuracy-summary > div > div > span + p {
  color: #6C757D;
}
#quizQuestionSection .mt-fixed #answer-block .analysis {
  box-shadow: 0 3px 4px #0000001A;
  padding: 1rem 0 0;
  border-radius: 7px;
}
#quizQuestionSection .mt-fixed #answer-block .analysis h2 {
  font-weight: 600;
}
#quizQuestionSection .mt-fixed #answer-block .analysis #graphicAnalysis {
  margin-top: 10px;
}
#quizQuestionSection .mt-fixed #answer-block .analysis #graphicAnalysis svg {
  border-radius: 5px;
}
#quizQuestionSection .mt-fixed #answer-block .analysis #graphicAnalysis svg text {
  font-family: 'DM Sans', sans-serif !important;
}
#quizQuestionSection .mt-fixed #answer-block .suggestions-for-user .suggested-topics {
  color: #444444;
  padding: 0;
}
#quizQuestionSection .mt-fixed #scrolltoTab {
  margin-top: 2rem;
}
#quizQuestionSection .mt-fixed #scrolltoTab .nav-tabs {
  background: #ededed;
}
#quizQuestionSection .mt-fixed .tab-content .validateMsg {
  text-align: center;
  padding: 10px;
  font-family: 'DM Sans', sans-serif !important;
}
#quizQuestionSection .mt-fixed .question-box {
  background-color: #FFFFFF;
  border-radius: 5px;
  box-shadow: 1px 1px 7px #d4d4d4;
  padding-bottom: 0.25rem;
  width: 98%;
  margin: 2rem auto 0;
}
#quizQuestionSection .mt-fixed .question-box > .question {
  border-color: #d4d4d4 !important;
}
#quizQuestionSection .mt-fixed .question-box .ans-neutral {
  border-color: #ededed !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .mt-fixed .question-box .your-answer {
    min-height: 60px;
  }
}
#quizQuestionSection .mt-fixed .question-box .your-answer .wrong-answer-by-user {
  font-size: 14px;
}
#quizQuestionSection .mt-fixed .question-box .your-answer .choice {
  line-height: 1.5;
}
#quizQuestionSection .mt-fixed .directions,
#quizQuestionSection .mt-fixed .passage,
#quizQuestionSection .mt-fixed .correct-answer-explanation {
  font-family: 'DM Sans', sans-serif !important;
  margin-bottom: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #quizQuestionSection .mt-fixed .directions,
  #quizQuestionSection .mt-fixed .passage,
  #quizQuestionSection .mt-fixed .correct-answer-explanation {
    padding: 0;
  }
}
#quizQuestionSection .mt-fixed .showVideobtn {
  outline: 0 !important;
}
#quizQuestionSection .mt-fixed .modal-body .checkmark {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}
#quizQuestionSection .mt-fixed .modal-body .checkmark::after {
  left: 6px;
  top: 2px;
}
#quizQuestionSection .mt-fixed .modal-footer button {
  box-shadow: 0 2px 4px #0000001A;
}
#quizQuestionSection .mt-fixed .modal-footer button.submit {
  background: #F79420 !important;
  color: #FFFFFF !important;
}
#quizQuestionSection .mt-fixed #submit-test .modal-header h1 {
  font-weight: 600;
  margin: 0 !important;
}
#quizQuestionSection .mt-fixed #submit-test .modal-body .circle span {
  margin-top: 0.75rem !important;
  font-weight: 600;
}
#quizQuestionSection .mt-fixed #submit-test .modal-body .circle p {
  font-size: 15px;
}
#quizQuestionSection .mt-fixed #report-que .modal-content {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}
#quizQuestionSection .mt-fixed #report-que .modal-header {
  padding: 1rem;
}
#quizQuestionSection .mt-fixed #report-que .modal-header h4 {
  font-size: 15px;
  color: #212121;
  margin: 0;
}
#quizQuestionSection .mt-fixed #report-que .modal-header h4 i {
  font-size: 20px;
}
#quizQuestionSection .mt-fixed #report-que .modal-header .close {
  font-weight: normal;
  position: absolute;
  top: 10px;
  right: 10px;
  outline: 0;
}
#quizQuestionSection .mt-fixed #report-que .modal-body {
  background: #f8f8f8;
}
#quizQuestionSection .mt-fixed #report-que .modal-body .letusknow {
  border: none;
  width: 100%;
  height: auto;
}
#quizQuestionSection .mt-fixed #report-que .modal-body .letusknow textarea {
  border: 1px solid rgba(68, 68, 68, 0.2);
  width: 100%;
  height: 100px;
  border-radius: 3px;
  padding: 7px;
  font-size: 14px;
  resize: unset;
}
#quizQuestionSection .mt-fixed #report-que .modal-body .letusknow textarea:focus {
  border-color: #212121;
}
#quizQuestionSection .mt-fixed #report-que .modal-footer {
  padding: 1rem;
}
#quizQuestionSection .mt-fixed #report-que .modal-footer button {
  background: #F79420 !important;
  color: #FFFFFF !important;
  padding: 6px 20px;
}
#quizQuestionSection .mt-fixed #videoContent .close {
  outline: 0;
}
.fixed-navbar #quizQuestionSection .mt-fixed .que-side-menu {
  top: 160px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  footer .footer-fluid {
    padding-bottom: 0;
  }
}
