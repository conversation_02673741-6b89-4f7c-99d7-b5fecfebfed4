.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
.moreforyou {
  display: none;
}
@media (min-width: 1200px) {
  .page_main__wrapper .container {
    max-width: 1200px;
  }
}
.top_main_mobile__menu {
  margin-bottom: 2rem;
  margin-top: 1rem;
}
.top_main_mobile__menu a.mdl-button {
  text-transform: inherit;
  background: #FFFFFF;
  box-shadow: 0 2px 5px rgba(128, 128, 128, 0.25);
  border-radius: 10px;
  margin: 0 0.25rem;
  height: 80px;
  display: flex;
  justify-content: start;
  align-items: center;
  color: rgba(68, 68, 68, 0.85);
  font-size: 12px;
  flex-direction: column;
  padding: 20px 15px;
  line-height: 1.2;
  text-align: center;
}
@media (max-width: 575px) {
  .top_main_mobile__menu a.mdl-button {
    height: 90px;
    padding: 18px 15px;
  }
}
.top_main_mobile__menu a.mdl-button:hover {
  text-decoration: none;
  color: #06D781;
}
.top_main_mobile__menu a.mdl-button img {
  width: 18px;
  margin-bottom: 7px;
}
.top_main_mobile__menu a.mdl-button:nth-child(6) img {
  width: 20px;
}
.db-main {
  margin-bottom: 15rem;
}
@media (max-width: 575px), (min-width: 576px) and (max-width: 767px) {
  .db-main {
    margin-bottom: 8rem;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .db-main {
    margin-bottom: 10rem;
  }
}
.db-main .welcome-user h3 span {
  color: #7F28A8;
  opacity: 0.3;
}
@media (max-width: 575px), (min-width: 576px) and (max-width: 767px) {
  .db-main .welcome-user h3 {
    font-size: 1.5rem;
  }
}
.db-main .db-section-title p {
  font-size: 16px;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.85);
}
@media (max-width: 575px), (min-width: 576px) and (max-width: 767px) {
  .db-main .db-section-title p {
    font-size: 14px;
  }
}
.db-main .db-common-info {
  background: #FFFFFF;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}
.db-main .db-common-info .card {
  border: none;
  position: relative;
  border-right: 1px solid #eee;
  border-bottom: 1px solid #eee;
  border-radius: 0;
  justify-content: center;
  min-height: 150px;
}
@media (max-width: 575px), (min-width: 576px) and (max-width: 767px) {
  .db-main .db-common-info .card {
    min-height: 100px;
    padding: 2rem 0.5rem;
  }
  .db-main .db-common-info .card:nth-child(2n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(2n):after,
  .db-main .db-common-info .card:nth-child(2n):before {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .db-main .db-common-info .card {
    padding: 2rem 1rem;
  }
  .db-main .db-common-info .card:nth-child(3n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(3n):after,
  .db-main .db-common-info .card:nth-child(3n):before {
    display: none;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .db-main .db-common-info .card {
    padding: 2rem 1rem;
  }
  .db-main .db-common-info .card:nth-child(4n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(4n):after,
  .db-main .db-common-info .card:nth-child(4n):before {
    display: none;
  }
}
@media (min-width: 1200px) {
  .db-main .db-common-info .card:nth-child(6n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(6n):after,
  .db-main .db-common-info .card:nth-child(6n):before {
    display: none;
  }
}
.db-main .db-common-info .card:before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background: #fff;
  right: -15px;
  top: -20px;
  border-radius: 50px;
  z-index: 1;
}
.db-main .db-common-info .card:after {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background: #fff;
  right: -15px;
  bottom: -20px;
  border-radius: 50px;
  z-index: 1;
}
.db-main .db-common-info .card a img {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  margin: 0 auto;
  padding: 2px;
}
@media (max-width: 575px) {
  .db-main .db-common-info .card a img {
    width: 45px;
    height: 45px;
  }
}
.db-main .db-common-info .card a p {
  margin-top: 10px;
  color: rgba(68, 68, 68, 0.85);
}
@media (max-width: 575px) {
  .db-main .db-common-info .card a p {
    font-size: 12px;
  }
}
.db-main .db-common-info .card a:hover {
  text-decoration: none;
}
.db-main .db-common-info .card a:hover p {
  color: #7F28A8;
}
@media (min-width: 1200px) {
  .db-main .four-cards .card:last-child {
    border-right: none;
  }
  .db-main .four-cards .card:last-child:after,
  .db-main .four-cards .card:last-child:before {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 991px), (min-width: 992px) and (max-width: 1199px), (min-width: 1200px) {
  .db-main .three-cards .card:last-child {
    border-right: none;
  }
}
@media (min-width: 992px) and (max-width: 1199px), (min-width: 1200px) {
  .db-main .three-cards .card:last-child:after,
  .db-main .three-cards .card:last-child:before {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 991px), (min-width: 992px) and (max-width: 1199px), (min-width: 1200px) {
  .db-main .two-cards .card:last-child {
    border-right: none;
  }
  .db-main .two-cards .card:last-child:after,
  .db-main .two-cards .card:last-child:before {
    display: none;
  }
}
.db-main .single-card .card {
  border: none;
}
.db-main .single-card .card:after,
.db-main .single-card .card:before {
  display: none;
}
