.mdl-layout__container {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 9991;
  height: auto;
  width: auto;
  transition: all 0.5s linear;
}
.fixed-navbar .back_to_top {
  transform: translateX(0px);
}
.mdl-js-layout {
  background: #fff;
  box-shadow: 0 2px 30px 2px #0000001a;
}
.navbar .navbar-hamburger {
  height: 26px;
  position: relative;
  width: 35px;
  transition: all 0.3s linear;
  z-index: 10;
}
.navbar .navbar-hamburger a {
  color: #212121;
}
.navbar .navbar-hamburger a i {
  font-size: 26px;
  position: absolute;
}
.navbar .navbar-hamburger .mega_menu__icon {
  opacity: 1;
  visibility: visible;
  transition: all 0.3s linear;
}
.navbar .navbar-hamburger .mega_menu__close {
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s linear;
}
.navbar .navbar-hamburger.menu-actives .mega_menu__icon {
  opacity: 0;
  visibility: hidden;
}
.navbar .navbar-hamburger.menu-actives .mega_menu__close {
  opacity: 1;
  visibility: visible;
}
.navbar .navbar-hamburger .mega_menu__overlay_big.active {
  position: fixed;
  height: 100vh;
  width: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9;
  top: 0;
  left: 0;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .navbar .navbar-brand img {
    width: 120px;
    height: 30px;
  }
}
.navbar .navbar-logo {
  position: absolute;
  left: 50%;
  padding: 0;
  margin: 0;
  transform: translate(-50%, 0);
  z-index: 999;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar .navbar-logo {
    left: 55px;
    transform: unset;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .navbar .navbar-logo {
    left: 4rem;
    transform: unset;
  }
}
.navbar .navbar-logo .navbar-brand {
  font-size: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar .navbar-user {
    position: absolute;
    left: 50px;
  }
}
.navbar .navbar-user .login-signup-menu {
  text-transform: uppercase;
  color: #212121;
  transition: all 0.3s linear;
  font-size: 14px;
  font-weight: 500;
}
@media (max-width: 575.98px) {
  .navbar .navbar-user .login-signup-menu {
    font-size: 12px;
  }
}
.navbar .navbar-user .login-signup-menu:hover {
  color: #f6941e;
}
.navbar .navbar-user .dropdown-user .dropdown-user-link {
  display: flex;
  align-items: center;
}
.navbar .navbar-user .dropdown-user .dropdown-user-link:hover {
  text-decoration: none;
}
.navbar .navbar-user .dropdown-user .dropdown-user-link:after {
  display: none;
}
.navbar .navbar-user .dropdown-user .dropdown-user-link .username {
  font-size: 15px;
  color: #212121;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .navbar .navbar-user .dropdown-user .dropdown-user-link .username {
    font-size: 13px;
  }
}
.navbar .navbar-user .dropdown-user .dropdown-user-link .avatar {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 40px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .navbar .navbar-user .dropdown-user .dropdown-user-link .avatar {
    width: 35px;
    height: 35px;
  }
}
.navbar .navbar-user .dropdown-user .dropdown-user-link .avatar img {
  width: 100%;
  max-width: 100%;
  height: 100%;
}
.navbar .navbar-user .dropdown-user .dropdown-menu {
  box-shadow: 0 5px 75px 2px #0000001A;
  border-color: #E4E5EC;
  left: auto !important;
  border-radius: 10px;
  min-width: 200px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar .navbar-user .dropdown-user .dropdown-menu {
    right: auto;
  }
  .navbar .navbar-user .dropdown-user .dropdown-menu .username {
    line-height: normal;
  }
}
.navbar .navbar-user .dropdown-user .dropdown-menu .dropdown-item {
  padding: 7px 15px;
  width: 100%;
  display: flex;
  align-items: center;
}
.navbar .navbar-user .dropdown-user .dropdown-menu .dropdown-item:hover,
.navbar .navbar-user .dropdown-user .dropdown-menu .dropdown-item:focus,
.navbar .navbar-user .dropdown-user .dropdown-menu .dropdown-item:focus:active {
  background-color: transparent;
  color: #F79420;
}
.navbar .navbar-user .dropdown-user .dropdown-menu .dropdown-item i {
  font-size: 18px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-header {
    height: 60px;
  }
}
.navbar-header .navbar_search_trigger {
  position: relative;
  z-index: 12;
}
.navbar-header .navbar_search_trigger a {
  color: #212121;
}
.navbar-header .navbar_search_trigger a i {
  font-size: 26px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-header #mainNavbar {
    flex-direction: column;
    width: 100%;
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 0 0 7px 7px;
    border-top: 1px solid #f5f5f5;
    z-index: -1;
    padding: 10px 0;
    transform: translateY(-250px);
    transition: all 0.3s linear;
    display: none;
  }
  .navbar-header #mainNavbar .nav-item {
    opacity: 0;
    visibility: hidden;
    transition: all 0.35s linear;
  }
  .navbar-header #mainNavbar.mobile-menu-showing {
    transform: translateY(0);
  }
  .navbar-header #mainNavbar.mobile-menu-showing .nav-item {
    visibility: visible;
    animation: fadeInRight 0.5s ease forwards;
    animation-delay: 0.35s;
  }
  .navbar-header #mainNavbar.mobile-menu-showing .nav-item:nth-child(2) {
    animation-delay: 0.4s;
  }
  .navbar-header #mainNavbar.mobile-menu-showing .nav-item:nth-child(3) {
    animation-delay: 0.45s;
  }
  .navbar-header #mainNavbar.mobile-menu-showing .nav-item:nth-child(4) {
    animation-delay: 0.5s;
  }
  .navbar-header #mainNavbar.mobile-menu-showing .nav-item:nth-child(5) {
    animation-delay: 0.55s;
  }
  .navbar-header #mainNavbar.mobile-menu-showing .nav-item:last-child {
    animation-delay: 0.6s;
  }
}
.navbar-header .navbar-nav .nav-item {
  position: relative;
  padding-right: 0;
  padding-left: 1.5rem;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-header .navbar-nav .nav-item {
    padding-right: 0;
    padding-left: 0.75rem;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .navbar-header .navbar-nav .nav-item {
    padding-right: 0;
    padding-left: 0.25rem;
  }
}
.navbar-header .navbar-nav .nav-item .nav-link {
  padding-right: 0;
  padding-left: 0;
  color: #212121;
  transition: all 0.3s linear;
  font-size: 15px;
  font-weight: 400;
  position: relative;
}
@media (max-width: 575.98px) {
  .navbar-header .navbar-nav .nav-item .nav-link {
    display: inline-block;
    font-size: 13px;
    padding-bottom: 0;
    margin-bottom: 5px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .navbar-header .navbar-nav .nav-item .nav-link {
    font-size: 12px;
    line-height: normal;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .navbar-header .navbar-nav .nav-item .nav-link {
    font-size: 14px;
  }
}
.navbar-header .navbar-nav .nav-item .nav-link::before {
  content: "";
  position: absolute;
  height: 1px;
  background-color: #F79420;
  width: 0;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  transition: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) all;
}
.navbar-header .navbar-nav .nav-item .nav-link:hover {
  color: #F79420;
}
.navbar-header .navbar-nav .nav-item .nav-link:hover::before {
  width: 100%;
  border-bottom-color: #F79420;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-header .navbar-nav .nav-item .nav-link.login-menu-link {
    background-color: #F79420;
    color: #FFFFFF;
    padding: 0.2rem 1.5rem;
    border-radius: 50px;
    margin-bottom: 0;
    box-shadow: 0 2px 4px #DDD;
  }
}
.navbar-header .navbar-nav .nav-item .nav-link.register-menu-btn {
  background-color: #F79420;
  color: #FFFFFF;
  padding: 0.5rem 1rem;
  border-radius: 50px;
}
.navbar-header .navbar-nav .nav-item .nav-link.register-menu-btn:hover::before {
  width: 0;
  border-bottom-color: transparent;
}
.navbar-header .navbar-whatsapp-link .whatsapp-btn {
  border-radius: 50px;
  align-items: center;
  color: #212121;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-header .navbar-whatsapp-link .whatsapp-btn {
    padding-right: 0;
  }
}
.navbar-header .navbar-whatsapp-link .whatsapp-btn i {
  font-size: 22px;
}
.navbar-header .navbar-whatsapp-link .whatsapp-btn:hover::before {
  width: 0;
  border-bottom-color: transparent;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-header.active-publisher .navbar-user {
    left: 100px;
  }
}
.institute-menu {
  display: flex;
}
.institute-menu .institute-menu-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .institute-menu .institute-menu-text {
    max-width: 100px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .institute-menu .institute-menu-text {
    max-width: 80px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-menu .institute-menu-text {
    white-space: pre-line;
    width: auto;
    max-width: unset;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 1.2;
  }
}
.mega_menu__wrapper {
  position: fixed;
  background: #212121;
  top: 55px;
  left: 0;
  right: 0;
  width: 100%;
  height: 0%;
  opacity: 0;
  visibility: hidden;
  overflow: hidden;
  transition: opacity 0.35s, visibility 0.35s, height 0.35s, top 0.5s;
  z-index: 9991;
  border-radius: 0 0 0px 0px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .mega_menu__wrapper {
    top: 0;
  }
}
.mega_menu__wrapper #mega_menu__mainNavbar .mega_menu__links {
  opacity: 0;
  background-color: transparent;
}
.mega_menu__wrapper #mega_menu__mainNavbar .mega_menu__links ul {
  padding-bottom: 10px;
  border-bottom: 1px solid #FFFFFF;
}
.mega_menu__wrapper #mega_menu__mainNavbar .mega_menu__links ul li a {
  color: #FFFFFF;
  padding: 0 0 10px;
  font-size: 16px;
}
.mega_menu__wrapper #mega_menu__mainNavbar .mega_menu__links ul li a:hover {
  color: #F79420;
  text-decoration: underline;
}
.mega_menu__wrapper #mega_menu__mainNavbar .explore_cat_btn {
  display: flex;
  align-items: center;
  color: #F79420;
  font-size: 15px;
}
.mega_menu__wrapper #mega_menu__mainNavbar .explore_cat_btn i {
  transform: rotate(0);
  transition: all 0.2s linear;
}
.mega_menu__wrapper #mega_menu__mainNavbar.explored .explore_cat_btn i {
  transform: rotate(-180deg);
}
.mega_menu__wrapper #topMenuItems {
  opacity: 1;
  visibility: visible;
  transition: all 0.2s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .mega_menu__wrapper #topMenuItems {
    padding-top: 15px;
  }
}
.mega_menu__wrapper #topMenuItems.hidden_categories {
  opacity: 0;
  visibility: hidden;
}
.mega_menu__wrapper #topMenuItems .mega_menu__links {
  opacity: 0;
  background-color: transparent;
}
.mega_menu__wrapper #topMenuItems .mega_menu__links h5 {
  color: #FFFFFF;
  position: relative;
  display: inline-block;
  padding-bottom: 8px;
  margin-bottom: 12px;
}
.mega_menu__wrapper #topMenuItems .mega_menu__links h5:after {
  position: absolute;
  content: '';
  left: 0;
  background-color: #FFFFFF;
  width: 100%;
  height: 1.5px;
  bottom: 0;
}
.mega_menu__wrapper #topMenuItems .mega_menu__links ul li a {
  color: #FFFFFF;
  line-height: normal;
  padding-bottom: 10px;
  display: block;
  font-size: 16px;
}
.mega_menu__wrapper #topMenuItems .mega_menu__links ul li a:hover {
  color: #F79420;
  text-decoration: underline;
}
.mega_menu__wrapper.menu-showing {
  opacity: 1;
  visibility: visible;
  padding: 50px;
  height: 400px;
  overflow-y: scroll;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .mega_menu__wrapper.menu-showing {
    height: 300px;
    padding: 30px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .mega_menu__wrapper.menu-showing {
    padding: 50px 30px;
    height: 100vh;
    z-index: 9999;
    top: 0;
  }
}
.mega_menu__wrapper.menu-showing #mega_menu__mainNavbar .mega_menu__links {
  animation: fadeInRight 0.5s ease forwards;
  animation-delay: 0.35s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links {
  animation: fadeInRight 0.5s ease forwards;
  animation-delay: 0.35s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(2) {
  animation-delay: 0.4s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(3) {
  animation-delay: 0.45s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(4) {
  animation-delay: 0.5s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(5) {
  animation-delay: 0.55s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(6) {
  animation-delay: 0.6s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(7) {
  animation-delay: 0.65s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(8) {
  animation-delay: 0.7s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(9) {
  animation-delay: 0.75s;
}
.mega_menu__wrapper.menu-showing #topMenuItems .mega_menu__links:nth-child(10) {
  animation-delay: 0.8s;
}
.mega_menu__wrapper .mega_menu__close_mob {
  position: fixed;
  top: 15px;
  right: 15px;
  width: 60px;
  font-weight: 500;
  letter-spacing: 1px;
  z-index: 10;
  border-radius: 3px;
  text-transform: unset;
  height: auto;
}
.mega_menu__overlay_bg.active {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #000000b3;
  z-index: 999;
}
.mobile_navigation__btn {
  color: #212121;
}
.mobile_navigation__btn:hover {
  color: #212121;
  text-decoration: none;
}
.mobile_navigation__btn .menu_line {
  transition: all 0.35s;
  position: relative;
  width: 28px;
  height: 28px;
}
.mobile_navigation__btn .menu_line .first_line,
.mobile_navigation__btn .menu_line .second_line {
  width: 18px;
  height: 2px;
  position: absolute;
  background: #212121;
  right: 0;
  margin: auto;
  left: 0;
  border-radius: 1px;
  transition: all 0.35s cubic-bezier(0.6, 0, 0.3, 0.9);
}
.mobile_navigation__btn .menu_line .first_line {
  top: calc(50% - 1px + -2px);
}
.mobile_navigation__btn .menu_line .second_line {
  top: calc(50% - 1px + 2px);
}
.mobile_navigation__btn.active .menu_line {
  transform: scale(1.11) rotate(180deg);
}
.mobile_navigation__btn.active .menu_line .first_line,
.mobile_navigation__btn.active .menu_line .second_line {
  top: calc(50% - 1px);
  width: 16px;
}
.mobile_navigation__btn.active .menu_line .first_line {
  transform: rotate(45deg);
}
.mobile_navigation__btn.active .menu_line .second_line {
  transform: rotate(-45deg);
}
html body {
  background-color: #F4F5FA;
}
html body.arihant,
html body.whitelabel-site {
  background-color: #FFFFFF;
}
.page-main-wrapper {
  min-height: 400px;
}
.user_profile {
  background-color: transparent !important;
}
.back_to_top {
  position: fixed;
  bottom: 25px;
  right: 25px;
  z-index: 999;
  transform: translateX(150px);
  transition: all 0.5s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .back_to_top {
    bottom: 75px;
    right: 10px;
  }
}
.back_to_top #goTopBtn {
  width: 45px;
  height: 45px;
  border-radius: 50px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 2px 10px 0 rgba(0, 0, 0, 0.1);
  background-color: #000000b3 !important;
  color: #FFFFFF;
  min-width: auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .back_to_top #goTopBtn {
    width: 40px;
    height: 40px;
  }
}
@keyframes fadeInRight {
  0% {
    opacity: 0;
    left: 20%;
  }
  100% {
    opacity: 1;
    left: 0;
  }
}
ul.typeahead {
  right: 0;
  box-shadow: 0 0 5px 1px #0000001A;
  z-index: 100;
}
ul.typeahead li a {
  white-space: pre-wrap;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  ul.typeahead li a {
    padding: 0.25rem 1rem;
  }
}
ul.typeahead li a:active {
  background-color: rgba(64, 64, 64, 0.70196078);
}
.ebooks .global-search.searching-book-store form .form-control {
  height: 48px;
  border-radius: 7px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .global-search.searching-book-store form .form-control {
    height: 42px;
  }
}
.ebooks .global-search input[type="text"] {
  position: relative;
  z-index: 10;
  padding-left: 20px;
  padding-right: 55px;
  height: 33.5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .global-search input[type="text"] {
    padding-left: 10px;
    padding-right: 45px;
  }
}
.ebooks .global-search button {
  position: relative;
  z-index: 10;
  width: 48px;
  height: 48px;
  margin-left: -48px;
  color: #000000b3 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .global-search button {
    height: 42px;
  }
}
.ebooks .global-search button .material-icons {
  font-size: 24px;
  line-height: 1.5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .global-search button .material-icons {
    line-height: normal;
  }
}
.ebooks .books-list .badge-overlay {
  z-index: unset;
}
.navbar-search {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 11;
}
.navbar-search .global-search .form-inline {
  margin: 0;
}
.navbar-search .global-search .form-inline i {
  position: relative;
  top: 25px;
  z-index: 12;
  left: 25px;
  color: #949494;
  font-size: 26px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-search .global-search .form-inline i {
    left: 10px;
  }
}
.navbar-search .global-search #search-book-header {
  padding-left: 55px;
  padding-right: 55px;
  height: 50px;
  font-size: 16px;
  top: -12px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-search .global-search #search-book-header {
    padding-left: 40px;
    font-size: 15px;
    padding-right: 40px;
  }
}
.navbar-search .global-search ul.typeahead {
  width: 95%;
  margin: 7px auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .navbar-search .global-search ul.typeahead {
    margin: 0 auto;
  }
}
.navbar-hamburger,
.navbar-hamburger .mega_menu__icon,
.mobile-back-button,
.navbar-brand,
.pomodoro_section,
.notify_icon,
.navbar-nav,
.navbar_cart,
.navbar-user,
.mobile_menu,
.navbar-whatsapp-link {
  transition: all 0.2s linear;
}
body.showing-search-form .navbar-hamburger,
body.showing-search-form .navbar-hamburger .mega_menu__icon,
body.showing-search-form .mobile-back-button,
body.showing-search-form .navbar-brand,
body.showing-search-form .pomodoro_section,
body.showing-search-form .notify_icon,
body.showing-search-form .navbar-nav,
body.showing-search-form .navbar_cart,
body.showing-search-form .navbar-user,
body.showing-search-form .mobile_menu,
body.showing-search-form .navbar-whatsapp-link {
  opacity: 0;
  visibility: hidden;
}
body.showing-search-form .navbar-header .navbar_search_trigger {
  position: absolute;
  right: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  body.showing-search-form .navbar-header .navbar_search_trigger {
    right: 10px;
  }
}
.navbar_cart {
  position: relative;
  padding: 0 0.5rem;
}
.navbar_cart .mobile_cart_icon {
  color: #212121;
  display: block;
}
.navbar_cart .mobile_cart_icon .cart_count {
  position: absolute;
  top: -10px;
  left: 0;
  right: -10px;
  width: 17px;
  height: 18px;
  text-align: center;
  background: #000000b3;
  color: #FFFFFF;
  border-radius: 50px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  line-height: normal;
  font-weight: 500;
}
.books-list .content-wrapper {
  position: relative;
}
.books-list .content-wrapper .add_to_cart_btn {
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000b3;
  margin: auto;
  font-size: 12px;
  border: 1px solid #000000b3;
  position: relative;
  padding: 3px;
  bottom: -3px;
}
.books-list .content-wrapper .add_to_cart_btn:hover,
.books-list .content-wrapper .add_to_cart_btn:focus,
.books-list .content-wrapper .add_to_cart_btn:active,
.books-list .content-wrapper .add_to_cart_btn:active:focus {
  color: #FFFFFF;
  background: #000000b3;
}
.books-list .content-wrapper .add_to_cart_btn:hover img,
.books-list .content-wrapper .add_to_cart_btn:focus img,
.books-list .content-wrapper .add_to_cart_btn:active img,
.books-list .content-wrapper .add_to_cart_btn:active:focus img {
  filter: contrast(4) invert(1);
  -webkit-filter: contrast(4) invert(1);
}
.books-list .content-wrapper .add_to_cart_btn i {
  font-size: 18px;
}
.books-list .content-wrapper .add_to_cart_btn img {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
#addedIconAnimation {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 20px auto;
}
#addedIconAnimation:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #b3eecc;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
#addedIconAnimation:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #27AE60;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}
.checkmark {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: #FFFFFF;
  stroke-miterlimit: 10;
  margin: 0;
  box-shadow: inset 0 0 0 #27AE60;
  animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
  position: absolute;
  z-index: 2;
  right: 0;
  left: 0;
}
.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}
.f-modal-alert .f-modal-icon {
  border-radius: 50%;
  border: 3px solid transparent;
  box-sizing: content-box;
  height: 56px;
  margin: 20px auto;
  padding: 0;
  position: relative;
  width: 56px;
}
.f-modal-alert .f-modal-icon.f-modal-warning {
  border-color: #F8BB86;
}
.f-modal-alert .f-modal-icon.f-modal-warning:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #ffd9b9;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
.f-modal-alert .f-modal-icon.f-modal-warning:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.f-modal-alert .f-modal-icon.f-modal-warning .f-modal-body {
  background-color: #F8BB86;
  border-radius: 2px;
  height: 26px;
  left: 50%;
  margin-left: -1px;
  position: absolute;
  top: 10px;
  width: 4px;
  z-index: 2;
}
.f-modal-alert .f-modal-icon.f-modal-warning .f-modal-dot {
  background-color: #F8BB86;
  border-radius: 50%;
  bottom: 10px;
  height: 6px;
  left: 50%;
  margin-left: -2px;
  position: absolute;
  width: 6px;
  z-index: 2;
}
.f-modal-alert .f-modal-icon + .f-modal-icon {
  margin-top: 50px;
}
.scaleAnimation {
  -webkit-animation: scaleAnimation 1s infinite alternate;
  animation: scaleAnimation 1s infinite alternate;
}
.pulseAnimationIns {
  -webkit-animation: pulseAnimationIns 0.75s infinite alternate;
  animation: pulseAnimationIns 0.75s infinite alternate;
}
@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
@keyframes fill {
  100% {
    box-shadow: inset 0 0 0 30px #27AE60;
  }
}
@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes pulseWarning {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulseAnimationIns {
  0% {
    background-color: #F8D486;
  }
  100% {
    background-color: #F8BB86;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .arihant .hide_mobile,
  .whitelabel-site .hide_mobile {
    display: none;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .arihant .menu-wrp-all-users-com,
  .whitelabel-site .menu-wrp-all-users-com {
    padding-right: 7px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .arihant .menu-wrp-all-users-com ul li a img,
  .whitelabel-site .menu-wrp-all-users-com ul li a img {
    margin-bottom: 0;
  }
}
.arihant .navbar_cart,
.whitelabel-site .navbar_cart {
  padding: 0;
}
.arihant .navbar_cart .mobile_cart_icon,
.whitelabel-site .navbar_cart .mobile_cart_icon {
  color: #FFFFFF;
  position: relative;
}
.arihant .navbar_cart .cart_count,
.whitelabel-site .navbar_cart .cart_count {
  left: 18px;
  top: 2px;
  right: auto;
  font-weight: normal;
  font-size: 12px;
  letter-spacing: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .arihant .navbar_cart .cart_count,
  .whitelabel-site .navbar_cart .cart_count {
    left: 15px;
    top: 5px;
  }
}
.arihant ul.typeahead,
.whitelabel-site ul.typeahead {
  margin: 0;
}
.arihant .ebooks,
.whitelabel-site .ebooks {
  background-color: #F4F5FA;
}
.arihant .ebooks .ebooks_filter,
.whitelabel-site .ebooks .ebooks_filter {
  padding: 0;
  box-shadow: none;
}
.book-cart-modal .book_variants .card-body {
  min-height: 105px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .book-cart-modal .book_variants .card-body {
    min-height: 120px;
  }
}
.book-cart-modal .book_variants .test-series-variant {
  background-color: #fde7cc;
  border-color: #F79420;
}
.book-cart-modal .book_variants .test-series-variant span.badge {
  display: initial;
  border-radius: 50px;
  font-size: 11px;
  padding: 7px 10px;
  font-weight: normal;
  background: var(--info);
  position: absolute;
  right: 0;
  left: 0;
  top: -14px;
  height: auto;
  width: 80px;
  margin: 0 auto;
  text-align: center;
  box-shadow: 0 2px 4px #F79420;
}
.book-cart-modal .book_variants h6 {
  padding-top: 5px;
}
.book-cart-modal .book_variants .list_price {
  font-size: 16px;
  font-weight: 500;
  color: #FF4B33;
}
.book-cart-modal .book_variants .list_price del {
  padding-right: 3px;
}
.book-cart-modal .book_variants .offer_price {
  font-size: 18px;
  font-weight: 500;
}
.book-cart-modal .book_variants .book_validity {
  line-height: normal;
}
#AddToCartModal .modal-content-modifier {
  background-color: #F4F5FA;
}
.wonderslate_main .book-cart-modal .book_variants .ebook-variant a.btn-outline-primary-modifier {
  border-color: #F79420 !important;
  color: #F79420 !important;
}
.wonderslate_main .ebooks .jumbotron h1 {
  font-size: 2rem;
  margin: 1rem 0 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .jumbotron h1 {
    font-size: 1.5rem;
  }
}
.rupee-symbol {
  font-family: 'Rubik', sans-serif !important;
}
.headerCategoriesMenu {
  position: relative;
  transition: all 0.3s ease-in;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .headerCategoriesMenu {
    display: none;
  }
}
.headerCategoriesMenu .header__categories {
  background: #1c1b1b;
  padding: 10px;
}
.headerCategoriesMenu .header__categories-list {
  display: flex;
  align-items: center;
  justify-content: center;
  list-style: none;
  padding-left: 0;
  padding-bottom: 0;
  margin-bottom: 0;
}
.headerCategoriesMenu .header__categories-list__item {
  padding-right: 12px;
  min-width: 120px;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .headerCategoriesMenu .header__categories-list__item {
    min-width: auto;
  }
}
.headerCategoriesMenu .header__categories-list__item p {
  color: #FFFFFF;
}
.headerCategoriesMenu .header__categories-list__item:not(:first-child) {
  padding-left: 12px;
  text-align: center;
}
.headerCategoriesMenu .header__categories-list__item:first-child {
  text-align: end;
}
.headerCategoriesMenu .header__categories-list__item:last-child {
  text-align: start;
}
.headerCategoriesMenu .header__categories-list__item:not(:last-child) {
  border-right: 1px solid #FFFFFF;
}
.headerCategoriesMenu .header__categories-list__item a {
  color: #FFFFFF;
  font-weight: 400;
}
@media (max-width: 575.98px) {
  .headerCategoriesMenu .header__categories-list__item a {
    font-size: 13px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .headerCategoriesMenu .header__categories-list__item a {
    font-size: 12px;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .headerCategoriesMenu .header__categories-list__item a {
    font-size: 14px;
    font-weight: 400;
  }
}
.headerCategoriesMenu .header__submenus {
  min-width: 500px;
  max-width: 920px;
  position: absolute;
  background: #fff;
  transition: all 0.4s ease-in;
  left: 50%;
  transform: translate(-50%, 0%);
  opacity: 0;
  overflow: scroll;
  border: 1px solid rgba(0, 0, 0, 0.15);
  z-index: 9999;
}
.headerCategoriesMenu .header__submenus .subMenuTitleText {
  position: sticky;
  top: 0;
  background: #fff;
  z-index: 9999;
  padding: 14px 0 0 0;
  color: #212121;
}
.headerCategoriesMenu .header__submenus .submenuLists {
  display: flex;
  width: 100%;
  padding: 0 12px 12px 0;
  min-height: auto;
  max-height: 500px;
  overflow: scroll;
}
.headerCategoriesMenu .header__submenus .submenuLists ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 0;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  justify-content: center;
  width: 100%;
}
.headerCategoriesMenu .header__submenus .submenuLists ul li {
  padding: 0 12px;
  margin-top: 2px;
  line-height: unset !important;
}
.headerCategoriesMenu .header__submenus .submenuLists ul li a {
  color: #212121;
  text-decoration: none !important;
  font-size: 13px;
}
.headerCategoriesMenu .header__submenus .submenuLists .listScrollArrow {
  position: fixed;
  right: 12px;
  top: 90%;
  width: 18px;
  height: 25px;
  border: 2px solid #bbb;
  border-radius: 12px;
  justify-content: center;
  align-items: flex-start;
  display: none;
  padding: 3px 0;
}
.headerCategoriesMenu .header__submenus .submenuLists .listScrollArrowBall {
  width: 4px;
  height: 4px;
  background-color: #bbb;
  border-radius: 50%;
  transition: all 0.3s ease;
  animation: scrollBall 2.4s ease infinite;
}
#accordion .card {
  border: none !important;
}
#accordion .card-header {
  background: #313030;
}
#accordion .card-header .btn-link:after {
  content: "\f107";
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  float: right;
  color: #FFFFFF;
}
#accordion .card-header .btn-link.collapsed:after {
  content: "\f106";
  color: #FFFFFF;
}
#accordion .card a {
  color: #FFFFFF;
}
#accordion .card-body {
  max-height: 293px;
  overflow: scroll;
  background: #2f2f2ff0;
}
@keyframes scrollBall {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(10px);
  }
  100% {
    transform: translateY(0px);
  }
}
.headerBackdrop {
  z-index: 1040 !important;
}
#categoryMenuBackdrop.modal-backdrop.show {
  opacity: 0.5 !important;
  background: #000 !important;
}
.minibanner {
  min-height: 100px;
  background: #5EC7D7;
}
.minibannerText {
  color: white;
}
