.chapter-wrapper {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
  position: fixed;
  background: none;
  border: none;
  width: 290px;
  height: calc(100vh - 190px);
  overflow-y: auto;
  display: flex;
  justify-content: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .chapter-wrapper {
    position: static;
    width: 100%;
    height: 100%;
    background: #FFFFFF;
    margin-top: 0 !important;
  }
}
.chapter-wrapper ol {
  padding: 0;
  margin: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .chapter-wrapper ol {
    width: 100%;
    padding: 0 10px;
  }
}
.chapter-wrapper li {
  width: 246px;
  min-height: 50px;
  box-shadow: 0 0 10px #0000001A;
  list-style-position: inside;
  list-style-type: none;
  margin-bottom: 1rem;
  border-radius: 5px;
  display: flex;
  align-items: center;
}
.chapter-wrapper li:last-child {
  margin-bottom: 8rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .chapter-wrapper li {
    width: 100%;
  }
  .chapter-wrapper li:last-child {
    margin-bottom: 6rem;
  }
}
.chapter-wrapper li::before {
  counter-increment: section;
  content: " " counter(section) ". ";
  margin-left: 10px;
  color: #000000b3;
  font-size: 12px;
}
.chapter-wrapper li.orangeText {
  background: #000000b3;
  color: #FFFFFF;
  font-weight: 500;
}
.chapter-wrapper li.orangeText::before {
  color: #FFFFFF;
}
.chapter-wrapper li.orangeText a {
  color: #FFFFFF;
  font-weight: 500;
}
.chapter-wrapper li a {
  text-decoration: none;
  color: #000000b3;
  font-size: 12px;
  display: flex;
  padding: 1px 5px;
  align-items: center;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.chapter-wrapper::-webkit-scrollbar {
  display: none;
}
.notes {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  background: #FFFFFF;
  border: 1px solid #000000b3;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 4px;
  color: #000000b3;
  font-size: 10px;
  width: 117px;
  height: 36px;
}
.notes:focus {
  outline: 0;
}
.notes i {
  color: #848484;
  font-size: 16px;
  margin-right: 5px;
}
.revise {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  background: #FFFFFF;
  border: 1px solid #848484;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 44px;
  color: #848484;
  font-size: 10px;
  height: 36px;
  width: 117px;
}
.revise:focus {
  outline: 0;
}
.revise i {
  color: #848484;
  font-size: 16px;
  margin-right: 5px;
}
.read-book-chapters-wrapper {
  counter-reset: section;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .read-book-chapters-wrapper {
    padding: 10px;
  }
}
.all-container .container-wrapper {
  width: 600px;
  margin-top: 1rem !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .all-container .container-wrapper {
    width: 100%;
    margin: 0 auto;
    border: none;
    margin-top: 1rem;
    border-radius: 4px;
  }
}
.all-container .container-wrapper .media .addtodo-wrapper button {
  width: 50px;
  height: 114px;
  color: #000000b3;
  font-size: 14px;
  padding: 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background: rgba(239, 239, 239, 0.35);
}
.all-container .container-wrapper .media .addtodo-wrapper button.btn-success {
  background: #000000b3;
  border: none;
}
.all-container .container-wrapper .media .addtodo-wrapper button.btn-success span {
  color: #FFFFFF;
}
.all-container .container-wrapper .media .addtodo-wrapper button.btn-success span:last-child {
  color: #FFFFFF;
}
.all-container .container-wrapper .media .addtodo-wrapper button.btn-success i {
  color: #FFFFFF;
}
.all-container .container-wrapper .media .addtodo-wrapper button span {
  color: #000000b3;
  font-size: 20px;
  display: block;
}
.all-container .container-wrapper .media .addtodo-wrapper button span:last-child {
  font-size: 10px;
  white-space: normal;
  font-style: italic;
  margin-top: 0;
}
.all-container #allAddButton .dropdown {
  position: static;
}
.all-menu {
  position: fixed;
}
.all-menu a {
  background: #000000b3;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 50px;
  width: 75px;
  height: 75px;
  color: #FFFFFF;
  text-align: center;
  display: block;
  margin-top: 1rem;
}
.all-menu a:hover {
  text-decoration: none;
}
.all-menu a:focus {
  text-decoration: none;
  background: #000000b3;
}
.all-menu a i {
  font-size: 18px;
  margin-top: 20px;
}
.all-menu a span {
  font-size: 10px;
  display: block;
  text-align: center;
}
.read-content {
  padding-bottom: 4rem;
}
.all-container .container-wrapper div > .media {
  flex-direction: column;
  width: 100%;
  padding: 0;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
}
.all-container .container-wrapper div > .media p {
  line-height: 1;
}
.all-container .container-wrapper div > .media .title {
  padding: 0 10px;
  color: #444444;
  font-size: 12px;
}
.all-container .container-wrapper div > .media .readnow {
  display: flex;
  align-items: center;
  padding: 0 1rem;
  width: 100%;
}
.all-container .container-wrapper div > .media .readnow:hover {
  text-decoration: none;
}
.all-container .container-wrapper div > .media .box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 52px;
  border-radius: 5px;
  margin: 0.5rem 0;
}
.all-container .container-wrapper div > .media .box p {
  font-size: 9px;
  margin: 0;
  text-align: center;
  top: 0;
  color: #FFFFFF;
  font-weight: 700;
}
.all-container .container-wrapper div > .media .box i {
  width: 32px;
  height: 24px;
  margin: 0 auto;
}
.all-container .container-wrapper div > .media .box.blue {
  background: radial-gradient(109.09% 109.09% at 0% 0%, #2D9CDB 0%, #2F80ED 100%);
}
.all-container .container-wrapper div > .media .box.blue i {
  background: url("../../images/ws/pdf.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.green {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, rgba(85, 115, 0, 0.8) 76.95%);
}
.all-container .container-wrapper div > .media .box.green i {
  background: url("../../images/ws/link.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.yellow {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F2C74C 0%, #F2994A 46.86%);
}
.all-container .container-wrapper div > .media .box.yellow i {
  background: url("../../images/ws/flashcard.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.pink {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F24CE1 0%, rgba(183, 7, 206, 0.9) 76.95%);
}
.all-container .container-wrapper div > .media .box.pink i {
  background: url("../../images/ws/video.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.lightgreen {
  background: radial-gradient(100% 100% at 0% 0%, #49E859 0%, #007D0C 100%);
}
.all-container .container-wrapper div > .media .box.lightgreen i {
  background: url("../../images/ws/notes.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.violet {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #BB6BD9 0%, #7B24CD 99.48%);
}
.all-container .container-wrapper div > .media .box.violet i {
  background: url("../../images/ws/mcq1.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.darkgreen {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #4CF2E8 5.08%, #006963 70.31%);
}
.all-container .container-wrapper div > .media .box.darkgreen i {
  background: url("../../images/ws/mindmap.svg") center center no-repeat;
}
.chapterList {
  display: flex;
  justify-content: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .chapterList {
    display: block;
  }
}
.chapterList .tab-content {
  width: 100%;
}
.generateTest {
  background: #000000b3;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  color: #FFFFFF;
  font-size: 12px;
  display: flex;
  align-items: center;
  padding: 8px;
}
.generateTest i {
  color: #FFFFFF;
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .generateTest {
    bottom: 85px;
    position: fixed;
    width: 90%;
    z-index: 99;
    display: flex;
    justify-content: center;
  }
}
.generateTest:hover {
  text-decoration: none;
  color: #FFFFFF;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .actionMenu {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    background: #000000b3;
    box-shadow: 0 0 4px #00000040;
    border-radius: 20px 20px 0 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .actionMenu .all-menu {
    position: static;
    display: flex;
    width: 100%;
    justify-content: space-between;
  }
  .actionMenu .all-menu a {
    border-radius: 0;
    margin-top: 0;
    display: flex;
    align-items: center;
    background-image: url('../../images/ws/menu-line.png');
    background-position: right;
    background-repeat: no-repeat;
    padding-right: 10px;
  }
  .actionMenu .all-menu a:last-child {
    background: none;
  }
  .actionMenu .all-menu a span {
    font-size: 11px;
  }
  .actionMenu .all-menu a i {
    margin-top: 0;
    font-size: 18px;
  }
}
.chapter-head {
  font-size: 20px;
  background: #000000b3;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
#allAddButton {
  margin-top: 2rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #allAddButton {
    margin-top: 0;
  }
}
.all-container .container-wrapper {
  min-height: auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .all-container .container-wrapper {
    margin: 0 auto;
  }
}
.all-container .container-wrapper .media {
  margin-bottom: 0;
  padding: 0 1rem;
}
select {
  color: #000000b3 !important;
}
select.type {
  width: 200px;
  border: 1px solid #000000b3;
}
select.sortby {
  width: 100px;
  border: 1px solid #000000b3;
}
.modal {
  background: #000000B3 !important;
  z-index: 9992;
}
.web-url .modal .modal-header,
.video-url .modal .modal-header {
  display: none;
}
.web-url .modal .modal-content,
.video-url .modal .modal-content {
  width: 100%;
}
.web-url .modal .modal-body form,
.video-url .modal .modal-body form {
  margin-top: 0;
}
.web-url .modal .modal-body input,
.video-url .modal .modal-body input {
  width: 100%;
  border-bottom: 1px solid #848484;
}
.web-url .modal .modal-footer button,
.video-url .modal .modal-footer button {
  background: transparent;
}
.web-url .modal .modal-footer button.cancel,
.video-url .modal .modal-footer button.cancel {
  color: #848484;
  text-transform: capitalize;
}
.web-url .modal .modal-footer button.saveLink,
.video-url .modal .modal-footer button.saveLink {
  color: #000000b3;
  font-size: 14px;
  font-weight: 700;
}
#htmlContent {
  min-width: 768px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #htmlContent {
    min-width: 100%;
  }
}
.read-back-btn {
  width: 100px;
  border-radius: 18px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  z-index: 1;
  top: 0;
  color: #848484;
  font-size: 14px !important;
  background: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .read-back-btn {
    justify-content: flex-start;
  }
}
.read-back-btn:focus {
  outline: none;
}
.read-back-btn i {
  color: #848484;
  font-size: 34px;
  background: #000000b3;
}
.read-back-btn:hover {
  text-decoration: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .read-back-btn {
    left: 0;
  }
}
.shine {
  background: #FFFFFF;
  background-image: #FFFFFF;
  background-repeat: no-repeat;
  background-size: 800px 104px;
  display: inline-block;
  position: relative;
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholderShimmer;
  -webkit-animation-timing-function: linear;
}
box {
  height: 104px;
  width: 100%;
}
div.line-wrapper {
  display: inline-flex;
  flex-direction: column;
  margin-left: 25px;
  margin-top: 15px;
  vertical-align: top;
}
lines {
  height: 10px;
  margin-top: 10px;
  width: 100%;
}
photo {
  display: block!important;
  width: 325px;
  height: 30px;
  margin-top: 15px;
}
.mt-20 {
  margin-top: 10px;
}
@-webkit-keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }
  100% {
    background-position: 468px 0;
  }
}
.flexAlign {
  display: flex;
  justify-content: center;
}
.line-seperator {
  background: url('../../images/ws/chapter-line.png') center center no-repeat;
  background-size: contain;
  position: fixed;
  width: 1px;
  height: 500px;
  margin-top: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .line-seperator {
    display: none;
  }
}
#htmlreadingcontent {
  margin: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
}
.pdfbutton {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  padding: 1.2rem 4rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .pdfbutton {
    padding: 0 20px;
  }
}
.pdfbutton button {
  background: #000000b3;
  color: #FFFFFF;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  border-radius: 4px;
  padding: 0 10px;
}
.subMenu.addFix {
  position: sticky;
  top: 65px;
  background: #FFFFFF;
  z-index: 99;
  padding-top: 10px;
  border-top: 1px solid #ececec;
  -webkit-box-shadow: 0 8px 6px -6px #0000001A;
  -moz-box-shadow: 0 8px 6px -6px #0000001A;
  box-shadow: 0 8px 6px -6px #0000001A;
  padding-bottom: 10px;
  transition: all 0.5s;
}
.bookTemplate.reFix #book-read-material {
  margin-top: 2rem;
}
.bookTemplate.reFix .read-content .export-notes {
  top: 65px;
}
.bookTemplate .content-wrapper .price-wrapper {
  padding: 0;
}
.select_box {
  width: 170px;
  overflow: hidden;
  border: none;
  position: relative;
  border-radius: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .select_box {
    margin-right: 10px;
    width: 100%;
  }
}
.select_box:after {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #000000b3;
  position: absolute;
  top: 16px;
  right: 5px;
  content: "";
  z-index: 999;
}
.select_box select {
  width: 183px;
  border: 0;
  position: relative;
  z-index: 99;
  background: none;
  font-style: italic;
  font-size: 12px;
  background: #444444;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .select_box select {
    width: 99%;
  }
}
.sharethis-inline-share-buttons {
  position: sticky;
  bottom: 0;
  width: 100%;
  background: #FFFFFF;
  padding: 10px;
  -webkit-box-shadow: 0 -4px 3px #0000001A;
  -moz-box-shadow: 0 -4px 3px #0000001A;
  box-shadow: 0 -4px 3px #0000001A;
  margin-left: 2rem;
  z-index: 1 !important;
}
.bookTemplate .read-content .export-notes {
  top: 80px;
}
#main-wrapper {
  max-width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #main-wrapper .subMenu {
    background: #FFFFFF;
    box-shadow: 0 0 10px #0000001A;
    border-radius: 0 0 10px 10px;
    padding-bottom: 1.5rem;
  }
}
#main-wrapper .subMenu .nav.nav-tabs {
  width: 246px;
  margin: 0 22px;
  border: none;
  min-height: 50px;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  display: flex;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #main-wrapper .subMenu .nav.nav-tabs {
    width: auto;
    margin: 0;
  }
}
#main-wrapper .subMenu .nav.nav-tabs li {
  border: none;
  width: 50%;
}
#main-wrapper .subMenu .nav.nav-tabs li:first-child a {
  border-right: 1px solid #ededed;
  border-radius: 0 ;
}
#main-wrapper .subMenu .nav.nav-tabs li a {
  border: none;
  text-align: center;
}
#main-wrapper .subMenu .nav.nav-tabs li a.active {
  font-weight: 700;
  color: #000000b3 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
}
.sharethis-inline-share-buttons {
  display: none !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .tab-content .container {
    padding: 0;
  }
  .tab-content .container #htmlreadingcontent {
    padding: 0 !important;
    margin: 0;
  }
  .tab-content .setname-wrapper p {
    display: none;
  }
}
.annotator-adder button {
  white-space: nowrap;
}
.annotator-adder button:last-child {
  border-left: none !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .subMenu {
    margin-top: 0 !important;
  }
}
.mobChapname {
  border-bottom: 0;
}
.mobChapname span {
  font-size: 20px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .all-container .container-wrapper:last-child {
    margin-bottom: 3rem;
  }
}
.all-container .container-wrapper .media i {
  background: none;
}
#addedTodo .modal-header {
  border: none;
}
#addedTodo .modal-footer {
  border: none;
}
#addedTodo .modal-footer .close-btn {
  background: #27AE60;
  border-radius: 5px;
  color: #FFFFFF;
}
#addedTodo .modal-body {
  text-align: center;
}
#addedTodo .modal-body i {
  color: #27AE60;
  font-size: 38px;
}
#addedTodo .modal-body p {
  color: #27AE60;
  font-size: 14px;
  margin-top: 10px;
}
#addedTodo .modal-body p span {
  font-weight: 700;
}
.watch {
  width: 50%;
  background: #F4F5FA;
  border-radius: 0 0 5px 5px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000b3;
}
.watch span {
  border-right: 1px solid #000000b3;
  width: 100%;
}
.listen {
  width: 50%;
  background: #F4F5FA;
  border-radius: 0 0 5px 5px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000b3;
}
.listen span {
  border-right: 1px solid #000000b3;
  width: 100%;
}
.listen span {
  border-right: none;
}
.notes-by-user li {
  padding: 10px;
}
.notes-by-user li .comment-bg {
  background: rgba(255, 255, 10, 0.3);
  font-size: 16px;
  font-weight: 400;
}
.chapter-notes {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #htmlreadingcontent embed {
    width: 400px !important;
  }
}
#allnotes-content .mynotes h3 {
  margin-top: 1rem;
  margin-bottom: 8px !important;
  color: #848484;
  font-size: 16px;
  font-weight: 700;
  text-align: left !important;
  text-transform: capitalize;
  margin-left: 1rem;
}
.btn-flashcard {
  border: 1px solid #17A2B8;
  box-sizing: border-box;
  box-shadow: 0 0 10px #0000001A;
  -webkit-box-shadow: 0 0 10px #0000001A;
  -moz-box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  font-size: 12px;
  color: #17A2B8;
  display: flex;
  align-items: center;
  background: transparent;
}
.btn-flashcard i {
  font-size: 16px;
  margin-right: 5px;
}
.btn-flashcard.print {
  color: #000000b3;
  border: 1px solid #000000b3;
}
.btn-flashcard.print i {
  margin-left: 5px;
  color: #000000b3;
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .btn-flashcard.print {
    display: none;
  }
}
#backfromAllnotes,
#backfromChapternotes,
#backChapternotes,
.backFromRead {
  background: none;
  color: #000000b3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #backfromAllnotes {
    visibility: hidden;
  }
}
#chapter-all-action {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.modal#PlayAudiOnlyModal {
  background: #000000B3 !important;
}
#the-canvas {
  max-width: 768px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #the-canvas {
    width: 280px;
  }
}
.bookTemplate .content-wrapper .price-wrapper .preview-book-btns .btn-book-buy {
  background: #000000b3;
}
.bookTemplate .content-wrapper .price-wrapper .preview-book-btns .btn-book-buy:focus {
  background: #000000b3 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .bookTemplate .content-wrapper .price-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
  .bookTemplate .content-wrapper .price-wrapper .preview-book-btns {
    margin-left: 0;
  }
  .bookTemplate .content-wrapper .price-wrapper .preview-book-btns .btn-book-buy {
    width: 140px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .bookTemplate .read-content {
    margin-bottom: 100px;
  }
}
.book-title {
  padding: 0 1rem;
}
.annotator-adder {
  background: #2F80ED;
  border-radius: 20px;
  border: none;
}
.annotator-adder button.annotate-btn {
  color: #FFFFFF;
  border-right: 1px solid #949494 !important;
}
.annotator-adder button.annotate-btn:last-child {
  border-right: none !important;
}
.annotator-notice {
  display: none !important;
}
.footer-menu-popover .modal-dialog .modal-content {
  box-shadow: none;
  background: none;
}
.annotator-adder,
.annotator-outer,
.annotator-notice {
  z-index: 99 !important;
}
.annotator-touch-widget-inner .annotator-button {
  width: auto;
}
.annotator-touch-controls {
  background: #2F80ED;
  border-radius: 20px;
  border: none;
}
.annotator-button.annotator-focus:first-child,
.annotator-touch-widget-inner .annotator-button {
  border-right: 1px solid #FFFFFF;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .annotator-button.annotator-focus:first-child,
  .annotator-touch-widget-inner .annotator-button {
    width: 33.33%;
  }
}
.annotator-touch-widget-inner > .annotator-button {
  color: #FFFFFF !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .annotator-touch-editor {
    top: -930px !important;
  }
}
#footer-menu-popover {
  z-index: 999;
}
.book-expiry {
  background: #06D781;
  border-radius: 4px;
  width: 246px;
  margin: 0 22px;
  margin-bottom: 1rem;
  height: 70px;
  padding: 0 1rem;
}
.book-expiry i {
  color: #FFFFFF;
  font-size: 36px;
}
.book-expiry p {
  color: #FFFFFF;
  font-size: 10px;
}
.book-expiry p span {
  font-size: 14px;
}
