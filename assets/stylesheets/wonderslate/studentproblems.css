.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
.student-problems .features .row {
  padding-left: 15px;
}
.student-problems .features .row .card {
  border-radius: 10px;
  height: 152px;
  max-width: 200px;
  color: #fff;
  border: none;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.15);
}
@media (max-width: 575px), (min-width: 576px) and (max-width: 767px) {
  .student-problems .features .row .card {
    width: 100%;
    max-width: 100%;
  }
}
.student-problems .features .row .card:nth-child(1) {
  background: radial-gradient(142.42% 142.42% at 8.23% 0%, #D585F6 0%, #8031BA 61.17%) !important;
}
.student-problems .features .row .card:nth-child(2) {
  background: radial-gradient(106.28% 106.45% at 0% 0%, #3ED8CF 0%, #068B82 99.48%) !important;
}
.student-problems .features .row .card:nth-child(3) {
  background: radial-gradient(142.42% 142.42% at 8.23% 0%, #F2C74C 0%, #F2994A 46.86%) !important;
}
.student-problems .features .row .card:nth-child(4) {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, #557300 66.95%) !important;
}
.student-problems .features .row .card:nth-child(5) {
  background: radial-gradient(67.7% 68.99% at 24.68% 24.68%, #F24CE1 0%, #A90098 91.9%) !important;
}
.student-problems .features .row .card:nth-child(6) {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #66C3FD 5.08%, #297AD6 66.95%) !important;
}
.student-problems .features .row .card:nth-child(7) {
  background: radial-gradient(106.28% 106.45% at 0% 0%, #3ED8CF 0%, #068B82 99.48%) !important;
}
.student-problems .features .row .card:nth-child(8) {
  background: radial-gradient(142.42% 142.42% at 8.23% 0%, #F2C74C 0%, #F2994A 46.86%) !important;
}
.student-problems .features .row .card:nth-child(9) {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, #557300 66.95%) !important;
}
.student-problems .features .row .card:nth-child(10) {
  background: radial-gradient(142.42% 142.42% at 8.23% 0%, #D585F6 0%, #8031BA 61.17%) !important;
}
