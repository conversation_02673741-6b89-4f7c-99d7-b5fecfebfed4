.player {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.player .details {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  margin-top: 25px;
}
.player .details .track-art {
  margin: 25px;
  background-size: cover;
  background-position: center;
  border-radius: 15%;
}
.player .details .now-playing,
.player .details .track-artist {
  font-size: 1rem;
}
.player .details .track-name {
  font-size: 2rem;
}
.player .buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.player .buttons .playpause-track,
.player .buttons .prev-track,
.player .buttons .next-track {
  padding: 25px;
  opacity: 0.8;
  transition: opacity 0.2s;
  color: #E83500;
}
.player .buttons .playpause-track:hover,
.player .buttons .prev-track:hover,
.player .buttons .next-track:hover {
  opacity: 1;
}
.player .slider_container {
  width: 75%;
  max-width: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.player .slider_container .seek_slider,
.player .slider_container .volume_slider {
  height: 5px;
  opacity: 0.7;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
}
.player .slider_container .seek_slider::-webkit-slider-thumb,
.player .slider_container .volume_slider::-webkit-slider-thumb {
  width: 15px;
  height: 15px;
  cursor: pointer;
  border-radius: 50%;
}
.player .slider_container .seek_slider:hover,
.player .slider_container .volume_slider:hover {
  opacity: 1;
}
.player .slider_container .seek_slider {
  width: 60%;
}
.player .slider_container .volume_slider {
  width: 30%;
}
.player .slider_container .current-time,
.player .slider_container .total-duration {
  padding: 10px;
}
i.fa-volume-down,
i.fa-volume-up {
  padding: 10px;
}
i.fa-play-circle,
i.fa-pause-circle,
i.fa-step-forward,
i.fa-step-backward {
  cursor: pointer;
}
.rotateInfinite {
  animation: rotateCircle 4s linear infinite;
}
@keyframes rotateCircle {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
