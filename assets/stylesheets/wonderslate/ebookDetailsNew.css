html,
body {
  scroll-behavior: smooth;
}
body {
  background: #fff !important;
}
.mobile-footer-nav {
  display: none !important;
}
.bookDetails__container {
  width: calc(100% - 20%);
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .bookDetails__container {
    width: calc(100% - 2%);
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .bookDetails__container {
    width: calc(100% - 10%);
  }
}
.details__breadcrum .breadcrumb {
  background: transparent !important;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.2);
  padding: 2px 2px 0 0;
  border-radius: 0;
  margin-left: 10px;
}
.details__breadcrum .breadcrumb-item a {
  color: #000;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .details__breadcrum .breadcrumb {
    margin-left: 0px;
  }
  .details__breadcrum .breadcrumb-item {
    font-size: 12px;
  }
}
.bookDetails {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 0 1rem;
}
.bookDetails__coverImage {
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  margin: 0 auto;
  height: fit-content;
}
.bookDetails__coverImage--freeChapterBtn a {
  width: 276px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(0, 0, 0, 0.26);
  height: 35px;
  margin-top: 12px;
  background: #fff;
  transition: all 0.2s ease;
  text-decoration: none;
  color: #000;
  text-align: center;
  border-radius: 5px;
}
.bookDetails__coverImage--freeChapterBtn a:hover {
  background: #FBBC04;
  border-color: #FBBC04;
}
.bookDetails__coverImage .featureIcons_dtl-icons {
  display: none;
}
.bookDetails__info {
  display: flex;
  flex-direction: column;
  grid-column-start: 2;
  grid-column-end: 4;
  height: fit-content;
}
.bookDetails__info .infoSection {
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
}
.bookDetails__info-title {
  font-size: 2rem;
}
.bookDetails__info-publisherName {
  color: rgba(0, 0, 0, 0.4);
  font-size: 1.2rem;
  margin: 10px 0 0 5px;
}
.bookDetails__info-publisherName a {
  color: inherit;
}
.bookDetails__info-dtls {
  margin: 10px 0 0 5px;
}
.bookDetails__info-dtls__box {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  color: rgba(0, 0, 0, 0.4);
}
.bookDetails__info-dtls__box h3 {
  font-size: 14px;
  font-weight: 300;
  color: rgba(0, 0, 0, 0.4);
}
.bookDetails__info-dtls__box h3:first-child {
  width: 75px;
}
.bookDetails__info-dtls__box span {
  width: 16px;
}
.bookDetails__info-dtls__rating {
  margin: 0px 0 10px 5px;
}
.bookDetails__info-dtls__rating .starIcons {
  margin-bottom: 5px;
}
.bookDetails__info-dtls__rating .starIcons i {
  color: #FFD700;
}
.bookDetails__info-dtls__rating a {
  text-decoration: underline !important;
}
.bookDetails__info-dtls__price {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  margin: auto 0 0 5px;
  gap: 1rem;
}
.bookDetails__info-dtls__price-card {
  padding: 12px 12px 5px 12px;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  cursor: pointer;
  min-height: 106px;
}
.bookDetails__info-dtls__price-card .bookType {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.bookDetails__info-dtls__price-card .bookType p {
  margin-right: 10px;
  font-size: 18px;
  font-weight: 500;
}
.bookDetails__info-dtls__price-card .bookType img {
  width: 20px;
}
.bookDetails__info-dtls__price-card .bookPrice {
  display: flex;
  align-items: center;
  gap: 12px;
}
.bookDetails__info-dtls__price-card .bookPrice__original {
  text-decoration: line-through;
  color: rgba(0, 0, 0, 0.38);
  font-weight: 600;
  font-size: 15px;
}
.bookDetails__info-dtls__price-card .bookPrice__offer {
  font-size: 20px;
  font-weight: 500;
}
.bookDetails__info-dtls__price-card .saving__price {
  color: green;
  font-size: 13px;
}
.bookDetails__info-dtls__addToCart {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  margin: 12px 0 0 5px;
  grid-gap: 1rem;
}
.bookDetails__info-dtls__addToCart .addToCartBtn {
  background: #FBBC04;
  border: 1px solid #FBBC04;
  padding: 12px 10px;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  grid-column-start: 1;
  display: flex;
  justify-content: center;
  color: #000;
}
.bookDetails__paperback {
  grid-row-start: 2;
  grid-column-start: 2;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  display: flex;
  grid-column-end: 4;
  flex-direction: column;
  height: fit-content;
  margin-top: 1rem;
}
.bookDetails__paperback h3 {
  font-size: 1.4rem !important;
  margin-left: 5px !important;
  position: relative;
}
.bookDetails__paperback h3 span {
  color: rgba(0, 0, 0, 0.2);
  font-size: 18px;
  cursor: pointer;
}
.bookDetails__paperback h3 span div {
  background: #000;
  color: #fff;
  padding: 8px;
  border-radius: 5px;
  position: absolute;
  top: -10px;
  right: 5px;
  transition: all 0.2s ease;
  display: none;
}
.bookDetails__paperback h3 span div::before {
  content: '';
  width: 10px;
  height: 10px;
  border: 1px solid #000;
  position: absolute;
  left: -5px;
  background: #000;
  transform: rotate(45deg);
  top: 15px;
}
.bookDetails__paperback h4 {
  margin: 0 0 0 5px;
}
.bookDetails__paperback .affiliationCards {
  display: flex;
  gap: 1rem;
  width: 60%;
  margin: 14px 0 0 5px;
}
.bookDetails__paperback .affiliationCards__card {
  width: 100%;
  padding: 5px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}
.bookDetails__paperback .affiliationCards__card p {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 5px;
}
.bookDetails__description {
  grid-row-start: 3;
  grid-column-start: 2;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  display: flex;
  grid-column-end: 4;
  height: fit-content;
  margin-bottom: 10px;
}
.bookDetails__description-tab {
  width: 100%;
  margin: 0 0 0 5px;
}
.bookDetails__description-tab .tabWrapper ul {
  padding-left: 0;
  list-style: none;
  display: flex;
  background: #EDEDED;
  border-radius: 5px;
  gap: 1rem;
  padding: 5px;
}
.bookDetails__description-tab .tabWrapper ul li {
  display: block;
  padding: 7px 18px;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  border: 1.5px solid;
  border-color: transparent;
  transition: all 0.3s ease;
}
.bookDetails__description-tab .tabWrapper .tabContent {
  padding: 0 10px;
  transition: all 0.2s ease;
  min-height: 100px;
  max-height: 150px;
  overflow-y: scroll;
}
.bookDetails__description-tab .tabWrapper .tabContent .bookContains {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: baseline;
}
.bookDetails__description-tab .tabWrapper .tabContent .bookContains__info {
  display: flex;
  align-items: center;
}
.bookDetails__description-tab .tabWrapper .tabContent .bookContains__info-text {
  width: 100px;
  font-weight: bold;
}
.bookDetails__description-tab .tabWrapper .tabContent .bookContains__info-sep {
  margin-right: 10px;
}
.bookDetails__subscription {
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  padding: 1rem;
  display: flex;
}
.bookDetails__subscription-form {
  margin-left: 5px;
  margin-bottom: 0;
}
.bookDetails__subscription-form__group {
  display: flex;
  flex-direction: column;
  margin-bottom: 12px;
}
.bookDetails__subscription-form__group:last-child {
  margin-bottom: 0px;
}
.bookDetails__subscription-form__group label {
  margin-bottom: 0;
  font-size: 16px;
}
.bookDetails__subscription-form__group select {
  border: 1px solid rgba(0, 0, 0, 0.2);
  width: 300px;
  padding: 10px;
  border-radius: 5px;
  margin-top: 5px;
}
.bookDetails__subscription-form__group select:focus-visible {
  outline: none;
}
.bookDetails__subscription-form__group .addSubscription {
  background: #FBBC04;
  border: 1px solid #FBBC04;
  padding: 12px 10px;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  grid-column-start: 1;
  display: flex;
  justify-content: center;
  color: #000;
}
.bookDetails__features .featureIcons_dtl {
  border: 1px dashed rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  height: fit-content;
  background-color: #fbbc041f;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 600px;
  width: 100%;
  padding: 10px 20px 10px 20px;
  margin-bottom: 10px;
}
.bookDetails__features .featureIcons_dtl h4 {
  text-align: center;
}
.bookDetails__features .featureIcons_dtl-icons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 1rem;
  margin-top: 15px;
}
.bookDetails__features .featureIcons_dtl-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.bookDetails__features .bookImp {
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  height: fit-content;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 600px;
  width: 100%;
  padding: 12px 20px 10px 20px;
}
.bookDetails__features .bookImp h4 {
  text-align: center;
}
.bookDetails__features .bookImp ul {
  padding-left: 20px;
  margin-top: 14px;
  margin-bottom: 0;
  list-style: none;
}
.bookDetails__features .bookImp ul li {
  margin-bottom: 10px;
}
.bookDetails__features .bookImp ul li div {
  display: flex;
  align-items: center;
  gap: 10px;
}
.bookDetails__features .bookImp ul li div p {
  font-size: 15px;
  font-weight: 500;
}
.bookDetails__reviews {
  grid-row-start: 4;
  grid-column-start: 1;
  grid-column-end: 5;
  display: flex;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 5px;
  flex-direction: column;
  background: #EFEFEF;
}
.bookDetails__reviews h4 {
  margin-bottom: 10px;
}
.bookDetails__reviews .reviewCards {
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-snap-stop: always;
}
.bookDetails__reviews .reviewCards .reviewCard {
  display: flex;
  flex: 0 0 auto;
  width: 400px;
  margin-right: 10px;
  gap: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 10px;
  border-radius: 10px;
  background: #fff;
  scroll-snap-align: start;
}
.bookDetails__reviews .reviewCards .reviewCard__img {
  width: 50px;
}
.bookDetails__reviews .reviewCards .reviewCard__img-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #EFEFEF;
  border-radius: 50%;
  width: 50px;
  height: 50px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .bookDetails {
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 1rem;
  }
  .bookDetails__coverImage {
    padding: 5px;
    border: none;
  }
  .bookDetails__coverImage .imgAndFeature {
    display: flex;
    gap: 12px;
  }
  .bookDetails__coverImage .featureIcons_dtl-icons {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    justify-content: space-between;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 10px 5px;
    border: 1px dashed rgba(0, 0, 0, 0.2);
    background-color: #fbbc041f;
    border-radius: 4px;
  }
  .bookDetails__coverImage .featureIcons_dtl-icons .featureIcons_dtl-icon p {
    font-size: 12px;
    line-height: normal;
  }
  .bookDetails__info {
    grid-column-start: unset;
    grid-column-end: unset;
  }
  .bookDetails__info-title {
    font-size: 1.6rem;
  }
  .bookDetails__info-publisherName {
    font-size: 1.1rem;
  }
  .bookDetails__info-dtls {
    display: none;
  }
  .bookDetails__info-dtls__rating {
    margin: 10px 0 10px 5px;
  }
  .bookDetails__info-dtls__rating .starIcons {
    margin-bottom: 5px;
  }
  .bookDetails__info-dtls__addToCart {
    grid-template-columns: repeat(1, 1fr);
  }
  .bookDetails__paperback {
    grid-row-start: unset;
    grid-column-start: unset;
    grid-column-end: unset;
    margin-top: 12px ;
  }
  .bookDetails__paperback h3 {
    font-size: 1.4rem !important;
    margin-left: 5px !important;
  }
  .bookDetails__paperback h3 span div {
    top: -50px;
  }
  .bookDetails__paperback h3 span div::before {
    content: '';
    width: 10px;
    height: 10px;
    border: 1px solid #000;
    position: absolute;
    left: 80%;
    background: #000;
    transform: rotate(45deg);
    top: 35px;
  }
  .bookDetails__paperback .affiliationCards {
    width: 100%;
  }
  .bookDetails__subscription {
    grid-column-start: 1;
    grid-column-end: unset;
  }
  .bookDetails__description {
    grid-row-start: unset;
    grid-column-start: unset;
    grid-column-end: unset;
    padding: 10px;
    margin-top: 0px !important;
    margin-bottom: 0 !important;
  }
  .bookDetails__description-tab {
    margin: 0;
  }
  .bookDetails__description-tab .tabWrapper ul {
    gap: 10px;
  }
  .bookDetails__description-tab .tabWrapper ul li {
    display: flex;
    padding: 5px 8px;
    border-radius: 5px;
    text-align: center;
    align-items: center;
  }
  .bookDetails__description-tab .tabWrapper .tabContent {
    padding: 0 5px;
  }
  .bookDetails__description-tab .tabWrapper .tabContent .bookContains {
    grid-template-columns: repeat(1, 1fr);
  }
  .bookDetails__description-tab .tabWrapper .tabContent .bookContains .bookInfoWrap {
    margin-top: 10px;
  }
  .bookDetails__description-tab .tabWrapper .tabContent .bookContains .bookSpecWrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
  }
  .bookDetails__features {
    grid-row-start: 4;
  }
  .bookDetails__features .featureIcons_dtl {
    display: none;
  }
  .bookDetails__reviews {
    grid-row-start: unset;
    grid-column-start: unset;
    grid-column-end: unset;
    overflow-x: hidden;
  }
  .bookDetails__reviews .reviewCards {
    overflow-x: scroll;
    flex-direction: row;
  }
  .bookDetails__reviews .reviewCards .reviewCard {
    width: 300px;
  }
  .bookImp h4 {
    text-align: start !important;
  }
  .bookImp ul {
    padding: 5px !important;
  }
}
.activeCart {
  background: #BFFFF7 !important;
  border: 1.5px solid rgba(0, 0, 0, 0.83);
  position: relative;
}
.activeCart::after {
  content: '\2713';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 1px solid limegreen;
  border-radius: 50%;
  background: limegreen;
  top: 3px;
  right: 3px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
}
.tabActive {
  background: #fff;
  border-color: rgba(0, 0, 0, 0.175) !important;
}
.bookDetails__paperback h3 span i:hover + .customToolTip {
  display: block;
}
#relatedBooks > div {
  padding: 0 0 !important;
}
.breadcrumb-item button {
  background: transparent;
  border: none !important;
}
.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #6c757d;
  content: ">" !important;
}
.relatedTitle {
  padding: 0 0px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .bookDetails__info-dtls__addToCart {
    position: fixed;
    width: 100%;
    bottom: 0px;
    margin-left: -20px;
    background: #fff;
    z-index: 99;
  }
  .bookDetails__info-dtls__price {
    position: fixed;
    bottom: 43px;
    padding: 5px;
    background: #fff;
    width: 100%;
    margin-left: -20px;
    z-index: 99;
    gap: 5px;
  }
  .bookDetails__info-dtls__price-card {
    padding: 12px 4px 5px 4px;
  }
  .relatedTitle {
    padding: 0 14px;
  }
}
.comboIcon {
  width: 50px !important;
}
