.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
@theme 1 {
  background: #9A309B;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li {
  font-family: 'Poppins', sans-serif;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
header {
  border-bottom-left-radius: 17px;
  border-bottom-right-radius: 17px;
}
header {
  width: 100%;
  z-index: 999;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  top: 0;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}
header.normalHeader {
  box-shadow: none;
  position: relative;
}
header .logo {
  height: 45px;
}
.ws-header {
  background: none;
}
.ws-header div.mobile-profile {
  display: flex;
  position: absolute;
  z-index: 9999;
  right: 10px;
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .ws-header div.mobile-profile {
    display: block !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait) {
  .ws-header div.mobile-profile .dropdown-menu-right {
    right: -10px;
    min-width: 353px;
    padding-bottom: 0;
    margin-top: 4px;
    margin-left: auto;
  }
  .ws-header div.mobile-profile .dropdown-menu-right .media {
    padding-bottom: 0 !important;
    position: relative;
  }
  .ws-header div.mobile-profile .dropdown-menu-right .media .drop-profile {
    width: 42px;
    height: 42px;
  }
  .ws-header div.mobile-profile .dropdown-menu-right .media .edit-btn {
    position: absolute;
    bottom: 2rem;
    left: 3rem;
    width: 15px;
    height: 15px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    text-align: center;
  }
  .ws-header div.mobile-profile .dropdown-menu-right .media .edit-btn i {
    font-size: 9px;
    position: relative;
    top: 1px;
  }
  .ws-header div.mobile-profile .dropdown-menu-right .dropdown-item {
    padding: 1rem 1.5rem;
    font-weight: normal;
  }
  .ws-header div.mobile-profile .dropdown-menu-right .dropdown-item:last-child {
    border-bottom: none;
  }
}
.ws-header div.mobile-profile .nav-item {
  list-style-type: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .ws-header div.mobile-profile .nav-item a {
    font-size: 10px;
    padding: 0;
  }
}
.ws-header div.mobile-profile .nav-item a img {
  width: 24px;
  height: 24px;
}
.ws-header .navbar-nav .nav-item.active {
  background-color: transparent;
}
.ws-header .navbar-nav .nav-link {
  color: #ffffff;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  cursor: pointer;
  white-space: nowrap;
}
.ws-header .navbar-nav .nav-link:hover {
  font-weight: 400;
  transition: all 0.5s;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .ws-header .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
}
.ws-header .navbar-nav.right-menu li.notification {
  width: 24px;
  height: 24px;
  display: inline-block;
  border-radius: 50%;
}
.ws-header .navbar-nav.right-menu li.notification a {
  padding: 0;
  text-align: center;
  cursor: pointer;
}
.ws-header .navbar-nav.right-menu li.notification i {
  margin-top: 5px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu {
  min-width: 370px;
  padding-bottom: 0;
  margin-top: -5px;
  margin-left: auto;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media {
  position: relative;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item {
  padding: 1rem 1.5rem;
  font-weight: normal;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item:last-child {
  border-bottom: none;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu a {
  position: relative;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn {
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 50%;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: absolute;
  left: 70px;
  bottom: 30px;
  text-align: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn {
    right: 80px;
  }
}
.ws-header .navbar-nav.right-menu li a img {
  width: 46px;
  height: 46px;
}
.ws-header .navbar-nav.right-menu li img.drop-profile {
  width: 72px;
  height: 72px;
}
.ws-header .navbar-nav.right-menu li:last-child a {
  cursor: pointer;
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .ws-header .navbar-nav.mr-auto,
  .ws-header .navbar-nav.right-menu {
    display: none !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
}
.bg-dark {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-bottom-left-radius: 17px;
  border-bottom-right-radius: 17px;
  height: 63px;
}
.logo {
  height: 36px;
}
.back-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
}
.back-btn:focus {
  outline: 0;
}
.back-btn i {
  font-size: 24px;
  color: #fff;
}
.navbar-dark .navbar-nav .nav-link {
  font-size: 14px;
  font-weight: 300;
  padding-right: 2rem;
}
.navbar-dark .navbar-nav .nav-link.active {
  font-weight: 500;
}
header.normalHeader {
  position: fixed;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  header.normalHeader {
    position: static;
  }
}
.bookTemplate {
  margin-top: 64px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate {
    margin-top: 0;
  }
}
.hasScrolled .mobChapname {
  width: 100%;
  z-index: 999;
  height: 64px;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, #cd3e81 0%, #872ba4 100%);
  top: 0;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-bottom-left-radius: 17px;
  border-bottom-right-radius: 17px;
}
.subMenu {
  position: sticky;
  top: 64px;
  padding: 0.3rem 0;
  z-index: 91;
  background: #ffffff;
}
