body {
  background: #fff !important;
}
.cus_container {
  width: calc(100% - 25%);
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .cus_container {
    width: calc(100% - 5%);
  }
}
.landingPage {
  min-height: 75vh;
}
.landingPage_container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container {
    width: 100%;
  }
}
.landingPage_container .lp_hero {
  padding: 1rem;
  margin-bottom: 5rem;
  width: 100%;
  background: #f7941e38;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container .lp_hero {
    padding: 0;
  }
}
.landingPage_container .lp_hero h2 {
  margin-bottom: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container .lp_hero h2 {
    text-align: center;
  }
}
.landingPage_container .lp_hero-search {
  width: 100%;
}
.landingPage_container .lp_hero-search .titleDiv {
  width: 50%;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container .lp_hero-search .titleDiv {
    width: 100%;
  }
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs {
  width: 80%;
  display: flex;
  justify-content: center;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs {
    width: 90%;
  }
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input {
  width: calc(100% - 45%);
  position: relative;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input {
    width: 100%;
  }
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input p {
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.3);
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input p a {
  color: rgba(0, 0, 0, 0.3);
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input input {
  width: 100%;
  height: 50px;
  border: 1px solid #f7941eb5;
  padding: 12px;
  font-size: 18px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input input:focus-visible {
  outline: none !important;
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input .typeahead.dropdown-menu {
  position: absolute !important;
  width: 100%;
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input-closeBtn {
  width: 25px;
  border: 1px solid rgba(0, 0, 0, 0.3);
  height: 25px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.3);
  position: absolute;
  right: 10px;
  top: 8px;
  font-size: 13px;
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs .lp_hero-search_input-closeBtn i {
  font-size: 16px;
  padding-left: 0;
  text-align: center;
  margin-left: -2px;
}
.landingPage_container .lp_hero-search_wrapper .lp_hero-search_inputs button {
  outline: none;
  border-left: 0;
  border-right: 1px solid #f7941eb5;
  border-top: 1px solid #f7941eb5;
  border-bottom: 1px solid #f7941eb5;
  background: #f7941eb5;
  width: 100px;
  height: 50px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  font-size: 16px;
}
.landingPage_container .lp_store {
  margin-bottom: 5rem;
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container .lp_store {
    width: 88%;
  }
}
.landingPage_container .lp_store-cards {
  display: flex;
  justify-content: center;
  gap: 1.6rem;
}
.landingPage_container .lp_store-cards_card {
  display: flex;
  padding: 10px;
  border-radius: 5px;
  width: 250px;
  text-align: center;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  gap: 10px;
  box-shadow: 0 4px 10px #0000001A;
  color: #5e5e5e;
  background: #fff;
}
.landingPage_container .lp_store-cards_card p {
  width: 100%;
  text-align: center;
  font-size: 17px;
  color: #5e5e5e;
  font-weight: bold;
}
.landingPage_container .lp_store-cards_card p span {
  font-size: 11px;
}
.landingPage_container .lp_store-cards_card img {
  width: 50px;
}
.landingPage_container .lp_products {
  margin-bottom: 5rem;
  width: 100%;
}
.landingPage_container .lp_products h2 {
  text-align: center;
  margin-bottom: 1.6rem;
  position: relative;
}
.landingPage_container .lp_products h2:before {
  content: '';
  position: absolute;
  width: 90px;
  height: 4px;
  background: #f7941e;
  display: flex;
  top: 100%;
  left: 45%;
  border-radius: 100px;
}
.landingPage_container .lp_products-cards {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.6rem;
  flex-wrap: wrap;
}
.landingPage_container .lp_products-cards_card {
  display: flex;
  padding: 10px;
  justify-content: center;
  border-radius: 5px;
  width: 250px;
  box-shadow: 0 4px 10px #0000001A;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  background: #fff;
}
.landingPage_container .lp_products-cards_card p {
  color: #5e5e5e;
  font-weight: bold;
  font-size: 17px;
}
.landingPage_container .lp_products-cards_card img {
  width: 40px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .landingPage_container .lp_products-cards_card {
    width: 165px;
  }
}
.publisher_products .pb_product-showcase {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  margin-top: 3rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_product-showcase {
    flex-direction: column;
  }
}
.publisher_products .pb_product-showcase_content {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  width: 50%;
  padding: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_product-showcase_content {
    width: 100%;
  }
}
.publisher_products .pb_product-showcase_img {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}
.publisher_products .pb_product-showcase_img img {
  width: 280px;
  border: 1px solid;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_product-showcase_img img {
    width: 100%;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_product-showcase_img {
    width: 100%;
  }
}
.publisher_products .publishers_logo {
  padding: 2rem;
  background: rgba(0, 0, 0, 0.1);
}
.publisher_products .publishers_logo-imgs {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  flex-wrap: wrap;
}
.publisher_products .pb_contact {
  display: flex;
  justify-content: center;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_contact {
    flex-direction: column;
  }
}
.publisher_products .pb_contact-details {
  width: 50%;
}
.publisher_products .pb_contact-details a {
  display: flex;
  align-items: center;
}
.publisher_products .pb_contact-details a img {
  width: 60px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_contact-details {
    width: 100%;
  }
}
.publisher_products .pb_contact-form {
  width: 50%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.publisher_products .pb_contact-form form {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  grid-gap: 1rem;
  margin-top: 2rem;
  width: 70%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_contact-form form {
    grid-template-columns: repeat(1, 1fr);
    width: 100%;
  }
}
.publisher_products .pb_contact-form form input {
  width: 100%;
  padding: 9px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}
.publisher_products .pb_contact-form form textarea {
  width: 100%;
  padding: 9px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}
.publisher_products .pb_contact-form form .form_item-5 {
  grid-column: 1/3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_contact-form form .form_item-6 {
    grid-column: 1/3;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_products .pb_contact-form {
    width: 100%;
  }
}
.showcase_wrapper {
  padding: 2rem;
}
.showcase_wrapper:nth-child(odd) {
  background: lightblue;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .showcase_wrapper {
    padding: 10px;
  }
}
.sc_product {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 40vh;
}
