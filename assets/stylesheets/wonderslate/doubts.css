.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
body {
  margin: 0 !important;
  padding: 0 !important;
}
a,
p,
h4,
button {
  font-family: 'Poppins', sans-serif !important;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.admin-wrapper {
  min-height: 100vh;
}
.discussion-menu {
  margin-top: 1rem;
}
.discussion-menu.nav-pills .nav-item {
  margin-bottom: 1rem;
  text-align: center;
}
.discussion-menu.nav-pills .nav-item .nav-link {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: #ffffff !important;
  color: #9A309B;
}
.discussion-menu.nav-pills .nav-item .nav-link.active {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%) !important;
  color: #ffffff;
}
.discussion-menu.nav-pills .nav-item .nav-link:focus {
  color: #ffffff;
}
.line {
  position: absolute;
  left: 0;
  top: 1rem;
}
.line img {
  height: 140px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
}
.discussion-card {
  box-sizing: border-box;
  padding: 1rem;
  padding-top: 0.5rem;
  background: transparent;
  border: none;
  width: 100%;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .discussion-card {
    padding: 10px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  }
}
.discussion-card img {
  width: auto;
  max-width: 290px;
  max-height: 200px;
}
.discussion-card ol {
  background: transparent;
  margin: 0;
  border-radius: 0;
  padding: 5px 0;
  margin-bottom: 10px;
}
.discussion-card ol span {
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  color: rgba(68, 68, 68, 0.48);
}
.discussion-card ol li {
  padding: 0;
}
.discussion-card ol li.breadcrumb-item {
  padding: 0;
}
.discussion-card ol li a {
  color: #9A309B;
  font-size: 12px;
}
.discussion-card ol .breadcrumb-item + .breadcrumb-item::before {
  content: '>';
  font-size: 10px;
  padding: 3px;
  top: -1px;
  position: relative;
  color: #9A309B;
}
.q-question {
  color: #A73397;
}
.content > p {
  color: #444444;
}
.profile {
  display: flex;
  align-items: center;
}
.profile img {
  width: 28px;
  height: 28px;
}
.profile h4 {
  font-size: 12px;
  margin-left: 10px;
  font-weight: normal;
}
.profile p {
  font-size: 10px;
  color: rgba(68, 68, 68, 0.48);
  margin-left: 5px;
}
.card-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  margin-bottom: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .card-actions {
    padding: 0.4rem 0;
  }
}
.card-actions button {
  background: transparent;
  outline: 0;
  border: none;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.48);
  white-space: nowrap;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .card-actions button {
    font-size: 10px;
  }
}
.card-actions button i {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.48);
}
.card-actions button i.circle {
  border-radius: 50px;
  width: 24px;
  height: 24px;
  border: 1.25px solid rgba(68, 68, 68, 0.48);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  margin-right: 5px;
}
.card-actions button i.circle.bord {
  border-color: #A73397;
}
.card-actions button.drop-menu {
  background: rgba(68, 68, 68, 0.2);
  justify-content: center;
  border-radius: 4px;
}
.card-actions button.drop-menu i {
  margin-right: 0;
}
.card-actions .dropdown-toggle::after {
  border: none;
}
.card-actions .dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  border: none;
}
.card-actions .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #9A309B;
}
.card-actions .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.card-actions .dropdown-menu > li:hover {
  background: rgba(0, 0, 0, 0.08);
}
.card-actions .dropdown-menu > li a {
  cursor: pointer;
  color: #ED2F2F;
}
.card-actions .dropdown-menu > li a i {
  color: #ED2F2F;
  -webkit-text-fill-color: #ED2F2F;
}
.card-actions .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.card-actions .dropdown-menu > li > a:hover {
  background: none;
}
.card-actions .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.card-actions button.answer {
  background: #FFFFFF;
  border: 1.5px solid #9A309B;
  box-sizing: border-box;
  border-radius: 5px;
  color: #9A309B;
  font-weight: normal;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-actions button.answer i {
  color: #9A309B;
  margin-right: 10px;
}
.divider {
  background: url('../../images/discussionboard/line-horiz.svg') center center no-repeat;
  height: 2px;
  margin: 0 auto;
}
.moderate-btns button {
  display: block;
  border: none;
  width: 75px;
  height: 75px;
  border-radius: 50px;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  box-shadow: 0px 0px 10px rgba(95, 95, 95, 0.15);
  font-size: 9px;
  color: #ffffff;
  outline: 0;
  margin: 10px auto;
}
.moderate-btns button.delete {
  border: 1px solid #AA3493;
  background: #ffffff;
  color: #ED2F2F;
}
.moderate-btns button.delete i {
  color: #ED2F2F;
}
.moderate-btns button:focus {
  outline: none;
}
.moderate-btns button i {
  display: block;
  margin-bottom: 5px;
}
.pagination {
  display: flex;
  align-items: center;
}
.pagination li {
  margin-right: 10px;
}
.pagination li.disabled {
  background: none;
}
.pagination li.disabled a.actions {
  color: #b4b4b4;
}
.pagination li a {
  font-size: 18px;
  text-align: center;
  background: none;
  color: #000;
}
.pagination li a span {
  display: block;
}
.pagination li a.actions {
  font-size: 8px;
}
.pagination li a.actions span {
  font-size: 18px;
}
.pagination li a.page-link {
  border-radius: 0;
  border: none;
}
.pagination li a.page-link:hover {
  background: none;
}
.pagination li a.page-link:focus {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
}
.pagination li.active a {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  width: 33px;
  height: 33px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.confirm,
.addtag {
  background: none;
  color: #9A309B;
  font-size: 10px;
  position: absolute;
  right: 5px;
  border: none;
  display: flex;
  align-items: center;
}
.confirm i,
.addtag i {
  font-size: 14px;
  color: #9A309B;
}
.breadcrumb {
  display: flex;
  align-items: center;
  min-height: 36px;
}
.breadcrumb > span {
  display: block;
  margin-right: 5px;
}
.breadcrumb select {
  background: #9A309B;
  color: #ffffff;
  border-radius: 4px;
  border: none;
  margin-right: 5px;
  font-size: 12px;
  height: 26px;
}
.sub-name {
  font-size: 12px;
  color: #949494;
}
.addDoubts {
  font-size: 12px;
  background: none;
  border: none;
  margin-right: 5px;
}
.addDoubts:focus {
  outline: none;
}
.addDoubts:hover {
  color: #A73397;
}
.dropup li a {
  font-size: 12px;
}
.dropup li a:hover {
  text-decoration: none;
}
.answer-card {
  min-height: 290px;
  width: 100%;
  padding: 1rem;
  border: none;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  background: transparent;
  border-radius: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .answer-card {
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  }
}
.answer-card .answer-textarea {
  min-height: 210px;
  border: none;
  border: 1px solid rgba(68, 68, 68, 0.1);
}
.answer-card .answer-textarea:focus {
  outline: 0;
}
.answer-card .answer-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
.answer-card .answer-actions button {
  margin-right: 15px;
  background: none;
  border: none;
}
.answer-card .answer-actions button i {
  color: #949494;
}
.answer-card .answer-actions button.post {
  width: 275px;
  background: none;
  text-transform: uppercase;
  color: #A73397;
  min-height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1.25px solid #A73397;
}
.answer-card .answer-actions button.cancels {
  width: 275px;
  background: #ffffff;
  text-transform: uppercase;
  min-height: 45px;
  border: 1px solid rgb(220 53 86.25);
  color: rgb(220 53 86.25);
  display: flex;
  align-items: center;
  justify-content: center;
}
.postmodal {
  text-align: center;
  position: absolute;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin: 0 auto;
  width: 319px;
  height: 190px;
  margin-top: 4rem;
}
.postmodal.fade:not(.show) {
  opacity: 1;
}
.postmodal .modal-content {
  border: none;
}
.postmodal h4 {
  font-size: 14px;
  color: #9A309B;
  margin-top: 1rem;
  font-weight: normal;
}
.postmodal p {
  font-size: 10px;
  color: #949494;
}
.postmodal .modal-dialog {
  transition: none !important;
  transform: none !important;
  margin: 0;
}
.modalBackdrop {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.5;
  border-radius: 4px;
  display: none;
}
.btn-dismiss {
  margin-top: 1rem;
  background: #9A309B;
  color: #fff;
  font-size: 14px;
}
.circle_around {
  border: 3px solid #9A309B;
  border-radius: 50px;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.circle_around i {
  color: #9A309B;
  font-weight: bold;
}
.answer-box {
  background: rgba(148, 148, 148, 0.1);
  width: 90%;
  border: none;
  min-height: 40px;
  font-size: 14px;
}
.category-modal {
  background: radial-gradient(156.52% 3032.45% at -2.67% 0%, #8A2CA3 20.31%, #CA3D82 100%) !important;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px 10px 0px 0px;
  display: none;
  position: absolute;
  margin: 0 auto;
  margin-top: 4rem;
}
.category-modal.fade:not(.show) {
  opacity: 1;
}
.category-modal .modal-content {
  border: none;
  background: transparent;
  min-height: 200px;
  margin-top: 4rem;
}
.category-modal h4 {
  font-size: 14px;
  color: #ffffff;
  font-weight: normal;
}
.category-modal p {
  font-size: 10px;
  color: #ffffff;
  margin-top: 0.5rem;
}
.category-modal .modal-dialog {
  transition: none;
  transform: none;
  margin: 0;
}
.category-modal .modal-footer {
  border: none;
}
.category-modal .modal-footer .skip {
  background: none;
  border: none;
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  width: 100%;
  height: 50px;
  color: #6D2AB8;
  border-radius: 5px;
}
.category-modal .modal-footer .skip:focus {
  outline: 0;
}
.category-modal .filter {
  border-color: #ffffff !important;
}
.category-modal .filter i {
  color: #ffffff;
}
.solved {
  position: absolute;
  right: 20px;
  color: #379B5B;
  background: #D4F1DF;
  border-radius: 50px;
  width: 65px;
  font-size: 14px;
  text-align: center;
  display: block;
}
.userAnswer {
  background: rgba(148, 148, 148, 0.1);
  margin-left: 10px;
  margin-top: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
}
.userAnswer img {
  max-width: 350px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .userAnswer img {
    max-width: 290px;
  }
}
.userAnswer input {
  background: transparent;
}
.answerContent .card-actions {
  padding: 0 0.5rem;
}
.answerContent .card-actions button {
  font-size: 10px;
}
.answer-head {
  font-size: 14px;
  text-transform: uppercase;
  color: #949494;
  margin-bottom: 1rem;
}
#mobileque-modal .category-modal {
  display: block;
  position: fixed;
}
#mobileque-modal .category-modal .modal.fade .modal-dialog {
  transition: none !important;
}
#mobileque-modal .modalBackdrop {
  position: fixed;
}
#mobileque-modal .postmodal {
  position: fixed;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}
.textOverflow {
  max-width: 98%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.typeahead.dropdown-menu {
  max-width: 349px;
  min-width: 349px;
}
.typeahead.dropdown-menu li a {
  max-width: 98%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.taggs {
  display: block;
  margin-right: 10px;
}
.ans-modal-img-remove {
  color: #ED2F2F;
  border: #ED2F2F;
  border: 1px solid #ED2F2F;
}
.searchbar .typeahead.dropdown-menu {
  max-width: 95%;
  min-width: 95%;
}
.admin-wrapper .container-fluid {
  margin: 0 4rem;
}
select {
  font-size: 16px !important;
}
.answer-drop .drop-menu {
  background: none;
  border: none;
  color: #A73397;
}
.answer-drop .drop-menu:focus {
  outline: none;
}
.answer-drop .dropleft .dropdown-toggle::before {
  display: none;
}
.answer-drop .dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  border: none;
}
.answer-drop .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #9A309B;
}
.answer-drop .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.answer-drop .dropdown-menu > li:hover {
  background: rgba(0, 0, 0, 0.08);
}
.answer-drop .dropdown-menu > li a {
  cursor: pointer;
  color: #ED2F2F;
}
.answer-drop .dropdown-menu > li a i {
  color: #ED2F2F;
  -webkit-text-fill-color: #ED2F2F;
}
.answer-drop .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.answer-drop .dropdown-menu > li > a:hover {
  background: none;
}
.answer-drop .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.answer-drop .dropdown,
.answer-drop .dropleft,
.answer-drop .dropright,
.answer-drop .dropup {
  top: 3px;
}
.addtodoubts {
  position: absolute;
  display: flex;
  align-items: center;
  right: 10px;
}
.addtodoubts button {
  color: rgba(68, 68, 68, 0.48);
}
.addtodoubts .drop-menu {
  background: none;
  border: none;
  color: #A73397;
}
.addtodoubts .drop-menu:focus {
  outline: none;
}
.addtodoubts .dropleft .dropdown-toggle::before {
  display: none;
}
.addtodoubts .dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  border: none;
}
.addtodoubts .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #9A309B;
}
.addtodoubts .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.addtodoubts .dropdown-menu > li:hover {
  background: rgba(0, 0, 0, 0.08);
}
.addtodoubts .dropdown-menu > li a {
  cursor: pointer;
  color: #ED2F2F;
}
.addtodoubts .dropdown-menu > li a i {
  color: #ED2F2F;
  -webkit-text-fill-color: #ED2F2F;
}
.addtodoubts .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.addtodoubts .dropdown-menu > li > a:hover {
  background: none;
}
.addtodoubts .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.addtodoubts .dropdown,
.addtodoubts .dropleft,
.addtodoubts .dropright,
.addtodoubts .dropup {
  top: 3px;
}
.flex-action {
  display: flex;
  align-items: center;
  min-height: 24px;
}
.flex-action #share-button {
  border-left: 0.5px solid rgba(68, 68, 68, 0.48);
}
#cke_questionText {
  border-radius: 10px;
  border: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .reset-padding {
    padding: 0;
  }
}
#myDoubtsTab > p,
#myAnswerTab > p,
#alldoubts > p {
  margin-top: 1rem;
  padding: 5px;
}
.reset-app #myDoubtsTab > p,
#myAnswerTab > p,
#alldoubts > p {
  margin-top: 7rem;
  padding: 5px;
}
select,
label,
input,
p,
h1,
h2,
h3,
h4,
a,
button {
  font-family: 'Poppins', sans-serif !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  select {
    font-size: 16px !important;
  }
}
.sectionHeight {
  min-height: calc(100vh - 328px);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .sectionHeight {
    min-height: 50px;
  }
}
.sectionHeight .nav-tabs {
  height: 36px;
  position: relative;
  background: #fff;
  border: none;
}
.sectionHeight .nav-tabs .nav-link {
  border: none;
  border-radius: 0;
  font-size: 14px;
  color: #949494;
  padding: 0.5rem;
}
.sectionHeight .nav-tabs .nav-item.show .nav-link,
.sectionHeight .nav-tabs .nav-link.active {
  border: none;
  background: none;
  color: #9A309B;
}
.sectionHeight h2 {
  font-size: 24px;
  font-weight: bold;
  color: #CE3E81;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .sectionHeight > .container {
    padding: 0;
  }
}
.search-ws {
  background: none;
  border: none;
  border-radius: 50px;
  width: 36px;
  height: 36px;
  border: 1px solid #CE3E81;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}
.search-ws:focus {
  outline: 0;
}
.search-ws i {
  color: #CE3E81;
}
.askdoubt {
  background: #9A309B;
  position: absolute;
  right: 30px;
  top: -30px;
  border: 1px solid #FFFFFF;
  box-sizing: border-box;
  border-radius: 10px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  height: 45px;
  padding: 10px;
}
.askdoubt i {
  margin-right: 10px;
  font-size: 18px;
}
.askdoubt:focus {
  outline: 0;
}
.selected.form-control {
  background: rgba(255, 226, 162, 0.21);
  border: none;
}
.myBox {
  position: relative;
  display: inline-block;
}
.myBox:after {
  content: "▼";
  width: 25px;
  height: 20px;
  display: block;
  position: absolute;
  text-align: center;
  color: #444444;
  top: 18px;
  right: 5px;
  z-index: 1;
  font-size: 10px;
}
.mySelect {
  border: 0;
  background: #fff;
  outline: none;
  margin: 0;
  min-width: 410px;
  vertical-align: middle;
  position: relative;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .mySelect {
    min-width: 140px;
  }
}
select.form-control {
  width: 100% !important;
  border: 2px solid rgba(255, 226, 162, 0.21);
  margin-right: 10px;
  border-radius: 5px;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.48) !important;
  font-weight: normal !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  select.form-control {
    width: 120px;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  select.form-control {
    width: 140px;
  }
}
.filter {
  position: relative;
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(0, 0, 0, 0.44) !important;
  border-radius: 5px;
}
.filter:focus {
  outline: 0;
}
.filter i {
  color: rgba(0, 0, 0, 0.44);
}
.filter .badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: rgba(255, 226, 162, 0.21);
  border-radius: 50px;
}
.question {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.48);
  font-weight: bold;
}
.answers {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.48);
}
.faq {
  color: #9A309B;
  font-weight: normal;
  font-size: 12px;
}
.faq a {
  text-decoration: underline;
  color: #9A309B;
  font-weight: bold;
}
.filter-wrapper .row {
  justify-content: center;
}
@media only screen and (min-width: 1024px) and (max-height: 1366px) and (-webkit-min-device-pixel-ratio: 1.5) and (hover: none) {
  .filter-wrapper > .row {
    margin-top: 1rem;
    flex-wrap: nowrap;
  }
}
.filter-wrappers {
  display: flex;
}
.doubt-menu {
  position: sticky;
  position: -webkit-sticky;
  top: 105px;
  z-index: 99;
  background: #ffffff;
}
@media (min-width: 576px) and (max-width: 767px), (min-width: 768px) and (max-width: 991px), (min-width: 992px) and (max-width: 1199px) {
  .doubt-menu {
    top: 97px;
  }
}
@media (max-width: 575px) {
  .doubt-menu {
    top: 76px;
  }
}
@media (max-width: 480px) and (orientation: portrait) {
  .doubt-menu {
    top: 116px;
  }
}
@media (max-width: 376px) and (orientation: portrait) {
  .doubt-menu {
    top: 106px;
  }
}
@media (max-width: 350px) and (orientation: portrait) {
  .doubt-menu {
    top: 96px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .doubt-menu {
    box-shadow: 3px 5px 10px rgba(0, 0, 0, 0.1);
    border-radius: 0px 0px 10px 10px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .doubt-menu {
    width: 100%;
  }
}
.doubt-menu.reset-app {
  margin-right: 0 !important;
  margin-left: 0 !important;
  position: fixed;
  top: 0;
  width: 100%;
}
.doubt-menu.reset-app .app-back-btn {
  display: none !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .doubt-menu .nav-tabs {
    display: flex;
    justify-content: space-around;
  }
}
.doubt-menu .nav-tabs li a {
  padding: 0.2rem 1rem !important;
  border-right: 1px solid #ededed !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .doubt-menu .nav-tabs li a {
    padding: 0.2rem 0.6rem !important;
  }
}
.doubt-menu .nav-tabs li#myAnswer-tab a {
  border-right: 0 !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .doubt-menu .nav-tabs li#myAnswer-tab a {
    border-right: 1px solid #ededed !important;
  }
}
.doubt-menu .nav-tabs li:last-child a {
  border-right: 0;
}
.doubt-menu .nav-tabs li:last-child {
  margin-left: 6px;
}
.doubt-menu .nav-tabs li:last-child i {
  line-height: inherit;
}
.app-back-btn {
  margin-right: 10px;
  cursor: pointer;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .mobile-drop {
    margin-top: 50px;
  }
}
.mobile-drop.reset-app {
  margin-top: 140px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .mobile-drop.reset-app .discussion-card:first-child {
    margin-top: 160px !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .mobile-drop.reset-app .filter-wrapper {
    margin-top: 120px;
  }
}
.mobile-footer {
  background: radial-gradient(94.93% 433.83% at 1.6% 0%, #D13F7F 0%, #8129A7 100%);
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -webkit-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -moz-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px;
  height: 65px;
  position: fixed;
  bottom: 5px;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 98%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 9991;
}
.mobile-footer button.askdoubt-mobile {
  background: #ffffff;
  border: none;
  width: 230px;
  height: 40px;
  color: #9A309B;
  border-radius: 9px;
  font-weight: bold;
  font-size: 12px;
}
.mobile-footer span {
  font-size: 14px;
}
.mobile-footer span i {
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .mobile-footer {
    width: 98%;
  }
}
#alldoubts,
#mydoubts,
#myanswers {
  padding: 0;
}
.answer-cancel {
  border: 1.5px solid #dc3545;
  color: #dc3545;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-sizing: border-box;
  border-radius: 5px;
  height: 43px;
  margin-top: 1.5rem;
  font-weight: normal;
}
.answer-cancel i {
  color: #dc3545;
  font-size: 18px;
  margin-right: 5px;
}
.search {
  width: 300px;
  background: rgba(232, 232, 232, 0.21);
  border-radius: 5px;
  border: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .search {
    width: 100%;
  }
}
.search-btn {
  border: 1px solid #C13B87;
  border-radius: 50px;
  background: none;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
}
.search-btn i {
  color: #C13B87;
}
.search-btn:focus {
  outline: 0;
}
#doubt-search {
  display: none;
  align-content: center;
}
#image-preview img {
  max-height: 300px;
  max-width: 100%;
}
.close-img,
.close-preview {
  background: #ffffff !important;
  position: absolute;
  top: -7px;
  right: 0px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  border: none !important;
  border-radius: 50px !important;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
}
.close-img i,
.close-preview i {
  color: rgb(220 53 86.25);
  font-size: 18px;
}
.close-preview {
  margin-top: 0;
}
.close-preview i {
  margin-right: 0;
}
#tags-modal .modal-header {
  border: none;
}
#tags-modal .modal-body h4 {
  font-size: 14px;
}
#tags-modal .modal-footer {
  border: none;
  justify-content: center;
}
#tags-modal .modal-footer .skip {
  color: rgb(220 53 86.25);
  text-transform: uppercase;
}
.que-saved {
  font-size: 16px;
  font-weight: 500;
  color: #ffffff;
}
.tag-text {
  font-size: 12px;
  font-weight: normal;
  margin-top: 10px;
  color: #A73397;
}
#add-tags-btn {
  color: #A73397 !important;
  border: 1px solid #A73397 !important;
}
#add-tags-btn {
  text-transform: uppercase;
  color: #9A309B;
}
#backToDoubts {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}
#backToDoubts:focus {
  outline: 0;
}
#backToDoubts i {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
.btn-askQuestion {
  background: #9A309B !important;
  color: #ffffff !important;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  border: none !important;
  font-weight: normal;
  padding: 10px 3rem !important;
  margin: 0 auto;
}
.btn-askQuestion span {
  font-weight: 700;
}
#image-modal #image-modal-body p {
  text-align: center;
}
#image-modal .modal-footer {
  padding: 10px;
}
#answer-successModal.postmodals .modal-header,
#image-modal.postmodals .modal-header,
#question-successModal.postmodals .modal-header {
  display: none;
}
#answer-successModal.postmodals .modal-content,
#image-modal.postmodals .modal-content,
#question-successModal.postmodals .modal-content {
  border: none;
}
#answer-successModal.postmodals .modal-footer,
#image-modal.postmodals .modal-footer,
#question-successModal.postmodals .modal-footer {
  display: flex;
  justify-content: center;
  border-top: none;
}
#answer-successModal.postmodals h4,
#image-modal.postmodals h4,
#question-successModal.postmodals h4 {
  font-size: 14px;
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  margin-top: 1rem;
  font-weight: normal;
  text-align: center;
}
#answer-successModal.postmodals p,
#image-modal.postmodals p,
#question-successModal.postmodals p {
  font-size: 10px;
  color: #949494;
  text-align: center;
}
.btn-flashcard {
  background: #9A309B;
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  #pagination-div {
    margin-bottom: 5rem;
  }
}
.row:before,
.row:after {
  display: flex !important;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder {
  background: none !important;
  border: none;
  color: #495057;
}
.bootstrap-select {
  margin-right: 10px;
}
.bootstrap-select .dropup .dropdown-toggle::after {
  position: relative !important;
  top: 3px !important;
}
.bootstrap-select .dropdown-toggle::after {
  position: relative !important;
  top: 3px !important;
}
.bootstrap-select .dropdown-toggle .filter-option {
  top: 3px !important;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder,
.bootstrap-select > .dropdown-toggle.bs-placeholder:hover,
.bootstrap-select > .dropdown-toggle.bs-placeholder:focus,
.bootstrap-select > .dropdown-toggle.bs-placeholder:active {
  color: #495057 !important;
  font-family: 'Poppins', sans-serif !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bootstrap-select .dropdown-menu {
    max-width: 300px !important;
    min-width: 300px !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #collapseFilter {
    background: #ffffff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    border-radius: 0px 0px 10px 10px;
    padding-bottom: 1rem;
  }
}
.filter-option {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.card-actions .dropleft .dropdown-toggle::before {
  display: none;
}
#queSave-modal .bootstrap-select {
  margin-top: 1.5rem !important;
}
.cke_1 {
  border: none;
}
.answeredby {
  font-size: 12px;
  color: #A73397;
  text-transform: capitalize;
}
.answeredby span {
  padding-right: 5px;
  color: rgba(68, 68, 68, 0.48);
  font-style: italic;
  font-weight: normal;
  text-transform: none;
}
.btn-showMore {
  background: #9A309B;
  color: #ffffff;
  color: #ffffff !important;
}
.content > p {
  margin-top: 0.5rem;
}
.content > p:first-child {
  color: #A73397;
}
.content > p:nth-child(2) {
  color: #A73397;
}
.content > p:nth-child(3) {
  color: #A73397;
}
#mobile-filter i {
  color: rgba(68, 68, 68, 0.48);
  font-size: 20px;
}
#tutorial .modal-dialog {
  height: 100%;
  margin: 0;
}
#tutorial .modal-dialog .modal-content {
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  border: none;
  border-radius: 0;
}
#tutorial .modal-dialog .modal-content h2 {
  font-size: 36px;
  font-weight: bold;
}
#tutorial .modal-dialog .modal-content p {
  font-size: 12px;
}
#tutorial .modal-dialog .modal-content p i {
  font-size: 16px;
  position: relative;
  top: 4px;
}
#tutorial .modal-header {
  border: none;
  display: flex;
  align-items: center;
}
#tutorial .modal-header .close {
  text-shadow: none;
  color: #ffffff;
  opacity: 1;
}
#tutorial .modal-header .btn-next {
  border: 1px solid #ffffff;
  color: #ffffff;
  border-radius: 4px;
  background: none;
  font-size: 9px;
}
#tutorial .modal-footer {
  border: none;
}
.carousel-item h2,
.carousel-item p {
  color: #ffffff;
}
.carousel-item.tutor .circle {
  width: 26.25px;
  height: 26.25px;
  border-radius: 50px;
  border: 1px solid #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.carousel-item.tutor .circle i {
  color: #ffffff;
  font-size: 16px;
}
.carousel-item.tutor .btn-answer {
  background: #FFFFFF;
  border: 1.25px solid #A73397;
  box-sizing: border-box;
  border-radius: 5px;
  color: #A73397;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.carousel-item.tutor .btn-answer i {
  margin-right: 10px;
}
.carousel-item.tutor h2 {
  font-size: 36px;
  margin-bottom: 1rem;
}
.carousel-item.tutor p {
  font-size: 14px;
  color: #ffffff;
}
.carousel-item.tutor .wonderwonk {
  color: #ffffff;
  font-size: 12px;
}
.carousel-item.tutor #grade-rating {
  display: flex;
  margin-right: 5px;
}
.carousel-item.tutor #grade-rating i {
  background: -webkit-linear-gradient(#FFB002, #FF7B02);
  background: -moz-linear-gradient(#FFB002, #FF7B02);
  background: -webkit-gradient(#FFB002, #FF7B02);
  background: -o-linear-gradient(#FFB002, #FF7B02);
  background: -ms-linear-gradient(#FFB002, #FF7B02);
  background: linear-gradient(#FFB002, #FF7B02);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
.indicator-override {
  position: static;
  margin-bottom: 0;
}
.indicator-override li {
  width: 14px;
  height: 8px;
  border-radius: 50%;
  background: #8A8A8A;
}
.indicator-override li.active {
  background: #ffffff;
}
