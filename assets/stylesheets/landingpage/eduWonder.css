body {
  overflow-x: hidden;
}
::-webkit-scrollbar {
  display: none;
}
.digital-library {
  overflow-x: visible;
}
.main-section {
  background: url("../../images/wslibrary/bgOrange.svg");
}
.edw-header {
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 9999;
}
.edw-header .logo-img {
  width: 240px;
}
@media screen and (max-width: 768px) {
  .edw-header .logo-img {
    width: 200px;
  }
}
.edw-banner__header {
  color: #fff;
}
@media screen and (max-width: 768px) {
  .banner-img {
    width: 340px;
  }
}
.edw-form__section {
  background: #f4f4f4;
  padding: 2.2rem;
  width: 470px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
@media screen and (max-width: 768px) {
  .edw-form__section {
    width: auto;
  }
}
.edw-form__section h5 {
  font-size: 18px;
  font-weight: 700;
  color: #202124;
  line-height: 24px;
  width: 100%;
  margin-top: 0;
  margin-bottom: 15px;
}
input.form-control:focus {
  border-color: #333 !important;
}
.btn-join {
  background: #F58D20;
  color: #fff;
}
.edw-descriptive__banners {
  min-height: 400px;
}
.edw-descriptive__banners .edw-banner-wrapper .banner-content__wrapper {
  width: 370px;
}
@media screen and (max-width: 768px) {
  .edw-descriptive__banners .edw-banner-wrapper .banner-content__wrapper {
    width: auto;
  }
}
@media screen and (max-width: 768px) {
  .edw-descriptive__banners .edw-banner-wrapper {
    flex-direction: column;
  }
}
.edw-descriptive__banners-header {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 3rem 0 0 1rem;
  align-items: center;
}
@media screen and (max-width: 768px) {
  .edw-descriptive__banners-header {
    justify-content: flex-start;
  }
}
.edw-descriptive__banners-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
}
.edw-descriptive__banners .edw-all__banners {
  height: 400px;
}
.edw-descriptive__banners .edw-all__banners img {
  width: 400px;
}
@media screen and (max-width: 768px) {
  .edw-descriptive__banners .edw-all__banners img {
    width: 100%;
  }
}
.odd {
  background: url("../../images/wslibrary/bgOrange.svg");
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 10px #0000001a;
}
.odd h2,
.odd p {
  color: #fff;
}
.even {
  background: #fff;
  background: url("../../images/eduwonder/drawer_bg.png");
  box-shadow: 0 4px 10px #0000001a;
  border-radius: 10px;
  padding: 2rem;
}
.digital-library .price-layer .card.pink-bg {
  background: url("../../images/wslibrary/bgOrange.svg");
}
.brands h2 {
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
  padding: 3rem 0 0 3rem;
}
.brands .brandlogos {
  display: grid;
  grid-gap: 2rem;
  grid-template-columns: repeat(5, 1fr);
  align-items: center;
}
@media screen and (max-width: 768px) {
  .brands .brandlogos {
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 1.5rem;
    display: none;
  }
}
@media screen and (max-width: 900px) {
  .brands .brandlogos {
    display: none;
  }
}
.brands .brandlogos img {
  width: 200px;
  display: flex;
  margin: 0 auto;
}
.brandList-mob {
  display: none;
  width: 100%;
  padding: 2rem;
}
.brandList-mob img {
  width: 100%;
}
@media screen and (max-width: 768px) {
  .brandList-mob {
    display: block;
  }
}
@media screen and (max-width: 900px) {
  .brandList-mob {
    display: block;
  }
}
.applogos {
  margin: 0 auto;
}
.mobile-footer-nav {
  display: none !important;
}
.brandsList {
  width: 100%;
}
.brandsList img {
  width: 100%;
}
.title-name {
  position: relative;
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
}
.title-name:before {
  content: "";
  position: absolute;
  background: #F58D20;
  height: 3px;
  width: 100px;
  bottom: -10px;
  margin-left: 50px;
}
.title-name-1 {
  position: relative;
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
}
.title-name-1:before {
  content: "";
  position: absolute;
  background: #F58D20;
  height: 3px;
  width: 60px;
  bottom: -10px;
  margin-left: 20px;
}
.btn {
  box-shadow: none!important;
}
.form-control:focus {
  box-shadow: none;
  border-color: #F58D20;
}
.digital-library-footer {
  min-height: 180px !important;
}
.contactus {
  font-size: 1.2rem !important;
  text-align: start !important;
}
.mobile-no {
  font-size: 1.1rem !important;
}
