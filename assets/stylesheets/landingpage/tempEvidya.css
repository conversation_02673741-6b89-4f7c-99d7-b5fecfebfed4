/*Wonderslate color themes*/
/* NOTES:-> When we are working on utkarsh comment above color themes and use below themes*/
/*eUtkarsh color themes*/
/*Home page*/
/*evidya*/

@import url('https://fonts.googleapis.com/css2?family=Rubik:wght@400;500&display=swap');

.utkarsh.sticky-header {
    height: 100px;
}
.utkarsh .wonderslate-navbar {
  background: #233982;
    height: 100px;
    padding: 20px 25px;
}
.utkarsh .wonderslate-navbar .navbar-brand {
  background: url('../../images/evidya/logo.png') center no-repeat;
  width: 330px;
  background-size: contain;
  background-color: #ffffff;
  padding: 1rem;
  border-top-right-radius: 50px;
  border-bottom-right-radius: 50px;
  display: flex;
  align-items: center;
  height: 60px;
  position: relative;
  left: -1rem;
}
.utkarsh .wonderslate-navbar .navbar-container .navbar-nav.header-menus {
    margin-left: 0;
    margin-top: 10px;
}
.nav .open > a, .nav .open > a:hover, .nav .open > a:focus{
  background: none;
}
.navbar-nav > li > .dropdown-menu {
    /*width: 100%;*/
    border-radius: .5rem !important;
    min-width: 200px;
}
.navbar-nav > li > .dropdown-menu li a{
    color: #000000;
    font-size: 14px;
    padding: .5rem 1.5rem;
}
.navbar-nav > li > .dropdown-menu li a:hover{
  color:#000000;
    background: #f5f5f5;
}
.dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent;
}
.utkarsh .wonderslate-navbar ul a {
  color: #ffffff;
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    padding-right: 2rem;
    padding-left: 2rem;
    padding-bottom: 10px;
}
.utkarsh .wonderslate-navbar ul a:hover,
.utkarsh .wonderslate-navbar ul a:focus {
  color: #ffffff;
  background-color: transparent;
}
.utkarsh .wonderslate-navbar .navbar-container .login-btn {
  color: #ffffff !important;
}
.utkarsh .wonderslate-navbar .navbar-container .signup-btn {
  background: none;
}
.utkarsh .user-logged-in {
  background: transparent !important;
}
.utkarsh .user-orders a {
  color: #444444 !important;
}
.utkarsh .user-logout p a {
  color: #233982 !important;
}
.eutkarsh .video-btn > a,
.eutkarsh .link-btn > a,
.eutkarsh .add-notesPr > a {
  background: #F79420;
}
.eutkarsh .custom-prBtn,
.eutkarsh .custom-prBtn-bl {
  color: #F79420;
}
.eutkarsh .nav .open > a,
.eutkarsh .nav .open > a:hover,
.eutkarsh .nav .open > a:focus {
  background-color: transparent;
}
.eutkarsh .dropdown-menu li a {
  color: #444444;
}
.eutkarsh .dropdown-menu li a:hover {
  color: #444444;
}
.eutkarsh #addNotes {
  margin-top: 0;
}
.eutkarsh .book-read-material {
  padding-bottom: 8rem;
}
@media screen and (min-width: 320px) and (max-width: 767px) {
  .eutkarsh .download-app-btn {
    max-width: 200px;
    height: auto;
  }
  .eutkarsh .mobile-store {
    width: 28px;
    height: 28px;
    background: url("../../images/eutkarsh/utkarsh-inactive.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .eutkarsh .mobile-store.active {
    background: url("../../images/eutkarsh/utkarsh-active.svg") !important;
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
  }
}
.flex_st {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.flex_st .form-control {
  width: auto;
  margin: 5px;
}
.adminForm .form-control {
  width: 33.33%;
}
.adminForm a {
  color: #F79420;
  padding: 10px;
}
.dflex {
  display: flex;
}
.dflex .form-group {
  width: 33.33%;
}
.dflex .form-group .form-control {
  width: 90%;
}
.dflex .form-group button {
  margin-top: 1rem;
}
.dflex_pub {
  display: flex;
  align-items: center;
}
.dflex_pub .form-control {
  width: 40%;
  margin: 5px;
}
.publish-management a {
  color: #F79420;
  padding: 10px;
}
.evidya .export-study-set{
  display: none;
}

.navbar-nav > li > .dropdown-menu.profile-dropdown {
    min-width: 450px;
    padding: 15px;
    top: 50px;
}
.profile-dropdown .user-logout {
    border-top: 1px solid #eee;
    padding-top: 15px;
}
.profile-dropdown .user-logout p {
    font-family: 'Rubik', sans-serif;
    font-size: 15px;
}
.profile-dropdown .user-logout p a {
    font-size: 15px;
    padding: 0;
}
.profile-dropdown .logged-in-user-details {
    max-width: inherit;
}
.utkarsh .navbar-right {
    background: #ffffff;
    padding: 1rem;
    border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;
    position: relative;
    right: -26px;
    padding-right: 4rem;
    padding-left: 3rem;
}
.utkarsh .wonderslate-navbar ul a.browse-link {
    color: #000000;
}
.navbar-nav > li > .dropdown-menu.browseMenu {
    min-width: 450px;
    padding: 15px;
    top: 50px;
}
.browseMenu h5 {
    color: #000;
    font-size: 14px;
    font-weight: bold;
}
.browseMenu:before {
    content: "";
    border-bottom: 10px solid #fff;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    position: absolute;
    top: -10px;
    right: 50px;
    z-index: 10;
}
.browseMenu:after {
    content: "";
    border-bottom: 12px solid #ccc;
    border-right: 12px solid transparent;
    border-left: 12px solid transparent;
    position: absolute;
    top: -12px;
    right: 48px;
    z-index: 9;
}
.browseMenu .browse-icons {
    margin-top: 25px;
}
.browseMenu .browse-icons .circle1 {
    border-radius: 50px;
    width: 28px;
    height: 28px;
    border: 1px solid #233982;
    float: left;
}
.browseMenu .browse-icons a {
    font-size: 14px !important;
    padding: 0 10px !important;
    color: #1F419B !important;
    position: relative;
    top: 5px;
}
.browseMenu .language {
    padding: 0;
    margin-left: 26px;
}
.browseMenu .language li {
    list-style-type: disc;
    padding-top: 1rem;
    margin-right: 0 !important;
}
.browseMenu .language li a {
    color: #000 !important;
    padding: 0 !important;
}
.browseMenu .language li a:hover {
    background-color: transparent !important;
}
@media only screen and (max-width: 575px) {
    .user-orders {
        display: none;
    }
    .evidya .mobile-header-icon.open .mobile-user {
        background: #fff !important;
    }
    .evidya .mobile-user::after {
        display: none;
    }
}
@media only screen and (max-width: 575px) {
    .navbar-nav > li > .dropdown-menu.profile-dropdown {
        min-width: 300px;
    }
    .user-image{
        margin-bottom: 15px;
    }
}
