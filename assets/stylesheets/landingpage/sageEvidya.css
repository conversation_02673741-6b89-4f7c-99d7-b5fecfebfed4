/*common*/
/*!
 * Bootstrap Reboot v4.1.2 (https://getbootstrap.com/)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)
 */
*,
*::before,
*::after {
    box-sizing: border-box;
}
html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
@-ms-viewport {
    width: device-width;
}
article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
    display: block;
}
body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: left;
    background-color: #fff;
}
[tabindex="-1"]:focus {
    outline: 0 !important;
}
hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
    margin-bottom: 0.5rem;
}
p {
    margin-top: 0;
    margin-bottom: 1rem;
}
abbr[title],
abbr[data-original-title] {
    text-decoration: underline;
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
    cursor: help;
    border-bottom: 0;
}
address {
    margin-bottom: 1rem;
    font-style: normal;
    line-height: inherit;
}
ol,
ul,
dl {
    margin-top: 0;
    margin-bottom: 1rem;
}
ol ol,
ul ul,
ol ul,
ul ol {
    margin-bottom: 0;
}
dt {
    font-weight: 700;
}
dd {
    margin-bottom: 0.5rem;
    margin-left: 0;
}
blockquote {
    margin: 0 0 1rem;
}
dfn {
    font-style: italic;
}
b,
strong {
    font-weight: bolder;
}
small {
    font-size: 80%;
}
sub,
sup {
    position: relative;
    font-size: 75%;
    line-height: 0;
    vertical-align: baseline;
}
sub {
    bottom: -0.25em;
}
sup {
    top: -0.5em;
}
a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
    -webkit-text-decoration-skip: objects;
}
a:hover {
    color: #0056b3;
    text-decoration: underline;
}
a:not([href]):not([tabindex]) {
    color: inherit;
    text-decoration: none;
}
a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
    color: inherit;
    text-decoration: none;
}
a:not([href]):not([tabindex]):focus {
    outline: 0;
}
pre,
code,
kbd,
samp {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 1em;
}
pre {
    margin-top: 0;
    margin-bottom: 1rem;
    overflow: auto;
    -ms-overflow-style: scrollbar;
}
figure {
    margin: 0 0 1rem;
}
img {
    vertical-align: middle;
    border-style: none;
}
svg:not(:root) {
    overflow: hidden;
    vertical-align: middle;
}
table {
    border-collapse: collapse;
}
caption {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    color: #6c757d;
    text-align: left;
    caption-side: bottom;
}
th {
    text-align: inherit;
}
label {
    display: inline-block;
    margin-bottom: 0.5rem;
}
button {
    border-radius: 0;
}
button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color;
}
input,
button,
select,
optgroup,
textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}
button,
input {
    overflow: visible;
}
button,
select {
    text-transform: none;
}
button,
html [type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
    padding: 0;
    border-style: none;
}
input[type="radio"],
input[type="checkbox"] {
    box-sizing: border-box;
    padding: 0;
}
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
    -webkit-appearance: listbox;
}
textarea {
    overflow: auto;
    resize: vertical;
}
fieldset {
    min-width: 0;
    padding: 0;
    margin: 0;
    border: 0;
}
legend {
    display: block;
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    line-height: inherit;
    color: inherit;
    white-space: normal;
}
progress {
    vertical-align: baseline;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    height: auto;
}
[type="search"] {
    outline-offset: -2px;
    -webkit-appearance: none;
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}
::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button;
}
output {
    display: inline-block;
}
summary {
    display: list-item;
    cursor: pointer;
}
template {
    display: none;
}
[hidden] {
    display: none !important;
}
/*Wonderslate color themes*/
/* NOTES:-> When we are working on utkarsh comment above color themes and use below themes*/
/*eUtkarsh color themes*/
/*Home page*/
/*evidya*/
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}
html {
    background: #ffffff;
    font-family: 'Rubik', sans-serif;
    color: #ffffff !important;
}
::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: rgba(68, 68, 68, 0.3);
    opacity: 1;
    /* Firefox */
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
}
:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: rgba(68, 68, 68, 0.3);
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
}
::-ms-input-placeholder {
    /* Microsoft Edge */
    color: rgba(68, 68, 68, 0.3);
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
}
.owl-dots,
.owl-prev {
    display: none;
}
.owl-next {
    background-color: #ffffff !important;
    width: 40px;
    height: 80px;
    right: -1px;
    top: 25%;
    border-radius: 4px 0 0 4px;
    position: absolute;
    -webkit-box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.25);
    outline: 0;
}
.owl-next:focus {
    outline: 0;
}
.owl-next span {
    color: rgba(68, 68, 68, 0.7);
    font-size: 32px;
}
.modal {
    z-index: 99999;
}
.modal-open {
    overflow: auto;
}
input[type='text'] {
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: 'Rubik', sans-serif;
}
.material-icons-new {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-size: contain;
}
.icon-white {
    webkit-filter: contrast(4) invert(1);
    -moz-filter: contrast(4) invert(1);
    -o-filter: contrast(4) invert(1);
    -ms-filter: contrast(4) invert(1);
    filter: contrast(4) invert(1);
}
.custom-select-ws {
    position: relative;
    font-family: Arial;
}
.custom-select-ws select {
    display: none;
    /*hide original SELECT element:*/
}
.select-selected {
    background-color: #ffffff;
    color: #444444;
    padding: 8px 2px;
    border: none;
    border-bottom: 1px solid rgba(1, 1, 1, 0.12);
    cursor: pointer;
    user-select: none;
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
    font-weight: 500;
}
.select-selected:after {
    position: absolute;
    content: "";
    top: 16px;
    right: 10px;
    width: 0;
    height: 0;
    border: 6px solid transparent;
    border-color: rgba(1, 1, 1, 0.5) transparent transparent transparent;
}
.select-selected.select-arrow-active:after {
    border-color: transparent transparent rgba(1, 1, 1, 0.5) transparent;
    top: 10px;
}
.select-items {
    position: absolute;
    background-color: #ffffff;
    top: 125%;
    left: 0;
    right: 0;
    z-index: 99;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    border-radius: 6px;
}
.select-items div {
    color: #444444;
    padding: 8px 16px;
    cursor: pointer;
    user-select: none;
}
.select-items div:first-child {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
}
.select-items div:last-child {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
}
.select-items div:hover {
    background-color: rgba(0, 0, 0, 0.1);
}
/*hide the items when the select box is closed:*/
.select-hide {
    display: none;
}
.same-as-selected {
    background-color: transparent;
}
.btn {
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    font-size: 14px;
}
.btn:focus,
.btn:active {
    outline: none !important;
    box-shadow: none;
}
.mobile-bottom-menu-wrapper {
    display: none;
}
p,
a,
button {
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
}
.email-error p {
    max-width: 100%;
}
.modal-open {
    padding-right: 0 !important;
}
.nav-link {
    color: #444444;
    font-family: 'Rubik', sans-serif;
}
.owl-item a:hover {
    text-decoration: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    #drift-widget-container iframe {
        bottom: 4rem !important;
    }
}
.hidden {
    display: none;
}
.loader {
    margin: 5% auto 30px;
}
.loader.book {
    border: 4px solid #2EBAC6;
    width: 60px;
    height: 45px;
    position: relative;
    perspective: 150px;
    background: #1F419B;
}
.loader.book .page {
    display: block;
    width: 30px;
    height: 45px;
    border: 4px solid #2EBAC6;
    border-left: 1px solid #1F419B;
    margin: 0;
    position: absolute;
    right: -4px;
    top: -4px;
    overflow: hidden;
    background: #1F419B;
    transform-style: preserve-3d;
    -webkit-transform-origin: left center;
    transform-origin: left center;
}
.book .page:nth-child(1) {
    -webkit-animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1.6s infinite;
    animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1.6s infinite;
}
.book .page:nth-child(2) {
    -webkit-animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1.45s infinite;
    animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1.45s infinite;
}
.book .page:nth-child(3) {
    -webkit-animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1.2s infinite;
    animation: pageTurn 1.2s cubic-bezier(0, 0.39, 1, 0.68) 1.2s infinite;
}
/* Page turn */
@-webkit-keyframes pageTurn {
    0% {
        -webkit-transform: rotateY(0deg);
        transform: rotateY(0deg);
    }
    20% {
        background: #1F419B;
    }
    40% {
        background: #1F419B;
        -webkit-transform: rotateY(-180deg);
        transform: rotateY(-180deg);
    }
    100% {
        background: #1F419B;
        -webkit-transform: rotateY(-180deg);
        transform: rotateY(-180deg);
    }
}
@keyframes pageTurn {
    0% {
        transform: rotateY(0deg);
    }
    20% {
        background: #1F419B;
    }
    40% {
        background: #1F419B;
        transform: rotateY(-180deg);
    }
    100% {
        background: #1F419B;
        transform: rotateY(-180deg);
    }
}
/* Dots */
@-webkit-keyframes Dots {
    0% {
        content: "";
    }
    33% {
        content: ".";
    }
    66% {
        content: "..";
    }
    100% {
        content: "...";
    }
}
@keyframes Dots {
    0% {
        content: "";
    }
    33% {
        content: ".";
    }
    66% {
        content: "..";
    }
    100% {
        content: "...";
    }
}
.loading-icon {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    background: rgba(68, 68, 68, 0.9);
    z-index: 999;
    overflow: hidden;
}
.load-wrapper {
    position: absolute;
    top: 35%;
    width: 100%;
}
#tab-head {
    white-space: nowrap;
}
/*a:focus {*/
    /*background: transparent !important;*/
/*}*/
.slick-prev {
    display: none;
}
.slick-initialized .slick-slide a:hover {
    text-decoration: none;
}
.slick-next {
    background: #ffffff;
    width: 40px;
    height: 80px;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    right: 0;
}
.slick-next:before {
    color: rgba(68, 68, 68, 0.7);
    font-family: 'Rubik', sans-serif;
    content: url("../../images/landingpageImages/right-arrow.svg");
    width: 7px;
    height: 12px;
}
.slick-next:hover,
.slick-next :focus {
    opacity: 1;
    background: #ffffff;
}
input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 30px white inset;
}
.input-error-tooltip-inner,
.email-error-text {
    color: #B72319 !important;
    font-size: 12px !important;
    max-width: 100% !important;
    padding: 0;
    margin: 0;
}
#divi3 {
    display: none;
}
.utkarsh .logo {
    width: 90px;
    height: 60px;
}
@media (min-width: 576px) {
    .filter.d-sm-blocks {
        display: block;
    }
    .filter.d-nones {
        display: none ;
    }
}
header {
    width: 100%;
    z-index: 999;
    background: #ffffff;
    top: 0;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.2), 0 0 rgba(0, 0, 0, 0.1);
}
.ws-header {
    height: 64px;
    background: #ffffff;
}
.ws-header div.mobile-profile {
    display: flex;
    position: absolute;
    z-index: 9999;
    right: 10px;
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .ws-header div.mobile-profile {
        display: block !important;
    }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .ws-header div.mobile-profile .dropdown-menu-right {
        right: -10px;
    }
    .ws-header div.mobile-profile .dropdown-menu-right .media {
        padding-bottom: 0 !important;
        position: relative;
    }
    .ws-header div.mobile-profile .dropdown-menu-right .media .drop-profile {
        width: 42px;
        height: 42px;
    }
    .ws-header div.mobile-profile .dropdown-menu-right .media .edit-btn {
        position: absolute;
        bottom: 2rem;
        left: 3rem;
        width: 15px;
        height: 15px;
        display: inline-block;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.94);
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        text-align: center;
    }
    .ws-header div.mobile-profile .dropdown-menu-right .media .edit-btn i {
        font-size: 9px;
        position: relative;
        top: 1px;
        color: rgba(68, 68, 68, 0.75);
    }
    .ws-header div.mobile-profile .dropdown-menu-right .dropdown-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #EDEDED;
        color: #444444;
        font-weight: normal;
    }
    .ws-header div.mobile-profile .dropdown-menu-right .dropdown-item:first-child {
        border-top: 1px solid #EDEDED;
    }
    .ws-header div.mobile-profile .dropdown-menu-right .dropdown-item:last-child {
        border-bottom: none;
    }
}
.ws-header div.mobile-profile .nav-item {
    list-style-type: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .ws-header div.mobile-profile .nav-item a {
        font-size: 10px;
        padding: 0;
    }
}
.ws-header div.mobile-profile .nav-item a img {
    width: 24px;
    height: 24px;
}
.ws-header .navbar-nav .nav-item.active {
    background-color: #ffffff;
}
.ws-header .navbar-nav .nav-link {
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    padding-right: 1.5rem;
    padding-left: 1.5rem;
    cursor: pointer;
}
.ws-header .navbar-nav .nav-link:hover {
    color: #1F419B;
    transition: all 0.5s;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .ws-header .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
        font-size: 14px;
    }
}
.ws-header .navbar-nav.right-menu {
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
}
.ws-header .navbar-nav.right-menu li.notification {
    width: 24px;
    height: 24px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(68, 68, 68, 0.54);
}
.ws-header .navbar-nav.right-menu li.notification a {
    padding: 0;
    text-align: center;
    cursor: pointer;
}
.ws-header .navbar-nav.right-menu li.notification i {
    color: #ffffff;
    font-size: 14px;
    margin-top: 5px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu {
    min-width: 330px;
    padding-bottom: 0;
    margin-top: -5px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media {
    border-bottom: 1px solid #EDEDED;
    position: relative;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media .media-body p {
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #EDEDED;
    color: #444444;
    font-weight: normal;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item span {
    color: #1F419B;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item:last-child {
    border-bottom: none;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu a {
    position: relative;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn {
    width: 20px;
    height: 20px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    position: absolute;
    left: 70px;
    bottom: 30px;
    text-align: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn {
        right: 80px;
    }
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn i {
    color: rgba(68, 68, 68, 0.74);
    font-size: 12px;
}
.ws-header .navbar-nav.right-menu li a {
    font-weight: 500;
}
.ws-header .navbar-nav.right-menu li a img {
    width: 46px;
    height: 46px;
}
.ws-header .navbar-nav.right-menu li img.drop-profile {
    width: 72px;
    height: 72px;
}
.ws-header .navbar-nav.right-menu li:first-child a {
    color: #1F419B;
}
.ws-header .navbar-nav.right-menu li:last-child a {
    color: #2EBAC6;
    cursor: pointer;
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .ws-header .navbar-nav.mr-auto,
    .ws-header .navbar-nav.right-menu {
        display: none !important;
    }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .ws-header {
        border-bottom: 1px solid rgba(68, 68, 68, 0.1);
    }
}
.main-menu {
    height: 48px;
    background: #ffffff;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.2), 0 0 rgba(0, 0, 0, 0.1);
}
.main-menu .navbar-nav .nav-item:active {
    background: transparent;
}
.main-menu .navbar-nav .nav-item:first-child .nav-link {
    border-right: 1px solid rgba(68, 68, 68, 0.2);
}
.main-menu .navbar-nav .nav-item:last-child .nav-link {
    border-left: 1px solid rgba(68, 68, 68, 0.2);
}
.main-menu .navbar-nav .nav-link {
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
    padding-right: 1.5rem;
    padding-left: 1.5rem;
}
.main-menu .navbar-nav .nav-link:hover {
    color: #1F419B;
    transition: all 0.5s;
}
.main-menu .navbar-nav .nav-link:hover:after {
    color: #1F419B;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .main-menu .navbar-nav .nav-link {
        padding-right: 0.5rem;
        padding-left: 0.5rem;
    }
}
.main-menu .dropdown-toggle::after {
    color: rgba(68, 68, 68, 0.8);
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .main-menu {
        display: none !important;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .mobile-toggle {
        display: flex !important;
        justify-content: flex-end;
    }
    .sidenav {
        display: block !important;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .sidenav .closebtn {
        right: 10% !important;
    }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : landscape), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .logo {
        position: relative;
        z-index: 9999;
    }
    .sidenav {
        height: 100vh;
        width: 0;
        position: fixed;
        z-index: 999;
        top: 0;
        right: 0;
        background-color: #ffffff;
        overflow-x: hidden;
        transition: 0.5s;
        padding: 0;
        border: 1px solid #EDEDED;
    }
    .sidenav > div {
        margin-top: 4rem;
    }
    .sidenav > div li {
        text-align: center;
    }
    .sidenav a {
        padding: 8px 8px 8px 32px;
        text-decoration: none;
        font-size: 14px;
        display: block;
        transition: 0.3s;
    }
    .sidenav a i {
        color: #444444;
    }
    .sidenav .closebtn {
        position: absolute;
        top: 0;
        right: 8px;
        font-size: 30px;
        margin-left: 50px;
    }
}
.ws-mob-menu {
    display: none;
}
.ws-mob-menu > li > a {
    color: #2EBAC6;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    padding-bottom: 1rem;
}
.ws-mob-menu .sub-menu {
    display: none;
}
.ws-mob-menu #mobile-menu {
    padding: 0;
    border-top: 2px solid #EDEDED;
}
.ws-mob-menu #mobile-menu > li {
    list-style-type: none;
    padding: 8px 0px;
    text-align: left;
    background: #ffffff;
    border-bottom: 1px solid #EDEDED;
}
.ws-mob-menu #mobile-menu > li.toggle-icon > a:after {
    content: url('../../images/landingpageImages/arrow-down.svg');
}
.ws-mob-menu #mobile-menu > li.toggle-icon.default-icon > a:after {
    content: url('../../images/landingpageImages/arrow-up.svg');
}
.ws-mob-menu #mobile-menu > li > a {
    color: #444444;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    position: relative;
}
.ws-mob-menu #mobile-menu > li > a:after {
    content: url('../../images/landingpageImages/arrow-down.svg');
    float: right;
    position: absolute;
    top: 0;
    right: 0;
    padding: 8px 16px;
    height: 100%;
    border-left: 1px solid #EDEDED;
}
.ws-mob-menu #mobile-menu > li:last-child {
    border-bottom: 1px solid #EDEDED;
}
.ws-mob-menu #mobile-menu > li ul {
    padding: 0;
}
.ws-mob-menu #mobile-menu > li ul li {
    list-style-type: none;
    border-bottom: 1px solid #EDEDED;
    margin-left: 2rem;
    padding: 6px 0px;
}
.ws-mob-menu #mobile-menu > li ul li:last-child {
    border: none;
}
.ws-mob-menu #mobile-menu > li ul li a {
    color: #444444;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    text-align: left;
}
/*Temp hide*/
.notification {
    display: none !important;
}
.user_profile {
    margin-top: 70px;
    background: #FAFAFA;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .user_profile {
        margin-top: 65px;
    }
}
.user_profile #profile-menu {
    margin-top: 2rem;
}
.user_profile #profile-menu.nav-tabs {
    border-bottom: none;
}
.user_profile #profile-menu.nav-tabs .nav-item:last-child {
    margin-left: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .user_profile #profile-menu.nav-tabs .nav-item:last-child {
        margin-left: 0;
    }
}
.user_profile #profile-menu.nav-tabs .nav-item.show .nav-link {
    border: none;
    background: none;
}
.user_profile #profile-menu.nav-tabs .nav-link {
    cursor: pointer;
    font-size: 32px;
    font-weight: 500;
    color: rgba(68, 68, 68, 0.7);
    font-family: 'Merriweather', serif;
}
.user_profile #profile-menu.nav-tabs .nav-link.active {
    border: none;
    background: none;
    border-bottom: 3px solid #1F419B;
    color: #444444;
}
.user_profile #profile-menu.nav-tabs .nav-link:hover {
    border: none;
    border-bottom: 3px solid #1F419B;
}
.user_profile .tab-content {
    margin-top: 2rem;
}
.user_profile .tab-content .jumbotron {
    background: #ffffff;
    border: 1px solid #EDEDED;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .user_profile .tab-content .jumbotron {
        margin-top: 3rem;
        padding-bottom: 4rem;
    }
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper {
    position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .user_profile .tab-content .jumbotron form .media .profile-wrapper {
        text-align: center;
    }
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-img {
    width: 190px;
    height: 190px;
    box-shadow: 0px 0px 4px 4px #EDEDED;
    border: 7px solid #ffffff;
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-btn {
    width: 20px;
    height: 20px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    position: absolute;
    right: 25px;
    bottom: 30px;
    text-align: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-btn {
        right: 80px;
    }
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-btn i {
    color: rgba(68, 68, 68, 0.74);
    font-size: 12px;
}
.user_profile .tab-content .jumbotron form .media .continue {
    width: 312px;
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color: #ffffff;
    text-transform: uppercase;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    float: right;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .user_profile .tab-content .jumbotron form .media .continue {
        width: 100%;
    }
}
.user_profile .tab-content .selectbox select {
    width: 100%;
}
.input-login {
    overflow: hidden;
    position: relative;
    z-index: 1;
    display: inline-block;
    margin: 0.3rem;
    width: 100%;
}
.input-login input {
    border: none;
    outline: 0;
    margin-top: 1rem;
    padding: 0.85em 0.15em;
    width: 100%;
    background: transparent;
    color: #595F6E;
}
.input-login input:focus + label::after {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
.input-login input:focus + label > span {
    -webkit-animation: anim-1 0.3s forwards;
    animation: anim-1 0.3s forwards;
}
.input-login label {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 0 0.25em;
    width: 100%;
    height: calc(100% - 1em);
    text-align: left;
    pointer-events: none;
}
.input-login label > span {
    position: absolute;
}
.input-login label > span::after {
    border-color: #00aaff;
}
.input-login label::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 0;
    width: 100%;
    height: calc(100% - 10px);
    border-bottom: 1px solid #B9C1CA;
}
.input-login label::after {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 100%;
    height: calc(100% - 10px);
    border-bottom: 1px solid #B9C1CA;
    margin-top: 2px;
    border-bottom: 2px solid #1F419B;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    -webkit-transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
}
@-webkit-keyframes anim-1 {
    50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
    }
    51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
    }
}
@keyframes anim-1 {
    50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
    }
    51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
    }
}
.input-login .change-password {
    position: absolute;
    right: 0;
    bottom: 15px;
    color: #1F419B;
    font-weight: 500;
}
#change-password-modal .modal-content {
    width: 320px;
    box-shadow: 0px 19px 38px rgba(0, 0, 0, 0.3), 0px 15px 12px rgba(0, 0, 0, 0.22);
}
#change-password-modal .modal-content .modal-header {
    border-bottom: none;
}
#change-password-modal .modal-content .modal-header h4 {
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-size: 20px;
    font-weight: 500;
}
#change-password-modal .modal-content[data-content='form'] .password-content {
    display: block;
}
#change-password-modal .modal-content[data-content='success'] .password-success {
    display: block;
}
#change-password-modal .password-content {
    display: none;
}
#change-password-modal .password-content .input-login {
    width: 280px;
}
#change-password-modal .password-content .input-login label > span {
    font-size: 12px;
    color: rgba(68, 68, 68, 0.5);
}
#change-password-modal .password-content button {
    background: none;
    border: none;
    text-transform: uppercase;
    font-weight: 500;
}
#change-password-modal .password-content button:first-child {
    color: #444444;
}
#change-password-modal .password-content button:last-child {
    color: #1F419B;
}
#change-password-modal .password-success {
    display: none;
}
#change-password-modal .password-success .success {
    font-size: 26px;
    font-weight: bold;
    font-family: 'Merriweather', serif;
    color: #444444;
}
/*Temp hide*/
.share-btn {
    display: none;
}
.users-orders:first-child {
    margin-top: 0 !important;
}
.users-orders:nth-child(2) {
    margin-top: 0 !important;
}
.users-orders > p {
    color: #444444;
    font-size: 12px;
    border-bottom: 1px solid #EDEDED;
    padding-bottom: 0.5rem;
}
.orders .title {
    margin-bottom: 0.5rem;
}
.orders .title p {
    color: #444444;
    font-family: 'Merriweather', serif;
    font-weight: normal;
    margin-bottom: 0;
    font-size: 12px;
}
.orders .title p span {
    color: rgba(68, 68, 68, 0.7);
    font-family: 'Rubik', sans-serif;
    margin-right: 2px;
}
.orders .title a {
    background: url('../../images/landingpageImages/share-btn.svg') no-repeat center center;
    background-size: contain;
    width: 12px;
    height: 24px;
}
.orders .media img {
    width: 64px;
    height: 80px;
}
.orders .media .media-body {
    margin-left: 1rem;
    border-bottom: 1px solid #EDEDED;
    height: 90px;
    margin-top: 10px;
}
.orders .media .media-body div p {
    color: #444444;
    margin-bottom: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .orders .media .media-body div p {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }
}
.orders .media .media-body div > span {
    font-size: 10px;
    color: #444444;
    font-family: 'Rubik', sans-serif;
}
.orders .media .media-body div > span span {
    color: rgba(68, 68, 68, 0.74);
    margin-right: 2px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .orders .payment-details {
        margin-top: 1rem;
    }
}
.orders .payment-details > div span {
    color: rgba(68, 68, 68, 0.6);
    font-size: 8px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
}
.orders .payment-details > div p {
    font-size: 14px;
    font-family: 'Merriweather', serif;
    color: #444444;
}
.orders .payment-details > div:last-child p {
    color: #B72319;
}
.orders .payment-details > div:last-child p .rupees {
    font-size: 14px;
    font-family: 'Merriweather', serif;
    color: #B72319;
    margin-right: 2px;
}
.about-us {
    background: #FAFAFA;
    padding: 2rem 0;
    padding-bottom: 0;
    position: relative;
    z-index: 99;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .about-us {
        height: 550px;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .about-us {
        height: auto;
    }
}
.about-us .container > h1 {
    font-size: 80px;
    font-family: 'Merriweather', serif;
    font-weight: bold;
    color: rgba(68, 68, 68, 0.4);
    opacity: 0.2;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait) {
    .about-us .container > h1 {
        font-size: 60px;
    }
}
.about-us .container .row {
    margin: 0 auto;
    position: relative;
    top: -3.4rem;
    left: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .about-us .container .row {
        left: 10px;
    }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait) {
    .about-us .container .row {
        left: 0;
    }
}
.about-us .container .row h1 {
    font-size: 64px;
    font-family: 'Merriweather', serif;
    font-weight: normal;
    color: #444444;
}
.about-us .container .row p {
    font-size: 16px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    width: 280px;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .about-us .container .row p {
        width: 200px;
    }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .about-us .container .row p {
        width: auto;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .about-us .container .row p {
        min-height: 200px;
    }
}
.about-us .container .row p span {
    font-weight: 500;
}
.about-us .container .row .col-sm-4 {
    position: relative;
    z-index: 999;
}
.about-us .container .row .bg-line {
    background: url("../../images/landingpageImages/Line.svg") center center no-repeat;
    background-size: cover;
    width: 655px;
    height: 65px;
    position: absolute;
    top: 60%;
    left: 21%;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .about-us .container .row .bg-line {
        width: 400px;
        background-size: contain;
        top: 58%;
        left: 25%;
    }
}
.about-us .container .row .ebook-img {
    background: url("../../images/landingpageImages/ws-img1.png") center center no-repeat;
    background-size: contain;
    width: 280px;
    height: 240px;
    position: relative;
    top: -12%;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .about-us .container .row .ebook-img {
        width: 200px;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .about-us .container .row .ebook-img {
        width: auto;
    }
}
.about-us .container .row .testgenerator-img {
    background: url("../../images/landingpageImages/Create tests_Color.png") center center no-repeat;
    background-size: contain;
    width: 280px;
    height: 240px;
    opacity: 0.2;
    position: relative;
    top: -12%;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .about-us .container .row .testgenerator-img {
        width: 200px;
        top: -18%;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .about-us .container .row .testgenerator-img {
        width: auto;
    }
}
.about-us .container .row .ws-mobileapp {
    background: url("../../images/landingpageImages/ws-img3.svg") center center no-repeat;
    background-size: contain;
    width: 280px;
    height: 240px;
    position: relative;
    top: -12%;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .about-us .container .row .ws-mobileapp {
        width: 200px;
        top: -17%;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .about-us .container .row .ws-mobileapp {
        width: auto;
    }
}
.footer-nav {
    padding: 3rem 0;
    background: #FAFAFA;
    border: 1px solid #EDEDED;
    position: relative;
    z-index: 99;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .footer-nav {
        padding: 2rem 0;
    }
}
.footer-nav .container h4 {
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.4);
    padding-bottom: 1rem;
}
.footer-nav .container a {
    display: block;
    font-size: 16px;
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.7);
    font-weight: 500;
    padding-bottom: 1rem;
}
.footer-nav .container a:hover {
    text-decoration: none;
    color: #1F419B;
}
.footer-nav .links {
    display: flex;
    justify-content: center;
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
    .footer-nav [class^="flaticon-"]:before,
    .footer-nav [class*=" flaticon-"]:before,
    .footer-nav [class^="flaticon-"]:after,
    .footer-nav [class*=" flaticon-"]:after {
        font-size: 24px;
    }
}
.footer-nav .social-icons {
    padding: 0;
    margin: 0;
}
.footer-nav .social-icons li {
    list-style-type: none;
    display: inline-block;
}
.footer-nav .social-icons li i {
    margin-left: 2rem;
    color: #444444;
}
.footer-nav .social-icons li i.fb {
    margin-left: 0;
}
.footer-nav .social-icons li i.fb:hover {
    color: #3a589e;
}
.footer-nav .social-icons li i.tw:hover {
    color: #429cd6;
}
.footer-nav .social-icons li i.ln:hover {
    color: #0d77b7;
}
.footer-nav .social-icons li i.yt:hover {
    color: #F44336;
}
footer {
    padding: 1rem 0;
    background: #FAFAFA;
    position: relative;
    z-index: 99;
}
footer img {
    width: 123px;
    height: 40px;
}
footer .footer-menu li {
    display: inline-block;
    list-style-type: circle;
    padding-left: 5px;
}
footer .footer-menu li:first-child {
    padding-left: 0;
}
footer .footer-menu li a {
    color: rgba(68, 68, 68, 0.6);
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
}
.mobile-menu {
    display: none;
    position: fixed;
    bottom: -1px;
    width: 100%;
    z-index: 999;
    background: #ffffff;
    padding: 1rem 0;
    box-shadow: 0px -1px 8px rgba(0, 0, 0, 0.25);
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
    .mobile-menu.d-sm-none {
        display: inherit !important;
    }
}
.mobile-menu .navbar-nav .nav-item .nav-link.store-btn {
    background: url('../../images/landingpageImages/ws-inactive.svg') center center no-repeat;
    background-size: contain;
    width: 28px;
    height: 28px;
}
.mobile-menu .navbar-nav .nav-item .nav-link.library-btn {
    background: url('../../images/landingpageImages/lib-inactive.svg') center center no-repeat;
    background-size: contain;
    width: 28px;
    height: 28px;
}
.mobile-menu .navbar-nav .nav-item .nav-link.profile-btn {
    background: url('../../images/landingpageImages/profile-inactive.svg') center center no-repeat;
    background-size: contain;
    width: 28px;
    height: 28px;
}
.mobile-menu .navbar-nav .nav-item .nav-link.active.store-btn {
    background: url('../../images/landingpageImages/ws-tabicon.svg') center center no-repeat;
    background-size: contain;
    width: 28px;
    height: 28px;
}
.mobile-menu .navbar-nav .nav-item .nav-link.active.library-btn {
    background: url('../../images/landingpageImages/lib-active.svg') center center no-repeat;
    background-size: contain;
    width: 28px;
    height: 28px;
}
.mobile-menu .navbar-nav .nav-item .nav-link.active.profile-btn {
    background: url('../../images/landingpageImages/profile-active.svg') center center no-repeat;
    background-size: contain;
    width: 28px;
    height: 28px;
}
/*homepage*/
#loginSignup .resend-otp {
    color: #1F419B;
    position: absolute;
    top: 25px;
    right: 0;
}
#loginSignup .continue {
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color: #ffffff;
    text-transform: uppercase;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
}
#loginSignup #otp-error {
    color: #B72319;
    font-size: 14px;
    display: none;
}
#loginSignup .otp-header {
    display: none;
}
#loginSignup .sign-content,
#loginSignup .login-content,
#loginSignup .signup-content,
#loginSignup .reset-password,
#loginSignup .otp-content {
    display: none;
}
#loginSignup[data-page='otp-content'] .otp-content {
    display: block;
}
#loginSignup[data-page='otp-content'] .otp-content input:disabled {
    color: rgba(68, 68, 68, 0.4);
}
#loginSignup[data-page='otp-content'] .modal-header .close {
    margin: 0;
    padding: 0;
}
#loginSignup[data-page='otp-content'] .modal-header .otp-header {
    display: block;
}
#loginSignup[data-page='otp-content'] .modal-footer .terms {
    display: none;
}
#loginSignup[data-page='login-google'] .sign-content {
    display: block;
}
#loginSignup[data-page='login-google'] .modal-header .login-back-btn,
#loginSignup[data-page='login-google'] .modal-header .login {
    display: none;
}
#loginSignup[data-page='login-google'] .modal-footer .terms {
    display: none;
}
#loginSignup[data-page='login'] .login-content {
    display: block;
}
#loginSignup[data-page='login'] .modal-header .close {
    margin: 0;
    padding: 0;
}
#loginSignup[data-page='login'] .modal-header .login {
    display: block;
}
#loginSignup[data-page='signup'] .signup-content {
    display: block;
}
#loginSignup[data-page='signup'] .modal-header .close {
    margin: 0;
    padding: 0;
}
#loginSignup[data-page='signup'] .modal-header .signup {
    display: block;
}
#loginSignup[data-page='reset'] .reset-password {
    display: block;
}
#loginSignup[data-page='reset'] .modal-header .close {
    margin: 0;
    padding: 0;
}
#loginSignup[data-page='reset'] .modal-header .reset {
    display: block;
}
#loginSignup[data-page='reset-completed'] #reset-password-completed {
    display: block;
}
#loginSignup[data-page='reset-completed'] #back-btn {
    display: none;
}
#loginSignup[data-page='reset-completed'] .modal-footer .terms {
    display: none;
}
#loginSignup[data-page='reset-google-paswd'] #reset-google-paswd {
    display: block;
}
#loginSignup[data-page='reset-google-paswd'] #back-btn {
    display: none;
}
#loginSignup[data-page='reset-google-paswd'] .modal-footer .terms {
    display: none;
}
#loginSignup h1 {
    font-size: 34px;
    font-family: 'Merriweather', serif;
    font-weight: bold;
    color: #444444;
    max-width: 217px;
    min-height: 86px;
    margin: 0 auto;
}
#loginSignup p {
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    color: #444444;
    min-height: 38px;
    max-width: 177px;
    margin: 0 auto;
}
#loginSignup .modal-header {
    border-bottom: none;
    padding: 1.5rem 1rem;
    display: flex;
    align-items: center;
}
#loginSignup .modal-header .signup,
#loginSignup .modal-header .login,
#loginSignup .modal-header .reset {
    display: none;
}
#loginSignup .modal-header #back-btn {
    cursor: pointer;
}
#loginSignup .modal-header .close {
    font-size: 28px;
}
#loginSignup .modal-header .close:focus {
    outline: 0;
}
#loginSignup .modal-footer {
    border-top: none;
    height: 40px;
}
#loginSignup .modal-footer .terms {
    color: rgba(68, 68, 68, 0.54);
    font-size: 8px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    max-width: 100%;
    display: flex;
    align-items: flex-end;
    padding-bottom: 5px;
    text-transform: uppercase;
}
#loginSignup .modal-footer .terms a {
    color: rgba(68, 68, 68, 0.54);
    font-size: 8px;
}
#loginSignup .modal-content {
    box-shadow: 0px 19px 38px rgba(0, 0, 0, 0.3), 0px 15px 12px rgba(0, 0, 0, 0.22);
    background-color: #ffffff;
}
@media (min-width: 576px) {
    #loginSignup .modal-dialog {
        width: 400px;
    }
}
#loginSignup .modal-body {
    padding: 0.5rem 1rem;
}
#loginSignup .modal-body .signup-bg {
    background: url('../../images/landingpageImages/singup.png') no-repeat center center;
    background-size: contain;
    width: 100%;
    height: 230px;
    position: absolute;
    top: -2rem;
    left: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    #loginSignup .modal-body .signup-bg {
        top: 0;
    }
}
#loginSignup .modal-body .sign-google {
    background: url('../../images/landingpageImages/ic_google.svg') no-repeat;
    background-position: 14px;
    font-size: 16px;
    color: #444444;
    font-family: 'Rubik', sans-serif;
    border: none;
    background-color: #ffffff;
    width: 296px;
    height: 48px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    position: relative;
    z-index: 999;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    #loginSignup .modal-body .sign-google {
        width: 250px;
    }
}
#loginSignup .modal-body .button-wrapper a {
    color: #1F419B;
    text-transform: uppercase;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    font-size: 14px;
    padding: 8px 40px;
    cursor: pointer;
}
#loginSignup .modal-body .button-wrapper a:first-child {
    border-right: 1px solid rgba(68, 68, 68, 0.6);
}
#loginSignup .modal-body .button-wrapper a:hover {
    text-decoration: none;
}
#loginSignup .login-content .forgot-password,
#loginSignup .signup-content .forgot-password,
#loginSignup .reset-password .forgot-password {
    color: #1F419B;
    font-family: 'Rubik', sans-serif;
    font-size: 12px;
    display: block;
    margin-top: 1rem;
    cursor: pointer;
}
#loginSignup .login-content .input-login,
#loginSignup .signup-content .input-login,
#loginSignup .reset-password .input-login {
    overflow: hidden;
    position: relative;
    z-index: 1;
    display: inline-block;
    margin: 0.3rem;
    width: 312px;
}
#loginSignup .login-content .input-login input,
#loginSignup .signup-content .input-login input,
#loginSignup .reset-password .input-login input {
    border: none;
    outline: 0;
    margin-top: 1rem;
    padding: 0.85em 0.15em;
    width: 100%;
    background: transparent;
    color: #595F6E;
}
#loginSignup .login-content .input-login input:focus + label::after,
#loginSignup .signup-content .input-login input:focus + label::after,
#loginSignup .reset-password .input-login input:focus + label::after {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
#loginSignup .login-content .input-login input:focus + label > span,
#loginSignup .signup-content .input-login input:focus + label > span,
#loginSignup .reset-password .input-login input:focus + label > span {
    -webkit-animation: anim-1 0.3s forwards;
    animation: anim-1 0.3s forwards;
}
#loginSignup .login-content .input-login label,
#loginSignup .signup-content .input-login label,
#loginSignup .reset-password .input-login label {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 0 0.25em;
    width: 100%;
    height: calc(100% - 1em);
    text-align: left;
    pointer-events: none;
}
#loginSignup .login-content .input-login label > span,
#loginSignup .signup-content .input-login label > span,
#loginSignup .reset-password .input-login label > span {
    position: absolute;
}
#loginSignup .login-content .input-login label > span::after,
#loginSignup .signup-content .input-login label > span::after,
#loginSignup .reset-password .input-login label > span::after {
    border-color: #00aaff;
}
#loginSignup .login-content .input-login label::before,
#loginSignup .signup-content .input-login label::before,
#loginSignup .reset-password .input-login label::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 0;
    width: 100%;
    height: calc(100% - 10px);
    border-bottom: 1px solid #B9C1CA;
}
#loginSignup .login-content .input-login label::after,
#loginSignup .signup-content .input-login label::after,
#loginSignup .reset-password .input-login label::after {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 100%;
    height: calc(100% - 10px);
    border-bottom: 1px solid #B9C1CA;
    margin-top: 2px;
    border-bottom: 2px solid #1F419B;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    -webkit-transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
}
@-webkit-keyframes anim-1 {
    50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
    }
    51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
    }
}
@keyframes anim-1 {
    50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
    }
    51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
    }
}
#loginSignup .login-content .continue,
#loginSignup .signup-content .continue,
#loginSignup .reset-password .continue,
#loginSignup .login-content .reset,
#loginSignup .signup-content .reset,
#loginSignup .reset-password .reset {
    width: 312px;
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color: #ffffff;
    text-transform: uppercase;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
}
#loginSignup .login-content .reset,
#loginSignup .signup-content .reset,
#loginSignup .reset-password .reset {
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
}
#loginSignup .reset-password > p {
    font-size: 14px;
    color: rgba(68, 68, 68, 0.75);
    font-family: 'Rubik', sans-serif;
    max-width: 85%;
}
#loginSignup #reset-password-completed,
#loginSignup #reset-google-paswd {
    display: none;
}
#loginSignup #reset-password-completed p,
#loginSignup #reset-google-paswd p {
    max-width: 100%;
}
#loginSignup #reset-password-completed p:first-child,
#loginSignup #reset-google-paswd p:first-child {
    color: #1F419B;
}
#loginSignup #back-login {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
    align-items: center;
    color: #1F419B;
    cursor: pointer;
}
#loginSignup #back-login i {
    margin-right: 10px;
    font-size: 18px;
}
.selectbox select {
    border: none;
    padding: 0.85em 0.15em;
    border-bottom: 1px solid #B9C1CA;
    margin-bottom: 1rem;
    outline: none;
    width: 312px;
}
.loglimit {
    height: calc(100vh - 70px);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}
.loglimit h1 {
    font-size: 32px;
    color: #444444;
    font-family: 'Merriweather', serif;
    font-weight: bold;
}
.loglimit p {
    margin-top: 3rem;
    color: rgba(68, 68, 68, 0.72);
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
}
.loglimit button {
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    font-size: 12px;
    text-transform: uppercase;
}
.loglimit button.logout {
    width: 200px;
    color: #ffffff;
    background: linear-gradient(92.46deg, #3AD03F 27.02%, #4DAF2B 86.83%);
}
.loglimit button.cancel {
    color: #B72319;
    border: none;
    background: none;
}
.banner {
    position: relative;
    padding-bottom: 1.4rem;
    padding-top: 6rem;
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : landscape) {
    .banner {
        padding-top: 4rem;
    }
}
.banner .banner-image {
    background: url('../../images/landingpageImages/banner.png') no-repeat right center;
    opacity: 0.2;
    width: 300px;
    height: 450px;
    background-size: contain;
    margin-top: 2.3rem;
    position: absolute;
    right: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .banner .banner-image {
        width: 100%;
        height: 100px;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
    .banner .banner-image {
        width: 200px;
        height: 200px;
    }
}
.banner .container {
    position: relative;
    top: 0;
    z-index: 99;
}
.banner .jumbotron {
    background-color: transparent;
}
.banner .jumbotron h1 {
    font-size: 40px;
    font-family: 'Merriweather', serif;
    text-align: center;
    color: #444444;
    font-weight: bold;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .banner .jumbotron h1 {
        font-size: 32px;
        margin-top: 4rem;
        text-align: left;
    }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .banner .jumbotron {
        margin-bottom: 0;
    }
}
.banner .form-container input[type="text"] {
    width: 448px;
    height: 48px;
    padding-left: 10px;
    padding-right: 10px;
    border: none;
    background: #ffffff;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24), 0px 0px 2px rgba(0, 0, 0, 0.12);
}
.banner .form-container input[type="text"]:focus {
    outline: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .banner .form-container input[type="text"] {
        width: 300px;
    }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
    .banner .form-container input[type="text"] {
        width: 350px;
    }
}
.banner .form-container button {
    width: 48px;
    height: 48px;
    background: #1F419B;
    border: none;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    margin-left: -48px;
}
.banner .form-container button i {
    color: #ffffff;
    font-size: 24px;
}
.banner .categories .category-line {
    width: 160px;
    border: 1px solid #EDEDED;
    background: #EDEDED;
    position: relative;
    top: -20px;
}
.banner .categories h2 {
    color: rgba(68, 68, 68, 0.7);
    font-family: 'Rubik', sans-serif;
    font-size: 20px;
    font-weight: 500;
}
.banner .categories .category-content {
    margin-top: 2.5rem;
}
.banner .categories .category-content > a.nav-link {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    text-align: center;
}
.banner .categories .category-content > a.nav-link i {
    color: rgba(68, 68, 68, 0.8);
}
.banner .categories .category-content > a.nav-link span {
    display: block;
    color: #444444;
    text-decoration: none;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .banner .categories .category-content > a.nav-link span {
        font-size: 12px;
    }
}
.banner .categories .category-content > a:hover i,
.banner .categories .category-content > a:hover span {
    color: #1F419B;
}
.sliders {
    padding-bottom: 20px;
}
.sliders .quick-menu .navbar-nav .nav-item .nav-link {
    color: #444444;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
}
.sliders .quick-menu .navbar-nav .nav-item .nav-link:hover {
    color: #1F419B;
}
.sliders .quick-menu .navbar-nav .nav-item.active .nav-link {
    color: #1F419B;
}
.sliders .header {
    padding-bottom: 10px;
    padding-top: 10px;
    border-bottom: none;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
}
.sliders .header.card {
    border: 1px solid #EDEDED;
}
.sliders .header .card-body {
    padding-top: 0;
    padding-bottom: 0;
}
.sliders .header p {
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    margin-bottom: 0;
}
.sliders .header p strong {
    font-weight: 500;
}
.sliders .header button {
    background-image: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
    color: #ffffff;
    text-transform: uppercase;
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
    width: 160px;
    height: 40px;
    margin-left: 1rem;
}
.sliders .header button:focus {
    outline: 0;
}
.sliders #topSellers {
    padding-bottom: 1.5rem;
    border: 1px solid #EDEDED;
}
.sliders #topSellers .item {
    position: relative;
    padding: 10px ;
}
.sliders #topSellers > h4 {
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.4);
    text-transform: uppercase;
    padding-top: 20px;
    padding-left: 20px;
}
.sliders #topSellers .item .image-wrapper {
    width: 132px;
    height: 165px;
    position: relative;
    z-index: 9999;
}
.sliders #topSellers .item .image-wrapper img {
    width: 132px;
    height: 165px;
    padding: 10px;
    position: relative;
    border-radius: 14px;
}
.sliders #topSellers .item .image-wrapper h3 {
    position: absolute;
    font-size: 10px;
    font-weight: 500;
    color: #ffffff;
    background-color: #1F419B;
    padding: 7px 14px;
    bottom: 32px;
    left: 4px;
    margin-bottom: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
.sliders #topSellers .item .image-wrapper h3:after {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 100%;
    border-width: 2px 3px;
    border-style: solid;
    border-color: #1F419B #1F419B transparent transparent;
}
.sliders #topSellers .item .content-wrapper {
    width: 288px;
    height: 113px;
    position: absolute;
    bottom: 5px;
    padding: 10px;
    border-radius: 6px;
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    background: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .sliders #topSellers .item .content-wrapper {
        width: 258px;
    }
}
.sliders #topSellers .item .content-wrapper h3 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    height: 43px;
    /* fallback */
    -webkit-line-clamp: 3;
    /* number of lines to show */
    -webkit-box-orient: vertical;
}
.sliders #topSellers .item .content-wrapper h3,
.sliders #topSellers .item .content-wrapper p {
    float: right;
    width: 140px;
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    font-weight: normal;
    margin-bottom: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .sliders #topSellers .item .content-wrapper h3,
    .sliders #topSellers .item .content-wrapper p {
        width: 120px;
    }
}
.sliders #topSellers .item .content-wrapper p.sub-name {
    font-weight: 500;
    font-size: 10px;
    margin-bottom: 0.5rem;
}
.sliders #topSellers .item .content-wrapper p.sub-name span {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
}
.sliders #topSellers .item .content-wrapper p.complete {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
}
.sliders #topSellers .item .content-wrapper p.price {
    color: #B72319;
    font-size: 14px;
    font-family: 'Merriweather', serif;
}
.sliders #topSellers .item .content-wrapper p.price span.ori-price {
    font-size: 10px;
    text-decoration: line-through;
    color: rgba(68, 68, 68, 0.6);
    margin-left: 5px;
}
.books-types {
    padding-top: 2rem;
    padding-bottom: 1rem;
}
#topSchoolBooks .content-header > h4,
#topCollegeBooks .content-header > h4,
#topTestBooks .content-header > h4,
#featuredBooks .content-header > h4,
#recentRead .content-header > h4 {
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.4);
    text-transform: uppercase;
    margin: 0;
    padding: 16px 20px;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    #topSchoolBooks .content-header > h4,
    #topCollegeBooks .content-header > h4,
    #topTestBooks .content-header > h4,
    #featuredBooks .content-header > h4,
    #recentRead .content-header > h4 {
        padding: 10px 0 10px 20px;
    }
}
#topSchoolBooks .content-header > a,
#topCollegeBooks .content-header > a,
#topTestBooks .content-header > a,
#featuredBooks .content-header > a,
#recentRead .content-header > a {
    padding: 16px 20px;
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    color: #2F80ED;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    #topSchoolBooks .content-header > a,
    #topCollegeBooks .content-header > a,
    #topTestBooks .content-header > a,
    #featuredBooks .content-header > a,
    #recentRead .content-header > a {
        padding: 10px 8px 10px 0;
    }
}
#topSchoolBooks .topSchoolBooks .item,
#topCollegeBooks .topSchoolBooks .item,
#topTestBooks .topSchoolBooks .item,
#featuredBooks .topSchoolBooks .item,
#recentRead .topSchoolBooks .item {
    padding: 10px;
}
#topSchoolBooks .topSchoolBooks .item .image-wrapper,
#topCollegeBooks .topSchoolBooks .item .image-wrapper,
#topTestBooks .topSchoolBooks .item .image-wrapper,
#featuredBooks .topSchoolBooks .item .image-wrapper,
#recentRead .topSchoolBooks .item .image-wrapper {
    width: 132px;
    height: 165px;
    position: relative;
    z-index: 999;
}
#topSchoolBooks .topSchoolBooks .item .image-wrapper img,
#topCollegeBooks .topSchoolBooks .item .image-wrapper img,
#topTestBooks .topSchoolBooks .item .image-wrapper img,
#featuredBooks .topSchoolBooks .item .image-wrapper img,
#recentRead .topSchoolBooks .item .image-wrapper img {
    width: 132px;
    height: 165px;
    position: relative;
    border-radius: 4px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
#topSchoolBooks .topSchoolBooks .item .image-wrapper h3,
#topCollegeBooks .topSchoolBooks .item .image-wrapper h3,
#topTestBooks .topSchoolBooks .item .image-wrapper h3,
#featuredBooks .topSchoolBooks .item .image-wrapper h3,
#recentRead .topSchoolBooks .item .image-wrapper h3 {
    position: absolute;
    font-size: 10px;
    font-weight: 500;
    color: #ffffff;
    background-color: #1F419B;
    padding: 7px 14px;
    bottom: 32px;
    left: -6px;
    margin-bottom: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
#topSchoolBooks .topSchoolBooks .item .image-wrapper h3:after,
#topCollegeBooks .topSchoolBooks .item .image-wrapper h3:after,
#topTestBooks .topSchoolBooks .item .image-wrapper h3:after,
#featuredBooks .topSchoolBooks .item .image-wrapper h3:after,
#recentRead .topSchoolBooks .item .image-wrapper h3:after {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 100%;
    border-width: 2px 3px;
    border-style: solid;
    border-color: #1F419B #1F419B transparent transparent;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper,
#topCollegeBooks .topSchoolBooks .item .content-wrapper,
#topTestBooks .topSchoolBooks .item .content-wrapper,
#featuredBooks .topSchoolBooks .item .content-wrapper,
#recentRead .topSchoolBooks .item .content-wrapper {
    height: 113px;
    margin-top: 8px;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper h3,
#topCollegeBooks .topSchoolBooks .item .content-wrapper h3,
#topTestBooks .topSchoolBooks .item .content-wrapper h3,
#featuredBooks .topSchoolBooks .item .content-wrapper h3,
#recentRead .topSchoolBooks .item .content-wrapper h3 {
    height: 43px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    /* number of lines to show */
    -webkit-box-orient: vertical;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper h3,
#topCollegeBooks .topSchoolBooks .item .content-wrapper h3,
#topTestBooks .topSchoolBooks .item .content-wrapper h3,
#featuredBooks .topSchoolBooks .item .content-wrapper h3,
#recentRead .topSchoolBooks .item .content-wrapper h3,
#topSchoolBooks .topSchoolBooks .item .content-wrapper p,
#topCollegeBooks .topSchoolBooks .item .content-wrapper p,
#topTestBooks .topSchoolBooks .item .content-wrapper p,
#featuredBooks .topSchoolBooks .item .content-wrapper p,
#recentRead .topSchoolBooks .item .content-wrapper p {
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    font-weight: normal;
    margin-bottom: 0;
    width: 132px;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper p.sub-name,
#topCollegeBooks .topSchoolBooks .item .content-wrapper p.sub-name,
#topTestBooks .topSchoolBooks .item .content-wrapper p.sub-name,
#featuredBooks .topSchoolBooks .item .content-wrapper p.sub-name,
#recentRead .topSchoolBooks .item .content-wrapper p.sub-name {
    font-weight: 500;
    font-size: 10px;
    margin-bottom: 0.5rem;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper p.sub-name span,
#topCollegeBooks .topSchoolBooks .item .content-wrapper p.sub-name span,
#topTestBooks .topSchoolBooks .item .content-wrapper p.sub-name span,
#featuredBooks .topSchoolBooks .item .content-wrapper p.sub-name span,
#recentRead .topSchoolBooks .item .content-wrapper p.sub-name span {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper p.complete,
#topCollegeBooks .topSchoolBooks .item .content-wrapper p.complete,
#topTestBooks .topSchoolBooks .item .content-wrapper p.complete,
#featuredBooks .topSchoolBooks .item .content-wrapper p.complete,
#recentRead .topSchoolBooks .item .content-wrapper p.complete {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper p.price,
#topCollegeBooks .topSchoolBooks .item .content-wrapper p.price,
#topTestBooks .topSchoolBooks .item .content-wrapper p.price,
#featuredBooks .topSchoolBooks .item .content-wrapper p.price,
#recentRead .topSchoolBooks .item .content-wrapper p.price {
    color: #B72319;
    font-size: 14px;
    font-family: 'Merriweather', serif;
}
#topSchoolBooks .topSchoolBooks .item .content-wrapper p.price span.ori-price,
#topCollegeBooks .topSchoolBooks .item .content-wrapper p.price span.ori-price,
#topTestBooks .topSchoolBooks .item .content-wrapper p.price span.ori-price,
#featuredBooks .topSchoolBooks .item .content-wrapper p.price span.ori-price,
#recentRead .topSchoolBooks .item .content-wrapper p.price span.ori-price {
    font-size: 10px;
    text-decoration: line-through;
    color: rgba(68, 68, 68, 0.6);
    margin-left: 5px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    #topSchoolBooks .topSchoolBooks .item .content-wrapper,
    #topCollegeBooks .topSchoolBooks .item .content-wrapper,
    #topTestBooks .topSchoolBooks .item .content-wrapper,
    #featuredBooks .topSchoolBooks .item .content-wrapper,
    #recentRead .topSchoolBooks .item .content-wrapper {
        width: 132px;
    }
}
.otp-screens [data-verify="otp-code"] .req-otpwrapper,
.otp-screens [data-verify="otp-code"] #verify-otp,
.otp-screens [data-verify="otp-code"] .mobile-text {
    display: block;
}
.otp-screens [data-verify="otp-code"] .mobile-wrapper,
.otp-screens [data-verify="otp-code"] .otp-text,
.otp-screens [data-verify="otp-code"] #get-otp {
    display: none;
}
.otp-screens h4 {
    font-size: 24px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    font-weight: normal;
}
.otp-screens p {
    font-size: 12px;
    color: rgba(68, 68, 68, 0.4);
    padding: 0.5rem 2rem;
}
.otp-screens p span {
    font-size: 12px;
    font-weight: 500;
    color: #444444;
}
.otp-screens .btn-continue {
    width: 90%;
}
.otp-screens .modal-header {
    border-bottom: none;
    padding: 1.5rem 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}
.otp-screens .modal-header .close {
    padding: 0;
    margin: 0;
}
.otp-screens .modal-footer {
    border-top: none;
    justify-content: space-around;
    margin-bottom: 1rem;
}
.otp-screens .input-login {
    overflow: hidden;
    position: relative;
    z-index: 1;
    display: inline-block;
    margin: 0.3rem;
    width: 312px;
}
.otp-screens .input-login input {
    border: none;
    outline: 0;
    margin-top: 1rem;
    padding: 0.85em 0.15em;
    width: 100%;
    background: transparent;
    color: #595F6E;
}
.otp-screens .input-login input:focus + label::after {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
.otp-screens .input-login input:focus + label > span {
    -webkit-animation: anim-1 0.3s forwards;
    animation: anim-1 0.3s forwards;
}
.otp-screens .input-login label {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 0 0.25em;
    width: 100%;
    height: calc(100% - 1em);
    text-align: left;
    pointer-events: none;
}
.otp-screens .input-login label > span {
    position: absolute;
    text-align: center;
    width: 100%;
    color: rgba(68, 68, 68, 0.4);
    font-size: 12px;
}
.otp-screens .input-login label > span::after {
    border-color: #00aaff;
}
.otp-screens .input-login label::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 0;
    width: 100%;
    height: calc(100% - 10px);
    border-bottom: 1px solid #B9C1CA;
}
.otp-screens .input-login label::after {
    content: '';
    position: absolute;
    top: 7px;
    left: 0;
    width: 100%;
    height: calc(100% - 10px);
    border-bottom: 1px solid #B9C1CA;
    margin-top: 2px;
    border-bottom: 2px solid #1F419B;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    -webkit-transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
}
@-webkit-keyframes anim-1 {
    50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
    }
    51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
    }
}
@keyframes anim-1 {
    50% {
        opacity: 0;
        -webkit-transform: translate3d(1em, 0, 0);
        transform: translate3d(1em, 0, 0);
    }
    51% {
        opacity: 0;
        -webkit-transform: translate3d(-1em, -40%, 0);
        transform: translate3d(-1em, -40%, 0);
    }
    100% {
        opacity: 1;
        -webkit-transform: translate3d(0, -40%, 0);
        transform: translate3d(0, -40%, 0);
    }
}
.resend-otp {
    color: #1F419B;
    font-weight: 500;
}
.resend-otp:hover {
    color: #1F419B;
}
#otp-error {
    color: #B72319;
}
@media (min-width: 576px) {
    .otp-screens .modal-dialog {
        width: 400px;
    }
}
.modal {
    background: rgba(0, 0, 0, 0.3) !important;
}
.btn-continue {
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color: #ffffff;
    text-transform: uppercase;
    font-size: 14px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
}
.req-otpwrapper,
#verify-otp,
.mobile-text {
    display: none;
}
.recieve-msg {
    color: rgba(68, 68, 68, 0.4);
    font-size: 12px;
    text-align: center;
    display: block;
}
#otp-next .modal-body p,
#otp-confirm .modal-body p,
.otp-confirm .modal-body p {
    font-size: 16px;
    color: #444444;
}
#otp-next .btn-continue,
#otp-confirm .btn-continue,
.otp-confirm .btn-continue {
    width: 50%;
}
.blog-article {
    padding-top: 2rem;
    padding-bottom: 2rem;
}
.blog-article #upcomingExams > h4 {
    color: rgba(68, 68, 68, 0.4);
    font-family: 'Rubik', sans-serif;
    font-size: 12px;
    font-weight: 500;
    padding: 16px 20px;
    margin: 0;
}
.blog-article #upcomingExams .upcomingExams .item {
    padding: 10px;
}
.blog-article #upcomingExams .upcomingExams .item .content-wrapper {
    width: 184px;
    min-height: 246px;
    background: #ffffff;
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    padding: 16px;
}
.blog-article #upcomingExams .upcomingExams .item .content-wrapper h3 {
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    font-weight: 500;
    color: #444444;
    text-transform: uppercase;
}
.blog-article #upcomingExams .upcomingExams .item .content-wrapper .short-text {
    font-family: 'Rubik', sans-serif;
    font-size: 12px;
    color: #444444;
}
.blog-article #upcomingExams .upcomingExams .item .content-wrapper .short-text .morelink {
    color: #1F419B;
}
.blog-article .blog > h4 {
    color: rgba(68, 68, 68, 0.4);
    font-family: 'Rubik', sans-serif;
    font-size: 12px;
    font-weight: 500;
    padding: 16px 20px;
    margin: 0;
}
.blog-article .blog .item {
    padding: 10px;
}
.blog-article .blog .item .card {
    min-height: 250px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .blog-article .blog .item .card {
        width: 280px;
    }
}
.blog-article .blog .item .card img {
    width: 320px;
    height: 180px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .blog-article .blog .item .card img {
        width: 280px;
    }
}
.blog-article .blog .item .card .card-body {
    width: 320px;
    padding: 0.6rem 1.25rem;
}
.blog-article .blog .item .card .card-body img {
    height: 180px;
}
.blog-article .blog .item .card .card-body p {
    font-size: 16px;
    color: #444444;
    margin-bottom: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .blog-article .blog .item .card .card-body p {
        width: 270px;
    }
}
/*store*/
.store {
    padding-top: 6rem;
    min-height: 75vh;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store {
        padding-top: 6rem;
        min-height: auto;
    }
}
.store .search {
    width: 248px;
    height: 48px;
    background: #ffffff;
    padding-left: 40px;
    position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
    .store .search {
        width: 100%;
    }
}
.store i.searchIcon {
    color: rgba(68, 68, 68, 0.7);
    position: absolute;
    top: 14px;
    left: 13px;
}
.store .bl-left {
    border-left: 1px solid #EDEDED;
    min-height: 70vh;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .bl-left {
        min-height: 100%;
    }
}
.store .tab-menu .nav-pills {
    padding: 1rem 0.5rem;
    border-bottom: 2px solid #EDEDED;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .tab-menu .nav-pills {
        border-bottom: 1px solid #EDEDED;
    }
}
.store .tab-menu .nav-pills .nav-link {
    color: #444444;
    text-align: center;
    padding: 0;
}
.store .tab-menu .nav-pills .nav-link i {
    color: rgba(68, 68, 68, 0.8);
}
.store .tab-menu .nav-pills .nav-link span {
    display: block;
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
    width: 64px;
    min-height: 34px;
}
.store .tab-menu .nav-pills .nav-link.active {
    color: #1F419B;
    background: none;
}
.store .tab-menu .nav-pills .nav-link.active i {
    color: #1F419B;
}
.store .tab-menu .nav-pills .nav-link.active span {
    font-weight: 500;
    color: #1F419B;
}
.store .tab-menu .nav-pills.show > .nav-link {
    color: #1F419B;
    background: none;
}
.store .ai-generate .card {
    border-bottom: none;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}
.store .ai-generate .card .card-body {
    padding: 10px;
    text-align: center;
}
.store .ai-generate .btn-start {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    border-color: transparent;
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
    color: #ffffff;
    text-transform: uppercase;
}
.store .ai-generate .btn-start:focus {
    outline: none !important;
    border: none;
}
.store .ai-generate .btn-start:active {
    outline: none !important;
    border: none;
}
.filter form label,
.mobile-filter form label {
    font-family: 'Rubik', sans-serif;
    font-size: 12px;
    color: rgba(68, 68, 68, 0.4);
    margin-top: 1rem;
    padding: 0 2px;
}
.filter form .class-selection-btn,
.mobile-filter form .class-selection-btn {
    background: none;
    border-bottom: 1px solid rgba(1, 1, 1, 0.12);
    padding: 8px 2px;
    width: 100%;
    text-align: left;
    border-radius: 0px;
}
.filter form .class-selection-btn.disabled,
.mobile-filter form .class-selection-btn.disabled {
    color: #444444;
}
.filter form .class-selection-btn:after,
.mobile-filter form .class-selection-btn:after {
    float: right;
    position: relative;
    top: 9px;
    right: 7px;
    border: 6px solid transparent;
    border-color: rgba(1, 1, 1, 0.5) transparent transparent transparent;
}
.filter form > div.show,
.mobile-filter form > div.show {
    position: relative;
}
.filter form > div.show #class-selection-btn:after,
.mobile-filter form > div.show #class-selection-btn:after {
    top: 0;
    border-color: transparent transparent rgba(1, 1, 1, 0.5) transparent;
}
.filter form > div.show ul,
.mobile-filter form > div.show ul {
    position: absolute;
    top: 125% !important;
    left: 0;
    right: 0;
    z-index: 99;
    transform: none !important;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    border-radius: 6px;
    width: 100%;
    max-height: 200px;
    overflow: auto;
    border: none;
    background: #ffffff;
}
.filter form > div.show ul li,
.mobile-filter form > div.show ul li {
    padding: 0;
    cursor: pointer;
    user-select: none;
}
.filter form > div.show ul li a,
.mobile-filter form > div.show ul li a {
    padding: 10px 20px;
    width: 100%;
    display: flex;
    color: #444444;
    font-size: 14px;
}
.filter form > div.show ul li:first-child,
.mobile-filter form > div.show ul li:first-child {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
}
.filter form > div.show ul li:last-child,
.mobile-filter form > div.show ul li:last-child {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
}
.filter form > div.show ul li:hover,
.mobile-filter form > div.show ul li:hover {
    background: #EDEDED;
    border-radius: 0;
}
.filter form > div.show ul li:hover a,
.mobile-filter form > div.show ul li:hover a {
    text-decoration: none;
}
.mobile-filter {
    width: 100%;
}
.mobile-filter .mob-sort > button {
    position: fixed;
    top: 80%;
    z-index: 999;
    left: 37%;
    background: #1F419B;
    border: none;
    outline: none;
    border-radius: 35px;
    width: 95px;
    height: 30px;
    color: #ffffff;
    justify-content: center;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
#filter-mode {
    z-index: 9999;
}
#filter-mode .modal-dialog {
    margin: 0;
}
#filter-mode .modal-dialog .modal-content {
    height: 100vh;
    border-radius: 0;
    border: none;
}
#filter-mode .modal-dialog .modal-content .modal-body form > div button {
    width: 100%;
    text-align: left;
}
#filter-mode .modal-dialog .modal-content .modal-body form > div button::after {
    float: right;
    margin-top: 8px;
}
#filter-mode .modal-dialog .modal-content .reset {
    background: none;
    border: none;
}
body.modal-open {
    overflow: hidden;
}
.eutkarsh.custom-fix .store div > .quick-sortmenu {
    margin-top: 4rem;
}
.store .tab-content .tab-pane > .d-flex.justify-content-between.align-items-center {
    position: absolute;
    top: 0;
    width: 100%;
    left: 0;
    padding-left: 20px;
}
.store .tab-content .tab-pane > div .custom-select-ws {
    width: 160px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .tab-content .tab-pane > div .custom-select-ws {
        width: 200px;
        margin-right: 20px;
    }
}
.store .tab-content .tab-pane > div .custom-select-ws .select-items {
    z-index: 9999;
}
.store .tab-content .tab-pane > div .custom-select-ws .select-selected {
    border: 1px solid #EDEDED;
    border-radius: 4px;
    font-family: 'Rubik', sans-serif;
    font-size: 12px;
}
.store .tab-content .tab-pane > div .custom-select-ws .select-selected:before {
    content: 'Sort by :';
    color: rgba(68, 68, 68, 0.5);
    font-weight: normal;
    margin-right: 5px;
    padding-left: 10px;
}
.store .tab-content .tab-pane > div > h3 {
    font-size: 18px;
    font-weight: 500;
    color: #444444;
    font-family: 'Rubik', sans-serif;
}
.store .tab-content .tab-pane > div > h3 span {
    color: rgba(68, 68, 68, 0.5);
    font-weight: normal;
}
.store .topSchoolBooks {
    padding: 10px;
    padding-bottom: 0;
}
.store .topSchoolBooks:hover {
    background: #ffffff;
    border-radius: 6px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    transition: all 0.5s;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
    .store .topSchoolBooks:hover {
        width: 152px;
    }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
    .store .topSchoolBooks:hover {
        width: 152px;
    }
}
.store .topSchoolBooks > a:hover {
    text-decoration: none;
}
.store .topSchoolBooks .image-wrapper {
    width: 132px;
    height: 165px;
    position: relative;
    z-index: 99;
}
.store .topSchoolBooks .image-wrapper > a:hover {
    text-decoration: none;
}
.store .topSchoolBooks .image-wrapper img {
    width: 132px;
    height: 165px;
    position: relative;
    border-radius: 4px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
.store .topSchoolBooks .image-wrapper h3 {
    position: absolute;
    font-size: 10px;
    font-weight: 500;
    color: #ffffff;
    background-color: #1F419B;
    padding: 7px 14px;
    bottom: 32px;
    left: -6px;
    margin-bottom: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
.store .topSchoolBooks .image-wrapper h3:after {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 100%;
    border-width: 2px 3px;
    border-style: solid;
    border-color: #1F419B #1F419B transparent transparent;
}
.store .topSchoolBooks .content-wrapper {
    height: 113px;
    margin-top: 8px;
}
.store .topSchoolBooks .content-wrapper a:hover {
    text-decoration: none;
}
.store .topSchoolBooks .content-wrapper h3 {
    height: 43px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    /* number of lines to show */
    -webkit-box-orient: vertical;
}
.store .topSchoolBooks .content-wrapper h3,
.store .topSchoolBooks .content-wrapper p {
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    font-weight: normal;
    margin-bottom: 0;
    width: 132px;
}
.store .topSchoolBooks .content-wrapper p.sub-name {
    font-weight: 500;
    font-size: 10px;
    margin-bottom: 0.5rem;
}
.store .topSchoolBooks .content-wrapper p.sub-name span {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
    margin-right: 4px;
}
.store .topSchoolBooks .content-wrapper p.complete {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
}
.store .topSchoolBooks .content-wrapper p.price {
    color: #B72319;
    font-size: 14px;
    font-family: 'Merriweather', serif;
}
.store .topSchoolBooks .content-wrapper p.price span {
    font-size: 10px;
    text-decoration: line-through;
    color: rgba(68, 68, 68, 0.6);
    margin-left: 5px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .topSchoolBooks .content-wrapper {
        width: 132px;
    }
}
.store .topSchoolBooks .content-wrapper .price-tag p {
    width: 100%;
}
.test-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}
.test-wrapper:hover {
    background: #ffffff;
    border-radius: 6px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    transition: all 0.5s;
    text-decoration: none;
}
.test-wrapper .content-wrapper {
    padding: 0.8rem 5px;
}
.test-wrapper .content-wrapper h3 {
    font-size: 8px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    color: rgba(68, 68, 68, 0.4);
    text-transform: uppercase;
}
.test-wrapper .content-wrapper p {
    font-size: 12px;
    color: #B72319;
}
.test-wrapper .content-wrapper p.sub-name {
    font-size: 10px;
    color: #444444;
    font-weight: 500;
}
.test-wrapper .content-wrapper p.sub-name span {
    color: rgba(68, 68, 68, 0.72);
    margin-right: 0.3rem;
    font-weight: normal;
}
.test-wrapper .card {
    padding: 0.8rem 0.8rem;
    border: 0.5px solid rgba(0, 0, 0, 0.4);
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    background: #ffffff;
    width: 132px;
    min-height: 165px;
    margin-top: 0.8rem;
}
.test-wrapper .card h4 {
    font-size: 12px;
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    text-align: center;
}
.test-wrapper .card > p {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    text-align: center;
    margin: 0;
}
.test-wrapper .card h2 {
    font-size: 14px;
    color: #444444;
    font-family: 'Merriweather', serif;
    font-weight: 500;
    text-align: center;
}
.test-wrapper .card .test-content > div {
    width: 50%;
}
.test-wrapper .card .test-content p {
    margin: 0;
    font-size: 12px;
    font-family: 'Merriweather', serif;
    color: #444444;
    text-align: center;
}
.test-wrapper .card .test-content p.language {
    font-family: 'Rubik', sans-serif;
}
.test-wrapper .card .test-content span {
    color: rgba(68, 68, 68, 0.4);
    font-size: 8px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    display: block;
    text-align: center;
    text-transform: uppercase;
}
.butn-wrappers .btn-rank {
    border: 1px solid #F79420;
    font-size: 14px;
    color: #F79420;
    display: block;
    margin-bottom: 0.5rem;
}
.butn-wrappers .btn-attempt {
    background: linear-gradient(94.13deg, #3AE878 13.47%, #30C465 73.92%);
    color: #ffffff;
    display: block;
}
.store .quick-sortmenu {
    position: fixed;
    background: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .quick-sortmenu {
        position: static;
    }
}
.store .quick-sort {
    border-bottom: 1px solid #EDEDED;
    margin-top: 2rem !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .quick-sort .quick-nav {
        flex-wrap: nowrap;
        overflow-y: auto;
        margin-right: -15px;
        margin-left: -15px;
        margin-top: 3rem;
    }
}
.store .quick-sort .quick-nav li {
    white-space: nowrap;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .quick-sort .quick-nav li {
        padding: 0px 10px;
    }
}
.store .quick-sort .quick-nav li a {
    color: #444444;
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
    padding-left: 0;
    padding-right: 1.5rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .store .quick-sort .quick-nav li a {
        padding-right: 0;
    }
}
.store .quick-sort .quick-nav li a.active {
    color: #1F419B;
    font-weight: 500;
}
.store .quick-sort .quick-nav button {
    background: none;
    outline: none;
    border: none;
    color: #2F80ED;
    font-family: 'Rubik', sans-serif;
    font-size: 14px;
    cursor: pointer;
}
.store .quick-sort .quick-nav button:before {
    content: '+';
    color: #2F80ED;
    padding: 5px;
}
.no-books-available {
    margin: 0 auto;
}
.no-books-available .no-book-wrapper {
    text-align: center;
}
.no-books-available .no-book-wrapper img {
    width: 200px;
    height: 200px;
}
.book-details {
    margin-top: 4rem;
    padding-bottom: 4rem;
}
.book-details .test-wrapper:hover {
    box-shadow: none;
    background: none;
}
.book-details .breadcrumb {
    background: none;
}
.book-details .breadcrumb li {
    font-size: 12px;
    color: rgba(68, 68, 68, 0.4);
    padding: 0 2px;
    font-family: 'Rubik', sans-serif;
}
.book-details .breadcrumb li.active {
    color: #444444;
    font-weight: 500;
}
.book-details .breadcrumb li a {
    color: rgba(68, 68, 68, 0.4);
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
}
.book-details .topSchoolBooks > a:hover {
    text-decoration: none;
}
.book-details .topSchoolBooks .image-wrapper {
    margin: 0 auto;
    width: 132px;
    height: 165px;
    position: relative;
    z-index: 99;
}
.book-details .topSchoolBooks .image-wrapper > a:hover {
    text-decoration: none;
}
.book-details .topSchoolBooks .image-wrapper img {
    width: 132px;
    height: 165px;
    position: relative;
    border-radius: 4px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
.book-details .topSchoolBooks .image-wrapper h3 {
    position: absolute;
    font-size: 10px;
    font-weight: 500;
    color: #ffffff;
    background-color: #1F419B;
    padding: 7px 14px;
    bottom: 32px;
    left: -6px;
    margin-bottom: 0;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
.book-details .topSchoolBooks .image-wrapper h3:after {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    left: 0;
    top: 100%;
    border-width: 2px 3px;
    border-style: solid;
    border-color: #1F419B #1F419B transparent transparent;
}
.content-wrappers h1 {
    font-size: 20px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    font-weight: 500;
    margin-bottom: 0;
}
.content-wrappers .author-name {
    font-size: 14px;
    color: rgba(68, 68, 68, 0.6);
}
.content-wrappers .author-name span {
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    color: #444444;
}
.content-wrappers .chapt-bk {
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.72);
    text-transform: uppercase;
    font-size: 10px;
}
.content-wrappers .offer-price,
.content-wrappers .original-price {
    color: #B72319;
    font-family: 'Merriweather', serif;
    font-weight: 500;
}
.content-wrappers div.d-flex > div {
    margin-right: 2rem;
}
.preview-book-btns {
    margin-left: 1rem;
}
.preview-book-btns .btn-book-preview {
    border: 1px solid #ededed;
    color: #1F419B !important;
    border-radius: 4px;
    text-transform: uppercase;
    width: 160px;
    height: 38px;
    font-size: 14px;
    margin: 1rem auto;
}
.preview-book-btns .btn-book-buy {
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
    width: 160px;
    height: 40px;
    color: #ffffff !important;
    font-size: 14px;
    margin: 1rem auto;
}
.preview-book-desc {
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
    background: #ffffff;
    padding: 2rem 0;
    border-radius: 4px;
    margin-top: -2rem;
}
.showrank-modal .modal-header {
    justify-content: center;
}
.showrank-modal .modal-header button {
    padding: 0;
    margin: 0;
    position: absolute;
    right: 25px;
    top: 25px;
}
.showrank-modal .modal-footer {
    border: none;
}
.showrank-modal .modal-body {
    padding: 0;
}
.showrank-modal .modal-body .table thead th {
    font-size: 12px;
    font-weight: 500;
    color: rgba(68, 68, 68, 0.7);
}
.showrank-modal .modal-body tr.user-active td {
    color: #1F419B !important;
}
.showrank-modal .modal-body tr td:first-child {
    font-size: 18px;
    font-weight: 500;
    color: rgba(68, 68, 68, 0.7);
    font-family: 'Merriweather', serif;
}
.showrank-modal .modal-body tr td:nth-child(2) {
    font-size: 14px;
    font-weight: normal;
    color: #444444;
    font-family: 'Rubik', sans-serif;
}
.showrank-modal .modal-body tr td:last-child {
    font-size: 14px;
    font-weight: bold;
    color: #444444;
    font-family: 'Merriweather', serif;
}
.showrank-modal .content-rankWrapper {
    background: #FFFFFF;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    margin-top: 1rem;
}
.showrank-modal .content-rankWrapper .profile {
    width: 92px;
    height: 92px;
    border-radius: 50%;
    border: 4px solid #FFFFFF;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
}
.showrank-modal .content-rankWrapper .profile img {
    width: 84px;
    height: 84px;
    border-radius: 50%;
}
.showrank-modal .content-rankWrapper .user-rank {
    text-align: center;
    font-weight: 500;
    font-family: 'Merriweather', serif;
    font-size: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}
.showrank-modal .content-rankWrapper .user-rank:before {
    content: url('../../../images/landingpageImages/badge.svg');
    width: 34px;
}
.showrank-modal .content-rankWrapper .yr-head {
    font-size: 8px;
    font-weight: 500;
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.4);
    text-transform: uppercase;
    text-align: center;
    margin: 0;
    margin-top: 8px;
}
.showrank-modal .content-rankWrapper .no-ques {
    font-size: 14px;
    font-weight: 500;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    text-align: center;
}
.showrank-modal .content-rankWrapper .rank-head {
    font-size: 12px;
    font-weight: 500;
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.4);
    text-transform: uppercase;
    text-align: center;
    margin: 0;
    margin-top: 8px;
}
.showrank-modal .content-rankWrapper .total-students {
    font-size: 10px;
    font-weight: normal;
    font-family: 'Rubik', sans-serif;
    color: rgba(68, 68, 68, 0.4);
    text-transform: uppercase;
    text-align: center;
}
.bookTemplate .tab-header > .navbar {
    position: fixed;
    background: #ffffff;
    height: 50px;
    width: 100%;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12);
    z-index: 99;
    top: 0;
}
.bookTemplate .tab-header a {
    max-width: 250px;
}
.bookTemplate .tab-header a.book-name {
    color: rgba(68, 68, 68, 0.72);
    font-family: 'Rubik', sans-serif;
    font-size: 12px;
}
.bookTemplate .menu {
    border-bottom: none;
}
.bookTemplate .menu li:hover a {
    background: transparent;
    border: transparent;
}
.bookTemplate .menu li a {
    color: #444444;
    font-size: 16px;
    font-family: 'Rubik', sans-serif;
}
.bookTemplate .menu.nav-tabs .nav-item.show .nav-link {
    color: #1F419B;
    background: transparent;
    border: none;
    border-bottom: 2px solid #1F419B;
}
.bookTemplate .menu.nav-tabs .nav-link.active {
    color: #1F419B;
    background: transparent;
    border: none;
    border-bottom: 2px solid #1F419B;
}
.bookTemplate .content-wrapper {
    height: calc(100vh - 50px);
    margin-top: 50px;
}
.bookTemplate .content-wrapper #book-sidebar {
    width: 250px;
    background: #f5f5f5;
    height: calc(100vh - 50px);
    position: relative;
}
.bookTemplate .content-wrapper #book-sidebar.closed i {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}
.bookTemplate .content-wrapper #book-sidebar.closed .side-content {
    display: none;
}
.bookTemplate .content-wrapper #book-sidebar i {
    position: absolute;
    right: -24px;
    height: 48px;
    width: 24px;
    top: 2rem;
    background: #EEEEEE;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #6F6F6F;
}
.bookTemplate .content-wrapper #book-sidebar .side-content h2 {
    color: rgba(68, 68, 68, 0.4);
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    font-weight: normal;
    padding: 1rem;
    border-bottom: 0.5px solid rgba(68, 68, 68, 0.2);
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol li a {
    color: rgba(68, 68, 68, 0.4);
    font-family: 'Rubik', sans-serif;
    font-size: 16px;
    font-weight: 500;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol li a.active {
    color: #1F419B;
}
/*Library*/
.library {
    padding-top: 4rem;
    min-height: 100vh;
    background: #F5F5F5;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .library {
        margin-top: 2rem;
    }
}
.library .nav-tabs {
    border: none;
}
.library .nav-tabs .nav-link {
    cursor: pointer;
    font-size: 26px;
    font-weight: 500;
    color: rgba(68, 68, 68, 0.7);
    font-family: 'Merriweather', serif;
}
.library .nav-tabs .nav-link.active {
    border: none;
    background: none;
    border-bottom: 3px solid #1F419B;
    color: #444444;
}
.library .nav-tabs .nav-link:hover {
    border: none;
    border-bottom: 3px solid #1F419B;
}
.library .nav-tabs li:last-child {
    margin-left: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .library .nav-tabs li:last-child {
        margin-left: 0;
    }
}
.library .username p {
    font-size: 32px;
    font-family: 'Merriweather', serif;
    font-weight: bold;
    color: rgba(68, 68, 68, 0.24);
    margin-bottom: 0.5rem;
}
.library .username p span {
    color: #444444;
    text-transform: capitalize;
}
.library .username p:last-child {
    font-size: 20px;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    color: rgba(68, 68, 68, 0.72);
}
.library .generate {
    background: #ffffff;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
    border-radius: 600px;
    color: #444444;
}
.library .generate img {
    width: 24px;
}
.library .tab-content {
    padding-bottom: 2rem;
    margin-top: 2rem;
}
.library .tab-content h4 {
    font-family: 'Rubik', sans-serif;
    font-size: 20px;
    color: #444444;
    padding: 10px;
}
.library .tab-content a:hover {
    text-decoration: none;
}
.library .tab-content .card {
    padding: 10px;
    width: 175px;
    border: none;
    background: none;
}
.library .tab-content .card > div.share-wrapper {
    position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .library .tab-content .card {
        width: 100%;
    }
}
.library .tab-content .card img {
    width: 154px;
    height: 192px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .library .tab-content .card img {
        height: 100%;
        width: 100%;
    }
}
.library .tab-content .card .card-body {
    padding: 0;
}
.library .tab-content .card .card-body .card-text {
    color: #444444;
    margin-top: 0.5rem;
    width: 152px;
    height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .library .tab-content .card .card-body .card-text {
        width: 100%;
    }
}
.library .tab-content .card .card-body .card-text:hover {
    color: #1F419B;
    text-decoration: underline;
}
.library .tab-content .card:hover .share-content {
    display: block;
}
.library .tab-content .dropup .dropdown-toggle::after {
    display: none;
}
.library .tab-content .dropdown-menu {
    min-width: 100px;
    top: 15px !important;
}
.library .tab-content .dropdown-menu a.dropdown-item {
    color: darkgreen;
}
.library .tab-content .dropdown-menu a.dropdown-item:last-child {
    color: #B72319;
}
.library #recentRead #recentBooks a .item .content-wrapper {
    height: auto;
}
.books-content-wrapper {
    margin-top: 2rem;
}
.books-content-wrapper h4 {
    font-family: 'Rubik', sans-serif;
    font-size: 20px;
    color: #444444;
    padding: 10px;
}
.books-content-wrapper a:hover {
    text-decoration: none;
}
.books-content-wrapper .card {
    padding: 10px;
    width: 175px;
    border: none;
    background: none;
}
.books-content-wrapper .card > div.share-wrapper {
    position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .books-content-wrapper .card {
        width: 100%;
    }
}
.books-content-wrapper .card img {
    width: 154px;
    height: 192px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .books-content-wrapper .card img {
        height: 100%;
        width: 100%;
    }
}
.books-content-wrapper .card .card-body {
    padding: 0;
}
.books-content-wrapper .card .card-body .card-text {
    color: #444444;
    margin-top: 0.5rem;
    width: 152px;
    height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .books-content-wrapper .card .card-body .card-text {
        width: 100%;
    }
}
.books-content-wrapper .card .card-body .card-text:hover {
    color: #1F419B;
    text-decoration: underline;
}
.books-content-wrapper .card:hover .share-content {
    display: block;
}
.books-content-wrapper .dropup .dropdown-toggle::after {
    display: none;
}
.books-content-wrapper .dropdown-menu {
    min-width: 100px;
    top: 15px !important;
}
.books-content-wrapper .dropdown-menu a.dropdown-item {
    color: darkgreen;
}
.books-content-wrapper .dropdown-menu a.dropdown-item:last-child {
    color: #B72319;
}
.lib-showcase {
    position: relative;
    z-index: 9;
}
.share-content {
    display: none;
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(68, 68, 68, 0.5);
    z-index: 999;
}
.btn-none {
    background: none;
    border: none;
    outline: 0;
    display: none;
}
.btn-none:hover {
    color: #1F419B;
}
.btn-none:focus {
    outline: none;
}
#classroom #libraryAssignment .media {
    padding: 1rem;
    background: #ffffff;
    border-left: 5px solid #1F70B5;
    margin-bottom: 2rem;
    border-radius: 3px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
#classroom #libraryAssignment .media .media-body p {
    margin-bottom: 0;
}
#classroom #libraryAssignment .media .media-body h4 {
    color: #444444;
    font-size: 20px;
    font-family: 'Rubik', sans-serif;
    font-weight: normal;
    margin-left: 0.5rem;
    text-transform: capitalize;
    padding: 0;
}
#classroom #libraryAssignment .media .media-body .instructor-name {
    margin-top: 2rem;
    border-bottom: 1px solid rgba(68, 68, 68, 0.5);
}
#classroom #libraryAssignment .media .media-body .instructor-name p {
    color: #444444;
    font-size: 16px;
}
#classroom #libraryAssignment .media .media-body .instructor-name p span {
    color: rgba(68, 68, 68, 0.7);
    margin-right: 0.5rem;
}
#classroom #libraryAssignment .media .media-body .assignment-done {
    display: flex;
    align-items: center;
    color: #30C465;
    width: 50%;
    justify-content: center;
    margin-left: -1.8rem;
}
#classroom #libraryAssignment .media .media-body .assignment-done i {
    padding-right: 0.5rem;
    color: #30C465;
}
#classroom #libraryAssignment .media .media-body .not-done {
    color: rgba(68, 68, 68, 0.9);
    width: 50%;
    justify-content: center;
    margin-left: -0.8rem;
}
#classroom #libraryAssignment .media .media-body .not-done i {
    padding-right: 0.5rem;
    color: rgba(68, 68, 68, 0.9);
}
#classroom #libraryAssignment .media .media-body .mark-complete {
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(68, 68, 68, 0.9);
    background: none;
    outline: 0;
    font-weight: normal;
    font-family: 'Rubik', sans-serif;
    width: 50%;
}
#classroom #libraryAssignment .media .media-body .mark-complete.complete i {
    color: #30C465;
}
#classroom #libraryAssignment .media .media-body .mark-complete.complete span {
    color: #30C465;
}
#classroom #libraryAssignment .media .media-body .mark-complete span {
    font-size: 14px;
}
#classroom #libraryAssignment .media .media-body .mark-complete i {
    padding-right: 0.5rem;
    color: rgba(68, 68, 68, 0.9);
}
#classroom #libraryAssignment .media .media-body .mark-complete:hover {
    color: #000;
}
#classroom #libraryAssignment .media .media-body .attempt-assignment-btn {
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
    color: #ffffff;
    font-weight: 500;
    border: none;
    width: 120px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    #classroom #libraryAssignment .media .media-body .attempt-assignment-btn {
        width: auto;
    }
}
#classroom #libraryAssignment .media .media-body div button {
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
    color: #ffffff;
    font-weight: 500;
    border: none;
}
#classroom #libraryAssignment .media .media-body div .button-wrapper {
    width: 50%;
    text-align: center;
    border-left: 1px solid rgba(68, 68, 68, 0.4);
    height: 34px;
}
#classroom #libraryAssignment .media .media-body div .button-wrapper p {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}
#allindiatest .testFilter {
    background: #ffffff;
}
#allindiatest .testFilter h3 {
    font-size: 14px;
    color: rgba(68, 68, 68, 0.5);
    border-bottom: 0.5px solid rgba(68, 68, 68, 0.5);
    font-weight: normal;
    font-family: 'Rubik', sans-serif;
    padding: 0.7rem 0.5rem;
}
#allindiatest .testFilter ul {
    padding: 0.5rem 1.5rem;
    list-style: none;
    counter-reset: li;
}
#allindiatest .testFilter ul li {
    padding: 0rem 2px;
}
#allindiatest .testFilter ul li a {
    color: #444444;
    counter-increment: li;
    font-size: 16px;
    font-family: 'Rubik', sans-serif;
}
#allindiatest .testFilter ul li a.active {
    color: #1F419B;
}
#allindiatest .testFilter ul li a.active:before {
    color: #1F419B;
}
#allindiatest .testFilter ul li a:before {
    content: counter(li);
    width: 1em;
    margin-left: -1em;
    font-size: 16px;
    font-family: 'Rubik', sans-serif;
    color: #444444;
    position: relative;
    left: -1rem;
}
#allindiatest .test-wrapper [data-state="live"] .card .livelabel {
    display: block;
}
#allindiatest .test-wrapper [data-state="live"] .card .btn-wrappers.startTest {
    display: block;
}
#allindiatest .test-wrapper [data-state="live"] .card .btn-wrappers.showRank {
    display: block;
}
#allindiatest .test-wrapper [data-state="live"] .card .btn-wrappers.showRank .btn-test {
    display: none;
}
#allindiatest .test-wrapper [data-state="showrankonly"] .card .btn-wrappers.showRank {
    display: block;
}
#allindiatest .test-wrapper [data-state="showrankonly"] .card .btn-wrappers.showRank .btn-test {
    display: none;
}
#allindiatest .test-wrapper [data-state="showrank"] .card .btn-wrappers.showRank {
    display: block;
}
#allindiatest .test-wrapper [data-state="registered"] .card .btn-wrappers.registered {
    display: block;
}
#allindiatest .test-wrapper:hover {
    box-shadow: none;
    background: none;
}
#allindiatest .test-wrapper .card {
    background: #ffffff;
    padding: 1rem 0;
    width: 152px;
    min-height: 192px;
    height: auto;
}
#allindiatest .test-wrapper .card .test-content > div {
    width: auto;
}
#allindiatest .test-wrapper .card .test-content p,
#allindiatest .test-wrapper .card .test-content span {
    text-align: left;
}
#allindiatest .test-wrapper .card .livelabel {
    width: 27px;
    height: 10px;
    text-align: center;
    background: #B72319;
    border-radius: 2px;
    display: none;
    font-size: 6px;
    color: #ffffff;
    float: right;
    font-family: 'Rubik', sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    position: absolute;
    top: 5px;
    right: 10px;
}
#allindiatest .test-wrapper .card > h4 {
    font-size: 14px;
    padding: 0;
}
#allindiatest .test-wrapper .card .btn-wrappers {
    text-align: center;
}
#allindiatest .test-wrapper .card .btn-wrappers.startTest,
#allindiatest .test-wrapper .card .btn-wrappers.showRank,
#allindiatest .test-wrapper .card .btn-wrappers.registered {
    display: none;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-test {
    background: linear-gradient(271.43deg, #30C465 23.2%, #3AE878 81.72%);
    color: #ffffff;
    width: 130px;
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-ranks {
    background: linear-gradient(271.43deg, #30C465 23.2%, #3AE878 81.72%);
    color: #ffffff;
    width: 130px;
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    background: none;
    border: 1px solid #1F419B;
    color: #1F419B;
    margin-bottom: 0.5rem;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-registers {
    background: linear-gradient(271.43deg, #30C465 23.2%, #3AE878 81.72%);
    color: #ffffff;
    width: 130px;
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    background: rgba(94, 199, 215, 0.6);
    display: flex;
    justify-content: center;
    margin: 0 auto;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-registers i {
    font-size: 16px;
    margin-right: 5px;
}
.ml-8 {
    margin-left: -8px;
}
/*Testgenerator*/
/*reset*/
#test-gen-modal {
    padding-right: 0 !important;
}
#test-gen-modal .modal-dialog {
    height: 100vh;
    max-width: 100vw;
    margin: 0;
    overflow: hidden;
}
#test-gen-modal .modal-dialog .modal-content {
    border: none;
    border-radius: 0;
}
#test-gen-modal .modal-dialog .modal-content .modal-header {
    position: relative;
    z-index: 9;
    height: 48px;
    border: none;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12);
}
#test-gen-modal .modal-dialog .modal-content .modal-header h4 {
    font-size: 16px;
    color: rgba(68, 68, 68, 0.72);
    font-family: 'Rubik', sans-serif;
    font-weight: normal;
}
#test-gen-modal .modal-dialog .modal-content .modal-header .close {
    padding: 0;
    margin: 0;
}
#test-gen-modal .modal-dialog .modal-content .modal-header .test-text {
    color: #fff;
}
#test-gen-modal .modal-dialog .modal-content .modal-body {
    padding: 0;
}
#test-gen-modal .modal-dialog .modal-content .modal-body .test-gen-books {
    height: calc(100vh - 120px);
    overflow: hidden;
    overflow-y: auto;
}
#test-gen-modal .modal-dialog .modal-content .modal-footer {
    position: relative;
    z-index: 9;
    height: 72px;
    box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24);
    border: none;
}
#test-gen-modal .next-btn {
    width: 140px;
    height: 40px;
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    border-radius: 4px;
    color: #ffffff;
}
#test-gen-modal .previous-btn {
    color: rgba(68, 68, 68, 0.72);
}
.overlay-testgen-book {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
}
.overlay-testgen-book .book-selected {
    text-align: center;
}
.overlay-testgen-book .book-selected i {
    position: relative;
    top: 6rem;
    color: #1F419B;
    font-size: 32px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .overlay-testgen-book .book-selected i {
        top: 5rem;
    }
}
.test-gen-chapters {
    height: calc(100vh - 120px);
    overflow: hidden;
    overflow-y: auto;
}
