<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090622 at Fri May 29 10:04:33 2020
 By deploy user
&#194;&#169; 2000, 2004 Adobe Systems Incorporated. All Rights Reserved. U.S. Patent D454,582.
</metadata>
<defs>
<font id="MyriadPro-Semibold" horiz-adv-x="300" >
  <font-face 
    font-family="Myriad Pro Light"
    font-weight="600"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 11 6 3 3 4 3 2 2 4"
    ascent="750"
    descent="-250"
    x-height="487"
    cap-height="674"
    bbox="-161 -250 1198 972"
    underline-thickness="50"
    underline-position="-50"
    unicode-range="U+0020-U+FB04"
  />
<missing-glyph horiz-adv-x="500" 
d="M0 0v700h500v-700h-500zM250 395l170 255h-340zM280 350l170 -255v510zM80 50h340l-170 255zM50 605v-510l170 255z" />
    <glyph glyph-name="f_f_j" unicode="ffj" horiz-adv-x="852" 
d="M747 693l-29 -93q-44 23 -100 23q-113 0 -113 -116v-20h290v-435q0 -145 -64 -208q-60 -57 -171 -57l-12 96q65 5 92 35q18 18 25 54.5t7 114.5v308h-166v-395h-124v395h-179v-395h-123v395h-66v92h66v12q0 102 61 160q51 49 136 49q45 0 85 -19l-29 -89q-24 13 -52 13
q-33 0 -52 -21q-27 -28 -27 -90v-15h180v23q0 98 64 154.5t172 56.5q78 0 129 -28z" />
    <glyph glyph-name="f_j" unicode="fj" horiz-adv-x="557" 
d="M203 0h-123v395h-66v92h66v23q0 98 63.5 154.5t172.5 56.5q86 0 134 -31l-27 -93q-46 26 -107 26q-114 0 -114 -116v-20h291v-435q0 -144 -64 -208q-61 -57 -172 -57l-12 96q66 5 92 35q18 18 25 54.5t7 114.5v308h-166v-395z" />
    <glyph glyph-name="f_f" unicode="ff" horiz-adv-x="620" 
d="M504 0h-124v395h-177v-395h-123v395h-66v92h66v10q0 106 55 161q48 50 137 50q37 0 67 -14l-20 -91q-22 9 -44 9q-73 0 -73 -108v-17h178v21q0 110 60 166q50 47 127 47q45 0 78 -12l-8 -95q-25 9 -53 9q-81 0 -81 -111v-25h106v-92h-105v-395z" />
    <glyph glyph-name="f_i" unicode="fi" horiz-adv-x="559" 
d="M203 0h-123v395h-66v92h66v23q0 98 63.5 154.5t172.5 56.5q86 0 134 -31l-27 -93q-46 26 -107 26q-114 0 -114 -116v-20h291v-487h-124v395h-166v-395z" />
    <glyph glyph-name="f_l" unicode="fl" horiz-adv-x="559" 
d="M203 0h-123v395h-66v92h66v21q0 101 72 161q64 52 172 52q101 0 169 -37v-684h-124v617q-20 11 -64 11q-53 0 -78 -33t-25 -86v-22h100v-92h-99v-395z" />
    <glyph glyph-name="f_f_i" unicode="ffi" horiz-adv-x="861" 
d="M747 693l-29 -93q-44 23 -100 23q-113 0 -113 -116v-20h290v-487h-123v395h-166v-395h-124v395h-179v-395h-123v395h-66v92h66v12q0 102 61 160q51 49 136 49q45 0 85 -19l-29 -89q-24 13 -52 13q-33 0 -52 -21q-27 -28 -27 -90v-15h180v23q0 98 64 154.5t172 56.5
q78 0 129 -28z" />
    <glyph glyph-name="f_f_l" unicode="ffl" horiz-adv-x="861" 
d="M363 692l-28 -91q-26 12 -52 12q-34 0 -54 -22q-27 -27 -27 -88v-16h180v21q0 100 72 161q64 52 172 52q103 0 169 -37v-684h-123v617q-20 11 -64 11q-53 0 -78 -33t-25 -86v-22h100v-92h-99v-395h-124v395h-179v-395h-123v395h-66v92h66v8q0 105 59 161q54 52 146 52
q41 0 78 -16z" />
    <glyph glyph-name=".notdef" horiz-adv-x="500" 
d="M0 0v700h500v-700h-500zM250 395l170 255h-340zM280 350l170 -255v510zM80 50h340l-170 255zM50 605v-510l170 255z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="333" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="207" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="251" 
d="M172 208h-94l-18 466h130zM125 -11h-1q-33 0 -54 22t-20 56q0 34 21 56t54 22q34 0 54.5 -21.5t21.5 -56.5q0 -34 -21 -56t-55 -22z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="370" 
d="M42 692h113l-20 -257h-73zM215 692h113l-20 -257h-73z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="526" 
d="M207 264h97l17 127h-97zM169 0h-76l26 186h-82v78h94l18 127h-86v77h97l26 182h75l-25 -182h97l26 182h75l-25 -182h81v-77h-93l-17 -127h84v-77h-97l-25 -187h-77l26 187h-96z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="536" 
d="M304 -86h-82v97q-101 3 -162 44l27 94q75 -44 156 -44q48 0 77 21.5t29 57.5q0 33 -26 56.5t-83 44.5q-86 31 -129.5 72t-43.5 107q0 65 43 111t118 59v98h82v-93q77 -2 138 -33l-27 -92q-74 34 -136 34q-49 0 -72 -20.5t-23 -49.5q0 -30 27 -50.5t94 -47.5
q85 -32 123.5 -75.5t38.5 -109.5t-44.5 -115.5t-124.5 -62.5v-103z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="840" 
d="M199 661h1q76 0 119.5 -52.5t43.5 -140.5q0 -98 -49 -152.5t-120 -54.5t-118 52.5t-48 143.5q0 92 47.5 148t123.5 56zM197 592h-1q-35 0 -54.5 -37t-19.5 -94q-1 -58 19 -94t56 -36t54.5 35t18.5 96q0 130 -73 130zM265 -12h-70l378 673h70zM647 392h1q76 0 119.5 -52.5
t43.5 -141.5q0 -98 -48.5 -152t-120.5 -54q-70 0 -117.5 52.5t-47.5 143.5t47.5 147.5t122.5 56.5zM645 322h-1q-35 0 -54.5 -37t-19.5 -94q-1 -57 19 -93.5t56 -36.5q73 0 73 131q0 130 -73 130z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="645" 
d="M641 0h-144q-23 23 -53 56q-79 -67 -190 -67q-106 0 -164.5 56t-58.5 134q0 126 130 195v3q-54 64 -54 135q0 69 50.5 121t138.5 52q72 0 119.5 -41.5t47.5 -109.5q0 -107 -139 -176l-1 -4l136 -154q42 65 61 179h110q-29 -166 -105 -253zM272 79h1q68 0 112 46
q-70 73 -168 187q-69 -47 -69 -113q0 -51 34.5 -85.5t89.5 -34.5zM290 606h-1q-33 0 -52 -24t-19 -58q0 -52 49 -108q46 29 68 55t22 59q0 30 -17 53t-50 23z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="198" 
d="M42 692h113l-20 -257h-73z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="301" 
d="M183 693h90q-113 -168 -113 -407q0 -232 113 -404h-90q-121 170 -121 404q1 237 121 407z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="301" 
d="M117 -118h-89q112 169 112 406q0 234 -112 405h89q122 -168 122 -406q-1 -236 -122 -405z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="437" 
d="M277 685l76 -43l-102 -126v-2l156 26v-86l-157 24v-3l103 -122l-77 -44l-59 148l-2 -1l-61 -148l-72 45l101 123v3l-153 -25v86l151 -26l1 3l-100 124l76 44l58 -147h3z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="596" 
d="M257 532h82v-226h217v-78h-217v-228h-82v228h-217v78h217v226z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="236" 
d="M92 -113l-84 -8q46 129 67 264l129 10q-48 -161 -112 -266z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="315" 
d="M30 309h255v-85h-255v85z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="236" 
d="M127 -11h-1q-33 0 -54 22.5t-21 56.5t21.5 56.5t54.5 22.5q34 0 55 -22t21 -57q0 -34 -21 -56.5t-55 -22.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="337" 
d="M94 -40h-86l244 725h86z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="536" 
d="M272 661q114 0 172 -90.5t58 -241.5q0 -159 -61 -249.5t-176 -90.5q-110 0 -170 91t-61 243q0 154 63 246t175 92zM269 567q-51 0 -81.5 -62.5t-29.5 -181.5q-1 -116 29 -178t82 -62q54 0 82 63t28 181q0 240 -110 240z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="536" 
d="M236 0v539h-2l-119 -60l-21 93l158 78h103v-650h-119z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="536" 
d="M484 0h-441v75l77 71q125 116 175 180.5t51 125.5q0 48 -28 78.5t-88 30.5q-68 0 -139 -54l-36 87q83 67 200 67q103 0 158.5 -56t55.5 -141q0 -79 -48.5 -149.5t-146.5 -161.5l-57 -49v-2h267v-102z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="536" 
d="M40 34l28 93q67 -40 148 -40q66 0 99 30.5t32 73.5q0 54 -42.5 82t-104.5 28h-57v90h55q50 0 89.5 23t39.5 68q0 35 -26.5 58t-77.5 23q-71 0 -133 -41l-28 89q30 21 81 35.5t106 14.5q96 0 149.5 -46t53.5 -113q0 -52 -30.5 -91.5t-89.5 -60.5v-2q62 -11 102.5 -54.5
t40.5 -107.5q0 -85 -68 -141t-185 -56q-114 0 -182 45z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="536" 
d="M430 0h-116v165h-294v80l265 405h145v-392h84v-93h-84v-165zM137 258h177v177q0 62 4 113h-4q-30 -61 -57 -110l-119 -178z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="536" 
d="M456 650v-102h-250l-19 -130q23 3 47 3q94 0 158 -42q85 -54 85 -165q0 -95 -73 -160t-190 -65q-104 0 -172 38l25 93q63 -34 143 -34q58 0 99.5 31.5t41.5 87.5q0 58 -45.5 90.5t-139.5 32.5q-50 0 -89 -6l43 328h336z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="536" 
d="M438 659l1 -96q-38 0 -63 -3q-96 -11 -150.5 -63.5t-67.5 -126.5h3q54 63 146 63q86 0 142.5 -57.5t56.5 -154.5q0 -96 -64 -164t-165 -68q-115 0 -180 78t-65 202q0 184 113 292q90 84 231 96q35 4 62 2zM276 81h1q47 0 75.5 37t28.5 96q0 58 -30 93.5t-83 35.5
q-34 0 -62.5 -18.5t-43.5 -48.5q-8 -16 -8 -39q2 -68 33.5 -112t88.5 -44z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="536" 
d="M53 650h437v-79l-275 -571h-130l274 546v2h-306v102z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="536" 
d="M156 336v3q-99 48 -99 144q0 79 62 128.5t155 49.5q98 0 151 -49t53 -115q0 -44 -24.5 -83t-75.5 -64v-3q56 -21 90 -63.5t34 -100.5q0 -87 -66 -140.5t-171 -53.5q-108 0 -169.5 52.5t-61.5 125.5q0 115 122 169zM269 76q48 0 78 27.5t30 69.5q0 88 -121 122
q-46 -13 -71 -43t-25 -71q-2 -43 28.5 -74t80.5 -31zM268 576q-44 0 -69 -25.5t-25 -62.5q0 -73 106 -102q35 10 58 36.5t23 61.5q0 38 -23.5 65t-69.5 27z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="536" 
d="M93 -9v97q23 -2 68 2q78 6 132 50q65 53 83 144l-2 1q-52 -58 -141 -58q-86 0 -142 56.5t-56 145.5q0 95 66.5 163.5t168.5 68.5q110 0 171 -77t61 -200q0 -194 -113 -302q-86 -80 -222 -89q-38 -4 -74 -2zM264 570h-1q-46 0 -76 -37.5t-30 -96.5q1 -52 29.5 -85.5
t77.5 -33.5q72 0 105 54q7 14 7 35q1 71 -27 117.5t-85 46.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="236" 
d="M127 326h-1q-33 0 -54 22t-21 56t21.5 56.5t54.5 22.5t54 -22t22 -57q0 -34 -21 -56t-55 -22zM127 -11h-1q-33 0 -54 22.5t-21 55.5q0 34 21.5 56.5t54.5 22.5q34 0 54.5 -22t21.5 -57q0 -34 -21 -56t-55 -22z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="236" 
d="M92 -113l-84 -8q47 129 67 264l129 10q-51 -166 -112 -266zM133 326h-1q-33 0 -54 22t-20 56q0 35 21 57t54 22t54 -22t22 -57q0 -34 -21 -56t-55 -22z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="596" 
d="M61 230v72l474 230v-90l-376 -175v-2l376 -176v-89z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="596" 
d="M556 330h-516v79h516v-79zM556 128h-516v78h516v-78z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="596" 
d="M535 304v-76l-474 -228v89l382 176v2l-382 176v89z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="428" 
d="M248 207h-108l-2 25q-8 83 60 164q63 76 63 120q0 33 -20.5 51.5t-61.5 19.5q-61 0 -100 -30l-30 88q64 41 157 41q88 0 134.5 -43.5t46.5 -108.5q0 -34 -14.5 -67t-29 -52t-43.5 -52q-56 -66 -53 -135zM191 -11h-1q-33 0 -54 22.5t-21 55.5q0 34 21.5 56t54.5 22
q34 0 54.5 -21.5t21.5 -56.5q0 -34 -21 -56t-55 -22z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="755" 
d="M445 252l18 103q-20 6 -40 6q-56 0 -96 -47.5t-40 -111.5q0 -32 14.5 -50.5t41.5 -18.5q34 0 64 36.5t38 82.5zM519 -16l17 -51q-79 -38 -181 -38q-128 0 -218 86.5t-90 228.5q0 158 103 270t267 112q131 0 214 -81.5t83 -207.5q0 -108 -53 -173t-131 -65
q-35 0 -57.5 20.5t-23.5 61.5h-3q-49 -82 -134 -82q-48 0 -79.5 34.5t-31.5 93.5q0 96 66.5 165.5t169.5 69.5q68 0 114 -23l-32 -183q-18 -98 27 -98q39 -2 70.5 45.5t31.5 128.5q0 107 -63.5 173.5t-176.5 66.5q-122 0 -207.5 -88.5t-85.5 -232.5q0 -123 71.5 -194.5
t184.5 -71.5q85 0 148 33z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="576" 
d="M71 2v663q68 14 179 14q131 0 193 -43q70 -42 70 -126q0 -48 -29 -87t-83 -59v-2q58 -15 97 -58t39 -109q0 -86 -65 -139q-72 -63 -250 -63q-81 0 -151 9zM193 583v-184h63q63 0 98 26.5t35 70.5q0 92 -126 92q-48 0 -70 -5zM193 309v-220q16 -3 65 -3q65 0 107 27.5
t42 85.5q0 56 -42.5 83t-110.5 27h-61z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="588" 
d="M534 117l20 -97q-60 -31 -174 -31q-159 0 -251.5 92.5t-92.5 247.5q0 163 101 259.5t259 96.5q106 0 164 -30l-27 -98q-59 26 -132 26q-106 0 -171 -65t-65 -183q0 -112 62.5 -177.5t172.5 -65.5q81 0 134 25z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="683" 
d="M71 2v663q84 14 196 14q182 0 275 -78q105 -86 105 -248q0 -173 -105 -268q-101 -92 -305 -92q-94 0 -166 9zM194 576v-482q17 -3 68 -3q121 -1 188 65t67 193q1 112 -61.5 173t-177.5 61q-54 0 -84 -7z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="509" 
d="M71 0v674h389v-101h-266v-186h248v-101h-248v-286h-123z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="666" 
d="M611 372v-341q-102 -38 -217 -38q-170 0 -264 90q-96 91 -94 248q0 159 103.5 254.5t271.5 95.5q113 0 179 -32l-27 -99q-67 29 -153 29q-111 0 -178 -63.5t-67 -178.5q0 -113 64 -178.5t170 -65.5q65 0 93 14v168h-119v97h238z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="672" 
d="M71 674h123v-270h285v270h123v-674h-123v297h-285v-297h-123v674z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="392" 
d="M205 241v433h123v-437q0 -133 -58.5 -190.5t-165.5 -57.5q-61 0 -103 16l15 99q40 -12 76 -12q56 0 84.5 33t28.5 116z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="582" 
d="M71 0v674h122v-310h3q14 22 49 73l176 237h152l-232 -287l247 -387h-144l-191 309l-60 -72v-237h-122z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="493" 
d="M71 0v674h123v-571h277v-103h-400z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="827" 
d="M653 0l-13 275q-9 195 -8 284h-3q-34 -130 -80 -259l-98 -292h-95l-90 288q-41 136 -68 263h-2q-6 -177 -12 -288l-15 -271h-115l45 674h162l88 -271q35 -116 62 -234h3q31 125 67 235l93 270h160l39 -674h-120z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-674h-127l-176 297q-77 131 -121 232l-3 -1q6 -104 6 -287v-241z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="559" 
d="M71 0v665q77 14 188 14q133 0 199 -55q66 -54 66 -149q0 -98 -58 -153q-72 -72 -213 -72q-39 0 -60 4v-254h-122zM193 579v-228q19 -5 60 -5q69 0 108.5 32.5t39.5 91.5q0 56 -36 85.5t-101 29.5q-45 0 -71 -6z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="704" 
d="M673 -11l-35 -97q-135 37 -263 87q-27 9 -44 10q-125 6 -210 96t-85 246q0 157 89 255.5t233 98.5q143 0 227 -96.5t84 -243.5q0 -118 -48 -199t-126 -112v-4q71 -20 178 -41zM352 87h1q86 0 136 71.5t50 180.5q0 103 -49 175.5t-136 72.5q-89 0 -139 -73t-49 -180
q-1 -105 49.5 -176t136.5 -71z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="569" 
d="M71 0v665q81 14 186 14q144 0 208 -54q58 -49 58 -135q0 -61 -35 -105.5t-87 -63.5v-3q67 -25 94 -130q42 -167 53 -188h-127q-17 33 -45 159q-14 65 -41 90.5t-80 27.5h-62v-277h-122zM193 580v-213h73q62 0 98.5 30.5t36.5 80.5q0 54 -35 81t-97 27q-52 0 -76 -6z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="519" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -87 -66 -145t-190 -58
q-51 0 -101.5 12.5t-80.5 30.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="525" 
d="M200 0v571h-192v103h509v-103h-194v-571h-123z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="601" 
d="M366 0h-141l-218 674h134l92 -306q42 -139 66 -246h2q24 109 69 244l98 308h131z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="869" 
d="M313 0h-133l-165 674h131l64 -306q26 -126 44 -235h2q8 55 49 236l73 305h129l67 -310q27 -129 41 -228h2q15 90 46 233l70 305h125l-181 -674h-132l-70 317q-27 122 -37 214h-2q-19 -114 -45 -214z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="594" 
d="M574 0h-142l-73 134q-41 73 -67 127h-3q-19 -45 -61 -127l-67 -134h-141l200 341l-192 333h141l73 -139q26 -49 56 -113h2q25 58 53 113l72 139h141l-197 -329z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="566" 
d="M26 0v68l345 501v3h-314v102h474v-72l-340 -496v-4h345v-102h-510z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="301" 
d="M269 -112h-195v798h195v-71h-104v-656h104v-71z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="335" 
d="M327 -40h-86l-234 725h86z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="301" 
d="M32 686h194v-798h-194v71h103v656h-103v71z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="596" 
d="M546 182h-92l-155 371h-3l-155 -371h-91l209 468h78z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="500" 
d="M0 -75h500v-50h-500v50z" />
    <glyph glyph-name="grave" unicode="`" 
d="M12 698h121l87 -148h-86z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="585" 
d="M66 137v573h123v-290h2q50 78 156 78q90 0 147 -68.5t56 -178.5q0 -124 -65.5 -193t-155.5 -69q-105 0 -154 84h-2l-6 -73h-105q4 66 4 137zM189 280v-79q0 -15 3 -29q10 -37 40 -61.5t69 -24.5q58 0 91 43t33 117q0 68 -32 112t-91 44q-38 0 -68.5 -25t-40.5 -65
q-4 -18 -4 -32z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="449" 
d="M407 106l17 -91q-56 -26 -138 -26q-114 0 -182.5 68t-68.5 181t73 186.5t198 73.5q69 0 119 -23l-22 -92q-42 19 -93 19q-69 0 -109.5 -45t-39.5 -113q0 -73 42 -115t107 -42q51 0 97 19z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="581" 
d="M392 710h123v-573q0 -71 4 -137h-110l-5 77h-2q-22 -41 -64 -64.5t-95 -23.5q-89 0 -148.5 69t-59.5 180q-1 118 62.5 189t155.5 71q48 0 84 -18t53 -46h2v276zM392 211v73q0 17 -3 32q-9 38 -37 62.5t-69 24.5q-58 0 -90.5 -45t-32.5 -116q0 -68 32.5 -111t89.5 -43
q38 0 67.5 24t38.5 63q4 15 4 36z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="319" 
d="M203 0h-123v395h-66v92h66v21q0 110 59 166q50 47 127 47q45 0 79 -12l-9 -95q-23 9 -52 9q-82 0 -82 -111v-25h107v-92h-106v-395z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="573" 
d="M507 347v-276q0 -156 -72 -221q-67 -59 -188 -59q-107 0 -170 39l27 93q64 -37 142 -37q64 0 102 36t38 111v42h-2q-47 -72 -142 -72q-91 0 -149 67t-58 171q0 116 65 186.5t156 70.5q97 0 141 -76h2l5 65h107q-4 -66 -4 -140zM384 213v79q0 20 -4 33q-10 35 -36 57
t-64 22q-53 0 -86.5 -43t-33.5 -116q0 -65 31.5 -107.5t87.5 -42.5q34 0 61.5 20.5t38.5 53.5q5 22 5 44z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="572" 
d="M66 0v710h124v-290h2q24 37 61 56q38 22 84 22q71 0 120.5 -51.5t49.5 -159.5v-287h-123v273q0 124 -94 124q-34 0 -59.5 -19.5t-35.5 -49.5q-5 -12 -5 -37v-291h-124z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="256" 
d="M190 0h-124v487h124v-487zM128 690q31 0 49.5 -19t19.5 -48q0 -28 -19 -47t-51 -19q-30 0 -49 19t-19 47q0 29 19.5 48t49.5 19z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="270" 
d="M-30 -213l-12 96q65 5 92 35q18 18 25 54.5t7 114.5v400h123v-435q0 -145 -63 -208q-61 -57 -172 -57zM144 690q31 0 49.5 -19t18.5 -48q0 -28 -19 -47t-51 -19q-30 0 -48.5 19t-18.5 47q0 29 19 48t50 19z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="509" 
d="M189 710v-437h2q22 34 39 57l118 157h148l-180 -199l206 -288h-151l-140 215l-42 -49v-166h-123v710h123z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="257" 
d="M66 0v710h124v-710h-124z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="848" 
d="M66 0v342q0 89 -4 145h106l5 -72h3q52 83 150 83q47 0 83 -24.5t52 -65.5h2q25 40 60 61q41 29 98 29q69 0 116 -51.5t47 -159.5v-287h-120v269q0 129 -87 129q-30 0 -53 -18.5t-33 -46.5q-6 -24 -6 -41v-292h-120v282q0 53 -21.5 84.5t-62.5 31.5q-32 0 -55.5 -21
t-32.5 -49q-7 -17 -7 -40v-288h-120z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="572" 
d="M66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-289h-123v275q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="585" 
d="M66 -198v523q0 70 -4 162h108l6 -75h2q56 86 167 86q86 0 145.5 -68.5t59.5 -179.5q0 -124 -65.5 -192.5t-156.5 -68.5q-45 0 -81.5 17.5t-55.5 47.5h-2v-252h-123zM189 279v-75q0 -19 4 -34q9 -38 39 -62t69 -24q58 0 91 44t33 117q0 67 -32 111.5t-89 44.5
q-39 0 -70 -25.5t-40 -65.5q-5 -17 -5 -31z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="581" 
d="M392 -198v265h-2q-48 -78 -151 -78q-85 0 -144.5 68t-59.5 179q0 125 66 193.5t152 68.5q100 0 142 -75l2 -1l3 65h119q-4 -88 -4 -141v-544h-123zM392 205v81q0 16 -3 30q-9 36 -37 60.5t-68 24.5q-58 0 -91 -44t-33 -115q0 -69 32 -112.5t89 -43.5q37 0 65.5 22
t39.5 59q6 18 6 38z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="356" 
d="M66 0v330q0 98 -4 157h107l4 -93h4q17 50 54.5 77t78.5 27q14 0 29 -3v-116q-20 4 -36 4q-43 0 -72.5 -25.5t-37.5 -67.5q-4 -22 -4 -38v-252h-123z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="417" 
d="M36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-136 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q66 -24 97.5 -59.5t32.5 -89.5q0 -68 -52 -110.5t-146 -42.5
q-85 0 -148 35z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="351" 
d="M87 577l120 35v-125h117v-92h-117v-215q0 -46 14.5 -68t48.5 -22q27 0 48 6l2 -94q-33 -13 -89 -13q-68 0 -106 41t-38 136v229h-70v92h70v90z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="569" 
d="M502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="508" 
d="M11 487h133l79 -247q18 -55 33 -122h3q10 43 33 122l77 247h130l-186 -487h-121z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="749" 
d="M14 487h126l51 -221q21 -100 31 -158h2q8 43 40 157l63 222h100l61 -216q24 -93 39 -163h2q10 67 32 163l54 216h121l-153 -487h-114l-58 198q-22 74 -37 156h-2q-11 -68 -37 -156l-62 -198h-115z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="494" 
d="M11 487h137l58 -92l44 -75h3q14 27 42 77l54 90h134l-163 -234l166 -253h-139l-60 97q-19 31 -44 79h-2q-15 -30 -44 -79l-56 -97h-136l169 248z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="500" 
d="M8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="450" 
d="M20 0v71l183 234q36 44 72 82v2h-237v98h387v-75l-179 -229q-48 -58 -72 -83v-2h257v-98h-411z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="301" 
d="M29 255v65q47 1 62.5 20.5t15.5 50.5q0 25 -6 81q-7 47 -7 81q0 67 39.5 100t109.5 33h29v-71h-18q-70 -1 -70 -78q0 -25 7 -64q6 -66 6 -72q1 -93 -80 -112v-2q81 -19 80 -113q0 -7 -6 -73q-7 -39 -7 -63q0 -78 70 -79h18v-71h-29q-149 0 -149 137q0 33 7 80q6 54 6 79
q0 70 -78 71z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="263" 
d="M87 750h89v-1000h-89v1000z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="301" 
d="M271 320v-65q-78 -1 -78 -71q0 -16 7 -79t7 -80q0 -137 -149 -137h-29v71h18q69 1 69 79q0 19 -6 63t-6 73q-1 94 79 113v2q-80 19 -79 112q0 28 6 72t6 64q0 77 -69 78h-18v71h29q70 0 109.5 -33t39.5 -100q0 -18 -7 -81q-7 -65 -7 -81q0 -31 16 -50.5t62 -20.5z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="596" 
d="M125 200h-80q-1 85 35 128t96 43q32 0 61 -10.5t79 -36.5q78 -40 109 -40q45 0 46 82h80q4 -90 -31.5 -131t-93.5 -41q-50 0 -142 47l-21.5 11t-20.5 11t-17 8.5t-18 7l-14.5 3.5t-15.5 2q-48 0 -52 -84z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="207" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="251" 
d="M191 -192h-130l18 463h94zM126 491h1q33 0 53.5 -21.5t20.5 -56.5q0 -34 -20.5 -56t-54.5 -22q-33 0 -54.5 22.5t-21.5 55.5q0 35 21 56.5t55 21.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="536" 
d="M356 -13h-82v100q-92 11 -147 73q-56 66 -56 167q0 94 54.5 162.5t148.5 85.5v98h82v-94q53 -1 102 -22l-22 -92q-42 21 -96 21q-66 0 -105 -43.5t-39 -110.5q0 -72 40.5 -112.5t101.5 -40.5q58 0 106 21l17 -89q-39 -22 -105 -26v-98z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="536" 
d="M491 0h-438v67q48 22 80.5 65t32.5 94q0 29 -4 54h-100v85h86q-10 56 -10 93q0 92 58.5 147.5t152.5 55.5q72 0 114 -25l-22 -93q-35 20 -87 20t-76.5 -30t-24.5 -78q0 -37 11 -90h140v-85h-129q2 -58 -7 -89q-14 -49 -58 -88v-2h281v-101z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="536" 
d="M268 543h1q67 0 115 -38l74 80l57 -58l-83 -75q33 -50 33 -117q0 -72 -35 -121l80 -78l-56 -56l-71 82q-46 -41 -117 -41t-115 39l-69 -80l-56 56l78 75q-36 49 -36 120q0 67 36 121l-81 74l57 59l73 -81q45 39 115 39zM267 459h-1q-46 0 -75 -36t-29 -91
q0 -60 32.5 -93.5t71.5 -33.5q42 0 73 33t31 96q0 52 -28.5 88.5t-74.5 36.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="536" 
d="M320 0h-115v167h-156v61h156v68h-156v61h125l-166 293h133l84 -181q8 -17 42 -100h2q33 82 42 102l87 179h130l-175 -293h124v-61h-157v-68h157v-61h-157v-167z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="263" 
d="M87 174h89v-350h-89v350zM87 674h89v-350h-89v350z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="542" 
d="M148 347v-1q0 -52 96 -85q97 -35 116 -45q33 22 33 63q0 53 -78 81q-124 48 -127 50q-40 -26 -40 -63zM443 653l-22 -77q-56 30 -127 30q-43 0 -69.5 -16.5t-26.5 -43.5q0 -28 27 -45.5t88 -36.5q87 -27 132.5 -63.5t45.5 -103.5q0 -73 -69 -121q42 -35 42 -91
q0 -54 -33.5 -90.5t-78.5 -50.5t-96 -14q-109 0 -174 43l27 76q65 -41 143 -41q45 0 72.5 17t27.5 48q0 29 -25 48t-88 40q-93 30 -140.5 66t-47.5 100q0 33 20 65.5t59 54.5q-37 28 -39 87q0 70 57.5 110t145.5 40q85 0 149 -31z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" 
d="M51 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM250 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="677" 
d="M340 648h1q126 0 214 -90t88 -218q0 -129 -88 -219.5t-215 -90.5q-128 0 -217 90.5t-89 219.5q0 128 89 218t217 90zM339 598h-1q-103 0 -174.5 -75.5t-71.5 -184.5q0 -108 72 -183t175 -75q103 -1 174.5 74.5t71.5 184.5q0 108 -71.5 183.5t-174.5 75.5zM474 493
l-13 -48q-37 23 -97 23q-62 0 -96.5 -35.5t-34.5 -95.5q0 -58 35.5 -95t95.5 -37q63 0 101 25l14 -45q-47 -32 -124 -32q-85 0 -134.5 51t-49.5 130q0 83 57 134.5t136 51.5q35 0 67 -8.5t43 -18.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="363" 
d="M325 266h-79l-6 37h-2q-40 -42 -100 -42q-48 0 -76.5 27.5t-28.5 69.5q0 55 53 85.5t146 29.5v8q0 9 -4 18.5t-20 20t-43 10.5q-53 0 -89 -26l-19 55q51 32 124 32t105.5 -39t32.5 -101v-107q0 -46 6 -78zM234 377v44q-115 2 -115 -57q0 -20 14.5 -32t35.5 -12
q40 0 58 33q7 11 7 24z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="444" 
d="M241 440l-126 -188l126 -188h-90l-124 188l123 188h91zM421 440l-125 -188l125 -188h-90l-123 188l123 188h90z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="596" 
d="M40 412h516v-299h-82v221h-434v78z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="291" 
d="M30 309h255v-85h-255v85z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="441" 
d="M197 493v-61h-44v161q39 6 67 6q42 0 60 -13q18 -11 18 -35q0 -27 -36 -37v-2q24 -8 31 -41q6 -27 12 -39h-49q-5 6 -12 37q-4 24 -30 24h-17zM198 566v-42h17q33 0 33 21q0 22 -31 22q-4 0 -10.5 -0.5t-8.5 -0.5zM221 684h1q72 0 122 -49t50 -119q0 -71 -50 -120.5
t-123 -49.5q-72 0 -123 49.5t-51 120.5q0 70 51 119t123 49zM221 646h-1q-53 0 -89 -38.5t-36 -92.5t36 -92t90 -38q53 -1 89 37t36 93t-36 93t-89 38z" />
    <glyph glyph-name="macron" unicode="&#xaf;" 
d="M31 655h239v-71h-239v71z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="339" 
d="M171 685h1q63 0 102.5 -40.5t39.5 -97.5q0 -61 -42.5 -101.5t-101.5 -40.5q-62 0 -102 40.5t-40 97.5q-1 60 41 101t102 41zM171 626h-1q-33 0 -53.5 -25t-20.5 -58t21.5 -55.5t53.5 -22.5t53.5 23t21.5 57q0 32 -19.5 56.5t-55.5 24.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="596" 
d="M257 576h82v-183h217v-78h-217v-187h-82v187h-217v78h217v183zM40 78h516v-78h-516v78z" />
    <glyph glyph-name="two.superior" unicode="&#xb2;" horiz-adv-x="333" 
d="M14 443v48l62 56q68 62 94 94t26 64q0 26 -17 42t-49 16q-45 0 -85 -30l-26 63q55 42 135 42q69 0 106 -34t37 -83q0 -34 -19 -68t-40 -55t-63 -57l-28 -23v-3h157v-72h-290z" />
    <glyph glyph-name="three.superior" unicode="&#xb3;" horiz-adv-x="328" 
d="M46 745l-20 62q52 31 129 31q66 0 100.5 -27.5t34.5 -69.5q0 -30 -22 -53.5t-58 -35.5v-2q41 -5 68 -31t27 -63q0 -51 -46 -84.5t-124 -33.5q-81 0 -126 28l21 65q43 -26 95 -26q37 0 58 16.5t21 39.5q0 29 -28 44t-67 15h-29v58h28q32 0 57.5 12.5t25.5 38.5
q0 18 -14.5 30t-42.5 12q-45 0 -88 -26z" />
    <glyph glyph-name="acute" unicode="&#xb4;" 
d="M174 698h121l-123 -148h-85z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="570" 
d="M396 71h-3q-16 -32 -49 -55.5t-79 -23.5q-59 0 -86 40v-50q0 -131 10 -180h-109q-14 38 -14 177v508h123v-279q0 -55 22.5 -87t67.5 -32q34 0 59.5 20t35.5 46q7 14 7 38v294h123v-339q0 -36 9.5 -50t33.5 -16l-7 -86q-17 -7 -44 -7q-82 0 -100 82z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="529" 
d="M298 -48h-81v312h-7q-60 0 -118.5 50.5t-59.5 137.5q0 43 12.5 80.5t43 71.5t89.5 53.5t142 19.5q91 0 131 -10v-715h-81v646h-71v-646z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="236" 
d="M118 182h-1q-33 0 -54 22t-21 57q0 34 21.5 56.5t54.5 22.5q34 0 55 -22t21 -57t-21 -57t-55 -22z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" 
d="M128 3h69l-30 -50q30 -3 51 -22.5t21 -48.5q0 -43 -32 -63.5t-82 -20.5q-47 0 -75 19l18 50q27 -16 54 -16q39 0 39 29q0 33 -78 39z" />
    <glyph glyph-name="one.superior" unicode="&#xb9;" horiz-adv-x="275" 
d="M209 443h-96v310h-2l-76 -35l-14 69l104 46h84v-390z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="372" 
d="M187 591h1q73 0 118 -45.5t45 -116.5q-1 -81 -50 -124.5t-116 -43.5q-70 0 -117 45t-47 117q0 75 47.5 121.5t118.5 46.5zM185 528h-1q-36 0 -55 -31.5t-19 -71.5q0 -43 21.5 -72t54.5 -29q34 0 54.5 28.5t20.5 73.5q0 39 -20 70.5t-56 31.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="444" 
d="M149 252l-126 188h89l124 -188l-124 -188h-89zM328 252l-126 188h89l124 -188l-124 -188h-89z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="799" 
d="M226 266h-96v310h-1l-76 -35l-14 69l104 46h83v-390zM228 -11h-70l373 672h71zM707 0h-89v96h-198v52l181 246h106v-235h56v-63h-56v-96zM618 159v93q0 4 3 70h-2l-35 -61l-74 -101l2 -1h106z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="799" 
d="M213 266h-96v310h-1l-77 -35l-13 69l103 46h84v-390zM207 -11h-71l373 672h71zM465 0v48l62 56q68 62 94 94t26 64q0 26 -17 42t-49 16q-45 0 -85 -30l-26 63q55 42 135 42q69 0 106 -34t37 -83q0 -34 -19 -68t-40 -55t-63 -57l-28 -23v-3h157v-72h-290z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="799" 
d="M65 568l-20 61q51 32 129 32q66 0 100.5 -27.5t34.5 -69.5q0 -30 -22 -53.5t-58 -35.5v-2q42 -5 68.5 -30.5t26.5 -63.5q0 -51 -46 -84.5t-124 -33.5q-80 0 -126 28l21 65q43 -26 96 -26q36 0 57.5 16.5t20.5 39.5q0 29 -28 44t-67 15h-29v58h28q32 0 57.5 12.5
t25.5 38.5q0 18 -14 30t-43 12q-45 0 -88 -26zM263 -11h-71l373 672h71zM717 0h-89v96h-198v52l181 246h106v-235h57v-63h-57v-96zM628 159v93q0 26 4 70h-3q-22 -44 -34 -61l-75 -101l2 -1h106z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="428" 
d="M243 491h1q33 0 54 -22t21 -56t-21 -56t-55 -22t-55 22t-21 56q0 35 21 56.5t55 21.5zM187 274h108l2 -26q6 -82 -61 -164q-63 -75 -63 -119q0 -33 21 -52t62 -20q58 0 100 31l29 -89q-64 -40 -156 -40q-88 0 -134.5 43.5t-46.5 108.5q0 34 14 67t29 52.5t43 51.5
q56 64 53 135v21z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM157 830h135l88 -119h-99z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM343 830h135l-124 -120h-99z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM273 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM248 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18
q-22 0 -25 -43z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM223 715h-1q-25 0 -42 18t-17 43q0 26 17.5 43t42.5 17t41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM420 715h-1q-25 0 -41.5 18t-16.5 43
q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM317 889h1q50 0 78.5 -27t28.5 -68q0 -40 -29.5 -66.5t-78.5 -26.5t-78.5 26.5t-29.5 65.5q0 41 29 68.5t79 27.5zM316 845h-1
q-21 0 -33 -15.5t-12 -36.5q0 -19 13 -33.5t33 -14.5q21 0 33.5 14t12.5 36q0 21 -12.5 35.5t-33.5 14.5z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="832" 
d="M128 0h-127l296 674h473l-1 -101h-280l21 -176h253v-100h-238l25 -196h252v-101h-357l-27 208h-201zM255 307h149l-21 177q-9 69 -12 103h-3l-39 -102z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="592" 
d="M534 117l20 -97q-57 -29 -166 -30l-21 -37q31 -6 51 -26.5t20 -51.5q0 -45 -32.5 -66.5t-78.5 -21.5t-80 18l18 55q26 -14 56 -14q38 0 38 28q0 31 -78 40l40 80q-134 16 -209.5 105t-75.5 230q0 163 101 259.5t259 96.5q106 0 164 -30l-27 -98q-59 26 -132 26
q-106 0 -171 -65t-65 -183q0 -112 62.5 -177.5t172.5 -65.5q81 0 134 25z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM115 830h135l88 -119h-99z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM295 830h135l-124 -120h-99z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM222 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM166 716h-1q-25 0 -42 18t-17 43t17 42.5t43 17.5q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM363 716h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43
q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM-26 830h135l88 -119h-99z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM156 830h135l-124 -120h-99z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM85 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM34 716h-1q-25 0 -42 18t-17 43t17 42.5t43 17.5q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM231 716h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="689" 
d="M1 294v97h76v276q92 15 196 15q183 0 275 -77q106 -85 106 -252q0 -172 -105 -269q-102 -93 -306 -93q-94 0 -166 9v294h-76zM355 391v-97h-155v-202q17 -3 69 -3q123 0 189 66.5t66 195.5q0 114 -62 174.5t-178 60.5q-42 0 -84 -7v-188h155z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-674h-127l-176 297q-77 131 -121 232l-3 -1q6 -104 6 -287v-241zM268 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19
q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM194 832h135
l88 -119h-99z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM378 832h135
l-124 -120h-99z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM305 831h93
l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM279 716h-60
q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM254 716h-1
q-25 0 -42 18t-17 43t17 42.5t43 17.5q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM451 716h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="596" 
d="M40 470l55 56l203 -207l202 207l56 -56l-202 -207l202 -207l-56 -56l-202 206l-202 -206l-56 56l202 207z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="704" 
d="M135 -56l-67 54l59 86q-91 94 -91 251q0 153 89 251.5t229 98.5q90 0 158 -43l57 81l71 -48l-59 -85q88 -94 88 -248q0 -165 -90 -259t-226 -94q-87 0 -156 43zM198 189l256 364q-45 35 -101 35q-90 0 -141.5 -72t-51.5 -179q0 -79 36 -149zM509 481l-254 -361
q40 -34 97 -34q89 0 141 71.5t52 181.5q0 78 -34 142h-2z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM178 830h135l88 -119h-99z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM357 830h135l-124 -120h-99z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM295 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM239 716h-1q-25 0 -42 18t-17 43t17.5 42.5t42.5 17.5t41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM436 716h-1
q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM320 827h135l-124 -120h-99z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="558" 
d="M71 0v674h121v-126q38 5 89 5q118 0 181 -54q61 -53 61 -139q0 -111 -71.5 -166.5t-187.5 -55.5q-30 0 -72 5v-143h-121zM192 452v-212q27 -6 68 -6q69 0 105 31.5t36 84.5q0 54 -35 81.5t-98 27.5q-52 0 -76 -7z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="576" 
d="M190 0h-124v440q0 154 75 223q61 58 165 58q84 0 141 -48.5t57 -129.5q0 -50 -23 -82q-64 -29 -64 -74q0 -20 9.5 -36.5t32.5 -39.5q27 -28 39 -42t26.5 -46.5t14.5 -67.5q0 -75 -51.5 -120.5t-140.5 -45.5q-64 0 -106 18l17 92q35 -16 76 -16q38 0 59.5 18.5t21.5 47.5
q0 40 -44 84q-24 23 -37.5 39t-26.5 43t-13 55q0 81 84 124q8 18 8 39q0 42 -24.5 67.5t-65.5 25.5q-106 0 -106 -177v-449z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="508" 
d="M98 698h121l87 -148h-86zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91
q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="508" 
d="M279 698h121l-123 -148h-85zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91
q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="508" 
d="M208 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67
q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="508" 
d="M175 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8
q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="508" 
d="M155 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM354 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65
q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="508" 
d="M254 538q-49 0 -79 27t-30 67q0 43 29.5 70.5t80.5 27.5q49 0 77.5 -27.5t28.5 -69.5q0 -41 -30 -68t-77 -27zM253 582q22 0 35.5 15t13.5 37q0 21 -13.5 36t-36.5 15q-21 0 -33.5 -15.5t-12.5 -37.5q0 -19 13 -34.5t34 -15.5zM444 293v-176q0 -75 7 -117h-111l-8 54h-3
q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="790" 
d="M754 216h-317q0 -63 39.5 -98.5t103.5 -35.5q73 0 134 26l19 -84q-69 -35 -170 -35q-63 0 -113 25t-73 71h-3q-55 -96 -181 -96q-75 0 -118 42.5t-43 103.5q0 86 77 132.5t213 45.5v17q0 9 -4 20.5t-14 26t-32 24.5t-52 10q-71 0 -127 -37l-26 79q76 45 163 45
q119 0 159 -90h2q58 90 168 90q52 0 92 -22t62 -57.5t33 -75t11 -79.5q0 -15 -3 -48zM325 175v60q-74 2 -123 -19t-49 -71q0 -32 20.5 -50t51.5 -18q35 0 60 19t36 47q4 12 4 32zM437 301h205q1 39 -22 76.5t-75 37.5q-50 0 -78.5 -37t-29.5 -77z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="449" 
d="M407 106l17 -91q-47 -24 -119 -25l-21 -37q30 -7 50.5 -26.5t20.5 -50.5q0 -46 -32.5 -67.5t-79.5 -21.5q-49 0 -78 19l17 54q23 -14 55 -14q40 0 40 28q-1 32 -79 39l41 81q-94 13 -149 78.5t-55 165.5q0 113 73 186.5t198 73.5q69 0 119 -23l-22 -92q-42 19 -93 19
q-69 0 -109.5 -45t-39.5 -113q0 -73 42 -115t107 -42q51 0 97 19z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="516" 
d="M126 698h121l87 -148h-86zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5
q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="516" 
d="M302 698h121l-123 -148h-85zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5
q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="516" 
d="M227 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214
q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="516" 
d="M176 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM375 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85
q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="256" 
d="M-29 698h121l87 -148h-86zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="256" 
d="M157 698h121l-123 -148h-85zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="256" 
d="M86 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="256" 
d="M29 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM228 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="560" 
d="M278 78h1q55 0 86 45.5t31 118.5q0 83 -28 119q-34 39 -87 39q-60 0 -89.5 -50t-29.5 -112q0 -68 32 -114t84 -46zM111 491l-23 53l110 50q-44 30 -99 53l54 72q73 -29 150 -80l133 60l26 -52l-105 -48q80 -65 124 -154q42 -82 42 -190q0 -130 -72 -198t-173 -68
q-105 0 -174 68t-69 180q0 108 66.5 173.5t161.5 67.5q52 2 90 -30l2 3q-41 61 -101 104z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="572" 
d="M214 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45zM66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-289h-123v275
q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="564" 
d="M138 698h121l87 -148h-86zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="564" 
d="M308 698h121l-123 -148h-85zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="564" 
d="M240 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5
q0 65 -30 114t-88 49z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="564" 
d="M210 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70z
M284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="564" 
d="M183 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM382 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5
t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="596" 
d="M298 377h-1q-28 0 -45.5 18t-17.5 47q0 30 17.5 48t46.5 18t46.5 -18t17.5 -48q0 -29 -17.5 -47t-46.5 -18zM556 228h-516v78h516v-78zM298 26h-1q-28 0 -45.5 18t-17.5 47q0 30 17.5 48t46.5 18t46.5 -18t17.5 -48q0 -29 -17.5 -47t-46.5 -18z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="564" 
d="M283 498h1q66 0 120 -30l44 65l47 -33l-44 -65q78 -70 78 -189q0 -124 -73 -190.5t-173 -66.5q-69 0 -120 29l-46 -66l-45 36l44 63q-81 68 -81 191q0 116 69.5 186t178.5 70zM182 145l167 240q-26 23 -68 23q-63 0 -95 -49.5t-32 -115.5q0 -58 26 -98h2zM383 338
l-166 -237q26 -23 64 -23q59 0 94 46t35 119q0 54 -24 95h-3z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="569" 
d="M135 698h121l87 -148h-86zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="569" 
d="M318 698h121l-123 -148h-85zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="569" 
d="M242 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="569" 
d="M188 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM387 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85
q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="500" 
d="M292 698h121l-123 -148h-85zM8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="585" 
d="M66 673h123v-254h2q51 79 154 79q85 2 145 -67t60 -182q0 -126 -66.5 -193t-154.5 -67q-92 0 -138 66h-2v-253h-123v871zM189 282v-86q0 -24 12 -46q13 -30 40.5 -48t60.5 -18q56 0 89.5 44t33.5 119q0 66 -32 110t-87 44q-36 0 -65 -20t-42 -54q-10 -24 -10 -45z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="500" 
d="M162 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM361 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132
l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM190 803h251v-69h-251v69z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="508" 
d="M135 655h239v-71h-239v71zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91
q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM184 827h68q8 -48 65 -48t67 48h66q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="508" 
d="M122 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26
q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="636" 
d="M394 674l219 -674l-61 -1q-38 -51 -38 -79q0 -24 15 -39.5t38 -15.5q25 0 45 9l18 -59q-37 -28 -93 -28q-48 0 -76 25t-28 73q0 56 47 117l-60 189h-214l-58 -191h-127l216 674h157zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="508" 
d="M444 293v-176q0 -78 8 -117h-41q-30 -48 -30 -82q0 -25 13 -38t35 -13q23 0 45 10l18 -60q-40 -30 -95 -30q-43 0 -70 24.5t-27 69.5q0 50 40 121l-8 52h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80
q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="588" 
d="M534 117l20 -97q-60 -31 -174 -31q-159 0 -251.5 92.5t-92.5 247.5q0 163 101 259.5t259 96.5q106 0 164 -30l-27 -98q-59 26 -132 26q-106 0 -171 -65t-65 -183q0 -112 62.5 -177.5t172.5 -65.5q81 0 134 25zM388 831h135l-124 -120h-99z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="449" 
d="M296 698h121l-123 -148h-85zM407 106l17 -91q-56 -26 -138 -26q-114 0 -182.5 68t-68.5 181t73 186.5t198 73.5q69 0 119 -23l-22 -92q-42 19 -93 19q-69 0 -109.5 -45t-39.5 -113q0 -73 42 -115t107 -42q51 0 97 19z" />
    <glyph glyph-name="Ccircumflex" unicode="&#x108;" horiz-adv-x="588" 
d="M534 117l20 -97q-60 -31 -174 -31q-159 0 -251.5 92.5t-92.5 247.5q0 163 101 259.5t259 96.5q106 0 164 -30l-27 -98q-59 26 -132 26q-106 0 -171 -65t-65 -183q0 -112 62.5 -177.5t172.5 -65.5q81 0 134 25zM307 833h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="ccircumflex" unicode="&#x109;" horiz-adv-x="449" 
d="M221 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM407 106l17 -91q-56 -26 -138 -26q-114 0 -182.5 68t-68.5 181t73 186.5t198 73.5q69 0 119 -23l-22 -92q-42 19 -93 19q-69 0 -109.5 -45t-39.5 -113q0 -73 42 -115t107 -42q51 0 97 19z" />
    <glyph glyph-name="Cdotaccent" unicode="&#x10a;" horiz-adv-x="588" 
d="M534 117l20 -97q-60 -31 -174 -31q-159 0 -251.5 92.5t-92.5 247.5q0 163 101 259.5t259 96.5q106 0 164 -30l-27 -98q-59 26 -132 26q-106 0 -171 -65t-65 -183q0 -112 62.5 -177.5t172.5 -65.5q81 0 134 25zM354 716h-1q-25 0 -42.5 18t-17.5 42q0 25 18 42.5t44 17.5
q25 0 41.5 -17.5t16.5 -42.5q0 -24 -17 -42t-42 -18z" />
    <glyph glyph-name="cdotaccent" unicode="&#x10b;" horiz-adv-x="449" 
d="M272 563h-1q-26 0 -43.5 18t-17.5 44t18 44t44 18q25 0 42.5 -18t17.5 -44t-17 -44t-43 -18zM407 106l17 -91q-56 -26 -138 -26q-114 0 -182.5 68t-68.5 181t73 186.5t198 73.5q69 0 119 -23l-22 -92q-42 19 -93 19q-69 0 -109.5 -45t-39.5 -113q0 -73 42 -115t107 -42
q51 0 97 19z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="588" 
d="M534 117l20 -97q-60 -31 -174 -31q-159 0 -251.5 92.5t-92.5 247.5q0 163 101 259.5t259 96.5q106 0 164 -30l-27 -98q-59 26 -132 26q-106 0 -171 -65t-65 -183q0 -112 62.5 -177.5t172.5 -65.5q81 0 134 25zM404 712h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="449" 
d="M308 550h-85l-98 148h85l54 -91h2l55 91h84zM407 106l17 -91q-56 -26 -138 -26q-114 0 -182.5 68t-68.5 181t73 186.5t198 73.5q69 0 119 -23l-22 -92q-42 19 -93 19q-69 0 -109.5 -45t-39.5 -113q0 -73 42 -115t107 -42q51 0 97 19z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="683" 
d="M71 2v663q84 14 196 14q182 0 275 -78q105 -86 105 -248q0 -173 -105 -268q-101 -92 -305 -92q-94 0 -166 9zM194 576v-482q17 -3 68 -3q121 -1 188 65t67 193q1 112 -61.5 173t-177.5 61q-54 0 -84 -7zM363 711h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="591" 
d="M392 710h123v-573q0 -71 4 -137h-110l-5 77h-2q-22 -41 -64 -64.5t-95 -23.5q-89 0 -148.5 69t-59.5 180q-1 118 62.5 189t155.5 71q48 0 84 -18t53 -46h2v276zM392 211v73q0 17 -3 32q-9 38 -37 62.5t-69 24.5q-58 0 -90.5 -45t-32.5 -116q0 -68 32.5 -111t89.5 -43
q38 0 67.5 24t38.5 63q4 15 4 36zM609 507l-52 36q31 45 31 96q0 36 -24 66l81 15q13 -9 23 -29.5t10 -44.5q0 -83 -69 -139z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="689" 
d="M1 294v97h76v276q92 15 196 15q183 0 275 -77q106 -85 106 -252q0 -172 -105 -269q-102 -93 -306 -93q-94 0 -166 9v294h-76zM355 391v-97h-155v-202q17 -3 69 -3q123 0 189 66.5t66 195.5q0 114 -62 174.5t-178 60.5q-42 0 -84 -7v-188h155z" />
    <glyph glyph-name="dcroat" unicode="&#x111;" horiz-adv-x="581" 
d="M594 545h-79v-408q0 -71 4 -137h-110l-5 77h-2q-22 -41 -64 -64.5t-95 -23.5q-89 0 -148.5 69t-59.5 180q-1 118 62.5 189t155.5 71q48 0 84 -18t53 -46h2v111h-189v71h189v94h123v-94h79v-71zM392 211v73q0 17 -3 32q-9 38 -37 62.5t-69 24.5q-58 0 -90.5 -45
t-32.5 -116q0 -68 32.5 -111t89.5 -43q38 0 67.5 24t38.5 63q4 15 4 36z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM140 803h251v-69h-251v69z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="516" 
d="M142 655h239v-71h-239v71zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5
q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="Ebreve" unicode="&#x114;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM133 832h68q8 -48 65 -48t67 48h66q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86z" />
    <glyph glyph-name="ebreve" unicode="&#x115;" horiz-adv-x="516" 
d="M129 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5
t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM261 717h-1q-25 0 -42.5 18t-17.5 42q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5q0 -24 -17 -42t-42 -18z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="516" 
d="M263 563h-1q-26 0 -43.5 18t-17.5 44t18 44t44 18q25 0 42.5 -18t17.5 -44t-17 -44t-43 -18zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5
t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="515" 
d="M466 -126l18 -60q-39 -27 -96 -27q-46 0 -72 23t-26 64q0 38 22.5 71.5t52.5 54.5h-294v674h392v-101h-269v-174h254v-100h-254v-198h284v-101h-21q-31 -20 -53 -47t-22 -51q0 -17 11.5 -26.5t29.5 -9.5q24 0 43 8z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="516" 
d="M405 -124l19 -59q-40 -29 -96 -29q-45 0 -70.5 21.5t-25.5 60.5q0 65 70 119v1q-4 0 -10 -0.5t-10 -0.5q-115 1 -181 68t-66 179q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85
q-8 -3 -24 -8.5t-20 -7.5q-32 -14 -61 -44.5t-29 -56.5q0 -18 10.5 -27.5t26.5 -9.5q23 0 46 10zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM312 710h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="516" 
d="M304 550h-85l-98 148h85l54 -91h2l55 91h84zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5
t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="Gcircumflex" unicode="&#x11c;" horiz-adv-x="666" 
d="M611 372v-341q-102 -38 -217 -38q-170 0 -264 90q-96 91 -94 248q0 159 103.5 254.5t271.5 95.5q113 0 179 -32l-27 -99q-67 29 -153 29q-111 0 -178 -63.5t-67 -178.5q0 -113 64 -178.5t170 -65.5q65 0 93 14v168h-119v97h238zM320 832h93l103 -119h-92l-57 65h-2
l-56 -65h-90z" />
    <glyph glyph-name="gcircumflex" unicode="&#x11d;" horiz-adv-x="573" 
d="M245 700h84l97 -148h-84l-54 91h-2l-54 -91h-84zM507 347v-276q0 -156 -72 -221q-67 -59 -188 -59q-107 0 -170 39l27 93q64 -37 142 -37q64 0 102 36t38 111v42h-2q-47 -72 -142 -72q-91 0 -149 67t-58 171q0 116 65 186.5t156 70.5q97 0 141 -76h2l5 65h107
q-4 -66 -4 -140zM384 213v79q0 20 -4 33q-10 35 -36 57t-64 22q-53 0 -86.5 -43t-33.5 -116q0 -65 31.5 -107.5t87.5 -42.5q34 0 61.5 20.5t38.5 53.5q5 22 5 44z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" horiz-adv-x="666" 
d="M611 372v-341q-102 -38 -217 -38q-170 0 -264 90q-96 91 -94 248q0 159 103.5 254.5t271.5 95.5q113 0 179 -32l-27 -99q-67 29 -153 29q-111 0 -178 -63.5t-67 -178.5q0 -113 64 -178.5t170 -65.5q65 0 93 14v168h-119v97h238zM234 835h68q8 -48 65 -48t67 48h66
q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" horiz-adv-x="573" 
d="M151 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5zM507 347v-276q0 -156 -72 -221q-67 -59 -188 -59q-107 0 -170 39l27 93q64 -37 142 -37q64 0 102 36t38 111v42h-2q-47 -72 -142 -72
q-91 0 -149 67t-58 171q0 116 65 186.5t156 70.5q97 0 141 -76h2l5 65h107q-4 -66 -4 -140zM384 213v79q0 20 -4 33q-10 35 -36 57t-64 22q-53 0 -86.5 -43t-33.5 -116q0 -65 31.5 -107.5t87.5 -42.5q34 0 61.5 20.5t38.5 53.5q5 22 5 44z" />
    <glyph glyph-name="Gdotaccent" unicode="&#x120;" horiz-adv-x="666" 
d="M611 372v-341q-102 -38 -217 -38q-170 0 -264 90q-96 91 -94 248q0 159 103.5 254.5t271.5 95.5q113 0 179 -32l-27 -99q-67 29 -153 29q-111 0 -178 -63.5t-67 -178.5q0 -113 64 -178.5t170 -65.5q65 0 93 14v168h-119v97h238zM369 716h-1q-25 0 -42.5 18t-17.5 42
q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5q0 -24 -17 -42t-42 -18z" />
    <glyph glyph-name="gdotaccent" unicode="&#x121;" horiz-adv-x="573" 
d="M285 565h-1q-26 0 -43.5 18t-17.5 44t18 44t44 18q25 0 42.5 -18t17.5 -44t-17 -44t-43 -18zM507 347v-276q0 -156 -72 -221q-67 -59 -188 -59q-107 0 -170 39l27 93q64 -37 142 -37q64 0 102 36t38 111v42h-2q-47 -72 -142 -72q-91 0 -149 67t-58 171q0 116 65 186.5
t156 70.5q97 0 141 -76h2l5 65h107q-4 -66 -4 -140zM384 213v79q0 20 -4 33q-10 35 -36 57t-64 22q-53 0 -86.5 -43t-33.5 -116q0 -65 31.5 -107.5t87.5 -42.5q34 0 61.5 20.5t38.5 53.5q5 22 5 44z" />
    <glyph glyph-name="Gcommaaccent" unicode="&#x122;" horiz-adv-x="666" 
d="M611 372v-341q-102 -38 -217 -38q-170 0 -264 90q-96 91 -94 248q0 159 103.5 254.5t271.5 95.5q113 0 179 -32l-27 -99q-67 29 -153 29q-111 0 -178 -63.5t-67 -178.5q0 -113 64 -178.5t170 -65.5q65 0 93 14v168h-119v97h238zM291 -232l-19 53q29 6 49 22t20 41
q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="gcommaaccent" unicode="&#x123;" horiz-adv-x="573" 
d="M507 347v-276q0 -156 -72 -221q-67 -59 -188 -59q-107 0 -170 39l27 93q64 -37 142 -37q64 0 102 36t38 111v42h-2q-47 -72 -142 -72q-91 0 -149 67t-58 171q0 116 65 186.5t156 70.5q97 0 141 -76h2l5 65h107q-4 -66 -4 -140zM384 213v79q0 20 -4 33q-10 35 -36 57
t-64 22q-53 0 -86.5 -43t-33.5 -116q0 -65 31.5 -107.5t87.5 -42.5q34 0 61.5 20.5t38.5 53.5q5 22 5 44zM353 717l20 -46q-74 -11 -74 -62q0 -29 32 -57l-83 -15q-40 28 -40 72q0 52 41.5 79t103.5 29z" />
    <glyph glyph-name="Hcircumflex" unicode="&#x124;" horiz-adv-x="672" 
d="M71 674h123v-270h285v270h123v-674h-123v297h-285v-297h-123v674zM290 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="hcircumflex" unicode="&#x125;" horiz-adv-x="572" 
d="M66 0v710h124v-290h2q24 37 61 56q38 22 84 22q71 0 120.5 -51.5t49.5 -159.5v-287h-123v273q0 124 -94 124q-34 0 -59.5 -19.5t-35.5 -49.5q-5 -12 -5 -37v-291h-124zM86 851h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="Hbar" unicode="&#x126;" horiz-adv-x="681" 
d="M667 475h-61v-475h-123v277h-285v-277h-123v475h-61v75h61v124h123v-124h285v124h123v-124h61v-75zM483 383v92h-285v-92h285z" />
    <glyph glyph-name="hbar" unicode="&#x127;" horiz-adv-x="572" 
d="M507 0h-123v273q0 124 -94 124q-34 0 -59.5 -19.5t-35.5 -49.5q-5 -12 -5 -37v-291h-124v545h-78v71h78v94h124v-94h187v-71h-187v-125h2q24 37 61 56q41 22 84 22q71 0 120.5 -51.5t49.5 -159.5v-287z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM59 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="256" 
d="M56 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM7 803h251v-69h-251v69z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="256" 
d="M9 655h239v-71h-239v71zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="Ibreve" unicode="&#x12c;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM-1 832h68q8 -48 65 -48t67 48h66q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86z" />
    <glyph glyph-name="ibreve" unicode="&#x12d;" horiz-adv-x="256" 
d="M-2 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="264" 
d="M194 674v-674h-51q-31 -49 -31 -82q0 -25 14.5 -39.5t38.5 -14.5q28 0 44 8l18 -59q-35 -28 -93 -28q-48 0 -75.5 25t-27.5 72t40 118v674h123z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="256" 
d="M128 560h-1q-31 0 -50.5 19.5t-19.5 48.5t19.5 48.5t51.5 19.5t51.5 -19t19.5 -49q0 -29 -19.5 -48.5t-51.5 -19.5zM190 487v-487h-52q-30 -48 -30 -82q0 -25 14.5 -39.5t38.5 -14.5q28 0 44 8l18 -59q-37 -28 -93 -28q-48 0 -75.5 25t-27.5 72q0 49 39 118v487h124z" />
    <glyph glyph-name="Idotaccent" unicode="&#x130;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM133 717h-1q-25 0 -42.5 18t-17.5 42q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5q0 -24 -17 -42t-42 -18z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="256" 
d="M190 0h-124v487h124v-487z" />
    <glyph glyph-name="IJ" unicode="&#x132;" horiz-adv-x="657" 
d="M71 674h123v-674h-123v674zM469 241v433h123v-437q0 -133 -58.5 -190.5t-165.5 -57.5q-61 0 -103 16l15 99q40 -12 76 -12q56 0 84.5 33t28.5 116z" />
    <glyph glyph-name="ij" unicode="&#x133;" horiz-adv-x="526" 
d="M190 0h-124v487h124v-487zM128 690q31 0 49.5 -19t19.5 -48q0 -28 -19 -47t-51 -19q-30 0 -49 19t-19 47q0 29 19.5 48t49.5 19zM226 -213l-12 96q65 5 92 35q18 18 25 54.5t7 114.5v400h123v-435q0 -145 -63 -208q-61 -57 -172 -57zM400 690q31 0 49.5 -19t18.5 -48
q0 -28 -19 -47t-51 -19q-30 0 -48.5 19t-18.5 47q0 29 19 48t50 19z" />
    <glyph glyph-name="Jcircumflex" unicode="&#x134;" horiz-adv-x="392" 
d="M205 241v433h123v-437q0 -133 -58.5 -190.5t-165.5 -57.5q-61 0 -103 16l15 99q40 -12 76 -12q56 0 84.5 33t28.5 116zM217 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="jcircumflex" unicode="&#x135;" horiz-adv-x="270" 
d="M-30 -213l-12 96q65 5 92 35q18 18 25 54.5t7 114.5v400h123v-435q0 -145 -63 -208q-61 -57 -172 -57zM94 694h84l97 -148h-84l-54 91h-2l-54 -91h-84z" />
    <glyph glyph-name="Kcommaaccent" unicode="&#x136;" horiz-adv-x="582" 
d="M71 0v674h122v-310h3q14 22 49 73l176 237h152l-232 -287l247 -387h-144l-191 309l-60 -72v-237h-122zM229 -231l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="kcommaaccent" unicode="&#x137;" horiz-adv-x="509" 
d="M189 710v-437h2q22 34 39 57l118 157h148l-180 -199l206 -288h-151l-140 215l-42 -49v-166h-123v710h123zM191 -233l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="kgreenlandic" unicode="&#x138;" horiz-adv-x="509" 
d="M189 487v-214h2q22 34 39 57l118 157h148l-180 -199l206 -288h-151l-140 215l-42 -49v-166h-123v487h123z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="493" 
d="M71 0v674h123v-571h277v-103h-400zM172 830h135l-124 -120h-99z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="257" 
d="M66 0v710h124v-710h-124zM149 860h135l-124 -120h-99z" />
    <glyph glyph-name="Lcommaaccent" unicode="&#x13b;" horiz-adv-x="493" 
d="M71 0v674h123v-571h277v-103h-400zM188 -232l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="lcommaaccent" unicode="&#x13c;" horiz-adv-x="257" 
d="M66 0v710h124v-710h-124zM55 -233l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="493" 
d="M71 0v674h123v-571h277v-103h-400zM278 494l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="266" 
d="M66 0v710h124v-710h-124zM284 507l-56 36q31 50 31 96q0 37 -25 66l81 15q14 -9 24 -30t10 -47q0 -13 -1.5 -25t-5 -22.5l-6.5 -19t-9 -17.5l-9.5 -14.5t-10 -13t-9 -10l-8.5 -8.5z" />
    <glyph glyph-name="Ldot" unicode="&#x13f;" horiz-adv-x="493" 
d="M71 0v674h123v-571h277v-103h-400zM364 315h-1q-25 0 -42.5 18t-17.5 42q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5q0 -24 -17 -42t-42 -18z" />
    <glyph glyph-name="ldot" unicode="&#x140;" horiz-adv-x="317" 
d="M66 0v710h124v-710h-124zM288 335h-1q-24 0 -40.5 17.5t-16.5 42.5q0 26 16.5 43t41.5 17q23 0 39 -17.5t16 -42.5t-16 -42.5t-39 -17.5z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="499" 
d="M476 0h-400v251l-79 -56v88l79 56v335h123v-252l130 90v-89l-130 -90v-230h277v-103z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="267" 
d="M195 0h-123v256l-67 -55v91l67 55v363h123v-270l71 58v-91l-71 -59v-348z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-674h-127l-176 297q-77 131 -121 232l-3 -1q6 -104 6 -287v-241zM361 830h135l-124 -120h-99z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="572" 
d="M311 698h121l-123 -148h-85zM66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-289h-123v275q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124z" />
    <glyph glyph-name="Ncommaaccent" unicode="&#x145;" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-674h-127l-176 297q-77 131 -121 232l-3 -1q6 -104 6 -287v-241zM273 -231l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="ncommaaccent" unicode="&#x146;" horiz-adv-x="572" 
d="M66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-289h-123v275q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124zM222 -232l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76
q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-674h-127l-176 297q-77 131 -121 232l-3 -1q6 -104 6 -287v-241zM387 707h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="572" 
d="M329 550h-85l-98 148h85l54 -91h2l55 91h84zM66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-289h-123v275q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124z" />
    <glyph glyph-name="napostrophe" unicode="&#x149;" horiz-adv-x="572" 
d="M66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-289h-123v275q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124zM67 530l-54 35q33 47 33 85q0 32 -25 57l83 21q33 -22 33 -74q0 -70 -70 -124z" />
    <glyph glyph-name="Eng" unicode="&#x14a;" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-630q0 -225 -212 -252l-27 94q108 24 119 103l-183 308q-77 131 -121 232l-3 -1q6 -104 6 -287v-241z" />
    <glyph glyph-name="eng" unicode="&#x14b;" horiz-adv-x="572" 
d="M66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-230q0 -222 -199 -247l-24 97q52 10 76 41.5t24 91.5v233q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM227 806h251
v-69h-251v69z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" horiz-adv-x="564" 
d="M163 655h239v-71h-239v71zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="Obreve" unicode="&#x14e;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM219 838h68
q8 -48 65 -48t67 48h66q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86z" />
    <glyph glyph-name="obreve" unicode="&#x14f;" horiz-adv-x="564" 
d="M150 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5
t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM298 833h121
l-106 -114h-87zM464 833h121l-106 -114h-87z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" horiz-adv-x="564" 
d="M217 697h105l-104 -143h-72zM377 697h105l-104 -143h-71zM287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5
q0 65 -30 114t-88 49z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="917" 
d="M879 101v-101h-364q-16 0 -64 -6q-60 -5 -83 -5q-158 0 -245 97.5t-87 245.5q0 162 92.5 257.5t243.5 95.5q29 0 89 -5q50 -6 64 -6h339v-101h-269v-174h254v-100h-254v-198h284zM472 100v472q-37 15 -91 15q-96 0 -156 -68t-59 -183q-1 -112 58.5 -180.5t165.5 -68.5
q50 0 82 13z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="866" 
d="M830 211h-318q1 -60 42 -94.5t104 -34.5q66 0 133 27l19 -85q-65 -35 -169 -35q-128 0 -185 99h-3q-28 -49 -77.5 -74t-107.5 -25q-100 0 -166.5 69t-66.5 184t68 185.5t173 70.5q58 0 105.5 -26t71.5 -74h2q58 100 174 100q55 0 97 -22t64.5 -57.5t33 -75t10.5 -81.5
q0 -31 -4 -51zM281 408h-1q-61 0 -89.5 -49.5t-28.5 -115.5q0 -71 32.5 -118t85.5 -47q54 0 85.5 45.5t31.5 120.5q0 68 -29.5 116t-86.5 48zM511 296h207q1 44 -21.5 81t-78.5 37q-50 0 -77 -37t-30 -81z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="569" 
d="M71 0v665q81 14 186 14q144 0 208 -54q58 -49 58 -135q0 -61 -35 -105.5t-87 -63.5v-3q67 -25 94 -130q42 -167 53 -188h-127q-17 33 -45 159q-14 65 -41 90.5t-80 27.5h-62v-277h-122zM193 580v-213h73q62 0 98.5 30.5t36.5 80.5q0 54 -35 81t-97 27q-52 0 -76 -6z
M299 832h135l-124 -120h-99z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="356" 
d="M214 698h121l-123 -148h-85zM66 0v330q0 98 -4 157h107l4 -93h4q17 50 54.5 77t78.5 27q14 0 29 -3v-116q-20 4 -36 4q-43 0 -72.5 -25.5t-37.5 -67.5q-4 -22 -4 -38v-252h-123z" />
    <glyph glyph-name="Rcommaaccent" unicode="&#x156;" horiz-adv-x="569" 
d="M71 0v665q81 14 186 14q144 0 208 -54q58 -49 58 -135q0 -61 -35 -105.5t-87 -63.5v-3q67 -25 94 -130q42 -167 53 -188h-127q-17 33 -45 159q-14 65 -41 90.5t-80 27.5h-62v-277h-122zM193 580v-213h73q62 0 98.5 30.5t36.5 80.5q0 54 -35 81t-97 27q-52 0 -76 -6z
M220 -228l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="rcommaaccent" unicode="&#x157;" horiz-adv-x="356" 
d="M66 0v330q0 98 -4 157h107l4 -93h4q17 50 54.5 77t78.5 27q14 0 29 -3v-116q-20 4 -36 4q-43 0 -72.5 -25.5t-37.5 -67.5q-4 -22 -4 -38v-252h-123zM63 -233l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="569" 
d="M71 0v665q81 14 186 14q144 0 208 -54q58 -49 58 -135q0 -61 -35 -105.5t-87 -63.5v-3q67 -25 94 -130q42 -167 53 -188h-127q-17 33 -45 159q-14 65 -41 90.5t-80 27.5h-62v-277h-122zM193 580v-213h73q62 0 98.5 30.5t36.5 80.5q0 54 -35 81t-97 27q-52 0 -76 -6z
M325 712h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="356" 
d="M235 550h-85l-98 148h85l54 -91h2l55 91h84zM66 0v330q0 98 -4 157h107l4 -93h4q17 50 54.5 77t78.5 27q14 0 29 -3v-116q-20 4 -36 4q-43 0 -72.5 -25.5t-37.5 -67.5q-4 -22 -4 -38v-252h-123z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="519" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -87 -66 -145t-190 -58
q-51 0 -101.5 12.5t-80.5 30.5zM296 832h135l-124 -120h-99z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="417" 
d="M244 698h121l-123 -148h-85zM36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-136 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q66 -24 97.5 -59.5t32.5 -89.5
q0 -68 -52 -110.5t-146 -42.5q-85 0 -148 35z" />
    <glyph glyph-name="Scircumflex" unicode="&#x15c;" horiz-adv-x="519" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -87 -66 -145t-190 -58
q-51 0 -101.5 12.5t-80.5 30.5zM214 831h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="scircumflex" unicode="&#x15d;" horiz-adv-x="417" 
d="M169 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-136 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q66 -24 97.5 -59.5
t32.5 -89.5q0 -68 -52 -110.5t-146 -42.5q-85 0 -148 35z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="519" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -73 -47 -126.5
t-138 -69.5l-24 -43q30 -4 50.5 -23t20.5 -47q0 -43 -32.5 -64t-80.5 -21q-45 0 -76 19l18 50q28 -16 54 -16q16 0 27.5 7.5t11.5 20.5q0 32 -78 40l38 70h-5q-43 0 -94 12.5t-83 30.5z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="417" 
d="M36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-134 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q67 -24 98.5 -60t31.5 -89q0 -56 -36 -95.5t-104 -51.5l-24 -42
q29 -3 50 -22.5t21 -48.5q0 -44 -32 -64t-82 -20q-45 0 -74 19l17 50q27 -16 55 -16q16 0 27.5 7t10.5 22q0 33 -77 39l37 71h-2q-72 0 -133 34z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="519" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -87 -66 -145t-190 -58
q-51 0 -101.5 12.5t-80.5 30.5zM317 712h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="417" 
d="M255 550h-85l-98 148h85l54 -91h2l55 91h84zM36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-136 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q66 -24 97.5 -59.5
t32.5 -89.5q0 -68 -52 -110.5t-146 -42.5q-85 0 -148 35z" />
    <glyph glyph-name="Tcommaaccent" unicode="&#x162;" horiz-adv-x="525" 
d="M200 0v571h-192v103h509v-103h-194v-571h-123zM186 -232l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="tcommaaccent" unicode="&#x163;" horiz-adv-x="351" 
d="M87 577l120 35v-125h117v-92h-117v-215q0 -46 14.5 -68t48.5 -22q27 0 48 6l2 -94q-33 -13 -89 -13q-68 0 -106 41t-38 136v229h-70v92h70v90zM123 -236l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="525" 
d="M200 0v571h-192v103h509v-103h-194v-571h-123zM309 710h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="357" 
d="M87 577l120 35v-125h117v-92h-117v-215q0 -46 14.5 -68t48.5 -22q27 0 48 6l2 -94q-33 -13 -89 -13q-68 0 -106 41t-38 136v229h-70v92h70v90zM305 539l-32 46q35 25 35 71q0 31 -23 56l82 15q13 -9 23 -28.5t10 -40.5q0 -48 -30.5 -78.5t-64.5 -40.5z" />
    <glyph glyph-name="Tbar" unicode="&#x166;" horiz-adv-x="525" 
d="M517 571h-194v-192h119v-76h-119v-303h-123v303h-120v76h120v192h-192v103h509v-103z" />
    <glyph glyph-name="tbar" unicode="&#x167;" horiz-adv-x="351" 
d="M324 395h-117v-90h90v-69h-90v-64q0 -82 63 -82q27 0 48 6l2 -94q-33 -13 -89 -13q-68 0 -106 41t-38 136v70h-65v69h65v90h-70v92h70v90l120 35v-125h117v-92z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM260 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112
q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="569" 
d="M212 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265
q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM208 803h251v-69h-251v69z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="569" 
d="M165 655h239v-71h-239v71zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="Ubreve" unicode="&#x16c;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM200 832h68q8 -48 65 -48t67 48h66q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86z" />
    <glyph glyph-name="ubreve" unicode="&#x16d;" horiz-adv-x="569" 
d="M152 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5
q7 19 7 38v294h123z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM333 880h1q50 0 78.5 -27t28.5 -68q0 -40 -29.5 -66.5t-78.5 -26.5t-78.5 26.5t-29.5 65.5q0 41 29 68.5t79 27.5z
M332 836h-1q-21 0 -33 -15.5t-12 -36.5q0 -19 13 -33.5t33 -14.5q21 0 33.5 14t12.5 36q0 21 -12.5 35.5t-33.5 14.5z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="569" 
d="M285 538q-49 0 -79 27t-30 67q0 43 29.5 70.5t80.5 27.5q49 0 77.5 -27.5t28.5 -69.5q0 -41 -30 -68t-77 -27zM284 582q22 0 35.5 15t13.5 37q0 21 -13.5 36t-36.5 15q-21 0 -33.5 -15.5t-12.5 -37.5q0 -19 13 -34.5t34 -15.5zM502 487v-341q0 -66 4 -146h-108l-6 74h-2
q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM269 831h121l-106 -114h-87zM435 831h121l-106 -114h-87z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="569" 
d="M232 697h105l-104 -143h-72zM392 697h105l-104 -143h-71zM502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="666" 
d="M436 -124l18 -59q-42 -29 -97 -29q-46 0 -71.5 23t-25.5 62q0 57 58 117h-7q-114 4 -177.5 76.5t-63.5 221.5v386h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -125 -49.5 -197.5t-139.5 -91.5q-58 -54 -58 -94q0 -19 12 -29t31 -10q23 0 44 9z
" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="569" 
d="M502 487v-341q0 -68 4 -146l-36 2q-30 -48 -30 -82q0 -25 14.5 -39.5t38.5 -14.5q28 0 44 8l18 -60q-38 -27 -93 -27q-48 0 -75.5 25t-27.5 72q0 49 39 118l-6 72h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 59 20t35 46
q7 15 7 38v294h123z" />
    <glyph glyph-name="Wcircumflex" unicode="&#x174;" horiz-adv-x="869" 
d="M313 0h-133l-165 674h131l64 -306q26 -126 44 -235h2q8 55 49 236l73 305h129l67 -310q27 -129 41 -228h2q15 90 46 233l70 305h125l-181 -674h-132l-70 317q-27 122 -37 214h-2q-19 -114 -45 -214zM391 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="wcircumflex" unicode="&#x175;" horiz-adv-x="749" 
d="M332 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM14 487h126l51 -221q21 -100 31 -158h2q8 43 40 157l63 222h100l61 -216q24 -93 39 -163h2q10 67 32 163l54 216h121l-153 -487h-114l-58 198q-22 74 -37 156h-2q-11 -68 -37 -156l-62 -198h-115z" />
    <glyph glyph-name="Ycircumflex" unicode="&#x176;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM241 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="ycircumflex" unicode="&#x177;" horiz-adv-x="500" 
d="M212 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM198 712h-1q-25 0 -42 18t-17 43t17 42.5t43 17.5q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM395 712h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17
q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="566" 
d="M26 0v68l345 501v3h-314v102h474v-72l-340 -496v-4h345v-102h-510zM311 830h135l-124 -120h-99z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="450" 
d="M259 698h121l-123 -148h-85zM20 0v71l183 234q36 44 72 82v2h-237v98h387v-75l-179 -229q-48 -58 -72 -83v-2h257v-98h-411z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="566" 
d="M26 0v68l345 501v3h-314v102h474v-72l-340 -496v-4h345v-102h-510zM287 716h-1q-25 0 -42.5 18t-17.5 42q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5q0 -24 -17 -42t-42 -18z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="450" 
d="M229 563h-1q-26 0 -43.5 18t-17.5 44t18 44t44 18q25 0 42.5 -18t17.5 -44t-17 -44t-43 -18zM20 0v71l183 234q36 44 72 82v2h-237v98h387v-75l-179 -229q-48 -58 -72 -83v-2h257v-98h-411z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="566" 
d="M26 0v68l345 501v3h-314v102h474v-72l-340 -496v-4h345v-102h-510zM338 710h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="450" 
d="M277 550h-85l-98 148h85l54 -91h2l55 91h84zM20 0v71l183 234q36 44 72 82v2h-237v98h387v-75l-179 -229q-48 -58 -72 -83v-2h257v-98h-411z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="536" 
d="M113 320v87h94l5 39q4 36 14.5 68.5t32 68t62.5 57t96 21.5q50 -2 76 -16l-20 -87q-25 9 -50 9q-79 0 -91 -112l-6 -48h110v-87h-121l-23 -190q-13 -105 -54.5 -161.5t-132.5 -56.5q-57 2 -85 17l19 86q24 -9 51 -9q41 0 59.5 33t28.5 116l19 165h-84z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" horiz-adv-x="704" 
d="M353 87q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73t-138 -73t-49 -179q-1 -105 50 -176.5t137 -71.5zM597 742l90 16q21 -46 21 -93q0 -46 -26.5 -74t-78.5 -27q66 -89 66 -220q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5
t231 98.5q62 0 115 -19q10 -3 32.5 -13t40.5 -16t31 -6q41 0 41 42q0 31 -19 69z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" horiz-adv-x="573" 
d="M284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM474 561l89 15q20 -38 20 -85q0 -44 -25 -70t-70 -27q41 -60 41 -146q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70
q44 0 86 -13q8 -3 25 -10.5t31.5 -12t26.5 -4.5q37 0 37 38q0 30 -19 65z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="705" 
d="M603 775l89 15q20 -40 20 -85q0 -43 -25 -68.5t-75 -29.5l-16 -1v-317q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h99q28 0 39 9.5t11 29.5q0 30 -19 62z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="569" 
d="M491 589l89 15q20 -38 20 -82q0 -41 -25 -66t-64 -28l-9 -1v-281q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h82q50 0 50 40q0 29 -20 62z" />
    <glyph glyph-name="uni01FA" unicode="&#x1fa;" horiz-adv-x="636" 
d="M321 692q-45 0 -72.5 24t-27.5 60t27.5 60.5t73.5 24.5t72 -24t26 -61t-26.5 -60.5t-72.5 -23.5zM320 737q21 0 32.5 11t11.5 29q0 17 -12 29t-33 12q-19 0 -30 -12t-11 -30q0 -16 11.5 -27.5t30.5 -11.5zM364 968h143l-145 -89h-104zM420 191h-214l-58 -191h-127
l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5z" />
    <glyph glyph-name="uni01FB" unicode="&#x1fb;" horiz-adv-x="508" 
d="M305 849h121l-145 -112h-85zM444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91
q0 -34 19.5 -52t50.5 -18q35 0 61 19.5t35 47.5q4 12 4 27zM254 523q-49 0 -79 27t-30 67q0 43 29.5 70.5t80.5 27.5q49 0 77.5 -27.5t28.5 -69.5q0 -41 -30 -68t-77 -27zM253 567q22 0 35.5 15t13.5 37q0 21 -13.5 36t-36.5 15q-21 0 -33.5 -15.5t-12.5 -37.5
q0 -19 13 -34.5t34 -15.5z" />
    <glyph glyph-name="AEacute" unicode="&#x1fc;" horiz-adv-x="832" 
d="M128 0h-127l296 674h473l-1 -101h-280l21 -176h253v-100h-238l25 -196h252v-101h-357l-27 208h-201zM255 307h149l-21 177q-9 69 -12 103h-3l-39 -102zM484 830h135l-124 -120h-99z" />
    <glyph glyph-name="aeacute" unicode="&#x1fd;" horiz-adv-x="790" 
d="M421 698h121l-123 -148h-85zM754 216h-317q0 -63 39.5 -98.5t103.5 -35.5q73 0 134 26l19 -84q-69 -35 -170 -35q-63 0 -113 25t-73 71h-3q-55 -96 -181 -96q-75 0 -118 42.5t-43 103.5q0 86 77 132.5t213 45.5v17q0 9 -4 20.5t-14 26t-32 24.5t-52 10q-71 0 -127 -37
l-26 79q76 45 163 45q119 0 159 -90h2q58 90 168 90q52 0 92 -22t62 -57.5t33 -75t11 -79.5q0 -15 -3 -48zM325 175v60q-74 2 -123 -19t-49 -71q0 -32 20.5 -50t51.5 -18q35 0 60 19t36 47q4 12 4 32zM437 301h205q1 39 -22 76.5t-75 37.5q-50 0 -78.5 -37t-29.5 -77z" />
    <glyph glyph-name="Oslashacute" unicode="&#x1fe;" horiz-adv-x="704" 
d="M135 -56l-67 54l59 86q-91 94 -91 251q0 153 89 251.5t229 98.5q90 0 158 -43l57 81l71 -48l-59 -85q88 -94 88 -248q0 -165 -90 -259t-226 -94q-87 0 -156 43zM198 189l256 364q-45 35 -101 35q-90 0 -141.5 -72t-51.5 -179q0 -79 36 -149zM509 481l-254 -361
q40 -34 97 -34q89 0 141 71.5t52 181.5q0 78 -34 142h-2zM379 836h135l-124 -120h-99z" />
    <glyph glyph-name="oslashacute" unicode="&#x1ff;" horiz-adv-x="564" 
d="M308 698h121l-123 -148h-85zM283 498h1q66 0 120 -30l44 65l47 -33l-44 -65q78 -70 78 -189q0 -124 -73 -190.5t-173 -66.5q-69 0 -120 29l-46 -66l-45 36l44 63q-81 68 -81 191q0 116 69.5 186t178.5 70zM182 145l167 240q-26 23 -68 23q-63 0 -95 -49.5t-32 -115.5
q0 -58 26 -98h2zM383 338l-166 -237q26 -23 64 -23q59 0 94 46t35 119q0 54 -24 95h-3z" />
    <glyph glyph-name="Scommaaccent" unicode="&#x218;" horiz-adv-x="519" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -87 -66 -145t-190 -58
q-51 0 -101.5 12.5t-80.5 30.5zM192 -232l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="scommaaccent" unicode="&#x219;" horiz-adv-x="417" 
d="M36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-136 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q66 -24 97.5 -59.5t32.5 -89.5q0 -68 -52 -110.5t-146 -42.5
q-85 0 -148 35zM141 -233l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="525" 
d="M200 0v571h-192v103h509v-103h-194v-571h-123zM186 -232l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="351" 
d="M87 577l120 35v-125h117v-92h-117v-215q0 -46 14.5 -68t48.5 -22q27 0 48 6l2 -94q-33 -13 -89 -13q-68 0 -106 41t-38 136v229h-70v92h70v90zM123 -236l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="uni021C" unicode="&#x21c;" horiz-adv-x="512" 
d="M69 -121l-14 95q67 8 131 34t110 72t46 99q0 47 -34 79.5t-97 32.5q-42 0 -98 -18l-14 85q82 17 138 58t56 95q0 35 -27.5 56.5t-71.5 21.5q-68 0 -136 -35l-24 81q83 50 186 50q86 0 141 -42t55 -110q0 -53 -33.5 -95.5t-87.5 -67.5v-2q77 -9 127.5 -57t50.5 -125
q0 -122 -114.5 -208t-289.5 -99z" />
    <glyph glyph-name="uni021D" unicode="&#x21d;" horiz-adv-x="453" 
d="M47 -209l-13 94q20 1 47 4.5t67 15t72 28.5t55 47.5t23 68.5q0 40 -27.5 66.5t-86.5 26.5q-32 0 -86 -16l-17 85q176 40 176 122q0 33 -24.5 51.5t-68.5 18.5q-52 0 -121 -36l-24 80q77 51 177 51q83 0 132 -38t49 -103q0 -91 -105 -135v-3q70 -9 111.5 -50.5
t41.5 -106.5q0 -112 -106.5 -187.5t-271.5 -83.5z" />
    <glyph glyph-name="uni0232" unicode="&#x232;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM162 803h251v-69h-251v69z" />
    <glyph glyph-name="uni0233" unicode="&#x233;" horiz-adv-x="500" 
d="M131 655h239v-71h-239v71zM8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" 
d="M108 698h84l97 -148h-84l-54 91h-2l-54 -91h-84z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" 
d="M193 550h-85l-98 148h85l54 -91h2l55 91h84z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" 
d="M31 655h239v-71h-239v71z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" 
d="M18 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" 
d="M130 563h-1q-26 0 -43.5 18t-17.5 44t18 44t44 18q25 0 42.5 -18t17.5 -44t-17 -44t-43 -18z" />
    <glyph glyph-name="ring" unicode="&#x2da;" 
d="M142 538q-49 0 -79 27t-30 67q0 43 29.5 70.5t80.5 27.5q49 0 77.5 -27.5t28.5 -69.5q0 -41 -30 -68t-77 -27zM141 582q22 0 35.5 15t13.5 37q0 21 -13.5 36t-36.5 15q-21 0 -33.5 -15.5t-12.5 -37.5q0 -19 13 -34.5t34 -15.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" 
d="M100 3h71q-38 -57 -38 -86q0 -23 14 -37t38 -14q25 0 45 9l18 -60q-35 -28 -92 -28q-48 0 -76 25t-28 73q0 59 48 118z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" 
d="M78 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" 
d="M89 697h105l-104 -143h-72zM249 697h105l-104 -143h-71z" />
    <glyph glyph-name="uni037E" unicode="&#x37e;" horiz-adv-x="250" 
d="M109 -113l-83 -8q40 146 49 260l129 4q-38 -146 -95 -256zM134 302h-1q-34 0 -56 23.5t-22 59.5t22 59.5t58 23.5q35 0 57 -23t22 -60q0 -36 -22 -59.5t-58 -23.5z" />
    <glyph glyph-name="tonos" unicode="&#x384;" horiz-adv-x="302" 
d="M133 698h113l-82 -148h-80z" />
    <glyph glyph-name="dieresistonos" unicode="&#x385;" horiz-adv-x="395" 
d="M168 720h95l-52 -166h-64zM61 568h-1q-23 0 -39 16.5t-16 39.5q0 24 16.5 40t40.5 16q23 0 38 -16t15 -40q0 -23 -15.5 -39.5t-38.5 -16.5zM332 568h-1q-23 0 -38.5 16.5t-15.5 39.5q0 24 16 40t39 16q24 0 39.5 -16t15.5 -40q0 -23 -15.5 -39.5t-39.5 -16.5z" />
    <glyph glyph-name="Alphatonos" unicode="&#x386;" horiz-adv-x="642" 
d="M425 191h-214l-57 -191h-128l217 674h157l219 -674h-133zM231 284h174l-53 166q-9 28 -34 126h-3q-19 -84 -32 -126zM43 674h102l-35 -165h-71z" />
    <glyph glyph-name="anoteleia" unicode="&#x387;" horiz-adv-x="250" 
d="M125 231h-1q-36 0 -59 24.5t-23 61.5t23.5 61.5t59.5 24.5q37 0 59.5 -24t23.5 -62q0 -37 -23 -61.5t-60 -24.5z" />
    <glyph glyph-name="Epsilontonos" unicode="&#x388;" horiz-adv-x="606" 
d="M539 399v-100h-254v-198h284v-101h-408v674h393v-101h-269v-174h254zM5 674h102l-36 -165h-71z" />
    <glyph glyph-name="Etatonos" unicode="&#x389;" horiz-adv-x="763" 
d="M161 674h123v-270h285v270h123v-674h-123v297h-285v-297h-123v674zM5 674h102l-36 -165h-71z" />
    <glyph glyph-name="Iotatonos" unicode="&#x38a;" horiz-adv-x="355" 
d="M161 674h124v-674h-124v674zM5 674h102l-36 -165h-71z" />
    <glyph glyph-name="Omicrontonos" unicode="&#x38c;" horiz-adv-x="763" 
d="M406 -11h-1q-141 0 -226 96t-85 247q0 156 89.5 254.5t231.5 98.5q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-232 -94.5zM410 87h1q87 0 137 71.5t50 180.5q0 102 -49 175t-137 73t-138 -73t-50 -179q0 -105 50.5 -176.5t135.5 -71.5zM5 674h102l-36 -165h-71z
" />
    <glyph glyph-name="Upsilontonos" unicode="&#x38e;" horiz-adv-x="746" 
d="M145 579l20 101q43 -2 94 -25.5t88 -64.5q79 -89 94 -231h6q19 131 105 226q36 40 83 66t84 29l20 -103q-69 -22 -132.5 -98t-90.5 -183q-18 -70 -18 -138v-158h-123v159q0 68 -12 134q-25 120 -82 195t-136 91zM5 674h102l-36 -165h-71z" />
    <glyph glyph-name="Omegatonos" unicode="&#x38f;" horiz-adv-x="782" 
d="M610 98v-3h140v-95h-277v74q138 99 137 276q0 95 -51 166t-138 71t-140.5 -70t-53.5 -167q0 -90 39.5 -165t98.5 -111v-74h-279v95h139v3q-53 43 -89.5 111.5t-36.5 153.5q0 138 91.5 230t233.5 92q143 0 228 -91t85 -221q0 -169 -127 -275zM5 674h102l-36 -165h-71z" />
    <glyph glyph-name="iotadieresistonos" unicode="&#x390;" horiz-adv-x="260" 
d="M190 487v-350q0 -30 10.5 -42.5t35.5 -11.5l-7 -86q-18 -8 -53 -8q-54 0 -81.5 30t-27.5 106v362h123zM109 708h95l-53 -165h-64zM3 557h-1q-23 0 -39 16t-16 39t16.5 39t39.5 16q22 0 37.5 -16t15.5 -39t-15.5 -39t-37.5 -16zM272 557h-1q-22 0 -37.5 16t-15.5 39t16 39
t39 16q22 0 37.5 -16t15.5 -39t-15 -39t-39 -16z" />
    <glyph glyph-name="Alpha" unicode="&#x391;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5z" />
    <glyph glyph-name="Beta" unicode="&#x392;" horiz-adv-x="576" 
d="M71 2v663q68 14 179 14q131 0 193 -43q70 -42 70 -126q0 -48 -29 -87t-83 -59v-2q58 -15 97 -58t39 -109q0 -86 -65 -139q-72 -63 -250 -63q-81 0 -151 9zM193 583v-184h63q63 0 98 26.5t35 70.5q0 92 -126 92q-48 0 -70 -5zM193 309v-220q16 -3 65 -3q65 0 107 27.5
t42 85.5q0 56 -42.5 83t-110.5 27h-61z" />
    <glyph glyph-name="Gamma" unicode="&#x393;" horiz-adv-x="468" 
d="M71 674h387v-103h-264v-571h-123v674z" />
    <glyph glyph-name="uni0394" unicode="&#x394;" horiz-adv-x="636" 
d="M20 0v64l225 610h146l221 -609v-65h-592zM153 101h319l-114 322q-33 102 -45 148h-3q-16 -60 -41 -137z" />
    <glyph glyph-name="Epsilon" unicode="&#x395;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254z" />
    <glyph glyph-name="Zeta" unicode="&#x396;" horiz-adv-x="566" 
d="M26 0v68l345 501v3h-314v102h474v-72l-340 -496v-4h345v-102h-510z" />
    <glyph glyph-name="Eta" unicode="&#x397;" horiz-adv-x="672" 
d="M71 674h123v-270h285v270h123v-674h-123v297h-285v-297h-123v674z" />
    <glyph glyph-name="Theta" unicode="&#x398;" horiz-adv-x="706" 
d="M348 -11h-1q-141 0 -226 96t-85 247q0 156 89.5 254.5t231.5 98.5q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-232 -94.5zM352 87h1q87 0 137.5 71.5t50.5 180.5q0 103 -49.5 175.5t-137.5 72.5q-89 0 -139 -72.5t-50 -179.5q0 -105 50.5 -176.5t136.5 -71.5z
M234 395h237v-100h-237v100z" />
    <glyph glyph-name="Iota" unicode="&#x399;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674z" />
    <glyph glyph-name="Kappa" unicode="&#x39a;" horiz-adv-x="582" 
d="M71 0v674h122v-310h3q14 22 49 73l176 237h152l-232 -287l247 -387h-144l-191 309l-60 -72v-237h-122z" />
    <glyph glyph-name="Lambda" unicode="&#x39b;" horiz-adv-x="630" 
d="M614 0h-134l-134 431q-8 28 -35 137h-2q-21 -97 -32 -137l-128 -431h-128l216 674h155z" />
    <glyph glyph-name="Mu" unicode="&#x39c;" horiz-adv-x="827" 
d="M653 0l-13 275q-9 195 -8 284h-3q-34 -130 -80 -259l-98 -292h-95l-90 288q-41 136 -68 263h-2q-6 -177 -12 -288l-15 -271h-115l45 674h162l88 -271q35 -116 62 -234h3q31 125 67 235l93 270h160l39 -674h-120z" />
    <glyph glyph-name="Nu" unicode="&#x39d;" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-674h-127l-176 297q-77 131 -121 232l-3 -1q6 -104 6 -287v-241z" />
    <glyph glyph-name="Xi" unicode="&#x39e;" horiz-adv-x="559" 
d="M55 674h456v-100h-456v100zM87 397h391v-100h-391v100zM43 101h480v-101h-480v101z" />
    <glyph glyph-name="Omicron" unicode="&#x39f;" horiz-adv-x="704" 
d="M347 -11h-1q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-232 -94.5zM352 87h1q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73t-138 -73t-49 -179q-1 -104 49.5 -176t136.5 -72z" />
    <glyph glyph-name="Pi" unicode="&#x3a0;" horiz-adv-x="652" 
d="M581 674v-674h-123v570h-264v-570h-123v674h510z" />
    <glyph glyph-name="Rho" unicode="&#x3a1;" horiz-adv-x="559" 
d="M71 0v665q77 14 188 14q133 0 199 -55q66 -54 66 -149q0 -98 -58 -153q-72 -72 -213 -72q-39 0 -60 4v-254h-122zM193 579v-228q19 -5 60 -5q69 0 108.5 32.5t39.5 91.5q0 56 -36 85.5t-101 29.5q-45 0 -71 -6z" />
    <glyph glyph-name="Sigma" unicode="&#x3a3;" horiz-adv-x="566" 
d="M186 105v-4h350v-101h-508v71l216 263l-204 259v81h467v-101h-310v-4l180 -230z" />
    <glyph glyph-name="Tau" unicode="&#x3a4;" horiz-adv-x="525" 
d="M200 0v571h-192v103h509v-103h-194v-571h-123z" />
    <glyph glyph-name="Upsilon" unicode="&#x3a5;" horiz-adv-x="609" 
d="M9 579l20 101q43 -2 94 -25.5t88 -64.5q79 -88 95 -231h5q19 131 105 226q36 40 83 66t84 29l20 -103q-69 -22 -132.5 -98t-90.5 -183q-18 -70 -18 -138v-158h-123v159q0 68 -12 134q-25 120 -82 195t-136 91z" />
    <glyph glyph-name="Phi" unicode="&#x3a6;" horiz-adv-x="755" 
d="M435 27v-54h-117v54q-55 6 -104 27t-90 57.5t-65 94t-24 128.5q0 73 26 132.5t68.5 97t91 59.5t99.5 27v51h116v-51q117 -12 200.5 -92.5t83.5 -218.5q0 -137 -83 -218t-202 -94zM319 114v450q-62 -9 -111 -68.5t-49 -160.5q0 -94 48 -152.5t112 -68.5zM435 564v-450
q65 8 112.5 65.5t47.5 156.5q0 102 -47.5 161t-112.5 67z" />
    <glyph glyph-name="Chi" unicode="&#x3a7;" horiz-adv-x="594" 
d="M574 0h-142l-73 134q-41 73 -67 127h-3q-19 -45 -61 -127l-67 -134h-141l200 341l-192 333h141l73 -139q26 -49 56 -113h2q25 58 53 113l72 139h141l-197 -329z" />
    <glyph glyph-name="Psi" unicode="&#x3a8;" horiz-adv-x="717" 
d="M673 674v-195q0 -136 -70.5 -207.5t-186.5 -77.5v-194h-114v194q-112 6 -185 68t-73 187v225h119v-205q0 -93 36.5 -134.5t102.5 -45.5v385h114v-386q138 9 138 207v179h119z" />
    <glyph glyph-name="uni03A9" unicode="&#x3a9;" horiz-adv-x="727" 
d="M555 98v-3h140v-95h-277v74q60 43 98.5 114.5t38.5 162.5q0 95 -51 165.5t-138 70.5q-88 0 -141 -69.5t-53 -167.5q0 -86 40 -163t98 -113v-74h-279v95h138v3q-53 43 -89 111.5t-36 154.5q0 138 91.5 229.5t233.5 91.5q143 0 228 -90.5t85 -220.5q0 -166 -127 -276z" />
    <glyph glyph-name="Iotadieresis" unicode="&#x3aa;" horiz-adv-x="264" 
d="M35 713h-1q-25 0 -42 17.5t-17 42.5t17.5 42.5t42.5 17.5t41.5 -17t16.5 -43q0 -25 -16.5 -42.5t-41.5 -17.5zM230 713h-1q-25 0 -41.5 17.5t-16.5 42.5t17 42.5t42 17.5t42 -17t17 -43q0 -25 -16.5 -42.5t-42.5 -17.5zM71 674h123v-674h-123v674z" />
    <glyph glyph-name="Upsilondieresis" unicode="&#x3ab;" horiz-adv-x="609" 
d="M211 697h-1q-25 0 -42 18t-17 42q0 25 17.5 42.5t42.5 17.5t41.5 -17t16.5 -43q0 -25 -16.5 -42.5t-41.5 -17.5zM406 697h-1q-25 0 -41.5 17.5t-16.5 42.5q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -42.5t-42.5 -17.5zM9 579l20 101q43 -2 94 -25.5
t88 -64.5q79 -88 95 -231h5q19 131 105 226q36 40 83 66t84 29l20 -103q-69 -22 -132.5 -98t-90.5 -183q-18 -70 -18 -138v-158h-123v159q0 68 -12 134q-25 120 -82 195t-136 91z" />
    <glyph glyph-name="alphatonos" unicode="&#x3ac;" horiz-adv-x="569" 
d="M286 694h114l-82 -148h-81zM410 487h103q-14 -154 -14 -332q0 -75 47 -73l-8 -87q-14 -6 -43 -6q-82 0 -95 95h-3q-53 -95 -161 -95q-87 0 -144 67.5t-57 177.5q0 116 66.5 190t153.5 74q98 0 140 -85h4q3 34 11 74zM160 239v-1q0 -63 30 -107t79 -44q38 0 68 31t40 74
q4 25 4 57t-3 46q-8 46 -36 77t-67 31q-51 0 -83 -47t-32 -117z" />
    <glyph glyph-name="epsilontonos" unicode="&#x3ad;" horiz-adv-x="455" 
d="M250 694h114l-82 -148h-81zM149 257v5q-44 12 -67 38.5t-23 63.5q0 54 53.5 94t151.5 40q94 0 156 -37l-23 -79q-50 29 -118 29q-44 0 -70 -16t-26 -42q0 -54 122 -56h53v-82h-54q-137 -2 -137 -64q0 -33 32.5 -51.5t80.5 -18.5q71 0 137 33l18 -81q-68 -44 -185 -44
q-88 0 -151 36.5t-63 112.5q0 44 32 77t81 42z" />
    <glyph glyph-name="etatonos" unicode="&#x3ae;" horiz-adv-x="572" 
d="M294 694h114l-82 -148h-81zM176 414h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-311q0 -137 11 -176h-118q-16 33 -16 175v298q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124v342q0 100 -15 145h110q13 -27 15 -73z" />
    <glyph glyph-name="iotatonos" unicode="&#x3af;" horiz-adv-x="260" 
d="M131 694h115l-82 -147h-82zM190 487v-350q0 -30 10.5 -42.5t35.5 -11.5l-7 -86q-18 -8 -53 -8q-54 0 -82 30t-28 106v362h124z" />
    <glyph glyph-name="upsilondieresistonos" unicode="&#x3b0;" horiz-adv-x="543" 
d="M349 487h115q37 -72 37 -188q0 -60 -12 -112t-37.5 -98t-73.5 -73t-114 -27q-90 0 -147.5 52.5t-57.5 151.5v131q0 126 -11 163h119q15 -16 15 -93v-190q0 -56 27 -85.5t65 -29.5q53 0 79.5 55t26.5 146q0 59 -10 115t-21 82zM241 707h95l-53 -165h-64zM134 556h-1
q-23 0 -38.5 16t-15.5 39t16 39t39 16t38 -15.5t15 -39.5q0 -23 -15 -39t-38 -16zM405 556h-1q-23 0 -38.5 16t-15.5 39t16 39t39 16t38.5 -15.5t15.5 -39.5q0 -23 -15 -39t-39 -16z" />
    <glyph glyph-name="alpha" unicode="&#x3b1;" horiz-adv-x="568" 
d="M410 487h103q-14 -154 -14 -332q0 -75 47 -73l-8 -87q-14 -6 -43 -6q-82 0 -95 95h-3q-53 -95 -161 -95q-87 0 -144 67.5t-57 177.5q0 116 66.5 190t153.5 74q98 0 140 -85h4q3 34 11 74zM160 239v-1q0 -63 30 -107t79 -44q38 0 68 31t40 74q4 25 4 57t-3 46
q-8 46 -36 77t-67 31q-51 0 -83 -47t-32 -117z" />
    <glyph glyph-name="beta" unicode="&#x3b2;" horiz-adv-x="575" 
d="M185 41h-2v-62q0 -129 13 -177h-115q-17 39 -17 171v449q0 171 77 242q60 57 154 57q85 0 136.5 -50.5t51.5 -128.5q0 -49 -23.5 -91t-60.5 -61v-5q58 -13 99 -63t41 -124q0 -95 -61 -152t-149 -57q-89 0 -144 52zM183 442v-296q45 -64 117 -64q52 0 83.5 34.5t31.5 84.5
q0 59 -36 92t-94 36h-24v79q11 0 17 2q44 5 69 37t25 76q0 46 -24 74t-62 28q-103 0 -103 -183z" />
    <glyph glyph-name="gamma" unicode="&#x3b3;" horiz-adv-x="492" 
d="M297 -198h-120q6 178 -17 281q-45 206 -170 404h138q33 -50 72 -152.5t58 -201.5h4q92 189 92 315q0 11 -0.5 23t-0.5 16h117q2 -9 2 -36q0 -176 -172 -429q-6 -10 -6 -24q0 -3 3 -196z" />
    <glyph glyph-name="delta" unicode="&#x3b4;" horiz-adv-x="562" 
d="M486 672l-34 -78q-17 13 -54.5 25.5t-73.5 12.5q-67 0 -67 -35q0 -14 13 -27t51 -42l81 -65q125 -98 125 -224q0 -112 -71 -181t-177 -69q-103 0 -173.5 66.5t-70.5 176.5q0 85 49 145.5t125 97.5v4l-21 17q-58 55 -58 101q0 49 49.5 86.5t136.5 37.5q106 0 170 -49z
M326 377l-49 40q-116 -64 -116 -184q0 -67 37 -111t88 -44q50 0 83.5 41.5t33.5 109.5q0 85 -77 148z" />
    <glyph glyph-name="epsilon" unicode="&#x3b5;" horiz-adv-x="455" 
d="M149 258v4q-43 12 -66.5 39t-23.5 63q0 54 53.5 94t151.5 40q96 0 156 -37l-23 -79q-50 29 -118 29q-44 0 -70 -16t-26 -42q0 -54 122 -56h53v-82h-54q-137 -2 -137 -64q0 -33 32.5 -51.5t80.5 -18.5q73 0 137 34l18 -82q-68 -44 -185 -44q-88 0 -151 36.5t-63 112.5
q0 44 32 77.5t81 42.5z" />
    <glyph glyph-name="zeta" unicode="&#x3b6;" horiz-adv-x="427" 
d="M351 -148l-77 24q9 17 18.5 51t10.5 53q-67 11 -114 28q-152 54 -152 220q0 101 59 205.5t162 181.5v4h-58q-77 0 -114 12l12 79q32 -3 131 -3h178l18 -83q-110 -52 -186.5 -159.5t-76.5 -213.5q0 -115 103 -157q42 -19 144 -34q10 -2 10 -12q0 -47 -21 -105t-47 -91z
" />
    <glyph glyph-name="eta" unicode="&#x3b7;" horiz-adv-x="572" 
d="M176 414h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-311q0 -137 11 -176h-118q-16 33 -16 175v298q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124v342q0 100 -15 145h110q13 -27 15 -73z" />
    <glyph glyph-name="theta" unicode="&#x3b8;" horiz-adv-x="554" 
d="M278 -11h-1q-115 0 -178.5 95t-63.5 276q0 159 65 260t183 101t177 -97t59 -264q0 -173 -60.5 -272t-180.5 -99zM157 408h240q0 99 -31 162.5t-88 63.5q-56 0 -88.5 -65t-32.5 -161zM398 319h-240q0 -113 33 -178t89 -65q59 0 88.5 65.5t29.5 177.5z" />
    <glyph glyph-name="iota" unicode="&#x3b9;" horiz-adv-x="260" 
d="M190 487v-350q0 -30 10.5 -42.5t35.5 -11.5l-7 -86q-18 -8 -53 -8q-54 0 -82 30t-28 106v362h124z" />
    <glyph glyph-name="kappa" unicode="&#x3ba;" horiz-adv-x="523" 
d="M302 265v-4q32 -8 63.5 -42t68.5 -91l83 -128h-142l-63 100q-36 53 -62 79.5t-53 27.5h-7v-207h-124v371q0 91 -12 116h120q16 -18 16 -85v-124h3q36 38 177 209h140v-5z" />
    <glyph glyph-name="lambda" unicode="&#x3bb;" horiz-adv-x="497" 
d="M357 0l-81 246q-20 64 -30 105h-5q-9 -46 -27 -102l-80 -249h-129l173 449q5 11 5 24q0 12 -5 26q-46 121 -114 121h-19l11 94q12 4 43 4q72 0 117 -56.5t87 -168.5l185 -493h-131z" />
    <glyph glyph-name="uni03BC" unicode="&#x3bc;" horiz-adv-x="570" 
d="M396 71h-3q-16 -32 -49 -55.5t-79 -23.5q-59 0 -86 40v-50q0 -131 10 -180h-109q-14 38 -14 177v508h123v-279q0 -55 22.5 -87t67.5 -32q34 0 59.5 20t35.5 46q7 14 7 38v294h123v-339q0 -36 9.5 -50t33.5 -16l-7 -86q-17 -7 -44 -7q-82 0 -100 82z" />
    <glyph glyph-name="nu" unicode="&#x3bd;" horiz-adv-x="492" 
d="M1 487h132l78 -247q8 -25 20.5 -71l14.5 -53h4q102 209 102 329v42h115q3 -16 3 -37q0 -93 -47 -202.5t-130 -247.5h-119z" />
    <glyph glyph-name="xi" unicode="&#x3be;" horiz-adv-x="454" 
d="M380 435v-85h-27q-91 0 -139.5 -36.5t-48.5 -95.5q0 -50 35 -82.5t107 -51.5q57 -14 130 -24q11 -2 11 -12q0 -42 -21 -103t-46 -93l-78 24q9 16 17.5 50.5t9.5 53.5q-45 6 -79.5 14t-77.5 25t-71 39.5t-47.5 61t-19.5 86.5q0 70 43.5 123t111.5 72v5q-108 38 -108 142
q0 71 58.5 122t162.5 51q78 0 132 -27l-22 -81q-44 20 -94 20q-54 0 -83.5 -28t-29.5 -69q0 -45 36.5 -70.5t97.5 -29.5q6 0 19.5 -0.5t20.5 -0.5z" />
    <glyph glyph-name="omicron" unicode="&#x3bf;" horiz-adv-x="564" 
d="M280 -11h-1q-106 0 -175 68t-69 183q0 118 70 188t182 70q108 0 175 -69.5t67 -180.5q0 -65 -21.5 -116t-57 -81.5t-79 -46t-91.5 -15.5zM282 78h1q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 32.5 -118.5t87.5 -46.5z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="580" 
d="M547 393h-63v-244q0 -97 13 -149h-118q-17 26 -17 139v254h-129q-2 -97 -20 -217.5t-41 -175.5h-118q24 70 41.5 187.5t18.5 205.5q-59 0 -92 -8l-14 72q35 30 158 30h393z" />
    <glyph glyph-name="rho" unicode="&#x3c1;" horiz-adv-x="579" 
d="M187 47h-2v-69q0 -137 7 -176h-119q-11 37 -11 174v223q0 160 83 235q67 64 166 64q105 0 169 -69t64 -181q0 -122 -67 -190.5t-158 -68.5q-92 0 -132 58zM185 231v-38q0 -15 3 -32q9 -32 39.5 -54.5t68.5 -22.5q57 0 90 45t33 116q0 66 -31.5 109t-84.5 43
q-50 0 -84 -43t-34 -123z" />
    <glyph glyph-name="uni03C2" unicode="&#x3c2;" horiz-adv-x="439" 
d="M428 475l-25 -88q-41 17 -89 17q-67 0 -109.5 -47t-42.5 -117q0 -113 118 -150q50 -15 135 -29q9 -1 9 -14q0 -46 -20.5 -107t-44.5 -89l-79 25q9 16 18 50t11 53q-55 10 -86 19q-85 24 -136.5 81t-51.5 152q0 108 74.5 187.5t201.5 79.5q71 0 117 -23z" />
    <glyph glyph-name="sigma" unicode="&#x3c3;" horiz-adv-x="576" 
d="M445 412l-1 -4q36 -28 56 -72.5t20 -91.5q0 -124 -71 -189.5t-170 -65.5q-106 0 -175 67.5t-69 181.5q0 120 71 190t190 70h93h66h111l-5 -90q-57 0 -116 4zM281 78h1q51 0 83.5 47t32.5 120q0 65 -29 114t-86 49q-60 0 -90.5 -48.5t-30.5 -116.5q0 -72 33 -118.5
t86 -46.5z" />
    <glyph glyph-name="tau" unicode="&#x3c4;" horiz-adv-x="435" 
d="M345 86l-6 -87q-18 -10 -60 -10q-70 0 -100 39.5t-30 122.5v242h-46q-53 0 -95 -8l-16 72q41 30 154 30h280l-13 -94h-141v-235q0 -73 53 -73q15 0 20 1z" />
    <glyph glyph-name="upsilon" unicode="&#x3c5;" horiz-adv-x="543" 
d="M349 487h115q37 -72 37 -188q0 -60 -12 -112t-37.5 -98t-73.5 -73t-114 -27q-90 0 -147.5 52.5t-57.5 151.5v131q0 126 -11 163h119q15 -16 15 -93v-190q0 -56 27 -85.5t65 -29.5q53 0 79.5 55t26.5 146q0 59 -10 115t-21 82z" />
    <glyph glyph-name="phi" unicode="&#x3c6;" horiz-adv-x="680" 
d="M208 500l47 -66q-40 -27 -68.5 -74t-28.5 -110q0 -68 33.5 -115.5t95.5 -58.5v260q0 82 42 122.5t104 38.5q84 -2 147.5 -69t63.5 -173q0 -117 -73 -186t-173 -79v-188h-111v188q-105 8 -178.5 73t-73.5 184q0 95 55 162.5t118 90.5zM397 359v-284q59 9 94.5 56t35.5 122
q0 65 -30 111.5t-66 46.5q-34 0 -34 -52z" />
    <glyph glyph-name="chi" unicode="&#x3c7;" horiz-adv-x="483" 
d="M10 487h133l108 -239h3q20 49 42 95l64 144h118l-170 -330l173 -349l-116 -28l-129 274l-4 -1l-16.5 -39.5t-25.5 -57.5l-80 -176l-105 29l173 347z" />
    <glyph glyph-name="psi" unicode="&#x3c8;" horiz-adv-x="694" 
d="M299 -198v187q-238 15 -238 260v119q0 85 -7 119h112q13 -17 13 -93v-135q0 -174 120 -183v538h110v-538q59 2 93 61.5t34 149.5q0 67 -11.5 123t-22.5 77h111q40 -70 40 -191q0 -58 -13 -109t-40.5 -95t-76 -71.5t-113.5 -31.5v-187h-111z" />
    <glyph glyph-name="omega" unicode="&#x3c9;" horiz-adv-x="716" 
d="M415 369l-3 -184q0 -40 17.5 -66.5t46.5 -26.5q35 0 59.5 45t24.5 126q0 67 -20 128t-54 97v3h114q32 -30 56 -93t24 -139q0 -128 -56 -199t-138 -71q-44 0 -78.5 22t-50.5 59h-4q-42 -81 -130 -81q-77 0 -132.5 66.5t-55.5 192.5q0 77 24.5 141.5t58.5 101.5h112v-3
q-33 -40 -54 -102t-21 -132q0 -80 25.5 -121t58.5 -41q28 0 45.5 27t16.5 66l-2 184h116z" />
    <glyph glyph-name="iotadieresis" unicode="&#x3ca;" horiz-adv-x="260" 
d="M35 552h-1q-26 0 -43 17.5t-17 42.5q0 26 17.5 43.5t43.5 17.5q25 0 41.5 -17.5t16.5 -43.5q0 -25 -16.5 -42.5t-41.5 -17.5zM230 552h-1q-26 0 -42.5 17.5t-16.5 42.5q0 26 17 43.5t43 17.5q25 0 42 -17.5t17 -43.5q0 -25 -16.5 -42.5t-42.5 -17.5zM190 487v-350
q0 -30 10.5 -42.5t35.5 -11.5l-7 -86q-18 -8 -53 -8q-54 0 -81.5 30t-27.5 106v362h123z" />
    <glyph glyph-name="upsilondieresis" unicode="&#x3cb;" horiz-adv-x="543" 
d="M168 552h-1q-26 0 -43 17.5t-17 42.5q0 26 17.5 43.5t43.5 17.5q25 0 41.5 -17.5t16.5 -43.5q0 -25 -16.5 -42.5t-41.5 -17.5zM363 552h-1q-25 0 -42 17.5t-17 42.5q0 26 17.5 43.5t42.5 17.5t42 -17.5t17 -43.5q0 -25 -16.5 -42.5t-42.5 -17.5zM349 487h115
q37 -72 37 -188q0 -60 -12 -112t-37.5 -98t-73.5 -73t-114 -27q-90 0 -147.5 52.5t-57.5 151.5v131q0 126 -11 163h119q15 -16 15 -93v-190q0 -56 27 -85.5t65 -29.5q53 0 79.5 55t26.5 146q0 59 -10 115t-21 82z" />
    <glyph glyph-name="omicrontonos" unicode="&#x3cc;" horiz-adv-x="564" 
d="M285 694h114l-82 -148h-81zM280 -11h-1q-106 0 -175 68t-69 183q0 118 70 188t182 70q108 0 175 -69.5t67 -180.5q0 -65 -21.5 -116t-57 -81.5t-79 -46t-91.5 -15.5zM282 78h1q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49q-61 0 -91.5 -48.5t-30.5 -116.5
q0 -72 32.5 -118.5t87.5 -46.5z" />
    <glyph glyph-name="upsilontonos" unicode="&#x3cd;" horiz-adv-x="543" 
d="M269 694h115l-82 -147h-82zM349 487h115q37 -72 37 -188q0 -60 -12 -112t-37.5 -98t-73.5 -73t-114 -27q-90 0 -147.5 52.5t-57.5 151.5v131q0 126 -11 163h119q15 -16 15 -93v-190q0 -56 27 -85.5t65 -29.5q53 0 79.5 55t26.5 146q0 59 -10 115t-21 82z" />
    <glyph glyph-name="omegatonos" unicode="&#x3ce;" horiz-adv-x="716" 
d="M360 694h114l-82 -148h-81zM415 369l-3 -184q0 -40 18 -66.5t46 -26.5q35 0 59.5 45t24.5 126q0 67 -20 128t-54 97v3h114q33 -30 57 -92.5t24 -139.5q0 -127 -56.5 -198.5t-138.5 -71.5q-44 0 -78.5 22t-50.5 59h-4q-42 -81 -130 -81q-77 0 -132.5 66.5t-55.5 192.5
q0 77 24.5 141.5t58.5 101.5h112v-3q-33 -40 -54 -102t-21 -132q0 -80 25.5 -121t58.5 -41q28 0 45.5 27t16.5 66l-2 184h116z" />
    <glyph glyph-name="afii10023" unicode="&#x401;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM166 716h-1q-25 0 -42 18t-17 43t17 42.5t43 17.5q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM363 716h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43
q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="afii10051" unicode="&#x402;" horiz-adv-x="696" 
d="M8 674h541v-102h-238v-156h3q55 34 126 34q103 0 164 -81q55 -74 55 -194q0 -87 -31 -154t-89 -104q-56 -52 -131 -63l-20 99q64 17 98 58q44 52 44 154q0 89 -32 135q-35 50 -95 50q-50 0 -92 -31v-319h-123v572h-180v102z" />
    <glyph glyph-name="afii10052" unicode="&#x403;" horiz-adv-x="464" 
d="M71 674h385v-101h-262v-573h-123v674zM278 830h135l-124 -120h-99z" />
    <glyph glyph-name="afii10053" unicode="&#x404;" horiz-adv-x="587" 
d="M556 652l-26 -95q-58 28 -133 28q-97 0 -157 -55t-72 -140h324v-96h-323q5 -89 68 -146t163 -57q75 0 133 25l20 -94q-64 -33 -173 -33q-157 0 -250.5 93t-93.5 253q0 95 39.5 173t122 127.5t194.5 49.5q98 0 164 -33z" />
    <glyph glyph-name="afii10054" unicode="&#x405;" horiz-adv-x="519" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -87 -66 -145t-190 -58
q-51 0 -101.5 12.5t-80.5 30.5z" />
    <glyph glyph-name="afii10055" unicode="&#x406;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674z" />
    <glyph glyph-name="afii10056" unicode="&#x407;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM34 716h-1q-25 0 -42 18t-17 43t17 42.5t43 17.5q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM231 716h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="afii10057" unicode="&#x408;" horiz-adv-x="392" 
d="M205 241v433h123v-437q0 -133 -58.5 -190.5t-165.5 -57.5q-61 0 -103 16l15 99q40 -12 76 -12q56 0 84.5 33t28.5 116z" />
    <glyph glyph-name="afii10058" unicode="&#x409;" horiz-adv-x="916" 
d="M124 674h413v-246q35 5 81 5q111 0 186 -54.5t75 -159.5q0 -94 -61 -150q-81 -76 -253 -76q-79 0 -151 9v571h-170v-180q0 -143 -23 -235t-90 -135q-53 -32 -119 -32l-17 98q46 7 75 38q54 53 54 257v290zM537 336v-246q19 -3 60 -3q66 0 110.5 34t44.5 96t-44 92.5
t-110 30.5q-23 0 -61 -4z" />
    <glyph glyph-name="afii10059" unicode="&#x40a;" horiz-adv-x="928" 
d="M71 674h123v-259h232v259h123v-251q44 4 87 4q110 0 182.5 -53t72.5 -155q0 -94 -61 -150q-80 -76 -252 -76q-80 0 -152 9v310h-232v-312h-123v674zM548 331v-240q27 -4 62 -4q66 0 109.5 33t43.5 95q0 61 -42.5 90.5t-107.5 29.5q-29 0 -65 -4z" />
    <glyph glyph-name="afii10060" unicode="&#x40b;" horiz-adv-x="705" 
d="M8 674h531v-102h-230v-161h3q70 43 147 43q82 0 129 -50q52 -55 52 -159v-245h-123v229q0 65 -28 96q-27 29 -73 29q-57 0 -107 -36v-318h-123v572h-178v102z" />
    <glyph glyph-name="afii10061" unicode="&#x40c;" horiz-adv-x="577" 
d="M71 674h121v-289h20l206 289h149l-244 -304q68 -13 106 -54.5t63 -112.5q58 -164 76 -203h-132q-15 30 -56 153q-23 70 -57.5 105.5t-101.5 35.5h-29v-294h-121v674zM315 825h135l-124 -120h-99z" />
    <glyph glyph-name="afii10062" unicode="&#x40e;" horiz-adv-x="558" 
d="M3 675h139l114 -264q10 -24 25.5 -65.5t22.5 -58.5h3q6 18 19 63t19 64l88 261h130l-133 -340q-77 -193 -130 -263q-69 -86 -168 -86q-34 0 -63 9l10 99q20 -4 42 -4q39 0 66.5 25.5t43.5 60.5q7 17 -3 36zM141 824h87q3 -61 52 -61q46 0 53 61h86q-8 -116 -142 -116
q-127 0 -136 116z" />
    <glyph glyph-name="afii10145" unicode="&#x40f;" horiz-adv-x="656" 
d="M71 674h123v-573h270v573h123v-674h-200l-7 -180h-102l-7 180h-200v674z" />
    <glyph glyph-name="afii10017" unicode="&#x410;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5z" />
    <glyph glyph-name="afii10018" unicode="&#x411;" horiz-adv-x="578" 
d="M71 674h416v-101h-294v-145q38 5 83 5q111 0 182 -51q82 -55 82 -163q0 -92 -60 -149q-84 -77 -258 -77q-81 0 -151 9v672zM193 336v-245q24 -4 65 -4q76 0 118 42q35 33 35 88q0 60 -45 91q-42 32 -112 32q-21 0 -61 -4z" />
    <glyph glyph-name="afii10019" unicode="&#x412;" horiz-adv-x="576" 
d="M71 2v663q68 14 179 14q131 0 193 -43q70 -42 70 -126q0 -48 -29 -87t-83 -59v-2q58 -15 97 -58t39 -109q0 -86 -65 -139q-72 -63 -250 -63q-81 0 -151 9zM193 583v-184h63q63 0 98 26.5t35 70.5q0 92 -126 92q-48 0 -70 -5zM193 309v-220q16 -3 65 -3q65 0 107 27.5
t42 85.5q0 56 -42.5 83t-110.5 27h-61z" />
    <glyph glyph-name="afii10020" unicode="&#x413;" horiz-adv-x="464" 
d="M71 674h385v-101h-262v-573h-123v674z" />
    <glyph glyph-name="afii10021" unicode="&#x414;" horiz-adv-x="662" 
d="M160 674h412v-576l64 -3l-7 -262h-94l-7 167h-409l-7 -167h-94l-6 262l54 3q47 85 65 149q29 99 29 251v176zM274 573v-106q0 -143 -32 -246q-18 -62 -50 -120h257v472h-175z" />
    <glyph glyph-name="afii10022" unicode="&#x415;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254z" />
    <glyph glyph-name="afii10024" unicode="&#x416;" horiz-adv-x="842" 
d="M12 674h144l186 -289h19v289h119v-289h20l186 289h143l-234 -305q68 -7 107 -49.5t65 -118.5q3 -8 27 -85.5t41 -115.5h-125q-10 20 -18.5 45t-19 61t-13.5 47q-22 70 -55 105.5t-98 35.5h-26v-294h-119v294h-27q-65 0 -97 -35t-56 -106q-4 -12 -14 -47t-18.5 -60.5
t-17.5 -45.5h-125q13 29 24.5 62t24.5 74.5t20 61.5q26 75 64.5 117.5t106.5 53.5z" />
    <glyph glyph-name="afii10025" unicode="&#x417;" horiz-adv-x="520" 
d="M75 544l-30 88q86 53 196 53q100 0 159 -46.5t59 -122.5q0 -61 -38 -104t-99 -58v-2q69 -8 115 -51.5t46 -111.5q0 -69 -41 -116t-98 -65.5t-124 -18.5q-112 0 -191 46l29 95q75 -41 157 -41q65 0 101.5 28.5t36.5 77.5q0 31 -14 53t-38.5 33.5t-51 16.5t-56.5 5h-53v92
h53q59 0 98.5 28.5t39.5 73.5q0 41 -30 64.5t-81 23.5q-67 0 -145 -41z" />
    <glyph glyph-name="afii10026" unicode="&#x418;" horiz-adv-x="675" 
d="M71 674h114v-238q0 -152 -8 -296l2 -1q46 100 123 233l178 302h125v-674h-114v239q0 181 9 279h-2q-44 -105 -120 -233l-170 -285h-137v674z" />
    <glyph glyph-name="afii10027" unicode="&#x419;" horiz-adv-x="675" 
d="M71 674h114v-238q0 -152 -8 -296l2 -1q46 100 123 233l178 302h125v-674h-114v239q0 181 9 279h-2q-44 -105 -120 -233l-170 -285h-137v674zM199 822h87q3 -61 52 -61q46 0 53 61h86q-8 -116 -142 -116q-127 0 -136 116z" />
    <glyph glyph-name="afii10028" unicode="&#x41a;" horiz-adv-x="577" 
d="M71 674h121v-289h20l206 289h149l-244 -304q68 -13 106 -54.5t63 -112.5q58 -164 76 -203h-132q-15 30 -56 153q-23 70 -57.5 105.5t-101.5 35.5h-29v-294h-121v674z" />
    <glyph glyph-name="afii10029" unicode="&#x41b;" horiz-adv-x="622" 
d="M123 674h428v-674h-123v573h-184v-180q0 -143 -23 -235t-91 -135q-52 -32 -120 -32l-15 98q43 7 74 36q54 55 54 259v290z" />
    <glyph glyph-name="afii10030" unicode="&#x41c;" horiz-adv-x="827" 
d="M653 0l-13 275q-9 195 -8 284h-3q-34 -130 -80 -259l-98 -292h-95l-90 288q-41 136 -68 263h-2q-6 -177 -12 -288l-15 -271h-115l45 674h162l88 -271q35 -116 62 -234h3q31 125 67 235l93 270h160l39 -674h-120z" />
    <glyph glyph-name="afii10031" unicode="&#x41d;" horiz-adv-x="672" 
d="M71 674h123v-270h285v270h123v-674h-123v297h-285v-297h-123v674z" />
    <glyph glyph-name="afii10032" unicode="&#x41e;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73z" />
    <glyph glyph-name="afii10033" unicode="&#x41f;" horiz-adv-x="660" 
d="M71 674h518v-674h-123v573h-272v-573h-123v674z" />
    <glyph glyph-name="afii10034" unicode="&#x420;" horiz-adv-x="559" 
d="M71 0v665q77 14 188 14q133 0 199 -55q66 -54 66 -149q0 -98 -58 -153q-72 -72 -213 -72q-39 0 -60 4v-254h-122zM193 579v-228q19 -5 60 -5q69 0 108.5 32.5t39.5 91.5q0 56 -36 85.5t-101 29.5q-45 0 -71 -6z" />
    <glyph glyph-name="afii10035" unicode="&#x421;" horiz-adv-x="588" 
d="M534 117l20 -97q-60 -31 -174 -31q-159 0 -251.5 92.5t-92.5 247.5q0 163 101 259.5t259 96.5q106 0 164 -30l-27 -98q-59 26 -132 26q-106 0 -171 -65t-65 -183q0 -112 62.5 -177.5t172.5 -65.5q81 0 134 25z" />
    <glyph glyph-name="afii10036" unicode="&#x422;" horiz-adv-x="525" 
d="M200 0v571h-192v103h509v-103h-194v-571h-123z" />
    <glyph glyph-name="afii10037" unicode="&#x423;" horiz-adv-x="558" 
d="M3 675h139l114 -264q10 -24 25.5 -65.5t22.5 -58.5h3q6 18 19 63t19 64l88 261h130l-133 -340q-77 -193 -130 -263q-69 -86 -168 -86q-34 0 -63 9l10 99q20 -4 42 -4q39 0 66.5 25.5t43.5 60.5q7 17 -3 36z" />
    <glyph glyph-name="afii10038" unicode="&#x424;" horiz-adv-x="760" 
d="M323 702h116v-60q56 -4 105.5 -22.5t91 -53.5t65.5 -93.5t24 -134.5t-24 -134t-65.5 -93t-91 -53.5t-107.5 -23.5v-62h-116v62q-57 4 -106 22t-90 52.5t-65 92t-24 133.5q0 77 26 137t69.5 95t91.5 53.5t100 22.5v60zM322 121v436q-65 -6 -113.5 -59.5t-48.5 -160.5
q0 -101 48 -155t114 -61zM438 557v-436q66 6 114 59t48 158q0 108 -47 160.5t-115 58.5z" />
    <glyph glyph-name="afii10039" unicode="&#x425;" horiz-adv-x="594" 
d="M574 0h-142l-73 134q-41 73 -67 127h-3q-19 -45 -61 -127l-67 -134h-141l200 341l-192 333h141l73 -139q26 -49 56 -113h2q25 58 53 113l72 139h141l-197 -329z" />
    <glyph glyph-name="afii10040" unicode="&#x426;" horiz-adv-x="675" 
d="M71 674h123v-573h269v573h123v-576l64 -3l-7 -262h-95l-7 167h-470v674z" />
    <glyph glyph-name="afii10041" unicode="&#x427;" horiz-adv-x="622" 
d="M65 674h123v-207q0 -128 126 -128q62 0 114 32v303h123v-681h-122v285h-3q-73 -38 -156 -38q-89 0 -147 49.5t-58 157.5v227z" />
    <glyph glyph-name="afii10042" unicode="&#x428;" horiz-adv-x="884" 
d="M71 674h120v-573h190v573h121v-573h190v573h121v-674h-742v674z" />
    <glyph glyph-name="afii10043" unicode="&#x429;" horiz-adv-x="903" 
d="M71 674h120v-573h190v573h121v-573h190v573h121v-576l64 -3l-7 -262h-94l-7 167h-698v674z" />
    <glyph glyph-name="afii10044" unicode="&#x42a;" horiz-adv-x="672" 
d="M5 674h295v-242q32 5 81 5q106 0 180 -55.5t74 -159.5q0 -90 -53 -146q-82 -83 -254 -83q-79 0 -151 9v571h-172v101zM299 337v-246q23 -4 62 -4q64 0 104.5 34t40.5 96t-41.5 93t-103.5 31q-37 0 -62 -4z" />
    <glyph glyph-name="afii10045" unicode="&#x42b;" horiz-adv-x="805" 
d="M71 674h122v-243q30 6 78 6q112 0 187.5 -55t75.5 -162q0 -88 -53 -144q-80 -83 -260 -83q-80 0 -150 9v672zM193 337v-246q20 -4 60 -4q64 0 108 34t44 96t-45 93t-107 31q-34 0 -60 -4zM602 674h123v-674h-123v674z" />
    <glyph glyph-name="afii10046" unicode="&#x42c;" horiz-adv-x="577" 
d="M71 674h122v-242q35 5 83 5q114 0 189 -54t75 -161q0 -90 -53 -146q-80 -83 -264 -83q-82 0 -152 9v672zM193 337v-246q24 -4 63 -4q67 0 111 33t44 96q0 64 -43 94.5t-111 30.5q-37 0 -64 -4z" />
    <glyph glyph-name="afii10047" unicode="&#x42d;" horiz-adv-x="578" 
d="M110 295v96h300q-8 85 -62 139.5t-144 54.5q-68 0 -141 -30l-26 91q88 39 176 39q162 0 246 -98.5t84 -250.5q0 -163 -96.5 -255t-243.5 -92q-100 0 -175 33l23 96q65 -28 140 -28q95 0 153.5 57t65.5 148h-300z" />
    <glyph glyph-name="afii10048" unicode="&#x42e;" horiz-adv-x="896" 
d="M71 674h122v-276h90q17 131 95 209t196 78q134 0 211 -96t77 -245q0 -167 -82.5 -261t-214.5 -94q-122 0 -199 84t-86 222h-87v-295h-122v674zM569 87h1q78 0 122.5 71t44.5 181q0 66 -18 121t-56.5 91t-92.5 36q-80 0 -125 -73t-45 -179q0 -105 45.5 -176.5t123.5 -71.5
z" />
    <glyph glyph-name="afii10049" unicode="&#x42f;" horiz-adv-x="574" 
d="M142 0h-131q15 26 31 69q6 17 19 57t21 61.5t23.5 50t34.5 46.5q23 23 56 34v3q-63 11 -104 54t-41 111q0 88 66 141q66 52 208 52q97 0 179 -14v-665h-122v277h-50q-53 0 -83 -29q-20 -20 -34.5 -51t-30 -82t-19.5 -62q-9 -28 -23 -53zM382 369v210q-30 7 -72 7
q-61 0 -98.5 -26.5t-37.5 -82.5q0 -52 40.5 -81t103.5 -29q48 0 64 2z" />
    <glyph glyph-name="afii10065" unicode="&#x430;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27z" />
    <glyph glyph-name="afii10066" unicode="&#x431;" horiz-adv-x="555" 
d="M470 720l-7 -98q-32 -11 -108 -20q-115 -15 -163 -76q-46 -56 -53 -139h4q23 46 67.5 73t96.5 27q96 0 154.5 -65.5t58.5 -176.5q0 -118 -65.5 -187.5t-173.5 -69.5q-121 0 -182 91.5t-61 232.5q0 185 92 284q65 81 206 101q106 15 134 23zM394 239v1q0 64 -27.5 112
t-87.5 48q-59 0 -88 -52q-26 -41 -26 -99q0 -64 29.5 -117.5t86.5 -53.5q58 0 85.5 47.5t27.5 113.5z" />
    <glyph glyph-name="afii10067" unicode="&#x432;" horiz-adv-x="528" 
d="M65 1v480q72 12 177 12q230 0 230 -123q0 -44 -28.5 -71.5t-71.5 -36.5v-2q56 -9 89 -39.5t33 -79.5q0 -147 -274 -147q-87 0 -155 7zM185 410v-120h51q51 0 83 15.5t32 48.5q0 60 -107 60q-21 0 -59 -4zM184 216v-142q30 -3 60 -3q51 0 87.5 16.5t36.5 55.5
q0 73 -131 73h-53z" />
    <glyph glyph-name="afii10068" unicode="&#x433;" horiz-adv-x="402" 
d="M65 487h322v-97h-199v-390h-123v487z" />
    <glyph glyph-name="afii10069" unicode="&#x434;" horiz-adv-x="563" 
d="M126 487h359v-398l52 -2l-5 -243h-94l-5 156h-320l-6 -156h-94l-4 242l44 3q32 50 48 100q25 82 25 175v123zM233 395v-66q0 -81 -21 -151q-11 -45 -36 -87h190v304h-133z" />
    <glyph glyph-name="afii10070" unicode="&#x435;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="afii10072" unicode="&#x436;" horiz-adv-x="706" 
d="M6 487h138l133 -203h17v203h118v-203h16l133 203h138l-180 -215q87 -12 129 -126q42 -120 56 -146h-124q-14 25 -43 111q-33 92 -112 92h-13v-203h-118v203h-14q-42 0 -68.5 -21.5t-45.5 -70.5q-29 -86 -44 -111h-120q14 30 56 145q43 113 128 127z" />
    <glyph glyph-name="afii10073" unicode="&#x437;" horiz-adv-x="462" 
d="M111 212v78h59q49 0 77.5 17.5t28.5 44.5t-23.5 43t-64.5 16q-63 0 -120 -35l-26 73q77 48 182 48q72 0 127 -31.5t55 -97.5q0 -44 -31.5 -72t-75.5 -38v-2q53 -6 91 -36.5t38 -81.5q0 -53 -37.5 -88t-85.5 -47.5t-104 -12.5q-101 0 -176 44l28 78q61 -32 135 -34
q50 0 77.5 19t27.5 48q0 67 -122 67h-60z" />
    <glyph glyph-name="afii10074" unicode="&#x438;" horiz-adv-x="578" 
d="M65 487h117v-175q0 -78 -7 -197h3q61 135 66 144l120 228h149v-487h-117v174q0 59 8 206h-4q-32 -77 -64 -140l-124 -240h-147v487z" />
    <glyph glyph-name="afii10075" unicode="&#x439;" horiz-adv-x="578" 
d="M65 487h117v-175q0 -78 -7 -197h3q61 135 66 144l120 228h149v-487h-117v174q0 59 8 206h-4q-32 -77 -64 -140l-124 -240h-147v487zM151 686h82q1 -33 15 -53t39 -20q50 0 57 73h82q-4 -64 -41.5 -98.5t-99.5 -34.5q-127 0 -134 133z" />
    <glyph glyph-name="afii10076" unicode="&#x43a;" horiz-adv-x="507" 
d="M66 487h123v-202h18l150 202h146l-194 -214q87 -13 141 -141q5 -11 15 -38.5t20 -51t20 -42.5h-128q-14 22 -45 104q-20 50 -50 74t-77 24h-16v-202h-123v487z" />
    <glyph glyph-name="afii10077" unicode="&#x43b;" horiz-adv-x="528" 
d="M93 487h371v-487h-124v392h-132v-105q0 -103 -15.5 -164t-56.5 -92q-41 -40 -122 -40l-13 96q35 7 52 24q25 21 32.5 61t7.5 119v196z" />
    <glyph glyph-name="afii10078" unicode="&#x43c;" horiz-adv-x="683" 
d="M43 0l35 487h155l69 -212q28 -89 38 -127h2q3 11 47 148l65 191h155l31 -487h-115l-9 212q-7 160 -7 172h-3q-20 -71 -50 -159l-78 -219h-89l-71 214q-12 40 -44 162h-4q-2 -107 -6 -173l-10 -209h-111z" />
    <glyph glyph-name="afii10079" unicode="&#x43d;" horiz-adv-x="564" 
d="M65 487h123v-186h188v186h123v-487h-123v205h-188v-205h-123v487z" />
    <glyph glyph-name="afii10080" unicode="&#x43e;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49z" />
    <glyph glyph-name="afii10081" unicode="&#x43f;" horiz-adv-x="560" 
d="M65 487h430v-487h-124v392h-183v-392h-123v487z" />
    <glyph glyph-name="afii10082" unicode="&#x440;" horiz-adv-x="585" 
d="M66 -198v523q0 70 -4 162h108l6 -75h2q56 86 167 86q86 0 145.5 -68.5t59.5 -179.5q0 -124 -65.5 -192.5t-156.5 -68.5q-45 0 -81.5 17.5t-55.5 47.5h-2v-252h-123zM189 279v-75q0 -19 4 -34q9 -38 39 -62t69 -24q58 0 91 44t33 117q0 67 -32 111.5t-89 44.5
q-39 0 -70 -25.5t-40 -65.5q-5 -17 -5 -31z" />
    <glyph glyph-name="afii10083" unicode="&#x441;" horiz-adv-x="449" 
d="M407 106l17 -91q-56 -26 -138 -26q-114 0 -182.5 68t-68.5 181t73 186.5t198 73.5q69 0 119 -23l-22 -92q-42 19 -93 19q-69 0 -109.5 -45t-39.5 -113q0 -73 42 -115t107 -42q51 0 97 19z" />
    <glyph glyph-name="afii10084" unicode="&#x442;" horiz-adv-x="438" 
d="M12 487h415v-95h-146v-392h-123v392h-146v95z" />
    <glyph glyph-name="afii10085" unicode="&#x443;" horiz-adv-x="500" 
d="M8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="afii10086" unicode="&#x444;" horiz-adv-x="684" 
d="M285 706h115l-1 -210q110 -10 180 -76t70 -177q0 -114 -71 -179t-179 -73l1 -189h-115l1 188q-109 8 -180 73.5t-71 176.5q0 114 71 179.5t180 75.5zM286 72v342q-61 -11 -95 -58t-34 -113q0 -67 34 -113.5t95 -57.5zM398 414v-342q62 12 96 58.5t34 113.5t-34 112.5
t-96 57.5z" />
    <glyph glyph-name="afii10087" unicode="&#x445;" horiz-adv-x="494" 
d="M11 487h137l58 -92l44 -75h3q14 27 42 77l54 90h134l-163 -234l166 -253h-139l-60 97q-19 31 -44 79h-2q-15 -30 -44 -79l-56 -97h-136l169 248z" />
    <glyph glyph-name="afii10088" unicode="&#x446;" horiz-adv-x="572" 
d="M65 487h123v-392h183v392h123v-398l53 -2l-6 -243h-94l-5 156h-377v487z" />
    <glyph glyph-name="afii10089" unicode="&#x447;" horiz-adv-x="539" 
d="M60 487h123v-158q0 -45 19.5 -68t65.5 -23q48 0 83 26v223h123v-494h-123v189h-2q-51 -33 -127 -33t-119 42.5t-43 125.5v170z" />
    <glyph glyph-name="afii10090" unicode="&#x448;" horiz-adv-x="779" 
d="M65 487h122v-392h142v392h122v-392h142v392h121v-487h-649v487z" />
    <glyph glyph-name="afii10091" unicode="&#x449;" horiz-adv-x="792" 
d="M65 487h122v-392h142v392h122v-392h142v392h121v-398l53 -2l-6 -243h-94l-5 156h-597v487z" />
    <glyph glyph-name="afii10092" unicode="&#x44a;" horiz-adv-x="618" 
d="M17 487h263v-153q38 5 82 5q58 0 105.5 -14.5t82.5 -54.5t35 -101q0 -75 -60 -120q-71 -54 -219 -54q-70 0 -149 7v390h-140v95zM280 249v-166q30 -4 58 -4q52 0 88 20.5t36 65.5q0 49 -37.5 69t-91.5 20q-11 0 -53 -5z" />
    <glyph glyph-name="afii10093" unicode="&#x44b;" horiz-adv-x="727" 
d="M65 487h123v-154q38 6 78 6q90 0 153 -41t63 -126q0 -79 -60 -122q-72 -55 -209 -55q-71 0 -148 7v485zM187 249v-167q23 -3 53 -3q50 0 84.5 21.5t34.5 67.5q0 45 -33 65.5t-81 20.5q-18 0 -58 -5zM539 487h123v-487h-123v487z" />
    <glyph glyph-name="afii10094" unicode="&#x44c;" horiz-adv-x="529" 
d="M65 487h123v-154q42 6 78 6q45 0 83.5 -8.5t73 -26.5t54 -52t19.5 -80q0 -79 -60 -122q-72 -55 -223 -55q-71 0 -148 7v485zM187 249v-166q32 -4 57 -4q52 0 90.5 20.5t38.5 67.5q0 33 -22.5 53.5t-49.5 27t-59 6.5q-27 0 -55 -5z" />
    <glyph glyph-name="afii10095" unicode="&#x44d;" horiz-adv-x="475" 
d="M88 206v80h227q-7 54 -44 87t-103 33q-58 0 -112 -25l-24 80q68 37 157 37q110 0 177 -64q74 -71 74 -191q0 -113 -72.5 -183.5t-196.5 -70.5q-83 0 -146 33l20 85q56 -23 116 -23q62 0 103 32.5t49 89.5h-225z" />
    <glyph glyph-name="afii10096" unicode="&#x44e;" horiz-adv-x="741" 
d="M65 487h122v-192h72q14 93 74.5 148t149.5 55q99 0 161 -69t62 -181q0 -120 -66 -189.5t-163 -69.5q-90 0 -150 58t-70 157h-70v-204h-122v487zM478 78h1q50 0 77 48.5t27 118.5q0 67 -26.5 115t-76.5 48q-51 0 -78.5 -48.5t-27.5 -117.5t27 -116.5t77 -47.5z" />
    <glyph glyph-name="afii10097" unicode="&#x44f;" horiz-adv-x="528" 
d="M463 483v-483h-122v193h-46q-46 0 -75 -32q-11 -9 -20.5 -25t-14.5 -29l-13 -35t-12 -32q-12 -27 -19 -40h-132q13 16 30 54l13.5 34.5t16.5 40t18.5 34.5t24.5 31q26 26 58 33v3q-50 5 -88.5 36t-38.5 84q0 75 75 113q59 31 172 31q94 0 173 -11zM341 271v140
q-28 5 -61 5q-46 0 -77.5 -17.5t-31.5 -55.5q0 -35 34 -54t75 -19q45 0 61 1z" />
    <glyph glyph-name="afii10071" unicode="&#x451;" horiz-adv-x="516" 
d="M176 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM375 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85
q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="afii10099" unicode="&#x452;" horiz-adv-x="573" 
d="M4 634h77v76h123v-76h218v-83h-218v-123h2q58 60 137 60q91 0 143 -84q49 -76 49 -204q0 -218 -114 -298q-58 -43 -120 -49l-23 98q50 10 81 43q53 58 53 197q0 92 -31 149q-30 49 -78 49q-49 0 -85 -45q-14 -17 -14 -47v-297h-123v551h-77v83z" />
    <glyph glyph-name="afii10100" unicode="&#x453;" horiz-adv-x="402" 
d="M65 487h322v-97h-199v-390h-123v487zM244 698h121l-123 -148h-85z" />
    <glyph glyph-name="afii10101" unicode="&#x454;" horiz-adv-x="467" 
d="M442 469l-24 -86q-48 24 -104 24q-64 0 -103.5 -33.5t-47.5 -87.5h222v-80h-221q7 -57 48 -90t104 -33q52 0 106 21l20 -85q-56 -30 -146 -30q-121 0 -191 69t-70 181q0 119 78.5 189t199.5 70q77 0 129 -29z" />
    <glyph glyph-name="afii10102" unicode="&#x455;" horiz-adv-x="417" 
d="M36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-136 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q66 -24 97.5 -59.5t32.5 -89.5q0 -68 -52 -110.5t-146 -42.5
q-85 0 -148 35z" />
    <glyph glyph-name="afii10103" unicode="&#x456;" horiz-adv-x="256" 
d="M190 0h-124v487h124v-487zM128 690q31 0 49.5 -19t19.5 -48q0 -28 -19 -47t-51 -19q-30 0 -49 19t-19 47q0 29 19.5 48t49.5 19z" />
    <glyph glyph-name="afii10104" unicode="&#x457;" horiz-adv-x="256" 
d="M29 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM228 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM190 0h-124v487h124v-487z" />
    <glyph glyph-name="afii10105" unicode="&#x458;" horiz-adv-x="270" 
d="M-30 -213l-12 96q65 5 92 35q18 18 25 54.5t7 114.5v400h123v-435q0 -145 -63 -208q-61 -57 -172 -57zM144 690q31 0 49.5 -19t18.5 -48q0 -28 -19 -47t-51 -19q-30 0 -48.5 19t-18.5 47q0 29 19 48t50 19z" />
    <glyph glyph-name="afii10106" unicode="&#x459;" horiz-adv-x="802" 
d="M96 487h368v-155q35 5 77 5q60 0 109 -15t84 -54.5t35 -99.5q0 -75 -56 -117q-69 -56 -222 -56q-73 0 -150 7v390h-131v-105q0 -101 -16.5 -163t-58.5 -93q-42 -40 -121 -40l-13 96q34 6 52 23q26 20 34.5 62.5t8.5 118.5v196zM463 248v-166q48 -3 57 -3q50 0 88 20.5
t38 66.5q0 47 -36.5 67t-84.5 20q-17 0 -62 -5z" />
    <glyph glyph-name="afii10107" unicode="&#x45a;" horiz-adv-x="814" 
d="M65 487h123v-172h168v172h123v-158q35 5 79 5q91 0 156.5 -41t65.5 -126q0 -73 -55 -118q-69 -54 -219 -54q-73 0 -150 7v220h-168v-222h-123v487zM478 246v-164q45 -3 57 -3q49 0 85.5 20t36.5 66t-35.5 65.5t-85.5 19.5q-22 0 -58 -4z" />
    <glyph glyph-name="afii10108" unicode="&#x45b;" horiz-adv-x="586" 
d="M4 634h77v76h123v-76h201v-83h-201v-139h2q24 34 59 54q39 22 86 22q80 0 125 -54.5t45 -155.5v-278h-123v263q0 125 -93 125q-35 0 -59.5 -20t-35.5 -49q-6 -17 -6 -40v-279h-123v551h-77v83z" />
    <glyph glyph-name="afii10109" unicode="&#x45c;" horiz-adv-x="507" 
d="M66 487h123v-202h18l150 202h146l-194 -214q87 -13 141 -141q5 -11 15 -38.5t20 -51t20 -42.5h-128q-14 22 -45 104q-20 50 -50 74t-77 24h-16v-202h-123v487zM281 698h121l-123 -148h-85z" />
    <glyph glyph-name="afii10110" unicode="&#x45e;" horiz-adv-x="500" 
d="M112 686h82q1 -33 15 -53t39 -20q50 0 57 73h82q-4 -64 -41.5 -98.5t-99.5 -34.5q-127 0 -134 133zM8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22
q0 10 -7 24z" />
    <glyph glyph-name="afii10193" unicode="&#x45f;" horiz-adv-x="559" 
d="M65 487h123v-392h183v392h123v-487h-158l-7 -161h-99l-7 161h-158v487z" />
    <glyph glyph-name="afii10050" unicode="&#x490;" horiz-adv-x="471" 
d="M71 674h292l16 126h89l-11 -227h-263v-573h-123v674z" />
    <glyph glyph-name="afii10098" unicode="&#x491;" horiz-adv-x="414" 
d="M65 487h232l13 114h86l-7 -211h-201v-390h-123v487z" />
    <glyph glyph-name="afii10846" unicode="&#x4d9;" horiz-adv-x="328" 
d="M77 381l-18 83q77 34 172 34q116 0 182.5 -67.5t66.5 -180.5q0 -107 -63 -184t-174 -77q-57 0 -99.5 21.5t-66 57.5t-34.5 75.5t-11 82.5q0 31 4 51h326q-2 64 -42 96t-106 32q-65 0 -137 -24zM362 191h-214q-1 -43 23.5 -80t79.5 -37q52 0 79 36t32 81z" />
    <glyph glyph-name="Wgrave" unicode="&#x1e80;" horiz-adv-x="869" 
d="M313 0h-133l-165 674h131l64 -306q26 -126 44 -235h2q8 55 49 236l73 305h129l67 -310q27 -129 41 -228h2q15 90 46 233l70 305h125l-181 -674h-132l-70 317q-27 122 -37 214h-2q-19 -114 -45 -214zM274 830h135l88 -119h-99z" />
    <glyph glyph-name="wgrave" unicode="&#x1e81;" horiz-adv-x="749" 
d="M232 698h121l87 -148h-86zM14 487h126l51 -221q21 -100 31 -158h2q8 43 40 157l63 222h100l61 -216q24 -93 39 -163h2q10 67 32 163l54 216h121l-153 -487h-114l-58 198q-22 74 -37 156h-2q-11 -68 -37 -156l-62 -198h-115z" />
    <glyph glyph-name="Wacute" unicode="&#x1e82;" horiz-adv-x="869" 
d="M313 0h-133l-165 674h131l64 -306q26 -126 44 -235h2q8 55 49 236l73 305h129l67 -310q27 -129 41 -228h2q15 90 46 233l70 305h125l-181 -674h-132l-70 317q-27 122 -37 214h-2q-19 -114 -45 -214zM472 830h135l-124 -120h-99z" />
    <glyph glyph-name="wacute" unicode="&#x1e83;" horiz-adv-x="749" 
d="M403 698h121l-123 -148h-85zM14 487h126l51 -221q21 -100 31 -158h2q8 43 40 157l63 222h100l61 -216q24 -93 39 -163h2q10 67 32 163l54 216h121l-153 -487h-114l-58 198q-22 74 -37 156h-2q-11 -68 -37 -156l-62 -198h-115z" />
    <glyph glyph-name="Wdieresis" unicode="&#x1e84;" horiz-adv-x="869" 
d="M313 0h-133l-165 674h131l64 -306q26 -126 44 -235h2q8 55 49 236l73 305h129l67 -310q27 -129 41 -228h2q15 90 46 233l70 305h125l-181 -674h-132l-70 317q-27 122 -37 214h-2q-19 -114 -45 -214zM343 716h-1q-25 0 -42 18t-17 43t17.5 42.5t42.5 17.5t41.5 -17
t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM540 716h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="wdieresis" unicode="&#x1e85;" horiz-adv-x="749" 
d="M275 563h-1q-26 0 -43.5 18t-17.5 43q0 26 18 44t45 18q25 0 42 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM474 563h-1q-26 0 -43 18t-17 43q0 26 17.5 44t43.5 18t43 -17.5t17 -44.5q0 -25 -17 -43t-43 -18zM14 487h126l51 -221q21 -100 31 -158h2q8 43 40 157l63 222h100
l61 -216q24 -93 39 -163h2q10 67 32 163l54 216h121l-153 -487h-114l-58 198q-22 74 -37 156h-2q-11 -68 -37 -156l-62 -198h-115z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM313 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM254 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM348 707l-59 15q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5
q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM274 528l-61 15q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM277 829h87l92 -119h-85l-49 66h-2l-50 -66h-84zM487 908h111l-128 -110h-63z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM204 698h88l87 -148h-83l-47 90h-2l-48 -90h-82zM419 787h101l-108 -141h-66z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM346 892h113l69 -109h-68zM271 829h87l92 -119h-85l-50 66h-2l-49 -66h-84z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM288 788h99l66 -142h-65zM204 698h88l87 -148h-83l-47 90h-2l-48 -90h-82z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM274 829h86l93 -119h-86l-49 66h-2l-49 -66h-84zM487 756l-52 13q7 20 29 43.5t22 39.5q0 13 -9 22t-23 9q-26 0 -47 -36l-39 24q35 69 104 69
q37 0 61 -21.5t24 -53.5q0 -22 -11.5 -38t-30 -34t-28.5 -37z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM202 698h88l87 -148h-83l-47 90h-2l-48 -90h-82zM413 634l-55 13q7 19 27.5 42t20.5 39q0 13 -8.5 21.5t-21.5 8.5q-22 0 -45 -34l-39 24q35 69 105 69q36 0 61 -22.5t25 -52.5q0 -31 -31.5 -63t-38.5 -45z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM270 829h93l103 -119h-92l-56 65h-2l-57 -65h-90zM248 844h-61q-1 48 19.5 76t55.5 28q26 0 64 -20q30 -17 42 -17q24 0 27 41h59
q4 -107 -75 -107q-21 0 -65 21q-29 16 -41 16q-22 0 -25 -38z" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM209 675h88l93 -125h-86l-50 72h-2l-50 -72h-85zM184 689h-61q-1 48 19.5 76.5t55.5 28.5q26 0 64 -20q30 -17 42 -17q13 0 19.5 8.5t7.5 31.5h60q3 -106 -76 -106q-25 0 -65 21q-27 15 -41 15q-22 0 -25 -38z" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM272 829h93l103 -119h-92l-57 65h-2l-56 -65h-90zM311 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5
t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM211 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM254 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM184 812h60q12 -47 75 -47q64 0 77 47h57q-6 -48 -41 -80.5t-95 -32.5t-93.5 31.5t-39.5 81.5zM346 912h116l-118 -110h-69z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM285 793h103l-118 -140h-68zM104 665h63q4 -28 25.5 -46t53.5 -18q36 0 57 18.5t23 45.5h62q-2 -60 -41 -96.5t-101 -36.5q-66 0 -103 38t-39 95z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM182 912h116l71 -110h-69zM187 812h59q14 -47 76 -47q63 0 76 47h58q-6 -48 -41 -80.5t-95 -32.5t-93.5 31.5t-39.5 81.5z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM112 793h103l82 -140h-67zM110 665h62q5 -28 26 -46t53 -18q36 0 57 18.5t23 45.5h62q-2 -60 -40.5 -96.5t-101.5 -36.5q-66 0 -102.5 37.5t-38.5 95.5z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM340 789l-53 13q7 20 27.5 43t20.5 38q0 13 -8.5 22t-21.5 9q-23 0 -45 -35l-39 24q35 69 105 69q36 0 60.5 -22t24.5 -52q0 -24 -11.5 -40.5
t-30.5 -34t-29 -34.5zM187 812h60q14 -47 75 -47q64 0 77 47h58q-6 -48 -41 -80.5t-95 -32.5t-94 31.5t-40 81.5z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM268 643l-55 13q7 19 27.5 42t20.5 39q0 13 -9 21.5t-22 8.5q-21 0 -44 -34l-39 24q35 69 105 69q36 0 61 -22t25 -53q0 -23 -11.5 -39.5t-30.5 -34t-28 -34.5zM107 665h63q4 -28 25.5 -46t53.5 -18q36 0 57 18.5t23 45.5h62
q-2 -60 -41 -96.5t-101 -36.5q-66 0 -103 38t-39 95z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM247 844h-63q1 44 21 70.5t55 26.5q25 0 63 -20q30 -17 43 -17t19 8.5t8 31.5h59q3 -106 -76 -106q-24 0 -64 21q-29 15 -41 15q-20 0 -24 -30
zM181 812h60q12 -47 75 -47q64 0 77 47h57q-6 -48 -41 -80.5t-95 -32.5t-93.5 31.5t-39.5 81.5z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM182 705h-62q0 43 20 70t55 27q25 0 63 -20q30 -17 43 -17q12 0 18.5 9t8.5 32h59q3 -107 -76 -107q-20 0 -64 21q-30 16 -41 16q-20 0 -24 -31zM114 665h62q5 -29 25 -48t50 -19q34 0 54.5 20t22.5 47h62q-2 -60 -39 -96.5t-99 -36.5
q-65 0 -100.5 38t-37.5 95z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="636" 
d="M420 191h-214l-58 -191h-127l216 674h157l219 -674h-132zM226 284h173l-53 166q-9 28 -34 126h-2q-1 -3 -13 -52.5t-19 -73.5zM183 841h68q8 -48 65 -48t67 48h66q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86zM313 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5
q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="508" 
d="M444 293v-176q0 -75 7 -117h-111l-8 54h-3q-51 -65 -144 -65q-70 0 -111.5 43t-41.5 102q0 91 76 138t213 46v8q0 33 -21 59t-78 26q-74 0 -131 -36l-24 80q70 43 176 43q108 0 154.5 -57.5t46.5 -147.5zM324 171v67q-170 4 -170 -91q0 -34 19.5 -52t50.5 -18
q35 0 61 19.5t35 47.5q4 12 4 27zM122 688h67q4 -29 20.5 -47t43.5 -18q31 0 47.5 19t18.5 46h67q0 -61 -35.5 -98t-96.5 -37q-65 0 -98.5 38.5t-33.5 96.5zM254 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM258 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82zM258 -80
q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM305 707l-59 15q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82zM278 528
l-61 15q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM203 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="516" 
d="M185 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45zM479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180
q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM227 829h87l92 -119h-85l-49 66h-2l-50 -66h-84zM437 908h111l-128 -110h-63z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82zM231 698
h88l87 -148h-83l-47 90h-2l-48 -90h-82zM446 787h101l-108 -141h-66z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM299 892h113l69 -109h-68zM224 829h87l92 -119h-85l-50 66h-2l-49 -66h-84z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82zM311 788
h99l66 -142h-65zM227 698h88l87 -148h-83l-47 90h-2l-48 -90h-82z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM226 829h86l93 -119h-86l-49 66h-2l-49 -66h-84zM439 756l-52 13q7 20 29 43.5t22 39.5q0 13 -9 22t-23 9q-26 0 -47 -36l-39 24q35 69 104 69q37 0 61 -21.5t24 -53.5q0 -22 -11.5 -38t-30 -34
t-28.5 -37z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82zM223 698
h88l87 -148h-83l-47 90h-2l-48 -90h-82zM434 634l-55 13q7 19 27.5 42t20.5 39q0 13 -8.5 21.5t-21.5 8.5q-22 0 -45 -34l-39 24q35 69 105 69q36 0 61 -22.5t25 -52.5q0 -31 -31.5 -63t-38.5 -45z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM233 829h93l103 -119h-92l-56 65h-2l-57 -65h-90zM211 844h-61q-1 48 19.5 76t55.5 28q26 0 64 -20q30 -17 42 -17q24 0 27 41h59q4 -107 -75 -107q-21 0 -65 21q-29 16 -41 16q-22 0 -25 -38z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82zM224 675
h88l93 -125h-86l-50 72h-2l-50 -72h-85zM199 689h-61q-1 48 19.5 76.5t55.5 28.5q26 0 64 -20q30 -17 42 -17q25 0 27 40h60q3 -106 -76 -106q-25 0 -65 21q-27 15 -41 15q-22 0 -25 -38z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="515" 
d="M448 399v-100h-254v-198h284v-101h-407v674h392v-101h-269v-174h254zM223 829h93l103 -119h-92l-57 65h-2l-56 -65h-90zM270 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="516" 
d="M479 209h-326q2 -62 44.5 -94t105.5 -32q77 0 135 22l18 -85q-77 -31 -170 -31q-117 0 -184 67t-67 180q0 108 63.5 185t174.5 77q57 0 99.5 -21.5t65.5 -57.5t34 -75.5t11 -82.5q0 -30 -4 -52zM153 295h214q1 44 -22.5 81.5t-78.5 37.5q-52 0 -80 -37t-33 -82zM215 698
h84l97 -148h-84l-54 91h-2l-54 -91h-84zM258 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM162 707l-59 15q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="256" 
d="M190 0h-124v487h124v-487zM148 528l-61 15q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="264" 
d="M71 674h123v-674h-123v674zM133 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="256" 
d="M190 0h-124v487h124v-487zM128 690q31 0 49.5 -19t19.5 -48q0 -28 -19 -47t-51 -19q-30 0 -49 19t-19 47q0 29 19.5 48t49.5 19zM129 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM353 -201h-1
q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM283 -80q25 0 42.5 -18t17.5 -44
t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM382 707l-59 15
q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM302 528l-61 15q6 22 30.5 49.5
t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM317 829h87
l92 -119h-85l-49 66h-2l-50 -66h-84zM527 908h111l-128 -110h-63z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM242 698h88l87 -148h-83l-47 90h-2
l-48 -90h-82zM457 787h101l-108 -141h-66z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM388 892h113
l69 -109h-68zM313 829h87l92 -119h-85l-50 66h-2l-49 -66h-84z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM327 788h99l66 -142h-65zM243 698
h88l87 -148h-83l-47 90h-2l-48 -90h-82z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM318 829h86
l93 -119h-86l-49 66h-2l-49 -66h-84zM531 756l-52 13q7 20 29 43.5t22 39.5q0 13 -9 22t-23 9q-26 0 -47 -36l-39 24q35 69 104 69q37 0 61 -21.5t24 -53.5q0 -22 -11.5 -38t-30 -34t-28.5 -37z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM238 698h88l87 -148h-83l-47 90h-2
l-48 -90h-82zM449 634l-55 13q7 19 27.5 42t20.5 39q0 13 -8.5 21.5t-21.5 8.5q-22 0 -45 -34l-39 24q35 69 105 69q36 0 61 -22.5t25 -52.5q0 -31 -31.5 -63t-38.5 -45z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM307 829h93
l103 -119h-92l-56 65h-2l-57 -65h-90zM285 844h-61q-1 48 19.5 76t55.5 28q26 0 64 -20q30 -17 42 -17q24 0 27 41h59q4 -107 -75 -107q-21 0 -65 21q-29 16 -41 16q-22 0 -25 -38z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM237 675h88l93 -125h-86l-50 72h-2
l-50 -72h-85zM212 689h-61q-1 48 19.5 76.5t55.5 28.5q26 0 64 -20q30 -17 42 -17q13 0 19.5 8.5t7.5 31.5h60q3 -106 -76 -106q-25 0 -65 21q-27 15 -41 15q-22 0 -25 -38z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" horiz-adv-x="704" 
d="M356 685q144 0 228.5 -96.5t84.5 -244.5q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5t231 98.5zM353 587q-88 0 -137.5 -72t-49.5 -181q0 -106 50.5 -176.5t136.5 -70.5q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73zM305 829h93
l103 -119h-92l-57 65h-2l-56 -65h-90zM353 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" horiz-adv-x="564" 
d="M287 498q108 0 175 -69.5t67 -180.5q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70zM284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM239 698h84l97 -148h-84l-54 91h-2
l-54 -91h-84zM283 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" horiz-adv-x="704" 
d="M353 87q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73t-138 -73t-49 -179q-1 -105 50 -176.5t137 -71.5zM597 742l90 16q21 -46 21 -93q0 -46 -26.5 -74t-78.5 -27q66 -89 66 -220q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5
t231 98.5q62 0 115 -19q10 -3 32.5 -13t40.5 -16t31 -6q41 0 41 42q0 31 -19 69zM385 830h135l-124 -120h-99z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" horiz-adv-x="573" 
d="M284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM474 561l89 15q20 -38 20 -85q0 -44 -25 -70t-70 -27q41 -60 41 -146q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70
q44 0 86 -13q8 -3 25 -10.5t31.5 -12t26.5 -4.5q37 0 37 38q0 30 -19 65zM311 698h121l-123 -148h-85z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" horiz-adv-x="704" 
d="M353 87q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73t-138 -73t-49 -179q-1 -105 50 -176.5t137 -71.5zM597 742l90 16q21 -46 21 -93q0 -46 -26.5 -74t-78.5 -27q66 -89 66 -220q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5
t231 98.5q62 0 115 -19q10 -3 32.5 -13t40.5 -16t31 -6q41 0 41 42q0 31 -19 69zM201 830h135l88 -119h-99z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" horiz-adv-x="573" 
d="M284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM474 561l89 15q20 -38 20 -85q0 -44 -25 -70t-70 -27q41 -60 41 -146q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70
q44 0 86 -13q8 -3 25 -10.5t31.5 -12t26.5 -4.5q37 0 37 38q0 30 -19 65zM141 698h121l87 -148h-86z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" horiz-adv-x="704" 
d="M353 87q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73t-138 -73t-49 -179q-1 -105 50 -176.5t137 -71.5zM597 742l90 16q21 -46 21 -93q0 -46 -26.5 -74t-78.5 -27q66 -89 66 -220q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5
t231 98.5q62 0 115 -19q10 -3 32.5 -13t40.5 -16t31 -6q41 0 41 42q0 31 -19 69zM382 707l-59 15q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" horiz-adv-x="573" 
d="M284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM474 561l89 15q20 -38 20 -85q0 -44 -25 -70t-70 -27q41 -60 41 -146q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70
q44 0 86 -13q8 -3 25 -10.5t31.5 -12t26.5 -4.5q37 0 37 38q0 30 -19 65zM302 528l-61 15q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" horiz-adv-x="704" 
d="M353 87q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73t-138 -73t-49 -179q-1 -105 50 -176.5t137 -71.5zM597 742l90 16q21 -46 21 -93q0 -46 -26.5 -74t-78.5 -27q66 -89 66 -220q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5
t231 98.5q62 0 115 -19q10 -3 32.5 -13t40.5 -16t31 -6q41 0 41 42q0 31 -19 69zM286 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" horiz-adv-x="573" 
d="M284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM474 561l89 15q20 -38 20 -85q0 -44 -25 -70t-70 -27q41 -60 41 -146q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70
q44 0 86 -13q8 -3 25 -10.5t31.5 -12t26.5 -4.5q37 0 37 38q0 30 -19 65zM210 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" horiz-adv-x="704" 
d="M353 87q87 0 136.5 71.5t49.5 180.5q0 102 -49 175t-137 73t-138 -73t-49 -179q-1 -105 50 -176.5t137 -71.5zM597 742l90 16q21 -46 21 -93q0 -46 -26.5 -74t-78.5 -27q66 -89 66 -220q0 -166 -90 -260.5t-233 -94.5q-141 0 -225.5 96t-84.5 247q0 156 89 254.5
t231 98.5q62 0 115 -19q10 -3 32.5 -13t40.5 -16t31 -6q41 0 41 42q0 31 -19 69zM353 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" horiz-adv-x="573" 
d="M284 408q-61 0 -91.5 -48.5t-30.5 -116.5q0 -72 33 -118.5t88 -46.5q52 0 85.5 46.5t33.5 120.5q0 65 -30 114t-88 49zM474 561l89 15q20 -38 20 -85q0 -44 -25 -70t-70 -27q41 -60 41 -146q0 -126 -74 -192.5t-176 -66.5q-106 0 -175 68t-69 183q0 118 70 188t182 70
q44 0 86 -13q8 -3 25 -10.5t31.5 -12t26.5 -4.5q37 0 37 38q0 30 -19 65zM285 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM334 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="569" 
d="M502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123zM285 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="666" 
d="M70 674h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h123v-385q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386zM363 707l-59 15q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73
q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="569" 
d="M502 487v-341q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h123zM305 528l-61 15q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74
q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="705" 
d="M603 775l89 15q20 -40 20 -85q0 -43 -25 -68.5t-75 -29.5l-16 -1v-317q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h99q28 0 39 9.5t11 29.5q0 30 -19 62zM357 830h135l-124 -120h-99z
" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="569" 
d="M491 589l89 15q20 -38 20 -82q0 -41 -25 -66t-64 -28l-9 -1v-281q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h82q50 0 50 40q0 29 -20 62zM327 698h121l-123 -148h-85
z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="705" 
d="M603 775l89 15q20 -40 20 -85q0 -43 -25 -68.5t-75 -29.5l-16 -1v-317q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h99q28 0 39 9.5t11 29.5q0 30 -19 62zM179 830h135l88 -119h-99z
" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="569" 
d="M491 589l89 15q20 -38 20 -82q0 -41 -25 -66t-64 -28l-9 -1v-281q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h82q50 0 50 40q0 29 -20 62zM144 698h121l87 -148h-86z
" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="705" 
d="M603 775l89 15q20 -40 20 -85q0 -43 -25 -68.5t-75 -29.5l-16 -1v-317q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h99q28 0 39 9.5t11 29.5q0 30 -19 62zM382 707l-59 15
q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="569" 
d="M491 589l89 15q20 -38 20 -82q0 -41 -25 -66t-64 -28l-9 -1v-281q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h82q50 0 50 40q0 29 -20 62zM312 528l-61 15
q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="705" 
d="M603 775l89 15q20 -40 20 -85q0 -43 -25 -68.5t-75 -29.5l-16 -1v-317q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h99q28 0 39 9.5t11 29.5q0 30 -19 62zM279 715h-60q-1 55 18.5 85.5
t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="569" 
d="M491 589l89 15q20 -38 20 -82q0 -41 -25 -66t-64 -28l-9 -1v-281q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h82q50 0 50 40q0 29 -20 62zM214 562h-62
q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="705" 
d="M603 775l89 15q20 -40 20 -85q0 -43 -25 -68.5t-75 -29.5l-16 -1v-317q0 -151 -71.5 -225.5t-197.5 -74.5q-122 0 -189.5 72.5t-67.5 226.5v386h123v-393q0 -96 37 -144.5t101 -48.5q142 0 142 193v393h99q28 0 39 9.5t11 29.5q0 30 -19 62zM353 -80q25 0 42.5 -18
t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="569" 
d="M491 589l89 15q20 -38 20 -82q0 -41 -25 -66t-64 -28l-9 -1v-281q0 -66 4 -146h-108l-6 74h-2q-51 -85 -156 -85q-74 0 -122 50.5t-48 161.5v286h123v-265q0 -133 91 -133q34 0 58.5 19.5t35.5 46.5q7 19 7 38v294h82q50 0 50 40q0 29 -20 62zM283 -80q25 0 42.5 -18
t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM136 826h135l88 -119h-99z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="500" 
d="M112 698h121l87 -148h-86zM8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM288 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="500" 
d="M8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24zM370 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z
" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM317 707l-59 15q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5
t-30 -37z" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="500" 
d="M8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24zM281 528l-61 15q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25
q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="575" 
d="M347 0h-123v281l-213 393h140l81 -173q37 -81 58 -134h2q21 53 59 134l81 173h139l-224 -390v-284zM214 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="500" 
d="M186 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45zM8 487h135l88 -261q13 -39 27 -95h3l13 47t13 49l76 260h132l-122 -332q-50 -135 -88 -205t-82 -108
q-60 -54 -129 -63l-28 104q37 9 72 34q41 27 67 77q8 14 8 22q0 10 -7 24z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="500" 
d="M30 293h440v-78h-440v78z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1000" 
d="M30 293h940v-78h-940v78z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="236" 
d="M149 439l-119 -10q48 155 108 256l76 7q-44 -123 -65 -253z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="236" 
d="M94 683l119 9q-45 -153 -108 -255l-75 -8q45 127 64 254z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="236" 
d="M94 -103l-76 -7q46 123 65 253l119 10q-44 -149 -108 -256z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="409" 
d="M148 439l-118 -10q46 155 108 256l75 7q-44 -123 -65 -253zM323 439l-119 -10q46 155 108 256l76 7q-46 -123 -65 -253z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="409" 
d="M94 683l119 9q-47 -153 -108 -255l-76 -8q44 124 65 254zM268 683l119 9q-44 -147 -108 -255l-76 -8q47 132 65 254z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="412" 
d="M93 -103l-76 -7q46 123 65 253l119 10q-46 -155 -108 -256zM267 -103l-75 -7q44 123 65 253l119 10q-48 -157 -109 -256z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="513" 
d="M201 674h112l-11 -205l173 9v-97l-173 10l11 -441h-112l11 441l-173 -10v97l173 -9z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="513" 
d="M201 674h111l-8 -192l171 10v-98l-171 11v-179l171 10v-97l-171 9l8 -198h-111l8 198l-171 -9v97l172 -10v179l-172 -11v98l172 -10z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="313" 
d="M156 142h-1q-49 0 -83 34t-34 85q-1 50 34 84.5t84 34.5q50 0 84.5 -34.5t34.5 -84.5t-34.5 -84.5t-84.5 -34.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1000" 
d="M166 -11h-1q-32 0 -53.5 22.5t-20.5 56.5q0 35 21 57t55 22t54.5 -22t21.5 -57q0 -34 -21.5 -56.5t-55.5 -22.5zM500 -11h-1q-33 0 -54 22.5t-21 56.5t21.5 56.5t54.5 22.5q34 0 55 -22t21 -57q0 -34 -21 -56.5t-55 -22.5zM834 -11h-1q-33 0 -54.5 22.5t-20.5 56.5
q0 35 21 57t55 22t54.5 -22t21.5 -57q0 -34 -21 -56.5t-55 -22.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="1227" 
d="M199 661h1q76 0 119.5 -52.5t43.5 -140.5q0 -98 -49 -152.5t-120 -54.5t-118 52.5t-48 143.5q0 92 47.5 148t123.5 56zM197 592h-1q-35 0 -54.5 -37t-19.5 -94q-1 -58 19 -94t56 -36t54.5 35t18.5 96q0 130 -73 130zM265 -12h-70l378 673h70zM647 392h1q76 0 119.5 -52.5
t43.5 -141.5q0 -98 -48.5 -152t-120.5 -54q-70 0 -117.5 52.5t-47.5 143.5t47.5 147.5t122.5 56.5zM645 322h-1q-35 0 -54.5 -37t-19.5 -94q-1 -57 19 -93.5t56 -36.5q73 0 73 131q0 130 -73 130zM1033 392h1q76 0 120 -53t44 -141q0 -97 -49 -151.5t-121 -54.5
q-70 0 -117.5 52.5t-47.5 143.5t47.5 147.5t122.5 56.5zM1031 322h-1q-35 0 -54.5 -37t-19.5 -94q-1 -57 19 -93.5t56 -36.5q73 0 73 131q0 130 -73 130z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="264" 
d="M241 440l-126 -188l126 -188h-90l-124 188l123 188h91z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="264" 
d="M148 252l-125 188h89l124 -188l-124 -188h-89z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="124" 
d="M-90 -11h-71l374 672h71z" />
    <glyph glyph-name="zero.superior" unicode="&#x2070;" horiz-adv-x="359" 
d="M178 438h-1q-74 0 -114.5 55t-40.5 145t41 145t117 55t116.5 -54.5t40.5 -144.5q0 -89 -40 -145t-119 -56zM179 504h1q64 0 64 135q0 133 -65 133q-30 0 -47 -34.5t-17 -98.5q0 -66 17 -100.5t47 -34.5z" />
    <glyph glyph-name="four.superior" unicode="&#x2074;" horiz-adv-x="372" 
d="M299 443h-90v96h-191v53l171 242h110v-232h53v-63h-53v-96zM209 602v92q0 25 4 69l-3 1q-17 -36 -35 -64l-68 -97l2 -1h100z" />
    <glyph glyph-name="five.superior" unicode="&#x2075;" horiz-adv-x="330" 
d="M285 832v-72h-158l-10 -65q7 1 28 1q67 0 104 -25q51 -32 51 -98q0 -59 -46 -97.5t-122 -38.5q-66 0 -112 23l16 65q45 -20 92 -20q32 0 57 16.5t25 45.5q0 68 -113 68q-13 0 -34.5 -2t-22.5 -2l26 201h219z" />
    <glyph glyph-name="six.superior" unicode="&#x2076;" horiz-adv-x="349" 
d="M282 837v-68q-23 2 -38 0q-56 -5 -89.5 -31.5t-42.5 -66.5h2q33 33 88 33q56 0 90.5 -34t34.5 -91q0 -61 -43 -101t-104 -40q-76 0 -117 48.5t-41 120.5q0 109 69 171q55 52 152 58q30 2 39 1zM179 504h1q25 0 41.5 20t16.5 50q0 31 -17 50.5t-48 19.5q-42 0 -60 -39
q-4 -10 -4 -20q0 -34 18.5 -57.5t51.5 -23.5z" />
    <glyph glyph-name="seven.superior" unicode="&#x2077;" horiz-adv-x="316" 
d="M22 832h280v-55l-168 -334h-94l168 316v1h-186v72z" />
    <glyph glyph-name="eight.superior" unicode="&#x2078;" horiz-adv-x="346" 
d="M175 838h1q63 0 96.5 -29.5t33.5 -69.5q0 -59 -60 -86v-2q33 -12 55 -36.5t22 -59.5q0 -52 -42 -84.5t-111 -32.5q-71 0 -109 32.5t-38 74.5q0 65 75 97v2q-29 13 -45 36t-16 49q0 49 39.5 79t98.5 30zM173 499h1q26 0 42.5 14t16.5 37q0 46 -67 65q-54 -17 -54 -61
q0 -23 16.5 -39t44.5 -16zM173 779h-1q-25 0 -39 -13.5t-14 -33.5q0 -38 61 -56q20 6 32.5 20t12.5 33q0 20 -13.5 35t-38.5 15z" />
    <glyph glyph-name="nine.superior" unicode="&#x2079;" horiz-adv-x="344" 
d="M58 439v68q15 -3 40 -1q51 5 83 26q40 28 51 76l-2 1q-29 -31 -83 -31q-53 0 -89 33t-36 88q0 58 42.5 98.5t106.5 40.5q73 0 112 -47.5t39 -119.5q0 -116 -68 -177q-55 -49 -154 -54q-7 -1 -42 -1zM170 773h-1q-25 0 -42 -20t-17 -50q0 -27 16.5 -46t44.5 -19
q40 0 58 29q5 8 5 21q0 37 -16 61t-48 24z" />
    <glyph glyph-name="parenleft.superior" unicode="&#x207d;" horiz-adv-x="193" 
d="M113 868h65q-65 -104 -65 -240q0 -133 65 -240h-65q-72 100 -72 240q0 139 72 240z" />
    <glyph glyph-name="parenright.superior" unicode="&#x207e;" horiz-adv-x="193" 
d="M80 388h-64q65 112 65 240q0 135 -65 240h64q73 -102 73 -240q0 -134 -73 -240z" />
    <glyph glyph-name="zero.inferior" unicode="&#x2080;" horiz-adv-x="359" 
d="M178 -152h-1q-74 0 -114.5 55t-40.5 145t41 145t117 55t116.5 -54.5t40.5 -144.5q0 -89 -40 -145t-119 -56zM179 -86h1q64 0 64 135q0 133 -65 133q-30 0 -47 -34.5t-17 -98.5q0 -66 17 -100.5t47 -34.5z" />
    <glyph glyph-name="one.inferior" unicode="&#x2081;" horiz-adv-x="275" 
d="M209 -147h-96v310h-2l-76 -35l-14 69l104 46h84v-390z" />
    <glyph glyph-name="two.inferior" unicode="&#x2082;" horiz-adv-x="333" 
d="M14 -147v48l62 56q68 62 94 94t26 64q0 26 -17 42t-49 16q-45 0 -85 -30l-26 63q55 42 135 42q69 0 106 -34t37 -83q0 -34 -19 -68t-40 -55t-63 -57l-28 -23v-3h157v-72h-290z" />
    <glyph glyph-name="three.inferior" unicode="&#x2083;" horiz-adv-x="328" 
d="M46 155l-20 62q52 31 129 31q66 0 100.5 -27.5t34.5 -69.5q0 -30 -22 -53.5t-58 -35.5v-2q41 -5 68 -31t27 -63q0 -51 -46 -84.5t-124 -33.5q-81 0 -126 28l21 65q43 -26 95 -26q37 0 58 16.5t21 39.5q0 29 -28 44t-67 15h-29v58h28q32 0 57.5 12.5t25.5 38.5
q0 18 -14.5 30t-42.5 12q-45 0 -88 -26z" />
    <glyph glyph-name="four.inferior" unicode="&#x2084;" horiz-adv-x="372" 
d="M299 -147h-90v96h-191v53l171 242h110v-232h53v-63h-53v-96zM209 12v92q0 25 4 69l-3 1q-17 -36 -35 -64l-68 -97l2 -1h100z" />
    <glyph glyph-name="five.inferior" unicode="&#x2085;" horiz-adv-x="330" 
d="M285 242v-72h-158l-10 -65q7 1 28 1q67 0 104 -25q51 -32 51 -98q0 -59 -46 -97.5t-122 -38.5q-66 0 -112 23l16 65q45 -20 92 -20q32 0 57 16.5t25 45.5q0 68 -113 68q-13 0 -34.5 -2t-22.5 -2l26 201h219z" />
    <glyph glyph-name="six.inferior" unicode="&#x2086;" horiz-adv-x="349" 
d="M282 247v-68q-23 2 -38 0q-56 -5 -89.5 -31.5t-42.5 -66.5h2q33 33 88 33q56 0 90.5 -34t34.5 -91q0 -61 -43 -101t-104 -40q-76 0 -117 48.5t-41 120.5q0 109 69 171q55 52 152 58q30 2 39 1zM179 -86h1q25 0 41.5 20t16.5 50q0 31 -17 50.5t-48 19.5q-42 0 -60 -39
q-4 -10 -4 -20q0 -34 18.5 -57.5t51.5 -23.5z" />
    <glyph glyph-name="seven.inferior" unicode="&#x2087;" horiz-adv-x="316" 
d="M22 242h280v-55l-168 -334h-94l168 316v1h-186v72z" />
    <glyph glyph-name="eight.inferior" unicode="&#x2088;" horiz-adv-x="346" 
d="M175 248h1q63 0 96.5 -29.5t33.5 -69.5q0 -59 -60 -86v-2q33 -12 55 -36.5t22 -59.5q0 -52 -42 -84.5t-111 -32.5q-71 0 -109 32.5t-38 74.5q0 65 75 97v2q-29 13 -45 36t-16 49q0 49 39.5 79t98.5 30zM173 -91h1q26 0 42.5 14t16.5 37q0 46 -67 65q-54 -17 -54 -61
q0 -23 16.5 -39t44.5 -16zM173 189h-1q-25 0 -39 -13.5t-14 -33.5q0 -38 61 -56q20 6 32.5 20t12.5 33q0 20 -13.5 35t-38.5 15z" />
    <glyph glyph-name="nine.inferior" unicode="&#x2089;" horiz-adv-x="344" 
d="M58 -151v68q15 -3 40 -1q51 5 83 26q40 28 51 76l-2 1q-29 -31 -83 -31q-53 0 -89 33t-36 88q0 58 42.5 98.5t106.5 40.5q73 0 112 -47.5t39 -119.5q0 -116 -68 -177q-55 -49 -154 -54q-8 -1 -42 -1zM170 183h-1q-25 0 -42 -20t-17 -50q0 -27 16.5 -46t44.5 -19
q40 0 58 29q5 8 5 21q0 37 -16 61t-48 24z" />
    <glyph glyph-name="parenleft.inferior" unicode="&#x208d;" horiz-adv-x="193" 
d="M113 278h65q-65 -104 -65 -240q0 -133 65 -240h-65q-72 100 -72 240q0 139 72 240z" />
    <glyph glyph-name="parenright.inferior" unicode="&#x208e;" horiz-adv-x="193" 
d="M80 -202h-64q65 112 65 240q0 135 -65 240h64q73 -102 73 -240q0 -134 -73 -240z" />
    <glyph glyph-name="dong" unicode="&#x20ab;" horiz-adv-x="536" 
d="M88 101h363v-75h-363v75zM342 670h105v-62h63v-61h-63v-291q0 -63 3 -99h-94l-4 54h-2q-37 -62 -123 -62q-69 0 -115.5 49.5t-46.5 129.5q0 86 49.5 137t122.5 51q72 0 103 -45h2v76h-122v61h122v62zM342 314v43q0 17 -2 23q-5 25 -28 42t-52 17q-42 0 -65 -30t-23 -77
q0 -46 23 -74.5t63 -28.5q29 0 52 16.5t29 42.5q3 12 3 26z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="536" 
d="M492 118l21 -89q-66 -40 -153 -40q-145 0 -226 103q-50 57 -63 149h-64v61h56v16q0 14 2 36h-58v62h67q18 91 75 151q85 94 216 94q79 0 143 -31l-25 -93q-50 27 -112 27q-78 0 -127 -55q-31 -33 -44 -93h262v-62h-274q-2 -11 -2 -35v-17h276v-61h-265q10 -62 43 -98
q50 -54 134 -54q62 0 118 29z" />
    <glyph glyph-name="afii61289" unicode="&#x2113;" horiz-adv-x="532" 
d="M227 499v-171q107 112 107 221q0 81 -50 81q-25 0 -41 -32t-16 -99zM445 173l58 -55q-75 -129 -216 -129q-148 0 -174 152q0 5 -1 7q-30 -21 -51 -34l-33 66q60 42 82 60v240q0 117 48 174.5t127 57.5q66 0 105 -44t39 -118q0 -86 -49.5 -165.5t-152.5 -169.5v-13
q1 -111 90 -111q67 0 128 82z" />
    <glyph glyph-name="afii61352" unicode="&#x2116;" horiz-adv-x="945" 
d="M751 638h1q76 0 120.5 -47.5t44.5 -125.5q0 -88 -48 -133t-120 -45q-73 0 -120 46.5t-47 126.5q0 82 47.5 130t121.5 48zM749 574h-1q-33 0 -51 -34.5t-18 -78.5q0 -47 20.5 -78.5t50.5 -31.5q33 0 51.5 31t18.5 81q0 45 -18 78t-53 33zM890 182h-280v64h280v-64zM176 0
h-107v653h134l136 -279q53 -115 93 -218l1 1q-10 123 -10 276v220h107v-653h-124l-139 285q-40 81 -94 222h-3q6 -125 6 -283v-224z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="636" 
d="M28 674h234v-57h-82v-216h-70v216h-82v57zM624 401h-68l-8 138q0 57 -1 93h-4q-7 -31 -23 -95l-38 -131h-68l-35 133q-1 4 -8 39.5t-11 53.5h-4q0 -35 -2 -93l-8 -138h-66l18 273h107l31 -97q6 -21 17 -79h2q8 41 18 79l33 97h101z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="727" 
d="M555 98v-3h140v-95h-277v74q60 43 98.5 114.5t38.5 162.5q0 95 -51 165.5t-138 70.5q-88 0 -141 -69.5t-53 -167.5q0 -86 40 -163t98 -113v-74h-279v95h138v3q-53 43 -89 111.5t-36 154.5q0 138 91.5 229.5t233.5 91.5q143 0 228 -90.5t85 -220.5q0 -166 -127 -276z" />
    <glyph glyph-name="estimated" unicode="&#x212e;" horiz-adv-x="817" 
d="M782 316h-605q-5 0 -5 -4v-183q0 -10 9 -21q93 -99 229 -99q145 0 240 111h55q-52 -61 -130 -96t-166 -35q-155 0 -264.5 98.5t-109.5 237.5t109.5 237.5t264.5 98.5t264 -98.5t109 -237.5v-9zM646 340v184q0 12 -10 22q-95 95 -226 95q-133 0 -228 -98q-10 -10 -10 -23
v-180q0 -5 5 -5h464q5 0 5 5z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="375" 
d="M89 591l-37 83q81 55 186 55q120 0 201 -96.5t81 -260.5q0 -181 -82 -282t-203 -101q-101 0 -156 65.5t-55 151.5q0 104 62 175t158 71q55 0 95.5 -24.5t59.5 -56.5h1v25q0 106 -50 169.5t-126 63.5q-78 0 -135 -38zM247 88h1q53 0 91.5 54.5t47.5 129.5
q-11 33 -42.5 58.5t-74.5 25.5q-52 0 -87 -43t-35 -107q0 -55 27.5 -86.5t71.5 -31.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="603" 
d="M569 0h-535v64l197 589h145l193 -588v-65zM438 100l-95 292q-27 89 -45 159h-3q-20 -86 -39 -149l-98 -302h280z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="642" 
d="M631 549h-93v-639h-115v639h-205v-639h-115v639h-93v101h621v-101z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="527" 
d="M528 -90h-516v71l233 296l-221 292v81h475v-101h-318v-4l197 -263l-208 -267v-4h358v-101z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="596" 
d="M40 306h516v-78h-516v78z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="124" 
d="M-90 -11h-71l374 672h71z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="236" 
d="M118 182h-1q-33 0 -54 22t-21 57q0 34 21.5 56.5t54.5 22.5q34 0 55 -22t21 -57t-21 -57t-55 -22z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="577" 
d="M597 834l-229 -920h-85l-158 418l-77 -31l-22 59l159 65l114 -319q10 -28 21 -74h2q12 60 16 76l173 726h86z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="741" 
d="M549 106h-1q-34 0 -69.5 18.5t-55 35t-53.5 50.5q-61 -59 -90 -76q-45 -28 -96 -28q-68 0 -110.5 44.5t-42.5 113.5q0 65 45.5 111.5t115.5 46.5q26 0 50.5 -8.5t49.5 -27t39 -31l40 -37.5q29 29 40.5 39t38 30t52 27.5t55.5 7.5q68 0 110.5 -44.5t42.5 -113.5
q0 -65 -45.5 -111.5t-115.5 -46.5zM548 183h1q33 0 56 24t23 57q0 35 -22 58t-55 23q-24 0 -49.5 -14.5t-40 -27.5t-39.5 -39q6 -5 21 -20t24 -23t23.5 -18t28.5 -15t29 -5zM190 183h1q23 0 49 15t38 26t40 39l-20 20l-24.5 24t-22.5 18t-29 15.5t-29 4.5q-33 0 -56.5 -24
t-23.5 -57q0 -35 22 -58t55 -23z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="339" 
d="M331 795l-13 -75q-20 4 -33 4q-31 0 -47 -25q-25 -35 -25 -152q0 -73 6.5 -223.5t6.5 -232.5q0 -127 -37 -186q-38 -64 -116 -64q-41 0 -63 11l15 77q18 -6 37 -6q27 0 43 22q28 35 28 143q0 83 -6.5 237t-6.5 230q0 145 57 205q38 42 102 42q33 0 52 -7z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="596" 
d="M525 427l48 -51q-26 -39 -68 -64t-89 -25q-20 0 -40.5 4.5t-29.5 7.5t-32 13.5t-24 10.5l-22 10.5t-27.5 12t-27 7.5t-34.5 4q-60 0 -115 -61l-43 53q27 36 69 61t90 25q16 0 30.5 -2t28.5 -7l22.5 -8t23 -10t19.5 -9q2 -1 27.5 -13t43.5 -16.5t40 -4.5q58 0 110 62z
M530 221l46 -49q-26 -40 -68.5 -65.5t-90.5 -25.5q-15 0 -29.5 2t-29 7t-22.5 8t-24 10.5t-19 8.5q-3 2 -22 10.5t-28 12t-27 7.5t-36 4q-58 0 -113 -61l-43 54q26 36 68.5 60.5t88.5 24.5q20 0 40.5 -4.5t30 -7.5t31.5 -13t24 -11l23 -10l26.5 -11.5t27.5 -8.5t33 -4
q64 0 113 62z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="596" 
d="M556 126h-309l-69 -110h-83l71 110h-126v78h176l82 128h-258v78h308l68 111h83l-71 -111h128v-78h-178l-81 -128h259v-78z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="596" 
d="M534 109l-472 184v77l472 183v-85l-364 -136v-2l364 -135v-86zM534 0h-471v74h471v-74z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="596" 
d="M534 293l-472 -184v86l364 135v2l-364 136v85l472 -183v-77zM533 0h-471v74h471v-74z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="550" 
d="M508 325l-187 -360h-92l-187 360l187 359h92zM407 322l-121 244q-8 16 -12 28h-2q-4 -12 -12 -28l-117 -239l120 -245q10 -17 12 -26h2q2 3 6.5 13t6.5 13z" />
    <glyph glyph-name="G_tildecomb" unicode="&#xe000;" horiz-adv-x="666" 
d="M611 372v-341q-102 -38 -217 -38q-170 0 -264 90q-96 91 -94 248q0 159 103.5 254.5t271.5 95.5q113 0 179 -32l-27 -99q-67 29 -153 29q-111 0 -178 -63.5t-67 -178.5q0 -113 64 -178.5t170 -65.5q65 0 93 14v168h-119v97h238zM305 715h-60q-1 55 18.5 85.5t54.5 30.5
q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="M_uni0302" unicode="&#xe001;" horiz-adv-x="827" 
d="M653 0l-13 275q-9 195 -8 284h-3q-34 -130 -80 -259l-98 -292h-95l-90 288q-41 136 -68 263h-2q-6 -177 -12 -288l-15 -271h-115l45 674h162l88 -271q35 -116 62 -234h3q31 125 67 235l93 270h160l39 -674h-120zM366 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="N_uni0302" unicode="&#xe002;" horiz-adv-x="676" 
d="M184 0h-113v674h140l174 -289q65 -109 115 -226h2q-11 124 -11 280v235h114v-674h-127l-176 297q-77 131 -121 232l-3 -1q6 -104 6 -287v-241zM291 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="g_tildecomb" unicode="&#xe005;" horiz-adv-x="573" 
d="M500 347v-276q0 -156 -72 -221q-67 -59 -188 -59q-107 0 -170 39l27 93q64 -37 142 -37q64 0 102 36t38 111v42h-2q-47 -72 -142 -72q-91 0 -149 67t-58 171q0 116 65 186.5t156 70.5q97 0 141 -76h2l5 65h107q-4 -66 -4 -140zM377 213v79q0 20 -4 33q-10 35 -36 57
t-64 22q-53 0 -86.5 -43t-33.5 -116q0 -65 31.5 -107.5t87.5 -42.5q34 0 61.5 20.5t38.5 53.5q5 22 5 44zM215 562h-62q-1 56 19.5 87.5t56.5 31.5q24 0 64 -21q32 -18 42 -18q12 0 18.5 9t8.5 34h59q4 -116 -75 -116q-26 0 -66 21q-29 17 -40 17q-23 0 -25 -45z" />
    <glyph glyph-name="m_uni0302" unicode="&#xe006;" horiz-adv-x="848" 
d="M382 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM66 0v342q0 89 -4 145h106l5 -72h3q52 83 150 83q47 0 83 -24.5t52 -65.5h2q25 40 60 61q41 29 98 29q69 0 116 -51.5t47 -159.5v-287h-120v269q0 129 -87 129q-30 0 -53 -18.5t-33 -46.5q-6 -24 -6 -41v-292h-120v282
q0 53 -21.5 84.5t-62.5 31.5q-32 0 -55.5 -21t-32.5 -49q-7 -17 -7 -40v-288h-120z" />
    <glyph glyph-name="n_uni0302" unicode="&#xe007;" horiz-adv-x="572" 
d="M244 698h84l97 -148h-84l-54 91h-2l-54 -91h-84zM66 0v342q0 89 -4 145h108l6 -73h3q18 33 58.5 58.5t96.5 25.5q72 0 122.5 -51t50.5 -158v-289h-123v275q0 56 -22.5 89.5t-70.5 33.5q-35 0 -60 -21t-36 -51q-5 -14 -5 -40v-286h-124z" />
    <glyph glyph-name="zero.slashfitted" unicode="&#xe008;" horiz-adv-x="536" 
d="M168 208l178 313q-26 46 -78 46q-53 0 -84.5 -64.5t-31.5 -175.5q0 -70 16 -119zM371 444l-179 -317q29 -44 75 -44q56 0 86.5 63.5t30.5 180.5q0 64 -13 117zM499 644l-52 -79q55 -85 55 -236q0 -159 -61 -249.5t-175 -90.5q-80 0 -135 49l-38 -61l-52 37l49 74
q-56 86 -56 235q-1 155 62 246.5t176 91.5q81 0 133 -47l41 66z" />
    <glyph glyph-name="dong.alt" unicode="&#xe184;" horiz-adv-x="536" 
d="M342 670h105v-62h63v-61h-63v-291q0 -63 3 -99h-94l-4 54h-2q-37 -62 -123 -62q-69 0 -115.5 49.5t-46.5 129.5q0 86 49.5 137t122.5 51q72 0 103 -45h2v76h-122v61h122v62zM342 314v43q0 17 -2 23q-5 25 -28 42t-52 17q-42 0 -65 -30t-23 -77q0 -46 23 -74.5t63 -28.5
q29 0 52 16.5t29 42.5q3 12 3 26z" />
    <glyph glyph-name="breve_acutecomb" unicode="&#xe300;" 
d="M189 793h103l-118 -140h-68zM8 665h63q4 -28 25.5 -46t53.5 -18q36 0 57 18.5t23 45.5h62q-2 -60 -41 -96.5t-101 -36.5q-66 0 -103 38t-39 95z" />
    <glyph glyph-name="breve_gravecomb" unicode="&#xe301;" 
d="M8 793h103l82 -140h-67zM6 665h62q5 -28 26 -46t53 -18q36 0 57 18.5t23 45.5h62q-2 -60 -40.5 -96.5t-101.5 -36.5q-66 0 -102.5 37.5t-38.5 95.5z" />
    <glyph glyph-name="breve_hookabovecomb" unicode="&#xe302;" 
d="M169 643l-55 13q7 19 27.5 42t20.5 39q0 13 -9 21.5t-22 8.5q-21 0 -44 -34l-39 24q35 69 105 69q36 0 61 -22t25 -53q0 -23 -11.5 -39.5t-30.5 -34t-28 -34.5zM8 665h63q4 -28 25.5 -46t53.5 -18q36 0 57 18.5t23 45.5h62q-2 -60 -41 -96.5t-101 -36.5q-66 0 -103 38
t-39 95z" />
    <glyph glyph-name="breve_tildecomb" unicode="&#xe303;" 
d="M80 705h-62q0 43 20 70t55 27q25 0 63 -20q30 -17 43 -17q12 0 18.5 9t8.5 32h59q3 -107 -76 -107q-20 0 -64 21q-30 16 -41 16q-20 0 -24 -31zM12 665h62q5 -29 25 -48t50 -19q34 0 54.5 20t22.5 47h62q-2 -60 -39 -96.5t-99 -36.5q-65 0 -100.5 38t-37.5 95z" />
    <glyph glyph-name="circumflex_acutecomb" unicode="&#xe304;" 
d="M106 698h88l87 -148h-83l-47 90h-2l-48 -90h-82zM321 787h101l-108 -141h-66z" />
    <glyph glyph-name="circumflex_gravecomb" unicode="&#xe305;" 
d="M190 788h99l66 -142h-65zM106 698h88l87 -148h-83l-47 90h-2l-48 -90h-82z" />
    <glyph glyph-name="circumflex_hookabovecomb" unicode="&#xe306;" 
d="M106 698h88l87 -148h-83l-47 90h-2l-48 -90h-82zM317 634l-55 13q7 19 27.5 42t20.5 39q0 13 -8.5 21.5t-21.5 8.5q-22 0 -45 -34l-39 24q35 69 105 69q36 0 61 -22.5t25 -52.5q0 -31 -31.5 -63t-38.5 -45z" />
    <glyph glyph-name="circumflex_tildecomb" unicode="&#xe307;" 
d="M106 675h88l93 -125h-86l-50 72h-2l-50 -72h-85zM81 689h-61q-1 48 19.5 76.5t55.5 28.5q26 0 64 -20q30 -17 42 -17q25 0 27 40h60q3 -106 -76 -106q-25 0 -65 21q-27 15 -41 15q-22 0 -25 -38z" />
    <glyph glyph-name="space_dotbelowcomb" unicode="&#xe308;" 
d="M151 -80q25 0 42.5 -18t17.5 -44t-17 -44t-44 -18q-26 0 -43.5 18t-17.5 44t18 44t44 18z" />
    <glyph glyph-name="space_hookabovecomb" unicode="&#xe309;" 
d="M166 528l-61 15q6 22 30.5 49.5t24.5 46.5q0 15 -9 24t-22 9q-25 0 -46 -34l-42 25q35 74 110 74q39 0 66 -23t27 -59q0 -27 -14 -47.5t-35 -41t-29 -38.5z" />
    <glyph glyph-name="space_uni031B" unicode="&#xe310;" 
d="M154 423v64h16q51 0 51 40q0 31 -20 62l89 15q21 -40 21 -83t-30.5 -70.5t-84.5 -27.5h-42z" />
    <glyph glyph-name="breve_acutecomb.cap" unicode="&#xe311;" 
d="M18 812h60q12 -47 75 -47q64 0 77 47h57q-6 -48 -41 -80.5t-95 -32.5t-93.5 31.5t-39.5 81.5zM180 912h116l-118 -110h-69z" />
    <glyph glyph-name="breve_gravecomb.cap" unicode="&#xe312;" 
d="M14 912h116l71 -110h-69zM19 812h59q14 -47 76 -47q63 0 76 47h58q-6 -48 -41 -80.5t-95 -32.5t-93.5 31.5t-39.5 81.5z" />
    <glyph glyph-name="breve_hookabovecomb.cap" unicode="&#xe313;" 
d="M172 789l-53 13q7 20 27.5 43t20.5 38q0 13 -8.5 22t-21.5 9q-23 0 -45 -35l-39 24q35 69 105 69q36 0 60.5 -22t24.5 -52q0 -24 -11.5 -40.5t-30.5 -34t-29 -34.5zM19 812h60q14 -47 75 -47q64 0 77 47h58q-6 -48 -41 -80.5t-95 -32.5t-94 31.5t-40 81.5z" />
    <glyph glyph-name="breve_tildecomb.cap" unicode="&#xe314;" 
d="M82 844h-63q1 44 21 70.5t55 26.5q25 0 63 -20q30 -17 43 -17t19 8.5t8 31.5h59q3 -106 -76 -106q-24 0 -64 21q-29 15 -41 15q-20 0 -24 -30zM16 812h60q12 -47 75 -47q64 0 77 47h57q-6 -48 -41 -80.5t-95 -32.5t-93.5 31.5t-39.5 81.5z" />
    <glyph glyph-name="circumflex_acutecomb.cap" unicode="&#xe315;" 
d="M110 829h87l92 -119h-85l-49 66h-2l-50 -66h-84zM320 908h111l-128 -110h-63z" />
    <glyph glyph-name="circumflex_gravecomb.cap" unicode="&#xe316;" 
d="M181 892h113l69 -109h-68zM106 829h87l92 -119h-85l-50 66h-2l-49 -66h-84z" />
    <glyph glyph-name="circumflex_hookabovecomb.cap" unicode="&#xe317;" 
d="M111 829h86l93 -119h-86l-49 66h-2l-49 -66h-84zM324 756l-52 13q7 20 29 43.5t22 39.5q0 13 -9 22t-23 9q-26 0 -47 -36l-39 24q35 69 104 69q37 0 61 -21.5t24 -53.5q0 -22 -11.5 -38t-30 -34t-28.5 -37z" />
    <glyph glyph-name="circumflex_tildecomb.cap" unicode="&#xe318;" 
d="M105 829h93l103 -119h-92l-56 65h-2l-57 -65h-90zM83 844h-61q-1 48 19.5 76t55.5 28q26 0 64 -20q30 -17 42 -17q24 0 27 41h59q4 -107 -75 -107q-21 0 -65 21q-29 16 -41 16q-22 0 -25 -38z" />
    <glyph glyph-name="space_dotbelowcomb.cap" unicode="&#xe319;" 
d="M151 -201h-1q-24 0 -42 18.5t-18 42.5q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5t-17 -43t-42 -18z" />
    <glyph glyph-name="space_hookabovecomb.cap" unicode="&#xe320;" 
d="M174 707l-59 15q7 20 28.5 43.5t21.5 40.5q0 14 -8.5 22.5t-22.5 8.5q-25 0 -47 -35l-41 26q37 73 111 73q38 0 64.5 -23.5t26.5 -55.5q0 -24 -12 -41.5t-32 -36.5t-30 -37z" />
    <glyph glyph-name="space_uni031B.cap" unicode="&#xe321;" 
d="M161 606v68h16q28 0 39.5 9t11.5 29q0 31 -19 63l89 15q20 -44 20 -84q0 -47 -30 -73.5t-89 -26.5h-38z" />
    <glyph glyph-name="dotaccent.cap" unicode="&#xefed;" 
d="M151 717h-1q-25 0 -42.5 18t-17.5 42q0 25 18 42.5t44 17.5q25 0 41.5 -17.5t16.5 -42.5q0 -24 -17 -42t-42 -18z" />
    <glyph glyph-name="breve.cap" unicode="&#xefee;" 
d="M17 841h68q8 -48 65 -48t67 48h66q-2 -53 -35 -85.5t-99 -32.5t-98 32t-34 86z" />
    <glyph glyph-name="ogonek.cap" unicode="&#xeff1;" 
d="M100 3h71q-38 -57 -38 -86q0 -23 14 -37t38 -14q25 0 45 9l18 -60q-35 -28 -92 -28q-48 0 -76 25t-28 73q0 59 48 118z" />
    <glyph glyph-name="cedilla.cap" unicode="&#xeff2;" 
d="M129 3h69l-27 -50q31 -7 51.5 -27t20.5 -51q0 -45 -32.5 -66.5t-79.5 -21.5t-79 19l17 54q26 -14 56 -14q39 0 39 27q0 32 -79 40z" />
    <glyph glyph-name="ring.cap" unicode="&#xeff3;" horiz-adv-x="400" 
d="M201 886h1q50 0 78.5 -27t28.5 -68q0 -40 -29.5 -66.5t-78.5 -26.5t-78.5 26.5t-29.5 65.5q0 41 29 68.5t79 27.5zM200 842h-1q-21 0 -33 -15.5t-12 -36.5q0 -19 13 -33.5t33 -14.5q21 0 33.5 14t12.5 36q0 21 -12.5 35.5t-33.5 14.5z" />
    <glyph glyph-name="tilde.cap" unicode="&#xeff5;" 
d="M77 715h-60q-1 55 18.5 85.5t54.5 30.5q26 0 62 -20q30 -19 46 -19q12 0 18.5 9t8.5 32h58q3 -112 -73 -112q-29 0 -64 19q-32 18 -44 18q-22 0 -25 -43z" />
    <glyph glyph-name="circumflex.cap" unicode="&#xeff7;" 
d="M103 829h93l103 -119h-92l-57 65h-2l-56 -65h-90z" />
    <glyph glyph-name="cent.denominator" unicode="&#xf629;" horiz-adv-x="323" 
d="M218 -6h-57v58q-57 8 -91 46q-34 37 -34 98q0 57 33 98.5t92 52.5v56h57v-54q31 0 60 -12l-14 -65q-26 12 -56 12q-38 0 -60.5 -23t-22.5 -60q0 -41 23.5 -62.5t59.5 -21.5q34 0 62 11l12 -62q-29 -14 -64 -15v-57z" />
    <glyph glyph-name="dollar.denominator" unicode="&#xf62a;" horiz-adv-x="336" 
d="M194 -51h-55v57q-65 2 -106 27l18 65q49 -27 103 -27q29 0 45.5 11t16.5 30q0 18 -14.5 29t-52.5 24q-57 20 -84.5 44t-27.5 65q0 40 27.5 68.5t76.5 37.5v56h56v-53q50 -1 89 -20l-18 -64q-47 22 -89 22q-55 0 -55 -37q0 -16 15.5 -27t60.5 -26q55 -18 79 -44.5
t24 -67.5q0 -40 -28.5 -70.5t-80.5 -38.5v-61z" />
    <glyph glyph-name="hyphen.denominator" unicode="&#xf62b;" horiz-adv-x="199" 
d="M18 215h163v-60h-163v60z" />
    <glyph glyph-name="parenleft.denominator" unicode="&#xf62c;" horiz-adv-x="193" 
d="M113 425h65q-65 -104 -65 -240q0 -133 65 -240h-65q-72 100 -72 240q0 139 72 240z" />
    <glyph glyph-name="parenright.denominator" unicode="&#xf62d;" horiz-adv-x="193" 
d="M80 -55h-64q65 112 65 240q0 135 -65 240h64q73 -102 73 -240q0 -134 -73 -240z" />
    <glyph glyph-name="cent.numerator" unicode="&#xf62e;" horiz-adv-x="323" 
d="M218 260h-57v58q-57 8 -91 46q-34 37 -34 98q0 57 33 98.5t92 52.5v56h57v-54q31 0 60 -12l-14 -65q-26 12 -56 12q-38 0 -60.5 -23t-22.5 -60q0 -41 23.5 -62.5t59.5 -21.5q34 0 62 11l12 -62q-29 -14 -64 -15v-57z" />
    <glyph glyph-name="dollar.numerator" unicode="&#xf62f;" horiz-adv-x="336" 
d="M194 215h-55v57q-65 2 -106 27l18 65q49 -27 103 -27q29 0 45.5 11t16.5 30q0 18 -14.5 29t-52.5 24q-57 20 -84.5 44t-27.5 65q0 40 27.5 68.5t76.5 37.5v56h56v-53q50 -1 89 -20l-18 -64q-47 22 -89 22q-55 0 -55 -37q0 -16 15.5 -27t60.5 -26q55 -18 79 -44.5
t24 -67.5q0 -40 -28.5 -70.5t-80.5 -38.5v-61z" />
    <glyph glyph-name="hyphen.numerator" unicode="&#xf630;" horiz-adv-x="199" 
d="M18 481h163v-60h-163v60z" />
    <glyph glyph-name="parenleft.numerator" unicode="&#xf631;" horiz-adv-x="193" 
d="M113 691h65q-65 -104 -65 -240q0 -133 65 -240h-65q-72 100 -72 240q0 139 72 240z" />
    <glyph glyph-name="parenright.numerator" unicode="&#xf632;" horiz-adv-x="193" 
d="M80 211h-64q65 112 65 240q0 135 -65 240h64q73 -102 73 -240q0 -134 -73 -240z" />
    <glyph glyph-name="at.cap" unicode="&#xf633;" horiz-adv-x="755" 
d="M445 345l18 103q-20 6 -40 6q-56 0 -96 -47.5t-40 -111.5q0 -32 14.5 -50.5t41.5 -18.5q34 0 64 36.5t38 82.5zM519 77l17 -51q-79 -38 -181 -38q-128 0 -218 86.5t-90 228.5q0 158 103 270t267 112q131 0 214 -81.5t83 -207.5q0 -108 -53 -173t-131 -65
q-35 0 -57.5 20.5t-23.5 61.5h-3q-49 -82 -134 -82q-48 0 -79.5 34.5t-31.5 93.5q0 96 66.5 165.5t169.5 69.5q68 0 114 -23l-32 -183q-18 -98 27 -98q39 -2 70.5 45.5t31.5 128.5q0 107 -63.5 173.5t-176.5 66.5q-122 0 -207.5 -88.5t-85.5 -232.5q0 -123 71.5 -194.5
t184.5 -71.5q85 0 148 33z" />
    <glyph glyph-name="space_uni0326.cap" unicode="&#xf634;" 
d="M86 -232l-19 53q29 6 49 22t20 41q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -31z" />
    <glyph glyph-name="zero.slash" unicode="&#xf638;" horiz-adv-x="536" 
d="M168 208l178 313q-26 46 -78 46q-53 0 -84.5 -64.5t-31.5 -175.5q0 -70 16 -119zM371 444l-179 -317q29 -44 75 -44q56 0 86.5 63.5t30.5 180.5q0 64 -13 117zM499 644l-52 -79q55 -85 55 -236q0 -159 -61 -249.5t-175 -90.5q-80 0 -135 49l-38 -61l-52 37l49 74
q-56 86 -56 235q-1 155 62 246.5t176 91.5q81 0 133 -47l41 66z" />
    <glyph glyph-name="zero.fitted" unicode="&#xf639;" horiz-adv-x="536" 
d="M272 661q114 0 172 -90.5t58 -241.5q0 -159 -61 -249.5t-176 -90.5q-110 0 -170 91t-61 243q0 154 63 246t175 92zM269 567q-51 0 -81.5 -62.5t-29.5 -181.5q-1 -116 29 -178t82 -62q54 0 82 63t28 181q0 240 -110 240z" />
    <glyph glyph-name="two.fitted" unicode="&#xf63a;" horiz-adv-x="472" 
d="M444 0h-441v75l77 71q125 116 175 180.5t51 125.5q0 48 -28 78.5t-88 30.5q-68 0 -139 -54l-36 87q83 67 200 67q103 0 158.5 -56t55.5 -141q0 -79 -48.5 -149.5t-146.5 -161.5l-57 -49v-2h267v-102z" />
    <glyph glyph-name="three.fitted" unicode="&#xf63b;" horiz-adv-x="476" 
d="M7 34l28 93q67 -40 148 -40q66 0 99 30.5t32 73.5q0 54 -42.5 82t-104.5 28h-57v90h55q50 0 89.5 23t39.5 68q0 35 -26.5 58t-77.5 23q-72 0 -133 -41l-28 89q30 21 80.5 35.5t106.5 14.5q96 0 149.5 -46t53.5 -113q0 -52 -30.5 -91.5t-89.5 -60.5v-2q62 -11 102.5 -54.5
t40.5 -107.5q0 -85 -68 -141t-185 -56q-114 0 -182 45z" />
    <glyph glyph-name="four.fitted" unicode="&#xf63c;" horiz-adv-x="527" 
d="M425 0h-116v165h-294v80l265 405h145v-392h84v-93h-84v-165zM132 258h177v177q0 62 4 113h-4q-30 -61 -57 -110l-119 -178z" />
    <glyph glyph-name="five.fitted" unicode="&#xf63d;" horiz-adv-x="485" 
d="M427 650v-102h-250l-19 -130q23 3 47 3q94 0 158 -42q85 -54 85 -165q0 -95 -73 -160t-190 -65q-104 0 -172 38l25 93q63 -34 143 -34q58 0 99.5 31.5t41.5 87.5q0 58 -45.5 90.5t-139.5 32.5q-50 0 -89 -6l43 328h336z" />
    <glyph glyph-name="six.fitted" unicode="&#xf63e;" horiz-adv-x="537" 
d="M439 659l1 -96q-38 0 -63 -3q-96 -11 -150.5 -63.5t-67.5 -126.5h3q54 63 146 63q86 0 142.5 -57.5t56.5 -154.5q0 -96 -64 -164t-165 -68q-115 0 -180 78t-65 202q0 184 113 292q90 84 231 96q34 4 62 2zM277 81h1q47 0 75.5 37t28.5 96q0 58 -30 93.5t-83 35.5
q-34 0 -62.5 -18.5t-43.5 -48.5q-8 -16 -8 -39q2 -68 33.5 -112t88.5 -44z" />
    <glyph glyph-name="seven.fitted" unicode="&#xf63f;" horiz-adv-x="440" 
d="M3 650h437v-79l-275 -571h-130l274 546v2h-306v102z" />
    <glyph glyph-name="eight.fitted" unicode="&#xf640;" horiz-adv-x="536" 
d="M156 336v3q-99 48 -99 144q0 79 62 128.5t155 49.5q98 0 151 -49t53 -115q0 -44 -24.5 -83t-75.5 -64v-3q56 -21 90 -63.5t34 -100.5q0 -87 -66 -140.5t-171 -53.5q-108 0 -169.5 52.5t-61.5 125.5q0 115 122 169zM269 76q48 0 78 27.5t30 69.5q0 88 -121 122
q-46 -13 -71 -43t-25 -71q-2 -43 28.5 -74t80.5 -31zM268 576q-44 0 -69 -25.5t-25 -62.5q0 -73 106 -102q35 10 58 36.5t23 61.5q0 38 -23.5 65t-69.5 27z" />
    <glyph glyph-name="nine.fitted" unicode="&#xf641;" horiz-adv-x="539" 
d="M93 -9v97q23 -2 68 2q78 6 132 50q65 53 83 144l-2 1q-52 -58 -141 -58q-86 0 -142 56.5t-56 145.5q0 95 66.5 163.5t168.5 68.5q110 0 171 -77t61 -200q0 -194 -113 -302q-86 -80 -222 -89q-38 -4 -74 -2zM264 570h-1q-46 0 -76 -37.5t-30 -96.5q1 -52 29.5 -85.5
t77.5 -33.5q72 0 105 54q7 14 7 35q1 71 -27 117.5t-85 46.5z" />
    <glyph glyph-name="percent.oldstyle" unicode="&#xf642;" horiz-adv-x="858" 
d="M194 610h1q75 0 119 -52.5t44 -140.5q0 -98 -49 -152.5t-120 -54.5t-118 52.5t-48 144.5q0 91 48 147t123 56zM191 541h-1q-35 0 -54 -36.5t-19 -94.5q-1 -58 19 -94t56 -36t54.5 35t18.5 96q0 130 -74 130zM289 -12h-70l350 625h70zM670 392h1q76 0 120 -53t44 -141
q0 -97 -49 -151.5t-121 -54.5q-70 0 -117.5 52.5t-47.5 143.5t47.5 147.5t122.5 56.5zM668 322h-1q-35 0 -54 -36.5t-20 -94.5q0 -57 20 -93.5t55 -36.5q73 0 73 131q0 130 -73 130z" />
    <glyph glyph-name="zero.taboldstyle" unicode="&#xf643;" horiz-adv-x="547" 
d="M274 -11h-1q-107 0 -172 71t-65 184q0 112 65.5 183.5t172.5 71.5t172.5 -71t65.5 -184q0 -112 -65.5 -183.5t-172.5 -71.5zM274 80h1q55 0 87 45t32 119t-32 119t-88 45t-88.5 -45t-32.5 -119t32.5 -119t88.5 -45z" />
    <glyph glyph-name="one.taboldstyle" unicode="&#xf644;" horiz-adv-x="547" 
d="M392 0h-119v384h-2l-179 -75l-18 91l217 87h101v-487z" />
    <glyph glyph-name="two.taboldstyle" unicode="&#xf645;" horiz-adv-x="547" 
d="M477 0h-414v67l55 37q107 68 154 116.5t47 98.5q0 41 -26 62.5t-71 21.5q-62 0 -126 -41l-29 79q82 60 190 60q85 0 135.5 -43t50.5 -118q0 -62 -41 -113t-116 -103l-46 -32v-2q45 6 95 6h142v-96z" />
    <glyph glyph-name="three.taboldstyle" unicode="&#xf646;" horiz-adv-x="547" 
d="M60 -99l14 90q49 -17 113 -17q66 0 107 27t41 74q0 40 -37 63.5t-100 23.5q-26 0 -54 -2l-1 87l23 2q66 5 107 27.5t41 59.5q0 33 -25 50.5t-69 17.5q-59 0 -125 -34l-23 81q30 20 78.5 34t97.5 14q84 0 136 -38.5t52 -102.5q0 -50 -33.5 -87.5t-85.5 -51.5v-2
q63 -10 104 -47t41 -96q0 -87 -77 -141t-185 -54q-88 0 -140 22z" />
    <glyph glyph-name="four.taboldstyle" unicode="&#xf647;" horiz-adv-x="547" 
d="M516 40h-84v-161h-114v161h-288v79l257 373h145v-361h84v-91zM318 131v161q0 70 3 104l-5 1q-33 -68 -58 -106l-113 -157l2 -3h171z" />
    <glyph glyph-name="five.taboldstyle" unicode="&#xf648;" horiz-adv-x="547" 
d="M73 -102l14 87q41 -13 104 -13q62 0 104 29t42 79q0 36 -25 61q-47 47 -220 40l53 306h309l-14 -99h-214l-23 -120q148 0 214 -63q46 -46 46 -116q0 -93 -71 -151.5t-192 -58.5q-78 0 -127 19z" />
    <glyph glyph-name="six.taboldstyle" unicode="&#xf649;" horiz-adv-x="547" 
d="M457 611l8 -88q-126 -6 -197 -56t-87 -124l2 -1q49 59 133 59q82 0 136.5 -52t54.5 -140q0 -97 -66.5 -158.5t-158.5 -61.5q-107 0 -170.5 69.5t-63.5 183.5q0 160 109.5 262t299.5 107zM283 81h1q40 0 70.5 31.5t30.5 89.5q0 53 -28.5 85t-74.5 32q-55 0 -89 -40
q-23 -24 -23 -65q0 -58 30.5 -95.5t82.5 -37.5z" />
    <glyph glyph-name="seven.taboldstyle" unicode="&#xf64a;" horiz-adv-x="547" 
d="M475 487v-75q-44 -115 -121.5 -263.5t-154.5 -267.5l-118 16q68 99 148.5 244t118.5 243v2h-285v101h412z" />
    <glyph glyph-name="eight.taboldstyle" unicode="&#xf64b;" horiz-adv-x="547" 
d="M381 325v-4q55 -21 88.5 -59t33.5 -90q0 -82 -66.5 -132.5t-168.5 -50.5q-101 0 -164 47.5t-63 122.5q0 102 118 154v4q-95 42 -95 127q0 74 60 121.5t154 47.5q93 0 147 -45t54 -110q0 -86 -98 -133zM277 534h-1q-40 0 -66 -23t-26 -59q0 -66 105 -95q34 11 56 35.5
t22 57.5q0 39 -26.5 61.5t-63.5 22.5zM270 74h1q45 0 75 25t30 64q0 40 -32 69.5t-89 44.5q-42 -14 -66.5 -42.5t-24.5 -64.5q0 -42 29.5 -69t76.5 -27z" />
    <glyph glyph-name="nine.taboldstyle" unicode="&#xf64c;" horiz-adv-x="547" 
d="M87 -118l-8 89q241 9 288 175l-2 1q-17 -23 -53.5 -40t-79.5 -17q-81 0 -135 53t-54 139q0 95 66.5 157.5t164.5 62.5q101 0 164 -68.5t63 -180.5q0 -118 -56 -202.5t-148 -125.5t-210 -43zM269 174h1q29 0 56 15t40 38q13 18 13 54q0 56 -31.5 92t-77.5 36
q-42 0 -73.5 -31.5t-31.5 -87.5q0 -55 29.5 -85.5t74.5 -30.5z" />
    <glyph glyph-name="Euro.taboldstyle" unicode="&#xf64e;" horiz-adv-x="547" 
d="M483 105l14 -83q-56 -33 -143 -33q-116 0 -186 72q-46 46 -63 117h-65v59h56q0 6 -0.5 13.5t-0.5 9.5q0 18 2 29h-56v59h65q16 66 64 114q77 77 197 77q72 0 128 -27l-25 -84q-42 21 -97 21q-69 0 -112 -43q-26 -24 -33 -58h226v-59h-240q-1 -10 -1 -25q0 -16 1 -27h240
v-59h-226q9 -33 30 -54q46 -43 116 -43q63 0 109 24z" />
    <glyph glyph-name="florin.taboldstyle" unicode="&#xf64f;" horiz-adv-x="547" 
d="M124 270v75h94l6 52q3 28 9.5 54t21.5 56.5t36.5 53t57 37.5t80.5 15q44 0 76 -16l-21 -88q-23 9 -49 9q-78 0 -92 -112l-7 -61h112v-75h-121l-22 -174q-13 -106 -54.5 -162.5t-132.5 -55.5q-53 0 -85 16l19 87q23 -10 51 -10q41 1 59.5 33.5t28.5 115.5l17 150h-84z" />
    <glyph glyph-name="numbersign.taboldstyle" unicode="&#xf650;" horiz-adv-x="547" 
d="M219 235h97l17 127h-97zM185 -6h-77l23 164h-82v77h94l18 127h-86v77h97l22 159h75l-21 -159h97l22 159h76l-22 -159h81v-77h-93l-17 -127h84v-77h-97l-22 -164h-76l22 164h-96z" />
    <glyph glyph-name="sterling.taboldstyle" unicode="&#xf651;" horiz-adv-x="547" 
d="M499 0h-438v66q49 20 77.5 53t28.5 72q0 19 -3 35h-94v71h79q-6 36 -6 54q0 88 57.5 145t153.5 57q70 0 114 -25l-22 -90q-35 19 -87 19q-49 0 -75 -29.5t-26 -79.5q0 -19 6 -51h146v-71h-133q1 -7 1 -23q-4 -67 -58 -104v-2h279v-97z" />
    <glyph glyph-name="yen.taboldstyle" unicode="&#xf652;" horiz-adv-x="547" 
d="M329 0h-116v109h-151v62h151v55h-151v61h121l-148 241h129l70 -138q29 -63 40 -91h2l43 92l73 137h125l-157 -241h121v-61h-152v-55h152v-62h-152v-109z" />
    <glyph glyph-name="dollar.taboldstyle" unicode="&#xf653;" horiz-adv-x="547" 
d="M311 -85h-74v83q-95 3 -156 42l28 83q24 -15 64.5 -26.5t76.5 -11.5q40 0 65 14.5t25 39.5q0 23 -20 38t-72 32q-83 27 -119 59.5t-36 83.5t40 86.5t109 46.5v89h75v-85q76 -4 130 -33l-27 -80q-55 31 -120 31q-36 0 -60 -13.5t-24 -34.5q0 -22 19.5 -36t79.5 -33
q81 -25 117 -60t36 -85q0 -52 -41 -92t-116 -52v-86z" />
    <glyph glyph-name="cent.taboldstyle" unicode="&#xf654;" horiz-adv-x="547" 
d="M417 143l15 -80q-41 -20 -97 -21v-73h-72v74q-83 11 -133 64.5t-50 137.5q0 80 49.5 137.5t133.5 71.5v71h72v-68q50 0 94 -18l-19 -80q-34 16 -88 16q-57 0 -92 -34t-35 -91q0 -60 35.5 -92.5t90.5 -32.5q56 0 96 18z" />
    <glyph glyph-name="zero.denominator" unicode="&#xf655;" horiz-adv-x="359" 
d="M178 -5h-1q-74 0 -114.5 55t-40.5 145t41 145t117 55t116.5 -54.5t40.5 -144.5q0 -89 -40 -145t-119 -56zM179 61h1q64 0 64 135q0 133 -65 133q-30 0 -47 -34.5t-17 -98.5q0 -66 17 -100.5t47 -34.5z" />
    <glyph glyph-name="one.denominator" unicode="&#xf656;" horiz-adv-x="275" 
d="M209 0h-96v310h-2l-76 -35l-14 69l104 46h84v-390z" />
    <glyph glyph-name="two.denominator" unicode="&#xf657;" horiz-adv-x="333" 
d="M14 0v48l62 56q68 62 94 94t26 64q0 26 -17 42t-49 16q-45 0 -85 -30l-26 63q55 42 135 42q69 0 106 -34t37 -83q0 -34 -19 -68t-40 -55t-63 -57l-28 -23v-3h157v-72h-290z" />
    <glyph glyph-name="three.denominator" unicode="&#xf658;" horiz-adv-x="328" 
d="M46 302l-20 62q52 31 129 31q66 0 100.5 -27.5t34.5 -69.5q0 -30 -22 -53.5t-58 -35.5v-2q41 -5 68 -31t27 -63q0 -51 -46 -84.5t-124 -33.5q-81 0 -126 28l21 65q43 -26 95 -26q37 0 58 16.5t21 39.5q0 29 -28 44t-67 15h-29v58h28q32 0 57.5 12.5t25.5 38.5
q0 18 -14.5 30t-42.5 12q-45 0 -88 -26z" />
    <glyph glyph-name="four.denominator" unicode="&#xf659;" horiz-adv-x="372" 
d="M299 0h-90v96h-191v53l171 242h110v-232h53v-63h-53v-96zM209 159v92q0 25 4 69l-3 1q-17 -36 -35 -64l-68 -97l2 -1h100z" />
    <glyph glyph-name="five.denominator" unicode="&#xf65a;" horiz-adv-x="330" 
d="M285 389v-72h-158l-10 -65q7 1 28 1q67 0 104 -25q51 -32 51 -98q0 -59 -46 -97.5t-122 -38.5q-66 0 -112 23l16 65q45 -20 92 -20q32 0 57 16.5t25 45.5q0 68 -113 68q-13 0 -34.5 -2t-22.5 -2l26 201h219z" />
    <glyph glyph-name="six.denominator" unicode="&#xf65b;" horiz-adv-x="349" 
d="M282 394v-68q-23 2 -38 0q-56 -5 -89.5 -31.5t-42.5 -66.5h2q33 33 88 33q56 0 90.5 -34t34.5 -91q0 -61 -43 -101t-104 -40q-76 0 -117 48.5t-41 120.5q0 109 69 171q55 52 152 58q30 2 39 1zM179 61h1q25 0 41.5 20t16.5 50q0 31 -17 50.5t-48 19.5q-42 0 -60 -39
q-4 -10 -4 -20q0 -34 18.5 -57.5t51.5 -23.5z" />
    <glyph glyph-name="seven.denominator" unicode="&#xf65c;" horiz-adv-x="316" 
d="M22 389h280v-55l-168 -334h-94l168 316v1h-186v72z" />
    <glyph glyph-name="eight.denominator" unicode="&#xf65d;" horiz-adv-x="346" 
d="M175 395h1q63 0 96.5 -29.5t33.5 -69.5q0 -59 -60 -86v-2q33 -12 55 -36.5t22 -59.5q0 -52 -42 -84.5t-111 -32.5q-71 0 -109 32.5t-38 74.5q0 65 75 97v2q-29 13 -45 36t-16 49q0 49 39.5 79t98.5 30zM173 56h1q26 0 42.5 14t16.5 37q0 46 -67 65q-54 -17 -54 -61
q0 -23 16.5 -39t44.5 -16zM173 336h-1q-25 0 -39 -13.5t-14 -33.5q0 -38 61 -56q20 6 32.5 20t12.5 33q0 20 -13.5 35t-38.5 15z" />
    <glyph glyph-name="nine.denominator" unicode="&#xf65e;" horiz-adv-x="344" 
d="M58 -4v68q15 -3 40 -1q51 5 83 26q40 28 51 76l-2 1q-29 -31 -83 -31q-53 0 -89 33t-36 88q0 58 42.5 98.5t106.5 40.5q73 0 112 -47.5t39 -119.5q0 -116 -68 -177q-55 -49 -154 -54q-8 -1 -42 -1zM170 330h-1q-25 0 -42 -20t-17 -50q0 -27 16.5 -46t44.5 -19q40 0 58 29
q5 8 5 21q0 37 -16 61t-48 24z" />
    <glyph glyph-name="comma.denominator" unicode="&#xf65f;" horiz-adv-x="153" 
d="M59 -64l-63 -5q30 85 44 171l94 6q-31 -100 -75 -172z" />
    <glyph glyph-name="period.denominator" unicode="&#xf660;" horiz-adv-x="153" 
d="M76 -6h-1q-23 0 -38 16t-15 40q0 25 15 40t40 15t40 -15t15 -40q0 -24 -15.5 -40t-40.5 -16z" />
    <glyph glyph-name="zero.numerator" unicode="&#xf661;" horiz-adv-x="359" 
d="M178 261h-1q-74 0 -114.5 55t-40.5 145t41 145t117 55t116.5 -54.5t40.5 -144.5q0 -89 -40 -145t-119 -56zM179 327h1q64 0 64 135q0 133 -65 133q-30 0 -47 -34.5t-17 -98.5q0 -66 17 -100.5t47 -34.5z" />
    <glyph glyph-name="one.numerator" unicode="&#xf662;" horiz-adv-x="275" 
d="M209 266h-96v310h-2l-76 -35l-14 69l104 46h84v-390z" />
    <glyph glyph-name="two.numerator" unicode="&#xf663;" horiz-adv-x="333" 
d="M14 266v48l62 56q68 62 94 94t26 64q0 26 -17 42t-49 16q-45 0 -85 -30l-26 63q55 42 135 42q69 0 106 -34t37 -83q0 -34 -19 -68t-40 -55t-63 -57l-28 -23v-3h157v-72h-290z" />
    <glyph glyph-name="three.numerator" unicode="&#xf664;" horiz-adv-x="328" 
d="M46 568l-20 62q52 31 129 31q66 0 100.5 -27.5t34.5 -69.5q0 -30 -22 -53.5t-58 -35.5v-2q41 -5 68 -31t27 -63q0 -51 -46 -84.5t-124 -33.5q-81 0 -126 28l21 65q43 -26 95 -26q37 0 58 16.5t21 39.5q0 29 -28 44t-67 15h-29v58h28q32 0 57.5 12.5t25.5 38.5
q0 18 -14.5 30t-42.5 12q-45 0 -88 -26z" />
    <glyph glyph-name="four.numerator" unicode="&#xf665;" horiz-adv-x="372" 
d="M299 266h-90v96h-191v53l171 242h110v-232h53v-63h-53v-96zM209 425v92q0 25 4 69l-3 1q-17 -36 -35 -64l-68 -97l2 -1h100z" />
    <glyph glyph-name="five.numerator" unicode="&#xf666;" horiz-adv-x="330" 
d="M285 655v-72h-158l-10 -65q7 1 28 1q67 0 104 -25q51 -32 51 -98q0 -59 -46 -97.5t-122 -38.5q-66 0 -112 23l16 65q45 -20 92 -20q32 0 57 16.5t25 45.5q0 68 -113 68q-13 0 -34.5 -2t-22.5 -2l26 201h219z" />
    <glyph glyph-name="six.numerator" unicode="&#xf667;" horiz-adv-x="349" 
d="M282 660v-68q-23 2 -38 0q-56 -5 -89.5 -31.5t-42.5 -66.5h2q33 33 88 33q56 0 90.5 -34t34.5 -91q0 -61 -43 -101t-104 -40q-76 0 -117 48.5t-41 120.5q0 109 69 171q55 52 152 58q30 2 39 1zM179 327h1q25 0 41.5 20t16.5 50q0 31 -17 50.5t-48 19.5q-42 0 -60 -39
q-4 -10 -4 -20q0 -34 18.5 -57.5t51.5 -23.5z" />
    <glyph glyph-name="seven.numerator" unicode="&#xf668;" horiz-adv-x="316" 
d="M22 655h280v-55l-168 -334h-94l168 316v1h-186v72z" />
    <glyph glyph-name="eight.numerator" unicode="&#xf669;" horiz-adv-x="346" 
d="M175 661h1q63 0 96.5 -29.5t33.5 -69.5q0 -59 -60 -86v-2q33 -12 55 -36.5t22 -59.5q0 -52 -42 -84.5t-111 -32.5q-71 0 -109 32.5t-38 74.5q0 65 75 97v2q-29 13 -45 36t-16 49q0 49 39.5 79t98.5 30zM173 322h1q26 0 42.5 14t16.5 37q0 46 -67 65q-54 -17 -54 -61
q0 -23 16.5 -39t44.5 -16zM173 602h-1q-25 0 -39 -13.5t-14 -33.5q0 -38 61 -56q20 6 32.5 20t12.5 33q0 20 -13.5 35t-38.5 15z" />
    <glyph glyph-name="nine.numerator" unicode="&#xf66a;" horiz-adv-x="344" 
d="M58 262v68q15 -3 40 -1q51 5 83 26q40 28 51 76l-2 1q-29 -31 -83 -31q-53 0 -89 33t-36 88q0 58 42.5 98.5t106.5 40.5q73 0 112 -47.5t39 -119.5q0 -116 -68 -177q-55 -49 -154 -54q-8 -1 -42 -1zM170 596h-1q-25 0 -42 -20t-17 -50q0 -27 16.5 -46t44.5 -19
q40 0 58 29q5 8 5 21q0 37 -16 61t-48 24z" />
    <glyph glyph-name="comma.numerator" unicode="&#xf66b;" horiz-adv-x="153" 
d="M59 202l-63 -5q30 85 44 171l94 6q-31 -100 -75 -172z" />
    <glyph glyph-name="period.numerator" unicode="&#xf66c;" horiz-adv-x="153" 
d="M76 260h-1q-23 0 -38 16t-15 40q0 25 15 40t40 15t40 -15t15 -40q0 -24 -15.5 -40t-40.5 -16z" />
    <glyph glyph-name="parenleft.cap" unicode="&#xf6ae;" horiz-adv-x="301" 
d="M183 732h90q-113 -168 -113 -407q0 -232 113 -404h-90q-121 170 -121 404q1 237 121 407z" />
    <glyph glyph-name="parenright.cap" unicode="&#xf6af;" horiz-adv-x="301" 
d="M117 -79h-89q112 169 112 406q0 234 -112 405h89q122 -168 122 -406q-1 -236 -122 -405z" />
    <glyph glyph-name="bracketleft.cap" unicode="&#xf6b0;" horiz-adv-x="301" 
d="M269 -73h-195v798h195v-71h-104v-656h104v-71z" />
    <glyph glyph-name="bracketright.cap" unicode="&#xf6b1;" horiz-adv-x="301" 
d="M32 725h194v-798h-194v71h103v656h-103v71z" />
    <glyph glyph-name="braceleft.cap" unicode="&#xf6b2;" horiz-adv-x="301" 
d="M29 294v65q47 1 62.5 20.5t15.5 50.5q0 25 -6 81q-7 47 -7 81q0 67 39.5 100t109.5 33h29v-71h-18q-70 -1 -70 -78q0 -26 7 -64q6 -66 6 -72q1 -93 -80 -112v-2q81 -19 80 -113q0 -7 -6 -73q-7 -39 -7 -63q0 -78 70 -79h18v-71h-29q-149 0 -149 137q0 33 7 80q6 54 6 79
q0 70 -78 71z" />
    <glyph glyph-name="braceright.cap" unicode="&#xf6b3;" horiz-adv-x="301" 
d="M271 359v-65q-78 -1 -78 -71q0 -16 7 -79t7 -80q0 -137 -149 -137h-29v71h18q69 1 69 79q0 19 -6 63t-6 73q-1 94 79 113v2q-80 19 -79 112q0 28 6 72t6 64q0 77 -69 78h-18v71h29q70 0 109.5 -33t39.5 -100q0 -18 -7 -81q-7 -65 -7 -81q0 -31 16 -50.5t62 -20.5z" />
    <glyph glyph-name="exclamdown.cap" unicode="&#xf6b4;" horiz-adv-x="251" 
d="M191 0h-130l18 463h94zM126 683h1q33 0 53.5 -21.5t20.5 -56.5q0 -34 -20.5 -56t-54.5 -22q-33 0 -54.5 22.5t-21.5 55.5q0 35 21 56.5t55 21.5z" />
    <glyph glyph-name="questiondown.cap" unicode="&#xf6b5;" horiz-adv-x="428" 
d="M243 683h1q33 0 54 -22t21 -56t-21 -56t-55 -22t-55 22t-21 56q0 35 21 56.5t55 21.5zM187 466h108l2 -26q6 -82 -61 -164q-63 -75 -63 -119q0 -33 21 -52t62 -20q58 0 100 31l29 -89q-64 -40 -156 -40q-88 0 -134.5 43.5t-46.5 108.5q0 34 14 67t29 52.5t43 51.5
q56 64 53 135v21z" />
    <glyph glyph-name="guillemotleft.cap" unicode="&#xf6b6;" horiz-adv-x="444" 
d="M241 515l-126 -188l126 -188h-90l-124 188l123 188h91zM421 515l-125 -188l125 -188h-90l-123 188l123 188h90z" />
    <glyph glyph-name="guillemotright.cap" unicode="&#xf6b7;" horiz-adv-x="444" 
d="M149 327l-126 188h89l124 -188l-124 -188h-89zM328 327l-126 188h89l124 -188l-124 -188h-89z" />
    <glyph glyph-name="guilsinglleft.cap" unicode="&#xf6b8;" horiz-adv-x="264" 
d="M241 515l-126 -188l126 -188h-90l-124 188l123 188h91z" />
    <glyph glyph-name="guilsinglright.cap" unicode="&#xf6b9;" horiz-adv-x="264" 
d="M148 327l-125 188h89l124 -188l-124 -188h-89z" />
    <glyph glyph-name="hyphen.cap" unicode="&#xf6ba;" horiz-adv-x="315" 
d="M30 384h255v-85h-255v85z" />
    <glyph glyph-name="endash.cap" unicode="&#xf6bb;" horiz-adv-x="500" 
d="M30 368h440v-78h-440v78z" />
    <glyph glyph-name="emdash.cap" unicode="&#xf6bc;" horiz-adv-x="1000" 
d="M30 368h940v-78h-940v78z" />
    <glyph glyph-name="periodcentered.cap" unicode="&#xf6bd;" horiz-adv-x="236" 
d="M118 257h-1q-33 0 -54 22t-21 57q0 34 21.5 56.5t54.5 22.5q34 0 55 -22t21 -57t-21 -57t-55 -22z" />
    <glyph glyph-name="Scedilla.dup" unicode="&#xf6c1;" horiz-adv-x="340" 
d="M41 32l27 102q75 -43 163 -43q58 0 90.5 25t32.5 67q0 38 -27.5 63.5t-92.5 48.5q-182 66 -182 196q0 84 65 139t173 55q96 0 162 -34l-30 -99q-59 32 -134 32q-55 0 -83.5 -24t-28.5 -57q0 -37 28.5 -60t100.5 -51q89 -34 131.5 -81t42.5 -119q0 -73 -47 -126.5
t-138 -69.5l-24 -43q30 -4 50.5 -23t20.5 -47q0 -43 -32.5 -64t-80.5 -21q-45 0 -76 19l18 50q28 -16 54 -16q16 0 27.5 7.5t11.5 20.5q0 32 -78 40l38 70h-5q-43 0 -94 12.5t-83 30.5z" />
    <glyph glyph-name="scedilla.dup" unicode="&#xf6c2;" horiz-adv-x="268" 
d="M36 24l24 89q58 -35 125 -35q78 0 78 54q0 25 -17.5 40t-62.5 31q-134 47 -134 142q0 66 50.5 109.5t133.5 43.5q75 0 129 -29l-24 -87q-49 28 -103 28q-32 0 -50 -14t-18 -37t18.5 -37t66.5 -31q67 -24 98.5 -60t31.5 -89q0 -56 -36 -95.5t-104 -51.5l-24 -42
q29 -3 50 -22.5t21 -48.5q0 -44 -32 -64t-82 -20q-45 0 -74 19l17 50q27 -16 55 -16q16 0 27.5 7t10.5 22q0 33 -77 39l37 71h-2q-72 0 -133 34z" />
    <glyph glyph-name="space_uni0326" unicode="&#xf6c3;" 
d="M86 -233l-19 54q29 5 49 21.5t20 41.5q0 31 -31 60l87 16q40 -28 40 -76q0 -56 -41.5 -85t-104.5 -32z" />
    <glyph glyph-name="acute.cap" unicode="&#xf6c9;" 
d="M177 830h135l-124 -120h-99z" />
    <glyph glyph-name="caron.cap" unicode="&#xf6ca;" 
d="M197 710h-93l-103 119h92l57 -66h2l55 66h91z" />
    <glyph glyph-name="dieresis.cap" unicode="&#xf6cb;" 
d="M52 716h-1q-25 0 -42 18t-17 43t17 42.5t43 17.5q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-41.5 -18zM249 716h-1q-25 0 -41.5 18t-16.5 43q0 26 17 43t43 17q25 0 41.5 -17t16.5 -43q0 -25 -16.5 -43t-42.5 -18z" />
    <glyph glyph-name="dieresis_acutecomb.cap" unicode="&#xf6cc;" horiz-adv-x="301" 
d="M-8 717h-1q-24 0 -40 16.5t-16 40.5q0 25 16.5 41.5t41.5 16.5q23 0 39 -16.5t16 -41.5q0 -24 -16 -40.5t-40 -16.5zM310 717h-1q-23 0 -39 17t-16 40q0 25 16.5 41.5t40.5 16.5t40 -16.5t16 -41.5t-16 -41t-41 -16zM126 832h128l-116 -121h-90z" />
    <glyph glyph-name="dieresis_gravecomb.cap" unicode="&#xf6cd;" horiz-adv-x="301" 
d="M-9 717h-1q-23 0 -39 17t-16 40q0 25 16.5 41.5t41.5 16.5q23 0 39 -16.5t16 -41.5q0 -24 -16 -40.5t-41 -16.5zM310 717h-1q-23 0 -39 17t-16 40q0 25 16.5 41.5t40.5 16.5t40 -16.5t16 -41.5t-16 -41t-41 -16zM46 832h128l79 -121h-91z" />
    <glyph glyph-name="grave.cap" unicode="&#xf6ce;" 
d="M-8 830h135l88 -119h-99z" />
    <glyph glyph-name="hungarumlaut.cap" unicode="&#xf6cf;" 
d="M92 831h121l-106 -114h-87zM258 831h121l-106 -114h-87z" />
    <glyph glyph-name="macron.cap" unicode="&#xf6d0;" 
d="M25 803h251v-69h-251v69z" />
    <glyph glyph-name="breve.cyrcap" unicode="&#xf6d1;" 
d="M11 840h87q3 -61 52 -61q46 0 53 61h86q-8 -116 -142 -116q-127 0 -136 116z" />
    <glyph glyph-name="circumflex.cyrcap" unicode="&#xf6d2;" 
d="M289 723h-86q-5 60 -52 60q-46 0 -53 -60h-87q9 115 142 115q63 0 97.5 -30t38.5 -85z" />
    <glyph glyph-name="space_uni030F.cap" unicode="&#xf6d3;" 
d="M-71 832h129l87 -121h-91zM128 832h129l86 -121h-91z" />
    <glyph glyph-name="breve.cyr" unicode="&#xf6d4;" 
d="M13 686h82q1 -33 15 -53t39 -20q50 0 57 73h82q-4 -64 -41.5 -98.5t-99.5 -34.5q-127 0 -134 133z" />
    <glyph glyph-name="circumflex.cyr" unicode="&#xf6d5;" 
d="M91 561h-81q4 66 41 100t100 34q65 0 98 -36t36 -98h-82q-2 34 -16 54t-38 20q-51 0 -58 -74z" />
    <glyph glyph-name="space_uni030F" unicode="&#xf6d6;" 
d="M-18 693h107l72 -146h-72zM148 693h107l71 -146h-72z" />
    <glyph glyph-name="dieresis_acutecomb" unicode="&#xf6d7;" horiz-adv-x="301" 
d="M8 566h-1q-24 0 -40 17.5t-16 41.5q0 25 16.5 41.5t41.5 16.5q24 0 40 -16.5t16 -41.5t-16 -42t-41 -17zM289 566h-1q-24 0 -40 17.5t-16 41.5q0 25 16.5 41.5t41.5 16.5t41 -16.5t16 -41.5q0 -26 -16.5 -42.5t-41.5 -16.5zM136 706h112l-102 -148h-77z" />
    <glyph glyph-name="dieresis_gravecomb" unicode="&#xf6d8;" horiz-adv-x="301" 
d="M11 566h-1q-24 0 -40.5 17.5t-16.5 41.5q0 25 17 41.5t42 16.5q24 0 40 -16.5t16 -41.5t-16 -42t-41 -17zM288 566h-1q-24 0 -40 17.5t-16 41.5q0 25 16.5 41.5t41.5 16.5t41 -16.5t16 -41.5q0 -26 -16.5 -42.5t-41.5 -16.5zM52 706h113l66 -148h-76z" />
    <glyph glyph-name="one.fitted" unicode="&#xf6dc;" horiz-adv-x="356" 
d="M154 0v539h-2l-119 -60l-21 93l158 78h103v-650h-119z" />
    <glyph glyph-name="cent.inferior" unicode="&#xf6df;" horiz-adv-x="323" 
d="M218 -153h-57v58q-57 8 -91 46q-34 37 -34 98q0 57 33 98.5t92 52.5v56h57v-54q31 0 60 -12l-14 -65q-26 12 -56 12q-38 0 -60.5 -23t-22.5 -60q0 -41 23.5 -62.5t59.5 -21.5q34 0 62 11l12 -62q-29 -14 -64 -15v-57z" />
    <glyph glyph-name="cent.superior" unicode="&#xf6e0;" horiz-adv-x="323" 
d="M218 437h-57v58q-57 8 -91 46q-34 37 -34 98q0 57 33 98.5t92 52.5v56h57v-54q31 0 60 -12l-14 -65q-26 12 -56 12q-38 0 -60.5 -23t-22.5 -60q0 -41 23.5 -62.5t59.5 -21.5q34 0 62 11l12 -62q-29 -14 -64 -15v-57z" />
    <glyph glyph-name="comma.inferior" unicode="&#xf6e1;" horiz-adv-x="153" 
d="M59 -211l-63 -5q30 85 44 171l94 6q-31 -100 -75 -172z" />
    <glyph glyph-name="comma.superior" unicode="&#xf6e2;" horiz-adv-x="153" 
d="M59 379l-63 -5q30 85 44 171l94 6q-31 -100 -75 -172z" />
    <glyph glyph-name="dollar.inferior" unicode="&#xf6e3;" horiz-adv-x="336" 
d="M194 -198h-55v57q-65 2 -106 27l18 65q49 -27 103 -27q29 0 45.5 11t16.5 30q0 18 -14.5 29t-52.5 24q-57 20 -84.5 44t-27.5 65q0 40 27.5 68.5t76.5 37.5v56h56v-53q50 -1 89 -20l-18 -64q-47 22 -89 22q-55 0 -55 -37q0 -16 15.5 -27t60.5 -26q55 -18 79 -44.5
t24 -67.5q0 -40 -28.5 -70.5t-80.5 -38.5v-61z" />
    <glyph glyph-name="dollar.superior" unicode="&#xf6e4;" horiz-adv-x="336" 
d="M194 392h-55v57q-65 2 -106 27l18 65q49 -27 103 -27q29 0 45.5 11t16.5 30q0 18 -14.5 29t-52.5 24q-57 20 -84.5 44t-27.5 65q0 40 27.5 68.5t76.5 37.5v56h56v-53q50 -1 89 -20l-18 -64q-47 22 -89 22q-55 0 -55 -37q0 -16 15.5 -27t60.5 -26q55 -18 79 -44.5
t24 -67.5q0 -40 -28.5 -70.5t-80.5 -38.5v-61z" />
    <glyph glyph-name="hyphen.inferior" unicode="&#xf6e5;" horiz-adv-x="199" 
d="M18 68h163v-60h-163v60z" />
    <glyph glyph-name="hyphen.superior" unicode="&#xf6e6;" horiz-adv-x="199" 
d="M18 658h163v-60h-163v60z" />
    <glyph glyph-name="period.inferior" unicode="&#xf6e7;" horiz-adv-x="153" 
d="M76 -153h-1q-23 0 -38 16t-15 40q0 25 15 40t40 15t40 -15t15 -40q0 -24 -15.5 -40t-40.5 -16z" />
    <glyph glyph-name="period.superior" unicode="&#xf6e8;" horiz-adv-x="153" 
d="M76 437h-1q-23 0 -38 16t-15 40q0 25 15 40t40 15t40 -15t15 -40q0 -24 -15.5 -40t-40.5 -16z" />
    <glyph glyph-name="zero.oldstyle" unicode="&#xf730;" horiz-adv-x="547" 
d="M274 -11h-1q-107 0 -172 71t-65 184q0 112 65.5 183.5t172.5 71.5t172.5 -71t65.5 -184q0 -112 -65.5 -183.5t-172.5 -71.5zM274 80h1q55 0 87 45t32 119t-32 119t-88 45t-88.5 -45t-32.5 -119t32.5 -119t88.5 -45z" />
    <glyph glyph-name="one.oldstyle" unicode="&#xf731;" horiz-adv-x="390" 
d="M298 0h-119v380h-2l-130 -60l-21 91l172 76h100v-487z" />
    <glyph glyph-name="two.oldstyle" unicode="&#xf732;" horiz-adv-x="463" 
d="M437 0h-414v67l55 37q107 68 154 116.5t47 98.5q0 41 -26 62.5t-71 21.5q-62 0 -126 -41l-29 79q82 60 190 60q85 0 135.5 -43t50.5 -118q0 -62 -41 -113t-116 -103l-46 -32v-2q45 6 95 6h142v-96z" />
    <glyph glyph-name="three.oldstyle" unicode="&#xf733;" horiz-adv-x="454" 
d="M18 -99l14 90q49 -17 113 -17q66 0 107 27t41 74q0 40 -37 63.5t-100 23.5q-26 0 -54 -2l-1 87l23 2q66 5 107 27.5t41 59.5q0 33 -25 50.5t-69 17.5q-59 0 -125 -34l-23 81q30 20 78.5 34t97.5 14q84 0 136 -38.5t52 -102.5q0 -50 -33.5 -87.5t-85.5 -51.5v-2
q63 -10 104 -47t41 -96q0 -87 -77 -141t-185 -54q-88 0 -140 22z" />
    <glyph glyph-name="four.oldstyle" unicode="&#xf734;" horiz-adv-x="526" 
d="M507 40h-84v-161h-114v161h-288v79l257 373h145v-361h84v-91zM309 131v161q0 70 3 104l-5 1q-33 -68 -58 -106l-113 -157l2 -3h171z" />
    <glyph glyph-name="five.oldstyle" unicode="&#xf735;" horiz-adv-x="455" 
d="M29 -102l14 87q41 -13 104 -13q62 0 104 29t42 79q0 36 -25 61q-47 47 -220 40l53 306h309l-14 -99h-214l-23 -120q148 0 214 -63q46 -46 46 -116q0 -93 -71 -151.5t-192 -58.5q-78 0 -127 19z" />
    <glyph glyph-name="six.oldstyle" unicode="&#xf736;" horiz-adv-x="537" 
d="M449 611l8 -88q-126 -6 -197 -56t-87 -124l2 -1q49 59 133 59q82 0 136.5 -52t54.5 -140q0 -97 -66.5 -158.5t-158.5 -61.5q-107 0 -170.5 69.5t-63.5 183.5q0 160 109.5 262t299.5 107zM275 81h1q40 0 70.5 31.5t30.5 89.5q0 53 -28.5 85t-74.5 32q-55 0 -89 -40
q-23 -24 -23 -65q0 -58 30.5 -95.5t82.5 -37.5z" />
    <glyph glyph-name="seven.oldstyle" unicode="&#xf737;" horiz-adv-x="454" 
d="M430 487v-75q-44 -115 -121.5 -263.5t-154.5 -267.5l-118 16q68 99 148.5 244t118.5 243v2h-285v101h412z" />
    <glyph glyph-name="eight.oldstyle" unicode="&#xf738;" horiz-adv-x="535" 
d="M376 325v-4q55 -21 88.5 -59t33.5 -90q0 -82 -66.5 -132.5t-168.5 -50.5q-101 0 -164 47.5t-63 122.5q0 102 118 154v4q-95 42 -95 127q0 74 60 121.5t154 47.5q93 0 147 -45t54 -110q0 -86 -98 -133zM272 534h-1q-40 0 -66 -23t-26 -59q0 -66 105 -95q34 11 56 35.5
t22 57.5q0 39 -26.5 61.5t-63.5 22.5zM265 74h1q45 0 75 25t30 64q0 40 -32 69.5t-89 44.5q-42 -14 -66.5 -42.5t-24.5 -64.5q0 -42 29.5 -69t76.5 -27z" />
    <glyph glyph-name="nine.oldstyle" unicode="&#xf739;" horiz-adv-x="537" 
d="M82 -118l-8 89q241 9 288 175l-2 1q-17 -23 -53.5 -40t-79.5 -17q-81 0 -135 53t-54 139q0 95 66.5 157.5t164.5 62.5q101 0 164 -68.5t63 -180.5q0 -118 -56 -202.5t-148 -125.5t-210 -43zM264 174h1q29 0 56 15t40 38q13 18 13 54q0 56 -31.5 92t-77.5 36
q-42 0 -73.5 -31.5t-31.5 -87.5q0 -55 29.5 -85.5t74.5 -30.5z" />
    <hkern u1="&#x20;" u2="&#x38f;" k="12" />
    <hkern u1="&#x20;" u2="&#x38e;" k="30" />
    <hkern u1="&#x20;" u2="&#x38c;" k="12" />
    <hkern u1="&#x20;" u2="&#x386;" k="50" />
    <hkern u1="C" u2="&#x152;" k="28" />
    <hkern u1="C" u2="&#x127;" k="-4" />
    <hkern u1="D" u2="&#x142;" k="-5" />
    <hkern u1="F" u2="&#xef;" k="-20" />
    <hkern u1="L" u2="&#x1fe;" k="35" />
    <hkern u1="L" u2="&#xd8;" k="35" />
    <hkern u1="O" u2="&#x142;" k="-5" />
    <hkern u1="Q" u2="&#x142;" k="-5" />
    <hkern u1="S" u2="&#x1fd;" k="-6" />
    <hkern u1="S" u2="&#xe6;" k="-6" />
    <hkern u1="T" u2="&#x133;" k="18" />
    <hkern u1="T" u2="&#x131;" k="18" />
    <hkern u1="T" u2="&#x12f;" k="18" />
    <hkern u1="T" u2="&#x12d;" k="-39" />
    <hkern u1="T" u2="&#x12b;" k="18" />
    <hkern u1="T" u2="&#x129;" k="18" />
    <hkern u1="T" u2="&#xef;" k="-64" />
    <hkern u1="T" u2="&#xee;" k="18" />
    <hkern u1="T" u2="&#xed;" k="18" />
    <hkern u1="T" u2="&#xec;" k="18" />
    <hkern u1="T" u2="&#xe8;" k="74" />
    <hkern u1="T" u2="&#xe0;" k="48" />
    <hkern u1="T" u2="i" k="18" />
    <hkern u1="V" u2="&#x1fc;" k="59" />
    <hkern u1="V" u2="&#x12d;" k="-37" />
    <hkern u1="V" u2="&#xef;" k="-61" />
    <hkern u1="V" u2="&#xc6;" k="59" />
    <hkern u1="W" u2="&#x1fc;" k="59" />
    <hkern u1="W" u2="&#x12d;" k="-37" />
    <hkern u1="W" u2="&#xef;" k="-61" />
    <hkern u1="W" u2="&#xc6;" k="59" />
    <hkern u1="Y" u2="&#x12d;" k="-17" />
    <hkern u1="Y" u2="&#xf6;" k="71" />
    <hkern u1="Y" u2="&#xef;" k="-48" />
    <hkern u1="Y" u2="&#xeb;" k="71" />
    <hkern u1="Y" u2="&#xe4;" k="52" />
    <hkern u1="r" u2="&#x142;" k="-5" />
    <hkern u1="&#xa0;" u2="&#x38f;" k="12" />
    <hkern u1="&#xa0;" u2="&#x38e;" k="30" />
    <hkern u1="&#xa0;" u2="&#x38c;" k="12" />
    <hkern u1="&#xa0;" u2="&#x386;" k="50" />
    <hkern u1="&#xc7;" u2="&#x152;" k="28" />
    <hkern u1="&#xc7;" u2="&#x127;" k="-4" />
    <hkern u1="&#xd0;" u2="&#x142;" k="-5" />
    <hkern u1="&#xd2;" u2="&#x142;" k="-5" />
    <hkern u1="&#xd3;" u2="&#x142;" k="-5" />
    <hkern u1="&#xd4;" u2="&#x142;" k="-5" />
    <hkern u1="&#xd5;" u2="&#x142;" k="-5" />
    <hkern u1="&#xd6;" u2="&#x142;" k="-5" />
    <hkern u1="&#xd8;" u2="&#x142;" k="-5" />
    <hkern u1="&#xdd;" u2="&#x12d;" k="-17" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="71" />
    <hkern u1="&#xdd;" u2="&#xef;" k="-48" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="71" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="52" />
    <hkern u1="&#x106;" u2="&#x152;" k="28" />
    <hkern u1="&#x106;" u2="&#x127;" k="-4" />
    <hkern u1="&#x108;" u2="&#x152;" k="28" />
    <hkern u1="&#x108;" u2="&#x127;" k="-4" />
    <hkern u1="&#x10a;" u2="&#x152;" k="28" />
    <hkern u1="&#x10a;" u2="&#x127;" k="-4" />
    <hkern u1="&#x10c;" u2="&#x152;" k="28" />
    <hkern u1="&#x10c;" u2="&#x127;" k="-4" />
    <hkern u1="&#x10e;" u2="&#x142;" k="-5" />
    <hkern u1="&#x10f;" u2="&#x201d;" k="-92" />
    <hkern u1="&#x10f;" u2="&#x2019;" k="-92" />
    <hkern u1="&#x10f;" u2="&#x142;" k="-83" />
    <hkern u1="&#x10f;" u2="&#x140;" k="-83" />
    <hkern u1="&#x10f;" u2="&#x13e;" k="-83" />
    <hkern u1="&#x10f;" u2="&#x13c;" k="-83" />
    <hkern u1="&#x10f;" u2="&#x13a;" k="-83" />
    <hkern u1="&#x10f;" u2="&#x137;" k="-83" />
    <hkern u1="&#x10f;" u2="&#x127;" k="-83" />
    <hkern u1="&#x10f;" u2="&#x125;" k="-83" />
    <hkern u1="&#x10f;" u2="l" k="-83" />
    <hkern u1="&#x10f;" u2="k" k="-83" />
    <hkern u1="&#x10f;" u2="h" k="-83" />
    <hkern u1="&#x10f;" u2="b" k="-83" />
    <hkern u1="&#x110;" u2="&#x142;" k="-5" />
    <hkern u1="&#x139;" u2="&#x1fe;" k="35" />
    <hkern u1="&#x139;" u2="&#xd8;" k="35" />
    <hkern u1="&#x13b;" u2="&#x1fe;" k="35" />
    <hkern u1="&#x13b;" u2="&#xd8;" k="35" />
    <hkern u1="&#x13d;" u2="&#x1fe;" k="35" />
    <hkern u1="&#x13d;" u2="&#xd8;" k="35" />
    <hkern u1="&#x13f;" u2="&#xe000;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ee2;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ee0;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ede;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1edc;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1eda;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ed8;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ed6;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ed4;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ed2;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ed0;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ece;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1ecc;" k="34" />
    <hkern u1="&#x13f;" u2="&#x39f;" k="34" />
    <hkern u1="&#x13f;" u2="&#x398;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1fe;" k="34" />
    <hkern u1="&#x13f;" u2="&#x1a0;" k="34" />
    <hkern u1="&#x13f;" u2="&#x152;" k="34" />
    <hkern u1="&#x13f;" u2="&#x150;" k="34" />
    <hkern u1="&#x13f;" u2="&#x14e;" k="34" />
    <hkern u1="&#x13f;" u2="&#x14c;" k="34" />
    <hkern u1="&#x13f;" u2="&#x122;" k="34" />
    <hkern u1="&#x13f;" u2="&#x120;" k="34" />
    <hkern u1="&#x13f;" u2="&#x11e;" k="34" />
    <hkern u1="&#x13f;" u2="&#x11c;" k="34" />
    <hkern u1="&#x13f;" u2="&#x10c;" k="34" />
    <hkern u1="&#x13f;" u2="&#x10a;" k="34" />
    <hkern u1="&#x13f;" u2="&#x108;" k="34" />
    <hkern u1="&#x13f;" u2="&#x106;" k="34" />
    <hkern u1="&#x13f;" u2="&#xd8;" k="34" />
    <hkern u1="&#x13f;" u2="&#xd6;" k="34" />
    <hkern u1="&#x13f;" u2="&#xd5;" k="34" />
    <hkern u1="&#x13f;" u2="&#xd4;" k="34" />
    <hkern u1="&#x13f;" u2="&#xd3;" k="34" />
    <hkern u1="&#x13f;" u2="&#xd2;" k="34" />
    <hkern u1="&#x13f;" u2="&#xc7;" k="34" />
    <hkern u1="&#x13f;" u2="Q" k="34" />
    <hkern u1="&#x13f;" u2="O" k="34" />
    <hkern u1="&#x13f;" u2="G" k="34" />
    <hkern u1="&#x13f;" u2="C" k="34" />
    <hkern u1="&#x141;" u2="&#x1fe;" k="35" />
    <hkern u1="&#x141;" u2="&#xd8;" k="35" />
    <hkern u1="&#x142;" u2="&#x201d;" k="-10" />
    <hkern u1="&#x142;" u2="&#x2019;" k="-10" />
    <hkern u1="&#x14c;" u2="&#x142;" k="-5" />
    <hkern u1="&#x14e;" u2="&#x142;" k="-5" />
    <hkern u1="&#x150;" u2="&#x142;" k="-5" />
    <hkern u1="&#x155;" u2="&#x142;" k="-5" />
    <hkern u1="&#x157;" u2="&#x142;" k="-5" />
    <hkern u1="&#x159;" u2="&#x142;" k="-5" />
    <hkern u1="&#x15a;" u2="&#x1fd;" k="-6" />
    <hkern u1="&#x15a;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x15c;" u2="&#x1fd;" k="-6" />
    <hkern u1="&#x15c;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x15e;" u2="&#x1fd;" k="-6" />
    <hkern u1="&#x15e;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x160;" u2="&#x1fd;" k="-6" />
    <hkern u1="&#x160;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x162;" u2="&#x133;" k="18" />
    <hkern u1="&#x162;" u2="&#x131;" k="18" />
    <hkern u1="&#x162;" u2="&#x12f;" k="18" />
    <hkern u1="&#x162;" u2="&#x12d;" k="-39" />
    <hkern u1="&#x162;" u2="&#x12b;" k="18" />
    <hkern u1="&#x162;" u2="&#x129;" k="18" />
    <hkern u1="&#x162;" u2="&#xef;" k="-64" />
    <hkern u1="&#x162;" u2="&#xee;" k="18" />
    <hkern u1="&#x162;" u2="&#xed;" k="18" />
    <hkern u1="&#x162;" u2="&#xec;" k="18" />
    <hkern u1="&#x162;" u2="&#xe8;" k="74" />
    <hkern u1="&#x162;" u2="&#xe0;" k="48" />
    <hkern u1="&#x162;" u2="i" k="18" />
    <hkern u1="&#x164;" u2="&#x133;" k="18" />
    <hkern u1="&#x164;" u2="&#x131;" k="18" />
    <hkern u1="&#x164;" u2="&#x12f;" k="18" />
    <hkern u1="&#x164;" u2="&#x12d;" k="-39" />
    <hkern u1="&#x164;" u2="&#x12b;" k="18" />
    <hkern u1="&#x164;" u2="&#x129;" k="18" />
    <hkern u1="&#x164;" u2="&#xef;" k="-64" />
    <hkern u1="&#x164;" u2="&#xee;" k="18" />
    <hkern u1="&#x164;" u2="&#xed;" k="18" />
    <hkern u1="&#x164;" u2="&#xec;" k="18" />
    <hkern u1="&#x164;" u2="&#xe8;" k="74" />
    <hkern u1="&#x164;" u2="&#xe0;" k="48" />
    <hkern u1="&#x164;" u2="i" k="18" />
    <hkern u1="&#x165;" u2="&#x201d;" k="-61" />
    <hkern u1="&#x165;" u2="&#x2019;" k="-61" />
    <hkern u1="&#x166;" u2="&#x133;" k="18" />
    <hkern u1="&#x166;" u2="&#x131;" k="18" />
    <hkern u1="&#x166;" u2="&#x12f;" k="18" />
    <hkern u1="&#x166;" u2="&#x12d;" k="-39" />
    <hkern u1="&#x166;" u2="&#x12b;" k="18" />
    <hkern u1="&#x166;" u2="&#x129;" k="18" />
    <hkern u1="&#x166;" u2="&#xef;" k="-64" />
    <hkern u1="&#x166;" u2="&#xee;" k="18" />
    <hkern u1="&#x166;" u2="&#xed;" k="18" />
    <hkern u1="&#x166;" u2="&#xec;" k="18" />
    <hkern u1="&#x166;" u2="&#xe8;" k="74" />
    <hkern u1="&#x166;" u2="&#xe0;" k="48" />
    <hkern u1="&#x166;" u2="i" k="18" />
    <hkern u1="&#x174;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x174;" u2="&#x12d;" k="-37" />
    <hkern u1="&#x174;" u2="&#xef;" k="-61" />
    <hkern u1="&#x174;" u2="&#xc6;" k="59" />
    <hkern u1="&#x178;" u2="&#x12d;" k="-17" />
    <hkern u1="&#x178;" u2="&#xf6;" k="71" />
    <hkern u1="&#x178;" u2="&#xef;" k="-48" />
    <hkern u1="&#x178;" u2="&#xeb;" k="71" />
    <hkern u1="&#x178;" u2="&#xe4;" k="52" />
    <hkern u1="&#x1a0;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1fe;" u2="&#x142;" k="-5" />
    <hkern u1="&#x218;" u2="&#x1fd;" k="-6" />
    <hkern u1="&#x218;" u2="&#xe6;" k="-6" />
    <hkern u1="&#x21a;" u2="&#x133;" k="18" />
    <hkern u1="&#x21a;" u2="&#x131;" k="18" />
    <hkern u1="&#x21a;" u2="&#x12f;" k="18" />
    <hkern u1="&#x21a;" u2="&#x12d;" k="-39" />
    <hkern u1="&#x21a;" u2="&#x12b;" k="18" />
    <hkern u1="&#x21a;" u2="&#x129;" k="18" />
    <hkern u1="&#x21a;" u2="&#xef;" k="-64" />
    <hkern u1="&#x21a;" u2="&#xee;" k="18" />
    <hkern u1="&#x21a;" u2="&#xed;" k="18" />
    <hkern u1="&#x21a;" u2="&#xec;" k="18" />
    <hkern u1="&#x21a;" u2="&#xe8;" k="74" />
    <hkern u1="&#x21a;" u2="&#xe0;" k="48" />
    <hkern u1="&#x21a;" u2="i" k="18" />
    <hkern u1="&#x232;" u2="&#x12d;" k="-17" />
    <hkern u1="&#x232;" u2="&#xf6;" k="71" />
    <hkern u1="&#x232;" u2="&#xef;" k="-48" />
    <hkern u1="&#x232;" u2="&#xeb;" k="71" />
    <hkern u1="&#x232;" u2="&#xe4;" k="52" />
    <hkern u1="&#x388;" u2="&#x3ca;" k="-27" />
    <hkern u1="&#x388;" u2="&#x390;" k="-53" />
    <hkern u1="&#x389;" u2="&#x3ca;" k="-17" />
    <hkern u1="&#x389;" u2="&#x390;" k="-42" />
    <hkern u1="&#x38a;" u2="&#x3ca;" k="-17" />
    <hkern u1="&#x38a;" u2="&#x390;" k="-42" />
    <hkern u1="&#x38c;" u2="&#x3be;" k="-8" />
    <hkern u1="&#x38c;" u2="&#x142;" k="-5" />
    <hkern u1="&#x38e;" u2="&#x3ca;" k="-58" />
    <hkern u1="&#x38e;" u2="&#x3bb;" k="3" />
    <hkern u1="&#x38e;" u2="&#x3b4;" k="67" />
    <hkern u1="&#x38e;" u2="&#x3b2;" k="19" />
    <hkern u1="&#x38e;" u2="&#x3b0;" k="6" />
    <hkern u1="&#x38e;" u2="&#x390;" k="-87" />
    <hkern u1="&#x38f;" u2="&#x3ca;" k="-6" />
    <hkern u1="&#x38f;" u2="&#x390;" k="-21" />
    <hkern u1="&#x390;" u2="&#x3c0;" k="-31" />
    <hkern u1="&#x390;" u2="&#x3be;" k="-38" />
    <hkern u1="&#x390;" u2="&#x3bb;" k="-63" />
    <hkern u1="&#x390;" u2="&#x3ba;" k="-4" />
    <hkern u1="&#x390;" u2="&#x3b8;" k="-42" />
    <hkern u1="&#x390;" u2="&#x3b7;" k="-4" />
    <hkern u1="&#x390;" u2="&#x3b6;" k="-50" />
    <hkern u1="&#x390;" u2="&#x3b2;" k="-38" />
    <hkern u1="&#x390;" u2="&#x3ae;" k="-4" />
    <hkern u1="&#x390;" u2="&#xb5;" k="-4" />
    <hkern u1="&#x391;" u2="&#x3c2;" k="13" />
    <hkern u1="&#x391;" u2="&#x3be;" k="4" />
    <hkern u1="&#x391;" u2="&#x3bd;" k="29" />
    <hkern u1="&#x391;" u2="&#x3b4;" k="11" />
    <hkern u1="&#x391;" u2="&#x390;" k="4" />
    <hkern u1="&#x392;" u2="&#x3b4;" k="-5" />
    <hkern u1="&#x393;" u2="&#x3cb;" k="34" />
    <hkern u1="&#x393;" u2="&#x3ca;" k="-64" />
    <hkern u1="&#x393;" u2="&#x3b4;" k="43" />
    <hkern u1="&#x393;" u2="&#x3b2;" k="14" />
    <hkern u1="&#x393;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x393;" u2="&#x390;" k="-94" />
    <hkern u1="&#x394;" u2="&#x3be;" k="-9" />
    <hkern u1="&#x395;" u2="&#x3ca;" k="-27" />
    <hkern u1="&#x395;" u2="&#x3be;" k="3" />
    <hkern u1="&#x395;" u2="&#x390;" k="-53" />
    <hkern u1="&#x397;" u2="&#x3ca;" k="-17" />
    <hkern u1="&#x397;" u2="&#x390;" k="-42" />
    <hkern u1="&#x398;" u2="&#x3be;" k="-8" />
    <hkern u1="&#x398;" u2="&#x142;" k="-5" />
    <hkern u1="&#x399;" u2="&#x3ca;" k="-17" />
    <hkern u1="&#x399;" u2="&#x390;" k="-42" />
    <hkern u1="&#x39a;" u2="&#x3ca;" k="-45" />
    <hkern u1="&#x39a;" u2="&#x3c2;" k="10" />
    <hkern u1="&#x39a;" u2="&#x3b2;" k="-5" />
    <hkern u1="&#x39a;" u2="&#x3b0;" k="8" />
    <hkern u1="&#x39a;" u2="&#x390;" k="-71" />
    <hkern u1="&#x39b;" u2="&#x390;" k="4" />
    <hkern u1="&#x39d;" u2="&#x3ca;" k="-17" />
    <hkern u1="&#x39d;" u2="&#x3c1;" k="5" />
    <hkern u1="&#x39d;" u2="&#x3bf;" k="5" />
    <hkern u1="&#x39d;" u2="&#x3b4;" k="5" />
    <hkern u1="&#x39d;" u2="&#x391;" k="11" />
    <hkern u1="&#x39d;" u2="&#x390;" k="-42" />
    <hkern u1="&#x39e;" u2="&#x3ca;" k="-40" />
    <hkern u1="&#x39e;" u2="&#x3b9;" k="-6" />
    <hkern u1="&#x39e;" u2="&#x3b7;" k="-9" />
    <hkern u1="&#x39e;" u2="&#x390;" k="-60" />
    <hkern u1="&#x39f;" u2="&#x3be;" k="-8" />
    <hkern u1="&#x39f;" u2="&#x142;" k="-5" />
    <hkern u1="&#x3a0;" u2="&#x3ca;" k="-17" />
    <hkern u1="&#x3a0;" u2="&#x390;" k="-42" />
    <hkern u1="&#x3a1;" u2="&#x3ca;" k="-24" />
    <hkern u1="&#x3a1;" u2="&#x390;" k="-51" />
    <hkern u1="&#x3a3;" u2="&#x3ca;" k="-19" />
    <hkern u1="&#x3a3;" u2="&#x3c7;" k="7" />
    <hkern u1="&#x3a3;" u2="&#x3b4;" k="12" />
    <hkern u1="&#x3a3;" u2="&#x3aa;" k="-4" />
    <hkern u1="&#x3a3;" u2="&#x390;" k="-44" />
    <hkern u1="&#x3a4;" u2="&#x3cb;" k="21" />
    <hkern u1="&#x3a4;" u2="&#x3ca;" k="-63" />
    <hkern u1="&#x3a4;" u2="&#x3b4;" k="51" />
    <hkern u1="&#x3a4;" u2="&#x3b0;" k="15" />
    <hkern u1="&#x3a4;" u2="&#x3a3;" k="-9" />
    <hkern u1="&#x3a4;" u2="&#x390;" k="-98" />
    <hkern u1="&#x3a4;" u2="&#x133;" k="18" />
    <hkern u1="&#x3a4;" u2="&#x131;" k="18" />
    <hkern u1="&#x3a4;" u2="&#x12f;" k="18" />
    <hkern u1="&#x3a4;" u2="&#x12d;" k="-39" />
    <hkern u1="&#x3a4;" u2="&#x12b;" k="18" />
    <hkern u1="&#x3a4;" u2="&#x129;" k="18" />
    <hkern u1="&#x3a4;" u2="&#xef;" k="-64" />
    <hkern u1="&#x3a4;" u2="&#xee;" k="18" />
    <hkern u1="&#x3a4;" u2="&#xed;" k="18" />
    <hkern u1="&#x3a4;" u2="&#xec;" k="18" />
    <hkern u1="&#x3a4;" u2="&#xe8;" k="74" />
    <hkern u1="&#x3a4;" u2="&#xe0;" k="48" />
    <hkern u1="&#x3a4;" u2="i" k="18" />
    <hkern u1="&#x3a5;" u2="&#x3ca;" k="-58" />
    <hkern u1="&#x3a5;" u2="&#x3bb;" k="3" />
    <hkern u1="&#x3a5;" u2="&#x3b4;" k="50" />
    <hkern u1="&#x3a5;" u2="&#x3b2;" k="19" />
    <hkern u1="&#x3a5;" u2="&#x3b0;" k="6" />
    <hkern u1="&#x3a5;" u2="&#x390;" k="-87" />
    <hkern u1="&#x3a7;" u2="&#x3ca;" k="-37" />
    <hkern u1="&#x3a7;" u2="&#x390;" k="-58" />
    <hkern u1="&#x3a8;" u2="&#x3aa;" k="-5" />
    <hkern u1="&#x3a9;" u2="&#x3ca;" k="-6" />
    <hkern u1="&#x3a9;" u2="&#x3bd;" k="-21" />
    <hkern u1="&#x3a9;" u2="&#x390;" k="-21" />
    <hkern u1="&#x3a9;" u2="&#xb5;" k="-5" />
    <hkern u1="&#x3ad;" u2="&#x3ca;" k="-22" />
    <hkern u1="&#x3ad;" u2="&#x390;" k="-36" />
    <hkern u1="&#x3af;" u2="&#x3bb;" k="-12" />
    <hkern u1="&#x3b1;" u2="&#x3b6;" k="-13" />
    <hkern u1="&#x3b2;" u2="&#x3ca;" k="-11" />
    <hkern u1="&#x3b2;" u2="&#x390;" k="-30" />
    <hkern u1="&#x3b3;" u2="&#x3b3;" k="-11" />
    <hkern u1="&#x3b4;" u2="&#x3ca;" k="-13" />
    <hkern u1="&#x3b4;" u2="&#x3c1;" k="13" />
    <hkern u1="&#x3b4;" u2="&#x3b3;" k="10" />
    <hkern u1="&#x3b4;" u2="&#x390;" k="-29" />
    <hkern u1="&#x3b6;" u2="&#x3c6;" k="7" />
    <hkern u1="&#x3b6;" u2="&#x3b2;" k="-10" />
    <hkern u1="&#x3b7;" u2="&#x3bd;" k="9" />
    <hkern u1="&#x3b8;" u2="&#x3be;" k="-4" />
    <hkern u1="&#x3b8;" u2="&#x3b6;" k="-12" />
    <hkern u1="&#x3bb;" u2="&#x3bd;" k="24" />
    <hkern u1="&#x3bd;" u2="&#x3c1;" k="20" />
    <hkern u1="&#x3bd;" u2="&#x3b6;" k="-5" />
    <hkern u1="&#x3bf;" u2="&#x3bd;" k="3" />
    <hkern u1="&#x3c0;" u2="&#x3ca;" k="-16" />
    <hkern u1="&#x3c0;" u2="&#x390;" k="-29" />
    <hkern u1="&#x3c3;" u2="&#x3ca;" k="-23" />
    <hkern u1="&#x3c3;" u2="&#x3b6;" k="-13" />
    <hkern u1="&#x3c3;" u2="&#x390;" k="-40" />
    <hkern u1="&#x3c5;" u2="&#x3c1;" k="7" />
    <hkern u1="&#x3c7;" u2="&#x3ca;" k="-13" />
    <hkern u1="&#x3c7;" u2="&#x390;" k="-21" />
    <hkern u1="&#x3ca;" u2="&#x3c0;" k="-17" />
    <hkern u1="&#x3ca;" u2="&#x3be;" k="-18" />
    <hkern u1="&#x3ca;" u2="&#x3bb;" k="-29" />
    <hkern u1="&#x3ca;" u2="&#x3ba;" k="-3" />
    <hkern u1="&#x3ca;" u2="&#x3b8;" k="-20" />
    <hkern u1="&#x3ca;" u2="&#x3b7;" k="-3" />
    <hkern u1="&#x3ca;" u2="&#x3b6;" k="-22" />
    <hkern u1="&#x3ca;" u2="&#x3b2;" k="-11" />
    <hkern u1="&#x3ca;" u2="&#x3ae;" k="-3" />
    <hkern u1="&#x3ca;" u2="&#xb5;" k="-3" />
    <hkern u1="&#x1e80;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x1e80;" u2="&#x12d;" k="-37" />
    <hkern u1="&#x1e80;" u2="&#xef;" k="-61" />
    <hkern u1="&#x1e80;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1e82;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x1e82;" u2="&#x12d;" k="-37" />
    <hkern u1="&#x1e82;" u2="&#xef;" k="-61" />
    <hkern u1="&#x1e82;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1e84;" u2="&#x1fc;" k="59" />
    <hkern u1="&#x1e84;" u2="&#x12d;" k="-37" />
    <hkern u1="&#x1e84;" u2="&#xef;" k="-61" />
    <hkern u1="&#x1e84;" u2="&#xc6;" k="59" />
    <hkern u1="&#x1ecc;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ece;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ed0;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ed2;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ed4;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ed6;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ed8;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1eda;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1edc;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ede;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ee0;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ee2;" u2="&#x142;" k="-5" />
    <hkern u1="&#x1ef2;" u2="&#x12d;" k="-17" />
    <hkern u1="&#x1ef2;" u2="&#xf6;" k="71" />
    <hkern u1="&#x1ef2;" u2="&#xef;" k="-48" />
    <hkern u1="&#x1ef2;" u2="&#xeb;" k="71" />
    <hkern u1="&#x1ef2;" u2="&#xe4;" k="52" />
    <hkern u1="&#x1ef4;" u2="&#x12d;" k="-17" />
    <hkern u1="&#x1ef4;" u2="&#xf6;" k="71" />
    <hkern u1="&#x1ef4;" u2="&#xef;" k="-48" />
    <hkern u1="&#x1ef4;" u2="&#xeb;" k="71" />
    <hkern u1="&#x1ef4;" u2="&#xe4;" k="52" />
    <hkern u1="&#x1ef6;" u2="&#x12d;" k="-17" />
    <hkern u1="&#x1ef6;" u2="&#xf6;" k="71" />
    <hkern u1="&#x1ef6;" u2="&#xef;" k="-48" />
    <hkern u1="&#x1ef6;" u2="&#xeb;" k="71" />
    <hkern u1="&#x1ef6;" u2="&#xe4;" k="52" />
    <hkern u1="&#x1ef8;" u2="&#x12d;" k="-17" />
    <hkern u1="&#x1ef8;" u2="&#xf6;" k="71" />
    <hkern u1="&#x1ef8;" u2="&#xef;" k="-48" />
    <hkern u1="&#x1ef8;" u2="&#xeb;" k="71" />
    <hkern u1="&#x1ef8;" u2="&#xe4;" k="52" />
    <hkern u1="&#x2018;" u2="&#x1ecb;" k="19" />
    <hkern u1="&#x2018;" u2="&#x1ec9;" k="19" />
    <hkern u1="&#x2018;" u2="&#x133;" k="19" />
    <hkern u1="&#x2018;" u2="&#x131;" k="19" />
    <hkern u1="&#x2018;" u2="&#x12f;" k="19" />
    <hkern u1="&#x2018;" u2="&#x12d;" k="19" />
    <hkern u1="&#x2018;" u2="&#x12b;" k="19" />
    <hkern u1="&#x2018;" u2="&#x129;" k="19" />
    <hkern u1="&#x2018;" u2="&#x127;" k="-10" />
    <hkern u1="&#x2018;" u2="&#xef;" k="19" />
    <hkern u1="&#x2018;" u2="&#xee;" k="19" />
    <hkern u1="&#x2018;" u2="&#xed;" k="19" />
    <hkern u1="&#x2018;" u2="&#xec;" k="19" />
    <hkern u1="&#x2018;" u2="i" k="19" />
    <hkern u1="&#x2019;" u2="&#x1ecb;" k="46" />
    <hkern u1="&#x2019;" u2="&#x1ec9;" k="46" />
    <hkern u1="&#x2019;" u2="&#x133;" k="46" />
    <hkern u1="&#x2019;" u2="&#x131;" k="46" />
    <hkern u1="&#x2019;" u2="&#x12f;" k="46" />
    <hkern u1="&#x2019;" u2="&#x12d;" k="46" />
    <hkern u1="&#x2019;" u2="&#x12b;" k="46" />
    <hkern u1="&#x2019;" u2="&#x129;" k="46" />
    <hkern u1="&#x2019;" u2="&#xef;" k="46" />
    <hkern u1="&#x2019;" u2="&#xee;" k="46" />
    <hkern u1="&#x2019;" u2="&#xed;" k="46" />
    <hkern u1="&#x2019;" u2="&#xec;" k="46" />
    <hkern u1="&#x2019;" u2="i" k="46" />
    <hkern u1="&#x201c;" u2="&#x1ecb;" k="19" />
    <hkern u1="&#x201c;" u2="&#x1ec9;" k="19" />
    <hkern u1="&#x201c;" u2="&#x133;" k="19" />
    <hkern u1="&#x201c;" u2="&#x131;" k="19" />
    <hkern u1="&#x201c;" u2="&#x12f;" k="19" />
    <hkern u1="&#x201c;" u2="&#x12d;" k="19" />
    <hkern u1="&#x201c;" u2="&#x12b;" k="19" />
    <hkern u1="&#x201c;" u2="&#x129;" k="19" />
    <hkern u1="&#x201c;" u2="&#x127;" k="-10" />
    <hkern u1="&#x201c;" u2="&#xef;" k="19" />
    <hkern u1="&#x201c;" u2="&#xee;" k="19" />
    <hkern u1="&#x201c;" u2="&#xed;" k="19" />
    <hkern u1="&#x201c;" u2="&#xec;" k="19" />
    <hkern u1="&#x201c;" u2="i" k="19" />
    <hkern u1="&#x201d;" u2="&#x1ecb;" k="46" />
    <hkern u1="&#x201d;" u2="&#x1ec9;" k="46" />
    <hkern u1="&#x201d;" u2="&#x133;" k="46" />
    <hkern u1="&#x201d;" u2="&#x131;" k="46" />
    <hkern u1="&#x201d;" u2="&#x12f;" k="46" />
    <hkern u1="&#x201d;" u2="&#x12d;" k="46" />
    <hkern u1="&#x201d;" u2="&#x12b;" k="46" />
    <hkern u1="&#x201d;" u2="&#x129;" k="46" />
    <hkern u1="&#x201d;" u2="&#xef;" k="46" />
    <hkern u1="&#x201d;" u2="&#xee;" k="46" />
    <hkern u1="&#x201d;" u2="&#xed;" k="46" />
    <hkern u1="&#x201d;" u2="&#xec;" k="46" />
    <hkern u1="&#x201d;" u2="i" k="46" />
    <hkern u1="&#xf63a;" u2="&#xf63c;" k="28" />
    <hkern u1="&#xf63a;" u2="&#xf63a;" k="-9" />
    <hkern u1="&#xf63b;" u2="&#xf641;" k="4" />
    <hkern u1="&#xf63b;" u2="&#xf63e;" k="5" />
    <hkern u1="&#xf63d;" u2="&#xf641;" k="11" />
    <hkern u1="&#xf63e;" u2="&#xf641;" k="15" />
    <hkern u1="&#xf63f;" u2="&#xf6dc;" k="-16" />
    <hkern u1="&#xf63f;" u2="&#xf63f;" k="-40" />
    <hkern u1="&#xf63f;" u2="&#xf63e;" k="14" />
    <hkern u1="&#xf63f;" u2="&#xf63c;" k="45" />
    <hkern u1="&#xf63f;" u2="&#xf63b;" k="-3" />
    <hkern u1="&#xf63f;" u2="&#xf63a;" k="-8" />
    <hkern u1="&#xf640;" u2="&#xf641;" k="6" />
    <hkern u1="&#xf641;" u2="&#xf63c;" k="6" />
    <hkern u1="&#xf6c1;" u2="&#x1fd;" k="-6" />
    <hkern u1="&#xf6c1;" u2="&#xe6;" k="-6" />
    <hkern u1="&#xf6dc;" u2="&#xf641;" k="8" />
    <hkern u1="&#xf6dc;" u2="&#xf63c;" k="7" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="J,Jcircumflex"
	k="-5" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="X,Chi"
	k="9" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-15" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="-16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="j,jcircumflex"
	k="-13" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="-9" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-4" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="-25" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-16" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="8" />
    <hkern g1="c,ccedilla,cacute,ccircumflex,cdotaccent,ccaron"
	g2="hyphen,uni00AD,endash,emdash"
	k="37" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="M,Mu,M_uni0302"
	k="8" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="X,Chi"
	k="-29" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="3" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="24" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="33" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="parenright,bracketright,braceright"
	k="-3" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,emacron,ebreve,edotaccent,eogonek,ecaron,oe,aeacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7"
	g2="comma,period,ellipsis"
	k="4" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="16" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	g2="X,Chi"
	k="54" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	g2="colon,semicolon"
	k="5" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	g2="four.denominator"
	k="11" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="7" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="-14" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="-13" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="7" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="-8" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="-11" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="-6" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="7" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="guillemotleft,guilsinglleft"
	k="-19" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="hyphen,uni00AD,endash,emdash"
	k="-9" />
    <hkern g1="h,m,n,ntilde,hcircumflex,hbar,nacute,ncommaaccent,ncaron,napostrophe,eng,m_uni0302,n_uni0302"
	g2="space,uni00A0"
	k="35" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="21" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="7" />
    <hkern g1="k,kcommaaccent,kgreenlandic"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="45" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="J,Jcircumflex"
	k="-15" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="M,Mu,M_uni0302"
	k="6" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="16" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="29" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="X,Chi"
	k="30" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="17" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="x"
	k="-28" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="colon,semicolon"
	k="8" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="parenright,bracketright,braceright"
	k="9" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="quoteleft,quotedblleft"
	k="-23" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="comma,period,ellipsis"
	k="-22" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="space,uni00A0"
	k="-8" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="quotesinglbase,quotedblbase"
	k="9" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	g2="four.denominator"
	k="-15" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,ohorn,oslashacute,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	g2="J,Jcircumflex"
	k="30" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,ohorn,oslashacute,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	g2="comma,period,ellipsis"
	k="14" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,omacron,obreve,ohungarumlaut,ohorn,oslashacute,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	g2="space,uni00A0"
	k="11" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="12" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="13" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="12" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="-16" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="j,jcircumflex"
	k="-14" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="6" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-26" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="-25" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="44" />
    <hkern g1="r,racute,rcommaaccent,rcaron"
	g2="parenright,bracketright,braceright"
	k="26" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-17" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="5" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="-19" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="25" />
    <hkern g1="s,germandbls,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	g2="quotesinglbase,quotedblbase"
	k="7" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-22" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="81" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-17" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="3" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="21" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="32" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="57" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="20" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="88" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="j,jcircumflex"
	k="-4" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="10" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="13" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="z,zacute,zdotaccent,zcaron"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="5" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="x"
	k="13" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="colon,semicolon"
	k="4" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="guillemotright,guilsinglright"
	k="15" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="hyphen,uni00AD,endash,emdash"
	k="27" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="parenright,bracketright,braceright"
	k="-7" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="quotesinglbase,quotedblbase"
	k="6" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="four.denominator"
	k="14" />
    <hkern g1="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	g2="seven.denominator"
	k="52" />
    <hkern g1="x"
	g2="J,Jcircumflex"
	k="5" />
    <hkern g1="x"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="25" />
    <hkern g1="x"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="3" />
    <hkern g1="x"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="-5" />
    <hkern g1="x"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-3" />
    <hkern g1="x"
	g2="x"
	k="-9" />
    <hkern g1="x"
	g2="hyphen,uni00AD,endash,emdash"
	k="-5" />
    <hkern g1="x"
	g2="parenright,bracketright,braceright"
	k="14" />
    <hkern g1="x"
	g2="quotesinglbase,quotedblbase"
	k="-20" />
    <hkern g1="x"
	g2="four.denominator"
	k="-7" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="M,Mu,M_uni0302"
	k="16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="16" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="X,Chi"
	k="27" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="-6" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="9" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-13" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="j,jcircumflex"
	k="-4" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="-13" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="x"
	k="-10" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="colon,semicolon"
	k="-20" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="parenright,bracketright,braceright"
	k="-8" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="four.denominator"
	k="3" />
    <hkern g1="z,zacute,zdotaccent,zcaron"
	g2="seven.denominator"
	k="7" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="77" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="j,jcircumflex"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="z,zacute,zdotaccent,zcaron"
	k="72" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="guillemotright,guilsinglright"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="parenright,bracketright,braceright"
	k="22" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="quotedbl,quotesingle"
	k="28" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="comma,period,ellipsis"
	k="31" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="space,uni00A0"
	k="21" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Amacron,Abreve,Aogonek,uni01FA,Alphatonos,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	g2="four.denominator"
	k="42" />
    <hkern g1="B,Beta"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="13" />
    <hkern g1="B,Beta"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-8" />
    <hkern g1="B,Beta"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="-6" />
    <hkern g1="B,Beta"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="-3" />
    <hkern g1="B,Beta"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="-6" />
    <hkern g1="B,Beta"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-11" />
    <hkern g1="B,Beta"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="-4" />
    <hkern g1="B,Beta"
	g2="x"
	k="-7" />
    <hkern g1="B,Beta"
	g2="space,uni00A0"
	k="3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-12" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="9" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-4" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="-5" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="9" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="29" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="-7" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="-13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="21" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="-14" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-3" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="z,zacute,zdotaccent,zcaron"
	k="6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="-6" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="x"
	k="-13" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quotedbl,quotesingle"
	k="97" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="comma,period,ellipsis"
	k="39" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="space,uni00A0"
	k="33" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="quotesinglbase,quotedblbase"
	k="62" />
    <hkern g1="C,Ccedilla,Cacute,Ccircumflex,Cdotaccent,Ccaron"
	g2="seven.denominator"
	k="87" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="J,Jcircumflex"
	k="32" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="26" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="29" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="99" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="119" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="100" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="hyphen,uni00AD,endash,emdash"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="space,uni00A0"
	k="-6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="quotesinglbase,quotedblbase"
	k="-9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="four.denominator"
	k="-5" />
    <hkern g1="F"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="3" />
    <hkern g1="F"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="F"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="27" />
    <hkern g1="F"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="3" />
    <hkern g1="F"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="30" />
    <hkern g1="F"
	g2="j,jcircumflex"
	k="33" />
    <hkern g1="F"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="15" />
    <hkern g1="F"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="-13" />
    <hkern g1="F"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-5" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="-3" />
    <hkern g1="F"
	g2="x"
	k="-3" />
    <hkern g1="F"
	g2="guillemotleft,guilsinglleft"
	k="-15" />
    <hkern g1="F"
	g2="hyphen,uni00AD,endash,emdash"
	k="-4" />
    <hkern g1="F"
	g2="parenright,bracketright,braceright"
	k="-12" />
    <hkern g1="F"
	g2="quotedbl,quotesingle"
	k="3" />
    <hkern g1="F"
	g2="quoteright,quotedblright"
	k="9" />
    <hkern g1="F"
	g2="quotesinglbase,quotedblbase"
	k="-8" />
    <hkern g1="F"
	g2="four.denominator"
	k="-13" />
    <hkern g1="F"
	g2="seven.denominator"
	k="13" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="M,Mu,M_uni0302"
	k="3" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="21" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="15" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="19" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="X,Chi"
	k="71" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="25" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="3" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="12" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="25" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="j,jcircumflex"
	k="21" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="-9" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="12" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="-6" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="21" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="z,zacute,zdotaccent,zcaron"
	k="11" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="x"
	k="20" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="colon,semicolon"
	k="15" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="guillemotright,guilsinglright"
	k="3" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="hyphen,uni00AD,endash,emdash"
	k="-12" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="parenright,bracketright,braceright"
	k="-24" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="quotedbl,quotesingle"
	k="142" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="four.denominator"
	k="-5" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="seven.denominator"
	k="-4" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-9" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="M,Mu,M_uni0302"
	k="-10" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="-3" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-14" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="-8" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="-10" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="-8" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="colon,semicolon"
	k="-29" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="guillemotright,guilsinglright"
	k="49" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="guillemotleft,guilsinglleft"
	k="3" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="hyphen,uni00AD,endash,emdash"
	k="28" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="quotedbl,quotesingle"
	k="-34" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="quoteleft,quotedblleft"
	k="-20" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="quoteright,quotedblright"
	k="-16" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="space,uni00A0"
	k="73" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="quotesinglbase,quotedblbase"
	k="8" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="31" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="J,Jcircumflex"
	k="39" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="M,Mu,M_uni0302"
	k="53" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="56" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-44" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-13" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="-25" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="73" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="32" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="guillemotright,guilsinglright"
	k="-6" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="quotesinglbase,quotedblbase"
	k="4" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="four.denominator"
	k="7" />
    <hkern g1="L,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash"
	g2="seven.denominator"
	k="5" />
    <hkern g1="M,Mu,M_uni0302"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="-27" />
    <hkern g1="M,Mu,M_uni0302"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="32" />
    <hkern g1="M,Mu,M_uni0302"
	g2="X,Chi"
	k="5" />
    <hkern g1="M,Mu,M_uni0302"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-10" />
    <hkern g1="M,Mu,M_uni0302"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="55" />
    <hkern g1="M,Mu,M_uni0302"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="4" />
    <hkern g1="M,Mu,M_uni0302"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="9" />
    <hkern g1="M,Mu,M_uni0302"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="7" />
    <hkern g1="M,Mu,M_uni0302"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="22" />
    <hkern g1="M,Mu,M_uni0302"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="38" />
    <hkern g1="M,Mu,M_uni0302"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="33" />
    <hkern g1="M,Mu,M_uni0302"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="25" />
    <hkern g1="M,Mu,M_uni0302"
	g2="x"
	k="10" />
    <hkern g1="M,Mu,M_uni0302"
	g2="guillemotright,guilsinglright"
	k="38" />
    <hkern g1="M,Mu,M_uni0302"
	g2="hyphen,uni00AD,endash,emdash"
	k="20" />
    <hkern g1="M,Mu,M_uni0302"
	g2="parenright,bracketright,braceright"
	k="18" />
    <hkern g1="M,Mu,M_uni0302"
	g2="quotedbl,quotesingle"
	k="29" />
    <hkern g1="M,Mu,M_uni0302"
	g2="quoteleft,quotedblleft"
	k="20" />
    <hkern g1="M,Mu,M_uni0302"
	g2="quoteright,quotedblright"
	k="-41" />
    <hkern g1="M,Mu,M_uni0302"
	g2="comma,period,ellipsis"
	k="-15" />
    <hkern g1="M,Mu,M_uni0302"
	g2="space,uni00A0"
	k="-4" />
    <hkern g1="M,Mu,M_uni0302"
	g2="quotesinglbase,quotedblbase"
	k="-18" />
    <hkern g1="M,Mu,M_uni0302"
	g2="four.denominator"
	k="63" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="J,Jcircumflex"
	k="11" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="3" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="10" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="29" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="j,jcircumflex"
	k="8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="17" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="z,zacute,zdotaccent,zcaron"
	k="-3" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="9" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quoteleft,quotedblleft"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="quoteright,quotedblright"
	k="62" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="comma,period,ellipsis"
	k="16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="space,uni00A0"
	k="42" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="four.denominator"
	k="-23" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="seven.denominator"
	k="13" />
    <hkern g1="P,Rho"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="33" />
    <hkern g1="P,Rho"
	g2="J,Jcircumflex"
	k="64" />
    <hkern g1="P,Rho"
	g2="M,Mu,M_uni0302"
	k="42" />
    <hkern g1="P,Rho"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="38" />
    <hkern g1="P,Rho"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="83" />
    <hkern g1="P,Rho"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="42" />
    <hkern g1="P,Rho"
	g2="X,Chi"
	k="42" />
    <hkern g1="P,Rho"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="33" />
    <hkern g1="P,Rho"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="61" />
    <hkern g1="P,Rho"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="57" />
    <hkern g1="P,Rho"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="-43" />
    <hkern g1="P,Rho"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="-8" />
    <hkern g1="P,Rho"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="9" />
    <hkern g1="P,Rho"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="-8" />
    <hkern g1="P,Rho"
	g2="b,h,k,l,hcircumflex,hbar,kcommaaccent,lacute,lcommaaccent,lcaron,ldot,lslash"
	k="108" />
    <hkern g1="P,Rho"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-16" />
    <hkern g1="P,Rho"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="22" />
    <hkern g1="P,Rho"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="P,Rho"
	g2="guillemotleft,guilsinglleft"
	k="-4" />
    <hkern g1="P,Rho"
	g2="parenright,bracketright,braceright"
	k="-5" />
    <hkern g1="P,Rho"
	g2="seven.denominator"
	k="11" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="-3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="colon,semicolon"
	k="-3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="guillemotright,guilsinglright"
	k="-6" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="parenright,bracketright,braceright"
	k="-3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quotedbl,quotesingle"
	k="3" />
    <hkern g1="R,Racute,Rcommaaccent,Rcaron"
	g2="quoteright,quotedblright"
	k="-8" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="-6" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="3" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="guillemotleft,guilsinglleft"
	k="-26" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="quoteleft,quotedblleft"
	k="9" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="J,Jcircumflex"
	k="3" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="M,Mu,M_uni0302"
	k="7" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="-6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="X,Chi"
	k="4" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="12" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="-6" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="j,jcircumflex"
	k="-9" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="8" />
    <hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="13" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="J,Jcircumflex"
	k="-30" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="M,Mu,M_uni0302"
	k="-11" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-86" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="X,Chi"
	k="-48" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="-45" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="-64" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="36" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="hyphen,uni00AD,endash,emdash"
	k="6" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="quoteright,quotedblright"
	k="6" />
    <hkern g1="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	g2="four.denominator"
	k="-7" />
    <hkern g1="X,Chi"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="38" />
    <hkern g1="X,Chi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="18" />
    <hkern g1="X,Chi"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="45" />
    <hkern g1="X,Chi"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="-4" />
    <hkern g1="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="60" />
    <hkern g1="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	g2="X,Chi"
	k="-7" />
    <hkern g1="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	g2="quoteright,quotedblright"
	k="53" />
    <hkern g1="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	g2="comma,period,ellipsis"
	k="12" />
    <hkern g1="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	g2="quotesinglbase,quotedblbase"
	k="-13" />
    <hkern g1="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	g2="seven.denominator"
	k="19" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="3" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-34" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="z,zacute,zdotaccent,zcaron"
	k="-13" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="x"
	k="18" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="guillemotright,guilsinglright"
	k="-37" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="guillemotleft,guilsinglleft"
	k="-8" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="hyphen,uni00AD,endash,emdash"
	k="-35" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="quotedbl,quotesingle"
	k="16" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="four.denominator"
	k="-52" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="quoteleft,quotedblleft"
	k="-5" />
    <hkern g1="J,IJ,Jcircumflex"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="u,ugrave,uacute,ucircumflex,udieresis,utilde,umacron,ubreve,uring,uhungarumlaut,uogonek,uhorn,uni1EE5,uni1EE7,uni1EE9,uni1EEB,uni1EED,uni1EEF,uni1EF1"
	k="-6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="11" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="guillemotleft,guilsinglleft"
	k="-13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="hyphen,uni00AD,endash,emdash"
	k="58" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="parenright,bracketright,braceright"
	k="3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="quotedbl,quotesingle"
	k="7" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="quoteright,quotedblright"
	k="-13" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="comma,period,ellipsis"
	k="-3" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="space,uni00A0"
	k="-6" />
    <hkern g1="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	g2="four.denominator"
	k="53" />
    <hkern g1="f,f_f"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="119" />
    <hkern g1="f,f_f"
	g2="i,m,n,p,r,igrave,iacute,icircumflex,idieresis,ntilde,itilde,imacron,ibreve,iogonek,dotlessi,ij,kgreenlandic,nacute,ncommaaccent,ncaron,eng,racute,rcommaaccent,rcaron,uni1EC9,uni1ECB,m_uni0302,n_uni0302"
	k="-28" />
    <hkern g1="f,f_f"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="84" />
    <hkern g1="f,f_f"
	g2="s,sacute,scircumflex,scedilla,scaron,scommaaccent,scedilla.dup"
	k="5" />
    <hkern g1="f,f_f"
	g2="t,tcommaaccent,tcaron,tbar,uni021B"
	k="29" />
    <hkern g1="f,f_f"
	g2="v,w,y,yacute,ydieresis,wcircumflex,uni0233,wgrave,wacute,wdieresis,ygrave,uni1EF5,uni1EF7,uni1EF9"
	k="-19" />
    <hkern g1="f,f_f"
	g2="z,zacute,zdotaccent,zcaron"
	k="-5" />
    <hkern g1="f,f_f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,amacron,abreve,aogonek,uni01FB,aeacute,uni1EA1,uni1EA3,uni1EA5,uni1EA7,uni1EA9,uni1EAB,uni1EAD,uni1EAF,uni1EB1,uni1EB3,uni1EB5,uni1EB7"
	k="-16" />
    <hkern g1="f,f_f"
	g2="colon,semicolon"
	k="70" />
    <hkern g1="f,f_f"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="f,f_f"
	g2="hyphen,uni00AD,endash,emdash"
	k="8" />
    <hkern g1="f,f_f"
	g2="parenright,bracketright,braceright"
	k="63" />
    <hkern g1="f,f_f"
	g2="quotedbl,quotesingle"
	k="-5" />
    <hkern g1="f,f_f"
	g2="quoteright,quotedblright"
	k="24" />
    <hkern g1="f,f_f"
	g2="comma,period,ellipsis"
	k="65" />
    <hkern g1="f,f_f"
	g2="space,uni00A0"
	k="37" />
    <hkern g1="f,f_f"
	g2="quotesinglbase,quotedblbase"
	k="-3" />
    <hkern g1="f,f_f"
	g2="four.denominator"
	k="29" />
    <hkern g1="t,tcommaaccent,tcaron,tbar,uni021B"
	g2="seven.denominator"
	k="-6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-3" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="J,Jcircumflex"
	k="-16" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="88" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="13" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="57" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="S,Sacute,Scircumflex,Scedilla,Scaron,Scommaaccent,Scedilla.dup"
	k="46" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="86" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="f,germandbls,f_f_j,f_j,f_f,f_i,f_l,f_f_i,f_f_l"
	k="83" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="g,gcircumflex,gbreve,gdotaccent,gcommaaccent,g_tildecomb"
	k="17" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="j,jcircumflex"
	k="14" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,ccircumflex,cdotaccent,ccaron,dcaron,dcroat,emacron,ebreve,edotaccent,eogonek,ecaron,omacron,obreve,ohungarumlaut,oe,ohorn,oslashacute,uni1EB9,uni1EBB,uni1EBD,uni1EBF,uni1EC1,uni1EC3,uni1EC5,uni1EC7,uni1ECD,uni1ECF,uni1ED1,uni1ED3,uni1ED5,uni1ED7,uni1ED9,uni1EDB,uni1EDD,uni1EDF,uni1EE1,uni1EE3"
	k="27" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="guillemotright,guilsinglright"
	k="62" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="guillemotleft,guilsinglleft"
	k="178" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="hyphen,uni00AD,endash,emdash"
	k="76" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="comma,period,ellipsis"
	k="85" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="space,uni00A0"
	k="-6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis,Utilde,Umacron,Ubreve,Uring,Uhungarumlaut,Uogonek,Uhorn,uni1EE4,uni1EE6,uni1EE8,uni1EEA,uni1EEC,uni1EEE,uni1EF0"
	k="23" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-8" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X,Chi"
	k="-3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="z,zacute,zdotaccent,zcaron"
	k="40" />
    <hkern g1="guillemotright,guilsinglright"
	g2="guillemotleft,guilsinglleft"
	k="36" />
    <hkern g1="guillemotright,guilsinglright"
	g2="parenright,bracketright,braceright"
	k="43" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="79" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Y,Yacute,Ydieresis,uni0232,Ygrave,uni1EF4,uni1EF6,uni1EF8"
	k="103" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="V,W,Wcircumflex,Wgrave,Wacute,Wdieresis"
	k="-31217" />
    <hkern g1="B,Beta"
	g2="Psi"
	k="11" />
    <hkern g1="B,Beta"
	g2="Upsilon,Upsilondieresis"
	k="15" />
    <hkern g1="B,Beta"
	g2="epsilontonos,epsilon"
	k="-5" />
    <hkern g1="B,Beta"
	g2="gamma,nu"
	k="-6" />
    <hkern g1="B,Beta"
	g2="rho"
	k="4" />
    <hkern g1="uni0394"
	g2="uni0394"
	k="-13" />
    <hkern g1="uni0394"
	g2="Phi"
	k="14" />
    <hkern g1="uni0394"
	g2="Psi"
	k="64" />
    <hkern g1="uni0394"
	g2="Upsilon,Upsilondieresis"
	k="88" />
    <hkern g1="uni0394"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="-11" />
    <hkern g1="uni0394"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="11" />
    <hkern g1="uni0394"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="76" />
    <hkern g1="uni0394"
	g2="X,Chi"
	k="-3" />
    <hkern g1="uni0394"
	g2="chi"
	k="24" />
    <hkern g1="uni0394"
	g2="epsilontonos,epsilon"
	k="-21" />
    <hkern g1="uni0394"
	g2="mu,etatonos,beta,eta,kappa"
	k="-5" />
    <hkern g1="uni0394"
	g2="gamma,nu"
	k="20" />
    <hkern g1="uni0394"
	g2="lambda"
	k="-19" />
    <hkern g1="uni0394"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="-3" />
    <hkern g1="uni0394"
	g2="rho"
	k="-6" />
    <hkern g1="uni0394"
	g2="tau"
	k="12" />
    <hkern g1="uni0394"
	g2="zeta,xi"
	k="-10" />
    <hkern g1="uni0394"
	g2="pi"
	k="-15" />
    <hkern g1="uni0394"
	g2="colon,semicolon"
	k="-9" />
    <hkern g1="uni0394"
	g2="guillemotright,guilsinglright"
	k="-4" />
    <hkern g1="uni0394"
	g2="parenright,bracketright,braceright"
	k="25" />
    <hkern g1="uni0394"
	g2="comma,period,ellipsis"
	k="-9" />
    <hkern g1="uni0394"
	g2="quotedbl,quotesingle"
	k="75" />
    <hkern g1="uni0394"
	g2="quoteright,quotedblright"
	k="56" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="uni0394"
	k="-14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="Psi"
	k="4" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="Xi"
	k="-5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="epsilontonos,epsilon"
	k="-16" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="lambda"
	k="-6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="tau"
	k="-9" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,OE,AEacute,Epsilontonos,Epsilon,Xi,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6"
	g2="pi"
	k="-14" />
    <hkern g1="Gamma"
	g2="uni0394"
	k="100" />
    <hkern g1="Gamma"
	g2="uni03A9"
	k="24" />
    <hkern g1="Gamma"
	g2="Phi"
	k="29" />
    <hkern g1="Gamma"
	g2="Psi"
	k="-14" />
    <hkern g1="Gamma"
	g2="Upsilon,Upsilondieresis"
	k="-18" />
    <hkern g1="Gamma"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="106" />
    <hkern g1="Gamma"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="26" />
    <hkern g1="Gamma"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-16" />
    <hkern g1="Gamma"
	g2="X,Chi"
	k="-7" />
    <hkern g1="Gamma"
	g2="Xi"
	k="-6" />
    <hkern g1="Gamma"
	g2="M,Mu,M_uni0302"
	k="10" />
    <hkern g1="Gamma"
	g2="Sigma"
	k="-11" />
    <hkern g1="Gamma"
	g2="epsilontonos,epsilon"
	k="73" />
    <hkern g1="Gamma"
	g2="mu,etatonos,beta,eta,kappa"
	k="49" />
    <hkern g1="Gamma"
	g2="gamma,nu"
	k="30" />
    <hkern g1="Gamma"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="49" />
    <hkern g1="Gamma"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="76" />
    <hkern g1="Gamma"
	g2="rho"
	k="104" />
    <hkern g1="Gamma"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="43" />
    <hkern g1="Gamma"
	g2="colon,semicolon"
	k="50" />
    <hkern g1="Gamma"
	g2="guillemotright,guilsinglright"
	k="56" />
    <hkern g1="Gamma"
	g2="hyphen,uni00AD,endash,emdash"
	k="70" />
    <hkern g1="Gamma"
	g2="parenright,bracketright,braceright"
	k="-10" />
    <hkern g1="Gamma"
	g2="comma,period,ellipsis"
	k="120" />
    <hkern g1="Gamma"
	g2="quotedbl,quotesingle"
	k="-16" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="uni0394"
	k="-16" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="uni03A9"
	k="-9" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Phi"
	k="34" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Xi"
	k="-21" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="Sigma"
	k="-17" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="epsilontonos,epsilon"
	k="-12" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="mu,etatonos,beta,eta,kappa"
	k="-3" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="gamma,nu"
	k="21" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="lambda"
	k="-13" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="10" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="rho"
	k="4" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="tau"
	k="22" />
    <hkern g1="K,Kcommaaccent,Kappa"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="23" />
    <hkern g1="M,Mu,M_uni0302"
	g2="Psi"
	k="10" />
    <hkern g1="M,Mu,M_uni0302"
	g2="Upsilon,Upsilondieresis"
	k="20" />
    <hkern g1="M,Mu,M_uni0302"
	g2="mu,etatonos,beta,eta,kappa"
	k="-4" />
    <hkern g1="M,Mu,M_uni0302"
	g2="pi"
	k="-10" />
    <hkern g1="Omegatonos,uni03A9"
	g2="uni03A9"
	k="-6" />
    <hkern g1="Omegatonos,uni03A9"
	g2="Phi"
	k="-4" />
    <hkern g1="Omegatonos,uni03A9"
	g2="Psi"
	k="5" />
    <hkern g1="Omegatonos,uni03A9"
	g2="Upsilon,Upsilondieresis"
	k="24" />
    <hkern g1="Omegatonos,uni03A9"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="23" />
    <hkern g1="Omegatonos,uni03A9"
	g2="chi"
	k="-9" />
    <hkern g1="Omegatonos,uni03A9"
	g2="mu,etatonos,beta,eta,kappa"
	k="-5" />
    <hkern g1="Omegatonos,uni03A9"
	g2="gamma,nu"
	k="-16" />
    <hkern g1="Omegatonos,uni03A9"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-3" />
    <hkern g1="Omegatonos,uni03A9"
	g2="lambda"
	k="-16" />
    <hkern g1="Omegatonos,uni03A9"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="-6" />
    <hkern g1="Omegatonos,uni03A9"
	g2="tau"
	k="-18" />
    <hkern g1="Omegatonos,uni03A9"
	g2="theta"
	k="-16" />
    <hkern g1="Omegatonos,uni03A9"
	g2="zeta,xi"
	k="-8" />
    <hkern g1="Omegatonos,uni03A9"
	g2="pi"
	k="-25" />
    <hkern g1="Omegatonos,uni03A9"
	g2="colon,semicolon"
	k="-7" />
    <hkern g1="Omegatonos,uni03A9"
	g2="guillemotright,guilsinglright"
	k="-3" />
    <hkern g1="Omegatonos,uni03A9"
	g2="hyphen,uni00AD,endash,emdash"
	k="-7" />
    <hkern g1="Omegatonos,uni03A9"
	g2="comma,period,ellipsis"
	k="-7" />
    <hkern g1="Omegatonos,uni03A9"
	g2="quotedbl,quotesingle"
	k="20" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="uni0394"
	k="15" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="uni03A9"
	k="-4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Upsilon,Upsilondieresis"
	k="30" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Xi"
	k="8" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="Sigma"
	k="18" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="epsilontonos,epsilon"
	k="4" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="gamma,nu"
	k="-13" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="lambda"
	k="21" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="rho"
	k="14" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="tau"
	k="-11" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="theta"
	k="-16" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="zeta,xi"
	k="-9" />
    <hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Dcaron,Dcroat,Omacron,Obreve,Ohungarumlaut,Ohorn,Oslashacute,Omicrontonos,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2"
	g2="pi"
	k="-13" />
    <hkern g1="P,Rho"
	g2="uni0394"
	k="70" />
    <hkern g1="P,Rho"
	g2="Psi"
	k="-6" />
    <hkern g1="P,Rho"
	g2="Upsilon,Upsilondieresis"
	k="3" />
    <hkern g1="P,Rho"
	g2="epsilontonos,epsilon"
	k="24" />
    <hkern g1="P,Rho"
	g2="mu,etatonos,beta,eta,kappa"
	k="6" />
    <hkern g1="P,Rho"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="6" />
    <hkern g1="P,Rho"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="24" />
    <hkern g1="P,Rho"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="8" />
    <hkern g1="Phi"
	g2="uni0394"
	k="19" />
    <hkern g1="Phi"
	g2="uni03A9"
	k="-3" />
    <hkern g1="Phi"
	g2="Upsilon,Upsilondieresis"
	k="40" />
    <hkern g1="Phi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="29" />
    <hkern g1="Phi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="26" />
    <hkern g1="Phi"
	g2="X,Chi"
	k="39" />
    <hkern g1="Phi"
	g2="Xi"
	k="8" />
    <hkern g1="Phi"
	g2="Sigma"
	k="29" />
    <hkern g1="Phi"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="18" />
    <hkern g1="Phi"
	g2="lambda"
	k="27" />
    <hkern g1="Phi"
	g2="rho"
	k="17" />
    <hkern g1="Phi"
	g2="tau"
	k="-8" />
    <hkern g1="Phi"
	g2="theta"
	k="-16" />
    <hkern g1="Phi"
	g2="zeta,xi"
	k="-10" />
    <hkern g1="Phi"
	g2="hyphen,uni00AD,endash,emdash"
	k="-14" />
    <hkern g1="Phi"
	g2="parenright,bracketright,braceright"
	k="10" />
    <hkern g1="Phi"
	g2="comma,period,ellipsis"
	k="54" />
    <hkern g1="Phi"
	g2="quotedbl,quotesingle"
	k="16" />
    <hkern g1="Phi"
	g2="quoteright,quotedblright"
	k="-3" />
    <hkern g1="Psi"
	g2="uni0394"
	k="68" />
    <hkern g1="Psi"
	g2="uni03A9"
	k="11" />
    <hkern g1="Psi"
	g2="Phi"
	k="6" />
    <hkern g1="Psi"
	g2="Upsilon,Upsilondieresis"
	k="-6" />
    <hkern g1="Psi"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="73" />
    <hkern g1="Psi"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="3" />
    <hkern g1="Psi"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-6" />
    <hkern g1="Psi"
	g2="X,Chi"
	k="11" />
    <hkern g1="Psi"
	g2="M,Mu,M_uni0302"
	k="22" />
    <hkern g1="Psi"
	g2="Sigma"
	k="6" />
    <hkern g1="Psi"
	g2="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	k="11" />
    <hkern g1="Psi"
	g2="B,D,E,F,H,I,K,L,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Dcaron,Dcroat,Emacron,Ebreve,Edotaccent,Eogonek,Ecaron,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,IJ,Kcommaaccent,Lacute,Lcommaaccent,Lcaron,Ldot,Lslash,Nacute,Ncommaaccent,Ncaron,Eng,Racute,Rcommaaccent,Rcaron,Beta,Gamma,Epsilon,Eta,Iota,Kappa,Nu,Pi,Rho,Iotadieresis,uni1EB8,uni1EBA,uni1EBC,uni1EBE,uni1EC0,uni1EC2,uni1EC4,uni1EC6,uni1EC8,uni1ECA,N_uni0302"
	k="4" />
    <hkern g1="Psi"
	g2="epsilontonos,epsilon"
	k="24" />
    <hkern g1="Psi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="23" />
    <hkern g1="Psi"
	g2="rho"
	k="41" />
    <hkern g1="Psi"
	g2="omega,omegatonos"
	k="12" />
    <hkern g1="Psi"
	g2="guillemotright,guilsinglright"
	k="3" />
    <hkern g1="Psi"
	g2="hyphen,uni00AD,endash,emdash"
	k="16" />
    <hkern g1="Psi"
	g2="comma,period,ellipsis"
	k="94" />
    <hkern g1="Psi"
	g2="quoteright,quotedblright"
	k="-3" />
    <hkern g1="Sigma"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="37" />
    <hkern g1="Sigma"
	g2="chi"
	k="6" />
    <hkern g1="Sigma"
	g2="gamma,nu"
	k="18" />
    <hkern g1="Sigma"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="5" />
    <hkern g1="Sigma"
	g2="lambda"
	k="-5" />
    <hkern g1="Sigma"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="16" />
    <hkern g1="Sigma"
	g2="rho"
	k="9" />
    <hkern g1="Sigma"
	g2="tau"
	k="15" />
    <hkern g1="Sigma"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="9" />
    <hkern g1="Sigma"
	g2="colon,semicolon"
	k="-7" />
    <hkern g1="Sigma"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="Sigma"
	g2="hyphen,uni00AD,endash,emdash"
	k="34" />
    <hkern g1="Sigma"
	g2="comma,period,ellipsis"
	k="-7" />
    <hkern g1="Sigma"
	g2="quotedbl,quotesingle"
	k="9" />
    <hkern g1="Sigma"
	g2="quoteright,quotedblright"
	k="36" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="uni0394"
	k="72" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="uni03A9"
	k="29" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="Phi"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="Psi"
	k="-6" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="Upsilon,Upsilondieresis"
	k="-10" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="Sigma"
	k="-3" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="epsilontonos,epsilon"
	k="56" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="mu,etatonos,beta,eta,kappa"
	k="36" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="36" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="lambda"
	k="3" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="70" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="rho"
	k="80" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="43" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="zeta,xi"
	k="17" />
    <hkern g1="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	g2="omega,omegatonos"
	k="50" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="uni0394"
	k="90" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="uni03A9"
	k="19" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Phi"
	k="29" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Psi"
	k="-8" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="Upsilon,Upsilondieresis"
	k="-6" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,AE,Amacron,Abreve,Aogonek,uni01FA,AEacute,Alpha,Lambda,uni1EA0,uni1EA2,uni1EA4,uni1EA6,uni1EA8,uni1EAA,uni1EAC,uni1EAE,uni1EB0,uni1EB2,uni1EB4,uni1EB6"
	k="99" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,Cacute,Ccircumflex,Cdotaccent,Ccaron,Gcircumflex,Gbreve,Gdotaccent,Gcommaaccent,Omacron,Obreve,Ohungarumlaut,OE,Ohorn,Oslashacute,Theta,Omicron,uni1ECC,uni1ECE,uni1ED0,uni1ED2,uni1ED4,uni1ED6,uni1ED8,uni1EDA,uni1EDC,uni1EDE,uni1EE0,uni1EE2,G_tildecomb"
	k="30" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="T,Tcommaaccent,Tcaron,Tbar,uni021A,Tau"
	k="-10" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="X,Chi"
	k="9" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="M,Mu,M_uni0302"
	k="20" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="epsilontonos,epsilon"
	k="62" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="mu,etatonos,beta,eta,kappa"
	k="32" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="gamma,nu"
	k="12" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="30" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="lambda"
	k="9" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="79" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="rho"
	k="91" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="27" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="pi"
	k="9" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="colon,semicolon"
	k="38" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="guillemotright,guilsinglright"
	k="30" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="hyphen,uni00AD,endash,emdash"
	k="57" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="parenright,bracketright,braceright"
	k="-7" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="comma,period,ellipsis"
	k="100" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="quotedbl,quotesingle"
	k="-3" />
    <hkern g1="Upsilontonos,Upsilon,Upsilondieresis"
	g2="quoteright,quotedblright"
	k="-3" />
    <hkern g1="X,Chi"
	g2="Phi"
	k="45" />
    <hkern g1="X,Chi"
	g2="Psi"
	k="13" />
    <hkern g1="X,Chi"
	g2="Upsilon,Upsilondieresis"
	k="17" />
    <hkern g1="X,Chi"
	g2="Xi"
	k="-11" />
    <hkern g1="X,Chi"
	g2="Sigma"
	k="3" />
    <hkern g1="X,Chi"
	g2="gamma,nu"
	k="32" />
    <hkern g1="X,Chi"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="13" />
    <hkern g1="X,Chi"
	g2="lambda"
	k="-11" />
    <hkern g1="X,Chi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="22" />
    <hkern g1="X,Chi"
	g2="rho"
	k="11" />
    <hkern g1="X,Chi"
	g2="tau"
	k="21" />
    <hkern g1="X,Chi"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="20" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="uni0394"
	k="-3" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="Upsilon,Upsilondieresis"
	k="-9" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="Xi"
	k="-7" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="Sigma"
	k="7" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="lambda"
	k="-10" />
    <hkern g1="Z,Zacute,Zdotaccent,Zcaron,Zeta"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="10" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="3" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="tau"
	k="-3" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="zeta,xi"
	k="-5" />
    <hkern g1="H,I,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Hcircumflex,Hbar,Itilde,Imacron,Ibreve,Iogonek,Idotaccent,Nacute,Ncommaaccent,Ncaron,Eng,Etatonos,Iotatonos,Eta,Iota,Nu,Pi,Iotadieresis,uni1EC8,uni1ECA,N_uni0302"
	g2="pi"
	k="-8" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="epsilontonos,epsilon"
	k="-4" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="mu,etatonos,beta,eta,kappa"
	k="-3" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="lambda"
	k="-8" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="rho"
	k="6" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="theta"
	k="-4" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="zeta,xi"
	k="-9" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="pi"
	k="-10" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="colon,semicolon"
	k="-5" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="guillemotright,guilsinglright"
	k="-3" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="hyphen,uni00AD,endash,emdash"
	k="-9" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="comma,period,ellipsis"
	k="-5" />
    <hkern g1="mu,iotadieresistonos,alphatonos,iotatonos,alpha,iota,iotadieresis"
	g2="quoteright,quotedblright"
	k="-3" />
    <hkern g1="beta"
	g2="gamma,nu"
	k="10" />
    <hkern g1="beta"
	g2="rho"
	k="10" />
    <hkern g1="beta"
	g2="zeta,xi"
	k="-4" />
    <hkern g1="beta"
	g2="colon,semicolon"
	k="3" />
    <hkern g1="beta"
	g2="hyphen,uni00AD,endash,emdash"
	k="-9" />
    <hkern g1="beta"
	g2="parenright,bracketright,braceright"
	k="14" />
    <hkern g1="beta"
	g2="comma,period,ellipsis"
	k="22" />
    <hkern g1="beta"
	g2="quotedbl,quotesingle"
	k="31" />
    <hkern g1="beta"
	g2="quoteright,quotedblright"
	k="24" />
    <hkern g1="chi"
	g2="epsilontonos,epsilon"
	k="13" />
    <hkern g1="chi"
	g2="mu,etatonos,beta,eta,kappa"
	k="-5" />
    <hkern g1="chi"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="4" />
    <hkern g1="chi"
	g2="lambda"
	k="22" />
    <hkern g1="chi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="23" />
    <hkern g1="chi"
	g2="rho"
	k="10" />
    <hkern g1="chi"
	g2="tau"
	k="-17" />
    <hkern g1="chi"
	g2="pi"
	k="-13" />
    <hkern g1="chi"
	g2="guillemotright,guilsinglright"
	k="-3" />
    <hkern g1="chi"
	g2="parenright,bracketright,braceright"
	k="13" />
    <hkern g1="chi"
	g2="comma,period,ellipsis"
	k="23" />
    <hkern g1="chi"
	g2="quoteright,quotedblright"
	k="-12" />
    <hkern g1="epsilontonos,epsilon"
	g2="gamma,nu"
	k="-4" />
    <hkern g1="epsilontonos,epsilon"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="5" />
    <hkern g1="epsilontonos,epsilon"
	g2="lambda"
	k="-4" />
    <hkern g1="epsilontonos,epsilon"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="4" />
    <hkern g1="epsilontonos,epsilon"
	g2="rho"
	k="4" />
    <hkern g1="epsilontonos,epsilon"
	g2="pi"
	k="-9" />
    <hkern g1="epsilontonos,epsilon"
	g2="guillemotright,guilsinglright"
	k="-7" />
    <hkern g1="etatonos,eta"
	g2="gamma,nu"
	k="14" />
    <hkern g1="etatonos,eta"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="3" />
    <hkern g1="etatonos,eta"
	g2="rho"
	k="4" />
    <hkern g1="etatonos,eta"
	g2="tau"
	k="3" />
    <hkern g1="etatonos,eta"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="5" />
    <hkern g1="etatonos,eta"
	g2="quoteright,quotedblright"
	k="29" />
    <hkern g1="gamma,nu"
	g2="epsilontonos,epsilon"
	k="8" />
    <hkern g1="gamma,nu"
	g2="gamma,nu"
	k="-10" />
    <hkern g1="gamma,nu"
	g2="lambda"
	k="17" />
    <hkern g1="gamma,nu"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="3" />
    <hkern g1="gamma,nu"
	g2="rho"
	k="24" />
    <hkern g1="gamma,nu"
	g2="tau"
	k="-19" />
    <hkern g1="gamma,nu"
	g2="theta"
	k="-22" />
    <hkern g1="gamma,nu"
	g2="zeta,xi"
	k="-3" />
    <hkern g1="gamma,nu"
	g2="pi"
	k="-18" />
    <hkern g1="gamma,nu"
	g2="guillemotright,guilsinglright"
	k="-7" />
    <hkern g1="gamma,nu"
	g2="hyphen,uni00AD,endash,emdash"
	k="-7" />
    <hkern g1="gamma,nu"
	g2="parenright,bracketright,braceright"
	k="3" />
    <hkern g1="gamma,nu"
	g2="comma,period,ellipsis"
	k="52" />
    <hkern g1="gamma,nu"
	g2="quotedbl,quotesingle"
	k="-8" />
    <hkern g1="gamma,nu"
	g2="quoteright,quotedblright"
	k="-11" />
    <hkern g1="kappa"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="18" />
    <hkern g1="kappa"
	g2="rho"
	k="9" />
    <hkern g1="kappa"
	g2="zeta,xi"
	k="10" />
    <hkern g1="kappa"
	g2="pi"
	k="-5" />
    <hkern g1="kappa"
	g2="colon,semicolon"
	k="-7" />
    <hkern g1="kappa"
	g2="guillemotright,guilsinglright"
	k="-7" />
    <hkern g1="kappa"
	g2="hyphen,uni00AD,endash,emdash"
	k="29" />
    <hkern g1="kappa"
	g2="comma,period,ellipsis"
	k="-7" />
    <hkern g1="kappa"
	g2="quoteright,quotedblright"
	k="-6" />
    <hkern g1="lambda"
	g2="chi"
	k="32" />
    <hkern g1="lambda"
	g2="epsilontonos,epsilon"
	k="-5" />
    <hkern g1="lambda"
	g2="mu,etatonos,beta,eta,kappa"
	k="3" />
    <hkern g1="lambda"
	g2="gamma,nu"
	k="38" />
    <hkern g1="lambda"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="8" />
    <hkern g1="lambda"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="5" />
    <hkern g1="lambda"
	g2="tau"
	k="32" />
    <hkern g1="lambda"
	g2="upsilondieresistonos,upsilon,psi,upsilondieresis,upsilontonos"
	k="11" />
    <hkern g1="lambda"
	g2="pi"
	k="3" />
    <hkern g1="lambda"
	g2="parenright,bracketright,braceright"
	k="9" />
    <hkern g1="lambda"
	g2="quotedbl,quotesingle"
	k="78" />
    <hkern g1="lambda"
	g2="quoteright,quotedblright"
	k="57" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="-4" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="hyphen,uni00AD,endash,emdash"
	k="-10" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="parenright,bracketright,braceright"
	k="27" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="comma,period,ellipsis"
	k="25" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="quotedbl,quotesingle"
	k="41" />
    <hkern g1="upsilondieresistonos,upsilon,psi,omega,upsilondieresis,upsilontonos,omegatonos"
	g2="quoteright,quotedblright"
	k="6" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="chi"
	k="23" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="gamma,nu"
	k="9" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="lambda"
	k="9" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="rho"
	k="9" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="tau"
	k="5" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="zeta,xi"
	k="-8" />
    <hkern g1="delta,omicron,rho,phi,omicrontonos"
	g2="omega,omegatonos"
	k="-4" />
    <hkern g1="pi"
	g2="gamma,nu"
	k="-9" />
    <hkern g1="pi"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="8" />
    <hkern g1="pi"
	g2="lambda"
	k="13" />
    <hkern g1="pi"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="5" />
    <hkern g1="pi"
	g2="rho"
	k="15" />
    <hkern g1="pi"
	g2="tau"
	k="-10" />
    <hkern g1="pi"
	g2="pi"
	k="-6" />
    <hkern g1="pi"
	g2="guillemotright,guilsinglright"
	k="-12" />
    <hkern g1="pi"
	g2="parenright,bracketright,braceright"
	k="23" />
    <hkern g1="pi"
	g2="comma,period,ellipsis"
	k="22" />
    <hkern g1="pi"
	g2="quotedbl,quotesingle"
	k="7" />
    <hkern g1="pi"
	g2="quoteright,quotedblright"
	k="-4" />
    <hkern g1="sigma"
	g2="chi"
	k="-5" />
    <hkern g1="sigma"
	g2="mu,etatonos,beta,eta,kappa"
	k="-5" />
    <hkern g1="sigma"
	g2="gamma,nu"
	k="-14" />
    <hkern g1="sigma"
	g2="iotadieresistonos,iotatonos,iota,iotadieresis"
	k="-5" />
    <hkern g1="sigma"
	g2="lambda"
	k="17" />
    <hkern g1="sigma"
	g2="rho"
	k="19" />
    <hkern g1="sigma"
	g2="tau"
	k="-21" />
    <hkern g1="sigma"
	g2="theta"
	k="-12" />
    <hkern g1="sigma"
	g2="zeta,xi"
	k="-9" />
    <hkern g1="sigma"
	g2="pi"
	k="-16" />
    <hkern g1="sigma"
	g2="guillemotright,guilsinglright"
	k="-17" />
    <hkern g1="sigma"
	g2="hyphen,uni00AD,endash,emdash"
	k="-12" />
    <hkern g1="sigma"
	g2="comma,period,ellipsis"
	k="33" />
    <hkern g1="sigma"
	g2="quotedbl,quotesingle"
	k="-3" />
    <hkern g1="sigma"
	g2="quoteright,quotedblright"
	k="-14" />
    <hkern g1="tau"
	g2="chi"
	k="-5" />
    <hkern g1="tau"
	g2="gamma,nu"
	k="-10" />
    <hkern g1="tau"
	g2="lambda"
	k="18" />
    <hkern g1="tau"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="11" />
    <hkern g1="tau"
	g2="rho"
	k="26" />
    <hkern g1="tau"
	g2="tau"
	k="-7" />
    <hkern g1="tau"
	g2="guillemotright,guilsinglright"
	k="-17" />
    <hkern g1="tau"
	g2="hyphen,uni00AD,endash,emdash"
	k="14" />
    <hkern g1="tau"
	g2="comma,period,ellipsis"
	k="28" />
    <hkern g1="tau"
	g2="quoteright,quotedblright"
	k="-19" />
    <hkern g1="theta"
	g2="lambda"
	k="9" />
    <hkern g1="theta"
	g2="zeta,xi"
	k="-14" />
    <hkern g1="theta"
	g2="hyphen,uni00AD,endash,emdash"
	k="-17" />
    <hkern g1="theta"
	g2="comma,period,ellipsis"
	k="31" />
    <hkern g1="theta"
	g2="quoteright,quotedblright"
	k="-3" />
    <hkern g1="xi"
	g2="epsilontonos,epsilon"
	k="-8" />
    <hkern g1="xi"
	g2="lambda"
	k="-33" />
    <hkern g1="xi"
	g2="pi"
	k="-16" />
    <hkern g1="xi"
	g2="colon,semicolon"
	k="-20" />
    <hkern g1="xi"
	g2="guillemotright,guilsinglright"
	k="-22" />
    <hkern g1="xi"
	g2="parenright,bracketright,braceright"
	k="-29" />
    <hkern g1="xi"
	g2="comma,period,ellipsis"
	k="-24" />
    <hkern g1="xi"
	g2="quotedbl,quotesingle"
	k="-13" />
    <hkern g1="zeta"
	g2="epsilontonos,epsilon"
	k="-7" />
    <hkern g1="zeta"
	g2="lambda"
	k="-30" />
    <hkern g1="zeta"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="5" />
    <hkern g1="zeta"
	g2="rho"
	k="-11" />
    <hkern g1="zeta"
	g2="tau"
	k="11" />
    <hkern g1="zeta"
	g2="omega,omegatonos"
	k="4" />
    <hkern g1="zeta"
	g2="colon,semicolon"
	k="-15" />
    <hkern g1="zeta"
	g2="guillemotright,guilsinglright"
	k="-21" />
    <hkern g1="zeta"
	g2="hyphen,uni00AD,endash,emdash"
	k="20" />
    <hkern g1="zeta"
	g2="parenright,bracketright,braceright"
	k="-38" />
    <hkern g1="zeta"
	g2="comma,period,ellipsis"
	k="-19" />
    <hkern g1="zeta"
	g2="quotedbl,quotesingle"
	k="-30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="uni0394"
	k="-11" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="uni03A9"
	k="-7" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Phi"
	k="8" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Psi"
	k="4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Upsilon,Upsilondieresis"
	k="50" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Xi"
	k="-3" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="gamma,nu"
	k="-11" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="lambda"
	k="-6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="tau"
	k="-18" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="pi"
	k="-20" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Epsilontonos,Etatonos,Iotatonos"
	k="3" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Omegatonos"
	k="-7" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Omicrontonos"
	k="-7" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Upsilontonos"
	k="54" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="uni03A9"
	k="-4" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Phi"
	k="-12" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Psi"
	k="12" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Upsilon,Upsilondieresis"
	k="58" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Sigma"
	k="25" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="theta"
	k="-5" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="zeta,xi"
	k="-14" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="omega,omegatonos"
	k="-12" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Omegatonos"
	k="-7" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Omicrontonos"
	k="-7" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="Upsilontonos"
	k="58" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="uni0394"
	k="15" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Phi"
	k="28" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Psi"
	k="-4" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Upsilon,Upsilondieresis"
	k="-7" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="chi"
	k="4" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="rho"
	k="28" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="tau"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="zeta,xi"
	k="14" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="omega,omegatonos"
	k="5" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Epsilontonos,Etatonos,Iotatonos"
	k="-7" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Omegatonos"
	k="-11" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Omicrontonos"
	k="-7" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="Upsilontonos"
	k="-7" />
    <hkern g1="comma,period,ellipsis"
	g2="Upsilon,Upsilondieresis"
	k="89" />
    <hkern g1="quotedbl,quotesingle"
	g2="uni0394"
	k="79" />
    <hkern g1="quotedbl,quotesingle"
	g2="uni03A9"
	k="20" />
    <hkern g1="quotedbl,quotesingle"
	g2="Phi"
	k="19" />
    <hkern g1="quotedbl,quotesingle"
	g2="epsilontonos,epsilon"
	k="54" />
    <hkern g1="quotedbl,quotesingle"
	g2="rho"
	k="74" />
    <hkern g1="quotedbl,quotesingle"
	g2="tau"
	k="-8" />
    <hkern g1="quotedbl,quotesingle"
	g2="theta"
	k="3" />
    <hkern g1="quotedbl,quotesingle"
	g2="zeta,xi"
	k="13" />
    <hkern g1="quotedbl,quotesingle"
	g2="omega,omegatonos"
	k="41" />
    <hkern g1="quotedbl,quotesingle"
	g2="Epsilontonos,Etatonos,Iotatonos"
	k="-19" />
    <hkern g1="quotedbl,quotesingle"
	g2="Omegatonos"
	k="-19" />
    <hkern g1="quotedbl,quotesingle"
	g2="Omicrontonos"
	k="-19" />
    <hkern g1="quotedbl,quotesingle"
	g2="Upsilontonos"
	k="-19" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni0394"
	k="87" />
    <hkern g1="quoteleft,quotedblleft"
	g2="uni03A9"
	k="47" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Phi"
	k="22" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Psi"
	k="-3" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Upsilon,Upsilondieresis"
	k="-14" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Xi"
	k="-4" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Sigma"
	k="-4" />
    <hkern g1="quoteleft,quotedblleft"
	g2="epsilontonos,epsilon"
	k="75" />
    <hkern g1="quoteleft,quotedblleft"
	g2="mu,etatonos,beta,eta,kappa"
	k="24" />
    <hkern g1="quoteleft,quotedblleft"
	g2="lambda"
	k="12" />
    <hkern g1="quoteleft,quotedblleft"
	g2="alphatonos,alpha,delta,omicron,uni03C2,sigma,phi,omicrontonos"
	k="65" />
    <hkern g1="quoteleft,quotedblleft"
	g2="rho"
	k="103" />
    <hkern g1="quoteleft,quotedblleft"
	g2="theta"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="zeta,xi"
	k="26" />
    <hkern g1="quoteleft,quotedblleft"
	g2="omega,omegatonos"
	k="64" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Epsilontonos,Etatonos,Iotatonos"
	k="-29" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Omegatonos"
	k="-29" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Omicrontonos"
	k="-29" />
    <hkern g1="quoteleft,quotedblleft"
	g2="Upsilontonos"
	k="-29" />
    <hkern g1="afii10018"
	g2="afii10041"
	k="4" />
    <hkern g1="afii10018"
	g2="afii10021"
	k="7" />
    <hkern g1="afii10018"
	g2="afii10051"
	k="-3" />
    <hkern g1="afii10018"
	g2="afii10025,afii10047"
	k="40" />
    <hkern g1="afii10018"
	g2="afii10038"
	k="4" />
    <hkern g1="afii10018"
	g2="afii10058,afii10029"
	k="9" />
    <hkern g1="afii10018"
	g2="afii10060,afii10036,afii10044"
	k="20" />
    <hkern g1="afii10018"
	g2="afii10057"
	k="6" />
    <hkern g1="afii10018"
	g2="afii10053,afii10032,afii10035"
	k="8" />
    <hkern g1="afii10018"
	g2="afii10062,afii10037"
	k="3" />
    <hkern g1="afii10018"
	g2="afii10024"
	k="9" />
    <hkern g1="afii10018"
	g2="afii10017"
	k="21" />
    <hkern g1="afii10018"
	g2="afii10049"
	k="9" />
    <hkern g1="afii10018"
	g2="afii10030"
	k="6" />
    <hkern g1="afii10018"
	g2="afii10066"
	k="8" />
    <hkern g1="afii10018"
	g2="afii10073,afii10095,afii10846"
	k="28" />
    <hkern g1="afii10018"
	g2="afii10086"
	k="10" />
    <hkern g1="afii10018"
	g2="afii10084,afii10092"
	k="4" />
    <hkern g1="afii10018"
	g2="afii10105"
	k="22" />
    <hkern g1="afii10018"
	g2="afii10108"
	k="3" />
    <hkern g1="afii10018"
	g2="afii10085,afii10110"
	k="17" />
    <hkern g1="afii10018"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="afii10018"
	g2="quoteright,quotedblright"
	k="-10" />
    <hkern g1="afii10051"
	g2="afii10041"
	k="92" />
    <hkern g1="afii10051"
	g2="afii10021"
	k="-8" />
    <hkern g1="afii10051"
	g2="afii10051"
	k="7" />
    <hkern g1="afii10051"
	g2="afii10025,afii10047"
	k="21" />
    <hkern g1="afii10051"
	g2="afii10038"
	k="-14" />
    <hkern g1="afii10051"
	g2="afii10053,afii10032,afii10035"
	k="4" />
    <hkern g1="afii10051"
	g2="afii10062,afii10037"
	k="-8" />
    <hkern g1="afii10051"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="13" />
    <hkern g1="afii10051"
	g2="afii10054"
	k="-9" />
    <hkern g1="afii10051"
	g2="afii10039"
	k="6" />
    <hkern g1="afii10051"
	g2="afii10089"
	k="-9" />
    <hkern g1="afii10051"
	g2="afii10069"
	k="20" />
    <hkern g1="afii10051"
	g2="afii10073,afii10095,afii10846"
	k="7" />
    <hkern g1="afii10051"
	g2="afii10086"
	k="19" />
    <hkern g1="afii10051"
	g2="afii10077,afii10106"
	k="-4" />
    <hkern g1="afii10051"
	g2="afii10084,afii10092"
	k="-10" />
    <hkern g1="afii10051"
	g2="comma,period,ellipsis"
	k="27" />
    <hkern g1="afii10051"
	g2="hyphen,uni00AD,endash,emdash"
	k="5" />
    <hkern g1="afii10051"
	g2="guillemotright,guilsinglright"
	k="7" />
    <hkern g1="afii10054"
	g2="afii10041"
	k="6" />
    <hkern g1="afii10054"
	g2="afii10062,afii10037"
	k="15" />
    <hkern g1="afii10054"
	g2="afii10024"
	k="3" />
    <hkern g1="afii10054"
	g2="afii10017"
	k="6" />
    <hkern g1="afii10054"
	g2="afii10054"
	k="-3" />
    <hkern g1="afii10054"
	g2="afii10039"
	k="-3" />
    <hkern g1="afii10054"
	g2="afii10049"
	k="-3" />
    <hkern g1="afii10054"
	g2="afii10030"
	k="26" />
    <hkern g1="afii10054"
	g2="afii10066"
	k="7" />
    <hkern g1="afii10054"
	g2="afii10089"
	k="-5" />
    <hkern g1="afii10054"
	g2="afii10099"
	k="14" />
    <hkern g1="afii10054"
	g2="afii10077,afii10106"
	k="-3" />
    <hkern g1="afii10054"
	g2="afii10103"
	k="6" />
    <hkern g1="afii10054"
	g2="colon,semicolon"
	k="3" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10060,afii10036,afii10044"
	k="6" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10053,afii10032,afii10035"
	k="-3" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10024"
	k="-4" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-3" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10054"
	k="6" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10030"
	k="-3" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10066"
	k="4" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10069"
	k="-4" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-9" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10065"
	k="10" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="29" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10078"
	k="27" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="afii10103"
	k="10" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="colon,semicolon"
	k="11" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="comma,period,ellipsis"
	k="20" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="hyphen,uni00AD,endash,emdash"
	k="19" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="afii10055,afii10056,afii10145,afii10026,afii10027,afii10029,afii10031,afii10033,afii10041,afii10042,afii10045,afii10049"
	g2="quoteright,quotedblright"
	k="16" />
    <hkern g1="afii10038"
	g2="afii10021"
	k="8" />
    <hkern g1="afii10038"
	g2="afii10038"
	k="20" />
    <hkern g1="afii10038"
	g2="afii10058,afii10029"
	k="-5" />
    <hkern g1="afii10038"
	g2="afii10024"
	k="-7" />
    <hkern g1="afii10038"
	g2="afii10017"
	k="3" />
    <hkern g1="afii10038"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-4" />
    <hkern g1="afii10038"
	g2="afii10054"
	k="-5" />
    <hkern g1="afii10038"
	g2="afii10039"
	k="-3" />
    <hkern g1="afii10038"
	g2="afii10049"
	k="-5" />
    <hkern g1="afii10038"
	g2="afii10066"
	k="4" />
    <hkern g1="afii10038"
	g2="afii10089"
	k="4" />
    <hkern g1="afii10038"
	g2="afii10097"
	k="19" />
    <hkern g1="afii10038"
	g2="afii10072"
	k="-6" />
    <hkern g1="afii10038"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="4" />
    <hkern g1="afii10038"
	g2="afii10078"
	k="-7" />
    <hkern g1="afii10038"
	g2="afii10103"
	k="4" />
    <hkern g1="afii10038"
	g2="colon,semicolon"
	k="4" />
    <hkern g1="afii10038"
	g2="comma,period,ellipsis"
	k="6" />
    <hkern g1="afii10038"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="afii10030"
	g2="afii10041"
	k="13" />
    <hkern g1="afii10030"
	g2="afii10021"
	k="-3" />
    <hkern g1="afii10030"
	g2="afii10051"
	k="-3" />
    <hkern g1="afii10030"
	g2="afii10038"
	k="-5" />
    <hkern g1="afii10030"
	g2="afii10060,afii10036,afii10044"
	k="-4" />
    <hkern g1="afii10030"
	g2="afii10057"
	k="6" />
    <hkern g1="afii10030"
	g2="afii10024"
	k="-3" />
    <hkern g1="afii10030"
	g2="afii10017"
	k="3" />
    <hkern g1="afii10030"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-4" />
    <hkern g1="afii10030"
	g2="afii10054"
	k="-5" />
    <hkern g1="afii10030"
	g2="afii10049"
	k="-3" />
    <hkern g1="afii10030"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="6" />
    <hkern g1="afii10030"
	g2="afii10108"
	k="65" />
    <hkern g1="afii10030"
	g2="afii10097"
	k="19" />
    <hkern g1="afii10030"
	g2="afii10072"
	k="7" />
    <hkern g1="afii10030"
	g2="afii10087"
	k="32" />
    <hkern g1="afii10030"
	g2="afii10065"
	k="-6" />
    <hkern g1="afii10030"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="84" />
    <hkern g1="afii10030"
	g2="afii10078"
	k="5" />
    <hkern g1="afii10030"
	g2="afii10103"
	k="11" />
    <hkern g1="afii10030"
	g2="parenright,bracketright,braceright"
	k="20" />
    <hkern g1="afii10030"
	g2="colon,semicolon"
	k="71" />
    <hkern g1="afii10030"
	g2="guillemotright,guilsinglright"
	k="27" />
    <hkern g1="afii10030"
	g2="guillemotleft,guilsinglleft"
	k="5" />
    <hkern g1="afii10030"
	g2="quoteright,quotedblright"
	k="16" />
    <hkern g1="afii10034"
	g2="afii10041"
	k="19" />
    <hkern g1="afii10034"
	g2="afii10051"
	k="25" />
    <hkern g1="afii10034"
	g2="afii10025,afii10047"
	k="21" />
    <hkern g1="afii10034"
	g2="afii10038"
	k="-4" />
    <hkern g1="afii10034"
	g2="afii10058,afii10029"
	k="19" />
    <hkern g1="afii10034"
	g2="afii10060,afii10036,afii10044"
	k="26" />
    <hkern g1="afii10034"
	g2="afii10057"
	k="-10" />
    <hkern g1="afii10034"
	g2="afii10053,afii10032,afii10035"
	k="-5" />
    <hkern g1="afii10034"
	g2="afii10062,afii10037"
	k="9" />
    <hkern g1="afii10034"
	g2="afii10024"
	k="-4" />
    <hkern g1="afii10034"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="22" />
    <hkern g1="afii10034"
	g2="afii10054"
	k="11" />
    <hkern g1="afii10034"
	g2="afii10039"
	k="15" />
    <hkern g1="afii10034"
	g2="afii10049"
	k="11" />
    <hkern g1="afii10034"
	g2="afii10066"
	k="19" />
    <hkern g1="afii10034"
	g2="afii10089"
	k="143" />
    <hkern g1="afii10034"
	g2="afii10069"
	k="12" />
    <hkern g1="afii10034"
	g2="afii10099"
	k="9" />
    <hkern g1="afii10034"
	g2="afii10077,afii10106"
	k="11" />
    <hkern g1="afii10034"
	g2="afii10084,afii10092"
	k="-17" />
    <hkern g1="afii10034"
	g2="afii10105"
	k="-27" />
    <hkern g1="afii10034"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-18" />
    <hkern g1="afii10034"
	g2="afii10108"
	k="27" />
    <hkern g1="afii10034"
	g2="afii10085,afii10110"
	k="-27" />
    <hkern g1="afii10034"
	g2="afii10097"
	k="-26" />
    <hkern g1="afii10034"
	g2="afii10087"
	k="23" />
    <hkern g1="afii10034"
	g2="afii10065"
	k="-17" />
    <hkern g1="afii10034"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="-13" />
    <hkern g1="afii10034"
	g2="afii10078"
	k="-7" />
    <hkern g1="afii10034"
	g2="colon,semicolon"
	k="-8" />
    <hkern g1="afii10034"
	g2="comma,period,ellipsis"
	k="-11" />
    <hkern g1="afii10034"
	g2="guillemotright,guilsinglright"
	k="16" />
    <hkern g1="afii10034"
	g2="guillemotleft,guilsinglleft"
	k="52" />
    <hkern g1="afii10034"
	g2="quoteright,quotedblright"
	k="-9" />
    <hkern g1="afii10053,afii10035"
	g2="afii10041"
	k="-14" />
    <hkern g1="afii10053,afii10035"
	g2="afii10021"
	k="27" />
    <hkern g1="afii10053,afii10035"
	g2="afii10025,afii10047"
	k="20" />
    <hkern g1="afii10053,afii10035"
	g2="afii10038"
	k="-15" />
    <hkern g1="afii10053,afii10035"
	g2="afii10058,afii10029"
	k="21" />
    <hkern g1="afii10053,afii10035"
	g2="afii10057"
	k="-13" />
    <hkern g1="afii10053,afii10035"
	g2="afii10062,afii10037"
	k="6" />
    <hkern g1="afii10053,afii10035"
	g2="afii10102"
	k="5" />
    <hkern g1="afii10053,afii10035"
	g2="afii10073,afii10095,afii10846"
	k="55" />
    <hkern g1="afii10053,afii10035"
	g2="afii10086"
	k="-41" />
    <hkern g1="afii10053,afii10035"
	g2="afii10077,afii10106"
	k="-9" />
    <hkern g1="afii10053,afii10035"
	g2="afii10084,afii10092"
	k="41" />
    <hkern g1="afii10053,afii10035"
	g2="afii10105"
	k="33" />
    <hkern g1="afii10053,afii10035"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-32" />
    <hkern g1="afii10053,afii10035"
	g2="afii10108"
	k="71" />
    <hkern g1="afii10053,afii10035"
	g2="afii10085,afii10110"
	k="34" />
    <hkern g1="afii10053,afii10035"
	g2="afii10097"
	k="-32" />
    <hkern g1="afii10053,afii10035"
	g2="afii10072"
	k="-16" />
    <hkern g1="afii10053,afii10035"
	g2="afii10087"
	k="78" />
    <hkern g1="afii10053,afii10035"
	g2="afii10103"
	k="9" />
    <hkern g1="afii10053,afii10035"
	g2="parenright,bracketright,braceright"
	k="8" />
    <hkern g1="afii10053,afii10035"
	g2="colon,semicolon"
	k="26" />
    <hkern g1="afii10053,afii10035"
	g2="comma,period,ellipsis"
	k="87" />
    <hkern g1="afii10053,afii10035"
	g2="hyphen,uni00AD,endash,emdash"
	k="105" />
    <hkern g1="afii10053,afii10035"
	g2="guillemotright,guilsinglright"
	k="-31" />
    <hkern g1="afii10053,afii10035"
	g2="guillemotleft,guilsinglleft"
	k="31" />
    <hkern g1="afii10053,afii10035"
	g2="quoteright,quotedblright"
	k="58" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10041"
	k="93" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10021"
	k="-31" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10051"
	k="49" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10025,afii10047"
	k="77" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10038"
	k="56" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10058,afii10029"
	k="56" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10060,afii10036,afii10044"
	k="77" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10057"
	k="71" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10053,afii10032,afii10035"
	k="82" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10062,afii10037"
	k="10" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10024"
	k="-19" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10017"
	k="60" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="81" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10054"
	k="82" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10039"
	k="65" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10049"
	k="45" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10089"
	k="18" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10069"
	k="-9" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10099"
	k="-13" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10102"
	k="-7" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10073,afii10095,afii10846"
	k="42" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10086"
	k="-19" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10077,afii10106"
	k="-14" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10105"
	k="35" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-11" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10108"
	k="-8" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10085,afii10110"
	k="4" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10065"
	k="-12" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10078"
	k="22" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="afii10103"
	k="53" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="parenright,bracketright,braceright"
	k="-11" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="colon,semicolon"
	k="-11" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="hyphen,uni00AD,endash,emdash"
	k="-9" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="guillemotright,guilsinglright"
	k="21" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="guillemotleft,guilsinglleft"
	k="-15" />
    <hkern g1="afii10052,afii10020,afii10036,afii10050"
	g2="quoteright,quotedblright"
	k="30" />
    <hkern g1="afii10039"
	g2="afii10041"
	k="-7" />
    <hkern g1="afii10039"
	g2="afii10021"
	k="-9" />
    <hkern g1="afii10039"
	g2="afii10051"
	k="-4" />
    <hkern g1="afii10039"
	g2="afii10025,afii10047"
	k="8" />
    <hkern g1="afii10039"
	g2="afii10053,afii10032,afii10035"
	k="6" />
    <hkern g1="afii10039"
	g2="afii10049"
	k="53" />
    <hkern g1="afii10039"
	g2="afii10030"
	k="7" />
    <hkern g1="afii10039"
	g2="afii10066"
	k="96" />
    <hkern g1="afii10039"
	g2="afii10089"
	k="9" />
    <hkern g1="afii10039"
	g2="afii10069"
	k="5" />
    <hkern g1="afii10039"
	g2="afii10102"
	k="83" />
    <hkern g1="afii10039"
	g2="afii10073,afii10095,afii10846"
	k="4" />
    <hkern g1="afii10039"
	g2="afii10086"
	k="4" />
    <hkern g1="afii10039"
	g2="afii10077,afii10106"
	k="56" />
    <hkern g1="afii10039"
	g2="afii10084,afii10092"
	k="3" />
    <hkern g1="afii10039"
	g2="afii10105"
	k="6" />
    <hkern g1="afii10039"
	g2="afii10108"
	k="8" />
    <hkern g1="afii10039"
	g2="afii10085,afii10110"
	k="23" />
    <hkern g1="afii10039"
	g2="afii10072"
	k="5" />
    <hkern g1="afii10039"
	g2="afii10065"
	k="12" />
    <hkern g1="afii10039"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="5" />
    <hkern g1="afii10039"
	g2="afii10078"
	k="6" />
    <hkern g1="afii10039"
	g2="afii10103"
	k="6" />
    <hkern g1="afii10039"
	g2="hyphen,uni00AD,endash,emdash"
	k="23" />
    <hkern g1="afii10039"
	g2="guillemotright,guilsinglright"
	k="12" />
    <hkern g1="afii10039"
	g2="quoteright,quotedblright"
	k="5" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10038"
	k="35" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10024"
	k="49" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="10" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10054"
	k="-15" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10039"
	k="4" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10049"
	k="-9" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10066"
	k="-18" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10069"
	k="-18" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10102"
	k="-4" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-7" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10085,afii10110"
	k="3" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10097"
	k="15" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10072"
	k="-9" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10087"
	k="-3" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10065"
	k="-3" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="-9" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10078"
	k="7" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="afii10103"
	k="-12" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="parenright,bracketright,braceright"
	k="16" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="colon,semicolon"
	k="6" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="comma,period,ellipsis"
	k="6" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="hyphen,uni00AD,endash,emdash"
	k="-5" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="guillemotleft,guilsinglleft"
	k="-6" />
    <hkern g1="afii10058,afii10059,afii10044,afii10046"
	g2="quoteright,quotedblright"
	k="-8" />
    <hkern g1="afii10023,afii10022"
	g2="afii10024"
	k="11" />
    <hkern g1="afii10023,afii10022"
	g2="afii10030"
	k="25" />
    <hkern g1="afii10023,afii10022"
	g2="afii10089"
	k="-3" />
    <hkern g1="afii10023,afii10022"
	g2="afii10069"
	k="3" />
    <hkern g1="afii10023,afii10022"
	g2="afii10099"
	k="8" />
    <hkern g1="afii10023,afii10022"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="7" />
    <hkern g1="afii10023,afii10022"
	g2="afii10108"
	k="15" />
    <hkern g1="afii10023,afii10022"
	g2="afii10085,afii10110"
	k="-8" />
    <hkern g1="afii10023,afii10022"
	g2="afii10097"
	k="5" />
    <hkern g1="afii10023,afii10022"
	g2="afii10078"
	k="9" />
    <hkern g1="afii10023,afii10022"
	g2="parenright,bracketright,braceright"
	k="-8" />
    <hkern g1="afii10023,afii10022"
	g2="comma,period,ellipsis"
	k="7" />
    <hkern g1="afii10023,afii10022"
	g2="guillemotright,guilsinglright"
	k="3" />
    <hkern g1="afii10023,afii10022"
	g2="guillemotleft,guilsinglleft"
	k="4" />
    <hkern g1="afii10057"
	g2="afii10060,afii10036,afii10044"
	k="16" />
    <hkern g1="afii10057"
	g2="afii10057"
	k="-25" />
    <hkern g1="afii10057"
	g2="afii10053,afii10032,afii10035"
	k="-14" />
    <hkern g1="afii10057"
	g2="afii10062,afii10037"
	k="-14" />
    <hkern g1="afii10057"
	g2="afii10024"
	k="22" />
    <hkern g1="afii10057"
	g2="afii10017"
	k="-30" />
    <hkern g1="afii10057"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-22" />
    <hkern g1="afii10057"
	g2="afii10054"
	k="-25" />
    <hkern g1="afii10057"
	g2="afii10039"
	k="18" />
    <hkern g1="afii10057"
	g2="afii10049"
	k="-17" />
    <hkern g1="afii10057"
	g2="afii10030"
	k="-18" />
    <hkern g1="afii10057"
	g2="afii10066"
	k="-9" />
    <hkern g1="afii10057"
	g2="afii10099"
	k="-11" />
    <hkern g1="afii10057"
	g2="afii10102"
	k="-24" />
    <hkern g1="afii10057"
	g2="afii10086"
	k="13" />
    <hkern g1="afii10057"
	g2="afii10077,afii10106"
	k="42" />
    <hkern g1="afii10057"
	g2="afii10084,afii10092"
	k="-22" />
    <hkern g1="afii10057"
	g2="afii10105"
	k="-17" />
    <hkern g1="afii10057"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-4" />
    <hkern g1="afii10057"
	g2="afii10108"
	k="-13" />
    <hkern g1="afii10057"
	g2="afii10085,afii10110"
	k="4" />
    <hkern g1="afii10057"
	g2="afii10097"
	k="-25" />
    <hkern g1="afii10057"
	g2="afii10072"
	k="30" />
    <hkern g1="afii10057"
	g2="afii10065"
	k="6" />
    <hkern g1="afii10057"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="-16" />
    <hkern g1="afii10057"
	g2="afii10078"
	k="25" />
    <hkern g1="afii10057"
	g2="afii10103"
	k="-12" />
    <hkern g1="afii10057"
	g2="parenright,bracketright,braceright"
	k="-16" />
    <hkern g1="afii10057"
	g2="colon,semicolon"
	k="-13" />
    <hkern g1="afii10057"
	g2="comma,period,ellipsis"
	k="-8" />
    <hkern g1="afii10057"
	g2="guillemotright,guilsinglright"
	k="-4" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10025,afii10047"
	k="6" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10038"
	k="23" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10058,afii10029"
	k="13" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10060,afii10036,afii10044"
	k="13" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10053,afii10032,afii10035"
	k="9" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10062,afii10037"
	k="8" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10024"
	k="9" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="28" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10054"
	k="18" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10039"
	k="18" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10066"
	k="32" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10069"
	k="4" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10102"
	k="-4" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10073,afii10095,afii10846"
	k="14" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10086"
	k="-5" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10084,afii10092"
	k="-5" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10108"
	k="-9" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10085,afii10110"
	k="3" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10072"
	k="-7" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10087"
	k="-10" />
    <hkern g1="afii10061,afii10024,afii10028"
	g2="afii10078"
	k="9" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10041"
	k="19" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10021"
	k="-12" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10051"
	k="13" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10025,afii10047"
	k="-5" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10038"
	k="11" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10058,afii10029"
	k="-15" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10060,afii10036,afii10044"
	k="13" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10057"
	k="-14" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10053,afii10032,afii10035"
	k="8" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10062,afii10037"
	k="9" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10024"
	k="-10" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10017"
	k="-6" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10049"
	k="-8" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10066"
	k="6" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10089"
	k="21" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10069"
	k="-14" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10073,afii10095,afii10846"
	k="-10" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10086"
	k="6" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10077,afii10106"
	k="-16" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10084,afii10092"
	k="25" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10105"
	k="-34" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="5" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10085,afii10110"
	k="10" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10097"
	k="-7" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10072"
	k="-11" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10087"
	k="-3" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="afii10065"
	k="-7" />
    <hkern g1="afii10032,afii10047,afii10048"
	g2="comma,period,ellipsis"
	k="-18" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10021"
	k="7" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10025,afii10047"
	k="74" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10058,afii10029"
	k="5" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10060,afii10036,afii10044"
	k="33" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-3" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10039"
	k="4" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10049"
	k="21" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10066"
	k="8" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10099"
	k="3" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10073,afii10095,afii10846"
	k="28" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10086"
	k="17" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10077,afii10106"
	k="3" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10084,afii10092"
	k="5" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="afii10105"
	k="23" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="guillemotleft,guilsinglleft"
	k="-3" />
    <hkern g1="afii10021,afii10040,afii10043"
	g2="quoteright,quotedblright"
	k="52" />
    <hkern g1="afii10060"
	g2="afii10041"
	k="-34" />
    <hkern g1="afii10060"
	g2="afii10021"
	k="69" />
    <hkern g1="afii10060"
	g2="afii10051"
	k="15" />
    <hkern g1="afii10060"
	g2="afii10025,afii10047"
	k="-23" />
    <hkern g1="afii10060"
	g2="afii10038"
	k="-17" />
    <hkern g1="afii10060"
	g2="afii10058,afii10029"
	k="57" />
    <hkern g1="afii10060"
	g2="afii10053,afii10032,afii10035"
	k="-8" />
    <hkern g1="afii10060"
	g2="afii10062,afii10037"
	k="4" />
    <hkern g1="afii10060"
	g2="afii10024"
	k="9" />
    <hkern g1="afii10060"
	g2="afii10017"
	k="17" />
    <hkern g1="afii10060"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="36" />
    <hkern g1="afii10060"
	g2="afii10054"
	k="75" />
    <hkern g1="afii10060"
	g2="afii10039"
	k="-37" />
    <hkern g1="afii10060"
	g2="afii10049"
	k="45" />
    <hkern g1="afii10060"
	g2="afii10030"
	k="23" />
    <hkern g1="afii10060"
	g2="afii10066"
	k="55" />
    <hkern g1="afii10060"
	g2="afii10089"
	k="39" />
    <hkern g1="afii10060"
	g2="afii10099"
	k="16" />
    <hkern g1="afii10060"
	g2="afii10102"
	k="57" />
    <hkern g1="afii10060"
	g2="afii10073,afii10095,afii10846"
	k="-31" />
    <hkern g1="afii10060"
	g2="afii10086"
	k="10" />
    <hkern g1="afii10060"
	g2="afii10077,afii10106"
	k="46" />
    <hkern g1="afii10060"
	g2="afii10084,afii10092"
	k="16" />
    <hkern g1="afii10060"
	g2="afii10105"
	k="16" />
    <hkern g1="afii10060"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="52" />
    <hkern g1="afii10060"
	g2="afii10108"
	k="28" />
    <hkern g1="afii10060"
	g2="afii10085,afii10110"
	k="41" />
    <hkern g1="afii10060"
	g2="afii10097"
	k="7" />
    <hkern g1="afii10060"
	g2="afii10072"
	k="-20" />
    <hkern g1="afii10060"
	g2="afii10087"
	k="38" />
    <hkern g1="afii10060"
	g2="afii10065"
	k="114" />
    <hkern g1="afii10060"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="48" />
    <hkern g1="afii10060"
	g2="afii10078"
	k="28" />
    <hkern g1="afii10060"
	g2="afii10103"
	k="30" />
    <hkern g1="afii10060"
	g2="comma,period,ellipsis"
	k="19" />
    <hkern g1="afii10060"
	g2="hyphen,uni00AD,endash,emdash"
	k="3" />
    <hkern g1="afii10060"
	g2="guillemotright,guilsinglright"
	k="10" />
    <hkern g1="afii10060"
	g2="quoteright,quotedblright"
	k="9" />
    <hkern g1="afii10062,afii10037"
	g2="afii10041"
	k="20" />
    <hkern g1="afii10062,afii10037"
	g2="afii10021"
	k="6" />
    <hkern g1="afii10062,afii10037"
	g2="afii10058,afii10029"
	k="13" />
    <hkern g1="afii10062,afii10037"
	g2="afii10062,afii10037"
	k="20" />
    <hkern g1="afii10062,afii10037"
	g2="afii10024"
	k="7" />
    <hkern g1="afii10062,afii10037"
	g2="afii10017"
	k="-3" />
    <hkern g1="afii10062,afii10037"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="4" />
    <hkern g1="afii10062,afii10037"
	g2="afii10039"
	k="-3" />
    <hkern g1="afii10062,afii10037"
	g2="afii10030"
	k="7" />
    <hkern g1="afii10062,afii10037"
	g2="afii10066"
	k="9" />
    <hkern g1="afii10062,afii10037"
	g2="afii10089"
	k="-4" />
    <hkern g1="afii10062,afii10037"
	g2="afii10099"
	k="6" />
    <hkern g1="afii10062,afii10037"
	g2="afii10102"
	k="4" />
    <hkern g1="afii10062,afii10037"
	g2="afii10086"
	k="10" />
    <hkern g1="afii10062,afii10037"
	g2="afii10077,afii10106"
	k="-3" />
    <hkern g1="afii10019,afii10025"
	g2="afii10058,afii10029"
	k="4" />
    <hkern g1="afii10019,afii10025"
	g2="afii10060,afii10036,afii10044"
	k="14" />
    <hkern g1="afii10019,afii10025"
	g2="afii10053,afii10032,afii10035"
	k="8" />
    <hkern g1="afii10019,afii10025"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-6" />
    <hkern g1="afii10019,afii10025"
	g2="afii10054"
	k="11" />
    <hkern g1="afii10019,afii10025"
	g2="afii10039"
	k="10" />
    <hkern g1="afii10019,afii10025"
	g2="afii10030"
	k="6" />
    <hkern g1="afii10019,afii10025"
	g2="afii10066"
	k="15" />
    <hkern g1="afii10019,afii10025"
	g2="afii10089"
	k="-6" />
    <hkern g1="afii10065"
	g2="afii10025,afii10047"
	k="10" />
    <hkern g1="afii10065"
	g2="afii10058,afii10029"
	k="7" />
    <hkern g1="afii10065"
	g2="afii10062,afii10037"
	k="-7" />
    <hkern g1="afii10065"
	g2="afii10024"
	k="14" />
    <hkern g1="afii10065"
	g2="afii10039"
	k="10" />
    <hkern g1="afii10065"
	g2="afii10049"
	k="-3" />
    <hkern g1="afii10099"
	g2="afii10041"
	k="14" />
    <hkern g1="afii10099"
	g2="afii10021"
	k="-4" />
    <hkern g1="afii10099"
	g2="afii10051"
	k="9" />
    <hkern g1="afii10099"
	g2="afii10057"
	k="4" />
    <hkern g1="afii10099"
	g2="afii10053,afii10032,afii10035"
	k="6" />
    <hkern g1="afii10099"
	g2="afii10024"
	k="8" />
    <hkern g1="afii10099"
	g2="afii10017"
	k="3" />
    <hkern g1="afii10102"
	g2="afii10025,afii10047"
	k="-3" />
    <hkern g1="afii10102"
	g2="afii10038"
	k="4" />
    <hkern g1="afii10102"
	g2="afii10058,afii10029"
	k="10" />
    <hkern g1="afii10102"
	g2="afii10057"
	k="4" />
    <hkern g1="afii10102"
	g2="afii10053,afii10032,afii10035"
	k="3" />
    <hkern g1="afii10102"
	g2="afii10062,afii10037"
	k="-3" />
    <hkern g1="afii10102"
	g2="guillemotleft,guilsinglleft"
	k="7" />
    <hkern g1="afii10102"
	g2="quoteright,quotedblright"
	k="8" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10041"
	k="-3" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10021"
	k="8" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10051"
	k="11" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10038"
	k="4" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10058,afii10029"
	k="19" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10057"
	k="9" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10053,afii10032,afii10035"
	k="22" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10062,afii10037"
	k="-3" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="afii10017"
	k="3" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="comma,period,ellipsis"
	k="14" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="hyphen,uni00AD,endash,emdash"
	k="-7" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="guillemotright,guilsinglright"
	k="5" />
    <hkern g1="afii10074,afii10075,afii10077,afii10079,afii10081,afii10089,afii10090,afii10093,afii10097,afii10193"
	g2="quoteright,quotedblright"
	k="-4" />
    <hkern g1="afii10086"
	g2="afii10041"
	k="3" />
    <hkern g1="afii10086"
	g2="afii10021"
	k="3" />
    <hkern g1="afii10086"
	g2="afii10051"
	k="8" />
    <hkern g1="afii10086"
	g2="afii10025,afii10047"
	k="-3" />
    <hkern g1="afii10086"
	g2="afii10078"
	k="-4" />
    <hkern g1="afii10086"
	g2="afii10103"
	k="4" />
    <hkern g1="afii10086"
	g2="parenright,bracketright,braceright"
	k="-17" />
    <hkern g1="afii10086"
	g2="colon,semicolon"
	k="-4" />
    <hkern g1="afii10086"
	g2="hyphen,uni00AD,endash,emdash"
	k="-16" />
    <hkern g1="afii10086"
	g2="guillemotright,guilsinglright"
	k="8" />
    <hkern g1="afii10086"
	g2="guillemotleft,guilsinglleft"
	k="-21" />
    <hkern g1="afii10086"
	g2="quoteright,quotedblright"
	k="-15" />
    <hkern g1="afii10078"
	g2="afii10041"
	k="-10" />
    <hkern g1="afii10078"
	g2="afii10021"
	k="-16" />
    <hkern g1="afii10078"
	g2="afii10051"
	k="-11" />
    <hkern g1="afii10078"
	g2="afii10025,afii10047"
	k="-5" />
    <hkern g1="afii10078"
	g2="afii10087"
	k="-3" />
    <hkern g1="afii10078"
	g2="afii10065"
	k="6" />
    <hkern g1="afii10078"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="41" />
    <hkern g1="afii10078"
	g2="afii10103"
	k="7" />
    <hkern g1="afii10078"
	g2="parenright,bracketright,braceright"
	k="-5" />
    <hkern g1="afii10078"
	g2="colon,semicolon"
	k="17" />
    <hkern g1="afii10078"
	g2="comma,period,ellipsis"
	k="16" />
    <hkern g1="afii10078"
	g2="hyphen,uni00AD,endash,emdash"
	k="-18" />
    <hkern g1="afii10078"
	g2="guillemotright,guilsinglright"
	k="12" />
    <hkern g1="afii10078"
	g2="guillemotleft,guilsinglleft"
	k="17" />
    <hkern g1="afii10083,afii10101"
	g2="afii10041"
	k="13" />
    <hkern g1="afii10083,afii10101"
	g2="afii10021"
	k="6" />
    <hkern g1="afii10083,afii10101"
	g2="afii10051"
	k="7" />
    <hkern g1="afii10083,afii10101"
	g2="afii10025,afii10047"
	k="6" />
    <hkern g1="afii10083,afii10101"
	g2="afii10038"
	k="22" />
    <hkern g1="afii10083,afii10101"
	g2="afii10060,afii10036,afii10044"
	k="74" />
    <hkern g1="afii10083,afii10101"
	g2="afii10057"
	k="27" />
    <hkern g1="afii10083,afii10101"
	g2="afii10062,afii10037"
	k="16" />
    <hkern g1="afii10083,afii10101"
	g2="afii10097"
	k="7" />
    <hkern g1="afii10083,afii10101"
	g2="afii10072"
	k="-16" />
    <hkern g1="afii10083,afii10101"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="-16" />
    <hkern g1="afii10083,afii10101"
	g2="afii10078"
	k="18" />
    <hkern g1="afii10083,afii10101"
	g2="afii10103"
	k="-22" />
    <hkern g1="afii10083,afii10101"
	g2="parenright,bracketright,braceright"
	k="-9" />
    <hkern g1="afii10083,afii10101"
	g2="comma,period,ellipsis"
	k="18" />
    <hkern g1="afii10083,afii10101"
	g2="guillemotright,guilsinglright"
	k="-9" />
    <hkern g1="afii10083,afii10101"
	g2="guillemotleft,guilsinglleft"
	k="-17" />
    <hkern g1="afii10083,afii10101"
	g2="quoteright,quotedblright"
	k="-17" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10060,afii10036,afii10044"
	k="18" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10105"
	k="5" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="34" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10108"
	k="3" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10085,afii10110"
	k="14" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10097"
	k="6" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10072"
	k="3" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10065"
	k="-4" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="59" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="afii10078"
	k="12" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="parenright,bracketright,braceright"
	k="8" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="colon,semicolon"
	k="40" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="comma,period,ellipsis"
	k="6" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="guillemotright,guilsinglright"
	k="11" />
    <hkern g1="afii10068,afii10084,afii10100,afii10098"
	g2="guillemotleft,guilsinglleft"
	k="-3" />
    <hkern g1="afii10087"
	g2="afii10038"
	k="55" />
    <hkern g1="afii10087"
	g2="afii10077,afii10106"
	k="7" />
    <hkern g1="afii10087"
	g2="afii10072"
	k="5" />
    <hkern g1="afii10087"
	g2="afii10087"
	k="5" />
    <hkern g1="afii10087"
	g2="afii10103"
	k="-3" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10099"
	k="-5" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10102"
	k="6" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10086"
	k="3" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10084,afii10092"
	k="-4" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10105"
	k="-4" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-6" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10108"
	k="7" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10085,afii10110"
	k="7" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10087"
	k="8" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10065"
	k="-4" />
    <hkern g1="afii10092,afii10094,afii10106,afii10107"
	g2="afii10078"
	k="6" />
    <hkern g1="afii10103"
	g2="afii10089"
	k="8" />
    <hkern g1="afii10103"
	g2="afii10069"
	k="-4" />
    <hkern g1="afii10103"
	g2="afii10099"
	k="-3" />
    <hkern g1="afii10103"
	g2="afii10084,afii10092"
	k="5" />
    <hkern g1="afii10103"
	g2="afii10108"
	k="-3" />
    <hkern g1="afii10070,afii10071"
	g2="afii10039"
	k="5" />
    <hkern g1="afii10070,afii10071"
	g2="afii10049"
	k="12" />
    <hkern g1="afii10070,afii10071"
	g2="afii10030"
	k="-21" />
    <hkern g1="afii10070,afii10071"
	g2="afii10069"
	k="-14" />
    <hkern g1="afii10070,afii10071"
	g2="afii10099"
	k="9" />
    <hkern g1="afii10070,afii10071"
	g2="afii10102"
	k="-24" />
    <hkern g1="afii10070,afii10071"
	g2="afii10073,afii10095,afii10846"
	k="-13" />
    <hkern g1="afii10070,afii10071"
	g2="afii10077,afii10106"
	k="7" />
    <hkern g1="afii10070,afii10071"
	g2="afii10105"
	k="-10" />
    <hkern g1="afii10070,afii10071"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="-16" />
    <hkern g1="afii10070,afii10071"
	g2="afii10108"
	k="-21" />
    <hkern g1="afii10070,afii10071"
	g2="afii10085,afii10110"
	k="-14" />
    <hkern g1="afii10070,afii10071"
	g2="afii10097"
	k="-10" />
    <hkern g1="afii10070,afii10071"
	g2="comma,period,ellipsis"
	k="14" />
    <hkern g1="afii10105"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="9" />
    <hkern g1="afii10105"
	g2="afii10054"
	k="12" />
    <hkern g1="afii10105"
	g2="afii10039"
	k="8" />
    <hkern g1="afii10105"
	g2="afii10049"
	k="3" />
    <hkern g1="afii10105"
	g2="afii10069"
	k="12" />
    <hkern g1="afii10105"
	g2="afii10099"
	k="10" />
    <hkern g1="afii10105"
	g2="afii10073,afii10095,afii10846"
	k="5" />
    <hkern g1="afii10105"
	g2="afii10086"
	k="15" />
    <hkern g1="afii10105"
	g2="afii10077,afii10106"
	k="5" />
    <hkern g1="afii10105"
	g2="afii10084,afii10092"
	k="9" />
    <hkern g1="afii10105"
	g2="afii10105"
	k="19" />
    <hkern g1="afii10105"
	g2="afii10085,afii10110"
	k="3" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10053,afii10032,afii10035"
	k="7" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10062,afii10037"
	k="16" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10024"
	k="-21" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10017"
	k="11" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10054"
	k="-6" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10039"
	k="5" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10049"
	k="-10" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10030"
	k="10" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10066"
	k="-31" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10089"
	k="4" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10069"
	k="7" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10099"
	k="5" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10102"
	k="-7" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10073,afii10095,afii10846"
	k="-9" />
    <hkern g1="afii10072,afii10076,afii10109"
	g2="afii10086"
	k="-4" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10060,afii10036,afii10044"
	k="19" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10053,afii10032,afii10035"
	k="12" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-4" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10054"
	k="18" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10039"
	k="14" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10066"
	k="15" />
    <hkern g1="afii10066,afii10080,afii10082,afii10095,afii10096,afii10846"
	g2="afii10089"
	k="-3" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10051"
	k="-6" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10025,afii10047"
	k="3" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10038"
	k="24" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10060,afii10036,afii10044"
	k="8" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10057"
	k="-6" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10053,afii10032,afii10035"
	k="11" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10062,afii10037"
	k="6" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10024"
	k="-16" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10017"
	k="9" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="11" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10039"
	k="-9" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10030"
	k="-13" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10066"
	k="-8" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10089"
	k="9" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10099"
	k="5" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10073,afii10095,afii10846"
	k="19" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10086"
	k="6" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10077,afii10106"
	k="57" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10084,afii10092"
	k="17" />
    <hkern g1="afii10069,afii10088,afii10091"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="15" />
    <hkern g1="afii10108"
	g2="afii10041"
	k="18" />
    <hkern g1="afii10108"
	g2="afii10051"
	k="9" />
    <hkern g1="afii10108"
	g2="afii10060,afii10036,afii10044"
	k="-7" />
    <hkern g1="afii10108"
	g2="afii10057"
	k="13" />
    <hkern g1="afii10108"
	g2="afii10053,afii10032,afii10035"
	k="12" />
    <hkern g1="afii10108"
	g2="afii10024"
	k="7" />
    <hkern g1="afii10108"
	g2="afii10017"
	k="13" />
    <hkern g1="afii10108"
	g2="afii10054"
	k="-3" />
    <hkern g1="afii10108"
	g2="afii10039"
	k="5" />
    <hkern g1="afii10108"
	g2="afii10049"
	k="-3" />
    <hkern g1="afii10085,afii10110"
	g2="afii10038"
	k="-3" />
    <hkern g1="afii10085,afii10110"
	g2="afii10058,afii10029"
	k="-11" />
    <hkern g1="afii10085,afii10110"
	g2="afii10057"
	k="-58" />
    <hkern g1="afii10085,afii10110"
	g2="afii10077,afii10106"
	k="21" />
    <hkern g1="afii10085,afii10110"
	g2="afii10084,afii10092"
	k="-14" />
    <hkern g1="afii10085,afii10110"
	g2="afii10105"
	k="-20" />
    <hkern g1="afii10085,afii10110"
	g2="afii10085,afii10110"
	k="13" />
    <hkern g1="afii10085,afii10110"
	g2="afii10097"
	k="-21" />
    <hkern g1="afii10085,afii10110"
	g2="afii10072"
	k="15" />
    <hkern g1="afii10085,afii10110"
	g2="afii10065"
	k="-9" />
    <hkern g1="afii10085,afii10110"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="3" />
    <hkern g1="afii10085,afii10110"
	g2="comma,period,ellipsis"
	k="13" />
    <hkern g1="afii10085,afii10110"
	g2="guillemotleft,guilsinglleft"
	k="50" />
    <hkern g1="afii10085,afii10110"
	g2="quoteright,quotedblright"
	k="-12" />
    <hkern g1="afii10067,afii10073"
	g2="afii10021"
	k="36" />
    <hkern g1="afii10067,afii10073"
	g2="afii10051"
	k="-31" />
    <hkern g1="afii10067,afii10073"
	g2="afii10038"
	k="-8" />
    <hkern g1="afii10067,afii10073"
	g2="afii10058,afii10029"
	k="21" />
    <hkern g1="afii10067,afii10073"
	g2="afii10060,afii10036,afii10044"
	k="14" />
    <hkern g1="afii10067,afii10073"
	g2="afii10102"
	k="6" />
    <hkern g1="afii10067,afii10073"
	g2="afii10073,afii10095,afii10846"
	k="-11" />
    <hkern g1="afii10067,afii10073"
	g2="afii10086"
	k="11" />
    <hkern g1="afii10067,afii10073"
	g2="comma,period,ellipsis"
	k="4" />
    <hkern g1="afii10067,afii10073"
	g2="hyphen,uni00AD,endash,emdash"
	k="-11" />
    <hkern g1="afii10067,afii10073"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="afii10104"
	g2="afii10021"
	k="3" />
    <hkern g1="afii10104"
	g2="afii10051"
	k="3" />
    <hkern g1="afii10104"
	g2="afii10089"
	k="17" />
    <hkern g1="afii10104"
	g2="afii10069"
	k="16" />
    <hkern g1="afii10104"
	g2="afii10099"
	k="49" />
    <hkern g1="afii10104"
	g2="afii10086"
	k="12" />
    <hkern g1="afii10104"
	g2="afii10077,afii10106"
	k="43" />
    <hkern g1="afii10104"
	g2="afii10084,afii10092"
	k="24" />
    <hkern g1="afii10104"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="40" />
    <hkern g1="afii10104"
	g2="afii10108"
	k="17" />
    <hkern g1="afii10104"
	g2="afii10087"
	k="34" />
    <hkern g1="afii10104"
	g2="afii10065"
	k="11" />
    <hkern g1="afii10104"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="6" />
    <hkern g1="afii10104"
	g2="afii10103"
	k="9" />
    <hkern g1="afii10104"
	g2="parenright,bracketright,braceright"
	k="17" />
    <hkern g1="afii10104"
	g2="colon,semicolon"
	k="8" />
    <hkern g1="afii10104"
	g2="guillemotleft,guilsinglleft"
	k="11" />
    <hkern g1="afii10104"
	g2="quoteright,quotedblright"
	k="13" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10041"
	k="9" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10021"
	k="17" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10051"
	k="9" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10030"
	k="-6" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10066"
	k="16" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10102"
	k="12" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10077,afii10106"
	k="4" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10085,afii10110"
	k="9" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="-6" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="afii10078"
	k="6" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="hyphen,uni00AD,endash,emdash"
	k="-3" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="guillemotright,guilsinglright"
	k="4" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="quoteright,quotedblright"
	k="4" />
    <hkern g1="colon,semicolon"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="22" />
    <hkern g1="colon,semicolon"
	g2="afii10054"
	k="3" />
    <hkern g1="colon,semicolon"
	g2="afii10039"
	k="36" />
    <hkern g1="colon,semicolon"
	g2="afii10066"
	k="3" />
    <hkern g1="colon,semicolon"
	g2="afii10089"
	k="17" />
    <hkern g1="colon,semicolon"
	g2="afii10069"
	k="7" />
    <hkern g1="colon,semicolon"
	g2="afii10102"
	k="23" />
    <hkern g1="colon,semicolon"
	g2="afii10073,afii10095,afii10846"
	k="11" />
    <hkern g1="colon,semicolon"
	g2="afii10070,afii10080,afii10083,afii10071,afii10101"
	k="10" />
    <hkern g1="colon,semicolon"
	g2="afii10097"
	k="6" />
    <hkern g1="colon,semicolon"
	g2="afii10072"
	k="6" />
    <hkern g1="colon,semicolon"
	g2="afii10087"
	k="11" />
    <hkern g1="colon,semicolon"
	g2="afii10103"
	k="3" />
    <hkern g1="colon,semicolon"
	g2="colon,semicolon"
	k="13" />
    <hkern g1="colon,semicolon"
	g2="hyphen,uni00AD,endash,emdash"
	k="11" />
    <hkern g1="colon,semicolon"
	g2="guillemotright,guilsinglright"
	k="12" />
    <hkern g1="colon,semicolon"
	g2="guillemotleft,guilsinglleft"
	k="9" />
    <hkern g1="colon,semicolon"
	g2="quoteright,quotedblright"
	k="10" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10017"
	k="-50" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10054"
	k="17" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10039"
	k="31" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10049"
	k="-52" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10030"
	k="84" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10066"
	k="17" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10089"
	k="-15" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10099"
	k="73" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10077,afii10106"
	k="18" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10108"
	k="64" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10085,afii10110"
	k="-27" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10065"
	k="41" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="-4" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="afii10078"
	k="12" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="parenright,bracketright,braceright"
	k="-27" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="colon,semicolon"
	k="9" />
    <hkern g1="hyphen,uni00AD,endash,emdash"
	g2="comma,period,ellipsis"
	k="34" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10060,afii10036,afii10044"
	k="6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10057"
	k="23" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10053,afii10032,afii10035"
	k="-11" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10017"
	k="19" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="-19" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10054"
	k="32" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10049"
	k="-6" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10066"
	k="34" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10077,afii10106"
	k="19" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10084,afii10092"
	k="30" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10105"
	k="-9" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10072"
	k="-4" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10087"
	k="11" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="-9" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="afii10078"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10025,afii10047"
	k="35" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10038"
	k="3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10058,afii10029"
	k="19" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10060,afii10036,afii10044"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10062,afii10037"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10024"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10023,afii10052,afii10055,afii10056,afii10059,afii10061,afii10145,afii10018,afii10019,afii10020,afii10022,afii10026,afii10027,afii10028,afii10031,afii10033,afii10034,afii10040,afii10042,afii10043,afii10045,afii10046,afii10048,afii10050"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10054"
	k="9" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10039"
	k="9" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10030"
	k="15" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10066"
	k="15" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10089"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10102"
	k="6" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10073,afii10095,afii10846"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10086"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10077,afii10106"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10108"
	k="-13" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10085,afii10110"
	k="9" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10072"
	k="9" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10087"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10065"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10067,afii10068,afii10074,afii10075,afii10076,afii10079,afii10081,afii10082,afii10088,afii10090,afii10091,afii10093,afii10094,afii10096,afii10100,afii10104,afii10107,afii10109,afii10193,afii10098"
	k="11" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10078"
	k="13" />
    <hkern g1="guillemotright,guilsinglright"
	g2="afii10103"
	k="3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="parenright,bracketright,braceright"
	k="4" />
    <hkern g1="guillemotright,guilsinglright"
	g2="colon,semicolon"
	k="3" />
    <hkern g1="guillemotright,guilsinglright"
	g2="comma,period,ellipsis"
	k="4" />
    <hkern g1="quoteleft,quotedblleft"
	g2="afii10025,afii10047"
	k="30959" />
  </font>
</defs></svg>
