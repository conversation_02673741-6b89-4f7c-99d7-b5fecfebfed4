/*Wonderslate color themes*/
/* NOTES:-> When we are working on utkarsh comment above color themes and use below themes*/
/*eUtkarsh color themes*/
/*Home page*/
/*@ws-white:#ffffff;
@ws-darkBlack:#444444;
@ws-lightOrange:#231F20;
@ws-darkOrange:#231F20;
@ws-darkBlue:#231F20;

@ws-red:#B72319;

@ws-gradient-start:#30C465;
@ws-gradient-end:#3AE878;

@ws-border:#EDEDED;

@blue:#2F80ED;

@ws-fadebg:#FAFAFA;

@ws-caret:#010101;

@ws-blue-start:#2EBAC6;
@ws-blude-end:#4DDCE8;

@ws-bluePrimary:#1F70B5;

@ws-transparentOrange: rgba(35, 31, 32, 0.3);*/
/*evidya*/
/*@ws-white:#ffffff;
@ws-darkBlack:#444444;
@ws-lightOrange:#1F419B;
@ws-darkOrange:#1F419B;
@ws-darkBlue:#2EBAC6;

@ws-red:#B72319;

@ws-gradient-start:#30C465;
@ws-gradient-end:#3AE878;

@ws-border:#EDEDED;

@blue:#2F80ED;

@ws-fadebg:#FAFAFA;

@ws-caret:#010101;

@ws-blue-start:#2EBAC6;
@ws-blude-end:#4DDCE8;

@ws-bluePrimary:#1F70B5;

@ws-transparentOrange: rgba(31, 65, 155, 0.5);*/
/*Ramaiah*/
/*Home page*/
/*-Arihant-----*/
/*@ws-white:#ffffff;
@ws-darkBlack:#444444;
@ws-lightOrange:#F79420;
@ws-darkOrange:#CF6C00;
@ws-darkBlue:#2EBAC6;


@ws-red:#B72319;

@ws-border:#EDEDED;

@blue:#2F80ED;

@ws-fadebg:#FAFAFA;

@ws-caret:#010101;

@ws-blue-start:#2EBAC6;
@ws-blude-end:#4DDCE8;

@ws-bluePrimary:#1F70B5;

@ws-transparentOrange: rgba(247, 148, 32, 0.5);
@ws-gradient-start:#FF3448;
@ws-gradient-end:#FF9B26;*/
@font-face {
  src: url('../../fonts/Kruti_Dev_010.ttf');
  font-family: "Kruti Dev 010";
}
@font-face {
  font-family: 'Rubik-medium';
  src: url('../../stylesheets/landingpage/fonts/Rubik-Medium.ttf');
}
@font-face {
  font-family: 'Rubik', sans-serif;
  src: url('../../stylesheets/landingpage/fonts/Rubik-Regular.ttf');
}
@font-face {
  font-family: 'Merriweather';
  src: url('../../stylesheets/landingpage/fonts/Merriweather-Regular.ttf');
}
html,
body,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
caption,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font-family: 'Rubik', sans-serif;
  /*vertical-align: baseline;*/
}
/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
table {
  margin: 0 auto;
  line-height: 1.5;
}
table,
td {
  border: 1px solid #EEEEEE;
  text-align: center;
}
@media screen and (max-width: 768px) {
  table {
    width: 90% !important;
    margin: 0px 15px 15px 15px;
    line-height: 1.5;
  }
  .indication-wrapper {
    position: fixed;
    width: 75%;
    z-index: 99;
    background: #ffffff;
    padding-bottom: 20px;
  }
  .que-side-menu .nav-tabs {
    position: fixed;
    background: #ffffff;
    width: 75%;
    top: 5rem;
    z-index: 99;
  }
  .que-side-menu .tab .tab-content {
    margin-top: 7rem;
  }
}
p,
a,
.table td,
.table th {
  font-family: 'Rubik', sans-serif;
  font-size: 14px;
}
.result-menu {
  position: fixed;
  width: 100%;
  z-index: 99;
  top: 0;
  background: #ffffff;
  display: none;
}
.result-menu > div {
  height: 56px;
}
.result-menu > div i {
  cursor: pointer;
}
.result-menu > div h2 {
  color: #444444;
  font-size: 16px;
  font-family: 'Rubik-medium';
  font-weight: 500;
}
.result-menu .language {
  background-image: url('../../images/mobile/language1.svg');
  width: 24px;
  height: 24px;
  background-size: contain;
  background-color: transparent;
  background-repeat: no-repeat;
  border: none;
  margin-right: 1rem;
  outline: 0;
}
.result-menu .language.rotate {
  background-image: url('../../images/mobile/language.svg');
}
.sub-header {
  background: #ffffff;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: fixed;
  width: 100%;
  z-index: 99;
  top: 0;
}
.sub-header .submit {
  /*height: 56px;*/
}
.sub-header .submit .container {
  /*height: 100%;*/
}
.sub-header .submit .container > div {
  /*height: 100%;*/
}
.sub-header .submit .btn-submit {
  background: none;
  border: none;
  color: #444444;
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  cursor: pointer;
  outline: 0;
  margin-bottom: 5px;
}
.sub-header .submit .btn-submit:hover {
  color: #F79420;
}
.sub-header .submit select {
  border: none;
  background: none;
  height: auto;
  outline: none;
  font-size: 12px;
  font-weight: bold;
}
.sub-header .options {
  height: 32px;
  border-top: 0.5px solid rgba(68, 68, 68, 0.4);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sub-header .options p {
  font-size: 13px;
  color: #444444;
}
.sub-header .options p span {
  font-family: 'Merriweather';
}
.sub-header .options .menu-wrapper {
  display: flex;
}
.sub-header .options .menu-wrapper .language {
  background-image: url('../../images/mobile/language1.svg');
  width: 24px;
  height: 24px;
  background-size: contain;
  background-color: transparent;
  background-repeat: no-repeat;
  border: none;
  margin-right: 1rem;
  outline: 0;
}
.sub-header .options .menu-wrapper .language.rotate {
  background-image: url('../../images/mobile/language.svg');
}
.sub-header .options .menu-wrapper .menu {
  background-image: url('../../images/mobile/ham-icon.svg');
  width: 24px;
  height: 24px;
  background-size: contain;
  background-color: transparent;
  background-repeat: no-repeat;
  border: none;
}
.sub-header #sections {
  border: none;
  outline: none;
  padding: 0 0.8rem;
  height: auto;
  font-family: 'Rubik-medium';
  font-weight: 500;
}
.sub-header #sections:focus {
  outline: none;
}
.sub-header .timer {
  font-size: 12px;
  color: rgba(24, 171, 0, 1);
  font-family: 'Merriweather';
}
.mt-fixed {
  padding-top: 1rem;
}
.close-menu {
  display: none;
  outline: none;
  position: absolute;
  top: 4rem;
  left: 18%;
  z-index: 9999;
  background-color: transparent;
  border: none;
}
.close-menu span {
  background-image: url('../../images/mobile/closeIcon.svg');
  background-size: contain;
  background-repeat: no-repeat;
  width: 24px;
  height: 24px;
  display: block;
}
.que-side-menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 0;
  height: 100vh;
  background: #ffffff;
  box-shadow: -4px 5px 4px rgba(0, 0, 0, 0.25);
  z-index: 999;
  overflow-x: hidden;
  overflow-y: auto;
  padding-bottom: 1rem;
}
.que-side-menu .indicator {
  margin-top: 1rem;
}
.que-side-menu .indicator .answered .circle {
  background: #2F80ED;
}
.que-side-menu .indicator .unanswered .circle {
  background: #fff;
  border: 1px solid #444444;
}
.que-side-menu .indicator .review .circle {
  background: #A056E5;
}
.que-side-menu .indicator .notseen .circle {
  background: #888888;
}
.que-side-menu .indicator .circle {
  height: 15px;
  width: 15px;
  border-radius: 35px;
  display: block;
  margin-right: 10px;
}
.que-side-menu .indicator p {
  color: #444444;
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  display: flex;
}
.que-side-menu .tab {
  display: none;
  margin-top: 1.5rem;
}
.que-side-menu .tab .nav-tabs {
  background: #F8F8F8;
  border: none;
  display: flex;
  justify-content: space-evenly;
}
.que-side-menu .tab .nav-tabs .nav-item.show .nav-link,
.que-side-menu .tab .nav-tabs .nav-link.active {
  border: none;
  background: none;
  border-bottom: 2px solid #F79420 !important;
  font-family: 'Rubik-medium', sans-serif;
  font-size: 14px;
  color: #F79420;
}
.que-side-menu .tab .nav-tabs .nav-item .nav-link,
.que-side-menu .tab .nav-tabs .nav-link {
  border-bottom: 2px solid transparent;
  font-family: 'Rubik-medium';
  font-weight: 500;
  font-size: 14px;
}
.hideref {
  color: transparent;
}
#sectionSelectionDiv select {
  /*width: 160px !important;*/
}
#sectionSelectionDiv select option {
  width: 80px;
}
#question-block .read {
  color: rgba(68, 68, 68, 0.72);
  font-size: 12px;
  margin-bottom: 4px;
}
#question-block .direction-passage {
  color: #444444;
  font-size: 14px;
  margin-bottom: 1rem;
}
#question-block .total-direction-que {
  color: rgba(68, 68, 68, 0.72);
  font-size: 12px;
  margin-top: 2rem;
}
#question-block .question-wrapper {
  background: #ffffff;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.22);
  margin-top: 2rem;
}
#question-block .question-wrapper table,
#question-block .question-wrapper td {
  border: 1px solid #444444;
}
#question-block .question-wrapper ul {
  list-style: unset;
  padding: 0px 15px 15px 15px;
  margin-left: 1rem;
}
#question-block .question-wrapper > P {
  padding: 5px 15px;
  line-height: 1.5;
}
#question-block .question-wrapper .que-menu {
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
#question-block .question-wrapper .que-menu span {
  font-family: 'Rubik-medium';
  color: #444444;
  font-weight: 500;
}
#question-block .question-wrapper .question {
  color: #444444;
  padding: 0px 15px 15px 15px;
  line-height: 1.5;
}
#question-block .question-wrapper .options-string .answer {
  margin-bottom: 0;
  line-height: 1.5;
}
#question-block .question-wrapper .options-string label {
  display: flex;
  align-items: center;
  min-height: 70px;
  border-bottom: 0.5px solid rgba(68, 68, 68, 0.4);
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
  overflow: hidden;
  overflow-x: auto;
}
#question-block .question-wrapper .options-string label input[type=radio] {
  display: none;
}
#question-block .question-wrapper .options-string.active {
  border-bottom: 3px solid #2EBAC6;
}
#question-block .question-wrapper .options-string.active label {
  border-bottom: 0.5px solid transparent;
}
#question-block .question-wrapper .options-string.active .choice {
  background: #2EBAC6;
}
#question-block .question-wrapper .options-string .choice {
  background: rgba(68, 68, 68, 0.4);
  width: 24px;
  height: 24px;
  border-radius: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
  font-family: 'Merriweather';
  font-size: 12px;
  margin-right: 1rem;
}
#question-block .question-wrapper:last-child {
  margin-bottom: 2rem;
}
#question-block .error-btn {
  background-image: url('../../images/mobile/error.svg');
  width: 15px;
  height: 15px;
  background-size: contain;
  background-color: transparent;
  background-repeat: no-repeat;
  border: none;
  margin-right: 1rem;
}
#question-block .bookmark-btn {
  width: 15px;
  height: 15px;
  background-size: contain;
  background-color: transparent;
  background-repeat: no-repeat;
  border: none;
}
#question-block .bookmark-btn i {
  color: rgba(68, 68, 68, 0.6);
  font-size: 18px;
}
#question-block .bookmark-btn i.bookmark {
  display: none;
  color: #A056E5;
}
#question-block .bookmark-btn.marked i {
  display: none;
}
#question-block .bookmark-btn.marked i.bookmark {
  display: block;
}
#question-block .btn-wrapper {
  display: flex;
  justify-content: space-between;
}
#question-block .border-start p {
  border-bottom: 0.5px solid rgba(68, 68, 68, 0.4);
  height: 1px;
}
#question-block > .container {
  margin-top: 1rem;
}
#question-block > .container p {
  line-height: 1.2;
  font-style: italic;
}
.overlay-container {
  width: 100%;
  height: 100vh;
  background: #444444;
  opacity: 0.8;
  position: absolute;
  top: 0;
  z-index: 99;
  overflow: hidden;
  display: none;
}
.list .que-wrapper .que-no-list {
  font-size: 14px;
  color: #ffffff;
  font-family: 'Merriweather';
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #2EBAC6;
  margin-right: 1rem;
  border: 2px solid #FFFFFF;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
}
.list .que-wrapper .que-no-list.answered {
  background: #2F80ED;
}
.list .que-wrapper .que-no-list.unanswered {
  background: #fff;
  border: 1px solid #444444;
}
.list .que-wrapper .que-no-list.marked {
  background: #A056E5;
}
.list .que-wrapper .que-no-list.notseen {
  background: #888888;
}
.list .que-wrapper .question p {
  font-size: 16px;
  color: #444444;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 160px;
}
.list .que-wrapper .question p:hover {
  cursor: pointer;
}
.list .que-wrapper .question table:hover,
.list .que-wrapper .question td:hover,
.list .que-wrapper .question tr:hover {
  cursor: pointer;
}
.grid {
  margin-top: 0rem;
}
.grid div.que-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 0.5rem;
}
.grid #accordion .card-link {
  margin: 1rem 0;
  display: block;
  position: relative;
  font-family: 'Rubik-medium', sans-serif;
  color: #444444;
}
.grid #accordion .card-link:hover {
  color: #444444;
}
.grid #accordion .card-link::after {
  width: 10px;
  height: 10px;
  content: url('../../images/mobile/arrow-up.svg');
  position: absolute;
  right: 12px;
  top: -4px;
  opacity: 0.6;
}
.grid #accordion .card-link.collapsed::after {
  width: 10px;
  height: 10px;
  content: url('../../images/mobile/arrow-down.svg');
  position: absolute;
  right: 12px;
  top: -4px;
  opacity: 0.6;
}
.grid .progress {
  height: 4px;
}
.grid div.que-no {
  font-size: 14px;
  color: #444444;
  font-family: 'Merriweather';
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #ffffff;
  margin-right: 1rem;
  margin-top: 1rem;
  border: 1px solid #444444;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
}
.grid div.que-no a {
  color: #444444;
  text-decoration: none;
}
.grid div.que-no.answered {
  background: #2F80ED;
  border: none;
}
.grid div.que-no.answered a {
  color: #ffffff;
}
.grid div.que-no.unanswered {
  background: #fff;
  border: 1px solid #444444;
}
.grid div.que-no.unanswered a {
  color: #444444;
}
.grid div.que-no.marked {
  background: #A056E5;
  border: none;
}
.grid div.que-no.marked a {
  color: #ffffff;
}
.grid div.que-no.notseen {
  background: #888888;
}
.grid div.que-no.notseen a {
  color: #ffffff;
}
#answer-block {
  padding-bottom: 2rem;
  overflow-x: hidden;
}
#answer-block .button-wrapper a {
  color: #ffffff;
  background: #2EBAC6;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24);
  border-radius: 2px;
  border: none;
  height: 28px;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  padding: 0 0.8rem;
  display: flex;
  align-items: center;
  text-decoration: none;
  width: auto;
  margin: 1rem auto;
  justify-content: center;
}
#answer-block .button-wrapper a:hover {
  background: #2EBAC6 !important;
}
#answer-block .button-wrapper a:focus {
  background: #2EBAC6 !important;
}
#answer-block .button-wrapper a:active {
  background: #2EBAC6 !important;
}
#answer-block .rank-wrapper {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
  text-align: center;
}
#answer-block .rank-wrapper .medal {
  background: url("../../images/mobile/medalResult.svg") center center no-repeat;
  background-size: contain;
  width: 32px;
  height: 32px;
  margin: 0 auto;
}
#answer-block .rank-wrapper p {
  margin-top: 0.5rem;
  font-family: 'Rubik', sans-serif;
}
#answer-block .rank-wrapper p.rankText {
  color: rgba(68, 68, 68, 0.4);
}
#answer-block .rank-wrapper p.dateText {
  color: #444444;
  font-weight: 500;
}
.practice-score-container {
  background: #2EBAC6;
  padding: 2rem 0;
  color: #ffffff;
  text-align: center;
  display: none;
}
.practice-score-container .practice-score-string p {
  font-family: 'Merriweather';
  color: #ffffff;
  font-size: 20px;
}
.practice-score-container .practice-score-string .board p {
  color: #ffffff;
}
.practice-score-container .practice-score-string .board > div p {
  color: rgba(0, 0, 0, 0.6);
  font-size: 12px;
}
.practice-score-container .practice-score-string .board > div p.practice-score-string-score {
  font-size: 18px;
  color: #ffffff;
  font-family: 'Merriweather';
  margin-top: 2px;
}
.practice-score-container .medal-picture img {
  height: 100px;
}
.answer-summary {
  margin: 1rem 0;
  padding-bottom: 0.5rem;
  box-shadow: 0px 2px 1px rgba(0, 0, 0, 0.1);
}
.answer-summary .summary-heading {
  text-align: center;
  font-size: 16px ;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  font-weight: bold;
}
.answer-summary .short-heading {
  text-align: center;
  font-size: 14px ;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  margin-top: 3px;
}
.answer-summary .share {
  font-size: 12px;
  color: #F79420;
  font-family: 'Rubik', sans-serif;
}
.answer-summary .score-summary {
  margin-top: 1rem;
}
.answer-summary .score-summary > div {
  text-align: center;
}
.answer-summary .score-summary > div:last-child > div {
  color: #444444;
}
.answer-summary .score-summary > div > div {
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
}
.answer-summary .score-summary .correct-answers {
  height: 64px;
  width: 64px;
  border-radius: 8px;
  background: #2EBAC6;
}
.answer-summary .score-summary .wrong-answers {
  height: 64px;
  width: 64px;
  border-radius: 8px;
  background: linear-gradient(270deg, #97160D 0%, #BF3E35 100%);
}
.answer-summary .score-summary .skipped-answers {
  height: 64px;
  width: 64px;
  border-radius: 8px;
  background: #ffffff;
  border: 1px solid rgba(68, 68, 68, 0.72);
}
.answer-summary .accuracy-summary {
  margin-top: 1rem;
  padding-bottom: 0.5rem;
}
.answer-summary .accuracy-summary > div {
  display: flex;
  align-items: center;
}
.answer-summary .accuracy-summary > div > div {
  margin-left: 0.3rem;
}
.answer-summary .accuracy-summary > div > div > span {
  margin-left: 5px;
  display: block;
  text-align: center;
  color: #444444;
  font-size: 14px;
}
.answer-summary .accuracy-summary > div > div > span + p {
  color: rgba(68, 68, 68, 0.4);
  font-size: 10px;
  text-align: center;
}
.answer-summary .accuracy-summary .time-taken {
  background: url("../../images/mobile/clock.svg") center center no-repeat;
  background-size: contain;
  width: 32px;
  height: 32px;
}
.answer-summary .accuracy-summary .answer-accuracy {
  background: url("../../images/mobile/accuracy.svg") center center no-repeat;
  background-size: contain;
  width: 32px;
  height: 32px;
}
.answer-summary .accuracy-summary .total-hours {
  background: url("../../images/mobile/hour.svg") center center no-repeat;
  background-size: contain;
  width: 32px;
  height: 32px;
}
.answer-summary .button-wrapper {
  display: flex;
  justify-content: space-around;
  margin: 1rem;
}
.answer-summary .button-wrapper .reattempt {
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: #F79420;
  background: none;
  border: none;
}
.answer-summary .button-wrapper .viewsolution {
  color: #ffffff;
  background: #2EBAC6;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24);
  border-radius: 2px;
  border: none;
  height: 28px;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  padding: 0 0.8rem;
  display: flex;
  align-items: center;
  text-decoration: none;
}
.analysis {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
}
.analysis > div h2 {
  text-align: center;
  font-weight: 500;
  font-size: 14px;
  font-family: 'Rubik-medium';
}
.analysis .nav-tabs .nav-link {
  color: #444444;
}
.analysis .nav-tabs .nav-item.show .nav-link,
.analysis .nav-tabs .nav-link.active {
  border: none;
  background: none;
  border-bottom: 2px solid #F79420;
  color: #F79420;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
}
.analysis ul {
  background: rgba(237, 237, 237, 0.5);
  border: none;
  flex-wrap: nowrap;
  overflow-y: auto;
  margin-top: 1rem;
}
.analysis ul li {
  white-space: nowrap;
  list-style-type: none;
}
.analysis .tab-content {
  margin-top: 1rem;
}
.analysis .tab-content > .tab-pane {
  height: 1px;
  overflow: hidden;
  display: block;
  visibility: hidden;
}
.analysis .tab-content > .active {
  height: auto;
  overflow: auto;
  visibility: visible;
}
.analysis .tab-content .button-wrapper {
  padding-bottom: 1rem;
  text-align: center;
}
.analysis .tab-content .button-wrapper .viewsolution {
  color: #ffffff;
  background: #2EBAC6;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24);
  border-radius: 2px;
  border: none;
  height: 28px;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  padding: 0 0.8rem;
  display: flex;
  align-items: center;
  text-decoration: none;
  width: 130px;
  justify-content: center;
  margin: 0 auto;
}
.analysis > div:first-child {
  width: 100%;
}
.notes-assignent {
  background: url("../../images/mobile/score.svg") center center no-repeat;
  background-size: contain;
  width: 24px;
  height: 24px;
}
.rank .notes-assignent {
  background: url("../../images/mobile/scorrerank.svg") center center no-repeat;
  background-size: contain;
  width: 24px;
  height: 24px;
}
.score {
  width: 50px;
  height: 50px;
  background: #0F4C6F;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}
.analysis-summary,
.suggestions-for-user {
  display: none;
}
.graph {
  height: 200px;
}
.top-scores {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
}
.top-scores h4 {
  color: rgba(68, 68, 68, 0.7);
  font-size: 12px;
  padding-bottom: 0.5rem;
  font-family: 'Rubik-medium';
  font-weight: 500;
}
.top-scores a {
  color: #2F80ED;
  font-size: 12px;
}
.top-scores h2 {
  color: #444444;
  font-size: 14px;
  font-weight: 500;
}
.top-scores .t-header {
  padding: 0 1.3rem;
}
.rank1 {
  background: url('../../images/mobile/rank1.svg') center no-repeat;
  background-size: contain;
  width: 17px;
  height: 20px;
  display: block;
  margin: 0 auto;
}
.rank2 {
  background: url('../../images/mobile/rank2.svg') center no-repeat;
  background-size: contain;
  width: 17px;
  height: 20px;
  display: block;
  margin: 0 auto;
}
.rank3 {
  background: url('../../images/mobile/rank3.svg') center no-repeat;
  background-size: contain;
  width: 17px;
  height: 20px;
  display: block;
  margin: 0 auto;
}
.feedback {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
}
.feedback h2 {
  color: rgba(68, 68, 68, 0.7);
  font-size: 12px;
  padding-bottom: 0.5rem;
  font-family: 'Rubik-medium';
  font-weight: 500;
  margin-left: 0.5rem;
}
.feedback p {
  margin-left: 0.5rem;
  font-size: 20px;
}
.feedback .starRating {
  display: flex;
  flex-direction: row-reverse;
  justify-content: center;
  margin-top: 1.5rem;
}
.feedback .starRating input {
  display: none;
}
.feedback .starRating:not(:checked) > label {
  width: 1em;
  padding: 0 0.1em;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
  font-size: 54px;
  line-height: 1.2;
  color: #ddd;
}
.feedback .starRating:not(:checked) > label:before {
  content: '★ ';
}
.feedback .starRating:not(:checked) > label:hover {
  color: gold;
}
.feedback .starRating:not(:checked) > label:hover ~ label {
  color: gold;
}
.feedback .starRating > input:checked + label:hover {
  color: #ea0;
}
.feedback .starRating > input:checked + label:hover ~ label {
  color: #ea0;
}
.feedback .starRating > input:checked ~ label {
  color: #ea0;
}
.feedback .starRating > input:checked ~ label:hover {
  color: #ea0;
}
.feedback .starRating > input:checked ~ label:hover ~ label {
  color: #ea0;
}
.feedback .starRating > label:hover ~ input:checked ~ label {
  color: #ea0;
}
.feedback .rating-submit {
  background: none;
  color: #F79420;
  border: none;
  padding: 1rem;
  text-align: center;
  width: 100%;
}
.feedback textarea {
  padding: 1rem;
  min-height: 90px;
  border-radius: 4px;
  width: 100%;
  border: 0.5px solid rgba(68, 68, 68, 0.4);
}
/*the container must be positioned relative:*/
.setters {
  position: absolute;
  left: 85px;
  top: 75px;
}
.minutes-set {
  float: left;
  margin-right: 28px;
}
.seconds-set {
  float: right;
}
.controlls {
  position: absolute;
  left: 75px;
  top: 105px;
  text-align: center;
}
.display-remain-time {
  font-family: 'Roboto';
  font-weight: 100;
  font-size: 65px;
  color: #F7958E;
}
#pause {
  outline: none;
  background: transparent;
  border: none;
  position: relative;
}
.play::before {
  display: block;
  content: "";
  position: absolute;
  top: -9px;
  left: -19px;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 8px solid #A8A8A8;
}
.pause::after {
  content: "";
  position: absolute;
  top: -8px;
  left: -22px;
  width: 9px;
  height: 10px;
  background-color: transparent;
  border-radius: 1px;
  border: 2px solid #A8A8A8;
  border-top: none;
  border-bottom: none;
}
#pause:hover {
  opacity: 0.8;
}
.e-c-base {
  fill: none;
  stroke: #A8A8A8;
  stroke-width: 12px;
}
.e-c-progress {
  fill: none;
  stroke: #F79420;
  stroke-width: 12px;
  transition: stroke-dashoffset 0.7s;
}
.e-c-pointer {
  fill: #FFF;
  stroke: #F7958E;
  stroke-width: 2px;
}
#time-progress {
  transform: rotate(-90deg);
}
#e-pointer {
  transition: transform 0.7s;
}
#report-que .modal-content {
  border: none;
  border-radius: 0px;
  width: 80%;
  margin: 0px auto;
}
#report-que .modal-header {
  border-bottom: none;
  padding: 0.5rem 1rem;
}
#report-que .modal-footer {
  justify-content: center;
  border-top: none;
  padding: 0.5rem 1rem;
}
#report-que .modal-footer .btn-submit {
  font-family: 'Rubik-medium';
  background: none;
  text-transform: uppercase;
  color: #F79420;
  font-size: 14px;
  cursor: pointer;
}
#report-que .modal-title {
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.4);
  display: flex;
  align-items: center;
  margin-top: 1rem;
}
#report-que .modal-title i {
  margin-left: 10px;
  color: #E95353;
  font-size: 14px;
}
/*checkbox component*/
.containers {
  display: block;
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 14px;
  color: #444444;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.containers input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}
.containers:hover input ~ .checkmark {
  background-color: #ccc;
}
.containers .checkmark:after {
  left: 5px;
  top: 1px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
.containers input:checked ~ .checkmark:after {
  display: block;
}
.containers input:checked ~ .checkmark {
  background-color: #F79420;
}
/* Hide the browser's default checkbox */
.modal-body {
  /* Create a custom checkbox */
  /* Create the checkmark/indicator (hidden when not checked) */
}
.modal-body .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 14px;
  width: 14px;
  background-color: #eee;
}
.modal-body .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}
.modal-body .letusknow {
  border: 1px solid rgba(68, 68, 68, 0.54);
  width: 200px;
  height: 60px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-body .letusknow textarea {
  border: none;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
  outline: none;
}
#continue-test .modal-header {
  border-bottom: none;
}
#continue-test .modal-footer {
  border-top: none;
  justify-content: center;
}
.img-success {
  background: url('../../images/mobile/clapping.svg') center no-repeat;
  height: 40px;
  width: 40px;
  background-size: contain;
  margin: 0 auto;
  margin-bottom: 1rem;
}
#completed-subject {
  text-align: center;
  color: rgba(68, 68, 68, 0.72);
  font-size: 14px;
  font-family: 'Rubik-medium';
  font-weight: 500;
  margin-top: 2rem;
  padding: 0 3rem;
}
.comingup {
  margin-top: 1rem;
  color: rgba(68, 68, 68, 0.4) !important;
  font-size: 12px;
  font-family: 'Rubik-medium';
  font-weight: 500;
  text-transform: uppercase;
  text-align: center;
}
.comingup span {
  color: #444444;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Rubik-medium';
}
.btn-continue {
  width: 75%;
  background: #2EBAC6;
  color: #ffffff;
  text-transform: uppercase;
  font-size: 14px;
  font-family: 'Rubik-medium';
  font-weight: 500;
}
.timeup {
  font-size: 24px;
  color: #B72319;
  font-family: 'Merriweather';
  text-align: center;
  font-weight: bold;
}
#submit-test .modal-header {
  border-bottom: none;
  justify-content: center;
}
#submit-test .modal-header h1 {
  text-align: center;
  color: #444444;
  font-family: 'Merriweather', serif;
  font-weight: bold;
  font-size: 24px;
}
#submit-test .modal-body {
  background: #f8f8f8;
}
#submit-test .modal-body h1 {
  color: #444444;
  font-size: 16px;
  font-family: 'Rubik-medium';
  font-weight: 500;
}
#submit-test .modal-body p.summary {
  font-size: 14px;
  margin: 0;
  padding: 0;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
}
#submit-test .modal-body span {
  font-size: 12px;
  font-weight: normal;
}
#submit-test .modal-body > div {
  margin: 0 auto;
}
#submit-test .modal-body > div > div p {
  font-size: 16px;
  margin-left: 1rem;
}
#submit-test .modal-body > div > div .circle {
  width: 40px;
  height: 40px;
  border-radius: 50px;
  display: inline-block;
}
#submit-test .modal-body > div > div .circle span {
  display: inline-block;
  text-align: center;
  width: 100%;
  margin-top: 0.8rem;
  font-size: 14px;
}
#submit-test .modal-body > div > div .circle.answered {
  background: linear-gradient(41.97deg, #2F80ED 14.96%, #579EFF 100%);
  color: #fff;
}
#submit-test .modal-body > div > div .circle.unanswered {
  border: 2px solid #444444;
  color: #444444;
}
#submit-test .modal-body > div > div .circle.review {
  background: linear-gradient(42.99deg, #A056E5 20.42%, #BF7AFF 100%);
  color: #fff;
}
#submit-test .modal-footer {
  justify-content: space-around;
  border-top: none;
}
#submit-test .modal-footer .Resume {
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  border-radius: 4px;
  color: #fff;
}
#submit-test .modal-footer button {
  background: none;
  border: none;
  color: #444444;
  font-family: 'Rubik-medium';
  font-weight: 500;
  font-size: 14px;
  outline: 0;
}
#submit-test .modal-footer button.submit {
  color: white;
  background:#AC3592 !important;
}
.question-box > .question {
  border-bottom: 1px solid rgba(68, 68, 68, 0.4);
}
.question-box .question p {
  padding: 0.4rem;
  font-size: 14px;
  color: #444444;
  font-family: 'Rubik', sans-serif;
  line-height: 1.5;
}
.question-box .question p.que-no {
  color: #ffffff;
  font-family: 'Rubik-medium';
  color: #444444;
  font-weight: 500;
}
.question-box .your-answer {
  position: relative;
  min-height: 80px;
  display: flex;
  align-items: center;
  border-bottom: 4px solid #B72319;
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
}
.question-box .your-answer > div {
  width: 100%;
  overflow: hidden;
  overflow-x: auto;
}
.question-box .your-answer p span.skipAnswer,
.question-box .your-answer p span.correct-answer-by-user,
.question-box .your-answer p span.wrong-answer-label {
  margin-top: 0.5rem;
}
.question-box .your-answer p.d-flex {
  align-items: center;
  min-height: 40px;
}
.question-box .your-answer p.d-flex span:last-child {
  line-height: 1.2;
}
.question-box .your-answer.ans-correct {
  border-bottom: 4px solid #46B520;
}
.question-box .your-answer.ans-correct .choice {
  display: none;
}
.question-box .your-answer.ans-correct .correct-answer i {
  position: static;
}
.question-box .your-answer.correct-answer-by-user {
  border-bottom: 4px solid #46B520;
}
.question-box .your-answer .choice {
  background: rgba(68, 68, 68, 0.4);
  width: 24px;
  height: 24px;
  border-radius: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff !important;
  font-family: 'Merriweather';
  font-size: 12px;
  margin-right: 1rem;
}
.question-box .wrong-answer-label {
  color: #B72319;
  font-size: 8px;
  text-transform: uppercase;
  padding-bottom: 0.5rem;
  display: block;
}
.question-box .wrong-answer-by-user {
  color: #444444;
  display: flex;
  align-items: center;
  width: 75%;
}
.question-box .wrong-answer-by-user i {
  color: #B72319;
  margin-right: 1rem;
  position: absolute;
  right: 0;
}
.question-box .wrong-answer-by-user i.check {
  display: none;
}
.question-box span.skipAnswer {
  color: #0AAEF9;
  font-size: 8px;
  text-transform: uppercase;
  padding-bottom: 0.5rem;
  display: flex;
  align-items: center;
}
.question-box span.correct-answer-by-user {
  color: #46B520;
  font-size: 8px;
  text-transform: uppercase;
  padding-bottom: 0.5rem;
  display: flex;
  align-items: center;
}
.question-box span.correct-answer-by-user.err-answer {
  color: #444444;
  font-size: 14px;
  text-transform: none;
}
.question-box span.correct-answer-by-user span {
  color: #444444;
}
.question-box span.correct-answer-by-user i {
  color: #B72319;
  margin-right: 1rem;
}
.question-box span.correct-answer-by-user i.check {
  display: block;
  color: #46B520;
}
.question-box span.correct-answer-by-user i.err {
  display: none;
}
.question-box .correct-answer {
  color: #444444;
  display: flex;
  align-items: center;
}
.question-box .correct-answer i {
  color: #46B520;
  margin-right: 1rem;
  position: absolute;
  right: 0;
}
.answer-explanation-row {
  min-height: 80px;
  display: flex;
  align-items: center;
  padding: 1rem 0;
}
.question-box {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  margin-top: 2rem;
  padding: 1rem 0;
}
.show-explanation-btn {
  color:#007bff !important;
  font-size: 8px;
  text-transform: uppercase;
  padding-bottom: 0.5rem;
  display: block;
}
.que-wrappers {
  padding: 1rem 0;
}
.que-wrappers#scrollto > .question-box:first-child {
  margin-top: 1rem;
}
.markbutn {
  display: none;
}
.markbutn + label {
  background: url('../../images/mobile/bookmark.svg') center no-repeat;
  height: 16px;
  width: 14px;
  background-size: contain;
  opacity: 0.7;
  cursor: pointer;
}
.markbutn:checked + label {
  background: url('../../images/mobile/bookmarked.svg') center no-repeat;
  height: 16px;
  width: 14px;
  background-size: contain;
}
.onclickScrollsList {
  border-bottom: 0.5px solid rgba(68, 68, 68, 0.3);
  padding-bottom: 0.5rem;
}
.onclickScrollsList:last-child {
  border: none;
}
.onclickScrollsList:first-child > div {
  padding-top: 0.5rem;
}
#list .question {
  width: 160px;
  padding: 0.5rem 0;
}
#list .question > p {
  display: none;
}
#list .question > p:first-child {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  font-size: 14px;
}
#list .question > p:first-child:empty {
  display: none;
}
#list .question > p:first-child:empty + p {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  font-size: 14px;
}
#list .question > p span {
  font-family: unset;
}
#list .question > p + table {
  display: none;
}
#list .question > p table,
#list .question > p img {
  display: none;
}
#list div.que-no-list {
  font-size: 14px;
  color: #444444;
  font-family: 'Merriweather';
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #ffffff;
  margin-right: 1rem;
  margin-top: 0.3rem;
  border: 1px solid #444444;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: center;
  align-items: center;
}
#list div.que-no-list a {
  color: #444444;
  text-decoration: none;
}
#list div.que-no-list.answered {
  background: #2F80ED;
  border: none;
}
#list div.que-no-list.answered a {
  color: #ffffff;
}
#list div.que-no-list.unanswered {
  background: #fff;
  border: 1px solid #444444;
}
#list div.que-no-list.unanswered a {
  color: #444444;
}
#list div.que-no-list.marked {
  background: #A056E5;
  border: none;
}
#list div.que-no-list.marked a {
  color: #ffffff;
}
#list div.que-no-list.notseen {
  background: #888888;
}
.web-mcq {
  min-height: 100vh;
}
.web-mcq table td {
  text-align: left;
  padding: 5px;
}
.web-mcq .onclickScrollsList:last-child {
  padding-bottom: 4rem;
}
.web-mcq #collapseOne {
  padding-bottom: 5rem;
}
.web-mcq .menu-wrapper .menu {
  display: none;
}
.web-mcq .sub-header {
  position: fixed;
  top: 85px;
  padding-top: 10px;
  /*z-index: 999;*/
}
@media only screen and (min-width: 768px) and (max-width:1024px) {
  .web-mcq .sub-header {
    top: 75px;
  }
  .web-mcq .result-menu{
     top: 75px !important;
   }
}
.web-mcq .result-menu {
  top: 85px;
  padding-top: 10px;
}
.web-mcq .que-side-menu {
  position: unset;
  box-shadow: none;
  z-index: 0;
  border-right: 1px solid #ededed;
}
.web-mcq .que-side-menu .tab {
  display: block;
  padding-bottom: 2rem;
}
.web-mcq .mt-fixed {
  margin-top: 3rem;
  padding-top: 2rem;
}
.web-mcq .grid,
.web-mcq .list {
  margin-top: 0;
}
.web-mcq .web-position {
  height: 100%;
  width: 100%;
  overflow: hidden;
}
/*.web-mcq .web-position > div:first-child {*/
/*  width: 260px;*/
/*  overflow-y: auto;*/
/*  top: 14rem;*/
/*  height: 100vh;*/
/*  position: fixed;*/
/*  padding-bottom: 7rem;*/
/*  overflow-x: auto;*/
/*}*/
/*@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {*/
/*  .web-mcq .web-position > div:first-child {*/
/*    left: 6px;*/
/*  }*/
/*}*/
/*@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {*/
/*  .web-mcq .web-position > div:first-child {*/
/*    top: 0;*/
/*    position: absolute;*/
/*    height: 100%;*/
/*  }*/
/*}*/
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape), only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 767px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 767px) and (orientation : landscape) {
  .web-mcq .menu-wrapper .menu {
    display: block;
  }
  .web-mcq .que-side-menu {
    position: fixed;
    top: 0;
    right: 0px;
    width: 0;
    height: 100vh;
    background: #ffffff;
    box-shadow: -4px 5px 4px rgba(0, 0, 0, 0.25);
    z-index: 999;
    display: none;
  }
  .web-mcq .que-side-menu .close-menu {
    display: none;
    outline: none;
    width: 20px;
    height: 20px;
    border-radius: 50px;
    background: #ffffff;
    border: none;
    top: 4px;
    right: 5px;
    left: unset;
    border: 1px solid #444444;
  }
  .web-mcq .que-side-menu .close-menu span {
    background-image: url('../../images/mobile/close.svg');
    width: 7px;
    height: 7px;
    background-size: contain;
    background-repeat: no-repeat;
    display: block;
  }
  .web-mcq .que-side-menu .indicator {
    margin-top: 1rem;
  }
  .web-mcq .que-side-menu .indicator .answered .circle {
    background: #2F80ED;
  }
  .web-mcq .que-side-menu .indicator .unanswered .circle {
    background: #fff;
    border: 1px solid #444444;
  }
  .web-mcq .que-side-menu .indicator .review .circle {
    background: #A056E5;
  }
  .web-mcq .que-side-menu .indicator .notseen .circle {
    background: #888888;
  }
  .web-mcq .que-side-menu .indicator .circle {
    height: 15px;
    width: 15px;
    border-radius: 35px;
    display: block;
    margin-right: 10px;
  }
  .web-mcq .que-side-menu .indicator p {
    color: #444444;
    font-size: 12px;
    font-family: 'Rubik', sans-serif;
    display: flex;
  }
  .web-mcq .que-side-menu .tab {
    display: none;
    margin-top: 1.5rem;
  }
  .web-mcq .que-side-menu .tab .nav-tabs {
    background: #F8F8F8;
    border: none;
    display: flex;
    justify-content: space-evenly;
  }
  .web-mcq .que-side-menu .tab .nav-tabs .nav-item.show .nav-link,
  .web-mcq .que-side-menu .tab .nav-tabs .nav-link.active {
    border: none;
    background: none;
    border-bottom: 2px solid #F79420 !important;
    font-family: 'Rubik-medium', sans-serif;
    font-size: 14px;
    color: #F79420;
  }
  .web-mcq .que-side-menu .tab .nav-tabs .nav-item .nav-link,
  .web-mcq .que-side-menu .tab .nav-tabs .nav-link {
    border-bottom: 2px solid transparent;
    font-family: 'Rubik-medium';
    font-weight: 500;
    font-size: 14px;
  }
  .arihant .tab-wrappers {
    position: static;
    width: auto;
    top: auto;
    height: 100%;
  }
}
.read-backarrow {
  display: none;
  cursor: pointer;
}
.start-test .header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  background: #FFFFFF;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.12);
  margin-bottom: 2rem;
}
.start-test .header p {
  font-size: 20px;
  font-weight: 500;
}
.start-test .card {
  min-width: 320px;
  min-height: 64px;
  background: #ffffff;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
}
.start-test .card .disclaimer h4 {
  color: rgba(68, 68, 68, 0.72);
  font-family: 'Rubik', sans-serif;
  font-size: 20px;
  font-weight: normal;
  padding: 1rem;
  margin-left: 3rem;
}
.start-test .card .disclaimer p {
  text-align: center;
  font-size: 20px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  padding: 0.5rem 3rem;
}
.start-test .card > .language {
  min-height: 64px;
}
.start-test .card > .language p {
  display: flex;
  align-items: center;
  font-size: 24px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .start-test .card > .language p {
    font-size: 16px;
  }
}
.start-test .card > .language p:before {
  background: url('../../images/mobile/language.svg') no-repeat;
  background-size: contain;
  width: 38px;
  height: 38px;
  content: '';
}
.start-test .card > .language select {
  border: none;
  color: rgba(68, 68, 68, 0.72);
}
.start-test .card .patterns.section p.lang {
  font-size: 18px;
}
.start-test .card .patterns.section p.lang:before {
  background: url('../../images/mobile/secpattern.svg') no-repeat;
  background-size: contain;
  width: 24px;
  height: 24px;
  position: relative;
  content: '';
  left: -15px;
}
.start-test .card .patterns > div {
  border-bottom: 1px solid #ededed;
  padding: 1rem 0;
}
.start-test .card .patterns h4 {
  color: rgba(68, 68, 68, 0.72);
  font-family: 'Rubik', sans-serif;
  font-size: 20px;
  font-weight: normal;
  padding: 1rem;
}
.start-test .card .patterns p {
  font-family: 'Rubik', sans-serif;
  font-size: 24px;
  font-weight: normal;
  color: #444444;
  display: flex;
  justify-content: center;
  align-items: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .start-test .card .patterns p {
    font-size: 18px;
  }
}
.start-test .card .patterns p span {
  font-size: 20px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
}
.start-test .card .patterns p span.positive {
  color: #46B520;
}
.start-test .card .patterns p span.negative {
  color: #B72319;
}
.start-test .card .patterns p:first-child {
  width: 40%;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .start-test .card .patterns p:first-child {
    width: auto;
  }
}
.start-test .card .patterns p:last-child {
  width: 40%;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .start-test .card .patterns p:last-child {
    width: auto;
  }
}
.start-test .card .patterns p.markval {
  font-weight: 500;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .start-test .card .patterns p.noque {
    font-size: 18px;
  }
}
.start-test .card .patterns p.noque:before {
  background: url('../../images/mobile/noque.svg') no-repeat;
  background-size: contain;
  width: 24px;
  height: 24px;
  position: relative;
  content: '';
  left: -15px;
}
.start-test .card .patterns p.timers:before {
  background: url('../../images/mobile/timer.svg') no-repeat;
  background-size: contain;
  width: 24px;
  height: 24px;
  position: relative;
  content: '';
  left: -15px;
}
.start-test .card .patterns p.markss:before {
  background: url('../../images/mobile/marks.svg') no-repeat;
  background-size: contain;
  width: 24px;
  height: 24px;
  position: relative;
  content: '';
  left: -15px;
}
.start-test .starttest-wrapper {
  overflow-y: auto;
}
.start-test .str-test {
  margin-bottom: 4rem;
  min-height: 70vh;
}
.start-test .footer-test {
  height: 87px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
}
.start-test .footer-test > .container {
  height: 87px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.start-test .footer-test .btn-starts {
  background: #2EBAC6;
  border-radius: 4px;
  color: #ffffff;
}
.btn-starts {
  background: #2EBAC6;
  border-radius: 4px;
  color: #ffffff;
}
.btn-starts:hover {
  color: #ffffff;
}
.btn-starts:focus {
  color: #444444;
}
button {
  cursor: pointer;
}
.hidden {
  display: none;
}
.correct-answer-explanation {
  line-height: 1.5;
  font-size: 14px;
}
.ans-neutral {
  background: #ffffff !important;
  border-bottom: 1px solid rgba(68, 68, 68, 0.5) !important;
  min-height: 80px;
  display: flex;
  align-items: center;
}
.ans-neutral p span {
  color: #444444 !important;
}
.ans-neutral p span i {
  color: #444444 !important;
}
.ans-neutral i.err {
  display: none;
}
.ans-tab {
  margin-top: 3rem;
  display: none;
}
.ans-tab .nav-tabs {
  background: rgba(237, 237, 237, 0.3);
  flex-wrap: nowrap;
  white-space: nowrap;
  overflow: hidden;
  overflow-x: auto;
  border: none;
  justify-content: center;
}
.ans-tab .nav-tabs .nav-item.show .nav-link {
  border-color: transparent;
}
.ans-tab .nav-tabs .nav-link {
  color: rgba(68, 68, 68, 0.72);
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  display: flex;
  align-items: center;
}
.ans-tab .nav-tabs .nav-link.active {
  border-color: transparent;
  color: #F79420;
  border-bottom: 3px solid #F79420;
  background: rgba(237, 237, 237, 0.3);
}
.ans-tab .nav-tabs .nav-link span {
  margin-right: 0.3rem;
  font-size: 24px;
}
.ans-tab .nav-tabs .nav-link.correct span {
  color: #29C42E;
}
.ans-tab .nav-tabs .nav-link.incorrect span {
  color: #B72319;
}
.ans-tab .nav-tabs .nav-link.skipped span {
  color: #F2C94C;
}
.medal-picture > div {
  width: 90px;
  height: 90px;
  margin: 0 auto;
  margin-bottom: 1rem;
}
.medal-picture > div.medalnone {
  background: url('../../images/mobile/medal-none.png') center no-repeat;
  background-size: contain;
}
.medal-picture > div.gold {
  background: url('../../images/mobile/medal-gold.png') center no-repeat;
  background-size: contain;
}
.medal-picture > div.silver {
  background: url('../../images/mobile/medal-silver.png') center no-repeat;
  background-size: contain;
}
.medal-picture > div.bronze {
  background: url('../../images/mobile/medal-bronz.png') center no-repeat;
  background-size: contain;
}
#sectionSelection {
  /*width: 150px;*/
}
#width_tmp_select {
  display: none;
}
.directions {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.directions p {
  line-height: 1.5;
}
.question-wrapper + .container p {
  padding-bottom: 0.5rem;
}
.question-wrapper + .container p span {
  line-height: 1.5;
}
.katex {
  line-height: 3 !important;
  white-space: normal !important;
}
.question .katex {
  line-height: 2 !important;
}
.katex .base {
  white-space: normal !important;
  width: unset !important;
}
.web-mcq .que-side-menu .tab .tab-content {
  margin-top: 0rem;
}
.web-mcq .que-side-menu .nav-tabs {
  position: static;
  width: auto;
  top: auto;
}
.video-wrapper {
  position: relative;
  padding-bottom: 50%;
  /* 16:9 */
  padding-top: 25px;
  height: 0;
}
.video-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.flex_st {
  display: flex;
}
.que-wrappers table,
.que-wrappers td {
  border: 1px solid #444444;
  text-align: center;
}
#videoContent .modal-content {
  background: transparent;
  border: none;
}
#videoContent .modal-header {
  border: none;
}
#videoContent .modal-body {
  padding: 0;
}
#videoContent .close {
  opacity: 1;
  text-shadow: none;
  color: #F79420;
}
.showVideobtn {
  background: none;
  border: none;
  color: #F79420;
  display: flex;
  align-items: center;
  margin: 5px auto;
}
.showVideobtn i {
  margin-right: 5px;
}
#answer-block ul {
  list-style-type: inherit;
  margin-left: 2rem;
}
.ws-header .navbar-nav .nav-item .dropdown-menu a.dropdown-item {
  padding: 0.5rem 1.5rem;
}
.ws-header .navbar-nav .nav-item .dropdown-menu .media a.edit-btn i {
  margin-top: 4px;
}
.ws-header .navbar-nav .nav-item .dropdown-menu .media-body p {
  margin-bottom: 20px;
}

.ans-tab .nav-tabs .nav-item{
  padding: 0 2rem;
}

@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .ans-tab .nav-tabs .nav-item {
    padding: 0;
  }
}
#changeLanguage1{
  display: none !important;
}
.timeLeft{
  color: rgba(68, 68, 68, 0.48);
  font-size: 12px;
}
.bg-yellow{
  background: #FFCF02 !important;
}
.totalTimeLeft{
  color: rgba(68, 68, 68, 0.48);
  margin-top: 0px;
  font-size: 12px;
  font-weight: bold;
}
#question-block .question-wrapper{
  margin-top: 3rem;
}
.bg-wsTheme{
  background: #AC3592 !important;
  color:#fff !important;
}
.web-mcq .sub-header.app-header{
  top: 0;
  padding: 10px 0px;
}
.web-mcq .result-menu.app-result-menu{
  top:0;
}
.web-mcq .sub-header.section-header{
  height: 105px;
}
.web-mcq .mt-fixed.mt-top{
  margin-top: 6rem;
}
.tab-wrappers{
  position: fixed;
  width: 250px;
  top:220px;
  height: calc(100vh - 250px);
  overflow: auto;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 767px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 767px) and (orientation : landscape) {
  .web-mcq .sub-header{
    top: 60px;
    z-index: 998;
  }
  .web-mcq .result-menu{
    top:60px;
  }
  .tab-wrappers{
    position: static;
    width: auto;
    top:auto;
    height: 100%;
  }
}

@media only screen and (max-width: 767px){
  .katex .vlist>span>span{
    display: inline-flex !important;
  }
  .katex{
    font-size: 0.8em;

  }
  .katex .col-align-c .mord{
    position: relative;
    margin-top: 0em !important;
  }
  .katex .mtable .vertical-separator, .katex .mtable .arraycolsep{
    position: relative;
    top: 2em;
  }
  .katex .col-align-l{
    position: relative;
    top: 0em;
  }
  .katex .mtable .col-align-l>.vlist-t{
     line-height: 1em !important;
  }
.katex .col-align-l .mord:first-child{
  top: 0.1em !important;
  position: relative;
}
}