#css,#htmlcode,#js{
    -moz-appearance: textfield-multiline;
    -webkit-appearance: textarea;
    font: medium -moz-fixed;
    font: -webkit-small-control;
    overflow: auto;
    padding: 2px;
    resize: vertical;
    height: 100%;
    color:#fff;
    outline: 0;
    border-right:0.5px solid #ddd;
    position: relative;

}
.output-content,.editor-content{
   min-height: 50vh;
    overflow-y: auto;
}
.editor-content{
    background: black;
}
.output-content{
    background: #DDDDDD;
}

.editor-content .col-4{

}
.editor-content p.text-light{

    height: 10px;

}

.output{

}
