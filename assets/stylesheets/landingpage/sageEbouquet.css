@font-face {
  font-family: 'Roboto-Regular';
  src: url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.eot?#iefix') format('embedded-opentype'), url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.woff') format('woff'), url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.ttf') format('truetype'), url('fonts/ebouquet/Roboto-Regular/Roboto-Regular.svg#Roboto-Regular') format('svg');
}
@font-face {
  font-family: 'Roboto-Light';
  src: url('fonts/ebouquet/Roboto-Light/Roboto-Light.eot?#iefix') format('embedded-opentype'), url('fonts/ebouquet/Roboto-Light/Roboto-Light.woff') format('woff'), url('fonts/ebouquet/Roboto-Light/Roboto-Light.ttf') format('truetype'), url('fonts/ebouquet/Roboto-Light/Roboto-Light.svg#Roboto-Light') format('svg');
}
@font-face {
  font-family: 'Roboto-Bold';
  src: url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.eot?#iefix') format('embedded-opentype'), url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.woff') format('woff'), url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.ttf') format('truetype'), url('fonts/ebouquet/Roboto-Bold/Roboto-Bold.svg#Roboto-Bold') format('svg');
}
@font-face {
  font-family: 'Roboto-Black';
  src: url('fonts/ebouquet/Roboto-Black/Roboto-Black.eot?#iefix') format('embedded-opentype'), url('fonts/ebouquet/Roboto-Black/Roboto-Black.woff') format('woff'), url('fonts/ebouquet/Roboto-Black/Roboto-Black.ttf') format('truetype'), url('fonts/ebouquet/Roboto-Black/Roboto-Black.svg#Roboto-Black') format('svg');
}
body {
  font-family: 'Roboto-Regular';
  margin: 0;
  padding: 0;
  font-size: 15px;
}
.btn {
  font-family: 'Roboto-Regular';
}
.btn-warning {
  background-color: #e18b1e !important;
  border-color: #e18b1e !important;
  color: white !important;
}
.btn-primary {
  background-color: #26358c !important;
  border-color: #26358c !important;
  color: white !important;
}
.btn-outline-primary {
  border-color: #26358c !important;
  color: #26358c !important;
}
.btn-outline-primary:hover {
  background-color: transparent !important;
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px white inset;
}
::-webkit-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Roboto-Regular';
  font-size: 15px !important;
}
::-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Roboto-Regular';
  font-size: 15px !important;
}
:-ms-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Roboto-Regular';
  font-size: 15px !important;
}
:-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Roboto-Regular';
  font-size: 15px !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Roboto-Regular';
  font-size: 15px !important;
}
input[type='text'] {
  font-family: 'Roboto-Regular';
}
@media only screen and (min-width: 1920px) and (max-height: 1200px) {
  .content-Preview .container-fluid {
    padding-top: 3rem !important;
  }
  .content-Preview .index-left {
    margin-top: 4rem;
  }
  .content-Preview .desktop_image img {
    width: 600px;
  }
  .content-Preview .index_buttons {
    margin-top: 3rem;
  }
  .content-Preview .index_buttons ul li a.humanities,
  .content-Preview .index_buttons ul li a.management {
    width: 300px;
    font-size: 20px;
    height: 75px;
  }
  .content-Preview .index_buttons ul li a.management {
    padding: 20px 50px;
  }
  .content-Preview .index_quick_links {
    margin-top: 4rem;
  }
  .content-Preview .index_quick_links ul li a {
    font-size: 20px;
  }
  .content-Preview .bg_circle {
    width: 1300px;
    height: 1300px;
    top: -150px;
    right: -350px;
  }
  .content-Preview .bg_outline_circle {
    width: 1300px;
    height: 1300px;
    top: -150px;
    right: -330px;
  }
  .content-Preview .index-right .heading h1 {
    font-size: 40px;
  }
  .content-Preview .index-right .heading h4 {
    font-size: 20px;
  }
  .content-Preview .index-right .icon_texts .icon img {
    width: 100px;
  }
  .content-Preview .index-right .icon_texts .texts h5 {
    font-size: 20px;
  }
  .content-Preview .index-right .icon_texts .texts p {
    font-size: 18px;
  }
  .content-Preview .index-right .icon_texts {
    padding-bottom: 2rem;
  }
}
@media only screen and (min-width: 1920px) and (max-height: 1080px) {
  .content-Preview .container-fluid {
    padding-top: 3rem !important;
  }
  .content-Preview .index-left {
    margin-top: 3rem;
  }
  .content-Preview .index-right .icon_texts {
    padding-bottom: 1rem;
  }
}
@media only screen and (min-width: 1680px) and (max-height: 1050px) {
  .content-Preview .container-fluid {
    padding-top: 3rem !important;
  }
  .content-Preview .index-left {
    margin-top: 3rem;
  }
  .content-Preview .desktop_image img {
    width: 550px;
  }
  .content-Preview .index_buttons {
    margin-top: 3rem;
  }
  .content-Preview .index_buttons ul li a.humanities,
  .content-Preview .index_buttons ul li a.management {
    width: 300px;
    font-size: 20px;
    height: 75px;
  }
  .content-Preview .index_buttons ul li a.management {
    padding: 20px 50px;
  }
  .content-Preview .index_quick_links {
    margin-top: 4rem;
  }
  .content-Preview .index_quick_links ul li a {
    font-size: 20px;
  }
  .content-Preview .bg_circle {
    width: 1200px;
    height: 1200px;
    top: -130px;
    right: -370px;
  }
  .content-Preview .bg_outline_circle {
    width: 1200px;
    height: 1200px;
    top: -130px;
    right: -350px;
  }
  .content-Preview .index-right .heading h1 {
    font-size: 36px;
  }
  .content-Preview .index-right .heading h4 {
    font-size: 20px;
  }
  .content-Preview .index-right .icon_texts .icon img {
    width: 90px;
  }
  .content-Preview .index-right .icon_texts .texts h5 {
    font-size: 20px;
  }
  .content-Preview .index-right .icon_texts .texts p {
    font-size: 18px;
  }
  .content-Preview .index-right .icon_texts {
    padding-bottom: 1.5rem;
  }
}
@media only screen and (min-width: 1600px) and (height: 900px) {
  .content-Preview .container-fluid {
    padding-top: 2rem !important;
  }
  .content-Preview .index-left {
    margin-top: 2rem;
  }
  .content-Preview .desktop_image img {
    width: 480px;
  }
  .content-Preview .index_buttons ul li a.humanities,
  .content-Preview .index_buttons ul li a.management {
    width: 270px;
    font-size: 18px;
    height: 70px;
  }
  .content-Preview .index_buttons ul li a.management {
    padding: 20px 50px;
  }
  .content-Preview .index_quick_links {
    margin-top: 2rem;
  }
  .content-Preview .index_quick_links ul li a {
    font-size: 18px;
  }
  .content-Preview .bg_circle {
    width: 1100px;
    height: 1100px;
    top: -170px;
    right: -370px;
  }
  .content-Preview .bg_outline_circle {
    width: 1100px;
    height: 1100px;
    top: -170px;
    right: -350px;
  }
  .content-Preview .index-right .heading h1 {
    font-size: 30px;
  }
  .content-Preview .index-right .heading h4 {
    font-size: 17px;
  }
  .content-Preview .index-right .icon_texts .icon img {
    width: 80px;
  }
  .content-Preview .index-right .icon_texts .texts h5 {
    font-size: 17px;
  }
  .content-Preview .index-right .icon_texts .texts p {
    font-size: 15px;
  }
  .content-Preview .index-right .icon_texts {
    padding-bottom: 1rem;
  }
}
@media only screen and (min-width: 1440px) and (height: 900px) {
  .content-Preview .container-fluid {
    padding-top: 2rem !important;
  }
  .content-Preview .index-left {
    margin-top: 2rem;
  }
  .content-Preview .desktop_image img {
    width: 480px;
  }
  .content-Preview .index_buttons ul li a.humanities,
  .content-Preview .index_buttons ul li a.management {
    width: 250px;
    font-size: 17px;
  }
  .content-Preview .index_buttons ul li a.management {
    padding: 18px 50px;
  }
  .content-Preview .index_quick_links {
    margin-top: 2rem;
  }
  .content-Preview .index_quick_links ul li a {
    font-size: 17px;
  }
  .content-Preview .bg_circle {
    width: 1050px;
    height: 1050px;
    top: -150px;
    right: -370px;
  }
  .content-Preview .bg_outline_circle {
    width: 1050px;
    height: 1050px;
    top: -150px;
    right: -350px;
  }
  .content-Preview .index-right .heading h1 {
    font-size: 32px;
  }
  .content-Preview .index-right .heading h4 {
    font-size: 17px;
  }
  .content-Preview .index-right .icon_texts .icon img {
    width: 80px;
  }
  .content-Preview .index-right .icon_texts .texts h5 {
    font-size: 16px;
  }
  .content-Preview .index-right .icon_texts .texts p {
    font-size: 15px;
  }
  .content-Preview .index-right .icon_texts {
    padding-bottom: 0.8rem;
  }
}
@media only screen and (min-width: 1366px) and (height: 1024px) {
  .content-Preview .container-fluid {
    padding-top: 2rem !important;
  }
  .content-Preview .index-left {
    margin-top: 3rem;
  }
  .content-Preview .index-right .icon_texts {
    padding-bottom: 1.5rem;
  }
  .content-Preview .index_buttons,
  .content-Preview .index_quick_links {
    margin-top: 3rem;
  }
  .content-Preview .index-right .heading h1 {
    font-size: 32px;
  }
  .content-Preview .index-right .heading h4 {
    font-size: 17px;
  }
  .content-Preview .index-right .icon_texts .icon img {
    width: 80px;
  }
  .content-Preview .index-right .icon_texts .texts h5 {
    font-size: 16px;
  }
  .content-Preview .index-right .icon_texts .texts p {
    font-size: 15px;
  }
  .content-Preview .bg_circle {
    width: 1080px;
    height: 1080px;
    top: -130px;
    right: -440px;
  }
  .content-Preview .bg_outline_circle {
    width: 1080px;
    height: 1080px;
    top: -130px;
    right: -420px;
  }
  .content-Preview .index_quick_links ul li a {
    font-size: 17px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 1199px) {
  /*.content-Preview {
    height: auto !important;
  }*/
  .bg_circle,
  .bg_outline_circle {
    display: none;
  }
  .index-left {
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .index-right {
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .index-right .heading h1 {
    font-size: 26px !important;
  }
  .index-right .heading h4 {
    font-size: 15px !important;
  }
  .desktop_image img {
    width: 400px !important;
  }
  .index_buttons ul li {
    padding: 0 10px !important;
  }
  .index_buttons ul li a.management {
    padding: 16px 30px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_buttons ul li a.humanities {
    padding: 8px 20px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_quick_links ul li a {
    font-size: 15px !important;
  }
  .searchbar {
    flex: 0 0 60%;
    max-width: 60%;
  }
  .footer-menu {
    height: auto !important;
  }
  .footer-menu .col-8 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .footer-menu .col-8 ul {
    justify-content: center;
  }
  .footer-menu .col-4 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 1.5rem;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .index-left {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .index-right {
    flex: 0 0 80%;
    max-width: 80%;
    top: 0 !important;
  }
  .searchbar {
    margin: 0 auto;
    flex: 0 0 100%;
    max-width: 100%;
  }
  footer.footer-menu ul li a {
    font-size: 11px !important;
  }
  header .logo img {
    width: 250px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 220px !important;
  }
  .library #content-data-books .card {
    width: 190px !important;
    margin-bottom: 20px !important;
  }
  .library #content-data-books .card img {
    height: 220px !important;
  }
  .library #content-data-books .uncover {
    height: 220px !important;
  }
}
@media only screen and (max-width: 768px) {
  .ebouquet .banner-info h1.maintext,
  .ebouquet .banner-info h1.subtext,
  .ebouquet .ask-options h1 {
    font-size: 22px !important;
  }
  .ebouquet .banner-info img {
    width: 100% !important;
  }
  .ebouquet .big-links a h3 {
    font-size: 16px !important;
    padding: 1.5rem 1rem !important;
  }
  .ebouquet .big-links {
    margin-top: 20px !important;
  }
  .ebouquet .big-links .mb-4 {
    margin-bottom: 15px !important;
  }
  .ebouquet .big-links a {
    border-radius: 50px !important;
  }
  .ebouquet .ask-options .options-info {
    padding-bottom: 0 !important;
  }
  footer .promise-icon img {
    width: 100px;
  }
  .ebouquet header .ws-header .navbar-nav.right-menu {
    position: initial !important;
  }
  .content-Preview {
    height: auto !important;
  }
  .bg_circle,
  .bg_outline_circle {
    display: none;
  }
  .index-left {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .index-right {
    flex: 0 0 100%;
    max-width: 100%;
    top: 0 !important;
  }
  .searchbar {
    margin: 0 auto;
    flex: 0 0 100%;
    max-width: 100%;
  }
  footer.footer-menu ul li {
    padding: 0 5px !important;
  }
  header .logo img {
    width: 250px !important;
  }
  .footer-menu {
    height: auto !important;
  }
  .footer-menu .col-8 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0;
  }
  .footer-menu .col-8 .d-flex.social-icons {
    display: flex !important;
  }
  .footer-menu .col-8 .d-flex.social-icons img {
    width: 35px;
    height: 35px;
  }
  .footer-menu .col-8 .d-flex {
    display: inline-block !important;
    width: 100%;
  }
  .footer-menu .col-8 .d-flex p {
    display: inline-block;
  }
  .footer-menu .col-8 .d-flex p:first-child {
    display: block;
  }
  .footer-menu .col-8 ul {
    justify-content: center;
    padding-left: 0;
  }
  .footer-menu .col-8 ul a {
    padding-right: 0 !important;
  }
  .footer-menu .col-4 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 1.5rem;
  }
  .index_buttons ul li a.management {
    padding: 16px 30px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_buttons ul li a.humanities {
    padding: 8px 20px !important;
    font-size: 15px !important;
    width: 180px !important;
    height: 60px !important;
  }
  .index_quick_links ul li a {
    font-size: 15px !important;
  }
  .index-right .heading h1 {
    font-size: 26px !important;
  }
  .index-right .heading h4 {
    font-size: 15px !important;
  }
  .desktop_image img {
    width: 400px !important;
  }
  .index_buttons ul li {
    padding: 0 10px !important;
  }
  footer.footer-menu > .row {
    margin: 0;
  }
  .ws-header div.mobile-profile {
    display: flex !important;
    right: 0;
    text-align: center;
    width: 100%;
  }
  .etexts .ws-header div.mobile-profile .nav-item {
    color: black !important;
    padding-top: 0;
  }
  .etexts .ws-header div.mobile-profile .nav-item .nav-link {
    font-family: 'Roboto-Regular';
    font-size: 12px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .etexts .ws-header div.mobile-profile .nav-item a img {
    width: 30px;
    height: 30px;
  }
  .etexts .ws-header div.mobile-profile p.user-name {
    margin-bottom: 0;
  }
  .etexts .ws-header div.mobile-profile .edit-btn {
    width: 18px;
    height: 18px;
    display: inline-block;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.94);
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    position: absolute;
    left: 40px;
    top: 35px;
    text-align: center;
  }
  .etexts .ws-header div.mobile-profile .edit-btn i {
    font-size: 12px;
  }
  .etexts .ws-header div.mobile-profile #logout {
    border-top: 1px solid #ddd;
  }
  .etexts .ws-header div.mobile-profile .nav-item .nav-link.login {
    padding: 0.5rem 1rem;
    color: #ffffff;
  }
  .ws-header .navbar-nav.right-menu li.nav-item {
    display: none !important;
  }
  .etexts .ws-header {
    border-bottom: none !important;
    height: 60px;
  }
  .etexts .user_profile .tab-content .jumbotron form .media .continue {
    width: 100% !important;
  }
  .ebouquet header .ebouquet-logo {
    padding: 30px 10px 10px !important;
  }
  .evidyaStore {
    margin-top: 2rem !important;
  }
  nav #menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    background: #172051;
    -webkit-transition: all 0.35s ease;
    transition: all 0.35s ease;
  }
  nav #menu ul {
    margin: 0;
    -webkit-transform: translateY(15vh);
    transform: translateY(15vh);
    -webkit-transition: all 0.35s ease;
    transition: all 0.35s ease;
    padding-left: 0;
  }
  nav ul li {
    list-style: none;
    display: block;
    text-align: center;
    margin: 0;
  }
  nav #menu ul li a:hover {
    color: #FFF;
  }
  nav #menu ul li a {
    color: #FFF;
    display: inline-block;
    padding: 15px;
    font-size: 24px;
    line-height: 20px;
    -webkit-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
  }
  #toggle.open {
    position: fixed;
  }
  #toggle {
    width: 31px;
    height: 30px;
    position: relative;
    top: 0px;
    right: 0;
    background: #26358c;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: 0.25s ease-in-out;
    transition: 0.25s ease-in-out;
    cursor: pointer;
    z-index: 555;
    border-radius: 3px;
  }
  #toggle span {
    display: block;
    position: absolute;
    height: 2px;
    width: 20px;
    background: #FFF;
    border-radius: 25px;
    opacity: 1;
    left: 50%;
    margin-left: -10px;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transition: 0.25s ease-in-out;
    transition: 0.25s ease-in-out;
  }
  #toggle span:nth-child(1) {
    top: 8px;
  }
  #toggle span:nth-child(2),
  #toggle span:nth-child(3) {
    top: 14px;
  }
  #toggle span:nth-child(4) {
    top: 20px;
  }
  #toggle.open span:nth-child(1) {
    top: 29px;
    width: 0%;
    left: 50%;
    margin: 0;
  }
  #toggle.open span:nth-child(2) {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
  }
  #toggle.open span:nth-child(3) {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
  #toggle.open span:nth-child(4) {
    top: 29px;
    width: 0%;
    left: 50%;
    margin: 0;
  }
  body.active nav #menu {
    opacity: 1;
    visibility: visible;
  }
  body.active nav #menu ul {
    -webkit-transform: translateY(20vh);
    transform: translateY(20vh);
  }
  .suggest_this_book {
    position: relative !important;
    bottom: -20px !important;
    right: 0 !important;
  }
}
@media only screen and (max-width: 575px) {
  footer.footer-menu ul li {
    margin-bottom: 10px;
  }
  .desktop_image img {
    width: 310px !important;
  }
  .index_quick_links ul li a {
    font-size: 14px !important;
  }
  header .logo img {
    width: 200px !important;
  }
  .index_buttons ul li {
    width: 100%;
    margin-bottom: 30px;
  }
  .index_buttons ul li:last-child {
    margin-bottom: 0;
  }
  .index_buttons ul li a.humanities,
  .index_buttons ul li a.management {
    width: 100% !important;
  }
  .searchbar {
    padding-left: 15px !important;
  }
  .searchbar .input-group-append {
    position: absolute;
    right: 0;
  }
  .searchbar .input-group-append button {
    right: 25px;
  }
  .footer-menu div.col-4 p br:first-child {
    display: none !important;
  }
  .index_quick_links ul {
    display: block;
    text-align: center;
  }
  .index_quick_links ul li {
    padding: 10px 10px !important;
    border-bottom: 1px solid #EEE;
    border-right: none !important;
  }
  body.active nav #menu ul {
    padding-top: 30px;
  }
}
@media only screen and (max-width: 480px) {
  .content-Preview .container-fluid {
    padding-top: 2rem !important;
  }
  .desktop_image img {
    width: 230px !important;
  }
  .ws-header div.mobile-profile {
    text-align: center;
    width: 100%;
    right: 0;
  }
  .etexts .ws-header {
    height: 50px !important;
    padding: 0;
  }
  .etexts .ws-header a {
    width: 100%;
    text-align: center;
  }
  .etexts .ws-header a .logo img {
    margin: 0 auto;
    margin-bottom: 10px;
  }
  .index-right {
    padding-left: 15px !important;
  }
  .index-right .heading h1 {
    font-size: 22px !important;
  }
  .index-right .icon_texts .icon img {
    width: 50px !important;
  }
  .index-right .icon_texts .texts h5 {
    font-size: 13px !important;
  }
  .index-right .icon_texts .texts p {
    font-size: 12px !important;
  }
  .footer-menu .col-8 ul {
    display: block !important;
  }
  .footer-menu .col-8 ul li {
    line-height: normal;
    border-right: none !important;
    margin-bottom: 0 !important;
  }
  footer.footer-menu ul li a {
    font-size: 11px !important;
  }
  .etexts .evidyaLogin {
    top: 3.8rem;
    right: 0px;
    left: 0;
    margin: 0 auto;
    width: 270px;
  }
  .etexts .evidyaLogin::before {
    left: 70px;
  }
}
@media only screen and (max-width: 1400px) {
  .evidyaStore .bookContainer .textWrapper {
    margin-left: 1.5rem;
  }
  .ebouquet .ask-options .options-info .circle-style {
    margin: 0 15px 30px !important;
  }
  .ebouquet .big-links a h3 {
    font-size: 28px ;
  }
  header .ws-header .navbar-nav.right-menu li .nav-link {
    padding: 0 2rem !important;
    text-align: center;
  }
}
@media only screen and (max-width: 1024px) {
  .ebouquet .banner-info h1.maintext,
  .ebouquet .banner-info h1.subtext,
  .ebouquet .ask-options h1 {
    font-size: 30px;
  }
  .ebouquet .banner-info img {
    width: 80% !important;
  }
  .ebouquet .ask-options ul.title-style li {
    margin: 0 15px !important;
  }
  .ebouquet .big-links a h3 {
    font-size: 22px;
  }
  header .ws-header .navbar-nav.right-menu li .nav-link {
    padding: 0 1.5rem !important;
    text-align: center;
  }
  .evidyaStore .bookContainer .bookImage {
    height: 180px;
    width: 120px;
  }
  .evidyaStore .bookContainer > div:first-child {
    width: 20%;
  }
  .evidyaStore .bookContainer > div:nth-child(3) {
    width: 20%;
  }
  .evidyaStore .bookContainer .language {
    min-width: 100px;
  }
  .etexts .libraryDimension h3 {
    padding: 0 1rem;
  }
  .etexts .libraryDimension p {
    padding: 0.5rem 1rem;
  }
  .etexts .libraryDimension .bookContainer > div:first-child {
    width: 20%;
  }
  .etexts .libraryDimension .bookContainer > div:nth-child(3) {
    width: 20%;
  }
  .etexts .libraryDimension .bookContainer .bookImage {
    height: 180px;
    width: 120px;
  }
  .etexts .libraryDimension .bookContainer .textWrapper {
    margin-left: 1.5rem;
  }
  .etexts .libraryDimension .bookContainer .language {
    min-width: 100px;
  }
  .etexts .libraryDimension .bookContainer .addLibrary {
    top: 80px;
    right: 0;
  }
}
@media only screen and (max-width: 768px) {
  .ebouquet .ask-options ul.title-style li {
    margin: 0 10px !important;
  }
  .evidyaStore .bookContainer > div:first-child {
    width: 120px;
    margin-right: 15px;
  }
  .evidyaStore .bookContainer > div:nth-child(2) {
    width: 100%;
    margin-left: 0;
  }
  .evidyaStore .bookContainer > div:nth-child(3) {
    width: 120px;
    position: absolute;
    top: 35px;
    right: 0;
  }
  .evidyaStore .bookContainer .language {
    margin-left: 0;
  }
  .evidyaStore .showResult > div {
    width: auto !important;
  }
  .etexts .libraryDimension .bookContainer > div:first-child {
    width: 120px;
  }
  .etexts .libraryDimension .bookContainer > div:nth-child(2) {
    width: 100%;
    margin-left: 0;
    margin-top: 1rem;
  }
  .etexts .libraryDimension .bookContainer > div:nth-child(3) {
    width: 100px;
    position: absolute;
    top: 30px;
    right: 0;
  }
  .etexts .libraryDimension .bookContainer .language {
    margin-left: 0;
    min-width: 80px;
  }
  .etexts .libraryDimension h3 {
    padding: 0;
  }
  .etexts .libraryDimension p {
    padding: 0;
  }
  .etexts .libraryDimension #library {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .etexts .libraryDimension ul.nav {
    display: inline-flex;
  }
  .etexts .libraryDimension .nav-tabs li a {
    font-size: 14px;
    padding: 1rem !important;
  }
  .etexts .libraryDimension .container-wrapper input.search {
    width: 100%;
    font-size: 15px;
    margin-bottom: 1rem;
    margin-top: 1rem;
  }
  .etexts .libraryDimension h4 {
    padding: 0;
  }
  .etexts .libraryDimension .bookContainer .addLibrary {
    top: 0;
    right: 0;
    position: relative;
  }
}
@media only screen and (min-width: 992px) {
  .show_filters_btn {
    display: none;
  }
  #showFilters {
    display: block;
  }
}
@media only screen and (max-width: 991px) {
  .show_filters_btn {
    display: block;
  }
}
@media only screen and (max-width: 1400px) {
  .content-Preview {
    height: auto !important;
  }
}
@media only screen and (max-width: 1250px) {
  header .ws-header .navbar-nav.right-menu li .nav-link {
    font-size: 14px !important;
  }
}
@media only screen and (max-width: 767px) {
  .library #content-data-books .card {
    margin-bottom: 20px;
  }
  .library .tab-content {
    margin-top: 0;
  }
  #ebpagination .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  #ebpagination .pagination .page-link {
    padding: 0.5rem;
  }
  .evidyaStore .bookContainer > div:nth-child(2) {
    margin-top: 1rem;
  }
}
@media only screen and (max-width: 575px) {
  .library #content-data-books .card {
    width: 180px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 200px !important;
  }
  .library #content-data-books .card img {
    height: 200px !important;
  }
  .library #content-data-books .uncover {
    height: 200px !important;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) and (orientation: portrait) {
  .library #content-data-books .card {
    width: 100% !important;
    margin-bottom: 10px !important;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) and (orientation: landscape) {
  .library #content-data-books .card {
    width: 180px !important;
    margin-bottom: 10px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 220px !important;
  }
  .library #content-data-books .card img {
    height: 220px !important;
  }
  .library #content-data-books .uncover {
    height: 220px !important;
  }
}
@media only screen and (max-width: 330px) {
  .library #content-data-books .card {
    width: 100% !important;
    margin-bottom: 10px !important;
  }
  .library #content-data-books .lib-showcase {
    min-height: 160px !important;
  }
  .library #content-data-books .card img {
    height: 160px !important;
  }
  .library #content-data-books .uncover {
    height: 160px !important;
  }
}
/*!
 * Bootstrap Reboot v4.1.2 (https://getbootstrap.com/)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)
 */
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
@-ms-viewport {
  width: device-width;
}
article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fafafa;
}
[tabindex="-1"]:focus {
  outline: 0 !important;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
p {
  margin-top: 0;
  margin-bottom: 1rem;
}
abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
}
address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 700;
}
dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1rem;
}
dfn {
  font-style: italic;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}
a:hover {
  color: #0056b3;
  text-decoration: underline;
}
a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):focus {
  outline: 0;
}
pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}
pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}
figure {
  margin: 0 0 1rem;
}
img {
  vertical-align: middle;
  border-style: none;
}
svg:not(:root) {
  overflow: hidden;
  vertical-align: middle;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}
th {
  text-align: inherit;
}
label {
  display: inline-block;
  margin-bottom: 0.5rem;
}
button {
  border-radius: 0;
}
button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
  cursor: pointer;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
html p,
html a,
html h1,
html h2,
html h3,
html h4,
html span,
html td,
html th {
  font-family: 'Roboto-Regular';
}
header {
  height: auto;
  display: block;
  align-items: center;
}
header > div {
  height: auto;
  display: flex;
  align-items: center;
}
header .logo {
  /*background-color: #ffffff;*/
  padding: 0;
  /*border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;*/
  display: inline-block;
  align-items: center;
  /*height: 60px;*/
}
header .logo img {
  width: 400px;
}
header .right-menu {
  /*background: #ffffff;*/
  padding: 0;
  /*border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;*/
  display: flex;
  align-items: center;
  height: 60px;
}
header .ws-header .navbar-nav .nav-link {
  color: #ffffff;
}
header .ws-header .navbar-nav.right-menu li .nav-link {
  color: #ffffff;
  padding: 0 3rem;
  font-size: 17px;
  text-align: center;
}
.login-btn {
  background: #1E4598;
  color: #fff !important;
  border-radius: 4px;
  font-family: 'Roboto-Regular' !important;
  text-transform: capitalize !important;
  padding: 6px 40px !important;
}
.evidyaLogin {
  display: none;
  background: #fff;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  width: 350px;
  min-height: 300px;
  position: absolute;
  top: 4rem;
  right: 20px;
  z-index: 9;
  padding: 1rem;
  padding-bottom: 2rem;
}
.evidyaLogin::before {
  content: "";
  width: 0px;
  height: 0px;
  border: 0.8em solid transparent;
  position: absolute;
  right: 30px;
  top: -20px;
  border-bottom: 10px solid #ffffff;
}
.evidyaLogin button {
  width: 100%;
  margin-top: 1rem;
  outline: 0;
  height: 40px;
}
.evidyaLogin .btn-login {
  background: #1E4598;
  color: #fff;
  width: 100%;
  margin-top: 1rem;
}
.evidyaLogin .btn-create {
  border: 1px solid #1E4598;
  background: none;
  color: #1E4598;
}
.evidyaLogin .googleLogin,
.evidyaLogin .facebookLogin {
  border-radius: 50px;
  color: #fff;
  background: none;
  border: none;
  outline: 0;
}
.evidyaLogin .googleLogin {
  background-color: #d95940;
}
.evidyaLogin .facebookLogin {
  background-color: #4c7ccf;
}
.evidyaLogin .frgtPassword {
  display: block;
  margin-top: 10px;
  color: #1E4598;
}
.evidyaSignup h3 {
  font-size: 20px;
}
.evidyaSignup .loginContinue {
  font-size: 16px;
  display: block;
  color: #1E4598 !important;
  cursor: pointer;
}
.evidyaSignup .loginContinue:hover {
  /*color:rgb(217,89,64) !important;*/
}
.evidyaSignup #signup {
  width: 100%;
  background: #1E4598;
  color: #ffffff;
}
.sageEvidya {
  position: relative;
}
.sageEvidya .order-pr {
  display: none;
}
.evidya .user_profile {
  min-height: calc(100vh - 175px);
}
.evidyaloginWrapper label {
  font-weight: normal;
}
.forgotPassword label {
  font-weight: normal;
}
.evidyaSignup label {
  font-weight: normal;
}
.evidya .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn {
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: absolute;
  left: 57px;
  bottom: 9px;
  text-align: center;
}
.evidya .input-login label > span {
  position: absolute;
  font-weight: normal;
}
.evidya .bookTemplate .tab-header > .navbar {
  position: sticky;
  box-shadow: none;
}
.evidya .bookTemplate .content-wrapper {
  margin-top: 0;
}
.evidya.custom-fix .bookTemplate .shadowHeader {
  position: fixed;
  top: 0;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media .media-body p {
  font-family: 'Roboto-Regular';
  font-size: 15px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item {
  padding: 0.4rem 1rem;
  font-size: 13px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu {
  min-width: 180px;
  margin-top: 10px;
  padding-top: 0;
  box-shadow: 0 5px 10px 0 #ccc;
}
.ebouquet header .ebouquet-logo {
  display: block;
}
.ebouquet header .ws-menu-start {
  background-color: #1E4598;
}
.ebouquet header .ws-header .navbar-nav.right-menu {
  position: relative;
}
.ebouquet .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 190px !important;
}
.ebouquet.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 0px !important;
}
.ebouquet .bookTemplate .content-wrapper .chapterSection a.slide-toggle {
  top: 290px !important;
}
.ebouquet.hasScrolled .bookTemplate .content-wrapper .chapterSection a.slide-toggle {
  top: 200px !important;
}
.ebouquet.hasScrolled .read-content {
  margin-top: 70px;
}
.ebouquet .bookTemplate .export-notes {
  top: 180px;
}
@media only screen and (max-width: 768px) {
  .ebouquet .bookTemplate .content-wrapper #book-sidebar .mobile-title {
    margin-top: 130px;
  }
  .ebouquet #chapters-toggle.left i {
    transform: rotate(0deg);
  }
  .ebouquet.hasScrolled .read-content {
    margin-top: 0px;
  }
  .ebouquet .ws-header div.mobile-profile .nav-item {
    margin: 0 7px;
  }
  .ebouquet .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
    margin-top: 150px !important;
  }
}
a {
  cursor: pointer !important;
}
.triangle1 {
  position: absolute;
  right: 0;
  top: 0px;
}
.triangle1 img {
  width: 400px;
}
.triangle2 {
  position: absolute;
  bottom: 0;
  left: 0;
}
.triangle2 img {
  width: 400px;
}
.content-Preview {
  position: relative;
  height: auto;
  /*background: url('../../images/sage/Header_Image.png') center no-repeat;
  background-size: cover;
  padding: 2px 0;
  padding-bottom: 2rem;*/
  /*.imgWrapper{
    position: relative;
    img{
      height: 395px;
      margin-top: 1rem;
      margin-left: -2rem;
    }
  }*/
}
.content-Preview h4 {
  /*font-size: 18px;
    font-weight: normal;
    color:@sageTheme;
    margin-left:-2rem;*/
}
.content-Preview h2 {
  /*font-size: 18px;
  color: #1F419B;
  font-weight: bold;*/
}
.circle {
  width: 100px;
  height: 100px;
  border: solid 1px #555;
  background-color: #fff;
  box-shadow: 3px 3px rgba(0, 0, 0, 0.8);
  -moz-box-shadow: 3px 3px rgba(0, 0, 0, 0.8);
  -webkit-box-shadow: 3px 3px rgba(0, 0, 0, 0.8);
  -o-box-shadow: 3px 3px rgba(0, 0, 0, 0.8);
  border-radius: 75px;
  text-align: center;
}
.circle img {
  margin-top: 1.2rem;
  height: 32px;
}
.circle a {
  font-size: 12px;
  font-family: 'Roboto-Bold';
  color: #1E4598;
  display: block;
  cursor: pointer;
}
.circle a:hover {
  text-decoration: none;
}
.circle:nth-child(2) p {
  color: #2dbbf9;
}
.searchSage {
  width: 400px;
  height: 40px;
  border-radius: 50px !important;
  border: 1px solid #1E4598 !important;
  padding: 1rem;
  outline: 0;
  position: relative;
}
.searchbar input {
  border-radius: 50px !important;
  border-color: #233982;
  border-width: 1.5px;
  height: 50px !important;
  padding-left: 30px !important;
  padding-right: 50px !important;
  font-size: 15px !important;
}
.searchbar input.focus-visible {
  background-color: transparent !important;
}
.searchbar button {
  background: none;
  border: none;
  outline: 0;
  position: absolute;
  right: 50px;
  width: 50px;
  height: 50px !important;
  border-radius: 50% !important;
  z-index: 99 !important;
}
.searchbar button img {
  height: 30px;
}
.searchbar ul.typeahead {
  width: 85%;
}
.searchbar ul.typeahead li a:active {
  color: #333333;
}
.browseMenu {
  display: none;
  width: 500px;
  min-height: 100px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  position: absolute;
  top: 4rem;
  z-index: 99;
  right: 5rem;
  padding: 2rem;
}
.browseMenu::before {
  content: "";
  width: 0px;
  height: 0px;
  border: 0.8em solid transparent;
  position: absolute;
  right: 60px;
  top: -20px;
  border-bottom: 10px solid #ededed;
}
.browseMenu h4 {
  color: #000;
  font-size: 14px;
  font-family: 'Roboto-Regular';
  font-family: 'Roboto-Bold';
}
.browseMenu ul {
  padding: 0;
  margin-left: 26px;
}
.browseMenu ul li {
  list-style-type: disc;
  padding-bottom: 0.5rem;
}
.browseMenu ul li a {
  color: #000 !important;
}
.language {
  padding: 0;
  margin-left: 15px;
}
.language li {
  list-style-type: disc;
  padding-bottom: 0.5rem;
}
.language li a {
  color: #000 !important;
}
.circle1 {
  border-radius: 50px;
  width: 28px;
  height: 28px;
  border: 1px solid #1E4598;
}
.circle1 img {
  height: 24px;
}
.subHeader h4 {
  margin-bottom: 0;
  margin-left: 8px;
}
.circle2 {
  border-radius: 50px;
  width: 24px;
  height: 24px;
  border: 1px solid #1E4598;
  display: inline-block;
  text-align: center;
  margin-left: 8px;
}
.content-wrappers {
  position: absolute;
  top: 80px;
  width: 235px;
}
.content-wrappers h2 {
  color: #fff;
}
.content-wrappers a {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 60px;
  border-radius: 50px;
  margin-top: 1rem;
  border: 1px solid #fff;
  color: #2F80ED;
  text-decoration: none;
  font-family: 'Roboto-Bold';
}
.content-wrappers a:hover {
  text-decoration: none;
}
.content-wrappers a img {
  width: 50px;
  height: 50px !important;
  margin-top: 0px !important;
  margin-left: -25px !important;
}
.content-wrappers a span {
  position: relative;
  left: -3rem;
  color: #0056b3;
}
#loginBenifits .modal-content {
  padding: 1rem;
}
#loginBenifits .modal-header {
  border: none;
}
#loginBenifits .modal-footer {
  border: none;
}
#loginBenifits h3 {
  font-size: 20px;
  font-family: 'Roboto-Bold';
  color: #1E4598;
  margin-top: 1rem;
}
#loginBenifits h2 {
  font-size: 24px;
  font-family: 'Roboto-Bold';
  color: #1E4598;
  border-bottom: 1px solid;
  padding-bottom: 3px;
}
#loginBenifits p {
  color: #000000;
  font-size: 18px;
  font-family: 'Roboto-Regular';
  margin-top: 0.5rem;
}
#loginBenifits ul li {
  font-size: 16px;
  font-family: 'Roboto-Regular';
  color: #000000;
}
#loginBenifits .btn-login {
  background: #1E4598;
  color: #fff;
}
#loginBenifits .btn-close {
  color: #fff;
}
#popupLogin {
  padding: 1rem;
}
.loginnow {
  color: #1E4598 !important;
  text-decoration: underline !important;
  font-family: 'Roboto-Bold';
  cursor: pointer;
}
.forgotPassword h3 {
  font-size: 18px;
  color: #1E4598;
  text-align: center;
  margin-top: 1rem;
}
.forgotPassword p {
  font-size: 14px;
  color: #444444;
}
.forgotPassword #fPbtn {
  width: 100%;
  color: #ffffff;
  background: #1E4598;
  margin-top: 2rem !important;
}
#back-login {
  background: #dddddd;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
}
#reset-password-completed p,
#reset-google-paswd p,
#account-exists p {
  margin-top: 2rem;
  color: #444444;
  font-size: 15px;
}
#reset-password-completed p:first-child,
#reset-google-paswd p:first-child,
#account-exists p:first-child {
  color: #1E4598;
}
.btn-back {
  background: #dddddd !important;
}
#fp-user-email,
#fp-user-email1 {
  color: #1E4598;
}
.evidya .user_profile {
  margin-top: 0;
}
.evidya .user_profile #profile-menu li a[data-target="#orders"] {
  display: none;
}
.evidya #notesMenu,
.evidya #formatMenu {
  display: block !important;
}
.desktop_image img {
  width: 450px;
}
.index_buttons ul li {
  padding: 0 20px;
}
.index_buttons ul li a.humanities {
  background-color: #e18b1e;
  color: #FFF;
  border-radius: 10px;
  padding: 8px 50px;
  font-size: 16px;
  line-height: 1.2;
  width: 250px;
  height: 65px;
  border-bottom: 5px solid #bf7e23;
  border-top: 3px solid #ffa638;
  box-shadow: 0 10px 20px rgba(93, 57, 36, 0.5);
}
.index_buttons ul li a.humanities:active {
  box-shadow: 0 5px 10px rgba(93, 57, 36, 0.5);
}
.index_buttons ul li a.management {
  background-color: #d3d3d3;
  color: #000;
  border-radius: 10px;
  padding: 16px 50px;
  font-size: 16px;
  width: 250px;
  height: 65px;
  border-bottom: 5px solid #c5c9c9;
  border-top: 3px solid #e5e5e5;
  box-shadow: 0 10px 20px rgba(93, 57, 36, 0.5);
}
.index_buttons ul li a.management:active {
  box-shadow: 0 5px 10px rgba(93, 57, 36, 0.5);
}
.index_quick_links ul li {
  padding: 0 15px;
  border-right: 2px solid #3b3b3b;
  line-height: normal;
}
.index_quick_links ul li:last-child {
  border-right: none;
}
.index_quick_links ul li a {
  color: #26358c;
  font-size: 16px;
}
.index-right {
  position: relative;
  top: -50px;
}
.index-right .heading h1 {
  font-size: 30px;
  font-family: HelveticaNeue-Medium;
}
.index-right .heading h4 {
  font-size: 16px;
}
.index-right .icon_texts .icon img {
  width: 70px;
}
.index-right .icon_texts .texts h5 {
  font-size: 14px;
}
.index-right .icon_texts .texts p {
  font-size: 14px;
  line-height: normal;
  margin-bottom: 0;
}
.profile-popup-open:before {
  content: '';
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 999;
}
.profile-popup-open .evidyaLogin {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  left: 0;
  margin: 0 auto;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.5);
  z-index: 999999;
}
.profile-popup-open .evidyaLogin:before {
  display: none;
}
.ebouquet .banner-info h1.maintext {
  font-family: 'Roboto-Black';
  color: #1E4598;
}
.ebouquet .banner-info h1.subtext {
  font-family: 'Roboto-Light';
  color: #6C635D;
}
.ebouquet .banner-info img {
  width: 90%;
  margin: 1rem 0;
}
.ebouquet .ask-options {
  background-color: #EAEAEA;
}
.ebouquet .ask-options h1 {
  font-family: 'Roboto-Black';
  color: #58595B;
}
.ebouquet .ask-options ul.title-style li {
  list-style-type: none;
  margin: 0 25px;
  width: 10px;
  height: 10px;
  border-radius: 50px;
}
.ebouquet .ask-options ul.title-style li:first-child {
  background-color: #00B59A;
}
.ebouquet .ask-options ul.title-style li:nth-child(2) {
  background-color: #FF9D00;
}
.ebouquet .ask-options ul.title-style li:nth-child(3) {
  background-color: #FF553A;
}
.ebouquet .ask-options ul.title-style li:nth-child(4) {
  background-color: #0E8ECE;
}
.ebouquet .ask-options ul.title-style li:nth-child(5) {
  background-color: #B164A5;
}
.ebouquet .ask-options ul.title-style li:nth-child(6) {
  background-color: #FFCB66;
}
.ebouquet .ask-options ul.title-style li:nth-child(7) {
  background-color: #00B59A;
}
.ebouquet .ask-options ul.title-style li:last-child {
  background-color: #FF9D00;
}
.ebouquet .ask-options .options-info .circle-style {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  font-size: 17px;
  line-height: 150px;
  text-align: center;
  color: #58595B;
  position: relative;
  border: 6px solid #16CABD;
  margin: 0 15px;
  box-shadow: inset -7px 5px 10px #CCC;
}
.ebouquet .ask-options .options-info .circle-style:before {
  content: '';
  position: absolute;
  top: -12px;
  left: -12px;
  width: 172px;
  height: 172px;
  border-radius: 50%;
  border: 1.5px solid #000;
}
.ebouquet .ask-options .options-info .circle-style span {
  display: inline-block;
  vertical-align: middle;
  line-height: normal;
  padding: 0 20px;
  position: relative;
  z-index: 99;
  font-family: 'Roboto-Bold';
}
.ebouquet .ask-options .options-info .circle-style img {
  width: 60px;
  position: absolute;
  top: -30px;
  left: -10px;
}
.ebouquet .ask-options .options-info .green-border {
  border-color: #16CABD;
}
.ebouquet .ask-options .options-info .orange-border {
  border-color: #FF9D00;
}
.ebouquet .ask-options .options-info .red-border {
  border-color: #FF553A;
}
.ebouquet .ask-options .options-info .blue-border {
  border-color: #0E8ECE;
}
.ebouquet .ask-options .options-info .purple-border {
  border-color: #B164A5;
}
.ebouquet .ask-options .options-info .yellow-border {
  border-color: #FFCB66;
}
.ebouquet .big-links a {
  display: block;
  background-color: #58595B;
  color: #ffffff;
  border-bottom-left-radius: 30px;
  border-bottom-right-radius: 30px;
}
.ebouquet .big-links a:hover {
  text-decoration: none;
  background-color: #FFCB66;
  color: #58595B;
}
.ebouquet .big-links a h3 {
  padding: 1.5rem 2.5rem;
  font-family: 'Roboto-Bold';
}
.evidyaStore {
  margin-top: 1rem;
  min-height: 600px;
  padding-bottom: 2rem;
}
@media (min-width: 1200px) {
  .evidyaStore .container {
    max-width: 1300px;
  }
}
.evidyaStore p {
  margin-bottom: 0;
  color: #444444;
}
.evidyaStore .Sidewrapper {
  background: #eff1f8;
  padding: 1rem;
}
.evidyaStore .Sidewrapper .search {
  border-radius: 50px;
  padding: 5px 25px;
  width: 100%;
  border: 1px solid #1E4598;
  outline: 0;
}
.evidyaStore .Sidewrapper h4 {
  color: #444444;
  font-family: 'Roboto-Regular';
  font-size: 14px;
  margin-top: 1rem;
  font-weight: bold;
  text-transform: uppercase;
}
.evidyaStore .Sidewrapper #langaugeList {
  list-style: none;
  padding-left: 0;
}
.evidyaStore .Sidewrapper #langaugeList li {
  padding: 5px 15px;
}
.evidyaStore .Sidewrapper .categories {
  color: #000;
  font-size: 14px;
  padding: 5px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.evidyaStore .Sidewrapper .categories i {
  width: 150px;
  text-align: right;
}
.evidyaStore .Sidewrapper .keywords li {
  list-style-type: none;
}
.evidyaStore .Sidewrapper .keywords li a {
  color: #1E4598;
  font-weight: normal;
  cursor: pointer;
}
.evidyaStore .Sidewrapper .keywords li a:hover {
  text-decoration: underline;
}
.evidyaStore .Sidewrapper .viewtitle {
  color: #2F80ED;
}
.evidyaStore .Sidewrapper ul li {
  padding: 0.2rem 0;
}
.evidyaStore .Sidewrapper ul li a {
  font-family: 'Roboto-Regular';
  cursor: pointer;
  font-size: 14px;
  color: #444444;
}
.evidyaStore .Sidewrapper ul li a.active {
  color: #2F80ED;
  font-weight: bold;
}
.evidyaStore .Sidewrapper ul.subCategory .sub-menu {
  overflow-y: auto;
  max-height: 170px;
}
.evidyaStore .result-wrapper p {
  padding: 0;
  margin: 0;
}
.evidyaStore .result-wrapper button {
  margin-left: 10px;
  color: #2F80ED;
  padding: 0.3rem 1.5rem;
  font-family: 'Roboto-Regular';
  font-weight: (min-width: 768px) and (max-width: 991px);
}
.evidyaStore .result-wrapper button i {
  color: rgba(68, 68, 68, 0.5);
  margin-left: 10px;
}
.evidyaStore .result-wrapper button.modify {
  background: #1E4598;
  color: #fff;
}
.evidyaStore .showResult > div {
  border-left: 1px solid rgba(0, 0, 0, 0.3);
  padding: 0px 1px;
  width: 30%;
  text-align: center;
}
.evidyaStore .showResult > div:first-child {
  border: none;
}
.evidyaStore .showResult p {
  color: #444444;
}
.evidyaStore .showResult p span {
  font-weight: bold;
}
.evidyaStore .showResult input[type='number'] {
  width: 40px;
}
.evidyaStore .bg-gray {
  background: #eff1f8;
  padding: 1rem;
}
.evidyaStore .subCategory {
  padding: 0;
  list-style-type: none;
}
.evidyaStore .bookImg {
  text-align: center;
}
.pagination {
  padding: 0;
  margin: 0;
}
.pagination .page-link {
  color: #444444 !important;
}
.pager {
  justify-content: flex-end;
}
.pager p {
  color: #444444;
  margin-right: 10px;
  font-size: 16px;
}
.pager input[type='text'] {
  width: 40px;
  margin-right: 10px;
}
.pager .go {
  background: #444444;
  border: none;
  outline: 0;
  border-radius: 4px;
  width: 70px;
  color: #fff;
}
.border-pagination {
  border-radius: 4px;
  height: 60px;
  padding: 0!important;
  overflow-y: hidden;
  overflow-x: auto;
}
.bookContainer {
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  padding: 2rem 0;
}
.bookContainer:last-child {
  border-bottom: none;
}
.bookContainer .language {
  background: #2b383e;
  min-width: 80px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px 0;
}
.bookContainer .language img {
  width: 28px;
  height: 18px;
}
.bookContainer .language span {
  color: #fff;
  text-transform: uppercase;
  font-size: 12px;
}
.bookContainer .language.hindi {
  background: #EE5670;
}
.bookContainer .language.tamil {
  background: #3D8CFF;
}
.bookContainer .language.marathi {
  background: #239A99;
}
.bookContainer .language.english {
  background: #233982;
}
.bookContainer .language.bengali {
  background: green;
}
.bookContainer .language.kannada {
  background: #FF7560;
}
.bookContainer .language.malayalam {
  background: #6753FF;
}
.bookContainer .language.odiya {
  background: #FFE51E;
}
.bookContainer .language.sanskrit {
  background: #FFBF00;
}
.bookContainer .language.urdu {
  background: #0b163f;
}
.bookContainer .language.telugu {
  background: #0b2e13;
}
.bookContainer .textWrapper {
  margin-left: 1rem;
}
.bookContainer .textWrapper h3 {
  font-size: 16px;
  font-weight: bold;
  color: #444444;
}
.bookContainer p {
  font-size: 14px;
  color: #444444;
}
.bookContainer p.editBy {
  font-weight: bold;
}
.bookContainer p span {
  color: #233982;
}
.bookContainer p a {
  color: #2F80ED;
  font-weight: normal;
  margin-left: 5px;
}
.bookContainer h4 {
  font-size: 14px;
  margin-bottom: 0;
  font-weight: bold;
  color: #444444;
}
.bookContainer .bookImage {
  height: 233px;
  width: 153px;
}
.bookContainer .languageIcon {
  height: 20px;
}
.bookContainer .addLibrary {
  position: absolute;
  bottom: 30px;
  right: 10px;
}
.bookContainer .addLibrary a {
  color: #2F80ED;
  align-items: center;
  font-weight: bold;
}
.bookContainer .addLibrary a:hover {
  text-decoration: none;
}
.bookContainer .addLibrary a i {
  margin-right: 8px;
  color: #2F80ED;
}
.add {
  display: none;
}
.minus .minimize {
  display: none;
}
.minus .add {
  display: block;
}
.lock-img {
  position: absolute;
  top: 25px;
  left: -10px;
}
.loading-icon {
  width: 100%;
  height: 100%;
  background-color: rgba(68, 68, 68, 0.64);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.btn-primary1 {
  background: #233982 !important;
  border: #233982;
}
.evidyaStore #displayResults {
  margin-top: 10px;
}
.evidyaStore .result-wrapper button i {
  font-size: 20px;
}
.about-banner img {
  width: 100%;
  border-bottom: 1px solid #DDDDDD;
}
.aboutus {
  padding-bottom: 4rem;
}
.aboutus p {
  color: #777777;
  font-size: 20px;
}
.aboutus p a {
  color: #383f8b;
  text-decoration: underline;
  font-family: 'Roboto-Bold';
  font-size: 20px;
}
.contactus h1 {
  color: #000000;
  font-size: 30px;
  font-family: 'Roboto-Bold';
}
.contactus p {
  color: #777777;
  font-size: 15px;
  font-family: 'Roboto-Regular';
}
.contactus a {
  font-size: 15px;
  color: #383f8b;
  text-decoration: underline;
}
.contactus .contact_form .form-check label {
  font-family: 'Roboto-Regular';
}
.contactus .contact_form label {
  font-family: 'Roboto-Regular';
}
.contactus .contact_form textarea {
  min-height: 120px;
}
.privacy h2,
.terms h2 {
  color: #333;
  font-size: 20px;
  font-family: 'Roboto-Regular';
  margin-top: 2rem;
}
.privacy p,
.terms p {
  color: #777777;
  font-size: 15px;
  font-family: 'Roboto-Regular';
  margin-bottom: 0;
}
.privacy p a,
.terms p a {
  color: #383f8b;
  text-decoration: underline;
  font-size: 15px;
}
.privacy ul li,
.terms ul li {
  color: #777777;
  font-size: 15px;
  font-family: 'Roboto-Regular';
}
.privacy h3,
.terms h3 {
  font-size: 18px;
  font-family: 'Roboto-Regular';
  color: #333333;
  margin-top: 1rem;
}
.privacy h1,
.terms h1 {
  font-size: 30px;
  font-family: 'Roboto-Regular';
  color: #000000;
}
.feedback_form .form-row {
  /*p {
    margin-bottom: 0;
  }*/
}
.custom-control-label::before {
  background-color: #fff;
  border: #adb5bd solid 1px;
  box-shadow: 0 0 3px;
}
.custom-control input[type="radio"]:focus + label {
  border: none !important;
}
.custom-control input[type="radio"]:checked + label {
  background-color: transparent !important;
  color: #777777 !important;
}
.custom-control {
  display: inline-flex;
}
.custom-control-label::after,
.custom-control-label::before {
  top: 0;
}
.custom-control-label {
  font-size: 14px;
  color: #777777 !important;
}
.libraryDimension {
  min-height: calc(100vh - 175px);
}
.libraryDimension ul.nav {
  display: block;
}
.libraryDimension .tab-content {
  background: #ffffff;
  padding: 1rem;
  margin: 1rem;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  min-height: calc(100vh - 190px);
}
.libraryDimension h4 {
  color: #232323;
  font-size: 14px;
  padding: 0 1rem ;
  margin-top: 2rem;
  font-family: 'Roboto-Regular';
  display: flex;
}
.libraryDimension h4 img {
  margin-left: 1rem;
}
.libraryDimension h3 {
  color: #777777;
  padding: 0 2rem;
  font-family: 'Roboto-Regular';
  font-weight: (min-width: 768px) and (max-width: 991px);
  font-size: 14px;
}
.libraryDimension p {
  padding: 0.5rem 2rem;
  color: #888888;
  font-size: 14px;
}
.libraryDimension p img {
  margin-right: 5px;
}
.libraryDimension .nav-tabs .nav-link {
  border-radius: 0px;
}
.libraryDimension .nav-tabs .nav-item.show .nav-link,
.libraryDimension .nav-tabs .nav-link.active {
  background: rgba(31, 65, 153, 0.15);
}
.libraryDimension .nav-tabs .nav-item.show .nav-link,
.libraryDimension .nav-tabs .nav-link:focus {
  background: rgba(31, 65, 153, 0.15);
}
.libraryDimension .nav-tabs {
  border: none;
  border-radius: 0px;
  border-top: 1px solid rgba(119, 119, 119, 0.1);
}
.libraryDimension .nav-tabs li {
  border-radius: 0px;
  border: none;
}
.libraryDimension .nav-tabs li a {
  border: none;
  font-size: 18px;
}
.libraryDimension .nav-tabs li a.nav-link {
  padding: 1rem 1.5rem;
}
.libraryDimension .nav-tabs li a img {
  margin-right: 1rem;
  width: 24px;
  height: 24px;
}
.bgBlue {
  background: rgba(31, 65, 153, 0.15);
}
.bgWhite {
  background: #ffffff;
}
.container-wrapper {
  border-bottom: 1px solid #dee3f0;
  padding-bottom: 0.5rem;
}
.container-wrapper h3 {
  font-size: 16px;
  color: #777777;
  font-weight: bold;
  font-family: 'Roboto-Regular';
}
.container-wrapper p {
  color: #ffffff;
  background: #1E4598;
  border-radius: 4px;
  padding: 5px 10px;
  margin: 0;
  font-family: 'Roboto-Regular';
}
.container-wrapper select {
  border: none;
  background: none;
  color: #777777;
  font-family: 'Roboto-Regular';
}
.container-wrapper input.search {
  border-radius: 50px;
  width: 300px;
  color: #1E4598;
  font-size: 16px;
  background: #dee3f0;
  padding: 5px 10px;
  outline: 0;
  font-family: 'Roboto-Regular';
}
.searchBtn {
  background: none;
  border: none;
  margin-left: -3rem;
  position: relative;
  top: 6px;
}
.searchBtn i {
  color: #1E4598;
}
.bookContainer > div:first-child {
  width: 18%;
}
.bookContainer > div:nth-child(2) {
  width: 65%;
}
.bookContainer > div:nth-child(3) {
  width: 17%;
  text-align: center;
}
.bookContainer .addLibrary a {
  color: #233982;
  align-items: center;
  font-weight: bold;
}
.bookContainer .addLibrary a i {
  margin-right: 8px;
  color: #233982;
}
footer {
  align-items: center;
  background: #DCDDDE;
  position: relative;
  /*&:before {
    content: '';
    position: absolute;
    top: 0;
    //left: -490px;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #37474f;
  }*/
}
footer .social-icons img {
  width: 45px;
  height: 45px;
}
footer .promise-icon img {
  width: 150px;
  height: auto;
}
footer.footer-menu > .row {
  display: flex;
  align-items: center;
}
footer.footer-menu ul li {
  list-style-type: none;
}
footer.footer-menu ul li a {
  color: #444444;
}
footer p {
  color: black;
}
footer a {
  color: black;
  padding-right: 0.25rem;
}
#syllabusError,
#examError,
#subjectError {
  color: #B72319;
}
.btn-primary {
  color: #ffffff !important;
  background-color: #1F419B !important;
  border-color: #1F419B !important;
}
.btn-primary:hover {
  background-color: #1F419B !important;
}
.btn-primary:focus {
  background-color: #1F419B !important;
}
.btn-primary:active {
  background-color: #1F419B !important;
}
.btn-primary:active:focus {
  box-shadow: 0 0 0 0.2rem rgba(31, 65, 155, 0.5) !important;
}
.managetabs .btn.disabled {
  background-color: #1F419B;
  opacity: 0.5;
}
.managetabs .alert {
  display: block;
  width: 100%;
}
.managetabs select {
  border: 1px solid #ced4da;
  font-family: 'Rubik', sans-serif;
}
.managetabs .form-control {
  border: 1px solid #ced4da;
}
.managetabs .custom-control {
  display: table-cell;
}
.managetabs .custom-control-label {
  bottom: 20px;
  left: 15px;
  cursor: pointer;
}
.managetabs .custom-control-label::before,
.managetabs .custom-control-label::after {
  width: 1.2rem;
  height: 1.2rem;
}
.managetabs .featured_btn {
  display: inline;
  font-size: 12px;
  font-weight: normal;
  position: relative;
  top: -3px;
}
.manageExams .btn {
  color: #ffffff;
  background-color: #1F419B;
}
.manageExams .btn:focus {
  background-color: #1F419B !important;
}
.manageExams .btn:active {
  background-color: #1F419B !important;
}
.manageExams select {
  border: 1px solid #ced4da;
  font-family: 'Rubik', sans-serif;
}
.manageExams .form-control {
  border: 1px solid #ced4da;
}
.manageExams a.btn.disabled {
  background-color: #1F419B;
  opacity: 0.5;
}
.manageExams .col-form-label {
  line-height: 1;
  font-weight: 500;
}
.manageExams .text-warning {
  color: #1F419B !important;
}
.manageExams textarea {
  min-height: 120px;
  font-family: 'Rubik', sans-serif;
}
.pub-desks .nav-tabs {
  border-bottom: none;
  padding-left: 15px;
}
.pub-desks .nav-link {
  display: block;
  padding: 1rem 3rem;
  border-color: #eee;
}
.nav-pills .nav-link:active:focus,
.nav-pills .nav-link:focus {
  background-color: #1F419B !important;
  color: #ffffff !important;
  outline: 0;
}
.nav-pills .nav-link.active {
  background-color: #1F419B !important;
  border-color: #1F419B !important;
}
.nav-pills .nav-link.active:hover {
  color: #ffffff !important;
}
.nav-pills .nav-link:hover {
  color: #1F419B !important;
  border-color: #1F419B !important;
}
.publishing_desk h4 strong {
  color: #1F419B;
}
table .bg-primary {
  background-color: #1F419B !important;
}
table td a {
  color: #212529;
}
table td a:hover {
  color: #007bff;
}
.custom_pagination .page_nos {
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 5px;
  box-shadow: 0 0 3px #ddd;
}
#pubTable tr a,
#pubTable td a {
  word-wrap: break-word;
  white-space: initial;
}
.eutkarsh .add-notesPr > a {
  height: 44px;
  background: #2EBAC6;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 600px;
  color: #ffffff;
  width: 160px;
  border: none;
  display: flex;
  align-items: center;
  float: right;
  justify-content: center;
}
.panelTab {
  border-bottom: #1F419B;
  margin-top: 1rem;
  border: none;
  padding: 0px;
}
.panelTab .nav-link {
  color: #1F419B;
}
.panelTab a:hover {
  color: #5ec8d7;
}
#book .form-group {
  padding-bottom: 0px;
}
#testStartDate {
  border: 1px solid #EDEDED;
  border-radius: 4px;
}
.greytext a {
  color: black;
}
.greytext a:hover,
.greytext a:focus {
  color: #5ec8d7;
}
#bookcover i {
  font-size: 20px;
  color: black;
}
#bookcover span {
  color: grey;
  font-size: 14px;
}
#bookcover .smallText {
  font-size: 13px;
  height: 150px;
  display: flex;
  align-items: center;
  margin-top: -31px;
}
.text-left .icon {
  text-align: center;
}
.text-left .icon1 {
  text-align: center;
}
.text-left label .smallText {
  font-size: 12px;
  height: 150px;
  display: flex;
  align-items: center;
}
#bookcover i {
  font-size: 48px;
  color: black;
}
.dropdown-menu li a {
  color: black;
}
#chapterdetailsdetails {
  padding: 0px;
  margin: 0px;
}
#chapterdetailsdetails .form-group {
  padding-bottom: 0px;
}
#chapterdetailsdetails .details {
  padding: 0px;
  white-space: nowrap;
}
.dropdown-menu li a:hover {
  background-color: #cccccc;
}
.dropdown-menu a:hover {
  text-decoration: none;
}
.buk {
  margin-top: -32px;
}
.buk1 {
  margin-top: -32px;
}
#notpublished .material-icons {
  font-size: 14px;
}
#resId {
  margin-top: 20px;
  border: 1px solid #aaa;
}
#readResId {
  margin-top: 20px;
  border: 1px solid #aaa;
}
.bukerror {
  white-space: nowrap;
  color: darkred;
}
.book {
  margin-left: 18px;
}
.buks {
  margin-left: 0px;
}
.alert {
  position: relative;
  margin-bottom: 1rem;
  margin-right: 32px;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}
.overlay-fade-in .image-overlay-content {
  opacity: 0;
  background-color: rgba(0, 0, 0, 0.4);
  transition: all 0.4s ease-in-out;
  text-align: center;
}
#newauthor {
  margin-top: 10px;
}
#newpublisher {
  margin-top: 10px;
}
#cke_1_contents {
  min-height: 300px !important;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder {
  color: black;
  background-color: white;
  border: 1px solid lightgray;
}
.authors a {
  color: black;
}
.form-group {
  padding-bottom: 0px;
}
.form-group a {
  color: black;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333333;
  white-space: nowrap;
}
.buk3 {
  margin-top: -18px;
}
.bukadds {
  margin-left: -17px;
}
#chaptersList a {
  color: black;
}
#chaptersList a:hover {
  color: #5ec8d7;
}
.btn-group .btn-primary {
  color: #ffffff;
  background-color: #1F419B;
  border-color: #1F419B;
}
#addChapters .btn-primary {
  color: #ffffff;
  background-color: #1F419B;
  border-color: #1F419B;
}
#addChapters .btn-primary btn:focus,
#addChapters .btn-primary .btn:active {
  outline: none !important;
  box-shadow: none;
}
#addedContents a {
  color: black;
}
#addedContents a:hover {
  color: #5ec8d7;
}
.buks4 .btn-primary {
  color: #ffffff;
  background-color: #1F419B;
  border-color: #1F419B;
}
.buks4 .btn:focus,
.buks4 .btn:active {
  background-color: #1F419B !important;
}
.show li {
  color: #333333;
}
.show li a:hover {
  color: #333333;
}
.filelink .form-control {
  display: block;
  width: 100%;
  height: 42px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555555;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
#createBook #testStartDate {
  border: 1px solid #cccccc;
}
#createBook #testEndDate {
  border: 1px solid #cccccc;
}
#published .material-icons {
  font-size: 14px;
}
.sage-body #quizModal .close-modal.close-sagemodal {
  position: relative;
  top: 0px;
}
.sage-body i {
  font-size: 14px;
}
.book_details_info .updateBtn {
  color: #0AAEF9;
  border-color: #0AAEF9;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: normal;
}
.book_details_info .image-wrapper {
  height: 160px;
}
.book_details_info #addedtags tr td {
  padding: 0.2rem;
}
#bookChapters .publishBtn {
  display: flex;
  align-items: center;
  font-size: 14px;
}
#bookChapters .publishBtn i {
  font-size: 18px;
  margin-right: 8px;
}
.sale-table tr {
  border: 1px solid black;
}
#searchForm btn-primary {
  color: #fff;
  background-color: #CF6C00;
  border-color: #CF6C00;
}
#studySets .study-set-textarea {
  font-size: 16px;
  width: 100%;
  resize: none;
  border: 0;
  border-bottom: 0px solid #455358;
  outline: none;
  overflow: hidden;
}
#studySets #revisionTitleInput .study-set-textarea {
  font-size: 16px;
  width: 427px;
  resize: none;
  background-color: #f8f8f8;
  border: none;
  border-bottom: 0px solid #455358;
  outline: none;
  overflow: hidden;
}
#studySets #study-set-wrapper .study-set-textarea {
  font-size: 16px;
  width: 427px;
  resize: none;
  background-color: #f8f8f8;
  border: none;
  border-bottom: 0px solid #455358;
  outline: none;
  overflow: hidden;
}
#studySets #keyvalueholder .study-set-textarea {
  font-size: 16px;
  width: 427px;
  resize: none;
  background-color: #f8f8f8;
  border: 0;
  outline: none;
  overflow: hidden;
}
#revisionTitle {
  display: flex;
  align-items: center;
}
.test {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: 342px;
  width: 100%;
}
.study-set-item {
  padding: 16px 24px 16px 48px;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.study-set-wrapper .study-set-item .form-group {
  background: #f8f8f8;
}
.study-set-wrapper .add-study-card-btn {
  background: #FFFFFF;
  border-bottom: 2px solid #2EBAC6;
  padding: 0.5rem 2rem;
  color: #2EBAC6;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
  text-decoration: none;
}
.study-set-wrapper .add-study-card-btn:hover {
  color: #1F419B;
  border-bottom: 2px solid #1F419B;
}
.store .tab-content .tab-pane > div > h3 {
  font-size: 18px;
  font-weight: 500;
  color: #444444;
  margin-top: 20px;
  font-family: 'Rubik', sans-serif;
}
.store .topSchoolBooks .content-wrapper h3 {
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  font-weight: normal;
  margin-bottom: 0;
  width: 132px;
}
.store .topSchoolBooks .content-wrapper h3 {
  height: 63px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.store .topSchoolBooks {
  padding: 10px;
  padding-bottom: 10px;
}
.study-set-wrapper .input-studyset.termDimen {
  width: 300px ;
}
/* Revision Mobile UI Reset Styles*/
@media screen and (max-width: 767px) {
  .revision_ui .chapternav-wrapper {
    margin-top: 0;
    margin-bottom: 1rem;
  }
  .revision_ui .revision_setup ::-webkit-input-placeholder {
    font-size: 16px;
  }
  .revision_ui .revision_setup ::-moz-placeholder {
    font-size: 16px;
  }
  .revision_ui .revision_setup :-ms-input-placeholder {
    font-size: 16px;
  }
  .revision_ui .revision_setup :-moz-placeholder {
    font-size: 16px;
  }
  .revision_ui .revision_setup .study-set-item {
    padding: 0;
    margin-right: 0;
    display: contents;
  }
  .revision_ui .revision_setup .input-studyset {
    width: 300px;
  }
  .revision_ui .revision_setup .input-studyset textarea {
    padding: 5px;
    width: 100% !important;
    padding-left: 0;
  }
  .revision_ui .revision_setup .input-studyset label > span {
    left: 0;
  }
  .revision_ui .revision_setup #study-set-form .study-set-main .term-counter:before {
    width: 40px;
    background-color: #e5e5e5;
  }
  .revision_ui .revision_setup #study-set-form .study-set-main .bg-revCard {
    padding-left: 50px;
    padding-right: 10px;
    margin: 0 auto;
  }
  .revision_ui .revision_setup #study-set-form .study-set-main .input-studyset {
    width: 100%;
  }
  .revision_ui .revision_setup #study-set-form .study-set-main .study-set-textarea {
    margin-left: 0;
    padding-left: 0;
  }
  .revision_ui .study-set-main .user-analytic-data-colored {
    display: block !important;
    padding-bottom: 0;
  }
  .revision_ui .study-set-main .user-analytic-data-colored div {
    padding: 0 0 10px;
  }
  .revision_ui .study-set-main .term-counter:before {
    width: 40px;
    background-color: #e5e5e5;
  }
  .revision_ui .study-set-main .bg-revCard {
    padding-left: 50px;
    padding-right: 10px;
    margin: 0 auto;
  }
  .revision_ui .study-set-main .study-set-textarea {
    overflow: scroll !important;
  }
}
@media screen and (max-width: 575px) {
  .revision_ui .revision_setup .input-studyset {
    width: 90%;
  }
  .revision_ui .revision_setup #study-set-form .study-set-main .input-studyset {
    width: 90%;
  }
}
.main {
  background-color: white;
  margin: 10px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}
.firstPage {
  background-color: #D3D3D3;
  padding-left: 50px;
}
.float-label-control {
  display: block;
  width: 100%;
  padding: 0.1em 0em 1px 0em;
  border: none;
  border-radius: 0px;
  outline: none;
  margin-right: 20px;
  background: none;
  display: flex;
}
.cktext {
  width: 100%;
}
.quiz {
  display: -webkit-box;
  width: 80%;
}
.float-label-control input {
  display: block;
  width: 100%;
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #aaa;
  outline: none;
  margin-right: 20px;
  background: none;
}
.quiz1 {
  display: -webkit-box;
  width: 80%;
  padding: 0.1em 3em 5px 4em;
}
.quiz2 {
  display: -webkit-box;
  width: 80%;
  padding: 0.1em 3em 5px 4em;
}
.quiz3 {
  display: -webkit-box;
  width: 80%;
  padding: 1.1em 3em 5px 4em;
}
.quiz4 {
  display: -webkit-box;
  width: 80%;
  padding: 1.1em 3em 5px 4em;
}
.quiz4 #subject {
  width: 33.33%;
}
.quiz5 {
  display: -webkit-box;
  padding: 1.1em 3em 5px 4em;
}
.quiz6 {
  display: -webkit-box;
  padding: 1.1em 3em 5px 16em;
}
.quiz6 .text-center {
  text-align: center;
}
@media (min-width: 768px) {
  .quiz6 .text-center .col-sm-12 {
    width: 100%;
    float: left;
  }
}
.quiz6 .text-center .btn-primary {
  color: #ffffff;
  background-color: #1F419B;
  border-color: #1F419B;
  margin: 0px;
}
.quiz6 .text-center .btn:focus,
.quiz6 .text-center .btn:active {
  background-color: #1F419B !important;
}
.quiz7 {
  display: -webkit-box;
  padding: 1.1em 3em 5px 4em;
}
#static-content h4 {
  margin-bottom: 9px;
  margin-left: 0px;
  font-size: 18px;
  font-weight: 500;
}
.pagenumber-green {
  height: 21px;
  width: 35px;
  text-align: center;
  border-width: 1px;
  border-color: green;
  border-radius: 3px;
  border-style: solid;
  display: inline-block;
  padding: 0px;
  background-color: green;
  color: white;
}
.quiz8 {
  width: 100%;
}
.quiz8 .alert-warning {
  background-color: #f8d9ac;
  border-color: #f6bd95;
  color: #f0ad4e;
}
.quiz8 .alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
  margin-left: 14px;
}
#sidebar a:focus {
  background-color: green;
  color: black;
  text-decoration: none;
}
#sidebar a:active {
  background-color: green;
  color: black;
  text-decoration: none;
}
#sidebar a:hover {
  text-decoration: none;
}
#quizcreatorbulk {
  padding: 30px;
}
#quizcreatorbulk .row {
  padding-left: 50px;
}
.qbulk {
  padding-top: 20px;
}
.quizb {
  display: flex;
  align-items: center;
}
.quizb1 .float-label-control input {
  display: block;
  width: 100%;
  padding: 7.1em 0em 23px 0em;
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #aaa;
  outline: none;
  margin-right: 20px;
  background: none;
}
.notes {
  display: flex;
  align-items: center;
  padding: 0.1em 0em 1px 1em;
}
.qanda {
  display: -webkit-box;
  width: 80%;
  padding: 0.1em 3em 5px 0em;
}
.qanda1 {
  padding: 0.1em 2em 1px 1em;
}
.qanda1 .cke_bottom {
  padding: 6px 8px 2px;
  position: relative;
  border-top: 2px solid #d1d1d1;
  border-bottom: 2px solid #d1d1d1;
  background: #f8f8f8;
}
.qanda2 {
  padding: 0.1em 2em 18px 3em;
}
.dflex_pub {
  padding-top: 0px;
  display: flex;
  align-items: center;
}
.dflex_pub .form-control {
  width: 40%;
  margin: 5px;
}
.dflex_pub .btn-primary {
  color: #ffffff;
  background-color: #1F419B;
  border-color: #1F419B;
}
.dflex_pub .btn:focus,
.dflex_pub .btn:active {
  background-color: #1F419B !important;
}
.publish-management a {
  color: #0AAEF9;
  padding: 10px;
}
.publish-management a:hover {
  color: #0AAEF9;
}
.alert-danger {
  border-color: #e78e9a;
  color: #d9534f;
}
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
}
.flex_st a {
  color: black;
}
.flex_st a:hover {
  color: blue;
}
.flex_st {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.adminForm a {
  color: #F79420;
  padding: 10px;
  font-size: 18px;
}
.adminForm a:hover {
  color: #F79420;
}
.adminForm .form-control {
  width: 33.33%;
}
.adminForm .form-control.admin {
  width: 90%;
}
.dflex .form-group .form-control {
  width: 90%;
}
textarea.form-control {
  height: auto;
}
.dflex .form-group {
  width: 33.33%;
}
.dflex {
  display: flex;
}
#content-books {
  padding: 0px;
}
#content-books .form-group {
  margin-bottom: 15px;
  padding: 0px;
}
.ad #endDate {
  border: 1px solid #EDEDED;
  margin-left: 20px;
}
::-webkit-input-placeholder {
  font-size: 17px;
  color: grey;
}
#errormsg .alert {
  position: relative;
  margin-bottom: 1rem;
  margin-left: 0px;
  margin-right: 32px;
  border: 1px solid red;
  border-radius: 0.25rem;
}
#errormsg .alert-danger {
  color: red;
}
.adminForm .btn:focus,
.adminForm .btn:active {
  background-color: #1F419B !important;
}
.eutkarsh #loginSignup .continue {
  background: linear-gradient(270deg, #1F419B 0%, #1F419B 100%);
  color: #ffffff;
  text-transform: uppercase;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
}
.eutkarsh .add-notesPr > a {
  height: 44px;
  background: #1F419B;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 600px;
  color: #ffffff;
  width: 160px;
  border: none;
  display: flex;
  align-items: center;
  float: right;
  justify-content: center;
}
.eutkarsh .user_profile .tab-content .jumbotron form .media .continue {
  width: 312px;
  background: linear-gradient(270deg, #1F419B 0%, #1F419B 100%);
  color: #ffffff;
  text-transform: uppercase;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  float: right;
}
.eutkarsh .ws-header .navbar-brand .logo {
  height: 48px;
  width: 75px;
}
.eutkarsh .btn-starts {
  background: linear-gradient(97.72deg, #1F419B 27.28%, #1F419B 80.81%);
  border-radius: 4px;
  color: #ffffff;
}
.eutkarsh .preview-book-btns .btn-book-buy {
  background: #1F419B;
  width: 160px;
  height: 40px;
  color: #ffffff !important;
  font-size: 14px;
  margin: 1rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.eutkarsh #book-read-material .quiz-item-wrapper .quiz-buttons .quiz-practice-btn {
  color: #ffffff;
  background: #1F419B;
}
.eutkarsh #book-read-material .quiz-item-wrapper .quiz-buttons .quiz-practice-btn:focus {
  background: #1F419B !important;
}
.eutkarsh #openApps .logoWs {
  background: #ffffff;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  left: -13px;
  border-radius: 50px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
}
.eutkarsh #openApps .get-app {
  background: linear-gradient(270deg, #1F419B 0%, #1F419B 100%);
  color: #ffffff;
}
.eutkarsh .next-btn {
  height: auto;
  font-size: 14px;
  color: #fff;
  background: #1F419B;
  padding: 8px;
  border: 0;
}
.eutkarsh .next-btn:focus {
  background: #1F419B !important;
}
.eutkarsh .btn-info {
  color: #fff;
  background-color: #1F419B;
  border-color: #1F419B;
}
.eutkarsh #addRefButton > .dropdown .dropdown-menu a i,
.eutkarsh #videoAddButton > .dropdown .dropdown-menu a i,
.eutkarsh #content-data-userNotes > .dropdown .dropdown-menu a i,
.eutkarsh #allAddButton > .dropdown .dropdown-menu a i {
  font-size: 20px;
  float: right;
  background: #1F419B;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.24), 0px 0px 6px rgba(0, 0, 0, 0.24);
  border-radius: 50px;
  width: 32px;
  height: 32px;
  line-height: 1.4;
  display: block;
  text-align: center;
  margin-left: 1rem;
}
.eutkarsh .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item span {
  color: #007bff;
}
.eutkarsh .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item span:hover {
  color: #007bff;
  text-decoration: underline;
}
.eutkarsh #loginSignup .no-account a {
  color: #007bff;
  text-decoration: underline;
  cursor: pointer;
  font-size: 12px;
  text-transform: none;
}
.eutkarsh .sign-in p a.forgot-password {
  color: #007bff;
  cursor: pointer;
  margin-right: 5px;
}
.eutkarsh .ws-header div.mobile-profile .nav-item {
  color: white;
}
.evidya #allAddButton .dropdown button {
  display: none;
}
.store .tab-content .tab-pane > .d-flex.justify-content-between.align-items-center {
  position: absolute;
  top: -36px;
  width: 100%;
  left: 0;
  padding-left: 20px;
}
#siteId {
  margin-bottom: 20px;
}
.publish-management #content-books {
  padding: 30px;
  padding-bottom: 0px;
}
.publish-management .btn-primary {
  color: #ffffff;
  background-color: #1F419B;
  border-color: #1F419B;
  margin: 0px;
  /* margin-left: 1px; */
}
#quizQuestionSection .web-mcq .sub-header {
  position: fixed;
  top: 64px;
  /* z-index: 999; */
}
#content-books .form-group {
  padding-bottom: 0px;
}
#intrst-area1 .btn:focus,
#intrst-area1 .btn:active {
  background-color: transparent !important;
}
.videoPlays .video-img-wrapper {
  position: relative;
}
.store .topSchoolBooks .content-wrapper h3 {
  height: 43px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* number of lines to show */
  -webkit-box-orient: vertical;
}
.store .topSchoolBooks .content-wrapper h3 {
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  font-weight: normal;
  margin-bottom: 0;
  width: 132px;
}
#keyvalueholder .study-set-textarea {
  font-size: 16px;
  width: 427px;
  resize: none;
  background-color: #f8f8f8;
  border-bottom: 2px solid #455358;
  outline: none;
  overflow: hidden;
  margin-left: 20px;
}
#keyvalueholder .study-set-textarea:hover {
  border-color: #ef4912;
}
#revisionTitleInput .study-set-textarea {
  font-size: 16px;
  width: 427px;
  resize: none;
  background-color: #f8f8f8;
  border: 0;
  border-bottom: 2px solid #455358;
  outline: none;
  overflow: hidden;
  transition: border-bottom 0.2s ease-in-out;
}
@media (min-width: 992px) {
  #revisionTitleInput .col-md-offset-4 {
    margin-left: 33.33333333%;
  }
}
#study-set-wrapper .study-set-textarea {
  font-size: 16px;
  width: 427px;
  resize: none;
  background-color: #f8f8f8;
  border: none;
  border-bottom: 2px solid #455358;
  outline: none;
  overflow: hidden;
}
#study-set-wrapper .study-set-textarea:hover {
  border-color: #ef4912;
}
#allAddButton .dropdown show .dropdown-menu show {
  position: absolute;
  will-change: transform;
  top: 0px;
  left: 0px;
  transform: translate3d(841px, 73px, 0px);
}
.miguser .btn-primary {
  color: #ffffff;
  background-color: #1F419B;
  border-color: #1F419B;
  margin: 20px;
  margin-left: 2px;
}
.miguser .btn-primary .btn-primary:hover {
  background-color: #1F419B;
}
.miguser .btn:focus,
.miguser .btn:active {
  background-color: #1F419B !important;
}
@media (min-width: 1920px) {
  .discount-manager .container-fluid {
    width: 80%;
  }
}
.discount-manager input[type="radio"]:focus + label {
  border: none;
}
.discount-manager input[type="radio"]:checked + label {
  color: inherit;
  background-color: transparent;
}
.discount-manager .status-filter-toggle {
  position: relative;
  z-index: 1;
  width: 350px;
  margin: 0 auto -30px;
}
.discount-manager .status-filter-toggle .form-check input,
.discount-manager .status-filter-toggle .form-check label {
  cursor: pointer;
}
.discount-manager #discountList table tr th:first-child,
.discount-manager #discountList table tr th:nth-child(3),
.discount-manager #discountList table tr th:nth-child(4),
.discount-manager #discountList table tr th:nth-child(5),
.discount-manager #discountList table tr th:nth-child(6),
.discount-manager #discountList table tr th:nth-child(7) {
  width: 85px !important;
}
.discount-manager #discountList table tr th:nth-child(2),
.discount-manager #discountList table tr th:nth-child(8) {
  width: 70px !important;
}
.discount-manager #discountList table tr th:nth-child(9),
.discount-manager #discountList table tr th:nth-child(10) {
  width: 100px !important;
}
.discount-manager #discountList table tr th:nth-child(11) {
  width: 50px !important;
}
.discount-manager #discountList table tr th:nth-child(12),
.discount-manager #discountList table tr th:last-child {
  width: 60px !important;
}
.discount-manager #couponCode::-webkit-input-placeholder {
  text-transform: initial;
}
.discount-manager #couponCode:-moz-placeholder {
  text-transform: initial;
}
.discount-manager #couponCode::-moz-placeholder {
  text-transform: initial;
}
.discount-manager #couponCode:-ms-input-placeholder {
  text-transform: initial;
}
.create {
  margin-top: 43px;
  margin-bottom: 30px;
  margin-left: 73px;
}
.create1 {
  margin-left: 77px;
  margin-right: -15px;
}
#bookcover span {
  color: grey;
  font-size: 14px;
}
#bookcover .smallText {
  font-size: 13px;
  height: 150px;
  display: flex;
  align-items: center;
  margin-top: -31px;
}
.form-group {
  padding-bottom: 0px;
}
.form-group a {
  color: black;
}
.authors a {
  color: black;
}
.cre {
  color: darkred;
}
.bootstrap-select > .dropdown-toggle.bs-placeholder {
  color: black;
  background-color: white;
  border: 1px solid lightgray;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333333;
  white-space: nowrap;
}
.buks {
  margin-left: 0px;
  margin-top: 10px;
  margin-bottom: 27px;
}
.flex_st {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.adminForm .btn-primary {
  color: #ffffff;
  background-color: #f15b2a;
  border-color: #ef4912;
}
::-webkit-input-placeholder {
  font-size: 15px;
  color: #cccccc;
}
.lib2 {
  font-size: 20px;
}
#addBooks .form-group {
  font-size: 18px;
}
.table-responsive {
  overflow-x: auto;
  min-height: 0.01%;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}
.table > tbody > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #dddddd;
}
.table-bordered th {
  width: 470px;
}
.adminForm .btadd {
  color: #ffffff;
  background-color: #f15b2a;
  border-color: #ef4912;
  margin-left: 2px;
  margin-top: -2px;
  margin-bottom: 0px;
}
#content-books1 .alert {
  position: relative;
  margin-bottom: 1rem;
  margin-left: 0px;
  margin-right: 32px;
  border: 1px solid red;
  border-radius: 0.25rem;
}
#content-books1 .alert-danger {
  color: red;
}
#batchUsers p {
  margin-bottom: 0;
}
.liad {
  font-weight: normal;
}
#published-books .evidya-Title td.pub-buk a {
  word-wrap: break-word;
  white-space: initial;
  padding: 0;
}
#content-books .alert {
  position: relative;
  margin-bottom: 1rem;
  margin-left: 0px;
  margin-right: 32px;
  border: 1px solid red;
  border-radius: 0.25rem;
}
#content-books .alert-danger {
  color: red;
}
.datepicker > .datepicker_header > span {
  margin-left: 4px !important;
  margin-right: 6px !important;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
}
.datepicker > .datepicker_inner_container > .datepicker_calendar > .datepicker_table > tbody > tr > td {
  padding: 0.5rem;
  border: 1px solid #000;
}
.flex_st {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.adminForm a {
  color: #F79420;
  padding: 10px;
  font-size: 18px;
}
.adminForm a:hover {
  color: #F79420;
}
.adminForm .form-control {
  width: 33.33%;
}
.adminForm .form-control.admin {
  width: 90%;
}
.dflex .form-group .form-control {
  width: 90%;
}
textarea.form-control {
  height: auto;
}
.dflex .form-group {
  width: 33.33%;
}
.dflex {
  display: flex;
}
#content-books {
  padding: 0px;
}
#content-books .form-group {
  margin-bottom: 15px;
}
.ad #endDate {
  border: 1px solid black;
  margin-left: 20px;
}
::-webkit-input-placeholder {
  font-size: 17px;
  color: grey;
}
#batchName {
  margin-left: 20px;
}
#addChapters #addedContents {
  text-align: center;
}
#addChapters #addedContents td.d-flex {
  border: none;
}
#addChapters #addedContents td .btn-primary {
  margin-right: 5px;
}
.store {
  padding-top: 2rem;
  min-height: 75vh;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store {
    padding-top: 2rem;
    min-height: auto;
  }
}
.store .search {
  width: 248px;
  height: 48px;
  background: #ffffff;
  padding-left: 40px;
  position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .store .search {
    width: 100%;
  }
}
.store i.searchIcon {
  color: rgba(68, 68, 68, 0.7);
  position: absolute;
  top: 14px;
  left: 13px;
}
.store .bl-left {
  border-left: 1px solid #EDEDED;
  min-height: 70vh;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .bl-left {
    min-height: 100%;
  }
}
.store .tab-menu .nav-pills {
  padding: 1rem 0.5rem;
  border-bottom: 2px solid #EDEDED;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .tab-menu .nav-pills {
    border-bottom: 1px solid #EDEDED;
  }
}
.store .tab-menu .nav-pills .nav-link {
  color: #444444;
  text-align: center;
  padding: 0;
  background: none !important;
}
.store .tab-menu .nav-pills .nav-link:active,
.store .tab-menu .nav-pills .nav-link:focus {
  background: none !important;
}
.store .tab-menu .nav-pills .nav-link i {
  color: rgba(68, 68, 68, 0.8);
}
.store .tab-menu .nav-pills .nav-link span {
  display: block;
  color: #444444;
  font-family: 'Rubik', sans-serif;
  font-size: 14px;
  width: 64px;
  min-height: 34px;
}
.store .tab-menu .nav-pills .nav-link.active {
  color: #1F419B;
  background: none !important;
}
.store .tab-menu .nav-pills .nav-link.active i {
  color: #1F419B;
}
.store .tab-menu .nav-pills .nav-link.active span {
  font-weight: 500;
  color: #1F419B;
}
.store .tab-menu .nav-pills.show > .nav-link {
  color: #1F419B;
  background: none;
}
.store .ai-generate .card {
  border-bottom: none;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.store .ai-generate .card .card-body {
  padding: 10px;
  text-align: center;
}
.store .ai-generate .btn-start {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-color: transparent;
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  color: #ffffff;
  text-transform: uppercase;
}
.store .ai-generate .btn-start:focus {
  outline: none !important;
  border: none;
}
.store .ai-generate .btn-start:active {
  outline: none !important;
  border: none;
}
.filter form label,
.mobile-filter form label {
  font-family: 'Rubik', sans-serif;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.4);
  margin-top: 1rem;
  padding: 0 2px;
}
.filter form .class-selection-btn,
.mobile-filter form .class-selection-btn {
  background: none;
  border-bottom: 1px solid rgba(1, 1, 1, 0.12);
  padding: 8px 2px;
  width: 100%;
  text-align: left;
  border-radius: 0px;
}
.filter form .class-selection-btn.disabled,
.mobile-filter form .class-selection-btn.disabled {
  color: #444444;
}
.filter form .class-selection-btn:after,
.mobile-filter form .class-selection-btn:after {
  float: right;
  position: relative;
  top: 9px;
  right: 7px;
  border: 6px solid transparent;
  border-color: rgba(1, 1, 1, 0.5) transparent transparent transparent;
}
.filter form > div.show,
.mobile-filter form > div.show {
  position: relative;
}
.filter form > div.show #class-selection-btn:after,
.mobile-filter form > div.show #class-selection-btn:after {
  top: 0;
  border-color: transparent transparent rgba(1, 1, 1, 0.5) transparent;
}
.filter form > div.show ul,
.mobile-filter form > div.show ul {
  position: absolute;
  top: 125% !important;
  left: 0;
  right: 0;
  z-index: 99;
  transform: none !important;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  width: 100%;
  max-height: 200px;
  overflow: auto;
  border: none;
  background: #ffffff;
}
.filter form > div.show ul li,
.mobile-filter form > div.show ul li {
  padding: 0;
  cursor: pointer;
  user-select: none;
}
.filter form > div.show ul li a,
.mobile-filter form > div.show ul li a {
  padding: 10px 20px;
  width: 100%;
  display: flex;
  color: #444444;
  font-size: 14px;
}
.filter form > div.show ul li:first-child,
.mobile-filter form > div.show ul li:first-child {
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
}
.filter form > div.show ul li:last-child,
.mobile-filter form > div.show ul li:last-child {
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}
.filter form > div.show ul li:hover,
.mobile-filter form > div.show ul li:hover {
  background: #EDEDED;
  border-radius: 0;
}
.filter form > div.show ul li:hover a,
.mobile-filter form > div.show ul li:hover a {
  text-decoration: none;
}
.mobile-filter {
  width: 100%;
}
.mobile-filter .mob-sort > button {
  position: fixed;
  top: 80%;
  z-index: 999;
  left: 37%;
  background: #1F419B;
  border: none;
  outline: none;
  border-radius: 35px;
  width: 95px;
  height: 30px;
  color: #ffffff;
  justify-content: center;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
#filter-mode {
  z-index: 9999;
}
#filter-mode .modal-dialog {
  margin: 0;
}
#filter-mode .modal-dialog .modal-content {
  height: 100vh;
  border-radius: 0;
  border: none;
}
#filter-mode .modal-dialog .modal-content .modal-body form > div button {
  width: 100%;
  text-align: left;
}
#filter-mode .modal-dialog .modal-content .modal-body form > div button::after {
  float: right;
  margin-top: 8px;
}
#filter-mode .modal-dialog .modal-content .reset {
  background: none;
  border: none;
}
body.modal-open {
  overflow: hidden;
}
.eutkarsh.custom-fix .store div > .quick-sortmenu {
  margin-top: 4rem;
}
.store .tab-content .tab-pane > .d-flex.justify-content-between.align-items-center {
  position: absolute;
  top: 0;
  width: 100%;
  left: 0;
  padding-left: 20px;
}
.store .tab-content .tab-pane > div .custom-select-ws {
  width: 160px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .tab-content .tab-pane > div .custom-select-ws {
    width: 200px;
    margin-right: 20px;
  }
}
.store .tab-content .tab-pane > div .custom-select-ws .select-items {
  z-index: 9999;
}
.store .tab-content .tab-pane > div .custom-select-ws .select-selected {
  border: 1px solid #EDEDED;
  border-radius: 4px;
  font-family: 'Rubik', sans-serif;
  font-size: 12px;
}
.store .tab-content .tab-pane > div .custom-select-ws .select-selected:before {
  content: 'Sort by :';
  color: rgba(68, 68, 68, 0.5);
  font-weight: normal;
  margin-right: 5px;
  padding-left: 10px;
}
.store .tab-content .tab-pane > div > h3 {
  font-size: 18px;
  font-weight: 500;
  color: #444444;
  font-family: 'Rubik', sans-serif;
}
.store .tab-content .tab-pane > div > h3 span {
  color: rgba(68, 68, 68, 0.5);
  font-weight: normal;
}
.store .topSchoolBooks {
  padding: 10px;
  padding-bottom: 0;
}
.store .topSchoolBooks:hover {
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  transition: all 0.5s;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .store .topSchoolBooks:hover {
    width: 152px;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .store .topSchoolBooks:hover {
    width: 152px;
  }
}
.store .topSchoolBooks > a:hover {
  text-decoration: none;
}
.store .topSchoolBooks .image-wrapper {
  width: 132px;
  height: 165px;
  position: relative;
  z-index: 99;
}
.store .topSchoolBooks .image-wrapper > a:hover {
  text-decoration: none;
}
.store .topSchoolBooks .image-wrapper img {
  width: 132px;
  height: 165px;
  position: relative;
  border-radius: 4px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
.store .topSchoolBooks .image-wrapper h3 {
  position: absolute;
  font-size: 10px;
  font-weight: 500;
  color: #ffffff;
  background-color: #1F419B;
  padding: 7px 14px;
  bottom: 32px;
  left: -6px;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
.store .topSchoolBooks .image-wrapper h3:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #1F419B #1F419B transparent transparent;
}
.store .topSchoolBooks .content-wrapper {
  height: 113px;
  margin-top: 8px;
}
.store .topSchoolBooks .content-wrapper a:hover {
  text-decoration: none;
}
.store .topSchoolBooks .content-wrapper h3 {
  height: 43px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* number of lines to show */
  -webkit-box-orient: vertical;
}
.store .topSchoolBooks .content-wrapper h3,
.store .topSchoolBooks .content-wrapper p {
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  font-weight: normal;
  margin-bottom: 0;
  width: 132px;
}
.store .topSchoolBooks .content-wrapper p.sub-name {
  font-weight: 500;
  font-size: 10px;
  margin-bottom: 0.5rem;
}
.store .topSchoolBooks .content-wrapper p.sub-name span {
  color: rgba(68, 68, 68, 0.4);
  font-size: 8px;
  margin-right: 4px;
}
.store .topSchoolBooks .content-wrapper p.complete {
  color: rgba(68, 68, 68, 0.4);
  font-size: 8px;
}
.store .topSchoolBooks .content-wrapper p.price {
  color: #B72319;
  font-size: 14px;
  font-family: 'Merriweather', serif;
}
.store .topSchoolBooks .content-wrapper p.price span {
  font-size: 10px;
  text-decoration: line-through;
  color: rgba(68, 68, 68, 0.6);
  margin-left: 5px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .topSchoolBooks .content-wrapper {
    width: 132px;
  }
}
.store .topSchoolBooks .content-wrapper .price-tag p {
  width: 100%;
}
.test-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.test-wrapper:hover {
  background: #ffffff;
  border-radius: 6px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  transition: all 0.5s;
  text-decoration: none;
}
.test-wrapper .content-wrapper {
  padding: 0.8rem 5px;
}
.test-wrapper .content-wrapper h3 {
  font-size: 8px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.4);
  text-transform: uppercase;
}
.test-wrapper .content-wrapper p {
  font-size: 12px;
  color: #B72319;
}
.test-wrapper .content-wrapper p.sub-name {
  font-size: 10px;
  color: #444444;
  font-weight: 500;
}
.test-wrapper .content-wrapper p.sub-name span {
  color: rgba(68, 68, 68, 0.72);
  margin-right: 0.3rem;
  font-weight: normal;
}
.test-wrapper .card {
  padding: 0.8rem 0.8rem;
  border: 0.5px solid rgba(0, 0, 0, 0.4);
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
  background: #ffffff;
  width: 132px;
  min-height: 165px;
  margin-top: 0.8rem;
}
.test-wrapper .card h4 {
  font-size: 12px;
  color: #444444;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  text-align: center;
}
.test-wrapper .card > p {
  color: rgba(68, 68, 68, 0.4);
  font-size: 8px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  text-align: center;
  margin: 0;
}
.test-wrapper .card h2 {
  font-size: 14px;
  color: #444444;
  font-family: 'Merriweather', serif;
  font-weight: 500;
  text-align: center;
}
.test-wrapper .card .test-content > div {
  width: 50%;
}
.test-wrapper .card .test-content p {
  margin: 0;
  font-size: 12px;
  font-family: 'Merriweather', serif;
  color: #444444;
  text-align: center;
}
.test-wrapper .card .test-content p.language {
  font-family: 'Rubik', sans-serif;
}
.test-wrapper .card .test-content span {
  color: rgba(68, 68, 68, 0.4);
  font-size: 8px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  display: block;
  text-align: center;
  text-transform: uppercase;
}
.butn-wrappers .btn-rank {
  border: 1px solid #F79420;
  font-size: 14px;
  color: #F79420;
  display: block;
  margin-bottom: 0.5rem;
}
.butn-wrappers .btn-attempt {
  background: linear-gradient(94.13deg, #3AE878 13.47%, #30C465 73.92%);
  color: #ffffff;
  display: block;
}
.store .quick-sortmenu {
  position: fixed;
  background: #ffffff;
  z-index: 999;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .quick-sortmenu {
    position: static;
  }
}
.store .quick-sort {
  border-bottom: 1px solid #EDEDED;
  margin-top: 2rem !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .quick-sort .quick-nav {
    flex-wrap: nowrap;
    overflow-y: auto;
    margin-right: -15px;
    margin-left: -15px;
    margin-top: 3rem;
  }
}
.store .quick-sort .quick-nav li {
  white-space: nowrap;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .quick-sort .quick-nav li {
    padding: 0px 10px;
  }
}
.store .quick-sort .quick-nav li a {
  color: #444444;
  font-family: 'Rubik', sans-serif;
  font-size: 14px;
  padding-left: 0;
  padding-right: 1.5rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store .quick-sort .quick-nav li a {
    padding-right: 0;
  }
}
.store .quick-sort .quick-nav li a.active {
  color: #1F419B;
  font-weight: 500;
}
.store .quick-sort .quick-nav button {
  background: none;
  outline: none;
  border: none;
  color: #2F80ED;
  font-family: 'Rubik', sans-serif;
  font-size: 14px;
  cursor: pointer;
}
.store .quick-sort .quick-nav button:before {
  content: '+';
  color: #2F80ED;
  padding: 5px;
}
.no-books-available {
  margin: 0 auto;
}
.no-books-available .no-book-wrapper {
  text-align: center;
}
.no-books-available .no-book-wrapper img {
  width: 200px;
  height: 200px;
}
.book-details {
  margin-top: 2rem;
  padding-bottom: 4rem;
}
.book-details .test-wrapper:hover {
  box-shadow: none;
  background: none;
}
.book-details .breadcrumb {
  background: none;
}
.book-details .breadcrumb li {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.4);
  padding: 0 2px;
  font-family: 'Rubik', sans-serif;
}
.book-details .breadcrumb li.active {
  color: #444444;
  font-weight: 500;
}
.book-details .breadcrumb li a {
  color: rgba(68, 68, 68, 0.4);
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
}
.book-details .topSchoolBooks > a:hover {
  text-decoration: none;
}
.book-details .topSchoolBooks .image-wrapper {
  margin: 0 auto;
  width: 132px;
  height: 165px;
  position: relative;
  z-index: 99;
}
.book-details .topSchoolBooks .image-wrapper > a:hover {
  text-decoration: none;
}
.book-details .topSchoolBooks .image-wrapper img {
  width: 132px;
  height: 165px;
  position: relative;
  border-radius: 4px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
.book-details .topSchoolBooks .image-wrapper h3 {
  position: absolute;
  font-size: 10px;
  font-weight: 500;
  color: #ffffff;
  background-color: #1F419B;
  padding: 7px 14px;
  bottom: 32px;
  left: -6px;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
.book-details .topSchoolBooks .image-wrapper h3:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #1F419B #1F419B transparent transparent;
}
.content-wrappers h1 {
  font-size: 20px;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  font-weight: 500;
  margin-bottom: 0;
}
.content-wrappers .author-name {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.6);
}
.content-wrappers .author-name span {
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: #444444;
}
.content-wrappers .chapt-bk {
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.72);
  text-transform: uppercase;
  font-size: 10px;
}
.content-wrappers .offer-price,
.content-wrappers .original-price {
  color: #B72319;
  font-family: 'Merriweather', serif;
  font-weight: 500;
}
.content-wrappers div.d-flex > div {
  margin-right: 2rem;
}
@media screen and (max-width: 767px) {
  .content-wrappers h1.preview-book-name {
    margin-top: 20px;
  }
}
.preview-book-btns {
  margin-left: 1rem;
}
.preview-book-btns .btn-book-preview {
  border: 1px solid #ededed;
  color: #1F419B !important;
  border-radius: 4px;
  text-transform: uppercase;
  width: 160px;
  height: 38px;
  font-size: 14px;
  margin: 1rem auto;
}
.preview-book-btns .btn-book-buy {
  background: #2EBAC6;
  width: 160px;
  height: 40px;
  color: #ffffff !important;
  font-size: 14px;
  margin: 1rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-book-btns .btn-book-buy:focus {
  background: #2EBAC6 !important;
}
.preview-book-btns #buyNow {
  background: #1F419B;
}
.preview-book-btns #buyNow:focus {
  background: #1F419B !important;
}
.preview-book-desc {
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
  background: #ffffff;
  padding: 2rem 0;
  border-radius: 4px;
  margin-top: -2rem;
}
@media screen and (max-width: 767px) {
  .preview-book-desc {
    margin-top: 0;
  }
}
.showrank-modal .modal-header {
  justify-content: center;
}
.showrank-modal .modal-header button {
  padding: 0;
  margin: 0;
  position: absolute;
  right: 25px;
  top: 25px;
}
.showrank-modal .modal-footer {
  border: none;
}
.showrank-modal .modal-body {
  padding: 0;
}
.showrank-modal .modal-body .table thead th {
  font-size: 12px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.7);
}
.showrank-modal .modal-body tr.user-active td {
  color: #1F419B !important;
}
.showrank-modal .modal-body tr td:first-child {
  font-size: 18px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.7);
  font-family: 'Merriweather', serif;
}
.showrank-modal .modal-body tr td:nth-child(2) {
  font-size: 14px;
  font-weight: normal;
  color: #444444;
  font-family: 'Rubik', sans-serif;
}
.showrank-modal .modal-body tr td:last-child {
  font-size: 14px;
  font-weight: bold;
  color: #444444;
  font-family: 'Merriweather', serif;
}
.showrank-modal .content-rankWrapper {
  background: #FFFFFF;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  margin-top: 1rem;
}
.showrank-modal .content-rankWrapper .profile {
  width: 92px;
  height: 92px;
  border-radius: 50%;
  border: 4px solid #FFFFFF;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
}
.showrank-modal .content-rankWrapper .profile img {
  width: 84px;
  height: 84px;
  border-radius: 50%;
}
.showrank-modal .content-rankWrapper .user-rank {
  text-align: center;
  font-weight: 500;
  font-family: 'Merriweather', serif;
  font-size: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
}
.showrank-modal .content-rankWrapper .user-rank:before {
  content: url('../../../images/landingpageImages/badge.svg');
  width: 34px;
}
.showrank-modal .content-rankWrapper .yr-head {
  font-size: 8px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.4);
  text-transform: uppercase;
  text-align: center;
  margin: 0;
  margin-top: 8px;
}
.showrank-modal .content-rankWrapper .no-ques {
  font-size: 14px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  text-align: center;
}
.showrank-modal .content-rankWrapper .rank-head {
  font-size: 12px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.4);
  text-transform: uppercase;
  text-align: center;
  margin: 0;
  margin-top: 8px;
}
.showrank-modal .content-rankWrapper .total-students {
  font-size: 10px;
  font-weight: normal;
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.4);
  text-transform: uppercase;
  text-align: center;
}
.bookTemplate {
  height: 100vh;
}
.bookTemplate .shadowHeader {
  position: sticky;
  position: -webkit-sticky;
  background: #ffffff;
  height: 50px;
  width: 100%;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  z-index: 99;
  top: 0;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .shadowHeader {
    top: 48px;
  }
}
.bookTemplate .ChapterHeader .bookTitle {
  padding: 5px;
  font-family: 'Rubik', sans-serif;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.72);
  margin-bottom: 0;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .bookTemplate .ChapterHeader .bookTitle {
    font-size: 10px;
    padding-right: 0;
    padding-top: 0;
  }
}
.bookTemplate .tab-header .nav-tabs .nav-link {
  border: none;
  border-radius: 0;
}
.bookTemplate .tab-header .nav-tabs,
.bookTemplate .tab-header .navbar-nav {
  overflow-y: hidden;
  overflow-x: auto;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .tab-header .nav-tabs {
    width: 100%;
    margin-top: 5px;
  }
  .bookTemplate .tab-header .navbar-nav {
    width: 20%;
    justify-content: space-around;
    border-left: 1px solid rgba(68, 68, 68, 0.1);
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .tab-header .contentEdit {
    width: 0;
    border-left: none !important;
  }
  .bookTemplate .tab-header .contentEdit #notesMenu {
    padding-left: 38px;
  }
}
.bookTemplate .tab-header .contentEdit i {
  color: rgba(68, 68, 68, 0.72);
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .tab-header .contentEdit i {
    font-size: 21px;
  }
}
.bookTemplate .tab-header .contentEdit .dropdown-menu {
  padding-bottom: 0;
}
.bookTemplate .tab-header .contentEdit .dropdown-menu > div {
  padding: 1rem;
}
.bookTemplate .tab-header .contentEdit .dropdown-menu > div:last-child {
  padding: 0;
}
.bookTemplate .tab-header a {
  max-width: 250px;
}
.bookTemplate .tab-header a.book-name {
  color: rgba(68, 68, 68, 0.72);
  font-family: 'Rubik', sans-serif;
  font-size: 12px;
}
.bookTemplate .tab-header .navbar {
  padding: 0.5rem 0;
}
.bookTemplate .tab-header .navbar li.active i {
  color: #1F419B;
}
.bookTemplate .menu {
  border-bottom: none;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .bookTemplate .menu {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto ;
    overflow-y: hidden;
  }
}
.bookTemplate .menu li {
  margin-bottom: 0;
  padding: 0 1rem;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .menu li {
    padding: 0;
  }
}
.bookTemplate .menu li:hover a {
  background: transparent;
  border: transparent;
}
.bookTemplate .menu li a {
  white-space: nowrap;
  color: #444444;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .menu li a {
    font-size: 14px;
  }
}
.bookTemplate .menu.nav-tabs .nav-item.show .nav-link {
  color: #1F419B;
  background: transparent;
  border: none;
  border-bottom: 2px solid #1F419B;
}
.bookTemplate .menu.nav-tabs .nav-link.active {
  color: #1F419B;
  background: transparent;
  border: none;
  border-bottom: 5px solid #1F419B;
}
.bookTemplate .content-wrapper {
  position: relative;
}
.bookTemplate .content-wrapper .price-wrapper {
  position: sticky;
  position: -webkit-sticky;
  bottom: 0;
  z-index: 9;
  padding: 0.5rem 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : landscape) {
  .bookTemplate .content-wrapper .price-wrapper {
    width: 100%;
  }
}
.bookTemplate .content-wrapper .price-wrapper .section-btns {
  width: 100%;
  padding: 0.5rem 0rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
}
.bookTemplate .content-wrapper .price-wrapper .section-btns a {
  color: #444444;
}
.bookTemplate .content-wrapper .price-wrapper .section-btns a:hover {
  text-decoration: none;
  color: #1F419B;
}
.bookTemplate .content-wrapper .price-wrapper .section-btns a .prev_chap,
.bookTemplate .content-wrapper .price-wrapper .section-btns a .next_chap {
  position: relative;
  bottom: -7px;
}
.bookTemplate .content-wrapper .chapterSection {
  position: fixed;
  z-index: 9;
  top: 50px;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .bookTemplate .content-wrapper .chapterSection {
    width: 250px;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .bookTemplate .content-wrapper .chapterSection {
    width: 300px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .content-wrapper .chapterSection {
    position: fixed;
    z-index: 99;
    top: 0;
  }
}
.bookTemplate .content-wrapper .chapterSection a.slide-toggle {
  position: fixed;
  height: 30px;
  width: 85px;
  top: 170px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6F6F6F;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  z-index: 9;
}
.bookTemplate .content-wrapper .chapterSection a.slide-toggle.left {
  left: -27px;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .bookTemplate .content-wrapper .chapterSection a.slide-toggle {
    left: 28%;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .content-wrapper .chapterSection a.slide-toggle {
    left: 56%;
  }
}
.bookTemplate .content-wrapper #book-sidebar {
  width: 100%;
  background: #f5f5f5;
  height: 100vh;
  position: relative;
  padding-bottom: 2rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .content-wrapper #book-sidebar {
    width: 100%;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .bookTemplate .content-wrapper #book-sidebar {
    width: 250px;
  }
}
.bookTemplate .content-wrapper #book-sidebar.closed .side-content {
  display: none;
}
.bookTemplate .content-wrapper #book-sidebar .side-content {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
.bookTemplate .content-wrapper #book-sidebar .side-content .backtolibrary {
  color: #444444;
  font-size: 12px;
  display: flex;
  align-items: center;
}
.bookTemplate .content-wrapper #book-sidebar .side-content .backtolibrary i {
  font-size: 12px;
  color: #444444;
}
.bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  color: rgba(68, 68, 68, 0.4);
  font-family: 'Rubik', sans-serif;
  font-size: 16px;
  font-weight: normal;
  padding: 1rem;
  border-bottom: 0.5px solid rgba(68, 68, 68, 0.2);
  background: #f5f5f5;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  margin-top: 60px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
    margin-top: 0;
    display: none;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .bookTemplate .content-wrapper #book-sidebar .side-content ol {
    margin: 0 !important;
    padding: 0 !important;
  }
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li {
  padding: 8px 4px;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li.chapter-name {
  color: rgba(68, 68, 68, 0.4);
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  word-break: break-all;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li.chapter-name.orangeText {
  color: #1F419B;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li.chapter-name.orangeText a {
  color: #1F419B;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .bookTemplate .content-wrapper #book-sidebar .side-content ol > li.chapter-name {
    border-bottom: 1px solid rgba(68, 68, 68, 0.1);
    margin: 0.5rem 2rem;
  }
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li.chapter-name i {
  font-size: 20px;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li a {
  color: #444444;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  word-break: break-word;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li a:hover {
  text-decoration: none;
  color: #1F419B;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li ul {
  padding: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .bookTemplate .content-wrapper #book-sidebar .side-content ol > li ul {
    padding: 7px;
  }
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li ul li {
  color: #444444;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  list-style-type: disc;
  padding: 12px 0;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li ul li a {
  color: #444444;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
}
.bookTemplate .content-wrapper #book-sidebar .side-content ol > li ul li a.orangeText {
  color: #1F419B;
}
.bookTemplate .content-wrapper #book-sidebar .mobile-title {
  color: rgba(68, 68, 68, 0.4);
  font-family: 'Rubik', sans-serif;
  font-size: 16px;
  font-weight: normal;
  padding: 1rem;
  border-bottom: 0.5px solid rgba(68, 68, 68, 0.2);
  background: #f5f5f5;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  margin-top: 60px;
}
.bookTemplate .content-wrapper #book-sidebar .mobile-title p {
  margin-bottom: 0;
  color: #444444;
}
.bookTemplate .export-notes {
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  width: 400px;
  background: #ffffff;
  right: 0;
  top: 110px;
  z-index: 99;
  padding-bottom: 2rem;
}
.bookTemplate .export-notes .no-notes-created {
  text-align: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .export-notes {
    width: 250px;
  }
}
.bookTemplate .export-notes .close-notes {
  position: fixed;
  right: calc(400px - 1rem);
  background: #fff;
  width: 35px;
  height: 35px;
  border-radius: 50px;
  border: none;
  outline: 0;
  z-index: 9;
  margin-top: 3rem;
  cursor: pointer;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .bookTemplate .export-notes .close-notes {
    right: calc(250px - 15px);
  }
}
.bookTemplate .export-notes .close-notes i {
  font-size: 18px;
  line-height: 1.5;
}
.bookTemplate .export-notes .export-study-set {
  color: #1F419B;
  font-weight: 500;
  font-size: 14px;
}
.bookTemplate .export-notes .notes-creation-header-title {
  color: rgba(68, 68, 68, 0.72);
  font-weight: 500;
  font-size: 20px;
}
#overlay {
  position: fixed;
  /* Sit on top of the page content */
  /* Hidden by default */
  width: 100%;
  /* Full width (cover the whole page) */
  height: 100%;
  /* Full height (cover the whole page) */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  /* Black background with opacity */
  z-index: 99;
  /* Specify a stack order in case you're using a different order for other elements */
  cursor: pointer;
  /* Add a pointer on hover */
}
#searchVideos h3 {
  text-align: center;
  margin-bottom: 4rem;
  font-family: 'Rubik', sans-serif;
  font-size: 16px;
}
#searchVideos .test-pr {
  width: 75%;
  margin: 2rem auto;
}
#searchVideos .overlay-pr {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background: rgba(68, 68, 68, 0.5);
}
#searchVideos .overlay-pr .play-btn-wrapper img {
  width: 40px;
  height: 40px;
}
#searchVideos .video-wrapper {
  display: flex;
  flex-wrap: wrap;
}
#searchVideos .video-wrapper .video-img-wrapper {
  position: relative;
}
#searchVideos .video-wrapper a:hover {
  text-decoration: none;
}
#searchVideos .video-wrapper a span {
  color: #444444;
}
#searchVideos .play-btn-wrapper {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: center;
}
#searchVideos .play-btn-wrapper img {
  width: 26px;
  height: 25px;
}
#searchVideos .play-btn-wrapper span {
  margin-left: 0.5rem;
}
#searchVideos .videoTOadd {
  display: flex;
  align-items: center;
  color: #444444;
}
#searchVideos .videoTOadd img {
  width: 24px;
  height: 16px;
}
#searchVideos .pr-flex {
  display: flex;
  justify-content: space-between;
}
#searchVideos .pr-flex .dpr-none {
  display: none;
}
#searchVideos .pr-flex .dpr-none img {
  width: 24px;
  height: 16px;
  margin-right: 0.3rem;
}
.preview-book-btns .card-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  bottom: 0;
  background: #ffffff;
  box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24);
  z-index: 99;
  padding: 0 1rem;
}
.preview-book-btns .card-wrapper .complte-book h4 {
  font-size: 10px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.72);
}
.preview-book-btns .card-wrapper .complte-book p {
  color: #B72319;
  font-size: 18px;
  font-family: 'Merriweather', serif;
  margin: 0;
}
.video-url .modal-header h4 {
  font-size: 18px;
}
.video-url #videoForm {
  text-align: center;
}
.video-url #videoForm input {
  margin: 0.5rem auto;
  padding: 0.3rem;
}
#flashCardModal .modal-header {
  display: block;
}
#flashCardModal .modal-header .close {
  padding: 0;
  margin: 0;
}
#flashCardModal .carousel-indicators {
  display: none;
}
#flashCardModal .flash-card-slider {
  position: relative;
  max-width: 440px;
  margin: 0 auto;
}
#flashCardModal .flash-card-slider .carousel-inner {
  max-width: 440px;
  margin: 40px auto 0;
}
#flashCardModal .flash-card-slider .item {
  min-height: 316px;
  padding: 24px;
}
#flashCardModal .flash-card-slider .carousel-control {
  top: auto;
  background: #1F419B;
  opacity: 1;
  width: 24px;
  height: 24px;
  border-radius: 100%;
  color: #ffffff;
}
#flashCardModal .flash-card-slider .carousel-control:focus {
  background: #1F419B !important;
}
#flashCardModal .flash-card-slider .carousel-btn-disabled {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.65;
}
#flashCardModal .flash-card-slider .carousel-control-wrapper {
  width: 208px;
  margin: 0 auto;
  float: none;
  display: flex;
  justify-content: space-around;
}
#flashCardModal .flash-card-slider .card-number {
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  color: rgba(68, 68, 68, 0.74);
}
#flashCardModal .flip-container {
  background: #FFFFFF;
  -webkit-perspective: 1000;
  -moz-perspective: 1000;
  -o-perspective: 1000;
  perspective: 1000;
}
#flashCardModal .flip .flipper {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
#flashCardModal .flip-container,
#flashCardModal .front,
#flashCardModal .back {
  width: 400px;
  min-height: 216px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #flashCardModal .flip-container,
  #flashCardModal .front,
  #flashCardModal .back {
    width: 100%;
  }
}
#flashCardModal .flipper {
  min-height: 216px;
  background: transparent;
  box-sizing: border-box;
  border-radius: 4px;
  -webkit-transition: 0.6s;
  -webkit-transform-style: preserve-3d;
  -moz-transition: 0.6s;
  -moz-transform-style: preserve-3d;
  -o-transition: 0.6s;
  -o-transform-style: preserve-3d;
  transition: 0.6s;
  transform-style: preserve-3d;
  position: relative;
}
#flashCardModal .front,
#flashCardModal .back {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -o-backface-visibility: hidden;
  backface-visibility: hidden;
  top: 0;
  left: 0;
  position: relative;
  text-align: center;
  border: 0.5px solid rgba(189, 189, 189, 0.55);
  border-radius: 4px;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  height: 216px;
  overflow: hidden;
}
#flashCardModal .front {
  position: absolute;
  z-index: 2;
}
#flashCardModal .front span {
  display: flex;
}
#flashCardModal .back {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
  position: absolute;
}
#flashCardModal .front .name {
  font-size: 16px;
  text-shadow: none;
  color: #000;
  padding: 0 16px;
  display: flex;
  align-items: center;
  height: 100%;
  overflow: auto;
  justify-content: center;
}
#flashCardModal .back-logo {
  position: absolute;
  top: 40px;
  left: 90px;
  width: 160px;
  height: 117px;
}
#flashCardModal .back-title {
  color: #000;
  text-align: center;
  font-size: 16px;
  padding: 0 16px;
  display: flex;
  justify-content: center;
  height: 100%;
  overflow: auto;
  padding-top: 40px;
  padding-bottom: 20px;
}
#flashCardModal .flip-card-btn {
  width: 112px;
  position: absolute;
  left: 0;
  right: 0;
  display: block;
  margin: 0 auto;
  font-size: 16px;
  bottom: -16px;
  text-align: center;
  color: #FFFFFF;
  background: #1F419B;
  padding: 8px 0;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 63px;
  z-index: 5;
}
#flashCardModal .flip-card-btn:focus {
  background: #1F419B !important;
  color: #ffffff;
}
#flashCardModal .flip-card-btn:hover {
  background: #1F419B;
  color: #FFFFFF;
  text-decoration: none;
}
#flashCardModal .flip-card-btn:active {
  background: #1F419B;
  color: #FFFFFF;
  text-decoration: none;
}
#flashCardModal .flip-card-btn:focus {
  background: #1F419B;
  color: #FFFFFF;
  text-decoration: none;
}
#flashCardModal .flip-card-btn > i {
  font-size: 16px;
  vertical-align: top;
}
#flashCardModal .btn-flip {
  transform: rotateY(180deg);
}
#flashCardModal .back p {
  position: absolute;
  bottom: 40px;
  left: 0;
  right: 0;
  text-align: center;
  padding: 0 20px;
  font-family: arial;
  line-height: 2em;
}
#flashCardModal .flash-card-slider {
  position: relative;
  max-width: 440px;
  margin: 0 auto;
}
#flashCardModal .flash-card-slider .carousel-inner {
  max-width: 440px;
  margin: 40px auto 0;
}
#flashCardModal .flash-card-slider .item {
  min-height: 316px;
  padding: 24px;
}
#flashCardModal .flash-card-slider .carousel-control {
  top: auto;
  background: #1F419B;
  opacity: 1;
  width: 24px;
  height: 24px;
  border-radius: 100%;
}
#flashCardModal .flash-card-slider .carousel-btn-disabled {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.65;
}
#flashCardModal .flash-card-slider .carousel-control-wrapper {
  width: 208px;
  margin: 0 auto;
  float: none;
}
#flashCardModal .flash-card-slider .card-number {
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  color: rgba(68, 68, 68, 0.74);
}
.comment-by-user {
  width: 100%;
  font-family: Montserrat;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.84);
  background-color: #FFFFFF;
  border: 0.5px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  padding: 16px;
  margin: 0;
  margin-top: 8px;
}
.created-note-by-user {
  background-color: rgba(251, 243, 173, 0.74);
}
.notes-created-by-user {
  display: inline-block;
  width: 95%;
  padding-bottom: 16px;
  margin-left: 24px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.24);
}
.notes-created-by-user p {
  margin-bottom: 0;
}
.highlight-by-user {
  background-color: rgba(33, 150, 83, 0.24);
  font-family: inherit;
}
.app_in_app .notes-creation-header {
  z-index: 99 !important;
}
.notes-creation-header {
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
  background: #ffffff;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  padding: 1rem 0;
}
.notes-creation-header p {
  margin-bottom: 0;
}
.notes-list-wrapper {
  padding: 0;
}
.notes-list-wrapper .export-btn-wrapper {
  display: flex;
  position: fixed;
  bottom: 0;
  width: 400px;
  text-align: center;
  background: #ffffff;
  justify-content: center;
}
.notes-list-wrapper .export-btn-wrapper a {
  color: #ffffff;
  display: block;
  padding: 0.5rem;
  background: #30C465;
  border-radius: 4px;
  width: 200px;
}
.notes-list-wrapper .export-btn-wrapper a:hover {
  text-decoration: none;
}
.notes-list-wrapper .export-btn-wrapper a:focus {
  background: #30C465;
}
.notes-list-wrapper .notes-list-item {
  display: flex;
  align-items: center;
  padding: 1rem;
}
.score-btn {
  color: rgba(68, 68, 68, 0.7);
}
.score-btn:hover {
  color: #1F419B;
}
.quiz-practice-btn {
  margin-bottom: 0 !important;
}
.bgScore {
  background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  color: #ffffff;
}
.bgScore .exercise-scores {
  border: 1px solid;
  border-radius: 4px;
  min-height: 75px;
  min-width: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.quiz-modal-body {
  overflow-y: scroll;
}
#analyticsid .bt-quiz {
  border-bottom: 1px solid;
  margin-bottom: 2rem;
  align-items: center;
}
.total-question-num {
  margin-bottom: 0;
}
#htmlContent img {
  max-width: 100%;
}
#htmlContent table {
  width: 100% !important;
  max-width: 100%;
}
.textEffects {
  position: absolute;
  right: 5rem;
  background: #fff;
  width: 32px;
  height: 42px;
  text-align: center;
  border-left: 1px solid #ededed;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .textEffects {
    right: 50px;
    width: 38px;
    height: 40px;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .textEffects {
    right: 50px;
    top: 8px;
  }
}
.textEffects .dropdown-menu {
  position: absolute;
  top: 3rem;
  left: -10rem;
}
.color-modes-wrapper .color-mode-list-item {
  width: 65px;
  height: 62px;
}
.color-modes-wrapper .color-mode-list-item.white {
  background: #F2F2F2;
}
.color-modes-wrapper .color-mode-list-item.sepia {
  background: #D4C79F;
}
.color-modes-wrapper .color-mode-list-item.grey {
  background: #5A5A5C;
}
.color-modes-wrapper .color-mode-list-item.black {
  background: #000000;
}
.color-modes-wrapper .color-mode-list-item.active {
  border-bottom: 2px solid #1F419B;
}
.study-set-wrapper-continer {
  margin-top: 40px;
  margin-bottom: 40px;
  counter-reset: study-set-counter;
}
.study-set-main {
  background-color: #f8f8f8;
  padding: 0;
  margin-bottom: 32px;
  counter-increment: study-set-counter;
  position: relative;
  display: flex;
}
.study-set-main:hover .term-counter:before {
  content: url("../../images/landingpageImages/delete.svg");
  color: #F05A2A;
  opacity: 1;
  position: absolute;
  text-align: center;
  bottom: auto;
  left: 2rem;
  right: auto;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.2;
  cursor: pointer;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .study-set-main:hover .term-counter:before {
    left: 0;
  }
}
.term-counter:before {
  position: absolute;
  content: counter(study-set-counter);
  text-align: center;
  bottom: auto;
  left: 2rem;
  right: auto;
  font-weight: 700;
  font-size: 16px;
  line-height: 1.2;
  width: 3.375rem;
  opacity: 0.5;
  cursor: pointer;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .term-counter:before {
    left: 0;
  }
}
.question-row-bordered-right {
  border-bottom: 2px solid #30C465;
}
.question-row-bordered-wrong {
  border-bottom: 2px solid #B72319;
}
.answer-summary .score-summary .correct-answers,
.answer-summary .score-summary .wrong-answers,
.answer-summary .score-summary .skipped-answers {
  width: 100px;
  height: 100px;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(43.98deg, #2A7E0D 14.96%, #4DEB17 100%);
  padding: 34px 35px 36px 34px;
  margin: 0 auto;
  margin-top: 8px;
  border-radius: 16px;
}
.answer-summary .score-summary .wrong-answers {
  background: linear-gradient(45deg, #97160D 20.42%, #F76E64 100%);
}
.answer-summary .score-summary .skipped-answers {
  color: #444444;
  background: #fff;
  border: 2px solid #444444;
}
.score-container {
  overflow: auto;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .score-container {
    min-height: 100%;
    max-height: 100%;
    height: 400px;
  }
}
.suggestions-for-user {
  padding: 1rem;
}
.row-heading {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}
.clearfix {
  display: block;
}
.mcq-question-div {
  margin: 0 auto;
}
.skipped-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #000;
  background-color: #fff;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid #000;
  border-radius: 8px;
}
.grey-bg {
  background: #5A5A5C;
}
.sepia-bg {
  background: #D4C79F;
}
.black-bg {
  background: #000000;
}
.black-bg a,
.grey-bg a,
.black-bg p,
.grey-bg p,
.black-bg h1,
.grey-bg h1,
.black-bg h2,
.grey-bg h2,
.black-bg h3,
.grey-bg h3,
.black-bg h4,
.grey-bg h4,
.black-bg h5,
.grey-bg h5,
.black-bg h6,
.grey-bg h6,
.black-bg span,
.grey-bg span,
.black-bg i,
.grey-bg i {
  color: #FFFFFF !important;
}
.black-bg .notes-creation-header p.notes-creation-header-title,
.grey-bg .notes-creation-header p.notes-creation-header-title {
  color: rgba(68, 68, 68, 0.72) !important;
}
.black-bg .notes-creation-header a.export-study-set,
.grey-bg .notes-creation-header a.export-study-set {
  color: #1F419B !important;
}
.black-bg .export-notes p,
.grey-bg .export-notes p {
  color: #212529 !important;
}
.black-bg .export-notes .close-notes i,
.grey-bg .export-notes .close-notes i {
  color: #212529 !important;
}
.black-bg .pr-back-btn,
.grey-bg .pr-back-btn {
  color: #444444 !important;
}
.black-bg .pr-back-btn i,
.grey-bg .pr-back-btn i {
  color: #444444 !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .black-bg .pr-back-btn,
  .grey-bg .pr-back-btn {
    color: #ffffff !important;
  }
  .black-bg .pr-back-btn i,
  .grey-bg .pr-back-btn i {
    color: #ffffff !important;
  }
}
#formatMenu a {
  cursor: pointer;
}
#htmlreadingcontent {
  margin-top: 2rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #htmlreadingcontent table {
    width: 100% !important;
  }
}
.video-url .modal-body form,
.web-url .modal-body form,
.upload-url .modal-body form {
  margin-top: 2rem;
}
.video-url .modal-body input:first-child,
.web-url .modal-body input:first-child,
.upload-url .modal-body input:first-child {
  margin-bottom: 2rem;
}
.video-url .modal-body input,
.web-url .modal-body input,
.upload-url .modal-body input {
  border: none;
  border-bottom: 1px solid rgba(68, 68, 68, 0.72);
  width: 280px;
  outline: 0;
  padding: 10px;
}
.upload-url .modal-body input {
  margin: 0 auto;
  border-radius: 0;
}
.video-url .modal-footer button:last-child,
.web-url .modal-footer button:last-child,
.upload-url .modal-footer button:last-child {
  color: #F79420;
}
.video-url .modal-footer button,
.web-url .modal-footer button,
.upload-url .modal-footer button {
  border: none;
}
#prev-section,
#next-section {
  display: none;
}
#jpedal {
  width: 100% !important;
}
#htmlreadingcontent {
  padding: 0 !important;
}
#htmlreadingcontent #jpedal {
  overflow: unset !important;
}
#htmlContent {
  margin-top: 0rem;
}
.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 0;
  transition: all 0.3s;
}
.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content .backtolibrary {
  color: #ffffff;
}
.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content .backtolibrary i {
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .hasScrolled .bookTemplate .content-wrapper #book-sidebar .mobile-title {
    margin-top: 0;
    transition: all 0.3s;
    background: #1F419B;
  }
  .hasScrolled .bookTemplate .content-wrapper #book-sidebar .mobile-title p {
    color: #ffffff;
  }
}
.hasScrolled .bookTemplate .export-notes {
  top: 50px;
  transition: all 0.1s;
}
.hasScrolled .mobChapname {
  background: #1F419B;
}
.hasScrolled .mobChapname p {
  color: #ffffff;
}
.hasScrolled .mobChapname #mobChapname {
  color: #ffffff;
  margin-bottom: 0;
}
.hasScrolled .mobChapname i {
  color: #ffffff;
  font-size: 12px;
}
.hasScrolled .mobChapname span {
  font-size: 12px;
  color: #ffffff;
  white-space: nowrap;
}
.all-container #android-menu:after {
  display: none;
}
.all-container #android-menu i {
  top: 9px;
  right: 5px;
  position: relative;
}
.all-container .container-wrapper {
  margin: 0 auto;
  border: 1px solid #ededed;
  border-radius: 4px;
  margin-top: 2rem;
  width: 600px;
  min-height: 100px;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .all-container .container-wrapper {
    margin-top: 0;
    border: none;
    border-bottom: 1px solid #ededed;
    box-shadow: none;
    border-radius: 0;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .all-container .container-wrapper .dropdown-menu {
    left: -4rem !important;
  }
}
.all-container .container-wrapper .d-flex p {
  margin: 0.5rem 1rem;
  color: rgba(68, 68, 68, 0.6);
  position: relative;
  top: 5px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .all-container .container-wrapper {
    width: 100%;
  }
}
.all-container .container-wrapper .media {
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
}
.all-container .container-wrapper .media i {
  width: 50px;
  height: 50px;
  background: darkred;
  font-size: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 2rem;
  color: #fff;
  border-radius: 50px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .all-container .container-wrapper .media i {
    margin: 0 1rem;
  }
}
.all-container .container-wrapper .media i.yellow {
  background: #f1c40f;
}
.all-container .container-wrapper .media i.blue {
  background: #2980b9;
}
.all-container .container-wrapper .media i.green {
  background: #27ae60;
}
.all-container .container-wrapper .media i.violet {
  background: #9b59b6;
}
.all-container .container-wrapper .media i.yt {
  background: #e74c3c;
}
.all-container .container-wrapper .media i.yt:before {
  margin-left: 0 !important;
}
.all-container .container-wrapper .media .date {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.4);
  display: block;
  padding-bottom: 10px;
}
.all-container .container-wrapper .media .title {
  font-weight: normal;
}
.all-container .container-wrapper .media .readnow {
  text-transform: uppercase;
  color: #1F419B;
}
.all-container .container-wrapper .logo {
  width: 18px;
  height: 18px;
  z-index: 9 !important;
}
.all-container a.listen-btn {
  padding-left: 10px;
  margin-left: 10px;
  border-left: 1px solid #000;
}
.all-container .adjusted-logo {
  margin-top: -18px;
}
.mobChapname {
  color: rgba(68, 68, 68, 0.4);
  font-family: 'Rubik', sans-serif;
  font-size: 16px;
  font-weight: normal;
  padding: 1rem;
  background: #ffffff;
  position: sticky;
  position: -webkit-sticky;
  top: 0;
  width: 100%;
  z-index: 99;
  border-bottom: 1px solid rgba(68, 68, 68, 0.1);
}
.mobChapname #mobChapname {
  margin-bottom: 0;
  color: #444444;
}
.mobChapname i {
  color: #444444;
  font-size: 12px;
}
.mobChapname span {
  font-size: 12px;
  color: #444444;
  white-space: nowrap;
}
#study-set-wrapper-container .pr-back-btn {
  z-index: 1;
}
.bookShadow {
  height: 100%;
  border-radius: 0 3px 3px 0;
  box-shadow: inset 8px 0 10px rgba(0, 0, 0, 0.1);
  position: relative;
}
.bookShadow::after {
  content: '';
  position: absolute;
  top: 0;
  left: 5px;
  bottom: 0;
  width: 2px;
  background: rgba(0, 0, 0, 0.1);
  box-shadow: 1px 0 3px rgba(255, 255, 255, 0.3);
}
.bookShadow > img {
  border-radius: 0 3px 3px 0;
  box-shadow: inset 4px 0 10px rgba(0, 0, 0, 0.1);
  position: relative;
}
#PlayAudiOnlyModal #LoadingAudio {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #FFF;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  z-index: 10;
}
#PlayAudiOnlyModal .LoadingImage {
  border: 7px solid #f3f3f3;
  border-radius: 50%;
  border-top: 7px solid #1F419B;
  width: 50px;
  height: 50px;
  margin: 15px auto;
  -webkit-animation: spin 1s linear infinite;
  /* Safari */
  animation: spin 1s linear infinite;
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.prev_chap {
  transform: rotate(-180deg);
  -webkit-transform: rotate(-180deg);
  -moz-transform: rotate(-180deg);
}
#all h3 {
  text-align: center;
  margin-bottom: 4rem;
  font-family: 'Rubik', sans-serif;
  font-size: 16px;
}
#all .test-pr {
  width: 75%;
  margin: 2rem auto;
}
#all .overlay-pr {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  background: rgba(68, 68, 68, 0.5);
}
#all .overlay-pr .play-btn-wrapper img {
  width: 40px;
  height: 40px;
}
#all .video-wrapper {
  display: flex;
  flex-wrap: wrap;
}
#all .video-wrapper .video-img-wrapper {
  position: relative;
}
#all .video-wrapper a:hover {
  text-decoration: none;
}
#all .video-wrapper a span {
  color: #444444;
}
#all .play-btn-wrapper {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: center;
}
#all .play-btn-wrapper img {
  width: 26px;
  height: 25px;
}
#all .play-btn-wrapper span {
  margin-left: 0.5rem;
}
#all .videoTOadd {
  display: flex;
  align-items: center;
  color: #444444;
}
#all .videoTOadd img {
  width: 24px;
  height: 16px;
}
#all .pr-flex {
  display: flex;
  justify-content: space-between;
}
#all .pr-flex .dpr-none {
  display: none;
}
#all .pr-flex .dpr-none img {
  width: 24px;
  height: 16px;
  margin-right: 0.3rem;
}
#book-read-material {
  padding-top: 2rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #book-read-material {
    padding: 2rem 0 0;
  }
  #book-read-material #content-data-all {
    padding: 0;
  }
  #book-read-material #content-data-all > .container {
    padding: 0;
  }
}
#book-read-material .quiz-item-wrapper {
  background: #ffffff;
  border: 1px solid #EDEDED;
  box-sizing: border-box;
  border-radius: 4px;
  margin: 1rem;
  width: 192px;
  padding: 1rem;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
}
#book-read-material .quiz-item-wrapper .dropdown a:after {
  display: none;
}
#book-read-material .quiz-item-wrapper .quiz-buttons .quiz-learn-btn,
#book-read-material .quiz-item-wrapper .quiz-buttons .quiz-practice-btn {
  width: 100%;
  display: flex;
  height: 35px;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  border: 1px solid #EDEDED;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
}
#book-read-material .quiz-item-wrapper .quiz-buttons .quiz-learn-btn {
  color: #1F419B;
}
#book-read-material .quiz-item-wrapper .quiz-buttons .quiz-practice-btn {
  color: #ffffff;
  background: #2EBAC6;
}
#book-read-material .quiz-item-wrapper .quiz-buttons .quiz-practice-btn:focus {
  background: #2EBAC6 !important;
}
#book-read-material .quiz-item-wrapper .quiz-item {
  min-height: 70px;
}
#book-read-material .quiz-item-wrapper .quiz-item p {
  text-align: center;
}
#book-read-material #content-data-no-notes {
  min-height: calc(100vh - 115px);
  display: flex;
  align-items: center;
}
.btn-theme {
  min-width: 260px;
  color: #1F419B;
  text-transform: uppercase;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  border: 1px solid rgba(68, 68, 68, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.btn-secondaries {
  min-width: 260px;
  color: #2F80ED !important;
  text-transform: uppercase;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  border: 1px solid rgba(68, 68, 68, 0.1);
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.hasScrolled #addRefButton .dropdown,
.hasScrolled #videoAddButton .dropdown,
.hasScrolled #content-data-userNotes .dropdown,
.hasScrolled #allAddButton .dropdown {
  top: 60px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .hasScrolled #addRefButton .dropdown,
  .hasScrolled #videoAddButton .dropdown,
  .hasScrolled #content-data-userNotes .dropdown,
  .hasScrolled #allAddButton .dropdown {
    top: 120px;
  }
}
.hasScrolled .add-notesPr {
  top: 60px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .hasScrolled .add-notesPr {
    top: 120px !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .add-notesPr {
    top: 180px !important;
  }
  .add-notesPr a span {
    display: none;
  }
  .add-notesPr > a {
    height: 50px !important;
    width: 50px !important;
  }
}
#addRefButton,
#videoAddButton,
#content-data-userNotes,
#allAddButton,
#addnotes {
  width: 100%;
}
#addRefButton .dropdown,
#videoAddButton .dropdown,
#content-data-userNotes .dropdown,
#allAddButton .dropdown,
#addnotes .dropdown {
  position: fixed;
  z-index: 19;
  right: 0;
  top: 120px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #addRefButton .dropdown,
  #videoAddButton .dropdown,
  #content-data-userNotes .dropdown,
  #allAddButton .dropdown,
  #addnotes .dropdown {
    top: 180px;
  }
}
#addRefButton .dropdown button,
#videoAddButton .dropdown button,
#content-data-userNotes .dropdown button,
#allAddButton .dropdown button,
#addnotes .dropdown button {
  height: 44px;
  background: #1F419B;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 600px;
  color: #ffffff;
  width: 160px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #addRefButton .dropdown button,
  #videoAddButton .dropdown button,
  #content-data-userNotes .dropdown button,
  #allAddButton .dropdown button,
  #addnotes .dropdown button {
    width: 42px;
    height: 42px;
    border-radius: 50px;
    top: 172px;
  }
  #addRefButton .dropdown button span,
  #videoAddButton .dropdown button span,
  #content-data-userNotes .dropdown button span,
  #allAddButton .dropdown button span,
  #addnotes .dropdown button span {
    display: none;
  }
  #addRefButton .dropdown button:after,
  #videoAddButton .dropdown button:after,
  #content-data-userNotes .dropdown button:after,
  #allAddButton .dropdown button:after,
  #addnotes .dropdown button:after {
    display: none;
  }
}
#addRefButton .dropdown button i,
#videoAddButton .dropdown button i,
#content-data-userNotes .dropdown button i,
#allAddButton .dropdown button i,
#addnotes .dropdown button i {
  font-size: 20px;
}
#addRefButton .dropdown .dropdown-menu,
#videoAddButton .dropdown .dropdown-menu,
#content-data-userNotes .dropdown .dropdown-menu,
#allAddButton .dropdown .dropdown-menu,
#addnotes .dropdown .dropdown-menu {
  background: transparent;
  border: none;
}
#addRefButton .dropdown .dropdown-menu.show,
#videoAddButton .dropdown .dropdown-menu.show,
#content-data-userNotes .dropdown .dropdown-menu.show,
#allAddButton .dropdown .dropdown-menu.show,
#addnotes .dropdown .dropdown-menu.show {
  right: 0;
}
#addRefButton .dropdown .dropdown-menu a,
#videoAddButton .dropdown .dropdown-menu a,
#content-data-userNotes .dropdown .dropdown-menu a,
#allAddButton .dropdown .dropdown-menu a,
#addnotes .dropdown .dropdown-menu a {
  display: flex;
  align-items: center;
  color: #ffffff;
  justify-content: flex-end;
}
#addRefButton .dropdown .dropdown-menu a:hover,
#videoAddButton .dropdown .dropdown-menu a:hover,
#content-data-userNotes .dropdown .dropdown-menu a:hover,
#allAddButton .dropdown .dropdown-menu a:hover,
#addnotes .dropdown .dropdown-menu a:hover {
  background: transparent;
}
#addRefButton .dropdown .dropdown-menu a i,
#videoAddButton .dropdown .dropdown-menu a i,
#content-data-userNotes .dropdown .dropdown-menu a i,
#allAddButton .dropdown .dropdown-menu a i,
#addnotes .dropdown .dropdown-menu a i {
  font-size: 20px;
  float: right;
  background: #2F80ED;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.24), 0px 0px 6px rgba(0, 0, 0, 0.24);
  border-radius: 50px;
  width: 32px;
  height: 32px;
  line-height: 1.4;
  display: block;
  text-align: center;
  margin-left: 1rem;
}
#addRefButton .dropdown .dropdown-menu a span,
#videoAddButton .dropdown .dropdown-menu a span,
#content-data-userNotes .dropdown .dropdown-menu a span,
#allAddButton .dropdown .dropdown-menu a span,
#addnotes .dropdown .dropdown-menu a span {
  background: rgba(68, 68, 68, 0.72);
  border-radius: 4px;
  padding: 0.5rem;
}
.additional-ref-wrapper {
  width: 600px;
  margin: 0 auto;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .additional-ref-wrapper {
    width: 300px;
  }
}
.additional-ref-wrapper:last-child {
  padding-bottom: 4rem;
}
.additional-ref-wrapper a {
  text-decoration: none;
}
.additional-ref-wrapper a:hover .additional-ref-item {
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
}
.additional-ref-item {
  display: flex;
  align-items: center;
  margin: 0 auto;
  border: 1px solid #ededed;
  border-radius: 4px;
  margin-top: 2rem;
}
.additional-ref-info {
  width: 100%;
  padding: 1rem;
}
.additional-ref-info p {
  color: #444444;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  font-size: 'Rubik', sans-serif;
}
.chapternav-wrapper {
  text-align: center;
  margin-top: 3rem;
}
.chapternav-wrapper .chapternav-items {
  display: flex;
  list-style-type: none;
  justify-content: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .chapternav-wrapper .chapternav-items {
    padding: 0;
  }
}
.chapternav-wrapper .chapternav-items .chapternav-item {
  display: inline-block;
  vertical-align: top;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  width: 100px;
  margin: 1rem;
  padding-bottom: 1rem;
}
.chapternav-wrapper .chapternav-items .chapternav-item .chapternav-icon {
  margin-top: 1rem;
}
.chapternav-wrapper .chapternav-items .chapternav-item i {
  color: rgba(68, 68, 68, 0.5);
}
.chapternav-wrapper .chapternav-items .chapternav-item .chapternav-label {
  display: block;
}
.chapternav-wrapper .chapternav-items .chapternav-item a {
  color: #444444;
  text-decoration: none;
}
.study-set-wrapper,
#study-set-from-notes {
  text-align: center;
}
.study-set-wrapper .study-set-item .form-group,
#study-set-from-notes .study-set-item .form-group {
  background: #ffffff;
}
.study-set-wrapper .study-set-item textarea,
#study-set-from-notes .study-set-item textarea {
  border: none;
  border-bottom: 1px solid rgba(68, 68, 68, 0.5);
  outline: 0;
  width: 300px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .study-set-wrapper .study-set-item textarea,
  #study-set-from-notes .study-set-item textarea {
    width: 100%;
  }
}
.study-set-wrapper .add-study-card-btn,
#study-set-from-notes .add-study-card-btn {
  background: #FFFFFF;
  border: 1px solid #EDEDED;
  box-sizing: border-box;
  border-radius: 4px;
  padding: 0.5rem 2rem;
  color: #1F419B;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
  text-decoration: none;
}
.study-set-wrapper .add-study-card-btn-wrapper,
#study-set-from-notes .add-study-card-btn-wrapper {
  margin-top: 2rem;
}
.study-set-wrapper .add-study-card-btn-wrapper a,
#study-set-from-notes .add-study-card-btn-wrapper a {
  text-decoration: none;
  margin-right: 1rem;
}
.study-set-wrapper .input-studyset,
#study-set-from-notes .input-studyset {
  overflow: hidden;
  position: relative;
  z-index: 1;
  display: inline-block;
  margin: 1rem 0;
  width: 300px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .study-set-wrapper .input-studyset,
  #study-set-from-notes .input-studyset {
    width: 210px;
  }
}
.study-set-wrapper .input-studyset.termDimen,
#study-set-from-notes .input-studyset.termDimen {
  width: 165px;
}
.study-set-wrapper .input-studyset textarea,
#study-set-from-notes .input-studyset textarea {
  resize: none;
  border: none;
  outline: 0;
  margin-top: 1rem;
  width: 100%;
  background: transparent;
  color: #595F6E;
  padding: 5px 5px;
  height: 40px;
  padding-bottom: 0;
}
.study-set-wrapper .input-studyset textarea:focus + label::after,
#study-set-from-notes .input-studyset textarea:focus + label::after {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.study-set-wrapper .input-studyset label,
#study-set-from-notes .input-studyset label {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 0 0.25em;
  width: 100%;
  height: calc(100% - 1em);
  text-align: left;
  pointer-events: none;
  margin-bottom: 0;
}
.study-set-wrapper .input-studyset label > span,
#study-set-from-notes .input-studyset label > span {
  position: absolute;
  font-size: 12px;
  top: -15px;
  z-index: 999;
  color: rgba(68, 68, 68, 0.7);
}
.study-set-wrapper .input-studyset label > span::after,
#study-set-from-notes .input-studyset label > span::after {
  border-color: hsl(200, 100%, 50%);
}
.study-set-wrapper .input-studyset label::before,
#study-set-from-notes .input-studyset label::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 0;
  width: 100%;
  height: calc(100% - 10px);
  border-bottom: 1px solid #B9C1CA;
}
.study-set-wrapper .input-studyset label::after,
#study-set-from-notes .input-studyset label::after {
  content: '';
  position: absolute;
  top: 7px;
  left: 0;
  width: 100%;
  height: calc(100% - 10px);
  border-bottom: 1px solid #B9C1CA;
  margin-top: 2px;
  border-bottom: 2px solid #1F419B;
  -webkit-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
}
@-webkit-keyframes anim-1 {
  50% {
    opacity: 0;
    -webkit-transform: translate3d(1em, 0, 0);
    transform: translate3d(1em, 0, 0);
  }
  51% {
    opacity: 0;
    -webkit-transform: translate3d(-1em, -40%, 0);
    transform: translate3d(-1em, -40%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
  }
}
@keyframes anim-1 {
  50% {
    opacity: 0;
    -webkit-transform: translate3d(1em, 0, 0);
    transform: translate3d(1em, 0, 0);
  }
  51% {
    opacity: 0;
    -webkit-transform: translate3d(-1em, -40%, 0);
    transform: translate3d(-1em, -40%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
  }
}
.study-set-wrapper .input-studyset .change-password,
#study-set-from-notes .input-studyset .change-password {
  position: absolute;
  right: 0;
  bottom: 15px;
  color: #1F419B;
  font-weight: 500;
}
.study-set-item {
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
#withnoweblinks,
#content-data-studyset-nosets,
#withnovideos {
  height: calc(100vh - 115px);
  align-items: center;
  display: flex;
}
#withnoweblinks a,
#content-data-studyset-nosets a,
#withnovideos a {
  margin-bottom: 2rem;
}
.web-url .modal .modal-content,
.video-url .modal .modal-content {
  box-shadow: 0px 19px 38px rgba(0, 0, 0, 0.3), 0px 15px 12px rgba(0, 0, 0, 0.22);
  width: 328px;
  min-height: 204px;
}
.web-url .modal .modal-header,
.video-url .modal .modal-header {
  border-bottom: none;
}
.web-url .modal .modal-title,
.video-url .modal .modal-title {
  margin: 0;
  line-height: 1.42857143;
  font-size: 20px;
}
.web-url .modal .modal-body,
.video-url .modal .modal-body {
  position: relative;
  padding: 15px;
}
.web-url .modal .modal-body input,
.video-url .modal .modal-body input {
  border: none;
  border-bottom: 1px solid rgba(68, 68, 68, 0.72);
  width: 280px;
  outline: 0;
  padding: 10px;
  border-radius: 0;
  margin-bottom: 1rem;
}
.web-url .modal .modal-footer,
.video-url .modal .modal-footer {
  border-top: none;
}
.web-url .modal .modal-footer button,
.video-url .modal .modal-footer button {
  border: none;
}
.web-url .modal .modal-footer button:last-child,
.video-url .modal .modal-footer button:last-child {
  color: #F79420;
}
#content-data-studyset .add-notesPr {
  position: fixed;
  right: 0;
}
#content-data-studyset .add-notesPr > a {
  height: 44px;
  background: #F79420;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 600px;
  color: #ffffff;
  width: 160px;
  border: none;
  display: flex;
  align-items: center;
  float: right;
  justify-content: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #content-data-studyset .add-notesPr > a {
    border-radius: 50%;
    width: 42px !important;
    height: 42px !important;
  }
}
#content-data-studyset .add-notesPr > a i {
  margin-right: 0.5rem;
  font-size: 20px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #content-data-studyset .add-notesPr > a i {
    margin-right: 0;
  }
}
.pr-back-btn {
  background: #ffffff;
  width: 100px;
  height: 40px;
  border-radius: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: -2rem;
  z-index: 1;
  top: 0;
  color: #444444;
  font-size: 14px !important;
}
.pr-back-btn:hover {
  text-decoration: none;
  color: #1F419B;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .pr-back-btn {
    left: -30px;
    background: transparent;
    top: 10px;
    height: 30px;
  }
}
.bg-revCard {
  border-radius: 4px;
  margin: 1rem auto;
  margin-bottom: 2rem;
}
.videoPlays .video-wrapper {
  margin: 1rem;
  max-width: 320px;
}
.videoPlays .video-wrapper a:hover {
  text-decoration: none;
}
.videoPlays .video-wrapper .card {
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border: none;
  text-decoration: none;
  border-radius: 4px;
}
.videoPlays .video-wrapper .card img {
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  min-height: 180px;
  width: 320px;
}
.videoPlays .video-wrapper .card .video-info p {
  padding: 1rem;
  padding-bottom: 0.5rem;
  color: #444444;
  font-size: 14px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
}
.videoPlays .play-btn-wrapper {
  border-radius: 50px;
  width: 40px;
  height: 40px;
  background: #ffffff;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 33%;
  right: 43%;
}
.videoPlays .play-btn-wrapper i {
  color: #1F419B;
}
.videoPlays .play-btn-wrapper span {
  color: rgba(68, 68, 68, 0.72);
}
.radio,
.checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.full-wdth-label {
  width: 100%;
  display: flex;
  align-items: center;
  font-weight: normal;
}
.mcq-question-div input[type=radio] {
  position: absolute;
  opacity: 0;
}
.mcq-question-div input[type="radio"]:checked ~ .checkmark {
  background-color: #1F419B;
  border: 2px solid #F05A2A;
  border-radius: 50px;
  width: 24px;
  height: 24px;
}
.mcq-question-div input[type="radio"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block;
  z-index: -1;
}
.mcq-question-div .checkmark-radio::after {
  left: 0;
  top: 0;
  width: 5px;
  height: 5px;
  border: 10px solid #F05A2A;
  border-radius: 10px;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}
.mcq-question-div .checkmark::after {
  left: 3px;
  top: 4px;
  width: 12px;
  height: 7px;
  border: solid #F05A2A;
  border-width: 3px 3px 0 0;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}
.checkmark-radio::after {
  left: 4px;
  top: 4px;
  width: 5px;
  height: 5px;
  border: 5px solid #F05A2A;
  border-radius: 5px;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}
.checkmark::after {
  left: 1px;
  top: 2px;
  width: 12px;
  height: 7px;
  border: solid #fff;
  border-width: 2px 2px 0 0;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}
.question-div {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.54);
}
.mcq-question-div input[type="radio"] ~ .checkmark {
  color: #fff;
  font-size: 14px;
  text-align: center;
  background-color: rgba(68, 68, 68, 0.74);
  border-radius: 50px;
  width: 24px;
  height: 24px;
}
.practice-score-container {
  min-height: 300px;
  color: #fff;
  text-align: center;
  background: linear-gradient(74.18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -webkit-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -o-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: linear-gradient(18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  padding: 26px 0;
}
.practice-score-container .practice-score {
  width: 274px;
  margin: 0 auto;
}
.practice-score-container .practice-score .medal-picture img {
  width: 150px;
}
.practice-score-container .practice-score .practice-score-string .practice-score-string-score {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}
.practice-score-container .practice-score .practice-score-string .practice-score-string-score p {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}
.answer-summary {
  min-height: 215px;
  padding: 24px 0;
  text-align: center;
  background: #F8F8F8;
}
.answer-summary .summary-heading {
  font-size: 22px;
}
.answer-summary .short-heading {
  font-size: 16px;
}
.answer-summary .score-summary {
  max-width: 634px;
  margin: 0 auto;
  margin-top: 16px;
}
.accuracy-summary {
  max-width: 634px;
  padding-bottom: 16px;
  margin: 0 auto;
  margin-top: 26px;
}
.mcq-question-div p.question-string {
  font-size: 18px;
}
.yellow-top-border > div.bg-revCard {
  border-top: 4px solid #F2C94C;
}
.yellow-top-border .user-analytic-data-colored div span {
  color: #F2C94C;
}
.green-top-border > div.bg-revCard {
  border-top: 4px solid #46B520;
}
.green-top-border .user-analytic-data-colored div span {
  color: #46B520;
}
.red-top-border > div.bg-revCard {
  border-top: 4px solid #B72319;
}
.red-top-border .user-analytic-data-colored div span {
  color: #B72319;
}
.user-analytic-data-colored {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-bottom: 1rem;
}
.btn-review {
  display: block;
  width: 173px;
  height: auto;
  color: #444444;
  font-size: 14px;
  background: #FFFFFF;
  padding: 8px;
  margin: 0 auto;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}
.next-btn {
  height: auto;
  font-size: 14px;
  color: #fff;
  background: #028EDB;
  padding: 8px;
  border: 0;
}
.next-btn:hover {
  color: #ffffff;
}
.correct-answer-learn .correct-answer-label {
  font-size: 20px;
  font-weight: bold;
  color: #888888;
}
.wrong-answer-by-user {
  color: #B72319 !important;
  font-size: 18px;
}
.correct-answer-by-user {
  color: #46B520 !important;
  font-size: 18px;
}
.correct-answer-learn {
  margin-top: 40px;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.74);
}
.wrong-question-answer {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #B72319;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}
.correct-question-answer {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #46B520;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}
.quiz-modal-body {
  min-height: 450px;
  max-height: 450px;
  overflow-y: auto;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .quiz-modal-body {
    min-height: 100%;
    max-height: 100%;
    height: 400px;
  }
}
.sharethis-inline-share-buttons {
  margin-top: 2rem;
}
#content-data-quiz,
#content-data-userNotes,
#content-data-studyset,
#additional-refs {
  justify-content: center;
  padding-top: 2.5rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #displayNotes {
    margin-top: 4rem;
  }
}
#displayNotes .notes_pdf .pr-back-btn {
  position: relative;
  left: 0;
}
#displayNotes .pr-back-btn {
  position: relative;
  /*left: -3rem;*/
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #displayNotes .pr-back-btn {
    top: 0 !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #displayNotes #htmlContent {
    padding-left: 2rem;
    padding-right: 1rem;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #additional-refs {
    margin-top: 0;
  }
}
.notesBtn button {
  background: none;
  color: #444444;
  border: none;
}
.notesBtn button:last-child {
  color: #1F419B;
}
.notesBtn button:hover {
  background: none;
  color: inherit;
}
.video-info {
  padding: 1rem;
}
.video-info p {
  margin-bottom: 0;
}
.video-info .dropdown a:after {
  display: none;
}
.video-img-wrapper img {
  width: 318px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .video-img-wrapper img {
    width: 300px;
  }
}
.next-btn:focus {
  background: #028EDB !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #questionumber-containter {
    display: none !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #videoDiv {
    justify-content: center;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #htmlContent .pr-back-btn {
    top: -30px;
    z-index: 1;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #htmlreadingcontent {
    padding: 15px !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #addNotes {
    padding: 1rem;
  }
}
#study-set-wrapper-container .pr-back-btn {
  left: 3rem;
  top: 2rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #study-set-wrapper-container .pr-back-btn {
    left: 0;
  }
}
#addnotes > .dropdown .dropdown-menu a i {
  font-size: 20px;
  float: right;
  background: #1F419B;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.24), 0px 0px 6px rgba(0, 0, 0, 0.24);
  border-radius: 50px;
  width: 32px;
  height: 32px;
  line-height: 1.4;
  display: block;
  text-align: center;
  margin-left: 1rem;
}
#addRefButton > .dropdown .dropdown-menu a span {
  background: rgba(68, 68, 68, 0.72);
  border-radius: 4px;
  padding: 0.5rem;
  margin-left: -10rem;
}
#videoAddButton .dropdown .dropdown-menu a span {
  background: rgba(68, 68, 68, 0.72);
  border-radius: 4px;
  padding: 0.5rem;
  margin-left: -10rem;
}
#videoAddButton .dropdown .dropdown-menu a i {
  font-size: 20px;
  float: right;
  background: #1F419B;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.24), 0px 0px 6px rgba(0, 0, 0, 0.24);
  border-radius: 50px;
  width: 32px;
  height: 32px;
  line-height: 1.4;
  display: block;
  text-align: center;
  margin-left: 1rem;
}
.annotator-notice,
.annotator-filter *,
.annotator-widget * {
  margin: 0;
  padding: 0;
  background: 0;
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
  color: #909090;
  font-family: "Montserrat", sans-serif;
}
.annotator-resize,
.annotator-widget::after,
.annotator-editor a::after,
.annotator-viewer .annotator-controls button,
.annotator-viewer .annotator-controls a,
.annotator-filter .annotator-filter-navigation button::after,
.annotator-filter .annotator-filter-property .annotator-filter-clear {
  background-image: url("data:image/png;base64,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");
  background-repeat: no-repeat;
}
.annotator-hl {
  background: rgba(255, 255, 10, 0.3);
}
.annotator-hlh {
  background-color: rgba(33, 150, 83, 0.24);
}
.annotator-hl-temporary {
  background: rgba(0, 124, 255, 0.3);
}
.annotator-wrapper {
  position: relative;
}
.annotator-adder,
.annotator-outer,
.annotator-notice {
  z-index: 1020;
}
.annotator-filter {
  z-index: 1010;
}
.annotator-adder,
.annotator-outer,
.annotator-widget,
.annotator-notice {
  position: absolute;
  font-size: 10px;
  line-height: 1;
}
.annotator-hide {
  display: none;
  visibility: hidden;
}
.annotator-adder {
  min-width: 262px;
  background: #FEFEFE;
  border: 0.5px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px 16px;
  margin-top: 0;
  margin-left: 0;
}
.annotator-adder:hover {
  background-position: center top;
}
.annotator-adder:active {
  background-position: center right;
}
.annotator-adder button {
  display: inline-block;
  margin: 0 auto;
  border: 0;
  background: 0;
  cursor: pointer;
  padding: 8px 32px;
  font-weight: 300;
  font-size: 14px;
}
.annotator-adder button:first-child {
  border-right: 1px solid rgba(68, 68, 68, 0.24);
}
.annotator-outer {
  width: 0;
  height: 0;
}
.annotator-widget {
  margin: 0;
  padding: 0 8px;
  left: -18px;
  min-width: 265px;
  background-color: #FFFFFF;
  border: 1px solid rgba(122, 122, 122, 0.6);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  -o-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
.annotator-invert-x .annotator-widget {
  left: auto;
  right: -18px;
}
.annotator-invert-y .annotator-widget {
  bottom: auto;
  top: 8px;
}
.annotator-widget strong {
  font-weight: bold;
}
.annotator-widget .annotator-listing,
.annotator-widget .annotator-item {
  padding: 0;
  margin: 0;
  list-style: none;
}
.annotator-widget::after {
  content: "";
  display: block;
  width: 18px;
  height: 10px;
  background-position: 0 0;
  position: absolute;
  bottom: -10px;
  left: 8px;
  background-image: none;
}
.annotator-invert-x .annotator-widget::after {
  left: auto;
  right: 8px;
}
.annotator-invert-y .annotator-widget::after {
  background-position: 0 -15px;
  bottom: auto;
  top: -9px;
}
.annotator-widget .annotator-item,
.annotator-editor .annotator-item input,
.annotator-editor .annotator-item textarea {
  position: relative;
  font-size: 12px;
}
.annotator-viewer .annotator-item {
  border-top: 2px solid rgba(122, 122, 122, 0.2);
}
.annotator-widget .annotator-item:first-child {
  border-top: 0;
}
.annotator-editor .annotator-item,
.annotator-viewer div {
  border-top: 1px solid rgba(68, 68, 68, 0.24);
}
.annotator-viewer div {
  padding: 6px 6px;
}
.annotator-viewer .annotator-item ol,
.annotator-viewer .annotator-item ul {
  padding: 4px 16px;
}
.annotator-viewer div:first-of-type,
.annotator-editor .annotator-item:first-child textarea {
  padding-top: 12px;
  padding-bottom: 12px;
  color: #3c3c3c;
  font-size: 13px;
  font-style: italic;
  line-height: 1.3;
  border-top: 0;
}
.annotator-viewer .annotator-controls {
  position: relative;
  top: 5px;
  right: 5px;
  padding-left: 5px;
  opacity: 0;
  -webkit-transition: opacity 0.2s ease-in;
  -moz-transition: opacity 0.2s ease-in;
  -o-transition: opacity 0.2s ease-in;
  transition: opacity 0.2s ease-in;
  float: right;
}
.annotator-viewer li:hover .annotator-controls,
.annotator-viewer li .annotator-controls.annotator-visible {
  opacity: 1;
}
.annotator-viewer .annotator-controls button,
.annotator-viewer .annotator-controls a {
  cursor: pointer;
  display: inline-block;
  width: 13px;
  height: 13px;
  margin-left: 2px;
  border: 0;
  opacity: 0.2;
  text-indent: -900em;
  background-color: transparent;
  outline: 0;
}
.annotator-viewer .annotator-controls button:hover,
.annotator-viewer .annotator-controls button:focus,
.annotator-viewer .annotator-controls a:hover,
.annotator-viewer .annotator-controls a:focus {
  opacity: 0.9;
}
.annotator-viewer .annotator-controls button:active,
.annotator-viewer .annotator-controls a:active {
  opacity: 1;
}
.annotator-viewer .annotator-controls button[disabled] {
  display: none;
}
.annotator-viewer .annotator-controls .annotator-edit {
  background-position: 0 -60px;
}
.annotator-viewer .annotator-controls .annotator-delete {
  background-position: 0 -75px;
}
.annotator-viewer .annotator-controls .annotator-link {
  background-position: 0 -270px;
}
.annotator-editor .annotator-item {
  position: relative;
}
.annotator-editor .annotator-item label {
  top: 0;
  display: inline;
  cursor: pointer;
  font-size: 12px;
}
.annotator-editor .annotator-item input,
.annotator-editor .annotator-item textarea {
  display: block;
  min-width: 100%;
  padding: 10px 8px;
  border: 0;
  margin: 0;
  color: #3c3c3c;
  background: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  resize: none;
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar-track-piece {
  margin: 13px 0 3px;
  background-color: #e5e5e5;
  -webkit-border-radius: 4px;
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar-thumb:vertical {
  height: 25px;
  background-color: #ccc;
  -webkit-border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
.annotator-editor .annotator-item textarea::-webkit-scrollbar-thumb:horizontal {
  width: 25px;
  background-color: #ccc;
  -webkit-border-radius: 4px;
}
.annotator-editor .annotator-item:first-child textarea {
  min-height: 5.5em;
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  -o-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
}
.annotator-editor .annotator-item input:focus,
.annotator-editor .annotator-item textarea:focus {
  background-color: #FFFFFF;
  outline: 0;
  resize: none;
}
.annotator-editor .annotator-item input[type=radio],
.annotator-editor .annotator-item input[type=checkbox] {
  width: auto;
  min-width: 0;
  padding: 0;
  display: inline;
  margin: 0 4px 0 0;
  cursor: pointer;
}
.annotator-editor .annotator-checkbox {
  padding: 8px 6px;
}
.annotator-filter,
.annotator-filter .annotator-filter-navigation button,
.annotator-editor .annotator-controls {
  text-align: right;
  padding: 3px;
  border-top: 1px solid #d4d4d4;
  background-color: #d4d4d4;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), color-stop(0.6, #dcdcdc), to(#d2d2d2));
  background-image: -moz-linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
  background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
  background-image: linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
  -webkit-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  -moz-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  -o-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  -webkit-border-radius: 0 0 5px 5px;
  -moz-border-radius: 0 0 5px 5px;
  -o-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px;
}
.annotator-widget .annotator-controls {
  background-color: transparent;
  background-image: none;
  padding: 8px 0;
  border-top: 0;
}
.annotator-editor.annotator-invert-y .annotator-controls {
  border-top: 0;
  border-bottom: 1px solid rgba(68, 68, 68, 0.24);
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  -o-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0;
}
.annotator-editor a,
.annotator-filter .annotator-filter-property label {
  position: relative;
  display: inline-block;
  padding: 0 6px 0 22px;
  color: #363636;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
  text-decoration: none;
  line-height: 24px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid #a2a2a2;
  background-color: #d4d4d4;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), color-stop(0.5, #d2d2d2), color-stop(0.5, #bebebe), to(#d2d2d2));
  background-image: -moz-linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
  background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
  background-image: linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
  -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -moz-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -o-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -o-border-radius: 0px;
  border-radius: 0px;
}
.annotator-controls .annotator-cancel {
  text-transform: uppercase;
  font-family: "Montserrat", sans-serif;
  font-style: normal;
  font-weight: 600 !important;
  line-height: normal;
  font-size: 12px;
  box-shadow: none;
  border: 0;
  background-color: transparent;
  background-image: none;
  color: #B72319;
  letter-spacing: 0.04em;
}
.annotator-controls .annotator-cancel:after {
  content: "";
  background-image: none;
}
.annotator-controls .annotator-cancel:hover {
  color: #B72319;
  text-decoration: none;
  background-color: #FFFFFF;
  background-image: none;
  text-shadow: none;
}
.annotator-controls .annotator-cancel:active {
  color: #B72319;
  text-decoration: none;
  background-color: #FFFFFF;
  background-image: none;
  text-shadow: none;
}
.annotator-controls .annotator-cancel:focus {
  color: #B72319;
  text-decoration: none;
  background-color: #FFFFFF;
  background-image: none;
  text-shadow: none;
}
.annotator-controls .annotator-save {
  text-transform: uppercase;
  font-family: "Montserrat", sans-serif;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  font-size: 12px;
  box-shadow: none;
  border: 0;
  background-color: #FFFFFF;
  background-image: none;
  color: #30C465;
}
.annotator-controls .annotator-save:after {
  content: "";
  background-image: none;
}
.annotator-controls .annotator-save:hover {
  color: #30C465;
  text-decoration: none;
  background-color: #FFFFFF;
  background-image: none;
  text-shadow: none;
}
.annotator-controls .annotator-save:active {
  color: #30C465;
  text-decoration: none;
  background-color: #FFFFFF;
  background-image: none;
  text-shadow: none;
}
.annotator-controls .annotator-save:focus {
  color: #30C465;
  text-decoration: none;
  background-color: #FFFFFF;
  background-image: none;
  text-shadow: none;
}
.annotator-controls .annotator-focus {
  font-weight: 600 !important;
  text-transform: uppercase;
  color: #30C465 !important;
  border-color: transparent !important;
  background-color: #FFFFFF !important;
  background-image: none !important;
  text-shadow: none !important;
  letter-spacing: 0.04em;
}
.annotator-editor a::after {
  position: absolute;
  top: 50%;
  left: 5px;
  display: block;
  content: "";
  width: 15px;
  height: 15px;
  margin-top: -7px;
  background-position: 0 -90px;
}
.annotator-editor a:hover,
.annotator-editor a:focus,
.annotator-editor a.annotator-focus,
.annotator-filter .annotator-filter-active label,
.annotator-filter .annotator-filter-navigation button:hover {
  outline: 0;
  border-color: #435aa0;
  background-color: #3865f9;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#7691fb), color-stop(0.5, #5075fb), color-stop(0.5, #3865f9), to(#3665fa));
  background-image: -moz-linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
  background-image: -webkit-linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
  background-image: linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.42);
}
.annotator-editor a:hover::after,
.annotator-editor a:focus::after {
  margin-top: -8px;
  background-position: 0 -105px;
}
.annotator-editor a:active,
.annotator-filter .annotator-filter-navigation button:active {
  border-color: #700c49;
  background-color: #d12e8e;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fc7cca), color-stop(0.5, #e85db2), color-stop(0.5, #d12e8e), to(#ff009c));
  background-image: -moz-linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
  background-image: -webkit-linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
  background-image: linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
}
.annotator-editor a.annotator-save::after {
  background-position: 0 -120px;
}
.annotator-editor a.annotator-save:hover::after,
.annotator-editor a.annotator-save:focus::after,
.annotator-editor a.annotator-save.annotator-focus::after {
  margin-top: -8px;
  background-position: 0 -135px;
}
.annotator-editor .annotator-widget::after {
  background-position: 0 -30px;
}
.annotator-editor.annotator-invert-y .annotator-widget .annotator-controls {
  background-color: #FFFFFF;
}
.annotator-editor.annotator-invert-y .annotator-widget::after {
  background-position: 0 -45px;
  height: 11px;
}
.annotator-resize {
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-position: 2px -150px;
  display: none;
}
.annotator-invert-x .annotator-resize {
  right: auto;
  left: 0;
  background-position: 0 -195px;
}
.annotator-invert-y .annotator-resize {
  top: auto;
  bottom: 0;
  background-position: 2px -165px;
}
.annotator-invert-y.annotator-invert-x .annotator-resize {
  background-position: 0 -180px;
}
.annotator-notice {
  color: #fff;
  position: absolute;
  position: fixed;
  top: -54px;
  left: 0;
  width: 100%;
  font-size: 14px;
  line-height: 50px;
  text-align: center;
  background: black;
  background: rgba(0, 0, 0, 0.9);
  border-bottom: 4px solid #d4d4d4;
  -webkit-transition: top 0.4s ease-out;
  -moz-transition: top 0.4s ease-out;
  -o-transition: top 0.4s ease-out;
  transition: top 0.4s ease-out;
}
.ie6 .annotator-notice {
  position: absolute;
}
.annotator-notice-success {
  border-color: #3665f9;
}
.annotator-notice-error {
  border-color: #ff7e00;
}
.annotator-notice p {
  margin: 0;
}
.annotator-notice a {
  color: #fff;
}
.annotator-notice-show {
  top: 0;
}
.annotator-tags {
  margin-bottom: -2px;
}
.annotator-tags .annotator-tag {
  display: inline-block;
  padding: 0 8px;
  margin-bottom: 2px;
  line-height: 1.6;
  font-weight: bold;
  background-color: #e6e6e6;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  border-radius: 8px;
}
.annotator-filter {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  text-align: left;
  line-height: 0;
  border: 0;
  border-bottom: 1px solid #878787;
  padding-left: 10px;
  padding-right: 10px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -o-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  -moz-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  -o-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
}
.annotator-filter strong {
  font-size: 12px;
  font-weight: bold;
  color: #3c3c3c;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
  position: relative;
  top: -9px;
}
.annotator-filter .annotator-filter-property,
.annotator-filter .annotator-filter-navigation {
  position: relative;
  display: inline-block;
  overflow: hidden;
  line-height: 10px;
  padding: 2px 0;
  margin-right: 8px;
}
.annotator-filter .annotator-filter-property label,
.annotator-filter .annotator-filter-navigation button {
  text-align: left;
  display: block;
  float: left;
  line-height: 20px;
  -webkit-border-radius: 10px 0 0 10px;
  -moz-border-radius: 10px 0 0 10px;
  -o-border-radius: 10px 0 0 10px;
  border-radius: 10px 0 0 10px;
}
.annotator-filter .annotator-filter-property label {
  padding-left: 8px;
}
.annotator-filter .annotator-filter-property input {
  display: block;
  float: right;
  -webkit-appearance: none;
  background-color: #fff;
  border: 1px solid #878787;
  border-left: none;
  padding: 2px 4px;
  line-height: 16px;
  min-height: 16px;
  font-size: 12px;
  width: 150px;
  color: #333;
  background-color: #f8f8f8;
  -webkit-border-radius: 0 10px 10px 0;
  -moz-border-radius: 0 10px 10px 0;
  -o-border-radius: 0 10px 10px 0;
  border-radius: 0 10px 10px 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
  -o-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
}
.annotator-filter .annotator-filter-property input:focus {
  outline: 0;
  background-color: #fff;
}
.annotator-filter .annotator-filter-clear {
  position: absolute;
  right: 3px;
  top: 6px;
  border: 0;
  text-indent: -900em;
  width: 15px;
  height: 15px;
  background-position: 0 -90px;
  opacity: 0.4;
}
.annotator-filter .annotator-filter-clear:hover,
.annotator-filter .annotator-filter-clear:focus {
  opacity: 0.8;
}
.annotator-filter .annotator-filter-clear:active {
  opacity: 1;
}
.annotator-filter .annotator-filter-navigation button {
  border: 1px solid #a2a2a2;
  padding: 0;
  text-indent: -900px;
  width: 20px;
  min-height: 22px;
  -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -moz-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -o-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
}
.annotator-filter .annotator-filter-navigation button,
.annotator-filter .annotator-filter-navigation button:hover,
.annotator-filter .annotator-filter-navigation button:focus {
  color: transparent;
}
.annotator-filter .annotator-filter-navigation button::after {
  position: absolute;
  top: 8px;
  left: 8px;
  content: "";
  display: block;
  width: 9px;
  height: 9px;
  background-position: 0 -210px;
}
.annotator-filter .annotator-filter-navigation button:hover::after {
  background-position: 0 -225px;
}
.annotator-filter .annotator-filter-navigation .annotator-filter-next {
  -webkit-border-radius: 0 10px 10px 0;
  -moz-border-radius: 0 10px 10px 0;
  -o-border-radius: 0 10px 10px 0;
  border-radius: 0 10px 10px 0;
  border-left: none;
}
.annotator-filter .annotator-filter-navigation .annotator-filter-next::after {
  left: auto;
  right: 7px;
  background-position: 0 -240px;
}
.annotator-filter .annotator-filter-navigation .annotator-filter-next:hover::after {
  background-position: 0 -255px;
}
.annotator-hl-active {
  background: rgba(255, 255, 10, 0.8);
}
.annotator-hl-filtered {
  background-color: transparent;
}
/*  Annotator Touch Plugin - v1.1.1
 *  Copyright 2012-2015, Compendio <www.compendio.ch>
 *  Released under the MIT license
 *  More Information: https://github.com/aron/annotator.touch.js
 */
.annotator-viewer .annotator-touch-controls .annotator-edit::after {
  /* Assign the image once to ensure data uri is not repeated. */
  background-image: url("data:image/png;base64,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");
}
.annotator-selection-handle::after {
  background-image: url("data:image/png;base64,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");
}
.annotator-button::after {
  background-image: url("data:image/png;base64,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");
}
/* Generic Touch Widget Styles */
.annotator-touch-widget * {
  font-family: "Montserrat", sans-serif !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  font-style: normal !important;
}
.annotator-touch-widget {
  font-family: "Montserrat", sans-serif;
  font-weight: 400 !important;
  font-size: 14px !important;
  border: none;
  background: rgba(0, 0, 0, 0.85);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)), color-stop(0.5, rgba(0, 0, 0, 0.85)), to(rgba(0, 0, 0, 0.85)));
  background: -moz-linear-gradient(-90deg, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)), color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
  background: -webkit-linear-gradient(-90deg, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)) 50%, color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
  background: linear-gradient(to bottom, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)) 50%, color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  -ms-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  -o-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  /* Removes the tap outline on elements that have bound touch events */
  -webkit-tap-highlight-color: transparent;
}
.annotator-touch-widget #annotator-field-1 {
  display: none;
}
/*.annotator-touch-widget-inner {
  background: #efefef;
  border: 1px solid rgba(0, 0, 0, 0.8);
  margin: 7px;
  padding: 6px;
  line-height: 0;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -ms-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -o-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
}*/
/*.annotator-touch-widget .annotator-button {
  cursor: pointer;
  font-size: 16px;
  line-height: 44px;
  padding-left: 40px;
  padding-right: 20px;
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}*/
.annotator-touch-widget .annotator-button[disabled] {
  opacity: 0.3;
  cursor: default;
}
.annotator-touch-widget .annotator-button::after {
  left: 15px;
}
.annotator-touch-widget .annotator-add::after,
.annotator-touch-widget .annotator-add:hover::after,
.annotator-touch-widget .annotator-add:focus::after,
.annotator-touch-widget .annotator-add.annotator-focus::after {
  margin-top: -7px;
  background-position: 0 -270px;
}
/* Adder Styles */
.annotator-touch-controls {
  position: fixed;
  top: 30%;
  font-size: 10px;
  line-height: 1;
  min-width: auto;
  background: #FEFEFE;
  border: 0.5px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  box-shadow: 8px 2px 32px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px 16px;
  margin-top: 0;
  margin-left: 0;
  z-index: 3;
  left: 4px;
}
.annotator-touch-controls.annotator-touch-hide {
  right: -9999em;
  opacity: 0;
  -webkit-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  -moz-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  -ms-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  -o-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
}
/*.annotator-touch-controls .annotator-button {
  line-height: 56px;
}*/
/* Viewer Overrides*/
.annotator-touch-viewer .annotator-widget {
  min-width: 380px;
}
.annotator-touch-viewer div {
  padding: 12px;
}
.annotator-touch-viewer div:first-of-type {
  font-size: 18px;
  padding-top: 20px;
  padding-bottom: 20px;
}
.annotator-touch-viewer .annotator-touch-controls {
  position: absolute;
  top: 0;
  left: auto;
  right: 0;
  display: none;
  background: #fff;
  -webkit-box-pack: end;
  -webkit-box-align: center;
  -webkit-box-orient: horizontal;
  -moz-box-pack: end;
  -moz-box-align: center;
  -moz-box-orient: horizontal;
  box-pack: end;
  box-align: center;
  box-orient: horizontal;
  padding: 10px;
  bottom: 0;
  padding: 0 10px 0 20px;
}
.annotator-touch-viewer li.annotator-visible .annotator-touch-controls {
  display: -webkit-box;
  display: -moz-box;
  display: box;
}
.annotator-touch-viewer .annotator-touch-controls button {
  line-height: 44px;
  padding-left: 40px;
  padding-right: 20px;
  margin-left: 6px;
  border: 0;
}
.annotator-touch-viewer .annotator-touch-controls .annotator-edit::after {
  background-position: 0 -15px;
}
.annotator-touch-controls .annotator-edit {
  color: #2F80ED;
}
.annotator-touch-controls .annotator-delete {
  color: #B72319;
}
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:hover::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:focus::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:active::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit.annotator-focus::after {
  background-position: 0 -30px;
}
.annotator-touch-viewer .annotator-touch-controls button::after {
  left: 15px;
}
/* Editor Overrides */
.annotator-touch-editor {
  position: fixed;
  top: -1000px !important;
  left: 0 !important;
  right: 0;
  bottom: -1000px;
  padding: 1000px 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  display: -webkit-box;
  display: -moz-box;
  display: box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -moz-box-pack: center;
  -moz-box-align: center;
  box-pack: center;
  box-align: center;
}
.annotator-touch-editor .annotator-touch-widget {
  pointer-events: all;
  position: relative;
  width: 80%;
  max-width: 680px;
}
.annotator-touch-editor .annotator-touch-widget-inner {
  position: static;
  width: auto;
  padding: 0;
  background: #fff;
}
.annotator-touch-editor .annotator-widget::after {
  display: none;
}
.annotator-touch-editor .annotator-widget .annotator-item {
  border-top-color: rgba(33, 150, 83, 0.24);
}
.annotator-touch-editor .annotator-widget .annotator-item,
.annotator-touch-editor.annotator-editor .annotator-item label,
.annotator-touch-editor.annotator-editor .annotator-item input,
.annotator-touch-editor.annotator-editor .annotator-item textarea {
  font-size: 18px;
}
.annotator-touch-editor.annotator-editor .annotator-item input,
.annotator-touch-editor.annotator-editor .annotator-item label {
  line-height: 30px;
  margin-left: 8px;
}
.annotator-touch-editor.annotator-editor .annotator-item input[checkbox] {
  font-size: large;
}
.annotator-touch-editor .annotator-widget .annotator-item:first-child textarea {
  font-size: 18px;
  background-color: #fff;
  -webkit-border-radius: 3px 3px 0 0;
  -moz-border-radius: 3px 3px 0 0;
  -o-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0;
}
.annotator-touch-editor .annotator-resize {
  display: none;
}
.annotator-touch-editor .annotator-controls {
  padding: 7px;
  background-color: #fff;
  background-image: none;
}
.annotator-touch-editor .annotator-item-quote {
  font-size: 16px;
  line-height: 1.2;
  border-color: rgba(33, 150, 83, 0.24);
  background-color: rgba(33, 150, 83, 0.24);
  color: #000;
  padding: 10px 7px;
}
.annotator-item-quote span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #000;
  font-style: italic !important;
}
.annotator-item-quote.annotator-touch-expand span {
  overflow: visible;
  text-overflow: inherit;
  white-space: inherit;
}
.annotator-item-quote button {
  font-size: 14px;
  line-height: 44px;
  margin-top: -13px;
  float: right;
  text-transform: uppercase;
  font-weight: bold;
  color: #a58129;
  border: none;
  background: none;
  margin-left: 10px;
  cursor: pointer;
}
.annotator-button::after {
  background-repeat: no-repeat;
}
/*.annotator-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  position: relative;
  display: inline-block;
  padding: 0 6px 0 22px;
  color: rgb(54, 54, 54);
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
  text-decoration: none;
  line-height: 24px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid rgb(162, 162, 162);
  background-color: rgb(212, 212, 212);
  background-image: -webkit-gradient(
    linear, left top, left bottom,
    from(rgb(245, 245, 245)),
    color-stop(0.5, rgb(210, 210, 210)),
    color-stop(0.5, rgb(190, 190, 190)),
    to(rgb(210, 210, 210))
  );
  background-image: -moz-linear-gradient(
      -90deg,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  background-image: -webkit-linear-gradient(
      -90deg,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  background-image: linear-gradient(
      to bottom,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  -webkit-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -moz-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -o-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
}*/
/*.annotator-button::after {
  position: absolute;
  top: 50%;
  left: 5px;
  display: block;
  content: "";
  width: 15px;
  height: 15px;
  margin-top: -7px;
  background-position: 0 -90px;
}*/
.annotator-button:hover,
.annotator-button:focus,
.annotator-button.annotator-focus {
  color: rgba(68, 68, 68, 0.5);
  display: inline-block;
  margin: 0 auto;
  border: 0;
  background: 0;
  cursor: pointer;
  padding: 8px 8px;
  font-weight: 300;
  font-size: 14px;
}
.annotator-button.annotator-focus:first-child {
  border-right: 1px solid rgba(68, 68, 68, 0.24);
}
/*.annotator-button:hover::after,
.annotator-button:focus::after {
  margin-top: -8px;
  background-position: 0 -105px;
}*/
/*.annotator-button:active {
  border-color: rgb(112, 12, 73);
  background-color: rgb(209, 46, 142);
  background-image: -webkit-gradient(
    linear, left top, left bottom,
    from(rgb(252, 124, 202)),
    color-stop(0.5, rgb(232, 93, 178)),
    color-stop(0.5, rgb(209, 46, 142)),
    to(rgb(255, 0, 156))
  );
  background-image: -moz-linear-gradient(
      -90deg,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
  background-image: -webkit-linear-gradient(
      -90deg,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
  background-image: linear-gradient(
      to bottom,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
}

.annotator-button.annotator-save::after {
  background-position: 0 -120px;
}*/
.annotator-button.annotator-save::after,
.annotator-button.annotator-save:focus::after,
.annotator-button.annotator-save.annotator-focus::after {
  margin-top: -8px;
  background-position: 0 -135px;
}
/* Icon only button styles */
[data-annotator-use-icons] .annotator-touch-widget .annotator-button {
  /* width & overflow is required by Android WebKit */
  width: 1px;
  overflow: hidden;
  text-indent: -999em;
  padding-left: 25px;
}
[data-annotator-use-icons] .annotator-touch-controls .annotator-button {
  padding-left: 35px;
}
[data-annotator-use-icons] .annotator-touch-controls .annotator-button::after {
  left: 20px;
}
[data-annotator-use-icons] .annotator-touch-viewer .annotator-touch-controls button {
  padding-left: 25px;
  text-indent: -9000em;
}
[data-annotator-use-icons] .annotator-touch-viewer .annotator-touch-controls button::after {
  left: 15px;
}
[data-annotator-use-icons] .annotator-touch-viewer .annotator-widget {
  min-width: 320px;
}
/* Highlighter Selection Styles */
.annotator-selection-handle {
  cursor: pointer;
  display: block;
  position: absolute;
  width: 44px;
  height: 44px;
  top: 0;
  left: 0;
  padding: 0;
  margin-left: -22px;
  margin-top: -22px;
  border-radius: 50%;
  /* Removes the tap outline on elements that have bound touch events */
  -webkit-tap-highlight-color: transparent;
}
.annotator-selection-handle::after {
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  left: 50%;
  margin-left: -10px;
  bottom: -5px;
  background-position: 0 0;
  background-repeat: no-repeat;
}
.annotator-selection-start::after {
  top: -5px;
  bottom: auto;
  background-position: 0 -20px;
}
.annotator-selection-hide .annotator-selection-handle {
  display: none;
}
/* Styles for smaller screens */
@media only screen and (max-width: 480px) {
  .annotator-touch-viewer {
    left: 0 !important;
    width: 100%;
    background: none;
    min-width: 0;
    border: none;
  }
  .annotator-touch-viewer .annotator-widget {
    position: static;
    left: 0;
    width: 100%;
    height: auto;
    min-width: 0;
    -webkit-box-sizing: border-box;
    -webkit-border-radius: none;
    border-radius: none;
  }
  .annotator-touch-viewer .annotator-widget::after {
    display: none;
  }
  .annotator-touch-editor {
    border: none;
    -webkit-box-align: start;
    -moz-box-align: start;
    box-align: start;
  }
  .annotator-touch-editor .annotator-touch-widget {
    width: 100%;
    max-width: auto;
    margin: 0;
    border-color: #333;
    border-left: none;
    border-right: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
  }
  .annotator-touch-editor .annotator-touch-widget-inner {
    width: 100%;
    max-width: auto;
    margin: 0;
    border: 0;
  }
  .annotator-touch-editor .annotator-controls {
    border-bottom: 1px solid #D4D4D4;
  }
  .annotator-touch-editor .annotator-touch-widget,
  .annotator-touch-editor .annotator-touch-widget-inner,
  .annotator-touch-editor .annotator-touch-widget .annotator-item:first-child textarea,
  .annotator-touch-editor .annotator-controls {
    border-radius: 0;
  }
}
#htmlContent .annotator-hl {
  background: rgba(255, 255, 10, 0.3);
}
#htmlContent .annotator-hlh {
  background-color: rgba(33, 150, 83, 0.24);
}
#htmlContent .annotator-hl-temporary {
  background: rgba(0, 124, 255, 0.3);
}
.annotator-adder .annotate-btn {
  border-right: 1px solid rgba(68, 68, 68, 0.24);
  display: inline-block;
}
.annotator-adder .annotate-btn:last-child {
  border-right: 0;
}
.annotator-adder a {
  display: block;
  white-space: nowrap;
  color: black;
}
.annotator-adder button,
.annotator-adder a {
  padding: 8px 12px;
}
.annotator-touch-widget-inner .annotator-button {
  width: 25%;
  white-space: nowrap;
  border-right: 1px solid rgba(68, 68, 68, 0.24);
  text-align: center;
}
.annotator-touch-widget-inner .annotator-button:last-child {
  border-right: none;
}
.annotator-touch-widget {
  width: 97%;
}
.annotator-touch-widget .annotator-touch-widget-inner {
  width: 100%;
}
.annotator-editor .annotator-touch-widget {
  padding: 0.5rem;
  border: none;
  background: transparent;
  box-shadow: none;
}
/*Wonderslate color themes*/
/* NOTES:-> When we are working on utkarsh comment above color themes and use below themes*/
/*eUtkarsh color themes*/
/*Home page*/
/*evidya*/
/*Ramaiah*/
/*Home page*/
/*-Arihant-----*/
/*Blackspine color themes*/
@font-face {
  src: url('../../fonts/Kruti_Dev_010.ttf');
  font-family: "Kruti Dev 010";
}
.new_book_create h4 {
  font-family: 'Rubik', sans-serif;
}
.new_book_create .lightblue_bg {
  background-color: #F3F7FA;
  box-shadow: 0 2px 10px #E9EEF5;
  padding: 0.1rem;
}
.new_book_create #pills-tab {
  box-shadow: 0 2px 4px #B9B9C8;
}
.new_book_create #pills-tab a {
  font-size: 16px;
}
.new_book_create #pills-tab a.active {
  background: none !important;
  border-bottom: 3px solid !important;
  color: #F79420;
}
.new_book_create #pills-tab a:hover,
.new_book_create #pills-tab a:focus,
.new_book_create #pills-tab a:active {
  color: #F79420 !important;
  background: none !important;
}
.new_book_create .book_details_info ::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #A7A7B7;
  font-size: 14px;
}
.new_book_create .book_details_info ::-moz-placeholder {
  /* Firefox 19+ */
  color: #A7A7B7;
  opacity: 1;
  font-size: 14px;
}
.new_book_create .book_details_info :-ms-input-placeholder {
  /* IE 10+ */
  color: #A7A7B7;
  font-size: 14px;
}
.new_book_create .book_details_info :-moz-placeholder {
  /* Firefox 18- */
  color: #A7A7B7;
  opacity: 1;
  font-size: 14px;
}
.new_book_create .book_details_info div.form-group label {
  font-weight: 500;
  font-size: 12px;
  width: 100% !important;
  height: 100% !important;
  margin-bottom: 0.2rem;
}
.new_book_create .book_details_info div.form-group input,
.new_book_create .book_details_info div.form-group textarea,
.new_book_create .book_details_info div.form-group select {
  font-size: 14px;
  box-shadow: 0 2px 4px #ECECFB;
  color: #212121;
  border-color: #B4CDDE !important;
}
.new_book_create .book_details_info div.form-group .invalid-feedback {
  font-weight: 500;
}
.uploadimages input {
  display: none;
}
.uploadimages label {
  border: 1px solid #ddd;
  width: 150px !important;
  height: 140px;
  margin: 5px;
  border-radius: 4px;
}
.coverimageWrapper {
  background: rgba(180, 205, 222, 0.2);
}
.coverimageWrapper h4 {
  text-align: center;
  font-size: 16px;
  font-weight: normal;
}
.chapter-wrapper {
  border: 1px solid #ddd;
  padding: 5px;
  border-radius: 4px;
  background: rgba(211, 232, 249, 0.5);
}
.chapter-wrapper h4 {
  font-size: 16px;
  font-weight: normal;
}
.chapter-wrapper button {
  border: 1px solid cornflowerblue;
  font-weight: normal;
}
.list-chapters div {
  border-bottom: 1px solid #ddd;
}
.list-chapters div:last-child {
  border-bottom: none;
}
.list-chapters div.p {
  font-size: 14px;
}
.list-chapters i {
  color: darkred;
}
.resources h2 {
  font-size: 16px;
  padding-bottom: 0.5rem;
}
.resources h4 {
  font-size: 14px;
}
.resources a {
  display: block;
  padding-bottom: 0.5rem;
  color: cornflowerblue;
}
.resources a:hover {
  color: coral;
  cursor: pointer;
}
.chapter-table {
  background: #fff;
}
.chapter-table td {
  font-size: 14px;
}
.chapter-table td.action-align {
  padding: 2px;
}
.chapter-table .thead-dark th {
  font-size: 14px;
  background: #1F419B;
  border-bottom: none ;
  border-top: none;
  border-color: #dee2e6;
}
.chapter-table a.btn {
  background: #fff !important;
  border-color: cornflowerblue;
  color: cornflowerblue !important;
  font-weight: normal;
  font-size: 12px;
  margin-right: 5px;
}
.chapter-table a.btn:last-child {
  border-color: darkred;
  color: darkred !important;
}
.chapter-table td,
.chapter-table th {
  padding: 0.5rem;
  text-align: center;
  vertical-align: middle;
}
#addedtags tr td {
  padding: 0;
  vertical-align: middle;
  text-align: center;
  background: #fff;
}
#addedtags tr td .delete {
  color: darkred;
}
.book_details_info label {
  text-transform: uppercase;
  margin-bottom: 0.5rem;
}
.addCategories span {
  display: block;
  color: #444;
  font-size: 12px;
  font-style: italic;
  padding: 2px;
}
.add-new {
  color: #007bff;
  border: 1px solid #007bff;
}
.buttonBrowse {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.createBook-modal .modal-header {
  border-bottom: none;
}
.createBook-modal .modal-header h4 {
  margin-top: 1rem;
  text-align: center;
  font-size: 18px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}
.createBook-modal .fileBtn {
  height: calc(2.25rem + 2px);
  padding: 0.375rem 0.75rem;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  width: 100%;
}
@media (min-width: 576px) {
  .createBook-modal .modal-dialog {
    width: 400px;
  }
}
.radioCustom,
.pdfyes {
  border: 1px solid #007bff;
  border-radius: 4px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding: 5px;
}
.radioCustom1,
.pdfno {
  border: 1px solid #007bff;
  border-left: none;
  border-radius: 4px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -4px;
  padding: 5px;
}
input[type="radio"]:focus + label {
  border: 1px solid #007bff;
}
input[type="radio"]:checked + label {
  background-color: #007bff;
  color: #fff;
}
.bookimages,
.coverimages {
  display: flex;
  align-items: center;
  justify-content: center;
}
.bookimages span.material-icons,
.coverimages span.material-icons {
  font-size: 48px;
  color: #ddd;
}
.dimension {
  color: rgba(68, 68, 68, 0.5);
  font-size: 14px;
  text-align: center;
  margin-top: 0.2rem;
}
.author-select .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
  width: 100%;
}
.btn-close {
  background: #000;
  font-size: 12px;
  border-radius: 4px;
  color: #fff;
  float: right;
  text-transform: capitalize;
}
.btn-close:focus {
  outline: 0;
}
.alert-warning.red {
  margin-left: 0;
  margin-top: 10px;
}
.book_details_info .overlay-fade-in img {
  width: 100px;
}
.book_details_info .image-wrapper {
  background: #fff;
}
.book_details_info #filelabel1,
.book_details_info #filelabel2 {
  height: 200px;
  position: absolute;
  top: 0;
  z-index: 999;
  display: flex;
  align-items: center;
}
.book_details_info .uploadimages label {
  margin: 0;
}
#bookcover .smallText {
  height: 100%;
  margin-top: 0;
}
.goback {
  color: cornflowerblue;
  font-size: 14px;
  padding-bottom: 0.5rem;
}
.goback i {
  color: #444;
}
.goback:hover {
  text-decoration: none;
  cursor: pointer;
}
.bookchapterHeader {
  font-size: 18px;
  margin-left: 6px;
}
.add-contents {
  margin-top: 4rem;
}
.mcq_creation #static-content {
  box-shadow: 0 2px 10px #E9EEF5;
  background: #FFF;
}
.mcq_creation #static-content .firstPage {
  background-color: #F3F7FA;
  box-shadow: 0 2px 10px #E9EEF5;
}
.mcq_creation #static-content .firstPage input {
  background: #fff;
  padding: 0.375rem 0.75rem;
  border-radius: 5px;
  border: 1px solid #B4CDDE;
  box-shadow: 0 2px 4px #ECECFB;
  font-size: 14px;
  width: 100%;
}
.mcq_creation #static-content .firstPage select {
  font-size: 14px;
  color: #212121;
  border: 1px solid #B4CDDE;
  box-shadow: 0 2px 4px #ECECFB;
  width: 100%;
}
.mcq_creation #static-content .firstPage label {
  font-weight: 500;
  font-size: 12px;
  margin-bottom: 0.2rem;
}
.mcq_creation #static-content .firstPage label.form-check-label {
  font-weight: normal;
  font-size: 14px;
  margin-bottom: 0;
}
.mcq_creation #static-content .firstPage .form-check input {
  width: 16px;
  height: 16px;
  box-shadow: none;
}
.mcq_creation #static-content .firstPage .inlineEditor {
  border-color: #B4CDDE;
}
.mcq_creation #static-content #questionLabel {
  font-weight: 500;
  font-size: 12px;
  margin-bottom: 0.2rem;
  text-transform: uppercase;
}
.mcq_creation #static-content .inlineEditor {
  border-color: #CCC;
}
.mcq_creation #static-content .quiz {
  width: 100%;
  margin: 0 auto;
}
.mcq_creation #static-content .quiz input[type="checkbox"] {
  width: 18px;
  height: 18px;
}
.mcq_creation #static-content .quiz1,
.mcq_creation #static-content .quiz2 {
  width: 100%;
}
.mcq_creation #static-content .quiz1 input,
.mcq_creation #static-content .quiz2 input {
  border-color: #CCC;
}
.mcq_creation #static-content .quiz1 label {
  font-weight: 500;
  font-size: 12px;
  margin-bottom: 0.2rem;
  color: #212529;
}
.mcq_creation #static-content .quiz3,
.mcq_creation #static-content .quiz4 {
  width: 100%;
}
.mcq_creation #static-content .smallerText label {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 12px;
  margin-bottom: 0.2rem;
}
.mcq_creation #static-content .smallerText input {
  border-color: #CCC;
  font-size: 14px;
}
.mcq_creation #static-content .smallerText select {
  font-size: 14px;
}
.mcq_creation #static-content .quiz6 .btn {
  text-transform: uppercase;
  font-size: 13px;
}
.mcq_creation #static-content .quiz6 .btn-default {
  color: #0AAEF9;
  border-color: #0AAEF9;
}
.mcq_creation #static-content .quiz6 .btn-default:focus,
.mcq_creation #static-content .quiz6 .btn-default:active {
  background-color: #efefef !important;
  border-color: transparent !important;
  box-shadow: 0 0 0 0.1rem rgba(23, 162, 184, 0.5) !important;
}
.mcq_creation #static-content .quiz6 .btn-outline-primary {
  color: #007bff !important;
}
.mcq_creation #static-content .quiz6 .btn-outline-primary:focus,
.mcq_creation #static-content .quiz6 .btn-outline-primary:active {
  background-color: transparent !important;
  border-color: #007bff !important;
  box-shadow: 0 0 0 0.1rem rgba(0, 123, 255, 0.5) !important;
}
.mcq_creation #static-content .quiz6 .btn-outline-primary:hover {
  background: transparent !important;
}
.mcq_creation #static-content .quiz8 .alert {
  margin-left: 0;
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}
.mcq_creation #static-content .quiz8 .alert p {
  margin-bottom: 0;
}
.mcq_creation #static-content .cktext.red-border {
  border: none;
}
.mcq_creation #static-content .cktext.red-border .has-error {
  border-color: red;
  border-width: 2px;
}
.mcq_creation #static-content .cktext.red-border .red-border {
  border: none;
  border-bottom: 2px solid red;
}
.mcq_creation #sidebar .quiz-number {
  height: 30px;
  width: 35px;
  padding: 4px;
  float: left;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-bottom: 10px;
  border-width: 2px;
  font-weight: bold;
}
.mcq_creation #sidebar .quiz-number:focus {
  color: #FFF;
}
/*Library*/
.library {
  padding-top: 2rem;
  min-height: 100vh;
  background: #F5F5F5;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .library {
    margin-top: 2rem;
  }
}
.library .nav-tabs {
  border: none;
}
.library .nav-tabs .nav-link {
  cursor: pointer;
  font-size: 26px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.7);
  font-family: 'Merriweather', serif;
}
.library .nav-tabs .nav-link.active {
  border: none;
  background: none;
  border-bottom: 3px solid #1F419B;
  color: #444444;
}
.library .nav-tabs .nav-link:hover {
  border: none;
  border-bottom: 3px solid #1F419B;
}
.library .nav-tabs li:last-child {
  margin-left: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .library .nav-tabs li:last-child {
    margin-left: 0;
  }
}
.library .username p {
  font-size: 32px;
  font-family: 'Merriweather', serif;
  font-weight: bold;
  color: rgba(68, 68, 68, 0.24);
  margin-bottom: 0.5rem;
}
.library .username p span {
  color: #444444;
  text-transform: capitalize;
}
.library .username p:last-child {
  font-size: 20px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.72);
}
.library .generate {
  background: #ffffff;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 600px;
  color: #444444;
}
.library .generate img {
  width: 24px;
}
.library .tab-content {
  padding-bottom: 2rem;
  margin-top: 2rem;
}
.library .tab-content h4 {
  font-family: 'Rubik', sans-serif;
  font-size: 20px;
  color: #444444;
  padding: 10px;
}
.library .tab-content a:hover {
  text-decoration: none;
}
.library .tab-content .card {
  padding: 10px;
  width: 175px;
  border: none;
  background: none;
}
.library .tab-content .card > div.share-wrapper {
  position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .library .tab-content .card {
    width: 100%;
  }
}
.library .tab-content .card img {
  width: 154px;
  height: 192px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait) {
  .library .tab-content .card img {
    height: auto;
    width: 100%;
  }
}
.library .tab-content .card .card-body {
  padding: 0;
}
.library .tab-content .card .card-body .card-text {
  color: #444444;
  margin-top: 0.5rem;
  width: 152px;
  height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .library .tab-content .card .card-body .card-text {
    width: 100%;
  }
}
.library .tab-content .card .card-body .card-text:hover {
  color: #1F419B;
  text-decoration: underline;
}
.library .tab-content .card:hover .share-content {
  display: block;
}
.library .tab-content .card .btn.dropdown-toggle {
  background: none;
}
.library .tab-content .dropup .dropdown-toggle::after {
  display: none;
}
.library .tab-content .dropdown-menu {
  min-width: 100px;
  top: 15px !important;
}
.library .tab-content .dropdown-menu a.dropdown-item {
  color: darkgreen;
}
.library .tab-content .dropdown-menu a.dropdown-item:last-child {
  color: #B72319;
}
.library #recentRead #recentBooks a .item .content-wrapper {
  height: auto;
}
.books-content-wrapper {
  margin-top: 2rem;
}
.books-content-wrapper h4 {
  font-family: 'Rubik', sans-serif;
  font-size: 20px;
  color: #444444;
  padding: 10px;
}
.books-content-wrapper a:hover {
  text-decoration: none;
}
.books-content-wrapper .card {
  padding: 10px;
  width: 175px;
  border: none;
  background: none;
}
.books-content-wrapper .card > div.share-wrapper {
  position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .books-content-wrapper .card {
    width: 100%;
  }
}
.books-content-wrapper .card img {
  width: 154px;
  height: 192px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait) {
  .books-content-wrapper .card img {
    height: auto;
    width: 100%;
  }
}
.books-content-wrapper .card .card-body {
  padding: 0;
}
.books-content-wrapper .card .card-body .card-text {
  color: #444444;
  margin-top: 0.5rem;
  width: 152px;
  height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .books-content-wrapper .card .card-body .card-text {
    width: 100%;
  }
}
.books-content-wrapper .card .card-body .card-text:hover {
  color: #1F419B;
  text-decoration: underline;
}
.books-content-wrapper .card:hover .share-content {
  display: block;
}
.books-content-wrapper .card .btn.dropdown-toggle {
  background: none;
}
.books-content-wrapper .dropup .dropdown-toggle::after {
  display: none;
}
.books-content-wrapper .dropdown-menu {
  min-width: 100px;
  top: 15px !important;
}
.books-content-wrapper .dropdown-menu a.dropdown-item {
  color: darkgreen;
}
.books-content-wrapper .dropdown-menu a.dropdown-item:last-child {
  color: #B72319;
}
.lib-showcase {
  position: relative;
  z-index: 9;
  min-height: 192px;
  border-radius: 4px;
  color: #fff;
}
.lib-showcase:hover p {
  color: #fff;
}
.share-content {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(68, 68, 68, 0.5);
  z-index: 999;
}
.btn-none {
  background: none;
  border: none;
  outline: 0;
  display: none;
}
.btn-none:hover {
  color: #1F419B;
}
.btn-none:focus {
  outline: none;
}
.headerSlider a:hover {
  text-decoration: none;
}
.quiz-buttons .btn.disabled {
  background: transparent;
}
.uncoverdetail {
  height: 165px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #F79420;
}
.uncoverdetail p {
  text-align: center;
  text-transform: capitalize;
  font-size: 16px;
  font-style: italic;
  color: #fff;
}
.uncover {
  height: 192px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}
.uncover p {
  text-align: center;
  text-transform: capitalize;
  font-size: 16px;
  font-style: italic;
  color: #fff;
}
.bookShadow .uncover {
  height: 165px;
}
.bookShadow .uncover p {
  color: #fff;
  font-size: 14px;
}
#ExportToStudySet {
  display: none;
}
#classroom #libraryAssignment .media {
  padding: 1rem;
  background: #ffffff;
  border-left: 5px solid #1F70B5;
  margin-bottom: 2rem;
  border-radius: 3px;
  box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
}
#classroom #libraryAssignment .media .media-body p {
  margin-bottom: 0;
}
#classroom #libraryAssignment .media .media-body h4 {
  color: #444444;
  font-size: 20px;
  font-family: 'Rubik', sans-serif;
  font-weight: normal;
  margin-left: 0.5rem;
  text-transform: capitalize;
  padding: 0;
}
#classroom #libraryAssignment .media .media-body .instructor-name {
  margin-top: 2rem;
  border-bottom: 1px solid rgba(68, 68, 68, 0.5);
}
#classroom #libraryAssignment .media .media-body .instructor-name p {
  color: #444444;
  font-size: 16px;
}
#classroom #libraryAssignment .media .media-body .instructor-name p span {
  color: rgba(68, 68, 68, 0.7);
  margin-right: 0.5rem;
}
#classroom #libraryAssignment .media .media-body .assignment-done {
  display: flex;
  align-items: center;
  color: #30C465;
  width: 50%;
  justify-content: center;
  margin-left: -1.8rem;
}
#classroom #libraryAssignment .media .media-body .assignment-done i {
  padding-right: 0.5rem;
  color: #30C465;
}
#classroom #libraryAssignment .media .media-body .not-done {
  color: rgba(68, 68, 68, 0.9);
  width: 50%;
  justify-content: center;
  margin-left: -0.8rem;
}
#classroom #libraryAssignment .media .media-body .not-done i {
  padding-right: 0.5rem;
  color: rgba(68, 68, 68, 0.9);
}
#classroom #libraryAssignment .media .media-body .mark-complete {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(68, 68, 68, 0.9);
  background: none;
  outline: 0;
  font-weight: normal;
  font-family: 'Rubik', sans-serif;
  width: 50%;
}
#classroom #libraryAssignment .media .media-body .mark-complete.complete i {
  color: #30C465;
}
#classroom #libraryAssignment .media .media-body .mark-complete.complete span {
  color: #30C465;
}
#classroom #libraryAssignment .media .media-body .mark-complete span {
  font-size: 14px;
}
#classroom #libraryAssignment .media .media-body .mark-complete i {
  padding-right: 0.5rem;
  color: rgba(68, 68, 68, 0.9);
}
#classroom #libraryAssignment .media .media-body .mark-complete:hover {
  color: #000;
}
#classroom #libraryAssignment .media .media-body .attempt-assignment-btn {
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  color: #ffffff;
  font-weight: 500;
  border: none;
  width: 120px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #classroom #libraryAssignment .media .media-body .attempt-assignment-btn {
    width: auto;
  }
}
#classroom #libraryAssignment .media .media-body div button {
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  color: #ffffff;
  font-weight: 500;
  border: none;
}
#classroom #libraryAssignment .media .media-body div .button-wrapper {
  width: 50%;
  text-align: center;
  border-left: 1px solid rgba(68, 68, 68, 0.4);
  height: 34px;
}
#classroom #libraryAssignment .media .media-body div .button-wrapper p {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
#allindiatest .testFilter {
  background: #ffffff;
}
#allindiatest .testFilter h3 {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.5);
  border-bottom: 0.5px solid rgba(68, 68, 68, 0.5);
  font-weight: normal;
  font-family: 'Rubik', sans-serif;
  padding: 0.7rem 0.5rem;
}
#allindiatest .testFilter ul {
  padding: 0.5rem 1.5rem;
  list-style: none;
  counter-reset: li;
}
#allindiatest .testFilter ul li {
  padding: 0rem 2px;
}
#allindiatest .testFilter ul li a {
  color: #444444;
  counter-increment: li;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
}
#allindiatest .testFilter ul li a.active {
  color: #1F419B;
}
#allindiatest .testFilter ul li a.active:before {
  color: #1F419B;
}
#allindiatest .testFilter ul li a:before {
  content: counter(li);
  width: 1em;
  margin-left: -1em;
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  position: relative;
  left: -1rem;
}
#allindiatest .test-wrapper [data-state="live"] .card .livelabel {
  display: block;
}
#allindiatest .test-wrapper [data-state="live"] .card .btn-wrappers.startTest {
  display: block;
}
#allindiatest .test-wrapper [data-state="live"] .card .btn-wrappers.showRank {
  display: block;
}
#allindiatest .test-wrapper [data-state="live"] .card .btn-wrappers.showRank .btn-test {
  display: none;
}
#allindiatest .test-wrapper [data-state="showrankonly"] .card .btn-wrappers.showRank {
  display: block;
}
#allindiatest .test-wrapper [data-state="showrankonly"] .card .btn-wrappers.showRank .btn-test {
  display: none;
}
#allindiatest .test-wrapper [data-state="showrank"] .card .btn-wrappers.showRank {
  display: block;
}
#allindiatest .test-wrapper [data-state="registered"] .card .btn-wrappers.registered {
  display: block;
}
#allindiatest .test-wrapper:hover {
  box-shadow: none;
  background: none;
}
#allindiatest .test-wrapper .card {
  background: #ffffff;
  padding: 1rem 0;
  width: 152px;
  min-height: 192px;
  height: auto;
}
#allindiatest .test-wrapper .card .test-content > div {
  width: auto;
}
#allindiatest .test-wrapper .card .test-content p,
#allindiatest .test-wrapper .card .test-content span {
  text-align: left;
}
#allindiatest .test-wrapper .card .livelabel {
  width: 27px;
  height: 10px;
  text-align: center;
  background: #B72319;
  border-radius: 2px;
  display: none;
  font-size: 6px;
  color: #ffffff;
  float: right;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  position: absolute;
  top: 5px;
  right: 10px;
}
#allindiatest .test-wrapper .card > h4 {
  font-size: 14px;
  padding: 0;
}
#allindiatest .test-wrapper .card .btn-wrappers {
  text-align: center;
}
#allindiatest .test-wrapper .card .btn-wrappers.startTest,
#allindiatest .test-wrapper .card .btn-wrappers.showRank,
#allindiatest .test-wrapper .card .btn-wrappers.registered {
  display: none;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-test {
  background: linear-gradient(271.43deg, #30C465 23.2%, #3AE878 81.72%);
  color: #ffffff;
  width: 130px;
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-ranks {
  background: linear-gradient(271.43deg, #30C465 23.2%, #3AE878 81.72%);
  color: #ffffff;
  width: 130px;
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  background: none;
  border: 1px solid #1F419B;
  color: #1F419B;
  margin-bottom: 0.5rem;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-registers {
  background: linear-gradient(271.43deg, #30C465 23.2%, #3AE878 81.72%);
  color: #ffffff;
  width: 130px;
  font-size: 12px;
  font-family: 'Rubik', sans-serif;
  background: rgba(94, 199, 215, 0.6);
  display: flex;
  justify-content: center;
  margin: 0 auto;
}
#allindiatest .test-wrapper .card .btn-wrappers .btn-registers i {
  font-size: 16px;
  margin-right: 5px;
}
.ml-8 {
  margin-left: -8px;
}
/* STORE 1 */
.or-text {
  width: 200px;
  border-bottom: 1px solid #dee2e6;
  line-height: 0.1rem;
  font-size: 20px;
  color: rgba(68, 68, 68, 0.7);
}
.or-text small {
  background: #ffffff;
  padding: 0 15px;
}
.categories1 {
  position: relative;
  z-index: 10;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .categories1 .card-columns {
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .categories1 .card-columns {
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2;
  }
}
.categories1 .nav-pills {
  border-radius: 0.25rem;
  border: none;
  /*li+li {
      border-left: 1px solid #dee2e6;
    }*/
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 .nav-pills {
    overflow-x: auto;
    white-space: nowrap;
    flex-wrap: nowrap;
    overflow-y: hidden;
    border-radius: 0;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 .nav-pills {
    overflow-x: auto;
    white-space: nowrap;
    flex-wrap: nowrap;
    overflow-y: hidden;
    border-radius: 0;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 .nav-pills {
    overflow-x: auto;
    white-space: nowrap;
    flex-wrap: nowrap;
    overflow-y: hidden;
    border-radius: 0;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 .nav-pills li:first-child a {
    padding-left: 0;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 .nav-pills li:first-child a {
    padding-left: 0;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 .nav-pills li:first-child a {
    padding-left: 0;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 .nav-pills li:last-child a {
    border: none;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 .nav-pills li:last-child a {
    border: none;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 .nav-pills li:last-child a {
    border: none;
  }
}
.categories1 a.nav-link {
  display: flex;
  align-items: center;
  border: none;
  position: relative;
  background-color: #cbeef1;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 a.nav-link {
    padding: 0 10px;
    margin-bottom: 10px;
    border-right: 1px solid #444;
    border-radius: 0;
    background: none !important;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 a.nav-link {
    padding: 0 10px;
    margin-bottom: 10px;
    border-right: 1px solid #444;
    border-radius: 0;
    background: none !important;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 a.nav-link {
    padding: 0 10px;
    margin-bottom: 10px;
    border-right: 1px solid #444;
    border-radius: 0;
    background: none !important;
  }
}
.categories1 a.nav-link:hover {
  color: #1F419B !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 a.nav-link:hover {
    border-color: #444;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 a.nav-link:hover {
    border-color: #444;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 a.nav-link:hover {
    border-color: #444;
  }
}
.categories1 a.nav-link.active:hover {
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 a.nav-link.active:hover {
    color: #1F419B !important;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 a.nav-link.active:hover {
    color: #1F419B !important;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 a.nav-link.active:hover {
    color: #1F419B !important;
  }
}
.categories1 a.nav-link:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #1F419B;
  opacity: 0;
  visibility: hidden;
  transform: translateY(0);
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -ms-transform: translateY(0);
  z-index: -1;
  transition: all 0.1s linear;
  -webkit-transition: all 0.1s linear;
  -moz-transition: all 0.1s linear;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 a.nav-link:after {
    display: none;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 a.nav-link:after {
    display: none;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 a.nav-link:after {
    display: none;
  }
}
.categories1 a.nav-link span {
  margin-left: 0.7rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 a.nav-link span {
    margin-left: 0.5rem;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 a.nav-link span {
    margin-left: 0.5rem;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 a.nav-link span {
    margin-left: 0.5rem;
  }
}
.categories1 .nav-pills a.nav-link.active,
.categories1 .nav-pills a.nav-link:focus {
  background-color: #1F419B !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 .nav-pills a.nav-link.active,
  .categories1 .nav-pills a.nav-link:focus {
    background-color: transparent !important;
    color: #1F419B !important;
    border-color: #444 !important;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .categories1 .nav-pills a.nav-link.active,
  .categories1 .nav-pills a.nav-link:focus {
    background-color: transparent !important;
    color: #1F419B !important;
    border-color: #444 !important;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .categories1 .nav-pills a.nav-link.active,
  .categories1 .nav-pills a.nav-link:focus {
    background-color: transparent !important;
    color: #1F419B !important;
    border-color: #444 !important;
  }
}
.categories1 .nav-pills a.nav-link.active:after,
.categories1 .nav-pills a.nav-link:focus:after {
  opacity: 1;
  visibility: visible;
  transform: translateY(9px);
  -webkit-transform: translateY(9px);
  -moz-transform: translateY(9px);
  -ms-transform: translateY(9px);
}
.categories1 .card .card-header {
  border-bottom: none;
  background-color: rgba(0, 0, 0, 0.08);
}
.categories1 .card .card-body {
  color: #dddddd;
  position: relative;
}
.categories1 .card .card-body a {
  white-space: normal;
  color: #ffffff;
  display: inline-block;
  /*border: 1px solid;
        border-radius: 50px;
        margin: 0 0 5px;*/
}
.categories1 .card .card-body a:hover,
.categories1 .card .card-body a:focus {
  /*text-decoration: none;
          background-color: #ffffff !important;
          color: @ws-lightOrange;*/
}
.categories1 .card .card-body a:first-child {
  padding-left: 0;
}
.categories1 .card .card-body .divider {
  padding: 0 8px;
}
.categories1 .card .card-body .divider:last-child {
  display: none;
}
.categories1 .card #goback-btn1,
.categories1 .card #goback-btn2 {
  position: absolute;
  left: 12px;
  top: 12px;
  padding: 1px 8px;
  display: none;
  font-weight: normal;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 .card #goback-btn1 span,
  .categories1 .card #goback-btn2 span {
    display: none;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .categories1 .card #goback-btn1 span,
  .categories1 .card #goback-btn2 span {
    display: none;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .categories1 .card #goback-btn1 span,
  .categories1 .card #goback-btn2 span {
    display: none;
  }
}
.categories1 #allSyllabus,
.categories1 #allStates {
  position: relative;
  /*left: 15px;
     right: 15px;*/
  transform-origin: left;
  transform: scale(1);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  opacity: 1;
}
.categories1 #schoolLevel,
.categories1 #stateExams {
  position: absolute;
  left: 15px;
  top: 20px;
  right: 15px;
  bottom: 15px;
  transform: translateY(100%);
  -webkit-transform: translateY(100%);
  -moz-transform: translateY(100%);
  -ms-transform: translateY(100%);
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s linear;
  -ms-transition: all 0.3s linear;
  opacity: 0;
  /*@media @iPhone {
      position: relative;
    }*/
}
.categories1 .show_exams {
  order: 1;
}
.categories1 .state-job-exams {
  order: 999;
  flex: 0 0 100%;
  max-width: 100%;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 .state-job-exams .card-header {
    padding-left: 45px;
  }
}
.categories1 #pills-school .card-body {
  min-height: 200px;
  overflow: hidden;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 #pills-school .card-body {
    min-height: 300px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 #pills-school .card-header {
    padding-left: 45px;
  }
}
.categories1 #pills-exams .state-job-exams .card-body {
  min-height: 200px;
  overflow: hidden;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .categories1 #pills-exams .state-job-exams .card-body {
    min-height: 300px;
  }
}
/* Print Books Management Page */
.printbooks_management .card {
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s linear;
}
.printbooks_management .card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border-color: #1F419B;
}
/* Publishing Books Page */
.publishing_sales #content-books .form-group {
  padding: 0 15px;
}
.publishing_sales #content-books input {
  width: 100%;
}
.publishing_sales .total_sales strong {
  color: #1F419B;
}
.btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(31, 65, 155, 0.5) !important;
}
/* Statistics */
.statistics .card h4 {
  font-weight: bold;
  color: #1F419B;
}
.statistics .card h6 {
  font-weight: bold;
}
.statistics .card .thumbnail {
  width: 90px;
  height: 90px;
  border-radius: 50px;
  border: 1px solid #1F419B;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .statistics .card .thumbnail {
    border: none;
  }
}
/* Testimonials Slider */
#testimonialSlider .card i {
  font-size: 100px;
  position: absolute;
  left: -10px;
  top: -30px;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  color: rgba(31, 65, 155, 0.3);
  z-index: 1;
}
#testimonialSlider .card .card-body {
  position: relative;
  z-index: 10;
  border-radius: 10px 50px 10px 50px;
  min-height: 160px;
}
#testimonialSlider .card h5.card-title {
  color: #1F419B;
}
#testimonialSlider ul.slick-dots li {
  width: 15px;
  height: 15px;
}
#testimonialSlider ul.slick-dots li button {
  width: 15px;
  height: 15px;
  background-color: rgba(31, 65, 155, 0.2);
  border-radius: 50px;
}
#testimonialSlider ul.slick-dots li button:before {
  display: none;
}
#testimonialSlider ul.slick-dots li.slick-active button {
  background-color: #1F419B;
  border-radius: 50px;
}
#testimonialSlider .slick-arrow {
  width: 35px;
  height: 70px;
}
#testimonialSlider .slick-prev {
  left: -40px;
}
#testimonialSlider .slick-next {
  right: -40px;
}
.store1_topbanner .breadcrumb_area {
  background-color: #e9ecef;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .breadcrumb {
    padding: 0.5rem 1rem;
  }
}
.store1_topbanner .breadcrumb li {
  font-size: 14px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .breadcrumb li {
    font-size: 13px;
  }
}
.store1_topbanner .breadcrumb li a {
  color: #333333;
}
.store1_topbanner .breadcrumb li a:hover {
  color: #000000;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .breadcrumb li a {
    font-size: 13px;
  }
}
.store1_topbanner .breadcrumb li.active {
  font-weight: 500;
  color: #1F419B;
}
.store1_topbanner .breadcrumb .breadcrumb-item + .breadcrumb-item::before {
  content: "";
  background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='%23333' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 14l6-6-6-6'/></svg>");
  background-repeat: no-repeat;
  background-size: 0.5rem;
  height: 8px;
  width: 15px;
}
.store1_topbanner .bg-banner {
  background-color: #2EBAC6;
}
.store1_topbanner .bg-banner h1 {
  font-size: 50px;
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .bg-banner h1 {
    font-size: 24px;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .store1_topbanner .bg-banner h1 {
    font-size: 36px;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .store1_topbanner .bg-banner h1 {
    font-size: 36px;
  }
}
.store1_topbanner .bg-banner h3 {
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .bg-banner h3 {
    font-size: 18px;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .store1_topbanner .bg-banner h3 {
    font-size: 20px;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .store1_topbanner .bg-banner h3 {
    font-size: 20px;
  }
}
.store1_topbanner .publisher_logo {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center bottom;
  height: 250px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .publisher_logo {
    height: 200px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .publisher_logo div {
    width: 100% !important;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .store1_topbanner .publisher_logo div {
    width: 100% !important;
  }
}
.store1_topbanner .publisher_logo h1 {
  color: #E76619;
  font-family: "Times New Roman";
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .publisher_logo h1 {
    font-size: 30px;
  }
}
.store1_topbanner .publisher_logo h2 {
  color: #00FE00;
  font-family: "Times New Roman";
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .publisher_logo h2 {
    font-size: 24px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_topbanner .banner_publisher {
    padding: 0;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait) {
  .store1_topbanner .banner_publisher {
    padding: 0;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .store1_topbanner .banner_publisher {
    padding: 0;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait) {
  .store1_topbanner .banner_publisher {
    padding: 0;
  }
}
.store1_index {
  min-height: auto;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_index h3 {
    font-size: 22px;
  }
}
.store1_index #subjectFilter {
  margin-top: 30px;
  margin-bottom: -63px;
  position: relative;
  z-index: 100;
}
.store1_index #subjectFilter .dropdown {
  width: 200px;
}
.store1_index #subjectFilter .dropdown .btn {
  overflow: hidden;
  text-overflow: ellipsis;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_index #subjectFilter .dropdown {
    width: 115px;
  }
  .store1_index #subjectFilter .dropdown .btn-primary {
    font-size: 12px;
    font-weight: normal;
  }
}
.store1_index #subjectFilter .dropdown-menu.show {
  right: 0px;
  left: auto !important;
  transform: translate(0px) !important;
  margin-top: 30px;
  min-height: auto;
  max-height: 230px;
  overflow-y: auto;
}
.store1_index #subjectFilter .dropdown-menu li a:hover {
  background-color: #1F419B;
}
.store1_index .topSchoolBooks {
  width: 152px;
}
.store1_index .topSchoolBooks .content-wrapper {
  height: auto;
}
.store1_index .topSchoolBooks .content-wrapper h3 {
  width: auto;
  height: auto;
}
@media only screen and (min-width: 992px) {
  .store1_index #content-data-books-ebooks .col-lg-2 {
    flex: 0 0 20%;
    max-width: 20%;
  }
}
@media only screen and (min-width: 992px) {
  .store1_index #content-data-books-printbooks .col-lg-2 {
    flex: 0 0 20%;
    max-width: 20%;
  }
}
.store1_quiz {
  position: relative;
  z-index: 10;
}
.store1_quiz .quiz-wrapper {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_quiz .headerSlider h2 {
    font-size: 24px;
  }
}
.store1_videos {
  position: relative;
  min-height: auto;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_videos {
    min-height: auto;
  }
}
.store1_videos .video-banner {
  top: 0;
  opacity: 0.7;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_videos .video-banner {
    top: -80px;
  }
}
.store1_videos .container {
  position: relative;
  z-index: 10;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_videos .headerSlider h2 {
    font-size: 24px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_videos h3 {
    width: 100%;
    padding-bottom: 0;
    border: none;
    font-size: 22px;
    line-height: normal;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .store1_videos h3 {
    width: 100%;
    padding-bottom: 0;
    border: none;
    font-size: 22px;
    line-height: normal;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .store1_videos h3 {
    width: 100%;
    padding-bottom: 0;
    border: none;
    font-size: 22px;
    line-height: normal;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_videos h3 span {
    padding: 0;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_videos .store1_download_links img.store1_appstore {
    height: 45px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_videos .store1_download_links img.store1_playstore {
    height: 51px;
    margin-left: 0 !important;
  }
}
.store1_banner_content {
  position: relative;
}
.store1_banner_content li {
  position: relative;
  padding-left: 45px;
}
.store1_banner_content li i {
  position: absolute;
  left: 0;
  font-size: 30px;
}
.store1_banner_content li p {
  font-size: 16px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .my_books {
    margin-top: 0;
  }
}
.my_books .nav-tabs .nav-link {
  padding: 0;
  margin-right: 1.5rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .my_books .nav-tabs .nav-link {
    margin-right: 1rem;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .my_books .nav-tabs .nav-link {
    margin-right: 1rem;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .my_books .nav-tabs .nav-link {
    margin-right: 1rem;
  }
}
.my_books .generate img {
  filter: brightness(1.5);
}
.my_books .generate:hover {
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_social_icons h5 {
    width: 100%;
    text-align: center;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .store1_social_icons h5 {
    width: 100%;
    text-align: center;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .store1_social_icons h5 {
    width: 100%;
    text-align: center;
  }
}
.store1_social_icons .social-icons li {
  width: 40px;
  height: 40px;
  margin-right: 7px;
  text-align: center;
}
.store1_social_icons .social-icons li a {
  line-height: 45px;
  font-size: 20px;
  color: #FFF;
  width: 40px;
  height: 40px;
  border: 1px solid transparent;
  border-radius: 50px;
  display: block;
}
.store1_social_icons .social-icons li a:focus {
  outline: 0;
}
.store1_social_icons .social-icons li.facebook-icon a {
  background-color: #4267b2;
  border-color: #4267b2;
}
.store1_social_icons .social-icons li.facebook-icon a:focus {
  background-color: #4267b2 !important;
}
.store1_social_icons .social-icons li.twitter-icon a {
  background-color: #55acee;
  border-color: #55acee;
}
.store1_social_icons .social-icons li.twitter-icon a:focus {
  background-color: #55acee !important;
}
.store1_social_icons .social-icons li.youtube-icon a {
  background-color: #FF0000;
  border-color: #FF0000;
}
.store1_social_icons .social-icons li.youtube-icon a:focus {
  background-color: #FF0000 !important;
}
.store1_social_icons .social-icons li.instagram-icon a {
  background: #f09433;
  background: -moz-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  background: -webkit-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f09433', endColorstr='#bc1888', GradientType=1);
}
.store1_social_icons .social-icons li.instagram-icon a:focus {
  background: #f09433 !important;
  background: -moz-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) !important;
  background: -webkit-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) !important;
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) !important;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f09433', endColorstr='#bc1888', GradientType=1) !important;
}
.store1_social_icons .social-icons li.telegram-icon a {
  background-color: #0088CC;
  border-color: #0088CC;
}
.store1_social_icons .social-icons li.telegram-icon a:focus {
  background-color: #0088CC !important;
}
.store1_index_accordion a.icon_link {
  margin-right: 1rem;
}
.store1_index_accordion a.icon_link:hover {
  text-decoration: none;
}
.store1_index_accordion a.icon_link:focus {
  text-decoration: none;
}
.store1_index_accordion i {
  width: 50px;
  height: 50px;
  background: darkred;
  font-size: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 50px;
}
.store1_index_accordion .youtube_link i {
  background: #e74c3c;
}
.store1_index_accordion .youtube_link i:before {
  margin-left: 0;
}
.store1_index_accordion .web_link i {
  background: #2980b9;
}
.store1_index_accordion .read_materials i {
  background: #27ae60;
}
.store1_index_accordion .card {
  box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1) !important;
  border-radius: 0;
}
.store1_index_accordion .card:hover {
  border-color: #1F419B !important;
}
.store1_index_accordion .card-header {
  background-color: transparent;
}
.store1_index_accordion .card-header a {
  position: relative;
  background-color: #1F419B !important;
  color: #FFF;
}
.store1_index_accordion .card-header a:focus {
  background-color: #1F419B !important;
}
.store1_index_accordion .card-header a:hover {
  text-decoration: none;
}
.store1_index_accordion .card-header a.collapsed {
  background-color: #1F419B !important;
}
.store1_index_accordion .card-header a.collapsed i {
  transform: rotate(0);
  -webkit-transform: rotate(0);
  -moz-transform: rotate(0);
}
.store1_index_accordion .card-header a i {
  width: auto;
  height: auto;
  background-color: transparent;
  color: #FFF;
  position: absolute;
  right: 10px;
  top: 10px;
  margin: 0;
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_index_accordion .card-body {
    justify-content: left !important;
  }
}
.store1_index_accordion .card-body .row {
  align-items: center;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .store1_index_accordion .card-body .row {
    max-width: 33.3333%;
    flex: 0 0 33.3333%;
    -ms-flex: 0 0 33.3333%;
  }
}
.store1_index_accordion .card-body h6 {
  margin-bottom: 0;
  line-height: normal;
  font-weight: normal;
}
.store1_index_accordion .card-body .info {
  width: 180px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .store1_index_accordion .card-body .info {
    width: 240px;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .store1_index_accordion .card-body .info {
    width: 210px;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait) {
  .store1_index_accordion .card-body .info {
    width: 70%;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .store1_index_accordion .card-body .info {
    width: 75%;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait) {
  .store1_index_accordion .card-body .info {
    width: 80%;
  }
}
.store1_index_accordion .card-body .info h6 {
  white-space: normal;
}
.store1_index_accordion .card-body .name_of_chapter {
  background-color: #cbeef1 !important;
  font-weight: 500;
  border-bottom: 2px solid #add;
}
.store1_index_accordion .card-body .name_of_chapter:first-child {
  margin-top: 0 !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .store1_index_accordion .container {
    padding: 0;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait) {
  .store1_index_accordion .container {
    padding: 0;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .store1_index_accordion .container {
    padding: 0;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait) {
  .store1_index_accordion .container {
    padding: 0;
  }
}
.view_syllabus_area {
  position: relative;
}
.view_syllabus_area .btn-outline-info {
  color: #ffffff;
  border-color: #2ebac6 !important;
  background-color: #1399ae !important;
}
.view_syllabus_area .btn-outline-info:hover {
  border-color: #2ebac6 !important;
  background-color: #1399ae !important;
}
.view_syllabus_area .btn-outline-info:active:focus {
  border-color: #2ebac6 !important;
  background-color: #1399ae !important;
  box-shadow: 0 0.2rem 0.8rem rgba(0, 0, 0, 0.15) !important;
}
.view_syllabus_area .btn-outline-info i {
  position: relative;
  top: 7px;
  transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  transition: all 0.3s linear;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s linear;
}
.view_syllabus_area .btn-outline-info i.icon_animated {
  transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : landscape) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : landscape) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : landscape) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
@media only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : landscape) {
  .view_syllabus_area span.justify-content-end {
    justify-content: center !important;
  }
}
#emptyBooks1 table {
  margin-bottom: 15px;
}
@media screen and (max-width: 767px) {
  #emptyBooks1 table {
    overflow-x: auto;
    min-height: 0.01%;
    width: 100%;
    display: block;
  }
}
#emptyBooks1 table tr td {
  padding: 0.5rem;
}
header {
  height: auto;
  width: 100%;
  /*  background:#1F419B ;*/
  display: block;
  align-items: center;
  box-shadow: none;
}
header > div {
  width: 100%;
}
/*.content-Preview{
  position: relative;
  height: calc(100vh - 185px);
  h2{
    font-size: 24px;
    color:#1F419B;
    font-weight: bold;
  }
}*/
.img-wrapper {
  width: 150px;
  position: relative;
}
.img-wrapper img {
  width: 150px;
  height: 200px;
  margin-top: 1rem;
}
.img-wrapper img:hover .zoomImage {
  display: block;
}
.text-wrapper h4 {
  width: 150px;
  font-size: 14px;
  color: #444444;
  margin-top: 0.8rem;
}
.zoomImage {
  display: none;
  position: absolute;
  left: 15rem;
  top: 0;
}
.zoomImage img {
  height: 400px;
  width: 300px;
}
.sageEvidya .ws-header {
  background: #1E4598;
  width: 100%;
}
.sageEvidya .ws-header .navbar-nav .nav-link {
  color: #ffffff;
  font-family: 'Roboto-Regular';
}
.sageEvidya .ws-header .navbar-nav .nav-item.active {
  background: transparent;
}
.evidLandingPage .order-pr {
  display: none !important;
}
.library .generate {
  display: none !important;
}
.library .vidClass {
  display: none !important;
}
.library .vidTest {
  display: none !important;
}
@-webkit-keyframes Dots {
  0% {
    content: "";
  }
  33% {
    content: ".";
  }
  66% {
    content: "..";
  }
  100% {
    content: "...";
  }
}
@keyframes Dots {
  0% {
    content: "";
  }
  33% {
    content: ".";
  }
  66% {
    content: "..";
  }
  100% {
    content: "...";
  }
}
.loading-icon {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background: rgba(68, 68, 68, 0.9);
  z-index: 9999;
  overflow: hidden;
}
.load-wrapper {
  position: absolute;
  top: 35%;
  width: 100%;
}
#book-read-material .tab-content > .tab-pane {
  min-height: calc(100vh - 150px);
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.bookTemplate #notesLoading .content-wrapper {
  height: auto;
}
header {
  width: 100%;
  z-index: 999;
  top: 0;
}
header.normalHeader {
  box-shadow: none;
  position: relative;
}
header.normalHeader .ws-header {
  max-width: 100%;
}
.preview-book-btns {
  display: none;
}
.etexts .book-read-material .pr-back-btn {
  display: none !important;
}
.etexts .read-content .price-wrapper .section-btns {
  display: none !important;
}
.etexts .bookTemplate .menu li a {
  padding: 0.9rem 1rem;
}
.etexts .export-notes .export-study-set {
  display: none !important;
}
.prevnextbtn {
  width: 768px;
  margin-left: 3rem;
}
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
  .prevnextbtn {
    width: 300px !important;
    margin-left: 0;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .ebouquet .bookTemplate .export-notes {
    top: 99px;
  }
  .ebouquet.hasScrolled .bookTemplate .export-notes {
    top: 50px;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
  #chapter-details-tabs {
    width: 25% !important;
  }
  .contentEdit {
    width: 80px !important;
  }
  .bookTemplate .tab-header .contentEdit #notesMenu {
    padding-left: 10px;
  }
}
.eutkarsh.evidya.etexts.ebouquet button#toggle {
  display: none;
}
