#htmlreadingcontent.zaza img{
    height: auto !important;
    width:auto !important;
}
#htmlreadingcontent.zaza ._idGenObjectLayout-1 #_idContainer000 p.ParaOverride-1 .CharOverride-1{
    font-size: inherit !important;
}
#htmlreadingcontent.zaza div._idGenObjectLayout-1A.moveleft{
    /*text-align: left;*/
}

#htmlreadingcontent.zaza div>._idGenObjectLayout-1{
    /*position: absolute;*/
}
#htmlreadingcontent.zaza .Basic-Paragraph.ParaOverride-1.fontInherit > span{
    font-size: inherit;
}
#htmlreadingcontent.zaza .Basic-Paragraph.ParaOverride-1.fontInherit{
    text-align: center;
}
#htmlreadingcontent.zaza #_idContainer010 > .Basic-Paragraph.ParaOverride-1.fontInherit.headerOverride{
text-align: right;
}
#htmlreadingcontent.zaza #_idContainer010 > .Basic-Paragraph.ParaOverride-1.fontInherit.headerOverride > span{
    font-size: 2em;
}
#htmlreadingcontent.zaza #_idContainer006 > .Basic-Paragraph.ParaOverride-1.fontInherit.handbookoverride > span{
    font-size: 2em;
}
#htmlreadingcontent.zaza #_idContainer006 > .Basic-Paragraph.ParaOverride-1.fontInherit.handbookoverride {
    text-align: right;
}
#_idContainer001 > .Basic-Paragraph.ParaOverride-1.fontInherit > .CharOverride-6.bulksales{
    font-weight: bold;
    font-style: normal;
}
#htmlreadingcontent.zaza span.CharOverride-10{
    font-weight: normal;
}
#_idContainer001._idGenObjectStyleOverride-1{
    width: 100%;
}
#_idContainer002._idGenObjectStyleOverride-1{
    width: 100%;
}
#htmlreadingcontent.zaza div>._idGenObjectLayout-1 > div > img{
    display: none;
}
#htmlreadingcontent.zaza div>._idGenObjectLayout-1 > div.figureImage > img{
    display: block;
}
#htmlreadingcontent.zaza div>._idGenObjectLayout-1 > div > img._idGenObjectAttribute-1{
    display: block;
}
#htmlreadingcontent.zaza div>._idGenObjectLayout-1 > div > img._idGenObjectAttribute-2{
    display: block;
}
#htmlreadingcontent.zaza div>._idGenObjectLayout-1 > div > img._idGenObjectAttribute-3{
    display: block;
}
#htmlreadingcontent.zaza div>._idGenObjectLayout-1 > div > img._idGenObjectAttribute-4{
    display: block;
}
#_idContainer002._idGenObjectStyleOverride-1 .Basic-Paragraph.ParaOverride-1.fontInherit .CharOverride-8{
    font-weight: normal;
}
.table-responsives .table-responsive{
    overflow-x: unset;
    min-height: auto;
}
.zaza .title-text{
    margin-top: 2rem;
}
.zaza p.title-no1{
    margin-top: unset !important;
}

.zaza p.part-no.ParaOverride-1{
    text-align: right;
}
.zaza p.part-title.ParaOverride-1{
    text-align: right;
}
.para.ParaOverride-18{
    text-align: right;
}
#htmlreadingcontent.zaza  #_idContainer002._idGenObjectStyleOverride-1 .Basic-Paragraph.ParaOverride-1.fontInherit{
    text-align: center;
}
#htmlreadingcontent.zaza  #_idContainer004{
    width: 100%;
}
#htmlreadingcontent.zaza  ._idGenObjectLayout-1  #_idContainer004{
    width: auto;
}
#htmlreadingcontent.zaza  #_idContainer002 .Basic-Paragraph.ParaOverride-1.fontInherit{
    text-align: left;
}
#htmlreadingcontent.zaza  #_idContainer002 .Basic-Paragraph.ParaOverride-1.fontInherit > span.CharOverride-1{
    font-size: 2em;
}
#htmlreadingcontent.zaza #_idContainer003.Basic-Text-Frame{
    width: 100%;
}
#htmlreadingcontent.zaza #_idContainer003.Basic-Text-Frame .Basic-Paragraph.ParaOverride-1.fontInherit > span.CharOverride-1{
    font-size: 2em;
}
#htmlreadingcontent.zaza #_idContainer001._idGenObjectStyleOverride-1 .Basic-Paragraph.ParaOverride-1.fontInherit > span.CharOverride-3 a{
    text-transform: lowercase;
}
#htmlreadingcontent.zaza #_idContainer003.Basic-Text-Frame p{
  text-align: center;
}
#htmlreadingcontent.zaza div{
    /*-webkit-transform:inherit !important;*/
    /*transform: inherit !important;*/
}
#htmlreadingcontent.zaza #_idContainer003 .Basic-Paragraph.ParaOverride-2 .CharOverride-2{
    font-style: normal;
}
#htmlreadingcontent.zaza #_idContainer003 .Basic-Paragraph.ParaOverride-3 .CharOverride-3{
    font-style: normal;
}
.bib-other span.para_italic._idGenCharOverride-1{
    font-size: 100%;
}
.box1 p.tx{
text-align: center;
}
#htmlreadingcontent.zaza .Basic-Paragraph.ParaOverride-1.fontInherit span.CharOverride-1{
    text-transform: inherit;
}
#htmlreadingcontent.zaza .Basic-Paragraph.ParaOverride-1.fontInherit span.CharOverride-3{
    text-transform: inherit;
    color: inherit;
}
#htmlreadingcontent.zaza .Basic-Paragraph.ParaOverride-1.fontInherit span.CharOverride-5{
    font-weight: normal;
}
#htmlreadingcontent.zaza .Basic-Paragraph.ParaOverride-1.fontInherit span.CharOverride-4{
    font-weight: bold;
}
#htmlreadingcontent.zaza  .para_italic.CharOverride-3{
    text-transform: inherit;
}
#htmlreadingcontent.zaza  span.para_italic._idGenCharOverride-1{
    font-size: 100%;
}
#page_16 .para_italic.CharOverride-2{
    text-transform: inherit;
    font-style: inherit;
    font-weight: normal;
    font-size: inherit;
}


#htmlreadingcontent.zaza #htmlContent #_idContainer017{
    padding-bottom: 4rem;
}
#htmlreadingcontent.zaza #_idContainer002.Basic-Text-Frame .Basic-Paragraph.ParaOverride-1.fontInherit{
    text-align: center;
}

#htmlreadingcontent.zaza span.fontchange_RupeeForadian {
    font-family: "Rupee Foradian", sans-serif !important;
    color: #333 !important;
    font-size: 12px;
    padding-right: 2px;
}
#htmlreadingcontent.zaza div.bgimage {
    position: inherit;
    height: 600px;
}
#htmlContent div.box{
    background: #d1d3d4;
}
#htmlContent span.cn{
    background: #696969;
}
#htmlContent h2.pt {
    padding-top: 3.7em !important;
    padding-bottom: 0em !important;
    padding-left: 3em !important;
    padding-right: 3em !important;
}
#htmlContent h2.cst{
    font-family: inherit;
}
#htmlContent h2.ct{
    font-family: inherit;
}

.zaza #htmlContent p.copyright img {
    width: 30% !important;
}
.zaza #htmlContent #rtab4_1 a {
    font-weight: bold;
}
.zaza #htmlContent h2.prect,
.zaza #htmlContent h2.prect1,
.zaza #htmlContent h2.cn {
    padding-top: 5rem !important;
}
.zaza #htmlContent p.copyright3 {
    margin-bottom: 5rem;
    padding-bottom: 5rem;
}
