.fade.in {
    opacity: 1;
}
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}
.modal {
    background: rgba(0, 0, 0, 0.8);
}
.test-gen-modal {
    z-index: 9999;
}
.modal {
    display: none;
    overflow: hidden;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    -webkit-overflow-scrolling: touch;
    outline: 0;
}
/*.fade {*/
    /*opacity: 0;*/
    /*-webkit-transition: opacity 0.15s linear;*/
    /*-o-transition: opacity 0.15s linear;*/
    /*transition: opacity 0.15s linear;*/
/*}*/
.modal.in .modal-dialog {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
    margin-top: 3rem;
}

@media (min-width: 992px) {
    .modal-lg {
        width: 900px;
    }
}
@media (min-width: 768px) {
    .modal-dialog {
        width: 600px;
        margin: 30px auto;
    }
}
.modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
     margin: 0 auto;
}
@media (min-width: 768px) {
    .modal-content {
        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }
}
.modal-content {
    position: relative;
    background-color: #ffffff;
    border: 1px solid #999999;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    background-clip: padding-box;
    outline: 0;
}
.test-gen-modal .modal-header, .test-gen-modal .modal-footer {
    position: relative;
    z-index: 2;
}
.modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    min-height: 16.42857143px;
    display:block;
}
.modal .close {
    font-size: 32px;
}
.modal-header .close {
    /*margin-top: -2px;*/
}
.test-gen-modal .modal-title {
    font-size: 22px;
}
.modal-title {
    margin: 0;
    line-height: 1.42857143;
}
.test-gen-modal-body {
    position: relative;
    width: 100%;
    min-height: 750px;
    max-height: 750px;
    background: url('booksmojo/testgen-bg-c8197d6….png');
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 60%;
    overflow: auto;
    overflow-x: hidden;
}
.test-gen-modal-body .on-top-overlay {
    position: relative;
    z-index: 2;
}
.stpe-count {
    font-weight: normal;
    line-height: normal;
    font-size: 18px;
    text-align: center;
    color: #444444;
}

.test-gen-modal .test-gen-books ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.test-gen-books-flex {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    -webkit-box-pack: center;
    justify-content: flex-start;
    margin-right: 0;
    margin-left: 0;
    margin-top: 32px;
}
.book-content-wrapper {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: 0;
    margin-bottom: 56px;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-item-test-gen {
    height: auto;
    background: #fff;
    border: 0.5px solid rgba(189, 189, 189, 0.55);
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
    border-radius: 6px;
}
.book-item {
    cursor: pointer;
    position: relative;
}
.book-item-link {
    width: 100%;
    text-decoration: none;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-image-wrapper {
    height: 240px;
    position: relative;
}
.book-image {
    display: block;
    width: 100%;
    max-width: 100%;
    height: 100%;
    border: 1px solid rgba(189, 189, 189, 0.3);
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book .book-selected {
    width: 60px;
    height: 60px;
    color: #fff;
    text-align: center;
    font-size: 32px;
    background: #F05A2A;
    margin: auto;
    border-radius: 100px;
    position: absolute;
    right: 0;
    left: 0;
    margin: auto;
    top: 0;
    bottom: 0;
    padding: 8px;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-info {
    height: auto;
}
.book-info {
    height: 102px;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-info .book-name-author {
    height: auto;
}
.book-info .book-name-author {
    height: 56px;
    margin-bottom: 5px;
}
.book-info .book-name {
    font-weight: 500;
    line-height: 19px;
    letter-spacing: 0.01em;
}
.test-gen-modal-body .on-top-overlay {
    position: relative;
    z-index: 2;
}
.test-type-container {
    max-width: 492px;
}
.test-gen-modal-body .overlay {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.96);
    z-index: 1;
}
.test-gen-modal .modal-header, .test-gen-modal .modal-footer {
    position: relative;
    z-index: 2;
}
.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}
.previous-btn {
    color: rgba(68, 68, 68, 0.74);
    border: 0;
}
#error_message {
    max-width: 500px;
    font-style: normal;
    font-weight: normal;
    line-height: normal;
    font-size: 18px;
    text-align: left;
    color: #DF4B41;
    padding: 0 8px;
}
.next-btn {
    width: 20%;
    height: auto;
    font-size: 14px;
    color: #fff;
    background: #028EDB;
    padding: 8px;
    border: 0;
}
.next-btn {
    float: right;
    width: 173px;
    height: 48px;
    color: #fff;
    font-size: 22px;
    background-color: #5EC7D7;
    padding: 9px 12px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
}
.quizTitle {
    display: inline-block;
    font-size: 26px;
    text-align: center;
}
@media screen and (max-width: 1536px) and (min-width: 768px) {
    .quiz-modal-body {
        min-height: 525px;
        max-height: 525px;
    }
}

.quiz-modal-body {
    width: 100%;
    min-height: 750px;
    max-height: 750px;
    font-family: 'Work Sans', sans-serif;
    font-size: 16px;
    padding: 15px 15px 0;
    overflow: auto;
}
@media screen and (max-width: 1536px) and (min-width: 768px) {
    .quiz-modal-body {
        min-height: 525px;
        max-height: 525px;
    }
}
.quiz-modal-body {
    min-height: 750px;
    max-height: 750px;
    font-family: 'Work Sans', sans-serif;
    font-size: 16px;
    padding: 15px 15px 0;
    overflow: auto;
}
.modal-body {
    position: relative;
    padding: 15px;
}
.modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}
.quiz-modal-body .container-fluid {
    margin: 0 !important;
}
.questionumber-containte {
    position: fixed;
}
@media screen and (max-width: 1536px) and (min-width: 768px) {
    .questionumber-containte {
        width: 30%;
        max-height: 500px;
        overflow: auto;
    }
}
#questionumber-containter {
    max-height: 734px;
    min-height: 350px;
    overflow: auto;
}
#questionumber-containter ul a:hover{
    text-decoration: none;
}
.question-number-help-wrapper {
    list-style: none;
    text-align: right;
    padding-right: 16px;
    padding-left: 16px;
}
.current-question {
    display: inline-block;
    width: 40px;
    height: 40px;
    text-align: center;
    color: #fff;
    font-weight: bold;
    background: #2EBAC6;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px solid #2EBAC6;
    border-radius: 8px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
}
.current-question {
    display: inline-block;
    width: 40px;
    height: 40px;
    text-align: center;
    color: #fff;
    font-weight: bold;
    background-color: #F05A2A;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px solid #F05A2A;
    border-radius: 8px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
}
.mcq-question-div {
    position: relative;
    overflow: auto;
}
.mcq-question-div {
    font-family: "Work Sans", sans-serif;
}
.mcq-question-div {
    position: relative;
    overflow: auto;
}
.mcq-question-div {
    position: absolute !important;
    right: 0;
}
.practice-question {
    /*margin-bottom: 40px;*/
}
.question-string {
    display: inline-block;
}
.question-row-bordered {
    padding: 16px 16px 0 16px;
}
.question-div {
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(68, 68, 68, 0.54);
}
.radio, .checkbox {
    position: relative;
    display: block;
    margin-top: 10px;
    margin-bottom: 10px;
}
.radio label, .checkbox label {
    min-height: 20px;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
}
.full-wdth-label {
    width: 100%;
}
.mcq-question-div input[type=radio] {
    position: absolute;
    opacity: 0;
}
.radio input[type="radio"], .radio-inline input[type="radio"], .checkbox input[type="checkbox"], .checkbox-inline input[type="checkbox"] {
    position: absolute;
    margin-left: -20px;
    margin-top: 4px \9;
}
input[type="radio"], input[type="checkbox"] {
    margin: 4px 0 0;
    margin-top: 1px \9;
    line-height: normal;
}
input[type="checkbox"], input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}
input, button, select, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}
.mcq-question-div input[type="radio"] ~ .checkmark {
    color: #fff;
    font-size: 12px;
    text-align: center;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 50px;
}
.mcq-question-div .checkmark-radio {
    position: absolute;
    top: 50%;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 50px !important;
    transform: translateY(-50%);
}
.mcq-question-div .checkmark {
    position: absolute;
    top: 20px;
    right: 40px;
    height: 22px;
    width: 22px;
    background-color: transparent;
    border: 2px solid #888;
}
.checkmark-radio {
    position: absolute;
    top: 20px;
    right: 40px;
    height: 22px;
    width: 22px;
    background-color: transparent;
    border: 2px solid #888;
    border-radius: 50px !important;
}
.checkmark {
    position: absolute;
    top: 0;
    right: 8px;
    height: 18px;
    width: 18px;
    background-color: transparent;
    border: 2px solid #888;
    border-radius: 4px;
}
.greytext {
    color: #444444;
}
.radio label, .checkbox label {
    min-height: 20px;
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
}
.question-row-bordered {
    padding: 16px 16px 0 16px;
}
.question-number-help-wrapper {
    list-style: none;
    text-align: right;
    padding-right: 16px;
    padding-left: 16px;
}
.current-question {
    display: inline-block;
    width: 40px;
    height: 40px;
    text-align: center;
    color: #fff;
    font-weight: bold;
    background: #2EBAC6;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px solid #2EBAC6;
    border-radius: 8px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
}
.grey-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #888;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
}
.dropup, .dropdown {
    position: relative;
}
.question-number-help-wrapper > li > a {
    cursor: pointer;
}
.question-number-help-dropdown {
    width: 100%;
    left: auto;
    right: -15px;
    padding: 18px 18px 0 18px;
    border-top-right-radius: 4px !important;
    border-top-left-radius: 4px !important;
    background: #FFFFFF;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    overflow-wrap: break-word;
}
.question-number-help-dropdown-list-item {
    margin-bottom: 16px;
}
.question-number-help-dropdown .answered-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #2F80ED;
    border-radius: 8px;
}
.question-number-help-label {
    display: inline-block;
    vertical-align: top;
    padding: 8px 0 0 6px;
    font-size: 16px;
}
.submit-quiz-btn {
    background: #FFFFFF;
    border: 0.5px solid rgba(68, 68, 68, 0.2);
    box-sizing: border-box;
    border-radius: 2px;
    width: 168px;
    color: #028EDB;
    margin: 0 auto;
    float: none;
}
.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.65;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none;
}
.skipped-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #000;
    background-color: #fff;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px solid #000;
    border-radius: 8px;
}
@media screen and (min-width: 768px) {
    .practice-modal {
        width: 1129px;
    }
}
.question-number-help-dropdown .answered-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #2F80ED;
    border-radius: 8px;
}
.question-container {
    position: relative;
    float: left;
    max-width: 666px;
}
.question-container :before {
     content: '';
     clear: both;
 }


.mcq-question-div input[type=radio] {
    position: absolute;
    opacity: 0;
}
.mcq-question-div input[type=checkbox] {
    position: absolute;
    opacity: 0;
}
.mcq-question-div .checkmark {
    position: absolute;
    top: 20px;
    right: 40px;
    height: 22px;
    width: 22px;
    background-color: transparent;
    border: 2px solid #888;
}
.mcq-question-div .checkmark::after {
    left: 3px;
    top: 4px;
    width: 12px;
    height: 7px;
    border: solid #F05A2A;
    border-width: 3px 3px 0 0;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
}
.mcq-question-div .checkmark-radio {
    position: absolute;
    top: 50%;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 50px !important;
    transform: translateY(-50%);
}
.mcq-question-div input[type="radio"] ~ .checkmark {
    color: #fff;
    font-size: 12px;
    text-align: center;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 50px;
}
.mcq-question-div input[type="radio"]:checked ~ .checkmark {
    background-color: transparent;
    border: 2px solid #F05A2A;
    border-radius: 50px;
}
.mcq-question-div input[type="checkbox"] ~ .checkmark {
    color: #fff;
    font-size: 14px;
    text-align: center;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 0 !important;
}
.mcq-question-div .user-input::after {
    content: '';
    position: absolute;
    display: none;
}
.mcq-question-div input[type="radio"]:checked ~ .checkmark::after {
    content: '';
    position: absolute;
    display: block;
    z-index: -1;
}
.mcq-question-div .checkmark-checkbox::after {
    border-radius: 0;
}
.mcq-question-div .checkmark-radio::after {
    left: 0;
    top: 0;
    width: 5px;
    height: 5px;
    border: 10px solid #F05A2A;
    border-radius: 10px;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
}
.mcq-question-div input[type="checkbox"]:checked ~ .checkmark::after {
    content: '';
    position: absolute;
    display: block;
    z-index: -1;
}
.mcq-question-div .checkmark-checkbox::after {
    left: 0;
    top: 0;
    width: 5px;
    height: 5px;
    border: 10px solid #F05A2A;
    border-radius: 0;
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
}
.mcq-question-div .question-number-help-wrapper {
    list-style: none;
    text-align: right;
    padding-right: 16px;
    padding-left: 16px;
}
.mcq-question-div .question-number-help-wrapper > li > a {
    cursor: pointer;
}
.mcq-question-div .question-number-help-wrapper > li > a:hover {
    color: #000;
}

.question-number-help-dropdown {
    width: 100%;
    left: auto;
    right: -15px;
    padding: 18px 18px 0 18px;
    border-top-right-radius: 4px !important;
    border-top-left-radius: 4px !important;
    background: #fff;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    overflow-wrap: break-word;
}
.question-number-help-dropdown:before {
     content: "";
     border-bottom: 10px solid #fff;
     border-right: 10px solid transparent;
     border-left: 10px solid transparent;
     position: absolute;
     top: -10px;
     right: 16px;
     z-index: 10;
 }
.question-number-help-dropdown:after {
     content: "";
     border-bottom: 12px solid #ccc;
     border-right: 12px solid transparent;
     border-left: 12px solid transparent;
     position: absolute;
     top: -12px;
     right: 14px;
     z-index: 9;
 }
.answered-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #2F80ED;
    border-radius: 8px;
}
.skipped-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    border: 1px solid #444444;
    border-radius: 8px;
}
.review-marked-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #A056E5;
    border-radius: 8px;
}
.review-marked-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #A056E5;
    border-radius: 8px;
}
.not-seen-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #888888;
    border-radius: 8px;
}
.current-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #2EBAC6;
    border-radius: 8px;
}
.question-number-help-dropdown-list-item {
    margin-bottom: 16px;
}
.question-number-help-label {
    display: inline-block;
    vertical-align: top;
    padding: 8px 0 0 6px;
    font-size: 16px;
}
.dropdown-toggle::after{
    display:none;
}
.question-number-help-dropdown .skipped-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    border: 1px solid #444444;
    border-radius: 8px;
}
.question-number-help-label {
    display: inline-block;
    vertical-align: top;
    padding: 8px 0 0 6px;
    font-size: 16px;
}
.question-number-help-dropdown .review-marked-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #A056E5;
    border-radius: 8px;
}
.question-number-help-dropdown .not-seen-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #888888;
    border-radius: 8px;
}
.question-number-help-dropdown .current-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #2EBAC6;
    border-radius: 8px;
}
.question-number-help-dropdown:after {
    content: "";
    border-bottom: 12px solid #ccc;
    border-right: 12px solid transparent;
    border-left: 12px solid transparent;
    position: absolute;
    top: -12px;
    right: 14px;
    z-index: 9;
}
.open > .dropdown-menu {
    display: block;
}
.question-number-help-dropdown {
    width: 100%;
    left: auto;
    right: -15px;
    padding: 18px 18px 0 18px;
    border-top-right-radius: 4px !important;
    border-top-left-radius: 4px !important;
    background: #FFFFFF;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    overflow-wrap: break-word;
}
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 183px;
    padding: 5px 5px;
    margin: 2px 0 0;
    list-style: none;
    font-size: 14px;
    text-align: left;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    background-clip: padding-box;
}
.question-number-help-dropdown:before {
    content: "";
    border-bottom: 10px solid #fff;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    position: absolute;
    top: -10px;
    right: 16px;
    z-index: 10;
}
@media screen and (max-width: 1536px) and (min-width: 768px) {
    .quiz-modal-body {
        min-height: 525px;
        max-height: 525px;
    }
}
    .quiz-modal-body {
        width: 100%;
        min-height:400px;
        max-height: 750px;
        font-family: 'Work Sans', sans-serif;
        font-size: 16px;
        padding: 15px 15px 0;
        overflow: auto;
    }

@media (min-width: 1200px) {
    .hidden-lg {
        display: none !important;
    }
}
.questionumber-containte{
    position: fixed;
}
@media screen and (max-width: 1536px) and (min-width: 768px) {
    .questionumber-containte {
        width: 30%;
        max-height: 500px;
        overflow: auto;
    }
}
.answered-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #2F80ED;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
}
.submit-quiz-btn {
    background: #FFFFFF;
    border: 0.5px solid rgba(68, 68, 68, 0.2);
    box-sizing: border-box;
    border-radius: 2px;
    width: 168px;
    color: #028EDB;
    margin: 0 auto;
    float: none;
}
.next-btn {
    width: 20%;
    height: auto;
    font-size: 14px;
    color: #fff;
    background: #028EDB;
    padding: 8px;
    border: 0;
}

.practice-question img {
    max-width: 100% !important; }

.correct-question-answer {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #46B520;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }

.wrong-question-answer {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #B72319;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }

.grey-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #888;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }
.grey-question:hover {
    color: #fff;
    text-decoration: none; }

.skipped-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #000;
    background-color: #fff;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px solid #000;
    border-radius: 8px; }
.skipped-question:hover {
    text-decoration: none; }

.review-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #A056E5;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }
.review-question:hover {
    color: #fff;
    text-decoration: none; }

.answered-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #2F80ED;
    padding: 8px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }
.answered-question:hover {
    color: #fff;
    text-decoration: none; }

.current-question {
    display: inline-block;
    width: 40px;
    height: 40px;
    text-align: center;
    color: #fff;
    font-weight: bold;
    background: #2EBAC6;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border: 1px solid #2EBAC6;
    border-radius: 8px;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25); }
.current-question:hover {
    color: #fff;
    text-decoration: none; }

.mcq-question-div {
    position: relative;
    overflow: auto; }
.mcq-question-div::before {
    content: '';
    position: absolute;
    left: 0;
    height: 100%;
    border-left: 1px solid rgba(189, 189, 189, 0.55); }

.quiz-modal-body {
    width: 100%;
    min-height: 750px;
    max-height: 750px;
    font-family: 'Work Sans', sans-serif;
    font-size: 16px;
    padding: 15px 15px 0;
    overflow: auto; }
.quiz-modal-body .container-fluid {
    margin: 0 !important; }
.quiz-modal-body .modal-footer {
    padding-left: 0; }
.quiz-modal-body .modal-footer .previous-btn {
    padding-left: 0; }

.score-container {
    display: none;
    width: 100%;
    min-height: 525px;
    max-height: 525px;
    min-height: 750px;
    max-height: 750px;
    overflow: auto; }

.practice-score-container {
    min-height: 300px;
    color: #fff;
    text-align: center;
    background: linear-gradient(74.18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
    background: -webkit-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
    background: -o-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
    background: linear-gradient(18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
    padding: 26px 0; }
.practice-score-container .practice-score {
    width: 274px;
    margin: 0 auto; }
.practice-score-container .practice-score .medal-picture {
    width: 135px;
    height: 138px;
    margin: 0 auto; }
.practice-score-container .practice-score .medal-picture img {
    width: 100%; }
.practice-score-container .practice-score .practice-score-string p {
    color: #fff;
    font-size: 24px;
    font-weight: 600;
    margin: 0; }
.practice-score-container .practice-score .practice-score-string .practice-score-string-score {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px; }

.answer-summary {
    min-height: 215px;
    padding: 24px 0;
    text-align: center;
    background: #F8F8F8; }
.answer-summary p {
    margin: 0; }
.answer-summary .summary-heading {
    font-size: 22px; }
.answer-summary .short-heading {
    font-size: 16px; }
.answer-summary .score-summary {
    max-width: 634px;
    margin: 0 auto;
    margin-top: 16px; }
.answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
    width: 100px;
    height: 100px;
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(43.98deg, #2A7E0D 14.96%, #4DEB17 100%);
    padding: 34px 35px 36px 34px;
    margin: 0 auto;
    margin-top: 8px;
    border-radius: 16px; }
.answer-summary .score-summary .wrong-answers {
    background: linear-gradient(45deg, #97160D 20.42%, #F76E64 100%); }
.answer-summary .score-summary .skipped-answers {
    color: #444444;
    background: #fff;
    border: 2px solid #444444; }

.accuracy-summary {
    max-width: 634px;
    padding-bottom: 16px;
    margin: 0 auto;
    margin-top: 26px; }
.accuracy-summary .time-taken, .accuracy-summary .answer-accuracy, .accuracy-summary .question-hr {
    display: inline-block;
    width: 56px;
    height: 56px;
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    background: url("../images/booksmojo/clock-mobile.png");
    background-repeat: no-repeat;
    background-size: 100%;
    padding: 34px 0 36px 0;
    border-radius: 500px;
    margin: 0 auto;
    margin-top: 8px; }
.accuracy-summary .answer-accuracy {
    background: url("../images/booksmojo/accuracy-mobile.png");
    background-repeat: no-repeat;
    background-size: 100%; }
.accuracy-summary .question-hr {
    background: url("../images/wonderslate/question-hr.png");
    background-repeat: no-repeat;
    background-size: 100%; }
.accuracy-summary .time-span {
    display: inline-block;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    vertical-align: top;
    margin-top: 16px;
    margin-left: 8px;
    padding-right: 30px;
    border-right: 1px solid rgba(68, 68, 68, 0.2); }
.accuracy-summary .time-span .clearfix {
    font-size: 12px;
    color: rgba(68, 68, 68, 0.54); }

.questions-summary {
    max-width: 712px;
    padding: 16px 50px;
    margin: 0 auto; }
.questions-summary .question-summary-questions .correct-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #46B520;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }
.questions-summary .question-summary-questions .wrong-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #B72319;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }
.questions-summary .question-summary-help-icon {
    display: inline-block;
    width: 25px;
    height: 25px;
    background: url("../images/booksmojo/help-icon.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-indent: -9999999px; }
.questions-summary .question-summary-help {
    position: relative;
    float: right; }
.questions-summary .question-summary-tooltip {
    display: none;
    position: relative;
    right: 0;
    width: 232px;
    list-style: none;
    text-align: left;
    background: #fff;
    padding: 16px;
    margin: 0;
    margin-top: 30px;
    border-radius: 6px;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25); }
.questions-summary .question-summary-tooltip li {
    display: block;
    padding: 10px; }
.questions-summary .question-summary-tooltip li .indicator-div {
    display: inline-block;
    width: 40px;
    height: 40px; }
.questions-summary .question-summary-tooltip li p {
    display: inline-block;
    margin-left: 10px;
    -webkit-transform: translateY(-55%);
    -moz-transform: translateY(-55%);
    -ms-transform: translateY(-55%);
    -o-transform: translateY(-55%);
    transform: translateY(-55%); }
.questions-summary .question-summary-tooltip li .correct {
    background: url("../images/booksmojo/correct.png");
    background-repeat: no-repeat;
    background-size: 100% 100%; }
.questions-summary .question-summary-tooltip li .wrong {
    background: url("../images/booksmojo/wrong.png");
    background-repeat: no-repeat;
    background-size: 100% 100%; }
.questions-summary .question-summary-tooltip li .skipped {
    background: url("../images/booksmojo/skipped.png");
    background-repeat: no-repeat;
    background-size: 100% 100%; }

.btn-review {
    display: block;
    width: 173px;
    height: auto;
    color: #444444;
    font-size: 14px;
    background: #FFFFFF;
    padding: 8px;
    margin: 0 auto;
    box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 4px; }
.btn-review:hover {
    color: #444444 !important; }

.modal {
    overflow: scroll; }

.chapter-details-area {
    height: 100%; }

.answer-holder-inner {
    float: left;
    max-width: 100%; }

.correct-answer-learn {
    margin-top: 40px;
    margin-bottom: 40px;
    border-bottom: 1px solid rgba(68, 68, 68, 0.74); }
.correct-answer-learn .correct-answer-label {
    font-size: 20px;
    font-weight: bold;
    color: #888888; }
.correct-answer-learn .correct-answer-label .correct-answer {
    color: #444444;
    font-size: 18px; }

.correct-answer-by-user {
    color: #46B520 !important;
    font-size: 18px; }

.wrong-answer-by-user {
    color: #B72319 !important;
    font-size: 18px; }

.mcq-learn {
    border-left: 0 !important; }
.mcq-learn::before {
    content: '';
    border-left: 0 !important; }

.show-explanation {
    float: right; }

.correct-answer-explanation {
    display: none;
    background-color: #FCFCFC;
    padding: 24px 40px;
    float: left;
    box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25); }

.modal .close {
    font-size: 32px; }

#quizanalytics {
    z-index: 9999; }

#quizModal .modal-header, #quizanalytics .modal-header {
    min-height: 56px; }

@media screen and (min-width: 768px) and (max-width: 1536px) {
    .quiz-modal-body {
        min-height: 525px;
        max-height: 525px; }

    .score-container {
        min-height: 525px;
        max-height: 525px; } }
@media screen and (max-width: 768px) {
    .submit-quiz-btn {
        width: auto; }

    .mcq-question-div::before {
        content: '';
        border-left: 0; }

    .btn-review {
        width: 100%; }

    .close-modal.next-btn.pull-right {
        width: 100%; }

    .answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
        width: 80px;
        height: 80px;
        padding: 23px 35px 36px 34px; }
    .answer-summary .score-summary .wrong-answers {
        width: 80px;
        height: 80px;
        padding: 23px 35px 36px 34px; }
    .answer-summary .score-summary .skipped-answers {
        width: 80px;
        height: 80px;
        padding: 23px 35px 36px 34px; }

    .next-btn.close-modal {
        width: 100% !important; }

    .learn-practice {
        width: 47%;
        float: left; }
    .learn-practice .learn-practice-card {
        width: 100%;
        height: 200px; }
    .learn-practice .learn-pactice-btns {
        margin-top: 30px; }

    .quiz-modal-body {
        min-height: 500px; } }
.question-div-mcq img {
    height: 200px;
    width: 200px;
    margin-top: 15px; }

.mcqquestion img {
    height: 200px !important;
    width: 200px !important; }

#sum-question img {
    display: block;
    height: 200px !important;
    width: 200px !important;
    margin-top: 15px;
    margin-left: 15px;
    float: none; }

.exercise-score-container {
    min-height: 220px;
    color: #fff;
    text-align: center;
    background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
    background: -webkit-linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
    background: -o-linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
    background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
    padding: 26px 0; }
.exercise-score-container p {
    color: #fff;
    font-size: 18px; }
.exercise-score-container .exercise-text {
    max-width: 85px;
    display: inline-block;
    margin: 0;
    line-height: normal; }
.exercise-score-container .exercise-scores {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
    text-align: center;
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    background-color: transparent;
    margin-bottom: 20px;
    border: 2px solid #fff;
    border-radius: 16px; }
.exercise-score-container .exercise-scores span {
    position: relative;
    top: 50%;
    display: block;
    transform: translateY(-50%); }

.total-question {
    height: 40px;
    padding: 12px;
    margin-bottom: 20px;
    box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25); }
.total-question .total-question-num {
    font-size: 12px;
    color: rgba(68, 68, 68, 0.74); }
.total-question .questions {
    font-size: 12px;
    font-weight: bold;
    color: #F05A2A; }

.take-test {
    width: 270px;
    text-align: center;
    float: none;
    margin: 0 auto;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    left: 0; }
.take-test img {
    width: 50%;
    margin: 0 auto; }

.no-test-text {
    font-size: 14px;
    font-weight: 300;
    color: #888; }

.previous-btn {
    color: rgba(68, 68, 68, 0.74);
    border: 0; }
.previous-btn:hover {
    color: rgba(68, 68, 68, 0.74); }
.previous-btn:active {
    color: rgba(68, 68, 68, 0.74); }
.previous-btn:focus {
    color: rgba(68, 68, 68, 0.74); }

.next-btn {
    width: 20%;
    height: auto;
    font-size: 14px;
    color: #fff;
    background: #028EDB;
    padding: 8px;
    border: 0; }
.next-btn:hover {
    color: #fff; }
.next-btn:active {
    color: #fff; }
.next-btn:focus {
    color: #fff; }

.submit-quiz-btn {
    background: #fff;
    border: 0.5px solid rgba(68, 68, 68, 0.2);
    box-sizing: border-box;
    border-radius: 2px;
    width: 168px;
    color: #028EDB;
    margin: 0 auto;
    float: none; }
.submit-quiz-btn:hover {
    color: #028EDB; }
.submit-quiz-btn:active {
    color: #028EDB; }
.submit-quiz-btn:focus {
    color: #028EDB; }

.done-btn-div {
    text-align: center;
    padding-left: 0; }

.next-btn.pull-right.close-modal {
    width: 30%; }

#quizModal {
    background: rgba(0, 0, 0, 0.8);
    z-index: 9999; }

.analysis-summary {
    float: left;
    width: 100%;
    background: #fff;
    text-align: center;
    padding: 24px 0; }

.row-heading {
    font-size: 22px;
    font-weight: 500; }

.analysis-by-book-detail {
    padding-bottom: 24px;
    margin-top: 16px; }

.collapsed-detail-container {
    text-align: center; }

.collapsed-detail-wrapper {
    list-style-position: inside;
    max-width: 503px;
    text-align: left;
    padding: 0;
    padding-left: 16px;
    margin: 0 auto; }

.collapsed-detail-list-item-chapter {
    display: inline-block;
    font-weight: 300;
    font-size: 16px;
    margin: 0 0 4px; }

.collapsed-detail-list-item-chapter-score {
    color: rgba(68, 68, 68, 0.74);
    font-weight: bold;
    margin-left: 24px; }

.analysis-book-name {
    display: block;
    font-weight: 300;
    font-size: 18px;
    line-height: 26px;
    padding-bottom: 16px; }
.analysis-book-name:hover {
    text-decoration: none;
    color: #444; }
.analysis-book-name:active {
    text-decoration: none;
    color: #444; }
.analysis-book-name:focus {
    text-decoration: none;
    color: #444; }
.analysis-book-name i {
    display: inline-block;
    margin-left: 12px; }
.analysis-book-name i.rotated-i {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg); }
.analysis-book-name i.simple {
    -webkit-transform: rotate(0deg) !important;
    -moz-transform: rotate(0deg) !important;
    -ms-transform: rotate(0deg) !important;
    -o-transform: rotate(0deg) !important;
    transform: rotate(0deg) !important; }

.badge-success, .badge-warning, .badge-danger {
    color: #fff;
    background: linear-gradient(0deg, #46B520, #46B520), #B72319;
    margin-left: 16px;
    border-radius: 8px; }

.badge-warning {
    background: linear-gradient(0deg, #F2C94C, #F2C94C), #B72319; }

.badge-danger {
    background: #B72319; }

.collapse-hr {
    width: 256px;
    margin: 0 auto 16px; }

.suggestions-for-user {
    float: left;
    width: 100%;
    text-align: center;
    background: #fff;
    margin: 24px 0; }

.div-separator {
    float: left;
    width: 100%;
    height: 24px;
    background: #F8F8F8; }

.suggested-topics {
    font-weight: 300;
    text-align: left;
    line-height: 26px;
    font-size: 16px;
    padding: 0 32px; }
.suggested-topics .icon-chevron {
    display: inline-block;
    margin-left: 8px;
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    transform: rotate(270deg); }

.topic-link {
    color: #2EBAC6; }

.depth-details {
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    font-size: 14px;
    color: #888;
    padding-right: 16px; }
.depth-details:hover {
    text-decoration: none; }
.depth-details .icon-chevron {
    display: inline-block;
    margin-left: 4px;
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    transform: rotate(270deg); }

.table-div {
    background: #F8F8F8;
    padding: 16px;
    margin-top: 16px;
    margin-bottom: 16px;
    overflow: auto; }

.detailed-table {
    width: 100%;
    text-align: center; }
.detailed-table tr, .detailed-table td {
    color: #444444;
    text-align: right;
    padding: 14px 0 16px 0; }
.detailed-table tr {
    border-bottom: 1px solid rgba(68, 68, 68, 0.74); }
.detailed-table th.table-chapters {
    text-align: left; }
.detailed-table td.table-chapters-data {
    text-align: left; }
.detailed-table th {
    font-weight: 500;
    color: rgba(68, 68, 68, 0.74);
    text-align: right;
    padding: 14px 0 16px 0; }
.detailed-table .correct {
    color: #46B520; }
.detailed-table .incorrect {
    color: #B72319; }

.expand-table-btn {
    font-size: 12px;
    background: #FFFFFF;
    border: 0.5px solid rgba(68, 68, 68, 0.54);
    padding: 6px;
    box-sizing: border-box;
    border-radius: 4px; }
.expand-table-btn:hover {
    color: #444;
    text-decoration: none; }
.expand-table-btn:active {
    color: #444;
    text-decoration: none; }
.expand-table-btn:focus {
    color: #444;
    text-decoration: none; }

.demo-books {
    padding: 24px 0; }

.demo-book-wrapper {
    background: #fff;
    padding: 8px; }
.demo-book-wrapper a {
    min-height: 320px;
    display: block;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
    padding: 8px; }
.demo-book-wrapper a:hover {
    text-decoration: none; }

.demo-book-name {
    font-weight: 500;
    margin-top: 8px;
    margin-bottom: 0; }

.suggested-books {
    font-weight: 500;
    line-height: 26px;
    font-size: 18px;
    text-align: center; }

.question-help {
    position: absolute;
    right: 24px;
    font-size: 24px; }
.question-help:hover {
    text-decoration: none;
    color: #444; }

.tooltipMenu {
    display: none;
    position: absolute;
    right: 24px;
    z-index: 1; }
.tooltipMenu:hover {
    display: block; }

.bottomSide {
    top: 100%; }

.tooltipMenu::after {
    content: " ";
    position: absolute;
    border-width: 10px;
    border-style: solid; }

.tooltipMenu ul li:hover {
    background: #0d77b6;
    color: white;
    cursor: pointer;
    font-weight: bold; }

.bottomSide.tooltipMenu::after {
    bottom: 100%;
    left: 45%;
    border-color: transparent transparent white transparent; }

#questionumber-containter {
    max-height: 734px;
    min-height: 350px;
    overflow: auto; }

@media screen and (min-width: 768px) and (max-width: 1536px) {
    .test-gen-modal-body {
        width: 100%;
        min-height: 525px;
        max-height: 525px;
        overflow: auto;
        overflow-x: hidden; }

    .answer-summary {
        min-height: 215px; } }
@media screen and (min-width: 768px) {
    .demo-book-wrapper a {
        min-height: 400px; } }
.question-string {
    display: inline-block; }

/*# sourceMappingURL=test.css.map */
.book-reviews {
    float: left;
    width: 100%;
    background: #fff;
    padding: 24px 40px;
    margin: 32px 0 0 0;
    border-radius: 4px;
    border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.review-rating-wrapper {
    padding: 0;
    margin-bottom: 32px; }

.rating-review-heading {
    font-style: normal;
    font-weight: normal;
    line-height: normal;
    letter-spacing: 0.04em; }

.book-overall-rating {
    padding: 0;
    padding-right: 40px; }

.rating-by-user {
    font-weight: 500;
    letter-spacing: 0.01em;
    margin: 0 0 24px 0; }

.rating-bars-wrapper {
    padding: 0 40px;
    border-right: 1px solid rgba(68, 68, 68, 0.2);
    border-left: 1px solid rgba(68, 68, 68, 0.2); }

.user-ratings-holder {
    clear: both;
    float: left;
    width: 100%;
    margin-bottom: 8px; }

.side-rating {
    float: left;
    width: 15%;
    text-align: left; }

.side-rating-right {
    float: left;
    width: 15%;
    text-align: center; }

.rating-progress-bar-wrapper {
    float: left;
    width: 70%;
    margin: 0;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none; }
.rating-progress-bar-wrapper .progress-bar {
    background: linear-gradient(90deg, #EDD38A 0%, #E5C260 100%); }

.user-write-review {
    padding: 0 40px; }

.write-review-label {
    font-weight: 500;
    margin: 0 0 24px; }

.write-review-btn {
    color: #fff;
    letter-spacing: 0.01em;
    padding: 11px 11px;
    border-radius: 4px;
    text-decoration: none; }
.write-review-btn:hover {
    color: #fff;
    text-decoration: none; }
.write-review-btn:active {
    color: #fff;
    text-decoration: none; }
.write-review-btn:focus {
    color: #fff;
    text-decoration: none; }

.user-reviews {
    padding: 24px;
    margin-top: 24px;
    border-top: 1px solid rgba(68, 68, 68, 0.2); }

.review-user-name {
    font-weight: 500;
    text-transform: capitalize; }
.review-user-name .review-date {
    font-weight: 300;
    margin-left: 8px; }

.user-full-review-wrapper {
    margin-bottom: 24px;
    border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.user-full-review {
    display: block !important;
    font-weight: 400;
    letter-spacing: 0.01em;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    margin: 0 0 24px; }
.user-full-review::first-letter {
    text-transform: uppercase; }

/*# sourceMappingURL=test.css.map */

#students #chart_div{
    display: none;
}
#questions #chart_div1{
    display: none;
}
#students .container .row{
    justify-content: center;
}
#students .container .row table{
    width: 100%;
}
#questions  .container .row{
    justify-content: center;
}
#questions  .container .row table{
    width: 100%;
}
#assignmentReview .nav-tabs-wrapper{
   display: flex;
    justify-content: center;
}
#assignmentReview .tab-content{
    margin-top: 0;
}
#assignmentReview .nav-tabs-wrapper li{
    padding: 10px;
}
#assignmentReview .nav-tabs-wrapper li a{
    color:#444444;
}
#assignmentReview .nav-tabs-wrapper li a:hover{
    text-decoration: none;
}
#assignmentReview .nav-tabs-wrapper li a.active{
    color:#F05A2A;
}
#assignmentReview .container-fluid .container .row{
   justify-content: center;
}
.pr-refresh{
    float: left !important;
    color:green;
    padding: 10px;
}
.pr-refresh:hover{
    text-decoration: none;
    color:#F05A2A;
}
.close-pr{
color:red;
    padding: 10px;
}
.close-pr:hover{
    text-decoration: none;
    color:#F05A2A;
}
.dropdown-menu-right {
    right: 0;
    left: auto;
}