.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
#loginOpen .modal-header,
#signup .modal-header,
#forgotPasswordmodal .modal-header {
  display: flex;
  align-items: center;
}
@media (min-width: 576px) {
  #loginOpen .modal-dialog,
  #signup .modal-dialog,
  #forgotPasswordmodal .modal-dialog {
    max-width: 350px;
  }
}
#loginOpen .head-title,
#signup .head-title,
#forgotPasswordmodal .head-title {
  background: -webkit-linear-gradient(#358EF0, #394696);
  background: -moz-linear-gradient(#358EF0, #394696);
  background: -webkit-gradient(#358EF0, #394696);
  background: -o-linear-gradient(#358EF0, #394696);
  background: -ms-linear-gradient(#358EF0, #394696);
  background: linear-gradient(#358EF0, #394696);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-size: 24px;
  font-weight: bold;
}
#loginOpen .modal-body p,
#signup .modal-body p,
#forgotPasswordmodal .modal-body p {
  color: #394696;
  font-size: 14px;
}
#loginOpen .modal-body input,
#signup .modal-body input,
#forgotPasswordmodal .modal-body input {
  color: #394696;
  border: 1px solid #394696;
  border-radius: 5px;
}
#loginOpen .modal-body input:focus,
#signup .modal-body input:focus,
#forgotPasswordmodal .modal-body input:focus {
  box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.08);
}
#loginOpen .modal-body input::placeholder,
#signup .modal-body input::placeholder,
#forgotPasswordmodal .modal-body input::placeholder {
  font-size: 12px;
}
#loginOpen .modal-body .login-btn,
#signup .modal-body .login-btn,
#forgotPasswordmodal .modal-body .login-btn {
  background: radial-gradient(145.92% 3435.71% at -10.22% 0%, #358EF0 0%, #394696 100%);
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  color: #ffffff;
  font-weight: normal;
}
#loginOpen .modal-footer p,
#signup .modal-footer p,
#forgotPasswordmodal .modal-footer p {
  color: #212121;
}
#loginOpen .modal-footer p a,
#signup .modal-footer p a,
#forgotPasswordmodal .modal-footer p a {
  color: #212121;
  font-weight: bold;
}
#loginOpen .modal-content,
#signup .modal-content,
#forgotPasswordmodal .modal-content {
  padding: 5px 10px;
}
.forgot {
  position: absolute;
  top: 10px;
  color: #394696;
  cursor: pointer;
  right: 8px;
  font-size: 12px;
  font-weight: 700;
}
.forgot:hover {
  color: #394696;
}
.error-msg {
  color: #dc3545 !important;
}
.loaders {
  background: #ccc;
  width: 348px;
  height: 4px;
  border-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  position: relative;
  margin-left: -10px;
  margin-top: -5px;
  display: none;
}
.loaders .bar-line {
  background: #394696;
  border-radius: 10px;
  position: absolute;
  left: 0;
  z-index: 1;
  width: 100px;
  height: 4px;
  animation: line-bounce 1s infinite;
}
@keyframes line-bounce {
  0% {
    left: 250px;
  }
  50% {
    left: 0;
  }
  100% {
    left: 250px;
  }
}
#loginOpen form,
#signup form,
#forgotPasswordmodal form {
  text-align: center;
}
#mobilemail-error {
  white-space: nowrap;
  display: block;
  margin-top: 10px;
}
