body {
  font-size: inherit;
  line-height: inherit;
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}
ul {
  margin: 0;
}
#htmlContent .katex .vlist {
  vertical-align: bottom !important;
}
#htmlContent img {
  vertical-align: unset;
}
#htmlContent img.cover {
  width: unset;
  height: unset;
}
#htmlContent ul {
  margin: 0;
}
#htmlContent td{
  border: none;
}
#htmlContent h1,
#htmlContent h2,
#htmlContent h3,
#htmlContent h4,
#htmlContent h5,
#htmlContent p,
#htmlContent span {
  background-color: transparent;
  color: #000;
  font-family: inherit;
  /*font-size: inherit;*/
  word-break: break-word;
  white-space: inherit;
}
p.title-no1{
  margin-top: 0 !important;
}
#htmlContent div {
  width: auto;
  height: auto;
  max-width: 100%;
  margin: 0;
  /*word-spacing: inherit;*/
}
#htmlContent div {
  background-color: transparent;
  color: #000;
}
/*#htmlContent p span{*/

/*}*/
/*p.ParaOverride-1{*/

/*}*/

/*.Basic-Paragraph.ParaOverride-1 span{*/
/*  color:inherit;*/
/*  font-size: inherit;*/
/*}*/
/*._idGenObjectLayout-1 #_idContainer000 .para1.ParaOverride-1 span{*/
/*  font-size: inherit;*/
/*}*/
div._idGenObjectLayout-1 {
  margin: 0;
}
.logo {
  margin: 0;
}
img {
  width: auto;
}
#htmlContent span.math-tex span{
  background-color: transparent;
  color: #000;
  font-family:initial;
}
.front{
  position: static !important;
}

#htmlreadingcontent h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, .h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small, h1 .small, h2 .small, h3 .small, h4 .small, h5 .small, h6 .small, .h1 .small, .h2 .small, .h3 .small, .h4 .small, .h5 .small, .h6 .small
{
  font-size: inherit;
  font-weight: inherit;
  color:#000;

}
p.TOC-head{
  text-align: left !important;
  margin-top: 1rem;
}
.img{
  text-align: inherit;
  position: static !important;
}
.toc,.toc1{
  text-align: inherit;
}

#htmlContent a:hover{
  text-decoration: none !important;
  cursor: pointer;
  color:#444444;
}
#htmlContent div p > a:hover{
  text-decoration: none !important;
  cursor: pointer;
}
a:focus{
  text-decoration: none;
  cursor: pointer;
}

#htmlContent .g1_1{
  display: none;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn i {
  padding-top: 5px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media .media-body p {
  margin-bottom: 15px;
}

