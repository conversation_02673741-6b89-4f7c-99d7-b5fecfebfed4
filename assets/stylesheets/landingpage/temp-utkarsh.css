/*Wonderslate color themes*/
/* NOTES:-> When we are working on utkarsh comment above color themes and use below themes*/
/*eUtkarsh color themes*/
/*Home page*/
.utkarsh .wonderslate-navbar {
  background: #000;
}
.utkarsh .wonderslate-navbar .navbar-brand {
  background: url('../../images/eutkarsh/utkarsh-logo.svg') center no-repeat;
  width: 153px;
  height: 40px;
  background-size: contain ;
}
.utkarsh .wonderslate-navbar ul a {
  color: #ffffff;
}
.utkarsh .wonderslate-navbar ul a:hover {
  color: #ffffff;
  background-color: transparent;
}
.utkarsh .wonderslate-navbar .navbar-container .login-btn {
  color: #ffffff !important;
}
.utkarsh .wonderslate-navbar .navbar-container .signup-btn {
  background: none;
}
.utkarsh .user-logged-in {
  background: transparent !important;
}
.utkarsh .user-orders a {
  color: #444444 !important;
}
.utkarsh .user-logout p a {
  color: #000 !important;
}
.eutkarsh .video-btn > a,
.eutkarsh .link-btn > a,
.eutkarsh .add-notesPr > a {
  background: #000;
}
.eutkarsh .custom-prBtn,
.eutkarsh .custom-prBtn-bl {
  color: #000;
}
.eutkarsh .nav .open > a,
.eutkarsh .nav .open > a:hover,
.eutkarsh .nav .open > a:focus {
  background-color: transparent;
}
.eutkarsh .dropdown-menu li a {
  color: #444444;
}
.eutkarsh .dropdown-menu li a:hover {
  color: #444444;
}
.eutkarsh #addNotes {
  margin-top: 0;
}
.eutkarsh .book-read-material {
  padding-bottom: 8rem;
}
@media screen and (min-width: 320px) and (max-width: 767px) {
  .eutkarsh .download-app-btn {
    max-width: 200px;
    height: auto;
  }
  .eutkarsh .mobile-store {
    width: 28px;
    height: 28px;
    background: url("../../images/eutkarsh/utkarsh-inactive.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .eutkarsh .mobile-store.active {
    background: url("../../images/eutkarsh/utkarsh-active.svg") !important;
    background-repeat: no-repeat !important;
    background-size: 100% 100% !important;
  }
}
.flex_st {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.flex_st .form-control {
  width: auto;
  margin: 5px;
}
.adminForm .form-control {
  width: 33.33%;
}
.adminForm a {
  color: #000;
  padding: 10px;
}
.dflex {
  display: flex;
}
.dflex .form-group {
  width: 33.33%;
}
.dflex .form-group .form-control {
  width: 90%;
}
.dflex .form-group button {
  margin-top: 1rem;
}
.dflex_pub {
  display: flex;
  align-items: center;
}
.dflex_pub .form-control {
  width: 40%;
  margin: 5px;
}
.publish-management a {
  color: #000;
  padding: 10px;
}
