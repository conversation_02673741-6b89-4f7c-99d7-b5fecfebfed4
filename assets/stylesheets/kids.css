/*!
 * Bootstrap Reboot v4.1.2 (https://getbootstrap.com/)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)
 */
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
@-ms-viewport {
  width: device-width;
}
article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}
[tabindex="-1"]:focus {
  outline: 0 !important;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
p {
  margin-top: 0;
  margin-bottom: 1rem;
}
abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
}
address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 700;
}
dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1rem;
}
dfn {
  font-style: italic;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}
a:hover {
  color: #0056b3;
  text-decoration: underline;
}
a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):focus {
  outline: 0;
}
pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}
pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}
figure {
  margin: 0 0 1rem;
}
img {
  vertical-align: middle;
  border-style: none;
}
svg:not(:root) {
  overflow: hidden;
  vertical-align: middle;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}
th {
  text-align: inherit;
}
label {
  display: inline-block;
  margin-bottom: 0.5rem;
}
button {
  border-radius: 0;
}
button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
  cursor: pointer;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
@font-face {
  src: url('../../fonts/Kruti_Dev_010.ttf');
  font-family: "Kruti Dev 010";
}
body {
  background: #C6FFDD;
  /* fallback for old browsers */
  background: -webkit-linear-gradient(to right, #f7797d, #FBD786, #C6FFDD);
  /* Chrome 10-25, Safari 5.1-6 */
  background: linear-gradient(to right, #f7797d, #FBD786, #C6FFDD);
  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  height: 100vh;
}
.language-wrapper {
  background: url('../images/kids/kidsBg.svg') no-repeat center;
  background-size: cover;
  width: 100%;
  height: 100vh;
}
.language-wrapper .jumbotron {
  background: transparent;
  padding-bottom: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .language-wrapper .jumbotron {
    padding-bottom: 2rem;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .language-wrapper .jumbotron#age-content {
    padding-bottom: 2rem;
  }
}
.languages div div,
.age-container div div {
  margin-right: 3rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .languages div div,
  .age-container div div {
    margin-right: 15px;
  }
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .container {
    padding: 0;
  }
}
input[type='radio'],
input[type='checkbox'] {
  display: none;
}
input[type=radio]:checked ~ label {
  border: 4px solid #6C63FF;
  color: #6C63FF;
  font-weight: 500;
}
input[type=checkbox]:checked ~ label {
  border: 4px solid #6C63FF;
  color: #6C63FF;
  font-weight: 500;
}
.language {
  height: 130px;
  width: 130px;
  border-radius: 8px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
  font-family: 'Rubik', sans-serif;
  background: url('../images/kids/language.svg') no-repeat 0% 10%;
  background-size: contain;
  background-color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .language {
    width: 100px;
    height: 100px;
  }
}
h4.header-text {
  font-family: 'Quicksand', sans-serif;
  font-weight: bold;
  font-size: 32px;
  margin-top: 3rem;
  color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  h4.header-text {
    margin-top: 0rem;
    font-size: 26px;
  }
}
.next {
  border-radius: 8px;
  background: #2EBAC6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: #ffffff;
  padding: 1rem 2rem;
  margin-top: 2rem;
  border: 1px solid;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .next {
    min-height: 50px;
    padding: 0.5rem 2rem;
  }
}
.next i {
  font-size: 16px;
  margin-left: 10px;
}
.next:focus {
  outline: 0;
}
.gender-container {
  height: 70%;
}
.gender-container input[type=radio]:checked + label {
  font-weight: bold;
}
.gender-container label {
  height: 300px;
  width: 300px;
  border-radius: 8px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
  font-family: 'Quicksand', sans-serif;
  font-size: 24px;
  background-color: #ffffff;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .gender-container label {
    width: 100px;
    height: 100px;
    margin-right: 10px;
    font-size: 16px;
  }
}
.gender-container label.boy {
  background-image: url('../images/kids/boy.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
.gender-container label.girl {
  background-image: url('../images/kids/girl.svg');
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}
.videos .nav-tabs {
  border: none;
  position: sticky;
  top: 7rem;
  z-index: 999;
}
.videos .nav-tabs li a {
  background: #ffffff;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 8px;
  min-width: 150px;
  min-height: 55px;
  margin-right: 1rem;
  text-align: center;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .videos .nav-tabs li a {
    margin-right: 5px;
    min-width: 90px;
    min-height: 45px;
    font-size: 12px;
  }
}
.videos .nav-tabs li a.active {
  border: 4px solid #6C63FF;
  font-weight: bold;
  color: #6C63FF;
}
.videos .tab-content {
  min-height: 250px;
}
.videos .tab-content .card {
  border-radius: 8px;
}
.videos .tab-content .card img {
  position: relative;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.videos .tab-content .card-body {
  background: url('../images/kids/videoBg.svg') no-repeat 0% 10%;
  background-size: contain;
  padding: 5px 10px;
}
.videos .tab-content .card-body .text-content {
  align-items: center;
  min-height: 60px;
}
.videos .tab-content .card-body .text-content i {
  display: none;
  cursor: pointer;
}
.videos .tab-content .card-body .card-text {
  margin: 0;
}
.videos .tab-content > div > div > div:last-child {
  padding-bottom: 2rem;
}
.play {
  background: none;
  border: none;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 250px;
}
.play i {
  color: #6C63FF;
  background: #ffffff;
  border-radius: 50px;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.play:hover {
  text-decoration: none;
}
#languageDropdown div.mt-4 {
  margin-top: 0 !important;
}
#languageDropdown .language {
  border: none;
  border-radius: 0;
  height: 40px;
  width: auto;
}
#languageDropdown .language div {
  margin: 0 !important;
}
#languageDropdown .language div div {
  margin: 0 !important;
}
#modifyLanguage,
#modifyAge,
#modifyGender {
  text-transform: capitalize;
  max-width: 180px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  min-width: 80px;
  margin-left: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  #modifyLanguage,
  #modifyAge,
  #modifyGender {
    max-width: 85px;
  }
}
.btn-wrapper {
  padding-top: 2rem;
  position: sticky;
  top: 0;
  z-index: 999;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .btn-wrapper {
    padding-top: 3rem;
  }
}
.btn-wrapper .nav-tabs {
  border: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .btn-wrapper .nav-tabs {
    margin-top: 2rem;
  }
}
.btn-wrapper .nav-link {
  display: none;
  padding: 0.5rem;
  background: #ffffff;
  border: 2px solid #6C63FF;
  color: #444444;
  margin-right: 0.5rem;
  outline: 0;
  border-radius: 0px;
  text-align: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .btn-wrapper .nav-link {
    font-size: 14px;
  }
}
.btn-wrapper .nav-link.active {
  background: #6C63FF;
  color: #ffffff;
}
#videoModal .modal-content {
  background: none;
  border: none;
}
#videoModal .modal-header {
  border: none;
}
#videoModal .modal-header .close {
  opacity: 1;
  text-shadow: none;
  color: #F79420;
}
#videoModal .modal-footer {
  border: none;
}
#rhymesContent .col-12 {
  margin-bottom: 1rem;
}
#background-wrap {
  bottom: 0;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: -1;
}
.back-btn {
  background: #fff;
  border-radius: 50px;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 9999;
}
.back-btn:hover {
  text-decoration: none;
}
.footer {
  position: fixed;
  top: 0;
  right: 5px;
  z-index: 999;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .footer {
    top: 8px;
  }
}
.footer a:first-child img {
  width: 150px;
  height: 50px;
  margin-top: 7px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .footer a:first-child img {
    width: 120px;
    height: 44px;
  }
}
.footer a:last-child img {
  width: 150px;
  height: 46px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .footer a:last-child img {
    width: 120px;
    height: 41px;
  }
}
/* KEYFRAMES */
#errorModal .modal-header {
  border: none;
}
#errorModal .modal-footer {
  border: none;
}
#errorModal .modal-body {
  text-align: center;
}
#errorModal .modal-body img {
  width: 100px;
  height: 80px;
}
#errorModal .modal-body p {
  margin-top: 1rem;
  font-family: 'Quicksand', sans-serif;
  color: #c23616;
}
#openApps .modal-body {
  padding: 0;
  text-align: center;
}
#openApps .modal-body h4 {
  font-size: 16px;
  font-family: 'Rubik', sans-serif;
  color: #444444;
}
#openApps .modal-body p {
  font-size: 12px;
  font-family: 'Merriweather', serif;
  color: rgba(68, 68, 68, 0.8);
}
#openApps .get-app {
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  color: #ffffff;
}
#openApps .modal-footer {
  border: none;
}
#openApps .modal-footer a {
  color: rgba(68, 68, 68, 0.8);
  font-size: 12px;
}
#openApps .logoWs {
  background: #ffffff;
  width: 55px;
  height: 55px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: relative;
  top: -22px;
  border-radius: 4px;
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.25);
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .next-wrapper {
    position: fixed;
    bottom: 0;
    background: #FBD786;
    height: 60px;
    display: flex;
    align-items: center;
  }
  .next-wrapper .next {
    margin-top: 0;
    background: #fff;
    color: #6C63FF;
  }
}
.next-wrapper {
  position: static;
}
