html,body{
    margin: 0;
    padding: 0;
}
.bg-image{
    background: url('../../images/winners/winnersBg.png') center center no-repeat;
    width: 100%;
    height: 100vh;
    background-size: cover;
    overflow-x: hidden;
    position: relative;
}
.winnersContainer{
background:radial-gradient(92.3% 92.3% at 91.33% 4.31%, rgba(218, 28, 188, 0.4) 0%, rgba(156, 0, 112, 0.4) 100%), radial-gradient(93.78% 93.78% at 7.47% 6.22%, rgba(242, 57, 47, 0.4) 0%, rgba(162, 15, 8, 0.4) 100%);
height: 100%;
    width: 100%;
}
.winners-content p,h4{
        color:#fff;
    font-family: 'Poppins', sans-serif;
    }
.winners-content h4{
    font-weight: bold;
    font-size: 18px;
}
.winners-content p{

}
.winners-content{
    text-align: center;
}

.shape{
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
}
.email{
    font-style: normal;
    font-weight: normal;
    font-size: 10px;
}
.mobile{
    font-style: normal;
    font-weight: normal;
    font-size: 10px;
}

.phone-2{
    position: absolute;
    z-index: 999;
    left: 65px;
    top: -35px;
}
.phone-1{
    position: relative;
    left: 2rem;
}
.android{
    border-radius: 5px;
    border:1px solid #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    margin: 1rem auto;
    padding: 5px;
}
.android:hover{
    text-decoration: none;
}
.android p span{
    font-style: normal;
    font-weight: normal;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.android p {
    font-size: 18px;
    text-align: center;
    margin-bottom: 0;
    margin-left: 10px;

}
.logo{
    height: 126px;
    width: 126px;
}
.android img{
    width: 28px;
    height: 28px;
}
.footer{
    background: linear-gradient(180deg, rgba(218, 37, 28, 0) 2.94%, #41001E 100%);
    height: 60px;
    position: absolute;
    width: 100%;
    bottom: 0;
}
.footer-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 0.5rem;
}
.footer p,li a{
    color:#fff;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
}
.footer-menu li{
    list-style-type: none;
    color:#fff;
    padding: 0 0.5rem;
}
.footer p{
    margin: 0;
}
.footer-menu li a:hover{
    text-decoration: none;
    color:yellow;
}
.copyrights{
    font-size: 10px !important;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
    .bg-image{
        background: url('../../images/winners/bg-mobile.png') center center no-repeat;
        height: 100%;
    }
    .col-lg-6{
        height: 100% !important;
    }
    .logo{
        height: 126px;
        width: 126px;
    }
    
    .d-flex .position-relative{
        width: 100%;
        margin-top: 2rem;
    }
    .phone-2 {
        position: absolute;
        z-index: 999;
        left: 95px;
        top: -15px;
        height: 430px;

    }
    .winners-content{
     margin-top: 2.5rem;
    }
    .footer{
     position: static;
        height: 80px;

    }

}
