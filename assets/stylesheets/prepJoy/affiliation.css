body {
  margin: 0;
  padding: 0;
  background: #04001D;
}
.navbar {
  background-color: #0F0738;
  padding: 1% 0%;
  margin: 0;
}
.navbar .container {
  width: 85%;
  margin: auto;
}
.login-button button {
  background-color: #E83500;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  margin: 0;
  padding: 6px 16px;
  color: white;
  font-family: Righteous;
}
section.banner.hero-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.banner.hero-img {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: black;
}
@media (max-width: 575px) {
  .container.information-class h1 {
    font-size: 30px;
  }
  .banner-heading.text-center {
    position: unset !important;
    background: white;
    padding: 20px;
  }
  section.banner.hero-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 1;
  }
  #second-banner .banner-heading p {
    color: #a9a9a9;
  }
  h1.text-center {
    font-size: 22px;
  }
  #first-banner .banner-heading.text-center p {
    font-size: 22px;
    color: white;
  }
  section#first-banner .banner-heading.text-center {
    background: #D4C2F7;
  }
  .banner-heading.text-center p {
    padding-bottom: 6% !important;
  }
  #first-banner #joinNow,
  #second-banner #joinNow {
    font-size: 18px;
  }
  #second-banner .banner-heading p {
    font-size: 22px !important;
    color: #a9a9a9;
  }
  #second-banner .banner-heading.text-center {
    top: 36%;
  }
  #first-banner .banner-heading.text-center {
    top: 60% !important;
  }
  .three-card-parent {
    padding: 10% 0%;
    padding-top: 3%;
  }
  .col-12.col-md-4 {
    padding: 5% ;
  }
  .col-12.col-md-3 {
    padding: 6%;
    box-shadow: 0 0 10px #00000040;
    margin-bottom: 5%;
  }
  lottie-player {
    position: absolute;
    top: 0px;
    left: 10% !important;
  }
}
.banner-heading.text-center p {
  font-weight: 600 !important;
  width: 100%;
  display: block;
  font-size: 45px;
  color: white;
  margin: 0;
  padding-bottom: 2%;
  font-family: Righteous;
}
.button-container a {
  cursor: pointer;
  font-size: 13px;
  margin: 0;
  color: white !important;
  font-family: Righteous;
  text-decoration: none;
  display: inline-block;
  letter-spacing: 1px;
  color: #fff;
  padding: 12px 23px;
  border: 1px solid #bebebe;
  border-radius: 23px;
  -webkit-transition: all ease 0.3s;
  transition: all ease 0.3s;
}
.button-container a:hover {
  color: #E83500 !important;
  border: 1px solid #E83500;
}
.img-container img {
  width: 75px;
  padding: 5px;
  border-radius: 50%;
  filter: invert(1);
}
section.affiliates.program {
  padding: 5% 0%;
}
.description-card.text-center h3 {
  color: #ff6600;
}
section.affiliates.program h1 span {
  color: #ff6600;
}
.description-card.text-center p {
  color: white;
  font-size: 15px ;
}
.three-card-parent .img-container,
.four-card .img-container {
  display: flex;
  justify-content: center;
}
h1.text-center {
  color: white;
  font-weight: 600;
  padding-bottom: 4%;
  line-height: normal;
  font-family: Righteous;
}
section.information {
  background: black !important;
  padding: 5% 0%;
}
nav.navbar.m-0 {
  position: sticky;
  top: 0;
  z-index: 100;
  overflow: auto;
  padding: 10px 0px;
  -webkit-transition: all ease 0.3s;
  transition: all ease 0.3s;
}
.container.information-class h4 {
  color: white;
  font-size: 18px;
}
section.information img {
  margin-bottom: 8%;
}
.join-us h2 {
  color: white;
  font-weight: 700;
  font-size: 20px;
  font-family: Righteous;
  background: #0F0638 !important;
  margin: 0;
  padding: 1%;
}
.prepjoy-header__logo.text-center p {
  color: white;
  font-size: 15px;
  font-weight: 600;
}
section.information h1 {
  color: #ff6600;
}
.prepjoy-header__logo.text-center img {
  margin-bottom: 5%;
}
section.prepjoy-footer {
  min-height: 150px;
  background: #0F0638 !important;
}
strong {
  color: #E83500;
}
button.close,
.close:focus,
.close:hover {
  color: #E83500;
  opacity: 1;
  text-shadow: none;
}
#first-banner .banner-heading.text-center {
  top: 70% ;
  color: #E83500 !important;
}
.submit_button button,
div#exampleModalCenter .submit_button a {
  background-color: #E83500;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  padding: 6px 16px;
  color: white;
  font-family: Righteous;
  text-decoration: none;
  width: 26%;
  margin: auto;
  z-index: 100;
}
.submit_button {
  text-align: center;
}
.modal-content {
  background: #0F0738;
}
.modal-header {
  border: none;
}
div#exampleModalCenter .submit_button {
  padding: 4% 0%;
}
.sign-up.form label {
  color: white;
  font-weight: 700;
}
.modal.in {
  top: 25%;
}
lottie-player {
  position: absolute;
  top: 0px;
  left: 25%;
}
div#exampleModalCenter .modal-body p {
  font-size: 20px;
  text-align: center;
  color: white;
  font-weight: 700px;
  font-family: Righteous;
  display: block;
  width: 100%;
}
div#exampleModalCenter .modal-body {
  min-height: 230px;
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
nav.navbar.m-0 .container {
  display: flex;
  align-items: center;
}
nav.navbar.m-0.scrolled {
  -webkit-transition: all ease 0.3s;
  transition: all ease 0.3s;
  animation: 0.5s ease-in-out 0s normal none 1 running fadeInDown;
}
nav.navbar.m-0.scrolled .button-container {
  display: block;
  -webkit-transition: all ease 0.3s;
  transition: all ease 0.3s;
}
@media (max-width: 575px) {
  .navbar .container {
    width: 100%;
  }
  .description-card.text-center h3 {
    font-size: 22px;
  }
  .container.information-class h4 {
    font-size: 18px;
    line-height: 1.5;
  }
  nav.navbar.m-0.scrolled .button-container a {
    font-size: 15px;
  }
}
@media (min-width: 1229px) {
  .modal-dialog.modal-dialog-zoom {
    width: 28%;
  }
}
section#second-banner .banner-heading.text-left p {
  font-weight: 600 !important;
  width: 100%;
  display: block;
  font-size: 35px;
  color: white;
  margin: 0;
  margin-bottom: 25px;
  font-family: Righteous;
}
nav.navbar.m-0.scrolled {
  display: block;
}
.prep-logo img {
  width: 200px !important;
  margin-bottom: 20px;
}
section#second-banner .banner-heading.text-left .button-container a {
  cursor: pointer;
  font-size: 20px;
  margin: 0;
  color: white !important;
  font-family: Righteous;
  text-decoration: none;
  display: inline-block;
  letter-spacing: 1px;
  color: #fff;
  padding: 10px 28px;
  border: 2px solid #bebebe;
  border-radius: 23px;
  -webkit-transition: all ease 0.3s;
  transition: all ease 0.3s;
}
section#second-banner .banner-heading.text-left .button-container a:hover {
  color: #E83500 !important;
  border: 1px solid #E83500;
}
.section-title {
  height: 75px;
  display: block;
  position: relative;
  font-size: 100px;
  line-height: 80px;
  font-family: 'Righteous';
  font-weight: 1000;
  letter-spacing: 3px;
  text-transform: uppercase;
  opacity: 0.15;
}
.section-title-primary {
  font-size: 50px;
  line-height: 25px;
  color: #fff !important;
  text-transform: capitalize;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  opacity: 1;
}
section.affiliates.program h1:first-child {
  padding-bottom: 0;
}
section.affiliates.program h1 {
  position: relative;
}
.container.information-class h1 {
  position: relative;
}
.col-12.col-md-3 {
  background-color: #212121;
  padding: 30px;
  border-radius: 10px;
  margin-right: 10px;
  box-shadow: 0 0 20px #000;
}
@media (max-width: 575px) {
  .banner.hero-img.container.py-5 {
    display: unset;
  }
  .four-card.d-flex {
    display: unset !important;
  }
  .banner-heading.text-left.col-12.col-md-5 {
    padding-top: 20px;
    padding-bottom: 20px;
    text-align: center !important;
  }
  .section-title {
    font-size: 60px;
  }
  .section-title-primary {
    font-size: 35px;
  }
  section.information .section-title-primary {
    top: 45% !important;
  }
}
section.information .section-title-primary {
  top: 35%;
}
.modal-header {
  display: unset !important;
}
div#exampleModalCenter {
  top: unset !important;
}
.three-card-parent .col-12.col-md-4 {
  border: 1px solid #808080bf;
  padding: 5%;
  margin-right: 10px;
  border-radius: 15px;
}
.three-card-parent {
  display: flex;
}
@media (max-width: 575px) {
  .banner.hero-img.py-5.first .banner.hero-img.container.py-5 {
    padding-top: 0 !important;
  }
  .three-card-parent {
    display: unset !important;
  }
  .three-card-parent .col-12.col-md-4 {
    margin-bottom: 12%;
  }
}
.col-12.col-md-4.first-child:before {
  content: "1";
  font-size: 20px;
  color: white;
  background: #ff6600;
  padding: 13px 22px;
  border-radius: 50px;
  position: absolute;
  top: -8%;
  left: 5%;
}
.col-12.col-md-4.second-child:before {
  content: "2";
  font-size: 20px;
  color: white;
  background: #ff6600;
  padding: 13px 22px;
  border-radius: 50px;
  position: absolute;
  top: -8%;
  left: 5%;
}
.col-12.col-md-4.third-child:before {
  content: "3";
  font-size: 20px;
  color: white;
  background: #ff6600;
  padding: 13px 22px;
  border-radius: 50px;
  position: absolute;
  top: -8%;
  left: 5%;
}
.prepjoy-footer__socialIcons ul li i {
  color: #fff;
  font-size: 22px;
}
.form-control:focus {
  border-color: red;
  box-shadow: inset 0 1px 1px rgba(255, 0, 0, 0.2), 0 0 8px rgba(255, 0, 0, 0.6);
}
.hidden {
  display: none !important;
}
.text_effect_wrap {
  overflow: hidden;
  position: relative;
}
.text_effect {
  line-height: 1.5;
  position: relative;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
  display: inline-block;
  -webkit-transition-duration: 1s;
  transition-duration: 1s;
  -webkit-transition-property: -webkit-transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  transition-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.text_effect:before {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  content: "";
  display: block;
  position: absolute;
  -webkit-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transition-delay: 0.4s;
  transition-delay: 0.4s;
  -webkit-transition-duration: 1s;
  transition-duration: 1s;
  -webkit-transition-property: -webkit-transform;
  transition-property: transform, -webkit-transform;
  background-color: #051441;
  -webkit-transition-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
  transition-timing-function: cubic-bezier(0.075, 0.82, 0.165, 1);
}
.text_effect_left {
  -webkit-transform-origin: left;
  transform-origin: left;
  -webkit-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
}
.text_effect_left:before {
  -webkit-transform-origin: right;
  transform-origin: right;
}
.text_effect_wrap.is_show .text_effect {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.text_effect_wrap.is_show .text_effect_left:before {
  -webkit-transform: scale(0, 1);
  -ms-transform: scale(0, 1);
  transform: scale(0, 1);
}
.col-12.col-md-4.first-child:hover:before,
.col-12.col-md-4.second-child:hover:before,
.col-12.col-md-4.third-child:hover:before {
  background: #04001D;
  border: 1px solid #ff6600;
}
#loading-image {
  left: 0 !important;
}
nav.navbar.m-0.scrolled a#joinNow1 {
  color: #E83500 !important;
  border: 1px solid #E83500;
}
nav.navbar.m-0 {
  transform: translateY(-100%);
  transition: all ease 0.3s;
}
nav.navbar.m-0.scrolled {
  transform: translateY(0%);
  transition: all ease 0.3s;
}
.banner.hero-img.py-5.first {
  margin-top: -90px;
}
.modal-header {
  z-index: 99999;
}
@media (max-width: 575px) {
  .submit_button button,
  div#exampleModalCenter .submit_button a {
    width: 35% !important;
  }
}
.sign-up.form p {
  font-family: Righteous;
}
