#datesList {
  margin: 2rem auto 5rem auto;
}
#datesList .swiper-wrapper {
  height: auto !important;
}
#datesList .swiper-wrapper .swiper-slide {
  margin-right: 15px !important;
}
#datesList .swiper-wrapper .swiper-slide .dateCardItem {
  padding: 1rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 10px 0;
  border-radius: 5px;
}
#datesList .swiper-wrapper .swiper-slide .dateCardItem button {
  background: transparent;
  border-radius: 2px;
  padding: 5px;
  width: 75%;
}
.prev strong,
.next strong {
  font-size: 3rem;
  cursor: pointer;
  display: block;
}
.prev {
  margin-left: -15px;
  margin-right: 3px;
}
.next {
  margin-right: -15px;
  margin-left: 3px;
}
#swal2-title {
  color: #fff !important;
  font-size: 18px;
}
.swal2-confirm.swal2-styled {
  width: 100px;
}
.readCAModalModifier {
  background: #0F0839;
}
.mcqOptionModalBody {
  background: #0F0839;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}
.mcqOptionButtons {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.mcqOptionButtons button {
  display: block;
  width: 200px;
  margin: 1rem 0;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: transparent;
  color: #fff;
  padding: 8px;
  border-radius: 2px;
}
.readCAModalWrap p,
h1,
h2,
h3,
h4,
a {
  color: #fff;
}
#prevRead,
#nextRead,
#nextVideo,
#prevVideo {
  background: transparent;
  color: #fff;
  font-size: 14px !important;
  font-weight: 200 !important;
  border: 1px solid rgba(255, 255, 255, 0.4);
  width: 100px;
  padding: 3px;
}
#prevRead:disabled,
#nextRead:disabled,
#nextVideo:disabled,
#prevVideo:disabled {
  opacity: 0.4;
}
.readCAModalWrap p {
  text-align: initial;
}
.langBtn {
  background: none;
  border: none;
}
.langBtn img {
  width: 25px;
}
.leaderboard .filter-wrapper {
  width: 40% !important;
}
.swiper-slide {
  width: 230px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard .filter-wrapper {
    width: 85% !important;
  }
}
.player {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
}
.player .details {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  margin-top: 25px;
}
.player .details .track-art {
  margin: 25px;
  background-size: cover;
  background-position: center;
  border-radius: 15%;
}
.player .details .now-playing,
.player .details .track-artist {
  font-size: 1rem;
  color: #fff;
}
.player .details .track-name {
  font-size: 2rem;
  color: #fff;
}
.player .buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.player .buttons .playpause-track,
.player .buttons .prev-track,
.player .buttons .next-track {
  padding: 25px;
  opacity: 0.8;
  transition: opacity 0.2s;
  color: #E83500;
}
.player .buttons .playpause-track:hover,
.player .buttons .prev-track:hover,
.player .buttons .next-track:hover {
  opacity: 1;
}
.player .slider_container {
  width: 75%;
  max-width: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.player .slider_container .seek_slider,
.player .slider_container .volume_slider {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  height: 5px;
  opacity: 0.7;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
}
.player .slider_container .seek_slider::-webkit-slider-thumb,
.player .slider_container .volume_slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  background: white;
  cursor: pointer;
  border-radius: 50%;
}
.player .slider_container .seek_slider:hover,
.player .slider_container .volume_slider:hover {
  opacity: 1;
}
.player .slider_container .seek_slider {
  width: 60%;
}
.player .slider_container .volume_slider {
  width: 30%;
}
.player .slider_container .current-time,
.player .slider_container .total-duration {
  padding: 10px;
  color: #fff;
}
i.fa-volume-down,
i.fa-volume-up {
  padding: 10px;
}
i.fa-play-circle,
i.fa-pause-circle,
i.fa-step-forward,
i.fa-step-backward {
  cursor: pointer;
}
.readCAContentDiv {
  min-height: auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .readCAContentDiv {
    min-height: auto;
  }
}
.header {
  z-index: 99 !important;
}
.cardModificationOne {
  background: #0F0839;
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #fff !important;
}
.cardModificationTwo {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.2);
  color: #000 !important;
}
.cardModificationTwo button {
  color: #000;
  border: 1px solid rgba(0, 0, 0, 0.2);
}
.cardModificationOne button {
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.mainWrapperSection {
  display: flex;
  flex-direction: column;

}
.mainWrapperSection .titleSection {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;
}
.readCAContentDiv h3 {
  color: #fff !important;
}
