body {
  background: #060029;
}
.col-12.first-row {
  padding-top: 5%;
}
.col-12.second-row {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.col-12.second-row {
  padding-top: 5%;
}
.col-12.second-row h2 {
  font-family: Righteous;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: center;
  color: #e83500;
}
.col-12.second-row h5 {
  font-family: Lato;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
  letter-spacing: -0.015em;
  text-align: center;
  color: #ffffff;
}
.request-btn-wrapper {
  width: 100%;
  text-align: center;
}
.inner-row {
  padding-top: 2%;
}
.inner-row h1 {
  font-size: 25px;
  color: #fff;
  /*text-shadow: 1px 1px #fff, 1px 1px #fff, -1px 0 5px #fff;*/
  text-align: center;
  font-family: 'Righteous';
}
.form-group input {
  background: transparent;
  border-color: rgba(232, 232, 232, 0.07);
  color: white;
}
form.form-group.parent {
  padding-top: 5%;
}
.form-group {
  text-align: center;
}
button.btn.btn-primary {
  background: #e83500;
  border: none;
}
::placeholder {
  font-family: Lato;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 12px;
  letter-spacing: 0em;
  text-align: left;
  padding: 1px;
  height: 25px;
}
.form-control:focus {
  background: transparent;
  border-color: white;
  border-color: rgba(232, 232, 232, 0.07);
  box-shadow: none;
  color: white;
}
.form-control input {
  background: rgba(255, 255, 255, 0.48);
}
span.message-telegram {
  color: white !important;
}
input#phone {
  margin-top: 2%;
}
.form-group input {
  background: rgba(232, 232, 232, 0.07);
}
#sbt-btn:focus {
  box-shadow: none !important;
}
#sbt-btn:active {
  background: #e83500 !important;
}
#success {
  width: 200px;
  height: 200px;
  margin: 0 auto;
}
#success img {
  width: 100%;
  height: 100%;
}
@media (max-width: 575px) {
  .col-12.second-row {
    padding-top: 17%;
  }
  .col-12.first-row {
    padding-top: 15%;
    text-align: center;
  }
}
@media (max-width: 575px) {
  .inner-row {
    width: 100% !important;
    margin: auto;
  }
}
.tel-link {
  color: #0088cc;
  text-decoration: none !important;
}
.inner-row {
  width: 50%;
  margin: auto;
  text-align: center;
}
span#message-telegram {
  width: 100%;
  text-align: center;
  padding: 5px;
}
.loading-icon {
  width: 100%;
  height: 100%;
  background-color: rgba(68, 68, 68, 0.64);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  visibility: hidden;
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  /*background-color: #FFFFFF;*/
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  /*box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);*/
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.container {
  padding-bottom: 5%;
  min-height: 900px;
}
.col-12.first-row img {
  margin-right: 3%;
  margin-left: 2.5%;
}
.col-12.first-row img.ws-logo.d-block.mt-2.mt-md-3 {
  width: 130px;
}
.col-12.first-row {
  padding-top: 5%;
  display: flex;
  justify-content: center;
  align-items: baseline;
}
.col-12.sec-row h6 {
  font-family: Lato;
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  line-height: 14px;
  letter-spacing: 0.455em;
  text-align: center;
  color: #fff;
  margin-top: 3%;
}
.col-12.sec-row h3 {
  font-family: Righteous;
  font-size: 36px;
  font-style: normal;
  font-weight: 400;
  line-height: 45px;
  letter-spacing: 0em;
  text-align: center;
  color: #fff;
  text-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.6) !important;
  margin-top: 10%;
}
.col-12.sec-row h4 {
  font-family: Righteous;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: center;
  color: #e83500;
}
@media (max-width: 575px) {
  .col-12.first-row {
    padding-top: 25%;
  }
  .col-12.sec-row h3 {
    margin-top: 35%;
    font-size: 28px;
    margin-bottom: 0;
  }
}
.row.justify-content-center.align-items-center.mt-4.w-100 img {
  width: 160px;
}
.col-12.sec-row h2 {
  font-family: Righteous;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 30px;
  letter-spacing: 0em;
  text-align: center;
  color: #e83500;
}
