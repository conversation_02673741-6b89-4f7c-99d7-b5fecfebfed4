html,
body {
  overflow-x: hidden;
  scroll-behavior: smooth;
  font-family: Righteous !important;
}
header {
  background: #12141D;
  position: fixed;
  width: 100%;
  z-index: 999;
}
header .prepjoy-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media only screen and (max-width: 767px) {
  header .prepjoy-navbar {
    flex-direction: column;
    padding: 1rem !important;
  }
}
header .prepjoy-navbar ul {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}
@media only screen and (max-width: 767px) {
  header .prepjoy-navbar ul {
    margin-top: 10px;
  }
}
header .prepjoy-navbar ul li {
  list-style: none;
  margin-right: 2rem;
  font-size: 1.2rem;
}
header .prepjoy-navbar ul li a {
  text-decoration: none;
}
header .prepjoy-navbar ul li a i {
  color: #fff;
}
header .prepjoy-navbar .prepjoy-logo {
  display: flex;
  align-items: center;
}
header .prepjoy-navbar .prepjoy-logo .prepjoy-logo-img {
  width: 120px !important;
}
header .prepjoy-navbar .prepjoy-logo .ws-logo-img {
  width: 95px !important;
  margin-top: 5px;
}
@media only screen and (max-width: 767px) {
}
.download-icon img {
  width: 150px;
  border: 1px solid #fff;
  border-radius: 9px;
}
.testimonial_section-1 {
  background: #E93500;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='900' preserveAspectRatio='none' viewBox='0 0 1687 900'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1057%26quot%3b)' fill='none'%3e%3crect width='1687' height='900' x='0' y='0' fill='rgba(233%2c 53%2c 0%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c512.135C96.819%2c519.474%2c187.391%2c471.129%2c270.329%2c420.64C354.904%2c369.154%2c440.771%2c310.134%2c480.677%2c219.518C520.375%2c129.376%2c506.713%2c26.443%2c486.372%2c-69.93C467.024%2c-161.597%2c434.498%2c-253.005%2c367.187%2c-318.17C302.557%2c-380.739%2c210.187%2c-396.551%2c123.644%2c-421.092C37.917%2c-445.401%2c-50.369%2c-487.737%2c-135.258%2c-460.646C-219.688%2c-433.701%2c-276.417%2c-354.986%2c-321.878%2c-278.909C-362.274%2c-211.307%2c-358.076%2c-130.533%2c-378.043%2c-54.354C-401.223%2c34.083%2c-475.337%2c117.455%2c-448.689%2c204.909C-421.968%2c292.604%2c-320.859%2c329.77%2c-245.216%2c381.564C-167.228%2c434.964%2c-94.248%2c504.991%2c0%2c512.135' fill='%23dd3200'%3e%3c/path%3e%3cpath d='M1687 1630.9180000000001C1822.745 1653.1019999999999 1964.757 1582.645 2065.5860000000002 1489.091 2160.032 1401.46 2157.527 1256.442 2214.916 1141.091 2275.024 1020.274 2425.542 930.081 2410.413 795.989 2395.335 662.35 2245.83 591.588 2143.121 504.769 2055.193 430.445 1967.495 352.677 1855.316 326.77 1746.585 301.659 1631.66 318.50699999999995 1529.327 363.01599999999996 1434.203 404.39 1379.833 497.618 1302.796 567.085 1212.47 648.5360000000001 1073.871 690.5699999999999 1037.406 806.602 1000.407 924.332 1052.672 1054.884 1116.2559999999999 1160.65 1176.118 1260.225 1290.713 1303.228 1380.38 1377.1100000000001 1484.894 1463.225 1553.351 1609.076 1687 1630.9180000000001' fill='%23f53800'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1057'%3e%3crect width='1687' height='900' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 65vh;
}
@media only screen and (max-width: 768px) {
  .testimonial_section-1 {
    flex-direction: column;
    height: auto;
  }
}
@media only screen and (max-width: 902px) {
  .testimonial_section-1 {
    height: auto;
  }
}
.testimonial_section-1_left {
  color: #fff;
}
@media only screen and (max-width: 768px) {
  .testimonial_section-1_left {
    border-right: none;
    padding-top: 24px;
  }
}
.testimonial_section-1_left h2 {
  font-size: 2.3rem;
  position: relative;
}
.testimonial_section-1_left h2::before {
  content: '';
  position: absolute;
  background: #12141D;
  width: 100px;
  height: 3px;
  border-radius: 5px;
  top: 100%;
  bottom: 0;
  margin-top: 6px;
}
.testimonial_section-1_right {
  width: 50%;
  padding: 10px;
}
.testimonial_section-1_right .card {
  background: #12141D;
  border-radius: 10px;
  box-shadow: 0 2px 3px rgba(255, 255, 255, 0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.testimonial_section-1_right .card img {
  border: 2px solid #fff;
  border-radius: 50%;
}
@media only screen and (max-width: 767px) {
  .testimonial_section-1_right {
    width: 100%;
  }
}
.testimonial_section-2 {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='683' preserveAspectRatio='none' viewBox='0 0 1687 683'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1290%26quot%3b)' fill='none'%3e%3crect width='1687' height='683' x='0' y='0' fill='rgba(18%2c 20%2c 29%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c656.839C124.274%2c642.525%2c236.209%2c596.395%2c350.283%2c545.051C484.028%2c484.853%2c639.659%2c450.54%2c724.368%2c330.808C814.466%2c203.459%2c871.528%2c29.183%2c819.232%2c-117.788C767.331%2c-263.647%2c576.713%2c-295.987%2c463.69%2c-401.79C366.563%2c-492.712%2c332.804%2c-661.569%2c203.76%2c-693.943C74.453%2c-726.383%2c-38.161%2c-602.665%2c-164.232%2c-559.322C-282.914%2c-518.519%2c-429.685%2c-538.259%2c-515.483%2c-446.668C-601.732%2c-354.596%2c-571.621%2c-208.444%2c-606.496%2c-87.201C-648.5%2c58.826%2c-782.093%2c192.18%2c-741.353%2c338.565C-700.713%2c484.591%2c-545.567%2c572.394%2c-406.284%2c632.19C-279.033%2c686.821%2c-137.573%2c672.684%2c0%2c656.839' fill='%230c0d13'%3e%3c/path%3e%3cpath d='M1687 1174.577C1826.794 1179.8809999999999 1955.7350000000001 1358.6979999999999 2080.205 1294.839 2200.372 1233.188 2147.542 1041.349 2193.921 914.5029999999999 2235.475 800.853 2359.149 708.552 2338.143 589.38 2317.11 470.055 2183.256 411.587 2091.3559999999998 332.624 2012.913 265.224 1935.573 201.32 1840.134 161.47500000000002 1735.166 117.65200000000004 1623.012 61.40700000000004 1513.253 91.27099999999996 1403.055 121.255 1336.91 228.64800000000002 1265.551 317.813 1196.774 403.751 1128.713 491.73199999999997 1106.4850000000001 599.535 1083.438 711.311 1104.893 824.408 1138.613 933.44 1176.312 1055.336 1195.714 1214.3519999999999 1312.5140000000001 1265.711 1432.55 1318.493 1555.966 1169.605 1687 1174.577' fill='%23181b27'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1290'%3e%3crect width='1687' height='683' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 60vh;
}
@media only screen and (max-width: 768px) {
  .testimonial_section-2 {
    flex-direction: column-reverse;
    height: auto;
  }
}
@media only screen and (max-width: 902px) {
  .testimonial_section-2 {
    height: auto;
  }
}
.testimonial_section-2_left {
  color: #fff;
  padding-top: 24px;
}
.testimonial_section-2_left h2 {
  position: relative;
}
.testimonial_section-2_left h2::before {
  content: '';
  position: absolute;
  background: #E93500;
  width: 100px;
  height: 3px;
  border-radius: 5px;
  top: 100%;
  bottom: 0;
  margin-top: 6px;
}
@media only screen and (max-width: 768px) {
  .testimonial_section-2 {
    border-left: none;
  }
}
.testimonial_section-2_right {
  width: 50%;
}
.testimonial_section-2_right .card {
  background: #E93500;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.testimonial_section-2_right .card img {
  border: 2px solid #fff;
  border-radius: 50%;
}
@media only screen and (max-width: 767px) {
  .testimonial_section-2_right {
    width: 100%;
  }
}
.first-sec {
  background: #E93500;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='900' preserveAspectRatio='none' viewBox='0 0 1687 900'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1057%26quot%3b)' fill='none'%3e%3crect width='1687' height='900' x='0' y='0' fill='rgba(233%2c 53%2c 0%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c512.135C96.819%2c519.474%2c187.391%2c471.129%2c270.329%2c420.64C354.904%2c369.154%2c440.771%2c310.134%2c480.677%2c219.518C520.375%2c129.376%2c506.713%2c26.443%2c486.372%2c-69.93C467.024%2c-161.597%2c434.498%2c-253.005%2c367.187%2c-318.17C302.557%2c-380.739%2c210.187%2c-396.551%2c123.644%2c-421.092C37.917%2c-445.401%2c-50.369%2c-487.737%2c-135.258%2c-460.646C-219.688%2c-433.701%2c-276.417%2c-354.986%2c-321.878%2c-278.909C-362.274%2c-211.307%2c-358.076%2c-130.533%2c-378.043%2c-54.354C-401.223%2c34.083%2c-475.337%2c117.455%2c-448.689%2c204.909C-421.968%2c292.604%2c-320.859%2c329.77%2c-245.216%2c381.564C-167.228%2c434.964%2c-94.248%2c504.991%2c0%2c512.135' fill='%23dd3200'%3e%3c/path%3e%3cpath d='M1687 1630.9180000000001C1822.745 1653.1019999999999 1964.757 1582.645 2065.5860000000002 1489.091 2160.032 1401.46 2157.527 1256.442 2214.916 1141.091 2275.024 1020.274 2425.542 930.081 2410.413 795.989 2395.335 662.35 2245.83 591.588 2143.121 504.769 2055.193 430.445 1967.495 352.677 1855.316 326.77 1746.585 301.659 1631.66 318.50699999999995 1529.327 363.01599999999996 1434.203 404.39 1379.833 497.618 1302.796 567.085 1212.47 648.5360000000001 1073.871 690.5699999999999 1037.406 806.602 1000.407 924.332 1052.672 1054.884 1116.2559999999999 1160.65 1176.118 1260.225 1290.713 1303.228 1380.38 1377.1100000000001 1484.894 1463.225 1553.351 1609.076 1687 1630.9180000000001' fill='%23f53800'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1057'%3e%3crect width='1687' height='900' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.first-sec .first-sec-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  grid-gap: 2rem;
  margin: 0 auto !important;
  width: calc(100% - 30%) !important;
}
@media only screen and (max-width: 768px) {
  .first-sec .first-sec-wrap {
    grid-template-columns: repeat(1, 1fr);
    width: auto !important;
  }
  .first-sec .first-sec-wrap .first-sec-2 img {
    width: 200px !important;
  }
}
.second-sec {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='683' preserveAspectRatio='none' viewBox='0 0 1687 683'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1290%26quot%3b)' fill='none'%3e%3crect width='1687' height='683' x='0' y='0' fill='rgba(18%2c 20%2c 29%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c656.839C124.274%2c642.525%2c236.209%2c596.395%2c350.283%2c545.051C484.028%2c484.853%2c639.659%2c450.54%2c724.368%2c330.808C814.466%2c203.459%2c871.528%2c29.183%2c819.232%2c-117.788C767.331%2c-263.647%2c576.713%2c-295.987%2c463.69%2c-401.79C366.563%2c-492.712%2c332.804%2c-661.569%2c203.76%2c-693.943C74.453%2c-726.383%2c-38.161%2c-602.665%2c-164.232%2c-559.322C-282.914%2c-518.519%2c-429.685%2c-538.259%2c-515.483%2c-446.668C-601.732%2c-354.596%2c-571.621%2c-208.444%2c-606.496%2c-87.201C-648.5%2c58.826%2c-782.093%2c192.18%2c-741.353%2c338.565C-700.713%2c484.591%2c-545.567%2c572.394%2c-406.284%2c632.19C-279.033%2c686.821%2c-137.573%2c672.684%2c0%2c656.839' fill='%230c0d13'%3e%3c/path%3e%3cpath d='M1687 1174.577C1826.794 1179.8809999999999 1955.7350000000001 1358.6979999999999 2080.205 1294.839 2200.372 1233.188 2147.542 1041.349 2193.921 914.5029999999999 2235.475 800.853 2359.149 708.552 2338.143 589.38 2317.11 470.055 2183.256 411.587 2091.3559999999998 332.624 2012.913 265.224 1935.573 201.32 1840.134 161.47500000000002 1735.166 117.65200000000004 1623.012 61.40700000000004 1513.253 91.27099999999996 1403.055 121.255 1336.91 228.64800000000002 1265.551 317.813 1196.774 403.751 1128.713 491.73199999999997 1106.4850000000001 599.535 1083.438 711.311 1104.893 824.408 1138.613 933.44 1176.312 1055.336 1195.714 1214.3519999999999 1312.5140000000001 1265.711 1432.55 1318.493 1555.966 1169.605 1687 1174.577' fill='%23181b27'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1290'%3e%3crect width='1687' height='683' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.second-sec .second-sec-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  margin: 0 auto !important;
  width: calc(100% - 30%) !important;
}
@media only screen and (max-width: 768px) {
  .second-sec .second-sec-wrap {
    grid-template-columns: repeat(1, 1fr);
    width: auto !important;
  }
  .second-sec .second-sec-wrap .second-sec-1 img {
    width: 200px;
  }
  .second-sec .second-sec-wrap .second-sec-2 {
    margin-top: 2rem;
  }
}
.third-sec {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='900' preserveAspectRatio='none' viewBox='0 0 1687 900'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1057%26quot%3b)' fill='none'%3e%3crect width='1687' height='900' x='0' y='0' fill='rgba(233%2c 53%2c 0%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c512.135C96.819%2c519.474%2c187.391%2c471.129%2c270.329%2c420.64C354.904%2c369.154%2c440.771%2c310.134%2c480.677%2c219.518C520.375%2c129.376%2c506.713%2c26.443%2c486.372%2c-69.93C467.024%2c-161.597%2c434.498%2c-253.005%2c367.187%2c-318.17C302.557%2c-380.739%2c210.187%2c-396.551%2c123.644%2c-421.092C37.917%2c-445.401%2c-50.369%2c-487.737%2c-135.258%2c-460.646C-219.688%2c-433.701%2c-276.417%2c-354.986%2c-321.878%2c-278.909C-362.274%2c-211.307%2c-358.076%2c-130.533%2c-378.043%2c-54.354C-401.223%2c34.083%2c-475.337%2c117.455%2c-448.689%2c204.909C-421.968%2c292.604%2c-320.859%2c329.77%2c-245.216%2c381.564C-167.228%2c434.964%2c-94.248%2c504.991%2c0%2c512.135' fill='%23dd3200'%3e%3c/path%3e%3cpath d='M1687 1630.9180000000001C1822.745 1653.1019999999999 1964.757 1582.645 2065.5860000000002 1489.091 2160.032 1401.46 2157.527 1256.442 2214.916 1141.091 2275.024 1020.274 2425.542 930.081 2410.413 795.989 2395.335 662.35 2245.83 591.588 2143.121 504.769 2055.193 430.445 1967.495 352.677 1855.316 326.77 1746.585 301.659 1631.66 318.50699999999995 1529.327 363.01599999999996 1434.203 404.39 1379.833 497.618 1302.796 567.085 1212.47 648.5360000000001 1073.871 690.5699999999999 1037.406 806.602 1000.407 924.332 1052.672 1054.884 1116.2559999999999 1160.65 1176.118 1260.225 1290.713 1303.228 1380.38 1377.1100000000001 1484.894 1463.225 1553.351 1609.076 1687 1630.9180000000001' fill='%23f53800'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1057'%3e%3crect width='1687' height='900' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.third-sec .third-sec-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  margin: 0 auto !important;
  width: calc(100% - 30%) !important;
}
@media only screen and (max-width: 768px) {
  .third-sec .third-sec-wrap {
    grid-template-columns: repeat(1, 1fr);
    width: auto !important;
  }
  .third-sec .third-sec-wrap .third-sec-2 {
    margin-top: 2rem;
  }
  .third-sec .third-sec-wrap .third-sec-2 img {
    width: 200px;
  }
}
.fourth-sec {
  background: #12141D;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='683' preserveAspectRatio='none' viewBox='0 0 1687 683'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1290%26quot%3b)' fill='none'%3e%3crect width='1687' height='683' x='0' y='0' fill='rgba(18%2c 20%2c 29%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c656.839C124.274%2c642.525%2c236.209%2c596.395%2c350.283%2c545.051C484.028%2c484.853%2c639.659%2c450.54%2c724.368%2c330.808C814.466%2c203.459%2c871.528%2c29.183%2c819.232%2c-117.788C767.331%2c-263.647%2c576.713%2c-295.987%2c463.69%2c-401.79C366.563%2c-492.712%2c332.804%2c-661.569%2c203.76%2c-693.943C74.453%2c-726.383%2c-38.161%2c-602.665%2c-164.232%2c-559.322C-282.914%2c-518.519%2c-429.685%2c-538.259%2c-515.483%2c-446.668C-601.732%2c-354.596%2c-571.621%2c-208.444%2c-606.496%2c-87.201C-648.5%2c58.826%2c-782.093%2c192.18%2c-741.353%2c338.565C-700.713%2c484.591%2c-545.567%2c572.394%2c-406.284%2c632.19C-279.033%2c686.821%2c-137.573%2c672.684%2c0%2c656.839' fill='%230c0d13'%3e%3c/path%3e%3cpath d='M1687 1174.577C1826.794 1179.8809999999999 1955.7350000000001 1358.6979999999999 2080.205 1294.839 2200.372 1233.188 2147.542 1041.349 2193.921 914.5029999999999 2235.475 800.853 2359.149 708.552 2338.143 589.38 2317.11 470.055 2183.256 411.587 2091.3559999999998 332.624 2012.913 265.224 1935.573 201.32 1840.134 161.47500000000002 1735.166 117.65200000000004 1623.012 61.40700000000004 1513.253 91.27099999999996 1403.055 121.255 1336.91 228.64800000000002 1265.551 317.813 1196.774 403.751 1128.713 491.73199999999997 1106.4850000000001 599.535 1083.438 711.311 1104.893 824.408 1138.613 933.44 1176.312 1055.336 1195.714 1214.3519999999999 1312.5140000000001 1265.711 1432.55 1318.493 1555.966 1169.605 1687 1174.577' fill='%23181b27'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1290'%3e%3crect width='1687' height='683' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.fourth-sec .fourth-sec-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  width: calc(100% - 30%);
  margin: 0 auto;
}
.fourth-sec .fourth-sec-wrap .fourth-sec-1 img {
  width: 260px;
}
@media only screen and (max-width: 768px) {
  .fourth-sec .fourth-sec-wrap {
    grid-template-columns: repeat(1, 1fr);
    width: auto !important;
  }
  .fourth-sec .fourth-sec-wrap .fourth-sec-1 {
    margin-bottom: 2rem;
  }
  .fourth-sec .fourth-sec-wrap .fourth-sec-1 img {
    width: 200px;
  }
}
.fifth-sec {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='900' preserveAspectRatio='none' viewBox='0 0 1687 900'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1057%26quot%3b)' fill='none'%3e%3crect width='1687' height='900' x='0' y='0' fill='rgba(233%2c 53%2c 0%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c512.135C96.819%2c519.474%2c187.391%2c471.129%2c270.329%2c420.64C354.904%2c369.154%2c440.771%2c310.134%2c480.677%2c219.518C520.375%2c129.376%2c506.713%2c26.443%2c486.372%2c-69.93C467.024%2c-161.597%2c434.498%2c-253.005%2c367.187%2c-318.17C302.557%2c-380.739%2c210.187%2c-396.551%2c123.644%2c-421.092C37.917%2c-445.401%2c-50.369%2c-487.737%2c-135.258%2c-460.646C-219.688%2c-433.701%2c-276.417%2c-354.986%2c-321.878%2c-278.909C-362.274%2c-211.307%2c-358.076%2c-130.533%2c-378.043%2c-54.354C-401.223%2c34.083%2c-475.337%2c117.455%2c-448.689%2c204.909C-421.968%2c292.604%2c-320.859%2c329.77%2c-245.216%2c381.564C-167.228%2c434.964%2c-94.248%2c504.991%2c0%2c512.135' fill='%23dd3200'%3e%3c/path%3e%3cpath d='M1687 1630.9180000000001C1822.745 1653.1019999999999 1964.757 1582.645 2065.5860000000002 1489.091 2160.032 1401.46 2157.527 1256.442 2214.916 1141.091 2275.024 1020.274 2425.542 930.081 2410.413 795.989 2395.335 662.35 2245.83 591.588 2143.121 504.769 2055.193 430.445 1967.495 352.677 1855.316 326.77 1746.585 301.659 1631.66 318.50699999999995 1529.327 363.01599999999996 1434.203 404.39 1379.833 497.618 1302.796 567.085 1212.47 648.5360000000001 1073.871 690.5699999999999 1037.406 806.602 1000.407 924.332 1052.672 1054.884 1116.2559999999999 1160.65 1176.118 1260.225 1290.713 1303.228 1380.38 1377.1100000000001 1484.894 1463.225 1553.351 1609.076 1687 1630.9180000000001' fill='%23f53800'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1057'%3e%3crect width='1687' height='900' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.fifth-sec .fifth-sec-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  margin: 0 auto !important;
  width: calc(100% - 30%) !important;
}
@media only screen and (max-width: 768px) {
  .fifth-sec .fifth-sec-wrap {
    grid-template-columns: repeat(1, 1fr);
    width: auto !important;
  }
  .fifth-sec .fifth-sec-wrap .fifth-sec-2 {
    margin-top: 2rem;
  }
  .fifth-sec .fifth-sec-wrap .fifth-sec-2 img {
    width: 200px;
  }
}
.sixth-sec {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1687' height='683' preserveAspectRatio='none' viewBox='0 0 1687 683'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1290%26quot%3b)' fill='none'%3e%3crect width='1687' height='683' x='0' y='0' fill='rgba(18%2c 20%2c 29%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c656.839C124.274%2c642.525%2c236.209%2c596.395%2c350.283%2c545.051C484.028%2c484.853%2c639.659%2c450.54%2c724.368%2c330.808C814.466%2c203.459%2c871.528%2c29.183%2c819.232%2c-117.788C767.331%2c-263.647%2c576.713%2c-295.987%2c463.69%2c-401.79C366.563%2c-492.712%2c332.804%2c-661.569%2c203.76%2c-693.943C74.453%2c-726.383%2c-38.161%2c-602.665%2c-164.232%2c-559.322C-282.914%2c-518.519%2c-429.685%2c-538.259%2c-515.483%2c-446.668C-601.732%2c-354.596%2c-571.621%2c-208.444%2c-606.496%2c-87.201C-648.5%2c58.826%2c-782.093%2c192.18%2c-741.353%2c338.565C-700.713%2c484.591%2c-545.567%2c572.394%2c-406.284%2c632.19C-279.033%2c686.821%2c-137.573%2c672.684%2c0%2c656.839' fill='%230c0d13'%3e%3c/path%3e%3cpath d='M1687 1174.577C1826.794 1179.8809999999999 1955.7350000000001 1358.6979999999999 2080.205 1294.839 2200.372 1233.188 2147.542 1041.349 2193.921 914.5029999999999 2235.475 800.853 2359.149 708.552 2338.143 589.38 2317.11 470.055 2183.256 411.587 2091.3559999999998 332.624 2012.913 265.224 1935.573 201.32 1840.134 161.47500000000002 1735.166 117.65200000000004 1623.012 61.40700000000004 1513.253 91.27099999999996 1403.055 121.255 1336.91 228.64800000000002 1265.551 317.813 1196.774 403.751 1128.713 491.73199999999997 1106.4850000000001 599.535 1083.438 711.311 1104.893 824.408 1138.613 933.44 1176.312 1055.336 1195.714 1214.3519999999999 1312.5140000000001 1265.711 1432.55 1318.493 1555.966 1169.605 1687 1174.577' fill='%23181b27'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1290'%3e%3crect width='1687' height='683' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.sixth-sec .sixth-sec-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  align-items: center;
  margin: 0 auto !important;
  width: calc(100% - 30%) !important;
}
@media only screen and (max-width: 768px) {
  .sixth-sec .sixth-sec-wrap {
    grid-template-columns: repeat(1, 1fr);
    width: auto !important;
  }
  .sixth-sec .sixth-sec-wrap .sixth-sec-2 {
    margin-top: 2rem;
  }
  .sixth-sec .sixth-sec-wrap .sixth-sec-1 img {
    width: 200px;
  }
}
.prep-footer {
  padding: 4rem;
  background: #E93500;
}
.prep-footer p {
  font-weight: bold;
}
.prep-footer a img {
  width: 200px;
}
@media only screen and (max-width: 768px) {
  .prep-footer a img {
    width: 100px;
  }
}
@media only screen and (max-width: 768px) {
  .prep-footer h2 {
    font-weight: bold;
  }
}
@media only screen and (max-width: 768px) {
  .prep-footer .storelogo {
    display: flex;
    justify-content: center;
  }
}
@media only screen and (max-width: 768px) {
  .prep-footer {
    padding: 1rem;
  }
}
.ml4 {
  position: relative;
  font-weight: 900;
  font-size: 4rem;
  z-index: 10000;
}
.ml4 .letters {
  position: absolute;
  margin: auto;
  left: 0;
  top: -1rem;
  right: 0;
  opacity: 1;
}
.ml7 {
  position: relative;
  font-weight: 900;
  font-size: 2.7rem;
}
.ml7 .text-wrapper {
  position: relative;
  display: inline-block;
  padding-top: 0.2em;
  padding-right: 0.05em;
  padding-bottom: 0.1em;
  overflow: hidden;
}
.ml7 .letter {
  transform-origin: 0 100%;
  display: inline-block;
  line-height: 1em;
}
.ml10 {
  position: relative;
  font-weight: 600;
}
.ml10 .text-wrapper {
  position: relative;
  display: inline-block;
  padding-top: 0.2em;
  padding-right: 0.05em;
  padding-bottom: 0.1em;
  overflow: hidden;
}
.ml10 .letter {
  display: inline-block;
  line-height: 1em;
  transform-origin: 0 0;
}
.reveal-text {
  visibility: hidden;
}
.testimonial_section {
  background: #12141D;
  height: 80vh;
}
@media only screen and (max-width: 1024px) {
  .testimonial_section {
    height: 100%;
  }
}
@media only screen and (max-width: 990px) {
  .testimonial_section {
    height: 100%;
  }
}
@media only screen and (max-width: 768px) {
  .testimonial_section {
    height: 100%;
  }
}
.downloadbtns {
  display: flex;
  justify-content: center;
  align-items: center;
}
.downloadbtns a img {
  width: 200px;
  border: 1px solid #fff;
  border-radius: 5px;
}
@media only screen and (max-width: 768px) {
  .downloadbtns a img {
    width: 120px;
  }
}
i {
  color: #FDCC0D;
}
@media only screen and (max-width: 990px) {
  .testimonial_img {
    left: 0% !important;
  }
  .testimonial_img img {
    width: 60px!important;
  }
}
@media only screen and (max-width: 768px) {
  .testimonial_img img {
    width: 80px!important;
  }
  .wrap {
    margin-bottom: 0px !important;
  }
}
@media only screen and (max-width: 1024px) {
  .wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px !important;
  }
  .testimonial_section .testimonial_box .testimonial_container .background_layer {
    left: 30%;
    margin-bottom: 20px;
  }
}
