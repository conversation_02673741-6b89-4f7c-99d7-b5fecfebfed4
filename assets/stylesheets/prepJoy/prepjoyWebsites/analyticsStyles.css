html,
body {
  scroll-behavior: smooth;
}
h6 {
  margin-bottom: 0!important;
}
.mdl-js-layout {
  position: sticky;
  top: 0;
  z-index: 999;
}
.an-header {
  position: sticky;
  top: 40px;
  background: #F4F5FA;
  padding: 1rem;
  z-index: 99;
}
#history__header-title .totalQuiz {
  width: 250px;
  margin: 0 auto;
  border: 2px solid #F79420 !important;
  padding: 0.5rem 0;
  font-weight: bolder;
  border-radius: 3px;
}
@media (max-width: 768px) {
  #history__header-title {
    margin-top: 20px;
  }
}
@media (max-width: 768px) {
  #history__header-content {
    margin-top: 1rem !important;
  }
}
.listContainer {
  padding-bottom: 4rem;
}
@media (max-width: 768px) {
  .listContainer {
    margin-top: 0!important;
  }
}
.history__card {
  border-radius: 5px;
  padding: 0.4rem;
  width: 700px;
  margin: 0 auto;
  margin-bottom: 10px;
  background: #fff;
  box-shadow: 0 0 10px #0000001a;
  cursor: pointer;
}
@media (max-width: 768px) {
  .history__card {
    width: 340px;
    padding: 0.4rem;
  }
}
@media (max-width: 320px) {
  .history__card {
    width: auto;
    padding: 0.4rem;
  }
}
.getHistoryData-btn {
  background: transparent;
  border: none;
  transition: all 0.3s ease-out;
}
.getHistoryData-btn:hover {
  transform: translateX(4px);
}
.datesDiv {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #BB6BD9 0%, #7B24CD 99.48%);
  text-align: center;
  border-radius: 5px;
  color: #fff !important;
  width: 70px;
}
.datesDiv p {
  font-size: 12px;
}
.datesDiv .dateDigit {
  font-size: 20px !important;
  font-weight: 500;
}
@media (max-width: 768px) {
  .datesDiv .dateDigit {
    font-size: 18px !important;
  }
}
@media (max-width: 768px) {
  .datesDiv {
    width: 60px;
  }
}
.pointsSummary {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  align-items: center;
  text-align: center;
  width: auto;
}
.pointsSummary .points-details-card {
  text-align: left;
}
@media (max-width: 768px) {
  .pointsSummary {
    grid-template-columns: repeat(2, 1fr);
    width: auto;
  }
  .pointsSummary p {
    font-size: 12px;
    text-align: left;
  }
}
.qName {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 300px;
}
@media (max-width: 768px) {
  .qName {
    max-width: 150px;
  }
}
footer,
.mobile-footer-nav {
  display: none !important;
}
@media (max-width: 768px) {
  .total-points {
    margin-right: -2px !important;
  }
}
.listContainer {
  padding-left: 10px !important;
}
