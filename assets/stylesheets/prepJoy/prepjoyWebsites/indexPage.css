* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
html,
body {
  scroll-behavior: smooth ;
  font-family: Righteous !important;
}
.prep-index__btn {
  background: #E83500;
  border-radius: 10px;
}
@media only screen and (max-width: 768px) {
  .prep-index__btn {
    margin-top: 10px;
    display: none;
  }
}
#prepjoyHeader {
  background: #0F0839;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.prep-index__btn-1 {
  background: #E83500;
  border-radius: 5px;
  margin-top: 10px;
  display: none;
}
@media only screen and (max-width: 768px) {
  .prep-index__btn-1 {
    margin-top: 10px;
    display: block;
  }
}
.hero {
  background: #0F0839;
  height: 10vh;
  display: flex;
  align-items: center;
}
.hero .prep-index__navbar {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: #0F0839;
  width: 100%;
  height: 10vh;
  position: fixed;
  top: 0px;
  padding: 10px 30px;
  transition: all 0.5s;
  transition-timing-function: cubic-bezier(0, 0.57, 0.88, 0.51);
  margin: 0 auto;
  font-family: Righteous;
}
@media only screen and (max-width: 768px) {
  .hero .prep-index__navbar {
    flex-direction: column;
  }
}
.hero .prep-index__navbar .prep-index__nav {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.hero .prep-index__navbar .prep-index__nav ul {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
}
.hero .prep-index__navbar .prep-index__nav ul li {
  list-style: none;
  margin-right: 20px;
}
.hero .prep-index__navbar .prep-index__nav ul li a {
  color: #fff;
  display: inline-block;
}
.hero .prep-index__navbar .prep-index__nav ul li a::after {
  content: '';
  display: block;
  width: 0;
  height: 2px;
  background: #E83500;
  transition: width 0.3s;
}
.hero .prep-index__navbar .prep-index__nav ul li a:hover {
  text-decoration: none;
}
.hero .prep-index__navbar .prep-index__nav ul li a:hover::after {
  width: 100%;
}
@media only screen and (max-width: 768px) {
  .hero .prep-index__navbar .prep-index__nav {
    flex-direction: column;
  }
}
.hero .prep-index__navbar .prep-index__search div {
  display: flex;
}
.hero .prep-index__navbar .prep-index__search div input {
  width: 400px;
  border: none;
  border-radius: 5px;
  outline: none;
  padding: 12px;
  height: 35px;
}
@media only screen and (max-width: 768px) {
  .hero .prep-index__navbar .prep-index__search div input {
    width: 300px;
  }
}
.hero .prep-index__navbar .prep-index__search div button {
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  margin-left: -30px;
  cursor: pointer;
}
.hero .prep-index__navbar .prep-index__search div button:focus {
  outline: none;
}
.hero .prep-index__navbar .prep-index__search div button i {
  color: #E83500;
}
.hero .prep-index__navbar .prep-index__login {
  margin-left: 30px;
}
.hero .prep-index__navbar .prep-index__login button {
  border: none;
  background: #E83500;
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 35px;
}
.hero .prep-index__navbar .prep-index__login button:focus {
  outline: none;
}
.prep-main {
  background: #04001D;
  padding: 2rem;
  height: 100vh;
  display: flex;
  align-items: center;
}
@media only screen and (max-width: 768px) {
  .prep-main {
    height: auto;
    padding: 1rem 0rem;
  }
}
.prep-main a {
  text-decoration: none;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 0 8px -3px #fff;
  transition: all 0.2s ease;
  font-family: Righteous;
}
.prep-main a p {
  color: #999 !important;
}
.prep-main a:hover {
  background: #0F0839;
}
@media only screen and (max-width: 768px) {
  .prep-main a {
    text-align: center !important;
    padding: 0.7rem;
  }
}
.prep-main__contents {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 2rem;
  justify-content: center;
}
@media only screen and (max-width: 768px) {
  .prep-main__contents {
    grid-template-columns: repeat(1, 1fr);
  }
}
.prep-footer {
  height: 30vh;
  background: #0F0839;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.prep-footer__content {
  display: flex;
  align-items: center;
  justify-content: center;
}
.prep-footer__content--img-1 {
  width: 150px;
}
.prep-footer__social {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
}
.prep-footer__social a {
  color: #fff;
  margin-right: 10px;
}
.prep-footer__social a i {
  font-size: 1.5rem;
}
#prepRegisterModal {
  background: #04001D;
}
#prepRegisterModal #prepRegisterModalContent {
  background: #0F0839;
}
#prepRegisterModal h4 {
  color: #E83500;
  font-family: Righteous;
}
#prepRegisterModal .form-control:focus {
  box-shadow: none;
  border-color: #E83500;
  font-size: 15px;
}
#prepRegisterModal input {
  font-family: Righteous;
}
#prepRegisterModal .prepLoginBtn {
  background: #E83500;
  color: #fff;
  font-family: Righteous;
  transition: all 0.3s ease;
}
#prepRegisterModal .prepLoginBtn:focus {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
