.leaderBoard__wrapper {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
}
@media (max-width: 768px) {
  .leaderBoard__wrapper {
    padding-left: 0px !important;
    padding-right: 0px !important;
    flex-direction: column;
  }
}
.leaderBoard__wrapper-contents {
  background: #fff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.09);
}
@media (max-width: 768px) {
  .leaderBoard__wrapper-contents {
    padding: 0 0 20px 0;
  }
}
.leaderBoard__wrapper-contents__tabs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
@media (max-width: 768px) {
  .leaderBoard__wrapper-contents__tabs {
    gap: 5px;
  }
}
.leaderBoard__wrapper-contents__tabs .lbTab {
  background: transparent;
  transition: background 0.3s ease-in;
  border-radius: 0!important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.16);
}
.leaderBoard__wrapper-contents__tabs .inst__lbTab {
  border-top-left-radius: 10px !important;
}
.leaderBoard__wrapper-contents__tabs .allInd__lbTab {
  border-top-right-radius: 10px !important;
}
.leaderBoard__wrapper-contents__tabs .activeTab {
  background: #ffd4a2 !important;
  border-bottom: 2px solid #F79420 !important;
}
.leaderBoard__wrapper-contents__datesTab {
  background: #eeeeee;
  padding: 4px;
  border-radius: 100px;
  width: 50%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  margin-bottom: 10px;
  margin-left: 1rem;
}
@media (max-width: 768px) {
  .leaderBoard__wrapper-contents__datesTab {
    width: 90%;
  }
}
.leaderBoard__wrapper-contents__datesTab button {
  border-radius: 100px;
  border: none;
  padding: 3px;
  transition: background 0.3s ease-in;
  color: #000 !important;
}
.leaderBoard__wrapper-contents__datesTab .activeDate {
  background: #15002D;
  color: #fff !important;
}
.leaderBoard__wrapper-contents__list {
  padding: 1.2rem;
}
@media (max-width: 768px) {
  .leaderBoard__wrapper-contents__list {
    padding: 20px 0;
  }
}
.leaderBoard__wrapper-contents__list-wrapper {
  overflow: auto;
  border-radius: 6px;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
}
.leaderBoard__wrapper-contents__list-wrapper table {
  border-spacing: 0;
  border-collapse: collapse;
  border-style: hidden;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);
  width: 100%;
}
.leaderBoard__wrapper-contents__list-wrapper table th,
.leaderBoard__wrapper-contents__list-wrapper table td {
  padding: 10px;
}
.leaderBoard__wrapper-contents__list-wrapper table thead {
  background: #000;
  color: #fff;
}
.leaderBoard__wrapper-contents__list-wrapper table tbody tr {
  position: relative;
}
.leaderBoard__wrapper-contents__list-wrapper table tbody tr:after {
  content: "";
  position: absolute;
  top: 0;
  left: 5%;
  right: 5%;
  width: 90%;
  height: 1px;
  background-image: linear-gradient(to right, transparent, rgba(48, 49, 51, 0.2), transparent);
}
.leaderBoard__wrapper-contents__list-wrapper table tbody tr:hover {
  background: rgba(234, 157, 10, 0.2);
}
.leaderBoard__wrapper-contents__list-wrapper table tbody tr .user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.leaderBoard__wrapper-contents__list-wrapper table tbody tr .user-info .name {
  font-weight: 600;
}
.leaderBoard__wrapper-contents__list-wrapper table tbody tr .user-info .lb-userImg {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(0deg, #D9D9D9, #D9D9D9), linear-gradient(0deg, rgba(217, 217, 217, 0.5), rgba(217, 217, 217, 0.5));
  padding: 2px;
}
.leaderBoard__wrapper-contents__list-wrapper table tbody tr .user-info .lb-userImg img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
@media (max-width: 768px) {
  .leaderBoard__wrapper-navigations {
    width: 100%;
  }
  .leaderBoard__wrapper-navigations h5 {
    text-align: center;
  }
}
.leaderBoard__wrapper-navigations__cards {
  width: 100%;
}
.leaderBoard__wrapper-navigations__card {
  background: #fff;
  padding: 1rem;
  width: 325px;
  border-radius: 5px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease-in-out;
}
@media (max-width: 768px) {
  .leaderBoard__wrapper-navigations__card {
    margin: 0 auto;
  }
}
.leaderBoard__wrapper-navigations__card:hover {
  background: #ffc350;
}
.leaderBoard__wrapper-navigations__card:hover a button {
  transform: translateX(5px);
}
.leaderBoard__wrapper-navigations__card a {
  color: #000 !important;
  display: flex;
  justify-content: space-between;
}
.leaderBoard__wrapper-navigations__card a .card__sec-1 p {
  font-size: 12px;
}
.leaderBoard__wrapper-navigations__card a .card__sec-1 h4 {
  font-size: 1.1rem;
}
.leaderBoard__wrapper-navigations__card a button {
  background: transparent;
  border: none;
  transition: all 0.2s ease;
  color: #000 !important;
}
.leaderBoard__title {
  padding-top: 10px;
}
.place {
  color: #777777;
}
.dailyTests {
  margin-bottom: 3rem;
}
.dailyTests__list {
  margin-top: 2rem;
}
.dailyTests__list-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 1rem;
  align-items: center;
}
@media (max-width: 768px) {
  .dailyTests__list-cards {
    grid-template-columns: repeat(1, 1fr);
    align-items: start;
  }
}
.dailyTests__list-cards__card {
  background: #fff;
  padding: 1rem;
  color: #000;
  border-radius: 5px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid transparent;
}
.dailyTests__list-cards__card button {
  background: transparent;
  border: none;
  transition: all 0.2s ease;
}
.dailyTests__list-cards__card:hover {
  background: #ffc350;
  color: #fff;
}
.dailyTests__list-cards__card:hover button {
  transform: translateX(5px);
}
.leaderBoard__wrapper-navigations__card-link {
  color: #000 !important;
}
.leaderBoard__wrapper-navigations__card-link button {
  color: #000 !important;
}
