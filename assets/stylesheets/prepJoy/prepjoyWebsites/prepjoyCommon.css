body {
  background: #04001D !important;
  font-family: Righteous !important;
}
h1,
h3,
p {
  color: #fff !important;
  font-family: Righteous !important;
}
.ebook_detail {
  background: #04001D !important;
}
.ebooks .ebooks_filter {
  background: transparent !important;
  box-shadow: 0 3px 10px 0 rgba(31, 38, 135, 0.37) !important;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
}
.books-list {
  background: transparent !important;
  box-shadow: 0 3px 40px 0 rgba(31, 38, 135, 0.37) !important;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
}
.books-list .image-wrapper {
  background: transparent !important;
  box-shadow: 0 3px 40px 0 rgba(31, 38, 135, 0.37) !important;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
}
#filters h5 {
  color: #fff !important;
}
#filters #resetFilter {
  color: #fff !important;
}
.book_info {
  color: #fff !important;
}
.book_info h2 {
  color: #fff !important;
}
#buyNow,
#okBuy,
#linkLibrary,
#linkOthers,
#addtoLibrary {
  background-color: #E83500 !important;
  color: #fff !important;
  border: 1px solid #E83500 !important;
  transition: all 0.2s ease;
  box-shadow: 2px 2px 2px rgba(255, 255, 255, 0.25), -2px -2px 2px rgba(255, 255, 255, 0.25) !important;
}
#buyNow:hover,
#okBuy:hover,
#linkLibrary:hover,
#linkOthers:hover,
#addtoLibrary:hover {
  transform: scale(1.06);
}
#buyNow:active,
#okBuy:active,
#linkLibrary:active,
#linkOthers:active,
#addtoLibrary:active {
  transform: scale(1);
}
#continue {
  background-color: #E83500 !important;
  color: #fff !important;
  border: 1px solid #E83500 !important;
  transition: all 0.2s ease;
}
#continue:hover {
  transform: scale(1.06);
}
#continue:active {
  transform: scale(1);
}
.ebook_detail .nav-tabs-book-details ul.nav li a.active {
  color: #6C757D !important;
  border: 1px solid #888 !important;
}
.ebook_detail .nav-tabs-book-details .wrp-details-about-cart {
  color: #fff !important;
}
.tab-pane {
  color: #fff !important;
}
.tab-pane h5 {
  color: #fff !important;
}
.orders {
  background: #0F0839 !important;
}
.orders img {
  width: 100px !important;
}
.users-orders > p {
  font-size: 12px;
  border-bottom: 1px solid #666;
  padding-bottom: 0.5rem;
}
.payment-details {
  margin-top: 10px !important;
}
.payment-details span {
  color: #fff !important;
}
.media-body {
  margin-left: 20px !important;
}
.page_title h4 {
  color: #fff;
}
