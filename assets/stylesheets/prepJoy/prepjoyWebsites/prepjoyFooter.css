.prepjoy_cta {
  background: #000;
  position: relative;
  margin-top: 3rem;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta {
    padding: 2.5rem 1.5rem 3rem;
  }
}
.prepjoy_cta .cta_inner {
  border: 1px solid #E83500;
  padding: 20px;
  border-radius: 20px;
  box-shadow: 0 2px 4px #000;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_info {
    width: 100%;
  }
}
.prepjoy_cta .cta_info h3 {
  color: white;
  font-weight: 100;
  line-height: 35px;
}
.prepjoy_cta .cta_info h3 span {
  font-weight: 600;
}
.prepjoy_cta .cta_info h4 {
  font-weight: normal;
}
.prepjoy_cta .cta_info h4 span {
  font-size: 15px;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_info h4 span {
    padding: 5px 0;
    display: inline-block;
  }
}
.prepjoy_cta .cta_info h4 a {
  color: #E83500 !important;
}
.prepjoy_cta .cta_info h4 a:hover {
  opacity: 0.7;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_btn {
    width: 100%;
  }
}
.prepjoy_cta .cta_btn p {
  color: white;
}
.prepjoy_cta .cta_btn a {
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background-color: #E83500;
  border-color: #E83500;
  border-radius: 7px;
  box-shadow: 0 2px 4px #444;
  -webkit-box-shadow: 0 2px 4px #444;
  -moz-box-shadow: 0 2px 4px #444;
}
.prepjoy_cta .cta_btn a:hover,
.prepjoy_cta .cta_btn a:active,
.prepjoy_cta .cta_btn a:focus,
.prepjoy_cta .cta_btn a:active:focus {
  background-color: #E83500 !important;
  border-color: #E83500 !important;
  box-shadow: none !important;
  outline: 0 !important;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_btn a {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }
}
.prepjoy-footer {
  background: #0F0839;
  padding: 2rem;
  height: 20vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media screen and (max-width: 767px) {
  .prepjoy-footer {
    height: 22vh;
  }
}
.prepjoy-footer__socialIcons {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}
.prepjoy-footer__socialIcons ul {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 0;
}
.prepjoy-footer__socialIcons ul li {
  list-style: none;
  font-size: 1.4rem;
  margin-right: 20px;
}
.prepjoy-footer__socialIcons ul li:nth-child(4) {
  margin-right: 0;
}
.prepjoy-footer__socialIcons ul li i {
  color: #fff;
}
