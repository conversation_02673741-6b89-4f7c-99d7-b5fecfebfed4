* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
body {
  overflow-x: hidden !important;
  position: relative !important;
}
@media screen and (max-width: 767px) {
  .hiddenDiv {
    display: none !important;
  }
}
.header {
  background: #0F0839;
  padding: 2rem;
  position: fixed;
  top: 0;
  z-index: 999999;
  width: 100%;
}
@media screen and (max-width: 767px) {
  .header {
    padding: 1rem;
  }
}
.header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media screen and (max-width: 767px) {
  .header-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
  .header-wrapper :nth-child(3) {
    order: 4;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
  .header-wrapper :nth-child(3) {
    order: 4;
  }
}
.header-wrapper__logo {
  display: flex;
  justify-content: space-between;
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo a img {
    width: 85px !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo a img {
    width: 100px;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo a span {
    font-size: 17px !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo a span {
    font-size: 17px !important;
  }
}
.header-wrapper__logo button {
  display: none;
  margin-left: auto;
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo button {
    display: block;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo button {
    display: block;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo {
    width: 100%;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo {
    width: 100%;
  }
}
.header-wrapper__navbar {
  display: flex;
  align-items: center;
  margin-left: auto;
}
.header-wrapper__navbar .navbar-list {
  display: flex;
  margin-bottom: 0;
  flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  .header-wrapper__navbar .navbar-list {
    flex-direction: column;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__navbar .navbar-list {
    flex-direction: column;
  }
}
.header-wrapper__navbar .navbar-list li {
  list-style: none;
  margin-right: 20px;
}
@media screen and (max-width: 767px) {
  .header-wrapper__navbar .navbar-list li {
    margin-bottom: 12px;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__navbar .navbar-list li {
    margin-bottom: 12px;
  }
}
.header-wrapper__navbar .navbar-list li a {
  color: #fff !important;
  transition: all 0.3s ease;
  position: relative;
}
.header-wrapper__navbar .navbar-list li a::before {
  content: "";
  position: absolute;
  height: 2px;
  background-color: #E83500;
  width: 0;
  left: 50%;
  bottom: -8px;
  transform: translateX(-50%);
  transition: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) all;
}
.header-wrapper__navbar .navbar-list li a:hover {
  color: #E83500 !important;
}
.header-wrapper__navbar .navbar-list li a:hover::before {
  width: 100%;
  border-bottom-color: #E83500;
}
@media screen and (max-width: 767px) {
  .header-wrapper__navbar {
    margin-left: 0;
    margin-top: 14px;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__navbar {
    margin-left: 0;
    margin-top: 14px;
  }
}
.header-wrapper__searchbar {
  display: flex;
  align-items: center;
  margin-bottom: 0!important;
  position: relative;
}
.header-wrapper__searchbar .search-bar__wrapper {
  width: 100%;
  margin-right: 30px;
}
.header-wrapper__searchbar .search-bar__wrapper input {
  width: 300px;
  outline: none;
  padding: 6px;
  border-radius: 5px;
  transition: all 0.4s ease;
  font-size: small;
  border: none;
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar .search-bar__wrapper input {
    width: 100%;
  }
  .header-wrapper__searchbar .search-bar__wrapper input:focus {
    width: 100% !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__searchbar .search-bar__wrapper input {
    width: 100%;
  }
  .header-wrapper__searchbar .search-bar__wrapper input:focus {
    width: 100% !important;
  }
}
.header-wrapper__searchbar .search-bar__wrapper input:focus {
  width: 350px;
}
.header-wrapper__searchbar .search-bar__wrapper button {
  background: #fff;
  border: none;
  outline: none;
  cursor: auto;
  color: #E83500;
  margin-left: -35px;
  border-radius: 50%;
  padding: 5px;
  width: 30px;
  height: 30px;
}
.header-wrapper__searchbar .search-bar__wrapper ul {
  width: 80%;
  position: absolute;
  min-height: 100px;
  max-height: 500px;
  overflow-y: scroll;
}
@media screen and (max-width: 860px) {
  .header-wrapper__searchbar .search-bar__wrapper ul {
    width: 75%;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar .search-bar__wrapper ul {
    width: 88%;
  }
}
@media screen and (max-width: 550px) {
  .header-wrapper__searchbar .search-bar__wrapper ul {
    width: 82% !important;
  }
}
.header-wrapper__searchbar .search-bar__wrapper ul li a {
  white-space: pre-line;
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar .search-bar__wrapper {
    margin-right: 10px;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar {
    width: 100%;
    margin-top: 20px !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__searchbar {
    width: 100%;
    margin-top: 30px;
  }
}
.header-wrapper__userProfile .profile-wrapper {
  border: 2px solid #fff;
  padding: 1rem;
  border-radius: 50%;
  height: 50px;
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.header-wrapper__userProfile .profile-wrapper span {
  border: 1px solid #fff;
  border-radius: 50%;
}
.header-wrapper__userProfile .profile-wrapper span img {
  transform: scale(0.9);
  object-fit: cover;
}
#prepjoyRegBtn {
  background: #E83500;
  color: #fff;
}
#prepjoyLoginModal {
  font-family: Righteous;
}
#prepjoyLoginModal #prepjoyRegModalContent h4 {
  color: #E83500;
}
#prepjoyLoginModal #prepjoyRegModalContent #regLoginForm {
  padding: 1rem;
}
#prepjoyLoginModal #prepjoyRegModalContent #regLoginForm .prepjoyLoginBtn {
  background: #E83500;
  color: #fff;
  margin-top: 10px;
}
.prepjoy-profile {
  background: #04001D;
  text-decoration: none;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 0 8px -3px #fff;
  transition: all 0.3s ease;
  font-family: Righteous;
  width: 30%;
  height: 100vh;
  z-index: 9999999;
}
@media screen and (max-width: 767px) {
  .prepjoy-profile {
    width: 100% !important;
  }
}
@media screen and (max-width: 800px) {
  .prepjoy-profile {
    width: 50%;
  }
}
.prepjoy-profile__content {
  flex-direction: column;
  height: 85%;
}
.prepjoy-profile__content img {
  border: 2px solid #fff;
  object-fit: cover;
}
.prepjoy-profile__content .logoutDiv {
  margin-top: auto;
}
@media screen and (max-width: 767px) {
  .prepjoy-profile__content {
    flex-direction: column;
  }
}
.logout {
  text-decoration: none;
  color: #E83500;
  font-size: 1.2rem;
}
#userCurBadge {
  color: #E83500 !important;
}
.ebooks .ebooks_filter select.background-bg {
  background: #E83500 !important;
  color: #fff !important;
  border-color: #E83500 !important;
}
.prepjoy-header__hamburger {
  width: 30px;
  height: 0;
  position: relative;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: 0.5s ease-in-out;
  -moz-transition: 0.5s ease-in-out;
  -o-transition: 0.5s ease-in-out;
  transition: 0.5s ease-in-out;
  cursor: pointer;
  display: none;
}
@media screen and (max-width: 767px) {
  .prepjoy-header__hamburger {
    display: block;
  }
}
@media screen and (max-width: 860px) {
  .prepjoy-header__hamburger {
    display: block;
  }
}
.prepjoy-header__hamburger span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: #fff;
  border-radius: 9px;
  opacity: 1;
  left: 0;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: 0.25s ease-in-out;
  -moz-transition: 0.25s ease-in-out;
  -o-transition: 0.25s ease-in-out;
  transition: 0.25s ease-in-out;
}
.prepjoy-header__hamburger span:nth-child(1) {
  top: 0px;
}
.prepjoy-header__hamburger span:nth-child(2) {
  top: 10px;
}
.prepjoy-header__hamburger span:nth-child(3) {
  top: 20px;
}
.prepjoy-header__hamburger.open span:nth-child(1) {
  top: 18px;
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
}
.prepjoy-header__hamburger.open span:nth-child(2) {
  opacity: 0;
  left: -60px;
}
.prepjoy-header__hamburger.open span:nth-child(3) {
  top: 18px;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  transform: rotate(-135deg);
}
.modal-content {
  border: none;
}
#prepjoySignupModalBody {
  background: #0F0839;
}
.form .input {
  margin-top: 20px;
  text-align: left;
}
.form .input .inputBox label {
  display: block;
  color: #868686;
  margin-bottom: 5px;
  font-size: 18px;
}
.form .input .inputBox input {
  width: 100%;
  height: 50px;
  color: lightgrey;
  background: #04001D !important;
  border: none;
  outline: none;
  border-radius: 40px;
  padding: 7px 15px;
  font-size: 14px;
  font-weight: lighter !important;
  box-shadow: inset -2px -2px 6px rgba(255, 255, 255, 0.1), inset 2px 2px 6px rgba(0, 0, 0, 0.8);
}
.form .input .inputBox input::placeholder {
  color: #555;
  font-size: 14px;
}
.form .input .inputBox input[type=number] {
  -moz-appearance: textfield;
}
.form .input .inputBox .editPencil {
  color: #555;
  font-size: 18px;
  margin-left: -30px;
  margin-top: 5px;
}
.form .input .inputBox input[type="button"] {
  height: 40px!important;
  background: #E83500 !important;
  color: #fff !important;
  box-shadow: -2px -2px 6px rgba(255, 255, 255, 0.1), 2px 2px 6px rgba(0, 0, 0, 0.8);
}
.form .input .inputBox input[type="button"]:active {
  color: #006c9c;
  margin-top: 20px;
  box-shadow: inset -2px -2px 6px rgba(255, 255, 255, 0.1), inset 2px 2px 6px rgba(0, 0, 0, 0.8);
}
.form .input .inputBox input[type=number]::-webkit-inner-spin-button,
.form .input .inputBox input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}
#linkTestandLibraryPrepjoy {
  background: #E83500 !important;
  color: #fff !important;
  border: none;
}
.userProfile > div {
  padding: 15px !important;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper {
  background-color: transparent !important;
}
.linkWrapper {
  display: flex;
}
.linkWrapper #playstr,
.linkWrapper #appstr {
  width: 200px;
  color: #fff;
  border-radius: 5px;
  border: 1px solid #E83500;
  padding: 5px;
  transition: all 0.3s ease;
}
@media screen and (max-width: 900px) {
  .linkWrapper #playstr,
  .linkWrapper #appstr {
    padding: 0;
  }
}
.linkWrapper #playstr:hover,
.linkWrapper #appstr:hover {
  color: #E83500;
  border-color: #9999;
}
@media screen and (max-width: 900px) {
  .linkWrapper #playstr .dwnp,
  .linkWrapper #appstr .dwnp {
    font-size: 12px !important;
  }
}
@media screen and (max-width: 900px) {
  .linkWrapper #playstr .dwnh,
  .linkWrapper #appstr .dwnh {
    font-size: 14px !important;
  }
}
@media screen and (max-width: 900px) {
  .browse-wrapper {
    padding: 0!important;
  }
}
#bookNewDescription {
  margin-top: 100px;
}
#contPurchase {
  background: #E83500 !important;
  border: 1px solid #E83500 !important;
  color: #fff;
}
.ebooks {
  min-height: 650px;
}
@media screen and (max-width: 990px) {
  .header-wrapper {
    flex-direction: column;
  }
  .header-wrapper__navbar {
    margin-left: 0;
  }
}
@media screen and (max-width: 900px) {
  .navbar {
    padding: 0.5rem 0 !important;
  }
}
.mic {
  cursor: pointer!important;
}
.ldbar {
  /*width:500px;*/
  margin: 0 auto;
  border-radius: 10px;
  border: 4px solid transparent;
  position: relative;
  padding: 1px;
  top: -30px;
}
.ldbar:before {
  content: '';
  /*border:1px solid #fff;*/
  border-radius: 10px;
  position: absolute;
  top: -4px;
  right: -4px;
  bottom: -4px;
  left: -4px;
}
.ldbar .ldbardiv {
  position: absolute;
  border-radius: 10px;
  top: 0;
  right: 100%;
  bottom: 0;
  left: 0;
  background: red;
  width: 0;
  animation: borealisBar 1s linear infinite;
}
@keyframes borealisBar {
  0% {
    left: 0%;
    right: 100%;
    width: 0%;
  }
  10% {
    left: 0%;
    right: 75%;
    width: 25%;
  }
  90% {
    right: 0%;
    left: 75%;
    width: 25%;
  }
  100% {
    left: 100%;
    right: 0%;
    width: 0%;
  }
}
#salesData {
  color: #fff !important;
}
.content-wrapper h6 {
  color: #fff;
}
.btn-primary-modifier {
  background: #E83500 !important;
  color: #fff !important;
}
.badge-overlay {
  position: absolute;
  left: -15px;
  top: -15px;
  width: 40px;
  height: 40px;
  z-index: 100;
  -webkit-transition: width 1s ease, height 1s ease;
  -moz-transition: width 1s ease, height 1s ease;
  -o-transition: width 1s ease, height 1s ease;
  transition: width 0.4s ease, height 0.4s ease;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .badge-overlay {
    left: -10px;
    top: -10px;
  }
}
.top-left {
  position: absolute;
  top: 0;
  left: 0;
  -ms-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -webkit-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -ms-transform-origin: top right;
  -webkit-transform-origin: top right;
  transform-origin: top right;
}
.badge {
  margin: 0;
  padding: 5px;
  color: white;
  line-height: 1;
  background: #01b901;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: pre-wrap;
}
.percentageOff {
  background: #01b901;
  padding: 2px;
  width: 100px;
  text-align: center;
  color: #fff;
  border-radius: 2px;
}
.star-rating i {
  color: #F79420;
}
.shimmer {
  background-repeat: no-repeat;
  animation: shimmer 2.5s infinite;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.notificationContainer {
  margin-top: 150px !important;
  padding-bottom: 2rem;
}
.notificationContainer #notificationForm label {
  color: #fff !important;
}
.notificationContainer #notificationForm input {
  height: auto !important;
}
.notificationContainer #notificationForm #btnSend {
  width: 200px;
  background: #E83500;
  color: #fff;
  border: 1px solid #E83500;
  display: block;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .notificationContainer #notificationForm #btnSend {
    width: 100%;
  }
}
.list_price {
  color: #FF4B33;
}
.offer_price {
  color: #212121;
}
#buyNow {
  width: 100%;
}
.ebook_detail .book_info .book_variants a.card .card-body {
  min-height: 130px;
}
.ebooks .global-search button {
  position: relative;
  z-index: 10;
  width: 48px;
  height: 48px;
  margin-left: -48px;
  color: #000000b3 !important;
}
.global-search {
  display: none !important;
  width: 96%;
  margin: 0 auto;
}
#search-book-store {
  height: 40px !important;
}
.ebooks .global-search button {
  background: #E83500 !important;
  opacity: 1;
  height: 40px;
}
.affiliationPrices {
  margin-top: 10px;
  width: fit-content;
  background: #fff;
  padding: 15px;
  border-radius: 10px;
}
.affiliationPrices_title p {
  font-weight: 500;
  color: #000 !important;
}
.affiliationPrices .affiliationLinks {
  display: flex;
  gap: 1rem;
  margin-top: 10px;
}
.affiliationPrices .affiliationLinks .fieldSet {
  position: relative;
  border: 1px solid orange;
  padding: 8px;
  width: 140px;
  border-radius: 5px;
}
.affiliationPrices .affiliationLinks .fieldSet:hover {
  border: 1px solid orange;
}
.affiliationPrices .affiliationLinks .fieldSet span img {
  width: 60px;
}
.affiliationPrices .affiliationLinks .fieldSet span img.flipkartLogo {
  width: 70px;
  position: relative;
  z-index: 2;
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend {
  position: absolute;
  top: 0;
  margin: -9px 0 0 -0.5rem;
  background: #fff;
  margin-left: 65px;
  width: 55px;
  text-align: center;
  z-index: 1;
  font-size: 18px;
  font-weight: 500;
  color: #000 !important;
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend span {
  margin-left: -4px;
}
.affiliationLinks .fieldSet:hover {
  background: #FFF0CC;
}
.affiliationLinks .fieldSet:hover .fieldSet_legend {
  background: linear-gradient(to bottom, #fff, #FFF0CC);
}
.affiliationLinksLoader {
  border: 1px solid #0003;
  width: 140px;
  height: 50px;
  border-radius: 5px;
  background: linear-gradient(to right, #F6F6F6 8%, #F0F0F0 18%, #F6F6F6 33%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiliationLinksLoader p {
  font-size: 12px;
  font-weight: 500;
  color: #0008 !important;
  text-align: center;
  line-height: initial;
}
.aa:after {
  position: absolute;
  margin-left: 0.1rem;
  content: ' ...';
  animation: loading steps(4) 2s infinite;
  clip: rect(auto, 0px, auto, auto);
}
.badge-info {
  display: initial;
  border-radius: 50px;
  font-size: 10px;
  padding: 3px 5px;
  font-weight: normal;
  background: #17a2b8;
  position: absolute;
  right: 1px;
  top: 1px;
  height: auto;
  width: 60px;
}
@keyframes loading {
  to {
    clip: rect(auto, 20px, auto, auto);
  }
}
