body,
html {
  scroll-behavior: smooth;
}
::-webkit-scrollbar {
  display: none;
}
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.text-primary-modifier {
  color: #000000b3 !important;
}
footer,
.mobile-footer-nav,
.prepjoy-footer {
  display: none !important;
}
.dates-section {
  margin-top: 70px;
}
@media (max-width: 767px) {
  .dates-section {
    padding-bottom: 70px;
    margin-top: 40px;
  }
}
.daily__test-header__subText {
  margin-top: 10px;
}
.dropdowns {
  margin-top: 10px;
}
#examTypeWrapper,
#examGroupWrapper {
  font-weight: bolder;
}
#examGroup,
#examType {
  box-shadow: 0 0 2px #cccccc;
  padding: 8px;
  border-radius: 6px;
}
.form-control {
  height: auto;
}
.form-control:focus {
  box-shadow: 0 0 2px #cccccc;
  border-color: transparent;
}
.navbtnsPrev {
  position: absolute;
  top: 45%;
  left: 32%;
  z-index: 9;
}
.navbtnsNext {
  position: absolute;
  top: 45%;
  left: 68%;
  z-index: 9;
  transition: all 0.3s ease;
}
.previousSlideBtn,
.nextSlideBtn {
  background: transparent;
  border: 2px solid;
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.fa-backward {
  margin-right: 2px;
}
@media (max-width: 768px) {
  .navbtnsPrev {
    left: -10px;
    display: none !important;
  }
  .navbtnsNext {
    left: 92%;
    display: none !important;
  }
}
.daily__test-dates {
  width: 100vw;
  overflow-y: scroll;
  scroll-snap-type: x mandatory;
  scroll-snap-stop: always;
  display: flex;
  gap: 1rem;
  padding: 0px 0 10px 0;
}
@media (max-width: 768px) {
  .daily__test-dates {
    width: 95vw;
  }
}
.daily__test-dates__card {
  width: 180px;
  display: flex;
  flex: none;
  flex-direction: column;
  align-items: center;
  scroll-snap-align: start;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
}
@media (max-width: 768px) {
  .daily__test-dates__card {
    width: 200px;
  }
}
.daily__test-dates__card .card-title {
  width: 100%;
  text-align: center;
  background: #F7941E;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  padding: 5px;
  color: #fff;
  margin-bottom: 5px !important;
}
.daily__test-dates__card .card-title h4 {
  color: #fff;
  font-size: 16px;
}
.daily__test-dates__card .card-options {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 5px;
}
.daily__test-dates__card .card-options button {
  background: transparent;
  border: 1px solid rgba(0, 0, 0, 0.2);
  width: 75%;
  margin: 0 auto;
  text-align: inherit;
  margin-bottom: 6px;
  padding: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.8);
}
.daily__test-dates__card .card-options button img {
  width: 18px;
  margin-right: 5px;
}
.daily__test-dates__card .card-options button:hover {
  background: rgba(247, 148, 32, 0.5);
}
.daily__test-dates__card .card-options button:active {
  transform: scale(0.8);
}
.hightText {
  color: #F7941E;
}
.mcq__ebooks-cards {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-top: 1rem;
  overflow-x: scroll;
  padding: 10px 0 10px 0;
  scroll-snap-type: x mandatory;
  scroll-snap-stop: always;
}
@media (max-width: 768px) {
  .mcq__ebooks-cards {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    padding: 0px;
  }
}
.mcq__ebooks-cards__card {
  border: 1.2px solid rgba(0, 0, 0, 0.14);
  border-radius: 10px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.14);
  cursor: pointer;
  width: 175px;
  transition: all 0.2s ease-in;
  background: #fff;
  scroll-snap-align: start;
}
.mcq__ebooks-cards__card .books__card-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 175px;
  padding-top: 12px;
  padding-left: 8px;
  padding-right: 8px;
}
.mcq__ebooks-cards__card .books__card-img img {
  width: 100%;
  border-radius: 5px;
  height: 175px;
}
.mcq__ebooks-cards__card .books__card-details {
  margin-top: 10px;
  padding: 10px 10px 0 10px;
}
.mcq__ebooks-cards__card .books__card-details__title {
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 0;
  white-space: nowrap;
}
.mcq__ebooks-cards__card .books__card-details__publisher {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
}
.mcq__ebooks-cards__card .books__card-details .books-details__listPrice {
  text-decoration: line-through;
  color: #ee3539;
  font-weight: 400;
  font-size: 18px !important;
}
.mcq__ebooks-cards__card .books__card-details__cart {
  margin-top: 4px;
  margin-bottom: 7px;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn {
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.67);
  background: transparent;
  border-radius: 4px !important;
  width: 100%;
  transition: background 0.2s ease-in;
  justify-content: center;
  color: #000 !important;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn img {
  margin-right: 3px;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn:hover {
  background: #000000b3;
  border: 1px solid #000000b3;
  color: #fff !important;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn:hover img {
  filter: contrast(4) invert(1);
  -webkit-filter: contrast(4) invert(1);
}
.uncover {
  width: 100%;
  height: 175px;
}
.startHereText {
  font-size: 18px;
}
.scrollBtn button {
  background: transparent;
  border: 2px solid rgba(0, 0, 0, 0.3);
  width: 35px;
  height: 35px;
  border-radius: 50%;
}
.scrollBtn button .fa-arrow-left,
.scrollBtn button .fa-arrow-right {
  color: rgba(0, 0, 0, 0.3);
}
@media (max-width: 768px) {
  .scrollBtn button {
    display: none;
  }
}
#datesCardList {
  scroll-behavior: smooth;
}
#scrollRight {
  margin-left: 15px;
}
#scrollLeft {
  margin-right: 15px;
}
.datesUIWrapper {
  position: relative;
}
.scrollBtnRight {
  position: absolute;
  right: 14%;
}
@media (max-width: 1600px) {
  .scrollBtnRight {
    right: 10%;
  }
}
@media (max-width: 1480px) {
  .scrollBtnRight {
    right: 8%;
  }
}
@media (max-width: 1400px) {
  .scrollBtnRight {
    right: 4%;
  }
}
@media (max-width: 1300px) {
  .scrollBtnRight {
    right: 0%;
  }
}
.scrollBtnLeft {
  position: absolute;
  left: 14%;
}
@media (max-width: 1600px) {
  .scrollBtnLeft {
    left: 10%;
  }
}
@media (max-width: 1480px) {
  .scrollBtnLeft {
    left: 8%;
  }
}
@media (max-width: 1400px) {
  .scrollBtnLeft {
    left: 4%;
  }
}
@media (max-width: 1300px) {
  .scrollBtnLeft {
    left: 0%;
  }
}
.dropdownslist {
  background-image: url(/assets/prepJoy/shiny-5.svg);
  position: relative;
  padding: 4rem;
  padding-top: 1rem !important;
}
.datesUIWrapper {
  overflow-x: scroll;
  position: static;
}
#mcqbooksblock {
  background: #fff;
  padding: 4rem;
}
.leaderBoard {
  padding: 4rem;
}
#scrollLeft,
#scrollRight {
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.25s ease-in;
}
#scrollLeft:active {
  transform: scale(0.8);
}
#scrollRight:active {
  transform: scale(0.8);
}
.showmoreCard {
  position: relative;
}
.showmoreCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  border-radius: 10px;
}
.showmoreCard * {
  z-index: 10;
  color: #fff;
  font-size: 17px;
}
.mcq__ebooks-cards__card {
  overflow: hidden;
}
.showmoreCard {
  transition: all 0.3s ease-in;
}
.showmoreCard:hover {
  transform: scale(1.2);
}
@media screen and (max-width: 768px) {
  .leaderBoard {
    padding: 2rem 1rem 2rem 1rem;
  }
  .dropdownslist {
    padding: 2rem 1rem 2rem 1rem;
  }
  #mcqbooksblock {
    padding: 2rem 1rem 2rem 1rem;
  }
  .leaderBoard__wrapper-contents {
    padding: 0 10px 20px 10px;
  }
  .container {
    padding: 0;
  }
  .card__sec-1 h4 {
    font-size: 11px !important;
    margin-top: 10px;
  }
  .card__sec-1 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
  .fa-solid {
    font-size: 1.5rem;
  }
}
