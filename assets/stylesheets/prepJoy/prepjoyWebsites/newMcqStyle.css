body,
html {
  scrollbar-width: none;
  overflow: hidden;
}
.btn:focus {
  box-shadow: none;
}
::-webkit-scrollbar {
  width: 0;
  /* Remove scrollbar space */
  background: transparent;
  /* Optional: just make scrollbar invisible */
}
* {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.container-custom {
  width: calc(100% - 50%);
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  .container-custom {
    width: 100%;
  }
}
.main-page {
  height: 85vh;
}
@media screen and (max-width: 767px) {
  .main-page {
    width: 100%;
  }
}
@media screen and (min-width: 1200px) {
}
.main-page .webMcq-instruction {
  padding: 1rem;
  text-align: center;
}
.main-page__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media screen and (max-width: 767px) {
  .main-page__header {
    flex-direction: column;
    align-items: start;
  }
}
.main-page__header-title {
  display: flex;
  flex-direction: column;
}
.main-page__header-title .mcq-name {
  text-transform: uppercase;
}
.main-page__sectionDetails {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  padding: 10px;
  border-radius: 10px;
}
.main-page__sectionDetails-description {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.main-page__sectionDetails-description p {
  font-weight: 700;
}
.main-page__sectionDetails table thead {
  text-align: center;
}
.main-page__sectionDetails table tbody {
  text-align: center;
}
.main-page .lang {
  width: 100%;
  height: 42px;
  margin-bottom: 5px;
}
.main-page__startButton {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  margin-left: 10px;
  gap: 1rem;
}
.main-page__startButton-button {
  display: block;
  width: 100%;
}
.main-page__startButton-button.continue {
  background: #F79420;
  color: white;
}
.pointsSection {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media screen and (max-width: 767px) {
  .pointsSection {
    grid-template-columns: repeat(1, 1fr);
  }
}
.info-det {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.info-det .answered {
  background: #0AAEF9;
}
.info-det .review {
  background: #A056E5;
  position: relative;
}
.info-det .review i {
  color: orange;
  position: absolute;
  transform: rotate(45deg);
  top: -6px;
  left: 5px;
}
.info-det .not-ans {
  background: lightgray;
}
.info-det .info-color_card {
  width: 25px;
  height: 25px;
  border-radius: 5px;
  display: flex;
  align-items: center;
}
.info-det li {
  list-style: none;
}
.preview-quiz {
  padding: 12px !important;
  height: auto;
  flex-direction: column;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
}
.preview-quiz__header {
  background: #ffffff;
  border-bottom-right-radius: 20px;
  border-bottom-left-radius: 20px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
}
.preview-quiz .section-selection {
  margin: 20px 10px;
}
.preview-quiz .previewTab {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 10px;
  margin: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  min-height: auto;
  max-height: 500px;
  overflow-y: scroll;
  padding: 12px;
  flex-wrap: wrap;
}
@media (max-width: 767px) {
  .preview-quiz .previewTab {
    height: auto;
  }
}
.preview-quiz #total-que-tab {
  display: flex;
  flex-wrap: wrap;
}
.preview-quiz #total-que-tab .num-wrapper {
  padding: 12px;
  background: #EFEFF1;
  margin: 10px 10px;
  border-radius: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px;
  height: 35px;
  border: 1px solid rgba(0, 0, 0, 0.28);
  font-size: 14px;
}
.preview-quiz #total-que-tab .num-wrapper.answered {
  background: #0AAEF9;
}
.preview-quiz #total-que-tab .num-wrapper.reviewed {
  background: #A056E5;
  border-color: #962ef7;
  color: #fff;
}
.preview-quiz #total-que-tab #attempt-status {
  display: none;
}
.app-header {
  background: #ffffff;
  min-height: 150px;
  border-bottom-right-radius: 15px;
  border-bottom-left-radius: 15px;
  padding: 12px;
}
@media (max-width: 767px) {
  .app-header {
    padding: 0 5px;
    min-height: 175px;
  }
}
.app-header__questionsQue {
  margin-top: 10px;
}
.app-header__questionsQue #quesList {
  font-style: italic;
  color: black;
  font-size: 14px;
}
.app-header__questionsQue .questionProgress {
  height: 10px;
  border-radius: 10px;
  position: relative;
  transition: all 0.3s ease;
}
.app-header__questionsQue .questionProgress__bar {
  background: red;
  height: 8px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-radius: 10px;
}
.quizes {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  position: relative;
  min-height: 410px;
  max-height: 100%;
}
.quizes .swiper-container {
  padding: 20px !important;
  padding-bottom: 5px !important;
}
.quizes .swiper-container .question-wrapper {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.quizes .swiper-container .question-wrapper .que_text {
  font-size: 14px;
  margin-bottom: 0px!important;
  width: 100%;
  line-height: 24px;
  margin-top: 10px;
}
.quizes .swiper-container .question-wrapper .directions {
  background: beige;
  padding: 10px;
  border-radius: 10px;
  margin-bottom: 16px;
}
.quizes .swiper-container .question-wrapper .directions p {
  font-size: 14px;
  line-height: 24px;
}
.quizes .swiper-container .question-wrapper .btn-review {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 30px;
  border-radius: 50%;
  position: absolute;
  top: -12px;
  right: 10px;
}
.quizes .swiper-container .question-wrapper .btn-review .material-icons {
  font-size: 16px !important;
  transform: rotate(45deg);
}
.show-google-explanation {
  background: #E2E6FD;
}
.que-options-wrapper .option {
  padding: 12px;
  background: #EFEFF1;
  margin: 10px 0;
  border-radius: 10px;
  text-align: center;
}
.que-options-wrapper .option.correct {
  background: #42B538;
  color: #fff;
}
.que-options-wrapper .option.incorrect {
  background: #FF4141;
  color: #fff;
}
.que-options-wrapper .option.clicked {
  background: #2980b9;
}
.que-options-wrapper .show-video-explanation {
  display: flex;
  align-items: center;
  margin: 0!important;
  background: #E2E6FD;
}
.btn-review span {
  display: none;
}
.btn-review.marked {
  color: #ffffff;
  background: #FF4141;
}
.btn-review:focus {
  box-shadow: none;
}
.app-footer {
  position: sticky;
  top: 0;
  bottom: 40px;
  padding: 10px;
  margin: 0;
}
.app-footer #btn-next {
  display: block;
  background: #F79420 !important;
  color: #fff !important;
  color: #000;
  width: 100%;
}
.submitreviewModal {
  gap: 2rem;
  justify-content: center;
  align-items: center;
  text-align: center;
  margin-top: 40px;
}
.submitreviewModal .review-wrap {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.submitreviewModal .review-wrap .box {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid black;
  text-align: center;
}
.submitreviewModal .review-wrap .box p {
  margin-bottom: 0!important;
}
.submitreviewModal .review-wrap .box.green {
  background: #0AAEF9;
  border: 1px solid #0AAEF9;
}
.submitreviewModal .review-wrap .box.yellow {
  background: #A056E5;
  border: 1px solid #A056E5;
}
.submitreviewModal .review-wrap .box.blue {
  background: white;
  color: black;
}
.explanation-wrapper {
  display: none;
  background: #fff;
  min-height: 400px;
  position: sticky;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  overflow-y: scroll;
  border: 1px solid rgba(0, 0, 0, 0.2);
}
@media (max-width: 767px) {
  .explanation-wrapper {
    min-height: 250px;
  }
}
.explanation-wrapper .explanation-header {
  background: #ffffff;
  min-height: 50px;
  padding: 1rem;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -10px 10px -10px #201a40;
  position: sticky;
  top: 0;
}
.explanation-wrapper .explanation-header h4 {
  color: #000;
  font-size: 16px;
}
.explanation-wrapper .explanation-header i {
  color: white;
  background: #151515;
  cursor: pointer;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 20px;
}
.explanation-wrapper .answer-explanation {
  padding: 0.5rem;
  max-height: 100vh;
  overflow-y: auto;
}
.explanation-wrapper .answer-explanation h4 {
  font-size: 14px;
  color: #201a40;
}
.video-explanation-wrapper {
  display: none;
  background: #fff;
  min-height: 400px;
  position: sticky;
  bottom: 60px;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}
@media (max-width: 767px) {
  .video-explanation-wrapper {
    min-height: 250px;
  }
}
.video-explanation-wrapper .explanation-header {
  background: #ffffff;
  min-height: 50px;
  padding: 1rem;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -10px 10px -10px #201a40;
}
.video-explanation-wrapper .explanation-header h4 {
  color: #000;
  font-size: 16px;
}
.video-explanation-wrapper .explanation-header i {
  color: white;
  background: #151515;
  cursor: pointer;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 20px;
}
.video-explanation-wrapper .answer-explanation {
  padding: 0.5rem;
  max-height: 100vh;
  overflow-y: auto;
}
.video-explanation-wrapper .answer-explanation h4 {
  font-size: 14px;
  color: #201a40;
}
.video-explanation-wrapper #videoExplanation iframe {
  width: 100%;
  min-height: 400px;
}
@media (max-width: 767px) {
  .video-explanation-wrapper #videoExplanation iframe {
    min-height: 250px;
  }
}
#review-modalDialog .modal-footer {
  justify-content: center;
}
#review-modalDialog .modal-footer button {
  width: 100%;
}
#review-modalDialog .modal-footer button.btn-submit {
  background: #F79420;
  color: white;
}
.practice-result {
  display: none;
  height: 90vh;
  overflow-y: scroll;
}
.practice-result .result-header {
  background: #ffffff;
  padding: 1rem;
}
.practice-result .result-header h2 {
  cursor: pointer;
}
.practice-result .score-media {
  background: #ffffff;
  border-bottom-right-radius: 25px;
  border-bottom-left-radius: 25px;
  height: 115px;
  display: flex;
  justify-content: center;
  margin-top: -20px;
  margin-bottom: 20px;
}
.practice-result .score-media div {
  height: 50%;
}
.practice-result .score-media div p {
  margin-bottom: 0!important;
}
.practice-result .score-media div .score-text {
  font-size: 16px;
  font-weight: bold;
}
.practice-result .score-media div .score-wrap {
  font-size: 2rem;
  font-weight: bold;
  margin-top: 10px;
}
.practice-result .score-media div .score-wrap #score,
.practice-result .score-media div .score-wrap #noofque {
  font-size: 2rem;
}
.practice-result .analysis {
  margin-top: 30px;
}
.practice-result .practice_score_info {
  margin-top: 0!important;
}
.practice-result .practice_score_info .next-match {
  display: flex;
  justify-content: center;
  margin: 20px !important;
}
.practice-result .practice_score_info .next-match button {
  width: 100%;
}
.practice-result .practice_score_info .next-match button#retryBtn {
  background: #F79420;
  border-color: transparent;
  color: #fff !important;
}
.practice-result .test-summary {
  margin: 0 20px;
  border-radius: 10px;
  padding: 1rem;
}
@media (max-width: 767px) {
  .practice-result .test-summary {
    margin: 0!important;
  }
}
.nav-tabs {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
  border-bottom: none;
  position: sticky;
  background: #060029;
  top: -20px;
  z-index: 999;
  box-shadow: 0 0 10px #999;
  padding: 1rem 0 0 0;
}
.nav-tabs li {
  width: 140px;
}
.nav-tabs li a {
  color: #fff !important;
  display: block;
  width: 100%;
  text-align: center;
  text-decoration: none;
}
.nav-tabs {
  box-shadow: none;
  margin-top: 1rem;
  box-shadow: 0 2px 3px -3px #000000;
}
.nav-tabs li a {
  padding-bottom: 10px;
}
@media (max-width: 830px) {
  .nav-tabs .mrkrTab {
    width: 130px;
  }
}
@media (max-width: 767px) {
  .nav-tabs {
    display: grid !important;
    grid-template-columns: repeat(5, 1fr);
    overflow-x: scroll;
    align-items: center;
  }
  .nav-tabs li {
    width: 108px;
  }
  .nav-tabs .mrkrTab {
    width: 154px;
  }
}
.tab-wrapper {
  margin: 0;
}
.tab-wrapper .tab-content {
  margin-top: 1rem;
}
.tab-wrapper .tab-content .tab-pane {
  min-height: 100px;
}
.tab-wrapper .tab-content .mcq-answers {
  border-radius: 15px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  padding: 10px;
  margin: 0 0 20px 0;
}
.tab-wrapper .tab-content .mcq-answers .question-wrapper {
  box-shadow: none;
}
.btn-outline-secondary:focus {
  color: #000;
  background: transparent;
}
.btn-outline-secondary:hover {
  color: #000;
  background: transparent;
}
.btn-outline-secondary:active {
  color: #000;
  background: transparent;
}
.head-text {
  text-align: start!important;
  cursor: pointer;
  color: #F79420;
  display: flex;
  align-items: center;
  font-size: 16px!important;
  margin-bottom: 12px;
}
.mcq-answers #que-no,
.mcq-answers .que_text {
  text-align: center;
}
.validate-answers {
  padding: 1rem;
}
.box-button {
  text-align: center;
  padding: 1rem;
  width: 150px;
  justify-content: center;
  border-radius: 10px;
  color: #fff;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
}
@media (max-width: 767px) {
  .box-button {
    margin-bottom: 10px;
  }
}
.box-button.green {
  background: green;
}
.box-button.red {
  background: red;
}
.box-button.yellow {
  background: orange;
}
.action-wrapper {
  display: none;
  background: #999;
  min-height: 300px;
  position: sticky;
  z-index: 999;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}
.action-wrapper .action-explanation {
  padding: 0.5rem;
  max-height: 100vh;
  overflow-y: auto;
  padding-bottom: 6rem;
}
.action-wrapper .action-explanation h4 {
  font-size: 14px;
  color: #201a40;
}
.action-wrapper .action-explanation #explanation {
  color: #201a40;
  padding-bottom: 10px;
  font-size: 14px;
}
.action-header {
  background: #213247;
  min-height: 80px;
  padding: 1rem;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -10px 10px -10px #201a40;
}
.action-header h4 {
  color: #42B538;
  font-size: 16px;
}
.action-header i {
  color: #fff;
  background: #201a40;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: CENTER;
  border-radius: 4px;
  font-size: 20px;
}
.action-header p {
  color: #fff;
}
.acw-header {
  text-align: end;
  padding: 10px 20px 0 0;
}
.acw-header .close-buttons {
  color: #fff;
  cursor: pointer;
}
.action-wrapper,
.explanation-wrapper,
.video-explanation-wrapper,
#btn-prev,
#slider,
.badgesWrapper,
.balance-points,
.button-results,
#btn-play {
  display: none;
}
.action-explanation .button-results {
  display: flex;
  flex-direction: column;
  margin: 0 20px;
}
.action-explanation .button-results .btn-playagain {
  margin: 10px 0;
}
.answer-wrapper {
  display: none !important;
}
@media screen and (min-width: 768px) {
  .main-page__startButton button {
    width: 200px;
  }
  .preview-quiz__header {
    display: none !important;
  }
  #total-que-tab {
    top: 40px !important;
  }
  .app-header {
    margin-top: 0!important;
  }
}
.num-text {
  display: flex;
  width: 10px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.num-wrapper .num-text i {
  transform: rotate(45deg);
  position: relative;
  top: -12px;
  left: 2px;
  color: orange;
}
.btn-review i {
  transform: rotate(45deg);
}
div#review-modalDialog {
  z-index: 9999;
}
.que-options-wrapper {
  cursor: pointer;
}
h4.validate-answers {
  text-align: center;
}
.num-wrapper.reviewed .num-text {
  justify-content: unset !important;
}
.checkbox-custom,
.radio-custom {
  opacity: 0;
  position: absolute;
}
.checkbox-custom,
.checkbox-custom-label,
.radio-custom,
.radio-custom-label {
  display: inline-block;
  vertical-align: middle;
  margin: 5px 0;
  cursor: pointer;
}
.checkbox-custom-label,
.radio-custom-label {
  position: relative;
}
.checkbox-custom + .checkbox-custom-label:before,
.radio-custom + .radio-custom-label:before {
  content: '';
  background: #fff;
  border: 2px solid #ddd;
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  padding: 2px;
  margin-right: 10px;
  text-align: center;
}
.checkbox-custom:checked + .checkbox-custom-label:before {
  content: "\f00c";
  font-family: 'FontAwesome';
  background: rebeccapurple;
  color: #fff;
}
.radio-custom + .radio-custom-label:before {
  border-radius: 50%;
}
.radio-custom:checked + .radio-custom-label:before {
  content: "\f00c";
  font-family: 'FontAwesome';
  font-size: 12px;
  color: white;
  background: green;
  border: none;
}
.checkbox-custom:focus + .checkbox-custom-label,
.radio-custom:focus + .radio-custom-label {
  outline: 1px solid #ddd;
  /* focus style */
}
input[type="radio"]:checked + label {
  background-color: unset !important;
  color: unset !important;
}
label.radio-custom-label:focus-visible {
  outline: none !important;
}
.inner-containner {
  box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  padding: 10px;
}
.questions-card {
  box-shadow: 0px 2px 40px 0px rgba(0, 0, 0, 0.2);
  border-radius: 20px;
  width: 50%;
  margin: auto;
  padding: 2%;
  margin-top: 5%;
  margin-bottom: 5%;
}
.first-header {
  padding: 5px;
  width: 90%;
  margin: auto;
  border-radius: 20px;
}
.first-header img {
  width: 25px;
  margin-right: 10px;
}
.first-header h3 {
  margin: 0;
  font-size: 18px;
}
.first-header span {
  padding-left: 2%;
  font-size: 16px;
}
.button-container-selector {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
.button-container-selector a {
  background: #ff9b26;
  color: white;
  text-decoration: none !important;
  padding: 5px 30px;
  border-radius: 20px;
  font-size: 16px !important;
}
.button-container-selector a:hover {
  color: white;
}
input.text-input--1 {
  border: 1px solid grey;
}
@media (max-width: 575px) {
  .questions-card {
    width: 100% !important;
  }
}
.selector-all h4 {
  margin: 5%;
}
.selector-all p {
  color: #666666;
  font-weight: 700;
}
.show-select h2 {
  font-size: 16px;
  margin-top: 10px;
}
.two-box-container input.text-input--1 {
  margin-left: 5px;
}
.two-box-container p {
  margin-left: 7px;
}
.selections-Header h3 {
  margin: 0;
  margin-left: 15px;
}
.selections-Header {
  background: #ffffff;
  padding: 15px;
}
.selections-Header h2 {
  cursor: pointer;
  display: flex;
  align-items: center;
  margin: 0;
}
.selections-Header h2 i {
  font-size: 2.5rem;
}
.selectErr.text-center {
  color: red;
  margin-top: 2%;
}
.que-options .option span {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.que-options .option span img {
  width: 100%;
}
p.total-que.d-flex.align-items-center span {
  padding-left: 3px;
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.app-footer .question-no {
  padding-left: 2%;
  color: grey;
}
#sharebtn i.material-icons {
  width: 0px;
}
.result-header i {
  cursor: pointer;
}
.app-footer {
  display: flex;
}
.app-footer div {
  display: flex;
  width: 100%;
}
.app-footer #btn-prev {
  width: 100%;
  border: 1px solid;
}
.app-header i {
  cursor: pointer;
}
div#closeTest .modal-body {
  text-align: center;
  font-size: 20px;
  font-family: Quicksand, sans-serif;
  font-weight: 700;
}
div#closeTest .modal-footer {
  border: none !important;
  display: flex;
  justify-content: center;
  align-items: center;
}
div#closeTest .modal-footer button {
  width: 300px;
}
div#closeTest {
  z-index: 100000;
}
body.arihant div#rank-dialog {
  z-index: 10000 !important;
}
body.arihant.radian_books div#rank-dialog {
  z-index: 10000 !important;
}
.explanation-wrappers {
  overflow-x: scroll;
}
body.arihant.radian_books #closeTest {
  z-index: 9999;
}
@media (max-width: 400px) {
  h4.quizTypeName.mb-0 {
    font-size: 15px;
  }
}
.libwonder .button-container-selector a,
.libwonder .main-page__startButton-button.continue,
.libwonder .app-footer #btn-next,
.arihant.radian_books .button-container-selector a,
.arihant.radian_books .main-page__startButton-button.continue,
.arihant.radian_books .app-footer #btn-next {
  background: #367AD8 !important;
}
.libwonder #review-modalDialog .modal-footer button.btn-submit,
.libwonder .practice-result .practice_score_info .next-match button#retryBtn {
  background: #367AD8 !important;
  color: white !important;
}
.arihant.radian_books #review-modalDialog .modal-footer button.btn-submit,
.arihant.radian_books .practice-result .practice_score_info .next-match button#retryBtn {
  background: #367AD8 !important;
  color: white !important;
}
.que_text {
  overflow-x: scroll !important;
}
.nav-tabs {
  border-bottom: none !important;
}
div::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, Opera */
}
.app-footer {
  margin-top: -30px !important;
  display: flex;
  gap: 1rem;
}
@media screen and (max-width: 768px) {
  .app-footer {
    margin-top: auto !important;
  }
}
.swiper-container {
  overflow-y: scroll;
}
.prepjoy_cta,
.mobile-footer-nav,
footer {
  display: none !important;
}
.profile .img {
  width: 80px !important;
}
.main-page__sectionDetails {
  overflow-x: scroll;
  width: 100%;
}
.section-result {
  background: #fff;
  margin: 0 20px 30px 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  padding: 1rem;
}
.section-result .section-result__table {
  overflow-x: scroll;
}
.section-result .section-result__table th {
  text-align: center;
}
.webMcq {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.selector-all {
  isplay: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin: 0 auto;
  flex-wrap: wrap;
  background: #fff;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.15);
  flex-direction: column;
  width: 50%;
}
@media screen and (max-width: 767px) {
  .selector-all {
    width: 100%;
  }
}
.selector-all label {
  width: 100%;
  padding: 10px;
  border: 1px solid #c8c5c5;
  border-radius: 5px;
}
#prep-report-question .prep-report-modal .chkbox {
  display: flex;
  align-items: center;
}
#prep-report-question .prep-report-modal label {
  display: flex;
  align-items: center;
  color: #000;
  cursor: pointer;
  position: relative;
}
#prep-report-question .prep-report-modal label span {
  display: inline-block;
  position: relative;
  background-color: transparent;
  width: 15px;
  height: 15px;
  transform-origin: center;
  border: 2px solid #333;
  border-radius: 50%;
  vertical-align: -6px;
  margin-right: 10px;
  transition: background-color 150ms 200ms, transform 350ms cubic-bezier(0.78, -1.22, 0.17, 1.89);
}
#prep-report-question .prep-report-modal label span:before {
  content: "";
  width: 0px;
  height: 2px;
  border-radius: 2px;
  background: #000;
  position: absolute;
  transform: rotate(45deg);
  top: 6px;
  left: 2px;
  transition: width 50ms ease 50ms;
  transform-origin: 0% 0%;
}
#prep-report-question .prep-report-modal label span:after {
  content: "";
  width: 0px;
  height: 2px;
  border-radius: 2px;
  background: #fff;
  position: absolute;
  transform: rotate(305deg);
  top: 9px;
  left: 3px;
  transition: width 50ms ease;
  transform-origin: 0% 0%;
}
#prep-report-question .prep-report-modal .form-check-label {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  color: #000;
  font-size: 14px;
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 0 0 1px #423e3e;
  margin-top: 1rem;
}
#prep-report-question .prep-report-modal input[type="checkbox"] {
  visibility: hidden;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label {
  color: #DA0101;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label span {
  background-color: #DA0101;
  transform: scale(1.05);
  border: 2px solid #DA0101;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label span:after {
  width: 10px;
  background: #fff;
  transition: width 150ms ease 100ms;
}
#prep-report-question .prep-report-modal input[type="checkbox"]:checked + label span:before {
  width: 5px;
  background: #fff;
  transition: width 150ms ease 100ms;
}
#prep-report-question .prep-report-modal label,
#prep-report-question .prep-report-modal input,
#prep-report-question .prep-report-modal p,
#prep-report-question .prep-report-modal textarea {
  font-size: 14px;
  color: #000;
  font-weight: lighter !important;
}
#prep-report-question .prep-report-modal .prep-report-icon i {
  font-size: 18px;
  color: #E83500;
}
#prep-report-question .prep-report-modal .form-control:focus {
  border: 1px solid #999;
  box-shadow: none;
}
.show-select {
  width: 100%;
  margin-top: 20px;
}
.show-select .inner-container-show-1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  width: 100%;
}
.show-select .inner-container-show-1 div {
  width: 100%;
}
.two-box-container {
  width: 100%;
}
.two-box-container div {
  width: 100%;
}
.qz-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
}
@media screen and (max-width: 767px) {
  .qz-wrap {
    grid-template-columns: repeat(1, 1fr);
  }
}
.qz-wrap .pointsDetails-cards {
  display: flex;
  gap: 1rem;
  align-items: center;
  text-align: center;
}
.qz-wrap .pointsDetails-cards h3 {
  font-size: 20px;
}
.qz-wrap .pointsDetails-cards h5 {
  font-size: 16px;
}
.pts {
  background: #fff;
  width: 90%;
  position: relative;
  top: -40px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}
@media screen and (min-width: 1200px) {
  .pts {
    width: 30%;
  }
}
#prep-report-question,
#report-success {
  z-index: 999999;
}
#sectionSelectOption {
  border: 1px solid #999;
}
.practice-container {
  height: 90vh;
}
.app__header-submit {
  height: 35px;
}
.app__header-submit-2 {
  height: 30px;
}
@media (max-width: 820px) {
  .app__header-submit-2 {
    margin-top: 12px;
  }
}
.quizTypeName {
  width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 820px) {
  .quizTypeName {
    width: 130px;
  }
}
@media (max-width: 820px) {
  #mobSelect {
    width: 175px;
  }
}
.btn-submitQuiz {
  width: 100px;
  background: transparent;
  border: 1px solid #F79420 !important;
  transition: all 0.2s ease-in;
  height: 30px;
  color: #000;
}
.btn-submitQuiz:hover {
  background: #F79420;
  color: #fff !important;
}
.btn-submitQuiz-mob {
  background: transparent;
  border: 2px solid red;
  font-weight: bolder;
  border-radius: 50%;
  height: 25px;
  width: 25px;
  cursor: pointer;
  color: #000;
}
mjx-math,
.MJX-TEX {
  white-space: pre-wrap !important;
}
.reporticon {
  font-size: 12px;
  text-decoration: none;
  color: #ffb3bd;
}
.reporticon:hover {
  text-decoration: none;
  color: #ffb3bd;
}
.close:focus {
  border: none !important;
  outline: none !important;
}
.validate-answers {
  font-size: 1rem;
}
.nav-tabs li a.active {
  border-bottom: 3px solid #F79420;
}
#graphicAnalysis svg {
  border-radius: 5px;
}
#right,
#wrong,
#skipped {
  color: #fff;
}
.show-google-explanation,
.show-video-explanation {
  background: transparent !important;
  color: #F79420;
  padding: 0 !important;
}
.markedOrange {
  background: #F79420 !important;
}
.ws_result {
  margin: 1rem auto;
  padding: 1rem;
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}
.ws_result-header {
  border-radius: 10px;
  color: #fff;
}
.ws_result-header_content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px;
  margin-top: 20px;
}
.ws_resultPage-title {
  display: flex;
  gap: 1rem;
}
.ws_resultPage-title h2 {
  font-size: 1.7rem;
  font-weight: 700;
  color: #9c9c9c;
  line-height: 1.125;
  margin-bottom: 8px;
}
.ws_result-header_chart {
  display: flex;
  justify-content: center;
  flex-direction: column;
}
.ws_result-header_points {
  display: flex;
  flex-direction: column;
  align-items: start;
  margin-top: 10px;
}
.ws_result-header_points h4 {
  color: #9c9c9c;
  font-size: 22px;
}
.ws_result-header_points h4 span {
  color: #fff;
}
.ws_result-header_separator {
  width: 2px;
  height: 100%;
  margin: 0 auto;
  background-image: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.15), transparent);
  position: relative;
  z-index: 9;
}
.ws_result-header_points-list {
  padding: 0;
  list-style: none;
  margin-top: 0.85rem;
  line-height: 2.2rem;
  width: 100%;
}
.ws_result-header_points-list_item {
  display: flex;
  font-size: 18px;
}
.ws_result-header_points-list_item-name {
  width: 85px;
}
.ws_result-header_points-list_item-points {
  margin-left: 5px;
}
.ws_result-header_points-summary {
  border: 2px solid orange;
  padding: 3px;
  background: orange;
  color: #fff;
  font-size: 1.1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 230px;
  margin-top: 15px;
}
.ws_result-header_points-summary:hover {
  background: transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.ws_result-header_points-summary:active {
  transform: scale(0.9);
}
#piechart {
  margin-left: -70px;
}
#piechart svg {
  height: 250px;
}
.ws_result-performance {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.ws_result-performance_header {
  text-align: center;
  margin-bottom: 12px;
}
.practice-result {
  position: relative;
}
.ws_result-summary {
  width: 100%;
  height: 100vh;
  position: absolute;
  background: #F4F5FA;
  transform: translateX(-110%);
  padding: 1rem;
  z-index: 99;
  transition: all 0.3s ease;
  overflow: hidden;
}
.retestButtons {
  gap: 1rem;
}
.retestButtons button {
  padding: 0.35rem;
  width: 100%;
}
.retestBtn {
  background: orange;
  border: 2px solid orange;
  font-weight: 500;
  color: #fff;
}
.reviseBtn {
  background: transparent;
  border: 2px solid orange;
}
#retestModal .modal-content {
  border-radius: 15px;
}
#retestModal.modal-body h4 {
  font-size: 20px;
}
.retestButtonsList {
  gap: 1rem;
}
.retestButtonsList button {
  width: 250px;
  padding: 0.7rem;
  background: transparent;
  border: 1.5px solid rgba(0, 0, 0, 0.2);
  text-align: initial;
}
.checkbox-group > * {
  margin: 0.5rem 0.5rem;
}
.ws_result-performance_title {
  font-size: 1.7rem;
  font-weight: 700;
  color: #9c9c9c;
  line-height: 1.125;
  margin-bottom: 8px;
}
.ws_result-performance_header p {
  color: #9c9c9c;
  line-height: 1.125;
  font-size: 16px;
  font-weight: 500;
}
.checkbox {
  margin: 3px !important;
}
.checkbox-input {
  clip: rect(0 0 0 0);
  clip-path: inset(100%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
.checkbox-input:checked + .checkbox-tile {
  border-color: green;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  color: green;
}
.checkbox-input:checked + .checkbox-tile:before {
  transform: scale(1);
  opacity: 1;
  background-color: green;
  border-color: green;
}
.checkbox-input:checked + .checkbox-tile .checkbox-icon,
.checkbox-input:checked + .checkbox-tile .checkbox-label {
  color: green;
}
.checkbox-input:focus + .checkbox-tile:before {
  transform: scale(1);
  opacity: 1;
}
.checkbox-tile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 260px;
  border-radius: 0.5rem;
  border: 1px solid #b5bfd9;
  background-color: #fff;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  transition: 0.15s ease;
  cursor: pointer;
  position: relative;
  padding: 5px;
}
.checkbox-tile:before {
  content: "";
  position: absolute;
  display: block;
  width: 22px;
  height: 22px;
  border: 1px solid #b5bfd9;
  background-color: #fff;
  border-radius: 5px;
  left: 0.25rem;
  opacity: 1;
  transform: scale(1);
  transition: 0.25s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='192' height='192' fill='%23FFFFFF' viewBox='0 0 256 256'%3E%3Crect width='256' height='256' fill='none'%3E%3C/rect%3E%3Cpolyline points='216 72.005 104 184 48 128.005' fill='none' stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='32'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: 12px;
  background-repeat: no-repeat;
  background-position: 50% 50%;
}
.checkbox-tile:hover {
  border-color: green;
}
.checkbox-tile:hover:before {
  transform: scale(1);
  opacity: 1;
}
.checkbox-icon {
  transition: 0.375s ease;
  color: #494949;
  font-size: 16px;
  margin-left: 2rem;
}
.checkbox-icon svg {
  width: 3rem;
  height: 3rem;
}
.checkbox-label {
  color: #707070;
  transition: 0.375s ease;
  text-align: center;
  font-size: 16px;
}
.tab-wrapper {
  height: 81vh;
  overflow: scroll;
}
.ws_result-summary button {
  background: transparent;
  border: none;
  font-weight: bold;
}
@media (max-width: 820px) {
  .tab-wrapper {
    width: 100%;
    height: 85vh;
    padding: 0 !important;
  }
  .ws_result {
    width: 100%;
    margin: 1rem auto;
    padding: 0!important;
  }
  .ws_result-header {
    grid-template-columns: repeat(1, 1fr);
    padding: 12px;
  }
  .ws_result-header_content {
    grid-template-columns: repeat(2, 1fr);
  }
  #piechart {
    width: 100%;
  }
  .ws_result-header_points-summary {
    padding: 0.2rem 0.5rem;
    font-size: 13px;
    width: auto;
    margin-top: 25px;
  }
  .ws_result-header_points {
    margin: 0 0 2rem 0;
    align-items: start;
  }
  .ws_result-header_points-list_item {
    font-size: 14px;
  }
  .ws_result-header_points h4 {
    font-size: 1.5rem;
  }
  .ws_result-summary {
    height: 100vh;
    padding: 0;
  }
  .ws_result-summary .container {
    padding: 4px!important;
  }
  #piechart {
    margin-left: 0px;
  }
  .practice-result {
    margin: 0 5px;
  }
  .ws_result-performance {
    padding-bottom: 10px;
    margin-top: 20px;
  }
  .ws_result-header_points-list {
    margin-top: 0.3rem;
  }
  .ws_result-header_points h4 {
    font-size: 20px;
  }
  .ws_result-header_points h4 span {
    font-size: 20px;
  }
  .ws_result-header_points-list {
    line-height: 1.8rem;
  }
  .ws_result-performance_title {
    font-size: 1.3rem;
  }
  .ws_result-performance_header p {
    font-size: 14px;
  }
}
.sectionSelectDropDown {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 1rem;
}
.sectionSelectDropDown select {
  padding: 5px;
  width: 150px;
  border: 1px solid blue;
  border-radius: 3px;
}
.checkbox-input:disabled + .checkbox-tile {
  opacity: 0.5;
}
.mtg_books .btn-submitQuiz {
  border-color: #6B903A !important;
}
.mtg_books .btn-submitQuiz:hover {
  background: #6B903A !important;
}
.mtg_books .button-container-selector a,
.mtg_books .main-page__startButton-button.continue,
.mtg_books .app-footer #btn-next,
.mtg_books #review-modalDialog .modal-footer button.btn-submit,
.mtg_books .practice-result .practice_score_info .next-match button#retryBtn,
.mtg_books .nav-tabs li a.active {
  background: #6B903A !important;
  color: white !important;
}
.mtg_books .ws_result-header_points-summary,
.mtg_books .retestBtn {
  border-color: #6B903A !important;
  background: #6B903A !important;
}
.mtg_books .reviseBtn {
  border-color: #6B903A !important;
}
.ws_result-books {
  width: calc(100% - 30%);
  margin: 0 auto;
}
@media (max-width: 820px) {
  .ws_result-books {
    width: calc(100% - 5%);
  }
  .ws_result-books .booksTitleHeading {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }
  .ws_result-books .booksTitleHeading a {
    margin-right: -16px;
  }
}
.ws_result-books .recommendedBooks-title {
  color: #9c9c9c;
  margin-bottom: 14px;
}
.ws_result-books .recommendedBooks-title span {
  color: orange;
}
@media (max-width: 820px) {
  .ws_result-books .recommendedBooks-title {
    margin-bottom: 0;
    font-size: 1.6rem;
  }
}
.ws_result-books .ws_result-books_wrapper {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-top: 1rem;
  overflow-x: scroll;
  padding: 10px 0 10px 0;
  scroll-snap-type: x mandatory;
  scroll-snap-stop: always;
}
@media (max-width: 768px) {
  .ws_result-books .ws_result-books_wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    padding: 0px;
  }
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card {
  border: 1.2px solid rgba(0, 0, 0, 0.14);
  border-radius: 10px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.14);
  cursor: pointer;
  width: 175px;
  transition: all 0.2s ease-in;
  background: #fff;
  scroll-snap-align: start;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card .publisherName {
  color: #9c9c9c;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 175px;
  padding-top: 12px;
  padding-left: 8px;
  padding-right: 8px;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card-img img {
  width: 100%;
  border-radius: 5px;
  height: 175px;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card-details {
  display: flex;
  flex-direction: column;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card-details .listPrice {
  font-size: 1.2rem;
  text-decoration: line-through;
  color: red;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card-details .offerPrice {
  font-size: 1.6rem;
  margin-left: 8px;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card .addtoCartButton {
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.67);
  background: transparent;
  border-radius: 4px !important;
  padding: 2px 20px;
  transition: background 0.2s ease-in;
  justify-content: center;
  color: #000 !important;
}
@media (max-width: 820px) {
  .ws_result-books .ws_result-books_wrapper .ws_result-books_card .addtoCartButton {
    width: 75% !important;
  }
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card .addtoCartButton:hover {
  background: #000000b3;
  border: 1px solid #000000b3;
  color: #fff !important;
}
.ws_result-books .ws_result-books_wrapper .ws_result-books_card .addtoCartButton:hover img {
  filter: contrast(4) invert(1);
  -webkit-filter: contrast(4) invert(1);
}
.rUser {
  width: 100% !important;
}
#chart {
  width: 45% !important;
}
@media (max-width: 1400px) {
}
.bookTitle {
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 0;
  white-space: nowrap;
}
@media (max-width: 820px) {
  .ws_result-books_card-details .bookTitle {
    font-size: 1.2rem;
  }
  .playerName {
    text-overflow: ellipsis;
    width: 50px;
    overflow: hidden;
  }
  #chart {
    width: 50vw !important;
    margin: 0 auto;
    margin-right: -10px;
    margin-left: -3px;
    margin-top: -15px;
  }
  .ws_result-header_points-list_item-name {
    width: 65px;
  }
  .ws_result-header_points-list_item-points {
    margin-left: 5px;
  }
  .ws_result-header_points {
    position: relative;
    right: 5px;
  }
  .nav-tabs {
    top: -5px;
  }
}
.mcq__ebooks-cards {
  display: flex;
  gap: 1.28rem;
  flex-wrap: wrap;
}
@media (max-width: 860px) {
  .mcq__ebooks-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    padding: 0px;
  }
}
@media (max-width: 767px) {
  .mcq__ebooks-cards {
    grid-template-columns: repeat(2, 1fr);
    padding: 0px;
  }
}
.mcq__ebooks-cards__card {
  border: 1.2px solid rgba(0, 0, 0, 0.14);
  border-radius: 10px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.14);
  cursor: pointer;
  width: 175px;
  transition: all 0.2s ease-in;
  background: #fff;
  scroll-snap-align: start;
}
@media (max-width: 768px) {
  .mcq__ebooks-cards__card {
    width: auto;
  }
}
.mcq__ebooks-cards__card .books__card-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 175px;
  padding-top: 12px;
  padding-left: 8px;
  padding-right: 8px;
}
.mcq__ebooks-cards__card .books__card-img img {
  width: 100%;
  border-radius: 5px;
  height: 175px;
}
.mcq__ebooks-cards__card .books__card-details {
  margin-top: 10px;
  padding: 10px 10px 0 10px;
}
.mcq__ebooks-cards__card .books__card-details__title {
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-bottom: 0;
  white-space: nowrap;
}
.mcq__ebooks-cards__card .books__card-details__publisher {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
}
.mcq__ebooks-cards__card .books__card-details .books-details__listPrice {
  text-decoration: line-through;
  color: #ee3539;
  font-weight: 400;
  font-size: 18px !important;
}
.mcq__ebooks-cards__card .books__card-details__cart {
  margin-top: 4px;
  margin-bottom: 7px;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn {
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.67);
  background: transparent;
  border-radius: 4px !important;
  width: 100%;
  transition: background 0.2s ease-in;
  justify-content: center;
  color: #000 !important;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn img {
  margin-right: 3px;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn:hover {
  background: #000000b3;
  border: 1px solid #000000b3;
  color: #fff !important;
}
.mcq__ebooks-cards__card .books__card-details__cart .books__details-cartBtn:hover img {
  filter: contrast(4) invert(1);
  -webkit-filter: contrast(4) invert(1);
}
.uncover {
  width: 100%;
  height: 175px !important;
}
.showmoreCard {
  position: relative;
}
.showmoreCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  border-radius: 10px;
}
.showmoreCard * {
  z-index: 10;
  color: #fff;
  font-size: 17px;
}
.mcq__ebooks-cards__card {
  overflow: hidden;
}
.showmoreCard {
  transition: all 0.3s ease-in;
}
.showmoreCard:hover {
  transform: scale(1.2);
}
.modal-fullscreen-xl {
  max-width: 100% !important;
  margin: 0;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100vh;
  display: flex;
  background: transparent !important;
}
.full-screen-dialog {
  max-width: 100% !important;
}
#newBadgeDetails {
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 76%;
}
#newBadgeDetails h2 {
  color: #fff;
}
#newBadgeDetails h4 {
  color: #fff;
  margin-top: 1.2rem;
}
#badgeLottieAnim {
  top: -100px !important;
}
#medalLottieAnim {
  position: relative ;
  top: -400px;
}
.medal-wrappers {
  top: -50px;
}
#newBadgeName {
  font-size: 2rem;
}
#newBadgeName span {
  color: orange;
  display: block;
  font-size: 2.6rem;
}
#medalModal h4,
#medalModal h5 {
  color: #fff;
}
#medalModal h5 {
  margin-bottom: 1rem;
}
#medalModal h4 {
  margin-bottom: 1rem;
}
#medal-upload lottie-player {
  position: static;
  top: 0;
  width: 200px;
  height: auto;
  margin: 0 auto;
}
.newMedalAlign {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150%;
}
#medal-name span {
  font-weight: bolder;
}
#medal-name .goldColor {
  color: gold;
}
#medal-name .silverColor {
  color: silver;
}
#medal-name .bronzeColor {
  color: #CD7F32;
}
.medalSummaryBtn,
.badgeSummaryBtn,
.rankSummaryBtn,
.rankInstSummaryBtn {
  background: #F79420;
  color: #fff;
}
#rankUpdateModal .modal-content,
#rankInstUpdateModal .modal-content {
  background: transparent;
  height: 100vh;
}
#rankDetails,
#rankInstDetails {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
}
#rankDisplayText,
#rankInstDisplayText {
  color: #fff !important;
  font-size: 1.7rem;
  margin-top: -50px;
}
#rankDisplayText span,
#rankInstDisplayText span {
  color: #F79420;
  font-weight: bolder;
}
.ws_result-wrapper {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
@media (max-width: 768px) {
  .ws_result-wrapper {
    grid-template-columns: repeat(1, 1fr);
    padding: 12px;
    grid-gap: 3rem;
  }
}
.arrowAnimation {
  animation: rightArrowAnim 2s ease infinite;
  transition: all 0.3s ease;
}
@media (max-width: 768px) {
  #chart {
    width: 25vw !important;
    margin-right: 0px;
    margin-left: 0px;
    margin-top: -10px;
  }
  .favMcqBtnNav {
    margin-left: 0.7rem !important;
    width: 130px !important;
  }
  .summaryTitle {
    font-size: 20px;
  }
}
@media (max-width: 425px) {
  #chart {
    width: 55vw !important;
  }
}
@media (max-width: 412px) {
  .ws_result-header_chart {
    align-items: start;
  }
  #chart {
    margin-right: 0px;
    margin-left: -3px;
    margin-top: -10px;
  }
  .ws_result-header_points {
    align-items: center;
  }
  .ws_result-header_points-list {
    margin-top: 0.5rem;
  }
}
@keyframes rightArrowAnim {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(5px);
  }
  100% {
    transform: translateX(0);
  }
}
.ws-whitelabel .practice-result .ws_result-summary .nav-tabs {
  top: -15px !important;
}
@media (max-width: 575px) {
  .ws-whitelabel .practice-result .ws_result-summary .nav-tabs {
    top: 0 !important;
  }
}
.favQuestionStar {
  text-align: end;
  margin-top: 10px;
  margin-bottom: 10px;
  position: relative;
  width: 100%;
  display: flex;
  margin-left: auto;
}
.fa-star {
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}
.addedToFav {
  color: #ffdc64;
  animation: favPopup 0.35s ease;
  transition: all 0.35s ease;
}
.notAdded {
  color: #000;
}
.tooltiptext {
  visibility: hidden;
  width: 160px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  position: absolute;
  z-index: 1;
  top: -5px;
  right: 150%;
}
.tooltiptext:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent black;
}
.favQuestionStar:hover .tooltiptext {
  visibility: visible;
}
.prepjoy-footer {
  display: none !important;
}
.titleSplit {
  width: 150px;
}
.titleColon {
  margin: 0 10px;
}
#wsResultPointsCount,
#wsSectionResultPointsCount {
  display: flex;
  align-items: center;
  font-size: 20px !important;
}
.ws_result-header_points #wsSectionResultPointsCount {
  margin-top: 10px;
}
.ws_result-header_points #wsResultPointsCount span {
  font-size: 22px;
}
.ws_result-header_points #wsSectionResultPointsCount span {
  font-size: 18px;
}
.ibookgpt-section {
  display: none !important;
}
.mcqChatBtns {
  gap: 0.5rem;
  margin-left: -20px;
  margin-right: -20px;
  background: #f6f6f6;
  padding: 10px;
  margin-bottom: -5px;
  border-radius: 5px;
  flex-wrap: wrap;
}
.mcqChatBtns .mcqChatEx {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px 16px;
  border-radius: 5px;
  font-size: 13px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}
.mcqChatBtns .mcqChatEx::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}
.mcqChatBtns .mcqChatEx:hover::before {
  opacity: 1;
}
.mcqChatBtns .mcqChatEx:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
.mcqChatBtns .mcqChatEx svg {
  width: 20px;
  height: 20px;
}
.mcqChatBtns .mcqChatEx.mc-primary {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
}
.mcqChatBtns .mcqChatEx.mc-primary:hover {
  background: linear-gradient(135deg, #FF4757 0%, #FF3346 100%);
}
.mcqChatBtns .mcqChatEx.mc-secondary {
  background: linear-gradient(135deg, #ec4899 0%, #d946ef 100%);
}
.mcqChatBtns .mcqChatEx.mc-secondary:hover {
  background: linear-gradient(135deg, #db2777 0%, #c026d3 100%);
}
.mcqChatBtns .mcqChatEx.mc-tertiary {
  background: linear-gradient(135deg, #764BA2 0%, #667EEA 100%);
}
.mcqChatBtns .mcqChatEx.mc-tertiary:hover {
  background: linear-gradient(135deg, #6a43a0 0%, #5a6eea 100%);
}
@media (max-width: 768px) {
  .mcqChatBtns {
    position: sticky;
    bottom: -5px;
    padding: 10px 0;
  }
}
@keyframes favPopup {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(2);
  }
  100% {
    transform: scale(0);
  }
}
.webMcq {
  overflow: scroll;
}
