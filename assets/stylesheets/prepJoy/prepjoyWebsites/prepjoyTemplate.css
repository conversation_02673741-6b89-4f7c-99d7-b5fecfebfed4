/* WS Template Styles */
button,
.btn {
  cursor: pointer;
  border-radius: 5px;
  text-transform: unset;
}
button:hover,
.btn:hover,
button:focus,
.btn:focus,
button:active,
.btn:active,
button:active:focus,
.btn:active:focus {
  outline: 0;
  box-shadow: none;
}
button:not(:disabled):not(.disabled):active,
.btn:not(:disabled):not(.disabled):active,
button:not(:disabled):not(.disabled):active:focus,
.btn:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
button.disabled,
.btn.disabled,
button:disabled,
.btn:disabled {
  cursor: unset;
}
button.btn-lg,
.btn.btn-lg {
  font-size: 14px;
}
.btn-shadow {
  box-shadow: 0 2px 4px #0000001A;
  -webkit-box-shadow: 0 2px 4px #0000001A;
  -moz-box-shadow: 0 2px 4px #0000001A;
}
.btn-primary-modifier {
  background-color: #0F0839 !important;
  border-color: #0F0839 !important;
}
.btn-primary-modifier:hover {
  background-color: #0F0839;
  border-color: #0F0839;
}
.btn-primary-modifier:not(:disabled):not(.disabled):active,
.btn-primary-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #0F0839;
  border-color: #0F0839;
}
.btn-primary-modifier.disabled,
.btn-primary-modifier:disabled {
  background-color: #0F0839;
  border-color: #0F0839;
}
.btn-primary-modifier.dropdown-toggle:focus {
  box-shadow: none !important;
}
.btn-primary-modifier:focus:not(:active) {
  background-color: #0F0839;
}
.btn-primary-modifier .material-icons {
  font-size: 18px;
}
.btn-outline-primary-modifier {
  color: #0F0839;
  border-color: #0F0839;
}
.btn-outline-primary-modifier:hover {
  color: #0F0839;
  background-color: transparent;
  border-color: #0F0839;
}
.btn-outline-primary-modifier:not(:disabled):not(.disabled):active,
.btn-outline-primary-modifier:not(:disabled):not(.disabled):active:focus {
  color: #0F0839;
  background-color: transparent;
  border-color: #0F0839;
}
.btn-outline-primary-modifier:focus:not(:active) {
  background-color: transparent;
}
.btn-secondary-modifier {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier:hover {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier:not(:disabled):not(.disabled):active,
.btn-secondary-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier.disabled,
.btn-secondary-modifier:disabled {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier:focus:not(:active) {
  background-color: #8E8E8E;
}
.btn-outline-secondary-modifier {
  color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-outline-secondary-modifier:hover {
  color: #8E8E8E;
  background-color: transparent;
  border-color: #8E8E8E;
}
.btn-outline-secondary-modifier:not(:disabled):not(.disabled):active,
.btn-outline-secondary-modifier:not(:disabled):not(.disabled):active:focus {
  color: #8E8E8E;
  background-color: transparent;
  border-color: #8E8E8E;
}
.btn-outline-secondary-modifier:focus:not(:active) {
  background-color: transparent;
}
.btn-success-modifier {
  background-color: #27AE60;
  border-color: #27AE60;
}
.btn-success-modifier:hover {
  background-color: #27AE60;
  border-color: #27AE60;
}
.btn-success-modifier:not(:disabled):not(.disabled):active,
.btn-success-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #27AE60;
  border-color: #27AE60;
}
.btn-success-modifier.disabled,
.btn-success-modifier:disabled {
  background-color: #27AE60 !important;
  color: #FFFFFF !important;
  border-color: #27AE60;
}
.btn-success-modifier:focus:not(:active) {
  background-color: #27AE60;
}
.btn-outline-success-modifier {
  color: #27AE60;
  border-color: #27AE60;
}
.btn-outline-success-modifier:hover {
  color: #27AE60;
  background-color: transparent;
  border-color: #27AE60;
}
.btn-outline-success-modifier:not(:disabled):not(.disabled):active,
.btn-outline-success-modifier:not(:disabled):not(.disabled):active:focus {
  color: #27AE60;
  background-color: transparent;
  border-color: #27AE60;
}
.btn-outline-success-modifier:focus:not(:active) {
  background-color: transparent;
}
.btn-danger-modifier {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier:hover {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier:not(:disabled):not(.disabled):active,
.btn-danger-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier.disabled,
.btn-danger-modifier:disabled {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier:focus:not(:active) {
  background-color: #FF4B33;
}
.btn-warning-modifier {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier:hover {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier:not(:disabled):not(.disabled):active,
.btn-warning-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier.disabled,
.btn-warning-modifier:disabled {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier:focus:not(:active) {
  background-color: #F79420;
  color: #FFFFFF;
}
.mdl-button-modifier {
  height: auto;
  width: auto;
  min-width: auto;
}
.card-modifier {
  border-radius: 10px;
}
.card-shadow {
  box-shadow: 0 4px 10px #0000001A;
  -webkit-box-shadow: 0 4px 10px #0000001A;
  -moz-box-shadow: 0 4px 10px #0000001A;
}
.dataTables_wrapper {
  padding-top: 45px;
}
.dataTables_wrapper label,
.dataTables_wrapper li,
.dataTables_wrapper .dataTables_info {
  font-size: 13px;
}
.dataTables_wrapper .page-item.active .page-link {
  color: #FFFFFF !important;
}
.datepicker table thead tr th {
  font-weight: 500;
}
.datepicker table thead tr th.prev,
.datepicker table thead tr th.next {
  font-size: 20px;
}
.datepicker table tbody tr td {
  padding: 5px 7px;
}
.datepicker.datepicker-dropdown.datepicker-orient-bottom:before {
  top: -6px;
}
.datepicker.datepicker-dropdown.datepicker-orient-bottom:after {
  top: -5px;
}
.dropdown-menu a:active,
.dropdown-menu span:active,
.dropdown-menu li:active {
  background-color: #462ce1;
}
.dropdown-modifier.show .dropdown-toggle:focus {
  box-shadow: none;
}
.dropdown-modifier .btn-outline-primary-modifier.dropdown-toggle {
  background-color: transparent;
  border-color: #0F0839;
  color: #0F0839;
}
.form-group-modifier {
  position: relative;
  margin-bottom: 0;
}
.form-group-modifier:after {
  background-color: #0F0839;
  bottom: 0;
  content: '';
  height: 2px;
  left: 45%;
  position: absolute;
  transition-duration: 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  visibility: hidden;
  width: 10px;
}
.form-group-modifier.is-focused:after {
  left: 0;
  visibility: visible;
  width: 100%;
}
.form-group-modifier .form-control-modifier {
  border: none;
  padding: 0.375rem 0;
  border-radius: 0;
}
.form-control-modifier {
  color: #212121;
}
.form-control-modifier:focus {
  box-shadow: none;
}
.mdl-textfield-modifier {
  padding-bottom: 0;
}
.mdl-textfield-modifier .mdl-textfield__label {
  font-size: 12px;
  margin-bottom: 0;
}
.mdl-textfield-modifier .mdl-textfield__label:after {
  background-color: #0F0839;
  bottom: 0;
}
.mdl-textfield-modifier.is-focused .mdl-textfield__label,
.mdl-textfield-modifier.is-dirty .mdl-textfield__label {
  color: #0F0839;
  margin-bottom: 0;
}
#cke_1_contents {
  min-height: 300px;
}
.form-text-modifier {
  font-size: 12px;
}
.modal {
  z-index: 9992;
}
.modal-backdrop.show {
  opacity: 0.85;
  z-index: 9991;
}
.modal-modifier .modal-header-modifier {
  border: none;
}
.modal-modifier .modal-body-modifier h1,
.modal-modifier .modal-body-modifier h2,
.modal-modifier .modal-body-modifier h3,
.modal-modifier .modal-body-modifier h4,
.modal-modifier .modal-body-modifier h5 {
  color: #444444;
  font-weight: 500;
  font-size: 18px;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-body-modifier h1,
  .modal-modifier .modal-body-modifier h2,
  .modal-modifier .modal-body-modifier h3,
  .modal-modifier .modal-body-modifier h4,
  .modal-modifier .modal-body-modifier h5 {
    font-size: 16px;
  }
}
.modal-modifier .modal-body-modifier p {
  color: #949494;
}
.modal-modifier .modal-body-modifier p strong {
  font-weight: 600;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-body-modifier p {
    font-size: 13px;
  }
  .modal-modifier .modal-body-modifier p br {
    display: none;
  }
}
.modal-modifier .modal-header-modifier .close {
  margin: 0;
}
.modal-modifier .close {
  position: absolute;
  right: 15px;
  top: -45px;
  padding: 0;
  font-weight: 100;
  font-size: 30px;
  opacity: 1;
}
.modal-modifier .close:focus,
.modal-modifier .close:active {
  outline: 0;
  box-shadow: none;
}
.modal-modifier .btn,
.modal-modifier a.btn {
  font-size: 14px;
}
.modal-modifier .modal-content-modifier {
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-dialog-modifier {
    align-items: flex-end;
    padding-bottom: 0;
    max-width: 100%;
    margin: 0;
    height: 100%;
  }
  .modal-modifier .modal-content-modifier {
    border-radius: 20px 20px 0 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-mobile-fullscreen .close {
    position: relative;
    right: 0;
    top: 0;
    font-weight: 200;
    color: #444444 !important;
  }
  .modal-mobile-fullscreen .modal-dialog-modifier {
    align-items: inherit;
  }
  .modal-mobile-fullscreen .modal-content-modifier {
    border-radius: 0;
    border: none;
    height: 100vh;
  }
}
.modal.fade .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(0.5);
  transform: translate(0, 0) scale(0.5);
}
.modal.show .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(1);
  transform: translate(0, 0) scale(1);
}
table thead tr th,
.table thead tr th {
  font-size: 13px;
  font-weight: 500;
  white-space: normal !important;
}
table thead tr th:first-child,
.table thead tr th:first-child {
  width: 15%;
}
table thead tr th:first-child span,
.table thead tr th:first-child span {
  font-size: 13px;
  font-weight: 500;
}
table tbody,
.table tbody {
  display: table-row-group;
}
table tbody tr td,
.table tbody tr td {
  font-size: 13px;
  white-space: normal !important;
}
table tbody tr td button,
.table tbody tr td button,
table tbody tr td p,
.table tbody tr td p {
  font-size: 13px;
}
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
li,
a,
input,
textarea,
select,
label,
span,
small,
button,
th,
td,
dl,
dt,
dd,
address {
  font-family: Righteous, sans-serif !important;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #212121;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
a:hover,
a:active {
  text-decoration: none;
}
.material-icons {
  font-family: 'Material Icons' !important;
}
.text-primary-modifier {
  color: #0F0839 !important;
}
a.text-primary-modifier:hover,
a.text-primary-modifier:focus {
  color: #0F0839 !important;
}
.text-secondary-modifier {
  color: #6C757D !important;
}
.text-danger-modifier {
  color: #FF4B33 !important;
}
.text-success-modifier {
  color: #27AE60 !important;
}
.bg-primary-modifier {
  background-color: #0F0839 !important;
}
.bg-success-modifier {
  background-color: #27AE60 !important;
}
.bg-warning-modifier {
  border-color: #FFD602 !important;
}
.border-primary-modifier {
  border-color: #0F0839 !important;
}
.rounded-modifier {
  border-radius: 5px !important;
}
.switch {
  display: flex;
  position: absolute;
  right: 0;
  top: 0;
}
.switch.reset-switch {
  position: static;
  display: flex;
  align-items: center;
}
.switch.reset-switch label {
  margin-bottom: 0 !important;
}
.public-text {
  margin-right: 10px;
  display: block;
  font-style: italic;
  font-size: 12px;
  color: #0F0839;
}
.toggleSwitch {
  /* Rounded sliders */
}
.toggleSwitch .switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 15px;
}
.toggleSwitch .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.toggleSwitch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch .slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 1px;
  background-color: #FFFFFF;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch input:checked + .slider {
  background: #27AE60;
}
.toggleSwitch input:focus + .slider {
  box-shadow: 0 0 1px #2F80ED;
}
.toggleSwitch input:checked + .slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}
.toggleSwitch .slider.round {
  border-radius: 10px;
}
.toggleSwitch .slider.round:before {
  border-radius: 50%;
}
.toggleSwitch input:disabled + .slider {
  opacity: 0.3;
  cursor: not-allowed;
}
.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 5em;
  padding: 0.5em;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #CCC;
  border-radius: 5px;
  color: #212121;
  font-weight: 600;
  font-size: 80%;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity 0.2s linear;
  -moz-transition: opacity 0.2s linear;
  -o-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
}
.chartist-tooltip:before {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -15px;
  border: 15px solid transparent;
  border-top-color: #FFFFFF;
  display: none;
}
.chartist-tooltip.tooltip-show {
  opacity: 1;
}
.sweet-overlay {
  background-color: #000000B3 !important;
}
.sweet-alert h2 {
  background: #0F0839;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-flex !important;
  justify-content: center;
  font-size: 26px !important;
  margin: 0 0 10px !important;
  color: transparent !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .sweet-alert h2 {
    font-size: 24px !important;
  }
}
.sweet-alert p {
  color: #212121 !important;
  font-size: 15px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .sweet-alert p {
    font-size: 14px !important;
  }
}
.sweet-alert button {
  font-size: 15px !important;
  font-weight: 400 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .sweet-alert button {
    font-size: 14px !important;
    padding: 7px 20px !important;
    margin: 15px 5px 0 5px !important;
  }
}
.sweet-alert button.cancel {
  background-color: transparent !important;
  color: #8E8E8E;
  border: 1px solid #8E8E8E;
}
.information-admin label {
  font-weight: 500;
}
.information-admin .toggle .toggle-group label {
  font-weight: 400;
}
.information-admin #viewInformation table {
  table-layout: fixed;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:first-child {
  width: 60px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(2),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(3) {
  width: 140px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(4),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(5),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(6),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(7) {
  width: 120px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(9) {
  width: 100px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:last-child {
  width: 120px !important;
}
.banner_management .add_banners label {
  color: #6C757D;
}
.banner_management .add_banners input {
  height: 40px;
}
.direct-sales {
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}
.direct-sales form .form-group {
  margin-bottom: 1.5rem;
}
.direct-sales form label {
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
}
.direct-sales form select,
.direct-sales form input {
  height: 40px;
}
.direct-sales form select:focus,
.direct-sales form input:focus {
  outline: 0;
  box-shadow: none;
  border-color: #0F0839;
}
.direct-sales form select.input-error,
.direct-sales form input.input-error {
  border-color: #FF4B33;
}
.direct-sales form .btn {
  font-weight: 600;
}
.direct-sales #processResult p {
  font-weight: 500;
  line-height: normal;
}
.direct-sales #processResult .payment-link-share {
  display: inline-flex;
  align-items: center;
}
.direct-sales #processResult .payment-link-share i {
  font-size: 20px;
}
.direct-sales .direct-sale-book-info {
  background-color: #FFFFFF;
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 10px;
  border: 1px solid #F79420;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales .institute-info .institute-name h4 {
    font-size: 1.25rem;
  }
}
.direct-sales .institute-info .institute-logo img {
  width: 5rem;
  height: 5rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales .institute-info .institute-logo img {
    width: 4rem;
    height: 4rem;
  }
}
.direct-sales.ebook_detail .book_info {
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 15;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-top: 1px solid #DDD;
}
.direct-sales.ebook_detail .book_info .book_description h2 {
  font-weight: 300;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales.ebook_detail .book_info .book_description h2 {
    text-align: center !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales.ebook_detail .book_info .book_price {
    justify-content: center !important;
  }
}
.direct-sales.ebook_detail .book_info .book_price .offer_price {
  font-size: 2.5rem;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales.ebook_detail .book_info .book_price .offer_price {
    font-size: 2rem;
  }
}
@media (max-width: 575.98px) {
  .direct-sales.ebook_detail .book_info .book_price .offer_price {
    font-size: 1.75rem;
  }
}
.direct-sales.ebook_detail .book_info .book_price .offer_price span {
  font-weight: 500;
}
.direct-sales.ebook_detail .book_info .book_price .list_price {
  font-size: 2rem;
  font-weight: 300;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales.ebook_detail .book_info .book_price .list_price {
    font-size: 1.75rem;
  }
}
@media (max-width: 575.98px) {
  .direct-sales.ebook_detail .book_info .book_price .list_price {
    font-size: 1.5rem;
  }
}
.direct-sales.ebook_detail .image_wrapper {
  margin-top: -100px;
}
.direct-sales.ebook_detail .image_wrapper .book_image {
  height: 400px;
  width: 300px;
  padding: 10px;
  border-radius: 10px;
  background-color: #FFFFFF;
}
@media (max-width: 330px) {
  .direct-sales.ebook_detail .image_wrapper .book_image {
    height: 330px !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales.ebook_detail .image_wrapper .book_image {
    height: 300px;
    width: 230px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales.ebook_detail .image_wrapper .book_image {
    height: 220px;
    width: 160px;
  }
}
.direct-sales.ebook_detail .image_wrapper .book_image .bookShadow {
  height: 100% !important;
}
.direct-sales.ebook_detail .image_wrapper .book_image .bookShadow img {
  height: 100% !important;
}
.direct-sales .pay-btn {
  border: none;
  background-color: #F79420;
  color: #FFFFFF;
  position: relative;
  overflow: hidden;
  font-weight: 600;
  font-size: 1.5rem;
  border-radius: 7px;
  box-shadow: 0 2px 4px #0000001A;
  width: 300px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales .pay-btn {
    width: 250px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales .pay-btn {
    height: 45px;
    width: 100%;
  }
}
.direct-sales .pay-btn:after {
  content: '';
  position: absolute;
  top: -10px;
  left: -50%;
  z-index: 10;
  display: block;
  width: 30px;
  height: 100px;
  opacity: 0.7;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-40deg);
  transform: skewX(-40deg);
  -webkit-animation: shine 3s infinite;
  animation: shine 3s infinite;
  filter: blur(5px);
}
.direct-sales .package-books .img-wrapper {
  height: 300px;
  width: 230px;
  padding: 10px;
  border-radius: 10px;
  background-color: #FFFFFF;
  margin: 0 auto;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales .package-books .img-wrapper {
    height: 220px;
    width: 160px;
  }
}
@media (max-width: 330px) {
  .direct-sales .package-books .img-wrapper {
    height: 330px !important;
  }
}
.direct-sales .package-books .img-wrapper .bookShadow {
  height: 100%;
}
.direct-sales .package-books .img-wrapper .bookShadow img {
  width: 100%;
  height: 100%;
}
.loading-icon {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background-color: #000000B3;
  z-index: 9999;
  overflow: hidden;
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0 0 10px #00000025;
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  -moz-animation: load7 1.8s infinite ease-in-out;
  -o-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #0F0839;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  -moz-animation-delay: -0.16s;
  -o-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  -moz-animation-delay: -0.32s;
  -o-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.pace-inactive {
  display: none;
}
.pace .pace-progress {
  background: #0F0839;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 100%;
  width: 100%;
  height: 3px;
}
.go-back-btn {
  background-color: transparent;
  color: #0F0839;
}
.go-back-btn:hover,
.go-back-btn:focus,
.go-back-btn:active {
  background-color: #F8F9FA;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .page_title h3 {
    font-size: 1.5rem;
  }
}
.svg-primary-color {
  fill: #0F0839;
}
.slick-prev,
.slick-next {
  background-color: #FFFFFF;
  width: 40px;
  height: 80px;
  box-shadow: 0 0 8px #00000040;
}
.slick-prev:before,
.slick-next:before {
  color: #444444;
  width: 7px;
  height: 12px;
}
.slick-prev:hover,
.slick-next:hover,
.slick-prev:focus,
.slick-next:focus,
.slick-prev:active,
.slick-next:active {
  background-color: #FFFFFF;
  box-shadow: 0 0 8px #00000040 !important;
}
.slick-prev {
  left: -40px;
  border-radius: 5px 0 0 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .slick-prev {
    left: 0;
    z-index: 10;
  }
}
.slick-prev:before {
  content: url("../../images/landingpageImages/left-arrow.svg");
}
.slick-next {
  right: -40px;
  border-radius: 0 5px 5px 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .slick-next {
    right: 0;
    z-index: 10;
  }
}
.slick-next:before {
  content: url("../../images/landingpageImages/right-arrow.svg");
}
.correct-animated-icon {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 20px auto;
}
.correct-animated-icon:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #b3eecc;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
.correct-animated-icon:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.correct-animated-icon .checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #27AE60;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}
.correct-animated-icon .checkmark {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: #FFFFFF;
  stroke-miterlimit: 10;
  margin: 0;
  box-shadow: inset 0 0 0 #27AE60;
  animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
  position: absolute;
  z-index: 2;
  right: 0;
  left: 0;
}
.correct-animated-icon .checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}
.info-animated-icon .modal-info-icon {
  border-radius: 50%;
  border: 3px solid #F8BB86;
  box-sizing: content-box;
  height: 56px;
  margin: 20px auto;
  padding: 0;
  position: relative;
  width: 56px;
}
.info-animated-icon .modal-info-icon:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #ffd9b9;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
.info-animated-icon .modal-info-icon:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.info-animated-icon .modal-info-icon .info-icon-body {
  background-color: #F8BB86;
  border-radius: 2px;
  height: 26px;
  left: 50%;
  margin-left: -1px;
  position: absolute;
  top: 10px;
  width: 4px;
  z-index: 2;
}
.info-animated-icon .modal-info-icon .info-icon-dot {
  background-color: #F8BB86;
  border-radius: 50%;
  bottom: 10px;
  height: 6px;
  left: 50%;
  margin-left: -2px;
  position: absolute;
  width: 6px;
  z-index: 2;
}
.info-animated-icon .modal-info-icon + .modal-info-icon {
  margin-top: 50px;
}
.scaleAnimation {
  -webkit-animation: scaleAnimation 1s infinite alternate;
  animation: scaleAnimation 1s infinite alternate;
}
.pulseAnimationIns {
  -webkit-animation: pulseAnimationIns 0.75s infinite alternate;
  animation: pulseAnimationIns 0.75s infinite alternate;
}
.fadein-animated {
  animation: fadein 1.5s;
  -moz-animation: fadein 1.5s;
  -webkit-animation: fadein 1.5s;
  -o-animation: fadein 1.5s;
}
@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-o-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.75);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes shine {
  100% {
    left: 125%;
  }
}
@keyframes line-bounce {
  0% {
    left: 250px;
  }
  50% {
    left: 0;
  }
  100% {
    left: 250px;
  }
}
@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
@keyframes fill {
  100% {
    box-shadow: inset 0 0 0 30px #27AE60;
  }
}
@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes pulseWarning {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulseAnimationIns {
  0% {
    background-color: #F8D486;
  }
  100% {
    background-color: #F8BB86;
  }
}
.app_in_app ul.typeahead {
  overflow-y: scroll !important;
  height: 200px !important;
}
.ellipsis-type {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.ellipsis-type.single-line-text {
  -webkit-line-clamp: 1;
}
.banner-ws .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #6C757D;
}
.banner-ws .carousel-indicators li.active {
  background: #FFFFFF;
}
.banner-ws .carousel-control-next,
.banner-ws .carousel-control-prev {
  width: 40px;
  height: 80px;
  top: 45%;
  border-radius: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .banner-ws .carousel-control-next,
  .banner-ws .carousel-control-prev {
    width: 25px;
    height: 35px;
    border-radius: 3px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .banner-ws .carousel-control-next,
  .banner-ws .carousel-control-prev {
    width: 35px;
    height: 50px;
  }
}
.banner-ws .carousel-control-next i,
.banner-ws .carousel-control-prev i {
  font-size: 35px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .banner-ws .carousel-control-next i,
  .banner-ws .carousel-control-prev i {
    font-size: 25px;
  }
}
.banner-ws .carousel-control-next {
  right: 10px;
}
.banner-ws .carousel-control-prev {
  left: 10px;
}
.border-animation:before,
.border-animation:after {
  content: "";
  width: 0;
  height: 1px;
  position: absolute;
  transition: all 0.2s linear;
  transition-delay: 0.2s;
  background: #0F0839;
}
.border-animation:before {
  right: 0;
  top: 0;
}
.border-animation:after {
  left: 0;
  bottom: 0;
}
.border-animation .border-animation-inside:before,
.border-animation .border-animation-inside:after {
  content: "";
  width: 1px;
  height: 0;
  position: absolute;
  transition: all 0.2s linear;
  transition-delay: 0s;
  background: #0F0839;
}
.border-animation .border-animation-inside:before {
  left: 0;
  top: 0;
}
.border-animation .border-animation-inside:after {
  right: 0;
  bottom: 0;
}
.border-animation:hover:before,
.border-animation:hover:after {
  width: 100%;
  transition-delay: 0s;
}
.border-animation:hover .border-animation-inside:before,
.border-animation:hover .border-animation-inside:after {
  height: 100%;
  transition-delay: 0.2s;
}
.calendar_wrapper {
  width: 100%;
}
.calendar_wrapper .datepicker {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  font-family: Righteous, sans-serif !important;
}
.calendar_wrapper .datepicker .table-condensed {
  width: 100%;
  font-size: 14px;
}
.calendar_wrapper .datepicker .datepicker-days .prev {
  color: #0F0839;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .next {
  color: #0F0839;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .datepicker-switch {
  height: 40px;
  color: #0F0839;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .dow {
  font-size: 12px;
  font-weight: 400;
  color: #949494;
  padding: 10px;
}
.calendar_wrapper .datepicker .datepicker-days .day {
  width: 40px;
  height: 40px;
  border-radius: 50px;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .day.active {
  background: #0F0839;
}
.calendar_wrapper .datepicker .datepicker-days .day.active:hover {
  color: #FFFFFF;
}
.calendar_wrapper .datepicker .datepicker-days .day:hover {
  color: #0F0839;
}
.calendar_wrapper .datepicker .datepicker-days .old.day,
.calendar_wrapper .datepicker .datepicker-days .new.day {
  color: #949494;
  font-weight: 400;
}
.resourcePageShimmer {
  width: 100%;
  height: 93vh;
  position: relative;
  z-index: 9;
  display: grid;
  grid-template-columns: 400px 750px;
  grid-gap: 8rem;
  background: #fff;
}
.resourcePageShimmer_sideBar {
  margin: 3rem 0 3rem 3rem;
}
.resourcePageShimmer_main {
  margin: 3rem 0;
}
.resourcePageShimmer .card {
  position: relative;
  -webkit-box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border-radius: 6px;
  height: 100%;
  overflow: hidden;
  margin: 40px auto;
}
.resourcePageShimmer .card .shimmerBG {
  animation-duration: 2.2s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: shimmerEffect;
  animation-timing-function: linear;
  background: #ddd;
  background: linear-gradient(to right, #f6f6f6 8%, #f0f0f0 18%, #f6f6f6 33%);
  background-size: 1200px 100%;
}
@-webkit-keyframes shimmerEffect {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}
@keyframes shimmerEffect {
  0% {
    background-position: -1200px 0;
  }
  100% {
    background-position: 1200px 0;
  }
}
.resourcePageShimmer .card .media {
  height: 200px;
}
.resourcePageShimmer .card .p-32 {
  margin: 1rem;
}
.resourcePageShimmer .card .title-line {
  height: 70px;
  width: 100%;
  margin-bottom: 2rem;
  border-radius: 5px;
}
.resourcePageShimmer .card .content-line {
  height: 20px;
  width: 100%;
  margin-bottom: 2rem;
  border-radius: 5px;
}
.resourcePageShimmer .card .shimmerBGWrp {
  display: flex;
  gap: 1rem;
  margin: 1rem;
}
.resourcePageShimmer .card .contentBtn {
  height: 50px;
  width: 50%;
  margin-bottom: 2rem;
  border-radius: 5px;
}
.resourcePageShimmer .card .end {
  width: 100%;
}
.m-t-24 {
  margin-top: 24px;
}
.resourcePageShimmer .card {
  box-shadow: none !important;
  background: transparent !important;
  border: none !important;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .books-list #content-data-books-ebooks .col-lg-2 {
    flex: 0 0 20%;
    max-width: 20%;
  }
}
.books-list .topSchoolBooks {
  margin-right: auto;
  margin-left: auto;
  width: 175px;
  padding: 0;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .topSchoolBooks {
    width: 155px;
  }
}
.books-list .image-wrapper {
  width: 100%;
  height: 220px;
  position: relative;
  z-index: 99;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .image-wrapper {
    height: 200px;
  }
}
.books-list .image-wrapper img {
  width: 100%;
  height: 220px;
  position: relative;
  border-radius: 4px;
  box-shadow: 0 0 14px #00000040;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .image-wrapper img {
    height: 200px;
  }
}
.books-list .image-wrapper h3 {
  position: absolute;
  font-size: 11px;
  font-weight: 400;
  color: #FFFFFF;
  background-color: #F79420;
  padding: 7px 14px;
  bottom: 32px;
  left: -5px;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  box-shadow: 0 0 6px #00000040;
}
.books-list .image-wrapper h3:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #CF6C00 #CF6C00 transparent transparent;
}
.books-list .uncover {
  padding: 10px;
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .uncover {
    height: 200px;
  }
}
.books-list .uncover p {
  color: #FFFFFF;
  text-align: center;
}
.books-list .content-wrapper {
  margin-top: 8px;
}
.books-list .content-wrapper h3 {
  font-size: 14px;
  font-weight: 400;
  color: #212121;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: normal;
}
.books-list .content-wrapper h6 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: normal;
}
.books-list .content-wrapper p {
  font-size: 12px;
  letter-spacing: -0.015em;
}
.books-list .content-wrapper p.price {
  font-size: 15px;
  font-family: 'Rubik', sans-serif !important;
  color: #212121;
  padding-top: 0;
}
.books-list .content-wrapper p.price span {
  font-size: 14px;
  font-family: 'Rubik', sans-serif !important;
  font-weight: 300;
  text-decoration: line-through;
  color: #6C757D;
  margin-left: 5px;
}
.books-list .lib-showcase {
  position: relative;
  z-index: 9;
  min-height: 192px;
  border-radius: 4px;
  color: #FFFFFF;
}
.books-list .card {
  padding: 10px;
  width: 175px;
  border: none;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  background-color: #FFFFFF;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  margin-bottom: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .books-list .card {
    width: 100%;
  }
}
.books-list .card:hover {
  box-shadow: 0 0 14px #00000040;
}
.books-list .card .uncover {
  height: 192px;
}
.books-list .card img {
  width: 154px;
  height: 192px;
  box-shadow: 0 0 14px #00000040;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .books-list .card img {
    width: 100%;
  }
}
.books-list .card .img-hero {
  position: absolute;
  z-index: 99;
  border-radius: 4px;
  box-shadow: 0 0 10px #0000001A;
}
.books-list .card .img-child {
  position: absolute;
  left: 7px;
  top: 5px;
  border-radius: 4px;
}
.books-list .card .card-body {
  padding: 0;
  align-items: start;
}
.books-list .card .card-body .card-text {
  height: auto;
  color: #444444;
  margin-top: 0.5rem;
  width: 152px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.books-list .card .card-body .card-text:hover {
  color: #0F0839;
  text-decoration: underline;
}
.books-list .card .card-body .dropup .dropdown-toggle,
.books-list .card .card-body .dropdown .dropdown-toggle {
  padding-right: 0;
  padding-bottom: 0;
}
.books-list .card .card-body .dropup .dropdown-toggle:after,
.books-list .card .card-body .dropdown .dropdown-toggle:after {
  display: none;
}
.books-list .card .card-body .dropup .dropdown-menu,
.books-list .card .card-body .dropdown .dropdown-menu {
  min-width: 100px;
  top: -30px !important;
  transform: none !important;
  padding: 0.25rem 0;
  z-index: 991;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .books-list .card .card-body .dropup .dropdown-menu,
  .books-list .card .card-body .dropdown .dropdown-menu {
    left: auto !important;
    right: 0;
  }
}
.books-list .card .card-body .dropup .dropdown-menu .delete,
.books-list .card .card-body .dropdown .dropdown-menu .delete {
  color: #FF4B33;
  font-size: 13px;
  text-align: center;
}
.books-list .card .card-body .dropup .dropdown-menu .delete:focus,
.books-list .card .card-body .dropdown .dropdown-menu .delete:focus,
.books-list .card .card-body .dropup .dropdown-menu .delete:active,
.books-list .card .card-body .dropdown .dropdown-menu .delete:active {
  outline: 0;
  background-color: transparent;
}
.books-list .card a {
  cursor: pointer;
}
.books-list .card .book-tag {
  position: absolute;
  font-size: 10px;
  font-weight: 500;
  color: #FFFFFF;
  background-color: #F79420;
  padding: 7px 14px;
  bottom: 32px;
  left: -5px;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  box-shadow: 0 0 6px #00000040;
}
.books-list .card .book-tag:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #CF6C00 #CF6C00 transparent transparent;
}
.bookShadow {
  height: auto;
  border-radius: 0 3px 3px 0;
  box-shadow: inset 8px 0 10px #0000001A;
  position: relative;
}
.bookShadow:after {
  content: '';
  position: absolute;
  top: 0;
  left: 5px;
  bottom: 0;
  width: 2px;
  background: #0000001A;
  box-shadow: 1px 0 3px rgba(255, 255, 255, 0.3);
}
.book-publisher-name {
  text-transform: uppercase;
  color: #aeaeae;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.badge-overlay {
  position: absolute;
  left: -15px;
  top: -15px;
  width: 40px;
  height: 40px;
  z-index: 100;
  -webkit-transition: width 1s ease, height 1s ease;
  -moz-transition: width 1s ease, height 1s ease;
  -o-transition: width 1s ease, height 1s ease;
  transition: width 0.4s ease, height 0.4s ease;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .badge-overlay {
    left: -10px;
    top: -10px;
  }
}
.top-left {
  position: absolute;
  top: 0;
  left: 0;
  -ms-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -webkit-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -ms-transform-origin: top right;
  -webkit-transform-origin: top right;
  transform-origin: top right;
}
.badge {
  margin: 0;
  padding: 5px;
  color: white;
  line-height: 1;
  background: #01b901;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: pre-wrap;
}
.popular_searches .popular_search_lists {
  height: 250px;
}
@media (max-width: 375px) {
  .popular_searches .popular_search_lists {
    margin-left: 4% !important;
  }
}
@media (min-device-width: 376px) and (max-device-width: 768px) {
  .popular_searches .popular_search_lists {
    margin-left: 8% !important;
  }
}
.popular_searches .bg-light {
  box-shadow: 0 2px 10px #0000001A;
}
.popular_searches .slick-track {
  width: 1900px !important;
  margin-left: 0;
  margin-right: 0;
}
.popular_searches .slick-track .slick-slide {
  width: 190px !important;
}
.popular_searches .img-wrapper {
  padding: 15px;
  width: 155px;
  margin: 0 auto;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
.popular_searches .img-wrapper img {
  width: 100%;
  height: 165px;
}
.popular_searches .bookShadow {
  margin-bottom: 10px;
}
.popular_searches a:hover .uncover p {
  text-decoration: none;
}
.popular_searches a:hover .content-wrapper p {
  text-decoration: underline;
}
.popular_searches .content-wrapper p {
  font-size: 12px;
  color: #444444;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: normal;
}
.popular_searches .uncover {
  height: 165px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popular_searches .uncover p {
  font-size: 12px;
}
.popular_searches #show-more,
.popular_searches #bestSeller-show-more {
  font-size: 13px;
}
.popular_searches #show-more:hover,
.popular_searches #bestSeller-show-more:hover {
  text-decoration: underline;
}
body.whitelabel_ws .popular_searches .img-wrapper {
  padding: 15px;
  width: 155px;
  margin: 0 auto;
  height: fit-content;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
.all-container .container-wrapper {
  min-height: auto;
  width: 600px;
  margin-top: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .all-container .container-wrapper {
    width: 100%;
  }
}
.all-container .container-wrapper div > .media {
  width: 100%;
  padding: 0;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.all-container .container-wrapper div > .media .media-body {
  padding: 0.5rem 0;
}
.all-container .container-wrapper div > .media .readnow {
  display: flex;
  align-items: center;
  padding: 0 1rem;
  width: 100%;
}
.all-container .container-wrapper div > .media .readnow:hover {
  text-decoration: none;
}
.all-container .container-wrapper div > .media p {
  color: #949494;
  font-size: 12px;
}
.all-container .container-wrapper div > .media .title {
  padding: 0 10px;
  color: #212121;
  font-size: 14px;
  font-weight: 400;
}
.all-container .container-wrapper div > .media .title a {
  color: #212121;
}
.all-container .container-wrapper div > .media .box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 52px;
  border-radius: 5px;
  margin: 0.5rem 0;
}
.all-container .container-wrapper div > .media .box i {
  width: 32px;
  height: 24px;
  margin: 0 auto;
  display: flex;
}
.all-container .container-wrapper div > .media .box p {
  line-height: 1;
  font-size: 9px;
  margin: 0;
  text-align: center;
  top: 0;
  color: #FFFFFF;
  font-weight: 600;
}
.all-container .container-wrapper div > .media .box.blue {
  background: radial-gradient(109.09% 109.09% at 0% 0%, #2D9CDB 0%, #2F80ED 100%);
}
.all-container .container-wrapper div > .media .box.blue i {
  background: url("../../images/ws/pdf.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.green {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, rgba(85, 115, 0, 0.8) 76.95%);
}
.all-container .container-wrapper div > .media .box.green i {
  background: url("../../images/ws/link.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.yellow {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F2C74C 0%, #F2994A 46.86%);
}
.all-container .container-wrapper div > .media .box.yellow i {
  background: url("../../images/ws/flashcard.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.pink {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F24CE1 0%, rgba(183, 7, 206, 0.9) 76.95%);
}
.all-container .container-wrapper div > .media .box.pink i {
  background: url("../../images/ws/video.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.lightgreen {
  background: radial-gradient(100% 100% at 0% 0%, #49E859 0%, #007D0C 100%);
}
.all-container .container-wrapper div > .media .box.lightgreen i {
  background: url("../../images/ws/notes.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.violet {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #BB6BD9 0%, #7B24CD 99.48%);
}
.all-container .container-wrapper div > .media .box.violet i {
  background: url("../../images/ws/mcq1.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.darkgreen {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #4CF2E8 5.08%, #006963 70.31%);
}
.all-container .container-wrapper div > .media .box.darkgreen i {
  background: url("../../images/ws/mindmap.svg") center center no-repeat;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .store-list-layout {
    flex-direction: column;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks #searchResults {
    padding: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks #content-data-books-ebooks {
    padding: 0;
  }
}
.ebooks #content-data-books-ebooks .fadein-animated {
  border-radius: 10px;
}
.ebooks .books-list .topSchoolBooks {
  width: auto;
  display: flex;
  justify-content: start;
  margin: initial;
}
.ebooks .books-list .topSchoolBooks .image-wrapper {
  width: 155px;
  height: 200px;
  margin-right: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .image-wrapper {
    width: 100px;
    height: 130px;
    margin-right: 15px;
  }
}
.ebooks .books-list .topSchoolBooks .image-wrapper .bookShadow {
  height: 100%;
}
.ebooks .books-list .topSchoolBooks .image-wrapper img {
  width: 100%;
  height: 100%;
}
.ebooks .books-list .topSchoolBooks .image-wrapper .uncover {
  width: 100%;
  height: 100%;
}
.ebooks .books-list .topSchoolBooks .image-wrapper .uncover p {
  line-height: normal;
  font-size: 12px;
}
.ebooks .books-list .topSchoolBooks .content-wrapper {
  margin-top: 0;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .left-div {
  display: flex;
  flex-direction: column;
  align-self: start;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-outer {
  display: inline-block;
  position: relative;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-outer:before {
  content: "\f005 \f005 \f005 \f005 \f005";
  font: var(--fa-font-regular);
  color: #FFC107;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-inner {
  position: absolute;
  top: 0;
  left: 0;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-inner:before {
  content: "\f005 \f005 \f005 \f005 \f005";
  font: var(--fa-font-solid);
  color: #FFC107;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .ratings {
  color: #949494;
  font-weight: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper {
    display: block;
  }
}
.ebooks .books-list .topSchoolBooks .content-wrapper h6 {
  overflow: inherit;
  overflow: unset;
  text-overflow: unset;
  -webkit-line-clamp: unset;
  -webkit-box-orient: unset;
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper h6 {
    font-size: 13px;
  }
}
.ebooks .books-list .topSchoolBooks .content-wrapper .book-publisher-name {
  text-transform: none;
  margin-bottom: 10px;
  font-size: 14px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper .book-publisher-name {
    font-size: 12px;
  }
}
.ebooks .books-list .topSchoolBooks .content-wrapper .add_to_cart_btn {
  margin: 5px 0 0;
  bottom: 0;
  width: 150px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper .add_to_cart_btn {
    margin: 0;
    width: 125px;
    padding: 2px;
  }
}
.ebooks .books-list .badge-overlay {
  position: relative;
  width: 65px;
  height: auto;
  left: unset;
  top: unset;
  margin: 0 0 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .badge-overlay {
    width: 55px;
  }
}
.ebooks .books-list .badge {
  border: 1px dashed #b8dcbb;
  background: transparent;
  color: #00A510;
  width: auto;
  height: auto;
  border-radius: 4px;
  line-height: normal;
  font-weight: 500;
  font-size: 12px;
  padding: 3px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .badge {
    font-size: 11px;
  }
}
.ebooks h1 {
  font-size: 40px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebooks h1 {
    font-size: 30px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks h1 {
    font-size: 20px;
  }
}
.ebooks .share-ebooks-page {
  width: 35px;
  height: 35px;
  cursor: pointer;
}
.ebooks .share-ebooks-page i {
  font-size: 20px;
}
.ebooks .ebooks_filter {
  background: #FFFFFF;
  border-radius: 7px;
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1), 0 1px 10px 0 rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 77px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .ebooks_filter {
    position: relative;
    top: unset;
    background: transparent;
  }
}
.ebooks .ebooks_filter .filter-icon-show-hide {
  position: absolute;
  top: -12px;
  right: 10px;
}
.ebooks .ebooks_filter select {
  font-style: italic;
  font-size: 14px;
  height: 45px;
  background: #FAFAFA;
  color: #949494;
  box-sizing: border-box;
  border: 2px solid #949494;
  box-shadow: 0 0 10px #0000001A;
}
@media (max-width: 575.98px) {
  .ebooks .ebooks_filter select {
    font-size: 13px;
    height: 40px;
  }
}
.ebooks .ebooks_filter select:hover,
.ebooks .ebooks_filter select:focus {
  border-color: #FFD602;
  color: #212121;
}
.ebooks .ebooks_filter select.background-bg {
  background-image: linear-gradient(#FFD602, #FFD602);
  border-color: #FFD602;
  color: #212121;
  font-style: normal;
  font-weight: 400;
}
.ebooks .ebooks_filter select option {
  font-style: normal;
  font-weight: 400;
}
.ebooks .ebooks_filter .hiddenFilters {
  display: none;
}
.ebooks #infiniteLoading .loading-div p {
  position: relative;
  letter-spacing: 1px;
  color: #2C3E50;
  font-size: 15px;
}
.ebooks #infiniteLoading .loading-div img {
  width: 120px;
  margin-top: -55px;
}
.ebooks #infiniteLoading .loading-div .alert {
  font-size: 14px;
}
.ebooks #resetFilter {
  color: #949494;
  border: 0;
  padding: 0;
  font-weight: normal;
}
.reach-us .card-modifier {
  flex-direction: unset;
}
.reach-us .card-modifier .reach-us-message h1 {
  font-size: 45px;
  margin-bottom: 10px;
  opacity: 0.7;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .reach-us .card-modifier .reach-us-message h1 {
    font-size: 35px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .reach-us .card-modifier .reach-us-message h1 {
    font-size: 30px;
  }
}
.reach-us .card-modifier .reach-us-message h5 {
  opacity: 0.7;
  font-weight: 300;
}
.reach-us .card-modifier .reach-us-message img {
  margin-top: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .reach-us .card-modifier .reach-us-message img {
    width: 100%;
  }
}
.reach-us .card-modifier .reach-us-form {
  max-width: 400px;
  min-height: 350px;
  max-height: 350px;
  margin-top: -7rem;
  border-radius: 20px;
  box-shadow: 0 0 10px #0000001A;
  background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #ffd000 0%, #ff5700 100%);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .reach-us .card-modifier .reach-us-form {
    min-height: 320px;
    max-height: 320px;
    margin-top: 2rem;
    margin-bottom: -4rem;
  }
}
.reach-us .card-modifier .reach-us-form .form-control-modifier {
  font-size: 14px;
}
.reach-us .card-modifier .reach-us-form .send-btn {
  background: #FF9901;
  border-color: #FFFFFF;
  box-shadow: 0 -4px 10px #0000001A;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
.reach-us .card-modifier .reach-us-form .send-btn:hover {
  border-color: transparent;
}
.view-more {
  margin: 0 auto;
  display: flex;
  color: #FFFFFF !important;
  text-align: center;
  background: #F79420 !important;
  border: none;
  width: 200px;
  height: 40px;
  border-radius: 50px;
  display: block;
}
.wonderslate_main .ebooks {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}
.wonderslate_main .ebooks #content-data-books-ebooks {
  margin-top: 0 !important;
}
.wonderslate_main .ebooks #searchResults {
  padding-left: 0;
  padding-right: 0;
}
.wonderslate_main .ebooks .banner-ws img {
  border-radius: 0 !important;
}
.wonderslate_main .ebooks .mobile {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .mobile {
    display: flex;
    margin-top: 60px !important;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .desktop {
    display: none;
  }
}
.wonderslate_main .ebooks .section-headerless {
  margin-top: 50px;
  margin-right: 0;
  margin-left: 0;
}
.wonderslate_main .ebooks .section-with-header {
  margin-top: 60px !important;
}
.wonderslate_main .ebooks .card {
  border-radius: 16px;
  border: none;
  padding: 25px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card {
    padding: 20px;
  }
  .wonderslate_main .ebooks .card div {
    flex-direction: column-reverse;
    justify-content: center !important;
    text-align: center;
  }
}
.wonderslate_main .ebooks .card .text-and-shop {
  align-self: flex-end;
  padding-right: 10px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card .text-and-shop {
    margin-top: 10px;
    padding-right: 0;
    width: 100%;
  }
}
.wonderslate_main .ebooks .card .text-and-shop p {
  font-family: Righteous, sans-serif !important;
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
  color: #212121;
}
.wonderslate_main .ebooks .card .text-and-shop button {
  width: 122px;
  height: 34px;
  border: none;
  border-radius: 16px;
  text-align: center;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card .text-and-shop button {
    width: 100px;
  }
}
.wonderslate_main .ebooks .card .text-and-shop button a {
  color: #212121 !important;
}
.wonderslate_main .ebooks .card img {
  min-height: 180px;
  min-width: 130px;
  height: 180px;
  width: 130px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .card img {
    width: 150px;
    height: 200px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card img {
    height: 100px;
    width: 80px;
    margin: 0 auto;
  }
}
.wonderslate_main .ebooks .new-release {
  background: linear-gradient(132.37deg, #5FD0E2 17.22%, rgba(95, 208, 226, 0) 130.82%, #5FD0E2 130.82%);
}
.wonderslate_main .ebooks .new-release button {
  background: #65C0CE;
}
.wonderslate_main .ebooks .book-of-day {
  background: linear-gradient(145.24deg, #B2DCBC 38.27%, rgba(178, 220, 188, 0) 123.5%);
}
.wonderslate_main .ebooks .book-of-day button {
  background-color: #86B792;
}
.wonderslate_main .ebooks .trending-now {
  background: linear-gradient(134.65deg, #FACED6 5.62%, rgba(250, 206, 214, 0) 122.6%);
}
.wonderslate_main .ebooks .trending-now button {
  background-color: #FEB8C5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .shop-now-cards {
    margin-bottom: 20px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .shop-now-cards {
    padding: 0 10px !important;
  }
}
.wonderslate_main .ebooks .ebooks_filter {
  margin-left: auto;
  margin-right: auto;
  box-shadow: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .ebooks_filter {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0 !important;
  }
}
.wonderslate_main .ebooks .ebooks_filter select {
  height: 40px !important;
  border-radius: 50px;
  background: #FFFFFF;
  border-width: 1px;
  border-color: #F79420;
  box-shadow: none;
}
.wonderslate_main .ebooks .ebooks_filter select:hover,
.wonderslate_main .ebooks .ebooks_filter select:focus {
  border-color: #F79420;
}
.wonderslate_main .ebooks .ebooks_filter select.background-bg {
  background-image: none;
  background-color: #F79420;
  border-color: #F79420;
}
.custom_container {
  width: calc(100% - 15%);
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .custom_container {
    width: calc(100% - 5%);
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .container-fluid {
    width: 100%;
  }
}
.ebook_detail .image_wrapper .book_image {
  position: relative;
  z-index: 10;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image {
    width: 100%;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image {
    height: 350px !important;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 350px !important;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow img {
  width: 100%;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 350px !important;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow:after {
  width: 3px;
}
.ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #F79420;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 350px !important;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail p {
  text-align: center;
  text-transform: capitalize;
  font-size: 16px;
  font-style: italic;
  color: #FFFFFF;
  padding: 60% 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail p {
    padding: 50% 0;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow .book-tag {
  position: absolute;
  z-index: 10;
  color: #FFFFFF;
  background-color: #27AE60;
  padding: 5px 15px;
  bottom: 50px;
  left: -5px;
  text-align: left;
  line-height: normal;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .book-tag {
    bottom: 30px;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow .book-tag span {
  font-size: 12px;
  display: block;
}
.ebook_detail .image_wrapper .book_image .bookShadow .book-tag:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #27AE60 #27AE60 transparent transparent;
}
.ebook_detail .image_wrapper .book_image_bottom {
  margin-top: -12px;
  position: relative;
  z-index: 11;
}
.ebook_detail .image_wrapper .book_image_bottom img {
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .book_info .book_description h2 {
    font-size: 22px;
  }
}
.ebook_detail .book_info .book_description .book-publisher-name {
  text-transform: capitalize;
  color: #6C757D;
}
.ebook_detail .book_info .book_description .book-desc {
  color: #949494;
}
.ebook_detail .book_info .book_description .book-desc a.exp:hover {
  text-decoration: underline;
}
.ebook_detail .book_info .book_description .book_briefDetail {
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  background: #fff;
  border-radius: 4px;
  margin-bottom: 1.6rem;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier {
  font-size: 14px;
  height: 40px;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier span {
  font-weight: 600;
  display: inline-block;
  width: 95%;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier span svg {
  margin-right: 10px;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier:after {
  color: #0F0839;
  vertical-align: 0.15em;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu {
  width: 100%;
  padding: 0;
  transform: translate3d(0, 40px, 0) !important;
  max-height: 330px;
  overflow-y: scroll;
  z-index: 99;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu .dropdown-item {
  white-space: pre-wrap;
  color: #0F0839;
  padding-top: 7px;
  padding-bottom: 7px;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu .dropdown-item:first-child {
  border-top-left-radius: 5px;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu .dropdown-item:last-child {
  border-bottom-left-radius: 5px;
}
.ebook_detail .book_info .book_resources .card-modifier {
  height: 100%;
}
.ebook_detail .book_info .book_resources .card-modifier p {
  font-size: 12px;
  color: #949494;
}
.ebook_detail .book_info .book_resources .card-modifier p img {
  padding-right: 5px;
}
.ebook_detail .book_info .book_resources.book_adding .card-modifier p {
  color: #0F0839;
}
.ebook_detail .book_info .book_price .offer_price {
  font-family: 'Rubik', sans-serif !important;
  font-size: 22px;
  display: block;
}
.ebook_detail .book_info .book_price .list_price {
  font-family: 'Rubik', sans-serif !important;
  font-size: 28px;
  color: #FF4B33;
}
.ebook_detail .book_info .book_variants a {
  width: 100%;
}
.ebook_detail .book_info .book_variants a.card.active {
  background-color: #FFF0CC;
  border-color: #F79420;
}
.ebook_detail .book_info .book_variants a.card:hover {
  border-color: #F79420;
}
.ebook_detail .book_info .book_variants a.card .card-body {
  min-height: 130px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .book_info .book_variants a.card .card-body {
    min-height: 140px;
  }
}
.ebook_detail .book_info .book_variants a.card .list_price {
  font-size: 18px;
  color: #FF4B33;
  padding-right: 0.25rem;
  line-height: normal;
}
.ebook_detail .book_info .book_variants a.card .offer_price {
  font-size: 20px;
  color: #212121;
  line-height: normal;
}
.ebook_detail .book_info .book_variants a.card h5 {
  padding-top: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .book_info .book_variants a.card h5 {
    font-size: 1rem;
  }
}
.ebook_detail .book_info .book_variants a.card h5 span.badge {
  display: initial;
  border-radius: 50px;
  font-size: 10px;
  padding: 3px 5px;
  font-weight: normal;
  background: var(--info);
  position: absolute;
  right: 1px;
  top: 1px;
  height: auto;
  width: 60px;
}
@media (max-width: 575.98px) {
  .ebook_detail .book_info .book_buttons {
    margin-bottom: 10px;
  }
}
.ebook_detail .book_info .book_buttons .col {
  padding: 0;
}
.ebook_detail .book_info .book_buttons .col a {
  width: 90%;
  height: 50px;
  font-size: 14px;
  font-weight: 500;
  line-height: 2.5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .book_info .book_buttons .col a {
    height: auto;
    line-height: normal;
  }
}
.ebook_detail .book_info .book_buttons .col a:hover {
  box-shadow: 0 0.2rem 1rem #0000001A;
}
.ebook_detail .book_info .couponInput #inputManualCoupon {
  width: 30%!important;
  border-color: #0F0839;
}
@media only screen and (max-width: 767px) {
  .ebook_detail .book_info .couponInput #inputManualCoupon {
    width: 45%!important;
  }
}
.ebook_detail .book_info .couponInput #manualCoupon {
  width: 30%!important;
  margin-left: 24px;
}
@media only screen and (max-width: 540px) {
  .ebook_detail .book_info .couponInput #manualCoupon {
    margin-left: 24px !important;
  }
}
@media only screen and (max-width: 414px) {
  .ebook_detail .book_info .couponInput #manualCoupon {
    margin-left: 18px !important;
  }
}
@media only screen and (max-width: 767px) {
  .ebook_detail .book_info .couponInput #manualCoupon {
    margin-left: 15px;
    width: 45%!important;
  }
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart {
  color: #6C757D;
  padding: 5px 0 0;
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart .first-table-cart {
  width: 110px;
  display: inline-block;
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart .second-table-cart {
  width: 50px;
  display: inline-block;
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart .mrp_price {
  font-family: 'Rubik', sans-serif !important;
}
.ebook_detail .nav-tabs-book-details ul.nav li a {
  position: relative;
  border: 1px solid transparent;
  border-bottom-color: #e1e1e1;
  margin-right: 12px;
  font-size: 16px;
  color: #6C757D;
}
.ebook_detail .nav-tabs-book-details ul.nav li a.active {
  color: #0F0839;
  border: 1px solid #0F0839;
  border-bottom-width: 2px;
}
.ebook_detail .nav-tabs-book-details ul.nav li a.active:hover {
  background-color: transparent;
  border-bottom-color: #0F0839;
}
.ebook_detail .nav-tabs-book-details ul.nav li a:hover {
  background-color: #ededed;
  border-bottom-width: 2px;
  border-bottom-color: #bababa;
}
@media (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .nav-tabs-book-details ul.nav li a {
    font-size: 14px;
    padding: 5px 15px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .nav-tabs-book-details ul.nav li a {
    padding: 4px 8px 4px 8px;
    margin-right: 6px;
    font-size: 12px;
    font-weight: 500;
  }
}
@media (max-width: 330px) {
  .ebook_detail .nav-tabs-book-details ul.nav li a {
    padding: 4px 5px;
    font-size: 11px;
  }
}
.ebook_detail .nav-tabs-book-details .wrp-details-about-cart {
  color: #444444;
}
.ebook_detail .nav-tabs-book-details .wrp-details-about-cart .first-table-cart {
  font-weight: 600;
}
.ebook_detail .nav-tabs-book-details #book-desc p {
  margin-bottom: 5px;
}
.ebook_detail .page-row-filter .breadcrumb {
  border-bottom: 1px solid #ccc;
  background-color: transparent;
  padding: 0 0 7px;
  border-radius: 0;
  margin-bottom: 16px;
}
.ebook_detail .page-row-filter .breadcrumb li.breadcrumb-item {
  font-size: 12px;
  color: #6C757D;
}
.ebook_detail .page-row-filter .breadcrumb li.breadcrumb-item a {
  font-size: 12px;
  color: #6C757D;
}
.ebook_detail .page-row-filter .breadcrumb li.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes {
  margin: 0 0 20px;
  padding: 10px 0;
  border-bottom: 1px solid #ccc;
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes li {
  float: left;
  list-style: none;
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes li a {
  display: inline-block;
  padding: 2px 12px;
  position: relative;
  font-size: 12px;
  color: #6C757D;
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes li a:after {
  content: "\f105";
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  position: absolute;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  top: 4px;
  right: -3px;
}
#couponmodal {
  overflow-y: scroll;
  height: 360px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1000%26quot%3b)' fill='none'%3e%3cuse xlink:href='%23SvgjsSymbol1007' x='0' y='0'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsSymbol1007' x='720' y='0'%3e%3c/use%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1000'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cpath d='M-1 0 a1 1 0 1 0 2 0 a1 1 0 1 0 -2 0z' id='SvgjsPath1003'%3e%3c/path%3e%3cpath d='M-3 0 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' id='SvgjsPath1001'%3e%3c/path%3e%3cpath d='M-5 0 a5 5 0 1 0 10 0 a5 5 0 1 0 -10 0z' id='SvgjsPath1005'%3e%3c/path%3e%3cpath d='M2 -2 L-2 2z' id='SvgjsPath1002'%3e%3c/path%3e%3cpath d='M6 -6 L-6 6z' id='SvgjsPath1004'%3e%3c/path%3e%3cpath d='M30 -30 L-30 30z' id='SvgjsPath1006'%3e%3c/path%3e%3c/defs%3e%3csymbol id='SvgjsSymbol1007'%3e%3cuse xlink:href='%23SvgjsPath1001' x='30' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='30' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='30' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='30' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='30' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='30' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='30' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='30' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='30' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='30' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='90' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1006' x='90' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)' stroke-width='3'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='90' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='90' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='90' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='90' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='90' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='90' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='90' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='90' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='150' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='150' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='150' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='150' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='210' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='210' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='210' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='210' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1006' x='210' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)' stroke-width='3'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='210' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='270' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='330' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='330' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='330' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='330' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='330' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='330' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='330' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='330' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='330' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='330' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='390' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='390' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='390' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='390' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='390' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='390' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='390' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='390' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='390' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='390' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='450' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='450' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='510' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='510' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='510' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='510' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='510' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='510' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='570' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='570' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='570' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='570' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='630' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='630' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='630' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='690' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='690' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='690' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='690' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1006' x='690' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)' stroke-width='3'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='690' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='690' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='690' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='690' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='690' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3c/symbol%3e%3c/svg%3e");
}
#couponmodal .availCoupon {
  border: 2px dashed red;
}
#couponmodal .wraps {
  border-radius: 3px;
  padding: 3px;
}
lottie-player {
  position: absolute;
  top: 20px;
}
.radio-toolbar input[type="radio"] {
  opacity: 0;
  position: fixed;
  width: 0;
}
.radio-toolbar label {
  display: block;
  background-color: #4c4;
  padding: 10px 20px;
  font-family: sans-serif, Arial;
  font-size: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}
.radio-toolbar input[type="radio"]:checked + label {
  background-color: lightgreen;
  color: #fff;
}
.orderDetailsModal .image-wrapper-od {
  display: grid;
  grid-gap: 1rem;
  padding: 16px;
  grid-template-columns: 30% 70%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .orderDetailsModal .image-wrapper-od {
    grid-template-columns: repeat(2, 1fr);
  }
}
.orderDetailsModal .image-wrapper-od .od-img .uncoverdetail {
  height: 150px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #F79420;
  width: 120px;
}
.orderDetailsModal .image-wrapper-od .od-img .uncoverdetail p {
  text-align: center;
  text-transform: capitalize;
  font-size: 16px;
  font-style: italic;
  color: #FFFFFF;
  padding: 60% 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .orderDetailsModal .image-wrapper-od .od-img .uncoverdetail p {
    padding: 50% 0;
  }
}
.orderDetailsModal .image-wrapper-od img {
  height: 150px;
  border-radius: 5px;
  width: 100px;
}
.orderDetailsModal .orderDetails-wrapper {
  padding-left: 16px;
}
.orderDetailsModal .orderDetails-wrapper .first-table-cart {
  width: 150px !important;
}
.orderDetailsModal .orderDetails-wrapper .second-table-cart {
  width: 40px !important;
}
.percentageOff {
  background: #01b901;
  padding: 2px;
  width: 100px;
  text-align: center;
  color: #fff;
  border-radius: 2px;
}
.star-rating i {
  color: #F79420;
}
.shimmer {
  -webkit-mask: linear-gradient(-40deg, #000 30%, #0005, #000 60%) right / 300% 100%;
  animation: shimmer 2.5s infinite;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.affiliationPrices {
  margin-top: 10px;
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.15);
}
@media (max-width: 768px) {
  .affiliationPrices {
    width: 100%;
  }
}
.affiliationPrices_title p {
  font-weight: 500;
}
.affiliationPrices .affiliationLinks {
  display: flex;
  gap: 1rem;
  margin-top: 10px;
}
.affiliationPrices .affiliationLinks .fieldSet {
  position: relative;
  border: 1px solid orange;
  padding: 8px;
  width: 175px;
  border-radius: 5px;
}
.affiliationPrices .affiliationLinks .fieldSet .ctaPrice {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
}
@media (max-width: 768px) {
  .affiliationPrices .affiliationLinks .fieldSet {
    width: 150px;
  }
}
.affiliationPrices .affiliationLinks .fieldSet:hover {
  border: 1px solid orange;
}
.affiliationPrices .affiliationLinks .fieldSet span img {
  width: 60px;
}
.affiliationPrices .affiliationLinks .fieldSet span img.flipkartLogo {
  width: 70px;
  position: relative;
  z-index: 2;
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend {
  position: absolute;
  top: 0;
  margin: -9px 0 0 -0.5rem;
  background: #fff;
  margin-left: 65px;
  width: 80px;
  text-align: center;
  z-index: 1;
  font-size: 18px;
  font-weight: 500;
  color: #000;
}
@media (max-width: 768px) {
  .affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend {
    width: 63px;
    font-size: 14px;
  }
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend span {
  margin-left: -4px;
}
.affiliationLinks .fieldSet:hover {
  background: #FFF0CC;
}
.affiliationLinks .fieldSet:hover .fieldSet_legend {
  background: linear-gradient(to bottom, #fff, #FFF0CC);
}
.affiliationLinksLoader {
  border: 1px solid #0003;
  width: 140px;
  height: 50px;
  border-radius: 5px;
  background: linear-gradient(to right, #F6F6F6 8%, #F0F0F0 18%, #F6F6F6 33%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiliationLinksLoader p {
  font-size: 12px;
  font-weight: 500;
  color: #0008;
  text-align: center;
  line-height: initial;
}
.aa:after {
  position: absolute;
  margin-left: 0.1rem;
  content: ' ...';
  animation: loading steps(4) 2s infinite;
  clip: rect(auto, 0px, auto, auto);
}
@keyframes loading {
  to {
    clip: rect(auto, 20px, auto, auto);
  }
}
.afPrice {
  font-size: 24px;
  margin-bottom: 12px;
  color: #000;
  font-weight: 500;
}
.dots {
  margin-left: 5px;
}
.dots .dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  margin-right: 3px;
}
.dots .dot:nth-child(1) {
  animation: dotsJump 0.8s 0.1s ease infinite;
}
.dots .dot:nth-child(2) {
  animation: dotsJump 0.8s 0.2s ease infinite;
}
.dots .dot:nth-child(3) {
  animation: dotsJump 0.8s 0.3s ease infinite;
}
.span-1 {
  color: rgba(0, 0, 0, 0.4);
}
.span-2 {
  color: rgba(0, 0, 0, 0.6);
  font-weight: bolder;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ctaPrice {
  margin-top: 3px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 12px;
}
@keyframes dotsJump {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, 5px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
.subscription {
  min-width: 350px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (max-width: 768px) {
  .subscription {
    min-width: 100%;
  }
}
.subscription__sec {
  margin-bottom: 1rem;
}
.subscription__sec label {
  display: block;
}
.subscription__sec select {
  padding: 8px 5px;
  border: 1px solid rgba(0, 0, 0, 0.4);
  border-radius: 4px;
  display: block;
  width: 100%;
}
.subscription__sec select:focus-visible {
  border: 1px solid;
  border-color: red !important;
  outline: none;
}
.subscription__sec-3 a {
  display: flex;
  text-align: center;
  justify-content: center;
}
.freeChapterDiv .freeChapterLink {
  background: #fff !important;
  border-radius: 5px !important;
  border: 1px solid #F79420 !important;
  padding: 10px;
  color: #000;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .freeChapterDiv {
    width: 100% !important;
  }
}
.book_variants {
  display: grid;
  grid-gap: 1rem;
  grid-template-columns: 240px 240px 240px;
  margin-top: 1.4rem;
  background: #fff;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants {
    grid-template-columns: auto;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}
.book_variants .book_variant_card {
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 5px 8px;
  background: #fff;
  margin-bottom: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card {
    width: 66%;
    margin-bottom: 0;
    min-height: auto;
  }
}
.book_variants .book_variant_card-body {
  display: flex;
  flex-direction: column;
}
.book_variants .book_variant_card-body .book_variant-name {
  position: relative;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-name {
    margin-bottom: 0 !important;
  }
}
.book_variants .book_variant_card-body .book_variant-name .variantType {
  display: flex;
  align-items: center;
  color: #E61F2A;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-name .variantType {
    font-size: 12px;
  }
}
.book_variants .book_variant_card-body .book_variant-name .variantType img {
  width: 20px;
  margin-left: 5px;
}
.book_variants .book_variant_card-body .book_variant-name span.badge {
  display: initial;
  border-radius: 50px;
  font-size: 10px;
  padding: 3px 5px;
  font-weight: normal;
  background: var(--info);
  position: absolute;
  right: 1px;
  top: 1px;
  height: auto;
  width: 60px;
}
.book_variants .book_variant_card-body .offerDiv .savePrice {
  font-size: 13px;
  margin-top: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .offerDiv .savePrice {
    margin-top: 3px;
  }
}
.book_variants .book_variant_card-body .book_variant-price {
  display: flex;
  gap: 8px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-price {
    margin-top: 10px;
  }
}
.book_variants .book_variant_card-body .book_variant-price .offer_price {
  font-size: 24px;
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-price .offer_price {
    font-size: 18px;
  }
}
.book_variants .book_variant .addtoCartBtn {
  border-radius: 3px;
  margin-bottom: 0!important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant .addtoCartBtn {
    width: 38% !important;
    margin-bottom: 0 !important;
  }
}
.image_wrapper {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  flex-direction: row-reverse;
}
.featureIcons {
  display: flex;
}
.featureIcons ul {
  list-style: none;
  text-align: justify;
  padding-left: inherit;
  width: 80px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.22);
  background: #f4f4f4;
  border-radius: 5px;
  margin-bottom: 0;
}
.featureIcons ul li {
  padding: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.featureIcons ul li p {
  font-size: 12px;
}
.featureIcons ul li img {
  width: 40px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .image_wrapper {
    flex-direction: unset;
  }
  .featureIcons {
    display: flex;
    height: unset;
    width: auto;
  }
  /*Cover image styles*/
  .image_wrapper .book_image {
    height: auto !important;
  }
  .image_wrapper .book_image .bookShadow {
    height: 100% !important;
  }
  .image_wrapper .book_image .bookShadow .image-wrapper img {
    width: 100%;
    height: 100%;
  }
  .freeChapterDiv .freeChapterLink {
    display: block;
    text-align: center;
    box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
  }
  .freeChapterDiv .freeChapterLink:active {
    transform: scale(0.9);
  }
  .featureIcons ul {
    width: 75px;
  }
}
.available-cards a {
  margin-bottom: 30px;
  min-height: 145px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .available-cards a {
    margin-bottom: 20px;
  }
}
.available-cards a.access-granted .card {
  border: 1px solid #0F0839 !important;
  z-index: 0;
  position: relative;
  background: #fffaec;
}
.available-cards a.access-granted .card:hover {
  border-color: #0F0839 !important;
}
.available-cards a.access-granted .card:after {
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  z-index: 10;
  display: block;
  width: 25%;
  height: 100%;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 100%);
  -webkit-transform: skewX(-20deg);
  transform: skewX(-20deg);
  -webkit-animation: shine 2s infinite;
  animation: shine 2s infinite;
}
.available-cards .card {
  box-shadow: 0 3px 7px #0000001A;
  transition: all 0.4s linear;
  padding: 25px;
  overflow: hidden;
}
.available-cards .card .institute-tag {
  position: absolute;
  top: -15px;
  right: -15px;
  z-index: 1;
  background: #212121;
  height: 50px;
  width: 50px;
  border-radius: 50px;
  color: #FFFFFF;
  transform: rotate(45deg);
  display: flex;
  justify-content: center;
  align-items: end;
  font-weight: 600;
  padding-bottom: 5px;
  box-shadow: 0 2px 5px #0000001A;
}
.available-cards .card h5 {
  font-size: 20px;
  transition: all 0.25s linear;
  color: #F79420;
}
.available-cards .card p {
  line-height: normal;
}
.available-cards .card .image-icon {
  width: 50px;
  position: absolute;
  right: 10px;
  opacity: 0.1;
  bottom: 10px;
}
.available-cards .card .image-icon img {
  width: 100%;
  height: auto;
}
.available-cards .card .arrow-link-right {
  margin: 10px 0 -10px 0;
}
.available-cards .card .arrow-link-right svg {
  width: 28px;
  height: 28px;
  color: #212121;
  transition: all 0.25s linear;
}
.available-cards .card:hover {
  box-shadow: 0 12px 15px #0000001A;
  background-color: #fffaec;
}
.available-cards .card:hover .arrow-link-right svg {
  margin-left: 15px;
}
.available-cards .shape-arrow {
  position: absolute;
  left: 35%;
  bottom: -30%;
  z-index: -1;
  width: 150px;
  opacity: 0.1;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .available-cards .shape-arrow {
    width: 100px;
    top: 0;
  }
}
.available-cards .shape-arrow img {
  width: 100%;
  height: auto;
}
.available-cards .shape-animated {
  position: absolute;
  border-radius: 100%;
  z-index: -1;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .available-cards .shape-animated {
    animation: rotateAnimation infinite linear;
  }
}
.available-cards .shape-round {
  width: 150px;
  height: 150px;
  background: #f9edd1;
  animation-duration: 30s;
  bottom: 0;
  right: 15%;
}
.available-cards .shape-bg {
  width: 1400px;
  height: 1400px;
  position: absolute;
  right: -40%;
  bottom: -100%;
  z-index: -2;
}
@media (max-width: 575.98px) {
  .available-cards .shape-bg {
    right: -100%;
    bottom: 10%;
  }
}
.available-cards .shape-bg svg {
  width: 1400px;
  height: 1400px;
}
@-webkit-keyframes rotateAnimation {
  0% {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg);
  }
  20% {
    -webkit-transform: translate(73px, 1px) rotate(36deg);
    transform: translate(73px, 1px) rotate(36deg);
  }
  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg);
    transform: translate(141px, 72px) rotate(72deg);
  }
  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg);
    transform: translate(83px, 122px) rotate(108deg);
  }
  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg);
    transform: translate(-40px, 72px) rotate(144deg);
  }
  to {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg);
  }
}
@keyframes rotateAnimation {
  0% {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg);
  }
  20% {
    -webkit-transform: translate(73px, 1px) rotate(36deg);
    transform: translate(73px, 1px) rotate(36deg);
  }
  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg);
    transform: translate(141px, 72px) rotate(72deg);
  }
  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg);
    transform: translate(83px, 122px) rotate(108deg);
  }
  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg);
    transform: translate(-40px, 72px) rotate(144deg);
  }
  to {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg);
  }
}
.db-main {
  overflow-x: hidden;
  overflow-y: hidden;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .db-main .welcome-user h3 {
    font-size: 1.5rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .db-main .db-section-title h5 {
    font-size: 1rem;
  }
}
.db-main .top_main_mobile__menu {
  margin-bottom: 2rem;
  margin-top: 1rem;
}
.db-main .top_main_mobile__menu .mdl-button-modifier {
  text-transform: inherit;
  background: #FFFFFF;
  box-shadow: 0 2px 5px #0000001A;
  border-radius: 10px;
  margin: 0 0.25rem;
  height: 80px;
  display: flex;
  justify-content: start;
  align-items: center;
  color: #6C757D;
  font-size: 12px;
  flex-direction: column;
  padding: 20px 15px;
  line-height: 1.2;
  text-align: center;
}
@media (max-width: 575.98px) {
  .db-main .top_main_mobile__menu .mdl-button-modifier {
    height: 90px;
    padding: 18px 15px;
  }
}
.db-main .top_main_mobile__menu .mdl-button-modifier:hover {
  color: #06D781;
}
.db-main .top_main_mobile__menu .mdl-button-modifier img {
  width: 18px;
  margin-bottom: 7px;
}
.db-main .top_main_mobile__menu .mdl-button-modifier:nth-child(6) img {
  width: 20px;
}
.db-main .db-common-info {
  background: #FFFFFF;
}
.db-main .db-common-info .card {
  border: none;
  position: relative;
  border-right: 1px solid #EEEEEE;
  border-bottom: 1px solid #EEEEEE;
  border-radius: 0;
  justify-content: center;
  min-height: 150px;
  box-shadow: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .db-main .db-common-info .card {
    min-height: 100px;
    padding: 2rem 0.5rem;
  }
  .db-main .db-common-info .card:nth-child(2n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(2n):after,
  .db-main .db-common-info .card:nth-child(2n):before {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .db-main .db-common-info .card {
    padding: 2rem 1rem;
  }
  .db-main .db-common-info .card:nth-child(3n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(3n):after,
  .db-main .db-common-info .card:nth-child(3n):before {
    display: none;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .db-main .db-common-info .card {
    padding: 2rem 1rem;
  }
  .db-main .db-common-info .card:nth-child(4n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(4n):after,
  .db-main .db-common-info .card:nth-child(4n):before {
    display: none;
  }
}
@media (min-width: 1200px) {
  .db-main .db-common-info .card:nth-child(6n) {
    border-right: none;
  }
  .db-main .db-common-info .card:nth-child(6n):after,
  .db-main .db-common-info .card:nth-child(6n):before {
    display: none;
  }
}
.db-main .db-common-info .card:before {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background: #FFFFFF;
  right: -15px;
  top: -20px;
  border-radius: 50px;
  z-index: 1;
}
.db-main .db-common-info .card:after {
  content: '';
  position: absolute;
  width: 30px;
  height: 30px;
  background: #FFFFFF;
  right: -15px;
  bottom: -20px;
  border-radius: 50px;
  z-index: 1;
}
.db-main .db-common-info .card a img {
  width: 50px;
  height: 50px;
  border-radius: 50px;
  margin: 0 auto;
  padding: 2px;
}
@media (max-width: 575.98px) {
  .db-main .db-common-info .card a img {
    width: 45px;
    height: 45px;
  }
}
.db-main .db-common-info .card a p {
  margin-top: 10px;
  color: rgba(68, 68, 68, 0.85);
}
@media (max-width: 575.98px) {
  .db-main .db-common-info .card a p {
    font-size: 12px;
  }
}
.db-main .db-common-info .card a:hover {
  text-decoration: none;
}
.db-main .db-common-info .card a:hover p {
  color: #0F0839;
}
.db-main .db-common-info .card .todo-count {
  width: 50px;
  height: 50px;
  display: inline-block;
}
.db-main .db-common-info .card .todo-count small {
  position: absolute;
  background: #FF4B33;
  padding: 1px;
  width: 22px;
  height: 22px;
  text-align: center;
  border-radius: 50px;
  color: #FFFFFF;
  font-weight: 500;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px #0000001A;
}
.db-main .db-common-info .card.institute-access.access-granted {
  border: 1px solid #0F0839;
  background-color: #F1F1F1;
  z-index: 0;
  position: relative;
}
.db-main .db-common-info .card.institute-access.access-granted:after {
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  z-index: 10;
  display: block;
  width: 25%;
  height: 100%;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-20deg);
  transform: skewX(-20deg);
  -webkit-animation: shine 2s infinite;
  animation: shine 2s infinite;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .db-main .db-common-info .card.institute-access.access-granted:after {
    display: block;
  }
}
.db-main .db-common-info .card.institute-access.access-granted p {
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .db-main .db-common-info.has-institutes .card:after,
  .db-main .db-common-info.has-institutes .card:before {
    display: none;
  }
}
@media (min-width: 1200px) {
  .db-main .four-cards .card:last-child {
    border-right: none;
  }
  .db-main .four-cards .card:last-child:after,
  .db-main .four-cards .card:last-child:before {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .db-main .three-cards .card:last-child {
    border-right: none;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .db-main .three-cards .card:last-child:after,
  .db-main .three-cards .card:last-child:before {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .db-main .two-cards .card:last-child {
    border-right: none;
  }
  .db-main .two-cards .card:last-child:after,
  .db-main .two-cards .card:last-child:before {
    display: none;
  }
}
.db-main .single-card .card {
  border: none;
}
.db-main .single-card .card:after,
.db-main .single-card .card:before {
  display: none;
}
.my_books .search-icon-lib {
  width: 35px;
  height: 35px;
  cursor: pointer;
  border-width: 2px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .search-icon-lib {
    width: 32px;
    height: 32px;
  }
}
.my_books .search-box #search-book,
.my_books .search-box .submit-search-btn {
  height: 40px;
}
.my_books .search-box ul.typeahead {
  top: 54px !important;
  border-color: #0F0839;
  box-shadow: 0 1px 4px #0000001A;
}
.my_books .username p.total-books {
  font-size: 18px;
  font-weight: 500;
  color: #0F0839;
}
.my_books #subjectFilter .dropdown {
  font-size: 13px;
}
.my_books #subjectFilter .dropdown #sortBy {
  border-radius: 5px;
  color: #0F0839;
  font-size: 13px;
  border-color: #0F0839;
}
.my_books .select-institute-dropdown {
  width: 250px;
}
.my_books .select-institute-dropdown .dropdown-toggle {
  height: 44px;
  width: 250px;
  text-align: left;
  background-color: #0F0839 !important;
  box-shadow: inset 0 2px 4px #0000001A;
  color: #FFFFFF !important;
  border-radius: 50px;
  border: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .select-institute-dropdown .dropdown-toggle {
    font-size: 13px;
  }
}
.my_books .select-institute-dropdown .dropdown-toggle:after {
  position: absolute;
  right: 15px;
  top: 20px;
}
.my_books .select-institute-dropdown .dropdown-toggle #selectedInstitute {
  width: 200px;
  position: relative;
  display: block;
  word-break: break-word;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.my_books #institute-list {
  transform: translate3d(0px, 48px, 0px) !important;
  background: #FFFFFF;
  box-shadow: 0 2px 10px #0000001A;
  border-radius: 12px;
  width: 100%;
  z-index: 991;
}
.my_books #institute-list li.dropdown-item {
  white-space: pre-wrap;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books #institute-list li.dropdown-item {
    font-size: 13px;
  }
}
.my_books #institute-list li.dropdown-item:hover {
  cursor: pointer;
  color: #0F0839;
}
.my_books #institute-list li.dropdown-item:focus,
.my_books #institute-list li.dropdown-item:active {
  background-color: transparent;
  outline: 0;
}
.my_books .queue-list-btn {
  position: relative;
  z-index: 99;
  margin-bottom: -10px;
}
.my_books .queue-list-btn a {
  background: #0F0839;
  box-shadow: 0 2px 4px #0000001A !important;
  border-radius: 50px;
  color: #FFFFFF;
  margin-right: 0;
  font-size: 14px;
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .my_books .queue-list-btn a {
    margin-right: 0;
  }
}
@media (max-width: 575.98px) {
  .my_books .queue-list-btn a {
    font-size: 12px;
  }
}
.my_books .books-content-wrapper {
  margin-top: 2rem;
}
.my_books .access_code h4 {
  color: #444444;
}
.my_books .access_code .submit_icon span {
  font-size: 70px;
}
.my_books .access_code .submit_icon p {
  color: #6C757D;
}
.my_books .access_code .fetch_icon span,
.my_books .access_code .invalid_icon span {
  opacity: 0.7;
}
.my_books .showMore,
.my_books .showLess {
  float: right;
  margin-right: 35px;
  font-size: 14px;
  color: #0F0839;
  cursor: pointer;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .my_books .showMore,
  .my_books .showLess {
    margin-right: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .showMore,
  .my_books .showLess {
    display: none;
  }
}
.my_books #content-data-books h4 {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 10px;
}
.my_books #content-data-institute-books h4,
.my_books #institute-recent-read-books h4,
.my_books #content-data-search-books h4,
.my_books #content-data-books-queue h4 {
  color: #17A2B8;
  font-weight: 400;
  font-size: 18px;
  padding: 10px 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books #content-data-institute-books h4,
  .my_books #institute-recent-read-books h4,
  .my_books #content-data-search-books h4,
  .my_books #content-data-books-queue h4 {
    font-size: 16px;
  }
}
.my_books .package_book_collapse {
  position: relative;
  margin: 0;
}
.my_books .package_books {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  position: relative;
  left: 0;
  right: 0;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  z-index: 99;
}
.my_books .package_books .package_book_list {
  padding-bottom: 0;
}
.my_books .package_books .package_book_list:last-child {
  padding-bottom: 0;
}
.my_books .package_books .package_book_list a {
  color: #212121;
  width: 85px;
  font-size: 11px;
  margin: 0 10px;
}
.my_books .package_books .package_book_list a:hover {
  color: #0F0839;
}
.my_books .package_books .package_book_list a span {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.my_books #content-data-books-queue .no-books-available p {
  font-size: 15px;
}
.my_books #content-data-books-queue .card-body .card-text:hover {
  text-decoration: none;
  color: #444444;
}
.my_books #content-data-books-queue .card img {
  border-radius: 4px;
}
.my_books .no-books-available p {
  color: #949494;
  line-height: normal;
}
.my_books .no-books-available .click-here-link {
  border: none;
  color: #FFFFFF;
  font-weight: 700;
  font-size: 15px;
}
.my_books #loadMoreButton,
.my_books #loadMoreButtonFree,
.my_books #loadMoreButtonPaid,
.my_books #loadMoreButtonSelf {
  text-align: center;
}
.my_books #loadMoreButton button,
.my_books #loadMoreButtonFree button,
.my_books #loadMoreButtonPaid button,
.my_books #loadMoreButtonSelf button {
  width: 200px;
  height: 40px;
  border-radius: 16px;
  border: none;
}
.my_books #infiniteLoading .loading-div p {
  position: relative;
  letter-spacing: 1px;
  color: #2C3E50;
  font-size: 15px;
}
.my_books #infiniteLoading .loading-div img {
  width: 120px;
  margin-top: -55px;
}
.my_books #infiniteLoading .loading-div .alert {
  font-size: 14px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .recent-read-books-list {
    overflow: auto;
    flex-wrap: nowrap;
  }
}
.my_books .has-latest-books .new-book .card {
  border: 2px solid #0F0839;
  position: relative;
  overflow: hidden;
}
.my_books .has-latest-books .new-book .card .lib-showcase {
  position: relative;
}
.my_books .has-latest-books .new-book .card .lib-showcase:after {
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  z-index: 10;
  display: block;
  width: 25%;
  height: 100%;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-20deg);
  transform: skewX(-20deg);
  -webkit-animation: shine 2s infinite;
  animation: shine 2s infinite;
}
.libwonder .my_books {
  margin-top: 0 !important;
}
.libwonder #goBack {
  display: none;
}
.libwonder .library .tab-content {
  margin-top: 0;
}
.libwonder .library .tab-content .card {
  background-color: #FFFFFF !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .libwonder .library .tab-content .card img {
    height: 192px !important;
  }
}
.libwonder #institute-list {
  top: 0 !important;
}
.libwonder .select-institute-dropdown .dropdown-toggle {
  background-color: #0F0839 !important;
}
.libwonder .select-institute-dropdown .dropdown-toggle.generate {
  display: block !important;
  background-color: #0F0839 !important;
}
.libwonder .no-books-available .click-here-link {
  color: #FFFFFF !important;
  background: #0F0839 !important;
  font-weight: 400;
}
.libwonder .card-body .card-text {
  height: auto !important;
}
@media (max-width: 575px) {
  .wonderslate_main div#button-container div a,
  .wonderslate_main #allBooks {
    width: 100%;
    font-size: 14px !important;
    margin-right: 10px;
  }
}
.wonderslate_main #paidBooks,
.wonderslate_main #freeBooks {
  margin-right: 4px;
  color: #17A2B8;
  font-weight: 400;
  font-size: 18px;
  cursor: pointer;
  padding: 5px 5px;
  border: 1px solid;
  border-radius: 10px;
}
.wonderslate_main #allBooks {
  margin-right: 4px;
  font-weight: 400;
  cursor: pointer;
  border: 1px solid;
  font-size: 15px !important;
  padding: 5px 15px !important;
  border-radius: 30px !important;
  background-color: #17A2B8 !important;
  color: white !important;
}
.wonderslate_main div#button-container div a {
  font-size: 15px !important;
  padding: 5px 15px !important;
  border-radius: 30px !important;
}
.wonderslate_main div#button-container div {
  margin-right: 2%;
}
.wonderslate_main div#button-container div a#total-freebooks-of-user,
.wonderslate_main div#button-container div a#total-paidbooks-of-user {
  padding: 5px !important;
}
.wonderslate_main .mystyle {
  background-color: #17A2B8 !important;
  color: white !important;
  font-size: 25px;
}
.wonderslate_main div#button-inner-container p {
  padding-left: 10px;
}
.wonderslate_main p#total-allbooks-of-user {
  padding-left: 10px;
  font-size: 10px;
}
.wonderslate_main div#button-inner-container p:after {
  content: ")";
}
.wonderslate_main div#button-inner-container p:before {
  content: "(";
}
.wonderslate_main div#button-inner-container p {
  font-size: 10px;
}
.wonderslate_main div#content-data-institute-books p:after {
  content: ")";
}
.wonderslate_main div#content-data-institute-books p:before {
  content: "(";
}
.wonderslate_main p#total-books-of-user {
  display: inline;
  font-size: 12px;
  padding-right: 10px;
}
.wonderslate_main p#total-books-of-user:before {
  content: '(';
}
.wonderslate_main p#total-books-of-user:after {
  content: ')';
}
.wonderslate_main .d-flex.justify-content-between.align-items-center.queue-list-btn.show-library.mb-3 h4 {
  background-color: #17A2B8 !important;
  color: white !important;
  margin-right: 4px;
  font-weight: 400;
  font-size: 15px !important;
  cursor: pointer;
  padding: 5px 10px !important;
  border: 1px solid;
  border-radius: 20px;
}
.wonderslate_main div#bestSellerBooksContainer {
  display: none !important;
}
.wonderslate_main p#searchBooksNo {
  display: inline;
  padding: 0px 5px;
}
.wonderslate_main #searchBooksNo:before {
  content: "(";
}
.wonderslate_main #searchBooksNo:after {
  content: ")";
}
.wonderslate_main div#content-data-search-books h4 {
  background-color: #17A2B8 !important;
  color: white !important;
  margin-right: 4px;
  font-weight: 400;
  font-size: 15px !important;
  cursor: pointer;
  padding: 5px 10px !important;
  border: 1px solid;
  border-radius: 20px;
  display: inline;
}
.wonderslate_main .row.institute-books-list.pt-3 {
  width: 100%;
}
.wonderslate_main div#button-container {
  height: 30px;
}
.my_books #libraryTabs {
  flex-wrap: nowrap;
  overflow: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.my_books #libraryTabs::-webkit-scrollbar {
  display: none;
}
.my_books #libraryTabs a.current {
  border-color: transparent;
  background-color: #0F0839 !important;
  color: #FFFFFF !important;
}
.my_books #libraryTabs .institute-tab {
  min-width: 180px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.my_books #instituteClasses select {
  width: 250px;
  overflow: hidden !important;
  text-overflow: ellipsis;
  outline: 0;
  box-shadow: none;
}
.my_books #instituteClasses select:focus,
.my_books #instituteClasses select:active {
  border-color: #0F0839;
}
@media (max-width: 575.98px) {
  .my_books #instituteClasses select {
    width: 100%;
  }
}
.my_books #libraryFilter h5 {
  font-size: 16px;
  font-weight: normal;
}
.my_books #libraryFilter #resetLibraryFilter:not(:disabled):not(.disabled) {
  color: #2F80ED;
}
.my_books #libraryFilter .available-filters a,
.my_books #libraryFilter .available-filters select {
  height: 30px;
  line-height: 1.75;
  outline: 0;
  box-shadow: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books #libraryFilter .available-filters a,
  .my_books #libraryFilter .available-filters select {
    width: 48%;
    margin-right: 10px !important;
  }
  .my_books #libraryFilter .available-filters a:nth-child(2),
  .my_books #libraryFilter .available-filters select:nth-child(2),
  .my_books #libraryFilter .available-filters a:last-child,
  .my_books #libraryFilter .available-filters select:last-child {
    margin-right: 0 !important;
  }
}
.my_books #libraryFilter .available-filters a.selected,
.my_books #libraryFilter .available-filters select.selected {
  border-color: transparent;
  background-color: #0F0839;
  color: #FFFFFF;
}
.my_books #libraryFilter .available-filters select {
  padding: 0.3rem;
}
.my_books #libraryFilter .available-filters select:focus,
.my_books #libraryFilter .available-filters select:active {
  border-color: #0F0839;
}
.my_books .library-book a.btn-outline-primary-modifier:hover,
.my_books .library-book a.btn-outline-primary-modifier:focus {
  background-color: #0F0839;
  color: #FFFFFF;
}
.my-activity .history-records .all-container .container-wrapper {
  width: auto;
  margin-top: 0;
  position: relative;
  min-height: 100px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my-activity .history-records .all-container .container-wrapper {
    min-height: 90px;
  }
}
.my-activity .history-records .all-container .container-wrapper:after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  background-image: url("../../images/ws/horizontal-separator-line.svg");
  background-repeat: no-repeat;
  background-size: contain;
  width: 100%;
  height: 4px;
}
.my-activity .history-records .all-container .container-wrapper:last-child:after {
  display: none;
}
.my-activity .history-records .all-container .container-wrapper .media {
  box-shadow: none;
}
.my-activity .history-records .all-container .container-wrapper .media .media-body .title {
  margin: 0;
  font-size: 13px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  -webkit-line-clamp: 1;
}
.my-activity .history-records .all-container .container-wrapper .media .media-body .title span {
  color: #949494;
  font-size: 12px;
}
.my-activity .history-records .all-container .container-wrapper .media .media-body .book-name {
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  font-weight: normal;
  font-size: 15px;
}
.my-activity .history-records .all-container .container-wrapper .media .media-body p.updated_info {
  font-size: 13px;
  line-height: normal;
}
.my-activity .history-records #showMore a {
  background: #0F0839;
  color: #FFFFFF;
  font-size: 12px;
  padding: 2px;
  border-radius: 0 0 10px 10px;
}
.my-activity .history-records #showMore a:hover {
  text-decoration: none;
}
.my-activity .history-records .empty_activity p {
  color: #949494;
}
.my-materials .media {
  border-radius: 10px !important;
}
.my-materials .box {
  min-height: 100px !important;
  height: 100% !important;
  border-radius: 10px 0px 0px 10px !important;
}
.my-materials .cardLabel {
  writing-mode: vertical-lr;
  text-orientation: mixed;
  transform: rotate(180deg);
  padding: 12px;
  text-align: center;
  color: #FFFFFF !important;
  letter-spacing: 2px;
  font-weight: 800 !important;
}
.my-materials .align-self-center {
  transform: rotate(270deg);
  margin-top: 5px !important;
  margin-left: 5px !important;
}
.my-materials .reset-switch {
  padding: 10px !important;
}
.my-materials #favButton,
.my-materials #allButton {
  font-size: 12px;
}
.my-materials #favButton i,
.my-materials #allButton i {
  font-size: 12px;
}
.my-materials .new-folder {
  font-size: 12px;
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
@media (max-width: 575.98px) {
  .my-materials .new-folder {
    padding: 0.5rem;
  }
}
.my-materials .new-folder i {
  margin-right: 10px;
}
@media (max-width: 575.98px) {
  .my-materials .new-folder i {
    margin-right: 5px;
  }
}
.my-materials #search-book,
.my-materials .submit-search-btn {
  height: 34px;
  opacity: 1;
}
.my-materials #search-book i,
.my-materials .submit-search-btn i {
  font-size: 18px;
}
.my-materials #search-book:disabled i,
.my-materials .submit-search-btn:disabled i {
  opacity: 0.5;
}
@media (max-width: 575.98px) {
  .my-materials .folder-box:nth-child(even) {
    padding-right: 0;
  }
}
.my-materials .folder-box .inside-folder-text {
  border: 1px solid #0F0839;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  background: #FFFFFF;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: start;
  width: 100%;
  font-size: 14px;
  padding: 0 10px;
  text-align: left;
}
.my-materials .folder-box .inside-folder-text:hover {
  box-shadow: none;
}
.my-materials .folder-box .inside-folder-text i {
  color: #0F0839;
}
.my-materials .folder-box .inside-folder-text span {
  display: block;
  width: 75%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  text-transform: capitalize;
  color: #212121;
  padding: 7px;
  font-weight: 400;
}
.my-materials .folder-box .inside-folder-text span.mdl-button__ripple-container {
  width: 100%;
}
.my-materials .materialsAddDropdown button {
  background: #0F0839;
  color: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  font-size: 12px;
  width: 90px;
  height: 36px;
  font-weight: 400;
  margin-left: 1rem;
}
.my-materials .materialsAddDropdown button:hover {
  color: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
}
.my-materials .materialsAddDropdown button i {
  color: #FFFFFF;
  font-size: 18px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.my-materials .materialsAddDropdown button i.rotated {
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
}
.my-materials .materialsAddDropdown button:after {
  margin-left: 0.5em;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.my-materials .materialsAddDropdown.show {
  z-index: 9992;
}
.my-materials .materialsAddDropdown.show button {
  background: #000000;
}
.my-materials .materialsAddDropdown.show button:after {
  transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
}
.my-materials .materialsAddDropdown .dropdown-menu {
  padding: 0;
  margin: 0;
  background: none;
  border: none;
}
.my-materials .materialsAddDropdown .dropdown-menu a.dropdown-item {
  margin: 5px 0;
  padding: 0.375rem 0.75rem;
  width: 100%;
  justify-content: flex-start;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.my-materials .materialsAddDropdown .dropdown-menu a.dropdown-item:hover {
  margin-left: 5px;
}
.my-materials .materialsAddDropdown .dropdown-menu a.new-folder {
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  font-weight: normal;
}
.my-materials .add-resource-btn {
  width: 80px;
  margin-left: 1rem;
}
.my-materials .add-resource-btn i {
  margin-right: 0;
}
.my-materials #flashCardSets .all-container .container-wrapper {
  width: auto;
  padding-top: 10px !important;
}
.my-materials .resource-set .media {
  min-height: 100px;
  align-items: unset !important;
}
.my-materials .resource-set .media p {
  font-size: 12px !important;
}
.my-materials .resource-set .media .mymaterial-img a {
  min-height: auto;
}
.my-materials .resource-set:hover .media {
  box-shadow: 0 0 10px #00000040;
}
.my-materials .resource-set h5.title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.my-materials .resource-set h5.title a.chapter_txt {
  min-height: auto;
}
.my-materials .resource-set .resource-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.my-materials .resource-set .resource-actions .share-resource-btn {
  background: none;
  border: none;
  text-align: center;
  padding: 0;
}
.my-materials .resource-set .resource-actions .share-resource-btn:hover i {
  color: #27AE60;
}
.my-materials .resource-set .resource-actions a {
  margin-left: 7px;
}
.my-materials .resource-set .resource-actions i {
  font-size: 18px;
  color: #c7c7c7;
}
.my-materials .resource-set .resource-actions i:hover {
  cursor: pointer;
}
.my-materials .resource-set .resource-actions i.active {
  color: #27AE60;
}
.my-materials .resource-set .resource-actions .active i {
  color: #27AE60;
}
.my-materials .resource-set .resource-actions a.favorite-resource:hover i {
  color: #27AE60;
}
.my-materials .resource-set .heart {
  animation: heartbeat 0.5s;
  -webkit-animation: heartbeat 0.5s;
  -moz-animation: heartbeat 0.5s;
}
.my-materials .resource-set .chapter_txt {
  min-height: 35px;
  display: block;
}
.my-materials .resource-set .set_public_btn .public-text {
  font-size: 10px;
}
.my-materials .resource-set .set_public_btn .toggleSwitch .switch {
  width: 25px;
  height: 13px;
}
.my-materials .resource-set .set_public_btn .toggleSwitch .slider:before {
  height: 11px;
  width: 10px;
}
.my-materials .resource-set .set_public_btn .toggleSwitch input:checked + .slider:before {
  -webkit-transform: translateX(12px);
  -ms-transform: translateX(12px);
  transform: translateX(12px);
}
.my-materials .resource-set .delete-resource:hover i {
  color: #FF4B33;
}
.my-materials .resource-set .edit-resource:hover i {
  color: #2F80ED;
}
.my-materials .resource-empty-msg {
  font-size: 12px;
  font-weight: 500;
  color: #949494;
}
#overlay-bg {
  background-color: rgba(0, 0, 0, 0.8);
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 9991;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
@media (max-width: 575.98px) and (orientation: portrait), (min-width: 576px) and (max-width: 767.98px) and (orientation: portrait) {
  #videoModal .modal-body-modifier iframe {
    height: 400px;
  }
}
@media (max-width: 575.98px) and (orientation: landscape), (min-width: 576px) and (max-width: 767.98px) and (orientation: landscape) {
  #videoModal .modal-body-modifier iframe {
    height: auto;
  }
}
.web-url .modal .modal-header,
.video-url .modal .modal-header {
  display: none;
}
.web-url .modal .modal-content,
.video-url .modal .modal-content {
  width: 100%;
}
.web-url .modal .modal-body form,
.video-url .modal .modal-body form {
  margin-top: 0;
}
.web-url .modal .modal-body input,
.video-url .modal .modal-body input {
  width: 100%;
  border-bottom: 1px solid rgba(68, 68, 68, 0.48);
  padding: 10px;
  margin-bottom: 1rem;
}
.web-url .modal .modal-footer,
.video-url .modal .modal-footer {
  border-top: none;
}
.web-url .modal .modal-footer button,
.video-url .modal .modal-footer button {
  background: transparent;
}
.web-url .modal .modal-footer button.cancel,
.video-url .modal .modal-footer button.cancel {
  color: rgba(68, 68, 68, 0.48);
  text-transform: capitalize;
}
.web-url .modal .modal-footer button.saveLink,
.video-url .modal .modal-footer button.saveLink {
  color: #0F0839;
  font-size: 14px;
  font-weight: 700;
}
#addVideo .mdl-textfield,
#webForm .mdl-textfield {
  width: 100%;
}
#webForm .mdl-textfield {
  padding: 14px 0;
}
.video-url #videoForm input {
  margin: 0.5rem auto;
  padding: 0.3rem;
}
#videoForm .is-focused {
  padding: 20px 0 !important;
}
.aboutus p {
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus p {
    font-size: 15px;
  }
}
.aboutus .sectionOne {
  margin-top: 3rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionOne {
    margin-top: 2rem;
  }
}
.aboutus .sectionOne .video-detail {
  position: relative;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionOne .video-detail {
    position: inherit;
  }
}
.aboutus .sectionOne .video-section {
  display: flex;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionOne .video-section {
    display: grid;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionOne .video-section h2 {
    margin-top: 1rem;
  }
}
.aboutus .sectionOne .video-section h4 {
  color: #F79420;
}
.aboutus .sectionOne .video-section button {
  margin-top: 1rem;
  border: none;
  color: #FFFFFF;
  background-color: #F79420;
  border-radius: 16px;
  height: 30px;
  width: 150px;
  font-size: 16px;
}
.aboutus .sectionOne .video-section hr {
  width: 50px;
  height: 5px;
  background-color: #F79420;
}
.aboutus .sectionOne .left-div {
  display: inline-block;
  position: relative;
}
.aboutus .sectionOne .left-div img {
  max-width: 100%;
  border-radius: 16px;
}
.aboutus section {
  margin-top: 3rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus section {
    margin-top: 0;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionTwo {
    margin-top: 0px;
  }
}
.aboutus .sectionTwo .card {
  background-color: transparent;
  border: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionTwo .card {
    margin-bottom: 2rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionTwo .card .card-body {
    align-self: center;
  }
}
.aboutus .sectionTwo .card img {
  height: 150px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionTwo .card p {
    text-align: center;
  }
}
.aboutus .sectionTwo .card hr {
  border: 2px solid #F79420;
  width: 30px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionTwo .card hr {
    margin: 10px auto;
  }
}
.aboutus .sectionTwo .card .card-title {
  color: #F79420;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionTwo .card .card-title {
    text-align: center;
  }
}
.aboutus .sectionTwo .card .card-text {
  height: 60px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionTwo .card .card-text {
    height: auto;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionTwo .card .card-text {
    height: 70px;
  }
}
.aboutus .sectionTwo .card a {
  background-color: #F79420;
  border-radius: 16px;
  border: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionTwo .card a {
    margin-top: 10;
    margin-left: 35%;
  }
}
.aboutus .sectionThree .row {
  margin-right: 0;
  margin-left: 0;
}
.aboutus .sectionThree .card {
  text-align: center;
  min-height: 150px;
  border-radius: 14px;
}
@media (min-width: 576px) and (max-width: 767.98px), (max-width: 575.98px) {
  .aboutus .sectionThree .card {
    margin-bottom: 2rem;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionThree .card {
    min-height: 350px;
  }
}
.aboutus .sectionThree .card .card-body {
  display: grid;
}
.aboutus .sectionThree .card .card-header {
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  border-top-left-radius: 14px;
  border-top-right-radius: 14px;
}
.aboutus .sectionThree .card .card-text {
  margin-bottom: 1rem;
  text-align: center;
}
.aboutus .sectionThree .card a {
  font-size: 16px;
  color: #212121;
  font-weight: 700;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionThree .card a {
    font-size: 15px;
  }
}
.aboutus .sectionThree .card a:hover {
  color: #F79420;
}
.aboutus .sectionFour {
  background-color: #F79420;
  border-radius: 16px;
  margin-left: 30px;
  margin-right: 30px;
}
.aboutus .sectionFour .input-group {
  left: 25%;
  width: 50%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFour .input-group {
    left: 0%;
    width: 90%;
    display: block;
    text-align: center;
    margin: 0 auto;
  }
}
.aboutus .sectionFour .input-group input {
  background-color: transparent;
  border-bottom: 1px solid #6C757D;
  border-top: none;
  border-left: none;
  border-right: none;
  border-radius: 0;
}
@media (min-width: 576px) and (max-width: 767.98px), (max-width: 575.98px) {
  .aboutus .sectionFour .input-group input {
    width: 100%;
  }
}
.aboutus .sectionFour .input-group .input-group-append button {
  background-color: #FFFFFF;
  color: #212121;
  border: none;
  width: 300px;
  border-radius: 0;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionFour .input-group .input-group-append button {
    width: 200px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFour .input-group .input-group-append button {
    width: 100px;
    margin: 10 auto;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFour {
    margin-top: 2rem !important;
  }
}
.aboutus .sectionFour .subscribe-section {
  padding-top: 30px;
  padding-bottom: 30px;
}
.aboutus .sectionFour .subscribe-section img {
  height: 40px;
  position: relative;
  left: 50%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFour .subscribe-section img {
    left: 45%;
  }
}
.aboutus .sectionFour .subscribe-section h5 {
  padding: 1rem;
  text-align: center;
  color: #FFFFFF;
}
.aboutus .sectionFour .subscribe-section .input-subscribe .form-control {
  color: black;
  background-color: transparent;
  border-bottom: 1px solid white;
  border-radius: 0;
  border-left: none;
  border-right: none;
  border-top: none;
}
.aboutus .sectionFour .subscribe-section .input-subscribe .email-input {
  width: 80%;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFour .subscribe-section .input-subscribe .email-input {
    text-align: center;
  }
}
.aboutus .sectionFour .subscribe-section .input-subscribe .subscribe {
  width: 50%;
  margin: 0 auto;
}
.aboutus .sectionFour .subscribe-section .subscribe-text {
  text-align: center;
  color: #FFFFFF;
  padding: 1rem;
}
.aboutus .sectionFive .left-img img {
  width: 350px;
  height: 100%;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionFive .left-img img {
    width: 300px;
    height: 100%;
  }
}
.aboutus .sectionFive .testimonial-container {
  border-radius: 14px;
  margin-left: 20px;
  border: 1px solid orange;
  min-height: 350px;
  width: 100%;
  align-self: center;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionFive .testimonial-container {
    min-height: 400px;
    margin-bottom: 2rem;
    margin-left: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFive .testimonial-container {
    min-height: 100%;
    margin-bottom: 2rem;
    margin-left: 0;
  }
}
.aboutus .sectionFive .testimonial-container .rating {
  margin-bottom: 1rem;
}
.aboutus .sectionFive .testimonial-container .rating img {
  width: 25px;
}
.aboutus .sectionFive .testimonial-container #carouselExampleCaptions {
  padding: 20px 20px 30px;
  min-height: 350px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionFive .testimonial-container #carouselExampleCaptions {
    min-height: 400px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFive .testimonial-container #carouselExampleCaptions {
    min-height: 100%;
  }
}
.aboutus .sectionFive .testimonial-container .carousel-indicators {
  margin-bottom: 0;
}
.aboutus .sectionFive .testimonial-container .carousel-indicators [data-target] {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #6C757D;
  border: none;
  margin: 2px;
}
.aboutus .sectionFive .testimonial-container .carousel-indicators [data-target].active {
  background-color: #F79420;
}
.aboutus .sectionFive .testimonial-container .carousel-inner .carousel-item {
  text-align: center;
}
.aboutus .sectionFive .testimonial-container .carousel-inner .carousel-item h3 {
  color: #F79420;
  font-size: 24px;
  margin-bottom: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFive .testimonial-container .carousel-inner .carousel-item h3 {
    font-size: 20px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionFive .testimonial-container .carousel-inner .carousel-item h4 {
    font-size: 18px;
  }
}
.aboutus .sectionFive .testimonial-container .carousel-inner .carousel-item p {
  color: #212121;
  margin-bottom: 20px;
  font-size: 15px;
}
.aboutus .sectionSix .card {
  border: 1px solid #F79420 !important;
  background-color: transparent;
  box-shadow: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .aboutus .sectionSix .card {
    margin-bottom: 1rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionSix .card {
    min-height: 210px;
  }
}
.aboutus .sectionSix .card .card-content {
  display: flex;
  justify-content: center;
  padding: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionSix .card .card-content {
    display: grid !important;
  }
}
.aboutus .sectionSix #total-books .circle {
  background: #F9B87C;
}
.aboutus .sectionSix #total-authors .circle {
  background-color: #A5D6DE;
}
.aboutus .sectionSix #total-books-sold .circle {
  background-color: #B2DCAB;
}
.aboutus .sectionSix #total-happy .circle {
  background-color: #FACED6;
}
.aboutus .sectionSix .totals {
  align-self: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionSix .totals {
    margin-top: 1rem;
    text-align: center;
  }
}
.aboutus .sectionSix .totals p {
  color: #6C757D;
}
.aboutus .sectionSix .circle {
  border-radius: 50%;
  min-width: 80px;
  min-height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .aboutus .sectionSix .circle {
    margin: 0 auto;
  }
}
a {
  text-decoration: none !important;
}
.faqs h4 {
  text-align: center;
  color: #F79420;
}
.faqs .card {
  margin-bottom: 1rem;
  border: none;
  background: transparent;
}
.faqs .card .btn-link {
  text-decoration: none;
  border: none;
  border-radius: 7px 7px 0 0;
  background: #FFFFFF;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  padding: 0;
}
.faqs .card .btn-link.collapsed {
  border-radius: 7px;
}
.faqs .card .btn-link.collapsed .card-header h5 {
  color: #212121;
}
.faqs .card .btn-link.collapsed .card-header i {
  transform: rotate(0);
  color: #212121;
}
.faqs .card .btn-link .card-header {
  background-color: transparent;
  border: none;
  display: flex;
  justify-content: space-between;
  white-space: normal;
  text-align: left;
}
.faqs .card .btn-link .card-header h5 {
  color: #F79420;
  line-height: normal;
  font-size: 15px;
}
.faqs .card .btn-link .card-header i {
  transform: rotate(-180deg);
  color: #F79420;
}
.faqs .card .card-body {
  font-size: 15px;
  background: #fff6ec;
  margin-top: 1px;
  border-radius: 0 0 7px 7px;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  min-height: 100px;
}
.nextexam_section .store1_topbanner .publisher_logo {
  border-radius: 10px;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center bottom;
  height: 250px;
}
@media (max-width: 575.98px) {
  .nextexam_section .store1_topbanner .publisher_logo {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .nextexam_section .store1_topbanner .publisher_logo div {
    width: 100% !important;
  }
}
.nextexam_section .store1_topbanner .publisher_logo h1,
.nextexam_section .store1_topbanner .publisher_logo h2 {
  font-family: "Times New Roman" !important;
}
.nextexam_section .store1_topbanner .bg-banner {
  border-radius: 10px;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #0F0839;
  position: relative;
  overflow: hidden;
}
.nextexam_section .store1_topbanner .bg-banner:before {
  content: '';
  position: absolute;
  top: -30px;
  right: -30px;
  background-image: url("../../images/ws/library-btn-bg.svg");
  background-repeat: no-repeat;
  width: 150px;
  height: 120px;
  opacity: 0.5;
}
.nextexam_section .store1_topbanner .bg-banner:after {
  content: '';
  position: absolute;
  bottom: -40px;
  left: 0;
  background-image: url("../../images/ws/library-btn-bg.svg");
  background-repeat: no-repeat;
  width: 150px;
  height: 120px;
  opacity: 0.5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .nextexam_section .store1_topbanner .bg-banner {
    min-height: 150px;
  }
}
.nextexam_section .store1_topbanner .bg-banner h1 {
  color: #E76619;
  font-size: 40px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .nextexam_section .store1_topbanner .bg-banner h1 {
    font-size: 30px;
  }
}
.nextexam_section .store1_topbanner .bg-banner h2 {
  color: #00FE00;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .nextexam_section .store1_topbanner .bg-banner h2 {
    font-size: 24px;
  }
}
.nextexam_section .store1_index_accordion .card {
  border-radius: 0 0 10px 10px;
  box-shadow: none !important;
  background: none;
}
.nextexam_section .store1_index_accordion .card-body .name_of_chapter {
  text-align: center;
}
.nextexam_section .store1_index_accordion .card-body .name_of_chapter a {
  width: 100%;
  height: 100px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 4px 15px 15px 15px;
  border: 1px solid;
  padding: 10px;
  font-size: 14px;
  color: #0F0839;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.nextexam_section .store1_index_accordion .card-body .name_of_chapter a:hover {
  box-shadow: 0 5px 10px #0000001A;
  color: #F79420;
  text-decoration: none;
}
.nextexam_section .monthly-test-btn {
  font-size: 14px;
}
.nextexam_section h5 {
  color: #212121;
}
.nextexam_section p {
  font-size: 15px;
  margin-bottom: 0.75rem;
}
@media (max-width: 575.98px) {
  .nextexam_section p {
    font-size: 14px;
  }
}
.nextexam_section .show-all,
.nextexam_section .show-less {
  float: right;
  margin-top: -15px;
  cursor: pointer;
  color: #0F0839;
}
.nextexam_section .show-all:hover,
.nextexam_section .show-less:hover {
  text-decoration: underline;
}
.nextexam_section #nextExamPrice a:hover {
  text-decoration: underline;
}
.nextexam_section .name_of_chapter a {
  cursor: pointer;
}
.nextexam_section .btn-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.nextexam_section .btn-wrapper a {
  cursor: pointer;
  Text-transform: inherit;
  background: #FFFFFF;
  box-shadow: 0 2px 7px #0000001A;
  border-radius: 5px;
  color: #7a7a7a;
  font-size: 14px;
  line-height: normal;
  padding: 10px 20px;
  border: 1px solid transparent;
}
.nextexam_section .btn-wrapper a:hover,
.nextexam_section .btn-wrapper a:focus {
  border-color: #0F0839;
}
.doubts-section .nav-tabs {
  height: 35px;
  position: relative;
  background: transparent;
  border: none;
}
@media (max-width: 575.98px) {
  .doubts-section .nav-tabs {
    display: flex;
    justify-content: space-around;
  }
}
.doubts-section .nav-tabs .nav-link {
  border: none;
  border-radius: 0;
  font-size: 14px;
  color: #949494;
}
.doubts-section .nav-tabs .nav-item.show .nav-link,
.doubts-section .nav-tabs .nav-link.active {
  color: #0F0839;
}
@media (max-width: 575.98px) {
  .doubts-section .nav-tabs .nav-item.show .nav-link,
  .doubts-section .nav-tabs .nav-link.active {
    background: #ededed;
    border-radius: 5px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section > .container {
    padding: 0;
  }
}
@media (max-width: 575.98px) {
  .doubts-section .ask-doubt-btn {
    font-size: 12px;
  }
}
.doubts-section #doubt-search {
  display: none;
  align-content: center;
}
.doubts-section .search-ws {
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-width: 2px;
}
.doubts-section #search-book,
.doubts-section .search-btn {
  height: 34px;
}
.doubts-section .askdoubt {
  position: absolute;
  right: 30px;
  top: -30px;
  border-radius: 10px;
  font-size: 16px;
}
.doubts-section .askdoubt i {
  margin-right: 10px;
  font-size: 18px;
}
.doubts-section .askdoubt:focus {
  outline: 0;
}
.doubts-section .doubt-menu {
  position: sticky;
  top: 69px;
  z-index: 99;
  background: #FFFFFF;
  border-radius: 5px;
  transition: all 0.35s linear;
}
@media (max-width: 575.98px) {
  .doubts-section .doubt-menu {
    top: 60px;
    width: 100%;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .doubts-section .doubt-menu {
    top: 60px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .doubt-menu {
    top: 54px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .doubt-menu {
    box-shadow: 0 4px 10px #0000001A;
    border-radius: 0 0 10px 10px;
  }
}
.doubts-section .doubt-menu.reset-app {
  margin-right: 0 !important;
  margin-left: 0 !important;
  position: fixed;
  top: 0;
  width: 100%;
}
.doubts-section .doubt-menu.reset-app .app-back-btn {
  display: none !important;
}
.doubts-section .doubt-menu .nav-tabs li a {
  padding: 0.2rem 1rem;
  border-right: 1px solid #ededed;
}
@media (max-width: 575.98px) {
  .doubts-section .doubt-menu .nav-tabs li a {
    padding: 0.2rem 0.6rem;
    border-right: none !important;
  }
}
.doubts-section .doubt-menu .nav-tabs li#myAnswer-tab a {
  border-right: 0;
}
.doubts-section .doubt-menu .nav-tabs li:last-child a {
  border-right: 0;
}
.doubts-section .doubt-menu .nav-tabs li:last-child {
  margin-left: 6px;
  padding-top: 4px;
}
.doubts-section .doubt-menu .nav-tabs li:last-child i {
  line-height: inherit;
}
.doubts-section .doubt-menu .nav-tabs #mobile-filter i {
  color: #949494;
  font-size: 20px;
}
.doubts-section .filter-wrapper .row {
  justify-content: center;
}
.doubts-section .filter-wrapper .myBox {
  position: relative;
  display: inline-block;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .filter-wrapper .myBox {
    padding-right: 0;
  }
}
.doubts-section .filter-wrapper .mySelect {
  position: relative;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
}
.doubts-section .bootstrap-select > .dropdown-toggle.bs-placeholder {
  background: none;
  border: none;
  color: #444444;
}
.doubts-section .bootstrap-select > .dropdown-toggle.bs-placeholder:hover,
.doubts-section .bootstrap-select > .dropdown-toggle.bs-placeholder:focus,
.doubts-section .bootstrap-select > .dropdown-toggle.bs-placeholder:active {
  color: #444444;
}
.doubts-section .bootstrap-select .dropdown-toggle:focus {
  outline: 0 !important;
}
.doubts-section .bootstrap-select .dropdown-toggle .filter-option {
  top: 3px;
  font-size: 13px;
  padding-right: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #444444;
}
.doubts-section .bootstrap-select .dropup .dropdown-toggle::after {
  position: relative;
  top: 3px;
}
.doubts-section .bootstrap-select .dropdown-toggle::after {
  position: relative;
  top: 3px;
}
.doubts-section .bootstrap-select .open > .dropdown-menu {
  display: block;
}
.doubts-section .bootstrap-select .open > .dropdown-menu li a {
  display: block;
  padding: 3px 20px;
  clear: both;
  line-height: 1.5;
  color: #444444;
  white-space: pre-wrap;
  font-size: 13px;
}
.doubts-section .bootstrap-select .open > .dropdown-menu li a:hover {
  background-color: #e1e1e1;
}
.doubts-section .bootstrap-select .open > .dropdown-menu li a span.text {
  line-height: 1.2;
}
.doubts-section .bootstrap-select .open > .dropdown-menu li.no-results {
  background: none;
  font-size: 13px;
  color: #FF4B33;
}
.doubts-section .bootstrap-select .bs-searchbox .form-control:focus {
  outline: 0;
  box-shadow: none;
  border-color: #0F0839;
}
@media (max-width: 575.98px) {
  .doubts-section .bootstrap-select .dropdown-menu {
    max-width: 300px !important;
    min-width: 300px !important;
  }
}
.doubts-section .bootstrap-select button.btn-default {
  background-image: linear-gradient(#fff4d3, #fff4d3);
  border: none;
}
.doubts-section .selected.form-control {
  background-image: linear-gradient(#fff4d3, #fff4d3);
  border: none;
}
.doubts-section select.form-control {
  border-radius: 5px;
  font-size: 13px;
  color: #444444;
}
.doubts-section #queSave-modal .bootstrap-select {
  margin-top: 1.5rem !important;
  width: 100%;
  -ms-flex: auto;
  flex: auto;
  max-width: 100%;
}
.doubts-section #alldoubts,
.doubts-section #mydoubts,
.doubts-section #myanswers {
  padding: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .mobile-drop {
    margin-top: 50px;
  }
}
.doubts-section .mobile-drop.reset-app {
  margin-top: 140px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .mobile-drop.reset-app .discussion-card:first-child {
    margin-top: 160px !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .mobile-drop.reset-app .filter-wrapper {
    margin-top: 120px;
  }
}
.doubts-section .discussion-card {
  box-sizing: border-box;
  padding: 1rem;
  padding-top: 0.5rem;
  background: #FFFFFF;
  border: none;
  width: 100%;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .discussion-card {
    padding: 10px;
    box-shadow: 0 0 10px #0000001A;
  }
}
.doubts-section .discussion-card img {
  width: auto;
  max-width: 290px;
  max-height: 200px;
  border-radius: 5px;
}
@media (max-width: 575.98px) {
  .doubts-section .discussion-card img {
    max-height: inherit;
    height: auto;
  }
}
.doubts-section .discussion-card ol {
  background: transparent;
  margin: 0;
  border-radius: 0;
  padding: 5px 0;
  margin-bottom: 10px;
}
.doubts-section .discussion-card ol span {
  font-style: normal;
  font-weight: 400;
  font-size: 10px;
  color: #bababa;
}
.doubts-section .discussion-card ol li.breadcrumb-item {
  padding-top: 15px;
  line-height: 1.2;
}
.doubts-section .discussion-card ol li a {
  color: #0F0839;
  font-size: 12px;
}
.doubts-section .discussion-card ol .breadcrumb-item + .breadcrumb-item::before {
  content: '>';
  font-size: 10px;
  padding: 3px;
  top: -1px;
  position: relative;
  color: #0F0839;
}
.doubts-section .confirm,
.doubts-section .addtag {
  background: none;
  color: #0F0839;
  font-size: 10px;
  position: absolute;
  right: 5px;
  border: none;
  display: flex;
  align-items: center;
}
.doubts-section .confirm i,
.doubts-section .addtag i {
  font-size: 14px;
  color: #0F0839;
}
.doubts-section .breadcrumb {
  display: flex;
  align-items: center;
  min-height: 36px;
}
.doubts-section .breadcrumb > span {
  display: block;
  margin-right: 5px;
}
.doubts-section .breadcrumb select {
  background: #0F0839;
  color: #FFFFFF;
  border-radius: 4px;
  border: none;
  margin-right: 5px;
  font-size: 12px;
  height: 26px;
}
.doubts-section .profile {
  display: flex;
  align-items: center;
}
.doubts-section .profile img {
  width: 28px;
  height: 28px;
}
.doubts-section .profile h4 {
  font-size: 12px;
  margin-left: 10px;
  font-weight: 400;
}
.doubts-section .profile p {
  font-size: 10px;
  color: #bababa;
  margin-left: 5px;
}
.doubts-section .addtodoubts {
  position: absolute;
  display: flex;
  align-items: center;
  right: 10px;
  top: 5px;
}
.doubts-section .addtodoubts button {
  color: #bababa;
}
.doubts-section .addtodoubts .drop-menu {
  background: none;
  border: none;
  color: #0F0839;
}
.doubts-section .addtodoubts .drop-menu:focus {
  outline: none;
}
.doubts-section .addtodoubts .dropleft .dropdown-toggle::before {
  display: none;
}
.doubts-section .addtodoubts .dropdown-menu {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  z-index: 9;
  border: none;
}
.doubts-section .addtodoubts .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #0F0839;
}
.doubts-section .addtodoubts .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.doubts-section .addtodoubts .dropdown-menu > li:hover {
  background: #0000001A;
}
.doubts-section .addtodoubts .dropdown-menu > li a {
  cursor: pointer;
  color: #FF4B33;
}
.doubts-section .addtodoubts .dropdown-menu > li a i {
  color: #FF4B33;
  -webkit-text-fill-color: #FF4B33;
}
.doubts-section .addtodoubts .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.doubts-section .addtodoubts .dropdown-menu > li > a:hover {
  background: none;
}
.doubts-section .addtodoubts .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.doubts-section .addtodoubts .dropdown,
.doubts-section .addtodoubts .dropleft,
.doubts-section .addtodoubts .dropright,
.doubts-section .addtodoubts .dropup {
  top: 3px;
}
.doubts-section .sub-name {
  font-size: 12px;
  color: #0F0839;
}
.doubts-section .addDoubts {
  font-size: 12px;
  background: none;
  border: none;
  margin-right: 5px;
  font-weight: 300;
}
.doubts-section .addDoubts:focus {
  outline: none;
}
.doubts-section .addDoubts:hover {
  color: #0F0839;
}
.doubts-section .answeredby {
  font-size: 12px;
  color: #0F0839;
  text-transform: capitalize;
}
.doubts-section .answeredby span {
  padding-right: 5px;
  color: #bababa;
  font-style: italic;
  font-weight: 300;
  text-transform: none;
}
.doubts-section .q-question {
  color: #0F0839;
}
.doubts-section .content > p {
  margin-top: 0.5rem;
  color: #444444;
}
.doubts-section .content > p:first-child {
  color: #0F0839;
}
.doubts-section .content > p:nth-child(2) {
  color: #0F0839;
}
.doubts-section .content > p:nth-child(3) {
  color: #0F0839;
}
.doubts-section .card-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.doubts-section .card-actions .dropleft .dropdown-toggle::before {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .card-actions {
    padding: 0.4rem 0;
  }
}
.doubts-section .card-actions button {
  background: transparent;
  outline: 0;
  border: none;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #bababa;
  white-space: nowrap;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .card-actions button {
    font-size: 10px;
  }
}
.doubts-section .card-actions button i {
  font-size: 14px;
  color: #bababa;
}
.doubts-section .card-actions button i.circle {
  border-radius: 50px;
  width: 24px;
  height: 24px;
  border: 1.25px solid #bababa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  margin-right: 5px;
}
.doubts-section .card-actions button i.circle.bord {
  border-color: #2F80ED;
  color: #2F80ED;
}
.doubts-section .card-actions button.drop-menu {
  background: rgba(68, 68, 68, 0.2);
  justify-content: center;
  border-radius: 4px;
}
.doubts-section .card-actions button.drop-menu i {
  margin-right: 0;
}
.doubts-section .card-actions .dropdown-toggle::after {
  border: none;
}
.doubts-section .card-actions .dropdown-menu {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  border: none;
}
.doubts-section .card-actions .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #0F0839;
}
.doubts-section .card-actions .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.doubts-section .card-actions .dropdown-menu > li:hover {
  background: #0000001A;
}
.doubts-section .card-actions .dropdown-menu > li a {
  cursor: pointer;
  color: #FF4B33;
}
.doubts-section .card-actions .dropdown-menu > li a i {
  color: #FF4B33;
  -webkit-text-fill-color: #FF4B33;
}
.doubts-section .card-actions .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.doubts-section .card-actions .dropdown-menu > li > a:hover {
  background: none;
}
.doubts-section .card-actions .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.doubts-section .card-actions button.answer {
  background: #FFFFFF;
  border: 1.5px solid #0F0839;
  box-sizing: border-box;
  border-radius: 5px;
  color: #0F0839;
  font-weight: 400;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.doubts-section .card-actions button.answer i {
  color: #0F0839;
  margin-right: 10px;
}
.doubts-section .flex-action {
  display: flex;
  align-items: center;
  min-height: 24px;
}
.doubts-section .flex-action #share-button {
  border-left: 0.5px solid #bababa;
}
.doubts-section .divider {
  background: url('../../images/discussionboard/line-horiz.svg') center center no-repeat;
  height: 2px;
  margin: 0 auto;
}
.doubts-section .answer-card {
  min-height: 290px;
  width: 100%;
  padding: 1rem;
  border: none;
  box-shadow: 0 0 10px #0000001A;
  position: relative;
  background: #FFFFFF;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .doubts-section .answer-card {
    box-shadow: 0 0 10px #00000040;
  }
}
.doubts-section .answer-card .answer-textarea {
  min-height: 210px;
  border: 1px solid #bababa;
}
.doubts-section .answer-card .answer-textarea:focus {
  outline: 0;
}
.doubts-section .answer-card .answer-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
.doubts-section .answer-card .answer-actions button {
  margin-right: 15px;
  background: none;
  border: none;
}
.doubts-section .answer-card .answer-actions button i {
  color: #949494;
}
.doubts-section .answer-card .answer-actions button.post {
  width: 275px;
  text-transform: uppercase;
  color: #0F0839;
  min-height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1.25px solid #0F0839;
  font-weight: 500;
}
.doubts-section .answer-card .answer-actions button.cancels {
  width: 275px;
  background: #FFFFFF;
  text-transform: uppercase;
  min-height: 45px;
  border: 1px solid #949494;
  color: #949494;
  display: flex;
  align-items: center;
  justify-content: center;
}
.doubts-section .answer-cancel {
  border: 1.5px solid #FF4B33;
  color: #FF4B33;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-sizing: border-box;
  border-radius: 5px;
  height: 43px;
  margin-top: 1.5rem;
  font-weight: 400;
}
.doubts-section .answer-cancel i {
  color: #FF4B33;
  font-size: 18px;
  margin-right: 5px;
}
.doubts-section #image-preview img {
  max-height: 300px;
  max-width: 100%;
}
.doubts-section .close-img,
.doubts-section .close-preview {
  background: #FFFFFF;
  position: absolute;
  top: -7px;
  right: 0;
  box-shadow: 0 0 10px #00000040;
  border: none;
  border-radius: 50px;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.doubts-section .close-img i,
.doubts-section .close-preview i {
  color: #FF4B33;
  font-size: 18px;
}
.doubts-section .close-preview {
  margin-top: 0;
}
.doubts-section .close-preview i {
  margin-right: 0;
}
.doubts-section .answer-drop .drop-menu {
  background: none;
  border: none;
  color: #0F0839;
}
.doubts-section .answer-drop .drop-menu:focus {
  outline: none;
}
.doubts-section .answer-drop .dropleft .dropdown-toggle::before {
  display: none;
}
.doubts-section .answer-drop .dropdown-menu {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  border: none;
  z-index: 9;
}
.doubts-section .answer-drop .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #0F0839;
}
.doubts-section .answer-drop .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.doubts-section .answer-drop .dropdown-menu > li:hover {
  background: #0000001A;
}
.doubts-section .answer-drop .dropdown-menu > li a {
  cursor: pointer;
  color: #FF4B33;
  font-size: 12px;
}
.doubts-section .answer-drop .dropdown-menu > li a i {
  color: #FF4B33;
  -webkit-text-fill-color: #FF4B33;
}
.doubts-section .answer-drop .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.doubts-section .answer-drop .dropdown-menu > li > a:hover {
  background: none;
}
.doubts-section .answer-drop .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.doubts-section .answer-drop .dropdown,
.doubts-section .answer-drop .dropleft,
.doubts-section .answer-drop .dropright,
.doubts-section .answer-drop .dropup {
  top: 3px;
}
.doubts-section .answer-head {
  font-size: 14px;
  text-transform: uppercase;
  color: #444444;
  margin-bottom: 1rem;
}
.doubts-section .userAnswer {
  background: rgba(148, 148, 148, 0.1);
  margin-left: 10px;
  margin-top: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
}
.doubts-section .userAnswer img {
  max-width: 350px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section .userAnswer img {
    max-width: 290px;
  }
}
.doubts-section .userAnswer input {
  background: transparent;
}
.doubts-section .question {
  font-size: 14px;
  color: #bababa;
  font-weight: 600;
}
.doubts-section .answers {
  font-size: 12px;
  color: #bababa;
}
.doubts-section .faq {
  color: #0F0839;
  font-weight: 400;
  font-size: 12px;
}
.doubts-section .faq a {
  text-decoration: underline;
  color: #0F0839;
  font-weight: 700;
}
.doubts-section .que-saved {
  font-size: 16px;
  font-weight: 700;
  color: #FFFFFF;
}
.doubts-section .tag-text {
  font-size: 12px;
  font-weight: 400;
  margin-top: 10px;
  color: #0F0839;
}
.doubts-section #add-tags-btn {
  color: #0F0839 !important;
  border: 1px solid #0F0839 !important;
}
.doubts-section #add-tags-btn {
  text-transform: uppercase;
  color: #0F0839;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .doubts-section #pagination-div {
    margin-bottom: 5rem;
  }
}
.doubts-section .row:before,
.doubts-section .row:after {
  display: flex !important;
}
.doubts-section .cke_1 {
  border: none;
}
.doubts-section .btn-askQuestion {
  background: #0F0839 !important;
  color: #FFFFFF !important;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
  border: none !important;
  font-weight: 400;
  padding: 10px 3rem !important;
  margin: 0 auto;
}
.doubts-section .btn-askQuestion span {
  font-weight: 700;
}
.doubts-section .admin-wrapper {
  min-height: 100vh;
}
.doubts-section .admin-wrapper .container-fluid {
  margin: 0 4rem;
}
.doubts-section .discussion-menu {
  margin-top: 1rem;
}
.doubts-section .discussion-menu.nav-pills .nav-item {
  margin-bottom: 1rem;
  text-align: center;
}
.doubts-section .discussion-menu.nav-pills .nav-item .nav-link {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: #FFFFFF !important;
  color: #0F0839;
}
.doubts-section .discussion-menu.nav-pills .nav-item .nav-link.active {
  background: #0F0839 !important;
  color: #FFFFFF;
}
.doubts-section .discussion-menu.nav-pills .nav-item .nav-link:focus {
  color: #FFFFFF;
}
.doubts-section .line {
  position: absolute;
  left: 0;
  top: 1rem;
}
.doubts-section .line img {
  height: 140px;
}
.doubts-section .q-question {
  color: #0F0839;
}
.doubts-section .moderate-btns button {
  display: block;
  border: none;
  width: 75px;
  height: 75px;
  border-radius: 50px;
  background: #0F0839;
  box-shadow: 0 0 10px #00000040;
  font-size: 9px;
  color: #FFFFFF;
  outline: 0;
  margin: 10px auto;
}
.doubts-section .moderate-btns button.delete {
  border: 1px solid #0F0839;
  background: #FFFFFF;
  color: #FF4B33;
}
.doubts-section .moderate-btns button.delete i {
  color: #FF4B33;
}
.doubts-section .moderate-btns button:focus {
  outline: none;
}
.doubts-section .moderate-btns button i {
  display: block;
  margin-bottom: 5px;
}
.doubts-section .pagination {
  display: flex;
  align-items: center;
}
.doubts-section .pagination li {
  margin-right: 10px;
}
.doubts-section .pagination li.disabled {
  background: none;
}
.doubts-section .pagination li.disabled a.actions {
  color: #b4b4b4;
}
.doubts-section .pagination li a {
  font-size: 18px;
  text-align: center;
  background: none;
  color: #212121;
}
.doubts-section .pagination li a span {
  display: block;
}
.doubts-section .pagination li a.actions {
  font-size: 8px;
}
.doubts-section .pagination li a.actions span {
  font-size: 18px;
}
.doubts-section .pagination li a.page-link {
  border-radius: 0;
  border: none;
}
.doubts-section .pagination li a.page-link:hover {
  background: none;
}
.doubts-section .pagination li a.page-link:focus {
  background: #0F0839;
}
.doubts-section .pagination li.active a {
  background: #0F0839;
  width: 33px;
  height: 33px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.doubts-section .answer-box {
  background: rgba(15, 8, 57, 0.1);
  width: 90%;
  border: none;
  min-height: 40px;
  font-size: 14px;
}
.doubts-section .solved {
  position: absolute;
  right: 20px;
  color: #27AE60;
  background: #e1e1e1;
  border-radius: 50px;
  width: 65px;
  font-size: 14px;
  text-align: center;
  display: block;
}
.doubts-section .answerContent .card-actions {
  padding: 0 0.5rem;
}
.doubts-section .answerContent .card-actions button {
  font-size: 10px;
}
.doubts-section .textOverflow {
  max-width: 98%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.doubts-section .taggs {
  display: block;
  margin-right: 10px;
}
.doubts-section .ans-modal-img-remove {
  color: #FF4B33;
  border: #FF4B33;
  border: 1px solid #FF4B33;
}
.doubts-section .searchbar .typeahead.dropdown-menu {
  max-width: 95%;
  min-width: 95%;
}
.doubts-section .searchbar .typeahead.dropdown-menu li a {
  max-width: 98%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.doubts-section #cke_questionText {
  border-radius: 10px;
  border: none;
}
.doubts-section #myDoubtsTab > p,
.doubts-section #myAnswerTab > p,
.doubts-section #alldoubts > p {
  margin-top: 1rem;
  padding: 5px;
}
.doubts-section .reset-app #myDoubtsTab > p,
.doubts-section #myAnswerTab > p,
.doubts-section #alldoubts > p {
  padding: 5px;
}
#tags-modal .modal-header {
  border: none;
}
#tags-modal .modal-body h4 {
  font-size: 14px;
}
#tags-modal .modal-footer {
  border: none;
  justify-content: center;
}
#tags-modal .modal-footer .skip {
  color: #949494;
  text-transform: uppercase;
}
#image-modal #image-modal-body p {
  text-align: center;
}
#image-modal .modal-footer {
  padding: 10px;
}
#tutorial .modal-dialog {
  height: 100%;
  margin: 0;
}
#tutorial .modal-dialog .modal-content {
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  border: none;
  border-radius: 0;
}
#tutorial .modal-dialog .modal-content h2 {
  font-size: 36px;
  font-weight: 700;
}
#tutorial .modal-dialog .modal-content p {
  font-size: 12px;
}
#tutorial .modal-dialog .modal-content p i {
  font-size: 16px;
  position: relative;
  top: 4px;
}
#tutorial .modal-header {
  border: none;
  display: flex;
  align-items: center;
}
#tutorial .modal-header .close {
  text-shadow: none;
  color: #FFFFFF;
  opacity: 1;
}
#tutorial .modal-header .btn-next {
  border: 1px solid #FFFFFF;
  color: #FFFFFF;
  border-radius: 4px;
  background: none;
  font-size: 9px;
}
#tutorial .modal-footer {
  border: none;
}
.carousel-item h2,
.carousel-item p {
  color: #FFFFFF;
}
.carousel-item.tutor .circle {
  width: 26.25px;
  height: 26.25px;
  border-radius: 50px;
  border: 1px solid #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.carousel-item.tutor .circle i {
  color: #FFFFFF;
  font-size: 16px;
}
.carousel-item.tutor .btn-answer {
  background: #FFFFFF;
  border: 1.25px solid #0F0839;
  box-sizing: border-box;
  border-radius: 5px;
  color: #0F0839;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.carousel-item.tutor .btn-answer i {
  margin-right: 10px;
}
.carousel-item.tutor h2 {
  font-size: 36px;
  margin-bottom: 1rem;
}
.carousel-item.tutor p {
  font-size: 14px;
  color: #FFFFFF;
}
.carousel-item.tutor .wonderwonk {
  color: #FFFFFF;
  font-size: 12px;
}
.carousel-item.tutor #grade-rating {
  display: flex;
  margin-right: 5px;
}
.carousel-item.tutor #grade-rating i {
  background: -webkit-linear-gradient(#FFC107, #FFC107);
  background: -moz-linear-gradient(#FFC107, #FFC107);
  background: -webkit-gradient(#FFC107, #FFC107);
  background: -o-linear-gradient(#FFC107, #FFC107);
  background: -ms-linear-gradient(#FFC107, #FFC107);
  background: linear-gradient(#FFC107, #FFC107);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
.indicator-override {
  position: static;
  margin-bottom: 0;
}
.indicator-override li {
  width: 14px;
  height: 8px;
  border-radius: 50%;
  background: #949494;
}
.indicator-override li.active {
  background: #FFFFFF;
}
.postmodal {
  text-align: center;
  position: absolute;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 4px;
  margin: 0 auto;
  width: 319px;
  height: 190px;
  margin-top: 4rem;
}
.postmodal.fade:not(.show) {
  opacity: 1;
}
.postmodal .modal-content {
  border: none;
}
.postmodal h4 {
  font-size: 14px;
  color: #0F0839;
  margin-top: 1rem;
  font-weight: 400;
}
.postmodal p {
  font-size: 10px;
  color: #949494;
}
.postmodal .modal-dialog {
  transition: none !important;
  transform: none !important;
  margin: 0;
}
.modalBackdrop {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100%;
  height: 100%;
  background-color: #212121;
  opacity: 0.5;
  border-radius: 4px;
  display: none;
}
.category-modal {
  background: #0F0839 !important;
  box-shadow: 0 -4px 10px #0000001A;
  border-radius: 10px 10px 0 0;
  display: none;
  position: absolute;
  margin: 0 auto;
  margin-top: 4rem;
}
.category-modal.fade:not(.show) {
  opacity: 1;
}
.category-modal .modal-content {
  border: none;
  background: transparent;
  min-height: 200px;
  margin-top: 4rem;
}
.category-modal h4 {
  font-size: 14px;
  color: #FFFFFF;
  font-weight: 400;
}
.category-modal p {
  font-size: 10px;
  color: #FFFFFF;
  margin-top: 0.5rem;
}
.category-modal .modal-dialog {
  transition: none;
  transform: none;
  margin: 0;
}
.category-modal .modal-footer {
  border: none;
}
.category-modal .modal-footer .skip {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  width: 100%;
  height: 50px;
  color: #0F0839;
  border-radius: 5px;
}
.category-modal .modal-footer .skip:focus {
  outline: 0;
}
.category-modal .filter {
  border-color: #FFFFFF !important;
}
.category-modal .filter i {
  color: #FFFFFF;
}
#mobileque-modal .category-modal {
  display: block;
  position: fixed;
}
#mobileque-modal .category-modal .modal.fade .modal-dialog {
  transition: none !important;
}
#mobileque-modal .modalBackdrop {
  position: fixed;
}
#mobileque-modal .postmodal {
  position: fixed;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}
.btn-dismiss {
  margin-top: 1rem;
  background: #0F0839;
  color: #FFFFFF;
  font-size: 14px;
}
.circle_around {
  border: 3px solid #0F0839;
  border-radius: 50px;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.circle_around i {
  color: #0F0839;
  font-weight: 700;
}
.btn-showMore {
  background: #0F0839;
  color: #FFFFFF;
  color: #FFFFFF !important;
}
.btn-flashcard {
  background: #0F0839;
  color: #FFFFFF;
}
body.guest-mode .doubt-menu {
  top: 140px;
}
@media (max-width: 575.98px) {
  body.guest-mode .doubt-menu {
    top: 120px;
  }
}
@media (max-width: 575.98px) {
}
.my-tracker .ct-series-a .ct-bar {
  /* Colour of your bars */
  stroke: #17A2B8;
  stroke-width: 30;
}
.my-tracker #userTimeReport {
  position: relative;
}
.my-tracker #emptyData p {
  color: #949494;
}
@media (max-width: 575.98px) {
  .my-tracker .chart-titles h4 {
    font-size: 18px;
  }
}
.my-tracker #yourForest {
  background: #FFFFFF;
  border: 1px solid #0051A0;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
}
.my-tracker #yourForest h4 {
  color: #0051A0;
  font-size: 20px;
}
.my-tracker .forest-info li {
  padding: 0 15px;
  border-right: 1px solid #0051A0;
  color: #0051A0;
  line-height: normal;
  font-size: 14px;
}
@media (max-width: 575.98px) {
  .my-tracker .forest-info li {
    padding: 0 10px;
  }
}
.my-tracker .forest-info li:last-child {
  border: none;
}
.todo_section .add-todo-btn {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .todo_section .todo_filter {
    margin-bottom: 0 !important;
    margin-top: 20px;
  }
}
.todo_section .todo_filter h4 {
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section .todo_filter h4 {
    font-size: 14px;
  }
}
.todo_section .todo_filter a {
  font-size: 12px;
  border-color: #0F0839;
  color: #0F0839;
  background-color: #FFFFFF;
  margin-left: 0.5rem;
  border-radius: 50px;
  padding: 0.25rem 1rem;
}
.todo_section .todo_filter a:hover,
.todo_section .todo_filter a:focus,
.todo_section .todo_filter a:focus:hover,
.todo_section .todo_filter a.active {
  border-color: #0F0839;
  background-color: #0F0839;
  color: #FFFFFF;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section .todo_filter a {
    font-size: 10px;
    margin-left: 0.3rem;
    padding: 0.2rem 0.7rem;
  }
}
.todo_section .todo_lists .empty_todo_list p {
  color: #949494;
}
.todo_section .todo_lists .card {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
}
.todo_section .todo_lists .card .custom-checkbox {
  position: absolute;
  top: 0;
}
.todo_section .todo_lists .card .custom-checkbox .custom-control-label {
  cursor: pointer;
}
.todo_section .todo_lists .card .custom-checkbox .custom-control-label::before {
  top: 20px;
  background-color: transparent;
  border: 2px solid #bababa;
  width: 1.3em;
  height: 1.3rem;
  border-radius: 3px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section .todo_lists .card .custom-checkbox .custom-control-label::before {
    width: 1.2rem;
    height: 1.2rem;
  }
}
.todo_section .todo_lists .card .custom-checkbox .custom-control-label::after {
  top: 20px;
  width: 1.3rem;
  height: 1.3rem;
  border-radius: 3px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section .todo_lists .card .custom-checkbox .custom-control-label::after {
    width: 1.2rem;
    height: 1.2rem;
  }
}
.todo_section .todo_lists .card .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
  background: #0F0839;
  border-color: transparent;
}
.todo_section .todo_lists .card div.todo_info {
  padding-right: 4rem;
}
.todo_section .todo_lists .card div.todo_info p {
  margin-bottom: 0.75rem;
}
.todo_section .todo_lists .card div.todo_info em {
  color: #6e6e6e;
}
.todo_section .todo_lists .card div.todo_info .high,
.todo_section .todo_lists .card div.todo_info .High {
  color: #FF4B33;
  font-weight: 500;
}
.todo_section .todo_lists .card div.todo_info .medium,
.todo_section .todo_lists .card div.todo_info .Medium {
  color: #FFC107;
  font-weight: 500;
}
.todo_section .todo_lists .card div.todo_info .low,
.todo_section .todo_lists .card div.todo_info .Low {
  color: #27AE60;
  font-weight: 500;
}
.todo_section .todo_lists .card del.todo_info {
  padding-right: 4rem;
  color: #6e6e6e;
}
.todo_section .todo_lists .card del.todo_info p {
  margin-bottom: 0.75rem;
}
.todo_section .todo_lists .card del.todo_info em {
  color: #6e6e6e;
}
.todo_section .todo_lists .card del.todo_info .high,
.todo_section .todo_lists .card del.todo_info .High {
  color: #6e6e6e;
}
.todo_section .todo_lists .card del.todo_info .medium,
.todo_section .todo_lists .card del.todo_info .Medium {
  color: #6e6e6e;
}
.todo_section .todo_lists .card del.todo_info .low,
.todo_section .todo_lists .card del.todo_info .Low {
  color: #6e6e6e;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section .todo_lists .card .todo_info {
    padding-right: 2.8rem !important;
  }
  .todo_section .todo_lists .card .todo_info p {
    margin-bottom: 0.5rem !important;
    font-size: 13px;
  }
}
.todo_section .bootstrap-datetimepicker-widget .btn-primary {
  border: none !important;
  width: 40px;
  background: #0F0839;
}
.todo_section .bootstrap-datetimepicker-widget .btn-primary:hover {
  box-shadow: 0 0.2rem 1rem #0000001A !important;
}
.todo_section .bootstrap-datetimepicker-widget .table-condensed .fa {
  font-family: "FontAwesome" !important;
}
.todo_section .bootstrap-datetimepicker-widget .table-condensed a {
  color: #444444;
}
.todo_section .bootstrap-datetimepicker-widget .table-condensed a:hover {
  background-color: transparent;
}
.todo_section .bootstrap-datetimepicker-widget .table-condensed a:hover .fa {
  color: #0F0839;
}
.todo_section .bootstrap-datetimepicker-widget .table-condensed .timepicker-hour,
.todo_section .bootstrap-datetimepicker-widget .table-condensed .timepicker-minute {
  border: 1px solid #949494;
}
.todo_section .bootstrap-datetimepicker-widget .table-condensed .timepicker-hour:hover,
.todo_section .bootstrap-datetimepicker-widget .table-condensed .timepicker-minute:hover {
  color: #0F0839;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker-hours .hour:hover {
  color: #0F0839;
}
.todo_section .bootstrap-datetimepicker-widget .timepicker-minutes .minute:hover {
  color: #0F0839;
}
.todo_section #recordsData .edit-access {
  position: absolute;
  right: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section #recordsData .edit-access {
    right: 10px;
    top: 10px;
  }
}
.todo_section #recordsData .edit-access a {
  display: inline-block;
}
.todo_section #recordsData .edit-access a.delete-icon {
  margin-right: 7px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section #recordsData .edit-access a.delete-icon {
    margin-right: 5px;
  }
}
.todo_section #recordsData .edit-access a.delete-icon:hover i {
  color: #FF4B33;
}
.todo_section #recordsData .edit-access a.edit-icon:hover i {
  color: #2F80ED;
}
.todo_section #recordsData .edit-access a i {
  font-size: 18px;
  color: #bababa;
}
.todo_section .calendar_filter {
  width: 100%;
}
.todo_section .calendar_filter .datepicker {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  font-family: Righteous, sans-serif !important;
}
.todo_section .calendar_filter .datepicker .table-condensed {
  width: 100%;
  font-size: 14px;
}
.todo_section .calendar_filter .datepicker .datepicker-days .prev {
  color: #0F0839;
  font-weight: 600;
}
.todo_section .calendar_filter .datepicker .datepicker-days .next {
  color: #0F0839;
  font-weight: 600;
}
.todo_section .calendar_filter .datepicker .datepicker-days .datepicker-switch {
  height: 40px;
  cursor: default;
  color: #0F0839;
  font-weight: 600;
}
.todo_section .calendar_filter .datepicker .datepicker-days .datepicker-switch:hover {
  background: none;
}
.todo_section .calendar_filter .datepicker .datepicker-days .dow {
  font-size: 12px;
  font-weight: 400;
  color: #949494;
  padding: 10px;
}
.todo_section .calendar_filter .datepicker .datepicker-days .day {
  width: 40px;
  height: 40px;
  border-radius: 50px;
  font-weight: 600;
}
.todo_section .calendar_filter .datepicker .datepicker-days .day.active {
  background: #0F0839;
}
.todo_section .calendar_filter .datepicker .datepicker-days .day.active:hover {
  color: #FFFFFF;
}
.todo_section .calendar_filter .datepicker .datepicker-days .day:hover {
  color: #0F0839;
}
.todo_section .calendar_filter .datepicker .datepicker-days .old.day,
.todo_section .calendar_filter .datepicker .datepicker-days .new.day {
  color: #949494;
  font-weight: 400;
}
.todo_section .todo_calendar_filter {
  display: flex;
  flex: 1 0 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .todo_section .flex-md-row-reverse {
    margin-right: 0;
    margin-left: 0;
  }
}
.todo_section #mobile_calendar #selectedMonthYear {
  font-weight: 700;
  color: #0F0839;
}
.todo_section #mobile_calendar .material-icons {
  color: #0F0839;
}
.todo_section .add_todo .add_todo_form {
  z-index: 99;
  min-width: 400px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .todo_section .add_todo .add_todo_form {
    min-width: auto;
  }
}
.todo_section .add_todo .add_todo_form h4 {
  font-weight: 700;
}
.todo_section .add_todo .add_todo_form .input-group .input-group-text {
  background: none;
}
.todo_section .add_todo .add_todo_form .input-group .calendar_input {
  border-bottom: 1px solid #e1e1e1 !important;
  font-weight: 500;
  border-radius: 0;
}
.todo_section .add_todo .add_todo_form .input-group .calendar_input:focus {
  box-shadow: none;
  border-color: #0F0839 !important;
}
.todo_section .add_todo .add_todo_form .input-group .time_input {
  border-bottom: 1px solid #e1e1e1 !important;
  border-radius: 0;
}
.todo_section .add_todo .add_todo_form .input-group .time_input:focus {
  box-shadow: none;
  border-color: #0F0839 !important;
}
.todo_section .add_todo .add_todo_form .input-group .priority_input {
  border-bottom: 1px solid #e1e1e1 !important;
  border-radius: 0;
  font-size: 14px;
  font-style: italic;
}
.todo_section .add_todo .add_todo_form .input-group .priority_input:focus {
  box-shadow: none;
  border-color: #0F0839 !important;
}
.homepage {
  -webkit-font-smoothing: antialiased;
}
.homepage .main-content .main-heading {
  max-width: 800px;
  margin: auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .main-heading h1 {
    font-size: 2rem;
  }
}
.homepage .main-content .main-heading p {
  font-size: 20px;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .main-heading p {
    text-align: justify;
    font-size: 18px;
  }
}
.homepage .main-content .categories-list .category-info {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  border: 1px solid transparent;
  border-radius: 15px;
  background: #FFFFFF;
  padding: 15px;
  margin-bottom: 30px;
  transition: all 0.3s;
  box-shadow: 0 2px 7px #0000001A;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .categories-list .category-info {
    min-height: 250px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .categories-list .category-info {
    justify-content: center;
  }
}
.homepage .main-content .categories-list .category-info:hover {
  border-color: #0F0839;
}
.homepage .main-content .categories-list .category-info .category-text h3 {
  font-size: 20px;
  color: #444444;
  font-weight: 600;
  line-height: normal;
  max-width: 100%;
  width: 140px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .categories-list .category-info .category-text h3 {
    width: auto;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .categories-list .category-info .category-text h3 {
    text-align: center;
    font-size: 22px;
  }
}
.homepage .main-content .categories-list .category-info .category-image svg,
.homepage .main-content .categories-list .category-info .category-image img {
  max-width: 100%;
  width: 160px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .categories-list .category-info .category-image svg,
  .homepage .main-content .categories-list .category-info .category-image img {
    width: 230px;
    height: auto;
  }
}
.homepage .main-content .institution-login {
  min-height: 300px;
  border-radius: 30px;
  border: 1px solid #F79420;
  background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, #ffffff 0%, #feeedc 100%);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 7px #0000001A;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .institution-login {
    min-height: auto;
    border: none;
    background: none;
    box-shadow: none;
    backdrop-filter: none;
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
}
.homepage .main-content .institution-login::before {
  content: '';
  position: absolute;
  width: 1px;
  height: 200px;
  background-color: #F79420;
  left: 33%;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .institution-login::before {
    left: 30%;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .institution-login::before {
    display: none;
  }
}
.homepage .main-content .institution-login h2 {
  line-height: normal;
}
.homepage .main-content .institution-login h2 strong {
  color: #F79420;
}
.homepage .main-content .institution-login p {
  font-size: 18px;
  line-height: normal;
}
.homepage .main-content .institution-login a {
  width: 150px;
  padding: 8px 20px;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  background-color: #F79420;
  border-color: #F79420;
  border-radius: 50px;
  box-shadow: 0 2px 4px #0000001A;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .institution-login a {
    width: 130px;
    padding: 7px 20px;
  }
}
.homepage .main-content .institution-login img {
  width: 250px;
  max-width: 100%;
  margin-right: 50px;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: cubic-bezier(0.54, 0.085, 0.5, 0.92);
  animation-timing-function: cubic-bezier(0.54, 0.085, 0.5, 0.92);
  -webkit-animation-name: floating;
  animation-name: floating;
  -webkit-animation-duration: 5s;
  animation-duration: 5s;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .institution-login img {
    width: 200px;
    margin-right: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .institution-login img {
    width: 100%;
    margin-top: 30px;
    margin-right: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .products-list h1 {
    font-size: 2rem;
  }
}
.homepage .main-content .products-list .product-info.wonderslate h4 {
  margin-bottom: 20px;
  text-align: left;
  font-weight: 600;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .products-list .product-info.wonderslate h4 {
    width: 75%;
  }
}
.homepage .main-content .products-list .product-info.wonderslate img {
  width: 50px;
  margin-bottom: 0;
  margin-right: 15px;
}
.homepage .main-content .products-list .product-info.prepjoy img {
  width: 120px;
}
.homepage .main-content .products-list .product-info {
  text-align: center;
  min-height: 300px;
  background-color: #FFFFFF;
  border: 1px solid transparent;
  border-radius: 20px;
  margin-bottom: 30px;
  transition: all 0.3s;
  box-shadow: 0 2px 7px #0000001A;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.homepage .main-content .products-list .product-info:hover {
  border-color: #F79420;
}
.homepage .main-content .products-list .product-info img {
  width: 180px;
  margin-bottom: 20px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage .main-content .products-list .product-info img {
    width: 100%;
  }
}
.homepage .main-content .products-list .product-info h3 {
  margin-bottom: 15px;
}
.homepage .main-content .products-list .product-info p {
  font-size: 18px;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage .main-content .products-list .product-info p {
    font-size: 16px;
    min-height: auto;
  }
}
.homepage .main-content .products-list .product-info a {
  position: relative;
  width: 150px;
  height: 40px;
  border-radius: 50px;
  background-color: transparent !important;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s;
  box-shadow: 0 2px 7px #0000001A;
  left: 0;
  right: 0;
  margin: 30px auto 0;
}
.homepage .main-content .products-list .product-info a:hover::after {
  left: 0;
  top: 0;
}
.homepage .main-content .products-list .product-info a::before {
  content: '';
  border: 2px solid #F79420;
  width: 150px;
  height: 40px;
  left: 0;
  top: 0;
  position: absolute;
  border-radius: 50px;
  z-index: 2;
}
.homepage .main-content .products-list .product-info a::after {
  content: '';
  background-color: #ffd4a2;
  width: 150px;
  height: 40px;
  left: 5px;
  top: 5px;
  position: absolute;
  border-radius: 50px;
  z-index: 1;
  transition: all 0.3s;
}
.homepage .main-content .products-list .product-info a span {
  position: relative;
  z-index: 3;
}
.homepage .main-content .home-institutes-list .product-info img {
  width: auto;
}
.homepage .main-content .home-institutes-list .product-info p {
  font-size: 16px;
}
@-webkit-keyframes floating {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0);
    transform: rotateX(0deg) translateY(0);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(15px);
    transform: rotateX(0deg) translateY(15px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0);
    transform: rotateX(0deg) translateY(0);
  }
}
@keyframes floating {
  0% {
    -webkit-transform: rotateX(0deg) translateY(0);
    transform: rotateX(0deg) translateY(0);
  }
  50% {
    -webkit-transform: rotateX(0deg) translateY(15px);
    transform: rotateX(0deg) translateY(15px);
  }
  100% {
    -webkit-transform: rotateX(0deg) translateY(0);
    transform: rotateX(0deg) translateY(0);
  }
}
.homepage-new .home-welcome {
  overflow: hidden;
}
.homepage-new .home-welcome p {
  line-height: 1.5;
  font-size: 14px;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-welcome p {
    font-size: 18px;
  }
}
.homepage-new .home-welcome .card-title {
  font-weight: 400;
  font-size: 1.15rem;
  position: relative;
  z-index: 2;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-welcome .card-title {
    font-size: 1.5rem;
  }
}
.homepage-new .home-welcome .card-title strong {
  color: #212121;
  font-weight: 500;
}
.homepage-new .home-welcome .card-title.hero-text {
  color: #212121;
  max-width: 250px;
  font-size: 1.25rem;
  top: 5px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage-new .home-welcome .card-title.hero-text {
    max-width: 400px;
    font-size: 1.75rem;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-welcome .card-title.hero-text {
    font-size: 2rem;
    max-width: inherit;
  }
}
.homepage-new .home-welcome .card-title.hero-text strong {
  color: #F79420;
  font-weight: 700;
}
.homepage-new .home-welcome .card-text {
  max-width: 250px;
  position: relative;
  z-index: 2;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage-new .home-welcome .card-text {
    max-width: 300px;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-welcome .card-text {
    max-width: inherit;
  }
}
.homepage-new .home-welcome .hero-image {
  position: absolute;
  right: 0;
  bottom: 0;
  max-width: 150px;
  width: 150px;
  z-index: 1;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage-new .home-welcome .hero-image {
    max-width: 250px;
    width: 250px;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-welcome .hero-image {
    max-width: 300px;
    width: 300px;
    right: 50px;
  }
}
.homepage-new .home-welcome::after {
  content: '';
  position: absolute;
  right: -20px;
  bottom: -20px;
  width: 120px;
  height: 120px;
  background: #FFF0CC;
  border-radius: 100%;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .homepage-new .home-welcome::after {
    right: 0;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-welcome::after {
    right: 50px;
  }
}
.homepage-new .home-report p {
  font-size: 13px;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-report p {
    font-size: 14px;
  }
}
.homepage-new .home-report::after {
  top: -50px;
  right: -50px;
  bottom: auto;
}
.homepage-new .home-report::before {
  content: '';
  position: absolute;
  right: -55px;
  top: -55px;
  bottom: auto;
  width: 135px;
  height: 135px;
  border: 2px solid #FFF0CC;
  border-radius: 100%;
}
.homepage-new .home-report .card-body::after {
  content: '';
  position: absolute;
  right: 10px;
  top: 100px;
  bottom: auto;
  width: 25px;
  height: 25px;
  background-color: #FFF0CC;
  border-radius: 100%;
}
.homepage-new .home-report .card-title {
  font-size: 1.15rem;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-report .card-title {
    font-size: 1.25rem;
  }
}
.homepage-new .home-report .current-badge a {
  cursor: pointer;
}
.homepage-new .home-report .current-badge a i {
  position: relative;
  top: 5px;
  font-size: 20px;
}
.homepage-new .home-report .seven-days-report .flex-fill:first-child {
  border-right: 1px solid #DEE2E6;
  padding-left: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage-new .home-report .seven-days-report .flex-fill:first-child {
    padding-right: 0;
    border-right: none;
  }
}
.homepage-new .home-report .seven-days-report .flex-fill:last-child {
  padding-right: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage-new .home-report .seven-days-report .flex-fill:last-child {
    padding-left: 0;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    border-top: 1px solid #DEE2E6;
  }
}
.homepage-new .home-report .seven-days-report .progress-hyperlink a {
  width: 150px;
  background-color: #212121 !important;
  border-color: #212121 !important;
}
.homepage-new .home-report .seven-days-report .progress-hyperlink a:active {
  transform: scale(0.99);
}
.homepage-new .continue-learning-area #latestReadBooks {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.homepage-new .continue-learning-area #latestReadBooks::-webkit-scrollbar {
  display: none;
}
.homepage-new .continue-learning-area a.card {
  margin: 0 1rem 0 0;
}
.homepage-new .continue-learning-area a img {
  width: 80px;
  height: 90px;
  opacity: 1;
  border-radius: 5px;
  margin: 0;
  box-shadow: 0 2px 4px #CCC;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .continue-learning-area a img {
    height: 100px;
  }
}
.homepage-new .continue-learning-area a .learning-book-info h6 {
  font-weight: 400;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  white-space: normal;
  display: -webkit-box;
  font-size: 13px;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .continue-learning-area a .learning-book-info h6 {
    font-size: 1rem;
  }
}
.homepage-new .continue-learning-area a .learning-book-info p {
  text-transform: uppercase;
  max-width: 220px;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .continue-learning-area a .learning-book-info p {
    max-width: inherit;
  }
}
.homepage-new .institutes-area #instituteLists {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.homepage-new .institutes-area #instituteLists::-webkit-scrollbar {
  display: none;
}
.homepage-new .institutes-area #instituteLists h6 {
  font-weight: 400;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .institutes-area #instituteLists h6 {
    font-size: 1.15rem;
  }
}
.homepage-new .institutes-area #instituteLists .institutes-logo {
  opacity: 1;
}
.homepage-new .institutes-area #instituteLists .institute-list {
  padding: 0 1rem 0 0;
  -ms-flex: 0 0 49%;
  flex: 0 0 49%;
  max-width: 49%;
}
.homepage-new .institutes-area #instituteLists .flex-fill:nth-child(odd) {
  padding: 0 0.5rem 0 0;
}
.homepage-new .institutes-area #instituteLists .flex-fill:nth-child(even) {
  padding: 0 0 0 0.5rem;
}
.homepage-new .latest-quiz-area {
  background-color: #FFEDC1;
}
.homepage-new .latest-quiz-area::before {
  border-color: #FFE5A6;
}
.homepage-new .latest-quiz-area::after {
  background: #FFE5A6;
}
.homepage-new .latest-quiz-area .card-body::after {
  background-color: #FFE5A6;
}
.homepage-new .latest-quiz-area .retest-hyperlink a {
  z-index: 2;
  background-color: #212121 !important;
  border-color: #212121 !important;
}
.homepage-new .latest-quiz-area .retest-hyperlink a:active {
  transform: scale(0.99);
}
.homepage-new .latest-quiz-area .quiz-score {
  position: relative;
  z-index: 2;
  width: 65px;
  height: 65px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 5px solid #FFC100;
  border-radius: 100%;
}
.homepage-new .latest-quiz-area .quiz-score h6 {
  margin: 0;
}
.homepage-new .latest-quiz-area .quiz-score h6 small {
  opacity: 1;
  font-size: 0.75em;
}
.homepage-new .latest-quiz-area .quiz-details {
  position: relative;
  z-index: 2;
}
.homepage-new .latest-quiz-area .quiz-details p {
  font-size: 13px;
}
.homepage-new .search-area {
  background-color: #FFFFFF;
  border-radius: 7px;
}
.homepage-new .search-area form .input-group .form-control {
  border-color: #FFCD56;
}
.homepage-new .search-area form .input-group.is-focused .form-control {
  border-color: #FFCD56;
  border-radius: 7px 7px 0 0;
}
.homepage-new .search-area form .input-group-prepend {
  position: absolute;
  z-index: 2;
}
.homepage-new .search-area form .input-group-prepend .input-group-text {
  border-color: transparent;
}
.homepage-new .search-area form .input-group-prepend .input-group-text i {
  font-size: 26px;
  color: #CCC;
}
.homepage-new .search-area form #removeSearchTextBtn {
  z-index: 2;
  right: 0;
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage-new .search-area form #removeSearchTextBtn {
    top: 2px;
  }
}
.homepage-new .search-area form #removeSearchTextBtn i {
  font-size: 26px;
  color: #949494;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage-new .search-area form #removeSearchTextBtn i {
    font-size: 22px;
  }
}
.homepage-new .search-area form .form-control {
  border-radius: 7px;
  border-width: 2px;
  font-size: 14px;
  padding: 0.5rem 35px 0.5rem 35px;
  margin: -1px 0 0;
  position: relative;
  z-index: 1;
}
.homepage-new .search-area .search-by-category h6 {
  font-size: 13px;
}
.homepage-new .search-area .search-by-category a {
  background-color: #DAE0E5;
  border-color: transparent;
  margin: 0 5px 5px 0;
  font-size: 12px;
  padding: 0.25rem 0.5rem;
  border-radius: 3px;
}
.homepage-new .search-area .search-by-category a:active,
.homepage-new .search-area .search-by-category a:focus,
.homepage-new .search-area .search-by-category a:hover {
  border-color: #FFE29C;
  background-color: #FFF0CC;
}
.homepage-new .search-area .quick-search {
  position: absolute;
  right: 0;
  left: 0;
  border: 2px solid #FFCD56;
  border-radius: 0 0 7px 7px;
  background-color: #FFE5A6;
  margin-top: -2px;
}
.homepage-new .search-area .quick-search.hide {
  visibility: hidden;
  opacity: 0;
  z-index: -1;
  transition: all 0.2s linear;
}
.homepage-new .search-area .quick-search.show {
  visibility: visible;
  opacity: 1;
  z-index: 2;
  transition: all 0.2s linear;
}
.homepage-new .search-area .quick-search p {
  font-size: 10px;
  padding: 0.5rem 1rem 0;
}
.homepage-new .search-area .quick-search .list-group a.list-group-item {
  border-radius: 0;
  border: none;
  padding: 0.4rem 1rem;
  font-weight: 400;
  font-size: 13px;
  display: flex;
  background-color: transparent;
  color: #212121;
}
.homepage-new .search-area .quick-search .list-group a.list-group-item:active,
.homepage-new .search-area .quick-search .list-group a.list-group-item:hover,
.homepage-new .search-area .quick-search .list-group a.list-group-item:focus {
  color: #F79420;
  background-color: transparent;
}
.homepage-new .search-area .quick-search .list-group a.list-group-item i {
  padding-right: 7px;
  font-size: 20px;
}
.homepage-new .search-area ul.typeahead {
  border-width: 2px;
  border-color: #FFCD56;
  background-color: #FFE5A6;
  border-radius: 0 0 7px 7px;
  padding: 0;
  margin-top: -3px;
}
.homepage-new .search-area ul.typeahead li:active {
  background-color: transparent;
}
.homepage-new .search-area ul.typeahead li a {
  font-size: 13px;
  padding: 0.4rem 1rem;
}
.homepage-new .search-area ul.typeahead li a:active,
.homepage-new .search-area ul.typeahead li a:hover,
.homepage-new .search-area ul.typeahead li a:focus {
  color: #F79420;
  background-color: transparent;
}
.homepage-new .aside .today-test {
  position: sticky;
  top: 80px;
}
.homepage-new .aside .today-test h5 {
  font-weight: 400;
  color: #949494;
}
.homepage-new .aside .today-test img {
  width: 70%;
}
.homepage-new .aside .today-test a:active {
  transform: scale(0.99);
}
.homepage-new .home-section h5 {
  margin: 0;
}
.homepage-new .home-section h6 span {
  font-size: 0.75rem;
  font-weight: 400;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-section h6 span {
    font-size: 1rem;
  }
}
.homepage-new .home-section p {
  font-size: 12px;
  color: #949494;
  line-height: normal;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-section p {
    font-size: 13px;
  }
}
.homepage-new .home-section img {
  width: 40px;
  margin: 0 auto 10px;
  opacity: 0.75;
}
.homepage-new .home-section a.card {
  border-color: transparent;
  border-width: 2px;
}
.homepage-new .home-section a.card:active,
.homepage-new .home-section a.card:focus,
.homepage-new .home-section a.card:hover {
  border-color: #FFE29C;
}
.homepage-new .home-section.study-centre-area .flex-fill:first-child a.card {
  border-color: #FFCD56 !important;
}
.homepage-new .home-section.study-centre-area .flex-fill:first-child a.card h6 {
  color: #F79420;
}
.homepage-new .home-section.study-centre-area .flex-fill:first-child a.card p {
  color: #F79420;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .homepage-new .home-section.study-centre-area .flex-fill h6,
  .homepage-new .home-section.practice-area .flex-fill h6 {
    font-size: 1.15rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .homepage-new .home-section.study-centre-area a.card,
  .homepage-new .home-section.practice-area a.card {
    min-height: 140px;
  }
}
.homepage-new .home-section.study-centre-area a.card:active,
.homepage-new .home-section.practice-area a.card:active,
.homepage-new .home-section.study-centre-area a.card:focus,
.homepage-new .home-section.practice-area a.card:focus,
.homepage-new .home-section.study-centre-area a.card:hover,
.homepage-new .home-section.practice-area a.card:hover {
  background-color: #FFF0CC;
}
.homepage-new .home-section.about-area #aboutWonderslate {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.homepage-new .home-section.about-area #aboutWonderslate::-webkit-scrollbar {
  display: none;
}
.homepage-new .home-section.about-area a.btn {
  border-color: transparent;
  border-radius: 7px;
  background-color: #FFFFFF;
  text-align: center;
  padding: 1rem 2rem 1rem 1rem;
  margin: 0 1rem 0 0;
  color: #212121;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
.homepage-new .home-section.about-area a.btn i {
  position: absolute;
  right: 5px;
}
.homepage-new .home-section.about-area a.btn:active,
.homepage-new .home-section.about-area a.btn:focus,
.homepage-new .home-section.about-area a.btn:hover {
  border-color: #FFE29C;
  background-color: #FFF0CC;
}
.information #contents .info-description {
  overflow: hidden;
  margin-bottom: 7px;
}
.information #contents .info-description.with-answer-desc,
.information #contents .info-description.humour-desc {
  overflow: unset;
  margin-bottom: 0;
  text-overflow: unset;
  display: initial;
  -webkit-line-clamp: unset;
  -webkit-box-orient: unset;
}
.information #contents .info-description h1,
.information #contents .info-description h2,
.information #contents .info-description h3,
.information #contents .info-description h4,
.information #contents .info-description h5,
.information #contents .info-description h6 {
  color: #444444;
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}
.information #contents .info-description img {
  max-width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .information #contents .info-description img {
    width: 100%;
    height: auto;
  }
}
.information #contents .info-description p {
  margin-bottom: 10px;
}
.information #contents .info-description table {
  max-width: 100%;
}
.information #contents .created-date {
  font-size: 12px;
  color: #949494;
  font-style: italic;
}
.information #contents .created-date strong {
  font-weight: normal;
}
.information #contents .info-title {
  color: #212121;
}
.information #contents .info-title:hover {
  color: #0F0839;
  text-decoration: underline;
}
.information #contents p,
.information #contents li {
  color: #444444;
}
.information #contents ol,
.information #contents ul {
  padding-left: 15px;
}
.information #contents blockquote {
  margin-left: 10px;
}
.information #contents .share-info {
  width: 30px;
  height: 30px;
  cursor: pointer;
  padding: 15px;
}
.information #contents .share-info i {
  font-size: 17px;
}
.information #contents .info-list {
  position: relative;
}
.information #contents .info-list .divider {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  background-image: url("../../images/ws/horizontal-separator-line.svg");
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
  width: 100%;
  height: 4px;
  opacity: 0.5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .information #contents .info-list .divider {
    background-size: cover;
  }
}
.information #contents .info-list:last-child .divider {
  display: none;
}
.information .show-info-details {
  font-size: 14px;
}
.information .show-info-details:hover {
  text-decoration: underline;
}
.information-detail .prev-info-link a:hover span,
.information-detail .next-info-link a:hover span {
  text-decoration: underline;
}
.information-detail #contents .info-description {
  overflow: unset;
  margin-bottom: 0;
  text-overflow: unset;
  display: initial;
  -webkit-line-clamp: unset;
  -webkit-box-orient: unset;
}
@media (max-width: 575.98px) {
  .institute-access-code .access_code {
    width: 100% !important;
  }
}
.institute-access-code .access_code #accessCode {
  background-color: transparent;
}
.libwonder .access-code-link a:hover {
  text-decoration: underline;
}
.contact-us .container {
  max-width: 900px;
}
.contact-us .contact-address {
  flex-direction: row;
  margin: 0 auto;
}
.contact-us .address-info {
  flex-direction: column;
  border-left: 1px solid #e1e1e1;
}
.contact-us .address-info h5 {
  font-size: 15px;
  color: #949494;
}
.contact-us .address-info address {
  font-size: 12px;
  color: #949494;
}
@media (max-width: 575.98px) {
  .contact-us .address-info address {
    font-size: 12px;
  }
}
.contact-us .address-info address span {
  font-size: 13px;
  padding-top: 7px;
  display: inline-block;
}
.contact-us .address-info address a {
  color: #0F0839;
  font-size: 12px;
}
.contact-us .address-info address a:hover {
  text-decoration: underline;
}
.contact-us .address-info .whatsapp-link {
  font-size: 12px;
  color: #949494;
  margin-top: -0.5rem;
  align-items: center;
}
.contact-us .address-info .whatsapp-link span {
  padding-right: 3px;
}
.contact-us .address-info .whatsapp-link p {
  font-size: 12px;
  align-items: center;
  padding-left: 3px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .contact-us .address-info .whatsapp-link p {
    padding-top: 3px;
    padding-left: 0;
  }
}
.contact-us .address-info .whatsapp-link a {
  padding-right: 3px;
  color: #27AE60;
}
.contact-us .address-info .whatsapp-link a span {
  padding-right: 0;
}
@media (max-width: 575.98px) {
  #drift-frame-controller {
    bottom: 115px !important;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  #drift-frame-controller {
    bottom: 75px !important;
  }
}
.digital-library {
  overflow-x: hidden;
}
.digital-library .main-section {
  min-height: 600px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .main-section {
    min-height: auto;
  }
}
.digital-library .banner {
  background: url("../../images/wslibrary/banner.svg") right no-repeat;
  background-size: contain;
  height: 400px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .banner .container-fluid {
    padding: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .banner {
    background: none;
    border-radius: 0px 0px 20px 20px;
    height: 100%;
    margin-top: 2rem;
    padding-bottom: 0;
  }
}
.digital-library .banner .banner-img {
  min-height: 450px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .banner .banner-img {
    height: auto;
    position: relative;
    bottom: -70px;
    min-height: 100%;
    max-width: 100%;
  }
}
.digital-library .btn-login {
  background: none;
  color: black;
  border: 1.25px solid rgba(68, 68, 68, 0.85);
  box-sizing: border-box;
  border-radius: 10px;
  font-size: 14px;
}
.digital-library .banner-section {
  margin-top: 4rem;
  padding-bottom: 4rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .banner-section {
    margin-top: 0;
  }
}
.digital-library .banner-section h1 {
  font-weight: 700;
  font-size: 36px;
  color: #444444;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .banner-section h1 {
    font-size: 24px;
  }
}
.digital-library .banner-section h1 span {
  color: #aeaeae;
  font-weight: 400;
}
.digital-library .banner-section p {
  color: #2ec1c0;
}
.digital-library .banner-section .btn-catalog {
  background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #2FD1AE 0%, #2C8FB7 100%);
  color: #FFFFFF;
  border-radius: 10px;
}
.digital-library .banner-section .available {
  color: #aeaeae;
}
.digital-library .ebook-banner {
  margin-top: 4rem;
  background: url("../../images/wslibrary/bgOrange.svg") right no-repeat;
  background-size: cover;
  height: 400px;
  display: flex;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .ebook-banner {
    margin-top: 4rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .ebook-banner .container {
    text-align: center;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .ebook-banner {
    height: 100%;
    padding: 2rem 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .ebook-banner img {
    margin: 2rem auto;
    max-width: 100%;
  }
}
.digital-library .features {
  min-height: 600px;
}
.digital-library .features .offer {
  font-size: 24px;
  font-weight: 700;
  color: #5e5e5e;
  padding: 3rem 0 2rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .features .offer {
    padding-bottom: 0;
  }
}
.digital-library .features .card {
  min-height: 270px;
  margin-top: 2rem;
  border: none;
  box-shadow: 0 4px 10px #0000001A;
}
.digital-library .features .card img {
  width: 116px;
  height: 92px;
  margin: 2rem auto;
}
.digital-library .features .card h4 {
  color: #5e5e5e;
  font-weight: 700;
  font-size: 18px;
}
.digital-library .features .card p {
  font-size: 14px;
  font-weight: 400;
  color: #5e5e5e;
  line-height: 1.5;
}
.digital-library .setup {
  margin-top: 6rem;
  background: url("../../images/wslibrary/bgOrange.svg") right no-repeat;
  background-size: cover;
  min-height: 400px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .setup {
    margin-top: 4rem;
  }
}
.digital-library .setup .row {
  margin: 0 auto;
}
.digital-library .setup .setup-simple {
  font-weight: 700;
  font-size: 24px;
  color: #FFFFFF;
  text-align: center;
  padding: 3rem 0 2rem;
}
.digital-library .setup .card {
  border: none;
  background: none;
  align-self: center;
}
.digital-library .setup .card h4 {
  color: #FFFFFF;
  font-weight: 700;
  font-size: 30px;
  margin-top: 3rem;
  margin-bottom: 0;
}
.digital-library .setup .card p {
  font-size: 13px;
  font-weight: 400;
  color: #212121;
  line-height: 1.5;
}
.digital-library .setup .card .circle {
  width: 22px;
  height: 22px;
  border-radius: 50px;
  position: relative;
  left: 0.8rem;
}
.digital-library .setup .card .circle.bg-blue {
  background: #86B1F2;
  box-shadow: 0 4px 10px #0000001A;
}
.digital-library .setup .card .circle.bg-blue h2 {
  left: -6px;
}
.digital-library .setup .card .circle.bg-pink {
  background: #F47458;
  box-shadow: 0 4px 10px #0000001A;
}
.digital-library .setup .card .circle.bg-thickBlue {
  background: #466EB6;
  box-shadow: 0 4px 10px #0000001A;
}
.digital-library .setup .card .circle h2 {
  color: rgba(255, 255, 255, 0.63);
  font-size: 52px;
  font-weight: 700;
  position: absolute;
  top: -8px;
  left: -15px;
}
.digital-library .price-layer {
  min-height: 600px;
  padding: 4rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .price-layer {
    padding: 0;
  }
}
.digital-library .price-layer .card {
  background: #FFFFFF;
  border-radius: 10px;
  border: none;
}
.digital-library .price-layer .card h1 {
  color: #444444;
  font-size: 30px;
  text-align: center;
  font-weight: 400;
  margin-top: 4rem;
}
.digital-library .price-layer .card h1 span {
  color: #FF6677;
  display: block;
  font-size: 36px;
  font-weight: 700;
}
.digital-library .price-layer .card.pink-bg {
  background: radial-gradient(159.88% 172.05% at -21.41% -39.3%, #FF6677 0%, #D21982 100%);
  min-height: 600px;
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .price-layer .card.pink-bg {
    border-radius: 0;
    min-height: auto;
  }
}
.digital-library .price-layer .card.pink-bg ul {
  margin: 4rem auto;
}
.digital-library .price-layer .card.pink-bg ul li {
  font-size: 14px;
  font-weight: 700;
  color: #FFFFFF;
  padding: 0.5rem;
}
.digital-library .price-layer .card.pink-bg ul li span {
  font-weight: 400;
  color: rgba(255, 255, 255, 0.63);
}
.digital-library .price-layer .card.whiteBg {
  background: #FFFFFF;
  box-shadow: 0 4px 10px rgba(236, 68, 124, 0.15);
  border-radius: 10px;
  min-height: 600px;
}
.digital-library .price-layer .card.whiteBg img {
  height: 300px;
}
.digital-library .price-layer .card .btn-green {
  background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #2FD1AE 0%, #2C8FB7 100%);
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  margin: 2rem auto;
  color: #FFFFFF;
}
.digital-library .priceList {
  min-height: 600px;
}
.digital-library .priceList .btn-demo {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  color: #c300f3;
  font-size: 14px;
  margin: 0 auto;
}
.digital-library .priceList ul {
  list-style-image: url('../../images/wslibrary/correction.svg');
}
.digital-library .priceList ul li {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 5px;
  padding-top: 10px;
}
.digital-library .priceList .col-12 {
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .priceList .col-12 {
    padding-left: 0;
  }
}
.digital-library .priceList .pricing-text {
  position: relative;
  z-index: 99;
  text-align: center;
}
.digital-library .priceList .pricing-text p {
  color: #FFFFFF;
  font-size: 24px;
  font-weight: 400;
  text-align: center;
}
.digital-library .priceList .pricing-text h1 {
  font-size: 64px;
  font-weight: 700;
  color: #FFFFFF;
}
.digital-library .priceList .pricing-text h1 sub {
  font-size: 24px;
  font-weight: 400;
}
.digital-library .priceList .price-img {
  background: url("../../images/wslibrary/price-image.svg") left no-repeat;
  background-size: contain;
  min-height: 520px;
  position: relative;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .priceList .price-img {
    display: none;
  }
}
.digital-library .priceList .price-image1 {
  position: absolute;
  top: 0;
  left: 0;
  background: url("../../images/wslibrary/priceimage_1.svg") left no-repeat;
  background-size: contain;
  min-height: 520px;
  border-radius: 10px;
  width: 755px;
}
.digital-library .priceList .price-agenda {
  color: rgba(68, 68, 68, 0.85);
  font-weight: 400;
}
.digital-library .priceList .col-8 {
  margin: 0 auto;
}
.digital-library .priceList .cover-price {
  background: url("../../images/wslibrary/coverprice.svg") left no-repeat;
  background-size: cover;
  width: 100%;
  position: relative;
  margin: 10px;
}
.digital-library .priceList .coverprice1 {
  width: 100%;
}
.digital-library .priceList .price-wrappers {
  display: flex;
  position: absolute;
  top: 30px;
  width: 100%;
  align-items: center;
}
.digital-library .priceList .price-wrappers > div {
  width: 50%;
  padding: 0 10px;
  text-align: center;
}
.digital-library .priceList .price-wrappers p {
  font-size: 24px;
  color: #FFFFFF;
  font-weight: 400;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .priceList .price-wrappers p {
    font-size: 14px;
  }
}
.digital-library .priceList .price-wrappers h1 {
  color: #FFFFFF;
  font-size: 28px;
  font-weight: 700;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .priceList .price-wrappers h1 {
    font-size: 18px;
  }
}
.digital-library .banner_wrap {
  width: 90%;
  margin: 0 auto;
  padding-bottom: 3rem;
}
@media (max-width: 1200px) {
  .digital-library .banner_wrap {
    width: 100%;
  }
}
.digital-library .banner_wrap .banner_info h2 {
  color: #FF5700;
  text-shadow: 0 4px 10px #0000001A;
  line-height: 1.3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_info h2 {
    font-size: 1.5rem;
    line-height: 1.5;
    text-align: center;
  }
}
@media (max-width: 575.98px) {
  .digital-library .banner_wrap .banner_info h2 {
    font-size: 1.35rem;
  }
}
.digital-library .banner_wrap .banner_info p {
  color: #444444;
}
.digital-library .banner_wrap .banner_info p br {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_info p br {
    display: block;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_info p {
    color: #212121;
    line-height: 22px;
    text-align: center;
    font-size: 15px;
  }
}
@media (max-width: 575.98px) {
  .digital-library .banner_wrap .banner_info p {
    font-size: 13.4px;
  }
}
.digital-library .banner_wrap .banner_info .icon-lists .icon-list {
  text-align: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_info .icon-lists .icon-list {
    margin-bottom: 15px;
  }
}
.digital-library .banner_wrap .banner_info .icon-lists .icon-list p {
  font-size: 14px;
  color: #444444;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_info .icon-lists .icon-list p {
    color: #212121;
  }
}
.digital-library .banner_wrap .banner_info .icon-lists .icon-list p br {
  display: block;
}
.digital-library .banner_wrap .banner_info .bg-white {
  background: transparent !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_info .bg-white {
    justify-content: center;
  }
}
.digital-library .banner_wrap .banner_info .bg-white p {
  font-size: 14px;
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 5px;
}
.digital-library .banner_wrap .banner_register_form {
  margin-top: -80px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library .banner_wrap .banner_register_form {
    margin-top: 0;
  }
}
.digital-library .banner_wrap .banner_register_form .form-info {
  background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, #ffffff 0%, #f7f7f7 100%);
  backdrop-filter: blur(10px);
  border-radius: 20px;
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal {
  margin-top: 0;
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group {
  background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #FFD000 0%, #FF5700 100%);
  box-shadow: 0 0 10px #0000001A;
  border-radius: 20px;
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group h3 {
  color: rgba(255, 255, 255, 0.48);
  font-size: 1.5rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group h3 {
    font-size: 1.25rem;
  }
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input {
  color: #212121;
  height: 35px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input {
    font-size: 15px;
  }
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input:focus {
  box-shadow: none;
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input::-webkit-input-placeholder {
  color: rgba(68, 68, 68, 0.81) !important;
  font-size: 13px !important;
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input::-moz-placeholder {
  color: rgba(68, 68, 68, 0.81) !important;
  font-size: 13px !important;
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input:-ms-input-placeholder {
  color: rgba(68, 68, 68, 0.81) !important;
  font-size: 13px !important;
}
.digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group input:-moz-placeholder {
  color: rgba(68, 68, 68, 0.81) !important;
  font-size: 13px !important;
}
@media (max-width: 575.98px) {
  .digital-library .banner_wrap .banner_register_form .form-info .form-horizontal .form-group .alert {
    font-size: 14px !important;
  }
}
.digital-library .banner_wrap .banner_register_form .form-info button.submit-btn {
  background: #FF9901;
  border: 1px solid #FF9901;
  box-sizing: border-box;
  box-shadow: 0 -4px 10px #0000001A;
  border-radius: 5px;
  width: 85%;
  font-weight: 500;
  text-transform: uppercase;
}
.digital-library .banner_wrap .banner_register_form .form-info .bottom-image {
  margin-bottom: -70px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .digital-library .banner_wrap .banner_register_form .form-info .bottom-image {
    margin-bottom: 0;
  }
}
.digital-library-footer {
  display: flex;
  align-items: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library-footer {
    padding-bottom: 4rem;
  }
}
.digital-library-footer .contactus {
  color: #FFFFFF;
  font-size: 12px;
  text-align: center;
}
.digital-library-footer .wsborder {
  border-right: 0.3px solid rgba(255, 255, 255, 0.5);
  padding-right: 20px;
  height: 75px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .digital-library-footer .wsborder {
    border-right: none;
    padding-right: 0;
    text-align: center;
  }
}
.digital-library-footer .mobile-no {
  opacity: 0.8;
  color: #FFFFFF;
  font-size: 12px;
  font-weight: 400;
  text-align: center;
  padding: 0;
  margin: 0;
}
.digital-library-footer .social-icons {
  justify-content: center !important;
}
.hidden-footer .footer-menus {
  min-height: auto;
  margin: 0 !important;
  padding: 0 !important;
}
.hidden-footer .footer-menus .logo-wrapper,
.hidden-footer .footer-menus .container-fluid,
.hidden-footer .footer-menus #download-app-btn-container {
  display: none !important;
}
.hidden-footer .prepjoy_cta {
  display: none !important;
}
.edit-profile .profile {
  text-align: center;
}
.edit-profile .profile .image-wrapper {
  width: 85px;
  height: 85px;
  background: #FFFFFF;
  border: 3px solid #FFFFFF;
  filter: drop-shadow(0px 0px 10px #00000040);
  position: relative;
  border-radius: 50%;
  margin: 0 auto;
}
.edit-profile .profile .image-wrapper img {
  width: 80px;
  height: 80px;
}
.edit-profile .profile .image-wrapper i {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 14px;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0F0839;
}
.edit-profile .profile .user-name #firstname {
  font-weight: 700;
}
.edit-profile .profile .user-name #secondname {
  font-weight: 400;
}
.edit-profile .profile #grade-rating {
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-profile .profile #grade-rating i {
  background: #FFC107;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-size: 18px;
  margin-right: 5px;
}
.edit-profile .profile .wonderwonk {
  color: #444444;
  font-weight: 400;
  font-size: 11px;
}
.edit-profile .btn-makepoint {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 36px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2F80ED;
}
.edit-profile .btn-makepoint i {
  margin-left: 5px;
  font-size: 16px;
  color: #2F80ED;
}
.edit-profile .margin-reset-auto {
  margin: 0 auto;
}
.edit-profile form label {
  color: #949494;
  font-size: 11px;
  font-style: italic;
}
.edit-profile form input.form-control {
  border: 1px solid #0F0839;
}
.edit-profile form select.form-control {
  border: 1px solid #0F0839;
}
.edit-profile form .btn-submit {
  background: #FFD602;
  box-shadow: 0 0 5px #0000001A;
  border-radius: 5px;
  color: #212121;
  padding: 0.5rem;
  font-weight: 500;
}
.edit-profile .profile-wrapper .profile {
  margin: 0 auto;
}
.edit-profile .profile-wrapper .profile .user-name {
  font-size: 24px;
  text-align: center;
  color: #0F0839;
  display: inline-block !important;
}
.edit-profile #morePoints {
  background: none !important;
}
.edit-profile #morePoints .modal-dialog {
  height: 100%;
  margin: 0;
  max-width: 100%;
}
.edit-profile #morePoints .modal-dialog h1,
.edit-profile #morePoints .modal-dialog p {
  color: #FFFFFF;
}
.edit-profile #morePoints .modal-dialog .modal-header {
  border: none;
}
.edit-profile #morePoints .modal-dialog .modal-header .close {
  font-weight: 400;
  color: #FFFFFF;
  text-shadow: unset;
  opacity: 1;
}
.edit-profile #morePoints .modal-dialog .modal-content {
  height: 100%;
  background: #000000B3;
  border-radius: 0;
}
.edit-profile #morePoints .modal-dialog .modal-content .modal-body h2 {
  text-align: center;
  color: #FFFFFF;
  font-weight: 700;
}
.edit-profile #morePoints .modal-dialog .modal-content .modal-body p {
  text-align: center;
  font-size: 12px;
}
.edit-profile #morePoints .modal-dialog .modal-content .modal-body p strong {
  font-weight: 700;
}
.edit-profile #morePoints .modal-dialog .modal-content .modal-body ul {
  margin-top: 2rem;
  padding: 0;
}
.edit-profile #morePoints .modal-dialog .modal-content .modal-body ul li {
  text-align: center;
  color: #FFFFFF;
  font-size: 10px;
  list-style-type: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-profile #morePoints .modal-dialog .modal-content .modal-body ul li i {
  background: #FFC107;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-size: 16px;
}
.edit-profile #morePoints .modal-dialog .modal-content .modal-body ul li span {
  display: block;
  margin-left: 10px;
}
.edit-profile #morePoints .modal-dialog .modal-footer {
  border: none;
}
.edit-profile .carousel-item h2,
.edit-profile .carousel-item p {
  color: #FFFFFF;
}
.edit-profile .carousel-item.tutor .circle {
  width: 26.25px;
  height: 26.25px;
  border-radius: 50px;
  border: 1px solid #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
}
.edit-profile .carousel-item.tutor .circle i {
  color: #FFFFFF;
  font-size: 16px;
}
.edit-profile .carousel-item.tutor .btn-answer {
  background: #FFFFFF;
  border: 1.25px solid #0F0839;
  box-sizing: border-box;
  border-radius: 5px;
  color: #0F0839;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.edit-profile .carousel-item.tutor .btn-answer i {
  margin-right: 10px;
}
.edit-profile .carousel-item.tutor h2 {
  font-size: 36px;
  margin-bottom: 1rem;
}
.edit-profile .carousel-item.tutor p {
  font-size: 14px;
  color: #FFFFFF;
}
.edit-profile .carousel-item.tutor .wonderwonk {
  color: #FFFFFF;
  font-size: 12px;
}
.edit-profile .carousel-item.tutor #grade-rating {
  display: flex;
  margin-right: 5px;
}
.edit-profile .carousel-item.tutor #grade-rating i {
  background: #FFC107;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
  font-size: 16px;
}
.academy-page {
  overflow-x: hidden !important;
}
.academy-page .future-ready {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1422' height='560' preserveAspectRatio='none' viewBox='0 0 1422 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1143%26quot%3b)' fill='none'%3e%3crect width='1422' height='560' x='0' y='0' fill='rgba(244%2c 244%2c 244%2c 1)'%3e%3c/rect%3e%3cpath d='M0%2c499.828C115.516%2c499.362%2c239.033%2c585.462%2c338.469%2c526.668C437.596%2c468.057%2c441.723%2c327.453%2c476.954%2c217.817C508.13%2c120.802%2c544.426%2c24.644%2c532.307%2c-76.534C520.015%2c-179.157%2c480.554%2c-279.87%2c408.467%2c-353.939C338.33%2c-426.005%2c234.189%2c-442.077%2c140.639%2c-478.971C36.697%2c-519.964%2c-60.185%2c-596.522%2c-171.033%2c-582.483C-287.211%2c-567.769%2c-406.791%2c-504.343%2c-464.337%2c-402.351C-520.496%2c-302.818%2c-453.269%2c-180.387%2c-462.188%2c-66.453C-470.306%2c37.253%2c-535.078%2c132.926%2c-514.219%2c234.836C-490.879%2c348.868%2c-443.693%2c476.085%2c-340.227%2c529.404C-236.945%2c582.629%2c-116.189%2c500.296%2c0%2c499.828' fill='%23e8e8e8'%3e%3c/path%3e%3cpath d='M1422 1040.155C1516.712 1030.864 1622.607 1047.1680000000001 1696.711 987.4580000000001 1769.531 928.783 1770.009 821.446 1802.339 733.6949999999999 1831.873 653.532 1893.4189999999999 578.478 1878.372 494.384 1863.416 410.801 1787.968 354.255 1724.377 297.989 1667.704 247.84500000000003 1598.338 222.69600000000003 1531.408 187.389 1445.561 142.103 1370.719 42.184999999999945 1275.545 61.221000000000004 1181.781 79.97500000000002 1153.858 201.171 1093.146 275.046 1034.379 346.554 953.7860000000001 400.68100000000004 924.4300000000001 488.46 892.039 585.314 889.037 691.7529999999999 921.393 788.619 955.719 891.385 1013.509 996.335 1110.3220000000001 1044.98 1205.085 1092.595 1316.454 1050.509 1422 1040.155' fill='white'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1143'%3e%3crect width='1422' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
}
.academy-page h1,
.academy-page h2,
.academy-page h3,
.academy-page h4 {
  color: #212121;
}
.academy-page .modal {
  z-index: 99999;
}
.academy-page .modal-content {
  border: none;
  border-radius: 15px;
}
.academy-page .modal {
  z-index: 10000;
}
.academy-page .register input,
.academy-page .register textarea {
  border: none !important;
}
.academy-page a {
  text-decoration: none!important;
}
.academy-page .tasks-list-group li {
  list-style: none;
}
.academy-page .tasks-list-group li i {
  color: #17A2B8;
}
.academy-page .gifts li {
  list-style: none;
  font-size: 17px;
}
.academy-page .card {
  box-shadow: 0px 4px 8px 0px #949494;
  border-radius: 8px !important;
}
.academy-page .color-green {
  background: #32c5d2;
}
.academy-page .description-content p {
  line-height: 1.6 !important;
}
.academy-page .stars i {
  color: yellow;
}
.academy-page .ws-des a {
  color: #00FE00;
  text-decoration: none;
}
.academy-page .description-cont {
  border-radius: 10px !important;
}
.academy-page .task-content {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1337' height='560' preserveAspectRatio='none' viewBox='0 0 1337 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1703%26quot%3b)' fill='none'%3e%3cpath d='M111.011%2c239.349C156.852%2c241.652%2c197.966%2c214.178%2c222.388%2c175.315C248.662%2c133.505%2c260.623%2c81.61%2c237.581%2c37.936C213.162%2c-8.348%2c163.34%2c-36.219%2c111.011%2c-35.789C59.394%2c-35.365%2c9.788%2c-6.805%2c-12.009%2c39.986C-31.389%2c81.588%2c-6.634%2c125.961%2c17.467%2c165.019C39.883%2c201.346%2c68.379%2c237.207%2c111.011%2c239.349' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M37.36%2c98.666C58.47%2c97.976%2c75.763%2c83.92%2c86.428%2c65.689C97.218%2c47.244%2c101.616%2c24.925%2c91.514%2c6.095C80.901%2c-13.687%2c59.805%2c-25.347%2c37.36%2c-25.774C14.128%2c-26.216%2c-8.835%2c-16.141%2c-20.483%2c3.965C-32.158%2c24.118%2c-29.736%2c49.213%2c-17.596%2c69.089C-5.974%2c88.117%2c15.075%2c99.394%2c37.36%2c98.666' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M35.033%2c102.125C59.629%2c102.113%2c84.91%2c92.191%2c96.065%2c70.27C106.501%2c49.762%2c95.217%2c26.853%2c83.377%2c7.122C72.009%2c-11.822%2c57.1%2c-30.377%2c35.033%2c-31.439C11.4%2c-32.576%2c-9.919%2c-18.236%2c-22.103%2c2.046C-34.687%2c22.994%2c-38.218%2c49.108%2c-26.102%2c70.33C-13.896%2c91.71%2c10.414%2c102.137%2c35.033%2c102.125' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1366.595%2c310.204C1422.358%2c311.09%2c1460.614%2c260.258%2c1487.402%2c211.343C1512.866%2c164.844%2c1526.959%2c110.205%2c1501.623%2c63.636C1475.305%2c15.262%2c1421.665%2c-10.497%2c1366.595%2c-10.709C1311.135%2c-10.922%2c1255.486%2c13.461%2c1229.756%2c62.591C1205.432%2c109.036%2c1226.9%2c161.718%2c1252.259%2c207.606C1278.805%2c255.642%2c1311.719%2c309.332%2c1366.595%2c310.204' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1358.196%2c276.874C1412.032%2c280.17%2c1465.885%2c257.614%2c1494.453%2c211.864C1524.676%2c163.463%2c1529.346%2c100.197%2c1498.491%2c52.196C1469.566%2c7.198%2c1411.681%2c-1.019%2c1358.196%2c-0.111C1306.752%2c0.762%2c1251.031%2c11.919%2c1225.914%2c56.823C1201.201%2c101.005%2c1221.984%2c153.391%2c1248.066%2c196.779C1273.132%2c238.476%2c1309.636%2c273.901%2c1358.196%2c276.874' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M1243.22%2c68.838C1260.808%2c68.129%2c1275.598%2c57.237%2c1284.615%2c42.12C1293.894%2c26.563%2c1298.636%2c7.138%2c1289.27%2c-8.367C1280.134%2c-23.492%2c1260.888%2c-26.649%2c1243.22%2c-26.409C1226.084%2c-26.176%2c1208.729%2c-21.447%2c1199.287%2c-7.145C1188.859%2c8.651%2c1186.135%2c29.322%2c1195.609%2c45.708C1205.075%2c62.079%2c1224.324%2c69.6%2c1243.22%2c68.838' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3cpath d='M53.47%2c574.659C82.916%2c575.424%2c116.21%2c571.706%2c130.826%2c546.132C145.375%2c520.674%2c131.611%2c490.183%2c115.898%2c465.427C101.581%2c442.87%2c80.187%2c424.26%2c53.47%2c424.257C26.749%2c424.254%2c4.751%2c442.492%2c-8.979%2c465.415C-23.172%2c489.111%2c-30.934%2c518.307%2c-17.226%2c542.286C-3.44%2c566.403%2c25.7%2c573.938%2c53.47%2c574.659' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M137.503%2c743.592C190.204%2c744.189%2c227.825%2c698.501%2c253.534%2c652.493C278.457%2c607.892%2c293.147%2c554.636%2c267.914%2c510.21C242.435%2c465.351%2c189.023%2c445.517%2c137.503%2c448.205C90.462%2c450.66%2c51.51%2c481.472%2c28.711%2c522.692C6.707%2c562.475%2c5.852%2c609.13%2c25.938%2c649.915C48.933%2c696.606%2c85.46%2c743.002%2c137.503%2c743.592' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M63.198%2c595.899C94.374%2c595.272%2c124.319%2c582.47%2c140.834%2c556.021C158.377%2c527.926%2c163.328%2c491.616%2c145.9%2c463.45C129.169%2c436.409%2c94.987%2c429.753%2c63.198%2c430.546C33.078%2c431.297%2c3.709%2c442.106%2c-12.653%2c467.405C-30.532%2c495.049%2c-36.781%2c530.907%2c-20.045%2c559.258C-3.527%2c587.239%2c30.712%2c596.553%2c63.198%2c595.899' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1286.016%2c596.003C1314.761%2c594.7%2c1333.836%2c569.851%2c1348.361%2c545.011C1363.08%2c519.839%2c1376.292%2c490.534%2c1362.807%2c464.68C1348.562%2c437.369%2c1316.806%2c424.593%2c1286.016%2c425.481C1256.756%2c426.325%2c1231.233%2c443.587%2c1216.534%2c468.901C1201.766%2c494.334%2c1198.938%2c525.339%2c1213.045%2c551.145C1227.724%2c577.997%2c1255.445%2c597.389%2c1286.016%2c596.003' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float2'%3e%3c/path%3e%3cpath d='M1265.556%2c549.857C1290.229%2c551.032%2c1317.265%2c546.91%2c1330.459%2c526.028C1344.349%2c504.045%2c1340.856%2c475.054%2c1326.533%2c453.351C1313.514%2c433.624%2c1289.182%2c428.354%2c1265.556%2c427.682C1240.268%2c426.962%2c1211.174%2c427.949%2c1198.257%2c449.701C1185.182%2c471.719%2c1196.263%2c499.062%2c1210.545%2c520.317C1222.987%2c538.833%2c1243.273%2c548.796%2c1265.556%2c549.857' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float1'%3e%3c/path%3e%3cpath d='M1250.426%2c526.861C1271.323%2c527.262%2c1293.431%2c522.372%2c1304.56%2c504.68C1316.305%2c486.009%2c1314.979%2c461.607%2c1303.205%2c442.954C1292.146%2c425.433%2c1271.145%2c418.925%2c1250.426%2c418.918C1229.692%2c418.911%2c1208.318%2c425.178%2c1197.588%2c442.92C1186.534%2c461.198%2c1188.398%2c484.591%2c1199.777%2c502.668C1210.46%2c519.639%2c1230.377%2c526.476%2c1250.426%2c526.861' fill='rgba(255%2c 193%2c 7%2c 1)' class='triangle-float3'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1703'%3e%3crect width='1337' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cstyle%3e %40keyframes float1 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-10px%2c 0)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float1 %7b animation: float1 5s infinite%3b %7d %40keyframes float2 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(-5px%2c -5px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float2 %7b animation: float2 4s infinite%3b %7d %40keyframes float3 %7b 0%25%7btransform: translate(0%2c 0)%7d 50%25%7btransform: translate(0%2c -10px)%7d 100%25%7btransform: translate(0%2c 0)%7d %7d .triangle-float3 %7b animation: float3 6s infinite%3b %7d %3c/style%3e%3c/defs%3e%3c/svg%3e");
}
.academy-page .quote {
  position: relative;
}
.academy-page .quote div {
  padding: 0px;
  border: 0;
  margin: 0;
  font-size: 14px;
  font-style: italic;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
}
.academy-page .quote div p {
  color: #FFFFFF;
  padding-top: 25px;
  padding-bottom: 45px;
  padding-left: 30px;
  padding-right: 30px;
}
.academy-page .quote.green div {
  background-color: #32c5d2;
}
.academy-page .quote.dark div {
  background-color: #444444;
}
.academy-page .quote-footer {
  margin: 10px 0;
}
.academy-page .quote-footer .quote-author-img img {
  float: left;
  max-width: 90px;
  width: 90px;
  height: 90px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  border-radius: 50%;
  margin-left: -5px;
  margin-top: -40px;
  position: relative;
  z-index: 1;
  padding: 5px;
  background-color: #FFFFFF;
}
.academy-page .quote-footer h4 {
  font-size: 14px;
  margin-bottom: 4px;
}
.academy-page .quote-footer p {
  font-weight: 400;
  font-style: italic;
  font-size: 14px;
}
.academy-page .modal-content {
  border: none;
  border-radius: 15px;
}
.academy-page .modal-backdrop.show {
  opacity: 0;
}
.academy-page .modal {
  z-index: 10000;
}
.academy-page .register input .register textarea {
  border: none !important;
}
.academy-page .register input:focus,
.academy-page .register textarea:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 0, 0, 0.5) !important;
}
@media (max-width: 760px) {
  .academy-page .banner-img {
    width: 220px;
    position: relative;
    left: 50%;
    top: 60px;
    transform: translate(-50%, -50%);
    bottom: 0;
    min-height: 100%;
  }
}
@media (max-width: 760px) {
  .academy-page .htext {
    position: relative;
    top: 30px;
    font-size: 20px;
  }
  .academy-page .banner-img {
    width: 220px;
    position: relative;
    left: 50%;
    top: 126px;
    transform: translate(-50%, -50%);
    bottom: 0;
    min-height: 100%;
  }
}
@media only screen and (max-width: 767px) {
  .academy-page .header {
    height: 100%;
  }
  .academy-page .tasks-list-group {
    width: 100%;
  }
  .academy-page .testim h2 {
    margin-top: 1rem;
  }
  .academy-page .testim {
    padding-bottom: 2rem !important;
    padding-top: 1.5rem !important;
  }
  .academy-page .three-step {
    padding: 10px !important;
  }
}
.self_service #batchUsers {
  font-size: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .self_service #batchUsers {
    font-size: 14px;
  }
}
.self_service #instructions p {
  font-size: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .self_service #instructions p {
    font-size: 14px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .self_service #instructions iframe {
    height: auto;
  }
}
.self_service #instructions .whatsapp-link span {
  padding-right: 3px;
  font-size: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .self_service #instructions .whatsapp-link span {
    font-size: 14px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .self_service #instructions .whatsapp-link p {
    padding-top: 3px;
  }
}
.self_service #instructions .whatsapp-link a {
  padding-right: 3px;
  color: #27AE60;
}
.self_service #instructions .whatsapp-link a span {
  font-size: 24px;
  padding-right: 0;
}
.self_service ol li,
.self_service ul li {
  padding-bottom: 7px;
  font-size: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .self_service ol li,
  .self_service ul li {
    font-size: 14px;
    line-height: 20px;
  }
}
.self_service ol li a:hover,
.self_service ul li a:hover {
  text-decoration: underline;
}
.publisher_page #slider-desktop {
  background: #FFFDF5;
  min-height: 400px;
  margin-bottom: -2rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_page #slider-desktop {
    min-height: 350px;
  }
}
.publisher_page #slider-desktop .carousel-inner,
.publisher_page #slider-desktop .carousel-item {
  min-height: 400px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_page #slider-desktop .carousel-inner,
  .publisher_page #slider-desktop .carousel-item {
    min-height: 350px;
  }
}
.publisher_page #slider-desktop .carousel-inner img.banner-img,
.publisher_page #slider-desktop .carousel-item img.banner-img,
.publisher_page #slider-desktop .carousel-inner .no-banners,
.publisher_page #slider-desktop .carousel-item .no-banners {
  min-height: 400px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_page #slider-desktop .carousel-inner img.banner-img,
  .publisher_page #slider-desktop .carousel-item img.banner-img,
  .publisher_page #slider-desktop .carousel-inner .no-banners,
  .publisher_page #slider-desktop .carousel-item .no-banners {
    min-height: 350px;
  }
}
.publisher_page #slider-desktop .carousel-indicators {
  margin-bottom: 3rem;
}
.publisher_page #slider-desktop .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #6d58e8;
}
.publisher_page #slider-desktop .carousel-indicators li.active {
  background: #0F0839;
}
.publisher_page #slider-mobile {
  background: #FFFDF5;
  min-height: 300px;
  margin-bottom: -1.5rem;
}
@media (max-width: 575.98px) {
  .publisher_page #slider-mobile {
    min-height: 250px;
  }
}
.publisher_page #slider-mobile .carousel-inner,
.publisher_page #slider-mobile .carousel-item {
  min-height: 300px;
}
@media (max-width: 575.98px) {
  .publisher_page #slider-mobile .carousel-inner,
  .publisher_page #slider-mobile .carousel-item {
    min-height: 250px;
  }
}
.publisher_page #slider-mobile .carousel-inner img.banner-img,
.publisher_page #slider-mobile .carousel-item img.banner-img {
  min-height: 300px;
}
@media (max-width: 575.98px) {
  .publisher_page #slider-mobile .carousel-inner img.banner-img,
  .publisher_page #slider-mobile .carousel-item img.banner-img {
    min-height: 250px;
  }
}
.publisher_page #slider-mobile .carousel-inner .no-banners,
.publisher_page #slider-mobile .carousel-item .no-banners {
  min-height: 350px;
}
.publisher_page #slider-mobile .carousel-indicators {
  margin-bottom: 2rem;
}
.publisher_page #slider-mobile .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #6d58e8;
}
.publisher_page #slider-mobile .carousel-indicators li.active {
  background: #0F0839;
}
.publisher_page #banner-carousel h1,
.publisher_page #mobile-banner-carousel h1 {
  font-size: 25px;
  color: #212121;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_page #banner-carousel h1,
  .publisher_page #mobile-banner-carousel h1 {
    font-size: 20px;
  }
}
.publisher_page #banner-carousel .books-list .topSchoolBooks:hover,
.publisher_page #mobile-banner-carousel .books-list .topSchoolBooks:hover {
  box-shadow: 0 0 10px #00000040;
}
.publisher_page #banner-carousel .books-list .topSchoolBooks .image-wrapper,
.publisher_page #mobile-banner-carousel .books-list .topSchoolBooks .image-wrapper,
.publisher_page #banner-carousel .books-list .topSchoolBooks .uncover,
.publisher_page #mobile-banner-carousel .books-list .topSchoolBooks .uncover {
  height: 190px;
}
.publisher_page #banner-carousel .books-list .topSchoolBooks .image-wrapper img,
.publisher_page #mobile-banner-carousel .books-list .topSchoolBooks .image-wrapper img {
  height: 190px;
}
.publisher_page .publisher_categories #category-inner .category-menu {
  white-space: nowrap;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  text-align: left;
}
.publisher_page .publisher_categories #category-inner .btn {
  font-size: 16px;
}
@media (max-width: 575.98px) {
  .publisher_page .publisher_categories #category-inner .btn {
    font-size: 12px;
  }
}
.publisher_page .publisher_categories #category-inner .dropdown .dropdown-menu {
  z-index: 991;
}
.publisher_page .publisher_categories #category-inner .dropdown .dropdown-list-item {
  border-radius: 0;
}
.publisher_page .publisher_categories #category-inner .dropdown .dropdown-list-item:hover {
  background-color: #f5f5f5;
}
.publisher_page .publisher_categories #category-inner .show_more_categories {
  position: absolute;
  right: 0;
  z-index: 1;
}
@media (max-width: 575.98px) {
  .publisher_page .publisher_categories #category-inner .show_more_categories i {
    font-size: 20px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .publisher_page .carousel-inner .no-banners,
  .publisher_page .carousel-item .no-banners {
    flex-direction: column;
  }
}
.publisher_page .carousel-inner .no-banners .banner-books,
.publisher_page .carousel-item .no-banners .banner-books {
  margin-right: -1.5rem;
  margin-left: -1.5rem;
}
.purchase-details-container.ws-orders .purchase-heading {
  margin-top: 40px;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-heading {
    margin-top: 30px;
  }
}
.purchase-details-container.ws-orders .purchase-heading h3 {
  font-weight: 600;
  font-size: 24px;
  color: #0F0839;
}
.purchase-details-container.ws-orders .purchase-details-wrapper {
  margin: 0 auto 40px;
  min-height: auto;
}
.purchase-details-container.ws-orders .purchase-details-wrapper .purchase-details,
.purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book {
  width: 50%;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-details-wrapper .purchase-details,
  .purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book {
    width: 100%;
  }
}
.purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
  padding: 0 40px 24px;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
    padding: 0 0 24px;
  }
}
.purchase-details-container.ws-orders .purchase-details-wrapper .learn-btn {
  display: inline-block;
  margin-top: 0;
}
.purchase-details-container .purchase-details-wrapper {
  margin: 40px auto;
  min-height: 400px;
}
.purchase-details-container .purchase-details-wrapper .purchase-heading {
  font-weight: 500;
  font-size: 24px;
  background: #0F0839;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.purchase-details-container .purchase-details-wrapper .purchase-success-confirmation {
  font-size: 16px;
  margin-bottom: 7px;
}
@media (max-width: 575px) {
  .purchase-details-container .purchase-details-wrapper .purchase-success-confirmation {
    font-size: 15px;
  }
}
.purchase-details-container .purchase-details-wrapper .purchase-success-confirmation strong {
  font-weight: 600;
}
.purchase-details-container .purchase-details-wrapper .purchase-success-id {
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.01em;
  margin-bottom: 16px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: start;
  align-items: start;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper {
  width: 100%;
  border-radius: 6px;
  background-color: #FFFFFF;
  padding: 8px;
  border: 1px solid #848484;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-item {
  display: flex;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-img-wrapper img {
  width: 111px;
  box-shadow: 0 0 14px #0000001A;
  border-radius: 4px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info {
  vertical-align: top;
  padding: 0 15px 15px 15px;
  max-height: inherit;
  text-align: left;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .purchased-book-name {
  font-style: normal;
  font-weight: 500;
  line-height: 21px;
  font-size: 16px;
  letter-spacing: 0.01em;
  margin-bottom: 7px;
}
@media (max-width: 767px) {
  .purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .purchased-book-name {
    font-size: 15px;
  }
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .detail-book-author-name {
  text-align: left;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .offer-price {
  display: inline-block;
  font-size: 20px;
  font-weight: 500;
  color: #FF4B33;
  letter-spacing: 0.01em;
  margin-right: 4px;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .offer-price i {
  font-size: 18px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .original-price {
  display: inline-block;
  font-size: 16px;
  font-weight: 300;
  color: #949494;
  letter-spacing: 0.01em;
  text-decoration: line-through;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchase-details {
  float: left;
  width: 45%;
  margin-right: 80px;
}
@media screen and (max-width: 991px) {
  .purchase-details-container .purchase-details-wrapper .purchase-details {
    width: 100%;
  }
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book {
  float: left;
  width: 45%;
  border-left: 1px solid #848484;
}
@media screen and (max-width: 991px) {
  .purchase-details-container .purchase-details-wrapper .browse-purchase-book {
    width: 100%;
    border: 0;
  }
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
  padding: 24px 40px;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .continue-browse {
  display: block;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.01em;
  color: #0F0839;
  padding: 24px 0;
  margin-bottom: 24px;
  border-top: 1px solid #848484;
  border-bottom: 1px solid #848484;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .read-on-app {
  font-weight: 400;
  line-height: normal;
  font-size: 12px;
  text-align: center;
  letter-spacing: -0.01em;
  color: #848484;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .download-app-btn {
  display: block;
  text-align: center;
  max-width: 122px;
  height: 40px;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .download-app-btn .download-app-btn-img {
  width: 100%;
  height: auto;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  vertical-align: middle;
  z-index: 1;
  transition: 0.3s ease-out;
}
.purchase-details-container .purchase-details-wrapper .learn-btn {
  font-size: 14px;
  display: block;
  text-align: center;
  font-weight: 500;
  color: #FFFFFF;
  background: #0F0839;
  letter-spacing: 0.01em;
  padding: 11px 25px;
  border-radius: 4px;
  margin-bottom: 24px;
  margin-top: -10px;
}
.purchase-details-container .purchase-details-wrapper .learn-btn:hover {
  text-decoration: none;
  color: #FFFFFF;
  box-shadow: 0 2px 8px #0000001A;
}
.purchase-details-container .purchase-details-wrapper .instructions h5 {
  font-weight: 600;
}
.purchase-details-container .purchase-details-wrapper .instructions ol li {
  padding-bottom: 7px;
  font-size: 15px;
}
.purchase-details-container .purchase-details-wrapper .instructions ol li strong {
  font-weight: 600;
}
.institute-homepage {
  -webkit-font-smoothing: antialiased;
}
.institute-homepage #instituteBannerImages #slider-desktop,
.institute-homepage #instituteBannerImages .carousel-inner,
.institute-homepage #instituteBannerImages .carousel-item {
  min-height: auto;
}
.institute-homepage .default-banner h1 {
  margin-bottom: 15px;
  font-weight: 600;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .default-banner h1 {
    font-size: 28px;
  }
}
.institute-homepage .default-banner p {
  font-size: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .default-banner p {
    font-size: 16px;
  }
}
.institute-homepage .default-banner #instituteDefaultBanner {
  min-height: 300px;
  background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, #ffffff 0%, #feeedc 100%);
  display: flex;
  justify-content: center;
  align-items: center;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .institute-homepage .default-banner #instituteDefaultBanner {
    min-height: 250px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .default-banner #instituteDefaultBanner {
    min-height: 200px;
  }
}
.institute-homepage .buttons-wrapper a {
  position: relative;
  width: 150px;
  height: 45px;
  border-radius: 50px;
  background-color: transparent !important;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s;
  box-shadow: 0 2px 7px #0000001A;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .buttons-wrapper a {
    width: 130px;
    height: 40px;
    font-size: 14px;
  }
}
.institute-homepage .buttons-wrapper a:hover::after {
  left: 0;
  top: 0;
}
.institute-homepage .buttons-wrapper a::before {
  content: '';
  border: 2px solid #F79420;
  width: 150px;
  height: 45px;
  left: 0;
  top: 0;
  position: absolute;
  border-radius: 50px;
  z-index: 2;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .buttons-wrapper a::before {
    width: 130px;
    height: 40px;
  }
}
.institute-homepage .buttons-wrapper a::after {
  content: '';
  background-color: #ffd4a2;
  width: 150px;
  height: 45px;
  left: 5px;
  top: 5px;
  position: absolute;
  border-radius: 50px;
  z-index: 1;
  transition: all 0.3s;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .buttons-wrapper a::after {
    width: 130px;
    height: 40px;
  }
}
.institute-homepage .buttons-wrapper a:nth-child(2)::after {
  display: none;
}
.institute-homepage .buttons-wrapper a span {
  position: relative;
  z-index: 3;
}
.institute-homepage .ebooks-categories h2 {
  margin-bottom: 15px;
  font-weight: 600;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .ebooks-categories h2 {
    font-size: 1.75rem;
  }
}
.institute-homepage .ebooks-categories p {
  font-size: 16px;
  font-weight: 600;
  color: #6C757D;
}
.institute-homepage .exploring-ebooks #instituteGrades a.grade-link {
  padding: 7px 20px;
  border: 1px solid #c7c7c7;
  border-radius: 7px;
  display: inline-block;
  margin-right: 1rem;
  box-shadow: 0 1px 2px #0000001A;
  color: #444444;
  transition: all 0.3s;
  margin-bottom: 1rem;
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .exploring-ebooks #instituteGrades a.grade-link {
    font-size: 15px;
  }
}
.institute-homepage .exploring-ebooks #instituteGrades a.grade-link:hover {
  color: #F79420;
  box-shadow: 0 2px 6px #0000001A;
  border-color: #F79420;
  background-color: #FFFFFF;
}
.institute-homepage .exploring-ebooks #instituteCategories .category-info {
  margin-bottom: 30px;
}
.institute-homepage .exploring-ebooks #instituteCategories a.category-link {
  border: 1px solid transparent;
  background-color: #FFFFFF;
  box-shadow: 0 2px 4px #0000001A;
  color: #444444;
  transition: all 0.3s;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  font-size: 18px;
  text-align: center;
  padding: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .exploring-ebooks #instituteCategories a.category-link {
    font-size: 16px;
  }
}
.institute-homepage .exploring-ebooks #instituteCategories a.category-link:hover {
  color: #F79420;
  box-shadow: 0 2px 6px #0000001A;
  border-color: #F79420;
}
@media (max-width: 575.98px) {
  .institute-homepage .videos-wrapper .col-md-6 {
    padding: 0;
  }
}
.institute-homepage .videos-wrapper iframe {
  border: 1px solid #949494;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .institute-homepage .videos-wrapper iframe {
    height: 250px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .institute-homepage .videos-wrapper iframe {
    height: 180px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .institute-homepage .videos-wrapper iframe {
    height: 200px;
  }
}
.lg-container .lg-backdrop,
.lg-container .lg-outer {
  z-index: 9991;
}
.lg-container .lg-outer .lg-thumb-item {
  border: 2px solid #FFFFFF;
}
.lg-container .lg-outer .lg-thumb-item.active,
.lg-container .lg-outer .lg-thumb-item:hover {
  border-color: #F79420;
}
.gallery-item {
  width: 200px;
  padding: 5px;
}
.store-integration-header {
  top: 0;
  z-index: 10;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-header .header-wrapper h4 {
    font-size: 1.25rem;
  }
  .store-integration-header .header-wrapper h4 img {
    width: 40px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-banner .container {
    padding: 0;
  }
}
.store-integration-banner video {
  box-shadow: 0 2px 4px #0000001A;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-banner video {
    border-radius: 0;
  }
}
.store-integration-content .main-heading h1 span {
  color: #F79420;
}
.store-integration-content .why-ebooks .reason-point {
  text-align: center;
  min-height: 280px;
  background-color: #FFFFFF;
  border: 1px solid transparent;
  border-radius: 20px;
  margin-bottom: 20px;
  transition: all 0.3s;
  box-shadow: 0 2px 7px #0000001A;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .store-integration-content .why-ebooks .reason-point {
    min-height: 300px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .store-integration-content .why-ebooks .reason-point {
    min-height: 330px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-content .why-ebooks .reason-point {
    min-height: auto;
  }
}
.store-integration-content .why-ebooks .reason-point:hover {
  border-color: #F79420;
}
.store-integration-content .why-ebooks .reason-point img {
  opacity: 0.8;
}
.store-integration-content .why-ebooks .reason-point p {
  font-size: 18px;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-content .why-ebooks .reason-point p {
    font-size: 16px;
  }
}
.store-integration-content .benefits {
  background: url("../../images/eduwonder/drawer_bg.png");
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 10px;
  padding: 2rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-content .benefits {
    padding: 1.5rem;
  }
}
.store-integration-content .benefits .benefits-content h1 span {
  color: #F79420;
}
.store-integration-content .benefits .benefits-content ul {
  padding-left: 20px;
}
.store-integration-content .benefits .benefits-content ul li {
  font-size: 18px;
  line-height: normal;
  padding-bottom: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-content .benefits .benefits-content ul li {
    font-size: 16px;
  }
}
.store-integration-content .benefits .benefits-image img {
  width: 400px;
  height: auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-content .benefits .benefits-image img {
    width: 100%;
  }
}
.store-integration-footer .footer-wrapper {
  background-color: #212121;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .store-integration-footer .footer-wrapper .border-left {
    border-left: none !important;
  }
}
.store-integration-footer .footer-wrapper .contact-wrapper h5 {
  color: #FFFFFF;
}
.store-integration-footer .footer-wrapper .contact-wrapper p {
  opacity: 0.8;
  color: #FFFFFF;
  font-size: 16px;
}
.download-apps {
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}
.download-apps .main-heading h1 {
  font-weight: 100;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .download-apps .main-heading h1 {
    text-align: center;
  }
}
.download-apps .main-heading h1 span {
  font-weight: 600;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .download-apps .benefits-content {
    padding: 0;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .download-apps .benefits-image img {
    width: 100% !important;
  }
}
.download-apps .app-links a {
  margin-right: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .download-apps .app-links a {
    margin: 0 7px 15px;
  }
}
.download-apps .app-links a img {
  width: 200px;
  height: auto;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .download-apps .app-links a img {
    width: 170px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .download-apps .app-links a img {
    width: 150px;
  }
}
@media (max-width: 575.98px) {
  .download-apps .app-links a img {
    width: 120px;
  }
}
.leaderboard #goBack {
  color: #212121;
}
.leaderboard-share {
  width: 40px;
  height: 40px;
  font-size: 12px;
  line-height: normal;
  background-color: #0F0839;
}
.leaderboard-share i {
  font-size: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-share {
    width: 50px;
    height: 50px;
    font-size: 13px;
    position: fixed;
    bottom: 80px;
    right: 12px;
    z-index: 999;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2), 0 2px 10px 0 rgba(0, 0, 0, 0.1);
  }
}
.leaderboard-tabs a {
  display: block;
  width: 150px;
  border-radius: 0;
  font-weight: 500;
  background-color: #FFFFFF;
  border-color: #F79420;
  color: #F79420;
  position: relative;
  transition: all 0.2s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-tabs a {
    width: 120px;
  }
}
.leaderboard-tabs a.active {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.leaderboard-tabs a.active:after {
  content: '';
  position: absolute;
  z-index: 1;
  left: 0;
  right: 0;
  margin: auto;
  bottom: -10px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid #F79420;
}
.leaderboard-tabs a:first-child {
  border-top-left-radius: 10px;
}
.leaderboard-tabs a:last-child {
  border-top-right-radius: 10px;
}
.leaderboard-info {
  border-radius: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-info {
    padding: 0;
  }
}
.leaderboard-info hr {
  background: radial-gradient(circle, rgba(0, 0, 0, 0.2) 0%, #ffffff 100%);
  height: 1px;
  border: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-sidebar {
    padding: 0;
    margin-top: 30px;
  }
}
.leaderboard-sidebar h6 {
  font-size: 15px;
}
.leaderboard-sidebar a {
  border-color: transparent;
  transition: all 0.2s linear;
}
.leaderboard-sidebar a span,
.leaderboard-sidebar a i {
  transition: all 0.2s linear;
}
.leaderboard-sidebar a:hover,
.leaderboard-sidebar a:focus {
  border-color: #F79420;
}
.leaderboard-sidebar a:hover span,
.leaderboard-sidebar a:focus span {
  color: #F79420;
  transition: all 0.2s linear;
}
.leaderboard-sidebar a:hover i,
.leaderboard-sidebar a:focus i {
  margin-right: -7px;
  transition: all 0.2s linear;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-sidebar a:hover i,
  .leaderboard-sidebar a:focus i {
    margin-right: 0;
    color: #F79420;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-sidebar a small {
    display: none;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-sidebar .others a {
    display: inline-grid;
    text-align: center;
    padding: 6px !important;
  }
  .leaderboard-sidebar .others a span {
    padding: 0 !important;
    display: block;
    line-height: normal;
  }
  .leaderboard-sidebar .others a:nth-child(2) {
    margin: 0 7px;
  }
  .leaderboard-sidebar .others a:last-child {
    margin-right: 0;
  }
}
.leaderboard .filter-wrapper {
  border-radius: 50px;
  background-color: #F9F9F9;
  padding: 7px;
  width: 70%;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #EEE;
  position: relative;
  justify-content: center;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard .filter-wrapper {
    width: 100%;
    padding: 5px;
    margin-top: 10px;
  }
}
.leaderboard .filter-wrapper .active-tab-bg {
  position: absolute;
  height: 100%;
  width: 33.33%;
  left: 0;
  top: 0;
  z-index: 0;
  border-radius: 50px;
  background: #15002D;
  box-shadow: 0 1px 3px #eeeeee;
  transition: 0.3s ease-out;
}
.leaderboard .filter-wrapper a {
  position: relative;
  color: #212121;
  transition: color 0.4s ease;
  text-align: center;
  z-index: 1;
  padding: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard .filter-wrapper a {
    font-size: 13px;
  }
}
.leaderboard .filter-wrapper a.active {
  color: #FFFFFF;
}
.leaderboard .filter-wrapper a.weekly-tab.active ~ .active-tab-bg {
  left: 33.33%;
}
.leaderboard .filter-wrapper a.monthly-tab.active ~ .active-tab-bg {
  left: 66.66%;
}
.leaderboard .calendar_wrapper #selectedCurrentDate a {
  color: #212121;
  font-weight: normal;
  font-size: 13px;
  line-height: normal;
}
.leaderboard .calendar_wrapper #selectedCurrentDate a i {
  font-size: 20px;
}
.leaderboard .calendar_wrapper.days_calendar {
  cursor: default;
}
.leaderboard .calendar_wrapper.days_calendar:hover {
  background: none;
}
.leaderboard-top .top-rank figure {
  position: relative;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figure {
    width: 70px;
    margin: 0 auto;
  }
}
.leaderboard-top .top-rank figure:after {
  content: '';
  position: absolute;
  width: 110px;
  height: 110px;
  left: 0;
  right: 0;
  top: -5px;
  z-index: 0;
  border-radius: 100%;
  margin: 0 auto;
  opacity: 0.5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figure:after {
    width: 80px;
    height: 80px;
    left: -5px;
  }
}
.leaderboard-top .top-rank figure img {
  position: relative;
  z-index: 1;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figure img {
    width: 70px;
    height: 70px;
  }
}
.leaderboard-top .top-rank figure .rank-badge {
  margin-top: -20px;
  display: block;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figure .rank-badge img {
    width: 40px !important;
    height: 40px !important;
  }
}
.leaderboard-top .top-rank figure .crown-img {
  position: absolute;
  top: -35px;
  left: 0;
  right: 0;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figure .crown-img img {
    width: 40px !important;
    height: 40px !important;
  }
}
.leaderboard-top .top-rank figcaption {
  max-width: 160px;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figcaption {
    min-height: 65px;
    max-width: 85px;
  }
}
.leaderboard-top .top-rank figcaption h5 {
  font-weight: 300;
  margin-top: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figcaption h5 {
    font-size: 16px;
  }
}
.leaderboard-top .top-rank figcaption p {
  line-height: 1.3;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank figcaption p {
    line-height: 1.2;
  }
}
.leaderboard-top .top-rank figcaption small {
  color: #6C757D;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: normal;
  padding-top: 5px;
}
.leaderboard-top .top-rank.second figure:after {
  background: linear-gradient(180deg, #b3b3b3 0%, rgba(179, 179, 179, 0) 100%);
}
.leaderboard-top .top-rank.third figure:after {
  background: linear-gradient(180deg, #d9866e 0%, rgba(217, 134, 110, 0) 100%);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank.first figure {
    width: 90px;
  }
}
.leaderboard-top .top-rank.first figure:after {
  width: 135px;
  height: 135px;
  background: linear-gradient(180deg, #ffd164 0%, rgba(255, 209, 100, 0) 100%);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank.first figure:after {
    width: 100px;
    height: 100px;
  }
}
.leaderboard-top .top-rank.first figure img {
  box-shadow: 0 0 100px rgba(250, 204, 98, 0.5);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank.first figure img {
    width: 90px;
    height: 90px;
  }
}
.leaderboard-top .top-rank.first figcaption p {
  font-size: 15px;
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-top .top-rank.first figcaption p {
    font-size: 14px;
  }
}
.leaderboard-list table {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}
.leaderboard-list table thead th {
  border: none;
  font-size: 12px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-list table thead th {
    font-size: 11px;
  }
}
.leaderboard-list table tbody tr {
  position: relative;
}
.leaderboard-list table tbody tr:last-child {
  display: none;
}
.leaderboard-list table tbody tr.current-rank:after {
  height: 70px;
  background: rgba(255, 195, 81, 0.3);
  z-index: 0;
  top: 6px;
  width: 96%;
  margin: 0 auto;
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-list table tbody tr.current-rank:after {
    height: 55px;
  }
}
.leaderboard-list table tbody tr:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 1px;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.2) 0%, #ffffff 100%);
}
.leaderboard-list table tbody tr td {
  vertical-align: middle;
  border: none;
  font-size: 14px;
  position: relative;
  z-index: 1;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-list table tbody tr td {
    font-size: 13px;
  }
}
.leaderboard-list table tbody .user-info {
  padding-left: 0;
  padding-right: 0;
}
.leaderboard-list table tbody .user-info .avatar img {
  border: 2px solid rgba(217, 217, 217, 0.5);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-list table tbody .user-info .avatar img {
    width: 45px;
    height: 45px;
  }
}
.leaderboard-list table tbody .user-info .name {
  line-height: 1.2;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-list table tbody .user-info .name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
@media (max-width: 330px) {
  .leaderboard-list table tbody .user-info .name {
    max-width: 105px !important;
  }
}
@media (max-width: 575.98px) {
  .leaderboard-list table tbody .user-info .name {
    max-width: 140px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-list table tbody .user-info .name {
    max-width: 200px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-list table tbody .user-info .name {
    max-width: 250px;
  }
}
.leaderboard-list table tbody .user-info .name small {
  color: #949494;
  padding-top: 5px;
  display: block;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-list table tbody .user-info .name small {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
@media (max-width: 330px) {
  .leaderboard-list table tbody .user-info .name small {
    max-width: 105px !important;
  }
}
@media (max-width: 575.98px) {
  .leaderboard-list table tbody .user-info .name small {
    max-width: 140px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-list table tbody .user-info .name small {
    max-width: 200px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .leaderboard-list table tbody .user-info .name small {
    max-width: 250px;
  }
}
#dailyTestModal .modal-content {
  border-radius: 15px;
}
#dailyTestModal .modal-body h4 {
  font-size: 20px;
}
#datePickerModal.modal-modifier .modal-content-modifier {
  border-radius: 10px !important;
}
.progress-report .report-start {
  background: #FFF0CC;
  border-style: dashed;
  border-color: var(--warning);
}
.progress-report .report-start .card-title {
  font-weight: 400;
}
.progress-report .report-start .card-title strong {
  color: #212121;
  font-weight: 500;
}
.progress-report .report-start .current-badge a {
  cursor: pointer;
}
.progress-report .report-start .current-badge a i {
  position: relative;
  top: 5px;
  font-size: 20px;
}
.progress-report .report-filter h6 {
  margin: 0;
}
.progress-report .report-filter .position-relative {
  background-color: #212121;
  border-radius: 5px;
}
.progress-report .report-filter #dateRange {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  border-radius: 5px;
  min-width: 120px;
  background-color: transparent;
  color: #FFFFFF;
  position: relative;
  z-index: 2;
  padding-right: 25px;
}
.progress-report .report-filter #dateRange option {
  color: #212121;
}
.progress-report .report-filter #dateRange:hover {
  cursor: pointer;
}
.progress-report .report-filter #dateRange:active,
.progress-report .report-filter #dateRange:focus {
  outline: 0;
  box-shadow: none;
}
.progress-report .report-filter i {
  position: absolute;
  top: 4px;
  right: 2px;
  font-size: 20px;
  color: #FFFFFF;
  z-index: 1;
}
.progress-report .time-report .card-body h6 {
  font-weight: 400;
}
.progress-report .time-report .card-body h5 {
  margin: 0;
}
.progress-report .subjects-report h5 {
  margin: 0;
}
.progress-report .subjects-report p {
  font-size: 13px;
}
.progress-report .subjects-report table thead tr th:first-child {
  min-width: 120px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .progress-report .subjects-report table thead tr th:first-child {
    min-width: 150px;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .progress-report .subjects-report table thead tr th:first-child {
    min-width: 200px;
  }
}
.progress-report .subjects-report table thead tr th:last-child {
  min-width: 100px;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .progress-report .subjects-report table thead tr th:last-child {
    min-width: 125px;
  }
}
.progress-report .observation-report table thead tr th {
  min-width: 150px;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .progress-report .observation-report table thead tr th {
    min-width: 200px;
  }
}
.progress-report .observation-report table thead tr th:first-child {
  min-width: 80px;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .progress-report .observation-report table thead tr th:first-child {
    min-width: 150px;
  }
}
.progress-report ol li {
  line-height: normal;
  padding-bottom: 0.5rem;
}
.progress-report table thead tr.text-muted {
  color: #A1A5B7 !important;
}
.progress-report table thead tr th {
  border: none;
  padding: 1rem;
}
.progress-report table thead tr th:first-child {
  border-radius: 5px 0 0 5px;
}
.progress-report table thead tr th:last-child {
  border-radius: 0 5px 5px 0;
}
.progress-report table tbody tr {
  border-bottom: 1px dashed #EEE;
}
.progress-report table tbody tr td {
  border: none;
  padding: 1rem;
}
.progress-report table tbody tr td:first-child {
  padding-left: 0;
}
.progress-report table tbody tr td a:hover,
.progress-report table tbody tr td a:focus {
  color: var(--dark) !important;
  background-color: #FFF0CC !important;
}
.progress-report table tbody tr td a:active {
  transform: scale(0.97);
}
#letsImprove h6 b {
  width: 120px;
  display: inline-block;
}
#letsImprove a {
  font-weight: 500;
}
#letsImprove a:active {
  transform: scale(0.97);
}
.chart-loader {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  z-index: 1;
  width: 50px;
  height: 50px;
  border: 5px solid #F3F3F3;
  border-radius: 50%;
  border-top: 5px solid #3498DB;
  -webkit-animation: spinLoader 1s linear infinite;
  animation: spinLoader 1s linear infinite;
}
@-webkit-keyframes spinLoader {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spinLoader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.fav_title {
  margin-bottom: 18px;
  width: 65%;
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 768px) {
  .fav_title {
    width: 100%;
  }
}
.fav_title .fav_title-text {
  margin-top: 3rem;
  font-size: 28px;
}
@media (max-width: 768px) {
  .fav_title .fav_title-text {
    margin-top: 1.4rem;
    font-size: 24px;
  }
}
.fav_subjects {
  margin-bottom: 18px;
  width: 65%;
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 768px) {
  .fav_subjects {
    width: 100%;
  }
}
.fav_subjects-dropdown {
  padding: 7px;
  width: 220px;
  border: none;
  border-radius: 5px;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.2);
}
.fav_subjects-dropdown:focus-visible {
  border: none !important;
  outline: none;
}
.fav_mcqsList {
  min-height: 400px;
  padding-bottom: 5rem;
}
.fav_mcqs-card {
  background: #FFFFFF;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 18px;
  margin-left: auto;
  margin-right: auto;
  width: 65%;
}
@media (max-width: 768px) {
  .fav_mcqs-card {
    width: 100%;
  }
}
.fav_mcqs-card_star {
  text-align: end;
  margin-bottom: 14px;
}
.fav_mcqs-card_star i {
  cursor: pointer;
}
.fav_mcqs-card_question {
  margin-bottom: 14px;
  padding: 10px;
}
.fav_mcqs-card_options .fav_mcqs-card_option {
  background: #f4f4f4;
  padding: 10px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
}
.shimmerLoader {
  color: grey;
  display: inline-block;
  -webkit-mask: linear-gradient(-60deg, #000 30%, #0005, #000 70%) right / 300% 100%;
  background-repeat: no-repeat;
  animation: shimmer 2.5s infinite;
  font-size: 50px;
}
.correctAnsHl {
  background: #42B538 !important;
  color: #fff;
}
.favMcqBtnNav {
  margin-left: 2rem;
  background: #F79420;
  color: #fff;
  border: none;
  padding: 5px;
  width: 150px;
  border-color: #F79420;
  border-radius: 5px;
  text-align: center;
}
.favMcqBtnNav:hover {
  color: #fff !important;
}
.clearFilterIcon {
  background: transparent;
  border: none;
}
.clearFilterIcon:disabled {
  opacity: 0.5;
}
.clearFilterIcon img {
  width: 25px;
}
.takeTestBtn {
  background: #F79420;
  padding: 0.5rem 2rem;
  color: #fff;
  border-radius: 5px;
  margin-top: 3px;
  width: 150px;
  text-align: center;
}
.takeTestBtn:hover {
  color: #fff !important;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.pre-loaderDiv {
  height: 50px;
  background: #f4f4f4;
  border-radius: 10px;
}
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
html,
body {
  overflow-x: inherit !important;
  position: relative !important;
}
@media screen and (max-width: 767px) {
  .hiddenDiv {
    display: none !important;
  }
}
.header {
  background: #0F0839;
  padding: 2rem;
  position: sticky;
  top: 0;
  z-index: 999999;
  width: 100%;
}
@media screen and (max-width: 767px) {
  .header {
    padding: 1rem;
  }
}
.header-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
@media screen and (max-width: 767px) {
  .header-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
  .header-wrapper :nth-child(3) {
    order: 4;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
  .header-wrapper :nth-child(3) {
    order: 4;
  }
}
.header-wrapper__logo {
  display: flex;
  justify-content: space-between;
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo a img {
    width: 85px !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo a img {
    width: 100px;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo a span {
    font-size: 17px !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo a span {
    font-size: 17px !important;
  }
}
.header-wrapper__logo button {
  display: none;
  margin-left: auto;
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo button {
    display: block;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo button {
    display: block;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__logo {
    width: 100%;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__logo {
    width: 100%;
  }
}
.header-wrapper__navbar {
  display: flex;
  align-items: center;
  margin-left: auto;
}
.header-wrapper__navbar .navbar-list {
  display: flex;
  margin-bottom: 0;
  flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  .header-wrapper__navbar .navbar-list {
    flex-direction: column;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__navbar .navbar-list {
    flex-direction: column;
  }
}
.header-wrapper__navbar .navbar-list li {
  list-style: none;
  margin-right: 20px;
}
@media screen and (max-width: 767px) {
  .header-wrapper__navbar .navbar-list li {
    margin-bottom: 12px;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__navbar .navbar-list li {
    margin-bottom: 12px;
  }
}
.header-wrapper__navbar .navbar-list li a {
  color: #fff !important;
  transition: all 0.3s ease;
  position: relative;
}
.header-wrapper__navbar .navbar-list li a::before {
  content: "";
  position: absolute;
  height: 2px;
  background-color: #E83500;
  width: 0;
  left: 50%;
  bottom: -8px;
  transform: translateX(-50%);
  transition: 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55) all;
}
.header-wrapper__navbar .navbar-list li a:hover {
  color: #E83500 !important;
}
.header-wrapper__navbar .navbar-list li a:hover::before {
  width: 100%;
  border-bottom-color: #E83500;
}
@media screen and (max-width: 767px) {
  .header-wrapper__navbar {
    margin-left: 0;
    margin-top: 14px;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__navbar {
    margin-left: 0;
    margin-top: 14px;
  }
}
.header-wrapper__searchbar {
  display: flex;
  align-items: center;
  margin-bottom: 0!important;
  position: relative;
}
.header-wrapper__searchbar .search-bar__wrapper {
  width: 100%;
  margin-right: 30px;
}
.header-wrapper__searchbar .search-bar__wrapper input {
  width: 300px;
  outline: none;
  padding: 6px;
  border-radius: 5px;
  transition: all 0.4s ease;
  font-size: small;
  border: none;
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar .search-bar__wrapper input {
    width: 100%;
  }
  .header-wrapper__searchbar .search-bar__wrapper input:focus {
    width: 100% !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__searchbar .search-bar__wrapper input {
    width: 100%;
  }
  .header-wrapper__searchbar .search-bar__wrapper input:focus {
    width: 100% !important;
  }
}
.header-wrapper__searchbar .search-bar__wrapper input:focus {
  width: 350px;
}
.header-wrapper__searchbar .search-bar__wrapper button {
  background: #fff;
  border: none;
  outline: none;
  cursor: auto;
  color: #E83500;
  margin-left: -35px;
  border-radius: 50%;
  padding: 5px;
  width: 30px;
  height: 30px;
}
.header-wrapper__searchbar .search-bar__wrapper ul {
  width: 80%;
  position: absolute;
  min-height: 100px;
  max-height: 500px;
  overflow-y: scroll;
}
@media screen and (max-width: 860px) {
  .header-wrapper__searchbar .search-bar__wrapper ul {
    width: 75%;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar .search-bar__wrapper ul {
    width: 88%;
  }
}
@media screen and (max-width: 550px) {
  .header-wrapper__searchbar .search-bar__wrapper ul {
    width: 82% !important;
  }
}
.header-wrapper__searchbar .search-bar__wrapper ul li a {
  white-space: pre-line;
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar .search-bar__wrapper {
    margin-right: 10px;
  }
}
@media screen and (max-width: 767px) {
  .header-wrapper__searchbar {
    width: 100%;
    margin-top: 20px !important;
  }
}
@media screen and (max-width: 860px) {
  .header-wrapper__searchbar {
    width: 100%;
    margin-top: 30px;
  }
}
.header-wrapper__userProfile .profile-wrapper {
  border: 2px solid #fff;
  padding: 1rem;
  border-radius: 50%;
  height: 50px;
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.header-wrapper__userProfile .profile-wrapper span {
  border: 1px solid #fff;
  border-radius: 50%;
}
.header-wrapper__userProfile .profile-wrapper span img {
  transform: scale(0.9);
  object-fit: cover;
}
#prepjoyRegBtn {
  background: #E83500;
  color: #fff;
}
#prepjoyLoginModal {
  font-family: Righteous;
}
#prepjoyLoginModal #prepjoyRegModalContent h4 {
  color: #E83500;
}
#prepjoyLoginModal #prepjoyRegModalContent #regLoginForm {
  padding: 1rem;
}
#prepjoyLoginModal #prepjoyRegModalContent #regLoginForm .prepjoyLoginBtn {
  background: #E83500;
  color: #fff;
  margin-top: 10px;
}
.prepjoy-profile {
  background: #04001D;
  text-decoration: none;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 0 8px -3px #fff;
  transition: all 0.3s ease;
  font-family: Righteous;
  width: 30%;
  height: 100vh;
  z-index: 9999999;
}
@media screen and (max-width: 767px) {
  .prepjoy-profile {
    width: 100% !important;
  }
}
@media screen and (max-width: 800px) {
  .prepjoy-profile {
    width: 50%;
  }
}
.prepjoy-profile__content {
  flex-direction: column;
  height: 85%;
}
.prepjoy-profile__content img {
  border: 2px solid #fff;
  object-fit: cover;
}
.prepjoy-profile__content .logoutDiv {
  margin-top: auto;
}
@media screen and (max-width: 767px) {
  .prepjoy-profile__content {
    flex-direction: column;
  }
}
.logout {
  text-decoration: none;
  color: #E83500;
  font-size: 1.2rem;
}
#userCurBadge {
  color: #E83500 !important;
}
.ebooks .ebooks_filter select.background-bg {
  background: #E83500 !important;
  color: #fff !important;
  border-color: #E83500 !important;
}
.prepjoy-header__hamburger {
  width: 30px;
  height: 0;
  position: relative;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: 0.5s ease-in-out;
  -moz-transition: 0.5s ease-in-out;
  -o-transition: 0.5s ease-in-out;
  transition: 0.5s ease-in-out;
  cursor: pointer;
  display: none;
}
@media screen and (max-width: 767px) {
  .prepjoy-header__hamburger {
    display: block;
  }
}
@media screen and (max-width: 860px) {
  .prepjoy-header__hamburger {
    display: block;
  }
}
.prepjoy-header__hamburger span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: #fff;
  border-radius: 9px;
  opacity: 1;
  left: 0;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
  -webkit-transition: 0.25s ease-in-out;
  -moz-transition: 0.25s ease-in-out;
  -o-transition: 0.25s ease-in-out;
  transition: 0.25s ease-in-out;
}
.prepjoy-header__hamburger span:nth-child(1) {
  top: 0px;
}
.prepjoy-header__hamburger span:nth-child(2) {
  top: 10px;
}
.prepjoy-header__hamburger span:nth-child(3) {
  top: 20px;
}
.prepjoy-header__hamburger.open span:nth-child(1) {
  top: 18px;
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
}
.prepjoy-header__hamburger.open span:nth-child(2) {
  opacity: 0;
  left: -60px;
}
.prepjoy-header__hamburger.open span:nth-child(3) {
  top: 18px;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  transform: rotate(-135deg);
}
.modal-content {
  border: none;
}
#prepjoySignupModalBody {
  background: #0F0839;
}
.form .input {
  margin-top: 20px;
  text-align: left;
}
.form .input .inputBox label {
  display: block;
  color: #868686;
  margin-bottom: 5px;
  font-size: 18px;
}
.form .input .inputBox input {
  width: 100%;
  height: 50px;
  color: lightgrey;
  background: #04001D !important;
  border: none;
  outline: none;
  border-radius: 40px;
  padding: 7px 15px;
  font-size: 14px;
  font-weight: lighter !important;
  box-shadow: inset -2px -2px 6px rgba(255, 255, 255, 0.1), inset 2px 2px 6px rgba(0, 0, 0, 0.8);
}
.form .input .inputBox input::placeholder {
  color: #555;
  font-size: 14px;
}
.form .input .inputBox input[type=number] {
  -moz-appearance: textfield;
}
.form .input .inputBox .editPencil {
  color: #555;
  font-size: 18px;
  margin-left: -30px;
  margin-top: 5px;
}
.form .input .inputBox input[type="button"] {
  height: 40px!important;
  background: #E83500 !important;
  color: #fff !important;
  box-shadow: -2px -2px 6px rgba(255, 255, 255, 0.1), 2px 2px 6px rgba(0, 0, 0, 0.8);
}
.form .input .inputBox input[type="button"]:active {
  color: #006c9c;
  margin-top: 20px;
  box-shadow: inset -2px -2px 6px rgba(255, 255, 255, 0.1), inset 2px 2px 6px rgba(0, 0, 0, 0.8);
}
.form .input .inputBox input[type=number]::-webkit-inner-spin-button,
.form .input .inputBox input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}
#linkTestandLibraryPrepjoy {
  background: #E83500 !important;
  color: #fff !important;
  border: none;
}
.userProfile > div {
  padding: 15px !important;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper {
  background-color: transparent !important;
}
.linkWrapper {
  display: flex;
}
.linkWrapper #playstr,
.linkWrapper #appstr {
  width: 200px;
  color: #fff;
  border-radius: 5px;
  border: 1px solid #E83500;
  padding: 5px;
  transition: all 0.3s ease;
}
@media screen and (max-width: 900px) {
  .linkWrapper #playstr,
  .linkWrapper #appstr {
    padding: 0;
  }
}
.linkWrapper #playstr:hover,
.linkWrapper #appstr:hover {
  color: #E83500;
  border-color: #9999;
}
@media screen and (max-width: 900px) {
  .linkWrapper #playstr .dwnp,
  .linkWrapper #appstr .dwnp {
    font-size: 12px !important;
  }
}
@media screen and (max-width: 900px) {
  .linkWrapper #playstr .dwnh,
  .linkWrapper #appstr .dwnh {
    font-size: 14px !important;
  }
}
@media screen and (max-width: 900px) {
  .browse-wrapper {
    padding: 0!important;
  }
}
#bookNewDescription {
  margin-top: 100px;
}
#contPurchase {
  background: #E83500 !important;
  border: 1px solid #E83500 !important;
  color: #fff;
}
.ebooks {
  min-height: 650px;
}
@media screen and (max-width: 990px) {
  .header-wrapper {
    flex-direction: column;
  }
  .header-wrapper__navbar {
    margin-left: 0;
  }
}
@media screen and (max-width: 900px) {
  .navbar {
    padding: 0.5rem 0 !important;
  }
}
.mic {
  cursor: pointer!important;
}
.ldbar {
  /*width:500px;*/
  margin: 0 auto;
  border-radius: 10px;
  border: 4px solid transparent;
  position: relative;
  padding: 1px;
  top: -30px;
}
.ldbar:before {
  content: '';
  /*border:1px solid #fff;*/
  border-radius: 10px;
  position: absolute;
  top: -4px;
  right: -4px;
  bottom: -4px;
  left: -4px;
}
.ldbar .ldbardiv {
  position: absolute;
  border-radius: 10px;
  top: 0;
  right: 100%;
  bottom: 0;
  left: 0;
  background: red;
  width: 0;
  animation: borealisBar 1s linear infinite;
}
@keyframes borealisBar {
  0% {
    left: 0%;
    right: 100%;
    width: 0%;
  }
  10% {
    left: 0%;
    right: 75%;
    width: 25%;
  }
  90% {
    right: 0%;
    left: 75%;
    width: 25%;
  }
  100% {
    left: 100%;
    right: 0%;
    width: 0%;
  }
}
#salesData {
  color: #fff !important;
}
.content-wrapper h6 {
  color: #fff;
}
.btn-primary-modifier {
  background: #E83500 !important;
  color: #fff !important;
}
.badge-overlay {
  position: absolute;
  left: -15px;
  top: -15px;
  width: 40px;
  height: 40px;
  z-index: 100;
  -webkit-transition: width 1s ease, height 1s ease;
  -moz-transition: width 1s ease, height 1s ease;
  -o-transition: width 1s ease, height 1s ease;
  transition: width 0.4s ease, height 0.4s ease;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .badge-overlay {
    left: -10px;
    top: -10px;
  }
}
.top-left {
  position: absolute;
  top: 0;
  left: 0;
  -ms-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -webkit-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -ms-transform-origin: top right;
  -webkit-transform-origin: top right;
  transform-origin: top right;
}
.badge {
  margin: 0;
  padding: 5px;
  color: white;
  line-height: 1;
  background: #01b901;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: pre-wrap;
}
.percentageOff {
  background: #01b901;
  padding: 2px;
  width: 100px;
  text-align: center;
  color: #fff;
  border-radius: 2px;
}
.star-rating i {
  color: #F79420;
}
.shimmer {
  background-repeat: no-repeat;
  animation: shimmer 2.5s infinite;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.notificationContainer {
  padding-bottom: 2rem;
}
.notificationContainer #notificationForm label {
  color: #fff !important;
}
.notificationContainer #notificationForm input {
  height: auto !important;
}
.notificationContainer #notificationForm #btnSend {
  width: 200px;
  background: #E83500;
  color: #fff;
  border: 1px solid #E83500;
  display: block;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .notificationContainer #notificationForm #btnSend {
    width: 100%;
  }
}
.list_price {
  color: #FF4B33;
}
.offer_price {
  color: #212121;
}
#buyNow {
  width: 100%;
}
.ebook_detail .book_info .book_variants a.card .card-body {
  min-height: 130px;
}
.ebooks .global-search button {
  position: relative;
  z-index: 10;
  width: 48px;
  height: 48px;
  margin-left: -48px;
  color: #000000b3 !important;
}
.global-search {
  display: none !important;
  width: 96%;
  margin: 0 auto;
}
#search-book-store {
  height: 40px !important;
}
.ebooks .global-search button {
  background: #E83500 !important;
  opacity: 1;
  height: 40px;
}
.affiliationPrices {
  margin-top: 10px;
  width: fit-content;
  background: #fff;
  padding: 15px;
  border-radius: 10px;
}
.affiliationPrices_title p {
  font-weight: 500;
  color: #000 !important;
}
.affiliationPrices .affiliationLinks {
  display: flex;
  gap: 1rem;
  margin-top: 10px;
}
.affiliationPrices .affiliationLinks .fieldSet {
  position: relative;
  border: 1px solid orange;
  padding: 8px;
  width: 140px;
  border-radius: 5px;
}
.affiliationPrices .affiliationLinks .fieldSet:hover {
  border: 1px solid orange;
}
.affiliationPrices .affiliationLinks .fieldSet span img {
  width: 60px;
}
.affiliationPrices .affiliationLinks .fieldSet span img.flipkartLogo {
  width: 70px;
  position: relative;
  z-index: 2;
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend {
  position: absolute;
  top: 0;
  margin: -9px 0 0 -0.5rem;
  background: #fff;
  margin-left: 65px;
  width: 55px;
  text-align: center;
  z-index: 1;
  font-size: 18px;
  font-weight: 500;
  color: #000 !important;
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend span {
  margin-left: -4px;
}
.affiliationLinks .fieldSet:hover {
  background: #FFF0CC;
}
.affiliationLinks .fieldSet:hover .fieldSet_legend {
  background: linear-gradient(to bottom, #fff, #FFF0CC);
}
.affiliationLinksLoader {
  border: 1px solid #0003;
  width: 140px;
  height: 50px;
  border-radius: 5px;
  background: linear-gradient(to right, #F6F6F6 8%, #F0F0F0 18%, #F6F6F6 33%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiliationLinksLoader p {
  font-size: 12px;
  font-weight: 500;
  color: #0008 !important;
  text-align: center;
  line-height: initial;
}
.aa:after {
  position: absolute;
  margin-left: 0.1rem;
  content: ' ...';
  animation: loading steps(4) 2s infinite;
  clip: rect(auto, 0px, auto, auto);
}
.badge-info {
  display: initial;
  border-radius: 50px;
  font-size: 10px;
  padding: 3px 5px;
  font-weight: normal;
  background: #17a2b8;
  position: absolute;
  right: 1px;
  top: 1px;
  height: auto;
  width: 60px;
}
@keyframes loading {
  to {
    clip: rect(auto, 20px, auto, auto);
  }
}
#addedIconAnimation {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 20px auto;
}
#addedIconAnimation:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #b3eecc;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
#addedIconAnimation:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #27AE60;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}
.checkmark {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: #FFFFFF;
  stroke-miterlimit: 10;
  margin: 0;
  box-shadow: inset 0 0 0 #27AE60;
  animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
  position: absolute;
  z-index: 2;
  right: 0;
  left: 0;
}
.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}
.f-modal-alert .f-modal-icon {
  border-radius: 50%;
  border: 3px solid transparent;
  box-sizing: content-box;
  height: 56px;
  margin: 20px auto;
  padding: 0;
  position: relative;
  width: 56px;
}
.f-modal-alert .f-modal-icon.f-modal-warning {
  border-color: #F8BB86;
}
.f-modal-alert .f-modal-icon.f-modal-warning:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #ffd9b9;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
.f-modal-alert .f-modal-icon.f-modal-warning:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.f-modal-alert .f-modal-icon.f-modal-warning .f-modal-body {
  background-color: #F8BB86;
  border-radius: 2px;
  height: 26px;
  left: 50%;
  margin-left: -1px;
  position: absolute;
  top: 10px;
  width: 4px;
  z-index: 2;
}
.f-modal-alert .f-modal-icon.f-modal-warning .f-modal-dot {
  background-color: #F8BB86;
  border-radius: 50%;
  bottom: 10px;
  height: 6px;
  left: 50%;
  margin-left: -2px;
  position: absolute;
  width: 6px;
  z-index: 2;
}
.f-modal-alert .f-modal-icon + .f-modal-icon {
  margin-top: 50px;
}
.scaleAnimation {
  -webkit-animation: scaleAnimation 1s infinite alternate;
  animation: scaleAnimation 1s infinite alternate;
}
.pulseAnimationIns {
  -webkit-animation: pulseAnimationIns 0.75s infinite alternate;
  animation: pulseAnimationIns 0.75s infinite alternate;
}
.header-wrapper__navbar .mobile_cart_icon {
  position: relative;
}
.header-wrapper__navbar .cart_count {
  position: absolute;
  top: -10px;
  left: 10px;
  right: -10px;
  width: 17px;
  height: 18px;
  text-align: center;
  background: #FFFFFF;
  color: #000;
  border-radius: 50px;
  font-size: 11px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  line-height: normal;
  font-weight: 500;
}
@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
@keyframes fill {
  100% {
    box-shadow: inset 0 0 0 30px #27AE60;
  }
}
@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes pulseWarning {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulseAnimationIns {
  0% {
    background-color: #F8D486;
  }
  100% {
    background-color: #F8BB86;
  }
}
#cartModalBtns .btn-primary {
  background: #E83500 !important;
}
.prepjoy_cta {
  background: #000;
  position: relative;
  margin-top: 3rem;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta {
    padding: 2.5rem 1.5rem 3rem;
  }
}
.prepjoy_cta .cta_inner {
  border: 1px solid #E83500;
  padding: 20px;
  border-radius: 20px;
  box-shadow: 0 2px 4px #000;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_info {
    width: 100%;
  }
}
.prepjoy_cta .cta_info h3 {
  color: white;
  font-weight: 100;
  line-height: 35px;
}
.prepjoy_cta .cta_info h3 span {
  font-weight: 600;
}
.prepjoy_cta .cta_info h4 {
  font-weight: normal;
}
.prepjoy_cta .cta_info h4 span {
  font-size: 15px;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_info h4 span {
    padding: 5px 0;
    display: inline-block;
  }
}
.prepjoy_cta .cta_info h4 a {
  color: #E83500 !important;
}
.prepjoy_cta .cta_info h4 a:hover {
  opacity: 0.7;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_btn {
    width: 100%;
  }
}
.prepjoy_cta .cta_btn p {
  color: white;
}
.prepjoy_cta .cta_btn a {
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background-color: #E83500;
  border-color: #E83500;
  border-radius: 7px;
  box-shadow: 0 2px 4px #444;
  -webkit-box-shadow: 0 2px 4px #444;
  -moz-box-shadow: 0 2px 4px #444;
}
.prepjoy_cta .cta_btn a:hover,
.prepjoy_cta .cta_btn a:active,
.prepjoy_cta .cta_btn a:focus,
.prepjoy_cta .cta_btn a:active:focus {
  background-color: #E83500 !important;
  border-color: #E83500 !important;
  box-shadow: none !important;
  outline: 0 !important;
}
@media screen and (max-width: 767px) {
  .prepjoy_cta .cta_btn a {
    margin-top: 10px !important;
    margin-bottom: 10px !important;
  }
}
.prepjoy-footer {
  background: #0F0839;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 999;
}
.prepjoy-footer__socialIcons {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}
.prepjoy-footer__socialIcons ul {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 0;
}
.prepjoy-footer__socialIcons ul li {
  list-style: none;
  font-size: 1.4rem;
  margin-right: 20px;
}
.prepjoy-footer__socialIcons ul li:nth-child(4) {
  margin-right: 0;
}
.prepjoy-footer__socialIcons ul li i {
  color: #fff;
}
body {
  background: #04001D !important;
  font-family: Righteous !important;
  overflow-x: hidden !important;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #FFFFFF !important;
}
p,
a {
  color: #FFFFFF !important;
}
.ebook_detail {
  background: #04001D !important;
}
.ebooks .ebooks_filter {
  background: transparent !important;
  box-shadow: 0 3px 10px 0 rgba(31, 38, 135, 0.37) !important;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
  width: 100%  !important;
}
.books-list {
  background: #04001D !important;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
  border-radius: 10px !important;
}
.books-list .image-wrapper {
  background: transparent !important;
  box-shadow: 0 3px 40px 0 rgba(31, 38, 135, 0.37) !important;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
  border-radius: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
}
#filters h5 {
  color: #fff !important;
}
#filters #resetFilter {
  color: #fff !important;
}
.book_info {
  color: #fff !important;
}
.book_info h2 {
  color: #fff !important;
}
#buyNow,
#okBuy,
#linkLibrary,
#linkOthers,
#addtoLibrary {
  background-color: #E83500 !important;
  color: #fff !important;
  border: 1px solid #E83500 !important;
  transition: all 0.2s ease;
  box-shadow: 2px 2px 2px rgba(255, 255, 255, 0.25), -2px -2px 2px rgba(255, 255, 255, 0.25) !important;
}
#buyNow:hover,
#okBuy:hover,
#linkLibrary:hover,
#linkOthers:hover,
#addtoLibrary:hover {
  transform: scale(1.06);
}
#buyNow:active,
#okBuy:active,
#linkLibrary:active,
#linkOthers:active,
#addtoLibrary:active {
  transform: scale(1);
}
#continue {
  background-color: #E83500 !important;
  color: #fff !important;
  border: 1px solid #E83500 !important;
  transition: all 0.2s ease;
}
#continue:hover {
  transform: scale(1.06);
}
#continue:active {
  transform: scale(1);
}
.ebook_detail .nav-tabs-book-details ul.nav li a.active {
  color: #6C757D !important;
  border: 1px solid #888 !important;
}
.ebook_detail .nav-tabs-book-details .wrp-details-about-cart {
  color: #fff !important;
}
.tab-pane {
  color: #fff !important;
}
.tab-pane h5 {
  color: #fff !important;
}
.orders {
  background: #0F0839 !important;
}
.orders img {
  width: 100px !important;
}
.users-orders > p {
  font-size: 12px;
  border-bottom: 1px solid #666;
  padding-bottom: 0.5rem;
}
.payment-details {
  margin-top: 10px !important;
}
.payment-details span {
  color: #fff !important;
}
.media-body {
  margin-left: 20px !important;
}
.page_title h4 {
  color: #fff;
}
.my_books {
  min-height: 60vh;
}
.mdl-tabs.side-column .sidebar-background {
  background: #04001D !important;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 10px;
}
#relatedBooksContainer .bg-light,
#bestSellerBooksContainer .bg-light {
  background: #04001D !important;
}
.related-book-wrapper {
  background: transparent !important;
}
#htmlreadingcontent iframe {
  background: #FFFFFF !important;
}
.publishing_desk table tbody,
.publishing_desk .table tbody {
  color: #FFFFFF !important;
}
.publishing_desk .main {
  margin: 10px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}
.publishing_desk .nav-pills .nav-link.active {
  background-color: #E83500 !important;
  border-color: #E83500 !important;
}
.pub-desks .nav-tabs {
  border-bottom: none;
  padding-left: 15px;
}
.pub-desks .nav-link {
  border-color: rgba(255, 255, 255, 0.2);
  color: #FFFFFF;
}
.page-item.disabled .page-link {
  color: #6c757d !important;
}
.light11 {
  background: #E83500 !important;
  border-color: #E83500 !important;
}
.typeahead a {
  color: #000 !important;
}
.mdl-layout__tab,
.mdl-tabs__tab {
  color: #848484 !important;
}
.nav-tabs a {
  color: #848484;
}
.mdl-tabs.side-column .sidebar-background {
  margin-top: 70px;
}
.new_book_create .container {
  padding: 0!important;
  border: 1px solid rgba(255, 255, 255, 0.5) !important;
}
.new_book_create #pills-tab {
  padding-left: 0!important;
  background: transparent !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}
.new_book_create #pills-tab .nav-item {
  padding-left: 0!important;
  padding-right: 5px !important;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: #E83500;
}
.book_details_info div .btn-primary,
.book_details_info #nxt-btn,
.chapter-wrapper .btn-primary {
  background: #E83500 !important;
  border-color: #E83500 !important;
}
#addedtags,
#chapter-resources,
#goBack {
  color: #FFFFFF !important;
}
.chapter-wrapper {
  border: 1px solid rgba(255, 255, 255, 0.5);
  padding: 5px;
  border-radius: 4px;
  background: rgba(211, 232, 249, 0.5);
}
#chapterdetailsdetails .col-6 {
  display: flex;
  flex-direction: column;
}
#chapterdetailsdetails .col-6 a {
  text-decoration: underline !important;
  margin-bottom: 10px !important;
}
#chapter-resources tr td a.btn.btn-primary {
  background: #E83500 !important;
  border-color: #E83500 !important;
  color: #FFFFFF !important;
}
#chapter-resources a {
  color: #FFFFFF !important;
}
#published a,
#notpublished a,
.saveCard .saveSubmit {
  background: #E83500 !important;
  border-color: #E83500 !important;
  color: #FFFFFF !important;
  flex-direction: row !important;
}
.all-container .container-wrapper div > .media .title {
  color: #444444 !important;
}
.all-container .container-wrapper div > .media a {
  color: #000000b3 !important;
}
.app-header__questionsQue h1,
h2,
h3,
h4,
h5,
h6,
.question-wrapper p,
.mcq-answers,
#leaderBoardList p,
.leaderBoard__wrapper-navigations__card a .card__sec-1 p,
.leaderboard .calendar_wrapper #selectedCurrentDate a,
.noRanksMsg h4,
.noRanksMsg p {
  color: #000 !important;
}
.content-wrapper h6,
.purchase-details,
.leaderboard-sidebar h6,
#totalBooksCount {
  color: #fff !important;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book {
  border-left: none !important;
}
.leaderboard h4 strong {
  color: #FFFFFF !important;
}
.leaderboard-share {
  background-color: #E83500 !important;
}
.leaderboard .filter-wrapper a {
  color: #212121 !important;
}
.leaderboard .filter-wrapper a.active {
  color: #FFFFFF !important;
}
.leaderboard thead tr {
  background: transparent !important;
}
.cart_checkout .order_summary p,
.cartItems .book_desc p,
.cartItems .book_desc .book_title h5 a,
.cartItems .book_desc h5 {
  color: #000 !important;
}
.cartItems .book_desc .book_title h5 a {
  color: #000 !important;
}
.book_discount_btn {
  color: #6C757D !important;
}
.book_title h5 a {
  color: #6C757D !important;
}
.book_publisher,
.continue_shop_btn,
#discountPriceFinal,
.use_ebooks_text h5 {
  color: #6C757D !important;
}
.book_publisher a {
  color: #6C757D !important;
}
.delete_item a {
  color: #CE0000 !important;
}
#discountAnimation {
  z-index: -1 !important;
}
.books-list .content-wrapper .add_to_cart_btn {
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000b3 !important;
  margin: auto;
  font-size: 12px;
  border: 1px solid #000000b3;
  position: relative;
  padding: 3px;
  bottom: -3px;
}
.books-list .content-wrapper .add_to_cart_btn:hover {
  background: #E83500 !important;
  color: #FFFFFF !important;
  border-color: #E83500 !important;
}
.books-list .content-wrapper .add_to_cart_btn:hover img {
  filter: contrast(4) invert(1);
  -webkit-filter: contrast(4) invert(1);
}
.my_books .books-content-wrapper {
  padding: 15px;
}
.left-div a h6 {
  color: #212121 !important;
}
.left-div .book-publisher-name {
  color: #aeaeae !important;
}
.ebooks #content-data-books-ebooks .fadein-animated {
  background: #f4f4f4 !important;
}
.books-list .content-wrapper p.price {
  color: #212121 !important;
}
#orders-wrapper h3,
.mcq-name,
.shopping_cart h3,
.order_information h5,
.purchase-heading h3,
.purchase-details .instructions h5,
.ws_resultPage-title h2,
.ws_result-performance_title,
.daily__test-header__title .text-primary-modifier,
#backToall,
#backfromnotes,
#backfromAllnotes,
#relatedBooksContainer h5,
#bestSellerBooksContainer h5,
#relatedBooksContainer a,
#bestSellerBooksContainer a,
#revisionTitle,
#description,
.public-text,
.hero-title,
#wsResultPointsCount,
.my_books h4,
.leaderBoard__title h3,
.ws-next,
.ws-previous,
.flashcard-set a,
.flashcard-set p,
.mynotes h3,
.mynotes li,
.fav_title h2,
.fav_mcqsList h4,
#historyList h4,
.tests-access h6,
.ebook-access h6,
.read-book-chapters-wrapper li,
.ws-progressbar .ws-next i,
.ws-progressbar .ws-previous i,
#amazonBooksTitle h5,
#timer,
.backfromcard,
.suggested-videos h6,
.my-activity h3 strong,
.bookchapterHeader,
#resourceNameDiv,
.recommendedBooks-title,
#bronze,
#silver,
#gold {
  color: #FFFFFF !important;
}
.library_book,
#ebook_AddToCartBtn {
  color: #444 !important;
}
.library-book a {
  color: #000 !important;
}
.library-book a.btn-outline-primary:hover {
  color: #FFFFFF !important;
}
#cartModalLibBtn .btn-primary,
.save-card,
.my_books #libraryTabs a.current {
  background: #E83500 !important;
}
.review-wrap .text-quote,
.fav_mcqs-card_question p,
.fav_mcqs-card_option p,
.fav_mcqs-card_option,
.pointsSummary p,
.mobile_summary p,
.mobile_view_summary a,
.que-header,
#add-createTest input,
.book_variants a.btn-light,
.preview-book-btns .buy p,
.flip-box-back p,
.inner-container-show-1 p,
.que-options p,
.flashcard-set p,
.flashcard-set h4,
#secondUserDetail p,
#firstUserDetail p,
#thirdUserDetail p,
.complte-book p,
.unlock-info h5,
.unlock-info p,
#startMatch h3,
#startMatch p,
.playmatch p,
.bootstrap-select p,
.bootstrap-select a,
.video-wrapper p,
#htmlContent .ptyyy p,
#expandFlashcard p,
#expandFlashcard,
#finishMatch p,
#finishMatch h2,
.ck-editor__editable p,
#add-createTest #htmlContent,
.show-explanation p,
.show-explanation h5,
.show-explanation h2 {
  color: #000 !important;
}
.review-wrap .box p {
  color: #000 !important;
}
.book-cart-modal .book_variants .card-body {
  min-height: 105px;
}
#videoModal .modal-dialog {
  margin-top: 230px;
}
.addwebmcq,
#add-createTest,
#htmlContent {
  background: #fff !important;
}
.addwebmcq p {
  color: #000 !important;
}
.an-header {
  background: transparent !important;
}
.slider-actions button:focus span,
.slider-actions button:hover span,
.slider-actions button:focus i,
.slider-actions button:hover i {
  color: #FFFFFF !important;
}
.ws-input input:focus ~ label.ws-label {
  color: #FFFFFF !important;
}
#successModal .modal-body p,
#successModal .modal-body p #msg-updated {
  color: #27AE60 !important;
}
#htmlreadingcontent .btn-back {
  background: #FFFFFF !important;
}
.left-div h6 {
  color: #000 !important;
}
#webMcq {
  overflow: scroll;
}
.flDescription {
  color: #fff !important;
}
.mdl-tabs.side-column .sidebar-background {
  overflow: scroll;
}
.mcqRead h4,
.mcqRead h5,
.mcqRead p,
.mcqRead li {
  color: #000 !important;
}
.inner-ques p,
.inner-ques ul li,
.inner-ques h5 {
  color: #000 !important;
}
.questionBlock p,
.questionBlock h5 {
  color: #000 !important;
}
.ptyyy i {
  color: #000 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .leaderboard-share {
    z-index: 9999;
  }
}
.print-books h1 {
  color: #0F0839 !important;
}
.print-books h4 {
  color: #FFF !important;
}
.print-books .category-lists a.btn {
  color: #0F0839 !important;
}
.print-books .category-lists a.btn:hover,
.print-books .category-lists a.btn:focus,
.print-books .category-lists a.btn:active,
.print-books .category-lists a.btn:active:focus {
  border-color: #ff6b41 !important;
  background-color: #ff9475 !important;
}
.print-books .topSchoolBooks h6 a {
  color: #000 !important;
}
.print-books .topSchoolBooks:hover h6 {
  color: #ff6b41 !important;
}
.print-books .topSchoolBooks:hover h6 a {
  color: #ff6b41 !important;
}
.print-books .topSchoolBooks .book-publisher-name a {
  color: #949494 !important;
}
.print-books #emptyState #emptyBox {
  margin: 0 auto 15px !important;
}
.print-books .books-list {
  padding: 0;
}
.print-books .breadcrumb li.breadcrumb-item a:hover {
  color: #E83500 !important;
}
.amazonPrice p,
.flipkartPrice p {
  color: #0F0839 !important;
}
.customerReviews .card a,
.customerReviews .card p {
  color: #0F0839 !important;
}
.subscription__sec label {
  color: #000;
}
.book_briefDetail h2 {
  color: #000 !important;
}
.book_briefDetail .book-publisher-name {
  color: black !important;
}
.book_briefDetail .book-publisher-name a {
  color: #007bff !important;
}
.freeChapterLink {
  color: #000 !important;
}
.shippingAdressWrapper h3 {
  color: inherit !important;
}
.shippingAdressWrapper a {
  color: #007bff !important;
}
.page-main-wrapper.information {
  background: #fff !important;
}
#goBack,
.showmoretab .info-description p,
#contents .info-description p {
  color: #000 !important;
}
.created-date,
.emptyContent {
  color: #999 !important;
}
.show-info-details,
.next-info-link {
  color: blue !important;
}
.information #contents .info-title {
  padding-top: 10px !important;
  color: #212121 !important;
}
.next-info-link a,
.prev-info-link a {
  color: blue !important;
}
.information #contents .info-description a {
  color: blue !important;
}
#batchUsers table tr td {
  color: #fff !important;
}
#section1Container table tr td,
#section2Container table tr td,
#section3Container table tr td,
#section4Container table tr td,
label {
  color: #fff !important;
}
.quizCreatorBulkInput .main .radioQuestion p,
label.checkbox {
  color: #000 !important;
}
#salesData_paginate ul li a {
  color: #000 !important;
}
.mockTestListItem,
.leaderBoardTitle,
#infoSec *,
.mockTests__currentMonth-title,
.allMockTests h5,
.suggestedBooksTitle,
.showMoreText,
.currentMonthItemWrapper h3,
.openTestBtn {
  color: #fff !important;
}
.accordion-item button p,
.accordion-content p,
.examCard__examName p,
.examActionItem .card-title p {
  color: #000 !important;
}
.quizInfo p {
  color: rgba(0, 0, 0, 0.3) !important;
}
.currentMonthItem {
  box-shadow: 0 2px 2px rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.44) !important;
}
.booksList__items-item {
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
}
.print-books .page_title h1 {
  position: absolute;
  top: 50%;
  left: 10%;
  width: 320px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
}
.print-books .page_title h1 small {
  opacity: 1;
  font-size: 70%;
  font-weight: bold;
}
.print-books .page_title h1 strong {
  font-size: 60px;
  text-transform: uppercase;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .print-books .page_title h1 {
    font-size: 40px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .print-books .page_title h1 {
    width: 220px;
    font-size: 26px;
  }
  .print-books .page_title h1 strong {
    font-size: 30px;
  }
}
.print-books .page_title img {
  width: 100%;
}
.print-books .category-lists {
  -webkit-column-count: 4;
  -moz-column-count: 4;
  column-count: 4;
  -webkit-column-gap: 0.5rem;
  -moz-column-gap: 0.5rem;
  column-gap: 0.5rem;
  orphans: 1;
  widows: 1;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .print-books .category-lists {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 0.3rem;
    -moz-column-gap: 0.3rem;
    column-gap: 0.3rem;
  }
}
.print-books .category-lists a.btn {
  border-color: transparent;
  border-radius: 7px;
  background-color: #FFFFFF;
  text-align: center;
  padding: 0.5rem 0.75rem;
  margin: 0 0 0.5rem;
  color: #212121;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .print-books .category-lists a.btn {
    margin: 0 0 0.3rem;
  }
}
.print-books .category-lists a.btn:hover,
.print-books .category-lists a.btn:focus,
.print-books .category-lists a.btn:active,
.print-books .category-lists a.btn:active:focus {
  border-color: #FFE29C;
  background-color: #FFF0CC;
}
.print-books .ebook_detail .page-row-filter .breadcrumb {
  border-bottom-style: dashed;
  border-color: #DDD;
}
.print-books .ebook_detail .page-row-filter .breadcrumb li.breadcrumb-item a:hover {
  color: #F79420;
}
.print-books #emptyState {
  padding: 1.5rem 1.5rem 2.5rem;
  border-radius: 20px;
}
.print-books #emptyState #emptyBox {
  width: 250px;
  height: 250px;
  position: relative;
  margin: 0 auto -15px;
}
.print-books .topSchoolBooks img {
  transition: all 0.2s ease-out;
  -webkit-transition: all 0.2s ease-out;
  -moz-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
}
.print-books .topSchoolBooks h6 a {
  color: #212121;
}
.print-books .topSchoolBooks:hover img {
  transform: scale(1.05);
  -ms-transform: scale(1.05);
  transition: all 0.2s ease-in;
  -webkit-transition: all 0.2s ease-in;
  -moz-transition: all 0.2s ease-in;
  -o-transition: all 0.2s ease-in;
}
.print-books .topSchoolBooks:hover h6 {
  color: #F79420;
}
.print-books .topSchoolBooks:hover h6 a {
  color: #F79420;
}
@media only screen and (max-width: 760px), (min-device-width: 768px) and (max-device-width: 1024px) {
  /* Hide table headers (but not display: none;, for accessibility) */
  #viewuser thead tr,
  #viewuserstd thead tr,
  #viewAccesscode thead tr,
  #viewBook thead tr,
  #pubTable thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  #batchUsers .batchusers tbody tr:first-child,
  #batchUsers .managebooks tbody tr:first-child {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }
  #viewuser tr:nth-child(odd),
  #viewuserstd tr:nth-child(odd),
  #viewAccesscode tr:nth-child(odd),
  #viewBook tr:nth-child(odd),
  #pubTable tr:nth-child(odd),
  #batchUsers .batchusers tr:nth-child(odd),
  #batchUsers .managebooks tr:nth-child(odd) {
    background: #ccc;
  }
  #viewuser td,
  #viewuserstd td,
  #viewAccesscode td,
  #viewBook td,
  #pubTable td,
  #batchUsers .batchusers td,
  #batchUsers .managebooks td {
    /* Behave  like a "row" */
    border: none;
    border-bottom: 1px solid #eee;
    position: relative;
  }
  #viewuser td:before,
  #viewuserstd td:before,
  #viewAccesscode td:before,
  #viewBook td:before,
  #pubTable td:before,
  #batchUsers .batchusers td:before,
  #batchUsers .managebooks td:before {
    /* Now like a table header */
    position: absolute;
    /* Top/left values mimic padding */
    top: 25%;
    left: 6px;
    width: 100%;
    padding-right: 10px;
    white-space: nowrap;
  }
  /*
    Label the data for managebooks
  You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
    */
  #batchUsers .managebooks td:nth-of-type(1):before {
    content: "Name";
  }
  #batchUsers .managebooks td:nth-of-type(2):before {
    content: "Username";
  }
  #batchUsers .managebooks td:nth-of-type(3):before {
    content: "Email";
  }
  #batchUsers .managebooks td:nth-of-type(4):before {
    content: "Mobile";
  }
  /*
  /*
   Label the data for batchUsers
 You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
   */
  #batchUsers .batchusers td:nth-of-type(1):before {
    content: "Book Id";
  }
  #batchUsers .batchusers td:nth-of-type(2):before {
    content: "Username";
  }
  #batchUsers .batchusers td:nth-of-type(3):before {
    content: "Expiry Date";
  }
  #batchUsers .batchusers td:nth-of-type(4):before {
    content: "Date Added";
  }
  /*
  /*
  Label the data for viewuserstd
You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
  */
  #viewuserstd td:nth-of-type(1):before {
    content: "Name";
  }
  #viewuserstd td:nth-of-type(2):before {
    content: "Email";
  }
  #viewuserstd td:nth-of-type(3):before {
    content: "Mobile";
  }
  #viewuserstd td:nth-of-type(4):before {
    content: "Admission No.";
  }
  #viewuserstd td:nth-of-type(5):before {
    content: "Delete";
  }
  /*
   Label the data for viewuser
You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
  */
  #viewuser td:nth-of-type(1):before {
    content: "Name";
  }
  #viewuser td:nth-of-type(2):before {
    content: "Email";
  }
  #viewuser td:nth-of-type(3):before {
    content: "Mobile";
  }
  #viewuser td:nth-of-type(4):before {
    content: "Delete";
  }
  /*
  Label the data for viewAcesscode
You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
  */
  #viewAccesscode td:nth-of-type(1):before {
    content: "Serial No.";
  }
  #viewAccesscode td:nth-of-type(2):before {
    content: "Access Code";
  }
  #viewAccesscode td:nth-of-type(3):before {
    content: "Status";
  }
  #viewAccesscode td:nth-of-type(4):before {
    content: "Username";
  }
  #viewAccesscode td:nth-of-type(5):before {
    content: "Date Created";
  }
  #viewAccesscode td:nth-of-type(6):before {
    content: "Date Redeemed";
  }
  /*
      Label the data for viewAcesscode
  You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
      */
  #viewBook td:nth-of-type(1):before {
    content: "Book Id";
  }
  #viewBook td:nth-of-type(2):before {
    content: "Title";
  }
  #viewBook td:nth-of-type(3):before {
    content: "Isbn";
  }
  #viewBook td:nth-of-type(4):before {
    content: "Publisher";
  }
  #viewBook td:nth-of-type(5):before {
    content: "Status";
  }
  #viewBook td:nth-of-type(6):before {
    content: "Number of copies";
  }
  #viewBook td:nth-of-type(7):before {
    content: "Validity (in days)";
  }
  button#toggle {
    position: absolute;
    left: 0;
    border-radius: 50%;
    background: #F79420;
    border: 1px solid white;
  }
  #viewuser tbody,
  #viewuserstd tbody,
  #viewAccesscode tbody,
  #viewBook tbody,
  #pubTable tbody,
  #batchUsers .batchusers tbody,
  #batchUsers .managebooks tbody {
    display: block !important;
  }
  #viewuser td:before,
  #viewuserstd td:before,
  #viewAccesscode td:before,
  #viewBook td:before,
  #pubTable td:before,
  #batchUsers .batchusers td:before,
  #batchUsers .managebooks td:before {
    text-align: left;
  }
  #viewuser td,
  #viewuserstd td,
  #viewAccesscode td,
  #viewBook td,
  #pubTable td,
  #batchUsers .batchusers td,
  #batchUsers .managebooks td {
    text-align: right !important;
    height: 30px;
    padding-left: 40%;
  }
  #batchUsers .batchusers td,
  #batchUsers .managebooks td {
    height: 40px !important;
  }
  #viewuser td:before,
  #viewuserstd td:before,
  #viewAccesscode td:before,
  #viewBook td:before,
  #pubTable td:before,
  #batchUsers .batchusers td:before,
  #batchUsers .managebooks td:before {
    padding-left: 6%;
  }
  #viewuser button.btn.btn-primary.mx-2,
  #viewuserstd button.btn.btn-primary.mx-2,
  #viewAccesscode button.btn.btn-primary.mx-2,
  #viewBook button.btn.btn-primary.mx-2,
  #batchUsers .batchusers button.btn.btn-primary.mx-2,
  #batchUsers .managebooks button.btn.btn-primary.mx-2 {
    margin-left: 1% !important;
  }
  #viewuserstd .admissionbox {
    width: 35%;
  }
  #viewuser input#admissionNoBox0,
  #viewAccesscode input#admissionNoBox0,
  #viewBook input#admissionNoBox0,
  #batchUsers .batchusers input#admissionNoBox0,
  #batchUsers .managebooks input#admissionNoBox0 {
    margin-right: 0px !important;
    width: 35%;
  }
  #viewuser tbody td,
  #viewuserstd tbody td,
  #viewAccesscode tbody td,
  #viewBook tbody td,
  #pubTable tbody td,
  #batchUsers .batchusers tbody td,
  #batchUsers .managebooks tbody td {
    display: none;
  }
  #viewuser tbody td:first-child,
  #viewuserstd tbody td:first-child,
  #viewAccesscode tbody td:first-child,
  #viewBook tbody td:first-child,
  #pubTable tbody td:first-child,
  #batchUsers .batchusers tbody td:first-child,
  #batchUsers .managebooks tbody td:first-child {
    display: block;
  }
}
@media (min-width: 576px) {
  button#toggle {
    display: none !important;
  }
}
@media (max-width: 575px) {
  ul.pagination .page-link {
    padding: 5px !important;
  }
}
