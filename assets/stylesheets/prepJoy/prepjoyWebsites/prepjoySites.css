.prepjoySites {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 70vh;
  margin: 0 auto;
}
@media screen and (max-width: 820px) {
  .prepjoySites {
    height: 73vh !important;
  }
}
@media screen and (max-width: 767px) {
  .prepjoySites {
    height: 70vh !important;
  }
}
@media screen and (max-width: 415px) {
  .prepjoySites {
    height: 73vh !important;
  }
}
.prepjoySites-wrapper {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  box-shadow: 0 3px 4px 0 rgba(31, 38, 135, 0.17) !important;
  backdrop-filter: blur(2px) !important;
  -webkit-backdrop-filter: blur(2px) !important;
  border-radius: 5px !important;
  border: 1px solid rgba(255, 255, 255, 0.18) !important;
  height: 80%;
}
.prepjoySites-wrapper__itemOne {
  margin-top: auto;
}
.prepjoySites-wrapper__itemOne img {
  width: 150px;
}
.prepjoySites-wrapper__itemOne h2 {
  font-size: 33px;
}
.prepjoySites-wrapper__itemOne p {
  font-size: 1.2rem;
  color: #9999 !important;
}
@media screen and (max-width: 767px) {
  .prepjoySites-wrapper__itemOne h2 {
    font-size: 21px;
  }
  .prepjoySites-wrapper__itemOne img {
    width: 100px;
  }
}
.prepjoySites-wrapper__itemTwo {
  margin-top: 2rem;
}
.prepjoySites-wrapper__itemTwo a {
  padding: 3px;
  border: 1px solid #9999;
  box-sizing: border-box;
  width: 150px;
  border-radius: 5px;
  text-align: center;
}
.prepjoySites-wrapper__itemTwo a img {
  margin-right: 3px;
}
.prepjoySites-wrapper__itemTwo a p {
  font-size: 10px !important;
}
.prepjoySites-wrapper__itemTwo a h4 {
  font-size: 12px !important;
}
.prepjoySites-wrapper__itemThree {
  margin-top: auto;
  margin-bottom: auto;
}
.prepjoySites-wrapper__itemThree a {
  width: 300px;
  color: #fff;
  border-radius: 5px;
  border: 1px solid #E83500;
  padding: 1rem;
  transition: all 0.3s ease;
}
.prepjoySites-wrapper__itemThree a:hover {
  color: #E83500;
  border-color: #9999;
}
