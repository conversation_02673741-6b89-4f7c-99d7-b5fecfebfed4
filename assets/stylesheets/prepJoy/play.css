.gamer-profile {
  min-height: 100vh;
}
.gamer-profile .media-body p {
  color: #212121;
}
.gamer-profile .media-body h5 {
  color: #212121;
}
.gamer-profile .media {
  height: 50vh;
}
.gamer-profile .media img {
  border: 3px solid #fff;
}
.gamer-profile .media h5,
.gamer-profile .media p {
  padding: 0;
  margin: 0;
}
.gamer-profile .media p {
  font-size: 10px;
}
.gamer-profile .media:first-child {
  padding: 6rem 2rem;
}
.gamer-profile .media.botProfile:last-child {
  border: none;
  padding: 6rem 2rem;
}
.gamer-profile .media.botProfile:last-child h5,
.gamer-profile .media.botProfile:last-child p {
  text-align: right;
}
.circle-wrapper {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 999;
}
.circle-wrapper .roundbg {
  background: url("../../images/prepJoy/roundbg.svg") center no-repeat;
  background-size: contain;
  width: 80px;
  height: 100%;
  margin: 40px auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.circle-wrapper .roundbg img {
  width: 45px;
}
.locate {
  color: #212121;
  font-size: 18px;
  margin-bottom: 1rem;
}
.playerbg {
  background: url("../../images/prepJoy/playerbg.svg") center no-repeat;
  background-size: cover;
  background-color: #E83500;
}
.bot-anim-wrapper {
  display: flex;
}
.bot-anim {
  background: url("../../images/prepJoy/botanim.svg") center no-repeat;
  height: 50px;
  width: 50px;
  animation: mymove 4s infinite;
  position: relative;
}
@keyframes mymove {
  0% {
    left: -50px;
    top: 0px;
  }
  25% {
    left: 300px;
    top: 0px;
  }
  50% {
    left: -50px;
    top: 0px;
  }
  75% {
    left: 300px;
    top: 0px;
  }
  100% {
    left: -50px;
    top: 0px;
  }
}
.botbg {
  background: url("../../images/prepJoy/botbg.svg") center no-repeat;
  background-size: cover;
}
.botselect {
  height: 45vh;
  display: flex;
  align-items: center;
}
.no-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
#no-counter {
  color: #212121;
}
#readyGo {
  position: relative;
}
@-webkit-keyframes count {
  0% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}
.nums {
  font-size: 5rem;
  height: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  text-align: center;
}
.three {
  -webkit-animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
  animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
}
.two {
  -webkit-animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
  animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
}
.one {
  -webkit-animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
  animation: count 0.1s cubic-bezier(0.1, 0.1, 1, 1) 1;
}
.quiz-profile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 0.5rem;
  padding-top: 1rem;
}
.quiz-profile .media img {
  border: 2px solid #fff;
}
.quiz-profile .username {
  font-size: 12px;
  color: #212121;
  margin-bottom: 0;
}
.quiz-profile .score {
  font-size: 12px;
  color: #212121;
}
.main-timer {
  text-align: center;
}
.main-timer h4 {
  font-size: 10px;
  text-transform: uppercase;
  color: #2980b9;
  margin-bottom: 0px;
}
.main-timer p {
  font-size: 16px;
  color: #3498db;
  font-weight: 500;
}
.base-timer {
  position: relative;
  width: 40px;
  height: 40px;
}
.base-timer__svg {
  transform: scaleX(-1);
}
.base-timer__circle {
  fill: none;
  stroke: none;
}
.base-timer__path-elapsed {
  stroke-width: 7px;
  stroke: grey;
}
.base-timer__path-remaining {
  stroke-width: 7px;
  stroke-linecap: round;
  transform: rotate(90deg);
  transform-origin: center;
  transition: 1s linear all;
  fill-rule: nonzero;
  stroke: currentColor;
}
.base-timer__path-remaining.green {
  color: #41b883;
}
.base-timer__path-remaining.orange {
  color: orange;
}
.base-timer__path-remaining.red {
  color: red;
}
.base-timer__label {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 0;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: none;
}
.time-wrapper {
  width: 40px;
  height: 40px;
  position: relative;
  margin: 5px auto;
}
#countdown {
  position: absolute;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  display: flex;
  top: 0;
}
#user-status,
.user-status {
  color: #E83500;
  font-family: 'Righteous', cursive;
  font-size: 26px;
  text-align: center;
}
.playScoreCard {
  border: 1px dashed;
  padding: 0.5rem 2rem;
  border-radius: 10px;
}
.playScoreCard__text {
  font-size: 16px;
}
.playScoreCard__points {
  margin-bottom: 0;
  text-align: center;
  font-size: 36px;
}
.result-status-text {
  font-size: 16px;
}
.progress {
  position: absolute;
  top: 0;
  width: 100%;
  border-radius: 0;
  height: 100% !important;
}
.progress .progress-bar {
  background-color: #3498db;
}
.progress-bar-vertical {
  width: 8px;
  height: 100%;
  display: flex;
  align-items: flex-end;
  position: absolute;
  bottom: 0;
  top: initial;
  border-radius: 4px ;
  background: #060029;
}
.progress-bar-vertical.player1 {
  left: 0;
}
.progress-bar-vertical.player2 {
  right: 0;
}
.progress-bar-vertical.progress-correct {
  background: rgba(66, 181, 56, 0.5);
}
.progress-bar-vertical.progress-incorrect {
  background: rgba(218, 1, 1, 0.5);
}
.progress-bar-vertical .progress-bar {
  width: 100%;
  height: 0;
  -webkit-transition: height 0.6s ease;
  -o-transition: height 0.6s ease;
  transition: height 0.6s ease;
}
.gameTimerSelection select {
  border: 1px solid #c8c5c5;
  border-radius: 2px;
  font-size: 12px;
}
.gameSoundToggle {
  position: relative;
}
.gameSoundToggle input {
  visibility: hidden;
}
.gameSoundToggle .gameSoundToggleBtn {
  width: 50px;
  padding: 2px;
  border-radius: 100px;
  margin-bottom: 0 !important;
  cursor: pointer;
}
.gameSoundToggle .gameSoundToggleBtn .gameSoundToggleBtnBall {
  background: red;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  transition: all 0.3s ease-in;
}
.gameSoundToggle input:checked ~ .gameSoundToggleBtn .gameSoundToggleBtnBall {
  transform: translateX(29px);
  background: #00c600;
}
.medalWinningText {
  margin-top: -450px;
}
@media (max-width: 768px) {
  .medalWinningText {
    margin-top: -250px;
  }
}
.gameExit button {
  background: transparent;
  border: 1px solid #fff;
  border-radius: 5px;
  padding: 5px 1rem;
  color: #fff;
  margin-top: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}
@media (max-width: 768px) {
  .gameExit button {
    margin-right: 10px;
  }
}
.gameExit button:active {
  transform: scale(0.9);
  outline: none;
}
.gameExit button:focus {
  outline: none;
}
