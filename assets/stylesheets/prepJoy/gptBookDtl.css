.testimonial-section {
  background-color: #0A042FD6 !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: 10px;
}
.testimonial-section h2 {
  color: #fff !important;
}
.testimonial-section .testimonial-card {
  border: 1px solid rgba(255, 255, 255, 0.5);
  background: #04001D !important;
}
.testimonial-section .testimonial-card img {
  filter: invert(100);
}
.gptBookInfoWrapper h2,
.gptBookInfoWrapper h3 {
  color: #fff !important;
}
.pricing-card {
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #fff;
  background-color: #0A042FD6 !important;
}
.pricing-card h4 {
  color: #fff !important;
}
.pricing-card .highlight {
  background-color: transparent !important;
}
.pricing-card .price {
  color: #fff !important;
}
.book-section {
  background-color: transparent !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 2rem;
}
.most-popular {
  border-color: orange !important;
}
.chat .chatInputField {
  font-size: 13px;
}
.rechargeTitle {
  color: #fff !important;
}
