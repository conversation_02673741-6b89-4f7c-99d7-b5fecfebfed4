.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
/* Header Styles */
header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-bottom: 1px solid transparent;
  padding: 12px 0;
  box-shadow: none;
  transition: all 0.3s ease;
}
header.scrolled {
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  text-decoration: none;
}
.logo img {
  height: 45px;
  margin-right: 8px;
}
.nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
}
.nav-links {
  display: flex;
  align-items: center;
  gap: 32px;
  list-style: none;
  margin-bottom: 0 !important;
}
.nav-links a {
  color: #666;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s;
}
.nav-links a:hover {
  color: #333;
}
.auth-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}
.login-btn {
  color: #666;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s;
}
.login-btn:hover {
  color: #333;
}
.signup-btn {
  background-color: #ff6b35;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.2s;
}
.signup-btn:hover {
  background-color: #e55a2b;
}
.login-btn {
  background: #462897 !important;
}
/* Main Content Styles */
main {
  padding-top: 60px;
  text-align: center;
}
.hero-badge {
  display: inline-block;
  background-color: #ffeee8;
  color: #ff6b35;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 32px;
}
.hero {
  padding: 100px 0 80px;
  background: #fff;
  min-height: 80vh;
  display: flex;
  align-items: center;
}
.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  width: 100%;
}
.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}
.hero-img {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 20px;
}
.hero-text {
  text-align: left;
}
.hero-title {
  font-size: 54px;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 16px;
  color: #1e293b;
}
.hero-title .highlight {
  color: #ff6b35;
}
.hero-subtitle-main {
  font-size: 36px;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 24px;
  color: #1e293b;
}
.hero-description {
  font-size: 18px;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 32px;
  max-width: 500px;
}
.hero-cta {
  margin-top: 8px;
}
