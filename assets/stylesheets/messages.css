/*********** BODY **************/
/* Change Body Attributes */
html, body {
    height: 100%;
}
.fill {
    height: 100%;

    padding-bottom: 5%;
}


/******************** MAIN WRAPPERS *******************/
/* Main Window - Boxed style */
.chat-wrap {
    height: 100%;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    background-color: white;
    overflow: hidden;
}
/* Panel Wrapper: Side Menu - Content Window */
.panel-wrap {
    height: 100%;
    padding: 0;
}
/* Section Wrapper: Conversations - Contacts - Messages - Details */
.section-wrap {
    height: 100%;
}
/* Header Wrapper */
.header-wrap {
    height: 10%;
}
/* Content Wrapper */
.content-wrap {
    height: 95%;
    overflow: auto;
   -webkit-overflow: auto;	
	float: left bottom;   
}
/* Messages Wrapper */
.content-wrap.messages {
    height: 75%;
}
/* Message Text Wrapper */
.send-wrap {
    height: 15%;
}


/**************** HEADER ****************/
/* Header Item */
.chat-header {
    height: 100%;
    width: 100%;

    background: #f9fbfd;
    box-shadow: 0 1px 1px;

    display: table;
}
/* Header Title */
.chat-header h4 {
    display: table-cell;
    vertical-align: middle;
    color: black;
    width: 90%;
}
/* Header Button */
.header-button {
    display: table-cell;
    vertical-align: middle;
    color: #333;
}
/* Header Properties */
.header-button a {
    padding: 0;
}
.chat-header .btn:hover {
    transition: color 0.5s ease;
    color: #333;
}


/************* CONVERSATIONS *************/
/* Conversation Item */
.conversation {
    text-align: left;
    padding: 8px 10px 5px;
    border-bottom: 1px solid #ddd;
    width: 100%;

}
/* Conversation Properties */
.conversation:hover {
    background-color: #c8d6eb;
}

.converstationbg{
    background-color: #c8d6eb;
}




/******************* CONTACTS ***************/
/* Contact Item */
.contact {
    padding: 21px 10px 12px;
    border-bottom: 1px solid #ddd;
    width: 100%;
    display: inline-block;

    transition: background-color 0.5s ease;
}
/* Contact Properties */
.contact.btn:hover {
    background-color: #c8d6eb;
}

.contactitem:hover {
    background-color: #c8d6eb;
}
/**************** MESSAGES *****************/
/* Message Item */
.msg
{
    padding: 8px 10px 5px;

}
/* Message Sender */
.message-wrap .media-heading
{
    color: #2285b3;
    font-weight: 700;
}
.media-body h5
{
    color: black;
}
/* Message Time */
.time
{
    color:#bfbfbf;
}


/*************** SEND MESSAGE **********************/
/* Send Message Item */
.send-message {
    width: 100%;
    height: 100%;

    background-color: whitesmoke;
    box-shadow: 0 -1px 1px;

    display: table;
}
/* Message Text */
.message-text
{
    padding: 2px;
    display: table-cell;
    vertical-align: middle;
}
/* Send Button */
.send-button
{
    display: table-cell;
    vertical-align: middle;
    text-align: center;

    width: 10%;
}
/* Send Message Properties */
.no-resize-bar
{
    resize: none;
    height: 100% !important;
}
.send-button a {
    padding: 0;
}
.send-message .btn:hover {
    transition: color 0.5s ease;
    color: #0451b7;
}

.lightishblue{
    background-color: #f8fafd;
}

/*************** OTHER ********************/
/* Remove Bootstrap Button Effect */
.btn.active, .btn:active {
    -webkit-box-shadow: none;
    box-shadow: none;
}

/* Fix Bootstrap Media Body Display */
/* For Conversations and Contacts */


/* Animate Side Information Menu */

/* Scrollbar Attributes */
body::-webkit-scrollbar {
    width: 12px;
}
::-webkit-scrollbar {
    width: 6px;
}
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
}
::-webkit-scrollbar-thumb {
    background:#ddd;
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.5);
}