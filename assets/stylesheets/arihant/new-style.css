
.header-wrapper{
  width:100%;
}
.category-main-wrp .container-fluid{
  padding-left: 25px;
}

.bg-wrapper-sec{
  background: url(../images/bg-banner.jpg);
  background-size: cover;
  height:auto;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-position: center;
  min-height: 750px;
}


.logo-wrapper {
    width: 100%;
    text-align: center;
    padding: 6px 0px;
}

.header-menu-wrapper{

}
.menu-bar-wrp{
  width:100%;
  text-align: center;
}
.menu-bar-wrp ul{
  padding: 0px;
  margin:0px;
  text-align: center;
  font-family: 'Open Sans', sans-serif;
  display: inline-block;
}
.menu-bar-wrp ul li{
  list-style: none;
  float: left;
  display: inline-block;
  line-height: 24px;
}

.menu-bar-wrp ul li a{
  padding: 0px 5px;
  color:#fff;
  font-size: 12px;
  border-right: 1px solid #fff;

  transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
}

.menu-bar-wrp ul li a:hover{
  text-decoration: none;
  color:#EB7215;
}

.menu-bar-wrp ul li.youtube a{
  border:none;
}




.main-menu-wrp{
  position: relative;

  padding: 10px 0px;
  border-bottom: 1px solid #dadada54;
  transition-property: all;
  transition-duration: 400ms;
  -webkit-transition: all 400ms ease-in-out;
  transition-timing-function: ease-in-out;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  background-color: linear-gradient(90deg,#FF3448  0%,#FF9B26 100%);

  width: 100%;
  z-index: 999;
}




.user-menu-wrp {
  display: inline-block;
  float: left;
}
.user-menu-wrp a.menu-dots-img-wrp {
    display: inline-block;
    padding: 14px 14px;
}
.user-menu-wrp img.menu-dots-img{
  width:24px;
  height:24px;
}

ul.menu-wrp-link-main{
  font-family: 'Oswald', sans-serif;
  padding:0px 0px;
  margin:0px;
  float: left;
}
.main-menu-wrp.search-active-now-row ul.menu-wrp-link-main{  
  -webkit-animation: slow-hiddsen-menu .4s both;
  animation: slow-hiddsen-menu .4s both;
}
ul.menu-wrp-link-main li.link-menu-li{
  float:left;
  list-style: none;
}
ul.menu-wrp-link-main li.link-menu-li a.menu-link-anchor{
  font-weight: 300;
  color:#fff;
  font-size: 20px;
  text-transform: uppercase;
  padding: 14px 14px;
  display: inline-block;
  letter-spacing: 1px;
}

ul.menu-wrp-link-main li.link-menu-li.menu-big-under:hover ul.under-menu-wrp-links{
  display: block;
}
ul.menu-wrp-link-main li.link-menu-li.menu-big-under a.menu-link-anchor:after {
    content: "";
    bottom: 2px;
    left: 48%;
    position: absolute;
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\f107";
}

ul.menu-wrp-link-main li.link-menu-li a.menu-link-anchor:hover{
  text-decoration: none;
}


.menu-main-min-height{
  min-height: 78px;
}
.menu-big-under{
  position: relative;
}
ul.under-menu-wrp-links{
  position: absolute;
  top: 100%;
  display: none;
  left: 0px;
  min-width: 180px;
  padding: 0px 15px 0px 10px;
  margin:0px;
  background-color: #2a2a2a;
}
ul.under-menu-wrp-links li{
  list-style: none;
}
ul.under-menu-wrp-links li a{
  color:#fff;
  display: block;
  padding: 5px 2px;
  text-transform: uppercase;
  position: relative;
  font-size:14px;
  font-weight: 300;
}
ul.under-menu-wrp-links li a:hover{
  text-decoration: none;
}
ul.under-menu-wrp-links li a:after{
    content: "";
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    height: 1px;
    width: 100%;
    position: absolute;
    left: 0px;
    top: 100%;
}
ul.under-menu-wrp-links li:last-child a:after{
  display: none;
}






.menu-wrp-all-users-com{

}
.menu-wrp-all-users-com ul{
  padding:0px;
  margin:0px;
  font-family: 'Oswald', sans-serif;
  float:right;
}

.main-menu-wrp.search-active-now-row .menu-wrp-all-users-com ul{  
  -webkit-animation: slow-hiddsen-menu .4s both;
  animation: slow-hiddsen-menu .4s both;
}

.menu-wrp-all-users-com ul li{
  list-style: none;
  float: left;
}

.menu-wrp-all-users-com ul li a{
  font-weight: 300;
  color: #fff;
  font-size: 20px;
  text-transform: uppercase;
  padding: 13px 14px;
  letter-spacing: 1px;
  display: inline-block;
}

.menu-wrp-all-users-com ul li a:hover{
  text-decoration: none;
}
.menu-wrp-all-users-com ul li a img{
  width: 20px;
  height: 20px;
  margin-bottom: 6px;
  margin-right: 2px;
}
.search-input-in-disable {
  width: 0px;
  border: 0px solid #cccccc42;
  position: absolute;
  border-radius: 6px 6px 6px 6px;
  z-index: -1;
  background: transparent;
  transition: 0.5s all ease-in-out;
  -o-transition: 0.5s all ease-in-out;
  -ms-transition: 0.5s all ease-in-out;
  -moz-transition: 0.5s all ease-in-out;  
  -webkit-transition: 0.5s all ease-in-out;
  left: 0px;
  top: 20%;
}
input.search-input-in-disable.active-now{
  width: 70%;
  display: block;
  padding: 2px 3px 3px 12px;
  position: absolute;
  z-index: 999;
  font-size:16px;

  background: #fff;
  border: 1px solid #cccccc42;
}

.search-header-close {
  position: absolute;
  right: 0px;
  top: 0px;
  height: 100%;
  background: #0c0c0c36;
  padding: 14px 15px;
  overflow: hidden;
  visibility: hidden;
}

li.none-add-class-responsive.display-none-now a {
    display: none;
}

.wrapper-in-box-side-close{
  overflow: hidden;
  width: 100%;
  position: absolute;
  height: 100%;
  z-index: -1;
}


.search-header-close.header-close-active-btn{
  right:0;
  cursor: pointer;
  visibility: visible;
}
.search-btn-click{

}
.margin-top-6{
  margin-top: 6px;
}

.counter {
  animation-duration: 1s;
  animation-delay: 0s;
}





/*====================================  Programs Section========================================*/

.programs-section{
  padding:30px 0px;
}
.border-title-programs{
  border-bottom:1px solid #b9b9b9;
}
h2.title-programs-section{
  display: inline-block;
  margin: 0px;
  letter-spacing: 2px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 55px;
  color: #000;
}

h2.title-programs-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}

.programs-disc-pera-about{
  color: #939797;
  text-align: center;
  font-size:17px;
  padding-top: 18px;
  margin-bottom: 30px;
  font-family: 'Open Sans', sans-serif;
}

.wrp-box-programs{
  text-align: center;
  border-right: 2px solid #b8b8b8;
  padding: 50px 0px;
}

.brdr-none-here{
  border-right: 0px;
}
.wrp-box-icon-size{

}

.wrp-box-icon-size img{

}

.wrp-box-text-sec-disc{
  padding-top:20px;
}
.wrp-box-text-sec-disc h3 {
    color: #939797;
    font-size: 38px;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
}
.wrp-box-text-sec-disc p{
  font-family: 'Fira Sans', sans-serif;
  font-size:16px;
  font-weight: 400;
  min-height: 83px;
  margin-bottom: 0px;
}
a.browse-now-button{
  font-weight: 400;
  font-size:17px;
  color:#eb7215;
  font-family: 'Fira Sans', sans-serif;
}

a.browse-now-button:hover{
  text-decoration: none;
}

.mrgn-bottom-set-pera{
  /*margin-bottom:12px !important;*/
}


/*=================================      Categories Start  ===================================================*/
.categories-section{
  padding: 40px 0px 30px;
  margin-top:20px;
  background: url(../images/bg-categories.jpg);
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: center;
}
.border-title-categories {
  border-bottom: 1px solid #b9b9b9;
}
h2.title-categories-section{
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 55px;
  color: #000;
  letter-spacing: 2px;
}
h2.title-categories-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}

.categories-section-disc-title-pera{
  color: #939797;
  text-align: center;
  font-size:17px;
  padding-top: 18px;
  font-family: 'Open Sans', sans-serif;
}

.margin-row-categories-rowss{
  margin-top:10px;
}
.box-categories-wrp{
  width: 100%;
}
.box-categories-wrp a{
  color: #fff;
  display: inline-block;
  text-align: center;
  border-radius: 14px;
  width: 100%;
  margin-bottom: 15px;
  font-weight: 400;
  font-size: 28px;
  line-height: 40px;
  padding: 40px 15px;
  font-family: 'Fira Sans', sans-serif;
}
.box-categories-wrp a:hover{
  text-decoration: none;
}

.bg-color-fill-categories{
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
.responsive-padding-none {
    padding-left: 7px;
    padding-right: 7px;
}
.bg-color-fill-categories h3{
  padding:45px 20px;
  text-align: center;
  font-family: 'Fira Sans', sans-serif;
  font-weight: 400;
  font-size: 28px;
  line-height: 40px;
  color: #fff;
}

.line-box-category{
  width: 70%;
  margin: 0px auto;
  background: #fff;
  height: 2px;
  border-radius: 5px;
}
.red-color-fill-bg{
  background:#ff3448;
}
.red-color-fill-bg:hover{
  background: #ea1d32;
}
.yellow-color-fill-bg{
  background: #ff9b26;
}
.yellow-color-fill-bg:hover{
  background: #f38c13;
}

/*======================================================================
                          Journey Section
==========================================================================*/

.journey-sec-wrp {
    padding: 30px 0px;
    background: url(../images/journery-bg.png);
    background-size: cover;
    height: auto;
    background-repeat: no-repeat;
    background-position: center;
}

.border-title-journey{
  border-bottom: 1px solid #b9b9b9;
}
.title-journey-section{
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 45px;
  color: #000;
  letter-spacing: 1px;
}

.title-journey-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}
.journey-section-disc-title-pera{
  color: #939797;
  text-align: center;
  font-size: 17px;
  padding-top: 18px;
  padding-bottom: 20px;
  font-family: 'Open Sans', sans-serif;
}
.brdr-right-nn:after{
  display: none;
}
.manage-count-wrp-box {
    text-align: center;
    padding: 30px 0px;
    position: relative;
}
.the-img-icon-journey{

}
.the-img-icon-journey img{
  width:50px;
}
.manage-count-wrp-box:after{
  content: "";
  position: absolute;
  right: 0px;
  top: 0px;
  height: 100%;
  background: linear-gradient(50deg,#FF3448 0%,#FF9B26 100%);
  width: 2px;
}
.manage-count-wrp-box h2{
  font-family: 'Oswald', sans-serif;
  color:#686868;
}
.manage-count-wrp-box h2 span{
  color:#686868 !important;
  font-family: 'Oswald', sans-serif;
}

.manage-count-wrp-box span {
  font-family: 'Fira Sans', sans-serif;
  color:#000000;
}


/*========================================================
                      Connect Section
==========================================================*/


.connect-section {
    padding: 30px 0px;
    transition-property: all;
    transition-duration: 400ms;
    -webkit-transition: all 400ms ease-in-out;
    transition-timing-function: ease-in-out;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}


.border-title-coonect{
  border-bottom: 1px solid #f5f5f5;
}
.title-connect-section{
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 55px;
  color: #fff;
  letter-spacing: 1px;
}

.title-connect-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #ffffff;
  height: 2px;
  top: 99%;
  left: 0px;
}
p.connect-sec-wrp-disc-pera{
  color: #ffffff;
  font-family: 'Open Sans', sans-serif;
  font-size: 17px;
  font-weight: 300;
  margin-top:20px;
  padding: 0px 15px;
  text-align: center;
}

ul.social-icon-wrp-connect{
  padding: 0px;
  border-right: 1px solid #fff;
  margin:0px;
  float: right;
}
ul.social-icon-wrp-connect li{
  list-style: none;
  float: left;
  width: 22%;
  font-size: 33px;
  text-align: center; 
}
ul.social-icon-wrp-connect li a{
  color:#fff;
  padding:0px 20px;
}
.img-wrp-call-in{
  float: left;
  margin-right: 10px;
  padding:6px 0px;
}
.img-wrp-call-in img{
  width:32px;
}
h3.call-here-number{
  font-size:24px;
  margin-top: 0px;
  padding:6px 0px;
}
h3.call-here-number a{
  color:#fff;
}
h3.call-here-number a:hover{
  text-decoration: none;
}


.mrgn-top-connect-sec{
  margin-top:12px;
}

/*==============================================================================================
                                              FOOTER
================================================================================================*/
.footer{
  background: #f7f7f7;
  padding: 15px 0px;
}
.image-wrapper-footer-logo{
  text-align: center;
}
.image-wrapper-footer-logo img{
  width:110px;
  margin-top:25px;
}
h3.footer-link-title{
  font-size: 14px;
  font-weight: 700;
  font-family: 'Open Sans', sans-serif;
  color: #000;
}
ul.link-ul-footer{
  padding:0;
  margin:0;
}
ul.link-ul-footer li{
  list-style: none;
}
ul.link-ul-footer li a{
  color: #000;
  display: block;
  padding: 6px 0px;
  font-size: 13px;
  font-weight: 400;
}
ul.link-ul-footer li a:hover{
  text-decoration: none;
}

.text-center-align-here{
  text-align: center;
  width:100%;
  margin-top: 6px;
}

.there-social-footer-link-wrp{
  padding:0;
  margin:0;
  display: inline-block;
  text-align: center;
}
.there-social-footer-link-wrp li{
  float: left;
  list-style: none;
}
.there-social-footer-link-wrp li a{
  padding:4px 10px;
  display: inline-block;
  color:#979a9b;

  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
  font-size: 19px;
}

.there-social-footer-link-wrp li a:hover{
  color:#EB7215;
}
.img-wrapper-connect {
    text-align: center;
}

/*==================================================================================================
                                      Copy Right
===================================================================================================*/
.footer-copyright{
  background: #ffffff;
}
.footer-copyright p.copy-right-text-footer{
  color:#000;
  margin:0;
  font-weight: 600;
  padding:6px 0px;
  text-align: center;
  font-size:14px;
}

.responsive-view-icon-this{
  display: none;
}

.responsive-view-not-desktop{
  display: none;
}

.overlay-menu-close{
  position: fixed;
  background: #00000059;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  display: none;
  z-index: 999;
  transition: 0.3s all ease-in-out;
}


.dispay-set-resposive-img-phone{
  display: none;
}
















/*=======================================================================================
                              Login Popup
========================================================================================*/
#login-arihant-popup{
  font-family: 'Fira Sans', sans-serif;
}
#login-arihant-popup .modal-body{
  padding:1.5em 2rem;
  overflow: hidden;
  min-height: 400px;
}
.title-header-login-popup-arihant {
  widows: 100%;
}
.title-header-login-popup-arihant h4{
  font-size: 16.96px;
  font-weight: 400;
  letter-spacing: -0.5px;
  text-align: center;
  margin-top: 10px;
  color:#686868;
}

.title-header-login-popup-arihant h4 span{
  color:#ef7215;
}
button.close.close-login-popup-arihant{
  position: absolute;
  right: 8px;
  top: 0px;
}
button.close.close-login-popup-arihant img{
  width:15px;
}
button.close.close-login-popup-arihant:hover,
button.close.close-login-popup-arihant:active,
button.close.close-login-popup-arihant:focus{
  outline: none;
}
.login-other-api-wrp {
    margin: 30px 0px;
}
.digital-serv-login-btn{

}
.digital-serv-login-btn button{
  width: 100%;
  letter-spacing: -0.5px; 
}
button.facebook-btn-login-arihant{
  background: #3b5998;
  border: 1px solid #3b5998;
  border-radius: 6px;
  padding: 10px 15px;
  color: #fff;
  cursor: pointer;
}

.google-btn-login-arihant{
  background: none;
  border:1px solid #d9d9d9;
  width: 100%;
  border-radius: 6px;
  padding: 10px 15px;
  color: #333;
  cursor: pointer;
  margin-top:10px;
}
.digital-serv-login-btn button i{
  float: left;
  display: inline-block;
  font-size: 18px;
  padding: 3px 0px;
}
.digital-serv-login-btn button span{
  font-weight: 300;
  font-size: 14px;
}

.google-btn-login-arihant i{
  font-size: 0px !important;
  padding:0px !important;
}
.google-btn-login-arihant i img{
  width: 18px;
  padding: 3px 0px;
}
.login-other-ways{
  text-align: center;
  padding:20px 0px;
}
span.login-with-other-ways-text {
    position: relative;
    color:#686868;
    letter-spacing: -0.5px;
    font-size: 17px;
}
span.login-with-other-ways-text:after {
    position: absolute;
    content: "";
    width: 70px;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    height: 2px;
    right: -80px;
    top: 8px;
}

span.login-with-other-ways-text:before {
    position: absolute;
    content: "";
    width: 70px;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    height: 2px;
    left: -80px;
    top: 8px;
}

.box-wrp-login-arihant-user {
    padding: 30px 0px;
}

.box-wrp-single-row-login-arihant{
  position: relative; 
}
.box-wrp-single-row-login-arihant i{
  position: absolute;
  left: 0px;
  color: #686868;
  top: 8px;
  display: inline-block;
  padding: 10px 15px;
}
.box-wrp-single-row-login-arihant i.fa.fa-mobile{
  font-size: 24px;
  padding: 6px 15px;
}
input.input-box-bottom-border {
    border: none;
    border-bottom: 1px solid #d8d8d8;
    width: 100%;
    color: #686868;
    min-height: 35px;
    font-size: 16px;
    letter-spacing: -0.5px;
    padding-left: 35px;
    font-weight:400;
}

input.input-box-bottom-border:focus,
input.input-box-bottom-border:hover,
input.input-box-bottom-border:active{
  outline: none;
}
.box-wrp-single-row-login-arihant.username-wrp-box-input-login-arihant {
  padding-bottom: 35px;
  padding-top: 10px;
}
.box-wrp-single-row-login-arihant.Password-wrp-box-input-login-arihant {
    padding-top: 10px;
}
.btn-to-show-password-hide{
  position: absolute;
  top: 5px;
  display: inline-block;
  right: 35px;
}
.btn-to-show-password-hide i{
  padding: 9px 10px;
  font-size: 13px;
}
.forgot-single-login-option-wrp {
    text-align: right;
}

.forgot-single-login-option-wrp a{
  display: inline-block;
  font-size: 10px;
  color:#ef7215;
  font-weight: 400;
}
.forgot-single-login-option-wrp a:hover,
.forgot-single-login-option-wrp a:hover,
.forgot-single-login-option-wrp a:focus{
  text-decoration: none;
}
.login-btn-wrp-arihant-popup{
  text-align: center;
  margin-top: 20px;
}
.login-btn-wrp-arihant-popup button.login-btn-arihant{
  cursor: pointer;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  border: none;
  color: #fff;
  padding: 8px 51px;
  border-radius: 6px;
  font-weight: 300;
  display: inline-block;
}
.signup-in-login-popup-wrp-text {
    text-align: center;
}
.signup-in-login-popup-wrp-text span{
  font-size: 10px;
  color: #979a9b;
}
a.signup-btn-in-arihant-login-popup{
  font-size: 12px;
  color: #ef7215;
  font-weight: 500;
}

/*===========================================================
                    Sign Up Body Popup
=============================================================*/
.signup-body-modal{
  display: none;
}
.display-block-item{
  display: block !important;
}
.display-none-item{
  display: none;
}
.forgot-password-body-modal{
  display: none;
}
h4.forgot-password-title {
  color: #000;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.2px;
}

p.sub-title-forgot-pera{
  text-align: center;
  color: #a7b1ac;
  font-size: 12px;
}
.forgot-wrp-box-row {
    width: 100%;
}
.wrp-box-manage-inpit-forgot {
    width: 100%;
}
input.forgot-input-box{
  width: 100%;
  color: #757575;
  text-align: center;
  min-height: 35px;
  border: 0px;
  border-bottom: 1px solid #b3b3b3;
}

input.forgot-input-box:focus,
input.forgot-input-box:hover,
input.forgot-input-box:active{
  outline: none;
}

a.btn-customer-care{
  color:#ef7215;
}

.mrgn-top-forgot-btn-sss{
  margin-top:20px;
}
.forgot-pera-bottom {
  line-height: 14px;
  margin-top: 26px;
}


/*==========================================================================================
                    Search Header CSS
===========================================================================================*/





.row.search-item-rows {
    font-family: 'Fira Sans', sans-serif;
}
.this-div-use-box-search{
  left: 20%;
  width: 60%;
  display: none;
  position: absolute;
  top: 0px;
  min-height: 84px;
}
.main-menu-wrp.search-active-now-row .this-div-use-box-search{ 
  display: block;
}
.search-view-bar-arihant {
    width: 100%;
}
input.input-box-search-in-header::placeholder{
  color:#fff;
}
input.input-box-search-in-header {
  overflow: hidden;
  color: #fff;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 17px;
  line-height: 1.29412;
  letter-spacing: -.021em;
  outline: none;
  width: calc(100% - 42px - 16px);
  height: 44px;
  background: none;
  border: none;
  position: absolute;
  padding: 0 17px 0 42px;
  top:20px;
}
.wrapper-form-header-search-input{
  min-height: 78px;
  opacity: 0;
}
.main-menu-wrp.search-active-now-row.now-hide-menuall .wrapper-form-header-search-input{
  /*-webkit-animation: arihant-searchform-slide 1s 0.4s both;
  animation: arihant-searchform-slide 1s 0.4s both;*/
  -webkit-animation: arihant-searchresults-items-show 0.4s both;
  animation: arihant-searchresults-items-show 0.4s both;
  opacity: 1;
}
button.btn-in-open-search-header-search {
  left: 0;
  position: absolute;
  z-index: 1;
  top: 20px;
  background: none;
  width: 40px;
  border: none;
  height: 44px;
  cursor: pointer;
  background-repeat: no-repeat;
  background-image: url(../images/search-icon-header-input.png);
  background-position: 50% 50%;
  background-repeat: no-repeat;
  -webkit-transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}


button.close-all-searchdiv-box{
  position: absolute;
  top: 20px;
  background: none;
  border: none;
  right: 0px;
  cursor: pointer;
}
button.close-all-searchdiv-box:hover,
button.close-all-searchdiv-box:focus,
button.close-all-searchdiv-box:active{
  outline: none;
}

span.wrapper-close-btn-search-header-all {
    display: block;
    width: 100%;
    height: 100%;
    min-width: 26px;
    min-height: 34px;
}

span.close-btn-search-header-one-part {
  right: 12px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  height: 18px;
  width: 1px;
  background: #fff;
  position: absolute;
  display: block;
  top: 8px;
  z-index: 1;
}

span.close-btn-search-header-second-part {
  height: 18px;
  width: 1px;
  background: #fff;
  position: absolute;
  display: block;
  top: 8px;
  z-index: 1;
  left: 12px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}

.form-search-box-arihant{
  position: relative;
}

section.search-result-in-wrapper-one {
    background: #fff;
    padding: 20px 10px;
}
h3.search-result-quick-disc{
  font-size: 14px;
  line-height: 1;
  letter-spacing: -0.2px;
  text-transform: uppercase;
  color: #686868;
}

.overlay-wrp-s.active-arihant-row{
  position: fixed;
  height: 100vh;
  width: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 9;
  top: 0px;
  left:0px;
}

ul.arihant-all-list-view-serach-result {
  padding: 0px;
  margin: 0px;
}
ul.arihant-all-list-view-serach-result li{
  list-style: none;
}
ul.arihant-all-list-view-serach-result li a{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #686868;
  letter-spacing: -0.2px;
  display: block;
  font-size: 12px;
  padding: 6px 12px;
  text-decoration: none;
}

ul.arihant-all-list-view-serach-result li a:hover{
  background-color: #f2f2f2;
}
ul.arihant-all-list-view-serach-result li:nth-child(1){
  -webkit-animation-delay: .22s !important;
  animation-delay: .22s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(2){
  -webkit-animation-delay: .24s !important;
  animation-delay: .24s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(3){
  -webkit-animation-delay: .26s !important;
  animation-delay: .26s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(4){
  -webkit-animation-delay: .28s !important;
  animation-delay: .28s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(5){
  -webkit-animation-delay: .3s !important;
  animation-delay: .3s !important;
}
ul.arihant-all-list-view-serach-result li:nth-child(6){
  -webkit-animation-delay: .32s !important;
  animation-delay: .32s !important;
}
ul.arihant-all-list-view-serach-result li:nth-child(7){
  -webkit-animation-delay: .34s !important;
  animation-delay: .34s !important;
}
ul.arihant-all-list-view-serach-result li:nth-child(8){
  -webkit-animation-delay: .36s !important;
  animation-delay: .36s !important;
  
}
ul.arihant-all-list-view-serach-result li:nth-child(9){
  -webkit-animation-delay: .38s !important;
  animation-delay: .38s !important;
  
}
ul.arihant-all-list-view-serach-result li:nth-child(10){
  -webkit-animation-delay: .4s !important;
  animation-delay: .4s !important;
}

.main-menu-wrp.search-active-now-row ul.arihant-all-list-view-serach-result li{
  -webkit-animation: arihant-searchresults-items-show   0.4s both;
  animation: arihant-searchresults-items-show  0.4s both;
}

.main-menu-wrp.search-active-now-row ul.menu-wrp-link-main li.link-menu-li{
  -webkit-animation: slow-hiddsen-menu 0.4s both;
  animation: slow-hiddsen-menu 0.4s both;
}
ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: .345s;
  animation-delay: .345s;
}

ul.menu-wrp-link-main li.link-menu-li:nth-child(3){
  -webkit-animation-delay: .315s;
  animation-delay: .315s;
}

ul.menu-wrp-link-main li.link-menu-li:nth-child(4){
  -webkit-animation-delay: .28s;
  animation-delay: .28s;
}


.main-menu-wrp.search-active-now-row  .menu-wrp-all-users-com ul li{
  -webkit-animation: slow-hiddsen-menu 0.4s both;
  animation: slow-hiddsen-menu  0.4s both;
}

.menu-wrp-all-users-com ul li:nth-child(1){
  -webkit-animation-delay: .14s;
  animation-delay: .14s;
}

.menu-wrp-all-users-com ul li:nth-child(2){
  -webkit-animation-delay: .175s;
  animation-delay: .175s;
}
.menu-wrp-all-users-com ul li:nth-child(3){
  -webkit-animation-delay: .21s;
  animation-delay: .21s;
}
.menu-wrp-all-users-com ul li:nth-child(4){
  -webkit-animation-delay: .245s;
  animation-delay: .245s;
}




@-webkit-keyframes slow-hiddsen-menu {
    0% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
    40% {
        opacity: 1
    }
    100% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
}


@keyframes slow-hiddsen-menu {
    0% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
    40% {
        opacity: 1
    }
    100% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
}

@-webkit-keyframes arihant-searchform-slide {
    0% {
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes arihant-searchform-slide {
    0% {
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}



@-webkit-keyframes arihant-searchresults-items-show {
    0% {
        opacity: 0;
        -webkit-transform: translateX(100px);
        transform: translateX(100px);
        -webkit-animation-timing-function: ease;
        animation-timing-function: ease
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@keyframes arihant-searchresults-items-show {
    0% {
        opacity: 0;
        -webkit-transform: translateX(100px);
        transform: translateX(100px);
        -webkit-animation-timing-function: ease;
        animation-timing-function: ease
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@-webkit-keyframes arihant-books-searchform-slide {
    0% {
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes arihant-books-searchform-slide {
    0% {
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}
.main-menu-wrp.search-active-now-row.now-hide-menuall ul.menu-wrp-link-main{
  visibility: hidden;
}
.main-menu-wrp.search-active-now-row.now-hide-menuall .menu-wrp-all-users-com ul{
  visibility: hidden;
}





.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li{
  -webkit-animation: arihant-books-item-searchhide .4s both;
  animation: arihant-books-item-searchhide .4s both
}
.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: 0s;
  animation-delay: 0s
}
.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: 0.035s;
  animation-delay: 0.035s
}
.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: .07s;
  animation-delay: .07s
}



.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li{
  -webkit-animation: arihant-books-item-searchhide .4s both;
  animation: arihant-books-item-searchhide .4s both
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(1){
  -webkit-animation-delay: .105s;
  animation-delay: .105s
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(2){
  -webkit-animation-delay: .14s;
  animation-delay: .14s
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(3){
  -webkit-animation-delay: .175s;
  animation-delay: .175s
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(4){
  -webkit-animation-delay: .21s;
  animation-delay: .21s
}



@-webkit-keyframes arihant-books-item-searchhide {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
    60% {
        opacity: 1
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
}

@keyframes arihant-books-item-searchhide {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
    60% {
        opacity: 1
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
}

















/*======================================================================
                          SHOPPING CART PAGE
=======================================================================*/
.shopping-wrp {
    padding: 50px 0px 60px;
}

h3.shoppig-cart-title-wrp-arihant {
    font-size: 50px;
    letter-spacing: 0.2px;
    font-weight: 700;
    font-family: 'Oswald', sans-serif;
}
p.text-quetion-in-shopping-cart-page-arihant {
    font-size: 20px;
    font-family: 'Fira Sans', sans-serif;
    margin: 0px;
    font-weight: 500;
    color: #686868;
}
span.call-us-shopping-cart-pg-arihant {
    font-family: 'Fira Sans', sans-serif;
    font-size: 15px;
}
span.call-us-shopping-cart-pg-arihant a{
  color: #ef7215;
}
span.call-us-shopping-cart-pg-arihant a:hover,
span.call-us-shopping-cart-pg-arihant a:active,
span.call-us-shopping-cart-pg-arihant a:focus{
  text-decoration: none;
}


.tabel-shopping-cart{
}
.tabel-shopping-cart thead{
  background: #979a9b;
  font-family: 'Oswald', sans-serif;
  color: #fff;
}
.tabel-shopping-cart tbody{
  font-family: 'Fira Sans', sans-serif;
}
.tabel-shopping-cart .table td, .tabel-shopping-cart .table th{
  border-top: 1px solid #dee2e6ab;
}
.tabel-shopping-cart .table tbody tr:first-child td{
  padding: .75em .75rem 2em .75em;
  border-bottom: 0px !important;
}
.tabel-shopping-cart .table tbody td{
  padding: 2em .75rem 2em .75em;
  border-bottom: 1px solid #dee2e659;
}
.tabel-shopping-cart thead th{
  font-weight: 400;
  font-size:22px;
  text-transform: uppercase;
  padding:6px 20px;
}
.tabel-shopping-cart thead th:nth-child(1){
  width: 18%
}
.tabel-shopping-cart thead th:nth-child(2){
  width: 34%;
}
.tabel-shopping-cart thead th:nth-child(3){
  width: 14%;
  text-align: center;
}
.tabel-shopping-cart thead th:nth-child(4){
  width: 18%;
}
.tabel-shopping-cart thead th:nth-child(5){
  width: 14%;
}
.book-image-wrp-shopping-cart{
  text-align: center;
}

.shopping-carttable-margin-wrp-arihant{
  margin-top: 30px;
}

.title-shopping-cart-table-wrps{
  font-family: 'Fira Sans', sans-serif; 
}
.title-shopping-cart-table-wrps h3{
  font-size: 16px;
  margin-bottom: 4px;
}
.title-shopping-cart-table-wrps p{
  font-weight: 400;
  font-size: 10px;
  color: #686868;
}

.details-book-about-cart-shopping-pg{

}

.wrp-details-about-shopping-cart-pg{
  font-weight: 400;
  font-family: 'Fira Sans', sans-serif;
  color: #686868;
  padding: 5px 0px;
  font-size: 14px;
}
.use-icon-small{  
  font-size: 14px !important;
  margin-right: 2px;
}
span.first-table-cart-page-arihant{
  width: 110px;
  display: inline-block;
}
span.second-details-cart-page-arihant {
    width: 50px;
    display: inline-block;
}

a.detail-product-in-shopping-cart-table {
    font-size: 14px;
    color: #686868;
    display: inline-block;
    margin-top: 10px;
    border-bottom: 1px solid #b3b3b3;
}
a.detail-product-in-shopping-cart-table:hover,
a.detail-product-in-shopping-cart-table:focus,
a.detail-product-in-shopping-cart-table:active{
  text-decoration: none;
}
.btn-quantity-cart-page-wrp{
  text-align: center;
}
.btn-quantity-cart-page-wrp input{
  text-align: center;
  padding: 0px 5px;
  width: 55px;
  height: 34px;
  border: 1px solid #b9b9b9;
}
.btn-quantity-cart-page-wrp input[type=number]::-webkit-inner-spin-button, 
.btn-quantity-cart-page-wrp input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

.btn-quantity-cart-page-wrp input:hover,
.btn-quantity-cart-page-wrp input:active,
.btn-quantity-cart-page-wrp input:focus,
.btn-quantity-cart-page-wrp button:hover,
.btn-quantity-cart-page-wrp button:focus,
.btn-quantity-cart-page-wrp button:active{
  outline: none;
}

button.sub-quantity-cart {
    border: none;
    background: none;
    cursor: pointer;
    color: #ef7215;
    font-size: 25px;
}
button.add-quantity-cart {
    border: none;
    background: none;
    cursor: pointer;
    color: #ef7215;
    font-size: 25px;
}

.price-details-in-shopping-cart-current{

}
.price-details-in-shopping-cart-current img{
  float: left;
  margin-top: 8px;
}
.price-details-in-shopping-cart-current h3 {
    display: inline-block;
    font-size: 24px;
    margin-top: 5px;
    color: #686868;
}
.price-details-in-shopping-cart-current h3 i.fa.fa-inr{
  float: left;
  margin-right: 4px;
  font-size: 20px;
  margin-top: 5px;
}
span.cross-price-current {
    color: #ef7215;
    font-size: 16px;
}
.min-heright-shopping-cart{
  min-height: 70px;
}
p.delivery-details-shopping-cart {
  font-size: 14px;
  color: #686868;
  font-family: 'Fira Sans', sans-serif;
}
span.date-of-delivery-dd {
  display: block;
}

a.remove-class-btn-shopping-btn{

}

a.remove-class-btn-shopping-btn i{
  color: #686868;
  font-size: 14px;
}


a.remove-class-btn-shopping-btn:hover,
a.remove-class-btn-shopping-btn:active,
a.remove-class-btn-shopping-btn:focus{
  color: #686868;
}
a.remove-class-btn-shopping-btn img{
  margin-top:10px;
}
a.remove-class-btn-shopping-btn span {
  display: inline-block;
  color: #686868;
  font-size: 13px;
  padding: 0px 4px 0px 1px;
  border-bottom: 1px solid #888888;
}

a.remove-class-btn-shopping-btn:hover{
  text-decoration: none;
}


.row.mrgn-row-last-wrp-shopping-cart{
  font-family: 'Fira Sans', sans-serif;
  margin-top:10px;
}

.width-18-in-shopping-paymeny {
    width: 18%;
}
.payment-options-shopping-cart-pg{
  padding-left: 15px;
}
.payment-options-shopping-cart-pg p{
  font-size: 15px;
  margin: 0px;
  color: #686868;
}

.coupon-details-use-shopping-cart-page{

}
.coupon-details-use-shopping-cart-page p{
  font-size: 15px;
  color: #686868;
  margin: 0px;
  font-weight: 500;
}
.coupon-details-use-shopping-cart-page span{
  font-size: 12px;
  color: #000;
  font-weight: 400;
  margin-top: 5px;
  line-height: 14px;
  display: inline-block;
}
.box-wrp-check-coupon-ws-shopping{
  align-items: center;
}
input.coupon-check-input-box{
  width: 50%;
  font-size: 12px;
  height: 26px;
  padding: 0px 5px;
  background: #fafafa;
  border: 1px solid #e4e5e5;
}
input.coupon-check-input-box:active,
input.coupon-check-input-box:focus{
  outline: none;
}
button.btn-coupon-click-check{
  text-transform: uppercase;
  font-size: 12px;
  color: #ef7215;
  border: none;
  background: none;
  letter-spacing: -0.2px;
  cursor: pointer;
  font-weight: 500;
}
button.btn-coupon-click-check:hover,
button.btn-coupon-click-check:active,
button.btn-coupon-click-check:focus{
  outline: none;
}


.width-32-in-table-show-final-rate-arihant {
  width: 32%;
  padding-right: 15px;
  float: left;
}
.wrp-table-box-bg-shhs{
  background: #f7f7f7;
  width: 100%;
  padding: 15px 15px;
  border-radius: 4px;
}

.row-in-all-part-money-detail-books {
  align-items: center;
    padding-bottom: 10px;
}
.first-pera-in-money-detail-shoppings-pg {
  float: left;
  font-size: 15px;
  color: #000;
  font-weight: 400;
}
.second-part-in-right-money-detail-shoppings-pg {
  float: right;
  font-size: 15px;
  color: #000;
  font-weight: 400;
}
.second-part-in-right-money-detail-shoppings-pg i{
  color: #686868;
}
img.in-use-three-img-icons {
    width: 11px;
}

.wrp-table-box-bg-shhs .row-in-all-part-money-detail-books:nth-child(4){
  border-bottom: 1px solid #cccccc3d;
}
.row-last-in-this-part-money-detail-books{
  align-items: center;
  padding-top: 10px;
}
.first-main-in-money-detail-shoppings-pg {
  font-size: 18px;
  float: left;
  font-weight: 500;
}
.second-part-in-main-right-money-detail-shoppings-pg {
  float: right;
  font-size: 18px;
  font-weight: 500;
} 
.second-part-in-main-right-money-detail-shoppings-pg span i{
  margin-right: 4px;
}

.row-this-btn-continue-and-checkout {
  padding-top: 20px;
}
.left-this-continue-btn-in-shopping-pg-cart {
  float: left;
}
.left-this-continue-btn-in-shopping-pg-cart a{
  font-size: 14px;
  margin-top: 5px;
  padding-bottom: 5px;
  color: #686868;
  display: inline-block;
  border-bottom: 1px solid #b9b9b9;
}
.left-this-continue-btn-in-shopping-pg-cart a:hover,
.left-this-continue-btn-in-shopping-pg-cart a:focus,
.left-this-continue-btn-in-shopping-pg-cart a:active{
  text-decoration: none;
}
.this-right-checkout-button-wrp {
    float: right;
}

button.btn-proceed-now {
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    border: none;
    padding: 6px 10px;
    color: #fff;
    text-transform: uppercase;
    cursor: pointer;
    font-size: 18px;
    font-family: 'Oswald', sans-serif;
}
button.btn-proceed-now:active,
button.btn-proceed-now:focus,
button.btn-proceed-now:hover{
  outline:none;
}












/*============================================================================
                            Category
==============================================================================*/
.category-main-wrp {
    padding: 50px 0px;
}
.side-menu-bar{
  font-family: 'Fira Sans', sans-serif;
}
.side-menu-bar .card-header{
  padding: 0px;
}

.side-menu-bar .card{
  border:none;
}


h5.btn-link-wrp-user-category-pg{
  font-size: 15px;
  font-weight: 400;
  margin: 0px;
  cursor: pointer;
  padding: 15px 8px;
  background: white;
}
h5.active-menu-this-here {
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    color: #fff;
    font-weight: 300;
    padding: 15px 9px;
    margin: 0px;
    font-size:16px;
}

h5.btn-link-wrp-user-category-pg:after {
  content: "";
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0px;
  height: 1px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
i.fa.fa-plus.this-use-up{
  background: -webkit-linear-gradient(#FF3448 , #FF9B26 );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.btn-link-wrp-user-category-pg.collapsed i.fa.fa-minus.this-use-open-now{ 
  display: none;
}

.btn-link-wrp-user-category-pg i.fa.fa-plus.this-use-up{
  display: none;
}
.btn-link-wrp-user-category-pg.collapsed i.fa.fa-plus.this-use-up{
  display: inline-block;
}

.btn-link-wrp-user-category-pg i{
  float: right;
  font-size: 10px;
  padding: 6px 5px;
}

ul.under-bold-soft-menus{
  padding:0px;
  margin:0px;
}

ul.under-bold-soft-menus li {
  list-style: none;
}
ul.under-bold-soft-menus li a{
  font-size: 14px;
  margin: 0px;
  font-weight: 400;
  color: #000;
  padding: 6px 15px 6px 25px;
  display: block;
  border-bottom: 1px solid #ccc;
}

ul.under-bold-soft-menus li a:hover{
  text-decoration: none;
}


.page-row-filter{
  font-family: 'Fira Sans', sans-serif;
}
ul.page-arrow-like-homes {
  margin: 0px;
  padding: 10px 0px;
  border-bottom: 1px solid #ccc;
  margin-bottom: 20px;
}

ul.page-arrow-like-homes li{
  float: left;
  list-style: none;
}
ul.page-arrow-like-homes li a{
  display: inline-block;
  padding: 2px 12px;
  position: relative;
  font-size: 12px;
  color: #686868;
}
.last-icons-slsl{
  display: inline-block;
  padding: 2px 12px;
  position: relative;
  font-size: 12px;
  color: #686868;
}

ul.page-arrow-like-homes li a:after {
    content: "\f105";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    position: absolute;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    top: 4px;
    right: -3px;
}
ul.page-arrow-like-homes li:last-child a:after{
  display: none;
}


ul.page-arrow-like-homes li a:hover,
ul.page-arrow-like-homes li a:focus,
ul.page-arrow-like-homes li a:active,
a.btn-sort-filter-allsis:active,
a.btn-sort-filter-allsis:hover,
a.btn-sort-filter-allsis:focus{
  text-decoration: none;
}

.manage-filter-row-in-front {
  padding: 0px 0px 20px 0px;
}
a.btn-sort-filter-allsis {
  padding: 6px 10px;
  min-width: 120px;
  font-family: 'Fira Sans', sans-serif;
  position: relative;
  display: inline-block;
  background: white;
  color: #686868;
  text-transform: uppercase;
  border-radius: 2px;
}
a.btn-sort-filter-allsis img{
  padding: 0px 0px 0px 4px;
}
a.btn-sort-filter-allsis:after {
  width: 102%;
  height: 106%;
  content: "";
  border-radius: 2px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  position: absolute;
  left: -1px;
  z-index: -1;
  top: -1px;
}




.product-wrp-listing{
  font-family: 'Fira Sans', sans-serif;
  margin-bottom:40px;
}
.book-img-wrp-listing{
  text-align: center;
}
.book-img-wrp-listing img{
  width: 95%;
}



.price-two-divide {
    padding: 5px 0px;
    width: 95%;
    margin:0px auto;
}
span.leftprice-minus-listing {
  float: left;
  font-size: 12px;
  font-weight: 500;
  padding-top:5px;
  color: #979a9b;
}
span.leftprice-minus-listing img {
  width: 9px;
  float: left;
  margin-top: 3px;
}



span.rightprice-this-listing {
  float: right;
  font-size: 18px;
  color: #ef7215;
  font-weight: 500;
}
span.rightprice-this-listing img{
  width: 14px;
  float: left;
  margin-top: 4px;
}
span.rightprice-this-listing i{
  color: #979a9b;
  font-size: 17px;
  float: left;
  margin-top: 4px;
  margin-right: 2px;
}
.offer-price-thiss{
  float: left;
  font-size: 10px;
  color: #686868;
  display: inline-block;
  font-weight: 400;
  padding: 6px 5px 0px 0px;
  font-family: 'Fira Sans', sans-serif;
}
.book-name-row-this{

}
h3.book-name-this-is-item-product{
  font-size: 12px;
  font-size: 0.78em;
  text-align: center;
  margin: 0px;
  height: 30px;
  line-height: 16px;
  overflow: hidden;
}






.btn-row-exprole-wrp{
  padding:10px 3px 0px; 
}
.exprole-wrp-btn-in-product-div {
    width: 40%;
    float: left;
    text-align: center;
}
.exprole-wrp-btn-in-product-div a{
  display: block;
  padding: 0px 7px;
  width: 100%;
  border: 1px solid #b9b9b9;
}
.exprole-wrp-btn-in-product-div a:hover{
  background: #000;
  border: 1px solid #000;
  color: #fff;
}
.exprole-wrp-btn-in-product-div a:hover span,
.exprole-wrp-btn-in-product-div a:hover i{
  color: #fff;
}


.exprole-wrp-btn-in-product-div a img{
  width: 12px;
}
.exprole-wrp-btn-in-product-div a i{
  font-size: 12px;
  color: #333;
}
.exprole-wrp-btn-in-product-div a span{
  color: #000;
  font-size: 10px;
  text-transform: uppercase;
  line-height: 28px;
  padding: 0px 0px 0px 0px;
  letter-spacing: -0.2px;
}
.heart-like-btn{
  width: 16%;
  float: left;
  text-align: center;
}
.heart-like-btn .wishlist-product-btn-in-product-arihant{
  display: inline-block;
  color: #ef7215;
  padding: 3.2px 0px 3px 0px;
}
.wishlist-product-btn-in-product-arihant img{
  width:17px;
}
.wishlist-product-btn-in-product-arihant img.second{
  display: none;
}
.wishlist-product-btn-in-product-arihant:hover img.first{
  display: none;
}
.wishlist-product-btn-in-product-arihant:hover img.second{
  display: inline-block;
}
.heart-like-btn i.fa.fa-heart{
  display: none;
}

.heart-like-btn:hover i.fa.fa-heart-o{
  display: none;
}

.heart-like-btn:hover i.fa.fa-heart{
  display: inline-block;
}

.add-to-cart-btn-product-in-arihant{
  float: left;
  width: 44%;
  text-align: center;
}
.add-to-cart-btn-product-in-arihant a{
  background: #ef7215;
  display: inline-block;
  width: 100%;
  padding: 0px 5px;
  border: 1px solid #ef7215;
}
.add-to-cart-btn-product-in-arihant a:hover{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
.add-to-cart-btn-product-in-arihant a i{
  font-size: 12px;
  color: #fff;
}
.add-to-cart-btn-product-in-arihant a img{
  width: 16px;

}
.add-to-cart-btn-product-in-arihant a span{
  font-size: 10px;
  line-height: 28px;
  text-transform: uppercase;
  letter-spacing: -0.2px;
  color: #fff;
  padding: 0px 0px 0px 0px;
}

.exprole-wrp-btn-in-product-div a:focus,
.exprole-wrp-btn-in-product-div a:hover,
.exprole-wrp-btn-in-product-div a:active,
.heart-like-btn .wishlist-product-btn-in-product-arihant:hover,
.heart-like-btn .wishlist-product-btn-in-product-arihant:focus,
.heart-like-btn .wishlist-product-btn-in-product-arihant:active,
.add-to-cart-btn-product-in-arihant a:hover,
.add-to-cart-btn-product-in-arihant a:active,
.add-to-cart-btn-product-in-arihant a:focus{
  text-decoration: none;
}



/*=================================================================
                          Product Detail Page
==================================================================*/


.product-row-detail-in-arihant{

}
.product-img-in-arihant-page{

}
.product-img-in-arihant-page img{
  width:100%;
}
.share-item-product-book-details{
  text-align:center;
  padding-top:5px;
}
.share-item-product-book-details span{
  font-weight: 300;
  font-family: 'Fira Sans', sans-serif;
  font-size: 12px;
}
.share-item-product-book-details a{
  display: inline-block;
  color: #686868;
  font-size: 12px;
  padding:0px 2px;
}


.product-disc-details-in-arihant {
    padding-left: 20px;
}
h3.arihant-product-name {
  font-size: 30px;
  font-weight: 600;
  font-family: 'Fira Sans', sans-serif;
}
span.product-create-by-arihant {
  display: block;
  margin-bottom: 10px;
  font-weight: 400;
  font-family: 'Fira Sans', sans-serif;
  color: #686868;
  padding: 2px 0px;
  font-size: 14px;
}

.download-wrp-width-this-arihant{
  display: inline-block;
  width: 150px;
}

.download-some-rear-details-products{
  padding:15px 0px;
}
.download-wrp-width-this-arihant a{
  
}

.download-wrp-width-this-arihant img{
  width: 36px;
}
.download-wrp-width-this-arihant span.product-pdf-download-pdf-text{
  font-weight: 500;
  font-size: 15px;
  color: #686868;
}
.download-ebook-arihant-book-wrp{
  display: inline-block;
}
.download-ebook-arihant-book-wrp a{
}
.download-ebook-arihant-book-wrp img{
  width: 36px;
}

.download-wrp-width-this-arihant a:hover,
.download-wrp-width-this-arihant a:active,
.download-ebook-arihant-book-wrp a:hover,
.download-ebook-arihant-book-wrp a:active,
a.timing-check-pin-btn-arihant:hover,
a.timing-check-pin-btn-arihant:focus,
a.timing-check-pin-btn-arihant:active{
  text-decoration: none;
}



.download-ebook-arihant-book-wrp span.product-ebook-download-text-arihant{
  font-weight: 500;
  font-size: 15px;
  color: #686868;
}


.wrp-delivery-message-good-and-fast-arihant {
    padding: 5px 0px 0px 0px;
    font-family: 'Fira Sans', sans-serif;
}

.deliver-wrp-book-text-arihant {
  width: auto;
  display: inline-block;
  float: left;
  padding: 10px 0px;
}
span.delivery-text{
  font-size:15px;
  font-weight: 400;
  padding-right: 10px;
  color: #686868;
}
.text-and-check-delivery-point-day-wrp {
  display: inline-block;
  float: left;
  width: auto;
}
.input-check-wrp-box-secs{
  position: relative;
  width: 180px
}

.input-check-wrp-box-secs .input-box-check-pin{
  border: none;
  border-bottom: 1px solid #686868;
  font-size: 12px;
  padding: 4px 0px 1px 15px;
  width: 100%;
  min-height: 20px;
}
.input-check-wrp-box-secs .input-box-check-pin:focus,
.input-check-wrp-box-secs .input-box-check-pin:active,
.input-check-wrp-box-secs .input-box-check-pin:hover,
.input-check-wrp-box-secs .input-box-check-pin:visited{
  outline: none;
}
.timing-check-with-pin{

}
.timing-check-with-pin span.timing-pin-text-with-area{
  font-size: 12px;
  font-weight: 500;
  display: block;
  line-height: 16px;
  padding-top:5px;
  color: #686868;
  padding-left:6px;
}
.input-check-wrp-box-secs i{
  position: absolute;
  top: 6px;
  left: 4px;
  color: #686868;
  font-size: 12px;

}
.check-btn-div-box-area-in-lol {
  width: auto;
  float: left;
  display: inline-block;
}
a.timing-check-pin-btn-arihant {
  font-size: 15px;
  color: #ef7215;
  font-weight: 500;
  padding: 6px 10px;
  display: block;
}
.price-btn-row-set-prouct-detail-arihant {
    font-family: 'Fira Sans', sans-serif;
}

.product-price-in-short-rate{
  padding-top:10px;
}
i.ruppee-icons-in-price{
  display: inline-block;
  margin-top: 5px;
  float: left;
}

i.ruppee-icons-in-price img{
  
}
h3.price-book-arihant-product {
  display: inline;
  color: #686868;
  font-size: 28px;
  font-weight: 500;
}
h3.price-book-arihant-product i{
  font-size: 25px;
  float: left;
  margin-top: 6px;
  margin-right: 4px;
}
span.offert-in-off-product-sale{
  font-size: 15px;
  color: #686868;
}

.btn-row-in-product-add-and-wishlist-arihant{
  font-family: 'Fira Sans', sans-serif;
  margin-top: 4px;
}
.wrp-row-box-btn-add-to-cart-in-product-arihant{
  display: inline-block;
  width: 160px;
}
.responsive-view-only-desk{
  display: none;
}
.desktop-none-this{
  display: none;
}
.responsive-close-btn-to-category{
  position: absolute;
  top: -35px;
  right: 0px;
  z-index: 99999;
}
a.open-side-menuss-category {
    color: #333;
    padding: 8px 14px;
    display: inline-block;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    color: #fff;
}
a.open-side-menuss-category:focus,
a.open-side-menuss-category:hover,
a.open-side-menuss-category:active{
  text-decoration: none;
}
a.open-side-menuss-category span{
  text-transform: uppercase;
  font-size: 14px;
  padding-left: 4px;
}
a.open-side-menuss-category i{
  font-size: 14px;
}

.responsive-close-btn-to-category{
  display: none;
}
.col-sm-3.thhis-in-responsive-view-and-dpdp.block-nowss{
  display: block;
}
.responsive-close-btn-to-category a{
  color: #fff;
  padding: 5px 12px;
  display: inline-block;
  background: #000;
}


a.btn-add-to-cart-product-book{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding: 8px 20px;
  display: inline-block;
}
a.btn-add-to-cart-product-book img{
  float: left;
  width: auto;
}
a.btn-add-to-cart-product-book span{
  font-size: 15px;
  font-weight: 500;
  color: #fff;
  padding: 2px 0px 0px 5px;
  display: inline-block;
}

.wrp-row-box-btn-add-to-wishlist-in-product-arihant{
  display: inline-block;
  width: auto;
}
a.btn-add-to-wishlist-product-book{
  padding: 8px 20px;
  display: inline-block;
  border: 1px solid #b3b3b3;
}
a.btn-add-to-wishlist-product-book:hover{
  background: #000;
}
a.btn-add-to-wishlist-product-book img{
  float: left;
  width: 22px;
  margin-right: 3px;
}

a.btn-add-to-wishlist-product-book img.white{
  display: none;
}

a.btn-add-to-wishlist-product-book:hover img.gray{
  display: none;
}

a.btn-add-to-wishlist-product-book:hover img.white{
  display: inline-block;
}
a.btn-add-to-wishlist-product-book i{
  color: #686868;
}
a.btn-add-to-wishlist-product-book span{
  font-size: 15px;
  font-weight: 500;
  color: #686868;
  padding: 0px 0px 0px 5px;
  display: inline-block;
}
a.btn-add-to-wishlist-product-book:hover span{
  color: #fff;
}
a.btn-add-to-wishlist-product-book:hover i{
  color: #fff;
}

a.btn-add-to-wishlist-product-book:hover{
  text-decoration: none;
}
.nav-tabs-acordings-arihant ul li a:hover {
    background: #eee;
}
.nav-tabs-acordings-arihant ul li a:hover span{
  background: #eee;
}
.nav-tabs-acordings-arihant ul li a span:after {
    position: absolute;
    content: "";
    background: #ddd;
    width: 100%;
    left: 0px;
    bottom: 0px;
    height: 0px;
}
.nav-tabs-acordings-arihant ul li a.active:hover{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%) !important;
}
.nav-tabs-acordings-arihant ul li a.active:hover span{
  background: #fff;
}
.nav-tabs-acordings-arihant ul li a.active:hover{
  background: #eee;
}
.nav-tabs-acordings-arihant ul li a:hover span:after{
  height: 2px;
}
.nav-tabs-acordings-arihant ul li a.active:hover span:after{
  height: 0px;
}
.aditional-charges-rate-wrp{
  font-family: 'Fira Sans', sans-serif;
}
.aditional-charges-rate-wrp p{
  margin: 0px;
  font-size: 13px;
  padding: 10px 0px;
}
.nav-tabs-acordings-arihant{
  font-family: 'Fira Sans', sans-serif;
  padding-top:34px;
}

.nav-tabs-acordings-arihant ul li{
}
.nav-tabs-acordings-arihant ul li a{
  position: relative;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding:0px 0px 1px 0px;
  margin-right: 12px;
}

.nav-tabs-acordings-arihant ul li a span{
  font-size: 20px;
  color: #686868;
  background: #fff;
  padding: 5px 17px 4px 17px;
  font-weight: 300;
  display: inline-block;
}

.nav-tabs-acordings-arihant ul li a.active:hover{
  cursor: default;
}
.nav-tabs-acordings-arihant ul li a.active{
  position: relative;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding: 1px;
  margin-right: 10px;
}
.padding-left30px{
  padding-left:50px;
}
.nav-tabs-acordings-arihant ul li a.active span{
  padding:4px 17px;
  color:#ef7215; 
}
.text-area-tabs-all-product-disc{

}
.text-area-tabs-all-product-disc p{
  font-size:16px;
  color: #000;
  font-weight: 300;
  font-family: 'Fira Sans', sans-serif;
}
.nav-tabs-acordings-arihant .container.tab-pane {
  padding:0px; 
  padding-right: 10px;
}
.text-area-tabs-all-product-disc ul li{
  font-weight: 300;
}


.similar-product-row-wrp.row {
  font-family: 'Fira Sans', sans-serif;
  padding-top:40px;
}
h3.similar-rpdocut-title-text{
  font-size:30px;
  padding-bottom: 20px;
  color: #000;
}
.similar-product-row-wrp.row .col-sm-2{
  padding: 0px 10px;
}
.similar-product-wrp-slide{
  text-align: center;
}
.details-similar-product{
  text-align: center;
}
.details-similar-product h3{
  font-size:11px;
  height: 30px;
  line-height: 15px;
  font-weight: 400;
  margin-top: 10px;
  overflow: hidden;
}

.price-in-center-all-set-similar{
  text-align: center;
}
.price-in-center-all-set-similar img{
  display: inline-block;
  width: 10px;
}
.price-in-center-all-set-similar span{
  display: inline-block;
  font-size: 15px;
  color:#ef7215;
  font-weight: 500;
}
.price-in-center-all-set-similar span i{
    font-size: 13px;
    color: #6e6e6e;
    margin-right: 4px;
}

.set-of-all-under-details-listss-arihants p{
  margin:0;
}
















/*=============================================================================
                        Responsive View Cart Page
================================================================================*/

.manage-row-responsive-title-asss{
  background: #fff;
  padding: 15px 0px 8px;
}
.row.manage-row-responsive-title-asss .col-6:first-child{
  padding-right: 0px;
}
.row.manage-row-responsive-title-asss .col-6:last-child{
  padding-left: 0px;
}
.mrgrn-manage-rpoduct-all-responsive-usses{
  margin-top: 15px;
}
.responsive-view-this{
  display: none;
}

.col-12.row-product-wrp-all-res-arihant-books{
  padding:0px;
  margin-bottom: 10px;
  background: #ffffff;
  box-shadow: 1px 1px 7px #ccc;
}
.row-of-product-main-item{
  margin-left: 16px;
  margin-right: 8px;
  margin-top: 16px;
}
.product-section-arihant-resposive-v {
  width: 73%;
  float: left;
}
.product-section-arihant-resposive-v h3.name-of-product-cart-resp-view{
  font-size: 14px;
  font-family: 'Fira Sans', sans-serif;
  margin:0px;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 16px;
  white-space: nowrap;
}
.product-section-arihant-resposive-v  h3.price-is-all-use-in-cart-res{
  font-size: 15px;
  margin: 0px;
}
.product-section-arihant-resposive-v h3.price-is-all-use-in-cart-res{
  font-size: 16px;
  margin: 0px;
  padding: 6px 0px;
}
.product-section-arihant-resposive-v h3.price-is-all-use-in-cart-res i{
  float: left;
  margin-top: 3px;
  font-size: 15px;
  margin-right: 1px;
}

.product-section-arihant-resposive-v span{
  font-size:12px;
  font-family: 'Fira Sans', sans-serif;
}
.details-images-cart-page-arihant {
  float: left;
  width: 26%;
  text-align: center;
}
.details-images-cart-page-arihant img{
  width: 80%;
  padding:0px 0px;
}

.res-views-details-book-about-cart-shopping-pg {
  width: 70%;
  float: left;
  margin-left: 15px;
}
.res-views-wrp-details-about-shopping-cart-pg {
  font-weight: 400;
  font-family: 'Fira Sans', sans-serif;
  color: #686868;
  padding: 2px 0px;
  font-size: 11px;
}

span.res-views-first-table-cart-page-arihant {
  width: 70px;
  display: inline-block;
}
span.res-views-second-details-cart-page-arihant {
  width: 40px;
  display: inline-block;
}


.quantity-product-wrp-resp-arihant-allsse {
    float: left;
    width: 25%;
}
.quantity-product-wrp-resp-arihant-allsse select{
  border:none;
  padding: 1px 4px;
  background: #fff;
  font-size: 12px;
}

.quantity-product-wrp-resp-arihant-allsse  .wrp-all-main-sepeopw{
  border: 1px solid #ddd;
  margin: 7px 0px;
  display: inline-block;
}
.quantity-product-wrp-resp-arihant-allsse span .name-qty-spsps{
  font-size: 13px;
  padding: 4px 0px 4px 6px;
  display: inline-block;
}

.quantity-product-wrp-resp-arihant-allsse select:active,
.quantity-product-wrp-resp-arihant-allsse select:focus,
.quantity-product-wrp-resp-arihant-allsse select:hover{
  outline: none;
}



.row-product-detailsss-resp-arihant {
  border-top: 1px solid rgb(240, 240, 240);
  margin-top: 5px;
  font-family: 'Fira Sans', sans-serif;
}
.product-detail-rep-btn-use-arihant {
  float: left;
  width: 50%;
  border-right:1px solid rgb(240, 240, 240);
}

.product-detail-rep-btn-use-arihant a{
  display: block;
  text-align: center; 
  font-size: 13px;
  color:#333;
  padding: 10px 0px;
}
.remove-btn-wrapper-res-wrp-slals{
  float: left;
  width:50%;
  text-align: center;
}
.remove-btn-wrapper-res-wrp-slals a{
  display: block;
  color:#333;
  font-size: 13px;
  padding: 10px 0px;

}

.product-detail-rep-btn-use-arihant a:hover,
.product-detail-rep-btn-use-arihant a:active,
.product-detail-rep-btn-use-arihant a:focus,
.remove-btn-wrapper-res-wrp-slals a:focus,
.remove-btn-wrapper-res-wrp-slals a:hover,
.remove-btn-wrapper-res-wrp-slals a:active{
  text-decoration: none;
}


.row.manage-coupon-code-resp-rowss{
  background: #fff;
  padding: 12px 0px;
  box-shadow: 1px 1px 7px #ccc;
  margin-top: 20px;
}
.row.manage-coupon-code-resp-rowss span{
  margin-bottom: 10px;
}
.row.manage-coupon-code-resp-rowss input.coupon-check-input-box{
  width:60%;
}
.row-manage-close-all-this-price-total-res-arihant.row {
  background: #fff;
  box-shadow: 1px 1px 7px #ccc;
  margin-top: 15px;
  padding-top: 10px;
  padding-bottom: 10px;
}
.row-manage-close-all-this-price-total-res-arihant.row .wrp-table-box-bg-shhs{
  background: none;
  padding: 0px;
}
.btn-all-set-last-arihant-resp.row {
    margin-top: 20px;
    margin-bottom: 20px;
}
.countinue-btn-all-responsive-trs{
  float: left;
  width: 50%;
}
.this-view-responsivesss{
  display: none;
}
.this-view-desktopss{
  display: block;
}
.countinue-btn-all-responsive-trs a{
  color: #333;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  display: inline-block;
  border-bottom: 1px solid #8a8a8a;
  padding: 10px 4px 7px 5px;
}
.btn-all-resp-checkout-proceed-hds{
  float: left;
  width: 50%;
}
.btn-all-resp-checkout-proceed-hds a{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  border: none;
  padding: 6px 10px;
  color: #fff;
  text-transform: uppercase;
  cursor: pointer;
  font-size: 16px;
  font-family: 'Oswald', sans-serif;
  display: block;
}


.countinue-btn-all-responsive-trs a:hover,
.countinue-btn-all-responsive-trs a:active,
.countinue-btn-all-responsive-trs a:focus,
.btn-all-resp-checkout-proceed-hds a:hover,
.btn-all-resp-checkout-proceed-hds a:active,
.btn-all-resp-checkout-proceed-hds a:focus{
  text-decoration: none;  
}
















/*================================================================================================
                                        Career Page
==================================================================================================*/

.image-in-top-manage-with-career-arihant{
  width:100%;
}
.image-in-top-manage-with-career-arihant img{
  width: 100%;
}


.manager-row-in-passion-career-pg{
  padding-top:40px;
}

.text-center.border-title-passion-career {
    border-bottom: 1px solid #b9b9b9;
}
h2.title-passion-career-section {
    display: inline-block;
    margin: 0px;
    position: relative;
    font-family: 'Fira Sans', sans-serif;
    padding-bottom: 14px;
    font-weight: 600;
    font-size: 38px;
    color: #000;
}
h2.title-passion-career-section:after {
    position: absolute;
    content: "";
    width: 100%;
    background: #000;
    height: 1px;
    top: 100%;
    left: 0px;
}
.margin-none-in-passion-inbox-arihantss{
  margin: 0px;
  padding: 45px 30px 20px 30px;
}


.wrp-passion-sec-box-career-page{

}
.wrp-passion-sec-box-career-page a{
  color: inherit;
}
.wrp-passion-sec-box-career-page a:hover,
.wrp-passion-sec-box-career-page a:active,
.wrp-passion-sec-box-career-page a:focus{
  text-decoration: none;
}
.passion-small-icon-wrp-pasion{
  text-align: center;
  font-family: 'Fira Sans', sans-serif;
}
.passion-small-icon-wrp-pasion img{
  width: 86px;
  margin:0px auto;
}
.wrp-passion-sec-box-career-page a:hover .passion-small-icon-wrp-pasion img.not-active{
  display: none;
}
.passion-small-icon-wrp-pasion img.active{
  display: none;
}
.wrp-passion-sec-box-career-page a:hover .passion-small-icon-wrp-pasion img.active{
  display: block;
}
.wrp-passion-sec-box-career-page a:hover .title-icon-passion-career-arihant h4{
  color:#26a97b;
}

.title-icon-passion-career-arihant {
    text-align: center;
    padding: 15px 0px;
}
.title-icon-passion-career-arihant h4{
  font-size:18px;
}


.manager-row-in-work-culture-career-pg{
  padding-bottom: 30px;
  padding-top:40px;
  font-family: 'Fira Sans', sans-serif;
}

.title-text-changess-pera-disc-work-culture p{
  font-size: 14px;
  text-align: center;
  padding-top:20px;
}


.slider-arihant-career-culture-user .carousel-indicators{
  bottom:-40px;
}

.slider-arihant-career-culture-user .carousel-indicators li{
  width: 10px;
  height:10px;
  margin:0px 3px;
  background: #cacaca;
  border-radius:  50%;
  cursor: pointer;
}
.slider-arihant-career-culture-user .carousel-indicators li.active{
  background: #ed7215;
}




.featured-jobs{
  position: relative;
  font-family: 'Fira Sans', sans-serif;
}
.featured-image-bg-offical-jobsd{
  width: 100%;
}
.featured-image-bg-offical-jobsd img{
  width:100%;
}

.featured-jobs-posi-arihant-careers{
  position: absolute;
  top: 40px;
  left: 0px;
  width: 100%;
}




.job-table-wrapper-career-arihant{
  width:60%;
  margin: 0px auto;
  padding-right: 7%;
  font-family: 'Fira Sans', sans-serif;
}
.wrp-row-all-use-other-job-career-feature{
  padding: 7px 0px;
}

.header-part-in-box-job-career-arihant {
  width: 40%;
  float: left;
}
.header-part-in-box-department-career-arihant {
  width: 25%;
  float: left;
}
.header-part-in-box-location-career-arihant {
  float: left;
  width: 25%;
}
.wrp-row-all-use-other-job-career-feature.body-table-jobs-manage-arihant {
  padding: 25px 0px;
  border-bottom: 1px solid #c3c5c5;
}
span.detail-of-head-table-job {
  font-size: 12px;
  color: #686868;
  text-transform: uppercase;
}

h4.details-jobs-name-arihant-career-feature{
  margin: 0px;
  font-size: 18px;
}
span.name-of-department-arihant-jobs-career {
    font-size: 12px;
    color: #ed7215;
}
span.job-location-of-arihant-career-feature {
    font-size: 14px;
    color: #000;
}


.publish-with-us-career-arihant-wrp{
  position: relative;
}
.wrp-all-rel-desk-image{
  width: 100%;
}
.wrp-all-rel-desk-image img{
  width: 100%;
}
.this-is-posi-wrp-div{
  width: 100%;
  position: absolute;
  top: 40px;
  left: 0px;
}

.details-of-manage-conect-wrp{
  padding-left: 40px;
}
h3.publish-with-text-wrp-ss{
  font-family: 'Oswald', sans-serif;
  font-size: 36px;
  color: #fff;
  font-weight: 600;
  letter-spacing: 1px;
}
.details-of-manage-conect-wrp p{
  color: #fff;
  font-size: 12px;
  font-weight: 400;
}


.position-this-button-to-publish-career-arihant-contact{
  font-family: 'Oswald', sans-serif;
  position: absolute;
  right: 10%;
  bottom: 35%;
}
.button-contact-career-wrp-spd{

}
a.button-wrp-ss-career-arihant-contact{
  background: white;
  padding: 10px 30px;
  display: inline-block;
  font-weight: 600;
  color: #ad3d87;
  font-size: 22px;
  border-radius: 30px;
  box-shadow: 0px 0px 8px #00000052;
}
a.button-wrp-ss-career-arihant-contact:hover,
a.button-wrp-ss-career-arihant-contact:focus,
a.button-wrp-ss-career-arihant-contact:active{
  text-decoration: none;
}


.row.Recruitment-process-career {
  padding-top: 50px;
  padding-bottom: 60px;
}
.text-center.border-title-recruitment-career {
  border-bottom: 1px solid #b9b9b9;
}

h2.title-recruitment-career-section {
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Fira Sans', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  font-size: 38px;
  color: #000;
}

h2.title-recruitment-career-section:after {
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}
.Recruitment-process-career .tab-content{
  padding-top: 40px;
}

.applying-to-arihant-process-career-wrp {
  font-family: 'Fira Sans', sans-serif;
  padding-top: 20px;
}
.applying-to-arihant-process-career-wrp h4{
  font-size: 20px;
  color: #ed7215;
  text-align: center;
  font-weight: 500;
}
.applying-to-arihant-process-career-wrp p{
  font-size: 12.5px;
  letter-spacing: -0.2px;
  text-align: center;
  color: #000000;
}


.tabs-on-career-all-applying{
  font-family: 'Fira Sans', sans-serif;
}

.wrp-all-tab-applying-career-ul-arihant{
  padding: 0px 20px;
}

.wrp-all-tab-applying-career-ul-arihant ul li{
  width: 20%; 
}
.wrp-all-tab-applying-career-ul-arihant ul li a{
  display: block;
  background: #f4f4f4;
  text-align: center;
  font-weight: 500;
  padding: 10px 0px;
  color: #000;
}
.wrp-all-tab-applying-career-ul-arihant ul li a.active{
  background: #686868;
  color: #fff;
}
.wrp-main-wrp-all-sec-applying-use-box-career-ops{
  font-family: 'Fira Sans', sans-serif;
}

.recruitment-process-box-incareer-width-20{
  width:25%;
  float: left;
  padding-left: 15px;
  padding-right: 15px;
}

.image-wrp-applying-recruitment-section-career{
  width: 100%;
}
.image-wrp-applying-recruitment-section-career img{
  
}

.manage-detail-applying-recruitment-section-career{
  padding-top: 15px;
}
.manage-detail-applying-recruitment-section-career h4{
  font-size: 18px;
  color: #ed7215;
  font-weight: 500;
}
.manage-detail-applying-recruitment-section-career p{
  font-size: 14px;
  color: #686868;
  padding-right: 0px;
}



.manage-section-short-on-time{
  padding-bottom: 30px;
}
.manage-short-one-wrp-text-content-cv{
  text-align: center;
}
.manage-short-one-wrp-text-content-cv h3{
  font-size: 25px;
  font-family: 'Oswald', sans-serif;
}
.manage-short-one-wrp-text-content-cv a{
  font-family: 'Fira Sans', sans-serif;
  font-size: 16px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding: 6px 20px;
  color: #fff;
  margin-top: 15px;
  margin-bottom: 10px;  
  display: inline-block;
  border-radius: 20px;
}
.manage-short-one-wrp-text-content-cv p{
  font-family: 'Fira Sans', sans-serif;
  font-size: 14px;
  color: #686868;
}








.contact-us-arihant{
  width: 100%;
}
.image-in-top-manage-with-contact-us-arihant{

}
.image-in-top-manage-with-contact-us-arihant img{
  width: 100%;
}

.details-box-of-cntact-arihant-books{
  padding:30px 45px;
  margin-top:-85px;
  font-family: 'Fira Sans', sans-serif;
}
.row.manage-padding-in-this{
  margin:0px;
}
.con-box-wrp-this-manage-rows {
  border: 1px solid #d4d4d4;
  background: #fff;
  padding: 30px 45px;
  text-align: center;

  transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
}
.con-box-wrp-this-manage-rows:hover {
  box-shadow: 2px 4px 10px #00000057; 
}
.icon-wrp-contact-uss{

}
.icon-wrp-contact-uss img{

}
.contact-us-box-wrp-content-disc{

}
.contact-us-box-wrp-content-disc h3{
  font-size: 28px;
  position: relative;
  display: inline-block;
  padding: 15px 0px 10px 0px;
}
.contact-us-box-wrp-content-disc h3:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #000;
  left: 0px;
  bottom: 0px;
}

.contact-us-box-wrp-content-disc p{
  font-size: 16px;
  color: #686868;
  margin-top:10px;
}

.contact-all-details-bottom-box-content{
  min-height: 85px;
  padding-top: 20px;
}
.contact-all-details-bottom-box-content a{
  font-size: 22px;
  color: #000;
  font-weight: 500;
}

.contact-all-details-bottom-box-content h4{
  font-size: 22px;
  margin:0px;
  color: #000;
  font-weight: 500;
}
.contact-all-details-bottom-box-content h4:first-child{
  margin-bottom: 10px;
}



.contact-all-details-bottom-box-content a:hover,
.contact-all-details-bottom-box-content a:active,
.contact-all-details-bottom-box-content a:focus{
  text-decoration: none;
}












.container-fluid.manage-padding-b2b-arihany.mobile-views{
  display: none;
}
.all-address-wrp-contact-arihant.mobile-view{
  display: none;
}
.all-address-wrp-contact-arihant.desk-view{
  display: block;
}
.all-address-wrp-contact-arihant{
  padding-top:30px;
}
.all-address-wrp-contact-arihant .row{
  margin:0px;
}
.padding-right-none-desktop-view0conn-address{
  padding-right: 0px;
}
.box-map-wrpsthis{
  min-height: 500px;
  width: 100%;
  border-right: 0px;
  border:1px solid #ccc;
}
.padding-none-in-desktop-view-conn-address{
  padding-left: 0px;
  padding-right: 0px;
  border:1px solid #ccc;
  border-left: 0px;
}
.padding-right-none-desktop-view0conn-address .container{
  padding: 0px;
}

.reach-off-head-proper-desk-contact-arihant {
  float: left;
  width: 20%;
  text-align: center;
}
.reach-off-head-proper-desk-contact-arihant img{
  width:45px;
}
.reach-us-wrp-text-arihant-contact{
  float: left;
  padding: 0px 20px;
  text-align: center;
  width: 80%;
}
.border-wrp-text-reach-us-contact-arihant{
  border-bottom:1px solid #b9b9b9;
}
h2.reach-us-contact-text{
  display: inline-block;
  margin: 0px;
  line-height: 40px;
  font-size: 28px;
  font-weight: 600;
  position: relative;
  font-family: 'Oswald', sans-serif;
}

h2.reach-us-contact-text:after{
  position: absolute;
  content: "";
  bottom: 0px;
  left: 0px;
  width: 100%;
  height:1px;
  background: #000;
}


.user-icon-show-contact-fl-desk-top-arihant{
  padding-top: 20px;
}

.nav-office-addres-tabs-offices li{
  width: 100%;
}

.nav-office-addres-tabs-offices li a{
  width: 100%;
  background: #fff;
  padding-left: 25px;
  padding-top: 15px;
  padding-bottom: 15px;
  color: #000;
}
.nav-office-addres-tabs-offices li a.nav-link.active {
  background: #f5f5f5;
}
ul.nav.nav-office-addres-tabs-offices {
  margin-top: 25px;
  font-family: 'Fira Sans', sans-serif;
}
.nav-office-addres-tabs-offices li a h3{
  color: #000;    
  font-size: 22px;
  letter-spacing: 0.2px;
  margin-bottom: 5px;
}
.nav-office-addres-tabs-offices li a p{
  color: #686868;    
  margin-bottom: 0px;
  font-size: 14px;
  margin-top: 4px;
}
.nav-office-addres-tabs-offices li a span{
  color: #e87215;
}


.manage-row-in-details-address-office-arihants{
  padding-top: 20px;
  padding-left: 15px;
}

.manage-row-in-details-address-office-arihants .row{
  margin:0px;
}


.manage-wrp-all-users-sets-bottom-location-contacts{
  text-align: center;
  display: inline-block;
  margin: 0px auto;
}
.image-images-iconsoso {
  width: 40px;
  float: left;
}
.name-of-image-bottom-so-address-locations {
  float: left;
  margin-top: 10px;
  color: #686868;
}
.name-of-image-bottom-so-address-locations.track{
  width:100px;
}
.name-of-image-bottom-so-address-locations.manage{
  width:115px;
}
.name-of-image-bottom-so-address-locations.transactions{
  width:125px;
}
.name-of-image-bottom-so-address-locations.faq{
  width: 40px;
}


.wrapper-showss-contacts-details-list{
  padding:30px 0px 20px;
  font-family: 'Fira Sans', sans-serif;
}
.sales-and-support-offices-section{

}

.title-manage-sales-and-support {
  text-align: center;
  border-bottom: 1px solid #d6d6d6;
}
.title-manage-sales-and-support h2{
  position: relative;
  margin: 0px;
  font-weight: 500;
  font-size: 28px;
  color: #e87215;
  display: inline-block;
  padding-bottom: 20px;
}
.title-manage-sales-and-support h2:after{
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #000;
  bottom: 0px;
  left: 0px;
}


.row.manage-all-details-arihant-contacts-lists{
  margin: 0px;
  padding: 20px 10px 0px; 
}
.manage-all-number-email-arihant-books-contacts{
  font-family: 'Fira Sans', sans-serif;
  margin-bottom: 25px;
}
.manage-all-number-email-arihant-books-contacts h3{
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
}
.manage-all-number-email-arihant-books-contacts p{
  font-size: 12px;
  margin:0px;
  color: #686868;
  letter-spacing: -0.2px;
}
.bg-list-all-conatiner-use-contact-ss{
  background: url(../images/contact-bg-all-list-arihant.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}







.b2b-service-wrapper-arihant{
  font-family: 'Fira Sans', sans-serif;
}
.reload-wrp-b2b-box-wrapper-arihant{

}
.reload-wrp-b2b-box-wrapper-arihant .row{
  margin:0px;
}
.title-b2b-service-arihant {
  text-align: center;
  border-bottom: 1px solid #d6d6d6;
}
.title-b2b-service-arihant h2{
  position: relative;
  margin: 0px;
  font-weight: 500;
  font-size: 28px;
  color: #e87215;
  display: inline-block;
  padding-bottom: 20px;
}
.title-b2b-service-arihant h2:after{
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #000;
  bottom: 0px;
  left: 0px;
}
.title-text-b2b-services-arihant-books{
  text-align: center;
  font-size: 14px;
  padding: 20px 30px;
}

.manage-padding-b2b-arihany{
  padding:0px;
}
.col-sm-6.col-12.center-all-and-pa-ri-o-arihant-b2b{
  padding:0px;
}
.center-all-and-pa-ri-o-arihant-b2b{
  display: flex;
  display: -ms-flexbox;
}
.arihant60-per-deta-b2b-services{
  width: 60%;
  float: left;
}
.arihant60-per-deta-b2b-services img{
  width: 100%;
}
.arihant40-per-deta-b2b-services {
  background: linear-gradient(0deg,#FF3448 0%,#FF9B26 100%);
  width: 40%;
  float: left;
}
.wrp-all-detail-b2b-box-arihant{
  text-align: center;
  padding: 30px 0px;
}
.wrp-all-detail-b2b-box-arihant img{
  
}
.text-manage-box-b2b-service-arihant-wrp{
  text-align: center;
}
.text-manage-box-b2b-service-arihant-wrp h3{
  font-family: 'Oswald', sans-serif;
  font-size: 26px;
  margin-bottom: 15px;
  color: #fff;
}
.text-manage-box-b2b-service-arihant-wrp a{
  color: #fff;
  font-size: 15px;
}
.text-manage-box-b2b-service-arihant-wrp a:hover,
.text-manage-box-b2b-service-arihant-wrp a:focus,
.text-manage-box-b2b-service-arihant-wrp a:active{
  text-decoration: none;
}
.manage-wrp-line-b2b-service-arihant-book {
  width: 60%;
  height: 2px;
  background: #fff;
  margin: 30px auto 0px;
}



























/*=========================================================================================================================================================
																	Responsive.css 
=========================================================================================================================================================*/


@media(max-width:768px){
	*{
		padding:0;
		margin:0;
	}
	
	.overlay-menu-close.menu-ovr-act{
	  z-index: 999;
	  display: block;
	  transition: 0.3s all ease-in-out;

	}
	.col-12.col-sm-12.col-lg-12.col-xl-9.title-text-changess-pera-disc-work-culture br{
	  display: none;
	}

	.logo-wrapper img{
		width:35px;
	}
	.menu-bar-wrp{
		display: none;
	}
	.main-menu-wrp{
		padding:0px 0px;
	}
	.bg-wrapper-sec{
		background: url(../images/bg-mobile-slide.png);
		min-height: 400px;
		background-size:cover;
		background-repeat: no-repeat;
		background-position: center;
	}
	.menu-bar-wrp ul li a{
		font-size:10px;
	}
	ul.menu-wrp-link-main{display: none;}
	.img-wrapper-connect{
		text-align: center;
	}
	.menu-wrp-all-users-com ul li a{
		padding: 13px 6px;
	}

	.menu-wrp-all-users-com ul li a span {
		display: none;
	}

	.search-header-close.header-close-active-btn{
		z-index: 9999;
	}

	li.none-add-class-responsive.display-none-now a{
		display: none;
	}

	ul.social-icon-wrp-connect{
		width:100%;
		border:none;
	}
	.responsive-center-text{
		text-align: center;
	}
	.img-wrp-call-in,h3.call-here-number{
		float: none;
		display: inline-block;
	}

	.phone-connect-img-here{
		padding-top:20px;
	}


	.image-wrapper-footer-logo {
	    margin-bottom: 25px;
	}
	.main-div-box-link-footer {
	    margin-bottom: 20px;
	}
	.box-categories-wrp a{
		padding: 25px 15px;
		margin-bottom: 0px;
		display: block;
	}
	.bg-color-fill-categories h3{
		padding: 16px 15px;
	    font-weight: 300;
	    line-height: 32px;
	    font-size: 22px;
	}
	a.bg-color-fill-categories.yellow-color-fill-bg.responsive-red-bg:hover{
		background: #ea1d32;
	}
	a.bg-color-fill-categories.red-color-fill-bg.responsive-yellow-bg{
		background: #f38c13;
	}
	.bg-color-fill-categories h3 a{
		font-size: 22px;
		padding:40px 1px;
	}
	.programs-disc-pera-about{
		display: none;
	}

	.mrgn-top-respo-programs{
	  margin-top:26px;
	}
	.wrp-box-programs{
		margin-top:0px;
	}
	.mrgn-top-respo-programs .col-md-6.col-lg-3.col-6:nth-child(1),
	.mrgn-top-respo-programs .col-md-6.col-lg-3.col-6:nth-child(3){
		border-right: 2px solid #b8b8b836;
	}

	.mrgn-top-respo-programs .col-md-6.col-lg-3.col-6:nth-child(3),
	.mrgn-top-respo-programs .col-md-6.col-lg-3.col-6:nth-child(4){
		border-top:2px solid #b8b8b836;
	}

	.wrp-box-programs{
		border-right: 0px;
	}
	.wrp-box-text-sec-disc h3{
		font-size:24px;
	}
	.wrp-box-icon-size img{
		width:50px;
	}
	.wrp-box-text-sec-disc p{
		font-size:10px;
		margin-bottom: 0px !important;
		min-height: 50px;
	}

	.categories-section{
		background: none;
		padding-bottom: 0px;
		margin-top: 0px;
		padding-top: 0px;
	}

	.manage-count-wrp-box{
		padding-top:30px;
		padding-bottom:0px;
	}

	p.categories-section-disc-title-pera{
		display: none;
	}

	.col-lg-4.col-sm-6.col-6.responsive-padding-none {
		padding: 0px !important;
	}
	.bg-color-fill-categories{
		border-radius: 0px !important;
		margin:0px;
	}

	.responsive-yellow-bg{
		background-color: #ff9b26;
	}
	.responsive-red-bg{
		background-color: #ff3448;
	}




	p.journey-section-disc-title-pera{
		display: none;
	}
	.manage-count-wrp-box:after{
		display: none;
	}
	.manage-count-wrp-box h2{
		color:#333;
	}
	.manage-count-wrp-box h2 span{
		color:#333;
		font-size: 34px;
	}
	.manage-count-wrp-box span{
		font-size:14px;
	}

	.display-none-responsive{
		display: none;
	}

	p.connect-sec-wrp-disc-pera{
		display: none;
	}
	.center-responsive-div{
		text-align: center;
	}
	ul.social-icon-wrp-connect{
		display: inline-block;
		margin: 0px auto;
		width:auto;
		float:none;
	}
	ul.social-icon-wrp-connect li{
		width:auto;
	}
	ul.social-icon-wrp-connect li a {
	    padding: 15px 10px;
	    display: inline-block;
	}

	.img-wrp-call-in{
		display: none;
	}
	.responsive-view-icon-this{
		display: block;
    	margin-top: 10px;
	}
	.responsive-view-none{
		display: none;
	}
	.responsive-none-view{
		display: none;
	}
	.responsive-view-not-desktop{
		display: block;
	}
	.responsive-icon-side-po{
		position: absolute;
		width: 52px;
		left: 5px;
		top:-8px;
	}
	.responsive-icon-side-po img{
		width: 100%;
	}
	.pattern-disc-headding-footer{
		text-align: center;
	}
	.pattern-disc-headding-footer h3{
		margin: 0px;
		position: relative;
		font-family: 'Oswald', sans-serif;
		padding-bottom: 0;
		padding: 6px 4px;
		font-weight: 500;
		text-transform: uppercase;
		font-size: 20px;
		display: inline-block;
		color: #939797;
	}

	.link-sec-footer-respo{
		text-align: center;
	}
	.link-sec-footer-respo a {
	    display: inline-block;
	    color: #333;
	    padding: 0px 6px 0px 0px;
	    font-size: 15px;
	    border-right: 1px solid #333;
	}
	.link-sec-footer-respo a:last-child{
		border:none;
	}

	.posi-static-respons{
		position: static;
	}
	ul.menu-wrp-link-main{
		display: block;
		position: fixed;
		left: -800px;
		top: 0;
		transition: 0.3s all ease-in-out;
		height: 100vh;
		border: 0px !important;
		z-index: 9999;
		width: 70%;
		background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
	}

	ul.menu-wrp-link-main.menu-active-all-sess{
		left:0;
	}
	ul.menu-wrp-link-main li{
		float:none !important;
	}
	ul.menu-wrp-link-main li.link-menu-li.menu-big-under a.menu-link-anchor:after{
		top:20px;
		left:80%;
	}
	ul.under-menu-wrp-links{
		position: static;
	}
	ul.menu-wrp-link-main li.link-menu-li.menu-big-under:hover ul.under-menu-wrp-links{
		display: none;
	}
	ul.under-menu-wrp-links.menu-set-all-ses{
		display: block !important;
	}
	.logo-wrapper{
		padding: 2px 0px;
	}

	.connect-section{
		position: relative;
	}
	.dispay-set-resposive-img-phone{
		display: none;
		position: absolute;
		right: 6px;
	    top: 44%;
	    width: 75px;
	}
	.dispay-set-resposive-img-phone img{
		width: 100%;
	}
	.respo-sive-some-changes{
		position: absolute;
		left: 5px;
		display: inline-block;
		top: 80%;
		width: auto;
		margin-top: 0px;
	}
	.respo-sive-some-changes li a{
		padding:4px 3px;
		font-size: 10px;
	}




	span.login-with-other-ways-text:before{
		left: -40px;
		width: 30px;
		top:10px;
	}
	span.login-with-other-ways-text:after{
		right: -40px;
		width: 30px;
		top:10px;
	}
	.this-div-use-box-search{
		width: 75%;
	}
	.wrapper-form-header-search-input{
		min-height: 58px;
	}
	button.close-all-searchdiv-box,
	input.input-box-search-in-header,
	button.btn-in-open-search-header-search{
		top: 6px;
	}
	span.wrapper-close-btn-search-header-all{
		min-width: 38px;
	}
	.main-menu-wrp.search-active-now-row.now-hide-menuall .user-menu-wrp{
		pointer-events: none;
	}
	input.input-box-search-in-header{
		padding: 0 12px 0 10px;
	}
	button.btn-in-open-search-header-search{
		display: none;
	}

	.menu-main-min-height{
		min-height: 70px;
	}

	.this-desktop-view-hide{
		display: none;
	}
	.responsive-view-this{
		display: block;
	}
	h3.shoppig-cart-title-wrp-arihant{
		font-size:22px;
	}
	p.text-quetion-in-shopping-cart-page-arihant{
		font-size: 14px;
	}
	span.call-us-shopping-cart-pg-arihant{
		font-size: 10px;
	}
	.shopping-wrp{
		padding: 0px 0px 20px;
	}
	div#wrapper.wrp-cart{
		background: #f1f2f4;
	}
	div#wrapper.wrp-cart .footer{
		background: #fff;
	}
	div#wrapper.wrp-cart .footer-copyright{
		background: #f1f2f4;
	}
	.col-sm-9.padding-left30px{
		padding-left: 15px;
	}
	.category-main-wrp .col-sm-9.padding-left30px{
		padding-top:20px;
	}
	.responsive-none-thiss{
		display: none;
	}

	.responsive-view-display-none{
		display: none;
	}
	.manage-filter-row-in-front{
		padding-top:20px;
	}

	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(1),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(3),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(5),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(7),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(9),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(11),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(13),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(15),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(17),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(19),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(21),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(23),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(25),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(27),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(29),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(31),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(33),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(35),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(37),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(39),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(41){
	    padding-right: 5px;
	    padding-left: 10px;
	}
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(2),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(4),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(6),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(8),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(10),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(12),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(14),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(16),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(18),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(20),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(22),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(24),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(26),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(28),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(30),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(32),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(34),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(36),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(38),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(40),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(42),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(44),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(46),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(48),
	.product-list-group-row .col-lg-3.col-sm-6.col-6:nth-child(50){
	    padding-left: 5px;
	    padding-right: 10px;
	}
	.manage-filter-row-in-front .col-sm-3.col-6.text-center:first-child{
		padding-right: 5px;
		padding-left: 10px;
	}
	.manage-filter-row-in-front .col-sm-3.col-6.text-center:last-child{
		padding-left: 5px;
		padding-right: 10px;
	}
	.exprole-wrp-btn-in-product-div{
		width:100%;
		margin-bottom: 10px;
	}
	.heart-like-btn{
		display: none;
	}
	.add-to-cart-btn-product-in-arihant{
		width: 100%;
	}




	.category-main-wrp{
		padding:0px 0px 30px 0px;
	}
	.category-main-wrp .container-fluid{
		padding-left: 15px;
	}
	.page-row-filter{
		display: none;
	}
	.product-disc-details-in-arihant{
		padding-left: 0px;
		padding-top:20px;
	}
	.product-disc-details-in-arihant h3.arihant-product-name{
	    font-size: 22px;
	    margin-bottom: 0px;
		font-weight: 500;
		padding:0px 6px;
	}
	span.product-create-by-arihant{
		padding: 5px 6px 10px 6px;
	}
	span.first-table-cart-page-arihant{
		width: 35%;
	}
	span.second-details-cart-page-arihant{
		width: 20%;
	}
	.download-wrp-width-this-arihant,
	.download-ebook-arihant-book-wrp{
		width:50%;
		float: left;
	}
	.this-view-desktopss{
		display: none;
	}
	.this-view-responsivesss{
		display: block;	
	}
	.download-some-rear-details-products{
		padding:10px 0px;
	}
	.share-item-product-book-details span,
	.share-item-product-book-details a{
		font-size: 14px;
	}
	.product-arihant-under-details {
	    padding: 0px 5px;
	}
	.product-price-in-short-rate{
		padding-bottom: 10px;
		padding-top:0px;
	}
	.product-price-in-short-rate h3.price-book-arihant-product{
		font-size: 50px;
	}
	.product-price-in-short-rate h3.price-book-arihant-product i{
		font-size: 44px;
	}
	span.offert-in-off-product-sale{
		font-size: 20px;
	}
	.nav-tabs-acordings-arihant ul li a span{
		font-size: 12px;
		padding: 4px 17px 4px 17px;
	}
	.nav-tabs-acordings-arihant ul li a{
		padding:1px 1px 1px 1px;
		margin-right: 6px;
	}
	.nav-tabs-acordings-arihant ul li a.active{
		padding:1px 1px 1px 1px;	
		margin-right: 6px;
	}
	.nav-tabs-acordings-arihant ul li a.active span{
		font-weight: 500;
	}
	.text-area-tabs-all-product-disc p{
		font-size: 14px;
	}
	.text-area-tabs-all-product-disc{
		font-size: 12px;
    	padding-left: 15px;
	}
	.nav-tabs-acordings-arihant ul li a.active span{
		padding:4px 17px 4px 17px;
	}
	h3.similar-rpdocut-title-text{
		font-size: 22px;
	}
	.similar-product-row-wrp.row .col-sm-2.col-6{
		margin-bottom: 20px;
		padding:0px 15px;
	}
	.similar-product-wrp-slide img{
		width: 100%;
	}
	.details-similar-product h3{
		margin-bottom: 0px;
	}
	.wrp-row-box-btn-add-to-cart-in-product-arihant{
		float: left;
		width: 50%;
		padding-right: 10px;
	}
	.wrp-row-box-btn-add-to-cart-in-product-arihant a.btn-add-to-cart-product-book{
		display: block;
    	text-align: center;
	}
	.wrp-row-box-btn-add-to-wishlist-in-product-arihant{
		float: left;
		width: 50%;
	}
	.wrp-row-box-btn-add-to-wishlist-in-product-arihant a.btn-add-to-wishlist-product-book{
		display: block;
		text-align: center;
	}

	.responsive-view-only-desk{
		display: block;
	}
	.col-12.responsive-view-only-desk{
		padding-bottom: 10px;
	}
	.left-arrow-side-arihant-back-button {
	    width:80%;
	    float: left;
	}
	.left-arrow-side-arihant-back-button a{
		background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
		color: #fff;
		display: inline-block;
		padding: 2px 10px;
	}
	.left-arrow-side-arihant-back-button a i{
		font-size: 24px;
		float: left;
		padding: 2px 0px 2px 0px;
	}
	.left-arrow-side-arihant-back-button a svg{
		height: 32px;
   		float: left;
	}
	.left-arrow-side-arihant-back-button a span{
		color: #fff;
		display: inline-block;
		font-size: 15px;
		padding:4px 3px 5px 10px;
		text-transform: uppercase;
		float: left;
	}
	.home-button-back-wrp-responsive {
	    float: left;
	    width: 20%;
	}
	.home-button-back-wrp-responsive a{
		font-size: 16px;
		display: block;
		padding:0px;
		text-align: center;
		color: #fff;
	}
	.home-button-back-wrp-responsive a img{
		width: 32px;
	}


	.user-menu-wrp a.menu-dots-img-wrp{
		padding:14px 14px 14px 4px;
	}


	.thhis-in-responsive-view-and-dpdp{
		position: absolute;
		right: -999px;
		display: none;
		background: #fff;
		padding: 0px;
		z-index: 999;
		width: 92%;
		transition: 0.3s all ease-in-out;
	}
	.thhis-in-responsive-view-and-dpdp .side-menu-bar{
		z-index: 99999;
   		position: relative;
	}
	.thhis-in-responsive-view-and-dpdp .overlay-addiidd.active-close-sides{
		position: fixed;
		background: #00000045;
		right: 0;
		top:0;
		z-index: 9999;
		width: 100%;
		height: 100vh;
		overflow: hidden;
	}
	span.rightprice-this-listing i{
		margin-top: 6px;
		font-size: 15px;
	}
	.thhis-in-responsive-view-and-dpdp.this-side-active-sd{
		right: 4%;
	}
	.height100vh{
		height: 100vh;
		overflow: hidden;
	}
	.desktop-none-this{
		display: block;
	}


	.responsive-close-btn-to-category{
	  display: block;
	}

	.row.margin-none-in-passion-inbox-arihantss{
		padding: 45px 0px 20px 0px;
	}
	.row.margin-none-in-passion-inbox-arihantss .col-md-2.col-sm-4.col-6{
		padding:0px;
	}
	.title-icon-passion-career-arihant h4{
		font-size: 16px;
	}

	.row.margin-none-in-passion-inbox-arihantss .col-md-2.col-sm-4.col-6:nth-child(1),
	.row.margin-none-in-passion-inbox-arihantss .col-md-2.col-sm-4.col-6:nth-child(3){
		border-bottom: 1px solid #0000001c;
		border-right: 1px solid #0000001c;
		padding-top:20px;
	}

	.row.margin-none-in-passion-inbox-arihantss .col-md-2.col-sm-4.col-6:nth-child(2),
	.row.margin-none-in-passion-inbox-arihantss .col-md-2.col-sm-4.col-6:nth-child(4){
		border-bottom: 1px solid #0000001c;
		padding-top:20px;
	}

	.row.margin-none-in-passion-inbox-arihantss .col-md-2.col-sm-4.col-6:nth-child(5){
		border-right: 1px solid #0000001c;
		padding-top:20px;
	}
	.row.margin-none-in-passion-inbox-arihantss .col-md-2.col-sm-4.col-6:nth-child(6){
		padding-top: 20px;
	}
	.title-icon-passion-career-arihant{
		padding:20px 0px 15px;
	}
	h2.title-passion-career-section{
		font-size: 22px;
	}
	.title-text-changess-pera-disc-work-culture p{
		display: none;
	}
	.publish-with-us-career-arihant-wrp{
		display: none;
	}
	h2.title-recruitment-career-section{
		font-size: 22px;
	}
	.wrp-all-tab-applying-career-ul-arihant ul li{
		width: 100%;
		margin-bottom: 10px;
	}
	.recruitment-process-box-incareer-width-20{
		width: 50%;
	}
	.manage-detail-applying-recruitment-section-career p{
		display: none;
	}
	.manage-detail-applying-recruitment-section-career h4{
		min-height: 50px;
		text-align: center;
		font-size: 16px;
		margin-bottom: 20px;
	}
	.image-wrp-applying-recruitment-section-career{
		text-align: center;
	}
	.row.Recruitment-process-career{
		padding-bottom:25px;
	}
	.manage-short-one-wrp-text-content-cv h3{
		font-size: 16px;
	}
	.applying-to-arihant-process-career-wrp p{
		display: none;
	}
	.applying-to-arihant-process-career-wrp h4{
		font-size: 14px;
		margin-bottom:30px;
	}
	.wrp-career .menu-main-min-height{
		min-height: 58px;
	}
	.details-box-of-cntact-arihant-books{
		padding:0px;
		margin-top:-20px;
	}
	.con-box-wrp-this-manage-rows{
		margin-bottom:10px;
		padding: 20px 0px;
	}
	.contact-all-details-bottom-box-content a{
		font-size:18px;
	}
	.contact-us-box-wrp-content-disc p{
		font-size: 12px;
		padding:0px 10px;
	}
	.manage-row-in-details-address-office-arihants{
		display: none;
	}
	.all-address-wrp-contact-arihant.desk-view{
		display: none;
	}
	.all-address-wrp-contact-arihant.mobile-view{
		display: block;
	}
	.title-text-b2b-services-arihant-books{
		display: none;
	}
	.first-of-second-1{
		padding: 0px;
		padding-right: 5px;
	}
	.col-sm-4.col-6.first-of-second-1.second-first-112{
		padding: 0px;
		padding-left: 5px;
	}
	.icon-wrp-contact-uss img {
	    width: 30px;
	}
	.contact-us-box-wrp-content-disc h3{
		font-size: 16px;
	}
	.contact-all-details-bottom-box-content h4{
		font-size: 12px;
	}
	.contact-all-details-bottom-box-content a{
		font-size: 12px;
	}
	.width-33-2ik-arihant{
		padding:0px;
	}
	.contact-all-details-bottom-box-content{
		min-height: 60px;
	}
	.text-manage-box-b2b-service-arihant-wrp h3{
		font-size: 13px;
	}
	.wrp-all-detail-b2b-box-arihant{
		padding:10px 0px 10px 0px;
	}
	.arihant40-per-deta-b2b-services{
		width:50%;
	}
	.arihant60-per-deta-b2b-services{
		width:50%;
	}
	.wrp-all-detail-b2b-box-arihant img{
		width:30px;
	}
	.text-manage-box-b2b-service-arihant-wrp a{
		font-size: 11px;
	}
	.manage-wrp-line-b2b-service-arihant-book{
		margin-top:15px;
	}
	.container-fluid.manage-padding-b2b-arihany.desk-views{
		display: none;
	}
	.container-fluid.manage-padding-b2b-arihany.mobile-views{
		display: block;
		margin-top:20px;
	}
	.featured-jobs-posi-arihant-careers{
		position: static;
		background: #efefef63;
	}
	.featured-image-bg-offical-jobsd img{
		display: none;
	}
	.job-table-wrapper-career-arihant{
		width:100%;
		padding-right: 0px;
		padding:0px 10px;
	}
	.header-part-in-box-location-career-arihant{
		width: 30%;
	}
	.header-part-in-box-department-career-arihant{
		width:30%;
	}
	h4.details-jobs-name-arihant-career-feature{
	    font-size: 14px;
	    padding-right: 10px;
	    line-height: 20px;
	}
	span.name-of-department-arihant-jobs-career{
		font-size: 10px;
	}
	span.job-location-of-arihant-career-feature{
		font-size: 10px;
	}
	div#career-arihant .container-fluid{
		padding: 0px;
	}
	div#career-arihant .row{
		margin: 0px;
	}


	.all-address-wrp-contact-arihant.mobile-view .box-map-wrpsthis{
		min-height: 300px;
		border-bottom: 0px;
		border:none;
	}
	.all-address-wrp-contact-arihant.mobile-view .padding-none-in-desktop-view-conn-address{
		border:none;
	}
	.all-address-wrp-contact-arihant.mobile-view .div-padngdd0-zero-arihantss{
		padding:5px 0px 10px 10px;
	    border:1px solid #cccccc69;
	    border-bottom: 0px;
	}
	.row-arihant-address-ofsosos {
	    margin: 30px 0px 0px 0px;
	}
	.row-arihant-address-ofsosos h3 {
	    color: #000;
	    font-size: 22px;
	    letter-spacing: 0.2px;
	    margin-bottom: 5px;
	}
	.row-arihant-address-ofsosos p {
	    color: #686868;
	    margin-bottom: 0px;
	    font-size: 14px;
	    margin-top: 4px;
	}
	.row-arihant-address-ofsosos span {
	    color: #e87215;
	}
	.b2b-service-wrapper-arihant{
		margin-top:25px;
	}

}

@media(max-width:560px){
	*{
		padding:0;
		margin:0;
	}
	.pattern-disc-headding-footer h3{
		font-size: 12px;
	}
	.pattern-disc-headding-footer{
		margin-left: 50px;
	}
	.link-sec-footer-respo{
		margin-left: 50px;
	}
	.link-sec-footer-respo a {
		font-size:9px;
	}
	.bg-wrapper-sec{
		min-height: 200px;
	}


}


@media screen 
  and (min-device-width: 1600px) 
  and (max-device-width: 1750px){
	#accordion .card:last-child h5.btn-link-wrp-user-category-pg:after{
	  height: 2px;
	}
}


@media screen 
  and (min-device-width: 2400px) 
  and (max-device-width: 2599px) { 
  	.bg-wrapper-sec{
  		min-height:1400px;
  	}
  	.categories-section .container {
	    min-width: 2200px;
	}
	.box-categories-wrp {
	    width: 90%;
	    margin: 30px auto 0px;
	}
}
@media screen 
  and (min-device-width: 2600px) 
  and (max-device-width: 2880px) { 
  	.bg-wrapper-sec{
  		min-height:1700px;
  	}
  	.categories-section .container {
	    min-width: 2200px;
	}
	.box-categories-wrp {
	    width: 90%;
	    margin: 30px auto 0px;
	}

}



@media screen 
  and (min-device-width: 4000px) 
  and (max-device-width: 4100px) { 
  	.bg-wrapper-sec{
  		min-height:2200px;
  	}
  	.categories-section .container {
	    min-width: 2200px;
	}
	.box-categories-wrp {
	    width: 90%;
	    margin: 30px auto 0px;
	}
}

@media(max-width:1192px){
	.tabel-shopping-cart thead th{font-size:16px;}
	.tabel-shopping-cart thead th:nth-child(3){width:17%;}
	.this-is-posi-wrp-div{
		top:0px;
	}
}








@media (min-width: 576px){
	.modal-dialog {
	    max-width: 500px;
	    margin: 4.75rem auto;
	}
}