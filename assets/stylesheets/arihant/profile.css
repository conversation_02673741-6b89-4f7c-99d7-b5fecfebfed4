/*Wonderslate color themes*/
/* NOTES:-> When we are working on utkarsh comment above color themes and use below themes*/
/*eUtkarsh color themes*/
/*Home page*/
/*evidya*/
/*@ws-white:#ffffff;
@ws-darkBlack:#444444;
@ws-lightOrange:#1F419B;
@ws-darkOrange:#1F419B;
@ws-darkBlue:#2EBAC6;

@ws-red:#B72319;

@ws-gradient-start:#30C465;
@ws-gradient-end:#3AE878;

@ws-border:#EDEDED;

@blue:#2F80ED;

@ws-fadebg:#FAFAFA;

@ws-caret:#010101;

@ws-blue-start:#2EBAC6;
@ws-blude-end:#4DDCE8;

@ws-bluePrimary:#1F70B5;

@ws-transparentOrange: rgba(31, 65, 155, 0.5);*/
/*Ramaiah*/
/*Home page*/
@font-face {
  src: url('../../fonts/<PERSON><PERSON><PERSON>_Dev_010.ttf');
  font-family: "<PERSON><PERSON><PERSON> Dev 010";
}
.user_profile {
  background: #FAFAFA;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .user_profile {
    margin-top: 65px;
  }
}
.user_profile #profile-menu {
  margin-top: 2rem;
}
.user_profile #profile-menu.nav-tabs {
  border-bottom: none;
}
.user_profile #profile-menu.nav-tabs .nav-item:last-child {
  margin-left: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .user_profile #profile-menu.nav-tabs .nav-item:last-child {
    margin-left: 0;
  }
}
.user_profile #profile-menu.nav-tabs .nav-item.show .nav-link {
  border: none;
  background: none;
}
.user_profile #profile-menu.nav-tabs .nav-link {
  cursor: pointer;
  font-size: 32px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.7);
  font-family: 'Merriweather', serif;
}
.user_profile #profile-menu.nav-tabs .nav-link.active {
  border: none;
  background: none;
  border-bottom: 3px solid #F79420;
  color: #444444;
}
.user_profile #profile-menu.nav-tabs .nav-link:hover {
  border: none;
  border-bottom: 3px solid #F79420;
}
.user_profile .tab-content {
  margin-top: 2rem;
}
.user_profile .tab-content .jumbotron {
  background: #ffffff;
  border: 1px solid #EDEDED;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .user_profile .tab-content .jumbotron {
    margin-top: 3rem;
    padding-bottom: 4rem;
  }
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper {
  position: relative;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .user_profile .tab-content .jumbotron form .media .profile-wrapper {
    text-align: center;
  }
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-img {
  width: 190px;
  height: 190px;
  box-shadow: 0px 0px 4px 4px #EDEDED;
  border: 7px solid #ffffff;
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-btn {
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: absolute;
  right: 25px;
  bottom: 30px;
  text-align: center;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-btn {
    right: 80px;
  }
}
.user_profile .tab-content .jumbotron form .media .profile-wrapper .edit-btn i {
  color: rgba(68, 68, 68, 0.74);
  font-size: 12px;
}
.user_profile .tab-content .jumbotron form .media .continue {
  width: 312px;
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  color: #ffffff;
  text-transform: uppercase;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  float: right;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px) {
  .user_profile .tab-content .jumbotron form .media .continue {
    width: 100%;
  }
}
.user_profile .tab-content .selectbox select {
  width: 100%;
}
.input-login {
  overflow: hidden;
  position: relative;
  z-index: 1;
  display: inline-block;
  margin: 0.3rem;
  width: 100%;
}
.input-login input {
  border: none;
  outline: 0;
  margin-top: 1rem;
  padding: 0.85em 0.15em;
  width: 100%;
  color: #595F6E !important;
  background-color: #fff !important;
}
.input-login input:focus + label::after {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.input-login input:focus + label > span {
  -webkit-animation: anim-1 0.3s forwards;
  animation: anim-1 0.3s forwards;
}
.input-login label {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 0 0.25em;
  width: 100%;
  height: calc(100% - 1em);
  text-align: left;
  pointer-events: none;
}
.input-login label > span {
  position: absolute;
}
.input-login label > span::after {
  border-color: hsl(200, 100%, 50%);
}
.input-login label::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 0;
  width: 100%;
  height: calc(100% - 10px);
  border-bottom: 1px solid #B9C1CA;
}
.input-login label::after {
  content: '';
  position: absolute;
  top: 7px;
  left: 0;
  width: 100%;
  height: calc(100% - 10px);
  border-bottom: 1px solid #B9C1CA;
  margin-top: 2px;
  border-bottom: 2px solid #F79420;
  -webkit-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
}
@-webkit-keyframes anim-1 {
  50% {
    opacity: 0;
    -webkit-transform: translate3d(1em, 0, 0);
    transform: translate3d(1em, 0, 0);
  }
  51% {
    opacity: 0;
    -webkit-transform: translate3d(-1em, -40%, 0);
    transform: translate3d(-1em, -40%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
  }
}
@keyframes anim-1 {
  50% {
    opacity: 0;
    -webkit-transform: translate3d(1em, 0, 0);
    transform: translate3d(1em, 0, 0);
  }
  51% {
    opacity: 0;
    -webkit-transform: translate3d(-1em, -40%, 0);
    transform: translate3d(-1em, -40%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
  }
}
.input-login .change-password {
  position: absolute;
  right: 0;
  bottom: 15px;
  color: #F79420;
  font-weight: 500;
}
#change-password-modal .modal-content {
  width: 320px;
  box-shadow: 0px 19px 38px rgba(0, 0, 0, 0.3), 0px 15px 12px rgba(0, 0, 0, 0.22);
}
#change-password-modal .modal-content .modal-header {
  border-bottom: none;
}
#change-password-modal .modal-content .modal-header h4 {
  color: #444444;
  font-family: 'Rubik', sans-serif;
  font-size: 20px;
  font-weight: 500;
}
#change-password-modal .modal-content[data-content='form'] .password-content {
  display: block;
}
#change-password-modal .modal-content[data-content='success'] .password-success {
  display: block;
}
#change-password-modal .password-content {
  display: none;
}
#change-password-modal .password-content .input-login {
  width: 280px;
}
#change-password-modal .password-content .input-login label > span {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.5);
}
#change-password-modal .password-content button {
  background: none;
  border: none;
  text-transform: uppercase;
  font-weight: 500;
}
#change-password-modal .password-content button:first-child {
  color: #444444;
}
#change-password-modal .password-content button:last-child {
  color: #F79420;
}
#change-password-modal .password-success {
  display: none;
}
#change-password-modal .password-success .success {
  font-size: 26px;
  font-weight: bold;
  font-family: 'Merriweather', serif;
  color: #444444;
}
/*Temp hide*/
.share-btn {
  display: none;
}
