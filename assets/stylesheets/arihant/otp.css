/*Wonderslate color themes*/
/* NOTES:-> When we are working on utkarsh comment above color themes and use below themes*/
/*eUtkarsh color themes*/
/*Home page*/
/*evidya*/
/*@ws-white:#ffffff;
@ws-darkBlack:#444444;
@ws-lightOrange:#1F419B;
@ws-darkOrange:#1F419B;
@ws-darkBlue:#2EBAC6;

@ws-red:#B72319;

@ws-gradient-start:#30C465;
@ws-gradient-end:#3AE878;

@ws-border:#EDEDED;

@blue:#2F80ED;

@ws-fadebg:#FAFAFA;

@ws-caret:#010101;

@ws-blue-start:#2EBAC6;
@ws-blude-end:#4DDCE8;

@ws-bluePrimary:#1F70B5;

@ws-transparentOrange: rgba(31, 65, 155, 0.5);*/
/*Ramaiah*/
/*Home page*/
@font-face {
  src: url('../../fonts/<PERSON><PERSON><PERSON>_Dev_010.ttf');
  font-family: "K<PERSON>ti Dev 010";
}
.otp-screens [data-verify="otp-code"] .req-otpwrapper,
.otp-screens [data-verify="otp-code"] #verify-otp,
.otp-screens [data-verify="otp-code"] .mobile-text {
  display: block;
}
.otp-screens [data-verify="otp-code"] .mobile-wrapper,
.otp-screens [data-verify="otp-code"] .otp-text,
.otp-screens [data-verify="otp-code"] #get-otp {
  display: none;
}
.otp-screens h4 {
  font-size: 24px;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  font-weight: normal;
}
.otp-screens p {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.4);
  padding: 0.5rem 2rem;
}
.otp-screens p span {
  font-size: 12px;
  font-weight: 500;
  color: #444444;
}
.otp-screens .btn-continue {
  width: 90%;
}
.otp-screens .modal-header {
  border-bottom: none;
  padding: 1.5rem 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.otp-screens .modal-header .close {
  padding: 0;
  margin: 0;
}
.otp-screens .modal-footer {
  border-top: none;
  justify-content: space-around;
  margin-bottom: 1rem;
}
.otp-screens .input-login {
  overflow: hidden;
  position: relative;
  z-index: 1;
  display: inline-block;
  margin: 0.3rem;
  width: 312px;
}
.otp-screens .input-login input {
  border: none;
  outline: 0;
  margin-top: 1rem;
  padding: 0.85em 0.15em;
  width: 100%;
  background: transparent;
  color: #595F6E;
}
.otp-screens .input-login input:focus + label::after {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
.otp-screens .input-login input:focus + label > span {
  -webkit-animation: anim-1 0.3s forwards;
  animation: anim-1 0.3s forwards;
}
.otp-screens .input-login label {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 0 0.25em;
  width: 100%;
  height: calc(100% - 1em);
  text-align: left;
  pointer-events: none;
}
.otp-screens .input-login label > span {
  position: absolute;
  text-align: center;
  width: 100%;
  color: rgba(68, 68, 68, 0.4);
  font-size: 12px;
}
.otp-screens .input-login label > span::after {
  border-color: hsl(200, 100%, 50%);
}
.otp-screens .input-login label::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 0;
  width: 100%;
  height: calc(100% - 10px);
  border-bottom: 1px solid #B9C1CA;
}
.otp-screens .input-login label::after {
  content: '';
  position: absolute;
  top: 7px;
  left: 0;
  width: 100%;
  height: calc(100% - 10px);
  border-bottom: 1px solid #B9C1CA;
  margin-top: 2px;
  border-bottom: 2px solid #F79420;
  -webkit-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
}
@-webkit-keyframes anim-1 {
  50% {
    opacity: 0;
    -webkit-transform: translate3d(1em, 0, 0);
    transform: translate3d(1em, 0, 0);
  }
  51% {
    opacity: 0;
    -webkit-transform: translate3d(-1em, -40%, 0);
    transform: translate3d(-1em, -40%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
  }
}
@keyframes anim-1 {
  50% {
    opacity: 0;
    -webkit-transform: translate3d(1em, 0, 0);
    transform: translate3d(1em, 0, 0);
  }
  51% {
    opacity: 0;
    -webkit-transform: translate3d(-1em, -40%, 0);
    transform: translate3d(-1em, -40%, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: translate3d(0, -40%, 0);
    transform: translate3d(0, -40%, 0);
  }
}
.resend-otp {
  color: #F79420;
  font-weight: 500;
}
.resend-otp:hover {
  color: #F79420;
}
#otp-error {
  color: #B72319;
}
@media (min-width: 576px) {
  .otp-screens .modal-dialog {
    width: 400px;
  }
}
.modal {
  background: rgba(0, 0, 0, 0.3) !important;
}
.btn-continue {
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  color: #ffffff;
  text-transform: uppercase;
  font-size: 14px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
}
.req-otpwrapper,
#verify-otp,
.mobile-text {
  display: none;
}
.recieve-msg {
  color: rgba(68, 68, 68, 0.4);
  font-size: 12px;
  text-align: center;
  display: block;
}
#otp-next .modal-body p,
#otp-confirm .modal-body p,
.otp-confirm .modal-body p {
  font-size: 16px;
  color: #444444;
}
#otp-next .btn-continue,
#otp-confirm .btn-continue,
.otp-confirm .btn-continue {
  width: 50%;
}
