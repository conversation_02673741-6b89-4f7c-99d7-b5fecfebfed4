#loginOpen,
#signup,
#forgotPasswordmodal {
  -webkit-font-smoothing: antialiased;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #loginOpen .modal-dialog,
  #signup .modal-dialog,
  #forgotPasswordmodal .modal-dialog {
    align-items: flex-start;
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }
}
#loginOpen .modal-header,
#signup .modal-header,
#forgotPasswordmodal .modal-header {
  display: flex;
  align-items: center;
  font-family: 'Poppins', sans-serif !important;
  padding-bottom: 0;
}
#loginOpen .head-title,
#signup .head-title,
#forgotPasswordmodal .head-title {
  color: #212121;
  font-size: 24px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 5px;
}
#loginOpen .modal-body,
#signup .modal-body,
#forgotPasswordmodal .modal-body {
  background: #d3e4fb;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #loginOpen .modal-body,
  #signup .modal-body,
  #forgotPasswordmodal .modal-body {
    background-color: transparent;
  }
}
#loginOpen .modal-body .close,
#signup .modal-body .close,
#forgotPasswordmodal .modal-body .close {
  font-size: 18px;
  position: absolute;
  right: 15px;
  top: 15px;
  z-index: 2;
}
#loginOpen .modal-body p,
#signup .modal-body p,
#forgotPasswordmodal .modal-body p {
  color: #212121;
  font-size: 14px;
  font-family: 'Poppins', sans-serif !important;
  line-height: normal;
}
#loginOpen .modal-body input,
#signup .modal-body input,
#forgotPasswordmodal .modal-body input {
  color: #212121;
  border: 1px solid #c7c7c7;
  border-radius: 7px;
  font-family: 'Poppins', sans-serif !important;
  height: 45px;
  border-left-width: 3px;
  border-left-color: #EF7215;
}
#loginOpen .modal-body input:focus,
#signup .modal-body input:focus,
#forgotPasswordmodal .modal-body input:focus {
  box-shadow: 0 0 5px 1px #0000001A;
  border-color: #EF7215;
}
#loginOpen .modal-body input::placeholder,
#signup .modal-body input::placeholder,
#forgotPasswordmodal .modal-body input::placeholder {
  font-size: 12px;
}
#loginOpen .modal-body input.input-error,
#signup .modal-body input.input-error,
#forgotPasswordmodal .modal-body input.input-error {
  border-color: #FF4B33;
}
#loginOpen .modal-body input#password,
#signup .modal-body input#password,
#forgotPasswordmodal .modal-body input#password,
#loginOpen .modal-body input#signup-password,
#signup .modal-body input#signup-password,
#forgotPasswordmodal .modal-body input#signup-password {
  padding-right: 45px;
}
#loginOpen .modal-body .login-btn,
#signup .modal-body .login-btn,
#forgotPasswordmodal .modal-body .login-btn {
  background: #EF7215;
  box-shadow: 0 0 7px #0000001A;
  border-radius: 5px;
  border: none;
  color: #FFFFFF;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  font-family: 'Poppins', sans-serif !important;
  font-size: 16px;
  width: 100%;
}
#loginOpen .modal-body .error-text,
#signup .modal-body .error-text,
#forgotPasswordmodal .modal-body .error-text {
  color: #FF4B33;
  margin-top: 2px;
}
#loginOpen .modal-body .register-success,
#signup .modal-body .register-success,
#forgotPasswordmodal .modal-body .register-success {
  color: #27AE60;
}
#loginOpen .modal-body .logo,
#signup .modal-body .logo,
#forgotPasswordmodal .modal-body .logo {
  margin-bottom: 10px;
  display: none;
}
#loginOpen .modal-footer p,
#signup .modal-footer p,
#forgotPasswordmodal .modal-footer p {
  color: #212121;
  font-family: 'Poppins', sans-serif !important;
}
#loginOpen .modal-footer p a,
#signup .modal-footer p a,
#forgotPasswordmodal .modal-footer p a {
  color: #212121;
  font-weight: 700;
  font-family: 'Poppins', sans-serif !important;
}
#loginOpen .modal-content,
#signup .modal-content,
#forgotPasswordmodal .modal-content {
  border-radius: 20px;
  overflow-x: hidden;
  flex-direction: unset;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #loginOpen .modal-content,
  #signup .modal-content,
  #forgotPasswordmodal .modal-content {
    height: 100%;
    border-radius: 0;
    flex-direction: unset;
  }
}
.forgot {
  color: #EF7215;
  cursor: pointer;
  font-size: 14px;
  font-weight: 700;
  font-family: 'Poppins', sans-serif !important;
}
.forgot:hover {
  color: #EF7215;
}
.error-msg {
  color: #FF4B33 !important;
}
#loginOpen form,
#signup form,
#forgotPasswordmodal form {
  text-align: center;
}
#mobilemail-error {
  white-space: nowrap;
  display: block;
  margin-top: 10px;
  font-family: 'Poppins', sans-serif !important;
}
.login_signup_loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: none;
  width: 100%;
  z-index: 1;
}
.login_signup_loader .progressbar {
  background-color: #EF7215 !important;
}
.login_signup_loader .bufferbar {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)), linear-gradient(to right, #EF7215, #EF7215) !important;
}
.login_signup_loader .auxbar {
  background-color: #EF7215 !important;
}
.modal-text-content {
  flex-direction: column;
  background: #d3e4fb;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-text-content {
    border-radius: 0 0 50px 50px;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px #00000040;
  }
}
.modal-text-content lottie-player {
  position: relative;
  width: 100%;
  height: 100%;
  top: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .modal-text-content lottie-player {
    display: none;
  }
}
.modal-text-content h1 {
  font-weight: 400;
  width: 100%;
  text-align: center;
  font-size: 2rem;
}
.modal-text-content h1 span {
  font-weight: 700;
  color: #EF7215;
}
.modal-form-content {
  background-color: #FFFFFF;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .modal-form-content {
    background-color: transparent;
  }
}
.hide-password {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #bababa;
  font-size: 22px !important;
  border-left: 1px solid #d4d4d4;
  padding-left: 7px;
}
.hide-password:hover {
  color: #bababa;
  text-decoration: none;
}
