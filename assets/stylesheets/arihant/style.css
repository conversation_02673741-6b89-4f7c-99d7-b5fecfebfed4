html,
body{
  font-family: 'Fira Sans', sans-serif;
}
.header-wrapper{
  width:100%;
}
.category-main-wrp .container-fluid{
  padding-left: 25px;
}

.bg-wrapper-sec{

}


.logo-wrapper {
    width: 100%;
    text-align: center;
    padding: 6px 0px;
}

.logo-wrapper a {
  width: 45px;
  display: inline-block;
}
.logo-wrapper a img {
  width: 100%;
}

.header-menu-wrapper{

}
.menu-bar-wrp{
  width:100%;
  text-align: center;
}
.menu-bar-wrp ul{
  padding: 0px;
  margin:0px;
  text-align: center;
  font-family: 'Open Sans', sans-serif;
  display: inline-block;
}
.menu-bar-wrp ul li{
  list-style: none;
  float: left;
  display: inline-block;
  line-height: 24px;
}

.menu-bar-wrp ul li a{
  padding: 0px 5px;
  color:#fff;
  font-size: 12px;
  border-right: 1px solid #fff;

  transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
}

.menu-bar-wrp ul li a:hover{
  text-decoration: none;
  color:#EB7215;
}

.menu-bar-wrp ul li.youtube a{
  border:none;
}




.main-menu-wrp{
  position: fixed;

  padding: 10px 0px;
  border-bottom: 1px solid #dadada54;
  transition-property: all;
  transition-duration: 400ms;
  -webkit-transition: all 400ms ease-in-out;
  transition-timing-function: ease-in-out;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  background-color: linear-gradient(90deg,#FF3448  0%,#FF9B26 100%);

  width: 100%;
  z-index: 999;
  height: 78px;
  top: 0;
}




.user-menu-wrp {
  display: inline-block;
  float: left;
}
.user-menu-wrp a.menu-dots-img-wrp {
    display: inline-block;
    padding: 14px 14px;
}
.user-menu-wrp img.menu-dots-img{
  width:24px;
  height:24px;
}

ul.menu-wrp-link-main{
  font-family: 'Oswald', sans-serif;
  padding:0px 0px;
  margin:0px;
  float: left;
}
.main-menu-wrp.search-active-now-row ul.menu-wrp-link-main{  
  -webkit-animation: slow-hiddsen-menu .4s both;
  animation: slow-hiddsen-menu .4s both;
}
ul.menu-wrp-link-main li.link-menu-li{
  float:left;
  list-style: none;
}
ul.menu-wrp-link-main li.link-menu-li a.menu-link-anchor{
  font-weight: 300;
  color:#fff;
  font-size: 20px;
  text-transform: uppercase;
  padding: 14px 14px;
  display: inline-block;
  letter-spacing: 1px;
}

ul.menu-wrp-link-main li.link-menu-li.menu-big-under:hover ul.under-menu-wrp-links{
  display: block;
}
ul.menu-wrp-link-main li.link-menu-li.menu-big-under a.menu-link-anchor:after {
    content: "";
    bottom: 2px;
    left: 48%;
    position: absolute;
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\f107";
}

ul.menu-wrp-link-main li.link-menu-li a.menu-link-anchor:hover{
  text-decoration: none;
}


.menu-main-min-height{
  min-height: 112px;
}
.menu-big-under{
  position: relative;
}
ul.under-menu-wrp-links{
  position: absolute;
  top: 100%;
  display: none;
  left: 0px;
  min-width: 180px;
  padding: 0px 15px 0px 10px;
  margin:0px;
  background-color: #2a2a2a;
}
ul.under-menu-wrp-links li{
  list-style: none;
}
ul.under-menu-wrp-links li a{
  color:#fff;
  display: block;
  padding: 5px 2px;
  text-transform: uppercase;
  position: relative;
  font-size:14px;
  font-weight: 300;
}
ul.under-menu-wrp-links li a:hover{
  text-decoration: none;
}
ul.under-menu-wrp-links li a:after{
    content: "";
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    height: 1px;
    width: 100%;
    position: absolute;
    left: 0px;
    top: 100%;
}
ul.under-menu-wrp-links li:last-child a:after{
  display: none;
}






.menu-wrp-all-users-com{

}
.menu-wrp-all-users-com ul{
  padding:0px;
  margin:0px;
  font-family: 'Oswald', sans-serif;
  float:right;
}

.main-menu-wrp.search-active-now-row .menu-wrp-all-users-com ul{  
  -webkit-animation: slow-hiddsen-menu .4s both;
  animation: slow-hiddsen-menu .4s both;
}

.menu-wrp-all-users-com ul li{
  list-style: none;
  float: left;
}

.menu-wrp-all-users-com ul li a{
  font-weight: normal;
  color: #fff;
  font-size: 18px;
  text-transform: uppercase;
  padding: 15px;
  letter-spacing: 1px;
  display: inline-block;
}

.menu-wrp-all-users-com ul li a:hover{
  text-decoration: none;
}
.menu-wrp-all-users-com ul li a img{
  width: 20px;
  height: 20px;
  margin-bottom: 6px;
  margin-right: 2px;
}
.menu-wrp-all-users-com ul li .dropdown-menu a {
  color: initial;
  font-size: 14px;
  padding: 4px 14px;
  text-transform: capitalize;
  letter-spacing: normal;
}
.search-input-in-disable {
  width: 0px;
  border: 0px solid #cccccc42;
  position: absolute;
  border-radius: 6px 6px 6px 6px;
  z-index: -1;
  background: transparent;
  transition: 0.5s all ease-in-out;
  -o-transition: 0.5s all ease-in-out;
  -ms-transition: 0.5s all ease-in-out;
  -moz-transition: 0.5s all ease-in-out;  
  -webkit-transition: 0.5s all ease-in-out;
  left: 0px;
  top: 20%;
}
input.search-input-in-disable.active-now{
  width: 70%;
  display: block;
  padding: 2px 3px 3px 12px;
  position: absolute;
  z-index: 999;
  font-size:16px;

  background: #fff;
  border: 1px solid #cccccc42;
}

.search-header-close {
  position: absolute;
  right: 0px;
  top: 0px;
  height: 100%;
  background: #0c0c0c36;
  padding: 14px 15px;
  overflow: hidden;
  visibility: hidden;
}

li.none-add-class-responsive.display-none-now a {
    display: none;
}

.wrapper-in-box-side-close{
  overflow: hidden;
  width: 100%;
  position: absolute;
  height: 100%;
  z-index: -1;
}


.search-header-close.header-close-active-btn{
  right:0;
  cursor: pointer;
  visibility: visible;
}
.search-btn-click{

}
.margin-top-6{
  margin-top: 6px;
}

.counter {
  animation-duration: 1s;
  animation-delay: 0s;
}





/*====================================  Programs Section========================================*/

.programs-section{
  padding:30px 0px;
}
.border-title-programs{
  border-bottom:1px solid #b9b9b9;
}
h2.title-programs-section{
  display: inline-block;
  margin: 0px;
  letter-spacing: 2px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 55px;
  color: #000;
}

h2.title-programs-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}

.programs-disc-pera-about{
  color: #939797;
  text-align: center;
  font-size:17px;
  padding-top: 18px;
  margin-bottom: 30px;
  font-family: 'Open Sans', sans-serif;
}

.wrp-box-programs{
  text-align: center;
  border-right: 2px solid #b8b8b8;
  padding: 50px 0px;
}

.brdr-none-here{
  border-right: 0px;
}
.wrp-box-icon-size{

}

.wrp-box-icon-size img{

}

.wrp-box-text-sec-disc{
  padding-top:20px;
}
.wrp-box-text-sec-disc h3 {
    color: #939797;
    font-size: 38px;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
}
.wrp-box-text-sec-disc p{
  font-family: 'Fira Sans', sans-serif;
  font-size:16px;
  font-weight: 400;
  min-height: 83px;
  margin-bottom: 0px;
}
a.browse-now-button{
  font-weight: 400;
  font-size:17px;
  color:#eb7215;
  font-family: 'Fira Sans', sans-serif;
}

a.browse-now-button:hover{
  text-decoration: none;
}

.mrgn-bottom-set-pera{
  /*margin-bottom:12px !important;*/
}


/*=================================      Categories Start  ===================================================*/
.categories-section{
  padding: 40px 0px 30px;
  /*margin-top:20px;*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: center;
  min-height: 800px;
}
.border-title-categories {
  border-bottom: 1px solid #b9b9b9;
}
h2.title-categories-section{
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 55px;
  color: #000;
  /*letter-spacing: 2px;*/
}
h2.title-categories-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}

.categories-section-disc-title-pera{
  color: #939797;
  text-align: center;
  font-size:16px;
  padding-top: 18px;
  font-family: 'Open Sans', sans-serif;
  margin-bottom: 1rem;
}
.categories-section .responsive-padding-none {
  padding-left: 7px;
  padding-right: 7px;
}
#ebookCategories .responsive-padding-none {
  display: flex;
  flex-direction: column;
  margin-bottom:15px;
}
#ebookCategories .responsive-padding-none:nth-child(even) a {
  background-color: #ff9b26;
}
#ebookCategories .responsive-padding-none:nth-child(even) a:hover {
  background: #f38c13;
}

.margin-row-categories-rowss{
  margin-top:10px;
}
.box-categories-wrp{
  width: 100%;
  height: 100%;
}
.box-categories-wrp a{
  color: #fff;
  display: inline-block;
  text-align: center;
  border-radius: 14px;
  width: 100%;
  height: 100%;
  margin-bottom: 15px;
  font-weight: 400;
  font-size: 28px;
  line-height: 40px;
  padding: 40px 15px;
  font-family: 'Fira Sans', sans-serif;
}
.box-categories-wrp a:hover{
  text-decoration: none;
}

.bg-color-fill-categories{
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
.responsive-padding-none {
    padding-left: 7px;
    padding-right: 7px;
}
.bg-color-fill-categories h3{
  padding:45px 0px;
  text-align: center;
  font-family: 'Fira Sans', sans-serif;
  font-weight: 400;
  font-size: 26px;
  line-height: 40px;
  color: #fff;
}

.line-box-category{
  width: 70%;
  margin: 0px auto;
  background: #fff;
  height: 2px;
  border-radius: 5px;
}
.red-color-fill-bg{
  background:#ff3448;
}
.red-color-fill-bg:hover{
  background: #ea1d32;
}
.yellow-color-fill-bg{
  background: #ff9b26;
}
.yellow-color-fill-bg:hover{
  background: #f38c13;
}

/*======================================================================
                          Journey Section
==========================================================================*/

.journey-sec-wrp {
    padding: 30px 0px;
    background: url("../../images/arihant/journery-bg.png");
    background-size: cover;
    height: auto;
    background-repeat: no-repeat;
    background-position: center;
}

.border-title-journey{
  border-bottom: 1px solid #b9b9b9;
}
.title-journey-section{
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 45px;
  color: #000;
  /*letter-spacing: 1px;*/
}

.title-journey-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}
.journey-section-disc-title-pera{
  color: #939797;
  text-align: center;
  font-size: 16px;
  padding-top: 18px;
  padding-bottom: 20px;
  font-family: 'Open Sans', sans-serif;
}
.brdr-right-nn:after{
  display: none;
}
.manage-count-wrp-box {
    text-align: center;
    padding: 30px 0px;
    position: relative;
}
.the-img-icon-journey{

}
.the-img-icon-journey img{
  width:50px;
}
.manage-count-wrp-box:after{
  content: "";
  position: absolute;
  right: 0px;
  top: 0px;
  height: 100%;
  background: linear-gradient(50deg,#FF3448 0%,#FF9B26 100%);
  width: 2px;
}
.manage-count-wrp-box h2{
  font-family: 'Oswald', sans-serif;
  color:#686868;
  font-weight: 600;
}
.manage-count-wrp-box h2 span{
  color:#686868 !important;
  font-family: 'Oswald', sans-serif;
}

.manage-count-wrp-box span {
  font-family: 'Fira Sans', sans-serif;
  color:#000000;
}


/*========================================================
                      Connect Section
==========================================================*/


.connect-section {
    padding: 30px 0px;
    transition-property: all;
    transition-duration: 400ms;
    -webkit-transition: all 400ms ease-in-out;
    transition-timing-function: ease-in-out;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}


.border-title-coonect{
  border-bottom: 1px solid #f5f5f5;
}
.title-connect-section{
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 55px;
  color: #fff;
  /*letter-spacing: 1px;*/
}

.title-connect-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #ffffff;
  height: 2px;
  top: 99%;
  left: 0px;
}
p.connect-sec-wrp-disc-pera{
  color: #ffffff;
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
  font-weight: 300;
  margin-top:20px;
  padding: 0px 15px;
  text-align: center;
  margin-bottom: 1rem;
}

ul.social-icon-wrp-connect{
  padding: 0px;
  border-right: 1px solid #fff;
  margin:0px;
  float: right;
}
ul.social-icon-wrp-connect li{
  list-style: none;
  float: left;
  width: 22%;
  font-size: 33px;
  text-align: center; 
}
ul.social-icon-wrp-connect li a{
  color:#fff;
  padding:0px 20px;
  font-size: 30px;
}
.img-wrp-call-in{
  float: left;
  margin-right: 10px;
  padding:0px 0px 6px;
}
.img-wrp-call-in img{
  width:32px;
}
h3.call-here-number{
  font-size:24px;
  margin-top: 0px;
  padding:0px 0px 6px;
}
h3.call-here-number a{
  color:#fff;
  font-size: 22px;
}
h3.call-here-number a:hover{
  text-decoration: none;
}


.mrgn-top-connect-sec{
  margin-top:12px;
}

/*==============================================================================================
                                              FOOTER
================================================================================================*/
.footer{
  background: #eee;
  padding: 30px 0px 0;
  z-index: initial;
}
.image-wrapper-footer-logo{
  text-align: center;
}
.image-wrapper-footer-logo img{
  width:110px;
  height: auto;
}
h3.footer-link-title{
  font-size: 14px;
  font-weight: 700;
  font-family: 'Open Sans', sans-serif;
  color: #000;
}
ul.link-ul-footer{
  padding:0;
  margin:0;
}
ul.link-ul-footer li{
  list-style: none;
}
ul.link-ul-footer li a{
  color: #000;
  display: block;
  padding: 6px 0px 0px;
  font-size: 13px;
  font-weight: 400;
}
ul.link-ul-footer li a:hover{
  text-decoration: none;
}

.text-center-align-here{
  text-align: center;
  width:100%;
  margin-top: 6px;
}

.there-social-footer-link-wrp{
  padding:0;
  margin:0;
  display: inline-block;
  text-align: center;
}
.there-social-footer-link-wrp li{
  float: left;
  list-style: none;
}
.there-social-footer-link-wrp li a{
  padding:4px 10px;
  display: inline-block;
  color:#979a9b;

  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
  font-size: 19px;
}

.there-social-footer-link-wrp li a:hover{
  color:#EB7215;
}
.img-wrapper-connect {
    text-align: center;
}

/*==================================================================================================
                                      Copy Right
===================================================================================================*/
.footer-copyright{
  background: #ffffff;
}
.footer-copyright p.copy-right-text-footer{
  color:#000;
  margin:0;
  font-weight: 500;
  padding:6px 0px;
  text-align: center;
  font-size:14px;
}

.responsive-view-icon-this{
  display: none;
}

.responsive-view-not-desktop{
  display: none;
}

.overlay-menu-close{
  position: fixed;
  background: #00000059;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  display: none;
  z-index: 999;
  transition: 0.3s all ease-in-out;
}


.dispay-set-resposive-img-phone{
  display: none;
}
















/*=======================================================================================
                              Login Popup
========================================================================================*/
#login-arihant-popup{
  font-family: 'Fira Sans', sans-serif;
}
#login-arihant-popup .modal-body{
  padding:1.5em 2rem;
  overflow: hidden;
  min-height: 400px;
}
.title-header-login-popup-arihant {
  widows: 100%;
}
.title-header-login-popup-arihant h4{
  font-size: 16.96px;
  font-weight: 400;
  letter-spacing: -0.5px;
  text-align: center;
  margin-top: 10px;
  color:#686868;
}

.title-header-login-popup-arihant h4 span{
  color:#ef7215;
}
button.close.close-login-popup-arihant{
  position: absolute;
  right: 8px;
  top: 0px;
}
button.close.close-login-popup-arihant img{
  width:15px;
}
button.close.close-login-popup-arihant:hover,
button.close.close-login-popup-arihant:active,
button.close.close-login-popup-arihant:focus{
  outline: none;
}
.login-other-api-wrp {
    margin: 30px 0px;
}
.digital-serv-login-btn{

}
.digital-serv-login-btn button{
  width: 100%;
  letter-spacing: -0.5px; 
}
button.facebook-btn-login-arihant{
  background: #3b5998;
  border: 1px solid #3b5998;
  border-radius: 6px;
  padding: 10px 15px;
  color: #fff;
  cursor: pointer;
}

.google-btn-login-arihant{
  background: none;
  border:1px solid #d9d9d9;
  width: 100%;
  border-radius: 6px;
  padding: 10px 15px;
  color: #333;
  cursor: pointer;
  margin-top:10px;
}
.digital-serv-login-btn button i{
  float: left;
  display: inline-block;
  font-size: 18px;
  padding: 3px 0px;
}
.digital-serv-login-btn button span{
  font-weight: 300;
  font-size: 14px;
}

.google-btn-login-arihant i{
  font-size: 0px !important;
  padding:0px !important;
}
.google-btn-login-arihant i img{
  width: 18px;
  padding: 3px 0px;
}
.login-other-ways{
  text-align: center;
  padding:20px 0px;
}
span.login-with-other-ways-text {
    position: relative;
    color:#686868;
    letter-spacing: -0.5px;
    font-size: 17px;
}
span.login-with-other-ways-text:after {
    position: absolute;
    content: "";
    width: 70px;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    height: 2px;
    right: -80px;
    top: 8px;
}

span.login-with-other-ways-text:before {
    position: absolute;
    content: "";
    width: 70px;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    height: 2px;
    left: -80px;
    top: 8px;
}

.box-wrp-login-arihant-user {
    padding: 30px 0px;
}

.box-wrp-single-row-login-arihant{
  position: relative; 
}
.box-wrp-single-row-login-arihant i{
  position: absolute;
  left: 0px;
  color: #686868;
  top: 8px;
  display: inline-block;
  padding: 10px 15px;
}
.box-wrp-single-row-login-arihant i.fa.fa-mobile{
  font-size: 24px;
  padding: 6px 15px;
}
input.input-box-bottom-border {
    border: none;
    border-bottom: 1px solid #d8d8d8;
    width: 100%;
    color: #686868;
    min-height: 35px;
    font-size: 16px;
    letter-spacing: -0.5px;
    padding-left: 35px;
    font-weight:400;
}

input.input-box-bottom-border:focus,
input.input-box-bottom-border:hover,
input.input-box-bottom-border:active{
  outline: none;
}
.box-wrp-single-row-login-arihant.username-wrp-box-input-login-arihant {
  padding-bottom: 35px;
  padding-top: 10px;
}
.box-wrp-single-row-login-arihant.Password-wrp-box-input-login-arihant {
    padding-top: 10px;
}
.btn-to-show-password-hide{
  position: absolute;
  top: 5px;
  display: inline-block;
  right: 35px;
}
.btn-to-show-password-hide i{
  padding: 9px 10px;
  font-size: 13px;
}
.forgot-single-login-option-wrp {
    text-align: right;
}

.forgot-single-login-option-wrp a{
  display: inline-block;
  font-size: 10px;
  color:#ef7215;
  font-weight: 400;
}
.forgot-single-login-option-wrp a:hover,
.forgot-single-login-option-wrp a:hover,
.forgot-single-login-option-wrp a:focus{
  text-decoration: none;
}
.login-btn-wrp-arihant-popup{
  text-align: center;
  margin-top: 20px;
}
.login-btn-wrp-arihant-popup button.login-btn-arihant{
  cursor: pointer;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  border: none;
  color: #fff;
  padding: 8px 51px;
  border-radius: 6px;
  font-weight: 300;
  display: inline-block;
}
.signup-in-login-popup-wrp-text {
    text-align: center;
}
.signup-in-login-popup-wrp-text span{
  font-size: 10px;
  color: #979a9b;
}
a.signup-btn-in-arihant-login-popup{
  font-size: 12px;
  color: #ef7215;
  font-weight: 500;
}

/*===========================================================
                    Sign Up Body Popup
=============================================================*/
.signup-body-modal{
  display: none;
}
.display-block-item{
  display: block !important;
}
.display-none-item{
  display: none;
}
.forgot-password-body-modal{
  display: none;
}
h4.forgot-password-title {
  color: #000;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.2px;
}

p.sub-title-forgot-pera{
  text-align: center;
  color: #a7b1ac;
  font-size: 12px;
}
.forgot-wrp-box-row {
    width: 100%;
}
.wrp-box-manage-inpit-forgot {
    width: 100%;
}
input.forgot-input-box{
  width: 100%;
  color: #757575;
  text-align: center;
  min-height: 35px;
  border: 0px;
  border-bottom: 1px solid #b3b3b3;
}

input.forgot-input-box:focus,
input.forgot-input-box:hover,
input.forgot-input-box:active{
  outline: none;
}

a.btn-customer-care{
  color:#ef7215;
}

.mrgn-top-forgot-btn-sss{
  margin-top:20px;
}
.forgot-pera-bottom {
  line-height: 14px;
  margin-top: 26px;
}


/*==========================================================================================
                    Search Header CSS
===========================================================================================*/





.row.search-item-rows {
    font-family: 'Fira Sans', sans-serif;
}
.this-div-use-box-search{
  left: 20%;
  width: 60%;
  display: none;
  position: absolute;
  top: 0px;
  min-height: 84px;
}
.main-menu-wrp.search-active-now-row .this-div-use-box-search{ 
  display: block;
}
.search-view-bar-arihant {
    width: 100%;
}
input.input-box-search-in-header::placeholder{
  color:#fff;
}
input.input-box-search-in-header {
  overflow: hidden;
  color: #fff;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 17px;
  line-height: 1.29412;
  letter-spacing: -.021em;
  outline: none;
  width: calc(100% - 42px - 16px);
  height: 44px;
  background: none;
  border: none;
  position: absolute;
  padding: 0 17px 0 42px;
  top:20px;
}
.wrapper-form-header-search-input{
  min-height: 78px;
  opacity: 0;
}
.main-menu-wrp.search-active-now-row.now-hide-menuall .wrapper-form-header-search-input{
  /*-webkit-animation: arihant-searchform-slide 1s 0.4s both;
  animation: arihant-searchform-slide 1s 0.4s both;*/
  -webkit-animation: arihant-searchresults-items-show 0.4s both;
  animation: arihant-searchresults-items-show 0.4s both;
  opacity: 1;
}
button.btn-in-open-search-header-search {
  left: 0;
  position: absolute;
  z-index: 1;
  top: 20px;
  background: none;
  width: 40px;
  border: none;
  height: 44px;
  cursor: pointer;
  background-repeat: no-repeat;
  /*background-image: url(../images/search-icon-header-input.png);*/
  background-position: 50% 50%;
  background-repeat: no-repeat;
  -webkit-transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: opacity 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}


button.close-all-searchdiv-box{
  position: absolute;
  top: 20px;
  background: none;
  border: none;
  right: 0px;
  cursor: pointer;
}
button.close-all-searchdiv-box:hover,
button.close-all-searchdiv-box:focus,
button.close-all-searchdiv-box:active{
  outline: none;
}

span.wrapper-close-btn-search-header-all {
    display: block;
    width: 100%;
    height: 100%;
    min-width: 26px;
    min-height: 34px;
}

span.close-btn-search-header-one-part {
  right: 12px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  height: 18px;
  width: 1px;
  background: #fff;
  position: absolute;
  display: block;
  top: 8px;
  z-index: 1;
}

span.close-btn-search-header-second-part {
  height: 18px;
  width: 1px;
  background: #fff;
  position: absolute;
  display: block;
  top: 8px;
  z-index: 1;
  left: 12px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}

.form-search-box-arihant{
  position: relative;
}

section.search-result-in-wrapper-one {
    background: #fff;
    padding: 20px 10px;
}
h3.search-result-quick-disc{
  font-size: 14px;
  line-height: 1;
  letter-spacing: -0.2px;
  text-transform: uppercase;
  color: #686868;
}

.overlay-wrp-s.active-arihant-row{
  position: fixed;
  height: 100vh;
  width: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 9;
  top: 0px;
  left:0px;
}

ul.arihant-all-list-view-serach-result {
  padding: 0px;
  margin: 0px;
}
ul.arihant-all-list-view-serach-result li{
  list-style: none;
}
ul.arihant-all-list-view-serach-result li a{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #686868;
  letter-spacing: -0.2px;
  display: block;
  font-size: 12px;
  padding: 6px 12px;
  text-decoration: none;
}

ul.arihant-all-list-view-serach-result li a:hover{
  background-color: #f2f2f2;
}
ul.arihant-all-list-view-serach-result li:nth-child(1){
  -webkit-animation-delay: .22s !important;
  animation-delay: .22s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(2){
  -webkit-animation-delay: .24s !important;
  animation-delay: .24s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(3){
  -webkit-animation-delay: .26s !important;
  animation-delay: .26s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(4){
  -webkit-animation-delay: .28s !important;
  animation-delay: .28s !important;
}

ul.arihant-all-list-view-serach-result li:nth-child(5){
  -webkit-animation-delay: .3s !important;
  animation-delay: .3s !important;
}
ul.arihant-all-list-view-serach-result li:nth-child(6){
  -webkit-animation-delay: .32s !important;
  animation-delay: .32s !important;
}
ul.arihant-all-list-view-serach-result li:nth-child(7){
  -webkit-animation-delay: .34s !important;
  animation-delay: .34s !important;
}
ul.arihant-all-list-view-serach-result li:nth-child(8){
  -webkit-animation-delay: .36s !important;
  animation-delay: .36s !important;
  
}
ul.arihant-all-list-view-serach-result li:nth-child(9){
  -webkit-animation-delay: .38s !important;
  animation-delay: .38s !important;
  
}
ul.arihant-all-list-view-serach-result li:nth-child(10){
  -webkit-animation-delay: .4s !important;
  animation-delay: .4s !important;
}

.main-menu-wrp.search-active-now-row ul.arihant-all-list-view-serach-result li{
  -webkit-animation: arihant-searchresults-items-show   0.4s both;
  animation: arihant-searchresults-items-show  0.4s both;
}

.main-menu-wrp.search-active-now-row ul.menu-wrp-link-main li.link-menu-li{
  -webkit-animation: slow-hiddsen-menu 0.4s both;
  animation: slow-hiddsen-menu 0.4s both;
}
ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: .345s;
  animation-delay: .345s;
}

ul.menu-wrp-link-main li.link-menu-li:nth-child(3){
  -webkit-animation-delay: .315s;
  animation-delay: .315s;
}

ul.menu-wrp-link-main li.link-menu-li:nth-child(4){
  -webkit-animation-delay: .28s;
  animation-delay: .28s;
}


.main-menu-wrp.search-active-now-row  .menu-wrp-all-users-com ul li{
  -webkit-animation: slow-hiddsen-menu 0.4s both;
  animation: slow-hiddsen-menu  0.4s both;
}

.menu-wrp-all-users-com ul li:nth-child(1){
  -webkit-animation-delay: .14s;
  animation-delay: .14s;
}

.menu-wrp-all-users-com ul li:nth-child(2){
  -webkit-animation-delay: .175s;
  animation-delay: .175s;
}
.menu-wrp-all-users-com ul li:nth-child(3){
  -webkit-animation-delay: .21s;
  animation-delay: .21s;
}
.menu-wrp-all-users-com ul li:nth-child(4){
  -webkit-animation-delay: .245s;
  animation-delay: .245s;
}




@-webkit-keyframes slow-hiddsen-menu {
    0% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
    40% {
        opacity: 1
    }
    100% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
}


@keyframes slow-hiddsen-menu {
    0% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
    40% {
        opacity: 1
    }
    100% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
}

@-webkit-keyframes arihant-searchform-slide {
    0% {
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes arihant-searchform-slide {
    0% {
        -webkit-transform: translate3d(200px, 0, 0);
        transform: translate3d(200px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}



@-webkit-keyframes arihant-searchresults-items-show {
    0% {
        opacity: 0;
        -webkit-transform: translateX(100px);
        transform: translateX(100px);
        -webkit-animation-timing-function: ease;
        animation-timing-function: ease
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@keyframes arihant-searchresults-items-show {
    0% {
        opacity: 0;
        -webkit-transform: translateX(100px);
        transform: translateX(100px);
        -webkit-animation-timing-function: ease;
        animation-timing-function: ease
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none
    }
}

@-webkit-keyframes arihant-books-searchform-slide {
    0% {
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes arihant-books-searchform-slide {
    0% {
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0);
        -webkit-animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1);
        animation-timing-function: cubic-bezier(0.12, 0.87, 0.15, 1)
    }
    100% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}
.main-menu-wrp.search-active-now-row.now-hide-menuall ul.menu-wrp-link-main{
  visibility: hidden;
}
.main-menu-wrp.search-active-now-row.now-hide-menuall .menu-wrp-all-users-com ul{
  visibility: hidden;
}





.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li{
  -webkit-animation: arihant-books-item-searchhide .4s both;
  animation: arihant-books-item-searchhide .4s both
}
.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: 0s;
  animation-delay: 0s
}
.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: 0.035s;
  animation-delay: 0.035s
}
.main-menu-wrp.animation-btn-all-clsls ul.menu-wrp-link-main li.link-menu-li:nth-child(2){
  -webkit-animation-delay: .07s;
  animation-delay: .07s
}



.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li{
  -webkit-animation: arihant-books-item-searchhide .4s both;
  animation: arihant-books-item-searchhide .4s both
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(1){
  -webkit-animation-delay: .105s;
  animation-delay: .105s
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(2){
  -webkit-animation-delay: .14s;
  animation-delay: .14s
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(3){
  -webkit-animation-delay: .175s;
  animation-delay: .175s
}
.main-menu-wrp.animation-btn-all-clsls .menu-wrp-all-users-com ul li:nth-child(4){
  -webkit-animation-delay: .21s;
  animation-delay: .21s
}



@-webkit-keyframes arihant-books-item-searchhide {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
    60% {
        opacity: 1
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
}

@keyframes arihant-books-item-searchhide {
    0% {
        opacity: 0;
        -webkit-transform: scale(0.7);
        transform: scale(0.7)
    }
    60% {
        opacity: 1
    }
    100% {
        opacity: 1;
        -webkit-transform: none;
        transform: none;
        -webkit-animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1);
        animation-timing-function: cubic-bezier(0.2727, 0.0986, 0.8333, 1)
    }
}

















/*======================================================================
                          SHOPPING CART PAGE
=======================================================================*/
.shopping-wrp {
    padding: 50px 0px 60px;
}

h3.shoppig-cart-title-wrp-arihant {
    font-size: 50px;
    letter-spacing: 0.2px;
    font-weight: 700;
    font-family: 'Oswald', sans-serif;
}
p.text-quetion-in-shopping-cart-page-arihant {
    font-size: 20px;
    font-family: 'Fira Sans', sans-serif;
    margin: 0px;
    font-weight: 500;
    color: #686868;
}
span.call-us-shopping-cart-pg-arihant {
    font-family: 'Fira Sans', sans-serif;
    font-size: 15px;
}
span.call-us-shopping-cart-pg-arihant a{
  color: #ef7215;
}
span.call-us-shopping-cart-pg-arihant a:hover,
span.call-us-shopping-cart-pg-arihant a:active,
span.call-us-shopping-cart-pg-arihant a:focus{
  text-decoration: none;
}


.tabel-shopping-cart{
}
.tabel-shopping-cart thead{
  background: #979a9b;
  font-family: 'Oswald', sans-serif;
  color: #fff;
}
.tabel-shopping-cart tbody{
  font-family: 'Fira Sans', sans-serif;
}
.tabel-shopping-cart .table td, .tabel-shopping-cart .table th{
  border-top: 1px solid #dee2e6ab;
}
.tabel-shopping-cart .table tbody tr:first-child td{
  padding: .75em .75rem 2em .75em;
  border-bottom: 0px !important;
}
.tabel-shopping-cart .table tbody td{
  padding: 2em .75rem 2em .75em;
  border-bottom: 1px solid #dee2e659;
}
.tabel-shopping-cart thead th{
  font-weight: 400;
  font-size:22px;
  text-transform: uppercase;
  padding:6px 20px;
}
.tabel-shopping-cart thead th:nth-child(1){
  width: 18%
}
.tabel-shopping-cart thead th:nth-child(2){
  width: 34%;
}
.tabel-shopping-cart thead th:nth-child(3){
  width: 14%;
  text-align: center;
}
.tabel-shopping-cart thead th:nth-child(4){
  width: 18%;
}
.tabel-shopping-cart thead th:nth-child(5){
  width: 14%;
}
.book-image-wrp-shopping-cart{
  text-align: center;
}

.shopping-carttable-margin-wrp-arihant{
  margin-top: 30px;
}

.title-shopping-cart-table-wrps{
  font-family: 'Fira Sans', sans-serif; 
}
.title-shopping-cart-table-wrps h3{
  font-size: 16px;
  margin-bottom: 4px;
}
.title-shopping-cart-table-wrps p{
  font-weight: 400;
  font-size: 10px;
  color: #686868;
}

.details-book-about-cart-shopping-pg{

}

.wrp-details-about-shopping-cart-pg{
  font-weight: 400;
  font-family: 'Fira Sans', sans-serif;
  color: #686868;
  padding: 5px 0px;
  font-size: 14px;
}
.use-icon-small{  
  font-size: 14px !important;
  margin-right: 2px;
}
span.first-table-cart-page-arihant{
  width: 110px;
  display: inline-block;
}
span.second-details-cart-page-arihant {
    width: 50px;
    display: inline-block;
}

a.detail-product-in-shopping-cart-table {
    font-size: 14px;
    color: #686868;
    display: inline-block;
    margin-top: 10px;
    border-bottom: 1px solid #b3b3b3;
}
a.detail-product-in-shopping-cart-table:hover,
a.detail-product-in-shopping-cart-table:focus,
a.detail-product-in-shopping-cart-table:active{
  text-decoration: none;
}
.btn-quantity-cart-page-wrp{
  text-align: center;
}
.btn-quantity-cart-page-wrp input{
  text-align: center;
  padding: 0px 5px;
  width: 55px;
  height: 34px;
  border: 1px solid #b9b9b9;
}
.btn-quantity-cart-page-wrp input[type=number]::-webkit-inner-spin-button, 
.btn-quantity-cart-page-wrp input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

.btn-quantity-cart-page-wrp input:hover,
.btn-quantity-cart-page-wrp input:active,
.btn-quantity-cart-page-wrp input:focus,
.btn-quantity-cart-page-wrp button:hover,
.btn-quantity-cart-page-wrp button:focus,
.btn-quantity-cart-page-wrp button:active{
  outline: none;
}

button.sub-quantity-cart {
    border: none;
    background: none;
    cursor: pointer;
    color: #ef7215;
    font-size: 25px;
}
button.add-quantity-cart {
    border: none;
    background: none;
    cursor: pointer;
    color: #ef7215;
    font-size: 25px;
}

.price-details-in-shopping-cart-current{

}
.price-details-in-shopping-cart-current img{
  float: left;
  margin-top: 8px;
}
.price-details-in-shopping-cart-current h3 {
    display: inline-block;
    font-size: 24px;
    margin-top: 5px;
    color: #686868;
}
.price-details-in-shopping-cart-current h3 i.fa.fa-inr{
  float: left;
  margin-right: 4px;
  font-size: 20px;
  margin-top: 5px;
}
span.cross-price-current {
    color: #ef7215;
    font-size: 16px;
}
.min-heright-shopping-cart{
  min-height: 70px;
}
p.delivery-details-shopping-cart {
  font-size: 14px;
  color: #686868;
  font-family: 'Fira Sans', sans-serif;
}
span.date-of-delivery-dd {
  display: block;
}

a.remove-class-btn-shopping-btn{

}

a.remove-class-btn-shopping-btn i{
  color: #686868;
  font-size: 14px;
}


a.remove-class-btn-shopping-btn:hover,
a.remove-class-btn-shopping-btn:active,
a.remove-class-btn-shopping-btn:focus{
  color: #686868;
}
a.remove-class-btn-shopping-btn img{
  margin-top:10px;
}
a.remove-class-btn-shopping-btn span {
  display: inline-block;
  color: #686868;
  font-size: 13px;
  padding: 0px 4px 0px 1px;
  border-bottom: 1px solid #888888;
}

a.remove-class-btn-shopping-btn:hover{
  text-decoration: none;
}


.row.mrgn-row-last-wrp-shopping-cart{
  font-family: 'Fira Sans', sans-serif;
  margin-top:10px;
}

.width-18-in-shopping-paymeny {
    width: 18%;
}
.payment-options-shopping-cart-pg{
  padding-left: 15px;
}
.payment-options-shopping-cart-pg p{
  font-size: 15px;
  margin: 0px;
  color: #686868;
}

.coupon-details-use-shopping-cart-page{

}
.coupon-details-use-shopping-cart-page p{
  font-size: 15px;
  color: #686868;
  margin: 0px;
  font-weight: 500;
}
.coupon-details-use-shopping-cart-page span{
  font-size: 12px;
  color: #000;
  font-weight: 400;
  margin-top: 5px;
  line-height: 14px;
  display: inline-block;
}
.box-wrp-check-coupon-ws-shopping{
  align-items: center;
}
input.coupon-check-input-box{
  width: 50%;
  font-size: 12px;
  height: 26px;
  padding: 0px 5px;
  background: #fafafa;
  border: 1px solid #e4e5e5;
}
input.coupon-check-input-box:active,
input.coupon-check-input-box:focus{
  outline: none;
}
button.btn-coupon-click-check{
  text-transform: uppercase;
  font-size: 12px;
  color: #ef7215;
  border: none;
  background: none;
  letter-spacing: -0.2px;
  cursor: pointer;
  font-weight: 500;
}
button.btn-coupon-click-check:hover,
button.btn-coupon-click-check:active,
button.btn-coupon-click-check:focus{
  outline: none;
}


.width-32-in-table-show-final-rate-arihant {
  width: 32%;
  padding-right: 15px;
  float: left;
}
.wrp-table-box-bg-shhs{
  background: #f7f7f7;
  width: 100%;
  padding: 15px 15px;
  border-radius: 4px;
}

.row-in-all-part-money-detail-books {
  align-items: center;
    padding-bottom: 10px;
}
.first-pera-in-money-detail-shoppings-pg {
  float: left;
  font-size: 15px;
  color: #000;
  font-weight: 400;
}
.second-part-in-right-money-detail-shoppings-pg {
  float: right;
  font-size: 15px;
  color: #000;
  font-weight: 400;
}
.second-part-in-right-money-detail-shoppings-pg i{
  color: #686868;
}
img.in-use-three-img-icons {
    width: 11px;
}

.wrp-table-box-bg-shhs .row-in-all-part-money-detail-books:nth-child(4){
  border-bottom: 1px solid #cccccc3d;
}
.row-last-in-this-part-money-detail-books{
  align-items: center;
  padding-top: 10px;
}
.first-main-in-money-detail-shoppings-pg {
  font-size: 18px;
  float: left;
  font-weight: 500;
}
.second-part-in-main-right-money-detail-shoppings-pg {
  float: right;
  font-size: 18px;
  font-weight: 500;
} 
.second-part-in-main-right-money-detail-shoppings-pg span i{
  margin-right: 4px;
}

.row-this-btn-continue-and-checkout {
  padding-top: 20px;
}
.left-this-continue-btn-in-shopping-pg-cart {
  float: left;
}
.left-this-continue-btn-in-shopping-pg-cart a{
  font-size: 14px;
  margin-top: 5px;
  padding-bottom: 5px;
  color: #686868;
  display: inline-block;
  border-bottom: 1px solid #b9b9b9;
}
.left-this-continue-btn-in-shopping-pg-cart a:hover,
.left-this-continue-btn-in-shopping-pg-cart a:focus,
.left-this-continue-btn-in-shopping-pg-cart a:active{
  text-decoration: none;
}
.this-right-checkout-button-wrp {
    float: right;
}

button.btn-proceed-now {
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    border: none;
    padding: 6px 10px;
    color: #fff;
    text-transform: uppercase;
    cursor: pointer;
    font-size: 18px;
    font-family: 'Oswald', sans-serif;
}
button.btn-proceed-now:active,
button.btn-proceed-now:focus,
button.btn-proceed-now:hover{
  outline:none;
}












/*============================================================================
                            Category
==============================================================================*/
.category-main-wrp {
    padding: 50px 0px;
}
.side-menu-bar{
  font-family: 'Fira Sans', sans-serif;
}
.side-menu-bar .card-header{
  padding: 0px;
}

.side-menu-bar .card{
  border:none;
}


h5.btn-link-wrp-user-category-pg{
  font-size: 15px;
  font-weight: 400;
  margin: 0px;
  cursor: pointer;
  padding: 15px 8px;
  background: white;
}
h5.active-menu-this-here {
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    color: #fff;
    font-weight: 300;
    padding: 15px 9px;
    margin: 0px;
    font-size:16px;
}

h5.btn-link-wrp-user-category-pg:after {
  content: "";
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0px;
  height: 1px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
i.fa.fa-plus.this-use-up{
  background: -webkit-linear-gradient(#FF3448 , #FF9B26 );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.btn-link-wrp-user-category-pg.collapsed i.fa.fa-minus.this-use-open-now{ 
  display: none;
}

.btn-link-wrp-user-category-pg i.fa.fa-plus.this-use-up{
  display: none;
}
.btn-link-wrp-user-category-pg.collapsed i.fa.fa-plus.this-use-up{
  display: inline-block;
}

.btn-link-wrp-user-category-pg i{
  float: right;
  font-size: 10px;
  padding: 6px 5px;
}

ul.under-bold-soft-menus{
  padding:0px;
  margin:0px;
}

ul.under-bold-soft-menus li {
  list-style: none;
}
ul.under-bold-soft-menus li a{
  font-size: 14px;
  margin: 0px;
  font-weight: 400;
  color: #000;
  padding: 6px 15px 6px 25px;
  display: block;
  border-bottom: 1px solid #ccc;
}

ul.under-bold-soft-menus li a:hover{
  text-decoration: none;
}


.page-row-filter{
  font-family: 'Fira Sans', sans-serif;
}
ul.page-arrow-like-homes {
  margin: 0px;
  padding: 10px 0px;
  border-bottom: 1px solid #ccc;
  margin-bottom: 20px;
}

ul.page-arrow-like-homes li{
  float: left;
  list-style: none;
}
ul.page-arrow-like-homes li a{
  display: inline-block;
  padding: 2px 12px;
  position: relative;
  font-size: 12px;
  color: #686868;
}
.last-icons-slsl{
  display: inline-block;
  padding: 2px 12px;
  position: relative;
  font-size: 12px;
  color: #686868;
}

ul.page-arrow-like-homes li a:after {
    content: "\f105";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    position: absolute;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    top: 4px;
    right: -3px;
}
ul.page-arrow-like-homes li:last-child a:after{
  display: none;
}


ul.page-arrow-like-homes li a:hover,
ul.page-arrow-like-homes li a:focus,
ul.page-arrow-like-homes li a:active,
a.btn-sort-filter-allsis:active,
a.btn-sort-filter-allsis:hover,
a.btn-sort-filter-allsis:focus{
  text-decoration: none;
}

.manage-filter-row-in-front {
  padding: 0px 0px 20px 0px;
}
a.btn-sort-filter-allsis {
  padding: 6px 10px;
  min-width: 120px;
  font-family: 'Fira Sans', sans-serif;
  position: relative;
  display: inline-block;
  background: white;
  color: #686868;
  text-transform: uppercase;
  border-radius: 2px;
}
a.btn-sort-filter-allsis img{
  padding: 0px 0px 0px 4px;
}
a.btn-sort-filter-allsis:after {
  width: 102%;
  height: 106%;
  content: "";
  border-radius: 2px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  position: absolute;
  left: -1px;
  z-index: -1;
  top: -1px;
}




.product-wrp-listing{
  font-family: 'Fira Sans', sans-serif;
  margin-bottom:40px;
}
.book-img-wrp-listing{
  text-align: center;
}
.book-img-wrp-listing img{
  width: 95%;
}



.price-two-divide {
    padding: 5px 0px;
    width: 95%;
    margin:0px auto;
}
span.leftprice-minus-listing {
  float: left;
  font-size: 12px;
  font-weight: 500;
  padding-top:5px;
  color: #979a9b;
}
span.leftprice-minus-listing img {
  width: 9px;
  float: left;
  margin-top: 3px;
}



span.rightprice-this-listing {
  float: right;
  font-size: 18px;
  color: #ef7215;
  font-weight: 500;
}
span.rightprice-this-listing img{
  width: 14px;
  float: left;
  margin-top: 4px;
}
span.rightprice-this-listing i{
  color: #979a9b;
  font-size: 17px;
  float: left;
  margin-top: 4px;
  margin-right: 2px;
}
.offer-price-thiss{
  float: left;
  font-size: 10px;
  color: #686868;
  display: inline-block;
  font-weight: 400;
  padding: 6px 5px 0px 0px;
  font-family: 'Fira Sans', sans-serif;
}
.book-name-row-this{

}
h3.book-name-this-is-item-product{
  font-size: 12px;
  font-size: 0.78em;
  text-align: center;
  margin: 0px;
  height: 30px;
  line-height: 16px;
  overflow: hidden;
}






.btn-row-exprole-wrp{
  padding:10px 3px 0px; 
}
.exprole-wrp-btn-in-product-div {
    width: 40%;
    float: left;
    text-align: center;
}
.exprole-wrp-btn-in-product-div a{
  display: block;
  padding: 0px 7px;
  width: 100%;
  border: 1px solid #b9b9b9;
}
.exprole-wrp-btn-in-product-div a:hover{
  background: #000;
  border: 1px solid #000;
  color: #fff;
}
.exprole-wrp-btn-in-product-div a:hover span,
.exprole-wrp-btn-in-product-div a:hover i{
  color: #fff;
}


.exprole-wrp-btn-in-product-div a img{
  width: 12px;
}
.exprole-wrp-btn-in-product-div a i{
  font-size: 12px;
  color: #333;
}
.exprole-wrp-btn-in-product-div a span{
  color: #000;
  font-size: 10px;
  text-transform: uppercase;
  line-height: 28px;
  padding: 0px 0px 0px 0px;
  letter-spacing: -0.2px;
}
.heart-like-btn{
  width: 16%;
  float: left;
  text-align: center;
}
.heart-like-btn .wishlist-product-btn-in-product-arihant{
  display: inline-block;
  color: #ef7215;
  padding: 3.2px 0px 3px 0px;
}
.wishlist-product-btn-in-product-arihant img{
  width:17px;
}
.wishlist-product-btn-in-product-arihant img.second{
  display: none;
}
.wishlist-product-btn-in-product-arihant:hover img.first{
  display: none;
}
.wishlist-product-btn-in-product-arihant:hover img.second{
  display: inline-block;
}
.heart-like-btn i.fa.fa-heart{
  display: none;
}

.heart-like-btn:hover i.fa.fa-heart-o{
  display: none;
}

.heart-like-btn:hover i.fa.fa-heart{
  display: inline-block;
}

.add-to-cart-btn-product-in-arihant{
  float: left;
  width: 44%;
  text-align: center;
}
.add-to-cart-btn-product-in-arihant a{
  background: #ef7215;
  display: inline-block;
  width: 100%;
  padding: 0px 5px;
  border: 1px solid #ef7215;
}
.add-to-cart-btn-product-in-arihant a:hover{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
.add-to-cart-btn-product-in-arihant a i{
  font-size: 12px;
  color: #fff;
}
.add-to-cart-btn-product-in-arihant a img{
  width: 16px;

}
.add-to-cart-btn-product-in-arihant a span{
  font-size: 10px;
  line-height: 28px;
  text-transform: uppercase;
  letter-spacing: -0.2px;
  color: #fff;
  padding: 0px 0px 0px 0px;
}

.exprole-wrp-btn-in-product-div a:focus,
.exprole-wrp-btn-in-product-div a:hover,
.exprole-wrp-btn-in-product-div a:active,
.heart-like-btn .wishlist-product-btn-in-product-arihant:hover,
.heart-like-btn .wishlist-product-btn-in-product-arihant:focus,
.heart-like-btn .wishlist-product-btn-in-product-arihant:active,
.add-to-cart-btn-product-in-arihant a:hover,
.add-to-cart-btn-product-in-arihant a:active,
.add-to-cart-btn-product-in-arihant a:focus{
  text-decoration: none;
}



/*=================================================================
                          Product Detail Page
==================================================================*/


.product-row-detail-in-arihant{

}
.product-img-in-arihant-page{

}
.product-img-in-arihant-page img{
  width:100%;
}
.share-item-product-book-details{
  text-align:center;
  padding-top:5px;
}
.share-item-product-book-details span{
  font-weight: 300;
  font-family: 'Fira Sans', sans-serif;
  font-size: 12px;
}
.share-item-product-book-details a{
  display: inline-block;
  color: #686868;
  font-size: 12px;
  padding:0px 2px;
}


.product-disc-details-in-arihant {
    padding-left: 20px;
}
h3.arihant-product-name {
  font-size: 30px;
  font-weight: 600;
  font-family: 'Fira Sans', sans-serif;
}
span.product-create-by-arihant {
  display: block;
  margin-bottom: 10px;
  font-weight: 400;
  font-family: 'Fira Sans', sans-serif;
  color: #686868;
  padding: 2px 0px;
  font-size: 14px;
}

.download-wrp-width-this-arihant{
  display: inline-block;
  width: 150px;
}

.download-some-rear-details-products{
  padding:15px 0px;
}
.download-wrp-width-this-arihant a{
  
}

.download-wrp-width-this-arihant img{
  width: 36px;
}
.download-wrp-width-this-arihant span.product-pdf-download-pdf-text{
  font-weight: 500;
  font-size: 15px;
  color: #686868;
}
.download-ebook-arihant-book-wrp{
  display: inline-block;
}
.download-ebook-arihant-book-wrp a{
}
.download-ebook-arihant-book-wrp img{
  width: 36px;
}

.download-wrp-width-this-arihant a:hover,
.download-wrp-width-this-arihant a:active,
.download-ebook-arihant-book-wrp a:hover,
.download-ebook-arihant-book-wrp a:active,
a.timing-check-pin-btn-arihant:hover,
a.timing-check-pin-btn-arihant:focus,
a.timing-check-pin-btn-arihant:active{
  text-decoration: none;
}



.download-ebook-arihant-book-wrp span.product-ebook-download-text-arihant{
  font-weight: 500;
  font-size: 15px;
  color: #686868;
}


.wrp-delivery-message-good-and-fast-arihant {
    padding: 5px 0px 0px 0px;
    font-family: 'Fira Sans', sans-serif;
}

.deliver-wrp-book-text-arihant {
  width: auto;
  display: inline-block;
  float: left;
  padding: 10px 0px;
}
span.delivery-text{
  font-size:15px;
  font-weight: 400;
  padding-right: 10px;
  color: #686868;
}
.text-and-check-delivery-point-day-wrp {
  display: inline-block;
  float: left;
  width: auto;
}
.input-check-wrp-box-secs{
  position: relative;
  width: 180px
}

.input-check-wrp-box-secs .input-box-check-pin{
  border: none;
  border-bottom: 1px solid #686868;
  font-size: 12px;
  padding: 4px 0px 1px 15px;
  width: 100%;
  min-height: 20px;
}
.input-check-wrp-box-secs .input-box-check-pin:focus,
.input-check-wrp-box-secs .input-box-check-pin:active,
.input-check-wrp-box-secs .input-box-check-pin:hover,
.input-check-wrp-box-secs .input-box-check-pin:visited{
  outline: none;
}
.timing-check-with-pin{

}
.timing-check-with-pin span.timing-pin-text-with-area{
  font-size: 12px;
  font-weight: 500;
  display: block;
  line-height: 16px;
  padding-top:5px;
  color: #686868;
  padding-left:6px;
}
.input-check-wrp-box-secs i{
  position: absolute;
  top: 6px;
  left: 4px;
  color: #686868;
  font-size: 12px;

}
.check-btn-div-box-area-in-lol {
  width: auto;
  float: left;
  display: inline-block;
}
a.timing-check-pin-btn-arihant {
  font-size: 15px;
  color: #ef7215;
  font-weight: 500;
  padding: 6px 10px;
  display: block;
}
.price-btn-row-set-prouct-detail-arihant {
    font-family: 'Fira Sans', sans-serif;
}

.product-price-in-short-rate{
  padding-top:10px;
}
i.ruppee-icons-in-price{
  display: inline-block;
  margin-top: 5px;
  float: left;
}

i.ruppee-icons-in-price img{
  
}
h3.price-book-arihant-product {
  display: inline;
  color: #686868;
  font-size: 28px;
  font-weight: 500;
}
h3.price-book-arihant-product i{
  font-size: 25px;
  float: left;
  margin-top: 6px;
  margin-right: 4px;
}
span.offert-in-off-product-sale{
  font-size: 15px;
  color: #686868;
}

.btn-row-in-product-add-and-wishlist-arihant{
  font-family: 'Fira Sans', sans-serif;
  margin-top: 4px;
}
.wrp-row-box-btn-add-to-cart-in-product-arihant{
  display: inline-block;
  width: 160px;
}
.responsive-view-only-desk{
  display: none;
}
.desktop-none-this{
  display: none;
}
.responsive-close-btn-to-category{
  position: absolute;
  top: -35px;
  right: 0px;
  z-index: 99999;
}
a.open-side-menuss-category {
    color: #333;
    padding: 8px 14px;
    display: inline-block;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
    color: #fff;
}
a.open-side-menuss-category:focus,
a.open-side-menuss-category:hover,
a.open-side-menuss-category:active{
  text-decoration: none;
}
a.open-side-menuss-category span{
  text-transform: uppercase;
  font-size: 14px;
  padding-left: 4px;
}
a.open-side-menuss-category i{
  font-size: 14px;
}

.responsive-close-btn-to-category{
  display: none;
}
.col-sm-3.thhis-in-responsive-view-and-dpdp.block-nowss{
  display: block;
}
.responsive-close-btn-to-category a{
  color: #fff;
  padding: 5px 12px;
  display: inline-block;
  background: #000;
}


a.btn-add-to-cart-product-book{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding: 8px 20px;
  display: inline-block;
}
a.btn-add-to-cart-product-book img{
  float: left;
  width: auto;
}
a.btn-add-to-cart-product-book span{
  font-size: 15px;
  font-weight: 500;
  color: #fff;
  padding: 2px 0px 0px 5px;
  display: inline-block;
}

.wrp-row-box-btn-add-to-wishlist-in-product-arihant{
  display: inline-block;
  width: auto;
}
a.btn-add-to-wishlist-product-book{
  padding: 8px 20px;
  display: inline-block;
  border: 1px solid #b3b3b3;
}
a.btn-add-to-wishlist-product-book:hover{
  background: #000;
}
a.btn-add-to-wishlist-product-book img{
  float: left;
  width: 22px;
  margin-right: 3px;
}

a.btn-add-to-wishlist-product-book img.white{
  display: none;
}

a.btn-add-to-wishlist-product-book:hover img.gray{
  display: none;
}

a.btn-add-to-wishlist-product-book:hover img.white{
  display: inline-block;
}
a.btn-add-to-wishlist-product-book i{
  color: #686868;
}
a.btn-add-to-wishlist-product-book span{
  font-size: 15px;
  font-weight: 500;
  color: #686868;
  padding: 0px 0px 0px 5px;
  display: inline-block;
}
a.btn-add-to-wishlist-product-book:hover span{
  color: #fff;
}
a.btn-add-to-wishlist-product-book:hover i{
  color: #fff;
}

a.btn-add-to-wishlist-product-book:hover{
  text-decoration: none;
}
.nav-tabs-acordings-arihant ul li a:hover {
    background: #eee;
}
.nav-tabs-acordings-arihant ul li a:hover span{
  background: #eee;
}
.nav-tabs-acordings-arihant ul li a span:after {
    position: absolute;
    content: "";
    background: #ddd;
    width: 100%;
    left: 0px;
    bottom: 0px;
    height: 0px;
}
.nav-tabs-acordings-arihant ul li a.active:hover{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%) !important;
}
.nav-tabs-acordings-arihant ul li a.active:hover span{
  background: #fff;
}
.nav-tabs-acordings-arihant ul li a.active:hover{
  background: #eee;
}
.nav-tabs-acordings-arihant ul li a:hover span:after{
  height: 2px;
}
.nav-tabs-acordings-arihant ul li a.active:hover span:after{
  height: 0px;
}
.aditional-charges-rate-wrp{
  font-family: 'Fira Sans', sans-serif;
}
.aditional-charges-rate-wrp p{
  margin: 0px;
  font-size: 13px;
  padding: 10px 0px;
}
.nav-tabs-acordings-arihant{
  font-family: 'Fira Sans', sans-serif;
  padding-top:34px;
}

.nav-tabs-acordings-arihant ul li{
}
.nav-tabs-acordings-arihant ul li a{
  position: relative;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding:0px 0px 1px 0px;
  margin-right: 12px;
}

.nav-tabs-acordings-arihant ul li a span{
  font-size: 20px;
  color: #686868;
  background: #fff;
  padding: 5px 17px 4px 17px;
  font-weight: 300;
  display: inline-block;
}

.nav-tabs-acordings-arihant ul li a.active:hover{
  cursor: default;
}
.nav-tabs-acordings-arihant ul li a.active{
  position: relative;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding: 1px;
  margin-right: 10px;
}
.padding-left30px{
  padding-left:50px;
}
.nav-tabs-acordings-arihant ul li a.active span{
  padding:4px 17px;
  color:#ef7215; 
}
.text-area-tabs-all-product-disc{

}
.text-area-tabs-all-product-disc p{
  font-size:16px;
  color: #000;
  font-weight: 300;
  font-family: 'Fira Sans', sans-serif;
}
.nav-tabs-acordings-arihant .container.tab-pane {
  padding:0px; 
  padding-right: 10px;
}
.text-area-tabs-all-product-disc ul li{
  font-weight: 300;
}


.similar-product-row-wrp.row {
  font-family: 'Fira Sans', sans-serif;
  padding-top:40px;
}
h3.similar-rpdocut-title-text{
  font-size:30px;
  padding-bottom: 20px;
  color: #000;
}
.similar-product-row-wrp.row .col-sm-2{
  padding: 0px 10px;
}
.similar-product-wrp-slide{
  text-align: center;
}
.details-similar-product{
  text-align: center;
}
.details-similar-product h3{
  font-size:11px;
  height: 30px;
  line-height: 15px;
  font-weight: 400;
  margin-top: 10px;
  overflow: hidden;
}

.price-in-center-all-set-similar{
  text-align: center;
}
.price-in-center-all-set-similar img{
  display: inline-block;
  width: 10px;
}
.price-in-center-all-set-similar span{
  display: inline-block;
  font-size: 15px;
  color:#ef7215;
  font-weight: 500;
}
.price-in-center-all-set-similar span i{
    font-size: 13px;
    color: #6e6e6e;
    margin-right: 4px;
}

.set-of-all-under-details-listss-arihants p{
  margin:0;
}
















/*=============================================================================
                        Responsive View Cart Page
================================================================================*/

.manage-row-responsive-title-asss{
  background: #fff;
  padding: 15px 0px 8px;
}
.row.manage-row-responsive-title-asss .col-6:first-child{
  padding-right: 0px;
}
.row.manage-row-responsive-title-asss .col-6:last-child{
  padding-left: 0px;
}
.mrgrn-manage-rpoduct-all-responsive-usses{
  margin-top: 15px;
}
.responsive-view-this{
  display: none;
}

.col-12.row-product-wrp-all-res-arihant-books{
  padding:0px;
  margin-bottom: 10px;
  background: #ffffff;
  box-shadow: 1px 1px 7px #ccc;
}
.row-of-product-main-item{
  margin-left: 16px;
  margin-right: 8px;
  margin-top: 16px;
}
.product-section-arihant-resposive-v {
  width: 73%;
  float: left;
}
.product-section-arihant-resposive-v h3.name-of-product-cart-resp-view{
  font-size: 14px;
  font-family: 'Fira Sans', sans-serif;
  margin:0px;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 16px;
  white-space: nowrap;
}
.product-section-arihant-resposive-v  h3.price-is-all-use-in-cart-res{
  font-size: 15px;
  margin: 0px;
}
.product-section-arihant-resposive-v h3.price-is-all-use-in-cart-res{
  font-size: 16px;
  margin: 0px;
  padding: 6px 0px;
}
.product-section-arihant-resposive-v h3.price-is-all-use-in-cart-res i{
  float: left;
  margin-top: 3px;
  font-size: 15px;
  margin-right: 1px;
}

.product-section-arihant-resposive-v span{
  font-size:12px;
  font-family: 'Fira Sans', sans-serif;
}
.details-images-cart-page-arihant {
  float: left;
  width: 26%;
  text-align: center;
}
.details-images-cart-page-arihant img{
  width: 80%;
  padding:0px 0px;
}

.res-views-details-book-about-cart-shopping-pg {
  width: 70%;
  float: left;
  margin-left: 15px;
}
.res-views-wrp-details-about-shopping-cart-pg {
  font-weight: 400;
  font-family: 'Fira Sans', sans-serif;
  color: #686868;
  padding: 2px 0px;
  font-size: 11px;
}

span.res-views-first-table-cart-page-arihant {
  width: 70px;
  display: inline-block;
}
span.res-views-second-details-cart-page-arihant {
  width: 40px;
  display: inline-block;
}


.quantity-product-wrp-resp-arihant-allsse {
    float: left;
    width: 25%;
}
.quantity-product-wrp-resp-arihant-allsse select{
  border:none;
  padding: 1px 4px;
  background: #fff;
  font-size: 12px;
}

.quantity-product-wrp-resp-arihant-allsse  .wrp-all-main-sepeopw{
  border: 1px solid #ddd;
  margin: 7px 0px;
  display: inline-block;
}
.quantity-product-wrp-resp-arihant-allsse span .name-qty-spsps{
  font-size: 13px;
  padding: 4px 0px 4px 6px;
  display: inline-block;
}

.quantity-product-wrp-resp-arihant-allsse select:active,
.quantity-product-wrp-resp-arihant-allsse select:focus,
.quantity-product-wrp-resp-arihant-allsse select:hover{
  outline: none;
}



.row-product-detailsss-resp-arihant {
  border-top: 1px solid rgb(240, 240, 240);
  margin-top: 5px;
  font-family: 'Fira Sans', sans-serif;
}
.product-detail-rep-btn-use-arihant {
  float: left;
  width: 50%;
  border-right:1px solid rgb(240, 240, 240);
}

.product-detail-rep-btn-use-arihant a{
  display: block;
  text-align: center; 
  font-size: 13px;
  color:#333;
  padding: 10px 0px;
}
.remove-btn-wrapper-res-wrp-slals{
  float: left;
  width:50%;
  text-align: center;
}
.remove-btn-wrapper-res-wrp-slals a{
  display: block;
  color:#333;
  font-size: 13px;
  padding: 10px 0px;

}

.product-detail-rep-btn-use-arihant a:hover,
.product-detail-rep-btn-use-arihant a:active,
.product-detail-rep-btn-use-arihant a:focus,
.remove-btn-wrapper-res-wrp-slals a:focus,
.remove-btn-wrapper-res-wrp-slals a:hover,
.remove-btn-wrapper-res-wrp-slals a:active{
  text-decoration: none;
}


.row.manage-coupon-code-resp-rowss{
  background: #fff;
  padding: 12px 0px;
  box-shadow: 1px 1px 7px #ccc;
  margin-top: 20px;
}
.row.manage-coupon-code-resp-rowss span{
  margin-bottom: 10px;
}
.row.manage-coupon-code-resp-rowss input.coupon-check-input-box{
  width:60%;
}
.row-manage-close-all-this-price-total-res-arihant.row {
  background: #fff;
  box-shadow: 1px 1px 7px #ccc;
  margin-top: 15px;
  padding-top: 10px;
  padding-bottom: 10px;
}
.row-manage-close-all-this-price-total-res-arihant.row .wrp-table-box-bg-shhs{
  background: none;
  padding: 0px;
}
.btn-all-set-last-arihant-resp.row {
    margin-top: 20px;
    margin-bottom: 20px;
}
.countinue-btn-all-responsive-trs{
  float: left;
  width: 50%;
}
.this-view-responsivesss{
  display: none;
}
.this-view-desktopss{
  display: block;
}
.countinue-btn-all-responsive-trs a{
  color: #333;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
  display: inline-block;
  border-bottom: 1px solid #8a8a8a;
  padding: 10px 4px 7px 5px;
}
.btn-all-resp-checkout-proceed-hds{
  float: left;
  width: 50%;
}
.btn-all-resp-checkout-proceed-hds a{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  border: none;
  padding: 6px 10px;
  color: #fff;
  text-transform: uppercase;
  cursor: pointer;
  font-size: 16px;
  font-family: 'Oswald', sans-serif;
  display: block;
}


.countinue-btn-all-responsive-trs a:hover,
.countinue-btn-all-responsive-trs a:active,
.countinue-btn-all-responsive-trs a:focus,
.btn-all-resp-checkout-proceed-hds a:hover,
.btn-all-resp-checkout-proceed-hds a:active,
.btn-all-resp-checkout-proceed-hds a:focus{
  text-decoration: none;  
}
















/*================================================================================================
                                        Career Page
==================================================================================================*/

.image-in-top-manage-with-career-arihant{
  width:100%;
}
.image-in-top-manage-with-career-arihant img{
  width: 100%;
}


.manager-row-in-passion-career-pg{
  padding-top:40px;
}

.text-center.border-title-passion-career {
    border-bottom: 1px solid #b9b9b9;
}
h2.title-passion-career-section {
    display: inline-block;
    margin: 0px;
    position: relative;
    font-family: 'Fira Sans', sans-serif;
    padding-bottom: 14px;
    font-weight: 600;
    font-size: 38px;
    color: #000;
}
h2.title-passion-career-section:after {
    position: absolute;
    content: "";
    width: 100%;
    background: #000;
    height: 1px;
    top: 100%;
    left: 0px;
}
.margin-none-in-passion-inbox-arihantss{
  margin: 0px;
  padding: 45px 30px 20px 30px;
}


.wrp-passion-sec-box-career-page{

}
.wrp-passion-sec-box-career-page a{
  color: inherit;
}
.wrp-passion-sec-box-career-page a:hover,
.wrp-passion-sec-box-career-page a:active,
.wrp-passion-sec-box-career-page a:focus{
  text-decoration: none;
}
.passion-small-icon-wrp-pasion{
  text-align: center;
  font-family: 'Fira Sans', sans-serif;
}
.passion-small-icon-wrp-pasion img{
  width: 86px;
  margin:0px auto;
}
.wrp-passion-sec-box-career-page a:hover .passion-small-icon-wrp-pasion img.not-active{
  display: none;
}
.passion-small-icon-wrp-pasion img.active{
  display: none;
}
.wrp-passion-sec-box-career-page a:hover .passion-small-icon-wrp-pasion img.active{
  display: block;
}
.wrp-passion-sec-box-career-page a:hover .title-icon-passion-career-arihant h4{
  color:#26a97b;
}

.title-icon-passion-career-arihant {
    text-align: center;
    padding: 15px 0px;
}
.title-icon-passion-career-arihant h4{
  font-size:18px;
}


.manager-row-in-work-culture-career-pg{
  padding-bottom: 30px;
  padding-top:40px;
  font-family: 'Fira Sans', sans-serif;
}

.title-text-changess-pera-disc-work-culture p{
  font-size: 14px;
  text-align: center;
  padding-top:20px;
}


.slider-arihant-career-culture-user .carousel-indicators{
  bottom:-40px;
}

.slider-arihant-career-culture-user .carousel-indicators li{
  width: 10px;
  height:10px;
  margin:0px 3px;
  background: #cacaca;
  border-radius:  50%;
  cursor: pointer;
}
.slider-arihant-career-culture-user .carousel-indicators li.active{
  background: #ed7215;
}




.featured-jobs{
  position: relative;
  font-family: 'Fira Sans', sans-serif;
}
.featured-image-bg-offical-jobsd{
  width: 100%;
}
.featured-image-bg-offical-jobsd img{
  width:100%;
}

.featured-jobs-posi-arihant-careers{
  position: absolute;
  top: 40px;
  left: 0px;
  width: 100%;
}




.job-table-wrapper-career-arihant{
  width:60%;
  margin: 0px auto;
  padding-right: 7%;
  font-family: 'Fira Sans', sans-serif;
}
.wrp-row-all-use-other-job-career-feature{
  padding: 7px 0px;
}

.header-part-in-box-job-career-arihant {
  width: 40%;
  float: left;
}
.header-part-in-box-department-career-arihant {
  width: 25%;
  float: left;
}
.header-part-in-box-location-career-arihant {
  float: left;
  width: 25%;
}
.wrp-row-all-use-other-job-career-feature.body-table-jobs-manage-arihant {
  padding: 25px 0px;
  border-bottom: 1px solid #c3c5c5;
}
span.detail-of-head-table-job {
  font-size: 12px;
  color: #686868;
  text-transform: uppercase;
}

h4.details-jobs-name-arihant-career-feature{
  margin: 0px;
  font-size: 18px;
}
span.name-of-department-arihant-jobs-career {
    font-size: 12px;
    color: #ed7215;
}
span.job-location-of-arihant-career-feature {
    font-size: 14px;
    color: #000;
}


.publish-with-us-career-arihant-wrp{
  position: relative;
}
.wrp-all-rel-desk-image{
  width: 100%;
}
.wrp-all-rel-desk-image img{
  width: 100%;
}
.this-is-posi-wrp-div{
  width: 100%;
  position: absolute;
  top: 40px;
  left: 0px;
}

.details-of-manage-conect-wrp{
  padding-left: 40px;
}
h3.publish-with-text-wrp-ss{
  font-family: 'Oswald', sans-serif;
  font-size: 36px;
  color: #fff;
  font-weight: 600;
  letter-spacing: 1px;
}
.details-of-manage-conect-wrp p{
  color: #fff;
  font-size: 12px;
  font-weight: 400;
}


.position-this-button-to-publish-career-arihant-contact{
  font-family: 'Oswald', sans-serif;
  position: absolute;
  right: 10%;
  bottom: 35%;
}
.button-contact-career-wrp-spd{

}
a.button-wrp-ss-career-arihant-contact{
  background: white;
  padding: 10px 30px;
  display: inline-block;
  font-weight: 600;
  color: #ad3d87;
  font-size: 22px;
  border-radius: 30px;
  box-shadow: 0px 0px 8px #00000052;
}
a.button-wrp-ss-career-arihant-contact:hover,
a.button-wrp-ss-career-arihant-contact:focus,
a.button-wrp-ss-career-arihant-contact:active{
  text-decoration: none;
}


.row.Recruitment-process-career {
  padding-top: 50px;
  padding-bottom: 60px;
}
.text-center.border-title-recruitment-career {
  border-bottom: 1px solid #b9b9b9;
}

h2.title-recruitment-career-section {
  display: inline-block;
  margin: 0px;
  position: relative;
  font-family: 'Fira Sans', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  font-size: 38px;
  color: #000;
}

h2.title-recruitment-career-section:after {
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0px;
}
.Recruitment-process-career .tab-content{
  padding-top: 40px;
}

.applying-to-arihant-process-career-wrp {
  font-family: 'Fira Sans', sans-serif;
  padding-top: 20px;
}
.applying-to-arihant-process-career-wrp h4{
  font-size: 20px;
  color: #ed7215;
  text-align: center;
  font-weight: 500;
}
.applying-to-arihant-process-career-wrp p{
  font-size: 12.5px;
  letter-spacing: -0.2px;
  text-align: center;
  color: #000000;
}


.tabs-on-career-all-applying{
  font-family: 'Fira Sans', sans-serif;
}

.wrp-all-tab-applying-career-ul-arihant{
  padding: 0px 20px;
}

.wrp-all-tab-applying-career-ul-arihant ul li{
  width: 20%; 
}
.wrp-all-tab-applying-career-ul-arihant ul li a{
  display: block;
  background: #f4f4f4;
  text-align: center;
  font-weight: 500;
  padding: 10px 0px;
  color: #000;
}
.wrp-all-tab-applying-career-ul-arihant ul li a.active{
  background: #686868;
  color: #fff;
}
.wrp-main-wrp-all-sec-applying-use-box-career-ops{
  font-family: 'Fira Sans', sans-serif;
}

.recruitment-process-box-incareer-width-20{
  width:25%;
  float: left;
  padding-left: 15px;
  padding-right: 15px;
}

.image-wrp-applying-recruitment-section-career{
  width: 100%;
}
.image-wrp-applying-recruitment-section-career img{
  
}

.manage-detail-applying-recruitment-section-career{
  padding-top: 15px;
}
.manage-detail-applying-recruitment-section-career h4{
  font-size: 18px;
  color: #ed7215;
  font-weight: 500;
}
.manage-detail-applying-recruitment-section-career p{
  font-size: 14px;
  color: #686868;
  padding-right: 0px;
}



.manage-section-short-on-time{
  padding-bottom: 30px;
}
.manage-short-one-wrp-text-content-cv{
  text-align: center;
}
.manage-short-one-wrp-text-content-cv h3{
  font-size: 25px;
  font-family: 'Oswald', sans-serif;
}
.manage-short-one-wrp-text-content-cv a{
  font-family: 'Fira Sans', sans-serif;
  font-size: 16px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  padding: 6px 20px;
  color: #fff;
  margin-top: 15px;
  margin-bottom: 10px;  
  display: inline-block;
  border-radius: 20px;
}
.manage-short-one-wrp-text-content-cv p{
  font-family: 'Fira Sans', sans-serif;
  font-size: 14px;
  color: #686868;
}








.contact-us-arihant{
  width: 100%;
}
.image-in-top-manage-with-contact-us-arihant{

}
.image-in-top-manage-with-contact-us-arihant img{
  width: 100%;
}

.details-box-of-cntact-arihant-books{
  padding:30px 45px;
  margin-top:-85px;
  font-family: 'Fira Sans', sans-serif;
}
.row.manage-padding-in-this{
  margin:0px;
}
.con-box-wrp-this-manage-rows {
  border: 1px solid #d4d4d4;
  background: #fff;
  padding: 30px 45px;
  text-align: center;

  transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
}
.con-box-wrp-this-manage-rows:hover {
  box-shadow: 2px 4px 10px #00000057; 
}
.icon-wrp-contact-uss{

}
.icon-wrp-contact-uss img{

}
.contact-us-box-wrp-content-disc{

}
.contact-us-box-wrp-content-disc h3{
  font-size: 28px;
  position: relative;
  display: inline-block;
  padding: 15px 0px 10px 0px;
}
.contact-us-box-wrp-content-disc h3:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #000;
  left: 0px;
  bottom: 0px;
}

.contact-us-box-wrp-content-disc p{
  font-size: 16px;
  color: #686868;
  margin-top:10px;
}

.contact-all-details-bottom-box-content{
  min-height: 85px;
  padding-top: 20px;
}
.contact-all-details-bottom-box-content a{
  font-size: 22px;
  color: #000;
  font-weight: 500;
}

.contact-all-details-bottom-box-content h4{
  font-size: 22px;
  margin:0px;
  color: #000;
  font-weight: 500;
}
.contact-all-details-bottom-box-content h4:first-child{
  margin-bottom: 10px;
}



.contact-all-details-bottom-box-content a:hover,
.contact-all-details-bottom-box-content a:active,
.contact-all-details-bottom-box-content a:focus{
  text-decoration: none;
}












.container-fluid.manage-padding-b2b-arihany.mobile-views{
  display: none;
}
.all-address-wrp-contact-arihant.mobile-view{
  display: none;
}
.all-address-wrp-contact-arihant.desk-view{
  display: block;
}
.all-address-wrp-contact-arihant{
  padding-top:30px;
}
.all-address-wrp-contact-arihant .row{
  margin:0px;
}
.padding-right-none-desktop-view0conn-address{
  padding-right: 0px;
}
.box-map-wrpsthis{
  min-height: 500px;
  width: 100%;
  border-right: 0px;
  border:1px solid #ccc;
}
.padding-none-in-desktop-view-conn-address{
  padding-left: 0px;
  padding-right: 0px;
  border:1px solid #ccc;
  border-left: 0px;
}
.padding-right-none-desktop-view0conn-address .container{
  padding: 0px;
}

.reach-off-head-proper-desk-contact-arihant {
  float: left;
  width: 20%;
  text-align: center;
}
.reach-off-head-proper-desk-contact-arihant img{
  width:45px;
}
.reach-us-wrp-text-arihant-contact{
  float: left;
  padding: 0px 20px;
  text-align: center;
  width: 80%;
}
.border-wrp-text-reach-us-contact-arihant{
  border-bottom:1px solid #b9b9b9;
}
h2.reach-us-contact-text{
  display: inline-block;
  margin: 0px;
  line-height: 40px;
  font-size: 28px;
  font-weight: 600;
  position: relative;
  font-family: 'Oswald', sans-serif;
}

h2.reach-us-contact-text:after{
  position: absolute;
  content: "";
  bottom: 0px;
  left: 0px;
  width: 100%;
  height:1px;
  background: #000;
}


.user-icon-show-contact-fl-desk-top-arihant{
  padding-top: 20px;
}

.nav-office-addres-tabs-offices li{
  width: 100%;
}

.nav-office-addres-tabs-offices li a{
  width: 100%;
  background: #fff;
  padding-left: 25px;
  padding-top: 15px;
  padding-bottom: 15px;
  color: #000;
}
.nav-office-addres-tabs-offices li a.nav-link.active {
  background: #f5f5f5;
}
ul.nav.nav-office-addres-tabs-offices {
  margin-top: 25px;
  font-family: 'Fira Sans', sans-serif;
}
.nav-office-addres-tabs-offices li a h3{
  color: #000;    
  font-size: 22px;
  letter-spacing: 0.2px;
  margin-bottom: 5px;
}
.nav-office-addres-tabs-offices li a p{
  color: #686868;    
  margin-bottom: 0px;
  font-size: 14px;
  margin-top: 4px;
}
.nav-office-addres-tabs-offices li a span{
  color: #e87215;
}


.manage-row-in-details-address-office-arihants{
  padding-top: 20px;
  padding-left: 15px;
}

.manage-row-in-details-address-office-arihants .row{
  margin:0px;
}


.manage-wrp-all-users-sets-bottom-location-contacts{
  text-align: center;
  display: inline-block;
  margin: 0px auto;
}
.image-images-iconsoso {
  width: 40px;
  float: left;
}
.name-of-image-bottom-so-address-locations {
  float: left;
  margin-top: 10px;
  color: #686868;
}
.name-of-image-bottom-so-address-locations.track{
  width:100px;
}
.name-of-image-bottom-so-address-locations.manage{
  width:115px;
}
.name-of-image-bottom-so-address-locations.transactions{
  width:125px;
}
.name-of-image-bottom-so-address-locations.faq{
  width: 40px;
}


.wrapper-showss-contacts-details-list{
  padding:30px 0px 20px;
  font-family: 'Fira Sans', sans-serif;
}
.sales-and-support-offices-section{

}

.title-manage-sales-and-support {
  text-align: center;
  border-bottom: 1px solid #d6d6d6;
}
.title-manage-sales-and-support h2{
  position: relative;
  margin: 0px;
  font-weight: 500;
  font-size: 28px;
  color: #e87215;
  display: inline-block;
  padding-bottom: 20px;
}
.title-manage-sales-and-support h2:after{
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #000;
  bottom: 0px;
  left: 0px;
}


.row.manage-all-details-arihant-contacts-lists{
  margin: 0px;
  padding: 20px 10px 0px; 
}
.manage-all-number-email-arihant-books-contacts{
  font-family: 'Fira Sans', sans-serif;
  margin-bottom: 25px;
}
.manage-all-number-email-arihant-books-contacts h3{
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
}
.manage-all-number-email-arihant-books-contacts p{
  font-size: 12px;
  margin:0px;
  color: #686868;
  letter-spacing: -0.2px;
}
.bg-list-all-conatiner-use-contact-ss{
  /*background: url(../images/contact-bg-all-list-arihant.png);*/
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}







.b2b-service-wrapper-arihant{
  font-family: 'Fira Sans', sans-serif;
}
.reload-wrp-b2b-box-wrapper-arihant{

}
.reload-wrp-b2b-box-wrapper-arihant .row{
  margin:0px;
}
.title-b2b-service-arihant {
  text-align: center;
  border-bottom: 1px solid #d6d6d6;
}
.title-b2b-service-arihant h2{
  position: relative;
  margin: 0px;
  font-weight: 500;
  font-size: 28px;
  color: #e87215;
  display: inline-block;
  padding-bottom: 20px;
}
.title-b2b-service-arihant h2:after{
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #000;
  bottom: 0px;
  left: 0px;
}
.title-text-b2b-services-arihant-books{
  text-align: center;
  font-size: 14px;
  padding: 20px 30px;
}

.manage-padding-b2b-arihany{
  padding:0px;
}
.col-sm-6.col-12.center-all-and-pa-ri-o-arihant-b2b{
  padding:0px;
}
.center-all-and-pa-ri-o-arihant-b2b{
  display: flex;
  display: -ms-flexbox;
}
.arihant60-per-deta-b2b-services{
  width: 60%;
  float: left;
}
.arihant60-per-deta-b2b-services img{
  width: 100%;
}
.arihant40-per-deta-b2b-services {
  background: linear-gradient(0deg,#FF3448 0%,#FF9B26 100%);
  width: 40%;
  float: left;
}
.wrp-all-detail-b2b-box-arihant{
  text-align: center;
  padding: 30px 0px;
}
.wrp-all-detail-b2b-box-arihant img{
  
}
.text-manage-box-b2b-service-arihant-wrp{
  text-align: center;
}
.text-manage-box-b2b-service-arihant-wrp h3{
  font-family: 'Oswald', sans-serif;
  font-size: 26px;
  margin-bottom: 15px;
  color: #fff;
}
.text-manage-box-b2b-service-arihant-wrp a{
  color: #fff;
  font-size: 15px;
}
.text-manage-box-b2b-service-arihant-wrp a:hover,
.text-manage-box-b2b-service-arihant-wrp a:focus,
.text-manage-box-b2b-service-arihant-wrp a:active{
  text-decoration: none;
}
.manage-wrp-line-b2b-service-arihant-book {
  width: 60%;
  height: 2px;
  background: #fff;
  margin: 30px auto 0px;
}






/*========================================================================
                        Add New Style 
=========================================================================*/
a.menu-dots-img-wrp:hover,
a.menu-dots-img-wrp:focus,
a.menu-dots-img-wrp:active{
  text-decoration: none;
}
a.menu-dots-img-wrp img.menu-close-btn-menub {
    width: 24px;
    height: 24px;
}
a.menu-dots-img-wrp .menu-close-btn-menub{
  display: none;
}
.wrp-new-posi-changes-arihant.menu-actives .menu-close-btn-menub{
  display: block;
}
.wrp-new-posi-changes-arihant.menu-actives .menu-dots-img{
  display: none;
}
.wrp-new-posi-changes-arihant.menu-actives .main-menu-wrp-arihant-big{
  display: block;
}
.menu-overlay-big-menus.actv{
  position: fixed;
  height: 100vh;
  width: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 9;
  top: 0px;
  left: 0px;
}
.wrp-new-posi-changes-arihant{
  position: relative;
  z-index: 99999;
}
.main-menu-wrp-arihant-big.menu-showing {
  display: block;
}
.index-page .main-menu-wrp-arihant-big {
  position: absolute;
}
.main-menu-wrp-arihant-big {
  position: fixed;
  width: 95%;
  background: red;
  display: none;
  top: 70px;
  left: 0;
  right: 0;
  z-index: 1000;
  float: left;
  min-width: 1020px;
  margin: 0 auto 7px;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0,0,0,.15);
  -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
  box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.main-menu-wrp-arihant-big .row .col-3:first-child{
  background: #fafafa;
  padding: 0px;
}
.manage-logo-big-menu-arihant{
  text-align: center;
  padding:20px 0px;
}

.arihant-big-menu-side-wrp{
  padding: 20px 10px 20px 40px;
  background: #fff;
  height: auto;
  font-family: 'Fira Sans', sans-serif;
}
.manage-row-fluides-menu-big{
  margin-bottom: 15px;
}
ul.manage-with-all-links-big-menus.manage-wrp-secting-to-top {
    padding-top: 35px;
}
ul.manage-with-all-links-big-menus{
  padding:0;
  margin: 0;
}
ul.manage-with-all-links-big-menus h4{
  font-size: 16px;
  color: #000;
  position: relative;
  display: inline-block;;
  padding-bottom: 8px;
  margin-bottom: 12px;
}
.margin-top-30-big-menus-wrps{
  margin-top:20px;
}
ul.manage-with-all-links-big-menus.manage-top-manages-big-menusuls {
    margin-top: 40px;
}
ul.manage-with-all-links-big-menus h4:after{
  position: absolute;
  content: '';
  left: 0px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  width: 100%;
  height: 1.5px;
  bottom: 0px;
}
ul.manage-with-all-links-big-menus li{
  list-style: none;
  line-height: 1.2;
  padding-bottom: 8px;
}
ul.manage-with-all-links-big-menus li a{
  text-decoration: none;
  color: #000;
  font-size: 13px;
}
ul.manage-with-all-links-big-menus li a:hover,
ul.manage-with-all-links-big-menus li a:active,
ul.manage-with-all-links-big-menus li a:focus{
  text-decoration: underline;
}

.bg-image-use-some-menus-big-menu{
  /*background: url(../images/about-big-menus-bg-menu.png);*/
  height: auto;
  background-size: cover;
  background-repeat: no-repeat;
  padding-bottom:0px !important ;
}
ul.this-is-side-wrp-ul-big-menu-arihant {
  width: 100%;
  margin: 0px;
  padding: 0px;
  font-family: 'Fira Sans', sans-serif;
  padding-bottom: 14px;
  border-bottom: 1px solid #ccc;
  margin-bottom: 14px;
}
ul.this-is-side-wrp-ul-big-menu-arihant li{
  list-style: none;
}
ul.this-is-side-wrp-ul-big-menu-arihant li a {
    padding: 7px 0px 7px 40px;
    display: block;
    color: #000000;
}

ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss a{
  color: #fff;
}

ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss a:hover,
ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss a:focus,
ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss a:active,
ul.this-is-side-wrp-ul-big-menu-arihant li a:hover,
ul.this-is-side-wrp-ul-big-menu-arihant li a:focus,
ul.this-is-side-wrp-ul-big-menu-arihant li a:active{
  text-decoration: none;
}
ul.this-is-side-wrp-ul-big-menu-arihant li:hover{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
ul.this-is-side-wrp-ul-big-menu-arihant li:hover a{
  color: #fff;
}

ul.menu-socia-icon-big-menu-wrps-arihantooks {
    padding: 0px;
    margin: 0px;
    width: 60%;
    margin: 0px auto;
    padding-bottom: 10px;
}
ul.menu-socia-icon-big-menu-wrps-arihantooks li{
  list-style: none;
  float:left;
  width: 25%;
}

ul.menu-socia-icon-big-menu-wrps-arihantooks li a{
  display: block;
  text-align: center;
  padding:4px 0px;
  color: #000;
}
ul.menu-socia-icon-big-menu-wrps-arihantooks li a i{
  font-size: 14px;
}
ul.menu-socia-icon-big-menu-wrps-arihantooks li a:hover{
  color: #ea1d32;
  text-decoration: none;
}

/*=========================================================================
                                About Us
==========================================================================*/
.top-upper-banner-bg-with-content {
  /*background: url(../images/about-us-bg-banner.jpg);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: bottom;
  min-height: 440px;
  font-family: 'Fira Sans', sans-serif;
}
.banner-upper-content-manages-widhs{
  padding-top:40px;
}
.banner-upper-content-manages-widhs h2{
  color:#e97215;
  font-weight: 600;
  font-size:55px;
}
.banner-upper-content-manages-widhs p{
  font-size:20px;
  color:#000000;
}




.about-us-story-category-section {
  /*background: url(../images/about-us-story-bg.jpg);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: top;
  min-height: 440px;
  font-family: 'Fira Sans', sans-serif;
  padding: 40px 60px;
}

.main-content-story-wrp-section {
  padding-right: 45px;
}
.main-content-story-wrp-section h2{
  font-size: 38px;
  font-weight: 600;
  padding-bottom: 10px;
}
.main-content-story-wrp-section p{
  font-size: 18px;
}

.side-main-wrapper-section-content {
    padding-top: 55px;
}

.manage-wrp-secto-pro-story-parts h3{
  margin-bottom: 10px;
  font-size: 23px;
  font-weight: 500;
  padding-bottom: 10px;
  position: relative;
}
.manage-wrp-secto-pro-story-parts p{
  font-size:16px;
}
.manage-wrp-secto-pro-story-parts h3:after {
  content: "";
  position: absolute;
  bottom: 0px;
  left: 0px;
  height: 2px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  width: 85px;
}
.working-section-wrp-with-mr{
  position: relative;
}
.upper-bg-layer-arihantbook{
  /*background: url(../images/working-bg-about-us-pg.jpg);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: top;
  min-height: 300px;
  font-family: 'Fira Sans', sans-serif;
}
.upper-bg-layer-arihantbook h3{
  text-align: center;
  font-size: 36px;
  color: #fff;
  padding: 100px 0px 0px 0px;
}


.four-part-section-working-sec{
  position: absolute;
  width: 100%;
  left: 0px;
  bottom: -120px;
}
.part-section-box-one-on-one-arihant-about{
  padding: 55px 0px;
  box-shadow: 0px 0px 4px 0px #ccc;
  background: #fff;
  text-align: center;
  border-radius: 15px;
  cursor: pointer;
  transition: 0.3s all ease-in-out;
}
.part-section-box-one-on-one-arihant-about h3{
  transition: 0.3s all ease-in-out;
  font-size: 42px;
  font-family: 'Oswald', sans-serif;
}
.part-section-box-one-on-one-arihant-about span{
  transition: 0.3s all ease-in-out;
  font-size: 18px;
  color: #686868;
  font-family: 'Fira Sans', sans-serif;
}

.part-section-box-one-on-one-arihant-about:hover{
  background: #ef7215;
}
.part-section-box-one-on-one-arihant-about:hover h3{
  color: #fff;
}
.part-section-box-one-on-one-arihant-about:hover span{
  color: #fff;
}


.timeline-inter-sec-wrp-sep{
  background: #fafafa;
  padding-top: 150px;
  text-align: center;
  padding-bottom: 40px;
}

.timeline-inter-sec-wrp-sep h4{
  font-size: 18px;
  line-height: 30px;
}
.timeline-inter-sec-wrp-sep a {
  font-size: 16px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  color: #fff;
  padding: 4px 20px;
  display: inline-block;
  font-weight: 600;
  margin-top: 25px;
}
.timeline-inter-sec-wrp-sep a:hover,
.timeline-inter-sec-wrp-sep a:active,
.timeline-inter-sec-wrp-sep a:focus{
  text-decoration: none;
}




.wrp-sect-port-asper-mantence {
  text-align: center;
  font-family: 'Fira Sans', sans-serif;
  padding:60px 0px 0px 0px;
}
.wrp-sect-port-asper-mantence h3{
  font-size: 34px;
  font-weight: 600;
}
.mentance-wrp-award-honors{
  padding:20px 0px 60px 0px;
}
.wrp-sect-port-asper-mantence p {
  color: #686868;
  font-size: 16px;
  line-height: 26px;
  margin-top: 22px;
}

#slider-award-wala .carousel-indicators li{
  width: 14px !important;
  height: 14px !important;
  border-radius: 50% !important;
  cursor: pointer;
  background: #979a9b;
}
#slider-award-wala .carousel-indicators li.active{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}

.posi-bottom-relative-walas{
  bottom:-50px;
}

.manage-with-listing-rowest {
  margin: 0px;
  padding: 0px;
  padding-left: 25px;
  padding-top: 30px;
}
.manage-with-listing-rowest li{
  list-style: none;
  position: relative;
  padding: 20px 0px;
  border-top: 1px solid #ccc;
  font-family: 'Fira Sans', sans-serif;
}
.manage-with-listing-rowest li:after{
  width: 14px !important;
  height: 14px !important;
  border-radius: 50% !important;
  cursor: pointer;
  background: #979a9b;  
  position: absolute;
  left: -25px;
  top:25px;
  content: '';
}
.manage-with-listing-rowest li.active:after{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
.manage-with-listing-rowest li span{
  font-size: 16px;
  color: #686868;
}
.manage-with-listing-rowest li span b{
  display: inline-block;
  padding-right: 20px;
}
.part-sec-post-belief-ours {
    text-align: center;
    padding-top:40px;
    padding-bottom: 50px;
}
.part-sec-post-belief-ours h3 {
  font-size: 22px;
  font-family: 'Playfair Display', serif;
  font-weight: 500;
  color: #686868;
  display: inline-block;
  border-bottom: 1px solid #ef7215;
  margin-bottom: 20px;
  padding-bottom: 5px;
}
.part-sec-post-belief-ours p {
  font-size: 24px;
  font-family: 'Playfair Display', serif;
  font-style: italic;
  color: #000;
  font-weight: 500;
}
.part-sec-post-belief-ours span {
  font-family: 'Fira Sans', sans-serif;
  font-weight: 500;
  color: #686868;
}



/*==============================
        FAQs Page Css        
=================================*/

.top-banner-part-imge-faqs-arihant-books-wrp{
  /*background: url(../images/faq-bg-banner.jpg);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: top;
  min-height: 440px;
  font-family: 'Fira Sans', sans-serif;
}
.manage-content-wrp-as-faqs-arihant {
    padding-top: 160px;
}
.manage-content-wrp-as-faqs-arihant h2 {
  font-size: 54px;
  color: #fff;
  text-shadow: 2px 2px 4px #383737;
}
.banner-faqs-details-on-pages-arihant{
  padding:10px 0px 180px 0px;
}
.manage-content-wrp-as-faqs-arihant h4 {
  color: #ffbc50;
  font-weight: 400;
  font-size: 26px;
}
.wrp-faqs-asport-kolas.col-12 {
  padding: 20px 0px;
}
ul.nav.nav-tabs.row.justify-content-center{

}
ul.nav.nav-tabs.row.justify-content-center li.nav-item.col-6.col-sm-3{
  text-align: center;
}
ul.nav.nav-tabs.row.justify-content-center li.nav-item.col-6.col-sm-3 a{
  border: none;
  background: transparent;
  display: inline-block;
  padding: 16px 12px;
  font-size: 20px;
  color: #686868;
  text-align: center;
  position: relative;
}
ul.nav.nav-tabs.row.justify-content-center li.nav-item.col-6.col-sm-3 a.nav-link.active{
  font-weight: 600;
  color: #000;
}
ul.nav.nav-tabs.row.justify-content-center li.nav-item.col-6.col-sm-3 a.nav-link.active:after{
  position: absolute;
  content: "";
  width: 100%;
  bottom: 0px;
  left: 0;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  height: 2px;
}
.wrp-row-under-all-arihant-faqs {
  padding: 20px 0px 20px 15px;
  border-bottom: 1px solid #ccc;
}
.tis-is-a-box-show-arihant-ar{
  position: relative;
}
a.link-clicks-arihantbooks-linkss {
  position: absolute;
  right: 15px;
  top: 10px;
}
.close-icons-bottoms-ari img{
  width: 16px;
}
a.this-is-a-link-arihantbook-faqs{
  font-size: 18px;
  font-weight: 500;
  color: #000;
}
a.link-clicks-arihantbooks-linkss.collapsed .close-btn-faqs-arihants{
  display: none;
}

a.link-clicks-arihantbooks-linkss.collapsed .close-bas-btn-faqs-arihants{
  display: block;
  padding:2px 2px;
}

a.link-clicks-arihantbooks-linkss .close-bas-btn-faqs-arihants{
  display: none;
}
img.close-btn-faqs-arihants {
    width: 18px;
    padding: 0px 4px;
}
.this-is-a-wrp-content-of-faqs {
  width: 90%;
  padding-top:15px;
  padding-bottom: 20px;
}
.this-is-a-wrp-content-of-faqs p{
  margin:0px;
  font-size: 16px;
  font-weight: 500;
  color: #686868;
}

a.link-clicks-arihantbooks-linkss:hover,
a.this-is-a-link-arihantbook-faqs:hover{
  text-decoration: none;
}



/*================================================================================================
                                    Privacy Policy
=================================================================================================*/
.privacy-policy-arihantbooks{

}

.privacy-policy-wrp-sec{
  /*background: url(../images/privacy-details-policy.jpg);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: top;
  min-height: 440px;
  font-family: 'Fira Sans', sans-serif;    
  list-style: none;
  display: flex;
  align-items: center;
}
.wrp-arihantbook-privacy-policys{

}
.wrp-arihantbook-privacy-policys h3{
  font-size: 54px;
  color: #fff;
  text-shadow: 2px 2px 4px #383737;
}
.wrp-arihantbook-privacy-policys a.btn-privacys-policy-asper{
  font-size: 14px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  color: #fff;
  font-weight: 300;
  padding: 6px 26px;
  display: inline-block;
  border-radius: 20px;
}
a.btn-privacys-policy-asper:hover{
  text-decoration: none;
}

.wrp-privacys-sec-ports{
  width:100%;
}
.wrp-container-checker-privacy-policy-remote {
  padding: 60px 0px 80px 0px;
  font-family: 'Fira Sans', sans-serif;
}
.row-privacy-policy-text-contentsa{
  margin-bottom: 40px;
}
.row-privacy-policy-text-contentsa h4{
  font-size: 14px;
  font-weight: 500;
  color: #000;
}
.row-privacy-policy-text-contentsa p{
  font-size: 14px;
  color: #697071;
  font-weight: 400;
  line-height: 24px;
}



/*===========================================================
                      Supply Page
=============================================================*/
.suppluy-bboksss-arihantbooks{

}
.supply-books-details-wrp-sec{
  /*background: url(../images/suply-page-bg.jpg);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: top;
  min-height: 360px;
  font-family: 'Fira Sans', sans-serif;    
  list-style: none;
  display: flex;
  align-items: center;
}
.wrp-arihantbook-supply-details-bbokss{

}
.wrp-arihantbook-supply-details-bbokss h3{
  font-size: 54px;
  color: #fff;
  text-shadow: 2px 2px 4px #383737;
}
.wrp-arihantbook-supply-details-bbokss a.btn-supply-details-offerss-asper{
  font-size: 14px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  color: #fff;
  font-weight: 300;
  padding: 6px 26px;
  display: inline-block;
  border-radius: 20px;
}
a.btn-supply-details-offerss-asper:hover{
  text-decoration: none;
}

.wrp-supply-bookss-sec-ports{
  width:100%;
}
.wrp-container-checker-supply-chains-remote {
  padding: 60px 0px 80px 0px;
  font-family: 'Fira Sans', sans-serif;
}
.row-supply-detailss-text-contentsa{
  margin-bottom: 40px;
}
.row-supply-detailss-text-contentsa h4{
  font-size: 14px;
  font-weight: 500;
  color: #000;
}
.row-supply-detailss-text-contentsa p{
  font-size: 14px;
  color: #697071;
  font-weight: 400;
  line-height: 24px;
}

/*===========================================================
                      Term And Condition
=============================================================*/
.term-and-contions-bboksss-arihantbooks{

}
.term-and-contions-books-details-wrp-sec{
  /*background: url(../images/suply-page-bg.jpg);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: top;
  min-height: 440px;
  font-family: 'Fira Sans', sans-serif;    
  list-style: none;
  display: flex;
  align-items: center;
}

.wrp-term-and-contions-bookss-sec-ports{
  width:100%;
}
.wrp-arihantbook-term-and-contions-details-bbokss h3{
  font-size: 54px;
  color: #fff;
  text-shadow: 2px 2px 4px #383737;
}
.wrp-arihantbook-term-and-contions-details-bbokss a.btn-term-and-conditions-details-offerss-asper{
  font-size: 14px;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  color: #fff;
  font-weight: 300;
  padding: 6px 26px;
  display: inline-block;
  border-radius: 20px;
}
a.btn-term-and-conditions-details-offerss-asper:hover{
  text-decoration: none;
}

.wrp-container-checker-term-and-conditions-chains-remote {
  padding: 60px 0px 80px 0px;
  font-family: 'Fira Sans', sans-serif;
}


/*====================================
                  Cateloge
======================================*/
.cateloge-wrps-arihantbooks{

}
.cateloge-wrps-wrp-sec{
  /*background: url(../images/catalogue-us-bg-banner.png);*/
  background-size: cover;
  height: auto;
  background-repeat: no-repeat;
  background-position: top;
  min-height: 440px;
  font-family: 'Fira Sans', sans-serif;    
  list-style: none;
  display: flex;
  align-items: center;
}
.wrp-cateloge-wrps-sec-ports{
  width: 100%;
}
.wrp-arihantbook-cateloge-wrps h3{
  font-size: 54px;
  color: #fff;
  text-shadow: 2px 2px 4px #383737;
}



.catalogue-download-wrp-checks{
  padding:30px 0px 30px 0px;
  font-family: 'Fira Sans', sans-serif;
}
.pera-disc-ground{
  padding-bottom: 60px;
}
.pera-disc-ground p{
  color: #686868;
  font-size: 18px;
  text-align: center;
}
.download-wrp-btn-with-images {
  text-align: center;
}
.row.manages-margin-second-catalogues-showsa{

}
.row.manages-margin-second-catalogues-showsa .border-parents-with-nth-child-use-arihant:nth-child(1){
  border-right: 1px solid #cccccc47;
  border-bottom: 1px solid #cccccc47;
}
.row.manages-margin-second-catalogues-showsa .border-parents-with-nth-child-use-arihant:nth-child(2){
  border-bottom: 1px solid #cccccc47;
}
.row.manages-margin-second-catalogues-showsa .border-parents-with-nth-child-use-arihant:nth-child(3){
  border-right: 1px solid #cccccc47;
}

.row.manages-margin-second-catalogues-showsa .border-parents-with-nth-child-use-arihant:nth-child(5),
.row.manages-margin-second-catalogues-showsa .border-parents-with-nth-child-use-arihant:nth-child(7){
  border-right: 1px solid #cccccc47;
  border-top: 1px solid #cccccc47;
}
.row.manages-margin-second-catalogues-showsa .border-parents-with-nth-child-use-arihant:nth-child(6),
.row.manages-margin-second-catalogues-showsa .border-parents-with-nth-child-use-arihant:nth-child(8){
  border-top: 1px solid #cccccc47;
}


.manage-box-wrp-catalogue-arihant-files {
  padding: 30px 30px 40px;
  text-align: center;
}
.download-wrp-btn-with-images a {
  display: inline-block;
  padding: 10px 20px;
}

.content-wrp-catelogue-download-page{

}
.content-wrp-catelogue-download-page h3{
  font-size: 28px;
  color: #000000;
  font-weight: 600;
}
.content-wrp-catelogue-download-page p{
  font-family: 'Fira Sans', sans-serif;
  font-size: 16px;
  color: #686868;
}

.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier span img {
  filter: invert(63%) sepia(68%) saturate(5309%) hue-rotate(357deg) brightness(102%) contrast(87%);
}

.logo-white-small-wrp {
  display: inline-block;
  float: left;
}

a.white-logo-anchor-white {
  padding: 3px 0px;
  display: inline-block;
}

.main-menu-wrp-arihant-big .arihant-big-menu-side-wrp .book-wrapper-sec-part-main-menu-big {
  overflow-y: scroll;
  max-height: 80vh;
  overflow-x: hidden;
}
@media(max-width:768px){
  .menu-main-min-height{
    min-height: 70px;
  }
}
