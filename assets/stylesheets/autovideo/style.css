/*!
 * Bootstrap Reboot v4.1.2 (https://getbootstrap.com/)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)
 */
*,
*::before,
*::after {
  box-sizing: border-box;
}
html {
  font-family: 'Rubik', sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
p,span,h1,h2,h3,h4,a{
  position: relative;
  z-index: 99 !important;
  font-family: 'Rubik', sans-serif;
}
p{
  position: relative;
  z-index: 999;
}
body{
  background: #ffffff !important;
  position: relative;
  height: 100vh;
  overflow-y: hidden;
}
@-ms-viewport {
  width: device-width;
}
article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
  display: block;
}
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #ffffff;

}
[tabindex="-1"]:focus {
  outline: 0 !important;
}
hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}
p {
  margin-top: 0;
  margin-bottom: 1rem;
}
abbr[title],
abbr[data-original-title] {
  text-decoration: underline;
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
}
address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}
dt {
  font-weight: 700;
}
dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
blockquote {
  margin: 0 0 1rem;
}
dfn {
  font-style: italic;
}
b,
strong {
  font-weight: bolder;
}
small {
  font-size: 80%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}
a:hover {
  color: #0056b3;
  text-decoration: underline;
}
a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}
a:not([href]):not([tabindex]):focus {
  outline: 0;
}
pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}
pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}
figure {
  margin: 0 0 1rem;
}
img {
  vertical-align: middle;
  border-style: none;
}
svg:not(:root) {
  overflow: hidden;
  vertical-align: middle;
}
table {
  border-collapse: collapse;
}
caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}
th {
  text-align: inherit;
}
label {
  display: inline-block;
  margin-bottom: 0.5rem;
}
button {
  border-radius: 0;
}
button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}
input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html [type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox;
}
textarea {
  overflow: auto;
  resize: vertical;
}
fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}
progress {
  vertical-align: baseline;
}
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}
[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
output {
  display: inline-block;
}
summary {
  display: list-item;
  cursor: pointer;
}
template {
  display: none;
}
[hidden] {
  display: none !important;
}
@font-face {
  font-family: 'Merge Light W05 Regular';
  src: url("../Fonts/5172677/f49518c8-21ec-40cf-9b19-c931805f1975.eot?#iefix");
  src: url("../Fonts/5172677/f49518c8-21ec-40cf-9b19-c931805f1975.eot?#iefix") format("eot"), url("../Fonts/5172677/4660b661-e52c-4125-93ac-4eccc802c566.woff2") format("woff2"), url("../Fonts/5172677/dd6ea7a3-de86-4fa8-83f6-80a5305ca6c1.woff") format("woff"), url("../Fonts/5172677/5e88e2b4-82e6-4389-abec-d0a267c7ecc4.ttf") format("truetype");
}
html,
p,
h1,
h2,
h3,
h4,
a {
  font-family: 'Rubik', sans-serif;
}
.ws-title {
  height: 80vh;
}
.ws-title img {
  margin: 0 auto;
  position: relative;
  z-index: 99;
}
.ws-title p {
  text-align: center;
  font-size: 28px;
  text-transform: uppercase;
  color: #ffffff;
  font-weight: 400;
}
.ws-title blockquote p {
  width: 600px;
  margin: 0 auto;
  color: #ffffff;
  font-weight: 500;
}
.ws-title blockquote footer {
  text-align: right;
  font-size: 28px;
  color: #ffffff;
  text-transform: uppercase;
}
.ws-link {
  height: 20vh;
  padding: 0.5rem;
  align-items: center;
  margin-right: 1rem;
}
.ws-link a {
  color: #F79420;
  font-size: 28px;
  text-transform: uppercase;
  font-weight: 500;
}
.ws-link img {
  width: 290px;
  /*height: 76px;*/
}

.start .img-wrapper img {
  position: absolute;
  right: 2rem;
  bottom:0;
}
.img-quiz {
  width: 235px;
  height: 240px;
}
.ws-quiz {
  height: 100vh;
}
.ws-quiz .content-wrapper h3 {
  text-transform: uppercase;
  color: #ffffff;
  font-size: 28px;
  font-weight: 500;
}
.ws-quiz .content-wrapper p {
  color:#ffffff;
  text-transform: uppercase;
  font-size: 28px;
  font-weight: 500;
  text-align: center;
  padding-bottom: 0.5rem;
}
.ws-quiz .content-wrapper p span {
  border-bottom: 2px solid #F79420;
  display: inline-block;
  position: relative;
  z-index: 99;
}
.ws-quiz .img-wrapper p {
  color: #F79420;
  font-weight: 500;
  text-transform: uppercase;
  margin: 1.5rem auto;
  margin-top: 3rem;
  font-size: 28px;
  text-align: center;
}
.ws-quiz .img-wrapper h3 {
  color: #F79420;
  font-size: 28px;
  font-weight: 500;
}
.img-que {
  position: absolute;
  right: 2rem;
  bottom: 0rem;
  width: 235px;
  height: 240px;
}
.img-answer {
  position: absolute;
  right: 2rem;
  top: 2rem;
  width: 235px;
  height: 240px;
}
.que-wrapper {
  margin-top: 4rem;
}
.que-wrapper h4 {
  text-transform: uppercase;
  font-size: 28px;
  font-weight: 500;
  color: #F79420;
  text-align: center;
  position: relative;
  z-index: 99;
}
.que-wrapper p {
  color: #ffffff;
  font-size: 32px;
  /*text-transform: capitalize;*/
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
}
.que-wrapper span {
  color: #ffffff;
  font-size: 28px;
  font-family: 'Rubik', sans-serif;
  font-weight: 500;
  margin-right: 1rem;
  position: relative;
  z-index: 99;
}
.que-wrapper .exp {
  color: #F79420;
  text-transform: uppercase;
  font-weight: 500;
  font-size: 14px;
}
#overlay, #overlaydark {
  position: fixed; /* Sit on top of the page content */
  /* Hidden by default */
  width: 100%; /* Full width (cover the whole page) */
  height: 100%; /* Full height (cover the whole page) */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 22; /* Specify a stack order in case you're using a different order for other elements */
  cursor: pointer; /* Add a pointer on hover */
}
#overlay {
  background-color: rgba(0,0,0); /* Black background with opacity */
}
#overlaydark {
  background-color: rgba(0,0,0,1); /* Black background */
}
.que-wrapper .exp-answer {
  border: 1px solid #F79420;
  border-radius: 6px;
  padding: 1.5rem;
  color: #ffffff;
  font-size: 26px;
  font-family: 'Merriweather', serif;
  font-weight: normal;
  width: 620px;
}
.exp-answer img{
  max-width:550px;
  max-height:450px;
}
.que-wrapper .exp-answer p{
  font-size: 26px;
  text-transform: none;
}
.answers input[type=radio] {
  display: none;
}
.answers label {
  border: 1px solid #F79420;
  margin: 1rem;
  padding: 1rem;
  border-radius: 8px;
  position: relative;
}
.answers label.checked {
  border: 1px solid #46B520;
}
.answers label.checked p:first-child {
  color: #46B520;
}
.answers p {
  font-size: 28px;
  text-transform: none;
  font-weight: normal;
  font-family: 'Merriweather', serif;
  color: #ffffff;
  /*width: 350px;*/
  position: relative;
  z-index:99;
}
.answers p:first-child {
  width: 50px;
  color: #F79420;
  font-size: 28px;
}
.answers p.optionname{
  font-size: 28px;
  color:#ffffff;
  position: absolute !important;
  left: 0;
}
.answers img{
  max-width:280px;
  max-height: 80px;
}
.question img{
  max-width: 600px;
  max-height: 100px;
}
.button-wrapper {
  display: inline-block;
  position: absolute;
  text-align: center;
  margin: 0 auto;
  width: 100%;
  position: relative;
  z-index: 99;
}
.button-wrapper button {
  color: #ffffff;
  border: none;
  outline: 0;
  background: #F79420;
  padding: 0.5rem 1rem;
  margin-right: 10px;
}
.comma {
  color: rgba(68, 68, 68, 0.5);
  font-size: 40px;
}
.button-wrapper {
  text-align: center;
  width: 100%;
}
#play {
  position: absolute;
  bottom: 2rem;
  color: #ffffff;
  border: none;
  outline: 0;
  background: #F79420;
  padding: 0.5rem 1rem;
}
.option-wrapper {
  border: 1px solid #F79420;
  margin: 1rem;
  padding: 1rem;
  border-radius: 8px;
  position: relative;
  min-height: 70px;
  align-items: center;
  position: relative;
  z-index: 999;
  justify-content: center;
}
.option-wrapper.checked {
  border: 1px solid #46B520;
}
.option-wrapper.checked p:first-child {
  color: #ffffff;
}

.option-wrapper.checked .done {
  display: block;
}
.option-wrapper p{
  margin:0;
  padding:0;
  display: flex;
  align-items: center;
  justify-content: center;
  /*margin-left: -14px;*/
}

.done {
  position: absolute;
  right: 2rem;
  bottom: 0;
  width: 50px;
  height: 50px;
  display: none;
  z-index: 99;
}
button[data-setter] {
  outline: none;
  background: transparent;
  border: none;
  font-family: 'Roboto';
  font-weight: 300;
  font-size: 18px;
  width: 25px;
  height: 30px;
  color: #F7958E;
  cursor: pointer;
}
button[data-setter]:hover {
  opacity: 0.5;
}
.timer-wrapper {
  position: relative;
  width: 56px;
  margin: 0 auto;
  text-align: center;
}
.minutes-set {
  float: left;
  margin-right: 28px;
}
.seconds-set {
  float: right;
}
.controlls {
  position: absolute;
  text-align: center;
}
.display-remain-time {
  font-weight: 100;
  font-size: 16px;
  color: #F7958E;
}
.time-remain {
  position: absolute;
  top: 32%;
  left: 1.3rem;
}
.time-remain span {
  color: #F7958E;
  font-weight: 100;
  font-size: 16px;
  display: block;
  margin-left: 2px;
}
#pause {
  outline: none;
  background: transparent;
  border: none;
  margin-top: 10px;
  width: 50px;
  height: 50px;
  position: relative;
}
.play::before {
  display: block;
  content: "";
  position: absolute;
  top: 8px;
  left: 16px;
  border-top: 15px solid transparent;
  border-bottom: 15px solid transparent;
  border-left: 22px solid #F7958E;
}
.pause::after {
  content: "";
  position: absolute;
  top: 8px;
  left: 12px;
  width: 15px;
  height: 30px;
  background-color: transparent;
  border-radius: 1px;
  border: 5px solid #F7958E;
  border-top: none;
  border-bottom: none;
}
#pause:hover {
  opacity: 0.8;
}
.e-c-base {
  fill: none;
  stroke: #B6B6B6;
  stroke-width: 12px;
}
.circle svg {
  position: relative;
}
.e-c-progress {
  fill: none;
  stroke: #F7958E;
  stroke-width: 12px;
  transition: stroke-dashoffset 0.7s;
}
.e-c-pointer {
  fill: #FFF;
  stroke: #F7958E;
  stroke-width: 4px;
}
#e-pointer {
  transition: transform 0.7s;
}
.form-wrapper {
  text-align: center;
  position: relative;
  z-index: 99;
}
.form-wrapper textarea {
  border: 1px solid #F79420;
  padding: 10px;
  border-radius: 4px;
  width: 400px;
  margin-top: 1rem;
}
.form-wrapper input {
  width: 400px;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #F79420;
  margin-top: 1rem;
}
.form-wrapper select {
  width: 400px;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #F79420;
  margin-top: 1rem;
}
.answers,
.explanation {
  display: flex;
}
.explanation{
  /*width: 700px !important;*/
}
.explanation p{
  font-weight: normal;
}
.exp-answer p{
  font-weight: normal;
  word-spacing: 5px;
}
.col-6 > div {
  min-width: 400px;
  margin: 0 auto;
}
.answers p p {
  width: auto !important;
  margin: 0;
}
#correctAnswerMoved {
  min-width: 400px;
  margin: 0 auto;
}
#correctAnswerMoved > div {
  min-width: 400px;
}
#correctAnswerMoved img{
  max-width: 250px;
  max-height: 80px;
}
#correctAnswerMoved > div.checked p:first-child{
  padding: 10px;
  width: 15%;
  left:0;
  position: absolute !important;
}
#correctAnswerMoved > div.checked p{
  width: 80%;
}

.explanation > div {
  /*width: 700px;*/
}
.thanks blockquote a {
  color: #F79420;
  font-size: 28px;
  text-transform: uppercase;
  margin-top: 1rem;
  display: block;
  font-weight: 500;
}
.thanks blockquote footer {
  text-align: center !important;
  margin-top: 1rem;
  font-size: 18px;
}
.thanks .img-wrapper {
  text-align: center;
}
.thanks .img-wrapper img {
  width: 110px;
  height: 110px;
  margin-bottom: 2rem;
}

#opt1para,#opt2para,#opt3para,#opt4para,#opt5class{
  color:#ffffff;
  width: 90%;
}
#opt1para p,#opt2para p,#opt3para p,#opt4para p,#opt5class p{
  color:#ffffff;
}
.option-wrapper.checked #opt1para p {
  color: #ffffff;
}
.option-wrapper.checked #opt2para p {
  color:#ffffff;
}
.option-wrapper.checked #opt3para p {
  color: #ffffff;
}
.option-wrapper.checked #opt4para p {
  color:#ffffff;
}
.option-wrapper.checked #opt5class p {
  color:#ffffff;
}
@media (min-width: 1200px) {
  /*.container {*/
  /*max-width: 1400px;*/
  /*}*/
}

.timer-container{
  position: relative;
  z-index: 99;
}
.timeup img{
  position: relative;
  z-index: 99;
}
.question{
  display: flex;
}
/*.question1 {
  justify-content: space-between;
  flex-wrap: wrap;
}
.question1 img {
  max-width: initial;
  max-height: initial;
  width: 100%;
  height: auto;
  -webkit-animation: zoominoutsinglefeatured;
  animation: zoominoutsinglefeatured;
  animation-duration: 3s;
  -webkit-animation-duration: 3s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  transform-origin: center;
  -webkit-transform-origin: center;
  -webkit-transform: scale(0);
  transform: scale(0);
}*/
@-webkit-keyframes zoominoutsinglefeatured {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
@keyframes zoominoutsinglefeatured {
  from {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
.question1 p+p {
  width: 50%;
  text-align: left;
  border-left: 1px solid #555;
}
.question1 p {
  padding: 0 30px;
  width: 100%;
}
.question1 p:first-child span {
  font-size: 36px;
}
.question1 p:first-child {
  width: 100%;
  text-align: center;
  border-bottom: 1px solid #555;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  font-size: 36px;
}
.question1 p:not(:first-child) {
  flex: 1;
  font-size: 30px;
  opacity: 0;
  visibility: hidden;
  -webkit-animation: smoothdisplay 1s;
  animation: smoothdisplay 1s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -moz-animation-delay: 1.5s;
  -webkit-animation-delay: 1.5s;
  -o-animation-delay: 1.5s;
  animation-delay: 1.5s;
}
/*.question1 p:nth-child(3) {
  opacity: 0;
  visibility: hidden;
  -webkit-animation: smoothdisplay 1s;
  animation: smoothdisplay 1s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -moz-animation-delay: 1.5s;
  -webkit-animation-delay: 1.5s;
  -o-animation-delay: 1.5s;
  animation-delay: 1.5s;
}*/
.question1 p:nth-child(2) {
  opacity: 1;
  visibility: visible;
  /*animation: none;
  animation-fill-mode: none;
  animation-delay: unset*/
}
.question1 ul, .question1 ol {
  flex: 1;
  font-size: 30px;
  opacity: 0;
  visibility: hidden;
  -webkit-animation: smoothdisplay 1s;
  animation: smoothdisplay 1s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -moz-animation-delay: 1.5s;
  -webkit-animation-delay: 1.5s;
  -o-animation-delay: 1.5s;
  animation-delay: 1.5s;
}
@-webkit-keyframes smoothdisplay {
  100% {
    opacity: 1;
    visibility: visible;
  }
}
@keyframes smoothdisplay {
  100% {
    opacity: 1;
    visibility: visible;
  }
}
.question1 p span, .question1 ul li span, .question1 ol li span {
  font-size: 30px;
}
.question1 ul li, .question1 ol li {
  margin-bottom: 0.5rem;
}


/***
====================================
*** Question New Template Design ***
====================================
***/

.question_new{
  text-align: left;
}
.question_new div {
  padding: 0 15px;
}
.question_new div#slide_header {
  opacity: 0;
  visibility: hidden;
  -webkit-animation: smoothdisplay 1s;
  animation: smoothdisplay 1s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -moz-animation-delay: 0s;
  -webkit-animation-delay: 0s;
  -o-animation-delay: 0s;
  animation-delay: 0s;
}
.question_new div#slide_points span {
  display: block;
  margin-right:0;
  margin-bottom: 1.5rem;
  font-size: 28px;
  opacity: 0;
  visibility: hidden;
  -webkit-animation: smoothdisplay 1s;
  animation: smoothdisplay 1s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -moz-animation-delay: 0s;
  -webkit-animation-delay: 0s;
  -o-animation-delay: 0s;
  animation-delay: 0s;
}
.question_new div#slide_points span span{
  display: inline;
  opacity: 1;
  visibility: visible;
  margin-bottom: inherit;
}
.question_new div#slide_points span p {
  font-size: 28px;
  font-weight: normal;
}
.question_new div:first-child {
  width: 100%;
  text-align: center;
  border-bottom: 1px solid #555;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  text-transform: uppercase;
  font-size: 32px;
  font-weight: 500;
  color: #F79420;
  position: relative;
  z-index: 99;
}
.question_new div:not(:first-child) {
  flex: 1;
}
/*.question_new div:nth-child(3) {
  opacity: 0;
  visibility: hidden;
  -webkit-animation: smoothdisplay 1s;
  animation: smoothdisplay 1s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -moz-animation-delay: 1.5s;
  -webkit-animation-delay: 1.5s;
  -o-animation-delay: 1.5s;
  animation-delay: 1.5s;
}*/

.question_new {
  justify-content: space-between;
  flex-wrap: wrap;
}
.question_new #slide_img {
  text-align: center;
}
.question_new img {
  max-width: initial;
  max-height: initial;
  /*width: auto;*/
  height: auto;
  min-height: auto;
  -webkit-animation: zoominoutsinglefeatured;
  animation: zoominoutsinglefeatured;
  animation-duration: 2s;
  -webkit-animation-duration: 2s;
  /*-webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  transform-origin: center;
  -webkit-transform-origin: center;
  -webkit-transform: scale(0);
  transform: scale(0);*/
}
.question_new ul, .question_new ol {
  flex: 1;
  font-size: 28px;
  font-weight: normal;
  opacity: 0;
  visibility: hidden;
  -webkit-animation: smoothdisplay 1s;
  animation: smoothdisplay 1s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -moz-animation-delay: 0s;
  -webkit-animation-delay: 0s;
  -o-animation-delay: 0s;
  animation-delay: 0s;
}
.question_new p span, .question_new ul li span, .question_new ol li span {
  font-size: 28px;
  font-weight: normal;
}
.question_new ul li, .question_new ol li {
  margin-bottom: 0.5rem;
}


/* Dark & Light Theme Styles */
.show-light,
.lightheme .hide-light {
  display: none;
}
.hide-light,
.lightheme .show-light {
  display: block;
}
.lightheme #overlaydark {
  background: #e5e5e5;
}
.lightheme .ws-link a,
.lightheme .ws-quiz .content-wrapper p#topictitle,
.lightheme .question_new div:first-child,
.lightheme .thanks blockquote a {
  color: #800;
}
.lightheme .ws-quiz .content-wrapper h3,
.lightheme .ws-quiz .content-wrapper p,
.lightheme .que-wrapper p,
.lightheme .ws-title blockquote p,
.lightheme .ws-title p,
.lightheme .que-wrapper span {
  color: #000;
}
.lightheme .ws-quiz .content-wrapper p,
.lightheme .question_new div:first-child {
  font-size: 34px;
}
.lightheme .thanks blockquote a,
.lightheme .ws-link a,
.lightheme .ws-quiz .content-wrapper h3 {
  font-size: 32px;
}



/* Automated video with image and text - New */
.video-making.darktheme #overlaydark {
  background-color: rgba(0,0,0,.7);
}
.video-making.lightheme #overlaydark {
  background: rgba(229,229,229,0.7);
}
.video-making.darktheme #slideText {
  color: #fff;
}
.video-making.lightheme #slideText {
  color: #000;
}
.video-making.darktheme #timeDurationError {
  color: #f79420;
}
.video-making.lightheme #timeDurationError {
  color: #f00;
}
.automated_video .ws-title {
  height: 100vh;
}
#beforeStartVideo .img-wrapper img {
  max-width: 100%;
}
#beforeStartVideo .form-wrapper select {
  height: 45px !important;
}
#beforeStartVideo .form-wrapper #timeDurationError {
  text-transform: unset;
  font-size: 14px;
  text-align: left;
}
#beforeStartVideo .button-wrapper button {
  padding: 0.5rem 3rem;
}
#videoStarted #wonderslate,
#videoStarted #prepjoy {
  align-items: center;
}
#videoStarted #wonderslate img,
#videoStarted #prepjoy img {
  width: 50px;
  height: 50px;
}
#videoStarted .ws-link {
  height: auto;
  margin-right: 0;
}
#videoStarted .ws-link a {
  font-size: 18px;
  text-transform: unset;
}
#videoStarted .slides {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
#videoStarted #wonderslateEndImage,
#videoStarted #prepjoyEndImage,
#videoStarted .background-images {
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100vh;
  position: absolute;
  z-index: 10;
}
#videoStarted #wonderslateEndImage,
#videoStarted #prepjoyEndImage {
  background-size: contain;
}
#videoStarted #prepjoyEndImage {
  background-color: #170d4c;
}
#videoStarted #slideText {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  padding: 0 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 30px;
  width: 50%;
  margin: 0 auto;
}
#videoStarted #slideText ol,
#videoStarted #slideText ul {
  text-align: left;
}
#videoStarted #slideText ol li,
#videoStarted #slideText ul li {
  line-height: 30px;
  padding-bottom: 10px;
  font-family: 'Rubik', sans-serif;
}
#videoStarted #slideText .splitting strong {
  padding-right: 6px;
}
#videoStarted #backgroundImages {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
#videoStarted .background-images p {
  display: none;
}
@media (max-width: 991px) {
  #videoStarted #slideText {
    font-size: 26px;
    width: 75%;
  }
}
@media (max-width: 767px) {
  .video-making.lightheme #overlaydark {
    background: #e5e5e5;
  }
  .prepjoy-video-end #videoStarted .ws-title {
    background-color: #170d4c;
  }
  .video-making.darktheme #overlaydark {
    background-color: rgba(0,0,0,1);
  }
  #beforeStartVideo .img-wrapper p {
    font-size: 22px;
  }
  #videoStarted #wonderslate img,
  #videoStarted #prepjoy img {
    width: 40px;
    height: 40px;
  }
  #videoStarted .ws-link a {
    font-size: 15px;
  }
  #videoStarted .ws-title {
    height: auto;
    padding: 5rem 0.5rem 0.5rem;
  }
  #videoStarted .slides {
    position: relative;
  }
  .video-making.lightheme #videoStarted .background-images {
    box-shadow: 0 0 5px #ccc;
  }
  .video-making.lightheme #videoStarted.no-images .background-images {
    box-shadow: none;
  }
  #videoStarted .background-images {
    position: absolute;
    background-image: none !important;
    height: 280px;
    border-radius: 15px;
    width: 95%;
    left: 0;
    right: 0;
    margin: 0 auto;
  }
  #videoStarted #backgroundImages {
    position: relative;
    height: 280px;
    z-index: 99;
  }
  #videoStarted .background-images p {
    display: block;
    margin: 0;
  }
  #videoStarted .background-images p img {
    width: 100%;
    height: 280px;
    border-radius: 15px;
  }
  .darktheme #videoStarted .background-images .slider_text {
    background: #000;
    color: #fff;
  }
  .lightheme #videoStarted .background-images .slider_text {
    background: #e5e5e5;
    color: #000;
  }
  #videoStarted.no-images .background-images .slider_text {
    top: 0;
  }
  #videoStarted .background-images .slider_text {
    position: absolute;
    height: 100vh;
    width: 95%;
    margin: 15px auto 0;
    text-align: center;
    top: 280px;
    left: 0;
    right: 0;
    z-index: 99;
    font-size: 20px;
  }
  #videoStarted .background-images .slider_text ol,
  #videoStarted .background-images .slider_text ul {
    text-align: left;
    font-family: 'Rubik', sans-serif;
  }
  #videoStarted #slideText {
    position: relative;
    font-size: 22px;
    width: 100%;
    padding-top: 20px;
    display: none;
  }
}
@media (max-width: 575px) {
  #beforeStartVideo .form-wrapper select {
    width: 290px;
  }
}