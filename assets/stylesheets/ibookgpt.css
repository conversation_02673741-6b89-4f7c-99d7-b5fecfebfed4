/* css/styles.css */

/* General Styles */
body {
  font-family: 'Roboto', sans-serif;
  color: #333333;
  margin-top: 70px; /* For fixed navbar */
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
}

a {
  color: #0066FF;
}

a:hover {
  color: #0056cc;
  text-decoration: none;
}

/* Navbar */
.navbar-brand img {
  max-height: 50px;
}

.navbar-nav .nav-link {
  margin-right: 15px;
  font-weight: 500;
}

.navbar-nav .btn {
  margin-left: 15px;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  position: relative;
}

.hero-section::after {
  content: '';
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.hero-section .container {
  position: relative;
  z-index: 2;
}

.hero-section h1 {
  font-size: 3rem;
}

.hero-section p {
  font-size: 1.25rem;
}

/* Features Section */
.features-section .feature-card {
  background-color: #F2F2F2;
  border-radius: 8px;
  position: relative;
}

.features-section .feature-card .icon {
  color: #0066FF;
}

.features-section .feature-card:hover {
  background-color: #E6E6E6;
}
/* Default height for small devices */
#intro-example {
  height: 400px;
  background-image: url('/assets/gptsir/file.webp');
}

.demoVideo{
  height: 300px;
  border-radius: 10px;
  width: 90%;
}

.img-wrap{
  max-width: 75%;
  display: flex;
  border-radius: 10px;
}
@media (max-width: 768px) {
  .demoVideo{
    width: 100%;
    height: 200px;
  }
  .learnmore{
    font-size: 14px;
  }
  #intro-example h6{
    font-size: 14px;
    margin: 0 12px;
  }
  .img-wrap{
    margin: 0 auto;
  }
}
/* Footer */
footer a {
  color: #ffffff;
}

footer a:hover {
  text-decoration: underline;
}

footer hr {
  border-top: 1px solid #666666;
}
