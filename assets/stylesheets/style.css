body{
    font-family: 'Roboto Slab', sans-serif;
    font-weight: 300;
    background-color: #f0f0f0;
}
a{
    color: #009bff;
}
.header{
    padding-top: 15px;
    padding-bottom: 15px;
}

#updatedHeader {
    text-align: center;
}

.navbar1 {
    -webkit-box-shadow: 0 8px 6px -6px #999;
    -moz-box-shadow: 0 8px 6px -6px #999;
    box-shadow: 0 8px 6px -6px #999;

    /* the rest of your styling */
}
.main {
    background-color: white;
    margin: 10px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    border-radius: 2px;
    /*border: 1px solid rgb(220,221,224);*/
}

.main-find-friends {
    height: 400px;
}

.main-shadow {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    background-color: white;
    border-radius: 2px;
    margin-bottom: 10px;
    margin-top: 10px;
}

.maincontent {
    padding-left: 40px;
}

.sidebar {
    background-color: white;
    margin: 10px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    border-radius: 2px;

    /*border: 1px solid rgb(220,221,224);*/
}


/*User Profile*/

.ProfileImg {
    text-align: center;
    padding-top: 15px;
}

.ProfileImg img {
    width: 40px;
    height: 40px;
}

.EditProfile {
    text-align: center;
    font-weight: 600;
    letter-spacing: 1px;
    margin-top: 20px;
}

.basicinfo {
    color: #f15b2a;
    padding-top: 20px;
    font-weight: 600;
}

.BasicInfo {
    border: 1px solid #ccc;
    border-radius: 5px;
}

.BasicInfo .form-group {
    margin-left: 20px;
    margin-right: 20px;
    margin-top: 10px;
}

.AdditionalInfo .form-group {
    margin-left: 20px;
    margin-right: 20px;
    margin-top: 10px;
}

.BasicInfo #profilepic {
    border: none;
}

.BasicInfo #countrySelect {
    height: 300px;
    color: red;
}

.AdditionalInfo {
    border: 1px solid #ccc;
    border-radius: 5px;
}

.additionalinfo {
    color: #f15b2a;
    padding-top: 20px;
    font-weight: 600;
}

.brand a:hover{
    text-decoration: none;
}

.brand a{
    font-family: 'Exo', sans-serif;
    font-size: 1.5em;
    color: #ffffff;
}

.homemessage {
    font-size: 3em;
    color: #ffffff;
}

.homemessagesmall {
    font-size: 1.5em;
    color: #ffffff;
}
.homemessagesmallest {
    font-size: 1em;
    color: #ffffff;
}

.brand img{
    display: inline-block;
    width: 40px;
    vertical-align: middle;
    margin-right: 5px;
}

.social-links ul li img{
    width: 30px;
}

.header-and-topic-select{
    min-height: 80vh;
    background-image: url(../images/newhome.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-attachment: fixed;
}

@media only screen and (min-width:320px) and (max-width:500px) {
    .header-and-topic-select{
        min-height: 100vh;
        background-image: url(../images/grey1.jpg);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-attachment: fixed;
    }
}

.success{
    min-height: 100vh;
    background-image: url(../images/library3.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-attachment: fixed;
}
.topic-select{
    margin-top: 70px;
}

.topic-select .col-xs-12{
    margin-top: 10px;
    margin-bottom: 10px;
}

select.form-control{
    border: 1px solid #828180;
    border-radius: 4px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: url(../images/icon-arrow-down.png) 96% no-repeat #fff;


}

.child{
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

}

.content-recent{
    min-height: 70vh;
    background-color: #f1f1f1;
    background-size: 100% 100%;
}

.content-appeal{
    min-height: 70vh;
    background-image: url(../images/bg-bottom.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    opacity: 0.9;
}

.content-appeal .column {
    height: 100%;
}

.content-appeal .column p{
    font-size: 2.7vmax;
    color: #ffffff;
}

.row-centered {
    text-align:center;

}

.row-right {
    /*text-align:right;*/
    /*height: 400px;*/
}

.topicHeight {
    height: 100px;
    margin-bottom: 50px;
    margin-top: 40px;
}

.col-centered {
    display:inline-block;
    float:none;
    /* reset the text-align */
    text-align:left;
    /* inline-block space fix */
    margin-right:-4px;
}

.col-centered-without-align {
    display:inline-block;
    float:none;

    /* inline-block space fix */
    margin-right:-4px;
}

.vcenter {
    display: inline-block;
    vertical-align: middle;
    float: none;

}


.vtop {
    display: inline-block;
    vertical-align: top;
    float: none;
}




 * {
margin: 0;
}
html, body {
height: 100%;
}
.wrapper {
min-height: 70%;
height: auto !important;
height: 100%;
margin: 0 auto -4em;
}
.footer, .push {
height: 4em;
}


/*Footer*/


footer {
    background-color: rgba(0, 0, 0, 0.7);
}


footer .white {
    color: white;
}

footer .row{
    margin-top: 15px;
    margin-bottom: 10px;
}
footer .footLink a{
    background: transparent;
    color: white;
    font-weight:300;
}

footer .footLink a:hover {
    color: #F25A29;
}


footer a:hover{
    text-decoration: none;
}

footer .lesson {
    color: white;
}

footer .lesson:hover {
    color: #F25A29;
}

footer .digest {
    color: white;
}

footer #sociallinks a:hover{
    color: #F25A29;
}

footer #sociallinks i:hover {
    color: #F25A29;
}

footer #sociallinks i {
    color: white;
}

/*End of Footer*/


/*login & register modal*/


#registerModal .modal-md .modal-content .modal-body {
    background-color: #ffffff;
    color: black;
}

#registerModal .modal-md .modal-content .modal-body a {
    color: #1F45FC;
}

#registerModal .modal-md .modal-content .modal-body .row {
    margin-bottom: 0;
    margin-top: 0;
}

#registerModal .modal-md .modal-content .modal-body .omb_btn-facebook {
    color:white;
    border-radius: 0;
}

#registerModal .modal-md .modal-content .modal-body .omb_btn-facebook .fbText {
    font-size: 15px;
}

#registerModal .modal-md .modal-content .modal-body .omb_btn-facebook i {
    float: left;
    font-size: 20px;
}

#registerModal .modal-md .modal-content .modal-body .logIn {
    border-radius: 0;
    background-color: #00bfa5;
    border: none;
}

#registerModal .modal-md .modal-content .modal-body .forgot a {
    color: #aaa;
    padding-left: 10px;
}

#registerModal .modal-md .modal-content .modal-body .remember {
    color: #aaa;
    margin-top: 14px;
    margin-left: 8px;
}

#registerModal .modal-md .modal-content .modal-body .account {
    font-size: 20px;
}

#registerModal .modal-md .modal-content .modal-body .account a {
    color: #00bfa5;
}

#registerModal .modal-md .modal-content .modal-body .omb_btn-google {
    color:white;
    border-radius: 0;
}

#registerModal .modal-md .modal-content .modal-body .omb_btn-google .gText {
    font-size: 15px;
}

#registerModal .modal-md .modal-content .modal-body .omb_btn-google i {
    float: left;
    font-size: 20px;
}

#registerModal .modal-md .modal-content .modal-body button .close {
    color: black;
}

#registerModal .modal-md .modal-content .modal-body .omb_authTitle {
    color: black;
}

#registerModal .modal-md .modal-content .modal-body h3 {
    margin-top: 0;
}

.verticalLine {
    border-left: 1px solid white;
    margin-left: 40px;
}

.horizontalLine {
    border-bottom: 1px solid white;
    border-top: 1px solid white;
}

.omb_socialButtons h3:after {
content: “”;
height: 2px;
display: block;
position: absolute;
left: 0;
right: 0;
top: .5em;
z-index: -1;
border-top: 1px solid #504331;
border-bottom: 1px solid #504331;
}

/*End of Modal*/


/* ROUNDED ONE CHECKBOX*/

.roundedOne {
	width: 28px;
	height: 28px;
	background: #fcfff4;                                        

	background: -webkit-linear-gradient(top, #fcfff4 0%, #dfe5d7 40%, #b3bead 100%);
	background: -moz-linear-gradient(top, #fcfff4 0%, #dfe5d7 40%, #b3bead 100%);
	background: -o-linear-gradient(top, #fcfff4 0%, #dfe5d7 40%, #b3bead 100%);
	background: -ms-linear-gradient(top, #fcfff4 0%, #dfe5d7 40%, #b3bead 100%);
	background: linear-gradient(top, #fcfff4 0%, #dfe5d7 40%, #b3bead 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcfff4', endColorstr='#b3bead',GradientType=0 );
	margin: 20px auto;

	-webkit-border-radius: 50px;
	-moz-border-radius: 50px;
	border-radius: 50px;

	-webkit-box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
	-moz-box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
	box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
	position: relative;
}

.roundedOne label {
	cursor: pointer;
	position: absolute;
	width: 20px;
	height: 20px;

	-webkit-border-radius: 50px;
	-moz-border-radius: 50px;
	border-radius: 50px;
	left: 4px;
	top: 4px;

	-webkit-box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,1);
	-moz-box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,1);
	box-shadow: inset 0px 1px 1px rgba(0,0,0,0.5), 0px 1px 0px rgba(255,255,255,1);

	background: -webkit-linear-gradient(top, #222 0%, #45484d 100%);
	background: -moz-linear-gradient(top, #222 0%, #45484d 100%);
	background: -o-linear-gradient(top, #222 0%, #45484d 100%);
	background: -ms-linear-gradient(top, #222 0%, #45484d 100%);
	background: linear-gradient(top, #222 0%, #45484d 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#222', endColorstr='#45484d',GradientType=0 );
}

.roundedOne label:after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	filter: alpha(opacity=0);
	opacity: 0;
	content: '';
	position: absolute;
	width: 16px;
	height: 16px;
	background: #00bfa5;

	background: -webkit-linear-gradient(top, #00bfa5 0%, #00bfa5 100%);
	background: -moz-linear-gradient(top, #00bfa5 0%, #00bfa5 100%);
	background: -o-linear-gradient(top, #00bfa5 0%, #00bfa5 100%);
	background: -ms-linear-gradient(top, #00bfa5 0%, #00bfa5 100%);
	background: linear-gradient(top, #00bfa5 0%, #00bfa5 100%);

	-webkit-border-radius: 50px;
	-moz-border-radius: 50px;
	border-radius: 50px;
	top: 2px;
	left: 2px;

	-webkit-box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
	-moz-box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
	box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
}

.roundedOne label:hover::after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
	filter: alpha(opacity=30);
	opacity: 0.3;
}

.roundedOne input[type=checkbox]:checked + label:after {
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
	filter: alpha(opacity=100);
	opacity: 1;
}
  

/*end of checkbox*/


/*************************/
.topic-banner{
    background-image: url(../images/bg-topic-banner.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
.topic-banner .container{
    position: relative;
    min-height: 22vh;
}

.topic-banner-mini{
    background-image: url(../images/bg-topic-banner.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    min-height: 6vh;

}
/*.topic-banner-mini .container{
    position: relative;
    min-height: 9vh;
}*/


.topic{
    position: absolute;
    top: 10%;
    left: 50%;
    transform: translate(-50%, -50%);
    width:90%;
    color: white;
    font-size: 1.7vmax;
    padding-top: 10px;
}

.topic-title{
    font-size: 2.0vmax;
}

.topic-title-white{
    font-size: 2.0vmax;
    color: #ffffff;
}


/********/

.boxify {
    border-width: 1.5px;
    border-color: rgb(130, 129, 128);
    border-style: solid;
    border-radius: 8px;
    position: relative;
    height: 191px;
}

.boxify:hover{
    cursor: pointer;
}

.boxify.quiz-header{
    cursor: default;
}
.boxifybook {
    border-width: 1.0px;
    border-color: rgb(130, 129, 128);
    border-style: solid;
    border-radius: 4px;
    position: relative;
    background-color: white;
    box-shadow: 2px 2px 2px #888888;
}
.boxifyhomepage {
    border-width: 1.0px;
    border-color: white;
    border-style: solid;
    border-radius: 4px;
    position: relative;
    background-color: transparent;
    box-shadow: 2px 2px 2px #888888;
}
.boxifywhite {
    border-width: 1.0px;
    border-color: rgb(130, 129, 128);
    border-style: solid;
    border-radius: 2px;
    position: relative;
    height: auto;
    background-color: white;
    width: 80%;
    box-shadow: 2px 2px 2px #888888;
}

.boxifywhitefull {
    border-width: 1.0px;
    border-color: rgb(130, 129, 128);
    border-style: solid;
    position: relative;
    height: auto;
    background-color: white;

}

.boxifywhite .img-responsive {
    margin: 0 auto;
    vertical-align: middle;
}

.fill {
    min-height: 100%;
    height: 100%;
}

.profilepage .thumbnail {
    position: relative;
    padding: 0px;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    /*box-shadow: 2px 2px 2px #888888;*/
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
}

.profile_picture img, .profile_picture i {
    height: 140px;
    width: 140px;
    position: relative;
    top: 20px;
    left: 80px;
}


.profilepage .caption {
    height: 700px;
    background: transparent;
    background-color: rgba(0, 0, 0, 0.1);
}


.profilepage .profileDetails {
    height: 90px;
}

.profilepage .thumbnail .profile_picture {
    /*background-color: rgba(164,190,57, 0.6);*/
    background-image: url(../images/bg-bottom.jpg);
    background-repeat: no-repeat;
    background-position: bottom;
    background-size: cover;
    height: 130px;
}

.profile_picture h2 {
    color: white;
    background: transparent;
}

.profile_picture .btn {
    color: #fff;
    background: transparent;
    border: 1px solid #fff;
}

.profile_picture h2 .btn a {
    text-decoration: none;
    color: white;
}



.profilepage .caption p {
    text-align: center;
}

.profilepage .infoProfile {
    width: 400px;
    padding-top: 30px;
    padding-left: 0;

}

.profilepage .caption .aboutUser .yourself {
    width: 400px;
}

.profilepage .infoProfile a {
    position: relative;
    /*background-color: #f0f0f0;*/
    border-radius: 3px;
    border: 1px solid #f0f0f0;
    left: 580px;
    bottom: 40px;
    font-size: 10px;
    width: 50px;
    height: 50px;
    padding: 10px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .20),
    0 2px 10px 0 rgba(0, 0, 0, .16);
}

.profilepage .infoProfile a:hover {
    border: 1px solid #f15b2a;
    border-radius: 3px;
    color: #f15b2a;
    box-shadow: none;

}

.profilepage .infoProfile h4 {
    text-align: center;
}

.profilepage .infoProfile p {
    text-align: center;
}

.profilepage .infoProfile .pinfo {
    border: 1px solid #ccc;
    border-radius: 3px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    /*height: 20px;*/
}

.profilepage .infoProfile .pinfo:hover {
    box-shadow: none;
}

.profilepage .infoProfile .pinfo h4 {
    font-size: 15px;
    margin-top: 3px;
}

.profilepage .infoProfile .pinfo p {
    margin-bottom: 3px;
}

.fb-share-button {
    position: relative;
    left: 1000px;
    bottom: 50px;
}

.profiletabs .nav-tabs > li {
    float:none;
    display:inline-block;
    *display:inline; /* ie7 fix */
    zoom:1; /* hasLayout ie7 trigger */
}

.profiletabs {
    padding-top: 50px;
}

.profiletabs .nav-tabs {
    text-align:center;
}

.profiletabs .nav-tabs a {
    color: black;
}

.profiletabs .nav-tabs li.active a {
    border-bottom: none;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .20),
    0 2px 10px 0 rgba(0, 0, 0, .16);
}

.profiletabs .nav-tabs li.active a {
    border-bottom: 2px solid #009bff;
}

.profiletabs .tab-content .active .table-responsive {
    background-color: #fff;
    /*border: 1px solid #f15b2a;*/
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .20),
    0 2px 10px 0 rgba(0, 0, 0, .16);
    transition: 5s ease;
    border-radius: 4px;
}

.profiletabs .tab-content .active .table-responsive .aboutUser {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    background-color: #f0f0f0;
}

.profiletabs .tab-content table tr {
    text-align: center;
}

.profiletabs .tab-content table tr {

}


.thumbnail{
    position: absolute;
    height: 100%;
    width: 100%;
    border-radius: 7px;
    border: 0px;
    padding: 0px;
}

.play-icon{
    position: absolute;
    top: 42%;
    left:42%;
    width: 40px;
    height: 40px;
}

.thumbnail-vid-title{
    color: white;
    margin-left: 15px;
    margin-top: 15px;
    position: absolute;
    font-size: 1.5rem;
}

.thumbnail-quiz-title{
    color: #f15a29;
    margin-left: 45px;
    margin-top: 55px;
    position: absolute;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 17px;
}

.thumbnail-title{
    color: #f15a29;
    font-weight: 700;
    text-transform: uppercase;
}

.thumbnail-link-options{
    margin-left: 15px;
    margin-top: 45px;
    position: absolute;
    font-size: 1.5rem;
}

.content{
    margin-top: 50px;
    padding-left: 6.2%;
    padding-right: 6.2%;
}

.thumbnail-oth-title{
    color:white;
    width: 100%;
    position: absolute;
    top:0;
    left:0;
    font-weight: 700;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
    padding: 5px;
    text-transform: uppercase;
    display:block;
    text-align:center
}

.video-section,.notes-section,.quiz-section,.mm-section{
    margin-top: 15px;
    margin-bottom: 15px;
}

.quiz-section{
    margin-bottom: 30px;
}

.quiz-modal-header{
    background-color: #73a7cb;
}

.quiz-modal-header p{
    color: white;
    font-weight: 700;
    font-size: 25px;
    margin-top:10px;
    margin-left: 10px;
    text-transform: uppercase;
}

.checkbox label, .radio label{

}

.quiz-hr{
    margin: 0px;
}

.quiz-modal-footer{
    border: none;
    padding: 15px;
}

.quiz-form{
    padding-top: 10px;
}

.red{
    color: red;
}

.green{
    color: #5cb85c;
}

.blue{
    color: blue;
}

.input-text-quiz {
    border: none;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: #bcbcbc;
    font-style: italic;
    font-weight: 700;
}

.input-text-quiz:focus{
    outline:0;
    border-bottom-color: blue;
}

.quiz-modal-body .container-fluid{
    margin-top: 35px;
    margin-bottom: 20px;

}

.radio-true, .radio-false {
    display: none;
}

.radio-true + label{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(186, 215, 125);
    border-radius: 5px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
}



.pagenumber-red{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: red;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: red;
    color: white;

}
.pagenumber-grey{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: grey;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: grey;
    color: white;
}

.pagenumber-current{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: green;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;

}
.pagenumber-green{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: green;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: green;
    color: white;
}
.pagenumber-blue{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: blue;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: blue;
    color: white;
}

.radio-true:checked + label, .true-selected-box{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(186, 215, 125);
    border-radius: 5px;
    background-color: rgb(186, 215, 125);
    display: inline-block;
    padding: 0px;
}

.radio-false + label{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(241, 90, 41);
    border-radius: 5px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
}

.radio-false:checked + label, .false-selected-box{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(241, 90, 41);
    border-radius: 5px;
    border-style: solid;
    background-color: rgb(241, 90, 41);
    display: inline-block;
    padding: 0px;
}

.left-border-vr{
    border: none;
    border-left-width: 2px;
    border-left-color:#8a8988;
    border-left-style: solid;
}

.sum-modal-header{
    background-color: #f26335;
}

.sum-modal-header-text{
    color: white;
    font-weight: 700;
    font-size: 20px;
    margin-top:10px;
    margin-left: 10px;
    text-transform: uppercase;
}

.sum-modal-body {
    margin-top: 35px;
    margin-bottom: 20px;
    font-size: 17px;
}

.top-container{
    padding-top:50px;
}

/*About Us*/

.founder img {
    width: 1000px;
    margin: 0 auto;
}

#anand img {
    filter: gray;
    filter: grayscale(1);
    -webkit-filter: grayscale(1);
}

#anand:hover img {
    filter: none;
    -webkit-filter: grayscale(0);
}

#anand:hover #anandInfo {
    background-color: black;
    color: white;
}

#anandInfo {
    background-color: rgba(245, 245, 245, 1);
}

/*Thumbnail*/

.about .thumbnail {
    position: relative;
    padding: 0px;
    margin-bottom: 20px;
    border: 1px solid #ccc;
    /*box-shadow: 2px 2px 2px #888888;*/
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
}

.about .thumbnail img {
    width: 50%;
    filter: gray;
    filter: grayscale(1);
    -webkit-filter: grayscale(1);
}

.about .thumbnail:hover img {
    filter: none;
    -webkit-filter: grayscale(0);
}

.about .thumbnail:hover .caption {
    background-color: black;
    color: white;
}

.about .thumbnail h3 {
    text-align: center;
}

/*Founder-SocialNetwork*/

.founderSocial a > i {
    font-size: 25px;
}

.founderSocial a {
    margin-left: 5px;
    margin-right: 5px;
}

.founderSocial .thumbnail a {
    display: inline-block;
}

.founderSocial {
    text-align: center;
}

/*Team*/

#team {
    background-color: rgba(245, 245, 245, 5);
    border: 1px solid #cccccc;
}

#team .thumbnail {
    background-color: white;
}

#team h3 {
    text-align: center;
}

#team h3.teamMembers {
    color: #f15b2a;
    font-size: 35px;
    letter-spacing: 2px;
}

#team .thumbnail a > i {
    font-size: 20px;
}

#team .thumbnail a {
    margin-left: 5px;
    margin-right: 5px;
}

#team .thumbnail img{
    width: 200px;
    height: 230px;
}

#team .qualification {
    text-align: center;
}

#team h4 {
    text-align: center;
}

#team .thumbnail a {
    display: inline-block;
}

#team .thumbnail .social {
    text-align: center;
}

.profile{
    color: white;
    font-size: 1.4vmax;
}

.profile-banner{
    background-image: url(../images/ProfilePageBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.profile-banner .container{
    position: relative;
    padding-top: 20px;
    padding-bottom: 20px;
    min-height: 22vh;
}

.smallText{
    font-size: 13px;
}

.smallText a{
    text-decoration: underline;
    color: white;
}

.smallerText{
    font-size: 12px;
}

.smallestText{
    font-size: 10px;
}


.lmodal{
    position: fixed;
    z-index: 999;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background-color: Black;
    filter: alpha(opacity=60);
    opacity: 0.6;
    -moz-opacity: 0.8;
}


@media only screen and (min-width:320px) and (max-width:500px) {
    .lmodal{
        position: fixed;
        z-index: 999;
        height: 40%;
        width: 40%;
        padding-right: 200px;
        background-color: black;
        filter: alpha(opacity=60);
        opacity: 0.6;
        -moz-opacity: 0.8;
    }
}

@media only screen and (min-width:500px) and (max-width:600px) {
    .lmodal{
        position: fixed;
        z-index: 999;
        height: 40%;
        width: 40%;
        /*padding-right: 50px;*/
        background-color: black;
        filter: alpha(opacity=60);
        opacity: 0.6;
        -moz-opacity: 0.8;
    }
}
.lcenter {
    z-index: 1000;
    margin: 300px auto;
    padding: 10px;
    width: 20%;
    height: 20%;
    border-radius: 10px;
    filter: alpha(opacity=100);
    opacity: 1;
    -moz-opacity: 1;
}

.alert-thin {
    padding: 1px;
    margin-bottom: 2px;
}

.alert{
    background-color: transparent;
    border: hidden;
}

.topsection{
    padding-top: 70px;
}

.dropdown-submenu {
    position: relative;
}

.dropdown-submenu>.dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: -1px;
    -webkit-border-radius: 0 6px 6px 6px;
    -moz-border-radius: 0 6px 6px;
    border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover>.dropdown-menu {
    display: block;
}

.dropdown-submenu>a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #ccc;
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover>a:after {
    border-left-color: #fff;
}

.dropdown-submenu.pull-left {
    float: none;
}

.dropdown-submenu.pull-left>.dropdown-menu {
    left: -100%;
    margin-left: 10px;
    -webkit-border-radius: 6px 0 6px 6px;
    -moz-border-radius: 6px 0 6px 6px;
    border-radius: 6px 0 6px 6px;
}

.questions{
    color: black;
}
h3,h4,h5 {
    color: #f15a29;
}



.belowbox a{
    color: black;
}

.bluelabel{
    color: #248ce6;

}

.yellowstar{
    color: brown;
}

.brown{
    color:brown;
}

.transparent-button {
    background-color: Transparent;
    background-repeat:no-repeat;
    cursor:pointer;
    overflow: hidden;
    border-width: 1.5px;
    border-color: white;
    border-style: solid;
    border-radius: 8px;
    color: #fff;

}

.transparent-button a{
    color: #fff;
}
#registerModalContent{
    background-image: url(../images/classic-felt-natural-white1.jpg);
}

.top-nav  a{
    color: #000;
    /*font-size: 14px;*/
    /*font-weight: bold;*/
}

/*.navbar .nav > li > a, .navbar .nav > li > a:first-letter,
.navbar .nav > li.current-menu-item > a,
.navbar .nav > li.current-menu-ancestor > a {
    font-weight: bold;
    color: #7b7a79;
}*/

#myNavbar .navbar-nav a:hover {
    color: #f15a29;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
}

.top-nav li a {
    /*font-weight: bold;*/
    /*color: #7b7a79;*/
}

.text-right {
    /*text-align: right;*/
}

.light-grey-background{
    background-color: #f1f1f1;
    /*background-color: rgb(233,234,237);*/
}

hr.myhrline{
    margin: 5px;
}

.quizgreen{
    background-color: #5dbcf9;
}
.relvideopink{
    background-color: #ef4f5e;
}

.quizblue{
    background-color: #a4be39;
}
.notesorange{
    background-color: #f7941e;
}
.linkorange{
      background-color: #F25A29;
  }

.panel-default > .panel-heading-custom{
    background-color: #ef4f5e;
    color: white;
}

.greytext{
    color: #7b7a79;
}
.whitetext{
    color: #ffffff;
}

.darkgrey{
    color: #333333;
}
.homefeature {
    font-size: 2.5em;
    color: #f15a29;
}

.homefeatureList {
    font-size: 1.5em;
    color: #5f6163;

}

.card {
    position: relative;
    overflow: hidden;
    margin: .5rem 0 1rem;
    background-color: #fff;
    border-radius: 2px;
}

.box-header{
    color:white;
    width: 100%;
    position: absolute;

}

.newtopic{

    width:90%;
    color: #333333;
    font-size: 2vmax;
    padding-top: 10px;
}

hr.topichrline{
    margin: 7px;
    background-color: #f15b2a;
    display: block;
    height: 1px;
}

.orange{
    color:orange;
}

.red-border{
    border: 1px solid red;
}

/*popover for home & topic*/

.popover {
    background: transparent;
    background-color: rgba(0, 0, 0, 0.5);
    border: 2px solid #f15a29;
    box-shadow: 0 2px 5px 0 white,
                0 2px 10px 0 white;
    -moz-transition-property: top, left;
  -moz-transition-duration: 1s;
  -moz-transition-timing-function: ease-in-out;
  -ms-transition-property: top, left;
  -ms-transition-duration: 1s;
  -ms-transition-timing-function: ease-in-out;
  -o-transition-property: top, left;
  -o-transition-duration: 1s;
  -o-transition-timing-function: ease-in-out;
  -webkit-transition-property: top, left;
  -webkit-transition-duration: 1s;
  -webkit-transition-timing-function: ease-in-out;
  transition-property: top, left;
  transition-duration: 1s;
  transition-timing-function: ease-in-out;
}

.popover-title {
    /*background-color: rgba(121,187,253, 0.8);*/
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
}

.popover-content {
    color: white;
}

/*.popoverhome {
    position: fixed;
    z-index: 999;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background-color: Black;
    filter: alpha(opacity=60);
    opacity: 0.3;
    -moz-opacity: 0.3;
}*/

#popover2 {
    position: relative;
    left: 131px;
    top: 32px;
}

#popover2 + .popover .arrow {
    left: 18% !important;
}

#popover3 {
    position: relative;
    left: 418px;
    top: 40px;
}

#popover1{
    position: relative;
    /*top: 100px;*/
    left: 90px;
}


/*End of Popover*/


/*design for parents, students & teachers*/


.btm {
  margin: 0;
  padding: 0;
  border-left: none;
  min-height: 500px;
  position: relative;
}

.studentBack {
    background: transparent;
   background-color: rgba(0, 0, 0, 0.5);
}

#students {
  background: url(../images/repositorymedium.jpg) no-repeat center center;
  background-size: cover;
  display: block;
}

#teachers {
  background: url(../images/collaborationmedium.jpg) no-repeat center center;
  background-size: cover;
  display: block;
}

#parents {
  background: url(../images/gamificationlow.jpg) no-repeat center center;
  background-size: cover;
  display: block;
}

.analytics {
    background: url(../images/analytics.jpg) no-repeat center center;
    background-size: cover;
    display: block;
}
.studentsRow row {
    margin-top: 0;
}

#student {
    background: url(../images/grey1.jpg) no-repeat;
    /*background-color: #ccc;*/
    background-attachment: fixed;
    background-position: center;
    background-size: cover;
    height: 100%;
    text-align: center;
}

.points p {
    color: white;
    font-size: 18px;
    font-weight: 300;
}

.points h2 {
    color: white;
}
.studentsRow {
    background: url(../images/teachers.jpg);
    background-attachment: fixed;
    background-position: bottom;
    background-size: cover;
}



/*Home HR Tag*/


hr.style-seven {
    height: 30px;
    border-style: solid;
    border-color: #449d44;
    border-width: 2px 0 0 0;
    border-radius: 20px;
}
hr.style-seven:before {
    display: block;
    content: "";
    height: 30px;
    margin-top: -31px;
    border-style: solid;
    border-color: #449d44;
    border-width: 0 0 2px 0;
    border-radius: 20px;
}


.style-seven {
    width: 340px;
    margin: 0 auto;
}

@media only screen and (max-width: 500px) {
    .style-seven {
        width: 250px;
    }
}


/*Home Search Inputs*/


#homeInputs select {
    background-color: rgba(0, 0, 0, 0.4);
    color: white;
    border: 1px solid white;
    -webkit-appearance: none; 
}

#homeInputs select option {
    background: transparent;
    background-color: rgba(0, 0, 0, 1);  
}

/*.redWarning {
       position: relative;
       background-color: red;
    
}*/

.iColor {
    border-bottom: 2px solid red;
}

.labelAlert {
    color: red;
}

.booktitle {
    font-size: 14px;
 font-weight: bold;
    font-family: "Nunito";


}



.chaptertext{
    font-family: "Nunito";
    font-size: 17px;
    line-height: 1.575;
    color: #6f6f6f;
}

.lefttitle{
    font-size: 17px;
    line-height: 1.575;
    color: #6f6f6f;
}
.chapterh3{
    font-family: "Nunito";
    font-size: 24px;
    line-height: 1.875;
    color: #4f4f4f;
    font-weight: bold;
}

.chapterbackground{
    background-image: url(../images/blank-notebook.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
@media only screen and (min-width:320px) and (max-width:500px) {
    .chapterbackground{
        background-image: url(../images/whitebackground.jpg);
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }

}
.bookbackground{
    background-image: url(../images/bookbackground.jpg);
}


.nunito{
    font-family: "Nunito";
}
.robotoslab{
    font-family: "Roboto Slab";
}

.redunderline{
    text-decoration: underline;
    -webkit-text-decoration-color: red;
    -moz-text-decoration-color: red;  /* vendor prefix not required as of V36 */
    text-decoration-color: red;
}
.bluewhitebackground{
    background-image: url(../images/bluewhite.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.lightbrownbackground{
    background-image: url(../images/pattern.png);
    min-height: 75vh;

    margin-top: 3px;


}
.lightcreambackground{
    background-image: url(../images/lightcream.jpg);
    min-height: 75vh;

    margin-top: 3px;

}

.lightgrey{
    color: #f1f1f1;
    /*background-color: rgb(233,234,237);*/
}

.wpbackground{
    min-height: 100vh;
    background-image: url(../images/wptable.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
@media only screen and (min-width:320px) and (max-width:500px) {

    .wpbackground{
        background-image: url(../images/wptableplain.jpg);
        background-size: 100% 100%;
        min-height: 100vh;

    }
}
.wpbackgroundsofa{
    min-height: 100vh;
    background-image: url(../images/wphomesofa.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
.wpbluebackground{

    background-color: #ff5e3a;
}

.wpbluetext{
    color: #03658E;
}

.wpwhiteimagebackground{
    background-image: url(../images/lightbrown.jpg);
    background-repeat: no-repeat;
    background-size: 100% ;
}
.brandorange{
   color: #F25A29;
}

.bigtext{
    font-size: 20px;
    line-height: 1.575;
    letter-spacing: .8px;

}

.bottomElement {
    position: absolute;
    bottom: 35px;

}

.boxifyexplore {
    border-width: 1.0px;
    border-color: rgb(130, 129, 128);
    border-style: solid;
    border-radius: 4px;
    position: relative;
    background-color: white;
    box-shadow: 2px 2px 2px #888888;

}
.boxifyexplore .row{
    margin-top: 10px;
    margin-bottom: 10px;
}

.numberCircle {
    vertical-align: top;
    position: relative;
    display: inline-block;
    width: 60px;
    height: 60px;
    border: 1px solid #D2D2D2;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    border-radius: 50%;
    text-align: center;
    font-size: 20px;
    line-height: 54px;
    color: #9b9b9b;
    margin-left: 20px;
}

.numberCircle.selected {
    border-color: #F7AC51;
    background-color: #F7AC51;
    color: #fff;
    font-weight: 700;
}

.numberCircle a:hover{
    background-color: #F7AC51;
}

.wpbrownbackground{
    background-image:url(../images/ProfilePageBG.jpg); ;
}
.boxifynoborder {
    position: relative;
    height: 191px;
    width:150px;
    box-shadow: 4px 4px 4px #888888;
}
.boxifytop {
    position: relative;
    height: 141px;
    top:0px;
}
.light0{
     background-color: #ce483d;
 }
.dark0{
    background-color: #bd4036;
}
.dark0text{
   color: #bd4036;
}
.light1{
    background-color: #8eb021;
}
.light1text{
    color: #8eb021;
}
.dark1{
    background-color: #7e9d1c;
}
.dark1text{
    color: #7e9d1c;
}
.light2{
    background-color: #79302a;
}
.dark2{
    background-color: #662621;
}
.dark2text{
    color: #662621;
}
.light3{
    background-color: #ffd006;
}
.dark3{
    background-color: #ebc009;
}
.dark3text{
    color: #ebc009;
}
.light4{
    background-color: #abcbd6;
}
.dark4{
    background-color: #8cbecf;
}
.dark4text{
   color: #8cbecf;
}
.light5{
    background-color: #7bc1a1;
}
.dark5{
    background-color: #5eb990;
}
.dark5text{
    color: #5eb990;
}
.light6{
    background-color: #101e55;
}
.dark6{
    background-color: #0b163f;
}
.light7{
    background-color: #fabf28;
}
.dark7{
    background-color: #f2b51c;
}
.light8{
    background-color: #2b2423;
}
.dark8{
    background-color: #151111;
}
.light9{
    background-color: #2a70e8;
}
.light9text{
    color: #2a70e8;
}
.dark9{
    background-color: #0957db;
}
.dark9text{
    color: #0957db;
}

.light10text{
   color: #fc635e;
}

.light11{
    background-color: #ffc80b;
}

.wpbooktitle{
    font-size: 18px;
}
.wpfooterbackground{
    background-image: url(../images/lightorange.jpg);
    background-size: 100% ;
}



.boxifychapter {
    height: 240px;
    width:170px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    border-style: solid;
    border-width: 1.5px;
    border-color: #f0f0f0;
}

.boxifychapterbody {
    position: relative;
    height: 190px;
    top:10px;
    left:10px;
    width:150px;
}


.boxifynobordernoheight {
    position: relative;
    box-shadow: 4px 4px 4px #888888;
}
.whitebackground{
    background-color: white;
}

.wpfeaturesbackground{
    background-color: #f0f0f0;
    /*background-color: rgb(233,234,237);*/
}

.boxifyfaq {
    border-width: 2.0px;
    border-color: #f0f0f0;
    border-style: solid;
    border-radius: 2px;
    position: relative;
    background-color: white;
    margin-right: 10px;
}

.boxifyfaq .row{
    margin-top: 10px;
    margin-bottom: 10px;
}

.bottomElementNoGap {
    position: absolute;
    bottom: 50px;

}

.wplandingimage{
    background-image: url(../images/dotswhite.jpg);
}
.wplandingblueimage{
    background-image: url(../images/dotsblue.jpg);
}

.rightsidebar{
    position: fixed;
    top: 150px;
    right:0px;
    width: 137px;
    height: 30px;
    background-image: url(../images/requestademo.jpg);
    background-size: 100% 100% ;
    box-shadow: 4px 4px 4px #888888;
}

.wpsidebar {
    background-color: white;
    top:10px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    border-radius: 2px;

    /*border: 1px solid rgb(220,221,224);*/
}


.wpbackgroundcolor{
    background-color: #fefefe;
}


.wpboxifychapter {
    height: 250px;
    width:170px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    border-style: solid;
    border-width: 1.0px;
    border-color: #f0f0f0;
}

.wpboxifychapterbody {
    position: relative;

    top:0px;
    width:170px;
}


.topnavblue .row{
    margin-top: 10px;
    margin-bottom: 10px;
}

.wpboxifybigchapter {
    width:600px;
    left:0px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    border-style: solid;
    border-width: 1.0px;
    border-color: #f0f0f0;
}

.wpboxifybigchapterinsidebox {
    position: relative;
    height: 50px;
    width:600px;
    bottom:0px;
    left:0px;
    background-color: #fc635e;

}



.fontsize16{
    font-size: 16px;
    letter-spacing: 0.4px;
    line-height: 1.2;

}

.wpboxifyquiz {
    width:170px;
    left:0px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    margin-bottom: 30px;

}

.wpboxifyquizinsidebox {
    width:170px;
    left:0px;
    bottom:0px;
}

.wpboxifyshadow {
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
}
.wpboxifyborder {
    border-style: solid;
    border-width: 1.0px;
    border-color: #cccccc;
    margin-bottom: 0px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
}
.close{
    color: white;
}

.wpboxifyborder .row{
    margin-top: 10px;
    margin-bottom:10px;
    margin-left:15px;

}

hr.quizhr{
    border-top: 1px solid #cccccc;
    margin-bottom: 0px;
    margin-top: 0px;

}

.wpboxifyvideo {
    width:100%;
    left:0px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    border-style: solid;
    border-width: 1.0px;
    border-color: #f0f0f0;
    margin-bottom: 30px;
}
.wpboxifyvideo .row {
    margin-left: 0;
    margin-right: 0;

}
.wpboxifyvideoinsidebox {
    position: relative;
    height: 40px;
    width:100%;
    bottom:0px;
    left:0px;
    background-color: #fc635e;

}

.normaltext{
    font-weight: normal;
}

.logoblue{
    color:#28bbc6;
}
.logoorange{
    color:#f15a29
}

.rightanswer{
    background-color: #b5dca0;
}

.wronganswer{
    background-color: #88ccf3;
}