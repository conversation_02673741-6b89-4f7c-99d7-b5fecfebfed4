/*! normalize.css v3.0.2 | MIT License | git.io/normalize */
html {
  font-family: 'Roboto Slab', sans-serif;
  font-weight: 300;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
body {
  margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden],
template {
  display: none;
}
a {
  background-color: transparent;
}
a:active,
a:hover {
  outline: 0;
}
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
dfn {
  font-style: italic;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
mark {
  background: #ff0;
  color: #000;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  border: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 1em 40px;
}
hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}
button {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
input {
  line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
legend {
  border: 0;
  padding: 0;
}
textarea {
  overflow: auto;
}
optgroup {
  font-weight: bold;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
  *,
  *:before,
  *:after {
    background: transparent !important;
    color: #000 !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  a[href^="#"]:after,
  a[href^="javascript:"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  select {
    background: #fff !important;
  }
  .navbar {
    display: none;
  }
  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .label {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #ddd !important;
  }
}
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 10px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.42857143;
  color: #333333;
  background-color: #ffffff;
}
input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
a {
  color: #212121;
  text-decoration: none;
}
a:hover,
a:focus {
  color: #000000;
  text-decoration: underline;
}
a:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
figure {
  margin: 0;
}
img {
  vertical-align: middle;
}
.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.img-rounded {
  border-radius: 6px;
}
.img-thumbnail {
  padding: 4px;
  line-height: 1.42857143;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 4px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transition:  all 1s 0s ease, 0.2s 1s 0s ease, ease-in-out 1s 0s ease;
  -moz-transition:  all 1s 0s ease, 0.2s 1s 0s ease, ease-in-out 1s 0s ease;
  -ms-transition:  all 1s 0s ease, 0.2s 1s 0s ease, ease-in-out 1s 0s ease;
  -o-transition:  all 1s 0s ease, 0.2s 1s 0s ease, ease-in-out 1s 0s ease;
  transition:  all 1s 0s ease, 0.2s 1s 0s ease, ease-in-out 1s 0s ease;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.img-circle {
  border-radius: 50%;
}
hr {
  margin-top: 20px;
  margin-bottom: 20px;
  border: 0;
  border-top: 1px solid #eeeeee;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}
[role="button"] {
  cursor: pointer;
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #777777;
}
h1,
.h1,
h2,
.h2,
h3,
.h3 {
  margin-top: 20px;
  margin-bottom: 10px;
}
h1 small,
.h1 small,
h2 small,
.h2 small,
h3 small,
.h3 small,
h1 .small,
.h1 .small,
h2 .small,
.h2 .small,
h3 .small,
.h3 .small {
  font-size: 65%;
}
h4,
.h4,
h5,
.h5,
h6,
.h6 {
  margin-top: 10px;
  margin-bottom: 10px;
}
h4 small,
.h4 small,
h5 small,
.h5 small,
h6 small,
.h6 small,
h4 .small,
.h4 .small,
h5 .small,
.h5 .small,
h6 .small,
.h6 .small {
  font-size: 75%;
}
h1,
.h1 {
  font-size: 36px;
}
h2,
.h2 {
  font-size: 30px;
}
h3,
.h3 {
  font-size: 24px;
}
h4,
.h4 {
  font-size: 18px;
}
h5,
.h5 {
  font-size: 14px;
}
h6,
.h6 {
  font-size: 12px;
}
p {
  margin: 0 0 10px;
}
.lead {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 300;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .lead {
    font-size: 21px;
  }
}
small,
.small {
  font-size: 85%;
}
mark,
.mark {
  background-color: #fcf8e3;
  padding: .2em;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.text-nowrap {
  white-space: nowrap;
}
.text-lowercase {
  text-transform: lowercase;
}
.text-uppercase {
  text-transform: uppercase;
}
.text-capitalize {
  text-transform: capitalize;
}
.text-muted {
  color: #777777;
}
.text-primary {
  color: #337ab7;
}
a.text-primary:hover {
  color: #286090;
}
.text-success {
  color: #3c763d;
}
a.text-success:hover {
  color: #2b542c;
}
.text-info {
  color: #31708f;
}
a.text-info:hover {
  color: #245269;
}
.text-warning {
  color: #8a6d3b;
}
a.text-warning:hover {
  color: #66512c;
}
.text-danger {
  color: #a94442;
}
a.text-danger:hover {
  color: #843534;
}
.bg-primary {
  color: #fff;
  background-color: #337ab7;
}
a.bg-primary:hover {
  background-color: #286090;
}
.bg-success {
  background-color: #dff0d8;
}
a.bg-success:hover {
  background-color: #c1e2b3;
}
.bg-info {
  background-color: #d9edf7;
}
a.bg-info:hover {
  background-color: #afd9ee;
}
.bg-warning {
  background-color: #fcf8e3;
}
a.bg-warning:hover {
  background-color: #f7ecb5;
}
.bg-danger {
  background-color: #f2dede;
}
a.bg-danger:hover {
  background-color: #e4b9b9;
}
.page-header {
  padding-bottom: 9px;
  margin: 40px 0 20px;
  border-bottom: 1px solid #eeeeee;
}
ul,
ol {
  margin-top: 0;
  margin-bottom: 10px;
}
ul ul,
ol ul,
ul ol,
ol ol {
  margin-bottom: 0;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
  margin-left: -5px;
}
.list-inline > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
dl {
  margin-top: 0;
  margin-bottom: 20px;
}
dt,
dd {
  line-height: 1.42857143;
}
dt {
  font-weight: bold;
}
dd {
  margin-left: 0;
}
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-left: 180px;
  }
}
abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #777777;
}
.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
blockquote {
  padding: 10px 20px;
  margin: 0 0 20px;
  font-size: 17.5px;
  border-left: 5px solid #eeeeee;
}
blockquote p:last-child,
blockquote ul:last-child,
blockquote ol:last-child {
  margin-bottom: 0;
}
blockquote footer,
blockquote small,
blockquote .small {
  display: block;
  font-size: 80%;
  line-height: 1.42857143;
  color: #777777;
}
blockquote footer:before,
blockquote small:before,
blockquote .small:before {
  content: '\2014 \00A0';
}
.blockquote-reverse,
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
  text-align: right;
}
.blockquote-reverse footer:before,
blockquote.pull-right footer:before,
.blockquote-reverse small:before,
blockquote.pull-right small:before,
.blockquote-reverse .small:before,
blockquote.pull-right .small:before {
  content: '';
}
.blockquote-reverse footer:after,
blockquote.pull-right footer:after,
.blockquote-reverse small:after,
blockquote.pull-right small:after,
.blockquote-reverse .small:after,
blockquote.pull-right .small:after {
  content: '\00A0 \2014';
}
address {
  margin-bottom: 20px;
  font-style: normal;
  line-height: 1.42857143;
}
code,
kbd,
pre,
samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 4px;
}
kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #ffffff;
  background-color: #333333;
  border-radius: 3px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: bold;
  box-shadow: none;
}
pre {
  display: block;
  padding: 9.5px;
  margin: 0 0 10px;
  font-size: 13px;
  line-height: 1.42857143;
  word-break: break-all;
  word-wrap: break-word;
  color: #333333;
  background-color: #f5f5f5;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1170px;
  }
}
.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.row {
  margin-left: -15px;
  margin-right: -15px;
}
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
}
.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
  float: left;
}
.col-xs-12 {
  width: 100%;
}
.col-xs-11 {
  width: 91.66666667%;
}
.col-xs-10 {
  width: 83.33333333%;
}
.col-xs-9 {
  width: 75%;
}
.col-xs-8 {
  width: 66.66666667%;
}
.col-xs-7 {
  width: 58.33333333%;
}
.col-xs-6 {
  width: 50%;
}
.col-xs-5 {
  width: 41.66666667%;
}
.col-xs-4 {
  width: 33.33333333%;
}
.col-xs-3 {
  width: 25%;
}
.col-xs-2 {
  width: 16.66666667%;
}
.col-xs-1 {
  width: 8.33333333%;
}
.col-xs-pull-12 {
  right: 100%;
}
.col-xs-pull-11 {
  right: 91.66666667%;
}
.col-xs-pull-10 {
  right: 83.33333333%;
}
.col-xs-pull-9 {
  right: 75%;
}
.col-xs-pull-8 {
  right: 66.66666667%;
}
.col-xs-pull-7 {
  right: 58.33333333%;
}
.col-xs-pull-6 {
  right: 50%;
}
.col-xs-pull-5 {
  right: 41.66666667%;
}
.col-xs-pull-4 {
  right: 33.33333333%;
}
.col-xs-pull-3 {
  right: 25%;
}
.col-xs-pull-2 {
  right: 16.66666667%;
}
.col-xs-pull-1 {
  right: 8.33333333%;
}
.col-xs-pull-0 {
  right: auto;
}
.col-xs-push-12 {
  left: 100%;
}
.col-xs-push-11 {
  left: 91.66666667%;
}
.col-xs-push-10 {
  left: 83.33333333%;
}
.col-xs-push-9 {
  left: 75%;
}
.col-xs-push-8 {
  left: 66.66666667%;
}
.col-xs-push-7 {
  left: 58.33333333%;
}
.col-xs-push-6 {
  left: 50%;
}
.col-xs-push-5 {
  left: 41.66666667%;
}
.col-xs-push-4 {
  left: 33.33333333%;
}
.col-xs-push-3 {
  left: 25%;
}
.col-xs-push-2 {
  left: 16.66666667%;
}
.col-xs-push-1 {
  left: 8.33333333%;
}
.col-xs-push-0 {
  left: auto;
}
.col-xs-offset-12 {
  margin-left: 100%;
}
.col-xs-offset-11 {
  margin-left: 91.66666667%;
}
.col-xs-offset-10 {
  margin-left: 83.33333333%;
}
.col-xs-offset-9 {
  margin-left: 75%;
}
.col-xs-offset-8 {
  margin-left: 66.66666667%;
}
.col-xs-offset-7 {
  margin-left: 58.33333333%;
}
.col-xs-offset-6 {
  margin-left: 50%;
}
.col-xs-offset-5 {
  margin-left: 41.66666667%;
}
.col-xs-offset-4 {
  margin-left: 33.33333333%;
}
.col-xs-offset-3 {
  margin-left: 25%;
}
.col-xs-offset-2 {
  margin-left: 16.66666667%;
}
.col-xs-offset-1 {
  margin-left: 8.33333333%;
}
.col-xs-offset-0 {
  margin-left: 0%;
}
@media (min-width: 768px) {
  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
    float: left;
  }
  .col-sm-12 {
    width: 100%;
  }
  .col-sm-11 {
    width: 91.66666667%;
  }
  .col-sm-10 {
    width: 83.33333333%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-8 {
    width: 66.66666667%;
  }
  .col-sm-7 {
    width: 58.33333333%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-5 {
    width: 41.66666667%;
  }
  .col-sm-4 {
    width: 33.33333333%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-2 {
    width: 16.66666667%;
  }
  .col-sm-1 {
    width: 8.33333333%;
  }
  .col-sm-pull-12 {
    right: 100%;
  }
  .col-sm-pull-11 {
    right: 91.66666667%;
  }
  .col-sm-pull-10 {
    right: 83.33333333%;
  }
  .col-sm-pull-9 {
    right: 75%;
  }
  .col-sm-pull-8 {
    right: 66.66666667%;
  }
  .col-sm-pull-7 {
    right: 58.33333333%;
  }
  .col-sm-pull-6 {
    right: 50%;
  }
  .col-sm-pull-5 {
    right: 41.66666667%;
  }
  .col-sm-pull-4 {
    right: 33.33333333%;
  }
  .col-sm-pull-3 {
    right: 25%;
  }
  .col-sm-pull-2 {
    right: 16.66666667%;
  }
  .col-sm-pull-1 {
    right: 8.33333333%;
  }
  .col-sm-pull-0 {
    right: auto;
  }
  .col-sm-push-12 {
    left: 100%;
  }
  .col-sm-push-11 {
    left: 91.66666667%;
  }
  .col-sm-push-10 {
    left: 83.33333333%;
  }
  .col-sm-push-9 {
    left: 75%;
  }
  .col-sm-push-8 {
    left: 66.66666667%;
  }
  .col-sm-push-7 {
    left: 58.33333333%;
  }
  .col-sm-push-6 {
    left: 50%;
  }
  .col-sm-push-5 {
    left: 41.66666667%;
  }
  .col-sm-push-4 {
    left: 33.33333333%;
  }
  .col-sm-push-3 {
    left: 25%;
  }
  .col-sm-push-2 {
    left: 16.66666667%;
  }
  .col-sm-push-1 {
    left: 8.33333333%;
  }
  .col-sm-push-0 {
    left: auto;
  }
  .col-sm-offset-12 {
    margin-left: 100%;
  }
  .col-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-sm-offset-9 {
    margin-left: 75%;
  }
  .col-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-sm-offset-6 {
    margin-left: 50%;
  }
  .col-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-sm-offset-3 {
    margin-left: 25%;
  }
  .col-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-sm-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: left;
  }
  .col-md-12 {
    width: 100%;
  }
  .col-md-11 {
    width: 91.66666667%;
  }
  .col-md-10 {
    width: 83.33333333%;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-8 {
    width: 66.66666667%;
  }
  .col-md-7 {
    width: 58.33333333%;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-5 {
    width: 41.66666667%;
  }
  .col-md-4 {
    width: 33.33333333%;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-2 {
    width: 16.66666667%;
  }
  .col-md-1 {
    width: 8.33333333%;
  }
  .col-md-pull-12 {
    right: 100%;
  }
  .col-md-pull-11 {
    right: 91.66666667%;
  }
  .col-md-pull-10 {
    right: 83.33333333%;
  }
  .col-md-pull-9 {
    right: 75%;
  }
  .col-md-pull-8 {
    right: 66.66666667%;
  }
  .col-md-pull-7 {
    right: 58.33333333%;
  }
  .col-md-pull-6 {
    right: 50%;
  }
  .col-md-pull-5 {
    right: 41.66666667%;
  }
  .col-md-pull-4 {
    right: 33.33333333%;
  }
  .col-md-pull-3 {
    right: 25%;
  }
  .col-md-pull-2 {
    right: 16.66666667%;
  }
  .col-md-pull-1 {
    right: 8.33333333%;
  }
  .col-md-pull-0 {
    right: auto;
  }
  .col-md-push-12 {
    left: 100%;
  }
  .col-md-push-11 {
    left: 91.66666667%;
  }
  .col-md-push-10 {
    left: 83.33333333%;
  }
  .col-md-push-9 {
    left: 75%;
  }
  .col-md-push-8 {
    left: 66.66666667%;
  }
  .col-md-push-7 {
    left: 58.33333333%;
  }
  .col-md-push-6 {
    left: 50%;
  }
  .col-md-push-5 {
    left: 41.66666667%;
  }
  .col-md-push-4 {
    left: 33.33333333%;
  }
  .col-md-push-3 {
    left: 25%;
  }
  .col-md-push-2 {
    left: 16.66666667%;
  }
  .col-md-push-1 {
    left: 8.33333333%;
  }
  .col-md-push-0 {
    left: auto;
  }
  .col-md-offset-12 {
    margin-left: 100%;
  }
  .col-md-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-md-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-md-offset-9 {
    margin-left: 75%;
  }
  .col-md-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-md-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-md-offset-6 {
    margin-left: 50%;
  }
  .col-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-md-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-md-offset-3 {
    margin-left: 25%;
  }
  .col-md-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-md-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-md-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 1200px) {
  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    float: left;
  }
  .col-lg-12 {
    width: 100%;
  }
  .col-lg-11 {
    width: 91.66666667%;
  }
  .col-lg-10 {
    width: 83.33333333%;
  }
  .col-lg-9 {
    width: 75%;
  }
  .col-lg-8 {
    width: 66.66666667%;
  }
  .col-lg-7 {
    width: 58.33333333%;
  }
  .col-lg-6 {
    width: 50%;
  }
  .col-lg-5 {
    width: 41.66666667%;
  }
  .col-lg-4 {
    width: 33.33333333%;
  }
  .col-lg-3 {
    width: 25%;
  }
  .col-lg-2 {
    width: 16.66666667%;
  }
  .col-lg-1 {
    width: 8.33333333%;
  }
  .col-lg-pull-12 {
    right: 100%;
  }
  .col-lg-pull-11 {
    right: 91.66666667%;
  }
  .col-lg-pull-10 {
    right: 83.33333333%;
  }
  .col-lg-pull-9 {
    right: 75%;
  }
  .col-lg-pull-8 {
    right: 66.66666667%;
  }
  .col-lg-pull-7 {
    right: 58.33333333%;
  }
  .col-lg-pull-6 {
    right: 50%;
  }
  .col-lg-pull-5 {
    right: 41.66666667%;
  }
  .col-lg-pull-4 {
    right: 33.33333333%;
  }
  .col-lg-pull-3 {
    right: 25%;
  }
  .col-lg-pull-2 {
    right: 16.66666667%;
  }
  .col-lg-pull-1 {
    right: 8.33333333%;
  }
  .col-lg-pull-0 {
    right: auto;
  }
  .col-lg-push-12 {
    left: 100%;
  }
  .col-lg-push-11 {
    left: 91.66666667%;
  }
  .col-lg-push-10 {
    left: 83.33333333%;
  }
  .col-lg-push-9 {
    left: 75%;
  }
  .col-lg-push-8 {
    left: 66.66666667%;
  }
  .col-lg-push-7 {
    left: 58.33333333%;
  }
  .col-lg-push-6 {
    left: 50%;
  }
  .col-lg-push-5 {
    left: 41.66666667%;
  }
  .col-lg-push-4 {
    left: 33.33333333%;
  }
  .col-lg-push-3 {
    left: 25%;
  }
  .col-lg-push-2 {
    left: 16.66666667%;
  }
  .col-lg-push-1 {
    left: 8.33333333%;
  }
  .col-lg-push-0 {
    left: auto;
  }
  .col-lg-offset-12 {
    margin-left: 100%;
  }
  .col-lg-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-lg-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-lg-offset-9 {
    margin-left: 75%;
  }
  .col-lg-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-lg-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-lg-offset-6 {
    margin-left: 50%;
  }
  .col-lg-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-lg-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-lg-offset-3 {
    margin-left: 25%;
  }
  .col-lg-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-lg-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-lg-offset-0 {
    margin-left: 0%;
  }
}
table {
  background-color: transparent;
}
caption {
  padding-top: 12px;
  padding-bottom: 12px;
  color: #777777;
  text-align: left;
}
th {
  text-align: left;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 12px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #f0f0f0;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #f0f0f0;
}
.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > th,
.table > caption + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > td,
.table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 2px solid #f0f0f0;
}
.table .table {
  background-color: #ffffff;
}
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > td {
  padding: 10px;
}
.table-bordered {
  border: 1px solid #f0f0f0;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #f0f0f0;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #fdfdfd;
}
.table-hover > tbody > tr:hover {
  background-color: #fdfdfd;
}
table col[class*="col-"] {
  position: static;
  float: none;
  display: table-column;
}
table td[class*="col-"],
table th[class*="col-"] {
  position: static;
  float: none;
  display: table-cell;
}
.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
  background-color: #fdfdfd;
}
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover,
.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr:hover > .active,
.table-hover > tbody > tr.active:hover > th {
  background-color: #f0f0f0;
}
.table > thead > tr > td.success,
.table > tbody > tr > td.success,
.table > tfoot > tr > td.success,
.table > thead > tr > th.success,
.table > tbody > tr > th.success,
.table > tfoot > tr > th.success,
.table > thead > tr.success > td,
.table > tbody > tr.success > td,
.table > tfoot > tr.success > td,
.table > thead > tr.success > th,
.table > tbody > tr.success > th,
.table > tfoot > tr.success > th {
  background-color: #dff0d8;
}
.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover,
.table-hover > tbody > tr.success:hover > td,
.table-hover > tbody > tr:hover > .success,
.table-hover > tbody > tr.success:hover > th {
  background-color: #d0e9c6;
}
.table > thead > tr > td.info,
.table > tbody > tr > td.info,
.table > tfoot > tr > td.info,
.table > thead > tr > th.info,
.table > tbody > tr > th.info,
.table > tfoot > tr > th.info,
.table > thead > tr.info > td,
.table > tbody > tr.info > td,
.table > tfoot > tr.info > td,
.table > thead > tr.info > th,
.table > tbody > tr.info > th,
.table > tfoot > tr.info > th {
  background-color: #d9edf7;
}
.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover,
.table-hover > tbody > tr.info:hover > td,
.table-hover > tbody > tr:hover > .info,
.table-hover > tbody > tr.info:hover > th {
  background-color: #c4e3f3;
}
.table > thead > tr > td.warning,
.table > tbody > tr > td.warning,
.table > tfoot > tr > td.warning,
.table > thead > tr > th.warning,
.table > tbody > tr > th.warning,
.table > tfoot > tr > th.warning,
.table > thead > tr.warning > td,
.table > tbody > tr.warning > td,
.table > tfoot > tr.warning > td,
.table > thead > tr.warning > th,
.table > tbody > tr.warning > th,
.table > tfoot > tr.warning > th {
  background-color: #fcf8e3;
}
.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover,
.table-hover > tbody > tr.warning:hover > td,
.table-hover > tbody > tr:hover > .warning,
.table-hover > tbody > tr.warning:hover > th {
  background-color: #faf2cc;
}
.table > thead > tr > td.danger,
.table > tbody > tr > td.danger,
.table > tfoot > tr > td.danger,
.table > thead > tr > th.danger,
.table > tbody > tr > th.danger,
.table > tfoot > tr > th.danger,
.table > thead > tr.danger > td,
.table > tbody > tr.danger > td,
.table > tfoot > tr.danger > td,
.table > thead > tr.danger > th,
.table > tbody > tr.danger > th,
.table > tfoot > tr.danger > th {
  background-color: #f2dede;
}
.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover,
.table-hover > tbody > tr.danger:hover > td,
.table-hover > tbody > tr:hover > .danger,
.table-hover > tbody > tr.danger:hover > th {
  background-color: #ebcccc;
}
.table-responsive {
  overflow-x: auto;
  min-height: 0.01%;
}
@media screen and (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #f0f0f0;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 20px;
  font-size: 21px;
  line-height: inherit;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: bold;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  line-height: normal;
}
input[type="file"] {
  display: block;
}
input[type="range"] {
  display: block;
  width: 100%;
}
select[multiple],
select[size] {
  height: auto;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
output {
  display: block;
  padding-top: 7px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #2b2b2b;
}
.form-control {
  display: block;
  width: 100%;
  height: 34px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #2b2b2b;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #cccccc;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -webkit-transition:  border-color ease-in-out .15s, box-shadow ease-in-out .15s 1s 0s ease;
  -moz-transition:  border-color ease-in-out .15s, box-shadow ease-in-out .15s 1s 0s ease;
  -ms-transition:  border-color ease-in-out .15s, box-shadow ease-in-out .15s 1s 0s ease;
  -o-transition:  border-color ease-in-out .15s, box-shadow ease-in-out .15s 1s 0s ease;
  transition:  border-color ease-in-out .15s, box-shadow ease-in-out .15s 1s 0s ease;
  font-size: 1em;
  line-height: 1.214em;
}
.form-control:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.form-control::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: #999999;
}
.form-control::-webkit-input-placeholder {
  color: #999999;
}
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  background-color: #eeeeee;
  opacity: 1;
}
.form-control[disabled],
fieldset[disabled] .form-control {
  cursor: not-allowed;
}
textarea.form-control {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: none;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"],
  input[type="time"],
  input[type="datetime-local"],
  input[type="month"] {
    line-height: 34px;
  }
  input[type="date"].input-sm,
  input[type="time"].input-sm,
  input[type="datetime-local"].input-sm,
  input[type="month"].input-sm,
  .input-group-sm input[type="date"],
  .input-group-sm input[type="time"],
  .input-group-sm input[type="datetime-local"],
  .input-group-sm input[type="month"] {
    line-height: 30px;
  }
  input[type="date"].input-lg,
  input[type="time"].input-lg,
  input[type="datetime-local"].input-lg,
  input[type="month"].input-lg,
  .input-group-lg input[type="date"],
  .input-group-lg input[type="time"],
  .input-group-lg input[type="datetime-local"],
  .input-group-lg input[type="month"] {
    line-height: 46px;
  }
}
.form-group {
  margin-bottom: 15px;
}
.radio,
.checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.radio label,
.checkbox label {
  min-height: 20px;
  padding-left: 20px;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}
.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-left: -20px;
  margin-top: 4px \9;
}
.radio + .radio,
.checkbox + .checkbox {
  margin-top: -5px;
}
.radio-inline,
.checkbox-inline {
  position: relative;
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer;
}
.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
input[type="radio"][disabled],
input[type="checkbox"][disabled],
input[type="radio"].disabled,
input[type="checkbox"].disabled,
fieldset[disabled] input[type="radio"],
fieldset[disabled] input[type="checkbox"] {
  cursor: not-allowed;
}
.radio-inline.disabled,
.checkbox-inline.disabled,
fieldset[disabled] .radio-inline,
fieldset[disabled] .checkbox-inline {
  cursor: not-allowed;
}
.radio.disabled label,
.checkbox.disabled label,
fieldset[disabled] .radio label,
fieldset[disabled] .checkbox label {
  cursor: not-allowed;
}
.form-control-static {
  padding-top: 7px;
  padding-bottom: 7px;
  margin-bottom: 0;
  min-height: 34px;
}
.form-control-static.input-lg,
.form-control-static.input-sm {
  padding-left: 0;
  padding-right: 0;
}
.input-sm {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.input-sm {
  height: 30px;
  line-height: 30px;
}
textarea.input-sm,
select[multiple].input-sm {
  height: auto;
}
.form-group-sm .form-control {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.form-group-sm .form-control {
  height: 30px;
  line-height: 30px;
}
textarea.form-group-sm .form-control,
select[multiple].form-group-sm .form-control {
  height: auto;
}
.form-group-sm .form-control-static {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  min-height: 32px;
}
.input-lg {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.input-lg {
  height: 46px;
  line-height: 46px;
}
textarea.input-lg,
select[multiple].input-lg {
  height: auto;
}
.form-group-lg .form-control {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.form-group-lg .form-control {
  height: 46px;
  line-height: 46px;
}
textarea.form-group-lg .form-control,
select[multiple].form-group-lg .form-control {
  height: auto;
}
.form-group-lg .form-control-static {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  min-height: 38px;
}
.has-feedback {
  position: relative;
}
.has-feedback .form-control {
  padding-right: 42.5px;
}
.form-control-feedback {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  display: block;
  width: 34px;
  height: 34px;
  line-height: 34px;
  text-align: center;
  pointer-events: none;
}
.input-lg + .form-control-feedback {
  width: 46px;
  height: 46px;
  line-height: 46px;
}
.input-sm + .form-control-feedback {
  width: 30px;
  height: 30px;
  line-height: 30px;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline,
.has-success.radio label,
.has-success.checkbox label,
.has-success.radio-inline label,
.has-success.checkbox-inline label {
  color: #3c763d;
}
.has-success .form-control {
  border-color: #3c763d;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-success .form-control:focus {
  border-color: #2b542c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}
.has-success .input-group-addon {
  color: #3c763d;
  border-color: #3c763d;
  background-color: #dff0d8;
}
.has-success .form-control-feedback {
  color: #3c763d;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline,
.has-warning.radio label,
.has-warning.checkbox label,
.has-warning.radio-inline label,
.has-warning.checkbox-inline label {
  color: #8a6d3b;
}
.has-warning .form-control {
  border-color: #8a6d3b;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-warning .form-control:focus {
  border-color: #66512c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}
.has-warning .input-group-addon {
  color: #8a6d3b;
  border-color: #8a6d3b;
  background-color: #fcf8e3;
}
.has-warning .form-control-feedback {
  color: #8a6d3b;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline,
.has-error.radio label,
.has-error.checkbox label,
.has-error.radio-inline label,
.has-error.checkbox-inline label {
  color: #a94442;
}
.has-error .form-control {
  border-color: #a94442;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-error .form-control:focus {
  border-color: #843534;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
.has-error .input-group-addon {
  color: #a94442;
  border-color: #a94442;
  background-color: #f2dede;
}
.has-error .form-control-feedback {
  color: #a94442;
}
.has-feedback label ~ .form-control-feedback {
  top: 25px;
}
.has-feedback label.sr-only ~ .form-control-feedback {
  top: 0;
}
.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #737373;
}
@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-static {
    display: inline-block;
  }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .form-inline .input-group .input-group-addon,
  .form-inline .input-group .input-group-btn,
  .form-inline .input-group .form-control {
    width: auto;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio label,
  .form-inline .checkbox label {
    padding-left: 0;
  }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px;
}
.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 27px;
}
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px;
}
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
    margin-bottom: 0;
    padding-top: 7px;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  right: 15px;
}
@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 14.333333px;
  }
}
@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 6px;
  }
}
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  border-radius: 4px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn:hover,
.btn:focus,
.btn.focus {
  color: #333333;
  text-decoration: none;
}
.btn:active,
.btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-default {
  color: #333333;
  background-color: #ffffff;
  border-color: #cccccc;
}
.btn-default:hover,
.btn-default:focus,
.btn-default.focus,
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  color: #333333;
  background-color: #e6e6e6;
  border-color: #adadad;
}
.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  background-image: none;
}
.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #ffffff;
  border-color: #cccccc;
}
.btn-default .badge {
  color: #ffffff;
  background-color: #333333;
}
.btn-primary {
  color: #ffffff;
  background-color: #337ab7;
  border-color: #2e6da4;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary.focus,
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #ffffff;
  background-color: #286090;
  border-color: #204d74;
}
.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  background-image: none;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #337ab7;
  border-color: #2e6da4;
}
.btn-primary .badge {
  color: #337ab7;
  background-color: #ffffff;
}
.btn-success {
  color: #ffffff;
  background-color: #84cd81;
  border-color: #72c66f;
}
.btn-success:hover,
.btn-success:focus,
.btn-success.focus,
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success {
  color: #ffffff;
  background-color: #60bf5c;
  border-color: #4ab146;
}
.btn-success:active,
.btn-success.active,
.open > .dropdown-toggle.btn-success {
  background-image: none;
}
.btn-success.disabled,
.btn-success[disabled],
fieldset[disabled] .btn-success,
.btn-success.disabled:hover,
.btn-success[disabled]:hover,
fieldset[disabled] .btn-success:hover,
.btn-success.disabled:focus,
.btn-success[disabled]:focus,
fieldset[disabled] .btn-success:focus,
.btn-success.disabled.focus,
.btn-success[disabled].focus,
fieldset[disabled] .btn-success.focus,
.btn-success.disabled:active,
.btn-success[disabled]:active,
fieldset[disabled] .btn-success:active,
.btn-success.disabled.active,
.btn-success[disabled].active,
fieldset[disabled] .btn-success.active {
  background-color: #84cd81;
  border-color: #72c66f;
}
.btn-success .badge {
  color: #84cd81;
  background-color: #ffffff;
}
.btn-info {
  color: #ffffff;
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info:hover,
.btn-info:focus,
.btn-info.focus,
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  color: #ffffff;
  background-color: #31b0d5;
  border-color: #269abc;
}
.btn-info:active,
.btn-info.active,
.open > .dropdown-toggle.btn-info {
  background-image: none;
}
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled.focus,
.btn-info[disabled].focus,
fieldset[disabled] .btn-info.focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info .badge {
  color: #5bc0de;
  background-color: #ffffff;
}
.btn-warning {
  color: #ffffff;
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning.focus,
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  color: #ffffff;
  background-color: #ec971f;
  border-color: #d58512;
}
.btn-warning:active,
.btn-warning.active,
.open > .dropdown-toggle.btn-warning {
  background-image: none;
}
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled.focus,
.btn-warning[disabled].focus,
fieldset[disabled] .btn-warning.focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning .badge {
  color: #f0ad4e;
  background-color: #ffffff;
}
.btn-danger {
  color: #ffffff;
  background-color: #d9534f;
  border-color: #d43f3a;
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger.focus,
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  color: #ffffff;
  background-color: #c9302c;
  border-color: #ac2925;
}
.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  background-image: none;
}
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
  background-color: #d9534f;
  border-color: #d43f3a;
}
.btn-danger .badge {
  color: #d9534f;
  background-color: #ffffff;
}
.btn-link {
  color: #212121;
  font-weight: normal;
  border-radius: 0;
}
.btn-link,
.btn-link:active,
.btn-link.active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
  border-color: transparent;
}
.btn-link:hover,
.btn-link:focus {
  color: #000000;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
  color: #777777;
  text-decoration: none;
}
.btn-lg,
.btn-group-lg > .btn {
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
.btn-sm,
.btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-xs,
.btn-group-xs > .btn {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}
.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
  -webkit-transition:  opacity 1s 0s ease, 0.15s 1s 0s ease, linear 1s 0s ease;
  -moz-transition:  opacity 1s 0s ease, 0.15s 1s 0s ease, linear 1s 0s ease;
  -ms-transition:  opacity 1s 0s ease, 0.15s 1s 0s ease, linear 1s 0s ease;
  -o-transition:  opacity 1s 0s ease, 0.15s 1s 0s ease, linear 1s 0s ease;
  transition:  opacity 1s 0s ease, 0.15s 1s 0s ease, linear 1s 0s ease;
}
.fade.in {
  opacity: 1;
}
.collapse {
  display: none;
}
.collapse.in {
  display: block;
}
tr.collapse.in {
  display: table-row;
}
tbody.collapse.in {
  display: table-row-group;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
}
.dropup,
.dropdown {
  position: relative;
}
.dropdown-toggle:focus {
  outline: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 14px;
  text-align: left;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
}
.dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menu .divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333333;
  white-space: nowrap;
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  color: #ffffff;
  text-decoration: none;
  outline: 0;
  background-color: #337ab7;
}
.dropdown-menu > .disabled > a,
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  color: #777777;
}
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.open > .dropdown-menu {
  display: block;
}
.open > a {
  outline: 0;
}
.dropdown-menu-right {
  left: auto;
  right: 0;
}
.dropdown-menu-left {
  left: 0;
  right: auto;
}
.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.42857143;
  color: #777777;
  white-space: nowrap;
}
.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px solid;
  content: "";
}
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}
@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    left: auto;
    right: 0;
  }
  .navbar-right .dropdown-menu-left {
    left: 0;
    right: auto;
  }
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  float: left;
}
.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover,
.btn-group > .btn:focus,
.btn-group-vertical > .btn:focus,
.btn-group > .btn:active,
.btn-group-vertical > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn.active {
  z-index: 2;
}
.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px;
}
.btn-toolbar {
  margin-left: -5px;
}
.btn-toolbar .btn-group,
.btn-toolbar .input-group {
  float: left;
}
.btn-toolbar > .btn,
.btn-toolbar > .btn-group,
.btn-toolbar > .input-group {
  margin-left: 5px;
}
.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.btn-group > .btn:first-child {
  margin-left: 0;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group > .btn-group {
  float: left;
}
.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px;
}
.btn-group > .btn-lg + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
}
.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-group.open .dropdown-toggle.btn-link {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn .caret {
  margin-left: 0;
}
.btn-lg .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.dropup .btn-lg .caret {
  border-width: 0 5px 5px;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group,
.btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.btn-group-vertical > .btn-group > .btn {
  float: none;
}
.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.btn-group-justified > .btn,
.btn-group-justified > .btn-group {
  float: none;
  display: table-cell;
  width: 1%;
}
.btn-group-justified > .btn-group .btn {
  width: 100%;
}
.btn-group-justified > .btn-group .dropdown-menu {
  left: auto;
}
[data-toggle="buttons"] > .btn input[type="radio"],
[data-toggle="buttons"] > .btn-group > .btn input[type="radio"],
[data-toggle="buttons"] > .btn input[type="checkbox"],
[data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.input-group[class*="col-"] {
  float: none;
  padding-left: 0;
  padding-right: 0;
}
.input-group .form-control {
  position: relative;
  z-index: 2;
  float: left;
  width: 100%;
  margin-bottom: 0;
}
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  height: 46px;
  padding: 10px 16px;
  font-size: 18px;
  line-height: 1.3333333;
  border-radius: 6px;
}
select.input-group-lg > .form-control,
select.input-group-lg > .input-group-addon,
select.input-group-lg > .input-group-btn > .btn {
  height: 46px;
  line-height: 46px;
}
textarea.input-group-lg > .form-control,
textarea.input-group-lg > .input-group-addon,
textarea.input-group-lg > .input-group-btn > .btn,
select[multiple].input-group-lg > .form-control,
select[multiple].input-group-lg > .input-group-addon,
select[multiple].input-group-lg > .input-group-btn > .btn {
  height: auto;
}
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  height: 30px;
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 3px;
}
select.input-group-sm > .form-control,
select.input-group-sm > .input-group-addon,
select.input-group-sm > .input-group-btn > .btn {
  height: 30px;
  line-height: 30px;
}
textarea.input-group-sm > .form-control,
textarea.input-group-sm > .input-group-addon,
textarea.input-group-sm > .input-group-btn > .btn,
select[multiple].input-group-sm > .form-control,
select[multiple].input-group-sm > .input-group-addon,
select[multiple].input-group-sm > .input-group-btn > .btn {
  height: auto;
}
.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell;
}
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.input-group-addon {
  padding: 6px 12px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #2b2b2b;
  text-align: center;
  background-color: #eeeeee;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
.input-group-addon.input-sm {
  padding: 5px 10px;
  font-size: 12px;
  border-radius: 3px;
}
.input-group-addon.input-lg {
  padding: 10px 16px;
  font-size: 18px;
  border-radius: 6px;
}
.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
  margin-top: 0;
}
.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.input-group-addon:first-child {
  border-right: 0;
}
.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child),
.input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.input-group-addon:last-child {
  border-left: 0;
}
.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.input-group-btn > .btn {
  position: relative;
}
.input-group-btn > .btn + .btn {
  margin-left: -1px;
}
.input-group-btn > .btn:hover,
.input-group-btn > .btn:focus,
.input-group-btn > .btn:active {
  z-index: 2;
}
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .btn-group {
  margin-right: -1px;
}
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .btn-group {
  margin-left: -1px;
}
.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.nav > li {
  position: relative;
  display: block;
}
.nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}
.nav > li > a:hover,
.nav > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.nav > li.disabled > a {
  color: #777777;
}
.nav > li.disabled > a:hover,
.nav > li.disabled > a:focus {
  color: #777777;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  background-color: #eeeeee;
  border-color: #212121;
}
.nav .nav-divider {
  height: 1px;
  margin: 9px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav > li > a > img {
  max-width: none;
}
.nav-tabs {
  border-bottom: 1px solid #dddddd;
}
.nav-tabs > li {
  float: left;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
  margin-right: 2px;
  line-height: 1.42857143;
  border: 1px solid transparent;
  border-radius: 4px 4px 0 0;
}
.nav-tabs > li > a:hover {
  border-color: #e8e8e8 #e8e8e8 #dddddd;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  color: #555555;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-bottom-color: transparent;
  cursor: default;
}
.nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}
.nav-tabs.nav-justified > li {
  float: none;
}
.nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a:focus {
  border: 1px solid #dddddd;
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #dddddd;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #ffffff;
  }
}
.nav-pills > li {
  float: left;
}
.nav-pills > li > a {
  border-radius: 4px;
}
.nav-pills > li + li {
  margin-left: 2px;
}
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
  color: #ffffff;
  background-color: #337ab7;
}
.nav-stacked > li {
  float: none;
}
.nav-stacked > li + li {
  margin-top: 2px;
  margin-left: 0;
}
.nav-justified {
  width: 100%;
}
.nav-justified > li {
  float: none;
}
.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs-justified {
  border-bottom: 0;
}
.nav-tabs-justified > li > a {
  margin-right: 0;
  border-radius: 4px;
}
.nav-tabs-justified > .active > a,
.nav-tabs-justified > .active > a:hover,
.nav-tabs-justified > .active > a:focus {
  border: 1px solid #dddddd;
}
@media (min-width: 768px) {
  .nav-tabs-justified > li > a {
    border-bottom: 1px solid #dddddd;
    border-radius: 4px 4px 0 0;
  }
  .nav-tabs-justified > .active > a,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs-justified > .active > a:focus {
    border-bottom-color: #ffffff;
  }
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar {
  position: relative;
  min-height: 50px;
  margin-bottom: 20px;
  border: 1px solid transparent;
}
@media (min-width: 768px) {
  .navbar {
    border-radius: 4px;
  }
}
@media (min-width: 768px) {
  .navbar-header {
    float: left;
  }
}
.navbar-collapse {
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
}
.navbar-collapse.in {
  overflow-y: auto;
}
@media (min-width: 768px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    box-shadow: none;
  }
  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-collapse.in {
    overflow-y: visible;
  }
  .navbar-fixed-top .navbar-collapse,
  .navbar-static-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    padding-left: 0;
    padding-right: 0;
  }
}
.navbar-fixed-top .navbar-collapse,
.navbar-fixed-bottom .navbar-collapse {
  max-height: 340px;
}
@media (max-device-width: 480px) and (orientation: landscape) {
  .navbar-fixed-top .navbar-collapse,
  .navbar-fixed-bottom .navbar-collapse {
    max-height: 200px;
  }
}
.container > .navbar-header,
.container-fluid > .navbar-header,
.container > .navbar-collapse,
.container-fluid > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .container > .navbar-header,
  .container-fluid > .navbar-header,
  .container > .navbar-collapse,
  .container-fluid > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px;
}
@media (min-width: 768px) {
  .navbar-static-top {
    border-radius: 0;
  }
}
.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}
@media (min-width: 768px) {
  .navbar-fixed-top,
  .navbar-fixed-bottom {
    border-radius: 0;
  }
}
.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px;
}
.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0;
}
.navbar-brand {
  float: left;
  padding: 15px 15px;
  font-size: 18px;
  line-height: 20px;
  height: 50px;
}
.navbar-brand:hover,
.navbar-brand:focus {
  text-decoration: none;
}
.navbar-brand > img {
  display: block;
}
@media (min-width: 768px) {
  .navbar > .container .navbar-brand,
  .navbar > .container-fluid .navbar-brand {
    margin-left: -15px;
  }
}
.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}
.navbar-toggle:focus {
  outline: 0;
}
.navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.navbar-toggle .icon-bar + .icon-bar {
  margin-top: 4px;
}
@media (min-width: 768px) {
  .navbar-toggle {
    display: none;
  }
}
.navbar-nav {
  margin: 7.5px -15px;
}
.navbar-nav > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 20px;
}
@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }
  .navbar-nav .open .dropdown-menu > li > a,
  .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 15px 5px 25px;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 20px;
  }
  .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-nav .open .dropdown-menu > li > a:focus {
    background-image: none;
  }
}
@media (min-width: 768px) {
  .navbar-nav {
    float: left;
    margin: 0;
  }
  .navbar-nav > li {
    float: left;
  }
  .navbar-nav > li > a {
    padding-top: 15px;
    padding-bottom: 15px;
  }
}
.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 8px;
  margin-bottom: 8px;
}
@media (min-width: 768px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .navbar-form .form-control-static {
    display: inline-block;
  }
  .navbar-form .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .navbar-form .input-group .input-group-addon,
  .navbar-form .input-group .input-group-btn,
  .navbar-form .input-group .form-control {
    width: auto;
  }
  .navbar-form .input-group > .form-control {
    width: 100%;
  }
  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio,
  .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio label,
  .navbar-form .checkbox label {
    padding-left: 0;
  }
  .navbar-form .radio input[type="radio"],
  .navbar-form .checkbox input[type="checkbox"] {
    position: relative;
    margin-left: 0;
  }
  .navbar-form .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (max-width: 767px) {
  .navbar-form .form-group {
    margin-bottom: 5px;
  }
  .navbar-form .form-group:last-child {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  .navbar-form {
    width: auto;
    border: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 0;
    padding-bottom: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}
.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  margin-bottom: 0;
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.navbar-btn {
  margin-top: 8px;
  margin-bottom: 8px;
}
.navbar-btn.btn-sm {
  margin-top: 10px;
  margin-bottom: 10px;
}
.navbar-btn.btn-xs {
  margin-top: 14px;
  margin-bottom: 14px;
}
.navbar-text {
  margin-top: 15px;
  margin-bottom: 15px;
}
@media (min-width: 768px) {
  .navbar-text {
    float: left;
    margin-left: 15px;
    margin-right: 15px;
  }
}
@media (min-width: 768px) {
  .navbar-left {
    float: left !important;
  }
  .navbar-right {
    float: right !important;
    margin-right: -15px;
  }
  .navbar-right ~ .navbar-right {
    margin-right: 0;
  }
}
.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7;
}
.navbar-default .navbar-brand {
  color: #777777;
}
.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
  color: #5e5e5e;
  background-color: transparent;
}
.navbar-default .navbar-text {
  color: #777777;
}
.navbar-default .navbar-nav > li > a {
  color: #777777;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
  color: #333333;
  background-color: transparent;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: #555555;
  background-color: #e7e7e7;
}
.navbar-default .navbar-nav > .disabled > a,
.navbar-default .navbar-nav > .disabled > a:hover,
.navbar-default .navbar-nav > .disabled > a:focus {
  color: #cccccc;
  background-color: transparent;
}
.navbar-default .navbar-toggle {
  border-color: #dddddd;
}
.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
  background-color: #dddddd;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #888888;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #e7e7e7;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  background-color: #e7e7e7;
  color: #555555;
}
@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #777777;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #333333;
    background-color: transparent;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #555555;
    background-color: #e7e7e7;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
  }
}
.navbar-default .navbar-link {
  color: #777777;
}
.navbar-default .navbar-link:hover {
  color: #333333;
}
.navbar-default .btn-link {
  color: #777777;
}
.navbar-default .btn-link:hover,
.navbar-default .btn-link:focus {
  color: #333333;
}
.navbar-default .btn-link[disabled]:hover,
fieldset[disabled] .navbar-default .btn-link:hover,
.navbar-default .btn-link[disabled]:focus,
fieldset[disabled] .navbar-default .btn-link:focus {
  color: #cccccc;
}
.navbar-inverse {
  background-color: #222222;
  border-color: #080808;
}
.navbar-inverse .navbar-brand {
  color: #9d9d9d;
}
.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-brand:focus {
  color: #ffffff;
  background-color: transparent;
}
.navbar-inverse .navbar-text {
  color: #9d9d9d;
}
.navbar-inverse .navbar-nav > li > a {
  color: #9d9d9d;
}
.navbar-inverse .navbar-nav > li > a:hover,
.navbar-inverse .navbar-nav > li > a:focus {
  color: #ffffff;
  background-color: transparent;
}
.navbar-inverse .navbar-nav > .active > a,
.navbar-inverse .navbar-nav > .active > a:hover,
.navbar-inverse .navbar-nav > .active > a:focus {
  color: #ffffff;
  background-color: #080808;
}
.navbar-inverse .navbar-nav > .disabled > a,
.navbar-inverse .navbar-nav > .disabled > a:hover,
.navbar-inverse .navbar-nav > .disabled > a:focus {
  color: #444444;
  background-color: transparent;
}
.navbar-inverse .navbar-toggle {
  border-color: #333333;
}
.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
  background-color: #333333;
}
.navbar-inverse .navbar-toggle .icon-bar {
  background-color: #ffffff;
}
.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border-color: #101010;
}
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .open > a:hover,
.navbar-inverse .navbar-nav > .open > a:focus {
  background-color: #080808;
  color: #ffffff;
}
@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #9d9d9d;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #ffffff;
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #ffffff;
    background-color: #080808;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #444444;
    background-color: transparent;
  }
}
.navbar-inverse .navbar-link {
  color: #9d9d9d;
}
.navbar-inverse .navbar-link:hover {
  color: #ffffff;
}
.navbar-inverse .btn-link {
  color: #9d9d9d;
}
.navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link:focus {
  color: #ffffff;
}
.navbar-inverse .btn-link[disabled]:hover,
fieldset[disabled] .navbar-inverse .btn-link:hover,
.navbar-inverse .btn-link[disabled]:focus,
fieldset[disabled] .navbar-inverse .btn-link:focus {
  color: #444444;
}
.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 20px;
  list-style: none;
  background-color: #f4f4f4;
  border-radius: 4px;
}
.breadcrumb > li {
  display: inline-block;
}
.breadcrumb > li + li:before {
  content: "/\00a0";
  padding: 0 5px;
  color: #2b2b2b;
}
.breadcrumb > .active {
  color: #2b2b2b;
}
.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px;
}
.pagination > li {
  display: inline;
}
.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  padding: 6px 12px;
  line-height: 1.42857143;
  text-decoration: none;
  color: #212121;
  background-color: #ffffff;
  border: 1px solid #e3e3e3;
  margin-left: -1px;
}
.pagination > li:first-child > a,
.pagination > li:first-child > span {
  margin-left: 0;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;
}
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  color: #2b383e;
  background-color: #ededed;
  border-color: #e3e3e3;
}
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  z-index: 2;
  color: #ffffff;
  background-color: #2b383e;
  border-color: #e3e3e3;
  cursor: default;
}
.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
  color: #777777;
  background-color: #ffffff;
  border-color: #dddddd;
  cursor: not-allowed;
}
.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 10px 16px;
  font-size: 18px;
}
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
}
.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 12px;
}
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25em;
}
a.label:hover,
a.label:focus {
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
}
.label:empty {
  display: none;
}
.btn .label {
  position: relative;
  top: -1px;
}
.label-default {
  background-color: #777777;
}
.label-default[href]:hover,
.label-default[href]:focus {
  background-color: #5e5e5e;
}
.label-primary {
  background-color: #337ab7;
}
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #286090;
}
.label-success {
  background-color: #84cd81;
}
.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #60bf5c;
}
.label-info {
  background-color: #5bc0de;
}
.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #31b0d5;
}
.label-warning {
  background-color: #f0ad4e;
}
.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #ec971f;
}
.label-danger {
  background-color: #d9534f;
}
.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #c9302c;
}
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #777777;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.btn-xs .badge,
.btn-group-xs > .btn .badge {
  top: 0;
  padding: 1px 5px;
}
a.badge:hover,
a.badge:focus {
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
}
.list-group-item.active > .badge,
.nav-pills > .active > a > .badge {
  color: #212121;
  background-color: #ffffff;
}
.list-group-item > .badge {
  float: right;
}
.list-group-item > .badge + .badge {
  margin-right: 5px;
}
.nav-pills > li > a > .badge {
  margin-left: 3px;
}
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}
.alert h4 {
  margin-top: 0;
  color: inherit;
}
.alert .alert-link {
  font-weight: bold;
}
.alert > p,
.alert > ul {
  margin-bottom: 0;
}
.alert > p + p {
  margin-top: 5px;
}
.alert-dismissable,
.alert-dismissible {
  padding-right: 35px;
}
.alert-dismissable .close,
.alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}
.alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #3c763d;
}
.alert-success hr {
  border-top-color: #c9e2b3;
}
.alert-success .alert-link {
  color: #2b542c;
}
.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f;
}
.alert-info hr {
  border-top-color: #a6e1ec;
}
.alert-info .alert-link {
  color: #245269;
}
.alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #8a6d3b;
}
.alert-warning hr {
  border-top-color: #f7e1b5;
}
.alert-warning .alert-link {
  color: #66512c;
}
.alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #a94442;
}
.alert-danger hr {
  border-top-color: #e4b9c0;
}
.alert-danger .alert-link {
  color: #843534;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  overflow: hidden;
  height: 20px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 20px;
  color: #ffffff;
  text-align: center;
  background-color: #337ab7;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease;
  -webkit-transition:  width 1s 0s ease, 0.6s 1s 0s ease, ease 1s 0s ease;
  -moz-transition:  width 1s 0s ease, 0.6s 1s 0s ease, ease 1s 0s ease;
  -ms-transition:  width 1s 0s ease, 0.6s 1s 0s ease, ease 1s 0s ease;
  -o-transition:  width 1s 0s ease, 0.6s 1s 0s ease, ease 1s 0s ease;
  transition:  width 1s 0s ease, 0.6s 1s 0s ease, ease 1s 0s ease;
}
.progress-striped .progress-bar,
.progress-bar-striped {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.progress.active .progress-bar,
.progress-bar.active {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}
.progress-bar-success {
  background-color: #84cd81;
}
.progress-striped .progress-bar-success {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-info {
  background-color: #5bc0de;
}
.progress-striped .progress-bar-info {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-warning {
  background-color: #f0ad4e;
}
.progress-striped .progress-bar-warning {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-danger {
  background-color: #d9534f;
}
.progress-striped .progress-bar-danger {
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.panel {
  margin-bottom: 20px;
  background-color: #ffffff;
  border: 1px solid transparent;
  border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.panel-body {
  padding: 15px;
}
.panel-heading {
  padding: 10px 15px;
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 16px;
  color: inherit;
}
.panel-title > a,
.panel-title > small,
.panel-title > .small,
.panel-title > small > a,
.panel-title > .small > a {
  color: inherit;
}
.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #dddddd;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .list-group,
.panel > .panel-collapse > .list-group {
  margin-bottom: 0;
}
.panel > .list-group .list-group-item,
.panel > .panel-collapse > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.panel > .list-group:first-child .list-group-item:first-child,
.panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
  border-top: 0;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel > .list-group:last-child .list-group-item:last-child,
.panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
  border-bottom: 0;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.list-group + .panel-footer {
  border-top-width: 0;
}
.panel > .table,
.panel > .table-responsive > .table,
.panel > .panel-collapse > .table {
  margin-bottom: 0;
}
.panel > .table caption,
.panel > .table-responsive > .table caption,
.panel > .panel-collapse > .table caption {
  padding-left: 15px;
  padding-right: 15px;
}
.panel > .table:first-child,
.panel > .table-responsive:first-child > .table:first-child {
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-left-radius: 3px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-right-radius: 3px;
}
.panel > .table:last-child,
.panel > .table-responsive:last-child > .table:last-child {
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 3px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 3px;
}
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive,
.panel > .table + .panel-body,
.panel > .table-responsive + .panel-body {
  border-top: 1px solid #f0f0f0;
}
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0;
}
.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-left: 0;
}
.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-right: 0;
}
.panel > .table-bordered > thead > tr:first-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.panel > .table-bordered > tbody > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.panel > .table-bordered > thead > tr:first-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.panel > .table-bordered > tbody > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
  border-bottom: 0;
}
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
  border-bottom: 0;
}
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0;
}
.panel-group {
  margin-bottom: 20px;
}
.panel-group .panel {
  margin-bottom: 0;
  border-radius: 4px;
}
.panel-group .panel + .panel {
  margin-top: 5px;
}
.panel-group .panel-heading {
  border-bottom: 0;
}
.panel-group .panel-heading + .panel-collapse > .panel-body,
.panel-group .panel-heading + .panel-collapse > .list-group {
  border-top: 1px solid #dddddd;
}
.panel-group .panel-footer {
  border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #dddddd;
}
.panel-default {
  border-color: #dddddd;
}
.panel-default > .panel-heading {
  color: #333333;
  background-color: #f5f5f5;
  border-color: #dddddd;
}
.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #dddddd;
}
.panel-default > .panel-heading .badge {
  color: #f5f5f5;
  background-color: #333333;
}
.panel-default > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #dddddd;
}
.panel-primary {
  border-color: #337ab7;
}
.panel-primary > .panel-heading {
  color: #ffffff;
  background-color: #337ab7;
  border-color: #337ab7;
}
.panel-primary > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #337ab7;
}
.panel-primary > .panel-heading .badge {
  color: #337ab7;
  background-color: #ffffff;
}
.panel-primary > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #337ab7;
}
.panel-success {
  border-color: #d6e9c6;
}
.panel-success > .panel-heading {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.panel-success > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #d6e9c6;
}
.panel-success > .panel-heading .badge {
  color: #dff0d8;
  background-color: #3c763d;
}
.panel-success > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #d6e9c6;
}
.panel-info {
  border-color: #bce8f1;
}
.panel-info > .panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.panel-info > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #bce8f1;
}
.panel-info > .panel-heading .badge {
  color: #d9edf7;
  background-color: #31708f;
}
.panel-info > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #bce8f1;
}
.panel-warning {
  border-color: #faebcc;
}
.panel-warning > .panel-heading {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.panel-warning > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #faebcc;
}
.panel-warning > .panel-heading .badge {
  color: #fcf8e3;
  background-color: #8a6d3b;
}
.panel-warning > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #faebcc;
}
.panel-danger {
  border-color: #ebccd1;
}
.panel-danger > .panel-heading {
  color: #a94442;
  background-color: #f2dede;
  border-color: #ebccd1;
}
.panel-danger > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ebccd1;
}
.panel-danger > .panel-heading .badge {
  color: #f2dede;
  background-color: #a94442;
}
.panel-danger > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ebccd1;
}
.close {
  float: right;
  font-size: 21px;
  font-weight: bold;
  line-height: 1;
  color: #000000;
  text-shadow: 0 1px 0 #ffffff;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.close:hover,
.close:focus {
  color: #000000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}
.modal-open {
  overflow: hidden;
}
.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  -o-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}
.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}
.modal-content {
  position: relative;
  background-color: #ffffff;
  border: 1px solid #999999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000000;
}
.modal-backdrop.fade {
  opacity: 0;
  filter: alpha(opacity=0);
}
.modal-backdrop.in {
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  min-height: 16.42857143px;
}
.modal-header .close {
  margin-top: -2px;
}
.modal-title {
  margin: 0;
  line-height: 1.42857143;
}
.modal-body {
  position: relative;
  padding: 15px;
}
.modal-footer {
  padding: 15px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}
.modal-footer .btn + .btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
  .modal-sm {
    width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  font-weight: normal;
  line-height: 1.4;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-left: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-left: -3px;
  padding: 0 5px;
}
.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #ffffff;
  text-align: center;
  text-decoration: none;
  background-color: #000000;
  border-radius: 4px;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  right: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000000;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000000;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.clearfix:before,
.clearfix:after,
.dl-horizontal dd:before,
.dl-horizontal dd:after,
.container:before,
.container:after,
.container-fluid:before,
.container-fluid:after,
.row:before,
.row:after,
.form-horizontal .form-group:before,
.form-horizontal .form-group:after,
.btn-toolbar:before,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:before,
.btn-group-vertical > .btn-group:after,
.nav:before,
.nav:after,
.navbar:before,
.navbar:after,
.navbar-header:before,
.navbar-header:after,
.navbar-collapse:before,
.navbar-collapse:after,
.panel-body:before,
.panel-body:after,
.modal-footer:before,
.modal-footer:after,
.header.boxed:before,
.header.boxed:after,
body.boxed .header:before,
body.boxed .header:after,
.header.fullwidth:before,
.header.fullwidth:after,
body.fullwidth .header:before,
body.fullwidth .header:after,
.header__dropdowns-container:before,
.header__dropdowns-container:after,
.footer.boxed:before,
.footer.boxed:after,
body.boxed .footer:before,
body.boxed .footer:after,
.footer.fullwidth:before,
.footer.fullwidth:after,
body.fullwidth .footer:before,
body.fullwidth .footer:after,
.footer.fullwidth .container:before,
.footer.fullwidth .container:after,
body.fullwidth .footer .container:before,
body.fullwidth .footer .container:after,
.content.boxed:before,
.content.boxed:after,
body.boxed *:not(.aside-column) > .content:before,
body.boxed *:not(.aside-column) > .content:after,
.content.fullwidth:before,
.content.fullwidth:after,
body.fullwidth *:not(.aside-column) > .content:before,
body.fullwidth *:not(.aside-column) > .content:after,
.content.fullwidth .container:before,
.content.fullwidth .container:after,
body.fullwidth *:not(.aside-column) > .content .container:before,
body.fullwidth *:not(.aside-column) > .content .container:after,
.breadcrumbs--full:before,
.breadcrumbs--full:after,
body.fullwidth .breadcrumbs:before,
body.fullwidth .breadcrumbs:after,
.breadcrumbs--full .container:before,
.breadcrumbs--full .container:after,
body.fullwidth .breadcrumbs .container:before,
body.fullwidth .breadcrumbs .container:after {
  content: " ";
  display: table;
}
.clearfix:after,
.dl-horizontal dd:after,
.container:after,
.container-fluid:after,
.row:after,
.form-horizontal .form-group:after,
.btn-toolbar:after,
.btn-group-vertical > .btn-group:after,
.nav:after,
.navbar:after,
.navbar-header:after,
.navbar-collapse:after,
.panel-body:after,
.modal-footer:after,
.header.boxed:after,
body.boxed .header:after,
.header.fullwidth:after,
body.fullwidth .header:after,
.header__dropdowns-container:after,
.footer.boxed:after,
body.boxed .footer:after,
.footer.fullwidth:after,
body.fullwidth .footer:after,
.footer.fullwidth .container:after,
body.fullwidth .footer .container:after,
.content.boxed:after,
body.boxed *:not(.aside-column) > .content:after,
.content.fullwidth:after,
body.fullwidth *:not(.aside-column) > .content:after,
.content.fullwidth .container:after,
body.fullwidth *:not(.aside-column) > .content .container:after,
.breadcrumbs--full:after,
body.fullwidth .breadcrumbs:after,
.breadcrumbs--full .container:after,
body.fullwidth .breadcrumbs .container:after {
  clear: both;
}
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.pull-right {
  float: right !important;
}
.pull-left {
  float: left !important;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
}
.affix {
  position: fixed;
}
@-ms-viewport {
  width: device-width;
}
.visible-xs,
.visible-sm,
.visible-md,
.visible-lg {
  display: none !important;
}
.visible-xs-block,
.visible-xs-inline,
.visible-xs-inline-block,
.visible-sm-block,
.visible-sm-inline,
.visible-sm-inline-block,
.visible-md-block,
.visible-md-inline,
.visible-md-inline-block,
.visible-lg-block,
.visible-lg-inline,
.visible-lg-inline-block {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-xs {
    display: block !important;
  }
  table.visible-xs {
    display: table;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  th.visible-xs,
  td.visible-xs {
    display: table-cell !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-block {
    display: block !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline {
    display: inline !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important;
  }
  table.visible-sm {
    display: table;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  th.visible-sm,
  td.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-block {
    display: block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline {
    display: inline !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important;
  }
  table.visible-md {
    display: table;
  }
  tr.visible-md {
    display: table-row !important;
  }
  th.visible-md,
  td.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-block {
    display: block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline {
    display: inline !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg {
    display: block !important;
  }
  table.visible-lg {
    display: table;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  th.visible-lg,
  td.visible-lg {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-block {
    display: block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline {
    display: inline !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline-block {
    display: inline-block !important;
  }
}
@media (max-width: 767px) {
  .hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}
.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  table.visible-print {
    display: table;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print,
  td.visible-print {
    display: table-cell !important;
  }
}
.visible-print-block {
  display: none !important;
}
@media print {
  .visible-print-block {
    display: block !important;
  }
}
.visible-print-inline {
  display: none !important;
}
@media print {
  .visible-print-inline {
    display: inline !important;
  }
}
.visible-print-inline-block {
  display: none !important;
}
@media print {
  .visible-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .hidden-print {
    display: none !important;
  }
}
.vertical-align {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.horizontal-align {
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
.noselect {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.dropdown-wd {
  margin-top: 7px;
  padding: 10px 18px;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  border-radius: 0;
  -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
}
.dropdown-wd > li > a {
  position: relative;
  margin: 0 -18px;
  padding: 3px 18px;
}
@media (max-width: 767px) {
  .dropdown-wd {
    margin-top: 0;
  }
}
.dropdown-menu > li > a:after,
.submenu > li > a:after {
  position: absolute;
  right: 7px;
  top: 50%;
  margin-top: -3px;
  content: "\e602";
  font-family: 'Welldone';
  font-size: 8px;
  line-height: 8px;
  cursor: pointer;
  opacity: 0.3;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.dropdown-menu > li > a:only-child:after,
.submenu > li > a:only-child:after {
  content: '';
}
.line-after {
  position: absolute;
  display: block;
  content: " ";
  right: 0;
  top: 0;
  height: 43px;
  width: 1px;
}
.line-before {
  position: absolute;
  display: block;
  content: " ";
  right: 0;
  top: 0;
  height: 43px;
  width: 1px;
  right: auto;
  left: 0;
}
.header {
  min-height: 70px;
  position: relative;
  z-index: 10001;
  -webkit-box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
}
@media (max-width: 767px) {
  .header {
    min-height: 43px;
  }
}
.header.boxed,
body.boxed .header {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 768px) {
  .header.boxed,
  body.boxed .header {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .header.boxed,
  body.boxed .header {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .header.boxed,
  body.boxed .header {
    width: 1170px;
  }
}
.header.boxed > .navbar-header,
body.boxed .header > .navbar-header,
.header.boxed > .navbar-collapse,
body.boxed .header > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .header.boxed > .navbar-header,
  body.boxed .header > .navbar-header,
  .header.boxed > .navbar-collapse,
  body.boxed .header > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.header.fullwidth,
body.fullwidth .header {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
.header.fullwidth > .navbar-header,
body.fullwidth .header > .navbar-header,
.header.fullwidth > .navbar-collapse,
body.fullwidth .header > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .header.fullwidth > .navbar-header,
  body.fullwidth .header > .navbar-header,
  .header.fullwidth > .navbar-collapse,
  body.fullwidth .header > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.header.fullwidth .container,
body.fullwidth .header .container {
  width: auto;
}
.header--only-logo {
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  text-align: center;
  min-height: 0;
  width: 100%;
}
.header--transparent {
  position: absolute;
  width: 100%;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
.header--transparent .dropdown .icon {
  color: #ffffff;
}
.header--transparent .navbar.stuck .dropdown .icon {
  color: #000;
}
.boxed .header--transparent {
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
}
.header--semi-transparent {
  position: absolute;
  width: 100%;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
.header--semi-transparent .dropdown .icon {
  color: #ffffff;
}
.header__dropdowns-container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  position: relative !important;
  height: 50px;
  margin-bottom: -82px;
  margin-top: 32px;
}
@media (min-width: 768px) {
  .header__dropdowns-container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .header__dropdowns-container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .header__dropdowns-container {
    width: 1170px;
  }
}
.header__dropdowns-container > .navbar-header,
.header__dropdowns-container > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .header__dropdowns-container > .navbar-header,
  .header__dropdowns-container > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.header.fullwidth .header__dropdowns-container,
body.fullwidth .header__dropdowns-container {
  width: 100%;
}
.header__dropdowns-container .dropdown--wd {
  margin-top: 7px;
  padding: 10px 18px;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  border-radius: 0;
  -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
}
.header__dropdowns-container .dropdown--wd > li > a {
  position: relative;
  margin: 0 -18px;
  padding: 3px 18px;
}
@media (max-width: 767px) {
  .header__dropdowns-container .dropdown--wd {
    margin-top: 0;
  }
}
@media (max-width: 767px) {
  .header__dropdowns-container {
    height: auto !important;
    margin: 0;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .header__dropdowns-container {
    margin-bottom: -60px;
    margin-top: 10px;
  }
}
.header__dropdowns {
  position: absolute;
  z-index: 1000;
  right: 0;
  top: -5px;
}
.navbar .header__dropdowns {
  margin: -26px 15px 0 0;
}
@media (max-width: 991px) {
  .navbar .header__dropdowns {
    margin: -13px 15px 0 0;
  }
}
@media (max-width: 767px) {
  .navbar .header__dropdowns {
    margin: -43px 0 0 0 !important;
  }
}
@media (max-width: 767px) {
  .header__dropdowns {
    top: 0;
  }
}
@media (max-width: 767px) {
  .header__dropdowns .dropdown {
    position: static;
  }
}
.header__dropdowns .dropdown .dropdown-toggle {
  position: relative;
}
.header__dropdowns .dropdown-menu {
  border: 0;
  margin-top: 7px;
  padding: 10px 18px;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  border-radius: 0;
  -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
}
.header__dropdowns .dropdown-menu > li > a {
  position: relative;
  margin: 0 -18px;
  padding: 3px 18px;
}
@media (max-width: 767px) {
  .header__dropdowns .dropdown-menu {
    margin-top: 0;
  }
}
.header__dropdowns .pull-right > .dropdown-menu {
  right: 3px;
}
.header__dropdowns__button {
  margin: 0 6px;
  padding: 10px 5px;
}
.header-line .header__dropdowns__button {
  padding-top: 0;
  padding-bottom: 0;
  border: 0;
  line-height: 40px;
  height: 40px;
}
@media (max-width: 767px) {
  .header__dropdowns__button {
    margin: -1px 0 0 0;
    padding: 0;
    text-align: center;
    width: 43px;
  }
  .header__dropdowns__button:before {
    position: absolute;
    display: block;
    content: " ";
    right: 0;
    top: 0;
    height: 43px;
    width: 1px;
    right: auto;
    left: 0;
  }
}
.header__dropdowns__button__symbol {
  font-size: 17px;
}
.header__dropdowns__button .icon {
  display: block;
  font-size: 20px;
  line-height: 1.4em;
  text-align: center;
}
@media (max-width: 767px) {
  .header__dropdowns__button .icon {
    font-size: 17px !important;
    line-height: 43px;
  }
}
.header__cart {
  display: inline-block;
}
.header__cart__indicator {
  float: left;
  line-height: 45px;
  margin: 0 5px;
  font-size: 1.077em;
  padding-top: 3px;
}
.header__cart .badge.badge--menu {
  right: -2px;
  top: 5px;
}
.header__cart__button {
  padding: 0;
}
.header__cart__button .icon {
  font-size: 36px;
  margin-top: -5px;
}
@media (max-width: 767px) {
  .header__cart__button .icon {
    margin-top: 0;
  }
}
.header .navbar-header {
  position: relative;
}
@media (max-width: 767px) {
  .header .navbar-header {
    margin-left: 0;
    margin-right: 0;
  }
}
.header .caret {
  border-top: 3px dashed;
  border-right: 3px solid transparent;
  border-left: 3px solid transparent;
}
.logo {
  float: left;
  display: block;
  font-family: 'Ubuntu', sans-serif;
  text-decoration: none;
  margin-right: 15px;
}
.logo img {
  max-width: 100%;
}
.logo img.logo-transparent {
  display: none;
}
.logo img.logo-mobile {
  display: none;
}
@media (max-width: 767px) {
  .logo img {
    height: 40px;
  }
  .logo img.logo-default {
    display: none !important;
  }
  .logo img.logo-transparent {
    display: none !important;
  }
  .logo img.logo-mobile {
    display: block !important;
  }
}
.header--transparent .logo img.logo-default {
  display: none;
}
.header--transparent .logo img.logo-transparent {
  display: block;
}
.header--transparent .stuck .logo img.logo-default {
  display: block;
}
.header--transparent .stuck .logo img.logo-transparent {
  display: none;
}
@media (max-width: 767px) {
  .logo {
    padding: 0 5px;
    text-align: center;
    position: relative;
  }
}
.header--only-logo .logo {
  margin: 60px 0 0;
  text-align: center;
  display: inline-block;
  float: none;
}
@media (max-width: 767px) {
  .header--only-logo .logo {
    padding: 0 5px;
    text-align: center;
    position: relative;
  }
}
.logo:hover {
  text-decoration: none;
}
.logo div {
  margin: 0;
  padding: 0;
  font-size: 36px;
  line-height: 1.35em;
  font-weight: 900;
}
@media (max-width: 767px) {
  .logo div {
    font-size: 25.2px;
    line-height: 1.5em;
  }
  .header--only-logo .logo div {
    font-size: 36px;
    line-height: 1.35em;
    font-weight: 900;
  }
}
.header-line {
  position: relative;
  z-index: 1;
  min-height: 40px;
  padding: 11px 0 10px;
  background-color: #2b383e;
  -webkit-box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
}
.header-line .header__dropdowns {
  position: relative;
  padding: 0;
  margin: -6px 0 -15px;
  border: 0;
}
.header-line .header__dropdowns .header__cart__indicator {
  line-height: 38px;
  padding-top: 2px;
}
.header-line .header__dropdowns .header__dropdowns__button .icon {
  line-height: 40px;
}
.header-line .header__dropdowns .header__cart__button .icon {
  font-size: 24px;
  margin-top: -2px;
}
.user-links ul {
  margin: 0;
  padding: 0;
}
.user-links ul .user-links__item {
  display: inline-block;
  margin-left: 1em;
  list-style: none;
  text-transform: uppercase;
  font-size: 0.846em;
}
.pull-right .user-links ul .user-links__item.user-links__item--separate {
  margin-left: 3em;
}
.pull-left .user-links ul .user-links__item.user-links__item--separate {
  margin-right: 3em;
}
.navbar {
  min-height: 43px;
}
.no-touch .navbar {
  -webkit-transition:  padding 200ms 0s ease;
  -moz-transition:  padding 200ms 0s ease;
  -ms-transition:  padding 200ms 0s ease;
  -o-transition:  padding 200ms 0s ease;
  transition:  padding 200ms 0s ease;
}
.header--sticky .navbar.stuck {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translateZ(0);
}
.header--sticky .navbar.stuck .header__dropdowns {
  margin: -77px 15px 0 0;
}
.header--sticky .navbar.stuck .header__search,
.header--sticky .navbar.stuck #openSlidemenu {
  display: none;
}
@media (min-width: 768px) {
  .boxed .header--sticky .navbar.stuck,
  body.boxed .header--sticky .navbar.stuck {
    width: auto;
  }
}
.header--small .navbar.stuck .header__dropdowns {
  margin: -57px 0 0 0;
}
.navbar #slidemenu {
  padding-right: 15px;
  padding-left: 15px;
}
@media (min-width: 768px) and (max-width: 991px) {
  .navbar #slidemenu {
    float: none !important;
  }
}
.navbar #slidemenu.stuck {
  left: 0 !important;
}
.navbar .navbar-toggle {
  float: left;
  border: 0;
  padding: 14px 11px 15px;
  margin: 0;
}
.navbar .navbar-toggle:after {
  position: absolute;
  display: block;
  content: " ";
  right: 0;
  top: 0;
  height: 43px;
  width: 1px;
}
.navbar .navbar-nav {
  margin-left: -10px;
  margin-right: -10px;
}
.navbar.navbar-wd {
  background-color: #ffffff;
  padding: 26px 0;
  margin-bottom: 0;
  border: 0;
  border-radius: 0;
}
@media (min-width: 768px) and (max-width: 991px) {
  .navbar.navbar-wd {
    padding: 13px 0;
  }
}
@media (max-width: 767px) {
  .navbar.navbar-wd {
    background-color: #454545 !important;
    padding: 0;
    margin: 0;
  }
  .navbar.navbar-wd .container {
    padding: 0;
  }
}
.header--transparent .navbar.navbar-wd {
  background-color: transparent;
  -webkit-transition:  all 200ms 0s ease-out;
  -moz-transition:  all 200ms 0s ease-out;
  -ms-transition:  all 200ms 0s ease-out;
  -o-transition:  all 200ms 0s ease-out;
  transition:  all 200ms 0s ease-out;
}
.header--semi-transparent .navbar.navbar-wd {
  background-color: transparent;
  -webkit-transition:  all 200ms 0s ease-out;
  -moz-transition:  all 200ms 0s ease-out;
  -ms-transition:  all 200ms 0s ease-out;
  -o-transition:  all 200ms 0s ease-out;
  transition:  all 200ms 0s ease-out;
}
.navbar.navbar-wd.stuck {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
@media (min-width: 992px) {
  .navbar.navbar-wd.stuck.stuck--smaller {
    padding: 8.66666667px 0 13.66666667px;
  }
}
@media (max-width: 767px) {
  .navbar.navbar-wd.stuck {
    padding: 0;
    margin: 0;
  }
  .navbar.navbar-wd.stuck .container {
    padding: 0;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .navbar.navbar-wd.stuck {
    padding: 8.66666667px 0 13.66666667px;
  }
}
@media (max-width: 767px) {
  .navbar.navbar-wd .navbar-collapse {
    float: none !important;
  }
}
@media (min-width: 768px) {
  .navbar.navbar-wd .navbar-collapse {
    display: inline-block !important;
    float: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .navbar.navbar-wd .navbar-collapse {
    display: block !important;
    margin: 15px -15px 0;
    padding: 10px 0 0;
    clear: both;
  }
}
@media (min-width: 768px) {
  .navbar.navbar-wd .dropdown-menu {
    margin-top: 7px;
    padding: 10px 18px;
    border-bottom-width: 2px;
    border-bottom-style: solid;
    border-radius: 0;
    -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  }
  .navbar.navbar-wd .dropdown-menu > li > a {
    position: relative;
    margin: 0 -18px;
    padding: 3px 18px;
  }
  .navbar.navbar-wd .dropdown-menu li {
    position: relative;
    z-index: 1;
  }
  .navbar.navbar-wd .dropdown-menu li:hover {
    z-index: 2;
  }
  .navbar.navbar-wd .dropdown-menu li:hover > ul.submenu {
    display: block;
  }
  .navbar.navbar-wd .dropdown-menu ul.submenu {
    display: none;
    position: absolute;
    left: 90%;
    list-style: none;
    margin: -36px 0 0 0;
    padding: 10px 0;
    min-width: 180px;
    -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  }
  .navbar.navbar-wd .dropdown-menu ul.submenu li > a {
    display: block;
    padding: 3px 18px;
    line-height: 1.42857143;
    text-decoration: none;
  }
}
@media (min-width: 768px) and (max-width: 767px) {
  .navbar.navbar-wd .dropdown-menu {
    margin-top: 0;
  }
}
@media (max-width: 767px) {
  .navbar.navbar-wd .dropdown-menu ul.submenu {
    margin: 0;
    padding: 10px 25px 0 45px;
    list-style: none;
  }
  .navbar.navbar-wd .dropdown-menu ul.submenu ul {
    padding: 10px 25px 0 25px;
  }
  .navbar.navbar-wd .dropdown-menu ul.submenu ul li {
    opacity: 0.7;
  }
  .navbar.navbar-wd .dropdown-menu ul.submenu li {
    margin-bottom: 10px;
    font-size: 12px;
  }
}
.navbar.navbar-wd .nav {
  text-align: center;
}
@media (min-width: 1200px) {
  .navbar.navbar-wd .nav {
    padding: 3px 0 0 0;
    width: 780px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .navbar.navbar-wd .nav {
    width: 580px;
    text-align: left;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .navbar.navbar-wd .nav {
    width: 100%;
    margin-top: 10px;
    margin-left: -25px !important;
    margin-right: -25px !important;
    padding: 0 15px;
    text-align: left;
  }
}
.navbar.navbar-wd .nav > li {
  position: relative;
  float: none;
  display: inline-block;
  vertical-align: top;
}
.navbar.navbar-wd .nav > li > a {
  padding: 0;
  min-height: 2.308em;
  font-size: 1.154em;
  text-transform: uppercase;
}
.navbar.navbar-wd .nav > li > a span.link-name {
  display: block;
  padding: 7px 14px 15px;
  margin-bottom: -8px;
}
@media (min-width: 768px) {
  header a.dropdown-toggle:before,
  header a.dropdown-toggle:after {
    bottom: -8px;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    z-index: 10000;
    pointer-events: none;
    opacity: 0;
    -webkit-transition:  opacity 100ms 0 ease-out;
    -moz-transition:  opacity 100ms 0 ease-out;
    -ms-transition:  opacity 100ms 0 ease-out;
    -o-transition:  opacity 100ms 0 ease-out;
    transition:  opacity 100ms 0 ease-out;
  }
  header a.dropdown-toggle:after {
    border-width: 8px;
    margin-left: -8px;
  }
  header a.dropdown-toggle:before {
    border-width: 9px;
    margin-left: -9px;
  }
  header .open > a.dropdown-toggle:before,
  header .open > a.dropdown-toggle:after,
  header li.hovernav:hover > a.dropdown-toggle:before,
  header li.hovernav:hover > a.dropdown-toggle:after {
    opacity: 1;
  }
}
.ul-row {
  overflow: hidden;
  min-width: 200px;
  padding: 20px !important;
  margin: 0;
}
.open .ul-row {
  display: table;
}
.ul-row .li-col {
  display: table-cell;
  min-width: 190px;
  padding: 0 30px 0 0;
}
@media (max-width: 991px) {
  .ul-row .li-col {
    display: block;
    margin-bottom: 10px;
    padding: 0;
  }
}
.ul-row .li-col ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.ul-row .li-col ul li a {
  display: block;
  padding: 8px 10px;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  text-decoration: none;
  font-size: 0.857em;
}
.ul-row .li-col ul li a:hover {
  text-decoration: none !important;
}
.ul-row .li-col ul li:last-child a {
  border: 0;
}
.ul-row .li-col h4 {
  font-size: 1.077em;
  line-height: 1.643em;
  text-transform: uppercase;
  padding: 0 0 8px;
  border-bottom-style: solid;
  border-bottom-width: 2px;
  text-align: left !important;
}
.list-user-menu {
  padding: 0 !important;
  min-width: 160px !important;
}
.list-user-menu a {
  white-space: nowrap;
}
.currency__item a span {
  padding-right: 10px;
  font-weight: bold;
}
.languages__item {
  min-height: 1.286em;
}
.languages__item a {
  white-space: nowrap;
}
.languages__item__flag {
  display: none;
  border-radius: 1px;
}
.languages__item--active .languages__item__flag {
  -webkit-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.2);
}
.languages--flag .languages__item__flag {
  display: inline-block;
  margin: 3px 0;
}
.languages__item__label {
  display: block;
  height: 18px;
  line-height: 18px;
}
.languages--flag .languages__item__label {
  display: inline-block;
  padding-left: 10px;
}
.menu-large {
  position: static !important;
}
.megamenu {
  padding: 0 !important;
  width: 100%;
  top: auto;
}
.megamenu > .container {
  padding-left: 15px !important;
  padding-right: 15px !important;
}
.megamenu ul {
  padding: 0;
  margin: 0;
  list-style: none;
  position: relative;
}
.megamenu__content {
  padding-top: 20px;
  padding-bottom: 20px;
}
.megamenu__columns {
  padding-top: 25px !important;
  padding-bottom: 20px !important;
  padding-right: 400px !important;
}
.megamenu__columns.megamenu__columns-image-off {
  padding-right: 0 !important;
}
.megamenu__columns.megamenu__columns-image-off .level-menu {
  width: 24%;
}
.navbar-nav--vertical .megamenu__columns {
  padding-right: 300px !important;
}
.navbar-nav--vertical .megamenu__columns.megamenu__columns-image-off {
  padding-right: 0 !important;
}
.navbar-nav--vertical .megamenu__columns.megamenu__columns-image-off .level-menu {
  width: 24%;
}
@media (max-width: 991px) {
  .megamenu__columns {
    padding-right: 0 !important;
  }
  .navbar-nav--vertical .megamenu__columns {
    padding-right: 0 !important;
  }
}
.megamenu__columns .level-menu {
  width: 32.9%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 15px;
  z-index: 100;
}
@media (max-width: 991px) {
  .megamenu__columns .level-menu {
    width: 49% !important;
  }
}
@media (max-width: 767px) {
  .megamenu__columns .level-menu {
    width: 100% !important;
  }
}
.megamenu__columns .level-menu li {
  position: relative;
  float: left;
  clear: both;
  margin-bottom: 5px;
  font-size: 12px;
  background-color: transparent !important;
}
.megamenu__columns .level-menu li:hover {
  background-color: transparent !important;
}
.megamenu__columns .level-menu li:hover > a {
  background-color: transparent !important;
}
.megamenu__columns .level-menu li a:hover {
  background-color: transparent !important;
}
.megamenu__columns .level-menu li.title {
  font-size: 1em;
  font-weight: bold;
  margin-bottom: 10px;
  text-transform: uppercase;
}
.megamenu__columns .level-menu li ul.level-menu__dropdown {
  display: none;
}
@media (max-width: 767px) {
  .megamenu__columns .level-menu li ul.level-menu__dropdown {
    display: block;
    position: relative;
    margin-left: 15px;
    margin-top: 10px;
  }
}
@media (min-width: 768px) {
  .megamenu__columns .level-menu li ul.level-menu__dropdown {
    position: absolute;
    top: -10px;
    left: 100%;
    z-index: 100;
    padding: 10px;
    background-color: #ffffff;
    -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.15);
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.15);
    min-width: 160px;
  }
  .megamenu__columns .level-menu li ul.level-menu__dropdown li {
    display: block;
    float: none;
    clear: none;
  }
}
.megamenu__columns__top-block,
.megamenu__columns__bottom-block {
  width: 100% !important;
  padding: 15px 0 10px;
  margin-bottom: 20px !important;
  font-size: 1.077em;
  line-height: 1.8em;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.megamenu__columns__top-block .icon,
.megamenu__columns__bottom-block .icon {
  vertical-align: top;
  padding-right: 7px;
}
.megamenu__columns__top-block li,
.megamenu__columns__bottom-block li {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
}
@media (max-width: 480px) {
  .megamenu__columns__top-block li,
  .megamenu__columns__bottom-block li {
    margin-right: 0;
    display: block;
  }
}
.megamenu__columns__top-block li a,
.megamenu__columns__bottom-block li a {
  padding: 0 10px;
  display: inline-block;
  vertical-align: top;
}
@media (max-width: 991px) {
  .megamenu__columns__top-block li a,
  .megamenu__columns__bottom-block li a {
    padding: 0;
  }
}
.megamenu__columns__top-block {
  margin-top: -25px !important;
}
.megamenu__columns__bottom-block {
  z-index: -2;
  margin-bottom: -20px !important;
  border-bottom: 0;
  border-top-width: 1px;
  border-top-style: solid;
}
.megamenu__columns__side-image {
  width: 400px !important;
  height: 100%;
  position: absolute !important;
  right: 5px;
  bottom: 0;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  z-index: -1;
}
@media (max-width: 991px) {
  .megamenu__columns__side-image {
    display: none;
  }
}
.megamenu__columns__side-image img {
  max-width: 100%;
  float: right;
}
.row-menu {
  padding: 0 !important;
  width: 100%;
  top: auto;
}
.row-menu > .container {
  padding-left: 15px !important;
  padding-right: 15px !important;
}
.row-menu ul {
  padding: 0;
  margin: 0;
  list-style: none;
  position: relative;
}
.row-menu__content {
  padding-top: 20px;
  padding-bottom: 20px;
}
.row-menu__columns {
  padding-top: 25px !important;
  padding-bottom: 20px !important;
  padding-right: 400px !important;
}
.row-menu__columns.megamenu__columns-image-off {
  padding-right: 0 !important;
}
.row-menu__columns.megamenu__columns-image-off .level-menu {
  width: 24%;
}
.navbar-nav--vertical .row-menu__columns {
  padding-right: 300px !important;
}
.navbar-nav--vertical .row-menu__columns.megamenu__columns-image-off {
  padding-right: 0 !important;
}
.navbar-nav--vertical .row-menu__columns.megamenu__columns-image-off .level-menu {
  width: 24%;
}
@media (max-width: 991px) {
  .row-menu__columns {
    padding-right: 0 !important;
  }
  .navbar-nav--vertical .row-menu__columns {
    padding-right: 0 !important;
  }
}
.row-menu__columns .level-menu {
  width: 32.9%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 15px;
  z-index: 100;
}
@media (max-width: 991px) {
  .row-menu__columns .level-menu {
    width: 49% !important;
  }
}
@media (max-width: 767px) {
  .row-menu__columns .level-menu {
    width: 100% !important;
  }
}
.row-menu__columns .level-menu li {
  position: relative;
  float: left;
  clear: both;
  margin-bottom: 5px;
  font-size: 12px;
  background-color: transparent !important;
}
.row-menu__columns .level-menu li:hover {
  background-color: transparent !important;
}
.row-menu__columns .level-menu li:hover > a {
  background-color: transparent !important;
}
.row-menu__columns .level-menu li a:hover {
  background-color: transparent !important;
}
.row-menu__columns .level-menu li.title {
  font-size: 1em;
  font-weight: bold;
  margin-bottom: 10px;
  text-transform: uppercase;
}
.row-menu__columns .level-menu li ul.level-menu__dropdown {
  display: none;
}
@media (max-width: 767px) {
  .row-menu__columns .level-menu li ul.level-menu__dropdown {
    display: block;
    position: relative;
    margin-left: 15px;
    margin-top: 10px;
  }
}
@media (min-width: 768px) {
  .row-menu__columns .level-menu li ul.level-menu__dropdown {
    position: absolute;
    top: -10px;
    left: 100%;
    z-index: 100;
    padding: 10px;
    background-color: #ffffff;
    -webkit-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.15);
    -moz-box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.15);
    box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.15);
    min-width: 160px;
  }
  .row-menu__columns .level-menu li ul.level-menu__dropdown li {
    display: block;
    float: none;
    clear: none;
  }
}
.row-menu__columns__top-block,
.row-menu__columns__bottom-block {
  width: 100% !important;
  padding: 15px 0 10px;
  margin-bottom: 20px !important;
  font-size: 1.077em;
  line-height: 1.8em;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.row-menu__columns__top-block .icon,
.row-menu__columns__bottom-block .icon {
  vertical-align: top;
  padding-right: 7px;
}
.row-menu__columns__top-block li,
.row-menu__columns__bottom-block li {
  display: inline-block;
  vertical-align: top;
  margin-right: 20px;
}
@media (max-width: 480px) {
  .row-menu__columns__top-block li,
  .row-menu__columns__bottom-block li {
    margin-right: 0;
    display: block;
  }
}
.row-menu__columns__top-block li a,
.row-menu__columns__bottom-block li a {
  padding: 0 10px;
  display: inline-block;
  vertical-align: top;
}
@media (max-width: 991px) {
  .row-menu__columns__top-block li a,
  .row-menu__columns__bottom-block li a {
    padding: 0;
  }
}
.row-menu__columns__top-block {
  margin-top: -25px !important;
}
.row-menu__columns__bottom-block {
  z-index: -2;
  margin-bottom: -20px !important;
  border-bottom: 0;
  border-top-width: 1px;
  border-top-style: solid;
}
.row-menu__columns__side-image {
  width: 400px !important;
  height: 100%;
  position: absolute !important;
  right: 5px;
  bottom: 0;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden;
  z-index: -1;
}
@media (max-width: 991px) {
  .row-menu__columns__side-image {
    display: none;
  }
}
.row-menu__columns__side-image img {
  max-width: 100%;
  float: right;
}
.row-menu ul {
  padding: 25px 0 15px;
  text-align: center;
}
@media (max-width: 767px) {
  .row-menu ul {
    text-align: left;
    padding: 10px 10px;
  }
}
.row-menu ul li {
  display: inline-block;
  vertical-align: top;
  margin: 0 10px 10px;
  font-size: 1em;
  text-transform: uppercase;
}
@media (min-width: 768px) {
  .row-menu ul li {
    background-color: transparent !important;
    text-decoration: none !important;
  }
  .row-menu ul li > a:hover,
  .row-menu ul li:hover a {
    color: #536dfe;
    text-decoration: none !important;
    background-color: transparent !important;
  }
}
@media (max-width: 767px) {
  .row-menu ul li {
    display: block;
    margin: 0 0 5px;
  }
}
.shopping-cart {
  width: 320px;
  padding-bottom: 15px !important;
}
.shopping-cart ul {
  margin: 0;
  padding: 0;
}
.shopping-cart__top {
  padding: 5px 18px 12px;
  margin: 0 -18px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.shopping-cart__bottom {
  padding: 12px 18px 0;
  margin: 0 -18px;
  border-top-width: 1px;
  border-top-style: solid;
  font-size: 0.923em;
  line-height: 39px;
}
.shopping-cart__total {
  font-size: 1.692em;
  line-height: 1em;
  vertical-align: sub;
  padding: 0 5px;
}
.shopping-cart__settings {
  position: absolute;
  right: 5px;
  top: 15px;
}
.shopping-cart__settings > a {
  padding: 10px;
}
.shopping-cart__item {
  display: inline-block;
  width: 100%;
  position: relative;
  padding: 11px 0 14px;
  font-size: 0.923em;
}
.shopping-cart__item:after {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  content: '';
  height: 1px;
}
.shopping-cart__item:last-child:after {
  display: none;
}
.shopping-cart__item__image {
  width: 58px;
}
.shopping-cart__item__image img {
  width: 100%;
  border-radius: 2px;
  -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.shopping-cart__item__image img:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.shopping-cart__item__info {
  overflow: hidden;
  padding: 0 55px 0 15px;
}
.shopping-cart__item__info__title h2 {
  font-size: 1em;
  padding: 0 0 10px;
  margin: 0;
}
.shopping-cart__item__info__price {
  position: absolute;
  top: 9px;
  right: 0;
}
.shopping-cart__item__info__qty {
  position: absolute;
  top: 30px;
  right: 0;
}
.shopping-cart__item__info__delete {
  position: absolute;
  top: 55px;
  right: 0;
}
.navbar-nav--vertical {
  width: 100%;
  position: relative;
  z-index: 1000;
  float: none;
  margin: 0;
  border-radius: 3px;
  border-width: 1px;
  border-style: solid;
}
@media (max-width: 991px) {
  .navbar-nav--vertical {
    display: none;
  }
}
@media (min-width: 992px) {
  .navbar-nav--vertical {
    display: block !important;
  }
}
.navbar-nav--vertical > li {
  float: none;
  position: relative !important;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.navbar-nav--vertical > li.menu-large {
  position: static !important;
}
.navbar-nav--vertical > li > a {
  padding: 16px 18px;
  text-transform: uppercase;
}
.navbar-nav--vertical > li > a .badge {
  border-radius: 3px;
  margin: 0 10px;
  font-size: 10px;
  line-height: 16px;
  vertical-align: top;
}
.navbar-nav--vertical > li > a .caret {
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
  margin: 0;
  height: 100%;
  padding: 18px 15px 25px 25px;
}
.navbar-nav--vertical > li .dropdown-menu {
  margin-top: 7px;
  padding: 10px 18px;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  border-radius: 0;
  -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  border: 0;
  top: -1px;
  margin-top: 0;
  background-color: #ffffff !important;
}
.navbar-nav--vertical > li .dropdown-menu > li > a {
  position: relative;
  margin: 0 -18px;
  padding: 3px 18px;
}
@media (max-width: 767px) {
  .navbar-nav--vertical > li .dropdown-menu {
    margin-top: 0;
  }
}
.navbar-nav--vertical > li .dropdown-menu li {
  position: relative;
}
@media (max-width: 991px) {
  .navbar-nav--vertical > li .dropdown-menu {
    top: auto;
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    border: 0;
    -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}
.navbar-nav--vertical > li .megamenu .container {
  width: auto;
}
@media (min-width: 992px) {
  .navbar-nav--vertical > li .megamenu__columns {
    position: static;
    padding-bottom: 55px !important;
  }
  .navbar-nav--vertical > li .megamenu__columns__bottom-block {
    position: absolute !important;
    bottom: 25px;
    margin: 0;
  }
}
@media (min-width: 992px) {
  .categories-title .icon {
    display: none;
  }
}
@media (max-width: 991px) {
  .categories-title {
    cursor: pointer;
    margin-bottom: 0;
    padding-bottom: 15px;
  }
  .categories-title .icon {
    vertical-align: middle;
    font-weight: 700;
    font-size: 12px;
    padding: 0 5px 0 10px;
    margin-top: -2px;
    cursor: pointer;
  }
  .categories-title .icon-arrow-up {
    display: none;
  }
  .categories-title.open .icon-arrow-down {
    display: none;
  }
  .categories-title.open .icon-arrow-up {
    display: inline-block;
  }
}
@media (max-width: 767px) {
  #slidemenu {
    left: -100%;
    width: 80%;
    min-width: 0;
    position: absolute;
    padding-left: 0;
    z-index: 2000;
    top: 0;
    margin: 0;
    padding: 0 !important;
    overflow-x: hidden;
    overflow-y: auto;
  }
  #slidemenu .slidemenu-close {
    position: absolute;
    z-index: 2001;
    right: 0;
    top: 3px;
    width: 40px;
    height: 40px;
    vertical-align: text-top;
    font-size: 26px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
  }
  #slidemenu .navbar-nav {
    width: 100%;
    position: relative;
    float: none;
    margin: 0;
  }
  #slidemenu .navbar-nav > li {
    float: none;
    display: block;
    width: 100%;
    position: relative !important;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: #767676;
    text-align: left;
  }
  #slidemenu .navbar-nav > li a {
    position: relative;
  }
  #slidemenu .navbar-nav > li a .badge {
    border-radius: 3px;
    margin: 0 10px;
    font-size: 10px;
    line-height: 16px;
    vertical-align: top;
  }
  #slidemenu .navbar-nav > li a .caret {
    position: absolute;
    right: 0;
    top: 50%;
    padding: 15px 15px 25px;
    margin: -23px 0 0 0;
  }
  #slidemenu .navbar-nav > li > a > span.link-name {
    padding: 14px 25px;
    margin-bottom: 0;
    text-transform: uppercase;
  }
  #slidemenu .navbar-nav > li .dropdown-menu {
    width: auto;
    top: auto;
    border-top-style: 1px;
    border-top-width: solid;
  }
  #slidemenu .navbar-nav > li .megamenu .container {
    width: auto;
    padding: 0 15px;
  }
}
/* Header variants */
/* header--small */
@media (min-width: 768px) {
  .header--small {
    min-height: 70px;
  }
}
@media (min-width: 768px) {
  .header--small .navbar-header {
    float: none;
    text-align: center;
  }
}
@media (min-width: 768px) {
  .header--small #slidemenu {
    display: none;
    opacity: 0;
    text-align: center;
    -webkit-transition:  opacity 100ms 0 ease-out;
    -moz-transition:  opacity 100ms 0 ease-out;
    -ms-transition:  opacity 100ms 0 ease-out;
    -o-transition:  opacity 100ms 0 ease-out;
    transition:  opacity 100ms 0 ease-out;
  }
  .header--small #slidemenu.open {
    opacity: 1;
  }
  .header--small #slidemenu .navbar-nav > li {
    float: none;
    display: inline-block;
    vertical-align: top;
  }
}
.boxed .header--small {
  background-color: #ffffff;
}
@media (min-width: 768px) {
  .header--small .header__dropdowns-container {
    margin-top: 0;
    height: 70px;
    padding-top: 10px;
    margin-bottom: -60px;
  }
}
@media (min-width: 768px) {
  .header--small .header__dropdowns {
    position: relative;
    top: 0;
  }
}
@media (min-width: 768px) {
  .header--small .navbar:not(.stuck) #slidemenu {
    width: 100% !important;
  }
  .header--small .navbar:not(.stuck) #slidemenu .navbar-nav {
    width: 100% !important;
  }
}
@media (min-width: 768px) {
  .header--small.header--sticky .stuck #slidemenu {
    display: block !important;
    opacity: 1 !important;
  }
}
@media (min-width: 768px) {
  .header--small.header--sticky .stuck .navbar-header {
    float: left;
    text-align: left;
  }
}
@media (min-width: 768px) {
  .header--small .logo {
    float: none;
  }
}
@media (min-width: 768px) {
  .header--small.header--sticky .stuck .logo {
    float: left;
  }
}
.header--small .navbar.navbar-wd {
  padding: 0;
}
/* header--max */
@media (min-width: 768px) {
  .header--max {
    min-height: 190px;
  }
}
@media (min-width: 768px) {
  .header--max .navbar-header {
    float: none;
    text-align: center;
    padding: 30px 0;
    -webkit-transition:  padding 200ms 0s ease;
    -moz-transition:  padding 200ms 0s ease;
    -ms-transition:  padding 200ms 0s ease;
    -o-transition:  padding 200ms 0s ease;
    transition:  padding 200ms 0s ease;
  }
}
.header--max #slidemenu.stuck {
  position: fixed;
  top: 0;
  width: 100%;
}
.header--max .header__dropdowns__button {
  padding-top: 0;
  padding-bottom: 0;
  line-height: 40px;
}
@media (min-width: 768px) {
  .header--max .header__dropdowns {
    top: -57px;
  }
  .header--max .header__dropdowns .header__cart__button .icon {
    font-size: 24px;
    margin-top: 5px;
  }
  .header--max .header__search .header__dropdowns__button {
    padding: 5px 5px;
  }
  .header--max .header__cart__indicator {
    line-height: 43px;
    padding-top: 0;
  }
  .header--max a.btn.dropdown-toggle.btn--links--dropdown.header__cart__button.header__dropdowns__button {
    border-width: 0;
    margin-left: 8px;
  }
}
@media (min-width: 992px) {
  .header--max .header__dropdowns {
    top: -73px;
  }
}
@media (max-width: 767px) and (min-width: 768px) {
  .header--max .header__dropdowns {
    top: -57px;
  }
}
@media (max-width: 767px) {
  .header--max .dropdown .header__dropdowns__button {
    width: auto;
    padding: 0 12px;
  }
  .header--max .header__cart .badge.badge--menu {
    right: 2px;
  }
  .header--max .header__dropdowns .dropdown {
    position: relative;
  }
  .header--max .dropdown-menu {
    right: 0;
    left: auto !important;
  }
}
@media (max-width: 767px) {
  .header--max .dropdown .header__dropdowns__button {
    padding: 0 6px;
  }
}
@media (min-width: 768px) {
  .header--max .logo {
    float: none;
    display: inline-block;
  }
}
.header--max .navbar.navbar-wd {
  padding: 0;
}
.header--max .navbar.navbar-wd .nav {
  margin: 0 auto !important;
  float: none;
  display: table;
  width: auto;
}
@media (min-width: 992px) {
  .navbar.navbar-wd.stuck.stuck--smaller {
    padding: 0;
  }
}
.navbar #slidemenu {
  padding: 7px 0 7px;
}
/* header--no-line */
@media (min-width: 768px) {
  .header--no-line .header__dropdowns-container {
    margin-top: 0;
  }
  .header--no-line .navbar.navbar-wd {
    margin-top: 15px;
  }
}
/*
Navbar "hovernav" dropdown menu - this works only for screen sizes larger than phones.
The Bootstrap CSS is unchanged.
*/
@media (min-width: 768px) {
  .navbar-nav .hovernav.open:hover {
    z-index: 100;
  }
  .navbar-nav .hovernav.open:hover > .dropdown-menu {
    display: block;
  }
}
.footer {
  position: relative;
}
.footer.boxed,
body.boxed .footer {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 768px) {
  .footer.boxed,
  body.boxed .footer {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .footer.boxed,
  body.boxed .footer {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .footer.boxed,
  body.boxed .footer {
    width: 1170px;
  }
}
.footer.boxed > .navbar-header,
body.boxed .footer > .navbar-header,
.footer.boxed > .navbar-collapse,
body.boxed .footer > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .footer.boxed > .navbar-header,
  body.boxed .footer > .navbar-header,
  .footer.boxed > .navbar-collapse,
  body.boxed .footer > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.footer.fullwidth,
body.fullwidth .footer {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
.footer.fullwidth > .navbar-header,
body.fullwidth .footer > .navbar-header,
.footer.fullwidth > .navbar-collapse,
body.fullwidth .footer > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .footer.fullwidth > .navbar-header,
  body.fullwidth .footer > .navbar-header,
  .footer.fullwidth > .navbar-collapse,
  body.fullwidth .footer > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.footer.fullwidth .container,
body.fullwidth .footer .container {
  width: auto;
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.footer.fullwidth .container > .navbar-header,
body.fullwidth .footer .container > .navbar-header,
.footer.fullwidth .container > .navbar-collapse,
body.fullwidth .footer .container > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .footer.fullwidth .container > .navbar-header,
  body.fullwidth .footer .container > .navbar-header,
  .footer.fullwidth .container > .navbar-collapse,
  body.fullwidth .footer .container > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.footer__links {
  position: relative;
  padding: 18px 0;
}
.footer .logo--footer {
  float: none;
  margin-bottom: 15px;
  display: block;
}
.footer .logo--footer img {
  max-width: 100%;
}
.footer__column-links {
  position: relative;
  padding: 45px 0;
}
@media (max-width: 767px) {
  .footer__column-links {
    padding: 35px 0 0;
  }
}
.footer__column-links .title {
  margin-top: 10px;
}
.footer__settings {
  padding: 10px 0;
}
.footer__settings .dropdown {
  display: inline-block;
  float: none !important;
  padding: 0 10px;
}
.footer__settings .dropdown .dropdown-menu {
  margin-top: 7px;
  padding: 10px 18px;
  border-bottom-width: 2px;
  border-bottom-style: solid;
  border-radius: 0;
  -webkit-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
  top: auto;
  bottom: 110%;
  border: 0;
}
.footer__settings .dropdown .dropdown-menu > li > a {
  position: relative;
  margin: 0 -18px;
  padding: 3px 18px;
}
@media (max-width: 767px) {
  .footer__settings .dropdown .dropdown-menu {
    margin-top: 0;
  }
}
@media (max-width: 767px) {
  .footer__settings .header__dropdowns__button {
    width: auto;
  }
}
.footer__subscribe {
  position: relative;
  background-color: #374850;
  padding: 18px 0;
}
.footer__bottom {
  position: relative;
  padding: 10px 0 8px;
  font-size: 0.846em;
  line-height: 1.5em;
}
.footer__bottom .container > div {
  width: 33.3%;
  line-height: 25px;
}
@media (max-width: 767px) {
  .footer__bottom .container > div {
    width: 100%;
    float: none !important;
    text-align: center;
    margin-bottom: 6px;
  }
}
.footer .social-links {
  text-align: right;
}
@media (max-width: 767px) {
  .footer .social-links {
    text-align: center;
    margin-top: 20px;
  }
}
.back-to-top {
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  /* note that you have @transform here */
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
  -webkit-transition:  all 200ms ease-out 0s;
  -moz-transition:  all 200ms ease-out 0s;
  -ms-transition:  all 200ms ease-out 0s;
  -o-transition:  all 200ms ease-out 0s;
  transition:  all 200ms ease-out 0s;
}
.back-to-top:hover {
  margin-top: -10px;
}
.back-to-top > a {
  -webkit-transition:  all 300ms ease-out 0s;
  -moz-transition:  all 300ms ease-out 0s;
  -ms-transition:  all 300ms ease-out 0s;
  -o-transition:  all 300ms ease-out 0s;
  transition:  all 300ms ease-out 0s;
  position: relative;
}
.back-to-top > a:hover span {
  position: absolute;
  bottom: 26px;
  left: 24px;
  -webkit-animation: scroll-ani-to-top 800ms linear infinite;
  -moz-animation: scroll-ani-to-top 800ms linear infinite;
  animation: scroll-ani-to-top 800ms linear infinite;
}
@media (max-width: 767px) {
  .mobile-collapse {
    width: 100%;
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }
  .mobile-collapse__content {
    display: none;
    padding-bottom: 15px;
  }
  .open > .mobile-collapse__content {
    display: block;
  }
  .mobile-collapse__title {
    cursor: pointer;
    padding: 16px 0;
    margin-top: 0 !important;
    position: relative;
  }
  .mobile-collapse__title:after {
    position: absolute;
    display: block;
    top: 20px;
    right: 15px;
    font-family: 'Welldone';
    font-size: 10px;
    content: "\e601";
    line-height: 1em;
  }
  .open > .mobile-collapse__title:after {
    content: "\e631";
  }
  .mobile-collapse--last {
    border-bottom: 0;
  }
}
.outer {
  width: 100%;
  display: inline-block;
}
.page-content {
  position: relative;
  overflow: hidden;
}
.row-no-padding [class*="col-"] {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.parallax,
.image-bg {
  background: url(../images/parallax-bg.jpg) 50% 50% no-repeat fixed;
}
.touch .parallax,
.touch .image-bg {
  background-attachment: scroll;
  background-repeat: repeat;
  background-size: cover;
  background-position: center 0 !important;
}
.title-outer {
  padding-top: 75px;
}
.title-outer.top-null {
  margin-top: 0 !important;
  padding-top: 0;
}
.title-outer.bottom-null {
  margin-bottom: 0 !important;
  padding-bottom: 0;
}
#pageContent > #slider.content {
  padding-top: 0;
  padding-bottom: 0 !important;
}
.content {
  padding-top: 75px;
}
@media (max-width: 767px) {
  .content {
    padding-top: 37.5px;
  }
}
#pageContent > .content:last-child,
#pageContent > .content:last-of-type {
  padding-bottom: 48.75px;
}
.content .aside-column {
  padding-top: 48.75px;
}
@media (max-width: 767px) {
  .content .aside-column {
    float: none !important;
  }
}
.aside-column .content {
  padding-top: 48.75px;
}
.aside-column .content:first-child {
  padding-top: 0;
}
.aside-column .content:last-child {
  padding-bottom: 48.75px;
}
.landing-content .content {
  padding-top: 0;
}
.content.top-padding {
  padding-top: 48.75px !important;
}
.content.top-null {
  margin-top: 0 !important;
  padding-top: 0;
}
.content.top-pad-null {
  padding-top: 0 !important;
}
.content.bottom-null {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
.content--fill {
  padding-top: 75px !important;
  padding-bottom: 75px !important;
  margin-top: 75px;
}
.content--fill.top-pad-null {
  padding-top: 0 !important;
}
.content--parallax {
  background: url(../images/parallax-bg.jpg) 50% 50% no-repeat fixed;
  padding: 60px 0 70px 0;
  margin-top: 75px;
}
.content--parallax.top-null {
  padding: 60px 0 70px 0;
}
.touch .content--parallax {
  background-attachment: scroll;
  background-repeat: repeat;
  background-size: cover;
  background-position: center 0 !important;
}
.content--parallax--short {
  padding: 60px 0 50px !important;
}
.content--parallax--high {
  padding: 75px 0 !important;
}
@media (min-width: 768px) {
  .content--parallax--high {
    padding: 112.5px 0 !important;
  }
}
@media (min-width: 992px) {
  .content--parallax--high {
    padding: 150px 0 !important;
  }
}
@media (min-width: 1200px) {
  .content--parallax--high {
    padding: 172.5px 0 !important;
  }
}
.content.boxed,
body.boxed *:not(.aside-column) > .content {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
@media (min-width: 768px) {
  .content.boxed,
  body.boxed *:not(.aside-column) > .content {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .content.boxed,
  body.boxed *:not(.aside-column) > .content {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .content.boxed,
  body.boxed *:not(.aside-column) > .content {
    width: 1170px;
  }
}
.content.boxed > .navbar-header,
body.boxed *:not(.aside-column) > .content > .navbar-header,
.content.boxed > .navbar-collapse,
body.boxed *:not(.aside-column) > .content > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .content.boxed > .navbar-header,
  body.boxed *:not(.aside-column) > .content > .navbar-header,
  .content.boxed > .navbar-collapse,
  body.boxed *:not(.aside-column) > .content > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.content.boxed .hide-outer-animation,
body.boxed *:not(.aside-column) > .content .hide-outer-animation {
  overflow: hidden;
  padding-bottom: 25px;
  margin-bottom: -25px;
}
.content.fullwidth,
body.fullwidth *:not(.aside-column) > .content {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
.content.fullwidth > .navbar-header,
body.fullwidth *:not(.aside-column) > .content > .navbar-header,
.content.fullwidth > .navbar-collapse,
body.fullwidth *:not(.aside-column) > .content > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .content.fullwidth > .navbar-header,
  body.fullwidth *:not(.aside-column) > .content > .navbar-header,
  .content.fullwidth > .navbar-collapse,
  body.fullwidth *:not(.aside-column) > .content > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.content.fullwidth .container,
body.fullwidth *:not(.aside-column) > .content .container {
  width: auto;
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
.content.fullwidth .container > .navbar-header,
body.fullwidth *:not(.aside-column) > .content .container > .navbar-header,
.content.fullwidth .container > .navbar-collapse,
body.fullwidth *:not(.aside-column) > .content .container > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .content.fullwidth .container > .navbar-header,
  body.fullwidth *:not(.aside-column) > .content .container > .navbar-header,
  .content.fullwidth .container > .navbar-collapse,
  body.fullwidth *:not(.aside-column) > .content .container > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
body.fullwidth .content .container {
  padding-left: 15px !important;
  padding-right: 15px !important;
}
.slider {
  padding-bottom: 75px;
}
@media (max-width: 1199px) {
  .aside-column {
    margin-bottom: 35px;
  }
}
.aside-column .container {
  width: 100% !important;
}
.social-item {
  text-align: center;
  margin-bottom: 75px;
}
@media (min-width: 992px) {
  .social-item {
    max-width: 340px;
  }
}
.social-item .img-responsive {
  display: inline-block;
}
@media (min-width: 1770px) {
  body.wide {
    width: 100%;
  }
  body.wide .container,
  body.wide .header__dropdowns-container {
    width: 1770px;
  }
}
#pluginIdTFPU_2 {
  display: none;
}
body.boxed {
  background: url(../images/bg-boxed.jpg) no-repeat center top fixed;
}
.touch body.boxed {
  background-attachment: inherit;
  background-repeat: repeat;
}
body.boxed .content--fill {
  margin-top: 0;
}
body.boxed .content--parallax {
  position: relative;
}
body.boxed .content--parallax:before {
  content: ' ';
  position: absolute;
  z-index: -1;
  width: 100%;
  height: 75px;
  top: -75px;
  display: block;
}
#loader-wrapper,
#modalLoader-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
}
#loader-wrapper.loader-off {
  display: none !important;
}
#loader,
#modalLoader {
  border: 3px solid transparent;
  border-top-color: #536dfe;
  border-radius: 50%;
  display: block;
  margin: -75px 0 0 -75px;
  width: 150px;
  height: 150px;
  position: relative;
  top: 50%;
  left: 50%;
  -webkit-animation: spin 2s linear infinite;
  -moz-animation: spin 2s linear infinite;
  -o-animation: spin 2s linear infinite;
  -ms-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}
#loader:before,
#modalLoader:before {
  border: 3px solid transparent;
  border-radius: 50%;
  content: "";
  position: absolute;
  top: 5px;
  right: 5px;
  bottom: 5px;
  left: 5px;
  -webkit-animation: spin 3s linear infinite;
  -moz-animation: spin 3s linear infinite;
  -o-animation: spin 3s linear infinite;
  -ms-animation: spin 3s linear infinite;
  animation: spin 3s linear infinite;
}
#loader:after,
#modalLoader:after {
  border: 3px solid transparent;
  border-radius: 50%;
  content: "";
  position: absolute;
  top: 15px;
  left: 15px;
  bottom: 15px;
  right: 15px;
  -webkit-animation: spin 1.5s linear infinite;
  -moz-animation: spin 1.5s linear infinite;
  -o-animation: spin 1.5s linear infinite;
  -ms-animation: spin 1.5s linear infinite;
  animation: spin 1.5s linear infinite;
}
@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-moz-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-o-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-ms-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.loaded #loader,
.touch #loader {
  opacity: 0;
  transition: all 0.3s ease-out;
}
.loaded #loader-wrapper,
.touch #loader-wrapper {
  visibility: hidden;
  transform: translateY(-100%);
  transition: all 0.3s 1s ease-out;
}
body {
  font-family: 'Roboto', sans-serif;
  font-size: 1.3em;
  line-height: 1.4em;
}
a:hover,
a:focus,
a:active {
  outline: none;
}
a:focus,
a:active {
  text-decoration: none;
}
a.underline {
  text-decoration: underline;
}
a.underline:hover,
a.underline:focus,
a.underline:active {
  text-decoration: none;
}
.text-uppercase {
  text-transform: uppercase;
}
.icon--lg {
  font-size: 24px;
}
.icon-enable {
  display: inline-block;
  margin-top: -3px;
  font-size: 19px;
  line-heigth: 1em;
}
.icon-disable {
  display: inline-block;
  margin-top: -3px;
  font-size: 19px;
  line-heigth: 1em;
}
label {
  font-weight: normal;
}
label.label--top {
  display: block;
}
.label {
  padding: .3em .8em .3em;
}
h1 {
  font-family: 'Roboto', sans-serif;
  font-weight: 300;
  font-size: 2.615em;
  padding: 0 0 50px;
  margin: 0;
}
h2 {
  font-family: 'Roboto', sans-serif;
  font-weight: 300;
  font-size: 2.308em;
  padding: 0 0 30px 0;
  margin: 0;
}
h3 {
  font-family: 'Roboto', sans-serif;
  font-weight: 300;
  font-size: 2em;
  padding: 0 0 1.5em;
  margin: 0;
}
h4 {
  font-family: 'Roboto', sans-serif;
  font-weight: 300;
  font-size: 1.692em;
  padding: 0 0 1.5em;
  margin: 0;
}
@media (max-width: 991px) {
  h4 {
    text-align: center;
  }
}
h5 {
  font-family: 'Roboto', sans-serif;
  font-weight: normal;
  font-size: 1.231em;
  padding: 0 0 1.2em;
  margin: 0;
}
h6 {
  font-family: 'Roboto', sans-serif;
  font-weight: normal;
  font-size: 1.077em;
  padding: 0 0 1em;
  margin: 0;
}
h1 .icon,
h2 .icon,
h3 .icon,
h4 .icon,
h5 .icon,
h6 .icon {
  vertical-align: bottom;
  display: inline-block;
  padding-right: 5px;
}
.h-pad-sm {
  padding-bottom: 20px;
}
.h-pad-xs {
  padding-bottom: 5px;
}
.divider {
  height: 75px;
  clear: both;
}
.divider--lg {
  height: 112.5px;
}
.divider--md {
  height: 50.025px;
}
.divider--sm {
  height: 35.025px;
}
.divider--xs {
  height: 20.025px;
}
.divider--xxs {
  height: 7.5px;
}
.divider--line {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  margin-bottom: 75px;
}
.hr {
  clear: both;
  height: 1px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
address {
  font-style: italic;
}
.img-center {
  display: inline-block;
}
.img-left {
  float: left;
  margin: 0 30px 15px 0;
}
.img-right {
  float: right;
  margin: 0 0 15px 30px;
}
.mark {
  padding: 2px 2px 0;
}
.mark--color {
  padding: 2px 2px 0;
}
.h-links-list ul {
  position: relative;
  list-style: none;
  margin: 0;
  padding: 0;
}
.h-links-list li {
  display: inline-block;
  padding: 0 16px 0 0;
}
.v-links-list ul {
  position: relative;
  list-style: none;
  margin: 0;
  padding: 0;
}
.v-links-list li {
  padding: 0 0 4px;
  font-weight: lighter;
}
.v-links-list li.icon {
  padding: 0 0 4px 22px;
  vertical-align: middle;
  font-family: 'Roboto', sans-serif;
  font-weight: lighter;
  font-size: 1em;
  line-height: 1.4em;
}
.v-links-list li.icon:before {
  font-family: 'Welldone';
  position: absolute;
  font-size: 1em;
  left: 0;
}
.category-list {
  margin: 0 0 15px;
  padding: 0;
  list-style: none;
}
.category-list li {
  position: relative;
  padding: 10px 0 10px 15px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  text-transform: uppercase;
}
.category-list li:last-child {
  border: 0;
}
.category-list li a {
  text-decoration: none;
}
.category-list li:after {
  position: absolute;
  top: 10px;
  left: 0;
  display: block;
  content: "»";
}
.circle-list {
  margin: 15px 0;
  padding: 0;
  list-style: none;
}
.circle-list li {
  position: relative;
  padding-left: 15px;
  margin-bottom: 10px;
}
.circle-list li:after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  content: "•";
  font-size: 1.5em;
}
.simple-list {
  margin: 15px 0;
  padding: 0;
  list-style: none;
}
.simple-list li {
  position: relative;
  padding-left: 15px;
  margin-bottom: 5px;
}
.simple-list li:after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  content: "»";
}
.simple-list ul {
  margin: 10px 0 10px 0;
  padding: 0;
  list-style: none;
}
.simple-list ul li:after {
  content: "•";
  font-size: 1em;
}
.decimal-list {
  margin: 15px 0 15px 25px;
  padding: 0;
  list-style: decimal outside;
}
.decimal-list li {
  padding-left: 0;
  margin-bottom: 5px;
  text-indent: 0;
}
.decimal-list ul {
  margin: 5px 0 5px 17px;
  padding: 0;
  list-style: decimal outside;
}
.sizes-row {
  margin: 0;
  padding: 0;
  list-style: none;
}
.sizes-row li {
  min-width: 50px;
  display: inline-block;
  text-align: center;
}
.table.text-center td,
.table.text-center th {
  text-align: center;
}
.table-address {
  min-width: 250px;
  margin: 20px 0 35px;
}
.table-address td {
  padding: 3px 25px 3px 0;
  vertical-align: top;
}
.table-params td {
  border-left-width: 1px;
  border-left-style: solid;
}
.table-params tr > td:first-child {
  border-left: 0;
  width: 30%;
}
.table-params tr:last-child td {
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.dropcap {
  display: block;
  float: left;
  font-size: 2.833em;
  line-height: 1em;
  margin: 0 5px 0 0;
}
.pagination {
  margin: 0;
  border-radius: 1px;
}
.pagination > li:first-child > a,
.pagination > li:first-child > span {
  border-bottom-left-radius: 1px;
  border-top-left-radius: 1px;
}
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-bottom-right-radius: 1px;
  border-top-right-radius: 1px;
}
.pagination-label {
  line-height: 30px;
  padding-right: 15px;
}
.card {
  border-radius: 3px;
  border-width: 1px;
  border-style: solid;
}
.card-title {
  cursor: pointer;
  padding: 20px 0;
  margin-top: 0 !important;
  position: relative;
  font-size: 1.385em;
  background-color: #f8f8f8;
  padding: 18px 45px 18px 25px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.card--collapse.open .card-title {
  display: block;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.card-title:after {
  position: absolute;
  display: block;
  top: 24px;
  right: 12px;
  font-family: 'Welldone';
  font-size: 10px;
  content: "\e601";
  line-height: 1em;
}
.open > .card-title:after {
  content: "\e631";
}
.card-content {
  display: none;
  padding: 18px 25px;
}
.card--collapse.open .card-content {
  display: block;
}
.card--padding {
  padding: 20px 25px;
}
.card--form {
  padding: 20px 45px 0;
  text-align: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.card--form__icon {
  display: inline-block;
  margin-bottom: 15px;
  font-size: 100px;
  line-height: 100px;
}
.card--form .checkbox-group {
  display: inline-block;
}
.card--form__footer {
  border-top-width: 1px;
  border-top-style: solid;
  margin: 25px -45px 0;
  padding: 15px 45px;
}
.card--icon__cell {
  padding: 12px;
  text-align: center;
}
.card--icon__cell__icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  margin-bottom: 10px;
  font-size: 40px;
  line-height: 98px;
  text-align: center;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.card--icon__cell__icon:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.card--icon__cell__title {
  padding-bottom: 0;
}
.card--icon__text {
  padding: 20px;
}
.card__row-line {
  padding: 10px 0 30px;
  margin: 10px 10px 25px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.card__row-line:last-child {
  margin-bottom: 0;
  border: 0;
}
.card__row-line table.order-history {
  margin-bottom: -30px;
}
.card__row {
  position: relative;
  padding: 15px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
a.card__row {
  display: table;
  width: 100%;
  cursor: pointer;
  text-decoration: none;
}
.card__row--big {
  padding: 45px 15px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .card__row--big {
    padding: 22px 15px;
  }
}
.card__row:last-child {
  border: none;
}
.card__row__title {
  font-size: 1.077em;
  text-transform: uppercase;
}
.card__row--big .card__row__title {
  font-size: 1.692em;
  line-height: 1em;
}
.card__row--icon {
  padding-left: 80px;
  min-height: 70px;
}
.card__row--icon.card__row--big {
  min-height: 151px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .card__row--icon.card__row--big {
    min-height: 100px;
  }
}
.card__row--icon__icon {
  position: absolute;
  left: 0;
  width: 80px;
  text-align: center;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.card__row--icon__icon .icon {
  font-size: 38px;
  line-height: 1em;
  -webkit-transition:  color 300ms 0s ease;
  -moz-transition:  color 300ms 0s ease;
  -ms-transition:  color 300ms 0s ease;
  -o-transition:  color 300ms 0s ease;
  transition:  color 300ms 0s ease;
}
.card__row--big .card__row--icon__icon .icon {
  font-size: 50px !important;
}
.card__row--icon__text {
  display: table-cell;
  vertical-align: middle;
}
.grey-box {
  padding: 30px;
  margin-bottom: 30px;
}
blockquote.blockquote--wd {
  padding: 0;
  margin: 0 0 20px;
  border: none;
  font-size: 1em;
}
blockquote.blockquote--wd p {
  border-left-width: 4px;
  border-left-style: solid;
  border-left-color: #536dfe;
  padding: 5px 0 5px 30px;
  margin-bottom: 0;
}
blockquote.blockquote--wd footer {
  padding: 5px 0 0 30px;
  font-style: italic;
  font-size: 100%;
}
.testimonial {
  display: table;
  width: 100%;
  overflow: hidden;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.testimonial:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.testimonial__author {
  display: table-cell;
  vertical-align: middle;
  padding: 14px 28px;
}
.testimonial__author__image {
  margin-bottom: 4px;
  text-align: center;
}
@media (min-width: 768px) and (max-width: 991px) {
  .testimonial__author__image {
    margin: 15px 0 4px;
  }
}
.testimonial__author__image img {
  min-width: 100px;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.testimonial__author__name {
  text-align: center;
  font-size: 0.923em;
}
@media (min-width: 768px) and (max-width: 991px) {
  .testimonial__author__name {
    margin: 0 0 15px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .testimonial__author {
    display: table-row;
    text-align: center;
  }
}
.testimonial__text {
  display: table-cell;
  vertical-align: middle;
  position: relative;
  padding: 20px 20px 15px 65px;
  overflow: hidden;
}
.testimonial__text:after {
  position: absolute;
  display: block;
  top: 24px;
  left: 20px;
  font-family: 'Welldone';
  font-size: 26px;
  line-height: 1em;
  content: "\e63b";
}
.infobox {
  display: table;
  width: 100%;
  overflow: hidden;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.infobox:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.infobox__icon {
  display: table-cell;
  vertical-align: middle;
  padding: 25px 20px;
  width: auto;
  font-size: 64px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.infobox__icon:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.infobox__text {
  display: table-cell;
  vertical-align: middle;
  position: relative;
  padding: 20px 25px 15px;
  overflow: hidden;
}
#map {
  width: 100%;
  height: 390px;
  margin: 0 0 -5px;
  border: 0;
  overflow: hidden;
}
.calendar__header {
  font-size: 16px;
  padding: 0 0 10px;
  text-transform: uppercase;
}
.calendar__footer {
  font-size: 16px;
  padding: 15px 0 0;
  text-transform: uppercase;
}
.calendar table {
  border-collapse: collapse;
  font-family: 'PT Sans', sans-serif;
  font-size: 13px;
  width: 100%;
  max-width: 350px;
}
.calendar th,
.calendar td {
  border: 0;
  cursor: pointer;
  line-height: 22px;
}
.calendar tr:first-child td {
  font-weight: 700;
  padding-bottom: 10px;
}
.calendar .selected {
  font-weight: bold;
}
button:focus,
.btn:focus,
button:active:focus,
.btn:active:focus,
button.active:focus,
.btn.active:focus,
button.focus,
.btn.focus,
button:active.focus,
.btn:active.focus,
button.active.focus,
.btn.active.focus {
  outline: 0;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
button:hover,
.btn:hover,
button:focus,
.btn:focus,
button.focus,
.btn.focus {
  outline: 0;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
button:active,
.btn:active,
button.active,
.btn.active {
  outline: 0;
  outline: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
button.pull-right,
.btn.pull-right {
  margin-right: 10px;
}
.btn--wd {
  padding: 12px 14px;
  font-size: 1.214em;
  line-height: 1em;
  height: 40px;
  border-radius: 3px;
  vertical-align: top;
  border: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.btn--wd:hover,
.btn--wd:active,
.btn--wd.focus,
.btn--wd:focus {
  padding: 12px 14px;
}
.btn--wd:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.btn--wd .icon {
  display: inline-block;
}
.btn--wd span {
  position: relative;
  top: 0;
  left: 0;
}
.btn--wd.btn--links--dropdown {
  border: none;
  background: none;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.25);
}
.btn--wd.btn--links--dropdown:hover {
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.25);
}
.btn--wd.btn--with-icon .icon {
  padding-right: 5px;
}
.btn--wd.btn--xs {
  padding: 5px 10px;
  font-size: 0.923em;
  height: auto;
}
.btn--wd.btn--xs:hover {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.btn--wd.btn--sm {
  padding: 8px 16px;
  height: auto;
  font-size: 13px;
}
.btn--wd.btn--sm .icon {
  font-size: 12px;
}
.btn--wd.btn--lg {
  padding: 15px 20px;
  height: auto;
}
.btn--wd.btn--xl {
  padding: 19px 30px;
  font-size: 1.385em;
  height: auto;
}
.btn--wd.btn--xxl {
  padding: 25px 35px;
  font-size: 1.923em;
  height: auto;
}
.btn--clear {
  border: 0;
  padding-left: 0;
  background-color: transparent;
}
.btn--clear:hover,
.btn--clear:active,
.btn--clear.focus,
.btn--clear:focus {
  background-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.open > .btn--clear.dropdown-toggle {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn--wait,
.btn--round.btn--wait {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, rgba(0, 0, 0, 0) 25%, rgba(0, 0, 0, 0) 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, rgba(0, 0, 0, 0) 75%, rgba(0, 0, 0, 0));
  background-size: 40px 40px;
  animation: 2s linear 0s normal none infinite progress-bar-stripes;
  -webkit-animation: progress-bar-stripes 2s linear infinite;
}
.btn--round {
  border-radius: 999em;
  width: 60px;
  height: 60px;
  padding: 0;
  line-height: 65px;
  font-size: 32px;
  position: relative;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.btn--round:hover {
  text-decoration: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.btn--round--lg {
  width: 76px;
  height: 76px;
  line-height: 78px;
  font-size: 27px;
}
.btn--round--sm {
  width: 46px;
  height: 46px;
  line-height: 78px;
  font-size: 22px;
}
.btn--round--xs {
  width: 37px;
  height: 37px;
  line-height: 37px;
  font-size: 16px;
}
.input--wd {
  appearance: none;
  padding: 9px 12px;
  font-size: 1.214em;
  line-height: 1em;
  height: 40px;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
.input--wd:hover,
.input--wd:focus,
.input--wd.focus {
  box-shadow: none;
  outline: none;
}
.input--wd--full {
  width: 100%;
}
.textarea--wd {
  appearance: none;
  padding: 9px 12px;
  font-size: 1.214em;
  line-height: 1em;
  height: 40px;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  min-height: 140px;
  height: auto;
}
.textarea--wd:hover,
.textarea--wd:focus,
.textarea--wd.focus {
  box-shadow: none;
  outline: none;
}
.textarea--wd--full {
  width: 100%;
}
.input--line {
  display: block;
  border: none;
  padding-left: 0;
  -webkit-transition:  all 300ms cubic-bezier(0.64, 0.09, 0.08, 1) 0s;
  -moz-transition:  all 300ms cubic-bezier(0.64, 0.09, 0.08, 1) 0s;
  -ms-transition:  all 300ms cubic-bezier(0.64, 0.09, 0.08, 1) 0s;
  -o-transition:  all 300ms cubic-bezier(0.64, 0.09, 0.08, 1) 0s;
  transition:  all 300ms cubic-bezier(0.64, 0.09, 0.08, 1) 0s;
  background-position: -1000px 0;
  background-size: 1000px 100%;
  background-repeat: no-repeat;
  border-radius: 0;
  transition: all 0.3s ease-in-out;
}
.input--line:focus {
  box-shadow: none;
  outline: none;
  background-position: 0 0;
}
.input--line:focus::-webkit-input-placeholder {
  transition: all 0.3s ease-in-out;
  font-size: 0.9em;
  transform: translateY(-20px);
  visibility: visible !important;
}
.input-group--wd {
  display: block;
  position: relative;
  margin-bottom: 45px;
}
.input-group--wd input,
.input-group--wd textarea {
  font-size: 16px;
  line-height: 18px;
  padding: 10px 0 10px 0;
  -webkit-appearance: none;
  display: block;
  background: transparent;
  width: 100%;
  border: none;
  border-radius: 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.input-group--wd input:focus,
.input-group--wd textarea:focus {
  outline: none;
}
.input-group--wd input.input--full,
.input-group--wd textarea.input--full {
  width: 100%;
}
.input-group--wd textarea {
  min-height: 140px;
  height: auto;
}
.input-group--wd label {
  font-size: 18px;
  font-weight: normal;
  position: absolute;
  pointer-events: none;
  left: 0;
  top: 10px;
  transition: all 0.2s ease;
}
.input-group input:focus ~ label,
.input-group input.used ~ label,
.input-group textarea:focus ~ label,
.input-group textarea.used ~ label {
  top: -20px;
  font-size: 13px;
  opacity: 0.5;
  -webkit-transition: top 0.3s cubic-bezier(0.8, 0.15, 0.15, 0.85), font-size 0.3s cubic-bezier(0.8, 0.15, 0.15, 0.85);
  transition: top 0.3s cubic-bezier(0.8, 0.15, 0.15, 0.85), font-size 0.3s cubic-bezier(0.8, 0.15, 0.15, 0.85);
}
.input-group__bar {
  position: relative;
  display: block;
  width: 100%;
}
.input-group__bar:before,
.input-group__bar:after {
  display: block;
  content: ' ';
  height: 2px;
  width: 0;
  bottom: 0;
  position: absolute;
  transition: all 0.2s ease;
}
.input-group__bar:before {
  left: 50%;
}
.input-group__bar:after {
  right: 50%;
}
.input-group input:focus ~ .input-group__bar:before,
.input-group input:focus ~ .input-group__bar:after,
.input-group textarea:focus ~ .input-group__bar:before,
.input-group textarea:focus ~ .input-group__bar:after {
  width: 50%;
}
.contact-form {
  margin-bottom: 15px;
}
.contact-form .input--wd,
.contact-form .textarea--wd {
  margin-bottom: 15px;
}
label.label--inline {
  font-size: 1.231em;
  line-height: 2.6em;
  font-weight: normal;
  padding-right: 10px;
  margin-bottom: 0;
}
@media (max-width: 767px) {
  label.label--inline {
    display: block;
    margin-bottom: 5px;
    float: none !important;
  }
}
.subscribe-form {
  position: relative;
}
.subscribe-form__label {
  font-size: 1.231em;
  line-height: 2.6em;
  font-weight: normal;
  padding-right: 10px;
  margin-bottom: 0;
}
@media (max-width: 767px) {
  .subscribe-form__label {
    display: block;
    margin-bottom: 5px;
    float: none !important;
  }
}
.subscribe-form__input {
  margin-right: 10px;
}
@media (max-width: 767px) {
  .subscribe-form__input {
    margin-right: 0;
    width: 97%;
    padding-right: 50px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .subscribe-form__input {
    width: 160px;
  }
}
@media (max-width: 767px) {
  .subscribe-form button {
    position: absolute;
    right: 0;
  }
}
.subscribe-form button .icon {
  font-size: 18px;
}
.placeholder {
  font-size: 1em;
  line-height: 1.214em;
}
input::-webkit-input-placeholder {
  font-size: 1em;
  line-height: 1.214em;
}
input::-webkit-input-placeholder::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
input::-webkit-input-placeholder:-ms-input-placeholder {
  color: #999999;
}
input::-webkit-input-placeholder::-webkit-input-placeholder {
  color: #999999;
}
input::-moz-placeholder {
  font-size: 1em;
  line-height: 1.214em;
}
input::-moz-placeholder::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
input::-moz-placeholder:-ms-input-placeholder {
  color: #999999;
}
input::-moz-placeholder::-webkit-input-placeholder {
  color: #999999;
}
input:-ms-input-placeholder {
  font-size: 1em;
  line-height: 1.214em;
}
input:-ms-input-placeholder::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
input:-ms-input-placeholder:-ms-input-placeholder {
  color: #999999;
}
input:-ms-input-placeholder::-webkit-input-placeholder {
  color: #999999;
}
input:-moz-placeholder {
  font-size: 1em;
  line-height: 1.214em;
}
input:-moz-placeholder::-moz-placeholder {
  color: #999999;
  opacity: 1;
}
input:-moz-placeholder:-ms-input-placeholder {
  color: #999999;
}
input:-moz-placeholder::-webkit-input-placeholder {
  color: #999999;
}
.select-wrapper {
  position: relative;
  display: inline-block;
}
.select-wrapper:after {
  content: " ";
  height: 0;
  width: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #000;
  position: absolute;
  right: 12px;
  transition: all 0.3s linear;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  -moz-transform: scale(0.9999);
}
@-moz-document url-prefix() {
  .select-wrapper:after {
    margin-top: -2px;
  }
}
.select--wd::-ms-expand {
  display: none;
}
.select--wd {
  appearance: none;
  padding: 9px 12px;
  font-size: 1.214em;
  line-height: 1em;
  height: 40px;
  border-width: 1px;
  border-style: solid;
  border-radius: 3px;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  padding: 12px 12px;
  font-size: inherit;
  line-height: 1.1em;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
.select--wd:hover,
.select--wd:focus,
.select--wd.focus {
  box-shadow: none;
  outline: none;
}
.select--wd--full {
  width: 100%;
}
.select--wd--sm {
  padding: 6px 20px 6px 10px;
  height: 34px;
  line-height: 1.385em;
}
.select--wd--xs {
  padding: 2px 20px 2px 5px;
  height: 24px;
}
.select--wd--lg {
  padding: 5px 20px 5px 12px;
  height: 40px;
  font-size: 16px;
  line-height: 28px;
  margin-bottom: 5px;
  width: 100%;
}
.checkbox-group {
  position: relative;
}
.checkbox-group input[type=checkbox] {
  display: none;
}
.checkbox-group label {
  padding-left: 25px;
  cursor: pointer;
}
.checkbox-group label span {
  display: block;
  position: absolute;
  left: 0;
  -webkit-transition-duration: .3s;
  -moz-transition-duration: .3s;
  transition-duration: 0.3s;
}
.checkbox-group label .box {
  border-width: 1px;
  border-style: solid;
  height: 16px;
  width: 16px;
  z-index: 9;
  -webkit-transition-delay: .2s;
  -moz-transition-delay: .2s;
  transition-delay: 0.2s;
}
.checkbox-group label .check {
  top: 0;
  left: 4px;
  width: 8px;
  height: 11px;
  border-width: 2px;
  border-style: solid;
  border-top: none;
  border-left: none;
  opacity: 0;
  z-index: 10;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  transform: rotate(180deg);
  -webkit-transition-delay: .3s;
  -moz-transition-delay: .3s;
  transition-delay: 0.3s;
}
input[type=checkbox]:checked ~ label .check {
  opacity: 1;
  -webkit-transform: scale(1) rotate(45deg);
  -moz-transform: scale(1) rotate(45deg);
  transform: scale(1) rotate(45deg);
}
.radio {
  display: block;
  cursor: pointer;
  margin: 0 0 8px;
}
.radio:hover .inner {
  -webkit-transform: scale(0.8);
  -ms-transform: scale(0.8);
  transform: scale(0.8);
  opacity: .5;
}
.radio input {
  width: 1px;
  height: 1px;
  opacity: 0;
}
.radio input:checked + .outer .inner {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}
.radio input:checked + .outer {
  border-width: 1px;
  border-style: solid;
}
.radio input:focus + .outer .inner {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  opacity: 1;
}
.radio .outer {
  width: 16px;
  height: 16px;
  display: block;
  float: left;
  margin: 0 9px 0 0;
  border-width: 1px;
  border-style: solid;
  border-radius: 50%;
}
.radio .inner {
  -webkit-transition: all 0.25s ease-in-out;
  transition: all 0.25s ease-in-out;
  width: 10px;
  height: 10px;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  transform: scale(0);
  display: block;
  margin: 2px;
  border-radius: 50%;
  opacity: 0;
}
input.input--wd,
textarea.textarea--wd,
.select-wrapper select.input--wd,
.select-wrapper select.select--wd {
  -webkit-appearance: none;
  -moz-appearance: none;
}
form label.error {
  color: #ff0000;
  font-size: 12px !important;
  position: absolute;
  bottom: -25px;
  top: auto !important;
  opacity: 1 !important;
}
#success,
#error,
#subscribeSuccess,
#subscribeError {
  display: none;
  height: 70px;
}
#subscribeSuccess,
#subscribeError {
  height: 30px;
}
#success span,
#error span,
#subscribeSuccess span,
#subscribeError span {
  display: block;
}
#success span p,
#error span p,
#subscribeSuccess span p,
#subscribeError span p {
  margin-top: 15px;
}
#success span p,
#subscribeSuccess span p {
  color: #2990d6;
}
#error span p,
#subscribeError span p {
  color: #c0392b;
}
#subscribe-form label.error {
  position: absolute;
  bottom: -5px;
  left: 95px;
}
@media (max-width: 767px) {
  #subscribe-form label.error {
    left: 0;
  }
}
#subscribe-form input.error {
  margin-bottom: 25px;
}
.header__search {
  display: inline-block;
  margin: 0 12px;
  position: relative;
  width: 40px;
  height: 40px;
}
.header__search .icon {
  font-size: 20px;
}
.header--transparent .header__search .icon {
  color: #ffffff;
}
@media (max-width: 767px) {
  .header__search {
    width: auto;
    height: 43px;
    margin: 0;
  }
}
.header__search__input {
  position: absolute;
  top: 7px;
  right: 0;
  z-index: 2;
  opacity: 0;
  width: 35px;
  height: 35px;
  padding: 7px 0 6px;
  transition: width 0.6s ease-out;
  cursor: pointer;
  border: 0;
  border-radius: 0;
  background-color: transparent;
  font-size: 1.231em;
  line-height: 22px;
  text-transform: uppercase;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
.header-line .header__search__input {
  top: 2px;
}
.header--transparent .header__search__input {
  color: #ffffff;
}
.header__search--to-right .header__search__input {
  right: auto;
  left: 0;
  padding-left: 35px;
}
.header__search__input:hover,
.header__search__input:focus,
.header__search__input.focus {
  box-shadow: none;
  outline: none;
}
.header__search__input:focus {
  opacity: 1;
  z-index: 1;
  transition: width 0.6s ease-out, opacity 0.6s ease-out;
  padding-right: 40px;
  cursor: text;
  width: 350px;
  border-bottom-width: 2px;
  border-bottom-style: solid;
}
.header__search__button {
  position: absolute;
  top: 6px;
  padding: 5px 4px;
  font-size: 20px;
  right: 0;
  z-index: 1;
  cursor: pointer;
  background-color: transparent;
  box-shadow: none;
}
.header-line .header__search__button {
  top: 3px;
}
.header__search--to-right .header__search__button {
  right: auto;
  left: 0;
}
.modal--search input {
  margin-bottom: 15px;
  background-color: transparent;
}
.dots {
  position: relative;
  display: block;
}
.dots:before {
  font-family: 'Welldone';
  content: "\e657";
}
.dots--vertical:before {
  content: "\e641";
}
.caret.caret--dots {
  margin: -20px auto 0;
  width: auto;
  border-width: 0 0 0 0;
  text-align: center;
  font-size: 9px;
  position: relative;
  display: block;
}
.caret.caret--dots:before {
  font-family: 'Welldone';
  content: "\e657";
}
.caret.caret--dots.caret--dots--vertical {
  height: 22px;
  font-size: 17px;
  margin: 0;
}
.caret.caret--dots.caret--dots--vertical:before {
  content: "\e641";
}
.social-links ul {
  margin: 0;
  padding: 0;
}
.social-links ul .social-links__item {
  display: inline-block;
  margin-right: 1.538em;
  list-style: none;
  text-align: center;
}
.social-links ul .social-links__item a {
  font-size: 1.154em;
  text-decoration: none;
  -webkit-transition:  color 300ms 0s ease;
  -moz-transition:  color 300ms 0s ease;
  -ms-transition:  color 300ms 0s ease;
  -o-transition:  color 300ms 0s ease;
  transition:  color 300ms 0s ease;
}
.social-links--large ul .social-links__item {
  margin-right: 2.3em;
}
.social-links--large ul .social-links__item a {
  font-size: 1.385em;
  line-height: 2em;
}
.social-links--padding {
  padding: 10px 0;
}
.badge.badge--menu {
  position: absolute;
  padding: 0;
  right: -5px;
  top: -5px;
  width: 17px;
  height: 17px;
  line-height: 18px;
  text-align: center;
}
.badge.badge--squared {
  border-radius: 3px;
  padding: 4px 7px;
  margin-left: 12px;
  vertical-align: bottom;
}
/*product*/
.product-preview-wrapper {
  float: none;
  display: inline-block;
  margin-right: -4px;
  vertical-align: top;
  padding-bottom: 20px;
}
.slick-slider .product-preview-wrapper {
  float: left;
  margin-right: 0;
}
.products-isotope {
  margin-right: -20px;
}
.one-in-row .product-preview-wrapper {
  display: block;
  margin-right: 0;
  padding-bottom: 50px;
}
@media (min-width: 768px) {
  .one-in-row .product-preview-wrapper {
    max-width: 360px;
    margin: 0 auto;
  }
}
.two-in-row .product-preview-wrapper {
  max-width: none;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 559px) {
  .two-in-row .product-preview-wrapper {
    width: 100%;
  }
}
@media (min-width: 560px) {
  .two-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .two-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 992px) {
  .two-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 1200px) {
  .two-in-row .product-preview-wrapper {
    width: 50%;
  }
}
.three-in-row .product-preview-wrapper {
  max-width: none;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 559px) {
  .three-in-row .product-preview-wrapper {
    width: 100%;
  }
}
@media (min-width: 560px) {
  .three-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .three-in-row .product-preview-wrapper {
    width: 33.333%;
  }
}
@media (min-width: 992px) {
  .three-in-row .product-preview-wrapper {
    width: 33.333%;
  }
}
@media (min-width: 1200px) {
  .three-in-row .product-preview-wrapper {
    width: 33.333%;
  }
}
@media (min-width: 560px) {
  .aside-column .three-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .aside-column .three-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 992px) {
  .aside-column .three-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 1200px) {
  .aside-column .three-in-row .product-preview-wrapper {
    width: 33.333%;
  }
}
.four-in-row .product-preview-wrapper {
  max-width: none;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 559px) {
  .four-in-row .product-preview-wrapper {
    width: 100%;
  }
}
@media (min-width: 560px) {
  .four-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .four-in-row .product-preview-wrapper {
    width: 33.333%;
  }
}
@media (min-width: 992px) {
  .four-in-row .product-preview-wrapper {
    width: 25%;
  }
}
@media (min-width: 1200px) {
  .four-in-row .product-preview-wrapper {
    width: 25%;
  }
}
.five-in-row .product-preview-wrapper {
  max-width: none;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 559px) {
  .five-in-row .product-preview-wrapper {
    width: 100%;
  }
}
@media (min-width: 560px) {
  .five-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .five-in-row .product-preview-wrapper {
    width: 25%;
  }
}
@media (min-width: 992px) {
  .five-in-row .product-preview-wrapper {
    width: 20%;
  }
}
@media (min-width: 1200px) {
  .five-in-row .product-preview-wrapper {
    width: 20%;
  }
}
.six-in-row .product-preview-wrapper {
  max-width: none;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 559px) {
  .six-in-row .product-preview-wrapper {
    width: 100%;
  }
}
@media (min-width: 560px) {
  .six-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .six-in-row .product-preview-wrapper {
    width: 25%;
  }
}
@media (min-width: 992px) {
  .six-in-row .product-preview-wrapper {
    width: 20%;
  }
}
@media (min-width: 1200px) {
  .six-in-row .product-preview-wrapper {
    width: 16.667%;
  }
}
.seven-in-row .product-preview-wrapper {
  max-width: none;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 559px) {
  .seven-in-row .product-preview-wrapper {
    width: 100%;
  }
}
@media (min-width: 560px) {
  .seven-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .seven-in-row .product-preview-wrapper {
    width: 25%;
  }
}
@media (min-width: 992px) {
  .seven-in-row .product-preview-wrapper {
    width: 20%;
  }
}
@media (min-width: 1200px) {
  .seven-in-row .product-preview-wrapper {
    width: 16.667%;
  }
}
@media (min-width: 1600px) {
  .seven-in-row .product-preview-wrapper {
    width: 14.2%;
  }
}
.eight-in-row .product-preview-wrapper {
  max-width: none;
  width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (max-width: 559px) {
  .eight-in-row .product-preview-wrapper {
    width: 100%;
  }
}
@media (min-width: 560px) {
  .eight-in-row .product-preview-wrapper {
    width: 50%;
  }
}
@media (min-width: 768px) {
  .eight-in-row .product-preview-wrapper {
    width: 25%;
  }
}
@media (min-width: 992px) {
  .eight-in-row .product-preview-wrapper {
    width: 20%;
  }
}
@media (min-width: 1200px) {
  .eight-in-row .product-preview-wrapper {
    width: 16.667%;
  }
}
@media (min-width: 1600px) {
  .eight-in-row .product-preview-wrapper {
    width: 12.5%;
  }
}
@media (max-width: 559px) {
  .products-listing.two-in-row .product-preview-wrapper {
    width: 50%;
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media (max-width: 559px) {
  .products-listing.three-in-row .product-preview-wrapper {
    width: 50%;
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media (max-width: 559px) {
  .products-listing.four-in-row .product-preview-wrapper {
    width: 50%;
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media (max-width: 559px) {
  .products-listing.five-in-row .product-preview-wrapper {
    width: 50%;
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media (max-width: 559px) {
  .products-listing.six-in-row .product-preview-wrapper {
    width: 50%;
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media (max-width: 559px) {
  .products-listing.seven-in-row .product-preview-wrapper {
    width: 50%;
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media (max-width: 559px) {
  .products-listing.eight-in-row .product-preview-wrapper {
    width: 50%;
    padding-left: 5px;
    padding-right: 5px;
  }
}
.product-links {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 0.846em;
  text-transform: uppercase;
}
.product-links .icon {
  padding: 0 5px;
  vertical-align: baseline;
}
.product-links li {
  padding-bottom: 1em;
}
.product-links a {
  text-decoration: none;
}
.product-links--inline li {
  display: inline-block;
  padding-right: 3px;
}
.product-preview {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.no-touch .product-preview {
  -webkit-transition:  all 300ms ease-out 0s;
  -moz-transition:  all 300ms ease-out 0s;
  -ms-transition:  all 300ms ease-out 0s;
  -o-transition:  all 300ms ease-out 0s;
  transition:  all 300ms ease-out 0s;
}
.no-transition .product-preview {
  -webkit-transition:  all 0s ease-out 0s;
  -moz-transition:  all 0s ease-out 0s;
  -ms-transition:  all 0s ease-out 0s;
  -o-transition:  all 0s ease-out 0s;
  transition:  all 0s ease-out 0s;
}
@media (max-width: 767px) {
  .product-preview {
    max-width: 270px;
    margin: 0 auto;
  }
}
@media (max-width: 321px) {
  .product-preview {
    width: auto;
    margin: 0 auto;
  }
}
.product-preview__image {
  margin-bottom: 110px;
  position: relative;
}
@media (max-width: 767px) {
  .product-preview__image {
    margin-bottom: 0 !important;
  }
}
.product-preview__image img {
  width: 100%;
  border-radius: 4px 4px 0 0;
}
.product-preview__info {
  position: absolute;
  bottom: 0;
  border-top-width: 2px;
  border-top-style: solid;
  width: 100%;
  padding: 30px 30px 20px 30px;
  border-radius: 0 0 5px 5px;
}
.no-touch .product-preview__info {
  -webkit-transition:  all 200ms ease-out 0s;
  -moz-transition:  all 200ms ease-out 0s;
  -ms-transition:  all 200ms ease-out 0s;
  -o-transition:  all 200ms ease-out 0s;
  transition:  all 200ms ease-out 0s;
}
.no-transition .product-preview__info {
  -webkit-transition:  all 0s ease-out 0s;
  -moz-transition:  all 0s ease-out 0s;
  -ms-transition:  all 0s ease-out 0s;
  -o-transition:  all 0s ease-out 0s;
  transition:  all 0s ease-out 0s;
}
@media (max-width: 767px) {
  .product-preview__info {
    position: relative;
    padding: 30px 10px 10px;
  }
}
.product-preview__info__title h2 {
  font-size: 1em;
  margin: 0 0 10px 0;
  padding: 0;
  text-transform: uppercase;
}
.product-preview__info__title h2 a {
  text-decoration: none;
}
.product-preview__info__btns {
  height: 0;
  overflow: hidden;
  text-align: center;
  margin: 0 -30px;
  -webkit-transition:  all 200ms ease-out 0s;
  -moz-transition:  all 200ms ease-out 0s;
  -ms-transition:  all 200ms ease-out 0s;
  -o-transition:  all 200ms ease-out 0s;
  transition:  all 200ms ease-out 0s;
}
.product-preview__info__btns .btn {
  display: inline-block;
  opacity: 0;
  -webkit-transition:  all 250ms ease-out 250ms;
  -moz-transition:  all 250ms ease-out 250ms;
  -ms-transition:  all 250ms ease-out 250ms;
  -o-transition:  all 250ms ease-out 250ms;
  transition:  all 250ms ease-out 250ms;
}
.product-preview__info__btns .btn:first-child {
  left: -50%;
}
.product-preview__info__btns .btn:last-child {
  right: -50%;
}
.product-preview__info__description {
  display: none;
}
.product-preview__info__link {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 0.846em;
  text-transform: uppercase;
  overflow: hidden;
  position: relative;
  height: 0;
  opacity: 0;
  margin: 0 -30px;
  -webkit-transition:  all 200ms ease-out 0s;
  -moz-transition:  all 200ms ease-out 0s;
  -ms-transition:  all 200ms ease-out 0s;
  -o-transition:  all 200ms ease-out 0s;
  transition:  all 200ms ease-out 0s;
}
.product-preview__info__link .icon {
  padding: 0 5px;
  vertical-align: baseline;
}
.product-preview__info__link li {
  padding-bottom: 1em;
}
.product-preview__info__link a {
  text-decoration: none;
}
.product-preview__info__link--inline li {
  display: inline-block;
  padding-right: 3px;
}
.no-transition .product-preview__info__link {
  -webkit-transition:  all 0s ease-out 0s;
  -moz-transition:  all 0s ease-out 0s;
  -ms-transition:  all 0s ease-out 0s;
  -o-transition:  all 0s ease-out 0s;
  transition:  all 0s ease-out 0s;
}
.product-preview__info__link .icon {
  padding: 0 5px;
  vertical-align: baseline;
}
.product-preview__info__link a {
  text-decoration: none;
  white-space: nowrap;
}
.product-preview__info__link:after {
  pointer-events: none;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  content: '';
  opacity: 0;
  -webkit-transition: opacity 350ms ease-out 350ms, -webkit-transform 350ms ease-out 350ms;
  transition: opacity 350ms ease-out 350ms, transform 350ms ease-out 350ms;
  border-top-width: 1px;
  border-top-style: solid;
  -webkit-transform: scale(0);
  transform: scale(0);
}
.product-preview__info__link .buy-link {
  display: none;
}
.product-preview__info .rating {
  height: 0;
  overflow: hidden;
  margin: 0;
}
.no-touch .product-preview__info .rating {
  -webkit-transition:  all 200ms ease-out 0s;
  -moz-transition:  all 200ms ease-out 0s;
  -ms-transition:  all 200ms ease-out 0s;
  -o-transition:  all 200ms ease-out 0s;
  transition:  all 200ms ease-out 0s;
}
.no-transition .product-preview__info .rating {
  -webkit-transition:  all 0s ease-out 0s;
  -moz-transition:  all 0s ease-out 0s;
  -ms-transition:  all 0s ease-out 0s;
  -o-transition:  all 0s ease-out 0s;
  transition:  all 0s ease-out 0s;
}
.product-preview__outstock {
  font-size: 16px;
  line-height: 18px;
  position: absolute;
  text-align: center;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 60%;
  padding: 15px 15px 15px;
  margin: 0 20%;
  z-index: 999;
}
.product-preview__label {
  position: absolute;
  top: 0;
  width: 52px;
  height: 52px;
  font-size: 0.923em;
  text-transform: uppercase;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.row-view .product-preview__label {
  width: 35px;
  height: 35px;
  font-size: 0.7em;
}
.product-preview__label span {
  display: block;
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.product-preview__label--left {
  left: 15px;
}
.row-view .product-preview__label--left {
  left: 0;
}
.product-preview__label--right {
  right: 15px;
}
.row-view .product-preview__label--right {
  right: 0;
}
@media (min-width: 768px) {
  .product-preview:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  }
  .product-preview:hover .product-preview__info__btns {
    height: 70px;
    margin-top: -65px;
  }
  .product-preview:hover .product-preview__info__btns .btn {
    opacity: 1;
  }
  .product-preview:hover .product-preview__info__btns .btn:first-child {
    left: 0;
  }
  .product-preview:hover .product-preview__info__btns .btn:last-child {
    right: 0;
  }
  .product-preview:hover .product-preview__info__link {
    height: auto;
    padding-top: 12px;
    opacity: 1;
  }
  .product-preview:hover .product-preview__info__link:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
  .product-preview:hover .rating {
    height: 11px;
    margin: 0 0 10px 0;
  }
}
@media (max-width: 767px) {
  .product-preview .product-preview__info__btns {
    height: 70px;
    margin-top: -65px;
  }
  .product-preview .product-preview__info__btns .btn {
    opacity: 1;
  }
  .product-preview .product-preview__info__btns .btn:first-child {
    left: 0;
  }
  .product-preview .product-preview__info__btns .btn:last-child {
    right: 0;
  }
  .product-preview .product-preview__info__btns .btn.btn-quickview {
    display: none;
  }
  .products-listing .product-preview .product-preview__info__btns {
    display: none;
  }
  .product-preview .product-preview__info__link {
    position: absolute;
    right: 30px;
    bottom: 18px;
    height: auto;
    padding-top: 0;
    opacity: 1;
    font-size: 14px;
  }
  .product-preview .product-preview__info__link:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
    border: 0;
  }
  .product-preview .product-preview__info__link__text {
    display: none;
  }
  .products-listing .product-preview .product-preview__info__link {
    position: relative;
    right: auto;
    bottom: auto;
    padding: 15px 30px 5px;
    text-align: left;
  }
  .products-listing .product-preview .product-preview__info__link .buy-link {
    display: block;
    position: absolute;
    right: 20px;
    height: auto;
    bottom: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    background: transparent;
    padding: 0;
  }
  .products-listing .product-preview .product-preview__info__link .buy-link .icon {
    font-size: 26px;
  }
  .product-preview .product-preview__info__link a:hover > .icon {
    color: #212121;
  }
  .product-preview .rating {
    height: 11px;
    margin: 0 0 10px 0;
  }
}
.products-listing.row-view .product-preview-wrapper {
  width: 100% !important;
}
.products-listing.row-view .product-preview-wrapper .product-preview {
  position: relative;
  overflow: hidden;
  background: #ffffff;
  width: 100%;
  max-width: none;
  padding: 0;
}
@media (max-width: 991px) {
  .products-listing.row-view .product-preview-wrapper .product-preview {
    border-radius: 0;
  }
}
.products-listing.row-view .product-preview-wrapper .product-preview__image {
  position: relative;
  width: 30%;
  float: left;
  margin: 30px !important;
}
@media (max-width: 991px) {
  .products-listing.row-view .product-preview-wrapper .product-preview__image {
    margin: 22.5px !important;
    width: 38%;
  }
}
@media (max-width: 991px) {
  .products-listing.row-view .product-preview-wrapper .product-preview__image {
    margin: 12px !important;
    width: 38%;
  }
}
.products-listing.row-view .product-preview-wrapper .product-preview__info {
  position: static;
  bottom: auto;
  border-top: 0;
  width: 100%;
  padding: 30px;
  background: transparent;
  text-align: left;
}
.open .products-listing.row-view .product-preview-wrapper .product-preview__info {
  padding-bottom: 0;
}
.products-listing.row-view .product-preview-wrapper .product-preview__info__btns {
  display: none;
}
.products-listing.row-view .product-preview-wrapper .product-preview__info__description {
  display: block;
}
@media (max-width: 991px) {
  .products-listing.row-view .product-preview-wrapper .product-preview__info__description {
    display: none;
  }
}
@media (max-width: 991px) {
  .products-listing.row-view .product-preview-wrapper .product-preview__info {
    padding: 12px !important;
  }
}
.products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link {
  display: inline-block;
  margin-left: -15px;
  height: auto;
  min-height: 42px;
  line-height: 42px;
  padding: 15px 0 20px 15px;
  opacity: 1;
}
@media (max-width: 1200px) {
  .open .products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link {
    clear: both;
  }
}
.products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link .buy-link {
  display: inline-block;
  float: left;
  margin-right: 15px;
}
.products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link .buy-link .icon {
  font-size: 18px;
  vertical-align: sub !important;
}
.products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link:after {
  display: none;
}
@media (max-width: 991px) {
  .products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link {
    min-height: 32px;
    line-height: 32px;
    position: absolute;
    width: 62%;
    left: 0;
    bottom: 15px;
    margin-left: 38%;
    padding: 0 0 0 45px;
    font-size: 14px;
  }
  .products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link__text {
    display: none;
  }
  .products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link .buy-link {
    position: absolute;
    right: 0;
    height: auto;
    top: -3px;
    -webkit-box-shadow: none;
    box-shadow: none;
    background: transparent;
    padding: 0;
    margin: 0 5px 0 0;
  }
  .products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link .buy-link .icon {
    font-size: 26px;
  }
}
@media (max-width: 991px) {
  .products-listing.row-view .product-preview-wrapper .product-preview__info .product-preview__info__link {
    bottom: 0;
    padding: 0 0 0 24px;
  }
}
.products-listing.row-view .product-preview-wrapper .product-preview__info .rating {
  height: 11px;
  margin: 0 0 10px 0;
}
.rating {
  font-size: 10px;
  line-height: 1em;
}
.rating span {
  padding-right: 1px;
}
.rating .empty-star {
  opacity: 0.5;
}
.rating--big {
  font-size: 13px;
  line-height: 1.4em !important;
}
.rating--big span {
  padding-right: 2px;
}
.price-box {
  margin: 0 0 10px 0;
  font-size: 1.231em;
  line-height: 1em;
}
.price-box__new {
  font-weight: bold;
}
.price-box__old {
  font-size: 0.95em;
  font-weight: light;
  text-decoration: line-through;
}
.filters-col__collapse .products-widget .slick-slider.slick-vertical {
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .products-widget {
    margin-bottom: 30px;
  }
}
.products-widget__item {
  position: relative;
  padding: 11px 20px 13px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .products-widget__item {
    padding: 11px 10px 13px;
  }
}
.filters-col__collapse .products-widget__item {
  padding-left: 0;
  padding-right: 0;
}
.products-widget__item:after {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  content: '';
  height: 1px;
}
.filters-col .products-widget__item:after {
  display: none;
}
.products-widget__item__image {
  width: 74px;
}
.products-widget__item__image img {
  width: 100%;
  border-radius: 2px;
  -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.products-widget__item__image img:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.products-widget__item__info {
  overflow: hidden;
  padding: 0 20px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .products-widget__item__info {
    padding: 0 0 0 10px;
  }
}
.products-widget__item__info__title h2 {
  font-size: 1em;
  padding: 0 0 10px;
  margin: 0;
}
.products-widget__item__info .rating {
  padding-bottom: 10px;
}
.product-carousel {
  margin-bottom: -20px;
}
.product-carousel .slick-prev,
.product-carousel .slick-next {
  margin-top: -21px;
}
@media (max-width: 767px) {
  .product-carousel .slick-prev,
  .product-carousel .slick-next {
    margin-top: -72px;
  }
}
.product-main-image {
  margin: 0;
  padding: 0;
  list-style: none;
  position: relative;
}
.product-main-image__zoom {
  position: absolute;
  z-index: 10001;
  bottom: 0;
  right: 0;
  display: block;
  width: 46px;
  height: 46px;
  text-align: center;
  cursor: pointer;
}
.product-main-image__zoom:after {
  font-family: 'Welldone';
  font-size: 15px;
  line-height: 46px;
  content: "\e62b";
}
.product-main-image.no-zoom {
  position: relative;
}
.product-main-image.no-zoom .product-main-image__item {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
}
.product-main-image.no-zoom .product-main-image__item.active {
  z-index: 1;
}
.product-main-image img {
  width: 100%;
}
.product-images-carousel {
  padding: 0 25px;
  margin-top: 30px;
  width: 100%;
  position: relative;
}
.product-images-carousel .loader {
  opacity: 1;
}
.product-images-carousel ul {
  margin: 0;
  padding: 0;
  list-style: none;
  -webkit-transition:  all 300ms 0s ease;
  -moz-transition:  all 300ms 0s ease;
  -ms-transition:  all 300ms 0s ease;
  -o-transition:  all 300ms 0s ease;
  transition:  all 300ms 0s ease;
}
.modal .product-images-carousel ul {
  opacity: 0;
  height: 90px;
  overflow: hidden;
}
.product-images-carousel ul li {
  display: inline;
}
.product-images-carousel ul li a {
  display: block;
  margin: 0 4px;
}
.product-images-carousel .slick-prev {
  left: -28px;
}
.product-images-carousel .slick-next {
  right: -28px;
}
.product-images-carousel.loaded ul {
  opacity: 1;
  overflow: visible;
  height: auto;
}
.product-images-carousel.loaded .loader {
  display: none;
}
.product-images-carousel .video-link {
  display: block;
  position: relative;
  text-decoration: none;
}
.product-images-carousel .video-link:after {
  position: absolute;
  display: block;
  width: 100%;
  text-align: center;
  font-family: 'Welldone';
  font-size: 42px;
  line-height: 42px;
  content: "\e638";
  line-height: 1em;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.product-info__title h2 {
  padding-bottom: 20px;
}
@media (max-width: 767px) {
  .product-info__title h2 {
    text-align: left !important;
    padding-bottom: 8px;
  }
}
.product-info__price {
  font-size: 1.846em;
}
.product-info__description {
  line-height: 1.231em;
  padding-bottom: 20px;
}
.product-info__rating {
  padding-bottom: 20px;
}
@media (max-width: 767px) {
  .product-info__rating {
    padding-bottom: 10px;
  }
}
.product-info__sku {
  margin-top: 4px;
}
@media (max-width: 991px) {
  .product-info__sku {
    float: none !important;
    margin-bottom: 15px;
  }
}
.product-info__divider {
  border-top-width: 1px;
  border-top-style: solid;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .product-info .social-links {
    float: none !important;
    padding-top: 25px;
    clear: both;
  }
}
@media (max-width: 991px) {
  .product-info .grey-box {
    padding: 0;
  }
}
.input-qty {
  width: 62px;
  text-align: center;
  padding-right: 22px;
  padding-left: 5px;
}
.input-group-qty {
  padding-right: 10px;
}
.input-group-qty .btn-number-container {
  position: relative;
}
.input-group-qty .btn-number-container .btn-number {
  position: absolute;
  left: -18px;
  width: 18px;
  height: 21px;
  padding: 0;
  line-height: 18px;
  border-left-width: 1px;
  border-left-style: solid;
  border-top-width: 1px;
  border-top-style: solid;
  background: none;
  border-radius: 0;
}
.input-group-qty .btn-number-container .btn-number--minus {
  top: 19px;
}
.input-group-qty .btn-number-container .btn-number--plus {
  top: 0;
}
.product-other {
  margin: 0;
  padding: 11px 0 9px;
  list-style: none;
}
.product-other__link {
  display: inline-block;
  position: relative;
  text-transform: uppercase;
  cursor: pointer;
}
.product-other__link a {
  text-decoration: none;
}
.product-other__link__image {
  position: absolute;
  display: none;
  top: 23px;
  width: 55px;
  z-index: 100;
}
.product-other__link__image img {
  width: 100%;
  -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.1);
}
.product-other__link.show-image .product-other__link__image {
  display: block;
}
.product-other__link.product-prev {
  padding-left: 25px;
  padding-right: 50px;
}
.product-other__link.product-prev:after {
  position: absolute;
  display: block;
  top: 3px;
  left: 0;
  font-family: 'Welldone';
  font-size: 12px;
  content: "\e603";
  line-height: 1em;
}
.product-other__link.product-prev .product-other__link__image {
  left: 0;
}
.product-other__link.product-prev:last-child {
  padding-right: 0 !important;
}
.product-other__link.product-next {
  padding-right: 25px;
}
.product-other__link.product-next:after {
  position: absolute;
  display: block;
  top: 3px;
  right: 0;
  font-family: 'Welldone';
  font-size: 12px;
  content: "\e602";
  line-height: 1em;
}
.product-other__link.product-next .product-other__link__image {
  right: 0;
}
#singleGallery {
  margin: 0 0 35px;
  padding: 0;
  list-style: none;
}
#singleGallery .slick-slide {
  margin-bottom: 30px;
}
#singleGallery .slick-dots {
  bottom: -15px;
}
#singleGalleryVertical {
  margin: 0 0 35px;
  padding: 0;
  list-style: none;
}
#singleGalleryVertical .slick-dots {
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0;
  bottom: auto;
  width: 20px;
}
#singleGalleryVertical .slick-track {
  top: 0 !important;
}
#singleGalleryVertical .slick-slide {
  border: 0 !important;
}
.products-widget.card {
  margin-bottom: 25px;
}
.products-isotope {
  margin-bottom: 50px;
}
.product-category {
  position: relative;
  cursor: pointer;
  border-radius: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.product-category img {
  width: 100%;
  border-radius: 0;
}
.product-category__info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
.product-category__info__ribbon {
  margin-bottom: 18px;
  padding: 12px 0;
  background: rgba(255, 255, 255, 0.9);
  text-align: center;
}
.product-category__info__ribbon__title {
  padding: 0;
  margin: 0;
  font-size: 1.077em;
  text-transform: uppercase;
}
.product-category__info__ribbon__count {
  font-size: 0.923em;
}
.product-category:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.product-category-carousel.slick-slider,
.product-category-carousel-aside.slick-slider {
  margin: 0 -15px -20px;
}
.product-category-carousel .slick-slide,
.product-category-carousel-aside .slick-slide {
  margin: 0 15px 20px;
}
.product-category-carousel .slick-prev,
.product-category-carousel-aside .slick-prev,
.product-category-carousel .slick-next,
.product-category-carousel-aside .slick-next {
  margin-top: -21px;
}
@media (max-width: 767px) {
  .product-category-carousel .slick-prev,
  .product-category-carousel-aside .slick-prev,
  .product-category-carousel .slick-next,
  .product-category-carousel-aside .slick-next {
    margin-top: -72px;
  }
}
@media (max-width: 767px) {
  .product-category-carousel.nav-inside .slick-prev,
  .product-category-carousel-aside.nav-inside .slick-prev,
  .product-category-carousel.nav-inside .slick-next,
  .product-category-carousel-aside.nav-inside .slick-next {
    margin-top: -72px !important;
  }
}
.category-big-banner {
  position: relative;
  overflow: hidden;
}
.category-big-banner__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  background-size: cover;
  background-position: center center;
}
.no-touch .category-big-banner__image {
  -webkit-transition: 15s -webkit-transform linear;
  transition: 15s transform linear;
}
.no-touch .category-big-banner__image:hover {
  -webkit-transform: scale(1.3);
  -ms-transform: scale(1.3);
  transform: scale(1.3);
}
.category-big-banner__text {
  text-align: center;
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 100;
  color: #fff;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.category-big-banner__text .category-title {
  font-size: 2.2em;
  padding-bottom: 15px;
}
.category-big-banner__text p {
  font-size: 1.6em;
  margin-bottom: 20px;
}
.category-big-banner__text .btn {
  visibility: hidden;
  opacity: 0;
  margin-top: 50px;
  -webkit-transition:  all 300ms 0s ease;
  -moz-transition:  all 300ms 0s ease;
  -ms-transition:  all 300ms 0s ease;
  -o-transition:  all 300ms 0s ease;
  transition:  all 300ms 0s ease;
}
.category-big-banner:hover .category-big-banner__text .btn {
  visibility: visible;
  opacity: 1;
  margin-top: 0;
  -webkit-transition:  all 300ms 0s ease;
  -moz-transition:  all 300ms 0s ease;
  -ms-transition:  all 300ms 0s ease;
  -o-transition:  all 300ms 0s ease;
  transition:  all 300ms 0s ease;
}
body.modal-open {
  overflow-y: scroll;
  padding-right: 0 !important;
}
.modal {
  z-index: 10003;
  text-align: center;
  padding: 0 !important;
}
.modal.quick-view .modal-dialog {
  width: 940px;
  max-width: 90%;
  -webkit-transition:  all 300ms 0s ease;
  -moz-transition:  all 300ms 0s ease;
  -ms-transition:  all 300ms 0s ease;
  -o-transition:  all 300ms 0s ease;
  transition:  all 300ms 0s ease;
}
.modal:before {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
  margin-right: -4px;
  /* Adjusts for spacing */
}
.modal .modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
  overflow: hidden;
}
.modal .modal-content {
  padding: 35px 20px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  border-radius: 0;
}
.modal button.close {
  margin: -20px -5px 0 0;
  font-size: 14px;
  opacity: 1;
}
.modal--bg .modal-dialog {
  width: auto !important;
  max-width: 678px;
  position: relative;
  padding: 0;
}
.modal--bg .modal-header {
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
  border: 0;
  padding: 14px;
}
.modal--bg .modal-header button.close {
  margin: 0;
  font-size: 20px;
  opacity: 1;
}
.modal--bg .modal-content {
  text-align: center;
  background: transparent;
  border: 0;
  width: 100%;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
@media (min-width: 768px) {
  .modal--bg .modal-content {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    padding: 0 75px;
  }
}
@media (max-width: 991px) {
  .modal--bg .modal-content {
    padding: 0 25px;
  }
}
@media (max-width: 767px) {
  .modal--bg .modal-content {
    padding: 45px 15px;
  }
}
.modal--bg .modal-content h1,
.modal--bg .modal-content h2,
.modal--bg .modal-content h3 {
  padding-bottom: 15px;
}
.modal--bg .modal-content input {
  max-width: 400px;
  margin: 25px 0 20px 0;
  padding: 11px 15px;
  height: 44px;
}
.modal--bg .modal-body {
  padding: 0;
}
.modal--bg .modal-footer {
  border: 0;
}
.modal--bg .popup-dont-show {
  position: absolute;
  bottom: 40px;
  left: 20px;
  text-align: left;
  color: #3b3434;
  font-size: 10px;
  font-weight: 300;
}
@media (max-width: 767px) {
  .modal--bg .modal-bg-image {
    display: none;
  }
}
.modal-backdrop {
  z-index: 10002;
}
.overlay {
  z-index: 10002;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.overlay .overlay-close {
  width: 80px;
  height: 80px;
  position: absolute;
  right: 20px;
  top: 20px;
  overflow: hidden;
  border: none;
  font-size: 40px;
  background: transparent;
  outline: none;
  z-index: 100;
}
.overlay__content {
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 60%;
  margin: 0 auto;
  text-align: center;
}
.overlay-scale {
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(0.9);
  transform: scale(0.9);
  -webkit-transition: -webkit-transform 0.2s, opacity 0.2s, visibility 0s 0.2s;
  transition: transform 0.2s, opacity 0.2s, visibility 0s 0.2s;
}
.overlay-scale.open {
  visibility: visible;
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.4s;
  transition: transform 0.4s, opacity 0.4s;
}
.overlay .input-group--wd input {
  text-align: center;
  font-size: 36px;
  line-height: 42px;
  font-weight: 300;
}
@media (max-width: 991px) {
  .overlay .input-group--wd input {
    font-size: 25px;
    line-height: 36px;
  }
}
@media screen and (max-height: 30.5em) {
  .overlay nav {
    height: 70%;
    font-size: 34px;
  }
  .overlay ul li {
    min-height: 34px;
  }
}
body.modal-open {
  overflow: hidden !important;
}
.loader {
  height: 50px;
  left: 50%;
  margin: -25px 0 0 -75px;
  position: absolute;
  top: 50%;
  width: 150px;
}
.loader .bar {
  font-size: 0;
  height: 3px;
  opacity: 0;
  width: 25px;
  -webkit-animation: subtleIn 2s ease-in-out infinite;
  -moz-animation: subtleIn 2s ease-in-out infinite;
  -ms-animation: subtleIn 2s ease-in-out infinite;
  animation: subtleIn 2s ease-in-out infinite;
  display: inline-block;
  vertical-align: middle;
  *vertical-align: auto;
  *zoom: 1;
  *display: inline;
}
.loader .bar:nth-child(2) {
  -webkit-animation-delay: .3s;
  animation-delay: .3s;
}
.loader .bar:nth-child(3) {
  -webkit-animation-delay: .6s;
  animation-delay: .6s;
}
.loader .bar:nth-child(4) {
  -webkit-animation-delay: .9s;
  animation-delay: .9s;
}
.loader .bar:nth-child(5) {
  -webkit-animation-delay: 1.2s;
  animation-delay: 1.2s;
}
@-moz-keyframes subtleIn {
  0% {
    opacity: 0;
    -moz-transform: translateY(300%);
    -ms-transform: translateY(300%);
    -webkit-transform: translateY(300%);
    transform: translateY(300%);
  }
  30% {
    opacity: 1;
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@-webkit-keyframes subtleIn {
  0% {
    opacity: 0;
    -moz-transform: translateY(300%);
    -ms-transform: translateY(300%);
    -webkit-transform: translateY(300%);
    transform: translateY(300%);
  }
  30% {
    opacity: 1;
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes subtleIn {
  0% {
    opacity: 0;
    -moz-transform: translateY(300%);
    -ms-transform: translateY(300%);
    -webkit-transform: translateY(300%);
    transform: translateY(300%);
  }
  30% {
    opacity: 1;
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
.options-swatch {
  margin: 0 0 10px 0;
  padding: 0;
  list-style: none;
}
.options-swatch li {
  display: inline-block;
  width: 24px;
  height: 23px;
  border-width: 2px;
  border-style: solid;
  cursor: pointer;
  font-size: 11px;
  line-height: 15px;
  text-align: center;
  -webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.25);
}
.options-swatch li:hover {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.options-swatch img {
  width: 100%;
  height: 100%;
}
.options-swatch--lg li {
  width: 34px;
  height: 32px;
  border-width: 3px;
  font-size: 11px;
  line-height: 22px;
}
.options-swatch--size li {
  padding: 0 !important;
  line-height: 19px;
}
.options-swatch--size.options-swatch--lg li {
  line-height: 26px;
}
.color-icon {
  display: inline-block;
  width: 17px;
  height: 17px;
  margin-right: 8px;
  cursor: pointer;
  font-size: 11px;
  line-height: 15px;
  text-align: center;
  vertical-align: text-top;
}
.color-icon img {
  width: 100%;
  height: 100%;
}
@media (max-width: 991px) {
  .compare-box {
    display: none;
  }
}
body.compare-minimize {
  padding-bottom: 48px;
}
.compare-box {
  position: fixed;
  z-index: 10001;
  left: 0;
  right: 0;
  height: 220px;
  bottom: -220px;
  padding: 20px 0;
  border-top-width: 1px;
  border-top-style: solid;
  border-top-color: #f0f0f0;
  background: rgba(255, 255, 255, 0.95);
  overflow: hidden;
  -webkit-transition:  all 300ms 0s ease;
  -moz-transition:  all 300ms 0s ease;
  -ms-transition:  all 300ms 0s ease;
  -o-transition:  all 300ms 0s ease;
  transition:  all 300ms 0s ease;
}
.compare-box .container {
  position: relative;
}
.compare-box.open {
  bottom: 0;
}
.compare-box.open__header__toggle__hide {
  display: block;
}
.compare-box.open__header__toggle__show {
  display: none;
}
.compare-box.minimize {
  bottom: 0;
  height: 48px;
  padding: 10px 0;
}
.compare-box__header {
  position: relative;
}
.compare-box__header__title {
  padding-bottom: 20px;
}
.minimize .compare-box__header__title {
  padding-bottom: 10px;
}
.compare-box__header__toggle {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 1.231em;
  line-height: 1em;
  text-transform: uppercase;
  cursor: pointer;
}
.minimize .compare-box__header__toggle {
  top: 5px;
}
.compare-box__header__toggle .icon {
  vertical-align: top;
  font-weight: bold;
  font-size: 12px;
  padding: 0 5px 0 10px;
  cursor: pointer;
}
.compare-box__header__toggle__hide {
  display: inline-block;
}
.minimize .compare-box__header__toggle__hide {
  display: none;
}
.compare-box__header__toggle__show {
  display: none;
}
.minimize .compare-box__header__toggle__show {
  display: inline-block;
}
.compare-box__header__toggle__close {
  font-size: 14px;
  padding-left: 15px;
}
.compare-box__actions {
  position: absolute;
  right: 0;
  top: 50px;
  width: 150px;
}
.compare-box__actions__btns {
  width: 135px;
}
.compare-box__actions .btn {
  width: 100%;
  margin: 7px 0;
}
.compare-box__items {
  margin-right: 200px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .compare-box__items {
    width: 655px;
  }
}
.compare-box__items .owl-welldone.nav-top .owl-nav {
  margin-top: -40px;
}
.minimize .compare-box__items .owl-welldone.nav-top .owl-nav {
  display: none;
}
.compare-box__items__list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.compare-box__items__list__item {
  display: block;
  float: left;
  position: relative;
  width: 115px;
  margin-left: 20px;
  margin-bottom: 100px;
  vertical-align: top;
  border-width: 4px;
  border-style: solid;
  -webkit-transition:  border-color 200ms 0s ease;
  -moz-transition:  border-color 200ms 0s ease;
  -ms-transition:  border-color 200ms 0s ease;
  -o-transition:  border-color 200ms 0s ease;
  transition:  border-color 200ms 0s ease;
}
.compare-box__items__list__item:first-child {
  margin-left: 0;
}
.compare-box__items__list__item.empty {
  border-width: 4px;
  border-style: dashed;
}
.compare-box__items__list__item img {
  width: 100%;
}
.compare-box__items__list__item__delete {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 10px;
}
.compare-box__items__list__item__num {
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  margin-top: -19px;
  text-align: center;
  font-size: 38px;
  line-height: 1em;
  font-weight: bold;
}
.compare-carousel.slick-slider {
  margin: 0 -10px !important;
}
.compare-carousel .slick-slide {
  width: 115px !important;
  margin: 0 10px !important;
}
.compare-carousel .slick-prev {
  right: 45px !important;
}
.minimize .compare-carousel .slick-prev {
  opacity: 0 !important;
}
.compare-carousel .slick-next {
  right: 10px !important;
}
.minimize .compare-carousel .slick-next {
  opacity: 0 !important;
}
@media (min-width: 0) and (max-width: 479px) {
  .compare-carousel .slick-next {
    top: 50%;
    margin-top: -11px;
    right: 22px !important;
  }
  .compare-carousel .slick-prev {
    top: 50%;
    margin-top: -11px;
    left: 22px !important;
  }
}
.flag {
  width: 16px;
  height: 11px;
  display: inline-block;
  vertical-align: middle;
}
.flag img {
  vertical-align: top;
}
.tp-banner-container {
  width: 100%;
  position: relative;
  padding: 0;
  opacity: 0;
  height: 2000px;
}
.tp-banner {
  width: 100%;
  position: relative;
}
.tp-banner-fullscreen-container {
  width: 100%;
  position: relative;
  padding: 0;
}
.tp-caption--wd-1 {
  font-size: 115px;
  line-height: 1em;
  font-family: 'Ubuntu', sans-serif;
  font-weight: bold;
}
.tp-caption--wd-2 {
  font-size: 84px;
  line-height: 1em;
  font-weight: lighter;
  font-family: 'Ubuntu', sans-serif;
}
.tp-caption--wd-3 {
  font-size: 26px;
  line-height: 1em;
  font-family: 'Roboto', sans-serif;
}
.tp-caption--wd-4 {
  font-size: 64px;
  line-height: 1em;
  font-family: 'Ubuntu', sans-serif;
  font-weight: bold;
}
.tp-caption--wd-5 {
  font-size: 44px;
  line-height: 1em;
  font-family: 'Ubuntu', sans-serif;
}
.tp-caption--wd-6 {
  font-size: 16px;
  line-height: 1em;
  font-family: 'Roboto', sans-serif;
}
.tp-caption--wd-7 {
  font-size: 64px;
  line-height: 1em;
  font-family: 'Roboto', sans-serif;
}
.tp-caption--wd-8 {
  font-size: 36px;
  line-height: 1em;
  font-family: 'Ubuntu', sans-serif;
}
.tp-caption .btn--wd {
  font-size: 18px;
}
@media (max-width: 767px) {
  .tp-caption .btn--wd.btn--xl {
    font-size: 16px;
    padding: 12px 20px;
  }
}
.scroll-to-content {
  position: absolute;
  z-index: 100;
  bottom: 15px;
  left: 50%;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  /* note that you have @transform here */
  -o-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  text-align: center;
  -webkit-transition:  all 300ms ease-out 0s;
  -moz-transition:  all 300ms ease-out 0s;
  -ms-transition:  all 300ms ease-out 0s;
  -o-transition:  all 300ms ease-out 0s;
  transition:  all 300ms ease-out 0s;
}
.scroll-to-content .icon {
  position: absolute;
  top: 26px;
  left: 24px;
}
.scroll-to-content:hover {
  bottom: 5px;
}
.scroll-to-content:hover .icon {
  -webkit-animation: scroll-ani 800ms linear infinite;
  -moz-animation: scroll-ani 800ms linear infinite;
  animation: scroll-ani 800ms linear infinite;
}
.single-slider {
  opacity: 0;
  -webkit-transition:  opacity 300ms ease-out 0s;
  -moz-transition:  opacity 300ms ease-out 0s;
  -ms-transition:  opacity 300ms ease-out 0s;
  -o-transition:  opacity 300ms ease-out 0s;
  transition:  opacity 300ms ease-out 0s;
}
.single-slider li {
  position: relative;
}
.single-slider__text {
  position: absolute;
  width: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  text-align: center;
}
.single-slider__text h2 {
  font-size: 68px;
  line-height: 1em;
  padding: 0;
}
@media (max-width: 479px) {
  .single-slider__text h2 {
    font-size: 42px;
  }
}
.single-slider__text h3 {
  font-size: 40px;
  line-height: 1em;
  padding: 0 0 10px;
}
@media (max-width: 479px) {
  .single-slider__text h3 {
    font-size: 24px;
    padding: 0 0 5px;
  }
}
.single-slider__text h4 {
  font-size: 17px;
  padding: 0 0 25px;
}
@media (max-width: 479px) {
  .single-slider__text h4 {
    font-size: 14px;
    padding: 0 0 10px;
  }
}
.banner {
  position: relative;
  cursor: pointer;
  line-height: 1em;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  transform-style: preserve-3d;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.banner:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.banner--image {
  display: inline-block;
}
@media (max-width: 991px) {
  .banner--image {
    margin-bottom: 34px;
  }
}
.banner--image img {
  width: 100%;
  max-width: 100%;
}
.banner--image__text {
  position: absolute;
  padding: 2%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.banner--image__text--bottom {
  top: 70%;
}
.banner--image__text--left {
  width: 45%;
  left: 6%;
}
.banner--image__text--right {
  width: 45%;
  right: 6%;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .banner--image__text--left {
    width: 55%;
    left: 6%;
  }
  .banner--image__text--right {
    width: 55%;
    right: 6%;
  }
}
.banner--image__text .btn {
  margin-top: 1.2em;
}
@media (max-width: 767px) {
  .banner--image__text .btn {
    margin-top: 0.8em;
  }
}
.banner--image__text h2 {
  font-size: 5.538em;
  line-height: 1em;
  padding-bottom: 0.2em;
  font-family: 'Ubuntu', sans-serif;
}
@media (max-width: 1199px) {
  .banner--image__text h2 {
    font-size: 4.8em;
    line-height: 0.9em;
  }
}
@media (max-width: 991px) {
  .banner--image__text h2 {
    font-size: 3.8em;
    line-height: 0.8em;
  }
}
@media (max-width: 767px) {
  .banner--image__text h2 {
    font-size: 2.8em;
    line-height: 0.8em;
  }
}
.banner--image__text h3 {
  font-size: 5.538em;
  line-height: 1em;
  padding-bottom: 0;
  font-family: 'Ubuntu', sans-serif;
}
@media (max-width: 1199px) {
  .banner--image__text h3 {
    font-size: 4.2em;
    line-height: 0.9em;
  }
}
@media (max-width: 991px) {
  .banner--image__text h3 {
    font-size: 3.2em;
    line-height: 0.8em;
  }
}
@media (max-width: 767px) {
  .banner--image__text h3 {
    font-size: 2.2em;
    line-height: 0.8em;
  }
}
.banner--image__text h5 {
  font-size: 1.654em;
  line-height: 1.2em;
  padding-bottom: 0;
  font-family: 'Ubuntu', sans-serif;
  font-weight: 300;
}
@media (max-width: 767px) {
  .banner--image__text h5 {
    font-size: 1.3em;
  }
}
.banner--image__text h6 {
  font-size: 1.308em;
  line-height: 1.3em;
  padding-bottom: 0;
  font-family: 'Ubuntu', sans-serif;
  font-weight: 300;
}
@media (max-width: 767px) {
  .banner--image__text h6 {
    font-size: 1.1em;
  }
}
.banner--icon {
  margin-bottom: 34px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .banner--icon {
    margin-bottom: 10px;
  }
}
.banner--icon.banner--last {
  margin-bottom: 0;
}
.banner--icon__icon {
  padding: 0.75em 0 0.6em 0;
  width: 1.643em;
  font-size: 4.615em;
  line-height: 1em;
  text-align: center;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .banner--icon__icon {
    padding: 0.38em 0 0.4em 0;
  }
}
.banner--icon__text {
  position: absolute;
  width: 100%;
  padding: 0 5% 0 8.582445em;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.banner--icon__text h4 {
  font-size: 1.846em;
  padding: 0;
  margin: 0;
}
.banner--icon__text__divider {
  height: 10px;
  border-bottom: 1px solid #ffffff;
  margin-bottom: 10px;
}
@media (max-width: 480px) {
  .banner {
    font-size: 8.5px;
  }
}
.banner-collection,
#bannerCollection [class*="col-"] {
  float: none;
  display: inline-block;
  vertical-align: top;
  margin-right: -4px;
  margin-bottom: 45px;
}
@media (max-width: 767px) {
  .banner-collection,
  #bannerCollection [class*="col-"] {
    display: block;
    margin-right: 0;
  }
}
.banner-collection img,
#bannerCollection [class*="col-"] img {
  max-width: 100%;
}
.banner-collection .banner-collection__title,
#bannerCollection [class*="col-"] .banner-collection__title {
  margin: 15px 0 15px;
  padding: 0;
}
.blog-widget {
  position: relative;
}
.boxed .blog-widget {
  padding-left: 60px;
  padding-right: 60px;
}
.boxed .blog-widget .row {
  margin-left: 0;
  margin-right: 0;
}
.blog-widget__title {
  padding-left: 15px;
  margin-left: 37%;
}
.show-2 .blog-widget__title,
.show-3 .blog-widget__title,
.show-5 .blog-widget__title,
.blog-widget--one-center .blog-widget__title {
  padding-left: 0;
  margin-left: 0;
}
@media (max-width: 767px) {
  .blog-widget__title {
    text-align: center;
    padding-left: 0;
    margin-left: 0;
  }
}
.blog-widget .slick-list {
  padding-bottom: 30px;
  z-index: 100;
}
.blog-widget__item {
  position: relative;
  padding-top: 45px;
}
@media (max-width: 991px) {
  .show-2 .blog-widget__item {
    padding-top: 20px;
  }
}
@media (max-width: 767px) {
  .blog-widget__item {
    padding-top: 20px;
  }
}
.blog-widget--one-center .blog-widget__item {
  display: block;
  overflow: hidden;
  padding-top: 20px;
  max-width: 490px;
  text-align: center;
  margin: 0 auto;
  margin-bottom: 75px;
}
.blog-widget__item:after {
  position: absolute;
  display: block;
  top: 0;
  right: 15px;
  font-family: 'Welldone';
  font-size: 60px;
  content: "\e63b";
  line-height: 1em;
}
.blog-widget--one-center .blog-widget__item:after {
  top: 0;
  right: 2px;
}
@media (min-width: 768px) {
  .show-3 .blog-widget__item:after {
    top: 50px;
  }
  .show-4 .blog-widget__item:after {
    top: 50px;
  }
}
@media (max-width: 991px) {
  .show-2 .blog-widget__item:after {
    top: 0;
    right: 2px;
  }
}
@media (max-width: 767px) {
  .blog-widget__item:after {
    top: 0;
    right: 2px;
  }
}
.blog-widget__item__image-cell {
  width: 22%;
  padding-left: 2px;
  padding-right: 15px;
}
@media (min-width: 1200px) {
  .blog-widget__item__image-cell {
    width: 20.5%;
    margin-left: 16.5%;
  }
  .show-2 .blog-widget__item__image-cell,
  .show-3 .blog-widget__item__image-cell,
  .show-5 .blog-widget__item__image-cell,
  .blog-widget--one-center .blog-widget__item__image-cell {
    margin-left: 0;
  }
}
.show-2 .blog-widget__item__image-cell {
  width: 50%;
}
.show-3 .blog-widget__item__image-cell {
  width: 100%;
}
@media (min-width: 768px) {
  .show-3 .blog-widget__item__image-cell {
    padding-right: 95px;
  }
}
.show-4 .blog-widget__item__image-cell {
  width: 100%;
}
@media (min-width: 768px) {
  .show-4 .blog-widget__item__image-cell {
    padding-right: 95px;
  }
}
@media (max-width: 991px) {
  .blog-widget__item__image-cell {
    width: 30%;
  }
  .show-2 .blog-widget__item__image-cell {
    width: 100%;
  }
  .show-3 .blog-widget__item__image-cell {
    width: 100%;
  }
  .show-4 .blog-widget__item__image-cell {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .blog-widget__item__image-cell {
    width: 100% !important;
  }
}
.blog-widget--one-center .blog-widget__item__image-cell {
  width: 100% !important;
}
.blog-widget__item__image-cell img {
  border-radius: 50%;
  width: 100%;
  max-width: 100%;
  -webkit-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 5px 5px 0 rgba(0, 0, 0, 0.25);
}
.show-3 .blog-widget__item__image-cell img {
  margin: 0 auto 10px;
}
.show-4 .blog-widget__item__image-cell img {
  margin: 0 auto 10px;
}
@media (max-width: 991px) {
  .show-2 .blog-widget__item__image-cell img {
    max-width: 60% !important;
    margin: 0 auto 25px;
  }
}
@media (max-width: 767px) {
  .blog-widget__item__image-cell img {
    max-width: 60% !important;
    margin: 0 auto 25px;
  }
}
.blog-widget--one-center .blog-widget__item__image-cell img {
  max-width: 60% !important;
  margin: 0 auto 25px;
}
.blog-widget__item__offset-text {
  width: 78%;
  padding-left: 15px;
  padding-right: 15px;
}
@media (min-width: 1200px) {
  .blog-widget__item__offset-text {
    width: 46.5%;
    margin-right: 16.5%;
    padding-top: 15px;
  }
  .show-2 .blog-widget__item__offset-text,
  .show-3 .blog-widget__item__offset-text,
  .show-5 .blog-widget__item__offset-text,
  .blog-widget--one-center .blog-widget__item__offset-text {
    margin-right: 0;
  }
}
.show-2 .blog-widget__item__offset-text {
  width: 50%;
}
.show-3 .blog-widget__item__offset-text {
  width: 100%;
  text-align: center;
}
@media (min-width: 768px) {
  .show-3 .blog-widget__item__offset-text {
    padding-right: 95px;
  }
}
.show-4 .blog-widget__item__offset-text {
  width: 100%;
  text-align: center;
}
@media (min-width: 768px) {
  .show-4 .blog-widget__item__offset-text {
    padding-right: 95px;
  }
}
@media (max-width: 991px) {
  .blog-widget__item__offset-text {
    width: 70%;
  }
  .show-2 .blog-widget__item__offset-text {
    width: 100%;
  }
  .show-3 .blog-widget__item__offset-text {
    width: 100%;
  }
  .show-4 .blog-widget__item__offset-text {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .blog-widget__item__offset-text {
    width: 100% !important;
  }
}
@media (max-width: 767px) {
  .blog-widget__item__offset-text {
    text-align: center;
  }
}
.blog-widget--one-center .blog-widget__item__offset-text {
  width: 100% !important;
}
.blog-widget__item__offset-text__title {
  padding-bottom: 15px;
}
.show-3 .blog-widget__item__offset-text__title {
  font-size: 1.538em;
  padding-bottom: 5px;
}
.show-4 .blog-widget__item__offset-text__title {
  font-size: 1.538em;
  padding-bottom: 5px;
}
.blog-widget__item__offset-text__date {
  overflow: hidden;
  font-size: 0.923em;
  padding-bottom: 20px;
}
.blog-widget__item__offset-text__date span {
  display: inline-block;
  vertical-align: baseline;
  zoom: 1;
  position: relative;
  padding: 0 10px 0 0;
}
.blog-widget--one-center .blog-widget__item__offset-text__date span {
  padding: 0 10px;
}
.blog-widget__item__offset-text__date span:after {
  content: '';
  left: 100%;
  display: block;
  width: 1000px;
  position: absolute;
  top: 0.73em;
  border-top-width: 1px;
  border-top-style: solid;
}
@media (max-width: 767px) {
  .blog-widget__item__offset-text__date span:after {
    display: none;
  }
}
.show-3 .blog-widget__item__offset-text__date span:after {
  display: none;
}
.show-4 .blog-widget__item__offset-text__date span:after {
  display: none;
}
.blog-widget--one-center .blog-widget__item__offset-text__date span:before {
  content: '';
  right: 100%;
  display: block;
  width: 1000px;
  position: absolute;
  top: 0.73em;
  border-top-width: 1px;
  border-top-style: solid;
}
@media (max-width: 767px) {
  .blog-widget--one-center .blog-widget__item__offset-text__date span:before {
    display: none;
  }
}
.blog-widget__item__offset-text p {
  font-size: 1.077em;
  line-height: 1.429em;
  margin-bottom: 20px;
}
.blog-widget .blog-carousel {
  margin-top: -25px;
}
@media (max-width: 767px) {
  .blog-widget .blog-carousel {
    margin: 0 20px;
  }
  .blog-widget .blog-carousel .slick-prev {
    left: -35px;
  }
  .blog-widget .blog-carousel .slick-next {
    right: -35px;
  }
}
@media (max-width: 767px) {
  .blog-widget .blog-carousel {
    margin: 0 20px;
  }
  .blog-widget .blog-carousel .slick-prev {
    left: -35px;
  }
  .blog-widget .blog-carousel .slick-next {
    right: -35px;
  }
}
@media (min-width: 1200px) {
  .blog-widget .blog-carousel:not(.show-2):not(.show-3):not(.show-4):not(.blog-widget--one-center) .slick-prev {
    left: 10%;
  }
  .blog-widget .blog-carousel:not(.show-2):not(.show-3):not(.show-4):not(.blog-widget--one-center) .slick-next {
    right: 10%;
  }
}
.content--parallax .blog-post-title {
  text-align: center;
}
.blog-post-title__title {
  padding-bottom: 20px;
}
.blog-post-title__meta {
  width: 250px;
  margin: 0 auto;
}
.blog-post-title__meta__text {
  text-align: left;
  padding: 12px 0 0;
}
.blog-post-title__meta__text__name {
  padding-bottom: 4px;
  font-style: italic;
}
.blog-post-title__meta__text__date {
  font-size: 0.923em;
}
.blog-post-title__meta__image {
  width: 70px;
  float: left;
  margin: 0 15px;
}
.blog-post-title__meta__image img {
  width: 100%;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.blog-post-title__meta__image img:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.blog-post-content .tags-list {
  margin: 50px 0;
}
.post {
  margin-bottom: 60px;
}
.post p a {
  text-decoration: underline;
}
.post p a:hover {
  text-decoration: none;
}
.post--fill {
  padding: 50px 50px;
  margin-left: -50px;
  margin-right: -50px;
}
.post__title {
  padding-bottom: 12px;
}
.post__title a {
  text-decoration: none;
}
.post--column {
  float: left;
  width: 24.9%;
  margin-bottom: 0;
  padding: 20px 20px 6.66666667px;
  border-radius: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.post--column:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  z-index: 100;
}
.post__image {
  display: block;
  margin: -20px -20px 0 -20px;
}
.post__image img {
  width: 100%;
  border-radius: 0 0 0 0;
}
.post__view-btn {
  position: relative;
  width: 100%;
  margin: -30px 0 15px;
}
.post__meta {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 0.846em;
  text-transform: uppercase;
  text-transform: none;
  padding-bottom: 35px;
}
.post__meta .icon {
  padding: 0 5px;
  vertical-align: baseline;
}
.post__meta li {
  padding-bottom: 1em;
}
.post__meta a {
  text-decoration: none;
}
.post__meta--inline li {
  display: inline-block;
  padding-right: 3px;
}
.post--column .post__meta {
  position: relative;
  padding-top: 6.66666667px;
  padding-bottom: 0;
}
.post__meta__item {
  margin-right: 7px;
}
.post__meta__date {
  display: block;
}
.post__meta__author {
  display: block;
}
.post__meta .icon {
  padding: 0 5px 0 0;
  vertical-align: baseline;
}
.post__meta a {
  text-decoration: none;
}
.post--column .post__meta:after {
  pointer-events: none;
  position: absolute;
  margin: 0 -20px;
  top: 0;
  right: 0;
  left: 0;
  content: '';
  border-top-width: 1px;
  border-top-style: solid;
}
.btn-plus {
  position: relative;
}
.btn-plus__links {
  position: absolute;
  top: 10px;
  right: 0;
  opacity: 0;
  width: 150px;
  transition: all 0.3s ease;
  text-align: right;
}
.btn-plus__links__icon {
  border-radius: 999em;
  width: 60px;
  height: 60px;
  padding: 0;
  line-height: 65px;
  font-size: 32px;
  position: relative;
  cursor: pointer;
  display: inline-block;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
  text-align: center;
  vertical-align: top;
  width: 42px;
  height: 42px;
  line-height: 50px;
  font-size: 22px;
}
.btn-plus__links__icon:hover {
  text-decoration: none;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.btn-plus__links__icon--lg {
  width: 76px;
  height: 76px;
  line-height: 78px;
  font-size: 27px;
}
.btn-plus__links__icon--sm {
  width: 46px;
  height: 46px;
  line-height: 78px;
  font-size: 22px;
}
.btn-plus__links__icon--xs {
  width: 37px;
  height: 37px;
  line-height: 37px;
  font-size: 16px;
}
.btn-plus__links--active {
  opacity: 1;
  right: 64px;
}
.btn-plus__toggle {
  display: block;
  margin: 0 0 0 50%;
  left: -30px;
  z-index: 2;
  transition: all 0.3s ease;
}
.btn-plus__toggle.expanded {
  margin: 0 0 0 100%;
  left: -60px;
  line-height: 58px;
}
.btn-plus__toggle.expanded span.icon:before {
  font-family: Garamond, "Apple Garamond";
  content: "\00D7";
}
.blog-content {
  padding: 35px 50px;
  -webkit-box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
}
.blog-content .img-left {
  margin-left: -50px;
}
.blog-content .img-right {
  margin-right: -50px;
}
.blog-content ul ul,
.blog-content ol ul,
.blog-content ul ol {
  list-style-type: inherit;
}
.audio-content {
  position: relative;
  margin-left: 70px;
  margin-bottom: 35px;
  width: auto;
  padding-left: 15px;
  border-left-width: 1px;
  border-left-style: solid;
}
.audio-content:before {
  display: block;
  position: absolute;
  left: -70px;
  top: 10px;
  font-family: "Welldone";
  content: "\e671";
  font-size: 36px;
}
.post .embed-vimeo iframe,
.post .embed-youtube iframe {
  width: 100%;
}
.post--status {
  padding: 50px 50px;
  margin-left: -50px;
  margin-right: -50px;
  border-left-width: 4px;
  border-left-style: solid;
}
.post--status .post__meta {
  padding-bottom: 0;
}
.post--status p {
  font-size: 26px;
  line-height: 1.2em;
  font-style: italic;
  font-weight: 300;
  margin-bottom: 25px;
}
.post--quotes {
  padding: 50px 50px;
  margin-left: -50px;
  margin-right: -50px;
}
.post--quotes .post__meta {
  padding-bottom: 0;
}
.post--quotes .post__quote {
  font-size: 26px;
  line-height: 1.2em;
  font-style: italic;
  font-weight: 300;
  margin-bottom: 25px;
  position: relative;
  padding-left: 50px;
}
.post--quotes .post__quote:after {
  position: absolute;
  display: block;
  top: 4px;
  left: 0;
  font-family: Welldone;
  font-size: 32px;
  line-height: 1em;
  content: "\e63b";
}
.post--quotes .post__quote cite {
  font-size: 13px;
}
.post--format-link .post__title {
  display: inline-block;
  font-size: 2em;
  line-height: 1.2em;
  font-style: italic;
  padding-right: 20px;
}
.post--format-link .post__meta {
  display: inline;
}
.brands__item img {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.brands__item:hover img {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.brands .product-category-carousel .slick-slide {
  margin: 0 15px;
}
.brands-carousel .slick-slide {
  margin: 2px 15px 10px;
}
.brands-carousel.slick-slider {
  margin: 0 -15px;
}
.brands-carousel .slick-prev,
.brands-carousel .slick-next {
  margin-top: -16px;
}
@media (max-width: 767px) {
  .brands-carousel .slick-prev,
  .brands-carousel .slick-next {
    margin-top: -67px;
  }
}
@media (max-width: 767px) {
  .brands-carousel.nav-inside .slick-prev,
  .brands-carousel.nav-inside .slick-next {
    margin-top: -67px !important;
  }
}
.testimonials__item {
  border-radius: 3px;
  border-width: 1px;
  border-style: solid;
}
.testimonials__item-title {
  cursor: pointer;
  padding: 20px 0;
  margin-top: 0 !important;
  position: relative;
  font-size: 1.385em;
  background-color: #f8f8f8;
  padding: 18px 45px 18px 25px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.card--collapse.open .testimonials__item-title {
  display: block;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.testimonials__item-title:after {
  position: absolute;
  display: block;
  top: 24px;
  right: 12px;
  font-family: 'Welldone';
  font-size: 10px;
  content: "\e601";
  line-height: 1em;
}
.open > .testimonials__item-title:after {
  content: "\e631";
}
.testimonials__item-content {
  display: none;
  padding: 18px 25px;
}
.card--collapse.open .testimonials__item-content {
  display: block;
}
.testimonials__item--padding {
  padding: 20px 25px;
}
.testimonials__item--form {
  padding: 20px 45px 0;
  text-align: center;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.testimonials__item--form__icon {
  display: inline-block;
  margin-bottom: 15px;
  font-size: 100px;
  line-height: 100px;
}
.testimonials__item--form .checkbox-group {
  display: inline-block;
}
.testimonials__item--form__footer {
  border-top-width: 1px;
  border-top-style: solid;
  margin: 25px -45px 0;
  padding: 15px 45px;
}
.testimonials__item--icon__cell {
  padding: 12px;
  text-align: center;
}
.testimonials__item--icon__cell__icon {
  display: inline-block;
  width: 96px;
  height: 96px;
  margin-bottom: 10px;
  font-size: 40px;
  line-height: 98px;
  text-align: center;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.testimonials__item--icon__cell__icon:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
.testimonials__item--icon__cell__title {
  padding-bottom: 0;
}
.testimonials__item--icon__text {
  padding: 20px;
}
.testimonials__item__row-line {
  padding: 10px 0 30px;
  margin: 10px 10px 25px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.testimonials__item__row-line:last-child {
  margin-bottom: 0;
  border: 0;
}
.testimonials__item__row-line table.order-history {
  margin-bottom: -30px;
}
.testimonials__item__row {
  position: relative;
  padding: 15px;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
a.testimonials__item__row {
  display: table;
  width: 100%;
  cursor: pointer;
  text-decoration: none;
}
.testimonials__item__row--big {
  padding: 45px 15px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonials__item__row--big {
    padding: 22px 15px;
  }
}
.testimonials__item__row:last-child {
  border: none;
}
.testimonials__item__row__title {
  font-size: 1.077em;
  text-transform: uppercase;
}
.card__row--big .testimonials__item__row__title {
  font-size: 1.692em;
  line-height: 1em;
}
.testimonials__item__row--icon {
  padding-left: 80px;
  min-height: 70px;
}
.testimonials__item__row--icon.card__row--big {
  min-height: 151px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonials__item__row--icon.card__row--big {
    min-height: 100px;
  }
}
.testimonials__item__row--icon__icon {
  position: absolute;
  left: 0;
  width: 80px;
  text-align: center;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.testimonials__item__row--icon__icon .icon {
  font-size: 38px;
  line-height: 1em;
  -webkit-transition:  color 300ms 0s ease;
  -moz-transition:  color 300ms 0s ease;
  -ms-transition:  color 300ms 0s ease;
  -o-transition:  color 300ms 0s ease;
  transition:  color 300ms 0s ease;
}
.card__row--big .testimonials__item__row--icon__icon .icon {
  font-size: 50px !important;
}
.testimonials__item__row--icon__text {
  display: table-cell;
  vertical-align: middle;
}
.testimonials__item__image-sell {
  padding: 12px 0 12px;
  text-align: center;
}
.testimonials__item__image-sell__author {
  font-size: 0.923em;
  padding: 10px 0 0;
}
.testimonials__item__image-sell img {
  width: inherit !important;
  max-width: 100% !important;
  margin: 0 auto;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.testimonials__item__image-sell img:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.testimonials__item__text {
  position: relative;
  margin: 17px 15px;
  padding-left: 45px;
  font-size: 1em;
  line-height: 1.35em;
  font-weight: lighter;
}
.testimonials__item__text:before {
  position: absolute;
  left: 0;
  top: 0;
  font-family: 'Welldone';
  content: "\e639";
  font-size: 26px;
}
.breadcrumbs {
  margin-bottom: 0;
}
.breadcrumbs--boxed,
body.boxed .breadcrumbs {
  padding-left: 0;
  padding-right: 0;
}
.breadcrumbs--full,
body.fullwidth .breadcrumbs {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
  padding-left: 0;
  padding-right: 0;
}
.breadcrumbs--full > .navbar-header,
body.fullwidth .breadcrumbs > .navbar-header,
.breadcrumbs--full > .navbar-collapse,
body.fullwidth .breadcrumbs > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .breadcrumbs--full > .navbar-header,
  body.fullwidth .breadcrumbs > .navbar-header,
  .breadcrumbs--full > .navbar-collapse,
  body.fullwidth .breadcrumbs > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.breadcrumbs--full .container,
body.fullwidth .breadcrumbs .container {
  width: auto;
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.breadcrumbs--full .container > .navbar-header,
body.fullwidth .breadcrumbs .container > .navbar-header,
.breadcrumbs--full .container > .navbar-collapse,
body.fullwidth .breadcrumbs .container > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .breadcrumbs--full .container > .navbar-header,
  body.fullwidth .breadcrumbs .container > .navbar-header,
  .breadcrumbs--full .container > .navbar-collapse,
  body.fullwidth .breadcrumbs .container > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.breadcrumbs .breadcrumb.breadcrumb--wd {
  display: inline-block;
  padding: 11px 0;
  border-radius: 0;
  margin: 0;
}
.breadcrumbs .breadcrumb.breadcrumb--wd > li {
  display: inline-block;
}
.breadcrumbs .breadcrumb.breadcrumb--wd > li + li:before {
  content: "/\00a0";
  padding: 0 5px;
}
.nav-tabs--wd {
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
@media (max-width: 767px) {
  .nav-tabs--wd > li {
    float: none;
    display: block;
  }
}
.nav-tabs--wd > li > a {
  border: 0;
  margin-right: 3px;
  border-radius: 0;
  padding: 14px 16px 13px;
}
@media (max-width: 767px) {
  .nav-tabs--wd > li > a {
    border-width: 1px;
    border-style: solid;
    margin-right: 0;
  }
}
.nav-tabs--wd > li.active > a {
  -webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
}
@media (max-width: 767px) {
  .nav-tabs--wd > li.active > a {
    -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}
.nav-tabs--wd > li.active > a,
.nav-tabs--wd > li.active > a:hover,
.nav-tabs--wd > li.active > a:focus {
  border-width: 1px;
  border-style: solid;
  border-bottom: 0;
  padding: 13px 15px 14px;
}
@media (max-width: 767px) {
  .nav-tabs--wd > li.active > a,
  .nav-tabs--wd > li.active > a:hover,
  .nav-tabs--wd > li.active > a:focus {
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }
}
.nav-tabs--wd > li.active > a:after {
  height: 2px;
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
}
@media (max-width: 767px) {
  .nav-tabs--wd > li.active > a:after {
    display: none;
  }
}
.tab-content--wd > .tab-pane {
  display: none;
  padding: 30px;
  border-width: 1px;
  border-style: solid;
  border-top: 0;
  -webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
}
@media (max-width: 767px) {
  .tab-content--wd > .tab-pane {
    -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  }
}
.tab-content--wd > .active {
  display: block;
}
.nav-product {
  background: #ffffff;
  border-top-width: 6px;
  border-top-style: solid;
  -webkit-box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 1px 0 rgba(0, 0, 0, 0.15);
}
@media (max-width: 767px) {
  .nav-product {
    display: none;
  }
}
.nav-product.stuck {
  position: fixed;
  z-index: 1000;
  top: 0;
  width: 100%;
}
body.boxed .nav-product.stuck {
  width: auto;
}
.nav-product ul {
  width: 100%;
  top: 0;
  z-index: 10000;
  margin: 10px 0;
  padding: 0;
  list-style: none;
}
.nav-product ul li {
  display: inline-block;
}
.nav-product ul li a {
  display: block;
  font-size: 1.077em;
  line-height: 1.429em;
  padding: 15px 20px;
  text-decoration: none;
  text-transform: uppercase;
  -webkit-transition:  all 100ms 0s ease;
  -moz-transition:  all 100ms 0s ease;
  -ms-transition:  all 100ms 0s ease;
  -o-transition:  all 100ms 0s ease;
  transition:  all 100ms 0s ease;
}
.product-additional section {
  padding: 50px 0 0 0;
  display: inline-block;
  width: 100%;
}
#landingBanners {
  overflow-y: hidden;
  margin-top: 50px;
}
article:last-of-type {
  margin-bottom: 0;
}
article {
  position: relative;
  height: auto;
}
@media (min-width: 480px) {
  article {
    margin-bottom: 100px;
  }
}
@media (min-width: 480px) {
  article .entry-content:before {
    padding-top: 55%;
    content: '';
    display: block;
  }
}
@media (min-width: 480px) {
  article .entry-content .banner {
    overflow: hidden;
    position: absolute;
  }
  article .entry-content .banner img {
    width: 100%;
  }
}
@media (min-width: 480px) and (max-width: 479px) {
  article .entry-content .banner {
    position: relative;
  }
}
article .entry-content .banner:hover {
  z-index: 100;
}
article .entry-content .banner::before {
  content: '';
  background: inherit;
  position: absolute;
  top: -20px;
  right: -1px;
  bottom: -20px;
  left: -1px;
}
@media (min-width: 480px) {
  article .banner--1 {
    top: -20%;
    left: 0%;
    right: 48%;
    z-index: 1;
  }
  .touch article .banner--1 {
    top: 0;
  }
  article .banner--2 {
    top: 30%;
    left: 50%;
    right: 0%;
    z-index: 2;
  }
  .touch article .banner--2 {
    top: 5%;
  }
  article .banner--3 {
    bottom: -15%;
    margin: 0 auto;
    left: 30%;
    right: 30%;
    z-index: 3;
  }
  article .banner--4 {
    top: 5%;
    left: 0%;
    right: 55%;
    z-index: 1;
  }
  article .banner--5 {
    top: 10%;
    left: 40%;
    right: 0%;
    z-index: 2;
  }
  article .banner--6 {
    bottom: -25%;
    margin: 0 auto;
    left: 35%;
    right: 35%;
    z-index: 3;
  }
  article .banner--7 {
    top: 10%;
    left: 0%;
    right: 40%;
    z-index: 1;
  }
  article .banner--8 {
    top: 5%;
    left: 55%;
    right: 0%;
    z-index: 2;
  }
}
@media (min-width: 480px) and (max-width: 1199px) {
  article .banner--3 {
    left: 20%;
    right: 20%;
  }
}
@media (min-width: 480px) and (max-width: 767px) {
  article .banner--3 {
    left: 10%;
    right: 10%;
  }
}
@media (min-width: 480px) and (max-width: 991px) {
  article .banner--6 {
    left: 25%;
    right: 25%;
  }
}
@media (min-width: 480px) and (max-width: 767px) {
  article .banner--6 {
    left: 20%;
    right: 20%;
  }
}
.article-nav {
  position: fixed;
  right: 25px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
@media (max-width: 479px) {
  .article-nav {
    display: none;
  }
}
.article-nav ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.article-nav ul li a {
  display: block;
  font-size: 0;
  height: 40px;
  line-height: 1;
  overflow: hidden;
  position: relative;
  text-align: center;
  cursor: pointer;
  text-decoration: none;
}
.article-nav ul li a:before {
  content: '\2022';
  display: block;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 25px;
}
.category-block {
  margin-bottom: 55px;
}
@media (max-width: 767px) {
  .category-block {
    width: 100%;
  }
}
.category-block__title {
  position: absolute;
  z-index: 0;
  width: 100%;
  left: 0;
  top: 0;
  font-size: 120px;
  line-height: 120px;
  font-weight: bold;
  text-transform: uppercase;
  text-align: center;
}
@media (max-width: 767px) {
  .category-block__title {
    font-size: 70px;
  }
}
.category-block__list {
  position: absolute;
  right: 0;
  z-index: 1;
  float: right;
  margin: 0;
  padding: 0;
  list-style: none;
  width: 190px;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
@media (max-width: 767px) {
  .category-block__list {
    text-align: right;
    padding-right: 15px;
  }
}
.category-block__list li {
  padding: 0 0 10px;
}
.category-block__list li > a {
  font-size: 1.231em;
  line-height: 1.25em;
  text-transform: uppercase;
  text-decoration: none;
}
@media (max-width: 767px) {
  .category-block__list li > a {
    font-size: 1em;
  }
}
.category-block__list li > a:hover,
.category-block__list li > a.active {
  font-weight: bold;
}
.category-block__image {
  position: relative;
  z-index: 1;
  float: left;
  text-align: center;
  margin: 25px 0 0 125px;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .category-block__image {
    margin-left: 50px;
  }
}
@media (max-width: 991px) and (min-width: 768px) {
  .category-block__image {
    margin-left: 100px;
  }
}
@media (max-width: 767px) {
  .category-block__image {
    margin-left: 0;
    max-width: 70%;
  }
}
.category-block__image__loader {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0;
  right: 0;
}
.category-block__image img {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
  opacity: 0;
  /*.transition (opacity, 400ms);*/
  width: 100%;
}
.category-block__image img.active {
  opacity: 1;
  position: relative;
}
.category-block--right .category-block__list {
  right: auto;
  left: 0;
  text-align: right;
}
@media (max-width: 767px) {
  .category-block--right .category-block__list {
    text-align: left;
    padding-left: 15px;
  }
}
.category-block--right .category-block__image {
  float: right;
  margin: 25px 125px 0 0;
}
@media (max-width: 1199px) and (min-width: 992px) {
  .category-block--right .category-block__image {
    margin-right: 50px;
  }
}
@media (max-width: 991px) and (min-width: 768px) {
  .category-block--right .category-block__image {
    margin-right: 100px;
  }
}
@media (max-width: 767px) {
  .category-block--right .category-block__image {
    margin-right: 0;
  }
}
.chapter {
  position: relative;
  padding-bottom: 0 !important;
}
.chapter .chapter__title {
  position: relative;
}
@media (min-width: 992px) {
  .chapter .chapter__title {
    position: absolute;
    width: 50%;
    top: 0;
    left: 0;
    -webkit-transition: 1s width cubic-bezier(0.77, 0, 0.175, 1);
    transition: 1s width cubic-bezier(0.77, 0, 0.175, 1);
    z-index: 200;
  }
}
@media (max-width: 991px) {
  .chapter .chapter__title {
    min-height: 300px !important;
  }
}
.chapter .chapter__title.title--fixed {
  position: fixed;
}
.chapter .chapter__title.title--bottom {
  position: absolute;
  bottom: 0;
  top: auto;
}
.chapter .chapter__title__text {
  position: absolute;
  width: 100%;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.chapter .chapter__end {
  height: 0;
  line-height: 0;
  font-size: 0;
  overflow: hidden;
}
.chapter .chapter__content {
  padding: 45px 0;
}
.chapter .chapter__content--full {
  padding: 0;
}
@media (min-width: 992px) {
  .chapter .chapter__content {
    padding: 75px 112.5px;
    position: relative;
    width: 50%;
    left: 50%;
    z-index: 100;
    -webkit-transition: 1s width cubic-bezier(0.77, 0, 0.175, 1) 0, 1s left cubic-bezier(0.77, 0, 0.175, 1) 0;
    transition: 1s width cubic-bezier(0.77, 0, 0.175, 1) 0, 1s left cubic-bezier(0.77, 0, 0.175, 1) 0;
  }
  .chapter .chapter__content--full {
    padding: 0;
  }
}
.chapter .chapter__content .art-catalogue__item {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}
.chapter .chapter__content .art-catalogue__item:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
@media (max-width: 991px) {
  .chapter .chapter__content .art-catalogue__item {
    width: 50%;
    margin-left: auto;
    margin-right: auto;
  }
}
@media (max-width: 767px) {
  .chapter .chapter__content .art-catalogue__item {
    width: 80%;
  }
}
#intro {
  padding-bottom: 0;
}
.art-catalogue {
  width: 100%;
}
.art-catalogue__item {
  width: 25%;
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.art-catalogue__item--double {
  width: 50%;
}
.art-catalogue__item--full {
  width: 100%;
  margin-bottom: 50px;
}
.art-catalogue__item:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  z-index: 100;
}
@media (max-width: 991px) {
  .art-catalogue__item {
    width: 50%;
  }
}
.art-catalogue__item .product-preview-wrapper {
  display: block;
  margin: 0;
  padding: 0;
}
.art-catalogue__item .product-preview-wrapper .product-preview {
  overflow: hidden;
  border: 0;
  border-radius: 0;
  -webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  -moz-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
}
@media (max-width: 767px) {
  .art-catalogue__item .product-preview-wrapper .product-preview {
    max-width: none;
  }
}
.art-catalogue__item .product-preview-wrapper .product-preview .product-preview__image {
  margin-bottom: 0 !important;
}
.art-catalogue__item .product-preview-wrapper .product-preview .product-preview__info {
  bottom: -200px;
  padding: 60px 30px 15px;
}
@media (max-width: 991px) {
  .art-catalogue__item .product-preview-wrapper .product-preview .product-preview__info {
    bottom: 0;
    position: absolute;
  }
}
.art-catalogue__item .product-preview-wrapper img {
  width: 100%;
  border-radius: 0;
}
.art-catalogue__item .product-preview-wrapper :hover .product-preview__info {
  bottom: 0;
}
.art-catalogue__item .product-preview-wrapper .product-preview__info__title h2 {
  font-size: 1.5em;
  margin-top: 15px;
}
.art-catalogue__item .product-preview-wrapper .product-preview__info__more {
  height: 110px;
  margin-top: -115px;
}
.art-catalogue__item .product-preview-wrapper .product-preview__info__more .btn--round {
  position: relative;
  width: 110px;
  height: 110px;
  line-height: 1em;
  font-size: 14px;
}
.art-catalogue__item .product-preview-wrapper .product-preview__info__more .btn--round span {
  position: absolute;
  width: 100%;
  text-align: center;
  display: block;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
}
.category-outer {
  -webkit-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.15);
}
@media (max-width: 767px) {
  .category-outer {
    margin: 0 -15px;
    margin-top: 0;
  }
}
.category-outer__text {
  padding: 35px;
}
@media (max-width: 767px) {
  .category-outer__text {
    padding: 15px;
  }
}
.category-outer__text__title {
  padding-bottom: 10px;
}
.filters-row {
  padding: 30px 15px;
}
.filters-row__view {
  margin: 0 12px 0 0;
  font-size: 26px;
  line-height: 32px;
  cursor: pointer;
}
.filters-row__items {
  display: inline;
  padding: 0 20px;
}
@media (max-width: 1199px) {
  .filters-row__items {
    padding: 0 10px;
  }
}
.aside-column .filters-row__items {
  padding: 0 10px;
}
@media (max-width: 1199px) {
  .aside-column .filters-row__items {
    padding: 0 5px;
  }
}
.filters-row__select {
  display: inline;
  padding: 0 12px;
}
@media (max-width: 1199px) {
  .filters-row__select {
    padding: 0 5px;
  }
}
.filters-row__select label {
  padding: 0 5px;
}
.filters-row__select .icon {
  display: inline-block;
  padding: 0 2px;
  font-size: 10px;
  vertical-align: middle;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .filters-row .col-2 {
    text-align: right;
  }
  .filters-row .col-2 .filters-row__select:last-child {
    padding-right: 0;
  }
}
.products-col {
  width: auto;
  position: relative;
}
body.boxed .products-col {
  overflow: hidden;
}
@media (max-width: 767px) {
  .products-col {
    margin-left: 0 !important;
  }
}
.filters-col {
  width: 280px;
  float: left;
  opacity: 0;
  -webkit-transition:  opacity 300ms ease-out 0s;
  -moz-transition:  opacity 300ms ease-out 0s;
  -ms-transition:  opacity 300ms ease-out 0s;
  -o-transition:  opacity 300ms ease-out 0s;
  transition:  opacity 300ms ease-out 0s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  padding: 4px 20px 4px;
  position: absolute;
}
@media (min-width: 768px) {
  .filters-col {
    right: auto !important;
  }
}
.open .filters-col {
  opacity: 1;
}
.aside-column .filters-col {
  height: auto !important;
  opacity: 1;
  width: 100%;
  float: none;
  position: relative !important;
}
.open .filters-col {
  position: relative;
}
.filters-col__close {
  display: none;
  position: absolute;
  top: 10px;
  left: 20px;
}
.filters-col__select {
  margin-bottom: 8px;
}
.filters-col__select label {
  width: 80px;
}
.filters-col__collapse {
  width: 100%;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}
.filters-col__collapse__content {
  display: none;
  padding-bottom: 15px;
}
.open > .filters-col__collapse__content {
  display: block;
}
.filters-col__collapse__title {
  cursor: pointer;
  padding: 20px 20px 20px 0;
  margin-top: 0 !important;
  position: relative;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.filters-col__collapse__title:after {
  position: absolute;
  display: block;
  top: 24px;
  right: 2px;
  font-family: 'Welldone';
  font-size: 10px;
  content: "\e601";
  line-height: 1em;
}
.open > .filters-col__collapse__title:after {
  content: "\e631";
}
.filters-col__collapse:last-child {
  border-bottom: 0;
}
.price-slider {
  margin: 25px 0 15px;
}
.filter-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.filter-list li {
  margin: 0 0 5px;
  text-transform: uppercase;
}
.filter-list li a.icon {
  display: inline-block;
  vertical-align: middle;
  font-size: 0.8em;
  margin: 0 8px 0 0 !important;
  text-decoration: none;
}
#showFilterMobile {
  display: none;
}
@media (max-width: 767px) {
  .col-1 {
    float: right;
    margin-right: -15px;
  }
  .col-2 {
    float: left;
    margin-left: -15px;
  }
  #showFilterMobile {
    display: inline-block;
  }
  #showFilter {
    display: none;
  }
  .outer #filtersCol {
    padding: 35px 20px;
    right: -100%;
    width: 80%;
    top: 0;
    margin: 0;
    min-width: 0;
    height: 100%;
    position: fixed;
    z-index: 10001;
    overflow-x: hidden;
    overflow-y: auto;
    opacity: 1;
  }
  .outer #filtersCol .filters-col__collapse__title {
    text-align: left;
  }
  .filters-row__select {
    display: none;
  }
  .aside-column .filters-row__select {
    display: inline;
  }
  .filters-row__items {
    padding: 0;
    line-height: 32px;
  }
}
.filters-by-category ul {
  margin: 0 0 50px;
  padding: 0;
  list-style: none;
  text-align: center;
}
.filters-by-category ul li {
  display: inline-block;
  line-height: 1em;
  margin-bottom: 2px;
}
.filters-by-category ul li a {
  display: block;
  font-size: 1.5385em;
  font-weight: 300;
  line-height: 1em;
  padding: 10px;
  background-color: #fafafa;
  text-decoration: none;
  text-transform: uppercase;
  border-radius: 4px;
}
.filters-by-category ul li a:hover,
.filters-by-category ul li a .selected {
  background-color: #536dfe;
  color: #ffffff;
}
.tag {
  display: block;
  border-width: 1px;
  border-style: solid;
  font-size: 0.923em;
  line-height: 1.8em;
  padding: 2px 10px 0;
  text-decoration: none;
  -webkit-transition:  all 200ms 0s ease;
  -moz-transition:  all 200ms 0s ease;
  -ms-transition:  all 200ms 0s ease;
  -o-transition:  all 200ms 0s ease;
  transition:  all 200ms 0s ease;
  text-transform: uppercase;
}
.tags-list {
  position: relative;
  list-style: none;
  margin: 0 0 10px;
  padding: 0;
}
.tags-list li {
  display: inline-block;
  padding: 0;
  margin-bottom: 2px;
}
.tags-list li a {
  display: block;
  border-width: 1px;
  border-style: solid;
  font-size: 0.923em;
  line-height: 1.8em;
  padding: 2px 10px 0;
  text-decoration: none;
  -webkit-transition:  all 200ms 0s ease;
  -moz-transition:  all 200ms 0s ease;
  -ms-transition:  all 200ms 0s ease;
  -o-transition:  all 200ms 0s ease;
  transition:  all 200ms 0s ease;
  text-transform: uppercase;
  line-height: 0.9em;
  padding: 7px 10px;
}
.rating-extended {
  margin-bottom: 15px;
}
.rating-extended .rating {
  margin-top: 4px;
}
.rating-extended__num {
  margin: 0 15px 10px 0;
  padding: 0;
  font-size: 2.615em;
  font-weight: 600;
}
.rating-extended__label {
  float: left;
  width: 50px;
  line-height: 1em;
}
.rating-extended__reviews-count {
  float: right;
  line-height: 1em;
}
.rating-extended .progress {
  overflow: hidden;
  height: 14px;
  margin-bottom: 3px;
  border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.rating-extended .progress-bar {
  text-align: left;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.review {
  padding: 20px 0;
  margin: 15px 0;
  border-top-width: 1px;
  border-top-style: solid;
}
.review__title {
  padding-bottom: 15px;
}
.review__content {
  padding-bottom: 10px;
}
.review__meta {
  padding-bottom: 15px;
}
.review__comments {
  padding-bottom: 7px;
}
.review__helpful {
  color: #adadad;
}
.review__helpful a {
  display: inline-block;
  padding: 0 7px;
  line-height: 20px;
  border-radius: 3px;
  text-decoration: none;
}
.review .rating {
  margin-bottom: 10px;
}
.panel-table {
  display: table;
  width: 100%;
  padding: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.panel-table > .panel-body {
  display: table-row-group;
  padding: 0;
}
.panel-table > .panel-body:before,
.panel-table > .panel-body:after {
  content: none;
}
.panel-table > div > .tr {
  display: table-row;
}
.panel-table > div:last-child > .tr:last-child > .td {
  border-bottom: none;
}
.panel-table .td {
  float: none;
  display: table-cell;
  padding: 20px 15px;
  border-width: 1px;
  border-style: solid;
  border-top: none;
  border-left: none;
  vertical-align: middle;
}
@media (max-width: 767px) {
  .panel-table .td {
    display: block;
    border-right: none;
    border-bottom-width: 1px !important;
    border-bottom-style: solid !important;
  }
}
.panel-table .td:last-child {
  border-right: none;
  border-bottom: none !important;
}
.panel-table h2,
.panel-table h3,
.panel-table h4,
.panel-table h5,
.panel-table h6 {
  padding: 0;
}
.panel-default,
.panel-checkout {
  border-color: #f0f0f0;
}
.panel-default > .panel-heading,
.panel-checkout > .panel-heading {
  color: #333333;
  background-color: #f4f4f4;
  border-color: #f0f0f0;
}
.panel-default > .panel-heading + .panel-collapse > .panel-body,
.panel-checkout > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #f0f0f0;
}
.panel-default > .panel-heading .badge,
.panel-checkout > .panel-heading .badge {
  color: #f4f4f4;
  background-color: #333333;
}
.panel-default > .panel-footer + .panel-collapse > .panel-body,
.panel-checkout > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #f0f0f0;
}
.panel-checkout .panel-heading {
  position: relative;
  border-left-width: 55px;
  border-left-style: solid;
}
.panel-checkout .panel-heading__number {
  content: " ";
  position: absolute;
  top: 0;
  bottom: 0;
  left: -55px;
  width: 55px;
  height: 100%;
  padding: 15px 0;
  font-size: 16px;
  text-align: center;
  border-right-style: solid;
  border-right-width: 1px;
}
.panel-heading {
  padding: 0;
}
.panel-number {
  position: absolute;
}
.panel-title {
  text-transform: uppercase;
  padding: 0;
}
.panel-title a {
  padding: 15px 15px;
  display: block;
  text-decoration: none;
}
.panel-body {
  padding: 20px 50px;
}
@media (max-width: 991px) {
  .panel-body {
    padding: 20px 25px;
  }
}
@media (max-width: 767px) {
  .panel-body {
    padding: 15px 20px;
  }
}
.comments__comment__userpic {
  width: 65px;
  height: 65px;
  float: left;
  border-radius: 50%;
  margin: 0 15px 0 0;
  font-size: 40px;
  line-height: 73px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  -webkit-transition:  box-shadow 300ms 0s ease;
  -moz-transition:  box-shadow 300ms 0s ease;
  -ms-transition:  box-shadow 300ms 0s ease;
  -o-transition:  box-shadow 300ms 0s ease;
  transition:  box-shadow 300ms 0s ease;
}
.comments__comment__userpic:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}
.comments__comment__text {
  overflow: hidden;
  padding: 10px 0 35px;
}
.comments__comment__text__username {
  padding-bottom: 3px;
  font-weight: 400;
}
.comments__comment__text__date {
  font-size: 0.923em;
  padding-bottom: 10px;
}
.comments__comment--replay {
  margin-left: 80px;
}
.comment-form .form-group {
  margin-bottom: 8px;
}
.comment-form input[type=text],
.comment-form textarea {
  border-left: 0;
  border-top: 0;
  border-right: 0;
  padding: 5px 0;
  margin-bottom: 0;
  height: 38px;
}
.comment-form textarea {
  padding: 12px 0;
  overflow: hidden;
}
.gallery__item {
  position: relative;
  float: left;
  width: 19.9%;
  overflow: hidden;
}
.gallery__item img {
  width: 100%;
}
.gallery__item--double {
  width: 39.9%;
}
.gallery__item .btn {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 50%;
  margin-left: -30px;
  margin-top: -30px;
  opacity: 0;
  -webkit-transition:  all 300ms 0s ease;
  -moz-transition:  all 300ms 0s ease;
  -ms-transition:  all 300ms 0s ease;
  -o-transition:  all 300ms 0s ease;
  transition:  all 300ms 0s ease;
}
.gallery__item__image {
  overflow: hidden;
}
.gallery__item__image img {
  -webkit-transition:  all 300ms 0s ease;
  -moz-transition:  all 300ms 0s ease;
  -ms-transition:  all 300ms 0s ease;
  -o-transition:  all 300ms 0s ease;
  transition:  all 300ms 0s ease;
  opacity: 0.5;
}
.gallery__item__image__caption {
  position: absolute;
  padding: 15px;
  width: 100%;
  bottom: 0;
  opacity: 0;
  text-align: center;
  text-transform: uppercase;
  -webkit-transform: translateY(100%);
  -moz-transform: translateY(100%);
  -ms-transform: translateY(100%);
  /* note that you have @transform here */
  -o-transform: translateY(100%);
  transform: translateY(100%);
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s 0.3s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s 0.3s;
  transition: transform 0.4s, opacity 0.1s 0.3s;
}
.gallery__item__image__caption h6 {
  padding: 0;
}
.gallery__item:hover .btn {
  opacity: 1;
}
.gallery__item:hover img {
  transform: translateY(-50px);
  opacity: 1;
}
.gallery__item:hover .gallery__item__image__caption {
  opacity: 1;
  -webkit-transform: translateY(0px);
  -moz-transform: translateY(0px);
  -ms-transform: translateY(0px);
  /* note that you have @transform here */
  -o-transform: translateY(0px);
  transform: translateY(0px);
  -webkit-transition: -webkit-transform 0.4s, opacity 0.1s;
  -moz-transition: -moz-transform 0.4s, opacity 0.1s;
  transition: transform 0.4s, opacity 0.1s;
}
.post .gallery-row {
  overflow: hidden;
  margin: 0 -2px;
}
.post .gallery-row .gallery-col {
  float: left;
}
.post .gallery-row .gallery-col img {
  padding: 2px;
  width: 100%;
}
.lookbook {
  text-align: center;
}
.lookbook__image {
  display: inline-block;
  position: relative;
  margin-bottom: -4px;
}
.lookbook__image img {
  position: relative;
  z-index: 0;
  max-width: 100%;
  width: 100%;
}
.hint {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  position: absolute;
  z-index: 1;
  font-size: 12px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
}
.hint:hover {
  z-index: 2;
}
.hint__dot {
  border-width: 10px;
  border-style: solid;
  background: transparent;
  border-radius: 50%;
  height: 28px;
  width: 28px;
  -webkit-animation: pulse 1s ease-out;
  -moz-animation: pulse 1s ease-out;
  animation: pulse 1s ease-out;
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  opacity: 0;
}
.tool {
  height: auto;
  width: auto;
  position: absolute;
  right: 38px;
  padding: 10px 12px;
  color: #000;
  border-bottom: 0;
  opacity: 1;
  z-index: 0;
  border-radius: 3px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  line-height: 1em;
  text-transform: uppercase;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  cursor: pointer;
  white-space: nowrap;
  -webkit-transition:  background 300ms ease-out 0s;
  -moz-transition:  background 300ms ease-out 0s;
  -ms-transition:  background 300ms ease-out 0s;
  -o-transition:  background 300ms ease-out 0s;
  transition:  background 300ms ease-out 0s;
}
@media (max-width: 991px) {
  .tool {
    padding: 6px 8px;
  }
}
.tool:before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  right: -10px;
  height: 32px;
  width: 10px;
  background: transparent;
}
.tool .tip {
  content: '';
  height: 8px;
  width: 8px;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  position: absolute;
  top: 50%;
  right: -4px;
  margin-top: -4px;
  z-index: 1;
  box-shadow: -2px 2px 2px rgba(0, 0, 0, 0.1);
  -webkit-transition:  background 300ms ease-out 0s;
  -moz-transition:  background 300ms ease-out 0s;
  -ms-transition:  background 300ms ease-out 0s;
  -o-transition:  background 300ms ease-out 0s;
  transition:  background 300ms ease-out 0s;
}
.hint:hover .tool .tip {
  background: #536dfe;
}
.tool--right {
  left: 38px;
  right: auto;
}
.tool--right:before {
  left: -10px;
  right: auto;
}
.tool--right .tip {
  left: -4px;
  right: auto;
  box-shadow: 2px -2px 2px rgba(0, 0, 0, 0.1);
}
.hover-tool {
  visibility: hidden;
  opacity: 0;
  -webkit-transition:  all 300ms ease-out 0s;
  -moz-transition:  all 300ms ease-out 0s;
  -ms-transition:  all 300ms ease-out 0s;
  -o-transition:  all 300ms ease-out 0s;
  transition:  all 300ms ease-out 0s;
  cursor: auto;
  height: auto;
  width: 425px;
  position: absolute;
  left: 50px;
  top: 48px;
  padding: 16px 20px;
  border-bottom: 0;
  z-index: 0;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  font-size: 13px;
  line-height: 1.4em;
  text-transform: none;
  text-align: left;
  white-space: normal;
}
.hint:hover .hover-tool {
  left: 0;
  visibility: visible;
  opacity: 1;
}
@media (max-width: 991px) {
  .hover-tool {
    font-size: 12px;
    line-height: 1.2em;
    width: 250px;
    top: 38px;
    padding-bottom: 70px;
  }
}
.tool--right .hover-tool {
  left: auto;
  right: 50px;
}
.hint:hover .tool--right .hover-tool {
  left: auto;
  right: 0;
}
.hover-tool:before {
  content: '';
  display: block;
  position: absolute;
  top: -18px;
  left: 0;
  height: 18px;
  width: 100%;
  background: transparent;
}
.hover-tool .btn {
  float: right;
  margin-left: 35px;
}
@media (max-width: 991px) {
  .hover-tool .btn {
    float: none;
    margin-left: 0;
    margin-bottom: 15px;
    position: absolute;
    left: 20px;
    bottom: 0;
  }
}
.hover-tool__tip {
  content: '';
  height: 8px;
  width: 8px;
  -webkit-transform: rotate(-135deg);
  -moz-transform: rotate(-135deg);
  -o-transform: rotate(-135deg);
  position: absolute;
  top: -4px;
  left: 35px;
  z-index: 1;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
}
.tool--right .hover-tool__tip {
  left: auto;
  right: 35px;
}
/*countdown*/
.eight-in-row .countdown_box,
.seven-in-row .countdown_box {
  display: none;
}
@media (max-width: 991px) {
  .countdown_box {
    display: none;
  }
}
.countdown-row {
  clear: both;
  width: 100%;
  padding: 0 2px;
  text-align: center;
}
.countdown-show1 .countdown-section {
  width: 98%;
}
.countdown-show2 .countdown-section {
  width: 48%;
}
.countdown-show3 .countdown-section {
  width: 31.5%;
}
.countdown-show4 .countdown-section {
  width: 22.5%;
}
.countdown-show5 .countdown-section {
  width: 19.5%;
}
.countdown-show6 .countdown-section {
  width: 16.25%;
}
.countdown-show7 .countdown-section {
  width: 14%;
}
.countdown-period {
  display: block;
}
.countdown-descr {
  display: block;
  width: 100%;
}
.countdown_box {
  line-height: 18px;
  position: absolute;
  text-align: center;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 100%;
  z-index: 999;
}
.countdown_inner {
  overflow: hidden;
  -webkit-box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  width: 80%;
  padding: 15px 15px 15px;
  text-align: center;
}
.countdown_inner .title {
  margin: 0 0 5px;
  font-size: 14px;
  text-transform: uppercase;
}
.countdown-section {
  display: inline-block;
  text-align: center;
  padding: 5px 0;
  margin: 0 1px 0 0;
  font-size: 9px;
  line-height: 10px;
  text-transform: uppercase;
}
.countdown-amount {
  line-height: 1em;
}
@media (min-width: 768px) {
  .countdown-amount {
    font-size: 16px;
  }
}
@media (min-width: 992px) {
  .countdown-amount {
    font-size: 20px;
  }
}
@media (min-width: 1200px) {
  .countdown-amount {
    font-size: 28px;
  }
}
@media (min-width: 768px) {
  .two-in-row .countdown-amount {
    font-size: 28px;
  }
}
@media (min-width: 992px) {
  .two-in-row .countdown-amount {
    font-size: 28px;
  }
}
@media (min-width: 1200px) {
  .two-in-row .countdown-amount {
    font-size: 28px;
  }
}
@media (min-width: 768px) {
  .three-in-row .countdown-amount {
    font-size: 22px;
  }
}
@media (min-width: 992px) {
  .three-in-row .countdown-amount {
    font-size: 26px;
  }
}
@media (min-width: 1200px) {
  .three-in-row .countdown-amount {
    font-size: 28px;
  }
}
@media (min-width: 768px) {
  .five-in-row .countdown-amount {
    font-size: 14px;
  }
}
@media (min-width: 992px) {
  .five-in-row .countdown-amount {
    font-size: 16px;
  }
}
@media (min-width: 1200px) {
  .five-in-row .countdown-amount {
    font-size: 20px;
  }
}
@media (min-width: 768px) {
  .six-in-row .countdown-amount {
    font-size: 14px;
  }
}
@media (min-width: 992px) {
  .six-in-row .countdown-amount {
    font-size: 16px;
  }
}
@media (min-width: 1200px) {
  .six-in-row .countdown-amount {
    font-size: 20px;
  }
}
@media (min-width: 768px) {
  .seven-in-row .countdown-amount {
    font-size: 13px;
  }
}
@media (min-width: 992px) {
  .seven-in-row .countdown-amount {
    font-size: 14px;
  }
}
@media (min-width: 1200px) {
  .seven-in-row .countdown-amount {
    font-size: 18px;
  }
}
@media (min-width: 768px) {
  .eight-in-row .countdown-amount {
    font-size: 13px;
  }
}
@media (min-width: 992px) {
  .eight-in-row .countdown-amount {
    font-size: 14px;
  }
}
@media (min-width: 1200px) {
  .eight-in-row .countdown-amount {
    font-size: 18px;
  }
}
.product-preview__image:hover .countdown_box {
  display: none;
}
.shopping-cart-table > tbody > tr > td {
  vertical-align: middle !important;
}
.shopping-cart-table__product-image img {
  max-width: 80px;
}
.shopping-cart-table__product-price {
  font-size: 14px;
}
.shopping-cart-table [class^="icon-"],
.shopping-cart-table [class*=" icon-"] {
  font-size: 18px;
  line-height: 38px;
  display: inline-block;
  vertical-align: top;
}
.shopping-cart-table td.image-col {
  width: 105px;
  padding-left: 0;
  padding-right: 0;
}
.shopping-cart-table h6 {
  padding: 2px 0 5px;
}
@media (max-width: 767px) {
  .shopping-cart-table > thead {
    display: none;
  }
  .shopping-cart-table__product-image img {
    max-width: 160px;
  }
  .shopping-cart-table td.image-col {
    width: 100%;
    text-align: center !important;
  }
  .shopping-cart-table,
  .shopping-cart-table tbody,
  .shopping-cart-table td,
  .shopping-cart-table tr {
    display: block;
    border: 0;
    width: 100%;
  }
  .shopping-cart-table td:first-child {
    border-top: 0;
  }
  .shopping-cart-table td {
    position: relative;
    padding-left: 50% !important;
    text-align: left  !important;
  }
  .shopping-cart-table td div.th-title {
    position: absolute;
    left: 10px;
    width: 45%;
    padding-right: 10px;
    text-align: right;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    font-weight: bold;
    text-transform: uppercase;
  }
  .shopping-cart-table td.no-title {
    padding-left: 20px !important;
    text-align: center !important;
  }
  .shopping-cart-table .input-group-qty {
    display: inline-block;
    padding-right: 10px;
  }
  .shopping-cart-btns {
    text-align: center;
  }
  .shopping-cart-btns .btn.pull-left,
  .shopping-cart-btns .btn.pull-right {
    float: none !important;
    margin: 0 !important;
  }
}
table.table-total {
  font-size: 18px;
  margin-bottom: 20px;
}
.aside-column table.table-total {
  margin-bottom: 0;
}
table.table-total tr > th {
  border-top: 0;
  font-weight: normal;
}
table.table-total tr > th,
table.table-total tr > td {
  padding: 4px 0 4px;
  width: 50%;
}
table.table-total h2 {
  font-size: 24px;
  padding-bottom: 0;
}
.twitter-widget {
  min-height: 450px;
}
.facebook-widget {
  min-height: 450px;
}
.pinterest-widget {
  min-height: 450px;
}
.not-found-box {
  text-align: center;
}
.not-found-box__image {
  position: relative;
  display: inline-block;
}
.not-found-box__image img {
  width: 100%;
  max-width: 100%;
}
.not-found-box__text {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 0 10% 8% 0;
  width: 55%;
}
@media (max-width: 767px) {
  .not-found-box__text {
    padding: 0;
  }
}
.animated {
  -webkit-animation-duration: .6s;
  animation-duration: .6s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  opacity: 1;
}
.animation {
  opacity: 0;
  opacity: 1\9;
}
@-webkit-keyframes slideDown {
  0% {
    max-height: 0;
  }
  100% {
    max-height: 500px;
  }
}
@keyframes slideDown {
  0% {
    max-height: 0;
  }
  100% {
    max-height: 500px;
  }
}
.slideDown {
  overflow: hidden;
  -webkit-animation-name: slideDown;
  animation-name: slideDown;
}
@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
.fadeIn {
  -webkit-animation-name: fadeIn;
  animation-name: fadeIn;
}
@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    -ms-transform: translateY(-20px);
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}
@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
  }
}
.fadeInUp {
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}
@-webkit-keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInLeft {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInLeft {
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
}
@-webkit-keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInRight {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
  }
  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInRight {
  -webkit-animation-name: fadeInRight;
  animation-name: fadeInRight;
}
.hover-squared {
  position: relative;
  /*	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
*/
}
.hover-squared .caption::before,
.hover-squared .caption::after {
  pointer-events: none;
  position: absolute;
  top: 10px;
  right: 10px;
  bottom: 10px;
  left: 10px;
  content: '';
  opacity: 0;
  -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
  transition: opacity 0.35s, transform 0.35s;
}
.hover-squared .caption::before {
  border-top: 1px solid #fff;
  border-bottom: 1px solid #fff;
  -webkit-transform: scale(0, 1);
  transform: scale(0, 1);
}
.hover-squared .caption::after {
  border-right: 1px solid #fff;
  border-left: 1px solid #fff;
  -webkit-transform: scale(1, 0);
  transform: scale(1, 0);
}
.hover-squared:hover .caption::before,
.hover-squared:hover .caption::after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
.modal-backdrop {
  -webkit-backface-visibility: hidden;
}
.modal-backdrop.zoom,
.modal-backdrop.newspaper,
.modal-backdrop.move-horizontal,
.modal-backdrop.move-from-top,
.modal-backdrop.unfold-3d,
.modal-backdrop.zoom-out {
  opacity: 0;
  transition: opacity 0.4s ease;
}
.modal-backdrop.in {
  opacity: 0.8;
}
.modal-backdrop.unfold-3d {
  perspective: 500;
}
.modal.zoom .modal-dialog {
  opacity: 0;
  -webkit-transition: all 0.5s ease-in-out;
  -o-transition: all 0.5s ease-in-out;
  transition: all 0.5s ease-in-out;
  -webkit-transition:  all 1s 0s ease, 0.5s 1s 0s ease, ease-in-out 1s 0s ease;
  -moz-transition:  all 1s 0s ease, 0.5s 1s 0s ease, ease-in-out 1s 0s ease;
  -ms-transition:  all 1s 0s ease, 0.5s 1s 0s ease, ease-in-out 1s 0s ease;
  -o-transition:  all 1s 0s ease, 0.5s 1s 0s ease, ease-in-out 1s 0s ease;
  transition:  all 1s 0s ease, 0.5s 1s 0s ease, ease-in-out 1s 0s ease;
  transform: scale(0.6);
}
.modal.in .modal-dialog {
  opacity: 1;
  -webkit-transition: all 0.4s ease-in-out;
  -o-transition: all 0.4s ease-in-out;
  transition: all 0.4s ease-in-out;
  -webkit-transition:  all 1s 0s ease, 0.4s 1s 0s ease, ease-in-out 1s 0s ease;
  -moz-transition:  all 1s 0s ease, 0.4s 1s 0s ease, ease-in-out 1s 0s ease;
  -ms-transition:  all 1s 0s ease, 0.4s 1s 0s ease, ease-in-out 1s 0s ease;
  -o-transition:  all 1s 0s ease, 0.4s 1s 0s ease, ease-in-out 1s 0s ease;
  transition:  all 1s 0s ease, 0.4s 1s 0s ease, ease-in-out 1s 0s ease;
  transform: scale(1);
}
.modal.fade.newspaper .modal-dialog,
.modal.newspaper .modal-dialog {
  opacity: 0;
  -webkit-transition: all 0.5s ease-in-out;
  transition: all 0.5s;
  transform: scale(0) rotate(500deg);
}
.modal.fade.newspaper.in .modal-dialog,
.modal.in .modal-dialog {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}
.modal.move-horizontal .modal-dialog {
  opacity: 0;
  transition: all 0.5s;
  transform: translateX(-50px);
}
.modal.in .modal-dialog {
  opacity: 1;
  transform: translateX(0);
}
.modal.move-from-top .modal-dialog {
  opacity: 0;
  transition: all 0.5s;
  transform: translateY(-100px);
}
.modal.in .modal-dialog {
  opacity: 1;
  transform: translateY(0);
}
.modal.unfold-3d .modal-dialog {
  opacity: 0;
  transition: all 0.5s ease-in-out;
  transform-style: preserve-3d;
  transform: rotateY(-60deg);
}
.modal.in .modal-dialog {
  opacity: 1;
  transform: rotateY(0deg);
}
.modal.fade.zoom-out .modal-dialog,
.modal.zoom-out .modal-dialog {
  opacity: 0;
  transition: all 0.5s;
  transform: scale(1.3);
}
.modal.fade.zoom-out.in .modal-dialog,
.modal.in .modal-dialog {
  opacity: 1;
  transform: scale(1);
}
.csstransforms3d .modal.unfold-3d .modal-dialog {
  transform: none;
  transform-origin: 50% 100%;
  transform: scale(0.7) rotate3d(-1, 1, 0, -60deg);
}
.csstransforms3d .modal.in .modal-dialog {
  transform: scale(1) rotate3d(-1, 1, 0, 0deg);
}
@-webkit-keyframes scroll-ani {
  0% {
    opacity: 0;
    top: 0;
  }
  50% {
    opacity: 1;
    top: 50%;
  }
  100% {
    opacity: 0;
    top: 75%;
  }
}
@-moz-keyframes scroll-ani {
  0% {
    opacity: 0;
    top: 0;
  }
  50% {
    opacity: 1;
    top: 50%;
  }
  100% {
    opacity: 0;
    top: 75%;
  }
}
@keyframes scroll-ani {
  0% {
    opacity: 0;
    top: 0;
  }
  50% {
    opacity: 1;
    top: 50%;
  }
  100% {
    opacity: 0;
    top: 75%;
  }
}
@-webkit-keyframes scroll-ani-to-top {
  0% {
    opacity: 0;
    bottom: 0;
  }
  50% {
    opacity: 1;
    bottom: 50%;
  }
  100% {
    opacity: 0;
    bottom: 75%;
  }
}
@-moz-keyframes scroll-ani-to-top {
  0% {
    opacity: 0;
    bottom: 0;
  }
  50% {
    opacity: 1;
    bottom: 50%;
  }
  100% {
    opacity: 0;
    bottom: 75%;
  }
}
@keyframes scroll-ani-to-top {
  0% {
    opacity: 0;
    bottom: 0;
  }
  50% {
    opacity: 1;
    bottom: 50%;
  }
  100% {
    opacity: 0;
    bottom: 75%;
  }
}
@-webkit-keyframes rotating {
  /* Safari and Chrome */
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotating {
  from {
    -ms-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
.rotating {
  -webkit-animation: rotating 2s linear infinite;
  -moz-animation: rotating 2s linear infinite;
  -ms-animation: rotating 2s linear infinite;
  -o-animation: rotating 2s linear infinite;
  animation: rotating 2s linear infinite;
}
@-moz-keyframes pulse {
  0% {
    -moz-transform: scale(1);
    opacity: 0.0;
  }
  25% {
    -moz-transform: scale(1);
    opacity: 0.1;
  }
  50% {
    -moz-transform: scale(1.1);
    opacity: 0.3;
  }
  75% {
    -moz-transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    -moz-transform: scale(1.8);
    opacity: 0.0;
  }
}
@-webkit-keyframes pulse {
  0% {
    -webkit-transform: scale(1);
    opacity: 0.0;
  }
  25% {
    -webkit-transform: scale(1);
    opacity: 0.1;
  }
  50% {
    -webkit-transform: scale(1.1);
    opacity: 0.3;
  }
  75% {
    -webkit-transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    -webkit-transform: scale(1.8);
    opacity: 0.0;
  }
}
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.0;
  }
  25% {
    transform: scale(1);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  75% {
    transform: scale(1.5);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.8);
    opacity: 0.0;
  }
}
.slick-dots li button:before {
  opacity: 1;
}
.slick-dots li.slick-active button:before {
  opacity: 1;
  font-size: 12px;
  margin-top: 1px;
}
@media (min-width: 768px) {
  .animated-arrows .slick-prev,
  .animated-arrows .slick-next {
    width: 40px;
    height: 40px;
    -webkit-transform: translateY(-7px);
    transform: translateY(-7px);
  }
  .animated-arrows .slick-prev .icon-wrap,
  .animated-arrows .slick-next .icon-wrap {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
  }
  .animated-arrows .slick-prev::before,
  .animated-arrows .slick-prev::after,
  .animated-arrows .slick-prev .icon-wrap::before,
  .animated-arrows .slick-prev .icon-wrap::after,
  .animated-arrows .slick-next::before,
  .animated-arrows .slick-next::after,
  .animated-arrows .slick-next .icon-wrap::before,
  .animated-arrows .slick-next .icon-wrap::after {
    position: absolute;
    left: 50%;
    width: 3px;
    height: 50%;
    content: '';
    -webkit-transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  .animated-arrows .slick-prev .icon-wrap::before,
  .animated-arrows .slick-prev .icon-wrap::after,
  .animated-arrows .slick-next .icon-wrap::before,
  .animated-arrows .slick-next .icon-wrap::after {
    z-index: 100;
    height: 0;
    -webkit-transition: height 0.3s, -webkit-transform 0.3s;
    transition: height 0.3s, transform 0.3s;
  }
  .animated-arrows .slick-prev::before,
  .animated-arrows .slick-prev .icon-wrap::before {
    top: 50%;
    -webkit-transform: translateX(-50%) rotate(-135deg);
    transform: translateX(-50%) rotate(-135deg);
    -webkit-transform-origin: 50% 0%;
    transform-origin: 50% 0%;
  }
  .animated-arrows .slick-next::before,
  .animated-arrows .slick-next .icon-wrap::before {
    top: 50%;
    -webkit-transform: translateX(-50%) rotate(135deg);
    transform: translateX(-50%) rotate(135deg);
    -webkit-transform-origin: 50% 0%;
    transform-origin: 50% 0%;
  }
  .animated-arrows .slick-prev::after,
  .animated-arrows .slick-prev .icon-wrap::after {
    top: 50%;
    -webkit-transform: translateX(-50%) rotate(-45deg);
    transform: translateX(-50%) rotate(-45deg);
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
  }
  .animated-arrows .slick-next::after,
  .animated-arrows .slick-next .icon-wrap::after {
    top: 50%;
    -webkit-transform: translateX(-50%) rotate(45deg);
    transform: translateX(-50%) rotate(45deg);
    -webkit-transform-origin: 100% 0%;
    transform-origin: 100% 0%;
  }
  .animated-arrows .slick-prev:hover .icon-wrap::before,
  .animated-arrows .slick-prev:hover .icon-wrap::after,
  .animated-arrows .slick-next:hover .icon-wrap::before,
  .animated-arrows .slick-next:hover .icon-wrap::after {
    height: 50%;
  }
  .animated-arrows .slick-prev:hover::before,
  .animated-arrows .slick-prev:hover .icon-wrap::before {
    -webkit-transform: translateX(-50%) rotate(-125deg);
    transform: translateX(-50%) rotate(-125deg);
  }
  .animated-arrows .slick-next:hover::before,
  .animated-arrows .slick-next:hover .icon-wrap::before {
    -webkit-transform: translateX(-50%) rotate(125deg);
    transform: translateX(-50%) rotate(125deg);
  }
  .animated-arrows .slick-prev:hover::after,
  .animated-arrows .slick-prev:hover .icon-wrap::after {
    -webkit-transform: translateX(-50%) rotate(-55deg);
    transform: translateX(-50%) rotate(-55deg);
  }
  .animated-arrows .slick-next:hover::after,
  .animated-arrows .slick-next:hover .icon-wrap::after {
    -webkit-transform: translateX(-50%) rotate(55deg);
    transform: translateX(-50%) rotate(55deg);
  }
}
@media (min-width: 768px) {
  .content.boxed .slick-slider:not(.nav-inside) .slick-prev,
  body.boxed .slick-slider:not(.nav-inside) .slick-prev,
  .content.boxed .slick-slider:not(.nav-inside) .slick-next,
  body.boxed .slick-slider:not(.nav-inside) .slick-next {
    opacity: 0;
  }
  .content.boxed .slick-slider:not(.nav-inside):hover .slick-next,
  body.boxed .slick-slider:not(.nav-inside):hover .slick-next,
  .content.boxed .slick-slider:not(.nav-inside):hover .slick-prev,
  body.boxed .slick-slider:not(.nav-inside):hover .slick-prev {
    opacity: 1;
  }
}
.nav-mobile .slick-prev,
.nav-mobile .slick-next {
  top: 50% !important;
  width: 22px !important;
  height: 50px !important;
  margin-top: -25px !important;
}
.nav-mobile .slick-next {
  right: 0 !important;
}
.nav-mobile .slick-prev {
  left: 0 !important;
}
.single-slider .slick-prev {
  left: 15px;
}
.single-slider .slick-next {
  right: 15px;
}
.single-slider .slick-dots {
  bottom: -35px;
}
.tp-rightarrow.default,
.tp-leftarrow.default {
  background-image: none !important;
  width: 80px !important;
  height: 80px !important;
}
.tparrows.default.tp-leftarrow .tp-arr-allwrapper,
.tparrows.default.tp-rightarrow .tp-arr-allwrapper {
  position: relative;
  display: block;
  width: 100%;
  height: 100%;
}
.tparrows.default.tp-leftarrow::before,
.tparrows.default.tp-leftarrow::after,
.tparrows.default.tp-leftarrow .tp-arr-allwrapper::before,
.tparrows.default.tp-leftarrow .tp-arr-allwrapper::after,
.tparrows.default.tp-rightarrow::before,
.tparrows.default.tp-rightarrow::after,
.tparrows.default.tp-rightarrow .tp-arr-allwrapper::before,
.tparrows.default.tp-rightarrow .tp-arr-allwrapper::after {
  position: absolute;
  left: 50%;
  width: 3px;
  height: 50%;
  content: '';
  -webkit-transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}
.tparrows.default.tp-leftarrow .tp-arr-allwrapper::before,
.tparrows.default.tp-leftarrow .tp-arr-allwrapper::after,
.tparrows.default.tp-rightarrow .tp-arr-allwrapper::before,
.tparrows.default.tp-rightarrow .tp-arr-allwrapper::after {
  z-index: 100;
  height: 0;
  -webkit-transition: height 0.3s, -webkit-transform 0.3s;
  transition: height 0.3s, transform 0.3s;
}
.tparrows.default.tp-leftarrow::before,
.tparrows.default.tp-leftarrow .tp-arr-allwrapper::before {
  top: 50%;
  -webkit-transform: translateX(-50%) rotate(-135deg);
  transform: translateX(-50%) rotate(-135deg);
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
}
.tparrows.default.tp-rightarrow::before,
.tparrows.default.tp-rightarrow .tp-arr-allwrapper::before {
  top: 50%;
  -webkit-transform: translateX(-50%) rotate(135deg);
  transform: translateX(-50%) rotate(135deg);
  -webkit-transform-origin: 50% 0%;
  transform-origin: 50% 0%;
}
.tparrows.default.tp-leftarrow::after,
.tparrows.default.tp-leftarrow .tp-arr-allwrapper::after {
  top: 50%;
  -webkit-transform: translateX(-50%) rotate(-45deg);
  transform: translateX(-50%) rotate(-45deg);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
}
.tparrows.default.tp-rightarrow::after,
.tparrows.default.tp-rightarrow .tp-arr-allwrapper::after {
  top: 50%;
  -webkit-transform: translateX(-50%) rotate(45deg);
  transform: translateX(-50%) rotate(45deg);
  -webkit-transform-origin: 100% 0%;
  transform-origin: 100% 0%;
}
.tparrows.default.tp-leftarrow:hover .tp-arr-allwrapper::before,
.tparrows.default.tp-leftarrow:hover .tp-arr-allwrapper::after,
.tparrows.default.tp-rightarrow:hover .tp-arr-allwrapper::before,
.tparrows.default.tp-rightarrow:hover .tp-arr-allwrapper::after {
  height: 50%;
}
.tparrows.default.tp-leftarrow:hover::before,
.tparrows.default.tp-leftarrow:hover .tp-arr-allwrapper::before {
  -webkit-transform: translateX(-50%) rotate(-125deg);
  transform: translateX(-50%) rotate(-125deg);
}
.tparrows.default.tp-rightarrow:hover::before,
.tparrows.default.tp-rightarrow:hover .tp-arr-allwrapper::before {
  -webkit-transform: translateX(-50%) rotate(125deg);
  transform: translateX(-50%) rotate(125deg);
}
.tparrows.default.tp-leftarrow:hover::after,
.tparrows.default.tp-leftarrow:hover .tp-arr-allwrapper::after {
  -webkit-transform: translateX(-50%) rotate(-55deg);
  transform: translateX(-50%) rotate(-55deg);
}
.tparrows.default.tp-rightarrow:hover::after,
.tparrows.default.tp-rightarrow:hover .tp-arr-allwrapper::after {
  -webkit-transform: translateX(-50%) rotate(55deg);
  transform: translateX(-50%) rotate(55deg);
}
/*ElevateZoom custom style*/
.zoomContainer {
  height: 0 !important;
}
.zoomContainer:hover {
  z-index: 10000;
  height: inherit !important;
}
/* Parallax Carousel */
.parallax-gallery .carousel-navigation-wrapper {
  height: 650px;
}
@media (max-width: 991px) {
  .parallax-gallery .carousel-navigation-wrapper {
    height: auto !important;
  }
  .parallax-gallery .parallax-gal-carousel.ready .parallax-gal-slide {
    margin: 15px;
  }
}
.parallax-gallery .parallax-gal-carousel.ready .parallax-gal-slide {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  background-color: #ffffff;
}
.parallax-gallery .parallax-gal-carousel.ready .parallax-gal-slide .parallax-gal-caption {
  padding: 15px;
}
.parallax-gallery .parallax-gal-carousel.ready .parallax-gal-slide .parallax-gal-image {
  position: relative;
}
.parallax-gallery .nav-arrow.right.slick-next {
  right: -15px;
}
.parallax-gallery .nav-arrow.left.slick-prev {
  left: -15px;
}
