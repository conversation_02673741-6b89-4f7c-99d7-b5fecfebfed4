.btn {
  font-weight: 500;
}

.sticky-header {
  position: relative;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: -90px;
  width: 100%;
  z-index: 9999;
  transition: 0.3s top cubic-bezier(0.3, 0.73, 0.3, 0.74);
}

.slideDown {
  top: 0;
}

.wonderslate-navbar {
  background-color: #8C1515;
  line-height: normal;
  padding: 12px 24px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
  z-index: 3;
}
.wonderslate-navbar .navbar-container {
  padding-left: 0;
  padding-right: 0;
}
.wonderslate-navbar .navbar-container .navbar-nav.header-menus {
  margin-left: 100px;
}
.wonderslate-navbar .navbar-container .nav.navbar-nav.navbar-right li {
  margin-right: 8px;
}
.wonderslate-navbar .navbar-container .nav.navbar-nav.navbar-right li:last-child {
  margin-right: 0;
}
.wonderslate-navbar .navbar-container .login-btn {
  font-size: 14px;
  color: #4DDCE8;
  font-weight: 500;
  padding: 11px 25px;
  border-radius: 4px;
}
.wonderslate-navbar .navbar-container .signup-btn {
  font-size: 14px;
  display: block;
  text-align: center;
  font-weight: 500;
  color: #FFFFFF;
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  letter-spacing: 0.01em;
  padding: 11px 25px;
  border-radius: 4px;
}
.wonderslate-navbar .navbar-container .signup-btn:hover {
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
}
.wonderslate-navbar .navbar-brand {
  width: 80px;
  height: 40px;
  background: url("../images/blackspine/logo.png");
  background-position: center;
  background-size: 100% 100%;
  display: block;
  text-indent: -9999999px;
}
.wonderslate-navbar ul a {
  font-size: 18px;
  font-weight: 300;
  color: #FFFFFF;
  padding-top: 9px;
}
.wonderslate-navbar ul a:hover {
  background-color: transparent;
}
.wonderslate-navbar ul a.active {
  font-weight: 500;
  color: #FFFFFF;
  background: linear-gradient(270deg, #F05A2A 0%, #FF7245 100%);
  padding: 11px 15px;
  box-shadow: 1px 0px 0px rgba(68, 68, 68, 0.04);
  border-radius: 4px;
}

.user-logged-in {
  display: block;
  width: 40px;
  height: 40px;
  background-color: #fff !important;
  padding: 0 !important;
}
.user-logged-in img {
  max-width: 100% !important;
  height: auto;
  border: 1px solid #BDBDBD;
  border-radius: 100%;
}

.profile-dropdown {
  min-width: 330px;
  left: auto;
  right: -8px !important;
  padding: 24px;
  border-top-right-radius: 4px !important;
  border-top-left-radius: 4px !important;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  overflow-wrap: break-word;
}
.profile-dropdown:before {
  content: "";
  border-bottom: 10px solid #fff;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  position: absolute;
  top: -10px;
  right: 16px;
  z-index: 10;
}
.profile-dropdown:after {
  content: "";
  border-bottom: 12px solid #ccc;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
  position: absolute;
  top: -12px;
  right: 14px;
  z-index: 9;
}
.profile-dropdown a {
  padding: 0;
}
.profile-dropdown a.text-zoom {
  width: 60px;
  height: 40px;
  font-size: 18px;
  font-weight: bold;
  padding: 6px 22px;
  border: 2px solid rgba(68, 68, 68, 0.54);
  border-radius: 4px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  margin-right: 20px;
}
.profile-dropdown li {
  display: inline-block;
}

.user-image {
  position: relative;
  width: 72px;
  height: 72px;
  float: left;
  margin-right: 24px;
  text-align: center;
}
.user-image img {
  max-width: 100%;
  height: auto;
  border: 1px solid #BDBDBD;
  border-radius: 100%;
}
.user-image .user-edit-profile {
  color: #FFFFFF;
  font-size: 8px;
  position: absolute;
  bottom: 2px;
  left: 4px;
  padding: 8px 8px;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
  width: 90%;
  margin-bottom: 0;
  border-bottom-left-radius: 32px;
  border-bottom-right-radius: 32px;
}
.user-image .user-edit-profile:hover {
  color: #FFFFFF;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
}
.user-image .user-edit-profile:focus {
  color: #FFFFFF;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
}
.user-image .user-edit-profile:active {
  color: #FFFFFF;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
}

.logged-in-user-details {
  float: left;
  max-width: 166px;
}
.logged-in-user-details .loggedin-user-name {
  font-weight: 300;
}
.logged-in-user-details .loggedin-user-name .user-name {
  font-weight: 500;
  text-transform: capitalize;
}
.logged-in-user-details .loggedin-user-mobile, .logged-in-user-details .loggedin-user-email {
  font-weight: 300;
}

.user-orders {
  clear: both;
  float: left;
  width: 100%;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid rgba(68, 68, 68, 0.2);
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
.user-orders a {
  color: #444 !important;
}
.user-orders a:hover {
  color: #444;
  text-decoration: none;
}
.user-orders a:active {
  color: #444;
  text-decoration: none;
}
.user-orders a:focus {
  color: #444;
  text-decoration: none;
}

.user-logout {
  clear: both;
  float: left;
  width: 100%;
  margin-top: 16px;
}
.user-logout p {
  font-weight: 300;
  color: #000;
  margin: 0;
}
.user-logout p a {
  font-weight: 400;
  color: #F05A2A;
}

/*# sourceMappingURL=blackspine.css.map */
