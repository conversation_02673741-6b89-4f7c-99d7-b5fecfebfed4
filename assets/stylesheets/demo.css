body {
  /*margin: 0;*/
  /*padding: 20px;*/
  font-family: '<PERSON>o', sans-serif;
  font-weight: 300;
  background-color: #f0f0f0;
}
#content button {
  cursor: pointer;
}
.imageSize {
    /*background-color: white;*/
}
.imageSize img {
    width: 100%;
    height: 100%;
    margin-bottom: 15px;
    margin-top: 15px;
}

.tour-shadow {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
                0 2px 10px 0 rgba(0, 0, 0, .12);
    background-color: #f0f0f0;
}
h1 {
/*  clear: left;
  margin-top: 20px;*/
}
.text-center {
  text-align: center;
}
.demo-headline {
  text-align: left;
  padding: 40px 0 0;
}
.tile {
  min-height: 330px;
}
#credits {
  margin-top: 100px;
}
