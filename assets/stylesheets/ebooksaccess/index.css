.ebooksaccess .curve-bg {
  position: absolute;
  top: 90px;
  right: 0;
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .curve-bg {
    top: 120px;
  }
}
.ebooksaccess .curve-bg img {
  width: 800px;
}
@media only screen and (min-width: 1201px) and (max-width: 1300px) {
  .ebooksaccess .curve-bg img {
    width: 700px;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1200px) {
  .ebooksaccess .curve-bg img {
    width: 600px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ebooksaccess .curve-bg img {
    width: 500px;
  }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ebooksaccess .banner_wrap {
    margin-top: 0 !important;
  }
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap {
    margin-top: -150px !important;
    padding-top: 8rem !important;
    border-radius: 0px 0px 20px 20px;
  }
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap .banner_info {
    margin-top: 5rem;
  }
}
.ebooksaccess .banner_wrap h1 {
  color: #444444;
  font-weight: normal;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ebooksaccess .banner_wrap h1 {
    font-size: 2rem;
  }
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap h1 {
    color: #ffffff;
    font-size: 1.6rem;
    justify-content: center;
  }
}
.ebooksaccess .banner_wrap h1 ion-icon {
  padding: 0 10px;
  color: rgba(68, 68, 68, 0.48);
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap h1 ion-icon {
    padding: 0 5px;
    color: #ffffff;
    font-size: 30px;
  }
}
.ebooksaccess .banner_wrap h1 span {
  color: rgba(68, 68, 68, 0.48);
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap h1 span {
    color: #ffffff;
  }
}
.ebooksaccess .banner_wrap h5 {
  color: rgba(68, 68, 68, 0.48);
  font-weight: normal;
  font-size: 1.15rem;
  font-family: 'Poppins', sans-serif;
  line-height: 28px;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ebooksaccess .banner_wrap h5 {
    font-size: 1.1rem;
    line-height: 25px;
  }
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap h5 {
    font-size: 0.9rem;
    line-height: 22px;
    text-align: center;
  }
}
.ebooksaccess .banner_wrap button {
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border-width: 1.25px;
  width: 140px;
  font-weight: normal;
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap button {
    width: 120px;
  }
}
.ebooksaccess .banner_wrap a.btn {
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border-width: 1.25px;
  width: 140px;
  font-weight: normal;
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap a.btn {
    width: 120px;
  }
}
.ebooksaccess .banner_wrap .login-btn {
  border-color: #444444;
  background: none;
}
.ebooksaccess .banner_wrap .signup-btn {
  background: radial-gradient(196.34% 2285.13% at -0.25% 0%, #358EF0 0%, #394696 100%);
  border-color: #3499FF;
  color: #ffffff;
}
.ebooksaccess .banner_wrap p,
.ebooksaccess .banner_wrap i.fa {
  color: rgba(68, 68, 68, 0.48);
  margin-bottom: 0;
}
.ebooksaccess .banner_wrap i.fa {
  font-size: 24px;
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap i.fa {
    font-size: 22px;
  }
}
.ebooksaccess .banner_wrap .banner_img {
  margin-top: -7rem;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  .ebooksaccess .banner_wrap .banner_img {
    margin-top: -5rem;
  }
}
@media only screen and (max-width: 767px) {
  .ebooksaccess .banner_wrap .banner_img {
    margin-top: 4rem;
    margin-bottom: -40px;
  }
}
.ebooksaccess .banner_wrap .banner_img img {
  width: 100%;
}
