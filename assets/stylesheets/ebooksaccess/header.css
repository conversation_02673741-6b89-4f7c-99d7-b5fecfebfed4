body {
  font-family: 'Poppins', sans-serif;
  margin: 0;
  padding: 0;
  font-size: 15px;
}
a:hover {
  cursor: pointer !important;
}
.btn {
  font-family: 'Poppins', sans-serif;
}
.btn-warning {
  background-color: #e18b1e !important;
  border-color: #e18b1e !important;
  color: white !important;
}
.btn-primary {
  background-color: #26358c !important;
  border-color: #26358c !important;
  color: white !important;
}
.btn-outline-primary {
  border-color: #26358c !important;
  color: #26358c !important;
}
.btn-outline-primary:hover {
  background-color: transparent !important;
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px white inset;
}
::-webkit-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Poppins', sans-serif;
  font-size: 15px !important;
}
::-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Poppins', sans-serif;
  font-size: 15px !important;
}
:-ms-input-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Poppins', sans-serif;
  font-size: 15px !important;
}
:-moz-placeholder {
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Poppins', sans-serif;
  font-size: 15px !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  color: rgba(68, 68, 68, 0.3);
  font-family: 'Poppins', sans-serif;
  font-size: 15px !important;
}
input[type='text'] {
  font-family: 'Poppins', sans-serif;
}
html p,
html a,
html h1,
html h2,
html h3,
html h4,
html span,
html td,
html th {
  font-family: 'Poppins', sans-serif;
}
header {
  background: #ece1b5 !important;
  display: block;
  align-items: center;
}
header > div {
  display: flex;
  align-items: center;
}
header .logo {
  /*background-color: #ffffff;*/
  padding: 0;
  /*border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;*/
  display: inline-block;
  align-items: center;
  height: 60px;
}
header .logo img {
  width: 180px;
  height: 100%;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  header .logo img {
    width: 140px;
  }
}
@media only screen and (max-width: 767px) {
  header .logo img {
    width: 120px;
  }
}
header .logo img.publisher-logo79 {
  width: auto;
}
header .ebooksaccess-logo img {
  width: 120px;
}
header .divider img {
  height: 60px;
}
@media only screen and (max-width: 767px) {
  header .divider img {
    height: 40px;
  }
}
header .right-menu {
  /*background: #ffffff;*/
  padding: 0;
  /*border-top-left-radius: 50px;
    border-bottom-left-radius: 50px;*/
  display: flex;
  align-items: center;
  height: 60px;
}
header .ws-header {
  border-bottom: none !important;
  background: transparent;
}
@media only screen and (max-width: 767px) {
  header .ws-header {
    height: 40px;
  }
}
header .ws-header .navbar-nav .nav-link {
  color: #394fa1;
}
header .ws-header .navbar-nav.right-menu li .nav-link {
  color: #394fa1;
  padding-left: 3rem;
  padding-right: 0;
  font-size: 15px;
  text-align: center;
  font-weight: 500;
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
  header .ws-header .navbar-nav.right-menu li .nav-link {
    padding-left: 1.5rem;
  }
}
@media only screen and (max-width: 767px) {
  header .ws-header .navbar-nav.right-menu li .nav-link {
    padding: 0;
  }
}
header .ws-header .navbar-nav.right-menu li .nav-link:after {
  color: #862AA5;
}
header .ws-header .navbar-nav.right-menu li .nav-link.loginButton {
  padding: 6px 30px;
  border-radius: 15px;
  border: 1.25px solid rgba(68, 68, 68, 0.85);
  font-size: 14px;
  background: none;
  -webkit-text-fill-color: unset;
  -webkit-background-clip: unset;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
header .ws-header .navbar-nav.right-menu li .nav-link.loginButton:hover {
  border-color: #3499FF;
  color: #3499FF;
}
.LibWonder {
  position: relative;
  box-shadow: 0 0 7px 0px #CCC;
}
.LibWonder .order-pr {
  display: none;
}
.libwonder .user_profile {
  min-height: calc(100vh - 175px);
}
.libwonder .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn {
  width: 20px;
  height: 20px;
  display: inline-block;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.94);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  position: absolute;
  left: 65px;
  bottom: 20px;
  text-align: center;
}
.libwonder .input-login label > span {
  position: absolute;
  font-weight: normal;
}
.libwonder .bookTemplate .tab-header > .navbar {
  position: sticky;
  box-shadow: none;
}
.libwonder .bookTemplate .content-wrapper {
  margin-top: 0;
}
.libwonder.custom-fix .bookTemplate .shadowHeader {
  position: fixed;
  top: 0;
}
header .ws-header .navbar-nav.right-menu li .nav-link:after {
  position: relative;
  top: 2px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media .media-body p {
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .media {
  border-bottom: 1px solid #EDEDED;
  position: relative;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu a {
  position: relative;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item {
  padding: 0.4rem 1rem;
  font-size: 13px;
  border-bottom: 1px solid #EDEDED;
  color: #444444;
  font-weight: 500;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item:hover {
  color: #358EF0;
}
@media only screen and (max-width: 767px) {
  .ws-header .navbar-nav.right-menu {
    display: flex !important;
    height: 40px;
  }
}
.ws-header .navbar-nav.right-menu li a {
  font-weight: 500;
}
.ws-header .navbar-nav.right-menu li img.drop-profile {
  width: 72px;
  height: 72px;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .edit-btn i {
  color: rgba(68, 68, 68, 0.74);
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item:last-child {
  border-bottom: none;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item:active {
  background-color: transparent;
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu {
  min-width: 180px;
  margin-top: 10px;
  padding-top: 0;
  box-shadow: 0 5px 10px 0 #ccc;
  padding-bottom: 0;
}
@media only screen and (max-width: 767px) {
  .ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu {
    position: absolute;
  }
}
.ws-header .navbar-nav.right-menu li a img {
  width: 46px;
  height: 46px;
}
@media only screen and (max-width: 767px) {
  .ws-header .navbar-nav.right-menu li a img {
    width: 36px;
    height: 36px;
  }
}
.ws-header .navbar-nav.right-menu li.dropdown .dropdown-menu .dropdown-item span {
  color: #3499FF;
}
.mobile-bottom-menu-wrapper {
  display: none;
}
@media only screen and (max-width: 767px) {
  .libwonder header .ws-menu-start {
    width: 100%;
  }
}
.libwonder header .ws-header .navbar-nav.right-menu {
  position: relative;
}
@media only screen and (max-width: 767px) {
  .libwonder header .ws-header .navbar-nav.right-menu {
    justify-content: space-between;
    width: 100%;
    flex-direction: unset;
  }
}
.libwonder header.LibWonder {
  box-shadow: none !important;
  border-bottom: 1px solid #ededed;
}
.libwonder .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 100px !important;
}
.libwonder.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 0px !important;
}
.libwonder .bookTemplate .content-wrapper .chapterSection a.slide-toggle {
  top: 290px !important;
}
.libwonder.hasScrolled .bookTemplate .content-wrapper .chapterSection a.slide-toggle {
  top: 200px !important;
}
.libwonder .bookTemplate .export-notes {
  top: 140px;
}
@media only screen and (max-width: 768px) {
  .libwonder #chapters-toggle.left i {
    transform: rotate(0deg);
  }
  .libwonder.hasScrolled .read-content {
    margin-top: 0px;
  }
  .libwonder .ws-header div.mobile-profile .nav-item {
    margin: 0 7px;
  }
  .libwonder .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
    margin-top: 150px !important;
  }
}
.modal-header {
  display: block;
}
#book-sidebar {
  top: 70px;
}
@media only screen and (max-width: 767px) {
  #book-sidebar {
    top: 0px;
  }
}
