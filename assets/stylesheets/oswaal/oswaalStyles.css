.oswaal_books .white-logo-anchor-white img {
  width: 60px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswaal_books .white-logo-anchor-white img {
    width: 100%;
  }
}
.oswaal_books .manage-logo-big-menu-arihant img {
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswaal_books .manage-logo-big-menu-arihant img {
    width: 80%;
  }
}
.oswaal_books .categories-section-disc-title-pera {
  color: #212121;
}
.oswaal_books a {
  font-weight: normal;
}
.oswaal_books p {
  line-height: normal;
}
.oswaal_books .global-search input[type="text"] {
  padding-left: 10px;
  padding-right: 40px;
  z-index: 3;
}
@media (max-width: 320px) {
  .oswaal_books .global-search input[type="text"] {
    padding-right: 20px;
  }
}
.oswaal_books .global-search button {
  width: auto;
  height: 33px;
  margin-left: -38px;
  padding: 4px;
  position: relative;
  z-index: 10;
  color: #0b65b3 !important;
}
.oswaal_books .global-search button .material-icons {
  line-height: normal;
}
.oswaal_books .add-tabs {
  top: 75px;
}
.oswaal_books #allAddButton {
  display: none !important;
}
.oswaal_books.hasScrolled .main-menu-wrp {
  position: relative !important;
}
.oswaal_books.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 0;
}
.oswaal_books.hasScrolled .bookTemplate .export-notes {
  top: 52px;
}
.oswaal_books.hasScrolled .user-menu-wrp .menu-actives a.menu-dots-img-wrp {
  position: fixed;
}
.oswaal_books .bookTemplate .content-wrapper {
  height: calc(100vh - 50px);
}
@media (max-width: 767px) {
  .oswaal_books .bookTemplate.book_preview .content-wrapper .read-content.col-md-12 .price-wrapper {
    display: none !important;
  }
  .oswaal_books .bookTemplate.book_preview .content-wrapper #book-read-material {
    padding-bottom: 70px;
  }
  .oswaal_books .bookTemplate.book_preview .content-wrapper #book-sidebar {
    height: calc(100vh - 70px);
    padding-bottom: 0;
  }
  .oswaal_books .bookTemplate.book_preview .content-wrapper .price-wrapper {
    padding: 0.5rem 0;
    position: fixed;
    width: 100%;
    z-index: 991;
  }
  .oswaal_books .bookTemplate.book_preview .content-wrapper .price-wrapper .section-btns {
    padding: 0.5rem 0 0;
    background: #FFFFFF;
  }
  .oswaal_books .bookTemplate.book_preview .content-wrapper .price-wrapper .preview-book-btns {
    margin-left: 0;
  }
}
.oswaal_books .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 100px;
}
.oswaal_books .bookTemplate .export-notes {
  top: 130px;
}
.oswaal_books .bookTemplate .mobChapname #chapters-toggle.left i {
  transform: rotate(0deg);
}
.oswaal_books .bookTemplate .preview-book-btns .btn-book-buy {
  background: #0b65b3 !important;
}
.oswaal_books .bookTemplate .ChapterHeader .bookTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.oswaal_books .web-mcq .sub-header {
  top: 90px;
  padding-top: 10px;
}
.oswaal_books .bg-wsTheme {
  background: #0b65b3 !important;
}
.oswaal_books #question-block .question-wrapper {
  margin-top: 2rem;
}
.oswaal_books #question-block .question-wrapper img {
  max-width: 100%;
}
.oswaal_books .tab-wrappers {
  top: 190px;
}
.oswaal_books .web-mcq .result-menu {
  top: 90px;
}
.oswaal_books #quizQuestionSection {
  padding-bottom: 50px;
}
.oswaal_books .book_details_info #filelabel1,
.oswaal_books .book_details_info #filelabel2 {
  right: 0;
  left: 0;
}
.oswaal_books #bookcover .smallText {
  justify-content: center;
}
.oswaal_books .orders .payment-details > div:last-child p .rupees {
  display: none;
}
.oswaal_books .users-orders > p {
  padding-right: 15px;
  padding-left: 15px;
}
.oswaal_books .user_profile .tab-content .jumbotron form .media .continue,
.oswaal_books .btn-starts,
.oswaal_books #answer-block .button-wrapper a,
.oswaal_books #answer-block .button-wrapper a:hover {
  background: #0b65b3 !important;
}
.oswaal_books .test-gen-box-main .test-gen-box .btn-info {
  background: #0b65b3 !important;
  border-color: #0b65b3 !important;
}
.oswaal_books .test-gen-box-main .test-gen-box .btn-info:active:focus {
  box-shadow: 0 0 0 0.2rem rgba(239, 114, 21, 0.5) !important;
}
.oswaal_books .dropdown-menu a:active,
.oswaal_books .dropdown-menu span:active,
.oswaal_books .dropdown-menu li:active {
  background-color: #fce5d4;
}
.oswaal_books .all-container .container-wrapper .media .quiz-practice-btn,
.oswaal_books .all-container .container-wrapper .media .showRank {
  text-transform: uppercase;
  color: #0b65b3;
  padding: 0;
  display: inherit;
  border-radius: 0;
}
.oswaal_books .all-container .container-wrapper .media .showRank {
  padding-left: 10px;
  margin-left: 10px;
  border-left: 1px solid #212121;
}
.oswaal_books .all-container .container-wrapper .d-flex p.testStarts {
  margin: 0;
  top: 0;
  font-size: 12px;
  flex: none;
}
.oswaal_books .play::before {
  left: -20px;
}
.oswaal_books .backfromgenerator {
  margin-top: 20px;
}
.oswaal_books .backfromgenerator i {
  color: #0b65b3;
}
.oswaal_books #htmlContent {
  margin-top: 2rem;
}
.oswaal_books .purchase-details-container .purchase-heading {
  background: none !important;
  -webkit-text-fill-color: unset !important;
}
.oswaal_books .purchase-details-container .browse-purchase-book a.learn-btn {
  background: #0b65b3 !important;
  color: #FFFFFF;
}
.oswaal_books .web-mcq .mt-fixed {
  margin-top: 3.5rem !important;
  padding-top: 0;
}
.oswaal_books.custom-fix .bookTemplate .shadowHeader {
  position: fixed;
  top: 0;
}
.oswaal_books .start-test .header p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  padding: 0 15px;
}
.oswaal_books #quizQuestionSection .result-menu > div h2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding: 0 10px;
}
.oswaal_books .mt-fixed #resourceTitle {
  margin-top: 50px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  padding: 0 15px;
}
.oswaal_books .notes-creation-header {
  z-index: 1;
}
.oswaal_books .index-page .main-menu-wrp {
  position: relative;
}
.oswaal_books .index-page .header-menu-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  text-align: center;
  width: 100%;
}
.oswaal_books .index-page .this-is-a-web-view-slider {
  display: block;
  overflow: hidden;
}
@media (max-width: 991px) {
  .oswaal_books .index-page .this-is-a-web-view-slider {
    display: none;
  }
}
.oswaal_books .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  min-height: 80vh;
  background-size: 100% auto !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}
@media (min-width: 1600px) {
  .oswaal_books .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
    background-position: left top !important;
  }
}
.oswaal_books .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
  width: 100%;
  max-width: 100%;
  min-height: auto;
  max-height: 70vh;
}
@media (min-width: 2000px) {
  .oswaal_books .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
    min-height: auto;
    max-height: 70vh;
  }
}
@media (max-width: 1199px) {
  .oswaal_books .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
    min-height: auto;
    max-height: 100%;
  }
}
.oswaal_books .index-page .this-is-a-responsive-view-slider {
  display: none;
}
@media (max-width: 991px) {
  .oswaal_books .index-page .this-is-a-responsive-view-slider {
    display: block;
  }
}
.oswaal_books .index-page .this-is-a-responsive-view-slider img {
  width: 100%;
}
.oswaal_books .index-page .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #c2e1fb;
}
.oswaal_books .index-page .carousel-indicators li.active {
  background: #0b65b3;
}
.oswaal_books .ebooks .ebooks_filter {
  width: 100%;
}
.mozilla .arihant .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  background-attachment: unset !important;
}
#guestUser,
#loginOpen,
#signup,
#forgotPasswordmodal,
#shareContentModal,
#deleteBook,
#change-password-modal,
#currentPomodoro,
#pomodoroSessionCompletion,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal,
#bookQueueModal,
#test-gen-modal,
#PlayAudiOnlyModal,
#quizModal {
  z-index: 9992;
}
.mobile-footer-nav {
  background: #0b65b3;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -webkit-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -moz-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px 20px 0 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.mobile-footer-nav.hide-menus {
  bottom: -75px;
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
}
.mobile-footer-nav a {
  text-decoration: none;
  flex-wrap: wrap;
  text-align: center;
  color: #FFFFFF;
}
.mobile-footer-nav a:focus {
  text-decoration: none;
}
.mobile-footer-nav a:visited {
  text-decoration: none;
}
.mobile-footer-nav a img {
  margin: 0 auto;
  width: 22px;
}
.mobile-footer-nav a p {
  width: 100%;
  font-size: 13px;
}
.mobile-footer-nav i {
  color: white;
}
.mobile-footer-nav .active-menu {
  opacity: 1;
}
.mobile-footer-nav .common-footer-nav {
  opacity: 0.8;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
#test-gen-modal .overlay-testgen-book .book-selected {
  width: 40px;
  height: 40px;
}
#quizQuestionSection #submit-test .modal-footer button {
  font-family: 'Poppins', sans-serif !important;
}
#quizQuestionSection #submit-test .modal-footer .submit {
  background: #0b65b3;
  color: #FFFFFF;
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: portrait), only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .arihant #book-read-material #content-data-all {
    padding: 0;
  }
  .arihant #book-read-material #content-data-all > .container {
    padding: 0;
  }
  .arihant .all-container .container-wrapper {
    margin-top: 0;
    border: none;
    border-bottom: 1px solid #ededed;
    box-shadow: none;
    border-radius: 0;
  }
  .arihant .all-container .container-wrapper .media i {
    margin: 0 1rem;
  }
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .arihant .bookTemplate .export-notes {
    top: 51px !important;
  }
}
@media only screen and (max-width: 767px) {
  .arihant h2#expiry-date {
    margin-top: 0 !important;
  }
}
.footer .download-app-links .android-app-link {
  border: 1px solid #ddd;
  padding: 2px 0 5px;
  border-radius: 7px;
  min-width: 140px;
  background-color: #e5e5e5;
}
.footer .download-app-links .android-app-link:hover {
  background-color: #e7e7e7;
}
.footer .download-app-links .android-app-link img {
  width: 20px;
  margin-right: 10px;
  height: auto;
}
.footer .download-app-links .android-app-link span {
  line-height: normal;
  font-size: 15px;
}
.footer .download-app-links .android-app-link span small {
  position: relative;
  top: 3px;
}
.footer .image-wrapper-footer-logo img {
  width: 260px;
}
.oswaal_books #total-books-of-user {
  display: none;
}
.oswaal_books #loginOpen .modal-header .close,
.oswaal_books #signup .modal-header .close,
.oswaal_books #forgotPasswordmodal .modal-header .close {
  font-size: 20px;
}
.oswaal_books .main-menu-wrp {
  background: linear-gradient(90deg, #8fc4ff 0%, #0b65b3 100%);
}
.oswaal_books .main-menu-wrp .posi-static-respons {
  display: flex;
  align-items: center;
}
.oswaal_books p,
.oswaal_books a,
.oswaal_books button {
  font-weight: initial;
}
.oswaal_books .bookTemplate .side-content ol li.chapter-name i {
  position: relative;
  top: 4px;
}
.oswaal_books .bookTemplate .chapterSection a.slide-toggle {
  top: 190px;
}
.oswaal_books .manage-count-wrp-box:after {
  background: linear-gradient(50deg, #0b65b3 0%, #89c1ff 100%);
}
.oswaal_books .connect-section {
  background: linear-gradient(90deg, #0b65b3 0%, #89c1ff 100%);
}
.oswaal_books .red-color-fill-bg {
  background: #0b65b3;
}
.oswaal_books .responsive-padding-none:nth-child(even) a {
  background-color: #ee3539 !important;
}
.oswaal_books ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss,
.oswaal_books ul.this-is-side-wrp-ul-big-menu-arihant li:hover {
  background: linear-gradient(90deg, #0b65b3 0%, #89c1ff 100%);
}
.oswaal_books .categories-section {
  background: none;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: auto;
}
.oswaal_books .categories-section .line-box-category {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 50px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswaal_books .categories-section .line-box-category {
    bottom: 20px;
  }
}
.oswaal_books .ebook_detail .book_info .book_buttons .col #buyNow {
  color: #FFFFFF;
}
.oswaal_books #okBuy {
  color: #FFFFFF;
}
.oswaal_books .there-social-footer-link-wrp li a:hover {
  color: #0b65b3;
}
.oswaal_books .my_books .no-books-available .click-here-link {
  background-color: #0b65b3 !important;
  color: #FFFFFF;
}
.oswaal_books #cartModalBtns .btn-primary {
  background-color: #0b65b3 !important;
  border-color: #0b65b3 !important;
  box-shadow: none !important;
  color: #FFFFFF !important;
}
.oswaal_books .bookTemplate .shadowHeader {
  height: 50px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswaal_books .bookTemplate {
    height: auto;
  }
  .oswaal_books .bookTemplate .mobChapname {
    transition: all 0.3s;
    z-index: 991;
  }
  .oswaal_books .bookTemplate .shadowHeader {
    z-index: 991;
    height: 45px !important;
    position: fixed;
    transition: all 0.3s;
  }
  .oswaal_books .bookTemplate .shadowHeader .tab-header .navbar {
    height: auto;
    padding-top: 0.15rem;
  }
  .oswaal_books .bookTemplate .shadowHeader .tab-header .contentEdit {
    position: fixed;
    top: 50px;
    right: 0;
    transition: all 0.3s;
    overflow: hidden;
  }
  .oswaal_books .bookTemplate .shadowHeader .prevnextbtn {
    position: fixed;
    top: 50px;
    width: 100% !important;
    justify-content: center !important;
    transition: all 0.3s;
  }
  .oswaal_books .bookTemplate .shadowHeader .prevnextbtn button {
    margin: 0 5px;
    width: 80px;
    font-size: 13px;
  }
  .oswaal_books .bookTemplate .chapterSection {
    z-index: 991;
  }
  .oswaal_books .bookTemplate #book-sidebar .backtolibrary {
    font-weight: normal !important;
  }
  .oswaal_books .bookTemplate #book-sidebar .mobile-title {
    z-index: 98;
  }
  .oswaal_books .bookTemplate #book-sidebar .mobile-title p {
    line-height: normal;
    padding-left: 10px;
  }
  .oswaal_books .bookTemplate #book-sidebar .side-content ol {
    padding-left: 10px !important;
  }
  .oswaal_books .bookTemplate #book-sidebar .side-content ol li.chapter-name {
    font-size: 15px;
    position: relative;
    margin-right: 1.5rem;
    padding-right: 20px;
  }
  .oswaal_books .bookTemplate #book-sidebar .side-content ol li.chapter-name.orangeText a {
    font-size: 15px;
  }
  .oswaal_books .bookTemplate #book-sidebar ul.chapter-sections {
    display: none;
  }
  .oswaal_books .bookTemplate #book-read-material #content-data-all {
    position: relative;
    z-index: 99;
  }
  .oswaal_books .bookTemplate #book-read-material .all-container {
    margin-top: 2rem;
  }
  .oswaal_books .bookTemplate #book-read-material .all-container .container-wrapper {
    width: 100%;
    margin-top: 1rem;
    min-height: auto;
  }
  .oswaal_books .bookTemplate #book-read-material .all-container .container-wrapper .media {
    padding: 0;
  }
  .oswaal_books .bookTemplate #book-read-material .all-container .container-wrapper .media i {
    margin-left: 0;
  }
  .oswaal_books .bookTemplate #book-read-material .all-container .container-wrapper .media .title {
    margin-bottom: 5px;
  }
  .oswaal_books .bookTemplate #book-read-material .all-container .container-wrapper .media .readnow {
    padding-right: 15px;
  }
  .oswaal_books .bookTemplate #htmlreadingcontent iframe {
    height: 100vh !important;
    border: 1px solid #6C757D;
    margin-top: 0 !important;
  }
  .oswaal_books .bookTemplate .export-notes {
    top: 90px !important;
  }
  .oswaal_books .bookTemplate .export-notes .notes-creation-header {
    padding: 0.5rem 0;
  }
  .oswaal_books .bookTemplate .export-notes .notes-creation-header-title {
    font-size: 16px;
    color: #212121;
  }
  .oswaal_books.hasScrolled .bookTemplate .tab-header .contentEdit {
    top: 5px;
  }
  .oswaal_books.hasScrolled .bookTemplate .export-notes {
    top: 45px !important;
  }
  .oswaal_books.hasScrolled .bookTemplate .shadowHeader .prevnextbtn {
    top: 7px;
  }
  .oswaal_books .logo-wrapper {
    display: none;
  }
  .oswaal_books .my_books #subjectFilter .dropdown #sortBy {
    width: auto;
  }
}
.oswaal_books .purchase-details-container .purchase-details-wrapper .learn-btn {
  background: #0b65b3;
}
.oswaal_books .menu-overlay-big-menus.actv {
  background: rgba(0, 0, 0, 0.7);
}
.oswaal_books .menu-bar-wrp ul li a {
  color: #6C757D;
  border-color: #6C757D;
}
.oswaal_books .menu-bar-wrp ul li a:hover {
  color: #0b65b3;
}
.category_list {
  display: flex;
  flex-direction: column;
}
.category_list .category_level {
  margin-bottom: 2rem;
}
.category_list .category_level h4 {
  position: relative;
  margin-bottom: 1.4rem;
}
.category_list .category_level h4:after {
  width: 50px;
  height: 2px;
  content: '';
  position: absolute;
  background: #0b65b3;
  left: 0;
  bottom: -4px;
}
.category_list .category_level .category_cards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 1.2rem;
}
@media (max-width: 768px) {
  .category_list .category_level .category_cards {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 1rem;
  }
}
.category_list .category_level .category_cards .category_card {
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 80px;
  background: #eee;
  transition: all 0.3s ease-in;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000;
}
.category_list .category_level .category_cards .category_card:active {
  transform: scale(0.7);
}
.category_list .category_level .category_cards .category_card:hover {
  background: transparent;
}
.category_list .category_level .category_cards .category_card a {
  color: black;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.category_list .category_level .category_cards .category_card-title {
  width: 100%;
  text-align: center;
}
@media (max-width: 768px) {
  .category_list .category_level .category_cards .category_card {
    width: 100%;
  }
}
.headerCategoriesMenu {
  border-bottom: 1px solid rgba(0, 0, 0, 0.175);
}
.headerCategoriesMenu .header__categories {
  background: #F5F5F5 !important;
}
.headerCategoriesMenu .header__categories-list__item:not(:last-child) {
  border-right-color: #0b65b3;
}
.headerCategoriesMenu .header__categories-list__item a {
  color: #212121;
}
#accordion .card-body {
  background: #e5e5e59c;
}
#accordion .card-header {
  background: #dadada;
}
#accordion .card a {
  color: #000;
}
#accordion .card-header .btn-link:after {
  color: #000;
}
#accordion .card-header .btn-link.collapsed:after {
  color: #000;
}
ul.link-ul-footer li a {
  padding-bottom: 6px;
}
.there-social-footer-link-wrp li a {
  padding: 10px 10px !important;
  width: 50px;
}
.loading-icon {
  z-index: 99999 !important;
}
.oswaal__header {
  position: sticky !important;
  top: 0;
  z-index: 99999;
}
.oswaal__header-nav {
  display: flex;
  align-items: center;
  padding: 1.2rem 2rem;
  justify-content: space-between;
  background: #fff;
  flex-wrap: wrap;
}
.oswaal__header-nav__list {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  list-style: none;
  margin-bottom: 0;
  padding-left: 0;
}
.oswaal__header-nav__list li a {
  color: #000;
  font-size: 16px;
  font-weight: 400;
}
.oswaal__header-nav__list li {
  margin-right: 12px;
}
.oswaal__header-nav .global-search {
  width: 400px;
}
.oswaal__header-nav .global-search form {
  margin: 0 auto;
  display: flex !important;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.3);
  padding-right: 0 !important;
  padding-left: 0 !important;
  width: 100%;
}
.oswaal__header-nav .global-search {
  position: relative;
}
#search-btn-header {
  padding: 5px;
  display: flex;
  align-items: center;
}
.headerCategoriesMenu .header__categories-list__item a {
  color: #fff;
}
.headerCategoriesMenu .header__categories {
  background: #1f87c8 !important;
  padding: 10px;
}
.arihant .navbar_cart .cart_count,
.whitelabel-site .navbar_cart .cart_count {
  left: 9px;
  top: -10px;
}
.logoImgOswaal {
  width: 200px !important;
}
.bottomLinks {
  display: flex;
  align-items: center;
}
.mobCatAcc {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .oswaal__header {
    height: auto;
  }
  .oswaal__header-nav {
    padding: 0.75rem 0.4rem;
    flex-direction: column;
  }
  .global-search form {
    height: fit-content !important;
  }
  .logoAndCart {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;
  }
  .global-search {
    align-items: center;
    padding-left: 0!important;
    width: 100%;
    gap: 1rem;
    margin-top: 1rem;
  }
  .oswaal__header-nav {
    flex-wrap: nowrap;
  }
  .fa-solid.fa-bars {
    font-size: 25px;
  }
  .activeMenuDrawer {
    transform: translateX(0) !important;
  }
  .mobMenuDrawerClose {
    display: flex;
    justify-content: end;
    padding: 10px;
    width: 100%;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 999;
  }
  .fa-solid.fa-xmark {
    font-size: 24px;
  }
  .oswaal__header-nav__list {
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 0;
    padding-left: 15px;
    padding-right: 15px;
    flex-direction: column;
    width: 80%;
    position: fixed;
    top: 0;
    height: 100vh;
    left: 0;
    transform: translateX(-200%);
    transition: all 0.3s ease;
    z-index: 99999;
    background: #fff;
    box-shadow: 0 5rem 5rem rgba(0, 0, 0, 0.5);
    overflow: scroll;
  }
  .oswaal__header-nav .global-search {
    width: 100%;
  }
  .oswaal__header-nav__list li {
    margin-bottom: 10px;
  }
  .navbar_cart .mobile_cart_icon .cart_count {
    right: 35px !important;
    top: -15px !important;
  }
  .navbar_cart {
    margin-top: 20px;
  }
  .minibanner {
    margin-top: 0px;
  }
  .bottomLinks {
    flex-direction: column;
    margin-top: auto;
    align-items: flex-start;
  }
  .mobCatAcc {
    display: block;
    width: 100%;
    padding-top: 12px;
    border-top: 1px dashed rgba(0, 0, 0, 0.2);
  }
  .mobile-footer-nav {
    background: #065091;
  }
}
