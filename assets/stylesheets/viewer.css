/* Copyright 2014 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#viewerContainer{
  background: #f8f9fa !important;
}
#viewer{
  padding-bottom: 100px;
}
.textLayer {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
}

.textLayer > span {
  color: transparent;
  position: absolute;
  white-space: pre;
  cursor: text;
  -webkit-transform-origin: 0% 0%;
  transform-origin: 0% 0%;
}

.textLayer .highlight {
  margin: -1px;
  padding: 1px;
  background-color: rgba(180, 0, 170, 1);
  border-radius: 4px;
}

.textLayer .highlight.begin {
  border-radius: 4px 0px 0px 4px;
}

.textLayer .highlight.end {
  border-radius: 0px 4px 4px 0px;
}

.textLayer .highlight.middle {
  border-radius: 0px;
}

.textLayer .highlight.selected {
  background-color: rgba(0, 100, 0, 1);
}

.textLayer ::-moz-selection {
  background: rgba(0, 0, 255, 1);
}

.textLayer ::selection {
  background: rgba(0, 0, 255, 1);
}

.textLayer .endOfContent {
  display: block;
  position: absolute;
  left: 0px;
  top: 100%;
  right: 0px;
  bottom: 0px;
  z-index: -1;
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.textLayer .endOfContent.active {
  top: 0px;
}


.annotationLayer section {
  position: absolute;
}

.annotationLayer .linkAnnotation > a,
.annotationLayer .buttonWidgetAnnotation.pushButton > a {
  position: absolute;
  font-size: 1em;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.annotationLayer .linkAnnotation > a:hover,
.annotationLayer .buttonWidgetAnnotation.pushButton > a:hover {
  opacity: 0.2;
  background: rgba(255, 255, 0, 1);
  box-shadow: 0px 2px 10px rgba(255, 255, 0, 1);
}

.annotationLayer .textAnnotation img {
  position: absolute;
  cursor: pointer;
}

.annotationLayer .textWidgetAnnotation input,
.annotationLayer .textWidgetAnnotation textarea,
.annotationLayer .choiceWidgetAnnotation select,
.annotationLayer .buttonWidgetAnnotation.checkBox input,
.annotationLayer .buttonWidgetAnnotation.radioButton input {
  background-color: rgba(0, 54, 255, 0.13);
  border: 1px solid transparent;
  box-sizing: border-box;
  font-size: 9px;
  height: 100%;
  margin: 0;
  padding: 0 3px;
  vertical-align: top;
  width: 100%;
}

.annotationLayer .choiceWidgetAnnotation select option {
  padding: 0;
}

.annotationLayer .buttonWidgetAnnotation.radioButton input {
  border-radius: 50%;
}

.annotationLayer .textWidgetAnnotation textarea {
  font: message-box;
  font-size: 9px;
  resize: none;
}

.annotationLayer .textWidgetAnnotation input[disabled],
.annotationLayer .textWidgetAnnotation textarea[disabled],
.annotationLayer .choiceWidgetAnnotation select[disabled],
.annotationLayer .buttonWidgetAnnotation.checkBox input[disabled],
.annotationLayer .buttonWidgetAnnotation.radioButton input[disabled] {
  background: none;
  border: 1px solid transparent;
  cursor: not-allowed;
}

.annotationLayer .textWidgetAnnotation input:hover,
.annotationLayer .textWidgetAnnotation textarea:hover,
.annotationLayer .choiceWidgetAnnotation select:hover,
.annotationLayer .buttonWidgetAnnotation.checkBox input:hover,
.annotationLayer .buttonWidgetAnnotation.radioButton input:hover {
  border: 1px solid rgba(0, 0, 0, 1);
}

.annotationLayer .textWidgetAnnotation input:focus,
.annotationLayer .textWidgetAnnotation textarea:focus,
.annotationLayer .choiceWidgetAnnotation select:focus {
  background: none;
  border: 1px solid transparent;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before,
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after,
.annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before {
  background-color: rgba(0, 0, 0, 1);
  content: '';
  display: block;
  position: absolute;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before,
.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after {
  height: 80%;
  left: 45%;
  width: 1px;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:before {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.annotationLayer .buttonWidgetAnnotation.checkBox input:checked:after {
  transform: rotate(-45deg);
}

.annotationLayer .buttonWidgetAnnotation.radioButton input:checked:before {
  border-radius: 50%;
  height: 50%;
  left: 30%;
  top: 20%;
  width: 50%;
}

.annotationLayer .textWidgetAnnotation input.comb {
  font-family: monospace;
  padding-left: 2px;
  padding-right: 0;
}

.annotationLayer .textWidgetAnnotation input.comb:focus {
  /*
   * Letter spacing is placed on the right side of each character. Hence, the
   * letter spacing of the last character may be placed outside the visible
   * area, causing horizontal scrolling. We avoid this by extending the width
   * when the element has focus and revert this when it loses focus.
   */
  width: 115%;
}

.annotationLayer .buttonWidgetAnnotation.checkBox input,
.annotationLayer .buttonWidgetAnnotation.radioButton input {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0;
}

.annotationLayer .popupWrapper {
  position: absolute;
  width: 20em;
}

.annotationLayer .popup {
  position: absolute;
  z-index: 200;
  max-width: 20em;
  background-color: rgba(255, 255, 153, 1);
  box-shadow: 0px 2px 5px rgba(136, 136, 136, 1);
  border-radius: 2px;
  padding: 6px;
  margin-left: 5px;
  cursor: pointer;
  font: message-box;
  font-size: 9px;
  word-wrap: break-word;
}

.annotationLayer .popup > * {
  font-size: 9px;
}

.annotationLayer .popup h1 {
  display: inline-block;
}

.annotationLayer .popup span {
  display: inline-block;
  margin-left: 5px;
}

.annotationLayer .popup p {
  border-top: 1px solid rgba(51, 51, 51, 1);
  margin-top: 2px;
  padding-top: 2px;
}

.annotationLayer .highlightAnnotation,
.annotationLayer .underlineAnnotation,
.annotationLayer .squigglyAnnotation,
.annotationLayer .strikeoutAnnotation,
.annotationLayer .freeTextAnnotation,
.annotationLayer .lineAnnotation svg line,
.annotationLayer .squareAnnotation svg rect,
.annotationLayer .circleAnnotation svg ellipse,
.annotationLayer .polylineAnnotation svg polyline,
.annotationLayer .polygonAnnotation svg polygon,
.annotationLayer .caretAnnotation,
.annotationLayer .inkAnnotation svg polyline,
.annotationLayer .stampAnnotation,
.annotationLayer .fileAttachmentAnnotation {
  cursor: pointer;
}

.pdfViewer .canvasWrapper {
  overflow: hidden;
}

.pdfViewer .page {
  direction: ltr;
  width: 816px;
  height: 1056px;
  margin: 1px auto -8px auto;
  position: relative;
  overflow: visible;
  /*margin: 0 auto !important;*/
  margin: 10px !important;
}

.pdfViewer.removePageBorders .page {
  margin: 0px auto 10px auto;
  border: none;
}

.pdfViewer.singlePageView {
  display: inline-block;
}

.pdfViewer.singlePageView .page {
  margin: 0;
  border: none;
}

.pdfViewer.scrollHorizontal, .pdfViewer.scrollWrapped, .spread {
  margin-left: 3.5px;
  margin-right: 3.5px;
  text-align: center;
}

.pdfViewer.scrollHorizontal, .spread {
  white-space: nowrap;
}

.pdfViewer.removePageBorders,
.pdfViewer.scrollHorizontal .spread,
.pdfViewer.scrollWrapped .spread {
  margin-left: 0;
  margin-right: 0;
}

.spread .page,
.pdfViewer.scrollHorizontal .page,
.pdfViewer.scrollWrapped .page,
.pdfViewer.scrollHorizontal .spread,
.pdfViewer.scrollWrapped .spread {
  display: inline-block;
  vertical-align: middle;
}

.spread .page,
.pdfViewer.scrollHorizontal .page,
.pdfViewer.scrollWrapped .page {
  margin-left: -3.5px;
  margin-right: -3.5px;
}

.pdfViewer.removePageBorders .spread .page,
.pdfViewer.removePageBorders.scrollHorizontal .page,
.pdfViewer.removePageBorders.scrollWrapped .page {
  margin-left: 5px;
  margin-right: 5px;
}

.pdfViewer .page canvas {
  margin: 0;
  display: block;
}

.pdfViewer .page canvas[hidden] {
  display: none;
}

.pdfViewer .page .loadingIcon {
  position: absolute;
  display: block;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: url('/assets/pdfreader/loading-icon.gif') center no-repeat;
}

.pdfPresentationMode .pdfViewer {
  margin-left: 0;
  margin-right: 0;
}

.pdfPresentationMode .pdfViewer .page,
.pdfPresentationMode .pdfViewer .spread {
  display: block;
}

.pdfPresentationMode .pdfViewer .page,
.pdfPresentationMode .pdfViewer.removePageBorders .page {
  margin-left: auto;
  margin-right: auto;
}

.pdfPresentationMode:-ms-fullscreen .pdfViewer .page {
  margin-bottom: 100% !important;
}

.pdfPresentationMode:-webkit-full-screen .pdfViewer .page {
  margin-bottom: 100%;
  border: 0;
}

.pdfPresentationMode:-moz-full-screen .pdfViewer .page {
  margin-bottom: 100%;
  border: 0;
}

.pdfPresentationMode:fullscreen .pdfViewer .page {
  margin-bottom: 100%;
  border: 0;
}

:root {
  --sidebar-width: 200px;
  --sidebar-transition-duration: 200ms;
  --sidebar-transition-timing-function: ease;
}

* {
  padding: 0;
  margin: 0;
}

html {
  height: 100%;
  width: 100%;
}

body {
  height: 100%;
  width: 100%;
}

/**/

.hidden {
  display: none !important;
}
[hidden] {
  display: none !important;
}

.pdfViewer.enablePermissions .textLayer > span {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  cursor: not-allowed;
}

#viewerContainer.pdfPresentationMode:-ms-fullscreen {
  top: 0px !important;
  overflow: hidden !important;
}

#viewerContainer.pdfPresentationMode:-ms-fullscreen::-ms-backdrop {
  background-color: rgba(0, 0, 0, 1);
}

#viewerContainer.pdfPresentationMode:-webkit-full-screen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -webkit-user-select: none;
  user-select: none;
}

#viewerContainer.pdfPresentationMode:-moz-full-screen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -moz-user-select: none;
  user-select: none;
}

#viewerContainer.pdfPresentationMode:-ms-fullscreen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -ms-user-select: none;
  user-select: none;
}

#viewerContainer.pdfPresentationMode:fullscreen {
  top: 0px;
  border-top: 2px solid rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 1);
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.pdfPresentationMode:-webkit-full-screen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:-moz-full-screen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:-ms-fullscreen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:fullscreen a:not(.internalLink) {
  display: none;
}

.pdfPresentationMode:-webkit-full-screen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode:-moz-full-screen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode:-ms-fullscreen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode:fullscreen .textLayer > span {
  cursor: none;
}

.pdfPresentationMode.pdfPresentationModeControls > *,
.pdfPresentationMode.pdfPresentationModeControls .textLayer > span {
  cursor: default;
}

#outerContainer {
  width: 100%;
  height: 100%;
  position: relative;
}

#sidebarContainer {
  position: absolute;
  top: 32px;
  bottom: 0;
  width: 200px;
  width: var(--sidebar-width);
  visibility: hidden;
  z-index: 100;
  border-top: 1px solid rgba(51, 51, 51, 1);
  transition-duration: 200ms;
  transition-duration: var(--sidebar-transition-duration);
  transition-timing-function: ease;
  transition-timing-function: var(--sidebar-transition-timing-function);
}
html[dir='ltr'] #sidebarContainer {
  transition-property: left;
  left: -200px;
  left: calc(0px - var(--sidebar-width));
}
html[dir='rtl'] #sidebarContainer {
  transition-property: right;
  right: -200px;
  right: calc(0px - var(--sidebar-width));
}

.loadingInProgress #sidebarContainer {
  top: 36px;
}

#outerContainer.sidebarResizing #sidebarContainer {
  /* Improve responsiveness and avoid visual glitches when the sidebar is resized. */
  transition-duration: 0s;
  /* Prevent e.g. the thumbnails being selected when the sidebar is resized. */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#outerContainer.sidebarMoving #sidebarContainer,
#outerContainer.sidebarOpen #sidebarContainer {
  visibility: visible;
}
html[dir='ltr'] #outerContainer.sidebarOpen #sidebarContainer {
  left: 0px;
}
html[dir='rtl'] #outerContainer.sidebarOpen #sidebarContainer {
  right: 0px;
}

#mainContainer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  min-width: 320px;
}

#sidebarContent {
  top: 32px;
  bottom: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}
html[dir='ltr'] #sidebarContent {
  left: 0;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
}
html[dir='rtl'] #sidebarContent {
  right: 0;
  box-shadow: inset 1px 0 0 rgba(0, 0, 0, 0.25);
}

#viewerContainer {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  top: 0px;
  right: 0;
  bottom: 0;
  left: 0;
  outline: none;
}
#viewerContainer:not(.pdfPresentationMode) {
  transition-duration: 200ms;
  transition-duration: var(--sidebar-transition-duration);
  transition-timing-function: ease;
  transition-timing-function: var(--sidebar-transition-timing-function);
}
html[dir='ltr'] #viewerContainer {
  box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.05);
}
html[dir='rtl'] #viewerContainer {
  box-shadow: inset -1px 0 0 rgba(255, 255, 255, 0.05);
}

#outerContainer.sidebarResizing #viewerContainer {
  /* Improve responsiveness and avoid visual glitches when the sidebar is resized. */
  transition-duration: 0s;
}

html[dir='ltr'] #outerContainer.sidebarOpen #viewerContainer:not(.pdfPresentationMode) {
  transition-property: left;
  left: 200px;
  left: var(--sidebar-width);
}
html[dir='rtl'] #outerContainer.sidebarOpen #viewerContainer:not(.pdfPresentationMode) {
  -webkit-transition-property: right;
  transition-property: right;
  right: 200px;
  right: var(--sidebar-width);
}

.toolbar {
  position: relative;
  left: 0;
  right: 0;
  z-index: 9999;
  cursor: default;
  transition: all 00.5s ease-in;
}

#toolbarContainer {
  width: 100%;
}

#toolbarSidebar {
  width: 100%;
  height: 32px;
  background-color: rgba(66, 66, 66, 1); /* fallback */
  background-image: linear-gradient(rgba(77, 77, 77, 0.99), rgba(64, 64, 64, 0.95));
}
html[dir='ltr'] #toolbarSidebar {
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25),
  inset 0 -1px 0 rgba(255, 255, 255, 0.05),
  0 1px 0 rgba(0, 0, 0, 0.15),
  0 0 1px rgba(0, 0, 0, 0.1);
}
html[dir='rtl'] #toolbarSidebar {
  box-shadow: inset 1px 0 0 rgba(0, 0, 0, 0.25),
  inset 0 1px 0 rgba(255, 255, 255, 0.05),
  0 1px 0 rgba(0, 0, 0, 0.15),
  0 0 1px rgba(0, 0, 0, 0.1);
}

#sidebarResizer {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 6px;
  z-index: 200;
  cursor: ew-resize;
}
html[dir='ltr'] #sidebarResizer {
  right: -6px;
}
html[dir='rtl'] #sidebarResizer {
  left: -6px;
}

#toolbarContainer, .findbar, .secondaryToolbar {
  position: relative;
  height: 70px;
  background: #fff !important;
}
html[dir='ltr'] #toolbarContainer, .findbar, .secondaryToolbar {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.15),
  inset 0 -1px 0 rgba(255, 255, 255, 0.05),
  0 1px 0 rgba(0, 0, 0, 0.15),
  0 1px 1px rgba(0, 0, 0, 0.1);
}
html[dir='rtl'] #toolbarContainer, .findbar, .secondaryToolbar {
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.15),
  inset 0 -1px 0 rgba(255, 255, 255, 0.05),
  0 1px 0 rgba(0, 0, 0, 0.15),
  0 1px 1px rgba(0, 0, 0, 0.1);
}

#toolbarViewer {
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#loadingBar {
  position: relative;
  width: 100%;
  height: 4px;
  background-color: rgba(51, 51, 51, 1);
  border-bottom: 1px solid rgba(51, 51, 51, 1);
}

#loadingBar .progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: rgba(221, 221, 221, 1);
  overflow: hidden;
  transition: width 200ms;
}

@-webkit-keyframes progressIndeterminate {
  0% { left: -142px; }
  100% { left: 0; }
}

@keyframes progressIndeterminate {
  0% { left: -142px; }
  100% { left: 0; }
}

#loadingBar .progress.indeterminate {
  background-color: rgba(153, 153, 153, 1);
  transition: none;
}

#loadingBar .progress.indeterminate .glimmer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: calc(100% + 150px);
  background: repeating-linear-gradient(135deg,
  rgba(187, 187, 187, 1) 0, rgba(153, 153, 153, 1) 5px,
  rgba(153, 153, 153, 1) 45px, rgba(221, 221, 221, 1) 55px,
  rgba(221, 221, 221, 1) 95px, rgba(187, 187, 187, 1) 100px);
  -webkit-animation: progressIndeterminate 950ms linear infinite;
  animation: progressIndeterminate 950ms linear infinite;
}

.findbar, .secondaryToolbar {
  top: 60px;
  position: absolute;
  z-index: 10000;
  height: auto;
  min-width: 16px;
  padding: 0px 6px 0px 6px;
  margin: 4px 2px 4px 2px;
  color: rgba(217, 217, 217, 1);
  font-size: 12px;
  line-height: 14px;
  text-align: left;
  cursor: default;
}

.findbar {
  min-width: 300px;
}
.findbar > div {
  height: 32px;
}
.findbar.wrapContainers > div {
  clear: both;
}
.findbar.wrapContainers > div#findbarMessageContainer {
  height: auto;
}
html[dir='ltr'] .findbar {
  /*left: 68px;*/
  right: 60px;
}
html[dir='rtl'] .findbar {
  right: 68px;
  height: 55px;
  border-radius: 5px;
}

.findbar label {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#findInput {
  width: 200px;
}
#findInput::-webkit-input-placeholder {
  color: rgba(191, 191, 191, 1);
}
#findInput::-moz-placeholder {
  font-style: italic;
}
#findInput:-ms-input-placeholder {
  font-style: italic;
}
#findInput::-ms-input-placeholder {
  font-style: italic;
}
#findInput::placeholder {
  font-style: italic;
}
#findInput[data-status="pending"] {
  background-image: url(/assets/pdfreader/loading-small.png);
  background-repeat: no-repeat;
  background-position: right;
}
html[dir='rtl'] #findInput[data-status="pending"] {
  background-position: left;
}

.secondaryToolbar {
  padding: 6px;
  height: auto;
  z-index: 30000;
}
html[dir='ltr'] .secondaryToolbar {
  right: 4px;
}
html[dir='rtl'] .secondaryToolbar {
  left: 4px;
}

#secondaryToolbarButtonContainer {
  /*max-width: 200px;*/
  max-height: 400px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: -4px;
}

#secondaryToolbarButtonContainer.hiddenScrollModeButtons > .scrollModeButtons,
#secondaryToolbarButtonContainer.hiddenSpreadModeButtons > .spreadModeButtons {
  display: none !important;
}

.doorHanger{
  /*border: 1px solid rgba(0, 0, 0, 0.5);*/
  border-radius: 5px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  height: 55px;
}

.doorHangerRight {
  /*border: 1px solid rgba(0, 0, 0, 0.5);*/
  border-radius: 5px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
  height:auto;
}


.doorHanger:after, .doorHanger:before,
.doorHangerRight:after, .doorHangerRight:before {
  bottom: 100%;
  border: solid rgba(0, 0, 0, 0);
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
.doorHanger:after,
.doorHangerRight:after {
  border-bottom-color: rgba(82, 82, 82, 0.99);
  border-width: 8px;
}
.doorHanger:before,
.doorHangerRight:before {
  border-bottom-color: rgba(0, 0, 0, 0.5);
  border-width: 9px;
}

html[dir='ltr'] .doorHanger:after,
html[dir='rtl'] .doorHangerRight:after {
  left: 70%;
  /*margin-left: -8px;*/
}

html[dir='ltr'] .doorHanger:before,
html[dir='rtl'] .doorHangerRight:before {
  left: 70%;
  /*margin-left: -9px;*/
}

html[dir='rtl'] .doorHanger:after,
html[dir='ltr'] .doorHangerRight:after {
  right: 70%;
  /*margin-right: -8px;*/
}

html[dir='rtl'] .doorHanger:before,
html[dir='ltr'] .doorHangerRight:before {
  right: 13px;
  margin-right: -9px;
}

#findResultsCount {
  background-color: rgba(217, 217, 217, 1);
  color: rgba(82, 82, 82, 1);
  text-align: center;
  padding: 3px 4px;
}

#findMsg {
  font-style: italic;
  color: rgba(166, 183, 208, 1);
}
#findMsg:empty {
  display: none;
}

#findInput.notFound {
  background-color: rgba(255, 102, 102, 1);
}

#toolbarViewerMiddle {
  /*position: absolute;*/
  /*left: 50%;*/
  /*transform: translateX(-50%);*/
}

html[dir='ltr'] #toolbarViewerLeft,
html[dir='rtl'] #toolbarViewerRight {
  float: left;
}
html[dir='ltr'] #toolbarViewerRight,
html[dir='rtl'] #toolbarViewerLeft {
  float: right;
}
html[dir='ltr'] #toolbarViewerLeft > *,
html[dir='ltr'] #toolbarViewerMiddle > *,
html[dir='ltr'] #toolbarViewerRight > *,
html[dir='ltr'] .findbar * {
  position: relative;
  float: left;
}
html[dir='rtl'] #toolbarViewerLeft > *,
html[dir='rtl'] #toolbarViewerMiddle > *,
html[dir='rtl'] #toolbarViewerRight > *,
html[dir='rtl'] .findbar * {
  position: relative;
  float: right;
}

html[dir='ltr'] .splitToolbarButton {
  margin: 3px 2px 4px 0;
  display: inline-block;
}
html[dir='rtl'] .splitToolbarButton {
  margin: 3px 0 4px 2px;
  display: inline-block;
}
html[dir='ltr'] .splitToolbarButton > .toolbarButton {
  border-radius: 0;
  float: left;
}
html[dir='rtl'] .splitToolbarButton > .toolbarButton {
  border-radius: 0;
  float: right;
}

.toolbarButton,
.secondaryToolbarButton,
.overlayButton {
  border: 0 none;
  background: none;
  width: 35px;
  height: 25px;
}

.toolbarButton > span {
  display: inline-block;
  width: 0;
  height: 0;
  overflow: hidden;
}

.toolbarButton[disabled],
.secondaryToolbarButton[disabled],
.overlayButton[disabled] {
  opacity: .5;
}

.splitToolbarButton.toggled .toolbarButton {
  margin: 0;
}

.splitToolbarButton:hover > .toolbarButton,
.splitToolbarButton:focus > .toolbarButton,
.splitToolbarButton.toggled > .toolbarButton,
.toolbarButton.textButton {
  background-color: rgba(0, 0, 0, 0.12);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.35);
  border-color: rgba(0, 0, 0, 0.32) rgba(0, 0, 0, 0.38) rgba(0, 0, 0, 0.42);
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset,
  0 0 1px rgba(255, 255, 255, 0.15) inset,
  0 1px 0 rgba(255, 255, 255, 0.05);
}
.splitToolbarButton > .toolbarButton:hover,
.splitToolbarButton > .toolbarButton:focus,
.dropdownToolbarButton:hover,
.overlayButton:hover,
.overlayButton:focus,
.toolbarButton.textButton:hover,
.toolbarButton.textButton:focus {
  background-color: rgba(0,0,0,0.2);
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset,
  0 0 1px rgba(255, 255, 255, 0.15) inset,
  0 0 1px rgba(0, 0, 0, 0.05);
  z-index: 199;
}
.dropdownToolbarButton:hover {
  background-color: rgba(0, 0, 0, 0.26);
}
.splitToolbarButton > .toolbarButton {
  position: relative;
}
html[dir='ltr'] .splitToolbarButton > .toolbarButton:first-child,
html[dir='rtl'] .splitToolbarButton > .toolbarButton:last-child {
  position: relative;
  margin: 0;
  margin-right: -1px;
  border-top-left-radius: 2px;
  border-bottom-left-radius: 2px;
  border-right-color: rgba(0, 0, 0, 0);
}
html[dir='ltr'] .splitToolbarButton > .toolbarButton:last-child,
html[dir='rtl'] .splitToolbarButton > .toolbarButton:first-child {
  position: relative;
  margin: 0;
  margin-left: -1px;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-left-color: rgba(0, 0, 0, 0);
}
.splitToolbarButtonSeparator {
  padding: 8px 0;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08);
  display: inline-block;
  margin: 5px 0;
}
html[dir='ltr'] .splitToolbarButtonSeparator {
  float: left;
}
html[dir='rtl'] .splitToolbarButtonSeparator {
  float: right;
}
.splitToolbarButton:hover > .splitToolbarButtonSeparator,
.splitToolbarButton.toggled > .splitToolbarButtonSeparator {
  padding: 12px 0;
  margin: 1px 0;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.03);
}

.toolbarButton,
.dropdownToolbarButton,
.secondaryToolbarButton,
.overlayButton {
  min-width: 16px;
  padding: 2px 6px 0;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 2px;
  /*color: rgba(255, 255, 255, 0.8);*/
  font-size: 14px;
  line-height: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Opera does not support user-select, use <... unselectable="on"> instead */
  cursor: default;
}
.toolbarButton:before{
  font-size: 30px;
}
html[dir='ltr'] .toolbarButton,
html[dir='ltr'] .overlayButton,
html[dir='ltr'] .dropdownToolbarButton {
  margin: 3px 2px 4px 0;
}
html[dir='rtl'] .toolbarButton,
html[dir='rtl'] .overlayButton,
html[dir='rtl'] .dropdownToolbarButton {
  margin: 3px 0 4px 2px;
}

.toolbarButton:hover,
.toolbarButton:focus,
.dropdownToolbarButton,
.overlayButton,
.secondaryToolbarButton:hover,
.secondaryToolbarButton:focus {
  /*background-color: rgba(0, 0, 0, 0.12);*/
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  background-clip: padding-box;
  /*border: 1px solid rgba(0, 0, 0, 0.35);*/
  /*border-color: rgba(0, 0, 0, 0.32) rgba(0, 0, 0, 0.38) rgba(0, 0, 0, 0.42);*/
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset,
  0 0 1px rgba(255, 255, 255, 0.15) inset,
  0 1px 0 rgba(255, 255, 255, 0.05);
}

.toolbarButton:hover:active,
.overlayButton:hover:active,
.dropdownToolbarButton:hover:active,
.secondaryToolbarButton:hover:active {
  background-color: rgba(0, 0, 0, 0.2);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  border-color: rgba(0, 0, 0, 0.35) rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.45);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset,
  0 0 1px rgba(0, 0, 0, 0.2) inset,
  0 1px 0 rgba(255, 255, 255, 0.05);
}

.toolbarButton.toggled,
.splitToolbarButton.toggled > .toolbarButton.toggled,
.secondaryToolbarButton.toggled {
  background-color: rgba(0, 0, 0, 0.3);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  border-color: rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.45) rgba(0, 0, 0, 0.5);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) inset,
  0 0 1px rgba(0, 0, 0, 0.2) inset,
  0 1px 0 rgba(255, 255, 255, 0.05);
}

.toolbarButton.toggled:hover:active,
.splitToolbarButton.toggled > .toolbarButton.toggled:hover:active,
.secondaryToolbarButton.toggled:hover:active {
  background-color: rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.5) rgba(0, 0, 0, 0.55);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) inset,
  0 0 1px rgba(0, 0, 0, 0.3) inset,
  0 1px 0 rgba(255, 255, 255, 0.05);
}

.dropdownToolbarButton {
  width: 140px;
  padding: 0;
  overflow: hidden;
}
.dropdownToolbarButton::after {
  content: "\f0d7";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}
html[dir='ltr'] .dropdownToolbarButton::after {
  right: 8px;
}
html[dir='rtl'] .dropdownToolbarButton::after {
  left: 8px;
}

.dropdownToolbarButton > select {
  width: 162px;
  height: 23px;
  font-size: 12px;
  /*color: rgba(242, 242, 242, 1);*/
  margin: 0;
  padding: 3px 2px 2px;
  border: none;
  background: rgba(0,0,0,0); /* Opera does not support 'transparent' <select> background */
}

.dropdownToolbarButton > select > option {
  background: rgba(61, 61, 61, 1);
}

#customScaleOption {
  display: none;
}

#pageWidthOption {
  border-bottom: 1px rgba(255, 255, 255, 0.5) solid;
}

html[dir='ltr'] .splitToolbarButton:first-child,
html[dir='ltr'] .toolbarButton:first-child,
html[dir='rtl'] .splitToolbarButton:last-child,
html[dir='rtl'] .toolbarButton:last-child {
  margin-left: 4px;
}
html[dir='ltr'] .splitToolbarButton:last-child,
html[dir='ltr'] .toolbarButton:last-child,
html[dir='rtl'] .splitToolbarButton:first-child,
html[dir='rtl'] .toolbarButton:first-child {
  margin-right: 4px;
}

.toolbarButtonSpacer {
  width: 30px;
  display: inline-block;
  height: 1px;
}

html[dir='ltr'] #findPrevious {
  margin-left: 3px;
}
html[dir='ltr'] #findNext {
  margin-right: 3px;
}

html[dir='rtl'] #findPrevious {
  margin-right: 3px;
}
html[dir='rtl'] #findNext {
  margin-left: 3px;
}

.toolbarButton::before,
.secondaryToolbarButton::before {
  /* All matching images have a size of 16x16
   * All relevant containers have a size of 32x25 */
  position: absolute;
  display: inline-block;
  top: 4px;
  left: 7px;
}

html[dir="rtl"] .secondaryToolbarButton::before {
  right: 4px;
}

.toolbarButton#sidebarToggle::before {
  content: "\f03a";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}
html[dir='rtl'] .toolbarButton#sidebarToggle::before {
  transform: scaleX(-1);
}

.toolbarButton#secondaryToolbarToggle::before {
  content: "\f0c9";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}
html[dir='rtl'] .toolbarButton#secondaryToolbarToggle::before {
  transform: scaleX(-1);
}

.toolbarButton.findPrevious::before {
  content: "\f30a";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}
html[dir='rtl'] .toolbarButton.findPrevious::before {
  transform: scaleX(-1);
  filter: invert(1);
}

.toolbarButton.findNext::before {
  content: "\f30b";
  cursor: pointer;
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}
html[dir='rtl'] .toolbarButton.findNext::before {
  transform: scaleX(-1);
}

.toolbarButton.pageUp::before {
  content: "\f30a";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}
html[dir='rtl'] .toolbarButton.pageUp::before {
  transform: scaleX(-1);
}

.toolbarButton.pageDown::before {
  content: "\f30b";
  cursor: pointer;
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}
html[dir='rtl'] .toolbarButton.pageDown::before {
  transform: scaleX(-1);
}

.toolbarButton.zoomOut::before {
  content: "\f068";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}

.toolbarButton.zoomIn::before {
  content: "\2b";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}

.toolbarButton.presentationMode::before,
.secondaryToolbarButton.presentationMode::before {
  content: url(/assets/pdfreader/toolbarButton-presentationMode.png);
  filter: invert(1);
}

.toolbarButton.print::before,
.secondaryToolbarButton.print::before {
  content: url(/assets/pdfreader/toolbarButton-print.png);
  filter: invert(1);
}

.toolbarButton.openFile::before,
.secondaryToolbarButton.openFile::before {
  content: url(/assets/pdfreader/toolbarButton-openFile.png);
  filter: invert(1);
}

.toolbarButton.download::before,
.secondaryToolbarButton.download::before {
  content: url(/assets/pdfreader/toolbarButton-download.png);
  filter: invert(1);
}

.toolbarButton.bookmark,
.secondaryToolbarButton.bookmark {
  box-sizing: border-box;
  outline: none;
  padding-top: 4px;
  text-decoration: none;
}
.secondaryToolbarButton.bookmark {
  padding-top: 5px;
}

.bookmark[href='#'] {
  opacity: .5;
  pointer-events: none;
}

.toolbarButton.bookmark::before,
.secondaryToolbarButton.bookmark::before {
  content: url(/assets/pdfreader/toolbarButton-bookmark.png);
  filter: invert(1);
}

#viewThumbnail.toolbarButton::before {
  content: url(/assets/pdfreader/toolbarButton-viewThumbnail.png);
  filter: invert(1);
}

#viewOutline.toolbarButton::before {
  content: url(/assets/pdfreader/toolbarButton-viewOutline.png);
  filter: invert(1);
}
html[dir="rtl"] #viewOutline.toolbarButton::before {
  transform: scaleX(-1);
}

#viewAttachments.toolbarButton::before {
  content: url(/assets/pdfreader/toolbarButton-viewAttachments.png);
  filter: invert(1);
}

#viewFind.toolbarButton::before {
  content: "\f002";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}

.toolbarButton.pdfSidebarNotification::after {
  position: absolute;
  display: inline-block;
  top: 1px;
  /* Create a filled circle, with a diameter of 9 pixels, using only CSS: */
  content: '';
  background-color: rgba(112, 219, 85, 1);
  height: 9px;
  width: 9px;
  border-radius: 50%;
}
html[dir='ltr'] .toolbarButton.pdfSidebarNotification::after {
  left: 17px;
}
html[dir='rtl'] .toolbarButton.pdfSidebarNotification::after {
  right: 17px;
}

.secondaryToolbarButton {
  position: relative;
  margin: 0 0 20px 0;
  padding: 3px 0 1px 0;
  height: auto;
  min-height: 25px;
  width: auto;
  min-width: 100%;
  white-space: normal;
}
html[dir="ltr"] .secondaryToolbarButton {
  padding-left: 24px;
  text-align: left;
}
html[dir="rtl"] .secondaryToolbarButton {
  padding-right: 24px;
  text-align: right;
}
html[dir="ltr"] .secondaryToolbarButton.bookmark {
  padding-left: 27px;
}
html[dir="rtl"] .secondaryToolbarButton.bookmark {
  padding-right: 27px;
}

html[dir="ltr"] .secondaryToolbarButton > span {
  padding-right: 4px;
}
html[dir="rtl"] .secondaryToolbarButton > span {
  padding-left: 4px;
}

.secondaryToolbarButton.firstPage::before {
  content: "\f051";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
  transform: rotate(270deg);
}

.secondaryToolbarButton.lastPage::before {
  content: "\f051";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
  transform: rotate(90deg);
}

.secondaryToolbarButton.rotateCcw::before {
  content: "\f2ea";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.rotateCw::before {
  content: "\f2f9";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.selectTool::before {
  content: "\f245";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.handTool::before {
  content: "\f256";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.scrollVertical::before {
  content: "\f543";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.scrollHorizontal::before {
  content: "\f543";
  cursor: pointer;
  font-weight: 900;
  font-size: 16px;
  font-family: 'Font Awesome 5 Free';
  transform: rotate(90deg);
}

.secondaryToolbarButton.scrollWrapped::before {
  content: "\f7a5";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.spreadNone::before {
  content: "\f70e";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.spreadOdd::before {
  content: "\31";
  cursor: pointer;
  font-weight: 900;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.spreadEven::before {
  content: "\32";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.secondaryToolbarButton.documentProperties::before {
  content: "\f05a";
  cursor: pointer;
  font-weight: 900;
  font-size: 20px;
  font-family: 'Font Awesome 5 Free';
}

.verticalToolbarSeparator {
  display: block;
  padding: 8px 0;
  margin: 8px 4px;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08);
}
html[dir='ltr'] .verticalToolbarSeparator {
  margin-left: 2px;
}
html[dir='rtl'] .verticalToolbarSeparator {
  margin-right: 2px;
}

.horizontalToolbarSeparator {
  display: block;
  margin: 0 0 4px 0;
  height: 1px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08);
}

.toolbarField {
  padding: 3px 6px;
  margin: 4px 0 4px 0;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.09);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  background-clip: padding-box;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.32) rgba(0, 0, 0, 0.38) rgba(0, 0, 0, 0.42);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05) inset,
  0 1px 0 rgba(255, 255, 255, 0.05);
  /*color: rgba(242, 242, 242, 1);*/
  font-size: 12px;
  line-height: 14px;
  outline-style: none;
}

.toolbarField[type=checkbox] {
  display: inline-block;
  margin: 8px 0px;
}

.toolbarField.pageNumber {
  -moz-appearance: textfield; /* hides the spinner in moz */
  min-width: 16px;
  text-align: right;
  width: 40px;
}

.toolbarField.pageNumber.visiblePageIsLoading {
  background-image: url(/assets/pdfreader/loading-small.png);
  background-repeat: no-repeat;
  background-position: 1px;
}

.toolbarField.pageNumber::-webkit-inner-spin-button,
.toolbarField.pageNumber::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.toolbarField:hover {
  background-color: rgba(255, 255, 255, 0.11);
  border-color: rgba(0, 0, 0, 0.4) rgba(0, 0, 0, 0.43) rgba(0, 0, 0, 0.45);
}

.toolbarField:focus {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(77, 184, 255, 0.8) rgba(77, 184, 255, 0.85) rgba(77, 184, 255, 0.9);
}

.toolbarLabel {
  min-width: 16px;
  padding: 3px 6px 3px 2px;
  margin: 4px 2px 4px 0;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 2px;
  /*color: rgba(217, 217, 217, 1);*/
  font-size: 12px;
  line-height: 14px;
  text-align: left;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;
}

#thumbnailView {
  position: absolute;
  width: calc(100% - 60px);
  top: 0;
  bottom: 0;
  padding: 10px 30px 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

#thumbnailView > a:active,
#thumbnailView > a:focus {
  outline: 0;
}

.thumbnail {
  margin: 0 10px 5px 10px;
}
html[dir='ltr'] .thumbnail {
  float: left;
}
html[dir='rtl'] .thumbnail {
  float: right;
}

#thumbnailView > a:last-of-type > .thumbnail {
  margin-bottom: 10px;
}

#thumbnailView > a:last-of-type > .thumbnail:not([data-loaded]) {
  margin-bottom: 9px;
}

.thumbnail:not([data-loaded]) {
  border: 1px dashed rgba(255, 255, 255, 0.5);
  margin: -1px 9px 4px 9px;
}

.thumbnailImage {
  border: 1px solid rgba(0, 0, 0, 0);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5), 0 2px 8px rgba(0, 0, 0, 0.3);
  opacity: 0.8;
  z-index: 99;
  background-color: rgba(255, 255, 255, 1);
  background-clip: content-box;
}

.thumbnailSelectionRing {
  border-radius: 2px;
  padding: 7px;
}

a:focus > .thumbnail > .thumbnailSelectionRing > .thumbnailImage,
.thumbnail:hover > .thumbnailSelectionRing > .thumbnailImage {
  opacity: .9;
}

a:focus > .thumbnail > .thumbnailSelectionRing,
.thumbnail:hover > .thumbnailSelectionRing {
  background-color: rgba(255, 255, 255, 0.15);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  background-clip: padding-box;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset,
  0 0 1px rgba(255, 255, 255, 0.2) inset,
  0 0 1px rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.thumbnail.selected > .thumbnailSelectionRing > .thumbnailImage {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
  opacity: 1;
}

.thumbnail.selected > .thumbnailSelectionRing {
  background-color: rgba(255, 255, 255, 0.3);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  background-clip: padding-box;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset,
  0 0 1px rgba(255, 255, 255, 0.1) inset,
  0 0 1px rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255,1);
}

#outlineView,
#attachmentsView {
  position: absolute;
  width: calc(100% - 8px);
  top: 0;
  bottom: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#outlineView {
  padding: 4px 4px 0;
}
#attachmentsView {
  padding: 3px 4px 0;
}

html[dir='ltr'] .outlineWithDeepNesting > .outlineItem,
html[dir='ltr'] .outlineItem > .outlineItems {
  margin-left: 20px;
}

html[dir='rtl'] .outlineWithDeepNesting > .outlineItem,
html[dir='rtl'] .outlineItem > .outlineItems {
  margin-right: 20px;
}

.outlineItem > a,
.attachmentsItem > button {
  text-decoration: none;
  display: inline-block;
  min-width: 95%;
  min-width: calc(100% - 4px); /* Subtract the right padding (left, in RTL mode)
                                  of the container. */
  height: auto;
  margin-bottom: 1px;
  border-radius: 2px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  line-height: 15px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: normal;
}

.attachmentsItem > button {
  border: 0 none;
  background: none;
  cursor: pointer;
  width: 100%;
}

html[dir='ltr'] .outlineItem > a {
  padding: 2px 0 5px 4px;
}
html[dir='ltr'] .attachmentsItem > button {
  padding: 2px 0 3px 7px;
  text-align: left;
}

html[dir='rtl'] .outlineItem > a {
  padding: 2px 4px 5px 0;
}
html[dir='rtl'] .attachmentsItem > button {
  padding: 2px 7px 3px 0;
  text-align: right;
}

.outlineItemToggler {
  position: relative;
  height: 0;
  width: 0;
  color: rgba(255, 255, 255, 0.5);
}
.outlineItemToggler::before {
  content: url(/assets/pdfreader/treeitem-expanded.png);
  filter: invert(1);
  display: inline-block;
  position: absolute;
}
.outlineItemToggler.outlineItemsHidden::before {
  content: url(/assets/pdfreader/treeitem-collapsed.png);
  filter: invert(1);
}
html[dir='rtl'] .outlineItemToggler.outlineItemsHidden::before {
  transform: scaleX(-1);
}
.outlineItemToggler.outlineItemsHidden ~ .outlineItems {
  display: none;
}
html[dir='ltr'] .outlineItemToggler {
  float: left;
}
html[dir='rtl'] .outlineItemToggler {
  float: right;
}
html[dir='ltr'] .outlineItemToggler::before {
  right: 4px;
}
html[dir='rtl'] .outlineItemToggler::before {
  left: 4px;
}

.outlineItemToggler:hover,
.outlineItemToggler:hover + a,
.outlineItemToggler:hover ~ .outlineItems,
.outlineItem > a:hover,
.attachmentsItem > button:hover {
  background-color: rgba(255, 255, 255, 0.02);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  background-clip: padding-box;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset,
  0 0 1px rgba(255, 255, 255, 0.2) inset,
  0 0 1px rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  color: rgba(255, 255, 255, 0.9);
}

.outlineItem.selected {
  background-color: rgba(255, 255, 255, 0.08);
  background-image: linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0));
  background-clip: padding-box;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.05) inset,
  0 0 1px rgba(255, 255, 255, 0.1) inset,
  0 0 1px rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 1);
}

.noResults {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-style: italic;
  cursor: default;
}

/* TODO: file FF bug to support ::-moz-selection:window-inactive
   so we can override the opaque grey background when the window is inactive;
   see https://bugzilla.mozilla.org/show_bug.cgi?id=706209 */
::-moz-selection {
  background: rgba(0, 0, 255, 0.3);
}
::selection {
  background: rgba(0, 0, 255, 0.3);
}

#errorWrapper {
  background: none repeat scroll 0 0 rgba(255, 85, 85, 1);
  color: rgba(255, 255, 255, 1);
  left: 0;
  position: absolute;
  right: 0;
  z-index: 1000;
  padding: 3px;
  font-size: 0.8em;
}
.loadingInProgress #errorWrapper {
  top: 37px;
}

#errorMessageLeft {
  float: left;
}

#errorMessageRight {
  float: right;
}

#errorMoreInfo {
  background-color: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  padding: 3px;
  margin: 3px;
  width: 98%;
}

.overlayButton {
  width: auto;
  margin: 3px 4px 2px 4px !important;
  padding: 2px 6px 3px 6px;
}

#overlayContainer {
  display: table;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  z-index: 40000;
}
#overlayContainer > * {
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

#overlayContainer > .container {
  display: table-cell;
  vertical-align: middle;
  text-align: center;
}

#overlayContainer > .container > .dialog {
  display: inline-block;
  padding: 15px;
  border-spacing: 4px;
  color: rgba(217, 217, 217, 1);
  font-size: 12px;
  line-height: 14px;
  background-color: rgba(71, 71, 71, 1); /* fallback */
  background-image:linear-gradient(rgba(82, 82, 82,0.99), rgba(69, 69, 69, 0.95));
  border: 1px solid rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.dialog > .row {
  display: table-row;
}

.dialog > .row > * {
  display: table-cell;
}

.dialog .toolbarField {
  margin: 5px 0;
}

.dialog .separator {
  display: block;
  margin: 4px 0 4px 0;
  height: 1px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08);
}

.dialog .buttonRow {
  text-align: center;
  vertical-align: middle;
}

.dialog :link {
  color: rgba(255, 255, 255, 1);
}

#passwordOverlay > .dialog {
  text-align: center;
}
#passwordOverlay .toolbarField {
  width: 200px;
}

#documentPropertiesOverlay > .dialog {
  text-align: left;
}
#documentPropertiesOverlay .row > * {
  min-width: 100px;
}
html[dir='ltr'] #documentPropertiesOverlay .row > * {
  text-align: left;
}
html[dir='rtl'] #documentPropertiesOverlay .row > * {
  text-align: right;
}
#documentPropertiesOverlay .row > span {
  width: 125px;
  word-wrap: break-word;
}
#documentPropertiesOverlay .row > p {
  max-width: 225px;
  word-wrap: break-word;
}
#documentPropertiesOverlay .buttonRow {
  margin-top: 10px;
}

.clearBoth {
  clear: both;
}

.fileInput {
  background: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
  margin-top: 5px;
  visibility: hidden;
  position: fixed;
  right: 0;
  top: 0;
}

#PDFBug {
  background: none repeat scroll 0 0 rgba(255, 255, 255, 1);
  border: 1px solid rgba(102, 102, 102, 1);
  position: fixed;
  top: 32px;
  right: 0;
  bottom: 0;
  font-size: 10px;
  padding: 0;
  width: 300px;
}
#PDFBug .controls {
  background: rgba(238, 238, 238, 1);
  border-bottom: 1px solid rgba(102, 102, 102, 1);
  padding: 3px;
}
#PDFBug .panels {
  bottom: 0;
  left: 0;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  right: 0;
  top: 27px;
}
#PDFBug .panels > div {
  padding: 5px;
}
#PDFBug button.active {
  font-weight: bold;
}
.debuggerShowText {
  background: none repeat scroll 0 0 rgba(255, 255, 0, 1);
  color: rgba(0, 0, 255, 1);
}
.debuggerHideText:hover {
  background: none repeat scroll 0 0 rgba(255, 255, 0, 1);
}
#PDFBug .stats {
  font-family: courier;
  font-size: 10px;
  white-space: pre;
}
#PDFBug .stats .title {
  font-weight: bold;
}
#PDFBug table {
  font-size: 10px;
}

#viewer.textLayer-visible .textLayer {
  opacity: 1.0;
}

#viewer.textLayer-visible .canvasWrapper {
  background-color: rgba(128, 255, 128, 1);
}

#viewer.textLayer-visible .canvasWrapper canvas {
  mix-blend-mode: screen;
}

#viewer.textLayer-visible .textLayer > span {
  background-color: rgba(255, 255, 0, 0.1);
  color: rgba(0, 0, 0, 1);
  border: solid 1px rgba(255, 0, 0, 0.5);
  box-sizing: border-box;
}

#viewer.textLayer-hover .textLayer > span:hover {
  background-color: rgba(255, 255, 255, 1);
  color: rgba(0, 0, 0, 1);
}

#viewer.textLayer-shadow .textLayer > span {
  background-color: rgba(255, 255, 255, 0.6);
  color: rgba(0, 0, 0, 1);
}

.grab-to-pan-grab {
  cursor: url("/assets/pdfreader/grab.cur"), move !important;
  cursor: -webkit-grab !important;
  cursor: grab !important;
}
.grab-to-pan-grab *:not(input):not(textarea):not(button):not(select):not(:link) {
  cursor: inherit !important;
}
.grab-to-pan-grab:active,
.grab-to-pan-grabbing {
  cursor: url("/assets/pdfreader/grabbing.cur"), move !important;
  cursor: -webkit-grabbing !important;
  cursor: grabbing !important;
  position: fixed;
  background: rgba(0, 0, 0, 0);
  display: block;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 50000; /* should be higher than anything else in PDF.js! */
}

@page {
  margin: 0;
}

#printContainer {
  display: none;
}

@media screen and (-webkit-min-device-pixel-ratio: 1.1), screen and (min-resolution: 1.1dppx) {
  /* Rules for Retina screens */
  .toolbarButton::before {
    transform: scale(0.5);
  }

  .secondaryToolbarButton::before {
    transform: scale(0.5);
  }

  html[dir='ltr'] .toolbarButton::before,
  html[dir='rtl'] .toolbarButton::before {
    left: -1px;
  }

  html[dir='rtl'] .secondaryToolbarButton::before {
    left: 186px;
  }

  .toolbarField.pageNumber.visiblePageIsLoading,
  #findInput[data-status="pending"] {
    background-image: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
    background-size: 16px 17px;
  }

  .dropdownToolbarButton::after {
    content: "\f0d7";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }
  html[dir='ltr'] .dropdownToolbarButton::after {
    position: relative;
    right: 12px;
  }
  html[dir='rtl'] .dropdownToolbarButton::after {
    left: 12px;
  }

  .toolbarButton#sidebarToggle::before {
    content: "\f03a";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }
  html[dir='rtl'] .toolbarButton#sidebarToggle::before {
    transform: scale(-0.5, 0.5);
  }

  .toolbarButton#secondaryToolbarToggle::before {
    content: "\f0c9";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }
  html[dir='rtl'] .toolbarButton#secondaryToolbarToggle::before {
    transform: scale(-0.5, 0.5);
  }

  .toolbarButton.findPrevious::before {
    content: "\f30a";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }
  html[dir='rtl'] .toolbarButton.findPrevious::before {
    transform: scale(-0.5, 0.5);
  }

  .toolbarButton.findNext::before {
    content: "\f30b";
    cursor: pointer;
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }
  html[dir='rtl'] .toolbarButton.findNext::before {
    transform: scale(-0.5, 0.5);
  }

  .toolbarButton.pageUp::before {
    content: "\f30a";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }
  html[dir='rtl'] .toolbarButton.pageUp::before {
    transform: scale(-0.5, 0.5);
  }

  .toolbarButton.pageDown::before {
    content: "\f30b";
    cursor: pointer;
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
  }
  html[dir='rtl'] .toolbarButton.pageDown::before {
    transform: scale(-0.5, 0.5);
  }

  .toolbarButton.zoomIn::before {
    content: "\2b";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }

  .toolbarButton.zoomOut::before {
    content: "\f068";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }

  .toolbarButton.presentationMode::before,
  .secondaryToolbarButton.presentationMode::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }

  .toolbarButton.print::before,
  .secondaryToolbarButton.print::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }

  .toolbarButton.openFile::before,
  .secondaryToolbarButton.openFile::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }

  .toolbarButton.download::before,
  .secondaryToolbarButton.download::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }

  .toolbarButton.bookmark::before,
  .secondaryToolbarButton.bookmark::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }

  #viewThumbnail.toolbarButton::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }

  #viewOutline.toolbarButton::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }
  html[dir="rtl"] #viewOutline.toolbarButton::before {
    transform: scale(-0.5, 0.5);
  }

  #viewAttachments.toolbarButton::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }

  #viewFind.toolbarButton::before {
    content: "\f002";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.firstPage::before {
    content: "\f051";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
    transform: rotate(270deg);
  }

  .secondaryToolbarButton.lastPage::before {
    content: "\f051";
    cursor: pointer;
    font-weight: 900;
    font-family: 'Font Awesome 5 Free';
    transform: rotate(90deg);
  }

  .secondaryToolbarButton.rotateCcw::before {
    content: "\f2ea";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.rotateCw::before {
    content: "\f2f9";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.selectTool::before {
    content: "\f245";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.handTool::before {
    content: "\f256";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.scrollVertical::before {
    content: "\f543";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.scrollHorizontal::before {
    content: "\f543";
    cursor: pointer;
    font-weight: 900;
    font-size: 16px;
    font-family: 'Font Awesome 5 Free';
    transform: rotate(90deg);
  }

  .secondaryToolbarButton.scrollWrapped::before {
    content: "\f7a5";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.spreadNone::before {
    content: "\f70e";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.spreadOdd::before {
    content: "\31";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.spreadEven::before {
    content: "\32";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .secondaryToolbarButton.documentProperties::before {
    content: "\f05a";
    cursor: pointer;
    font-weight: 900;
    font-size: 20px;
    font-family: 'Font Awesome 5 Free';
  }

  .outlineItemToggler::before {
    transform: scale(0.5);
    top: -1px;
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }
  .outlineItemToggler.outlineItemsHidden::before {
    content: url(/assets/pdfreader/<EMAIL>);
    filter: invert(1);
  }
  html[dir='rtl'] .outlineItemToggler.outlineItemsHidden::before {
    transform: scale(-0.5, 0.5);
  }
  html[dir='ltr'] .outlineItemToggler::before {
    right: 0;
  }
  html[dir='rtl'] .outlineItemToggler::before {
    left: 0;
  }
}

@media print {
  /* General rules for printing. */
  body {
  }

  /* Rules for browsers that don't support mozPrintCallback. */
  #sidebarContainer, #secondaryToolbar, .toolbar, #loadingBox, #errorWrapper, .textLayer {
    display: none;
  }
  #viewerContainer {
    overflow: visible;
  }

  #mainContainer, #viewerContainer, .page, .page canvas {
    position: static;
    padding: 0;
    margin: 0;
  }

  .page {
    float: left;
    display: none;
    border: none;
    box-shadow: none;
    background-clip: content-box;
    background-color: rgba(255, 255, 255, 1);
  }

  .page[data-loaded] {
    display: block;
  }

  .fileInput {
    display: none;
  }

  /* Rules for browsers that support PDF.js printing */
  body[data-pdfjsprinting] #outerContainer {
    display: none;
  }
  body[data-pdfjsprinting] #printContainer {
    display: block;
  }
  #printContainer {
    height: 100%;
  }
  /* wrapper around (scaled) print canvas elements */
  #printContainer > div {
    position: relative;
    top: 0;
    left: 0;
    width: 1px;
    height: 1px;
    overflow: visible;
    page-break-after: always;
    page-break-inside: avoid;
  }
  #printContainer canvas,
  #printContainer img {
    display: block;
  }
}

.visibleLargeView,
.visibleMediumView,
.visibleSmallView {
  display: none;
}

@media all and (max-width: 900px) {
  #toolbarViewerMiddle {
    display: table;
    margin: auto;
    left: auto;
    position: inherit;
    transform: none;
  }
}

@media all and (max-width: 840px) {
  #sidebarContent {
    background-color: rgba(0, 0, 0, 0.7);
  }

  html[dir='ltr'] #outerContainer.sidebarOpen #viewerContainer {
    left: 0px !important;
  }
  html[dir='rtl'] #outerContainer.sidebarOpen #viewerContainer {
    right: 0px !important;
  }

  #outerContainer .hiddenLargeView,
  #outerContainer .hiddenMediumView {
    display: inherit;
  }
  #outerContainer .visibleLargeView,
  #outerContainer .visibleMediumView {
    display: none;
  }
}

@media all and (max-width: 770px) {
  #outerContainer .hiddenLargeView {
    display: none;
  }
  #outerContainer .visibleLargeView {
    display: inherit;
  }
}

@media all and (max-width: 700px) {
  #outerContainer .hiddenMediumView {
    display: none;
  }
  #outerContainer .visibleMediumView {
    display: inherit;
  }
}

@media all and (max-width: 640px) {
  .hiddenSmallView, .hiddenSmallView * {
    display: none;
  }
  .visibleSmallView {
    display: inherit;
  }
  .toolbarButtonSpacer {
    width: 0;
  }
  html[dir='ltr'] .findbar {
    left: 38px;
  }
  html[dir='rtl'] .findbar {
    right: 38px;
  }
}
#scaleSelectContainer {
  display: flex;
  align-items: center;
}
@media all and (max-width: 535px) {
  #scaleSelectContainer {
    display: none;
  }
}
#toolbarViewerLeft {
  width: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
#toolbarViewerLeft .bookTitle p{
  font-size: 20px;
  font-family: 'DM Sans', sans-serif;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 15ch;
}

#toolbarViewerLeft .backButton button{
  width: 60px;
  background: transparent;
  border: none;
  font-size: 18px;
  margin-top: 5px;
  cursor: pointer;
}

#findbarInputContainer,
#findbarOptionsOneContainer,
#findbarOptionsTwoContainer{
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

#secondaryToolbar{
  width: 300px;
}

/* LAZY LOADING*/
.slider-anim{
  text-align: center;
  position: relative;
}

.caption{
  line-height: 100px;
  font-size: 60px;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  margin-left: -300px;
}
.text-box{
  display: inline-block;
  position: relative;
}

.text-box div{
  display: inline-block;
  position: absolute;
  top: -150px;
  transform: rotateX(-90deg);
  opacity: 0;
  animation-timing-function: ease;
}

.text-box div:nth-child(1){
  animation: rollDown 4s forwards;
  transition: all 0.3s ease;
}

.text-box div:nth-child(2){
  animation: rollDown2 4s forwards;
  transition: all 0.3s ease;
}

.text-box div:nth-child(3){
  animation: rollDown3 4s forwards;
  transition: all 0.3s ease;
}

.footerProgressSlider{
  width: 75%;
  height: 2px;
}
.highlightsAndNotesBtn{
  transform: scale(0.3);
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: #000 !important;
}
.highlightsAndNotesBtn::before {
  content: "\f31c";
  cursor: pointer;
  font-weight: 900;
  font-size: 60px;
  font-family: 'Font Awesome 5 Free';
}
.highlightsAndNotesBtn p{
  font-size: 3rem;
  margin-left: 10px;
}
.notesWrapper{
  height: 100vh;
  justify-content: center;
  align-items: center;
  position: relative;
  width: auto;
  margin: 0 auto;
  transition: all 0.4s ease;
  background: rgba(0,0,0,0.4);
}
.notesDisplay{
  background: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.15);
  border-radius: 10px;
  width:100%;
  height: 100%;
  padding: 10px;
}
.notesDisplay_header{
  display: flex;
  justify-content: space-between;
  padding: 12px;
  align-items: center;
}
.notesDisplay_body{
  display: flex;
  width: 50%;
  margin: 0 auto;
  overflow-x: hidden;
  height: 100%;
}
.notesList{
  padding: 10px;
  list-style: none;
}
.notesList li{
  font-size: 18px;
  margin-bottom: 8px;
  position: relative;
  min-height: 70px;
  max-height: max-content;
  padding: 5px 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  cursor: pointer;
  border-left: 2px solid;
}
.notesList li:hover{
  background: #f1f5f7;
  border-radius: 5px;
}
.notesList li a{
  text-decoration: none;
}
.notesList li p{
  font-size: 14px;
  margin-bottom: 10px;
  color: #848484;
  display: flex;
  align-items: center;
}
.notesList li p span{
  margin: 0 5px;
}
.close{
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
}

.openPopupWindow{
  animation: popupOpenWindow 0.4s ease;
  transition: all 0.4s ease;
  display: flex;
}

.closePopupWindow{
  animation: popupCloseWindow 0.4s ease;
  transition: all 0.4s ease;
}

.displayOpenClose{
  display: none;
}
.customMenuIcon{
  color: #000 !important;
}
.backBtn{
  color: #000 !important;
}
@keyframes popupOpenWindow {
  0%{
    transform: scale(0.2);
    opacity: 0;
  }
  100%{
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes popupCloseWindow {
  0%{
    transform: scale(1);
    opacity: 1;
  }
  100%{
    transform: scale(0);
    opacity: 0;
  }
}

@keyframes rollDown {
  0%{
    top: -150px;
    transform: rotateX(-90deg);
  }
  11%{
    top: -74px;
    transform: rotateX(0deg);
    opacity: 1;
  }
  22%{
    top: -74px;
    transform: rotateX(0deg);
    opacity: 1;
  }
  33%{
    top: 50px;
    transform: rotateX(30deg);
    opacity: 0;
  }
}

@keyframes rollDown2 {
  33%{
    top: -150px;
    transform: rotateX(-90deg);
  }
  44%{
    top: -74px;
    transform: rotateX(0deg);
    opacity: 1;
  }
  55%{
    top: -74px;
    transform: rotateX(0deg);
    opacity: 1;
  }
  66%{
    top: 50px;
    transform: rotateX(30deg);
    opacity: 0;
  }
}
@keyframes rollDown3 {
  66%{
    top: -150px;
    transform: rotateX(-90deg);
  }
  77%{
    top: -74px;
    transform: rotateX(0deg);
    opacity: 1;
  }
  88%{
    top: -74px;
    transform: rotateX(0deg);
    opacity: 1;
  }
  99%{
    top: 50px;
    transform: rotateX(30deg);
    opacity: 0;
  }
}

.toolbarViewerLeftWrapper{
  display: flex;
  align-items: center;
}
.headerOptionsWrapper{
  width: 100%;
}
.customMenuIcon{
  display: none;
  background: transparent;
  border: none;
  font-size: 1.2rem;
  width: 50px;
}
.headerOptionsWrapperAnimation{
  display: flex;
  transition: all 0.4s ease;
  animation: menuSlideOpen 0.3s forwards;
}
.headerOptionsWrapperClose{
  transition: all 0.4s ease;
  animation: menuSlideClose 0.3s forwards;
  display: none;
}

.annotator-wrapper{
  padding: 0 !important;
}
@media all and (max-width: 770px) {
  #toolbarViewerLeft .bookTitle{
    margin-left: 10px;
  }
  #toolbarContainer{
    height: auto;
  }
  #toolbarViewer{
    flex-direction: column;
  }
  #viewerContainer{
    top: 0px;
    position: absolute;
  }
  #toolbarViewerLeft .backButton button{
    width: auto;
  }
  #toolbarViewerLeft{
    display: flex;
    width: 100%;
    justify-content: space-between;
  }
  #toolbarViewerLeft .bookTitle p{
    width: 15ch !important;
  }
  .highlightsAndNotes{
    width: 30px;
    position: relative;
    height: 40px;
  }
  .highlightsAndNotesBtn{
    width: 100%;
  }
  .toolbarViewerLeftWrapper{
    margin-top: 3px;
    width: 100%;
    justify-content: flex-end;
  }
  .highlightsAndNotes{
    display: flex;
    align-items: center;
  }
  .customMenuIcon{
    display: block;
    width: 30px;
  }
  .findbar, .secondaryToolbar{
    top: 120px;
  }
  .pdfReaderFooter {
    width: 100% !important;
    position: absolute !important;
    bottom: 0;
    top: unset !important;
  }
  .caption{
    margin-left: -150px;
  }
  #thumbnailView{
    top: 100px;
  }
  .backButton{
    margin-left: 10px;
  }
  #outerContainer{
    position: fixed;
  }
  .doorHanger{
    height: auto !important;
  }
  .rowOne{
    margin-bottom: 10px;
  }
  #toolbarViewerLeft{
    padding-bottom: 10px;
  }
}

@media only screen and (max-width: 770px) and (pointer: coarse) {
  /* You can further adjust the max-width value to target specific iOS devices */
  .pdfReaderFooter {
    bottom: 0 !important;
  }
}
.sloganTexts{
  transition: all 0.3s ease-in-out;
  font-family: "poppins", sans-serif;
}

.notesList{
  overflow-y: scroll;
  width: 100%;
  overflow-x: hidden;
  height: 80%;
}
.thumbnail.selected{
  background-color:#ffe086 !important;
}

.nextResourceBtn{
  margin-left: auto;
}
.nextResourceBtn{
  background: transparent;
  border: 1px solid #6677;
  width: 150px;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  margin-right: 15px;
}
.nextResourceBtn:hover{
  background: #ffe086;
}

.hLogin{
  background: orange;
  width: 100px;
  border: none;
  padding: 5px 10px;
  border-radius: 100px;
  color: #fff;
  margin-top: 10px;
  cursor: pointer;
}
.downloadChapterButton{
  margin-top: 5px;
  margin-left: 10px;
  width: 30px;
  padding: 5px;
  background: #1F419B !important;
  color: #fff;
  border: none;
  border-radius: 2px;
  cursor: pointer;
}
#chapterId{
  width: 175px;
  padding: 10px;
  border: 1px solid rgba(0,0,0,0.3);
  border-radius: 4px;
}
.rowOne{
  display: flex;
  align-items: center;
}
@media screen and (max-width: 1400px) {
  #toolbarViewerLeft .bookTitle p {
    width: 8ch;
  }
}
@media screen and (max-width: 1200px) {
  #chapterId {
    position: relative;
    z-index: 999999;
    margin-right: -65px;
  }
}
@keyframes menuSlideOpen {
  0%{
    transform: translateY(-30px);
    opacity: 0;
  }
  100%{
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes menuSlideClose {
  100%{
    transform: translateY(-30px);
    opacity: 0;
  }
}
#nextChapter{
  border: 1px solid rgba(0,0,0,0.2);
  width: auto;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}
#previousChapter{
  border: 1px solid rgba(0,0,0,0.2);
  width: auto;
  padding: 10px;
  border-radius: 4px;
  text-align: center;
}

#nextChapter a,
#previousChapter a{
  text-decoration: none;
  color: #000;
}

.snapTitleWrapQA,
.snapTitleWrapMCQ{
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  position: sticky;
  top: 0;
  margin-top: 14px;
  z-index: 9;
  background: #f8f9fa !important;
}
.languagedropdown{
  width: 100px;
  padding: 3px 5px;
  border: 1px solid rgba(0, 0, 0, 0.4);
  border-radius: 5px;
}

@media (max-width: 768px) {
  .snapTitleWrapQA,
  .snapTitleWrapMCQ{
    align-items: start;
    gap: 0px;
    flex-direction: column;
    margin-left: 0px;
  }
}

@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
 .rowOne{
   margin-top: 10px;
   width: 100%;
 }
  .rowTwo{
    width: 100%;
  }
  #chapterId {
    margin-right: 0 !important;
  }
  #toolbarViewerRight{
    width: 100%;
    display: flex;
    justify-content: space-evenly;
  }
  #nextChapter a,
  #previousChapter a{
    font-size: 14px;
  }
  #nextChapter{
    width: 70px;
    padding: 5px;
  }
  #previousChapter{
    width: 70px;
    padding: 5px;
  }
  .notesDisplay_body{
    width: 100%;
  }
}

.lottieLoader{
  width: 78px;
  height: 78px;
  border-radius: 50%;
  border: 7px solid;
  border-bottom-color: #FF3D00;
  margin: 0 auto;
  animation:spinRotation 1s linear infinite;
  transition: all 1s ease;
}

.book_snaps{
  margin: 20px 0;
  width: 80%;
  margin: 0 auto;
}

.mcq_snap,
.qa_snap,
.summary_snap{
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 30px;
  font-weight:300;
  padding: 10px;
  margin-bottom: 20px;
  margin-left: 10px !important;
  margin-right: 10px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px){
  .mcq_content,
  .qa_content,
  .snap_content{
    margin-left: 6px !important;
    margin-right: 6px !important;
  }
  .mcq_snap,
  .qa_snap,
  .summary_snap{
    margin-left: 6px;
    margin-right: 6px;
  }
  .snapTitle{
    font-size: 22px;
    margin-bottom: 12px;
  }
  .book_snaps{
    margin: 20px 0;
    width: 98%;
    margin: 0 auto;
  }

  .answerwrapItem,
  .expwrapItem,
  .optNo {
    overflow: auto;
    scrollbar-width: none; /* For Firefox */
  }

  .answerwrapItem::-webkit-scrollbar,
  .expwrapItem::-webkit-scrollbar,
  .optNo::-webkit-scrollbar {
    display: none; /* For Chrome, Safari, and Edge */
  }
}
.snap_content_div h1{
  margin-bottom: 18px;
  color: #ED6114;
}
.snapTitle{
  font-size: 24px;
  color: #ED6114;
}
.mcq_content,
.qa_content{
  margin-bottom: 24px;
  overflow: hidden;
}

.toggleBtn{
  display: flex;
  justify-content: center;
  padding: 5px 12px;
  cursor: pointer;
  border-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  align-items: center;
  margin: 3px auto;
  margin-left: auto;
  margin-right: 10px;
  background: transparent;
  color: blue;
  border: none;
  font-family: "Lora", serif;
}
.info_ribbon{
  background: #ECE5E5;
  margin: 30px 0;
  padding: 10px;
}
.ques{
  font-weight: 600;
}
.queWrap{
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.ansTxt{
  font-weight: 600;
}
.optNo{
  display: block;
  width: 70%;
  padding: 10px 15px;
  margin-bottom: 10px;
  text-align: left;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.button-group {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.button-group .btn {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px 16px;
  border-radius: 5px;
  font-size: 13px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.button-group .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.button-group .btn:hover::before {
  opacity: 1;
}

.button-group .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.button-group .btn svg {
  width: 20px;
  height: 20px;
}

.button-group .btn-primary {
  background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
}

.button-group .btn-primary:hover {
  background: linear-gradient(135deg, #FF4757 0%, #FF3346 100%);
}

.button-group .btn-secondary {
  background: linear-gradient(135deg, #ec4899 0%, #d946ef 100%);
}

.button-group .btn-secondary:hover {
  background: linear-gradient(135deg, #db2777 0%, #c026d3 100%);
}

.button-group .btn-tertiary {
  background: linear-gradient(135deg, #764BA2 0%, #667EEA 100%);
}

.button-group .btn-tertiary:hover {
  background: linear-gradient(135deg, #6a43a0 0%, #5a6eea 100%);
}

.ts_opt_cont {
  margin-bottom: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.ts_opt_link {
  text-decoration: none;
  font-weight: 600;
}

.ts_opt_separator {
  margin: 0 10px;
}

@media (hover: hover) {
  .button-group .btn:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}
@keyframes spinRotation {
  0%{
    transform: rotate(0deg);
}
  100%{
    transform: rotate(360deg);
  }
}