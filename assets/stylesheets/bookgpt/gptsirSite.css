* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: #f8f9fa;
  line-height: 1.6;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
/* Header */
.header {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}
.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
}
.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}
.nav-links {
  display: flex;
  list-style: none;
  gap: 30px;
  align-items: center;
}
.nav-links a {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s;
}
.nav-links a:hover {
  color: #4f46e5;
}
.store-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white !important;
  padding: 10px 20px;
  border-radius: 8px;
  transition: transform 0.2s;
}
.store-btn:hover {
  transform: translateY(-2px);
}
/* Hero Section */
.hero {
  background: linear-gradient(135deg, #4f46e5, #7c3aed, #06b6d4);
  color: white;
  padding: 60px 0;
  position: relative;
  overflow: hidden;
}
.hero-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 400px;
}
.ai-bot {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.bot-face {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}
.textbook {
  width: 140px;
  height: 100px;
  background: white;
  border-radius: 10px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}
.textbook::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 30px;
  right: 30px;
  height: 3px;
  background: #e5e7eb;
  border-radius: 2px;
}
.textbook::after {
  content: "";
  position: absolute;
  top: 35px;
  left: 30px;
  right: 30px;
  height: 3px;
  background: #e5e7eb;
  border-radius: 2px;
}
/* Banner Carousel Styles */
.banner-carousel-desktop,
.banner-carousel-mobile {
  width: 100%;
  max-width: 100%;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
}
.banner-carousel-desktop .carousel-inner,
.banner-carousel-mobile .carousel-inner {
  border-radius: 20px;
}
.banner-carousel-desktop .carousel-item img,
.banner-carousel-mobile .carousel-item img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 20px;
}
.banner-carousel-desktop .carousel-indicators,
.banner-carousel-mobile .carousel-indicators {
  bottom: 15px;
  margin-bottom: 0;
}
.banner-carousel-desktop .carousel-indicators li,
.banner-carousel-mobile .carousel-indicators li {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.8);
  margin: 0 4px;
}
.banner-carousel-desktop .carousel-indicators .active,
.banner-carousel-mobile .carousel-indicators .active {
  background-color: rgba(255, 255, 255, 0.9);
}
/* Desktop carousel - visible by default, hidden on mobile */
.banner-carousel-desktop {
  display: block;
}
/* Mobile carousel - hidden by default, shown on mobile */
.banner-carousel-mobile {
  display: none;
}
/* Fallback visual elements */
.fallback-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  width: 100%;
  min-height: 400px;
}
/* Loading styles */
.loading-icon {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}
.loading-icon.hidden {
  display: none;
}
.loader-wrapper {
  background: white;
  padding: 20px 40px;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}
.loader {
  color: #4f46e5;
  font-weight: 600;
  font-size: 18px;
}
/* Carousel Controls */
.banner-carousel-desktop .carousel-control-prev,
.banner-carousel-desktop .carousel-control-next,
.banner-carousel-mobile .carousel-control-prev,
.banner-carousel-mobile .carousel-control-next {
  width: 5%;
  color: rgba(255, 255, 255, 0.8);
  opacity: 0.7;
  transition: opacity 0.3s;
}
.banner-carousel-desktop .carousel-control-prev:hover,
.banner-carousel-desktop .carousel-control-next:hover,
.banner-carousel-mobile .carousel-control-prev:hover,
.banner-carousel-mobile .carousel-control-next:hover {
  opacity: 1;
}
.banner-carousel-desktop .carousel-control-prev-icon,
.banner-carousel-desktop .carousel-control-next-icon,
.banner-carousel-mobile .carousel-control-prev-icon,
.banner-carousel-mobile .carousel-control-next-icon {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  padding: 10px;
  backdrop-filter: blur(10px);
}
/* Smooth transitions */
.banner-carousel-desktop .carousel-item,
.banner-carousel-mobile .carousel-item {
  transition: transform 0.6s ease-in-out;
}
/* Auto-play carousel */
.banner-carousel-desktop[data-ride="carousel"],
.banner-carousel-mobile[data-ride="carousel"] {
  animation: none;
}
/* Benefits Section */
.benefits {
  padding: 40px 0 80px 0;
}
.benefits h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 50px;
}
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 60px;
}
.benefit-card {
  padding: 40px;
  border-radius: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s;
}
.benefit-card:hover {
  transform: translateY(-5px);
}
.benefit-card.doubt-solving {
  background: linear-gradient(135deg, #f59e0b, #ef4444, #8b5cf6);
}
.benefit-card.ai-tutor {
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
}
.benefit-card.step-by-step {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
}
.benefit-card.adaptive-practice {
  background: linear-gradient(135deg, #84cc16, #f59e0b);
}
.benefit-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}
.benefit-text h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}
/* Stats */
.stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
  margin-bottom: 60px;
  background: #f8f9fa;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
}
.stat {
  color: #1f2937;
}
.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: #4f46e5;
}
.stat-label {
  font-size: 18px;
  color: #6b7280;
  margin-top: 5px;
}
/* Categories Section */
.categories-section {
  padding: 40px 0 60px 0;
}
.categorySectionWrapper {
  display: flex;
  width: 100%;
  justify-content: flex-start;
}
.category_level {
  margin-bottom: 40px;
}
.category_level h4 {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 20px;
}
.category_level h4 a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}
.category_level h4 a:hover {
  color: #4f46e5;
  text-decoration: none;
}
.category_cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.category_card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 15px;
  padding: 25px 20px;
  text-decoration: none;
  color: #1f2937;
  transition: all 0.3s ease;
  display: block;
  min-height: 120px;
  position: relative;
  overflow: hidden;
  max-width: 250px;
}
.category_card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-color: #4f46e5;
  text-decoration: none;
  color: #1f2937;
}
.category_card-title {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 70px;
}
.category_card-title p {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  text-align: center;
  line-height: 1.3;
  color: inherit;
}
/* Add some gradient backgrounds for variety */
.category_card:nth-child(3n+1) {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-color: #cbd5e1;
}
.category_card:nth-child(3n+1):hover {
  background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
  border-color: #4f46e5;
}
.category_card:nth-child(3n+2) {
  background: linear-gradient(135deg, #fef7ff, #f3e8ff);
  border-color: #d8b4fe;
}
.category_card:nth-child(3n+2):hover {
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
  border-color: #4f46e5;
}
.category_card:nth-child(3n+3) {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-color: #7dd3fc;
}
.category_card:nth-child(3n+3):hover {
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
  border-color: #4f46e5;
}
/* Campus Section */
.campus-demo {
  display: flex;
  align-items: center;
  gap: 40px;
  background: linear-gradient(135deg, #ff8c00 0%, #ff6b35 25%, #e91e63 75%, #9c27b0 100%);
  padding: 40px;
  color: white;
}
.campus-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
}
.campus-features {
  list-style: none;
  padding: 0;
  margin: 0;
}
.campus-features li {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  line-height: 1.4;
}
.campus-features li::before {
  content: "•";
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  flex-shrink: 0;
}
/* Phone Mockup Styles */
.phone-mockup {
  width: 190px;
  height: 360px;
  background: #1a1a1a;
  border-radius: 25px;
  padding: 8px;
  position: relative;
  margin: 0 auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid #333;
}
.phone-screen {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 18px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.phone-header {
  background: #ffffff;
  padding: 8px 15px 5px;
  border-bottom: 1px solid #e9ecef;
}
.phone-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  color: #000;
}
.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}
.signal-bars span {
  width: 3px;
  background: #000;
  border-radius: 1px;
}
.signal-bars span:nth-child(1) {
  height: 4px;
}
.signal-bars span:nth-child(2) {
  height: 6px;
}
.signal-bars span:nth-child(3) {
  height: 8px;
}
.phone-content {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background: #f8f9fa;
}
.chat-message {
  display: flex;
  margin-bottom: 8px;
}
.user-message {
  justify-content: flex-end;
}
.ai-message {
  justify-content: flex-start;
}
.message-bubble {
  max-width: 80%;
  padding: 8px 12px;
  border-radius: 18px;
  font-size: 11px;
  line-height: 1.3;
  word-wrap: break-word;
}
.user-message .message-bubble {
  background: #007bff;
  color: white;
  border-bottom-right-radius: 4px;
}
.ai-message .message-bubble {
  background: #e9ecef;
  color: #333;
  border-bottom-left-radius: 4px;
}
/* Campus CTA Block */
.campus-cta {
  background: #f8f9fa;
  padding: 40px;
}
.campus-cta h2 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 1.2;
  color: #1a365d;
}
/* Consultation Button */
.consultation-btn {
  background: #2563eb;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
  text-decoration: none;
  display: inline-block;
  text-align: center;
}
.consultation-btn:hover {
  background: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
  color: white;
}
.contact-card {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 25px 35px;
  margin: 0 auto;
  max-width: 400px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
.contact-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.15);
}
.contact-icon {
  font-size: 32px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
  flex-shrink: 0;
}
.contact-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.contact-label {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}
.contact-number {
  font-size: 24px;
  font-weight: 700;
  color: white;
  text-decoration: none;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}
.contact-number:hover {
  color: #a855f7;
  text-decoration: none;
  transform: scale(1.05);
}
/* Medium screens - Tablet */
@media (max-width: 1024px) and (min-width: 769px) {
  .category_cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  .campus-section {
    gap: 50px;
  }
  .campus-demo {
    gap: 35px;
  }
  .phone-mockup {
    width: 160px;
    height: 320px;
  }
  .campus-cta h2 {
    font-size: 42px;
  }
}
/* Responsive Design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  .hero-content {
    min-height: 250px;
  }
  /* Show mobile carousel, hide desktop carousel */
  .banner-carousel-desktop {
    display: none;
  }
  .banner-carousel-mobile {
    display: block;
  }
  .banner-carousel-mobile .carousel-item img {
    height: 250px;
  }
  .fallback-visual {
    min-height: 250px;
  }
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  .categories-section {
    padding: 30px 0 40px 0;
  }
  .category_level h4 {
    font-size: 24px;
    margin-bottom: 15px;
  }
  .category_cards {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 0 15px;
  }
  .category_card {
    min-height: 100px;
    padding: 20px 15px;
  }
  .category_card-title p {
    font-size: 16px;
  }
  .campus-section {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  .campus-demo {
    flex-direction: column;
    gap: 25px;
    text-align: center;
  }
  .phone-mockup {
    width: 140px;
    height: 280px;
    margin: 0 auto;
  }
  .phone-content {
    padding: 10px;
    gap: 8px;
  }
  .message-bubble {
    font-size: 9px;
    padding: 6px 10px;
  }
  .phone-status-bar {
    font-size: 10px;
  }
  .campus-cta {
    padding: 30px 20px;
    margin: 0 10px;
  }
  .campus-cta h2 {
    font-size: 32px;
    text-align: center;
    margin-bottom: 25px;
  }
  .consultation-btn {
    font-size: 16px;
    padding: 14px 28px;
    width: 100%;
    max-width: 280px;
  }
  .campus-features {
    text-align: left;
  }
  .campus-features li {
    font-size: 16px;
    margin-bottom: 15px;
  }
  .stats {
    flex-direction: column;
    gap: 30px;
  }
}
/* Footer Styles */
.gptsir-footer {
  background: linear-gradient(135deg, #1f2937, #374151);
  color: white;
  padding: 60px 0 0;
  margin-top: 80px;
  position: relative;
  overflow: hidden;
}
.gptsir-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed, #06b6d4, #f59e0b);
}
.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1.5fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}
.footer-section {
  display: flex;
  flex-direction: column;
}
.footer-about {
  max-width: 300px;
}
.footer-logo-img {
  width: 120px;
  height: auto;
  margin-bottom: 20px;
  filter: brightness(0) invert(1);
}
.footer-description {
  font-size: 14px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 25px;
}
.footer-social {
  display: flex;
  gap: 15px;
}
.social-link {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
  color: white;
  text-decoration: none;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}
.footer-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 20px;
  color: white;
  position: relative;
  padding-bottom: 10px;
}
.footer-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #4f46e5, #7c3aed);
  border-radius: 1px;
}
.footer-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}
.footer-menu li {
  margin-bottom: 12px;
}
.footer-menu a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  display: inline-block;
}
.footer-menu a:hover {
  color: #a855f7;
  text-decoration: none;
  transform: translateX(5px);
}
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}
.contact-item i {
  color: #4f46e5;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}
.contact-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.contact-details a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}
.contact-details a:hover {
  color: #a855f7;
  text-decoration: none;
}
.app-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.app-link {
  display: inline-block;
  transition: transform 0.3s ease;
}
.app-link:hover {
  transform: scale(1.05);
}
.app-link img {
  width: 140px;
  height: auto;
  border-radius: 8px;
}
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 25px 12px;
  background: rgba(0, 0, 0, 0.2);
}
.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}
.copy-right-text-footer {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}
.copy-right-text-footer span {
  color: #a855f7;
  font-weight: 500;
}
.footer-policies {
  display: flex;
  align-items: center;
  gap: 15px;
}
.footer-policies a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}
.footer-policies a:hover {
  color: #a855f7;
  text-decoration: none;
}
.footer-policies .separator {
  color: rgba(255, 255, 255, 0.4);
}
/* Mobile Footer Navigation */
.mobile-footer-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 10px 0;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}
.mobile-footer-nav a {
  text-decoration: none;
  color: #6b7280;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 8px;
  transition: color 0.3s ease;
}
.mobile-footer-nav a:hover {
  color: #4f46e5;
  text-decoration: none;
}
.mobile-footer-nav a i {
  font-size: 20px;
}
.mobile-footer-nav a p {
  font-size: 12px;
  margin: 0;
  font-weight: 500;
}
/* Footer Responsive Styles */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 30px;
  }
  .footer-apps {
    grid-column: span 2;
  }
}
@media (max-width: 768px) {
  .gptsir-footer {
    padding: 40px 0 0;
    margin-top: 60px;
  }
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
  .footer-about {
    max-width: 100%;
  }
  .footer-logo-img {
    width: 100px;
    margin: 0 auto 20px;
    display: block;
  }
  .footer-social {
    justify-content: center;
  }
  .footer-title::after {
    left: 50%;
    transform: translateX(-50%);
  }
  .footer-menu {
    text-align: center;
  }
  .contact-info {
    align-items: center;
  }
  .contact-item {
    justify-content: center;
    text-align: left;
  }
  .app-links {
    align-items: center;
  }
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  .footer-policies {
    justify-content: center;
  }
  /* Add padding bottom for mobile nav */
  body {
    padding-bottom: 70px;
  }
}
@media (max-width: 480px) {
  .campus-section {
    gap: 25px;
    border-radius: 15px;
  }
  .campus-demo {
    gap: 20px;
  }
  .phone-mockup {
    width: 120px;
    height: 240px;
  }
  .phone-content {
    padding: 8px;
    gap: 6px;
  }
  .message-bubble {
    font-size: 8px;
    padding: 5px 8px;
  }
  .phone-status-bar {
    font-size: 9px;
    padding: 6px 12px 4px;
  }
  .campus-cta {
    padding: 25px 15px;
    margin: 0 5px;
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .campus-cta h2 {
    font-size: 28px;
    margin-bottom: 20px;
  }
  .consultation-btn {
    font-size: 14px;
    padding: 12px 24px;
    width: 100%;
    max-width: 250px;
  }
  .campus-features li {
    font-size: 14px;
    margin-bottom: 12px;
  }
  .campus-features li::before {
    font-size: 20px;
  }
  .gptsir-footer {
    padding: 30px 0 0;
  }
  .footer-content {
    gap: 30px;
  }
  .footer-title {
    font-size: 16px;
    margin-bottom: 15px;
  }
  .footer-description {
    font-size: 13px;
  }
  .footer-menu a {
    font-size: 13px;
  }
  .contact-details a {
    font-size: 13px;
  }
  .social-link {
    width: 35px;
    height: 35px;
  }
  .app-link img {
    width: 120px;
  }
  .footer-bottom {
    padding: 20px 0;
  }
  .copy-right-text-footer,
  .footer-policies a {
    font-size: 12px;
  }
}
