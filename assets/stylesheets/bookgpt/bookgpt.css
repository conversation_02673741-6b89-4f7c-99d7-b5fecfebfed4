::-webkit-scrollbar {
  width: 2px;
  height: 2px;
  /* Adjust height for horizontal scrollbar */
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: white;
  border-radius: 10px;
  border: 1px solid transparent;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #f0f0f0;
  /* Color when hovered */
}
/* Firefox specific scrollbar styling */
* {
  scrollbar-width: thin;
  /* Makes the scrollbar thin */
  scrollbar-color: white transparent;
}
/* Hover effect for Firefox */
*::-moz-scrollbar-thumb:hover {
  background-color: #f0f0f0;
}
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
*,
*::before,
*::after {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}
html,
body {
  width: 100vw;
  height: 100vh;
  margin: 0;
  border: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
p {
  margin-bottom: 0 !important;
}
button:focus {
  outline: none !important;
}
.loader {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background: #ffffff;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
.spinner {
  border: 10px solid #f3f3f3;
  /* Light grey */
  border-top: 10px solid #3498db;
  /* Blue */
  border-radius: 50%;
  width: 100px;
  height: 100px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.bookgpt {
  margin: 0 30px;
  font-family: "Lexend", sans-serif;
}
.bookgpt .header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
.bookgpt .header .chapterWrap {
  display: flex;
  align-items: center;
  gap: 12px;
}
.bookgpt .header .gptTokens {
  font-size: 14px;
  display: none;
  flex-direction: column;
  gap: 12px 0;
  justify-content: center;
  align-items: flex-end;
}
.bookgpt .header .gptTokens .rechargeWrap {
  display: flex;
  align-items: center;
  gap: 14px;
}
.bookgpt .header .gptTokens .countsWrap {
  display: flex;
  align-items: center;
  gap: 10px;
}
.bookgpt .header .gptTokens .fa-comments,
.bookgpt .header .gptTokens .fa-comments-dollar {
  color: #fbbd08;
}
@media (max-width: 768px) {
  .bookgpt .header .gptTokens {
    font-size: 12px !important;
  }
}
@media (max-width: 768px) {
  .bookgpt .header {
    margin-bottom: 0px;
    height: 45px;
    margin-left: 5px;
    margin-right: 5px;
  }
}
.bookgpt .header .backButton {
  font-size: 16px;
  background: transparent;
  border: none;
  cursor: pointer;
}
@media (max-width: 768px) {
  .bookgpt .header .backButton {
    margin-left: 10px;
    display: none;
  }
}
.bookgpt .header .custom-dropdown {
  position: relative;
  display: inline-block;
  width: 200px;
}
@media (max-width: 768px) {
  .bookgpt .header .custom-dropdown {
    width: 170px;
  }
}
.bookgpt .header #dropDownValue {
  font-size: 15px;
  width: 20ch;
  overflow: hidden;
  text-wrap: nowrap;
  text-overflow: ellipsis;
}
.bookgpt .header .dropdown-button {
  width: 100%;
  padding: 5px 10px;
  cursor: pointer;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 5px;
  height: 35px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background: #efecfe;
}
.bookgpt .header .dropdown-menu {
  display: none;
  position: absolute;
  background-color: white;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
  z-index: 1;
  width: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 300;
  max-height: 71vh;
  min-height: auto;
  overflow: scroll;
}
.bookgpt .header .dropdown-item {
  padding: 10px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 12px;
  white-space: normal;
}
.bookgpt .header .dropdown-item:active {
  color: #000 !important;
}
@media (max-width: 768px) {
  .bookgpt .header .dropdown-item {
    font-size: 13px;
  }
  .bookgpt .header .dropdown-item i {
    font-size: 11px;
  }
}
.bookgpt .header .dropdown-item:hover {
  background-color: #efecfe;
}
.bookgpt .header .show {
  display: block;
}
.bookgpt .header #dropdownArrow {
  transition: all 0.3s ease;
}
.bookgpt .header .rotateArrowUp {
  transform: rotate(180deg);
}
.bookgpt .header .rotateArrowDown {
  transform: rotate(0deg);
}
.bookgpt .sections {
  display: flex;
  height: 90vh;
  overflow: hidden;
}
@media (max-width: 768px) {
  .bookgpt .sections {
    height: calc(100vh - 60px);
    position: relative;
  }
}
.bookgpt .sections .pdfViewer {
  background: #ECE5E5;
  height: 100%;
  padding: 16px;
  padding-top: 5px !important;
}
@media (max-width: 768px) {
  .bookgpt .sections .pdfViewer {
    transition: all 0.5s ease;
  }
}
.bookgpt .sections .pdfViewer iframe {
  height: 100%;
  width: 100%;
  border: none;
  border-radius: 10px;
  background: #fff;
}
.bookgpt .sections .chatViewer {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  margin-top: 12px;
}
@media (max-width: 768px) {
  .bookgpt .sections .chatViewer {
    transition: all 0.5s ease;
    margin-top: 0px;
  }
}
.bookgpt .sections .chatViewer .gpt_loader {
  background: #fff;
  height: 100%;
  position: absolute;
  width: 100%;
  top: 0;
  z-index: 99998;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.bookgpt .sections .chatViewer .conversation {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: scroll;
  transition: all 0.3s ease;
}
@media (max-width: 768px) {
  .bookgpt .sections .chatViewer .conversation {
    padding-bottom: 2.2rem;
  }
}
.bookgpt .sections .chatViewer .conversation #prevChatBtn {
  margin-left: 12px;
  margin-top: 12px;
  text-decoration: none;
  font-size: 13px;
}
.bookgpt .sections .chatViewer .conversation .messages {
  margin: 0px 0 0 12px;
  display: flex;
  flex-direction: column;
  flex: 1;
}
.bookgpt .sections .chatViewer .conversation .messages .message {
  display: inline-block;
  padding: 14px;
  width: auto;
  margin: 8px 0;
  border-radius: 10px;
}
.bookgpt .sections .chatViewer .conversation .messages .message p {
  font-size: 14px !important;
  font-weight: 300 !important;
  line-height: 26px !important;
}
.bookgpt .sections .chatViewer .conversation .messages .message.userMessage {
  background: #efecfe;
  position: relative;
  float: inline-end;
}
.bookgpt .sections .chatViewer .conversation .messages .message.botMessage {
  background: #d8d8ffe6;
  position: relative;
}
@media (max-width: 768px) {
  .bookgpt .sections .chatViewer .conversation .messages .message.botMessage {
    padding: unset !important;
  }
}
.bookgpt .sections .chatViewer .conversation .messages .message.botMessage pre {
  width: 100%;
  overflow: scroll;
  font-family: Lexend, sans-serif;
  transition: all 0.3s ease;
  scrollbar-color: white transparent;
  text-wrap: wrap;
}
.bookgpt .sections .chatViewer .conversation .messages .nonText_options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.bookgpt .sections .chatViewer .conversation .messages .nonText_options_btn {
  width: 125px;
  padding: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  height: 37px;
  cursor: pointer;
  background: #FF6F61;
  font-size: 13px;
  font-weight: 400;
  transition: all 0.3s ease;
  font-family: 'Lexend', sans-serif;
  color: #fff;
}
.bookgpt .sections .chatViewer .conversation .messages .nonText_options_btn:hover {
  background-color: #f9f8fc !important;
  border: 1px solid rgba(0, 0, 0, 0.3);
  color: #000;
}
.bookgpt .sections .chatViewer .chatOptions {
  display: flex;
  flex-direction: column;
  position: sticky;
  top: 0;
  background-color: #fff;
  z-index: 10;
  padding-bottom: 10px;
  margin-top: 12px;
  margin-left: 12px;
  margin-right: 12px;
}
@media (max-width: 768px) {
  .bookgpt .sections .chatViewer .chatOptions {
    flex-direction: row;
  }
}
.bookgpt .sections .chatViewer .chatOptions .defaultOptions {
  position: relative;
  flex-wrap: wrap;
  gap: 10px 20px;
  padding-bottom: 10px;
  min-width: 350px;
  transition: all 0.5s ease;
  border-radius: 10px;
  box-shadow: 0 1px 2px 2px rgba(0, 0, 0, 0.1);
  padding-top: 10px;
  padding-right: 10px;
  padding-left: 10px;
  margin: 0 auto;
  overflow: hidden;
}
@media (max-width: 768px) {
  .bookgpt .sections .chatViewer .chatOptions .defaultOptions {
    padding: 5px;
    margin: unset;
    overflow: visible;
    min-width: 250px;
  }
}
.bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard {
  width: auto;
  padding: 5px;
  border-radius: 10px;
  text-align: center;
  display: flex;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  border: 1px solid transparent;
  align-items: center;
  transition: all 0.3s ease;
  gap: 10px;
  padding-right: 8px;
}
@media (max-width: 768px) {
  .bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard {
    text-align: start;
  }
  .bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard:first-child {
    margin-top: 15px;
  }
}
.bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard .defaultOptionCardImgWrap {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard .defaultOptionCardImgWrap img {
  width: 20px;
  filter: brightness(10);
}
@media (max-width: 768px) {
  .bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard .defaultOptionCardImgWrap {
    width: 25px;
    height: 25px;
  }
}
.bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard span {
  background: -webkit-linear-gradient(#615108, #3259ee);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.bookgpt .sections .chatViewer .chatOptions .defaultOptions .defaultOptionCard:hover {
  background-color: #f9f8fc !important;
  border: 1px solid rgba(0, 0, 0, 0.3);
}
.bookgpt .sections .chatViewer .chatOptions #openCloseDP {
  background: transparent;
  border: none;
  width: 90px;
  color: blue;
  cursor: pointer;
  position: absolute;
  right: 0;
  bottom: 12px;
}
.buyPopup {
  display: none;
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s ease-in-out;
  transform: translateY(300%);
  width: 90%;
  margin: 0 auto;
  background: #fff;
  border-top: 2px solid rgba(0, 0, 0, 0.1);
  z-index: 9999;
}
.buyPopup h4 {
  text-align: center;
}
.buyPopup .popupOpt {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  gap: 5px;
  flex-direction: column;
}
.buyPopup .popupOpt p {
  font-size: 14px !important;
  font-weight: 300;
  text-align: center;
  color: #999;
}
.buyPopup .popupOpt .gptAskBuyNowBtn {
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  background: #fbbd08;
  border: none;
  color: #fff;
}
.chat {
  margin: 14px 20px 0 20px;
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  padding: 10px;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 10;
}
@media (max-width: 768px) {
  .chat {
    bottom: 60px !important;
  }
}
.chat .chatInputField {
  width: 100%;
  padding: 0 10px;
  border: none;
  resize: none;
  font-family: Lexend, sans-serif;
  max-height: 250px;
}
.chat .chatInputField:focus {
  border: none;
  outline: none;
}
.chat .chatInputOptions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  align-items: center;
}
.chat .chatInputOptions .formulaBtn {
  border: none;
  color: rgba(105, 76, 252, 0.8);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
.chat .micIcon,
.chat .sendIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  font-size: 16px;
  color: rgba(105, 76, 252, 0.8);
  cursor: pointer;
}
.chat .micIcon:disabled,
.chat .sendIcon:disabled {
  cursor: not-allowed;
}
.chat .upgradeText {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}
.chat .upgradeText span {
  display: flex;
  font-weight: bold;
  align-items: center;
}
.chat .upgradeText span span {
  color: #2F75FE;
}
.chat .upgradeText span img {
  width: 20px;
  margin-left: 4px;
}
.toolbar {
  display: none !important;
}
#viewerContainer {
  top: 0 !important;
  margin-top: 0 !important;
}
.is-typing {
  width: 50px;
  justify-content: space-around;
  align-items: center;
  padding-bottom: 20px;
  scroll-behavior: smooth;
  margin-top: 12px;
}
#resTypeSelector {
  height: 35px;
  font-size: 14px;
  width: 300px;
  margin-left: 12px;
}
.jump1,
.jump2,
.jump3 {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background-color: #2F75FE;
}
.jump1 {
  animation: typing 1.5s linear infinite;
  animation-delay: 0.1s;
}
.jump2 {
  animation: typing 1.5s linear infinite;
  animation-delay: 0.3s;
}
.jump3 {
  animation: typing 1.5s linear infinite;
  animation-delay: 0.5s;
}
.introTextWord {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
}
.introTextWord .logoHighlight {
  color: #ED6114;
}
.introText {
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* align-items: center; */
  color: rgba(0, 0, 0, 0.26);
  font-family: "Lexend", sans-serif;
}
.introText h3 {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
}
.introText h3 .logo {
  font-weight: bold;
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.introText h3 .logo img {
  width: 30px;
  margin-left: 3px;
}
.introText h3 .logo .logoHighlight {
  color: #ED6114;
}
@keyframes typing {
  0% {
    transform: translateY(0px);
  }
  25% {
    transform: translateY(0px);
  }
  35% {
    transform: translateY(8px);
  }
  45% {
    transform: translateY(0px);
  }
  60% {
    transform: translateY(-8px);
  }
  75% {
    background-color: white;
    transform: translateY(0px);
  }
  100% {
    transform: translateY(0px);
  }
}
.chars {
  width: 100%;
  animation: chartyping 4s steps(76), cursor 0.4s step-end infinite alternate;
}
.botAnswerWrapper {
  display: flex;
  margin-bottom: 30px;
  padding-bottom: 10px;
}
.botAnswerWrapper .botTutorIcon {
  width: 40px;
  background: #d8d8ffe6;
  padding: 5px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 8px;
}
.botAnswerWrapper .botTutorIcon img {
  width: 60%;
  height: 60%;
}
.botMessage {
  margin-left: 10px !important;
  max-width: 85%;
}
.userMessage {
  margin-right: 10px !important;
  max-width: 85%;
}
.studentMessageWrapper {
  display: flex;
  justify-content: end;
}
.studentMessageWrapper .studentIcon {
  width: 40px;
  background: #d8d8ffe6;
  padding: 5px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}
.studentMessageWrapper .studentIcon img {
  width: 60%;
  height: 60%;
}
.feedbackWrap {
  margin: 0 15px;
  align-items: center;
}
.feedbackWrap span {
  cursor: pointer;
}
.feedbackOptions {
  padding: 10px;
  width: 100%;
}
.feedbackOptions .closeBtn {
  width: 100%;
  display: block;
  text-align: right;
  margin-bottom: 5px;
  cursor: pointer;
}
.feedbackOptions .feedbackOptions_cards {
  display: flex;
  gap: 12px;
  align-items: center;
  font-size: 13px;
  font-weight: 300;
  margin-bottom: 10px;
  flex-wrap: wrap;
}
.feedbackOptions .feedbackOptions_cards .feedbackOptions_card {
  padding: 5px 10px;
  background: #99999930;
  border-radius: 5px;
  cursor: pointer;
  width: 200px;
  min-height: 40px;
  display: flex;
  align-items: center;
}
.feedbackOptions input {
  width: 100%;
  height: 40px;
  padding: 5px;
  border-radius: 100px;
  border-color: #0000001a;
  padding-left: 18px !important;
}
.feedbackOptions .feedback_submitBtn {
  height: 40px;
  border: none;
  margin-left: 5px;
  width: 100px;
  border-radius: 100px;
  cursor: pointer;
  background: #FF6F61;
  color: #fff;
}
.feedbackModal {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.8);
  /* Black with opacity */
  animation: fadeIn 0.5s;
}
.feedbackModal .modal-content {
  background-color: #fff;
  margin: 15% auto;
  border: 1px solid #888;
  width: 80%;
  max-width: 600px;
  border-radius: 10px;
  animation: slideIn 0.5s;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.3);
}
@media (max-width: 768px) {
  .feedbackModal .modal-content {
    width: 92%;
  }
}
#feedbackContent {
  padding: 10px;
}
#feedbackContent .inputGroup {
  display: flex;
  gap: 8px;
  margin: 12px;
  width: 100%;
  align-items: center;
}
#feedbackContent .inputGroup label {
  width: 30%;
}
@media (max-width: 768px) {
  #feedbackContent .inputGroup label {
    width: 40%;
  }
}
#feedbackContent .inputGroup input,
#feedbackContent .inputGroup select {
  height: 35px;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  width: 20%;
}
@media (max-width: 768px) {
  #feedbackContent .inputGroup input,
  #feedbackContent .inputGroup select {
    width: 50%;
  }
}
#feedbackContent .inputGroup input {
  padding: 10px;
}
#feedbackContent .inputGroup select {
  padding: 5px;
}
#feedbackContent .createBtn {
  margin: 30px 12px 12px 12px;
}
#feedbackContent .createBtn button {
  display: block;
  border: 1px solid #FF6F61;
  border-radius: 5px;
  background: #FF6F61;
  padding: 10px;
  cursor: pointer;
  width: 100%;
  transition: all 0.3s ease;
  color: #fff;
}
#feedbackContent .createBtn button:hover {
  background: transparent;
  color: #000 !important;
}
.notesAndHighlights {
  background: #fff;
  position: absolute;
  width: 350px;
  height: 92vh;
  top: -12px;
  z-index: 9999;
  right: -32px;
  transition: all 0.3s linear;
  transform: translateX(150%);
  padding: 12px;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.5);
}
@media (max-width: 768px) {
  .notesAndHighlights {
    width: 80%;
    top: 0;
    right: 0;
  }
}
.notesAndHighlights .hnHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 12px;
  margin-right: 24px;
}
.notesAndHighlights .hnHeader .fa-xmark {
  font-size: 20px;
  width: 30px;
  height: 30px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
.notesAndHighlights .hnHeader .refresHN {
  margin-left: 5px;
  cursor: pointer;
}
.notesAndHighlights .hnContent {
  margin-top: 10px;
  height: 92%;
  overflow-y: scroll;
}
.notesAndHighlights .hnContent .hn-item {
  margin: 18px 0;
  cursor: pointer;
}
.notesAndHighlights .hnContent .hn-item .hnType {
  font-size: 10px;
  border: 1px solid;
  padding: 2px;
  border-radius: 100px;
  display: flex;
  width: 55px;
  text-align: center;
  justify-content: center;
  align-items: center;
}
.notesAndHighlights .hnContent .hn-item .hnText {
  font-size: 13px;
  margin-top: 5px;
  font-weight: 300;
}
.notesAndHighlights .hnContent .hn-item .hnote {
  margin-left: 5px;
  font-size: 12px;
  margin-top: 5px;
  background: #f4f4f4;
  padding: 4px 5px;
  border-radius: 2px;
  font-weight: 300;
}
.notesAndHighlights .hnContent .hn-item .hnOpt {
  margin-top: 8px;
  display: none;
  justify-content: center;
  align-items: center;
  transition: all 0.3s linear;
  transform: translateY(-30px);
}
.notesAndHighlights .hnContent .hn-item .hnOpt button {
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 2px 6px;
  cursor: pointer;
  border-radius: 10px;
  background: #fff;
}
.notesAndHighlights .hnContent .emptyHnText {
  text-align: center;
  font-size: 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80%;
  flex-direction: column;
}
.notesAndHighlights .hnContent .emptyHnText span {
  color: #ED6114;
}
.notesAndHighlights .apiLoader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100% - 35px);
  position: absolute;
  left: 50%;
  transform: translate(-50%);
  background: #fff;
  width: 100%;
  margin-top: 35px;
}
.close {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}
.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}
.testAns {
  background: #fff;
  padding: 5px;
  border-radius: 5px;
  display: block;
  width: 100%;
}
.testAns i {
  color: green;
  margin-left: 5px;
}
.clearChat #clearChatBtn {
  position: fixed;
  bottom: 12px;
  right: 2px;
  z-index: 999;
  transform: translateX(150px);
  transition: all 0.5s ease;
  border: none;
  padding: 14px;
  border-radius: 5px;
  width: 100px;
  color: #fff;
  cursor: pointer;
  background: #fbbd08;
  width: 115px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px !important;
}
.clearChat #showClearChatBtn {
  transition: all 0.5s ease;
  position: fixed;
  bottom: 12px;
  right: 0px;
  border: none;
  padding: 14px 6px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  background: #fbbd08;
  cursor: pointer;
  color: #fff;
  border-right: 1px solid rgba(0, 0, 0, 0.15);
  z-index: 9999;
  font-size: 13px !important;
}
#defaultPromptPanel {
  margin: 0 auto;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 999;
  background: #fff;
  width: 100%;
}
.legalText {
  margin-bottom: 10px;
}
.legalText p {
  text-align: center;
  font-size: 10px;
  color: #999;
  margin-top: 5px;
  margin-bottom: 5px !important;
}
.chapterHighlight {
  background-color: #efecfe;
}
.tokensSec {
  display: none;
  padding: 5px 10px;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}
@media (max-width: 768px) {
  .tokensSec {
    background: #f4f4f4;
  }
}
.tokensSec-counts {
  display: flex;
  gap: 9px;
}
.tokensSec .superChargeBtn {
  text-decoration: none;
  background: #fbbd08;
  padding: 4px;
  text-align: center;
  border-radius: 4px;
  color: #fff;
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 14px;
}
@media (max-width: 768px) {
  .tokensSec .superChargeBtn {
    font-size: 12px;
  }
}
@media (max-width: 768px) {
  .bookgpt {
    margin: 0;
  }
}
.video-card {
  width: 250px;
  padding: 10px;
  margin-bottom: 10px;
}
.video-card .thumbnail {
  width: 100%;
  border-radius: 5px;
}
.view-btn {
  margin-top: 12px;
  padding: 10px 20px;
  background-color: #ff0000;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}
.view-btn:hover {
  background-color: #cc0000;
}
.videoModal {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}
.videoModal-modal-content {
  position: relative;
  margin: 10% auto;
  padding: 20px;
  background-color: white;
  width: 80%;
  max-width: 600px;
  text-align: center;
  border-radius: 10px;
}
@media (max-width: 768px) {
  .videoModal-modal-content {
    width: 100%;
  }
}
.video-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  /* 16:9 aspect ratio */
  overflow: hidden;
  border-radius: 8px;
}
.video-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}
#videoModal-closeModalBtn {
  position: absolute;
  top: -12px;
  right: 20px;
  font-size: 30px;
  cursor: pointer;
}
.pdfViewer.mobileViewer {
  width: 100% !important;
  position: relative;
  padding: 0 !important;
  display: flex;
  flex-direction: column;
}
@media (max-width: 768px) {
  .pdfViewer.mobileViewer {
    height: 92% !important;
    position: absolute;
    top: 0;
  }
}
.chatViewer.mobileChatViewer {
  width: 100% !important;
  position: relative;
  height: calc(100% - 35px) !important;
  position: absolute !important;
  top: 0;
  transform: translateX(150%);
}
.chatViewer.mobileChatViewer .botMessage {
  max-width: 90% !important;
  min-width: auto !important;
}
.chatViewer.mobileChatViewer .message.userMessage {
  padding: 10px !important;
}
.chatViewer.mobileChatViewer .clearChat #showClearChatBtn {
  bottom: 120px;
}
.chatViewer.mobileChatViewer .clearChat #clearChatBtn {
  bottom: 120px;
}
.chatViewer.mobileChatViewer .legalText {
  margin-bottom: 2px;
}
.chatViewer.mobileChatViewer .legalText p {
  font-size: 10px;
  margin-bottom: 5px;
}
.chatViewer.mobileChatViewer #defaultPromptPanel {
  margin: 0;
  text-align: start;
}
.chatViewer.mobileChatViewer #defaultPromptPanel .introText {
  text-align: start;
  flex-direction: row;
  justify-content: space-between;
}
.chatViewer.mobileChatViewer #defaultPromptPanel .introText h3 {
  justify-content: start;
  margin-left: 12px;
}
.chatViewer.mobileChatViewer #defaultPromptPanel .introTextWord {
  margin-left: 0;
}
.chatViewer.mobileChatViewer .chat {
  margin: 14px 5px 0 5px;
}
.defaultHam {
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  height: 100%;
}
.cta-button {
  display: flex;
  align-items: center;
  padding: 15px 30px;
  font-size: 1.2em;
  background: #fbbd08;
  color: #000;
  border: none;
  cursor: pointer;
  transition: background 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  text-align: center;
  font-weight: bold;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}
.cta-button:hover {
  box-shadow: 0px 10px 20px rgba(0, 123, 255, 0.3);
}
.hamburgerMobileClose,
.closeBuyPopup {
  display: flex;
  position: absolute;
  right: 10px;
  top: -10px;
  box-shadow: 2px 3px 3px rgba(0, 0, 0, 0.2);
  background: #FF6F61;
  padding: 10px;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  justify-content: center;
  align-items: center;
  color: #fff;
  cursor: pointer;
}
.closeBuyPopup {
  width: 18px !important;
  height: 18px !important;
  font-size: 12px;
}
.cta-button {
  font-size: 1em;
  padding: 10px 20px;
  width: 100%;
  text-align: center;
  justify-content: center;
  height: 50px;
}
.cta-button .gptBtnArrow {
  transition: all 0.5s ease-in;
  margin-left: 5px;
  animation: move 4s infinite;
}
.chat-icon {
  width: 16px;
  height: 16px;
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideIn {
  from {
    transform: translateY(200px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
@keyframes cursor {
  50% {
    border-color: transparent;
  }
}
@keyframes chartyping {
  from {
    width: 0;
  }
}
@keyframes move {
  0% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(6px);
  }
  100% {
    transform: translateY(0px);
  }
}
.noticeMsg {
  font-size: 12px;
  font-weight: 300;
}
.noticeMsg.mob {
  font-size: 14px;
  margin-top: 30px;
}
#noticeClose {
  display: flex;
  justify-content: end;
  margin-left: auto;
  cursor: pointer;
  width: 20px;
}
.mobNotice {
  color: rgba(0, 0, 0, 0.2);
}
.hColor {
  color: #ac94ea;
  border-color: #ac94ea;
}
.nColor {
  color: #a1d36e;
  border-color: #a1d36e;
}
.lockedContent {
  -webkit-text-fill-color: unset !important;
  color: #999 !important;
}
.gptBuyNowBtnWrap .gptBuyNowBtn {
  background: #fbbd08;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  color: #fff;
  font-weight: 600;
}
.bookwithAIWrap {
  display: none;
}
.purchaseMsgWrap {
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
  margin-top: 16px;
}
@media (max-width: 768px) {
  .purchaseMsgWrap {
    margin-left: 5px;
    margin-right: 5px;
  }
}
.purchaseMsgWrap svg {
  width: 20px;
  height: 20px;
}
.purchaseMsgWrap p {
  font-size: 15px;
  margin-top: 4px;
}
@media (max-width: 768px) {
  .purchaseMsgWrap p {
    text-align: center;
    font-size: 14px;
    margin-top: 5px;
    margin-left: -5px;
  }
}
.purchaseMsgWrap p span {
  color: #EF6013;
  font-weight: bold;
}
.resizable-x,
.resizable-y {
  display: flex;
  overflow: hidden;
}
.resizable-x {
  height: 100%;
}
.resizable-y {
  flex-direction: column;
}
.resizer-x,
.resizer-y {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ED6114;
  padding: 2px;
}
.resizer-x {
  z-index: 2;
  cursor: col-resize;
}
.resizer-x::before,
.resizer-x::after {
  content: '';
  width: 2px;
  height: 16px;
  margin: 2px;
  background: #fff;
}
.resizer-y {
  z-index: 1;
  cursor: row-resize;
  flex-direction: column;
}
.resizer-y::before,
.resizer-y::after {
  content: '';
  width: 16px;
  height: 2px;
  margin: 2px;
  background: lightgray;
}
.gptBuyNowBtnNew,
.gptBuyNowBtnNewMob {
  border-radius: 5px;
  padding: 10px;
  box-shadow: 0 1px 1px 1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  background: #fbbd08;
  border: none;
  color: #fff;
}
.gptBuyNowBtnNewMob {
  display: none;
}
.showChapterLockedModal {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.showChapterLockedModal p {
  color: #999;
}
.showChapterLockedModal button {
  margin-top: 10px;
  width: 150px;
}
.panel-container {
  display: flex;
  flex-direction: row;
  overflow: hidden;
  /* avoid browser level touch actions */
  xtouch-action: none;
  width: 100%;
}
.panel-left {
  flex: 0 0 auto;
  /* only manually resize */
  min-height: 200px;
  max-width: 95%;
  display: flex;
  flex-direction: column;
  width: 70%;
  transition: all 0.1s ease-in;
}
.splitter {
  flex: 0 0 auto;
  width: 12px;
  background: #ED6114;
  min-height: 200px;
  cursor: col-resize;
  display: flex;
  justify-content: center;
  align-items: center;
}
.splitter:before {
  content: '';
  width: 2px;
  height: 16px;
  margin: 2px;
  background: #fff;
  display: flex;
}
.splitter:after {
  content: '';
  width: 2px;
  height: 16px;
  margin: 2px;
  background: #fff;
  display: flex;
}
.panel-right {
  flex: 1 1 auto;
  /* resizable */
  width: 100%;
  min-height: 200px;
  min-width: 200px;
}
.modal-backdrop {
  z-index: 99999 !important;
}
.modal {
  z-index: 999999 !important;
}
.pdfOpts {
  display: flex;
  color: blue;
  margin-bottom: 5px;
  margin-right: 15px;
}
.pdfOpts span {
  cursor: pointer;
}
#openBookGPT {
  background: #ED6114;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 18px;
  width: 200px;
  margin: 10px auto;
  border-radius: 10px;
  position: relative;
}
#openBookGPT .btn-tooltip {
  position: absolute;
  font-size: 11px;
  background: #000;
  top: -55px;
  width: 210px;
  padding: 5px 10px;
  font-weight: 300;
  border-radius: 10px;
  transition: all 0.3s linear;
  animation: up 2s infinite;
}
#openBookGPT .btn-tooltip:before {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background: #000;
  bottom: -3px;
  transform: rotate(45deg);
  left: 45%;
}
#openBookGPT .imgcont {
  display: flex;
  align-items: center;
  position: relative;
}
#openBookGPT .imgcont img {
  width: 25px;
}
#openBookGPT .imgcont .img1 {
  position: absolute;
  top: -16px;
  right: -11px;
  z-index: 9;
  transition: all 0.44s ease;
}
#openBookGPT .imgcont .img2 {
  position: absolute;
  right: -2px;
  transition: all 0.44s ease;
}
@keyframes up {
  0% {
    top: -55px;
  }
  50% {
    top: -62px;
  }
  100% {
    top: -55px;
  }
}
@keyframes down {
  0% {
    transform: translateY(45px);
  }
  100% {
    transform: translateY(0px);
  }
}
.pdfOpts {
  display: flex;
  justify-content: flex-end;
  color: blue;
  margin-bottom: 5px;
  margin-right: 15px;
  align-items: center;
}
.pdfOpts span {
  cursor: pointer;
}
.printOpt,
.shareOpt,
.likeOtp,
.dislikeOtp {
  position: relative;
  display: flex;
  flex-direction: column;
}
.printOpt:hover .printToolTip,
.shareOpt:hover .printToolTip,
.likeOtp:hover .printToolTip,
.dislikeOtp:hover .printToolTip,
.printOpt:hover .shareOptToolTip,
.shareOpt:hover .shareOptToolTip,
.likeOtp:hover .shareOptToolTip,
.dislikeOtp:hover .shareOptToolTip,
.printOpt:hover .likeBtnTooltip,
.shareOpt:hover .likeBtnTooltip,
.likeOtp:hover .likeBtnTooltip,
.dislikeOtp:hover .likeBtnTooltip,
.printOpt:hover .dislikeBtnTooltip,
.shareOpt:hover .dislikeBtnTooltip,
.likeOtp:hover .dislikeBtnTooltip,
.dislikeOtp:hover .dislikeBtnTooltip {
  display: block;
}
.printToolTip,
.shareOptToolTip,
.likeBtnTooltip,
.dislikeBtnTooltip {
  display: none;
  position: absolute;
  width: 100px;
  background: #000;
  color: #fff;
  top: 24px;
  left: -33px;
  padding: 2px;
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
  transition: all 0.2s linear;
}
.printToolTip:after,
.shareOptToolTip:after,
.likeBtnTooltip:after,
.dislikeBtnTooltip:after {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #000;
  top: -3px;
  transform: rotate(45deg);
  left: 35%;
}
.mobBottomDiv {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 10px;
  gap: 9px;
  margin-left: 5px;
  margin-right: 5px;
}
.mobBottomDiv .gptBuyNowBtnNewMob {
  width: 45% !important;
  padding: 15px !important;
}
.mobBottomDiv #openBookGPT {
  width: 55%;
  border-radius: 5px !important;
  margin: 0 !important;
}
.warning-dialog {
  position: absolute;
  top: -150%;
  background: #ED6114;
  z-index: 99999;
  width: 400px;
  color: #fff;
  padding: 10px;
  transform: translate(-50%, -50%);
  left: 50%;
  border-radius: 10px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.7s ease;
}
.warning-dialog .warning-icon {
  color: yellow;
}
.warning-dialog .warning-close {
  position: absolute;
  right: 0;
  background: #fff;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  color: #000 !important;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  top: -7px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  cursor: pointer;
}
.chatInputWrapper {
  width: 95%;
}
.formulaModalContent p {
  font-size: 16px;
}
.formulaModalContent textarea {
  width: 100%;
  height: 80px;
  border-radius: 10px;
  margin-top: 10px;
  padding: 10px;
}
.formulaActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
.formulaActions .addFormula {
  background: #ED6114;
  color: #fff;
  width: 150px;
  border-radius: 5px;
  padding: 5px 10px;
  border: 1px solid #ED6114;
  cursor: pointer;
}
@media (max-width: 768px) {
  .formulaActions .addFormula {
    width: 125px;
  }
}
.formulaActions .previewFormula {
  width: 150px;
  border-radius: 5px;
  padding: 5px 10px;
  border: 1px solid #ED6114;
  margin-right: 10px;
  cursor: pointer;
}
.formulaPreviewDiv {
  padding: 10px;
  margin: 10px 0;
}
.katex .mathnormal,
.mord.text {
  font-family: "Lexend", sans-serif !important;
  font-style: unset !important;
  font-size: 14px !important;
  font-weight: normal !important;
}
.katex-error {
  color: #000 !important;
}
.katex-display > .katex {
  white-space: pre-wrap;
}
.teacherTitle {
  font-size: 16px;
  color: #000;
  margin-top: 10px;
  font-weight: 500;
  text-align: center;
}
.teacherTitle span {
  color: #ED6114;
}
.dividerLine {
  width: 90%;
  height: 2px;
  background: #0001;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px auto;
}
.katex-display {
  margin: 0 !important;
}
.readBookBtn {
  margin-right: 12px;
  width: auto;
  border-radius: 10px;
  border: none;
  height: 40px;
  font-size: 12px;
  background: transparent;
}
.st_icon {
  background-image: url("data:image/svg+xml,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M5.55988%202.99517C5.72335%202.4804%206.45029%202.4804%206.61376%202.99517C7.30418%206.86986%207.91287%207.47854%2011.7876%208.16896C12.3023%208.33244%2012.3023%209.05938%2011.7876%209.22285C7.91287%209.91327%207.30418%2010.522%206.61376%2014.3966C6.45029%2014.9114%205.72335%2014.9114%205.55988%2014.3966C4.86946%2010.522%204.26077%209.91327%200.386078%209.22285C-0.128693%209.05938%20-0.128693%208.33244%200.386078%208.16896C4.26077%207.47854%204.86946%206.86986%205.55988%202.99517ZM13.1388%2011.5524C13.1621%2011.4807%2013.2074%2011.4183%2013.2683%2011.374C13.3293%2011.3297%2013.4026%2011.3058%2013.478%2011.3058C13.5533%2011.3058%2013.6267%2011.3297%2013.6876%2011.374C13.7485%2011.4183%2013.7938%2011.4807%2013.8171%2011.5524C14.2605%2014.0436%2014.6518%2014.4349%2017.1431%2014.8792C17.4735%2014.9836%2017.4735%2015.4514%2017.1431%2015.5558C14.6518%2016.0001%2014.2605%2016.3914%2013.8162%2018.8826C13.7935%2018.9545%2013.7484%2019.0173%2013.6876%2019.0618C13.6268%2019.1064%2013.5534%2019.1304%2013.478%2019.1304C13.4026%2019.1304%2013.3291%2019.1064%2013.2683%2019.0618C13.2075%2019.0173%2013.1624%2018.9545%2013.1397%2018.8826C12.6954%2016.3914%2012.3041%2016.0001%209.81282%2015.5558C9.74094%2015.533%209.67817%2015.488%209.63362%2015.4271C9.58907%2015.3663%209.56505%2015.2929%209.56505%2015.2175C9.56505%2015.1421%209.58907%2015.0687%209.63362%2015.0079C9.67817%2014.947%209.74094%2014.902%209.81282%2014.8792C12.3041%2014.4349%2012.6945%2014.0436%2013.1388%2011.5524ZM15.2753%200.276097C15.3005%200.196018%2015.3507%200.126072%2015.4184%200.0764212C15.4861%200.0267701%2015.5679%200%2015.6518%200C15.7358%200%2015.8176%200.0267701%2015.8853%200.0764212C15.953%200.126072%2016.0031%200.196018%2016.0283%200.276097C16.5214%203.04386%2016.9561%203.47863%2019.7239%203.97167C19.804%203.99691%2019.8739%204.04702%2019.9236%204.11473C19.9732%204.18244%2020%204.26422%2020%204.34818C20%204.43214%2019.9732%204.51392%2019.9236%204.58163C19.8739%204.64934%2019.804%204.69945%2019.7239%204.72469C16.9561%205.21772%2016.5214%205.6525%2016.0283%208.42026C16.0031%208.50034%2015.953%208.57029%2015.8853%208.61994C15.8176%208.66959%2015.7358%208.69636%2015.6518%208.69636C15.5679%208.69636%2015.4861%208.66959%2015.4184%208.61994C15.3507%208.57029%2015.3005%208.50034%2015.2753%208.42026C14.7823%205.6525%2014.3475%205.21772%2011.5797%204.72469C11.4997%204.69945%2011.4297%204.64934%2011.3801%204.58163C11.3304%204.51392%2011.3036%204.43214%2011.3036%204.34818C11.3036%204.26422%2011.3304%204.18244%2011.3801%204.11473C11.4297%204.04702%2011.4997%203.99691%2011.5797%203.97167C14.3475%203.47863%2014.7823%203.04386%2015.2753%200.276097Z%22%20fill%3D%22url(%23paint0_linear_74_2)%22%2F%3E%0A%3Cdefs%3E%0A%3ClinearGradient%20id%3D%22paint0_linear_74_2%22%20x1%3D%2214.5%22%20y1%3D%2212%22%20x2%3D%2210%22%20y2%3D%2219.1304%22%20gradientUnits%3D%22userSpaceOnUse%22%3E%0A%3Cstop%20offset%3D%220.135%22%20stop-color%3D%22%23FBBD08%22%2F%3E%0A%3Cstop%20offset%3D%220.51%22%20stop-color%3D%22%23B45F6A%22%20stop-opacity%3D%220.6%22%2F%3E%0A%3Cstop%20offset%3D%220.95%22%20stop-color%3D%22%23B45F6A%22%20stop-opacity%3D%220.2%22%2F%3E%0A%3C%2FlinearGradient%3E%0A%3C%2Fdefs%3E%0A%3C%2Fsvg%3E%0A");
  width: 20px;
  height: 20px;
  display: block;
}
.prompts_dropdown,
.prompts_dropdown-highlights {
  border: 1px solid #ddd;
  border-radius: 8px;
  width: 93%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;
  background-color: #fff;
  margin: 0 12px;
  position: sticky;
  top: 0;
  z-index: 999;
  max-width: 60%;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .prompts_dropdown,
  .prompts_dropdown-highlights {
    max-width: 93%;
  }
}
.prompts_dropdown-header,
.prompts_dropdown-highlights .prompts_dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  cursor: pointer;
  font-weight: bold;
  border-radius: 8px 8px 0 0;
}
.prompts_dropdown-highlights .prompts_dropdown-header {
  justify-content: center;
}
.prompts_dropdown-divider {
  width: 70%;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}
.prompts_dropdown-title {
  display: flex;
  align-items: center;
  gap: 8px;
}
.prompts_dropdown-header .arrow {
  transition: transform 0.3s;
}
.prompts_dropdown-header.open .prompts_dropdown-header.arrow i {
  transform: rotate(180deg);
}
.prompts_dropdown-content {
  max-height: 0;
  transition: max-height 0.7s cubic-bezier(0.44, -0.28, 0.06, 1.11);
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: scroll;
}
.prompts_dropdown.open .prompts_dropdown-content {
  max-height: 100%;
  transition: max-height 0.2s ease-in-out;
}
.prompts_dropdown.open {
  padding-bottom: 10px;
}
.prompts_dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px;
  text-decoration: none;
  color: #333;
  transition: background-color 0.3s;
  cursor: pointer;
  text-align: start;
}
.prompts_dropdown-item img {
  margin-right: 8px;
  filter: brightness(0.1);
  width: 20px;
}
.prompts_dropdown-item:hover {
  background-color: #f1f1f1;
}
#snip-btn {
  display: none;
}
#selection-box {
  position: absolute;
  border: 2px solid #999;
  display: none;
  z-index: 1000;
}
#capture-result {
  display: none;
  width: 70px;
  height: 70px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  padding: 7px;
  cursor: pointer;
}
#capture-result img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.inptOtpBtn {
  border: none;
  color: rgba(105, 76, 252, 0.8);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
#capture-result-wrapper {
  position: relative;
}
.snipCancel {
  position: absolute;
  top: -5px;
  right: 0px;
  background: #ED6114;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  cursor: pointer;
}
.snipCancel i {
  font-size: 10px;
}
.thumbnail-container {
  min-width: 300px;
  max-width: 380px;
  margin: 12px;
  padding: 0 12px;
  cursor: pointer;
  transition: transform 0.2s ease;
}
.thumbnail-container:hover {
  transform: scale(1.02);
}
.thumbnail-wrapper {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
.thumbnail {
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
  background: #000;
}
.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 64px;
  height: 64px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
  border: 2px solid #fff;
}
.thumbnail-wrapper:hover .play-overlay {
  background: #6d33e5;
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: none;
  opacity: 1;
  transition: opacity 0.3s ease;
  z-index: 1000;
}
.modal.active {
  display: flex;
  opacity: 1;
}
.video-modal-content {
  position: relative;
  width: 90%;
  max-width: 1200px;
  margin: auto;
}
.close-button {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  z-index: 1001;
}
.video-container {
  width: 100%;
  background: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
}
.video-wrapper {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
  overflow: hidden;
}
.video-wrapper iframe,
.video-wrapper div {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.custom-controls {
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  background: linear-gradient(#1a1a1a, #2d2d2d);
}
.control-btn {
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  color: white;
}
.control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}
.progress-bar {
  flex-grow: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  cursor: pointer;
  position: relative;
}
.progress {
  height: 100%;
  background: #ff0000;
  border-radius: 2px;
  width: 0%;
  transition: width 0.1s linear;
}
.time-display {
  color: white;
  font-size: 0.875rem;
  min-width: 100px;
  text-align: center;
}
.thumbnail-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.4);
}
.videos {
  display: flex;
  flex-wrap: wrap;
}
@media (max-width: 640px) {
  .thumbnail-container {
    margin: 14px 10px;
    min-width: 250px;
    max-width: 320px;
  }
  .play-overlay {
    width: 48px;
    height: 48px;
  }
  .time-display {
    display: none;
  }
  .custom-controls {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  .control-btn {
    width: 32px;
    height: 32px;
  }
}
._qna-label,
._mcq-label {
  text-transform: capitalize;
}
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.fa-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
}
.compiler {
  /* Modal styles */
}
.compiler_open-modal-btn {
  background-color: #3f51b5;
  border: none;
  color: #fff;
  padding: 5px 16px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 14px;
  margin: 4px 2px;
  cursor: pointer;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
  letter-spacing: 0.5px;
}
.compiler_open-modal-btn:hover {
  background-color: #303f9f;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}
.compiler_modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 99999;
  opacity: 0;
  transition: opacity 0.4s ease;
  backdrop-filter: blur(5px);
}
.compiler_modal.show {
  opacity: 1;
}
.compiler_modal.show .compiler_modal-content {
  transform: scale(1);
}
.compiler_modal-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transform: scale(0.95);
  transition: transform 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  overflow: hidden;
  border-radius: 0;
}
.compiler_close-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 99999;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}
.compiler_close-btn:hover {
  background-color: #fff;
  transform: rotate(90deg);
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.3);
}
.compiler_close-btn::before,
.compiler_close-btn::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 2px;
  background-color: #333;
  border-radius: 1px;
}
.compiler_close-btn::before {
  transform: rotate(45deg);
}
.compiler_close-btn::after {
  transform: rotate(-45deg);
}
.compiler_iframe-container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  position: relative;
}
.compiler_iframe-container iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: #fff;
}
.compiler_modal-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  background-color: #3f51b5;
  color: white;
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  z-index: 99999;
}
.compiler_modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}
.compiler_with-header {
  padding-top: 60px;
}
