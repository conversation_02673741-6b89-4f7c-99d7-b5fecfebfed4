.ebookFeatures {
  padding-bottom: 40px !important;
}
.ibookgpt-section {
  background: #fff;
  border-top: 1px solid #EF6013;
  width: 100%;
  min-height: 235px;
  max-height: 300px;
}
@media (max-width: 768px) {
  .ibookgpt-section {
    min-height: 160px;
    display: flex;
    max-height: fit-content;
  }
}
.ibookgpt-section .secs {
  display: flex;
  align-items: center;
  width: 100%;
}
@media (max-width: 768px) {
  .ibookgpt-section .secs {
    flex-direction: column;
  }
}
.ibookgpt-section .secs .sec-left {
  min-height: 235px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 65%;
  clip-path: polygon(0 0, 100% 0, 87% 100%, 0% 100%);
  background: linear-gradient(0deg, rgba(222, 132, 83, 0.44) -12.44%, #EF6013 93.24%), url("data:image/svg+xml,%3Csvg width='967' height='662' viewBox='0 0 967 662' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.656419 192.663C9.34807 194.138 11.6836 196.812 12.9725 206.761C13.1033 207.776 14.3766 207.776 14.5086 206.761C15.7975 196.812 18.1331 194.138 26.8247 192.663C27.7114 192.513 27.7114 191.056 26.8247 190.905C18.1331 189.429 15.7975 186.756 14.5086 176.807C14.3778 175.792 13.1045 175.792 12.9725 176.807C11.6836 186.756 9.34807 189.429 0.656419 190.905C-0.230311 191.056 -0.230311 192.513 0.656419 192.663Z' fill='%23F9E867'/%3E%3Cpath d='M24.0934 627.805C32.7851 629.28 35.1206 631.954 36.4095 641.903C36.5416 642.918 37.8134 642.918 37.9455 641.903C39.2344 631.954 41.5701 629.28 50.2617 627.805C51.1484 627.654 51.1484 626.198 50.2617 626.046C41.5701 624.571 39.2344 621.898 37.9455 611.948C37.8134 610.933 36.5416 610.933 36.4095 611.948C35.1206 621.898 32.7851 624.571 24.0934 626.046C23.2079 626.198 23.2079 627.654 24.0934 627.805Z' fill='%23F9E867'/%3E%3Cpath d='M151.61 364.1C160.301 365.575 162.637 368.248 163.926 378.198C164.058 379.213 165.33 379.213 165.462 378.198C166.751 368.248 169.086 365.575 177.778 364.1C178.665 363.948 178.665 362.492 177.778 362.341C169.086 360.866 166.751 358.192 165.462 348.243C165.33 347.228 164.058 347.228 163.926 348.243C162.637 358.192 160.301 360.866 151.61 362.341C150.723 362.492 150.723 363.95 151.61 364.1Z' fill='%23F9E867'/%3E%3Cpath d='M591.92 616.171C600.611 617.646 602.947 620.32 604.236 630.269C604.367 631.284 605.64 631.284 605.772 630.269C607.061 620.32 609.396 617.646 618.088 616.171C618.975 616.02 618.975 614.564 618.088 614.413C609.396 612.937 607.061 610.264 605.772 600.315C605.64 599.299 604.368 599.299 604.236 600.315C602.947 610.264 600.611 612.937 591.92 614.413C591.033 614.564 591.033 616.021 591.92 616.171Z' fill='%23F9E867'/%3E%3Cpath d='M940.166 217.784C948.857 219.26 951.193 221.933 952.482 231.882C952.614 232.897 953.886 232.897 954.018 231.882C955.307 221.933 957.642 219.26 966.334 239.784C967.221 217.634 967.221 216.177 966.334 216.026C957.642 214.55 955.307 211.877 954.018 201.928C953.887 200.913 952.614 200.913 952.482 201.928C951.193 211.877 948.857 214.55 940.166 216.026C939.279 216.177 939.279 217.634 940.166 217.784Z' fill='%23F9E867'/%3E%3Cpath d='M351.279 16.6963C359.971 18.1717 362.306 20.8452 363.595 30.7944C363.727 31.8094 364.999 31.8094 365.131 30.7944C366.42 20.8452 368.756 18.1717 377.447 16.6963C378.334 16.5452 378.334 15.0892 377.447 14.938C368.756 13.4626 366.42 10.7892 365.131 0.839928C364.999 -0.175102 363.727 -0.175102 363.595 0.839928C362.306 10.7892 359.971 13.4626 351.279 14.938C350.392 15.0892 350.392 16.5465 351.279 16.6963Z' fill='%23F9E867'/%3E%3Cpath d='M674.819 343.446C674.819 345.157 676.032 346.545 677.526 346.545C679.021 346.545 680.234 345.158 680.234 343.446C680.234 341.735 679.021 340.347 677.526 340.347C676.03 340.347 674.819 341.735 674.819 343.446Z' fill='white'/%3E%3Cpath d='M724.002 25.2387C724.002 26.9498 725.215 28.3379 726.71 28.3379C728.204 28.3379 729.417 26.9498 729.417 25.2387C729.417 23.5276 728.204 22.1396 726.71 22.1396C725.214 22.1396 724.002 23.5276 724.002 25.2387Z' fill='white'/%3E%3Cpath d='M234.332 658.904C234.332 660.615 235.545 662.003 237.04 662.003C238.534 662.003 239.747 660.615 239.747 658.904C239.747 657.193 238.534 655.805 237.04 655.805C235.545 655.805 234.332 657.193 234.332 658.904Z' fill='white'/%3E%3Cpath d='M13.74 429.575C13.74 431.286 14.9525 432.674 16.4474 432.674C17.9422 432.674 19.1548 431.286 19.1548 429.575C19.1548 427.864 17.9422 426.475 16.4474 426.475C14.9525 426.475 13.74 427.862 13.74 429.575Z' fill='white'/%3E%3Cpath d='M77.5822 152.875C77.5822 154.587 78.7948 155.975 80.2897 155.975C81.7845 155.975 82.9971 154.587 82.9971 152.875C82.9971 151.164 81.7845 149.776 80.2897 149.776C78.7948 149.776 77.5822 151.164 77.5822 152.875Z' fill='white'/%3E%3Cpath d='M437.102 92.5536C435.607 92.5536 434.395 93.9411 434.395 95.6528C434.395 97.3644 435.607 98.752 437.102 98.752C438.597 98.752 439.81 97.3644 439.81 95.6528C439.81 93.9411 438.597 92.5536 437.102 92.5536Z' fill='white'/%3E%3Cpath d='M50.5709 63.9555C50.5709 65.6666 51.7824 67.0547 53.2784 67.0547C54.7733 67.0547 55.9858 65.6666 55.9858 63.9555C55.9858 62.2444 54.7733 60.8563 53.2784 60.8563C51.7836 60.8563 319.5709 62.2444 50.5709 63.9555Z' fill='white'/%3E%3C/svg%3E%0A");
  background-size: inherit;
  background-blend-mode: overlay;
  background-attachment: fixed;
}
@media (max-width: 768px) {
  .ibookgpt-section .secs .sec-left {
    clip-path: unset;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
  }
}
.ibookgpt-section .secs .sec-left h3 {
  font-size: 36px;
  font-weight: 600;
  text-align: center;
}
.ibookgpt-section .secs .sec-left h3 span {
  color: #fff;
}
.ibookgpt-section .secs .sec-left p {
  display: flex;
  align-items: center;
  font-size: 22px;
  font-weight: 600;
  margin-top: 20px;
  text-align: center;
  justify-content: center;
  color: #000 !important;
}
.ibookgpt-section .secs .sec-left p img {
  width: 135px;
  margin-top: -9px;
  margin-right: 10px;
}
.ibookgpt-section .secs .sec-left .tryNowBtn {
  width: 120px;
  display: flex;
  align-items: center;
  background: #F9E867;
  color: #000 !important;
  padding: 10px;
  justify-content: center;
  margin: 18px auto 2px auto;
  border-radius: 5px;
}
@media (max-width: 768px) {
  .ibookgpt-section .secs .sec-left {
    width: 100%;
    padding: 12px;
    min-height: 150px;
  }
  .ibookgpt-section .secs .sec-left h3 {
    font-size: 22px;
  }
  .ibookgpt-section .secs .sec-left p {
    font-size: 18px;
    margin-top: 8px;
  }
  .ibookgpt-section .secs .sec-left p img {
    width: 90px;
    margin-top: -5px;
  }
}
.ibookgpt-section .secs .sec-right {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50%;
}
@media (max-width: 768px) {
  .ibookgpt-section .secs .sec-right {
    width: 100%;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    clip-path: unset;
  }
}
.ibookgpt-section .secs .sec-right img {
  width: 45%;
  padding: 12px;
}
