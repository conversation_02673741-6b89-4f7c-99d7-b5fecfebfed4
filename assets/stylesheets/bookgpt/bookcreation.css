body {
    font-family: '<PERSON><PERSON>', sans-serif;
    background-color: #f7f7f7;
    margin: 0;
    padding: 0;
    color: #333;
}
.container {
    width: 85%;
    margin: 30px auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Title */
.title {
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    color: #212529;
    margin-bottom: 30px;
}

/* Resource Section */
.resource-selection {
    display: flex;
    gap: 3rem;
    flex-wrap: wrap;
    margin-bottom: 25px;
}

article{
    margin: 12px 18px;
}
.chapters-selection, .resource-type {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    width: 45%;
}
h4 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 15px;
    margin-top: 0;
}
h2{
    font-size: 22px;
}
label {
    display: block;
    font-size: 16px !important;
    color: #6e6893;
    margin-bottom: 10px;
    cursor: pointer;
}
input[type="checkbox"] {
    margin-right: 10px;
}

/* Buttons */
.btn-wrap {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 30px;
}
button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}
.start-btn {
    background-color: #4A90E2;
    color: white;
}
.start-btn:hover {
    background-color: #357ABD;
}
.reset-btn {
    background-color: #D9D5EC;
    color: #212529;
}
.reset-btn:disabled {
    background-color: #e4e4e4;
    cursor: not-allowed;
}
.download-btn {
    background-color: #6D5BD0;
    color: white;
}
.download-btn:disabled {
    background-color: #b5b5ff;
    cursor: not-allowed;
}

/* Hover Effects */
button:hover:not(:disabled) {
    transform: translateY(-3px);
}

/* Progress Bar */
.progress-bar-container {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}
#progress-container {
    height: 10px;
    background: #ddd;
    border-radius: 10px;
    width: 100%;
    display: none;
    margin-top: 10px;
}
#progress-bar {
    background: #6D5BD0;
    border-radius: 10px;
    height: 100%;
    width: 0;
}
#progress-status {
    display: none;
    background: #039B01;
    color: #fff;
    border-radius: 10px;
    padding: 18px;
    width: 250px;
    text-align: center;
    margin-top: 20px;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%);
}

/* Table of Contents */
#book_toc {
    list-style-type: none;
    padding-left: 0;
}
#book_toc li {
    margin: 10px 0;
    font-size: 16px;
}
#book_toc a {
    text-decoration: none;
    color: #4A4AFF;
    transition: color 0.2s ease;
}
#book_toc a:hover {
    color: #039B01;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        width: 95%;
    }
    .btn-wrap {
        flex-direction: column;
        gap: 15px;
        justify-content: center;
    }
    button {
        width: 100%;
        padding: 15px;
        font-size: 18px;
    }
    .resource-selection {
        width: 100%;
        gap: 1.5rem;
    }
}