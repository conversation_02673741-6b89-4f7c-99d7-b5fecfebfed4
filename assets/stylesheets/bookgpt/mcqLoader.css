/* MCQ Loader Styles */
:root {
    --primary: #4a6cf7;
    --secondary: #3151d3;
    --success: #2ecc71;
    --danger: #e74c3c;
    --warning: #f39c12;
    --info: #3498db;
    --light-gray: #e9ecef;
    --gray: #6c757d;
    --dark: #343a40;
    --gradient-start: #4a6cf7;
    --gradient-end: #3151d3;
}

/* Improved Loader Container */
.loader-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.97), rgba(240,242,255,0.97));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.3s ease;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1) inset;
}

/* Enhanced Spinner */
.spinner {
    width: 100px;
    height: 100px;
    border: 10px solid rgba(74, 108, 247, 0.1);
    border-top: 10px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 30px;
    box-shadow: 0 0 15px rgba(74, 108, 247, 0.2);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Improved Loader Text */
.loader-text {
    font-size: 20px;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 15px;
    text-align: center;
    max-width: 80%;
    line-height: 1.5;
    letter-spacing: 0.3px;
}

/* Enhanced Timer Display */
.loader-timer {
    font-size: 42px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 30px;
    font-family: 'Courier New', monospace;
    background: linear-gradient(to right, var(--gradient-start), var(--gradient-end));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(74, 108, 247, 0.15);
    background-color: rgba(255, 255, 255, 0.9);
}

/* Improved Quote Container */
.loader-quote {
    max-width: 700px;
    text-align: center;
    font-style: italic;
    color: var(--gray);
    margin-top: 30px;
    padding: 20px 30px;
    line-height: 1.7;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border-left: 4px solid var(--primary);
}

.loader-quote-author {
    font-weight: 600;
    color: var(--dark);
    margin-top: 12px;
    font-style: normal;
    text-align: right;
}

body.loaded .loader-container {
    display: none;
}

/* Improved Header Styles */
header {
    background: linear-gradient(to right, var(--gradient-start), var(--gradient-end));
    color: white;
    padding: 15px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.logo span {
    font-size: 24px;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.timer-container {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 8px 15px;
    border-radius: 8px;
}

.timer-label {
    margin-right: 10px;
    font-weight: 500;
}

.timer {
    font-family: 'Courier New', monospace;
    font-weight: 700;
    font-size: 18px;
}

/* Main Content Styles */
main {
    padding: 40px 0;
    background-color: #f8f9fa;
    min-height: calc(100vh - 80px);
}

.page-title-container {
    text-align: center;
    margin-bottom: 30px;
}

.page-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 18px;
    color: var(--gray);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Info Card Styles */
.info-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-card-header {
    background: linear-gradient(to right, var(--gradient-start), var(--gradient-end));
    color: white;
    padding: 15px 25px;
}

.info-card-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.info-card-body {
    padding: 25px;
    color: var(--gray);
    line-height: 1.6;
}

.info-card-body p {
    margin-bottom: 15px;
}

.info-card-body p:last-child {
    margin-bottom: 0;
}
