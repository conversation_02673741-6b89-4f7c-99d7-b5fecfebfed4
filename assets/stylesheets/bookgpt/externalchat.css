* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f3f4f8;
  font-family: "Lexend", sans-serif;
}
.chat-container {
  width: 600px;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.chat-box-wrap {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}
.ch-title {
  padding: 10px 16px;
  margin-bottom: 16px;
  background: #fff;
  position: sticky;
  top: 0;
}
.message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}
.message .icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin: 0 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.message .icon img {
  width: 60%;
  height: 60%;
}
.message .bubble {
  max-width: 80%;
  padding: 10px;
  border-radius: 10px;
  font-size: 14px;
  line-height: 1.8;
}
.student-message {
  flex-direction: row-reverse;
}
.student-message .bubble {
  background-color: #efecfe;
}
.bot-message {
  flex-direction: row;
}
.bot-message .bubble {
  background-color: #d8d8ffe6;
  padding: 10px 25px;
}
.input-field {
  padding: 10px;
  border: 1px solid #ccc;
  resize: none;
  margin: 10px 0;
  width: 90%;
}
.input-field:focus {
  outline: none;
}
.chat {
  margin: 14px;
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  padding: 10px;
  background-color: #fff;
}
.chat .chatInputField {
  width: 100%;
  padding: 0 5px;
  border: none;
  resize: none;
  font-family: Lexend, sans-serif;
  max-height: 250px;
}
.chat .chatInputField:focus {
  border: none;
  outline: none;
}
.chat .chatInputOptions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  align-items: center;
}
.chat .chatInputOptions .formulaBtn {
  border: none;
  color: rgba(105, 76, 252, 0.8);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
.chat .chatInputOptions .sendIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  font-size: 16px;
  color: rgba(105, 76, 252, 0.8);
  cursor: pointer;
}
.jump1,
.jump2,
.jump3 {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background-color: #2F75FE;
}
.jump1 {
  animation: typing 1.5s linear infinite;
  animation-delay: 0.1s;
}
.jump2 {
  animation: typing 1.5s linear infinite;
  animation-delay: 0.3s;
}
.jump3 {
  animation: typing 1.5s linear infinite;
  animation-delay: 0.5s;
}
@keyframes typing {
  0%,
  25%,
  45%,
  100% {
    transform: translateY(0px);
  }
  35% {
    transform: translateY(8px);
  }
  60% {
    transform: translateY(-8px);
  }
  75% {
    background-color: white;
  }
}
.chars {
  width: 100%;
  animation: chartyping 4s steps(76), cursor 0.4s step-end infinite alternate;
}
@media (max-width: 1024px) {
  .chat-container {
    width: 100%;
  }
  .message .icon {
    width: 30px;
    height: 30px;
  }
  .message .bubble {
    max-width: 85%;
  }
  .addFormula {
    width: 120px;
  }
}
/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}
.modal .modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  position: relative;
}
.modal .modal-content .close-icon {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
}
.modal .modal-content .close-icon:hover {
  color: red;
}
/* Input Group Styles */
.inputGroup {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
  margin-left: 14px;
}
.inputGroup label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}
.inputGroup input {
  padding: 10px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}
.inputGroup input:focus {
  border-color: #FF6F61;
  box-shadow: 0 0 8px rgba(52, 152, 219, 0.5);
}
/* Create Button */
.createBtn {
  display: flex;
  justify-content: center;
}
.createBtn button {
  background-color: #FF6F61;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: block;
  width: 100%;
}
.createBtn button:hover {
  background-color: #f33b29;
}
.formulaModalContent textarea {
  width: 100%;
  height: 80px;
  border-radius: 10px;
  margin-top: 10px;
  padding: 10px;
}
.formulaPreviewDiv {
  padding: 10px;
  margin: 10px 0;
}
.formulaActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
.formulaActions .addFormula {
  background: #ED6114;
  color: #fff;
  width: 150px;
  border-radius: 5px;
  padding: 5px 10px;
  border: 1px solid #ED6114;
  cursor: pointer;
}
