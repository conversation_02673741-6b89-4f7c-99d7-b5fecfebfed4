@keyframes typing {
  0% {
    transform: translateY(0px);
  }
  25% {
    transform: translateY(0px);
  }
  35% {
    transform: translateY(8px);
  }
  45% {
    transform: translateY(0px);
  }
  60% {
    transform: translateY(-8px);
  }
  75% {
    background-color: white;
    transform: translateY(0px);
  }
  100% {
    transform: translateY(0px);
  }
}
.gpt_loader {
  background: #fff;
  height: 100%;
  position: relative;
  width: 100%;
  top: 0;
  z-index: 99998;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
.spinner {
  border: 10px solid #f3f3f3;
  border-top: 10px solid #3498db;
  border-radius: 50%;
  width: 100px;
  height: 100px;
  animation: spin 1s linear infinite;
}
.introText {
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: rgba(0, 0, 0, 0.26);
  font-family: "Lexend", sans-serif;
}
.introText h3 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 6px;
}
.ib_chat {
  width: 500px;
  height: 85%;
  padding: 12px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  position: absolute;
  background: #fff;
  border-radius: 10px;
  z-index: 9999;
  right: 100px;
  display: flex;
  flex-direction: column;
  transition: all 0.5s ease;
  transform: translateY(150%);
  top: 135px;
}
.chatOpen {
  transform: translateY(0);
}
.ib_chat_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.ib_chat_header h3 {
  display: flex;
  justify-content: center;
}
.ib_chat_header h3 img {
  width: 120px;
}
.ib_chat_header_close {
  width: 18px;
  height: 18px;
  background: red;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
}
.ib_chat_conversation {
  overflow: scroll;
  margin-top: 16px;
}
.studentMessageWrapper {
  display: flex;
  justify-content: end;
  gap: 10px;
}
.studentMessageWrapper .studentIcon {
  width: 30px;
  background: #d8d8ffe6;
  padding: 5px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}
.studentMessageWrapper .studentIcon img {
  width: 60%;
  height: 60%;
}
.messages .message {
  display: inline-block;
  padding: 14px;
  width: auto;
  margin: 8px 0;
  border-radius: 10px;
}
.messages .message.userMessage {
  background: #efecfe;
  position: relative;
  float: inline-end;
  max-width: 80%;
}
.messages .message.botMessage {
  background: #d8d8ffe6;
  position: relative;
  max-width: 100%;
}
.botAnswerWrapper {
  display: flex;
  margin-bottom: 30px;
  padding-bottom: 10px;
  gap: 10px;
}
.botAnswerWrapper .botTutorIcon {
  width: 30px;
  background: #d8d8ffe6;
  padding: 5px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}
.botAnswerWrapper .botTutorIcon img {
  width: 60%;
  height: 60%;
}
.printOpt {
  position: relative;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}
.printOpt:hover .printToolTip {
  display: block;
}
.shareOpt {
  position: relative;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}
.likeOtp {
  position: relative;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}
.dislikeOtp {
  position: relative;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}
.printToolTip {
  display: none;
  position: absolute;
  width: 100px;
  background: #000;
  color: #fff;
  top: 24px;
  left: -33px;
  padding: 2px;
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
  transition: all 0.2s linear;
}
.shareOptToolTip {
  display: none;
  position: absolute;
  width: 100px;
  background: #000;
  color: #fff;
  top: 24px;
  left: -33px;
  padding: 2px;
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
  transition: all 0.2s linear;
}
.likeBtnTooltip {
  display: none;
  position: absolute;
  width: 100px;
  background: #000;
  color: #fff;
  top: 24px;
  left: -33px;
  padding: 2px;
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
  transition: all 0.2s linear;
}
.dislikeBtnTooltip {
  display: none;
  position: absolute;
  width: 100px;
  background: #000;
  color: #fff;
  top: 24px;
  left: -33px;
  padding: 2px;
  font-size: 12px;
  text-align: center;
  border-radius: 5px;
  transition: all 0.2s linear;
}
.ib_chat_input {
  border: 1px solid rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  padding: 10px;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 10;
  margin-top: auto;
}
.chatInputField {
  width: 100%;
  padding: 0 10px;
  border: none;
  resize: none;
  font-family: Lexend, sans-serif;
  max-height: 250px;
}
.chatInputField:focus-visible {
  outline: none;
  border: none;
}
.sendIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  font-size: 16px;
  color: rgba(105, 76, 252, 0.8);
  cursor: pointer;
}
.inptOtpBtn {
  border: none;
  color: rgba(105, 76, 252, 0.8);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
.formulaBtn {
  border: none;
  color: rgba(105, 76, 252, 0.8);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
.chatInputOptions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.is-typing {
  width: 50px;
  justify-content: space-around;
  align-items: center;
  padding-bottom: 20px;
  scroll-behavior: smooth;
  margin-top: 12px;
}
.jump1 {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background-color: #2F75FE;
  animation: typing 1.5s linear infinite;
  animation-delay: 0.1s;
}
.jump2 {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background-color: #2F75FE;
  animation: typing 1.5s linear infinite;
  animation-delay: 0.3s;
}
.jump3 {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background-color: #2F75FE;
  animation: typing 1.5s linear infinite;
  animation-delay: 0.5s;
}
#selection-box {
  position: absolute;
  border: 1px solid transparent;
  display: none;
  z-index: 1000;
  border-radius: 5px;
}
.overlay {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  pointer-events: none;
  z-index: 999;
}
#snip-btn {
  display: none;
  position: absolute;
}
.mainContainerBorder {
  border: 2px solid blue;
  border-radius: 10px;
}
#capture-result-wrapper {
  position: relative;
}
#capture-result {
  display: none;
  width: 70px;
  height: 70px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  padding: 7px;
  cursor: pointer;
}
#capture-result img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.snipCancel {
  position: absolute;
  top: -5px;
  right: 0px;
  background: #ED6114;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  cursor: pointer;
}
.snipCancel i {
  font-size: 10px;
}
.feedbackOptions {
  padding: 10px;
  width: 100%;
}
.feedbackOptions .closeBtn {
  width: 100%;
  display: block;
  text-align: right;
  margin-bottom: 5px;
  cursor: pointer;
}
.formulaModalContent textarea {
  width: 100%;
  height: 80px;
  border-radius: 10px;
  margin-top: 10px;
  padding: 10px;
}
.formulaPreviewDiv {
  padding: 10px;
  margin: 10px 0;
}
.formulaActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}
.formulaActions .addFormula {
  background: #ED6114;
  color: #fff;
  width: 150px;
  border-radius: 5px;
  padding: 5px 10px;
  border: 1px solid #ED6114;
  cursor: pointer;
}
.feedbackModal {
  display: none;
  position: fixed;
  z-index: 9999;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.8);
  animation: fadeIn 0.5s;
}
.feedbackModal .modal-content {
  background-color: #fff;
  margin: 15% auto;
  border: 1px solid #888;
  width: 80%;
  max-width: 600px;
  border-radius: 10px;
  animation: slideIn 0.5s;
  box-shadow: 0 2px 2px rgba(0, 0, 0, 0.3);
}
#feedbackContent {
  padding: 10px;
}
#feedbackContent .inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 12px;
}
#feedbackContent .inputGroup input {
  height: 35px;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 10px;
}
#feedbackContent .inputGroup select {
  height: 35px;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  padding: 5px;
}
#feedbackContent .createBtn {
  margin: 30px 12px 12px 12px;
}
#feedbackContent .createBtn button {
  display: block;
  border: 1px solid #FF6F61;
  border-radius: 5px;
  background: #FF6F61;
  padding: 10px;
  cursor: pointer;
  width: 100%;
  transition: all 0.3s ease;
  color: #fff;
}
#feedbackContent .createBtn button:hover {
  background: transparent;
  color: #000 !important;
}
@media (max-width: 768px) {
  .ib_chat {
    right: 0;
    height: 92%;
    width: 100%;
    top: 60px !important;
  }
}
@media (max-width: 1024px) and (min-width: 769px) {
  .ib_chat {
    right: 20px;
  }
}
