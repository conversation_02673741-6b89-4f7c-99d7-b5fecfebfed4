body {
  font-family: '<PERSON><PERSON>', sans-serif;
  color: #333;
  line-height: 1.5;
  background-color: #f9f9f9;
  margin: 0;
  padding: 0;
}
.book-detail-page {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.book-detail-page .book-title {
  overflow: hidden;
  display: inline-block;
  white-space: pre-wrap;
}
.show-chapters-btn {
  display: none;
}
.chapters-list {
  width: 30%;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 85vh;
  overflow-y: scroll;
}
.chapters-list .chapter-item {
  padding: 10px;
  margin-bottom: 10px;
  background-color: #f6f5f5;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
}
.chapters-list .chapter-item:hover {
  background-color: #d9e4f1;
  color: #000;
}
.chapters-list .chapter-item.active {
  background-color: #d9e4f1;
  color: #000;
  font-weight: bold;
}
.chapter-details {
  width: 65%;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 85vh;
  overflow-y: auto;
}
.chapter-details h2 {
  margin-bottom: 15px;
  font-size: 24px;
  color: #333;
}
.chapter-details table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}
.chapter-details table th,
.chapter-details table td {
  text-align: left;
  padding: 10px;
  border: 1px solid #e1e1e1;
}
.chapter-details table th {
  background-color: #f4f4f4;
  font-weight: bold;
}
.chapter-details table td a {
  color: #0073e6;
  text-decoration: none;
}
.chapter-details table td a:hover {
  text-decoration: underline;
}
.copy-button {
  padding: 8px 12px;
  background-color: #3498db;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
}
.copy-button:hover {
  background-color: #2980b9;
}
.copy-button.copied {
  background-color: #2ecc71;
  transform: scale(1.1);
  color: #fff;
}
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 20px auto;
  display: block;
}
.loader-container {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;
  justify-content: center;
  align-items: center;
}
.loader-container .app-loader {
  border: 8px solid #f3f3f3;
  border-top: 8px solid #3498db;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (max-width: 1024px) {
  .book-detail-page {
    flex-direction: column;
    gap: 10px;
    position: relative;
  }
  .book-detail-page .chapters-list {
    width: 100%;
    position: absolute;
    left: -100%;
    transition: left 0.3s ease;
    z-index: 10;
  }
  .book-detail-page .chapter-details {
    width: 100%;
  }
  .show-chapters-btn {
    display: block;
    margin: 10px auto;
    padding: 10px 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  .show-chapters-btn:hover {
    background-color: #2980b9;
  }
}
@media (max-width: 768px) {
  .book-title {
    font-size: 18px;
  }
  .chapters-list {
    padding: 15px;
  }
  .chapter-details h2 {
    font-size: 20px;
  }
  .chapter-details table th,
  .chapter-details table td {
    font-size: 14px;
  }
  .copy-button {
    padding: 6px 10px;
    font-size: 14px;
  }
}
@media (max-width: 480px) {
  .book-title {
    width: auto;
  }
  .copy-button {
    width: 100%;
  }
}
.admin-dash {
  margin-top: 3rem;
  min-height: 760px;
}
.header {
  text-align: center;
  margin-bottom: 20px;
}
.header h1 {
  font-size: 24px;
  color: #2f3640;
  text-transform: uppercase;
}
.book-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));
  gap: 20px;
  padding: 20px;
}
.book-card {
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}
.book-image {
  width: 100%;
  padding-top: 140%;
  background-size: cover;
  background-position: center;
}
.book-title {
  padding: 10px;
  background: #fff;
  border-top: 1px solid #eee;
}
@media (max-width: 600px) {
  .book-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 15px;
  }
}
.pagination {
  text-align: center;
  margin-top: 20px;
}
.pagination button {
  padding: 10px 20px;
  margin: 5px;
  background-color: #2f3640;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.pagination button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}
/* Typography */
h1 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 15px;
}
h2 {
  font-size: 30px;
  font-weight: 600;
  margin-bottom: 12px;
}
h3 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 10px;
}
h4 {
  font-size: 20px;
  font-weight: 500;
  margin-bottom: 8px;
}
h5 {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 6px;
}
h6 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 5px;
}
p {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 15px;
}
ul,
ol {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 15px;
  padding-left: 40px;
}
li {
  margin-bottom: 8px;
}
.container {
  margin-top: 20px;
}
.card {
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding-bottom: 0;
}
.card-header {
  background-color: #3498db;
  color: white;
  font-weight: bold;
  padding: 10px 15px;
  border-radius: 8px 8px 0 0;
}
.card-body {
  padding: 15px;
  padding-bottom: 0;
  display: flex;
  flex-direction: column;
}
pre {
  background-color: #f8f9fa;
  padding: 10px;
  border-radius: 5px;
  font-family: monospace;
  overflow-x: auto;
}
code {
  color: #d63384;
}
/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
}
.modal-content {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 500px;
  position: relative;
}
.close-icon {
  position: absolute;
  top: 10px;
  right: 15px;
  font-size: 20px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
}
.close-icon:hover {
  color: red;
}
/* Input Group */
.inputGroup {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
  margin-left: 14px;
}
.inputGroup label {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #333;
}
.inputGroup input {
  padding: 10px;
  font-size: 16px;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  outline: none;
  width: 100%;
  box-sizing: border-box;
}
.inputGroup input:focus {
  border-color: #FF6F61;
  box-shadow: 0 0 8px rgba(52, 152, 219, 0.5);
}
/* Buttons */
.createBtn {
  display: flex;
  justify-content: center;
}
.createBtn button {
  background-color: #FF6F61;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: block;
  width: 100%;
}
.createBtn button:hover {
  background-color: #ff412e;
}
.question {
  font-size: 16px;
  font-weight: 600;
  color: #2f3640;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.15);
}
.question:last-child {
  border-bottom: none;
}
.answer {
  font-size: 14px;
  margin: 10px 0;
  padding: 8px;
  background-color: #e1f0fa;
  border-left: 5px solid #3498db;
}
.difficulty {
  font-size: 14px !important;
  font-style: italic;
  color: #333 !important;
  text-transform: capitalize !important;
}
.question-paper-container {
  margin: 20px auto;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  max-width: 900px;
  border-radius: 8px;
}
.question-paper-container .question {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}
.question-paper-container .question p {
  margin: 5px 0;
  font-size: 16px;
  color: #333;
}
.show-answers-btn {
  display: block;
  margin: 20px auto;
  padding: 10px 20px;
  font-size: 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}
.show-answers-btn:hover {
  background-color: #3d8b40;
}
.cwrap {
  width: 60%;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .card {
    padding: 15px;
    padding-bottom: 0;
  }
  .card-body {
    padding-bottom: 0;
  }
  .card-header {
    font-size: 18px;
  }
  .modal-content {
    width: 90%;
  }
  .cwrap {
    width: 100%;
  }
}
.question-paper-container,
.show-answers-btn {
  display: none;
}
.menuOptions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  /* Light background for better contrast */
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}
.menuOptions button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 7px;
  background-color: #007bff;
  /* Primary blue color */
  color: #ffffff;
  border: none;
  border-radius: 5px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.menuOptions button i {
  font-size: 16px;
}
.menuOptions button:hover {
  background-color: #0056b3;
  /* Darker shade on hover */
}
.menuOptions button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
  /* Focus ring */
}
.menuOptions button:active {
  background-color: #004085;
  /* Even darker shade on click */
}
.updateTitle {
  background-color: #FF6F61;
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: block;
  width: 100%;
  margin-top: 12px;
}
.copy-btn {
  background: transparent;
  border: none;
  padding-bottom: 10px;
}
@media print {
  .copy-btn {
    display: none;
  }
}
.menuOptions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  /* Light background for better contrast */
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}
.menuOptions button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 7px;
  background-color: #007bff;
  /* Primary blue color */
  color: #ffffff;
  border: none;
  border-radius: 5px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}
.menuOptions button i {
  font-size: 16px;
}
.menuOptions button:hover {
  background-color: #0056b3;
  /* Darker shade on hover */
}
.menuOptions button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
  /* Focus ring */
}
.menuOptions button:active {
  background-color: #004085;
  /* Even darker shade on click */
}
.updateTitle {
  background-color: #FF6F61;
  color: white;
  padding: 10px 20px;
  font-size: 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: block;
  width: 100%;
  margin-top: 12px;
}
.copy-btn {
  background: transparent;
  border: none;
  padding-bottom: 10px;
  margin-left: auto;
}
@media print {
  .copy-btn {
    display: none;
  }
}
