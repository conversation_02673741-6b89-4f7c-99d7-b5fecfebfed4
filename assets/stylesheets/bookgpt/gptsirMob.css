/* PrepCapsule Mobile Styles */
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
a,
blockquote,
cite,
ol,
ul,
li,
fieldset,
form,
label,
img,
div,
dl,
dt,
dd,
input,
figure,
figcaption,
picture,
button {
    margin: 0;
    padding: 0;
    border: 0;
    font-weight: normal;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

html {
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
}

html,
body {
    font-family: 'Rubik', sans-serif;
    overflow-x: visible;
    scroll-behavior: smooth;
}

body {
    /* background-image: url('../images/bg-noise.png'); */
    background-size: 80px 80px;
    background-color: #263238;
    color: #ffffff99;
}

/* KEYFRAMES */
@-webkit-keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@-moz-keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@-o-keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@-webkit-keyframes slideInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-moz-keyframes slideInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-o-keyframes slideInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@keyframes slideInUp {
    0% {
        opacity: 0;
        -webkit-transform: translateY(100%);
        transform: translateY(100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-webkit-keyframes slideInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-moz-keyframes slideInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@-o-keyframes slideInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@keyframes slideInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
@keyframes a {
    0%,
    to {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}
/* GLOBAL */
.wrap {
    width: 84%;
    padding: 0 8%;
    max-width: 400px;
    margin: 0 auto;
}

.mark {
    width: 28px;
    height: 44px;
    display: block;
    text-indent: -9999px;
    background-size: contain;
}

.button {
    height: 42px;
    padding: 0 12px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 8px;
    font-weight: 700;
    font-size: 14px;
    line-height: 42px;
    text-transform: uppercase;
    background-color: #d03f53;
    color: #fff;
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    align-items: center;
    display: inline-flex;
    text-decoration: none;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -o-transition: all 0.4s ease;
    transition: all 0.4s ease;
    justify-content: center;
    border: 1px solid #d03f53;
}
.button:hover {
    background-color: inherit;
    color: #d03f53;
}
.button.but-secondary {
    background-color: transparent;
    border: 1px solid #fff;
}
.button.but-secondary:hover {
    background-color: inherit;
    border-color: #58a8ca;
    color: #58a8ca;
}

/* SECTIONS */
.hero {
    text-align: center;
    padding-top: 32px;
}
.hero .hero-content {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: center;
    z-index: 1;
    position: relative;
}
.hero .mark {
    margin: 0 auto;
    width: 100px;
}
.hero .intro {
    margin: 40px 0 70px 0;
}
.hero .intro h3 {
    font-weight: 400;
    font-size: 36px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    margin-bottom: 20px;
}
.hero .intro p {
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    text-align: center;
}
.hero .vertical-separator {
    display: none;
}
.hero img {
    width: 100%;
    height: auto;
    max-width: 500px;
}
.hero img.logo_image {
    width: 30%;
}

.scroll-down {
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #58a8ca;
    margin: 56px 0 16px 0;
}
.helper-text {
    text-align: center;
    margin: 50px 0 0;
    line-height: 26px;
    font-size: 18px;
}
.pillar-title {
    font-weight: 400;
    font-size: 26px;
    line-height: 36px;
    color: #fff;
    margin: 10px 0 30px;
    text-align: center;
}
@media only screen and (min-width: 700px) {
    .scroll-down {
        width: 20px !important;
        height: 140px;
        padding: 0;
        display: block;
        text-indent: -9999px;
        background-image: url("data:image/svg+xml,%3csvg width='83' height='606' viewBox='0 0 83 606' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M65.7 491.16c0 4.06-.607 7.467-1.82 10.22-1.26 2.753-2.87 4.877-4.83 6.37-2.007 1.447-4.13 2.217-6.37 2.31-.373 0-.7-.14-.98-.42-.327-.327-.49-.7-.49-1.12v-3.57c0-.56.14-.98.42-1.26.28-.28.607-.467.98-.56 1.12-.233 2.24-.793 3.36-1.68 1.073-.933 1.983-2.217 2.73-3.85.7-1.68 1.05-3.827 1.05-6.44 0-4.013-.7-6.953-2.1-8.82-1.447-1.913-3.36-2.87-5.74-2.87-1.633 0-2.94.513-3.92 1.54-1.027.98-1.937 2.497-2.73 4.55-.793 2.053-1.657 4.713-2.59 7.98-.98 3.407-2.053 6.23-3.22 8.47-1.167 2.24-2.613 3.92-4.34 5.04-1.727 1.073-3.92 1.61-6.58 1.61-2.52 0-4.76-.677-6.72-2.03-2.007-1.353-3.593-3.29-4.76-5.81-1.167-2.567-1.75-5.67-1.75-9.31 0-2.893.397-5.413 1.19-7.56.747-2.193 1.773-4.013 3.08-5.46 1.26-1.447 2.637-2.543 4.13-3.29 1.447-.747 2.87-1.143 4.27-1.19.327 0 .653.117.98.35.28.233.42.607.42 1.12v3.71c0 .327-.093.677-.28 1.05-.187.327-.537.583-1.05.77-1.913.28-3.523 1.353-4.83 3.22-1.307 1.867-1.96 4.293-1.96 7.28 0 3.033.583 5.507 1.75 7.42 1.167 1.867 2.987 2.8 5.46 2.8 1.587 0 2.917-.443 3.99-1.33 1.027-.887 1.937-2.287 2.73-4.2.793-1.913 1.633-4.41 2.52-7.49 1.027-3.733 2.123-6.79 3.29-9.17 1.12-2.38 2.543-4.13 4.27-5.25 1.68-1.167 3.85-1.75 6.51-1.75 2.94 0 5.46.77 7.56 2.31 2.053 1.54 3.64 3.71 4.76 6.51 1.073 2.753 1.61 6.02 1.61 9.8Zm0-48.935c0 4.293-.793 7.863-2.38 10.71-1.587 2.8-3.803 4.923-6.65 6.37-2.847 1.447-6.16 2.24-9.94 2.38-1.913.047-3.967.07-6.16.07s-4.293-.023-6.3-.07c-3.78-.14-7.093-.933-9.94-2.38-2.847-1.447-5.063-3.57-6.65-6.37-1.587-2.847-2.38-6.417-2.38-10.71 0-3.22.443-6.043 1.33-8.47.887-2.427 2.077-4.433 3.57-6.02 1.493-1.633 3.173-2.87 5.04-3.71 1.82-.84 3.71-1.307 5.67-1.4.42-.047.77.093 1.05.42.28.28.42.63.42 1.05v3.85c0 .42-.117.793-.35 1.12-.233.28-.653.49-1.26.63-3.64.793-6.137 2.287-7.49 4.48-1.353 2.147-2.03 4.853-2.03 8.12 0 3.733 1.073 6.697 3.22 8.89 2.1 2.193 5.483 3.383 10.15 3.57 3.827.14 7.747.14 11.76 0 4.667-.187 8.073-1.377 10.22-3.57 2.1-2.193 3.15-5.157 3.15-8.89 0-3.267-.677-5.973-2.03-8.12-1.353-2.193-3.85-3.687-7.49-4.48-.607-.14-1.027-.35-1.26-.63a1.88 1.88 0 0 1-.35-1.12v-3.85c0-.42.14-.77.42-1.05.28-.327.63-.467 1.05-.42 1.96.093 3.873.56 5.74 1.4 1.82.84 3.477 2.077 4.97 3.71 1.493 1.587 2.683 3.593 3.57 6.02.887 2.427 1.33 5.25 1.33 8.47Zm-.7-34.091c0 .513-.14.91-.42 1.19-.327.28-.723.42-1.19.42H17.68c-.513 0-.91-.14-1.19-.42-.327-.28-.49-.677-.49-1.19v-17.08c0-5.273 1.237-9.45 3.71-12.53s6.137-4.62 10.99-4.62c3.593 0 6.533.91 8.82 2.73 2.24 1.773 3.803 4.2 4.69 7.28l18.62-10.78c.28-.14.537-.21.77-.21.373 0 .7.163.98.49.28.28.42.607.42.98v3.36c0 .793-.21 1.377-.63 1.75-.42.373-.84.7-1.26.98l-17.78 10.15v12.25h18.06c.467 0 .863.163 1.19.49.28.28.42.677.42 1.19v3.57Zm-25.62-5.25v-11.55c0-3.5-.723-6.113-2.17-7.84-1.493-1.727-3.687-2.59-6.58-2.59-2.847 0-5.017.863-6.51 2.59-1.493 1.68-2.24 4.293-2.24 7.84v11.55h17.5Zm26.32-60.011c0 3.967-.677 7.374-2.03 10.22-1.4 2.847-3.523 5.04-6.37 6.58-2.893 1.54-6.51 2.404-10.85 2.59-2.053.047-4.013.07-5.88.07-1.913 0-3.897-.023-5.95-.07-4.293-.186-7.863-1.073-10.71-2.66-2.893-1.633-5.04-3.873-6.44-6.72-1.447-2.893-2.17-6.23-2.17-10.01 0-3.826.723-7.163 2.17-10.01 1.4-2.893 3.547-5.156 6.44-6.79 2.847-1.633 6.417-2.52 10.71-2.66 2.053-.093 4.037-.14 5.95-.14 1.867 0 3.827.047 5.88.14 4.34.14 7.957 1.004 10.85 2.59 2.847 1.54 4.97 3.734 6.37 6.58 1.353 2.847 2.03 6.277 2.03 10.29Zm-5.95 0c0-3.593-1.073-6.51-3.22-8.75-2.147-2.286-5.623-3.523-10.43-3.71-2.1-.093-3.967-.14-5.6-.14-1.68 0-3.547.047-5.6.14-3.22.094-5.833.7-7.84 1.82-2.007 1.074-3.477 2.52-4.41 4.34-.933 1.82-1.4 3.92-1.4 6.3 0 2.287.467 4.34 1.4 6.16.933 1.82 2.403 3.29 4.41 4.41 2.007 1.074 4.62 1.68 7.84 1.82 2.053.047 3.92.07 5.6.07 1.633 0 3.5-.023 5.6-.07 4.807-.186 8.283-1.423 10.43-3.71 2.147-2.286 3.22-5.18 3.22-8.68ZM65 307.96c0 .513-.14.91-.42 1.19-.327.28-.723.42-1.19.42H17.61c-.467 0-.84-.14-1.12-.42-.327-.28-.49-.677-.49-1.19v-3.71c0-.467.163-.84.49-1.12.28-.28.653-.42 1.12-.42h41.3V279.4c0-.513.163-.91.49-1.19.28-.28.677-.42 1.19-.42h2.8c.467 0 .863.14 1.19.42.28.28.42.677.42 1.19v28.56Zm0-42.943c0 .513-.14.91-.42 1.19-.327.28-.723.42-1.19.42H17.61c-.467 0-.84-.14-1.12-.42-.327-.28-.49-.677-.49-1.19v-3.71c0-.467.163-.84.49-1.12.28-.28.653-.42 1.12-.42h41.3v-23.31c0-.514.163-.91.49-1.19.28-.28.677-.42 1.19-.42h2.8c.467 0 .863.14 1.19.42.28.28.42.676.42 1.19v28.56Zm0-61.756c0 .513-.14.91-.42 1.19-.327.28-.723.42-1.19.42H17.68c-.513 0-.91-.14-1.19-.42-.327-.28-.49-.677-.49-1.19v-15.89c0-4.807.723-8.68 2.17-11.62 1.447-2.94 3.593-5.087 6.44-6.44 2.8-1.353 6.253-2.053 10.36-2.1 2.1-.047 3.943-.07 5.53-.07 1.587 0 3.407.023 5.46.07 4.293.093 7.863.817 10.71 2.17 2.8 1.307 4.9 3.407 6.3 6.3 1.353 2.847 2.03 6.627 2.03 11.34v16.24Zm-5.95-5.25v-10.64c0-3.22-.443-5.787-1.33-7.7-.887-1.913-2.31-3.29-4.27-4.13-1.96-.887-4.55-1.353-7.77-1.4a55.96 55.96 0 0 0-3.64-.14h-3.15a53.66 53.66 0 0 0-3.64.14c-4.527.093-7.863 1.167-10.01 3.22-2.193 2.053-3.29 5.507-3.29 10.36v10.29h37.1Zm6.65-62.472c0 3.967-.677 7.374-2.03 10.22-1.4 2.847-3.523 5.04-6.37 6.58-2.893 1.54-6.51 2.404-10.85 2.59-2.053.047-4.013.07-5.88.07-1.913 0-3.897-.023-5.95-.07-4.293-.186-7.863-1.073-10.71-2.66-2.893-1.633-5.04-3.873-6.44-6.72-1.447-2.893-2.17-6.23-2.17-10.01 0-3.826.723-7.163 2.17-10.01 1.4-2.893 3.547-5.156 6.44-6.79 2.847-1.633 6.417-2.52 10.71-2.66 2.053-.093 4.037-.14 5.95-.14 1.867 0 3.827.047 5.88.14 4.34.14 7.957 1.004 10.85 2.59 2.847 1.54 4.97 3.734 6.37 6.58 1.353 2.847 2.03 6.277 2.03 10.29Zm-5.95 0c0-3.593-1.073-6.51-3.22-8.75-2.147-2.286-5.623-3.523-10.43-3.71-2.1-.093-3.967-.14-5.6-.14-1.68 0-3.547.047-5.6.14-3.22.094-5.833.7-7.84 1.82-2.007 1.074-3.477 2.52-4.41 4.34-.933 1.82-1.4 3.92-1.4 6.3 0 2.287.467 4.34 1.4 6.16.933 1.82 2.403 3.29 4.41 4.41 2.007 1.074 4.62 1.68 7.84 1.82 2.053.047 3.92.07 5.6.07 1.633 0 3.5-.023 5.6-.07 4.807-.186 8.283-1.423 10.43-3.71 2.147-2.286 3.22-5.18 3.22-8.68ZM65 94.591c0 .607-.163 1.097-.49 1.47-.327.327-.793.56-1.4.7l-44.94 9.03a2.04 2.04 0 0 1-.49.07h-.28c-.373 0-.7-.14-.98-.42-.28-.28-.42-.606-.42-.98v-3.64c0-.98.397-1.54 1.19-1.68l36.12-7.14-24.78-7.56a2.837 2.837 0 0 1-1.12-.7c-.373-.373-.56-.886-.56-1.54v-2.59c0-.7.187-1.213.56-1.54.327-.326.7-.536 1.12-.63l24.78-7.63-36.12-7.07c-.793-.14-1.19-.723-1.19-1.75v-3.64c0-.373.14-.7.42-.98.28-.28.607-.42.98-.42.047 0 .14.024.28.07h.49l44.94 9.03c.607.14 1.073.397 1.4.77.327.327.49.794.49 1.4v2.73c0 .654-.163 1.167-.49 1.54-.327.374-.747.63-1.26.77l-26.67 8.68 26.67 8.61a3 3 0 0 1 1.26.84c.327.327.49.817.49 1.47v2.73Zm0-52.904c0 .513-.14.91-.42 1.19-.327.28-.723.42-1.19.42H17.68c-.513 0-.91-.14-1.19-.42-.327-.28-.49-.677-.49-1.19v-3.22c0-.56.14-.98.42-1.26.233-.28.42-.444.56-.49l35.7-23.24h-35c-.513 0-.91-.14-1.19-.42-.327-.28-.49-.677-.49-1.19v-3.36c0-.514.163-.91.49-1.19.28-.327.677-.49 1.19-.49h45.64c.467 0 .863.163 1.19.49.327.28.49.653.49 1.12v3.36c0 .513-.14.91-.42 1.19-.28.233-.467.396-.56.49l-35.35 23.17h34.72c.467 0 .863.163 1.19.49.28.28.42.676.42 1.19v3.36Z' fill='%2311D3E5'/%3e%3cpath stroke='%2311D3E5' stroke-width='5' d='M44.5 546v60'/%3e%3c/svg%3e");
        background-size: contain;
        margin: 0 auto;
        margin-top: 50px;
    }
}

.mod {
    /*padding: 30px 0;*/
    margin: 20px 0;
}
@media only screen and (min-width: 700px) {
    .mod {
        padding: 30px 0;
    }
}
.mod .col {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    position: relative;
}
.mod .col.phone {
    text-align: center;
}
.mod img {
    width: 100%;
    height: auto;
    max-width: 400px;
    margin-top: 40px;
}
.mod h3 {
    font-weight: 600;
    font-size: 42px;
    line-height: 70px;
    color: #fff;
}
@media only screen and (min-width: 1020px) {
    .mod h3 {
        font-size: 60px;
        line-height: 52px;
    }
}

.mod p {
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    margin: 12px 0 0 0;
}
.mod.prep_immerse img {
    width: 100%;
}

/* SVG WORDS */
.two-words,
.word {
    display: none;
}

/* TERMS */
.terms {
    padding: 56px 0;
}
.terms.wrap {
    max-width: 740px !important;
}
.terms h1,
.terms h2 {
    font-weight: 400;
    text-transform: uppercase;
    font-size: 42px;
    line-height: 36px;
    margin-bottom: 56px;
}
@media only screen and (min-width: 1020px) {
    .terms h1,
    .terms h2 {
        font-size: 60px;
        line-height: 52px;
    }
}
.terms h2 {
    font-size: 30px;
    line-height: 24px;
    padding-top: 32px;
    margin-bottom: 24px;
}
.terms h3,
.terms p,
.terms li {
    font-weight: 400;
    font-size: 17px;
    line-height: 28px;
    margin-bottom: 20px;
}
.terms h3 {
    font-weight: 700;
    padding-top: 12px;
    margin-bottom: 8px;
}
.terms p strong {
    font-weight: 700;
}
.terms p a {
    color: #fff;
    text-decoration: underline;
}
.terms p a:hover {
    color: #58a8ca;
}
.terms ul,
.terms ol {
    list-style: outside;
    padding-top: 12px;
    margin-left: 16px;
    margin-bottom: 32px;
}
.terms ul ul,
.terms ol ul {
    padding-top: 20px;
    margin-left: 32px;
}
.terms ul ul li,
.terms ol ul li {
    list-style-type: circle;
}

/* LOCOMOTIVE SCROLL */
html.has-scroll-smooth {
    overflow: hidden;
}

.has-scroll-smooth body {
    overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
    min-height: 100vh;
}

.c-scrollbar {
    position: absolute;
    right: 0;
    top: 0;
    width: 8px;
    height: 100vh;
    transform-origin: center;
    transition: transform 0.3s, opacity 0.3s;
    opacity: 0;
}

.c-scrollbar,
.c-scrollbar:hover,
.has-scroll-scrolling .c-scrollbar {
    opacity: 1;
}

.c-scrollbar_thumb {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #58a8ca;
    width: 8px;
}

/* ANIMATION */
.hero-content,
img,
.scroll-down,
.pillar-title,
.animate-title .animate-line span .animate-text .animate-line span {
    opacity: 0;
}

.mark {
    -webkit-animation-delay: 0.3s;
    -webkit-animation-duration: 1s;
    -webkit-animation-name: slideInDown;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.3s;
    -moz-animation-duration: 1s;
    -moz-animation-name: slideInDown;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.3s;
    -o-animation-duration: 1s;
    -o-animation-name: slideInDown;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.3s;
    animation-duration: 1s;
    animation-name: slideInDown;
    animation-fill-mode: both;
    animation-timing-function: ease;
}

.button .animate-line {
    display: block;
    overflow-y: hidden;
}
.button .animate-line span {
    display: block;
}
.button .animate-line.word1 span {
    -webkit-animation-delay: 0.5s;
    -webkit-animation-duration: 0.3s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.5s;
    -moz-animation-duration: 0.3s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.5s;
    -o-animation-duration: 0.3s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.5s;
    animation-duration: 0.3s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}

.in-view.hero-content {
    -webkit-animation-delay: 0.3s;
    -webkit-animation-duration: 1s;
    -webkit-animation-name: fadeIn;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.3s;
    -moz-animation-duration: 1s;
    -moz-animation-name: fadeIn;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.3s;
    -o-animation-duration: 1s;
    -o-animation-name: fadeIn;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.3s;
    animation-duration: 1s;
    animation-name: fadeIn;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view img {
    -webkit-animation-delay: 0.2s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: fadeIn;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.2s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: fadeIn;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.2s;
    -o-animation-duration: 0.8s;
    -o-animation-name: fadeIn;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.2s;
    animation-duration: 0.8s;
    animation-name: fadeIn;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view.scroll-down {
    -webkit-animation-delay: 0.3s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.3s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.3s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.3s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view.pillar-title {
    -webkit-animation-delay: 0.3s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.3s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.3s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.3s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-title .animate-line,
.in-view .animate-text .animate-line {
    display: block;
    overflow-y: hidden;
}
.in-view .animate-title .animate-line span,
.in-view .animate-text .animate-line span {
    display: block;
}
.in-view .animate-title .animate-line.line1 span,
.in-view .animate-text .animate-line.line1 span {
    -webkit-animation-delay: 0.2s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.2s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.2s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.2s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-title .animate-line.line2 span,
.in-view .animate-text .animate-line.line2 span {
    -webkit-animation-delay: 0.3s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.3s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.3s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.3s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-title .animate-line.line3 span,
.in-view .animate-text .animate-line.line3 span {
    -webkit-animation-delay: 0.4s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.4s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.4s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.4s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-title .animate-line.line4 span,
.in-view .animate-text .animate-line.line4 span {
    -webkit-animation-delay: 0.5s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.5s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.5s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.5s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-title .animate-line.line5 span,
.in-view .animate-text .animate-line.line5 span {
    -webkit-animation-delay: 0.6s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.6s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.6s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.6s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-text .animate-line {
    display: block;
    overflow-y: hidden;
}
.in-view .animate-text .animate-line span {
    display: block;
}

.in-view .animate-text .animate-line.line2 span {
    -webkit-animation-delay: 0.7s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.7s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.7s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.7s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-text .animate-line.line3 span {
    -webkit-animation-delay: 0.8s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.8s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.8s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.8s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}
.in-view .animate-text .animate-line.line4 span {
    -webkit-animation-delay: 0.9s;
    -webkit-animation-duration: 0.8s;
    -webkit-animation-name: slideInUp;
    -webkit-animation-fill-mode: both;
    -webkit-animation-timing-function: ease;
    -moz-animation-delay: 0.9s;
    -moz-animation-duration: 0.8s;
    -moz-animation-name: slideInUp;
    -moz-animation-fill-mode: both;
    -moz-animation-timing-function: ease;
    -o-animation-delay: 0.9s;
    -o-animation-duration: 0.8s;
    -o-animation-name: slideInUp;
    -o-animation-fill-mode: both;
    -o-animation-timing-function: ease;
    animation-delay: 0.9s;
    animation-duration: 0.8s;
    animation-name: slideInUp;
    animation-fill-mode: both;
    animation-timing-function: ease;
}

/* NEW STYLES */
.vertical-separator {
    height: 120px;
    width: 1px;
    background: rgba(0, 0, 0, 0.5);
    border: 0;
    box-shadow: 1px 0 0 rgb(255 255 255 / 5%);
    margin: 0 50px;
}
.horizontal-separator {
    height: 1px;
    width: 100%;
    background: rgba(0, 0, 0, 0.5);
    border: 0;
    box-shadow: 0 1px 0 rgb(255 255 255 / 5%);
    margin: 0 auto;
}
.mod.popular_capsules {
    padding: 0;
}
.mod.popular_capsules .col {
    width: 100%;
}

section:not(.horizontal-scroll) {
    /*min-height: 100vh;*/
    /*width: 100%;*/
    /*position: relative;*/
}
.pin-wrap {
    height: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 0 8% 10%;
}
.pin-wrap > * {
    min-width: auto;
    margin: 3vh 0 0;
    padding: 5vh 5vw;
}
.pin-wrap img {
    height: auto;
    width: 100%;
    object-fit: cover;
    opacity: 1;
    margin: 0 auto;
    border-radius: 10px;
}

.capsule_item {
    /*box-shadow: 8px 8px 24px 0 rgb(9 13 20 / 40%), -4px -4px 8px 0 rgb(224 224 255 / 4%), 0 1px 1px 0 rgb(9 13 20 / 40%);*/
    /*border: solid 1px rgba(245,247,250,0.06);*/
    /*background-image: linear-gradient(101deg,rgba(245,247,250,.12),rgba(245,247,250,.06) 52%,rgba(245,247,250,0));*/
    /*-webkit-backdrop-filter: blur(40px);*/
    /*border-radius: 30px;*/
    text-align: center;
    padding: 15px;
}
.capsule_item.first_capsule {
    background: none;
    box-shadow: none;
    border: none;
    padding: 30px 0 0;
}
.first_capsule h3 {
    font-size: 26px;
    line-height: 40px;
    color: #fff;
}
.first_capsule h3 .line2 {
    font-size: 30px;
    font-weight: 700;
}
.capsule_item h4 {
    font-size: 22px;
    margin-top: 20px;
    font-weight: 500;
}
.prep_partners {
    padding: 0 0 50px;
}
.prep_partners .logos {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 20px;
}
.prep_partners .col {
    width: 50%;
    padding: 10px;
    box-sizing: border-box;
}
.prep_partners .col:hover {
    opacity: 0.8;
}
.prep_partners .wrap.logos .in-view {
    width: 20%;
    height: 100px;
    box-shadow: 8px 8px 24px 0 rgb(9 13 20 / 40%),
    -4px -4px 8px 0 rgb(224 224 255 / 4%), 0 1px 1px 0 rgb(9 13 20 / 40%);
    border: solid 1px rgba(245, 247, 250, 0.06);
    background-image: linear-gradient(
            101deg,
            rgba(255, 255, 255, 1),
            rgba(255, 255, 255, 0.7) 52%,
            rgba(255, 255, 255, 0.5)
    );
    -webkit-backdrop-filter: blur(40px);
    border-radius: 15px;

    text-align: center;
    color: #000;
    cursor: pointer;
}
.prep_partners h3 {
    font-size: 26px;
    line-height: 40px;
    color: #fff;
}
.prep_partners h3 .line2 {
    font-size: 30px;
    font-weight: 700;
}

.prep_capsules .col {
    width: 50%;
    text-align: center;
}
.prep_capsules .logos {
    justify-content: left;
}
.prep_capsules .col img {
    border-radius: 10px;
}
.prep_capsules .col h4 {
    margin-top: 10px;
    font-size: 20px;
}

.mod.prep_explain .col {
    width: 100%;
    box-sizing: border-box;
}
.prep_explain .explain_title {
    margin-bottom: 30px;
    text-align: center;
}
.prep_explain .explain_title h3 .line1 {
    font-size: 40px;
    line-height: 50px;
}
.prep_explain .explain_title h3 .line2 {
    font-size: 25px;
    line-height: normal;
}
.prep_explain .steps {
    box-shadow: 0 2px 50px 0 rgb(0 0 0 / 20%);
    background: #333838;
    border-radius: 20px;
    padding: 20px;
}
.prep_explain .steps ul {
    list-style: none;
}
.prep_explain .steps ul li {
    font-weight: 400;
    font-size: 18px;
    line-height: 26px;
    margin: 0 0 15px;
    position: relative;
    padding-left: 30px;
}
.prep_explain .steps ul li span {
    position: absolute;
    left: 0;
    color: #e83500;
    font-weight: 600;
}

.mod.prep_footer {
    padding: 20px 0 40px;
    background: #1f2323;
    margin: 0;
    min-height: 120px;
}
.prep_footer a {
    color: #fff;
    text-decoration: none;
    padding-bottom: 10px;
    position: relative;
    font-weight: 300;
}
.prep_footer a:after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 0;
    width: 0;
    border-bottom: 1px solid #e83500;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -o-transition: all 0.4s ease;
    transition: all 0.4s ease;
}
.prep_footer a:hover {
    color: #e83500;
}
.prep_footer a:hover:after {
    width: 100%;
}
.mod.prep_footer p {
    margin: 0;
    font-size: 16px;
}
.prep_footer .copyright {
    justify-content: center;
    flex-direction: row;
    margin-bottom: 20px;
}
.prep_footer .links {
    display: flex;
    flex-direction: row;
    justify-content: center;
}
.prep_footer .links p {
    margin-left: 0;
    width: 100%;
    text-align: center;
}

.mod.terms_content {
    padding: 20px 0;
    margin: 0;
}
.mod.terms_content .col {
    width: 100%;
    flex-direction: row;
}
.terms_content h3 {
    text-align: left;
    margin-bottom: 0;
    font-weight: 500;
    font-size: 30px;
    line-height: 40px;
}
.terms_content p {
    font-size: 17px;
    line-height: 26px;
}
.terms_content h2 {
    margin-top: 30px;
    font-weight: 500;
    font-size: 20px;
    color: #fff;
    text-transform: uppercase;
}
.terms_content ul,
.terms_content ol {
    padding-left: 30px;
    font-size: 17px;
    line-height: 26px;
    margin: 10px 0;
}

.button-container {
    display: flex;
    /* flex-wrap: wrap; */
    gap: 10px;
    position: relative;
    z-index: 9999;
}

.button-container a {
    width: 130px;
    padding: 14px;
    border-radius: 10px;
    font-size: 16px;
    cursor: pointer;
    font-weight: 600;
    color: #fff;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
}

.try-for-free {
    background: #e83500 !important;
}
.store {
    background: #708090 !important;
}
.how-to-use {
    background: transparent;
}

.how-to-use,
.store,
.try-for-free {
    border: 1px solid;
}
@media only screen and (max-width: 900px) {
    .button-container {
        flex-wrap: wrap;
        padding-bottom: 10px;
        margin-top: 16px;
        justify-content: center;
    }
    .prep_partners .wrap.logos .in-view {
        width: 45%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .prep_partners iframe {
        height: 200px !important;
    }
    .intro h3 {
        display: flex;
    }
    .hero img.logo_image {
        width: 55%;
    }
}

.contact-wrapper {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    background: #2a1949;
    margin-top: 30px;
}

.contact-info,
.contact-form {
    border-radius: 10px;
    padding: 30px;
    max-width: 400px;
    width: 100%;
    box-sizing: border-box;
}

.contact-info h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.contact-info p {
    font-size: 1rem;
    margin-bottom: 20px;
}

.contact-info ul {
    list-style: none;
    padding: 0;
}

.contact-info ul li {
    font-size: 20px;
    margin: 10px 0;
    display: flex;
    align-items: center;
}

.contact-info ul li span {
    margin-right: 10px;
}

.contact-form form {
    display: flex;
    flex-direction: column;
}

.input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.input-group input,
.contact-form textarea {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #3e2b5e;
    color: #ffffff;
    font-size: 1rem;
}

.contact-form textarea {
    resize: none;
    height: 100px;
    margin-bottom: 20px;
}

.contact-form button {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ffffff;
    background-color: transparent;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
}

.contact-form button:hover {
    background-color: #ffffff;
    color: #2a1949;
}
.gpttxt {
    color: #fff !important;
}

.contact-info ul li a {
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #000;
}
