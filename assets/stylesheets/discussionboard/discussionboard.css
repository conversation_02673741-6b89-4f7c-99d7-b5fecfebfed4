.reset-btn {
  background: none;
  border: none;
}
.reset-btn:focus {
  outline: 0;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
p,
a,
button,
.btn,
h1,
h2,
h3,
h4,
label,
input,
span,
textarea,
li,
select {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
.gradientText {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  background: -moz-linear-gradient(#862AA5, #CE3E81);
  background: -webkit-gradient(#862AA5, #CE3E81);
  background: -o-linear-gradient(#862AA5, #CE3E81);
  background: -ms-linear-gradient(#862AA5, #CE3E81);
  background: linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block !important;
}
::placeholder {
  /* Chrome, <PERSON>fox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
.text-gradient {
  background: -webkit-linear-gradient(#862AA5, #CE3E81);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.text-radial-gradient {
  background: linear-gradient(to left, #8129a7 0%, #cb3e82 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.border-gradient {
  border: double 1px transparent;
  background-image: linear-gradient(white, white), linear-gradient(to right, #C13B87, #9B319A);
  background-origin: border-box;
  background-clip: content-box, border-box;
}
body {
  margin: 0 !important;
  padding: 0 !important;
}
a,
p,
h4,
button {
  font-family: 'Poppins', sans-serif !important;
}
.myflex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.admin-wrapper {
  min-height: 100vh;
}
.discussion-menu {
  margin-top: 1rem;
}
.discussion-menu.nav-pills .nav-item {
  margin-bottom: 1rem;
  text-align: center;
}
.discussion-menu.nav-pills .nav-item .nav-link {
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background: #ffffff !important;
  color: #9A309B;
}
.discussion-menu.nav-pills .nav-item .nav-link.active {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%) !important;
  color: #ffffff;
}
.discussion-menu.nav-pills .nav-item .nav-link:focus {
  color: #ffffff;
}
.line {
  position: absolute;
  left: 0;
  top: 1rem;
}
.line img {
  height: 140px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
}
.discussion-card {
  box-sizing: border-box;
  padding: 1rem;
  padding-top: 0.5rem;
  background: transparent;
  border: none;
  width: 85%;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .discussion-card {
    padding: 10px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  }
}
.discussion-card img {
  width: auto;
  max-width: 290px;
  max-height: 200px;
}
.discussion-card ol {
  background: transparent;
  margin: 0;
  border-radius: 0;
  padding: 5px 0;
  margin-bottom: 10px;
}
.discussion-card ol span {
  font-style: normal;
  font-weight: normal;
  font-size: 10px;
  color: rgba(68, 68, 68, 0.48);
}
.discussion-card ol li {
  padding: 0;
}
.discussion-card ol li.breadcrumb-item {
  padding: 0;
}
.discussion-card ol li a {
  color: #9A309B;
  font-size: 12px;
}
.discussion-card ol .breadcrumb-item + .breadcrumb-item::before {
  content: '>';
  font-size: 10px;
  padding: 3px;
  top: -1px;
  position: relative;
  color: #9A309B;
}
.q-question {
  color: #A73397;
}
.content > p {
  color: #444444;
}
.profile {
  display: flex;
  align-items: center;
}
.profile img {
  width: 28px;
  height: 28px;
}
.profile h4 {
  font-size: 12px;
  margin-left: 10px;
  font-weight: normal;
}
.profile p {
  font-size: 10px;
  color: rgba(68, 68, 68, 0.48);
  margin-left: 5px;
}
.card-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  margin-bottom: 1rem;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .card-actions {
    padding: 0.4rem 0;
  }
}
.card-actions button {
  background: transparent;
  outline: 0;
  border: none;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.48);
  white-space: nowrap;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .card-actions button {
    font-size: 10px;
  }
}
.card-actions button i {
  font-size: 14px;
  color: rgba(68, 68, 68, 0.48);
}
.card-actions button i.circle {
  border-radius: 50px;
  width: 24px;
  height: 24px;
  border: 1.25px solid rgba(68, 68, 68, 0.48);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 5px;
  margin-right: 5px;
}
.card-actions button i.circle.bord {
  border-color: #A73397;
}
.card-actions button.drop-menu {
  background: rgba(68, 68, 68, 0.2);
  justify-content: center;
  border-radius: 4px;
}
.card-actions button.drop-menu i {
  margin-right: 0;
}
.card-actions .dropdown-toggle::after {
  border: none;
}
.card-actions .dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  border: none;
}
.card-actions .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #9A309B;
}
.card-actions .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.card-actions .dropdown-menu > li:hover {
  background: rgba(0, 0, 0, 0.08);
}
.card-actions .dropdown-menu > li a {
  cursor: pointer;
  color: #ED2F2F;
}
.card-actions .dropdown-menu > li a i {
  color: #ED2F2F;
  -webkit-text-fill-color: #ED2F2F;
}
.card-actions .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.card-actions .dropdown-menu > li > a:hover {
  background: none;
}
.card-actions .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.card-actions button.answer {
  background: #FFFFFF;
  border: 1.5px solid #9A309B;
  box-sizing: border-box;
  border-radius: 5px;
  color: #9A309B;
  font-weight: normal;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-actions button.answer i {
  color: #9A309B;
  margin-right: 10px;
}
.divider {
  background: url('../../images/discussionboard/line-horiz.svg') center center no-repeat;
  height: 2px;
  margin: 0 auto;
}
.moderate-btns button {
  display: block;
  border: none;
  width: 75px;
  height: 75px;
  border-radius: 50px;
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  box-shadow: 0px 0px 10px rgba(95, 95, 95, 0.15);
  font-size: 9px;
  color: #ffffff;
  outline: 0;
  margin: 10px auto;
}
.moderate-btns button.delete {
  border: 1px solid #AA3493;
  background: #ffffff;
  color: #ED2F2F;
}
.moderate-btns button.delete i {
  color: #ED2F2F;
}
.moderate-btns button:focus {
  outline: none;
}
.moderate-btns button i {
  display: block;
  margin-bottom: 5px;
}
.pagination {
  display: flex;
  align-items: center;
}
.pagination li {
  margin-right: 10px;
}
.pagination li.disabled {
  background: none;
}
.pagination li.disabled a.actions {
  color: #b4b4b4;
}
.pagination li a {
  font-size: 18px;
  text-align: center;
  background: none;
  color: #000;
}
.pagination li a span {
  display: block;
}
.pagination li a.actions {
  font-size: 8px;
}
.pagination li a.actions span {
  font-size: 18px;
}
.pagination li a.page-link {
  border-radius: 0;
  border: none;
}
.pagination li a.page-link:hover {
  background: none;
}
.pagination li a.page-link:focus {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
}
.pagination li.active a {
  background: radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
  width: 33px;
  height: 33px;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.confirm,
.addtag {
  background: none;
  color: #9A309B;
  font-size: 10px;
  position: absolute;
  right: 5px;
  border: none;
  display: flex;
  align-items: center;
}
.confirm i,
.addtag i {
  font-size: 14px;
  color: #9A309B;
}
.breadcrumb {
  display: flex;
  align-items: center;
  min-height: 36px;
}
.breadcrumb > span {
  display: block;
  margin-right: 5px;
}
.breadcrumb select {
  background: #9A309B;
  color: #ffffff;
  border-radius: 4px;
  border: none;
  margin-right: 5px;
  font-size: 12px;
  height: 26px;
}
.sub-name {
  font-size: 12px;
  color: #949494;
}
.addDoubts {
  font-size: 12px;
  background: none;
  border: none;
  margin-right: 5px;
}
.addDoubts:focus {
  outline: none;
}
.addDoubts:hover {
  color: #A73397;
}
.dropup li a {
  font-size: 12px;
}
.dropup li a:hover {
  text-decoration: none;
}
.answer-card {
  min-height: 290px;
  width: 100%;
  padding: 1rem;
  border: none;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  background: transparent;
  border-radius: 10px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width: 320px) and (max-device-width: 568px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait), only screen and (min-device-width: 414px) and (max-device-width: 736px) and (orientation : portrait), only screen and (min-device-width: 375px) and (max-device-width: 667px) and (orientation : portrait) {
  .answer-card {
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  }
}
.answer-card .answer-textarea {
  min-height: 210px;
  border: none;
  border: 1px solid rgba(68, 68, 68, 0.1);
}
.answer-card .answer-textarea:focus {
  outline: 0;
}
.answer-card .answer-actions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
}
.answer-card .answer-actions button {
  margin-right: 15px;
  background: none;
  border: none;
}
.answer-card .answer-actions button i {
  color: #949494;
}
.answer-card .answer-actions button.post {
  width: 275px;
  background: none;
  text-transform: uppercase;
  color: #A73397;
  min-height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1.25px solid #A73397;
}
.answer-card .answer-actions button.cancels {
  width: 275px;
  background: #ffffff;
  text-transform: uppercase;
  min-height: 45px;
  border: 1px solid rgba(220, 53, 69, 0.8);
  color: rgba(220, 53, 69, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}
.postmodal {
  text-align: center;
  position: absolute;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin: 0 auto;
  width: 319px;
  height: 190px;
  margin-top: 4rem;
}
.postmodal.fade:not(.show) {
  opacity: 1;
}
.postmodal .modal-content {
  border: none;
}
.postmodal h4 {
  font-size: 14px;
  color: #9A309B;
  margin-top: 1rem;
  font-weight: normal;
}
.postmodal p {
  font-size: 10px;
  color: #949494;
}
.postmodal .modal-dialog {
  transition: none !important;
  transform: none !important;
  margin: 0;
}
.modalBackdrop {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.5;
  border-radius: 4px;
  display: none;
}
.btn-dismiss {
  margin-top: 1rem;
  background: #9A309B;
  color: #fff;
  font-size: 14px;
}
.circle_around {
  border: 3px solid #9A309B;
  border-radius: 50px;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.circle_around i {
  color: #9A309B;
  font-weight: bold;
}
.answer-box {
  background: rgba(148, 148, 148, 0.1);
  width: 90%;
  border: none;
  min-height: 40px;
  font-size: 14px;
}
.category-modal {
  background: radial-gradient(156.52% 3032.45% at -2.67% 0%, #8A2CA3 20.31%, #CA3D82 100%) !important;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px 10px 0px 0px;
  display: none;
  position: absolute;
  margin: 0 auto;
  margin-top: 4rem;
}
.category-modal.fade:not(.show) {
  opacity: 1;
}
.category-modal .modal-content {
  border: none;
  background: transparent;
  min-height: 200px;
  margin-top: 4rem;
}
.category-modal h4 {
  font-size: 14px;
  color: #ffffff;
  font-weight: normal;
}
.category-modal p {
  font-size: 10px;
  color: #ffffff;
  margin-top: 0.5rem;
}
.category-modal .modal-dialog {
  transition: none;
  transform: none;
  margin: 0;
}
.category-modal .modal-footer {
  border: none;
}
.category-modal .modal-footer .skip {
  background: none;
  border: none;
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  width: 100%;
  height: 50px;
  color: #6D2AB8;
  border-radius: 5px;
}
.category-modal .modal-footer .skip:focus {
  outline: 0;
}
.category-modal .filter {
  border-color: #ffffff !important;
}
.category-modal .filter i {
  color: #ffffff;
}
.solved {
  position: absolute;
  right: 20px;
  color: #379B5B;
  background: #D4F1DF;
  border-radius: 50px;
  width: 65px;
  font-size: 14px;
  text-align: center;
  display: block;
}
.userAnswer {
  background: rgba(148, 148, 148, 0.1);
  margin-left: 10px;
  margin-top: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
}
.userAnswer img {
  max-width: 350px;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .userAnswer img {
    max-width: 290px;
  }
}
.userAnswer input {
  background: transparent;
}
.answerContent .card-actions {
  padding: 0 0.5rem;
}
.answerContent .card-actions button {
  font-size: 10px;
}
.answer-head {
  font-size: 14px;
  text-transform: uppercase;
  color: #949494;
  margin-bottom: 1rem;
}
#mobileque-modal .category-modal {
  display: block;
  position: fixed;
}
#mobileque-modal .category-modal .modal.fade .modal-dialog {
  transition: none !important;
}
#mobileque-modal .modalBackdrop {
  position: fixed;
}
#mobileque-modal .postmodal {
  position: fixed;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}
.textOverflow {
  max-width: 98%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.typeahead.dropdown-menu {
  max-width: 349px;
  min-width: 349px;
}
.typeahead.dropdown-menu li a {
  max-width: 98%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.taggs {
  display: block;
  margin-right: 10px;
}
.ans-modal-img-remove {
  color: #ED2F2F;
  border: #ED2F2F;
  border: 1px solid #ED2F2F;
}
.searchbar .typeahead.dropdown-menu {
  max-width: 95%;
  min-width: 95%;
}
.admin-wrapper .container-fluid {
  margin: 0 4rem;
}
select {
  font-size: 16px !important;
}
.answer-drop .drop-menu {
  background: none;
  border: none;
  color: #A73397;
}
.answer-drop .drop-menu:focus {
  outline: none;
}
.answer-drop .dropleft .dropdown-toggle::before {
  display: none;
}
.answer-drop .dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  border: none;
}
.answer-drop .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #9A309B;
}
.answer-drop .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.answer-drop .dropdown-menu > li:hover {
  background: rgba(0, 0, 0, 0.08);
}
.answer-drop .dropdown-menu > li a {
  cursor: pointer;
  color: #ED2F2F;
}
.answer-drop .dropdown-menu > li a i {
  color: #ED2F2F;
  -webkit-text-fill-color: #ED2F2F;
}
.answer-drop .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.answer-drop .dropdown-menu > li > a:hover {
  background: none;
}
.answer-drop .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.answer-drop .dropdown,
.answer-drop .dropleft,
.answer-drop .dropright,
.answer-drop .dropup {
  top: 3px;
}
.addtodoubts {
  position: absolute;
  display: flex;
  align-items: center;
  right: 10px;
}
.addtodoubts button {
  color: rgba(68, 68, 68, 0.48);
}
.addtodoubts .drop-menu {
  background: none;
  border: none;
  color: #A73397;
}
.addtodoubts .drop-menu:focus {
  outline: none;
}
.addtodoubts .dropleft .dropdown-toggle::before {
  display: none;
}
.addtodoubts .dropdown-menu {
  background: #ffffff;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  border: none;
}
.addtodoubts .dropdown-menu i {
  font-size: 18px;
  margin-right: 10px;
  color: #9A309B;
}
.addtodoubts .dropdown-menu > li {
  padding: 0.3rem 0.5rem;
}
.addtodoubts .dropdown-menu > li:hover {
  background: rgba(0, 0, 0, 0.08);
}
.addtodoubts .dropdown-menu > li a {
  cursor: pointer;
  color: #ED2F2F;
}
.addtodoubts .dropdown-menu > li a i {
  color: #ED2F2F;
  -webkit-text-fill-color: #ED2F2F;
}
.addtodoubts .dropdown-menu > li > a {
  display: flex;
  align-items: center;
}
.addtodoubts .dropdown-menu > li > a:hover {
  background: none;
}
.addtodoubts .dropdown-menu > li.notcorrect > a i {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.addtodoubts .dropdown,
.addtodoubts .dropleft,
.addtodoubts .dropright,
.addtodoubts .dropup {
  top: 3px;
}
.flex-action {
  display: flex;
  align-items: center;
  min-height: 24px;
}
.flex-action #share-button {
  border-left: 0.5px solid rgba(68, 68, 68, 0.48);
}
#cke_questionText {
  border-radius: 10px;
  border: none;
}
@media only screen and (min-device-width : 320px) and (max-device-width : 480px), only screen and (min-device-width : 768px) and (max-device-width : 1024px) and (orientation : portrait) {
  .reset-padding {
    padding: 0;
  }
}
.tabs {
  margin: 0 auto;
}
#myDoubtsTab > p,
#myAnswerTab > p,
#alldoubts > p {
  margin-top: 1rem;
  padding: 5px;
}
.reset-app #myDoubtsTab > p,
#myAnswerTab > p,
#alldoubts > p {
  margin-top: 7rem;
  padding: 5px;
}
