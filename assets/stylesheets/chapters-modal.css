@import url("https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700");
@import url("https://fonts.googleapis.com/css?family=Abril+Fatface");
@import url("https://fonts.googleapis.com/css?family=Roboto:300,400,500,700");
.chapters-modal .modal-body {
  padding: 0;
  color: #444444;
}

.book-details-aside {
  font-family: 'Montserrat', sans-serif;
  height: 100%;
  background: url("../images/wonderslate/pattern.png");
  background-repeat: no-repeat;
  background-position: right 24px top 24px;
  padding: 24px;
  border-right: 1px solid rgba(68, 68, 68, 0.2);
}

.book-image-wrapper {
  width: 160px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  margin-bottom: 8px;
}
.book-image-wrapper img {
  border-radius: 4px;
}

.book-name {
  max-width: 160px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  color: #444444;
  line-height: 19px;
  font-size: 14px;
  letter-spacing: 0.01em;
  margin: 0;
  margin-bottom: 8px;
}

.publisher-name {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 300;
  line-height: 16px;
  font-size: 12px;
  letter-spacing: 0.01em;
  color: rgba(68, 68, 68, 0.74);
  margin: 0;
  margin-bottom: 8px;
}
.publisher-name span {
  color: #444444;
  font-weight: 500;
  line-height: 19px;
  font-size: 12px;
  letter-spacing: 0.01em;
  margin: 0;
}

.chapter-book-price-details {
  padding: 0;
}

.chapter-book {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 500;
  line-height: 11px;
  font-size: 8px;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  margin: 0;
}

.offer-price {
  display: inline-block;
  font-size: 20px;
  font-weight: 500;
  color: #AE2B24;
  letter-spacing: 0.01em;
  margin-right: 4px;
}
.offer-price i {
  font-size: 18px;
}

.original-price {
  display: inline-block;
  font-size: 16px;
  font-weight: 300;
  color: #909090;
  letter-spacing: 0.01em;
  text-decoration: line-through;
  margin: 0;
}

.offer-chapters {
  float: left;
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: 24px;
}
.offer-chapters li {
  font-weight: 500;
  line-height: normal;
  font-size: 14px;
  margin-bottom: 16px;
}
.offer-chapters li .fa-tag {
  color: #AE2B24;
  margin-right: 8px;
}

.book-buy {
  clear: both;
  float: none;
  width: 160px;
  margin: 0 auto;
  text-align: center;
  position: fixed;
  bottom: 24px;
  left: 11%;
}

.complete-book-legend {
  font-weight: 300;
  line-height: normal;
  font-size: 14px;
  text-align: center;
}

.buy-complete-book-btn {
  display: block;
  font-weight: 500;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.01em;
  color: #F05A2A;
  padding: 11px;
  border: 0.5px solid rgba(68, 68, 68, 0.54);
  box-sizing: border-box;
  border-radius: 4px;
}
.buy-complete-book-btn:focus {
  text-decoration: none;
  color: #F05A2A;
}
.buy-complete-book-btn:hover {
  text-decoration: none;
  color: #F05A2A;
}
.buy-complete-book-btn:active {
  text-decoration: none;
  color: #F05A2A;
}

.chapter-selection-table {
  display: block;
  min-height: 488px;
  max-height: 488px;
  overflow: hidden;
  overflow-y: auto;
}
.chapter-selection-table tbody {
  display: block;
  width: 100%;
}
.chapter-selection-table tbody tr {
  display: block;
  width: 100%;
}

.chapter-selection-table > tbody > tr > td {
  padding: 0;
}

.chapter-details-aside {
  height: 100%;
  padding: 16px;
}
.chapter-details-aside .chapter-name {
  display: block;
  position: relative;
  color: #444;
  font-size: 14px;
  font-weight: 500;
  margin-top: 24px;
  border-top: 0;
  border-bottom: 0;
  cursor: pointer;
}
.chapter-details-aside .chapter-name:hover {
  color: #F05A2A;
}
.chapter-details-aside .chapter-name.active {
  color: #F05A2A;
}
.chapter-details-aside .chapter-name label {
  cursor: pointer;
  font-size: 14px;
  color: #444;
  font-weight: 300;
}
.chapter-details-aside .chapter-name label:hover {
  color: #444;
}
.chapter-details-aside .chapter-name label.active {
  font-weight: 500;
  color: #444;
}
.chapter-details-aside input[type=checkbox] {
  position: absolute;
  opacity: 0;
}
.chapter-details-aside label {
  cursor: pointer;
  font-size: 14px;
  color: #444;
}
.chapter-details-aside label:hover {
  color: #F05A2A;
}
.chapter-details-aside label.active {
  font-weight: bold;
  color: #F05A2A;
  font-weight: 500;
}
.chapter-details-aside .checkmark {
  position: absolute;
  top: 0;
  right: 0;
  height: 18px;
  width: 18px;
  background-color: transparent;
  border: 2px solid #888;
}
.chapter-details-aside .chapter-name:hover input[type="checkbox"] ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
}
.chapter-details-aside .chapter-name input[type="checkbox"]:checked ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
}
.chapter-details-aside .chapter-name::after {
  content: '';
  position: absolute;
  display: none;
}
.chapter-details-aside .chapter-name input[type="checkbox"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block;
}
.chapter-details-aside .checkmark::after {
  left: 1px;
  top: 2px;
  width: 12px;
  height: 7px;
  border: solid #F05A2A;
  border-width: 3px 3px 0 0;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}

.select-chapters-legend {
  font-family: 'Abril Fatface', cursive;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 22px;
  letter-spacing: 0.04em;
  color: #444444;
  padding-bottom: 16px;
  margin: 0;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}

.book-unlock {
  float: none;
  width: 160px;
  margin: 0 auto;
  text-align: center;
  position: fixed;
  bottom: 24px;
  right: 11%;
}

.unlock-complete-book-btn {
  color: #fff;
  font-family: 'Roboto', sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.01em;
  padding: 11px;
  display: block;
  font-weight: 500;
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  border-radius: 4px;
}
.unlock-complete-book-btn:hover {
  text-decoration: none;
  color: #fff;
}
.unlock-complete-book-btn:focus {
  text-decoration: none;
  color: #fff;
}
.unlock-complete-book-btn:active {
  text-decoration: none;
  color: #fff;
}

@media screen and (min-width: 678px) {
  .chapters-modal .modal-dialog {
    width: 640px;
  }
  .chapters-modal .modal-body {
    height: 685px;
  }
}

/*# sourceMappingURL=chapters-modal.css.map */
