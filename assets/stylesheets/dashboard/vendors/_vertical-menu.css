/*
 = Vertical Menu Styles
 */
.vertical-compact-menu .content {
  margin-left: 130px; }

.vertical-compact-menu .navbar .navbar-header {
  float: left;
  width: 130px; }

.vertical-compact-menu .navbar .navbar-container {
  margin-left: 130px; }

.vertical-compact-menu .navbar.navbar-brand-center .navbar-container {
  margin-left: 0; }

.vertical-compact-menu .navbar.navbar-brand-center .navbar-header {
  float: left;
  width: auto; }

.vertical-compact-menu .main-menu, .vertical-compact-menu .vertical-overlay-menu.menu-hide .main-menu, .vertical-overlay-menu.menu-hide .vertical-compact-menu .main-menu {
  width: 130px;
  left: 0; }
  .vertical-compact-menu .main-menu .main-menu-header .user-content .media-left {
    display: block;
    text-align: center;
    padding: 0; }
    .vertical-compact-menu .main-menu .main-menu-header .user-content .media-left .avatar-md {
      width: 60px;
      text-align: center; }
  .vertical-compact-menu .main-menu .main-menu-header .user-content .media-body {
    display: block;
    text-align: center;
    margin: 0;
    padding: 0;
    width: auto;
    margin-top: 0.5rem; }
  .vertical-compact-menu .main-menu .main-menu-header .user-content .media-right {
    display: none; }
  .vertical-compact-menu .main-menu .main-menu-content > span.menu-title, .vertical-compact-menu .main-menu .main-menu-content a.menu-title {
    right: -260px;
    width: 260px;
    font-weight: 600;
    color: #fff;
    text-transform: uppercase;
    text-align: left;
    background-color: #1E9FF2;
    border-color: #1E9FF2;
    padding: 14px 20px; }
  .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content {
    left: 130px;
    width: 260px;
    transition: visibility .25s,opacity .25s;
    box-shadow: 25px 5px 75px 2px rgba(64, 70, 74, 0.2);
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    border-left: 1px solid rgba(0, 0, 0, 0.02); }
    .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li {
      white-space: nowrap;
      position: relative; }
      .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li a {
        display: block;
        padding: 8px 20px 8px 20px;
        transition: all 0.2s ease; }
      .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li.has-sub > a:not(.mm-next):after {
        content: "\f112";
        font-family: 'LineAwesome';
        font-size: 1rem;
        display: inline-block;
        position: absolute;
        right: 20px;
        top: 14px;
        transform: rotate(0deg);
        transition: -webkit-transform 0.2s ease-in-out; }
      .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li.has-sub .has-sub > a:not(.mm-next):after {
        top: 8px; }
      .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li.open > a:not(.mm-next):after {
        transform: rotate(90deg); }
      .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li:hover > a, .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li.hover > a {
        transform: translateX(4px); }
        .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li:hover > a > a, .vertical-compact-menu .main-menu .main-menu-content > ul.menu-content li.hover > a > a {
          transform: translateX(-4px); }
  .vertical-compact-menu .main-menu .navigation {
    overflow: visible; }
    .vertical-compact-menu .main-menu .navigation > li.navigation-header {
      padding: 12px 20px;
      text-align: center;
      font-weight: 800; }
      .vertical-compact-menu .main-menu .navigation > li.navigation-header .ft-minus {
        visibility: hidden;
        display: none;
        opacity: 0; }
    .vertical-compact-menu .main-menu .navigation > li > a {
      width: auto;
      padding: 0.5rem 1rem;
      text-align: center;
      transition: visibility .25s,opacity .25s; }
      .vertical-compact-menu .main-menu .navigation > li > a > i {
        display: block;
        width: 100%;
        margin: 0 auto;
        font-size: 2.2rem;
        margin: 0.2rem 0 0.8rem 0;
        text-align: center; }
        .vertical-compact-menu .main-menu .navigation > li > a > i:before {
          transition: 200ms ease all; }
      .vertical-compact-menu .main-menu .navigation > li > a > span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle; }
    .vertical-compact-menu .main-menu .navigation > li.hover > ul {
      display: none; }
    .vertical-compact-menu .main-menu .navigation > li.hover > a > span {
      visibility: visible;
      opacity: 1; }
  .vertical-compact-menu .main-menu .mTSWrapper {
    overflow: visible; }

.vertical-compact-menu.menu-open .main-menu {
  opacity: 1;
  transition: transform .25s, opacity .25s,top .35s,height .35s;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000; }

.vertical-compact-menu.menu-open .content, .vertical-compact-menu.menu-open .footer {
  margin-left: 130px; }

.vertical-compact-menu.menu-open .navbar-brand-center .content, .vertical-compact-menu.menu-open .navbar-brand-center .footer {
  margin-left: 0; }

.vertical-compact-menu.menu-open.boxed-layout .main-menu {
  transform: translate3d(130px, 0, 0); }

.vertical-compact-menu.menu-hide .content {
  margin-left: 0; }

.vertical-compact-menu.menu-hide .main-menu {
  opacity: 0;
  transition: transform .25s, opacity .25s,top .35s,height .35s;
  transform: translate3d(-130px, 0, 0);
  backface-visibility: hidden;
  perspective: 1000; }

.vertical-compact-menu.menu-hide .content, .vertical-compact-menu.menu-hide .footer {
  margin-left: 0; }

.vertical-compact-menu.menu-hide .navbar-brand-center .content, .vertical-compact-menu.menu-hide .navbar-brand-center .footer {
  margin-left: 0; }

.vertical-compact-menu.menu-flipped .content {
  margin: 0;
  margin-right: 130px; }

.vertical-compact-menu.menu-flipped .main-menu {
  right: 0;
  left: inherit; }
  .vertical-compact-menu.menu-flipped .main-menu ul.menu-content {
    right: 130px;
    left: inherit; }
  .vertical-compact-menu.menu-flipped .main-menu .navigation > li > ul {
    right: 130px;
    left: inherit; }

.vertical-compact-menu.menu-flipped .navbar .navbar-header {
  float: right; }

.vertical-compact-menu.menu-flipped .navbar .navbar-container {
  margin: 0;
  margin-right: 130px; }

.vertical-compact-menu.menu-flipped.menu-open.boxed-layout .main-menu {
  transform: translate3d(-130px, 0, 0); }

.vertical-compact-menu.menu-flipped.menu-hide .content {
  margin: 0;
  margin-right: 0; }

.vertical-compact-menu.menu-flipped.menu-hide .main-menu {
  transform: translate3d(130px, 0, 0);
  backface-visibility: hidden;
  perspective: 1000; }

@media (min-width: 992px) {
  body.vertical-compact-menu .main-menu, body.vertical-compact-menu .vertical-overlay-menu.menu-hide .main-menu, .vertical-overlay-menu.menu-hide body.vertical-compact-menu .main-menu {
    width: 130px; }
  body.vertical-compact-menu .navbar .navbar-header {
    width: 130px; }
  body.vertical-compact-menu .content, body.vertical-compact-menu .footer {
    margin-left: 130px; } }

@media (max-width: 991.98px) {
  body.vertical-compact-menu .navbar .navbar-header {
    width: 0; }
  body.vertical-compact-menu .content, body.vertical-compact-menu .footer {
    margin-left: 0; } }

@media (max-width: 767.98px) {
  body.vertical-compact-menu .content,
  body.vertical-compact-menu .footer {
    margin-left: 0; } }

[data-textdirection="rtl"] body.vertical-layout.vertical-compact-menu .main-menu .navigation > li > a > span {
  display: none; }

/*=========================================================================================
	File Name: vertical-overlay-menu.scss
	Description: A overlay style vertical menu with show and hide support. It support 
	light & dark version, filpped layout, right side icons, native scroll and borders menu 
	item seperation.
	----------------------------------------------------------------------------------------
	Item Name: Modern Admin - Clean Bootstrap 4 Dashboard HTML Template
	Version: 1.0
	Author: PIXINVENT
	Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/
.vertical-overlay-menu .content {
  margin-left: 0; }

.vertical-overlay-menu .navbar .navbar-header {
  float: left;
  width: 260px; }

.vertical-overlay-menu .navbar.navbar-brand-center .navbar-container {
  margin-left: 0; }

.vertical-overlay-menu .navbar.navbar-brand-center .navbar-header {
  float: left;
  width: auto; }

.vertical-overlay-menu .main-menu, .vertical-overlay-menu.menu-hide .main-menu {
  opacity: 0;
  transform: translate3d(0, 0, 0);
  transition: width .25s,opacity .25s,transform .25s;
  width: 260px;
  left: -260px; }
  .vertical-overlay-menu .main-menu .navigation .navigation-header .ft-minus {
    display: none; }
  .vertical-overlay-menu .main-menu .navigation > li > a > i {
    font-size: 1.4rem;
    margin-right: 12px;
    float: left; }
    .vertical-overlay-menu .main-menu .navigation > li > a > i:before {
      transition: 200ms ease all; }
  .vertical-overlay-menu .main-menu .navigation li.has-sub > a:not(.mm-next):after, .vertical-overlay-menu.menu-hide .main-menu .navigation li.has-sub > a:not(.mm-next):after {
    content: "\f112";
    font-family: 'LineAwesome';
    font-size: 1rem;
    display: inline-block;
    position: absolute;
    right: 20px;
    top: 14px;
    transform: rotate(0deg);
    transition: -webkit-transform 0.2s ease-in-out; }
  .vertical-overlay-menu .main-menu .navigation li.has-sub .has-sub > a:not(.mm-next):after, .vertical-overlay-menu.menu-hide .main-menu .navigation li.has-sub .has-sub > a:not(.mm-next):after {
    top: 8px; }
  .vertical-overlay-menu .main-menu .navigation li.open > a:not(.mm-next):after, .vertical-overlay-menu.menu-hide .main-menu .navigation li.open > a:not(.mm-next):after {
    transform: rotate(90deg); }
  .vertical-overlay-menu .main-menu .main-menu-footer {
    bottom: 55px; }
  .vertical-overlay-menu .main-menu .main-menu-footer {
    width: 260px; }

.vertical-overlay-menu.menu-open .main-menu {
  opacity: 1;
  transform: translate3d(260px, 0, 0);
  transition: width .25s,opacity .25s,transform .25s; }

.vertical-overlay-menu.menu-flipped .main-menu {
  right: -260px;
  left: inherit; }

.vertical-overlay-menu.menu-flipped .navbar .navbar-container {
  margin: 0;
  margin-right: 260px; }

.vertical-overlay-menu.menu-flipped .navbar .navbar-header {
  float: right; }

.vertical-overlay-menu.menu-flipped.menu-open .main-menu {
  transform: translate3d(-260px, 0, 0); }

@media (max-width: 991.98px) {
  .vertical-overlay-menu .main-menu .main-menu-footer {
    bottom: 0px; } }
