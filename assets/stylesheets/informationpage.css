div#contents .info-list.mb-4.pb-4 {
  box-sizing: border-box;
  padding: 1rem;
  background: transparent;
  border: none;
  width: 100%;
  box-shadow: 0 0 10px #0000001a;
  border-radius: 10px;
}
.information #contents .info-description p {
  padding-top: 10px;
}
a.show-info-details {
  font-size: 12px;
  curser: pointer;
  text-decoration: none !important;
}
.showmoretab {
  box-sizing: border-box;
  padding: 1rem;
  background: transparent;
  border: none;
  width: 100%;
  box-shadow: 0 0 10px #0000001a;
  border-radius: 10px;
}
.information #contents .info-title {
  color: #212121;
  margin: 0 !important;
  padding-top: 7%;
}
.information #contents .info-description p {
  padding-top: 10px;
}
.col-12.d-flex.align-items-center a h5 {
  text-decoration: none !important;
}
.col-12.d-flex.align-items-center a {
  text-decoration: none !important;
}
p.answers b {
  padding-right: 5px;
  color: #bababa;
  font-style: italic;
  font-weight: 300;
  text-transform: none;
}
.col-12 p b {
  padding-right: 5px;
  color: #bababa;
  font-style: italic;
  font-weight: 300;
  text-transform: none;
}
.showmoretab .col-12 a {
  font-size: 13px;
  padding-right: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #007bff;
  text-decoration: none;
  word-break: break-word;
}
.col.text-right.next-info-link p small,
.col.text-left.prev-info-link p small {
  padding-right: 10px;
  color: #bababa;
  font-style: italic;
  font-weight: 300;
  text-transform: none;
  font-size: 12px;
}
.col.text-right.next-info-link a,
.col.text-left.prev-info-link a {
  color: #007bff;
}
.col.text-right.next-info-link a:hover,
.col.text-left.prev-info-link a:hover {
  color: #007bff;
}
.showmoretab a:hover,
.showmoretab a {
  color: #007bff;
}
.col.text-right.next-info-link a:after {
  content: "\2192";
}
.col.text-left.prev-info-link a:before {
  content: "\2190";
}
