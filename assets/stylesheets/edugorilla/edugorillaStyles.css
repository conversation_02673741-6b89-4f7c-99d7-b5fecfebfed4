.edugorilla .logo-wrapper a {
  width: 80px;
}
.edugorilla .white-logo-anchor-white img {
  width: 40px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .edugorilla .white-logo-anchor-white img {
    width: 30px;
  }
}
.edugorilla .manage-logo-big-menu-arihant img {
  width: 70%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .edugorilla .manage-logo-big-menu-arihant img {
    width: 50%;
  }
}
.edugorilla .menu-wrp-all-users-com ul li a {
  color: #212121 !important;
}
.edugorilla .categories-section-disc-title-pera {
  color: #212121;
}
.edugorilla a {
  font-weight: normal;
}
.edugorilla p {
  line-height: normal;
}
.edugorilla .global-search input[type="text"] {
  padding-left: 10px;
  padding-right: 40px;
  z-index: 3;
  border: 1px solid #d0d0d0 !important;
  border-radius: 20px;
  min-height: 40px;
}
@media (max-width: 320px) {
  .edugorilla .global-search input[type="text"] {
    padding-right: 20px;
  }
}
.edugorilla .global-search button {
  width: auto;
  height: 33px;
  margin-left: -38px;
  padding: 4px;
  position: relative;
  z-index: 10;
  color: #C9302C !important;
}
.edugorilla .global-search button .material-icons {
  line-height: normal;
}
.edugorilla .add-tabs {
  top: 75px;
}
.edugorilla #allAddButton {
  display: none !important;
}
.edugorilla .user-menu-wrp img.menu-dots-img {
  filter: invert(1) contrast(0.1);
}
.edugorilla.hasScrolled .main-menu-wrp {
  position: relative !important;
}
.edugorilla.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 0;
}
.edugorilla.hasScrolled .bookTemplate .export-notes {
  top: 52px;
}
.edugorilla.hasScrolled .user-menu-wrp .menu-actives a.menu-dots-img-wrp {
  position: fixed;
}
.edugorilla .bookTemplate .content-wrapper {
  height: calc(100vh - 50px);
}
@media (max-width: 767px) {
  .edugorilla .bookTemplate.book_preview .content-wrapper .read-content.col-md-12 .price-wrapper {
    display: none !important;
  }
  .edugorilla .bookTemplate.book_preview .content-wrapper #book-read-material {
    padding-bottom: 70px;
  }
  .edugorilla .bookTemplate.book_preview .content-wrapper #book-sidebar {
    height: calc(100vh - 70px);
    padding-bottom: 0;
  }
  .edugorilla .bookTemplate.book_preview .content-wrapper .price-wrapper {
    padding: 0.5rem 0;
    position: fixed;
    width: 100%;
    z-index: 991;
  }
  .edugorilla .bookTemplate.book_preview .content-wrapper .price-wrapper .section-btns {
    padding: 0.5rem 0 0;
    background: #FFFFFF;
  }
  .edugorilla .bookTemplate.book_preview .content-wrapper .price-wrapper .preview-book-btns {
    margin-left: 0;
  }
}
.edugorilla .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 100px;
}
.edugorilla .bookTemplate .export-notes {
  top: 130px;
}
.edugorilla .bookTemplate .mobChapname #chapters-toggle.left i {
  transform: rotate(0deg);
}
.edugorilla .bookTemplate .preview-book-btns .btn-book-buy {
  background: #C9302C !important;
}
.edugorilla .bookTemplate .ChapterHeader .bookTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.edugorilla .web-mcq .sub-header {
  top: 90px;
  padding-top: 10px;
}
.edugorilla .bg-wsTheme {
  background: #C9302C !important;
}
.edugorilla #question-block .question-wrapper {
  margin-top: 2rem;
}
.edugorilla #question-block .question-wrapper img {
  max-width: 100%;
}
.edugorilla .tab-wrappers {
  top: 190px;
}
.edugorilla .web-mcq .result-menu {
  top: 75px;
}
.edugorilla #quizQuestionSection {
  padding-bottom: 50px;
}
.edugorilla .book_details_info #filelabel1,
.edugorilla .book_details_info #filelabel2 {
  right: 0;
  left: 0;
}
.edugorilla #bookcover .smallText {
  justify-content: center;
}
.edugorilla .orders .payment-details > div:last-child p .rupees {
  display: none;
}
.edugorilla .users-orders > p {
  padding-right: 15px;
  padding-left: 15px;
}
.edugorilla .user_profile .tab-content .jumbotron form .media .continue,
.edugorilla .btn-starts,
.edugorilla #answer-block .button-wrapper a,
.edugorilla #answer-block .button-wrapper a:hover {
  background: #C9302C !important;
}
.edugorilla .test-gen-box-main .test-gen-box .btn-info {
  background: #C9302C !important;
  border-color: #C9302C !important;
}
.edugorilla .test-gen-box-main .test-gen-box .btn-info:active:focus {
  box-shadow: 0 0 0 0.2rem rgba(239, 114, 21, 0.5) !important;
}
.edugorilla .dropdown-menu a:active,
.edugorilla .dropdown-menu span:active,
.edugorilla .dropdown-menu li:active {
  background-color: #fce5d4;
}
.edugorilla .all-container .container-wrapper .media .quiz-practice-btn,
.edugorilla .all-container .container-wrapper .media .showRank {
  text-transform: uppercase;
  color: #C9302C;
  padding: 0;
  display: inherit;
  border-radius: 0;
}
.edugorilla .all-container .container-wrapper .media .showRank {
  padding-left: 10px;
  margin-left: 10px;
  border-left: 1px solid #212121;
}
.edugorilla .all-container .container-wrapper .d-flex p.testStarts {
  margin: 0;
  top: 0;
  font-size: 12px;
  flex: none;
}
.edugorilla .play::before {
  left: -20px;
}
.edugorilla .backfromgenerator {
  margin-top: 20px;
}
.edugorilla .backfromgenerator i {
  color: #C9302C;
}
.edugorilla #htmlContent {
  margin-top: 2rem;
}
.edugorilla .purchase-details-container .purchase-heading {
  background: none !important;
  -webkit-text-fill-color: unset !important;
}
.edugorilla .purchase-details-container .browse-purchase-book a.learn-btn {
  background: #C9302C !important;
  color: #FFFFFF;
}
.edugorilla .web-mcq .mt-fixed {
  margin-top: 3.5rem !important;
  padding-top: 0;
}
.edugorilla.custom-fix .bookTemplate .shadowHeader {
  position: fixed;
  top: 0;
}
.edugorilla .start-test .header p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  padding: 0 15px;
}
.edugorilla #quizQuestionSection .result-menu > div h2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding: 0 10px;
}
.edugorilla .mt-fixed #resourceTitle {
  margin-top: 50px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  padding: 0 15px;
}
.edugorilla .notes-creation-header {
  z-index: 1;
}
.edugorilla .index-page .main-menu-wrp {
  position: relative;
}
.edugorilla .index-page .header-menu-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  text-align: center;
  width: 100%;
}
.edugorilla .index-page .this-is-a-web-view-slider {
  display: block;
  overflow: hidden;
}
@media (max-width: 991px) {
  .edugorilla .index-page .this-is-a-web-view-slider {
    display: none;
  }
}
.edugorilla .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  min-height: 80vh;
  background-size: 100% auto !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}
@media (min-width: 1600px) {
  .edugorilla .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
    background-position: left top !important;
  }
}
.edugorilla .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
  width: 100%;
  max-width: 100%;
  min-height: auto;
  max-height: inherit;
}
.edugorilla .index-page .this-is-a-responsive-view-slider {
  display: none;
}
@media (max-width: 991px) {
  .edugorilla .index-page .this-is-a-responsive-view-slider {
    display: block;
  }
}
.edugorilla .index-page .this-is-a-responsive-view-slider img {
  width: 100%;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .edugorilla .index-page .this-is-a-responsive-view-slider img {
    height: 350px;
    object-fit: cover;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .edugorilla .index-page .this-is-a-responsive-view-slider img {
    height: 190px;
    object-fit: cover;
  }
}
.edugorilla .index-page .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #fdf7f7;
}
.edugorilla .index-page .carousel-indicators li.active {
  background: #C9302C;
}
.edugorilla .ebooks .ebooks_filter {
  width: 100%;
}
.mozilla .arihant .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  background-attachment: unset !important;
}
#guestUser,
#loginOpen,
#signup,
#forgotPasswordmodal,
#shareContentModal,
#deleteBook,
#change-password-modal,
#currentPomodoro,
#pomodoroSessionCompletion,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal,
#bookQueueModal,
#test-gen-modal,
#PlayAudiOnlyModal,
#quizModal {
  z-index: 9992;
}
.mobile-footer-nav {
  background: #C9302C;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -webkit-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -moz-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px 20px 0 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.mobile-footer-nav.hide-menus {
  bottom: -75px;
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
}
.mobile-footer-nav a {
  text-decoration: none;
  flex-wrap: wrap;
  text-align: center;
  color: #FFFFFF;
}
.mobile-footer-nav a:focus {
  text-decoration: none;
}
.mobile-footer-nav a:visited {
  text-decoration: none;
}
.mobile-footer-nav a img {
  margin: 0 auto;
  width: 22px;
}
.mobile-footer-nav a p {
  width: 100%;
  font-size: 13px;
}
.mobile-footer-nav i {
  color: white;
}
.mobile-footer-nav .active-menu {
  opacity: 1;
}
.mobile-footer-nav .common-footer-nav {
  opacity: 0.8;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
#test-gen-modal .overlay-testgen-book .book-selected {
  width: 40px;
  height: 40px;
}
#quizQuestionSection #submit-test .modal-footer button {
  font-family: 'Poppins', sans-serif !important;
}
#quizQuestionSection #submit-test .modal-footer .submit {
  background: #C9302C;
  color: #FFFFFF;
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: portrait), only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .arihant #book-read-material #content-data-all {
    padding: 0;
  }
  .arihant #book-read-material #content-data-all > .container {
    padding: 0;
  }
  .arihant .all-container .container-wrapper {
    margin-top: 0;
    border: none;
    border-bottom: 1px solid #ededed;
    box-shadow: none;
    border-radius: 0;
  }
  .arihant .all-container .container-wrapper .media i {
    margin: 0 1rem;
  }
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .arihant .bookTemplate .export-notes {
    top: 51px !important;
  }
}
@media only screen and (max-width: 767px) {
  .arihant h2#expiry-date {
    margin-top: 0 !important;
  }
}
.footer .download-app-links .android-app-link {
  border: 1px solid #ddd;
  padding: 2px 0 5px;
  border-radius: 7px;
  min-width: 140px;
  background-color: #e5e5e5;
}
.footer .download-app-links .android-app-link:hover {
  background-color: #e7e7e7;
}
.footer .download-app-links .android-app-link img {
  width: 20px;
  margin-right: 10px;
  height: auto;
}
.footer .download-app-links .android-app-link span {
  line-height: normal;
  font-size: 15px;
}
.footer .download-app-links .android-app-link span small {
  position: relative;
  top: 3px;
}
.footer .image-wrapper-footer-logo img {
  width: 150px;
}
.edugorilla #total-books-of-user {
  display: none;
}
.edugorilla #loginOpen .modal-header .close,
.edugorilla #signup .modal-header .close,
.edugorilla #forgotPasswordmodal .modal-header .close {
  font-size: 20px;
}
.edugorilla .main-menu-wrp {
  background: var(--light);
  box-shadow: 0 4px 8px 0 rgba(95, 95, 95, 0.2), 0 6px 20px 0 rgba(146, 146, 146, 0.2);
}
.edugorilla .main-menu-wrp .posi-static-respons {
  display: flex;
  align-items: center;
}
.edugorilla p,
.edugorilla a,
.edugorilla button {
  font-weight: initial;
}
.edugorilla .bookTemplate .side-content ol li.chapter-name i {
  position: relative;
  top: 4px;
}
.edugorilla .bookTemplate .chapterSection a.slide-toggle {
  top: 190px;
}
.edugorilla .manage-count-wrp-box:after {
  background: linear-gradient(50deg, #C9302C 0%, #89c1ff 100%);
}
.edugorilla .connect-section {
  background: #C9302C;
}
.edugorilla .red-color-fill-bg {
  background: #C9302C;
}
.edugorilla .responsive-padding-none:nth-child(even) a {
  background-color: #ee3539 !important;
}
.edugorilla ul.this-is-side-wrp-ul-big-menu-arihant li.active-menuss,
.edugorilla ul.this-is-side-wrp-ul-big-menu-arihant li:hover {
  background: #C9302C;
}
.edugorilla .categories-section {
  background: none;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: auto;
}
.edugorilla .categories-section .line-box-category {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 50px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .edugorilla .categories-section .line-box-category {
    bottom: 20px;
  }
}
.edugorilla .ebook_detail .book_info .book_buttons .col #buyNow {
  color: #FFFFFF;
}
.edugorilla #okBuy {
  color: #FFFFFF;
}
.edugorilla .there-social-footer-link-wrp li a:hover {
  color: #C9302C;
}
.edugorilla .my_books .no-books-available .click-here-link {
  background-color: #C9302C !important;
  color: #FFFFFF;
}
.edugorilla #cartModalBtns .btn-primary,
.edugorilla #cartModalLibBtn .btn-primary {
  background-color: #C9302C !important;
  border-color: #C9302C !important;
  box-shadow: none !important;
  color: #FFFFFF !important;
}
.edugorilla .shopping_cart a.proceed_btn {
  background-color: #C9302C !important;
}
.edugorilla .bookTemplate .shadowHeader {
  height: 50px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .edugorilla .bookTemplate {
    height: auto;
  }
  .edugorilla .bookTemplate .mobChapname {
    transition: all 0.3s;
    z-index: 991;
  }
  .edugorilla .bookTemplate .shadowHeader {
    z-index: 991;
    height: 45px !important;
    position: fixed;
    transition: all 0.3s;
  }
  .edugorilla .bookTemplate .shadowHeader .tab-header .navbar {
    height: auto;
    padding-top: 0.15rem;
  }
  .edugorilla .bookTemplate .shadowHeader .tab-header .contentEdit {
    position: fixed;
    top: 50px;
    right: 0;
    transition: all 0.3s;
    overflow: hidden;
  }
  .edugorilla .bookTemplate .shadowHeader .prevnextbtn {
    position: fixed;
    top: 50px;
    width: 100% !important;
    justify-content: center !important;
    transition: all 0.3s;
  }
  .edugorilla .bookTemplate .shadowHeader .prevnextbtn button {
    margin: 0 5px;
    width: 80px;
    font-size: 13px;
  }
  .edugorilla .bookTemplate .chapterSection {
    z-index: 991;
  }
  .edugorilla .bookTemplate #book-sidebar .backtolibrary {
    font-weight: normal !important;
  }
  .edugorilla .bookTemplate #book-sidebar .mobile-title {
    z-index: 98;
  }
  .edugorilla .bookTemplate #book-sidebar .mobile-title p {
    line-height: normal;
    padding-left: 10px;
  }
  .edugorilla .bookTemplate #book-sidebar .side-content ol {
    padding-left: 10px !important;
  }
  .edugorilla .bookTemplate #book-sidebar .side-content ol li.chapter-name {
    font-size: 15px;
    position: relative;
    margin-right: 1.5rem;
    padding-right: 20px;
  }
  .edugorilla .bookTemplate #book-sidebar .side-content ol li.chapter-name.orangeText a {
    font-size: 15px;
  }
  .edugorilla .bookTemplate #book-sidebar ul.chapter-sections {
    display: none;
  }
  .edugorilla .bookTemplate #book-read-material #content-data-all {
    position: relative;
    z-index: 99;
  }
  .edugorilla .bookTemplate #book-read-material .all-container {
    margin-top: 2rem;
  }
  .edugorilla .bookTemplate #book-read-material .all-container .container-wrapper {
    width: 100%;
    margin-top: 1rem;
    min-height: auto;
  }
  .edugorilla .bookTemplate #book-read-material .all-container .container-wrapper .media {
    padding: 0;
  }
  .edugorilla .bookTemplate #book-read-material .all-container .container-wrapper .media i {
    margin-left: 0;
  }
  .edugorilla .bookTemplate #book-read-material .all-container .container-wrapper .media .title {
    margin-bottom: 5px;
  }
  .edugorilla .bookTemplate #book-read-material .all-container .container-wrapper .media .readnow {
    padding-right: 15px;
  }
  .edugorilla .bookTemplate #htmlreadingcontent iframe {
    height: 100vh !important;
    border: 1px solid #6C757D;
    margin-top: 0 !important;
  }
  .edugorilla .bookTemplate .export-notes {
    top: 90px !important;
  }
  .edugorilla .bookTemplate .export-notes .notes-creation-header {
    padding: 0.5rem 0;
  }
  .edugorilla .bookTemplate .export-notes .notes-creation-header-title {
    font-size: 16px;
    color: #212121;
  }
  .edugorilla.hasScrolled .bookTemplate .tab-header .contentEdit {
    top: 5px;
  }
  .edugorilla.hasScrolled .bookTemplate .export-notes {
    top: 45px !important;
  }
  .edugorilla.hasScrolled .bookTemplate .shadowHeader .prevnextbtn {
    top: 7px;
  }
  .edugorilla .logo-wrapper {
    display: none;
  }
  .edugorilla .my_books #subjectFilter .dropdown #sortBy {
    width: auto;
  }
}
.edugorilla .purchase-details-container .purchase-details-wrapper .learn-btn {
  background: #C9302C;
}
.edugorilla .menu-overlay-big-menus.actv {
  background: rgba(0, 0, 0, 0.7);
}
.edugorilla .menu-bar-wrp ul li a {
  color: #6C757D;
  border-color: #6C757D;
}
.edugorilla .menu-bar-wrp ul li a:hover {
  color: #C9302C;
}
.edugorilla ul.social-icon-wrp-connect li {
  width: 18%;
}
.edugorilla ul.social-icon-wrp-connect li a {
  font-size: 26px;
}
.edugorilla #AddToCartModal #TS_AddToCartBtn:hover,
.edugorilla #AddToCartModal #TS_AddToCartBtn:active,
.edugorilla #AddToCartModal #TS_AddToCartBtn:focus {
  box-shadow: none !important;
  color: #FFFFFF !important;
  background-color: #C9302C !important;
}
.category_list {
  display: flex;
  flex-direction: column;
}
.category_list .category_level {
  margin-bottom: 2rem;
}
.category_list .category_level h4 {
  position: relative;
  margin-bottom: 1.4rem;
}
.category_list .category_level h4:after {
  width: 50px;
  height: 2px;
  content: '';
  position: absolute;
  background: #C9302C;
  left: 0;
  bottom: -4px;
}
.category_list .category_level .category_cards {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 1.2rem;
}
@media (max-width: 768px) {
  .category_list .category_level .category_cards {
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 1rem;
  }
}
.category_list .category_level .category_cards .category_card {
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  height: 80px;
  background: #eee;
  transition: all 0.3s ease-in;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000;
}
.category_list .category_level .category_cards .category_card:active {
  transform: scale(0.7);
}
.category_list .category_level .category_cards .category_card:hover {
  background: transparent;
}
.category_list .category_level .category_cards .category_card a {
  color: black;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.category_list .category_level .category_cards .category_card-title {
  width: 100%;
  text-align: center;
}
@media (max-width: 768px) {
  .category_list .category_level .category_cards .category_card {
    width: 100%;
  }
}
.headerCategoriesMenu {
  border-bottom: 1px solid rgba(0, 0, 0, 0.175);
}
.headerCategoriesMenu .header__categories {
  background: #C9302C !important;
}
.headerCategoriesMenu .header__categories-list__item:not(:last-child) {
  border-right-color: #C9302C;
}
.headerCategoriesMenu .header__categories-list__item a {
  color: #FFFFFF;
}
#accordion .card-body {
  background: #e5e5e59c;
}
#accordion .card-header {
  background: #dadada;
}
#accordion .card a {
  color: #000;
}
#accordion .card-header .btn-link:after {
  color: #000;
}
#accordion .card-header .btn-link.collapsed:after {
  color: #000;
}
