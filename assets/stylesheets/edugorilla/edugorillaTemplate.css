/* MTG Books Template Styles */
button,
.btn {
  cursor: pointer;
  border-radius: 5px;
  text-transform: unset;
}
button:hover,
.btn:hover,
button:focus,
.btn:focus,
button:active,
.btn:active,
button:active:focus,
.btn:active:focus {
  outline: 0;
  box-shadow: none;
}
button:not(:disabled):not(.disabled):active,
.btn:not(:disabled):not(.disabled):active,
button:not(:disabled):not(.disabled):active:focus,
.btn:not(:disabled):not(.disabled):active:focus {
  box-shadow: none;
}
button.disabled,
.btn.disabled,
button:disabled,
.btn:disabled {
  cursor: unset;
}
button.btn-lg,
.btn.btn-lg {
  font-size: 14px;
}
.btn-shadow {
  box-shadow: 0 2px 4px #0000001A;
  -webkit-box-shadow: 0 2px 4px #0000001A;
  -moz-box-shadow: 0 2px 4px #0000001A;
}
.btn-primary-modifier {
  background-color: #C9302C !important;
  border-color: #C9302C !important;
}
.btn-primary-modifier:hover {
  background-color: #C9302C;
  border-color: #C9302C;
}
.btn-primary-modifier:not(:disabled):not(.disabled):active,
.btn-primary-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #C9302C;
  border-color: #C9302C;
}
.btn-primary-modifier.disabled,
.btn-primary-modifier:disabled {
  background-color: #C9302C;
  border-color: #C9302C;
}
.btn-primary-modifier.dropdown-toggle:focus {
  box-shadow: none !important;
}
.btn-primary-modifier:focus:not(:active) {
  background-color: #C9302C;
}
.btn-primary-modifier .material-icons {
  font-size: 18px;
}
.btn-outline-primary-modifier {
  color: #C9302C;
  border-color: #C9302C;
}
.btn-outline-primary-modifier:hover {
  color: #C9302C;
  background-color: transparent;
  border-color: #C9302C;
}
.btn-outline-primary-modifier:not(:disabled):not(.disabled):active,
.btn-outline-primary-modifier:not(:disabled):not(.disabled):active:focus {
  color: #C9302C;
  background-color: transparent;
  border-color: #C9302C;
}
.btn-outline-primary-modifier:focus:not(:active) {
  background-color: transparent;
}
.btn-secondary-modifier {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier:hover {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier:not(:disabled):not(.disabled):active,
.btn-secondary-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier.disabled,
.btn-secondary-modifier:disabled {
  background-color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-secondary-modifier:focus:not(:active) {
  background-color: #8E8E8E;
}
.btn-outline-secondary-modifier {
  color: #8E8E8E;
  border-color: #8E8E8E;
}
.btn-outline-secondary-modifier:hover {
  color: #8E8E8E;
  background-color: transparent;
  border-color: #8E8E8E;
}
.btn-outline-secondary-modifier:not(:disabled):not(.disabled):active,
.btn-outline-secondary-modifier:not(:disabled):not(.disabled):active:focus {
  color: #8E8E8E;
  background-color: transparent;
  border-color: #8E8E8E;
}
.btn-outline-secondary-modifier:focus:not(:active) {
  background-color: transparent;
}
.btn-success-modifier {
  background-color: #27AE60;
  border-color: #27AE60;
}
.btn-success-modifier:hover {
  background-color: #27AE60;
  border-color: #27AE60;
}
.btn-success-modifier:not(:disabled):not(.disabled):active,
.btn-success-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #27AE60;
  border-color: #27AE60;
}
.btn-success-modifier.disabled,
.btn-success-modifier:disabled {
  background-color: #27AE60 !important;
  color: #FFFFFF !important;
  border-color: #27AE60;
}
.btn-success-modifier:focus:not(:active) {
  background-color: #27AE60;
}
.btn-outline-success-modifier {
  color: #27AE60;
  border-color: #27AE60;
}
.btn-outline-success-modifier:hover {
  color: #27AE60;
  background-color: transparent;
  border-color: #27AE60;
}
.btn-outline-success-modifier:not(:disabled):not(.disabled):active,
.btn-outline-success-modifier:not(:disabled):not(.disabled):active:focus {
  color: #27AE60;
  background-color: transparent;
  border-color: #27AE60;
}
.btn-outline-success-modifier:focus:not(:active) {
  background-color: transparent;
}
.btn-danger-modifier {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier:hover {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier:not(:disabled):not(.disabled):active,
.btn-danger-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier.disabled,
.btn-danger-modifier:disabled {
  background-color: #FF4B33;
  border-color: #FF4B33;
}
.btn-danger-modifier:focus:not(:active) {
  background-color: #FF4B33;
}
.btn-warning-modifier {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier:hover {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier:not(:disabled):not(.disabled):active,
.btn-warning-modifier:not(:disabled):not(.disabled):active:focus {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier.disabled,
.btn-warning-modifier:disabled {
  background-color: #F79420;
  border-color: #F79420;
  color: #FFFFFF;
}
.btn-warning-modifier:focus:not(:active) {
  background-color: #F79420;
  color: #FFFFFF;
}
.mdl-button-modifier {
  height: auto;
  width: auto;
  min-width: auto;
}
.card-modifier {
  border-radius: 10px;
}
.card-shadow {
  box-shadow: 0 4px 10px #0000001A;
  -webkit-box-shadow: 0 4px 10px #0000001A;
  -moz-box-shadow: 0 4px 10px #0000001A;
}
.dataTables_wrapper {
  padding-top: 45px;
}
.dataTables_wrapper label,
.dataTables_wrapper li,
.dataTables_wrapper .dataTables_info {
  font-size: 13px;
}
.dataTables_wrapper .page-item.active .page-link {
  color: #FFFFFF !important;
}
.datepicker table thead tr th {
  font-weight: 500;
}
.datepicker table thead tr th.prev,
.datepicker table thead tr th.next {
  font-size: 20px;
}
.datepicker table tbody tr td {
  padding: 5px 7px;
}
.datepicker.datepicker-dropdown.datepicker-orient-bottom:before {
  top: -6px;
}
.datepicker.datepicker-dropdown.datepicker-orient-bottom:after {
  top: -5px;
}
.dropdown-menu a:active,
.dropdown-menu span:active,
.dropdown-menu li:active {
  background-color: #f4cecd;
}
.dropdown-modifier.show .dropdown-toggle:focus {
  box-shadow: none;
}
.dropdown-modifier .btn-outline-primary-modifier.dropdown-toggle {
  background-color: transparent;
  border-color: #C9302C;
  color: #C9302C;
}
.form-group-modifier {
  position: relative;
  margin-bottom: 0;
}
.form-group-modifier:after {
  background-color: #C9302C;
  bottom: 0;
  content: '';
  height: 2px;
  left: 45%;
  position: absolute;
  transition-duration: 0.2s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  visibility: hidden;
  width: 10px;
}
.form-group-modifier.is-focused:after {
  left: 0;
  visibility: visible;
  width: 100%;
}
.form-group-modifier .form-control-modifier {
  border: none;
  padding: 0.375rem 0;
  border-radius: 0;
}
.form-control-modifier {
  color: #212121;
}
.form-control-modifier:focus {
  box-shadow: none;
}
.mdl-textfield-modifier {
  padding-bottom: 0;
}
.mdl-textfield-modifier .mdl-textfield__label {
  font-size: 12px;
  margin-bottom: 0;
}
.mdl-textfield-modifier .mdl-textfield__label:after {
  background-color: #C9302C;
  bottom: 0;
}
.mdl-textfield-modifier.is-focused .mdl-textfield__label,
.mdl-textfield-modifier.is-dirty .mdl-textfield__label {
  color: #C9302C;
  margin-bottom: 0;
}
#cke_1_contents {
  min-height: 300px;
}
.form-text-modifier {
  font-size: 12px;
}
.modal {
  z-index: 9992;
}
.modal-backdrop.show {
  opacity: 0.85;
  z-index: 9991;
}
.modal-modifier .modal-header-modifier {
  border: none;
}
.modal-modifier .modal-body-modifier h1,
.modal-modifier .modal-body-modifier h2,
.modal-modifier .modal-body-modifier h3,
.modal-modifier .modal-body-modifier h4,
.modal-modifier .modal-body-modifier h5 {
  color: #444444;
  font-weight: 500;
  font-size: 18px;
  line-height: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-body-modifier h1,
  .modal-modifier .modal-body-modifier h2,
  .modal-modifier .modal-body-modifier h3,
  .modal-modifier .modal-body-modifier h4,
  .modal-modifier .modal-body-modifier h5 {
    font-size: 16px;
  }
}
.modal-modifier .modal-body-modifier p {
  color: #949494;
}
.modal-modifier .modal-body-modifier p strong {
  font-weight: 600;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-body-modifier p {
    font-size: 13px;
  }
  .modal-modifier .modal-body-modifier p br {
    display: none;
  }
}
.modal-modifier .modal-header-modifier .close {
  margin: 0;
}
.modal-modifier .close {
  position: absolute;
  right: 15px;
  top: -45px;
  padding: 0;
  font-weight: 100;
  font-size: 30px;
  opacity: 1;
}
.modal-modifier .close:focus,
.modal-modifier .close:active {
  outline: 0;
  box-shadow: none;
}
.modal-modifier .btn,
.modal-modifier a.btn {
  font-size: 14px;
}
.modal-modifier .modal-content-modifier {
  border-radius: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-modifier .modal-dialog-modifier {
    align-items: flex-end;
    padding-bottom: 0;
    max-width: 100%;
    margin: 0;
    height: 100%;
  }
  .modal-modifier .modal-content-modifier {
    border-radius: 20px 20px 0 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-mobile-fullscreen .close {
    position: relative;
    right: 0;
    top: 0;
    font-weight: 200;
    color: #444444 !important;
  }
  .modal-mobile-fullscreen .modal-dialog-modifier {
    align-items: inherit;
  }
  .modal-mobile-fullscreen .modal-content-modifier {
    border-radius: 0;
    border: none;
    height: 100vh;
  }
}
.modal.fade .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(0.5);
  transform: translate(0, 0) scale(0.5);
}
.modal.show .modal-dialog.modal-dialog-zoom {
  -webkit-transform: translate(0, 0) scale(1);
  transform: translate(0, 0) scale(1);
}
table thead tr th,
.table thead tr th {
  font-size: 13px;
  font-weight: 500;
  white-space: normal !important;
}
table thead tr th:first-child,
.table thead tr th:first-child {
  width: 15%;
}
table thead tr th:first-child span,
.table thead tr th:first-child span {
  font-size: 13px;
  font-weight: 500;
}
table tbody,
.table tbody {
  display: table-row-group;
}
table tbody tr td,
.table tbody tr td {
  font-size: 13px;
  white-space: normal !important;
}
table tbody tr td button,
.table tbody tr td button,
table tbody tr td p,
.table tbody tr td p {
  font-size: 13px;
}
html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
li,
a,
input,
textarea,
select,
label,
span,
small,
button,
th,
td,
dl,
dt,
dd,
address {
  font-family: 'Poppins', sans-serif !important;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #212121;
}
h1,
h2,
h3,
h4,
p {
  margin: 0;
  padding: 0;
}
a:hover,
a:active {
  text-decoration: none;
}
.material-icons {
  font-family: 'Material Icons' !important;
}
.text-primary-modifier {
  color: #C9302C !important;
}
a.text-primary-modifier:hover,
a.text-primary-modifier:focus {
  color: #C9302C !important;
}
.text-secondary-modifier {
  color: #6C757D !important;
}
.text-danger-modifier {
  color: #FF4B33 !important;
}
.text-success-modifier {
  color: #27AE60 !important;
}
.bg-primary-modifier {
  background-color: #C9302C !important;
}
.bg-success-modifier {
  background-color: #27AE60 !important;
}
.bg-warning-modifier {
  border-color: #FFD602 !important;
}
.border-primary-modifier {
  border-color: #C9302C !important;
}
.rounded-modifier {
  border-radius: 5px !important;
}
.switch {
  display: flex;
  position: absolute;
  right: 0;
  top: 0;
}
.switch.reset-switch {
  position: static;
  display: flex;
  align-items: center;
}
.switch.reset-switch label {
  margin-bottom: 0 !important;
}
.public-text {
  margin-right: 10px;
  display: block;
  font-style: italic;
  font-size: 12px;
  color: #C9302C;
}
.toggleSwitch {
  /* Rounded sliders */
}
.toggleSwitch .switch {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 15px;
}
.toggleSwitch .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.toggleSwitch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch .slider:before {
  position: absolute;
  content: "";
  height: 13px;
  width: 13px;
  left: 2px;
  bottom: 1px;
  background-color: #FFFFFF;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}
.toggleSwitch input:checked + .slider {
  background: #27AE60;
}
.toggleSwitch input:focus + .slider {
  box-shadow: 0 0 1px #2F80ED;
}
.toggleSwitch input:checked + .slider:before {
  -webkit-transform: translateX(14px);
  -ms-transform: translateX(14px);
  transform: translateX(14px);
}
.toggleSwitch .slider.round {
  border-radius: 10px;
}
.toggleSwitch .slider.round:before {
  border-radius: 50%;
}
.toggleSwitch input:disabled + .slider {
  opacity: 0.3;
  cursor: not-allowed;
}
.chartist-tooltip {
  position: absolute;
  display: inline-block;
  opacity: 0;
  min-width: 5em;
  padding: 0.5em;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #CCC;
  border-radius: 5px;
  color: #212121;
  font-weight: 600;
  font-size: 80%;
  text-align: center;
  pointer-events: none;
  z-index: 1;
  -webkit-transition: opacity 0.2s linear;
  -moz-transition: opacity 0.2s linear;
  -o-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
}
.chartist-tooltip:before {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  width: 0;
  height: 0;
  margin-left: -15px;
  border: 15px solid transparent;
  border-top-color: #FFFFFF;
  display: none;
}
.chartist-tooltip.tooltip-show {
  opacity: 1;
}
.sweet-overlay {
  background-color: #000000B3 !important;
}
.sweet-alert h2 {
  background: #C9302C;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-flex !important;
  justify-content: center;
  font-size: 26px !important;
  margin: 0 0 10px !important;
  color: transparent !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .sweet-alert h2 {
    font-size: 24px !important;
  }
}
.sweet-alert p {
  color: #212121 !important;
  font-size: 15px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .sweet-alert p {
    font-size: 14px !important;
  }
}
.sweet-alert button {
  font-size: 15px !important;
  font-weight: 400 !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .sweet-alert button {
    font-size: 14px !important;
    padding: 7px 20px !important;
    margin: 15px 5px 0 5px !important;
  }
}
.sweet-alert button.cancel {
  background-color: transparent !important;
  color: #8E8E8E;
  border: 1px solid #8E8E8E;
}
.information-admin label {
  font-weight: 500;
}
.information-admin .toggle .toggle-group label {
  font-weight: 400;
}
.information-admin #viewInformation table {
  table-layout: fixed;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:first-child {
  width: 60px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(2),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(3) {
  width: 140px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(4),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(5),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(6),
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(7) {
  width: 120px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:nth-child(9) {
  width: 100px !important;
}
.information-admin #viewInformation #informationData_wrapper thead tr th:last-child {
  width: 120px !important;
}
.banner_management .add_banners label {
  color: #6C757D;
}
.banner_management .add_banners input {
  height: 40px;
}
.direct-sales {
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}
.direct-sales form .form-group {
  margin-bottom: 1.5rem;
}
.direct-sales form label {
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
}
.direct-sales form select,
.direct-sales form input {
  height: 40px;
}
.direct-sales form select:focus,
.direct-sales form input:focus {
  outline: 0;
  box-shadow: none;
  border-color: #C9302C;
}
.direct-sales form select.input-error,
.direct-sales form input.input-error {
  border-color: #FF4B33;
}
.direct-sales form .btn {
  font-weight: 600;
}
.direct-sales #processResult p {
  font-weight: 500;
  line-height: normal;
}
.direct-sales #processResult .payment-link-share {
  display: inline-flex;
  align-items: center;
}
.direct-sales #processResult .payment-link-share i {
  font-size: 20px;
}
.direct-sales .direct-sale-book-info {
  background-color: #FFFFFF;
  box-shadow: 0 4px 10px #0000001A;
  border-radius: 10px;
  border: 1px solid #F79420;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales .institute-info .institute-name h4 {
    font-size: 1.25rem;
  }
}
.direct-sales .institute-info .institute-logo img {
  width: 5rem;
  height: 5rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales .institute-info .institute-logo img {
    width: 4rem;
    height: 4rem;
  }
}
.direct-sales.ebook_detail .book_info {
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 15;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-top: 1px solid #DDD;
}
.direct-sales.ebook_detail .book_info .book_description h2 {
  font-weight: 300;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales.ebook_detail .book_info .book_description h2 {
    text-align: center !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales.ebook_detail .book_info .book_price {
    justify-content: center !important;
  }
}
.direct-sales.ebook_detail .book_info .book_price .offer_price {
  font-size: 2.5rem;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales.ebook_detail .book_info .book_price .offer_price {
    font-size: 2rem;
  }
}
@media (max-width: 575.98px) {
  .direct-sales.ebook_detail .book_info .book_price .offer_price {
    font-size: 1.75rem;
  }
}
.direct-sales.ebook_detail .book_info .book_price .offer_price span {
  font-weight: 500;
}
.direct-sales.ebook_detail .book_info .book_price .list_price {
  font-size: 2rem;
  font-weight: 300;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales.ebook_detail .book_info .book_price .list_price {
    font-size: 1.75rem;
  }
}
@media (max-width: 575.98px) {
  .direct-sales.ebook_detail .book_info .book_price .list_price {
    font-size: 1.5rem;
  }
}
.direct-sales.ebook_detail .image_wrapper {
  margin-top: -100px;
}
.direct-sales.ebook_detail .image_wrapper .book_image {
  height: 400px;
  width: 300px;
  padding: 10px;
  border-radius: 10px;
  background-color: #FFFFFF;
}
@media (max-width: 330px) {
  .direct-sales.ebook_detail .image_wrapper .book_image {
    height: 330px !important;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales.ebook_detail .image_wrapper .book_image {
    height: 300px;
    width: 230px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales.ebook_detail .image_wrapper .book_image {
    height: 220px;
    width: 160px;
  }
}
.direct-sales.ebook_detail .image_wrapper .book_image .bookShadow {
  height: 100% !important;
}
.direct-sales.ebook_detail .image_wrapper .book_image .bookShadow img {
  height: 100% !important;
}
.direct-sales .pay-btn {
  border: none;
  background-color: #F79420;
  color: #FFFFFF;
  position: relative;
  overflow: hidden;
  font-weight: 600;
  font-size: 1.5rem;
  border-radius: 7px;
  box-shadow: 0 2px 4px #0000001A;
  width: 300px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales .pay-btn {
    width: 250px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .direct-sales .pay-btn {
    height: 45px;
    width: 100%;
  }
}
.direct-sales .pay-btn:after {
  content: '';
  position: absolute;
  top: -10px;
  left: -50%;
  z-index: 10;
  display: block;
  width: 30px;
  height: 100px;
  opacity: 0.7;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-40deg);
  transform: skewX(-40deg);
  -webkit-animation: shine 3s infinite;
  animation: shine 3s infinite;
  filter: blur(5px);
}
.direct-sales .package-books .img-wrapper {
  height: 300px;
  width: 230px;
  padding: 10px;
  border-radius: 10px;
  background-color: #FFFFFF;
  margin: 0 auto;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .direct-sales .package-books .img-wrapper {
    height: 220px;
    width: 160px;
  }
}
@media (max-width: 330px) {
  .direct-sales .package-books .img-wrapper {
    height: 330px !important;
  }
}
.direct-sales .package-books .img-wrapper .bookShadow {
  height: 100%;
}
.direct-sales .package-books .img-wrapper .bookShadow img {
  width: 100%;
  height: 100%;
}
.loading-icon {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  background-color: #000000B3;
  z-index: 9999;
  overflow: hidden;
}
.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0 0 10px #00000025;
  margin: 0 auto;
  border-radius: 4px;
}
.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  -moz-animation: load7 1.8s infinite ease-in-out;
  -o-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #C9302C;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  -moz-animation-delay: -0.16s;
  -o-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  -moz-animation-delay: -0.32s;
  -o-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
.pace {
  -webkit-pointer-events: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.pace-inactive {
  display: none;
}
.pace .pace-progress {
  background: #C9302C;
  position: fixed;
  z-index: 2000;
  top: 0;
  right: 100%;
  width: 100%;
  height: 3px;
}
.go-back-btn {
  background-color: transparent;
  color: #C9302C;
}
.go-back-btn:hover,
.go-back-btn:focus,
.go-back-btn:active {
  background-color: #F8F9FA;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .page_title h3 {
    font-size: 1.5rem;
  }
}
.svg-primary-color {
  fill: #C9302C;
}
.slick-prev,
.slick-next {
  background-color: #FFFFFF;
  width: 40px;
  height: 80px;
  box-shadow: 0 0 8px #00000040;
}
.slick-prev:before,
.slick-next:before {
  color: #444444;
  width: 7px;
  height: 12px;
}
.slick-prev:hover,
.slick-next:hover,
.slick-prev:focus,
.slick-next:focus,
.slick-prev:active,
.slick-next:active {
  background-color: #FFFFFF;
  box-shadow: 0 0 8px #00000040 !important;
}
.slick-prev {
  left: -40px;
  border-radius: 5px 0 0 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .slick-prev {
    left: 0;
    z-index: 10;
  }
}
.slick-prev:before {
  content: url("../../images/landingpageImages/left-arrow.svg");
}
.slick-next {
  right: -40px;
  border-radius: 0 5px 5px 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .slick-next {
    right: 0;
    z-index: 10;
  }
}
.slick-next:before {
  content: url("../../images/landingpageImages/right-arrow.svg");
}
.correct-animated-icon {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 20px auto;
}
.correct-animated-icon:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #b3eecc;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
.correct-animated-icon:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.correct-animated-icon .checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #27AE60;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}
.correct-animated-icon .checkmark {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: block;
  stroke-width: 2;
  stroke: #FFFFFF;
  stroke-miterlimit: 10;
  margin: 0;
  box-shadow: inset 0 0 0 #27AE60;
  animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
  position: absolute;
  z-index: 2;
  right: 0;
  left: 0;
}
.correct-animated-icon .checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}
.info-animated-icon .modal-info-icon {
  border-radius: 50%;
  border: 3px solid #F8BB86;
  box-sizing: content-box;
  height: 56px;
  margin: 20px auto;
  padding: 0;
  position: relative;
  width: 56px;
}
.info-animated-icon .modal-info-icon:before {
  -webkit-animation: pulseWarning 2s linear infinite;
  animation: pulseWarning 2s linear infinite;
  background-color: #ffd9b9;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 100%;
  opacity: 0;
  position: absolute;
  width: 100%;
  left: 0;
  right: 0;
}
.info-animated-icon .modal-info-icon:after {
  background-color: #FFFFFF;
  border-radius: 50%;
  content: "";
  display: block;
  height: 100%;
  position: absolute;
  width: 100%;
  z-index: 1;
  left: 0;
  right: 0;
}
.info-animated-icon .modal-info-icon .info-icon-body {
  background-color: #F8BB86;
  border-radius: 2px;
  height: 26px;
  left: 50%;
  margin-left: -1px;
  position: absolute;
  top: 10px;
  width: 4px;
  z-index: 2;
}
.info-animated-icon .modal-info-icon .info-icon-dot {
  background-color: #F8BB86;
  border-radius: 50%;
  bottom: 10px;
  height: 6px;
  left: 50%;
  margin-left: -2px;
  position: absolute;
  width: 6px;
  z-index: 2;
}
.info-animated-icon .modal-info-icon + .modal-info-icon {
  margin-top: 50px;
}
.scaleAnimation {
  -webkit-animation: scaleAnimation 1s infinite alternate;
  animation: scaleAnimation 1s infinite alternate;
}
.pulseAnimationIns {
  -webkit-animation: pulseAnimationIns 0.75s infinite alternate;
  animation: pulseAnimationIns 0.75s infinite alternate;
}
.fadein-animated {
  animation: fadein 1.5s;
  -moz-animation: fadein 1.5s;
  -webkit-animation: fadein 1.5s;
  -o-animation: fadein 1.5s;
}
@keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-moz-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-webkit-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@-o-keyframes fadein {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.75);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes shine {
  100% {
    left: 125%;
  }
}
@keyframes line-bounce {
  0% {
    left: 250px;
  }
  50% {
    left: 0;
  }
  100% {
    left: 250px;
  }
}
@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}
@keyframes scale {
  0%,
  100% {
    transform: none;
  }
  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}
@keyframes fill {
  100% {
    box-shadow: inset 0 0 0 30px #27AE60;
  }
}
@keyframes scaleAnimation {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes pulseWarning {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1);
    opacity: 0.5;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulseAnimationIns {
  0% {
    background-color: #F8D486;
  }
  100% {
    background-color: #F8BB86;
  }
}
.app_in_app ul.typeahead {
  overflow-y: scroll !important;
  height: 200px !important;
}
.ellipsis-type {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.ellipsis-type.single-line-text {
  -webkit-line-clamp: 1;
}
.banner-ws .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #6C757D;
}
.banner-ws .carousel-indicators li.active {
  background: #FFFFFF;
}
.banner-ws .carousel-control-next,
.banner-ws .carousel-control-prev {
  width: 40px;
  height: 80px;
  top: 45%;
  border-radius: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .banner-ws .carousel-control-next,
  .banner-ws .carousel-control-prev {
    width: 25px;
    height: 35px;
    border-radius: 3px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .banner-ws .carousel-control-next,
  .banner-ws .carousel-control-prev {
    width: 35px;
    height: 50px;
  }
}
.banner-ws .carousel-control-next i,
.banner-ws .carousel-control-prev i {
  font-size: 35px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .banner-ws .carousel-control-next i,
  .banner-ws .carousel-control-prev i {
    font-size: 25px;
  }
}
.banner-ws .carousel-control-next {
  right: 10px;
}
.banner-ws .carousel-control-prev {
  left: 10px;
}
.border-animation:before,
.border-animation:after {
  content: "";
  width: 0;
  height: 1px;
  position: absolute;
  transition: all 0.2s linear;
  transition-delay: 0.2s;
  background: #C9302C;
}
.border-animation:before {
  right: 0;
  top: 0;
}
.border-animation:after {
  left: 0;
  bottom: 0;
}
.border-animation .border-animation-inside:before,
.border-animation .border-animation-inside:after {
  content: "";
  width: 1px;
  height: 0;
  position: absolute;
  transition: all 0.2s linear;
  transition-delay: 0s;
  background: #C9302C;
}
.border-animation .border-animation-inside:before {
  left: 0;
  top: 0;
}
.border-animation .border-animation-inside:after {
  right: 0;
  bottom: 0;
}
.border-animation:hover:before,
.border-animation:hover:after {
  width: 100%;
  transition-delay: 0s;
}
.border-animation:hover .border-animation-inside:before,
.border-animation:hover .border-animation-inside:after {
  height: 100%;
  transition-delay: 0.2s;
}
.calendar_wrapper {
  width: 100%;
}
.calendar_wrapper .datepicker {
  width: 100%;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  font-family: 'Poppins', sans-serif !important;
}
.calendar_wrapper .datepicker .table-condensed {
  width: 100%;
  font-size: 14px;
}
.calendar_wrapper .datepicker .datepicker-days .prev {
  color: #C9302C;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .next {
  color: #C9302C;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .datepicker-switch {
  height: 40px;
  color: #C9302C;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .dow {
  font-size: 12px;
  font-weight: 400;
  color: #949494;
  padding: 10px;
}
.calendar_wrapper .datepicker .datepicker-days .day {
  width: 40px;
  height: 40px;
  border-radius: 50px;
  font-weight: 600;
}
.calendar_wrapper .datepicker .datepicker-days .day.active {
  background: #C9302C;
}
.calendar_wrapper .datepicker .datepicker-days .day.active:hover {
  color: #FFFFFF;
}
.calendar_wrapper .datepicker .datepicker-days .day:hover {
  color: #C9302C;
}
.calendar_wrapper .datepicker .datepicker-days .old.day,
.calendar_wrapper .datepicker .datepicker-days .new.day {
  color: #949494;
  font-weight: 400;
}
.resourcePageShimmer {
  width: 100%;
  height: 93vh;
  position: relative;
  z-index: 9;
  display: grid;
  grid-template-columns: 400px 750px;
  grid-gap: 8rem;
  background: #fff;
}
.resourcePageShimmer_sideBar {
  margin: 3rem 0 3rem 3rem;
}
.resourcePageShimmer_main {
  margin: 3rem 0;
}
.resourcePageShimmer .card {
  position: relative;
  -webkit-box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
  border-radius: 6px;
  height: 100%;
  overflow: hidden;
  margin: 40px auto;
}
.resourcePageShimmer .card .shimmerBG {
  animation-duration: 2.2s;
  animation-fill-mode: forwards;
  animation-iteration-count: infinite;
  animation-name: shimmerEffect;
  animation-timing-function: linear;
  background: #ddd;
  background: linear-gradient(to right, #f6f6f6 8%, #f0f0f0 18%, #f6f6f6 33%);
  background-size: 1200px 100%;
}
@-webkit-keyframes shimmerEffect {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 100% 0;
  }
}
@keyframes shimmerEffect {
  0% {
    background-position: -1200px 0;
  }
  100% {
    background-position: 1200px 0;
  }
}
.resourcePageShimmer .card .media {
  height: 200px;
}
.resourcePageShimmer .card .p-32 {
  margin: 1rem;
}
.resourcePageShimmer .card .title-line {
  height: 70px;
  width: 100%;
  margin-bottom: 2rem;
  border-radius: 5px;
}
.resourcePageShimmer .card .content-line {
  height: 20px;
  width: 100%;
  margin-bottom: 2rem;
  border-radius: 5px;
}
.resourcePageShimmer .card .shimmerBGWrp {
  display: flex;
  gap: 1rem;
  margin: 1rem;
}
.resourcePageShimmer .card .contentBtn {
  height: 50px;
  width: 50%;
  margin-bottom: 2rem;
  border-radius: 5px;
}
.resourcePageShimmer .card .end {
  width: 100%;
}
.m-t-24 {
  margin-top: 24px;
}
.resourcePageShimmer .card {
  box-shadow: none !important;
  background: transparent !important;
  border: none !important;
}
@media (min-width: 992px) and (max-width: 1199.98px), (min-width: 1200px) {
  .books-list #content-data-books-ebooks .col-lg-2 {
    flex: 0 0 20%;
    max-width: 20%;
  }
}
.books-list .topSchoolBooks {
  margin-right: auto;
  margin-left: auto;
  width: 175px;
  padding: 0;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .topSchoolBooks {
    width: 155px;
  }
}
.books-list .image-wrapper {
  width: 100%;
  height: 220px;
  position: relative;
  z-index: 99;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .image-wrapper {
    height: 200px;
  }
}
.books-list .image-wrapper img {
  width: 100%;
  height: 220px;
  position: relative;
  border-radius: 4px;
  box-shadow: 0 0 14px #00000040;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .image-wrapper img {
    height: 200px;
  }
}
.books-list .image-wrapper h3 {
  position: absolute;
  font-size: 11px;
  font-weight: 400;
  color: #FFFFFF;
  background-color: #F79420;
  padding: 7px 14px;
  bottom: 32px;
  left: -5px;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  box-shadow: 0 0 6px #00000040;
}
.books-list .image-wrapper h3:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #CF6C00 #CF6C00 transparent transparent;
}
.books-list .uncover {
  padding: 10px;
  height: 220px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .books-list .uncover {
    height: 200px;
  }
}
.books-list .uncover p {
  color: #FFFFFF;
  text-align: center;
}
.books-list .content-wrapper {
  margin-top: 8px;
}
.books-list .content-wrapper h3 {
  font-size: 14px;
  font-weight: 400;
  color: #212121;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: normal;
}
.books-list .content-wrapper h6 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: normal;
}
.books-list .content-wrapper p {
  font-size: 12px;
  letter-spacing: -0.015em;
}
.books-list .content-wrapper p.price {
  font-size: 15px;
  font-family: 'Rubik', sans-serif !important;
  color: #212121;
  padding-top: 0;
}
.books-list .content-wrapper p.price span {
  font-size: 14px;
  font-family: 'Rubik', sans-serif !important;
  font-weight: 300;
  text-decoration: line-through;
  color: #6C757D;
  margin-left: 5px;
}
.books-list .lib-showcase {
  position: relative;
  z-index: 9;
  min-height: 192px;
  border-radius: 4px;
  color: #FFFFFF;
}
.books-list .card {
  padding: 10px;
  width: 175px;
  border: none;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  background-color: #FFFFFF;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  margin-bottom: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .books-list .card {
    width: 100%;
  }
}
.books-list .card:hover {
  box-shadow: 0 0 14px #00000040;
}
.books-list .card .uncover {
  height: 192px;
}
.books-list .card img {
  width: 154px;
  height: 192px;
  box-shadow: 0 0 14px #00000040;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .books-list .card img {
    width: 100%;
  }
}
.books-list .card .img-hero {
  position: absolute;
  z-index: 99;
  border-radius: 4px;
  box-shadow: 0 0 10px #0000001A;
}
.books-list .card .img-child {
  position: absolute;
  left: 7px;
  top: 5px;
  border-radius: 4px;
}
.books-list .card .card-body {
  padding: 0;
  align-items: start;
}
.books-list .card .card-body .card-text {
  height: auto;
  color: #444444;
  margin-top: 0.5rem;
  width: 152px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.books-list .card .card-body .card-text:hover {
  color: #C9302C;
  text-decoration: underline;
}
.books-list .card .card-body .dropup .dropdown-toggle,
.books-list .card .card-body .dropdown .dropdown-toggle {
  padding-right: 0;
  padding-bottom: 0;
}
.books-list .card .card-body .dropup .dropdown-toggle:after,
.books-list .card .card-body .dropdown .dropdown-toggle:after {
  display: none;
}
.books-list .card .card-body .dropup .dropdown-menu,
.books-list .card .card-body .dropdown .dropdown-menu {
  min-width: 100px;
  top: -30px !important;
  transform: none !important;
  padding: 0.25rem 0;
  z-index: 991;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .books-list .card .card-body .dropup .dropdown-menu,
  .books-list .card .card-body .dropdown .dropdown-menu {
    left: auto !important;
    right: 0;
  }
}
.books-list .card .card-body .dropup .dropdown-menu .delete,
.books-list .card .card-body .dropdown .dropdown-menu .delete {
  color: #FF4B33;
  font-size: 13px;
  text-align: center;
}
.books-list .card .card-body .dropup .dropdown-menu .delete:focus,
.books-list .card .card-body .dropdown .dropdown-menu .delete:focus,
.books-list .card .card-body .dropup .dropdown-menu .delete:active,
.books-list .card .card-body .dropdown .dropdown-menu .delete:active {
  outline: 0;
  background-color: transparent;
}
.books-list .card a {
  cursor: pointer;
}
.books-list .card .book-tag {
  position: absolute;
  font-size: 10px;
  font-weight: 500;
  color: #FFFFFF;
  background-color: #F79420;
  padding: 7px 14px;
  bottom: 32px;
  left: -5px;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  box-shadow: 0 0 6px #00000040;
}
.books-list .card .book-tag:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #CF6C00 #CF6C00 transparent transparent;
}
.bookShadow {
  height: auto;
  border-radius: 0 3px 3px 0;
  box-shadow: inset 8px 0 10px #0000001A;
  position: relative;
}
.bookShadow:after {
  content: '';
  position: absolute;
  top: 0;
  left: 5px;
  bottom: 0;
  width: 2px;
  background: #0000001A;
  box-shadow: 1px 0 3px rgba(255, 255, 255, 0.3);
}
.book-publisher-name {
  text-transform: uppercase;
  color: #aeaeae;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.badge-overlay {
  position: absolute;
  left: -15px;
  top: -15px;
  width: 40px;
  height: 40px;
  z-index: 100;
  -webkit-transition: width 1s ease, height 1s ease;
  -moz-transition: width 1s ease, height 1s ease;
  -o-transition: width 1s ease, height 1s ease;
  transition: width 0.4s ease, height 0.4s ease;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .badge-overlay {
    left: -10px;
    top: -10px;
  }
}
.top-left {
  position: absolute;
  top: 0;
  left: 0;
  -ms-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -webkit-transform: translateX(-30%) translateY(0%) rotate(-45deg);
  transform: translateX(-30%) translateY(0%) rotate(-45deg);
  -ms-transform-origin: top right;
  -webkit-transform-origin: top right;
  transform-origin: top right;
}
.badge {
  margin: 0;
  padding: 5px;
  color: white;
  line-height: 1;
  background: #01b901;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: pre-wrap;
}
.popular_searches .popular_search_lists {
  height: 250px;
}
@media (max-width: 375px) {
  .popular_searches .popular_search_lists {
    margin-left: 4% !important;
  }
}
@media (min-device-width: 376px) and (max-device-width: 768px) {
  .popular_searches .popular_search_lists {
    margin-left: 8% !important;
  }
}
.popular_searches .bg-light {
  box-shadow: 0 2px 10px #0000001A;
}
.popular_searches .slick-track {
  width: 1900px !important;
  margin-left: 0;
  margin-right: 0;
}
.popular_searches .slick-track .slick-slide {
  width: 190px !important;
}
.popular_searches .img-wrapper {
  padding: 15px;
  width: 155px;
  margin: 0 auto;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
.popular_searches .img-wrapper img {
  width: 100%;
  height: 165px;
}
.popular_searches .bookShadow {
  margin-bottom: 10px;
}
.popular_searches a:hover .uncover p {
  text-decoration: none;
}
.popular_searches a:hover .content-wrapper p {
  text-decoration: underline;
}
.popular_searches .content-wrapper p {
  font-size: 12px;
  color: #444444;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  line-height: normal;
}
.popular_searches .uncover {
  height: 165px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popular_searches .uncover p {
  font-size: 12px;
}
.popular_searches #show-more,
.popular_searches #bestSeller-show-more {
  font-size: 13px;
}
.popular_searches #show-more:hover,
.popular_searches #bestSeller-show-more:hover {
  text-decoration: underline;
}
body.whitelabel_ws .popular_searches .img-wrapper {
  padding: 15px;
  width: 155px;
  margin: 0 auto;
  height: fit-content;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
.all-container .container-wrapper {
  min-height: auto;
  width: 600px;
  margin-top: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .all-container .container-wrapper {
    width: 100%;
  }
}
.all-container .container-wrapper div > .media {
  width: 100%;
  padding: 0;
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 5px;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
}
.all-container .container-wrapper div > .media .media-body {
  padding: 0.5rem 0;
}
.all-container .container-wrapper div > .media .readnow {
  display: flex;
  align-items: center;
  padding: 0 1rem;
  width: 100%;
}
.all-container .container-wrapper div > .media .readnow:hover {
  text-decoration: none;
}
.all-container .container-wrapper div > .media p {
  color: #949494;
  font-size: 12px;
}
.all-container .container-wrapper div > .media .title {
  padding: 0 10px;
  color: #212121;
  font-size: 14px;
  font-weight: 400;
}
.all-container .container-wrapper div > .media .title a {
  color: #212121;
}
.all-container .container-wrapper div > .media .box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 42px;
  height: 52px;
  border-radius: 5px;
  margin: 0.5rem 0;
}
.all-container .container-wrapper div > .media .box i {
  width: 32px;
  height: 24px;
  margin: 0 auto;
  display: flex;
}
.all-container .container-wrapper div > .media .box p {
  line-height: 1;
  font-size: 9px;
  margin: 0;
  text-align: center;
  top: 0;
  color: #FFFFFF;
  font-weight: 600;
}
.all-container .container-wrapper div > .media .box.blue {
  background: radial-gradient(109.09% 109.09% at 0% 0%, #2D9CDB 0%, #2F80ED 100%);
}
.all-container .container-wrapper div > .media .box.blue i {
  background: url("../../images/ws/pdf.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.green {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, rgba(85, 115, 0, 0.8) 76.95%);
}
.all-container .container-wrapper div > .media .box.green i {
  background: url("../../images/ws/link.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.yellow {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F2C74C 0%, #F2994A 46.86%);
}
.all-container .container-wrapper div > .media .box.yellow i {
  background: url("../../images/ws/flashcard.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.pink {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #F24CE1 0%, rgba(183, 7, 206, 0.9) 76.95%);
}
.all-container .container-wrapper div > .media .box.pink i {
  background: url("../../images/ws/video.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.lightgreen {
  background: radial-gradient(100% 100% at 0% 0%, #49E859 0%, #007D0C 100%);
}
.all-container .container-wrapper div > .media .box.lightgreen i {
  background: url("../../images/ws/notes.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.violet {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #BB6BD9 0%, #7B24CD 99.48%);
}
.all-container .container-wrapper div > .media .box.violet i {
  background: url("../../images/ws/mcq1.svg") center center no-repeat;
}
.all-container .container-wrapper div > .media .box.darkgreen {
  background: radial-gradient(142.42% 142.42% at 0% 0%, #4CF2E8 5.08%, #006963 70.31%);
}
.all-container .container-wrapper div > .media .box.darkgreen i {
  background: url("../../images/ws/mindmap.svg") center center no-repeat;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .store-list-layout {
    flex-direction: column;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks #searchResults {
    padding: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks #content-data-books-ebooks {
    padding: 0;
  }
}
.ebooks #content-data-books-ebooks .fadein-animated {
  border-radius: 10px;
}
.ebooks .books-list .topSchoolBooks {
  width: auto;
  display: flex;
  justify-content: start;
  margin: initial;
}
.ebooks .books-list .topSchoolBooks .image-wrapper {
  width: 155px;
  height: 200px;
  margin-right: 20px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .image-wrapper {
    width: 100px;
    height: 130px;
    margin-right: 15px;
  }
}
.ebooks .books-list .topSchoolBooks .image-wrapper .bookShadow {
  height: 100%;
}
.ebooks .books-list .topSchoolBooks .image-wrapper img {
  width: 100%;
  height: 100%;
}
.ebooks .books-list .topSchoolBooks .image-wrapper .uncover {
  width: 100%;
  height: 100%;
}
.ebooks .books-list .topSchoolBooks .image-wrapper .uncover p {
  line-height: normal;
  font-size: 12px;
}
.ebooks .books-list .topSchoolBooks .content-wrapper {
  margin-top: 0;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .left-div {
  display: flex;
  flex-direction: column;
  align-self: start;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-outer {
  display: inline-block;
  position: relative;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-outer:before {
  content: "\f005 \f005 \f005 \f005 \f005";
  font: var(--fa-font-regular);
  color: #FFC107;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-inner {
  position: absolute;
  top: 0;
  left: 0;
  white-space: nowrap;
  overflow: hidden;
  width: 0;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .stars-inner:before {
  content: "\f005 \f005 \f005 \f005 \f005";
  font: var(--fa-font-solid);
  color: #FFC107;
}
.ebooks .books-list .topSchoolBooks .content-wrapper .right-div .ratings {
  color: #949494;
  font-weight: normal;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper {
    display: block;
  }
}
.ebooks .books-list .topSchoolBooks .content-wrapper h6 {
  overflow: inherit;
  overflow: unset;
  text-overflow: unset;
  -webkit-line-clamp: unset;
  -webkit-box-orient: unset;
  font-size: 16px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper h6 {
    font-size: 13px;
  }
}
.ebooks .books-list .topSchoolBooks .content-wrapper .book-publisher-name {
  text-transform: none;
  margin-bottom: 10px;
  font-size: 14px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper .book-publisher-name {
    font-size: 12px;
  }
}
.ebooks .books-list .topSchoolBooks .content-wrapper .add_to_cart_btn {
  margin: 5px 0 0;
  bottom: 0;
  width: 150px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .topSchoolBooks .content-wrapper .add_to_cart_btn {
    margin: 0;
    width: 125px;
    padding: 2px;
  }
}
.ebooks .books-list .badge-overlay {
  position: relative;
  width: 65px;
  height: auto;
  left: unset;
  top: unset;
  margin: 0 0 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .badge-overlay {
    width: 55px;
  }
}
.ebooks .books-list .badge {
  border: 1px dashed #b8dcbb;
  background: transparent;
  color: #00A510;
  width: auto;
  height: auto;
  border-radius: 4px;
  line-height: normal;
  font-weight: 500;
  font-size: 12px;
  padding: 3px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .books-list .badge {
    font-size: 11px;
  }
}
.ebooks h1 {
  font-size: 40px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebooks h1 {
    font-size: 30px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks h1 {
    font-size: 20px;
  }
}
.ebooks .share-ebooks-page {
  width: 35px;
  height: 35px;
  cursor: pointer;
}
.ebooks .share-ebooks-page i {
  font-size: 20px;
}
.ebooks .ebooks_filter {
  background: #FFFFFF;
  border-radius: 7px;
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1), 0 1px 10px 0 rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 77px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebooks .ebooks_filter {
    position: relative;
    top: unset;
    background: transparent;
  }
}
.ebooks .ebooks_filter .filter-icon-show-hide {
  position: absolute;
  top: -12px;
  right: 10px;
}
.ebooks .ebooks_filter select {
  font-style: italic;
  font-size: 14px;
  height: 45px;
  background: #FAFAFA;
  color: #949494;
  box-sizing: border-box;
  border: 2px solid #949494;
  box-shadow: 0 0 10px #0000001A;
}
@media (max-width: 575.98px) {
  .ebooks .ebooks_filter select {
    font-size: 13px;
    height: 40px;
  }
}
.ebooks .ebooks_filter select:hover,
.ebooks .ebooks_filter select:focus {
  border-color: #FFD602;
  color: #212121;
}
.ebooks .ebooks_filter select.background-bg {
  background-image: linear-gradient(#FFD602, #FFD602);
  border-color: #FFD602;
  color: #212121;
  font-style: normal;
  font-weight: 400;
}
.ebooks .ebooks_filter select option {
  font-style: normal;
  font-weight: 400;
}
.ebooks .ebooks_filter .hiddenFilters {
  display: none;
}
.ebooks #infiniteLoading .loading-div p {
  position: relative;
  letter-spacing: 1px;
  color: #2C3E50;
  font-size: 15px;
}
.ebooks #infiniteLoading .loading-div img {
  width: 120px;
  margin-top: -55px;
}
.ebooks #infiniteLoading .loading-div .alert {
  font-size: 14px;
}
.ebooks #resetFilter {
  color: #949494;
  border: 0;
  padding: 0;
  font-weight: normal;
}
.reach-us .card-modifier {
  flex-direction: unset;
}
.reach-us .card-modifier .reach-us-message h1 {
  font-size: 45px;
  margin-bottom: 10px;
  opacity: 0.7;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .reach-us .card-modifier .reach-us-message h1 {
    font-size: 35px;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .reach-us .card-modifier .reach-us-message h1 {
    font-size: 30px;
  }
}
.reach-us .card-modifier .reach-us-message h5 {
  opacity: 0.7;
  font-weight: 300;
}
.reach-us .card-modifier .reach-us-message img {
  margin-top: 15px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .reach-us .card-modifier .reach-us-message img {
    width: 100%;
  }
}
.reach-us .card-modifier .reach-us-form {
  max-width: 400px;
  min-height: 350px;
  max-height: 350px;
  margin-top: -7rem;
  border-radius: 20px;
  box-shadow: 0 0 10px #0000001A;
  background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #ffd000 0%, #ff5700 100%);
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .reach-us .card-modifier .reach-us-form {
    min-height: 320px;
    max-height: 320px;
    margin-top: 2rem;
    margin-bottom: -4rem;
  }
}
.reach-us .card-modifier .reach-us-form .form-control-modifier {
  font-size: 14px;
}
.reach-us .card-modifier .reach-us-form .send-btn {
  background: #FF9901;
  border-color: #FFFFFF;
  box-shadow: 0 -4px 10px #0000001A;
  transition: all 0.2s linear;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
}
.reach-us .card-modifier .reach-us-form .send-btn:hover {
  border-color: transparent;
}
.view-more {
  margin: 0 auto;
  display: flex;
  color: #FFFFFF !important;
  text-align: center;
  background: #F79420 !important;
  border: none;
  width: 200px;
  height: 40px;
  border-radius: 50px;
  display: block;
}
.wonderslate_main .ebooks {
  padding-bottom: 0 !important;
  padding-top: 0 !important;
}
.wonderslate_main .ebooks #content-data-books-ebooks {
  margin-top: 0 !important;
}
.wonderslate_main .ebooks #searchResults {
  padding-left: 0;
  padding-right: 0;
}
.wonderslate_main .ebooks .banner-ws img {
  border-radius: 0 !important;
}
.wonderslate_main .ebooks .mobile {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .mobile {
    display: flex;
    margin-top: 60px !important;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .desktop {
    display: none;
  }
}
.wonderslate_main .ebooks .section-headerless {
  margin-top: 50px;
  margin-right: 0;
  margin-left: 0;
}
.wonderslate_main .ebooks .section-with-header {
  margin-top: 60px !important;
}
.wonderslate_main .ebooks .card {
  border-radius: 16px;
  border: none;
  padding: 25px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card {
    padding: 20px;
  }
  .wonderslate_main .ebooks .card div {
    flex-direction: column-reverse;
    justify-content: center !important;
    text-align: center;
  }
}
.wonderslate_main .ebooks .card .text-and-shop {
  align-self: flex-end;
  padding-right: 10px;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card .text-and-shop {
    margin-top: 10px;
    padding-right: 0;
    width: 100%;
  }
}
.wonderslate_main .ebooks .card .text-and-shop p {
  font-family: 'Poppins', sans-serif !important;
  font-style: normal;
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
  color: #212121;
}
.wonderslate_main .ebooks .card .text-and-shop button {
  width: 122px;
  height: 34px;
  border: none;
  border-radius: 16px;
  text-align: center;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card .text-and-shop button {
    width: 100px;
  }
}
.wonderslate_main .ebooks .card .text-and-shop button a {
  color: #212121 !important;
}
.wonderslate_main .ebooks .card img {
  min-height: 180px;
  min-width: 130px;
  height: 180px;
  width: 130px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .card img {
    width: 150px;
    height: 200px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .card img {
    height: 100px;
    width: 80px;
    margin: 0 auto;
  }
}
.wonderslate_main .ebooks .new-release {
  background: linear-gradient(132.37deg, #5FD0E2 17.22%, rgba(95, 208, 226, 0) 130.82%, #5FD0E2 130.82%);
}
.wonderslate_main .ebooks .new-release button {
  background: #65C0CE;
}
.wonderslate_main .ebooks .book-of-day {
  background: linear-gradient(145.24deg, #B2DCBC 38.27%, rgba(178, 220, 188, 0) 123.5%);
}
.wonderslate_main .ebooks .book-of-day button {
  background-color: #86B792;
}
.wonderslate_main .ebooks .trending-now {
  background: linear-gradient(134.65deg, #FACED6 5.62%, rgba(250, 206, 214, 0) 122.6%);
}
.wonderslate_main .ebooks .trending-now button {
  background-color: #FEB8C5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .shop-now-cards {
    margin-bottom: 20px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .wonderslate_main .ebooks .shop-now-cards {
    padding: 0 10px !important;
  }
}
.wonderslate_main .ebooks .ebooks_filter {
  margin-left: auto;
  margin-right: auto;
  box-shadow: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .wonderslate_main .ebooks .ebooks_filter {
    width: 100%;
    padding-left: 0;
    padding-right: 0;
    padding-top: 0 !important;
  }
}
.wonderslate_main .ebooks .ebooks_filter select {
  height: 40px !important;
  border-radius: 50px;
  background: #FFFFFF;
  border-width: 1px;
  border-color: #F79420;
  box-shadow: none;
}
.wonderslate_main .ebooks .ebooks_filter select:hover,
.wonderslate_main .ebooks .ebooks_filter select:focus {
  border-color: #F79420;
}
.wonderslate_main .ebooks .ebooks_filter select.background-bg {
  background-image: none;
  background-color: #F79420;
  border-color: #F79420;
}
.custom_container {
  width: calc(100% - 15%);
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .custom_container {
    width: calc(100% - 5%);
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .container-fluid {
    width: 100%;
  }
}
.ebook_detail .image_wrapper .book_image {
  width: 75%;
  height: 450px;
  position: relative;
  z-index: 10;
  margin: 0 auto;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image {
    width: 100%;
  }
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image {
    height: 350px !important;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow {
  height: 450px;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image .bookShadow {
    height: 350px !important;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow img {
  width: 100%;
  height: 450px;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image .bookShadow img {
    height: 350px !important;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow:after {
  width: 3px;
}
.ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
  height: 450px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #F79420;
}
@media (min-width: 992px) and (max-width: 1199.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 380px;
  }
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 300px;
  }
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 200px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 400px;
  }
}
@media (max-width: 330px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail {
    height: 350px !important;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail p {
  text-align: center;
  text-transform: capitalize;
  font-size: 16px;
  font-style: italic;
  color: #FFFFFF;
  padding: 60% 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .uncoverdetail p {
    padding: 50% 0;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow .book-tag {
  position: absolute;
  z-index: 10;
  color: #FFFFFF;
  background-color: #27AE60;
  padding: 5px 15px;
  bottom: 50px;
  left: -5px;
  text-align: left;
  line-height: normal;
  margin-bottom: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  -webkit-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.25);
}
@media (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .image_wrapper .book_image .bookShadow .book-tag {
    bottom: 30px;
  }
}
.ebook_detail .image_wrapper .book_image .bookShadow .book-tag span {
  font-size: 12px;
  display: block;
}
.ebook_detail .image_wrapper .book_image .bookShadow .book-tag:after {
  content: ' ';
  position: absolute;
  width: 0;
  height: 0;
  left: 0;
  top: 100%;
  border-width: 2px 3px;
  border-style: solid;
  border-color: #27AE60 #27AE60 transparent transparent;
}
.ebook_detail .image_wrapper .book_image_bottom {
  margin-top: -12px;
  position: relative;
  z-index: 11;
}
.ebook_detail .image_wrapper .book_image_bottom img {
  width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .book_info .book_description h2 {
    font-size: 22px;
  }
}
.ebook_detail .book_info .book_description .book-publisher-name {
  text-transform: capitalize;
  color: #6C757D;
}
.ebook_detail .book_info .book_description .book-desc {
  color: #949494;
}
.ebook_detail .book_info .book_description .book-desc a.exp:hover {
  text-decoration: underline;
}
.ebook_detail .book_info .book_description .book_briefDetail {
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  background: #fff;
  border-radius: 4px;
  margin-bottom: 1rem;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier {
  font-size: 14px;
  height: 40px;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier span {
  font-weight: 600;
  display: inline-block;
  width: 95%;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier span svg {
  margin-right: 10px;
}
.ebook_detail .book_info .chapter_lists .btn-outline-primary-modifier:after {
  color: #C9302C;
  vertical-align: 0.15em;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu {
  width: 100%;
  padding: 0;
  transform: translate3d(0, 40px, 0) !important;
  max-height: 330px;
  overflow-y: scroll;
  z-index: 99;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu .dropdown-item {
  white-space: pre-wrap;
  color: #C9302C;
  padding-top: 7px;
  padding-bottom: 7px;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu .dropdown-item:first-child {
  border-top-left-radius: 5px;
}
.ebook_detail .book_info .chapter_lists .dropdown-menu .dropdown-item:last-child {
  border-bottom-left-radius: 5px;
}
.ebook_detail .book_info .book_resources .card-modifier {
  height: 100%;
}
.ebook_detail .book_info .book_resources .card-modifier p {
  font-size: 12px;
  color: #949494;
}
.ebook_detail .book_info .book_resources .card-modifier p img {
  padding-right: 5px;
}
.ebook_detail .book_info .book_resources.book_adding .card-modifier p {
  color: #C9302C;
}
.ebook_detail .book_info .book_price .offer_price {
  font-family: 'Rubik', sans-serif !important;
  font-size: 22px;
  display: block;
}
.ebook_detail .book_info .book_price .list_price {
  font-family: 'Rubik', sans-serif !important;
  font-size: 28px;
  color: #FF4B33;
}
.ebook_detail .book_info .book_variants a {
  width: 100%;
}
.ebook_detail .book_info .book_variants a.card.active {
  background-color: #FFF0CC;
  border-color: #F79420;
}
.ebook_detail .book_info .book_variants a.card:hover {
  border-color: #F79420;
}
.ebook_detail .book_info .book_variants a.card .card-body {
  min-height: 130px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .book_info .book_variants a.card .card-body {
    min-height: 140px;
  }
}
.ebook_detail .book_info .book_variants a.card .list_price {
  font-size: 18px;
  color: #FF4B33;
  padding-right: 0.25rem;
  line-height: normal;
}
.ebook_detail .book_info .book_variants a.card .offer_price {
  font-size: 20px;
  color: #212121;
  line-height: normal;
}
.ebook_detail .book_info .book_variants a.card h5 {
  padding-top: 5px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .book_info .book_variants a.card h5 {
    font-size: 1rem;
  }
}
.ebook_detail .book_info .book_variants a.card h5 span.badge {
  display: initial;
  border-radius: 50px;
  font-size: 10px;
  padding: 3px 5px;
  font-weight: normal;
  background: var(--info);
  position: absolute;
  right: 1px;
  top: 1px;
  height: auto;
  width: 60px;
}
@media (max-width: 575.98px) {
  .ebook_detail .book_info .book_buttons {
    margin-bottom: 10px;
  }
}
.ebook_detail .book_info .book_buttons .col {
  padding: 0;
}
.ebook_detail .book_info .book_buttons .col a {
  width: 90%;
  height: 50px;
  font-size: 14px;
  font-weight: 500;
  line-height: 2.5;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .ebook_detail .book_info .book_buttons .col a {
    height: auto;
    line-height: normal;
  }
}
.ebook_detail .book_info .book_buttons .col a:hover {
  box-shadow: 0 0.2rem 1rem #0000001A;
}
.ebook_detail .book_info .couponInput #inputManualCoupon {
  width: 30%!important;
  border-color: #C9302C;
}
@media only screen and (max-width: 767px) {
  .ebook_detail .book_info .couponInput #inputManualCoupon {
    width: 45%!important;
  }
}
.ebook_detail .book_info .couponInput #manualCoupon {
  width: 30%!important;
  margin-left: 24px;
}
@media only screen and (max-width: 540px) {
  .ebook_detail .book_info .couponInput #manualCoupon {
    margin-left: 24px !important;
  }
}
@media only screen and (max-width: 414px) {
  .ebook_detail .book_info .couponInput #manualCoupon {
    margin-left: 18px !important;
  }
}
@media only screen and (max-width: 767px) {
  .ebook_detail .book_info .couponInput #manualCoupon {
    margin-left: 15px;
    width: 45%!important;
  }
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart {
  color: #6C757D;
  padding: 5px 0 0;
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart .first-table-cart {
  width: 110px;
  display: inline-block;
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart .second-table-cart {
  width: 50px;
  display: inline-block;
}
.ebook_detail .details-book-about-cart .wrp-details-about-cart .mrp_price {
  font-family: 'Rubik', sans-serif !important;
}
.ebook_detail .nav-tabs-book-details ul.nav li a {
  position: relative;
  border: 1px solid transparent;
  border-bottom-color: #e1e1e1;
  margin-right: 12px;
  font-size: 16px;
  color: #6C757D;
}
.ebook_detail .nav-tabs-book-details ul.nav li a.active {
  color: #C9302C;
  border: 1px solid #C9302C;
  border-bottom-width: 2px;
}
.ebook_detail .nav-tabs-book-details ul.nav li a.active:hover {
  background-color: transparent;
  border-bottom-color: #C9302C;
}
.ebook_detail .nav-tabs-book-details ul.nav li a:hover {
  background-color: #ededed;
  border-bottom-width: 2px;
  border-bottom-color: #bababa;
}
@media (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .ebook_detail .nav-tabs-book-details ul.nav li a {
    font-size: 14px;
    padding: 5px 15px;
  }
}
@media (max-width: 575.98px) {
  .ebook_detail .nav-tabs-book-details ul.nav li a {
    padding: 4px 8px 4px 8px;
    margin-right: 6px;
    font-size: 12px;
    font-weight: 500;
  }
}
@media (max-width: 330px) {
  .ebook_detail .nav-tabs-book-details ul.nav li a {
    padding: 4px 5px;
    font-size: 11px;
  }
}
.ebook_detail .nav-tabs-book-details .wrp-details-about-cart {
  color: #444444;
}
.ebook_detail .nav-tabs-book-details .wrp-details-about-cart .first-table-cart {
  font-weight: 600;
}
.ebook_detail .nav-tabs-book-details #book-desc p {
  margin-bottom: 5px;
}
.ebook_detail .page-row-filter .breadcrumb {
  border-bottom: 1px solid #ccc;
  background-color: transparent;
  padding: 0 0 7px;
  border-radius: 0;
  margin-bottom: 16px;
}
.ebook_detail .page-row-filter .breadcrumb li.breadcrumb-item {
  font-size: 12px;
  color: #6C757D;
}
.ebook_detail .page-row-filter .breadcrumb li.breadcrumb-item a {
  font-size: 12px;
  color: #6C757D;
}
.ebook_detail .page-row-filter .breadcrumb li.breadcrumb-item + .breadcrumb-item::before {
  content: ">";
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes {
  margin: 0 0 20px;
  padding: 10px 0;
  border-bottom: 1px solid #ccc;
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes li {
  float: left;
  list-style: none;
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes li a {
  display: inline-block;
  padding: 2px 12px;
  position: relative;
  font-size: 12px;
  color: #6C757D;
}
.ebook_detail .page-row-filter ul.page-arrow-like-homes li a:after {
  content: "\f105";
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  position: absolute;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  top: 4px;
  right: -3px;
}
#couponmodal {
  overflow-y: scroll;
  height: 360px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1000%26quot%3b)' fill='none'%3e%3cuse xlink:href='%23SvgjsSymbol1007' x='0' y='0'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsSymbol1007' x='720' y='0'%3e%3c/use%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1000'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cpath d='M-1 0 a1 1 0 1 0 2 0 a1 1 0 1 0 -2 0z' id='SvgjsPath1003'%3e%3c/path%3e%3cpath d='M-3 0 a3 3 0 1 0 6 0 a3 3 0 1 0 -6 0z' id='SvgjsPath1001'%3e%3c/path%3e%3cpath d='M-5 0 a5 5 0 1 0 10 0 a5 5 0 1 0 -10 0z' id='SvgjsPath1005'%3e%3c/path%3e%3cpath d='M2 -2 L-2 2z' id='SvgjsPath1002'%3e%3c/path%3e%3cpath d='M6 -6 L-6 6z' id='SvgjsPath1004'%3e%3c/path%3e%3cpath d='M30 -30 L-30 30z' id='SvgjsPath1006'%3e%3c/path%3e%3c/defs%3e%3csymbol id='SvgjsSymbol1007'%3e%3cuse xlink:href='%23SvgjsPath1001' x='30' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='30' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='30' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='30' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='30' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='30' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='30' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='30' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='30' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='30' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='90' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1006' x='90' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)' stroke-width='3'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='90' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='90' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='90' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='90' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='90' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='90' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='90' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='90' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='150' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='150' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='150' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='150' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='150' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='210' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='210' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='210' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='210' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1006' x='210' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)' stroke-width='3'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='210' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='210' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='270' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='270' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='270' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='330' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='330' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='330' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='330' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='330' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='330' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='330' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='330' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='330' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='330' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='390' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='390' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='390' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='390' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='390' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='390' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='390' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='390' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='390' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='390' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='450' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='450' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='450' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='450' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='510' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='510' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='510' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='510' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='510' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='510' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='510' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='570' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='570' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='570' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1002' x='570' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='570' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='630' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='630' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='630' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='630' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='690' y='30' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1001' x='690' y='90' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='690' y='150' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='690' y='210' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1006' x='690' y='270' stroke='rgba(223%2c 208%2c 208%2c 1)' stroke-width='3'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1003' x='690' y='330' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='690' y='390' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1005' x='690' y='450' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='690' y='510' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3cuse xlink:href='%23SvgjsPath1004' x='690' y='570' stroke='rgba(223%2c 208%2c 208%2c 1)'%3e%3c/use%3e%3c/symbol%3e%3c/svg%3e");
}
#couponmodal .availCoupon {
  border: 2px dashed red;
}
#couponmodal .wraps {
  border-radius: 3px;
  padding: 3px;
}
lottie-player {
  position: absolute;
  top: 20px;
}
.radio-toolbar input[type="radio"] {
  opacity: 0;
  position: fixed;
  width: 0;
}
.radio-toolbar label {
  display: block;
  background-color: #4c4;
  padding: 10px 20px;
  font-family: sans-serif, Arial;
  font-size: 16px;
  border-radius: 8px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}
.radio-toolbar input[type="radio"]:checked + label {
  background-color: lightgreen;
  color: #fff;
}
.orderDetailsModal .image-wrapper-od {
  display: grid;
  grid-gap: 1rem;
  padding: 16px;
  grid-template-columns: 30% 70%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .orderDetailsModal .image-wrapper-od {
    grid-template-columns: repeat(2, 1fr);
  }
}
.orderDetailsModal .image-wrapper-od .od-img .uncoverdetail {
  height: 150px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  background: #F79420;
  width: 120px;
}
.orderDetailsModal .image-wrapper-od .od-img .uncoverdetail p {
  text-align: center;
  text-transform: capitalize;
  font-size: 16px;
  font-style: italic;
  color: #FFFFFF;
  padding: 60% 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .orderDetailsModal .image-wrapper-od .od-img .uncoverdetail p {
    padding: 50% 0;
  }
}
.orderDetailsModal .image-wrapper-od img {
  height: 150px;
  border-radius: 5px;
  width: 100px;
}
.orderDetailsModal .orderDetails-wrapper {
  padding-left: 16px;
}
.orderDetailsModal .orderDetails-wrapper .first-table-cart {
  width: 150px !important;
}
.orderDetailsModal .orderDetails-wrapper .second-table-cart {
  width: 40px !important;
}
.percentageOff {
  background: #01b901;
  padding: 2px;
  width: 100px;
  text-align: center;
  color: #fff;
  border-radius: 2px;
}
.star-rating i {
  color: #F79420;
}
.shimmer {
  -webkit-mask: linear-gradient(-40deg, #000 30%, #0005, #000 60%) right / 300% 100%;
  animation: shimmer 2.5s infinite;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.affiliationPrices {
  margin-top: 10px;
  background: #fff;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.15);
}
@media (max-width: 768px) {
  .affiliationPrices {
    width: 100%;
  }
}
.affiliationPrices_title p {
  font-weight: 500;
}
.affiliationPrices .affiliationLinks {
  display: flex;
  gap: 1rem;
  margin-top: 10px;
}
.affiliationPrices .affiliationLinks .fieldSet {
  position: relative;
  border: 1px solid orange;
  padding: 8px;
  width: 175px;
  border-radius: 5px;
}
.affiliationPrices .affiliationLinks .fieldSet .ctaPrice {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
}
@media (max-width: 768px) {
  .affiliationPrices .affiliationLinks .fieldSet {
    width: 150px;
  }
}
.affiliationPrices .affiliationLinks .fieldSet:hover {
  border: 1px solid orange;
}
.affiliationPrices .affiliationLinks .fieldSet span img {
  width: 60px;
}
.affiliationPrices .affiliationLinks .fieldSet span img.flipkartLogo {
  width: 70px;
  position: relative;
  z-index: 2;
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend {
  position: absolute;
  top: 0;
  margin: -9px 0 0 -0.5rem;
  background: #fff;
  margin-left: 65px;
  width: 80px;
  text-align: center;
  z-index: 1;
  font-size: 18px;
  font-weight: 500;
  color: #000;
}
@media (max-width: 768px) {
  .affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend {
    width: 63px;
    font-size: 14px;
  }
}
.affiliationPrices .affiliationLinks .fieldSet .fieldSet_legend span {
  margin-left: -4px;
}
.affiliationLinks .fieldSet:hover {
  background: #FFF0CC;
}
.affiliationLinks .fieldSet:hover .fieldSet_legend {
  background: linear-gradient(to bottom, #fff, #FFF0CC);
}
.affiliationLinksLoader {
  border: 1px solid #0003;
  width: 140px;
  height: 50px;
  border-radius: 5px;
  background: linear-gradient(to right, #F6F6F6 8%, #F0F0F0 18%, #F6F6F6 33%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.affiliationLinksLoader p {
  font-size: 12px;
  font-weight: 500;
  color: #0008;
  text-align: center;
  line-height: initial;
}
.aa:after {
  position: absolute;
  margin-left: 0.1rem;
  content: ' ...';
  animation: loading steps(4) 2s infinite;
  clip: rect(auto, 0px, auto, auto);
}
@keyframes loading {
  to {
    clip: rect(auto, 20px, auto, auto);
  }
}
.afPrice {
  font-size: 24px;
  margin-bottom: 12px;
  color: #000;
  font-weight: 500;
}
.dots {
  margin-left: 5px;
}
.dots .dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  margin-right: 3px;
}
.dots .dot:nth-child(1) {
  animation: dotsJump 0.8s 0.1s ease infinite;
}
.dots .dot:nth-child(2) {
  animation: dotsJump 0.8s 0.2s ease infinite;
}
.dots .dot:nth-child(3) {
  animation: dotsJump 0.8s 0.3s ease infinite;
}
.span-1 {
  color: rgba(0, 0, 0, 0.4);
}
.span-2 {
  color: rgba(0, 0, 0, 0.6);
  font-weight: bolder;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ctaPrice {
  margin-top: 3px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 12px;
}
@keyframes dotsJump {
  0% {
    transform: translate3d(0, 0, 0);
  }
  50% {
    transform: translate3d(0, 5px, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
.subscription {
  min-width: 350px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (max-width: 768px) {
  .subscription {
    min-width: 100%;
  }
}
.subscription__sec {
  margin-bottom: 1rem;
}
.subscription__sec label {
  display: block;
}
.subscription__sec select {
  padding: 8px 5px;
  border: 1px solid rgba(0, 0, 0, 0.4);
  border-radius: 4px;
  display: block;
  width: 100%;
}
.subscription__sec select:focus-visible {
  border: 1px solid;
  border-color: red !important;
  outline: none;
}
.subscription__sec-3 a {
  display: flex;
  text-align: center;
  justify-content: center;
}
.freeChapterDiv .freeChapterLink {
  background: #fff !important;
  border-radius: 5px !important;
  border: 1px solid #F79420 !important;
  padding: 10px;
  color: #000;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .freeChapterDiv {
    width: 100% !important;
  }
}
.book_variants {
  display: grid;
  grid-gap: 1rem;
  grid-template-columns: 240px 240px 240px;
  margin-top: 1rem;
  background: #fff;
  padding: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  margin-bottom: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants {
    grid-template-columns: auto;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}
.book_variants .book_variant_card {
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  padding: 5px 8px;
  background: #fff;
  margin-bottom: 1rem;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card {
    width: 66%;
    margin-bottom: 0;
    min-height: auto;
  }
}
.book_variants .book_variant_card-body {
  display: flex;
  flex-direction: column;
}
.book_variants .book_variant_card-body .book_variant-name {
  position: relative;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-name {
    margin-bottom: 0 !important;
  }
}
.book_variants .book_variant_card-body .book_variant-name .variantType {
  display: flex;
  align-items: center;
  color: #E61F2A;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-name .variantType {
    font-size: 12px;
  }
}
.book_variants .book_variant_card-body .book_variant-name .variantType img {
  width: 20px;
  margin-left: 5px;
}
.book_variants .book_variant_card-body .book_variant-name span.badge {
  display: initial;
  border-radius: 50px;
  font-size: 10px;
  padding: 3px 5px;
  font-weight: normal;
  background: var(--info);
  position: absolute;
  right: 1px;
  top: 1px;
  height: auto;
  width: 60px;
}
.book_variants .book_variant_card-body .offerDiv .savePrice {
  font-size: 13px;
  margin-top: 10px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .offerDiv .savePrice {
    margin-top: 3px;
  }
}
.book_variants .book_variant_card-body .book_variant-price {
  display: flex;
  gap: 8px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-price {
    margin-top: 10px;
  }
}
.book_variants .book_variant_card-body .book_variant-price .offer_price {
  font-size: 24px;
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant_card-body .book_variant-price .offer_price {
    font-size: 18px;
  }
}
.book_variants .book_variant .addtoCartBtn {
  border-radius: 3px;
  margin-bottom: 0!important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .book_variants .book_variant .addtoCartBtn {
    width: 38% !important;
    margin-bottom: 0 !important;
  }
}
.image_wrapper {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  flex-direction: row-reverse;
}
.featureIcons {
  width: 22%;
  background: #fff;
  border-radius: 5px;
  display: flex;
  height: 450px;
}
.featureIcons ul {
  list-style: none;
  text-align: justify;
  padding-left: inherit;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}
.featureIcons ul li {
  padding: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.featureIcons ul li p {
  font-size: 12px;
}
.featureIcons ul li img {
  width: 40px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .image_wrapper {
    flex-direction: unset;
  }
  .featureIcons {
    width: 18%;
    display: flex;
    height: unset;
  }
  /*Cover image styles*/
  .image_wrapper .book_image {
    height: auto !important;
  }
  .image_wrapper .book_image .bookShadow {
    height: 100% !important;
  }
  .image_wrapper .book_image .bookShadow .image-wrapper img {
    width: 100%;
    height: 100%;
  }
  .freeChapterDiv .freeChapterLink {
    display: block;
    text-align: center;
    box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
  }
  .freeChapterDiv .freeChapterLink:active {
    transform: scale(0.9);
  }
}
.my_books .search-icon-lib {
  width: 35px;
  height: 35px;
  cursor: pointer;
  border-width: 2px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .search-icon-lib {
    width: 32px;
    height: 32px;
  }
}
.my_books .search-box #search-book,
.my_books .search-box .submit-search-btn {
  height: 40px;
}
.my_books .search-box ul.typeahead {
  top: 54px !important;
  border-color: #C9302C;
  box-shadow: 0 1px 4px #0000001A;
}
.my_books .username p.total-books {
  font-size: 18px;
  font-weight: 500;
  color: #C9302C;
}
.my_books #subjectFilter .dropdown {
  font-size: 13px;
}
.my_books #subjectFilter .dropdown #sortBy {
  border-radius: 5px;
  color: #C9302C;
  font-size: 13px;
  border-color: #C9302C;
}
.my_books .select-institute-dropdown {
  width: 250px;
}
.my_books .select-institute-dropdown .dropdown-toggle {
  height: 44px;
  width: 250px;
  text-align: left;
  background-color: #C9302C !important;
  box-shadow: inset 0 2px 4px #0000001A;
  color: #FFFFFF !important;
  border-radius: 50px;
  border: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .select-institute-dropdown .dropdown-toggle {
    font-size: 13px;
  }
}
.my_books .select-institute-dropdown .dropdown-toggle:after {
  position: absolute;
  right: 15px;
  top: 20px;
}
.my_books .select-institute-dropdown .dropdown-toggle #selectedInstitute {
  width: 200px;
  position: relative;
  display: block;
  word-break: break-word;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.my_books #institute-list {
  transform: translate3d(0px, 48px, 0px) !important;
  background: #FFFFFF;
  box-shadow: 0 2px 10px #0000001A;
  border-radius: 12px;
  width: 100%;
  z-index: 991;
}
.my_books #institute-list li.dropdown-item {
  white-space: pre-wrap;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books #institute-list li.dropdown-item {
    font-size: 13px;
  }
}
.my_books #institute-list li.dropdown-item:hover {
  cursor: pointer;
  color: #C9302C;
}
.my_books #institute-list li.dropdown-item:focus,
.my_books #institute-list li.dropdown-item:active {
  background-color: transparent;
  outline: 0;
}
.my_books .queue-list-btn {
  position: relative;
  z-index: 99;
  margin-bottom: -10px;
}
.my_books .queue-list-btn a {
  background: #C9302C;
  box-shadow: 0 2px 4px #0000001A !important;
  border-radius: 50px;
  color: #FFFFFF;
  margin-right: 0;
  font-size: 14px;
  font-weight: 500;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .my_books .queue-list-btn a {
    margin-right: 0;
  }
}
@media (max-width: 575.98px) {
  .my_books .queue-list-btn a {
    font-size: 12px;
  }
}
.my_books .books-content-wrapper {
  margin-top: 2rem;
}
.my_books .access_code h4 {
  color: #444444;
}
.my_books .access_code .submit_icon span {
  font-size: 70px;
}
.my_books .access_code .submit_icon p {
  color: #6C757D;
}
.my_books .access_code .fetch_icon span,
.my_books .access_code .invalid_icon span {
  opacity: 0.7;
}
.my_books .showMore,
.my_books .showLess {
  float: right;
  margin-right: 35px;
  font-size: 14px;
  color: #C9302C;
  cursor: pointer;
}
@media (min-width: 768px) and (max-width: 991.98px), (min-width: 992px) and (max-width: 1199.98px) {
  .my_books .showMore,
  .my_books .showLess {
    margin-right: 0;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .showMore,
  .my_books .showLess {
    display: none;
  }
}
.my_books #content-data-books h4 {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 10px;
}
.my_books #content-data-institute-books h4,
.my_books #institute-recent-read-books h4,
.my_books #content-data-search-books h4,
.my_books #content-data-books-queue h4 {
  color: #17A2B8;
  font-weight: 400;
  font-size: 18px;
  padding: 10px 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books #content-data-institute-books h4,
  .my_books #institute-recent-read-books h4,
  .my_books #content-data-search-books h4,
  .my_books #content-data-books-queue h4 {
    font-size: 16px;
  }
}
.my_books .package_book_collapse {
  position: relative;
  margin: 0;
}
.my_books .package_books {
  background: #FFFFFF;
  box-shadow: 0 0 10px #0000001A;
  border-radius: 10px;
  position: relative;
  left: 0;
  right: 0;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  z-index: 99;
}
.my_books .package_books .package_book_list {
  padding-bottom: 0;
}
.my_books .package_books .package_book_list:last-child {
  padding-bottom: 0;
}
.my_books .package_books .package_book_list a {
  color: #212121;
  width: 85px;
  font-size: 11px;
  margin: 0 10px;
}
.my_books .package_books .package_book_list a:hover {
  color: #C9302C;
}
.my_books .package_books .package_book_list a span {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.my_books #content-data-books-queue .no-books-available p {
  font-size: 15px;
}
.my_books #content-data-books-queue .card-body .card-text:hover {
  text-decoration: none;
  color: #444444;
}
.my_books #content-data-books-queue .card img {
  border-radius: 4px;
}
.my_books .no-books-available p {
  color: #949494;
  line-height: normal;
}
.my_books .no-books-available .click-here-link {
  border: none;
  color: #FFFFFF;
  font-weight: 700;
  font-size: 15px;
}
.my_books #loadMoreButton,
.my_books #loadMoreButtonFree,
.my_books #loadMoreButtonPaid,
.my_books #loadMoreButtonSelf {
  text-align: center;
}
.my_books #loadMoreButton button,
.my_books #loadMoreButtonFree button,
.my_books #loadMoreButtonPaid button,
.my_books #loadMoreButtonSelf button {
  width: 200px;
  height: 40px;
  border-radius: 16px;
  border: none;
}
.my_books #infiniteLoading .loading-div p {
  position: relative;
  letter-spacing: 1px;
  color: #2C3E50;
  font-size: 15px;
}
.my_books #infiniteLoading .loading-div img {
  width: 120px;
  margin-top: -55px;
}
.my_books #infiniteLoading .loading-div .alert {
  font-size: 14px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books .recent-read-books-list {
    overflow: auto;
    flex-wrap: nowrap;
  }
}
.my_books .has-latest-books .new-book .card {
  border: 2px solid #C9302C;
  position: relative;
  overflow: hidden;
}
.my_books .has-latest-books .new-book .card .lib-showcase {
  position: relative;
}
.my_books .has-latest-books .new-book .card .lib-showcase:after {
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  z-index: 10;
  display: block;
  width: 25%;
  height: 100%;
  background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
  -webkit-transform: skewX(-20deg);
  transform: skewX(-20deg);
  -webkit-animation: shine 2s infinite;
  animation: shine 2s infinite;
}
.libwonder .my_books {
  margin-top: 0 !important;
}
.libwonder #goBack {
  display: none;
}
.libwonder .library .tab-content {
  margin-top: 0;
}
.libwonder .library .tab-content .card {
  background-color: #FFFFFF !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .libwonder .library .tab-content .card img {
    height: 192px !important;
  }
}
.libwonder #institute-list {
  top: 0 !important;
}
.libwonder .select-institute-dropdown .dropdown-toggle {
  background-color: #C9302C !important;
}
.libwonder .select-institute-dropdown .dropdown-toggle.generate {
  display: block !important;
  background-color: #C9302C !important;
}
.libwonder .no-books-available .click-here-link {
  color: #FFFFFF !important;
  background: #C9302C !important;
  font-weight: 400;
}
.libwonder .card-body .card-text {
  height: auto !important;
}
@media (max-width: 575px) {
  .wonderslate_main div#button-container div a,
  .wonderslate_main #allBooks {
    width: 100%;
    font-size: 14px !important;
    margin-right: 10px;
  }
}
.wonderslate_main #paidBooks,
.wonderslate_main #freeBooks {
  margin-right: 4px;
  color: #17A2B8;
  font-weight: 400;
  font-size: 18px;
  cursor: pointer;
  padding: 5px 5px;
  border: 1px solid;
  border-radius: 10px;
}
.wonderslate_main #allBooks {
  margin-right: 4px;
  font-weight: 400;
  cursor: pointer;
  border: 1px solid;
  font-size: 15px !important;
  padding: 5px 15px !important;
  border-radius: 30px !important;
  background-color: #17A2B8 !important;
  color: white !important;
}
.wonderslate_main div#button-container div a {
  font-size: 15px !important;
  padding: 5px 15px !important;
  border-radius: 30px !important;
}
.wonderslate_main div#button-container div {
  margin-right: 2%;
}
.wonderslate_main div#button-container div a#total-freebooks-of-user,
.wonderslate_main div#button-container div a#total-paidbooks-of-user {
  padding: 5px !important;
}
.wonderslate_main .mystyle {
  background-color: #17A2B8 !important;
  color: white !important;
  font-size: 25px;
}
.wonderslate_main div#button-inner-container p {
  padding-left: 10px;
}
.wonderslate_main p#total-allbooks-of-user {
  padding-left: 10px;
  font-size: 10px;
}
.wonderslate_main div#button-inner-container p:after {
  content: ")";
}
.wonderslate_main div#button-inner-container p:before {
  content: "(";
}
.wonderslate_main div#button-inner-container p {
  font-size: 10px;
}
.wonderslate_main div#content-data-institute-books p:after {
  content: ")";
}
.wonderslate_main div#content-data-institute-books p:before {
  content: "(";
}
.wonderslate_main p#total-books-of-user {
  display: inline;
  font-size: 12px;
  padding-right: 10px;
}
.wonderslate_main p#total-books-of-user:before {
  content: '(';
}
.wonderslate_main p#total-books-of-user:after {
  content: ')';
}
.wonderslate_main .d-flex.justify-content-between.align-items-center.queue-list-btn.show-library.mb-3 h4 {
  background-color: #17A2B8 !important;
  color: white !important;
  margin-right: 4px;
  font-weight: 400;
  font-size: 15px !important;
  cursor: pointer;
  padding: 5px 10px !important;
  border: 1px solid;
  border-radius: 20px;
}
.wonderslate_main div#bestSellerBooksContainer {
  display: none !important;
}
.wonderslate_main p#searchBooksNo {
  display: inline;
  padding: 0px 5px;
}
.wonderslate_main #searchBooksNo:before {
  content: "(";
}
.wonderslate_main #searchBooksNo:after {
  content: ")";
}
.wonderslate_main div#content-data-search-books h4 {
  background-color: #17A2B8 !important;
  color: white !important;
  margin-right: 4px;
  font-weight: 400;
  font-size: 15px !important;
  cursor: pointer;
  padding: 5px 10px !important;
  border: 1px solid;
  border-radius: 20px;
  display: inline;
}
.wonderslate_main .row.institute-books-list.pt-3 {
  width: 100%;
}
.wonderslate_main div#button-container {
  height: 30px;
}
.my_books #libraryTabs {
  flex-wrap: nowrap;
  overflow: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.my_books #libraryTabs::-webkit-scrollbar {
  display: none;
}
.my_books #libraryTabs a.current {
  border-color: transparent;
  background-color: #C9302C !important;
  color: #FFFFFF !important;
}
.my_books #libraryTabs .institute-tab {
  min-width: 180px;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
}
.my_books #instituteClasses select {
  width: 250px;
  overflow: hidden !important;
  text-overflow: ellipsis;
  outline: 0;
  box-shadow: none;
}
.my_books #instituteClasses select:focus,
.my_books #instituteClasses select:active {
  border-color: #C9302C;
}
@media (max-width: 575.98px) {
  .my_books #instituteClasses select {
    width: 100%;
  }
}
.my_books #libraryFilter h5 {
  font-size: 16px;
  font-weight: normal;
}
.my_books #libraryFilter #resetLibraryFilter:not(:disabled):not(.disabled) {
  color: #2F80ED;
}
.my_books #libraryFilter .available-filters a,
.my_books #libraryFilter .available-filters select {
  height: 30px;
  line-height: 1.75;
  outline: 0;
  box-shadow: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .my_books #libraryFilter .available-filters a,
  .my_books #libraryFilter .available-filters select {
    width: 48%;
    margin-right: 10px !important;
  }
  .my_books #libraryFilter .available-filters a:nth-child(2),
  .my_books #libraryFilter .available-filters select:nth-child(2),
  .my_books #libraryFilter .available-filters a:last-child,
  .my_books #libraryFilter .available-filters select:last-child {
    margin-right: 0 !important;
  }
}
.my_books #libraryFilter .available-filters a.selected,
.my_books #libraryFilter .available-filters select.selected {
  border-color: transparent;
  background-color: #C9302C;
  color: #FFFFFF;
}
.my_books #libraryFilter .available-filters select {
  padding: 0.3rem;
}
.my_books #libraryFilter .available-filters select:focus,
.my_books #libraryFilter .available-filters select:active {
  border-color: #C9302C;
}
.my_books .library-book a.btn-outline-primary-modifier:hover,
.my_books .library-book a.btn-outline-primary-modifier:focus {
  background-color: #C9302C;
  color: #FFFFFF;
}
@media (max-width: 575.98px) {
  .institute-access-code .access_code {
    width: 100% !important;
  }
}
.institute-access-code .access_code #accessCode {
  background-color: transparent;
}
.libwonder .access-code-link a:hover {
  text-decoration: underline;
}
.purchase-details-container.ws-orders .purchase-heading {
  margin-top: 40px;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-heading {
    margin-top: 30px;
  }
}
.purchase-details-container.ws-orders .purchase-heading h3 {
  font-weight: 600;
  font-size: 24px;
  color: #C9302C;
}
.purchase-details-container.ws-orders .purchase-details-wrapper {
  margin: 0 auto 40px;
  min-height: auto;
}
.purchase-details-container.ws-orders .purchase-details-wrapper .purchase-details,
.purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book {
  width: 50%;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-details-wrapper .purchase-details,
  .purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book {
    width: 100%;
  }
}
.purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
  padding: 0 40px 24px;
}
@media (max-width: 991px) {
  .purchase-details-container.ws-orders .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
    padding: 0 0 24px;
  }
}
.purchase-details-container.ws-orders .purchase-details-wrapper .learn-btn {
  display: inline-block;
  margin-top: 0;
}
.purchase-details-container .purchase-details-wrapper {
  margin: 40px auto;
  min-height: 400px;
}
.purchase-details-container .purchase-details-wrapper .purchase-heading {
  font-weight: 500;
  font-size: 24px;
  background: #C9302C;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.purchase-details-container .purchase-details-wrapper .purchase-success-confirmation {
  font-size: 16px;
  margin-bottom: 7px;
}
@media (max-width: 575px) {
  .purchase-details-container .purchase-details-wrapper .purchase-success-confirmation {
    font-size: 15px;
  }
}
.purchase-details-container .purchase-details-wrapper .purchase-success-confirmation strong {
  font-weight: 600;
}
.purchase-details-container .purchase-details-wrapper .purchase-success-id {
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 0.01em;
  margin-bottom: 16px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: start;
  align-items: start;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper {
  width: 100%;
  border-radius: 6px;
  background-color: #FFFFFF;
  padding: 8px;
  border: 1px solid #848484;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-item {
  display: flex;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-img-wrapper img {
  width: 111px;
  box-shadow: 0 0 14px #0000001A;
  border-radius: 4px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info {
  vertical-align: top;
  padding: 0 15px 15px 15px;
  max-height: inherit;
  text-align: left;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .purchased-book-name {
  font-style: normal;
  font-weight: 500;
  line-height: 21px;
  font-size: 16px;
  letter-spacing: 0.01em;
  margin-bottom: 7px;
}
@media (max-width: 767px) {
  .purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .purchased-book-name {
    font-size: 15px;
  }
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .detail-book-author-name {
  text-align: left;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .offer-price {
  display: inline-block;
  font-size: 20px;
  font-weight: 500;
  color: #FF4B33;
  letter-spacing: 0.01em;
  margin-right: 4px;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .offer-price i {
  font-size: 18px;
}
.purchase-details-container .purchase-details-wrapper .purchased-book-container .purchased-book-wrapper .purchased-book-info .original-price {
  display: inline-block;
  font-size: 16px;
  font-weight: 300;
  color: #949494;
  letter-spacing: 0.01em;
  text-decoration: line-through;
  margin-bottom: 7px;
}
.purchase-details-container .purchase-details-wrapper .purchase-details {
  float: left;
  width: 45%;
  margin-right: 80px;
}
@media screen and (max-width: 991px) {
  .purchase-details-container .purchase-details-wrapper .purchase-details {
    width: 100%;
  }
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book {
  float: left;
  width: 45%;
  border-left: 1px solid #848484;
}
@media screen and (max-width: 991px) {
  .purchase-details-container .purchase-details-wrapper .browse-purchase-book {
    width: 100%;
    border: 0;
  }
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper {
  padding: 24px 40px;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .continue-browse {
  display: block;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.01em;
  color: #C9302C;
  padding: 24px 0;
  margin-bottom: 24px;
  border-top: 1px solid #848484;
  border-bottom: 1px solid #848484;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .read-on-app {
  font-weight: 400;
  line-height: normal;
  font-size: 12px;
  text-align: center;
  letter-spacing: -0.01em;
  color: #848484;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .download-app-btn {
  display: block;
  text-align: center;
  max-width: 122px;
  height: 40px;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .browse-purchase-book .browse-wrapper .download-app-btn .download-app-btn-img {
  width: 100%;
  height: auto;
  margin: 0 auto;
}
.purchase-details-container .purchase-details-wrapper .waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  vertical-align: middle;
  z-index: 1;
  transition: 0.3s ease-out;
}
.purchase-details-container .purchase-details-wrapper .learn-btn {
  font-size: 14px;
  display: block;
  text-align: center;
  font-weight: 500;
  color: #FFFFFF;
  background: #C9302C;
  letter-spacing: 0.01em;
  padding: 11px 25px;
  border-radius: 4px;
  margin-bottom: 24px;
  margin-top: -10px;
}
.purchase-details-container .purchase-details-wrapper .learn-btn:hover {
  text-decoration: none;
  color: #FFFFFF;
  box-shadow: 0 2px 8px #0000001A;
}
.purchase-details-container .purchase-details-wrapper .instructions h5 {
  font-weight: 600;
}
.purchase-details-container .purchase-details-wrapper .instructions ol li {
  padding-bottom: 7px;
  font-size: 15px;
}
.purchase-details-container .purchase-details-wrapper .instructions ol li strong {
  font-weight: 600;
}
.order-management {
  -webkit-font-smoothing: antialiased;
  overflow-x: hidden;
}
.order-management form label {
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
}
.order-management form select:focus,
.order-management form input:focus {
  outline: 0;
  box-shadow: none;
  border-color: #C9302C;
}
.order-management form select.input-error,
.order-management form input.input-error {
  border-color: #FF4B33;
}
.order-management form .btn {
  font-weight: 600;
  width: 150px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .order-management form .form-inline {
    display: block;
  }
}
.order-management form .alert p {
  font-size: 13px;
}
.fav_title {
  margin-bottom: 18px;
  width: 65%;
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 768px) {
  .fav_title {
    width: 100%;
  }
}
.fav_title .fav_title-text {
  margin-top: 3rem;
  font-size: 28px;
}
@media (max-width: 768px) {
  .fav_title .fav_title-text {
    margin-top: 1.4rem;
    font-size: 24px;
  }
}
.fav_subjects {
  margin-bottom: 18px;
  width: 65%;
  margin-left: auto;
  margin-right: auto;
}
@media (max-width: 768px) {
  .fav_subjects {
    width: 100%;
  }
}
.fav_subjects-dropdown {
  padding: 7px;
  width: 220px;
  border: none;
  border-radius: 5px;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.2);
}
.fav_subjects-dropdown:focus-visible {
  border: none !important;
  outline: none;
}
.fav_mcqsList {
  min-height: 400px;
  padding-bottom: 5rem;
}
.fav_mcqs-card {
  background: #FFFFFF;
  box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 18px;
  margin-left: auto;
  margin-right: auto;
  width: 65%;
}
@media (max-width: 768px) {
  .fav_mcqs-card {
    width: 100%;
  }
}
.fav_mcqs-card_star {
  text-align: end;
  margin-bottom: 14px;
}
.fav_mcqs-card_star i {
  cursor: pointer;
}
.fav_mcqs-card_question {
  margin-bottom: 14px;
  padding: 10px;
}
.fav_mcqs-card_options .fav_mcqs-card_option {
  background: #f4f4f4;
  padding: 10px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
}
.shimmerLoader {
  color: grey;
  display: inline-block;
  -webkit-mask: linear-gradient(-60deg, #000 30%, #0005, #000 70%) right / 300% 100%;
  background-repeat: no-repeat;
  animation: shimmer 2.5s infinite;
  font-size: 50px;
}
.correctAnsHl {
  background: #42B538 !important;
  color: #fff;
}
.favMcqBtnNav {
  margin-left: 2rem;
  background: #F79420;
  color: #fff;
  border: none;
  padding: 5px;
  width: 150px;
  border-color: #F79420;
  border-radius: 5px;
  text-align: center;
}
.favMcqBtnNav:hover {
  color: #fff !important;
}
.clearFilterIcon {
  background: transparent;
  border: none;
}
.clearFilterIcon:disabled {
  opacity: 0.5;
}
.clearFilterIcon img {
  width: 25px;
}
.takeTestBtn {
  background: #F79420;
  padding: 0.5rem 2rem;
  color: #fff;
  border-radius: 5px;
  margin-top: 3px;
  width: 150px;
  text-align: center;
}
.takeTestBtn:hover {
  color: #fff !important;
}
@keyframes shimmer {
  100% {
    -webkit-mask-position: left;
  }
}
.pre-loaderDiv {
  height: 50px;
  background: #f4f4f4;
  border-radius: 10px;
}
.arihant #total-books-of-user {
  display: none;
}
.arihant .ebooks .section-headerless {
  margin-top: 0 !important;
}
.arihant .view-more {
  margin-bottom: 20px !important;
}
.arihant .mobile {
  display: none;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .arihant .mobile {
    display: flex;
    margin-top: 60px !important;
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .arihant .desktop {
    display: none;
  }
}
