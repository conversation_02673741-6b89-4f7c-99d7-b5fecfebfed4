.btn {
  font-weight: 500; }

@font-face {
  font-family: "wonderslate";
  src: url("../fonts/wonderslate/fonts/wonderslate.eot");
  src: url("../fonts/wonderslate/fonts/wonderslate.eot?#iefix") format("embedded-opentype"), url("../fonts/wonderslate/fonts/wonderslate.woff") format("woff"), url("../fonts/wonderslate/fonts/wonderslate.ttf") format("truetype"), url("../fonts/wonderslate/fonts/wonderslate.svg#wonderslate") format("svg");
  font-weight: normal;
  font-style: normal; }
[data-icon]:before {
  font-family: "wonderslate" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "wonderslate" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.icon-back:before {
  content: "\61"; }

.icon-checkbox-deselected:before {
  content: "\62"; }

.icon-checkbox-selected:before {
  content: "\63"; }

.icon-search-light:before {
  content: "\64"; }

.icon-search-dark:before {
  content: "\65"; }

.icon-close:before {
  content: "\66"; }

.icon-comment:before {
  content: "\67"; }

.icon-done:before {
  content: "\68"; }

.icon-error-dark:before {
  content: "\69"; }

.icon-error-light:before {
  content: "\6a"; }

.icon-filter:before {
  content: "\6b"; }

.icon-help:before {
  content: "\6c"; }

.icon-text-format:before {
  content: "\6d"; }

.icon-list:before {
  content: "\6f"; }

.icon-sort:before {
  content: "\70"; }

.icon-settings:before {
  content: "\71"; }

.icon-radio-selected:before {
  content: "\72"; }

.icon-radio-deselected:before {
  content: "\73"; }

.icon-add:before {
  content: "\6e"; }

.icon-bookmark:before {
  content: "\74"; }

.icon-chevron:before {
  content: "\75"; }

.icon-dropdown:before {
  content: "\76"; }

.icon-favorite:before {
  content: "\77"; }

.icon-fullscreen:before {
  content: "\78"; }

.icon-grid:before {
  content: "\79"; }

.icon-hamburger:before {
  content: "\7a"; }

.icon-reload:before {
  content: "\41"; }

.icon-star-filled:before {
  content: "\43"; }

.icon-star:before {
  content: "\42"; }

.icon-info-outline-black:before {
  content: "\44"; }

.icon-fontsize-less:before {
  content: "\45"; }

.icon-fontsize-zoom:before {
  content: "\46"; }

.icon-letter-spacing-decrease:before {
  content: "\47"; }

.icon-letter-spacing-increase:before {
  content: "\48"; }

.icon-lineheight:before {
  content: "\49"; }

.icon-lineheight-1:before {
  content: "\4a"; }

body {
  font-family: "Montserrat", sans-serif;
  color: #444444;
  background-color: #F8F8F8;
  line-height: normal;
  font-weight: normal;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden; }

p, a, h1, h2, h3, h4, h5, h6 {
  color: #444444; }

.loader-wrapper {
  width: 139px;
  height: 65px;
  background-color: #FFFFFF;
  position: relative;
  top: 50% !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25);
  margin: 0 auto;
  border-radius: 4px; }

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out; }

.loader {
  color: #F05A2A;
  font-size: 9px;
  margin: 0 auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s; }

.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0; }

.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s; }

.loader:after {
  left: 3.5em; }

@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em; }
  40% {
    box-shadow: 0 2.5em 0 0; } }
.sign-in-modal-dialog {
  top: 80px; }

.sign-in-modal-content {
  min-height: 460px;
  max-height: 460px;
  padding: 30px 70px 30px; }

.close-signin-signup {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  background: url("../images/wonderslate/close.svg");
  background-size: 100% 100%;
  background-position: center;
  text-indent: -999999px;
  border: 0;
  box-shadow: none;
  z-index: 99; }

.sign-in-modal-content::before {
  position: absolute;
  left: 0;
  top: 0;
  content: '';
  width: 100%;
  height: 100%;
  background: url("../images/wonderslate/signup-bg.png");
  background-position: center;
  background-size: 100% 100%; }

.sign-in-modal-header {
  font-family: "Abril Fatface", cursive;
  font-size: 22px;
  text-align: center;
  letter-spacing: 0.04em;
  padding: 0;
  border-bottom: 0; }

.sign-in-modal-body {
  text-align: center; }
  .sign-in-modal-body p {
    margin: 0; }

.signin-modal-credentials {
  font-size: 16px;
  font-weight: 300;
  letter-spacing: 0.01em; }

.sign-in-inputs {
  max-width: 320px;
  margin: 16px auto 0;
  border: 1px solid rgba(68, 68, 68, 0.24);
  border-radius: 6px; }
  .sign-in-inputs .login-signup-input {
    color: rgba(68, 68, 68, 0.74);
    border-bottom: rgba(68, 68, 68, 0.24);
    padding-right: 25px;
    border: 0;
    border-radius: 0; }
  .sign-in-inputs .login-signup-input:first-child {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px; }
  .sign-in-inputs .login-signup-input:last-child {
    border: 0;
    border-radius: 0;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px; }

.forgot-password {
  max-width: 320px;
  text-align: right;
  margin: 16px auto 0; }
  .forgot-password .forgot-pwd-btn {
    font-size: 12px;
    font-weight: 300;
    color: #F05A2A; }
    .forgot-password .forgot-pwd-btn:hover {
      text-decoration: none; }

.submit-btn {
  max-width: 320px;
  margin: 16px auto 0; }
  .submit-btn .login-signup-btn {
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    padding: 11px 0;
    color: #FFFFFF;
    letter-spacing: 0.01em;
    border: 0; }
    .submit-btn .login-signup-btn:hover {
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25); }
  .submit-btn input {
    width: 100%; }

.term-condition {
  font-size: 12px;
  font-weight: 300;
  text-align: center;
  padding: 0 72px;
  margin-top: 24px; }
  .term-condition a {
    text-decoration: underline; }

hr {
  border-top: 1px solid rgba(68, 68, 68, 0.24); }

.create-account {
  font-weight: 300;
  text-align: center;
  padding: 0 72px; }
  .create-account a {
    color: #F05A2A; }

.login-back-btn {
  color: #F05A2A; }

.is-correct {
  background: url("../images/wonderslate/img_correct.svg");
  background-repeat: no-repeat;
  background-position: 99% 5px;
  background-color: #FFFFFF; }

.has-error {
  background: url("../images/wonderslate/img_error.svg");
  background-repeat: no-repeat;
  background-position: 99% 5px;
  background-color: #FFFFFF; }

.input-error-tooltip {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  position: absolute;
  z-index: 99; }

.input-error-tooltip-inner {
  padding: 11px 80px;
  background: #FFFFFF;
  margin-top: -50px;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  padding: 11px 20px;
  -moz-transform: rotate(0deg);
  -webkit-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg); }

.input-error-tooltip:before {
  content: '';
  position: relative;
  margin-left: 45%;
  top: -10px;
  background: #FFFFFF;
  display: block;
  width: 20px;
  height: 50px;
  -webkit-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  -moz-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg); }

.email-error {
  max-width: 320px;
  margin: 16px auto 0; }
  .email-error .email-error-text {
    color: #B72319; }

.connecting-user {
  width: 100%;
  font-family: "Abril Fatface", cursive;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%); }
  .connecting-user .connecting-text {
    font-size: 22px; }

.social-login-btns {
  max-width: 320px;
  margin: 16px auto 0; }
  .social-login-btns .social-google {
    background: url("../images/wonderslate/ic_google.svg");
    background-repeat: no-repeat;
    background-position: 32px 11px; }
  .social-login-btns .social-facebook {
    background: url("../images/wonderslate/ic_fb.svg");
    background-repeat: no-repeat;
    background-position: 32px 8px; }
  .social-login-btns .social-email {
    background: url("../images/wonderslate/ic_email.svg");
    background-repeat: no-repeat;
    background-position: 32px 11px; }

.social-login {
  display: block;
  width: 100%;
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  background-color: #FFFFFF !important;
  padding: 11px 72px;
  margin-bottom: 16px;
  border: 1px solid rgba(68, 68, 68, 0.24);
  border-radius: 4px;
  outline: none;
  -webkit-transition: all .2s;
  -moz-transition: all .2s;
  -ms-transition: all .2s;
  -o-transition: all .2s;
  transition: all .2s; }
  .social-login:hover {
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25); }

#reset-password-div {
  text-align: center;
  position: relative;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto; }
  #reset-password-div .signin-modal-credentials {
    line-height: 25px; }

.login-signup-container {
  min-height: calc(100vh - 156px);
  font-family: Montserrat;
  position: relative;
  width: 100%;
  padding: 40px 0;
  background: linear-gradient(180deg, rgba(94, 199, 215, 0.29) 0%, rgba(247, 148, 32, 0.25) 148.83%);
  z-index: 1; }

.login-signup-container::before {
  content: '';
  background-image: url("../images/pattern.png");
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.02;
  z-index: -1; }

.login-signup-form {
  display: block;
  width: 476px;
  background: #fff;
  text-align: center;
  padding: 24px 0;
  margin: 0 auto;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
  border-radius: 4px; }

.login-signup-headline {
  font-size: 32px;
  margin-bottom: 32px; }

.easy-login, .enter-email {
  font-size: 14px;
  text-transform: uppercase;
  opacity: .75; }

.enter-email {
  text-transform: none; }

.social-btns {
  width: 355px;
  margin: 16px auto; }
  .social-btns .btns {
    width: 168px;
    height: 50px;
    font-size: 14px;
    background: #FFFFFF;
    border: 1px solid rgba(68, 68, 68, 0.6);
    box-sizing: border-box;
    border-radius: 4px; }
  .social-btns .google {
    margin-right: 12px;
    background: url(../images/ic_google.png);
    background-repeat: no-repeat;
    background-size: 20px 20px;
    background-position: 18px 15px; }
  .social-btns .facebook {
    background: url(../images/ic_fb.png);
    background-repeat: no-repeat;
    background-size: 20px 20px;
    background-position: 12px 15px; }

.submit-btn {
  width: 380px; }
  .submit-btn .login-signup-btn {
    height: 50px;
    color: #fff;
    font-size: 16px;
    background: #5EC7D7; }

.using-email {
  margin-top: 32px;
  margin-bottom: 16px;
  font-size: 14px; }

.form-container {
  width: 380px;
  margin: 0 auto; }

.form-inputs {
  position: relative;
  border: 1px solid rgba(68, 68, 68, 0.2);
  border-radius: 4px; }
  .form-inputs .show-password {
    width: 22px;
    height: 13px;
    position: absolute;
    right: 15px;
    bottom: 15px;
    background: url("../images/eye.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-indent: -5000px; }

#password {
  padding-right: 40px; }

.login-signup-input {
  height: 40px;
  border: 1px solid rgba(68, 68, 68, 0.2);
  border-radius: 0; }

.already-user {
  margin: 16px auto 0;
  text-align: left; }
  .already-user a {
    color: #F05A2A; }

.have-an-account {
  font-size: 12px; }

@media screen and (max-width: 767px) {
  .login-signup-form {
    width: 91.6666667%; }

  .form-container {
    width: 100%; }

  .form-inputs {
    width: 100%;
    padding: 0 5px;
    border: 0; }

  .submit-btn {
    width: 100%;
    padding: 0 5px; }

  .forgot-password {
    text-align: center; }

  .already-user {
    text-align: center; }

  .have-an-account {
    float: none !important; } }
.sticky-header {
  position: relative;
  z-index: 999; }

.fixed-header {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9999;
  animation: smoothScroll .5s forwards; }

.slideDown {
  top: 0; }

.wonderslate-navbar {
  background-color: #FFFFFF;
  line-height: normal;
  padding: 12px 24px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
  z-index: 3; }
  .wonderslate-navbar .navbar-container {
    padding-left: 0;
    padding-right: 0; }
    .wonderslate-navbar .navbar-container .navbar-nav.header-menus {
      margin-left: 100px; }
    .wonderslate-navbar .navbar-container .nav.navbar-nav.navbar-right li {
      margin-right: 8px; }
    .wonderslate-navbar .navbar-container .nav.navbar-nav.navbar-right li:last-child {
      margin-right: 0; }
    .wonderslate-navbar .navbar-container .login-btn {
      font-size: 14px;
      color: #4DDCE8;
      font-weight: 500;
      padding: 11px 25px;
      border-radius: 4px; }
    .wonderslate-navbar .navbar-container .signup-btn {
      font-size: 14px;
      display: block;
      text-align: center;
      font-weight: 500;
      color: #FFFFFF;
      background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
      letter-spacing: 0.01em;
      padding: 11px 25px;
      border-radius: 4px; }
      .wonderslate-navbar .navbar-container .signup-btn:hover {
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25); }
  .wonderslate-navbar .navbar-brand {
    width: 153px;
    height: 40px;
    background: url("../images/wonderslate/logo.svg");
    background-position: center;
    background-size: 100% 100%;
    display: block;
    text-indent: -9999999px; }
  .wonderslate-navbar ul a {
    font-size: 18px;
    font-weight: 300;
    color: rgba(68, 68, 68, 0.64);
    padding-top: 9px; }
    .wonderslate-navbar ul a:hover {
      color: #444444;
      background-color: #FFFFFF; }
  .wonderslate-navbar ul a.active {
    font-weight: 500;
    color: #444444;
    padding: 11px 15px; }

.user-logged-in {
  display: block;
  width: 40px;
  height: 40px;
  background-color: #fff !important;
  padding: 0 !important; }
  .user-logged-in img {
    max-width: 100% !important;
    height: auto;
    border: 1px solid #BDBDBD;
    border-radius: 100%; }

.profile-dropdown {
  min-width: 330px;
  left: auto;
  right: -8px !important;
  padding: 24px;
  border-top-right-radius: 4px !important;
  border-top-left-radius: 4px !important;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  overflow-wrap: break-word; }
  .profile-dropdown:before {
    content: "";
    border-bottom: 10px solid #fff;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    position: absolute;
    top: -10px;
    right: 16px;
    z-index: 10; }
  .profile-dropdown:after {
    content: "";
    border-bottom: 12px solid #ccc;
    border-right: 12px solid transparent;
    border-left: 12px solid transparent;
    position: absolute;
    top: -12px;
    right: 14px;
    z-index: 9; }
  .profile-dropdown a {
    padding: 0; }
    .profile-dropdown a.text-zoom {
      width: 60px;
      height: 40px;
      font-size: 18px;
      font-weight: bold;
      padding: 6px 22px;
      border: 2px solid rgba(68, 68, 68, 0.54);
      border-radius: 4px;
      transform: matrix(-1, 0, 0, 1, 0, 0);
      margin-right: 20px; }
  .profile-dropdown li {
    display: inline-block; }

.user-name-profile-img {
  width: 100%;
  height: 100%;
  background: linear-gradient(270deg, #F05A2A 0%, #FF7245 100%);
  color: #FFFFFF;
  text-align: center;
  font-weight: 900;
  font-size: 32px;
  text-transform: uppercase;
  padding-top: 9px;
  border: 1px solid rgba(68, 68, 68, 0.54);
  border-radius: 100%; }

.user-image {
  position: relative;
  width: 72px;
  height: 72px;
  float: left;
  margin-right: 24px;
  text-align: center; }
  .user-image img {
    max-width: 100%;
    height: auto;
    border: 1px solid #BDBDBD;
    border-radius: 100%; }
  .user-image .user-edit-profile {
    color: #FFFFFF;
    font-size: 8px;
    position: absolute;
    bottom: 2px;
    left: 4px;
    padding: 8px 8px;
    background: rgba(0, 40, 71, 0.64);
    text-decoration: none;
    width: 90%;
    margin-bottom: 0;
    border-bottom-left-radius: 32px;
    border-bottom-right-radius: 32px; }
    .user-image .user-edit-profile:hover {
      color: #FFFFFF;
      background: rgba(0, 40, 71, 0.64);
      text-decoration: none; }
    .user-image .user-edit-profile:focus {
      color: #FFFFFF;
      background: rgba(0, 40, 71, 0.64);
      text-decoration: none; }
    .user-image .user-edit-profile:active {
      color: #FFFFFF;
      background: rgba(0, 40, 71, 0.64);
      text-decoration: none; }

.logged-in-user-details {
  float: left;
  max-width: 166px; }
  .logged-in-user-details .loggedin-user-name {
    font-weight: 300; }
    .logged-in-user-details .loggedin-user-name .user-name {
      font-weight: 500;
      text-transform: capitalize; }
  .logged-in-user-details .loggedin-user-mobile, .logged-in-user-details .loggedin-user-email {
    font-weight: 300; }

.user-orders {
  clear: both;
  float: left;
  width: 100%;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid rgba(68, 68, 68, 0.2);
  border-bottom: 1px solid rgba(68, 68, 68, 0.2); }
  .user-orders a {
    color: #444; }
    .user-orders a:hover {
      color: #444;
      text-decoration: none; }
    .user-orders a:active {
      color: #444;
      text-decoration: none; }
    .user-orders a:focus {
      color: #444;
      text-decoration: none; }

.user-logout {
  clear: both;
  float: left;
  width: 100%;
  margin-top: 16px; }
  .user-logout p {
    font-weight: 300;
    color: #000;
    margin: 0; }
    .user-logout p a {
      font-weight: 400;
      color: #F05A2A; }

.user-search-input {
  min-width: 320px;
  padding: 8px;
  background-color: #FFFFFF;
  border: 1px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  border-radius: 4px; }

.search-btn {
  padding: 0;
  position: absolute;
  right: 0px;
  top: 0;
  width: 38px;
  height: 35px;
  color: #FFFFFF;
  background-color: #F05A2A;
  padding: 2px;
  box-shadow: none;
  border: 0;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px; }
  .search-btn i {
    vertical-align: middle;
    font-size: 32px; }

.header-menu-item .typeahead.dropdown-menu {
  max-width: 320px;
  max-height: 200px;
  overflow-x: hidden;
  overflow-y: auto;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical; }

.tabs-holder {
  background-color: #FFFFFF;
  padding-left: 80px;
  padding-right: 80px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12); }

.nav-tabs-wrapper {
  border-bottom: 0; }
  .nav-tabs-wrapper > li {
    margin-left: 23px; }
    .nav-tabs-wrapper > li > a {
      font-size: 18px;
      font-weight: 500;
      text-align: center;
      color: rgba(68, 68, 68, 0.64);
      background-color: transparent;
      padding: 10px 8px;
      border: 0; }
      .nav-tabs-wrapper > li > a:hover, .nav-tabs-wrapper > li > a:focus, .nav-tabs-wrapper > li > a:active {
        background-color: transparent;
        border: 0; }
    .nav-tabs-wrapper > li.active > a {
      font-size: 18px;
      font-weight: 500;
      text-align: center;
      color: #F05A2A;
      background-color: transparent;
      border: 0;
      border-bottom: 5px solid #F05A2A; }
      .nav-tabs-wrapper > li.active > a:hover, .nav-tabs-wrapper > li.active > a:focus, .nav-tabs-wrapper > li.active > a:active {
        color: #F05A2A;
        background-color: transparent;
        border: 0;
        border-bottom: 5px solid #F05A2A; }
    .nav-tabs-wrapper > li .level-label {
      font-weight: 500;
      font-size: 22px;
      color: #444444;
      padding: 10px 15px;
      padding-right: 32px;
      margin: 0; }

.tab-sub-categories {
  padding-left: 80px;
  padding-right: 80px;
  margin-top: 16px; }

.tab-sub-categories-wrapper {
  list-style: none;
  padding: 0;
  margin: 0; }

.tab-sub-categories-item {
  display: inline-block;
  position: relative;
  margin-left: 23px;
  margin-bottom: 10px; }

.tab-sub-categories-item-btn {
  display: block;
  font-weight: 300;
  font-size: 16px;
  text-align: center;
  color: rgba(68, 68, 68, 0.74);
  background-color: #FFFFFF;
  border-radius: 4px;
  padding: 8px 35px;
  text-decoration: none;
  border: 1px solid rgba(68, 68, 68, 0.54); }
  .tab-sub-categories-item-btn:hover {
    color: #F05A2A;
    text-decoration: none; }
  .tab-sub-categories-item-btn:focus {
    color: #F05A2A;
    text-decoration: none; }
  .tab-sub-categories-item-btn:active {
    color: #F05A2A;
    text-decoration: none; }
  .tab-sub-categories-item-btn.active {
    color: #FFFFFF;
    background-color: #F05A2A; }

.class-selection-div {
  padding-left: 24px;
  border-left: 2px solid #DFE0DF; }
  .class-selection-div .btn-group {
    width: 100%; }
  .class-selection-div .class-selection-btn {
    min-width: 100%;
    font-weight: 500;
    font-size: 16px;
    text-align: left;
    background-color: #FFFFFF !important;
    background: url("../images/wonderslate/caret.png");
    background-repeat: no-repeat;
    background-position: 95%;
    padding: 6px 16px;
    border: 1px solid rgba(68, 68, 68, 0.54); }
  .class-selection-div .class-selection-btn.disabled {
    opacity: 0.45; }
  .class-selection-div .btn-group.open .class-selection-btn {
    background: url("../images/wonderslate/caret-open.png");
    background-repeat: no-repeat;
    background-position: 95%; }
  .class-selection-div .btn-group.open .class-selection-dropdown {
    width: 100%; }
    .class-selection-div .btn-group.open .class-selection-dropdown > li a {
      font-weight: 500;
      font-size: 16px;
      color: #444444; }

.tabs-holder-library {
  background-color: #FFFFFF; }

.user-greeting {
  margin-top: 40px;
  margin-left: 40px; }
  .user-greeting .user-clm {
    padding-left: 5px; }

.r-buy-butn {
  width: 160px;
  height: 40px;
  margin-right: 20px; }

.book-read-material-full-width .card-wrapper {
  width: 100%; }

.card-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 85%;
  height: 75px;
  position: fixed;
  bottom: 0;
  background: #FFFFFF;
  padding: 8px 16px;
  box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24); }
  .card-wrapper .rprice-tag {
    display: flex; }
    .card-wrapper .rprice-tag .complte-book {
      margin-left: 20px; }
    .card-wrapper .rprice-tag h4 {
      font-size: 10px;
      color: rgba(68, 68, 68, 0.72);
      font-family: 'Rubik', sans-serif; }
    .card-wrapper .rprice-tag p {
      font-size: 18px;
      color: #B72319;
      font-family: 'Merriweather', serif; }

.greeting-user {
  font-family: "Abril Fatface", cursive;
  font-size: 52px;
  color: rgba(68, 68, 68, 0.24);
  letter-spacing: 0.04em;
  text-transform: capitalize; }
  .greeting-user .greeting-user-name {
    font-weight: 600;
    color: rgba(68, 68, 68, 0.84); }

.total-books {
  font-size: 22px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.84);
  letter-spacing: 0.01em; }

.books-content-wrapper {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: flex-start;
  margin-right: 0;
  margin-left: 0;
  margin-top: 32px; }

.book-wrapper {
  width: 160px;
  height: 0%;
  padding: 8px;
  margin: 0 34px 40px;
  border-radius: 6px;
  -webkit-transition: background-color .4s ease, box-shadow .4s ease;
  -moz-transition: background-color .4s ease, box-shadow .4s ease;
  -ms-transition: background-color .4s ease, box-shadow .4s ease;
  -o-transition: background-color .4s ease, box-shadow .4s ease;
  transition: background-color .4s ease, box-shadow .4s ease; }
  .book-wrapper a {
    text-decoration: none; }
  .book-wrapper:hover {
    background-color: #FFFFFF;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25); }

.book-item {
  cursor: pointer;
  position: relative; }

.book-btns-home {
  position: absolute;
  left: 0;
  top: 100px;
  width: calc(100% - 1px);
  padding: 0 16px; }

.book-img-wrapper {
  position: relative;
  margin-bottom: 8px;
  z-index: 0; }
  .book-img-wrapper img {
    width: 145px;
    height: 181.25px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    border-radius: 4px; }

.book-info .book-name {
  font-weight: 500;
  line-height: 19px;
  letter-spacing: 0.01em; }
.book-info .author-name {
  font-size: 12px;
  font-weight: 300;
  color: rgba(68, 68, 68, 0.74); }
.book-info .book-test-wise {
  font-style: normal;
  font-weight: 500;
  line-height: 11px;
  font-size: 8px;
  letter-spacing: 0.01em;
  text-transform: uppercase; }
.book-info .offer-price {
  display: inline-block;
  font-size: 20px;
  font-weight: 500;
  color: #AE2B24;
  letter-spacing: 0.01em;
  margin-right: 4px; }
.book-info .original-price {
  display: inline-block;
  font-size: 16px;
  font-weight: 300;
  color: #909090;
  letter-spacing: 0.01em;
  text-decoration: line-through; }

.click-here-link {
  color: #F05A2A; }

.no-books-available {
  max-width: 180px;
  margin: 0 auto;
  text-align: center; }
  .no-books-available > p {
    font-style: normal;
    font-weight: 300;
    line-height: 16px;
    font-size: 14px;
    text-align: center;
    letter-spacing: 0.04em;
    color: rgba(68, 68, 68, 0.84);
    margin-top: 16px; }
  .no-books-available > a {
    font-weight: 500;
    color: #F05A2A; }
    .no-books-available > a:hover {
      text-decoration: none; }
    .no-books-available > a:focus {
      text-decoration: none; }
    .no-books-available > a:active {
      text-decoration: none; }

.profile {
  margin: 0 auto; }

.material-float-btn {
  color: #F05A2A !important;
  position: absolute;
  right: 0;
  top: 0; }

.showrank-modal .container {
  width: 100%; }

#rank-dialog .modal-title {
  text-align: center; }

#detailedResults {
  text-align: center; }

.showRank {
  margin-top: 1rem;
  display: inline-block;
  font-weight: 500;
  letter-spacing: 0.01em;
  color: #F79420;
  background: #FFFFFF;
  padding: 10px 30px;
  border: 1px solid #F79420;
  border-radius: 4px;
  width: auto; }

.showrank-modal .modal-header {
  justify-content: center; }

.showrank-modal .modal-header button {
  padding: 0;
  margin: 0;
  position: absolute;
  right: 25px;
  top: 12px; }

.showrank-modal .modal-footer {
  border: none; }

.showrank-modal .modal-body {
  padding: 0; }

.showrank-modal .modal-body .table thead th {
  font-size: 12px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.7); }

.showrank-modal .modal-body tr.user-active td {
  color: #0AAEF9 !important; }

.showrank-modal .modal-body tr td:first-child {
  font-size: 18px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.7);
  font-family: 'Merriweather', serif; }

.showrank-modal .modal-body tr td:nth-child(2) {
  font-size: 14px;
  font-weight: normal;
  color: #444444;
  font-family: 'Rubik', sans-serif; }

.showrank-modal .modal-body tr td:last-child {
  font-size: 14px;
  font-weight: bold;
  color: #444444;
  font-family: 'Merriweather', serif; }

.showrank-modal .content-rankWrapper {
  background: #FFFFFF;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  margin-top: 1rem; }

.showrank-modal .content-rankWrapper .profile {
  width: 92px;
  height: 92px;
  border-radius: 50%;
  border: 4px solid #FFFFFF;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25); }

.showrank-modal .content-rankWrapper .profile img {
  width: 84px;
  height: 84px;
  border-radius: 50%; }

.showrank-modal .content-rankWrapper .user-rank {
  text-align: center;
  font-weight: 500;
  font-family: 'Merriweather', serif;
  font-size: 34px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0; }

.showrank-modal .content-rankWrapper .user-rank:before {
  content: url("../../../images/landingpageImages/badge.svg");
  width: 34px; }

.showrank-modal .content-rankWrapper .yr-head {
  font-size: 8px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.4);
  text-transform: uppercase;
  text-align: center;
  margin: 0;
  margin-top: 8px; }

.showrank-modal .content-rankWrapper .no-ques {
  font-size: 14px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  color: #444444;
  text-align: center; }

.showrank-modal .content-rankWrapper .rank-head {
  font-size: 12px;
  font-weight: 500;
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.4);
  text-transform: uppercase;
  text-align: center;
  margin: 0;
  margin-top: 8px; }

.showrank-modal .content-rankWrapper .total-students {
  font-size: 10px;
  font-weight: normal;
  font-family: 'Rubik', sans-serif;
  color: rgba(68, 68, 68, 0.4);
  text-transform: uppercase;
  text-align: center; }

.testStarts {
  color: rgba(68, 68, 68, 0.6);
  font-weight: 700;
  font-family: 'Rubik', sans-serif; }

.testDate {
  color: #444444;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold; }

.seperator {
  color: rgba(68, 68, 68, 0.4);
  font-size: 24px;
  margin: 0 5px;
  font-weight: 500; }

.testTime {
  color: #444444;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: bold; }

.annotator-notice,
.annotator-filter *,
.annotator-widget * {
  margin: 0;
  padding: 0;
  background: 0;
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  transition: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  -o-box-shadow: none;
  box-shadow: none;
  color: #909090;
  font-family: "Montserrat", sans-serif; }

.annotator-resize,
.annotator-widget::after,
.annotator-editor a::after,
.annotator-viewer .annotator-controls button,
.annotator-viewer .annotator-controls a,
.annotator-filter .annotator-filter-navigation button::after,
.annotator-filter .annotator-filter-property .annotator-filter-clear {
  background-image: url("data:image/png;base64,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");
  background-repeat: no-repeat; }

.annotator-hl {
  background: rgba(255, 255, 10, 0.3); }

.annotator-hlh {
  background-color: rgba(33, 150, 83, 0.24); }

.annotator-hl-temporary {
  background: rgba(0, 124, 255, 0.3); }

.annotator-wrapper {
  position: relative; }

.annotator-adder,
.annotator-outer,
.annotator-notice {
  z-index: 1020; }

.annotator-filter {
  z-index: 1010; }

.annotator-adder,
.annotator-outer,
.annotator-widget,
.annotator-notice {
  position: absolute;
  font-size: 10px;
  line-height: 1; }

.annotator-hide {
  display: none;
  visibility: hidden; }

.annotator-adder {
  min-width: 262px;
  background: #FEFEFE;
  border: 0.5px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px 16px;
  margin-top: 0;
  margin-left: 0; }

.annotator-adder:hover {
  background-position: center top; }

.annotator-adder:active {
  background-position: center right; }

.annotator-adder button {
  display: inline-block;
  margin: 0 auto;
  border: 0;
  background: 0;
  cursor: pointer;
  padding: 8px 32px;
  font-weight: 300;
  font-size: 14px; }

.annotator-adder button:first-child {
  border-right: 1px solid rgba(68, 68, 68, 0.24); }

.annotator-outer {
  width: 0;
  height: 0; }

.annotator-widget {
  margin: 0;
  padding: 0 8px;
  left: -18px;
  min-width: 265px;
  background-color: #FFFFFF;
  border: 1px solid rgba(122, 122, 122, 0.6);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  -o-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); }

.annotator-invert-x .annotator-widget {
  left: auto;
  right: -18px; }

.annotator-invert-y .annotator-widget {
  bottom: auto;
  top: 8px; }

.annotator-widget strong {
  font-weight: bold; }

.annotator-widget .annotator-listing,
.annotator-widget .annotator-item {
  padding: 0;
  margin: 0;
  list-style: none; }

.annotator-widget::after {
  content: "";
  display: block;
  width: 18px;
  height: 10px;
  background-position: 0 0;
  position: absolute;
  bottom: -10px;
  left: 8px;
  background-image: none; }

.annotator-invert-x .annotator-widget::after {
  left: auto;
  right: 8px; }

.annotator-invert-y .annotator-widget::after {
  background-position: 0 -15px;
  bottom: auto;
  top: -9px; }

.annotator-widget .annotator-item,
.annotator-editor .annotator-item input,
.annotator-editor .annotator-item textarea {
  position: relative;
  font-size: 12px; }

.annotator-viewer .annotator-item {
  border-top: 2px solid rgba(122, 122, 122, 0.2); }

.annotator-widget .annotator-item:first-child {
  border-top: 0; }

.annotator-editor .annotator-item,
.annotator-viewer div {
  border-top: 1px solid rgba(68, 68, 68, 0.24); }

.annotator-viewer div {
  padding: 6px 6px; }

.annotator-viewer .annotator-item ol,
.annotator-viewer .annotator-item ul {
  padding: 4px 16px; }

.annotator-viewer div:first-of-type,
.annotator-editor .annotator-item:first-child textarea {
  padding-top: 12px;
  padding-bottom: 12px;
  color: #3c3c3c;
  font-size: 13px;
  font-style: italic;
  line-height: 1.3;
  border-top: 0; }

.annotator-viewer .annotator-controls {
  position: relative;
  top: 5px;
  right: 5px;
  padding-left: 5px;
  opacity: 0;
  -webkit-transition: opacity .2s ease-in;
  -moz-transition: opacity .2s ease-in;
  -o-transition: opacity .2s ease-in;
  transition: opacity .2s ease-in;
  float: right; }

.annotator-viewer li:hover .annotator-controls,
.annotator-viewer li .annotator-controls.annotator-visible {
  opacity: 1; }

.annotator-viewer .annotator-controls button,
.annotator-viewer .annotator-controls a {
  cursor: pointer;
  display: inline-block;
  width: 13px;
  height: 13px;
  margin-left: 2px;
  border: 0;
  opacity: .2;
  text-indent: -900em;
  background-color: transparent;
  outline: 0; }

.annotator-viewer .annotator-controls button:hover,
.annotator-viewer .annotator-controls button:focus,
.annotator-viewer .annotator-controls a:hover,
.annotator-viewer .annotator-controls a:focus {
  opacity: 0.9; }

.annotator-viewer .annotator-controls button:active,
.annotator-viewer .annotator-controls a:active {
  opacity: 1; }

.annotator-viewer .annotator-controls button[disabled] {
  display: none; }

.annotator-viewer .annotator-controls .annotator-edit {
  background-position: 0 -60px; }

.annotator-viewer .annotator-controls .annotator-delete {
  background-position: 0 -75px; }

.annotator-viewer .annotator-controls .annotator-link {
  background-position: 0 -270px; }

.annotator-editor .annotator-item {
  position: relative; }

.annotator-editor .annotator-item label {
  top: 0;
  display: inline;
  cursor: pointer;
  font-size: 12px; }

.annotator-editor .annotator-item input,
.annotator-editor .annotator-item textarea {
  display: block;
  min-width: 100%;
  padding: 10px 8px;
  border: 0;
  margin: 0;
  color: #3c3c3c;
  background: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  resize: none; }

.annotator-editor .annotator-item textarea::-webkit-scrollbar {
  height: 8px;
  width: 8px; }

.annotator-editor .annotator-item textarea::-webkit-scrollbar-track-piece {
  margin: 13px 0 3px;
  background-color: #e5e5e5;
  -webkit-border-radius: 4px; }

.annotator-editor .annotator-item textarea::-webkit-scrollbar-thumb:vertical {
  height: 25px;
  background-color: #ccc;
  -webkit-border-radius: 4px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); }

.annotator-editor .annotator-item textarea::-webkit-scrollbar-thumb:horizontal {
  width: 25px;
  background-color: #ccc;
  -webkit-border-radius: 4px; }

.annotator-editor .annotator-item:first-child textarea {
  min-height: 5.5em;
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  -o-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0; }

.annotator-editor .annotator-item input:focus,
.annotator-editor .annotator-item textarea:focus {
  background-color: #FFFFFF;
  outline: 0;
  resize: none; }

.annotator-editor .annotator-item input[type=radio],
.annotator-editor .annotator-item input[type=checkbox] {
  width: auto;
  min-width: 0;
  padding: 0;
  display: inline;
  margin: 0 4px 0 0;
  cursor: pointer; }

.annotator-editor .annotator-checkbox {
  padding: 8px 6px; }

.annotator-filter,
.annotator-filter .annotator-filter-navigation button,
.annotator-editor .annotator-controls {
  text-align: right;
  padding: 3px;
  border-top: 1px solid #d4d4d4;
  background-color: #d4d4d4;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), color-stop(0.6, #dcdcdc), to(#d2d2d2));
  background-image: -moz-linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
  background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
  background-image: linear-gradient(to bottom, #f5f5f5, #dcdcdc 60%, #d2d2d2);
  -webkit-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  -moz-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  -o-box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.7), inset -1px 0 0 rgba(255, 255, 255, 0.7), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  -webkit-border-radius: 0 0 5px 5px;
  -moz-border-radius: 0 0 5px 5px;
  -o-border-radius: 0 0 5px 5px;
  border-radius: 0 0 5px 5px; }

.annotator-widget .annotator-controls {
  background-color: transparent;
  background-image: none;
  padding: 8px 0;
  border-top: 0; }

.annotator-editor.annotator-invert-y .annotator-controls {
  border-top: 0;
  border-bottom: 1px solid rgba(68, 68, 68, 0.24);
  -webkit-border-radius: 5px 5px 0 0;
  -moz-border-radius: 5px 5px 0 0;
  -o-border-radius: 5px 5px 0 0;
  border-radius: 5px 5px 0 0; }

.annotator-editor a,
.annotator-filter .annotator-filter-property label {
  position: relative;
  display: inline-block;
  padding: 0 6px 0 22px;
  color: #363636;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
  text-decoration: none;
  line-height: 24px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid #a2a2a2;
  background-color: #d4d4d4;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5), color-stop(0.5, #d2d2d2), color-stop(0.5, #bebebe), to(#d2d2d2));
  background-image: -moz-linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
  background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
  background-image: linear-gradient(to bottom, #f5f5f5, #d2d2d2 50%, #bebebe 50%, #d2d2d2);
  -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -moz-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -o-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px; }

.annotator-controls .annotator-cancel {
  text-transform: uppercase;
  font-family: "Montserrat", sans-serif;
  font-style: normal;
  font-weight: 600 !important;
  line-height: normal;
  font-size: 12px;
  box-shadow: none;
  border: 0;
  background-color: transparent;
  background-image: none;
  color: #B72319;
  letter-spacing: 0.04em; }
  .annotator-controls .annotator-cancel:after {
    content: "";
    background-image: none; }
  .annotator-controls .annotator-cancel:hover {
    color: #B72319;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none; }
  .annotator-controls .annotator-cancel:active {
    color: #B72319;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none; }
  .annotator-controls .annotator-cancel:focus {
    color: #B72319;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none; }
.annotator-controls .annotator-save {
  text-transform: uppercase;
  font-family: "Montserrat", sans-serif;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  font-size: 12px;
  box-shadow: none;
  border: 0;
  background-color: #FFFFFF;
  background-image: none;
  color: #30C465; }
  .annotator-controls .annotator-save:after {
    content: "";
    background-image: none; }
  .annotator-controls .annotator-save:hover {
    color: #30C465;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none; }
  .annotator-controls .annotator-save:active {
    color: #30C465;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none; }
  .annotator-controls .annotator-save:focus {
    color: #30C465;
    text-decoration: none;
    background-color: #FFFFFF;
    background-image: none;
    text-shadow: none; }
.annotator-controls .annotator-focus {
  font-weight: 600 !important;
  text-transform: uppercase;
  color: #30C465 !important;
  border-color: transparent !important;
  background-color: #FFFFFF !important;
  background-image: none !important;
  text-shadow: none !important;
  letter-spacing: 0.04em; }

.annotator-editor a::after {
  position: absolute;
  top: 50%;
  left: 5px;
  display: block;
  content: "";
  width: 15px;
  height: 15px;
  margin-top: -7px;
  background-position: 0 -90px; }

.annotator-editor a:hover,
.annotator-editor a:focus,
.annotator-editor a.annotator-focus,
.annotator-filter .annotator-filter-active label,
.annotator-filter .annotator-filter-navigation button:hover {
  outline: 0;
  border-color: #435aa0;
  background-color: #3865f9;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#7691fb), color-stop(0.5, #5075fb), color-stop(0.5, #3865f9), to(#3665fa));
  background-image: -moz-linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
  background-image: -webkit-linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
  background-image: linear-gradient(to bottom, #7691fb, #5075fb 50%, #3865f9 50%, #3665fa);
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.42); }

.annotator-editor a:hover::after,
.annotator-editor a:focus::after {
  margin-top: -8px;
  background-position: 0 -105px; }

.annotator-editor a:active,
.annotator-filter .annotator-filter-navigation button:active {
  border-color: #700c49;
  background-color: #d12e8e;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#fc7cca), color-stop(0.5, #e85db2), color-stop(0.5, #d12e8e), to(#ff009c));
  background-image: -moz-linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
  background-image: -webkit-linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c);
  background-image: linear-gradient(to bottom, #fc7cca, #e85db2 50%, #d12e8e 50%, #ff009c); }

.annotator-editor a.annotator-save::after {
  background-position: 0 -120px; }

.annotator-editor a.annotator-save:hover::after,
.annotator-editor a.annotator-save:focus::after,
.annotator-editor a.annotator-save.annotator-focus::after {
  margin-top: -8px;
  background-position: 0 -135px; }

.annotator-editor .annotator-widget::after {
  background-position: 0 -30px; }

.annotator-editor.annotator-invert-y .annotator-widget .annotator-controls {
  background-color: #FFFFFF; }

.annotator-editor.annotator-invert-y .annotator-widget::after {
  background-position: 0 -45px;
  height: 11px; }

.annotator-resize {
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-position: 2px -150px;
  display: none; }

.annotator-invert-x .annotator-resize {
  right: auto;
  left: 0;
  background-position: 0 -195px; }

.annotator-invert-y .annotator-resize {
  top: auto;
  bottom: 0;
  background-position: 2px -165px; }

.annotator-invert-y.annotator-invert-x .annotator-resize {
  background-position: 0 -180px; }

.annotator-notice {
  color: #fff;
  position: absolute;
  position: fixed;
  top: -54px;
  left: 0;
  width: 100%;
  font-size: 14px;
  line-height: 50px;
  text-align: center;
  background: black;
  background: rgba(0, 0, 0, 0.9);
  border-bottom: 4px solid #d4d4d4;
  -webkit-transition: top .4s ease-out;
  -moz-transition: top .4s ease-out;
  -o-transition: top .4s ease-out;
  transition: top 0.4s ease-out; }

.ie6 .annotator-notice {
  position: absolute; }

.annotator-notice-success {
  border-color: #3665f9; }

.annotator-notice-error {
  border-color: #ff7e00; }

.annotator-notice p {
  margin: 0; }

.annotator-notice a {
  color: #fff; }

.annotator-notice-show {
  top: 0; }

.annotator-tags {
  margin-bottom: -2px; }

.annotator-tags .annotator-tag {
  display: inline-block;
  padding: 0 8px;
  margin-bottom: 2px;
  line-height: 1.6;
  font-weight: bold;
  background-color: #e6e6e6;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  border-radius: 8px; }

.annotator-filter {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  text-align: left;
  line-height: 0;
  border: 0;
  border-bottom: 1px solid #878787;
  padding-left: 10px;
  padding-right: 10px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -o-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  -moz-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  -o-box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 -1px 0 rgba(255, 255, 255, 0.3); }

.annotator-filter strong {
  font-size: 12px;
  font-weight: bold;
  color: #3c3c3c;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.7);
  position: relative;
  top: -9px; }

.annotator-filter .annotator-filter-property,
.annotator-filter .annotator-filter-navigation {
  position: relative;
  display: inline-block;
  overflow: hidden;
  line-height: 10px;
  padding: 2px 0;
  margin-right: 8px; }

.annotator-filter .annotator-filter-property label,
.annotator-filter .annotator-filter-navigation button {
  text-align: left;
  display: block;
  float: left;
  line-height: 20px;
  -webkit-border-radius: 10px 0 0 10px;
  -moz-border-radius: 10px 0 0 10px;
  -o-border-radius: 10px 0 0 10px;
  border-radius: 10px 0 0 10px; }

.annotator-filter .annotator-filter-property label {
  padding-left: 8px; }

.annotator-filter .annotator-filter-property input {
  display: block;
  float: right;
  -webkit-appearance: none;
  background-color: #fff;
  border: 1px solid #878787;
  border-left: none;
  padding: 2px 4px;
  line-height: 16px;
  min-height: 16px;
  font-size: 12px;
  width: 150px;
  color: #333;
  background-color: #f8f8f8;
  -webkit-border-radius: 0 10px 10px 0;
  -moz-border-radius: 0 10px 10px 0;
  -o-border-radius: 0 10px 10px 0;
  border-radius: 0 10px 10px 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
  -o-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2); }

.annotator-filter .annotator-filter-property input:focus {
  outline: 0;
  background-color: #fff; }

.annotator-filter .annotator-filter-clear {
  position: absolute;
  right: 3px;
  top: 6px;
  border: 0;
  text-indent: -900em;
  width: 15px;
  height: 15px;
  background-position: 0 -90px;
  opacity: 0.4; }

.annotator-filter .annotator-filter-clear:hover,
.annotator-filter .annotator-filter-clear:focus {
  opacity: 0.8; }

.annotator-filter .annotator-filter-clear:active {
  opacity: 1; }

.annotator-filter .annotator-filter-navigation button {
  border: 1px solid #a2a2a2;
  padding: 0;
  text-indent: -900px;
  width: 20px;
  min-height: 22px;
  -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -moz-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  -o-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8);
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2), inset 0 0 1px rgba(255, 255, 255, 0.8); }

.annotator-filter .annotator-filter-navigation button,
.annotator-filter .annotator-filter-navigation button:hover,
.annotator-filter .annotator-filter-navigation button:focus {
  color: transparent; }

.annotator-filter .annotator-filter-navigation button::after {
  position: absolute;
  top: 8px;
  left: 8px;
  content: "";
  display: block;
  width: 9px;
  height: 9px;
  background-position: 0 -210px; }

.annotator-filter .annotator-filter-navigation button:hover::after {
  background-position: 0 -225px; }

.annotator-filter .annotator-filter-navigation .annotator-filter-next {
  -webkit-border-radius: 0 10px 10px 0;
  -moz-border-radius: 0 10px 10px 0;
  -o-border-radius: 0 10px 10px 0;
  border-radius: 0 10px 10px 0;
  border-left: none; }

.annotator-filter .annotator-filter-navigation .annotator-filter-next::after {
  left: auto;
  right: 7px;
  background-position: 0 -240px; }

.annotator-filter .annotator-filter-navigation .annotator-filter-next:hover::after {
  background-position: 0 -255px; }

.annotator-hl-active {
  background: rgba(255, 255, 10, 0.8); }

.annotator-hl-filtered {
  background-color: transparent; }

/*  Annotator Touch Plugin - v1.1.1
 *  Copyright 2012-2015, Compendio <www.compendio.ch>
 *  Released under the MIT license
 *  More Information: https://github.com/aron/annotator.touch.js
 */
.annotator-viewer .annotator-touch-controls .annotator-edit::after {
  /* Assign the image once to ensure data uri is not repeated. */
  background-image: url("data:image/png;base64,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"); }

.annotator-selection-handle::after {
  background-image: url("data:image/png;base64,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"); }

.annotator-button::after {
  background-image: url("data:image/png;base64,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"); }

/* Generic Touch Widget Styles */
.annotator-touch-widget * {
  font-family: "Montserrat", sans-serif !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  font-style: normal !important; }

.annotator-touch-widget {
  font-family: "Montserrat", sans-serif;
  font-weight: 400 !important;
  font-size: 14px !important;
  border: 1px solid rgba(0, 0, 0, 0.8);
  background: rgba(0, 0, 0, 0.85);
  background: -webkit-gradient(linear, left top, left bottom, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)), color-stop(0.5, rgba(0, 0, 0, 0.85)), to(rgba(0, 0, 0, 0.85)));
  background: -moz-linear-gradient(-90deg, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)), color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
  background: -webkit-linear-gradient(-90deg, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)) 50%, color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
  background: linear-gradient(to bottom, from(rgba(57, 57, 57, 0.85)), color-stop(0.5, rgba(58, 58, 58, 0.85)) 50%, color-stop(0.5, rgba(0, 0, 0, 0.85)) 50%, to(rgba(0, 0, 0, 0.85)));
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  -ms-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  -o-box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4);
  /* Removes the tap outline on elements that have bound touch events */
  -webkit-tap-highlight-color: transparent; }
  .annotator-touch-widget #annotator-field-1 {
    display: none; }

/*.annotator-touch-widget-inner {
  background: #efefef;
  border: 1px solid rgba(0, 0, 0, 0.8);
  margin: 7px;
  padding: 6px;
  line-height: 0;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -ms-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  -o-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.8);
}*/
/*.annotator-touch-widget .annotator-button {
  cursor: pointer;
  font-size: 16px;
  line-height: 44px;
  padding-left: 40px;
  padding-right: 20px;
  -webkit-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.1);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}*/
.annotator-touch-widget .annotator-button[disabled] {
  opacity: 0.3;
  cursor: default; }

.annotator-touch-widget .annotator-button::after {
  left: 15px; }

.annotator-touch-widget .annotator-add::after,
.annotator-touch-widget .annotator-add:hover::after,
.annotator-touch-widget .annotator-add:focus::after,
.annotator-touch-widget .annotator-add.annotator-focus::after {
  margin-top: -7px;
  background-position: 0 -270px; }

/* Adder Styles */
.annotator-touch-controls {
  position: fixed;
  top: 30%;
  font-size: 10px;
  line-height: 1;
  min-width: auto;
  background: #FEFEFE;
  border: 0.5px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  box-shadow: 8px 2px 32px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  padding: 4px 16px;
  margin-top: 0;
  margin-left: 0;
  z-index: 3;
  left: 80px; }

.annotator-touch-controls.annotator-touch-hide {
  right: -9999em;
  opacity: 0;
  -webkit-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  -moz-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  -ms-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  -o-transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear;
  transition: opacity 0.2s 0 ease-in, right 0s 0.3s linear; }

/*.annotator-touch-controls .annotator-button {
  line-height: 56px;
}*/
/* Viewer Overrides*/
.annotator-touch-viewer .annotator-widget {
  min-width: 380px; }

.annotator-touch-viewer div {
  padding: 12px; }

.annotator-touch-viewer div:first-of-type {
  font-size: 18px;
  padding-top: 20px;
  padding-bottom: 20px; }

.annotator-touch-viewer .annotator-touch-controls {
  position: absolute;
  top: 0;
  left: auto;
  right: 0;
  display: none;
  background: #fff;
  -webkit-box-pack: end;
  -webkit-box-align: center;
  -webkit-box-orient: horizontal;
  -moz-box-pack: end;
  -moz-box-align: center;
  -moz-box-orient: horizontal;
  box-pack: end;
  box-align: center;
  box-orient: horizontal;
  padding: 10px;
  bottom: 0;
  padding: 0 10px 0 20px; }

.annotator-touch-viewer li.annotator-visible .annotator-touch-controls {
  display: -webkit-box;
  display: -moz-box;
  display: box; }

.annotator-touch-viewer .annotator-touch-controls button {
  line-height: 44px;
  padding-left: 40px;
  padding-right: 20px;
  margin-left: 6px;
  border: 0; }

.annotator-touch-viewer .annotator-touch-controls .annotator-edit::after {
  background-position: 0 -15px; }

.annotator-touch-controls .annotator-edit {
  color: #2F80ED; }
.annotator-touch-controls .annotator-delete {
  color: #B72319; }

.annotator-touch-viewer .annotator-touch-controls .annotator-edit:hover::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:focus::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit:active::after,
.annotator-touch-viewer .annotator-touch-controls .annotator-edit.annotator-focus::after {
  background-position: 0 -30px; }

.annotator-touch-viewer .annotator-touch-controls button::after {
  left: 15px; }

/* Editor Overrides */
.annotator-touch-editor {
  position: fixed;
  top: -1000px !important;
  left: 0 !important;
  right: 0;
  bottom: -1000px;
  padding: 1000px 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  display: -webkit-box;
  display: -moz-box;
  display: box;
  -webkit-box-pack: center;
  -webkit-box-align: center;
  -moz-box-pack: center;
  -moz-box-align: center;
  box-pack: center;
  box-align: center; }

.annotator-touch-editor .annotator-touch-widget {
  pointer-events: all;
  position: relative;
  width: 80%;
  max-width: 680px; }

.annotator-touch-editor .annotator-touch-widget-inner {
  position: static;
  width: auto;
  padding: 0;
  background: #fff; }

.annotator-touch-editor .annotator-widget::after {
  display: none; }

.annotator-touch-editor .annotator-widget .annotator-item {
  border-top-color: rgba(33, 150, 83, 0.24); }

.annotator-touch-editor .annotator-widget .annotator-item,
.annotator-touch-editor.annotator-editor .annotator-item label,
.annotator-touch-editor.annotator-editor .annotator-item input,
.annotator-touch-editor.annotator-editor .annotator-item textarea {
  font-size: 18px; }

.annotator-touch-editor.annotator-editor .annotator-item input,
.annotator-touch-editor.annotator-editor .annotator-item label {
  line-height: 30px;
  margin-left: 8px; }

.annotator-touch-editor.annotator-editor .annotator-item input[checkbox] {
  font-size: large; }

.annotator-touch-editor .annotator-widget .annotator-item:first-child textarea {
  font-size: 18px;
  background-color: #fff;
  -webkit-border-radius: 3px 3px 0 0;
  -moz-border-radius: 3px 3px 0 0;
  -o-border-radius: 3px 3px 0 0;
  border-radius: 3px 3px 0 0; }

.annotator-touch-editor .annotator-resize {
  display: none; }

.annotator-touch-editor .annotator-controls {
  padding: 7px;
  background-color: #fff;
  background-image: none; }

.annotator-touch-editor .annotator-item-quote {
  font-size: 16px;
  line-height: 1.2;
  border-color: rgba(33, 150, 83, 0.24);
  background-color: rgba(33, 150, 83, 0.24);
  color: #000;
  padding: 10px 7px; }

.annotator-item-quote span {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #000;
  font-style: italic !important; }

.annotator-item-quote.annotator-touch-expand span {
  overflow: visible;
  text-overflow: inherit;
  white-space: inherit; }

.annotator-item-quote button {
  font-size: 14px;
  line-height: 44px;
  margin-top: -13px;
  float: right;
  text-transform: uppercase;
  font-weight: bold;
  color: #a58129;
  border: none;
  background: none;
  margin-left: 10px;
  cursor: pointer; }

.annotator-button::after {
  background-repeat: no-repeat; }

/*.annotator-button {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  position: relative;
  display: inline-block;
  padding: 0 6px 0 22px;
  color: rgb(54, 54, 54);
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
  text-decoration: none;
  line-height: 24px;
  font-size: 12px;
  font-weight: bold;
  border: 1px solid rgb(162, 162, 162);
  background-color: rgb(212, 212, 212);
  background-image: -webkit-gradient(
    linear, left top, left bottom,
    from(rgb(245, 245, 245)),
    color-stop(0.5, rgb(210, 210, 210)),
    color-stop(0.5, rgb(190, 190, 190)),
    to(rgb(210, 210, 210))
  );
  background-image: -moz-linear-gradient(
      -90deg,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  background-image: -webkit-linear-gradient(
      -90deg,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  background-image: linear-gradient(
      to bottom,
      rgb(245, 245, 245),
      rgb(210, 210, 210) 50%,
      rgb(190, 190, 190) 50%,
      rgb(210, 210, 210)
  );
  -webkit-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -moz-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -o-box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  box-shadow:
    inset 0 0 5px rgba(255, 255, 255, 0.2),
    inset 0 0 1px rgba(255, 255, 255, 0.8);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
}*/
/*.annotator-button::after {
  position: absolute;
  top: 50%;
  left: 5px;
  display: block;
  content: "";
  width: 15px;
  height: 15px;
  margin-top: -7px;
  background-position: 0 -90px;
}*/
.annotator-button:hover,
.annotator-button:focus,
.annotator-button.annotator-focus {
  color: rgba(68, 68, 68, 0.5);
  display: inline-block;
  margin: 0 auto;
  border: 0;
  background: 0;
  cursor: pointer;
  padding: 8px 32px;
  font-weight: 300;
  font-size: 14px; }

.annotator-button.annotator-focus:first-child {
  border-right: 1px solid rgba(68, 68, 68, 0.24); }

/*.annotator-button:hover::after,
.annotator-button:focus::after {
  margin-top: -8px;
  background-position: 0 -105px;
}*/
/*.annotator-button:active {
  border-color: rgb(112, 12, 73);
  background-color: rgb(209, 46, 142);
  background-image: -webkit-gradient(
    linear, left top, left bottom,
    from(rgb(252, 124, 202)),
    color-stop(0.5, rgb(232, 93, 178)),
    color-stop(0.5, rgb(209, 46, 142)),
    to(rgb(255, 0, 156))
  );
  background-image: -moz-linear-gradient(
      -90deg,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
  background-image: -webkit-linear-gradient(
      -90deg,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
  background-image: linear-gradient(
      to bottom,
      rgb(252, 124, 202),
      rgb(232, 93, 178) 50%,
      rgb(209, 46, 142) 50%,
      rgb(255, 0, 156)
  );
}

.annotator-button.annotator-save::after {
  background-position: 0 -120px;
}*/
.annotator-button.annotator-save::after,
.annotator-button.annotator-save:focus::after,
.annotator-button.annotator-save.annotator-focus::after {
  margin-top: -8px;
  background-position: 0 -135px; }

/* Icon only button styles */
[data-annotator-use-icons] .annotator-touch-widget .annotator-button {
  /* width & overflow is required by Android WebKit */
  width: 1px;
  overflow: hidden;
  text-indent: -999em;
  padding-left: 25px; }

[data-annotator-use-icons] .annotator-touch-controls .annotator-button {
  padding-left: 35px; }

[data-annotator-use-icons] .annotator-touch-controls .annotator-button::after {
  left: 20px; }

[data-annotator-use-icons] .annotator-touch-viewer .annotator-touch-controls button {
  padding-left: 25px;
  text-indent: -9000em; }

[data-annotator-use-icons] .annotator-touch-viewer .annotator-touch-controls button::after {
  left: 15px; }

[data-annotator-use-icons] .annotator-touch-viewer .annotator-widget {
  min-width: 320px; }

/* Highlighter Selection Styles */
.annotator-selection-handle {
  cursor: pointer;
  display: block;
  position: absolute;
  width: 44px;
  height: 44px;
  top: 0;
  left: 0;
  padding: 0;
  margin-left: -22px;
  margin-top: -22px;
  border-radius: 50%;
  /* Removes the tap outline on elements that have bound touch events */
  -webkit-tap-highlight-color: transparent; }

.annotator-selection-handle::after {
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  left: 50%;
  margin-left: -10px;
  bottom: -5px;
  background-position: 0 0;
  background-repeat: no-repeat; }

.annotator-selection-start::after {
  top: -5px;
  bottom: auto;
  background-position: 0 -20px; }

.annotator-selection-hide .annotator-selection-handle {
  display: none; }

/* Styles for smaller screens */
@media only screen and (max-width: 480px) {
  .annotator-touch-viewer {
    left: 0 !important;
    width: 100%;
    background: none;
    min-width: 0;
    border: none; }

  .annotator-touch-viewer .annotator-widget {
    position: static;
    left: 0;
    width: 100%;
    height: auto;
    min-width: 0;
    -webkit-box-sizing: border-box;
    -webkit-border-radius: none;
    border-radius: none; }

  .annotator-touch-viewer .annotator-widget::after {
    display: none; }

  .annotator-touch-editor {
    border: none;
    -webkit-box-align: start;
    -moz-box-align: start;
    box-align: start; }

  .annotator-touch-editor .annotator-touch-widget {
    width: 100%;
    max-width: auto;
    margin: 0;
    border-color: #333;
    border-left: none;
    border-right: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none; }

  .annotator-touch-editor .annotator-touch-widget-inner {
    width: 100%;
    max-width: auto;
    margin: 0;
    border: 0; }

  .annotator-touch-editor .annotator-controls {
    border-bottom: 1px solid #D4D4D4; }

  .annotator-touch-editor .annotator-touch-widget,
  .annotator-touch-editor .annotator-touch-widget-inner,
  .annotator-touch-editor .annotator-touch-widget .annotator-item:first-child textarea,
  .annotator-touch-editor .annotator-controls {
    border-radius: 0; } }
.chapters-modal {
  z-index: 9999; }
  .chapters-modal .modal-body {
    padding: 0;
    color: #444444; }

.book-details-aside {
  font-family: 'Montserrat', sans-serif;
  height: 100%;
  background: url("../images/wonderslate/pattern.png");
  background-repeat: no-repeat;
  background-position: right 24px top 24px;
  padding: 24px;
  border-right: 1px solid rgba(68, 68, 68, 0.2); }

.chapter-book-image-wrapper {
  width: 160px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  margin-bottom: 8px; }
  .chapter-book-image-wrapper img {
    border-radius: 4px; }

.book-name {
  max-width: 160px;
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  color: #444444;
  line-height: 19px;
  font-size: 14px;
  letter-spacing: 0.01em;
  margin: 0;
  margin-bottom: 8px; }

.publisher-name {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 300;
  line-height: 16px;
  font-size: 12px;
  letter-spacing: 0.01em;
  color: rgba(68, 68, 68, 0.74);
  margin: 0;
  margin-bottom: 8px; }
  .publisher-name span {
    color: #444444;
    font-weight: 500;
    line-height: 19px;
    font-size: 12px;
    letter-spacing: 0.01em;
    margin: 0; }

.chapter-book-price-details {
  padding: 0; }

.chapter-book {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  font-weight: 500;
  line-height: 11px;
  font-size: 8px;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  margin: 0; }

.offer-price {
  display: inline-block;
  font-size: 20px;
  font-weight: 500;
  color: #AE2B24;
  letter-spacing: 0.01em;
  margin-right: 4px; }
  .offer-price i {
    font-size: 18px; }

.original-price {
  display: inline-block;
  font-size: 16px;
  font-weight: 300;
  color: #909090;
  letter-spacing: 0.01em;
  text-decoration: line-through;
  margin: 0; }

.offer-chapters {
  float: left;
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: 24px; }
  .offer-chapters li {
    font-weight: 500;
    line-height: normal;
    font-size: 14px;
    margin-bottom: 16px; }
    .offer-chapters li .fa-tag {
      color: #AE2B24;
      margin-right: 8px; }

.book-buy {
  clear: both;
  float: none;
  width: 160px;
  margin: 0 auto;
  text-align: center;
  position: fixed;
  bottom: 24px;
  left: 11%; }

.complete-book-legend {
  font-weight: 300;
  line-height: normal;
  font-size: 14px;
  text-align: center; }

.buy-complete-book-btn {
  display: block;
  font-weight: 500;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.01em;
  color: #F05A2A;
  padding: 11px;
  border: 0.5px solid rgba(68, 68, 68, 0.54);
  box-sizing: border-box;
  border-radius: 4px; }
  .buy-complete-book-btn:focus {
    text-decoration: none;
    color: #F05A2A; }
  .buy-complete-book-btn:hover {
    text-decoration: none;
    color: #F05A2A; }
  .buy-complete-book-btn:active {
    text-decoration: none;
    color: #F05A2A; }

.chapter-modal-chapter-selection-table {
  display: block;
  min-height: 488px;
  max-height: 488px;
  padding-right: 16px;
  overflow: hidden;
  overflow-y: auto; }
  .chapter-modal-chapter-selection-table tbody {
    display: block;
    width: 100%; }
    .chapter-modal-chapter-selection-table tbody tr {
      display: block;
      width: 100%; }

.chapter-modal-chapter-selection-table > tbody > tr > td {
  padding: 0; }

.chapter-details-aside {
  height: 100%;
  padding: 16px;
  padding-right: 0; }
  .chapter-details-aside .chapter-name {
    display: block;
    position: relative;
    color: #444;
    font-size: 14px;
    font-weight: 500;
    margin-top: 24px;
    border-top: 0;
    border-bottom: 0;
    cursor: pointer; }
    .chapter-details-aside .chapter-name:hover {
      color: #F05A2A; }
    .chapter-details-aside .chapter-name.active {
      color: #F05A2A; }
    .chapter-details-aside .chapter-name label {
      cursor: pointer;
      font-size: 14px;
      color: #444;
      font-weight: 300; }
      .chapter-details-aside .chapter-name label:hover {
        color: #444; }
    .chapter-details-aside .chapter-name label.active {
      font-weight: 500;
      color: #444; }
  .chapter-details-aside input[type=checkbox] {
    position: absolute;
    opacity: 0; }
  .chapter-details-aside label {
    cursor: pointer;
    font-size: 14px;
    color: #444; }
    .chapter-details-aside label:hover {
      color: #F05A2A; }
  .chapter-details-aside label.active {
    font-weight: bold;
    color: #F05A2A;
    font-weight: 500; }
  .chapter-details-aside .checkmark {
    position: absolute;
    top: 0;
    right: 0;
    height: 18px;
    width: 18px;
    background-color: transparent;
    border: 2px solid #888; }
  .chapter-details-aside .chapter-name:hover input[type="checkbox"] ~ .checkmark {
    background-color: transparent;
    border: 2px solid #F05A2A; }
  .chapter-details-aside .chapter-name input[type="checkbox"]:checked ~ .checkmark {
    background-color: transparent;
    border: 2px solid #F05A2A; }
  .chapter-details-aside .chapter-name::after {
    content: '';
    position: absolute;
    display: none; }
  .chapter-details-aside .chapter-name input[type="checkbox"]:checked ~ .checkmark::after {
    content: '';
    position: absolute;
    display: block; }
  .chapter-details-aside .checkmark::after {
    left: 1px;
    top: 2px;
    width: 12px;
    height: 7px;
    border: solid #F05A2A;
    border-width: 3px 3px 0 0;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg); }

.select-chapters-legend {
  font-family: 'Abril Fatface', cursive;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 22px;
  letter-spacing: 0.04em;
  color: #444444;
  padding-bottom: 16px;
  margin: 0;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.book-unlock {
  float: none;
  width: 160px;
  margin: 0 auto;
  text-align: center;
  position: fixed;
  bottom: 24px;
  right: 11%; }

.unlock-complete-book-btn {
  color: #fff;
  font-family: 'Roboto', sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.01em;
  padding: 11px;
  display: block;
  font-weight: 500;
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  border-radius: 4px; }
  .unlock-complete-book-btn:hover {
    text-decoration: none;
    color: #fff; }
  .unlock-complete-book-btn:focus {
    text-decoration: none;
    color: #fff; }
  .unlock-complete-book-btn:active {
    text-decoration: none;
    color: #fff; }

@media screen and (min-width: 678px) {
  .chapters-modal .modal-dialog {
    width: 640px; }
  .chapters-modal .modal-body {
    height: 685px; } }
.main-footer {
  border-radius: 2px;
  border-top: 1px solid rgba(68, 68, 68, 0.24); }

.footer-container {
  clear: both;
  background-color: #F2F2F2;
  padding: 0 24px; }

.footer-credits, .wonderslate-mobile-app {
  font-size: 13px;
  color: rgba(68, 68, 68, 0.84);
  padding: 31px 0;
  margin: 0; }

.wonderslate-mobile-app {
  font-weight: 500;
  text-align: center;
  letter-spacing: -0.01em;
  padding: 10px 0; }

.download-app-btn {
  display: block;
  text-align: center;
  max-width: 122px;
  height: 40px;
  margin: 0 auto; }

.download-app-btn-img {
  width: 100%;
  height: auto;
  margin: 0 auto; }

.footer-links {
  list-style: none;
  text-align: right;
  padding: 0;
  margin: 0; }
  .footer-links li {
    display: inline-block; }
    .footer-links li a {
      display: block;
      font-size: 13px;
      color: rgba(68, 68, 68, 0.64);
      padding: 31px 0; }
      .footer-links li a:hover {
        color: #F05A2A;
        text-decoration: none; }

.top-books-list {
  float: left;
  width: 50%;
  padding: 24px 24px 8px 24px; }

.top-books-label {
  font-weight: 300;
  color: rgba(68, 68, 68, 0.64);
  line-height: normal;
  letter-spacing: 0.01em;
  margin: 0;
  margin-bottom: 16px; }

.top-book-title {
  clear: both;
  float: left;
  font-weight: 500;
  font-size: 16px;
  color: rgba(68, 68, 68, 0.84);
  letter-spacing: 0.01em;
  text-decoration: none;
  margin-bottom: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical; }
  .top-book-title:hover {
    color: rgba(68, 68, 68, 0.84);
    text-decoration: none; }
  .top-book-title:focus {
    color: rgba(68, 68, 68, 0.84);
    text-decoration: none; }
  .top-book-title:active {
    color: rgba(68, 68, 68, 0.84);
    text-decoration: none; }

.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  vertical-align: middle;
  z-index: 1;
  transition: .3s ease-out; }
  .waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    opacity: 0;
    background: rgba(0, 0, 0, 0.2);
    transition: all 0.7s ease-out;
    transition-property: transform, opacity;
    transform: scale(0);
    pointer-events: none; }
  .waves-effect.waves-light .waves-ripple {
    background-color: rgba(255, 255, 255, 0.45); }
  .waves-effect.waves-red .waves-ripple {
    background-color: rgba(244, 67, 54, 0.7); }
  .waves-effect.waves-yellow .waves-ripple {
    background-color: rgba(255, 235, 59, 0.7); }
  .waves-effect.waves-orange .waves-ripple {
    background-color: rgba(255, 152, 0, 0.7); }
  .waves-effect.waves-purple .waves-ripple {
    background-color: rgba(156, 39, 176, 0.7); }
  .waves-effect.waves-green .waves-ripple {
    background-color: rgba(76, 175, 80, 0.7); }
  .waves-effect.waves-teal .waves-ripple {
    background-color: rgba(0, 150, 136, 0.7); }
  .waves-effect input[type="button"], .waves-effect input[type="reset"], .waves-effect input[type="submit"] {
    border: 0;
    font-style: normal;
    font-size: inherit;
    text-transform: inherit;
    background: none; }
  .waves-effect img {
    position: relative;
    z-index: -1; }

.waves-notransition {
  transition: none !important; }

.waves-circle {
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom; }
  .waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1; }

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
  -webkit-mask-image: none; }

.waves-block {
  display: block; }

/* Firefox Bug: link not triggered */
.waves-effect .waves-ripple {
  z-index: -1; }

.preview-book-container {
  min-height: calc(100vh - 204px);
  margin: 24px auto; }

.wonderslate-breadcrumb {
  font-size: 12px;
  background: transparent;
  padding: 0 15px;
  margin-bottom: 0; }
  .wonderslate-breadcrumb li {
    color: #000000;
    font-weight: 300;
    letter-spacing: 0.023em; }
    .wonderslate-breadcrumb li a {
      display: block;
      color: #000000; }
      .wonderslate-breadcrumb li a:hover {
        color: #F05A2A; }
    .wonderslate-breadcrumb li.active {
      color: #000000;
      font-weight: 500; }
    .wonderslate-breadcrumb li:before {
      content: '';
      color: #000000;
      padding: 0; }
  .wonderslate-breadcrumb li + li:before {
    content: '';
    color: #000000;
    padding: 0; }

.preview-book-wrapper {
  padding: 0 40px;
  margin-top: 40px; }

.preview-book-image {
  max-width: 160px;
  height: 160px;
  padding: 0;
  z-index: 0; }
  .preview-book-image .book-img {
    height: 100%; }
    .preview-book-image .book-img img {
      height: 100%;
      border-radius: 4px;
      box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25); }

.book-preview-detail {
  padding: 8px 24px; }

.preview-book-name {
  font-family: "Montserrat", sans-serif;
  font-size: 22px;
  font-weight: 500;
  margin: 0 0 16px 0; }

.author-name {
  font-size: 16px;
  font-weight: 300;
  color: rgba(68, 68, 68, 0.74);
  line-height: 21px;
  margin: 0 0 16px 0; }

.offer-price {
  font-size: 20px;
  font-weight: 400;
  color: #AE2B24;
  letter-spacing: 0.01em; }

.preview-book-btns {
  margin-top: 24px; }

.btn-book-preview {
  color: #F05A2A;
  font-weight: 500;
  line-height: normal;
  text-align: center;
  letter-spacing: 0.01em;
  background: #FFFFFF;
  border: 1px solid rgba(68, 68, 68, 0.54);
  border-radius: 4px;
  padding: 11px 0;
  margin-bottom: 16px; }
  .btn-book-preview:hover {
    color: #F05A2A;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25); }
  .btn-book-preview:focus {
    color: #F05A2A; }
  .btn-book-preview:active {
    color: #F05A2A; }

.btn-book-buy {
  color: #FFFFFF;
  font-weight: 500;
  line-height: normal;
  text-align: center;
  letter-spacing: 0.01em;
  background: -webkit-linear-gradient(right, #30C465 0%, #3AE878 100%);
  background: -o-linear-gradient(right, #30C465 0%, #3AE878 100%);
  background: linear-gradient(to left, #30C465 0%, #3AE878 100%);
  border: 0;
  border-radius: 4px;
  padding: 11px 0; }
  .btn-book-buy:hover {
    color: #FFFFFF;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    background: -webkit-linear-gradient(right, #30C465 0%, #3AE878 100%);
    background: -o-linear-gradient(right, #30C465 0%, #3AE878 100%);
    background: linear-gradient(to left, #30C465 0%, #3AE878 100%); }
  .btn-book-buy:focus {
    color: #FFFFFF; }
  .btn-book-buy:active {
    color: #FFFFFF; }

.preview-book-desc {
  width: 100%;
  background-color: #FFFFFF !important;
  background-repeat: no-repeat;
  background-position: 64px 51px;
  background-size: 90%;
  padding: 40px 25px;
  margin-top: -40px;
  margin-left: 0;
  border-radius: 4px; }

.book-preview-desc {
  padding-left: 24px;
  margin-top: -22px;
  margin-bottom: 8px; }

.book-desc-legend {
  font-family: "Abril Fatface", cursive;
  font-size: 22px;
  letter-spacing: 0.01em;
  margin: 0 0 8px 0; }

.book-desc {
  font-family: "Work Sans", sans-serif;
  font-size: 16px;
  line-height: 21px;
  max-height: 166px;
  overflow: hidden; }

.show-full-desc {
  position: absolute;
  bottom: 0;
  font-size: 24px;
  text-align: center;
  width: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) -25.75%, rgba(255, 255, 255, 0.91) 36.14%, #FFFFFF 78.54%);
  transition: all 0.3s ease; }
  .show-full-desc .icon-back {
    display: inline-block;
    transition: all 0.3s ease;
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
    -ms-transform: rotate(-90deg);
    -o-transform: rotate(-90deg);
    transform: rotate(-90deg); }
  .show-full-desc:hover {
    text-decoration: none;
    color: #444; }
  .show-full-desc:focus {
    text-decoration: none;
    color: #444; }
  .show-full-desc:active {
    text-decoration: none;
    color: #444; }

.book-test-wise-wrapper {
  padding: 0; }

.chapter-test-wise-detail-wrapper {
  width: 30%;
  padding: 0;
  margin: 32px 57px 0 0; }

.chapter-test-wise-detail-wrapper:nth-child(3n) {
  margin: 32px 0 0 0; }

.chapter-test-wise-detail-btn {
  color: rgba(68, 68, 68, 0.74);
  background: #FFFFFF;
  padding: 8px 17px;
  border-radius: 4px;
  font-weight: 500;
  display: block;
  -webkit-transition: all .4s ease;
  -moz-transition: all .4s ease;
  -ms-transition: all .4s ease;
  -o-transition: all .4s ease;
  transition: all .4s ease; }
  .chapter-test-wise-detail-btn:hover {
    background: #F05A2A;
    color: #FFFFFF;
    text-decoration: none;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); }
    .chapter-test-wise-detail-btn:hover i.fa-tag {
      color: #FFFFFF; }
  .chapter-test-wise-detail-btn:focus {
    background: #F05A2A;
    color: #FFFFFF;
    text-decoration: none; }
    .chapter-test-wise-detail-btn:focus i.fa-tag {
      color: #FFFFFF; }
  .chapter-test-wise-detail-btn:active {
    background: #F05A2A;
    color: #FFFFFF;
    text-decoration: none; }
    .chapter-test-wise-detail-btn:active i.fa-tag {
      color: #FFFFFF; }
  .chapter-test-wise-detail-btn i.fa-tag {
    color: #AE2B24;
    margin-right: 8px; }

.amazon-buy {
  text-indent: -999999px;
  background: url("../images/wonderslate/amazon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 60%; }
  .amazon-buy:active {
    background: url("../images/wonderslate/amazon.svg");
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 60%; }
  .amazon-buy:focus {
    background: url("../images/wonderslate/amazon.svg");
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 60%; }

.flipkart-buy {
  text-indent: -999999px;
  background: url("../images/wonderslate/flipkart.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 60%; }
  .flipkart-buy:active {
    background: url("../images/wonderslate/flipkart.svg");
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 60%; }
  .flipkart-buy:focus {
    background: url("../images/wonderslate/flipkart.svg");
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 60%; }

.marketplace-rating-wrapper {
  padding: 0;
  margin-top: 32px; }

.marketplace-rating {
  padding-left: 0; }

.marketplace-rating-link {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 16px;
  color: rgba(68, 68, 68, 0.84); }
  .marketplace-rating-link:hover {
    color: rgba(68, 68, 68, 0.84);
    text-decoration: none; }
  .marketplace-rating-link:active {
    color: rgba(68, 68, 68, 0.84);
    text-decoration: none; }
  .marketplace-rating-link:focus {
    color: rgba(68, 68, 68, 0.84);
    text-decoration: none; }
  .marketplace-rating-link span {
    margin-right: 4px; }

.marketplace-rating-link:first-child {
  padding-right: 24px;
  border-right: 1px solid rgba(68, 68, 68, 0.2); }

.marketplace-rating-link:last-child {
  padding-left: 24px;
  border-right: 0; }

.marketplace-total-rating-star {
  font-size: 16px;
  color: #E5C260; }

.marketplace-image {
  width: 72px; }
  .marketplace-image img {
    max-width: 100%;
    display: inline-block; }

.marketplace-link {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
  font-size: 12px;
  text-transform: uppercase;
  font-variant: small-caps;
  color: rgba(68, 68, 68, 0.74);
  display: inline-block;
  vertical-align: -webkit-baseline-middle; }

.marketplace-buy-legend {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  line-height: 19px;
  font-size: 14px;
  text-transform: uppercase;
  color: rgba(68, 68, 68, 0.74);
  margin-bottom: 16px; }

.book-reviews {
  float: left;
  width: 100%;
  background: #FFFFFF;
  padding: 24px 40px;
  margin: 32px 0 0 0;
  border-radius: 4px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.review-rating-wrapper {
  padding: 0;
  margin-bottom: 32px; }

.rating-review-heading {
  font-family: "Abril Fatface", cursive;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 22px;
  letter-spacing: 0.04em; }

.book-overall-rating {
  padding: 0;
  padding-right: 40px; }

.rating-by-user {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.01em;
  margin: 0 0 24px 0; }

.rating-bars-wrapper {
  padding: 0 40px;
  border-right: 1px solid rgba(68, 68, 68, 0.2);
  border-left: 1px solid rgba(68, 68, 68, 0.2); }

.user-ratings-holder {
  clear: both;
  float: left;
  width: 100%;
  margin-bottom: 8px; }

.side-rating {
  float: left;
  width: 15%;
  text-align: left; }

.side-rating-right {
  float: left;
  width: 15%;
  text-align: center; }

.rating-progress-bar-wrapper {
  float: left;
  width: 70%;
  margin: 0;
  background-color: rgba(68, 68, 68, 0.24);
  box-shadow: none;
  -webkit-box-shadow: none;
  -moz-box-shadow: none; }
  .rating-progress-bar-wrapper .progress-bar {
    background: linear-gradient(90deg, #EDD38A 0%, #E5C260 100%); }

.user-write-review {
  padding: 0 40px; }

.write-review-label {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 24px; }

.write-review-btn {
  font-size: 16px;
  color: #FFFFFF;
  letter-spacing: 0.01em;
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  padding: 11px 11px;
  border-radius: 4px;
  text-decoration: none; }
  .write-review-btn:hover {
    color: #FFFFFF;
    text-decoration: none; }
  .write-review-btn:active {
    color: #FFFFFF;
    text-decoration: none; }
  .write-review-btn:focus {
    color: #FFFFFF;
    text-decoration: none; }

.user-reviews {
  padding: 24px;
  margin-top: 24px;
  border-top: 1px solid rgba(68, 68, 68, 0.2); }

.review-user-name {
  font-weight: 500;
  font-size: 16px;
  text-transform: capitalize; }
  .review-user-name .review-date {
    font-weight: 300;
    font-size: 12px;
    color: rgba(68, 68, 68, 0.74);
    margin-left: 8px; }

.user-full-review-wrapper {
  margin-bottom: 24px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.user-full-review {
  display: block !important;
  font-weight: 400;
  letter-spacing: 0.01em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  margin: 0 0 24px; }
  .user-full-review::first-letter {
    text-transform: uppercase; }

.read-book-container {
  min-height: calc(100vh - 160px); }

.book-read-tabs {
  position: relative;
  background: #FFFFFF;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12);
  z-index: 2; }

.read-book-name {
  width: 15%;
  padding: 8px 15px 8px;
  border-right: 1px solid rgba(68, 68, 68, 0.2);
  overflow: hidden; }

.read-book-title {
  display: inline-block;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.74);
  letter-spacing: 0.023em;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical; }

.android-like-menu {
  display: inline-block;
  float: right; }

.android-menu-items-display {
  top: 0;
  display: block;
  left: 10px;
  width: 90%; }

.read-book-chapters {
  width: 15%;
  min-height: calc(100vh - 122px);
  max-height: calc(100vh - 122px);
  background-color: #EEEEEE;
  -webkit-transition: transform .3s ease-in-out;
  -moz-transition: transform .3s ease-in-out;
  -ms-transition: transform .3s ease-in-out;
  -o-transition: transform .3s ease-in-out;
  transition: transform .3s ease-in-out;
  overflow: auto; }

.read-book-chapters-slide-left {
  -webkit-transform: translateX(-105%);
  -moz-transform: translateX(-105%);
  -ms-transform: translateX(-105%);
  -o-transform: translateX(-105%);
  transform: translateX(-105%);
  -webkit-transition: transform .3s ease-in-out;
  -moz-transition: transform .3s ease-in-out;
  -ms-transition: transform .3s ease-in-out;
  -o-transition: transform .3s ease-in-out;
  transition: transform .3s ease-in-out; }

.read-book-chapters::-webkit-scrollbar {
  width: 7px;
  height: 5px; }

.read-book-chapters::-webkit-scrollbar-track {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1); }

.read-book-chapters::-webkit-scrollbar-thumb {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2); }

.read-book-chapters::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4); }

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.05); }

.chapters-label {
  font-size: 18px;
  font-weight: 300;
  color: rgba(68, 68, 68, 0.84);
  padding: 16px 5px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.read-book-chapters-wrapper {
  width: 100%;
  min-height: calc(100vh - 250px);
  padding: 8px 16px 8px 16px;
  margin: 0; }

.chapter-name {
  position: relative;
  font-weight: 500;
  font-size: 14px;
  color: rgba(68, 68, 68, 0.34);
  padding: 8px; }
  .chapter-name a {
    font-weight: 300;
    color: rgba(68, 68, 68, 0.84);
    text-transform: capitalize; }
    .chapter-name a:hover {
      color: #F05A2A;
      text-decoration: none; }
    .chapter-name a.orangeText {
      color: #F05A2A;
      font-weight: 500;
      text-decoration: none; }
  .chapter-name a.chapter-sub-section {
    font-size: 14px; }

.chapter-name > a {
  font-weight: 500; }

.read-book-chapters .preview-book-btns {
  margin: 16px 0; }

.book-read-material {
  width: 85%;
  min-height: calc(100vh - 122px);
  max-height: calc(100vh - 122px);
  background-color: #FFFFFF;
  padding-left: 0; }

.book-read-material-full-width {
  width: 100%; }

#htmlContent p {
  word-break: break-word; }
  #htmlContent p img {
    width: 100%; }

#htmlreadingcontent {
  max-width: 632px;
  padding: 16px 0;
  margin: 0 auto;
  overflow-wrap: break-word;
  padding-bottom: 6rem !important; }
  #htmlreadingcontent img {
    max-width: 100%;
    height: auto;
    border: 0; }
  #htmlreadingcontent h1 {
    font-family: "Work Sans", sans-serif;
    font-size: 30px !important;
    line-height: 36px !important;
    letter-spacing: 0.04em !important;
    color: #1B2733 !important; }
  #htmlreadingcontent h2 {
    font-family: "Work Sans", sans-serif;
    font-size: 24px !important;
    line-height: 30px !important;
    letter-spacing: 0.02em !important;
    color: #1B2733 !important; }
  #htmlreadingcontent h3 {
    font-family: "Work Sans", sans-serif;
    font-size: 18px !important;
    line-height: 28px !important;
    letter-spacing: 0.01em !important;
    color: #1B2733 !important; }
  #htmlreadingcontent p, #htmlreadingcontent span {
    font-family: "Work Sans", sans-serif; }
  #htmlreadingcontent span.annotator-hlh {
    font-size: inherit !important;
    font-family: inherit; }
  #htmlreadingcontent span.annotator-hl {
    font-size: inherit !important;
    font-family: inherit; }
  #htmlreadingcontent table {
    width: 100% !important;
    max-height: 700px !important;
    height: auto !important;
    overflow: auto;
    display: block;
    white-space: nowrap; }

.r-tab {
  width: 80%; }

.tabs-section {
  font-family: 'Montserrat', sans-serif; }
  .tabs-section .chapter-tabs {
    max-width: 85%;
    font-size: 24px;
    padding-left: 9px;
    white-space: nowrap;
    border-bottom: 0;
    overflow: hidden;
    overflow-x: auto; }
    .tabs-section .chapter-tabs li {
      display: inline-block;
      float: none; }
      .tabs-section .chapter-tabs li a {
        color: rgba(68, 68, 68, 0.64);
        font-size: 16px;
        font-weight: 500;
        border: 0;
        border-bottom: 2px solid transparent; }
        .tabs-section .chapter-tabs li a:visited {
          background-color: transparent;
          border: 0; }
        .tabs-section .chapter-tabs li a:active {
          background-color: transparent;
          border: 0; }
        .tabs-section .chapter-tabs li a:link {
          background-color: transparent;
          border: 0; }
      .tabs-section .chapter-tabs li a:hover {
        color: #F05A2A;
        background-color: transparent;
        border: 0;
        border-bottom: 5px solid #F05A2A; }
      .tabs-section .chapter-tabs li.active a {
        color: #F05A2A;
        padding: 0 15px 10px;
        border-bottom: 5px solid #F05A2A; }

#hideShowDiv {
  padding: 10px;
  background: #EEEEEE;
  position: fixed;
  top: 23%;
  left: 12.5%;
  width: 100px;
  height: 48px;
  display: block;
  background-size: 100% 100%;
  background-position: center;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
  border-top: 2px solid #F05A2A; }
#hideShowDiv:hover {
  text-decoration: none; }
.hideShowDiv-slide-left {
  position: fixed;
  left: -26px !important;
  -webkit-transition: all .1s ease-in-out;
  -moz-transition: all .1s ease-in-out;
  -ms-transition: all .1s ease-in-out;
  -o-transition: all .1s ease-in-out;
  transition: all .1s ease-in-out; }

.chapter-tabs {
  float: left; }

.zoom-icons {
  list-style: none;
  font-size: 24px;
  padding: 0;
  margin: 0;
  float: right; }
  .zoom-icons .zoom-icons-list-item {
    display: inline-block;
    float: none; }
    .zoom-icons .zoom-icons-list-item .text-format {
      position: relative;
      display: block;
      font-size: 22px;
      color: rgba(68, 68, 68, 0.64);
      line-height: 1.42857143;
      padding: 5px 16px;
      -webkit-transition: all .2s ease;
      -moz-transition: all .2s ease;
      -ms-transition: all .2s ease;
      -o-transition: all .2s ease;
      transition: all .2s ease; }
      .zoom-icons .zoom-icons-list-item .text-format:hover {
        color: #F05A2A;
        text-decoration: none; }
      .zoom-icons .zoom-icons-list-item .text-format:active {
        color: #F05A2A;
        text-decoration: none; }
      .zoom-icons .zoom-icons-list-item .text-format:focus {
        color: #F05A2A;
        text-decoration: none; }
    .zoom-icons .zoom-icons-list-item .text-format-dropdown {
      text-align: center;
      min-width: 262px;
      left: auto;
      right: 8px;
      padding: 0;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown:before {
        content: "";
        border-bottom: 10px solid #fff;
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
        position: absolute;
        top: -10px;
        right: 16px;
        z-index: 10; }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown:after {
        content: "";
        border-bottom: 12px solid #ccc;
        border-right: 12px solid transparent;
        border-left: 12px solid transparent;
        position: absolute;
        top: -12px;
        right: 14px;
        z-index: 9; }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown a {
        padding: 0; }
        .zoom-icons .zoom-icons-list-item .text-format-dropdown a.text-zoom {
          clear: both;
          display: block;
          font-size: 32px;
          font-weight: bold;
          padding: 18px 0; }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown li {
        display: inline-block; }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown a:hover {
        text-decoration: none;
        color: rgba(68, 68, 68, 0.64); }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown a:active {
        text-decoration: none;
        color: rgba(68, 68, 68, 0.64); }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown a:focus {
        text-decoration: none;
        color: rgba(68, 68, 68, 0.64); }
      .zoom-icons .zoom-icons-list-item .text-format-dropdown .separator {
        float: left;
        width: 100%;
        border-bottom: 1px solid rgba(68, 68, 68, 0.24); }
    .zoom-icons .zoom-icons-list-item .notes-menu-link {
      position: relative;
      display: block;
      font-size: 22px;
      background: url(../images/wonderslate/notes-icon.svg);
      background-repeat: no-repeat;
      background-size: 70% 85%;
      line-height: 1.42857143;
      padding: 5px 16px;
      -webkit-transition: all .2s ease;
      -moz-transition: all .2s ease;
      -ms-transition: all .2s ease;
      -o-transition: all .2s ease;
      transition: all .2s ease;
      text-indent: -9999999px; }
      .zoom-icons .zoom-icons-list-item .notes-menu-link:hover {
        color: #F05A2A;
        text-decoration: none;
        background: url(../images/wonderslate/notes-icon-active.svg);
        background-repeat: no-repeat;
        background-size: 70% 85%; }
      .zoom-icons .zoom-icons-list-item .notes-menu-link:active {
        color: #F05A2A;
        text-decoration: none;
        background: url(../images/wonderslate/notes-icon-active.svg);
        background-repeat: no-repeat;
        background-size: 70% 85%; }
      .zoom-icons .zoom-icons-list-item .notes-menu-link:focus {
        color: #F05A2A;
        text-decoration: none;
        background: url(../images/wonderslate/notes-icon-active.svg);
        background-repeat: no-repeat;
        background-size: 70% 85%; }

.text-formatter-left-border {
  border-right: 1px solid rgba(68, 68, 68, 0.24); }

.color-modes-wrapper {
  float: left;
  width: 100%;
  padding: 0;
  margin: 0; }
  .color-modes-wrapper .color-mode-list-item {
    float: left;
    width: 65px;
    height: 62px;
    cursor: pointer; }
  .color-modes-wrapper .color-mode-list-item.active {
    border-bottom: 2px solid #F05A2A; }
  .color-modes-wrapper .color-mode-list-item.white {
    background: #F2F2F2; }
  .color-modes-wrapper .color-mode-list-item.sepia {
    background: #D4C79F; }
  .color-modes-wrapper .color-mode-list-item.grey {
    background: #5A5A5C; }
  .color-modes-wrapper .color-mode-list-item.black {
    background: #000000; }
  .color-modes-wrapper .color-mode-list-item:last-child {
    margin-right: 0; }

.white-bg {
  background: #F2F2F2; }
  .white-bg a, .white-bg p, .white-bg h1, .white-bg h2, .white-bg h3, .white-bg h4, .white-bg h5, .white-bg h6, .white-bg span, .white-bg i {
    color: #000000 !important; }

.sepia-bg {
  background: #D4C79F; }
  .sepia-bg a, .sepia-bg p, .sepia-bg h1, .sepia-bg h2, .sepia-bg h3, .sepia-bg h4, .sepia-bg h5, .sepia-bg h6, .sepia-bg span, .sepia-bg i {
    color: #000000 !important; }

.grey-bg {
  background: #5A5A5C; }
  .grey-bg a, .grey-bg p, .grey-bg h1, .grey-bg h2, .grey-bg h3, .grey-bg h4, .grey-bg h5, .grey-bg h6, .grey-bg span, .grey-bg i {
    color: #FFFFFF !important; }

.black-bg {
  background: #000000; }
  .black-bg a, .black-bg p, .black-bg h1, .black-bg h2, .black-bg h3, .black-bg h4, .black-bg h5, .black-bg h6, .black-bg span, .black-bg i {
    color: #FFFFFF !important; }

.chapter-sections {
  padding: 12px;
  margin: 0; }
  .chapter-sections li {
    padding: 12px 0; }

.section-btns {
  width: 85%;
  height: 48px;
  position: fixed;
  z-index: 999;
  bottom: 0;
  background: #FFFFFF;
  padding: 8px 16px;
  box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24); }

#read .section-btns {
  z-index: 0; }

.prev-sec {
  font-size: 16px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.64);
  padding: 8px 16px 8px 0;
  border-right: 1px solid rgba(68, 68, 68, 0.2);
  float: left; }
  .prev-sec:hover {
    color: #F05A2A;
    text-decoration: none; }

.next-sec {
  font-size: 16px;
  font-weight: 500;
  color: rgba(68, 68, 68, 0.64);
  padding: 8px 0 8px 16px;
  border-left: 1px solid rgba(68, 68, 68, 0.2); }
  .next-sec:hover {
    color: #F05A2A;
    text-decoration: none; }

.write-book-review-read {
  width: 15%;
  background: #FFFFFF;
  padding: 8px;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 99; }

.close-Write-Book-Review {
  text-decoration: none; }
  .close-Write-Book-Review:hover {
    text-decoration: none;
    color: #444; }

.enjoy-reading {
  float: left;
  width: 100%;
  padding: 8px; }

.notes-creation-wrapper {
  position: fixed;
  right: 0;
  top: 122px;
  width: 512px;
  height: calc(100vh - 122px);
  background-color: #FFFFFF;
  margin-right: -600px;
  z-index: 999;
  overflow: hidden;
  overflow-y: auto; }
  .notes-creation-wrapper:before {
    content: ''; }

.notes-creation-header {
  float: left;
  width: 100%;
  background-color: #FFFFFF;
  padding: 16px; }

.notes-creation-header-title {
  display: inline-block;
  font-weight: 500;
  line-height: 21px;
  font-size: 16px;
  color: rgba(68, 68, 68, 0.84);
  margin: 0;
  -webkit-transform: translateY(50%);
  -moz-transform: translateY(50%);
  -ms-transform: translateY(50%);
  -o-transform: translateY(50%);
  transform: translateY(50%); }

.notes-list-wrapper {
  float: left;
  width: 100%;
  list-style: none;
  padding: 0;
  margin: 0; }

.notes-list-item {
  display: block;
  width: 100%;
  position: relative;
  padding: 16px; }

.notes-list-item:nth-child(odd) {
  background-color: #F8F8F8; }

.notes-list-item-indicator {
  position: absolute;
  top: 50%;
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: rgba(68, 68, 68, 0.54);
  border: 1px solid rgba(68, 68, 68, 0.54);
  border-radius: 100%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%); }

.notes-created-by-user {
  display: inline-block;
  width: 95%;
  padding-bottom: 16px;
  margin-left: 24px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.24); }

.note-of-user {
  font-family: Work Sans;
  font-style: normal;
  font-weight: normal;
  line-height: 15px;
  font-size: 12px;
  color: #000000;
  margin: 0; }

.created-note-by-user {
  background-color: rgba(251, 243, 173, 0.74); }

.highlight-by-user {
  background-color: rgba(33, 150, 83, 0.24); }

.comment-by-user {
  width: 100%;
  font-family: Montserrat;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 12px;
  color: rgba(68, 68, 68, 0.84);
  background-color: #FFFFFF;
  border: 0.5px solid rgba(68, 68, 68, 0.24);
  box-sizing: border-box;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  padding: 16px;
  margin: 0;
  margin-top: 8px; }

.no-notes-created {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 300;
  line-height: 19px;
  font-size: 14px;
  text-align: center;
  color: rgba(68, 68, 68, 0.84);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  right: 0; }

.questions-container-wrapper {
  margin-left: 0;
  margin-right: 0; }

.read-question-answer-wrapper {
  padding-left: 24px;
  padding-right: 24px; }

.read-question-answer {
  float: left;
  width: 100%;
  padding: 0;
  margin: 0;
  margin-left: 24px; }
  .read-question-answer li {
    float: left;
    font-style: normal;
    font-weight: bold;
    line-height: 18px;
    font-size: 16px;
    padding: 16px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.14); }
    .read-question-answer li p {
      margin: 0; }
    .read-question-answer li .read-question {
      margin-bottom: 16px; }
    .read-question-answer li .read-answer {
      font-style: normal;
      font-weight: normal;
      line-height: 18px;
      font-size: 16px; }
      .read-question-answer li .read-answer .sage-read-more {
        color: #1F419B;
        font-style: normal;
        font-weight: normal;
        line-height: 18px;
        font-size: 16px;
        text-decoration: underline;
        position: absolute;
        bottom: -3px;
        right: 0; }
        .read-question-answer li .read-answer .sage-read-more:hover {
          color: #1F419B; }
        .read-question-answer li .read-answer .sage-read-more:active {
          color: #1F419B; }
        .read-question-answer li .read-answer .sage-read-more:focus {
          color: #1F419B; }
    .read-question-answer li .answer-collapsed {
      max-height: 50px;
      overflow: hidden;
      -webkit-transition: all .2s ease;
      -moz-transition: all .2s ease;
      -ms-transition: all .2s ease;
      -o-transition: all .2s ease;
      transition: all .2s ease; }

.study-set-wrapper-continer {
  margin-top: 40px;
  margin-bottom: 40px;
  counter-reset: study-set-counter; }
  .study-set-wrapper-continer .pr-back-btn {
    position: absolute;
    top: 1rem;
    left: 4rem; }

.study-set-main {
  background-color: #f8f8f8;
  padding: 0;
  margin-bottom: 32px;
  counter-increment: study-set-counter; }
  .study-set-main:hover .term-counter:before {
    content: '\f1f8';
    font-family: Font Awesome\ 5 Free;
    color: #F05A2A;
    opacity: 1; }

.study-set-main:first-child:hover .term-counter:before {
  color: #000;
  font-family: "Montserrat", sans-serif;
  content: counter(study-set-counter); }

.study-set-item {
  padding: 16px 24px 16px 48px; }
  .study-set-item .form-group {
    margin-bottom: 0; }

.study-set-textarea {
  font-size: 16px;
  width: 100%;
  resize: none;
  background-color: #f8f8f8;
  border: 0;
  border-bottom: 2px solid #455358;
  outline: none;
  overflow: hidden;
  -webkit-transition: border-bottom .2s ease-in-out;
  -moz-transition: border-bottom .2s ease-in-out;
  -ms-transition: border-bottom .2s ease-in-out;
  -o-transition: border-bottom .2s ease-in-out;
  transition: border-bottom .2s ease-in-out; }
  .study-set-textarea:focus {
    border-bottom: 2px solid #F05A2A; }

.term-counter:before {
  content: counter(study-set-counter);
  display: block;
  height: 2rem;
  line-height: 2rem !important;
  text-align: center;
  position: absolute;
  bottom: auto;
  left: 0;
  right: auto;
  top: 50%;
  transform: translate(0, -50%);
  font-weight: 700;
  font-size: 20px;
  line-height: 1.2;
  width: 3.375rem;
  opacity: 0.5;
  cursor: pointer;
  z-index: 2; }

.add-study-card-btn-wrapper {
  text-align: center;
  background-color: #FFFFFF;
  display: flex;
  justify-content: center; }

.cancel-study-card-btn {
  display: block;
  color: #F05A2A;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-transform: uppercase;
  padding: 16px 16px 24px 16px;
  -webkit-transition: color .2ease-in-out;
  -moz-transition: color .2ease-in-out;
  -ms-transition: color .2ease-in-out;
  -o-transition: color .2ease-in-out;
  transition: color .2ease-in-out; }
  .cancel-study-card-btn:hover {
    color: #F05A2A;
    text-decoration: none; }
  .cancel-study-card-btn:focus {
    color: #F05A2A;
    text-decoration: none; }
  .cancel-study-card-btn:active {
    color: #F05A2A;
    text-decoration: none; }
  .cancel-study-card-btn > span {
    padding-bottom: 8px; }

.add-study-card-btn {
  display: block;
  color: #2EBAC6;
  font-size: 16px;
  font-weight: 600;
  text-decoration: none;
  text-transform: uppercase;
  padding: 16px 16px 24px 16px;
  -webkit-transition: color .2ease-in-out;
  -moz-transition: color .2ease-in-out;
  -ms-transition: color .2ease-in-out;
  -o-transition: color .2ease-in-out;
  transition: color .2ease-in-out; }
  .add-study-card-btn:hover {
    color: #F05A2A;
    text-decoration: none; }
    .add-study-card-btn:hover span {
      border-bottom: 2px solid #F05A2A; }
  .add-study-card-btn:focus {
    color: #F05A2A;
    text-decoration: none; }
    .add-study-card-btn:focus span {
      border-bottom: 2px solid #F05A2A; }
  .add-study-card-btn:active {
    color: #F05A2A;
    text-decoration: none; }
    .add-study-card-btn:active span {
      border-bottom: 2px solid #F05A2A; }
  .add-study-card-btn > span {
    border-bottom: 2px solid #2EBAC6;
    padding-bottom: 8px; }

.remove-tras {
  content: "\f1f8"; }

.question-row-bordered {
  padding: 16px 16px 0 16px; }

.question-row-bordered-right {
  background: rgba(143, 242, 109, 0.1);
  box-shadow: inset 0px 4px 0px #46B520; }

.question-row-bordered-wrong {
  background: rgba(207, 102, 95, 0.1);
  box-shadow: inset 0px 4px 0px #B72319; }

.full-wdth-label {
  width: 100%; }

.question-div {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.54); }

.export-study-set {
  display: inline-block;
  float: right;
  padding: 11px 11px; }

.close-notes-wrapper {
  display: none;
  width: 24px;
  height: 24px;
  background-color: #FFFFFF;
  position: fixed;
  top: 160px;
  right: 504px;
  border-radius: 100%;
  box-shadow: -4px 5px 4px rgba(0, 0, 0, 0.25); }
  .close-notes-wrapper > a:hover {
    color: #000000; }
  .close-notes-wrapper > a:active {
    color: #000000; }
  .close-notes-wrapper > a:focus {
    color: #000000; }
  .close-notes-wrapper > a > i {
    color: rgba(68, 68, 68, 0.74);
    font-size: 16px;
    margin-top: 5px;
    margin-left: 3px; }

.individual-note-selection {
  width: 22px;
  height: 22px;
  position: absolute;
  top: 50%;
  display: inline-block;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%); }
  .individual-note-selection input[type="checkbox"] {
    display: none; }
  .individual-note-selection label {
    cursor: pointer;
    font-size: 18px;
    color: #888; }
    .individual-note-selection label:hover {
      color: #F05A2A; }
  .individual-note-selection label.active {
    font-weight: bold;
    color: #F05A2A; }

.checkmark {
  position: absolute;
  top: 0;
  right: 8px;
  height: 18px;
  width: 18px;
  background-color: transparent;
  border: 2px solid #888;
  border-radius: 4px; }

.individual-note-selection:hover input[type="checkbox"] ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A; }

.individual-note-selection input[type="checkbox"]:checked ~ .checkmark {
  background-color: #F05A2A;
  border: 2px solid #F05A2A; }

.individual-note-selection::after {
  content: '';
  position: absolute;
  display: none; }

.individual-note-selection input[type="checkbox"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block; }

.checkmark::after {
  left: 1px;
  top: 2px;
  width: 12px;
  height: 7px;
  border: solid #fff;
  border-width: 2px 2px 0 0;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg); }

.checkmark-radio {
  position: absolute;
  top: 20px;
  right: 40px;
  height: 22px;
  width: 22px;
  background-color: transparent;
  border: 2px solid #888;
  border-radius: 50px !important; }

.individual-note-selection:hover input[type="radio"] ~ .checkmark {
  background-color: transparent;
  border-radius: 50px;
  border: 2px solid #F05A2A; }

.individual-note-selection input[type="radio"]:checked ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
  border-radius: 50px; }

.individual-note-selection::after {
  content: '';
  position: absolute;
  display: none; }

.individual-note-selection input[type="radio"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block; }

.checkmark-radio::after {
  left: 4px;
  top: 4px;
  width: 5px;
  height: 5px;
  border: 5px solid #F05A2A;
  border-radius: 5px;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg); }

.export-btn-wrapper {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #FFFFFF;
  text-align: center;
  padding: 8px;
  box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24); }

.export-notes-btn {
  display: block;
  width: 142px;
  color: #FFFFFF;
  text-align: center;
  background: linear-gradient(270deg, #2F80ED 0%, #2D9CDB 100%);
  padding: 11px;
  margin: 0 auto;
  border-radius: 4px; }
  .export-notes-btn:hover {
    color: #FFFFFF;
    text-decoration: none; }
  .export-notes-btn:active {
    color: #FFFFFF;
    text-decoration: none; }
  .export-notes-btn:focus {
    color: #FFFFFF;
    text-decoration: none; }

.cancel-export-study-set {
  width: 162px;
  background: linear-gradient(45deg, #97160D 20.42%, #F76E64 100%) !important;
  border-radius: 4px; }

.notes-wrapper-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.24);
  z-index: 1; }

.add-card-btn-wrapper-save {
  position: fixed;
  width: 85%;
  right: 0;
  bottom: 0;
  background-color: #FFFFFF;
  padding: 8px;
  box-shadow: 0px -1px 0px rgba(68, 68, 68, 0.24);
  z-index: 99; }
  .add-card-btn-wrapper-save > .add-study-card-btn {
    width: 142px;
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    float: right;
    color: #fff;
    text-decoration: none !important;
    padding: 11px;
    border-radius: 4px;
    border: 0; }
    .add-card-btn-wrapper-save > .add-study-card-btn span {
      border: 0; }
    .add-card-btn-wrapper-save > .add-study-card-btn:hover {
      color: #fff;
      text-decoration: none !important;
      border: 0; }
      .add-card-btn-wrapper-save > .add-study-card-btn:hover span {
        border: 0; }
    .add-card-btn-wrapper-save > .add-study-card-btn:focus {
      color: #fff;
      text-decoration: none !important;
      border: 0; }
    .add-card-btn-wrapper-save > .add-study-card-btn:active {
      color: #fff;
      text-decoration: none !important;
      border: 0; }

.chapternav-wrapper {
  position: relative;
  height: 100%;
  text-align: center;
  z-index: 1; }

.chapternav-items {
  list-style: none;
  padding: 0 !important;
  margin: 0;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 50px;
  white-space: nowrap; }

.chapternav-item {
  display: inline-block;
  vertical-align: top;
  margin: 0 -.11765em;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  padding: 16px;
  margin: 16px; }

.chapternav-link {
  color: #444444;
  display: block;
  margin-top: 3px;
  padding: 0;
  position: relative;
  z-index: 1;
  text-decoration: none; }

.chapternav-link:hover {
  color: #444444;
  text-decoration: none; }

.chapternav-icon {
  width: 28px;
  margin: 8px 16px;
  -webkit-margin-start: 16px;
  -webkit-margin-end: 16px;
  -moz-margin-start: 16px;
  -moz-margin-end: 16px; }

.chapternav-label {
  font-size: 14px;
  line-height: 1.09091;
  font-weight: 400;
  letter-spacing: .005em;
  display: block;
  margin: 0; }

.user-analytic-data-colored {
  text-align: center;
  padding-left: 0;
  padding-right: 0;
  margin-bottom: 16px; }

.recommend-link {
  color: #2F80ED;
  font-weight: 500;
  line-height: 24px;
  font-size: 14px;
  letter-spacing: 0.08em;
  text-decoration-line: underline;
  text-transform: uppercase;
  font-variant: small-caps;
  text-decoration: none; }
  .recommend-link:hover {
    text-decoration: none;
    color: #2F80ED; }
  .recommend-link:focus {
    text-decoration: none;
    color: #2F80ED; }
  .recommend-link:active {
    text-decoration: none;
    color: #2F80ED; }

.prepare-more {
  font-weight: 500;
  line-height: 24px;
  font-size: 14px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  font-variant: small-caps;
  color: #B72319; }

.good-enough {
  font-weight: 500;
  line-height: 24px;
  font-size: 14px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  font-variant: small-caps;
  color: #F2C94C; }

.you-are-awesome {
  font-weight: 500;
  line-height: 24px;
  font-size: 14px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  font-variant: small-caps;
  color: #46B520; }

.green-top-border {
  border-left: 5px solid #46B520; }

.yellow-top-border {
  border-top: 5px solid #F2C94C; }

.red-top-border {
  border-left: 5px solid #B72319; }

.sage-read-more {
  color: #1F419B;
  font-style: normal;
  font-weight: normal;
  line-height: 18px;
  font-size: 14px;
  text-decoration: underline;
  position: absolute;
  bottom: 0;
  right: 0;
  text-align: right;
  text-decoration: none;
  margin-bottom: 0; }
  .sage-read-more:hover {
    color: #1F419B;
    text-decoration: none; }
  .sage-read-more:active {
    color: #1F419B;
    text-decoration: none; }
  .sage-read-more:focus {
    color: #1F419B;
    text-decoration: none; }

.check-answer-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  width: 85%;
  background-color: #FFFFFF;
  box-shadow: 0 -1px 0 rgba(68, 68, 68, 0.24);
  padding: 8px 40px;
  z-index: 999; }

.btn-practice-question-answer {
  float: right;
  width: 142px;
  color: #FFFFFF;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 12px;
  text-align: center;
  letter-spacing: 0.01em;
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  border-radius: 4px; }

#content-data-no-notes, #content-data-studyset-nosets, #content-data-videos, #content-data-url, #withnovideos, #withnoweblinks {
  min-height: calc(100vh - 340px);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%; }

#upload-notesPr {
  display: none; }

#addNotes {
  margin-top: 4rem; }

.custom-prBtn, .custom-prBtn-bl {
  background: #FFFFFF;
  border: 1px solid #EDEDED;
  box-sizing: border-box;
  border-radius: 4px;
  color: #F79420; }

.custom-prBtn-bl {
  color: #2F80ED; }

#cke_1_contents {
  min-height: 300px !important; }

.add-notesPr {
  position: absolute;
  top: 10px;
  right: 50px;
  z-index: 99;
  width: 200px; }
  .add-notesPr:hover .drop-pr {
    display: block; }
  .add-notesPr .drop-pr {
    display: none;
    padding: 0;
    position: relative;
    left: -50px; }
    .add-notesPr .drop-pr li {
      list-style-type: none;
      justify-content: flex-end;
      display: flex;
      align-items: center;
      margin-top: 1rem;
      position: relative;
      z-index: 999; }
      .add-notesPr .drop-pr li.upload-notesPr {
        display: none; }
      .add-notesPr .drop-pr li a {
        display: flex;
        outline: 0;
        text-decoration: none;
        font-size: 14px; }
        .add-notesPr .drop-pr li a i {
          background: #2F80ED;
          box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.24), 0px 0px 6px rgba(0, 0, 0, 0.24);
          width: 40px;
          height: 40px;
          border-radius: 50px;
          text-align: center;
          align-items: center;
          justify-content: center;
          display: flex;
          color: #fff; }
        .add-notesPr .drop-pr li a:hover {
          text-decoration: none; }
      .add-notesPr .drop-pr li:first-child a:before {
        content: 'Add New Notes';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
      .add-notesPr .drop-pr li:nth-child(2) a:before {
        content: 'Upload Notes';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
  .add-notesPr > a {
    width: 127px;
    height: 44px;
    background: #F79420;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 600px;
    color: #fff; }
    .add-notesPr > a:before {
      content: '+';
      color: #fff;
      position: relative;
      left: -5px;
      font-size: 16px; }

#content-data-userNotes, #content-data-studyset, #videoDiv, #additional-refs, #content-data-url {
  margin-top: 7rem; }

.share {
  background: url("../../../images/share.svg") center center no-repeat;
  background-size: contain;
  width: 18px;
  height: 24px;
  position: absolute;
  top: 5px;
  right: 5px; }

#videoDiv {
  padding-bottom: 6rem; }

.video-btn, .link-btn {
  position: absolute;
  top: 10px;
  right: 50px; }
  .video-btn > a, .link-btn > a {
    height: 44px;
    background: #F79420;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 600px;
    color: #fff;
    width: 160px; }
    .video-btn > a:before, .link-btn > a:before {
      content: '+';
      color: #fff;
      position: relative;
      left: -5px;
      font-size: 16px; }

.upload-url .modal-body input {
  margin: 0 auto; }

.video-url .modal, .web-url .modal, .upload-url .modal {
  margin: 0 auto;
  position: absolute;
  left: 0;
  right: 0;
  top: 45%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%); }
.video-url .modal-content, .web-url .modal-content, .upload-url .modal-content {
  box-shadow: 0px 19px 38px rgba(0, 0, 0, 0.3), 0px 15px 12px rgba(0, 0, 0, 0.22);
  width: 328px;
  min-height: 204px; }
.video-url .modal-header, .web-url .modal-header, .upload-url .modal-header {
  border-bottom: none; }
.video-url .modal-footer, .web-url .modal-footer, .upload-url .modal-footer {
  border-top: none; }
  .video-url .modal-footer button, .web-url .modal-footer button, .upload-url .modal-footer button {
    border: none; }
    .video-url .modal-footer button:last-child, .web-url .modal-footer button:last-child, .upload-url .modal-footer button:last-child {
      color: #F79420; }
.video-url .modal-body form, .web-url .modal-body form, .upload-url .modal-body form {
  margin-top: 2rem; }
.video-url .modal-body input, .web-url .modal-body input, .upload-url .modal-body input {
  border: none;
  border-bottom: 1px solid rgba(68, 68, 68, 0.72);
  width: 280px;
  outline: 0;
  padding: 10px; }
  .video-url .modal-body input:first-child, .web-url .modal-body input:first-child, .upload-url .modal-body input:first-child {
    margin-bottom: 2rem; }

.pr-close-btn {
  position: absolute;
  top: 6px;
  right: -50px;
  border-left: 2px solid #ddd;
  line-height: 2; }
  .pr-close-btn li {
    list-style-type: none;
    width: 10px;
    height: 24px;
    position: relative;
    padding-left: 10px; }
    .pr-close-btn li a {
      background: url("../../../images/close.svg") center center no-repeat;
      width: 14px;
      height: 24px;
      background-size: contain;
      display: block; }

.video-btn {
  width: 200px; }
  .video-btn .drop-pr {
    display: none;
    padding: 0;
    position: relative;
    left: -30px; }
    .video-btn .drop-pr li {
      list-style-type: none;
      justify-content: flex-end;
      display: flex;
      align-items: center;
      margin-top: 1rem;
      position: relative;
      z-index: 999; }
      .video-btn .drop-pr li a {
        display: flex;
        outline: 0;
        text-decoration: none;
        font-size: 14px; }
        .video-btn .drop-pr li a i {
          background: #2F80ED;
          box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.24), 0px 0px 6px rgba(0, 0, 0, 0.24);
          width: 40px;
          height: 40px;
          border-radius: 50px;
          text-align: center;
          align-items: center;
          justify-content: center;
          display: flex;
          color: #fff; }
        .video-btn .drop-pr li a:hover {
          text-decoration: none; }
      .video-btn .drop-pr li:first-child a:before {
        content: 'Add New Videos';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
      .video-btn .drop-pr li:nth-child(2) a:before {
        content: 'Add related Videos';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
      .video-btn .drop-pr li:last-child a:before {
        content: 'See related Videos';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
  .video-btn:hover .drop-pr {
    display: block;
    transition: all 0.5s; }

.link-btn {
  width: 280px;
  text-align: center; }
  .link-btn .drop-pr {
    display: none;
    padding: 0;
    position: relative;
    left: -95px; }
    .link-btn .drop-pr li {
      list-style-type: none;
      justify-content: flex-end;
      display: flex;
      align-items: center;
      margin-top: 1rem;
      position: relative;
      z-index: 999; }
      .link-btn .drop-pr li a {
        display: flex;
        outline: 0;
        text-decoration: none;
        font-size: 14px; }
        .link-btn .drop-pr li a i {
          background: #2F80ED;
          box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.24), 0px 0px 6px rgba(0, 0, 0, 0.24);
          width: 40px;
          height: 40px;
          border-radius: 50px;
          text-align: center;
          align-items: center;
          justify-content: center;
          display: flex;
          color: #fff; }
        .link-btn .drop-pr li a:hover {
          text-decoration: none; }
      .link-btn .drop-pr li:first-child a:before {
        content: 'Add Ref. link';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
      .link-btn .drop-pr li:nth-child(2) a:before {
        content: 'Search related refs.';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
      .link-btn .drop-pr li:nth-child(3) a:before {
        content: 'Search related worksheets.';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
      .link-btn .drop-pr li:last-child a:before {
        content: 'Search Previous year qustions.';
        position: relative;
        z-index: 999;
        background: rgba(68, 68, 68, 0.72);
        border-radius: 4px;
        height: 33px;
        padding: 8px 10px;
        color: #fff;
        left: -20px; }
  .link-btn:hover .drop-pr {
    display: block;
    transition: all 0.5s; }

.additional-ref-section {
  padding-bottom: 6rem; }

#searchVideos {
  margin-top: 10rem; }
  #searchVideos h3 {
    text-align: center;
    font-size: 22px;
    font-family: 'Rubik', sans-serif; }
  #searchVideos .video-wrapper {
    width: 640px;
    min-height: 200px;
    display: flex;
    align-items: center; }
  #searchVideos .video-name {
    font-size: 20px;
    font-family: 'Rubik', sans-serif; }

.test-pr {
  display: flex;
  justify-content: center; }

.pr-flex {
  display: flex;
  align-items: center;
  justify-content: space-between; }

.def-imgPr {
  width: 60px;
  height: 60px; }

.sm-playImg {
  width: 20px;
  height: 20px; }

.play-btn-wrapper span {
  margin-left: 1rem;
  color: #4a4a4a;
  font-size: 14px; }
  .play-btn-wrapper span:hover {
    color: #F05A2A; }

.overlay-pr {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100%;
  width: 100%;
  transition: .5s ease;
  background-color: rgba(0, 0, 0, 0.5); }

.addBtn-pr {
  width: 16px;
  height: 16px;
  margin-right: 5px; }

.videoTOadd {
  font-weight: 500; }
  .videoTOadd:hover {
    color: #F05A2A; }

.dpr-none {
  display: none;
  font-size: 14px;
  font-weight: 500;
  color: #4a4a4a; }

.pr-back-btn {
  background: #fff;
  width: 100px;
  height: 40px;
  border-radius: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 5rem;
  z-index: 99; }
  .pr-back-btn i {
    font-size: 24px;
    margin-right: 10px;
    color: #000; }
  .pr-back-btn:hover {
    background: rgba(207, 202, 197, 0.6);
    text-decoration: none;
    color: #F05A2A; }
    .pr-back-btn:hover i {
      color: #F05A2A; }

.dflexPr {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  width: 100%; }

.pr-none {
  display: none !important; }

#displayNotes {
  margin: 0 auto; }

.preview-book-btns {
  z-index: 9; }

#qainwebref .pr-back-btn {
  top: 1rem; }
#qainwebref .read-question-answer {
  margin-top: 6rem; }
  #qainwebref .read-question-answer .read-question-answer-wrapper {
    display: flex;
    justify-content: center; }

#additional .link-btn {
  width: 340px; }

.sage-body .pr-close-btn {
  display: none; }

.flip-container {
  background: #FFFFFF;
  -webkit-perspective: 1000;
  -moz-perspective: 1000;
  -o-perspective: 1000;
  perspective: 1000; }

.flip .flipper {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg); }

.flip-container, .front, .back {
  width: 432px;
  min-height: 216px; }

.flipper {
  min-height: 216px;
  background: transparent;
  box-sizing: border-box;
  border-radius: 4px;
  -webkit-transition: 0.6s;
  -webkit-transform-style: preserve-3d;
  -moz-transition: 0.6s;
  -moz-transform-style: preserve-3d;
  -o-transition: 0.6s;
  -o-transform-style: preserve-3d;
  transition: 0.6s;
  transform-style: preserve-3d;
  position: relative; }

.front, .back {
  -webkit-backface-visibility: hidden;
  -moz-backface-visibility: hidden;
  -o-backface-visibility: hidden;
  backface-visibility: hidden;
  top: 0;
  left: 0;
  position: relative;
  text-align: center;
  border: 0.5px solid rgba(189, 189, 189, 0.55);
  border-radius: 4px;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  height: 216px;
  overflow: hidden; }

.front {
  position: absolute;
  z-index: 2; }
  .front span {
    display: flex; }

.back {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
  position: absolute; }

.front .name {
  font-size: 16px;
  text-shadow: none;
  color: #000;
  padding: 0 16px;
  display: flex;
  align-items: center;
  height: 100%;
  overflow: auto;
  justify-content: center; }

.back-logo {
  position: absolute;
  top: 40px;
  left: 90px;
  width: 160px;
  height: 117px; }

.back-title {
  color: #000;
  text-align: center;
  font-size: 16px;
  padding: 0 16px;
  display: flex;
  justify-content: center;
  height: 100%;
  overflow: auto;
  padding-top: 40px;
  padding-bottom: 20px; }

.flip-card-btn {
  width: 112px;
  position: absolute;
  left: 0;
  right: 0;
  display: block;
  margin: 0 auto;
  font-size: 16px;
  bottom: -16px;
  text-align: center;
  color: #FFFFFF;
  background: #1F419B;
  padding: 8px 0;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 63px;
  z-index: 5; }
  .flip-card-btn:hover {
    background: #1F419B;
    color: #FFFFFF;
    text-decoration: none; }
  .flip-card-btn:active {
    background: #1F419B;
    color: #FFFFFF;
    text-decoration: none; }
  .flip-card-btn:focus {
    background: #1F419B;
    color: #FFFFFF;
    text-decoration: none; }
  .flip-card-btn > i {
    font-size: 16px;
    vertical-align: top; }

.btn-flip {
  transform: rotateY(180deg); }

.back p {
  position: absolute;
  bottom: 40px;
  left: 0;
  right: 0;
  text-align: center;
  padding: 0 20px;
  font-family: arial;
  line-height: 2em; }

.flash-card-slider {
  position: relative;
  max-width: 440px;
  margin: 0 auto; }
  .flash-card-slider .carousel-inner {
    max-width: 440px;
    margin: 40px auto 0; }
  .flash-card-slider .item {
    min-height: 316px;
    padding: 24px; }
  .flash-card-slider .carousel-control {
    top: auto;
    background: #1F419B;
    opacity: 1;
    width: 24px;
    height: 24px;
    border-radius: 100%; }
  .flash-card-slider .carousel-btn-disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.65; }
  .flash-card-slider .carousel-control-wrapper {
    width: 208px;
    margin: 0 auto;
    float: none; }
  .flash-card-slider .card-number {
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    color: rgba(68, 68, 68, 0.74); }

.question-container {
  position: relative;
  float: left;
  max-width: 666px; }
  .question-container:before {
    content: '';
    clear: both; }

.mcq-question-div {
  font-family: "Work Sans", sans-serif; }
  .mcq-question-div input[type=radio] {
    position: absolute;
    opacity: 0; }
  .mcq-question-div input[type=checkbox] {
    position: absolute;
    opacity: 0; }
  .mcq-question-div .checkmark {
    position: absolute;
    top: 20px;
    right: 40px;
    height: 22px;
    width: 22px;
    background-color: transparent;
    border: 2px solid #888; }
  .mcq-question-div .checkmark::after {
    left: 3px;
    top: 4px;
    width: 12px;
    height: 7px;
    border: solid #F05A2A;
    border-width: 3px 3px 0 0;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg); }
  .mcq-question-div .checkmark-radio {
    position: absolute;
    top: 50%;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 50px !important;
    transform: translateY(-50%); }
  .mcq-question-div input[type="radio"] ~ .checkmark {
    color: #fff;
    font-size: 14px;
    text-align: center;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 50px; }
  .mcq-question-div input[type="radio"]:checked ~ .checkmark {
    background-color: transparent;
    border: 2px solid #F05A2A;
    border-radius: 50px; }
  .mcq-question-div input[type="checkbox"] ~ .checkmark {
    color: #fff;
    font-size: 14px;
    text-align: center;
    background-color: rgba(68, 68, 68, 0.74);
    border-radius: 0 !important; }
  .mcq-question-div .user-input::after {
    content: '';
    position: absolute;
    display: none; }
  .mcq-question-div input[type="radio"]:checked ~ .checkmark::after {
    content: '';
    position: absolute;
    display: block;
    z-index: -1; }
  .mcq-question-div .checkmark-checkbox::after {
    border-radius: 0; }
  .mcq-question-div .checkmark-radio::after {
    left: 0;
    top: 0;
    width: 5px;
    height: 5px;
    border: 10px solid #F05A2A;
    border-radius: 10px;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg); }
  .mcq-question-div input[type="checkbox"]:checked ~ .checkmark::after {
    content: '';
    position: absolute;
    display: block;
    z-index: -1; }
  .mcq-question-div .checkmark-checkbox::after {
    left: 0;
    top: 0;
    width: 5px;
    height: 5px;
    border: 10px solid #F05A2A;
    border-radius: 0;
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg); }

.question-number-help-wrapper {
  list-style: none;
  text-align: right;
  padding-right: 16px;
  padding-left: 16px; }
  .question-number-help-wrapper > li > a {
    cursor: pointer; }
    .question-number-help-wrapper > li > a:hover {
      color: #444444; }

.question-number-help-dropdown {
  width: 100%;
  left: auto;
  right: -15px;
  padding: 18px 18px 0 18px;
  border-top-right-radius: 4px !important;
  border-top-left-radius: 4px !important;
  background: #FFFFFF;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  overflow-wrap: break-word; }
  .question-number-help-dropdown:before {
    content: "";
    border-bottom: 10px solid #fff;
    border-right: 10px solid transparent;
    border-left: 10px solid transparent;
    position: absolute;
    top: -10px;
    right: 16px;
    z-index: 10; }
  .question-number-help-dropdown:after {
    content: "";
    border-bottom: 12px solid #ccc;
    border-right: 12px solid transparent;
    border-left: 12px solid transparent;
    position: absolute;
    top: -12px;
    right: 14px;
    z-index: 9; }
  .question-number-help-dropdown .answered-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #2F80ED;
    border-radius: 8px; }
  .question-number-help-dropdown .skipped-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    border: 1px solid #444444;
    border-radius: 8px; }
  .question-number-help-dropdown .review-marked-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #A056E5;
    border-radius: 8px; }
  .question-number-help-dropdown .review-marked-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #A056E5;
    border-radius: 8px; }
  .question-number-help-dropdown .not-seen-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #888888;
    border-radius: 8px; }
  .question-number-help-dropdown .current-indicator {
    display: inline-block;
    width: 36px;
    height: 36px;
    background: #2EBAC6;
    border-radius: 8px; }

.question-number-help-dropdown-list-item {
  margin-bottom: 16px; }

.question-number-help-label {
  display: inline-block;
  vertical-align: top;
  padding: 8px 0 0 6px;
  font-size: 16px; }

.quiz-section {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: center;
  margin-right: 0;
  margin-left: 0;
  margin-top: 40px; }

.quiz-item-wrapper {
  position: relative;
  width: 192px;
  min-height: 204px;
  margin: 0 34px 40px;
  border-radius: 6px;
  background: #FFFFFF;
  padding: 48px 8px 8px 8px;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  -webkit-transition: background-color .4s ease, box-shadow .4s ease;
  -moz-transition: background-color .4s ease, box-shadow .4s ease;
  -ms-transition: background-color .4s ease, box-shadow .4s ease;
  -o-transition: background-color .4s ease, box-shadow .4s ease;
  transition: background-color .4s ease, box-shadow .4s ease; }
  .quiz-item-wrapper:hover {
    background-color: #FFFFFF;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25); }

.quiz-number {
  position: absolute;
  top: 0;
  left: 0;
  font-weight: 500;
  padding: 5px 9px;
  border: 1px solid #DBDBDB;
  border-left: 0;
  border-top: 0;
  border-bottom-right-radius: 4px; }

.quiz-item {
  text-align: center; }

.quiz-name {
  font-weight: 400;
  line-height: 19px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical; }

.quiz-buttons {
  position: absolute;
  bottom: 8px;
  width: calc(100% - 16px);
  text-align: center; }

.quiz-learn-btn {
  font-size: 12px;
  letter-spacing: 0.01em;
  color: #F05A2A;
  padding: 8px 40px;
  margin-bottom: 8px;
  border: 1px solid #DBDBDB;
  border-radius: 4px; }
  .quiz-learn-btn:hover {
    text-decoration: none;
    color: #F05A2A;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25); }
  .quiz-learn-btn:active {
    text-decoration: none;
    color: #F05A2A; }
  .quiz-learn-btn:focus {
    text-decoration: none;
    color: #F05A2A; }

.quiz-practice-btn {
  display: inline-block;
  font-weight: 500;
  letter-spacing: 0.01em;
  color: #FFFFFF;
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  padding: 10px 30px;
  border: none;
  border-radius: 4px; }
  .quiz-practice-btn:hover {
    text-decoration: none;
    color: #FFFFFF;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25); }
  .quiz-practice-btn:active {
    text-decoration: none;
    color: #FFFFFF; }
  .quiz-practice-btn:focus {
    text-decoration: none;
    color: #FFFFFF; }

.score-btn {
  display: inline-block;
  width: 35px;
  height: 40px;
  background: url("../images/wonderslate/scoreboard.svg");
  background-repeat: no-repeat;
  background-size: 90% 90%;
  background-position: center;
  background-color: #FFFFFF !important;
  margin-left: 8px;
  border: 1px solid rgba(68, 68, 68, 0.54); }
  .score-btn:hover {
    text-decoration: none;
    color: #FFFFFF;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25); }
  .score-btn:active {
    text-decoration: none;
    color: #FFFFFF; }
  .score-btn:focus {
    text-decoration: none;
    color: #FFFFFF; }

.plz-login {
  color: #F05A2A; }

.additional-ref-section {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: center;
  align-items: center;
  margin-right: 0;
  margin-left: 0;
  margin-top: 40px; }

.additional-ref-wrapper {
  width: 616px;
  margin: 0 34px 40px;
  border-radius: 6px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  -webkit-transition: background-color .4s ease, box-shadow .4s ease;
  -moz-transition: background-color .4s ease, box-shadow .4s ease;
  -ms-transition: background-color .4s ease, box-shadow .4s ease;
  -o-transition: background-color .4s ease, box-shadow .4s ease;
  transition: background-color .4s ease, box-shadow .4s ease; }

.additional-ref-img-wrapper {
  display: inline-block; }

.additional-ref-info {
  display: inline-block;
  vertical-align: top;
  padding: 16px; }

.additional-ref-name {
  font-weight: 500; }

.practice-question img {
  max-width: 100% !important; }

.correct-question-answer {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #46B520;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px; }

.wrong-question-answer {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #B72319;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px; }

.grey-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #888;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px; }
  .grey-question:hover {
    color: #FFFFFF;
    text-decoration: none; }

.skipped-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #000;
  background-color: #fff;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid #000;
  border-radius: 8px; }
  .skipped-question:hover {
    text-decoration: none; }

.review-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #A056E5;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px; }
  .review-question:hover {
    color: #FFFFFF;
    text-decoration: none; }

.answered-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #2F80ED;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px; }
  .answered-question:hover {
    color: #FFFFFF;
    text-decoration: none; }

.current-question {
  display: inline-block;
  width: 40px;
  height: 40px;
  text-align: center;
  color: #fff;
  font-weight: bold;
  background: #2EBAC6;
  padding: 10px;
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid #2EBAC6;
  border-radius: 8px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25); }
  .current-question:hover {
    color: #FFFFFF;
    text-decoration: none; }

.mcq-question-div {
  position: relative;
  overflow: auto; }
  .mcq-question-div::before {
    content: '';
    position: absolute;
    left: 0;
    height: 100%;
    border-left: 1px solid rgba(189, 189, 189, 0.55); }

.quiz-modal-body {
  width: 100%;
  min-height: 750px;
  max-height: 750px;
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  padding: 15px 15px 0;
  overflow: auto; }
  .quiz-modal-body .container-fluid {
    margin: 0 !important; }
  .quiz-modal-body .modal-footer {
    padding-left: 0; }
    .quiz-modal-body .modal-footer .previous-btn {
      padding-left: 0; }

.score-container {
  display: none;
  width: 100%;
  min-height: 525px;
  max-height: 525px;
  min-height: 750px;
  max-height: 750px;
  overflow: auto; }

.practice-score-container {
  min-height: 300px;
  color: #fff;
  text-align: center;
  background: linear-gradient(74.18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -webkit-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -o-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: linear-gradient(18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  padding: 26px 0; }
  .practice-score-container .practice-score {
    width: 274px;
    margin: 0 auto; }
    .practice-score-container .practice-score .medal-picture {
      width: 135px;
      height: 138px;
      margin: 0 auto; }
      .practice-score-container .practice-score .medal-picture img {
        width: 100%; }
    .practice-score-container .practice-score .practice-score-string p {
      color: #fff;
      font-size: 24px;
      font-weight: 600;
      margin: 0; }
    .practice-score-container .practice-score .practice-score-string .practice-score-string-score {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 8px; }

.answer-summary {
  min-height: 215px;
  padding: 24px 0;
  text-align: center;
  background: #F8F8F8; }
  .answer-summary p {
    margin: 0; }
  .answer-summary .summary-heading {
    font-size: 22px; }
  .answer-summary .short-heading {
    font-size: 16px; }
  .answer-summary .score-summary {
    max-width: 634px;
    margin: 0 auto;
    margin-top: 16px; }
    .answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
      width: 100px;
      height: 100px;
      color: #fff;
      font-size: 24px;
      font-weight: bold;
      background: linear-gradient(43.98deg, #2A7E0D 14.96%, #4DEB17 100%);
      padding: 34px 35px 36px 34px;
      margin: 0 auto;
      margin-top: 8px;
      border-radius: 16px; }
    .answer-summary .score-summary .wrong-answers {
      background: linear-gradient(45deg, #97160D 20.42%, #F76E64 100%); }
    .answer-summary .score-summary .skipped-answers {
      color: #444444;
      background: #fff;
      border: 2px solid #444444; }

.accuracy-summary {
  max-width: 634px;
  padding-bottom: 16px;
  margin: 0 auto;
  margin-top: 26px; }
  .accuracy-summary .time-taken, .accuracy-summary .answer-accuracy, .accuracy-summary .question-hr {
    display: inline-block;
    width: 56px;
    height: 56px;
    color: #fff;
    font-size: 24px;
    font-weight: bold;
    background: url("../images/booksmojo/clock-mobile.png");
    background-repeat: no-repeat;
    background-size: 100%;
    padding: 34px 0 36px 0;
    border-radius: 500px;
    margin: 0 auto;
    margin-top: 8px; }
  .accuracy-summary .answer-accuracy {
    background: url("../images/booksmojo/accuracy-mobile.png");
    background-repeat: no-repeat;
    background-size: 100%; }
  .accuracy-summary .question-hr {
    background: url("../images/wonderslate/question-hr.png");
    background-repeat: no-repeat;
    background-size: 100%; }
  .accuracy-summary .time-span {
    display: inline-block;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    vertical-align: top;
    margin-top: 16px;
    margin-left: 8px;
    padding-right: 30px;
    border-right: 1px solid rgba(68, 68, 68, 0.2); }
    .accuracy-summary .time-span .clearfix {
      font-size: 12px;
      color: rgba(68, 68, 68, 0.54); }

.questions-summary {
  max-width: 712px;
  padding: 16px 50px;
  margin: 0 auto; }
  .questions-summary .question-summary-questions .correct-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #46B520;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }
  .questions-summary .question-summary-questions .wrong-question {
    display: inline-block;
    width: 36px;
    height: 36px;
    text-align: center;
    color: #fff;
    background-color: #B72319;
    padding: 10px;
    margin-right: 20px;
    margin-bottom: 20px;
    border-radius: 8px; }
  .questions-summary .question-summary-help-icon {
    display: inline-block;
    width: 25px;
    height: 25px;
    background: url("../images/booksmojo/help-icon.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-indent: -9999999px; }
  .questions-summary .question-summary-help {
    position: relative;
    float: right; }
  .questions-summary .question-summary-tooltip {
    display: none;
    position: relative;
    right: 0;
    width: 232px;
    list-style: none;
    text-align: left;
    background: #fff;
    padding: 16px;
    margin: 0;
    margin-top: 30px;
    border-radius: 6px;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25); }
    .questions-summary .question-summary-tooltip li {
      display: block;
      padding: 10px; }
      .questions-summary .question-summary-tooltip li .indicator-div {
        display: inline-block;
        width: 40px;
        height: 40px; }
      .questions-summary .question-summary-tooltip li p {
        display: inline-block;
        margin-left: 10px;
        -webkit-transform: translateY(-55%);
        -moz-transform: translateY(-55%);
        -ms-transform: translateY(-55%);
        -o-transform: translateY(-55%);
        transform: translateY(-55%); }
      .questions-summary .question-summary-tooltip li .correct {
        background: url("../images/booksmojo/correct.png");
        background-repeat: no-repeat;
        background-size: 100% 100%; }
      .questions-summary .question-summary-tooltip li .wrong {
        background: url("../images/booksmojo/wrong.png");
        background-repeat: no-repeat;
        background-size: 100% 100%; }
      .questions-summary .question-summary-tooltip li .skipped {
        background: url("../images/booksmojo/skipped.png");
        background-repeat: no-repeat;
        background-size: 100% 100%; }

.btn-review {
  display: block;
  width: 173px;
  height: auto;
  color: #444444;
  font-size: 14px;
  background: #FFFFFF;
  padding: 8px;
  margin: 0 auto;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px; }
  .btn-review:hover {
    color: #444444 !important; }

.modal {
  overflow: scroll; }

.chapter-details-area {
  height: 100%; }

.answer-holder-inner {
  float: left;
  max-width: 100%; }

.correct-answer-learn {
  margin-top: 40px;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.74); }
  .correct-answer-learn .correct-answer-label {
    font-size: 20px;
    font-weight: bold;
    color: #888888; }
    .correct-answer-learn .correct-answer-label .correct-answer {
      color: #444444;
      font-size: 18px; }

.correct-answer-by-user {
  color: #46B520 !important;
  font-size: 18px; }

.wrong-answer-by-user {
  color: #B72319 !important;
  font-size: 18px; }

.mcq-learn {
  border-left: 0 !important; }
  .mcq-learn::before {
    content: '';
    border-left: 0 !important; }

.show-explanation {
  float: right; }

.correct-answer-explanation {
  display: none;
  background-color: #FCFCFC;
  padding: 24px 40px;
  float: left;
  box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25); }

.modal .close {
  font-size: 32px; }

#quizanalytics {
  z-index: 9999; }

#quizModal .modal-header, #quizanalytics .modal-header {
  min-height: 56px; }

@media screen and (min-width: 768px) and (max-width: 1536px) {
  .quiz-modal-body {
    min-height: 525px;
    max-height: 525px; }

  .score-container {
    min-height: 525px;
    max-height: 525px; } }
@media screen and (max-width: 768px) {
  .submit-quiz-btn {
    width: auto; }

  .mcq-question-div::before {
    content: '';
    border-left: 0; }

  .btn-review {
    width: 100%; }

  .close-modal.next-btn.pull-right {
    width: 100%; }

  .answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
    width: 80px;
    height: 80px;
    padding: 23px 35px 36px 34px; }
  .answer-summary .score-summary .wrong-answers {
    width: 80px;
    height: 80px;
    padding: 23px 35px 36px 34px; }
  .answer-summary .score-summary .skipped-answers {
    width: 80px;
    height: 80px;
    padding: 23px 35px 36px 34px; }

  .next-btn.close-modal {
    width: 100% !important; }

  .learn-practice {
    width: 47%;
    float: left; }
    .learn-practice .learn-practice-card {
      width: 100%;
      height: 200px; }
    .learn-practice .learn-pactice-btns {
      margin-top: 30px; }

  .quiz-modal-body {
    min-height: 500px; } }
.question-div-mcq img {
  height: auto;
  width: auto;
  margin-top: 15px; }

.mcqquestion img {
  height: 200px !important;
  width: 200px !important; }

#sum-question img {
  display: block;
  height: auto !important;
  width: auto !important;
  margin-top: 15px;
  margin-left: 15px;
  float: none; }

.exercise-score-container {
  min-height: 220px;
  color: #fff;
  text-align: center;
  background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  background: -webkit-linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  background: -o-linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  padding: 26px 0; }
  .exercise-score-container p {
    color: #fff;
    font-size: 18px; }
  .exercise-score-container .exercise-text {
    max-width: 85px;
    display: inline-block;
    margin: 0;
    line-height: normal; }
  .exercise-score-container .exercise-scores {
    position: relative;
    display: inline-block;
    width: 120px;
    height: 120px;
    text-align: center;
    font-size: 28px;
    font-weight: bold;
    color: #fff;
    background-color: transparent;
    margin-bottom: 20px;
    border: 2px solid #fff;
    border-radius: 16px; }
    .exercise-score-container .exercise-scores span {
      position: relative;
      top: 50%;
      display: block;
      transform: translateY(-50%); }

.total-question {
  height: 40px;
  padding: 12px;
  margin-bottom: 20px;
  box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25); }
  .total-question .total-question-num {
    font-size: 12px;
    color: rgba(68, 68, 68, 0.74); }
  .total-question .questions {
    font-size: 12px;
    font-weight: bold;
    color: #F05A2A; }

.take-test {
  width: 270px;
  text-align: center;
  float: none;
  margin: 0 auto;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  left: 0; }
  .take-test img {
    width: 50%;
    margin: 0 auto; }

.no-test-text {
  font-size: 14px;
  font-weight: 300;
  color: #888; }

.previous-btn {
  color: rgba(68, 68, 68, 0.74);
  border: 0; }
  .previous-btn:hover {
    color: rgba(68, 68, 68, 0.74); }
  .previous-btn:active {
    color: rgba(68, 68, 68, 0.74); }
  .previous-btn:focus {
    color: rgba(68, 68, 68, 0.74); }

.next-btn {
  width: 20%;
  height: auto;
  font-size: 14px;
  color: #fff;
  background: #028EDB;
  padding: 8px;
  border: 0; }
  .next-btn:hover {
    color: #fff; }
  .next-btn:active {
    color: #fff; }
  .next-btn:focus {
    color: #fff; }

.submit-quiz-btn {
  background: #FFFFFF;
  border: 0.5px solid rgba(68, 68, 68, 0.2);
  box-sizing: border-box;
  border-radius: 2px;
  width: 168px;
  color: #028EDB;
  margin: 0 auto;
  float: none; }
  .submit-quiz-btn:hover {
    color: #028EDB; }
  .submit-quiz-btn:active {
    color: #028EDB; }
  .submit-quiz-btn:focus {
    color: #028EDB; }

.done-btn-div {
  text-align: center;
  padding-left: 0; }

.next-btn.pull-right.close-modal {
  width: 30%; }

#quizModal {
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999; }

.analysis-summary {
  float: left;
  width: 100%;
  background: #fff;
  text-align: center;
  padding: 24px 0; }

.row-heading {
  font-size: 22px;
  font-weight: 500; }

.analysis-by-book-detail {
  padding-bottom: 24px;
  margin-top: 16px; }

.collapsed-detail-container {
  text-align: center; }

.collapsed-detail-wrapper {
  list-style-position: inside;
  max-width: 503px;
  text-align: left;
  padding: 0;
  padding-left: 16px;
  margin: 0 auto; }

.collapsed-detail-list-item-chapter {
  display: inline-block;
  font-weight: 300;
  font-size: 16px;
  margin: 0 0 4px; }

.collapsed-detail-list-item-chapter-score {
  color: rgba(68, 68, 68, 0.74);
  font-weight: bold;
  margin-left: 24px; }

.analysis-book-name {
  display: block;
  font-weight: 300;
  font-size: 18px;
  line-height: 26px;
  padding-bottom: 16px; }
  .analysis-book-name:hover {
    text-decoration: none;
    color: #444; }
  .analysis-book-name:active {
    text-decoration: none;
    color: #444; }
  .analysis-book-name:focus {
    text-decoration: none;
    color: #444; }
  .analysis-book-name i {
    display: inline-block;
    margin-left: 12px; }
    .analysis-book-name i.rotated-i {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
      -o-transform: rotate(180deg);
      transform: rotate(180deg); }
    .analysis-book-name i.simple {
      -webkit-transform: rotate(0deg) !important;
      -moz-transform: rotate(0deg) !important;
      -ms-transform: rotate(0deg) !important;
      -o-transform: rotate(0deg) !important;
      transform: rotate(0deg) !important; }

.badge-success, .badge-warning, .badge-danger {
  color: #fff;
  background: linear-gradient(0deg, #46B520, #46B520), #B72319;
  margin-left: 16px;
  border-radius: 8px; }

.badge-warning {
  background: linear-gradient(0deg, #F2C94C, #F2C94C), #B72319; }

.badge-danger {
  background: #B72319; }

.collapse-hr {
  width: 256px;
  margin: 0 auto 16px; }

.suggestions-for-user {
  float: left;
  width: 100%;
  text-align: center;
  background: #fff;
  margin: 24px 0; }

.div-separator {
  float: left;
  width: 100%;
  height: 24px;
  background: #F8F8F8; }

.suggested-topics {
  font-weight: 300;
  text-align: left;
  line-height: 26px;
  font-size: 16px;
  padding: 0 32px; }
  .suggested-topics .icon-chevron {
    display: inline-block;
    margin-left: 8px;
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    transform: rotate(270deg); }

.topic-link {
  color: #2EBAC6; }

.depth-details {
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 14px;
  color: #888;
  padding-right: 16px; }
  .depth-details:hover {
    text-decoration: none; }
  .depth-details .icon-chevron {
    display: inline-block;
    margin-left: 4px;
    -webkit-transform: rotate(270deg);
    -moz-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    transform: rotate(270deg); }

.table-div {
  background: #F8F8F8;
  padding: 16px;
  margin-top: 16px;
  margin-bottom: 16px;
  overflow: auto; }

.detailed-table {
  width: 100%;
  text-align: center; }
  .detailed-table tr, .detailed-table td {
    color: #444444;
    text-align: right;
    padding: 14px 0 16px 0; }
  .detailed-table tr {
    border-bottom: 1px solid rgba(68, 68, 68, 0.74); }
  .detailed-table th.table-chapters {
    text-align: left; }
  .detailed-table td.table-chapters-data {
    text-align: left; }
  .detailed-table th {
    font-weight: 500;
    color: rgba(68, 68, 68, 0.74);
    text-align: right;
    padding: 14px 0 16px 0; }
  .detailed-table .correct {
    color: #46B520; }
  .detailed-table .incorrect {
    color: #B72319; }

.expand-table-btn {
  font-size: 12px;
  background: #FFFFFF;
  border: 0.5px solid rgba(68, 68, 68, 0.54);
  padding: 6px;
  box-sizing: border-box;
  border-radius: 4px; }
  .expand-table-btn:hover {
    color: #444;
    text-decoration: none; }
  .expand-table-btn:active {
    color: #444;
    text-decoration: none; }
  .expand-table-btn:focus {
    color: #444;
    text-decoration: none; }

.demo-books {
  padding: 24px 0; }

.demo-book-wrapper {
  background: #fff;
  padding: 8px; }
  .demo-book-wrapper a {
    min-height: 320px;
    display: block;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
    padding: 8px; }
    .demo-book-wrapper a:hover {
      text-decoration: none; }

.demo-book-name {
  font-weight: 500;
  margin-top: 8px;
  margin-bottom: 0; }

.suggested-books {
  font-weight: 500;
  line-height: 26px;
  font-size: 18px;
  text-align: center; }

.question-help {
  position: absolute;
  right: 24px;
  font-size: 24px; }
  .question-help:hover {
    text-decoration: none;
    color: #444; }

.tooltipMenu {
  display: none;
  position: absolute;
  right: 24px;
  z-index: 1; }
  .tooltipMenu:hover {
    display: block; }

.bottomSide {
  top: 100%; }

.tooltipMenu::after {
  content: " ";
  position: absolute;
  border-width: 10px;
  border-style: solid; }

.tooltipMenu ul li:hover {
  background: #0d77b6;
  color: white;
  cursor: pointer;
  font-weight: bold; }

.bottomSide.tooltipMenu::after {
  bottom: 100%;
  left: 45%;
  border-color: transparent transparent white transparent; }

#questionumber-containter {
  max-height: 734px;
  min-height: 350px;
  overflow: auto; }

@media screen and (min-width: 768px) and (max-width: 1536px) {
  .test-gen-modal-body {
    width: 100%;
    min-height: 525px;
    max-height: 525px;
    overflow: auto;
    overflow-x: hidden; }

  .answer-summary {
    min-height: 215px; } }
@media screen and (min-width: 768px) {
  .demo-book-wrapper a {
    min-height: 400px; } }
.question-string {
  display: inline-block; }

.video-section {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: center;
  margin-right: 0;
  margin-left: 0;
  margin-top: 40px; }

.video-wrapper {
  width: 272px;
  margin: 0 34px 40px;
  border-radius: 6px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
  -webkit-transition: background-color .4s ease, box-shadow .4s ease;
  -moz-transition: background-color .4s ease, box-shadow .4s ease;
  -ms-transition: background-color .4s ease, box-shadow .4s ease;
  -o-transition: background-color .4s ease, box-shadow .4s ease;
  transition: background-color .4s ease, box-shadow .4s ease; }
  .video-wrapper a {
    text-decoration: none; }
  .video-wrapper:hover {
    background-color: #FFFFFF;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.25); }

.video-item {
  cursor: pointer; }

.video-img-wrapper {
  position: relative; }
  .video-img-wrapper .video-img {
    width: 272px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px; }
  .video-img-wrapper .play-btn-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); }

.video-info {
  font-weight: 500;
  color: #000000;
  padding: 8px; }

.video-name {
  font-weight: 500;
  color: #000000; }

.user-profile-orders .user-profile-tabs {
  font-family: "Abril Fatface", cursive;
  background-color: transparent;
  border: none; }
  .user-profile-orders .user-profile-tabs > li {
    margin-right: 24px; }
    .user-profile-orders .user-profile-tabs > li > a {
      position: relative;
      font-size: 32px;
      letter-spacing: 0.04em;
      color: rgba(68, 68, 68, 0.54);
      background-color: transparent;
      padding: 0;
      border: none; }
    .user-profile-orders .user-profile-tabs > li.active > a {
      color: #444444;
      background-color: transparent;
      border: none; }
      .user-profile-orders .user-profile-tabs > li.active > a:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        border-bottom: 4px solid #F05A2A; }

.user-profile-orders-tab-content {
  float: left; }

.user-profile-tab, .user-order-tab {
  float: left;
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 4px;
  padding: 40px; }

.user-profile-image {
  position: relative;
  float: left;
  width: 160px;
  height: 160px;
  margin-right: 40px;
  border: 4px solid #FFFFFF;
  border-radius: 100%;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25); }
  .user-profile-image img {
    width: 100%;
    max-height: 150px;
    border-radius: 100%;
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25); }
  .user-profile-image .upload-user-image {
    position: absolute;
    right: 0;
    bottom: 24px;
    color: rgba(68, 68, 68, 0.74);
    font-size: 16px;
    text-align: center;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.94);
    border-radius: 100%;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25); }

.user-profile-details {
  float: left;
  width: 79%;
  border: 1px solid rgba(68, 68, 68, 0.24);
  border-radius: 6px; }
  .user-profile-details .user-profile-input {
    display: block;
    width: 100%;
    font-weight: 500;
    padding: 11px 16px;
    margin: 0;
    border: 0;
    border-bottom: 1px solid rgba(68, 68, 68, 0.24); }
    .user-profile-details .user-profile-input:focus {
      box-shadow: none;
      outline: none; }
  .user-profile-details .user-profile-input:first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px; }
  .user-profile-details .user-profile-input:last-child {
    border-bottom: 0;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px; }

.user-profile-password {
  position: relative; }
  .user-profile-password .change-password-profile {
    position: absolute;
    top: 12px;
    right: 16px;
    font-size: 12px;
    font-weight: 500;
    color: #F05A2A; }
    .user-profile-password .change-password-profile:hover {
      text-decoration: none; }

.update-profile-area {
  float: left;
  clear: both;
  width: 100%;
  padding-right: 8px; }

.update-profile-btn {
  color: #FFFFFF;
  background: #F05A2A;
  padding: 11px 32px;
  margin-top: 24px;
  border: 0;
  border-radius: 4px; }
  .update-profile-btn:hover {
    text-decoration: none;
    color: #FFFFFF; }
  .update-profile-btn:active {
    text-decoration: none;
    color: #FFFFFF; }
  .update-profile-btn:focus {
    text-decoration: none;
    color: #FFFFFF; }

.user-order-tab {
  padding: 40px 40px 16px;
  margin-bottom: 56px; }

.users-orders {
  clear: both;
  float: left;
  width: 100%;
  margin-bottom: 24px; }

.purchase-date {
  color: rgba(68, 68, 68, 0.74); }

.users-orders-details-wrapper {
  clear: both;
  float: left;
  width: 100%;
  background: #FEFEFE;
  margin-bottom: 16px;
  border: 1px solid rgba(68, 68, 68, 0.24);
  border-radius: 4px; }

.users-orders-details {
  clear: both;
  float: left;
  width: 100%;
  padding: 16px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.users-orders-details:last-child {
  border-bottom: 0; }

.order-purchase-id {
  font-size: 16px;
  font-weight: 500;
  line-height: 21px; }

.order-book-image {
  float: left;
  width: 111px;
  margin-right: 16px; }
  .order-book-image img {
    border-radius: 4px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25); }

.order-book-detail {
  float: left;
  width: 65%;
  padding-right: 15px;
  padding-top: 19px; }
  .order-book-detail .preview-book-name {
    font-size: 16px; }

.user-purchase-order-detail {
  float: left;
  width: 65%; }

.order-payment-details {
  width: 35%;
  float: left;
  padding: 16px 0 0 16px;
  overflow-wrap: break-word;
  border-left: 1px solid rgba(68, 68, 68, 0.2); }

.order-payment-id-label, .order-payment-label {
  color: rgba(68, 68, 68, 0.64);
  font-size: 12px;
  margin: 0;
  clear: both; }

.order-payment-id {
  font-size: 16px; }

.order-review-section {
  float: left;
  width: 100%;
  padding-bottom: 16px;
  margin-bottom: 32px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

.order-book-review {
  color: #F05A2A;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.01em; }
  .order-book-review:hover {
    text-decoration: none;
    color: #F05A2A; }

.order-book-help {
  color: rgba(68, 68, 68, 0.74);
  font-size: 12px;
  font-weight: 300;
  letter-spacing: 0.01em; }
  .order-book-help:hover {
    text-decoration: none;
    color: rgba(68, 68, 68, 0.74); }

.change-password-modal {
  text-align: center;
  padding: 0 !important; }
  .change-password-modal .modal-content {
    min-height: 300px;
    padding: 32px; }
  .change-password-modal .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle; }
  .change-password-modal:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px; }

.change-password-modal-header {
  font-family: "Abril Fatface", cursive;
  font-size: 22px;
  letter-spacing: 0.04em;
  margin-bottom: 24px; }

.change-password-input-fields {
  background: #FFFFFF;
  border: 1px solid rgba(68, 68, 68, 0.24);
  border-radius: 6px; }

.password-reset-input {
  display: block;
  width: 100%;
  font-weight: 500;
  padding: 11px 16px;
  margin: 0;
  border: 0;
  border-bottom: 1px solid rgba(68, 68, 68, 0.24); }
  .password-reset-input:focus {
    box-shadow: none;
    outline: none; }

.password-reset-input:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px; }

.password-reset-input:last-child {
  border-bottom: 0;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px; }

.change-password-modal-footer {
  padding: 0;
  margin-top: 24px;
  border-top: 0; }
  .change-password-modal-footer .cncl-btn {
    background: #FFFFFF;
    color: #F05A2A;
    padding: 11px 32px;
    font-weight: 500; }
  .change-password-modal-footer .save-btn {
    background: #F05A2A;
    color: #FFFFFF;
    font-weight: 500;
    padding: 11px 32px;
    border: 0; }

.book-review-modal {
  text-align: center;
  padding: 0 !important; }
  .book-review-modal .modal-content {
    padding: 32px; }
  .book-review-modal .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle; }
  .book-review-modal:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -4px; }

.book-review-modal-header {
  font-family: "Abril Fatface", cursive;
  font-size: 22px;
  letter-spacing: 0.04em;
  margin-bottom: 16px; }

.book-review-input-fields {
  clear: both;
  background: #FFFFFF;
  border: 1px solid rgba(68, 68, 68, 0.24);
  border-radius: 6px; }

.book-review-input {
  display: block;
  width: 100%;
  font-weight: 500;
  padding: 11px 16px;
  margin: 0;
  border: 0;
  resize: none; }
  .book-review-input:focus {
    box-shadow: none;
    outline: none; }

.book-review-input:first-child {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px; }

.book-review-input:last-child {
  border-bottom: 0;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px; }

.book-review-modal-footer {
  padding: 0;
  margin-top: 24px;
  border-top: 0; }
  .book-review-modal-footer .cncl-btn {
    background: #FFFFFF;
    color: #F05A2A;
    padding: 11px 32px;
    font-weight: 500; }
  .book-review-modal-footer .save-btn {
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    color: #FFFFFF;
    font-weight: 500;
    padding: 11px 32px;
    border: 0; }

.book-rating .orange {
  color: #E5C260;
  font-size: 21px; }
  .book-rating .orange .fa.fa-star.fa-x {
    margin-right: 3px; }

.fa.orange {
  color: #E5C260;
  margin-right: 3px; }

.rating, .rating label {
  margin: 0;
  padding: 0; }

.rating {
  border: none;
  float: left;
  margin-bottom: 16px; }

.rating > input {
  display: none; }

.rating > label:before {
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translate(0, 0);
  margin: 5px;
  font-family: "FontAwesome" !important;
  display: inline-block;
  content: "\f006";
  cursor: pointer; }

.rating > label {
  font-size: 21px;
  color: #E5C260;
  float: right;
  cursor: pointer; }

.rating > input:checked ~ label:before,
.rating:not(:checked) > label:hover,
.rating:not(:checked) > label:hover ~ label:before {
  content: "\f005";
  color: #E5C260;
  cursor: pointer; }

.rating > input:checked + label:hover,
.rating > input:checked ~ label:hover,
.rating > label:hover ~ input:checked ~ label,
.rating > input:checked ~ label:hover ~ label:before {
  content: "\f005";
  color: #E5C260;
  cursor: pointer; }

.new-password-error {
  right: 0; }
  .new-password-error:before {
    margin-left: 90% !important; }

.empty-input-error {
  right: 0; }
  .empty-input-error:before {
    margin-left: 85% !important; }

.password-change-success {
  text-align: center;
  transform: translateY(-50%);
  position: absolute;
  top: 50%;
  left: 0;
  font-family: Abril Fatface;
  font-size: 22px; }

.purchase-details-container {
  min-height: calc(100vh - 165px); }

.purchase-details-wrapper {
  margin: 40px auto 0; }

.purchase-details {
  float: left;
  width: 50%;
  margin-right: 82px; }

.purchase-heading {
  font-family: Abril Fatface;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 22px;
  letter-spacing: 0.04em; }

.purchase-success-confirmation {
  font-size: 16px;
  font-weight: 300;
  margin-top: 16px; }

.purchase-success-id {
  font-weight: 500;
  line-height: 21px;
  font-size: 16px;
  letter-spacing: 0.01em;
  margin-bottom: 16px; }

.purchased-book-container {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: left;
  align-items: left; }

.purchased-book-wrapper {
  width: 100%;
  border-radius: 6px;
  background-color: #FFFFFF;
  padding: 8px;
  border: 1px solid rgba(68, 68, 68, 0.24); }

.purchased-book-img-wrapper {
  display: inline-block; }
  .purchased-book-img-wrapper img {
    width: 111px;
    box-shadow: 0px 0px 14px rgba(0, 0, 0, 0.25);
    border-radius: 4px; }

.purchased-book-info {
  max-width: 361px;
  display: inline-block;
  vertical-align: top;
  padding: 16px; }

.purchased-book-name {
  font-style: normal;
  font-weight: 500;
  line-height: 21px;
  font-size: 16px;
  letter-spacing: 0.01em; }

.browse-purchase-book {
  float: left;
  width: 40%;
  border-left: 1px solid rgba(68, 68, 68, 0.24); }

.browse-wrapper {
  /*max-width: 221px;*/
  padding: 24px 40px;
  margin: 0 auto; }

.learn-btn {
  font-size: 14px;
  display: block;
  text-align: center;
  font-weight: 500;
  color: #FFFFFF;
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  letter-spacing: 0.01em;
  padding: 11px 25px;
  border-radius: 4px;
  margin-bottom: 24px; }
  .learn-btn:hover {
    text-decoration: none;
    color: #FFFFFF;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25); }
  .learn-btn:active {
    text-decoration: none;
    color: #FFFFFF;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25); }
  .learn-btn:focus {
    text-decoration: none;
    color: #FFFFFF;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25); }

.continue-browse {
  display: block;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  font-size: 13px;
  text-align: center;
  letter-spacing: 0.01em;
  color: #F05A2A;
  padding: 24px 0;
  margin-bottom: 24px;
  border-top: 1px solid rgba(68, 68, 68, 0.24);
  border-bottom: 1px solid rgba(68, 68, 68, 0.24); }
  .continue-browse:hover {
    color: #F05A2A;
    text-decoration: none; }
  .continue-browse:active {
    color: #F05A2A;
    text-decoration: none; }
  .continue-browse:focus {
    color: #F05A2A;
    text-decoration: none; }

.read-on-app {
  font-weight: 500;
  line-height: normal;
  font-size: 12px;
  text-align: center;
  letter-spacing: -0.01em;
  color: rgba(68, 68, 68, 0.84); }

.publisher-books-container {
  min-height: calc(100vh - 156px);
  background-color: #fcfcfc; }

.publisher-sidebar {
  max-width: 280px;
  min-height: calc(100vh - 156px);
  background: url("../images/wonderslate/publish-bg.png");
  background-repeat: no-repeat;
  background-color: #18382E;
  padding: 0;
  padding-top: 32px; }

.publisher-menu-wrapper {
  float: left;
  list-style: none;
  width: 100%;
  padding: 0;
  margin: 0;
  margin-top: 84px; }

.publisher-menu-item.active {
  background-color: #fcfcfc;
  border-left: 4px solid #F79420; }

.publisher-menu-item-link {
  display: block;
  color: #F79420;
  font-weight: 500;
  font-size: 16px;
  padding: 16px 20px 16px 42px; }
  .publisher-menu-item-link:hover {
    color: #F79420;
    text-decoration: none; }
  .publisher-menu-item-link:focus {
    color: #F79420;
    text-decoration: none; }
  .publisher-menu-item-link:active {
    color: #F79420;
    text-decoration: none; }

.wp-logo {
  display: block;
  width: 132px;
  height: 40px;
  background: url("../images/wonderslate/ws-publish-logo.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-indent: -999999px;
  margin: 0 auto; }

.publisher-books-table {
  font-family: "Montserrat", sans-serif;
  margin-top: 32px; }
  .publisher-books-table > tbody > tr > th, .publisher-books-table > tbody > tr td {
    padding: 14px 24px;
    border-top: 0;
    border-bottom: 1px solid rgba(151, 151, 151, 0.57); }
  .publisher-books-table > tbody > tr > th {
    color: rgba(68, 68, 68, 0.74);
    font-size: 14px;
    font-weight: 500; }
  .publisher-books-table > tbody > tr .right-aligned-data {
    text-align: right; }
  .publisher-books-table > tbody > tr > td {
    font-weight: 500;
    line-height: normal;
    color: #444444;
    mix-blend-mode: normal; }

.publisher-tabs {
  font-family: "Abril Fatface", cursive;
  padding-left: 0;
  margin-top: 32px;
  border-bottom: 0; }
  .publisher-tabs > li > a {
    position: relative;
    font-size: 32px;
    color: rgba(68, 68, 68, 0.64);
    padding: 0 15px 0 0;
    margin-right: 24px;
    border: 0; }
    .publisher-tabs > li > a.active {
      color: #444444;
      background-color: transparent;
      border: 0; }
    .publisher-tabs > li > a:hover {
      color: #444444;
      background-color: transparent;
      border: 0; }
    .publisher-tabs > li > a:active {
      color: #444444;
      background-color: transparent;
      border: 0; }
    .publisher-tabs > li > a:focus {
      color: #444444;
      background-color: transparent;
      border: 0; }
  .publisher-tabs > li.active > a {
    background-color: transparent;
    border: 0; }
    .publisher-tabs > li.active > a:hover {
      background-color: transparent;
      border: 0; }
    .publisher-tabs > li.active > a:active {
      background-color: transparent;
      border: 0; }
    .publisher-tabs > li.active > a:focus {
      background-color: transparent;
      border: 0; }
    .publisher-tabs > li.active > a:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 60px;
      border-bottom: 4px solid #F79420; }

.error-page {
  max-width: 602px;
  min-height: calc(100vh - 196px);
  margin-top: 40px; }

.error-wrapper {
  max-width: 296px;
  float: left; }

.error-head {
  font-family: "Montserrat", sans-serif;
  font-weight: 600;
  line-height: normal;
  font-size: 80px; }

.error-image-wrapper {
  float: left;
  max-width: 290px;
  margin-left: 16px; }

.error-text {
  font-weight: 300;
  line-height: 32px;
  font-size: 24px; }

.error-code {
  font-weight: 500;
  line-height: 21px;
  font-size: 16px;
  color: rgba(68, 68, 68, 0.74); }

.answer-match-container {
  min-height: calc(100vh - 162px);
  background-color: #FFFFFF; }

.answer-match-wrapper {
  float: none;
  max-width: 616px;
  padding: 24px 15px;
  margin: 0 auto; }

.answer-wrapper {
  float: left;
  width: 100%;
  margin-bottom: 16px; }

.label-answer {
  line-height: normal;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: rgba(68, 68, 68, 0.74); }

.answer-input-textarea {
  width: 100%;
  padding: 16px;
  border: 1px solid rgba(68, 68, 68, 0.74);
  border-radius: 4px;
  resize: none; }

.compare-btn-wrapper {
  float: left;
  width: 100%;
  text-align: center;
  margin-top: 8px;
  margin-bottom: 24px; }

.answer-compare-btn {
  color: #FFFFFF;
  font-weight: 500;
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  padding: 11px 32px;
  border-radius: 4px; }
  .answer-compare-btn:hover {
    color: #FFFFFF;
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
    text-decoration: none; }
  .answer-compare-btn:active {
    color: #FFFFFF;
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    text-decoration: none; }
  .answer-compare-btn:focus {
    color: #FFFFFF;
    background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
    text-decoration: none; }

.answer-match-progress-wrapper {
  float: left;
  width: 87%;
  height: 25px;
  background-color: #c4c4c4;
  border-radius: 30px; }

.answer-match-progress-success {
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  border-radius: 30px; }

.answer-match-accuracy {
  float: left;
  width: 10%;
  font-size: 18px;
  color: #000000;
  font-weight: 500;
  text-align: right;
  margin-left: 3%; }

.answer-phrase-match-table-wrapper {
  float: left;
  width: 100%;
  border-radius: 4px; }

.answer-phrase-match-table {
  background-color: #F8F8F8; }
  .answer-phrase-match-table > thead > tr > th {
    color: rgba(68, 68, 68, 0.74);
    font-weight: 500;
    line-height: normal;
    font-size: 12px;
    letter-spacing: 2px;
    text-transform: uppercase;
    padding: 16px;
    border-bottom: 0; }
  .answer-phrase-match-table > tbody > tr > td {
    color: #444444;
    font-weight: normal;
    line-height: normal;
    padding: 16px;
    border-top: 1px solid #979797; }

.show-more-accuracy-details {
  float: left;
  width: 100%;
  text-align: right;
  margin-bottom: 16px; }

.show-accuracy-table {
  font-weight: 500;
  line-height: normal;
  font-size: 12px;
  text-align: right;
  letter-spacing: 2px;
  text-transform: uppercase;
  color: #2F80ED; }
  .show-accuracy-table:hover {
    color: #2F80ED;
    text-decoration: none; }
  .show-accuracy-table:active {
    color: #2F80ED;
    text-decoration: none; }
  .show-accuracy-table:focus {
    color: #2F80ED;
    text-decoration: none; }

.is-rotated {
  display: inline-block;
  vertical-align: top;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg); }

.arihant-slider .carousel-indicators li {
  background-color: rgba(255, 255, 255, 0.5);
  border: none; }
  .arihant-slider .carousel-indicators li.active {
    background-color: #F05A2A; }
.arihant-slider .carousel-control {
  width: 5%; }

.ribbon-wrapper {
  position: absolute;
  bottom: 16px; }

.ribbon-front {
  background-color: #F05A2A;
  position: relative;
  left: -12px;
  padding: 4px 16px;
  font-size: 12px;
  font-weight: bold;
  color: #FFFFFF;
  z-index: 2;
  border-radius: 4px; }

.ribbon-front, .ribbon-back-left, .ribbon-back-right {
  -moz-box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.55);
  -khtml-box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.55);
  -webkit-box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.55);
  -o-box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.55); }

.ribbon-edge-topleft, .ribbon-edge-topright, .ribbon-edge-bottomleft, .ribbon-edge-bottomright {
  position: absolute;
  z-index: 1;
  border-style: solid;
  height: 0px;
  width: 0px; }

.ribbon-edge-bottomleft, .ribbon-edge-bottomright {
  top: 23px; }

.ribbon-edge-topleft, .ribbon-edge-bottomleft {
  left: -8px;
  border-color: transparent #A74400 transparent transparent;
  z-index: -1; }

.ribbon-edge-topleft {
  top: 0px;
  border-width: 0px 20px 0 0; }

.ribbon-edge-bottomleft {
  border-width: 0 20px 10px 0; }

.ribbon-edge-topright, .ribbon-edge-bottomright {
  left: 100px;
  border-color: transparent transparent transparent #A74400; }

.ribbon-edge-topright {
  top: 0px;
  border-width: 0px 0 0 0px; }

.ribbon-edge-bottomright {
  border-width: 0 0 0px 0px; }

.ribbon-back-left {
  position: absolute;
  top: 10px;
  left: 0px;
  width: 0px;
  height: 40px;
  z-index: 0; }

.ribbon-back-right {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 0px;
  height: 40px;
  z-index: 0; }

.additional-assignment {
  padding: 16px; }

.additional-assignment-teacher-img-wrapper {
  width: 32px;
  height: 32px; }

.additional-assignment-teacher-img {
  width: 100%; }

.additional-assignment-teacher-name-wrapper {
  padding: 0; }

.additional-assignment-teacher-name {
  margin: 0; }

.additional-assignment-teacher-time {
  font-size: 12px;
  opacity: 0.5; }

.additional-assignment-item {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.24); }

.review-attempt-wrapper {
  padding: 24px 24px 0; }

.review-attempt-clm {
  padding-top: 2px; }

.review-attempt-clm:first-child {
  padding: 14px 0;
  border-right: 1px solid rgba(68, 68, 68, 0.24); }

.assignment-not-done {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 16px;
  text-align: center;
  text-transform: uppercase;
  margin: 0; }

.attempt-assignment-btn {
  width: 160px;
  text-align: center;
  color: #FFFFFF;
  background: linear-gradient(270deg, #30C465 0%, #3AE878 100%);
  padding: 11px;
  border: 0;
  border-radius: 4px; }
  .attempt-assignment-btn:hover {
    color: #FFFFFF;
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%); }
  .attempt-assignment-btn:active {
    color: #FFFFFF;
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%); }
  .attempt-assignment-btn:focus {
    color: #FFFFFF;
    background: linear-gradient(270deg, #30C465 0%, #3AE878 100%); }

.review-assignment-btn {
  width: 160px;
  text-align: center;
  color: #FFFFFF;
  background: linear-gradient(270deg, #2F80ED 0%, #2D9CDB 100%);
  padding: 11px;
  border: 0;
  border-radius: 4px; }
  .review-assignment-btn:hover {
    color: #FFFFFF;
    text-decoration: none; }
  .review-assignment-btn:active {
    color: #FFFFFF;
    background: linear-gradient(270deg, #2F80ED 0%, #2D9CDB 100%);
    box-shadow: none; }
  .review-assignment-btn:focus {
    color: #FFFFFF;
    background: linear-gradient(270deg, #2F80ED 0%, #2D9CDB 100%);
    box-shadow: none; }

.assignment-done {
  font-family: Montserrat;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 16px;
  text-transform: uppercase;
  color: #46B520;
  margin: 0; }
  .assignment-done > .check-circle {
    vertical-align: bottom;
    font-size: 21px; }

.done-student {
  font-family: Montserrat;
  font-style: normal;
  font-weight: normal;
  line-height: 13px;
  font-size: 10px;
  text-transform: uppercase;
  color: rgba(68, 68, 68, 0.54);
  margin: 0; }

.smart-books-slider {
  background-color: #FFFFFF;
  position: relative;
  z-index: 100; }

.main-heading {
  font-family: Abril Fatface;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 40px;
  text-align: center;
  letter-spacing: 0.04em; }

.slider-hash-tags {
  list-style: none;
  padding: 0;
  padding-left: 10px;
  margin: 0; }

.slider-hash-tag-item {
  display: inline-block;
  margin-right: 38px; }

.slider-hash-tag-link {
  display: block;
  font-weight: 300;
  font-size: 22px;
  letter-spacing: 0.01em; }
  .slider-hash-tag-link:hover {
    text-decoration: none;
    color: #F05A2A; }

.slider-books-heading {
  font-family: "Abril Fatface", cursive;
  font-size: 86px;
  color: rgba(68, 68, 68, 0.24);
  letter-spacing: 0.04em;
  text-transform: uppercase;
  margin-bottom: 0; }

.slider-books-wrapper {
  min-height: 380px;
  position: relative;
  top: -24px; }

.slider-books-holder-down {
  position: absolute;
  top: 80px;
  left: 38px; }

.slider-book {
  display: inline-block;
  max-width: 160px;
  max-height: 230px;
  margin-right: 40px;
  transform-style: preserve-3d; }
  .slider-book img {
    box-shadow: 0px 4px 16px rgba(3, 3, 3, 0.6);
    border-radius: 4px; }

.slider-book:last-child {
  margin-right: 0; }

.slider-book-link {
  display: block;
  transform: translateZ(50px); }

.slider-control {
  max-height: 400px; }
  .slider-control img {
    position: absolute;
    top: 50%;
    margin: 0 auto;
    border-radius: 50px;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%); }

.carousel-showmanymoveone .carousel-control {
  width: 4%;
  background-image: none; }

.carousel-inner .active.left {
  left: -33%; }

.carousel-inner .active.right {
  left: 33%; }

.carousel-inner .next {
  left: 33%; }

.carousel-inner .prev {
  left: -33%; }

.carousel-control.left {
  background-image: none; }

.carousel-control.right {
  background-image: none; }

.carousel-inner .item {
  background: white; }

/*
 *  Owl Carousel - Animate Plugin
 */
.owl-carousel .animated {
  animation-duration: 1000ms;
  animation-fill-mode: both; }
.owl-carousel .owl-animated-in {
  z-index: 0; }
.owl-carousel .owl-animated-out {
  z-index: 1; }
.owl-carousel .fadeOut {
  animation-name: fadeOut; }

@keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
/*
 * 	Owl Carousel - Auto Height Plugin
 */
.owl-height {
  transition: height 500ms ease-in-out; }

/*
 *  Owl Carousel - Core
 */
.owl-carousel {
  display: none;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  /* position relative and z-index fix webkit rendering fonts issue */
  position: relative;
  z-index: 1; }
  .owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    -moz-backface-visibility: hidden;
    /* fix firefox animation glitch */ }
  .owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0; }
  .owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    /* fix for flashing background */
    -webkit-transform: translate3d(0px, 0px, 0px); }
  .owl-carousel .owl-wrapper,
  .owl-carousel .owl-item {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0); }
  .owl-carousel .owl-item {
    position: relative;
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none; }
  .owl-carousel .owl-item img {
    display: block;
    width: 100%; }
  .owl-carousel .owl-nav.disabled,
  .owl-carousel .owl-dots.disabled {
    display: none; }
  .owl-carousel .owl-nav .owl-prev,
  .owl-carousel .owl-nav .owl-next,
  .owl-carousel .owl-dot {
    cursor: pointer;
    cursor: hand;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
  .owl-carousel.owl-loaded {
    display: block; }
  .owl-carousel.owl-loading {
    opacity: 0;
    display: block; }
  .owl-carousel.owl-hidden {
    opacity: 0; }
  .owl-carousel.owl-refresh .owl-item {
    visibility: hidden; }
  .owl-carousel.owl-drag .owl-item {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
  .owl-carousel.owl-grab {
    cursor: move;
    cursor: grab; }
  .owl-carousel.owl-rtl {
    direction: rtl; }
  .owl-carousel.owl-rtl .owl-item {
    float: right; }

/* No Js */
.no-js .owl-carousel {
  display: block; }

/*
 * 	Owl Carousel - Lazy Load Plugin
 */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity 400ms ease; }
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d; }

/*
 * 	Green theme - Owl Carousel CSS File
 */
.owl-theme .owl-nav {
  margin-top: 10px;
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-nav [class*='owl-'] {
    color: #FFF;
    font-size: 14px;
    margin: 5px;
    padding: 4px 7px;
    background: #D6D6D6;
    display: inline-block;
    cursor: pointer;
    border-radius: 3px; }
    .owl-theme .owl-nav [class*='owl-']:hover {
      background: #4DC7A0;
      color: #FFF;
      text-decoration: none; }
  .owl-theme .owl-nav .disabled {
    opacity: 0.5;
    cursor: default; }
.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px; }
.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom: 1;
    *display: inline; }
    .owl-theme .owl-dots .owl-dot span {
      width: 10px;
      height: 10px;
      margin: 5px 7px;
      background: #D6D6D6;
      display: block;
      -webkit-backface-visibility: visible;
      transition: opacity 200ms ease;
      border-radius: 30px; }
    .owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
      background: #4DC7A0; }

/*
 * 	Default theme - Owl Carousel CSS File
 */
.owl-theme .owl-nav {
  margin-top: 10px;
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-nav [class*='owl-'] {
    color: #FFF;
    font-size: 14px;
    margin: 5px;
    padding: 4px 7px;
    background: #D6D6D6;
    display: inline-block;
    cursor: pointer;
    border-radius: 3px; }
    .owl-theme .owl-nav [class*='owl-']:hover {
      background: #4DC7A0;
      color: #FFF;
      text-decoration: none; }
  .owl-theme .owl-nav .disabled {
    opacity: 0.5;
    cursor: default; }
.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px; }
.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom: 1;
    *display: inline; }
    .owl-theme .owl-dots .owl-dot span {
      width: 10px;
      height: 10px;
      margin: 5px 7px;
      background: #D6D6D6;
      display: block;
      -webkit-backface-visibility: visible;
      transition: opacity 200ms ease;
      border-radius: 30px; }
    .owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
      background: #4DC7A0; }

.owl-theme .owl-nav {
  margin-top: 10px;
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-nav [class*='owl-'] {
    color: #FFF;
    font-size: 14px;
    margin: 5px;
    padding: 4px 7px;
    background: #D6D6D6;
    display: inline-block;
    cursor: pointer;
    border-radius: 3px; }
    .owl-theme .owl-nav [class*='owl-']:hover {
      background: #4DC7A0;
      color: #FFF;
      text-decoration: none; }
  .owl-theme .owl-nav .disabled {
    opacity: 0.5;
    cursor: default; }
.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px; }
.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom: 1;
    *display: inline; }
    .owl-theme .owl-dots .owl-dot span {
      width: 10px;
      height: 10px;
      margin: 5px 7px;
      background: #D6D6D6;
      display: block;
      -webkit-backface-visibility: visible;
      transition: opacity 200ms ease;
      border-radius: 30px; }
    .owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
      background: #4DC7A0; }

/*
 * 	Owl Carousel - Video Plugin
 */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000; }
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url("owl.video.play.png") no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform 100ms ease; }
.owl-carousel .owl-video-play-icon:hover {
  transform: scale(1.3, 1.3); }
.owl-carousel .owl-video-playing .owl-video-tn,
.owl-carousel .owl-video-playing .owl-video-play-icon {
  display: none; }
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity 400ms ease; }
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%; }

/*
 *  Owl Carousel - Core
 */
.owl-carousel {
  display: none;
  width: 100%;
  -webkit-tap-highlight-color: transparent;
  /* position relative and z-index fix webkit rendering fonts issue */
  position: relative;
  z-index: 1; }
  .owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
    -moz-backface-visibility: hidden;
    /* fix firefox animation glitch */ }
  .owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0; }
  .owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    /* fix for flashing background */
    -webkit-transform: translate3d(0px, 0px, 0px); }
  .owl-carousel .owl-wrapper,
  .owl-carousel .owl-item {
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0); }
  .owl-carousel .owl-item {
    position: relative;
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none; }
  .owl-carousel .owl-item img {
    display: block;
    width: 100%; }
  .owl-carousel .owl-nav.disabled,
  .owl-carousel .owl-dots.disabled {
    display: none; }
  .owl-carousel .owl-nav .owl-prev,
  .owl-carousel .owl-nav .owl-next,
  .owl-carousel .owl-dot {
    cursor: pointer;
    cursor: hand;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
  .owl-carousel.owl-loaded {
    display: block; }
  .owl-carousel.owl-loading {
    opacity: 0;
    display: block; }
  .owl-carousel.owl-hidden {
    opacity: 0; }
  .owl-carousel.owl-refresh .owl-item {
    visibility: hidden; }
  .owl-carousel.owl-drag .owl-item {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; }
  .owl-carousel.owl-grab {
    cursor: move;
    cursor: grab; }
  .owl-carousel.owl-rtl {
    direction: rtl; }
  .owl-carousel.owl-rtl .owl-item {
    float: right; }

/* No Js */
.no-js .owl-carousel {
  display: block; }

/*
 *  Owl Carousel - Animate Plugin
 */
.owl-carousel .animated {
  animation-duration: 1000ms;
  animation-fill-mode: both; }
.owl-carousel .owl-animated-in {
  z-index: 0; }
.owl-carousel .owl-animated-out {
  z-index: 1; }
.owl-carousel .fadeOut {
  animation-name: fadeOut; }

@keyframes fadeOut {
  0% {
    opacity: 1; }
  100% {
    opacity: 0; } }
/*
 * 	Owl Carousel - Auto Height Plugin
 */
.owl-height {
  transition: height 500ms ease-in-out; }

/*
 * 	Owl Carousel - Lazy Load Plugin
 */
.owl-carousel .owl-item .owl-lazy {
  opacity: 0;
  transition: opacity 400ms ease; }
.owl-carousel .owl-item img.owl-lazy {
  transform-style: preserve-3d; }

/*
 * 	Owl Carousel - Video Plugin
 */
.owl-carousel .owl-video-wrapper {
  position: relative;
  height: 100%;
  background: #000; }
.owl-carousel .owl-video-play-icon {
  position: absolute;
  height: 80px;
  width: 80px;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
  background: url("owl.video.play.png") no-repeat;
  cursor: pointer;
  z-index: 1;
  -webkit-backface-visibility: hidden;
  transition: transform 100ms ease; }
.owl-carousel .owl-video-play-icon:hover {
  transform: scale(1.3, 1.3); }
.owl-carousel .owl-video-playing .owl-video-tn,
.owl-carousel .owl-video-playing .owl-video-play-icon {
  display: none; }
.owl-carousel .owl-video-tn {
  opacity: 0;
  height: 100%;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  transition: opacity 400ms ease; }
.owl-carousel .owl-video-frame {
  position: relative;
  z-index: 1;
  height: 100%;
  width: 100%; }

/*
 * 	Default theme - Owl Carousel CSS File
 */
.owl-theme .owl-nav {
  margin-top: 10px;
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-nav [class*='owl-'] {
    color: #FFF;
    font-size: 14px;
    margin: 5px;
    padding: 4px 7px;
    background: #D6D6D6;
    display: inline-block;
    cursor: pointer;
    border-radius: 3px; }
    .owl-theme .owl-nav [class*='owl-']:hover {
      background: #4DC7A0;
      color: #FFF;
      text-decoration: none; }
  .owl-theme .owl-nav .disabled {
    opacity: 0.5;
    cursor: default; }
.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px; }
.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom: 1;
    *display: inline; }
    .owl-theme .owl-dots .owl-dot span {
      width: 10px;
      height: 10px;
      margin: 5px 7px;
      background: #D6D6D6;
      display: block;
      -webkit-backface-visibility: visible;
      transition: opacity 200ms ease;
      border-radius: 30px; }
    .owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
      background: #4DC7A0; }

/*
 * 	Green theme - Owl Carousel CSS File
 */
.owl-theme .owl-nav {
  margin-top: 10px;
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-nav [class*='owl-'] {
    color: #FFF;
    font-size: 14px;
    margin: 5px;
    padding: 4px 7px;
    background: #D6D6D6;
    display: inline-block;
    cursor: pointer;
    border-radius: 3px; }
    .owl-theme .owl-nav [class*='owl-']:hover {
      background: #4DC7A0;
      color: #FFF;
      text-decoration: none; }
  .owl-theme .owl-nav .disabled {
    opacity: 0.5;
    cursor: default; }
.owl-theme .owl-nav.disabled + .owl-dots {
  margin-top: 10px; }
.owl-theme .owl-dots {
  text-align: center;
  -webkit-tap-highlight-color: transparent; }
  .owl-theme .owl-dots .owl-dot {
    display: inline-block;
    zoom: 1;
    *display: inline; }
    .owl-theme .owl-dots .owl-dot span {
      width: 10px;
      height: 10px;
      margin: 5px 7px;
      background: #D6D6D6;
      display: block;
      -webkit-backface-visibility: visible;
      transition: opacity 200ms ease;
      border-radius: 30px; }
    .owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
      background: #4DC7A0; }

@media screen and (min-width: 320px) and (max-width: 767px) {
  .slide-left {
    margin-left: -600px; }

  .flash-card-slider .item {
    min-width: 100%; }

  .video-btn {
    right: -20px; }

  .link-btn {
    right: -60px; }

  .card-wrapper {
    width: 100%;
    padding: 0; }
    .card-wrapper .rprice-tag .complte-book {
      margin-left: 5px; }

  .navbar-default .navbar-toggle:hover.collapsed, .navbar-default .navbar-toggle:focus.collapsed {
    background: #f05a2a; }

  .flip-container {
    width: 100%; }

  .flipper {
    width: 100%; }
    .flipper .front {
      width: 100%; }
    .flipper .back {
      width: 100%; }

  .notes-creation-wrapper {
    top: 0;
    height: calc(100vh - 85px);
    width: 100%;
    height: 100%;
    z-index: 1100; }

  .notes-creation-header {
    padding: 8px 8px 16px; }

  .notes-created-by-user {
    max-width: 100%;
    padding-right: 24px; }

  .notes-close-mobile {
    color: #444444;
    position: absolute;
    left: 0;
    top: 20px; }
    .notes-close-mobile:hover {
      color: #444444; }
    .notes-close-mobile:active {
      color: #444444; }
    .notes-close-mobile:focus {
      color: #444444; }

  .preview-book-name {
    font-size: 16px; }

  .marketplace-rating-wrapper {
    padding: 0;
    margin-top: 0; }

  .marketplace-rating-link:first-child {
    font-size: 14px;
    display: block;
    padding-right: 0;
    margin-bottom: 8px;
    border-right: 0; }

  .marketplace-rating-link:last-child {
    font-size: 14px;
    display: block;
    padding-left: 0; }

  #drift-widget-container {
    display: none; }

  .fixed-tabs-catos-mobile {
    position: fixed;
    width: 100%;
    background-color: #FFFFFF;
    z-index: 99;
    animation: smoothScroll .2s forwards; }

  .tab-margin {
    margin-top: 60px; }

  .fixed-header {
    top: 0; }

  #materialtabspanel {
    margin-top: 40px; }

  .wonderslate-navbar .navbar-container .navbar-nav.header-menus {
    text-align: center;
    margin-left: 0; }
  .wonderslate-navbar .navbar-container .navbar-right {
    margin-left: 0; }
    .wonderslate-navbar .navbar-container .navbar-right li {
      text-align: center; }
  .wonderslate-navbar .navbar-container .navbar-collapse {
    padding-left: 0; }

  .r-form-edit {
    margin-top: 20px; }

  .tabs-section .chapter-tabs {
    padding: 4px 10px !important; }

  .r-tab .r-tab-child {
    padding-left: 0; }

  .navbar-default .navbar-toggle {
    background-color: #F05A2A; }
    .navbar-default .navbar-toggle .icon-bar {
      border: 1px solid #FFFFFF; }

  .navbar-default .navbar-toggle .icon-bar {
    background: #ffffff; }

  .social-login {
    padding: 11px 64px; }

  .modal.fade .sign-in-modal-dialog {
    top: 80px;
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none; }

  .sign-in-modal-content {
    min-height: auto;
    padding: 10px; }

  .term-condition {
    padding: 0 10px;
    margin-top: 16px; }

  .create-account {
    padding: 0 10px; }

  .slider-hash-tag-item {
    margin-right: 10px; }

  .slider-books-heading {
    margin: 0; }

  .user-greeting {
    margin-left: 0; }

  .greeting-user {
    font-size: 30px; }

  .book-wrapper {
    margin: 0 0px 20px; }

  .books-content-wrapper {
    margin-top: 0;
    justify-content: space-evenly; }

  .tabs-holder {
    padding-left: 15px;
    padding-right: 15px;
    box-shadow: inset 0px -1px 0px rgba(0, 0, 0, 0.25); }

  .carousel-showmanymoveone .carousel-control {
    width: 10%;
    background-image: none; }

  .nav-tabs-wrapper {
    max-width: 100%;
    overflow: hidden;
    overflow-x: auto;
    white-space: nowrap; }
    .nav-tabs-wrapper li {
      display: inline-block;
      float: none;
      margin-right: 0;
      margin-left: 0; }
      .nav-tabs-wrapper li .level-label {
        display: none; }

  .tab-sub-categories-wrapper {
    width: 100%;
    padding: 0 5px;
    margin-left: 0;
    white-space: nowrap;
    overflow: hidden;
    overflow-x: auto; }

  .tab-sub-categories {
    background-color: #fff;
    padding: 4px 8px;
    padding-left: 15px;
    padding-right: 15px;
    margin-top: 0;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25), 0px 0px 4px rgba(0, 0, 0, 0.25);
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease; }

  .tab-sub-categories-item {
    margin-left: 5px;
    margin-bottom: 0; }

  .tab-sub-categories-item-btn {
    padding: 6px 15px;
    border: 1px solid rgba(68, 68, 68, 0.24); }

  .class-selection-div {
    padding: 10px; }
    .class-selection-div .btn-group {
      width: 50%;
      float: right; }
    .class-selection-div .class-selection-btn {
      width: 100%;
      text-align: center;
      background: none;
      padding: 8px 0 0 0;
      border: 0;
      -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
      box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125); }

  .main-footer {
    padding: 0; }

  .download-app-btn {
    max-width: 200px;
    height: 60px; }

  .footer-credits, .wonderslate-mobile-app {
    text-align: center;
    padding: 10px 0; }

  .mobile-app-legend {
    margin: 0; }

  .footer-links {
    text-align: center; }
    .footer-links li a {
      padding: 10px 0; }
      .footer-links li a:hover {
        color: #F05A2A;
        text-decoration: none; }

  .preview-book-container {
    margin-top: 8px; }

  .preview-book-wrapper {
    margin: 0;
    padding: 24px 0; }

  .book-preview-detail {
    padding: 0 16px; }

  .preview-book-desc {
    margin-top: 0;
    padding: 16px; }

  .preview-book-btn {
    width: 100%;
    max-width: 100%; }

  .preview-book-btns {
    margin-top: 0; }

  .book-preview-desc {
    padding: 0;
    margin-top: 8px; }

  .video-section {
    justify-content: center; }

  .read-book-chapters {
    top: 0;
    width: 72%;
    min-height: calc(100vh - 1px);
    max-height: calc(100vh - 1px);
    margin-left: -600px;
    position: fixed;
    z-index: 999;
    overflow: hidden;
    overflow-y: auto; }

  #hideShowDivMobile {
    padding: 10px;
    background: #EEEEEE;
    position: fixed;
    top: 30%;
    left: -27px;
    width: 100px;
    height: 48px;
    display: block;
    background-size: 100% 100%;
    background-position: center;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    border-top: 2px solid #F05A2A;
    z-index: 9; }
  #hideShowDivMobile:hover {
    text-decoration: none; }
  #hideShowDivMobile:focus {
    text-decoration: none; }

  .book-read-material {
    width: 100% !important;
    max-height: 100%;
    padding-left: 15px; }

  #htmlreadingcontent {
    padding-left: 15px;
    padding-bottom: 70px; }
    #htmlreadingcontent table {
      width: auto !important; }

  #quizModal {
    z-index: 9999; }

  .tabs-section {
    width: 100%; }
    .tabs-section .chapter-tabs {
      max-width: 100%;
      overflow: auto;
      overflow-y: hidden; }

  .user-profile-orders-tab-content {
    float: none; }

  .user-profile-orders {
    min-height: calc(100vh - 245px); }
    .user-profile-orders .user-profile-tabs > li {
      margin-right: 8px; }

  .user-profile-image {
    float: none;
    margin: 0 auto; }

  .user-profile-details {
    width: 100%; }

  .user-profile-tab, .user-order-tab {
    padding: 16px; }

  .user-order-tab {
    padding: 16px; }

  .users-orders-details {
    padding: 8px; }

  .user-purchase-order-detail {
    width: 100%;
    border-right: 0;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(68, 68, 68, 0.2); }

  .order-book-image {
    margin-right: 8px; }

  .order-book-detail {
    width: 57%;
    padding-top: 0;
    padding-right: 0; }

  .order-payment-details {
    width: 100%;
    padding: 16px 0;
    border: 0; }

  .order-review-section {
    width: 100%;
    margin-bottom: 16px; }

  .book-reviews {
    padding: 0;
    margin: 16px 0 0 0; }

  .review-rating-wrapper {
    margin-bottom: 8px;
    padding: 16px; }

  .rating-review-heading {
    margin: 0; }

  .book-overall-rating {
    padding-right: 0;
    padding: 15px;
    margin: 8px 0 0 0; }

  .rating-by-user {
    font-size: 14px;
    margin: 0 0 16px; }

  .rating-bars-wrapper {
    padding: 0 15px;
    border: 0;
    border-left: 1px solid rgba(68, 68, 68, 0.2); }

  .user-write-review {
    padding: 0;
    margin-top: 16px; }

  .write-review-label {
    margin-top: 16px; }

  .user-reviews {
    padding: 16px; }

  .error-page {
    min-height: calc(100vh - 285px); }

  .error-head {
    font-size: 40px;
    margin: 0; }

  .error-text {
    font-size: 13px;
    line-height: normal; }

  .error-image-wrapper {
    margin: 0; }

  .publisher-tabs > li > a {
    font-size: 15px;
    padding: 0 8px 0 0;
    margin-right: 0; }
  .publisher-tabs > li.active a:after {
    width: 30px;
    border-bottom: 2px solid #F79420; }

  .publisher-tabs-content {
    overflow-x: auto; }

  .side-rating {
    width: 30%; }

  .chapter-test-wise-detail-wrapper {
    width: 100%;
    padding: 0;
    margin: 16px 0 0 0; }

  .chapter-test-wise-detail-wrapper:nth-child(3n) {
    margin: 16px 0 0 0; }

  .purchase-details {
    width: 100%; }

  .purchased-book-info {
    max-width: 212px;
    padding: 8px 0 0 8px; }

  .browse-purchase-book {
    width: 100%;
    border: 0; }

  .chapters-modal {
    margin-top: 80px; }

  .chapter-selection-table {
    min-height: 400px;
    max-height: 400px;
    padding-right: 0;
    margin-bottom: 56px;
    border: 0; }

  .chapter-details-aside {
    padding-right: 15px;
    padding-left: 15px; }

  .book-unlock {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) -25.75%, rgba(255, 255, 255, 0.91) 36.14%, #FFFFFF 78.54%);
    background-position: top;
    background-size: 100% 100%;
    padding-top: 24px;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 24px; }

  .unlock-complete-book-btn {
    width: 60%;
    margin: 0 auto; }

  .section-btns {
    width: 100%; }

  .wonderslate-navbar {
    width: 100%; }

  .mobile-header-brand {
    float: left; }

  .navbar-right-mobile {
    float: right;
    width: 30%;
    text-align: right; }

  .mobile-header-icon {
    display: inline-block !important;
    width: 28px;
    height: 28px; }

  .mobile-header-icon:first-child {
    margin-right: 16px; }

  .mobile-search {
    display: block;
    width: 28px;
    height: 28px;
    background: url("../images/wonderslate/search-icon.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-indent: -99999px; }

  .mobile-user {
    display: block;
    width: 28px;
    height: 28px;
    background: url("../images/wonderslate/user-icon.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    text-indent: -99999px; }

  .mobile-bottom-menu-wrapper {
    list-style: none;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #FFFFFF;
    padding: 6px 0;
    margin: 0;
    box-shadow: 0px -1px 8px rgba(0, 0, 0, 0.25);
    z-index: 5; }

  .mobile-bottom-menu-item {
    float: left;
    width: 33.33%;
    font-weight: 500;
    line-height: normal;
    font-size: 11px;
    text-align: center;
    letter-spacing: 0.01em; }

  .mobile-bottom-menu-item-link {
    display: block;
    margin: 0 auto 4px;
    text-indent: -9999999px; }

  .mobile-store {
    width: 28px;
    height: 28px;
    background: url("../images/wonderslate/icons/wonderslate-shop-inactive.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%; }
    .mobile-store.active {
      background: url("../images/wonderslate/icons/wonderslate-shop.svg") !important;
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important; }

  .mobile-library {
    width: 28px;
    height: 28px;
    background: url("../images/wonderslate/icons/library-inactive.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%; }
    .mobile-library.active {
      background: url("../images/wonderslate/icons/library-active.svg") !important;
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important; }

  .mobile-test-gen {
    width: 28px;
    height: 28px;
    background: url("../images/wonderslate/icons/testgen-inactive.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%; }
    .mobile-test-gen.active {
      background: url("../images/wonderslate/icons/testgen-active.svg") !important;
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important; }

  .profile-dropdown {
    position: absolute !important;
    background-color: #FFFFFF !important;
    top: 48px !important;
    border: 1px solid rgba(68, 68, 68, 0.24) !important; } }
@media screen and (max-width: 920px) {
  .additional-ref-section {
    padding-left: 15px; }

  .additional-ref-wrapper {
    width: 100%; }

  .additional-ref-img-wrapper {
    width: 30%; }

  .additional-ref-img {
    max-width: 100%;
    height: auto; }

  .additional-ref-info {
    width: 70%;
    padding: 8px; } }
@media screen and (max-width: 1280px) {
  .slider-book {
    max-width: 135px;
    margin-right: 0; }

  .slider-books-holder-down {
    left: 20px; } }
@media screen and (min-width: 320px) and (max-width: 768px) and (orientation: portrait) {
  .top-books-list {
    width: 100%; }

  .slider-books-wrapper {
    min-height: 256px;
    top: -15px; }

  .slider-books-holder-down {
    left: 15px; }

  .slider-book {
    max-width: 128px;
    margin-right: 0; }

  .slider-book:last-child {
    margin-left: 16px; } }
@media screen and (min-width: 320px) and (max-width: 768px) and (orientation: landscape) {
  .slider-hash-tag-item {
    margin-right: 30px; }

  .book-wrapper {
    margin: 0 8px 20px; }

  .video-section {
    justify-content: flex-start; } }
@media screen and (min-width: 769px) {
  .book-read-material {
    overflow: hidden;
    overflow-y: auto; }

  .user-profile-orders {
    min-height: calc(100vh - 204px);
    margin-top: 40px; }

  .user-profile-orders-tab-content {
    padding-left: 32px;
    padding-right: 32px;
    margin-top: 40px; }

  .change-password-modal .modal-sm {
    width: 424px; }

  .book-review-modal .modal-sm {
    width: 640px; }

  .error-wrapper {
    padding: 0; }

  .error-image-wrapper {
    padding: 0; }

  .publisher-books-wrapper {
    padding: 0 40px; } }
@media screen and (min-width: 1024px) {
  .user-profile-orders-tab-content {
    width: 100%; }

  .total-paid-amt {
    margin-top: 32px; } }
@media screen and (min-width: 640px) {
  .book-review-modal .modal-sm {
    width: 640px; } }
@media screen and (min-width: 768px) and (max-width: 770px) {
  .card-wrapper {
    width: 100%; }

  .read-book-chapters {
    width: 50%;
    margin-left: -500px;
    overflow: hidden;
    overflow-y: auto;
    position: fixed;
    z-index: 9999; }

  .book-read-material {
    width: 100%; }

  #hideShowDivTab {
    display: block !important;
    padding: 10px;
    background: #EEEEEE;
    position: fixed;
    top: 25%;
    left: -27px;
    width: 100px;
    height: 48px;
    display: block;
    background-size: 100% 100%;
    background-position: center;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
    border-top: 2px solid #F05A2A;
    z-index: 1; }
  #hideShowDivTab:hover {
    text-decoration: none; }
  #hideShowDivTab:focus {
    text-decoration: none; }

  .no-scroll {
    overflow: hidden;
    position: relative;
    height: 100%;
    width: 100%;
    position: fixed; }

  .book-read-material {
    max-height: 100%; }

  .chapter-test-wise-detail-wrapper {
    width: 50%;
    padding: 0 16px 0 0;
    margin: 32px 0 0 0; }

  .chapter-test-wise-detail-wrapper:nth-child(odd) {
    padding: 0 0 0 16px; }

  .section-btns {
    width: 100%; } }
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .purchase-details {
    width: 100%; }

  .browse-purchase-book {
    width: 100%;
    border: 0; }

  .video-url .modal, .web-url .modal {
    top: 37%; }

  #cke_1_contents {
    height: 400px !important; }

  #additional .link-btn {
    text-align: right; }

  .tabs-section .chapter-tabs {
    max-width: 100%; }

  #main-navbar {
    display: flex !important; }

  .custom-prBtn, .custom-prBtn-bl {
    width: 400px; }

  .link-btn .drop-pr {
    left: 0; } }
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape) {
  .read-book-chapters {
    position: absolute;
    height: 100%;
    z-index: 9;
    width: 25%; }

  #hideShowDiv {
    left: 25%;
    z-index: 10; } }
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .chapter-test-wise-detail-wrapper {
    margin: 32px 47px 0 0; } }
@media screen and (min-width: 768px) {
  .fixed-tabs-holder-desktop {
    position: fixed;
    width: 100%;
    z-index: 99; } }
.hide-btns {
  animation: hideSectionBtns .2s forwards; }

/* ========== Animations ==========*/
@keyframes smoothScroll {
  0% {
    transform: translateY(-40px); }
  100% {
    transform: translateY(0px); } }
@keyframes hideSectionBtns {
  0% {
    transform: translateY(100px); }
  100% {
    transform: translateY(100px); } }
/*Tab devices-Raj*/
@media (min-width: 768px) and (max-width: 1024px) {
  #book-read-material {
    width: 100%; }

  .tabs-section .chapter-tabs#chapter-details-tabs {
    padding: 4px 10px; }

  .r-tab .r-tab-child {
    padding-left: 0; }

  .user-profile-details {
    width: 100%; }

  .user-profile-orders-tab-content {
    float: none; }

  .r-form-edit {
    margin-top: 20px; } }
.loading-icon {
  width: 100%;
  height: 100%;
  background-color: rgba(68, 68, 68, 0.64);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999; }

.pagenumber-green {
  height: 21px;
  width: 35px;
  text-align: center;
  border-width: 1px;
  border-color: green;
  border-radius: 3px;
  border-style: solid;
  display: inline-block;
  padding: 0px;
  background-color: green;
  color: white; }

.main {
  background-color: white;
  margin: 10px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 4px; }

html, body {
  font-family: "Montserrat", sans-serif !important;
  color: #444444; }

body {
  font-size: initial !important;
  line-height: initial !important;
  padding: 0 !important;
  text-align: initial !important; }

h2 {
  background: none !important;
  padding: 0; }

.lining_box, .lining_box1 {
  border: none !important; }

* {
  margin: 0;
  padding: 0; }

.chapter {
  text-align: inherit !important; }

.image {
  text-align: center; }

.subjectHead {
  text-align: inherit !important; }

.readfind {
  color: inherit !important;
  margin: 0 !important;
  padding: 0 !important; }

.glossaryText {
  color: inherit !important; }

.author {
  text-align: inherit !important; }

.bg {
  background-color: inherit !important; }

span.char-style-override-6 {
  color: inherit !important;
  font-size: inherit !important; }

span.char-style-override-10 {
  color: inherit !important;
  font-family: inherit !important;
  font-size: inherit !important;
  font-style: inherit !important;
  font-weight: inherit !important; }

.note {
  font-style: inherit !important;
  font-size: inherit !important;
  color: inherit !important;
  text-align: inherit !important; }

div.layout {
  text-align: inherit !important; }

div.chapter_pos {
  text-align: inherit !important;
  width: inherit !important;
  position: static !important; }

div.chapter_pos div {
  background: none !important;
  padding: 0 !important;
  line-height: 0 !important;
  width: auto !important;
  margin: auto;
  opacity: 1 !important; }

div.chapter_pos div span {
  font-size: inherit !important;
  color: inherit !important;
  font-weight: normal !important; }

.footer {
  display: inherit !important; }

.cover_img_small {
  width: auto !important; }

@media only screen and (max-width: 767px) {
  div.chapter_pos {
    top: 10%;
    font-size: 1em; }

  div.chapter_pos div {
    width: 100% !important; }

  .cover_img_small {
    width: auto !important; } }
.underline_txt {
  font-decoration: inherit !important; }

.bold_txt {
  font-weight: inherit !important; }

.center_element {
  margin: inherit !important; }

.italics_txt {
  font-style: inherit !important; }

.block_element {
  display: inherit !important; }

.img_rt {
  float: inherit !important;
  clear: inherit !important; }

.img_lft {
  float: inherit !important; }

table {
  width: 100%;
  border: none !important;
  border-collapse: collapse; }

td {
  padding: 0;
  border: 1px solid #000; }

#prelims {
  line-height: inherit !important; }

#prelims .char-style-override-18 {
  font-weight: inherit !important; }

#prelims .heading {
  font-size: inherit !important;
  color: inherit !important; }

#prelims .char-style-override-2 {
  font-style: inherit !important; }

#prelims .subheading {
  color: inherit !important; }

h2 {
  color: inherit !important;
  font-size: inherit !important;
  background: none !important;
  padding: 0 !important; }

.eHeading {
  color: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important; }

.ebox {
  border: none !important;
  padding: 0 !important; }

.mt-4 {
  margin-top: 2rem; }

.modal-header {
  position: relative;
  z-index: 9; }

.modal-footer {
  position: relative;
  z-index: 9; }

.test-text {
  display: none; }
#app-wrapper {
  padding: 1rem 0.5rem;
  border-bottom: 1px solid #ededed;
}
#app-wrapper .get-app {
  background: #F79420;
  color: #ffffff;
  font-size: 10px;
  padding: 0.3rem 0.5rem;
  outline: 0;
  border: none;
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: bold;
}
#app-wrapper h4 {
  font-size: 12px;
  margin-bottom: 0;
}
#app-wrapper p {
  font-size: 10px;
  margin-bottom: 0;
}
#app-wrapper button {
  border: none;
  background: none;
  outline: 0;
}
#app-wrapper button i {
  font-size: 16px;
}
.d-flex{
  display: flex;
}
.align-items-center{
  align-items: center;
}
.justify-content-between{
  justify-content:space-between;
}

/*# sourceMappingURL=eutkarsh.css.map */
