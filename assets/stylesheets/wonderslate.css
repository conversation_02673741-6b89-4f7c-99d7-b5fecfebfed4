.slow-transition, .header-shadow, .header-shadow .navbar-brand, .header-shadow .navbar-brand img, .header-shadow .navbar-right.header-menus .user-profile-dropdown, .header-shadow .navbar-right.header-menus .user-profile-dropdown > a::before, .header-shadow .navbar-right.header-menus .user-profile-dropdown > a::after, .header-shadow .header-menus .header-menu-item .header-menu-item-link, .header-shadow .header-menus .search-book .book-search-input, .header-shadow .header-menus .search-book .search-icon, header .navbar-right.header-menus .user-profile-dropdown, header .navbar-right.header-menus .user-profile-dropdown > a::before, .search-book, .search-book .book-search-input, .search-book .search-icon {
  -webkit-transition: all .3s ease;
  -moz-transition: all .3s ease;
  -ms-transition: all .3s ease;
  -o-transition: all .3s ease;
  transition: all .3s ease;
}

.header-shadow {
  box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1);
}
.header-shadow .navbar-brand {
  height: 45px;
}
.header-shadow .navbar-brand img {
  width: 25%;
}
.header-shadow .navbar-right.header-menus .user-profile-dropdown > a {
  padding: 30px 0 25px 0;
}
.header-shadow .navbar-right.header-menus .user-profile-dropdown > a::before {
  height: 38px;
  top: 20px;
}
.header-shadow .navbar-right.header-menus .user-profile-dropdown > a::after {
  height: 35px;
  top: 25px;
}
.header-shadow .header-menus .header-menu-item .header-menu-item-link {
  padding: 35px 0 25px 0;
  font-size: 20px;
}
.header-shadow .header-menus .search-book .book-search-input {
  margin: 20px 0 20px 0;
}
.header-shadow .header-menus .search-book .search-icon {
  top: 20px;
}

header {
  width: 100%;
  font-family: 'Montserrat', sans-serif;
  font-weight: normal;
  position: fixed;
  top: 0;
  left: 0;
  border-top: 2px solid #F05A2A;
  z-index: 5;
}
header .navbar-wonderslate {
  padding: 0 0 0 112px;
  font-weight: normal;
  background-color: #fff;
  border-radius: 0;
}
header .navbar-wonderslate .navbar-header .navbar-toggle {
  float: left;
}
header .navbar-wonderslate .navbar-header .navbar-toggle .icon-bar {
  background-color: #000;
}
header .navbar-brand {
  width: 210px;
  height: 75px;
  font-family: 'Exo';
  font-weight: 300;
  text-transform: uppercase;
  margin: 20px 0 20px 0;
  padding: 0;
  transition: all .3s ease;
}
header .navbar-brand:hover {
  color: #888;
}
header .navbar-brand img {
  width: 35%;
  max-width: 100%;
  display: inline-block;
  height: auto;
  transition: all .3s ease;
}
header .navbar-right.header-menus {
  padding-right: 160px;
  margin-left: 0;
}
header .navbar-right.header-menus .user-profile-dropdown .header-menu-item-link {
  padding-right: 10px;
  padding-left: 10px;
}
header .navbar-right.header-menus .user-profile-dropdown > a::before {
  content: '';
  width: 1px;
  height: 40px;
  background-color: rgba(68, 68, 68, 0.2);
  position: absolute;
  top: 40px;
  left: -12px;
  border: 1px solid rgba(68, 68, 68, 0.2);
}
header .navbar-right.header-menus .header-menu-item {
  margin-right: 20px;
}
header .navbar-right.header-menus .header-menu-item .login-signup-dropdown {
  min-width: 226px;
  text-align: center;
  margin-top: 0;
  right: -70px;
  padding: 26px 0 10px 0;
  border-radius: 4px;
}
header .navbar-right.header-menus .header-menu-item .login-signup-dropdown .login-btn {
  width: 130px;
  height: 50px;
  text-align: center;
  color: #5ec7d7;
  background: #FFFFFF;
  padding: 15px 20px;
  margin: 0 auto;
  margin-bottom: 15px;
  border: 1px solid #5EC7D7;
  border-radius: 4px;
}
header .navbar-right.header-menus .header-menu-item .login-signup-dropdown p {
  font-size: 12px;
}
header .navbar-right.header-menus .header-menu-item .login-signup-dropdown p a {
  color: #F05A2A;
}
header .navbar-right.header-menus .header-menu-item .loggedin-user {
  text-align: left;
  padding: 16px 16px 5px 16px;
}
header .navbar-right.header-menus .header-menu-item .loggedin-user .user-area {
  padding-bottom: 10px;
  margin-bottom: 5px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
header .navbar-right.header-menus .header-menu-item .loggedin-user .user-area p {
  margin-bottom: 5px;
}
header .navbar-right.header-menus .header-menu-item .loggedin-user .loggedin-user-name {
  font-size: 12px;
}
header .navbar-right.header-menus .header-menu-item .loggedin-user .user-name {
  font-weight: bold;
}
header .header-menus {
  margin-left: 80px;
}
header .header-menus .header-menu-item {
  margin-right: 40px;
}
header .header-menus .header-menu-item a:hover {
  color: #F05A2A;
  background-color: transparent;
}
header .header-menus .header-menu-item a:active {
  background-color: transparent;
}
header .header-menus .header-menu-item a:visited {
  background-color: transparent;
}
header .header-menus .header-menu-item a:focus {
  background-color: transparent;
  outline: none;
}
header .header-menus .header-menu-item a.active {
  border-bottom: 5px solid #F05A2A;
  color: #F05A2A;
}
header .header-menus .header-menu-item.open a {
  background-color: transparent;
  outline: none;
}
header .header-menus .header-menu-item a.header-menu-item-link {
  padding: 47px 0 44px 0;
  color: #888;
  font-size: 20px;
  transition: all .3s ease;
}
header .header-menus .header-menu-item a.header-menu-item-link:hover {
  color: #F05A2A;
  background-color: transparent;
}
header .header-menus .header-menu-item a.header-menu-item-link:active {
  background-color: transparent;
}
header .header-menus .header-menu-item a.header-menu-item-link:visited {
  background-color: transparent;
}
header .header-menus .header-menu-item a.header-menu-item-link:focus {
  background-color: transparent;
  outline: none;
}
header .header-menus .header-menu-item a.header-menu-item-link.active {
  border-bottom: 5px solid #F05A2A;
  color: #F05A2A;
}

.search-book {
  position: relative;
}
.search-book .book-search-input {
  max-width: 322px;
  height: 40px;
  border-radius: 0;
  padding-right: 30px;
  margin: 40px 0 40px 0;
  background-color: #f5f5f5;
  border: 1px solid rgba(68, 68, 68, 0.55);
  border-radius: 4px;
}
.search-book .book-search-input:focus {
  background-color: #fff;
  border: 1px solid #5EC7D7;
}
.search-book .typeahead {
  max-width: 225px;
  max-height: 290px;
  overflow: auto;
  top: 80px !important;
  padding: 0;
}
.search-book .typeahead li {
  margin-bottom: 5px;
  border-bottom: 1px solid #ccc;
  padding-bottom: 1px;
}
.search-book .typeahead li a {
  white-space: normal;
}
.search-book .typeahead li a strong {
  color: #F05A2A;
}
.search-book .typeahead li:last-of-type {
  margin: 0;
}
.search-book .typeahead li.active a {
  background-color: #2EBAC6;
}
.search-book .typeahead li.active a strong {
  color: #F05A2A;
}
.search-book .book-search-type-btn {
  position: absolute;
  height: 40px;
  border-radius: 0;
}
.search-book .search-icon {
  position: absolute;
  top: 40px;
  right: 15px;
  height: 40px;
  width: 40px;
  background-color: transparent;
  color: rgba(68, 68, 68, 0.74);
  box-shadow: none;
  border: 0;
  outline: none;
}

@media screen and (min-width: 320px) and (max-width: 767px) {
  header {
    position: relative;
  }
  header .navbar-wonderslate .navbar-header {
    margin-left: -15px !important;
    padding-bottom: 0;
  }
  header .navbar-wonderslate .navbar-header .navbar-brand {
    height: 50px;
    padding: 10px 0;
    margin: 0 auto;
    text-align: center;
    display: inline-block;
    position: relative;
    float: none;
    transition: all .3s ease;
  }
  header .navbar-wonderslate .navbar-header .navbar-brand img {
    width: 15%;
  }
  header .navbar-wonderslate .navbar-header .navbar-brand span {
    display: inline-block;
  }
  header .navbar-wonderslate .navbar-collapse {
    padding-right: 0;
    padding-left: 0;
    overflow-x: hidden;
  }
  header .navbar-wonderslate .header-menus.navbar-right {
    padding-right: 0;
  }
  header .navbar-wonderslate .header-menus {
    margin-left: 0;
  }
  header .navbar-wonderslate .header-menus .header-menu-item {
    margin-right: 0;
    margin-bottom: 5px;
    border-bottom: 1px solid #ccc;
  }
  header .navbar-wonderslate .header-menus .header-menu-item a {
    padding: 10px 0 10px 5px;
  }
}
.main-footer {
  background-color: #fff;
  padding: 40px 0;
  position: relative;
  z-index: 0;
}

.footer-container {
  width: 633px;
  padding: 0;
}
.footer-container ul li {
  margin-bottom: 10px;
}
.footer-container ul li p {
  font-size: 18px;
  font-weight: bold;
}
.footer-container ul li a {
  font-size: 18px;
  color: rgba(68, 68, 68, 0.74);
}

.footer-row {
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.74);
}
.footer-row .col-md-4 {
  padding: 0;
}

.store-icons {
  padding: 0;
  margin: 0;
  list-style: none;
}
.store-icons a {
  display: block;
  margin-bottom: 10px;
  text-indent: -99999px;
}
.store-icons .apple, .store-icons .google {
  width: 144px;
  height: 43px;
  background: url(../images/apple.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.store-icons .google {
  background: url(../images/googleplay.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.popular-books {
  padding: 0;
  margin: 0;
  list-style: none;
}

.important-links {
  padding: 0;
  margin: 0;
  list-style: none;
}

.footer-copy-rights {
  padding-top: 32px;
}
.footer-copy-rights .col-md-8 {
  padding: 0;
}
.footer-copy-rights .col-md-4 {
  padding: 0;
}

.social-icons {
  padding: 0;
  margin: 0;
  list-style: none;
  float: right;
}
.social-icons li {
  display: inline-block;
  margin-right: 8px;
}
.social-icons li a {
  width: 30px;
  height: 30px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: block;
  text-indent: -99999px;
}
.social-icons li .fb {
  background: url(../images/fb-vector.png);
  background-size: 100% 100%;
}
.social-icons li .fb:hover {
  background: url(../images/fb-vector-dark.png);
  background-size: 100% 100%;
}
.social-icons li .youtube {
  background: url(../images/youtube.png);
  background-size: 100% 100%;
}
.social-icons li .youtube:hover {
  background: url(../images/youtube-dark.png);
  background-size: 100% 100%;
}
.social-icons li .quora {
  background: url(../images/quora.png);
  background-size: 100% 100%;
}
.social-icons li .quora:hover {
  background: url(../images/quora-dark.png);
  background-size: 100% 100%;
}

@media screen and (min-width: 768px) and (max-width: 1100px) {
  header .navbar-wonderslate .navbar-header {
    margin-left: 10px;
  }
  header .header-menus {
    margin-left: 50px;
  }
  header .header-menus .header-menu-item {
    margin-right: 15px;
  }
  header .header-menus .header-menu-item a {
    font-size: 14px;
  }
  header .navbar-right.header-menus {
    padding-right: 0;
  }
}
@media screen and (min-width: 1100px) and (max-width: 1500px) {
  header .navbar-right.header-menus {
    padding-right: 60px;
  }
}
@media screen and (min-width: 320px) and (max-width: 768px) {
  .main-footer {
    padding: 20px 0;
  }
  .main-footer ul {
    padding: 0 10px 0 15px;
  }

  .footer-container {
    width: 100%;
  }

  .footer-row .col-md-4 {
    padding-left: 15px;
    padding-right: 15px;
  }

  .download-links .store-icons li:nth-child(1) {
    display: block;
  }
  .download-links .store-icons li:nth-child(2) {
    margin-right: 20px;
  }
  .download-links .store-icons li {
    display: inline-block;
  }

  .footer-copy-rights {
    padding-top: 10px;
  }
  .footer-copy-rights .col-md-8 {
    padding: 0 10px 0 15px;
  }
  .footer-copy-rights .col-xs-12 {
    text-align: center;
  }
  .footer-copy-rights .col-xs-12 p {
    font-size: 14px;
  }

  .social-icons {
    text-align: center;
    float: none;
  }
}
@media screen and (max-width: 767px) {
  header {
    position: relative;
  }
  header .navbar-wonderslate {
    padding: 0;
  }

  .book-info .book-preview .preview-links {
    padding: 5px;
  }

  .book-item {
    box-shadow: 0px 0px 1px rgba(0, 0, 0, 0.25) !important;
  }

  .search-book .book-search-input {
    width: 94%;
    max-width: 100%;
    margin: 15px 0 15px 5px;
  }
  .search-book #search-button {
    display: none;
  }

  .login-signup-form {
    width: 98%;
  }

  .form-inputs {
    border: 0;
    padding: 0 5px 0 5px;
  }
  .form-inputs .login-signup-input {
    width: 96%;
  }
  .form-inputs .show-password {
    right: 23px;
  }

  .submit-btn {
    width: 90%;
    margin: 15px 10px;
  }

  .already-user {
    text-align: center;
  }
  .already-user .have-an-account {
    float: none !important;
  }

  .forgot-password {
    text-align: center;
  }
}
.breadcrumbs {
  padding-top: 15px;
  margin-top: 0;
}

.btn:hover {
  color: #fff;
}

::-webkit-scrollbar {
  width: 7px;
}

::-webkit-scrollbar-track {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.05);
}

/*# sourceMappingURL=wonderslate.css.map */
