.publisher-cover-photo {
  position: relative;
  float: left;
}
.publisher-cover-photo:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #FFFFFF -1.23%, rgba(255, 255, 255, 0) 203.08%);
  transform: matrix(1, 0, 0, -1, 0, 0);
}

.publisher-profile {
  position: absolute;
  left: 36px;
  bottom: 36px;
}

.publisher-profile-img {
  display: inline-block;
  width: 100px;
  height: 100px;
  padding: 6px 12px;
}

.publisher-name {
  font-size: 40px;
  color: #444444;
  font-weight: 500;
}

.publisher-name-wrapper {
  display: inline-block;
  vertical-align: top;
  margin-left: 10px;
}

.publisher-contact {
  width: 96%;
  background: #fff;
  padding: 24px 0;
  margin-left: 15px;
}

.contact-details {
  list-style: none;
  padding: 0;
  margin: 0;
  float: left;
}
.contact-details li {
  font-size: 24px;
  font-weight: bold;
  color: #444;
  padding: 14px 0 14px 52px;
}
.contact-details .phone {
  background: url("../images/publisher/phone.png");
  background-position: 10px;
  background-repeat: no-repeat;
  background-size: 24px;
}
.contact-details .email {
  background: url("../images/publisher/email.png");
  background-position: 10px;
  background-repeat: no-repeat;
  background-size: 24px;
}
.contact-details .website {
  background: url("../images/publisher/website.png");
  background-position: 10px;
  background-repeat: no-repeat;
  background-size: 24px;
}
.contact-details .address {
  background: url("../images/publisher/map-icon.png");
  background-position: 10px;
  background-repeat: no-repeat;
  background-size: 24px;
}

.publisher-desc p {
  font-weight: 300;
  line-height: 29px;
  font-size: 20px;
  margin: 0;
}

.total-books {
  font-size: 24px;
}

/*# sourceMappingURL=publisher.css.map */
