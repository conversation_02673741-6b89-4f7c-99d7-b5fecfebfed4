.btn {
  font-weight: 500;
}

.sticky-header {
  position: relative;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: -90px;
  width: 100%;
  z-index: 9999;
  transition: 0.3s top cubic-bezier(0.3, 0.73, 0.3, 0.74);
}

.slideDown {
  top: 0;
}

.wonderslate-navbar {
  background-color: #FFFFFF;
  line-height: normal;
  padding: 12px 24px;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
  z-index: 3;
}
.wonderslate-navbar .navbar-container {
  padding-left: 0;
  padding-right: 0;
}
.wonderslate-navbar .navbar-container .navbar-nav.header-menus {
  margin-left: 100px;
}
.wonderslate-navbar .navbar-container .nav.navbar-nav.navbar-right li {
  margin-right: 8px;
}
.wonderslate-navbar .navbar-container .nav.navbar-nav.navbar-right li:last-child {
  margin-right: 0;
}
.wonderslate-navbar .navbar-container .login-btn {
  font-size: 14px;
  color: #4DDCE8;
  font-weight: 500;
  padding: 11px 25px;
  border-radius: 4px;
}
.wonderslate-navbar .navbar-container .signup-btn {
  font-size: 14px;
  display: block;
  text-align: center;
  font-weight: 500;
  color: #FFFFFF;
  background: linear-gradient(270deg, #2EBAC6 0%, #4DDCE8 100%);
  letter-spacing: 0.01em;
  padding: 11px 25px;
  border-radius: 4px;
}
.wonderslate-navbar .navbar-container .signup-btn:hover {
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.25);
}
.wonderslate-navbar .navbar-brand {
  width: 153px;
  height: 40px;
  background: url("../images/arivupro/logo.png");
  background-position: center;
  background-size: cover;
  display: block;
  text-indent: -9999999px;
}
.wonderslate-navbar ul a {
  font-size: 18px;
  font-weight: 300;
  color: rgba(68, 68, 68, 0.64);
  padding-top: 9px;
}
.wonderslate-navbar ul a:hover {
  background-color: #FFFFFF;
}
.wonderslate-navbar ul a.active {
  font-weight: 500;
  color: #FFFFFF;
  background: linear-gradient(270deg, #F05A2A 0%, #FF7245 100%);
  padding: 11px 15px;
  box-shadow: 1px 0px 0px rgba(68, 68, 68, 0.04);
  border-radius: 4px;
}

.user-logged-in {
  display: block;
  width: 40px;
  height: 40px;
  background-color: #fff !important;
  padding: 0 !important;
}
.user-logged-in img {
  max-width: 100% !important;
  height: auto;
  border: 1px solid #BDBDBD;
  border-radius: 100%;
}

.profile-dropdown {
  min-width: 330px;
  left: auto;
  right: -8px !important;
  padding: 24px;
  border-top-right-radius: 4px !important;
  border-top-left-radius: 4px !important;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  overflow-wrap: break-word;
}
.profile-dropdown:before {
  content: "";
  border-bottom: 10px solid #fff;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  position: absolute;
  top: -10px;
  right: 16px;
  z-index: 10;
}
.profile-dropdown:after {
  content: "";
  border-bottom: 12px solid #ccc;
  border-right: 12px solid transparent;
  border-left: 12px solid transparent;
  position: absolute;
  top: -12px;
  right: 14px;
  z-index: 9;
}
.profile-dropdown a {
  padding: 0;
}
.profile-dropdown a.text-zoom {
  width: 60px;
  height: 40px;
  font-size: 18px;
  font-weight: bold;
  padding: 6px 22px;
  border: 2px solid rgba(68, 68, 68, 0.54);
  border-radius: 4px;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  margin-right: 20px;
}
.profile-dropdown li {
  display: inline-block;
}

.user-image {
  position: relative;
  width: 72px;
  height: 72px;
  float: left;
  margin-right: 24px;
  text-align: center;
}
.user-image img {
  max-width: 100%;
  height: auto;
  border: 1px solid #BDBDBD;
  border-radius: 100%;
}
.user-image .user-edit-profile {
  color: #FFFFFF;
  font-size: 8px;
  position: absolute;
  bottom: 2px;
  left: 4px;
  padding: 8px 8px;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
  width: 90%;
  margin-bottom: 0;
  border-bottom-left-radius: 32px;
  border-bottom-right-radius: 32px;
}
.user-image .user-edit-profile:hover {
  color: #FFFFFF;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
}
.user-image .user-edit-profile:focus {
  color: #FFFFFF;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
}
.user-image .user-edit-profile:active {
  color: #FFFFFF;
  background: rgba(0, 40, 71, 0.64);
  text-decoration: none;
}

.logged-in-user-details {
  float: left;
  max-width: 166px;
}
.logged-in-user-details .loggedin-user-name {
  font-weight: 300;
}
.logged-in-user-details .loggedin-user-name .user-name {
  font-weight: 500;
  text-transform: capitalize;
}
.logged-in-user-details .loggedin-user-mobile, .logged-in-user-details .loggedin-user-email {
  font-weight: 300;
}

.user-orders {
  clear: both;
  float: left;
  width: 100%;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid rgba(68, 68, 68, 0.2);
  border-bottom: 1px solid rgba(68, 68, 68, 0.2);
}
.user-orders a {
  color: #444;
}
.user-orders a:hover {
  color: #444;
  text-decoration: none;
}
.user-orders a:active {
  color: #444;
  text-decoration: none;
}
.user-orders a:focus {
  color: #444;
  text-decoration: none;
}

.user-logout {
  clear: both;
  float: left;
  width: 100%;
  margin-top: 16px;
}
.user-logout p {
  font-weight: 300;
  color: #000;
  margin: 0;
}
.user-logout p a {
  font-weight: 400;
  color: #F05A2A;
}

.progress-wrapper {
  max-width: 512px;
  min-height: calc(100vh - 60px);
  background-color: #FFFFFF;
  padding: 0;
}

.progress-content-wrapper {
  padding: 32px;
  margin: 0;
}

.back-module {
  display: inline-block;
  vertical-align: top;
}

.module-name-label {
  background-color: #FFFFFF;
  padding: 16px;
  margin: 0;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0.24), 0px 4px 3px rgba(0, 0, 0, 0.12);
}

.module-label {
  display: inline-block;
  vertical-align: top;
  font-family: Rubik;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 20px;
  letter-spacing: 0.01em;
  margin: 0;
  margin-left: 16px;
}

.progress-list-wrapper {
  position: relative;
  list-style: none;
  padding: 0;
  margin: 0;
  margin-bottom: 32px;
}
.progress-list-wrapper > li {
  display: inline-block;
}
.progress-list-wrapper > li:nth-child(2n+2) {
  float: right;
}
.progress-list-wrapper > li:nth-child(3n+3) {
  float: left;
}
.progress-list-wrapper > li:nth-child(odd) .module-locked:after {
  content: '';
  position: absolute;
  left: 100%;
  top: 36px;
  width: 167px;
  height: 2px;
  border: 1px dashed rgba(86, 62, 88, 0.48);
  border-bottom: 0;
  border-right: 0;
  border-left: 0;
  border-width: 2px;
}
.progress-list-wrapper > li:last-child .module-locked:after {
  border: 0;
}

.progress-list-wrapper:nth-child(odd):after {
  content: '';
  position: absolute;
  left: 100%;
  top: 36px;
  width: 16px;
  height: 144px;
  border: 1px dashed rgba(86, 62, 88, 0.48);
  border-top-right-radius: 24px;
  border-bottom-right-radius: 24px;
  border-left: 0;
  border-width: 2px;
}

.progress-list-wrapper:nth-child(even):before {
  content: '';
  position: absolute;
  right: 100%;
  top: 36px;
  width: 16px;
  height: 144px;
  border: 1px dashed rgba(86, 62, 88, 0.48);
  border-top-left-radius: 24px;
  border-bottom-left-radius: 24px;
  border-right: 0;
  border-width: 2px;
}

.progress-list-wrapper:last-child:before {
  border: 0;
}

.progress-list-wrapper:last-child:after {
  border: 0;
}

.progress-list-item {
  position: relative;
  clear: both;
  display: inline-block;
  width: 72px;
  height: 72px;
  color: #FFFFFF;
  text-align: center;
  padding-top: 16px;
  border-radius: 16px;
}
.progress-list-item > i {
  font-size: 40px;
}

.module-locked {
  background: linear-gradient(135deg, #C4C4C4 0%, #D9D9D9 100%);
}

.module-current {
  width: 74px;
  height: 74px;
  background: linear-gradient(135deg, #CD8E0C 0%, #FFC541 100%);
  border: 2px solid #FFFFFF;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.25);
}

.module-finished {
  background: linear-gradient(135deg, #3EB890 0%, #5CD6AE 100%);
}
.module-finished:after {
  content: '';
  position: absolute;
  left: 100%;
  top: 36px;
  width: 167px;
  height: 4px;
  background-color: #55CFA7;
}

.module-name {
  font-family: Work Sans;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 12px;
  text-align: center;
  color: #000000;
  margin-top: 8px;
}

.active-module-name {
  background: #FFFFFF;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12);
}

@media only screen and (min-width: 768px) {
  body {
    padding-top: 0px;
  }

  .module-finished:after {
    width: 304px;
  }

  .progress-list-wrapper > li:nth-child(odd) .module-locked:after {
    width: 304px;
  }

  .module-name-label {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
  }

  .module-name-wrapper {
    float: none;
    max-width: 532px;
    margin: 0 auto;
  }
}
@media only screen and (max-width: 768px) {
  .module-name-label {
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.24), 0px 0px 4px rgba(0, 0, 0, 0.12);
  }
}
@media only screen and (min-width: 400px) and (max-width: 678px) {
  .module-finished:after {
    width: 205px;
  }

  .progress-list-wrapper > li:nth-child(odd) .module-locked:after {
    width: 205px;
  }
}
.user-book-wrapper {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-book-data-wrapper {
  position: relative;
  list-style: none;
  float: left;
  width: 50%;
  padding-right: 16px;
  padding-left: 16px;
  margin-bottom: 16px;
}

.user-book-data-wrapper:nth-child(2n+2) {
  float: right;
}
.user-book-data-wrapper:nth-child(2n+2) .user-book-data {
  min-height: 336px;
}
.user-book-data-wrapper:nth-child(2n+2) .user-book-image {
  height: 168px;
  max-height: 100%;
}
.user-book-data-wrapper:nth-child(2n+2) .user-book-image > img {
  height: 168px;
  max-height: 100%;
}

.user-book-data {
  position: relative;
  float: left;
  width: 100%;
  clear: both;
  border-radius: 4px;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.24), 0px 0px 2px rgba(0, 0, 0, 0.12);
}

.user-book-image {
  clear: both;
  max-height: 128px;
}
.user-book-image > img {
  width: 100%;
  max-height: 128px;
}

.user-book-name-desc {
  float: left;
  width: 100%;
  clear: both;
  text-align: center;
  padding: 32px 12px 16px 12px;
}

.user-book-name {
  font-family: Rubik;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 10px;
  text-align: center;
  letter-spacing: 0.24em;
  text-transform: uppercase;
  color: #2EBAC6;
}

.user-book-desc-wrapper {
  float: left;
  width: 100%;
  text-align: center;
}

.user-book-desc {
  font-family: Rubik;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 16px;
  text-align: center;
  color: #444444;
  padding: 8px;
}

.total-videos {
  font-family: Rubik;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  font-size: 14px;
  text-align: center;
  color: #2EBAC6;
}
.total-videos > i {
  vertical-align: bottom;
  font-size: 18px;
}

.user-book-locked {
  filter: blur(3px);
}

.user-book-locked-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(240, 240, 240, 0.54);
}

.book-locked-img-wrapper {
  width: 48px;
  height: 48px;
  background: url("../images/arivupro/book-locked.png");
  background-repeat: no-repeat;
  background-size: 60%;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  top: 45%;
  transform: translateY(-50%);
  background-color: #fff;
  border-radius: 100%;
  background-position: center;
}

.mobile-bottom-menu-item {
  float: left;
  width: 33.33%;
  font-weight: 500;
  line-height: normal;
  font-size: 11px;
  text-align: center;
  letter-spacing: 0.01em;
}

.mobile-bottom-menu-item-link {
  display: block;
  margin: 0 auto 4px;
  text-indent: -9999999px;
}

@media screen and (min-width: 768px) {
  .mobile-bottom-menu-wrapper {
    list-style: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    text-align: center;
    background-color: #FFFFFF;
    padding: 6px 0;
    margin: 0;
    box-shadow: 0px -1px 8px rgba(0, 0, 0, 0.25);
    z-index: 5;
  }

  .mobile-bottom-menu-item {
    float: none;
    display: inline-block;
    width: 216px;
    font-weight: 500;
    line-height: normal;
    font-size: 11px;
    text-align: center;
    letter-spacing: 0.01em;
  }
}
@media screen and (max-width: 768px) {
  .mobile-bottom-menu-wrapper {
    list-style: none;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #FFFFFF;
    padding: 6px 0;
    margin: 0;
    box-shadow: 0px -1px 8px rgba(0, 0, 0, 0.25);
    z-index: 5;
  }
}
.mobile-store {
  width: 28px;
  height: 28px;
  background: url(../images/arivupro/learn_inactive.svg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mobile-store.active {
  width: 28px;
  height: 28px;
  background: url(../images/arivupro/learn_active.svg) !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
}

.mobile-library {
  width: 28px;
  height: 28px;
  background: url(../images/arivupro/leaderboard_inactive.svg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mobile-library.active {
  width: 28px;
  height: 28px;
  background: url(../images/arivupro/leaderboard_active.svg) !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
}

.mobile-test-gen {
  width: 28px;
  height: 28px;
  background: url(../images/arivupro/you_inactive.svg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.mobile-test-gen.active {
  width: 28px;
  height: 28px;
  background: url(../images/arivupro/you_active.svg) !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
}

/*# sourceMappingURL=arivupro.css.map */
