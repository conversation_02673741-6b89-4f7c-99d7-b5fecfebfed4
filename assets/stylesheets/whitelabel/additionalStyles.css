.banner_wrap {
    width: 90%;
    margin: 0 auto;
}
.cuetAcademics {
    width: 90%;
    margin: 10px auto !important;
}
.cuetAcademics, .LibWonder .ws-header {
    background: transparent;
    box-shadow: none;
}
.libwonder header.LibWonder {
    border-bottom: none;
}
.cuetAcademics .curve-bg {
    position: absolute;
    top: -30px;
    right: 0;
}
.cuetAcademics .banner_wrap .banner_img {
    margin-top: -7rem;
}
.cuetAcademics, .LibWonder .ws-header{
    min-height: 75vh;
}

.titleRowOne{
    font-weight: 400;
    font-size: 2rem;
}
.titleRowTwo{
    font-size: 4rem;
    font-weight: bold;
    line-height: 60px;
}
.titleRowThree{
    margin-bottom: 22px;
    letter-spacing: 1px;
    font-size: 22px !important;
    line-height: 34px;
}
.titleRowOne,
.titleRowTwo,
.titleRowThree{
    font-family: inherit !important;
}
.cuetLogin{
    display: block;
    width: 200px;
    padding: 12px;
    border-radius: 100px;
    background: #000;
    color: #fff !important;
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cuetAcademics .banner_wrap .banner_img {
        margin-top: -5rem;
    }
}
@media only screen and (max-width: 767px) {
    .cuetAcademics .banner_wrap .banner_img {
        margin-top: 4rem;
        margin-bottom: -40px;
    }
    .cuetAcademics{
        width: 100%;
        padding: 0 !important;
    }
    .banner_info,
    .banner_wrap{
        padding: 0!important;
    }
}
.cuetAcademics .banner_wrap .banner_img img {
    width: 100%;
}


.educart__footer{
    min-height: 300px;
    background: #000 !important;
}
.footer_connect{
    font-size: 1.2rem;
    font-weight: bold !important;
    position: relative;
    color: #fff;
}
.educart__footer-info{
    display: flex;
    justify-content: space-around;
    align-items: center;
}
.footer_contactInfo{
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-top: 1.6rem;
}
.footer_contactInfo .fa-solid{
    color: #c4bebe !important;
    margin-right: 5px;
}
.footer_contactInfo a{
    color: #c4bebe !important
}
.educart__footer-info__content.top{
    display: none;
}
.educart__footer-info__content.bottom{
    display: flex;
    flex-direction: column;
}
.educart__footer-info__content ul{
    list-style: none;
    display: flex;
    gap: 10px;
    justify-content: center;
}
.educart__footer-info__content ul li img{
    width: 35px !important;
}

.horLine,
.horLineOne{
    color: #fff;
    background: #fff;
    width: calc(100% - 20%);
    margin: 1rem auto;
}
.horLineOne{
   display: none;
}
.educart__footer-about{
    width: calc(100% - 20%);
    margin: 0 auto;
}
.educart__footer-about__title{
    font-size: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
    color: #fff;
}
.copy-right-text-footer{
    color: grey;
}
.educart__footer-about__content{
    margin-bottom: 1rem;
    color: #fff;
}
.educart__footer-legal{
    text-align: center;
}
.catList{
    flex-direction: column;
    padding-left: 0;
    list-style: unset !important;
}
.catList  li{
    color: #fff;
}
.catList li a{
    color: #fff;
}
.footer_connect:after{
    content: '';
    position: absolute;
    width: 70%;
    height: 1px;
    background: rgba(255, 255, 255, 0.3);
    top: 30px;
    left: 0%;
}
.footerLogo{
    width: 123px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px){
    .educart__footer{
        padding-bottom: 6rem;
    }
    .educart__footer-info__content.top{
        display: flex;
        justify-content: center;
        margin-top: 1rem;
    }
    .educart__footer-info__content.top ul{
        margin-bottom: 1.5rem;
    }
    .educart__footer-info__content.bottom{
        display: none;
    }
    .educart__footer-info{
        flex-direction: column;
        align-items: unset;
        gap: 15px;
        width: calc(100% - 20%);
        margin: 0 auto;
    }
    .footerLogo{
        display: flex;
        margin: 0 auto;
        width: 123px;
    }
    .footer_connect{
        text-align: center;
    }
    .footer_connect:after{
        width: 50%;
        left: 26%;
    }

    .horLineOne{
        width: calc(100%);
        display: block;
    }
}
@media only screen and (max-width: 767px) {
    .cuetAcademics .curve-bg {
        display: none;
    }
}
.cuetAcademics .curve-bg img {
    width: 800px;
}
@media only screen and (min-width: 1201px) and (max-width: 1300px) {
    .cuetAcademics .curve-bg img {
        width: 700px;
    }
}
@media only screen and (min-width: 992px) and (max-width: 1200px) {
    .cuetAcademics .curve-bg img {
        width: 600px;
    }
}
@media only screen and (min-width: 768px) and (max-width: 991px) {
    .cuetAcademics .curve-bg img {
        width: 500px;
    }
}
@media only screen and (max-width: 1200px) {
    .banner_wrap {
        width: 100%;
    }
    .LibWonder {
        width: 100%;
        margin: 0 auto !important;
    }
}