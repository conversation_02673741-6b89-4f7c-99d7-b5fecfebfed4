/* Modified Styles */

.header-wrapper{
  width:100%;
}
.main-menu-wrp{
  position: fixed;
  padding: 10px 0;
  border-bottom: 1px solid #dadada54;
  -webkit-transition: all 400ms ease-in-out;
  transition-timing-function: ease-in-out;
  width: 100%;
  z-index: 999;
  height: 78px;
  top: 0;
}
.user-menu-wrp {
  display: inline-block;
  float: left;
}
.user-menu-wrp a.menu-dots-img-wrp {
    display: inline-block;
    padding: 14px 14px;
}
.user-menu-wrp img.menu-dots-img{
  width:24px;
  height:24px;
}
.menu-main-min-height{
  min-height: 112px;
}
.menu-wrp-all-users-com ul{
  padding:0;
  margin:0;
  float:right;
}
.menu-wrp-all-users-com ul li{
  list-style: none;
  float: left;
}
.menu-wrp-all-users-com ul li a{
  font-weight: normal;
  color: #fff;
  font-size: 18px;
  text-transform: uppercase;
  padding: 15px;
  letter-spacing: 1px;
  display: inline-block;
}
.menu-wrp-all-users-com ul li a:hover{
  text-decoration: none;
}
.menu-wrp-all-users-com ul li a img{
  width: 20px;
  height: 20px;
  margin-bottom: 6px;
  margin-right: 2px;
}
.menu-wrp-all-users-com ul li .dropdown-menu a {
  color: initial;
  font-size: 14px;
  padding: 4px 14px;
  text-transform: capitalize;
  letter-spacing: normal;
}
li.none-add-class-responsive.display-none-now a {
    display: none;
}

/*========================================================
                      Categories Section
==========================================================*/
.categories-section{
  padding: 40px 0 30px;
  background-size: cover;
  height: auto;
  min-height: 800px;
}
.border-title-categories {
  border-bottom: 1px solid #b9b9b9;
}
h2.title-categories-section{
  display: inline-block;
  margin: 0;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 55px;
  color: #000;
}
h2.title-categories-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #000;
  height: 1px;
  top: 100%;
  left: 0;
}
.categories-section-disc-title-pera{
  color: #939797;
  text-align: center;
  font-size:16px;
  padding-top: 18px;
  margin-bottom: 1rem;
}
.categories-section .responsive-padding-none {
  padding-left: 7px;
  padding-right: 7px;
}
#ebookCategories .responsive-padding-none {
  display: flex;
  flex-direction: column;
  margin-bottom:15px;
}
#ebookCategories .responsive-padding-none:nth-child(even) a {
  background-color: #ff9b26;
}
#ebookCategories .responsive-padding-none:nth-child(even) a:hover {
  background: #f38c13;
}
.margin-row-categories-rowss{
  margin-top:10px;
}
.box-categories-wrp{
  width: 100%;
  height: 100%;
}
.box-categories-wrp a{
  color: #fff;
  display: inline-block;
  text-align: center;
  border-radius: 14px;
  width: 100%;
  height: 100%;
  margin-bottom: 15px;
  font-weight: 400;
  font-size: 28px;
  line-height: 40px;
  padding: 40px 15px;
  font-family: 'Fira Sans', sans-serif;
}
.box-categories-wrp a:hover{
  text-decoration: none;
}

.bg-color-fill-categories{
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
}
.responsive-padding-none {
    padding-left: 7px;
    padding-right: 7px;
}
.bg-color-fill-categories h3{
  padding:45px 0;
  text-align: center;
  font-weight: 400;
  font-size: 26px;
  line-height: 40px;
  color: #fff;
}

.line-box-category{
  width: 70%;
  margin: 0 auto;
  background: #fff;
  height: 2px;
  border-radius: 5px;
}
.red-color-fill-bg{
  background:#ff3448;
}
.red-color-fill-bg:hover{
  background: #ea1d32;
}
.yellow-color-fill-bg{
  background: #ff9b26;
}
.yellow-color-fill-bg:hover{
  background: #f38c13;
}

/*========================================================
                      Connect Section
==========================================================*/
.connect-section {
    padding: 30px 0;
    transition-property: all;
    transition-duration: 400ms;
    -webkit-transition: all 400ms ease-in-out;
    transition-timing-function: ease-in-out;
    background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
.border-title-coonect{
  border-bottom: 1px solid #f5f5f5;
}
.title-connect-section{
  display: inline-block;
  margin: 0;
  position: relative;
  font-family: 'Oswald', sans-serif;
  padding-bottom: 14px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 55px;
  color: #fff;
}
.title-connect-section:after{
  position: absolute;
  content: "";
  width: 100%;
  background: #ffffff;
  height: 2px;
  top: 99%;
  left: 0;
}
p.connect-sec-wrp-disc-pera{
  color: #ffffff;
  font-size: 16px;
  font-weight: 300;
  margin-top:20px;
  padding: 0 15px;
  text-align: center;
  margin-bottom: 1rem;
}
ul.social-icon-wrp-connect{
  padding: 0;
  border-right: 1px solid #fff;
  margin:0;
  float: right;
}
ul.social-icon-wrp-connect li{
  list-style: none;
  float: left;
  width: 22%;
  font-size: 33px;
  text-align: center; 
}
ul.social-icon-wrp-connect li a{
  color:#fff;
  padding:0 20px;
  font-size: 30px;
}
h3.call-here-number{
  font-size:24px;
  margin-top: 0;
  padding:0 0 6px;
}
h3.call-here-number a{
  color:#fff;
  font-size: 22px;
}
h3.call-here-number a:hover{
  text-decoration: none;
}
.mrgn-top-connect-sec{
  margin-top:12px;
}

/*==============================================================================================
                                              FOOTER
================================================================================================*/
.footer{
  background: #eee;
  padding: 30px 0 0;
  z-index: initial;
}
.image-wrapper-footer-logo{
  text-align: center;
}
.image-wrapper-footer-logo img{
  width:110px;
  height: auto;
}
h3.footer-link-title{
  font-size: 14px;
  font-weight: 700;
  color: #000;
}
ul.link-ul-footer{
  padding:0;
  margin:0;
}
ul.link-ul-footer li{
  list-style: none;
}
ul.link-ul-footer li a{
  color: #000;
  display: block;
  padding: 6px 0 0;
  font-size: 13px;
  font-weight: 400;
}
ul.link-ul-footer li a:hover{
  text-decoration: none;
}
.text-center-align-here{
  text-align: center;
  width:100%;
  margin-top: 6px;
}
.there-social-footer-link-wrp{
  padding:0;
  margin:0;
  display: inline-block;
  text-align: center;
}
.there-social-footer-link-wrp li{
  float: left;
  list-style: none;
}
.there-social-footer-link-wrp li a{
  padding:4px 10px;
  display: inline-block;
  color:#979a9b;
  -ms-transition: 0.3s all ease-in-out;
  -o-transition: 0.3s all ease-in-out;
  -moz-transition: 0.3s all ease-in-out;
  -webkit-transition: 0.3s all ease-in-out;
  transition: 0.3s all ease-in-out;
  font-size: 19px;
}
.there-social-footer-link-wrp li a:hover{
  color:#EB7215;
}

/*==================================================================================================
                                      Copy Right
===================================================================================================*/
.footer-copyright{
  background: #ffffff;
}
.footer-copyright p.copy-right-text-footer{
  color:#000;
  margin:0;
  font-weight: 500;
  padding:6px 0;
  text-align: center;
  font-size:14px;
}
.overlay-menu-close{
  position: fixed;
  background: #00000059;
  width: 100%;
  height: 100vh;
  left: 0;
  top: 0;
  display: none;
  z-index: 999;
  transition: 0.3s all ease-in-out;
}

/*========================================================================
                        Add New Style 
=========================================================================*/
a.menu-dots-img-wrp:hover,
a.menu-dots-img-wrp:focus,
a.menu-dots-img-wrp:active{
  text-decoration: none;
}
a.menu-dots-img-wrp img.menu-close-btn-menub {
    width: 24px;
    height: 24px;
}
a.menu-dots-img-wrp .menu-close-btn-menub{
  display: none;
}
.wrp-new-posi-changes-whitelabel.menu-actives .menu-close-btn-menub{
  display: block;
}
.wrp-new-posi-changes-whitelabel.menu-actives .menu-dots-img{
  display: none;
}
.wrp-new-posi-changes-whitelabel.menu-actives .main-menu-wrp-whitelabel-big{
  display: block;
}
.menu-overlay-big-menus.actv{
  position: fixed;
  height: 100vh;
  width: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 9;
  top: 0;
  left: 0;
}
.wrp-new-posi-changes-whitelabel{
  position: relative;
  z-index: 99999;
}
.main-menu-wrp-whitelabel-big.menu-showing {
  display: block;
}
.index-page .main-menu-wrp-whitelabel-big {
  position: absolute;
}
.main-menu-wrp-whitelabel-big {
  position: fixed;
  width: 95%;
  display: none;
  top: 70px;
  left: 0;
  right: 0;
  z-index: 1000;
  float: left;
  min-width: 1020px;
  margin: 0 auto 7px;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
  box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.main-menu-wrp-whitelabel-big .row .col-3:first-child{
  background: #fafafa;
  padding: 0;
}
.manage-logo-big-menu-whitelabel{
  text-align: center;
  padding:20px 0;
}
.whitelabel-big-menu-side-wrp{
  padding: 20px 10px 20px 40px;
  background: #fff;
  height: auto;
}
.manage-row-fluides-menu-big{
  margin-bottom: 15px;
}
ul.manage-with-all-links-big-menus.manage-wrp-secting-to-top {
    padding-top: 35px;
}
ul.manage-with-all-links-big-menus{
  padding:0;
  margin: 0;
}
ul.manage-with-all-links-big-menus h4{
  font-size: 16px;
  color: #000;
  position: relative;
  display: inline-block;;
  padding-bottom: 8px;
  margin-bottom: 12px;
}
ul.manage-with-all-links-big-menus.manage-top-manages-big-menusuls {
    margin-top: 40px;
}
ul.manage-with-all-links-big-menus h4:after{
  position: absolute;
  content: '';
  left: 0;
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
  width: 100%;
  height: 1.5px;
  bottom: 0;
}
ul.manage-with-all-links-big-menus li{
  list-style: none;
  line-height: 1.2;
  padding-bottom: 8px;
}
ul.manage-with-all-links-big-menus li a{
  text-decoration: none;
  color: #000;
  font-size: 13px;
}
ul.manage-with-all-links-big-menus li a:hover,
ul.manage-with-all-links-big-menus li a:active,
ul.manage-with-all-links-big-menus li a:focus{
  text-decoration: underline;
}
ul.this-is-side-wrp-ul-big-menu-whitelabel {
  width: 100%;
  margin: 0;
  padding: 0;
  padding-bottom: 14px;
  border-bottom: 1px solid #ccc;
  margin-bottom: 14px;
}
ul.this-is-side-wrp-ul-big-menu-whitelabel li{
  list-style: none;
}
ul.this-is-side-wrp-ul-big-menu-whitelabel li a {
    padding: 7px 0 7px 40px;
    display: block;
    color: #000000;
}
ul.this-is-side-wrp-ul-big-menu-whitelabel li.active-menuss{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
ul.this-is-side-wrp-ul-big-menu-whitelabel li.active-menuss a{
  color: #fff;
}
ul.this-is-side-wrp-ul-big-menu-whitelabel li.active-menuss a:hover,
ul.this-is-side-wrp-ul-big-menu-whitelabel li.active-menuss a:focus,
ul.this-is-side-wrp-ul-big-menu-whitelabel li.active-menuss a:active,
ul.this-is-side-wrp-ul-big-menu-whitelabel li a:hover,
ul.this-is-side-wrp-ul-big-menu-whitelabel li a:focus,
ul.this-is-side-wrp-ul-big-menu-whitelabel li a:active{
  text-decoration: none;
}
ul.this-is-side-wrp-ul-big-menu-whitelabel li:hover{
  background: linear-gradient(90deg,#FF3448 0%,#FF9B26 100%);
}
ul.this-is-side-wrp-ul-big-menu-whitelabel li:hover a{
  color: #fff;
}
a.white-logo-anchor-white {
  padding: 3px 0;
  display: inline-block;
}
.main-menu-wrp-whitelabel-big .whitelabel-big-menu-side-wrp .book-wrapper-sec-part-main-menu-big {
  overflow-y: scroll;
  max-height: 80vh;
  overflow-x: hidden;
}

/*========================================================================
                        Responsive Styles
=========================================================================*/
@media(max-width:768px){
  *{
    padding:0;
    margin:0;
  }
  .overlay-menu-close.menu-ovr-act{
    z-index: 999;
    display: block;
    transition: 0.3s all ease-in-out;
  }
  .main-menu-wrp{
    padding:10px 0;
    height: 70px;
  }
  a.white-logo-anchor-white {
    width: 35px;
    padding: 5px 0;
  }
  .menu-wrp-all-users-com ul li a{
    padding: 13px 6px;
    font-size: 15px;
  }
  .user-menu-wrp img.menu-dots-img {
    width: 20px;
    height: 20px;
  }
  a.white-logo-anchor-white img {
    width: 100%;
  }
  li.none-add-class-responsive.display-none-now a{
    display: none;
  }
  ul.social-icon-wrp-connect{
    width:100%;
    border:none;
  }
  .responsive-center-text{
    text-align: center;
  }
  .img-wrp-call-in,h3.call-here-number{
    float: none;
    display: inline-block;
  }
  .image-wrapper-footer-logo {
    margin-bottom: 10px;
  }
  .image-wrapper-footer-logo img {
    width: 80px;
  }
  .main-div-box-link-footer {
    margin-bottom: 20px;
  }
  #ebookCategories .responsive-padding-none {
    margin-bottom: 0;
  }
  .box-categories-wrp a{
    padding: 25px 15px;
    margin-bottom: 0;
    display: block;
  }
  .bg-color-fill-categories h3{
    padding: 16px 0;
    font-weight: 300;
    line-height: 32px;
    font-size: 20px;
  }
  .title-connect-section {
    font-size: 45px;
  }
  a.bg-color-fill-categories.yellow-color-fill-bg.responsive-red-bg:hover{
    background: #ea1d32;
  }
  a.bg-color-fill-categories.red-color-fill-bg.responsive-yellow-bg{
    background: #f38c13;
  }
  .bg-color-fill-categories h3 a{
    font-size: 22px;
    padding:40px 1px;
  }
  .categories-section{
    background: none;
    min-height: auto;
  }
  h2.title-categories-section {
    font-size: 40px;
  }

  p.categories-section-disc-title-pera{
    font-size: 15px;
    text-align: left;
  }
  p.categories-section-disc-title-pera br {
    display: none;
  }
  .col-lg-4.col-sm-6.col-6.responsive-padding-none {
    padding: 0 !important;
  }
  .bg-color-fill-categories{
    border-radius: 0 !important;
    margin:0;
  }
  .display-none-responsive{
    display: none;
  }
  p.connect-sec-wrp-disc-pera{
    font-size: 15px;
  }
  p.connect-sec-wrp-disc-pera br {
    display: none;
  }
  .center-responsive-div{
    text-align: center;
  }
  ul.social-icon-wrp-connect{
    display: inline-block;
    margin: 0 auto;
    width:auto;
    float:none;
  }
  ul.social-icon-wrp-connect li{
    width:auto;
  }
  ul.social-icon-wrp-connect li a {
    padding: 15px 10px;
    display: inline-block;
  }
  .responsive-view-none{
    padding-top: 20px;
  }
  .responsive-none-view .text-center-align-here {
    border-bottom: 1px solid #ccc;
  }
  .posi-static-respons{
    position: static;
  }
  .connect-section{
    position: relative;
  }
  .main-menu-wrp.search-active-now-row.now-hide-menuall .user-menu-wrp{
    pointer-events: none;
  }
  .menu-main-min-height{
    min-height: 70px;
  }
  div#wrapper.wrp-cart{
    background: #f1f2f4;
  }
  div#wrapper.wrp-cart .footer{
    background: #fff;
  }
  div#wrapper.wrp-cart .footer-copyright{
    background: #f1f2f4;
  }
  .user-menu-wrp a.menu-dots-img-wrp{
    padding: 14px 14px 14px 4px;
  }
  .wrp-new-posi-changes-whitelabel.menu-actives a.menu-dots-img-wrp {
    background: #8b8b8b;
    z-index: 9999;
    position: fixed;
    padding: 0 !important;
    border-radius: 50%;
    top: 10px;
    left: 5px;
  }
  .main-menu-wrp-whitelabel-big.menu-showing {
    position: fixed;
  }
  .whitelabel-site header {
    position: relative;
    z-index: 999;
  }
  .whitelabel-site .modal-backdrop {
    z-index: 9991;
  }
  ul.this-is-side-wrp-ul-big-menu-whitelabel li a {
    padding: 7px 7px 7px 20px;
  }
  .footer {
    padding: 15px 0 0;
  }
  .footer-copyright p.copy-right-text-footer {
    font-size: 12px;
    padding-bottom: 75px;
  }
  .main-menu-wrp-whitelabel-big {
    position: fixed;
    top: 0;
    left: 0;
    min-width: 100%;
    margin: 0;
    border: 0;
    overflow: scroll;
    height: 100vh;
  }
  .whitelabel-big-menu-side-wrp .book-wrapper-sec-part-main-menu-big {
    padding-left: 20px;
    padding-bottom: 50px;
  }
  .whitelabel-site .bookTemplate .export-notes {
    top: 96px !important;
  }
  .whitelabel-site .web-mcq .sub-header,
  .whitelabel-site .web-mcq .result-menu {
    top: 70px !important;
  }
  .whitelabel-site .web-mcq .que-side-menu .close-menu span {
    display: inline-block;
  }
  .whitelabel-site .play::before {
    top: -10px;
  }
  .whitelabel-site .all-container .container-wrapper .media {
    padding: 1.5rem 1rem .5rem;
  }
  .whitelabel-site #htmlContent .pr-back-btn {
    top: -5px;
  }
}
@media(max-width:560px){
  .whitelabel-site .bookTemplate .content-wrapper #book-sidebar .mobile-title {
    margin-top: 70px;
  }
}
@media screen and (min-device-width: 2400px) and (max-device-width: 2599px) {
  .bg-wrapper-sec{
    min-height:1400px;
  }
  .categories-section .container {
    min-width: 2200px;
  }
  .box-categories-wrp {
    width: 90%;
    margin: 30px auto 0;
  }
}
@media screen and (min-device-width: 2600px) and (max-device-width: 2880px) {
  .bg-wrapper-sec{
    min-height:1700px;
  }
  .categories-section .container {
    min-width: 2200px;
  }
  .box-categories-wrp {
    width: 90%;
    margin: 30px auto 0;
  }
}
@media screen and (min-device-width: 4000px) and (max-device-width: 4100px) {
  .bg-wrapper-sec{
    min-height:2200px;
  }
  .categories-section .container {
    min-width: 2200px;
  }
  .box-categories-wrp {
    width: 90%;
    margin: 30px auto 0;
  }
}
@media only screen and (min-device-width: 320px) and (max-device-width: 480px) {
  .whitelabel-site .bookTemplate .shadowHeader {
    top: 45px;
  }
  .whitelabel-site .bookTemplate .tab-header .nav-tabs {
    margin-top: 0;
  }
}
@media (min-width: 576px){
  .modal-dialog {
    max-width: 500px;
    margin: 4.75rem auto;
  }
}

