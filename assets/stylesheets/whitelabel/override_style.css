/*========================================================================
                        Login/Signup Styles
=========================================================================*/
#loginOpen,
#signup,
#forgotPasswordmodal {
  -webkit-font-smoothing: antialiased;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #loginOpen .modal-dialog,
  #signup .modal-dialog,
  #forgotPasswordmodal .modal-dialog {
    align-items: flex-start;
    margin: 0;
    border-radius: 0;
    height: 100vh;
  }
}
#loginOpen .modal-header,
#signup .modal-header,
#forgotPasswordmodal .modal-header {
  display: flex;
  align-items: center;
  font-family: 'Poppins', sans-serif !important;
  padding-bottom: 0;
}
#loginOpen .head-title,
#signup .head-title,
#forgotPasswordmodal .head-title {
  color: #212121;
  font-size: 24px;
  font-weight: 700;
  line-height: normal;
  margin-bottom: 5px;
}
#loginOpen .modal-body,
#signup .modal-body,
#forgotPasswordmodal .modal-body {
  background: #d3e4fb;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  #loginOpen .modal-body,
  #signup .modal-body,
  #forgotPasswordmodal .modal-body {
    background-color: transparent;
  }
}
#loginOpen .modal-body .close,
#signup .modal-body .close,
#forgotPasswordmodal .modal-body .close {
  font-size: 18px;
  position: absolute;
  right: 15px;
  top: 15px;
  z-index: 2;
}
#loginOpen .modal-body p,
#signup .modal-body p,
#forgotPasswordmodal .modal-body p {
  color: #212121;
  font-size: 14px;
  font-family: 'Poppins', sans-serif !important;
  line-height: normal;
}
#loginOpen .modal-body input,
#signup .modal-body input,
#forgotPasswordmodal .modal-body input,
#signup .modal-body select,
#loginOpen .modal-body select{
  color: #212121;
  border: 1px solid #c7c7c7;
  border-radius: 7px;
  font-family: 'Poppins', sans-serif !important;
  height: 45px;
  border-left-width: 3px;
  border-left-color: var(--theme-color);
}
#loginOpen .modal-body input:focus,
#signup .modal-body input:focus,
#forgotPasswordmodal .modal-body input:focus {
  box-shadow: 0 0 5px 1px #0000001A;
  border-color: var(--theme-color);
}
#loginOpen .modal-body input::placeholder,
#signup .modal-body input::placeholder,
#forgotPasswordmodal .modal-body input::placeholder {
  font-size: 12px;
}
#loginOpen .modal-body input.input-error,
#signup .modal-body input.input-error,
#forgotPasswordmodal .modal-body input.input-error {
  border-color: #FF4B33;
}
#loginOpen .modal-body input#password,
#signup .modal-body input#password,
#forgotPasswordmodal .modal-body input#password,
#loginOpen .modal-body input#signup-password,
#signup .modal-body input#signup-password,
#forgotPasswordmodal .modal-body input#signup-password {
  padding-right: 45px;
}
#loginOpen .modal-body .login-btn,
#signup .modal-body .login-btn,
#forgotPasswordmodal .modal-body .login-btn {
  background: var(--theme-color);
  box-shadow: 0 0 7px #0000001A;
  border-radius: 5px;
  border: none;
  color: #FFFFFF;
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  font-family: 'Poppins', sans-serif !important;
  font-size: 16px;
  width: 100%;
}
#loginOpen .modal-body .error-text,
#signup .modal-body .error-text,
#forgotPasswordmodal .modal-body .error-text {
  color: #FF4B33;
  margin-top: 2px;
}
#loginOpen .modal-body .register-success,
#signup .modal-body .register-success,
#forgotPasswordmodal .modal-body .register-success {
  color: #27AE60;
}
#loginOpen .modal-body .logo,
#signup .modal-body .logo,
#forgotPasswordmodal .modal-body .logo {
  margin-bottom: 10px;
  display: none;
}
#loginOpen .modal-footer p,
#signup .modal-footer p,
#forgotPasswordmodal .modal-footer p {
  color: #212121;
  font-family: 'Poppins', sans-serif !important;
}
#loginOpen .modal-footer p a,
#signup .modal-footer p a,
#forgotPasswordmodal .modal-footer p a {
  color: #212121;
  font-weight: 700;
  font-family: 'Poppins', sans-serif !important;
}
#loginOpen .modal-content,
#signup .modal-content,
#forgotPasswordmodal .modal-content {
  border-radius: 20px;
  overflow-x: hidden;
  flex-direction: unset;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  #loginOpen .modal-content,
  #signup .modal-content,
  #forgotPasswordmodal .modal-content {
    height: 100%;
    border-radius: 0;
    flex-direction: unset;
  }
}
.forgot {
  color: var(--theme-color);
  cursor: pointer;
  font-size: 14px;
  font-weight: 700;
  font-family: 'Poppins', sans-serif !important;
}
.forgot:hover {
  color: var(--theme-color);
}
.error-msg {
  color: #FF4B33 !important;
}
#loginOpen form,
#signup form,
#forgotPasswordmodal form {
  text-align: center;
}
#mobilemail-error {
  white-space: nowrap;
  display: block;
  margin-top: 10px;
  font-family: 'Poppins', sans-serif !important;
}
.login_signup_loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: none;
  width: 100%;
  z-index: 1;
}
.login_signup_loader .progressbar {
  background-color: var(--theme-color) !important;
}
.login_signup_loader .bufferbar {
  background-image: linear-gradient(to right, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)), linear-gradient(to right, var(--theme-color), var(--theme-color)) !important;
}
.login_signup_loader .auxbar {
  background-color: var(--theme-color) !important;
}
.modal-text-content {
  flex-direction: column;
  background: #d3e4fb;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .modal-text-content {
    border-radius: 0 0 50px 50px;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px #00000040;
  }
}
.modal-text-content lottie-player {
  position: relative;
  width: 100%;
  height: 100%;
  top: 0;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .modal-text-content lottie-player {
    display: none;
  }
}
.modal-text-content h1 {
  font-weight: 400;
  width: 100%;
  text-align: center;
  font-size: 2rem;
}
.modal-text-content h1 span {
  font-weight: 700;
  color: var(--theme-color);
}
.modal-form-content {
  background-color: #FFFFFF;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px), (min-width: 768px) and (max-width: 991.98px) {
  .modal-form-content {
    background-color: transparent;
  }
}
.hide-password {
  position: absolute;
  top: 12px;
  right: 12px;
  color: #bababa;
  font-size: 22px !important;
  border-left: 1px solid #d4d4d4;
  padding-left: 7px;
}
.hide-password:hover {
  color: #bababa;
  text-decoration: none;
}

/*========================================================================
                        Override Styles
=========================================================================*/
.whitelabel-site .logo-wrapper a {
  width: 80px;
}
.whitelabel-site .white-logo-anchor-white img {
  width: 110px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .whitelabel-site .white-logo-anchor-white img {
    width: 100%;
  }
}
.whitelabel-site .manage-logo-big-menu-whitelabel img {
  width: 70%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .whitelabel-site .manage-logo-big-menu-whitelabel img {
    width: 50%;
  }
}
.whitelabel-site .categories-section-disc-title-pera {
  color: #212121;
}
.whitelabel-site a {
  font-weight: normal;
}
.whitelabel-site p {
  line-height: normal;
}
.whitelabel-site .global-search input[type="text"] {
  padding-left: 10px;
  padding-right: 40px;
  z-index: 3;
}
@media (max-width: 320px) {
  .whitelabel-site .global-search input[type="text"] {
    padding-right: 20px;
  }
}
.whitelabel-site .global-search button {
  width: auto;
  height: 33px;
  margin-left: -38px;
  padding: 4px;
  position: relative;
  z-index: 10;
  color: var(--theme-color) !important;
}
.whitelabel-site .global-search button .material-icons {
  line-height: normal;
}
.whitelabel-site .add-tabs {
  top: 100px;
}
.whitelabel-site #allAddButton {
  display: none !important;
}
.whitelabel-site.hasScrolled .main-menu-wrp {
  position: relative !important;
}
.whitelabel-site.hasScrolled .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 0;
}
.whitelabel-site.hasScrolled .bookTemplate .export-notes {
  top: 52px;
}
.whitelabel-site.hasScrolled .user-menu-wrp .menu-actives a.menu-dots-img-wrp {
  position: fixed;
}
.whitelabel-site .bookTemplate .content-wrapper {
  height: calc(100vh - 50px);
}
@media (max-width: 767px) {
  .whitelabel-site .bookTemplate.book_preview .content-wrapper .read-content.col-md-12 .price-wrapper {
    display: none !important;
  }
  .whitelabel-site .bookTemplate.book_preview .content-wrapper #book-read-material {
    padding-bottom: 70px;
  }
  .whitelabel-site .bookTemplate.book_preview .content-wrapper #book-sidebar {
    height: calc(100vh - 70px);
    padding-bottom: 0;
  }
  .whitelabel-site .bookTemplate.book_preview .content-wrapper .price-wrapper {
    padding: 0.5rem 0;
    position: fixed;
    width: 100%;
    z-index: 991;
  }
  .whitelabel-site .bookTemplate.book_preview .content-wrapper .price-wrapper .section-btns {
    padding: 0.5rem 0 0;
    background: #FFFFFF;
  }
  .whitelabel-site .bookTemplate.book_preview .content-wrapper .price-wrapper .preview-book-btns {
    margin-left: 0;
  }
}
.whitelabel-site .bookTemplate .content-wrapper #book-sidebar .side-content > h2 {
  margin-top: 100px;
}
.whitelabel-site .bookTemplate .export-notes {
  top: 130px;
}
.whitelabel-site .bookTemplate .mobChapname #chapters-toggle.left i {
  transform: rotate(0deg);
}
.whitelabel-site .bookTemplate .preview-book-btns .btn-book-buy {
  background: var(--theme-color) !important;
}
.whitelabel-site .bookTemplate .ChapterHeader .bookTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.whitelabel-site .web-mcq .sub-header {
  top: 90px;
  padding-top: 10px;
}
.whitelabel-site .bg-wsTheme {
  background: var(--theme-color) !important;
}
.whitelabel-site #question-block .question-wrapper {
  margin-top: 2rem;
}
.whitelabel-site #question-block .question-wrapper img {
  max-width: 100%;
}
.whitelabel-site .tab-wrappers {
  top: 190px;
}
.whitelabel-site .web-mcq .result-menu {
  top: 75px;
}
.whitelabel-site #quizQuestionSection {
  padding-bottom: 50px;
}
.whitelabel-site .book_details_info #filelabel1,
.whitelabel-site .book_details_info #filelabel2 {
  right: 0;
  left: 0;
}
.whitelabel-site #bookcover .smallText {
  justify-content: center;
}
.whitelabel-site .orders .payment-details > div:last-child p .rupees {
  display: none;
}
.whitelabel-site .users-orders > p {
  padding-right: 15px;
  padding-left: 15px;
}
.whitelabel-site .user_profile .tab-content .jumbotron form .media .continue,
.whitelabel-site .btn-starts,
.whitelabel-site #answer-block .button-wrapper a,
.whitelabel-site #answer-block .button-wrapper a:hover {
  background: var(--theme-color) !important;
}
.whitelabel-site .test-gen-box-main .test-gen-box .btn-info {
  background: var(--theme-color) !important;
  border-color: var(--theme-color) !important;
}
.whitelabel-site .test-gen-box-main .test-gen-box .btn-info:active:focus {
  box-shadow: 0 0 0 0.2rem rgba(239, 114, 21, 0.5) !important;
}
.whitelabel-site .dropdown-menu a:active,
.whitelabel-site .dropdown-menu span:active,
.whitelabel-site .dropdown-menu li:active {
  background-color: #fce5d4;
}
.whitelabel-site .all-container .container-wrapper .media .quiz-practice-btn,
.whitelabel-site .all-container .container-wrapper .media .showRank {
  text-transform: uppercase;
  color: var(--theme-color);
  padding: 0;
  display: inherit;
  border-radius: 0;
}
.whitelabel-site .all-container .container-wrapper .media .showRank {
  padding-left: 10px;
  margin-left: 10px;
  border-left: 1px solid #212121;
}
.whitelabel-site .all-container .container-wrapper .d-flex p.testStarts {
  margin: 0;
  top: 0;
  font-size: 12px;
  flex: none;
}
.whitelabel-site .play::before {
  left: -20px;
}
.whitelabel-site .backfromgenerator {
  margin-top: 20px;
}
.whitelabel-site .backfromgenerator i {
  color: var(--theme-color);
}
.whitelabel-site #htmlContent {
  margin-top: 2rem;
}
.whitelabel-site .purchase-details-container .purchase-heading {
  background: none !important;
  -webkit-text-fill-color: unset !important;
}
.whitelabel-site .purchase-details-container .browse-purchase-book a.learn-btn {
  background: var(--theme-color) !important;
  color: #FFFFFF;
}
.whitelabel-site .web-mcq .mt-fixed {
  margin-top: 3.5rem !important;
  padding-top: 0;
}
.whitelabel-site.custom-fix .bookTemplate .shadowHeader {
  position: fixed;
  top: 0;
}
.whitelabel-site .start-test .header p {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  padding: 0 15px;
}
.whitelabel-site #quizQuestionSection .result-menu > div h2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  padding: 0 10px;
}
.whitelabel-site .mt-fixed #resourceTitle {
  margin-top: 50px;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  padding: 0 15px;
}
.whitelabel-site .notes-creation-header {
  z-index: 1;
}
.whitelabel-site .index-page .main-menu-wrp {
  position: relative;
}
.whitelabel-site .index-page .header-menu-wrapper {
  position: absolute;
  left: 0;
  top: 0;
  text-align: center;
  width: 100%;
}
.whitelabel-site .index-page .this-is-a-web-view-slider {
  display: block;
  overflow: hidden;
}
@media (max-width: 991px) {
  .whitelabel-site .index-page .this-is-a-web-view-slider {
    display: none;
  }
}
.whitelabel-site .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  min-height: 80vh;
  background-size: 100% auto !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  background-position: center !important;
}
@media (min-width: 1600px) {
  .whitelabel-site .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
    background-position: left top !important;
  }
}
.whitelabel-site .index-page .this-is-a-web-view-slider .carousel-inner .carousel-item img {
  width: 100%;
  max-width: 100%;
  min-height: auto;
  max-height: inherit;
}
.whitelabel-site .index-page .this-is-a-responsive-view-slider {
  display: none;
}
@media (max-width: 991px) {
  .whitelabel-site .index-page .this-is-a-responsive-view-slider {
    display: block;
  }
}
.whitelabel-site .index-page .this-is-a-responsive-view-slider img {
  width: 100%;
}
@media (min-width: 768px) and (max-width: 991.98px) {
  .whitelabel-site .index-page .this-is-a-responsive-view-slider img {
    height: 250px;
    object-fit: cover;
  }
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .whitelabel-site .index-page .this-is-a-responsive-view-slider img {
    height: 190px;
    object-fit: cover;
  }
}
.whitelabel-site .index-page .carousel-indicators li {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #e6f0d9;
}
.whitelabel-site .index-page .carousel-indicators li.active {
  background: var(--theme-color);
}
.whitelabel-site .ebooks .ebooks_filter {
  width: 100%;
}
.mozilla .whitelabel-site .index-page .this-is-a-web-view-slider .wapper-all-slider-staticss {
  background-attachment: unset !important;
}
#guestUser,
#loginOpen,
#signup,
#forgotPasswordmodal,
#shareContentModal,
#deleteBook,
#change-password-modal,
#currentPomodoro,
#pomodoroSessionCompletion,
#submit-test,
#report-que,
#force-submit-test,
#videoModal,
#image-modal,
#continue-test,
#successModalOrders,
#removePhone,
#libraryExpiredModal,
#bookQueueModal,
#test-gen-modal,
#PlayAudiOnlyModal,
#quizModal {
  z-index: 9992;
}
.mobile-footer-nav {
  background: var(--theme-color);
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -webkit-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  -moz-box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.11);
  border-radius: 20px 20px 0 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  width: 100%;
  height: 70px;
  position: fixed;
  z-index: 9991;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}
.mobile-footer-nav.hide-menus {
  bottom: -75px;
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
}
.mobile-footer-nav a {
  text-decoration: none;
  flex-wrap: wrap;
  text-align: center;
  color: #FFFFFF;
}
.mobile-footer-nav a:focus {
  text-decoration: none;
}
.mobile-footer-nav a:visited {
  text-decoration: none;
}
.mobile-footer-nav a img {
  margin: 0 auto;
  width: 22px;
}
.mobile-footer-nav a p {
  width: 100%;
  font-size: 13px;
}
.mobile-footer-nav i {
  color: white;
}
.mobile-footer-nav .active-menu {
  opacity: 1;
}
.mobile-footer-nav .common-footer-nav {
  opacity: 0.8;
}
::placeholder {
  /* Chrome, Firefox, Opera, Safari 10.1+ */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
:-ms-input-placeholder {
  /* Internet Explorer 10-11 */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
::-ms-input-placeholder {
  /* Microsoft Edge */
  font-family: 'Poppins', sans-serif !important;
  font-style: italic !important;
  font-weight: normal !important;
  font-size: 14px !important;
  color: rgba(68, 68, 68, 0.3) !important;
}
#test-gen-modal .overlay-testgen-book .book-selected {
  width: 40px;
  height: 40px;
}
#quizQuestionSection #submit-test .modal-footer button {
  font-family: 'Poppins', sans-serif !important;
}
#quizQuestionSection #submit-test .modal-footer .submit {
  background: var(--theme-color);
  color: #FFFFFF;
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: portrait), only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .whitelabel-site #book-read-material #content-data-all {
    padding: 0;
  }
  .whitelabel-site #book-read-material #content-data-all > .container {
    padding: 0;
  }
  .whitelabel-site .all-container .container-wrapper {
    margin-top: 0;
    border: none;
    border-bottom: 1px solid #ededed;
    box-shadow: none;
    border-radius: 0;
  }
  .whitelabel-site .all-container .container-wrapper .media i {
    margin: 0 1rem;
  }
}
@media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
  .whitelabel-site .bookTemplate .export-notes {
    top: 51px !important;
  }
}
@media only screen and (max-width: 767px) {
  .whitelabel-site h2#expiry-date {
    margin-top: 0 !important;
  }
}
.footer .download-app-links .android-app-link {
  border: 1px solid #ddd;
  padding: 2px 0 5px;
  border-radius: 7px;
  min-width: 140px;
  background-color: #e5e5e5;
}
.footer .download-app-links .android-app-link:hover {
  background-color: #e7e7e7;
}
.footer .download-app-links .android-app-link img {
  width: 20px;
  margin-right: 10px;
  height: auto;
}
.footer .download-app-links .android-app-link span {
  line-height: normal;
  font-size: 15px;
}
.footer .download-app-links .android-app-link span small {
  position: relative;
  top: 3px;
}
.whitelabel-site #total-books-of-user {
  display: none;
}
.whitelabel-site #loginOpen .modal-header .close,
.whitelabel-site #signup .modal-header .close,
.whitelabel-site #forgotPasswordmodal .modal-header .close {
  font-size: 20px;
}
.whitelabel-site .main-menu-wrp {
  background: var(--theme-color);
}
.whitelabel-site .main-menu-wrp .posi-static-respons {
  display: flex;
  align-items: center;
}
.whitelabel-site p,
.whitelabel-site a,
.whitelabel-site button {
  font-weight: initial;
}
.whitelabel-site .bookTemplate .side-content ol li.chapter-name i {
  position: relative;
  top: 4px;
}
.whitelabel-site .bookTemplate .chapterSection a.slide-toggle {
  top: 190px;
}
.whitelabel-site .manage-count-wrp-box:after {
  background: linear-gradient(50deg, var(--theme-color) 0%, #89c1ff 100%);
}
.whitelabel-site .connect-section {
  background: var(--theme-color);
}
.whitelabel-site .red-color-fill-bg {
  background: var(--theme-color);
}
.whitelabel-site .responsive-padding-none:nth-child(even) a {
  background-color: #ee3539 !important;
}
.whitelabel-site ul.this-is-side-wrp-ul-big-menu-whitelabel li.active-menuss,
.whitelabel-site ul.this-is-side-wrp-ul-big-menu-whitelabel li:hover {
  background: var(--theme-color);
}
.whitelabel-site .categories-section {
  background: none;
  background-size: cover;
  background-repeat: no-repeat;
  min-height: auto;
}
.whitelabel-site .categories-section .line-box-category {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 50px;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .whitelabel-site .categories-section .line-box-category {
    bottom: 20px;
  }
}
.whitelabel-site .ebook_detail .book_info .book_buttons .col #buyNow {
  color: #FFFFFF;
}
.whitelabel-site #okBuy {
  color: #FFFFFF;
}
.whitelabel-site .there-social-footer-link-wrp li a:hover {
  color: var(--theme-color);
}
.whitelabel-site .my_books .no-books-available .click-here-link {
  background-color: var(--theme-color) !important;
  color: #FFFFFF;
}
.whitelabel-site #cartModalBtns .btn-primary,
.whitelabel-site #cartModalLibBtn .btn-primary {
  background-color: var(--theme-color) !important;
  border-color: var(--theme-color) !important;
  box-shadow: none !important;
  color: #FFFFFF !important;
}
.whitelabel-site .shopping_cart a.proceed_btn {
  background-color: var(--theme-color) !important;
}
.whitelabel-site .bookTemplate .shadowHeader {
  height: 50px !important;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
  .whitelabel-site .bookTemplate {
    height: auto;
  }
  .whitelabel-site .bookTemplate .mobChapname {
    transition: all 0.3s;
    z-index: 991;
  }
  .whitelabel-site .bookTemplate .shadowHeader {
    z-index: 991;
    height: 45px !important;
    position: fixed;
    transition: all 0.3s;
  }
  .whitelabel-site .bookTemplate .shadowHeader .tab-header .navbar {
    height: auto;
    padding-top: 0.15rem;
  }
  .whitelabel-site .bookTemplate .shadowHeader .tab-header .contentEdit {
    position: fixed;
    top: 50px;
    right: 0;
    transition: all 0.3s;
    overflow: hidden;
  }
  .whitelabel-site .bookTemplate .shadowHeader .prevnextbtn {
    position: fixed;
    top: 50px;
    width: 100% !important;
    justify-content: center !important;
    transition: all 0.3s;
  }
  .whitelabel-site .bookTemplate .shadowHeader .prevnextbtn button {
    margin: 0 5px;
    width: 80px;
    font-size: 13px;
  }
  .whitelabel-site .bookTemplate .chapterSection {
    z-index: 991;
  }
  .whitelabel-site .bookTemplate #book-sidebar .backtolibrary {
    font-weight: normal !important;
  }
  .whitelabel-site .bookTemplate #book-sidebar .mobile-title {
    z-index: 98;
  }
  .whitelabel-site .bookTemplate #book-sidebar .mobile-title p {
    line-height: normal;
    padding-left: 10px;
  }
  .whitelabel-site .bookTemplate #book-sidebar .side-content ol {
    padding-left: 10px !important;
  }
  .whitelabel-site .bookTemplate #book-sidebar .side-content ol li.chapter-name {
    font-size: 15px;
    position: relative;
    margin-right: 1.5rem;
    padding-right: 20px;
  }
  .whitelabel-site .bookTemplate #book-sidebar .side-content ol li.chapter-name.orangeText a {
    font-size: 15px;
  }
  .whitelabel-site .bookTemplate #book-sidebar ul.chapter-sections {
    display: none;
  }
  .whitelabel-site .bookTemplate #book-read-material #content-data-all {
    position: relative;
    z-index: 99;
  }
  .whitelabel-site .bookTemplate #book-read-material .all-container {
    margin-top: 2rem;
  }
  .whitelabel-site .bookTemplate #book-read-material .all-container .container-wrapper {
    width: 100%;
    margin-top: 1rem;
    min-height: auto;
  }
  .whitelabel-site .bookTemplate #book-read-material .all-container .container-wrapper .media {
    padding: 0;
  }
  .whitelabel-site .bookTemplate #book-read-material .all-container .container-wrapper .media i {
    margin-left: 0;
  }
  .whitelabel-site .bookTemplate #book-read-material .all-container .container-wrapper .media .title {
    margin-bottom: 5px;
  }
  .whitelabel-site .bookTemplate #book-read-material .all-container .container-wrapper .media .readnow {
    padding-right: 15px;
  }
  .whitelabel-site .bookTemplate #htmlreadingcontent iframe {
    height: 100vh !important;
    border: 1px solid #6C757D;
    margin-top: 0 !important;
  }
  .whitelabel-site .bookTemplate .export-notes {
    top: 90px !important;
  }
  .whitelabel-site .bookTemplate .export-notes .notes-creation-header {
    padding: 0.5rem 0;
  }
  .whitelabel-site .bookTemplate .export-notes .notes-creation-header-title {
    font-size: 16px;
    color: #212121;
  }
  .whitelabel-site.hasScrolled .bookTemplate .tab-header .contentEdit {
    top: 5px;
  }
  .whitelabel-site.hasScrolled .bookTemplate .export-notes {
    top: 45px !important;
  }
  .whitelabel-site.hasScrolled .bookTemplate .shadowHeader .prevnextbtn {
    top: 7px;
  }
  .whitelabel-site .logo-wrapper {
    display: none;
  }
  .whitelabel-site .my_books #subjectFilter .dropdown #sortBy {
    width: auto;
  }
}
.whitelabel-site .purchase-details-container .purchase-details-wrapper .learn-btn {
  background: var(--theme-color);
}
.whitelabel-site .menu-overlay-big-menus.actv {
  background: rgba(0, 0, 0, 0.7);
}
.whitelabel-site .menu-bar-wrp ul li a {
  color: #6C757D;
  border-color: #6C757D;
}
.whitelabel-site .menu-bar-wrp ul li a:hover {
  color: var(--theme-color);
}
.whitelabel-site ul.social-icon-wrp-connect li {
  width: 18%;
}
.whitelabel-site ul.social-icon-wrp-connect li a {
  font-size: 26px;
}
.whitelabel-site #AddToCartModal #TS_AddToCartBtn:hover,
.whitelabel-site #AddToCartModal #TS_AddToCartBtn:active,
.whitelabel-site #AddToCartModal #TS_AddToCartBtn:focus {
  box-shadow: none !important;
  color: #FFFFFF !important;
  background-color: var(--theme-color) !important;
}


.ulList{
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1.1rem;
}
.ulList li a{
  font-size: 17px;
  color: #fff;
}

.ulList li i{
  margin-right: 10px;
}
.brand{
  text-align: center;
}
.brand span{
  color: #fff;
}

.wrapper{
  border-radius: 10px;
  background: #fff;
}

.wrapper > * {
  padding: 1em;
}
.company-info{
  background: var(--theme-color);
  border-radius: 10px;
}
.company-info *{
  color: #fff;
}
.company-info ul{
  margin: 0 0 1rem 0;
}

.contact{
  background: #f9feff;
  /*border-top-right-radius: 10px;*/
  /*border-bottom-right-radius: 10px;*/
  border: 1px solid var(--theme-color);
  border-radius: 10px;
}

/* Form styling */

.contact form{
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 25px;
}

.contact form p{
  margin: 0;
}
.contact form .full{
  grid-column: 1 /3;
}

.contact form button, .contact form input, .contact form textarea{
  width: 100%;
  padding: 1em;
  border: 1px solid #c9e6ff;
}
.contact form button{
  border-color: var(--theme-color);
}
.contact button{
  background: var(--theme-color);
  border: 0;
  color: #fff;
}

.contact button:hover, .contact button:focus{
  background:var(--theme-color);
  color: #fff;
  transition: background-color .5s ease-out;
  outline: 0;
}


.infoCard:after{
  left: 115px !important;
}
.title_wrapper{
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-bottom: 3rem;
  margin-top: 3rem;
}
.title_wrapper h1{
  font-size: 30px;
}
.title_wrapper{
  color: rgba(0,0,0,0.5);
}
.infoText{
  margin-top: 10px;
  margin-bottom: 30px;
  color: #fff;
}
/* Desktop view */
@media(min-width:700px){
  .wrapper{
    display: grid;
    grid-template-columns: 2fr 3fr;
  }

  .wrapper > * {
    padding: 2em;
  }

  .wrapper h3, .wrapper ul, .brand{
    text-align: left;
  }
  .company-info{
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .infoCard:after{
    left: 0 !important;
  }
  .company-info{
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
  }
}

.editPencil{
  margin-left: -20px;
  color:var(--theme-color);
}
.headerCategoriesMenu .header__categories{
  background: var(--theme-color);
}
#accordion .card-body{
  background: #e5e5e59c;
}
#accordion .card-header{
  background: #dadada;
}
#accordion .card a{
  color: #000;
}
#accordion .card-header .btn-link:after{
  color:#000;
}
#accordion .card-header .btn-link.collapsed:after{
  color:#000;
}