@font-face {
  font-family: 'icomoon';
  src: url("../fonts/icomoon.eot?2elvf8");
  src: url("../fonts/icomoon.eot?2elvf8#iefix") format("embedded-opentype"), url("../fonts/icomoon.ttf?2elvf8") format("truetype"), url("../fonts/icomoon.woff?2elvf8") format("woff"), url("../fonts/icomoon.svg?2elvf8#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
}
[class^="icons-"], [class*=" icons-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
[class^="icons-"]:before, [class*=" icons-"]:before {
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icons-User_Icon:before {
  content: "\e900" !important;
}

.icon-profile {
  font-size: 18px;
}
.icon-profile .path1:before {
  content: "\e900";
  color: #444444;
  opacity: 0.8;
}

.icon-profile .path2:before {
  content: "\e901";
  margin-left: -1.900em;
  color: #444444;
  opacity: 0.8;
}

.icon-profile .path3:before {
  content: "\e902";
  margin-left: -1.400390625em;
  color: #757575;
}

.icon-cart:before {
  content: "\e906";
  font-size: 18px;
}

body, html {
  height: 100%;
  font-size: 16px;
  font-family: 'Montserrat', sans-serif;
  background-color: #f9f9f9;
  padding: 60px 0 0 0;
  position: relative;
  -webkit-overflow-scrolling: touch;
}

body {
  overflow: auto;
  overflow-x: hidden;
}

#content-data-books li:nth-child(n+12) {
  display: none;
}

img {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
}

.wrapper {
  position: relative;
  height: 100%;
}

a, p {
  color: #000;
  text-decoration: none;
}
a:hover, a.book-desc-btn:visited, a.book-desc-btn:active, a.book-desc-btn:link, p:hover, p.book-desc-btn:visited, p.book-desc-btn:active, p.book-desc-btn:link {
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  color: #000;
}

.banner-container {
  max-height: 352px;
  background: linear-gradient(90deg, #F9FBFB 20.26%, #F9FBFB 20.92%, rgba(255, 255, 255, 0) 21.81%, rgba(249, 251, 251, 0) 78.32%, #F9FBFB 79.22%, #F9FBFB 100%);
}
.banner-container .banner-image {
  position: relative;
  max-width: 1128px;
  margin: 0 auto;
  float: none;
}
.banner-container .banner-image img {
  opacity: 0.5;
}
.banner-container .banner-image .banner-text {
  position: absolute;
  top: 0;
  padding-bottom: 47px;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.74) 66.53%, rgba(255, 255, 255, 0) 100%);
}
.banner-container .banner-image .banner-text .banner-heading {
  max-width: 251px;
  color: #F05A2A;
  font-size: 56px;
  font-weight: bold;
  letter-spacing: 1px;
}
.banner-container .banner-image .banner-text .banner-heading .clearfix {
  font-size: 28px;
  display: block;
  letter-spacing: 1.5px;
}
@media screen and (max-width: 768px) {
  .banner-container .banner-image .banner-text .banner-heading {
    font-size: 36px;
  }
  .banner-container .banner-image .banner-text .banner-heading .clearfix {
    font-size: 18px;
  }
}
.banner-container .banner-image .banner-text .banner-sub-heading {
  max-width: 258px;
  font-size: 18px;
  font-weight: bold;
  color: #192D36;
  margin-top: 25px;
}

.main-content {
  background: linear-gradient(180deg, rgba(94, 199, 215, 0.05) 0%, rgba(247, 148, 32, 0.05) 148.83%);
  background-size: cover;
  position: relative;
  z-index: 1;
}
.main-content .main-content-container {
  padding-left: 112px;
  padding-right: 112px;
}
.main-content .main-content-container select {
  margin-bottom: 25px;
}
.main-content .main-content-container .books-wrapper {
  position: relative;
  margin-left: 15px;
  padding-right: 15px;
  padding-left: 15px;
}
.main-content .main-content-container .books-wrapper::before {
  content: '';
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid rgba(189, 189, 189, 0.2);
}
.main-content .level-list {
  overflow: hidden;
  padding: 0;
}
.main-content .level-list option {
  padding: 10px;
}

.main-content::before {
  content: '';
  background-image: url("../images/pattern.png");
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.02;
  z-index: -1;
}

.books {
  list-style: none;
  padding: 0;
  margin: 0;
}

.book-content-wrapper {
  padding-right: 15px;
  padding-left: 15px;
  margin-right: 0;
  margin-bottom: 56px;
}

.book-item-link {
  width: 100%;
  text-decoration: none;
}

.book-image-wrapper {
  height: 300px;
}

.book-image {
  display: block;
  width: 100%;
  max-width: 100%;
  height: 100%;
  border: 1px solid rgba(189, 189, 189, 0.3);
}
.book-image:hover, .book-image.book-desc-btn:visited, .book-image.book-desc-btn:active, .book-image.book-desc-btn:link {
  border-radius: 4px;
}

.book-info {
  height: 102px;
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}
.book-info .book-preview {
  text-align: center;
  border-bottom: 1px solid #CACACA;
  padding: 10px 0 5px 0;
  margin-bottom: 5px;
}
.book-info .book-preview .preview-links {
  color: #F05A2A;
  display: inline-block;
  padding: 5px 15px 5px 15px;
  border-right: 1px solid #CACACA;
}
.book-info .book-preview .preview-links:last-child {
  border-right: none;
}
.book-info .book-name-author {
  height: 56px;
  margin-bottom: 5px;
}
@media screen and (max-width: 768px) {
  .book-info .book-name-author {
    height: 35px;
    margin-bottom: 5px;
  }
}
.book-info .book-name {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.book-info .author-name {
  color: #444444;
  font-weight: 500;
}

.price-rating {
  margin-top: 10px;
}
.price-rating p {
  margin: 0;
}
.price-rating .book-price .original-price, .price-rating .book-price .offer-price {
  display: inline-block;
}
.price-rating .book-price .original-price {
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.5);
  margin-left: 5px;
  text-decoration: line-through;
}
.price-rating .book-price .offer-price {
  font-size: 20px;
  font-weight: 500;
}

.user-profile {
  width: 260px;
  margin: 0 auto;
  text-align: center;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.2);
}

.user-image-wrapper {
  position: relative;
  width: 100%;
  margin: 0 auto;
}
.user-image-wrapper img {
  width: 100%;
}
.user-image-wrapper .user-details-name {
  width: 100%;
  text-align: left;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 0;
  padding: 10px 0 10px 5px;
}
.user-image-wrapper .user-details-name p {
  color: #fff;
  margin: 0;
}
.user-image-wrapper .user-profession {
  font-size: 14px;
}

.user-details {
  text-align: left;
  padding: 10px 0 10px 0;
}
.user-details p {
  color: #444444;
  padding: 0 0 5px 5px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.user-details p:last-child {
  border-bottom: 0;
  margin: 0;
}

.click-here {
  color: #2EBAC6;
}
.click-here:hover, .click-here.book-desc-btn:visited, .click-here.book-desc-btn:active, .click-here.book-desc-btn:link {
  text-decoration: underline;
}

.loading-icon {
  width: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
}
.loading-icon .loading-img {
  display: block;
  max-width: 100%;
  margin: 0 auto;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 5px;
}

.no-books {
  font-size: 25px;
  text-align: center;
  margin-bottom: 30px;
}
.no-books i.fa-frown-o {
  display: block;
}

@media screen and (min-width: 320px) and (max-width: 767px) {
  body, html {
    padding: 0;
  }

  body {
    overflow-x: hidden;
  }

  .main-content .main-content-container {
    padding-left: 15px;
    padding-right: 15px;
  }
  .main-content .main-content-container .books-wrapper {
    margin-left: 0;
    padding-right: 0;
    padding-left: 0;
  }
  .main-content .main-content-container .books-wrapper .book-content-wrapper {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: 0;
    margin-bottom: 0;
  }

  .loading-icon .loading-img {
    max-width: 50%;
  }

  .search-book {
    margin-bottom: 20px;
  }

  .search-icon {
    right: 0;
  }
}
.reviews-container {
  padding: 16px 0 16px 0;
  margin-top: 16px;
  border-top: 1px solid rgba(189, 189, 189, 0.54);
}

.book-user-review {
  width: 100%;
  float: left;
  margin-bottom: 30px;
  clear: both;
}

.reviews-header {
  font-size: 22px;
}

.review-user-name {
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  text-transform: capitalize;
}

.user-review {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  position: relative;
}

.user-full-reviews {
  position: relative;
}
.user-full-reviews::after {
  content: '';
  width: 50%;
  height: 1px;
  background-color: rgba(189, 189, 189, 0.55);
  position: absolute;
  bottom: -20px;
  left: 60px;
}

.review-see-more {
  color: #F05A2A;
}

.review-date {
  font-size: 14px;
}

.see-all-reviews {
  color: #BBBDBF;
  font-size: 14px;
}
.see-all-reviews i {
  font-size: 18px;
  padding-left: 5px;
  vertical-align: text-bottom;
}

@media screen and (max-width: 320px) {
  .main-content .main-content-container .books-wrapper .book-content-wrapper {
    width: 50%;
    padding: 0;
    padding-left: 15px;
    padding-right: 15px;
  }

  .loading-icon .loading-img {
    max-width: 50%;
  }
}
@media screen and (min-width: 320px) and (max-width: 374px) {
  .book-item {
    width: 100%;
    height: 270px;
    margin: 0 0 20px 0;
  }
  .book-item .book-image-wrapper {
    height: 170px;
  }

  .loading-icon .loading-img {
    max-width: 50%;
  }
}
@media screen and (min-width: 768px) {
  .books {
    margin-top: 65px;
  }

  .search-filters {
    width: 20%;
    margin-top: 40px;
    margin-right: 0;
  }
  .search-filters .level-selector {
    margin: 0;
    padding: 0;
    border: 1px solid rgba(189, 189, 189, 0.55);
  }
  .search-filters .level-selector .level-selector-link {
    width: 90px;
    padding: 10px;
    text-align: center;
    display: inline-block;
    border-right: 1px solid rgba(189, 189, 189, 0.55);
  }
  .search-filters .level-selector li:last-of-type {
    border-right: 0;
  }

  .search-book {
    padding-right: 15px;
    padding-left: 15px;
  }

  .book-search-type-dropdown {
    left: 15px;
    top: 40px;
    border-radius: 0;
  }

  .no-books {
    font-size: 22px;
    margin-top: 100px;
  }

  .search-book .typeahead {
    width: 1164px;
    border-radius: 0;
  }
}
@media screen and (min-width: 768px) and (max-width: 1100px) {
  .main-content .main-content-container {
    padding-left: 15px;
    padding-right: 15px;
  }

  .book-item {
    width: 100%;
    margin: 0 30px 60px 0;
  }
}
@media screen and (min-width: 375px) and (max-width: 767px) {
  .search-book .typeahead {
    width: 100%;
    border-radius: 0;
  }

  .book-item {
    width: 100%;
    height: auto;
    margin: 0 15px 20px 0;
  }

  .book-image-wrapper {
    height: 200px;
  }
}
@media screen and (min-width: 320px) and (max-width: 414px) {
  .main-content-container .books-wrapper .col-xs-6 {
    padding-left: 5px;
    padding-right: 5px;
  }
}
@media screen and (min-width: 1100px) and (max-width: 1500px) {
  .main-content .main-content-container {
    padding-left: 112px;
    padding-right: 15px;
  }
}
.show-more-btn {
  display: block;
  width: 350px;
  height: 50px;
  color: #5EC7D7;
  margin: 0 auto 56px;
  background: transparent;
  border: 1px solid #5EC7D7;
  border-radius: 4px;
  clear: both;
}
.show-more-btn:hover, .show-more-btn.book-desc-btn:visited, .show-more-btn.book-desc-btn:active, .show-more-btn.book-desc-btn:link {
  color: #5EC7D7 !important;
}

.no-books-available {
  text-align: center;
}
.no-books-available p {
  font-size: 20px;
}

.no-book-wrapper {
  width: 150px;
  display: block;
  margin: 10px auto;
}
.no-book-wrapper .book-image {
  width: 100%;
}

.no-book-wrapper .book-image {
  border: 0;
}

.plz-login {
  color: #F05A2A;
}

.exercise-number {
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.zoom-sections {
  height: 40px;
  border: 1px solid rgba(189, 189, 189, 0.55);
  border-left: 0;
}
.zoom-sections .dropdown {
  width: 460px;
  float: left;
}
.zoom-sections button {
  width: 100%;
  height: 40px;
  text-align: left;
  border-radius: 0;
  background-color: rgba(189, 189, 189, 0.55);
  background: url("../images/caret-icon.png");
  background-repeat: no-repeat;
  background-position: 99% center;
  border: 0;
}
.zoom-sections button:hover, .zoom-sections button.book-desc-btn:visited, .zoom-sections button.book-desc-btn:active, .zoom-sections button.book-desc-btn:link {
  color: #000;
}
.zoom-sections .open > .btn-default:active, .zoom-sections .btn-default.active, .zoom-sections .open > .dropdown-toggle.btn-default {
  width: 100%;
  text-align: left;
  border-radius: 0;
  background-color: rgba(189, 189, 189, 0.55);
  background: url("../images/caret-icon.png");
  background-repeat: no-repeat;
  background-position: 99% center;
}
.zoom-sections .dropdown-menu {
  width: 100%;
}
.zoom-sections .dropdown-menu li a {
  white-space: normal;
}
.zoom-sections .zoom-icons {
  float: left;
}
.zoom-sections .zoom-icons .zoom-links {
  height: 39px;
  display: inline-block;
  text-indent: -9999999px;
}
.zoom-sections .zoom-icons .zoom-out {
  background: url("../images/zoom-out.png");
  background-repeat: no-repeat;
  background-position: center;
  border: 1px solid rgba(189, 189, 189, 0.55);
  border-bottom: 0;
  border-top: 0;
}
.zoom-sections .zoom-icons .zoom-in {
  background: url("../images/zoom-in.png");
  background-repeat: no-repeat;
  background-position: center;
  border: 1px solid rgba(189, 189, 189, 0.55);
  border-bottom: 0;
  border-top: 0;
  border-left: 0;
}
.zoom-sections .zoom-icons .fullScreen {
  background: url("../images/fullscreen.png");
  background-repeat: no-repeat;
  background-position: center;
}
.zoom-sections .zoom-icons .search-iframe {
  height: 38px;
  position: relative;
  padding-left: 10px;
  background: url("../images/search.png");
  background-repeat: no-repeat;
  background-position: right;
  box-shadow: none;
  vertical-align: top;
  border: 0;
  border-left: 1px solid rgba(189, 189, 189, 0.55);
  outline: none;
}

.full-100 {
  width: 100%;
  transition: all ease .2s;
  overflow: scroll;
  overflow-x: hidden;
}

.full-50 {
  width: auto;
}

#htmlContent {
  clear: both;
}

@media screen and (max-width: 767px) {
  .main-content {
    padding-top: 16px;
  }

  .zoom-sections .dropdown {
    width: 190px;
  }
}
@media screen and (min-width: 768px) {
  .practice-modal {
    width: 1129px;
  }
}
@media screen and (max-width: 500px) {
  .zoom-sections .dropdown {
    width: 98%;
  }
}
.fixed-section {
  position: fixed;
  width: 100%;
  top: 15px;
  background: #fff;
  z-index: 999;
}

.login-signup-container {
  font-family: Montserrat;
  position: relative;
  width: 100%;
  padding: 40px 0;
  background: linear-gradient(180deg, rgba(94, 199, 215, 0.29) 0%, rgba(247, 148, 32, 0.25) 148.83%);
  z-index: 1;
}

.login-signup-container::before {
  content: '';
  background-image: url("../images/pattern.png");
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.02;
  z-index: -1;
}

.login-signup-form {
  display: block;
  width: 476px;
  background: #fff;
  text-align: center;
  padding: 24px 0;
  margin: 0 auto;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}

.login-signup-headline {
  font-size: 32px;
  margin-bottom: 32px;
}

.easy-login, .enter-email {
  font-size: 14px;
  text-transform: uppercase;
  opacity: .75;
}

.enter-email {
  text-transform: none;
}

.social-btns {
  width: 355px;
  margin: 16px auto;
}
.social-btns .btns {
  width: 168px;
  height: 50px;
  font-size: 14px;
  background: #FFFFFF;
  border: 1px solid rgba(68, 68, 68, 0.6);
  box-sizing: border-box;
  border-radius: 4px;
}
.social-btns .google {
  margin-right: 12px;
  background: url(../images/ic_google.png);
  background-repeat: no-repeat;
  background-size: 20px 20px;
  background-position: 18px 15px;
}
.social-btns .facebook {
  background: url(../images/ic_fb.png);
  background-repeat: no-repeat;
  background-size: 20px 20px;
  background-position: 12px 15px;
}

.submit-btn {
  width: 380px;
  margin: 30px auto 0;
}
.submit-btn .login-signup-btn {
  height: 50px;
  color: #fff;
  font-size: 16px;
  background: #5EC7D7;
}

.using-email {
  margin-top: 32px;
  margin-bottom: 16px;
  font-size: 14px;
}

.form-container {
  width: 380px;
  margin: 0 auto;
}

.form-inputs {
  position: relative;
  border: 1px solid rgba(68, 68, 68, 0.2);
  border-radius: 4px;
}
.form-inputs .show-password {
  width: 22px;
  height: 13px;
  position: absolute;
  right: 15px;
  bottom: 15px;
  background: url("../images/eye.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-indent: -5000px;
}

#password {
  padding-right: 40px;
}

.login-signup-input {
  height: 40px;
  border: 1px solid rgba(68, 68, 68, 0.2);
  border-radius: 0;
}

.already-user {
  margin: 16px auto 0;
  text-align: left;
}
.already-user a {
  color: #F05A2A;
}

.have-an-account {
  font-size: 12px;
}

.forgot-password {
  font-size: 12px;
  color: #F05A2A;
  text-align: left;
  padding-right: 5px;
  margin: 16px auto 0;
}
.forgot-password a {
  color: #F05A2A;
}

@media screen and (max-width: 767px) {
  .login-signup-form {
    width: 91.6666667%;
  }

  .form-container {
    width: 100%;
  }

  .form-inputs {
    width: 100%;
    padding: 0 5px;
    border: 0;
  }

  .submit-btn {
    width: 100%;
    padding: 0 5px;
  }

  .forgot-password {
    text-align: center;
  }

  .already-user {
    text-align: center;
  }

  .have-an-account {
    float: none !important;
  }
}
.greytext {
  color: #444444;
}

.breadcrumbs, .breadcrumb {
  background-color: transparent !important;
  margin-top: 15px;
}

.breadcrumbs {
  padding-left: 85px;
}
.breadcrumbs .active {
  font-weight: bold;
}

.bookDetails {
  background: linear-gradient(180deg, rgba(94, 199, 215, 0.05) 0%, rgba(247, 148, 32, 0.05) 148.83%);
  background-size: cover;
  position: relative;
  padding-bottom: 40px;
  z-index: 1;
}

.bookDetails::before {
  content: '';
  background-image: url("../images/pattern.png");
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  opacity: 0.02;
  z-index: -1;
}

.book-details-container {
  font-family: 'Montserrat', sans-serif;
  min-height: 700px;
  background-color: #fff;
  position: relative;
  padding: 24px;
  margin-top: 15px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}

.detail-book-image, .book-preview-chapters {
  max-height: 700px;
  border-right: 1px solid rgba(189, 189, 189, 0.55);
}
.detail-book-image img, .book-preview-chapters img {
  width: 100%;
  border: 1px solid rgba(189, 189, 189, 0.55);
  border-radius: 4px;
}

.buy-btns {
  margin-top: 10px;
}

.buy-btn, .add-cart-btn {
  height: 60px;
  color: #fff;
  font-size: 18px;
  text-transform: uppercase;
  background: #5EC7D7;
  margin-bottom: 15px;
  border: 0;
  border-radius: 4px;
  box-shadow: none;
}

.book-deatils-name-area {
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(189, 189, 189, 0.55);
}

.detail-book-name {
  font-size: 28px;
}

.book-desc-area {
  position: relative;
  padding: 15px 0 16px 0;
  border-top: 1px solid rgba(189, 189, 189, 0.55);
}

.book-desc-btn {
  text-decoration: none;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
}
.book-desc-btn:hover, .book-desc-btn:visited, .book-desc-btn:active, .book-desc-btn:link {
  text-decoration: none;
  color: #000;
}

.book-details-price {
  padding-top: 16px;
}
.book-details-price p {
  display: inline-block;
}
.book-details-price .offer-price {
  font-size: 30px;
  font-weight: 500;
}
.book-details-price .original-price {
  font-size: 25px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.5);
  margin-left: 5px;
  text-decoration: line-through;
}

.continue-reading {
  position: absolute;
  bottom: 0;
  width: 100%;
  bottom: -15px;
  padding-top: 40px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 3.31%, rgba(255, 255, 255, 0.270936) 21.55%, rgba(255, 255, 255, 0.523809) 37.57%, #FFFFFF 61.33%);
}

.continue-reading-text {
  color: #F05A2A;
}

.book-rating {
  margin-left: 80px;
  font-size: 24px;
  letter-spacing: 3px;
}

.book-preview-chapters {
  overflow: auto;
}

.chapter-names-wrapper {
  list-style: none;
  padding: 0;
  margin: 0;
}

.chapter-name {
  font-size: 18px;
  padding: 16px 9px;
  border-bottom: 1px solid rgba(189, 189, 189, 0.55);
}
.chapter-name i {
  float: right;
}
.chapter-name .orangeText {
  color: #F05A2A;
}
.chapter-name .orangeText:hover, .chapter-name .orangeText.book-desc-btn:visited, .chapter-name .orangeText.book-desc-btn:active, .chapter-name .orangeText.book-desc-btn:link {
  text-decoration: underline;
}

.buy-widget {
  max-width: 810px;
  max-height: 140px;
  padding: 10px 10px;
  background-color: #fff;
  margin: 0 auto;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  position: fixed;
  bottom: 0;
  left: 50%;
  z-index: 999;
  -webkit-transform: translate(-50%, 0);
  -moz-transform: translate(-50%, 0);
  -ms-transform: translate(-50%, 0);
  -o-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
}
.buy-widget .buy-widget-book {
  float: left;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.buy-widget .buy-widget-book-name-wrapper {
  max-width: 380px;
  padding: 8px 8px 15px 8px;
  float: left;
}
.buy-widget .buy-widget-book-name {
  font-size: 20px;
  line-height: 25px;
}
.buy-widget .buy-widget-author-name {
  font-size: 14px;
  opacity: 0.8;
}
.buy-widget .buy-widget-btns {
  float: left;
  padding: 40px 0 0 0;
}
.buy-widget .buy-widget-btns .buy-btn, .buy-widget .buy-widget-btns .add-cart-btn {
  width: auto;
  font-size: 16px;
}
.buy-widget .buy-widget-btns .add-to-library {
  font-size: 12px !important;
}

.buy-widget-book {
  width: 90px;
}

@media screen and (min-width: 320px) and (max-width: 767px) {
  .questionumber-containte {
    width: 30%;
    max-height: 500px;
    overflow: auto;
  }

  .book-details-container {
    width: 97%;
    padding-right: 14px;
    padding-left: 14px;
  }

  .book-details-container {
    min-height: 1px;
  }

  .buy-btns {
    display: block;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 9999;
  }
  .buy-btns button {
    width: 100%;
    margin: 0;
    border-radius: 0;
  }
}
@media screen and (min-width: 768px) and (max-width: 1536px) {
  .questionumber-containte {
    width: 30%;
    max-height: 500px;
    overflow: auto;
  }
}
@media screen and (min-width: 1537px) {
  .questionumber-containte {
    width: 30%;
    max-height: 730px;
    overflow: auto;
  }
}
@media screen and (min-width: 768px) and (max-width: 1536px) {
  .book-details-container-book {
    width: 100%;
  }
}
.book-details-container-book {
  height: 100% !important;
}

.questionumber-containte {
  position: fixed;
}

.mcq-question-div {
  position: absolute !important;
  right: 0;
}

.tabs-section {
  font-family: 'Montserrat', sans-serif;
}
.tabs-section .chapter-tabs {
  max-width: 825px;
  font-size: 24px;
  padding-left: 9px;
  white-space: nowrap;
  border-bottom: 0;
  overflow: hidden;
}
.tabs-section .chapter-tabs li {
  display: inline-block;
  float: none;
}
.tabs-section .chapter-tabs li a {
  color: #888888;
  font-size: 20px;
  border: 0;
  border-bottom: 2px solid transparent;
}
.tabs-section .chapter-tabs li a:visited {
  background-color: transparent;
  border: 0;
}
.tabs-section .chapter-tabs li a:active {
  background-color: transparent;
  border: 0;
}
.tabs-section .chapter-tabs li a:link {
  background-color: transparent;
  border: 0;
}
.tabs-section .chapter-tabs li a:hover, .tabs-section .chapter-tabs li a.book-desc-btn:visited, .tabs-section .chapter-tabs li a.book-desc-btn:active, .tabs-section .chapter-tabs li a.book-desc-btn:link {
  color: #F05A2A;
  background-color: transparent;
  border: 0;
  border-bottom: 2px solid #F05A2A;
}
.tabs-section .chapter-tabs li.active a {
  color: #F05A2A;
  border-bottom: 2px solid #F05A2A;
}
.tabs-section .section-btns a {
  font-size: 14px;
  color: #888;
}
.tabs-section .section-btns .prev-sec {
  background: url("../images/booksmojo/prev-section.png");
  background-repeat: no-repeat;
  background-position: left;
  background-size: 11px;
  padding-left: 15px;
}
.tabs-section .section-btns .next-sec {
  color: #5EC7D7;
  background: url("../images/booksmojo/next-section.png");
  background-repeat: no-repeat;
  background-position: right;
  background-size: 11px;
  padding-right: 15px;
}

.learn-practice {
  padding-left: 24px;
  margin-top: 24px;
}
.learn-practice .learn-practice-card {
  width: 221px;
  height: 220px;
  padding: 16px 24px;
  margin-right: 24px;
  margin-bottom: 24px;
  background: url("../images/card-pattern.png");
  background-repeat: no-repeat;
  background-size: cover;
  border: 1px solid rgba(189, 189, 189, 0.55);
  border-radius: 4px;
  transition: box-shadow ease .2s;
}
.learn-practice .learn-practice-card:hover, .learn-practice .learn-practice-card.book-desc-btn:visited, .learn-practice .learn-practice-card.book-desc-btn:active, .learn-practice .learn-practice-card.book-desc-btn:link {
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
}
.learn-practice .test-image {
  width: 20px;
  height: 20px;
  margin: 0 auto;
}
.learn-practice .test-image img {
  width: 100%;
}
.learn-practice .exercise-number {
  font-weight: bold;
  text-align: center;
  margin-top: 4px;
}
.learn-practice .learn-pactice-btns {
  margin-top: 20px;
}
.learn-practice .learn-pactice-btns .practice-btn {
  display: inline-block;
  width: 132px;
  height: 36px;
  color: #fff;
  background-color: #5EC7D7;
  margin-top: 16px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
}
.learn-practice .learn-pactice-btns .learn-btn {
  height: 36px;
  color: #444;
  background-color: #fff;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
}
.learn-practice .learn-pactice-btns .score-btn {
  display: inline-block;
  width: 30px;
  height: 36px;
  background: #FFFFFF;
  background: url("../images/booksmojo/scorecard.png");
  background-repeat: no-repeat;
  background-size: 85% 85%;
  background-position: 3px;
  margin-top: 16px;
  margin-left: 8px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  text-indent: 999999999px;
}

.practice-questions-header {
  text-align: center;
  background-color: transparent;
  box-shadow: 0px 5px 3px -2px rgba(0, 0, 0, 0.1);
}

.quizTitle {
  display: inline-block;
  font-size: 26px;
  text-align: center;
}

.next-btn {
  float: right;
  width: 173px;
  height: 48px;
  color: #fff;
  font-size: 22px;
  background-color: #5EC7D7;
  padding: 9px 12px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}

.submit-quiz-btn {
  display: block;
  width: 320px;
  height: 48px;
  color: #F05A2A;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  background: #FCFCFC;
  padding: 14px 0;
  margin: 0 auto;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}
.submit-quiz-btn:hover, .submit-quiz-btn.book-desc-btn:visited, .submit-quiz-btn.book-desc-btn:active, .submit-quiz-btn.book-desc-btn:link {
  color: #F05A2A !important;
  text-decoration: none;
}

.discussion-form {
  margin-top: 24px;
}
.discussion-form textarea {
  resize: none;
}
.discussion-form .submit-question-btn {
  margin-top: 24px;
  color: #fff;
  background-color: #5EC7D7;
}

#close-button {
  width: 24px;
  height: 24px;
  display: inline-block;
  float: left;
  background: url("../images/close.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-indent: -5000px;
}

.question-title {
  color: #888888;
  font-size: 20px;
  font-weight: bold;
}

.practice-question {
  margin-bottom: 40px;
}

@media screen and (min-width: 993px) {
  .chapter-tabs > li > a {
    margin-right: 24px;
  }
}
#left-tab {
  width: 20px;
  position: absolute;
  top: 15px;
  left: 5px;
  background: #fff;
  background-image: url(../images/tab-scroll-icon.png);
  background-repeat: no-repeat;
  background-position: right;
  transform: matrix(-1, 0, 0, 1, 0, 0);
  text-indent: -99999px;
}

#right-tab {
  width: 20px;
  position: absolute;
  top: 10px;
  right: 5px;
  background: #fff;
  background-image: url(../images/tab-scroll-icon.png);
  background-repeat: no-repeat;
  background-position: right;
  text-indent: -99999px;
}

@media screen and (max-width: 767px) {
  .chapter-details-area {
    padding-left: 5px;
    padding-right: 5px;
  }
}
.correct-question-answer {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #46B520;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.wrong-question-answer {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #B72319;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.grey-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #888;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.skipped-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #000;
  background-color: #fff;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid #000;
  border-radius: 8px;
}

.review-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #A056E5;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.answered-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #2F80ED;
  padding: 8px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}

.current-question {
  display: inline-block;
  width: 40px;
  height: 40px;
  text-align: center;
  color: #fff;
  font-weight: bold;
  background-color: #F05A2A;
  padding: 10px;
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid #F05A2A;
  border-radius: 8px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
}

.mcq-question-div {
  position: relative;
  overflow: auto;
}
.mcq-question-div::before {
  content: '';
  position: absolute;
  left: 0;
  height: 100%;
  border-left: 1px solid rgba(189, 189, 189, 0.55);
}

.quiz-modal-body {
  min-height: 750px;
  max-height: 750px;
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  padding: 15px 15px 0;
  overflow: auto;
}
.quiz-modal-body .container-fluid {
  margin: 0 !important;
}
.quiz-modal-body .modal-footer {
  padding-left: 0;
}
.quiz-modal-body .modal-footer .previous-btn {
  padding-left: 0;
}

.score-container {
  display: none;
  min-height: 525px;
  max-height: 525px;
  min-height: 750px;
  max-height: 750px;
  overflow: auto;
}

.practice-score-container {
  min-height: 300px;
  color: #fff;
  text-align: center;
  background: linear-gradient(74.18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -webkit-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: -o-linear-gradient(288deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  background: linear-gradient(18deg, rgba(67, 198, 172, 0.87) 0%, rgba(25, 22, 84, 0.77) 100%);
  padding: 26px 0;
}
.practice-score-container .practice-score {
  width: 274px;
  margin: 0 auto;
}
.practice-score-container .practice-score .medal-picture {
  width: 135px;
  height: 138px;
  margin: 0 auto;
}
.practice-score-container .practice-score .medal-picture img {
  width: 100%;
}
.practice-score-container .practice-score .practice-score-string p {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}
.practice-score-container .practice-score .practice-score-string .practice-score-string-score {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
}

.answer-summary {
  padding: 24px 0;
  text-align: center;
  background: #F8F8F8;
}
.answer-summary p {
  margin: 0;
}
.answer-summary .summary-heading {
  font-size: 22px;
}
.answer-summary .short-heading {
  font-size: 16px;
}
.answer-summary .score-summary {
  max-width: 634px;
  margin: 0 auto;
  margin-top: 16px;
}
.answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
  width: 100px;
  height: 100px;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(43.98deg, #2A7E0D 14.96%, #4DEB17 100%);
  padding: 34px 35px 36px 34px;
  margin: 0 auto;
  margin-top: 8px;
  border-radius: 16px;
}
.answer-summary .score-summary .wrong-answers {
  background: linear-gradient(45deg, #97160D 20.42%, #F76E64 100%);
}
.answer-summary .score-summary .skipped-answers {
  color: #444444;
  background: #fff;
  border: 2px solid #444444;
}

.accuracy-summary {
  max-width: 634px;
  padding-bottom: 16px;
  margin: 0 auto;
  margin-top: 26px;
}
.accuracy-summary .time-taken, .accuracy-summary .answer-accuracy, .accuracy-summary .question-hr {
  display: inline-block;
  width: 56px;
  height: 56px;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
  background: url("../images/booksmojo/clock-mobile.png");
  background-repeat: no-repeat;
  background-size: 100%;
  padding: 34px 0 36px 0;
  border-radius: 500px;
  margin: 0 auto;
  margin-top: 8px;
}
.accuracy-summary .answer-accuracy {
  background: url("../images/booksmojo/accuracy-mobile.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.accuracy-summary .question-hr {
  background: url("../images/wonderslate/question-hr.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
.accuracy-summary .time-span {
  display: inline-block;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  vertical-align: top;
  margin-top: 16px;
  margin-left: 8px;
  padding-right: 30px;
  border-right: 1px solid rgba(68, 68, 68, 0.2);
}
.accuracy-summary .time-span .clearfix {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.54);
}

.questions-summary {
  max-width: 712px;
  padding: 16px 50px;
  margin: 0 auto;
}
.questions-summary .question-summary-questions .correct-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #46B520;
  padding: 10px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}
.questions-summary .question-summary-questions .wrong-question {
  display: inline-block;
  width: 36px;
  height: 36px;
  text-align: center;
  color: #fff;
  background-color: #B72319;
  padding: 10px;
  margin-right: 20px;
  margin-bottom: 20px;
  border-radius: 8px;
}
.questions-summary .question-summary-help-icon {
  display: inline-block;
  width: 25px;
  height: 25px;
  background: url("../images/booksmojo/help-icon.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-indent: -9999999px;
}
.questions-summary .question-summary-help {
  position: relative;
  float: right;
}
.questions-summary .question-summary-tooltip {
  display: none;
  position: relative;
  right: 0;
  width: 232px;
  list-style: none;
  text-align: left;
  background: #fff;
  padding: 16px;
  margin: 0;
  margin-top: 30px;
  border-radius: 6px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
}
.questions-summary .question-summary-tooltip li {
  display: block;
  padding: 10px;
}
.questions-summary .question-summary-tooltip li .indicator-div {
  display: inline-block;
  width: 40px;
  height: 40px;
}
.questions-summary .question-summary-tooltip li p {
  display: inline-block;
  margin-left: 10px;
  -webkit-transform: translateY(-55%);
  -moz-transform: translateY(-55%);
  -ms-transform: translateY(-55%);
  -o-transform: translateY(-55%);
  transform: translateY(-55%);
}
.questions-summary .question-summary-tooltip li .correct {
  background: url("../images/booksmojo/correct.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.questions-summary .question-summary-tooltip li .wrong {
  background: url("../images/booksmojo/wrong.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.questions-summary .question-summary-tooltip li .skipped {
  background: url("../images/booksmojo/skipped.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.btn-review {
  display: block;
  width: 173px;
  height: 48px;
  color: #444444;
  font-size: 16px;
  font-weight: bold;
  background: #FFFFFF;
  padding: 15px;
  margin: 0 auto;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}
.btn-review:hover, .btn-review.book-desc-btn:visited, .btn-review.book-desc-btn:active, .btn-review.book-desc-btn:link {
  color: #444444 !important;
}

.modal {
  overflow: scroll;
}

.chapter-details-area {
  height: 100%;
}

.answer-holder-inner {
  float: left;
}

.correct-answer-learn {
  margin-top: 40px;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(68, 68, 68, 0.74);
}
.correct-answer-learn .correct-answer-label {
  font-size: 20px;
  font-weight: bold;
  color: #888888;
}
.correct-answer-learn .correct-answer-label .correct-answer {
  color: #444444;
  font-size: 18px;
}

.correct-answer-by-user {
  color: #46B520 !important;
  font-size: 18px;
}

.wrong-answer-by-user {
  color: #B72319 !important;
  font-size: 18px;
}

.mcq-learn {
  border-left: 0 !important;
}
.mcq-learn::before {
  content: '';
  border-left: 0 !important;
}

.show-explanation {
  float: right;
}

.correct-answer-explanation {
  display: none;
  background-color: #FCFCFC;
  padding: 24px 40px;
  float: left;
  box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
}

.modal .close {
  font-size: 50px;
}

#hideShowDiv {
  position: absolute;
  top: 90px;
  left: 0;
  width: 15px;
  height: 30px;
  display: block;
  background: url("../images/booksmojo/collapse.png");
  background-size: 100% 100%;
  background-position: center;
  text-indent: 999999999px;
}

.rotated {
  transform: rotate(180deg);
}

@media screen and (min-width: 1537px) {
  #hideShowDiv {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1536px) {
  .quiz-modal-body {
    min-height: 525px;
    max-height: 525px;
  }

  .score-container {
    min-height: 525px;
    max-height: 525px;
  }

  #htmlreadingcontent {
    margin-top: 30px;
  }

  .demo-book-wrapper a {
    min-height: 400px;
  }
}
@media screen and (max-width: 768px) {
  #hideShowDiv {
    display: none;
  }

  .submit-quiz-btn {
    width: auto;
  }

  .mcq-question-div::before {
    content: '';
    border-left: 0;
  }

  .btn-review {
    width: 100%;
  }

  .close-modal.next-btn.pull-right {
    width: 100%;
  }

  .answer-summary .score-summary .correct-answers, .answer-summary .score-summary .wrong-answers, .answer-summary .score-summary .skipped-answers {
    width: 80px;
    height: 80px;
    padding: 23px 35px 36px 34px;
  }
  .answer-summary .score-summary .wrong-answers {
    width: 80px;
    height: 80px;
    padding: 23px 35px 36px 34px;
  }
  .answer-summary .score-summary .skipped-answers {
    width: 80px;
    height: 80px;
    padding: 23px 35px 36px 34px;
  }

  .next-btn.close-modal {
    width: 100% !important;
  }

  .learn-practice {
    width: 47%;
    float: left;
  }
  .learn-practice .learn-practice-card {
    width: 100%;
    height: 200px;
  }
  .learn-practice .learn-pactice-btns {
    margin-top: 30px;
  }

  .quiz-modal-body {
    min-height: 500px;
  }
}
.question-div-mcq img {
  height: auto !important;
  width: auto !important;
  margin-top: 15px;
}

.mcqquestion img {
  height: 200px !important;
  width: 200px !important;
}

#sum-question img {
  display: block;
  height: auto !important;
  width: auto !important;
  margin-top: 15px;
  margin-left: 15px;
  float: none;
}

.exercise-score-container {
  min-height: 220px;
  color: #fff;
  text-align: center;
  background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  background: -webkit-linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  background: -o-linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  background: linear-gradient(78.97deg, #1C4F6B 0%, rgba(20, 74, 97, 0.89) 100%);
  padding: 26px 0;
}
.exercise-score-container p {
  color: #fff;
  font-size: 18px;
}
.exercise-score-container .exercise-text {
  max-width: 85px;
  display: inline-block;
  margin: 0;
  line-height: normal;
}
.exercise-score-container .exercise-scores {
  position: relative;
  display: inline-block;
  width: 120px;
  height: 120px;
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  background-color: transparent;
  margin-bottom: 20px;
  border: 2px solid #fff;
  border-radius: 16px;
}
.exercise-score-container .exercise-scores span {
  position: relative;
  top: 50%;
  display: block;
  transform: translateY(-50%);
}

.total-question {
  height: 40px;
  padding: 12px;
  margin-bottom: 20px;
  box-shadow: 0px 1px 0px rgba(0, 0, 0, 0.25);
}
.total-question .total-question-num {
  font-size: 12px;
  color: rgba(68, 68, 68, 0.74);
}
.total-question .questions {
  font-size: 12px;
  font-weight: bold;
  color: #F05A2A;
}

.take-test {
  width: 270px;
  text-align: center;
  float: none;
  margin: 0 auto;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  left: 0;
}
.take-test img {
  width: 50%;
  margin: 0 auto;
}

.no-test-text {
  font-size: 14px;
  font-weight: 300;
  color: #888;
}

@font-face {
  font-family: "wonderslate";
  src: url("../fonts/wonderslate/fonts/wonderslate.eot");
  src: url("../fonts/wonderslate/fonts/wonderslate.eot?#iefix") format("embedded-opentype"), url("../fonts/wonderslate/fonts/wonderslate.woff") format("woff"), url("../fonts/wonderslate/fonts/wonderslate.ttf") format("truetype"), url("../fonts/wonderslate/fonts/wonderslate.svg#wonderslate") format("svg");
  font-weight: normal;
  font-style: normal;
}
[data-icon]:before {
  font-family: "wonderslate" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "wonderslate" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
  content: "\61";
  vertical-align: middle;
}

.icon-checkbox-deselected:before {
  content: "\62";
  vertical-align: middle;
}

.icon-checkbox-selected:before {
  content: "\63";
  vertical-align: middle;
}

.icon-search-light:before {
  content: "\64";
  vertical-align: middle;
}

.icon-search-dark:before {
  content: "\65";
  vertical-align: middle;
}

.icon-close:before {
  content: "\66";
  vertical-align: middle;
}

.icon-comment:before {
  content: "\67";
  vertical-align: middle;
}

.icon-done:before {
  content: "\68";
  vertical-align: middle;
}

.icon-error-dark:before {
  content: "\69";
  vertical-align: middle;
}

.icon-error-light:before {
  content: "\6a";
  vertical-align: middle;
}

.icon-filter:before {
  content: "\6b";
  vertical-align: middle;
}

.icon-help:before {
  content: "\6c";
  vertical-align: middle;
}

.icon-text-format:before {
  content: "\6d";
  vertical-align: middle;
}

.icon-list:before {
  content: "\6f";
  vertical-align: middle;
}

.icon-sort:before {
  content: "\70";
  vertical-align: middle;
}

.icon-settings:before {
  content: "\71";
  vertical-align: middle;
}

.icon-radio-selected:before {
  content: "\72";
  vertical-align: middle;
}

.icon-radio-deselected:before {
  content: "\73";
  vertical-align: middle;
}

.icon-add:before {
  content: "\6e";
  vertical-align: middle;
}

.icon-bookmark:before {
  content: "\74";
  vertical-align: middle;
}

.icon-chevron:before {
  content: "\75";
  vertical-align: middle;
}

.icon-dropdown:before {
  content: "\76";
  vertical-align: middle;
}

.icon-favorite:before {
  content: "\77";
  vertical-align: middle;
}

.icon-fullscreen:before {
  content: "\78";
  vertical-align: middle;
}

.icon-grid:before {
  content: "\79";
  vertical-align: middle;
}

.icon-hamburger:before {
  content: "\7a";
  vertical-align: middle;
}

.icon-reload:before {
  content: "\41";
  vertical-align: middle;
}

.test-gen-main {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  position: relative;
  background: url("../images/booksmojo/testgen-bg.png");
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 60%;
  padding: 40px 0;
  margin-bottom: 50px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}
.test-gen-main .overlay {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.96);
  z-index: 1;
}

.test-gen-box-main {
  position: relative;
  max-width: 352px;
  margin: 0 auto;
  z-index: 2;
}
.test-gen-box-main .test-gen-box {
  height: 200px;
  background-color: #fff;
  padding: 24px 24px;
  margin-bottom: 40px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}
.test-gen-box-main .test-gen-box .test-gen-label {
  font-size: 18px;
  font-weight: 500;
}
.test-gen-box-main .test-gen-box .test-gen-desc {
  font-size: 16px;
  font-weight: 300;
}
.test-gen-box-main .test-gen-box .test-gen-btn {
  float: right;
  margin: 0;
}
.test-gen-box-main .test-gen-box .test-gen-btn a {
  display: block;
  width: 190px;
  text-align: center;
  color: #fff;
  font-size: 16px;
  background: #5EC7D7;
  padding: 15px;
  box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
}
.test-gen-box-main .test-gen-box .blank-p {
  margin: 0;
}
.test-gen-box-main .test-gen-box:last-child {
  margin-bottom: 0;
}

.test-gen-modal .modal-title {
  font-size: 22px;
}
.test-gen-modal .modal-header, .test-gen-modal .modal-footer {
  position: relative;
  z-index: 2;
}
.test-gen-modal .search-bar {
  position: relative;
  width: 330px;
  margin: 0 auto;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  z-index: 2;
}
.test-gen-modal .test-gen-books {
  position: relative;
  z-index: 2;
}
.test-gen-modal .test-gen-books ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book .book-selected {
  width: 60px;
  height: 60px;
  color: #fff;
  text-align: center;
  font-size: 32px;
  background: #F05A2A;
  margin: auto;
  border-radius: 100px;
  position: absolute;
  right: 0;
  left: 0;
  margin: auto;
  top: 0;
  bottom: 0;
  padding: 8px;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-image-wrapper {
  height: 240px;
  position: relative;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-item-test-gen {
  height: auto;
  background: #fff;
  border: 0.5px solid rgba(189, 189, 189, 0.55);
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-info {
  height: auto;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-info .book-name-author {
  height: auto;
}

.test-gen-modal-body {
  position: relative;
  width: 100%;
  min-height: 750px;
  max-height: 750px;
  background: url("../images/booksmojo/testgen-bg.png");
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 60%;
  overflow: auto;
  overflow-x: hidden;
}
.test-gen-modal-body .test-gen-chapters , .test-gen-modal-body .test-gen-chapters .test-type {
  font-size: 18px;
  color: rgba(68, 68, 68, 0.74);
}
.test-gen-chapters .book-name{
  color:#F05A2A;
}
.test-gen-modal-body .test-gen-chapters .book-name {
  max-width: 100%;
  text-align: center;
  color:#F05A2A;
}
.test-gen-modal-body .overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.96);
  z-index: 1;
}
.test-gen-modal-body .on-top-overlay {
  position: relative;
  z-index: 2;
}
.test-gen-modal-body input[type=checkbox] {
  position: absolute;
  opacity: 0;
}
.test-gen-modal-body input[type=radio] {
  position: absolute;
  opacity: 0;
}

.chapter-selection-table .chapter-selection {
  border-bottom: 1px solid rgba(189, 189, 189, 0.55);
}
.chapter-selection-table .chapter-selection .chapter-name {
  width: auto;
  position: relative;
  color: #444;
  font-size: 22px;
  padding: 16px;
  border-top: 0;
  border-bottom: 0;
}
.chapter-selection-table .chapter-selection .chapter-name label {
  cursor: pointer;
  font-size: 18px;
  color: #888;
}
.chapter-selection-table .chapter-selection .chapter-name label:hover, .chapter-selection-table .chapter-selection .chapter-name label.book-desc-btn:visited, .chapter-selection-table .chapter-selection .chapter-name label.book-desc-btn:active, .chapter-selection-table .chapter-selection .chapter-name label.book-desc-btn:link {
  color: #F05A2A;
}
.chapter-selection-table .chapter-selection .chapter-name label.active {
  font-weight: bold;
  color: #F05A2A;
}
.chapter-selection-table .chapter-selection .checkmark {
  position: absolute;
  top: 20px;
  right: 16px;
  height: 22px;
  width: 22px;
  background-color: transparent;
  border: 2px solid #888;
}
.chapter-selection-table .chapter-selection .chapter-name:hover input[type="checkbox"] ~ .checkmark, .chapter-selection-table .chapter-selection .chapter-name.book-desc-btn:visited input[type="checkbox"] ~ .checkmark, .chapter-selection-table .chapter-selection .chapter-name.book-desc-btn:active input[type="checkbox"] ~ .checkmark, .chapter-selection-table .chapter-selection .chapter-name.book-desc-btn:link input[type="checkbox"] ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
}
.chapter-selection-table .chapter-selection .chapter-name input[type="checkbox"]:checked ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
}
.chapter-selection-table .chapter-selection .chapter-name::after {
  content: '';
  position: absolute;
  display: none;
}
.chapter-selection-table .chapter-selection .chapter-name input[type="checkbox"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block;
}
.chapter-selection-table .chapter-selection .checkmark::after {
  left: 3px;
  top: 4px;
  width: 12px;
  height: 7px;
  border: solid #F05A2A;
  border-width: 3px 3px 0 0;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}
.chapter-selection-table .chapter-selection .checkmark-radio {
  position: absolute;
  top: 20px;
  right: 40px;
  height: 22px;
  width: 22px;
  background-color: transparent;
  border: 2px solid #888;
  border-radius: 50px !important;
}
.chapter-selection-table .chapter-selection .chapter-name:hover input[type="radio"] ~ .checkmark, .chapter-selection-table .chapter-selection .chapter-name.book-desc-btn:visited input[type="radio"] ~ .checkmark, .chapter-selection-table .chapter-selection .chapter-name.book-desc-btn:active input[type="radio"] ~ .checkmark, .chapter-selection-table .chapter-selection .chapter-name.book-desc-btn:link input[type="radio"] ~ .checkmark {
  background-color: transparent;
  border-radius: 50px;
  border: 2px solid #F05A2A;
}
.chapter-selection-table .chapter-selection .chapter-name input[type="radio"]:checked ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
  border-radius: 50px;
}
.chapter-selection-table .chapter-selection .chapter-name::after {
  content: '';
  position: absolute;
  display: none;
}
.chapter-selection-table .chapter-selection .chapter-name input[type="radio"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block;
}
.chapter-selection-table .chapter-selection .checkmark-radio::after {
  left: 4px;
  top: 4px;
  width: 5px;
  height: 5px;
  border: 5px solid #F05A2A;
  border-radius: 5px;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg);
}

.test-gen-modal {
  z-index: 9999;
}

@media screen and (min-width: 320px) and (max-width: 768px) {
  .test-gen-modal .test-gen-books ul .book-content-wrapper {
    margin-bottom: 0;
  }
  .test-gen-modal .test-gen-books ul .book-content-wrapper .book-image-wrapper {
    height: 150px;
  }

  .main-content .main-content-container {
    padding: 0;
  }

  .test-gen-main {
    padding-left: 15px;
    padding-right: 15px;
  }

  .test-gen-modal .search-bar {
    width: auto;
  }

  .chapter-selection-table .chapter-selection .chapter-name {
    display: block;
    border-bottom: 1px solid rgba(68, 68, 68, 0.55);
  }
}
.test-type-container {
  max-width: 492px;
}

.stpe-count {
  font-weight: normal;
  line-height: normal;
  font-size: 18px;
  text-align: center;
  color: #444444;
}

#error_message {
  max-width: 500px;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 18px;
  text-align: left;
  color: #DF4B41;
  padding: 0 8px;
}

.test-gen-books-flex {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: flex-start;
  margin-right: 0;
  margin-left: 0;
  margin-top: 32px;
}

@media screen and (min-width: 768px) and (max-width: 1536px) {
  .test-gen-modal-body {
    width: 100%;
    min-height: 525px;
    max-height: 525px;
    overflow: auto;
    overflow-x: hidden;
  }

  .answer-summary {
    min-height: 215px;
  }
}
.breadcrumbs {
  padding-top: 15px;
  margin-top: 0;
}

.btn:hover, .btn.book-desc-btn:visited, .btn.book-desc-btn:active, .btn.book-desc-btn:link {
  color: #fff;
}

.modal-backdrop {
  display: none;
}

::-webkit-scrollbar {
  width: 7px;
  height: 5px;
}

::-webkit-scrollbar-track {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover, .book-desc-btn:visited::-webkit-scrollbar-thumb, .book-desc-btn:active::-webkit-scrollbar-thumb, .book-desc-btn:link::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.05);
}

.less-width {
  width: 185px !important;
  min-height: 160px !important;
  height: 180px !important;
}

.modal {
  background: rgba(0, 0, 0, 0.8);
}

.click-here-link {
  color: #F05A2A;
}

/*# sourceMappingURL=common.css.map */
