.brand a:hover{
    text-decoration: none;
}

.brand a{
    font-family: 'Exo', sans-serif;
    font-size: 1.5em;
    color: #ffffff;
}
a{
    color: #009bff;
}
.logoblue{
    color:#28bbc6;
}
.logoorange{
    color:#f15a29
}
h3 {
    color: #f15a29;
}
.wpsidebar {
    background-color: white;
    top:10px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    border-radius: 2px;

    /*border: 1px solid rgb(220,221,224);*/
}
.greytext{
    color: #7b7a79;
}
.wpboxifyquiz {
    width:170px;
    left:0px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    margin-bottom: 30px;

}
.light0{
    background-color: #ce483d;
}
.dark0{
    background-color: #bd4036;
}
.dark0text{
    color: #bd4036;
}
.light1{
    background-color: #8eb021;
}
.light1text{
    color: #8eb021;
}
.dark1{
    background-color: #7e9d1c;
}
.dark1text{
    color: #7e9d1c;
}
.light2{
    background-color: #79302a;
}
.dark2{
    background-color: #662621;
}
.dark2text{
    color: #662621;
}
.light3{
    background-color: #ffd006;
}
.dark3{
    background-color: #ebc009;
}
.dark3text{
    color: #ebc009;
}
.light4{
    background-color: #abcbd6;
}
.dark4{
    background-color: #8cbecf;
}
.dark4text{
    color: #8cbecf;
}
.light5{
    background-color: #7bc1a1;
}
.dark5{
    background-color: #5eb990;
}
.dark5text{
    color: #5eb990;
}
.light6{
    background-color: #101e55;
}
.dark6{
    background-color: #0b163f;
}
.light7{
    background-color: #fabf28;
}
.dark7{
    background-color: #f2b51c;
}
.light8{
    background-color: #2b2423;
}
.dark8{
    background-color: #151111;
}
.light9{
    background-color: #2a70e8;
}
.light9text{
    color: #2a70e8;
}
.dark9{
    background-color: #0957db;
}
.dark9text{
    color: #0957db;
}

.light10text{
    color: #fc635e;
}

.light11{
    background-color: #ffc80b;
}
.smallText{
    font-size: 13px;
}
.smallestText{
    font-size: 10px;
}
.main {
    background-color: white;
    margin: 10px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    border-radius: 2px;
    /*border: 1px solid rgb(220,221,224);*/
}
.numberCircle {
    vertical-align: top;
    position: relative;
    display: inline-block;
    width: 60px;
    height: 60px;
    border: 1px solid #D2D2D2;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    -ms-border-radius: 50%;
    -o-border-radius: 50%;
    border-radius: 50%;
    text-align: center;
    font-size: 20px;
    line-height: 54px;
    color: #9b9b9b;
    margin-left: 20px;
}

@media only screen and (min-width: 768px) {
    .wpboxifybigchapter {
        width: 600px;
        left: 0px;
        box-shadow: 4px 4px 4px #888888;
        background-color: white;
        border-style: solid;
        border-width: 1.0px;
        border-color: #f0f0f0;
    }

    .wpboxifybigchapterinsidebox {
        position: relative;
        height: 50px;
        width: 600px;
        bottom: 0px;
        left: 0px;
        background-color: #fc635e;

    }
}

.wpboxifybigchapter {
    width: 100%;
    left: 0px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    border-style: solid;
    border-width: 1.0px;
    border-color: #f0f0f0;
}

.wpboxifybigchapterinsidebox {
    position: relative;
    height: 50px;
    width: 100%;
    bottom: 0px;
    left: 0px;
    background-color: #fc635e;

}
.fontsize16{
    font-size: 16px;
    letter-spacing: 0.4px;
    line-height: 1.2;

}
.whitetext{
    color: #ffffff;
}
.darkgrey{
    color: #333333;
}
.wpboxifyvideo {
    width:100%;
    left:0px;
    box-shadow: 4px 4px 4px #888888;
    background-color: white;
    border-style: solid;
    border-width: 1.0px;
    border-color: #f0f0f0;
    margin-bottom: 30px;
}
.wpboxifyvideo .row {
    margin-left: 0;
    margin-right: 0;

}
.wpboxifyvideoinsidebox {
    position: relative;
    height: 40px;
    width:100%;
    bottom:0px;
    left:0px;
    background-color: #fc635e;

}
.wplandingimage{
    background-image: url(../images/dotswhite.jpg);
}
.wplandingblueimage{
    background-image: url(../images/dotsblue.jpg);
}
h3,h4,h5 {
    color: #f15a29;
}

.wpsidebar {
    background-color: white;
    top:10px;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, .16),
    0 2px 10px 0 rgba(0, 0, 0, .12);
    border-radius: 2px;

    /*border: 1px solid rgb(220,221,224);*/
}
.quiz-section{
    margin-bottom: 30px;
}

.quiz-modal-header{
    background-color: #f15a29;
}

.quiz-modal-header p{
    color: white;
    font-weight: 700;
    font-size: 25px;
    margin-top:10px;
    margin-left: 10px;
    text-transform: uppercase;
}

.checkbox label, .radio label{

}

.quiz-hr{
    margin: 0px;
}

.quiz-modal-footer{
    border: none;
    padding: 15px;
}

.quiz-form{
    padding-top: 10px;
}

.red{
    color: red;
}

.green{
    color: #5cb85c;
}

.blue{
    color: blue;
}

.input-text-quiz {
    border: none;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    border-bottom-color: #bcbcbc;
    font-style: italic;
    font-weight: 700;
}

.input-text-quiz:focus{
    outline:0;
    border-bottom-color: blue;
}

.quiz-modal-body .container-fluid{
    margin-top: 35px;
    margin-bottom: 20px;

}

.radio-true, .radio-false {
    display: none;
}

.radio-true + label{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(186, 215, 125);
    border-radius: 5px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
}



.pagenumber-red{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: red;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: red;
    color: white;

}
.pagenumber-grey{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: grey;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: grey;
    color: white;
}

.pagenumber-current{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: green;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;

}
.pagenumber-green{
    height:21px;
    width:35px;
    text-align: center;
    border-width: 1px;
    border-color: green;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: green;
    color: white;
}
.pagenumber-blue{
    height:21px;
    width:21px;
    border-width: 1px;
    border-color: blue;
    border-radius: 3px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
    background-color: blue;
    color: white;
}

.radio-true:checked + label, .true-selected-box{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(186, 215, 125);
    border-radius: 5px;
    background-color: rgb(186, 215, 125);
    display: inline-block;
    padding: 0px;
}

.radio-false + label{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(241, 90, 41);
    border-radius: 5px;
    border-style: solid;
    display: inline-block;
    padding: 0px;
}

.radio-false:checked + label, .false-selected-box{
    height:31px;
    width:31px;
    border-width: 3px;
    border-color: rgb(241, 90, 41);
    border-radius: 5px;
    border-style: solid;
    background-color: rgb(241, 90, 41);
    display: inline-block;
    padding: 0px;
}
.left-border-vr{
    border: none;
    border-left-width: 2px;
    border-left-color:#8a8988;
    border-left-style: solid;
}

.sum-modal-header{
    background-color: #f15a29;
}

.sum-modal-header-text{
    color: white;
    font-weight: 700;
    font-size: 20px;
    margin-top:10px;
    margin-left: 10px;
    text-transform: uppercase;
}

.sum-modal-body {
    margin-top: 35px;
    margin-bottom: 20px;
    font-size: 17px;
}

.top-container{
    padding-top:50px;
}
.row-centered {
    text-align:center;

}

.bigtext{
    font-size: 20px;
    line-height: 1.575;
    letter-spacing: .8px;
}

.quizquestion{
    font-size: 17px;
    line-height: 1.275;
    letter-spacing: .8px;
}

.red-border{
    border: 1px solid red;
}

.rightanswer{
    background-color: #b5dca0;
}

.wronganswer{
    background-color: #88ccf3;
}
.yellowstar{
    color: brown;
}
.orange{
    color:orange;
}

.white a {
    color: #ffffff;
}

.iColor {
    border-bottom: 2px solid red;
}

.labelAlert {
    color: red;
}

.minimenu {
    width: 25px;
    cursor: pointer;
    float: left;
    padding-left: 0px;
    padding-top: 5px;
    margin-right: 20px;

}
.minimenu span {
    float: left;
    background: #000;
    width: 100%;
    height: 3px;
    margin: 3px 0px;
    position: relative;
}

.btn-primary-outline {
    background-color: transparent;
    border-color: #ccc;
}
.boxifyfaq {
    border-width: 2.0px;
    border-color: #f0f0f0;
    border-style: solid;
    border-radius: 2px;
    position: relative;
    background-color: white;
    margin-right: 10px;
}

.boxifyfaq .row{
    margin-top: 10px;
    margin-bottom: 10px;
}

.category-selection {
    width: 250px;
    margin-bottom: 15px;
}
.category-selection .dropdown-menu {
    width: 250px;
    padding: 0;
    top: -2px;
    z-index: 99999;
}
.category-selection .dropdown-menu a {
    color: #000;
    display: block;
    padding: 10px;
    text-decoration: none;
}
.category-selection .dropdown-menu a:hover {
    color: #fff;
    background-color: #359BE0;
    text-decoration: none;
}
.drpdwn-btn {
    text-align: left;
    padding: 10px;
    border: 1px solid #E5E5E5;
    background-color: #fff;
    background: url('../images/caret-icon.png');
    background-repeat: no-repeat;
    background-position: 95%;
}
.toggle-btns-list {
    margin: 0;
    padding: 0;
    margin-bottom: 15px;
    border: 1px solid #E5E5E5;
    border-radius: 4px;
    width: 250px;
}
.toggle-btns-list .toggle-btns-list-item {
    width: 100%;
    list-style: none;
    background-color: transparent;
    text-align: left;
}
.toggle-btns-list .toggle-btns-list-item a {
    width:  100%;
    display: block;
    color: #000;
    padding: 10px 15px 10px 11px;
    text-decoration: none;
    border-left: 1px solid rgba(220, 220, 220, 0.3);
}
.toggle-btns-list .toggle-btns-list-itel:first-child a {
    border: none;
}
.toggle-btns-list .toggle-btns-list-item a:hover, .toggle-btns-list .toggle-btns-list-item a.active {
    background-color: #48A3DF;
    color: #fff;
    border-radius: 4px;
}
.level-list {
    overflow: hidden;
    padding: 0;
}
.level-list option {
    padding: 10px;
}

#logo {
    padding-left: 110px;
}
#arihant-menu {
    text-align: left;
    padding-left: 80px;
    color: #888;
}
#arihant-menu li a {
  color: #888;
}
#arihant-menu li a.active {
  color: #444;
}
#wonderpublish-logo {
  padding-left: 110px;
}
#wonderpublish-menu {
  text-align: left;
  padding-left: 80px;
}
#wonderpublish-menu li {
  padding: 15px 0 0 0;
  margin-right: 15px;
}
#wonderpublish-menu li a {
  color: #888;
  font-family: 'Montserrat', sans-serif;
  font-weight: bold;
  font-size: 18px;
}
#wonderpublish-menu li a:hover {
  color: #444;
}
#wonderpublish-menu li a.active {
  color: #444;
  border-bottom: 1px solid #000;
}


#myBtn {
    display: none; /* Hidden by default */
    position: fixed; /* Fixed/sticky position */
    bottom: 20px; /* Place the button at the bottom of the page */
    right: 30px; /* Place the button 30px from the right */
    z-index: 99; /* Make sure it does not overlap */
    border: none; /* Remove borders */
    outline: none; /* Remove outline */
    background-color: red; /* Set a background color */
    color: white; /* Text color */
    cursor: pointer; /* Add a mouse pointer on hover */
    padding: 15px; /* Some padding */
    border-radius: 10px; /* Rounded corners */
}

#myBtn:hover {
    background-color: #555; /* Add a dark-grey background on hover */
}