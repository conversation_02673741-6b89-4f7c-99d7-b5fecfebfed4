body {
  font-family: '<PERSON>o', sans-serif !important;
  font-weight: 400;
  background-color: #FFFFFF;
  padding-right: 0 !important;
  overflow-x: hidden !important; }
  body p {
    font-family: 'Roboto', sans-serif !important; }
  body a {
    font-family: 'Roboto', sans-serif !important; }

body {
  overflow-x: hidden; }

.sage-header {
  background-color: #FFFFFF;
  padding: 0 80px; }

.sage-navbar {
  background-color: #FFFFFF;
  border: 0;
  z-index: 2; }
  .sage-navbar > .container-fluid {
    padding: 0; }
  .sage-navbar .navbar-brand {
    padding: 8px 15px; }

.sage-menu {
  margin-top: 26px; }
  .sage-menu > li > a {
    line-height: normal;
    font-size: 18px; }
    .sage-menu > li > a:hover {
      color: #F6A145 !important;
      text-decoration: underline; }
    .sage-menu > li > a .fa-chevron-down {
      font-size: 12px; }

.sage-head-title-row {
  margin: 0; }

.sage-head-title {
  padding: 0;
  margin-top: 16px; }
  .sage-head-title p {
    line-height: normal;
    font-size: 28px;
    font-weight: 900;
    letter-spacing: 0.04em;
    color: #FFFFFF;
    background-color: #1F419B;
    text-align: center;
    padding: 8px 80px;
    margin: 0; }
    .sage-head-title p .italic-text-span {
      font-style: italic;
      font-weight: normal; }

.login-btn-link {
  margin-bottom: -18px;
  z-index: 5; }

.login-btn-wrapper {
  position: relative;
  margin: 0;
  z-index: 3; }
  .login-btn-wrapper li a {
    font-weight: 900;
    color: #000000;
    line-height: normal;
    font-size: 14px;
    text-align: right;
    letter-spacing: 0.04em;
    text-transform: uppercase;
    padding: 0 16px !important; }
    .login-btn-wrapper li a:hover {
      background-color: transparent;
      color: #F6A145;
      text-decoration: underline; }
    .login-btn-wrapper li a:active {
      background-color: transparent;
      color: #F6A145;
      text-decoration: underline; }
    .login-btn-wrapper li a:focus {
      background-color: transparent;
      color: #F6A145;
      text-decoration: underline; }

.sage-dropdown-menu > li > a {
  padding: 8px 16px; }
.sage-body .all-container{
  display: none;
}
.sage-body .next-btn{width:auto;}
.sage-body .sage-header .login-btn-wrapper li a {
  padding: 16px 16px !important; }

.read-question-answer-wrapper li {
  list-style-type: none; }

.dflex-r {
  display: flex;
  justify-content: center; }
  .dflex-r div:first-child {
    border-right: 2px solid #1F419B; }
  .dflex-r div:last-child {
    margin-left: 1.5rem; }

.sage-body .video-btn, .sage-body .link-btn {
  display: none; }

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-overflow-scrolling: touch; }

* {
  box-sizing: border-box; }

.main-section {
  padding: 24px 80px 0;
  margin: 0; }

.registr-in-seconds-wrapper {
  padding: 0;
  margin-top: 32px; }

.registr-in-seconds {
  color: #1F419B;
  line-height: 51px;
  font-size: 48px;
  font-weight: 900;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  margin: 0 0 32px 0; }

.get-access {
  line-height: normal;
  font-size: 24px;
  letter-spacing: 0.015em;
  margin: 0 0 16px 0; }

.btn-get-started {
  line-height: normal;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  letter-spacing: 0.01em;
  text-transform: uppercase;
  background-color: #FFFFFF;
  color: #1F419B;
  width: 192px;
  text-align: center;
  padding: 16px 0;
  border-radius: 300px;
  box-shadow: 0px 0px 24px rgba(0, 0, 0, 0.25);
  outline: none; }
  .btn-get-started:hover {
    background-color: #F6A145;
    color: #FFFFFF;
    outline: none;
    box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.25); }
  .btn-get-started:active {
    background-color: #F6A145;
    color: #FFFFFF;
    outline: none;
    box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.25); }
  .btn-get-started:focus {
    background-color: #F6A145;
    color: #FFFFFF;
    outline: none !important;
    box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.25); }
  .btn-get-started:link {
    background-color: #F6A145;
    color: #FFFFFF;
    outline: none;
    box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.25); }

.main-page-wrapper {
  padding: 0 0 0 40px; }

.simplify-learning {
  position: relative;
  background-color: #F6A145;
  padding: 0 56px 8px;
  margin: -8px 0 -24px 0;
  padding-bottom: 0; }

.simplify-learning-student {
  position: relative;
  top: -80px;
  padding: 24px 40px 0;
  padding-bottom: 0;
  background-color: #FFFFFF;
  box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25);
  border-radius: 12px; }

.simplify-explained {
  padding: 0;
  margin-top: 56px; }

.changed-teaching-experience {
  line-height: normal;
  font-size: 40px;
  font-weight: 900;
  letter-spacing: 0.04em;
  margin: 0; }

.simplified-details {
  line-height: 28px;
  font-size: 20px;
  margin-top: 24px; }

.benefits {
  padding-left: 40px;
  padding-right: 0; }

.students-benefits-label {
  line-height: normal;
  font-size: 64px;
  font-weight: 900;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.08); }

.student-benefit {
  padding-left: 0;
  padding-right: 0; }

.benefits-content {
  top: -48px;
  padding-left: 0;
  padding-right: 0; }
  .benefits-content:hover .img-benefit img {
    box-shadow: 0px 0px 32px rgba(0, 0, 0, 0.25);
    -webkit-transition: all .2s ease;
    -moz-transition: all .2s ease;
    -ms-transition: all .2s ease;
    -o-transition: all .2s ease;
    transition: all .2s ease; }
    .benefits-content:hover .img-benefit img:hover {
      -webkit-transform: scale(1.1);
      -moz-transform: scale(1.1);
      -ms-transform: scale(1.1);
      -o-transform: scale(1.1);
      transform: scale(1.1); }

.img-benefit {
  position: relative;
  width: 64px;
  height: 64px;
  margin-bottom: 16px; }
  .img-benefit img {
    border-radius: 100%; }

.benefits-label {
  line-height: normal;
  font-size: 24px;
  font-weight: 900;
  letter-spacing: 0.04em; }

.benefits-text {
  line-height: normal;
  font-size: 16px; }

.instructor-content:after {
  content: "";
  border-bottom: 0; }

.author-of-books {
  padding-left: 72px;
  padding-right: 72px;
  margin-top: -64px;
  margin-bottom: 0;
  text-align: center; }
  .author-of-books > p {
    line-height: normal;
    font-size: 18px;
    letter-spacing: 0.04em;
    color: #FFFFFF;
    margin-bottom: 0; }
    .author-of-books > p > span {
      font-weight: 500; }
    .author-of-books > p > a {
      line-height: normal;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      letter-spacing: 0.01em;
      text-transform: uppercase;
      border: 2px solid #FFFFFF;
      color: #FFFFFF;
      box-sizing: border-box;
      border-radius: 300px;
      padding: 8px 24px; }
      .author-of-books > p > a:hover {
        color: #FFFFFF;
        text-decoration: none; }
      .author-of-books > p > a:active {
        color: #FFFFFF;
        text-decoration: none; }
      .author-of-books > p > a:focus {
        color: #FFFFFF;
        text-decoration: none; }

.r-tab {
  width: 80%; }
  .r-tab .r-tab-child {
    padding-right: 0; }

.user-login {
  position: fixed;
  right: 0;
  top: 0;
  width: 640px;
  height: 100%;
  background-color: #FFFFFF;
  padding: 24px;
  z-index: 1001;
  box-shadow: -4px 0px 4px rgba(0, 0, 0, 0.25);
  overflow-y: auto;
  -webkit-transform: translateX(105%);
  -moz-transform: translateX(105%);
  -ms-transform: translateX(105%);
  -o-transform: translateX(105%);
  transform: translateX(105%);
  -webkit-transition: transform .3s ease-in-out;
  -moz-transition: transform .3s ease-in-out;
  -ms-transition: transform .3s ease-in-out;
  -o-transition: transform .3s ease-in-out;
  transition: transform .3s ease-in-out; }

.user-login-slide {
  -webkit-transform: translateX(0);
  -moz-transform: translateX(0);
  -ms-transform: translateX(0);
  -o-transform: translateX(0);
  transform: translateX(0); }

.close-sidebar {
  color: #000000; }
  .close-sidebar:hover {
    color: #000000;
    text-decoration: none; }
  .close-sidebar i:hover {
    background-color: rgba(68, 68, 68, 0.12);
    border-radius: 100%; }

.sage-login {
  position: relative; }

.sage-login-signup-title {
  max-width: 230px;
  font-size: 18px;
  font-weight: 900;
  margin: 0 auto; }

.sage-login-signup-welcome {
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.21875px;
  color: rgba(68, 68, 68, 0.54);
  max-width: 320px;
  margin: 0 auto; }

.social-login {
  font-family: Roboto;
  width: 320px;
  padding: 0;
  margin: 32px auto 0;
  border-bottom: 0; }

.btn-google-login {
  display: block;
  width: 100%;
  line-height: normal;
  font-size: 14px;
  letter-spacing: 0.21875px;
  color: rgba(0, 0, 0, 0.54);
  background: url("../images/wonderslate/ic_google.svg");
  background-repeat: no-repeat;
  background-position: 4%;
  background-color: #FFFFFF;
  padding: 12px 0;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.24), 0px 0px 1px rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  outline: none; }
  .btn-google-login:hover {
    background: url("../images/wonderslate/ic_google.svg");
    background-repeat: no-repeat;
    background-position: 4%;
    outline: none !important; }
  .btn-google-login:focus {
    background: url("../images/wonderslate/ic_google.svg");
    background-repeat: no-repeat;
    background-position: 4%;
    outline: none !important; }
  .btn-google-login:active {
    background: url("../images/wonderslate/ic_google.svg");
    background-repeat: no-repeat;
    background-position: 4%;
    outline: none !important; }

.email-signup-btn {
  display: block;
  width: 100%;
  line-height: normal;
  font-size: 14px;
  letter-spacing: 0.21875px;
  color: rgba(0, 0, 0, 0.54);
  background-color: #FFFFFF;
  padding: 12px 0;
  box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.24), 0px 0px 1px rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  outline: none; }
  .email-signup-btn:hover {
    outline: none !important; }
  .email-signup-btn:focus {
    outline: none !important; }
  .email-signup-btn:active {
    outline: none !important; }

.sage-login-form {
  width: 320px;
  margin: 24px auto; }
  .sage-login-form .form-group {
    position: relative;
    margin-bottom: 16px; }
  .sage-login-form .password-form-group {
    margin-bottom: 24px; }

#forgotpassword {
  width: 320px;
  margin: 24px auto; }
  #forgotpassword .form-group {
    margin-bottom: 16px; }
  #forgotpassword .password-form-group {
    margin-bottom: 24px; }

.sage-input {
  height: 44px;
  line-height: normal;
  font-size: 15px;
  color: #000000;
  padding: 12px 0 12px 16px; }

.sage-input::-webkit-input-placeholder {
  color: rgba(68, 68, 68, 0.64); }

select.sage-input {
  color: rgba(68, 68, 68, 0.64); }

.multiselect-active {
  color: #000000 !important; }

.sage-login-btn {
  line-height: normal;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.21875px;
  color: #FFFFFF;
  background-color: #1F419B;
  padding: 0; }

.forgot-password-signup {
  width: 320px;
  margin: 16px auto;
  text-align: center; }

.forgot-or-signup {
  display: block;
  line-height: 20px;
  font-size: 14px;
  text-align: center;
  color: rgba(0, 0, 0, 0.54); }
  .forgot-or-signup:hover {
    color: #F6A145; }
  .forgot-or-signup i {
    font-size: 14px;
    vertical-align: sub; }

.user-type-selection {
  list-style: none;
  text-align: left;
  width: 320px;
  padding: 0;
  padding-bottom: 24px;
  margin: 56px auto 0; }
  .user-type-selection > li {
    line-height: normal;
    font-size: 14px;
    text-align: center;
    letter-spacing: 0.21875px;
    display: inline-block;
    width: 144px;
    height: 144px;
    background-color: #FFFFFF;
    cursor: pointer;
    padding: 16px 0;
    box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.24), 0px 0px 1px rgba(0, 0, 0, 0.12);
    border-radius: 4px; }
    .user-type-selection > li > a {
      width: 100%;
      height: 80px;
      display: block;
      text-align: center;
      color: rgba(0, 0, 0, 0.74); }
      .user-type-selection > li > a > i {
        font-size: 80px; }
    .user-type-selection > li:hover {
      color: #FFFFFF;
      background-color: #F6A145; }
      .user-type-selection > li:hover > a {
        color: #FFFFFF; }
    .user-type-selection > li.active {
      color: #FFFFFF;
      background-color: #F6A145; }
      .user-type-selection > li.active > a {
        color: #FFFFFF; }
  .user-type-selection li:first-child {
    margin-right: 24px; }

.do-not-hide {
  display: block;
  -webkit-transition: display .2;
  -moz-transition: display .2;
  -ms-transition: display .2;
  -o-transition: display .2;
  transition: display .2; }

.hr-text {
  width: 320px;
  position: relative;
  outline: 0;
  border: 0;
  color: black;
  font-size: 11px;
  text-align: center;
  height: 1.5em;
  opacity: .5;
  margin-top: 24px;
  margin-bottom: 24px; }
  .hr-text:before {
    content: '';
    background: rgba(68, 68, 68, 0.54);
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px; }
  .hr-text:after {
    content: attr(data-content);
    position: relative;
    padding: 8px 8px;
    line-height: 1.5em;
    color: rgba(68, 68, 68, 0.54);
    background-color: #FFFFFF;
    border-radius: 100%;
    font-size: 12px; }

.bottom-hr {
  display: none;
  width: 320px;
  border-top: 1px solid rgba(68, 68, 68, 0.54);
  margin: 0 auto; }

.sage-modal-header {
  border-bottom: 0; }

textarea.sage-input {
  resize: none; }

.sage-menu > .dropdown.open > a {
  background-color: #FFFFFF; }
  .sage-menu > .dropdown.open > a:focus {
    background-color: #FFFFFF; }
  .sage-menu > .dropdown.open > a:active {
    background-color: #FFFFFF; }

.full-width-input > input {
  width: 100%;
  height: 100%; }

.login-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.24);
  z-index: 1000; }

span.multiselect-native-select {
  position: relative; }

span.multiselect-native-select select {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  height: 1px !important;
  margin: -1px -1px -1px -3px !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  left: 50%;
  top: 30px; }

.multiselect-container {
  position: absolute;
  list-style-type: none;
  margin: 0;
  padding: 0; }

.multiselect-container .input-group {
  margin: 5px; }

.multiselect-container .multiselect-reset .input-group {
  width: 93%; }

.multiselect-container > li {
  padding: 0; }

.multiselect-container > li > a.multiselect-all label {
  font-weight: 700; }

.multiselect-container > li.multiselect-group label {
  margin: 0;
  padding: 3px 20px;
  height: 100%;
  font-weight: 700; }

.multiselect-container > li.multiselect-group-clickable label {
  cursor: pointer; }

.multiselect-container > li > a {
  padding: 0; }

.multiselect-container > li > a > label {
  margin: 0;
  height: 100%;
  cursor: pointer;
  font-weight: 400;
  padding: 3px 20px 3px 40px; }

.multiselect-container > li > a > label.checkbox,
.multiselect-container > li > a > label.radio {
  margin: 0; }

.multiselect-container > li > a > label > input[type=checkbox] {
  margin-bottom: 5px; }

.btn-group > .btn-group:nth-child(2) > .multiselect.btn {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px; }

.form-inline .multiselect-container label.checkbox,
.form-inline .multiselect-container label.radio {
  padding: 3px 20px 3px 40px; }

.form-inline .multiselect-container li a label.checkbox input[type=checkbox],
.form-inline .multiselect-container li a label.radio input[type=radio] {
  margin-left: -20px;
  margin-right: 0; }

.multiselect .caret {
  display: none; }

.form-group .btn-group {
  width: 100%; }
  .form-group .btn-group .multiselect.dropdown-toggle {
    width: 100% !important;
    height: 44px;
    line-height: normal;
    background-color: #FFFFFF;
    text-align: left;
    padding: 12px 0 12px 16px;
    box-shadow: none;
    border: 1px solid #cccccc;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    color: #000000;
    font-weight: normal; }
  .form-group .btn-group .multiselect-container {
    width: 100%;
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64);
    max-height: 250px;
    overflow: hidden;
    overflow-y: auto; }
    .form-group .btn-group .multiselect-container li {
      line-height: normal;
      font-size: 15px;
      background-color: #FFFFFF;
      color: rgba(68, 68, 68, 0.64); }
    .form-group .btn-group .multiselect-container a {
      line-height: normal;
      font-size: 15px;
      background-color: #FFFFFF;
      color: rgba(68, 68, 68, 0.64); }

.to-unlock {
  font-size: 32px;
  text-transform: none; }

.option-label {
  font-size: 18px; }

.show-password {
  width: 22px;
  height: 13px;
  position: absolute;
  right: 15px;
  bottom: 15px;
  background: url("../images/eye.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-indent: -5000px; }

.sage-input-select {
  height: 44px;
  line-height: normal;
  font-size: 15px;

  padding: 12px 0 12px 16px; }

.resend-otp {
  color: #2F80ED;
  position: absolute;
  right: 15px;
  bottom: 13px; }
  .resend-otp:hover {
    color: #2F80ED;
    text-decoration: none; }
  .resend-otp:focus {
    color: #2F80ED;
    text-decoration: none; }
  .resend-otp:active {
    color: #2F80ED;
    text-decoration: none; }

.r-form-edit .btn-group.open .dropdown-toggle {
  box-shadow: none; }
.r-form-edit .multiselect-container {
  width: 100%;
  height: 400px;
  overflow: auto; }
.r-form-edit .multiselect {
  background: transparent;
  box-shadow: none;
  width: 100% !important;
  text-align: left; }
.r-form-edit .r-select + .btn-group {
  width: 100%; }
.r-form-edit .countrypicker {
  border-top: 1px solid rgba(68, 68, 68, 0.24);
  border-radius: 0px;
  background: none;
  font-weight: 500;
  padding: 11px 10px;
  margin: 0;
  color: #444444; }

.book-read-tabs {
  background: #F8F8F8;
  padding-bottom: 2px;
  box-shadow: none;
  z-index: 1; }

.read-book-name {
  width: 20%;
  padding: 14px 15px 12px 25px;
  border-right: 0; }

.read-book-title {
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  font-size: 16px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.54); }

.tabs-section .chapter-tabs {
  width: 100%;
  max-width: 100%;
  padding: 4px 0; }
  .tabs-section .chapter-tabs li {
    background-color: #FFFFFF;
    min-width: 132px; }
    .tabs-section .chapter-tabs li a {
      font-style: normal;
      font-weight: bold;
      line-height: normal;
      font-size: 14px;
      text-align: center;
      letter-spacing: 0.03em;
      color: #1F419B;
      background: #FFFFFF;
      border: 1px solid #1F419B;
      box-sizing: border-box;
      border-radius: 4px; }
      .tabs-section .chapter-tabs li a:hover {
        color: #FFFFFF;
        background: #F7941D;
        border-radius: 4px;
        padding: 10px 15px;
        border: 1px solid #F7941D; }
    .tabs-section .chapter-tabs li.active a {
      color: #FFFFFF;
      background: #F7941D;
      border-radius: 4px;
      padding: 10px 15px;
      border: 0 !important;
      border: 1px solid #F7941D !important; }
.tabs-section .chapter-tabs::-webkit-scrollbar {
  width: 7px;
  height: 5px; }
.tabs-section .chapter-tabs::-webkit-scrollbar-track {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1); }
.tabs-section .chapter-tabs::-webkit-scrollbar-thumb {
  -webkit-border-radius: 5px;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.2); }
.tabs-section .chapter-tabs::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4); }
.tabs-section .chapter-tabs::-webkit-scrollbar-thumb:window-inactive {
  background: rgba(0, 0, 0, 0.05); }

.tabs-section .chapter-tabs li a:link {
  border: 1px solid; }

.read-book-chapters-wrapper {
  min-height: 470px;
  max-height: 470px;
  overflow: hidden;
  overflow-y: auto;
  padding-left: 20px; }

.read-book-chapters-wrapper::-webkit-scrollbar {
  width: 7px;
  height: 5px; }

::-webkit-scrollbar-thumb:hover, .read-book-chapters-wrapper:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.4); }

.chapter-read-material-wrapper {
  display: flex;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: flex-start;
  margin-right: 0; }

.read-book-chapters {
  /*min-height: 656px;*/
  min-height: 100% !important;
  max-height: 100%;
  width: 20%;
  background-color: #FFFFFF; }

.book-read-material {
  width: 80%;
  min-height: 0;
  max-height: 100%;
  overflow-y: hidden;
  padding-right: 0;
  margin-top: 0;
  border-left: 1px solid rgba(0, 0, 0, 0.14); }
  .book-read-material .tab-content {
    clear: both;
    float: left;
    width: 100%;
    padding-right: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.14);
    /*overflow-y: scroll;*/
    padding-bottom: 1rem;
  }

.additional-ref-section {
  align-items: flex-start; }

.additional-ref-wrapper {
  list-style: none;
  padding-left: 8px;
  box-shadow: none;
  margin-bottom: 24px; }

.additional-ref-link {
  line-height: 18px;
  font-size: 16px;
  color: #2F80ED;
  text-decoration: underline; }
  .additional-ref-link:hover {
    color: #F6A145;
    text-decoration: underline; }
  .additional-ref-link:active {
    color: #F6A145;
    text-decoration: none; }
  .additional-ref-link:focus {
    color: #F6A145;
    text-decoration: none; }

.chapter-name-active > a {
  color: #F6A145; }

.chapter-name {
  width: 100%; }
  .chapter-name:hover {
    color: #F6A145; }
.chapter-name:hover a {
  color: #F6A145; }
.chapter-name:focus a {
  color: #F6A145; }
  .chapter-name:focus {
    color: #F6A145; }
  .chapter-name:active {
    color: #F6A145; }
  /*.chapter-name > a:hover {*/
    /*color: #F6A145;*/
    /*text-decoration: underline; }*/
  .chapter-name > a:focus {
    color: #F6A145;
    text-decoration: none; }
  .chapter-name > a:active {
    color: #F6A145;
    text-decoration: none; }

.btn-sort {
  text-align: left;
  width: 100%;
  border: 1px solid #1F419B;
  box-sizing: border-box;
  border-radius: 4px; }
  .btn-sort > i {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg); }

.read-book-container {
  min-height: 0;
  position: relative;
  padding-right: 0;
  border: 1px solid rgba(0, 0, 0, 0.14);
  border-top: 0; }

.resource-locked-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) -56.59%, #FFFFFF 43.18%);
  z-index: 99; }

.locked-resource-overlay-content {
  position: relative;
  top: 25%;
  max-width: 435px;
  margin: 0 auto;
  transform: translateY(-50%); }

.banner-book-name {
  position: absolute;
  left: 15px;
  bottom: 60px;
  min-width: 390px;
  min-height: 114px;
  background: rgba(0, 0, 0, 0.54);
  padding: 20px;

}

.banner-book-title {
  line-height: normal;
  font-size: 22px;
  letter-spacing: 0.02em;
  text-transform: uppercase;
  color: #FFFFFF;
  font-weight: 900; }

.banner-book-author {
  line-height: normal;
  font-size: 19px;
  letter-spacing: 0.02em;
  text-transform: uppercase;
  color: #F6A145; }

[data-icon]:before {
  font-family: "wonderslate" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

[class^="icon-"]:before,
[class*=" icon-"]:before {
  font-family: "wonderslate" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.icon-back:before {
  content: "\61";
  vertical-align: middle; }

.icon-checkbox-deselected:before {
  content: "\62";
  vertical-align: middle; }

.icon-checkbox-selected:before {
  content: "\63";
  vertical-align: middle; }

.icon-search-light:before {
  content: "\64";
  vertical-align: middle; }

.icon-search-dark:before {
  content: "\65";
  vertical-align: middle; }

.icon-close:before {
  content: "\66";
  vertical-align: middle; }

.icon-comment:before {
  content: "\67";
  vertical-align: middle; }

.icon-done:before {
  content: "\68";
  vertical-align: middle; }

.icon-error-dark:before {
  content: "\69";
  vertical-align: middle; }

.icon-error-light:before {
  content: "\6a";
  vertical-align: middle; }

.icon-filter:before {
  content: "\6b";
  vertical-align: middle; }

.icon-help:before {
  content: "\6c";
  vertical-align: middle; }

.icon-text-format:before {
  content: "\6d";
  vertical-align: middle; }

.icon-list:before {
  content: "\6f";
  vertical-align: middle; }

.icon-sort:before {
  content: "\70";
  vertical-align: middle; }

.icon-settings:before {
  content: "\71";
  vertical-align: middle; }

.icon-radio-selected:before {
  content: "\72";
  vertical-align: middle; }

.icon-radio-deselected:before {
  content: "\73";
  vertical-align: middle; }

.icon-add:before {
  content: "\6e";
  vertical-align: middle; }

.icon-bookmark:before {
  content: "\74";
  vertical-align: middle; }

.icon-chevron:before {
  content: "\75";
  vertical-align: middle; }

.icon-dropdown:before {
  content: "\76";
  vertical-align: middle; }

.icon-favorite:before {
  content: "\77";
  vertical-align: middle; }

.icon-fullscreen:before {
  content: "\78";
  vertical-align: middle; }

.icon-grid:before {
  content: "\79";
  vertical-align: middle; }

.icon-hamburger:before {
  content: "\7a";
  vertical-align: middle; }

.icon-reload:before {
  content: "\41";
  vertical-align: middle; }

.test-gen-main {
  font-family: 'Montserrat', sans-serif;
  font-style: normal;
  position: relative;
  background: url("../images/booksmojo/testgen-bg.png");
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 60%;
  padding: 40px 0;
  margin-bottom: 50px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
  border-radius: 4px; }
  .test-gen-main .overlay {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.96);
    z-index: 1; }

.test-gen-box-main {
  position: relative;
  max-width: 352px;
  margin: 0 auto;
  z-index: 2; }
  .test-gen-box-main .test-gen-box {
    height: 200px;
    background-color: #fff;
    padding: 24px 24px;
    margin-bottom: 40px;
    box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.3);
    border-radius: 4px; }
    .test-gen-box-main .test-gen-box .test-gen-label {
      font-size: 18px;
      font-weight: 500; }
    .test-gen-box-main .test-gen-box .test-gen-desc {
      font-size: 16px;
      font-weight: 300; }
    .test-gen-box-main .test-gen-box .test-gen-btn {
      float: right;
      margin: 0; }
      .test-gen-box-main .test-gen-box .test-gen-btn a {
        display: block;
        width: 190px;
        text-align: center;
        color: #fff;
        font-size: 16px;
        background: #5EC7D7;
        padding: 15px;
        box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25), 0px 2px 2px rgba(0, 0, 0, 0.25);
        border-radius: 4px; }
    .test-gen-box-main .test-gen-box .blank-p {
      margin: 0; }
  .test-gen-box-main .test-gen-box:last-child {
    margin-bottom: 0; }

.test-gen-modal .modal-title {
  font-size: 22px; }
.test-gen-modal .modal-header, .test-gen-modal .modal-footer {
  position: relative;
  z-index: 2; }
.test-gen-modal .search-bar {
  position: relative;
  width: 330px;
  margin: 0 auto;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  z-index: 2; }
.test-gen-modal .test-gen-books {
  position: relative;
  z-index: 2; }
  .test-gen-modal .test-gen-books ul {
    list-style: none;
    padding: 0;
    margin: 0; }
    .test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book {
      display: none;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.5); }
      .test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book .book-selected {
        width: 60px;
        height: 60px;
        color: #fff;
        text-align: center;
        font-size: 40px;
        background: #F05A2A;
        margin: auto;
        border-radius: 100px;
        position: absolute;
        right: 0;
        left: 0;
        margin: auto;
        top: 0;
        bottom: 0;
        padding: 8px; }
    .test-gen-modal .test-gen-books ul .book-content-wrapper .book-image-wrapper {
      height: 240px;
      position: relative; }
    .test-gen-modal .test-gen-books ul .book-content-wrapper .book-item-test-gen {
      height: auto;
      background: #fff;
      border: 0.5px solid rgba(189, 189, 189, 0.55);
      box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
      border-radius: 6px; }
    .test-gen-modal .test-gen-books ul .book-content-wrapper .book-info {
      height: auto; }
      .test-gen-modal .test-gen-books ul .book-content-wrapper .book-info .book-name-author {
        height: auto; }

.test-gen-modal-body {
  position: relative;
  width: 100%;
  /*max-height: 750px;*/
  background: url("../images/booksmojo/testgen-bg.png");
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 60%;
  overflow: auto;
  overflow-x: hidden; }
  .test-gen-modal-body .test-gen-chapters .book-name, .test-gen-modal-body .test-gen-chapters .test-type {
    font-size: 18px;
    color: rgba(68, 68, 68, 0.74); }
  .test-gen-modal-body .test-gen-chapters .book-name {
    max-width: 100%;
    text-align: center; }
  .test-gen-modal-body .overlay {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: rgba(255, 255, 255, 0.96);
    z-index: 1; }
  .test-gen-modal-body .on-top-overlay {
    position: relative;
    z-index: 2; }
  .test-gen-modal-body input[type=checkbox] {
    position: absolute;
    opacity: 0; }
  .test-gen-modal-body input[type=radio] {
    position: absolute;
    opacity: 0; }

.chapter-selection-table .chapter-name {
  width: auto;
  position: relative;
  color: #444;
  font-size: 22px;
  padding: 16px; }
  .chapter-selection-table .chapter-name label {
    cursor: pointer;
    font-size: 18px;
    color: #888; }
    .chapter-selection-table .chapter-name label:hover {
      color: #F05A2A; }
  .chapter-selection-table .chapter-name label.active {
    font-weight: bold;
    color: #F05A2A; }
.chapter-selection-table .checkmark {
  position: absolute;
  top: 20px;
  right: 16px;
  height: 22px;
  width: 22px;
  background-color: transparent;
  border: 2px solid #888; }
.chapter-selection-table .chapter-name:hover input[type="checkbox"] ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A; }
.chapter-selection-table .chapter-name input[type="checkbox"]:checked ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A; }
.chapter-selection-table .chapter-name::after {
  content: '';
  position: absolute;
  display: none; }
.chapter-selection-table .chapter-name input[type="checkbox"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block; }
.chapter-selection-table .checkmark::after {
  left: 3px;
  top: 4px;
  width: 12px;
  height: 7px;
  border: solid #F05A2A;
  border-width: 3px 3px 0 0;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg); }
.chapter-selection-table .checkmark-radio {
  position: absolute;
  top: 20px;
  right: 40px;
  height: 22px;
  width: 22px;
  background-color: transparent;
  border: 2px solid #888;
  border-radius: 50px !important; }
.chapter-selection-table .chapter-name:hover input[type="radio"] ~ .checkmark {
  background-color: transparent;
  border-radius: 50px;
  border: 2px solid #F05A2A; }
.chapter-selection-table .chapter-name input[type="radio"]:checked ~ .checkmark {
  background-color: transparent;
  border: 2px solid #F05A2A;
  border-radius: 50px; }
.chapter-selection-table .chapter-name::after {
  content: '';
  position: absolute;
  display: none; }
.chapter-selection-table .chapter-name input[type="radio"]:checked ~ .checkmark::after {
  content: '';
  position: absolute;
  display: block; }
.chapter-selection-table .checkmark-radio::after {
  left: 4px;
  top: 4px;
  width: 5px;
  height: 5px;
  border: 5px solid #F05A2A;
  border-radius: 5px;
  -webkit-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  transform: rotate(135deg); }
.chapter-selection-table .chapter-selection {
  border-bottom: 1px solid rgba(189, 189, 189, 0.55); }
  .chapter-selection-table .chapter-selection .chapter-name {
    width: auto;
    position: relative;
    color: #444;
    font-size: 22px;
    padding: 16px; }
    .chapter-selection-table .chapter-selection .chapter-name label {
      cursor: pointer;
      font-size: 18px;
      color: #888;
    width:95%;
    }
      .chapter-selection-table .chapter-selection .chapter-name label:hover {
        color: #F05A2A; }
    .chapter-selection-table .chapter-selection .chapter-name label.active {
      font-weight: bold;
      color: #F05A2A; }
  .chapter-selection-table .chapter-selection .checkmark {
    position: absolute;
    top: 20px;
    right: 8px;
    height: 22px;
    width: 22px;
    background-color: transparent;
    border: 2px solid #888; }
  .chapter-selection-table .chapter-selection .chapter-name:hover input[type="checkbox"] ~ .checkmark {
    background-color: transparent;
    border: 2px solid #F05A2A; }
  .chapter-selection-table .chapter-selection .chapter-name input[type="checkbox"]:checked ~ .checkmark {
    background-color: transparent;
    border: 2px solid #F05A2A; }
  .chapter-selection-table .chapter-selection .chapter-name::after {
    content: '';
    position: absolute;
    display: none; }
  .chapter-selection-table .chapter-selection .chapter-name input[type="checkbox"]:checked ~ .checkmark::after {
    content: '';
    position: absolute;
    display: block; }
  .chapter-selection-table .chapter-selection .checkmark::after {
    left: 3px;
    top: 4px;
    width: 12px;
    height: 7px;
    border: solid #F05A2A;
    border-width: 3px 3px 0 0;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg); }
  .chapter-selection-table .chapter-selection .checkmark-radio {
    position: absolute;
    top: 20px;
    right: 40px;
    height: 22px;
    width: 22px;
    background-color: transparent;
    border: 2px solid #888;
    border-radius: 50px !important; }
  .chapter-selection-table .chapter-selection .chapter-name:hover input[type="radio"] ~ .checkmark {
    background-color: transparent;
    border-radius: 50px;
    border: 2px solid #F05A2A; }
  .chapter-selection-table .chapter-selection .chapter-name input[type="radio"]:checked ~ .checkmark {
    background-color: transparent;
    border: 2px solid #F05A2A;
    border-radius: 50px; }
  .chapter-selection-table .chapter-selection .chapter-name::after {
    content: '';
    position: absolute;
    display: none; }
  .chapter-selection-table .chapter-selection .chapter-name input[type="radio"]:checked ~ .checkmark::after {
    content: '';
    position: absolute;
    display: block; }
  .chapter-selection-table .chapter-selection .checkmark-radio::after {
    left: 4px;
    top: 4px;
    width: 5px;
    height: 5px;
    border: 5px solid #F05A2A;
    border-radius: 5px;
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg); }

.test-gen-modal {
  z-index: 9999; }

@media screen and (min-width: 320px) and (max-width: 768px) {
  .banner-book-name{
    height: 100%;
  }
  .navbar-default .navbar-toggle .collapsed {
    background: #f05a2a; }

  .tabs-section .chapter-tabs li:last-child {
    padding-right: 10px; }

  .read-question p {
    text-align: left !important; }

  .read-answer p {
    text-align: left !important; }

  .chapter-selection-table .chapter-selection .checkmark-radio {
    right: 20px !important; }

  .test-gen-modal .test-gen-books ul .book-content-wrapper {
    margin-bottom: 0; }
    .test-gen-modal .test-gen-books ul .book-content-wrapper .book-image-wrapper {
      height: 150px; }

  #related-books-sage {
    text-align: center; }

  .related-books-carousel-item {
    margin-right: 0; }

  .main-content .main-content-container {
    padding: 0; }

  .test-gen-main {
    padding-left: 15px;
    padding-right: 15px; }

  .test-gen-modal .search-bar {
    width: auto; }

  .chapter-selection-table .chapter-selection .chapter-name {
    display: block;
    border-bottom: 1px solid rgba(68, 68, 68, 0.55); } }
.test-type-container {
  max-width: 492px; }

.stpe-count {
  font-weight: normal;
  line-height: normal;
  font-size: 18px;
  text-align: center;
  color: #444444; }

#error_message {
  max-width: 500px;
  font-style: normal;
  font-weight: normal;
  line-height: normal;
  font-size: 18px;
  text-align: left;
  color: #DF4B41;
  padding: 0 8px; }

.test-gen-books-flex {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  justify-content: flex-start;
  margin-right: 0;
  margin-left: 0;
  margin-top: 32px; }
.book-info p{
  color: rgba(68, 68, 68, 0.74);
}
.book-info .book-name {
  font-weight: 500;
  line-height: 19px;
  text-align: left;
  letter-spacing: 0.01em;
color:#444444;
  padding: 5px;
}
.book-info .book-description{
  color: rgba(68, 68, 68, 0.74);
}
.book-info .author-name{
  color:#444444;
}
.book-info .published-date{
  color: rgba(68, 68, 68, 0.74);
}
.book-item-link:hover {
  text-decoration: none; }

.book-info .book-name {
  max-width: 100%;
                          /*overflow: hidden;*/
                          /*text-overflow: ellipsis;*/
                          /*display: -webkit-box;*/
                          /*-webkit-line-clamp: 2;*/
                          /*-webkit-box-orient: vertical;*/
  margin-bottom: 4; }

.book-info .book-description {
  font-size: 14px;
  font-weight: 300;
  color: rgba(68, 68, 68, 0.74);
  margin-bottom: 4; }
.book-info .author-name {
  color: #444444;
  font-size: 14px;
  font-weight: 500;
  line-height: 19px;
  text-align: left;
  letter-spacing: 0.01em;
  margin-bottom: 4px; }
.book-info .published-date {
  font-size: 14px;
  font-weight: 300;
  margin-bottom: 4; }

.book-image {
  display: block;
  width: 100%;
  max-width: 100%;
  height: 100%;
  border: 0;
  box-shadow: none !important; }

@media screen and (min-width: 768px) and (max-width: 1536px) {
  .test-gen-modal-body {
    width: 100%;
    min-height: 525px;
    max-height: 525px;
    overflow: auto;
    overflow-x: hidden; }

  .answer-summary {
    min-height: 215px; } }
.related-books-carousel-item {
  display: inline-block;
  margin-right: 40px; }

.related-books-label {
  clear: both;
  float: left;
  width: 100%;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  font-size: 16px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 24px;
  margin-top: 24px; }

.at-end-read-more {
  margin-bottom: 20px; }

#related-books-sage {
  clear: both;
  padding: 0 24px; }

.sort-by-dropdown {
  top: 0;
  right: 0;
  left: auto; }

.book-wrapper {
  width: 175px;
  margin-top: 40px; }
  .book-wrapper:hover {
    border: 0;
    box-shadow: none; }

.skip-add-info {
  line-height: normal;
  font-weight: 500;
  font-size: 14px;
  text-align: center;
  letter-spacing: 0.21875px;
  color: rgba(0, 0, 0, 0.54);
  cursor: pointer; }
  .skip-add-info:hover {
    color: #F6A145; }

.provide-add-info {
  line-height: normal;
  font-size: 13px;
  letter-spacing: 0.21875px;
  color: rgba(0, 0, 0, 0.54); }

.add-info-form {
  width: 100%;
  margin-bottom: 0; }

.chapter-name > a {
  font-weight: normal; }

.chapter-name-active {
  color: #F6A145; }
  .chapter-name-active > a {
    color: #F6A145;
    font-weight: 500; }

.chapter-overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2; }

.instructor-tab-content-label {
  line-height: 21px;
  font-size: 16px;
  letter-spacing: 0.01em;
  color: #444444;
  margin-top: 32px; }

.description-list-wrapper {
  width: 100%;
  list-style: none;
  padding: 0;
  margin-top: 24px; }
  .description-list-wrapper .description-list-item {
    margin-bottom: 16px; }
    .description-list-wrapper .description-list-item .description-list-item-link {
      display: block;
      line-height: 18px;
      font-size: 16px;
      text-transform: capitalize;
      color: #444444;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.54); }
      .description-list-wrapper .description-list-item .description-list-item-link > i {
        color: rgba(31, 65, 155, 0.74);
        float: right;
        font-size: 22px; }
      .description-list-wrapper .description-list-item .description-list-item-link:hover {
        color: #F6A145;
        text-decoration: none;
        border-bottom: 1px solid #F6A145; }
        .description-list-wrapper .description-list-item .description-list-item-link:hover i {
          color: #F6A145; }
      .description-list-wrapper .description-list-item .description-list-item-link:active {
        color: #F6A145;
        text-decoration: none;
        border-bottom: 1px solid #F6A145; }
      .description-list-wrapper .description-list-item .description-list-item-link:focus {
        color: #F6A145;
        text-decoration: none;
        border-bottom: 1px solid #F6A145; }
    .description-list-wrapper .description-list-item .description-list-item-btn {
      display: block;
      line-height: 18px;
      font-size: 14px;
      font-weight: 900;
      text-align: center;
      text-transform: uppercase;
      color: #1F419B;
      border: 2px solid #1F419B;
      border-radius: 4px;
      padding: 8px 16px; }
      .description-list-wrapper .description-list-item .description-list-item-btn:hover {
        background-color: #F6A145;
        color: #fff;
        border: 2px solid #F6A145;
        text-decoration: none; }
      .description-list-wrapper .description-list-item .description-list-item-btn:active {
        background-color: #F6A145;
        color: #fff;
        border: 2px solid #F6A145;
        text-decoration: none; }
      .description-list-wrapper .description-list-item .description-list-item-btn:focus {
        background-color: #F6A145;
        color: #fff;
        border: 2px solid #F6A145;
        text-decoration: none; }

.resources-link-wrapper {
  list-style: none;
  margin-top: 24px; }
  .resources-link-wrapper .resources-link-item {
    line-height: 21px;
    font-size: 16px;
    letter-spacing: 0.01em;
    color: #1F419B;
    margin-bottom: 24px; }
    .resources-link-wrapper .resources-link-item .resources-link {
      line-height: 21px;
      font-size: 16px;
      letter-spacing: 0.01em;
      color: #1F419B; }
      .resources-link-wrapper .resources-link-item .resources-link:hover {
        color: #F6A145;
        text-decoration: underline; }
      .resources-link-wrapper .resources-link-item .resources-link:active {
        color: #F6A145;
        text-decoration: underline; }
      .resources-link-wrapper .resources-link-item .resources-link:focus {
        color: #F6A145;
        text-decoration: underline; }

.quick-links-label {
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  font-size: 16px;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: rgba(0, 0, 0, 0.54);
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #1F419B; }

.student-description-list-wrapper {
  padding-left: 16px;
  padding-right: 16px; }
  .student-description-list-wrapper .description-list-item .description-list-item-link {
    border-bottom: 0; }
    .student-description-list-wrapper .description-list-item .description-list-item-link:hover {
      color: #F6A145;
      border-bottom: 0; }
    .student-description-list-wrapper .description-list-item .description-list-item-link:active {
      color: #F6A145;
      border-bottom: 0; }
    .student-description-list-wrapper .description-list-item .description-list-item-link:focus {
      color: #F6A145;
      border-bottom: 0; }

.description-list-wrapper-instructor-page {
  position: relative; }

.questions-container-wrapper {
  margin-left: 0;
  margin-right: 0; }

.read-question-answer-wrapper {
  font-family: 'Roboto', sans-serif !important;
  padding-left: 24px;
  padding-right: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.14); }
  .read-question-answer-wrapper p {
    font-family: 'Roboto', sans-serif !important; }
  .read-question-answer-wrapper span {
    font-family: 'Roboto', sans-serif !important; }
  .read-question-answer-wrapper a {
    font-family: 'Roboto', sans-serif !important; }
  .read-question-answer-wrapper li {
    font-family: 'Roboto', sans-serif !important; }
  .read-question-answer-wrapper h1, .read-question-answer-wrapper h2, .read-question-answer-wrapper h3, .read-question-answer-wrapper h4, .read-question-answer-wrapper h5, .read-question-answer-wrapper h6 {
    font-family: 'Roboto', sans-serif !important; }
  .read-question-answer-wrapper table {
    font-family: 'Roboto', sans-serif !important; }
    .read-question-answer-wrapper table th {
      font-family: 'Roboto', sans-serif !important; }
    .read-question-answer-wrapper table td {
      font-family: 'Roboto', sans-serif !important; }
  .read-question-answer-wrapper > li {
    position: relative; }
  .read-question-answer-wrapper .sage-read-more {
    color: #1F419B;
    font-style: normal;
    font-weight: normal;
    line-height: 18px;
    font-size: 16px;
    text-decoration: underline;
    position: absolute;
    bottom: 0;
    right: 0;
    text-align: right;
    text-decoration: none;
    margin-bottom: 8px; }
    .read-question-answer-wrapper .sage-read-more:hover {
      color: #1F419B;
      text-decoration: none; }
    .read-question-answer-wrapper .sage-read-more:active {
      color: #1F419B;
      text-decoration: none; }
    .read-question-answer-wrapper .sage-read-more:focus {
      color: #1F419B;
      text-decoration: none; }

.read-question-answer-wrapper:last-child {
  border-bottom: 0; }

.read-question-answer {
  float: left;
  width: 100%;
  padding: 0;
  margin: 0;
  margin-left: 0; }
  .read-question-answer li {
    float: left;
    font-style: normal;
    font-weight: bold;
    line-height: 18px;
    font-size: 16px;
    padding: 16px 0;
    border-bottom: 0; }
    .read-question-answer li p {
      margin: 0;
      margin-bottom: 1rem;
      margin-left:10px !important;
    }
    .read-question-answer li .read-question {
      margin-bottom: 16px; }
    .read-question-answer li .read-answer {
      font-style: normal;
      font-weight: normal;
      line-height: 18px;
      font-size: 16px;
      margin-bottom: 16px; }
    .read-question-answer li .answer-collapsed {
      max-height: 50px;
      overflow: hidden;
      -webkit-transition: all .2s ease;
      -moz-transition: all .2s ease;
      -ms-transition: all .2s ease;
      -o-transition: all .2s ease;
      transition: all .2s ease; }

.quiz-item-wrapper {
  background: #FFFFFF;
  border: 0.5px solid rgba(189, 189, 189, 0.55);
  box-sizing: border-box;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px; }

.quiz-buttons {
  text-align: center; }

.quiz-learn-btn {
  max-width: 122px;
  color: #FFFFFF;
  background: #1F419B;
  padding: 8px 0;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
  border-radius: 2px; }
  .quiz-learn-btn:hover {
    color: #FFFFFF; }
  .quiz-learn-btn:active {
    color: #FFFFFF; }
  .quiz-learn-btn:focus {
    color: #FFFFFF; }

.practice-dropdown {
  top: 75%;
  background: #FFFFFF;
  box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.25);
  border-radius: 2px; }

.sage-footer {
  position: relative;
  background-color: #FFFFFF;
  padding: 40px 64px 16px; }

.footer-wrapper {
  padding-left: 0;
  padding-right: 0;
  margin: 0; }

.footer-content-wrapper {
  padding-left: 0;
  padding-right: 0; }

.footer-link-label {
  line-height: normal;
  font-size: 15px;
  font-weight: 900;
  color: #1F419B;
  margin-top: 2px;
  margin-bottom: 16px; }

.footer-content.details-contact {
  padding-left: 42px; }

.footer-content {
  margin: 0 auto; }

.footer-link {
  line-height: normal;
  font-size: 12px;
  color: #000000;
  float: left;
  clear: both;
  margin-bottom: 8px; }
  .footer-link:hover {
    color: #F6A145;
    text-decoration: underline; }
  .footer-link:focus {
    color: #F6A145;
    text-decoration: underline; }
  .footer-link:active {
    color: #F6A145;
    text-decoration: underline; }

.head-office-label {
  line-height: normal;
  font-size: 12px;
  font-weight: 900; }

.head-office-address {
  line-height: normal;
  font-size: 12px;
  margin: 0; }

.head-office-email {
  color: #000000;
  line-height: normal;
  font-size: 12px;
  font-weight: 900; }
  .head-office-email:hover {
    color: #000000;
    text-decoration: none; }
  .head-office-email:focus {
    color: #000000;
    text-decoration: none; }
  .head-office-email:active {
    color: #000000;
    text-decoration: none; }

.blue-color-hr {
  position: absolute;
  left: 0;
  width: 100%;
  height: 24px;
  background-color: #1F419B; }

.min-footer {
  padding: 2rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center; }
  .min-footer div {
    display: flex; }
    .min-footer div a {
      padding: 0px 10px; }
      .min-footer div a.footer-link:first-child {
        border-right: 1px solid #4a4a4a; }
    .min-footer div.copyright a {
      font-size: 12px; }
      .min-footer div.copyright a:hover {
        text-decoration: none;
        color: #4a4a4a; }

.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  vertical-align: middle;
  z-index: 1;
  transition: .3s ease-out; }
  .waves-effect .waves-ripple {
    position: absolute;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    margin-top: -10px;
    margin-left: -10px;
    opacity: 0;
    background: rgba(0, 0, 0, 0.2);
    transition: all 0.7s ease-out;
    transition-property: transform, opacity;
    transform: scale(0);
    pointer-events: none; }
  .waves-effect.waves-light .waves-ripple {
    background-color: rgba(255, 255, 255, 0.45); }
  .waves-effect.waves-red .waves-ripple {
    background-color: rgba(244, 67, 54, 0.7); }
  .waves-effect.waves-yellow .waves-ripple {
    background-color: rgba(255, 235, 59, 0.7); }
  .waves-effect.waves-orange .waves-ripple {
    background-color: rgba(255, 152, 0, 0.7); }
  .waves-effect.waves-purple .waves-ripple {
    background-color: rgba(156, 39, 176, 0.7); }
  .waves-effect.waves-green .waves-ripple {
    background-color: rgba(76, 175, 80, 0.7); }
  .waves-effect.waves-teal .waves-ripple {
    background-color: rgba(0, 150, 136, 0.7); }
  .waves-effect input[type="button"], .waves-effect input[type="reset"], .waves-effect input[type="submit"] {
    border: 0;
    font-style: normal;
    font-size: inherit;
    text-transform: inherit;
    background: none; }
  .waves-effect img {
    position: relative;
    z-index: -1; }

.waves-notransition {
  transition: none !important; }

.waves-circle {
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%); }

.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom; }
  .waves-input-wrapper .waves-button-input {
    position: relative;
    top: 0;
    left: 0;
    z-index: 1; }

.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
  -webkit-mask-image: none; }

.waves-block {
  display: block; }

/* Firefox Bug: link not triggered */
.waves-effect .waves-ripple {
  z-index: -1; }

@media screen and (min-width: 320px) and (max-width: 768px) {
  .book-read-material {
    overflow: hidden;
    overflow-y: auto; }

  .read-question-answer .sage-read-more {
    color: #1F419B;
    font-style: normal;
    font-weight: normal;
    line-height: 18px;
    font-size: 16px;
    text-decoration: underline;
    position: absolute;
    bottom: 0;
    right: 0;
    text-align: right;
    text-decoration: none;
    margin-bottom: 8px; }
    .read-question-answer .sage-read-more:hover {
      color: #1F419B;
      text-decoration: none; }
    .read-question-answer .sage-read-more:active {
      color: #1F419B;
      text-decoration: none; }
    .read-question-answer .sage-read-more:focus {
      color: #1F419B;
      text-decoration: none; }

  .banner-wrapper {
    min-height: 114px; }
    .banner-wrapper > img {
      min-height: 114px; }
    .banner-wrapper .banner-book-name {
      min-width: 100%;
      min-height: 114px;
      bottom: 0;
      left: 0; }

  .tabs-section {
    width: 100%;
    padding-left: 0;
    padding-right: 0; }

  .no-scroll {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden; }

  .sage-menu > li > a {
    font-size: 15px; }

  .login-btn-wrapper li a {
    font-size: 13px; }

  .sage-navbar .navbar-brand {
    padding: 8px; }
    .sage-navbar .navbar-brand img {
      width: auto;
      height: 100%; }

  .sage-header {
    padding: 0 18px; }

  .login-btn-link {
    text-align: right;
    padding: 0;
    margin-bottom: 8px; }
    .login-btn-link li {
      display: inline-block; }

  .sage-dropdown-menu {
    background-color: rgba(0, 0, 0, 0.03) !important; }

  .sage-head-title {
    margin-top: 16px; }
    .sage-head-title p {
      font-size: 18px;
      padding: 8px 16px; }

  .main-section {
    padding: 24px 60px 16px 16px; }

  .registr-in-seconds-wrapper {
    margin: 0; }

  .registr-in-seconds {
    font-size: 29px;
    line-height: 32px; }

  .get-access {
    font-size: 16px; }

  .btn-get-started {
    width: 170px;
    font-size: 16px;
    padding: 8px 0; }

  .simplify-learning {
    padding: 0 16px 16px;
    margin-top: 80px; }

  .simplify-learning-student {
    top: -56px;
    padding: 16px;
    padding-bottom: 0;
    box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.25); }

  .simplify-explained {
    margin-top: 0; }

  .changed-teaching-experience {
    font-size: 20px;
    margin: 0; }

  .simplified-details {
    font-size: 14px;
    line-height: 20px; }

  .benefits {
    padding: 0; }

  .students-benefits-label {
    font-size: 32px; }

  .benefits-content {
    top: -24px;
    padding-right: 8px; }

  .benefits-content:last-child {
    padding-left: 8px !important; }

  .img-benefit {
    width: 40px;
    height: 40px; }

  .benefits-label {
    font-size: 13px; }

  .benefits-text {
    font-size: 12px; }

  .student-benefit:after {
    bottom: 16px; }

  .author-of-books {
    padding: 0;
    margin-top: -40px;
    margin-bottom: 24px; }
    .author-of-books > p {
      float: left;
      max-width: 276px;
      font-size: 12px; }
    .author-of-books .mobile-click-here {
      display: inline-block;
      width: 96px;
      text-align: center;
      font-size: 12px;
      border: 1px solid #FFFFFF;
      color: #FFFFFF;
      padding: 8px 16px;
      margin-left: 8px;
      box-sizing: border-box;
      border-radius: 300px; }

  .sage-footer {
    padding: 40px 32px 24px; }

  .footer-content.details-contact {
    padding-left: 0; }

  .footer-content-wrapper:last-child {
    margin-top: 24px; }

  .footer-content-wrapper:nth-child(3) {
    padding-left: 12px; }
    .footer-content-wrapper:nth-child(3):after {
      border-right: 0; }

  .footer-link {
    font-size: 11px; }

  .user-login {
    width: 100%;
    padding: 8px; }

  .social-login {
    width: 80%; }

  .sage-login-form {
    width: 80%; }

  .forgot-password-signup {
    width: 80%; }

  .forgot-or-signup {
    font-size: 13px; }

  .sage-login {
    top: 0;
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0); }

  .user-type-selection {
    width: 100%;
    margin: 24px auto 0;
    text-align: center; }
    .user-type-selection li:first-child {
      margin-right: 12px; }

  .bottom-hr {
    width: 80%; }

  .hr-text {
    width: 80%; }

  .footer-content-wrapper:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 54px;
    border-right: 1px solid rgba(68, 68, 68, 0.54); }

  .footer-content-wrapper:last-child:after {
    content: '';
    border-right: 0; }

  .footer-content.information-content {
    max-width: 88px; }

  .footer-content.details-content {
    max-width: 111px; }

  .read-book-chapters {
    top: 0;
    width: 72%;
    /*min-height: calc(100vh - 1px);*/
    /*max-height: calc(100vh - 1px);*/
    margin-left: -600px;
    position: fixed;
    z-index: 999;
    overflow: hidden;
    overflow-y: auto;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.25); }

  #hideShowDivMobile {
    position: fixed;
    top: 50%;
    left: -27px;
    width: 100px;
    height: 48px;
    display: block;
    /*background: url("../images/wonderslate/collapsed.png");*/
    /*background-size: 100% 100%;*/
    /*background-position: center;*/
    background: #FFFFFF;
    z-index: 999; }

  .rotated {
    /*background: url(wonderslate/collapse.png) !important;*/
    background-size: 100% 100% !important;
    background-position: center !important; }

  .locked-read-book-chapters {
    width: 100%;
    position: relative; }

  .quiz-modal-footer {
    display: flex;
    justify-content: space-between; }

  .next-btn {
    width: auto;
    padding: auto; } }
@media screen and (min-width: 320px) and (max-width: 768px) and (orientation: landscape) {
  .author-of-books > p {
    max-width: 80%; } }
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .sage-dropdown-menu {
    background-color: #FFFFFF !important; }

  .author-of-books {
    padding-left: 12px; }
    .author-of-books > p {
      max-width: 100%; } }
@media screen and (min-width: 320px) and (max-width: 374px) {
  .main-section {
    padding: 24px 60px 0 16px; }

  .registr-in-seconds {
    font-size: 24px;
    line-height: 32px; }

  .simplify-explained {
    margin-top: 0; }

  .changed-teaching-experience {
    font-size: 20px;
    margin: 0; }

  .simplified-details {
    font-size: 12px;
    line-height: 20px; }

  .benefits {
    padding: 0; }

  .students-benefits-label {
    font-size: 32px; }

  .benefits-content {
    top: -24px;
    padding-right: 8px; }

  .benefits-content:last-child {
    padding-left: 8px !important; }

  .img-benefit {
    width: 40px;
    height: 40px; }

  .benefits-label {
    font-size: 10px; }

  .benefits-text {
    font-size: 11px; }

  .student-benefit:after {
    bottom: 16px; }

  .author-of-books {
    padding: 0; }
    .author-of-books > p {
      float: left;
      max-width: 200px;
      font-size: 12px; }
    .author-of-books .mobile-click-here {
      display: inline-block;
      width: 80px;
      text-align: center;
      font-size: 10px;
      border: 1px solid #FFFFFF;
      color: #FFFFFF;
      padding: 8px 16px;
      margin-top: 8px;
      margin-left: 0;
      box-sizing: border-box;
      border-radius: 300px; }

  .sage-footer {
    padding: 40px 32px 24px; }

  .footer-content.details-contact {
    padding-left: 0; }

  .footer-content-wrapper:last-child {
    margin-top: 24px; }

  .footer-content-wrapper:nth-child(3):after {
    border-right: 0; }

  .footer-link-label {
    font-size: 12px; }

  .footer-link {
    font-size: 10px; } }
@media screen and (min-width: 320px) and (max-width: 375px) {
  .sage-navbar .navbar-brand {
    padding: 12px 15px; }

  .sage-footer {
    padding: 40px 16px 24px; } }
@media screen and (min-width: 360px) and (max-width: 380px) {
  .author-of-books {
    padding: 0; }
    .author-of-books > p {
      float: left;
      max-width: 228px;
      font-size: 12px; }
    .author-of-books .mobile-click-here {
      display: inline-block;
      width: 96px;
      text-align: center;
      font-size: 12px;
      border: 1px solid #FFFFFF;
      color: #FFFFFF;
      padding: 8px 16px;
      margin-left: 0;
      box-sizing: border-box;
      border-radius: 300px; } }
@media screen and (min-width: 768px) {
  body {
    overflow-y: scroll !important; }

  .sage-body {
    max-width: 1280px;
    padding: 16px 0;
    margin: 0 auto; }

  .registr-in-seconds-wrapper {
    padding-right: 43px; }

  .sage-modal.in > .modal-dialog {
    -webkit-transform: translate(0, 5%) !important;
    -moz-transform: translate(0, 5%) !important;
    -ms-transform: translate(0, 5%) !important;
    -o-transform: translate(0, 5%) !important;
    transform: translate(0, 5%) !important; } }
@media screen and (min-width: 769px) {
  .footer-link-label {
    color: #FFFFFF; }

  .instructor-content {
    margin-top: -48px; }

  .sage-menu > li > a {
    padding: 4px 16px 0; }

  .instructor-tabs {
    background-color: #F8F8F8;
    padding-right: 0; }
    .instructor-tabs .chapter-tabs li {
      min-width: 180px; } }
@media screen and (min-width: 769px) and (max-width: 1024px) {
  .no-scroll {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden; }

  .login-btn-wrapper li a {
    font-size: 13px; }

  .sage-header {
    padding: 0 18px; }

  .login-btn-link {
    padding: 0; }
    .login-btn-link .navbar-right {
      float: right; }
      .login-btn-link .navbar-right li {
        display: inline-block; }

  .sage-dropdown-menu {
    background-color: rgba(0, 0, 0, 0.03) !important; }

  .sage-head-title {
    margin-top: 24px; }
    .sage-head-title p {
      font-size: 28px;
      padding: 10px 16px; }

  .main-section {
    padding: 40px 60px 0 40px; }

  .registr-in-seconds-wrapper {
    margin-top: 48px; }

  .registr-in-seconds {
    font-size: 40px;
    line-height: 48px; }

  .get-access {
    font-size: 20px;
    margin-bottom: 32px; }

  .btn-get-started {
    width: 170px;
    font-size: 16px;
    padding: 12px 0; }

  .simplify-learning {
    padding: 0 16px 16px;
    margin-top: 80px; }

  .simplify-learning-student {
    top: -80px;
    padding: 40px 40px 0; }

  .simplify-explained {
    margin-top: 0; }

  .changed-teaching-experience {
    font-size: 32px;
    margin: 0; }

  .simplified-details {
    padding-right: 24px;
    margin-top: 16px; }

  .benefits {
    padding: 0; }

  .benefits-label {
    font-size: 22px; }

  .benefits-content {
    padding-right: 8px; }

  .benefits-content:last-child {
    padding-left: 8px !important; }

  .author-of-books {
    padding: 0;
    margin-top: -46px;
    padding-left: 12px; }
    .author-of-books > p {
      float: left; }

  .sage-footer {
    padding: 40px 32px 24px; }

  .sage-dropdown-menu {
    background-color: #FFFFFF !important; } }
@media screen and (min-width: 768px) {
  .discipline-dropdown-menu {
    position: relative; }
    .discipline-dropdown-menu:before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      width: 2px;
      height: 15px;
      background-color: #000000;
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%); }

  .sage-modal.in .modal-dialog {
    -webkit-transform: translate(0, 35%);
    -moz-transform: translate(0, 35%);
    -ms-transform: translate(0, 35%);
    -o-transform: translate(0, 35%);
    transform: translate(0, 35%); } }

/*# sourceMappingURL=sage.css.map */
.read-question-answer ul li{
  float: none !important;
  padding: 10px 0;
  list-style-type: inherit;
  font-weight: normal;
}
.read-question-answer ul{
  margin-bottom: 0;
  margin-left: 6rem;
}
.read-answer p{
  margin-bottom: 1rem;
}
.test-gen-modal .modal-title {
  font-size: 22px;
}
.test-gen-modal .modal-header, .test-gen-modal .modal-footer {
  position: relative;
  z-index: 2;
}
.test-gen-modal .search-bar {
  position: relative;
  width: 330px;
  margin: 0 auto;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  z-index: 2;
}
.test-gen-modal .test-gen-books {
  position: relative;
  z-index: 2;
}
.test-gen-modal .test-gen-books ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .overlay-testgen-book .book-selected {
  width: 60px;
  height: 60px;
  color: #fff;
  text-align: center;
  font-size: 32px;
  background: #F05A2A;
  margin: auto;
  border-radius: 100px;
  position: absolute;
  right: 0;
  left: 0;
  margin: auto;
  top: 0;
  bottom: 0;
  padding: 8px;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-image-wrapper {
  height: 240px;
  position: relative;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-item-test-gen {
  height: auto;
  background: #fff;
  border: 0.5px solid rgba(189, 189, 189, 0.55);
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-info {
  height: auto;
}
.test-gen-modal .test-gen-books ul .book-content-wrapper .book-info .book-name-author {
  height: auto;
}

.test-gen-modal-body {
  position: relative;
  width: 100%;
  /*min-height: 750px;*/
  /*max-height: 750px;*/
  background: url("../images/booksmojo/testgen-bg.png");
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 60%;
  overflow: auto;
  overflow-x: hidden;
}
.test-gen-modal-body .test-gen-chapters .book-name, .test-gen-modal-body .test-gen-chapters .test-type {
  font-size: 18px;
  color: rgba(68, 68, 68, 0.74);
}
.test-gen-modal-body .test-gen-chapters .book-name {
  max-width: 100%;
  text-align: center;
}
.test-gen-modal-body .overlay {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.96);
  z-index: 1;
}
.test-gen-modal-body .on-top-overlay {
  position: relative;
  z-index: 2;
}
.test-gen-modal-body input[type=checkbox] {
  position: absolute;
  opacity: 0;
}
.test-gen-modal-body input[type=radio] {
  position: absolute;
  opacity: 0;
}
.errorField{
  color:red;
  font-size: 12px;
}
.sage-body .flip-container{
  background: #1F419B;
}
.front .name{
  color:#fff;
}
.back-title{
  color:#fff;
}
.flip-card-btn{
  background: #fff;
  color:#1F419B;
}
.flip-card-btn:hover{
  background: #fff;
  color:#1F419B;
}
#quizModal #close-quizz-modal{
  cursor: pointer !important;
  pointer-events: unset !important;
  opacity: 1;
}
#quizModal .close-modal.close-sagemodal{
  position: relative;
  /*top:-22px;*/
}
.question-number-help-dropdown li:nth-child(3){
  /*display: none;*/
}
.questionumber-containter{
  /*position: fixed;*/
  width: 30%;
}
.sage-input-select .disabled{
  color:rgba(0, 0, 0, 0.6);
}

#show-password.passwordShow{
  width: 22px;
  height: 13px;
  position: absolute;
  right: 15px;
  bottom: 15px;
  background: url("../images/eyeCross.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  text-indent: -5000px;
}

.multiselect[title='Area of Interests'],.multiselect[title='Area of Interests']{
  color:rgba(0, 0, 0, 0.5)!important;
}

[include*="form-input-select()"] {
  display: block;
  padding: 0;
  position: relative;
  /* Set options to normal weight */
  /* ------------------------------------  */
  /* START OF UGLY BROWSER-SPECIFIC HACKS */
  /* ----------------------------------  */
  /* FIREFOX won't let us hide the native select arrow, so we have to make it wider than needed and clip it via overflow on the parent container.
     The percentage width is a fallback since FF 4+ supports calc() so we can just add a fixed amount of extra width to push the native arrow out of view. We're applying this hack across all FF versions because all the previous hacks were too fragile and complex.
     You might want to consider not using this hack and using the native select arrow in FF. Note this makes the menus wider than the select button because they display at the specified width and aren't clipped. Targeting hack via http://browserhacks.com/#hack-758bff81c5c32351b02e10480b5ed48e */
  /* Show only the native arrow */
  color: #000000;
  display: block;
  border-radius: 0;
  box-shadow: none;
  font-size: 16px;
  /*margin-top: 9px;*/
  /*margin-bottom: 15px;*/
  width: 100%;
}
[include*="form-input-select()"]::before, [include*="form-input-select()"]::after {
  content: '';
  display: block;
  position: absolute;
  pointer-events: none;
  z-index: 2;
}
[include*="form-input-select()"] select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: none;
  box-sizing: border-box;
  /*width: 100%;*/
  /*margin: 0;*/
  /*border: 1px solid transparent;*/
  font-size: 16px;
  outline: none;
  /* Focus style */
}
[include*="form-input-select()"] select:focus {
  background-color: transparent;
  outline: none;
}
[include*="form-input-select()"] option {
  font-weight: normal;
}
[include*="form-input-select()"] x:-o-prefocus, [include*="form-input-select()"]::after {
  display: none;
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  [include*="form-input-select()"] select::-ms-expand {
    display: none;
  }
  [include*="form-input-select()"] select:focus::-ms-value {
    background: transparent;
    color: #000;
  }
}
@-moz-document url-prefix() {
  [include*="form-input-select()"] {
    overflow: hidden;
  }
  [include*="form-input-select()"] select {
    width: 120%;
    width: calc(100% + 3em);
    /* Firefox focus has odd artifacts around the text, this kills that. See https://developer.mozilla.org/en-US/docs/Web/CSS/:-moz-focusring */
  }
  @supports (-moz-appearance: none) {
    [include*="form-input-select()"] select {
      width: 100%;
    }
  }
  [include*="form-input-select()"] select:-moz-focusring {
    color: transparent;
    text-shadow: 0 0 0 #000;
  }
}
@supports (-moz-appearance: none) {
  [include*="form-input-select()"] {
    width: 100%;
  }
}
[include*="form-input-select()"]::before, [include*="form-input-select()"]::after {
  content: '';
  display: block;
  position: absolute;
  pointer-events: none;
  border: 1px solid transparent;
  width: 0;
  height: 0;
  right: 16px;
}
[include*="form-input-select()"]::before {
  bottom: 55%;
  border-width: 0 6.5px 8px 6.5px;
  border-bottom-color: #D6D6D6;
}
[include*="form-input-select()"]::after {
  border-width: 8px 6.5px 0 6.5px;
  border-top-color: #D6D6D6;
  top: 55%;
}
@-moz-document url-prefix() {
  [include*="form-input-select()"] {
    border-right: 3px solid #E6E6E6;
  }
  [include*="form-input-select()"]:hover {
    /*border-right: 3px solid #005BA6;*/
  }
}
[include*="form-input-select()"]:hover select {
  /*box-shadow: 0 2px 3px rgba(0, 91, 166, 0.1) inset;*/
  /*border-color: #005BA6;*/
}
[include*="form-input-select()"]:hover select:focus {
  outline-color: transparent;
}
[include*="form-input-select()"]:hover::before {
  /*border-bottom-color: #005BA6;*/
}
[include*="form-input-select()"]:hover::after {
  /*border-top-color: #005BA6;*/
}
[include*="form-input-select()"] select {

  font-weight: 400;
  color: inherit;
  /*padding: 11px 15px;*/
  /*line-height: normal;*/
  /*transition: border-color 0.2s ease, outline 0.2s ease;*/
}
[include*="form-input-select()"] select:focus {
  /*box-shadow: 0 3px 4px rgba(0, 91, 166, 0.3) inset;*/
  /*outline: 3px solid #005BA6;*/
  /*outline-offset: -3px;*/
}
[include*="form-input-select()"] select[disabled], [include*="form-input-select()"] select:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}
[include*="form-input-select()"] select:invalid {
  color:rgba(0, 0, 0, 0.5);
}
#sum-question {
  display: flex;
}
#sum-question span{
  font-size: 18px !important;
}
#userProfile #course-level{
  display: block !important;
}
#userProfile #course-level + .btn-group{
  display: none;
}
.modal {
  text-align: center;
}

@media screen and (min-width: 768px) {
  .modal:before {
    display: inline-block;
    vertical-align: middle;
    content: " ";
    height: 100%;
  }
}

.modal-dialog {
  display: inline-block;
  text-align: left;
  vertical-align: middle;
}
#course-level option{
  color:#000;
}

.sage-body #questionumber-containter {
height: 500px;
}
.full-wdth-label p{
  margin-bottom: 0;
  margin-left: 1rem !important;
}
.sage-body #videoAddButton,#addRefButton{
  display: none !important;
}
.sage-body .video-img-wrapper .video-img{
  width: 100%;
}
.sage-body #htmlreadingcontent{
  padding: 0 !important;
}
#content-data-no-notes .btn, #content-data-studyset-nosets .btn, #content-data-videos .btn, #content-data-url .btn, #withnovideos .btn, #withnoweblinks .btn{
  padding: 0.5rem;
  color:#1F419B;
  font-size: 16px;
  border:1px solid #1f419b;
  min-width: 300px;
  max-width: 300px;
  margin: 10px auto;
}
.refrenceText{
  margin-top: 1rem;
  text-align: center;
  font-size: 14px;
}
.sage-body .card-wrapper{
  display: none;
}
#pubTable td {
  white-space: nowrap;
  padding: 0.33rem;
}
.sage-body .tags select[multiple] {
  height: auto;
  width: 100%;
}
.book #language{
    width:30%;
}
#addGroup h4{
  margin-left: 20px;
}
#subjects td {
  border: 1px solid #000;
  padding: 1rem;
}
#subjects tr {
  border: 1px solid #000;
  padding: 1rem;
}
#subjects th {
  border: 1px solid #000;
  padding: 1rem;
}
#grades th, #grades td {
  border: 1px solid #000;
  padding: 1rem;
}
.main {
  background-color: white;
  margin: 10px;
  box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 4px;
}
.firstPage {
  background-color: #D3D3D3;
  padding-left: 50px;
}
.firstPage .form-group{
  margin-left:0px;
}
.float-label-control {
  display: block;
  width: 100%;
  padding: 0.1em 0em 1px 0em;
  border: none;
  border-radius: 0px;
 /*border-bottom: 1px solid #aaa;*/
  outline: none;
  /*margin-right: 20px;*/
  background: none;
  display: flex;
}
.cktext{
  width: 100%;
}
 #expiryDate{
  line-height: 14px;
}
input[type="date"]{
  -webkit-appearance: listbox;
}
.quiz{
  display: -webkit-box;
  width: 80%;
//padding-bottom: 20px;
}
.float-label-control input {
  display: block;
  width: 100%;
  padding: 7.1em 0em 1px 0em;
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #aaa;
  outline: none;
  margin-right: 20px;
  background: none;

}
.quiz1{
  display: -webkit-box;
  width: 80%;
  padding:0.1em 3em 5px 4em;
}
.quiz2{
  display: -webkit-box;
  width: 80%;
  padding:0.1em 3em 5px 4em;
}
.quiz3{
  display: -webkit-box;
  width: 80%;
  padding:1.1em 3em 5px 4em;
}
.quiz4{
  display: -webkit-box;
  width: 80%;
  padding:1.1em 3em 5px 4em;
}
.quiz5{
  display: -webkit-box;
//width: 80%;
  padding:1.1em 3em 5px 4em;
}
.quiz6{
  display: -webkit-box;
//width: 80%;
  padding:1.1em 3em 5px 16em;
.text-center {
  text-align: center;
//margin-left: -195px;
//.btn-primary {
  //  color: #ffffff;
  //  background-color: #f15b2a;
  //  border-color: #ef4912;
  //  margin:0px;
  //}
@media (min-width: 768px) {
  .col-sm-12 {
    width: 100%;
    float: left;
  }
}
.btn-primary {
  color: #ffffff;
  background-color: #CF6C00;
  border-color: #CF6C00;
  margin: 0px;
}
.btn:focus,.btn:active {
  background-color: @ws-darkOrange!important;
}
}
}
.quiz7{
  display: -webkit-box;
  /*width: 80%;*/
  padding:1.1em 3em 5px 3em;
}
#static-content h4{
  margin-bottom: 9px;
//margin-top: 11px;
  margin-left: 0px;
  font-size: 18px;
  font-weight: 500;
}
.pagenumber-green {
  height: 21px;
  width: 35px;
  text-align: center;
  border-width: 1px;
  border-color: green;
  border-radius: 3px;
  border-style: solid;
  display: inline-block;
  padding: 0px;
  background-color: green;
  color: white;
}
.quiz8 {
  width: 100%;
}
.quiz8 .alert-warning {
  background-color: #f8d9ac;
  border-color: #f6bd95;
  color: #f0ad4e;
}

.quiz8 .alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
  margin-left: 14px;

}
@media (min-width: 992px) {
  .col-md-offset-1 {
     margin-left: 0px;
  }
}
@media (min-width: 768px) {
  .col-sm-offset-1 {
     margin-left: 0px;
  }
}
#mcqquestionsection .modal-footer .pull-left{
  align-items: center;
}
.modal-dialog .modal-footer .btn{
  align-items: center;
  display: inline-flex;
}
.modal-dialog .modal-footer .btn-review{
  display: block;
}
.modal-dialog .modal-footer #close-quizz-modal{
  display: block;
}
.sage-body #error_message .material-icons{
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 15px;
  line-height: 2;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
}
.sage-body .qanda {
  display: -webkit-box;
  width: 80%;
  padding: 0.1em 3em 5px 0em;
}
.sage-body .row{
  margin-left: 0px;
  margin-right: 0px;
}
.sage-body .qanda2 {
  padding: 0.1em 2em 18px 3em;
}
#notpublished .material-icons {
  font-size: 14px;
}
#published .material-icons {
  font-size: 14px;
}
#resource8Form .btn{
  margin-left: 50px;
}
#resource9Form .btn{
  margin-left: 50px;
}
#resource6Form .buk3 {
  margin-top: -18px;
}
#quizresourceName{
  margin-left: -50px;
}
#resource7Form .buk3 {
  margin-top: -18px;

}
#file10{
  margin-left: -200px;
}
#file5{
  margin-left: -130px;
}
#resource4Form .buk3 {
  margin-top: -18px;
}
.filelink .form-control {
  display: block;
  width: 100%;
  height: 42px;
  padding: 6px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555555;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #cccccc;
  border-radius: 4px;
}
#nxt-btn{
    margin-left: 16px;
}
.row.addtag{
  margin-left: -16px;
}
#createLevel{
  margin-left: -16px;
}
.flex-line{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 2px;
  padding-right:2px;
}
#addedContents td{
  text-align: center;
}
