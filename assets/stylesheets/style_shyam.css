body{
  font-family: '<PERSON><PERSON>', sans-serif;
  font-weight: 300;
}



.header{
  padding-top: 15px;
  padding-bottom: 15px;
}
.brand a:hover{
  text-decoration: none;
}
.brand a{
  font-family: 'Exo', sans-serif;
  font-size: 1.5em;
  color: #7b7a79;
}
.brand img{
  display: inline-block;
  width: 40px;
  vertical-align: middle;
  margin-right: 5px;
}
.social-links ul li img{
  width: 30px;
}


.header-and-topic-select{
  min-height: 80vh;
  background-image: url(../images/bg-top.jpg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.topic-select{
  margin-top: 150px;
}
.topic-select .col-xs-12{
  margin-top: 10px;
  margin-bottom: 10px;
}
select.form-control{
  color: #7b7a7b;
  border: 1px solid #828180;
  border-radius: 4px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: url(../images/icon-arrow-down.png) 96% no-repeat #fff;
  opacity: 0.5;
  font-weight: 300;
}


.child{
  position: absolute;
  top: 50%;
  transform: translateY(-50%);

}
.content-appeal{
  min-height: 70vh;
  background-image: url(../images/bg-bottom.jpg);
  background-repeat: no-repeat;
  background-size: 100% 100%; 
}
.content-appeal .column {
  height: 100%;
}
.content-appeal .column p{
  font-size: 2.7vmax;
  color: #ffffff;
}
.row-centered {
  text-align:center;
}
.row-right {
  text-align:right;
}
.col-centered {
  display:inline-block;
  float:none;
  /* reset the text-align */
  text-align:left;
  /* inline-block space fix */
  margin-right:-4px;
}
.vcenter {
    display: inline-block;
    vertical-align: middle;
    float: none;
}


.footer{
  background-color: #eef2f6;
}
.footer .row{
  margin-top: 15px;
  margin-bottom: 10px;
}
.footer a{
  color: #606060;
  font-weight:300;
}
.footer p{
  font-family: 'Exo', sans-serif;
  font-weight: 300; 
}
.footer a:hover{
  text-decoration: none;
}

/*************************/
.topic-banner{
  background-image: url(../images/bg-topic-banner.jpg);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.topic-banner .container{
  position: relative;
  min-height: 22vh;
}


.topic{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width:60%;
  color: white;
  font-size: 1.2vmax;
}
.topic-title{
  font-size: 2.4vmax;
}


/********/

.boxify {
  border-width: 1.5px;
  border-color: rgb(130, 129, 128);
  border-style: solid;
  border-radius: 8px;
  position: relative;
  height: 191px;
}

.boxify:hover{
  cursor: pointer;
}

.boxify.quiz-header{
    cursor: default;
}



.thumbnail{
  position: absolute;
  height: 100%;
  width: 100%; 
  border-radius: 7px; 
  border: 0px; 
  padding: 0px; 
}

.play-icon{
  position: absolute; 
  top: 42%; 
  left:42%; 
  width: 40px; 
  height: 40px; 
}


.thumbnail-vid-title{
  color: white; 
  margin-left: 15px;
  margin-top: 15px;
  position: absolute; 
  font-size: 1.5rem; 
}

.thumbnail-quiz-title{
    color: #f15a29;
    margin-left: 45px;
    margin-top: 55px;
    position: absolute;
    font-weight: 700;
    text-transform: uppercase;
    font-size: 17px;
}

.content{
  margin-top: 50px;
  padding-left: 6.2%;
  padding-right: 6.2%;
}

.thumbnail-oth-title{
  color:white;
  width: 100%;
  position: absolute;
  bottom:0;
  left:0;
  font-weight: 700;
  border-bottom-left-radius: 7px;
  border-bottom-right-radius: 7px;
  padding: 5px;
  text-transform: uppercase;
}

.video-section,.notes-section,.quiz-section,.mm-section{
  margin-top: 15px;
  margin-bottom: 15px;
}
.quiz-section{
  margin-bottom: 30px;
}

.quiz-modal-header{
  background-color: #73a7cb;

}

.quiz-modal-header p{
  color: white;
  font-weight: 700;
  font-size: 25px;
  margin-top:10px;
  margin-left: 10px;
  text-transform: uppercase;
  
}

.checkbox label, .radio label{
  font-weight: 700;
}

.quiz-hr{
  margin: 0px;
}

.quiz-modal-footer{
  border: none;
  padding: 15px;

}


.quiz-form{
  padding-top: 10px;
}

.red{
  color: red;
}
.green{
  color: green;
}
.blue{
  color: blue;
}

.input-text-quiz {
  border: none;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  border-bottom-color: #bcbcbc;
  font-style: italic;
  font-weight: 700;
}

.input-text-quiz:focus{
  outline:0;
  border-bottom-color: blue;
}

.quiz-modal-body .container-fluid{
  margin-top: 35px;
  margin-bottom: 20px;
  font-size: 17px;
}

.radio-true, .radio-false {
  display: none;
}


.radio-true + label{
  height:31px;
  width:31px;
  border-width: 3px;
  border-color: rgb(186, 215, 125);
  border-radius: 5px;
  border-style: solid;
  display: inline-block;
  padding: 0px;
}
.radio-true:checked + label, .true-selected-box{
  height:31px;
  width:31px;
  border-width: 3px;
  border-color: rgb(186, 215, 125);
  border-radius: 5px;
  background-color: rgb(186, 215, 125);
  display: inline-block;
  padding: 0px;
}

.radio-false + label{
  height:31px;
  width:31px;
  border-width: 3px;
  border-color: rgb(241, 90, 41);
  border-radius: 5px;
  border-style: solid;
  display: inline-block;
  padding: 0px;
}
.radio-false:checked + label, .false-selected-box{
  height:31px;
  width:31px;
  border-width: 3px;
  border-color: rgb(241, 90, 41);
  border-radius: 5px;
  border-style: solid;
  background-color: rgb(241, 90, 41);
  display: inline-block;
  padding: 0px;
}

.left-border-vr{
  border: none; 
  border-left-width: 2px; 
  border-left-color:#8a8988; 
  border-left-style: solid;
}

.sum-modal-header{
  background-color: #f26335;
}

.sum-modal-header p{
  color: white;
  font-weight: 700;
  font-size: 25px;
  margin-top:10px;
  margin-left: 10px;
  text-transform: uppercase;
  
}

.sum-modal-body {
  margin-top: 35px;
  margin-bottom: 20px;
  font-size: 17px;
}

.top-container{
    padding-top:70px;
}
div.img-div{
    height:200px;
    width:200px;
    overflow:hidden;
    border-radius:50%;
    border-style: solid;
    border-color: white;
    border-width: 1px;
    border-spacing: 2px;
    padding: 0px;

}

.img-div img{
    -webkit-transform:translate(-50%);
    margin-left:100px;
    width: 200px;
    height: 200px;
}

.profile{
    color: white;
    font-size: 1.4vmax;
}

.profile-banner{
    background-image: url(../images/ProfilePageBG.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
}
.profile-banner .container{
    position: relative;
    min-height: 40vh;
    padding-top: 20px;
    padding-bottom: 20px;
}

.smallText{
    font-size: 13px;
}
.smallerText{
  font-size: 12px;
}

.lmodal{
    position: fixed;
    z-index: 999;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background-color: Black;
    filter: alpha(opacity=60);
    opacity: 0.6;
    -moz-opacity: 0.8;
}

.lcenter {
    z-index: 1000;
    margin: 300px auto;
    padding: 10px;
    width: 20%;
    height: 20%;
    background-color: White;
    border-radius: 10px;
    filter: alpha(opacity=100);
    opacity: 1;
    -moz-opacity: 1;
}

.lcenter img {
    height: 100%;
    width: 100%;
}

.alert{
    background-color: transparent;
    border: hidden
}

