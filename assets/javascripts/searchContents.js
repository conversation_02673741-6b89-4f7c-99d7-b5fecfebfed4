/**
 * Created by achyutanand on 09/11/15.
 */
var formattedTopicMapIndex;
var syllabus;
var grade;
var subject;
var level;
var dropdownMode="search";
var bookCategories=false;

function fetchLevelData(data) { 
    var levelList = document.getElementById('level-list');
    var syllabusList = document.getElementById('selectedSyllabus');
    var gradeList = document.getElementById('selectedGrade');
    var subjectList = document.getElementById('selectedSubject');

    for(var i = 0; i < data.results.length; i++) {
        var levelItem = document.createElement('a');
        levelItem.className = 'level-item';
        levelItem.href = '#';
        levelItem.innerHTML = data.results[i].level;
        levelList.appendChild(levelItem);
        levelItem.setAttribute('data', 'levels');

        var syllabusItem = document.createElement('a');
        syllabusItem.className = 'dropdown-item';
        syllabusItem.href = '#';
        syllabusItem.text = data.results[i].syllabus;
        syllabusList.appendChild(syllabusItem);

        var gradeItem = document.createElement('a');
        gradeItem.className = 'dropdown-item';
        gradeItem.href = '#';
        gradeItem.text = data.results[i].grade;
        gradeList.appendChild(gradeItem);

        var subjectItem = document.createElement('a');
        subjectItem.className = 'dropdown-item';
        subjectItem.href = '#';
        subjectItem.text = data.results[i].subject;
        subjectList.appendChild(subjectItem);
    }
}

$('#levels-wrapper').on('click', '.level-item', function(e) {
    e.preventDefault();
    if($(this).attr('data', 'levels')) {
        $('#syllabus-div').removeClass('disabled');
    };
});

$('#selectedSyllabus').on('click', '.dropdown-item', function(e) {
    e.preventDefault();
    $('#grade-div').removeClass('disabled');
});

$('#selectedGrade').on('click', '.dropdown-item', function(e) {
    e.preventDefault();
    $('#subject-div').removeClass('disabled');
});


function changeBoard(value){
    if(dropdownMode=="search") $('#selectedTitleAndId').hide();
    if(value!="") {
        var levelkey = value.replace(/\W+/g, '');
        populateSelect('selectedBoard', formattedTopicMapIndex.boards[levelkey]);
        $('#selectedBoard').show();
        $('#selectedBoard').removeAttr('disabled');
    }
    else {
        var select ;
        if(dropdownMode=="search") {
            select = document.getElementById("selectedTitleAndId");
            select.options.length = 1;
        }
        select = document.getElementById("selectedSubject");
        select.options.length = 1;
        select = document.getElementById("selectedGrade");
        select.options.length = 1;
        select = document.getElementById("selectedBoard");
        select.options.length = 1;

    }
}
function changeGrade(value){
    if(dropdownMode=="search") $('#selectedTitleAndId').hide();
    if(bookCategories&&"All"==value) getBooksForSelectedCategories("syllabus",value);
    else {
        if (value != "") {
            var levels = document.getElementById('selectedLevel');
            var levelkey = levels.options[levels.selectedIndex].value.replace(/\W+/g, '');
            var syllabuskey = value.replace(/\W+/g, '');
            populateSelect('selectedGrade', formattedTopicMapIndex.grades[levelkey + syllabuskey]);
            $('#selectedGrade').show();
            $('#selectedGrade').removeAttr('disabled');

        }
        else {

            var select;
            if (dropdownMode == "search") {
                select = document.getElementById("selectedTitleAndId");
                select.options.length = 1;
            }
            select = document.getElementById("selectedSubject");
            select.options.length = 1;
            select = document.getElementById("selectedGrade");
            select.options.length = 1;
        }
    }
}
function changeSubject(value){
    if(dropdownMode=="search") $('#selectedTitleAndId').hide();
    if(bookCategories&&"All"==value) getBooksForSelectedCategories("grade",value);
    else {
        if (value != "") {
            var levels = document.getElementById('selectedLevel');
            var levelkey = levels.options[levels.selectedIndex].value.replace(/\W+/g, '');
            var syllabus = document.getElementById('selectedBoard');
            var syllabuskey = syllabus.options[syllabus.selectedIndex].value.replace(/\W+/g, '');
            var gradekey = value.replace(/\W+/g, '');
            populateSelect('selectedSubject', formattedTopicMapIndex.subjects[levelkey + syllabuskey + gradekey]);
            $('#selectedSubject').show();
            $('#selectedSubject').removeAttr('disabled');

        } else {

            var select;
            if (dropdownMode == "search") {
                select = document.getElementById("selectedTitleAndId");
                select.options.length = 1;
            }
            select = document.getElementById("selectedSubject");
            select.options.length = 1;
        }
    }
}

function changeTopic(value){
    if(bookCategories) getBooksForSelectedCategories("subject",value);
    else {
        $(".lmodal").show();
        value = value.replace(/\W+/g, '');
        syllabus = document.getElementById('selectedBoard');
        grade = document.getElementById('selectedGrade');
        level = document.getElementById('selectedLevel');
        var obj = eval('formattedTopicMapIndex.topics.' + ((level.options[level.selectedIndex].value).replace(/\W+/g, '') + (syllabus.options[syllabus.selectedIndex].value).replace(/\W+/g, '') + grade.options[grade.selectedIndex].value.replace(/\W+/g, '') + value));

        var select = document.getElementById('selectedTitleAndId');
        if (select != null) {
            if (select.options.length > 1 && select.options[1].value == "addChapter") select.options.length = 2;
            else  select.options.length = 1;

            for (var key in obj) {
                if (obj.hasOwnProperty(key)) {
                    var val = obj[key];
                    var el = document.createElement("option");
                    el.textContent = key;
                    el.value = val;
                    select.appendChild(el);
                }
            }

            select.focus();
        }
        $(".lmodal").hide();
        $('#selectedTitleAndId').show();
    }
}

function loadDetailsPage(field,target){
    if(field.value=="addChapter"){
        addTopic();
    }else {
        $(".lmodal").show();
        syllabus = document.getElementById('selectedBoard');
        grade = document.getElementById('selectedGrade');
        subject = document.getElementById('selectedSubject');
        var chapter = document.getElementById('selectedTitleAndId');
        var desc = chapter.options[chapter.selectedIndex].text + '-' + subject.options[subject.selectedIndex].value + '-' + grade.options[grade.selectedIndex].value + '-' + syllabus.options[syllabus.selectedIndex].value;
        desc = desc.replace(new RegExp(' ', 'g'), '-');
        createCookie('syllabus', syllabus.options[syllabus.selectedIndex].value, 30);
        createCookie('grade', grade.options[grade.selectedIndex].value, 30);
        createCookie('subject', subject.options[subject.selectedIndex].value, 30);
        window.open(serverPath + "/funlearn/" + target + "/" + field.value + "/" + desc, "_self");

    }
}

function populateSelect(fieldName,options){
   $(".lmodal").show();
    var select = document.getElementById(fieldName);
    select.options.length = 1;

   if(bookCategories&&fieldName!="selectedLevel") {
       var el = document.createElement("option");
       el.textContent = "All";
       el.value = "All";
       select.appendChild(el);
   }
    
    for(var i=0; typeof options!='undefined' && i<options.length; i++) {
        var opt = options[i];
        var el = document.createElement("option");
        el.textContent = opt;
        el.value = opt;
        select.appendChild(el);
    }                                 
    
    select.focus();   
    $(".lmodal").hide();    
}

function formatDataIndex(data){
    var formattedTopicMap = {};
    var levels = formattedTopicMap.levels = [],
        boards = formattedTopicMap.boards = {},
        grades = formattedTopicMap.grades = {},
        subjects = formattedTopicMap.subjects = {},
        topics = formattedTopicMap.topics = {};
    var length = data.length;
    var level,board, grade, subject, topic, topicId,subjectkey,boardkey,gradekey,levelkey;

    for(var i = 0; i < length; ++i){
        level = data[i].level;
        levelkey = level.replace(/\W+/g, '');//removing the space in between
        board = data[i].syllabus;
        boardkey = board.replace(/\W+/g, '');//removing the space in between
        grade = ""+data[i].grade;
        gradekey = grade.replace(/\W+/g, '');//removing the space in between
        subject = data[i].subject;
        subjectkey = subject.replace(/\W+/g, ''); //removing the space in between
      if(!bookCategories) {
          topic = data[i].topic;
          topicId = data[i].id;
      }


        if(levels.indexOf(level) < 0) //Compatible IE 9 or later
            levels.push(level);
        if(!boards[levelkey])
            boards[levelkey] = [];
        if(boards[levelkey].indexOf(board) < 0)
            boards[levelkey].push(board);

        if(!grades[levelkey+boardkey])
            grades[levelkey+boardkey] = [];
        if(grades[levelkey+boardkey].indexOf(grade) < 0)
            grades[levelkey+boardkey].push(grade);

        if(!subjects[levelkey+boardkey+gradekey])
            subjects[levelkey+boardkey+gradekey] = [];
        if(subjects[levelkey+boardkey+gradekey].indexOf(subject) < 0) {
            subjects[levelkey+boardkey + gradekey].push(subject);

        }
        if(!bookCategories) {
            if (!topics[levelkey + boardkey + gradekey + subjectkey]) topics[levelkey + boardkey + gradekey + subjectkey] = {};
            topics[levelkey + boardkey + gradekey + subjectkey][topic] = "" + topicId;
        }
        //console.log("subject key="+subjectkey+" and topic is "+topics[board+grade+subjectkey][topic]);
    }
    return formattedTopicMap;
}

function createCookie(name, value, days) {
    var date, expires;
    if (days) {
        date = new Date();
        date.setTime(date.getTime()+(days*24*60*60*1000));
        expires = "; expires="+date.toGMTString();
    } else {
        expires = "";
    }
    var cooki = name+"="+value+expires+"; path=/";


}

$('#selectedSyllabus, #selectedGrade, #selectedSubject').on('click', '.dropdown-item', function(e) {
    e.preventDefault();
    var a = $(this).html();
    $(this).parents('.category-selection').children('.drpdwn-btn').html(a);
});

$('#level-list').on('click', '.level-item', function() {
    $(this).addClass('active').siblings().removeClass('active');
});

function setLevel(level){

    var levels = document.getElementById("selectedLevel");
    for (var i = 0; i < levels.options.length; i++) {
        if (levels.options[i].text==level) {
            levels.options[i].selected = true;
            return;
        }
    }

}