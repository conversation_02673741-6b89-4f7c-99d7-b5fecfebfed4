var globalUseType,globalResourceType;

function addResource(a,b){
    console.log("a="+a+" and b="+b);
    globalUseType=b;globalResourceType=a;
    if(a=="Reference Web Links"){
        addWebLinks();
    }else {
        $("#addResource").find(".resourcename").text(a);
        $("#addResource").find(".filelink").css("display", "none");
        $("#addResource").find(".videolink").css("display", "none");
        $("#addResource").find(".samplelink").css("display", "none");
        $("#addResource").find(".videolinkinfo").css("display", "none");
        $("#addResource").find(".filelinkinfo").css("display", "none");
        $(".form-group").removeClass('has-error');

        "quiz" == b ? $("#addResource").find(".samplelink").css("display", "block") : $("#addResource").find(".samplelink").css("display", "none");

        if ("link" == b) {
            $("#addResource").find(".videolinkinfo").css("display", "block");
            $("#addResource").find(".videolink").css("display", "block");
        }
        else {

            $("#addResource").find(".filelink").css("display", "block");
            if ("file" == b)$("#addResource").find(".filelinkinfo").css("display", "block");
        }
        if ("quiz" == b) {
            if (a == "Fill in the blanks") {
                $('#sampleButton').attr('onclick', 'javascript:downloadSample("FB")');
            } else if (a == "Multiple Choice Questions") {
                $('#sampleButton').attr('onclick', 'javascript:downloadSample("MCQ")');
            } else if (a == "True or False") {
                $('#sampleButton').attr('onclick', 'javascript:downloadSample("TF")');
            }
        }

        $(".alert").hide();
        "link" == b ? $("#youTubeAlert").show() : $("#youTubeAlert").hide();
        $("#addResource").modal("show");
    }
}

function addWebLinks(){
    $("#addWebLink").modal("show");
}
function addAlias(){
    $("#addAlias").modal("show")
}

function validateYouTubeUrl(url) {
    if (url != undefined || url != '') {
        var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|\?v=)([^#\&\?]*).*/;
        var match = url.match(regExp);
        if (match && match[2].length == 11) {
            // Do anything for being valid
            // if need to change the url to embed url then use below line
            //$('#ytplayerSide').attr('src', 'https://www.youtube.com/embed/' + match[2] + '?autoplay=0');
            return true;
        } else {
            // Do anything for not being valid
            return false;
        }
    }

    return false;
}

function getIdFromYouTubeURL(url) {
	if(url.match(/(youtu.be)/)){
		var split_c = "/";
		var split_n = 3;
	}	
	
	if(url.match(/(youtube.com)/)){
		var split_c = "v=";
		var split_n = 1;
	}
 
	var getYouTubeVideoID = url.split(split_c)[split_n];
	return getYouTubeVideoID.replace(/(&)+(.*)/, "");
}

function submitResource(){
    validate("link"==globalUseType?["resourceName","link"]:["resourceName","file"]) && (0==globalUseType && 
            (document.resourceForm.enctype="multipart/form-data"),
                    document.resourceForm.resourceType.value=globalResourceType,
                    document.resourceForm.useType.value=globalUseType,
                    document.resourceForm.submit(),
                    $("#addResource").modal("hide"),
                    $("#progressWindow").modal("show"),
                    $(".alert").show()
                    );
}
function submitWeblink(){
    $("#webgenericAlert").hide();
    $("#weblinkAlert").hide();
    $("#weblink").closest('.form-group').removeClass('has-error');
    $("#webresourceName").closest('.form-group').removeClass('has-error');
    if(!$("#weblink").val() ||!$("#webresourceName").val()){
        if(!$("#weblink").val()){
            $("#weblink").closest('.form-group').addClass('has-error');
        }
        if(!$("#webresourceName").val()){
            $("#webresourceName").closest('.form-group').addClass('has-error');
        }
        $("#webgenericAlert").show();

    }else if(!validURL($("#weblink").val())){
        $("#weblinkAlert").show();
    }
    else {

        document.webresourceForm.resourceType.value = globalResourceType;
        document.webresourceForm.useType.value = globalUseType;
        document.webresourceForm.submit();
        $("#addWebLink").modal("hide");
        $("#progressWindow").modal("show");
        $(".alert").show();
    }

}

function validURL(str) {
    var pattern = new RegExp('^(https?:\\/\\/)?'+ // protocol
        '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|'+ // domain name
        '((\\d{1,3}\\.){3}\\d{1,3}))'+ // OR ip (v4) address
        '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*'+ // port and path
        '(\\?[;&a-z\\d%_.~+=-]*)?'+ // query string
        '(\\#[-a-z\\d_]*)?$','i'); // fragment locator
    return pattern.test(str);
}
function checkAliasNameExists(a){
    "0"==a?(document.aliasForm.topic.value=document.aliasForm.alias.value,document.aliasForm.submit()):$("#addAlias").find(".topicexists").text("This topic/chapter name already exists.")
}

function validate(a){    
    var b=!0;
    $(".alert").hide();
    if (typeof a != 'undefined')
{

    for (i = 0; i < a.length; i++) $("#" + a[i]).val() ? ($("#" + a[i]).removeClass("has-error"), $("#" + a[i]).closest(".form-group").removeClass("has-error")) : ($("#" + a[i]).addClass("has-error"), $("#" + a[i]).closest(".form-group").addClass("has-error"), b = !1);

    b || $("#genericAlert").show();

    if (a[1] == 'link') $("#youTubeAlert").show();

    if (a[1] == 'link' && b) {
        if (!validateYouTubeUrl(document.resourceForm.link.value)) {
            $("#youTubeLinkAlert").show();
            b = !b;
        } else {
            var link = document.resourceForm.link.value;
            document.resourceForm.link.value = getIdFromYouTubeURL(link);
        }
    }
}
    
    return b;
};

function downloadSample(resType) {
    window.open(serverPath+'/funlearn/downloadSample?resType='+resType,"_self");
}