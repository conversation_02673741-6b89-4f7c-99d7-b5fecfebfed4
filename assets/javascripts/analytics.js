
function callGoogle(link) {
    var uaLink = 'UA-70151191-1';

    if (typeof googleUAId !== 'undefined') {
        uaLink = googleUAId;
    }

    (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function () {
            (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
            m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

    ga('create', uaLink, 'auto');
    ga('require', 'linkid');
    if(link!=""){
        ga('set', 'page', link);
        ga('set', 'title', document.title);
    }
    ga('send', 'pageview');
}




