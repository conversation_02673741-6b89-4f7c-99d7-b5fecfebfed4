// Get the base URL from the current location
const BASE_URL = window.location.origin;
console.log('BASE_URL:', BASE_URL);
const form = document.getElementById('textExtractorForm');
const resultsContainer = document.getElementById('resultsContainer');
const extractionSummary = document.getElementById('extractionSummary');
const textContent = document.getElementById('textContent');
const spinner = document.getElementById('spinner');
const progressLog = document.getElementById('progressLog');
const timerDisplay = document.getElementById('timerDisplay');
const timerValue = document.getElementById('timerValue');

// Timer variables
let startTime = null;
let timerInterval = null;

// Timer functions
function startTimer() {
    startTime = Date.now();
    timerDisplay.style.display = 'block';
    timerValue.textContent = '00:00';

    timerInterval = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        timerValue.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

function stopTimer() {
    if (timerInterval) {
        clearInterval(timerInterval);
        timerInterval = null;
    }

    if (startTime) {
        const elapsed = Date.now() - startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        const finalTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        timerValue.textContent = finalTime;
        return finalTime;
    }
    return '00:00';
}

function resetTimer() {
    stopTimer();
    timerDisplay.style.display = 'none';
    startTime = null;
}

form.addEventListener('submit', async (e) => {
    e.preventDefault();

    const formData = new FormData(form);
    const resId = formData.get('resId');
    const totalQuestions = parseInt(formData.get('totalQuestions'));

    const explanationStartPageValue = formData.get('explanationStartPage');
    const explanationStartPage = explanationStartPageValue ? parseInt(explanationStartPageValue) : null;
    const forceReextract = formData.get('forceReextract') === 'on';

    if (!resId) {
        alert('Please enter a resource ID');
        return;
    }

    if (!totalQuestions || totalQuestions < 1) {
        alert('Please enter a valid total number of questions (minimum 1)');
        return;
    }

    // Reset any previous timer and show spinner and progress log
    resetTimer();
    spinner.style.display = 'block';
    progressLog.style.display = 'block';
    progressLog.innerHTML = '';
    resultsContainer.style.display = 'none';

    // Start the timer
    startTimer();

    // Add initial log entry
    const extractionType = forceReextract ? 'forced MCQ text extraction' : 'MCQ text extraction';
    addLogEntry(`Starting ${extractionType}...`, 'info');

    try {
        const requestBody = {
            resId: resId,
            force_reextract: forceReextract,
            total_questions: totalQuestions,
        }
        // Only include explanation_start_page if it has a value
        if (explanationStartPage !== null) {
            requestBody.explanation_start_page = explanationStartPage;
        }
        const response = await fetch(`/pdfExtractor/startMcqExtraction`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();

        if (result.status === 'started') {
            // Task started successfully, begin polling
            addLogEntry(`Task started with ID: ${result.task_id}`, 'info');
            addLogEntry('Polling for status updates...', 'info');

            // Start polling for status
            pollTaskStatus(result.task_id);
        } else {
            // Stop timer and get final time
            const finalTime = stopTimer();

            // Hide spinner
            spinner.style.display = 'none';

            addLogEntry(`Error: ${result.message} (Failed after ${finalTime})`, 'error');
            displayError(result.message);
        }

    } catch (error) {
        console.error('Error:', error);
        const finalTime = stopTimer();
        spinner.style.display = 'none';
        addLogEntry(`Network error: ${error.message} (Failed after ${finalTime})`, 'error');
        displayError('Network error occurred. Please try again.');
    }
});

function addLogEntry(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.textContent = `[${timestamp}] ${message}`;
    progressLog.appendChild(logEntry);
    progressLog.scrollTop = progressLog.scrollHeight;
}

function displayExtractionResults(result, elapsedTime = '00:00') {
    // Show results container
    resultsContainer.style.display = 'block';

    // Display summary based on whether file already existed or was newly extracted
    const title = result.already_existed ? "File Already Exists!" : "Extraction Successful!";
    const statusMessage = result.already_existed ?
        "Text file was already available in S3" :
        `Successfully extracted text from ${result.total_images} images`;

    extractionSummary.innerHTML = `
            <div class="success-message">
                <strong>${title}</strong><br>
                ${statusMessage}<br>
                <strong>Time Taken: ${elapsedTime}</strong><br>
                ${result.total_images > 0 ? `Total Images Processed: ${result.total_images}<br>` : ''}
                ${result.text_files_created > 0 ? `Text Files Created: ${result.text_files_created}<br>` : ''}
                Chapter ID: ${result.chapter_id}<br>
                Resource ID: ${result.resource_id}<br>
                S3 Path: ${result.s3_path}
            </div>
            <div class="buttons-container">
                <button class="view-text-button" onclick="loadExtractedText('${result.chapter_id}', '${result.resource_id}')">
                    View Extracted Text
                </button>
                <button class="download-button" onclick="downloadMCQText('${result.chapter_id}', '${result.resource_id}')">
                    Download Text File
                </button>
            </div>
            <div class="link-container">
                <label>Download Link:</label><br>
                <input type="text" class="link-input" value="${window.location.origin}/pdfExtractor/downloadMcqTextFile?chapterId=${result.chapter_id}&resId=${result.resource_id}" readonly>
                <button class="copy-link-button" onclick="copyDownloadLink('${result.chapter_id}', '${result.resource_id}')">Copy Link</button>
            </div>
        `;

    // Clear text content
    textContent.innerHTML = '';
}

function displayError(message) {
    resultsContainer.style.display = 'block';
    extractionSummary.innerHTML = `
            <div class="error-message">
                <strong>Extraction Failed!</strong><br>
                ${message}
            </div>
        `;
    textContent.innerHTML = '';
}

// Polling function for task status
async function pollTaskStatus(taskId) {
    const pollInterval = 5000; // 5 seconds
    let pollCount = 0;
    const maxPolls = 360; // Maximum 30 minutes (360 * 5 seconds)

    const poll = async () => {
        try {
            pollCount++;
            addLogEntry(`Checking status...`, 'info');

            const response = await fetch(`/pdfExtractor/mcqExtractorStatus`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: taskId
                })
            });

            const statusResult = await response.json();

            if (statusResult.status === 'COMPLETED') {
                // Task completed successfully
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                addLogEntry(`Text extraction completed successfully! (Completed in ${finalTime})`, 'success');

                // Display results from the task result
                if (statusResult.result) {
                    displayExtractionResults(statusResult.result, finalTime);
                } else {
                    addLogEntry('Task completed but no result data available', 'warning');
                }

                return; // Stop polling

            } else if (statusResult.status === 'FAILED') {
                // Task failed
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                const errorMsg = statusResult.error_message || 'Unknown error occurred';
                addLogEntry(`Error: ${errorMsg} (Failed after ${finalTime})`, 'error');
                displayError(errorMsg);

                return; // Stop polling

            } else if (statusResult.status === 'not_found') {
                // Task not found
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                addLogEntry(`Error: Task not found (Failed after ${finalTime})`, 'error');
                displayError('Task not found');

                return; // Stop polling

            } else if (statusResult.status === 'IN_PROGRESS') {
                addLogEntry('Task is in progress...', 'info');

            } else if (statusResult.status === 'STARTED') {
                addLogEntry('Task is starting...', 'info');
            }

            // Continue polling if not completed/failed and under max polls
            if (pollCount < maxPolls) {
                setTimeout(poll, pollInterval);
            } else {
                // Max polls reached
                const finalTime = stopTimer();
                spinner.style.display = 'none';

                addLogEntry(`Polling timeout reached (${finalTime})`, 'error');
                displayError('Task is taking too long. Please check back later.');
            }

        } catch (error) {
            console.error('Error polling task status:', error);
            addLogEntry(`Error checking status: ${error.message}`, 'error');

            // Continue polling on error (might be temporary network issue)
            if (pollCount < maxPolls) {
                setTimeout(poll, pollInterval);
            } else {
                const finalTime = stopTimer();
                spinner.style.display = 'none';
                displayError('Failed to check task status');
            }
        }
    };

    // Start polling after a short delay
    setTimeout(poll, 2000); // Wait 2 seconds before first poll
}

async function loadExtractedText(chapterId, resId) {
    try {
        addLogEntry('Loading extracted text...', 'info');

        const response = await fetch(`/pdfExtractor/getMCQText?chapterId=${chapterId}&resId=${resId}`);
        const result = await response.json();

        if (result.status === 'success') {
            addLogEntry('Text loaded successfully!', 'success');
            displayTextContent(result.content, chapterId, resId);
        } else {
            addLogEntry(`Error loading text: ${result.message}`, 'error');
            textContent.innerHTML = `<div class="error-message">Error loading text: ${result.message}</div>`;
        }

    } catch (error) {
        console.error('Error loading text:', error);
        addLogEntry(`Error loading text: ${error.message}`, 'error');
        textContent.innerHTML = `<div class="error-message">Error loading text: ${error.message}</div>`;
    }
}

function displayTextContent(content, chapterId = null, resId = null) {
    // Get chapter and resource IDs from the current extraction results if not provided
    if (!chapterId || !resId) {
        const summaryDiv = document.getElementById('extractionSummary');
        if (summaryDiv) {
            const summaryText = summaryDiv.textContent;
            const chapterMatch = summaryText.match(/Chapter ID: (\d+)/);
            const resourceMatch = summaryText.match(/Resource ID: (\d+)/);
            if (chapterMatch) chapterId = chapterMatch[1];
            if (resourceMatch) resId = resourceMatch[1];
        }
    }

    let buttonsHtml = '<button class="copy-button" onclick="copyTextToClipboard()">Copy Text</button>';

    // Add download button if we have the required IDs
    if (chapterId && resId) {
        buttonsHtml += `<button class="download-button" onclick="downloadMCQText('${chapterId}', '${resId}')" style="margin-left: 10px;">Download Text File</button>`;
    }

    textContent.innerHTML = `
            <div style="margin-bottom: 15px;">
                ${buttonsHtml}
            </div>
            <pre id="textContentData">${content}</pre>
        `;
    renderMathInElement(textContent, {
        delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
        ],
        throwOnError: false,
        output: 'html',
        ignoredTags: []
    });
}

function copyTextToClipboard() {
    const textData = document.getElementById('textContentData').textContent;
    navigator.clipboard.writeText(textData).then(() => {
        addLogEntry('Text copied to clipboard!', 'success');
    }).catch(err => {
        console.error('Failed to copy text: ', err);
        addLogEntry('Failed to copy text to clipboard', 'error');
    });
}

function downloadMCQText(chapterId, resId) {
    // Show loading state on button
    const downloadButtons = document.querySelectorAll('.download-button');
    downloadButtons.forEach(button => {
        const originalText = button.textContent;
        button.textContent = 'Downloading...';
        button.disabled = true;

        // Restore button state after a short delay
        setTimeout(() => {
            button.textContent = originalText;
            button.disabled = false;
        }, 2000);
    });

    const downloadUrl = `/pdfExtractor/downloadMcqTextFile?chapterId=${chapterId}&resId=${resId}`;
    window.open(downloadUrl, '_blank');
    addLogEntry('Download started...', 'info');
}

function copyDownloadLink(chapterId, resId) {
    const downloadUrl = `${window.location.origin}/pdfExtractor/downloadMcqTextFile?chapterId=${chapterId}&resId=${resId}`;
    navigator.clipboard.writeText(downloadUrl).then(() => {
        addLogEntry('Download link copied to clipboard!', 'success');

        // Show visual feedback on the copy button
        const copyButtons = document.querySelectorAll('.copy-link-button');
        copyButtons.forEach(button => {
            const originalText = button.textContent;
            button.textContent = 'Copied!';
            setTimeout(() => {
                button.textContent = originalText;
            }, 2000);
        });
    }).catch(err => {
        console.error('Failed to copy link: ', err);
        addLogEntry('Failed to copy download link', 'error');
    });
}
