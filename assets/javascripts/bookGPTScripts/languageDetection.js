function detectLanguage(inputText) {
    // Preprocess the text
    const text = inputText.trim().replace(/\s+/g, ' ');

    // Comprehensive language detection patterns
    const languagePatterns = [
        // Indian Languages
        {
            code: 'mr',
            name: 'Marathi',
            ranges: [{ start: 0x0900, end: 0x097F }],
            minCharMatch: 3,
            distinctiveChars: ['ॐ', 'ऽ', 'ाँ', 'ृ', 'ॅ', 'ॆ']
        },
        {
            code: 'hi',
            name: 'Hindi',
            ranges: [{ start: 0x0900, end: 0x097F }],
            minCharMatch: 3,
            distinctiveChars: ['ि', 'ी', 'ु', 'ू', 'े', 'ो', 'ौ']
        },
        {
            code: 'bn',
            name: 'Bengali',
            ranges: [{ start: 0x0980, end: 0x09FF }],
            minCharMatch: 3,
            distinctiveChars: ['ে', 'ো', 'ৎ', 'ৱ', 'ঃ', 'ৃ']
        },
        {
            code: 'gu',
            name: 'Gujarati',
            ranges: [{ start: 0x0A80, end: 0x0AFF }],
            minCharMatch: 3,
            distinctiveChars: ['ાં', 'ું', 'ઃ', 'ૅ', 'ે', 'ૈ', 'ો', 'ૌ']
        },
        {
            code: 'pa',
            name: 'Punjabi',
            ranges: [{ start: 0x0A00, end: 0x0A7F }],
            minCharMatch: 3,
            distinctiveChars: ['ਾ', 'ਿ', 'ੀ', 'ੁ', 'ੂ', 'ੇ', 'ੋ', 'ੌ', 'ੰ', 'ੱ']
        },
        {
            code: 'or',
            name: 'Odia',
            ranges: [{ start: 0x0B00, end: 0x0B7F }],
            minCharMatch: 3,
            distinctiveChars: ['ଁ', 'ଃ', 'ାଁ', 'ୃ', 'େ', 'ୈ', 'ୋ', 'ୌ']
        },
        {
            code: 'te',
            name: 'Telugu',
            ranges: [{ start: 0x0C00, end: 0x0C7F }],
            minCharMatch: 3,
            distinctiveChars: ['ా', 'ి', 'ీ', 'ు', 'ూ', 'ృ', 'ె', 'ే', 'ై', 'ొ', 'ో', 'ౌ']
        },
        {
            code: 'kn',
            name: 'Kannada',
            ranges: [{ start: 0x0C80, end: 0x0CFF }],
            minCharMatch: 3,
            distinctiveChars: ['ಾ', 'ಿ', 'ೀ', 'ು', 'ೂ', 'ೃ', 'ೆ', 'ೇ', 'ೈ', 'ೊ', 'ೋ', 'ೌ']
        },
        {
            code: 'ta',
            name: 'Tamil',
            ranges: [{ start: 0x0B80, end: 0x0BFF }],
            minCharMatch: 3,
            distinctiveChars: ['ா', 'ி', 'ீ', 'ு', 'ூ', 'ெ', 'ே', 'ை', 'ொ', 'ோ', 'ௌ']
        },
        {
            code: 'ml',
            name: 'Malayalam',
            ranges: [{ start: 0x0D00, end: 0x0D7F }],
            minCharMatch: 3,
            distinctiveChars: ['ാ', 'ിഁ', 'ീ', 'ു', 'ൂ', 'ൃ', 'െ', 'േ', 'ൈ', 'ൊ', 'ോ', 'ൌ']
        },

        // International Languages
        {
            code: 'en',
            name: 'English',
            ranges: [{ start: 0x0000, end: 0x007F }],
            minCharMatch: 5,
            distinctiveChars: ['q', 'w', 'x', 'z']
        },
        {
            code: 'es',
            name: 'Spanish',
            ranges: [{ start: 0x0000, end: 0x007F }],
            minCharMatch: 3,
            distinctiveChars: ['á', 'é', 'í', 'ó', 'ú', 'ñ', '¿', '¡']
        },
        {
            code: 'fr',
            name: 'French',
            ranges: [{ start: 0x0000, end: 0x007F }],
            minCharMatch: 3,
            distinctiveChars: ['à', 'â', 'ç', 'é', 'è', 'ê', 'ë', 'î', 'ï', 'ô', 'û', 'ù', 'ü', 'ÿ']
        },
        {
            code: 'de',
            name: 'German',
            ranges: [{ start: 0x0000, end: 0x007F }],
            minCharMatch: 3,
            distinctiveChars: ['ä', 'ö', 'ü', 'ß']
        },
        {
            code: 'ru',
            name: 'Russian',
            ranges: [{ start: 0x0400, end: 0x04FF }],
            minCharMatch: 3,
            distinctiveChars: ['ё', 'щ', 'ъ', 'ы', 'э', 'ю', 'я']
        },
        {
            code: 'ar',
            name: 'Arabic',
            ranges: [{ start: 0x0600, end: 0x06FF }],
            minCharMatch: 3,
            distinctiveChars: ['ء', 'آ', 'أ', 'ؤ', 'إ', 'ئ', 'ة']
        },
        {
            code: 'zh',
            name: 'Chinese',
            ranges: [{ start: 0x4E00, end: 0x9FFF }],
            minCharMatch: 3,
            distinctiveChars: ['的', '是', '了', '在', '有']
        },
        {
            code: 'ja',
            name: 'Japanese',
            ranges: [
                { start: 0x3040, end: 0x309F },  // Hiragana
                { start: 0x30A0, end: 0x30FF },  // Katakana
                { start: 0x4E00, end: 0x9FFF }   // Kanji
            ],
            minCharMatch: 3,
            distinctiveChars: ['は', 'が', 'を', 'に', 'で']
        },
        {
            code: 'ko',
            name: 'Korean',
            ranges: [{ start: 0xAC00, end: 0xD7A3 }],
            minCharMatch: 3,
            distinctiveChars: ['은', '는', '이', '가', '를']
        },
        {
            code: 'pt',
            name: 'Portuguese',
            ranges: [{ start: 0x0000, end: 0x007F }],
            minCharMatch: 3,
            distinctiveChars: ['ã', 'õ', 'á', 'é', 'í', 'ó', 'ú', 'ç']
        },
        {
            code: 'it',
            name: 'Italian',
            ranges: [{ start: 0x0000, end: 0x007F }],
            minCharMatch: 3,
            distinctiveChars: ['à', 'è', 'ì', 'ò', 'ù']
        }
    ];

    function isInRange(char, ranges) {
        const charCode = char.charCodeAt(0);
        return ranges.some(
            (range) => charCode >= range.start && charCode <= range.end
        );
    }

    function analyzeLanguage(text) {
        if (!text || text.length < 3) {
            return { code: 'unknown', name: 'Unknown Language' };
        }

        const languageScores = languagePatterns.map((lang) => {
            // Count characters in language range
            const matchedChars = [...text].filter((char) =>
                isInRange(char, lang.ranges)
            );

            // Count distinctive characters
            const distinctiveCharCount = [...text].filter((char) =>
                lang.distinctiveChars.some(dc => char.includes(dc))
            ).length;

            return {
                ...lang,
                score: matchedChars.length,
                distinctiveScore: distinctiveCharCount
            };
        });

        // Sort by total score (character match + distinctive character match)
        const sortedScores = languageScores.sort((a, b) =>
            (b.score + b.distinctiveScore) - (a.score + a.distinctiveScore)
        );

        const topLanguage = sortedScores[0];

        if (topLanguage.score >= topLanguage.minCharMatch) {
            return {
                code: topLanguage.code,
                name: topLanguage.name
            };
        }

        return {
            code: 'unknown',
            name: 'Unknown Language'
        };
    }

    return analyzeLanguage(text);
}