document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('translatorForm');
    const loaderContainer = document.getElementById('loaderContainer');
    const loaderTitle = document.getElementById('loaderTitle');
    const loaderSubtitle = document.getElementById('loaderSubtitle');
    const progressBarFill = document.getElementById('progressBarFill');
    const progressText = document.getElementById('progressText');
    const logContainer = document.getElementById('logContainer');
    const progressLog = document.getElementById('progressLog');
    const resultContainer = document.getElementById('resultContainer');
    const translationResults = document.getElementById('translationResults');
    let translationId = null;

    function addLogEntry(message, type = 'info') {
        const entry = document.createElement('div');
        entry.className = 'log-entry';

        // Add timestamp
        const now = new Date();
        const timestamp = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        entry.setAttribute('data-time', timestamp);

        // Set message content
        entry.textContent = message;

        // Add specific styling based on message content or type
        if (message.toLowerCase().includes('error') || type === 'error') {
            entry.classList.add('log-error');
            entry.style.backgroundColor = '#f8d7da';
            entry.style.color = '#721c24';
            entry.style.borderLeftColor = '#dc3545';
        } else if (message.toLowerCase().includes('completed') || message.toLowerCase().includes('success') || type === 'success') {
            entry.classList.add('log-success');
            entry.style.backgroundColor = '#d4edda';
            entry.style.color = '#155724';
            entry.style.borderLeftColor = '#28a745';
        } else if (message.toLowerCase().includes('starting') || message.toLowerCase().includes('initializing') || type === 'start') {
            entry.classList.add('log-start');
            entry.style.backgroundColor = '#d1ecf1';
            entry.style.color = '#0c5460';
            entry.style.borderLeftColor = '#17a2b8';
        } else if (message.toLowerCase().includes('status:') || message.toLowerCase().includes('checking') || type === 'status') {
            entry.classList.add('log-status');
            entry.style.backgroundColor = '#fff3cd';
            entry.style.color = '#856404';
            entry.style.borderLeftColor = '#ffc107';
        } else if (message.toLowerCase().includes('task started') || type === 'task') {
            entry.classList.add('log-task');
            entry.style.backgroundColor = '#e2e3e5';
            entry.style.color = '#383d41';
            entry.style.borderLeftColor = '#6c757d';
            entry.style.fontWeight = '500';
        }

        // Add entry with animation
        entry.style.opacity = '0';
        entry.style.transform = 'translateX(-10px)';
        progressLog.appendChild(entry);

        // Trigger animation
        setTimeout(() => {
            entry.style.opacity = '1';
            entry.style.transform = 'translateX(0)';
            entry.style.transition = 'all 0.3s ease-out';
        }, 10);

        // Auto-scroll to bottom
        progressLog.scrollTop = progressLog.scrollHeight;

        // Add a subtle sound effect for important messages (optional)
        if (type === 'error' || type === 'success') {
            // You can add a subtle beep sound here if desired
        }
    }

    function updateLoader(title, subtitle, progress) {
        loaderTitle.textContent = title;
        loaderSubtitle.textContent = subtitle;
        progressBarFill.style.width = progress + '%';
        progressText.textContent = progress + '%';
    }

    function showLoader() {
        loaderContainer.style.display = 'block';
        updateLoader('Initializing Translation...', 'Preparing to process your PDF file', 0);
    }

    function hideLoader() {
        loaderContainer.style.display = 'none';
    }

    // Timer functionality
    let startTime = null;
    let timerInterval = null;

    function startTimer() {
        startTime = new Date();
        timerInterval = setInterval(updateTimer, 1000);
    }

    function updateTimer() {
        if (startTime) {
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const timerElement = document.getElementById('timer');
            if (timerElement) {
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }
    }

    function stopTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }
        if (startTime) {
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        return '00:00';
    }

    function resetTimer() {
        stopTimer();
        startTime = null;
        const timerElement = document.getElementById('timer');
        if (timerElement) {
            timerElement.textContent = '00:00';
        }
    }

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Clear previous results
        progressLog.innerHTML = '';
        translationResults.innerHTML = '';
        resultContainer.style.display = 'none';

        // Reset timer and show loader
        resetTimer();
        showLoader();
        logContainer.style.display = 'block';

        // Start the timer
        startTimer();

        // Get form values
        const pdfFile = document.getElementById('pdfFile').files[0];
        const totalQuestions = document.getElementById('totalQuestions').value;
        const sourceLanguage = document.getElementById('sourceLanguage').value;
        const destinationLanguage = document.getElementById('destinationLanguage').value;

        // Validate inputs
        if (!pdfFile || !totalQuestions || !sourceLanguage || !destinationLanguage) {
            addLogEntry('Error: All fields are required', 'error');
            hideLoader();
            return;
        }

        if (sourceLanguage === destinationLanguage) {
            addLogEntry('Error: Source and destination languages must be different', 'error');
            hideLoader();
            return;
        }

        addLogEntry(`Starting translation for file: ${pdfFile.name}`, 'start');
        addLogEntry(`Source language: ${sourceLanguage}, Destination language: ${destinationLanguage}`, 'info');

        try {
            // Create FormData for file upload
            const formData = new FormData();
            formData.append('pdfFile', pdfFile);
            formData.append('total_questions', totalQuestions);
            formData.append('source_language', sourceLanguage);
            formData.append('destination_language', destinationLanguage);
            formData.append('username', 'web_user');

            // Call the API to start translation
            const response = await fetch('/pdfExtractor/startMcqTranslation', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'started') {
                // Task started successfully, begin polling
                addLogEntry(`Task started with ID: ${data.task_id}`, 'task');
                addLogEntry('Polling for status updates...', 'status');

                // Start polling for status
                pollTaskStatus(data.task_id);
            } else {
                // Stop timer and get final time
                const finalTime = stopTimer();

                // Hide loader
                hideLoader();

                addLogEntry(`Error: ${data.message} (Failed after ${finalTime})`, 'error');
            }

        } catch (error) {
            // Stop timer and get final time
            const finalTime = stopTimer();

            // Hide loader
            hideLoader();

            addLogEntry(`Error: ${error.message} (Failed after ${finalTime})`, 'error');
        }
    });

    // Polling function for task status
    async function pollTaskStatus(taskId) {
        const pollInterval = 5000; // 5 seconds
        let pollCount = 0;
        const maxPolls = 360; // Maximum 30 minutes (360 * 5 seconds)

        const poll = async () => {
            try {
                pollCount++;
                addLogEntry(`Checking status...`, 'status');

                const response = await fetch('/pdfExtractor/mcqTranslatorStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        task_id: taskId,
                    })
                });

                const statusResult = await response.json();

                if (statusResult.status === 'COMPLETED') {
                    // Task completed successfully
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Translation completed successfully! (Completed in ${finalTime})`, 'success');

                    // Display results from the task result
                    if (statusResult.result) {
                        translationId = statusResult.result.translation_id;
                        displayTranslationResults(statusResult.result, finalTime);
                    } else {
                        addLogEntry('Task completed but no result data available', 'error');
                    }

                    return; // Stop polling

                } else if (statusResult.status === 'FAILED') {
                    // Task failed
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Translation failed: ${statusResult.message} (Failed after ${finalTime})`, 'error');
                    return; // Stop polling

                } else if (statusResult.status === 'not_found') {
                    // Task not found
                    const finalTime = stopTimer();
                    hideLoader();

                    addLogEntry(`Task not found: ${statusResult.message} (Failed after ${finalTime})`, 'error');
                    return; // Stop polling

                } else {
                    // Task still in progress
                    addLogEntry(`Status: ${statusResult.status} - ${statusResult.message || 'Processing...'}`, 'status');

                    // Continue polling if we haven't exceeded max polls
                    if (pollCount < maxPolls) {
                        setTimeout(poll, pollInterval);
                    } else {
                        // Timeout reached
                        const finalTime = stopTimer();
                        hideLoader();
                        addLogEntry(`Translation timed out after ${maxPolls * pollInterval / 1000 / 60} minutes (Timed out after ${finalTime})`, 'error');
                    }
                }

            } catch (error) {
                addLogEntry(`Error checking status: ${error.message}`, 'error');

                // Continue polling on error (might be temporary network issue)
                if (pollCount < maxPolls) {
                    setTimeout(poll, pollInterval);
                } else {
                    const finalTime = stopTimer();
                    hideLoader();
                    addLogEntry(`Polling failed after ${maxPolls} attempts (Failed after ${finalTime})`, 'error');
                }
            }
        };

        // Start polling
        setTimeout(poll, pollInterval);
    }

    // Function to display translation results
    function displayTranslationResults(result, completionTime) {
        // Show the translation results container
        resultContainer.style.display = 'block';

        // Create buttons container
        const buttonsContainer = document.createElement('div');
        buttonsContainer.className = 'buttons-container';

        // Create a button to view the translated content
        const viewButton = document.createElement('button');
        viewButton.textContent = 'View Translated Content';
        viewButton.className = 'view-button';
        viewButton.addEventListener('click', async function() {
            try {
                // Show loading state on button
                const originalText = viewButton.textContent;
                viewButton.textContent = 'Loading...';
                viewButton.disabled = true;

                addLogEntry('Fetching translated content...', 'status');

                const contentResponse = await fetch(`/pdfExtractor/getTranslatedText?task_id=${result.translation_id}`);
                const contentData = await contentResponse.json();

                if (contentData.status === 'success') {
                    // Clear previous content
                    translationResults.innerHTML = '';
                    translationResults.appendChild(buttonsContainer);

                    // Add the translated text in a pre element
                    const textPre = document.createElement('pre');
                    textPre.className = 'translation-text';
                    textPre.textContent = contentData.content;
                    translationResults.appendChild(textPre);

                    addLogEntry('Translated content loaded successfully', 'success');
                } else {
                    addLogEntry(`Error fetching translated content: ${contentData.message}`, 'error');
                }
            } catch (error) {
                addLogEntry(`Error: ${error.message}`, 'error');
            } finally {
                // Restore button state
                viewButton.textContent = originalText;
                viewButton.disabled = false;
            }
        });

        // Create download button
        const downloadButton = document.createElement('button');
        downloadButton.textContent = 'Download Translated File';
        downloadButton.className = 'download-button';
        downloadButton.addEventListener('click', function() {
            // Show loading state on button
            const originalText = downloadButton.textContent;
            downloadButton.textContent = 'Downloading...';
            downloadButton.disabled = true;

            const downloadUrl = `/pdfExtractor/downloadTranslatedMcqTextFile?task_id=${result.translation_id}`;
            window.open(downloadUrl, '_blank');
            addLogEntry('Download started...', 'info');

            // Restore button state after a short delay
            setTimeout(() => {
                downloadButton.textContent = originalText;
                downloadButton.disabled = false;
            }, 2000);
        });

        // Create link display and copy functionality
        const linkContainer = document.createElement('div');
        linkContainer.className = 'link-container';

        const linkLabel = document.createElement('label');
        linkLabel.textContent = 'Download Link:';

        const linkInput = document.createElement('input');
        linkInput.type = 'text';
        linkInput.className = 'link-input';
        linkInput.value = `${window.location.origin}/pdfExtractor/downloadTranslatedMcqTextFile?task_id=${result.translation_id}`;
        linkInput.readOnly = true;

        const copyButton = document.createElement('button');
        copyButton.textContent = 'Copy Link';
        copyButton.className = 'copy-button';
        copyButton.addEventListener('click', function() {
            linkInput.select();
            document.execCommand('copy');
            copyButton.textContent = 'Copied!';
            addLogEntry('Download link copied to clipboard', 'success');
            setTimeout(() => {
                copyButton.textContent = 'Copy Link';
            }, 2000);
        });

        // Add elements to containers
        buttonsContainer.appendChild(viewButton);
        buttonsContainer.appendChild(downloadButton);

        linkContainer.appendChild(linkLabel);
        linkContainer.appendChild(linkInput);
        linkContainer.appendChild(copyButton);

        translationResults.appendChild(buttonsContainer);
        translationResults.appendChild(linkContainer);

        // Add completion summary with better formatting
        addLogEntry(`🎉 Translation completed in ${completionTime}`, 'success');
        addLogEntry(`📋 Translation ID: ${result.translation_id}`, 'info');
        addLogEntry(`📄 File: ${result.filename}`, 'info');
        addLogEntry(`❓ Questions: ${result.total_questions}`, 'info');
        addLogEntry(`🌐 ${result.source_language} → ${result.destination_language}`, 'info');
    }
});