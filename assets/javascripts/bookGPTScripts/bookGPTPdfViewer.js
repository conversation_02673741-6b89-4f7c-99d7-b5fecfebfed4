let title = '';

const bookGPT=true;
const knimbusInstUser = false;
const bookGPTReader = document.getElementById('bookGPTReader')
async function getPDF(resId,chapterId){
    gptResId = resId
    gptChapterId = chapterId
    showAppLoader(true)

    let resourceURL = ""

    if(bookFileType == "xhtml" || bookFileType == "html"){
        resourceURL = "/resources/epubReader?bookId="+gptBookId+"&resId="+gptResId+"&chapterId="+gptChapterId+"&bookGPT=true"
        if(!mobileView){
            bookGPTReader.style.height="95%"
        }
    }else if(bookFileType=="pdf"){
        resourceURL = "/resources/pdfReader?bookLang=&resId="+gptResId+"&bookId="+gptBookId+"&loggedInUser="+loggedInUser+"&bookUrl=/funlearn/getPdfFile?resId="+gptResId+"__encryptedKey="+encryptedKey+"&chapterId="+gptChapterId+"&title="+title+"&knimbusInstUser="+knimbusInstUser+"&mobileView="+isMb+"&bookGPT=true"
    }
    if(isSnapAvl){
        resourceURL +="#book_snaps"
    }
    if(enableCompiler == "true"){
        resourceURL +="&enableCompiler=true"
    }
    bookGPTReader.src = resourceURL
}
getPDF(gptResId,gptChapterId)
const groupByChapterId = (arr) => {
    return arr.reduce((acc, curr) => {
        if (!acc[curr.chapterId]) {
            acc[curr.chapterId] = {
                chapterId: curr.chapterId,
                chapterName: curr.chapterName,
                resources: [],
                previewChapter:curr.previewChapter,
                sortOrder:curr.sortOrder
            };
        }
        acc[curr.chapterId].resources.push({
            bookId: curr.bookId,
            link: curr.link,
            name: curr.name,
            resId: curr.resId,
            resType: curr.resType,
            vectorStored: curr.vectorStored
        });
        return acc;
    }, {});
};
const groupedData = groupByChapterId(chaptersList);
updateChapterDropDown(groupedData)
