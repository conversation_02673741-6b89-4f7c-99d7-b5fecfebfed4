/**
 * Created by achyutanand on 08/01/16.
 */
var quizId,resourceDtlId,objectiveMstId;
var currentQuestionNo=0;
var quizItems=[];
var firstQuestion = true;
var quizName;
var passage;
var saveAndGoNext=false;

var flds = new Array (
    'resourceName',
    'question'
);

var optionFlds = new Array (
    'option1',
    'option2'
);


function questionAdded(req){
    var quizItem={};
    var returnParams = req.split('&');

    //first one is resourceDtlId
    resourceDtlId = returnParams[0].split('=')[1];

    //second one quiz id
    quizId = returnParams[1].split('=')[1];

    // third one objectivemst id
    objectiveMstId = returnParams[2].split('=')[1];
    quizItem = quizItems[currentQuestionNo];
    quizItem.objectiveMstId = objectiveMstId;
    quizItems[currentQuestionNo] = quizItem;
    currentQuestionNo = quizItems.length;
    mode = 'add';

}

function questionSaved(req){
     if("failed"==req){
         if(siteId==9) {
             document.getElementById("savedNotificationText").innerHTML="Quiz failed. Please login again and try.";
         } else {
             document.getElementById("savedNotificationText").innerHTML = "<div class=\"alert alert-danger mb-0 w-100\" role=\"alert\"><p class='mb-0'>Quiz failed. Please login again and try.</p></div>";
         }
    }else{

         if(saveAndGoNext) {
             if((currentQuestionNo+1)<quizItems.length){
                 gotoQuestion((currentQuestionNo+1));
             }
         }else{
             if(siteId==9) {
                 document.getElementById("savedNotificationText").innerHTML="Saved";
             } else {
                 document.getElementById("savedNotificationText").innerHTML="<div class=\"alert alert-success mb-0 w-100\" role=\"alert\"><p class='mb-0'>Quiz saved successfully!</p></div>";
             }
         }
    }
    $('#savedNotification').show();
}
function validate(submitType){

    var allFilled=true;
    var quizItem={};
    $('.alert').hide();
    document.addquiz.question.value=CKEDITOR.instances.question.getData();
    document.addquiz.option1.value=CKEDITOR.instances.option1.getData();
    document.addquiz.option2.value=CKEDITOR.instances.option2.getData();
    document.addquiz.option3.value=CKEDITOR.instances.option3.getData();
    document.addquiz.option4.value=CKEDITOR.instances.option4.getData();
    document.addquiz.option5.value=CKEDITOR.instances.option5.getData();
    document.addquiz.answerDescription.value=CKEDITOR.instances.answerDescription.getData();
    if(pubUser) {
        document.addquiz.directions.value = CKEDITOR.instances.directions.getData();
        document.addquiz.passage.value = CKEDITOR.instances.passage.getData();
    }
    var field;
   for (i=0; i<flds.length; i++) {
        //condition to check for the quiz name only with the first question
        field = eval("document.addquiz."+flds[i]);
        if(flds[i] == 'resourceName' && quizItems.length > 0 ) continue;
        document.addquiz.question.value=CKEDITOR.instances.question.getData();
        if( !field.value ) {
            //actual code to check all fields needs to be entered. use the array of fields
             $("#"+flds[i]).addClass('has-error');
            $("#"+flds[i]).closest('.cktext').addClass('red-border');
            allFilled = false;
        }
        else{
            $("#"+flds[i]).removeClass('has-error');
            $("#"+flds[i]).closest('.cktext').removeClass('red-border');
        }

    }
    for (i=0; i<optionFlds.length; i++) {
        //condition to check for the quiz name only with the first question
        field = eval("document.addquiz."+optionFlds[i]);
        if( !(field.value ||   $("#file"+optionFlds[i]+"name").val() )) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#"+optionFlds[i]).addClass('red-border');
            $("#"+optionFlds[i]).closest('.cktext').addClass('red-border');
            allFilled = false;
        }
        else{
            $("#"+optionFlds[i]).removeClass('red-border');
            $("#"+optionFlds[i]).closest('.cktext').removeClass('red-border');
        }

    }
    if(pubUser) {
        if (document.getElementById("explainLink").value) {
            var checkUrl = document.getElementById("explainLink").value;
            if (checkUrl.includes("youtube") || checkUrl.includes("youtu.be")) {
                if (!validateYouTubeUrl(document.getElementById("explainLink").value)) {
                    allFilled = false;
                    $("#explainLink").addClass('has-error');
                    $("#explainLink").closest('.cktext').addClass('red-border');
                }
            }
        }
    }

    if(!allFilled){
        if(siteId==9) {
            document.getElementById("alertbox").innerHTML = "<b>Please complete required fields marked in red.</b>";
        } else {
            document.getElementById("alertbox").innerHTML = "<p class='mb-0'>Please complete required fields marked in red.</p>";
        }
        $('.alert').show();

    }else{
        if (!(document.getElementById('answer1').checked||document.getElementById('answer2').checked||document.getElementById('answer3').checked||document.getElementById('answer4').checked||document.getElementById('answer5').checked)){
            if(siteId==9) {
                document.getElementById("alertbox").innerHTML = "<b>Please select atleast 1 correct answer.</b>";
            } else {
                document.getElementById("alertbox").innerHTML = "<p class='mb-0'>Please select atleast 1 correct answer.</p>";
            }
            $('.alert').show();
            allFilled = false;
        }else if(document.getElementById("difficultylevel").selectedIndex==0){
            if(siteId==9) {
                document.getElementById("alertbox").innerHTML = "<b>Please select Difficulty level.</b>";
            } else {
                document.getElementById("alertbox").innerHTML = "<p class='mb-0'>Please select Difficulty level.</p>";
            }
            document.getElementById("difficultylevel").focus();
            $('.alert').show();
            allFilled = false;
        }
    }

    if(allFilled) {
        if(currentQuestionNo == 0) {
            if(siteId==9){
                document.getElementById("sidebar").innerHTML = "<br><b> Saved Questions </b> <br>";
            } else {
                document.getElementById("sidebar").innerHTML = "<div class='sidebar_title border-bottom mb-3'><h6><strong>Saved Questions</strong></h6></div>";
            }
            quizName = document.addquiz.resourceName.value;
            if(pubUser) {
                passage = document.addquiz.passage.value;
                quizItem.examSyllabus = document.addquiz.examSyllabus.value;
                quizItem.grade = document.addquiz.grade.value;
                quizItem.language1 = document.addquiz.language1.value;
                quizItem.language2 = document.addquiz.language2.value;
                quizItem.testStartDate = document.addquiz.testStartDate.value;
                quizItem.testEndDate = document.addquiz.testEndDate.value;
                quizItem.mcqTotalTime = document.getElementById('mcqTotalTime').value;
                 gradeId = document.addquiz.grade.value;
                syllabus = document.addquiz.examSyllabus.value;

            }
            if(submitType!="Save") $('.resourceName').hide();
        }else{

        }
        if(pubUser) {
            quizItem.examSyllabus = document.addquiz.examSyllabus.value;
            quizItem.grade = document.addquiz.grade.value;

        }
        quizItem.resourceName = quizName; // to use the same name in all items.. as the quizname input is visible only for first question
        quizItem.question = document.addquiz.question.value;
        if(document.addquiz.answer1.checked)  quizItem.answer1 = document.addquiz.answer1.value;
        if(document.addquiz.answer2.checked) quizItem.answer2 = document.addquiz.answer2.value;
        if(document.addquiz.answer3.checked) quizItem.answer3 = document.addquiz.answer3.value;
        if(document.addquiz.answer4.checked)  quizItem.answer4 = document.addquiz.answer4.value;
        if(document.addquiz.answer5.checked)  quizItem.answer5 = document.addquiz.answer5.value;
        quizItem.option1 = document.addquiz.option1.value;
        quizItem.option2 = document.addquiz.option2.value;
        quizItem.option3 = document.addquiz.option3.value;
        quizItem.option4 = document.addquiz.option4.value;
        quizItem.option5 = document.addquiz.option5.value;
        if(pubUser) {
            quizItem.hint = document.addquiz.hint.value;
        }
        quizItem.answerDescription = document.addquiz.answerDescription.value;
        if(pubUser) {
            quizItem.directions = document.addquiz.directions.value;
        }
        quizItem.difficultylevel = document.addquiz.difficultylevel[document.addquiz.difficultylevel.selectedIndex].value;
        if(pubUser) {
            quizItem.subject = document.addquiz.subject.value;
            quizItem.expiryDate = document.addquiz.expiryDate.value;
            quizItem.marks = document.addquiz.marks.value;
            quizItem.negativeMarks = document.addquiz.negativeMarks.value;
            quizItem.explainLink = document.addquiz.explainLink.value;
            quizItem.startTime = document.addquiz.startTime.value;
            quizItem.endTime = document.addquiz.endTime.value;

            //temp solution
            if (!quizItem.directionId && document.addquiz.direction.checked) {
                quizItem.directionId = -1;
            }
        }
        if(mode=='edit'){
            quizItems[currentQuestionNo]=quizItem;
            if(siteId==9) {
                document.getElementById("sidebar").innerHTML = "<br><b> Saved Questions </b> <br>";
            } else {
                document.getElementById("sidebar").innerHTML = "<div class='sidebar_title border-bottom mb-3'><h6><strong>Saved Questions</strong></h6></div>";
            }
            for(i=0;i<quizItems.length;i++){
                var quest = quizItems[i].question
                if(quest.indexOf("<p")==0) quest = quest.substring(3,(quest.length-5));
                if(siteId==9) {
                    document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='pagenumber-green' style='margin-bottom: 20px'>" + (i + 1)  + "</a>&nbsp;&nbsp;";
                } else {
                    document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='quiz-number btn btn-outline-success'>" + (i + 1)  + "</a>";
                }

            }
        }
        else {
            if(siteId==9) {
                document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + currentQuestionNo + ")' class='pagenumber-green' style='margin-bottom: 20px'>" + (currentQuestionNo + 1) + "</a>&nbsp;&nbsp;";
            } else {
                document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + currentQuestionNo + ")' class='quiz-number btn btn-outline-success'>" + (currentQuestionNo + 1) + "</a>";
            }
            quizItems.push(quizItem);
        }

    }
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
    return allFilled;

}
function setExamGroup(syllabus){
    var examsList= document.getElementById("examSyllabus");

    for(i=0; i<examsList.options.length;i++){
        if(examsList.options[i].value==syllabus){
            examsList.options[i].selected = true;
        }

    }
}

function setSelect(field, value){
    var fieldList= document.getElementById(field);

    for(i=0; i<fieldList.options.length;i++){
        if(fieldList.options[i].value==value){
            fieldList.options[i].selected = true;
        }

    }
}
function setSubject(subject){
    var subjectList= document.getElementById("subject");

    for(i=0; i<subjectList.options.length;i++){
        if(subjectList.options[i].value==subject){
            subjectList.options[i].selected = true;
        }

    }
}
function gotoQuestion(questionNo){
     document.getElementById("savedNotificationText").innerHTML="";
    var quizItem={};
    currentQuestionNo = questionNo;
    quizItem = quizItems[questionNo];

    //to clear the contents and previous states
    document.getElementById("addquiz").reset();
    if(publisherUser) {
        document.getElementById("previousDirection").innerHTML = "";
    }
    $("#previousDirection").hide();

    $('.resourceName').hide();
    if(questionNo == 0){
        $('.resourceName').show();
        document.addquiz.resourceName.value = quizName;
        if(publisherUser) {
            document.addquiz.testStartDate.value = quizItem.testStartDate;
            document.addquiz.testEndDate.value = quizItem.testEndDate;
            CKEDITOR.instances.passage.setData(passage);
        }
        // Load mcqTotalTime value
        if(quizItem.mcqTotalTime) {
            document.getElementById('mcqTotalTime').value = quizItem.mcqTotalTime;
        }
         if(quizItem.examSyllabus){
            setSelect('examSyllabus',quizItem.examSyllabus);
            getCreateGrade(quizItem.examSyllabus);
        }
        if(quizItem.language1) setSelect('language1',quizItem.language1);
        if(quizItem.language2) setSelect('language2',quizItem.language2);
    }else {
        if(syllabus){
            setSelect('examSyllabus',quizItem.examSyllabus);
            if(gradeId){
                setSelect('grade',gradeId);
            }
        }
    }
    document.getElementById('questionLabel').innerHTML="Question "+(1+questionNo);
    document.addquiz.question.value = quizItem.question;
    CKEDITOR.instances.question.setData(quizItem.question);
    CKEDITOR.instances.option1.setData(quizItem.option1);
    CKEDITOR.instances.option2.setData(quizItem.option2);
    CKEDITOR.instances.option3.setData(quizItem.option3);
    CKEDITOR.instances.option4.setData(quizItem.option4);
    CKEDITOR.instances.option5.setData(quizItem.option5);
    CKEDITOR.instances.answerDescription.setData(quizItem.answerDescription);
    if(publisherUser) {
        CKEDITOR.instances.directions.setData(quizItem.directions);
    }


    document.addquiz.option1.value = quizItem.option1;
    document.addquiz.option2.value = quizItem.option2;
    document.addquiz.option3.value = quizItem.option3;
    document.addquiz.option4.value = quizItem.option4;
    document.addquiz.option5.value = quizItem.option5;
    if(quizItem.difficultylevel!=null){
        if("Easy"==quizItem.difficultylevel) document.addquiz.difficultylevel.selectedIndex=1;
        else if("Medium"==quizItem.difficultylevel) document.addquiz.difficultylevel.selectedIndex=2;
        else if("Tough"==quizItem.difficultylevel) document.addquiz.difficultylevel.selectedIndex=3;
    }
    if(quizItem.subject){
       subject = quizItem.subject;
       setSubject(subject);
    }else if(quizItem.examSubject){
        subject = quizItem.examSubject;
        setSubject(subject);
    }
    if('Yes' == quizItem.answer1) document.addquiz.answer1.checked = true;
    if('Yes' == quizItem.answer2) document.addquiz.answer2.checked = true;
    if('Yes' == quizItem.answer3) document.addquiz.answer3.checked = true;
    if('Yes' == quizItem.answer4) document.addquiz.answer4.checked = true;
    if('Yes' == quizItem.answer5) document.addquiz.answer5.checked = true;
    if(quizItem.directionId) {
        document.addquiz.direction.checked = true;
        directionChanged(document.addquiz.direction);
    }
     objectiveMstId = quizItem.objectiveMstId;
    mode = 'edit';
    var eDate="";
    if(quizItem.expiryDate.length>9) eDate=quizItem.expiryDate.substring(0,10);
    if(publisherUser) {
        document.addquiz.expiryDate.value = eDate;
        document.addquiz.marks.value = quizItem.marks;
        document.addquiz.negativeMarks.value = quizItem.negativeMarks;
        document.addquiz.explainLink.value = quizItem.explainLink;
        document.addquiz.startTime.value = quizItem.startTime;
        document.addquiz.endTime.value = quizItem.endTime;
    }

    $('#deletebutton').show();
    $("html, body").animate({ scrollTop: 0 }, "fast");

}

function directionChanged(field){

    if(field.checked&&!(CKEDITOR.instances.directions.getData())){
         for(i=(currentQuestionNo-1);i>=0;i--){
            if(quizItems[i].directions){
                document.getElementById("previousDirection").innerHTML = quizItems[i].directions;
                $("#previousDirection").show();
                break;
            }
        }
    }
    else{
        document.getElementById("previousDirection").innerHTML ="";
        $("#previousDirection").hide();
    }
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
}


function validateYouTubeUrl(url) {
    if (url != undefined || url != '') {
        var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|\?v=)([^#\&\?]*).*/;
        var match = url.match(regExp);
        if (match && match[2].length == 11) {
            // Do anything for being valid
            // if need to change the url to embed url then use below line
            //$('#ytplayerSide').attr('src', 'https://www.youtube.com/embed/' + match[2] + '?autoplay=0');
            return true;
        } else {
            // Do anything for not being valid
            return false;
        }
    }

    return false;
}

function getIdFromYouTubeURL(url) {
    if(url.match(/(youtu.be)/)){
        var split_c = "/";
        var split_n = 3;
    }

    if(url.match(/(youtube.com)/)){
        var split_c = "v=";
        var split_n = 1;
    }

    var getYouTubeVideoID = url.split(split_c)[split_n];
    return getYouTubeVideoID.replace(/(&)+(.*)/, "");
}

