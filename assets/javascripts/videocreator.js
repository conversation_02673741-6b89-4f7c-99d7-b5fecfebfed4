/**
 * Created by achyutanand on 08/01/16.
 */
var quizId,resourceDtlId,objectiveMstId;
var currentQuestionNo=0;
var quizItems=[];
var firstQuestion = true;
var quizName;
var passage;
var saveAndGoNext=false;

var flds = new Array (
    'resourceName',
    'question'
);

var optionFlds = new Array (
    'option1',
    'option2'
);


function questionAdded(req){
    var quizItem={};
    var returnParams = req.split('&');
    //first one is resourceDtlId
    resourceDtlId = returnParams[0].split('=')[1];
    // third one objectivemst id
    objectiveMstId = returnParams[1].split('=')[1];
    quizItem = quizItems[currentQuestionNo];
    quizItem.objectiveMstId = objectiveMstId;
    quizItems[currentQuestionNo] = quizItem;
    currentQuestionNo = quizItems.length;
    mode = 'add';

}

function questionSaved(req){
    if("failed"==req){
        document.getElementById("savedNotificationText").innerHTML="Quiz failed. Please login again and try.";
    }else{

        if(saveAndGoNext) {
            if((currentQuestionNo+1)<quizItems.length){
                gotoQuestion((currentQuestionNo+1));
            }
        }else{
            document.getElementById("savedNotificationText").innerHTML="Saved";
        }
    }
    $('#savedNotification').show();
}
function validate(submitType){
    var allFilled=true;
    var quizItem={};
    $('.alert').hide();
    document.addquiz.notes.value=CKEDITOR.instances.notes.getData();
    if(allFilled) {
        if(currentQuestionNo == 0) {
            document.getElementById("sidebar").innerHTML = "<br><div class='sidebar_title border-bottom '><h6><strong>Saved Videos</strong></h6></div> <br>";
            quizName = document.addquiz.resourceName.value;
            quizItem.description = document.addquiz.description.value;
            quizItem.slideName = document.addquiz.slideName.value;
            quizItem.description2 = document.addquiz.description2.value;
            quizItem.description3 = document.addquiz.description3.value;
            quizItem.description4 = document.addquiz.description3.value;
            quizItem.description5 = document.addquiz.description3.value;
            quizItem.notes = document.addquiz.notes.value;
            quizItem.notes1 = document.addquiz.notes1.value;
            quizItem.notes2 = document.addquiz.notes2.value;
            quizItem.notes3 = document.addquiz.notes3.value;
            quizItem.notes4 = document.addquiz.notes4.value;
            quizItem.notes5 = document.addquiz.notes5.value;

        }else{

        }
        quizItem.description = document.addquiz.description.value;
        quizItem.slideName = document.addquiz.slideName.value;
        quizItem.description2 = document.addquiz.description2.value;
        quizItem.description3 = document.addquiz.description3.value;
        quizItem.description4 = document.addquiz.description3.value;
        quizItem.description5 = document.addquiz.description3.value;
        quizItem.notes = document.addquiz.notes.value;
        quizItem.notes1 = document.addquiz.notes1.value;
        quizItem.notes2 = document.addquiz.notes2.value;
        quizItem.notes3 = document.addquiz.notes3.value;
        quizItem.notes4 = document.addquiz.notes4.value;
        quizItem.notes5 = document.addquiz.notes5.value;
        quizItem.resourceName = quizName;


        if(mode=='edit'){
            quizItems[currentQuestionNo]=quizItem;
            document.getElementById("sidebar").innerHTML = "<br><b> Saved Questions </b> <br>";
            for(i=0;i<quizItems.length;i++){

                document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='pagenumber-green' style='margin-bottom: 20px'>" + (i + 1)  + "</a>&nbsp;&nbsp;";

            }
        }
        else {
            document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + currentQuestionNo + ")' class='pagenumber-green' style='margin-bottom: 20px'>" + (currentQuestionNo + 1) + "</a>&nbsp;&nbsp;";
            quizItems.push(quizItem);
        }

    }
    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
    return allFilled;

}
function setExamGroup(syllabus){
    var examsList= document.getElementById("examSyllabus");

    for(i=0; i<examsList.options.length;i++){
        if(examsList.options[i].value==syllabus){
            examsList.options[i].selected = true;
        }

    }
}

function setSelect(field, value){
    var fieldList= document.getElementById(field);

    for(i=0; i<fieldList.options.length;i++){
        if(fieldList.options[i].value==value){
            fieldList.options[i].selected = true;
        }

    }
}
function setSubject(subject){
    var subjectList= document.getElementById("subject");

    for(i=0; i<subjectList.options.length;i++){
        if(subjectList.options[i].value==subject){
            subjectList.options[i].selected = true;
        }

    }
}
function gotoQuestion(questionNo){
    document.getElementById("savedNotificationText").innerHTML="";
    var quizItem={};
    currentQuestionNo = questionNo;
    quizItem = quizItems[questionNo];

    //to clear the contents and previous states
    document.getElementById("addquiz").reset();
    document.getElementById("previousDirection").innerHTML ="";
    $("#previousDirection").hide();

    $('.resourceName').hide();
    if(questionNo == 0){
        $('.resourceName').show();
        document.addquiz.resourceName.value = quizName;

    }
    CKEDITOR.instances.notes.setData(quizItem.notes);
    CKEDITOR.instances.notes1.setData(quizItem.notes1);
    CKEDITOR.instances.notes2.setData(quizItem.notes2);
    CKEDITOR.instances.notes3.setData(quizItem.notes3);
    CKEDITOR.instances.notes4.setData(quizItem.notes4);
    CKEDITOR.instances.notes5.setData(quizItem.notes5);


    document.addquiz.description.value = quizItem.description;
    document.addquiz.description2.value = quizItem.description2;
    document.addquiz.description3.value = quizItem.description3;
    document.addquiz.description4.value = quizItem.description4;
    document.addquiz.description5.value = quizItem.description5;

    document.addquiz.slideName.value = quizItem.slideName;


    objectiveMstId = quizItem.objectiveMstId;
    mode = 'edit';
    $('#deletebutton').show();
    $("html, body").animate({ scrollTop: 0 }, "fast");

}

function directionChanged(field){

    if(field.checked&&!(CKEDITOR.instances.directions.getData())){
        for(i=(currentQuestionNo-1);i>=0;i--){
            if(quizItems[i].directions){
                document.getElementById("previousDirection").innerHTML = quizItems[i].directions;
                $("#previousDirection").show();
                break;
            }
        }
    }
    else{
        document.getElementById("previousDirection").innerHTML ="";
        $("#previousDirection").hide();
    }
}


function validateYouTubeUrl(url) {
    if (url != undefined || url != '') {
        var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|\?v=)([^#\&\?]*).*/;
        var match = url.match(regExp);
        if (match && match[2].length == 11) {
            // Do anything for being valid
            // if need to change the url to embed url then use below line
            //$('#ytplayerSide').attr('src', 'https://www.youtube.com/embed/' + match[2] + '?autoplay=0');
            return true;
        } else {
            // Do anything for not being valid
            return false;
        }
    }

    return false;
}

function getIdFromYouTubeURL(url) {
    if(url.match(/(youtu.be)/)){
        var split_c = "/";
        var split_n = 3;
    }

    if(url.match(/(youtube.com)/)){
        var split_c = "v=";
        var split_n = 1;
    }

    var getYouTubeVideoID = url.split(split_c)[split_n];
    return getYouTubeVideoID.replace(/(&)+(.*)/, "");
}

