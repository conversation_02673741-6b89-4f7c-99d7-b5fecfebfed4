
var contentpopover = {
  id: 'Add-Content'
};

/* ========== */
/* Adding Content popover */
/* ========== */
addClickListener = function(el, fn) {
  if (el.addEventListener) {
    el.addEventListener('click', fn, false);
  }
  else {
    el.attachEvent('onclick', fn);
  }
},

addpopover = function() {
  var startBtnId = 'addingcontent',
      calloutId = 'startTourCallout',
      mgr = hopscotch.getCalloutManager(),
      state = hopscotch.getState();

  if (state && state.indexOf('Add-Content:') === 0) {
    
  }
  else {
    
    setTimeout(function() {
      mgr.createCallout({
        id: calloutId,
        target: startBtnId,
        placement: 'bottom',
//        title: 'Add Contents',
        content: 'Click here to add your videos, notes, quizzes for this chapter/topic',
        arrowOffset: 50,
        width: 280
      });
    }, 200);
  }

  addClickListener(document.getElementById(startBtnId), function() {
    if (!hopscotch.isActive) {
      mgr.removeAllCallouts();
    }
  });
 
};

