/**
 * Created by achyutanand on 08/01/16.
 */
var quizId,resourceDtlId,objectiveMstId;
var currentQuestionNo=0;
var quizItems=[];
var firstQuestion = true;
var quizName;

var flds = new Array (
    'resourceName',
    'question',
    'answer'
);

function questionAdded(req){
    var quizItem={};
    var returnParams = req.split('&');

    //first one is resourceDtlId
    resourceDtlId = returnParams[0].split('=')[1];

    //second one quiz id
    quizId = returnParams[1].split('=')[1];

    // third one objectivemst id
    objectiveMstId = returnParams[2].split('=')[1];
    quizItem = quizItems[currentQuestionNo];
    quizItem.objectiveMstId = objectiveMstId;
    quizItems[currentQuestionNo] = quizItem;
    currentQuestionNo = quizItems.length;
    mode = 'add';
}

function questionSaved(req){
    $('#savedNotification').show();
}

function validate(){
    var allFilled=true;
    var quizItem={};
    $('.alert').hide();
    document.addquiz.question.value=CKEDITOR.instances.question.getData();
    document.addquiz.answer.value=CKEDITOR.instances.answer.getData();
    // qType is optional, so no validation needed
    for (i=0; i<flds.length; i++) {
        //condition to check for the quiz name only with the first question
        if(flds[i] == 'resourceName' && quizItems.length > 0 ) continue;
        document.addquiz.question.value=CKEDITOR.instances.question.getData();
        if( !$("#"+flds[i]).val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#"+flds[i]).addClass('has-error');
            $("#"+flds[i]).closest('.cktext').addClass('red-border');
            allFilled = false;
        } else {
            $("#"+flds[i]).removeClass('has-error');
            $("#"+flds[i]).closest('.cktext').removeClass('red-border');
        }
    }

    if(!allFilled){
        document.getElementById("alertbox").innerHTML = "Please enter all fields.";
        $('.alert').show();
    }

    if(allFilled) {
        if(currentQuestionNo == 0) {
            document.getElementById("sidebar").innerHTML = "<br><div class='sidebar_title border-bottom '><h6><strong>Saved Questions</strong></h6></div>  <br>";
            quizName = document.addquiz.resourceName.value;
            $('.resourceName').hide();
        }

        quizItem.resourceName = quizName; // to use the same name in all items.. as the quizname input is visible only for first question
        quizItem.question = document.addquiz.question.value;
        quizItem.answer = document.addquiz.answer.value;
        quizItem.marks = document.addquiz.marks.value;
        quizItem.qType = document.addquiz.qType.value;

        //   quizItem.subtopicid = document.addquiz.subtopicid[document.addquiz.subtopicid.selectedIndex].value;
        if(mode=='edit'){
            quizItems[currentQuestionNo]=quizItem;
            document.getElementById("sidebar").innerHTML = "<br><div class='sidebar_title border-bottom '><h6><strong>Saved Questions</strong></h6></div>  <br>";
            for(i=0;i<quizItems.length;i++){
                document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='pagenumber-green' style='margin-bottom: 20px' >" + (i + 1)  + "</a>&nbsp;&nbsp;";
            }
        } else {
            document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + currentQuestionNo + ")' class='pagenumber-green' style='margin-bottom: 20px' >" + (currentQuestionNo + 1)  + "</a>&nbsp;&nbsp;";
            quizItems.push(quizItem);
        }

    }

    MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
    return allFilled;
}

function gotoQuestion(questionNo){
    var quizItem={};
    currentQuestionNo = questionNo;
    quizItem = quizItems[questionNo];

    //to clear the contents and previous states
    document.getElementById("addquiz").reset();
    $('.resourceName').hide();
    if(questionNo == 0){
        $('.resourceName').show();
        document.addquiz.resourceName.value = quizName;
    }
    document.addquiz.question.value = quizItem.question;
    CKEDITOR.instances.question.setData(quizItem.question);
    CKEDITOR.instances.answer.setData(quizItem.answer);
    //update the input field id marks if quizItem.marks is not empty
    if(quizItem.marks){
      document.addquiz.marks.value = Math.floor(quizItem.marks);
    }


    //set the question type if it exists
    if(quizItem.qType){
      document.addquiz.qType.value = quizItem.qType;
    }

    objectiveMstId = quizItem.objectiveMstId;
    mode = 'edit';
    $('#deletebutton').show();
}

