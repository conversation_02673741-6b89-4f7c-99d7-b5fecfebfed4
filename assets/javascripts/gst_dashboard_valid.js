$(document).ready(function() {
    $('#accountForm')
        .bootstrapValidator({
            excluded: [':disabled'],
            fields: {
                business_const:{
                    validators: {
                        notEmpty: {
                            message: 'Constitution of business is required'
                        }
                    }
                },
                district:{
                    validators: {
                        notEmpty: {
                            message: 'District is required'
                        }
                    }
                },
                sector:{
                    validators: {
                        notEmpty: {
                            message: 'State Jurisdiction is required'
                        }
                    }
                },
                commission:{
                    validators: {
                        notEmpty: {
                            message: 'Commissionerate code is required'
                        }
                    }
                },
                centerDivision:{
                    validators: {
                        notEmpty: {
                            message: 'Division code is required'
                        }
                    }
                },
                centerRange:{
                    validators: {
                        notEmpty: {
                            message: 'Range code is required'
                        }
                    }
                },
                date_Bstart:{
                    validators: {
                        notEmpty: {
                            message: 'Date is required'
                        },
                        date: {
                            format: 'MM/DD/YYYY',
                            message: 'The format is dd/mm/yyyy'
                        }
                    }
                },
                date_Bend:{
                    validators: {
                        notEmpty: {
                            message: 'Date is required'
                        },
                        date: {
                            format: 'MM/DD/YYYY',
                            message: 'The format is dd/mm/yyyy'
                        }
                    }
                },
                reg_type:{
                    validators: {
                        notEmpty: {
                            message: 'Reason is required'
                        }
                    }
                },
                commencedate_from:{
                    validators: {
                        notEmpty: {
                            message: 'Date is required'
                        },
                        date: {
                            format: 'MM/DD/YYYY',
                            message: 'The format is dd/mm/yyyy'
                        }
                    }
                },
                commencedate_to:{
                    validators: {
                        notEmpty: {
                            message: 'Date is required'
                        },
                        date: {
                            format: 'MM/DD/YYYY',
                            message: 'The format is dd/mm/yyyy'
                        }
                    }
                },
                exist_reg:{
                    validators: {
                        notEmpty: {
                            message: 'Type of Registration is required'
                        }
                    }
                },
                reg_no:{
                    validators: {
                        notEmpty: {
                            message: 'Registration no is required'
                        }
                    }
                },
                exist_date:{
                    validators: {
                        notEmpty: {
                            message: 'Date is required'
                        },
                        date: {
                            format: 'MM/DD/YYYY',
                            message: 'The format is dd/mm/yyyy'
                        }
                    }
                },

                firstName_p2: {
                    validators: {
                        notEmpty: {
                            message: 'First Name is required'
                        }
                    }
                },
                fathersFirstName_p2: {
                    validators: {
                        notEmpty: {
                            message: 'Father\'s First name is required'
                        }
                    }
                },
                date_p2: {
                    validators: {
                        notEmpty: {
                            message: 'Date of birth is required'
                        },
                        date: {
                            format: 'MM/DD/YYYY',
                            message: 'The format is dd/mm/yyyy'
                        }
                    }
                },
                date_p3:{
                    validators: {
                        notEmpty: {
                            message: 'Date of birth is required'
                        },
                        date: {
                            format: 'MM/DD/YYYY',
                            message: 'The format is dd/mm/yyyy'
                        }
                    }
                },
                mobileNumber_p2: {
                    validators: {
                        notEmpty: {
                            message: 'Mobile number is required'
                        },
                        integer:{
                            message:'Enter a number'
                        }

                    }
                },
                emailAddress_p2:{
                    validators:{
                        notEmpty:{
                            message: 'Email Address is required'
                        },
                        emailAddress: {
                            message: 'The value is not a valid email address'
                        }
                    }
                },
                gender_p2:{
                    validators:{
                        notEmpty:{
                            message: 'Gender is required'
                        }
                    }
                },
                designation_p2:{
                    validators:{
                        notEmpty:{
                            message: 'Designation is required'
                        }
                    }
                },
                PAN_p2:{
                    validators:{
                        notEmpty:{
                            message: 'PAN number is required'
                        }
                    }
                },
                buildingNo_p2:{
                    validators:{
                        notEmpty:{
                            message: 'Building No. / Flat No. is required'
                        }
                    }
                },
                road_p2:{
                    validators:{
                        notEmpty:{
                            message: 'Road / street name is required'
                        }
                    }
                },
                city_p2:{
                    validators:{
                        notEmpty:{
                            message: 'City / Town / Locality / Village is Required'
                        }
                    }
                },
                country_p2:{
                    validators:{
                        notEmpty:{
                            message: 'country name is required'
                        }
                    }
                },
                state_p2:{
                    validators:{
                        notEmpty:{
                            message: 'state name is required'
                        }
                    }
                },
                district_p2:{
                    validators:{
                        notEmpty:{
                            message: 'District name is required'
                        }
                    }
                },
                pinCode_p2:{
                    validators:{
                        notEmpty:{
                            message: 'PIN code is required'
                        }
                    }
                },
                firstname_p3: {
                    validators: {
                        notEmpty: {
                            message: 'First Name is required'
                        }
                    }
                },
                ffirstname_p3: {
                    validators: {
                        notEmpty: {
                            message: 'Father\'s First name is required'
                        }
                    }
                },
                date_p4: {
                    validators: {
                        notEmpty: {
                            message: 'Date of birth is required'
                        }
                    }
                },
                mobileNumber_p3: {
                    validators: {
                        notEmpty: {
                            message: 'Mobile number is required'
                        }
                    }
                },
                emailAddress_p3:{
                    validators:{
                        notEmpty:{
                            message: 'Email Address is required'
                        },
                        emailAddress: {
                            message: 'The value is not a valid email address'
                        }
                    }
                },
                gender_p3:{
                    validators:{
                        notEmpty:{
                            message: 'Gender is required'
                        }
                    }
                },
                designation_p3:{
                    validators:{
                        notEmpty:{
                            message: 'Designation is required'
                        }
                    }
                },
                PAN_p3:{
                    validators:{
                        notEmpty:{
                            message: 'PAN number is required'
                        }
                    }
                },
                buildingNo_p3:{
                    validators:{
                        notEmpty:{
                            message: 'Building No. / Flat No. is required'
                        }
                    }
                },
                road_p3:{
                    validators:{
                        notEmpty:{
                            message: 'Road / street name is required'
                        }
                    }
                },
                city_p3:{
                    validators:{
                        notEmpty:{
                            message: 'City / Town / Locality / Village is Required'
                        }
                    }
                },
                country_p3:{
                    validators:{
                        notEmpty:{
                            message: 'country name is required'
                        }
                    }
                },
                state_p3:{
                    validators:{
                        notEmpty:{
                            message: 'state name is required'
                        }
                    }
                },
                district_p3:{
                    validators:{
                        notEmpty:{
                            message: 'District name is required'
                        }
                    }
                },
                PIN_p3:{
                    validators:{
                        notEmpty:{
                            message: 'PIN code is required'
                        }
                    }
                },
                firstName_p4: {
                    validators: {
                        notEmpty: {
                            message: 'First Name is required'
                        }
                    }
                },
                mobileNumber_p4: {
                    validators: {
                        notEmpty: {
                            message: 'Mobile number is required'
                        }
                    }
                },
                emailAddress_p4:{
                    validators:{
                        notEmpty:{
                            message: 'Email Address is required'
                        }
                    }
                },
                designation_p4:{
                    validators:{
                        notEmpty:{
                            message: 'Designation is required'
                        }
                    }
                },
                PAN_p4:{
                    validators:{
                        notEmpty:{
                            message: 'PAN number is required'
                        }
                    }
                },
                enrollmentId_p4:{
                    validators:{
                        notEmpty:{
                            message: 'Enrollment ID is required'
                        }
                    }
                },

                buildingNo_p5:{
                    validators:{
                        notEmpty:{
                            message: 'Building No. / Flat No. is required'
                        }
                    }
                },
                road_p5:{
                    validators:{
                        notEmpty:{
                            message: 'Road / street name is required'
                        }
                    }
                },
                city_p5:{
                    validators:{
                        notEmpty:{
                            message: 'City / Town / Locality / Village is Required'
                        }
                    }
                },
                district_p5:{
                    validators:{
                        notEmpty:{
                            message: 'District name is required'
                        }
                    }
                },
                pin_p5:{
                    validators:{
                        notEmpty:{
                            message: 'PIN code is required'
                        }
                    }
                },
                nature_p5:{
                    validators:{
                        notEmpty:{
                            message: 'Select the nature of possession of premises'
                        }
                    }
                },
                add_places:{
                    validators:{
                        notEmpty:{
                            message: 'Enter the Business place'
                        }
                    }
                },
                chapter_code:{
                    validators:{
                        notEmpty:{
                            message: 'Hsn code is required'
                        }
                    }
                },
                no_accounts:{
                    validators:{
                        notEmpty:{
                            message: 'Atleast one Bank accounts is required'
                        },
                        integer: {
                            message: 'Please Enter a number'
                        }
                    }
                },
                acc_no:{
                    validators: {
                        notEmpty: {
                            message: 'Enter a Account number'
                        },
                        integer: {
                            message: 'Please Enter a number'
                        }
                    }
                },
                type_account:{
                    validators: {
                        notEmpty: {
                            message: 'Enter a Account number'
                        }
                    }
                },
                emp_code:{
                    validators:{
                        notEmpty:{
                            message: 'Enter Tax Employee code'
                        }
                    }
                },
                signature:{
                    validators:{
                        notEmpty:{
                            message: 'Select the name'
                        }
                    }
                },
                place_verify:{
                    validators:{
                        notEmpty:{
                            message: 'Enter the place'
                        }
                    }
                }
            }
        })
        .on('change','#togBtn1',function(){
            var date_template = $('#date-template').html();
            var tax_template = $('#tax-table').html();
            var btn = document.getElementById('togBtn1').checked;
            if (btn === true) {
                $('#taxable_person_table').append(tax_template);
                $('#datepickerhs').append(date_template);
                $('#togBtn2').attr("disabled",true);

            } else {
                $('#datepickerhs').empty();
                $('#taxable_person_table').empty();
                $('#togBtn2').attr("disabled",false);
            }
        })
        .on('change','#togBtn2',function(){
            var btn = document.getElementById('togBtn2').checked;
            var declaration_temp =$('#composition-condition').html();
            if (btn === true) {
                $('#declaration_check_box').append(declaration_temp);
                $('#togBtn1').attr("disabled",true);
            } else {
                $('#declaration_check_box').empty();
                $('#togBtn1').attr("disabled",false);
            }
        })
        .on('status.field.bv', function(e, data) {
            var $form     = $(e.target),
                validator = data.bv,
                $tabPane  = data.element.parents('.tab-pane'),
                tabId     = $tabPane.attr('id');

            if (tabId) {
                var $icon = $('a[href="#' + tabId + '"][data-toggle="tab"]').parent().find('.update');

                // Add custom class to tab containing the field
                if (data.status === validator.STATUS_INVALID) {
                    $icon.removeClass('fa-check').addClass('fa-times');
                } else if (data.status === validator.STATUS_VALID) {
                    var isValidTab = validator.isValidContainer($tabPane);
                    $icon.removeClass('fa-check fa-times')
                        .addClass(isValidTab ? 'fa-check' : 'fa-times');
                }
            }
        })
        .on('error.field.bv', function(e, data) {
            if (data.bv.getSubmitButton()) {
                data.bv.disableSubmitButtons(false);
            }
        })
        .on('success.field.bv', function(e, data) {
            if (data.bv.getSubmitButton()) {
                data.bv.disableSubmitButtons(false);
            }
        }).on('click','#bashboard_bd_back',function(e, data){
        $('.nav-tabs > .active').prev('li').find('a').trigger('click');
    });
});
