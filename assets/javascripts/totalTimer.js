//circle ends
var displayTotalTime = document.querySelector('.total-test-time');


var myintervalTimer;
var totalTimeLeft;
var totalTestTime = 3600; // manage this to set the whole time
var isStarted = false;
function changeTotalTime(seconds){
    totalTestTime=seconds;
    displaytotalTimeLeft(wholeTime);
}



function myTimer(seconds,event){ //counts time, takes seconds
    var remainTime = Date.now() + (seconds * 1000);
    displaytotalTimeLeft(seconds);
    myintervalTimer = setInterval(function(){
        totalTimeLeft = Math.round((remainTime - Date.now()) / 1000) - 1;
        if(totalTimeLeft < 0){
            clearInterval(myintervalTimer);
            isStarted = false;
            displaytotalTimeLeft(totalTestTime);
            clearForm();
            return ;
        }
        displaytotalTimeLeft(totalTimeLeft);
    }, 1000);
}
function clearForm(){
    if(!testSubmitted) {
        submitForm();
    }
}

var totTime;
function displaytotalTimeLeft (totalTimeLeft){//displays time on the input
    totTime=totalTimeLeft;

    if(totTime < secTimer){
        if((examMst.noOfSections>=1) ||(examMst.noOfSections===null) ) {
            document.querySelectorAll('.sectiontime-wrapper').forEach(function(el) { el.remove()});
            $('.normal-time').hide();
            $('.total-time-wrapper').insertAfter('.normal-time');
            $('.totalTimeLeft').css('text-align','left');
            document.querySelector('.timeLeft').textContent='Total Time Left';
        }
    }
    var minutes = Math.floor(totalTimeLeft / 60);
    var seconds = totalTimeLeft % 60;
    var hours = Math.floor(minutes / 60) ;
    minutes %= 60;
    hours %= 60;

    var displayString ="";
    if(hours<10) displayString="0"+hours+":";
    else displayString=""+hours+":";

    if(minutes<10) displayString +="0"+minutes+":";
    else displayString +=""+minutes+":";

    if(seconds<10) displayString +="0"+seconds;
    else displayString +=""+seconds;

    // var displayString='';
    displayTotalTime.textContent = displayString;
}

function startTotalTimer(){
    myTimer(totalTestTime);
}

