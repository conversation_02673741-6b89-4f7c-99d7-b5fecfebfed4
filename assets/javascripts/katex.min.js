!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.katex=t():e.katex=t()}("undefined"!=typeof self?self:this,function(){return function(r){var n={};function i(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return r[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}return i.m=r,i.c=n,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=2)}([function(e,t,r){},,function(e,t,r){"use strict";r.r(t);r(0);var p=function(){function r(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}return r.prototype.getSource=function(){return this.lexer.input.slice(this.start,this.end)},r.range=function(e,t){return t?e&&e.loc&&t.loc&&e.loc.lexer===t.loc.lexer?new r(e.loc.lexer,e.loc.start,t.loc.end):null:e&&e.loc},r}(),a=function(){function r(e,t){this.text=void 0,this.loc=void 0,this.text=e,this.loc=t}return r.prototype.range=function(e,t){return new r(t,p.range(this,e))},r}(),n=function e(t,r){this.position=void 0;var n,i="KaTeX parse error: "+t,a=r&&r.loc;if(a&&a.start<=a.end){var o=a.lexer.input;n=a.start;var s=a.end;n===o.length?i+=" at end of input: ":i+=" at position "+(n+1)+": ";var l=o.slice(n,s).replace(/[^]/g,"$&\u0332");i+=(15<n?"\u2026"+o.slice(n-15,n):o.slice(0,n))+l+(s+15<o.length?o.slice(s,s+15)+"\u2026":o.slice(s))}var h=new Error(i);return h.name="ParseError",h.__proto__=e.prototype,h.position=n,h};n.prototype.__proto__=Error.prototype;var X=n,i=/([A-Z])/g,o={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},s=/[&><"']/g;var l=function e(t){return"ordgroup"===t.type?1===t.body.length?e(t.body[0]):t:"color"===t.type?1===t.body.length?e(t.body[0]):t:"font"===t.type?e(t.body):t},T=function(e){if(!e)throw new Error("Expected non-null, but got "+String(e));return e},Y={contains:function(e,t){return-1!==e.indexOf(t)},deflt:function(e,t){return void 0===e?t:e},escape:function(e){return String(e).replace(s,function(e){return o[e]})},hyphenate:function(e){return e.replace(i,"-$1").toLowerCase()},getBaseElem:l,isCharacterBox:function(e){var t=l(e);return"mathord"===t.type||"textord"===t.type||"atom"===t.type}},h=function(){function e(e){this.displayMode=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.allowedProtocols=void 0,e=e||{},this.displayMode=Y.deflt(e.displayMode,!1),this.throwOnError=Y.deflt(e.throwOnError,!0),this.errorColor=Y.deflt(e.errorColor,"#cc0000"),this.macros=e.macros||{},this.colorIsTextColor=Y.deflt(e.colorIsTextColor,!1),this.strict=Y.deflt(e.strict,"warn"),this.maxSize=Math.max(0,Y.deflt(e.maxSize,1/0)),this.maxExpand=Math.max(0,Y.deflt(e.maxExpand,1e3)),this.allowedProtocols=Y.deflt(e.allowedProtocols,["http","https","mailto","_relative"])}var t=e.prototype;return t.reportNonstrict=function(e,t,r){var n=this.strict;if("function"==typeof n&&(n=n(e,t,r)),n&&"ignore"!==n){if(!0===n||"error"===n)throw new X("LaTeX-incompatible input and strict mode is set to 'error': "+t+" ["+e+"]",r);"warn"===n?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+t+" ["+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+n+"': "+t+" ["+e+"]")}},t.useStrictBehavior=function(e,t,r){var n=this.strict;if("function"==typeof n)try{n=n(e,t,r)}catch(e){n="error"}return!(!n||"ignore"===n)&&(!0===n||"error"===n||("warn"===n?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+t+" ["+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to unrecognized '"+n+"': "+t+" ["+e+"]"),!1))},e}(),m=function(){function e(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}var t=e.prototype;return t.sup=function(){return c[u[this.id]]},t.sub=function(){return c[d[this.id]]},t.fracNum=function(){return c[f[this.id]]},t.fracDen=function(){return c[g[this.id]]},t.cramp=function(){return c[v[this.id]]},t.text=function(){return c[y[this.id]]},t.isTight=function(){return 2<=this.size},e}(),c=[new m(0,0,!1),new m(1,0,!0),new m(2,1,!1),new m(3,1,!0),new m(4,2,!1),new m(5,2,!0),new m(6,3,!1),new m(7,3,!0)],u=[4,5,4,5,6,7,6,7],d=[5,5,5,5,7,7,7,7],f=[2,3,4,5,6,7,6,7],g=[3,3,5,5,7,7,7,7],v=[1,1,3,3,5,5,7,7],y=[0,1,2,3,2,3,2,3],q={DISPLAY:c[0],TEXT:c[2],SCRIPT:c[4],SCRIPTSCRIPT:c[6]},b=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}];var x=[];function w(e){for(var t=0;t<x.length;t+=2)if(e>=x[t]&&e<=x[t+1])return!0;return!1}b.forEach(function(e){return e.blocks.forEach(function(e){return x.push.apply(x,e)})});var k={path:{sqrtMain:"M95,702c-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,\n-10,-9.5,-14c0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54c44.2,-33.3,65.8,\n-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10s173,378,173,378c0.7,0,\n35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429c69,-144,104.5,-217.7,106.5,\n-221c5.3,-9.3,12,-14,20,-14H400000v40H845.2724s-225.272,467,-225.272,467\ns-235,486,-235,486c-2.7,4.7,-9,7,-19,7c-6,0,-10,-1,-12,-3s-194,-422,-194,-422\ns-65,47,-65,47z M834 80H400000v40H845z",sqrtSize1:"M263,681c0.7,0,18,39.7,52,119c34,79.3,68.167,\n158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120c340,-704.7,510.7,-1060.3,512,-1067\nc4.7,-7.3,11,-11,19,-11H40000v40H1012.3s-271.3,567,-271.3,567c-38.7,80.7,-84,\n175,-136,283c-52,108,-89.167,185.3,-111.5,232c-22.3,46.7,-33.8,70.3,-34.5,71\nc-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1s-109,-253,-109,-253c-72.7,-168,-109.3,\n-252,-110,-252c-10.7,8,-22,16.7,-34,26c-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26\ns76,-59,76,-59s76,-60,76,-60z M1001 80H40000v40H1012z",sqrtSize2:"M1001,80H400000v40H1013.1s-83.4,268,-264.1,840c-180.7,\n572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7s-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,\n-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744c-10,12,-21,25,-33,39s-32,39,-32,39\nc-6,-5.3,-15,-14,-27,-26s25,-30,25,-30c26.7,-32.7,52,-63,76,-91s52,-60,52,-60\ns208,722,208,722c56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,\n-658.5c53.7,-170.3,84.5,-266.8,92.5,-289.5c4,-6.7,10,-10,18,-10z\nM1001 80H400000v40H1013z",sqrtSize3:"M424,2478c-1.3,-0.7,-38.5,-172,-111.5,-514c-73,\n-342,-109.8,-513.3,-110.5,-514c0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,\n25c-5.7,9.3,-9.8,16,-12.5,20s-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,\n-13s76,-122,76,-122s77,-121,77,-121s209,968,209,968c0,-2,84.7,-361.7,254,-1079\nc169.3,-717.3,254.7,-1077.7,256,-1081c4,-6.7,10,-10,18,-10H400000v40H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M1001 80H400000v40H1014z",sqrtSize4:"M473,2793c339.3,-1799.3,509.3,-2700,510,-2702\nc3.3,-7.3,9.3,-11,18,-11H400000v40H1017.7s-90.5,478,-276.2,1466c-185.7,988,\n-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9c-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,\n-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200c0,-1.3,-5.3,8.7,-16,30c-10.7,\n21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26s76,-153,76,-153s77,-151,\n77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,606z\nM1001 80H400000v40H1017z",doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 241v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"}},A=function(){function e(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}var t=e.prototype;return t.hasClass=function(e){return Y.contains(this.classes,e)},t.toNode=function(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e},t.toMarkup=function(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e},t.toText=function(){var e=function(e){return e.toText()};return this.children.map(e).join("")},e}(),S=function(e){return e.filter(function(e){return e}).join(" ")},z=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var n=t.getColor();n&&(this.style.color=n)}},M=function(e){var t=document.createElement(e);for(var r in t.className=S(this.classes),this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var n in this.attributes)this.attributes.hasOwnProperty(n)&&t.setAttribute(n,this.attributes[n]);for(var i=0;i<this.children.length;i++)t.appendChild(this.children[i].toNode());return t},B=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+Y.escape(S(this.classes))+'"');var r="";for(var n in this.style)this.style.hasOwnProperty(n)&&(r+=Y.hyphenate(n)+":"+this.style[n]+";");for(var i in r&&(t+=' style="'+Y.escape(r)+'"'),this.attributes)this.attributes.hasOwnProperty(i)&&(t+=" "+i+'="'+Y.escape(this.attributes[i])+'"');t+=">";for(var a=0;a<this.children.length;a++)t+=this.children[a].toMarkup();return t+="</"+e+">"},C=function(){function e(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,z.call(this,e,r,n),this.children=t||[]}var t=e.prototype;return t.setAttribute=function(e,t){this.attributes[e]=t},t.hasClass=function(e){return Y.contains(this.classes,e)},t.toNode=function(){return M.call(this,"span")},t.toMarkup=function(){return B.call(this,"span")},e}(),N=function(){function e(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,z.call(this,t,n),this.children=r||[],this.setAttribute("href",e)}var t=e.prototype;return t.setAttribute=function(e,t){this.attributes[e]=t},t.hasClass=function(e){return Y.contains(this.classes,e)},t.toNode=function(){return M.call(this,"a")},t.toMarkup=function(){return B.call(this,"a")},e}(),E={"\xee":"\u0131\u0302","\xef":"\u0131\u0308","\xed":"\u0131\u0301","\xec":"\u0131\u0300"},O=function(){function e(e,t,r,n,i,a,o,s){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=n||0,this.skew=i||0,this.width=a||0,this.classes=o||[],this.style=s||{},this.maxFontSize=0;var l=function(e){for(var t=0;t<b.length;t++)for(var r=b[t],n=0;n<r.blocks.length;n++){var i=r.blocks[n];if(e>=i[0]&&e<=i[1])return r.name}return null}(this.text.charCodeAt(0));l&&this.classes.push(l+"_fallback"),/[\xee\xef\xed\xec]/.test(this.text)&&(this.text=E[this.text])}var t=e.prototype;return t.hasClass=function(e){return Y.contains(this.classes,e)},t.toNode=function(){var e=document.createTextNode(this.text),t=null;for(var r in 0<this.italic&&((t=document.createElement("span")).style.marginRight=this.italic+"em"),0<this.classes.length&&((t=t||document.createElement("span")).className=S(this.classes)),this.style)this.style.hasOwnProperty(r)&&((t=t||document.createElement("span")).style[r]=this.style[r]);return t?(t.appendChild(e),t):e},t.toMarkup=function(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=Y.escape(S(this.classes)),t+='"');var r="";for(var n in 0<this.italic&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(n)&&(r+=Y.hyphenate(n)+":"+this.style[n]+";");r&&(e=!0,t+=' style="'+Y.escape(r)+'"');var i=Y.escape(this.text);return e?(t+=">",t+=i,t+="</span>"):i},e}(),I=function(){function e(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}var t=e.prototype;return t.toNode=function(){var e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e},t.toMarkup=function(){var e="<svg";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</svg>"},e}(),R=function(){function e(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}var t=e.prototype;return t.toNode=function(){var e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",k.path[this.pathName]),e},t.toMarkup=function(){return this.alternate?"<path d='"+this.alternate+"'/>":"<path d='"+k.path[this.pathName]+"'/>"},e}(),L=function(){function e(e){this.attributes=void 0,this.attributes=e||{}}var t=e.prototype;return t.toNode=function(){var e=document.createElementNS("http://www.w3.org/2000/svg","line");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);return e},t.toMarkup=function(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+"='"+this.attributes[t]+"'");return e+="/>"},e}();var H={"AMS-Regular":{65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473]},"Fraktur-Regular":{33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],824:[.19444,.69444,0,0,0],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444]},"Main-BoldItalic":{33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],163:[0,.69444,0,0,.86853],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],163:[0,.69444,0,0,.76909],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],305:[0,.43056,0,.02778,.32246],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],567:[.19444,.43056,0,.08334,.38403],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],824:[.19444,.69444,0,0,0],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.12,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,1],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.67,0,0,.778],8800:[.215,.716,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.9,0,0,.278],8943:[-.19,.31,0,0,1.172],8945:[-.1,.82,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.744,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.744,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778]},"Math-BoldItalic":{47:[.19444,.69444,0,0,0],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333]},"Math-Italic":{47:[.19444,.69444,0,0,0],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059]},"Math-Regular":{65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059]},"SansSerif-Bold":{33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212]},"Size1-Regular":{40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},D={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2]},P={"\xc5":"A","\xc7":"C","\xd0":"D","\xde":"o","\xe5":"a","\xe7":"c","\xf0":"d","\xfe":"o","\u0410":"A","\u0411":"B","\u0412":"B","\u0413":"F","\u0414":"A","\u0415":"E","\u0416":"K","\u0417":"3","\u0418":"N","\u0419":"N","\u041a":"K","\u041b":"N","\u041c":"M","\u041d":"H","\u041e":"O","\u041f":"N","\u0420":"P","\u0421":"C","\u0422":"T","\u0423":"y","\u0424":"O","\u0425":"X","\u0426":"U","\u0427":"h","\u0428":"W","\u0429":"W","\u042a":"B","\u042b":"X","\u042c":"B","\u042d":"3","\u042e":"X","\u042f":"R","\u0430":"a","\u0431":"b","\u0432":"a","\u0433":"r","\u0434":"y","\u0435":"e","\u0436":"m","\u0437":"e","\u0438":"n","\u0439":"n","\u043a":"n","\u043b":"n","\u043c":"m","\u043d":"n","\u043e":"o","\u043f":"n","\u0440":"p","\u0441":"c","\u0442":"o","\u0443":"y","\u0444":"b","\u0445":"x","\u0446":"n","\u0447":"n","\u0448":"w","\u0449":"w","\u044a":"a","\u044b":"m","\u044c":"a","\u044d":"e","\u044e":"m","\u044f":"r"};function F(e,t,r){if(!H[t])throw new Error("Font metrics not found for font: "+t+".");var n=e.charCodeAt(0);e[0]in P&&(n=P[e[0]].charCodeAt(0));var i=H[t][n];if(i||"text"!==r||w(n)&&(i=H[t][77]),i)return{depth:i[0],height:i[1],italic:i[2],skew:i[3],width:i[4]}}var V={};var U={bin:1,close:1,inner:1,open:1,punct:1,rel:1},G={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},_={math:{},text:{}},W=_;function j(e,t,r,n,i,a){_[e][i]={font:t,group:r,replace:n},a&&n&&(_[e][n]=_[e][i])}var $="math",Z="text",K="main",J="ams",Q="accent-token",ee="bin",te="close",re="inner",ne="mathord",ie="op-token",ae="open",oe="punct",se="rel",le="spacing",he="textord";j($,K,se,"\u2261","\\equiv",!0),j($,K,se,"\u227a","\\prec",!0),j($,K,se,"\u227b","\\succ",!0),j($,K,se,"\u223c","\\sim",!0),j($,K,se,"\u22a5","\\perp"),j($,K,se,"\u2aaf","\\preceq",!0),j($,K,se,"\u2ab0","\\succeq",!0),j($,K,se,"\u2243","\\simeq",!0),j($,K,se,"\u2223","\\mid",!0),j($,K,se,"\u226a","\\ll",!0),j($,K,se,"\u226b","\\gg",!0),j($,K,se,"\u224d","\\asymp",!0),j($,K,se,"\u2225","\\parallel"),j($,K,se,"\u22c8","\\bowtie",!0),j($,K,se,"\u2323","\\smile",!0),j($,K,se,"\u2291","\\sqsubseteq",!0),j($,K,se,"\u2292","\\sqsupseteq",!0),j($,K,se,"\u2250","\\doteq",!0),j($,K,se,"\u2322","\\frown",!0),j($,K,se,"\u220b","\\ni",!0),j($,K,se,"\u221d","\\propto",!0),j($,K,se,"\u22a2","\\vdash",!0),j($,K,se,"\u22a3","\\dashv",!0),j($,K,se,"\u220b","\\owns"),j($,K,oe,".","\\ldotp"),j($,K,oe,"\u22c5","\\cdotp"),j($,K,he,"#","\\#"),j(Z,K,he,"#","\\#"),j($,K,he,"&","\\&"),j(Z,K,he,"&","\\&"),j($,K,he,"\u2135","\\aleph",!0),j($,K,he,"\u2200","\\forall",!0),j($,K,he,"\u210f","\\hbar",!0),j($,K,he,"\u2203","\\exists",!0),j($,K,he,"\u2207","\\nabla",!0),j($,K,he,"\u266d","\\flat",!0),j($,K,he,"\u2113","\\ell",!0),j($,K,he,"\u266e","\\natural",!0),j($,K,he,"\u2663","\\clubsuit",!0),j($,K,he,"\u2118","\\wp",!0),j($,K,he,"\u266f","\\sharp",!0),j($,K,he,"\u2662","\\diamondsuit",!0),j($,K,he,"\u211c","\\Re",!0),j($,K,he,"\u2661","\\heartsuit",!0),j($,K,he,"\u2111","\\Im",!0),j($,K,he,"\u2660","\\spadesuit",!0),j(Z,K,he,"\xa7","\\S",!0),j(Z,K,he,"\xb6","\\P",!0),j($,K,he,"\u2020","\\dag"),j(Z,K,he,"\u2020","\\dag"),j(Z,K,he,"\u2020","\\textdagger"),j($,K,he,"\u2021","\\ddag"),j(Z,K,he,"\u2021","\\ddag"),j(Z,K,he,"\u2021","\\textdaggerdbl"),j($,K,te,"\u23b1","\\rmoustache",!0),j($,K,ae,"\u23b0","\\lmoustache",!0),j($,K,te,"\u27ef","\\rgroup",!0),j($,K,ae,"\u27ee","\\lgroup",!0),j($,K,ee,"\u2213","\\mp",!0),j($,K,ee,"\u2296","\\ominus",!0),j($,K,ee,"\u228e","\\uplus",!0),j($,K,ee,"\u2293","\\sqcap",!0),j($,K,ee,"\u2217","\\ast"),j($,K,ee,"\u2294","\\sqcup",!0),j($,K,ee,"\u25ef","\\bigcirc"),j($,K,ee,"\u2219","\\bullet"),j($,K,ee,"\u2021","\\ddagger"),j($,K,ee,"\u2240","\\wr",!0),j($,K,ee,"\u2a3f","\\amalg"),j($,K,ee,"&","\\And"),j($,K,se,"\u27f5","\\longleftarrow",!0),j($,K,se,"\u21d0","\\Leftarrow",!0),j($,K,se,"\u27f8","\\Longleftarrow",!0),j($,K,se,"\u27f6","\\longrightarrow",!0),j($,K,se,"\u21d2","\\Rightarrow",!0),j($,K,se,"\u27f9","\\Longrightarrow",!0),j($,K,se,"\u2194","\\leftrightarrow",!0),j($,K,se,"\u27f7","\\longleftrightarrow",!0),j($,K,se,"\u21d4","\\Leftrightarrow",!0),j($,K,se,"\u27fa","\\Longleftrightarrow",!0),j($,K,se,"\u21a6","\\mapsto",!0),j($,K,se,"\u27fc","\\longmapsto",!0),j($,K,se,"\u2197","\\nearrow",!0),j($,K,se,"\u21a9","\\hookleftarrow",!0),j($,K,se,"\u21aa","\\hookrightarrow",!0),j($,K,se,"\u2198","\\searrow",!0),j($,K,se,"\u21bc","\\leftharpoonup",!0),j($,K,se,"\u21c0","\\rightharpoonup",!0),j($,K,se,"\u2199","\\swarrow",!0),j($,K,se,"\u21bd","\\leftharpoondown",!0),j($,K,se,"\u21c1","\\rightharpoondown",!0),j($,K,se,"\u2196","\\nwarrow",!0),j($,K,se,"\u21cc","\\rightleftharpoons",!0),j($,J,se,"\u226e","\\nless",!0),j($,J,se,"\ue010","\\nleqslant"),j($,J,se,"\ue011","\\nleqq"),j($,J,se,"\u2a87","\\lneq",!0),j($,J,se,"\u2268","\\lneqq",!0),j($,J,se,"\ue00c","\\lvertneqq"),j($,J,se,"\u22e6","\\lnsim",!0),j($,J,se,"\u2a89","\\lnapprox",!0),j($,J,se,"\u2280","\\nprec",!0),j($,J,se,"\u22e0","\\npreceq",!0),j($,J,se,"\u22e8","\\precnsim",!0),j($,J,se,"\u2ab9","\\precnapprox",!0),j($,J,se,"\u2241","\\nsim",!0),j($,J,se,"\ue006","\\nshortmid"),j($,J,se,"\u2224","\\nmid",!0),j($,J,se,"\u22ac","\\nvdash",!0),j($,J,se,"\u22ad","\\nvDash",!0),j($,J,se,"\u22ea","\\ntriangleleft"),j($,J,se,"\u22ec","\\ntrianglelefteq",!0),j($,J,se,"\u228a","\\subsetneq",!0),j($,J,se,"\ue01a","\\varsubsetneq"),j($,J,se,"\u2acb","\\subsetneqq",!0),j($,J,se,"\ue017","\\varsubsetneqq"),j($,J,se,"\u226f","\\ngtr",!0),j($,J,se,"\ue00f","\\ngeqslant"),j($,J,se,"\ue00e","\\ngeqq"),j($,J,se,"\u2a88","\\gneq",!0),j($,J,se,"\u2269","\\gneqq",!0),j($,J,se,"\ue00d","\\gvertneqq"),j($,J,se,"\u22e7","\\gnsim",!0),j($,J,se,"\u2a8a","\\gnapprox",!0),j($,J,se,"\u2281","\\nsucc",!0),j($,J,se,"\u22e1","\\nsucceq",!0),j($,J,se,"\u22e9","\\succnsim",!0),j($,J,se,"\u2aba","\\succnapprox",!0),j($,J,se,"\u2246","\\ncong",!0),j($,J,se,"\ue007","\\nshortparallel"),j($,J,se,"\u2226","\\nparallel",!0),j($,J,se,"\u22af","\\nVDash",!0),j($,J,se,"\u22eb","\\ntriangleright"),j($,J,se,"\u22ed","\\ntrianglerighteq",!0),j($,J,se,"\ue018","\\nsupseteqq"),j($,J,se,"\u228b","\\supsetneq",!0),j($,J,se,"\ue01b","\\varsupsetneq"),j($,J,se,"\u2acc","\\supsetneqq",!0),j($,J,se,"\ue019","\\varsupsetneqq"),j($,J,se,"\u22ae","\\nVdash",!0),j($,J,se,"\u2ab5","\\precneqq",!0),j($,J,se,"\u2ab6","\\succneqq",!0),j($,J,se,"\ue016","\\nsubseteqq"),j($,J,ee,"\u22b4","\\unlhd"),j($,J,ee,"\u22b5","\\unrhd"),j($,J,se,"\u219a","\\nleftarrow",!0),j($,J,se,"\u219b","\\nrightarrow",!0),j($,J,se,"\u21cd","\\nLeftarrow",!0),j($,J,se,"\u21cf","\\nRightarrow",!0),j($,J,se,"\u21ae","\\nleftrightarrow",!0),j($,J,se,"\u21ce","\\nLeftrightarrow",!0),j($,J,se,"\u25b3","\\vartriangle"),j($,J,he,"\u210f","\\hslash"),j($,J,he,"\u25bd","\\triangledown"),j($,J,he,"\u25ca","\\lozenge"),j($,J,he,"\u24c8","\\circledS"),j($,J,he,"\xae","\\circledR"),j(Z,J,he,"\xae","\\circledR"),j($,J,he,"\u2221","\\measuredangle",!0),j($,J,he,"\u2204","\\nexists"),j($,J,he,"\u2127","\\mho"),j($,J,he,"\u2132","\\Finv",!0),j($,J,he,"\u2141","\\Game",!0),j($,J,he,"k","\\Bbbk"),j($,J,he,"\u2035","\\backprime"),j($,J,he,"\u25b2","\\blacktriangle"),j($,J,he,"\u25bc","\\blacktriangledown"),j($,J,he,"\u25a0","\\blacksquare"),j($,J,he,"\u29eb","\\blacklozenge"),j($,J,he,"\u2605","\\bigstar"),j($,J,he,"\u2222","\\sphericalangle",!0),j($,J,he,"\u2201","\\complement",!0),j($,J,he,"\xf0","\\eth",!0),j($,J,he,"\u2571","\\diagup"),j($,J,he,"\u2572","\\diagdown"),j($,J,he,"\u25a1","\\square"),j($,J,he,"\u25a1","\\Box"),j($,J,he,"\u25ca","\\Diamond"),j($,J,he,"\xa5","\\yen",!0),j(Z,J,he,"\xa5","\\yen",!0),j($,J,he,"\u2713","\\checkmark",!0),j(Z,J,he,"\u2713","\\checkmark"),j($,J,he,"\u2136","\\beth",!0),j($,J,he,"\u2138","\\daleth",!0),j($,J,he,"\u2137","\\gimel",!0),j($,J,he,"\u03dd","\\digamma"),j($,J,he,"\u03f0","\\varkappa"),j($,J,ae,"\u250c","\\ulcorner",!0),j($,J,te,"\u2510","\\urcorner",!0),j($,J,ae,"\u2514","\\llcorner",!0),j($,J,te,"\u2518","\\lrcorner",!0),j($,J,se,"\u2266","\\leqq",!0),j($,J,se,"\u2a7d","\\leqslant",!0),j($,J,se,"\u2a95","\\eqslantless",!0),j($,J,se,"\u2272","\\lesssim",!0),j($,J,se,"\u2a85","\\lessapprox",!0),j($,J,se,"\u224a","\\approxeq",!0),j($,J,ee,"\u22d6","\\lessdot"),j($,J,se,"\u22d8","\\lll",!0),j($,J,se,"\u2276","\\lessgtr",!0),j($,J,se,"\u22da","\\lesseqgtr",!0),j($,J,se,"\u2a8b","\\lesseqqgtr",!0),j($,J,se,"\u2251","\\doteqdot"),j($,J,se,"\u2253","\\risingdotseq",!0),j($,J,se,"\u2252","\\fallingdotseq",!0),j($,J,se,"\u223d","\\backsim",!0),j($,J,se,"\u22cd","\\backsimeq",!0),j($,J,se,"\u2ac5","\\subseteqq",!0),j($,J,se,"\u22d0","\\Subset",!0),j($,J,se,"\u228f","\\sqsubset",!0),j($,J,se,"\u227c","\\preccurlyeq",!0),j($,J,se,"\u22de","\\curlyeqprec",!0),j($,J,se,"\u227e","\\precsim",!0),j($,J,se,"\u2ab7","\\precapprox",!0),j($,J,se,"\u22b2","\\vartriangleleft"),j($,J,se,"\u22b4","\\trianglelefteq"),j($,J,se,"\u22a8","\\vDash",!0),j($,J,se,"\u22aa","\\Vvdash",!0),j($,J,se,"\u2323","\\smallsmile"),j($,J,se,"\u2322","\\smallfrown"),j($,J,se,"\u224f","\\bumpeq",!0),j($,J,se,"\u224e","\\Bumpeq",!0),j($,J,se,"\u2267","\\geqq",!0),j($,J,se,"\u2a7e","\\geqslant",!0),j($,J,se,"\u2a96","\\eqslantgtr",!0),j($,J,se,"\u2273","\\gtrsim",!0),j($,J,se,"\u2a86","\\gtrapprox",!0),j($,J,ee,"\u22d7","\\gtrdot"),j($,J,se,"\u22d9","\\ggg",!0),j($,J,se,"\u2277","\\gtrless",!0),j($,J,se,"\u22db","\\gtreqless",!0),j($,J,se,"\u2a8c","\\gtreqqless",!0),j($,J,se,"\u2256","\\eqcirc",!0),j($,J,se,"\u2257","\\circeq",!0),j($,J,se,"\u225c","\\triangleq",!0),j($,J,se,"\u223c","\\thicksim"),j($,J,se,"\u2248","\\thickapprox"),j($,J,se,"\u2ac6","\\supseteqq",!0),j($,J,se,"\u22d1","\\Supset",!0),j($,J,se,"\u2290","\\sqsupset",!0),j($,J,se,"\u227d","\\succcurlyeq",!0),j($,J,se,"\u22df","\\curlyeqsucc",!0),j($,J,se,"\u227f","\\succsim",!0),j($,J,se,"\u2ab8","\\succapprox",!0),j($,J,se,"\u22b3","\\vartriangleright"),j($,J,se,"\u22b5","\\trianglerighteq"),j($,J,se,"\u22a9","\\Vdash",!0),j($,J,se,"\u2223","\\shortmid"),j($,J,se,"\u2225","\\shortparallel"),j($,J,se,"\u226c","\\between",!0),j($,J,se,"\u22d4","\\pitchfork",!0),j($,J,se,"\u221d","\\varpropto"),j($,J,se,"\u25c0","\\blacktriangleleft"),j($,J,se,"\u2234","\\therefore",!0),j($,J,se,"\u220d","\\backepsilon"),j($,J,se,"\u25b6","\\blacktriangleright"),j($,J,se,"\u2235","\\because",!0),j($,J,se,"\u22d8","\\llless"),j($,J,se,"\u22d9","\\gggtr"),j($,J,ee,"\u22b2","\\lhd"),j($,J,ee,"\u22b3","\\rhd"),j($,J,se,"\u2242","\\eqsim",!0),j($,K,se,"\u22c8","\\Join"),j($,J,se,"\u2251","\\Doteq",!0),j($,J,ee,"\u2214","\\dotplus",!0),j($,J,ee,"\u2216","\\smallsetminus"),j($,J,ee,"\u22d2","\\Cap",!0),j($,J,ee,"\u22d3","\\Cup",!0),j($,J,ee,"\u2a5e","\\doublebarwedge",!0),j($,J,ee,"\u229f","\\boxminus",!0),j($,J,ee,"\u229e","\\boxplus",!0),j($,J,ee,"\u22c7","\\divideontimes",!0),j($,J,ee,"\u22c9","\\ltimes",!0),j($,J,ee,"\u22ca","\\rtimes",!0),j($,J,ee,"\u22cb","\\leftthreetimes",!0),j($,J,ee,"\u22cc","\\rightthreetimes",!0),j($,J,ee,"\u22cf","\\curlywedge",!0),j($,J,ee,"\u22ce","\\curlyvee",!0),j($,J,ee,"\u229d","\\circleddash",!0),j($,J,ee,"\u229b","\\circledast",!0),j($,J,ee,"\u22c5","\\centerdot"),j($,J,ee,"\u22ba","\\intercal",!0),j($,J,ee,"\u22d2","\\doublecap"),j($,J,ee,"\u22d3","\\doublecup"),j($,J,ee,"\u22a0","\\boxtimes",!0),j($,J,se,"\u21e2","\\dashrightarrow",!0),j($,J,se,"\u21e0","\\dashleftarrow",!0),j($,J,se,"\u21c7","\\leftleftarrows",!0),j($,J,se,"\u21c6","\\leftrightarrows",!0),j($,J,se,"\u21da","\\Lleftarrow",!0),j($,J,se,"\u219e","\\twoheadleftarrow",!0),j($,J,se,"\u21a2","\\leftarrowtail",!0),j($,J,se,"\u21ab","\\looparrowleft",!0),j($,J,se,"\u21cb","\\leftrightharpoons",!0),j($,J,se,"\u21b6","\\curvearrowleft",!0),j($,J,se,"\u21ba","\\circlearrowleft",!0),j($,J,se,"\u21b0","\\Lsh",!0),j($,J,se,"\u21c8","\\upuparrows",!0),j($,J,se,"\u21bf","\\upharpoonleft",!0),j($,J,se,"\u21c3","\\downharpoonleft",!0),j($,J,se,"\u22b8","\\multimap",!0),j($,J,se,"\u21ad","\\leftrightsquigarrow",!0),j($,J,se,"\u21c9","\\rightrightarrows",!0),j($,J,se,"\u21c4","\\rightleftarrows",!0),j($,J,se,"\u21a0","\\twoheadrightarrow",!0),j($,J,se,"\u21a3","\\rightarrowtail",!0),j($,J,se,"\u21ac","\\looparrowright",!0),j($,J,se,"\u21b7","\\curvearrowright",!0),j($,J,se,"\u21bb","\\circlearrowright",!0),j($,J,se,"\u21b1","\\Rsh",!0),j($,J,se,"\u21ca","\\downdownarrows",!0),j($,J,se,"\u21be","\\upharpoonright",!0),j($,J,se,"\u21c2","\\downharpoonright",!0),j($,J,se,"\u21dd","\\rightsquigarrow",!0),j($,J,se,"\u21dd","\\leadsto"),j($,J,se,"\u21db","\\Rrightarrow",!0),j($,J,se,"\u21be","\\restriction"),j($,K,he,"\u2018","`"),j($,K,he,"$","\\$"),j(Z,K,he,"$","\\$"),j(Z,K,he,"$","\\textdollar"),j($,K,he,"%","\\%"),j(Z,K,he,"%","\\%"),j($,K,he,"_","\\_"),j(Z,K,he,"_","\\_"),j(Z,K,he,"_","\\textunderscore"),j($,K,he,"\u2220","\\angle",!0),j($,K,he,"\u221e","\\infty",!0),j($,K,he,"\u2032","\\prime"),j($,K,he,"\u25b3","\\triangle"),j($,K,he,"\u0393","\\Gamma",!0),j($,K,he,"\u0394","\\Delta",!0),j($,K,he,"\u0398","\\Theta",!0),j($,K,he,"\u039b","\\Lambda",!0),j($,K,he,"\u039e","\\Xi",!0),j($,K,he,"\u03a0","\\Pi",!0),j($,K,he,"\u03a3","\\Sigma",!0),j($,K,he,"\u03a5","\\Upsilon",!0),j($,K,he,"\u03a6","\\Phi",!0),j($,K,he,"\u03a8","\\Psi",!0),j($,K,he,"\u03a9","\\Omega",!0),j($,K,he,"A","\u0391"),j($,K,he,"B","\u0392"),j($,K,he,"E","\u0395"),j($,K,he,"Z","\u0396"),j($,K,he,"H","\u0397"),j($,K,he,"I","\u0399"),j($,K,he,"K","\u039a"),j($,K,he,"M","\u039c"),j($,K,he,"N","\u039d"),j($,K,he,"O","\u039f"),j($,K,he,"P","\u03a1"),j($,K,he,"T","\u03a4"),j($,K,he,"X","\u03a7"),j($,K,he,"\xac","\\neg",!0),j($,K,he,"\xac","\\lnot"),j($,K,he,"\u22a4","\\top"),j($,K,he,"\u22a5","\\bot"),j($,K,he,"\u2205","\\emptyset"),j($,J,he,"\u2205","\\varnothing"),j($,K,ne,"\u03b1","\\alpha",!0),j($,K,ne,"\u03b2","\\beta",!0),j($,K,ne,"\u03b3","\\gamma",!0),j($,K,ne,"\u03b4","\\delta",!0),j($,K,ne,"\u03f5","\\epsilon",!0),j($,K,ne,"\u03b6","\\zeta",!0),j($,K,ne,"\u03b7","\\eta",!0),j($,K,ne,"\u03b8","\\theta",!0),j($,K,ne,"\u03b9","\\iota",!0),j($,K,ne,"\u03ba","\\kappa",!0),j($,K,ne,"\u03bb","\\lambda",!0),j($,K,ne,"\u03bc","\\mu",!0),j($,K,ne,"\u03bd","\\nu",!0),j($,K,ne,"\u03be","\\xi",!0),j($,K,ne,"\u03bf","\\omicron",!0),j($,K,ne,"\u03c0","\\pi",!0),j($,K,ne,"\u03c1","\\rho",!0),j($,K,ne,"\u03c3","\\sigma",!0),j($,K,ne,"\u03c4","\\tau",!0),j($,K,ne,"\u03c5","\\upsilon",!0),j($,K,ne,"\u03d5","\\phi",!0),j($,K,ne,"\u03c7","\\chi",!0),j($,K,ne,"\u03c8","\\psi",!0),j($,K,ne,"\u03c9","\\omega",!0),j($,K,ne,"\u03b5","\\varepsilon",!0),j($,K,ne,"\u03d1","\\vartheta",!0),j($,K,ne,"\u03d6","\\varpi",!0),j($,K,ne,"\u03f1","\\varrho",!0),j($,K,ne,"\u03c2","\\varsigma",!0),j($,K,ne,"\u03c6","\\varphi",!0),j($,K,ee,"\u2217","*"),j($,K,ee,"+","+"),j($,K,ee,"\u2212","-"),j($,K,ee,"\u22c5","\\cdot",!0),j($,K,ee,"\u2218","\\circ"),j($,K,ee,"\xf7","\\div",!0),j($,K,ee,"\xb1","\\pm",!0),j($,K,ee,"\xd7","\\times",!0),j($,K,ee,"\u2229","\\cap",!0),j($,K,ee,"\u222a","\\cup",!0),j($,K,ee,"\u2216","\\setminus"),j($,K,ee,"\u2227","\\land"),j($,K,ee,"\u2228","\\lor"),j($,K,ee,"\u2227","\\wedge",!0),j($,K,ee,"\u2228","\\vee",!0),j($,K,he,"\u221a","\\surd"),j($,K,ae,"(","("),j($,K,ae,"[","["),j($,K,ae,"\u27e8","\\langle",!0),j($,K,ae,"\u2223","\\lvert"),j($,K,ae,"\u2225","\\lVert"),j($,K,te,")",")"),j($,K,te,"]","]"),j($,K,te,"?","?"),j($,K,te,"!","!"),j($,K,te,"\u27e9","\\rangle",!0),j($,K,te,"\u2223","\\rvert"),j($,K,te,"\u2225","\\rVert"),j($,K,se,"=","="),j($,K,se,"<","<"),j($,K,se,">",">"),j($,K,se,":",":"),j($,K,se,"\u2248","\\approx",!0),j($,K,se,"\u2245","\\cong",!0),j($,K,se,"\u2265","\\ge"),j($,K,se,"\u2265","\\geq",!0),j($,K,se,"\u2190","\\gets"),j($,K,se,">","\\gt"),j($,K,se,"\u2208","\\in",!0),j($,K,se,"\u0338","\\@not"),j($,K,se,"\u2282","\\subset",!0),j($,K,se,"\u2283","\\supset",!0),j($,K,se,"\u2286","\\subseteq",!0),j($,K,se,"\u2287","\\supseteq",!0),j($,J,se,"\u2288","\\nsubseteq",!0),j($,J,se,"\u2289","\\nsupseteq",!0),j($,K,se,"\u22a8","\\models"),j($,K,se,"\u2190","\\leftarrow",!0),j($,K,se,"\u2264","\\le"),j($,K,se,"\u2264","\\leq",!0),j($,K,se,"<","\\lt"),j($,K,se,"\u2192","\\rightarrow",!0),j($,K,se,"\u2192","\\to"),j($,J,se,"\u2271","\\ngeq",!0),j($,J,se,"\u2270","\\nleq",!0),j($,K,le,"\xa0","\\ "),j($,K,le,"\xa0","~"),j($,K,le,"\xa0","\\space"),j($,K,le,"\xa0","\\nobreakspace"),j(Z,K,le,"\xa0","\\ "),j(Z,K,le,"\xa0","~"),j(Z,K,le,"\xa0","\\space"),j(Z,K,le,"\xa0","\\nobreakspace"),j($,K,le,null,"\\nobreak"),j($,K,le,null,"\\allowbreak"),j($,K,oe,",",","),j($,K,oe,";",";"),j($,J,ee,"\u22bc","\\barwedge",!0),j($,J,ee,"\u22bb","\\veebar",!0),j($,K,ee,"\u2299","\\odot",!0),j($,K,ee,"\u2295","\\oplus",!0),j($,K,ee,"\u2297","\\otimes",!0),j($,K,he,"\u2202","\\partial",!0),j($,K,ee,"\u2298","\\oslash",!0),j($,J,ee,"\u229a","\\circledcirc",!0),j($,J,ee,"\u22a1","\\boxdot",!0),j($,K,ee,"\u25b3","\\bigtriangleup"),j($,K,ee,"\u25bd","\\bigtriangledown"),j($,K,ee,"\u2020","\\dagger"),j($,K,ee,"\u22c4","\\diamond"),j($,K,ee,"\u22c6","\\star"),j($,K,ee,"\u25c3","\\triangleleft"),j($,K,ee,"\u25b9","\\triangleright"),j($,K,ae,"{","\\{"),j(Z,K,he,"{","\\{"),j(Z,K,he,"{","\\textbraceleft"),j($,K,te,"}","\\}"),j(Z,K,he,"}","\\}"),j(Z,K,he,"}","\\textbraceright"),j($,K,ae,"{","\\lbrace"),j($,K,te,"}","\\rbrace"),j($,K,ae,"[","\\lbrack"),j(Z,K,he,"[","\\lbrack"),j($,K,te,"]","\\rbrack"),j(Z,K,he,"]","\\rbrack"),j($,K,ae,"(","\\lparen"),j($,K,te,")","\\rparen"),j(Z,K,he,"<","\\textless"),j(Z,K,he,">","\\textgreater"),j($,K,ae,"\u230a","\\lfloor",!0),j($,K,te,"\u230b","\\rfloor",!0),j($,K,ae,"\u2308","\\lceil",!0),j($,K,te,"\u2309","\\rceil",!0),j($,K,he,"\\","\\backslash"),j($,K,he,"\u2223","|"),j($,K,he,"\u2223","\\vert"),j(Z,K,he,"|","\\textbar"),j($,K,he,"\u2225","\\|"),j($,K,he,"\u2225","\\Vert"),j(Z,K,he,"\u2225","\\textbardbl"),j(Z,K,he,"~","\\textasciitilde"),j($,K,se,"\u2191","\\uparrow",!0),j($,K,se,"\u21d1","\\Uparrow",!0),j($,K,se,"\u2193","\\downarrow",!0),j($,K,se,"\u21d3","\\Downarrow",!0),j($,K,se,"\u2195","\\updownarrow",!0),j($,K,se,"\u21d5","\\Updownarrow",!0),j($,K,ie,"\u2210","\\coprod"),j($,K,ie,"\u22c1","\\bigvee"),j($,K,ie,"\u22c0","\\bigwedge"),j($,K,ie,"\u2a04","\\biguplus"),j($,K,ie,"\u22c2","\\bigcap"),j($,K,ie,"\u22c3","\\bigcup"),j($,K,ie,"\u222b","\\int"),j($,K,ie,"\u222b","\\intop"),j($,K,ie,"\u222c","\\iint"),j($,K,ie,"\u222d","\\iiint"),j($,K,ie,"\u220f","\\prod"),j($,K,ie,"\u2211","\\sum"),j($,K,ie,"\u2a02","\\bigotimes"),j($,K,ie,"\u2a01","\\bigoplus"),j($,K,ie,"\u2a00","\\bigodot"),j($,K,ie,"\u222e","\\oint"),j($,K,ie,"\u222f","\\oiint"),j($,K,ie,"\u2230","\\oiiint"),j($,K,ie,"\u2a06","\\bigsqcup"),j($,K,ie,"\u222b","\\smallint"),j(Z,K,re,"\u2026","\\textellipsis"),j($,K,re,"\u2026","\\mathellipsis"),j(Z,K,re,"\u2026","\\ldots",!0),j($,K,re,"\u2026","\\ldots",!0),j($,K,re,"\u22ef","\\@cdots",!0),j($,K,re,"\u22f1","\\ddots",!0),j($,K,he,"\u22ee","\\varvdots"),j($,K,Q,"\u02ca","\\acute"),j($,K,Q,"\u02cb","\\grave"),j($,K,Q,"\xa8","\\ddot"),j($,K,Q,"~","\\tilde"),j($,K,Q,"\u02c9","\\bar"),j($,K,Q,"\u02d8","\\breve"),j($,K,Q,"\u02c7","\\check"),j($,K,Q,"^","\\hat"),j($,K,Q,"\u20d7","\\vec"),j($,K,Q,"\u02d9","\\dot"),j($,K,Q,"\u02da","\\mathring"),j($,K,ne,"\u0131","\\imath",!0),j($,K,ne,"\u0237","\\jmath",!0),j(Z,K,he,"\u0131","\\i",!0),j(Z,K,he,"\u0237","\\j",!0),j(Z,K,he,"\xdf","\\ss",!0),j(Z,K,he,"\xe6","\\ae",!0),j(Z,K,he,"\xe6","\\ae",!0),j(Z,K,he,"\u0153","\\oe",!0),j(Z,K,he,"\xf8","\\o",!0),j(Z,K,he,"\xc6","\\AE",!0),j(Z,K,he,"\u0152","\\OE",!0),j(Z,K,he,"\xd8","\\O",!0),j(Z,K,Q,"\u02ca","\\'"),j(Z,K,Q,"\u02cb","\\`"),j(Z,K,Q,"\u02c6","\\^"),j(Z,K,Q,"\u02dc","\\~"),j(Z,K,Q,"\u02c9","\\="),j(Z,K,Q,"\u02d8","\\u"),j(Z,K,Q,"\u02d9","\\."),j(Z,K,Q,"\u02da","\\r"),j(Z,K,Q,"\u02c7","\\v"),j(Z,K,Q,"\xa8",'\\"'),j(Z,K,Q,"\u02dd","\\H"),j(Z,K,Q,"\u25ef","\\textcircled");var me={"--":!0,"---":!0,"``":!0,"''":!0};j(Z,K,he,"\u2013","--"),j(Z,K,he,"\u2013","\\textendash"),j(Z,K,he,"\u2014","---"),j(Z,K,he,"\u2014","\\textemdash"),j(Z,K,he,"\u2018","`"),j(Z,K,he,"\u2018","\\textquoteleft"),j(Z,K,he,"\u2019","'"),j(Z,K,he,"\u2019","\\textquoteright"),j(Z,K,he,"\u201c","``"),j(Z,K,he,"\u201c","\\textquotedblleft"),j(Z,K,he,"\u201d","''"),j(Z,K,he,"\u201d","\\textquotedblright"),j($,K,he,"\xb0","\\degree",!0),j(Z,K,he,"\xb0","\\degree"),j(Z,K,he,"\xb0","\\textdegree",!0),j($,K,ne,"\xa3","\\pounds"),j($,K,ne,"\xa3","\\mathsterling",!0),j(Z,K,ne,"\xa3","\\pounds"),j(Z,K,ne,"\xa3","\\textsterling",!0),j($,J,he,"\u2720","\\maltese"),j(Z,J,he,"\u2720","\\maltese"),j(Z,K,le,"\xa0","\\ "),j(Z,K,le,"\xa0"," "),j(Z,K,le,"\xa0","~");for(var ce='0123456789/@."',ue=0;ue<ce.length;ue++){var pe=ce.charAt(ue);j($,K,he,pe,pe)}for(var de='0123456789!@*()-=+[]<>|";:?/.,',fe=0;fe<de.length;fe++){var ge=de.charAt(fe);j(Z,K,he,ge,ge)}for(var ve="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",ye=0;ye<ve.length;ye++){var be=ve.charAt(ye);j($,K,ne,be,be),j(Z,K,he,be,be)}for(var xe="",we=0;we<ve.length;we++){var ke=ve.charAt(we);j($,K,ne,ke,xe=String.fromCharCode(55349,56320+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56372+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56424+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56580+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56736+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56788+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56840+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56944+we)),j(Z,K,he,ke,xe),we<26&&(j($,K,ne,ke,xe=String.fromCharCode(55349,56632+we)),j(Z,K,he,ke,xe),j($,K,ne,ke,xe=String.fromCharCode(55349,56476+we)),j(Z,K,he,ke,xe))}j($,K,ne,"k",xe=String.fromCharCode(55349,56668)),j(Z,K,he,"k",xe);for(var Se=0;Se<10;Se++){var ze=Se.toString();j($,K,ne,ze,xe=String.fromCharCode(55349,57294+Se)),j(Z,K,he,ze,xe),j($,K,ne,ze,xe=String.fromCharCode(55349,57314+Se)),j(Z,K,he,ze,xe),j($,K,ne,ze,xe=String.fromCharCode(55349,57324+Se)),j(Z,K,he,ze,xe),j($,K,ne,ze,xe=String.fromCharCode(55349,57334+Se)),j(Z,K,he,ze,xe)}for(var Me="\xc7\xd0\xde\xe7\xfe",Te=0;Te<Me.length;Te++){var Ae=Me.charAt(Te);j($,K,ne,Ae,Ae),j(Z,K,he,Ae,Ae)}j(Z,K,he,"\xf0","\xf0"),j(Z,K,he,"\u2013","\u2013"),j(Z,K,he,"\u2014","\u2014"),j(Z,K,he,"\u2018","\u2018"),j(Z,K,he,"\u2019","\u2019"),j(Z,K,he,"\u201c","\u201c"),j(Z,K,he,"\u201d","\u201d");var Be=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathdefault","textit","Math-Italic"],["mathdefault","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["","",""],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],Ce=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],Ne=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],qe=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],Ee=function(e,t){return t.size<2?e:Ne[e-1][t.size-1]},Oe=function(){function n(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||n.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=qe[this.size-1],this.maxSize=e.maxSize,this._fontMetrics=void 0}var e=n.prototype;return e.extend=function(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new n(t)},e.havingStyle=function(e){return this.style===e?this:this.extend({style:e,size:Ee(this.textSize,e)})},e.havingCrampedStyle=function(){return this.havingStyle(this.style.cramp())},e.havingSize=function(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:qe[e-1]})},e.havingBaseStyle=function(e){e=e||this.style.text();var t=Ee(n.BASESIZE,e);return this.size===t&&this.textSize===n.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})},e.havingBaseSizing=function(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})},e.withColor=function(e){return this.extend({color:e})},e.withPhantom=function(){return this.extend({phantom:!0})},e.withFont=function(e){return this.extend({font:e})},e.withTextFontFamily=function(e){return this.extend({fontFamily:e,font:""})},e.withTextFontWeight=function(e){return this.extend({fontWeight:e,font:""})},e.withTextFontShape=function(e){return this.extend({fontShape:e,font:""})},e.sizingClasses=function(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]},e.baseSizingClasses=function(){return this.size!==n.BASESIZE?["sizing","reset-size"+this.size,"size"+n.BASESIZE]:[]},e.fontMetrics=function(){return this._fontMetrics||(this._fontMetrics=function(e){var t;if(!V[t=5<=e?0:3<=e?1:2]){var r=V[t]={cssEmPerMu:D.quad[t]/18};for(var n in D)D.hasOwnProperty(n)&&(r[n]=D[n][t])}return V[t]}(this.size)),this._fontMetrics},e.getColor=function(){return this.phantom?"transparent":null!=this.color&&n.colorMap.hasOwnProperty(this.color)?n.colorMap[this.color]:this.color},n}();Oe.BASESIZE=6,Oe.colorMap={"katex-blue":"#6495ed","katex-orange":"#ffa500","katex-pink":"#ff00af","katex-red":"#df0030","katex-green":"#28ae7b","katex-gray":"gray","katex-purple":"#9d38bd","katex-blueA":"#ccfaff","katex-blueB":"#80f6ff","katex-blueC":"#63d9ea","katex-blueD":"#11accd","katex-blueE":"#0c7f99","katex-tealA":"#94fff5","katex-tealB":"#26edd5","katex-tealC":"#01d1c1","katex-tealD":"#01a995","katex-tealE":"#208170","katex-greenA":"#b6ffb0","katex-greenB":"#8af281","katex-greenC":"#74cf70","katex-greenD":"#1fab54","katex-greenE":"#0d923f","katex-goldA":"#ffd0a9","katex-goldB":"#ffbb71","katex-goldC":"#ff9c39","katex-goldD":"#e07d10","katex-goldE":"#a75a05","katex-redA":"#fca9a9","katex-redB":"#ff8482","katex-redC":"#f9685d","katex-redD":"#e84d39","katex-redE":"#bc2612","katex-maroonA":"#ffbde0","katex-maroonB":"#ff92c6","katex-maroonC":"#ed5fa6","katex-maroonD":"#ca337c","katex-maroonE":"#9e034e","katex-purpleA":"#ddd7ff","katex-purpleB":"#c6b9fc","katex-purpleC":"#aa87ff","katex-purpleD":"#7854ab","katex-purpleE":"#543b78","katex-mintA":"#f5f9e8","katex-mintB":"#edf2df","katex-mintC":"#e0e5cc","katex-grayA":"#f6f7f7","katex-grayB":"#f0f1f2","katex-grayC":"#e3e5e6","katex-grayD":"#d6d8da","katex-grayE":"#babec2","katex-grayF":"#888d93","katex-grayG":"#626569","katex-grayH":"#3b3e40","katex-grayI":"#21242c","katex-kaBlue":"#314453","katex-kaGreen":"#71B307"};var Ie=Oe,Re={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},Le={ex:!0,em:!0,mu:!0},He=function(e,t){var r;if(e.unit in Re)r=Re[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if("mu"===e.unit)r=t.fontMetrics().cssEmPerMu;else{var n;if(n=t.style.isTight()?t.havingStyle(t.style.text()):t,"ex"===e.unit)r=n.fontMetrics().xHeight;else{if("em"!==e.unit)throw new X("Invalid unit: '"+e.unit+"'");r=n.fontMetrics().quad}n!==t&&(r*=n.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},De=["\\imath","\u0131","\\jmath","\u0237","\\pounds","\\mathsterling","\\textsterling","\xa3"],Pe=function(e,t,r){return W[r][e]&&W[r][e].replace&&(e=W[r][e].replace),{value:e,metrics:F(e,t,r)}},Fe=function(e,t,r,n,i){var a,o=Pe(e,t,r),s=o.metrics;if(e=o.value,s){var l=s.italic;("text"===r||n&&"mathit"===n.font)&&(l=0),a=new O(e,s.height,s.depth,l,s.skew,s.width,i)}else"undefined"!=typeof console&&console.warn("No character metrics for '"+e+"' in style '"+t+"'"),a=new O(e,0,0,0,0,0,i);if(n){a.maxFontSize=n.sizeMultiplier,n.style.isTight()&&a.classes.push("mtight");var h=n.getColor();h&&(a.style.color=h)}return a},Ve=function(e,t){if(S(e.classes)!==S(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;for(var r in e.style)if(e.style.hasOwnProperty(r)&&e.style[r]!==t.style[r])return!1;for(var n in t.style)if(t.style.hasOwnProperty(n)&&e.style[n]!==t.style[n])return!1;return!0},Ue=function(e){for(var t=0,r=0,n=0,i=0;i<e.children.length;i++){var a=e.children[i];a.height>t&&(t=a.height),a.depth>r&&(r=a.depth),a.maxFontSize>n&&(n=a.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=n},Ge=function(e,t,r,n){var i=new C(e,t,r,n);return Ue(i),i},Xe=function(e,t,r,n){return new C(e,t,r,n)},Ye=function(e){var t=new A(e);return Ue(t),t},_e=function(e,t,r){var n="";switch(e){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=e}return n+"-"+("textbf"===t&&"textit"===r?"BoldItalic":"textbf"===t?"Bold":"textit"===t?"Italic":"Regular")},We={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},je={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},$e={fontMap:We,makeSymbol:Fe,mathsym:function(e,t,r,n){return void 0===n&&(n=[]),r&&r.font&&"boldsymbol"===r.font&&Pe(e,"Main-Bold",t).metrics?Fe(e,"Main-Bold",t,r,n.concat(["mathbf"])):"\\"===e||"main"===W[t][e].font?Fe(e,"Main-Regular",t,r,n):Fe(e,"AMS-Regular",t,r,n.concat(["amsrm"]))},makeSpan:Ge,makeSvgSpan:Xe,makeLineSpan:function(e,t,r){var n=Ge([e],[],t);return n.height=r||t.fontMetrics().defaultRuleThickness,n.style.borderBottomWidth=n.height+"em",n.maxFontSize=1,n},makeAnchor:function(e,t,r,n){var i=new N(e,t,r,n);return Ue(i),i},makeFragment:Ye,wrapFragment:function(e,t){return e instanceof A?Ge([],[e],t):e},makeVList:function(e,t){for(var r=function(e){if("individualShift"===e.positionType){for(var t=e.children,r=[t[0]],n=-t[0].shift-t[0].elem.depth,i=n,a=1;a<t.length;a++){var o=-t[a].shift-i-t[a].elem.depth,s=o-(t[a-1].elem.height+t[a-1].elem.depth);i+=o,r.push({type:"kern",size:s}),r.push(t[a])}return{children:r,depth:n}}var l;if("top"===e.positionType){for(var h=e.positionData,m=0;m<e.children.length;m++){var c=e.children[m];h-="kern"===c.type?c.size:c.elem.height+c.elem.depth}l=h}else if("bottom"===e.positionType)l=-e.positionData;else{var u=e.children[0];if("elem"!==u.type)throw new Error('First child must have type "elem".');if("shift"===e.positionType)l=-u.elem.depth-e.positionData;else{if("firstBaseline"!==e.positionType)throw new Error("Invalid positionType "+e.positionType+".");l=-u.elem.depth}}return{children:e.children,depth:l}}(e),n=r.children,i=r.depth,a=0,o=0;o<n.length;o++){var s=n[o];if("elem"===s.type){var l=s.elem;a=Math.max(a,l.maxFontSize,l.height)}}a+=2;var h=Ge(["pstrut"],[]);h.style.height=a+"em";for(var m=[],c=i,u=i,p=i,d=0;d<n.length;d++){var f=n[d];if("kern"===f.type)p+=f.size;else{var g=f.elem,v=f.wrapperClasses||[],y=f.wrapperStyle||{},b=Ge(v,[h,g],void 0,y);b.style.top=-a-p-g.depth+"em",f.marginLeft&&(b.style.marginLeft=f.marginLeft),f.marginRight&&(b.style.marginRight=f.marginRight),m.push(b),p+=g.height+g.depth}c=Math.min(c,p),u=Math.max(u,p)}var x,w=Ge(["vlist"],m);if(w.style.height=u+"em",c<0){var k=Ge([],[]),S=Ge(["vlist"],[k]);S.style.height=-c+"em";var z=Ge(["vlist-s"],[new O("\u200b")]);x=[Ge(["vlist-r"],[w,z]),Ge(["vlist-r"],[S])]}else x=[Ge(["vlist-r"],[w])];var M=Ge(["vlist-t"],x);return 2===x.length&&M.classes.push("vlist-t2"),M.height=u,M.depth=-c,M},makeOrd:function(e,t,r){var n,i=e.mode,a=e.text,o=["mord"],s="math"===i||"text"===i&&t.font,l=s?t.font:t.fontFamily;if(55349===a.charCodeAt(0)){var h=function(e,t){var r=1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320)+65536,n="math"===t?0:1;if(119808<=r&&r<120484){var i=Math.floor((r-119808)/26);return[Be[i][2],Be[i][n]]}if(120782<=r&&r<=120831){var a=Math.floor((r-120782)/10);return[Ce[a][2],Ce[a][n]]}if(120485===r||120486===r)return[Be[0][2],Be[0][n]];if(120486<r&&r<120782)return["",""];throw new X("Unsupported character: "+e)}(a,i),m=h[0],c=h[1];return Fe(a,m,i,t,o.concat(c))}if(l){var u,p;if("boldsymbol"===l||"mathnormal"===l){var d="boldsymbol"===l?Pe(a,"Math-BoldItalic",i).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"}:(n=a,Y.contains(De,n)?{fontName:"Main-Italic",fontClass:"mathit"}:/[0-9]/.test(n.charAt(0))?{fontName:"Caligraphic-Regular",fontClass:"mathcal"}:{fontName:"Math-Italic",fontClass:"mathdefault"});u=d.fontName,p=[d.fontClass]}else p=Y.contains(De,a)?(u="Main-Italic",["mathit"]):s?(u=We[l].fontName,[l]):(u=_e(l,t.fontWeight,t.fontShape),[l,t.fontWeight,t.fontShape]);if(Pe(a,u,i).metrics)return Fe(a,u,i,t,o.concat(p));if(me.hasOwnProperty(a)&&"Typewriter"===u.substr(0,10)){for(var f=[],g=0;g<a.length;g++)f.push(Fe(a[g],u,i,t,o.concat(p)));return Ye(f)}}if("mathord"===r){var v=/[0-9]/.test((w=a).charAt(0))||Y.contains(De,w)?{fontName:"Main-Italic",fontClass:"mathit"}:{fontName:"Math-Italic",fontClass:"mathdefault"};return Fe(a,v.fontName,i,t,o.concat([v.fontClass]))}if("textord"!==r)throw new Error("unexpected type: "+r+" in makeOrd");var y=W[i][a]&&W[i][a].font;if("ams"===y){var b=_e("amsrm",t.fontWeight,t.fontShape);return Fe(a,b,i,t,o.concat("amsrm",t.fontWeight,t.fontShape))}if("main"!==y&&y){var x=_e(y,t.fontWeight,t.fontShape);return Fe(a,x,i,t,o.concat(x,t.fontWeight,t.fontShape))}var w,k=_e("textrm",t.fontWeight,t.fontShape);return Fe(a,k,i,t,o.concat(t.fontWeight,t.fontShape))},makeGlue:function(e,t){var r=Ge(["mspace"],[],t),n=He(e,t);return r.style.marginRight=n+"em",r},staticSvg:function(e,t){var r=je[e],n=r[0],i=r[1],a=r[2],o=new R(n),s=new I([o],{width:i+"em",height:a+"em",style:"width:"+i+"em",viewBox:"0 0 "+1e3*i+" "+1e3*a,preserveAspectRatio:"xMinYMin"}),l=Xe(["overlay"],[s],t);return l.height=a,l.style.height=a+"em",l.style.width=i+"em",l},svgData:je,tryCombineChars:function(e){for(var t=0;t<e.length-1;t++){var r=e[t],n=e[t+1];r instanceof O&&n instanceof O&&Ve(r,n)&&(r.text+=n.text,r.height=Math.max(r.height,n.height),r.depth=Math.max(r.depth,n.depth),r.italic=n.italic,e.splice(t+1,1),t--)}return e}};function Ze(e,t){var r=Ke(e,t);if(!r)throw new Error("Expected node of type "+t+", but got "+(e?"node of type "+e.type:String(e)));return r}function Ke(e,t){return e&&e.type===t?e:null}function Je(e,t){var r,n,i=(n=t,(r=e)&&"atom"===r.type&&r.family===n?r:null);if(!i)throw new Error('Expected node of type "atom" and family "'+t+'", but got '+(e?"atom"===e.type?"atom of family "+e.family:"node of type "+e.type:String(e)));return i}function Qe(e){return e&&("atom"===e.type||G.hasOwnProperty(e.type))?e:null}var et={number:3,unit:"mu"},tt={number:4,unit:"mu"},rt={number:5,unit:"mu"},nt={mord:{mop:et,mbin:tt,mrel:rt,minner:et},mop:{mord:et,mop:et,mrel:rt,minner:et},mbin:{mord:tt,mop:tt,mopen:tt,minner:tt},mrel:{mord:rt,mop:rt,mopen:rt,minner:rt},mopen:{},mclose:{mop:et,mbin:tt,mrel:rt,minner:et},mpunct:{mord:et,mop:et,mrel:rt,mopen:et,mclose:et,mpunct:et,minner:et},minner:{mord:et,mop:et,mbin:tt,mrel:rt,mopen:et,mpunct:et,minner:et}},it={mord:{mop:et},mop:{mord:et,mop:et},mbin:{},mrel:{},mopen:{},mclose:{mop:et},mpunct:{},minner:{mop:et}},at={},ot={},st={};function lt(e){for(var t=e.type,r=(e.nodeType,e.names),n=e.props,i=e.handler,a=e.htmlBuilder,o=e.mathmlBuilder,s={type:t,numArgs:n.numArgs,argTypes:n.argTypes,greediness:void 0===n.greediness?1:n.greediness,allowedInText:!!n.allowedInText,allowedInMath:void 0===n.allowedInMath||n.allowedInMath,numOptionalArgs:n.numOptionalArgs||0,infix:!!n.infix,consumeMode:n.consumeMode,handler:i},l=0;l<r.length;++l)at[r[l]]=s;t&&(a&&(ot[t]=a),o&&(st[t]=o))}function ht(e){lt({type:e.type,names:[],props:{numArgs:0},handler:function(){throw new Error("Should never be called.")},htmlBuilder:e.htmlBuilder,mathmlBuilder:e.mathmlBuilder})}var mt=function(e){var t=Ke(e,"ordgroup");return t?t.body:[e]},ct=$e.makeSpan,ut={display:q.DISPLAY,text:q.TEXT,script:q.SCRIPT,scriptscript:q.SCRIPTSCRIPT},pt={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},dt=function(e,t,r,n){void 0===n&&(n=[null,null]);for(var i=[],a=0;a<e.length;a++){var o=bt(e[a],t);if(o instanceof A){var s=o.children;i.push.apply(i,s)}else i.push(o)}for(var l,h,m,c,u=[n[0]?ct([n[0]],[],t):null].concat(i.filter(function(e){return e&&"mspace"!==e.classes[0]}),[n[1]?ct([n[1]],[],t):null]),p=1;p<u.length-1;p++){var d=T(u[p]),f=ft(d,"left");"mbin"===f.classes[0]&&(m=u[p-1],c=r,m?Y.contains(["mbin","mopen","mrel","mop","mpunct"],gt(m,"right")):c)&&(f.classes[0]="mord");var g=ft(d,"right");"mbin"===g.classes[0]&&(l=u[p+1],h=r,l?Y.contains(["mrel","mclose","mpunct"],gt(l,"left")):h)&&(g.classes[0]="mord")}for(var v=[],y=0,b=0;b<i.length;b++)if(v.push(i[b]),"mspace"!==i[b].classes[0]&&y<u.length-1){0===y&&(v.pop(),b--);var x=gt(u[y],"right"),w=gt(u[y+1],"left");if(x&&w&&r){var k=T(u[y+1]),S=vt(k)?it[x][w]:nt[x][w];if(S){var z=t;if(1===e.length){var M=Ke(e[0],"sizing")||Ke(e[0],"styling");M&&("sizing"===M.type?z=t.havingSize(M.size):"styling"===M.type&&(z=t.havingStyle(ut[M.style])))}v.push($e.makeGlue(S,z))}}y++}return v},ft=function e(t,r){if(t instanceof A||t instanceof N){var n=t.children;if(n.length){if("right"===r)return e(n[n.length-1],"right");if("left"===r)return e(n[0],"right")}}return t},gt=function(e,t){return e?(e=ft(e,t),pt[e.classes[0]]||null):null},vt=function(e){return(e=ft(e,"left")).hasClass("mtight")},yt=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return ct(t.concat(r))},bt=function(e,t,r){if(!e)return ct();if(ot[e.type]){var n=ot[e.type](e,t);if(r&&t.size!==r.size){n=ct(t.sizingClasses(r),[n],t);var i=t.sizeMultiplier/r.sizeMultiplier;n.height*=i,n.depth*=i}return n}throw new X("Got group of unknown type: '"+e.type+"'")};function xt(e,t){var r=ct(["base"],e,t),n=ct(["strut"]);return n.style.height=r.height+r.depth+"em",n.style.verticalAlign=-r.depth+"em",r.children.unshift(n),r}function wt(e,t){var r=null;1===e.length&&"tag"===e[0].type&&(r=e[0].tag,e=e[0].body);for(var n,i=dt(e,t,!0),a=[],o=[],s=0;s<i.length;s++)if(o.push(i[s]),i[s].hasClass("mbin")||i[s].hasClass("mrel")||i[s].hasClass("allowbreak")){for(var l=!1;s<i.length-1&&i[s+1].hasClass("mspace");)s++,o.push(i[s]),i[s].hasClass("nobreak")&&(l=!0);l||(a.push(xt(o,t)),o=[])}else i[s].hasClass("newline")&&(o.pop(),0<o.length&&(a.push(xt(o,t)),o=[]),a.push(i[s]));0<o.length&&a.push(xt(o,t)),r&&((n=xt(dt(r,t,!0))).classes=["tag"],a.push(n));var h=ct(["katex-html"],a);if(h.setAttribute("aria-hidden","true"),n){var m=n.children[0];m.style.height=h.height+h.depth+"em",m.style.verticalAlign=-h.depth+"em"}return h}function kt(e){return new A(e)}var St=function(){function e(e,t){this.type=void 0,this.attributes=void 0,this.children=void 0,this.type=e,this.attributes={},this.children=t||[]}var t=e.prototype;return t.setAttribute=function(e,t){this.attributes[e]=t},t.getAttribute=function(e){return this.attributes[e]},t.toNode=function(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e},t.toMarkup=function(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=Y.escape(this.attributes[t]),e+='"');e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+="</"+this.type+">"},t.toText=function(){return this.children.map(function(e){return e.toText()}).join("")},e}(),zt=function(){function e(e,t){void 0===t&&(t=!0),this.text=void 0,this.needsEscape=void 0,this.text=e,this.needsEscape=t}var t=e.prototype;return t.toNode=function(){return document.createTextNode(this.toText())},t.toMarkup=function(){return this.toText()},t.toText=function(){return this.needsEscape?Y.escape(this.text):this.text},e}(),Mt={MathNode:St,TextNode:zt,SpaceNode:function(){function e(e){this.width=void 0,this.character=void 0,this.width=e,this.character=.05555<=e&&e<=.05556?"&VeryThinSpace;":.1666<=e&&e<=.1667?"&ThinSpace;":.2222<=e&&e<=.2223?"&MediumSpace;":.2777<=e&&e<=.2778?"&ThickSpace;":-.05556<=e&&e<=-.05555?"&NegativeVeryThinSpace;":-.1667<=e&&e<=-.1666?"&NegativeThinSpace;":-.2223<=e&&e<=-.2222?"&NegativeMediumSpace;":-.2778<=e&&e<=-.2777?"&NegativeThickSpace;":null}var t=e.prototype;return t.toNode=function(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",this.width+"em"),e},t.toMarkup=function(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+this.width+'em"/>'},t.toText=function(){return this.character?this.character:" "},e}(),newDocumentFragment:kt},Tt=function(e,t,r){return!W[t][e]||!W[t][e].replace||55349===e.charCodeAt(0)||me.hasOwnProperty(e)&&r&&(r.fontFamily&&"tt"===r.fontFamily.substr(4,2)||r.font&&"tt"===r.font.substr(4,2))||(e=W[t][e].replace),new Mt.TextNode(e)},At=function(e){return 1===e.length?e[0]:new Mt.MathNode("mrow",e)},Bt=function(e,t){if("texttt"===t.fontFamily)return"monospace";if("textsf"===t.fontFamily)return"textit"===t.fontShape&&"textbf"===t.fontWeight?"sans-serif-bold-italic":"textit"===t.fontShape?"sans-serif-italic":"textbf"===t.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===t.fontShape&&"textbf"===t.fontWeight)return"bold-italic";if("textit"===t.fontShape)return"italic";if("textbf"===t.fontWeight)return"bold";var r=t.font;if(!r||"mathnormal"===r)return null;var n=e.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"bold-italic";var i=e.text;return Y.contains(["\\imath","\\jmath"],i)?null:(W[n][i]&&W[n][i].replace&&(i=W[n][i].replace),F(i,$e.fontMap[r].fontName,n)?$e.fontMap[r].variant:null)},Ct=function(e,t){for(var r,n=[],i=0;i<e.length;i++){var a=qt(e[i],t);if(a instanceof St&&r instanceof St){if("mtext"===a.type&&"mtext"===r.type&&a.getAttribute("mathvariant")===r.getAttribute("mathvariant")){var o;(o=r.children).push.apply(o,a.children);continue}if("mn"===a.type&&"mn"===r.type){var s;(s=r.children).push.apply(s,a.children);continue}if("mi"===a.type&&1===a.children.length&&"mn"===r.type){var l=a.children[0];if(l instanceof zt&&"."===l.text){var h;(h=r.children).push.apply(h,a.children);continue}}}n.push(a),r=a}return n},Nt=function(e,t){return At(Ct(e,t))},qt=function(e,t){if(!e)return new Mt.MathNode("mrow");if(st[e.type])return st[e.type](e,t);throw new X("Got group of unknown type: '"+e.type+"'")};var Et=function(e){return new Ie({style:e.displayMode?q.DISPLAY:q.TEXT,maxSize:e.maxSize})},Ot=function(e,t,r){var n=Et(r),i=function(e,t,r){var n,i=Ct(e,r);n=1===i.length&&i[0]instanceof St&&Y.contains(["mrow","mtable"],i[0].type)?i[0]:new Mt.MathNode("mrow",i);var a=new Mt.MathNode("annotation",[new Mt.TextNode(t)]);a.setAttribute("encoding","application/x-tex");var o=new Mt.MathNode("semantics",[n,a]),s=new Mt.MathNode("math",[o]);return $e.makeSpan(["katex-mathml"],[s])}(e,t,n),a=wt(e,n),o=$e.makeSpan(["katex"],[i,a]);return r.displayMode?$e.makeSpan(["katex-display"],[o]):o},It={widehat:"^",widecheck:"\u02c7",widetilde:"~",utilde:"~",overleftarrow:"\u2190",underleftarrow:"\u2190",xleftarrow:"\u2190",overrightarrow:"\u2192",underrightarrow:"\u2192",xrightarrow:"\u2192",underbrace:"\u23b5",overbrace:"\u23de",overleftrightarrow:"\u2194",underleftrightarrow:"\u2194",xleftrightarrow:"\u2194",Overrightarrow:"\u21d2",xRightarrow:"\u21d2",overleftharpoon:"\u21bc",xleftharpoonup:"\u21bc",overrightharpoon:"\u21c0",xrightharpoonup:"\u21c0",xLeftarrow:"\u21d0",xLeftrightarrow:"\u21d4",xhookleftarrow:"\u21a9",xhookrightarrow:"\u21aa",xmapsto:"\u21a6",xrightharpoondown:"\u21c1",xleftharpoondown:"\u21bd",xrightleftharpoons:"\u21cc",xleftrightharpoons:"\u21cb",xtwoheadleftarrow:"\u219e",xtwoheadrightarrow:"\u21a0",xlongequal:"=",xtofrom:"\u21c4",xrightleftarrows:"\u21c4",xrightequilibrium:"\u21cc",xleftequilibrium:"\u21cb"},Rt={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},Lt=function(e,t,r,n){var i,a=e.height+e.depth+2*r;if(/fbox|color/.test(t)){if(i=$e.makeSpan(["stretchy",t],[],n),"fbox"===t){var o=n.color&&n.getColor();o&&(i.style.borderColor=o)}}else{var s=[];/^[bx]cancel$/.test(t)&&s.push(new L({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&s.push(new L({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var l=new I(s,{width:"100%",height:a+"em"});i=$e.makeSvgSpan([],[l],n)}return i.height=a,i.style.height=a+"em",i},Ht=function(e){var t=new Mt.MathNode("mo",[new Mt.TextNode(It[e.substr(1)])]);return t.setAttribute("stretchy","true"),t},Dt=function(S,z){var e=function(){var e=4e5,t=S.label.substr(1);if(Y.contains(["widehat","widecheck","widetilde","utilde"],t)){var r,n,i,a="ordgroup"===(c=S.base).type?c.body.length:1;if(5<a)n="widehat"===t||"widecheck"===t?(r=420,e=2364,i=.42,t+"4"):(r=312,e=2340,i=.34,"tilde4");else{var o=[1,1,2,2,3,3][a];n="widehat"===t||"widecheck"===t?(e=[0,1062,2364,2364,2364][o],r=[0,239,300,360,420][o],i=[0,.24,.3,.3,.36,.42][o],t+o):(e=[0,600,1033,2339,2340][o],r=[0,260,286,306,312][o],i=[0,.26,.286,.3,.306,.34][o],"tilde"+o)}var s=new R(n),l=new I([s],{width:"100%",height:i+"em",viewBox:"0 0 "+e+" "+r,preserveAspectRatio:"none"});return{span:$e.makeSvgSpan([],[l],z),minWidth:0,height:i}}var h,m,c,u=[],p=Rt[t],d=p[0],f=p[1],g=p[2],v=g/1e3,y=d.length;if(1===y)h=["hide-tail"],m=[p[3]];else if(2===y)h=["halfarrow-left","halfarrow-right"],m=["xMinYMin","xMaxYMin"];else{if(3!==y)throw new Error("Correct katexImagesData or update code here to support\n                    "+y+" children.");h=["brace-left","brace-center","brace-right"],m=["xMinYMin","xMidYMin","xMaxYMin"]}for(var b=0;b<y;b++){var x=new R(d[b]),w=new I([x],{width:"400em",height:v+"em",viewBox:"0 0 "+e+" "+g,preserveAspectRatio:m[b]+" slice"}),k=$e.makeSvgSpan([h[b]],[w],z);if(1===y)return{span:k,minWidth:f,height:v};k.style.height=v+"em",u.push(k)}return{span:$e.makeSpan(["stretchy"],u,z),minWidth:f,height:v}}(),t=e.span,r=e.minWidth,n=e.height;return t.height=n,t.style.height=n+"em",0<r&&(t.style.minWidth=r+"em"),t},Pt=function(e,t){var r,n,i,a=Ke(e,"supsub");a?(r=(n=Ze(a.base,"accent")).base,a.base=r,i=function(e){if(e instanceof C)return e;throw new Error("Expected span<HtmlDomNode> but got "+String(e)+".")}(bt(a,t)),a.base=n):r=(n=Ze(e,"accent")).base;var o=bt(r,t.havingCrampedStyle()),s=0;if(n.isShifty&&Y.isCharacterBox(r)){var l=Y.getBaseElem(r);s=function(e){if(e instanceof O)return e;throw new Error("Expected symbolNode but got "+String(e)+".")}(bt(l,t.havingCrampedStyle())).skew}var h,m=Math.min(o.height,t.fontMetrics().xHeight);if(n.isStretchy)h=Dt(n,t),h=$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"elem",elem:h,wrapperClasses:["svg-align"],wrapperStyle:0<s?{width:"calc(100% - "+2*s+"em)",marginLeft:2*s+"em"}:void 0}]},t);else{var c,u;u="\\vec"===n.label?(c=$e.staticSvg("vec",t),$e.svgData.vec[1]):((c=$e.makeSymbol(n.label,"Main-Regular",n.mode,t)).italic=0,c.width),h=$e.makeSpan(["accent-body"],[c]);var p="\\textcircled"===n.label;p&&(h.classes.push("accent-full"),m=o.height);var d=s;p||(d-=u/2),h.style.left=d+"em","\\textcircled"===n.label&&(h.style.top=".2em"),h=$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"kern",size:-m},{type:"elem",elem:h}]},t)}var f=$e.makeSpan(["mord","accent"],[h],t);return i?(i.children[0]=f,i.height=Math.max(f.height,i.height),i.classes[0]="mord",i):f},Ft=function(e,t){var r=e.isStretchy?Ht(e.label):new Mt.MathNode("mo",[Tt(e.label,e.mode)]),n=new Mt.MathNode("mover",[qt(e.base,t),r]);return n.setAttribute("accent","true"),n},Vt=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(function(e){return"\\"+e}).join("|"));lt({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:function(e,t){var r=t[0],n=!Vt.test(e.funcName),i=!n||"\\widehat"===e.funcName||"\\widetilde"===e.funcName||"\\widecheck"===e.funcName;return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:n,isShifty:i,base:r}},htmlBuilder:Pt,mathmlBuilder:Ft}),lt({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!1},handler:function(e,t){var r=t[0];return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:Pt,mathmlBuilder:Ft}),lt({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName,i=t[0];return{type:"accentUnder",mode:r.mode,label:n,base:i}},htmlBuilder:function(e,t){var r=bt(e.base,t),n=Dt(e,t),i="\\utilde"===e.label?.12:0,a=$e.makeVList({positionType:"bottom",positionData:n.height+i,children:[{type:"elem",elem:n,wrapperClasses:["svg-align"]},{type:"kern",size:i},{type:"elem",elem:r}]},t);return $e.makeSpan(["mord","accentunder"],[a],t)},mathmlBuilder:function(e,t){var r=Ht(e.label),n=new Mt.MathNode("munder",[qt(e.base,t),r]);return n.setAttribute("accentunder","true"),n}}),lt({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium"],props:{numArgs:1,numOptionalArgs:1},handler:function(e,t,r){var n=e.parser,i=e.funcName;return{type:"xArrow",mode:n.mode,label:i,body:t[0],below:r[0]}},htmlBuilder:function(e,t){var r,n=t.style,i=t.havingStyle(n.sup()),a=$e.wrapFragment(bt(e.body,i,t),t);a.classes.push("x-arrow-pad"),e.below&&(i=t.havingStyle(n.sub()),(r=$e.wrapFragment(bt(e.below,i,t),t)).classes.push("x-arrow-pad"));var o,s=Dt(e,t),l=-t.fontMetrics().axisHeight+.5*s.height,h=-t.fontMetrics().axisHeight-.5*s.height-.111;if((.25<a.depth||"\\xleftequilibrium"===e.label)&&(h-=a.depth),r){var m=-t.fontMetrics().axisHeight+r.height+.5*s.height+.111;o=$e.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:h},{type:"elem",elem:s,shift:l},{type:"elem",elem:r,shift:m}]},t)}else o=$e.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:h},{type:"elem",elem:s,shift:l}]},t);return o.children[0].children[0].children[1].classes.push("svg-align"),$e.makeSpan(["mrel","x-arrow"],[o],t)},mathmlBuilder:function(e,t){var r,n,i=Ht(e.label);if(e.body){var a=qt(e.body,t);r=e.below?(n=qt(e.below,t),new Mt.MathNode("munderover",[i,n,a])):new Mt.MathNode("mover",[i,a])}else r=e.below?(n=qt(e.below,t),new Mt.MathNode("munder",[i,n])):new Mt.MathNode("mover",[i]);return r}}),lt({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){for(var r=e.parser,n=Ze(t[0],"ordgroup").body,i="",a=0;a<n.length;a++){i+=Ze(n[a],"textord").text}var o=parseInt(i);if(isNaN(o))throw new X("\\@char has non-numeric argument "+i);return{type:"textord",mode:r.mode,text:String.fromCharCode(o)}}});var Ut=function(e,t){var r=dt(e.body,t.withColor(e.color),!1);return $e.makeFragment(r)},Gt=function(e,t){var r=Ct(e.body,t),n=new Mt.MathNode("mstyle",r);return n.setAttribute("mathcolor",e.color),n};lt({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,greediness:3,argTypes:["color","original"]},handler:function(e,t){var r=e.parser,n=Ze(t[0],"color-token").color,i=t[1];return{type:"color",mode:r.mode,color:n,body:mt(i)}},htmlBuilder:Ut,mathmlBuilder:Gt}),lt({type:"color",names:["\\blue","\\orange","\\pink","\\red","\\green","\\gray","\\purple","\\blueA","\\blueB","\\blueC","\\blueD","\\blueE","\\tealA","\\tealB","\\tealC","\\tealD","\\tealE","\\greenA","\\greenB","\\greenC","\\greenD","\\greenE","\\goldA","\\goldB","\\goldC","\\goldD","\\goldE","\\redA","\\redB","\\redC","\\redD","\\redE","\\maroonA","\\maroonB","\\maroonC","\\maroonD","\\maroonE","\\purpleA","\\purpleB","\\purpleC","\\purpleD","\\purpleE","\\mintA","\\mintB","\\mintC","\\grayA","\\grayB","\\grayC","\\grayD","\\grayE","\\grayF","\\grayG","\\grayH","\\grayI","\\kaBlue","\\kaGreen"],props:{numArgs:1,allowedInText:!0,greediness:3},handler:function(e,t){var r=e.parser,n=e.funcName,i=t[0];return{type:"color",mode:r.mode,color:"katex-"+n.slice(1),body:mt(i)}},htmlBuilder:Ut,mathmlBuilder:Gt}),lt({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,greediness:3,argTypes:["color"]},handler:function(e,t){var r=e.parser,n=e.breakOnTokenText,i=Ze(t[0],"color-token").color,a=r.parseExpression(!0,n);return{type:"color",mode:r.mode,color:i,body:a}},htmlBuilder:Ut,mathmlBuilder:Gt}),lt({type:"cr",names:["\\cr","\\newline"],props:{numArgs:0,numOptionalArgs:1,argTypes:["size"],allowedInText:!0},handler:function(e,t,r){var n=e.parser,i=e.funcName,a=r[0],o="\\cr"===i,s=!1;return o||(s=!n.settings.displayMode||!n.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode")),{type:"cr",mode:n.mode,newLine:s,newRow:o,size:a&&Ze(a,"size").value}},htmlBuilder:function(e,t){if(e.newRow)throw new X("\\cr valid only within a tabular/array environment");var r=$e.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=He(e.size,t)+"em")),r},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",He(e.size,t)+"em")),r}});var Xt=function(e,t,r){var n=F(W.math[e]&&W.math[e].replace||e,t,r);if(!n)throw new Error("Unsupported symbol "+e+" and font size "+t+".");return n},Yt=function(e,t,r,n){var i=r.havingBaseStyle(t),a=$e.makeSpan(n.concat(i.sizingClasses(r)),[e],r),o=i.sizeMultiplier/r.sizeMultiplier;return a.height*=o,a.depth*=o,a.maxFontSize=i.sizeMultiplier,a},_t=function(e,t,r){var n=t.havingBaseStyle(r),i=(1-t.sizeMultiplier/n.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=i+"em",e.height-=i,e.depth+=i},Wt=function(e,t,r,n,i,a){var o,s,l,h,m=(o=e,s=t,l=i,h=n,$e.makeSymbol(o,"Size"+s+"-Regular",l,h)),c=Yt($e.makeSpan(["delimsizing","size"+t],[m],n),q.TEXT,n,a);return r&&_t(c,n,q.TEXT),c},jt=function(e,t,r){var n;return n="Size1-Regular"===t?"delim-size1":"delim-size4",{type:"elem",elem:$e.makeSpan(["delimsizinginner",n],[$e.makeSpan([],[$e.makeSymbol(e,t,r)])])}},$t=function(e,t,r,n,i,a){var o,s,l,h;o=l=h=e,s=null;var m="Size1-Regular";"\\uparrow"===e?l=h="\u23d0":"\\Uparrow"===e?l=h="\u2016":"\\downarrow"===e?o=l="\u23d0":"\\Downarrow"===e?o=l="\u2016":"\\updownarrow"===e?(o="\\uparrow",l="\u23d0",h="\\downarrow"):"\\Updownarrow"===e?(o="\\Uparrow",l="\u2016",h="\\Downarrow"):"["===e||"\\lbrack"===e?(o="\u23a1",l="\u23a2",h="\u23a3",m="Size4-Regular"):"]"===e||"\\rbrack"===e?(o="\u23a4",l="\u23a5",h="\u23a6",m="Size4-Regular"):"\\lfloor"===e||"\u230a"===e?(l=o="\u23a2",h="\u23a3",m="Size4-Regular"):"\\lceil"===e||"\u2308"===e?(o="\u23a1",l=h="\u23a2",m="Size4-Regular"):"\\rfloor"===e||"\u230b"===e?(l=o="\u23a5",h="\u23a6",m="Size4-Regular"):"\\rceil"===e||"\u2309"===e?(o="\u23a4",l=h="\u23a5",m="Size4-Regular"):"("===e||"\\lparen"===e?(o="\u239b",l="\u239c",h="\u239d",m="Size4-Regular"):")"===e||"\\rparen"===e?(o="\u239e",l="\u239f",h="\u23a0",m="Size4-Regular"):"\\{"===e||"\\lbrace"===e?(o="\u23a7",s="\u23a8",h="\u23a9",l="\u23aa",m="Size4-Regular"):"\\}"===e||"\\rbrace"===e?(o="\u23ab",s="\u23ac",h="\u23ad",l="\u23aa",m="Size4-Regular"):"\\lgroup"===e||"\u27ee"===e?(o="\u23a7",h="\u23a9",l="\u23aa",m="Size4-Regular"):"\\rgroup"===e||"\u27ef"===e?(o="\u23ab",h="\u23ad",l="\u23aa",m="Size4-Regular"):"\\lmoustache"===e||"\u23b0"===e?(o="\u23a7",h="\u23ad",l="\u23aa",m="Size4-Regular"):"\\rmoustache"!==e&&"\u23b1"!==e||(o="\u23ab",h="\u23a9",l="\u23aa",m="Size4-Regular");var c=Xt(o,m,i),u=c.height+c.depth,p=Xt(l,m,i),d=p.height+p.depth,f=Xt(h,m,i),g=f.height+f.depth,v=0,y=1;if(null!==s){var b=Xt(s,m,i);v=b.height+b.depth,y=2}var x=u+g+v,w=Math.ceil((t-x)/(y*d)),k=x+w*y*d,S=n.fontMetrics().axisHeight;r&&(S*=n.sizeMultiplier);var z=k/2-S,M=[];if(M.push(jt(h,m,i)),null===s)for(var T=0;T<w;T++)M.push(jt(l,m,i));else{for(var A=0;A<w;A++)M.push(jt(l,m,i));M.push(jt(s,m,i));for(var B=0;B<w;B++)M.push(jt(l,m,i))}M.push(jt(o,m,i));var C=n.havingBaseStyle(q.TEXT),N=$e.makeVList({positionType:"bottom",positionData:z,children:M},C);return Yt($e.makeSpan(["delimsizing","mult"],[N],C),q.TEXT,n,a)},Zt=function(e,t,r,n){var i;"sqrtTall"===e&&(i="M702 80H400000v40H742v"+(r-54-80)+"l-4 4-4 4c-.667.7\n-2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1h-12l-28-84c-16.667-52-96.667\n-294.333-240-727l-212 -643 -85 170c-4-3.333-8.333-7.667-13 -13l-13-13l77-155\n 77-156c66 199.333 139 419.667 219 661 l218 661zM702 80H400000v40H742z");var a=new R(e,i),o=new I([a],{width:"400em",height:t+"em",viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return $e.makeSvgSpan(["hide-tail"],[o],n)},Kt=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230a","\u230b","\\lceil","\\rceil","\u2308","\u2309","\\surd"],Jt=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27ee","\u27ef","\\lmoustache","\\rmoustache","\u23b0","\u23b1"],Qt=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],er=[0,1.2,1.8,2.4,3],tr=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],rr=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"stack"}],nr=[{type:"small",style:q.SCRIPTSCRIPT},{type:"small",style:q.SCRIPT},{type:"small",style:q.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],ir=function(e){if("small"===e.type)return"Main-Regular";if("large"===e.type)return"Size"+e.size+"-Regular";if("stack"===e.type)return"Size4-Regular";throw new Error("Add support for delim type '"+e.type+"' here.")},ar=function(e,t,r,n){for(var i=Math.min(2,3-n.style.size);i<r.length&&"stack"!==r[i].type;i++){var a=Xt(e,ir(r[i]),"math"),o=a.height+a.depth;if("small"===r[i].type&&(o*=n.havingBaseStyle(r[i].style).sizeMultiplier),t<o)return r[i]}return r[r.length-1]},or=function(e,t,r,n,i,a){var o;"<"===e||"\\lt"===e||"\u27e8"===e?e="\\langle":">"!==e&&"\\gt"!==e&&"\u27e9"!==e||(e="\\rangle"),o=Y.contains(Qt,e)?tr:Y.contains(Kt,e)?nr:rr;var s,l,h,m,c,u,p,d,f=ar(e,t,o,n);return"small"===f.type?(s=e,l=f.style,h=r,m=n,c=i,u=a,p=$e.makeSymbol(s,"Main-Regular",c,m),d=Yt(p,l,m,u),h&&_t(d,m,l),d):"large"===f.type?Wt(e,f.size,r,n,i,a):$t(e,t,r,n,i,a)},sr=function(e,t){var r,n,i=t.havingBaseSizing(),a=ar("\\surd",e*i.sizeMultiplier,nr,i),o=i.sizeMultiplier,s=0,l=0,h=0;return n="small"===a.type?(e<1?o=1:e<1.4&&(o=.7),l=1/o,(r=Zt("sqrtMain",s=1.08/o,h=1080,t)).style.minWidth="0.853em",.833/o):"large"===a.type?(h=1080*er[a.size],l=er[a.size]/o,s=(er[a.size]+.08)/o,(r=Zt("sqrtSize"+a.size,s,h,t)).style.minWidth="1.02em",1/o):(s=e+.08,l=e,h=Math.floor(1e3*e)+80,(r=Zt("sqrtTall",s,h,t)).style.minWidth="0.742em",1.056),r.height=l,r.style.height=s+"em",{span:r,advanceWidth:n,ruleWidth:t.fontMetrics().sqrtRuleThickness*o}},lr=function(e,t,r,n,i){if("<"===e||"\\lt"===e||"\u27e8"===e?e="\\langle":">"!==e&&"\\gt"!==e&&"\u27e9"!==e||(e="\\rangle"),Y.contains(Kt,e)||Y.contains(Qt,e))return Wt(e,t,!1,r,n,i);if(Y.contains(Jt,e))return $t(e,er[t],!1,r,n,i);throw new X("Illegal delimiter: '"+e+"'")},hr=or,mr=function(e,t,r,n,i,a){var o=n.fontMetrics().axisHeight*n.sizeMultiplier,s=5/n.fontMetrics().ptPerEm,l=Math.max(t-o,r+o),h=Math.max(l/500*901,2*l-s);return or(e,h,!0,n,i,a)},cr={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},ur=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","\u230a","\u230b","\\lceil","\\rceil","\u2308","\u2309","<",">","\\langle","\u27e8","\\rangle","\u27e9","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","\u27ee","\u27ef","\\lmoustache","\\rmoustache","\u23b0","\u23b1","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function pr(e,t){var r=Qe(e);if(r&&Y.contains(ur,r.text))return r;throw new X("Invalid delimiter: '"+(r?r.text:JSON.stringify(e))+"' after '"+t.funcName+"'",e)}function dr(e){if(!e.body)throw new Error("Bug: The leftright ParseNode wasn't fully parsed.")}lt({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1},handler:function(e,t){var r=pr(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:cr[e.funcName].size,mclass:cr[e.funcName].mclass,delim:r.text}},htmlBuilder:function(e,t){return"."===e.delim?$e.makeSpan([e.mclass]):lr(e.delim,e.size,t,e.mode,[e.mclass])},mathmlBuilder:function(e){var t=[];"."!==e.delim&&t.push(Tt(e.delim,e.mode));var r=new Mt.MathNode("mo",t);return"mopen"===e.mclass||"mclose"===e.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r}}),lt({type:"leftright-right",names:["\\right"],props:{numArgs:1},handler:function(e,t){return{type:"leftright-right",mode:e.parser.mode,delim:pr(t[0],e).text}}}),lt({type:"leftright",names:["\\left"],props:{numArgs:1},handler:function(e,t){var r=pr(t[0],e),n=e.parser;++n.leftrightDepth;var i=n.parseExpression(!1);--n.leftrightDepth,n.expect("\\right",!1);var a=Ze(n.parseFunction(),"leftright-right");return{type:"leftright",mode:n.mode,body:i,left:r.text,right:a.delim}},htmlBuilder:function(e,t){dr(e);for(var r,n,i=dt(e.body,t,!0,[null,"mclose"]),a=0,o=0,s=!1,l=0;l<i.length;l++)i[l].isMiddle?s=!0:(a=Math.max(i[l].height,a),o=Math.max(i[l].depth,o));if(a*=t.sizeMultiplier,o*=t.sizeMultiplier,r="."===e.left?yt(t,["mopen"]):mr(e.left,a,o,t,e.mode,["mopen"]),i.unshift(r),s)for(var h=1;h<i.length;h++){var m=i[h].isMiddle;m&&(i[h]=mr(m.delim,a,o,m.options,e.mode,[]))}return n="."===e.right?yt(t,["mclose"]):mr(e.right,a,o,t,e.mode,["mclose"]),i.push(n),$e.makeSpan(["minner"],i,t)},mathmlBuilder:function(e,t){dr(e);var r=Ct(e.body,t);if("."!==e.left){var n=new Mt.MathNode("mo",[Tt(e.left,e.mode)]);n.setAttribute("fence","true"),r.unshift(n)}if("."!==e.right){var i=new Mt.MathNode("mo",[Tt(e.right,e.mode)]);i.setAttribute("fence","true"),r.push(i)}return At(r)}}),lt({type:"middle",names:["\\middle"],props:{numArgs:1},handler:function(e,t){var r=pr(t[0],e);if(!e.parser.leftrightDepth)throw new X("\\middle without preceding \\left",r);return{type:"middle",mode:e.parser.mode,delim:r.text}},htmlBuilder:function(e,t){var r;if("."===e.delim)r=yt(t,[]);else{r=lr(e.delim,1,t,e.mode,[]);var n={delim:e.delim,options:t};r.isMiddle=n}return r},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mo",[Tt(e.delim,e.mode)]);return r.setAttribute("fence","true"),r}});var fr=function(e,t){var r,n,i=$e.wrapFragment(bt(e.body,t),t),a=e.label.substr(1),o=t.sizeMultiplier,s=0,l=Y.isCharacterBox(e.body);if("sout"===a)(r=$e.makeSpan(["stretchy","sout"])).height=t.fontMetrics().defaultRuleThickness/o,s=-.5*t.fontMetrics().xHeight;else{/cancel/.test(a)?l||i.classes.push("cancel-pad"):i.classes.push("boxpad");var h=0;h=/box/.test(a)?"colorbox"===a?.3:.34:l?.2:0,r=Lt(i,a,h,t),s=i.depth+h,e.backgroundColor&&(r.style.backgroundColor=e.backgroundColor,e.borderColor&&(r.style.borderColor=e.borderColor))}return n=e.backgroundColor?$e.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:s},{type:"elem",elem:i,shift:0}]},t):$e.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:0},{type:"elem",elem:r,shift:s,wrapperClasses:/cancel/.test(a)?["svg-align"]:[]}]},t),/cancel/.test(a)&&(n.height=i.height,n.depth=i.depth),/cancel/.test(a)&&!l?$e.makeSpan(["mord","cancel-lap"],[n],t):$e.makeSpan(["mord"],[n],t)},gr=function(e,t){var r=new Mt.MathNode("menclose",[qt(e.body,t)]);switch(e.label){case"\\cancel":r.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":r.setAttribute("notation","downdiagonalstrike");break;case"\\sout":r.setAttribute("notation","horizontalstrike");break;case"\\fbox":case"\\fcolorbox":r.setAttribute("notation","box");break;case"\\xcancel":r.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return e.backgroundColor&&r.setAttribute("mathbackground",e.backgroundColor),r};lt({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,greediness:3,argTypes:["color","text"]},handler:function(e,t,r){var n=e.parser,i=e.funcName,a=Ze(t[0],"color-token").color,o=t[1];return{type:"enclose",mode:n.mode,label:i,backgroundColor:a,body:o}},htmlBuilder:fr,mathmlBuilder:gr}),lt({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,greediness:3,argTypes:["color","color","text"]},handler:function(e,t,r){var n=e.parser,i=e.funcName,a=Ze(t[0],"color-token").color,o=Ze(t[1],"color-token").color,s=t[2];return{type:"enclose",mode:n.mode,label:i,backgroundColor:o,borderColor:a,body:s}},htmlBuilder:fr,mathmlBuilder:gr}),lt({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0},handler:function(e,t){return{type:"enclose",mode:e.parser.mode,label:"\\fbox",body:t[0]}}}),lt({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout"],props:{numArgs:1},handler:function(e,t,r){var n=e.parser,i=e.funcName,a=t[0];return{type:"enclose",mode:n.mode,label:i,body:a}},htmlBuilder:fr,mathmlBuilder:gr});var vr={};function yr(e){for(var t=e.type,r=e.names,n=e.props,i=e.handler,a=e.htmlBuilder,o=e.mathmlBuilder,s={type:t,numArgs:n.numArgs||0,greediness:1,allowedInText:!1,numOptionalArgs:0,handler:i},l=0;l<r.length;++l)vr[r[l]]=s;a&&(ot[t]=a),o&&(st[t]=o)}function br(e){var t=[];e.consumeSpaces();for(var r=e.nextToken.text;"\\hline"===r||"\\hdashline"===r;)e.consume(),t.push("\\hdashline"===r),e.consumeSpaces(),r=e.nextToken.text;return t}function xr(e,t,r){var n=t.hskipBeforeAndAfter,i=t.addJot,a=t.cols,o=t.arraystretch;if(e.gullet.beginGroup(),e.gullet.macros.set("\\\\","\\cr"),!o){var s=e.gullet.expandMacroAsText("\\arraystretch");if(null==s)o=1;else if(!(o=parseFloat(s))||o<0)throw new X("Invalid \\arraystretch: "+s)}var l=[],h=[l],m=[],c=[];for(c.push(br(e));;){var u=e.parseExpression(!1,"\\cr");u={type:"ordgroup",mode:e.mode,body:u},r&&(u={type:"styling",mode:e.mode,style:r,body:[u]}),l.push(u);var p=e.nextToken.text;if("&"===p)e.consume();else{if("\\end"===p){1===l.length&&"styling"===u.type&&0===u.body[0].body.length&&h.pop(),c.length<h.length+1&&c.push([]);break}if("\\cr"!==p)throw new X("Expected & or \\\\ or \\cr or \\end",e.nextToken);var d=Ze(e.parseFunction(),"cr");m.push(d.size),c.push(br(e)),l=[],h.push(l)}}return e.gullet.endGroup(),{type:"array",mode:e.mode,addJot:i,arraystretch:o,body:h,cols:a,rowGaps:m,hskipBeforeAndAfter:n,hLinesBeforeRow:c}}function wr(e){return"d"===e.substr(0,1)?"display":"text"}var kr=function(e,t){var r,n,i=e.body.length,a=e.hLinesBeforeRow,o=0,s=new Array(i),l=[],h=1/t.fontMetrics().ptPerEm,m=5*h,c=12*h,u=3*h,p=e.arraystretch*c,d=.7*p,f=.3*p,g=0;function v(e){for(var t=0;t<e.length;++t)0<t&&(g+=.25),l.push({pos:g,isDashed:e[t]})}for(v(a[0]),r=0;r<e.body.length;++r){var y=e.body[r],b=d,x=f;o<y.length&&(o=y.length);var w=new Array(y.length);for(n=0;n<y.length;++n){var k=bt(y[n],t);x<k.depth&&(x=k.depth),b<k.height&&(b=k.height),w[n]=k}var S=e.rowGaps[r],z=0;S&&0<(z=He(S,t))&&(x<(z+=f)&&(x=z),z=0),e.addJot&&(x+=u),w.height=b,w.depth=x,g+=b,w.pos=g,g+=x+z,s[r]=w,v(a[r+1])}var M,T,A=g/2+t.fontMetrics().axisHeight,B=e.cols||[],C=[];for(T=n=0;n<o||T<B.length;++n,++T){for(var N=B[T]||{},q=!0;"separator"===N.type;){if(q||((M=$e.makeSpan(["arraycolsep"],[])).style.width=t.fontMetrics().doubleRuleSep+"em",C.push(M)),"|"===N.separator){var E=$e.makeSpan(["vertical-separator"],[],t);E.style.height=g+"em",E.style.verticalAlign=-(g-A)+"em",C.push(E)}else{if(":"!==N.separator)throw new X("Invalid separator type: "+N.separator);var O=$e.makeSpan(["vertical-separator","vs-dashed"],[],t);O.style.height=g+"em",O.style.verticalAlign=-(g-A)+"em",C.push(O)}N=B[++T]||{},q=!1}if(!(o<=n)){var I=void 0;(0<n||e.hskipBeforeAndAfter)&&0!==(I=Y.deflt(N.pregap,m))&&((M=$e.makeSpan(["arraycolsep"],[])).style.width=I+"em",C.push(M));var R=[];for(r=0;r<i;++r){var L=s[r],H=L[n];if(H){var D=L.pos-A;H.depth=L.depth,H.height=L.height,R.push({type:"elem",elem:H,shift:D})}}R=$e.makeVList({positionType:"individualShift",children:R},t),R=$e.makeSpan(["col-align-"+(N.align||"c")],[R]),C.push(R),(n<o-1||e.hskipBeforeAndAfter)&&0!==(I=Y.deflt(N.postgap,m))&&((M=$e.makeSpan(["arraycolsep"],[])).style.width=I+"em",C.push(M))}}if(s=$e.makeSpan(["mtable"],C),0<l.length){for(var P=$e.makeLineSpan("hline",t,.05),F=$e.makeLineSpan("hdashline",t,.05),V=[{type:"elem",elem:s,shift:0}];0<l.length;){var U=l.pop(),G=U.pos-A;U.isDashed?V.push({type:"elem",elem:F,shift:G}):V.push({type:"elem",elem:P,shift:G})}s=$e.makeVList({positionType:"individualShift",children:V},t)}return $e.makeSpan(["mord"],[s],t)},Sr=function(e,t){return new Mt.MathNode("mtable",e.body.map(function(e){return new Mt.MathNode("mtr",e.map(function(e){return new Mt.MathNode("mtd",[qt(e,t)])}))}))},zr=function(e,t){var n,r=[],i=xr(e.parser,{cols:r,addJot:!0},"display"),a=0,o={type:"ordgroup",mode:e.mode,body:[]},s=Ke(t[0],"ordgroup");if(s){for(var l="",h=0;h<s.body.length;h++){l+=Ze(s.body[h],"textord").text}n=Number(l),a=2*n}var m=!a;i.body.forEach(function(e){for(var t=1;t<e.length;t+=2){Ze(Ze(e[t],"styling").body[0],"ordgroup").body.unshift(o)}if(m)a<e.length&&(a=e.length);else{var r=e.length/2;if(n<r)throw new X("Too many math in a row: expected "+n+", but got "+r,e[0])}});for(var c=0;c<a;++c){var u="r",p=0;c%2==1?u="l":0<c&&m&&(p=1),r[c]={type:"align",align:u,pregap:p,postgap:0}}return i};yr({type:"array",names:["array","darray"],props:{numArgs:1},handler:function(e,t){var r={cols:(Qe(t[0])?[t[0]]:Ze(t[0],"ordgroup").body).map(function(e){var t=function(e){var t=Qe(e);if(!t)throw new Error("Expected node of symbol group type, but got "+(e?"node of type "+e.type:String(e)));return t}(e).text;if(-1!=="lcr".indexOf(t))return{type:"align",align:t};if("|"===t)return{type:"separator",separator:"|"};if(":"===t)return{type:"separator",separator:":"};throw new X("Unknown column alignment: "+t,e)}),hskipBeforeAndAfter:!0};return xr(e.parser,r,wr(e.envName))},htmlBuilder:kr,mathmlBuilder:Sr}),yr({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix"],props:{numArgs:0},handler:function(e){var t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName],r=xr(e.parser,{hskipBeforeAndAfter:!1},wr(e.envName));return t?{type:"leftright",mode:e.mode,body:[r],left:t[0],right:t[1]}:r},htmlBuilder:kr,mathmlBuilder:Sr}),yr({type:"array",names:["cases","dcases"],props:{numArgs:0},handler:function(e){var t=xr(e.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},wr(e.envName));return{type:"leftright",mode:e.mode,body:[t],left:"\\{",right:"."}},htmlBuilder:kr,mathmlBuilder:Sr}),yr({type:"array",names:["aligned"],props:{numArgs:0},handler:zr,htmlBuilder:kr,mathmlBuilder:Sr}),yr({type:"array",names:["gathered"],props:{numArgs:0},handler:function(e){return xr(e.parser,{cols:[{type:"align",align:"c"}],addJot:!0},"display")},htmlBuilder:kr,mathmlBuilder:Sr}),yr({type:"array",names:["alignedat"],props:{numArgs:1},handler:zr,htmlBuilder:kr,mathmlBuilder:Sr}),lt({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler:function(e,t){throw new X(e.funcName+" valid only within array environment")}});var Mr=vr;lt({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler:function(e,t){var r=e.parser,n=e.funcName,i=t[0];if("ordgroup"!==i.type)throw new X("Invalid environment name",i);for(var a="",o=0;o<i.body.length;++o)a+=Ze(i.body[o],"textord").text;if("\\begin"!==n)return{type:"environment",mode:r.mode,name:a,nameGroup:i};if(!Mr.hasOwnProperty(a))throw new X("No such environment: "+a,i);var s=Mr[a],l=r.parseArguments("\\begin{"+a+"}",s),h=l.args,m=l.optArgs,c={mode:r.mode,envName:a,parser:r},u=s.handler(c,h,m);r.expect("\\end",!1);var p=r.nextToken,d=Ze(r.parseFunction(),"environment");if(d.name!==a)throw new X("Mismatch: \\begin{"+a+"} matched by \\end{"+d.name+"}",p);return u}});var Tr=$e.makeSpan;function Ar(e,t){var r=dt(e.body,t,!0);return Tr([e.mclass],r,t)}function Br(e,t){var r=Ct(e.body,t);return Mt.newDocumentFragment(r)}lt({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName,i=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+n.substr(5),body:mt(i)}},htmlBuilder:Ar,mathmlBuilder:Br});var Cr=function(e){var t="ordgroup"===e.type&&e.body.length?e.body[0]:e;return"atom"!==t.type||"bin"!==t.family&&"rel"!==t.family?"mord":"m"+t.family};lt({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler:function(e,t){return{type:"mclass",mode:e.parser.mode,mclass:Cr(t[0]),body:[t[1]]}}}),lt({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler:function(e,t){var r,n=e.parser,i=e.funcName,a=t[1],o=t[0];r="\\stackrel"!==i?Cr(a):"mrel";var s={type:"op",mode:a.mode,limits:!0,alwaysHandleSupSub:!0,symbol:!1,suppressBaseShift:"\\stackrel"!==i,body:mt(a)},l={type:"supsub",mode:o.mode,base:s,sup:"\\underset"===i?null:o,sub:"\\underset"===i?o:null};return{type:"mclass",mode:n.mode,mclass:r,body:[l]}},htmlBuilder:Ar,mathmlBuilder:Br});var Nr=function(e,t){var r=e.font,n=t.withFont(r);return bt(e.body,n)},qr=function(e,t){var r=e.font,n=t.withFont(r);return qt(e.body,n)},Er={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};lt({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,greediness:2},handler:function(e,t){var r=e.parser,n=e.funcName,i=t[0],a=n;return a in Er&&(a=Er[a]),{type:"font",mode:r.mode,font:a.slice(1),body:i}},htmlBuilder:Nr,mathmlBuilder:qr}),lt({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1,greediness:2},handler:function(e,t){var r=e.parser,n=t[0];return{type:"mclass",mode:r.mode,mclass:Cr(n),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:n}]}}}),lt({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it"],props:{numArgs:0,allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName,i=e.breakOnTokenText,a=r.mode;r.consumeSpaces();var o=r.parseExpression(!0,i);return{type:"font",mode:a,font:"math"+n.slice(1),body:{type:"ordgroup",mode:r.mode,body:o}}},htmlBuilder:Nr,mathmlBuilder:qr});var Or=function(e,t){var r=t.style;"display"===e.size?r=q.DISPLAY:"text"===e.size&&r.size===q.DISPLAY.size?r=q.TEXT:"script"===e.size?r=q.SCRIPT:"scriptscript"===e.size&&(r=q.SCRIPTSCRIPT);var n,i=r.fracNum(),a=r.fracDen();n=t.havingStyle(i);var o=bt(e.numer,n,t);if(e.continued){var s=8.5/t.fontMetrics().ptPerEm,l=3.5/t.fontMetrics().ptPerEm;o.height=o.height<s?s:o.height,o.depth=o.depth<l?l:o.depth}n=t.havingStyle(a);var h,m,c,u,p,d,f,g,v,y,b=bt(e.denom,n,t);if(c=e.hasBarLine?(m=(h=e.barSize?(m=He(e.barSize,t),$e.makeLineSpan("frac-line",t,m)):$e.makeLineSpan("frac-line",t)).height,h.height):(h=null,m=0,t.fontMetrics().defaultRuleThickness),d=r.size===q.DISPLAY.size?(u=t.fontMetrics().num1,p=0<m?3*c:7*c,t.fontMetrics().denom1):(p=0<m?(u=t.fontMetrics().num2,c):(u=t.fontMetrics().num3,3*c),t.fontMetrics().denom2),h){var x=t.fontMetrics().axisHeight;u-o.depth-(x+.5*m)<p&&(u+=p-(u-o.depth-(x+.5*m))),x-.5*m-(b.height-d)<p&&(d+=p-(x-.5*m-(b.height-d)));var w=-(x-.5*m);f=$e.makeVList({positionType:"individualShift",children:[{type:"elem",elem:b,shift:d},{type:"elem",elem:h,shift:w},{type:"elem",elem:o,shift:-u}]},t)}else{var k=u-o.depth-(b.height-d);k<p&&(u+=.5*(p-k),d+=.5*(p-k)),f=$e.makeVList({positionType:"individualShift",children:[{type:"elem",elem:b,shift:d},{type:"elem",elem:o,shift:-u}]},t)}return n=t.havingStyle(r),f.height*=n.sizeMultiplier/t.sizeMultiplier,f.depth*=n.sizeMultiplier/t.sizeMultiplier,g=r.size===q.DISPLAY.size?t.fontMetrics().delim1:t.fontMetrics().delim2,v=null==e.leftDelim?yt(t,["mopen"]):hr(e.leftDelim,g,!0,t.havingStyle(r),e.mode,["mopen"]),y=e.continued?$e.makeSpan([]):null==e.rightDelim?yt(t,["mclose"]):hr(e.rightDelim,g,!0,t.havingStyle(r),e.mode,["mclose"]),$e.makeSpan(["mord"].concat(n.sizingClasses(t)),[v,$e.makeSpan(["mfrac"],[f]),y],t)},Ir=function(e,t){var r=new Mt.MathNode("mfrac",[qt(e.numer,t),qt(e.denom,t)]);if(e.hasBarLine){if(e.barSize){var n=He(e.barSize,t);r.setAttribute("linethickness",n+"em")}}else r.setAttribute("linethickness","0px");if(null==e.leftDelim&&null==e.rightDelim)return r;var i=[];if(null!=e.leftDelim){var a=new Mt.MathNode("mo",[new Mt.TextNode(e.leftDelim)]);a.setAttribute("fence","true"),i.push(a)}if(i.push(r),null!=e.rightDelim){var o=new Mt.MathNode("mo",[new Mt.TextNode(e.rightDelim)]);o.setAttribute("fence","true"),i.push(o)}return At(i)};lt({type:"genfrac",names:["\\cfrac","\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,greediness:2},handler:function(e,t){var r,n=e.parser,i=e.funcName,a=t[0],o=t[1],s=null,l=null,h="auto";switch(i){case"\\cfrac":case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,s="(",l=")";break;case"\\\\bracefrac":r=!1,s="\\{",l="\\}";break;case"\\\\brackfrac":r=!1,s="[",l="]";break;default:throw new Error("Unrecognized genfrac command")}switch(i){case"\\cfrac":case"\\dfrac":case"\\dbinom":h="display";break;case"\\tfrac":case"\\tbinom":h="text"}return{type:"genfrac",mode:n.mode,continued:"\\cfrac"===i,numer:a,denom:o,hasBarLine:r,leftDelim:s,rightDelim:l,size:h,barSize:null}},htmlBuilder:Or,mathmlBuilder:Ir}),lt({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler:function(e){var t,r=e.parser,n=e.funcName,i=e.token;switch(n){case"\\over":t="\\frac";break;case"\\choose":t="\\binom";break;case"\\atop":t="\\\\atopfrac";break;case"\\brace":t="\\\\bracefrac";break;case"\\brack":t="\\\\brackfrac";break;default:throw new Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:t,token:i}}});var Rr=["display","text","script","scriptscript"],Lr=function(e){var t=null;return 0<e.length&&(t="."===(t=e)?null:t),t};lt({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,greediness:6,argTypes:["math","math","size","text","math","math"]},handler:function(e,t){var r=e.parser,n=t[4],i=t[5],a=Ke(t[0],"ordgroup");a=Je(a?a.body[0]:t[0],"open");var o=Lr(a.text),s=Ke(t[1],"ordgroup");s=Je(s?s.body[0]:t[1],"close");var l,h=Lr(s.text),m=Ze(t[2],"size"),c=null;l=!!m.isBlank||0<(c=m.value).number;var u="auto",p=Ke(t[3],"ordgroup");if(p){if(0<p.body.length){var d=Ze(p.body[0],"textord");u=Rr[Number(d.text)]}}else p=Ze(t[3],"textord"),u=Rr[Number(p.text)];return{type:"genfrac",mode:r.mode,numer:n,denom:i,continued:!1,hasBarLine:l,barSize:c,leftDelim:o,rightDelim:h,size:u}},htmlBuilder:Or,mathmlBuilder:Ir}),lt({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler:function(e,t){var r=e.parser,n=(e.funcName,e.token);return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:Ze(t[0],"size").value,token:n}}}),lt({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:function(e,t){var r=e.parser,n=(e.funcName,t[0]),i=T(Ze(t[1],"infix").size),a=t[2],o=0<i.number;return{type:"genfrac",mode:r.mode,numer:n,denom:a,continued:!1,hasBarLine:o,barSize:i,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:Or,mathmlBuilder:Ir});var Hr=function(e,t){var r,n,i=t.style,a=Ke(e,"supsub");n=a?(r=a.sup?bt(a.sup,t.havingStyle(i.sup()),t):bt(a.sub,t.havingStyle(i.sub()),t),Ze(a.base,"horizBrace")):Ze(e,"horizBrace");var o,s=bt(n.base,t.havingBaseStyle(q.DISPLAY)),l=Dt(n,t);if(n.isOver?(o=$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:l}]},t)).children[0].children[0].children[1].classes.push("svg-align"):(o=$e.makeVList({positionType:"bottom",positionData:s.depth+.1+l.height,children:[{type:"elem",elem:l},{type:"kern",size:.1},{type:"elem",elem:s}]},t)).children[0].children[0].children[0].classes.push("svg-align"),r){var h=$e.makeSpan(["mord",n.isOver?"mover":"munder"],[o],t);o=n.isOver?$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:h},{type:"kern",size:.2},{type:"elem",elem:r}]},t):$e.makeVList({positionType:"bottom",positionData:h.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:h}]},t)}return $e.makeSpan(["mord",n.isOver?"mover":"munder"],[o],t)};lt({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=e.funcName;return{type:"horizBrace",mode:r.mode,label:n,isOver:/^\\over/.test(n),base:t[0]}},htmlBuilder:Hr,mathmlBuilder:function(e,t){var r=Ht(e.label);return new Mt.MathNode(e.isOver?"mover":"munder",[qt(e.base,t),r])}}),lt({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[1],i=Ze(t[0],"url").url;return{type:"href",mode:r.mode,href:i,body:mt(n)}},htmlBuilder:function(e,t){var r=dt(e.body,t,!1);return $e.makeAnchor(e.href,[],r,t)},mathmlBuilder:function(e,t){var r=Nt(e.body,t);return r instanceof St||(r=new St("mrow",[r])),r.setAttribute("href",e.href),r}}),lt({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:function(e,t){for(var r=e.parser,n=Ze(t[0],"url").url,i=[],a=0;a<n.length;a++){var o=n[a];"~"===o&&(o="\\textasciitilde"),i.push({type:"textord",mode:"text",text:o})}var s={type:"text",mode:r.mode,font:"\\texttt",body:i};return{type:"href",mode:r.mode,href:n,body:mt(s)}}}),lt({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:function(e,t){return{type:"htmlmathml",mode:e.parser.mode,html:mt(t[0]),mathml:mt(t[1])}},htmlBuilder:function(e,t){var r=dt(e.html,t,!1);return $e.makeFragment(r)},mathmlBuilder:function(e,t){return Nt(e.mathml,t)}}),lt({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName,i=Ze(t[0],"size");if(r.settings.strict){var a="m"===n[1],o="mu"===i.value.unit;a?(o||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" supports only mu units, not "+i.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" works only in math mode")):o&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:i.value}},htmlBuilder:function(e,t){return $e.makeGlue(e.dimension,t)},mathmlBuilder:function(e,t){var r=He(e.dimension,t);return new Mt.SpaceNode(r)}}),lt({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=e.funcName,i=t[0];return{type:"lap",mode:r.mode,alignment:n.slice(5),body:i}},htmlBuilder:function(e,t){var r;r="clap"===e.alignment?(r=$e.makeSpan([],[bt(e.body,t)]),$e.makeSpan(["inner"],[r],t)):$e.makeSpan(["inner"],[bt(e.body,t)]);var n=$e.makeSpan(["fix"],[]),i=$e.makeSpan([e.alignment],[r,n],t),a=$e.makeSpan(["strut"]);return a.style.height=i.height+i.depth+"em",a.style.verticalAlign=-i.depth+"em",i.children.unshift(a),i=$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:i}]},t),$e.makeSpan(["mord"],[i],t)},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mpadded",[qt(e.body,t)]);if("rlap"!==e.alignment){var n="llap"===e.alignment?"-1":"-0.5";r.setAttribute("lspace",n+"width")}return r.setAttribute("width","0px"),r}}),lt({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1,consumeMode:"math"},handler:function(e,t){var r=e.funcName,n=e.parser,i=n.mode;n.switchMode("math");var a="\\("===r?"\\)":"$",o=n.parseExpression(!1,a);return n.expect(a,!1),n.switchMode(i),n.consume(),{type:"styling",mode:n.mode,style:"text",body:o}}}),lt({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler:function(e,t){throw new X("Mismatched "+e.funcName)}});var Dr=function(e,t){switch(t.style.size){case q.DISPLAY.size:return e.display;case q.TEXT.size:return e.text;case q.SCRIPT.size:return e.script;case q.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};lt({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4},handler:function(e,t){return{type:"mathchoice",mode:e.parser.mode,display:mt(t[0]),text:mt(t[1]),script:mt(t[2]),scriptscript:mt(t[3])}},htmlBuilder:function(e,t){var r=Dr(e,t),n=dt(r,t,!1);return $e.makeFragment(n)},mathmlBuilder:function(e,t){var r=Dr(e,t);return Nt(r,t)}});var Pr=function(e,t){var r,n,i,a=!1,o=Ke(e,"supsub");o?(r=o.sup,n=o.sub,i=Ze(o.base,"op"),a=!0):i=Ze(e,"op");var s,l=t.style,h=!1;if(l.size===q.DISPLAY.size&&i.symbol&&!Y.contains(["\\smallint"],i.name)&&(h=!0),i.symbol){var m=h?"Size2-Regular":"Size1-Regular",c="";if("\\oiint"!==i.name&&"\\oiiint"!==i.name||(c=i.name.substr(1),i.name="oiint"===c?"\\iint":"\\iiint"),s=$e.makeSymbol(i.name,m,"math",t,["mop","op-symbol",h?"large-op":"small-op"]),0<c.length){var u=s.italic,p=$e.staticSvg(c+"Size"+(h?"2":"1"),t);s=$e.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:0},{type:"elem",elem:p,shift:h?.08:0}]},t),i.name="\\"+c,s.classes.unshift("mop"),s.italic=u}}else if(i.body){var d=dt(i.body,t,!0);1===d.length&&d[0]instanceof O?(s=d[0]).classes[0]="mop":s=$e.makeSpan(["mop"],$e.tryCombineChars(d),t)}else{for(var f=[],g=1;g<i.name.length;g++)f.push($e.mathsym(i.name[g],i.mode));s=$e.makeSpan(["mop"],f,t)}var v=0,y=0;if((s instanceof O||"\\oiint"===i.name||"\\oiiint"===i.name)&&!i.suppressBaseShift&&(v=(s.height-s.depth)/2-t.fontMetrics().axisHeight,y=s.italic),a){var b,x,w;if(s=$e.makeSpan([],[s]),r){var k=bt(r,t.havingStyle(l.sup()),t);x={elem:k,kern:Math.max(t.fontMetrics().bigOpSpacing1,t.fontMetrics().bigOpSpacing3-k.depth)}}if(n){var S=bt(n,t.havingStyle(l.sub()),t);b={elem:S,kern:Math.max(t.fontMetrics().bigOpSpacing2,t.fontMetrics().bigOpSpacing4-S.height)}}if(x&&b){var z=t.fontMetrics().bigOpSpacing5+b.elem.height+b.elem.depth+b.kern+s.depth+v;w=$e.makeVList({positionType:"bottom",positionData:z,children:[{type:"kern",size:t.fontMetrics().bigOpSpacing5},{type:"elem",elem:b.elem,marginLeft:-y+"em"},{type:"kern",size:b.kern},{type:"elem",elem:s},{type:"kern",size:x.kern},{type:"elem",elem:x.elem,marginLeft:y+"em"},{type:"kern",size:t.fontMetrics().bigOpSpacing5}]},t)}else if(b){var M=s.height-v;w=$e.makeVList({positionType:"top",positionData:M,children:[{type:"kern",size:t.fontMetrics().bigOpSpacing5},{type:"elem",elem:b.elem,marginLeft:-y+"em"},{type:"kern",size:b.kern},{type:"elem",elem:s}]},t)}else{if(!x)return s;var T=s.depth+v;w=$e.makeVList({positionType:"bottom",positionData:T,children:[{type:"elem",elem:s},{type:"kern",size:x.kern},{type:"elem",elem:x.elem,marginLeft:y+"em"},{type:"kern",size:t.fontMetrics().bigOpSpacing5}]},t)}return $e.makeSpan(["mop","op-limits"],[w],t)}return v&&(s.style.position="relative",s.style.top=v+"em"),s},Fr=function(e,t){var r;if(e.symbol)r=new St("mo",[Tt(e.name,e.mode)]);else{if(!e.body)return kt([r=new St("mi",[new zt(e.name.slice(1))]),new St("mo",[Tt("\u2061","text")])]);r=new St("mo",Ct(e.body,t))}return r},Vr={"\u220f":"\\prod","\u2210":"\\coprod","\u2211":"\\sum","\u22c0":"\\bigwedge","\u22c1":"\\bigvee","\u22c2":"\\bigcap","\u22c3":"\\bigcap","\u2a00":"\\bigodot","\u2a01":"\\bigoplus","\u2a02":"\\bigotimes","\u2a04":"\\biguplus","\u2a06":"\\bigsqcup"};lt({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","\u220f","\u2210","\u2211","\u22c0","\u22c1","\u22c2","\u22c3","\u2a00","\u2a01","\u2a02","\u2a04","\u2a06"],props:{numArgs:0},handler:function(e,t){var r=e.parser,n=e.funcName;return 1===n.length&&(n=Vr[n]),{type:"op",mode:r.mode,limits:!0,symbol:!0,name:n}},htmlBuilder:Pr,mathmlBuilder:Fr}),lt({type:"op",names:["\\mathop"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=t[0];return{type:"op",mode:r.mode,limits:!1,symbol:!1,body:mt(n)}},htmlBuilder:Pr,mathmlBuilder:Fr});var Ur={"\u222b":"\\int","\u222c":"\\iint","\u222d":"\\iiint","\u222e":"\\oint","\u222f":"\\oiint","\u2230":"\\oiiint"};function Gr(e,t,r){for(var n=dt(e,t,!1),i=t.sizeMultiplier/r.sizeMultiplier,a=0;a<n.length;a++){var o=n[a].classes.indexOf("sizing");o<0?Array.prototype.push.apply(n[a].classes,t.sizingClasses(r)):n[a].classes[o+1]==="reset-size"+t.size&&(n[a].classes[o+1]="reset-size"+r.size),n[a].height*=i,n[a].depth*=i}return $e.makeFragment(n)}lt({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler:function(e){var t=e.parser,r=e.funcName;return{type:"op",mode:t.mode,limits:!1,symbol:!1,name:r}},htmlBuilder:Pr,mathmlBuilder:Fr}),lt({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler:function(e){var t=e.parser,r=e.funcName;return{type:"op",mode:t.mode,limits:!0,symbol:!1,name:r}},htmlBuilder:Pr,mathmlBuilder:Fr}),lt({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","\u222b","\u222c","\u222d","\u222e","\u222f","\u2230"],props:{numArgs:0},handler:function(e){var t=e.parser,r=e.funcName;return 1===r.length&&(r=Ur[r]),{type:"op",mode:t.mode,limits:!1,symbol:!0,name:r}},htmlBuilder:Pr,mathmlBuilder:Fr}),lt({type:"operatorname",names:["\\operatorname"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=t[0];return{type:"operatorname",mode:r.mode,body:mt(n)}},htmlBuilder:function(e,t){if(0<e.body.length){for(var r=e.body.map(function(e){var t=e.text;return"string"==typeof t?{type:"textord",mode:e.mode,text:t}:e}),n=dt(r,t.withFont("mathrm"),!0),i=0;i<n.length;i++){var a=n[i];a instanceof O&&(a.text=a.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}return $e.makeSpan(["mop"],n,t)}return $e.makeSpan(["mop"],[],t)},mathmlBuilder:function(e,t){for(var r=Ct(e.body,t.withFont("mathrm")),n=!0,i=0;i<r.length;i++){var a=r[i];if(a instanceof Mt.SpaceNode);else if(a instanceof Mt.MathNode)switch(a.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":var o=a.children[0];1===a.children.length&&o instanceof Mt.TextNode?o.text=o.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):n=!1;break;default:n=!1}else n=!1}if(n){var s=r.map(function(e){return e.toText()}).join("");r=[new Mt.TextNode(s,!1)]}var l=new Mt.MathNode("mi",r);l.setAttribute("mathvariant","normal");var h=new Mt.MathNode("mo",[Tt("\u2061","text")]);return Mt.newDocumentFragment([l,h])}}),ht({type:"ordgroup",htmlBuilder:function(e,t){return $e.makeSpan(["mord"],dt(e.body,t,!0),t)},mathmlBuilder:function(e,t){return Nt(e.body,t)}}),lt({type:"overline",names:["\\overline"],props:{numArgs:1},handler:function(e,t){var r=e.parser,n=t[0];return{type:"overline",mode:r.mode,body:n}},htmlBuilder:function(e,t){var r=bt(e.body,t.havingCrampedStyle()),n=$e.makeLineSpan("overline-line",t),i=$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*n.height},{type:"elem",elem:n},{type:"kern",size:n.height}]},t);return $e.makeSpan(["mord","overline"],[i],t)},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mo",[new Mt.TextNode("\u203e")]);r.setAttribute("stretchy","true");var n=new Mt.MathNode("mover",[qt(e.body,t),r]);return n.setAttribute("accent","true"),n}}),lt({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[0];return{type:"phantom",mode:r.mode,body:mt(n)}},htmlBuilder:function(e,t){var r=dt(e.body,t.withPhantom(),!1);return $e.makeFragment(r)},mathmlBuilder:function(e,t){var r=Ct(e.body,t);return new Mt.MathNode("mphantom",r)}}),lt({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[0];return{type:"hphantom",mode:r.mode,body:n}},htmlBuilder:function(e,t){var r=$e.makeSpan([],[bt(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var n=0;n<r.children.length;n++)r.children[n].height=0,r.children[n].depth=0;return r=$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t)},mathmlBuilder:function(e,t){var r=Ct(mt(e.body),t),n=new Mt.MathNode("mphantom",r);return n.setAttribute("height","0px"),n}}),lt({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){var r=e.parser,n=t[0];return{type:"vphantom",mode:r.mode,body:n}},htmlBuilder:function(e,t){var r=$e.makeSpan(["inner"],[bt(e.body,t.withPhantom())]),n=$e.makeSpan(["fix"],[]);return $e.makeSpan(["mord","rlap"],[r,n],t)},mathmlBuilder:function(e,t){var r=Ct(mt(e.body),t),n=new Mt.MathNode("mphantom",r);return n.setAttribute("width","0px"),n}});var Xr=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"],Yr=function(e,t){var r=t.havingSize(e.size);return Gr(e.body,r,t)};lt({type:"sizing",names:Xr,props:{numArgs:0,allowedInText:!0},handler:function(e,t){var r=e.breakOnTokenText,n=e.funcName,i=e.parser;i.consumeSpaces();var a=i.parseExpression(!1,r);return{type:"sizing",mode:i.mode,size:Xr.indexOf(n)+1,body:a}},htmlBuilder:Yr,mathmlBuilder:function(e,t){var r=t.havingSize(e.size),n=Ct(e.body,r),i=new Mt.MathNode("mstyle",n);return i.setAttribute("mathsize",r.sizeMultiplier+"em"),i}}),lt({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","text"],allowedInText:!0},handler:function(e,t){var r=e.parser,n=Ze(t[0],"size").value,i=t[1];return{type:"raisebox",mode:r.mode,dy:n,body:i}},htmlBuilder:function(e,t){var r={type:"text",mode:e.mode,body:mt(e.body),font:"mathrm"},n={type:"sizing",mode:e.mode,body:[r],size:6},i=Yr(n,t),a=He(e.dy,t);return $e.makeVList({positionType:"shift",positionData:-a,children:[{type:"elem",elem:i}]},t)},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mpadded",[qt(e.body,t)]),n=e.dy.number+e.dy.unit;return r.setAttribute("voffset",n),r}}),lt({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,argTypes:["size","size","size"]},handler:function(e,t,r){var n=e.parser,i=r[0],a=Ze(t[0],"size"),o=Ze(t[1],"size");return{type:"rule",mode:n.mode,shift:i&&Ze(i,"size").value,width:a.value,height:o.value}},htmlBuilder:function(e,t){var r=$e.makeSpan(["mord","rule"],[],t),n=0;e.shift&&(n=He(e.shift,t));var i=He(e.width,t),a=He(e.height,t);return r.style.borderRightWidth=i+"em",r.style.borderTopWidth=a+"em",r.style.bottom=n+"em",r.width=i,r.height=a+n,r.depth=-n,r.maxFontSize=1.125*a*t.sizeMultiplier,r},mathmlBuilder:function(e,t){return new Mt.MathNode("mrow")}}),lt({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:function(e,t,r){var n=e.parser,i=!1,a=!1,o=r[0]&&Ze(r[0],"ordgroup");if(o)for(var s="",l=0;l<o.body.length;++l){if("t"===(s=o.body[l].text))i=!0;else{if("b"!==s){a=i=!1;break}a=!0}}else a=i=!0;var h=t[0];return{type:"smash",mode:n.mode,body:h,smashHeight:i,smashDepth:a}},htmlBuilder:function(e,t){var r=$e.makeSpan(["mord"],[bt(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(var i=0;i<r.children.length;i++)r.children[i].depth=0;return $e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t)},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mpadded",[qt(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),lt({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler:function(e,t,r){var n=e.parser,i=r[0],a=t[0];return{type:"sqrt",mode:n.mode,body:a,index:i}},htmlBuilder:function(e,t){var r=bt(e.body,t.havingCrampedStyle());0===r.height&&(r.height=t.fontMetrics().xHeight),r=$e.wrapFragment(r,t);var n=t.fontMetrics().defaultRuleThickness,i=n;t.style.id<q.TEXT.id&&(i=t.fontMetrics().xHeight);var a=n+i/4,o=r.height+r.depth+a+n,s=sr(o,t),l=s.span,h=s.ruleWidth,m=s.advanceWidth,c=l.height-h;c>r.height+r.depth+a&&(a=(a+c-r.height-r.depth)/2);var u=l.height-r.height-a-h;r.style.paddingLeft=m+"em";var p=$e.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+u)},{type:"elem",elem:l},{type:"kern",size:h}]},t);if(e.index){var d=t.havingStyle(q.SCRIPTSCRIPT),f=bt(e.index,d,t),g=.6*(p.height-p.depth),v=$e.makeVList({positionType:"shift",positionData:-g,children:[{type:"elem",elem:f}]},t),y=$e.makeSpan(["root"],[v]);return $e.makeSpan(["mord","sqrt"],[y,p],t)}return $e.makeSpan(["mord","sqrt"],[p],t)},mathmlBuilder:function(e,t){var r=e.body,n=e.index;return n?new Mt.MathNode("mroot",[qt(r,t),qt(n,t)]):new Mt.MathNode("msqrt",[qt(r,t)])}});var _r={display:q.DISPLAY,text:q.TEXT,script:q.SCRIPT,scriptscript:q.SCRIPTSCRIPT};lt({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0},handler:function(e,t){var r=e.breakOnTokenText,n=e.funcName,i=e.parser;i.consumeSpaces();var a=i.parseExpression(!0,r),o=n.slice(1,n.length-5);return{type:"styling",mode:i.mode,style:o,body:a}},htmlBuilder:function(e,t){var r=_r[e.style],n=t.havingStyle(r).withFont("");return Gr(e.body,n,t)},mathmlBuilder:function(e,t){var r={display:q.DISPLAY,text:q.TEXT,script:q.SCRIPT,scriptscript:q.SCRIPTSCRIPT}[e.style],n=t.havingStyle(r),i=Ct(e.body,n),a=new Mt.MathNode("mstyle",i),o={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[e.style];return a.setAttribute("scriptlevel",o[0]),a.setAttribute("displaystyle",o[1]),a}});ht({type:"supsub",htmlBuilder:function(e,t){var r,n,i,a=(n=t,(i=(r=e).base)?"op"!==i.type?"accent"===i.type?Y.isCharacterBox(i.base)?Pt:null:"horizBrace"!==i.type?null:!r.sub===i.isOver?Hr:null:i.limits&&(n.style.size===q.DISPLAY.size||i.alwaysHandleSupSub)?Pr:null:null);if(a)return a(e,t);var o,s,l,h=e.base,m=e.sup,c=e.sub,u=bt(h,t),p=t.fontMetrics(),d=0,f=0,g=h&&Y.isCharacterBox(h);if(m){var v=t.havingStyle(t.style.sup());o=bt(m,v,t),g||(d=u.height-v.fontMetrics().supDrop*v.sizeMultiplier/t.sizeMultiplier)}if(c){var y=t.havingStyle(t.style.sub());s=bt(c,y,t),g||(f=u.depth+y.fontMetrics().subDrop*y.sizeMultiplier/t.sizeMultiplier)}l=t.style===q.DISPLAY?p.sup1:t.style.cramped?p.sup3:p.sup2;var b,x=t.sizeMultiplier,w=.5/p.ptPerEm/x+"em",k=null;if(s){var S=e.base&&"op"===e.base.type&&e.base.name&&("\\oiint"===e.base.name||"\\oiiint"===e.base.name);(u instanceof O||S)&&(k=-u.italic+"em")}if(o&&s){d=Math.max(d,l,o.depth+.25*p.xHeight),f=Math.max(f,p.sub2);var z=4*p.defaultRuleThickness;if(d-o.depth-(s.height-f)<z){f=z-(d-o.depth)+s.height;var M=.8*p.xHeight-(d-o.depth);0<M&&(d+=M,f-=M)}var T=[{type:"elem",elem:s,shift:f,marginRight:w,marginLeft:k},{type:"elem",elem:o,shift:-d,marginRight:w}];b=$e.makeVList({positionType:"individualShift",children:T},t)}else if(s){f=Math.max(f,p.sub1,s.height-.8*p.xHeight);var A=[{type:"elem",elem:s,marginLeft:k,marginRight:w}];b=$e.makeVList({positionType:"shift",positionData:f,children:A},t)}else{if(!o)throw new Error("supsub must have either sup or sub.");d=Math.max(d,l,o.depth+.25*p.xHeight),b=$e.makeVList({positionType:"shift",positionData:-d,children:[{type:"elem",elem:o,marginRight:w}]},t)}var B=gt(u,"right")||"mord";return $e.makeSpan([B],[u,$e.makeSpan(["msupsub"],[b])],t)},mathmlBuilder:function(e,t){var r,n=!1,i=Ke(e.base,"horizBrace");i&&!!e.sup===i.isOver&&(n=!0,r=i.isOver);var a,o=[qt(e.base,t)];if(e.sub&&o.push(qt(e.sub,t)),e.sup&&o.push(qt(e.sup,t)),n)a=r?"mover":"munder";else if(e.sub)if(e.sup){var s=e.base;a=s&&"op"===s.type&&s.limits&&t.style===q.DISPLAY?"munderover":"msubsup"}else{var l=e.base;a=l&&"op"===l.type&&l.limits&&t.style===q.DISPLAY?"munder":"msub"}else{var h=e.base;a=h&&"op"===h.type&&h.limits&&t.style===q.DISPLAY?"mover":"msup"}return new Mt.MathNode(a,o)}}),ht({type:"atom",htmlBuilder:function(e,t){return $e.mathsym(e.text,e.mode,t,["m"+e.family])},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mo",[Tt(e.text,e.mode)]);if("bin"===e.family){var n=Bt(e,t);"bold-italic"===n&&r.setAttribute("mathvariant",n)}else"punct"===e.family&&r.setAttribute("separator","true");return r}});var Wr={mi:"italic",mn:"normal",mtext:"normal"};ht({type:"mathord",htmlBuilder:function(e,t){return $e.makeOrd(e,t,"mathord")},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mi",[Tt(e.text,e.mode,t)]),n=Bt(e,t)||"italic";return n!==Wr[r.type]&&r.setAttribute("mathvariant",n),r}}),ht({type:"textord",htmlBuilder:function(e,t){return $e.makeOrd(e,t,"textord")},mathmlBuilder:function(e,t){var r,n=Tt(e.text,e.mode,t),i=Bt(e,t)||"normal";return r="text"===e.mode?new Mt.MathNode("mtext",[n]):/[0-9]/.test(e.text)?new Mt.MathNode("mn",[n]):"\\prime"===e.text?new Mt.MathNode("mo",[n]):new Mt.MathNode("mi",[n]),i!==Wr[r.type]&&r.setAttribute("mathvariant",i),r}});var jr={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},$r={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};ht({type:"spacing",htmlBuilder:function(e,t){if($r.hasOwnProperty(e.text)){var r=$r[e.text].className||"";if("text"!==e.mode)return $e.makeSpan(["mspace",r],[$e.mathsym(e.text,e.mode,t)],t);var n=$e.makeOrd(e,t,"textord");return n.classes.push(r),n}if(jr.hasOwnProperty(e.text))return $e.makeSpan(["mspace",jr[e.text]],[],t);throw new X('Unknown type of space "'+e.text+'"')},mathmlBuilder:function(e,t){if($r.hasOwnProperty(e.text))return new Mt.MathNode("mtext",[new Mt.TextNode("\xa0")]);if(jr.hasOwnProperty(e.text))return new Mt.MathNode("mspace");throw new X('Unknown type of space "'+e.text+'"')}}),ht({type:"tag",mathmlBuilder:function(e,t){var r=new Mt.MathNode("mtable",[new Mt.MathNode("mlabeledtr",[new Mt.MathNode("mtd",[Nt(e.tag,t)]),new Mt.MathNode("mtd",[Nt(e.body,t)])])]);return r.setAttribute("side","right"),r}});var Zr={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},Kr={"\\textbf":"textbf"},Jr={"\\textit":"textit"},Qr=function(e,t){var r=e.font;return r?Zr[r]?t.withTextFontFamily(Zr[r]):Kr[r]?t.withTextFontWeight(Kr[r]):t.withTextFontShape(Jr[r]):t};lt({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textit"],props:{numArgs:1,argTypes:["text"],greediness:2,allowedInText:!0,consumeMode:"text"},handler:function(e,t){var r=e.parser,n=e.funcName,i=t[0];return{type:"text",mode:r.mode,body:mt(i),font:n}},htmlBuilder:function(e,t){var r=Qr(e,t),n=dt(e.body,r,!0);return $e.makeSpan(["mord","text"],$e.tryCombineChars(n),r)},mathmlBuilder:function(e,t){var r=Qr(e,t);return Nt(e.body,r)}}),lt({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler:function(e,t){return{type:"underline",mode:e.parser.mode,body:t[0]}},htmlBuilder:function(e,t){var r=bt(e.body,t),n=$e.makeLineSpan("underline-line",t),i=$e.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:n.height},{type:"elem",elem:n},{type:"kern",size:3*n.height},{type:"elem",elem:r}]},t);return $e.makeSpan(["mord","underline"],[i],t)},mathmlBuilder:function(e,t){var r=new Mt.MathNode("mo",[new Mt.TextNode("\u203e")]);r.setAttribute("stretchy","true");var n=new Mt.MathNode("munder",[qt(e.body,t),r]);return n.setAttribute("accentunder","true"),n}}),lt({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler:function(e,t,r){throw new X("\\verb ended by end of line instead of matching delimiter")},htmlBuilder:function(e,t){for(var r=en(e),n=[],i=t.havingStyle(t.style.text()),a=0;a<r.length;a++){var o=r[a];"~"===o&&(o="\\textasciitilde"),n.push($e.makeSymbol(o,"Typewriter-Regular",e.mode,i,["mord","texttt"]))}return $e.makeSpan(["mord","text"].concat(i.sizingClasses(t)),$e.tryCombineChars(n),i)},mathmlBuilder:function(e,t){var r=new Mt.TextNode(en(e)),n=new Mt.MathNode("mtext",[r]);return n.setAttribute("mathvariant","monospace"),n}});var en=function(e){return e.body.replace(/ /g,e.star?"\u2423":"\xa0")},tn=at,rn="[ \r\n\t]",nn="\\\\[a-zA-Z@]+",an=new RegExp("^("+nn+")"+rn+"*$"),on="[\u0300-\u036f]",sn=new RegExp(on+"+$"),ln=(new RegExp("^"+nn),function(){function e(e){this.input=void 0,this.tokenRegex=void 0,this.input=e,this.tokenRegex=new RegExp("([ \r\n\t]+)|([!-\\[\\]-\u2027\u202a-\ud7ff\uf900-\uffff][\u0300-\u036f]*|[\ud800-\udbff][\udc00-\udfff][\u0300-\u036f]*|\\\\verb\\*([^]).*?\\3|\\\\verb([^*a-zA-Z]).*?\\4|\\\\[a-zA-Z@]+[ \r\n\t]*|\\\\[^\ud800-\udfff])","g")}return e.prototype.lex=function(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new a("EOF",new p(this,t,t));var r=this.tokenRegex.exec(e);if(null===r||r.index!==t)throw new X("Unexpected character: '"+e[t]+"'",new a(e[t],new p(this,t,t+1)));var n=r[2]||" ",i=n.match(an);return i&&(n=i[1]),new a(n,new p(this,t,this.tokenRegex.lastIndex))},e}()),hn=function(){function e(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}var t=e.prototype;return t.beginGroup=function(){this.undefStack.push({})},t.endGroup=function(){if(0===this.undefStack.length)throw new X("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(void 0===e[t]?delete this.current[t]:this.current[t]=e[t])},t.has=function(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)},t.get=function(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]},t.set=function(e,t,r){if(void 0===r&&(r=!1),r){for(var n=0;n<this.undefStack.length;n++)delete this.undefStack[n][e];0<this.undefStack.length&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var i=this.undefStack[this.undefStack.length-1];i&&!i.hasOwnProperty(e)&&(i[e]=this.current[e])}this.current[e]=t},e}(),mn={},cn=mn;function un(e,t){mn[e]=t}un("\\@firstoftwo",function(e){return{tokens:e.consumeArgs(2)[0],numArgs:0}}),un("\\@secondoftwo",function(e){return{tokens:e.consumeArgs(2)[1],numArgs:0}}),un("\\@ifnextchar",function(e){var t=e.consumeArgs(3),r=e.future();return 1===t[0].length&&t[0][0].text===r.text?{tokens:t[1],numArgs:0}:{tokens:t[2],numArgs:0}}),un("\\@ifstar","\\@ifnextchar *{\\@firstoftwo{#1}}"),un("\\TextOrMath",function(e){var t=e.consumeArgs(2);return"text"===e.mode?{tokens:t[0],numArgs:0}:{tokens:t[1],numArgs:0}});var pn={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};un("\\char",function(e){var t,r=e.popToken(),n="";if("'"===r.text)t=8,r=e.popToken();else if('"'===r.text)t=16,r=e.popToken();else if("`"===r.text)if("\\"===(r=e.popToken()).text[0])n=r.text.charCodeAt(1);else{if("EOF"===r.text)throw new X("\\char` missing argument");n=r.text.charCodeAt(0)}else t=10;if(t){if(null==(n=pn[r.text])||t<=n)throw new X("Invalid base-"+t+" digit "+r.text);for(var i;null!=(i=pn[e.future().text])&&i<t;)n*=t,n+=i,e.popToken()}return"\\@char{"+n+"}"});var dn=function(e,t){var r=e.consumeArgs(1)[0];if(1!==r.length)throw new X("\\gdef's first argument must be a macro name");var n=r[0].text,i=0;for(r=e.consumeArgs(1)[0];1===r.length&&"#"===r[0].text;){if(1!==(r=e.consumeArgs(1)[0]).length)throw new X('Invalid argument number length "'+r.length+'"');if(!/^[1-9]$/.test(r[0].text))throw new X('Invalid argument number "'+r[0].text+'"');if(i++,parseInt(r[0].text)!==i)throw new X('Argument number "'+r[0].text+'" out of order');r=e.consumeArgs(1)[0]}return e.macros.set(n,{tokens:r,numArgs:i},t),""};un("\\gdef",function(e){return dn(e,!0)}),un("\\def",function(e){return dn(e,!1)}),un("\\global",function(e){var t=e.consumeArgs(1)[0];if(1!==t.length)throw new X("Invalid command after \\global");var r=t[0].text;if("\\def"===r)return dn(e,!0);throw new X("Invalid command '"+r+"' after \\global")});var fn=function(e,t,r){var n=e.consumeArgs(1)[0];if(1!==n.length)throw new X("\\newcommand's first argument must be a macro name");var i=n[0].text,a=e.isDefined(i);if(a&&!t)throw new X("\\newcommand{"+i+"} attempting to redefine "+i+"; use \\renewcommand");if(!a&&!r)throw new X("\\renewcommand{"+i+"} when command "+i+" does not yet exist; use \\newcommand");var o=0;if(1===(n=e.consumeArgs(1)[0]).length&&"["===n[0].text){for(var s="",l=e.expandNextToken();"]"!==l.text&&"EOF"!==l.text;)s+=l.text,l=e.expandNextToken();if(!s.match(/^\s*[0-9]+\s*$/))throw new X("Invalid number of arguments: "+s);o=parseInt(s),n=e.consumeArgs(1)[0]}return e.macros.set(i,{tokens:n,numArgs:o}),""};un("\\newcommand",function(e){return fn(e,!1,!0)}),un("\\renewcommand",function(e){return fn(e,!0,!1)}),un("\\providecommand",function(e){return fn(e,!0,!0)}),un("\\bgroup","{"),un("\\egroup","}"),un("\\begingroup","{"),un("\\endgroup","}"),un("\\lq","`"),un("\\rq","'"),un("\\aa","\\r a"),un("\\AA","\\r A"),un("\\textcopyright","\\html@mathml{\\textcircled{c}}{\\char`\xa9}"),un("\\copyright","\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}"),un("\\textregistered","\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xae}"),un("\u2102","\\mathbb{C}"),un("\u210d","\\mathbb{H}"),un("\u2115","\\mathbb{N}"),un("\u2119","\\mathbb{P}"),un("\u211a","\\mathbb{Q}"),un("\u211d","\\mathbb{R}"),un("\u2124","\\mathbb{Z}"),un("\u210e","\\mathit{h}"),un("\u212c","\\mathscr{B}"),un("\u2130","\\mathscr{E}"),un("\u2131","\\mathscr{F}"),un("\u210b","\\mathscr{H}"),un("\u2110","\\mathscr{I}"),un("\u2112","\\mathscr{L}"),un("\u2133","\\mathscr{M}"),un("\u211b","\\mathscr{R}"),un("\u212d","\\mathfrak{C}"),un("\u210c","\\mathfrak{H}"),un("\u2128","\\mathfrak{Z}"),un("\xb7","\\cdotp"),un("\\llap","\\mathllap{\\textrm{#1}}"),un("\\rlap","\\mathrlap{\\textrm{#1}}"),un("\\clap","\\mathclap{\\textrm{#1}}"),un("\\not","\\mathrel{\\mathrlap\\@not}"),un("\\neq","\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`\u2260}}"),un("\\ne","\\neq"),un("\u2260","\\neq"),un("\\notin","\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`\u2209}}"),un("\u2209","\\notin"),un("\u2258","\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`\u2258}}"),un("\u2259","\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`\u2258}}"),un("\u225a","\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`\u225a}}"),un("\u225b","\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`\u225b}}"),un("\u225d","\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`\u225d}}"),un("\u225e","\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`\u225e}}"),un("\u225f","\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`\u225f}}"),un("\u27c2","\\perp"),un("\u203c","\\mathclose{!\\mkern-0.8mu!}"),un("\u220c","\\notni"),un("\u231c","\\ulcorner"),un("\u231d","\\urcorner"),un("\u231e","\\llcorner"),un("\u231f","\\lrcorner"),un("\xa9","\\copyright"),un("\xae","\\textregistered"),un("\ufe0f","\\textregistered"),un("\\vdots","\\mathord{\\varvdots\\rule{0pt}{15pt}}"),un("\u22ee","\\vdots"),un("\\varGamma","\\mathit{\\Gamma}"),un("\\varDelta","\\mathit{\\Delta}"),un("\\varTheta","\\mathit{\\Theta}"),un("\\varLambda","\\mathit{\\Lambda}"),un("\\varXi","\\mathit{\\Xi}"),un("\\varPi","\\mathit{\\Pi}"),un("\\varSigma","\\mathit{\\Sigma}"),un("\\varUpsilon","\\mathit{\\Upsilon}"),un("\\varPhi","\\mathit{\\Phi}"),un("\\varPsi","\\mathit{\\Psi}"),un("\\varOmega","\\mathit{\\Omega}"),un("\\colon","\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu"),un("\\boxed","\\fbox{$\\displaystyle{#1}$}"),un("\\iff","\\DOTSB\\;\\Longleftrightarrow\\;"),un("\\implies","\\DOTSB\\;\\Longrightarrow\\;"),un("\\impliedby","\\DOTSB\\;\\Longleftarrow\\;");var gn={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};un("\\dots",function(e){var t="\\dotso",r=e.expandAfterFuture().text;return r in gn?t=gn[r]:"\\not"===r.substr(0,4)?t="\\dotsb":r in W.math&&Y.contains(["bin","rel"],W.math[r].group)&&(t="\\dotsb"),t});var vn={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};un("\\dotso",function(e){return e.future().text in vn?"\\ldots\\,":"\\ldots"}),un("\\dotsc",function(e){var t=e.future().text;return t in vn&&","!==t?"\\ldots\\,":"\\ldots"}),un("\\cdots",function(e){return e.future().text in vn?"\\@cdots\\,":"\\@cdots"}),un("\\dotsb","\\cdots"),un("\\dotsm","\\cdots"),un("\\dotsi","\\!\\cdots"),un("\\dotsx","\\ldots\\,"),un("\\DOTSI","\\relax"),un("\\DOTSB","\\relax"),un("\\DOTSX","\\relax"),un("\\tmspace","\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax"),un("\\,","\\tmspace+{3mu}{.1667em}"),un("\\thinspace","\\,"),un("\\>","\\mskip{4mu}"),un("\\:","\\tmspace+{4mu}{.2222em}"),un("\\medspace","\\:"),un("\\;","\\tmspace+{5mu}{.2777em}"),un("\\thickspace","\\;"),un("\\!","\\tmspace-{3mu}{.1667em}"),un("\\negthinspace","\\!"),un("\\negmedspace","\\tmspace-{4mu}{.2222em}"),un("\\negthickspace","\\tmspace-{5mu}{.277em}"),un("\\enspace","\\kern.5em "),un("\\enskip","\\hskip.5em\\relax"),un("\\quad","\\hskip1em\\relax"),un("\\qquad","\\hskip2em\\relax"),un("\\tag","\\@ifstar\\tag@literal\\tag@paren"),un("\\tag@paren","\\tag@literal{({#1})}"),un("\\tag@literal",function(e){if(e.macros.get("\\df@tag"))throw new X("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"}),un("\\bmod","\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}"),un("\\pod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)"),un("\\pmod","\\pod{{\\rm mod}\\mkern6mu#1}"),un("\\mod","\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1"),un("\\pmb","\\html@mathml{\\@binrel{#1}{\\mathrlap{#1}\\mathrlap{\\mkern0.4mu\\raisebox{0.4mu}{$#1$}}{\\mkern0.8mu#1}}}{\\mathbf{#1}}"),un("\\\\","\\newline"),un("\\TeX","\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}");var yn=H["Main-Regular"]["T".charCodeAt(0)][1]-.7*H["Main-Regular"]["A".charCodeAt(0)][1]+"em";un("\\LaTeX","\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+yn+"}{\\scriptsize A}\\kern-.15em\\TeX}{LaTeX}}"),un("\\KaTeX","\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+yn+"}{\\scriptsize A}\\kern-.15em\\TeX}{KaTeX}}"),un("\\hspace","\\@ifstar\\@hspacer\\@hspace"),un("\\@hspace","\\hskip #1\\relax"),un("\\@hspacer","\\rule{0pt}{0pt}\\hskip #1\\relax"),un("\\ordinarycolon",":"),un("\\vcentcolon","\\mathrel{\\mathop\\ordinarycolon}"),un("\\dblcolon","\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}"),un("\\coloneqq","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}"),un("\\Coloneqq","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}"),un("\\coloneq","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}"),un("\\Coloneq","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}"),un("\\eqqcolon","\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),un("\\Eqqcolon","\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}"),un("\\eqcolon","\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),un("\\Eqcolon","\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}"),un("\\colonapprox","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}"),un("\\Colonapprox","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}"),un("\\colonsim","\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}"),un("\\Colonsim","\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}"),un("\u2254","\\coloneqq"),un("\u2255","\\eqqcolon"),un("\u2a74","\\Coloneqq"),un("\\ratio","\\vcentcolon"),un("\\coloncolon","\\dblcolon"),un("\\colonequals","\\coloneqq"),un("\\coloncolonequals","\\Coloneqq"),un("\\equalscolon","\\eqqcolon"),un("\\equalscoloncolon","\\Eqqcolon"),un("\\colonminus","\\coloneq"),un("\\coloncolonminus","\\Coloneq"),un("\\minuscolon","\\eqcolon"),un("\\minuscoloncolon","\\Eqcolon"),un("\\coloncolonapprox","\\Colonapprox"),un("\\coloncolonsim","\\Colonsim"),un("\\simcolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),un("\\simcoloncolon","\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}"),un("\\approxcolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}"),un("\\approxcoloncolon","\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}"),un("\\notni","\\html@mathml{\\not\\ni}{\\mathrel{\\char`\u220c}}"),un("\\limsup","\\DOTSB\\mathop{\\operatorname{lim\\,sup}}\\limits"),un("\\liminf","\\DOTSB\\mathop{\\operatorname{lim\\,inf}}\\limits"),un("\u27e6","\\mathopen{[\\mkern-3.2mu[}"),un("\u27e7","\\mathclose{]\\mkern-3.2mu]}"),un("\\darr","\\downarrow"),un("\\dArr","\\Downarrow"),un("\\Darr","\\Downarrow"),un("\\lang","\\langle"),un("\\rang","\\rangle"),un("\\uarr","\\uparrow"),un("\\uArr","\\Uparrow"),un("\\Uarr","\\Uparrow"),un("\\N","\\mathbb{N}"),un("\\R","\\mathbb{R}"),un("\\Z","\\mathbb{Z}"),un("\\alef","\\aleph"),un("\\alefsym","\\aleph"),un("\\Alpha","\\mathrm{A}"),un("\\Beta","\\mathrm{B}"),un("\\bull","\\bullet"),un("\\Chi","\\mathrm{X}"),un("\\clubs","\\clubsuit"),un("\\cnums","\\mathbb{C}"),un("\\Complex","\\mathbb{C}"),un("\\Dagger","\\ddagger"),un("\\diamonds","\\diamondsuit"),un("\\empty","\\emptyset"),un("\\Epsilon","\\mathrm{E}"),un("\\Eta","\\mathrm{H}"),un("\\exist","\\exists"),un("\\harr","\\leftrightarrow"),un("\\hArr","\\Leftrightarrow"),un("\\Harr","\\Leftrightarrow"),un("\\hearts","\\heartsuit"),un("\\image","\\Im"),un("\\infin","\\infty"),un("\\Iota","\\mathrm{I}"),un("\\isin","\\in"),un("\\Kappa","\\mathrm{K}"),un("\\larr","\\leftarrow"),un("\\lArr","\\Leftarrow"),un("\\Larr","\\Leftarrow"),un("\\lrarr","\\leftrightarrow"),un("\\lrArr","\\Leftrightarrow"),un("\\Lrarr","\\Leftrightarrow"),un("\\Mu","\\mathrm{M}"),un("\\natnums","\\mathbb{N}"),un("\\Nu","\\mathrm{N}"),un("\\Omicron","\\mathrm{O}"),un("\\plusmn","\\pm"),un("\\rarr","\\rightarrow"),un("\\rArr","\\Rightarrow"),un("\\Rarr","\\Rightarrow"),un("\\real","\\Re"),un("\\reals","\\mathbb{R}"),un("\\Reals","\\mathbb{R}"),un("\\Rho","\\mathrm{R}"),un("\\sdot","\\cdot"),un("\\sect","\\S"),un("\\spades","\\spadesuit"),un("\\sub","\\subset"),un("\\sube","\\subseteq"),un("\\supe","\\supseteq"),un("\\Tau","\\mathrm{T}"),un("\\thetasym","\\vartheta"),un("\\weierp","\\wp"),un("\\Zeta","\\mathrm{Z}");var bn={"\\relax":!0,"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0},xn=function(){function e(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new hn(cn,t.macros),this.mode=r,this.stack=[]}var t=e.prototype;return t.feed=function(e){this.lexer=new ln(e)},t.switchMode=function(e){this.mode=e},t.beginGroup=function(){this.macros.beginGroup()},t.endGroup=function(){this.macros.endGroup()},t.future=function(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]},t.popToken=function(){return this.future(),this.stack.pop()},t.pushToken=function(e){this.stack.push(e)},t.pushTokens=function(e){var t;(t=this.stack).push.apply(t,e)},t.consumeSpaces=function(){for(;;){if(" "!==this.future().text)break;this.stack.pop()}},t.consumeArgs=function(e){for(var t=[],r=0;r<e;++r){this.consumeSpaces();var n=this.popToken();if("{"===n.text){for(var i=[],a=1;0!==a;){var o=this.popToken();if(i.push(o),"{"===o.text)++a;else if("}"===o.text)--a;else if("EOF"===o.text)throw new X("End of input in macro argument",n)}i.pop(),i.reverse(),t[r]=i}else{if("EOF"===n.text)throw new X("End of input expecting macro argument");t[r]=[n]}}return t},t.expandOnce=function(){var e=this.popToken(),t=e.text,r=this._getExpansion(t);if(null==r)return this.pushToken(e),e;if(this.expansionCount++,this.expansionCount>this.settings.maxExpand)throw new X("Too many expansions: infinite loop or need to increase maxExpand setting");var n=r.tokens;if(r.numArgs)for(var i=this.consumeArgs(r.numArgs),a=(n=n.slice()).length-1;0<=a;--a){var o=n[a];if("#"===o.text){if(0===a)throw new X("Incomplete placeholder at end of macro body",o);if("#"===(o=n[--a]).text)n.splice(a+1,1);else{if(!/^[1-9]$/.test(o.text))throw new X("Not a valid argument number",o);var s;(s=n).splice.apply(s,[a,2].concat(i[+o.text-1]))}}}return this.pushTokens(n),n},t.expandAfterFuture=function(){return this.expandOnce(),this.future()},t.expandNextToken=function(){for(;;){var e=this.expandOnce();if(e instanceof a){if("\\relax"!==e.text)return this.stack.pop();this.stack.pop()}}throw new Error},t.expandMacro=function(e){if(this.macros.get(e)){var t=[],r=this.stack.length;for(this.pushToken(new a(e));this.stack.length>r;){this.expandOnce()instanceof a&&t.push(this.stack.pop())}return t}},t.expandMacroAsText=function(e){var t=this.expandMacro(e);return t?t.map(function(e){return e.text}).join(""):t},t._getExpansion=function(e){var t=this.macros.get(e);if(null==t)return t;var r="function"==typeof t?t(this):t;if("string"!=typeof r)return r;var n=0;if(-1!==r.indexOf("#"))for(var i=r.replace(/##/g,"");-1!==i.indexOf("#"+(n+1));)++n;for(var a=new ln(r),o=[],s=a.lex();"EOF"!==s.text;)o.push(s),s=a.lex();return o.reverse(),{tokens:o,numArgs:n}},t.isDefined=function(e){return this.macros.has(e)||tn.hasOwnProperty(e)||W.math.hasOwnProperty(e)||W.text.hasOwnProperty(e)||bn.hasOwnProperty(e)},e}(),wn={"\u0301":{text:"\\'",math:"\\acute"},"\u0300":{text:"\\`",math:"\\grave"},"\u0308":{text:'\\"',math:"\\ddot"},"\u0303":{text:"\\~",math:"\\tilde"},"\u0304":{text:"\\=",math:"\\bar"},"\u0306":{text:"\\u",math:"\\breve"},"\u030c":{text:"\\v",math:"\\check"},"\u0302":{text:"\\^",math:"\\hat"},"\u0307":{text:"\\.",math:"\\dot"},"\u030a":{text:"\\r",math:"\\mathring"},"\u030b":{text:"\\H"}},kn={"\xe1":"a\u0301","\xe0":"a\u0300","\xe4":"a\u0308","\u01df":"a\u0308\u0304","\xe3":"a\u0303","\u0101":"a\u0304","\u0103":"a\u0306","\u1eaf":"a\u0306\u0301","\u1eb1":"a\u0306\u0300","\u1eb5":"a\u0306\u0303","\u01ce":"a\u030c","\xe2":"a\u0302","\u1ea5":"a\u0302\u0301","\u1ea7":"a\u0302\u0300","\u1eab":"a\u0302\u0303","\u0227":"a\u0307","\u01e1":"a\u0307\u0304","\xe5":"a\u030a","\u01fb":"a\u030a\u0301","\u1e03":"b\u0307","\u0107":"c\u0301","\u010d":"c\u030c","\u0109":"c\u0302","\u010b":"c\u0307","\u010f":"d\u030c","\u1e0b":"d\u0307","\xe9":"e\u0301","\xe8":"e\u0300","\xeb":"e\u0308","\u1ebd":"e\u0303","\u0113":"e\u0304","\u1e17":"e\u0304\u0301","\u1e15":"e\u0304\u0300","\u0115":"e\u0306","\u011b":"e\u030c","\xea":"e\u0302","\u1ebf":"e\u0302\u0301","\u1ec1":"e\u0302\u0300","\u1ec5":"e\u0302\u0303","\u0117":"e\u0307","\u1e1f":"f\u0307","\u01f5":"g\u0301","\u1e21":"g\u0304","\u011f":"g\u0306","\u01e7":"g\u030c","\u011d":"g\u0302","\u0121":"g\u0307","\u1e27":"h\u0308","\u021f":"h\u030c","\u0125":"h\u0302","\u1e23":"h\u0307","\xed":"i\u0301","\xec":"i\u0300","\xef":"i\u0308","\u1e2f":"i\u0308\u0301","\u0129":"i\u0303","\u012b":"i\u0304","\u012d":"i\u0306","\u01d0":"i\u030c","\xee":"i\u0302","\u01f0":"j\u030c","\u0135":"j\u0302","\u1e31":"k\u0301","\u01e9":"k\u030c","\u013a":"l\u0301","\u013e":"l\u030c","\u1e3f":"m\u0301","\u1e41":"m\u0307","\u0144":"n\u0301","\u01f9":"n\u0300","\xf1":"n\u0303","\u0148":"n\u030c","\u1e45":"n\u0307","\xf3":"o\u0301","\xf2":"o\u0300","\xf6":"o\u0308","\u022b":"o\u0308\u0304","\xf5":"o\u0303","\u1e4d":"o\u0303\u0301","\u1e4f":"o\u0303\u0308","\u022d":"o\u0303\u0304","\u014d":"o\u0304","\u1e53":"o\u0304\u0301","\u1e51":"o\u0304\u0300","\u014f":"o\u0306","\u01d2":"o\u030c","\xf4":"o\u0302","\u1ed1":"o\u0302\u0301","\u1ed3":"o\u0302\u0300","\u1ed7":"o\u0302\u0303","\u022f":"o\u0307","\u0231":"o\u0307\u0304","\u0151":"o\u030b","\u1e55":"p\u0301","\u1e57":"p\u0307","\u0155":"r\u0301","\u0159":"r\u030c","\u1e59":"r\u0307","\u015b":"s\u0301","\u1e65":"s\u0301\u0307","\u0161":"s\u030c","\u1e67":"s\u030c\u0307","\u015d":"s\u0302","\u1e61":"s\u0307","\u1e97":"t\u0308","\u0165":"t\u030c","\u1e6b":"t\u0307","\xfa":"u\u0301","\xf9":"u\u0300","\xfc":"u\u0308","\u01d8":"u\u0308\u0301","\u01dc":"u\u0308\u0300","\u01d6":"u\u0308\u0304","\u01da":"u\u0308\u030c","\u0169":"u\u0303","\u1e79":"u\u0303\u0301","\u016b":"u\u0304","\u1e7b":"u\u0304\u0308","\u016d":"u\u0306","\u01d4":"u\u030c","\xfb":"u\u0302","\u016f":"u\u030a","\u0171":"u\u030b","\u1e7d":"v\u0303","\u1e83":"w\u0301","\u1e81":"w\u0300","\u1e85":"w\u0308","\u0175":"w\u0302","\u1e87":"w\u0307","\u1e98":"w\u030a","\u1e8d":"x\u0308","\u1e8b":"x\u0307","\xfd":"y\u0301","\u1ef3":"y\u0300","\xff":"y\u0308","\u1ef9":"y\u0303","\u0233":"y\u0304","\u0177":"y\u0302","\u1e8f":"y\u0307","\u1e99":"y\u030a","\u017a":"z\u0301","\u017e":"z\u030c","\u1e91":"z\u0302","\u017c":"z\u0307","\xc1":"A\u0301","\xc0":"A\u0300","\xc4":"A\u0308","\u01de":"A\u0308\u0304","\xc3":"A\u0303","\u0100":"A\u0304","\u0102":"A\u0306","\u1eae":"A\u0306\u0301","\u1eb0":"A\u0306\u0300","\u1eb4":"A\u0306\u0303","\u01cd":"A\u030c","\xc2":"A\u0302","\u1ea4":"A\u0302\u0301","\u1ea6":"A\u0302\u0300","\u1eaa":"A\u0302\u0303","\u0226":"A\u0307","\u01e0":"A\u0307\u0304","\xc5":"A\u030a","\u01fa":"A\u030a\u0301","\u1e02":"B\u0307","\u0106":"C\u0301","\u010c":"C\u030c","\u0108":"C\u0302","\u010a":"C\u0307","\u010e":"D\u030c","\u1e0a":"D\u0307","\xc9":"E\u0301","\xc8":"E\u0300","\xcb":"E\u0308","\u1ebc":"E\u0303","\u0112":"E\u0304","\u1e16":"E\u0304\u0301","\u1e14":"E\u0304\u0300","\u0114":"E\u0306","\u011a":"E\u030c","\xca":"E\u0302","\u1ebe":"E\u0302\u0301","\u1ec0":"E\u0302\u0300","\u1ec4":"E\u0302\u0303","\u0116":"E\u0307","\u1e1e":"F\u0307","\u01f4":"G\u0301","\u1e20":"G\u0304","\u011e":"G\u0306","\u01e6":"G\u030c","\u011c":"G\u0302","\u0120":"G\u0307","\u1e26":"H\u0308","\u021e":"H\u030c","\u0124":"H\u0302","\u1e22":"H\u0307","\xcd":"I\u0301","\xcc":"I\u0300","\xcf":"I\u0308","\u1e2e":"I\u0308\u0301","\u0128":"I\u0303","\u012a":"I\u0304","\u012c":"I\u0306","\u01cf":"I\u030c","\xce":"I\u0302","\u0130":"I\u0307","\u0134":"J\u0302","\u1e30":"K\u0301","\u01e8":"K\u030c","\u0139":"L\u0301","\u013d":"L\u030c","\u1e3e":"M\u0301","\u1e40":"M\u0307","\u0143":"N\u0301","\u01f8":"N\u0300","\xd1":"N\u0303","\u0147":"N\u030c","\u1e44":"N\u0307","\xd3":"O\u0301","\xd2":"O\u0300","\xd6":"O\u0308","\u022a":"O\u0308\u0304","\xd5":"O\u0303","\u1e4c":"O\u0303\u0301","\u1e4e":"O\u0303\u0308","\u022c":"O\u0303\u0304","\u014c":"O\u0304","\u1e52":"O\u0304\u0301","\u1e50":"O\u0304\u0300","\u014e":"O\u0306","\u01d1":"O\u030c","\xd4":"O\u0302","\u1ed0":"O\u0302\u0301","\u1ed2":"O\u0302\u0300","\u1ed6":"O\u0302\u0303","\u022e":"O\u0307","\u0230":"O\u0307\u0304","\u0150":"O\u030b","\u1e54":"P\u0301","\u1e56":"P\u0307","\u0154":"R\u0301","\u0158":"R\u030c","\u1e58":"R\u0307","\u015a":"S\u0301","\u1e64":"S\u0301\u0307","\u0160":"S\u030c","\u1e66":"S\u030c\u0307","\u015c":"S\u0302","\u1e60":"S\u0307","\u0164":"T\u030c","\u1e6a":"T\u0307","\xda":"U\u0301","\xd9":"U\u0300","\xdc":"U\u0308","\u01d7":"U\u0308\u0301","\u01db":"U\u0308\u0300","\u01d5":"U\u0308\u0304","\u01d9":"U\u0308\u030c","\u0168":"U\u0303","\u1e78":"U\u0303\u0301","\u016a":"U\u0304","\u1e7a":"U\u0304\u0308","\u016c":"U\u0306","\u01d3":"U\u030c","\xdb":"U\u0302","\u016e":"U\u030a","\u0170":"U\u030b","\u1e7c":"V\u0303","\u1e82":"W\u0301","\u1e80":"W\u0300","\u1e84":"W\u0308","\u0174":"W\u0302","\u1e86":"W\u0307","\u1e8c":"X\u0308","\u1e8a":"X\u0307","\xdd":"Y\u0301","\u1ef2":"Y\u0300","\u0178":"Y\u0308","\u1ef8":"Y\u0303","\u0232":"Y\u0304","\u0176":"Y\u0302","\u1e8e":"Y\u0307","\u0179":"Z\u0301","\u017d":"Z\u030c","\u1e90":"Z\u0302","\u017b":"Z\u0307","\u03ac":"\u03b1\u0301","\u1f70":"\u03b1\u0300","\u1fb1":"\u03b1\u0304","\u1fb0":"\u03b1\u0306","\u03ad":"\u03b5\u0301","\u1f72":"\u03b5\u0300","\u03ae":"\u03b7\u0301","\u1f74":"\u03b7\u0300","\u03af":"\u03b9\u0301","\u1f76":"\u03b9\u0300","\u03ca":"\u03b9\u0308","\u0390":"\u03b9\u0308\u0301","\u1fd2":"\u03b9\u0308\u0300","\u1fd1":"\u03b9\u0304","\u1fd0":"\u03b9\u0306","\u03cc":"\u03bf\u0301","\u1f78":"\u03bf\u0300","\u03cd":"\u03c5\u0301","\u1f7a":"\u03c5\u0300","\u03cb":"\u03c5\u0308","\u03b0":"\u03c5\u0308\u0301","\u1fe2":"\u03c5\u0308\u0300","\u1fe1":"\u03c5\u0304","\u1fe0":"\u03c5\u0306","\u03ce":"\u03c9\u0301","\u1f7c":"\u03c9\u0300","\u038e":"\u03a5\u0301","\u1fea":"\u03a5\u0300","\u03ab":"\u03a5\u0308","\u1fe9":"\u03a5\u0304","\u1fe8":"\u03a5\u0306","\u038f":"\u03a9\u0301","\u1ffa":"\u03a9\u0300"},Sn=function(){function a(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new xn(e,t,this.mode),this.settings=t,this.leftrightDepth=0}var e=a.prototype;return e.expect=function(e,t){if(void 0===t&&(t=!0),this.nextToken.text!==e)throw new X("Expected '"+e+"', got '"+this.nextToken.text+"'",this.nextToken);t&&this.consume()},e.consume=function(){this.nextToken=this.gullet.expandNextToken()},e.switchMode=function(e){this.mode=e,this.gullet.switchMode(e)},e.parse=function(){this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor"),this.consume();var e=this.parseExpression(!1);return this.expect("EOF",!1),this.gullet.endGroup(),e},e.parseExpression=function(e,t){for(var r=[];;){"math"===this.mode&&this.consumeSpaces();var n=this.nextToken;if(-1!==a.endOfExpression.indexOf(n.text))break;if(t&&n.text===t)break;if(e&&tn[n.text]&&tn[n.text].infix)break;var i=this.parseAtom(t);if(!i)break;r.push(i)}return"text"===this.mode&&this.formLigatures(r),this.handleInfixNodes(r)},e.handleInfixNodes=function(e){for(var t,r=-1,n=0;n<e.length;n++){var i=Ke(e[n],"infix");if(i){if(-1!==r)throw new X("only one infix operator per group",i.token);r=n,t=i.replaceWith}}if(-1!==r&&t){var a,o,s=e.slice(0,r),l=e.slice(r+1);return a=1===s.length&&"ordgroup"===s[0].type?s[0]:{type:"ordgroup",mode:this.mode,body:s},o=1===l.length&&"ordgroup"===l[0].type?l[0]:{type:"ordgroup",mode:this.mode,body:l},["\\\\abovefrac"===t?this.callFunction(t,[a,e[r],o],[]):this.callFunction(t,[a,o],[])]}return e},e.handleSupSubscript=function(e){var t=this.nextToken,r=t.text;this.consume(),this.consumeSpaces();var n=this.parseGroup(e,!1,a.SUPSUB_GREEDINESS);if(!n)throw new X("Expected group after '"+r+"'",t);return n},e.handleUnsupportedCmd=function(){for(var e=this.nextToken.text,t=[],r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});var n={type:"text",mode:this.mode,body:t},i={type:"color",mode:this.mode,color:this.settings.errorColor,body:[n]};return this.consume(),i},e.parseAtom=function(e){var t,r,n=this.parseGroup("atom",!1,null,e);if("text"===this.mode)return n;for(;;){this.consumeSpaces();var i=this.nextToken;if("\\limits"===i.text||"\\nolimits"===i.text){var a=Ke(n,"op");if(!a)throw new X("Limit controls must follow a math operator",i);var o="\\limits"===i.text;a.limits=o,a.alwaysHandleSupSub=!0,this.consume()}else if("^"===i.text){if(t)throw new X("Double superscript",i);t=this.handleSupSubscript("superscript")}else if("_"===i.text){if(r)throw new X("Double subscript",i);r=this.handleSupSubscript("subscript")}else if("'"===i.text){if(t)throw new X("Double superscript",i);var s={type:"textord",mode:this.mode,text:"\\prime"},l=[s];for(this.consume();"'"===this.nextToken.text;)l.push(s),this.consume();"^"===this.nextToken.text&&l.push(this.handleSupSubscript("superscript")),t={type:"ordgroup",mode:this.mode,body:l}}else{if("%"!==i.text)break;this.consumeComment()}}return t||r?{type:"supsub",mode:this.mode,base:n,sup:t,sub:r}:n},e.parseFunction=function(e,t,r){var n=this.nextToken,i=n.text,a=tn[i];if(!a)return null;if(null!=r&&a.greediness<=r)throw new X("Got function '"+i+"' with no arguments"+(t?" as "+t:""),n);if("text"===this.mode&&!a.allowedInText)throw new X("Can't use function '"+i+"' in text mode",n);if("math"===this.mode&&!1===a.allowedInMath)throw new X("Can't use function '"+i+"' in math mode",n);if(a.consumeMode){var o=this.mode;this.switchMode(a.consumeMode),this.consume(),this.switchMode(o)}else this.consume();var s=this.parseArguments(i,a),l=s.args,h=s.optArgs;return this.callFunction(i,l,h,n,e)},e.callFunction=function(e,t,r,n,i){var a={funcName:e,parser:this,token:n,breakOnTokenText:i},o=tn[e];if(o&&o.handler)return o.handler(a,t,r);throw new X("No function handler for "+e)},e.parseArguments=function(e,t){var r=t.numArgs+t.numOptionalArgs;if(0===r)return{args:[],optArgs:[]};for(var n=t.greediness,i=[],a=[],o=0;o<r;o++){var s=t.argTypes&&t.argTypes[o],l=o<t.numOptionalArgs;0<o&&!l&&this.consumeSpaces(),0!==o||l||"math"!==this.mode||this.consumeSpaces();var h=this.nextToken,m=this.parseGroupOfType("argument to '"+e+"'",s,l,n);if(!m){if(l){a.push(null);continue}throw new X("Expected group after '"+e+"'",h)}(l?a:i).push(m)}return{args:i,optArgs:a}},e.parseGroupOfType=function(e,t,r,n){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseGroup(e,r,n,void 0,t);case"original":case null:case void 0:return this.parseGroup(e,r,n);default:throw new X("Unknown group type as "+e,this.nextToken)}},e.consumeSpaces=function(){for(;" "===this.nextToken.text;)this.consume()},e.consumeComment=function(){for(;"EOF"!==this.nextToken.text&&this.nextToken.loc&&-1===this.nextToken.loc.getSource().indexOf("\n");)this.consume();if("EOF"===this.nextToken.text&&this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)"),"math"===this.mode)this.consumeSpaces();else if(this.nextToken.loc){var e=this.nextToken.loc.getSource();e.indexOf("\n")===e.length-1&&this.consumeSpaces()}},e.parseStringGroup=function(e,t,r){var n=t?"[":"{",i=t?"]":"}",a=this.nextToken;if(a.text!==n){if(t)return null;if(r&&"EOF"!==a.text&&/[^{}[\]]/.test(a.text))return this.consume(),a}var o=this.mode;this.mode="text",this.expect(n);for(var s="",l=this.nextToken,h=0,m=l;r&&0<h||this.nextToken.text!==i;){switch(this.nextToken.text){case"EOF":throw new X("Unexpected end of input in "+e,l.range(m,s));case"%":if(r)break;this.consumeComment();continue;case n:h++;break;case i:h--}s+=(m=this.nextToken).text,this.consume()}return this.mode=o,this.expect(i),l.range(m,s)},e.parseRegexGroup=function(e,t){var r=this.mode;this.mode="text";for(var n=this.nextToken,i=n,a="";"EOF"!==this.nextToken.text&&(e.test(a+this.nextToken.text)||"%"===this.nextToken.text);)"%"!==this.nextToken.text?(a+=(i=this.nextToken).text,this.consume()):this.consumeComment();if(""===a)throw new X("Invalid "+t+": '"+n.text+"'",n);return this.mode=r,n.range(i,a)},e.parseColorGroup=function(e){var t=this.parseStringGroup("color",e);if(!t)return null;var r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new X("Invalid color: '"+t.text+"'",t);var n=r[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}},e.parseSizeGroup=function(e){var t,r=!1;if(!(t=e||"{"===this.nextToken.text?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||0!==t.text.length||(t.text="0pt",r=!0);var n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!n)throw new X("Invalid size: '"+t.text+"'",t);var i,a={number:+(n[1]+n[2]),unit:n[3]};if("string"!=typeof(i=a)&&(i=i.unit),!(i in Re||i in Le||"ex"===i))throw new X("Invalid unit: '"+a.unit+"'",t);return{type:"size",mode:this.mode,value:a,isBlank:r}},e.parseUrlGroup=function(e){var t=this.parseStringGroup("url",e,!0);if(!t)return null;var r=t.text.replace(/\\([#$%&~_^{}])/g,"$1"),n=/^\s*([^\\/#]*?)(?::|&#0*58|&#x0*3a)/i.exec(r);n=null!=n?n[1]:"_relative";var i=this.settings.allowedProtocols;if(!Y.contains(i,"*")&&!Y.contains(i,n))throw new X("Forbidden protocol '"+n+"'",t);return{type:"url",mode:this.mode,url:r}},e.parseGroup=function(e,t,r,n,i){var a,o=this.mode,s=this.nextToken,l=s.text;if(i&&this.switchMode(i),l===(t?"[":"{")){this.gullet.beginGroup(),this.consume();var h=this.parseExpression(!1,t?"]":"}"),m=this.nextToken;return i&&this.switchMode(o),this.gullet.endGroup(),this.expect(t?"]":"}"),{type:"ordgroup",mode:this.mode,loc:p.range(s,m),body:h}}if(t)a=null;else if(null==(a=this.parseFunction(n,e,r)||this.parseSymbol())&&"\\"===l[0]&&!bn.hasOwnProperty(l)){if(this.settings.throwOnError)throw new X("Undefined control sequence: "+l,s);a=this.handleUnsupportedCmd()}return i&&this.switchMode(o),a},e.formLigatures=function(e){for(var t=e.length-1,r=0;r<t;++r){var n=e[r],i=n.text;"-"===i&&"-"===e[r+1].text&&(r+1<t&&"-"===e[r+2].text?(e.splice(r,3,{type:"textord",mode:"text",loc:p.range(n,e[r+2]),text:"---"}),t-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:p.range(n,e[r+1]),text:"--"}),t-=1)),"'"!==i&&"`"!==i||e[r+1].text!==i||(e.splice(r,2,{type:"textord",mode:"text",loc:p.range(n,e[r+1]),text:i+i}),t-=1)}},e.parseSymbol=function(){var e=this.nextToken,t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var r=t.slice(5),n="*"===r.charAt(0);if(n&&(r=r.slice(1)),r.length<2||r.charAt(0)!==r.slice(-1))throw new X("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:r=r.slice(1,-1),star:n}}if("%"===t)return this.consumeComment(),this.parseSymbol();kn.hasOwnProperty(t[0])&&!W[this.mode][t[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=kn[t[0]]+t.substr(1));var i,a=sn.exec(t);if(a&&("i"===(t=t.substring(0,a.index))?t="\u0131":"j"===t&&(t="\u0237")),W[this.mode][t]){this.settings.strict&&"math"===this.mode&&0<=Me.indexOf(t)&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var o,s=W[this.mode][t].group,l=p.range(e);if(U.hasOwnProperty(s)){var h=s;o={type:"atom",mode:this.mode,family:h,loc:l,text:t}}else o={type:s,mode:this.mode,loc:l,text:t};i=o}else{if(!(128<=t.charCodeAt(0)))return null;this.settings.strict&&(w(t.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'" ('+t.charCodeAt(0)+")",e)),i={type:"textord",mode:this.mode,loc:p.range(e),text:t}}if(this.consume(),a)for(var m=0;m<a[0].length;m++){var c=a[0][m];if(!wn[c])throw new X("Unknown accent ' "+c+"'",e);var u=wn[c][this.mode];if(!u)throw new X("Accent "+c+" unsupported in "+this.mode+" mode",e);i={type:"accent",mode:this.mode,loc:p.range(e),label:u,isStretchy:!1,isShifty:!0,base:i}}return i},a}();Sn.endOfExpression=["}","\\end","\\right","&"],Sn.SUPSUB_GREEDINESS=1;var zn=function(e,t){if(!("string"==typeof e||e instanceof String))throw new TypeError("KaTeX can only parse string typed expression");var r=new Sn(e,t);delete r.gullet.macros.current["\\df@tag"];var n=r.parse();if(r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new X("\\tag works only in display equations");r.gullet.feed("\\df@tag"),n=[{type:"tag",mode:"text",body:n,tag:r.parse()}]}return n},Mn=function(e,t,r){t.textContent="";var n=An(e,r).toNode();t.appendChild(n)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&("undefined"!=typeof console&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),Mn=function(){throw new X("KaTeX doesn't work in quirks mode.")});var Tn=function(e,t,r){if(r.throwOnError||!(e instanceof X))throw e;var n=$e.makeSpan(["katex-error"],[new O(t)]);return n.setAttribute("title",e.toString()),n.setAttribute("style","color:"+r.errorColor),n},An=function(t,e){var r=new h(e);try{var n=zn(t,r);return Ot(n,t,r)}catch(e){return Tn(e,t,r)}},Bn={version:"0.10.0",render:Mn,renderToString:function(e,t){return An(e,t).toMarkup()},ParseError:X,__parse:function(e,t){var r=new h(t);return zn(e,r)},__renderToDomTree:An,__renderToHTMLTree:function(t,e){var r,n,i,a=new h(e);try{var o=zn(t,a);return n=wt(o,Et(r=a)),i=$e.makeSpan(["katex"],[n]),r.displayMode?$e.makeSpan(["katex-display"],[i]):i}catch(e){return Tn(e,t,a)}},__setFontMetrics:function(e,t){H[e]=t},__defineSymbol:j,__defineMacro:un,__domTree:{Span:C,Anchor:N,SymbolNode:O,SvgNode:I,PathNode:R,LineNode:L}};t.default=Bn}]).default});