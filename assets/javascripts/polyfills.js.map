{"version": 3, "sources": ["./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/a-function.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/a-possible-prototype.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/an-instance.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/an-object.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/array-includes.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/array-iteration.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/array-species-create.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/bind-context.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/call-with-safe-iteration-closing.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/check-correctness-of-iteration.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/classof-raw.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/classof.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/collection-strong.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/collection-weak.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/collection.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/copy-constructor-properties.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/correct-prototype-getter.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/create-iterator-constructor.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/create-property-descriptor.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/define-iterator.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/descriptors.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/document-create-element.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/enum-bug-keys.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/export.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/fails.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/freezing.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/function-to-string.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/get-built-in.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/get-iterator-method.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/global.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/has.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/hidden-keys.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/hide.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/html.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/ie8-dom-define.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/indexed-object.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/inherit-if-required.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/internal-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/internal-state.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/is-array-iterator-method.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/is-array.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/is-forced.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/is-object.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/is-pure.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/iterate.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/iterators-core.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/iterators.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/native-symbol.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/native-weak-map.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-create.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-define-properties.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-define-property.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-get-own-property-descriptor.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-get-own-property-names.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-get-own-property-symbols.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-get-prototype-of.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-keys-internal.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-keys.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-property-is-enumerable.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/object-set-prototype-of.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/own-keys.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/path.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/redefine-all.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/redefine.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/reflect-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/require-object-coercible.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/set-global.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/set-species.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/set-to-string-tag.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/shared-key.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/shared.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/to-absolute-index.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/to-indexed-object.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/to-integer.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/to-length.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/to-object.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/to-primitive.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/uid.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/internals/well-known-symbol.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/es.map.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/es.set.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/es.weak-map.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.define-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.delete-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.get-metadata-keys.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.get-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.get-own-metadata-keys.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.get-own-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.has-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.has-own-metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/modules/esnext.reflect.metadata.js", "./node_modules/@angular-devkit/build-angular/node_modules/core-js/proposals/reflect-metadata.js", "./node_modules/@angular-devkit/build-angular/src/angular-cli-files/models/jit-polyfills.js", "./node_modules/tslib/tslib.es6.js", "./node_modules/zone.js/dist/zone.js", "./src/polyfills.ts"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;ACJA,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;ACNA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;ACJA,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;ACNA,sBAAsB,mBAAO,CAAC,wIAAgC;AAC9D,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,sBAAsB,mBAAO,CAAC,wIAAgC;;AAE9D,qBAAqB,oBAAoB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,YAAY,eAAe;AAChC;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC/BA,WAAW,mBAAO,CAAC,8HAA2B;AAC9C,oBAAoB,mBAAO,CAAC,kIAA6B;AACzD,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,yBAAyB,mBAAO,CAAC,8IAAmC;;AAEpE;;AAEA,qBAAqB,qDAAqD;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,eAAe;AACzB;AACA;AACA;AACA,2CAA2C;AAC3C;AACA,8BAA8B;AAC9B,+BAA+B;AAC/B,+BAA+B;AAC/B,2CAA2C;AAC3C,SAAS,iCAAiC;AAC1C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AChEA,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,cAAc,mBAAO,CAAC,sHAAuB;AAC7C,sBAAsB,mBAAO,CAAC,wIAAgC;;AAE9D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;ACnBA,gBAAgB,mBAAO,CAAC,0HAAyB;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACZA,sBAAsB,mBAAO,CAAC,wIAAgC;;AAE9D;AACA;;AAEA;AACA;AACA;AACA;AACA,cAAc;AACd,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,SAAS,EAAE;AACzD,CAAC,gBAAgB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;;;;;;;;;;;;ACrCA,iBAAiB;;AAEjB;AACA;AACA;;;;;;;;;;;;ACJA,iBAAiB,mBAAO,CAAC,4HAA0B;AACnD,sBAAsB,mBAAO,CAAC,wIAAgC;;AAE9D;AACA;AACA,gDAAgD,kBAAkB,EAAE;;AAEpE;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACxBa;AACb,qBAAqB,mBAAO,CAAC,kJAAqC;AAClE,aAAa,mBAAO,CAAC,gIAA4B;AACjD,kBAAkB,mBAAO,CAAC,8HAA2B;AACrD,WAAW,mBAAO,CAAC,8HAA2B;AAC9C,iBAAiB,mBAAO,CAAC,4HAA0B;AACnD,cAAc,mBAAO,CAAC,oHAAsB;AAC5C,qBAAqB,mBAAO,CAAC,oIAA8B;AAC3D,iBAAiB,mBAAO,CAAC,4HAA0B;AACnD,kBAAkB,mBAAO,CAAC,4HAA0B;AACpD,cAAc,mBAAO,CAAC,wIAAgC;AACtD,0BAA0B,mBAAO,CAAC,kIAA6B;;AAE/D;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,OAAO;AACtC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA,kCAAkC;AAClC,oCAAoC;AACpC,cAAc;AACd,KAAK;;AAEL;AACA;AACA;AACA;;;;;;;;;;;;;ACzLa;AACb,kBAAkB,mBAAO,CAAC,8HAA2B;AACrD,kBAAkB,mBAAO,CAAC,wIAAgC;AAC1D,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,iBAAiB,mBAAO,CAAC,4HAA0B;AACnD,cAAc,mBAAO,CAAC,oHAAsB;AAC5C,2BAA2B,mBAAO,CAAC,oIAA8B;AACjE,WAAW,mBAAO,CAAC,4GAAkB;AACrC,0BAA0B,mBAAO,CAAC,kIAA6B;;AAE/D;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,KAAK;;AAEL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;;;;;;;;;;;;;ACxHa;AACb,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,eAAe,mBAAO,CAAC,sHAAuB;AAC9C,6BAA6B,mBAAO,CAAC,wIAAgC;AACrE,cAAc,mBAAO,CAAC,oHAAsB;AAC5C,iBAAiB,mBAAO,CAAC,4HAA0B;AACnD,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,YAAY,mBAAO,CAAC,gHAAoB;AACxC,kCAAkC,mBAAO,CAAC,kKAA6C;AACvF,qBAAqB,mBAAO,CAAC,wIAAgC;AAC7D,wBAAwB,mBAAO,CAAC,4IAAkC;;AAElE;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,qDAAqD;AACrD;AACA,kDAAkD,iBAAiB,EAAE;AACrE;AACA;AACA,4EAA4E,iCAAiC,EAAE;AAC/G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA,KAAK,yDAAyD;;AAE9D;;AAEA;;AAEA;AACA;;;;;;;;;;;;AChGA,UAAU,mBAAO,CAAC,4GAAkB;AACpC,cAAc,mBAAO,CAAC,sHAAuB;AAC7C,qCAAqC,mBAAO,CAAC,0KAAiD;AAC9F,2BAA2B,mBAAO,CAAC,kJAAqC;;AAExE;AACA;AACA;AACA;AACA,iBAAiB,iBAAiB;AAClC;AACA;AACA;AACA;;;;;;;;;;;;ACbA,YAAY,mBAAO,CAAC,gHAAoB;;AAExC;AACA,gBAAgB;AAChB;AACA;AACA,CAAC;;;;;;;;;;;;;ACNY;AACb,wBAAwB,mBAAO,CAAC,kIAA6B;AAC7D,aAAa,mBAAO,CAAC,gIAA4B;AACjD,+BAA+B,mBAAO,CAAC,0JAAyC;AAChF,qBAAqB,mBAAO,CAAC,wIAAgC;AAC7D,gBAAgB,mBAAO,CAAC,wHAAwB;;AAEhD,8BAA8B,aAAa;;AAE3C;AACA;AACA,6DAA6D,0CAA0C;AACvG;AACA;AACA;AACA;;;;;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACPa;AACb,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,gCAAgC,mBAAO,CAAC,4JAA0C;AAClF,qBAAqB,mBAAO,CAAC,oJAAsC;AACnE,qBAAqB,mBAAO,CAAC,oJAAsC;AACnE,qBAAqB,mBAAO,CAAC,wIAAgC;AAC7D,WAAW,mBAAO,CAAC,8GAAmB;AACtC,eAAe,mBAAO,CAAC,sHAAuB;AAC9C,sBAAsB,mBAAO,CAAC,wIAAgC;AAC9D,cAAc,mBAAO,CAAC,oHAAsB;AAC5C,gBAAgB,mBAAO,CAAC,wHAAwB;AAChD,oBAAoB,mBAAO,CAAC,kIAA6B;;AAEzD;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;;AAEA;AACA;AACA;AACA;AACA,yCAAyC,4CAA4C;AACrF,6CAA6C,4CAA4C;AACzF,+CAA+C,4CAA4C;AAC3F,KAAK,qBAAqB,sCAAsC;AAChE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,gBAAgB,mBAAmB;AACnC;AACA;AACA,yCAAyC,kCAAkC;AAC3E;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,SAAS,qFAAqF;AACnG;;AAEA;AACA;;;;;;;;;;;;ACzFA,YAAY,mBAAO,CAAC,gHAAoB;;AAExC;AACA;AACA,iCAAiC,QAAQ,mBAAmB,UAAU,EAAE,EAAE;AAC1E,CAAC;;;;;;;;;;;;ACLD,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;ACTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACTA,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,+BAA+B,mBAAO,CAAC,0KAAiD;AACxF,WAAW,mBAAO,CAAC,8GAAmB;AACtC,eAAe,mBAAO,CAAC,sHAAuB;AAC9C,gBAAgB,mBAAO,CAAC,0HAAyB;AACjD,gCAAgC,mBAAO,CAAC,4JAA0C;AAClF,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,mDAAmD;AACnD,GAAG;AACH,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACrDA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;;;;;;;;;;;ACNA,YAAY,mBAAO,CAAC,gHAAoB;;AAExC;AACA,wDAAwD;AACxD,CAAC;;;;;;;;;;;;ACJD,aAAa,mBAAO,CAAC,kHAAqB;;AAE1C;;;;;;;;;;;;ACFA,WAAW,mBAAO,CAAC,8GAAmB;AACtC,aAAa,mBAAO,CAAC,kHAAqB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;ACVA,cAAc,mBAAO,CAAC,oHAAsB;AAC5C,gBAAgB,mBAAO,CAAC,wHAAwB;AAChD,sBAAsB,mBAAO,CAAC,wIAAgC;;AAE9D;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACVA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACbA,uBAAuB;;AAEvB;AACA;AACA;;;;;;;;;;;;ACJA;;;;;;;;;;;;ACAA,kBAAkB,mBAAO,CAAC,4HAA0B;AACpD,2BAA2B,mBAAO,CAAC,kJAAqC;AACxE,+BAA+B,mBAAO,CAAC,0JAAyC;;AAEhF;AACA;AACA,CAAC;AACD;AACA;AACA;;;;;;;;;;;;ACTA,iBAAiB,mBAAO,CAAC,8HAA2B;;AAEpD;;;;;;;;;;;;ACFA,kBAAkB,mBAAO,CAAC,4HAA0B;AACpD,YAAY,mBAAO,CAAC,gHAAoB;AACxC,oBAAoB,mBAAO,CAAC,oJAAsC;;AAElE;AACA;AACA;AACA,sBAAsB,UAAU;AAChC,GAAG;AACH,CAAC;;;;;;;;;;;;ACTD,YAAY,mBAAO,CAAC,gHAAoB;AACxC,cAAc,mBAAO,CAAC,4HAA0B;;AAEhD;;AAEA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;;;;;;;;;;;;ACZD,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,qBAAqB,mBAAO,CAAC,oJAAsC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AChBA,iBAAiB,mBAAO,CAAC,4HAA0B;AACnD,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,UAAU,mBAAO,CAAC,4GAAkB;AACpC,qBAAqB,mBAAO,CAAC,kJAAqC;AAClE,UAAU,mBAAO,CAAC,4GAAkB;AACpC,eAAe,mBAAO,CAAC,sHAAuB;;AAE9C;AACA;;AAEA;AACA;AACA;;AAEA;AACA,gCAAgC;AAChC;AACA,gBAAgB;AAChB,GAAG,EAAE;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;;;;;AC5DA,sBAAsB,mBAAO,CAAC,oIAA8B;AAC5D,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,WAAW,mBAAO,CAAC,8GAAmB;AACtC,gBAAgB,mBAAO,CAAC,4GAAkB;AAC1C,gBAAgB,mBAAO,CAAC,0HAAyB;AACjD,iBAAiB,mBAAO,CAAC,4HAA0B;;AAEnD;AACA;;AAEA;AACA,uCAAuC;AACvC;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC5DA,sBAAsB,mBAAO,CAAC,wIAAgC;AAC9D,gBAAgB,mBAAO,CAAC,wHAAwB;;AAEhD;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;ACTA,cAAc,mBAAO,CAAC,4HAA0B;;AAEhD;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA,YAAY,mBAAO,CAAC,gHAAoB;;AAExC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;;;;;;;;;;;ACpBA;AACA;AACA;;;;;;;;;;;;ACFA;;;;;;;;;;;;ACAA,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,4BAA4B,mBAAO,CAAC,sJAAuC;AAC3E,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,WAAW,mBAAO,CAAC,8HAA2B;AAC9C,wBAAwB,mBAAO,CAAC,4IAAkC;AAClE,mCAAmC,mBAAO,CAAC,sKAA+C;;AAE1F;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,yDAAyD,gBAAgB;AACzE;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;;;;;;;;;;;;;ACzCa;AACb,qBAAqB,mBAAO,CAAC,oJAAsC;AACnE,WAAW,mBAAO,CAAC,8GAAmB;AACtC,UAAU,mBAAO,CAAC,4GAAkB;AACpC,sBAAsB,mBAAO,CAAC,wIAAgC;AAC9D,cAAc,mBAAO,CAAC,oHAAsB;;AAE5C;AACA;;AAEA,8BAA8B,aAAa;;AAE3C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;AClCA;;;;;;;;;;;;ACAA,YAAY,mBAAO,CAAC,gHAAoB;;AAExC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACND,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,6BAA6B,mBAAO,CAAC,0IAAiC;;AAEtE;;AAEA;;;;;;;;;;;;ACLA,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,uBAAuB,mBAAO,CAAC,sJAAuC;AACtE,kBAAkB,mBAAO,CAAC,gIAA4B;AACtD,iBAAiB,mBAAO,CAAC,4HAA0B;AACnD,WAAW,mBAAO,CAAC,8GAAmB;AACtC,4BAA4B,mBAAO,CAAC,oJAAsC;AAC1E,gBAAgB,mBAAO,CAAC,0HAAyB;AACjD;;AAEA;AACA,yBAAyB;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;;AAEA;;;;;;;;;;;;AChDA,kBAAkB,mBAAO,CAAC,4HAA0B;AACpD,2BAA2B,mBAAO,CAAC,kJAAqC;AACxE,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,iBAAiB,mBAAO,CAAC,4HAA0B;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACfA,kBAAkB,mBAAO,CAAC,4HAA0B;AACpD,qBAAqB,mBAAO,CAAC,kIAA6B;AAC1D,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,kBAAkB,mBAAO,CAAC,8HAA2B;;AAErD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;AACA;AACA;;;;;;;;;;;;ACnBA,kBAAkB,mBAAO,CAAC,4HAA0B;AACpD,iCAAiC,mBAAO,CAAC,gKAA4C;AACrF,+BAA+B,mBAAO,CAAC,0JAAyC;AAChF,sBAAsB,mBAAO,CAAC,wIAAgC;AAC9D,kBAAkB,mBAAO,CAAC,8HAA2B;AACrD,UAAU,mBAAO,CAAC,4GAAkB;AACpC,qBAAqB,mBAAO,CAAC,kIAA6B;;AAE1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;;;;;;;;;;;;ACnBA,yBAAyB,mBAAO,CAAC,8IAAmC;AACpE,kBAAkB,mBAAO,CAAC,gIAA4B;;AAEtD;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACTA;;;;;;;;;;;;ACAA,UAAU,mBAAO,CAAC,4GAAkB;AACpC,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,gBAAgB,mBAAO,CAAC,0HAAyB;AACjD,+BAA+B,mBAAO,CAAC,sJAAuC;;AAE9E;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;;;;;;;;;;;AChBA,UAAU,mBAAO,CAAC,4GAAkB;AACpC,sBAAsB,mBAAO,CAAC,wIAAgC;AAC9D,cAAc,mBAAO,CAAC,kIAA6B;AACnD,iBAAiB,mBAAO,CAAC,4HAA0B;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AChBA,yBAAyB,mBAAO,CAAC,8IAAmC;AACpE,kBAAkB,mBAAO,CAAC,gIAA4B;;AAEtD;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACPa;AACb,mCAAmC;AACnC;;AAEA;AACA,gFAAgF,OAAO;;AAEvF;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACZD,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,yBAAyB,mBAAO,CAAC,8IAAmC;;AAEpE;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,gBAAgB;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACvBD,iBAAiB,mBAAO,CAAC,8HAA2B;AACpD,gCAAgC,mBAAO,CAAC,gKAA4C;AACpF,kCAAkC,mBAAO,CAAC,oKAA8C;AACxF,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACVA,iBAAiB,mBAAO,CAAC,kHAAqB;;;;;;;;;;;;ACA9C,eAAe,mBAAO,CAAC,sHAAuB;;AAE9C;AACA;AACA;AACA;;;;;;;;;;;;ACLA,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,WAAW,mBAAO,CAAC,8GAAmB;AACtC,UAAU,mBAAO,CAAC,4GAAkB;AACpC,gBAAgB,mBAAO,CAAC,0HAAyB;AACjD,6BAA6B,mBAAO,CAAC,0IAAiC;AACtE,0BAA0B,mBAAO,CAAC,kIAA6B;;AAE/D;AACA;AACA;;AAEA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;;;;;;;;;;;;ACtCD;AACA,UAAU,mBAAO,CAAC,8GAAmB;AACrC,cAAc,mBAAO,CAAC,wHAAwB;AAC9C,aAAa,mBAAO,CAAC,kHAAqB;;AAE1C;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,0DAA0D,gBAAgB,EAAE;AAC5E;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACtDA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACLA,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,WAAW,mBAAO,CAAC,8GAAmB;;AAEtC;AACA;AACA;AACA,GAAG;AACH;AACA,GAAG;AACH;;;;;;;;;;;;;ACTa;AACb,iBAAiB,mBAAO,CAAC,8HAA2B;AACpD,2BAA2B,mBAAO,CAAC,kJAAqC;AACxE,sBAAsB,mBAAO,CAAC,wIAAgC;AAC9D,kBAAkB,mBAAO,CAAC,4HAA0B;;AAEpD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,wBAAwB,aAAa;AACrC,KAAK;AACL;AACA;;;;;;;;;;;;AClBA,qBAAqB,mBAAO,CAAC,kJAAqC;AAClE,UAAU,mBAAO,CAAC,4GAAkB;AACpC,sBAAsB,mBAAO,CAAC,wIAAgC;;AAE9D;;AAEA;AACA;AACA,uCAAuC,iCAAiC;AACxE;AACA;;;;;;;;;;;;ACVA,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,UAAU,mBAAO,CAAC,4GAAkB;;AAEpC;;AAEA;AACA;AACA;;;;;;;;;;;;ACPA,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,gBAAgB,mBAAO,CAAC,0HAAyB;AACjD,cAAc,mBAAO,CAAC,oHAAsB;;AAE5C;AACA,kDAAkD;;AAElD;AACA,qEAAqE;AACrE,CAAC;AACD;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACbD,gBAAgB,mBAAO,CAAC,0HAAyB;;AAEjD;AACA;;AAEA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;;;;;;;;;;;;ACXA;AACA,oBAAoB,mBAAO,CAAC,kIAA6B;AACzD,6BAA6B,mBAAO,CAAC,sJAAuC;;AAE5E;AACA;AACA;;;;;;;;;;;;ACNA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACPA,gBAAgB,mBAAO,CAAC,0HAAyB;;AAEjD;;AAEA;AACA;AACA;AACA,uEAAuE;AACvE;;;;;;;;;;;;ACRA,6BAA6B,mBAAO,CAAC,sJAAuC;;AAE5E;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACNA,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACbA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;ACLA,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,UAAU,mBAAO,CAAC,4GAAkB;AACpC,oBAAoB,mBAAO,CAAC,gIAA4B;;AAExD;AACA;;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;ACXa;AACb,iBAAiB,mBAAO,CAAC,0HAAyB;AAClD,uBAAuB,mBAAO,CAAC,wIAAgC;;AAE/D;AACA;AACA;AACA,yBAAyB,+DAA+D;AACxF,CAAC;;;;;;;;;;;;;ACRY;AACb,iBAAiB,mBAAO,CAAC,0HAAyB;AAClD,uBAAuB,mBAAO,CAAC,wIAAgC;;AAE/D;AACA;AACA;AACA,yBAAyB,+DAA+D;AACxF,CAAC;;;;;;;;;;;;;ACRY;AACb,aAAa,mBAAO,CAAC,kHAAqB;AAC1C,kBAAkB,mBAAO,CAAC,8HAA2B;AACrD,6BAA6B,mBAAO,CAAC,wIAAgC;AACrE,iBAAiB,mBAAO,CAAC,0HAAyB;AAClD,qBAAqB,mBAAO,CAAC,oIAA8B;AAC3D,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,0BAA0B,mBAAO,CAAC,kIAA6B;AAC/D,sBAAsB,mBAAO,CAAC,oIAA8B;;AAE5D;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH;;;;;;;;;;;;AClEA,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACdD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACpBD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC;AACA,UAAU,mBAAO,CAAC,8GAAmB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,qBAAqB,mBAAO,CAAC,oJAAsC;AACnE,cAAc,mBAAO,CAAC,oHAAsB;;AAE5C;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;AChCD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,qBAAqB,mBAAO,CAAC,oJAAsC;;AAEnE;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACvBD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACdD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACdD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;AAC/C,qBAAqB,mBAAO,CAAC,oJAAsC;;AAEnE;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACtBD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACdD,QAAQ,mBAAO,CAAC,kHAAqB;AACrC,4BAA4B,mBAAO,CAAC,sIAA+B;AACnE,eAAe,mBAAO,CAAC,wHAAwB;;AAE/C;AACA;;AAEA;AACA;AACA,GAAG,gCAAgC;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;;;;;;;;;;;;ACfD,mBAAO,CAAC,8JAA2C;AACnD,mBAAO,CAAC,8JAA2C;AACnD,mBAAO,CAAC,wJAAwC;AAChD,mBAAO,CAAC,kKAA6C;AACrD,mBAAO,CAAC,gKAA4C;AACpD,mBAAO,CAAC,0KAAiD;AACzD,mBAAO,CAAC,wJAAwC;AAChD,mBAAO,CAAC,gKAA4C;AACpD,mBAAO,CAAC,gJAAoC;;;;;;;;;;;;;ACR5C;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAC4C;;;;;;;;;;;;;ACP5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA,+DAA+D;AAC/D;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,gBAAgB,sCAAsC,iBAAiB,EAAE;AACnF,yBAAyB,uDAAuD;AAChF;AACA;;AAEO;AACP;AACA,mBAAmB,sBAAsB;AACzC;AACA;;AAEO;AACP;AACA,gDAAgD,OAAO;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA,4DAA4D,cAAc;AAC1E;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA,4CAA4C,QAAQ;AACpD;AACA;;AAEO;AACP,mCAAmC,oCAAoC;AACvE;;AAEO;AACP;AACA;;AAEO;AACP;AACA,mCAAmC,MAAM,6BAA6B,EAAE,YAAY,WAAW,EAAE;AACjG,kCAAkC,MAAM,iCAAiC,EAAE,YAAY,WAAW,EAAE;AACpG,+BAA+B,iEAAiE,uBAAuB,EAAE,4BAA4B;AACrJ;AACA,KAAK;AACL;;AAEO;AACP,aAAa,6BAA6B,0BAA0B,aAAa,EAAE,qBAAqB;AACxG,gBAAgB,qDAAqD,oEAAoE,aAAa,EAAE;AACxJ,sBAAsB,sBAAsB,qBAAqB,GAAG;AACpE;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,kCAAkC,SAAS;AAC3C,kCAAkC,WAAW,UAAU;AACvD,yCAAyC,cAAc;AACvD;AACA,6GAA6G,OAAO,UAAU;AAC9H,gFAAgF,iBAAiB,OAAO;AACxG,wDAAwD,gBAAgB,QAAQ,OAAO;AACvF,8CAA8C,gBAAgB,gBAAgB,OAAO;AACrF;AACA,iCAAiC;AACjC;AACA;AACA,SAAS,YAAY,aAAa,OAAO,EAAE,UAAU,WAAW;AAChE,mCAAmC,SAAS;AAC5C;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;;AAEO;AACP,4BAA4B,sBAAsB;AAClD;AACA;AACA;;AAEO;AACP,iDAAiD,QAAQ;AACzD,wCAAwC,QAAQ;AAChD,wDAAwD,QAAQ;AAChE;AACA;AACA;;AAEO;AACP;AACA;;AAEO;AACP;AACA;AACA,iBAAiB,sFAAsF,aAAa,EAAE;AACtH,sBAAsB,gCAAgC,qCAAqC,0CAA0C,EAAE,EAAE,GAAG;AAC5I,2BAA2B,MAAM,eAAe,EAAE,YAAY,oBAAoB,EAAE;AACpF,sBAAsB,oGAAoG;AAC1H,6BAA6B,uBAAuB;AACpD,4BAA4B,wBAAwB;AACpD,2BAA2B,yDAAyD;AACpF;;AAEO;AACP;AACA,iBAAiB,4CAA4C,SAAS,EAAE,qDAAqD,aAAa,EAAE;AAC5I,yBAAyB,6BAA6B,oBAAoB,gDAAgD,gBAAgB,EAAE,KAAK;AACjJ;;AAEO;AACP;AACA;AACA,2GAA2G,sFAAsF,aAAa,EAAE;AAChN,sBAAsB,8BAA8B,gDAAgD,uDAAuD,EAAE,EAAE,GAAG;AAClK,4CAA4C,sCAAsC,UAAU,oBAAoB,EAAE,EAAE,UAAU;AAC9H;;AAEO;AACP,gCAAgC,uCAAuC,aAAa,EAAE,EAAE,OAAO,kBAAkB;AACjH;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP,4CAA4C;AAC5C;;;;;;;;;;;;ACnMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,KAA4D;AAC7D,CAAC,SACW;AACZ,CAAC,qBAAqB;;AAEtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,kBAAkB;AACzD,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,0BAA0B;AACrD;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,iEAAiE,+CAA+C,EAAE;AAClH;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT,4DAA4D,0CAA0C;AACtG;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA,uCAAuC,0BAA0B,EAAE;AACnE;AACA;AACA;AACA,wCAAwC,6DAA6D,EAAE;AACvG,uCAAuC,WAAW,EAAE;AACpD;AACA,kCAAkC,aAAa,EAAE;AACjD,oCAAoC,WAAW,EAAE;AACjD,gCAAgC,aAAa,EAAE;AAC/C,qCAAqC,aAAa,EAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,0CAA0C,aAAa,EAAE;AACzD,iCAAiC,cAAc,EAAE;AACjD,uCAAuC,kBAAkB,EAAE;AAC3D,2CAA2C,aAAa,EAAE;AAC1D,qDAAqD,kBAAkB,EAAE;AACzE,mCAAmC,kBAAkB,EAAE;AACvD,iCAAiC,WAAW,EAAE;AAC9C,iCAAiC,aAAa,EAAE;AAChD,0CAA0C,aAAa,EAAE;AACzD,uCAAuC,WAAW,EAAE;AACpD,4CAA4C,aAAa,EAAE;AAC3D,wCAAwC,aAAa,EAAE;AACvD,qCAAqC,aAAa;AAClD;AACA,6BAA6B;AAC7B;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4HAA4H,wBAAwB,oCAAoC;AACxL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gFAAgF,sEAAsE;AACtJ;AACA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,oDAAoD;AAC5F;AACA;AACA;AACA;AACA;AACA,2BAA2B,mCAAmC;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,qEAAqE,gBAAgB;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF,kBAAkB;AACrG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,QAAQ,gBAAgB;AACnD;AACA;AACA;AACA;AACA,yBAAyB,0BAA0B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,mFAAmF,kBAAkB;AACrG;AACA;AACA;AACA;AACA,2BAA2B,QAAQ,gBAAgB;AACnD;AACA;AACA;AACA;AACA,yBAAyB,0BAA0B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,QAAQ;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,mBAAmB,oBAAoB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,uBAAuB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,yBAAyB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,EAAE;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA,iBAAiB;AACjB;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA,SAAS;AACT,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gEAAgE;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,EAAE;AACR;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,oCAAoC;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,sBAAsB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,sBAAsB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,0BAA0B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,sBAAsB;AAChE,qCAAqC,iBAAiB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,8BAA8B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,0BAA0B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,0BAA0B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,kBAAkB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,iBAAiB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,wBAAwB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC,wBAAwB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,kBAAkB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0FAA0F;AAC1F;AACA;AACA;AACA;AACA;AACA,UAAU,EAAE;AACZ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,0BAA0B,EAAE;AAClF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,6BAA6B,EAAE;AACpF;AACA;AACA;AACA;AACA,8CAA8C,kDAAkD,EAAE;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6CAA6C,sDAAsD;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,+BAA+B,EAAE;AACtF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE,gBAAgB;AACrF;AACA,mBAAmB,uBAAuB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,0BAA0B;AAC7C;AACA;AACA,uBAAuB,uBAAuB;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA,6CAA6C,6BAA6B;AAC1E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,8FAA8F;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,SAAS;AACT;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,mBAAmB,uBAAuB;AAC1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,EAAE;AACZ;AACA,6DAA6D;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,EAAE;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,uBAAuB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,mBAAmB,4BAA4B;AAC/C;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,sBAAsB;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF;AACnF;AACA;AACA;AACA,UAAU,EAAE;AACZ;AACA;AACA;AACA,mFAAmF;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,EAAE;AACZ,qFAAqF;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,EAAE;AACZ;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kEAAkE,0CAA0C;AAC5G;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,CAAC;;;;;;;;;;;;;ACl5GD;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;GAcG;;AAEH;;GAEG;AAEH,+EAA+E;AAC/E,oEAAoE;AAEpE;;;;GAIG;AACH,8EAA8E;AAE9E;;;;;;;;;;;;;;;;;;;;;;GAsBG;AAEH;;GAEG;AACwB,CAAE,6BAA6B;AAG1D;;GAEG", "file": "polyfills.js", "sourcesContent": ["module.exports = function (it) {\n  if (typeof it != 'function') {\n    throw TypeError(String(it) + ' is not a function');\n  } return it;\n};\n", "var isObject = require('../internals/is-object');\n\nmodule.exports = function (it) {\n  if (!isObject(it) && it !== null) {\n    throw TypeError(\"Can't set \" + String(it) + ' as a prototype');\n  } return it;\n};\n", "module.exports = function (it, Constructor, name) {\n  if (!(it instanceof Constructor)) {\n    throw TypeError('Incorrect ' + (name ? name + ' ' : '') + 'invocation');\n  } return it;\n};\n", "var isObject = require('../internals/is-object');\n\nmodule.exports = function (it) {\n  if (!isObject(it)) {\n    throw TypeError(String(it) + ' is not an object');\n  } return it;\n};\n", "var toIndexedObject = require('../internals/to-indexed-object');\nvar toLength = require('../internals/to-length');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = toLength(O.length);\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare\n    if (IS_INCLUDES && el != el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare\n      if (value != value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n", "var bind = require('../internals/bind-context');\nvar IndexedObject = require('../internals/indexed-object');\nvar toObject = require('../internals/to-object');\nvar toLength = require('../internals/to-length');\nvar arraySpeciesCreate = require('../internals/array-species-create');\n\nvar push = [].push;\n\n// `Array.prototype.{ forEach, map, filter, some, every, find, findIndex }` methods implementation\nvar createMethod = function (TYPE) {\n  var IS_MAP = TYPE == 1;\n  var IS_FILTER = TYPE == 2;\n  var IS_SOME = TYPE == 3;\n  var IS_EVERY = TYPE == 4;\n  var IS_FIND_INDEX = TYPE == 6;\n  var NO_HOLES = TYPE == 5 || IS_FIND_INDEX;\n  return function ($this, callbackfn, that, specificCreate) {\n    var O = toObject($this);\n    var self = IndexedObject(O);\n    var boundFunction = bind(callbackfn, that, 3);\n    var length = toLength(self.length);\n    var index = 0;\n    var create = specificCreate || arraySpeciesCreate;\n    var target = IS_MAP ? create($this, length) : IS_FILTER ? create($this, 0) : undefined;\n    var value, result;\n    for (;length > index; index++) if (NO_HOLES || index in self) {\n      value = self[index];\n      result = boundFunction(value, index, O);\n      if (TYPE) {\n        if (IS_MAP) target[index] = result; // map\n        else if (result) switch (TYPE) {\n          case 3: return true;              // some\n          case 5: return value;             // find\n          case 6: return index;             // findIndex\n          case 2: push.call(target, value); // filter\n        } else if (IS_EVERY) return false;  // every\n      }\n    }\n    return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.forEach` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.foreach\n  forEach: createMethod(0),\n  // `Array.prototype.map` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.map\n  map: createMethod(1),\n  // `Array.prototype.filter` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.filter\n  filter: createMethod(2),\n  // `Array.prototype.some` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.some\n  some: createMethod(3),\n  // `Array.prototype.every` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.every\n  every: createMethod(4),\n  // `Array.prototype.find` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.find\n  find: createMethod(5),\n  // `Array.prototype.findIndex` method\n  // https://tc39.github.io/ecma262/#sec-array.prototype.findIndex\n  findIndex: createMethod(6)\n};\n", "var isObject = require('../internals/is-object');\nvar isArray = require('../internals/is-array');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar SPECIES = wellKnownSymbol('species');\n\n// `ArraySpeciesCreate` abstract operation\n// https://tc39.github.io/ecma262/#sec-arrayspeciescreate\nmodule.exports = function (originalArray, length) {\n  var C;\n  if (isArray(originalArray)) {\n    C = originalArray.constructor;\n    // cross-realm fallback\n    if (typeof C == 'function' && (C === Array || isArray(C.prototype))) C = undefined;\n    else if (isObject(C)) {\n      C = C[SPECIES];\n      if (C === null) C = undefined;\n    }\n  } return new (C === undefined ? Array : C)(length === 0 ? 0 : length);\n};\n", "var aFunction = require('../internals/a-function');\n\n// optional / simple context binding\nmodule.exports = function (fn, that, length) {\n  aFunction(fn);\n  if (that === undefined) return fn;\n  switch (length) {\n    case 0: return function () {\n      return fn.call(that);\n    };\n    case 1: return function (a) {\n      return fn.call(that, a);\n    };\n    case 2: return function (a, b) {\n      return fn.call(that, a, b);\n    };\n    case 3: return function (a, b, c) {\n      return fn.call(that, a, b, c);\n    };\n  }\n  return function (/* ...args */) {\n    return fn.apply(that, arguments);\n  };\n};\n", "var anObject = require('../internals/an-object');\n\n// call something on iterator step with safe closing on error\nmodule.exports = function (iterator, fn, value, ENTRIES) {\n  try {\n    return ENTRIES ? fn(anObject(value)[0], value[1]) : fn(value);\n  // 7.4.6 IteratorClose(iterator, completion)\n  } catch (error) {\n    var returnMethod = iterator['return'];\n    if (returnMethod !== undefined) anObject(returnMethod.call(iterator));\n    throw error;\n  }\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar SAFE_CLOSING = false;\n\ntry {\n  var called = 0;\n  var iteratorWithReturn = {\n    next: function () {\n      return { done: !!called++ };\n    },\n    'return': function () {\n      SAFE_CLOSING = true;\n    }\n  };\n  iteratorWithReturn[ITERATOR] = function () {\n    return this;\n  };\n  // eslint-disable-next-line no-throw-literal\n  Array.from(iteratorWithReturn, function () { throw 2; });\n} catch (error) { /* empty */ }\n\nmodule.exports = function (exec, SKIP_CLOSING) {\n  if (!SKIP_CLOSING && !SAFE_CLOSING) return false;\n  var ITERATION_SUPPORT = false;\n  try {\n    var object = {};\n    object[ITERATOR] = function () {\n      return {\n        next: function () {\n          return { done: ITERATION_SUPPORT = true };\n        }\n      };\n    };\n    exec(object);\n  } catch (error) { /* empty */ }\n  return ITERATION_SUPPORT;\n};\n", "var toString = {}.toString;\n\nmodule.exports = function (it) {\n  return toString.call(it).slice(8, -1);\n};\n", "var classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) == 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : result;\n};\n", "'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\nvar create = require('../internals/object-create');\nvar redefineAll = require('../internals/redefine-all');\nvar bind = require('../internals/bind-context');\nvar anInstance = require('../internals/an-instance');\nvar iterate = require('../internals/iterate');\nvar defineIterator = require('../internals/define-iterator');\nvar setSpecies = require('../internals/set-species');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fastKey = require('../internals/internal-metadata').fastKey;\nvar InternalStateModule = require('../internals/internal-state');\n\nvar setInternalState = InternalStateModule.set;\nvar internalStateGetterFor = InternalStateModule.getterFor;\n\nmodule.exports = {\n  getConstructor: function (wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER) {\n    var C = wrapper(function (that, iterable) {\n      anInstance(that, C, CONSTRUCTOR_NAME);\n      setInternalState(that, {\n        type: CONSTRUCTOR_NAME,\n        index: create(null),\n        first: undefined,\n        last: undefined,\n        size: 0\n      });\n      if (!DESCRIPTORS) that.size = 0;\n      if (iterable != undefined) iterate(iterable, that[ADDER], that, IS_MAP);\n    });\n\n    var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);\n\n    var define = function (that, key, value) {\n      var state = getInternalState(that);\n      var entry = getEntry(that, key);\n      var previous, index;\n      // change existing entry\n      if (entry) {\n        entry.value = value;\n      // create new entry\n      } else {\n        state.last = entry = {\n          index: index = fastKey(key, true),\n          key: key,\n          value: value,\n          previous: previous = state.last,\n          next: undefined,\n          removed: false\n        };\n        if (!state.first) state.first = entry;\n        if (previous) previous.next = entry;\n        if (DESCRIPTORS) state.size++;\n        else that.size++;\n        // add to index\n        if (index !== 'F') state.index[index] = entry;\n      } return that;\n    };\n\n    var getEntry = function (that, key) {\n      var state = getInternalState(that);\n      // fast case\n      var index = fastKey(key);\n      var entry;\n      if (index !== 'F') return state.index[index];\n      // frozen object case\n      for (entry = state.first; entry; entry = entry.next) {\n        if (entry.key == key) return entry;\n      }\n    };\n\n    redefineAll(C.prototype, {\n      // ******** Map.prototype.clear()\n      // ******** Set.prototype.clear()\n      clear: function clear() {\n        var that = this;\n        var state = getInternalState(that);\n        var data = state.index;\n        var entry = state.first;\n        while (entry) {\n          entry.removed = true;\n          if (entry.previous) entry.previous = entry.previous.next = undefined;\n          delete data[entry.index];\n          entry = entry.next;\n        }\n        state.first = state.last = undefined;\n        if (DESCRIPTORS) state.size = 0;\n        else that.size = 0;\n      },\n      // ******** Map.prototype.delete(key)\n      // ******** Set.prototype.delete(value)\n      'delete': function (key) {\n        var that = this;\n        var state = getInternalState(that);\n        var entry = getEntry(that, key);\n        if (entry) {\n          var next = entry.next;\n          var prev = entry.previous;\n          delete state.index[entry.index];\n          entry.removed = true;\n          if (prev) prev.next = next;\n          if (next) next.previous = prev;\n          if (state.first == entry) state.first = next;\n          if (state.last == entry) state.last = prev;\n          if (DESCRIPTORS) state.size--;\n          else that.size--;\n        } return !!entry;\n      },\n      // ******** Set.prototype.forEach(callbackfn, thisArg = undefined)\n      // ******** Map.prototype.forEach(callbackfn, thisArg = undefined)\n      forEach: function forEach(callbackfn /* , that = undefined */) {\n        var state = getInternalState(this);\n        var boundFunction = bind(callbackfn, arguments.length > 1 ? arguments[1] : undefined, 3);\n        var entry;\n        while (entry = entry ? entry.next : state.first) {\n          boundFunction(entry.value, entry.key, this);\n          // revert to the last existing entry\n          while (entry && entry.removed) entry = entry.previous;\n        }\n      },\n      // ******** Map.prototype.has(key)\n      // ******** Set.prototype.has(value)\n      has: function has(key) {\n        return !!getEntry(this, key);\n      }\n    });\n\n    redefineAll(C.prototype, IS_MAP ? {\n      // ******** Map.prototype.get(key)\n      get: function get(key) {\n        var entry = getEntry(this, key);\n        return entry && entry.value;\n      },\n      // ******** Map.prototype.set(key, value)\n      set: function set(key, value) {\n        return define(this, key === 0 ? 0 : key, value);\n      }\n    } : {\n      // ******** Set.prototype.add(value)\n      add: function add(value) {\n        return define(this, value = value === 0 ? 0 : value, value);\n      }\n    });\n    if (DESCRIPTORS) defineProperty(C.prototype, 'size', {\n      get: function () {\n        return getInternalState(this).size;\n      }\n    });\n    return C;\n  },\n  setStrong: function (C, CONSTRUCTOR_NAME, IS_MAP) {\n    var ITERATOR_NAME = CONSTRUCTOR_NAME + ' Iterator';\n    var getInternalCollectionState = internalStateGetterFor(CONSTRUCTOR_NAME);\n    var getInternalIteratorState = internalStateGetterFor(ITERATOR_NAME);\n    // add .keys, .values, .entries, [@@iterator]\n    // ********, ********, *********, *********, ********, ********, *********, *********\n    defineIterator(C, CONSTRUCTOR_NAME, function (iterated, kind) {\n      setInternalState(this, {\n        type: ITERATOR_NAME,\n        target: iterated,\n        state: getInternalCollectionState(iterated),\n        kind: kind,\n        last: undefined\n      });\n    }, function () {\n      var state = getInternalIteratorState(this);\n      var kind = state.kind;\n      var entry = state.last;\n      // revert to the last existing entry\n      while (entry && entry.removed) entry = entry.previous;\n      // get next entry\n      if (!state.target || !(state.last = entry = entry ? entry.next : state.state.first)) {\n        // or finish the iteration\n        state.target = undefined;\n        return { value: undefined, done: true };\n      }\n      // return step by kind\n      if (kind == 'keys') return { value: entry.key, done: false };\n      if (kind == 'values') return { value: entry.value, done: false };\n      return { value: [entry.key, entry.value], done: false };\n    }, IS_MAP ? 'entries' : 'values', !IS_MAP, true);\n\n    // add [@@species], 23.1.2.2, 23.2.2.2\n    setSpecies(CONSTRUCTOR_NAME);\n  }\n};\n", "'use strict';\nvar redefineAll = require('../internals/redefine-all');\nvar getWeakData = require('../internals/internal-metadata').getWeakData;\nvar anObject = require('../internals/an-object');\nvar isObject = require('../internals/is-object');\nvar anInstance = require('../internals/an-instance');\nvar iterate = require('../internals/iterate');\nvar ArrayIterationModule = require('../internals/array-iteration');\nvar $has = require('../internals/has');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar setInternalState = InternalStateModule.set;\nvar internalStateGetterFor = InternalStateModule.getterFor;\nvar find = ArrayIterationModule.find;\nvar findIndex = ArrayIterationModule.findIndex;\nvar id = 0;\n\n// fallback for uncaught frozen keys\nvar uncaughtFrozenStore = function (store) {\n  return store.frozen || (store.frozen = new UncaughtFrozenStore());\n};\n\nvar UncaughtFrozenStore = function () {\n  this.entries = [];\n};\n\nvar findUncaughtFrozen = function (store, key) {\n  return find(store.entries, function (it) {\n    return it[0] === key;\n  });\n};\n\nUncaughtFrozenStore.prototype = {\n  get: function (key) {\n    var entry = findUncaughtFrozen(this, key);\n    if (entry) return entry[1];\n  },\n  has: function (key) {\n    return !!findUncaughtFrozen(this, key);\n  },\n  set: function (key, value) {\n    var entry = findUncaughtFrozen(this, key);\n    if (entry) entry[1] = value;\n    else this.entries.push([key, value]);\n  },\n  'delete': function (key) {\n    var index = findIndex(this.entries, function (it) {\n      return it[0] === key;\n    });\n    if (~index) this.entries.splice(index, 1);\n    return !!~index;\n  }\n};\n\nmodule.exports = {\n  getConstructor: function (wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER) {\n    var C = wrapper(function (that, iterable) {\n      anInstance(that, C, CONSTRUCTOR_NAME);\n      setInternalState(that, {\n        type: CONSTRUCTOR_NAME,\n        id: id++,\n        frozen: undefined\n      });\n      if (iterable != undefined) iterate(iterable, that[ADDER], that, IS_MAP);\n    });\n\n    var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);\n\n    var define = function (that, key, value) {\n      var state = getInternalState(that);\n      var data = getWeakData(anObject(key), true);\n      if (data === true) uncaughtFrozenStore(state).set(key, value);\n      else data[state.id] = value;\n      return that;\n    };\n\n    redefineAll(C.prototype, {\n      // 23.3.3.2 WeakMap.prototype.delete(key)\n      // 23.4.3.3 WeakSet.prototype.delete(value)\n      'delete': function (key) {\n        var state = getInternalState(this);\n        if (!isObject(key)) return false;\n        var data = getWeakData(key);\n        if (data === true) return uncaughtFrozenStore(state)['delete'](key);\n        return data && $has(data, state.id) && delete data[state.id];\n      },\n      // 23.3.3.4 WeakMap.prototype.has(key)\n      // 23.4.3.4 WeakSet.prototype.has(value)\n      has: function has(key) {\n        var state = getInternalState(this);\n        if (!isObject(key)) return false;\n        var data = getWeakData(key);\n        if (data === true) return uncaughtFrozenStore(state).has(key);\n        return data && $has(data, state.id);\n      }\n    });\n\n    redefineAll(C.prototype, IS_MAP ? {\n      // 23.3.3.3 WeakMap.prototype.get(key)\n      get: function get(key) {\n        var state = getInternalState(this);\n        if (isObject(key)) {\n          var data = getWeakData(key);\n          if (data === true) return uncaughtFrozenStore(state).get(key);\n          return data ? data[state.id] : undefined;\n        }\n      },\n      // 23.3.3.5 WeakMap.prototype.set(key, value)\n      set: function set(key, value) {\n        return define(this, key, value);\n      }\n    } : {\n      // 23.4.3.1 WeakSet.prototype.add(value)\n      add: function add(value) {\n        return define(this, value, true);\n      }\n    });\n\n    return C;\n  }\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar isForced = require('../internals/is-forced');\nvar redefine = require('../internals/redefine');\nvar InternalMetadataModule = require('../internals/internal-metadata');\nvar iterate = require('../internals/iterate');\nvar anInstance = require('../internals/an-instance');\nvar isObject = require('../internals/is-object');\nvar fails = require('../internals/fails');\nvar checkCorrectnessOfIteration = require('../internals/check-correctness-of-iteration');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar inheritIfRequired = require('../internals/inherit-if-required');\n\nmodule.exports = function (CONSTRUCTOR_NAME, wrapper, common, IS_MAP, IS_WEAK) {\n  var NativeConstructor = global[CONSTRUCTOR_NAME];\n  var NativePrototype = NativeConstructor && NativeConstructor.prototype;\n  var Constructor = NativeConstructor;\n  var ADDER = IS_MAP ? 'set' : 'add';\n  var exported = {};\n\n  var fixMethod = function (KEY) {\n    var nativeMethod = NativePrototype[KEY];\n    redefine(NativePrototype, KEY,\n      KEY == 'add' ? function add(value) {\n        nativeMethod.call(this, value === 0 ? 0 : value);\n        return this;\n      } : KEY == 'delete' ? function (key) {\n        return IS_WEAK && !isObject(key) ? false : nativeMethod.call(this, key === 0 ? 0 : key);\n      } : KEY == 'get' ? function get(key) {\n        return IS_WEAK && !isObject(key) ? undefined : nativeMethod.call(this, key === 0 ? 0 : key);\n      } : KEY == 'has' ? function has(key) {\n        return IS_WEAK && !isObject(key) ? false : nativeMethod.call(this, key === 0 ? 0 : key);\n      } : function set(key, value) {\n        nativeMethod.call(this, key === 0 ? 0 : key, value);\n        return this;\n      }\n    );\n  };\n\n  // eslint-disable-next-line max-len\n  if (isForced(CONSTRUCTOR_NAME, typeof NativeConstructor != 'function' || !(IS_WEAK || NativePrototype.forEach && !fails(function () {\n    new NativeConstructor().entries().next();\n  })))) {\n    // create collection constructor\n    Constructor = common.getConstructor(wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER);\n    InternalMetadataModule.REQUIRED = true;\n  } else if (isForced(CONSTRUCTOR_NAME, true)) {\n    var instance = new Constructor();\n    // early implementations not supports chaining\n    var HASNT_CHAINING = instance[ADDER](IS_WEAK ? {} : -0, 1) != instance;\n    // V8 ~ Chromium 40- weak-collections throws on primitives, but should return false\n    var THROWS_ON_PRIMITIVES = fails(function () { instance.has(1); });\n    // most early implementations doesn't supports iterables, most modern - not close it correctly\n    // eslint-disable-next-line no-new\n    var ACCEPT_ITERABLES = checkCorrectnessOfIteration(function (iterable) { new NativeConstructor(iterable); });\n    // for early implementations -0 and +0 not the same\n    var BUGGY_ZERO = !IS_WEAK && fails(function () {\n      // V8 ~ Chromium 42- fails only with 5+ elements\n      var $instance = new NativeConstructor();\n      var index = 5;\n      while (index--) $instance[ADDER](index, index);\n      return !$instance.has(-0);\n    });\n\n    if (!ACCEPT_ITERABLES) {\n      Constructor = wrapper(function (dummy, iterable) {\n        anInstance(dummy, Constructor, CONSTRUCTOR_NAME);\n        var that = inheritIfRequired(new NativeConstructor(), dummy, Constructor);\n        if (iterable != undefined) iterate(iterable, that[ADDER], that, IS_MAP);\n        return that;\n      });\n      Constructor.prototype = NativePrototype;\n      NativePrototype.constructor = Constructor;\n    }\n\n    if (THROWS_ON_PRIMITIVES || BUGGY_ZERO) {\n      fixMethod('delete');\n      fixMethod('has');\n      IS_MAP && fixMethod('get');\n    }\n\n    if (BUGGY_ZERO || HASNT_CHAINING) fixMethod(ADDER);\n\n    // weak collections should not contains .clear method\n    if (IS_WEAK && NativePrototype.clear) delete NativePrototype.clear;\n  }\n\n  exported[CONSTRUCTOR_NAME] = Constructor;\n  $({ global: true, forced: Constructor != NativeConstructor }, exported);\n\n  setToStringTag(Constructor, CONSTRUCTOR_NAME);\n\n  if (!IS_WEAK) common.setStrong(Constructor, CONSTRUCTOR_NAME, IS_MAP);\n\n  return Constructor;\n};\n", "var has = require('../internals/has');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!has(target, key)) defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n  }\n};\n", "var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  function F() { /* empty */ }\n  F.prototype.constructor = null;\n  return Object.getPrototypeOf(new F()) !== F.prototype;\n});\n", "'use strict';\nvar IteratorPrototype = require('../internals/iterators-core').IteratorPrototype;\nvar create = require('../internals/object-create');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar Iterators = require('../internals/iterators');\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (IteratorConstructor, NAME, next) {\n  var TO_STRING_TAG = NAME + ' Iterator';\n  IteratorConstructor.prototype = create(IteratorPrototype, { next: createPropertyDescriptor(1, next) });\n  setToStringTag(IteratorConstructor, TO_STRING_TAG, false, true);\n  Iterators[TO_STRING_TAG] = returnThis;\n  return IteratorConstructor;\n};\n", "module.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar createIteratorConstructor = require('../internals/create-iterator-constructor');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar setToStringTag = require('../internals/set-to-string-tag');\nvar hide = require('../internals/hide');\nvar redefine = require('../internals/redefine');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\nvar Iterators = require('../internals/iterators');\nvar IteratorsCore = require('../internals/iterators-core');\n\nvar IteratorPrototype = IteratorsCore.IteratorPrototype;\nvar BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;\nvar ITERATOR = wellKnownSymbol('iterator');\nvar KEYS = 'keys';\nvar VALUES = 'values';\nvar ENTRIES = 'entries';\n\nvar returnThis = function () { return this; };\n\nmodule.exports = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {\n  createIteratorConstructor(IteratorConstructor, NAME, next);\n\n  var getIterationMethod = function (KIND) {\n    if (KIND === DEFAULT && defaultIterator) return defaultIterator;\n    if (!BUGGY_SAFARI_ITERATORS && KIND in IterablePrototype) return IterablePrototype[KIND];\n    switch (KIND) {\n      case KEYS: return function keys() { return new IteratorConstructor(this, KIND); };\n      case VALUES: return function values() { return new IteratorConstructor(this, KIND); };\n      case ENTRIES: return function entries() { return new IteratorConstructor(this, KIND); };\n    } return function () { return new IteratorConstructor(this); };\n  };\n\n  var TO_STRING_TAG = NAME + ' Iterator';\n  var INCORRECT_VALUES_NAME = false;\n  var IterablePrototype = Iterable.prototype;\n  var nativeIterator = IterablePrototype[ITERATOR]\n    || IterablePrototype['@@iterator']\n    || DEFAULT && IterablePrototype[DEFAULT];\n  var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);\n  var anyNativeIterator = NAME == 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;\n  var CurrentIteratorPrototype, methods, KEY;\n\n  // fix native\n  if (anyNativeIterator) {\n    CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));\n    if (IteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {\n      if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {\n        if (setPrototypeOf) {\n          setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);\n        } else if (typeof CurrentIteratorPrototype[ITERATOR] != 'function') {\n          hide(CurrentIteratorPrototype, ITERATOR, returnThis);\n        }\n      }\n      // Set @@toStringTag to native iterators\n      setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true, true);\n      if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;\n    }\n  }\n\n  // fix Array#{values, @@iterator}.name in V8 / FF\n  if (DEFAULT == VALUES && nativeIterator && nativeIterator.name !== VALUES) {\n    INCORRECT_VALUES_NAME = true;\n    defaultIterator = function values() { return nativeIterator.call(this); };\n  }\n\n  // define iterator\n  if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {\n    hide(IterablePrototype, ITERATOR, defaultIterator);\n  }\n  Iterators[NAME] = defaultIterator;\n\n  // export additional methods\n  if (DEFAULT) {\n    methods = {\n      values: getIterationMethod(VALUES),\n      keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),\n      entries: getIterationMethod(ENTRIES)\n    };\n    if (FORCED) for (KEY in methods) {\n      if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {\n        redefine(IterablePrototype, KEY, methods[KEY]);\n      }\n    } else $({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);\n  }\n\n  return methods;\n};\n", "var fails = require('../internals/fails');\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !fails(function () {\n  return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;\n});\n", "var global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n", "// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "var global = require('../internals/global');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar hide = require('../internals/hide');\nvar redefine = require('../internals/redefine');\nvar setGlobal = require('../internals/set-global');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target      - name of the target object\n  options.global      - target is the global object\n  options.stat        - export as static methods of target\n  options.proto       - export as prototype methods of target\n  options.real        - real prototype method for the `pure` version\n  options.forced      - export even if the native feature is available\n  options.bind        - bind methods to the target, required for the `pure` version\n  options.wrap        - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe      - use the simple assignment of property instead of delete + defineProperty\n  options.sham        - add a flag to not completely full polyfills\n  options.enumerable  - export as enumerable property\n  options.noTargetGet - prevent calling a getter on target\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || setGlobal(TARGET, {});\n  } else {\n    target = (global[TARGET] || {}).prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.noTargetGet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty === typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      hide(sourceProperty, 'sham', true);\n    }\n    // extend global\n    redefine(target, key, sourceProperty, options);\n  }\n};\n", "module.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "var fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  return Object.isExtensible(Object.preventExtensions({}));\n});\n", "var shared = require('../internals/shared');\n\nmodule.exports = shared('native-function-to-string', Function.toString);\n", "var path = require('../internals/path');\nvar global = require('../internals/global');\n\nvar aFunction = function (variable) {\n  return typeof variable == 'function' ? variable : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(global[namespace])\n    : path[namespace] && path[namespace][method] || global[namespace] && global[namespace][method];\n};\n", "var classof = require('../internals/classof');\nvar Iterators = require('../internals/iterators');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar ITERATOR = wellKnownSymbol('iterator');\n\nmodule.exports = function (it) {\n  if (it != undefined) return it[ITERATOR]\n    || it['@@iterator']\n    || Iterators[classof(it)];\n};\n", "var O = 'object';\nvar check = function (it) {\n  return it && it.Math == Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line no-undef\n  check(typeof globalThis == O && globalThis) ||\n  check(typeof window == O && window) ||\n  check(typeof self == O && self) ||\n  check(typeof global == O && global) ||\n  // eslint-disable-next-line no-new-func\n  Function('return this')();\n", "var hasOwnProperty = {}.hasOwnProperty;\n\nmodule.exports = function (it, key) {\n  return hasOwnProperty.call(it, key);\n};\n", "module.exports = {};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "var getBuiltIn = require('../internals/get-built-in');\n\nmodule.exports = getBuiltIn('document', 'documentElement');\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thank's IE8 for his funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a != 7;\n});\n", "var fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar split = ''.split;\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins\n  return !Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) == 'String' ? split.call(it, '') : Object(it);\n} : Object;\n", "var isObject = require('../internals/is-object');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    typeof (NewTarget = dummy.constructor) == 'function' &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n", "var hiddenKeys = require('../internals/hidden-keys');\nvar isObject = require('../internals/is-object');\nvar has = require('../internals/has');\nvar defineProperty = require('../internals/object-define-property').f;\nvar uid = require('../internals/uid');\nvar FREEZING = require('../internals/freezing');\n\nvar METADATA = uid('meta');\nvar id = 0;\n\nvar isExtensible = Object.isExtensible || function () {\n  return true;\n};\n\nvar setMetadata = function (it) {\n  defineProperty(it, METADATA, { value: {\n    objectID: 'O' + ++id, // object ID\n    weakData: {}          // weak collections IDs\n  } });\n};\n\nvar fastKey = function (it, create) {\n  // return a primitive with prefix\n  if (!isObject(it)) return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;\n  if (!has(it, METADATA)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return 'F';\n    // not necessary to add metadata\n    if (!create) return 'E';\n    // add missing metadata\n    setMetadata(it);\n  // return object ID\n  } return it[METADATA].objectID;\n};\n\nvar getWeakData = function (it, create) {\n  if (!has(it, METADATA)) {\n    // can't set metadata to uncaught frozen object\n    if (!isExtensible(it)) return true;\n    // not necessary to add metadata\n    if (!create) return false;\n    // add missing metadata\n    setMetadata(it);\n  // return the store of weak collections IDs\n  } return it[METADATA].weakData;\n};\n\n// add metadata on freeze-family methods calling\nvar onFreeze = function (it) {\n  if (FREEZING && meta.REQUIRED && isExtensible(it) && !has(it, METADATA)) setMetadata(it);\n  return it;\n};\n\nvar meta = module.exports = {\n  REQUIRED: false,\n  fastKey: fastKey,\n  getWeakData: getWeakData,\n  onFreeze: onFreeze\n};\n\nhiddenKeys[METADATA] = true;\n", "var NATIVE_WEAK_MAP = require('../internals/native-weak-map');\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\nvar hide = require('../internals/hide');\nvar objectHas = require('../internals/has');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP) {\n  var store = new WeakMap();\n  var wmget = store.get;\n  var wmhas = store.has;\n  var wmset = store.set;\n  set = function (it, metadata) {\n    wmset.call(store, it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return wmget.call(store, it) || {};\n  };\n  has = function (it) {\n    return wmhas.call(store, it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    hide(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return objectHas(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return objectHas(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "var wellKnownSymbol = require('../internals/well-known-symbol');\nvar Iterators = require('../internals/iterators');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar ArrayPrototype = Array.prototype;\n\n// check on default Array iterator\nmodule.exports = function (it) {\n  return it !== undefined && (Iterators.Array === it || ArrayPrototype[ITERATOR] === it);\n};\n", "var classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.github.io/ecma262/#sec-isarray\nmodule.exports = Array.isArray || function isArray(arg) {\n  return classof(arg) == 'Array';\n};\n", "var fails = require('../internals/fails');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value == POLYFILL ? true\n    : value == NATIVE ? false\n    : typeof detection == 'function' ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "module.exports = function (it) {\n  return typeof it === 'object' ? it !== null : typeof it === 'function';\n};\n", "module.exports = false;\n", "var anObject = require('../internals/an-object');\nvar isArrayIteratorMethod = require('../internals/is-array-iterator-method');\nvar toLength = require('../internals/to-length');\nvar bind = require('../internals/bind-context');\nvar getIteratorMethod = require('../internals/get-iterator-method');\nvar callWithSafeIterationClosing = require('../internals/call-with-safe-iteration-closing');\n\nvar Result = function (stopped, result) {\n  this.stopped = stopped;\n  this.result = result;\n};\n\nvar iterate = module.exports = function (iterable, fn, that, AS_ENTRIES, IS_ITERATOR) {\n  var boundFunction = bind(fn, that, AS_ENTRIES ? 2 : 1);\n  var iterator, iterFn, index, length, result, step;\n\n  if (IS_ITERATOR) {\n    iterator = iterable;\n  } else {\n    iterFn = getIteratorMethod(iterable);\n    if (typeof iterFn != 'function') throw TypeError('Target is not iterable');\n    // optimisation for array iterators\n    if (isArrayIteratorMethod(iterFn)) {\n      for (index = 0, length = toLength(iterable.length); length > index; index++) {\n        result = AS_ENTRIES\n          ? boundFunction(anObject(step = iterable[index])[0], step[1])\n          : boundFunction(iterable[index]);\n        if (result && result instanceof Result) return result;\n      } return new Result(false);\n    }\n    iterator = iterFn.call(iterable);\n  }\n\n  while (!(step = iterator.next()).done) {\n    result = callWithSafeIterationClosing(iterator, boundFunction, step.value, AS_ENTRIES);\n    if (result && result instanceof Result) return result;\n  } return new Result(false);\n};\n\niterate.stop = function (result) {\n  return new Result(true, result);\n};\n", "'use strict';\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar hide = require('../internals/hide');\nvar has = require('../internals/has');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar IS_PURE = require('../internals/is-pure');\n\nvar ITERATOR = wellKnownSymbol('iterator');\nvar BUGGY_SAFARI_ITERATORS = false;\n\nvar returnThis = function () { return this; };\n\n// `%IteratorPrototype%` object\n// https://tc39.github.io/ecma262/#sec-%iteratorprototype%-object\nvar IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;\n\nif ([].keys) {\n  arrayIterator = [].keys();\n  // Safari 8 has buggy iterators w/o `next`\n  if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;\n  else {\n    PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));\n    if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;\n  }\n}\n\nif (IteratorPrototype == undefined) IteratorPrototype = {};\n\n// 25.1.2.1.1 %IteratorPrototype%[@@iterator]()\nif (!IS_PURE && !has(IteratorPrototype, ITERATOR)) hide(IteratorPrototype, ITERATOR, returnThis);\n\nmodule.exports = {\n  IteratorPrototype: IteratorPrototype,\n  BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS\n};\n", "module.exports = {};\n", "var fails = require('../internals/fails');\n\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  // Chrome 38 Symbol has incorrect toString conversion\n  // eslint-disable-next-line no-undef\n  return !String(Symbol());\n});\n", "var global = require('../internals/global');\nvar nativeFunctionToString = require('../internals/function-to-string');\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = typeof WeakMap === 'function' && /native code/.test(nativeFunctionToString.call(WeakMap));\n", "var anObject = require('../internals/an-object');\nvar defineProperties = require('../internals/object-define-properties');\nvar enumBugKeys = require('../internals/enum-bug-keys');\nvar hiddenKeys = require('../internals/hidden-keys');\nvar html = require('../internals/html');\nvar documentCreateElement = require('../internals/document-create-element');\nvar sharedKey = require('../internals/shared-key');\nvar IE_PROTO = sharedKey('IE_PROTO');\n\nvar PROTOTYPE = 'prototype';\nvar Empty = function () { /* empty */ };\n\n// Create object with fake `null` prototype: use iframe Object with cleared prototype\nvar createDict = function () {\n  // Thrash, waste and sodomy: IE GC bug\n  var iframe = documentCreateElement('iframe');\n  var length = enumBugKeys.length;\n  var lt = '<';\n  var script = 'script';\n  var gt = '>';\n  var js = 'java' + script + ':';\n  var iframeDocument;\n  iframe.style.display = 'none';\n  html.appendChild(iframe);\n  iframe.src = String(js);\n  iframeDocument = iframe.contentWindow.document;\n  iframeDocument.open();\n  iframeDocument.write(lt + script + gt + 'document.F=Object' + lt + '/' + script + gt);\n  iframeDocument.close();\n  createDict = iframeDocument.F;\n  while (length--) delete createDict[PROTOTYPE][enumBugKeys[length]];\n  return createDict();\n};\n\n// `Object.create` method\n// https://tc39.github.io/ecma262/#sec-object.create\nmodule.exports = Object.create || function create(O, Properties) {\n  var result;\n  if (O !== null) {\n    Empty[PROTOTYPE] = anObject(O);\n    result = new Empty();\n    Empty[PROTOTYPE] = null;\n    // add \"__proto__\" for Object.getPrototypeOf polyfill\n    result[IE_PROTO] = O;\n  } else result = createDict();\n  return Properties === undefined ? result : defineProperties(result, Properties);\n};\n\nhiddenKeys[IE_PROTO] = true;\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar anObject = require('../internals/an-object');\nvar objectKeys = require('../internals/object-keys');\n\n// `Object.defineProperties` method\n// https://tc39.github.io/ecma262/#sec-object.defineproperties\nmodule.exports = DESCRIPTORS ? Object.defineProperties : function defineProperties(O, Properties) {\n  anObject(O);\n  var keys = objectKeys(Properties);\n  var length = keys.length;\n  var index = 0;\n  var key;\n  while (length > index) definePropertyModule.f(O, key = keys[index++], Properties[key]);\n  return O;\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar anObject = require('../internals/an-object');\nvar toPrimitive = require('../internals/to-primitive');\n\nvar nativeDefineProperty = Object.defineProperty;\n\n// `Object.defineProperty` method\n// https://tc39.github.io/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? nativeDefineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPrimitive(P, true);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return nativeDefineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "var DESCRIPTORS = require('../internals/descriptors');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPrimitive = require('../internals/to-primitive');\nvar has = require('../internals/has');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\nvar nativeGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.github.io/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? nativeGetOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPrimitive(P, true);\n  if (IE8_DOM_DEFINE) try {\n    return nativeGetOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (has(O, P)) return createPropertyDescriptor(!propertyIsEnumerableModule.f.call(O, P), O[P]);\n};\n", "var internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.github.io/ecma262/#sec-object.getownpropertynames\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "exports.f = Object.getOwnPropertySymbols;\n", "var has = require('../internals/has');\nvar toObject = require('../internals/to-object');\nvar sharedKey = require('../internals/shared-key');\nvar CORRECT_PROTOTYPE_GETTER = require('../internals/correct-prototype-getter');\n\nvar IE_PROTO = sharedKey('IE_PROTO');\nvar ObjectPrototype = Object.prototype;\n\n// `Object.getPrototypeOf` method\n// https://tc39.github.io/ecma262/#sec-object.getprototypeof\nmodule.exports = CORRECT_PROTOTYPE_GETTER ? Object.getPrototypeOf : function (O) {\n  O = toObject(O);\n  if (has(O, IE_PROTO)) return O[IE_PROTO];\n  if (typeof O.constructor == 'function' && O instanceof O.constructor) {\n    return O.constructor.prototype;\n  } return O instanceof Object ? ObjectPrototype : null;\n};\n", "var has = require('../internals/has');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar indexOf = require('../internals/array-includes').indexOf;\nvar hiddenKeys = require('../internals/hidden-keys');\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !has(hiddenKeys, key) && has(O, key) && result.push(key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (has(O, key = names[i++])) {\n    ~indexOf(result, key) || result.push(key);\n  }\n  return result;\n};\n", "var internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\n// `Object.keys` method\n// https://tc39.github.io/ecma262/#sec-object.keys\nmodule.exports = Object.keys || function keys(O) {\n  return internalObjectKeys(O, enumBugKeys);\n};\n", "'use strict';\nvar nativePropertyIsEnumerable = {}.propertyIsEnumerable;\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !nativePropertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.github.io/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : nativePropertyIsEnumerable;\n", "var anObject = require('../internals/an-object');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.github.io/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n/* eslint-disable no-proto */\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = Object.getOwnPropertyDescriptor(Object.prototype, '__proto__').set;\n    setter.call(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    anObject(O);\n    aPossiblePrototype(proto);\n    if (CORRECT_SETTER) setter.call(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "var getBuiltIn = require('../internals/get-built-in');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? keys.concat(getOwnPropertySymbols(it)) : keys;\n};\n", "module.exports = require('../internals/global');\n", "var redefine = require('../internals/redefine');\n\nmodule.exports = function (target, src, options) {\n  for (var key in src) redefine(target, key, src[key], options);\n  return target;\n};\n", "var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hide = require('../internals/hide');\nvar has = require('../internals/has');\nvar setGlobal = require('../internals/set-global');\nvar nativeFunctionToString = require('../internals/function-to-string');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar getInternalState = InternalStateModule.get;\nvar enforceInternalState = InternalStateModule.enforce;\nvar TEMPLATE = String(nativeFunctionToString).split('toString');\n\nshared('inspectSource', function (it) {\n  return nativeFunctionToString.call(it);\n});\n\n(module.exports = function (O, key, value, options) {\n  var unsafe = options ? !!options.unsafe : false;\n  var simple = options ? !!options.enumerable : false;\n  var noTargetGet = options ? !!options.noTargetGet : false;\n  if (typeof value == 'function') {\n    if (typeof key == 'string' && !has(value, 'name')) hide(value, 'name', key);\n    enforceInternalState(value).source = TEMPLATE.join(typeof key == 'string' ? key : '');\n  }\n  if (O === global) {\n    if (simple) O[key] = value;\n    else setGlobal(key, value);\n    return;\n  } else if (!unsafe) {\n    delete O[key];\n  } else if (!noTargetGet && O[key]) {\n    simple = true;\n  }\n  if (simple) O[key] = value;\n  else hide(O, key, value);\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n})(Function.prototype, 'toString', function toString() {\n  return typeof this == 'function' && getInternalState(this).source || nativeFunctionToString.call(this);\n});\n", "// TODO: in core-js@4, move /modules/ dependencies to public entries for better optimization by tools like `preset-env`\nvar Map = require('../modules/es.map');\nvar WeakMap = require('../modules/es.weak-map');\nvar shared = require('../internals/shared');\n\nvar metadata = shared('metadata');\nvar store = metadata.store || (metadata.store = new WeakMap());\n\nvar getOrCreateMetadataMap = function (target, targetKey, create) {\n  var targetMetadata = store.get(target);\n  if (!targetMetadata) {\n    if (!create) return;\n    store.set(target, targetMetadata = new Map());\n  }\n  var keyMetadata = targetMetadata.get(targetKey);\n  if (!keyMetadata) {\n    if (!create) return;\n    targetMetadata.set(targetKey, keyMetadata = new Map());\n  } return keyMetadata;\n};\n\nvar ordinaryHasOwnMetadata = function (MetadataKey, O, P) {\n  var metadataMap = getOrCreateMetadataMap(O, P, false);\n  return metadataMap === undefined ? false : metadataMap.has(MetadataKey);\n};\n\nvar ordinaryGetOwnMetadata = function (MetadataKey, O, P) {\n  var metadataMap = getOrCreateMetadataMap(O, P, false);\n  return metadataMap === undefined ? undefined : metadataMap.get(MetadataKey);\n};\n\nvar ordinaryDefineOwnMetadata = function (MetadataKey, MetadataValue, O, P) {\n  getOrCreateMetadataMap(O, P, true).set(MetadataKey, MetadataValue);\n};\n\nvar ordinaryOwnMetadataKeys = function (target, targetKey) {\n  var metadataMap = getOrCreateMetadataMap(target, targetKey, false);\n  var keys = [];\n  if (metadataMap) metadataMap.forEach(function (_, key) { keys.push(key); });\n  return keys;\n};\n\nvar toMetadataKey = function (it) {\n  return it === undefined || typeof it == 'symbol' ? it : String(it);\n};\n\nmodule.exports = {\n  store: store,\n  getMap: getOrCreateMetadataMap,\n  has: ordinaryHasOwnMetadata,\n  get: ordinaryGetOwnMetadata,\n  set: ordinaryDefineOwnMetadata,\n  keys: ordinaryOwnMetadataKeys,\n  toKey: toMetadataKey\n};\n", "// `RequireObjectCoercible` abstract operation\n// https://tc39.github.io/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (it == undefined) throw TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "var global = require('../internals/global');\nvar hide = require('../internals/hide');\n\nmodule.exports = function (key, value) {\n  try {\n    hide(global, key, value);\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar definePropertyModule = require('../internals/object-define-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\nvar DESCRIPTORS = require('../internals/descriptors');\n\nvar SPECIES = wellKnownSymbol('species');\n\nmodule.exports = function (CONSTRUCTOR_NAME) {\n  var Constructor = getBuiltIn(CONSTRUCTOR_NAME);\n  var defineProperty = definePropertyModule.f;\n\n  if (DESCRIPTORS && Constructor && !Constructor[SPECIES]) {\n    defineProperty(Constructor, SPECIES, {\n      configurable: true,\n      get: function () { return this; }\n    });\n  }\n};\n", "var defineProperty = require('../internals/object-define-property').f;\nvar has = require('../internals/has');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (it, TAG, STATIC) {\n  if (it && !has(it = STATIC ? it : it.prototype, TO_STRING_TAG)) {\n    defineProperty(it, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n", "var shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "var global = require('../internals/global');\nvar setGlobal = require('../internals/set-global');\nvar IS_PURE = require('../internals/is-pure');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || setGlobal(SHARED, {});\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.2.1',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2019 <PERSON> (zloirock.ru)'\n});\n", "var toInteger = require('../internals/to-integer');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(length, length).\nmodule.exports = function (index, length) {\n  var integer = toInteger(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "var ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `ToInteger` abstract operation\n// https://tc39.github.io/ecma262/#sec-tointeger\nmodule.exports = function (argument) {\n  return isNaN(argument = +argument) ? 0 : (argument > 0 ? floor : ceil)(argument);\n};\n", "var toInteger = require('../internals/to-integer');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.github.io/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  return argument > 0 ? min(toInteger(argument), 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "var requireObjectCoercible = require('../internals/require-object-coercible');\n\n// `ToObject` abstract operation\n// https://tc39.github.io/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return Object(requireObjectCoercible(argument));\n};\n", "var isObject = require('../internals/is-object');\n\n// `ToPrimitive` abstract operation\n// https://tc39.github.io/ecma262/#sec-toprimitive\n// instead of the ES6 spec version, we didn't implement @@toPrimitive case\n// and the second argument - flag - preferred type is a string\nmodule.exports = function (input, PREFERRED_STRING) {\n  if (!isObject(input)) return input;\n  var fn, val;\n  if (PREFERRED_STRING && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;\n  if (typeof (fn = input.valueOf) == 'function' && !isObject(val = fn.call(input))) return val;\n  if (!PREFERRED_STRING && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;\n  throw TypeError(\"Can't convert object to primitive value\");\n};\n", "var id = 0;\nvar postfix = Math.random();\n\nmodule.exports = function (key) {\n  return 'Symbol(' + String(key === undefined ? '' : key) + ')_' + (++id + postfix).toString(36);\n};\n", "var global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/native-symbol');\n\nvar Symbol = global.Symbol;\nvar store = shared('wks');\n\nmodule.exports = function (name) {\n  return store[name] || (store[name] = NATIVE_SYMBOL && Symbol[name]\n    || (NATIVE_SYMBOL ? Symbol : uid)('Symbol.' + name));\n};\n", "'use strict';\nvar collection = require('../internals/collection');\nvar collectionStrong = require('../internals/collection-strong');\n\n// `Map` constructor\n// https://tc39.github.io/ecma262/#sec-map-objects\nmodule.exports = collection('Map', function (get) {\n  return function Map() { return get(this, arguments.length ? arguments[0] : undefined); };\n}, collectionStrong, true);\n", "'use strict';\nvar collection = require('../internals/collection');\nvar collectionStrong = require('../internals/collection-strong');\n\n// `Set` constructor\n// https://tc39.github.io/ecma262/#sec-set-objects\nmodule.exports = collection('Set', function (get) {\n  return function Set() { return get(this, arguments.length ? arguments[0] : undefined); };\n}, collectionStrong);\n", "'use strict';\nvar global = require('../internals/global');\nvar redefineAll = require('../internals/redefine-all');\nvar InternalMetadataModule = require('../internals/internal-metadata');\nvar collection = require('../internals/collection');\nvar collectionWeak = require('../internals/collection-weak');\nvar isObject = require('../internals/is-object');\nvar enforceIternalState = require('../internals/internal-state').enforce;\nvar NATIVE_WEAK_MAP = require('../internals/native-weak-map');\n\nvar IS_IE11 = !global.ActiveXObject && 'ActiveXObject' in global;\nvar isExtensible = Object.isExtensible;\nvar InternalWeakMap;\n\nvar wrapper = function (get) {\n  return function WeakMap() {\n    return get(this, arguments.length ? arguments[0] : undefined);\n  };\n};\n\n// `WeakMap` constructor\n// https://tc39.github.io/ecma262/#sec-weakmap-constructor\nvar $WeakMap = module.exports = collection('WeakMap', wrapper, collectionWeak, true, true);\n\n// IE11 WeakMap frozen keys fix\n// We can't use feature detection because it crash some old IE builds\n// https://github.com/zloirock/core-js/issues/485\nif (NATIVE_WEAK_MAP && IS_IE11) {\n  InternalWeakMap = collectionWeak.getConstructor(wrapper, 'WeakMap', true);\n  InternalMetadataModule.REQUIRED = true;\n  var WeakMapPrototype = $WeakMap.prototype;\n  var nativeDelete = WeakMapPrototype['delete'];\n  var nativeHas = WeakMapPrototype.has;\n  var nativeGet = WeakMapPrototype.get;\n  var nativeSet = WeakMapPrototype.set;\n  redefineAll(WeakMapPrototype, {\n    'delete': function (key) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        return nativeDelete.call(this, key) || state.frozen['delete'](key);\n      } return nativeDelete.call(this, key);\n    },\n    has: function has(key) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        return nativeHas.call(this, key) || state.frozen.has(key);\n      } return nativeHas.call(this, key);\n    },\n    get: function get(key) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        return nativeHas.call(this, key) ? nativeGet.call(this, key) : state.frozen.get(key);\n      } return nativeGet.call(this, key);\n    },\n    set: function set(key, value) {\n      if (isObject(key) && !isExtensible(key)) {\n        var state = enforceIternalState(this);\n        if (!state.frozen) state.frozen = new InternalWeakMap();\n        nativeHas.call(this, key) ? nativeSet.call(this, key, value) : state.frozen.set(key, value);\n      } else nativeSet.call(this, key, value);\n      return this;\n    }\n  });\n}\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\n\nvar toMetadataKey = ReflectMetadataModule.toKey;\nvar ordinaryDefineOwnMetadata = ReflectMetadataModule.set;\n\n// `Reflect.defineMetadata` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  defineMetadata: function defineMetadata(metadataKey, metadataValue, target /* , targetKey */) {\n    var targetKey = arguments.length < 4 ? undefined : toMetadataKey(arguments[3]);\n    ordinaryDefineOwnMetadata(metadataKey, metadataValue, anObject(target), targetKey);\n  }\n});\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\n\nvar toMetadataKey = ReflectMetadataModule.toKey;\nvar getOrCreateMetadataMap = ReflectMetadataModule.getMap;\nvar store = ReflectMetadataModule.store;\n\n// `Reflect.deleteMetadata` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  deleteMetadata: function deleteMetadata(metadataKey, target /* , targetKey */) {\n    var targetKey = arguments.length < 3 ? undefined : toMetadataKey(arguments[2]);\n    var metadataMap = getOrCreateMetadataMap(anObject(target), targetKey, false);\n    if (metadataMap === undefined || !metadataMap['delete'](metadataKey)) return false;\n    if (metadataMap.size) return true;\n    var targetMetadata = store.get(target);\n    targetMetadata['delete'](targetKey);\n    return !!targetMetadata.size || store['delete'](target);\n  }\n});\n", "var $ = require('../internals/export');\n// TODO: in core-js@4, move /modules/ dependencies to public entries for better optimization by tools like `preset-env`\nvar Set = require('../modules/es.set');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\nvar iterate = require('../internals/iterate');\n\nvar ordinaryOwnMetadataKeys = ReflectMetadataModule.keys;\nvar toMetadataKey = ReflectMetadataModule.toKey;\n\nvar from = function (iter) {\n  var result = [];\n  iterate(iter, result.push, result);\n  return result;\n};\n\nvar ordinaryMetadataKeys = function (O, P) {\n  var oKeys = ordinaryOwnMetadataKeys(O, P);\n  var parent = getPrototypeOf(O);\n  if (parent === null) return oKeys;\n  var pKeys = ordinaryMetadataKeys(parent, P);\n  return pKeys.length ? oKeys.length ? from(new Set(oKeys.concat(pKeys))) : pKeys : oKeys;\n};\n\n// `Reflect.getMetadataKeys` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  getMetadataKeys: function getMetadataKeys(target /* , targetKey */) {\n    var targetKey = arguments.length < 2 ? undefined : toMetadataKey(arguments[1]);\n    return ordinaryMetadataKeys(anObject(target), targetKey);\n  }\n});\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\n\nvar ordinaryHasOwnMetadata = ReflectMetadataModule.has;\nvar ordinaryGetOwnMetadata = ReflectMetadataModule.get;\nvar toMetadataKey = ReflectMetadataModule.toKey;\n\nvar ordinaryGetMetadata = function (MetadataKey, O, P) {\n  var hasOwn = ordinaryHasOwnMetadata(MetadataKey, O, P);\n  if (hasOwn) return ordinaryGetOwnMetadata(MetadataKey, O, P);\n  var parent = getPrototypeOf(O);\n  return parent !== null ? ordinaryGetMetadata(MetadataKey, parent, P) : undefined;\n};\n\n// `Reflect.getMetadata` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  getMetadata: function getMetadata(metadata<PERSON>ey, target /* , targetKey */) {\n    var targetKey = arguments.length < 3 ? undefined : toMetadataKey(arguments[2]);\n    return ordinaryGetMetadata(metadataKey, anObject(target), targetKey);\n  }\n});\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\n\nvar ordinaryOwnMetadataKeys = ReflectMetadataModule.keys;\nvar toMetadataKey = ReflectMetadataModule.toKey;\n\n// `Reflect.getOwnMetadataKeys` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  getOwnMetadataKeys: function getOwnMetadataKeys(target /* , targetKey */) {\n    var targetKey = arguments.length < 2 ? undefined : toMetadataKey(arguments[1]);\n    return ordinaryOwnMetadataKeys(anObject(target), targetKey);\n  }\n});\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\n\nvar ordinaryGetOwnMetadata = ReflectMetadataModule.get;\nvar toMetadataKey = ReflectMetadataModule.toKey;\n\n// `Reflect.getOwnMetadata` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  getOwnMetadata: function getOwnMetadata(metadataKey, target /* , targetKey */) {\n    var targetKey = arguments.length < 3 ? undefined : toMetadataKey(arguments[2]);\n    return ordinaryGetOwnMetadata(metadataKey, anObject(target), targetKey);\n  }\n});\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\nvar getPrototypeOf = require('../internals/object-get-prototype-of');\n\nvar ordinaryHasOwnMetadata = ReflectMetadataModule.has;\nvar toMetadataKey = ReflectMetadataModule.toKey;\n\nvar ordinaryHasMetadata = function (MetadataKey, O, P) {\n  var hasOwn = ordinaryHasOwnMetadata(MetadataKey, O, P);\n  if (hasOwn) return true;\n  var parent = getPrototypeOf(O);\n  return parent !== null ? ordinaryHasMetadata(MetadataKey, parent, P) : false;\n};\n\n// `Reflect.hasMetadata` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  hasMetadata: function hasMetadata(metadataKey, target /* , targetKey */) {\n    var targetKey = arguments.length < 3 ? undefined : toMetadataKey(arguments[2]);\n    return ordinaryHasMetadata(metadataKey, anObject(target), targetKey);\n  }\n});\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\n\nvar ordinaryHasOwnMetadata = ReflectMetadataModule.has;\nvar toMetadataKey = ReflectMetadataModule.toKey;\n\n// `Reflect.hasOwnMetadata` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  hasOwnMetadata: function hasOwnMetadata(metadataKey, target /* , targetKey */) {\n    var targetKey = arguments.length < 3 ? undefined : toMetadataKey(arguments[2]);\n    return ordinaryHasOwnMetadata(metadataKey, anObject(target), targetKey);\n  }\n});\n", "var $ = require('../internals/export');\nvar ReflectMetadataModule = require('../internals/reflect-metadata');\nvar anObject = require('../internals/an-object');\n\nvar toMetadataKey = ReflectMetadataModule.toKey;\nvar ordinaryDefineOwnMetadata = ReflectMetadataModule.set;\n\n// `Reflect.metadata` method\n// https://github.com/rbuckton/reflect-metadata\n$({ target: 'Reflect', stat: true }, {\n  metadata: function metadata(metadataKey, metadataValue) {\n    return function decorator(target, key) {\n      ordinaryDefineOwnMetadata(metadataKey, metadataValue, anObject(target), toMetadataKey(key));\n    };\n  }\n});\n", "require('../modules/esnext.reflect.define-metadata');\nrequire('../modules/esnext.reflect.delete-metadata');\nrequire('../modules/esnext.reflect.get-metadata');\nrequire('../modules/esnext.reflect.get-metadata-keys');\nrequire('../modules/esnext.reflect.get-own-metadata');\nrequire('../modules/esnext.reflect.get-own-metadata-keys');\nrequire('../modules/esnext.reflect.has-metadata');\nrequire('../modules/esnext.reflect.has-own-metadata');\nrequire('../modules/esnext.reflect.metadata');\n", "/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport 'core-js/proposals/reflect-metadata';\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\r\n    if (m) return m.call(o);\r\n    return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n", "/**\n* @license\n* Copyright Google Inc. All Rights Reserved.\n*\n* Use of this source code is governed by an MIT-style license that can be\n* found in the LICENSE file at https://angular.io/license\n*/\n(function (global, factory) {\n\ttypeof exports === 'object' && typeof module !== 'undefined' ? factory() :\n\ttypeof define === 'function' && define.amd ? define(factory) :\n\t(factory());\n}(this, (function () { 'use strict';\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nvar Zone$1 = (function (global) {\n    var performance = global['performance'];\n    function mark(name) {\n        performance && performance['mark'] && performance['mark'](name);\n    }\n    function performanceMeasure(name, label) {\n        performance && performance['measure'] && performance['measure'](name, label);\n    }\n    mark('Zone');\n    var checkDuplicate = global[('__zone_symbol__forceDuplicateZoneCheck')] === true;\n    if (global['Zone']) {\n        // if global['Zone'] already exists (maybe zone.js was already loaded or\n        // some other lib also registered a global object named Zone), we may need\n        // to throw an error, but sometimes user may not want this error.\n        // For example,\n        // we have two web pages, page1 includes zone.js, page2 doesn't.\n        // and the 1st time user load page1 and page2, everything work fine,\n        // but when user load page2 again, error occurs because global['Zone'] already exists.\n        // so we add a flag to let user choose whether to throw this error or not.\n        // By default, if existing Zone is from zone.js, we will not throw the error.\n        if (checkDuplicate || typeof global['Zone'].__symbol__ !== 'function') {\n            throw new Error('Zone already loaded.');\n        }\n        else {\n            return global['Zone'];\n        }\n    }\n    var Zone = /** @class */ (function () {\n        function Zone(parent, zoneSpec) {\n            this._parent = parent;\n            this._name = zoneSpec ? zoneSpec.name || 'unnamed' : '<root>';\n            this._properties = zoneSpec && zoneSpec.properties || {};\n            this._zoneDelegate =\n                new ZoneDelegate(this, this._parent && this._parent._zoneDelegate, zoneSpec);\n        }\n        Zone.assertZonePatched = function () {\n            if (global['Promise'] !== patches['ZoneAwarePromise']) {\n                throw new Error('Zone.js has detected that ZoneAwarePromise `(window|global).Promise` ' +\n                    'has been overwritten.\\n' +\n                    'Most likely cause is that a Promise polyfill has been loaded ' +\n                    'after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. ' +\n                    'If you must load one, do so before loading zone.js.)');\n            }\n        };\n        Object.defineProperty(Zone, \"root\", {\n            get: function () {\n                var zone = Zone.current;\n                while (zone.parent) {\n                    zone = zone.parent;\n                }\n                return zone;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        Object.defineProperty(Zone, \"current\", {\n            get: function () {\n                return _currentZoneFrame.zone;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        Object.defineProperty(Zone, \"currentTask\", {\n            get: function () {\n                return _currentTask;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        Zone.__load_patch = function (name, fn) {\n            if (patches.hasOwnProperty(name)) {\n                if (checkDuplicate) {\n                    throw Error('Already loaded patch: ' + name);\n                }\n            }\n            else if (!global['__Zone_disable_' + name]) {\n                var perfName = 'Zone:' + name;\n                mark(perfName);\n                patches[name] = fn(global, Zone, _api);\n                performanceMeasure(perfName, perfName);\n            }\n        };\n        Object.defineProperty(Zone.prototype, \"parent\", {\n            get: function () {\n                return this._parent;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        Object.defineProperty(Zone.prototype, \"name\", {\n            get: function () {\n                return this._name;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        Zone.prototype.get = function (key) {\n            var zone = this.getZoneWith(key);\n            if (zone)\n                return zone._properties[key];\n        };\n        Zone.prototype.getZoneWith = function (key) {\n            var current = this;\n            while (current) {\n                if (current._properties.hasOwnProperty(key)) {\n                    return current;\n                }\n                current = current._parent;\n            }\n            return null;\n        };\n        Zone.prototype.fork = function (zoneSpec) {\n            if (!zoneSpec)\n                throw new Error('ZoneSpec required!');\n            return this._zoneDelegate.fork(this, zoneSpec);\n        };\n        Zone.prototype.wrap = function (callback, source) {\n            if (typeof callback !== 'function') {\n                throw new Error('Expecting function got: ' + callback);\n            }\n            var _callback = this._zoneDelegate.intercept(this, callback, source);\n            var zone = this;\n            return function () {\n                return zone.runGuarded(_callback, this, arguments, source);\n            };\n        };\n        Zone.prototype.run = function (callback, applyThis, applyArgs, source) {\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        };\n        Zone.prototype.runGuarded = function (callback, applyThis, applyArgs, source) {\n            if (applyThis === void 0) { applyThis = null; }\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                try {\n                    return this._zoneDelegate.invoke(this, callback, applyThis, applyArgs, source);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                _currentZoneFrame = _currentZoneFrame.parent;\n            }\n        };\n        Zone.prototype.runTask = function (task, applyThis, applyArgs) {\n            if (task.zone != this) {\n                throw new Error('A task can only be run in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            }\n            // https://github.com/angular/zone.js/issues/778, sometimes eventTask\n            // will run in notScheduled(canceled) state, we should not try to\n            // run such kind of task but just return\n            if (task.state === notScheduled && (task.type === eventTask || task.type === macroTask)) {\n                return;\n            }\n            var reEntryGuard = task.state != running;\n            reEntryGuard && task._transitionTo(running, scheduled);\n            task.runCount++;\n            var previousTask = _currentTask;\n            _currentTask = task;\n            _currentZoneFrame = { parent: _currentZoneFrame, zone: this };\n            try {\n                if (task.type == macroTask && task.data && !task.data.isPeriodic) {\n                    task.cancelFn = undefined;\n                }\n                try {\n                    return this._zoneDelegate.invokeTask(this, task, applyThis, applyArgs);\n                }\n                catch (error) {\n                    if (this._zoneDelegate.handleError(this, error)) {\n                        throw error;\n                    }\n                }\n            }\n            finally {\n                // if the task's state is notScheduled or unknown, then it has already been cancelled\n                // we should not reset the state to scheduled\n                if (task.state !== notScheduled && task.state !== unknown) {\n                    if (task.type == eventTask || (task.data && task.data.isPeriodic)) {\n                        reEntryGuard && task._transitionTo(scheduled, running);\n                    }\n                    else {\n                        task.runCount = 0;\n                        this._updateTaskCount(task, -1);\n                        reEntryGuard &&\n                            task._transitionTo(notScheduled, running, notScheduled);\n                    }\n                }\n                _currentZoneFrame = _currentZoneFrame.parent;\n                _currentTask = previousTask;\n            }\n        };\n        Zone.prototype.scheduleTask = function (task) {\n            if (task.zone && task.zone !== this) {\n                // check if the task was rescheduled, the newZone\n                // should not be the children of the original zone\n                var newZone = this;\n                while (newZone) {\n                    if (newZone === task.zone) {\n                        throw Error(\"can not reschedule task to \" + this.name + \" which is descendants of the original zone \" + task.zone.name);\n                    }\n                    newZone = newZone.parent;\n                }\n            }\n            task._transitionTo(scheduling, notScheduled);\n            var zoneDelegates = [];\n            task._zoneDelegates = zoneDelegates;\n            task._zone = this;\n            try {\n                task = this._zoneDelegate.scheduleTask(this, task);\n            }\n            catch (err) {\n                // should set task's state to unknown when scheduleTask throw error\n                // because the err may from reschedule, so the fromState maybe notScheduled\n                task._transitionTo(unknown, scheduling, notScheduled);\n                // TODO: @JiaLiPassion, should we check the result from handleError?\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            if (task._zoneDelegates === zoneDelegates) {\n                // we have to check because internally the delegate can reschedule the task.\n                this._updateTaskCount(task, 1);\n            }\n            if (task.state == scheduling) {\n                task._transitionTo(scheduled, scheduling);\n            }\n            return task;\n        };\n        Zone.prototype.scheduleMicroTask = function (source, callback, data, customSchedule) {\n            return this.scheduleTask(new ZoneTask(microTask, source, callback, data, customSchedule, undefined));\n        };\n        Zone.prototype.scheduleMacroTask = function (source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(macroTask, source, callback, data, customSchedule, customCancel));\n        };\n        Zone.prototype.scheduleEventTask = function (source, callback, data, customSchedule, customCancel) {\n            return this.scheduleTask(new ZoneTask(eventTask, source, callback, data, customSchedule, customCancel));\n        };\n        Zone.prototype.cancelTask = function (task) {\n            if (task.zone != this)\n                throw new Error('A task can only be cancelled in the zone of creation! (Creation: ' +\n                    (task.zone || NO_ZONE).name + '; Execution: ' + this.name + ')');\n            task._transitionTo(canceling, scheduled, running);\n            try {\n                this._zoneDelegate.cancelTask(this, task);\n            }\n            catch (err) {\n                // if error occurs when cancelTask, transit the state to unknown\n                task._transitionTo(unknown, canceling);\n                this._zoneDelegate.handleError(this, err);\n                throw err;\n            }\n            this._updateTaskCount(task, -1);\n            task._transitionTo(notScheduled, canceling);\n            task.runCount = 0;\n            return task;\n        };\n        Zone.prototype._updateTaskCount = function (task, count) {\n            var zoneDelegates = task._zoneDelegates;\n            if (count == -1) {\n                task._zoneDelegates = null;\n            }\n            for (var i = 0; i < zoneDelegates.length; i++) {\n                zoneDelegates[i]._updateTaskCount(task.type, count);\n            }\n        };\n        Zone.__symbol__ = __symbol__;\n        return Zone;\n    }());\n    var DELEGATE_ZS = {\n        name: '',\n        onHasTask: function (delegate, _, target, hasTaskState) { return delegate.hasTask(target, hasTaskState); },\n        onScheduleTask: function (delegate, _, target, task) {\n            return delegate.scheduleTask(target, task);\n        },\n        onInvokeTask: function (delegate, _, target, task, applyThis, applyArgs) {\n            return delegate.invokeTask(target, task, applyThis, applyArgs);\n        },\n        onCancelTask: function (delegate, _, target, task) { return delegate.cancelTask(target, task); }\n    };\n    var ZoneDelegate = /** @class */ (function () {\n        function ZoneDelegate(zone, parentDelegate, zoneSpec) {\n            this._taskCounts = { 'microTask': 0, 'macroTask': 0, 'eventTask': 0 };\n            this.zone = zone;\n            this._parentDelegate = parentDelegate;\n            this._forkZS = zoneSpec && (zoneSpec && zoneSpec.onFork ? zoneSpec : parentDelegate._forkZS);\n            this._forkDlgt = zoneSpec && (zoneSpec.onFork ? parentDelegate : parentDelegate._forkDlgt);\n            this._forkCurrZone = zoneSpec && (zoneSpec.onFork ? this.zone : parentDelegate.zone);\n            this._interceptZS =\n                zoneSpec && (zoneSpec.onIntercept ? zoneSpec : parentDelegate._interceptZS);\n            this._interceptDlgt =\n                zoneSpec && (zoneSpec.onIntercept ? parentDelegate : parentDelegate._interceptDlgt);\n            this._interceptCurrZone =\n                zoneSpec && (zoneSpec.onIntercept ? this.zone : parentDelegate.zone);\n            this._invokeZS = zoneSpec && (zoneSpec.onInvoke ? zoneSpec : parentDelegate._invokeZS);\n            this._invokeDlgt =\n                zoneSpec && (zoneSpec.onInvoke ? parentDelegate : parentDelegate._invokeDlgt);\n            this._invokeCurrZone = zoneSpec && (zoneSpec.onInvoke ? this.zone : parentDelegate.zone);\n            this._handleErrorZS =\n                zoneSpec && (zoneSpec.onHandleError ? zoneSpec : parentDelegate._handleErrorZS);\n            this._handleErrorDlgt =\n                zoneSpec && (zoneSpec.onHandleError ? parentDelegate : parentDelegate._handleErrorDlgt);\n            this._handleErrorCurrZone =\n                zoneSpec && (zoneSpec.onHandleError ? this.zone : parentDelegate.zone);\n            this._scheduleTaskZS =\n                zoneSpec && (zoneSpec.onScheduleTask ? zoneSpec : parentDelegate._scheduleTaskZS);\n            this._scheduleTaskDlgt = zoneSpec &&\n                (zoneSpec.onScheduleTask ? parentDelegate : parentDelegate._scheduleTaskDlgt);\n            this._scheduleTaskCurrZone =\n                zoneSpec && (zoneSpec.onScheduleTask ? this.zone : parentDelegate.zone);\n            this._invokeTaskZS =\n                zoneSpec && (zoneSpec.onInvokeTask ? zoneSpec : parentDelegate._invokeTaskZS);\n            this._invokeTaskDlgt =\n                zoneSpec && (zoneSpec.onInvokeTask ? parentDelegate : parentDelegate._invokeTaskDlgt);\n            this._invokeTaskCurrZone =\n                zoneSpec && (zoneSpec.onInvokeTask ? this.zone : parentDelegate.zone);\n            this._cancelTaskZS =\n                zoneSpec && (zoneSpec.onCancelTask ? zoneSpec : parentDelegate._cancelTaskZS);\n            this._cancelTaskDlgt =\n                zoneSpec && (zoneSpec.onCancelTask ? parentDelegate : parentDelegate._cancelTaskDlgt);\n            this._cancelTaskCurrZone =\n                zoneSpec && (zoneSpec.onCancelTask ? this.zone : parentDelegate.zone);\n            this._hasTaskZS = null;\n            this._hasTaskDlgt = null;\n            this._hasTaskDlgtOwner = null;\n            this._hasTaskCurrZone = null;\n            var zoneSpecHasTask = zoneSpec && zoneSpec.onHasTask;\n            var parentHasTask = parentDelegate && parentDelegate._hasTaskZS;\n            if (zoneSpecHasTask || parentHasTask) {\n                // If we need to report hasTask, than this ZS needs to do ref counting on tasks. In such\n                // a case all task related interceptors must go through this ZD. We can't short circuit it.\n                this._hasTaskZS = zoneSpecHasTask ? zoneSpec : DELEGATE_ZS;\n                this._hasTaskDlgt = parentDelegate;\n                this._hasTaskDlgtOwner = this;\n                this._hasTaskCurrZone = zone;\n                if (!zoneSpec.onScheduleTask) {\n                    this._scheduleTaskZS = DELEGATE_ZS;\n                    this._scheduleTaskDlgt = parentDelegate;\n                    this._scheduleTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onInvokeTask) {\n                    this._invokeTaskZS = DELEGATE_ZS;\n                    this._invokeTaskDlgt = parentDelegate;\n                    this._invokeTaskCurrZone = this.zone;\n                }\n                if (!zoneSpec.onCancelTask) {\n                    this._cancelTaskZS = DELEGATE_ZS;\n                    this._cancelTaskDlgt = parentDelegate;\n                    this._cancelTaskCurrZone = this.zone;\n                }\n            }\n        }\n        ZoneDelegate.prototype.fork = function (targetZone, zoneSpec) {\n            return this._forkZS ? this._forkZS.onFork(this._forkDlgt, this.zone, targetZone, zoneSpec) :\n                new Zone(targetZone, zoneSpec);\n        };\n        ZoneDelegate.prototype.intercept = function (targetZone, callback, source) {\n            return this._interceptZS ?\n                this._interceptZS.onIntercept(this._interceptDlgt, this._interceptCurrZone, targetZone, callback, source) :\n                callback;\n        };\n        ZoneDelegate.prototype.invoke = function (targetZone, callback, applyThis, applyArgs, source) {\n            return this._invokeZS ? this._invokeZS.onInvoke(this._invokeDlgt, this._invokeCurrZone, targetZone, callback, applyThis, applyArgs, source) :\n                callback.apply(applyThis, applyArgs);\n        };\n        ZoneDelegate.prototype.handleError = function (targetZone, error) {\n            return this._handleErrorZS ?\n                this._handleErrorZS.onHandleError(this._handleErrorDlgt, this._handleErrorCurrZone, targetZone, error) :\n                true;\n        };\n        ZoneDelegate.prototype.scheduleTask = function (targetZone, task) {\n            var returnTask = task;\n            if (this._scheduleTaskZS) {\n                if (this._hasTaskZS) {\n                    returnTask._zoneDelegates.push(this._hasTaskDlgtOwner);\n                }\n                returnTask = this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt, this._scheduleTaskCurrZone, targetZone, task);\n                if (!returnTask)\n                    returnTask = task;\n            }\n            else {\n                if (task.scheduleFn) {\n                    task.scheduleFn(task);\n                }\n                else if (task.type == microTask) {\n                    scheduleMicroTask(task);\n                }\n                else {\n                    throw new Error('Task is missing scheduleFn.');\n                }\n            }\n            return returnTask;\n        };\n        ZoneDelegate.prototype.invokeTask = function (targetZone, task, applyThis, applyArgs) {\n            return this._invokeTaskZS ? this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt, this._invokeTaskCurrZone, targetZone, task, applyThis, applyArgs) :\n                task.callback.apply(applyThis, applyArgs);\n        };\n        ZoneDelegate.prototype.cancelTask = function (targetZone, task) {\n            var value;\n            if (this._cancelTaskZS) {\n                value = this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt, this._cancelTaskCurrZone, targetZone, task);\n            }\n            else {\n                if (!task.cancelFn) {\n                    throw Error('Task is not cancelable');\n                }\n                value = task.cancelFn(task);\n            }\n            return value;\n        };\n        ZoneDelegate.prototype.hasTask = function (targetZone, isEmpty) {\n            // hasTask should not throw error so other ZoneDelegate\n            // can still trigger hasTask callback\n            try {\n                this._hasTaskZS &&\n                    this._hasTaskZS.onHasTask(this._hasTaskDlgt, this._hasTaskCurrZone, targetZone, isEmpty);\n            }\n            catch (err) {\n                this.handleError(targetZone, err);\n            }\n        };\n        ZoneDelegate.prototype._updateTaskCount = function (type, count) {\n            var counts = this._taskCounts;\n            var prev = counts[type];\n            var next = counts[type] = prev + count;\n            if (next < 0) {\n                throw new Error('More tasks executed then were scheduled.');\n            }\n            if (prev == 0 || next == 0) {\n                var isEmpty = {\n                    microTask: counts['microTask'] > 0,\n                    macroTask: counts['macroTask'] > 0,\n                    eventTask: counts['eventTask'] > 0,\n                    change: type\n                };\n                this.hasTask(this.zone, isEmpty);\n            }\n        };\n        return ZoneDelegate;\n    }());\n    var ZoneTask = /** @class */ (function () {\n        function ZoneTask(type, source, callback, options, scheduleFn, cancelFn) {\n            this._zone = null;\n            this.runCount = 0;\n            this._zoneDelegates = null;\n            this._state = 'notScheduled';\n            this.type = type;\n            this.source = source;\n            this.data = options;\n            this.scheduleFn = scheduleFn;\n            this.cancelFn = cancelFn;\n            this.callback = callback;\n            var self = this;\n            // TODO: @JiaLiPassion options should have interface\n            if (type === eventTask && options && options.useG) {\n                this.invoke = ZoneTask.invokeTask;\n            }\n            else {\n                this.invoke = function () {\n                    return ZoneTask.invokeTask.call(global, self, this, arguments);\n                };\n            }\n        }\n        ZoneTask.invokeTask = function (task, target, args) {\n            if (!task) {\n                task = this;\n            }\n            _numberOfNestedTaskFrames++;\n            try {\n                task.runCount++;\n                return task.zone.runTask(task, target, args);\n            }\n            finally {\n                if (_numberOfNestedTaskFrames == 1) {\n                    drainMicroTaskQueue();\n                }\n                _numberOfNestedTaskFrames--;\n            }\n        };\n        Object.defineProperty(ZoneTask.prototype, \"zone\", {\n            get: function () {\n                return this._zone;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        Object.defineProperty(ZoneTask.prototype, \"state\", {\n            get: function () {\n                return this._state;\n            },\n            enumerable: true,\n            configurable: true\n        });\n        ZoneTask.prototype.cancelScheduleRequest = function () {\n            this._transitionTo(notScheduled, scheduling);\n        };\n        ZoneTask.prototype._transitionTo = function (toState, fromState1, fromState2) {\n            if (this._state === fromState1 || this._state === fromState2) {\n                this._state = toState;\n                if (toState == notScheduled) {\n                    this._zoneDelegates = null;\n                }\n            }\n            else {\n                throw new Error(this.type + \" '\" + this.source + \"': can not transition to '\" + toState + \"', expecting state '\" + fromState1 + \"'\" + (fromState2 ? ' or \\'' + fromState2 + '\\'' : '') + \", was '\" + this._state + \"'.\");\n            }\n        };\n        ZoneTask.prototype.toString = function () {\n            if (this.data && typeof this.data.handleId !== 'undefined') {\n                return this.data.handleId.toString();\n            }\n            else {\n                return Object.prototype.toString.call(this);\n            }\n        };\n        // add toJSON method to prevent cyclic error when\n        // call JSON.stringify(zoneTask)\n        ZoneTask.prototype.toJSON = function () {\n            return {\n                type: this.type,\n                state: this.state,\n                source: this.source,\n                zone: this.zone.name,\n                runCount: this.runCount\n            };\n        };\n        return ZoneTask;\n    }());\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  MICROTASK QUEUE\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    var symbolSetTimeout = __symbol__('setTimeout');\n    var symbolPromise = __symbol__('Promise');\n    var symbolThen = __symbol__('then');\n    var _microTaskQueue = [];\n    var _isDrainingMicrotaskQueue = false;\n    var nativeMicroTaskQueuePromise;\n    function scheduleMicroTask(task) {\n        // if we are not running in any task, and there has not been anything scheduled\n        // we must bootstrap the initial task creation by manually scheduling the drain\n        if (_numberOfNestedTaskFrames === 0 && _microTaskQueue.length === 0) {\n            // We are not running in Task, so we need to kickstart the microtask queue.\n            if (!nativeMicroTaskQueuePromise) {\n                if (global[symbolPromise]) {\n                    nativeMicroTaskQueuePromise = global[symbolPromise].resolve(0);\n                }\n            }\n            if (nativeMicroTaskQueuePromise) {\n                var nativeThen = nativeMicroTaskQueuePromise[symbolThen];\n                if (!nativeThen) {\n                    // native Promise is not patchable, we need to use `then` directly\n                    // issue 1078\n                    nativeThen = nativeMicroTaskQueuePromise['then'];\n                }\n                nativeThen.call(nativeMicroTaskQueuePromise, drainMicroTaskQueue);\n            }\n            else {\n                global[symbolSetTimeout](drainMicroTaskQueue, 0);\n            }\n        }\n        task && _microTaskQueue.push(task);\n    }\n    function drainMicroTaskQueue() {\n        if (!_isDrainingMicrotaskQueue) {\n            _isDrainingMicrotaskQueue = true;\n            while (_microTaskQueue.length) {\n                var queue = _microTaskQueue;\n                _microTaskQueue = [];\n                for (var i = 0; i < queue.length; i++) {\n                    var task = queue[i];\n                    try {\n                        task.zone.runTask(task, null, null);\n                    }\n                    catch (error) {\n                        _api.onUnhandledError(error);\n                    }\n                }\n            }\n            _api.microtaskDrainDone();\n            _isDrainingMicrotaskQueue = false;\n        }\n    }\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    ///  BOOTSTRAP\n    //////////////////////////////////////////////////////\n    //////////////////////////////////////////////////////\n    var NO_ZONE = { name: 'NO ZONE' };\n    var notScheduled = 'notScheduled', scheduling = 'scheduling', scheduled = 'scheduled', running = 'running', canceling = 'canceling', unknown = 'unknown';\n    var microTask = 'microTask', macroTask = 'macroTask', eventTask = 'eventTask';\n    var patches = {};\n    var _api = {\n        symbol: __symbol__,\n        currentZoneFrame: function () { return _currentZoneFrame; },\n        onUnhandledError: noop,\n        microtaskDrainDone: noop,\n        scheduleMicroTask: scheduleMicroTask,\n        showUncaughtError: function () { return !Zone[__symbol__('ignoreConsoleErrorUncaughtError')]; },\n        patchEventTarget: function () { return []; },\n        patchOnProperties: noop,\n        patchMethod: function () { return noop; },\n        bindArguments: function () { return []; },\n        patchThen: function () { return noop; },\n        patchMacroTask: function () { return noop; },\n        setNativePromise: function (NativePromise) {\n            // sometimes NativePromise.resolve static function\n            // is not ready yet, (such as core-js/es6.promise)\n            // so we need to check here.\n            if (NativePromise && typeof NativePromise.resolve === 'function') {\n                nativeMicroTaskQueuePromise = NativePromise.resolve(0);\n            }\n        },\n        patchEventPrototype: function () { return noop; },\n        isIEOrEdge: function () { return false; },\n        getGlobalObjects: function () { return undefined; },\n        ObjectDefineProperty: function () { return noop; },\n        ObjectGetOwnPropertyDescriptor: function () { return undefined; },\n        ObjectCreate: function () { return undefined; },\n        ArraySlice: function () { return []; },\n        patchClass: function () { return noop; },\n        wrapWithCurrentZone: function () { return noop; },\n        filterProperties: function () { return []; },\n        attachOriginToPatched: function () { return noop; },\n        _redefineProperty: function () { return noop; },\n        patchCallbacks: function () { return noop; }\n    };\n    var _currentZoneFrame = { parent: null, zone: new Zone(null, null) };\n    var _currentTask = null;\n    var _numberOfNestedTaskFrames = 0;\n    function noop() { }\n    function __symbol__(name) {\n        return '__zone_symbol__' + name;\n    }\n    performanceMeasure('Zone', 'Zone');\n    return global['Zone'] = Zone;\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n\nvar __values = (undefined && undefined.__values) || function (o) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator], i = 0;\n    if (m) return m.call(o);\n    return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n};\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('ZoneAwarePromise', function (global, Zone, api) {\n    var ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n    var ObjectDefineProperty = Object.defineProperty;\n    function readableObjectToString(obj) {\n        if (obj && obj.toString === Object.prototype.toString) {\n            var className = obj.constructor && obj.constructor.name;\n            return (className ? className : '') + ': ' + JSON.stringify(obj);\n        }\n        return obj ? obj.toString() : Object.prototype.toString.call(obj);\n    }\n    var __symbol__ = api.symbol;\n    var _uncaughtPromiseErrors = [];\n    var symbolPromise = __symbol__('Promise');\n    var symbolThen = __symbol__('then');\n    var creationTrace = '__creationTrace__';\n    api.onUnhandledError = function (e) {\n        if (api.showUncaughtError()) {\n            var rejection = e && e.rejection;\n            if (rejection) {\n                console.error('Unhandled Promise rejection:', rejection instanceof Error ? rejection.message : rejection, '; Zone:', e.zone.name, '; Task:', e.task && e.task.source, '; Value:', rejection, rejection instanceof Error ? rejection.stack : undefined);\n            }\n            else {\n                console.error(e);\n            }\n        }\n    };\n    api.microtaskDrainDone = function () {\n        while (_uncaughtPromiseErrors.length) {\n            var _loop_1 = function () {\n                var uncaughtPromiseError = _uncaughtPromiseErrors.shift();\n                try {\n                    uncaughtPromiseError.zone.runGuarded(function () {\n                        throw uncaughtPromiseError;\n                    });\n                }\n                catch (error) {\n                    handleUnhandledRejection(error);\n                }\n            };\n            while (_uncaughtPromiseErrors.length) {\n                _loop_1();\n            }\n        }\n    };\n    var UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL = __symbol__('unhandledPromiseRejectionHandler');\n    function handleUnhandledRejection(e) {\n        api.onUnhandledError(e);\n        try {\n            var handler = Zone[UNHANDLED_PROMISE_REJECTION_HANDLER_SYMBOL];\n            if (handler && typeof handler === 'function') {\n                handler.call(this, e);\n            }\n        }\n        catch (err) {\n        }\n    }\n    function isThenable(value) {\n        return value && value.then;\n    }\n    function forwardResolution(value) {\n        return value;\n    }\n    function forwardRejection(rejection) {\n        return ZoneAwarePromise.reject(rejection);\n    }\n    var symbolState = __symbol__('state');\n    var symbolValue = __symbol__('value');\n    var symbolFinally = __symbol__('finally');\n    var symbolParentPromiseValue = __symbol__('parentPromiseValue');\n    var symbolParentPromiseState = __symbol__('parentPromiseState');\n    var source = 'Promise.then';\n    var UNRESOLVED = null;\n    var RESOLVED = true;\n    var REJECTED = false;\n    var REJECTED_NO_CATCH = 0;\n    function makeResolver(promise, state) {\n        return function (v) {\n            try {\n                resolvePromise(promise, state, v);\n            }\n            catch (err) {\n                resolvePromise(promise, false, err);\n            }\n            // Do not return value or you will break the Promise spec.\n        };\n    }\n    var once = function () {\n        var wasCalled = false;\n        return function wrapper(wrappedFunction) {\n            return function () {\n                if (wasCalled) {\n                    return;\n                }\n                wasCalled = true;\n                wrappedFunction.apply(null, arguments);\n            };\n        };\n    };\n    var TYPE_ERROR = 'Promise resolved with itself';\n    var CURRENT_TASK_TRACE_SYMBOL = __symbol__('currentTaskTrace');\n    // Promise Resolution\n    function resolvePromise(promise, state, value) {\n        var onceWrapper = once();\n        if (promise === value) {\n            throw new TypeError(TYPE_ERROR);\n        }\n        if (promise[symbolState] === UNRESOLVED) {\n            // should only get value.then once based on promise spec.\n            var then = null;\n            try {\n                if (typeof value === 'object' || typeof value === 'function') {\n                    then = value && value.then;\n                }\n            }\n            catch (err) {\n                onceWrapper(function () {\n                    resolvePromise(promise, false, err);\n                })();\n                return promise;\n            }\n            // if (value instanceof ZoneAwarePromise) {\n            if (state !== REJECTED && value instanceof ZoneAwarePromise &&\n                value.hasOwnProperty(symbolState) && value.hasOwnProperty(symbolValue) &&\n                value[symbolState] !== UNRESOLVED) {\n                clearRejectedNoCatch(value);\n                resolvePromise(promise, value[symbolState], value[symbolValue]);\n            }\n            else if (state !== REJECTED && typeof then === 'function') {\n                try {\n                    then.call(value, onceWrapper(makeResolver(promise, state)), onceWrapper(makeResolver(promise, false)));\n                }\n                catch (err) {\n                    onceWrapper(function () {\n                        resolvePromise(promise, false, err);\n                    })();\n                }\n            }\n            else {\n                promise[symbolState] = state;\n                var queue = promise[symbolValue];\n                promise[symbolValue] = value;\n                if (promise[symbolFinally] === symbolFinally) {\n                    // the promise is generated by Promise.prototype.finally\n                    if (state === RESOLVED) {\n                        // the state is resolved, should ignore the value\n                        // and use parent promise value\n                        promise[symbolState] = promise[symbolParentPromiseState];\n                        promise[symbolValue] = promise[symbolParentPromiseValue];\n                    }\n                }\n                // record task information in value when error occurs, so we can\n                // do some additional work such as render longStackTrace\n                if (state === REJECTED && value instanceof Error) {\n                    // check if longStackTraceZone is here\n                    var trace = Zone.currentTask && Zone.currentTask.data &&\n                        Zone.currentTask.data[creationTrace];\n                    if (trace) {\n                        // only keep the long stack trace into error when in longStackTraceZone\n                        ObjectDefineProperty(value, CURRENT_TASK_TRACE_SYMBOL, { configurable: true, enumerable: false, writable: true, value: trace });\n                    }\n                }\n                for (var i = 0; i < queue.length;) {\n                    scheduleResolveOrReject(promise, queue[i++], queue[i++], queue[i++], queue[i++]);\n                }\n                if (queue.length == 0 && state == REJECTED) {\n                    promise[symbolState] = REJECTED_NO_CATCH;\n                    try {\n                        // try to print more readable error log\n                        throw new Error('Uncaught (in promise): ' + readableObjectToString(value) +\n                            (value && value.stack ? '\\n' + value.stack : ''));\n                    }\n                    catch (err) {\n                        var error_1 = err;\n                        error_1.rejection = value;\n                        error_1.promise = promise;\n                        error_1.zone = Zone.current;\n                        error_1.task = Zone.currentTask;\n                        _uncaughtPromiseErrors.push(error_1);\n                        api.scheduleMicroTask(); // to make sure that it is running\n                    }\n                }\n            }\n        }\n        // Resolving an already resolved promise is a noop.\n        return promise;\n    }\n    var REJECTION_HANDLED_HANDLER = __symbol__('rejectionHandledHandler');\n    function clearRejectedNoCatch(promise) {\n        if (promise[symbolState] === REJECTED_NO_CATCH) {\n            // if the promise is rejected no catch status\n            // and queue.length > 0, means there is a error handler\n            // here to handle the rejected promise, we should trigger\n            // windows.rejectionhandled eventHandler or nodejs rejectionHandled\n            // eventHandler\n            try {\n                var handler = Zone[REJECTION_HANDLED_HANDLER];\n                if (handler && typeof handler === 'function') {\n                    handler.call(this, { rejection: promise[symbolValue], promise: promise });\n                }\n            }\n            catch (err) {\n            }\n            promise[symbolState] = REJECTED;\n            for (var i = 0; i < _uncaughtPromiseErrors.length; i++) {\n                if (promise === _uncaughtPromiseErrors[i].promise) {\n                    _uncaughtPromiseErrors.splice(i, 1);\n                }\n            }\n        }\n    }\n    function scheduleResolveOrReject(promise, zone, chainPromise, onFulfilled, onRejected) {\n        clearRejectedNoCatch(promise);\n        var promiseState = promise[symbolState];\n        var delegate = promiseState ?\n            (typeof onFulfilled === 'function') ? onFulfilled : forwardResolution :\n            (typeof onRejected === 'function') ? onRejected : forwardRejection;\n        zone.scheduleMicroTask(source, function () {\n            try {\n                var parentPromiseValue = promise[symbolValue];\n                var isFinallyPromise = chainPromise && symbolFinally === chainPromise[symbolFinally];\n                if (isFinallyPromise) {\n                    // if the promise is generated from finally call, keep parent promise's state and value\n                    chainPromise[symbolParentPromiseValue] = parentPromiseValue;\n                    chainPromise[symbolParentPromiseState] = promiseState;\n                }\n                // should not pass value to finally callback\n                var value = zone.run(delegate, undefined, isFinallyPromise && delegate !== forwardRejection && delegate !== forwardResolution ?\n                    [] :\n                    [parentPromiseValue]);\n                resolvePromise(chainPromise, true, value);\n            }\n            catch (error) {\n                // if error occurs, should always return this error\n                resolvePromise(chainPromise, false, error);\n            }\n        }, chainPromise);\n    }\n    var ZONE_AWARE_PROMISE_TO_STRING = 'function ZoneAwarePromise() { [native code] }';\n    var ZoneAwarePromise = /** @class */ (function () {\n        function ZoneAwarePromise(executor) {\n            var promise = this;\n            if (!(promise instanceof ZoneAwarePromise)) {\n                throw new Error('Must be an instanceof Promise.');\n            }\n            promise[symbolState] = UNRESOLVED;\n            promise[symbolValue] = []; // queue;\n            try {\n                executor && executor(makeResolver(promise, RESOLVED), makeResolver(promise, REJECTED));\n            }\n            catch (error) {\n                resolvePromise(promise, false, error);\n            }\n        }\n        ZoneAwarePromise.toString = function () {\n            return ZONE_AWARE_PROMISE_TO_STRING;\n        };\n        ZoneAwarePromise.resolve = function (value) {\n            return resolvePromise(new this(null), RESOLVED, value);\n        };\n        ZoneAwarePromise.reject = function (error) {\n            return resolvePromise(new this(null), REJECTED, error);\n        };\n        ZoneAwarePromise.race = function (values) {\n            var e_1, _a;\n            var resolve;\n            var reject;\n            var promise = new this(function (res, rej) {\n                resolve = res;\n                reject = rej;\n            });\n            function onResolve(value) {\n                resolve(value);\n            }\n            function onReject(error) {\n                reject(error);\n            }\n            try {\n                for (var values_1 = __values(values), values_1_1 = values_1.next(); !values_1_1.done; values_1_1 = values_1.next()) {\n                    var value = values_1_1.value;\n                    if (!isThenable(value)) {\n                        value = this.resolve(value);\n                    }\n                    value.then(onResolve, onReject);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (values_1_1 && !values_1_1.done && (_a = values_1.return)) _a.call(values_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return promise;\n        };\n        ZoneAwarePromise.all = function (values) {\n            var e_2, _a;\n            var resolve;\n            var reject;\n            var promise = new this(function (res, rej) {\n                resolve = res;\n                reject = rej;\n            });\n            // Start at 2 to prevent prematurely resolving if .then is called immediately.\n            var unresolvedCount = 2;\n            var valueIndex = 0;\n            var resolvedValues = [];\n            var _loop_2 = function (value) {\n                if (!isThenable(value)) {\n                    value = this_1.resolve(value);\n                }\n                var curValueIndex = valueIndex;\n                value.then(function (value) {\n                    resolvedValues[curValueIndex] = value;\n                    unresolvedCount--;\n                    if (unresolvedCount === 0) {\n                        resolve(resolvedValues);\n                    }\n                }, reject);\n                unresolvedCount++;\n                valueIndex++;\n            };\n            var this_1 = this;\n            try {\n                for (var values_2 = __values(values), values_2_1 = values_2.next(); !values_2_1.done; values_2_1 = values_2.next()) {\n                    var value = values_2_1.value;\n                    _loop_2(value);\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (values_2_1 && !values_2_1.done && (_a = values_2.return)) _a.call(values_2);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            // Make the unresolvedCount zero-based again.\n            unresolvedCount -= 2;\n            if (unresolvedCount === 0) {\n                resolve(resolvedValues);\n            }\n            return promise;\n        };\n        Object.defineProperty(ZoneAwarePromise.prototype, Symbol.toStringTag, {\n            get: function () {\n                return 'Promise';\n            },\n            enumerable: true,\n            configurable: true\n        });\n        ZoneAwarePromise.prototype.then = function (onFulfilled, onRejected) {\n            var chainPromise = new this.constructor(null);\n            var zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFulfilled, onRejected);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFulfilled, onRejected);\n            }\n            return chainPromise;\n        };\n        ZoneAwarePromise.prototype.catch = function (onRejected) {\n            return this.then(null, onRejected);\n        };\n        ZoneAwarePromise.prototype.finally = function (onFinally) {\n            var chainPromise = new this.constructor(null);\n            chainPromise[symbolFinally] = symbolFinally;\n            var zone = Zone.current;\n            if (this[symbolState] == UNRESOLVED) {\n                this[symbolValue].push(zone, chainPromise, onFinally, onFinally);\n            }\n            else {\n                scheduleResolveOrReject(this, zone, chainPromise, onFinally, onFinally);\n            }\n            return chainPromise;\n        };\n        return ZoneAwarePromise;\n    }());\n    // Protect against aggressive optimizers dropping seemingly unused properties.\n    // E.g. Closure Compiler in advanced mode.\n    ZoneAwarePromise['resolve'] = ZoneAwarePromise.resolve;\n    ZoneAwarePromise['reject'] = ZoneAwarePromise.reject;\n    ZoneAwarePromise['race'] = ZoneAwarePromise.race;\n    ZoneAwarePromise['all'] = ZoneAwarePromise.all;\n    var NativePromise = global[symbolPromise] = global['Promise'];\n    var ZONE_AWARE_PROMISE = Zone.__symbol__('ZoneAwarePromise');\n    var desc = ObjectGetOwnPropertyDescriptor(global, 'Promise');\n    if (!desc || desc.configurable) {\n        desc && delete desc.writable;\n        desc && delete desc.value;\n        if (!desc) {\n            desc = { configurable: true, enumerable: true };\n        }\n        desc.get = function () {\n            // if we already set ZoneAwarePromise, use patched one\n            // otherwise return native one.\n            return global[ZONE_AWARE_PROMISE] ? global[ZONE_AWARE_PROMISE] : global[symbolPromise];\n        };\n        desc.set = function (NewNativePromise) {\n            if (NewNativePromise === ZoneAwarePromise) {\n                // if the NewNativePromise is ZoneAwarePromise\n                // save to global\n                global[ZONE_AWARE_PROMISE] = NewNativePromise;\n            }\n            else {\n                // if the NewNativePromise is not ZoneAwarePromise\n                // for example: after load zone.js, some library just\n                // set es6-promise to global, if we set it to global\n                // directly, assertZonePatched will fail and angular\n                // will not loaded, so we just set the NewNativePromise\n                // to global[symbolPromise], so the result is just like\n                // we load ES6 Promise before zone.js\n                global[symbolPromise] = NewNativePromise;\n                if (!NewNativePromise.prototype[symbolThen]) {\n                    patchThen(NewNativePromise);\n                }\n                api.setNativePromise(NewNativePromise);\n            }\n        };\n        ObjectDefineProperty(global, 'Promise', desc);\n    }\n    global['Promise'] = ZoneAwarePromise;\n    var symbolThenPatched = __symbol__('thenPatched');\n    function patchThen(Ctor) {\n        var proto = Ctor.prototype;\n        var prop = ObjectGetOwnPropertyDescriptor(proto, 'then');\n        if (prop && (prop.writable === false || !prop.configurable)) {\n            // check Ctor.prototype.then propertyDescriptor is writable or not\n            // in meteor env, writable is false, we should ignore such case\n            return;\n        }\n        var originalThen = proto.then;\n        // Keep a reference to the original method.\n        proto[symbolThen] = originalThen;\n        Ctor.prototype.then = function (onResolve, onReject) {\n            var _this = this;\n            var wrapped = new ZoneAwarePromise(function (resolve, reject) {\n                originalThen.call(_this, resolve, reject);\n            });\n            return wrapped.then(onResolve, onReject);\n        };\n        Ctor[symbolThenPatched] = true;\n    }\n    api.patchThen = patchThen;\n    function zoneify(fn) {\n        return function () {\n            var resultPromise = fn.apply(this, arguments);\n            if (resultPromise instanceof ZoneAwarePromise) {\n                return resultPromise;\n            }\n            var ctor = resultPromise.constructor;\n            if (!ctor[symbolThenPatched]) {\n                patchThen(ctor);\n            }\n            return resultPromise;\n        };\n    }\n    if (NativePromise) {\n        patchThen(NativePromise);\n        var fetch_1 = global['fetch'];\n        if (typeof fetch_1 == 'function') {\n            global[api.symbol('fetch')] = fetch_1;\n            global['fetch'] = zoneify(fetch_1);\n        }\n    }\n    // This is not part of public API, but it is useful for tests, so we expose it.\n    Promise[Zone.__symbol__('uncaughtPromiseErrors')] = _uncaughtPromiseErrors;\n    return ZoneAwarePromise;\n});\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * Suppress closure compiler errors about unknown 'Zone' variable\n * @fileoverview\n * @suppress {undefinedVars,globalThis,missingRequire}\n */\n// issue #989, to reduce bundle size, use short name\n/** Object.getOwnPropertyDescriptor */\nvar ObjectGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n/** Object.defineProperty */\nvar ObjectDefineProperty = Object.defineProperty;\n/** Object.getPrototypeOf */\nvar ObjectGetPrototypeOf = Object.getPrototypeOf;\n/** Object.create */\nvar ObjectCreate = Object.create;\n/** Array.prototype.slice */\nvar ArraySlice = Array.prototype.slice;\n/** addEventListener string const */\nvar ADD_EVENT_LISTENER_STR = 'addEventListener';\n/** removeEventListener string const */\nvar REMOVE_EVENT_LISTENER_STR = 'removeEventListener';\n/** zoneSymbol addEventListener */\nvar ZONE_SYMBOL_ADD_EVENT_LISTENER = Zone.__symbol__(ADD_EVENT_LISTENER_STR);\n/** zoneSymbol removeEventListener */\nvar ZONE_SYMBOL_REMOVE_EVENT_LISTENER = Zone.__symbol__(REMOVE_EVENT_LISTENER_STR);\n/** true string const */\nvar TRUE_STR = 'true';\n/** false string const */\nvar FALSE_STR = 'false';\n/** __zone_symbol__ string const */\nvar ZONE_SYMBOL_PREFIX = '__zone_symbol__';\nfunction wrapWithCurrentZone(callback, source) {\n    return Zone.current.wrap(callback, source);\n}\nfunction scheduleMacroTaskWithCurrentZone(source, callback, data, customSchedule, customCancel) {\n    return Zone.current.scheduleMacroTask(source, callback, data, customSchedule, customCancel);\n}\nvar zoneSymbol = Zone.__symbol__;\nvar isWindowExists = typeof window !== 'undefined';\nvar internalWindow = isWindowExists ? window : undefined;\nvar _global = isWindowExists && internalWindow || typeof self === 'object' && self || global;\nvar REMOVE_ATTRIBUTE = 'removeAttribute';\nvar NULL_ON_PROP_VALUE = [null];\nfunction bindArguments(args, source) {\n    for (var i = args.length - 1; i >= 0; i--) {\n        if (typeof args[i] === 'function') {\n            args[i] = wrapWithCurrentZone(args[i], source + '_' + i);\n        }\n    }\n    return args;\n}\nfunction patchPrototype(prototype, fnNames) {\n    var source = prototype.constructor['name'];\n    var _loop_1 = function (i) {\n        var name_1 = fnNames[i];\n        var delegate = prototype[name_1];\n        if (delegate) {\n            var prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, name_1);\n            if (!isPropertyWritable(prototypeDesc)) {\n                return \"continue\";\n            }\n            prototype[name_1] = (function (delegate) {\n                var patched = function () {\n                    return delegate.apply(this, bindArguments(arguments, source + '.' + name_1));\n                };\n                attachOriginToPatched(patched, delegate);\n                return patched;\n            })(delegate);\n        }\n    };\n    for (var i = 0; i < fnNames.length; i++) {\n        _loop_1(i);\n    }\n}\nfunction isPropertyWritable(propertyDesc) {\n    if (!propertyDesc) {\n        return true;\n    }\n    if (propertyDesc.writable === false) {\n        return false;\n    }\n    return !(typeof propertyDesc.get === 'function' && typeof propertyDesc.set === 'undefined');\n}\nvar isWebWorker = (typeof WorkerGlobalScope !== 'undefined' && self instanceof WorkerGlobalScope);\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nvar isNode = (!('nw' in _global) && typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]');\nvar isBrowser = !isNode && !isWebWorker && !!(isWindowExists && internalWindow['HTMLElement']);\n// we are in electron of nw, so we are both browser and nodejs\n// Make sure to access `process` through `_global` so that WebPack does not accidentally browserify\n// this code.\nvar isMix = typeof _global.process !== 'undefined' &&\n    {}.toString.call(_global.process) === '[object process]' && !isWebWorker &&\n    !!(isWindowExists && internalWindow['HTMLElement']);\nvar zoneSymbolEventNames = {};\nvar wrapFn = function (event) {\n    // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n    // event will be undefined, so we need to use window.event\n    event = event || _global.event;\n    if (!event) {\n        return;\n    }\n    var eventNameSymbol = zoneSymbolEventNames[event.type];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames[event.type] = zoneSymbol('ON_PROPERTY' + event.type);\n    }\n    var target = this || event.target || _global;\n    var listener = target[eventNameSymbol];\n    var result;\n    if (isBrowser && target === internalWindow && event.type === 'error') {\n        // window.onerror have different signiture\n        // https://developer.mozilla.org/en-US/docs/Web/API/GlobalEventHandlers/onerror#window.onerror\n        // and onerror callback will prevent default when callback return true\n        var errorEvent = event;\n        result = listener &&\n            listener.call(this, errorEvent.message, errorEvent.filename, errorEvent.lineno, errorEvent.colno, errorEvent.error);\n        if (result === true) {\n            event.preventDefault();\n        }\n    }\n    else {\n        result = listener && listener.apply(this, arguments);\n        if (result != undefined && !result) {\n            event.preventDefault();\n        }\n    }\n    return result;\n};\nfunction patchProperty(obj, prop, prototype) {\n    var desc = ObjectGetOwnPropertyDescriptor(obj, prop);\n    if (!desc && prototype) {\n        // when patch window object, use prototype to check prop exist or not\n        var prototypeDesc = ObjectGetOwnPropertyDescriptor(prototype, prop);\n        if (prototypeDesc) {\n            desc = { enumerable: true, configurable: true };\n        }\n    }\n    // if the descriptor not exists or is not configurable\n    // just return\n    if (!desc || !desc.configurable) {\n        return;\n    }\n    var onPropPatchedSymbol = zoneSymbol('on' + prop + 'patched');\n    if (obj.hasOwnProperty(onPropPatchedSymbol) && obj[onPropPatchedSymbol]) {\n        return;\n    }\n    // A property descriptor cannot have getter/setter and be writable\n    // deleting the writable and value properties avoids this error:\n    //\n    // TypeError: property descriptors must not specify a value or be writable when a\n    // getter or setter has been specified\n    delete desc.writable;\n    delete desc.value;\n    var originalDescGet = desc.get;\n    var originalDescSet = desc.set;\n    // substr(2) cuz 'onclick' -> 'click', etc\n    var eventName = prop.substr(2);\n    var eventNameSymbol = zoneSymbolEventNames[eventName];\n    if (!eventNameSymbol) {\n        eventNameSymbol = zoneSymbolEventNames[eventName] = zoneSymbol('ON_PROPERTY' + eventName);\n    }\n    desc.set = function (newValue) {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        var target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return;\n        }\n        var previousValue = target[eventNameSymbol];\n        if (previousValue) {\n            target.removeEventListener(eventName, wrapFn);\n        }\n        // issue #978, when onload handler was added before loading zone.js\n        // we should remove it with originalDescSet\n        if (originalDescSet) {\n            originalDescSet.apply(target, NULL_ON_PROP_VALUE);\n        }\n        if (typeof newValue === 'function') {\n            target[eventNameSymbol] = newValue;\n            target.addEventListener(eventName, wrapFn, false);\n        }\n        else {\n            target[eventNameSymbol] = null;\n        }\n    };\n    // The getter would return undefined for unassigned properties but the default value of an\n    // unassigned property is null\n    desc.get = function () {\n        // in some of windows's onproperty callback, this is undefined\n        // so we need to check it\n        var target = this;\n        if (!target && obj === _global) {\n            target = _global;\n        }\n        if (!target) {\n            return null;\n        }\n        var listener = target[eventNameSymbol];\n        if (listener) {\n            return listener;\n        }\n        else if (originalDescGet) {\n            // result will be null when use inline event attribute,\n            // such as <button onclick=\"func();\">OK</button>\n            // because the onclick function is internal raw uncompiled handler\n            // the onclick will be evaluated when first time event was triggered or\n            // the property is accessed, https://github.com/angular/zone.js/issues/525\n            // so we should use original native get to retrieve the handler\n            var value = originalDescGet && originalDescGet.call(this);\n            if (value) {\n                desc.set.call(this, value);\n                if (typeof target[REMOVE_ATTRIBUTE] === 'function') {\n                    target.removeAttribute(prop);\n                }\n                return value;\n            }\n        }\n        return null;\n    };\n    ObjectDefineProperty(obj, prop, desc);\n    obj[onPropPatchedSymbol] = true;\n}\nfunction patchOnProperties(obj, properties, prototype) {\n    if (properties) {\n        for (var i = 0; i < properties.length; i++) {\n            patchProperty(obj, 'on' + properties[i], prototype);\n        }\n    }\n    else {\n        var onProperties = [];\n        for (var prop in obj) {\n            if (prop.substr(0, 2) == 'on') {\n                onProperties.push(prop);\n            }\n        }\n        for (var j = 0; j < onProperties.length; j++) {\n            patchProperty(obj, onProperties[j], prototype);\n        }\n    }\n}\nvar originalInstanceKey = zoneSymbol('originalInstance');\n// wrap some native API on `window`\nfunction patchClass(className) {\n    var OriginalClass = _global[className];\n    if (!OriginalClass)\n        return;\n    // keep original class in global\n    _global[zoneSymbol(className)] = OriginalClass;\n    _global[className] = function () {\n        var a = bindArguments(arguments, className);\n        switch (a.length) {\n            case 0:\n                this[originalInstanceKey] = new OriginalClass();\n                break;\n            case 1:\n                this[originalInstanceKey] = new OriginalClass(a[0]);\n                break;\n            case 2:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1]);\n                break;\n            case 3:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2]);\n                break;\n            case 4:\n                this[originalInstanceKey] = new OriginalClass(a[0], a[1], a[2], a[3]);\n                break;\n            default:\n                throw new Error('Arg list too long.');\n        }\n    };\n    // attach original delegate to patched function\n    attachOriginToPatched(_global[className], OriginalClass);\n    var instance = new OriginalClass(function () { });\n    var prop;\n    for (prop in instance) {\n        // https://bugs.webkit.org/show_bug.cgi?id=44721\n        if (className === 'XMLHttpRequest' && prop === 'responseBlob')\n            continue;\n        (function (prop) {\n            if (typeof instance[prop] === 'function') {\n                _global[className].prototype[prop] = function () {\n                    return this[originalInstanceKey][prop].apply(this[originalInstanceKey], arguments);\n                };\n            }\n            else {\n                ObjectDefineProperty(_global[className].prototype, prop, {\n                    set: function (fn) {\n                        if (typeof fn === 'function') {\n                            this[originalInstanceKey][prop] = wrapWithCurrentZone(fn, className + '.' + prop);\n                            // keep callback in wrapped function so we can\n                            // use it in Function.prototype.toString to return\n                            // the native one.\n                            attachOriginToPatched(this[originalInstanceKey][prop], fn);\n                        }\n                        else {\n                            this[originalInstanceKey][prop] = fn;\n                        }\n                    },\n                    get: function () {\n                        return this[originalInstanceKey][prop];\n                    }\n                });\n            }\n        }(prop));\n    }\n    for (prop in OriginalClass) {\n        if (prop !== 'prototype' && OriginalClass.hasOwnProperty(prop)) {\n            _global[className][prop] = OriginalClass[prop];\n        }\n    }\n}\nfunction copySymbolProperties(src, dest) {\n    if (typeof Object.getOwnPropertySymbols !== 'function') {\n        return;\n    }\n    var symbols = Object.getOwnPropertySymbols(src);\n    symbols.forEach(function (symbol) {\n        var desc = Object.getOwnPropertyDescriptor(src, symbol);\n        Object.defineProperty(dest, symbol, {\n            get: function () {\n                return src[symbol];\n            },\n            set: function (value) {\n                if (desc && (!desc.writable || typeof desc.set !== 'function')) {\n                    // if src[symbol] is not writable or not have a setter, just return\n                    return;\n                }\n                src[symbol] = value;\n            },\n            enumerable: desc ? desc.enumerable : true,\n            configurable: desc ? desc.configurable : true\n        });\n    });\n}\nvar shouldCopySymbolProperties = false;\n\nfunction patchMethod(target, name, patchFn) {\n    var proto = target;\n    while (proto && !proto.hasOwnProperty(name)) {\n        proto = ObjectGetPrototypeOf(proto);\n    }\n    if (!proto && target[name]) {\n        // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n        proto = target;\n    }\n    var delegateName = zoneSymbol(name);\n    var delegate = null;\n    if (proto && !(delegate = proto[delegateName])) {\n        delegate = proto[delegateName] = proto[name];\n        // check whether proto[name] is writable\n        // some property is readonly in safari, such as HtmlCanvasElement.prototype.toBlob\n        var desc = proto && ObjectGetOwnPropertyDescriptor(proto, name);\n        if (isPropertyWritable(desc)) {\n            var patchDelegate_1 = patchFn(delegate, delegateName, name);\n            proto[name] = function () {\n                return patchDelegate_1(this, arguments);\n            };\n            attachOriginToPatched(proto[name], delegate);\n            if (shouldCopySymbolProperties) {\n                copySymbolProperties(delegate, proto[name]);\n            }\n        }\n    }\n    return delegate;\n}\n// TODO: @JiaLiPassion, support cancel task later if necessary\nfunction patchMacroTask(obj, funcName, metaCreator) {\n    var setNative = null;\n    function scheduleTask(task) {\n        var data = task.data;\n        data.args[data.cbIdx] = function () {\n            task.invoke.apply(this, arguments);\n        };\n        setNative.apply(data.target, data.args);\n        return task;\n    }\n    setNative = patchMethod(obj, funcName, function (delegate) { return function (self, args) {\n        var meta = metaCreator(self, args);\n        if (meta.cbIdx >= 0 && typeof args[meta.cbIdx] === 'function') {\n            return scheduleMacroTaskWithCurrentZone(meta.name, args[meta.cbIdx], meta, scheduleTask);\n        }\n        else {\n            // cause an error by calling it directly.\n            return delegate.apply(self, args);\n        }\n    }; });\n}\n\nfunction attachOriginToPatched(patched, original) {\n    patched[zoneSymbol('OriginalDelegate')] = original;\n}\nvar isDetectedIEOrEdge = false;\nvar ieOrEdge = false;\nfunction isIE() {\n    try {\n        var ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1) {\n            return true;\n        }\n    }\n    catch (error) {\n    }\n    return false;\n}\nfunction isIEOrEdge() {\n    if (isDetectedIEOrEdge) {\n        return ieOrEdge;\n    }\n    isDetectedIEOrEdge = true;\n    try {\n        var ua = internalWindow.navigator.userAgent;\n        if (ua.indexOf('MSIE ') !== -1 || ua.indexOf('Trident/') !== -1 || ua.indexOf('Edge/') !== -1) {\n            ieOrEdge = true;\n        }\n    }\n    catch (error) {\n    }\n    return ieOrEdge;\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// override Function.prototype.toString to make zone.js patched function\n// look like native function\nZone.__load_patch('toString', function (global) {\n    // patch Func.prototype.toString to let them look like native\n    var originalFunctionToString = Function.prototype.toString;\n    var ORIGINAL_DELEGATE_SYMBOL = zoneSymbol('OriginalDelegate');\n    var PROMISE_SYMBOL = zoneSymbol('Promise');\n    var ERROR_SYMBOL = zoneSymbol('Error');\n    var newFunctionToString = function toString() {\n        if (typeof this === 'function') {\n            var originalDelegate = this[ORIGINAL_DELEGATE_SYMBOL];\n            if (originalDelegate) {\n                if (typeof originalDelegate === 'function') {\n                    return originalFunctionToString.call(originalDelegate);\n                }\n                else {\n                    return Object.prototype.toString.call(originalDelegate);\n                }\n            }\n            if (this === Promise) {\n                var nativePromise = global[PROMISE_SYMBOL];\n                if (nativePromise) {\n                    return originalFunctionToString.call(nativePromise);\n                }\n            }\n            if (this === Error) {\n                var nativeError = global[ERROR_SYMBOL];\n                if (nativeError) {\n                    return originalFunctionToString.call(nativeError);\n                }\n            }\n        }\n        return originalFunctionToString.call(this);\n    };\n    newFunctionToString[ORIGINAL_DELEGATE_SYMBOL] = originalFunctionToString;\n    Function.prototype.toString = newFunctionToString;\n    // patch Object.prototype.toString to let them look like native\n    var originalObjectToString = Object.prototype.toString;\n    var PROMISE_OBJECT_TO_STRING = '[object Promise]';\n    Object.prototype.toString = function () {\n        if (this instanceof Promise) {\n            return PROMISE_OBJECT_TO_STRING;\n        }\n        return originalObjectToString.call(this);\n    };\n});\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nvar passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n            }\n        });\n        window.addEventListener('test', options, options);\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\n// an identifier to tell ZoneTask do not create a new invoke closure\nvar OPTIMIZED_ZONE_EVENT_TASK_DATA = {\n    useG: true\n};\nvar zoneSymbolEventNames$1 = {};\nvar globalSources = {};\nvar EVENT_NAME_SYMBOL_REGX = /^__zone_symbol__(\\w+)(true|false)$/;\nvar IMMEDIATE_PROPAGATION_SYMBOL = ('__zone_symbol__propagationStopped');\nfunction patchEventTarget(_global, apis, patchOptions) {\n    var ADD_EVENT_LISTENER = (patchOptions && patchOptions.add) || ADD_EVENT_LISTENER_STR;\n    var REMOVE_EVENT_LISTENER = (patchOptions && patchOptions.rm) || REMOVE_EVENT_LISTENER_STR;\n    var LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.listeners) || 'eventListeners';\n    var REMOVE_ALL_LISTENERS_EVENT_LISTENER = (patchOptions && patchOptions.rmAll) || 'removeAllListeners';\n    var zoneSymbolAddEventListener = zoneSymbol(ADD_EVENT_LISTENER);\n    var ADD_EVENT_LISTENER_SOURCE = '.' + ADD_EVENT_LISTENER + ':';\n    var PREPEND_EVENT_LISTENER = 'prependListener';\n    var PREPEND_EVENT_LISTENER_SOURCE = '.' + PREPEND_EVENT_LISTENER + ':';\n    var invokeTask = function (task, target, event) {\n        // for better performance, check isRemoved which is set\n        // by removeEventListener\n        if (task.isRemoved) {\n            return;\n        }\n        var delegate = task.callback;\n        if (typeof delegate === 'object' && delegate.handleEvent) {\n            // create the bind version of handleEvent when invoke\n            task.callback = function (event) { return delegate.handleEvent(event); };\n            task.originalDelegate = delegate;\n        }\n        // invoke static task.invoke\n        task.invoke(task, target, [event]);\n        var options = task.options;\n        if (options && typeof options === 'object' && options.once) {\n            // if options.once is true, after invoke once remove listener here\n            // only browser need to do this, nodejs eventEmitter will cal removeListener\n            // inside EventEmitter.once\n            var delegate_1 = task.originalDelegate ? task.originalDelegate : task.callback;\n            target[REMOVE_EVENT_LISTENER].call(target, event.type, delegate_1, options);\n        }\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = false\n    var globalZoneAwareCallback = function (event) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        var target = this || event.target || _global;\n        var tasks = target[zoneSymbolEventNames$1[event.type][FALSE_STR]];\n        if (tasks) {\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                invokeTask(tasks[0], target, event);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                var copyTasks = tasks.slice();\n                for (var i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    invokeTask(copyTasks[i], target, event);\n                }\n            }\n        }\n    };\n    // global shared zoneAwareCallback to handle all event callback with capture = true\n    var globalZoneAwareCaptureCallback = function (event) {\n        // https://github.com/angular/zone.js/issues/911, in IE, sometimes\n        // event will be undefined, so we need to use window.event\n        event = event || _global.event;\n        if (!event) {\n            return;\n        }\n        // event.target is needed for Samsung TV and SourceBuffer\n        // || global is needed https://github.com/angular/zone.js/issues/190\n        var target = this || event.target || _global;\n        var tasks = target[zoneSymbolEventNames$1[event.type][TRUE_STR]];\n        if (tasks) {\n            // invoke all tasks which attached to current target with given event.type and capture = false\n            // for performance concern, if task.length === 1, just invoke\n            if (tasks.length === 1) {\n                invokeTask(tasks[0], target, event);\n            }\n            else {\n                // https://github.com/angular/zone.js/issues/836\n                // copy the tasks array before invoke, to avoid\n                // the callback will remove itself or other listener\n                var copyTasks = tasks.slice();\n                for (var i = 0; i < copyTasks.length; i++) {\n                    if (event && event[IMMEDIATE_PROPAGATION_SYMBOL] === true) {\n                        break;\n                    }\n                    invokeTask(copyTasks[i], target, event);\n                }\n            }\n        }\n    };\n    function patchEventTargetMethods(obj, patchOptions) {\n        if (!obj) {\n            return false;\n        }\n        var useGlobalCallback = true;\n        if (patchOptions && patchOptions.useG !== undefined) {\n            useGlobalCallback = patchOptions.useG;\n        }\n        var validateHandler = patchOptions && patchOptions.vh;\n        var checkDuplicate = true;\n        if (patchOptions && patchOptions.chkDup !== undefined) {\n            checkDuplicate = patchOptions.chkDup;\n        }\n        var returnTarget = false;\n        if (patchOptions && patchOptions.rt !== undefined) {\n            returnTarget = patchOptions.rt;\n        }\n        var proto = obj;\n        while (proto && !proto.hasOwnProperty(ADD_EVENT_LISTENER)) {\n            proto = ObjectGetPrototypeOf(proto);\n        }\n        if (!proto && obj[ADD_EVENT_LISTENER]) {\n            // somehow we did not find it, but we can see it. This happens on IE for Window properties.\n            proto = obj;\n        }\n        if (!proto) {\n            return false;\n        }\n        if (proto[zoneSymbolAddEventListener]) {\n            return false;\n        }\n        var eventNameToString = patchOptions && patchOptions.eventNameToString;\n        // a shared global taskData to pass data for scheduleEventTask\n        // so we do not need to create a new object just for pass some data\n        var taskData = {};\n        var nativeAddEventListener = proto[zoneSymbolAddEventListener] = proto[ADD_EVENT_LISTENER];\n        var nativeRemoveEventListener = proto[zoneSymbol(REMOVE_EVENT_LISTENER)] =\n            proto[REMOVE_EVENT_LISTENER];\n        var nativeListeners = proto[zoneSymbol(LISTENERS_EVENT_LISTENER)] =\n            proto[LISTENERS_EVENT_LISTENER];\n        var nativeRemoveAllListeners = proto[zoneSymbol(REMOVE_ALL_LISTENERS_EVENT_LISTENER)] =\n            proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER];\n        var nativePrependEventListener;\n        if (patchOptions && patchOptions.prepend) {\n            nativePrependEventListener = proto[zoneSymbol(patchOptions.prepend)] =\n                proto[patchOptions.prepend];\n        }\n        function checkIsPassive(task) {\n            if (!passiveSupported && typeof taskData.options !== 'boolean' &&\n                typeof taskData.options !== 'undefined' && taskData.options !== null) {\n                // options is a non-null non-undefined object\n                // passive is not supported\n                // don't pass options as object\n                // just pass capture as a boolean\n                task.options = !!taskData.options.capture;\n                taskData.options = task.options;\n            }\n        }\n        var customScheduleGlobal = function (task) {\n            // if there is already a task for the eventName + capture,\n            // just return, because we use the shared globalZoneAwareCallback here.\n            if (taskData.isExisting) {\n                return;\n            }\n            checkIsPassive(task);\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, taskData.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, taskData.options);\n        };\n        var customCancelGlobal = function (task) {\n            // if task is not marked as isRemoved, this call is directly\n            // from Zone.prototype.cancelTask, we should remove the task\n            // from tasksList of target first\n            if (!task.isRemoved) {\n                var symbolEventNames = zoneSymbolEventNames$1[task.eventName];\n                var symbolEventName = void 0;\n                if (symbolEventNames) {\n                    symbolEventName = symbolEventNames[task.capture ? TRUE_STR : FALSE_STR];\n                }\n                var existingTasks = symbolEventName && task.target[symbolEventName];\n                if (existingTasks) {\n                    for (var i = 0; i < existingTasks.length; i++) {\n                        var existingTask = existingTasks[i];\n                        if (existingTask === task) {\n                            existingTasks.splice(i, 1);\n                            // set isRemoved to data for faster invokeTask check\n                            task.isRemoved = true;\n                            if (existingTasks.length === 0) {\n                                // all tasks for the eventName + capture have gone,\n                                // remove globalZoneAwareCallback and remove the task cache from target\n                                task.allRemoved = true;\n                                task.target[symbolEventName] = null;\n                            }\n                            break;\n                        }\n                    }\n                }\n            }\n            // if all tasks for the eventName + capture have gone,\n            // we will really remove the global event callback,\n            // if not, return\n            if (!task.allRemoved) {\n                return;\n            }\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.capture ? globalZoneAwareCaptureCallback : globalZoneAwareCallback, task.options);\n        };\n        var customScheduleNonGlobal = function (task) {\n            checkIsPassive(task);\n            return nativeAddEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        var customSchedulePrepend = function (task) {\n            return nativePrependEventListener.call(taskData.target, taskData.eventName, task.invoke, taskData.options);\n        };\n        var customCancelNonGlobal = function (task) {\n            return nativeRemoveEventListener.call(task.target, task.eventName, task.invoke, task.options);\n        };\n        var customSchedule = useGlobalCallback ? customScheduleGlobal : customScheduleNonGlobal;\n        var customCancel = useGlobalCallback ? customCancelGlobal : customCancelNonGlobal;\n        var compareTaskCallbackVsDelegate = function (task, delegate) {\n            var typeOfDelegate = typeof delegate;\n            return (typeOfDelegate === 'function' && task.callback === delegate) ||\n                (typeOfDelegate === 'object' && task.originalDelegate === delegate);\n        };\n        var compare = (patchOptions && patchOptions.diff) ? patchOptions.diff : compareTaskCallbackVsDelegate;\n        var blackListedEvents = Zone[Zone.__symbol__('BLACK_LISTED_EVENTS')];\n        var makeAddListener = function (nativeListener, addSource, customScheduleFn, customCancelFn, returnTarget, prepend) {\n            if (returnTarget === void 0) { returnTarget = false; }\n            if (prepend === void 0) { prepend = false; }\n            return function () {\n                var target = this || _global;\n                var eventName = arguments[0];\n                var delegate = arguments[1];\n                if (!delegate) {\n                    return nativeListener.apply(this, arguments);\n                }\n                if (isNode && eventName === 'uncaughtException') {\n                    // don't patch uncaughtException of nodejs to prevent endless loop\n                    return nativeListener.apply(this, arguments);\n                }\n                // don't create the bind delegate function for handleEvent\n                // case here to improve addEventListener performance\n                // we will create the bind delegate when invoke\n                var isHandleEvent = false;\n                if (typeof delegate !== 'function') {\n                    if (!delegate.handleEvent) {\n                        return nativeListener.apply(this, arguments);\n                    }\n                    isHandleEvent = true;\n                }\n                if (validateHandler && !validateHandler(nativeListener, delegate, target, arguments)) {\n                    return;\n                }\n                var options = arguments[2];\n                if (blackListedEvents) {\n                    // check black list\n                    for (var i = 0; i < blackListedEvents.length; i++) {\n                        if (eventName === blackListedEvents[i]) {\n                            return nativeListener.apply(this, arguments);\n                        }\n                    }\n                }\n                var capture;\n                var once = false;\n                if (options === undefined) {\n                    capture = false;\n                }\n                else if (options === true) {\n                    capture = true;\n                }\n                else if (options === false) {\n                    capture = false;\n                }\n                else {\n                    capture = options ? !!options.capture : false;\n                    once = options ? !!options.once : false;\n                }\n                var zone = Zone.current;\n                var symbolEventNames = zoneSymbolEventNames$1[eventName];\n                var symbolEventName;\n                if (!symbolEventNames) {\n                    // the code is duplicate, but I just want to get some better performance\n                    var falseEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + FALSE_STR;\n                    var trueEventName = (eventNameToString ? eventNameToString(eventName) : eventName) + TRUE_STR;\n                    var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n                    var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n                    zoneSymbolEventNames$1[eventName] = {};\n                    zoneSymbolEventNames$1[eventName][FALSE_STR] = symbol;\n                    zoneSymbolEventNames$1[eventName][TRUE_STR] = symbolCapture;\n                    symbolEventName = capture ? symbolCapture : symbol;\n                }\n                else {\n                    symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n                }\n                var existingTasks = target[symbolEventName];\n                var isExisting = false;\n                if (existingTasks) {\n                    // already have task registered\n                    isExisting = true;\n                    if (checkDuplicate) {\n                        for (var i = 0; i < existingTasks.length; i++) {\n                            if (compare(existingTasks[i], delegate)) {\n                                // same callback, same capture, same event name, just return\n                                return;\n                            }\n                        }\n                    }\n                }\n                else {\n                    existingTasks = target[symbolEventName] = [];\n                }\n                var source;\n                var constructorName = target.constructor['name'];\n                var targetSource = globalSources[constructorName];\n                if (targetSource) {\n                    source = targetSource[eventName];\n                }\n                if (!source) {\n                    source = constructorName + addSource +\n                        (eventNameToString ? eventNameToString(eventName) : eventName);\n                }\n                // do not create a new object as task.data to pass those things\n                // just use the global shared one\n                taskData.options = options;\n                if (once) {\n                    // if addEventListener with once options, we don't pass it to\n                    // native addEventListener, instead we keep the once setting\n                    // and handle ourselves.\n                    taskData.options.once = false;\n                }\n                taskData.target = target;\n                taskData.capture = capture;\n                taskData.eventName = eventName;\n                taskData.isExisting = isExisting;\n                var data = useGlobalCallback ? OPTIMIZED_ZONE_EVENT_TASK_DATA : undefined;\n                // keep taskData into data to allow onScheduleEventTask to access the task information\n                if (data) {\n                    data.taskData = taskData;\n                }\n                var task = zone.scheduleEventTask(source, delegate, data, customScheduleFn, customCancelFn);\n                // should clear taskData.target to avoid memory leak\n                // issue, https://github.com/angular/angular/issues/20442\n                taskData.target = null;\n                // need to clear up taskData because it is a global object\n                if (data) {\n                    data.taskData = null;\n                }\n                // have to save those information to task in case\n                // application may call task.zone.cancelTask() directly\n                if (once) {\n                    options.once = true;\n                }\n                if (!(!passiveSupported && typeof task.options === 'boolean')) {\n                    // if not support passive, and we pass an option object\n                    // to addEventListener, we should save the options to task\n                    task.options = options;\n                }\n                task.target = target;\n                task.capture = capture;\n                task.eventName = eventName;\n                if (isHandleEvent) {\n                    // save original delegate for compare to check duplicate\n                    task.originalDelegate = delegate;\n                }\n                if (!prepend) {\n                    existingTasks.push(task);\n                }\n                else {\n                    existingTasks.unshift(task);\n                }\n                if (returnTarget) {\n                    return target;\n                }\n            };\n        };\n        proto[ADD_EVENT_LISTENER] = makeAddListener(nativeAddEventListener, ADD_EVENT_LISTENER_SOURCE, customSchedule, customCancel, returnTarget);\n        if (nativePrependEventListener) {\n            proto[PREPEND_EVENT_LISTENER] = makeAddListener(nativePrependEventListener, PREPEND_EVENT_LISTENER_SOURCE, customSchedulePrepend, customCancel, returnTarget, true);\n        }\n        proto[REMOVE_EVENT_LISTENER] = function () {\n            var target = this || _global;\n            var eventName = arguments[0];\n            var options = arguments[2];\n            var capture;\n            if (options === undefined) {\n                capture = false;\n            }\n            else if (options === true) {\n                capture = true;\n            }\n            else if (options === false) {\n                capture = false;\n            }\n            else {\n                capture = options ? !!options.capture : false;\n            }\n            var delegate = arguments[1];\n            if (!delegate) {\n                return nativeRemoveEventListener.apply(this, arguments);\n            }\n            if (validateHandler &&\n                !validateHandler(nativeRemoveEventListener, delegate, target, arguments)) {\n                return;\n            }\n            var symbolEventNames = zoneSymbolEventNames$1[eventName];\n            var symbolEventName;\n            if (symbolEventNames) {\n                symbolEventName = symbolEventNames[capture ? TRUE_STR : FALSE_STR];\n            }\n            var existingTasks = symbolEventName && target[symbolEventName];\n            if (existingTasks) {\n                for (var i = 0; i < existingTasks.length; i++) {\n                    var existingTask = existingTasks[i];\n                    if (compare(existingTask, delegate)) {\n                        existingTasks.splice(i, 1);\n                        // set isRemoved to data for faster invokeTask check\n                        existingTask.isRemoved = true;\n                        if (existingTasks.length === 0) {\n                            // all tasks for the eventName + capture have gone,\n                            // remove globalZoneAwareCallback and remove the task cache from target\n                            existingTask.allRemoved = true;\n                            target[symbolEventName] = null;\n                        }\n                        existingTask.zone.cancelTask(existingTask);\n                        if (returnTarget) {\n                            return target;\n                        }\n                        return;\n                    }\n                }\n            }\n            // issue 930, didn't find the event name or callback\n            // from zone kept existingTasks, the callback maybe\n            // added outside of zone, we need to call native removeEventListener\n            // to try to remove it.\n            return nativeRemoveEventListener.apply(this, arguments);\n        };\n        proto[LISTENERS_EVENT_LISTENER] = function () {\n            var target = this || _global;\n            var eventName = arguments[0];\n            var listeners = [];\n            var tasks = findEventTasks(target, eventNameToString ? eventNameToString(eventName) : eventName);\n            for (var i = 0; i < tasks.length; i++) {\n                var task = tasks[i];\n                var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                listeners.push(delegate);\n            }\n            return listeners;\n        };\n        proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER] = function () {\n            var target = this || _global;\n            var eventName = arguments[0];\n            if (!eventName) {\n                var keys = Object.keys(target);\n                for (var i = 0; i < keys.length; i++) {\n                    var prop = keys[i];\n                    var match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n                    var evtName = match && match[1];\n                    // in nodejs EventEmitter, removeListener event is\n                    // used for monitoring the removeListener call,\n                    // so just keep removeListener eventListener until\n                    // all other eventListeners are removed\n                    if (evtName && evtName !== 'removeListener') {\n                        this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, evtName);\n                    }\n                }\n                // remove removeListener listener finally\n                this[REMOVE_ALL_LISTENERS_EVENT_LISTENER].call(this, 'removeListener');\n            }\n            else {\n                var symbolEventNames = zoneSymbolEventNames$1[eventName];\n                if (symbolEventNames) {\n                    var symbolEventName = symbolEventNames[FALSE_STR];\n                    var symbolCaptureEventName = symbolEventNames[TRUE_STR];\n                    var tasks = target[symbolEventName];\n                    var captureTasks = target[symbolCaptureEventName];\n                    if (tasks) {\n                        var removeTasks = tasks.slice();\n                        for (var i = 0; i < removeTasks.length; i++) {\n                            var task = removeTasks[i];\n                            var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                    if (captureTasks) {\n                        var removeTasks = captureTasks.slice();\n                        for (var i = 0; i < removeTasks.length; i++) {\n                            var task = removeTasks[i];\n                            var delegate = task.originalDelegate ? task.originalDelegate : task.callback;\n                            this[REMOVE_EVENT_LISTENER].call(this, eventName, delegate, task.options);\n                        }\n                    }\n                }\n            }\n            if (returnTarget) {\n                return this;\n            }\n        };\n        // for native toString patch\n        attachOriginToPatched(proto[ADD_EVENT_LISTENER], nativeAddEventListener);\n        attachOriginToPatched(proto[REMOVE_EVENT_LISTENER], nativeRemoveEventListener);\n        if (nativeRemoveAllListeners) {\n            attachOriginToPatched(proto[REMOVE_ALL_LISTENERS_EVENT_LISTENER], nativeRemoveAllListeners);\n        }\n        if (nativeListeners) {\n            attachOriginToPatched(proto[LISTENERS_EVENT_LISTENER], nativeListeners);\n        }\n        return true;\n    }\n    var results = [];\n    for (var i = 0; i < apis.length; i++) {\n        results[i] = patchEventTargetMethods(apis[i], patchOptions);\n    }\n    return results;\n}\nfunction findEventTasks(target, eventName) {\n    var foundTasks = [];\n    for (var prop in target) {\n        var match = EVENT_NAME_SYMBOL_REGX.exec(prop);\n        var evtName = match && match[1];\n        if (evtName && (!eventName || evtName === eventName)) {\n            var tasks = target[prop];\n            if (tasks) {\n                for (var i = 0; i < tasks.length; i++) {\n                    foundTasks.push(tasks[i]);\n                }\n            }\n        }\n    }\n    return foundTasks;\n}\nfunction patchEventPrototype(global, api) {\n    var Event = global['Event'];\n    if (Event && Event.prototype) {\n        api.patchMethod(Event.prototype, 'stopImmediatePropagation', function (delegate) { return function (self, args) {\n            self[IMMEDIATE_PROPAGATION_SYMBOL] = true;\n            // we need to call the native stopImmediatePropagation\n            // in case in some hybrid application, some part of\n            // application will be controlled by zone, some are not\n            delegate && delegate.apply(self, args);\n        }; });\n    }\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction patchCallbacks(api, target, targetName, method, callbacks) {\n    var symbol = Zone.__symbol__(method);\n    if (target[symbol]) {\n        return;\n    }\n    var nativeDelegate = target[symbol] = target[method];\n    target[method] = function (name, opts, options) {\n        if (opts && opts.prototype) {\n            callbacks.forEach(function (callback) {\n                var source = targetName + \".\" + method + \"::\" + callback;\n                var prototype = opts.prototype;\n                if (prototype.hasOwnProperty(callback)) {\n                    var descriptor = api.ObjectGetOwnPropertyDescriptor(prototype, callback);\n                    if (descriptor && descriptor.value) {\n                        descriptor.value = api.wrapWithCurrentZone(descriptor.value, source);\n                        api._redefineProperty(opts.prototype, callback, descriptor);\n                    }\n                    else if (prototype[callback]) {\n                        prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                    }\n                }\n                else if (prototype[callback]) {\n                    prototype[callback] = api.wrapWithCurrentZone(prototype[callback], source);\n                }\n            });\n        }\n        return nativeDelegate.call(target, name, opts, options);\n    };\n    api.attachOriginToPatched(target[method], nativeDelegate);\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/*\n * This is necessary for Chrome and Chrome mobile, to enable\n * things like redefining `createdCallback` on an element.\n */\nvar zoneSymbol$1 = Zone.__symbol__;\nvar _defineProperty = Object[zoneSymbol$1('defineProperty')] = Object.defineProperty;\nvar _getOwnPropertyDescriptor = Object[zoneSymbol$1('getOwnPropertyDescriptor')] =\n    Object.getOwnPropertyDescriptor;\nvar _create = Object.create;\nvar unconfigurablesKey = zoneSymbol$1('unconfigurables');\nfunction propertyPatch() {\n    Object.defineProperty = function (obj, prop, desc) {\n        if (isUnconfigurable(obj, prop)) {\n            throw new TypeError('Cannot assign to read only property \\'' + prop + '\\' of ' + obj);\n        }\n        var originalConfigurableFlag = desc.configurable;\n        if (prop !== 'prototype') {\n            desc = rewriteDescriptor(obj, prop, desc);\n        }\n        return _tryDefineProperty(obj, prop, desc, originalConfigurableFlag);\n    };\n    Object.defineProperties = function (obj, props) {\n        Object.keys(props).forEach(function (prop) {\n            Object.defineProperty(obj, prop, props[prop]);\n        });\n        return obj;\n    };\n    Object.create = function (obj, proto) {\n        if (typeof proto === 'object' && !Object.isFrozen(proto)) {\n            Object.keys(proto).forEach(function (prop) {\n                proto[prop] = rewriteDescriptor(obj, prop, proto[prop]);\n            });\n        }\n        return _create(obj, proto);\n    };\n    Object.getOwnPropertyDescriptor = function (obj, prop) {\n        var desc = _getOwnPropertyDescriptor(obj, prop);\n        if (desc && isUnconfigurable(obj, prop)) {\n            desc.configurable = false;\n        }\n        return desc;\n    };\n}\nfunction _redefineProperty(obj, prop, desc) {\n    var originalConfigurableFlag = desc.configurable;\n    desc = rewriteDescriptor(obj, prop, desc);\n    return _tryDefineProperty(obj, prop, desc, originalConfigurableFlag);\n}\nfunction isUnconfigurable(obj, prop) {\n    return obj && obj[unconfigurablesKey] && obj[unconfigurablesKey][prop];\n}\nfunction rewriteDescriptor(obj, prop, desc) {\n    // issue-927, if the desc is frozen, don't try to change the desc\n    if (!Object.isFrozen(desc)) {\n        desc.configurable = true;\n    }\n    if (!desc.configurable) {\n        // issue-927, if the obj is frozen, don't try to set the desc to obj\n        if (!obj[unconfigurablesKey] && !Object.isFrozen(obj)) {\n            _defineProperty(obj, unconfigurablesKey, { writable: true, value: {} });\n        }\n        if (obj[unconfigurablesKey]) {\n            obj[unconfigurablesKey][prop] = true;\n        }\n    }\n    return desc;\n}\nfunction _tryDefineProperty(obj, prop, desc, originalConfigurableFlag) {\n    try {\n        return _defineProperty(obj, prop, desc);\n    }\n    catch (error) {\n        if (desc.configurable) {\n            // In case of errors, when the configurable flag was likely set by rewriteDescriptor(), let's\n            // retry with the original flag value\n            if (typeof originalConfigurableFlag == 'undefined') {\n                delete desc.configurable;\n            }\n            else {\n                desc.configurable = originalConfigurableFlag;\n            }\n            try {\n                return _defineProperty(obj, prop, desc);\n            }\n            catch (error) {\n                var descJson = null;\n                try {\n                    descJson = JSON.stringify(desc);\n                }\n                catch (error) {\n                    descJson = desc.toString();\n                }\n                console.log(\"Attempting to configure '\" + prop + \"' with descriptor '\" + descJson + \"' on object '\" + obj + \"' and got error, giving up: \" + error);\n            }\n        }\n        else {\n            throw error;\n        }\n    }\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nvar globalEventHandlersEventNames = [\n    'abort',\n    'animationcancel',\n    'animationend',\n    'animationiteration',\n    'auxclick',\n    'beforeinput',\n    'blur',\n    'cancel',\n    'canplay',\n    'canplaythrough',\n    'change',\n    'compositionstart',\n    'compositionupdate',\n    'compositionend',\n    'cuechange',\n    'click',\n    'close',\n    'contextmenu',\n    'curechange',\n    'dblclick',\n    'drag',\n    'dragend',\n    'dragenter',\n    'dragexit',\n    'dragleave',\n    'dragover',\n    'drop',\n    'durationchange',\n    'emptied',\n    'ended',\n    'error',\n    'focus',\n    'focusin',\n    'focusout',\n    'gotpointercapture',\n    'input',\n    'invalid',\n    'keydown',\n    'keypress',\n    'keyup',\n    'load',\n    'loadstart',\n    'loadeddata',\n    'loadedmetadata',\n    'lostpointercapture',\n    'mousedown',\n    'mouseenter',\n    'mouseleave',\n    'mousemove',\n    'mouseout',\n    'mouseover',\n    'mouseup',\n    'mousewheel',\n    'orientationchange',\n    'pause',\n    'play',\n    'playing',\n    'pointercancel',\n    'pointerdown',\n    'pointerenter',\n    'pointerleave',\n    'pointerlockchange',\n    'mozpointerlockchange',\n    'webkitpointerlockerchange',\n    'pointerlockerror',\n    'mozpointerlockerror',\n    'webkitpointerlockerror',\n    'pointermove',\n    'pointout',\n    'pointerover',\n    'pointerup',\n    'progress',\n    'ratechange',\n    'reset',\n    'resize',\n    'scroll',\n    'seeked',\n    'seeking',\n    'select',\n    'selectionchange',\n    'selectstart',\n    'show',\n    'sort',\n    'stalled',\n    'submit',\n    'suspend',\n    'timeupdate',\n    'volumechange',\n    'touchcancel',\n    'touchmove',\n    'touchstart',\n    'touchend',\n    'transitioncancel',\n    'transitionend',\n    'waiting',\n    'wheel'\n];\nvar documentEventNames = [\n    'afterscriptexecute', 'beforescriptexecute', 'DOMContentLoaded', 'freeze', 'fullscreenchange',\n    'mozfullscreenchange', 'webkitfullscreenchange', 'msfullscreenchange', 'fullscreenerror',\n    'mozfullscreenerror', 'webkitfullscreenerror', 'msfullscreenerror', 'readystatechange',\n    'visibilitychange', 'resume'\n];\nvar windowEventNames = [\n    'absolutedeviceorientation',\n    'afterinput',\n    'afterprint',\n    'appinstalled',\n    'beforeinstallprompt',\n    'beforeprint',\n    'beforeunload',\n    'devicelight',\n    'devicemotion',\n    'deviceorientation',\n    'deviceorientationabsolute',\n    'deviceproximity',\n    'hashchange',\n    'languagechange',\n    'message',\n    'mozbeforepaint',\n    'offline',\n    'online',\n    'paint',\n    'pageshow',\n    'pagehide',\n    'popstate',\n    'rejectionhandled',\n    'storage',\n    'unhandledrejection',\n    'unload',\n    'userproximity',\n    'vrdisplyconnected',\n    'vrdisplaydisconnected',\n    'vrdisplaypresentchange'\n];\nvar htmlElementEventNames = [\n    'beforecopy', 'beforecut', 'beforepaste', 'copy', 'cut', 'paste', 'dragstart', 'loadend',\n    'animationstart', 'search', 'transitionrun', 'transitionstart', 'webkitanimationend',\n    'webkitanimationiteration', 'webkitanimationstart', 'webkittransitionend'\n];\nvar mediaElementEventNames = ['encrypted', 'waitingforkey', 'msneedkey', 'mozinterruptbegin', 'mozinterruptend'];\nvar ieElementEventNames = [\n    'activate',\n    'afterupdate',\n    'ariarequest',\n    'beforeactivate',\n    'beforedeactivate',\n    'beforeeditfocus',\n    'beforeupdate',\n    'cellchange',\n    'controlselect',\n    'dataavailable',\n    'datasetchanged',\n    'datasetcomplete',\n    'errorupdate',\n    'filterchange',\n    'layoutcomplete',\n    'losecapture',\n    'move',\n    'moveend',\n    'movestart',\n    'propertychange',\n    'resizeend',\n    'resizestart',\n    'rowenter',\n    'rowexit',\n    'rowsdelete',\n    'rowsinserted',\n    'command',\n    'compassneedscalibration',\n    'deactivate',\n    'help',\n    'mscontentzoom',\n    'msmanipulationstatechanged',\n    'msgesturechange',\n    'msgesturedoubletap',\n    'msgestureend',\n    'msgesturehold',\n    'msgesturestart',\n    'msgesturetap',\n    'msgotpointercapture',\n    'msinertiastart',\n    'mslostpointercapture',\n    'mspointercancel',\n    'mspointerdown',\n    'mspointerenter',\n    'mspointerhover',\n    'mspointerleave',\n    'mspointermove',\n    'mspointerout',\n    'mspointerover',\n    'mspointerup',\n    'pointerout',\n    'mssitemodejumplistitemremoved',\n    'msthumbnailclick',\n    'stop',\n    'storagecommit'\n];\nvar webglEventNames = ['webglcontextrestored', 'webglcontextlost', 'webglcontextcreationerror'];\nvar formEventNames = ['autocomplete', 'autocompleteerror'];\nvar detailEventNames = ['toggle'];\nvar frameEventNames = ['load'];\nvar frameSetEventNames = ['blur', 'error', 'focus', 'load', 'resize', 'scroll', 'messageerror'];\nvar marqueeEventNames = ['bounce', 'finish', 'start'];\nvar XMLHttpRequestEventNames = [\n    'loadstart', 'progress', 'abort', 'error', 'load', 'progress', 'timeout', 'loadend',\n    'readystatechange'\n];\nvar IDBIndexEventNames = ['upgradeneeded', 'complete', 'abort', 'success', 'error', 'blocked', 'versionchange', 'close'];\nvar websocketEventNames = ['close', 'error', 'open', 'message'];\nvar workerEventNames = ['error', 'message'];\nvar eventNames = globalEventHandlersEventNames.concat(webglEventNames, formEventNames, detailEventNames, documentEventNames, windowEventNames, htmlElementEventNames, ieElementEventNames);\nfunction filterProperties(target, onProperties, ignoreProperties) {\n    if (!ignoreProperties || ignoreProperties.length === 0) {\n        return onProperties;\n    }\n    var tip = ignoreProperties.filter(function (ip) { return ip.target === target; });\n    if (!tip || tip.length === 0) {\n        return onProperties;\n    }\n    var targetIgnoreProperties = tip[0].ignoreProperties;\n    return onProperties.filter(function (op) { return targetIgnoreProperties.indexOf(op) === -1; });\n}\nfunction patchFilteredProperties(target, onProperties, ignoreProperties, prototype) {\n    // check whether target is available, sometimes target will be undefined\n    // because different browser or some 3rd party plugin.\n    if (!target) {\n        return;\n    }\n    var filteredProperties = filterProperties(target, onProperties, ignoreProperties);\n    patchOnProperties(target, filteredProperties, prototype);\n}\nfunction propertyDescriptorPatch(api, _global) {\n    if (isNode && !isMix) {\n        return;\n    }\n    if (Zone[api.symbol('patchEvents')]) {\n        // events are already been patched by legacy patch.\n        return;\n    }\n    var supportsWebSocket = typeof WebSocket !== 'undefined';\n    var ignoreProperties = _global['__Zone_ignore_on_properties'];\n    // for browsers that we can patch the descriptor:  Chrome & Firefox\n    if (isBrowser) {\n        var internalWindow = window;\n        var ignoreErrorProperties = isIE ? [{ target: internalWindow, ignoreProperties: ['error'] }] : [];\n        // in IE/Edge, onProp not exist in window object, but in WindowPrototype\n        // so we need to pass WindowPrototype to check onProp exist or not\n        patchFilteredProperties(internalWindow, eventNames.concat(['messageerror']), ignoreProperties ? ignoreProperties.concat(ignoreErrorProperties) : ignoreProperties, ObjectGetPrototypeOf(internalWindow));\n        patchFilteredProperties(Document.prototype, eventNames, ignoreProperties);\n        if (typeof internalWindow['SVGElement'] !== 'undefined') {\n            patchFilteredProperties(internalWindow['SVGElement'].prototype, eventNames, ignoreProperties);\n        }\n        patchFilteredProperties(Element.prototype, eventNames, ignoreProperties);\n        patchFilteredProperties(HTMLElement.prototype, eventNames, ignoreProperties);\n        patchFilteredProperties(HTMLMediaElement.prototype, mediaElementEventNames, ignoreProperties);\n        patchFilteredProperties(HTMLFrameSetElement.prototype, windowEventNames.concat(frameSetEventNames), ignoreProperties);\n        patchFilteredProperties(HTMLBodyElement.prototype, windowEventNames.concat(frameSetEventNames), ignoreProperties);\n        patchFilteredProperties(HTMLFrameElement.prototype, frameEventNames, ignoreProperties);\n        patchFilteredProperties(HTMLIFrameElement.prototype, frameEventNames, ignoreProperties);\n        var HTMLMarqueeElement_1 = internalWindow['HTMLMarqueeElement'];\n        if (HTMLMarqueeElement_1) {\n            patchFilteredProperties(HTMLMarqueeElement_1.prototype, marqueeEventNames, ignoreProperties);\n        }\n        var Worker_1 = internalWindow['Worker'];\n        if (Worker_1) {\n            patchFilteredProperties(Worker_1.prototype, workerEventNames, ignoreProperties);\n        }\n    }\n    var XMLHttpRequest = _global['XMLHttpRequest'];\n    if (XMLHttpRequest) {\n        // XMLHttpRequest is not available in ServiceWorker, so we need to check here\n        patchFilteredProperties(XMLHttpRequest.prototype, XMLHttpRequestEventNames, ignoreProperties);\n    }\n    var XMLHttpRequestEventTarget = _global['XMLHttpRequestEventTarget'];\n    if (XMLHttpRequestEventTarget) {\n        patchFilteredProperties(XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype, XMLHttpRequestEventNames, ignoreProperties);\n    }\n    if (typeof IDBIndex !== 'undefined') {\n        patchFilteredProperties(IDBIndex.prototype, IDBIndexEventNames, ignoreProperties);\n        patchFilteredProperties(IDBRequest.prototype, IDBIndexEventNames, ignoreProperties);\n        patchFilteredProperties(IDBOpenDBRequest.prototype, IDBIndexEventNames, ignoreProperties);\n        patchFilteredProperties(IDBDatabase.prototype, IDBIndexEventNames, ignoreProperties);\n        patchFilteredProperties(IDBTransaction.prototype, IDBIndexEventNames, ignoreProperties);\n        patchFilteredProperties(IDBCursor.prototype, IDBIndexEventNames, ignoreProperties);\n    }\n    if (supportsWebSocket) {\n        patchFilteredProperties(WebSocket.prototype, websocketEventNames, ignoreProperties);\n    }\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nZone.__load_patch('util', function (global, Zone, api) {\n    api.patchOnProperties = patchOnProperties;\n    api.patchMethod = patchMethod;\n    api.bindArguments = bindArguments;\n    api.patchMacroTask = patchMacroTask;\n    // In earlier version of zone.js (<0.9.0), we use env name `__zone_symbol__BLACK_LISTED_EVENTS` to\n    // define which events will not be patched by `Zone.js`.\n    // In newer version (>=0.9.0), we change the env name to `__zone_symbol__UNPATCHED_EVENTS` to keep\n    // the name consistent with angular repo.\n    // The  `__zone_symbol__BLACK_LISTED_EVENTS` is deprecated, but it is still be supported for\n    // backwards compatibility.\n    var SYMBOL_BLACK_LISTED_EVENTS = Zone.__symbol__('BLACK_LISTED_EVENTS');\n    var SYMBOL_UNPATCHED_EVENTS = Zone.__symbol__('UNPATCHED_EVENTS');\n    if (global[SYMBOL_UNPATCHED_EVENTS]) {\n        global[SYMBOL_BLACK_LISTED_EVENTS] = global[SYMBOL_UNPATCHED_EVENTS];\n    }\n    if (global[SYMBOL_BLACK_LISTED_EVENTS]) {\n        Zone[SYMBOL_BLACK_LISTED_EVENTS] = Zone[SYMBOL_UNPATCHED_EVENTS] =\n            global[SYMBOL_BLACK_LISTED_EVENTS];\n    }\n    api.patchEventPrototype = patchEventPrototype;\n    api.patchEventTarget = patchEventTarget;\n    api.isIEOrEdge = isIEOrEdge;\n    api.ObjectDefineProperty = ObjectDefineProperty;\n    api.ObjectGetOwnPropertyDescriptor = ObjectGetOwnPropertyDescriptor;\n    api.ObjectCreate = ObjectCreate;\n    api.ArraySlice = ArraySlice;\n    api.patchClass = patchClass;\n    api.wrapWithCurrentZone = wrapWithCurrentZone;\n    api.filterProperties = filterProperties;\n    api.attachOriginToPatched = attachOriginToPatched;\n    api._redefineProperty = _redefineProperty;\n    api.patchCallbacks = patchCallbacks;\n    api.getGlobalObjects = function () { return ({\n        globalSources: globalSources,\n        zoneSymbolEventNames: zoneSymbolEventNames$1,\n        eventNames: eventNames,\n        isBrowser: isBrowser,\n        isMix: isMix,\n        isNode: isNode,\n        TRUE_STR: TRUE_STR,\n        FALSE_STR: FALSE_STR,\n        ZONE_SYMBOL_PREFIX: ZONE_SYMBOL_PREFIX,\n        ADD_EVENT_LISTENER_STR: ADD_EVENT_LISTENER_STR,\n        REMOVE_EVENT_LISTENER_STR: REMOVE_EVENT_LISTENER_STR\n    }); };\n});\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction eventTargetLegacyPatch(_global, api) {\n    var _a = api.getGlobalObjects(), eventNames = _a.eventNames, globalSources = _a.globalSources, zoneSymbolEventNames = _a.zoneSymbolEventNames, TRUE_STR = _a.TRUE_STR, FALSE_STR = _a.FALSE_STR, ZONE_SYMBOL_PREFIX = _a.ZONE_SYMBOL_PREFIX;\n    var WTF_ISSUE_555 = 'Anchor,Area,Audio,BR,Base,BaseFont,Body,Button,Canvas,Content,DList,Directory,Div,Embed,FieldSet,Font,Form,Frame,FrameSet,HR,Head,Heading,Html,IFrame,Image,Input,Keygen,LI,Label,Legend,Link,Map,Marquee,Media,Menu,Meta,Meter,Mod,OList,Object,OptGroup,Option,Output,Paragraph,Pre,Progress,Quote,Script,Select,Source,Span,Style,TableCaption,TableCell,TableCol,Table,TableRow,TableSection,TextArea,Title,Track,UList,Unknown,Video';\n    var NO_EVENT_TARGET = 'ApplicationCache,EventSource,FileReader,InputMethodContext,MediaController,MessagePort,Node,Performance,SVGElementInstance,SharedWorker,TextTrack,TextTrackCue,TextTrackList,WebKitNamedFlow,Window,Worker,WorkerGlobalScope,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload,IDBRequest,IDBOpenDBRequest,IDBDatabase,IDBTransaction,IDBCursor,DBIndex,WebSocket'\n        .split(',');\n    var EVENT_TARGET = 'EventTarget';\n    var apis = [];\n    var isWtf = _global['wtf'];\n    var WTF_ISSUE_555_ARRAY = WTF_ISSUE_555.split(',');\n    if (isWtf) {\n        // Workaround for: https://github.com/google/tracing-framework/issues/555\n        apis = WTF_ISSUE_555_ARRAY.map(function (v) { return 'HTML' + v + 'Element'; }).concat(NO_EVENT_TARGET);\n    }\n    else if (_global[EVENT_TARGET]) {\n        apis.push(EVENT_TARGET);\n    }\n    else {\n        // Note: EventTarget is not available in all browsers,\n        // if it's not available, we instead patch the APIs in the IDL that inherit from EventTarget\n        apis = NO_EVENT_TARGET;\n    }\n    var isDisableIECheck = _global['__Zone_disable_IE_check'] || false;\n    var isEnableCrossContextCheck = _global['__Zone_enable_cross_context_check'] || false;\n    var ieOrEdge = api.isIEOrEdge();\n    var ADD_EVENT_LISTENER_SOURCE = '.addEventListener:';\n    var FUNCTION_WRAPPER = '[object FunctionWrapper]';\n    var BROWSER_TOOLS = 'function __BROWSERTOOLS_CONSOLE_SAFEFUNC() { [native code] }';\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (var i = 0; i < eventNames.length; i++) {\n        var eventName = eventNames[i];\n        var falseEventName = eventName + FALSE_STR;\n        var trueEventName = eventName + TRUE_STR;\n        var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    //  predefine all task.source string\n    for (var i = 0; i < WTF_ISSUE_555.length; i++) {\n        var target = WTF_ISSUE_555_ARRAY[i];\n        var targets = globalSources[target] = {};\n        for (var j = 0; j < eventNames.length; j++) {\n            var eventName = eventNames[j];\n            targets[eventName] = target + ADD_EVENT_LISTENER_SOURCE + eventName;\n        }\n    }\n    var checkIEAndCrossContext = function (nativeDelegate, delegate, target, args) {\n        if (!isDisableIECheck && ieOrEdge) {\n            if (isEnableCrossContextCheck) {\n                try {\n                    var testString = delegate.toString();\n                    if ((testString === FUNCTION_WRAPPER || testString == BROWSER_TOOLS)) {\n                        nativeDelegate.apply(target, args);\n                        return false;\n                    }\n                }\n                catch (error) {\n                    nativeDelegate.apply(target, args);\n                    return false;\n                }\n            }\n            else {\n                var testString = delegate.toString();\n                if ((testString === FUNCTION_WRAPPER || testString == BROWSER_TOOLS)) {\n                    nativeDelegate.apply(target, args);\n                    return false;\n                }\n            }\n        }\n        else if (isEnableCrossContextCheck) {\n            try {\n                delegate.toString();\n            }\n            catch (error) {\n                nativeDelegate.apply(target, args);\n                return false;\n            }\n        }\n        return true;\n    };\n    var apiTypes = [];\n    for (var i = 0; i < apis.length; i++) {\n        var type = _global[apis[i]];\n        apiTypes.push(type && type.prototype);\n    }\n    // vh is validateHandler to check event handler\n    // is valid or not(for security check)\n    api.patchEventTarget(_global, apiTypes, { vh: checkIEAndCrossContext });\n    Zone[api.symbol('patchEventTarget')] = !!_global[EVENT_TARGET];\n    return true;\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n// we have to patch the instance since the proto is non-configurable\nfunction apply(api, _global) {\n    var _a = api.getGlobalObjects(), ADD_EVENT_LISTENER_STR = _a.ADD_EVENT_LISTENER_STR, REMOVE_EVENT_LISTENER_STR = _a.REMOVE_EVENT_LISTENER_STR;\n    var WS = _global.WebSocket;\n    // On Safari window.EventTarget doesn't exist so need to patch WS add/removeEventListener\n    // On older Chrome, no need since EventTarget was already patched\n    if (!_global.EventTarget) {\n        api.patchEventTarget(_global, [WS.prototype]);\n    }\n    _global.WebSocket = function (x, y) {\n        var socket = arguments.length > 1 ? new WS(x, y) : new WS(x);\n        var proxySocket;\n        var proxySocketProto;\n        // Safari 7.0 has non-configurable own 'onmessage' and friends properties on the socket instance\n        var onmessageDesc = api.ObjectGetOwnPropertyDescriptor(socket, 'onmessage');\n        if (onmessageDesc && onmessageDesc.configurable === false) {\n            proxySocket = api.ObjectCreate(socket);\n            // socket have own property descriptor 'onopen', 'onmessage', 'onclose', 'onerror'\n            // but proxySocket not, so we will keep socket as prototype and pass it to\n            // patchOnProperties method\n            proxySocketProto = socket;\n            [ADD_EVENT_LISTENER_STR, REMOVE_EVENT_LISTENER_STR, 'send', 'close'].forEach(function (propName) {\n                proxySocket[propName] = function () {\n                    var args = api.ArraySlice.call(arguments);\n                    if (propName === ADD_EVENT_LISTENER_STR || propName === REMOVE_EVENT_LISTENER_STR) {\n                        var eventName = args.length > 0 ? args[0] : undefined;\n                        if (eventName) {\n                            var propertySymbol = Zone.__symbol__('ON_PROPERTY' + eventName);\n                            socket[propertySymbol] = proxySocket[propertySymbol];\n                        }\n                    }\n                    return socket[propName].apply(socket, args);\n                };\n            });\n        }\n        else {\n            // we can patch the real socket\n            proxySocket = socket;\n        }\n        api.patchOnProperties(proxySocket, ['close', 'error', 'message', 'open'], proxySocketProto);\n        return proxySocket;\n    };\n    var globalWebSocket = _global['WebSocket'];\n    for (var prop in WS) {\n        globalWebSocket[prop] = WS[prop];\n    }\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {globalThis}\n */\nfunction propertyDescriptorLegacyPatch(api, _global) {\n    var _a = api.getGlobalObjects(), isNode = _a.isNode, isMix = _a.isMix;\n    if (isNode && !isMix) {\n        return;\n    }\n    if (!canPatchViaPropertyDescriptor(api, _global)) {\n        var supportsWebSocket = typeof WebSocket !== 'undefined';\n        // Safari, Android browsers (Jelly Bean)\n        patchViaCapturingAllTheEvents(api);\n        api.patchClass('XMLHttpRequest');\n        if (supportsWebSocket) {\n            apply(api, _global);\n        }\n        Zone[api.symbol('patchEvents')] = true;\n    }\n}\nfunction canPatchViaPropertyDescriptor(api, _global) {\n    var _a = api.getGlobalObjects(), isBrowser = _a.isBrowser, isMix = _a.isMix;\n    if ((isBrowser || isMix) &&\n        !api.ObjectGetOwnPropertyDescriptor(HTMLElement.prototype, 'onclick') &&\n        typeof Element !== 'undefined') {\n        // WebKit https://bugs.webkit.org/show_bug.cgi?id=134364\n        // IDL interface attributes are not configurable\n        var desc = api.ObjectGetOwnPropertyDescriptor(Element.prototype, 'onclick');\n        if (desc && !desc.configurable)\n            return false;\n        // try to use onclick to detect whether we can patch via propertyDescriptor\n        // because XMLHttpRequest is not available in service worker\n        if (desc) {\n            api.ObjectDefineProperty(Element.prototype, 'onclick', {\n                enumerable: true,\n                configurable: true,\n                get: function () {\n                    return true;\n                }\n            });\n            var div = document.createElement('div');\n            var result = !!div.onclick;\n            api.ObjectDefineProperty(Element.prototype, 'onclick', desc);\n            return result;\n        }\n    }\n    var XMLHttpRequest = _global['XMLHttpRequest'];\n    if (!XMLHttpRequest) {\n        // XMLHttpRequest is not available in service worker\n        return false;\n    }\n    var ON_READY_STATE_CHANGE = 'onreadystatechange';\n    var XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n    var xhrDesc = api.ObjectGetOwnPropertyDescriptor(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE);\n    // add enumerable and configurable here because in opera\n    // by default XMLHttpRequest.prototype.onreadystatechange is undefined\n    // without adding enumerable and configurable will cause onreadystatechange\n    // non-configurable\n    // and if XMLHttpRequest.prototype.onreadystatechange is undefined,\n    // we should set a real desc instead a fake one\n    if (xhrDesc) {\n        api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, {\n            enumerable: true,\n            configurable: true,\n            get: function () {\n                return true;\n            }\n        });\n        var req = new XMLHttpRequest();\n        var result = !!req.onreadystatechange;\n        // restore original desc\n        api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, xhrDesc || {});\n        return result;\n    }\n    else {\n        var SYMBOL_FAKE_ONREADYSTATECHANGE_1 = api.symbol('fake');\n        api.ObjectDefineProperty(XMLHttpRequestPrototype, ON_READY_STATE_CHANGE, {\n            enumerable: true,\n            configurable: true,\n            get: function () {\n                return this[SYMBOL_FAKE_ONREADYSTATECHANGE_1];\n            },\n            set: function (value) {\n                this[SYMBOL_FAKE_ONREADYSTATECHANGE_1] = value;\n            }\n        });\n        var req = new XMLHttpRequest();\n        var detectFunc = function () { };\n        req.onreadystatechange = detectFunc;\n        var result = req[SYMBOL_FAKE_ONREADYSTATECHANGE_1] === detectFunc;\n        req.onreadystatechange = null;\n        return result;\n    }\n}\n// Whenever any eventListener fires, we check the eventListener target and all parents\n// for `onwhatever` properties and replace them with zone-bound functions\n// - Chrome (for now)\nfunction patchViaCapturingAllTheEvents(api) {\n    var eventNames = api.getGlobalObjects().eventNames;\n    var unboundKey = api.symbol('unbound');\n    var _loop_1 = function (i) {\n        var property = eventNames[i];\n        var onproperty = 'on' + property;\n        self.addEventListener(property, function (event) {\n            var elt = event.target, bound, source;\n            if (elt) {\n                source = elt.constructor['name'] + '.' + onproperty;\n            }\n            else {\n                source = 'unknown.' + onproperty;\n            }\n            while (elt) {\n                if (elt[onproperty] && !elt[onproperty][unboundKey]) {\n                    bound = api.wrapWithCurrentZone(elt[onproperty], source);\n                    bound[unboundKey] = elt[onproperty];\n                    elt[onproperty] = bound;\n                }\n                elt = elt.parentElement;\n            }\n        }, true);\n    };\n    for (var i = 0; i < eventNames.length; i++) {\n        _loop_1(i);\n    }\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction registerElementPatch(_global, api) {\n    var _a = api.getGlobalObjects(), isBrowser = _a.isBrowser, isMix = _a.isMix;\n    if ((!isBrowser && !isMix) || !('registerElement' in _global.document)) {\n        return;\n    }\n    var callbacks = ['createdCallback', 'attachedCallback', 'detachedCallback', 'attributeChangedCallback'];\n    api.patchCallbacks(api, document, 'Document', 'registerElement', callbacks);\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\n(function (_global) {\n    _global['__zone_symbol__legacyPatch'] = function () {\n        var Zone = _global['Zone'];\n        Zone.__load_patch('registerElement', function (global, Zone, api) {\n            registerElementPatch(global, api);\n        });\n        Zone.__load_patch('EventTargetLegacy', function (global, Zone, api) {\n            eventTargetLegacyPatch(global, api);\n            propertyDescriptorLegacyPatch(api, global);\n        });\n    };\n})(typeof window !== 'undefined' && window || typeof self !== 'undefined' && self || global);\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nvar taskSymbol = zoneSymbol('zoneTask');\nfunction patchTimer(window, setName, cancelName, nameSuffix) {\n    var setNative = null;\n    var clearNative = null;\n    setName += nameSuffix;\n    cancelName += nameSuffix;\n    var tasksByHandleId = {};\n    function scheduleTask(task) {\n        var data = task.data;\n        function timer() {\n            try {\n                task.invoke.apply(this, arguments);\n            }\n            finally {\n                // issue-934, task will be cancelled\n                // even it is a periodic task such as\n                // setInterval\n                if (!(task.data && task.data.isPeriodic)) {\n                    if (typeof data.handleId === 'number') {\n                        // in non-nodejs env, we remove timerId\n                        // from local cache\n                        delete tasksByHandleId[data.handleId];\n                    }\n                    else if (data.handleId) {\n                        // Node returns complex objects as handleIds\n                        // we remove task reference from timer object\n                        data.handleId[taskSymbol] = null;\n                    }\n                }\n            }\n        }\n        data.args[0] = timer;\n        data.handleId = setNative.apply(window, data.args);\n        return task;\n    }\n    function clearTask(task) {\n        return clearNative(task.data.handleId);\n    }\n    setNative =\n        patchMethod(window, setName, function (delegate) { return function (self, args) {\n            if (typeof args[0] === 'function') {\n                var options = {\n                    isPeriodic: nameSuffix === 'Interval',\n                    delay: (nameSuffix === 'Timeout' || nameSuffix === 'Interval') ? args[1] || 0 :\n                        undefined,\n                    args: args\n                };\n                var task = scheduleMacroTaskWithCurrentZone(setName, args[0], options, scheduleTask, clearTask);\n                if (!task) {\n                    return task;\n                }\n                // Node.js must additionally support the ref and unref functions.\n                var handle = task.data.handleId;\n                if (typeof handle === 'number') {\n                    // for non nodejs env, we save handleId: task\n                    // mapping in local cache for clearTimeout\n                    tasksByHandleId[handle] = task;\n                }\n                else if (handle) {\n                    // for nodejs env, we save task\n                    // reference in timerId Object for clearTimeout\n                    handle[taskSymbol] = task;\n                }\n                // check whether handle is null, because some polyfill or browser\n                // may return undefined from setTimeout/setInterval/setImmediate/requestAnimationFrame\n                if (handle && handle.ref && handle.unref && typeof handle.ref === 'function' &&\n                    typeof handle.unref === 'function') {\n                    task.ref = handle.ref.bind(handle);\n                    task.unref = handle.unref.bind(handle);\n                }\n                if (typeof handle === 'number' || handle) {\n                    return handle;\n                }\n                return task;\n            }\n            else {\n                // cause an error by calling it directly.\n                return delegate.apply(window, args);\n            }\n        }; });\n    clearNative =\n        patchMethod(window, cancelName, function (delegate) { return function (self, args) {\n            var id = args[0];\n            var task;\n            if (typeof id === 'number') {\n                // non nodejs env.\n                task = tasksByHandleId[id];\n            }\n            else {\n                // nodejs env.\n                task = id && id[taskSymbol];\n                // other environments.\n                if (!task) {\n                    task = id;\n                }\n            }\n            if (task && typeof task.type === 'string') {\n                if (task.state !== 'notScheduled' &&\n                    (task.cancelFn && task.data.isPeriodic || task.runCount === 0)) {\n                    if (typeof id === 'number') {\n                        delete tasksByHandleId[id];\n                    }\n                    else if (id) {\n                        id[taskSymbol] = null;\n                    }\n                    // Do not cancel already canceled functions\n                    task.zone.cancelTask(task);\n                }\n            }\n            else {\n                // cause an error by calling it directly.\n                delegate.apply(window, args);\n            }\n        }; });\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction patchCustomElements(_global, api) {\n    var _a = api.getGlobalObjects(), isBrowser = _a.isBrowser, isMix = _a.isMix;\n    if ((!isBrowser && !isMix) || !_global['customElements'] || !('customElements' in _global)) {\n        return;\n    }\n    var callbacks = ['connectedCallback', 'disconnectedCallback', 'adoptedCallback', 'attributeChangedCallback'];\n    api.patchCallbacks(api, _global.customElements, 'customElements', 'define', callbacks);\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nfunction eventTargetPatch(_global, api) {\n    if (Zone[api.symbol('patchEventTarget')]) {\n        // EventTarget is already patched.\n        return;\n    }\n    var _a = api.getGlobalObjects(), eventNames = _a.eventNames, zoneSymbolEventNames = _a.zoneSymbolEventNames, TRUE_STR = _a.TRUE_STR, FALSE_STR = _a.FALSE_STR, ZONE_SYMBOL_PREFIX = _a.ZONE_SYMBOL_PREFIX;\n    //  predefine all __zone_symbol__ + eventName + true/false string\n    for (var i = 0; i < eventNames.length; i++) {\n        var eventName = eventNames[i];\n        var falseEventName = eventName + FALSE_STR;\n        var trueEventName = eventName + TRUE_STR;\n        var symbol = ZONE_SYMBOL_PREFIX + falseEventName;\n        var symbolCapture = ZONE_SYMBOL_PREFIX + trueEventName;\n        zoneSymbolEventNames[eventName] = {};\n        zoneSymbolEventNames[eventName][FALSE_STR] = symbol;\n        zoneSymbolEventNames[eventName][TRUE_STR] = symbolCapture;\n    }\n    var EVENT_TARGET = _global['EventTarget'];\n    if (!EVENT_TARGET || !EVENT_TARGET.prototype) {\n        return;\n    }\n    api.patchEventTarget(_global, [EVENT_TARGET && EVENT_TARGET.prototype]);\n    return true;\n}\nfunction patchEvent$1(global, api) {\n    api.patchEventPrototype(global, api);\n}\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/**\n * @fileoverview\n * @suppress {missingRequire}\n */\nZone.__load_patch('legacy', function (global) {\n    var legacyPatch = global[Zone.__symbol__('legacyPatch')];\n    if (legacyPatch) {\n        legacyPatch();\n    }\n});\nZone.__load_patch('timers', function (global) {\n    var set = 'set';\n    var clear = 'clear';\n    patchTimer(global, set, clear, 'Timeout');\n    patchTimer(global, set, clear, 'Interval');\n    patchTimer(global, set, clear, 'Immediate');\n});\nZone.__load_patch('requestAnimationFrame', function (global) {\n    patchTimer(global, 'request', 'cancel', 'AnimationFrame');\n    patchTimer(global, 'mozRequest', 'mozCancel', 'AnimationFrame');\n    patchTimer(global, 'webkitRequest', 'webkitCancel', 'AnimationFrame');\n});\nZone.__load_patch('blocking', function (global, Zone) {\n    var blockingMethods = ['alert', 'prompt', 'confirm'];\n    for (var i = 0; i < blockingMethods.length; i++) {\n        var name_1 = blockingMethods[i];\n        patchMethod(global, name_1, function (delegate, symbol, name) {\n            return function (s, args) {\n                return Zone.current.run(delegate, global, args, name);\n            };\n        });\n    }\n});\nZone.__load_patch('EventTarget', function (global, Zone, api) {\n    patchEvent$1(global, api);\n    eventTargetPatch(global, api);\n    // patch XMLHttpRequestEventTarget's addEventListener/removeEventListener\n    var XMLHttpRequestEventTarget = global['XMLHttpRequestEventTarget'];\n    if (XMLHttpRequestEventTarget && XMLHttpRequestEventTarget.prototype) {\n        api.patchEventTarget(global, [XMLHttpRequestEventTarget.prototype]);\n    }\n    patchClass('MutationObserver');\n    patchClass('WebKitMutationObserver');\n    patchClass('IntersectionObserver');\n    patchClass('FileReader');\n});\nZone.__load_patch('on_property', function (global, Zone, api) {\n    propertyDescriptorPatch(api, global);\n    propertyPatch();\n});\nZone.__load_patch('customElements', function (global, Zone, api) {\n    patchCustomElements(global, api);\n});\nZone.__load_patch('XHR', function (global, Zone) {\n    // Treat XMLHttpRequest as a macrotask.\n    patchXHR(global);\n    var XHR_TASK = zoneSymbol('xhrTask');\n    var XHR_SYNC = zoneSymbol('xhrSync');\n    var XHR_LISTENER = zoneSymbol('xhrListener');\n    var XHR_SCHEDULED = zoneSymbol('xhrScheduled');\n    var XHR_URL = zoneSymbol('xhrURL');\n    var XHR_ERROR_BEFORE_SCHEDULED = zoneSymbol('xhrErrorBeforeScheduled');\n    function patchXHR(window) {\n        var XMLHttpRequest = window['XMLHttpRequest'];\n        if (!XMLHttpRequest) {\n            // XMLHttpRequest is not available in service worker\n            return;\n        }\n        var XMLHttpRequestPrototype = XMLHttpRequest.prototype;\n        function findPendingTask(target) {\n            return target[XHR_TASK];\n        }\n        var oriAddListener = XMLHttpRequestPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n        var oriRemoveListener = XMLHttpRequestPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n        if (!oriAddListener) {\n            var XMLHttpRequestEventTarget_1 = window['XMLHttpRequestEventTarget'];\n            if (XMLHttpRequestEventTarget_1) {\n                var XMLHttpRequestEventTargetPrototype = XMLHttpRequestEventTarget_1.prototype;\n                oriAddListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = XMLHttpRequestEventTargetPrototype[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n        }\n        var READY_STATE_CHANGE = 'readystatechange';\n        var SCHEDULED = 'scheduled';\n        function scheduleTask(task) {\n            var data = task.data;\n            var target = data.target;\n            target[XHR_SCHEDULED] = false;\n            target[XHR_ERROR_BEFORE_SCHEDULED] = false;\n            // remove existing event listener\n            var listener = target[XHR_LISTENER];\n            if (!oriAddListener) {\n                oriAddListener = target[ZONE_SYMBOL_ADD_EVENT_LISTENER];\n                oriRemoveListener = target[ZONE_SYMBOL_REMOVE_EVENT_LISTENER];\n            }\n            if (listener) {\n                oriRemoveListener.call(target, READY_STATE_CHANGE, listener);\n            }\n            var newListener = target[XHR_LISTENER] = function () {\n                if (target.readyState === target.DONE) {\n                    // sometimes on some browsers XMLHttpRequest will fire onreadystatechange with\n                    // readyState=4 multiple times, so we need to check task state here\n                    if (!data.aborted && target[XHR_SCHEDULED] && task.state === SCHEDULED) {\n                        // check whether the xhr has registered onload listener\n                        // if that is the case, the task should invoke after all\n                        // onload listeners finish.\n                        var loadTasks = target['__zone_symbol__loadfalse'];\n                        if (loadTasks && loadTasks.length > 0) {\n                            var oriInvoke_1 = task.invoke;\n                            task.invoke = function () {\n                                // need to load the tasks again, because in other\n                                // load listener, they may remove themselves\n                                var loadTasks = target['__zone_symbol__loadfalse'];\n                                for (var i = 0; i < loadTasks.length; i++) {\n                                    if (loadTasks[i] === task) {\n                                        loadTasks.splice(i, 1);\n                                    }\n                                }\n                                if (!data.aborted && task.state === SCHEDULED) {\n                                    oriInvoke_1.call(task);\n                                }\n                            };\n                            loadTasks.push(task);\n                        }\n                        else {\n                            task.invoke();\n                        }\n                    }\n                    else if (!data.aborted && target[XHR_SCHEDULED] === false) {\n                        // error occurs when xhr.send()\n                        target[XHR_ERROR_BEFORE_SCHEDULED] = true;\n                    }\n                }\n            };\n            oriAddListener.call(target, READY_STATE_CHANGE, newListener);\n            var storedTask = target[XHR_TASK];\n            if (!storedTask) {\n                target[XHR_TASK] = task;\n            }\n            sendNative.apply(target, data.args);\n            target[XHR_SCHEDULED] = true;\n            return task;\n        }\n        function placeholderCallback() { }\n        function clearTask(task) {\n            var data = task.data;\n            // Note - ideally, we would call data.target.removeEventListener here, but it's too late\n            // to prevent it from firing. So instead, we store info for the event listener.\n            data.aborted = true;\n            return abortNative.apply(data.target, data.args);\n        }\n        var openNative = patchMethod(XMLHttpRequestPrototype, 'open', function () { return function (self, args) {\n            self[XHR_SYNC] = args[2] == false;\n            self[XHR_URL] = args[1];\n            return openNative.apply(self, args);\n        }; });\n        var XMLHTTPREQUEST_SOURCE = 'XMLHttpRequest.send';\n        var fetchTaskAborting = zoneSymbol('fetchTaskAborting');\n        var fetchTaskScheduling = zoneSymbol('fetchTaskScheduling');\n        var sendNative = patchMethod(XMLHttpRequestPrototype, 'send', function () { return function (self, args) {\n            if (Zone.current[fetchTaskScheduling] === true) {\n                // a fetch is scheduling, so we are using xhr to polyfill fetch\n                // and because we already schedule macroTask for fetch, we should\n                // not schedule a macroTask for xhr again\n                return sendNative.apply(self, args);\n            }\n            if (self[XHR_SYNC]) {\n                // if the XHR is sync there is no task to schedule, just execute the code.\n                return sendNative.apply(self, args);\n            }\n            else {\n                var options = { target: self, url: self[XHR_URL], isPeriodic: false, args: args, aborted: false };\n                var task = scheduleMacroTaskWithCurrentZone(XMLHTTPREQUEST_SOURCE, placeholderCallback, options, scheduleTask, clearTask);\n                if (self && self[XHR_ERROR_BEFORE_SCHEDULED] === true && !options.aborted &&\n                    task.state === SCHEDULED) {\n                    // xhr request throw error when send\n                    // we should invoke task instead of leaving a scheduled\n                    // pending macroTask\n                    task.invoke();\n                }\n            }\n        }; });\n        var abortNative = patchMethod(XMLHttpRequestPrototype, 'abort', function () { return function (self, args) {\n            var task = findPendingTask(self);\n            if (task && typeof task.type == 'string') {\n                // If the XHR has already completed, do nothing.\n                // If the XHR has already been aborted, do nothing.\n                // Fix #569, call abort multiple times before done will cause\n                // macroTask task count be negative number\n                if (task.cancelFn == null || (task.data && task.data.aborted)) {\n                    return;\n                }\n                task.zone.cancelTask(task);\n            }\n            else if (Zone.current[fetchTaskAborting] === true) {\n                // the abort is called from fetch polyfill, we need to call native abort of XHR.\n                return abortNative.apply(self, args);\n            }\n            // Otherwise, we are trying to abort an XHR which has not yet been sent, so there is no\n            // task\n            // to cancel. Do nothing.\n        }; });\n    }\n});\nZone.__load_patch('geolocation', function (global) {\n    /// GEO_LOCATION\n    if (global['navigator'] && global['navigator'].geolocation) {\n        patchPrototype(global['navigator'].geolocation, ['getCurrentPosition', 'watchPosition']);\n    }\n});\nZone.__load_patch('PromiseRejectionEvent', function (global, Zone) {\n    // handle unhandled promise rejection\n    function findPromiseRejectionHandler(evtName) {\n        return function (e) {\n            var eventTasks = findEventTasks(global, evtName);\n            eventTasks.forEach(function (eventTask) {\n                // windows has added unhandledrejection event listener\n                // trigger the event listener\n                var PromiseRejectionEvent = global['PromiseRejectionEvent'];\n                if (PromiseRejectionEvent) {\n                    var evt = new PromiseRejectionEvent(evtName, { promise: e.promise, reason: e.rejection });\n                    eventTask.invoke(evt);\n                }\n            });\n        };\n    }\n    if (global['PromiseRejectionEvent']) {\n        Zone[zoneSymbol('unhandledPromiseRejectionHandler')] =\n            findPromiseRejectionHandler('unhandledrejection');\n        Zone[zoneSymbol('rejectionHandledHandler')] =\n            findPromiseRejectionHandler('rejectionhandled');\n    }\n});\n\n/**\n * @license\n * Copyright Google Inc. All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n})));\n", "/**\n * This file includes polyfills needed by Angular and is loaded before the app.\n * You can add your own extra polyfills to this file.\n *\n * This file is divided into 2 sections:\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\n *      file.\n *\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\n * automatically update themselves. This includes Safari >= 10, Chrome >= 55 (including Opera),\n * Edge >= 13 on the desktop, and iOS 10 and Chrome on mobile.\n *\n * Learn more in https://angular.io/guide/browser-support\n */\n\n/***************************************************************************************************\n * BROWSER POLYFILLS\n */\n\n/** IE10 and IE11 requires the following for NgClass support on SVG elements */\n// import 'classlist.js';  // Run `npm install --save classlist.js`.\n\n/**\n * Web Animations `@angular/platform-browser/animations`\n * Only required if AnimationBuilder is used within the application and using IE/Edge or Safari.\n * Standard animation support in Angular DOES NOT require any polyfills (as of Angular 6.0).\n */\n// import 'web-animations-js';  // Run `npm install --save web-animations-js`.\n\n/**\n * By default, zone.js will patch all possible macroTask and DomEvents\n * user can disable parts of macroTask/DomEvents patch by setting following flags\n * because those flags need to be set before `zone.js` being loaded, and webpack\n * will put import in the top of bundle, so user need to create a separate file\n * in this directory (for example: zone-flags.ts), and put the following flags\n * into that file, and then add the following code before importing zone.js.\n * import './zone-flags.ts';\n *\n * The flags allowed in zone-flags.ts are listed here.\n *\n * The following flags will work for all browsers.\n *\n * (window as any).__Zone_disable_requestAnimationFrame = true; // disable patch requestAnimationFrame\n * (window as any).__Zone_disable_on_property = true; // disable patch onProperty such as onclick\n * (window as any).__zone_symbol__BLACK_LISTED_EVENTS = ['scroll', 'mousemove']; // disable patch specified eventNames\n *\n *  in IE/Edge developer tools, the addEventListener will also be wrapped by zone.js\n *  with the following flag, it will bypass `zone.js` patch for IE/Edge\n *\n *  (window as any).__Zone_enable_cross_context_check = true;\n *\n */\n\n/***************************************************************************************************\n * Zone JS is required by default for Angular itself.\n */\nimport 'zone.js/dist/zone';  // Included with Angular CLI.\n\n\n/***************************************************************************************************\n * APPLICATION IMPORTS\n */\n"], "sourceRoot": "webpack:///"}