CREATE OR REPLACE
ALGORITHM = UNDEFINED 
DEFINER = 'wsshop'@'localhost'
VIEW `wsshop`.`po_book_list` AS
select
    `a`.`item_code` AS `item_code`
from
    (`wsshop`.`purchase_order` `a`
join `wsshop`.`po_book_list1` `b` on
    (((`a`.`item_code` = `b`.`item_code`)
        and (`a`.`id` = `b`.`id`)
            and (`a`.`site_id` in (select id from wscontent.site_mst where display_in_main_site='Y'))
                and (cast(`a`.`date_created` as date) > cast((sysdate() + interval '-1' month) as date)))))
order by
    `a`.`date_created` desc
limit 10