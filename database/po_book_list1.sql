CREATE OR REPLACE
ALGORITHM = UNDEFINED 
DEFINER = 'wsshop'@'localhost'
VIEW `wsshop`.`po_book_list1` AS
select
    `wsshop`.`purchase_order`.`item_code` AS `item_code`,
    max(`wsshop`.`purchase_order`.`id`) AS `id`
from
    `wsshop`.`purchase_order`
where
    ((`wsshop`.`purchase_order`.`site_id` in (select id from wscontent.site_mst where display_in_main_site='Y'))
        and (cast(`wsshop`.`purchase_order`.`date_created` as date) > cast((sysdate() + interval '-1' month) as date)))
group by
    `wsshop`.`purchase_order`.`item_code`