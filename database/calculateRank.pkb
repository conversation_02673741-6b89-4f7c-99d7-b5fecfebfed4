CREATE DEFINER=`root`@`localhost` PROCEDURE `wslog`.`calculateRank`(
	IN vQuizId int,
	in vTestEndDateStr varchar(25),
	out vNoOfTestTakers int
)
BEGIN
	#creating temporary table 
	CREATE TEMPORARY TABLE tmp_quizrecorder (
		`qrid` bigint(20) NOT NULL DEFAULT '0',
  	    `username` varchar(255) CHARACTER SET utf8 NOT NULL,
	    `quizid` int(11) DEFAULT NULL,
	    `endtime` datetime DEFAULT NULL,
	    `testEndDate` datetime DEFAULT NULL,
	    `score` double DEFAULT NULL,
	    `rank` int(11) DEFAULT NULL,
	    `rank1` bigint(60) DEFAULT NULL,
	    `same_rank_count` bigint(21) DEFAULT NULL,
	    `current_score` double DEFAULT NULL,
		`current_flag` int(11) DEFAULT NULL,
	    KEY `temp_quizrecorder_qrid_IDX` (`qrid`) USING BTREE
	) ENGINE=InnoDB DEFAULT CHARSET=utf8;
	
	#inserting calculated rank into this temporary table
	insert into tmp_quizrecorder (qrid,username,quizid,endtime,testEndDate,score,rank,rank1,same_rank_count,current_score,current_flag)
	select x.id qrid,x.username,x.quizid, x.endtime,STR_TO_DATE(vTestEndDateStr,'%Y-%m-%d %H:%i:%s') testEndDate,x.score,x.rank,
		@curRank := IF(@curScore != x.score, if(@curScore = 0 and @rankFlag = 0, 1,@curRank+@curCount), @curRank) AS rank1,
		@curCount := IF(@curScore != x.score, 1, @curCount+1) AS same_rank_count,
		@curScore := IF(@curScore != x.score, x.score, @curScore) AS current_score,
		@rankFlag := IF(@rankFlag = 0, @rankFlag+1, @rankFlag) AS current_flag
	from 
		(select a.id,a.username,a.quizid,a.endtime,a.score,a.rank from (select username, min(id) as minid 
		   from quizrecorder where quizid=vQuizId  group by username 
		) as b inner join quizrecorder as a on a.username = b.username 
	and a.id = b.minid and a.quizid=vQuizId  
	and a.endtime < DATE_ADD(STR_TO_DATE(vTestEndDateStr,'%Y-%m-%d %H:%i:%s'),interval +5 MINUTE)
	order by score desc, endtime) as x,
	(SELECT @curRank := 0) r,  (SELECT @curCount := 0) r1, (SELECT @curScore := 0) r2, (SELECT @rankFlag := 0) r3;

	#update quizrecorder with generated ranks
	update quizrecorder q inner join tmp_quizrecorder tq on q.id=tq.qrid set q.`rank`=tq.rank1;

    #returning the count of testers
	select count(*) into vNoOfTestTakers from tmp_quizrecorder;

	#dropping the temp table
    drop TEMPORARY table tmp_quizrecorder;
END