{"questions": [{"question": "What is a basic functionality of a test platform related to marking questions?", "op1": "Adding subjects to questions", "op2": "Marking questions as favorite", "op3": "Creating multiple-choice questions", "op4": "Removing questions from the database", "answer": "Marking questions as favorite", "difficulty": "Easy", "explanation": "Marking a question as favorite is a basic functionality of a test platform."}, {"question": "Which API is used to add a question as a favorite?", "op1": "/usermanagement/addUserMCQFavourite?questionId=<questionId>", "op2": "/funlearn/quizQuestionAnswers?favouriteMCQs=true", "op3": "/usermanagement/removeUserMCQFavourite?questionId=<questionId>", "op4": "/funlearn/quizQuestionAnswers?favouriteMCQs=true&subject=<>", "answer": "/usermanagement/addUserMCQFavourite?questionId=<questionId>", "difficulty": "Medium", "explanation": "The API '/usermanagement/addUserMCQFavourite?questionId=<questionId>' is used to add a question as a favorite."}, {"question": "What happens when a question is marked as favorite?", "op1": "The question is removed from the database", "op2": "The subject of the question is stored in the database", "op3": "The question is displayed on the homepage", "op4": "The question is shared with other users", "answer": "The subject of the question is stored in the database", "difficulty": "Easy", "explanation": "When a question is marked as favorite, its subject is also stored in the database."}, {"question": "What new option is created when a question is marked as favorite?", "op1": "Favorites", "op2": "History", "op3": "Settings", "op4": "Profile", "answer": "Favorites", "difficulty": "Easy", "explanation": "A new option 'Favorites' is created when a question is marked as favorite."}]}