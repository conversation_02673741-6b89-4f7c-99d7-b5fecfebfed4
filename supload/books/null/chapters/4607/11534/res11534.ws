**Key Points Summary:**

1. **Favorite Functionality**: Users can mark questions as favorites on the test platform.
  
2. **Storage**: When a question is marked as favorite, its subject is saved in the database.

3. **Favorites Option**: A new section called 'Favorites' is created for users to view their marked questions.

4. **Subject Filter**: Users can filter their favorite questions by selecting a subject from a dropdown menu.

5. **Phase 2 Feature**: Favorites marked in any section will also appear in the relevant book the user is currently viewing.

6. **APIs for Favorites**:
   - **Add Favorite**: To mark a question as favorite.
   - **Remove Favorite**: To unmark a question as favorite.
   - **Get All Favorites**: To retrieve a list of all favorite questions.
   - **Get Favorites by Subject**: To retrieve favorite questions filtered by a specific subject.
