Chapter Name: Test Platform Functionality - Marking Questions as Favorite

Study Notes:
- Marking a question as favorite is a basic functionality of a test platform.
- Users can mark a question as favorite, and it will not be shown again even if marked earlier.
- When a question is marked as favorite, its subject is stored in the database.
- A new option 'Favorites' is created, where users can see a list of their favorite questions.
- A dropdown with subjects is displayed on the 'Favorites' page for users to choose from.
- Phase 2: Favorite questions belonging to a particular book opened by the user should be displayed in that book as well.

APIs:
1. To add a favorite question: /usermanagement/addUserMCQFavourite?questionId=<question id>
2. To remove a favorite question: /usermanagement/removeUserMCQFavourite?questionId=<questionId>
3. To get all favorite questions: /funlearn/quizQuestionAnswers?favouriteMCQs=true
4. To get quizzes with subjects: /funlearn/quizQuestionAnswers?favouriteMCQs=true&subject=<subject>

Key Points:
- Marking questions as favorite is a key functionality of the test platform.
- Users can mark and unmark questions as favorite using specific APIs.
- Favorite questions are stored in the database along with their subjects.
- Users can view their favorite questions on the 'Favorites' page and filter them by subject.
- Phase 2 involves displaying favorite questions in the context of the book the user has opened.

Remember:
- Marking questions as favorite helps in revisiting important questions easily.
- Use the 'Favorites' feature to focus on key topics before exams.
- Make use of the APIs provided to manage favorite questions efficiently.
- Organize favorite questions by subject for better exam preparation.
