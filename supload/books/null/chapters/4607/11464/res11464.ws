### Chapter [Chapter Name] Key Points:
- Marking a question as favorite is a basic functionality of the test platform.
- Users have the option to mark a question as a favorite.
- When a question is marked as favorite, its subject is stored in the database.
- A new 'Favorites' option is created to view a list of favorite questions.
- Users can choose the subject of the favorite questions from a drop-down menu.
  
### Phase 2:
- Favorite questions from a specific book opened by the user will be displayed.
  
### APIs:
- Add favorite: `/usermanagement/addUserMCQFavourite?questionId=<question id>`
- Remove favorite: `/usermanagement/removeUserMCQFavourite?questionId=<questionId>`
- Get all favorites: `/funlearn/quizQuestionAnswers?favouriteMCQs=true`
- Get quizzes with subjects: `/funlearn/quizQuestionAnswers?favouriteMCQs=true&subject=<subject>`
