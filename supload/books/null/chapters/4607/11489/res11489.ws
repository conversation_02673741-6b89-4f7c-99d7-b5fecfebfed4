Key Points and Main Arguments in Chapter:
- Marking a question as favorite is a basic functionality of the test platform.
- Users have the option to mark a question as favorite, and it will not be shown again even if marked earlier.
- When a question is marked as favorite, its subject is stored in the database.
- A new option 'Favorites' is created, where users can view a list of their favorite questions and select subjects using a dropdown.
- In Phase 2, favorite questions belonging to a book the user has opened should also be displayed.
- APIs are provided for adding, removing, and retrieving favorite questions.
- API endpoints include /usermanagement/addUserMCQFavourite, /usermanagement/removeUserMCQFavourite, and /funlearn/quizQuestionAnswers.
- Users can get all favorite questions using the endpoint /funlearn/quizQuestionAnswers?favouriteMCQs=true.
- Users can also retrieve quizzes with subjects using the endpoint /funlearn/quizQuestionAnswers?favouriteMCQs=true&subject=<>.
