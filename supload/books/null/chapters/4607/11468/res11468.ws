Front: Marking a question as favorite
Back: Basic functionality of a test platform

Front: Option to mark a question as favorite
Back: Provided for the user

Front: Storing the subject of a favorite question
Back: In the database

Front: Creating a new option 'Favorites'
Back: List of marked questions displayed

Front: Selecting a subject for favorite questions
Back: Dropdown with subjects

Front: Displaying favorites from a specific book
Back: Irrespective of where they were added

Front: API for adding a favorite question
Back: /usermanagement/addUserMCQFavourite?questionId=<question id>

Front: API for removing a favorite question
Back: /usermanagement/removeUserMCQFavourite?questionId=<questionId>

Front: API for getting all favorite questions
Back: /funlearn/quizQuestionAnswers?favouriteMCQs=true

Front: API for getting quizzes with subjects
Back: /funlearn/quizQuestionAnswers?favouriteMCQs=true&subject=<>
