#
# A fatal error has been detected by the Java Runtime Environment:
#
#  SIGBUS (0xa) at pc=0x000000010fab7dc0, pid=57973, tid=0x0000000000005a07
#
# JRE version: Java(TM) SE Runtime Environment (8.0_211-b12) (build 1.8.0_211-b12)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.211-b12 mixed mode bsd-amd64 compressed oops)
# Problematic frame:
# V  [libjvm.dylib+0x2b7dc0]  binary_search(Array<Method*>*, Symbol*)+0x2c
#
# Failed to write core dump. Core dumps have been disabled. To enable core dumping, try "ulimit -c unlimited" before starting Java again
#
# If you would like to submit a bug report, please visit:
#   http://bugreport.java.com/bugreport/crash.jsp
#

---------------  T H R E A D  ---------------

Current thread (0x00007ff91fd23800):  JavaThread "localhost-startStop-2" daemon [_thread_in_vm, id=23047, stack(0x0000700011583000,0x0000700011683000)]

siginfo: si_signo: 10 (SIGBUS), si_code: 2 (BUS_ADRERR), si_addr: 0x000000012c1c5d70

Registers:
RAX=0x0000000000000003, RBX=0x0000000000000000, RCX=0x0000000000000007, RDX=0x0000000000000000
RSP=0x0000700011681be0, RBP=0x0000700011681be0, RSI=0x00007ff91eda6c10, RDI=0x000000012c1c69d0
R8 =0x000000012c1c4100, R9 =0x0000000000000384, R10=0x0000000000000001, R11=0x00000007c0035040
R12=0x000000012c1c69d0, R13=0x00007000116822e8, R14=0x00007ff91eda6c10, R15=0x00000007c0ac4aa0
RIP=0x000000010fab7dc0, EFLAGS=0x0000000000010207, ERR=0x0000000000000004
  TRAPNO=0x000000000000000e

Top of Stack: (sp=0x0000700011681be0)
0x0000700011681be0:   0000700011681c40 000000010fab7eb0
0x0000700011681bf0:   0000700011681c20 0000000100000000
0x0000700011681c00:   00007ff91eda6c10 00007ff91ed96340
0x0000700011681c10:   000000012c1c69d0 00007ff91ed96340
0x0000700011681c20:   000000012c1c69d0 00007000116822e8
0x0000700011681c30:   00007ff91eda6c10 00000007c0ac4aa0
0x0000700011681c40:   0000700011681c70 000000010fab8115
0x0000700011681c50:   00007ff91ed96340 00007000116820c0
0x0000700011681c60:   00007ff91eda6c10 00000007c0ac4e18
0x0000700011681c70:   0000700011681cd0 000000010fbcd21c
0x0000700011681c80:   0000000100000000 0000000000000000
0x0000700011681c90:   00007ff91fd23800 00000007c047db58
0x0000700011681ca0:   0000700011681d20 00007000116820c0
0x0000700011681cb0:   00007ff91fd23800 00007000116822e8
0x0000700011681cc0:   00007ff91fd23800 00000007c0ac4e18
0x0000700011681cd0:   0000700011681e70 000000010fbcda2c
0x0000700011681ce0:   00007ff91fd23800 00007ff920026988
0x0000700011681cf0:   00000001000003d8 00000007c0ac3ad0
0x0000700011681d00:   00007ff91eda6c10 00007ff91ed96340
0x0000700011681d10:   00007ff91fd23800 00000007c047db58
0x0000700011681d20:   0000700011681ec0 000000010fbcdd14
0x0000700011681d30:   00007ff91fd23800 00007ff9200265a0
0x0000700011681d40:   00000001200265b0 00000007c047db58
0x0000700011681d50:   00007ff91ef42d40 00007ff91ed96700
0x0000700011681d60:   0000000118ec7e88 0000000000000000
0x0000700011681d70:   00007ff91fd23800 00000007c047db58
0x0000700011681d80:   0000700011681f20 000000010fbcdd14
0x0000700011681d90:   00007ff91fd23800 0000000118eb2858
0x0000700011681da0:   0000000118eb0d80 00000007c047db58
0x0000700011681db0:   0000000118ec7da0 00007ff91fd23800
0x0000700011681dc0:   0000000118ec7da0 0000000118eb0d80
0x0000700011681dd0:   0000700011682100 000000010fbd0e7d 

Instructions: (pc=0x000000010fab7dc0)
0x000000010fab7da0:   ff c0 89 c2 eb 2d 8d 04 11 d1 f8 4c 63 c0 4e 8b
0x000000010fab7db0:   44 c7 08 4d 8b 40 08 45 0f b7 48 22 4d 8b 40 08
0x000000010fab7dc0:   4f 8b 44 c8 50 49 39 f0 72 d6 49 39 f0 74 0d ff
0x000000010fab7dd0:   c8 89 c1 39 ca 7e cf b8 ff ff ff ff 5d c3 55 48 

Register to memory mapping:

RAX=0x0000000000000003 is an unknown value
RBX=0x0000000000000000 is an unknown value
RCX=0x0000000000000007 is an unknown value
RDX=0x0000000000000000 is an unknown value
RSP=0x0000700011681be0 is pointing into the stack for thread: 0x00007ff91fd23800
RBP=0x0000700011681be0 is pointing into the stack for thread: 0x00007ff91fd23800
RSI=0x00007ff91eda6c10 is an unknown value
RDI=0x000000012c1c69d0 is pointing into metadata
R8 =0x000000012c1c4100 is pointing into metadata
R9 =0x0000000000000384 is an unknown value
R10=0x0000000000000001 is an unknown value
R11=0x00000007c0035040 is pointing into metadata
R12=0x000000012c1c69d0 is pointing into metadata
R13=0x00007000116822e8 is pointing into the stack for thread: 0x00007ff91fd23800
R14=0x00007ff91eda6c10 is an unknown value
R15=0x00000007c0ac4aa0 is pointing into metadata


Stack: [0x0000700011583000,0x0000700011683000],  sp=0x0000700011681be0,  free space=1018k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [libjvm.dylib+0x2b7dc0]  binary_search(Array<Method*>*, Symbol*)+0x2c
V  [libjvm.dylib+0x2b7eb0]  InstanceKlass::find_method_index(Array<Method*>*, Symbol*, Symbol*, Klass::OverpassLookupMode, Klass::StaticLookupMode, Klass::PrivateLookupMode)+0x2c
V  [libjvm.dylib+0x2b8115]  InstanceKlass::uncached_lookup_method(Symbol*, Symbol*, Klass::OverpassLookupMode) const+0x31
V  [libjvm.dylib+0x3cd21c]  LinkResolver::lookup_method_in_klasses(methodHandle&, KlassHandle, Symbol*, Symbol*, bool, bool, Thread*)+0x3c
V  [libjvm.dylib+0x3cda2c]  LinkResolver::resolve_method(methodHandle&, KlassHandle, Symbol*, Symbol*, KlassHandle, bool, bool, Thread*)+0x7e
V  [libjvm.dylib+0x3cdd75]  LinkResolver::linktime_resolve_virtual_method(methodHandle&, KlassHandle, Symbol*, Symbol*, KlassHandle, bool, Thread*)+0x47
V  [libjvm.dylib+0x3d01ab]  LinkResolver::resolve_virtual_call(CallInfo&, Handle, KlassHandle, KlassHandle, Symbol*, Symbol*, KlassHandle, bool, bool, Thread*)+0x57
V  [libjvm.dylib+0x3d03d1]  LinkResolver::resolve_invokevirtual(CallInfo&, Handle, constantPoolHandle, int, Thread*)+0x143
V  [libjvm.dylib+0x3d2068]  LinkResolver::resolve_invoke(CallInfo&, Handle, constantPoolHandle, int, Bytecodes::Code, Thread*)+0x208
V  [libjvm.dylib+0x2eb0fe]  InterpreterRuntime::resolve_invoke(JavaThread*, Bytecodes::Code)+0x364
j  org.apache.tomcat.websocket.server.WsContextListener.contextDestroyed(Ljavax/servlet/ServletContextEvent;)V+25
j  org.apache.catalina.core.StandardContext.listenerStop()Z+159
j  org.apache.catalina.core.StandardContext.stopInternal()V+122
j  org.apache.catalina.util.LifecycleBase.stop()V+214
j  org.apache.catalina.core.ContainerBase$StopChild.call()Ljava/lang/Void;+19
j  org.apache.catalina.core.ContainerBase$StopChild.call()Ljava/lang/Object;+1
j  java.util.concurrent.FutureTask.run$$$capture()V+42
J 9014 C1 java.util.concurrent.FutureTask.run()V (18 bytes) @ 0x000000011ac5ec6c [0x000000011ac5ec20+0x4c]
J 11577 C1 java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V (225 bytes) @ 0x000000011b125334 [0x000000011b1250c0+0x274]
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5
j  java.lang.Thread.run()V+11
v  ~StubRoutines::call_stub
V  [libjvm.dylib+0x2f0d1e]  JavaCalls::call_helper(JavaValue*, methodHandle*, JavaCallArguments*, Thread*)+0x6ae
V  [libjvm.dylib+0x2f14c2]  JavaCalls::call_virtual(JavaValue*, KlassHandle, Symbol*, Symbol*, JavaCallArguments*, Thread*)+0x164
V  [libjvm.dylib+0x2f166e]  JavaCalls::call_virtual(JavaValue*, Handle, KlassHandle, Symbol*, Symbol*, Thread*)+0x4a
V  [libjvm.dylib+0x34bf5d]  thread_entry(JavaThread*, Thread*)+0x7c
V  [libjvm.dylib+0x570b93]  JavaThread::thread_main_inner()+0x9b
V  [libjvm.dylib+0x57228e]  JavaThread::run()+0x1c2
V  [libjvm.dylib+0x48f30a]  java_start(Thread*)+0xf6
C  [libsystem_pthread.dylib+0x6109]  _pthread_start+0x94
C  [libsystem_pthread.dylib+0x1b8b]  thread_start+0xf
C  0x0000000000000000

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  org.apache.tomcat.websocket.server.WsContextListener.contextDestroyed(Ljavax/servlet/ServletContextEvent;)V+25
j  org.apache.catalina.core.StandardContext.listenerStop()Z+159
j  org.apache.catalina.core.StandardContext.stopInternal()V+122
j  org.apache.catalina.util.LifecycleBase.stop()V+214
j  org.apache.catalina.core.ContainerBase$StopChild.call()Ljava/lang/Void;+19
j  org.apache.catalina.core.ContainerBase$StopChild.call()Ljava/lang/Object;+1
j  java.util.concurrent.FutureTask.run$$$capture()V+42
J 9014 C1 java.util.concurrent.FutureTask.run()V (18 bytes) @ 0x000000011ac5ec6c [0x000000011ac5ec20+0x4c]
J 11577 C1 java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V (225 bytes) @ 0x000000011b125334 [0x000000011b1250c0+0x274]
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5
j  java.lang.Thread.run()V+11
v  ~StubRoutines::call_stub

---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
=>0x00007ff91fd23800 JavaThread "localhost-startStop-2" daemon [_thread_in_vm, id=23047, stack(0x0000700011583000,0x0000700011683000)]
  0x00007ff91fd72000 JavaThread "Tomcat-startStop-2" daemon [_thread_blocked, id=39183, stack(0x000070001137d000,0x000070001147d000)]
  0x00007ff920809800 JavaThread "Thread-8" [_thread_blocked, id=30535, stack(0x0000700012ece000,0x0000700012fce000)]
  0x00007ff9269d6000 JavaThread "SIGTERM handler" daemon [_thread_blocked, id=18451, stack(0x000070001085c000,0x000070001095c000)]
  0x00007ff91ff52000 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_in_native, id=19471, stack(0x0000700011177000,0x0000700011277000)]
  0x00007ff91f364000 JavaThread "Thread-12" daemon [_thread_blocked, id=29967, stack(0x0000700012dcb000,0x0000700012ecb000)]
  0x00007ff91f80e000 JavaThread "DestroyJavaVM" [_thread_blocked, id=2819, stack(0x000070000fa2f000,0x000070000fb2f000)]
  0x00007ff928117000 JavaThread "http-nio-8080-AsyncTimeout" daemon [_thread_blocked, id=28675, stack(0x00007000129bf000,0x0000700012abf000)]
  0x00007ff9214be800 JavaThread "http-nio-8080-Acceptor-0" daemon [_thread_blocked, id=36867, stack(0x00007000128bc000,0x00007000129bc000)]
  0x00007ff9214bf000 JavaThread "http-nio-8080-ClientPoller-1" daemon [_thread_in_native, id=28163, stack(0x00007000127b9000,0x00007000128b9000)]
  0x00007ff9214bd800 JavaThread "http-nio-8080-ClientPoller-0" daemon [_thread_in_native, id=27651, stack(0x00007000126b6000,0x00007000127b6000)]
  0x00007ff9264ff800 JavaThread "http-nio-8080-exec-10" daemon [_thread_blocked, id=27139, stack(0x00007000125b3000,0x00007000126b3000)]
  0x00007ff91fe64000 JavaThread "http-nio-8080-exec-9" daemon [_thread_blocked, id=37379, stack(0x00007000124b0000,0x00007000125b0000)]
  0x00007ff9264ff000 JavaThread "http-nio-8080-exec-8" daemon [_thread_blocked, id=26371, stack(0x00007000123ad000,0x00007000124ad000)]
  0x00007ff9214bd000 JavaThread "http-nio-8080-exec-7" daemon [_thread_blocked, id=37891, stack(0x00007000122aa000,0x00007000123aa000)]
  0x00007ff920928000 JavaThread "http-nio-8080-exec-6" daemon [_thread_blocked, id=26115, stack(0x00007000121a7000,0x00007000122a7000)]
  0x00007ff9214bc000 JavaThread "http-nio-8080-exec-5" daemon [_thread_blocked, id=25859, stack(0x00007000120a4000,0x00007000121a4000)]
  0x00007ff9214a2800 JavaThread "http-nio-8080-exec-4" daemon [_thread_blocked, id=25347, stack(0x0000700011fa1000,0x00007000120a1000)]
  0x00007ff91fe63000 JavaThread "http-nio-8080-exec-3" daemon [_thread_blocked, id=25091, stack(0x0000700011e9e000,0x0000700011f9e000)]
  0x00007ff91fe3d800 JavaThread "http-nio-8080-exec-2" daemon [_thread_blocked, id=43019, stack(0x0000700011d9b000,0x0000700011e9b000)]
  0x00007ff9264fb800 JavaThread "http-nio-8080-exec-1" daemon [_thread_blocked, id=39943, stack(0x000070001127a000,0x000070001137a000)]
  0x00007ff91fe5e800 JavaThread "NioBlockingSelector.BlockPoller-1" daemon [_thread_in_native, id=42503, stack(0x0000700011074000,0x0000700011174000)]
  0x00007ff91ffb3800 JavaThread "container-0" [_thread_blocked, id=39431, stack(0x0000700011c98000,0x0000700011d98000)]
  0x00007ff9264ac000 JavaThread "AWT-AppKit" daemon [_thread_in_native, id=775, stack(0x00007ffee0db5000,0x00007ffee15b5000)]
  0x00007ff926429000 JavaThread "Abandoned connection cleanup thread" daemon [_thread_blocked, id=23299, stack(0x0000700011480000,0x0000700011580000)]
  0x00007ff91f555000 JavaThread "FileSystemWatcher: files=#501 cl=sun.misc.Launcher$AppClassLoader@18b4aac2" daemon [_thread_blocked, id=22275, stack(0x0000700010f71000,0x0000700011071000)]
  0x00007ff91f849800 JavaThread "Service Thread" daemon [_thread_blocked, id=16899, stack(0x0000700010d6b000,0x0000700010e6b000)]
  0x00007ff920e32800 JavaThread "C1 CompilerThread2" daemon [_thread_blocked, id=16387, stack(0x0000700010c68000,0x0000700010d68000)]
  0x00007ff920e32000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=16131, stack(0x0000700010b65000,0x0000700010c65000)]
  0x00007ff920e31000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=15619, stack(0x0000700010a62000,0x0000700010b62000)]
  0x00007ff92080a800 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=17923, stack(0x000070001095f000,0x0000700010a5f000)]
  0x00007ff92080d000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=18691, stack(0x0000700010759000,0x0000700010859000)]
  0x00007ff91f813000 JavaThread "Finalizer" daemon [_thread_blocked, id=12803, stack(0x0000700010550000,0x0000700010650000)]
  0x00007ff91f037800 JavaThread "Reference Handler" daemon [_thread_blocked, id=20227, stack(0x000070001044d000,0x000070001054d000)]

Other Threads:
  0x00007ff91f031000 VMThread [stack: 0x000070001034a000,0x000070001044a000] [id=20483]
  0x00007ff91f878000 WatcherThread [stack: 0x0000700010e6e000,0x0000700010f6e000] [id=21763]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x0000000790000000, size: 768 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 260096K, used 212396K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 258048K, 82% used [0x00000007b0000000,0x00000007bcf2b120,0x00000007bfc00000)
  from space 2048K, 12% used [0x00000007bfc00000,0x00000007bfc40000,0x00000007bfe00000)
  to   space 2048K, 0% used [0x00000007bfe00000,0x00000007bfe00000,0x00000007c0000000)
 ParOldGen       total 524288K, used 420282K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a6e8d0,0x00000007b0000000)
 Metaspace       used 157437K, capacity 161374K, committed 168152K, reserved 1202176K
  class space    used 13441K, capacity 14328K, committed 16384K, reserved 1048576K

Card table byte_map: [0x0000000110b79000,0x0000000110cfa000] byte_map_base: 0x000000010cef9000

Marking Bits: (ParMarkBitMap*) 0x00000001100fbe40
 Begin Bits: [0x0000000110dfa000, 0x00000001119fa000)
 End Bits:   [0x00000001119fa000, 0x00000001125fa000)

Polling page: 0x000000010e6be000

CodeCache: size=245760Kb used=34509Kb max_used=34623Kb free=211250Kb
 bounds [0x000000011975b000, 0x000000011b95b000, 0x000000012875b000]
 total_blobs=15577 nmethods=14706 adapters=791
 compilation: enabled

Compilation events (10 events):
Event: 53222.134 Thread 0x00007ff920e32800 15444       1       java.text.DecimalFormat::expandAffix (188 bytes)
Event: 53222.136 Thread 0x00007ff920e32800 nmethod 15444 0x000000011b72a1d0 code [0x000000011b72a420, 0x000000011b72b328]
Event: 53222.137 Thread 0x00007ff920e32800 15445       1       java.text.NumberFormat::getMaximumIntegerDigits (5 bytes)
Event: 53222.137 Thread 0x00007ff920e32800 nmethod 15445 0x000000011b857910 code [0x000000011b857a60, 0x000000011b857b70]
Event: 53222.139 Thread 0x00007ff920e32800 15446       1       java.text.NumberFormat::getMinimumIntegerDigits (5 bytes)
Event: 53222.139 Thread 0x00007ff920e32800 nmethod 15446 0x000000011b8a6f10 code [0x000000011b8a7060, 0x000000011b8a7170]
Event: 53222.140 Thread 0x00007ff920e32800 15447       1       java.text.NumberFormat::getMaximumFractionDigits (5 bytes)
Event: 53222.141 Thread 0x00007ff920e32800 nmethod 15447 0x000000011b1b6650 code [0x000000011b1b67a0, 0x000000011b1b68b0]
Event: 53222.142 Thread 0x00007ff920e32800 15448       1       java.text.NumberFormat::getMinimumFractionDigits (5 bytes)
Event: 53222.142 Thread 0x00007ff920e32800 nmethod 15448 0x000000011b1b6390 code [0x000000011b1b64e0, 0x000000011b1b65f0]

GC Heap History (10 events):
Event: 50992.167 GC heap before
{Heap before GC invocations=283 (full 6):
 PSYoungGen      total 259584K, used 257280K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 257024K, 100% used [0x00000007b0000000,0x00000007bfb00000,0x00000007bfb00000)
  from space 2560K, 10% used [0x00000007bfd80000,0x00000007bfdc0000,0x00000007c0000000)
  to   space 2560K, 0% used [0x00000007bfb00000,0x00000007bfb00000,0x00000007bfd80000)
 ParOldGen       total 524288K, used 420242K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a648d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
Event: 50992.180 GC heap after
Heap after GC invocations=283 (full 6):
 PSYoungGen      total 259584K, used 192K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 257024K, 0% used [0x00000007b0000000,0x00000007b0000000,0x00000007bfb00000)
  from space 2560K, 7% used [0x00000007bfb00000,0x00000007bfb30000,0x00000007bfd80000)
  to   space 2560K, 0% used [0x00000007bfd80000,0x00000007bfd80000,0x00000007c0000000)
 ParOldGen       total 524288K, used 420250K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a668d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
}
Event: 51525.901 GC heap before
{Heap before GC invocations=284 (full 6):
 PSYoungGen      total 259584K, used 257216K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 257024K, 100% used [0x00000007b0000000,0x00000007bfb00000,0x00000007bfb00000)
  from space 2560K, 7% used [0x00000007bfb00000,0x00000007bfb30000,0x00000007bfd80000)
  to   space 2560K, 0% used [0x00000007bfd80000,0x00000007bfd80000,0x00000007c0000000)
 ParOldGen       total 524288K, used 420250K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a668d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
Event: 51525.909 GC heap after
Heap after GC invocations=284 (full 6):
 PSYoungGen      total 259584K, used 192K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 257024K, 0% used [0x00000007b0000000,0x00000007b0000000,0x00000007bfb00000)
  from space 2560K, 7% used [0x00000007bfd80000,0x00000007bfdb0000,0x00000007c0000000)
  to   space 2560K, 0% used [0x00000007bfb00000,0x00000007bfb00000,0x00000007bfd80000)
 ParOldGen       total 524288K, used 420258K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a688d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
}
Event: 52069.101 GC heap before
{Heap before GC invocations=285 (full 6):
 PSYoungGen      total 259584K, used 257216K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 257024K, 100% used [0x00000007b0000000,0x00000007bfb00000,0x00000007bfb00000)
  from space 2560K, 7% used [0x00000007bfd80000,0x00000007bfdb0000,0x00000007c0000000)
  to   space 2560K, 0% used [0x00000007bfb00000,0x00000007bfb00000,0x00000007bfd80000)
 ParOldGen       total 524288K, used 420258K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a688d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
Event: 52069.106 GC heap after
Heap after GC invocations=285 (full 6):
 PSYoungGen      total 259584K, used 224K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 257024K, 0% used [0x00000007b0000000,0x00000007b0000000,0x00000007bfb00000)
  from space 2560K, 8% used [0x00000007bfb00000,0x00000007bfb38000,0x00000007bfd80000)
  to   space 2048K, 0% used [0x00000007bfe00000,0x00000007bfe00000,0x00000007c0000000)
 ParOldGen       total 524288K, used 420266K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a6a8d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
}
Event: 52609.657 GC heap before
{Heap before GC invocations=286 (full 6):
 PSYoungGen      total 259584K, used 257248K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 257024K, 100% used [0x00000007b0000000,0x00000007bfb00000,0x00000007bfb00000)
  from space 2560K, 8% used [0x00000007bfb00000,0x00000007bfb38000,0x00000007bfd80000)
  to   space 2048K, 0% used [0x00000007bfe00000,0x00000007bfe00000,0x00000007c0000000)
 ParOldGen       total 524288K, used 420266K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a6a8d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
Event: 52609.669 GC heap after
Heap after GC invocations=286 (full 6):
 PSYoungGen      total 260096K, used 224K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 258048K, 0% used [0x00000007b0000000,0x00000007b0000000,0x00000007bfc00000)
  from space 2048K, 10% used [0x00000007bfe00000,0x00000007bfe38000,0x00000007c0000000)
  to   space 2048K, 0% used [0x00000007bfc00000,0x00000007bfc00000,0x00000007bfe00000)
 ParOldGen       total 524288K, used 420274K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a6c8d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
}
Event: 53151.855 GC heap before
{Heap before GC invocations=287 (full 6):
 PSYoungGen      total 260096K, used 258272K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 258048K, 100% used [0x00000007b0000000,0x00000007bfc00000,0x00000007bfc00000)
  from space 2048K, 10% used [0x00000007bfe00000,0x00000007bfe38000,0x00000007c0000000)
  to   space 2048K, 0% used [0x00000007bfc00000,0x00000007bfc00000,0x00000007bfe00000)
 ParOldGen       total 524288K, used 420274K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a6c8d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
Event: 53151.864 GC heap after
Heap after GC invocations=287 (full 6):
 PSYoungGen      total 260096K, used 256K [0x00000007b0000000, 0x00000007c0000000, 0x00000007c0000000)
  eden space 258048K, 0% used [0x00000007b0000000,0x00000007b0000000,0x00000007bfc00000)
  from space 2048K, 12% used [0x00000007bfc00000,0x00000007bfc40000,0x00000007bfe00000)
  to   space 2048K, 0% used [0x00000007bfe00000,0x00000007bfe00000,0x00000007c0000000)
 ParOldGen       total 524288K, used 420282K [0x0000000790000000, 0x00000007b0000000, 0x00000007b0000000)
  object space 524288K, 80% used [0x0000000790000000,0x00000007a9a6e8d0,0x00000007b0000000)
 Metaspace       used 156646K, capacity 160142K, committed 168152K, reserved 1202176K
  class space    used 13218K, capacity 13979K, committed 16384K, reserved 1048576K
}

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 53222.167 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException': Socket closed> (0x00000007bcda01c8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 736]
Event: 53222.169 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException'> (0x00000007bcda0768) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 53222.169 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException': Socket closed> (0x00000007bcda3fd8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 736]
Event: 53222.171 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException'> (0x00000007bcda4578) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 53222.173 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException': Socket closed> (0x00000007bcda7de8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 736]
Event: 53222.173 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException'> (0x00000007bcda8388) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 53222.175 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException': Socket closed> (0x00000007bcdabbf8) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 736]
Event: 53222.175 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException'> (0x00000007bcdac198) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 710]
Event: 53222.175 Thread 0x00007ff920809800 Exception <a 'java/net/SocketException': Socket closed> (0x00000007bcdafa08) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jni.cpp, line 736]
Event: 53222.277 Thread 0x00007ff920d46800 Exception <a 'java/lang/InterruptedException': sleep interrupted> (0x00000007b1bdad20) thrown at [/Users/<USER>/workspace/8-2-build-macosx-x86_64/jdk8u211/12973/hotspot/src/share/vm/prims/jvm.cpp, line 3336]

Events (10 events):
Event: 53222.375 Executing VM operation: RevokeBias
Event: 53222.375 Executing VM operation: RevokeBias done
Event: 53222.376 Executing VM operation: RevokeBias
Event: 53222.376 Executing VM operation: RevokeBias done
Event: 53222.376 Executing VM operation: RevokeBias
Event: 53222.376 Executing VM operation: RevokeBias done
Event: 53222.381 Executing VM operation: RevokeBias
Event: 53222.381 Executing VM operation: RevokeBias done
Event: 53222.381 Executing VM operation: RevokeBias
Event: 53222.381 Executing VM operation: RevokeBias done


Dynamic libraries:
0x00007fff35a17000 	/System/Library/Frameworks/Cocoa.framework/Versions/A/Cocoa
0x00007fff43248000 	/System/Library/Frameworks/Security.framework/Versions/A/Security
0x00007fff3497c000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/ApplicationServices
0x00007fff6ff93000 	/usr/lib/libz.1.dylib
0x00007fff6d9c9000 	/usr/lib/libSystem.B.dylib
0x00007fff6f8c0000 	/usr/lib/libobjc.A.dylib
0x00007fff368ca000 	/System/Library/Frameworks/CoreFoundation.framework/Versions/A/CoreFoundation
0x00007fff38faa000 	/System/Library/Frameworks/Foundation.framework/Versions/C/Foundation
0x00007fff33b6c000 	/System/Library/Frameworks/AppKit.framework/Versions/C/AppKit
0x00007fff3639b000 	/System/Library/Frameworks/CoreData.framework/Versions/A/CoreData
0x00007fff679de000 	/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation
0x00007fff62a72000 	/System/Library/PrivateFrameworks/RemoteViewServices.framework/Versions/A/RemoteViewServices
0x00007fff6a539000 	/System/Library/PrivateFrameworks/XCTTargetBootstrap.framework/Versions/A/XCTTargetBootstrap
0x00007fff36797000 	/System/Library/Frameworks/CoreDisplay.framework/Versions/A/CoreDisplay
0x00007fff3be69000 	/System/Library/Frameworks/Metal.framework/Versions/A/Metal
0x00007fff50a5f000 	/System/Library/PrivateFrameworks/DesktopServicesPriv.framework/Versions/A/DesktopServicesPriv
0x00007fff6eb9b000 	/usr/lib/liblangid.dylib
0x00007fff4fcf4000 	/System/Library/PrivateFrameworks/CoreSVG.framework/Versions/A/CoreSVG
0x00007fff65bce000 	/System/Library/PrivateFrameworks/SkyLight.framework/Versions/A/SkyLight
0x00007fff36d4c000 	/System/Library/Frameworks/CoreGraphics.framework/Versions/A/CoreGraphics
0x00007fff324db000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Accelerate
0x00007fff6fe84000 	/usr/lib/libxml2.2.dylib
0x00007fff5b9fd000 	/System/Library/PrivateFrameworks/IconServices.framework/Versions/A/IconServices
0x00007fff39789000 	/System/Library/Frameworks/IOSurface.framework/Versions/A/IOSurface
0x00007fff6d4f3000 	/usr/lib/libDiagnosticMessagesClient.dylib
0x00007fff508b9000 	/System/Library/PrivateFrameworks/DFRFoundation.framework/Versions/A/DFRFoundation
0x00007fff6e92a000 	/usr/lib/libicucore.A.dylib
0x00007fff34cae000 	/System/Library/Frameworks/AudioToolbox.framework/Versions/A/AudioToolbox
0x00007fff34d91000 	/System/Library/Frameworks/AudioUnit.framework/Versions/A/AudioUnit
0x00007fff6dc61000 	/usr/lib/libauto.dylib
0x00007fff5095b000 	/System/Library/PrivateFrameworks/DataDetectorsCore.framework/Versions/A/DataDetectorsCore
0x00007fff35538000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/HIToolbox.framework/Versions/A/HIToolbox
0x00007fff42442000 	/System/Library/Frameworks/QuartzCore.framework/Versions/A/QuartzCore
0x00007fff35877000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SpeechRecognition.framework/Versions/A/SpeechRecognition
0x00007fff503cd000 	/System/Library/PrivateFrameworks/CoreUI.framework/Versions/A/CoreUI
0x00007fff35e06000 	/System/Library/Frameworks/CoreAudio.framework/Versions/A/CoreAudio
0x00007fff38c69000 	/System/Library/Frameworks/DiskArbitration.framework/Versions/A/DiskArbitration
0x00007fff5da4d000 	/System/Library/PrivateFrameworks/MultitouchSupport.framework/Versions/A/MultitouchSupport
0x00007fff6e7f1000 	/usr/lib/libenergytrace.dylib
0x00007fff396e3000 	/System/Library/Frameworks/IOKit.framework/Versions/A/IOKit
0x00007fff37cd5000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/CoreServices
0x00007fff5fc05000 	/System/Library/PrivateFrameworks/PerformanceAnalysis.framework/Versions/A/PerformanceAnalysis
0x00007fff41475000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/OpenGL
0x00007fff35a25000 	/System/Library/Frameworks/ColorSync.framework/Versions/A/ColorSync
0x00007fff373f1000 	/System/Library/Frameworks/CoreImage.framework/Versions/A/CoreImage
0x00007fff38748000 	/System/Library/Frameworks/CoreText.framework/Versions/A/CoreText
0x00007fff39819000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/ImageIO
0x00007fff6dd45000 	/usr/lib/libc++.1.dylib
0x00007fff6ddc1000 	/usr/lib/libcompression.dylib
0x00007fff6d837000 	/usr/lib/libMobileGestalt.dylib
0x00007fff67748000 	/System/Library/PrivateFrameworks/TextureIO.framework/Versions/A/TextureIO
0x00007fff6db2e000 	/usr/lib/libate.dylib
0x00007fff5bc06000 	/System/Library/PrivateFrameworks/InternationalSupport.framework/Versions/A/InternationalSupport
0x00007fff70854000 	/usr/lib/system/libcache.dylib
0x00007fff7085a000 	/usr/lib/system/libcommonCrypto.dylib
0x00007fff70866000 	/usr/lib/system/libcompiler_rt.dylib
0x00007fff7086e000 	/usr/lib/system/libcopyfile.dylib
0x00007fff70878000 	/usr/lib/system/libcorecrypto.dylib
0x00007fff70a17000 	/usr/lib/system/libdispatch.dylib
0x00007fff70a58000 	/usr/lib/system/libdyld.dylib
0x00007fff70a8f000 	/usr/lib/system/libkeymgr.dylib
0x00007fff70a9d000 	/usr/lib/system/liblaunch.dylib
0x00007fff70a9e000 	/usr/lib/system/libmacho.dylib
0x00007fff70aa4000 	/usr/lib/system/libquarantine.dylib
0x00007fff70aa7000 	/usr/lib/system/libremovefile.dylib
0x00007fff70aa9000 	/usr/lib/system/libsystem_asl.dylib
0x00007fff70ac1000 	/usr/lib/system/libsystem_blocks.dylib
0x00007fff70ac2000 	/usr/lib/system/libsystem_c.dylib
0x00007fff70b4a000 	/usr/lib/system/libsystem_configuration.dylib
0x00007fff70b4e000 	/usr/lib/system/libsystem_coreservices.dylib
0x00007fff70b52000 	/usr/lib/system/libsystem_darwin.dylib
0x00007fff70b5b000 	/usr/lib/system/libsystem_dnssd.dylib
0x00007fff70b63000 	/usr/lib/system/libsystem_featureflags.dylib
0x00007fff70b65000 	/usr/lib/system/libsystem_info.dylib
0x00007fff70be0000 	/usr/lib/system/libsystem_m.dylib
0x00007fff70c2c000 	/usr/lib/system/libsystem_malloc.dylib
0x00007fff70c54000 	/usr/lib/system/libsystem_networkextension.dylib
0x00007fff70c62000 	/usr/lib/system/libsystem_notify.dylib
0x00007fff70c80000 	/usr/lib/system/libsystem_sandbox.dylib
0x00007fff70c85000 	/usr/lib/system/libsystem_secinit.dylib
0x00007fff70bb3000 	/usr/lib/system/libsystem_kernel.dylib
0x00007fff70c6c000 	/usr/lib/system/libsystem_platform.dylib
0x00007fff70c75000 	/usr/lib/system/libsystem_pthread.dylib
0x00007fff70c88000 	/usr/lib/system/libsystem_symptoms.dylib
0x00007fff70c90000 	/usr/lib/system/libsystem_trace.dylib
0x00007fff70ca8000 	/usr/lib/system/libunwind.dylib
0x00007fff70cae000 	/usr/lib/system/libxpc.dylib
0x00007fff6dd98000 	/usr/lib/libc++abi.dylib
0x00007fff6eb9d000 	/usr/lib/liblzma.5.dylib
0x00007fff6e818000 	/usr/lib/libfakelink.dylib
0x00007fff6dabb000 	/usr/lib/libarchive.2.dylib
0x00007fff442f9000 	/System/Library/Frameworks/SystemConfiguration.framework/Versions/A/SystemConfiguration
0x00007fff6d426000 	/usr/lib/libCRFSuite.dylib
0x00007fff35128000 	/System/Library/Frameworks/CFNetwork.framework/Versions/A/CFNetwork
0x00007fff6dd27000 	/usr/lib/libbsm.0.dylib
0x00007fff70a90000 	/usr/lib/system/libkxld.dylib
0x00007fff4a236000 	/System/Library/PrivateFrameworks/AppleFSCompression.framework/Versions/A/AppleFSCompression
0x00007fff6e0b3000 	/usr/lib/libcoretls.dylib
0x00007fff6e0ca000 	/usr/lib/libcoretls_cfhelpers.dylib
0x00007fff6f905000 	/usr/lib/libpam.2.dylib
0x00007fff6fa3a000 	/usr/lib/libsqlite3.dylib
0x00007fff6fe71000 	/usr/lib/libxar.1.dylib
0x00007fff6dd38000 	/usr/lib/libbz2.1.0.dylib
0x00007fff6e839000 	/usr/lib/libiconv.2.dylib
0x00007fff6ddae000 	/usr/lib/libcharset.1.dylib
0x00007fff6f3a3000 	/usr/lib/libnetwork.dylib
0x00007fff6f90c000 	/usr/lib/libpcap.A.dylib
0x00007fff6da70000 	/usr/lib/libapple_nghttp2.dylib
0x00007fff3808c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents
0x00007fff37d5c000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore
0x00007fff382d0000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata
0x00007fff38369000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices
0x00007fff38397000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit
0x00007fff37cd6000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE
0x00007fff38095000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices
0x00007fff3803e000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices
0x00007fff383ff000 	/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList
0x00007fff3d294000 	/System/Library/Frameworks/NetFS.framework/Versions/A/NetFS
0x00007fff5df7d000 	/System/Library/PrivateFrameworks/NetAuth.framework/Versions/A/NetAuth
0x00007fff6a906000 	/System/Library/PrivateFrameworks/login.framework/Versions/A/Frameworks/loginsupport.framework/Versions/A/loginsupport
0x00007fff67213000 	/System/Library/PrivateFrameworks/TCC.framework/Versions/A/TCC
0x00007fff4ee67000 	/System/Library/PrivateFrameworks/CoreNLP.framework/Versions/A/CoreNLP
0x00007fff5d47a000 	/System/Library/PrivateFrameworks/MetadataUtilities.framework/Versions/A/MetadataUtilities
0x00007fff6ec75000 	/usr/lib/libmecabra.dylib
0x00007fff6ebcd000 	/usr/lib/libmecab.dylib
0x00007fff6e829000 	/usr/lib/libgermantok.dylib
0x00007fff6da57000 	/usr/lib/libThaiTokenizer.dylib
0x00007fff6d45d000 	/usr/lib/libChineseTokenizer.dylib
0x00007fff324f4000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vImage.framework/Versions/A/vImage
0x00007fff339c2000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/vecLib
0x00007fff33843000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvMisc.dylib
0x00007fff336b0000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libvDSP.dylib
0x00007fff32d90000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBLAS.dylib
0x00007fff33268000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLAPACK.dylib
0x00007fff33610000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libLinearAlgebra.dylib
0x00007fff3369d000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparseBLAS.dylib
0x00007fff33626000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libQuadrature.dylib
0x00007fff32f26000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libBNNS.dylib
0x00007fff3362c000 	/System/Library/Frameworks/Accelerate.framework/Versions/A/Frameworks/vecLib.framework/Versions/A/libSparse.dylib
0x00007fff5bfee000 	/System/Library/PrivateFrameworks/LanguageModeling.framework/Versions/A/LanguageModeling
0x00007fff4e817000 	/System/Library/PrivateFrameworks/CoreEmoji.framework/Versions/A/CoreEmoji
0x00007fff5c10c000 	/System/Library/PrivateFrameworks/LinguisticData.framework/Versions/A/LinguisticData
0x00007fff5c0bd000 	/System/Library/PrivateFrameworks/Lexicon.framework/Versions/A/Lexicon
0x00007fff6ddaf000 	/usr/lib/libcmph.dylib
0x00007fff3fe83000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/Frameworks/CFOpenDirectory.framework/Versions/A/CFOpenDirectory
0x00007fff3fea0000 	/System/Library/Frameworks/OpenDirectory.framework/Versions/A/OpenDirectory
0x00007fff482e3000 	/System/Library/PrivateFrameworks/APFS.framework/Versions/A/APFS
0x00007fff43592000 	/System/Library/Frameworks/SecurityFoundation.framework/Versions/A/SecurityFoundation
0x00007fff6fe6d000 	/usr/lib/libutil.dylib
0x00007fff4fd4c000 	/System/Library/PrivateFrameworks/CoreServicesStore.framework/Versions/A/CoreServicesStore
0x00007fff43649000 	/System/Library/Frameworks/ServiceManagement.framework/Versions/A/ServiceManagement
0x00007fff4ba06000 	/System/Library/PrivateFrameworks/BackgroundTaskManagement.framework/Versions/A/BackgroundTaskManagement
0x00007fff6ff6a000 	/usr/lib/libxslt.1.dylib
0x00007fff4a8a9000 	/System/Library/PrivateFrameworks/AppleSystemInfo.framework/Versions/A/AppleSystemInfo
0x00007fff39a37000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJPEG.dylib
0x00007fff39cf7000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libTIFF.dylib
0x00007fff39cda000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libPng.dylib
0x00007fff39979000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libGIF.dylib
0x00007fff3997d000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libJP2.dylib
0x00007fff39cf5000 	/System/Library/Frameworks/ImageIO.framework/Versions/A/Resources/libRadiance.dylib
0x00007fff6e7f2000 	/usr/lib/libexpat.1.dylib
0x00007fff4a391000 	/System/Library/PrivateFrameworks/AppleJPEG.framework/Versions/A/AppleJPEG
0x00007fff53062000 	/System/Library/PrivateFrameworks/FontServices.framework/libFontParser.dylib
0x00007fff69908000 	/System/Library/PrivateFrameworks/WatchdogClient.framework/Versions/A/WatchdogClient
0x00007fff5b649000 	/System/Library/PrivateFrameworks/IOAccelerator.framework/Versions/A/IOAccelerator
0x00007fff3c204000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Versions/A/MetalPerformanceShaders
0x00007fff57ddf000 	/System/Library/PrivateFrameworks/GPUWrangler.framework/Versions/A/GPUWrangler
0x00007fff5b65e000 	/System/Library/PrivateFrameworks/IOPresentment.framework/Versions/A/IOPresentment
0x00007fff508cc000 	/System/Library/PrivateFrameworks/DSExternalDisplay.framework/Versions/A/DSExternalDisplay
0x00007fff40814000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreFSCache.dylib
0x00007fff3bf50000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSCore.framework/Versions/A/MPSCore
0x00007fff3bf8e000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSImage.framework/Versions/A/MPSImage
0x00007fff3c055000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSNeuralNetwork.framework/Versions/A/MPSNeuralNetwork
0x00007fff3c019000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSMatrix.framework/Versions/A/MPSMatrix
0x00007fff3c1b4000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSRayIntersector.framework/Versions/A/MPSRayIntersector
0x00007fff3c03f000 	/System/Library/Frameworks/MetalPerformanceShaders.framework/Frameworks/MPSNDArray.framework/Versions/A/MPSNDArray
0x00007fff5d4c7000 	/System/Library/PrivateFrameworks/MetalTools.framework/Versions/A/MetalTools
0x00007fff494bd000 	/System/Library/PrivateFrameworks/AggregateDictionary.framework/Versions/A/AggregateDictionary
0x00007fff4e25f000 	/System/Library/PrivateFrameworks/CoreAnalytics.framework/Versions/A/CoreAnalytics
0x00007fff4a7c8000 	/System/Library/PrivateFrameworks/AppleSauce.framework/Versions/A/AppleSauce
0x00007fff6d719000 	/usr/lib/libIOReport.dylib
0x00007fff38900000 	/System/Library/Frameworks/CoreVideo.framework/Versions/A/CoreVideo
0x00007fff59258000 	/System/Library/PrivateFrameworks/GraphVisualizer.framework/Versions/A/GraphVisualizer
0x00007fff525a8000 	/System/Library/PrivateFrameworks/FaceCore.framework/Versions/A/FaceCore
0x00007fff3fe2a000 	/System/Library/Frameworks/OpenCL.framework/Versions/A/OpenCL
0x00007fff6d53b000 	/usr/lib/libFosl_dynamic.dylib
0x00007fff5e99d000 	/System/Library/PrivateFrameworks/OTSVG.framework/Versions/A/OTSVG
0x00007fff34a81000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/Resources/libFontRegistry.dylib
0x00007fff53233000 	/System/Library/PrivateFrameworks/FontServices.framework/libhvf.dylib
0x00007fff4081f000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGFXShared.dylib
0x00007fff409fd000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib
0x00007fff40828000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib
0x00007fff40833000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLImage.dylib
0x00007fff40811000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCVMSPluginSupport.dylib
0x00007fff4081a000 	/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libCoreVMClient.dylib
0x00007fff6f244000 	/usr/lib/libncurses.5.4.dylib
0x00007fff3497d000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS
0x00007fff34b4a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy
0x00007fff34be8000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices
0x00007fff34c40000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/LangAnalysis.framework/Versions/A/LangAnalysis
0x00007fff34c4f000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore
0x00007fff34c95000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD
0x00007fff34ca0000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis
0x00007fff34b1a000 	/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI
0x00007fff6e688000 	/usr/lib/libcups.2.dylib
0x00007fff3b29f000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Kerberos
0x00007fff393dc000 	/System/Library/Frameworks/GSS.framework/Versions/A/GSS
0x00007fff6f9c6000 	/usr/lib/libresolv.9.dylib
0x00007fff59405000 	/System/Library/PrivateFrameworks/Heimdal.framework/Versions/A/Heimdal
0x00007fff3b2b2000 	/System/Library/Frameworks/Kerberos.framework/Versions/A/Libraries/libHeimdalProxy.dylib
0x00007fff6e82f000 	/usr/lib/libheimdal-asn1.dylib
0x00007fff4d6f7000 	/System/Library/PrivateFrameworks/CommonAuth.framework/Versions/A/CommonAuth
0x00007fff4a946000 	/System/Library/PrivateFrameworks/AssertionServices.framework/Versions/A/AssertionServices
0x00007fff4b4e9000 	/System/Library/PrivateFrameworks/AudioToolboxCore.framework/Versions/A/AudioToolboxCore
0x00007fff6a5b6000 	/System/Library/PrivateFrameworks/caulk.framework/Versions/A/caulk
0x00007fff4bab2000 	/System/Library/PrivateFrameworks/BaseBoard.framework/Versions/A/BaseBoard
0x00007fff62c00000 	/System/Library/PrivateFrameworks/RunningBoardServices.framework/Versions/A/RunningBoardServices
0x00007fff5fc11000 	/System/Library/PrivateFrameworks/PersistentConnection.framework/Versions/A/PersistentConnection
0x00007fff625f9000 	/System/Library/PrivateFrameworks/ProtocolBuffer.framework/Versions/A/ProtocolBuffer
0x00007fff4d71b000 	/System/Library/PrivateFrameworks/CommonUtilities.framework/Versions/A/CommonUtilities
0x00007fff4bc40000 	/System/Library/PrivateFrameworks/Bom.framework/Versions/A/Bom
0x00007fff6d3ec000 	/usr/lib/libAudioToolboxUtility.dylib
0x00007fff4ba10000 	/System/Library/PrivateFrameworks/Backup.framework/Versions/A/Backup
0x00007fff507ed000 	/System/Library/PrivateFrameworks/CrashReporterSupport.framework/Versions/A/CrashReporterSupport
0x00007fff64692000 	/System/Library/PrivateFrameworks/Sharing.framework/Versions/A/Sharing
0x00007fff49f54000 	/System/Library/PrivateFrameworks/Apple80211.framework/Versions/A/Apple80211
0x00007fff4b72d000 	/System/Library/PrivateFrameworks/AuthKit.framework/Versions/A/AuthKit
0x00007fff504fa000 	/System/Library/PrivateFrameworks/CoreUtils.framework/Versions/A/CoreUtils
0x00007fff38945000 	/System/Library/Frameworks/CoreWLAN.framework/Versions/A/CoreWLAN
0x00007fff39569000 	/System/Library/Frameworks/IOBluetooth.framework/Versions/A/IOBluetooth
0x00007fff5d7cc000 	/System/Library/PrivateFrameworks/MobileKeyBag.framework/Versions/A/MobileKeyBag
0x00007fff4f2ff000 	/System/Library/PrivateFrameworks/CorePhoneNumbers.framework/Versions/A/CorePhoneNumbers
0x00007fff4a344000 	/System/Library/PrivateFrameworks/AppleIDAuthSupport.framework/Versions/A/AppleIDAuthSupport
0x00007fff3d2a1000 	/System/Library/Frameworks/Network.framework/Versions/A/Network
0x00007fff5be9a000 	/System/Library/PrivateFrameworks/KeychainCircle.framework/Versions/A/KeychainCircle
0x00007fff36362000 	/System/Library/Frameworks/CoreBluetooth.framework/Versions/A/CoreBluetooth
0x00007fff66711000 	/System/Library/PrivateFrameworks/SpeechRecognitionCore.framework/Versions/A/SpeechRecognitionCore
0x000000010f800000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/server/libjvm.dylib
0x00007fff6fd40000 	/usr/lib/libstdc++.6.dylib
0x000000010e67a000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libverify.dylib
0x000000010e688000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libjava.dylib
0x000000010e6bf000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libjdwp.dylib
0x000000010e70a000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libnpt.dylib
0x000000010e70e000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libinstrument.dylib
0x000000010e77a000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libzip.dylib
0x000000010e7a2000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libdt_socket.dylib
0x0000000115b76000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libnet.dylib
0x000000011638d000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libmanagement.dylib
0x00000001169a0000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libnio.dylib
0x000000012bf5b000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libawt.dylib
0x000000012c004000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libmlib_image.dylib
0x00007fff3b289000 	/System/Library/Frameworks/JavaVM.framework/Versions/A/Frameworks/JavaNativeFoundation.framework/Versions/A/JavaNativeFoundation
0x00007fff3b292000 	/System/Library/Frameworks/JavaVM.framework/Versions/A/Frameworks/JavaRuntimeSupport.framework/Versions/A/JavaRuntimeSupport
0x00007fff3b29a000 	/System/Library/Frameworks/JavaVM.framework/Versions/A/JavaVM
0x00007fff5bcb1000 	/System/Library/PrivateFrameworks/JavaLaunching.framework/Versions/A/JavaLaunching
0x00007fff35533000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Carbon
0x00007fff5029f000 	/System/Library/PrivateFrameworks/CoreSymbolication.framework/Versions/A/CoreSymbolication
0x00007fff35534000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/CommonPanels.framework/Versions/A/CommonPanels
0x00007fff3582d000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Help.framework/Versions/A/Help
0x00007fff35831000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/ImageCapture.framework/Versions/A/ImageCapture
0x00007fff35838000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/OpenScripting.framework/Versions/A/OpenScripting
0x00007fff35837000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Ink.framework/Versions/A/Ink
0x00007fff35873000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/Print.framework/Versions/A/Print
0x00007fff35874000 	/System/Library/Frameworks/Carbon.framework/Versions/A/Frameworks/SecurityHI.framework/Versions/A/SecurityHI
0x00007fff50a21000 	/System/Library/PrivateFrameworks/DebugSymbols.framework/Versions/A/DebugSymbols
0x000000012c0d0000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libawt_lwawt.dylib
0x000000012c18b000 	/Library/Java/JavaVirtualMachines/jdk1.8.0_211.jdk/Contents/Home/jre/lib/libosxapp.dylib
0x00007fff38e45000 	/System/Library/Frameworks/ExceptionHandling.framework/Versions/A/ExceptionHandling

VM Arguments:
jvm_args: -Dendpoints.shutdown.enabled=true -Denv=development -Dfull.stacktrace=false -Dgrails.env=development -Dgrails.full.stacktrace=false -Dinfo.app.grailsVersion=3.2.9 -Dinfo.app.name=wonderslate329 -Dinfo.app.version=0.1 -Djdk.reflect.allowGetCallerClass=true -Drun.active=true -Dspring.output.ansi.enabled=always -Dspringloaded=inclusions=grails.plugins..*;synchronize=true;allowSplitPackages=true;cacheDir=/Users/<USER>/Documents/technical/wonderslate/build/springloaded -Dverbose=false -XX:+TieredCompilation -XX:TieredStopAtLevel=1 -XX:CICompilerCount=3 -agentlib:jdwp=transport=dt_socket,address=49623,suspend=n,server=y -javaagent:/Users/<USER>/Library/Caches/IntelliJIdea2019.2/captureAgent/debugger-agent.jar -javaagent:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework/springloaded/1.2.7.RELEASE/36be57f1a306cec1185b0276960fbe95dee08bf/springloaded-1.2.7.RELEASE.jar -Xverify:none -Xms768m -Xmx768m -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant 
java_command: wonderslate329.Application
java_class_path (initial): /Users/<USER>/Documents/technical/wonderslate/src/main/resources:/Users/<USER>/Documents/technical/wonderslate/grails-app/views:/Users/<USER>/Documents/technical/wonderslate/grails-app/i18n:/Users/<USER>/Documents/technical/wonderslate/grails-app/conf:/Users/<USER>/Documents/technical/wonderslate/build/classes/main:/Users/<USER>/Documents/technical/wonderslate/build/resources/main:/Users/<USER>/Documents/technical/wonderslate/gsp-classes:/Users/<USER>/Documents/technical/wonderslate/libs/ccavutil.jar:/Users/<USER>/Documents/technical/wonderslate/libs/opennlp-tools-1.9.2.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/medialive/2.11.12/medialive-2.11.12.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/mediapackage/2.11.12/mediapackage-2.11.12.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-translate/1.78.0/google-cloud-translate-1.78.0.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.grails.plugins/mongodb/6.0.10/b5bef18262dbaf5702008fc127df67e6ab4e4f6c/mongodb-6.0.10.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-logging/1.4.6.RELEASE/639c8849482bb82fb13be276338deee10115c80/spring-boot-starter-logging-1.4.6.RELEASE.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-autoconfigure/1.4.6.RELEASE/e99ac84ecc30710ac838ad02894100b0020127a4/spring-boot-autoconfigure-1.4.6.RELEASE.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.grails/grails-core/3.2.9/6dadaddfa4e6dfb7e135f9116aabd0f3e2a08285/grails-core-3.2.9.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.springframework.boot/spring-boot-starter-actuator/1.4.6.RELEASE/a3c60642beb74380a118cbf99d43aee30b09a806/spring-boot-starter-actuator-1.4.6.RELEASE.jar:/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.spr
Launcher Type: SUN_STANDARD

Environment Variables:
PATH=/opt/local/bin:/opt/local/sbin:/Users/<USER>/opt/anaconda3/bin:/Users/<USER>/opt/anaconda3/condabin:/Users/<USER>/.sdkman/candidates/grails/current/bin:/Library/Java/JavaVirtualMachines/openjdk-13.0.1.jdk/Contents/Home/bin:/Users/<USER>/technical/apache-maven-3.6.3/bin:/Users/<USER>/Documents/technical/mongodb/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin:/Library/Apple/usr/bin
SHELL=/bin/bash

Signal Handlers:
SIGSEGV: [libjvm.dylib+0x5b71a7], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_ONSTACK|SA_RESTART|SA_SIGINFO
SIGBUS: [libjvm.dylib+0x5b71a7], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGFPE: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGPIPE: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGXFSZ: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGILL: [libjvm.dylib+0x48c8fc], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGUSR1: SIG_DFL, sa_mask[0]=00000000000000000000000000000000, sa_flags=none
SIGUSR2: [libjvm.dylib+0x48c41a], sa_mask[0]=00100000000000000000000000000000, sa_flags=SA_RESTART|SA_SIGINFO
SIGHUP: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGINT: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGTERM: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO
SIGQUIT: [libjvm.dylib+0x48a9a5], sa_mask[0]=11111111011111110111111111111111, sa_flags=SA_RESTART|SA_SIGINFO


---------------  S Y S T E M  ---------------

OS:Bsduname:Darwin 19.6.0 Darwin Kernel Version 19.6.0: Mon Aug 31 22:12:52 PDT 2020; root:xnu-6153.141.2~1/RELEASE_X86_64 x86_64
rlimit: STACK 8192k, CORE 0k, NPROC 2784, NOFILE 10240, AS infinity
load average:4.64 3.00 3.04

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 58 stepping 9, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, aes, clmul, erms, ht, tsc, tscinvbit, tscinv

Memory: 4k page, physical 16777216k(2108216k free)

/proc/meminfo:


vm_info: Java HotSpot(TM) 64-Bit Server VM (25.211-b12) for bsd-amd64 JRE (1.8.0_211-b12), built on Apr  1 2019 20:53:18 by "java_re" with gcc 4.2.1 (Based on Apple Inc. build 5658) (LLVM build 2336.11.00)

time: Thu Jan  7 12:29:12 2021
timezone: IST
elapsed time: 53222 seconds (0d 14h 47m 2s)

